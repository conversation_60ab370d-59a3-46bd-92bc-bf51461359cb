<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_notification_settings', function (Blueprint $table) {
            $table->boolean('email_enabled')->default(true)->comment('Enable/disable email notifications for this type')->after('is_enabled');
            $table->boolean('sms_enabled')->default(true)->comment('Enable/disable SMS notifications for this type')->after('email_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_notification_settings', function (Blueprint $table) {
            $table->dropColumn(['email_enabled', 'sms_enabled']);
        });
    }
};
