<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ticket_category_replies', function (Blueprint $table) {
            $table->id('ticket_category_reply_id');
            $table->foreignId('ticket_category_id')->constrained('ticket_categories', 'ticket_category_id')->cascadeOnDelete();
            $table->text('content');
            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ticket_category_replies');
    }
};
