<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id('ticket_id');

            $table->foreignId('user_id')->comment('The customer who owns the ticket')->constrained('users', 'user_id')->cascadeOnDelete();

            $table->foreignId('ticket_category_id')->constrained('ticket_categories', 'ticket_category_id')->cascadeOnDelete();
            $table->string('title');
            $table->text('content');
            $table->string('status')->default('open');
            $table->string('priority')->default('medium');
            $table->timestamp('resolved_at')->nullable();

            $table->foreignId('preferred_agent_id')->nullable()->comment('User\'s preferred agent')->constrained('users', 'user_id')->nullOnDelete();
            $table->foreignId('assigned_to')->nullable()->comment('Agent currently assigned')->constrained('users', 'user_id')->nullOnDelete();

            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
