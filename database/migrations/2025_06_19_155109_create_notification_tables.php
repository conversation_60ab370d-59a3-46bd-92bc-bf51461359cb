<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_types', function (Blueprint $table) {
            $table->id('notification_type_id');
            $table->string('name', 100)->unique()->comment('Machine-readable name, e.g., new-comment');
            $table->json('default_channels')->nullable()->comment('Default delivery channels like ["mail", "database"]');

            $table->enum('event', ['account_created', 'account_locked', 'account_reactivated', 'account_role_changed', 'account_password_reset', 'account_invitation_sent', 'account_otp_sent', 'assigned_license', 'provided_license', 'receipt_approved', 'receipt_rejected', 'subscription_activated']);
            $table->json('placeholders', 100)->nullable()->comment('List of placeholders used in templates, e.g., ["{user_name}", "{post_title}"]');

            $table->withAudits();
        });

        Schema::create('notification_type_translations', function (Blueprint $table) {
            $table->id('notification_type_translation_id');
            $table->foreignId('type_id')->constrained('notification_types', 'notification_type_id')->onDelete('cascade');
            $table->string('language_code', 10);
            $table->string('description');
            $table->string('template_location')->nullable()->comment('Path or identifier for the template file');
            $table->text('template_string')->nullable()->comment('The content with placeholders like {variable}');

            $table->withAudits();

            $table->unique(['type_id', 'language_code'], 'notification_type_unique_translation');
        });

        Schema::create('notifications', function (Blueprint $table) {
            $table->id('notification_id');

            $table->foreignId('user_id')->constrained('users', 'user_id')->onDelete('cascade');
            $table->foreignId('notification_type_id')->constrained('notification_types', 'notification_type_id')->onDelete('cascade');
            $table->json('delivery_channels')->nullable()->comment('Created notifications count will be based on notification type channels, e.g., ["mail", "database"]');

            $table->jsonb('template_params')->nullable()->comment('Dynamic data for the template');
            $table->string('link_url', 2048)->nullable();

            $table->boolean('is_sent')->default(false);
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->boolean('is_archived')->default(false);

            $table->timestamp('expires_at')->nullable();

            $table->withAudits();

            $table->index(['user_id', 'is_read', 'created_at']);
            $table->index(['user_id', 'is_archived', 'created_at']);
        });

        Schema::create('user_notification_settings', function (Blueprint $table) {
            $table->id('user_notification_setting_id');

            $table->foreignId('user_id')->constrained('users', 'user_id')->onDelete('cascade');
            $table->foreignId('notification_type_id')->constrained('notification_types', 'notification_type_id')->onDelete('cascade');

            $table->boolean('is_enabled')->default(true);
            $table->json('delivery_channels')->nullable()->comment('User overrides for default channels');

            $table->unique(['user_id', 'notification_type_id'], 'user_notification_unique_setting');

            $table->withAudits();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_notification_settings');
        Schema::dropIfExists('notifications');
        Schema::dropIfExists('notification_type_translations');
        Schema::dropIfExists('notification_types');
        Schema::dropIfExists('notification_template_translations');
        Schema::dropIfExists('notification_templates');
    }
};
