INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(12670, 'Juarina', 2020, 'TO', 31, 'BR', -8.10180000, -49.07995000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q675677'),
(12671, 'Juatuba', 1998, 'MG', 31, 'BR', -19.95194000, -44.34278000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790670'),
(12672, 'Ju<PERSON><PERSON><PERSON><PERSON>', 2005, 'PB', 31, 'BR', -6.99463000, -36.60213000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790670'),
(12673, '<PERSON><PERSON><PERSON>', 2002, 'BA', 31, 'BR', -9.39679000, -40.23381000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790670'),
(12674, '<PERSON><PERSON><PERSON> do Norte', 2016, 'CE', 31, 'BR', -7.21306000, -39.31528000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q817227'),
(12675, '<PERSON><PERSON><PERSON> do Piauí', 2008, 'PI', 31, 'BR', -5.02486000, -41.52841000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q817227'),
(12676, 'Jucati', 2006, 'PE', 31, 'BR', -8.75067000, -36.47750000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q817227'),
(12677, 'Jucurutu', 2019, 'RN', 31, 'BR', -6.03389000, -37.02028000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1787030'),
(12678, 'Jucuruçu', 2002, 'BA', 31, 'BR', -16.84832000, -40.08414000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1787030'),
(12679, 'Jucás', 2016, 'CE', 31, 'BR', -6.44677000, -39.62005000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q2027711'),
(12680, 'Juiz de Fora', 1998, 'MG', 31, 'BR', -21.76417000, -43.35028000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q193019'),
(12681, 'Jumirim', 2021, 'SP', 31, 'BR', -23.10825000, -47.79820000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q193019'),
(12682, 'Junco do Maranhão', 2015, 'MA', 31, 'BR', -1.94176000, -46.11711000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q193019'),
(12683, 'Junco do Seridó', 2005, 'PB', 31, 'BR', -6.97843000, -36.72769000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q193019'),
(12684, 'Jundiaí', 2021, 'SP', 31, 'BR', -23.18639000, -46.88417000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q275109'),
(12685, 'Jundiaí do Sul', 2022, 'PR', 31, 'BR', -23.47919000, -50.18065000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q275109'),
(12686, 'Jundiá', 2007, 'AL', 31, 'BR', -8.93472000, -35.57361000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q2020978'),
(12687, 'Jundiá', 2019, 'RN', 31, 'BR', -6.25532000, -35.34358000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q2355804'),
(12688, 'Junqueiro', 2007, 'AL', 31, 'BR', -9.88524000, -36.45640000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1808328'),
(12689, 'Junqueirópolis', 2021, 'SP', 31, 'BR', -21.51472000, -51.43361000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1754616'),
(12690, 'Jupi', 2006, 'PE', 31, 'BR', -8.71547000, -36.39397000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1754616'),
(12691, 'Jupiá', 2014, 'SC', 31, 'BR', -26.41103000, -52.73097000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1754616'),
(12692, 'Juquitiba', 2021, 'SP', 31, 'BR', -23.95860000, -47.01366000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1760410'),
(12693, 'Juquiá', 2021, 'SP', 31, 'BR', -24.32083000, -47.63472000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1760417'),
(12694, 'Juramento', 1998, 'MG', 31, 'BR', -16.83343000, -43.58833000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790506'),
(12695, 'Juranda', 2022, 'PR', 31, 'BR', -24.41323000, -52.82730000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790506'),
(12696, 'Jurema', 2008, 'PI', 31, 'BR', -9.29862000, -43.15301000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1941164'),
(12697, 'Jurema', 2006, 'PE', 31, 'BR', -8.78579000, -36.13364000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2104639'),
(12698, 'Juripiranga', 2005, 'PB', 31, 'BR', -7.35000000, -35.22087000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2008351'),
(12699, 'Juru', 2005, 'PB', 31, 'BR', -7.49131000, -37.78854000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1808550'),
(12700, 'Juruaia', 1998, 'MG', 31, 'BR', -21.22649000, -46.51449000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790502'),
(12701, 'Juruena', 2011, 'MT', 31, 'BR', -10.28992000, -58.65303000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790502'),
(12702, 'Juruti', 2009, 'PA', 31, 'BR', -2.33065000, -56.02742000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1008242'),
(12703, 'Juruá', 2004, 'AM', 31, 'BR', -3.27526000, -66.24211000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1008242'),
(12704, 'Juscimeira', 2011, 'MT', 31, 'BR', -16.20525000, -54.88029000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1008242'),
(12705, 'Jussara', 2002, 'BA', 31, 'BR', -10.96408000, -41.85572000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1008242'),
(12706, 'Jussara', 2022, 'PR', 31, 'BR', -23.62121000, -52.47084000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1025757'),
(12707, 'Jussara', 2000, 'GO', 31, 'BR', -15.58295000, -51.32970000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1008360'),
(12708, 'Jussari', 2002, 'BA', 31, 'BR', -15.15451000, -39.51559000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q338795'),
(12709, 'Jussiape', 2002, 'BA', 31, 'BR', -13.48810000, -41.62335000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q935612'),
(12710, 'Jutaí', 2004, 'AM', 31, 'BR', -5.18333000, -68.90000000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q22054462'),
(12711, 'Juti', 2010, 'MS', 31, 'BR', -22.85356000, -54.52155000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q991137'),
(12712, 'Juvenília', 1998, 'MG', 31, 'BR', -14.39930000, -43.95055000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q991137'),
(12714, 'Juá dos Vieiras', 2016, 'CE', 31, 'BR', -3.53333000, -41.30000000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q22021479'),
(12715, 'Juína', 2011, 'MT', 31, 'BR', -11.40329000, -59.50810000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q22021479'),
(12716, 'Jóia', 2001, 'RS', 31, 'BR', -28.72932000, -54.12253000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q22021479'),
(12717, 'Júlio Borges', 2008, 'PI', 31, 'BR', -10.51580000, -44.19178000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q22021479'),
(12718, 'Júlio Mesquita', 2021, 'SP', 31, 'BR', -21.97689000, -49.78623000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q22021479'),
(12719, 'Júlio de Castilhos', 2001, 'RS', 31, 'BR', -29.26449000, -53.62459000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1754330'),
(12720, 'Kaloré', 2022, 'PR', 31, 'BR', -23.86713000, -51.68029000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1754330'),
(12721, 'Lacerdópolis', 2014, 'SC', 31, 'BR', -27.25354000, -51.58504000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1754330'),
(12722, 'Ladainha', 1998, 'MG', 31, 'BR', -17.64365000, -41.83262000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1754330'),
(12723, 'Ladário', 2010, 'MS', 31, 'BR', -19.10505000, -57.58416000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1798712'),
(12724, 'Lafaiete Coutinho', 2002, 'BA', 31, 'BR', -13.62029000, -40.20444000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1798712'),
(12725, 'Lagamar', 1998, 'MG', 31, 'BR', -17.97706000, -46.70095000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1790657'),
(12726, 'Lagarto', 2003, 'SE', 31, 'BR', -10.89844000, -37.67993000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2288052'),
(12727, 'Lages', 2014, 'SC', 31, 'BR', -27.81611000, -50.32611000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q762009'),
(12728, 'Lago Verde', 2015, 'MA', 31, 'BR', -3.99073000, -44.88312000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2105837'),
(12729, 'Lago da Pedra', 2015, 'MA', 31, 'BR', -4.28674000, -45.23824000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q22021608'),
(12730, 'Lago do Junco', 2015, 'MA', 31, 'BR', -4.47033000, -44.91005000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q927111'),
(12731, 'Lago dos Rodrigues', 2015, 'MA', 31, 'BR', -4.61099000, -44.97837000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q305180'),
(12732, 'Lagoa', 2014, 'SC', 31, 'BR', -27.60491000, -48.46713000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q305180'),
(12733, 'Lagoa', 2005, 'PB', 31, 'BR', -6.59521000, -37.84991000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q305180'),
(12734, 'Lagoa Alegre', 2008, 'PI', 31, 'BR', -4.49510000, -42.56555000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q305180'),
(12735, 'Lagoa Bonita do Sul', 2001, 'RS', 31, 'BR', -29.49396000, -53.03470000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1785881'),
(12736, 'Lagoa Dourada', 1998, 'MG', 31, 'BR', -20.91034000, -44.06512000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1790717'),
(12737, 'Lagoa Formosa', 1998, 'MG', 31, 'BR', -18.76494000, -46.29006000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1791516'),
(12738, 'Lagoa Grande', 2006, 'PE', 31, 'BR', -8.78698000, -40.25356000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1791516'),
(12739, 'Lagoa Grande', 1998, 'MG', 31, 'BR', -17.74523000, -46.53558000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q22062425'),
(12740, 'Lagoa Grande do Maranhão', 2015, 'MA', 31, 'BR', -4.93168000, -45.34950000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q22062425'),
(12741, 'Lagoa Nova', 2019, 'RN', 31, 'BR', -6.11020000, -36.54607000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q526249'),
(12742, 'Lagoa Real', 2002, 'BA', 31, 'BR', -14.10985000, -42.21697000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q526249'),
(12743, 'Lagoa Salgada', 2019, 'RN', 31, 'BR', -6.12321000, -35.49513000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q526249'),
(12744, 'Lagoa Santa', 1998, 'MG', 31, 'BR', -19.62683000, -43.87993000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q178667'),
(12745, 'Lagoa Santa', 2000, 'GO', 31, 'BR', -19.21207000, -51.25891000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2099571'),
(12746, 'Lagoa Seca', 2005, 'PB', 31, 'BR', -7.17083000, -35.85361000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2010981'),
(12747, 'Lagoa Vermelha', 2001, 'RS', 31, 'BR', -28.21995000, -51.47225000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q22040124'),
(12748, 'Lagoa d\'Anta', 2019, 'RN', 31, 'BR', -6.37037000, -35.63637000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1802281'),
(12749, 'Lagoa da Canoa', 2007, 'AL', 31, 'BR', -9.82972000, -36.73778000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1795976'),
(12750, 'Lagoa da Confusão', 2020, 'TO', 31, 'BR', -10.99307000, -49.93702000, '2019-10-05 22:35:11', '2020-05-01 17:22:38', 1, 'Q1801577'),
(12751, 'Lagoa da Prata', 1998, 'MG', 31, 'BR', -19.99802000, -45.49952000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1749899'),
(12752, 'Lagoa de Dentro', 2005, 'PB', 31, 'BR', -6.68308000, -35.35907000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2011329'),
(12753, 'Lagoa de Itaenga', 2006, 'PE', 31, 'BR', -7.90293000, -35.29323000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2011329'),
(12754, 'Lagoa de Pedras', 2019, 'RN', 31, 'BR', -6.16608000, -35.45319000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2011329'),
(12755, 'Lagoa de São Francisco', 2008, 'PI', 31, 'BR', -4.35468000, -41.59168000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q1114540'),
(12756, 'Lagoa de Velhos', 2019, 'RN', 31, 'BR', -6.00816000, -35.82312000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1114540'),
(12757, 'Lagoa do Barro do Piauí', 2008, 'PI', 31, 'BR', -8.73535000, -41.54813000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q1114540'),
(12758, 'Lagoa do Carro', 2006, 'PE', 31, 'BR', -7.85419000, -35.33637000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1114540'),
(12759, 'Lagoa do Itaenga', 2006, 'PE', 31, 'BR', -7.93611000, -35.29028000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1887813'),
(12760, 'Lagoa do Mato', 2015, 'MA', 31, 'BR', -5.95535000, -43.63105000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1887813'),
(12761, 'Lagoa do Ouro', 2006, 'PE', 31, 'BR', -9.16636000, -36.48221000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1887813'),
(12762, 'Lagoa do Piauí', 2008, 'PI', 31, 'BR', -5.47513000, -42.54814000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q1887813'),
(12763, 'Lagoa do Sítio', 2008, 'PI', 31, 'BR', -6.51918000, -41.42058000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q1887813'),
(12764, 'Lagoa do Tocantins', 2020, 'TO', 31, 'BR', -10.33721000, -47.48133000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1887813'),
(12765, 'Lagoa dos Gatos', 2006, 'PE', 31, 'BR', -8.67965000, -35.88979000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1887813'),
(12766, 'Lagoa dos Patos', 1998, 'MG', 31, 'BR', -17.01433000, -44.66633000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q22062428'),
(12767, 'Lagoa dos Três Cantos', 2001, 'RS', 31, 'BR', -28.56607000, -52.84650000, '2019-10-05 22:35:11', '2020-05-01 17:22:38', 1, 'Q22062428'),
(12768, 'Lagoinha', 2021, 'SP', 31, 'BR', -23.08443000, -45.19973000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1759587'),
(12769, 'Lagoinha do Piauí', 2008, 'PI', 31, 'BR', -5.80702000, -42.62796000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q2064145'),
(12770, 'Lagoão', 2001, 'RS', 31, 'BR', -29.25902000, -52.76687000, '2019-10-05 22:35:11', '2020-05-01 17:22:38', 1, 'Q1774445'),
(12771, 'Laguna', 2014, 'SC', 31, 'BR', -28.48250000, -48.78083000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1615291'),
(12772, 'Laguna Carapã', 2010, 'MS', 31, 'BR', -22.71043000, -55.08248000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q1615291'),
(12773, 'Laje', 2002, 'BA', 31, 'BR', -10.18708000, -40.97076000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q22040135'),
(12774, 'Laje do Muriaé', 1997, 'RJ', 31, 'BR', -21.23762000, -42.13269000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q519652'),
(12775, 'Lajeado', 2001, 'RS', 31, 'BR', -29.46694000, -51.96139000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1012550'),
(12776, 'Lajeado', 2020, 'TO', 31, 'BR', -9.86927000, -48.28058000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1012550'),
(12777, 'Lajeado Grande', 2014, 'SC', 31, 'BR', -26.85054000, -52.54065000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1012550'),
(12778, 'Lajeado Novo', 2015, 'MA', 31, 'BR', -6.10634000, -46.90224000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1012550'),
(12779, 'Lajeado do Bugre', 2001, 'RS', 31, 'BR', -27.70327000, -53.20742000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1012550'),
(12780, 'Lajedinho', 2002, 'BA', 31, 'BR', -12.42698000, -41.15097000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1012550'),
(12781, 'Lajedo', 2006, 'PE', 31, 'BR', -8.66361000, -36.32000000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2078213'),
(12782, 'Lajedo do Tabocal', 2002, 'BA', 31, 'BR', -13.37125000, -40.23004000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2078213'),
(12783, 'Lajedão', 2002, 'BA', 31, 'BR', -17.57112000, -40.31123000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q2078213'),
(12784, 'Lajes', 2019, 'RN', 31, 'BR', -5.78338000, -36.17286000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2078213'),
(12785, 'Lajes Pintadas', 2019, 'RN', 31, 'BR', -6.12734000, -36.12516000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2078213'),
(12786, 'Lajinha', 1998, 'MG', 31, 'BR', -20.12411000, -41.61531000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q641281'),
(12787, 'Lamarão', 2002, 'BA', 31, 'BR', -11.77625000, -38.90341000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q641281'),
(12788, 'Lambari', 1998, 'MG', 31, 'BR', -21.98590000, -45.35167000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1780214'),
(12789, 'Lambari d\'Oeste', 2011, 'MT', 31, 'BR', -15.65457000, -57.78813000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1871942'),
(12790, 'Lamim', 1998, 'MG', 31, 'BR', -20.77481000, -43.48487000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1871942'),
(12791, 'Landri Sales', 2008, 'PI', 31, 'BR', -7.23268000, -43.86456000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1871942'),
(12792, 'Lapa', 2022, 'PR', 31, 'BR', -25.76972000, -49.71583000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2064788'),
(12793, 'Lapão', 2002, 'BA', 31, 'BR', -11.38333000, -41.83194000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q1761847'),
(12794, 'Laranja da Terra', 2018, 'ES', 31, 'BR', -19.89889000, -41.05667000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q616506'),
(12795, 'Laranjal', 1998, 'MG', 31, 'BR', -21.35920000, -42.45663000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q979042'),
(12796, 'Laranjal', 2022, 'PR', 31, 'BR', -24.92573000, -52.45952000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q979042'),
(12797, 'Laranjal Paulista', 2021, 'SP', 31, 'BR', -23.04972000, -47.83667000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1795659'),
(12798, 'Laranjal do Jari', 1999, 'AP', 31, 'BR', 0.93828000, -53.22949000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1117969'),
(12799, 'Laranjeiras', 2003, 'SE', 31, 'BR', -10.80700000, -37.16896000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2014486'),
(12800, 'Laranjeiras do Sul', 2022, 'PR', 31, 'BR', -25.40778000, -52.41611000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1803900'),
(12801, 'Lassance', 1998, 'MG', 31, 'BR', -17.91014000, -44.70859000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1803900'),
(12802, 'Lastro', 2005, 'PB', 31, 'BR', -6.53740000, -38.18946000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1803900'),
(12803, 'Laurentino', 2014, 'SC', 31, 'BR', -27.20853000, -49.73411000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1803900'),
(12804, 'Lauro Muller', 2014, 'SC', 31, 'BR', -28.39278000, -49.39667000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q22040343'),
(12806, 'Lauro de Freitas', 2002, 'BA', 31, 'BR', -12.89444000, -38.32722000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q905164'),
(12807, 'Lavandeira', 2020, 'TO', 31, 'BR', -12.83658000, -46.39307000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1801192'),
(12808, 'Lavras', 1998, 'MG', 31, 'BR', -21.26738000, -45.04190000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1638425'),
(12809, 'Lavras da Mangabeira', 2016, 'CE', 31, 'BR', -6.75333000, -38.96444000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q664219'),
(12810, 'Lavras do Sul', 2001, 'RS', 31, 'BR', -30.84980000, -54.02830000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1750149'),
(12811, 'Lavrinhas', 2021, 'SP', 31, 'BR', -22.51707000, -44.88055000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1750149'),
(12812, 'Lavínia', 2021, 'SP', 31, 'BR', -21.17069000, -51.05184000, '2019-10-05 22:35:11', '2020-05-01 17:22:38', 1, 'Q1750149'),
(12813, 'Leandro Ferreira', 1998, 'MG', 31, 'BR', -19.67492000, -45.06134000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1750149'),
(12814, 'Lebon Régis', 2014, 'SC', 31, 'BR', -26.88524000, -50.67811000, '2019-10-05 22:35:11', '2020-05-01 17:22:38', 1, 'Q1750149'),
(12815, 'Leme', 2021, 'SP', 31, 'BR', -22.18556000, -47.39028000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q582880'),
(12816, 'Leme do Prado', 1998, 'MG', 31, 'BR', -17.05253000, -42.74591000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q582880'),
(12817, 'Lençóis', 2002, 'BA', 31, 'BR', -12.47259000, -41.30815000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q582880'),
(12818, 'Lençóis Paulista', 2021, 'SP', 31, 'BR', -22.59861000, -48.80028000, '2019-10-05 22:35:11', '2020-05-01 17:22:38', 1, 'Q1649601'),
(12819, 'Leoberto Leal', 2014, 'SC', 31, 'BR', -27.48804000, -49.25121000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1649601'),
(12820, 'Leopoldina', 1998, 'MG', 31, 'BR', -21.53489000, -42.64473000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1805359'),
(12821, 'Leopoldo de Bulhões', 2000, 'GO', 31, 'BR', -16.58142000, -48.90604000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q1805359'),
(12822, 'Leópolis', 2022, 'PR', 31, 'BR', -23.02502000, -50.72558000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q1805359'),
(12823, 'Liberato Salzano', 2001, 'RS', 31, 'BR', -27.53845000, -53.07401000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1805359'),
(12824, 'Liberdade', 1998, 'MG', 31, 'BR', -22.01634000, -44.34756000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q22062417'),
(12825, 'Licínio de Almeida', 2002, 'BA', 31, 'BR', -14.70644000, -42.50442000, '2019-10-05 22:35:11', '2020-05-01 17:22:36', 1, 'Q22062417'),
(12826, 'Lidianópolis', 2022, 'PR', 31, 'BR', -24.08135000, -51.64406000, '2019-10-05 22:35:11', '2020-05-01 17:22:37', 1, 'Q22062417'),
(12827, 'Lima Campos', 2015, 'MA', 31, 'BR', -4.55295000, -44.47986000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q22062417'),
(12828, 'Lima Duarte', 1998, 'MG', 31, 'BR', -21.79037000, -43.89583000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1754844'),
(12829, 'Limeira', 2021, 'SP', 31, 'BR', -22.56472000, -47.40167000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q841241'),
(12830, 'Limeira do Oeste', 1998, 'MG', 31, 'BR', -19.38786000, -50.61280000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q841241'),
(12831, 'Limoeiro', 2006, 'PE', 31, 'BR', -7.85092000, -35.44512000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2064902'),
(12832, 'Limoeiro de Anadia', 2007, 'AL', 31, 'BR', -9.72685000, -36.45407000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q961349'),
(12833, 'Limoeiro do Ajuru', 2009, 'PA', 31, 'BR', -1.89647000, -49.51015000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q2008564'),
(12834, 'Limoeiro do Norte', 2016, 'CE', 31, 'BR', -5.17406000, -38.02656000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1795215'),
(12835, 'Lindoeste', 2022, 'PR', 31, 'BR', -25.26796000, -53.56870000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1795215'),
(12836, 'Lindolfo Collor', 2001, 'RS', 31, 'BR', -29.57482000, -51.22786000, '2019-10-05 22:35:11', '2019-10-05 22:35:11', 1, 'Q1648535'),
(12837, 'Lindóia', 2021, 'SP', 31, 'BR', -22.51250000, -46.65352000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1439170'),
(12838, 'Lindóia do Sul', 2014, 'SC', 31, 'BR', -27.03246000, -52.04059000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1439170'),
(12839, 'Linha Nova', 2001, 'RS', 31, 'BR', -29.45535000, -51.21876000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q733173'),
(12840, 'Linhares', 2018, 'ES', 31, 'BR', -19.39111000, -40.07222000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1755402'),
(12841, 'Lins', 2021, 'SP', 31, 'BR', -21.67861000, -49.74250000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1750605'),
(12842, 'Livramento', 2005, 'PB', 31, 'BR', -7.34008000, -36.93209000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2065825'),
(12843, 'Livramento de Nossa Senhora', 2002, 'BA', 31, 'BR', -13.80680000, -41.99709000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1793460'),
(12844, 'Livramento do Brumado', 2002, 'BA', 31, 'BR', -13.65145000, -41.84564000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22040445'),
(12845, 'Lizarda', 2020, 'TO', 31, 'BR', -9.52341000, -46.97679000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22040445'),
(12846, 'Loanda', 2022, 'PR', 31, 'BR', -22.92306000, -53.13722000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1804157'),
(12847, 'Lobato', 2022, 'PR', 31, 'BR', -22.96387000, -52.00714000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q303107'),
(12848, 'Logradouro', 2005, 'PB', 31, 'BR', -6.55268000, -35.42864000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2012226'),
(12849, 'Londrina', 2022, 'PR', 31, 'BR', -23.31028000, -51.16278000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q192387'),
(12850, 'Lontra', 1998, 'MG', 31, 'BR', -15.83942000, -44.27821000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22062414'),
(12851, 'Lontras', 2014, 'SC', 31, 'BR', -27.17928000, -49.49924000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22062414'),
(12852, 'Lorena', 2021, 'SP', 31, 'BR', -22.73083000, -45.12472000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1948966'),
(12853, 'Loreto', 2015, 'MA', 31, 'BR', -7.11921000, -45.23138000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1948966'),
(12854, 'Lourdes', 2021, 'SP', 31, 'BR', -20.94345000, -50.23799000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1948966'),
(12855, 'Louveira', 2021, 'SP', 31, 'BR', -23.08252000, -46.93308000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1795673'),
(12856, 'Lucas', 2011, 'MT', 31, 'BR', -16.33333000, -55.93333000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22040474'),
(12857, 'Lucas do Rio Verde', 2011, 'MT', 31, 'BR', -13.04098000, -56.15577000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22040474'),
(12858, 'Lucena', 2005, 'PB', 31, 'BR', -6.92461000, -34.90267000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22040474'),
(12859, 'Lucianópolis', 2021, 'SP', 31, 'BR', -22.48385000, -49.54507000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q22040474'),
(12860, 'Luciara', 2011, 'MT', 31, 'BR', -11.01415000, -50.94299000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1805614'),
(12861, 'Lucrécia', 2019, 'RN', 31, 'BR', -6.10358000, -37.82437000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1805614'),
(12862, 'Lucélia', 2021, 'SP', 31, 'BR', -21.72028000, -51.01889000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1761493'),
(12863, 'Luisburgo', 1998, 'MG', 31, 'BR', -20.44641000, -42.07286000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1761493'),
(12864, 'Luislândia', 1998, 'MG', 31, 'BR', -16.19922000, -44.60969000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1761493'),
(12865, 'Luiz Alves', 2014, 'SC', 31, 'BR', -26.73625000, -48.88531000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1761493'),
(12866, 'Luiziana', 2022, 'PR', 31, 'BR', -24.34081000, -52.27043000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1761493'),
(12867, 'Luiziânia', 2021, 'SP', 31, 'BR', -21.68237000, -50.35305000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1761493'),
(12868, 'Luminárias', 1998, 'MG', 31, 'BR', -21.51526000, -44.92617000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1761493'),
(12869, 'Lunardelli', 2022, 'PR', 31, 'BR', -24.07214000, -51.76003000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1761493'),
(12870, 'Lupionópolis', 2022, 'PR', 31, 'BR', -22.73847000, -51.68386000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1761493'),
(12871, 'Lupércio', 2021, 'SP', 31, 'BR', -22.42771000, -49.81232000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1761493'),
(12872, 'Lutécia', 2021, 'SP', 31, 'BR', -22.30652000, -50.37817000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1761493'),
(12873, 'Luz', 1998, 'MG', 31, 'BR', -19.84190000, -45.67539000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1779998'),
(12874, 'Luzerna', 2014, 'SC', 31, 'BR', -27.07656000, -51.49348000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1779998'),
(12875, 'Luzilândia', 2008, 'PI', 31, 'BR', -3.60743000, -42.36456000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1924294'),
(12876, 'Luzinópolis', 2020, 'TO', 31, 'BR', -6.20636000, -47.83045000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1924294'),
(12877, 'Luziânia', 2000, 'GO', 31, 'BR', -16.25250000, -47.95028000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q956017'),
(12878, 'Luís Antônio', 2021, 'SP', 31, 'BR', -21.55997000, -47.81156000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q956017'),
(12879, 'Luís Correia', 2008, 'PI', 31, 'BR', -3.03117000, -41.51665000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q2064046'),
(12880, 'Luís Domingues', 2015, 'MA', 31, 'BR', -1.26964000, -45.83792000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q2064046'),
(12881, 'Luís Eduardo Magalhães', 2002, 'BA', 31, 'BR', -12.23208000, -46.11460000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1808537'),
(12882, 'Luís Gomes', 2019, 'RN', 31, 'BR', -6.38626000, -38.41895000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1808537'),
(12883, 'Lábrea', 2004, 'AM', 31, 'BR', -8.18437000, -66.07500000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q386631'),
(12884, 'Macajuba', 2002, 'BA', 31, 'BR', -12.14371000, -40.30199000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q386631'),
(12885, 'Macambira', 2003, 'SE', 31, 'BR', -10.68545000, -37.60003000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2013214'),
(12886, 'Macaparana', 2006, 'PE', 31, 'BR', -7.52866000, -35.44430000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2077831'),
(12887, 'Macapá', 1999, 'AP', 31, 'BR', 0.03889000, -51.06639000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q180215'),
(12888, 'Macarani', 2002, 'BA', 31, 'BR', -15.55700000, -40.39049000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1794428'),
(12889, 'Macatuba', 2021, 'SP', 31, 'BR', -22.50222000, -48.71139000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1653558'),
(12890, 'Macau', 2019, 'RN', 31, 'BR', -5.11500000, -36.63444000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q585538'),
(12891, 'Macaubal', 2021, 'SP', 31, 'BR', -20.86271000, -49.98530000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q585538'),
(12892, 'Macaé', 1997, 'RJ', 31, 'BR', -22.38484000, -41.78324000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q473250'),
(12893, 'Macaíba', 2019, 'RN', 31, 'BR', -5.94094000, -35.39833000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1786943'),
(12894, 'Macaúbas', 2002, 'BA', 31, 'BR', -13.01944000, -42.69861000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q776627'),
(12895, 'Macedônia', 2021, 'SP', 31, 'BR', -20.08825000, -50.17318000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q776627'),
(12896, 'Maceió', 2007, 'AL', 31, 'BR', -9.66583000, -35.73528000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q168888'),
(12897, 'Machacalis', 1998, 'MG', 31, 'BR', -17.08810000, -40.71826000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q168888'),
(12898, 'Machadinho', 2001, 'RS', 31, 'BR', -27.59183000, -51.68758000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q168888'),
(12899, 'Machadinho d\'Oeste', 2013, 'RO', 31, 'BR', -9.23953000, -62.11241000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q168888'),
(12900, 'Machado', 1998, 'MG', 31, 'BR', -21.69549000, -45.88809000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1780021'),
(12901, 'Machados', 2006, 'PE', 31, 'BR', -7.71477000, -35.51882000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1780021'),
(12902, 'Macieira', 2014, 'SC', 31, 'BR', -26.81070000, -51.34343000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1815712'),
(12903, 'Macuco', 1997, 'RJ', 31, 'BR', -22.02427000, -42.27369000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1815712'),
(12904, 'Macururé', 2002, 'BA', 31, 'BR', -9.28091000, -38.91463000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1815712'),
(12905, 'Madalena', 2016, 'CE', 31, 'BR', -4.87622000, -39.47587000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1815712'),
(12906, 'Madeiro', 2008, 'PI', 31, 'BR', -3.54658000, -42.51114000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1815712'),
(12907, 'Madre de Deus', 2002, 'BA', 31, 'BR', -12.74083000, -38.62083000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q985510'),
(12908, 'Madre de Deus de Minas', 1998, 'MG', 31, 'BR', -21.48464000, -44.33264000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q985510'),
(12909, 'Maetinga', 2002, 'BA', 31, 'BR', -14.66530000, -41.49055000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q985510'),
(12910, 'Mafra', 2014, 'SC', 31, 'BR', -26.11139000, -49.80528000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1020414'),
(12911, 'Magalhães Barata', 2009, 'PA', 31, 'BR', -0.81838000, -47.62990000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q2008546'),
(12912, 'Magalhães de Almeida', 2015, 'MA', 31, 'BR', -3.32329000, -42.12855000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q2008546'),
(12913, 'Magda', 2021, 'SP', 31, 'BR', -20.57602000, -50.22413000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2008546'),
(12914, 'Magé', 1997, 'RJ', 31, 'BR', -22.63490000, -43.12056000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q2008546'),
(12915, 'Maiquinique', 2002, 'BA', 31, 'BR', -15.69897000, -40.26411000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2008546'),
(12916, 'Mairi', 2002, 'BA', 31, 'BR', -11.71139000, -40.14889000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1795253'),
(12917, 'Mairinque', 2021, 'SP', 31, 'BR', -23.54583000, -47.18333000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1760053'),
(12918, 'Mairiporã', 2021, 'SP', 31, 'BR', -23.31861000, -46.58667000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q931560'),
(12919, 'Mairipotaba', 2000, 'GO', 31, 'BR', -17.32688000, -49.50788000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q931560'),
(12920, 'Major Gercino', 2014, 'SC', 31, 'BR', -27.42331000, -49.05744000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q931560'),
(12921, 'Major Isidoro', 2007, 'AL', 31, 'BR', -9.53222000, -36.98500000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1808271'),
(12922, 'Major Sales', 2019, 'RN', 31, 'BR', -6.40617000, -38.30965000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1808271'),
(12923, 'Major Vieira', 2014, 'SC', 31, 'BR', -26.49482000, -50.32712000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1808271'),
(12924, 'Malacacheta', 1998, 'MG', 31, 'BR', -17.83800000, -42.08895000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1755703'),
(12925, 'Malhada', 2002, 'BA', 31, 'BR', -14.19145000, -43.63151000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1755703'),
(12926, 'Malhada de Pedras', 2002, 'BA', 31, 'BR', -14.27766000, -41.89207000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1755703'),
(12927, 'Malhada dos Bois', 2003, 'SE', 31, 'BR', -10.32806000, -36.93573000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1755703'),
(12928, 'Malhador', 2003, 'SE', 31, 'BR', -10.65778000, -37.30472000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2013198'),
(12929, 'Mallet', 2022, 'PR', 31, 'BR', -25.90209000, -50.83050000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q741333'),
(12930, 'Malta', 2005, 'PB', 31, 'BR', -6.90400000, -37.51251000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q741333'),
(12931, 'Mamanguape', 2005, 'PB', 31, 'BR', -6.83861000, -35.12611000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1008338'),
(12932, 'Mambaí', 2000, 'GO', 31, 'BR', -14.44220000, -46.05807000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1008338'),
(12933, 'Mamborê', 2022, 'PR', 31, 'BR', -24.40607000, -52.61692000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1008338'),
(12934, 'Mamonas', 1998, 'MG', 31, 'BR', -15.03302000, -42.94513000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1008338'),
(12935, 'Mampituba', 2001, 'RS', 31, 'BR', -29.26145000, -50.01070000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1008338'),
(12936, 'Manacapuru', 2004, 'AM', 31, 'BR', -3.29972000, -60.62056000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q983526'),
(12937, 'Manaquiri', 2004, 'AM', 31, 'BR', -3.31667000, -60.35000000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22054566'),
(12938, 'Manari', 2006, 'PE', 31, 'BR', -8.88175000, -37.58760000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22054566'),
(12939, 'Manaus', 2004, 'AM', 31, 'BR', -3.04361000, -60.01282000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q40236'),
(12940, 'Manaíra', 2005, 'PB', 31, 'BR', -7.70611000, -38.15444000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q2009309'),
(12941, 'Mandaguari', 2022, 'PR', 31, 'BR', -23.54750000, -51.67083000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1803633'),
(12942, 'Mandaguaçu', 2022, 'PR', 31, 'BR', -23.34722000, -52.09528000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q675706'),
(12943, 'Mandirituba', 2022, 'PR', 31, 'BR', -25.84677000, -49.32704000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q675706'),
(12944, 'Manduri', 2021, 'SP', 31, 'BR', -23.05388000, -49.30262000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1800707'),
(12945, 'Manfrinópolis', 2022, 'PR', 31, 'BR', -26.10011000, -53.35828000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q1800707'),
(12946, 'Manga', 1998, 'MG', 31, 'BR', -14.65871000, -44.12621000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q283581'),
(12947, 'Mangaratiba', 1997, 'RJ', 31, 'BR', -22.95972000, -44.04056000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q988680'),
(12948, 'Mangueirinha', 2022, 'PR', 31, 'BR', -26.03649000, -52.22257000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q988680'),
(12949, 'Manhuaçu', 1998, 'MG', 31, 'BR', -20.18647000, -42.08653000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q935191'),
(12950, 'Manhumirim', 1998, 'MG', 31, 'BR', -20.34755000, -41.93559000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1789756'),
(12951, 'Manicoré', 2004, 'AM', 31, 'BR', -5.80917000, -61.30028000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q43490'),
(12952, 'Manoel Emídio', 2008, 'PI', 31, 'BR', -8.16469000, -43.83360000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q43490'),
(12953, 'Manoel Ribas', 2022, 'PR', 31, 'BR', -24.52402000, -51.63111000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q43490'),
(12954, 'Manoel Urbano', 2012, 'AC', 31, 'BR', -8.83889000, -69.25972000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1772688'),
(12955, 'Manoel Viana', 2001, 'RS', 31, 'BR', -29.40696000, -55.56870000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1750110'),
(12956, 'Manoel Vitorino', 2002, 'BA', 31, 'BR', -14.00487000, -40.48008000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1750110'),
(12957, 'Mansidão', 2002, 'BA', 31, 'BR', -11.20780000, -44.14109000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1750110'),
(12958, 'Mantena', 1998, 'MG', 31, 'BR', -18.68550000, -41.10755000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2008042'),
(12959, 'Mantenópolis', 2018, 'ES', 31, 'BR', -18.86250000, -41.12278000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1806721'),
(12960, 'Maquiné', 2001, 'RS', 31, 'BR', -29.57608000, -50.25993000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1806721'),
(12961, 'Mar Vermelho', 2007, 'AL', 31, 'BR', -9.49247000, -36.35042000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2028214'),
(12962, 'Mar de Espanha', 1998, 'MG', 31, 'BR', -21.87597000, -43.02192000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1754689'),
(12963, 'Mara Rosa', 2000, 'GO', 31, 'BR', -14.01285000, -49.39507000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1754689'),
(12964, 'Marabá', 2009, 'PA', 31, 'BR', -5.38146000, -49.13232000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q509818'),
(12965, 'Marabá Paulista', 2021, 'SP', 31, 'BR', -22.11727000, -52.05131000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q509818'),
(12966, 'Maracaju', 2010, 'MS', 31, 'BR', -21.61444000, -55.16833000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1792127'),
(12967, 'Maracajá', 2014, 'SC', 31, 'BR', -28.85185000, -49.46443000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1792127'),
(12968, 'Maracanaú', 2016, 'CE', 31, 'BR', -3.87667000, -38.62556000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1794732'),
(12969, 'Maracanã', 2009, 'PA', 31, 'BR', -0.75819000, -47.48755000, '2019-10-05 22:35:12', '2020-05-01 17:22:37', 1, 'Q2089279'),
(12970, 'Maracaçumé', 2015, 'MA', 31, 'BR', -2.04278000, -45.95917000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q2064676'),
(12971, 'Maracaí', 2021, 'SP', 31, 'BR', -22.61056000, -50.66722000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1760928'),
(12972, 'Maracás', 2002, 'BA', 31, 'BR', -13.44111000, -40.43083000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1795100'),
(12973, 'Maragogi', 2007, 'AL', 31, 'BR', -9.01222000, -35.22250000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q971736'),
(12974, 'Maragogipe', 2002, 'BA', 31, 'BR', -12.77778000, -38.91944000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1761467'),
(12975, 'Maraial', 2006, 'PE', 31, 'BR', -8.78250000, -35.80889000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q2011013'),
(12976, 'Marajá do Sena', 2015, 'MA', 31, 'BR', -4.70009000, -45.63144000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q2011013'),
(12977, 'Maranguape', 2016, 'CE', 31, 'BR', -3.99581000, -38.72969000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1795232'),
(12978, 'Maranhãozinho', 2015, 'MA', 31, 'BR', -2.45247000, -45.98846000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q1795232'),
(12979, 'Marapanim', 2009, 'PA', 31, 'BR', -0.84063000, -47.70897000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1990477'),
(12980, 'Marapoama', 2021, 'SP', 31, 'BR', -21.25761000, -49.14859000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1990477'),
(12981, 'Marataizes', 2018, 'ES', 31, 'BR', -21.04333000, -40.82444000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q22040763'),
(12983, 'Maratá', 2001, 'RS', 31, 'BR', -29.54921000, -51.55693000, '2019-10-05 22:35:12', '2020-05-01 17:22:38', 1, 'Q1806661'),
(12984, 'Marau', 2001, 'RS', 31, 'BR', -28.44217000, -52.27411000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1784401'),
(12985, 'Maravilha', 2007, 'AL', 31, 'BR', -9.22305000, -37.37936000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1784401'),
(12986, 'Maravilha', 2014, 'SC', 31, 'BR', -26.75798000, -53.18283000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1784401'),
(12987, 'Maravilhas', 1998, 'MG', 31, 'BR', -19.51232000, -44.67995000, '2019-10-05 22:35:12', '2019-10-05 22:35:12', 1, 'Q1784401'),
(12988, 'Maraã', 2004, 'AM', 31, 'BR', -1.82403000, -65.35883000, '2019-10-05 22:35:12', '2020-05-01 17:22:36', 1, 'Q22054605'),
(12989, 'Maraú', 2002, 'BA', 31, 'BR', -14.10395000, -39.01490000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q2028783'),
(12990, 'Marcação', 2005, 'PB', 31, 'BR', -6.74717000, -34.99103000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q2028783'),
(12991, 'Marcelino Ramos', 2001, 'RS', 31, 'BR', -27.47851000, -51.95065000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2028783'),
(12992, 'Marcelino Vieira', 2019, 'RN', 31, 'BR', -6.32176000, -38.14275000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2028783'),
(12993, 'Marcelândia', 2011, 'MT', 31, 'BR', -11.00372000, -54.13318000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q2012355'),
(12994, 'Marcionílio Souza', 2002, 'BA', 31, 'BR', -13.11581000, -40.62915000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q2012355'),
(12995, 'Marco', 2016, 'CE', 31, 'BR', -3.18943000, -40.26283000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q22059549'),
(12996, 'Marcolândia', 2008, 'PI', 31, 'BR', -7.40891000, -40.74945000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q22059549'),
(12997, 'Marcos Parente', 2008, 'PI', 31, 'BR', -7.05412000, -43.91664000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q22059549'),
(12998, 'Marechal Cândido Rondon', 2022, 'PR', 31, 'BR', -24.55611000, -54.05667000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q2003833'),
(12999, 'Marechal Deodoro', 2007, 'AL', 31, 'BR', -9.71028000, -35.89500000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q985275'),
(13000, 'Marechal Floriano', 2018, 'ES', 31, 'BR', -20.41278000, -40.68306000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1806664'),
(13001, 'Marechal Thaumaturgo', 2012, 'AC', 31, 'BR', -8.94111000, -72.79167000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1772613'),
(13002, 'Marema', 2014, 'SC', 31, 'BR', -26.79773000, -52.62047000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1772613'),
(13003, 'Mari', 2005, 'PB', 31, 'BR', -7.07138000, -35.30258000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q965785'),
(13004, 'Maria Helena', 2022, 'PR', 31, 'BR', -23.55348000, -53.23153000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q965785'),
(13005, 'Maria da Fé', 1998, 'MG', 31, 'BR', -22.32718000, -45.31720000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q965785'),
(13006, 'Marialva', 2022, 'PR', 31, 'BR', -23.48500000, -51.79167000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1003473'),
(13007, 'Mariana', 1998, 'MG', 31, 'BR', -20.37778000, -43.41611000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q984567'),
(13008, 'Mariana Pimentel', 2001, 'RS', 31, 'BR', -30.30220000, -51.58133000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q984567'),
(13009, 'Mariano Moro', 2001, 'RS', 31, 'BR', -27.35013000, -52.18363000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q984567'),
(13010, 'Marianópolis do Tocantins', 2020, 'TO', 31, 'BR', -9.81443000, -49.71607000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q984567'),
(13011, 'Maribondo', 2007, 'AL', 31, 'BR', -9.57722000, -36.30528000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2021217'),
(13012, 'Maricá', 1997, 'RJ', 31, 'BR', -22.91630000, -42.82203000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q847037'),
(13013, 'Marilac', 1998, 'MG', 31, 'BR', -18.49516000, -42.06556000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q847037'),
(13014, 'Marilena', 2022, 'PR', 31, 'BR', -22.72742000, -53.08170000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q847037'),
(13015, 'Mariluz', 2022, 'PR', 31, 'BR', -24.06183000, -53.22984000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q847037'),
(13016, 'Marilândia', 2018, 'ES', 31, 'BR', -19.42199000, -40.52021000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q1806633'),
(13017, 'Marilândia do Sul', 2022, 'PR', 31, 'BR', -23.74380000, -51.29410000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q1806633'),
(13018, 'Maringá', 2022, 'PR', 31, 'BR', -23.42528000, -51.93861000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q208669'),
(13019, 'Marinópolis', 2021, 'SP', 31, 'BR', -20.48780000, -50.83467000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q208669'),
(13020, 'Maripá', 2022, 'PR', 31, 'BR', -24.46267000, -53.80179000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q208669'),
(13021, 'Maripá de Minas', 1998, 'MG', 31, 'BR', -21.70096000, -42.95929000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q208669'),
(13022, 'Marituba', 2009, 'PA', 31, 'BR', -1.37399000, -48.31420000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q208669'),
(13023, 'Marizópolis', 2005, 'PB', 31, 'BR', -6.82133000, -38.33496000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q208669'),
(13024, 'Mariápolis', 2021, 'SP', 31, 'BR', -21.77944000, -51.16754000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q208669'),
(13025, 'Mariópolis', 2022, 'PR', 31, 'BR', -26.31630000, -52.57421000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q208669'),
(13026, 'Marliéria', 1998, 'MG', 31, 'BR', -19.69241000, -42.62724000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q208669'),
(13027, 'Marmeleiro', 2022, 'PR', 31, 'BR', -26.23791000, -53.09751000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q208669'),
(13028, 'Marmelópolis', 1998, 'MG', 31, 'BR', -22.45970000, -45.16973000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q208669'),
(13029, 'Marques de Souza', 2001, 'RS', 31, 'BR', -29.27327000, -52.15720000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q208669'),
(13030, 'Marquinho', 2022, 'PR', 31, 'BR', -25.10570000, -52.25677000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1803569'),
(13031, 'Martinho Campos', 1998, 'MG', 31, 'BR', -19.42013000, -45.17974000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q522303'),
(13032, 'Martins', 2019, 'RN', 31, 'BR', -6.09212000, -37.92472000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q522303'),
(13033, 'Martins Soares', 1998, 'MG', 31, 'BR', -20.25994000, -41.83980000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q522303'),
(13034, 'Martinópole', 2016, 'CE', 31, 'BR', -3.19761000, -40.60948000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q522303'),
(13035, 'Martinópolis', 2021, 'SP', 31, 'BR', -22.14583000, -51.17083000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q1800770'),
(13036, 'Maruim', 2003, 'SE', 31, 'BR', -10.73750000, -37.08167000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q609459'),
(13037, 'Marumbi', 2022, 'PR', 31, 'BR', -23.76658000, -51.66062000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q609459'),
(13038, 'Marzagão', 2000, 'GO', 31, 'BR', -17.99103000, -48.68316000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q1806945'),
(13039, 'Marília', 2021, 'SP', 31, 'BR', -22.21389000, -49.94583000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q737660'),
(13040, 'Mascote', 2002, 'BA', 31, 'BR', -15.56306000, -39.30250000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q956401'),
(13041, 'Massapê', 2016, 'CE', 31, 'BR', -3.50912000, -40.36655000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q2028110'),
(13042, 'Massapê do Piauí', 2008, 'PI', 31, 'BR', -7.53879000, -41.01755000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q2028110'),
(13043, 'Massaranduba', 2005, 'PB', 31, 'BR', -7.20476000, -35.78379000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2079004'),
(13044, 'Massaranduba', 2014, 'SC', 31, 'BR', -26.61983000, -48.98214000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2079004'),
(13045, 'Mata', 2001, 'RS', 31, 'BR', -29.54895000, -54.44545000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q960563'),
(13046, 'Mata Grande', 2007, 'AL', 31, 'BR', -8.98543000, -37.76150000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2027641'),
(13047, 'Mata Roma', 2015, 'MA', 31, 'BR', -3.58219000, -43.23178000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2027641'),
(13048, 'Mata Verde', 1998, 'MG', 31, 'BR', -15.76794000, -40.69882000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1755144'),
(13049, 'Mata de São João', 2002, 'BA', 31, 'BR', -12.53028000, -38.29917000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q648152'),
(13050, 'Mataraca', 2005, 'PB', 31, 'BR', -6.55476000, -35.03690000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q648152'),
(13051, 'Mateiros', 2020, 'TO', 31, 'BR', -10.41802000, -46.48380000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q648152'),
(13052, 'Matelândia', 2022, 'PR', 31, 'BR', -25.24083000, -53.99639000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q1803927'),
(13053, 'Materlândia', 1998, 'MG', 31, 'BR', -18.44658000, -43.01437000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q1803927'),
(13054, 'Mateus Leme', 1998, 'MG', 31, 'BR', -20.01630000, -44.43422000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q631022'),
(13055, 'Mathias Lobato', 1998, 'MG', 31, 'BR', -18.60479000, -41.96192000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q631022'),
(13056, 'Matias Barbosa', 1998, 'MG', 31, 'BR', -21.87484000, -43.30507000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1764539'),
(13057, 'Matias Cardoso', 1998, 'MG', 31, 'BR', -14.84562000, -43.69791000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1764539'),
(13058, 'Matias Olímpio', 2008, 'PI', 31, 'BR', -3.68774000, -42.60695000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q1894372'),
(13059, 'Matina', 2002, 'BA', 31, 'BR', -13.89342000, -42.98279000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1894372'),
(13060, 'Matinha', 2015, 'MA', 31, 'BR', -3.10056000, -45.03361000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1116758'),
(13061, 'Matinhas', 2005, 'PB', 31, 'BR', -7.12428000, -35.76070000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1116758'),
(13062, 'Matinhos', 2022, 'PR', 31, 'BR', -25.76114000, -48.57290000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1116758'),
(13063, 'Matipó', 1998, 'MG', 31, 'BR', -20.30226000, -42.31613000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q1790074'),
(13064, 'Mato Castelhano', 2001, 'RS', 31, 'BR', -28.27248000, -52.17628000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1790074'),
(13065, 'Mato Grosso', 2005, 'PB', 31, 'BR', -6.54728000, -37.75067000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1790074'),
(13066, 'Mato Leitão', 2001, 'RS', 31, 'BR', -29.51457000, -52.13856000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q1790074'),
(13067, 'Mato Queimado', 2001, 'RS', 31, 'BR', -28.25680000, -54.67109000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1790074'),
(13068, 'Mato Rico', 2022, 'PR', 31, 'BR', -24.70689000, -52.24085000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1790074'),
(13069, 'Mato Verde', 1998, 'MG', 31, 'BR', -15.42884000, -42.87463000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1790065'),
(13070, 'Matos Costa', 2014, 'SC', 31, 'BR', -26.48360000, -51.16293000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1790065'),
(13071, 'Matozinhos', 1998, 'MG', 31, 'BR', -19.52070000, -44.05031000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1755730'),
(13072, 'Matrinchã', 2000, 'GO', 31, 'BR', -15.33819000, -50.82655000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q1755730'),
(13073, 'Matriz de Camaragibe', 2007, 'AL', 31, 'BR', -9.15167000, -35.53333000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q2013614'),
(13074, 'Matupá', 2011, 'MT', 31, 'BR', -10.01532000, -54.35496000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q2013614'),
(13075, 'Maturéia', 2005, 'PB', 31, 'BR', -7.27175000, -37.33940000, '2019-10-05 22:35:13', '2020-05-01 17:22:37', 1, 'Q2009368'),
(13076, 'Matutina', 1998, 'MG', 31, 'BR', -19.19364000, -45.99564000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1761228'),
(13077, 'Matão', 2021, 'SP', 31, 'BR', -21.59547000, -48.45033000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q1649629'),
(13078, 'Matões', 2015, 'MA', 31, 'BR', -5.41542000, -43.40049000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q1649629'),
(13079, 'Matões do Norte', 2015, 'MA', 31, 'BR', -3.77876000, -44.40753000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q1649629'),
(13080, 'Maurilândia', 2000, 'GO', 31, 'BR', -18.05037000, -50.32935000, '2019-10-05 22:35:13', '2020-05-01 17:22:36', 1, 'Q1649629'),
(13081, 'Maurilândia do Tocantins', 2020, 'TO', 31, 'BR', -5.98609000, -47.57986000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q1649629'),
(13082, 'Mauriti', 2016, 'CE', 31, 'BR', -7.38806000, -38.63645000, '2019-10-05 22:35:13', '2019-10-05 22:35:13', 1, 'Q1993771'),
(13083, 'Mauá', 2021, 'SP', 31, 'BR', -23.66364000, -46.44656000, '2019-10-05 22:35:13', '2020-05-01 17:22:38', 1, 'Q331300'),
(13084, 'Mauá da Serra', 2022, 'PR', 31, 'BR', -23.91107000, -51.18026000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q331300'),
(13085, 'Maués', 2004, 'AM', 31, 'BR', -3.38361000, -57.71861000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q1808581'),
(13086, 'Maxaranguape', 2019, 'RN', 31, 'BR', -5.46874000, -35.35657000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1808581'),
(13087, 'Maximiliano de Almeida', 2001, 'RS', 31, 'BR', -27.58802000, -51.80358000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1808581'),
(13088, 'Mazagão', 1999, 'AP', 31, 'BR', -0.11500000, -51.28944000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q1800704'),
(13089, 'Maçambará', 2001, 'RS', 31, 'BR', -29.05822000, -55.61102000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q1800704'),
(13090, 'Medeiros', 1998, 'MG', 31, 'BR', -20.01139000, -46.35800000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1755734'),
(13091, 'Medeiros Neto', 2002, 'BA', 31, 'BR', -17.37389000, -40.22056000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1794359'),
(13092, 'Medianeira', 2022, 'PR', 31, 'BR', -25.29528000, -54.09389000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q375318'),
(13093, 'Medicilândia', 2009, 'PA', 31, 'BR', -3.15674000, -53.17769000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q375318'),
(13094, 'Medina', 1998, 'MG', 31, 'BR', -16.30152000, -41.53818000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q22062380'),
(13095, 'Meleiro', 2014, 'SC', 31, 'BR', -28.85788000, -49.59973000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1799434'),
(13096, 'Melgaço', 2009, 'PA', 31, 'BR', -1.66163000, -51.05884000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q2011923'),
(13097, 'Mendes', 1997, 'RJ', 31, 'BR', -22.54316000, -43.74899000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1798722'),
(13098, 'Mendes Pimentel', 1998, 'MG', 31, 'BR', -18.62642000, -41.34871000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1798722'),
(13099, 'Mendonça', 2021, 'SP', 31, 'BR', -21.19620000, -49.56427000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q1800752'),
(13100, 'Mercedes', 2022, 'PR', 31, 'BR', -24.42600000, -54.17683000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1800752'),
(13101, 'Mercês', 1998, 'MG', 31, 'BR', -21.18716000, -43.33657000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q376377'),
(13102, 'Meridiano', 2021, 'SP', 31, 'BR', -20.41766000, -50.20536000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q376377'),
(13103, 'Meruoca', 2016, 'CE', 31, 'BR', -3.58604000, -40.46730000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2028094'),
(13104, 'Mesquita', 1997, 'RJ', 31, 'BR', -22.78993000, -43.45966000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q984780'),
(13105, 'Mesquita', 1998, 'MG', 31, 'BR', -19.24964000, -42.61603000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1022432'),
(13106, 'Messias', 2007, 'AL', 31, 'BR', -9.38333000, -35.84167000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2028160'),
(13107, 'Messias Targino', 2019, 'RN', 31, 'BR', -6.08097000, -37.48508000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2028160'),
(13108, 'Mesópolis', 2021, 'SP', 31, 'BR', -19.95808000, -50.60938000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q2028160'),
(13109, 'Miguel Alves', 2008, 'PI', 31, 'BR', -4.15790000, -42.77263000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q677160'),
(13110, 'Miguel Calmon', 2002, 'BA', 31, 'BR', -11.42889000, -40.59500000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1793651'),
(13111, 'Miguel Leão', 2008, 'PI', 31, 'BR', -5.72867000, -42.68942000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q1793651'),
(13112, 'Miguel Pereira', 1997, 'RJ', 31, 'BR', -22.45389000, -43.46889000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1798731'),
(13113, 'Miguelópolis', 2021, 'SP', 31, 'BR', -20.17944000, -48.03194000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q1759485'),
(13114, 'Milagres', 2016, 'CE', 31, 'BR', -7.26057000, -38.96244000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2092809'),
(13115, 'Milagres', 2002, 'BA', 31, 'BR', -12.95591000, -39.81783000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2092809'),
(13116, 'Milagres do Maranhão', 2015, 'MA', 31, 'BR', -3.50554000, -42.86468000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q2092809'),
(13117, 'Milhã', 2016, 'CE', 31, 'BR', -5.66904000, -39.22525000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q2092809'),
(13118, 'Milton Brandão', 2008, 'PI', 31, 'BR', -4.74021000, -41.59457000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q2092809'),
(13119, 'Mimoso de Goiás', 2000, 'GO', 31, 'BR', -15.04832000, -48.34448000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q2092809'),
(13120, 'Mimoso do Sul', 2018, 'ES', 31, 'BR', -21.10474000, -41.37566000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1795062'),
(13121, 'Minador do Negrão', 2007, 'AL', 31, 'BR', -9.30528000, -36.86472000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q767029'),
(13122, 'Minas Novas', 1998, 'MG', 31, 'BR', -17.35500000, -42.40892000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1755693'),
(13123, 'Minas do Leão', 2001, 'RS', 31, 'BR', -30.07189000, -52.11996000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q1750029'),
(13124, 'Minaçu', 2000, 'GO', 31, 'BR', -13.42682000, -48.38780000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q1750029'),
(13125, 'Minduri', 1998, 'MG', 31, 'BR', -21.66835000, -44.59475000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1750029'),
(13126, 'Mineiros', 2000, 'GO', 31, 'BR', -17.56944000, -52.55111000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1795244'),
(13127, 'Mineiros do Tietê', 2021, 'SP', 31, 'BR', -22.44860000, -48.44074000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q1795244'),
(13128, 'Ministro Andreazza', 2013, 'RO', 31, 'BR', -11.16029000, -61.56897000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q775204'),
(13129, 'Mira Estrela', 2021, 'SP', 31, 'BR', -19.97244000, -50.13043000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q775204'),
(13130, 'Mirabela', 1998, 'MG', 31, 'BR', -16.27910000, -44.16713000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1790146'),
(13131, 'Miracatu', 2021, 'SP', 31, 'BR', -24.28139000, -47.45972000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1760269'),
(13132, 'Miracema', 1997, 'RJ', 31, 'BR', -21.37442000, -42.15048000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1804056'),
(13133, 'Miracema do Tocantins', 2020, 'TO', 31, 'BR', -9.76855000, -48.58348000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1804056'),
(13134, 'Mirador', 2015, 'MA', 31, 'BR', -6.47266000, -45.11312000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2089741'),
(13135, 'Mirador', 2022, 'PR', 31, 'BR', -23.21282000, -52.75351000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2089741'),
(13136, 'Miradouro', 1998, 'MG', 31, 'BR', -20.85191000, -42.38714000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2089741'),
(13137, 'Miraguaí', 2001, 'RS', 31, 'BR', -27.48658000, -53.75549000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q1786369'),
(13138, 'Miranda', 2010, 'MS', 31, 'BR', -20.24056000, -56.37833000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q519471'),
(13139, 'Miranda do Norte', 2015, 'MA', 31, 'BR', -3.53142000, -44.52685000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q519471'),
(13140, 'Mirandiba', 2006, 'PE', 31, 'BR', -8.13454000, -38.74089000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q519471'),
(13141, 'Mirandopólis', 2021, 'SP', 31, 'BR', -21.13361000, -51.10167000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q1645072'),
(13143, 'Mirangaba', 2002, 'BA', 31, 'BR', -10.80362000, -40.67056000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1645072'),
(13144, 'Miranorte', 2020, 'TO', 31, 'BR', -9.39114000, -48.66598000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1645072'),
(13145, 'Mirante', 2002, 'BA', 31, 'BR', -14.18613000, -40.78310000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1794459'),
(13146, 'Mirante da Serra', 2013, 'RO', 31, 'BR', -11.13706000, -62.86154000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1794459'),
(13147, 'Mirante do Paranapanema', 2021, 'SP', 31, 'BR', -22.29194000, -51.90639000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q383765'),
(13148, 'Miraselva', 2022, 'PR', 31, 'BR', -22.97660000, -51.47042000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q383765'),
(13149, 'Mirassol', 2021, 'SP', 31, 'BR', -20.84158000, -49.50879000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q383765'),
(13150, 'Mirassol d\'Oeste', 2011, 'MT', 31, 'BR', -15.63523000, -58.00508000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2012414'),
(13151, 'Mirassolândia', 2021, 'SP', 31, 'BR', -20.59295000, -49.48426000, '2019-10-05 22:35:14', '2020-05-01 17:22:38', 1, 'Q2012414'),
(13152, 'Miravânia', 1998, 'MG', 31, 'BR', -14.75285000, -44.42057000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q2012414'),
(13153, 'Miraí', 1998, 'MG', 31, 'BR', -21.14581000, -42.60077000, '2019-10-05 22:35:14', '2020-05-01 17:22:37', 1, 'Q1790116'),
(13154, 'Miraíma', 2016, 'CE', 31, 'BR', -3.56511000, -39.90390000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q1790116'),
(13155, 'Mirim Doce', 2014, 'SC', 31, 'BR', -27.17316000, -50.18872000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1790116'),
(13156, 'Mirinzal', 2015, 'MA', 31, 'BR', -2.04774000, -44.77403000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1790116'),
(13157, 'Missal', 2022, 'PR', 31, 'BR', -25.11125000, -54.25786000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1790116'),
(13158, 'Missão Velha', 2016, 'CE', 31, 'BR', -7.23707000, -39.11196000, '2019-10-05 22:35:14', '2020-05-01 17:22:36', 1, 'Q2027847'),
(13159, 'Mocajuba', 2009, 'PA', 31, 'BR', -2.58417000, -49.50722000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q2008596'),
(13160, 'Mococa', 2021, 'SP', 31, 'BR', -21.46778000, -47.00472000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1760484'),
(13161, 'Modelo', 2014, 'SC', 31, 'BR', -26.77290000, -53.05042000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1760484'),
(13162, 'Moeda', 1998, 'MG', 31, 'BR', -20.33273000, -44.01660000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1760484'),
(13163, 'Moema', 1998, 'MG', 31, 'BR', -19.84295000, -45.42119000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q1789780'),
(13164, 'Mogeiro', 2005, 'PB', 31, 'BR', -7.29944000, -35.47944000, '2019-10-05 22:35:14', '2019-10-05 22:35:14', 1, 'Q764440'),
(13165, 'Mogi Guaçu', 2021, 'SP', 31, 'BR', -22.36770000, -46.94552000, '2019-10-05 22:35:15', '2020-05-01 17:22:38', 1, 'Q956172'),
(13166, 'Mogi Mirim', 2021, 'SP', 31, 'BR', -22.43194000, -46.95778000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q600310'),
(13167, 'Mogi das Cruzes', 2021, 'SP', 31, 'BR', -23.56813000, -46.18770000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q194332'),
(13168, 'Moiporá', 2000, 'GO', 31, 'BR', -16.50172000, -50.76811000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q194332'),
(13169, 'Moita Bonita', 2003, 'SE', 31, 'BR', -10.57750000, -37.34278000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1857645'),
(13170, 'Moju', 2009, 'PA', 31, 'BR', -1.88389000, -48.76889000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q2008213'),
(13171, 'Mojuí Dos Campos', 2009, 'PA', 31, 'BR', -3.07044000, -54.57515000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q22074332'),
(13172, 'Mombaça', 2016, 'CE', 31, 'BR', -5.79217000, -39.77985000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q1920600'),
(13173, 'Mombuca', 2021, 'SP', 31, 'BR', -22.95107000, -47.60238000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1920600');

