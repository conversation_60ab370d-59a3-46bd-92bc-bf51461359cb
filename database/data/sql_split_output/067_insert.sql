INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(33391, 'Casinos', 1175, 'V', 207, 'ES', 39.70000000, -0.70000000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q1628765'),
(33392, 'Casla', 1192, 'SG', 207, 'ES', 41.16602000, -3.65643000, '2019-10-05 22:45:40', '2022-08-29 11:50:42', 1, 'Q1628765'),
(33393, '<PERSON><PERSON><PERSON>', 5113, 'Z', 207, 'ES', 41.23402000, -0.03945000, '2019-10-05 22:45:40', '2022-08-29 11:42:54', 1, 'Q64010'),
(33394, 'Caspue<PERSON><PERSON>', 5107, '<PERSON><PERSON>', 207, 'ES', 40.69497000, -2.97941000, '2019-10-05 22:45:40', '2022-08-29 11:06:45', 1, 'Q939792'),
(33395, '<PERSON><PERSON> de la <PERSON>lva', 5103, 'GI', 207, 'ES', 41.88784000, 2.87524000, '2019-10-05 22:45:40', '2022-08-29 10:53:16', 1, 'Q939792'),
(33396, 'Castalla', 5108, 'A', 207, 'ES', 38.59694000, -0.67207000, '2019-10-05 22:45:40', '2022-08-29 11:15:47', 1, 'Q1646122'),
(33397, 'Castañar de Ibor', 1190, 'CC', 207, 'ES', 39.62843000, -5.41709000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1630518'),
(33398, 'Castañares de Rioja', 1171, 'LO', 207, 'ES', 42.51248000, -2.93148000, '2019-10-05 22:45:40', '2022-08-29 12:05:09', 1, 'Q1648461'),
(33399, 'Castejón', 1204, 'NA', 207, 'ES', 42.16912000, -1.68951000, '2019-10-05 22:45:40', '2022-08-29 12:06:07', 1, 'Q1648461'),
(33400, 'Castejón de Alarba', 5113, 'Z', 207, 'ES', 41.18365000, -1.63612000, '2019-10-05 22:45:40', '2022-08-29 11:42:54', 1, 'Q1648461'),
(33401, 'Castejón de Henares', 5107, 'GU', 207, 'ES', 40.93762000, -2.78665000, '2019-10-05 22:45:40', '2022-08-29 11:06:45', 1, 'Q1656265'),
(33402, 'Castejón de Monegros', 1177, 'HU', 207, 'ES', 41.61802000, -0.24013000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q24013393'),
(33403, 'Castejón de Sos', 1177, 'HU', 207, 'ES', 42.51235000, 0.49241000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q984671'),
(33404, 'Castejón de Tornos', 5111, 'TE', 207, 'ES', 40.99726000, -1.42805000, '2019-10-05 22:45:40', '2022-08-29 11:29:44', 1, 'Q1652455'),
(33405, 'Castejón de Valdejasa', 5113, 'Z', 207, 'ES', 41.98204000, -0.99393000, '2019-10-05 22:45:40', '2022-08-29 11:42:54', 1, 'Q1650584'),
(33406, 'Castejón de las Armas', 5113, 'Z', 207, 'ES', 41.30976000, -1.81084000, '2019-10-05 22:45:40', '2022-08-29 11:42:54', 1, 'Q63156175'),
(33407, 'Castejón del Puente', 1177, 'HU', 207, 'ES', 41.96179000, 0.15883000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q1651365'),
(33408, 'Castel de Cabra', 5111, 'TE', 207, 'ES', 40.80255000, -0.69600000, '2019-10-05 22:45:40', '2022-08-29 11:29:44', 1, 'Q1653519'),
(33409, 'Castelflorite', 1177, 'HU', 207, 'ES', 41.80273000, -0.02169000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q984675'),
(33410, 'Castell de Cabres', 5110, 'CS', 207, 'ES', 40.66058000, 0.04217000, '2019-10-05 22:45:40', '2022-08-29 11:26:42', 1, 'Q984675'),
(33411, 'Castell de Castells', 5108, 'A', 207, 'ES', 38.72555000, -0.19242000, '2019-10-05 22:45:40', '2022-08-29 11:15:47', 1, 'Q984675'),
(33414, 'Castellanos de Castro', 1146, 'BU', 207, 'ES', 42.32806000, -4.03417000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q1626927'),
(33415, 'Castellanos de Moriscos', 1147, 'SA', 207, 'ES', 41.01917000, -5.59065000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1628745'),
(33416, 'Castellanos de Villiquera', 1147, 'SA', 207, 'ES', 41.05139000, -5.69477000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1640666'),
(33417, 'Castellanos de Zapardiel', 1189, 'AV', 207, 'ES', 41.08450000, -4.90984000, '2019-10-05 22:45:40', '2022-08-29 11:49:56', 1, 'Q378230'),
(33418, 'Castellar de Santiago', 5105, 'CR', 207, 'ES', 38.53928000, -3.27573000, '2019-10-05 22:45:40', '2022-08-29 11:03:24', 1, 'Q1644848'),
(33420, 'Castellar de la Frontera', 5096, 'CA', 207, 'ES', 36.31736000, -5.45407000, '2019-10-05 22:45:40', '2022-08-28 18:44:29', 1, 'Q1610266'),
(33421, 'Castellar de la Muela', 5107, 'GU', 207, 'ES', 40.81902000, -1.75932000, '2019-10-05 22:45:40', '2022-08-29 11:06:45', 1, 'Q1610266'),
(33422, 'Castellar del Vallès', 5102, 'B', 207, 'ES', 41.61667000, 2.08333000, '2019-10-05 22:45:40', '2022-08-29 10:50:00', 1, 'Q1610266'),
(33423, 'Castellbisbal', 5102, 'B', 207, 'ES', 41.47534000, 1.98174000, '2019-10-05 22:45:40', '2022-08-29 10:50:00', 1, 'Q13921'),
(33424, 'Castellcir', 5102, 'B', 207, 'ES', 41.76074000, 2.16128000, '2019-10-05 22:45:40', '2022-08-29 10:50:00', 1, 'Q15407'),
(33425, 'Castelldefels', 5102, 'B', 207, 'ES', 41.27794000, 1.97033000, '2019-10-05 22:45:40', '2022-08-29 10:50:00', 1, 'Q15407'),
(33427, 'Castellfollit de Riubregós', 5102, 'B', 207, 'ES', 41.76667000, 1.43333000, '2019-10-05 22:45:40', '2022-08-29 10:50:00', 1, 'Q15945'),
(33428, 'Castellfort', 5110, 'CS', 207, 'ES', 40.50208000, -0.19133000, '2019-10-05 22:45:40', '2022-08-29 11:26:42', 1, 'Q15945'),
(33429, 'Castellnou de Seana', 5104, 'L', 207, 'ES', 41.64802000, 0.97093000, '2019-10-05 22:45:40', '2022-08-29 10:55:25', 1, 'Q1116148'),
(33430, 'Castellnovo', 5110, 'CS', 207, 'ES', 39.86667000, -0.45000000, '2019-10-05 22:45:40', '2022-08-29 11:26:42', 1, 'Q1635733'),
(33431, 'Castellolí', 5102, 'B', 207, 'ES', 41.59829000, 1.70057000, '2019-10-05 22:45:40', '2022-08-29 10:50:00', 1, 'Q24009579'),
(33432, 'Castellonet de la Conquesta', 1175, 'V', 207, 'ES', 38.91667000, -0.26667000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q24009579'),
(33433, 'Castellote', 5111, 'TE', 207, 'ES', 40.80000000, -0.31975000, '2019-10-05 22:45:40', '2022-08-29 11:29:44', 1, 'Q24015141'),
(33434, 'Castellserà', 5104, 'L', 207, 'ES', 41.75000000, 1.00000000, '2019-10-05 22:45:40', '2022-08-29 10:55:25', 1, 'Q1769681'),
(33435, 'Castellví de Rosanes', 5102, 'B', 207, 'ES', 41.45000000, 1.90000000, '2019-10-05 22:45:40', '2022-08-29 10:50:00', 1, 'Q1769681'),
(33437, 'Castelló de Rugat', 1175, 'V', 207, 'ES', 38.88333000, -0.36667000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q11286'),
(33438, 'Castelló de la Plana', 1175, 'V', 207, 'ES', 39.98567000, -0.04935000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q15092'),
(33439, 'Castelnou', 5111, 'TE', 207, 'ES', 41.22892000, -0.36434000, '2019-10-05 22:45:40', '2022-08-29 11:29:44', 1, 'Q24015145'),
(33440, 'Castelserás', 5111, 'TE', 207, 'ES', 40.98133000, -0.14666000, '2019-10-05 22:45:40', '2022-08-29 11:29:44', 1, 'Q932738'),
(33441, 'Castielfabib', 1175, 'V', 207, 'ES', 40.13076000, -1.30396000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q582026'),
(33442, 'Castiello de Jaca', 1177, 'HU', 207, 'ES', 42.62964000, -0.55020000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q24013390'),
(33443, 'Castigaleu', 1177, 'HU', 207, 'ES', 42.20374000, 0.57944000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q24013390'),
(33444, 'Castil de Peones', 1146, 'BU', 207, 'ES', 42.48347000, -3.38461000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q1607140'),
(33445, 'Castil de Vela', 1157, 'P', 207, 'ES', 41.98461000, -4.95920000, '2019-10-05 22:45:40', '2022-08-29 11:45:44', 1, 'Q925497'),
(33446, 'Castilblanco', 5092, 'BA', 207, 'ES', 39.28487000, -5.09098000, '2019-10-05 22:45:40', '2022-08-28 18:09:23', 1, 'Q780375'),
(33447, 'Castilblanco de los Arroyos', 1193, 'SE', 207, 'ES', 37.67576000, -5.98886000, '2019-10-05 22:45:40', '2022-08-28 19:08:49', 1, 'Q980056'),
(33448, 'Castildelgado', 1146, 'BU', 207, 'ES', 42.43756000, -3.08389000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q946296'),
(33449, 'Castilfalé', 1200, 'LE', 207, 'ES', 42.21991000, -5.42122000, '2019-10-05 22:45:40', '2020-05-01 17:23:16', 1, 'Q946296'),
(33450, 'Castilforte', 5107, 'GU', 207, 'ES', 40.55735000, -2.43226000, '2019-10-05 22:45:40', '2022-08-29 11:06:45', 1, 'Q519701'),
(33451, 'Castilfrío de la Sierra', 1208, 'SO', 207, 'ES', 41.91929000, -2.30497000, '2019-10-05 22:45:40', '2022-08-29 11:51:23', 1, 'Q830537'),
(33452, 'Castiliscar', 5113, 'Z', 207, 'ES', 42.37662000, -1.27326000, '2019-10-05 22:45:40', '2022-08-29 11:42:54', 1, 'Q1650715'),
(33453, 'Castillazuelo', 1177, 'HU', 207, 'ES', 42.06774000, 0.06491000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q1651300'),
(33454, 'Castilleja de Guzmán', 1193, 'SE', 207, 'ES', 37.40955000, -6.05515000, '2019-10-05 22:45:40', '2022-08-28 19:08:49', 1, 'Q1628350'),
(33455, 'Castilleja de la Cuesta', 1193, 'SE', 207, 'ES', 37.38594000, -6.05258000, '2019-10-05 22:45:40', '2022-08-28 19:08:49', 1, 'Q841589'),
(33456, 'Castilleja del Campo', 1193, 'SE', 207, 'ES', 37.38627000, -6.33443000, '2019-10-05 22:45:40', '2022-08-28 19:08:49', 1, 'Q1628373'),
(33457, 'Castillejo de Iniesta', 5106, 'CU', 207, 'ES', 39.53333000, -1.76667000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1902662'),
(33458, 'Castillejo de Martín Viejo', 1147, 'SA', 207, 'ES', 40.69728000, -6.63763000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1640598'),
(33459, 'Castillejo de Mesleón', 1192, 'SG', 207, 'ES', 41.28100000, -3.60137000, '2019-10-05 22:45:40', '2022-08-29 11:50:42', 1, 'Q1917630'),
(33460, 'Castillejo de Robledo', 1208, 'SO', 207, 'ES', 41.55867000, -3.49689000, '2019-10-05 22:45:40', '2022-08-29 11:51:23', 1, 'Q831405'),
(33461, 'Castillejo-Sierra', 5106, 'CU', 207, 'ES', 40.37477000, -2.14040000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1769868'),
(33462, 'Castillo de Bayuela', 1205, 'TO', 207, 'ES', 40.10032000, -4.68562000, '2019-10-05 22:45:40', '2022-08-29 11:08:29', 1, 'Q1641508'),
(33463, 'Castillo de Garcimuñoz', 5106, 'CU', 207, 'ES', 39.65928000, -2.38030000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1903518'),
(33464, 'Castillo de Locubín', 5100, 'J', 207, 'ES', 37.52858000, -3.94220000, '2019-10-05 22:45:40', '2022-08-28 19:04:30', 1, 'Q1611707'),
(33465, 'Castillo de Villamalefa', 5110, 'CS', 207, 'ES', 40.13333000, -0.38333000, '2019-10-05 22:45:40', '2022-08-29 11:26:42', 1, 'Q932479'),
(33466, 'Castillo-Albaráñez', 5106, 'CU', 207, 'ES', 40.29869000, -2.39277000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1769832'),
(33467, 'Castillonuevo', 1204, 'NA', 207, 'ES', 42.68270000, -1.03121000, '2019-10-05 22:45:40', '2022-08-29 12:06:07', 1, 'Q1636245'),
(33468, 'Castilléjar', 5098, 'GR', 207, 'ES', 37.71697000, -2.64060000, '2019-10-05 22:45:40', '2022-08-28 18:52:57', 1, 'Q555918'),
(33469, 'Castilnuevo', 5107, 'GU', 207, 'ES', 40.81431000, -1.85782000, '2019-10-05 22:45:40', '2022-08-29 11:06:45', 1, 'Q1654884'),
(33470, 'Castilruiz', 1208, 'SO', 207, 'ES', 41.87678000, -2.05930000, '2019-10-05 22:45:40', '2022-08-29 11:51:23', 1, 'Q830541'),
(33471, 'Castraz', 1147, 'SA', 207, 'ES', 40.70517000, -6.33383000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1640722'),
(33472, 'Castrejón de la Peña', 1157, 'P', 207, 'ES', 42.80795000, -4.59901000, '2019-10-05 22:45:40', '2022-08-29 11:45:44', 1, 'Q1929204'),
(33473, 'Castrelo de Miño', 5091, 'OR', 207, 'ES', 42.29798000, -8.06697000, '2019-10-05 22:45:40', '2022-08-28 17:53:26', 1, 'Q1605406'),
(33474, 'Castril', 5098, 'GR', 207, 'ES', 37.79581000, -2.78002000, '2019-10-05 22:45:40', '2022-08-28 18:52:57', 1, 'Q555913'),
(33475, 'Castrillo de Cabrera', 1200, 'LE', 207, 'ES', 42.34036000, -6.54451000, '2019-10-05 22:45:40', '2019-10-05 22:45:40', 1, 'Q141252'),
(33476, 'Castrillo de Don Juan', 1157, 'P', 207, 'ES', 41.79086000, -4.07031000, '2019-10-05 22:45:40', '2022-08-29 11:45:44', 1, 'Q1915050'),
(33477, 'Castrillo de Duero', 1183, 'VA', 207, 'ES', 41.57494000, -4.01547000, '2019-10-05 22:45:40', '2022-08-29 11:48:44', 1, 'Q1915050'),
(33478, 'Castrillo de Onielo', 1157, 'P', 207, 'ES', 41.85820000, -4.30125000, '2019-10-05 22:45:40', '2022-08-29 11:45:44', 1, 'Q1949179'),
(33479, 'Castrillo de Villavega', 1157, 'P', 207, 'ES', 42.45463000, -4.48069000, '2019-10-05 22:45:40', '2022-08-29 11:45:44', 1, 'Q24012212'),
(33480, 'Castrillo de la Guareña', 1161, 'ZA', 207, 'ES', 41.23043000, -5.32550000, '2019-10-05 22:45:40', '2022-08-29 11:48:03', 1, 'Q1778099'),
(33481, 'Castrillo de la Reina', 1146, 'BU', 207, 'ES', 41.98714000, -3.23560000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q1645109'),
(33482, 'Castrillo de la Valduerna', 1200, 'LE', 207, 'ES', 42.32393000, -6.13455000, '2019-10-05 22:45:40', '2019-10-05 22:45:40', 1, 'Q138258'),
(33483, 'Castrillo de la Vega', 1146, 'BU', 207, 'ES', 41.65166000, -3.78089000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q1628340'),
(33484, 'Castrillo del Val', 1146, 'BU', 207, 'ES', 42.31401000, -3.58501000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q24010548'),
(33485, 'Castrillo-Tejeriego', 1183, 'VA', 207, 'ES', 41.70340000, -4.37142000, '2019-10-05 22:45:40', '2022-08-29 11:48:44', 1, 'Q2047584'),
(33487, 'Castro Caldelas', 5091, 'OR', 207, 'ES', 42.37366000, -7.42578000, '2019-10-05 22:45:40', '2022-08-28 17:53:26', 1, 'Q1607646'),
(33488, 'Castro de Filabres', 5095, 'AL', 207, 'ES', 37.18484000, -2.44024000, '2019-10-05 22:45:40', '2022-08-28 18:41:41', 1, 'Q1607646'),
(33489, 'Castro de Fuentidueña', 1192, 'SG', 207, 'ES', 41.42044000, -3.85528000, '2019-10-05 22:45:40', '2022-08-29 11:50:42', 1, 'Q1917016'),
(33490, 'Castro de Rei', 5090, 'LU', 207, 'ES', 43.20866000, -7.40026000, '2019-10-05 22:45:41', '2022-08-28 17:49:36', 1, 'Q1917016'),
(33491, 'Castro del Río', 5097, 'CO', 207, 'ES', 37.69125000, -4.48058000, '2019-10-05 22:45:41', '2022-08-28 18:49:38', 1, 'Q1447412'),
(33492, 'Castro-Urdiales', 1170, 'S', 207, 'ES', 43.38285000, -3.22043000, '2019-10-05 22:45:41', '2019-10-05 22:45:41', 1, 'Q501090'),
(33493, 'Castrobol', 1183, 'VA', 207, 'ES', 42.13717000, -5.31423000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q24017072'),
(33494, 'Castrocalbón', 1200, 'LE', 207, 'ES', 42.19706000, -5.98226000, '2019-10-05 22:45:41', '2020-05-01 17:23:16', 1, 'Q137198'),
(33495, 'Castrocontrigo', 1200, 'LE', 207, 'ES', 42.18363000, -6.19022000, '2019-10-05 22:45:41', '2019-10-05 22:45:41', 1, 'Q1442747'),
(33496, 'Castrodeza', 1183, 'VA', 207, 'ES', 41.64921000, -4.95888000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q971452'),
(33497, 'Castrogonzalo', 1161, 'ZA', 207, 'ES', 41.99099000, -5.60301000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q1646325'),
(33498, 'Castrojimeno', 1192, 'SG', 207, 'ES', 41.39659000, -3.84726000, '2019-10-05 22:45:41', '2022-08-29 11:50:42', 1, 'Q1985310'),
(33499, 'Castromembibre', 1183, 'VA', 207, 'ES', 41.67246000, -5.30473000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q1907601'),
(33500, 'Castromocho', 1157, 'P', 207, 'ES', 42.03104000, -4.82404000, '2019-10-05 22:45:41', '2022-08-29 11:45:44', 1, 'Q1923846'),
(33501, 'Castromonte', 1183, 'VA', 207, 'ES', 41.77355000, -5.03909000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q1907214'),
(33502, 'Castronuevo', 1161, 'ZA', 207, 'ES', 41.72046000, -5.54315000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q24016731'),
(33503, 'Castronuevo de Esgueva', 1183, 'VA', 207, 'ES', 41.68180000, -4.58866000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q1916474'),
(33504, 'Castronuño', 1183, 'VA', 207, 'ES', 41.38918000, -5.26408000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q762915'),
(33505, 'Castropodame', 1200, 'LE', 207, 'ES', 42.57943000, -6.46837000, '2019-10-05 22:45:41', '2019-10-05 22:45:41', 1, 'Q44812'),
(33506, 'Castroponce', 1183, 'VA', 207, 'ES', 42.12646000, -5.18245000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q1921084'),
(33507, 'Castroserna de Abajo', 1192, 'SG', 207, 'ES', 41.20835000, -3.73387000, '2019-10-05 22:45:41', '2022-08-29 11:50:42', 1, 'Q1905697'),
(33508, 'Castroserracín', 1192, 'SG', 207, 'ES', 41.39329000, -3.80196000, '2019-10-05 22:45:41', '2022-08-29 11:50:42', 1, 'Q921837'),
(33509, 'Castroverde', 5090, 'LU', 207, 'ES', 43.03020000, -7.32428000, '2019-10-05 22:45:41', '2022-08-28 17:49:36', 1, 'Q1368794'),
(33510, 'Castroverde de Campos', 1161, 'ZA', 207, 'ES', 41.97049000, -5.31434000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q1778086'),
(33511, 'Castroverde de Cerrato', 1183, 'VA', 207, 'ES', 41.75609000, -4.22151000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q1907177'),
(33512, 'Castuera', 5092, 'BA', 207, 'ES', 38.73204000, -5.54390000, '2019-10-05 22:45:41', '2022-08-28 18:09:23', 1, 'Q515016'),
(33513, 'Catadau', 1175, 'V', 207, 'ES', 39.26667000, -0.56667000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1989352'),
(33514, 'Catarroja', 1175, 'V', 207, 'ES', 39.40000000, -0.40000000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1771131'),
(33515, 'Catoira', 1167, 'PO', 207, 'ES', 42.66748000, -8.72323000, '2019-10-05 22:45:41', '2022-08-28 17:57:54', 1, 'Q377244'),
(33516, 'Catral', 5108, 'A', 207, 'ES', 38.16061000, -0.80209000, '2019-10-05 22:45:41', '2022-08-29 11:15:47', 1, 'Q1752447'),
(33517, 'Catí', 5110, 'CS', 207, 'ES', 40.47156000, 0.02275000, '2019-10-05 22:45:41', '2022-08-29 11:26:42', 1, 'Q1752447'),
(33519, 'Caudete de las Fuentes', 1175, 'V', 207, 'ES', 39.55965000, -1.27853000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1752447'),
(33520, 'Cayuela', 1146, 'BU', 207, 'ES', 42.27223000, -3.81895000, '2019-10-05 22:45:41', '2022-08-29 11:24:19', 1, 'Q1645374'),
(33521, 'Cazalegas', 1205, 'TO', 207, 'ES', 40.00958000, -4.67606000, '2019-10-05 22:45:41', '2022-08-29 11:08:29', 1, 'Q1631908'),
(33522, 'Cazalilla', 5100, 'J', 207, 'ES', 37.98389000, -3.88295000, '2019-10-05 22:45:41', '2022-08-28 19:04:30', 1, 'Q1632610'),
(33523, 'Cazalla de la Sierra', 1193, 'SE', 207, 'ES', 37.93333000, -5.75000000, '2019-10-05 22:45:41', '2022-08-28 19:08:49', 1, 'Q1445283'),
(33524, 'Cazorla', 5100, 'J', 207, 'ES', 37.91495000, -3.00342000, '2019-10-05 22:45:41', '2022-08-28 19:04:30', 1, 'Q518339'),
(33525, 'Cazurra', 1161, 'ZA', 207, 'ES', 41.41583000, -5.70454000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q515234'),
(33526, 'Cañada Rosal', 1193, 'SE', 207, 'ES', 37.59924000, -5.21016000, '2019-10-05 22:45:41', '2022-08-28 19:08:49', 1, 'Q62859'),
(33527, 'Cañada Vellida', 5111, 'TE', 207, 'ES', 40.70811000, -0.91489000, '2019-10-05 22:45:41', '2022-08-29 11:29:44', 1, 'Q1653436'),
(33528, 'Cañada de Benatanduz', 5111, 'TE', 207, 'ES', 40.57934000, -0.53682000, '2019-10-05 22:45:41', '2022-08-29 11:29:44', 1, 'Q378331'),
(33529, 'Cañada de Calatrava', 5105, 'CR', 207, 'ES', 38.85429000, -4.02103000, '2019-10-05 22:45:41', '2022-08-29 11:03:24', 1, 'Q1658003'),
(33530, 'Cañada del Hoyo', 5106, 'CU', 207, 'ES', 39.96667000, -1.90000000, '2019-10-05 22:45:41', '2022-08-29 11:05:00', 1, 'Q1771107'),
(33531, 'Cañamaque', 1208, 'SO', 207, 'ES', 41.44453000, -2.23792000, '2019-10-05 22:45:41', '2022-08-29 11:51:23', 1, 'Q513222'),
(33532, 'Cañamares', 5106, 'CU', 207, 'ES', 40.45203000, -2.23983000, '2019-10-05 22:45:41', '2022-08-29 11:05:00', 1, 'Q2269620'),
(33533, 'Cañamero', 1190, 'CC', 207, 'ES', 39.37995000, -5.38857000, '2019-10-05 22:45:41', '2022-08-28 18:12:22', 1, 'Q1632497'),
(33534, 'Cañas', 1171, 'LO', 207, 'ES', 42.39240000, -2.84649000, '2019-10-05 22:45:41', '2022-08-29 12:05:09', 1, 'Q970306'),
(33535, 'Cañaveral', 1190, 'CC', 207, 'ES', 39.79198000, -6.39130000, '2019-10-05 22:45:41', '2022-08-28 18:12:22', 1, 'Q970306'),
(33536, 'Cañaveral de León', 5099, 'H', 207, 'ES', 38.01667000, -6.51667000, '2019-10-05 22:45:41', '2022-08-28 19:00:43', 1, 'Q983430'),
(33537, 'Cañaveras', 5106, 'CU', 207, 'ES', 40.35994000, -2.39611000, '2019-10-05 22:45:41', '2022-08-29 11:05:00', 1, 'Q1771178'),
(33538, 'Cañaveruelas', 5106, 'CU', 207, 'ES', 40.40024000, -2.63719000, '2019-10-05 22:45:41', '2022-08-29 11:05:00', 1, 'Q24011603'),
(33539, 'Cañete', 5106, 'CU', 207, 'ES', 40.05000000, -1.65000000, '2019-10-05 22:45:41', '2022-08-29 11:05:00', 1, 'Q24011602'),
(33540, 'Cañete de las Torres', 5097, 'CO', 207, 'ES', 37.86717000, -4.31835000, '2019-10-05 22:45:41', '2022-08-28 18:49:38', 1, 'Q1447425'),
(33541, 'Cañete la Real', 5101, 'MA', 207, 'ES', 36.95154000, -5.02440000, '2019-10-05 22:45:41', '2022-08-28 19:06:52', 1, 'Q1447425'),
(33542, 'Cañizal', 1161, 'ZA', 207, 'ES', 41.16654000, -5.36828000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q593380'),
(33543, 'Cañizar', 5107, 'GU', 207, 'ES', 40.76763000, -3.06399000, '2019-10-05 22:45:41', '2022-08-29 11:06:45', 1, 'Q1656474'),
(33544, 'Cañizar del Olivar', 5111, 'TE', 207, 'ES', 40.81622000, -0.64567000, '2019-10-05 22:45:41', '2022-08-29 11:29:44', 1, 'Q1651612'),
(33545, 'Cañizares', 5106, 'CU', 207, 'ES', 40.51870000, -2.19201000, '2019-10-05 22:45:41', '2022-08-29 11:05:00', 1, 'Q24013959'),
(33546, 'Cañizo', 1161, 'ZA', 207, 'ES', 41.76955000, -5.50199000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q24016739'),
(33548, 'Cebanico', 1200, 'LE', 207, 'ES', 42.72526000, -5.02568000, '2019-10-05 22:45:41', '2019-10-05 22:45:41', 1, 'Q24016739'),
(33549, 'Cebolla', 1205, 'TO', 207, 'ES', 39.94897000, -4.57175000, '2019-10-05 22:45:41', '2022-08-29 11:08:29', 1, 'Q24016739'),
(33550, 'Cebrecos', 1146, 'BU', 207, 'ES', 41.98408000, -3.59661000, '2019-10-05 22:45:41', '2022-08-29 11:24:19', 1, 'Q1637104'),
(33551, 'Cebreros', 1189, 'AV', 207, 'ES', 40.45835000, -4.46433000, '2019-10-05 22:45:41', '2022-08-29 11:49:56', 1, 'Q642073'),
(33552, 'Cebrones del Río', 1200, 'LE', 207, 'ES', 42.25726000, -5.82622000, '2019-10-05 22:45:41', '2020-05-01 17:23:16', 1, 'Q137129'),
(33553, 'Ceclavín', 1190, 'CC', 207, 'ES', 39.82231000, -6.77329000, '2019-10-05 22:45:41', '2022-08-28 18:12:22', 1, 'Q1632687'),
(33554, 'Cedeira', 5089, 'C', 207, 'ES', 43.66044000, -8.05719000, '2019-10-05 22:45:41', '2022-08-28 13:37:16', 1, 'Q1632687'),
(33555, 'Cedillo', 1190, 'CC', 207, 'ES', 39.65099000, -7.49817000, '2019-10-05 22:45:41', '2022-08-28 18:12:22', 1, 'Q1646364'),
(33556, 'Cedillo de la Torre', 1192, 'SG', 207, 'ES', 41.42482000, -3.60577000, '2019-10-05 22:45:41', '2022-08-29 11:50:42', 1, 'Q1939112'),
(33557, 'Cedillo del Condado', 1205, 'TO', 207, 'ES', 40.11260000, -3.92127000, '2019-10-05 22:45:41', '2022-08-29 11:08:29', 1, 'Q1628809'),
(33558, 'Cedrillas', 5111, 'TE', 207, 'ES', 40.43708000, -0.85150000, '2019-10-05 22:45:41', '2022-08-29 11:29:44', 1, 'Q1653295'),
(33559, 'Cee', 5089, 'C', 207, 'ES', 42.95466000, -9.18800000, '2019-10-05 22:45:41', '2022-08-28 13:37:16', 1, 'Q988707'),
(33560, 'Cehegín', 1176, 'MU', 207, 'ES', 38.09242000, -1.79850000, '2019-10-05 22:45:41', '2022-08-29 12:05:49', 1, 'Q1757489'),
(33561, 'Ceinos de Campos', 1183, 'VA', 207, 'ES', 42.03284000, -5.15007000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q1921049'),
(33562, 'Celada del Camino', 1146, 'BU', 207, 'ES', 42.26359000, -3.93362000, '2019-10-05 22:45:41', '2022-08-29 11:24:19', 1, 'Q975881'),
(33563, 'Celanova', 5091, 'OR', 207, 'ES', 42.15303000, -7.95513000, '2019-10-05 22:45:41', '2022-08-28 17:53:26', 1, 'Q975881'),
(33564, 'Cella', 5111, 'TE', 207, 'ES', 40.45325000, -1.28750000, '2019-10-05 22:45:41', '2022-08-29 11:29:44', 1, 'Q975881'),
(33565, 'Cellorigo', 1171, 'LO', 207, 'ES', 42.62703000, -3.00016000, '2019-10-05 22:45:41', '2022-08-29 12:05:09', 1, 'Q1637462'),
(33566, 'Celrà', 5103, 'GI', 207, 'ES', 42.03333000, 2.88333000, '2019-10-05 22:45:41', '2022-08-29 10:53:16', 1, 'Q13426'),
(33567, 'Cendejas de la Torre', 5107, 'GU', 207, 'ES', 40.97916000, -2.85053000, '2019-10-05 22:45:41', '2022-08-29 11:06:45', 1, 'Q1655035'),
(33568, 'Cenes de la Vega', 5098, 'GR', 207, 'ES', 37.16006000, -3.53548000, '2019-10-05 22:45:41', '2022-08-28 18:52:57', 1, 'Q555126'),
(33569, 'Cenicero', 1171, 'LO', 207, 'ES', 42.48130000, -2.64412000, '2019-10-05 22:45:41', '2022-08-29 12:05:09', 1, 'Q555126'),
(33570, 'Cenicientos', 1158, 'M', 207, 'ES', 40.26459000, -4.46683000, '2019-10-05 22:45:41', '2022-08-29 12:04:40', 1, 'Q685764'),
(33572, 'Cenlle', 5091, 'OR', 207, 'ES', 42.34183000, -8.08982000, '2019-10-05 22:45:41', '2022-08-28 17:53:26', 1, 'Q1607762'),
(33573, 'Centelles', 5102, 'B', 207, 'ES', 41.79746000, 2.21902000, '2019-10-05 22:45:41', '2022-08-29 10:50:00', 1, 'Q1607762'),
(33574, 'Centenera', 5107, 'GU', 207, 'ES', 40.65064000, -3.05120000, '2019-10-05 22:45:41', '2022-08-29 11:06:45', 1, 'Q1655972'),
(33575, 'Centenera de Andaluz', 1208, 'SO', 207, 'ES', 41.50679000, -2.71813000, '2019-10-05 22:45:41', '2022-08-29 11:51:23', 1, 'Q672878'),
(33576, 'Cepeda', 1147, 'SA', 207, 'ES', 40.46600000, -6.04100000, '2019-10-05 22:45:41', '2022-08-29 11:44:51', 1, 'Q1640738'),
(33577, 'Cepeda la Mora', 1189, 'AV', 207, 'ES', 40.45780000, -5.04833000, '2019-10-05 22:45:41', '2022-08-29 11:49:56', 1, 'Q818921'),
(33578, 'Cerbón', 1208, 'SO', 207, 'ES', 41.92934000, -2.16895000, '2019-10-05 22:45:41', '2022-08-29 11:51:23', 1, 'Q829793'),
(33579, 'Cercedilla', 1158, 'M', 207, 'ES', 40.74101000, -4.05644000, '2019-10-05 22:45:41', '2022-08-29 12:04:40', 1, 'Q1646215'),
(33580, 'Cerdanyola del Vallès', 5102, 'B', 207, 'ES', 41.49109000, 2.14079000, '2019-10-05 22:45:41', '2022-08-29 10:50:01', 1, 'Q1646215'),
(33581, 'Cerdido', 5089, 'C', 207, 'ES', 43.62063000, -7.99959000, '2019-10-05 22:45:41', '2022-08-28 13:37:17', 1, 'Q1646215'),
(33582, 'Cerdà', 1175, 'V', 207, 'ES', 38.98333000, -0.56667000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1646215'),
(33583, 'Cereceda de la Sierra', 1147, 'SA', 207, 'ES', 40.56627000, -6.09140000, '2019-10-05 22:45:41', '2022-08-29 11:44:51', 1, 'Q1606686'),
(33584, 'Cerecinos de Campos', 1161, 'ZA', 207, 'ES', 41.90017000, -5.48605000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q1769002'),
(33585, 'Cerecinos del Carrizal', 1161, 'ZA', 207, 'ES', 41.68384000, -5.65303000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q1652193'),
(33586, 'Cerezal de Peñahorcada', 1147, 'SA', 207, 'ES', 41.13071000, -6.65357000, '2019-10-05 22:45:41', '2022-08-29 11:44:51', 1, 'Q1637672'),
(33587, 'Cerezo', 1190, 'CC', 207, 'ES', 40.23678000, -6.22764000, '2019-10-05 22:45:41', '2022-08-28 18:12:22', 1, 'Q1637672'),
(33588, 'Cerezo de Abajo', 1192, 'SG', 207, 'ES', 41.21817000, -3.59126000, '2019-10-05 22:45:41', '2022-08-29 11:50:42', 1, 'Q1637672'),
(33589, 'Cerezo de Arriba', 1192, 'SG', 207, 'ES', 41.23890000, -3.55846000, '2019-10-05 22:45:41', '2022-08-29 11:50:42', 1, 'Q1905563'),
(33590, 'Cernadilla', 1161, 'ZA', 207, 'ES', 42.02083000, -6.41701000, '2019-10-05 22:45:41', '2022-08-29 11:48:03', 1, 'Q1769126'),
(33591, 'Cerralbo', 1147, 'SA', 207, 'ES', 40.97347000, -6.58710000, '2019-10-05 22:45:41', '2022-08-29 11:44:51', 1, 'Q1633616'),
(33592, 'Cerratón de Juarros', 1146, 'BU', 207, 'ES', 42.42227000, -3.37347000, '2019-10-05 22:45:41', '2022-08-29 11:24:19', 1, 'Q1644231'),
(33593, 'Cervatos de la Cueza', 1157, 'P', 207, 'ES', 42.29054000, -4.76947000, '2019-10-05 22:45:41', '2022-08-29 11:45:44', 1, 'Q1915073'),
(33594, 'Cervelló', 5102, 'B', 207, 'ES', 41.39587000, 1.95917000, '2019-10-05 22:45:41', '2022-08-29 10:50:00', 1, 'Q1915073'),
(33595, 'Cervera', 5104, 'L', 207, 'ES', 41.67003000, 1.27210000, '2019-10-05 22:45:41', '2022-08-29 10:55:25', 1, 'Q1915073'),
(33596, 'Cervera de Buitrago', 1158, 'M', 207, 'ES', 40.91988000, -3.52702000, '2019-10-05 22:45:41', '2022-08-29 12:04:40', 1, 'Q1772231'),
(33597, 'Cervera de Pisuerga', 1157, 'P', 207, 'ES', 42.86676000, -4.49796000, '2019-10-05 22:45:41', '2022-08-29 11:45:44', 1, 'Q1771120'),
(33598, 'Cervera de la Cañada', 5113, 'Z', 207, 'ES', 41.43301000, -1.73568000, '2019-10-05 22:45:41', '2022-08-29 11:42:54', 1, 'Q24019996'),
(33599, 'Cervera de los Montes', 1205, 'TO', 207, 'ES', 40.05182000, -4.81072000, '2019-10-05 22:45:41', '2022-08-29 11:08:29', 1, 'Q1632182'),
(33600, 'Cervera del Llano', 5106, 'CU', 207, 'ES', 39.78312000, -2.42019000, '2019-10-05 22:45:41', '2022-08-29 11:05:01', 1, 'Q1902690'),
(33601, 'Cervera del Maestre', 5110, 'CS', 207, 'ES', 40.45366000, 0.27659000, '2019-10-05 22:45:41', '2022-08-29 11:26:42', 1, 'Q1902690'),
(33602, 'Cervera del Río Alhama', 1171, 'LO', 207, 'ES', 42.00517000, -1.95531000, '2019-10-05 22:45:41', '2022-08-29 12:05:09', 1, 'Q1626960'),
(33603, 'Cerveruela', 5113, 'Z', 207, 'ES', 41.21567000, -1.21525000, '2019-10-05 22:45:41', '2022-08-29 11:42:54', 1, 'Q1639553'),
(33604, 'Cervillego de la Cruz', 1183, 'VA', 207, 'ES', 41.18783000, -4.94966000, '2019-10-05 22:45:41', '2022-08-29 11:48:44', 1, 'Q1907141'),
(33605, 'Cervià de Ter', 5103, 'GI', 207, 'ES', 42.06650000, 2.90743000, '2019-10-05 22:45:41', '2022-08-29 10:53:16', 1, 'Q13428'),
(33606, 'Cervo', 5090, 'LU', 207, 'ES', 43.67019000, -7.41013000, '2019-10-05 22:45:41', '2022-08-28 17:49:36', 1, 'Q13428'),
(33607, 'Cesuras', 5089, 'C', 207, 'ES', 43.17282000, -8.20061000, '2019-10-05 22:45:41', '2022-08-28 13:37:17', 1, 'Q772776'),
(33608, 'Cetina', 5113, 'Z', 207, 'ES', 41.29208000, -1.96281000, '2019-10-05 22:45:41', '2022-08-29 11:42:54', 1, 'Q24019994'),
(33610, 'Ceuti', 1176, 'MU', 207, 'ES', 38.07859000, -1.27467000, '2019-10-05 22:45:41', '2022-08-29 12:05:49', 1, 'Q23986853'),
(33611, 'Cevico Navero', 1157, 'P', 207, 'ES', 41.86112000, -4.18498000, '2019-10-05 22:45:41', '2022-08-29 11:45:44', 1, 'Q1919414'),
(33612, 'Cevico de la Torre', 1157, 'P', 207, 'ES', 41.85113000, -4.40973000, '2019-10-05 22:45:41', '2022-08-29 11:45:44', 1, 'Q1923913'),
(33613, 'Chalamera', 1177, 'HU', 207, 'ES', 41.66567000, 0.16299000, '2019-10-05 22:45:41', '2022-08-29 12:06:20', 1, 'Q732971'),
(33614, 'Chamartín', 1189, 'AV', 207, 'ES', 40.70267000, -4.95769000, '2019-10-05 22:45:41', '2022-08-29 11:49:56', 1, 'Q732971'),
(33616, 'Chamberí', 1158, 'M', 207, 'ES', 40.43404000, -3.70379000, '2019-10-05 22:45:41', '2022-08-29 12:04:40', 1, 'Q1763370'),
(33617, 'Chantada', 5090, 'LU', 207, 'ES', 42.60876000, -7.77115000, '2019-10-05 22:45:41', '2022-08-28 17:49:36', 1, 'Q1763370'),
(33618, 'Chapinería', 1158, 'M', 207, 'ES', 40.37891000, -4.21009000, '2019-10-05 22:45:41', '2022-08-29 12:04:40', 1, 'Q1772178'),
(33619, 'Chauchina', 5098, 'GR', 207, 'ES', 37.19977000, -3.77157000, '2019-10-05 22:45:41', '2022-08-28 18:52:57', 1, 'Q555943'),
(33620, 'Chañe', 1192, 'SG', 207, 'ES', 41.33834000, -4.42764000, '2019-10-05 22:45:41', '2022-08-29 11:50:42', 1, 'Q1939096'),
(33621, 'Checa', 5107, 'GU', 207, 'ES', 40.58614000, -1.79056000, '2019-10-05 22:45:41', '2022-08-29 11:06:45', 1, 'Q24011217'),
(33622, 'Cheles', 5092, 'BA', 207, 'ES', 38.51225000, -7.28177000, '2019-10-05 22:45:41', '2022-08-28 18:09:23', 1, 'Q844205'),
(33623, 'Chella', 1175, 'V', 207, 'ES', 39.04203000, -0.65916000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q844205'),
(33624, 'Chelva', 1175, 'V', 207, 'ES', 39.74930000, -0.99684000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1771138'),
(33625, 'Chequilla', 5107, 'GU', 207, 'ES', 40.60600000, -1.82787000, '2019-10-05 22:45:41', '2022-08-29 11:06:45', 1, 'Q1655358'),
(33626, 'Chera', 1175, 'V', 207, 'ES', 39.60000000, -0.96667000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1655358'),
(33627, 'Chercos', 5095, 'AL', 207, 'ES', 37.25450000, -2.26657000, '2019-10-05 22:45:41', '2022-08-28 18:41:41', 1, 'Q1358286'),
(33628, 'Chert/Xert', 5110, 'CS', 207, 'ES', 40.51944000, 0.15831000, '2019-10-05 22:45:41', '2022-08-29 11:26:42', 1, 'Q1358286'),
(33629, 'Cheste', 1175, 'V', 207, 'ES', 39.48333000, -0.68333000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1551943'),
(33630, 'Chiclana de Segura', 5100, 'J', 207, 'ES', 38.31187000, -3.04219000, '2019-10-05 22:45:41', '2022-08-28 19:04:30', 1, 'Q1551943'),
(33631, 'Chiclana de la Frontera', 5096, 'CA', 207, 'ES', 36.41976000, -6.14367000, '2019-10-05 22:45:41', '2022-08-28 18:44:29', 1, 'Q24011946'),
(33632, 'Chilches', 1175, 'V', 207, 'ES', 39.78238000, -0.18742000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q23986898'),
(33633, 'Chillarón de Cuenca', 5106, 'CU', 207, 'ES', 40.10539000, -2.22181000, '2019-10-05 22:45:41', '2022-08-29 11:05:00', 1, 'Q23986898'),
(33634, 'Chillarón del Rey', 5107, 'GU', 207, 'ES', 40.59818000, -2.69126000, '2019-10-05 22:45:41', '2022-08-29 11:06:45', 1, 'Q23986898'),
(33635, 'Chilluévar', 5100, 'J', 207, 'ES', 38.00088000, -3.03240000, '2019-10-05 22:45:41', '2022-08-28 19:04:30', 1, 'Q23986898'),
(33636, 'Chiloeches', 5107, 'GU', 207, 'ES', 40.57038000, -3.16003000, '2019-10-05 22:45:41', '2022-08-29 11:06:45', 1, 'Q1655103'),
(33637, 'Chimeneas', 5098, 'GR', 207, 'ES', 37.13125000, -3.82130000, '2019-10-05 22:45:41', '2022-08-28 18:52:57', 1, 'Q557953'),
(33638, 'Chimillas', 1177, 'HU', 207, 'ES', 42.17033000, -0.45168000, '2019-10-05 22:45:41', '2022-08-29 12:06:20', 1, 'Q383797'),
(33640, 'Chinchón', 1158, 'M', 207, 'ES', 40.14020000, -3.42267000, '2019-10-05 22:45:41', '2022-08-29 12:04:40', 1, 'Q915395'),
(33641, 'Chipiona', 5096, 'CA', 207, 'ES', 36.73663000, -6.43703000, '2019-10-05 22:45:41', '2022-08-28 18:44:29', 1, 'Q741749'),
(33642, 'Chiprana', 5113, 'Z', 207, 'ES', 41.26317000, -0.12741000, '2019-10-05 22:45:41', '2022-08-29 11:42:54', 1, 'Q24019982'),
(33643, 'Chirivel', 5095, 'AL', 207, 'ES', 37.59527000, -2.26844000, '2019-10-05 22:45:41', '2022-08-28 18:41:41', 1, 'Q24019982'),
(33644, 'Chiva', 1175, 'V', 207, 'ES', 39.46667000, -0.71667000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q24019982'),
(33645, 'Chodes', 5113, 'Z', 207, 'ES', 41.48696000, -1.48012000, '2019-10-05 22:45:41', '2022-08-29 11:42:54', 1, 'Q24019982'),
(33646, 'Chozas de Abajo', 1200, 'LE', 207, 'ES', 42.50657000, -5.68656000, '2019-10-05 22:45:41', '2019-10-05 22:45:41', 1, 'Q141270'),
(33647, 'Chozas de Canales', 1205, 'TO', 207, 'ES', 40.10014000, -4.04333000, '2019-10-05 22:45:41', '2022-08-29 11:08:29', 1, 'Q1629996'),
(33648, 'Chucena', 5099, 'H', 207, 'ES', 37.36305000, -6.39304000, '2019-10-05 22:45:41', '2022-08-28 19:00:43', 1, 'Q1615116'),
(33649, 'Chueca', 1205, 'TO', 207, 'ES', 39.73191000, -3.94365000, '2019-10-05 22:45:41', '2022-08-29 11:08:29', 1, 'Q1615116'),
(33650, 'Chulilla', 1175, 'V', 207, 'ES', 39.65000000, -0.88333000, '2019-10-05 22:45:41', '2022-08-29 12:05:40', 1, 'Q1998550'),
(33651, 'Chumillas', 5106, 'CU', 207, 'ES', 39.76667000, -2.03333000, '2019-10-05 22:45:41', '2022-08-29 11:05:01', 1, 'Q1903475'),
(33652, 'Churriana de la Vega', 5098, 'GR', 207, 'ES', 37.14499000, -3.64617000, '2019-10-05 22:45:41', '2022-08-28 18:52:57', 1, 'Q558134'),
(33653, 'Chía', 1177, 'HU', 207, 'ES', 42.52080000, 0.46563000, '2019-10-05 22:45:41', '2022-08-29 12:06:20', 1, 'Q987432'),
(33654, 'Chóvar', 5110, 'CS', 207, 'ES', 39.85000000, -0.31667000, '2019-10-05 22:45:41', '2022-08-29 11:26:42', 1, 'Q693679'),
(33655, 'Ciadoncha', 1146, 'BU', 207, 'ES', 42.15814000, -3.93235000, '2019-10-05 22:45:41', '2022-08-29 11:24:19', 1, 'Q1631235'),
(33656, 'Cidamón', 1171, 'LO', 207, 'ES', 42.49518000, -2.87834000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q1647242'),
(33657, 'Cidones', 1208, 'SO', 207, 'ES', 41.81444000, -2.63991000, '2019-10-05 22:45:42', '2022-08-29 11:51:23', 1, 'Q829955'),
(33658, 'Ciempozuelos', 1158, 'M', 207, 'ES', 40.15913000, -3.62103000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1751930'),
(33659, 'Cieza', 1176, 'MU', 207, 'ES', 38.23998000, -1.41987000, '2019-10-05 22:45:42', '2022-08-29 12:05:49', 1, 'Q1751930'),
(33660, 'Cifuentes', 5107, 'GU', 207, 'ES', 40.78622000, -2.62245000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q986570'),
(33661, 'Cigales', 1183, 'VA', 207, 'ES', 41.75780000, -4.69848000, '2019-10-05 22:45:42', '2022-08-29 11:48:44', 1, 'Q986570'),
(33662, 'Cigudosa', 1208, 'SO', 207, 'ES', 41.93333000, -2.05000000, '2019-10-05 22:45:42', '2022-08-29 11:51:23', 1, 'Q830595'),
(33663, 'Ciguñuela', 1183, 'VA', 207, 'ES', 41.64064000, -4.85688000, '2019-10-05 22:45:42', '2022-08-29 11:48:44', 1, 'Q1921076'),
(33664, 'Cihuela', 1208, 'SO', 207, 'ES', 41.40721000, -1.99989000, '2019-10-05 22:45:42', '2022-08-29 11:51:23', 1, 'Q841496'),
(33665, 'Cihuri', 1171, 'LO', 207, 'ES', 42.56499000, -2.92287000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q1647698'),
(33666, 'Cijuela', 5098, 'GR', 207, 'ES', 37.19800000, -3.81174000, '2019-10-05 22:45:42', '2022-08-28 18:52:57', 1, 'Q548578'),
(33667, 'Cilleros', 1190, 'CC', 207, 'ES', 40.11486000, -6.79256000, '2019-10-05 22:45:42', '2022-08-28 18:12:22', 1, 'Q1613475'),
(33668, 'Cilleros de la Bastida', 1147, 'SA', 207, 'ES', 40.57614000, -6.06094000, '2019-10-05 22:45:42', '2022-08-29 11:44:51', 1, 'Q1640895'),
(33669, 'Cilleruelo de Abajo', 1146, 'BU', 207, 'ES', 41.88405000, -3.79723000, '2019-10-05 22:45:42', '2022-08-29 11:24:19', 1, 'Q1643676'),
(33670, 'Cilleruelo de Arriba', 1146, 'BU', 207, 'ES', 41.90487000, -3.66102000, '2019-10-05 22:45:42', '2022-08-29 11:24:19', 1, 'Q1633280'),
(33671, 'Cilleruelo de San Mamés', 1192, 'SG', 207, 'ES', 41.43220000, -3.56614000, '2019-10-05 22:45:42', '2022-08-29 11:50:42', 1, 'Q1917639'),
(33672, 'Cillán', 1189, 'AV', 207, 'ES', 40.70675000, -4.98135000, '2019-10-05 22:45:42', '2022-08-29 11:49:56', 1, 'Q1633515'),
(33673, 'Cimanes de la Vega', 1200, 'LE', 207, 'ES', 42.11646000, -5.59851000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q137123'),
(33674, 'Cimanes del Tejar', 1200, 'LE', 207, 'ES', 42.61789000, -5.80506000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q137123'),
(33675, 'Cimballa', 5113, 'Z', 207, 'ES', 41.10118000, -1.77352000, '2019-10-05 22:45:42', '2022-08-29 11:42:54', 1, 'Q1650672'),
(33676, 'Cinco Olivas', 5113, 'Z', 207, 'ES', 41.33966000, -0.37116000, '2019-10-05 22:45:42', '2022-08-29 11:42:54', 1, 'Q936125'),
(33677, 'Cincovillas', 5107, 'GU', 207, 'ES', 41.20501000, -2.81834000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q936125'),
(33678, 'Cinctorres', 5110, 'CS', 207, 'ES', 40.58333000, -0.21667000, '2019-10-05 22:45:42', '2022-08-29 11:26:42', 1, 'Q1646167'),
(33679, 'Cintruénigo', 1204, 'NA', 207, 'ES', 42.07937000, -1.80458000, '2019-10-05 22:45:42', '2022-08-29 12:06:07', 1, 'Q1637322'),
(33680, 'Cipérez', 1147, 'SA', 207, 'ES', 40.96210000, -6.26552000, '2019-10-05 22:45:42', '2022-08-29 11:44:51', 1, 'Q1640795'),
(33681, 'Cirat', 5110, 'CS', 207, 'ES', 40.05000000, -0.45000000, '2019-10-05 22:45:42', '2022-08-29 11:26:42', 1, 'Q1769924'),
(33682, 'Cirauqui', 1204, 'NA', 207, 'ES', 42.67596000, -1.89115000, '2019-10-05 22:45:42', '2022-08-29 12:06:07', 1, 'Q206308'),
(33683, 'Ciria', 1208, 'SO', 207, 'ES', 41.61831000, -1.96578000, '2019-10-05 22:45:42', '2022-08-29 11:51:23', 1, 'Q24015367'),
(33684, 'Ciriza', 1204, 'NA', 207, 'ES', 42.79019000, -1.82822000, '2019-10-05 22:45:42', '2022-08-29 12:06:07', 1, 'Q1641813'),
(33685, 'Ciruelas', 5107, 'GU', 207, 'ES', 40.75021000, -3.08580000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q1654945'),
(33686, 'Ciruelos', 1205, 'TO', 207, 'ES', 39.93881000, -3.61383000, '2019-10-05 22:45:42', '2022-08-29 11:08:29', 1, 'Q1654945'),
(33687, 'Ciruelos de Cervera', 1146, 'BU', 207, 'ES', 41.90562000, -3.53015000, '2019-10-05 22:45:42', '2022-08-29 11:24:19', 1, 'Q1643918'),
(33688, 'Cirueña', 1171, 'LO', 207, 'ES', 42.41214000, -2.89593000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q986104'),
(33689, 'Cirujales del Río', 1208, 'SO', 207, 'ES', 41.86691000, -2.32549000, '2019-10-05 22:45:42', '2022-08-29 11:51:23', 1, 'Q835700'),
(33690, 'Cisla', 1189, 'AV', 207, 'ES', 40.96662000, -5.01405000, '2019-10-05 22:45:42', '2022-08-29 11:49:56', 1, 'Q1618190'),
(33691, 'Cisneros', 1157, 'P', 207, 'ES', 42.22046000, -4.85807000, '2019-10-05 22:45:42', '2022-08-29 11:45:44', 1, 'Q62415411'),
(33692, 'Cistella', 5103, 'GI', 207, 'ES', 42.26870000, 2.84780000, '2019-10-05 22:45:42', '2022-08-29 10:53:16', 1, 'Q11291'),
(33693, 'Cistierna', 1200, 'LE', 207, 'ES', 42.80344000, -5.12664000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q11291'),
(33694, 'Cistérniga', 1183, 'VA', 207, 'ES', 41.61294000, -4.68697000, '2019-10-05 22:45:42', '2022-08-29 11:48:44', 1, 'Q1768521'),
(33695, 'City Center', 1158, 'M', 207, 'ES', 40.41831000, -3.70275000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1763376'),
(33696, 'Ciudad Lineal', 1158, 'M', 207, 'ES', 40.44505000, -3.65132000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1763694'),
(33697, 'Ciudad Real', 5105, 'CR', 207, 'ES', 38.98626000, -3.92907000, '2019-10-05 22:45:42', '2022-08-29 11:03:24', 1, 'Q56241563'),
(33698, 'Ciudad Rodrigo', 1147, 'SA', 207, 'ES', 40.60000000, -6.53333000, '2019-10-05 22:45:42', '2022-08-29 11:44:51', 1, 'Q820476'),
(33699, 'Ciutadella', 1174, 'PM', 207, 'ES', 40.00112000, 3.84144000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q820476'),
(33700, 'Ciutadilla', 5104, 'L', 207, 'ES', 41.56113000, 1.13935000, '2019-10-05 22:45:42', '2022-08-29 10:55:25', 1, 'Q1769563'),
(33703, 'Cizur Mayor', 1204, 'NA', 207, 'ES', 42.78795000, -1.69065000, '2019-10-05 22:45:42', '2022-08-29 12:06:07', 1, 'Q1769563'),
(33704, 'Clarés de Ribota', 5113, 'Z', 207, 'ES', 41.52957000, -1.83773000, '2019-10-05 22:45:42', '2022-08-29 11:42:54', 1, 'Q1650641'),
(33705, 'Clavijo', 1171, 'LO', 207, 'ES', 42.34897000, -2.42666000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q767222'),
(33706, 'Cobatillas', 1176, 'MU', 207, 'ES', 38.05545000, -1.07683000, '2019-10-05 22:45:42', '2022-08-29 12:05:49', 1, 'Q767222'),
(33707, 'Cobeja', 1205, 'TO', 207, 'ES', 40.02076000, -3.85599000, '2019-10-05 22:45:42', '2022-08-29 11:08:29', 1, 'Q1631925'),
(33708, 'Cobeta', 5107, 'GU', 207, 'ES', 40.86560000, -2.14211000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q1655353'),
(33709, 'Cobeña', 1158, 'M', 207, 'ES', 40.56667000, -3.50000000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1655353'),
(33710, 'Cobisa', 1205, 'TO', 207, 'ES', 39.80425000, -4.02528000, '2019-10-05 22:45:42', '2022-08-29 11:08:29', 1, 'Q1628606'),
(33711, 'Cobos de Cerrato', 1157, 'P', 207, 'ES', 42.02808000, -4.00251000, '2019-10-05 22:45:42', '2022-08-29 11:45:44', 1, 'Q1923975'),
(33712, 'Cobos de Fuentidueña', 1192, 'SG', 207, 'ES', 41.38265000, -3.92723000, '2019-10-05 22:45:42', '2022-08-29 11:50:42', 1, 'Q1916964'),
(33713, 'Cobreros', 1161, 'ZA', 207, 'ES', 42.07323000, -6.70053000, '2019-10-05 22:45:42', '2022-08-29 11:48:03', 1, 'Q1768959'),
(33714, 'Coca', 1192, 'SG', 207, 'ES', 41.21767000, -4.52145000, '2019-10-05 22:45:42', '2022-08-29 11:50:42', 1, 'Q24014547'),
(33715, 'Coca de Alba', 1147, 'SA', 207, 'ES', 40.87833000, -5.36537000, '2019-10-05 22:45:42', '2022-08-29 11:44:51', 1, 'Q1651744'),
(33716, 'Cocentaina', 5108, 'A', 207, 'ES', 38.73975000, -0.43976000, '2019-10-05 22:45:42', '2022-08-29 11:15:47', 1, 'Q24008524'),
(33717, 'Codorniz', 1192, 'SG', 207, 'ES', 41.06745000, -4.60021000, '2019-10-05 22:45:42', '2022-08-29 11:50:42', 1, 'Q2249235'),
(33718, 'Codos', 5113, 'Z', 207, 'ES', 41.29266000, -1.37425000, '2019-10-05 22:45:42', '2022-08-29 11:42:54', 1, 'Q1768746'),
(33719, 'Cofrentes', 1175, 'V', 207, 'ES', 39.22926000, -1.06061000, '2019-10-05 22:45:42', '2022-08-29 12:05:40', 1, 'Q115313'),
(33720, 'Cogeces del Monte', 1183, 'VA', 207, 'ES', 41.51030000, -4.31721000, '2019-10-05 22:45:42', '2022-08-29 11:48:44', 1, 'Q1907192'),
(33721, 'Cogollor', 5107, 'GU', 207, 'ES', 40.84818000, -2.74425000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q1655300'),
(33722, 'Cogollos', 1146, 'BU', 207, 'ES', 42.19982000, -3.70005000, '2019-10-05 22:45:42', '2022-08-29 11:24:19', 1, 'Q1644225'),
(33723, 'Cogollos de Guadix', 5098, 'GR', 207, 'ES', 37.22521000, -3.16094000, '2019-10-05 22:45:42', '2022-08-28 18:52:57', 1, 'Q558105'),
(33724, 'Cogolludo', 5107, 'GU', 207, 'ES', 40.94691000, -3.08930000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q986532'),
(33725, 'Coirós', 5089, 'C', 207, 'ES', 43.25000000, -8.16667000, '2019-10-05 22:45:42', '2022-08-28 13:37:17', 1, 'Q527259'),
(33726, 'Colera', 5103, 'GI', 207, 'ES', 42.40394000, 3.15153000, '2019-10-05 22:45:42', '2022-08-29 10:53:16', 1, 'Q11296'),
(33727, 'Coles', 5091, 'OR', 207, 'ES', 42.40000000, -7.83333000, '2019-10-05 22:45:42', '2022-08-28 17:53:26', 1, 'Q1605474'),
(33728, 'Colindres', 1170, 'S', 207, 'ES', 43.39667000, -3.45361000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q1443892'),
(33729, 'Coll d\'en Rabassa', 1174, 'PM', 207, 'ES', 39.55083000, 2.69468000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q1443892'),
(33730, 'Coll de Nargó', 5104, 'L', 207, 'ES', 42.17473000, 1.31694000, '2019-10-05 22:45:42', '2022-08-29 10:55:25', 1, 'Q1443892'),
(33731, 'Collado', 1190, 'CC', 207, 'ES', 40.05729000, -5.72052000, '2019-10-05 22:45:42', '2022-08-28 18:12:22', 1, 'Q1637534'),
(33732, 'Collado Hermoso', 1192, 'SG', 207, 'ES', 41.03869000, -3.91859000, '2019-10-05 22:45:42', '2022-08-29 11:50:42', 1, 'Q1939220'),
(33733, 'Collado Mediano', 1158, 'M', 207, 'ES', 40.69326000, -4.02280000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1751903'),
(33734, 'Collado de Contreras', 1189, 'AV', 207, 'ES', 40.88743000, -4.93140000, '2019-10-05 22:45:42', '2022-08-29 11:49:56', 1, 'Q680439'),
(33735, 'Collado del Mirón', 1189, 'AV', 207, 'ES', 40.55307000, -5.35398000, '2019-10-05 22:45:42', '2022-08-29 11:49:56', 1, 'Q1607852'),
(33736, 'Collado-Villalba', 1158, 'M', 207, 'ES', 40.63506000, -4.00486000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q735759'),
(33737, 'Collazos de Boedo', 1157, 'P', 207, 'ES', 42.62009000, -4.48294000, '2019-10-05 22:45:42', '2022-08-29 11:45:44', 1, 'Q1923956'),
(33738, 'Collbató', 5102, 'B', 207, 'ES', 41.57009000, 1.82712000, '2019-10-05 22:45:42', '2022-08-29 10:50:00', 1, 'Q1923956'),
(33739, 'Colldejou', 1203, 'T', 207, 'ES', 41.09970000, 0.88717000, '2019-10-05 22:45:42', '2022-08-29 10:57:32', 1, 'Q1923956'),
(33740, 'Collsuspina', 5102, 'B', 207, 'ES', 41.82580000, 2.17546000, '2019-10-05 22:45:42', '2022-08-29 10:50:00', 1, 'Q1923956'),
(33741, 'Colmenar', 5101, 'MA', 207, 'ES', 36.90519000, -4.33557000, '2019-10-05 22:45:42', '2022-08-28 19:06:52', 1, 'Q1923956'),
(33742, 'Colmenar Viejo', 1158, 'M', 207, 'ES', 40.65909000, -3.76762000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q24002162'),
(33743, 'Colmenar de Montemayor', 1147, 'SA', 207, 'ES', 40.39957000, -5.95618000, '2019-10-05 22:45:42', '2022-08-29 11:44:51', 1, 'Q1640815'),
(33744, 'Colmenar de Oreja', 1158, 'M', 207, 'ES', 40.10675000, -3.38547000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q686674'),
(33745, 'Colmenar del Arroyo', 1158, 'M', 207, 'ES', 40.41897000, -4.19845000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q686674'),
(33746, 'Colmenarejo', 1158, 'M', 207, 'ES', 40.56063000, -4.01713000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1646220'),
(33747, 'Colomera', 5098, 'GR', 207, 'ES', 37.37156000, -3.71393000, '2019-10-05 22:45:42', '2022-08-28 18:52:57', 1, 'Q557966'),
(33748, 'Colungo', 1177, 'HU', 207, 'ES', 42.17125000, 0.06812000, '2019-10-05 22:45:42', '2022-08-29 12:06:20', 1, 'Q1651311'),
(33749, 'Colònia de Sant Jordi', 1174, 'PM', 207, 'ES', 39.31810000, 2.99197000, '2019-10-05 22:45:42', '2020-05-01 17:23:15', 1, 'Q1651311'),
(33751, 'Comares', 5101, 'MA', 207, 'ES', 36.84929000, -4.24664000, '2019-10-05 22:45:42', '2022-08-28 19:06:52', 1, 'Q488895'),
(33753, 'Comillas', 1170, 'S', 207, 'ES', 43.38603000, -4.29162000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q24002177'),
(33754, 'Condemios de Abajo', 5107, 'GU', 207, 'ES', 41.21667000, -3.10000000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q1751969'),
(33755, 'Condemios de Arriba', 5107, 'GU', 207, 'ES', 41.21644000, -3.12543000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q1640904'),
(33756, 'Conesa', 1203, 'T', 207, 'ES', 41.51667000, 1.30000000, '2019-10-05 22:45:42', '2022-08-29 10:57:32', 1, 'Q1248512'),
(33757, 'Confrides', 5108, 'A', 207, 'ES', 38.68451000, -0.26890000, '2019-10-05 22:45:42', '2022-08-29 11:15:47', 1, 'Q1768778'),
(33758, 'Congosto', 1200, 'LE', 207, 'ES', 42.61755000, -6.52048000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q1768778'),
(33759, 'Congosto de Valdavia', 1157, 'P', 207, 'ES', 42.71584000, -4.63366000, '2019-10-05 22:45:42', '2022-08-29 11:45:44', 1, 'Q1923922'),
(33760, 'Congostrina', 5107, 'GU', 207, 'ES', 41.03719000, -2.98569000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q1653806'),
(33761, 'Conil de la Frontera', 5096, 'CA', 207, 'ES', 36.27719000, -6.08850000, '2019-10-05 22:45:42', '2022-08-28 18:44:29', 1, 'Q832984'),
(33762, 'Conquista', 5097, 'CO', 207, 'ES', 38.40000000, -4.50000000, '2019-10-05 22:45:42', '2022-08-28 18:49:38', 1, 'Q1616129'),
(33763, 'Conquista de la Sierra', 1190, 'CC', 207, 'ES', 39.35070000, -5.73464000, '2019-10-05 22:45:42', '2022-08-28 18:12:22', 1, 'Q920677'),
(33764, 'Consell', 1174, 'PM', 207, 'ES', 39.66861000, 2.81267000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q950889'),
(33765, 'Constantina', 1193, 'SE', 207, 'ES', 37.86667000, -5.61667000, '2019-10-05 22:45:42', '2022-08-28 19:08:49', 1, 'Q949381'),
(33766, 'Constantí', 1203, 'T', 207, 'ES', 41.15392000, 1.21262000, '2019-10-05 22:45:42', '2022-08-29 10:57:32', 1, 'Q949381'),
(33767, 'Constanzana', 1189, 'AV', 207, 'ES', 40.93821000, -4.87515000, '2019-10-05 22:45:42', '2022-08-29 11:49:56', 1, 'Q1618221'),
(33768, 'Consuegra', 1205, 'TO', 207, 'ES', 39.46246000, -3.60800000, '2019-10-05 22:45:42', '2022-08-29 11:08:29', 1, 'Q919046'),
(33769, 'Contamina', 5113, 'Z', 207, 'ES', 41.30535000, -1.91731000, '2019-10-05 22:45:42', '2022-08-29 11:42:54', 1, 'Q1641087'),
(33770, 'Contreras', 1146, 'BU', 207, 'ES', 42.02004000, -3.41084000, '2019-10-05 22:45:42', '2022-08-29 11:24:20', 1, 'Q1641087'),
(33771, 'Coomonte', 1161, 'ZA', 207, 'ES', 42.11546000, -5.81316000, '2019-10-05 22:45:42', '2022-08-29 11:48:03', 1, 'Q1765220'),
(33772, 'Copernal', 5107, 'GU', 207, 'ES', 40.86463000, -3.05396000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q1653762'),
(33773, 'Corbalán', 5111, 'TE', 207, 'ES', 40.40308000, -0.98525000, '2019-10-05 22:45:42', '2022-08-29 11:29:44', 1, 'Q1653320'),
(33774, 'Corbera', 1175, 'V', 207, 'ES', 39.15000000, -0.35000000, '2019-10-05 22:45:42', '2022-08-29 12:05:40', 1, 'Q1653320'),
(33775, 'Corbera de Llobregat', 5102, 'B', 207, 'ES', 41.41702000, 1.91970000, '2019-10-05 22:45:42', '2022-08-29 10:50:00', 1, 'Q1653320'),
(33776, 'Corbillos de los Oteros', 1200, 'LE', 207, 'ES', 42.40769000, -5.45963000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q1615350'),
(33777, 'Corbins', 5104, 'L', 207, 'ES', 41.68333000, 0.70000000, '2019-10-05 22:45:42', '2022-08-29 10:55:25', 1, 'Q1615350'),
(33778, 'Corcos', 1183, 'VA', 207, 'ES', 41.80946000, -4.69270000, '2019-10-05 22:45:42', '2022-08-29 11:48:44', 1, 'Q2249269'),
(33779, 'Corcubión', 5089, 'C', 207, 'ES', 42.94414000, -9.19260000, '2019-10-05 22:45:42', '2022-08-28 13:37:17', 1, 'Q581367'),
(33780, 'Cordobilla de Lácara', 5092, 'BA', 207, 'ES', 39.14763000, -6.43619000, '2019-10-05 22:45:42', '2022-08-28 18:09:23', 1, 'Q1404680'),
(33781, 'Cordovilla', 1147, 'SA', 207, 'ES', 40.95095000, -5.40725000, '2019-10-05 22:45:42', '2022-08-29 11:44:51', 1, 'Q1651775'),
(33782, 'Cordovilla la Real', 1157, 'P', 207, 'ES', 42.07961000, -4.25988000, '2019-10-05 22:45:42', '2022-08-29 11:45:44', 1, 'Q1923964'),
(33783, 'Cordovín', 1171, 'LO', 207, 'ES', 42.38557000, -2.81500000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q1768216'),
(33784, 'Corduente', 5107, 'GU', 207, 'ES', 40.84304000, -1.97841000, '2019-10-05 22:45:42', '2022-08-29 11:06:45', 1, 'Q24011219'),
(33785, 'Corella', 1204, 'NA', 207, 'ES', 42.11507000, -1.78563000, '2019-10-05 22:45:42', '2022-08-29 12:06:07', 1, 'Q550407'),
(33786, 'Corera', 1171, 'LO', 207, 'ES', 42.34344000, -2.22023000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q1647642'),
(33787, 'Coreses', 1161, 'ZA', 207, 'ES', 41.54794000, -5.62252000, '2019-10-05 22:45:42', '2022-08-29 11:48:03', 1, 'Q1646359'),
(33789, 'Coria', 1190, 'CC', 207, 'ES', 39.98406000, -6.53603000, '2019-10-05 22:45:42', '2022-08-28 18:12:22', 1, 'Q1646359'),
(33790, 'Coria del Río', 1193, 'SE', 207, 'ES', 37.28766000, -6.05410000, '2019-10-05 22:45:42', '2022-08-28 19:08:49', 1, 'Q1445412'),
(33791, 'Coripe', 1193, 'SE', 207, 'ES', 36.97335000, -5.44022000, '2019-10-05 22:45:42', '2022-08-28 19:08:49', 1, 'Q1605547'),
(33792, 'Coristanco', 5089, 'C', 207, 'ES', 43.20000000, -8.75000000, '2019-10-05 22:45:42', '2022-08-28 13:37:17', 1, 'Q1369028'),
(33793, 'Cornago', 1171, 'LO', 207, 'ES', 42.06444000, -2.09486000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q941048'),
(33794, 'Cornellà de Llobregat', 5102, 'B', 207, 'ES', 41.35000000, 2.08333000, '2019-10-05 22:45:42', '2022-08-29 10:50:00', 1, 'Q941048'),
(33795, 'Cornellà del Terri', 5103, 'GI', 207, 'ES', 42.08333000, 2.81667000, '2019-10-05 22:45:42', '2022-08-29 10:53:16', 1, 'Q13613'),
(33796, 'Corpa', 1158, 'M', 207, 'ES', 40.42327000, -3.26003000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1915174'),
(33797, 'Corporales', 1171, 'LO', 207, 'ES', 42.43208000, -2.99535000, '2019-10-05 22:45:42', '2022-08-29 12:05:09', 1, 'Q1915174'),
(33798, 'Corral de Almaguer', 1205, 'TO', 207, 'ES', 39.75987000, -3.16452000, '2019-10-05 22:45:42', '2022-08-29 11:08:29', 1, 'Q598148'),
(33799, 'Corral de Ayllón', 1192, 'SG', 207, 'ES', 41.39081000, -3.45856000, '2019-10-05 22:45:42', '2022-08-29 11:50:42', 1, 'Q1919723'),
(33800, 'Corral de Calatrava', 5105, 'CR', 207, 'ES', 38.85793000, -4.08140000, '2019-10-05 22:45:42', '2022-08-29 11:03:24', 1, 'Q1644258'),
(33802, 'Corralejo', 1185, 'GC', 207, 'ES', 28.73079000, -13.86749000, '2019-10-05 22:45:42', '2022-08-29 12:06:32', 1, 'Q187978'),
(33803, 'Corrales', 1200, 'LE', 207, 'ES', 41.35800000, -5.72479000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q1990609'),
(33804, 'Corrales de Duero', 1183, 'VA', 207, 'ES', 41.67205000, -4.04817000, '2019-10-05 22:45:42', '2022-08-29 11:48:44', 1, 'Q1907244'),
(33805, 'Corte de Peleas', 5092, 'BA', 207, 'ES', 38.72631000, -6.67035000, '2019-10-05 22:45:42', '2022-08-28 18:09:23', 1, 'Q1372541'),
(33806, 'Corteconcepción', 5099, 'H', 207, 'ES', 37.90000000, -6.50000000, '2019-10-05 22:45:42', '2022-08-28 19:00:43', 1, 'Q1635886'),
(33807, 'Cortelazor', 5099, 'H', 207, 'ES', 37.93639000, -6.62462000, '2019-10-05 22:45:42', '2022-08-28 19:00:43', 1, 'Q957544'),
(33808, 'Cortes de Aragón', 5111, 'TE', 207, 'ES', 40.97366000, -0.83575000, '2019-10-05 22:45:42', '2022-08-29 11:29:44', 1, 'Q957544'),
(33809, 'Cortes de Arenoso', 5110, 'CS', 207, 'ES', 40.18812000, -0.54195000, '2019-10-05 22:45:42', '2022-08-29 11:26:42', 1, 'Q957544'),
(33810, 'Cortes de Baza', 5098, 'GR', 207, 'ES', 37.65514000, -2.77167000, '2019-10-05 22:45:42', '2022-08-28 18:52:57', 1, 'Q557947'),
(33811, 'Cortes de Pallás', 1175, 'V', 207, 'ES', 39.25000000, -0.93333000, '2019-10-05 22:45:42', '2022-08-29 12:05:40', 1, 'Q1998567'),
(33812, 'Cortes de la Frontera', 5101, 'MA', 207, 'ES', 36.61710000, -5.34266000, '2019-10-05 22:45:42', '2022-08-28 19:06:53', 1, 'Q1453000'),
(33813, 'Corullón', 1200, 'LE', 207, 'ES', 42.57896000, -6.81925000, '2019-10-05 22:45:42', '2020-05-01 17:23:16', 1, 'Q1453000'),
(33814, 'Coruña del Conde', 1146, 'BU', 207, 'ES', 41.76566000, -3.39059000, '2019-10-05 22:45:42', '2022-08-29 11:24:20', 1, 'Q1613822'),
(33815, 'Cosa', 5111, 'TE', 207, 'ES', 40.83392000, -1.13650000, '2019-10-05 22:45:42', '2022-08-29 11:29:44', 1, 'Q1613822'),
(33816, 'Coscurita', 1208, 'SO', 207, 'ES', 41.43483000, -2.47571000, '2019-10-05 22:45:42', '2022-08-29 11:51:23', 1, 'Q830303'),
(33817, 'Coslada', 1158, 'M', 207, 'ES', 40.42378000, -3.56129000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q164197'),
(33818, 'Costa Calma', 1185, 'GC', 207, 'ES', 28.16155000, -14.22691000, '2019-10-05 22:45:42', '2022-08-29 12:06:32', 1, 'Q1136332'),
(33819, 'Costa Teguise', 1185, 'GC', 207, 'ES', 28.99838000, -13.49911000, '2019-10-05 22:45:42', '2022-08-29 12:06:32', 1, 'Q991489'),
(33821, 'Costitx', 1174, 'PM', 207, 'ES', 39.65675000, 2.94953000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q851224'),
(33822, 'Costur', 5110, 'CS', 207, 'ES', 40.11971000, -0.17385000, '2019-10-05 22:45:42', '2022-08-29 11:26:42', 1, 'Q1645737'),
(33823, 'Cosuenda', 5113, 'Z', 207, 'ES', 41.36559000, -1.29867000, '2019-10-05 22:45:42', '2022-08-29 11:42:54', 1, 'Q599787'),
(33824, 'Cotes', 1175, 'V', 207, 'ES', 39.07010000, -0.57449000, '2019-10-05 22:45:42', '2022-08-29 12:05:40', 1, 'Q599787'),
(33826, 'Covaleda', 1208, 'SO', 207, 'ES', 41.93493000, -2.88325000, '2019-10-05 22:45:42', '2022-08-29 11:51:23', 1, 'Q829758'),
(33827, 'Covarrubias', 1146, 'BU', 207, 'ES', 42.05927000, -3.51956000, '2019-10-05 22:45:42', '2022-08-29 11:24:20', 1, 'Q24001741'),
(33828, 'Covelo', 1167, 'PO', 207, 'ES', 42.23333000, -8.35000000, '2019-10-05 22:45:42', '2022-08-28 17:57:54', 1, 'Q305392'),
(33829, 'Cox', 5108, 'A', 207, 'ES', 38.14164000, -0.88736000, '2019-10-05 22:45:42', '2022-08-29 11:15:47', 1, 'Q305392'),
(33830, 'Cozuelos de Fuentidueña', 1192, 'SG', 207, 'ES', 41.39086000, -4.09564000, '2019-10-05 22:45:42', '2022-08-29 11:50:43', 1, 'Q1939085'),
(33831, 'Coín', 5101, 'MA', 207, 'ES', 36.65947000, -4.75639000, '2019-10-05 22:45:42', '2022-08-28 19:06:52', 1, 'Q1110418'),
(33832, 'Creixell', 1203, 'T', 207, 'ES', 41.16618000, 1.44032000, '2019-10-05 22:45:42', '2022-08-29 10:57:32', 1, 'Q1020556'),
(33833, 'Crespià', 5103, 'GI', 207, 'ES', 42.18333000, 2.80000000, '2019-10-05 22:45:42', '2022-08-29 10:53:16', 1, 'Q24019203'),
(33834, 'Crespos', 1189, 'AV', 207, 'ES', 40.87128000, -4.97094000, '2019-10-05 22:45:42', '2022-08-29 11:49:56', 1, 'Q24019203'),
(33835, 'Crevillente', 1175, 'V', 207, 'ES', 38.24994000, -0.80975000, '2019-10-05 22:45:42', '2022-08-29 12:05:40', 1, 'Q23987103'),
(33836, 'Cristina', 5092, 'BA', 207, 'ES', 38.83745000, -6.09867000, '2019-10-05 22:45:42', '2022-08-28 18:09:23', 1, 'Q23987103'),
(33837, 'Cristóbal', 1147, 'SA', 207, 'ES', 40.46923000, -5.88967000, '2019-10-05 22:45:42', '2022-08-29 11:44:51', 1, 'Q1651917'),
(33838, 'Crivillén', 5111, 'TE', 207, 'ES', 40.88325000, -0.57666000, '2019-10-05 22:45:42', '2022-08-29 11:29:44', 1, 'Q1653447'),
(33839, 'Cruce de Arinaga', 1185, 'GC', 207, 'ES', 27.87656000, -15.42798000, '2019-10-05 22:45:42', '2022-08-29 12:06:32', 1, 'Q5792267'),
(33841, 'Crémenes', 1200, 'LE', 207, 'ES', 42.90357000, -5.14374000, '2019-10-05 22:45:42', '2020-05-01 17:23:16', 1, 'Q137713'),
(33842, 'Cuadros', 1200, 'LE', 207, 'ES', 42.71163000, -5.63828000, '2019-10-05 22:45:42', '2019-10-05 22:45:42', 1, 'Q137713'),
(33843, 'Cualedro', 5091, 'OR', 207, 'ES', 41.98897000, -7.59464000, '2019-10-05 22:45:42', '2022-08-28 17:53:26', 1, 'Q601546'),
(33844, 'Cuarte de Huerva', 5113, 'Z', 207, 'ES', 41.59466000, -0.93268000, '2019-10-05 22:45:42', '2022-08-29 11:42:54', 1, 'Q1629930'),
(33845, 'Cubas', 1158, 'M', 207, 'ES', 40.18900000, -3.83526000, '2019-10-05 22:45:42', '2022-08-29 12:04:40', 1, 'Q1629930'),
(33846, 'Cubel', 5113, 'Z', 207, 'ES', 41.09604000, -1.63729000, '2019-10-05 22:45:43', '2022-08-29 11:42:54', 1, 'Q617125'),
(33847, 'Cubelles', 5102, 'B', 207, 'ES', 41.20772000, 1.67267000, '2019-10-05 22:45:43', '2022-08-29 10:50:00', 1, 'Q617125'),
(33848, 'Cubells', 5104, 'L', 207, 'ES', 41.85062000, 0.95900000, '2019-10-05 22:45:43', '2022-08-29 10:55:25', 1, 'Q1769656'),
(33849, 'Cubilla', 1208, 'SO', 207, 'ES', 41.74905000, -2.93717000, '2019-10-05 22:45:43', '2022-08-29 11:51:23', 1, 'Q831396'),
(33850, 'Cubillas de Cerrato', 1157, 'P', 207, 'ES', 41.79844000, -4.46720000, '2019-10-05 22:45:43', '2022-08-29 11:45:44', 1, 'Q1907612'),
(33851, 'Cubillas de Rueda', 1200, 'LE', 207, 'ES', 42.65626000, -5.17528000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q1907612'),
(33852, 'Cubillas de Santa Marta', 1183, 'VA', 207, 'ES', 41.83358000, -4.61463000, '2019-10-05 22:45:43', '2022-08-29 11:48:44', 1, 'Q1922891'),
(33853, 'Cubillas de los Oteros', 1200, 'LE', 207, 'ES', 42.37199000, -5.50877000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q141169'),
(33854, 'Cubillo', 1192, 'SG', 207, 'ES', 41.12138000, -3.90878000, '2019-10-05 22:45:43', '2022-08-29 11:50:42', 1, 'Q1916994'),
(33855, 'Cubillo del Campo', 1146, 'BU', 207, 'ES', 42.16854000, -3.61025000, '2019-10-05 22:45:43', '2022-08-29 11:24:20', 1, 'Q1765331'),
(33856, 'Cubillos', 1161, 'ZA', 207, 'ES', 41.57436000, -5.73920000, '2019-10-05 22:45:43', '2022-08-29 11:48:03', 1, 'Q1657043'),
(33857, 'Cubillos del Sil', 1200, 'LE', 207, 'ES', 42.62519000, -6.56360000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q44786'),
(33858, 'Cubla', 5111, 'TE', 207, 'ES', 40.20978000, -1.07917000, '2019-10-05 22:45:43', '2022-08-29 11:29:44', 1, 'Q1644054'),
(33859, 'Cubo de Benavente', 1161, 'ZA', 207, 'ES', 42.12490000, -6.16342000, '2019-10-05 22:45:43', '2022-08-29 11:48:03', 1, 'Q1652992'),
(33860, 'Cubo de Bureba', 1146, 'BU', 207, 'ES', 42.64011000, -3.20626000, '2019-10-05 22:45:43', '2022-08-29 11:24:20', 1, 'Q1656771'),
(33861, 'Cubo de la Solana', 1208, 'SO', 207, 'ES', 41.60224000, -2.42179000, '2019-10-05 22:45:43', '2022-08-29 11:51:23', 1, 'Q835846'),
(33862, 'Cucalón', 5111, 'TE', 207, 'ES', 41.08608000, -1.21475000, '2019-10-05 22:45:43', '2022-08-29 11:29:44', 1, 'Q835846'),
(33863, 'Cuelgamures', 1161, 'ZA', 207, 'ES', 41.30738000, -5.65769000, '2019-10-05 22:45:43', '2022-08-29 11:48:03', 1, 'Q1765213'),
(33864, 'Cuenca', 5106, 'CU', 207, 'ES', 40.06667000, -2.13333000, '2019-10-05 22:45:43', '2022-08-29 11:05:01', 1, 'Q61963154'),
(33865, 'Cuenca de Campos', 1183, 'VA', 207, 'ES', 42.05927000, -5.05539000, '2019-10-05 22:45:43', '2022-08-29 11:48:44', 1, 'Q1919543'),
(33866, 'Cuerva', 1205, 'TO', 207, 'ES', 39.66374000, -4.21085000, '2019-10-05 22:45:43', '2022-08-29 11:08:29', 1, 'Q1629679'),
(33867, 'Cueva de Ágreda', 1208, 'SO', 207, 'ES', 41.76310000, -1.88818000, '2019-10-05 22:45:43', '2022-08-29 11:51:23', 1, 'Q835658'),
(33868, 'Cueva del Hierro', 5106, 'CU', 207, 'ES', 40.58258000, -2.03612000, '2019-10-05 22:45:43', '2022-08-29 11:05:01', 1, 'Q738589'),
(33869, 'Cuevas Bajas', 5101, 'MA', 207, 'ES', 37.23526000, -4.48714000, '2019-10-05 22:45:43', '2022-08-28 19:06:53', 1, 'Q1630277'),
(33870, 'Cuevas Labradas', 5111, 'TE', 207, 'ES', 40.45350000, -1.05008000, '2019-10-05 22:45:43', '2022-08-29 11:29:44', 1, 'Q24001819'),
(33871, 'Cuevas de Almudén', 5111, 'TE', 207, 'ES', 40.71342000, -0.82958000, '2019-10-05 22:45:43', '2022-08-29 11:29:44', 1, 'Q1652401'),
(33872, 'Cuevas de Provanco', 1192, 'SG', 207, 'ES', 41.54209000, -3.96203000, '2019-10-05 22:45:43', '2022-08-29 11:50:43', 1, 'Q1938540'),
(33873, 'Cuevas de San Clemente', 1146, 'BU', 207, 'ES', 42.13009000, -3.56840000, '2019-10-05 22:45:43', '2022-08-29 11:24:20', 1, 'Q1633302'),
(33874, 'Cuevas de San Marcos', 5101, 'MA', 207, 'ES', 37.26666000, -4.41432000, '2019-10-05 22:45:43', '2022-08-28 19:06:53', 1, 'Q1647751'),
(33875, 'Cuevas de Vinromá', 1175, 'V', 207, 'ES', 40.30976000, 0.12084000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q1646761'),
(33876, 'Cuevas del Almanzora', 5095, 'AL', 207, 'ES', 37.29678000, -1.88218000, '2019-10-05 22:45:43', '2022-08-28 18:41:41', 1, 'Q1048762'),
(33877, 'Cuevas del Becerro', 5101, 'MA', 207, 'ES', 36.87537000, -5.04488000, '2019-10-05 22:45:43', '2022-08-28 19:06:53', 1, 'Q1647740'),
(33878, 'Cuevas del Campo', 5098, 'GR', 207, 'ES', 37.60755000, -2.92938000, '2019-10-05 22:45:43', '2022-08-28 18:52:58', 1, 'Q1647740'),
(33879, 'Cuevas del Valle', 1189, 'AV', 207, 'ES', 40.29418000, -5.00938000, '2019-10-05 22:45:43', '2022-08-29 11:49:56', 1, 'Q1607095'),
(33880, 'Culla', 5110, 'CS', 207, 'ES', 40.33650000, -0.16569000, '2019-10-05 22:45:43', '2022-08-29 11:26:42', 1, 'Q1983206'),
(33881, 'Cullera', 1175, 'V', 207, 'ES', 39.16667000, -0.25000000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q986733'),
(33882, 'Culleredo', 5089, 'C', 207, 'ES', 43.28788000, -8.38858000, '2019-10-05 22:45:43', '2022-08-28 13:37:17', 1, 'Q1113450'),
(33883, 'Cumbres Mayores', 5099, 'H', 207, 'ES', 38.06194000, -6.64565000, '2019-10-05 22:45:43', '2022-08-28 19:00:43', 1, 'Q1615072'),
(33884, 'Cunit', 1203, 'T', 207, 'ES', 41.19829000, 1.63645000, '2019-10-05 22:45:43', '2022-08-29 10:57:32', 1, 'Q474489'),
(33885, 'Cuntis', 1167, 'PO', 207, 'ES', 42.63396000, -8.56256000, '2019-10-05 22:45:43', '2022-08-28 17:57:54', 1, 'Q1631860'),
(33886, 'Curiel de Duero', 1183, 'VA', 207, 'ES', 41.64327000, -4.10110000, '2019-10-05 22:45:43', '2022-08-29 11:48:44', 1, 'Q593991'),
(33887, 'Curtis', 5089, 'C', 207, 'ES', 43.12374000, -8.14818000, '2019-10-05 22:45:43', '2022-08-28 13:37:17', 1, 'Q1373939'),
(33888, 'Cuéllar', 1192, 'SG', 207, 'ES', 41.40155000, -4.31474000, '2019-10-05 22:45:43', '2022-08-29 11:50:42', 1, 'Q1373939'),
(33889, 'Càlig', 5110, 'CS', 207, 'ES', 40.46262000, 0.35521000, '2019-10-05 22:45:43', '2022-08-29 11:26:42', 1, 'Q1373939'),
(33890, 'Cànoves i Samalús', 5102, 'B', 207, 'ES', 41.68333000, 2.35000000, '2019-10-05 22:45:43', '2022-08-29 10:50:00', 1, 'Q1373939'),
(33891, 'Càrcer', 1175, 'V', 207, 'ES', 39.06667000, -0.56667000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q1904454'),
(33892, 'Cáceres', 1190, 'CC', 207, 'ES', 39.47649000, -6.37224000, '2019-10-05 22:45:43', '2022-08-28 18:12:22', 1, 'Q15678'),
(33893, 'Cádiar', 5098, 'GR', 207, 'ES', 36.94591000, -3.18020000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q554489'),
(33894, 'Cájar', 5098, 'GR', 207, 'ES', 37.13381000, -3.57274000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q554805'),
(33895, 'Cárcar', 1204, 'NA', 207, 'ES', 42.39314000, -1.97904000, '2019-10-05 22:45:43', '2022-08-29 12:06:07', 1, 'Q1648466'),
(33896, 'Cárdenas', 1171, 'LO', 207, 'ES', 42.37464000, -2.76726000, '2019-10-05 22:45:43', '2022-08-29 12:05:09', 1, 'Q1916210'),
(33897, 'Cármenes', 1200, 'LE', 207, 'ES', 42.95863000, -5.57346000, '2019-10-05 22:45:43', '2020-05-01 17:23:16', 1, 'Q1391449'),
(33898, 'Cártama', 5101, 'MA', 207, 'ES', 36.71068000, -4.63297000, '2019-10-05 22:45:43', '2022-08-28 19:06:52', 1, 'Q935266'),
(33899, 'Cáseda', 1204, 'NA', 207, 'ES', 42.52255000, -1.36636000, '2019-10-05 22:45:43', '2022-08-29 12:06:07', 1, 'Q1648512'),
(33900, 'Cástaras', 5098, 'GR', 207, 'ES', 36.93164000, -3.25406000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q555134'),
(33901, 'Cáñar', 5098, 'GR', 207, 'ES', 36.92684000, -3.42808000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q555114'),
(33902, 'Cóbdar', 5095, 'AL', 207, 'ES', 37.26166000, -2.21098000, '2019-10-05 22:45:43', '2022-08-28 18:41:41', 1, 'Q555114'),
(33903, 'Cómpeta', 5101, 'MA', 207, 'ES', 36.83352000, -3.97430000, '2019-10-05 22:45:43', '2022-08-28 19:06:52', 1, 'Q1623982'),
(33904, 'Córdoba', 5097, 'CO', 207, 'ES', 37.89155000, -4.77275000, '2019-10-05 22:45:43', '2022-08-28 18:49:38', 1, 'Q5818'),
(33905, 'Cózar', 5105, 'CR', 207, 'ES', 38.66219000, -3.07205000, '2019-10-05 22:45:43', '2022-08-29 11:03:25', 1, 'Q24011799'),
(33907, 'Cútar', 5101, 'MA', 207, 'ES', 36.83134000, -4.22739000, '2019-10-05 22:45:43', '2022-08-28 19:06:53', 1, 'Q385419'),
(33908, 'Daganzo de Arriba', 1158, 'M', 207, 'ES', 40.54293000, -3.45457000, '2019-10-05 22:45:43', '2022-08-29 12:04:40', 1, 'Q1772173'),
(33909, 'Daimiel', 5105, 'CR', 207, 'ES', 39.07004000, -3.61498000, '2019-10-05 22:45:43', '2022-08-29 11:03:25', 1, 'Q1631261'),
(33910, 'Daimús', 1175, 'V', 207, 'ES', 38.96667000, -0.15000000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q974889'),
(33911, 'Dalías', 5095, 'AL', 207, 'ES', 36.82179000, -2.87138000, '2019-10-05 22:45:43', '2022-08-28 18:41:41', 1, 'Q1110389'),
(33912, 'Darnius', 5103, 'GI', 207, 'ES', 42.36667000, 2.83333000, '2019-10-05 22:45:43', '2022-08-29 10:53:16', 1, 'Q1110389');

