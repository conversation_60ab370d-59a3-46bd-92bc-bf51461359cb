INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(13174, 'Mondaí', 2014, 'SC', 31, 'BR', -27.08404000, -53.43972000, '2019-10-05 22:35:15', '2020-05-01 17:22:38', 1, 'Q1920600'),
(13175, 'Mongaguá', 2021, 'SP', 31, 'BR', -24.09306000, -46.62083000, '2019-10-05 22:35:15', '2020-05-01 17:22:38', 1, 'Q983529'),
(13176, '<PERSON><PERSON><PERSON>', 1998, 'MG', 31, 'BR', -18.37562000, -44.02596000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q983529'),
(13177, '<PERSON><PERSON><PERSON>', 2008, 'P<PERSON>', 31, '<PERSON>', -5.64162000, -42.54879000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1935712'),
(13178, '<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>', 2008, 'PI', 31, 'BR', -6.94982000, -41.03155000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q1935712'),
(13179, 'Mon<PERSON><PERSON> <PERSON>', 1998, 'MG', 31, 'BR', -21.72267000, -45.46795000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1935712'),
(13180, 'Monsenhor Tabosa', 2016, 'CE', 31, 'BR', -4.96764000, -40.06725000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q526892'),
(13181, 'Montadas', 2005, 'PB', 31, 'BR', -7.08947000, -35.90300000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q526892'),
(13182, 'Montalvânia', 1998, 'MG', 31, 'BR', -14.41086000, -44.44214000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q526892'),
(13183, 'Montanha', 2018, 'ES', 31, 'BR', -18.12694000, -40.36333000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q2013818'),
(13184, 'Montanhas', 2019, 'RN', 31, 'BR', -6.48583000, -35.28750000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q548971'),
(13185, 'Montauri', 2001, 'RS', 31, 'BR', -28.66498000, -52.05388000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q548971'),
(13186, 'Monte Alegre', 2019, 'RN', 31, 'BR', -5.88972000, -36.30139000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22018312'),
(13187, 'Monte Alegre', 2009, 'PA', 31, 'BR', -2.00082000, -54.08102000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q2289897'),
(13188, 'Monte Alegre de Goiás', 2000, 'GO', 31, 'BR', -13.26150000, -46.84266000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q2289897'),
(13189, 'Monte Alegre de Minas', 1998, 'MG', 31, 'BR', -18.81882000, -48.91823000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1789748'),
(13190, 'Monte Alegre de Sergipe', 2003, 'SE', 31, 'BR', -10.07263000, -37.60470000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22038204'),
(13191, 'Monte Alegre do Piauí', 2008, 'PI', 31, 'BR', -9.74213000, -44.98266000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q22038204'),
(13192, 'Monte Alegre do Sul', 2021, 'SP', 31, 'BR', -22.71002000, -46.67181000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22038204'),
(13193, 'Monte Alegre dos Campos', 2001, 'RS', 31, 'BR', -28.70177000, -50.80405000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22038204'),
(13194, 'Monte Alto', 2021, 'SP', 31, 'BR', -21.26111000, -48.49639000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1760626'),
(13195, 'Monte Aprazível', 2021, 'SP', 31, 'BR', -20.77250000, -49.71417000, '2019-10-05 22:35:15', '2020-05-01 17:22:38', 1, 'Q1648289'),
(13196, 'Monte Azul', 1998, 'MG', 31, 'BR', -15.21266000, -43.02334000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1790247'),
(13197, 'Monte Azul Paulista', 2021, 'SP', 31, 'BR', -20.90722000, -48.64139000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1800695'),
(13198, 'Monte Belo', 1998, 'MG', 31, 'BR', -21.32001000, -46.33519000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1800695'),
(13199, 'Monte Belo do Sul', 2001, 'RS', 31, 'BR', -29.13180000, -51.64287000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1800695'),
(13200, 'Monte Carlo', 2014, 'SC', 31, 'BR', -27.18677000, -50.92110000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1800695'),
(13201, 'Monte Carmelo', 1998, 'MG', 31, 'BR', -18.63960000, -47.49788000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062362'),
(13202, 'Monte Castelo', 2021, 'SP', 31, 'BR', -21.22773000, -51.57141000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062362'),
(13203, 'Monte Castelo', 2014, 'SC', 31, 'BR', -26.61655000, -50.28851000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062362'),
(13204, 'Monte Formoso', 1998, 'MG', 31, 'BR', -16.87599000, -41.27134000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062362'),
(13205, 'Monte Horebe', 2005, 'PB', 31, 'BR', -7.20792000, -38.52503000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062362'),
(13206, 'Monte Mor', 2021, 'SP', 31, 'BR', -22.94667000, -47.31583000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q784207'),
(13207, 'Monte Negro', 2013, 'RO', 31, 'BR', -10.30450000, -63.35402000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q784207'),
(13208, 'Monte Santo', 2002, 'BA', 31, 'BR', -10.43778000, -39.33278000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1808562'),
(13209, 'Monte Santo de Minas', 1998, 'MG', 31, 'BR', -21.20428000, -46.95403000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1789740'),
(13210, 'Monte Santo do Tocantins', 2020, 'TO', 31, 'BR', -10.00972000, -49.12747000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1789740'),
(13211, 'Monte Sião', 1998, 'MG', 31, 'BR', -22.43134000, -46.56796000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q1755752'),
(13212, 'Monte Verde', 1998, 'MG', 31, 'BR', -22.86417000, -46.03500000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1755752'),
(13213, 'Monte das Gameleiras', 2019, 'RN', 31, 'BR', -6.43448000, -35.79888000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1802831'),
(13214, 'Monte do Carmo', 2020, 'TO', 31, 'BR', -10.72034000, -48.01807000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1802831'),
(13215, 'Monteiro', 2005, 'PB', 31, 'BR', -7.91637000, -37.17496000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1808426'),
(13216, 'Monteiro Lobato', 2021, 'SP', 31, 'BR', -22.93300000, -45.80525000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1760453'),
(13217, 'Monteirópolis', 2007, 'AL', 31, 'BR', -9.62019000, -37.29350000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q1760453'),
(13218, 'Montenegro', 2001, 'RS', 31, 'BR', -29.68861000, -51.46111000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1001470'),
(13219, 'Montes Altos', 2015, 'MA', 31, 'BR', -5.83333000, -47.06667000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q2012463'),
(13220, 'Montes Claros', 1998, 'MG', 31, 'BR', -16.58789000, -43.89995000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q651906'),
(13221, 'Montes Claros de Goiás', 2000, 'GO', 31, 'BR', -15.96248000, -51.50601000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q651906'),
(13222, 'Montezuma', 1998, 'MG', 31, 'BR', -15.19248000, -42.48132000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062358'),
(13223, 'Montividiu', 2000, 'GO', 31, 'BR', -17.25815000, -51.15739000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062358'),
(13224, 'Montividiu do Norte', 2000, 'GO', 31, 'BR', -13.09232000, -48.74161000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22062358'),
(13225, 'Monção', 2015, 'MA', 31, 'BR', -3.53292000, -45.30690000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q2103583'),
(13226, 'Monções', 2021, 'SP', 31, 'BR', -20.86875000, -50.08331000, '2019-10-05 22:35:15', '2020-05-01 17:22:38', 1, 'Q2103583'),
(13227, 'Morada Nova', 2016, 'CE', 31, 'BR', -5.10667000, -38.37250000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1807869'),
(13228, 'Morada Nova de Minas', 1998, 'MG', 31, 'BR', -18.56612000, -45.47222000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1807869'),
(13229, 'Moraújo', 2016, 'CE', 31, 'BR', -3.46006000, -40.69958000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q1807869'),
(13230, 'Moreilândia', 2006, 'PE', 31, 'BR', -7.63092000, -39.52167000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q1807869'),
(13231, 'Moreira Sales', 2022, 'PR', 31, 'BR', -24.06222000, -53.00694000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q2005476'),
(13232, 'Moreno', 2006, 'PE', 31, 'BR', -8.11861000, -35.09222000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q2012258'),
(13233, 'Mormaço', 2001, 'RS', 31, 'BR', -28.69004000, -52.66928000, '2019-10-05 22:35:15', '2020-05-01 17:22:38', 1, 'Q1757400'),
(13234, 'Morpará', 2002, 'BA', 31, 'BR', -11.75265000, -43.09133000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q1757400'),
(13235, 'Morretes', 2022, 'PR', 31, 'BR', -25.53075000, -48.87427000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1757400'),
(13236, 'Morrinhos', 2016, 'CE', 31, 'BR', -3.28421000, -40.09125000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q22059544'),
(13237, 'Morrinhos', 2000, 'GO', 31, 'BR', -17.79922000, -49.09225000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1025767'),
(13238, 'Morrinhos do Sul', 2001, 'RS', 31, 'BR', -29.34362000, -49.96435000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1025767'),
(13239, 'Morro Agudo', 2021, 'SP', 31, 'BR', -20.73139000, -48.05778000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1759626'),
(13240, 'Morro Agudo de Goiás', 2000, 'GO', 31, 'BR', -15.32310000, -50.01578000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q1806964'),
(13241, 'Morro Cabeça no Tempo', 2008, 'PI', 31, 'BR', -9.87713000, -43.90657000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q1806964'),
(13242, 'Morro Grande', 2014, 'SC', 31, 'BR', -28.72547000, -49.74789000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q1806964'),
(13243, 'Morro Redondo', 2001, 'RS', 31, 'BR', -31.63833000, -52.63569000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q505340'),
(13244, 'Morro Reuter', 2001, 'RS', 31, 'BR', -29.51567000, -51.01861000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q505340'),
(13245, 'Morro da Cruz', 2014, 'SC', 31, 'BR', -27.58490000, -48.53562000, '2019-10-05 22:35:15', '2019-10-05 22:35:15', 1, 'Q505340'),
(13246, 'Morro da Fumaça', 2014, 'SC', 31, 'BR', -28.65083000, -49.21000000, '2019-10-05 22:35:15', '2020-05-01 17:22:38', 1, 'Q1758949'),
(13247, 'Morro da Garça', 1998, 'MG', 31, 'BR', -18.61746000, -44.63238000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q1758949'),
(13248, 'Morro do Chapéu', 2002, 'BA', 31, 'BR', -11.54852000, -41.15804000, '2019-10-05 22:35:15', '2020-05-01 17:22:36', 1, 'Q1807821'),
(13249, 'Morro do Chapéu do Piauí', 2008, 'PI', 31, 'BR', -3.68694000, -42.22185000, '2019-10-05 22:35:15', '2020-05-01 17:22:37', 1, 'Q1807821'),
(13250, 'Morro do Pilar', 1998, 'MG', 31, 'BR', -19.23372000, -43.40140000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1807821'),
(13251, 'Morros', 2015, 'MA', 31, 'BR', -2.96159000, -43.87725000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q301700'),
(13252, 'Mortugaba', 2002, 'BA', 31, 'BR', -14.98480000, -42.40555000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q301700'),
(13253, 'Morungaba', 2021, 'SP', 31, 'BR', -22.88318000, -46.78699000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q301700'),
(13254, 'Mossoró', 2019, 'RN', 31, 'BR', -5.18750000, -37.34417000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q694845'),
(13255, 'Mossâmedes', 2000, 'GO', 31, 'BR', -16.16495000, -50.17004000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q694845'),
(13256, 'Mostardas', 2001, 'RS', 31, 'BR', -30.92226000, -50.85421000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q694845'),
(13257, 'Motuca', 2021, 'SP', 31, 'BR', -21.48936000, -48.15669000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q694845'),
(13258, 'Mozarlândia', 2000, 'GO', 31, 'BR', -14.74472000, -50.57056000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1003419'),
(13259, 'Muaná', 2009, 'PA', 31, 'BR', -1.52833000, -49.21667000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q1804369'),
(13260, 'Mucambo', 2016, 'CE', 31, 'BR', -3.90780000, -40.76944000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2027361'),
(13261, 'Mucugê', 2002, 'BA', 31, 'BR', -13.14869000, -41.51742000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q2027361'),
(13262, 'Mucuri', 2002, 'BA', 31, 'BR', -18.08639000, -39.55083000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1761474'),
(13263, 'Mucurici', 2018, 'ES', 31, 'BR', -18.09333000, -40.51583000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q339532'),
(13264, 'Muitos Capões', 2001, 'RS', 31, 'BR', -28.43447000, -51.24691000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q339532'),
(13265, 'Muliterno', 2001, 'RS', 31, 'BR', -28.31235000, -51.78652000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q339532'),
(13266, 'Mulungu', 2005, 'PB', 31, 'BR', -7.02444000, -35.46194000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2341988'),
(13267, 'Mulungu', 2016, 'CE', 31, 'BR', -4.30636000, -38.98955000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22059542'),
(13268, 'Mulungu do Morro', 2002, 'BA', 31, 'BR', -12.00797000, -41.42582000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22059542'),
(13269, 'Mundo Novo', 2002, 'BA', 31, 'BR', -11.85889000, -40.47250000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1984590'),
(13270, 'Mundo Novo', 2010, 'MS', 31, 'BR', -23.92628000, -54.25746000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2289881'),
(13271, 'Mundo Novo', 2000, 'GO', 31, 'BR', -13.71649000, -50.23715000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2289881'),
(13272, 'Munhoz', 1998, 'MG', 31, 'BR', -22.62898000, -46.30889000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2289881'),
(13273, 'Munhoz de Melo', 2022, 'PR', 31, 'BR', -23.11288000, -51.73192000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2289881'),
(13274, 'Muniz Ferreira', 2002, 'BA', 31, 'BR', -13.00146000, -39.10601000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1793732'),
(13275, 'Muniz Freire', 2018, 'ES', 31, 'BR', -20.46417000, -41.41306000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1797483'),
(13276, 'Muqui', 2018, 'ES', 31, 'BR', -20.91522000, -41.30089000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1797483'),
(13277, 'Muquém de São Francisco', 2002, 'BA', 31, 'BR', -12.19886000, -43.51311000, '2019-10-05 22:35:16', '2025-04-28 16:02:50', 1, 'Q1797483'),
(13278, 'Muriaé', 1998, 'MG', 31, 'BR', -21.13056000, -42.36639000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q1637446'),
(13279, 'Muribeca', 2003, 'SE', 31, 'BR', -10.38925000, -36.97628000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1637446'),
(13280, 'Murici', 2007, 'AL', 31, 'BR', -9.29835000, -35.89999000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1795628'),
(13281, 'Murici dos Portelas', 2008, 'PI', 31, 'BR', -3.36504000, -42.00218000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1795628'),
(13282, 'Muricilândia', 2020, 'TO', 31, 'BR', -6.98598000, -48.79167000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q1795628'),
(13283, 'Muritiba', 2002, 'BA', 31, 'BR', -12.91667000, -39.25000000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22038377'),
(13284, 'Murutinga do Sul', 2021, 'SP', 31, 'BR', -20.99638000, -51.30376000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1649943'),
(13285, 'Mutum', 1998, 'MG', 31, 'BR', -19.92677000, -41.44835000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q945723'),
(13286, 'Mutunópolis', 2000, 'GO', 31, 'BR', -13.70257000, -49.29995000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q945723'),
(13287, 'Mutuípe', 2002, 'BA', 31, 'BR', -13.22861000, -39.50472000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q975660'),
(13288, 'Muzambinho', 1998, 'MG', 31, 'BR', -21.35240000, -46.51842000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1790124'),
(13289, 'Muçum', 2001, 'RS', 31, 'BR', -29.14017000, -51.82330000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q1790124'),
(13290, 'Mário Campos', 1998, 'MG', 31, 'BR', -20.07230000, -44.17602000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q1790124'),
(13291, 'Mâncio Lima', 2012, 'AC', 31, 'BR', -7.61417000, -72.89583000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1772621'),
(13292, 'Mãe d\'Água', 2005, 'PB', 31, 'BR', -7.21990000, -37.45284000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q1772621'),
(13293, 'Mãe do Rio', 2009, 'PA', 31, 'BR', -1.98328000, -47.51572000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q2008401'),
(13294, 'Nacip Raydan', 1998, 'MG', 31, 'BR', -18.47447000, -42.17506000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2008401'),
(13295, 'Nantes', 2021, 'SP', 31, 'BR', -22.60334000, -51.20840000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1119526'),
(13296, 'Nanuque', 1998, 'MG', 31, 'BR', -17.73565000, -40.44441000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q83011'),
(13297, 'Naque', 1998, 'MG', 31, 'BR', -19.18321000, -42.33843000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q83011'),
(13298, 'Narandiba', 2021, 'SP', 31, 'BR', -22.53395000, -51.50976000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q83011'),
(13299, 'Natal', 2019, 'RN', 31, 'BR', -5.81010000, -35.22674000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q131620'),
(13300, 'Natalândia', 1998, 'MG', 31, 'BR', -16.56307000, -46.51392000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q131620'),
(13301, 'Natividade', 1997, 'RJ', 31, 'BR', -21.04222000, -41.97333000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2057463'),
(13302, 'Natividade', 2020, 'TO', 31, 'BR', -11.75889000, -47.64485000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1801567'),
(13303, 'Natividade da Serra', 2021, 'SP', 31, 'BR', -23.39447000, -45.39368000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1801567'),
(13304, 'Natuba', 2005, 'PB', 31, 'BR', -7.64139000, -35.55000000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2008665'),
(13305, 'Natércia', 1998, 'MG', 31, 'BR', -22.13227000, -45.49689000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q749384'),
(13306, 'Navegantes', 2014, 'SC', 31, 'BR', -26.89889000, -48.65417000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1010299'),
(13307, 'Naviraí', 2010, 'MS', 31, 'BR', -23.06500000, -54.19056000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1793018'),
(13308, 'Nazareno', 1998, 'MG', 31, 'BR', -21.20793000, -44.60851000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2007785'),
(13309, 'Nazarezinho', 2005, 'PB', 31, 'BR', -6.91991000, -38.32592000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2007785'),
(13310, 'Nazaré', 2020, 'TO', 31, 'BR', -6.31311000, -47.78944000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q2007785'),
(13311, 'Nazaré', 2002, 'BA', 31, 'BR', -12.95002000, -38.97869000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1808393'),
(13312, 'Nazaré Paulista', 2021, 'SP', 31, 'BR', -23.18203000, -46.36202000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q1808393'),
(13313, 'Nazaré da Mata', 2006, 'PE', 31, 'BR', -7.74167000, -35.22778000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q1811182'),
(13314, 'Nazaré do Piauí', 2008, 'PI', 31, 'BR', -7.03279000, -42.73216000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q1811182'),
(13315, 'Nazária', 2008, 'PI', 31, 'BR', -5.44546000, -42.86840000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q10335889'),
(13316, 'Nazário', 2000, 'GO', 31, 'BR', -16.58010000, -49.86543000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q10335889'),
(13317, 'Nepomuceno', 1998, 'MG', 31, 'BR', -21.24027000, -45.24015000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q583832'),
(13318, 'Nerópolis', 2000, 'GO', 31, 'BR', -16.43883000, -49.18117000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q986284'),
(13319, 'Neves Paulista', 2021, 'SP', 31, 'BR', -20.87439000, -49.64850000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q986284'),
(13320, 'Neópolis', 2003, 'SE', 31, 'BR', -10.35698000, -36.66870000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q2011928'),
(13321, 'Nhamundá', 2004, 'AM', 31, 'BR', -2.18611000, -56.71306000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1793306'),
(13322, 'Nhandeara', 2021, 'SP', 31, 'BR', -20.68970000, -50.04070000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1649650'),
(13323, 'Nicolau Vergueiro', 2001, 'RS', 31, 'BR', -28.52442000, -52.44444000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1649650'),
(13324, 'Nilo Peçanha', 2002, 'BA', 31, 'BR', -13.64812000, -39.23876000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1649650'),
(13325, 'Nilópolis', 1997, 'RJ', 31, 'BR', -22.82106000, -43.42703000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q983459'),
(13326, 'Nina Rodrigues', 2015, 'MA', 31, 'BR', -3.46375000, -43.77165000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2020346'),
(13327, 'Ninheira', 1998, 'MG', 31, 'BR', -15.42231000, -41.65775000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2020346'),
(13328, 'Nioaque', 2010, 'MS', 31, 'BR', -21.18151000, -55.75856000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2020346'),
(13329, 'Nipoã', 2021, 'SP', 31, 'BR', -20.89366000, -49.78133000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q2020346'),
(13330, 'Niquelândia', 2000, 'GO', 31, 'BR', -14.47389000, -48.45972000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1794378'),
(13331, 'Niterói', 1997, 'RJ', 31, 'BR', -22.91715000, -43.08391000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q178725'),
(13332, 'Nobres', 2011, 'MT', 31, 'BR', -14.72028000, -56.32750000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1805596'),
(13333, 'Nonoai', 2001, 'RS', 31, 'BR', -27.36903000, -52.87590000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q941207'),
(13334, 'Nordestina', 2002, 'BA', 31, 'BR', -10.89685000, -39.45141000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q941207'),
(13335, 'Nortelândia', 2011, 'MT', 31, 'BR', -14.45472000, -56.80278000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q1805662'),
(13336, 'Nossa Senhora Aparecida', 2003, 'SE', 31, 'BR', -10.29846000, -37.48812000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1805662'),
(13337, 'Nossa Senhora da Glória', 2003, 'SE', 31, 'BR', -10.19293000, -37.53292000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q22038535'),
(13338, 'Nossa Senhora das Dores', 2003, 'SE', 31, 'BR', -10.45356000, -37.24677000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22038537'),
(13339, 'Nossa Senhora das Graças', 2022, 'PR', 31, 'BR', -22.93036000, -51.80240000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q22038537'),
(13340, 'Nossa Senhora de Lourdes', 2003, 'SE', 31, 'BR', -10.10241000, -37.02768000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2011901'),
(13341, 'Nossa Senhora de Nazaré', 2008, 'PI', 31, 'BR', -4.63841000, -42.19017000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q963907'),
(13342, 'Nossa Senhora do Livramento', 2011, 'MT', 31, 'BR', -15.93447000, -56.54590000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2107985'),
(13343, 'Nossa Senhora do Socorro', 2003, 'SE', 31, 'BR', -10.85500000, -37.12611000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q1800687'),
(13344, 'Nossa Senhora dos Remédios', 2008, 'PI', 31, 'BR', -4.05765000, -42.60651000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q2104808'),
(13345, 'Nova Aliança', 2021, 'SP', 31, 'BR', -21.06591000, -49.52593000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q2104808'),
(13346, 'Nova Aliança do Ivaí', 2022, 'PR', 31, 'BR', -23.18670000, -52.62571000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q2104808'),
(13347, 'Nova Alvorada', 2001, 'RS', 31, 'BR', -28.70497000, -52.16616000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2104808'),
(13348, 'Nova Alvorada do Sul', 2010, 'MS', 31, 'BR', -21.48079000, -54.15260000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2104808'),
(13349, 'Nova América', 2000, 'GO', 31, 'BR', -15.05098000, -49.92499000, '2019-10-05 22:35:16', '2020-05-01 17:22:36', 1, 'Q2104808'),
(13350, 'Nova América da Colina', 2022, 'PR', 31, 'BR', -23.33377000, -50.69624000, '2019-10-05 22:35:16', '2020-05-01 17:22:37', 1, 'Q2104808'),
(13351, 'Nova Andradina', 2010, 'MS', 31, 'BR', -21.88205000, -53.46637000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q2104808'),
(13352, 'Nova Araçá', 2001, 'RS', 31, 'BR', -28.64411000, -51.73489000, '2019-10-05 22:35:16', '2020-05-01 17:22:38', 1, 'Q2104808'),
(13353, 'Nova Aurora', 2022, 'PR', 31, 'BR', -22.86667000, -52.56667000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22038543'),
(13354, 'Nova Aurora', 2000, 'GO', 31, 'BR', -18.10107000, -48.27661000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22038543'),
(13355, 'Nova Bandeirantes', 2011, 'MT', 31, 'BR', -9.68571000, -58.01840000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22038543'),
(13356, 'Nova Bassano', 2001, 'RS', 31, 'BR', -28.70293000, -51.80542000, '2019-10-05 22:35:16', '2019-10-05 22:35:16', 1, 'Q22038543'),
(13357, 'Nova Belém', 1998, 'MG', 31, 'BR', -18.49977000, -41.11879000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q22038543'),
(13358, 'Nova Boa Vista', 2001, 'RS', 31, 'BR', -28.00887000, -52.98206000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q22038543'),
(13359, 'Nova Brasilândia', 2011, 'MT', 31, 'BR', -14.75742000, -55.10534000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q22038543'),
(13360, 'Nova Brasilândia d\'Oeste', 2013, 'RO', 31, 'BR', -11.49111000, -62.16495000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q2014329'),
(13361, 'Nova Bréscia', 2001, 'RS', 31, 'BR', -29.20206000, -52.04822000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q2014329'),
(13362, 'Nova Campina', 2021, 'SP', 31, 'BR', -24.20918000, -48.98456000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2014329'),
(13363, 'Nova Canaã', 2002, 'BA', 31, 'BR', -14.84621000, -40.17826000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q2014329'),
(13364, 'Nova Canaã Paulista', 2021, 'SP', 31, 'BR', -20.37020000, -50.90616000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q2014329'),
(13365, 'Nova Canaã do Norte', 2011, 'MT', 31, 'BR', -10.71987000, -56.03436000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q2014329'),
(13366, 'Nova Candelária', 2001, 'RS', 31, 'BR', -27.59344000, -54.12172000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q734270'),
(13367, 'Nova Cantu', 2022, 'PR', 31, 'BR', -24.65652000, -52.56315000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q734270'),
(13368, 'Nova Castilho', 2021, 'SP', 31, 'BR', -20.78530000, -50.34957000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q734270'),
(13369, 'Nova Colinas', 2015, 'MA', 31, 'BR', -7.26656000, -46.29184000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q734270'),
(13370, 'Nova Crixás', 2000, 'GO', 31, 'BR', -14.27678000, -50.57649000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q734270'),
(13371, 'Nova Cruz', 2019, 'RN', 31, 'BR', -6.46807000, -35.45455000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1802859'),
(13372, 'Nova Era', 1998, 'MG', 31, 'BR', -19.70970000, -43.01401000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1789722'),
(13373, 'Nova Erechim', 2014, 'SC', 31, 'BR', -26.91297000, -52.89851000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1789722'),
(13374, 'Nova Esperança', 2022, 'PR', 31, 'BR', -23.18311000, -52.25446000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q1789722'),
(13375, 'Nova Esperança do Piriá', 2009, 'PA', 31, 'BR', -2.45364000, -46.97384000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q1121422'),
(13376, 'Nova Esperança do Sudoeste', 2022, 'PR', 31, 'BR', -25.88851000, -53.26030000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q1121422'),
(13377, 'Nova Esperança do Sul', 2001, 'RS', 31, 'BR', -29.39225000, -54.83460000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q520098'),
(13378, 'Nova Europa', 2021, 'SP', 31, 'BR', -21.79214000, -48.56880000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q520098'),
(13379, 'Nova Floresta', 2005, 'PB', 31, 'BR', -6.48792000, -36.20101000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q521125'),
(13380, 'Nova Friburgo', 1997, 'RJ', 31, 'BR', -22.28194000, -42.53111000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q468563'),
(13381, 'Nova Fátima', 2002, 'BA', 31, 'BR', -11.56608000, -39.58609000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q468563'),
(13382, 'Nova Fátima', 2022, 'PR', 31, 'BR', -23.39579000, -50.53565000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q2472564'),
(13383, 'Nova Glória', 2000, 'GO', 31, 'BR', -15.06360000, -49.48353000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q2472564'),
(13384, 'Nova Granada', 2021, 'SP', 31, 'BR', -20.53389000, -49.31417000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1648342'),
(13385, 'Nova Guarita', 2011, 'MT', 31, 'BR', -10.28525000, -55.33730000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1648342'),
(13386, 'Nova Guataporanga', 2021, 'SP', 31, 'BR', -21.31898000, -51.64323000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1648342'),
(13387, 'Nova Hartz', 2001, 'RS', 31, 'BR', -29.59640000, -50.90836000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1648342'),
(13388, 'Nova Ibiá', 2002, 'BA', 31, 'BR', -13.86230000, -39.58877000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q1648342'),
(13389, 'Nova Iguaçu', 1997, 'RJ', 31, 'BR', -22.75917000, -43.45111000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q188897'),
(13390, 'Nova Iguaçu de Goiás', 2000, 'GO', 31, 'BR', -14.29731000, -49.31188000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q188897'),
(13391, 'Nova Independência', 2021, 'SP', 31, 'BR', -21.15481000, -51.52759000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q188897'),
(13392, 'Nova Iorque', 2015, 'MA', 31, 'BR', -6.74961000, -44.03977000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q188897'),
(13393, 'Nova Ipixuna', 2009, 'PA', 31, 'BR', -4.98779000, -49.19861000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q188897'),
(13394, 'Nova Itaberaba', 2014, 'SC', 31, 'BR', -26.93156000, -52.84006000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q188897'),
(13395, 'Nova Itarana', 2002, 'BA', 31, 'BR', -13.04028000, -40.00784000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q188897'),
(13396, 'Nova Lacerda', 2011, 'MT', 31, 'BR', -14.26732000, -59.79280000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q188897'),
(13397, 'Nova Laranjeiras', 2022, 'PR', 31, 'BR', -25.21942000, -52.57752000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q188897'),
(13398, 'Nova Lima', 1998, 'MG', 31, 'BR', -19.98556000, -43.84667000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1642662'),
(13399, 'Nova Londrina', 2022, 'PR', 31, 'BR', -22.76583000, -52.98500000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2004101'),
(13400, 'Nova Luzitânia', 2021, 'SP', 31, 'BR', -20.87304000, -50.24297000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q2004101'),
(13401, 'Nova Mamoré', 2013, 'RO', 31, 'BR', -10.53837000, -64.49429000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q2004101'),
(13402, 'Nova Marilândia', 2011, 'MT', 31, 'BR', -14.31258000, -57.26088000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q2004101'),
(13403, 'Nova Maringá', 2011, 'MT', 31, 'BR', -12.98384000, -57.19167000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q2004101'),
(13404, 'Nova Monte Verde', 2011, 'MT', 31, 'BR', -9.99743000, -57.29768000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2004101'),
(13405, 'Nova Mutum', 2011, 'MT', 31, 'BR', -13.58906000, -56.15193000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2004101'),
(13406, 'Nova Módica', 1998, 'MG', 31, 'BR', -18.45009000, -41.51304000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q2004101'),
(13407, 'Nova Nazaré', 2011, 'MT', 31, 'BR', -14.14648000, -51.79821000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q2012379'),
(13408, 'Nova Odessa', 2021, 'SP', 31, 'BR', -22.77750000, -47.29583000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1648345'),
(13409, 'Nova Olinda', 2020, 'TO', 31, 'BR', -7.68973000, -48.28152000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1648345'),
(13410, 'Nova Olinda', 2005, 'PB', 31, 'BR', -7.45231000, -38.01122000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1648345'),
(13411, 'Nova Olinda', 2016, 'CE', 31, 'BR', -7.08567000, -39.67195000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1913711'),
(13412, 'Nova Olinda do Maranhão', 2015, 'MA', 31, 'BR', -2.84498000, -45.92010000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q1913711'),
(13413, 'Nova Olinda do Norte', 2004, 'AM', 31, 'BR', -3.89174000, -59.09542000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1772535'),
(13414, 'Nova Olímpia', 2011, 'MT', 31, 'BR', -14.79722000, -57.28806000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q2103607'),
(13415, 'Nova Olímpia', 2022, 'PR', 31, 'BR', -23.43206000, -53.03895000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q2103607'),
(13416, 'Nova Palma', 2001, 'RS', 31, 'BR', -29.40710000, -53.43240000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2103607'),
(13417, 'Nova Palmeira', 2005, 'PB', 31, 'BR', -6.67346000, -36.44186000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2103607'),
(13418, 'Nova Petrópolis', 2001, 'RS', 31, 'BR', -29.37639000, -51.11444000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q983710'),
(13419, 'Nova Ponte', 1998, 'MG', 31, 'BR', -19.28646000, -47.71367000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q983710'),
(13420, 'Nova Porteirinha', 1998, 'MG', 31, 'BR', -15.71578000, -43.28155000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q983710'),
(13421, 'Nova Prata', 2001, 'RS', 31, 'BR', -28.78389000, -51.61000000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1758146'),
(13422, 'Nova Prata do Iguaçu', 2022, 'PR', 31, 'BR', -25.60849000, -53.38052000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q1758146'),
(13423, 'Nova Pádua', 2001, 'RS', 31, 'BR', -29.01222000, -51.31451000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q1758146'),
(13424, 'Nova Ramada', 2001, 'RS', 31, 'BR', -28.09297000, -53.69502000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1758146'),
(13425, 'Nova Redenção', 2002, 'BA', 31, 'BR', -12.87403000, -41.12335000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q1758146'),
(13426, 'Nova Resende', 1998, 'MG', 31, 'BR', -21.09898000, -46.41684000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1758146'),
(13427, 'Nova Roma', 2000, 'GO', 31, 'BR', -13.84078000, -47.00832000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1758146'),
(13428, 'Nova Roma do Sul', 2001, 'RS', 31, 'BR', -28.99091000, -51.41443000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1758146'),
(13429, 'Nova Rosalândia', 2020, 'TO', 31, 'BR', -10.56285000, -48.97053000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q1758146'),
(13430, 'Nova Russas', 2016, 'CE', 31, 'BR', -4.72277000, -40.59553000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1824152'),
(13431, 'Nova Santa Bárbara', 2022, 'PR', 31, 'BR', -23.60238000, -50.75450000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q2005130'),
(13432, 'Nova Santa Helena', 2011, 'MT', 31, 'BR', -10.92036000, -54.90636000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2012034'),
(13433, 'Nova Santa Rita', 2008, 'PI', 31, 'BR', -8.11204000, -42.01361000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q2103222'),
(13434, 'Nova Santa Rita', 2001, 'RS', 31, 'BR', -29.83265000, -51.27431000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q985571'),
(13435, 'Nova Santa Rosa', 2022, 'PR', 31, 'BR', -24.44687000, -53.99553000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q985571'),
(13436, 'Nova Serrana', 1998, 'MG', 31, 'BR', -19.84529000, -44.97669000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q985571'),
(13437, 'Nova Soure', 2002, 'BA', 31, 'BR', -11.23333000, -38.48333000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1772518'),
(13438, 'Nova Tebas', 2022, 'PR', 31, 'BR', -24.43082000, -51.94584000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1772518'),
(13439, 'Nova Timboteua', 2009, 'PA', 31, 'BR', -1.12059000, -47.42089000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1772518'),
(13440, 'Nova Trento', 2014, 'SC', 31, 'BR', -27.30470000, -49.04260000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1772518'),
(13441, 'Nova Ubiratã', 2011, 'MT', 31, 'BR', -12.68233000, -54.47729000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q1772518'),
(13442, 'Nova União', 2013, 'RO', 31, 'BR', -10.94938000, -62.51087000, '2019-10-05 22:35:17', '2020-05-01 17:22:38', 1, 'Q1772518'),
(13443, 'Nova União', 1998, 'MG', 31, 'BR', -19.64356000, -43.57796000, '2019-10-05 22:35:17', '2020-05-01 17:22:37', 1, 'Q22062438'),
(13444, 'Nova Veneza', 2014, 'SC', 31, 'BR', -28.70447000, -49.58779000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q22062438'),
(13445, 'Nova Veneza', 2000, 'GO', 31, 'BR', -16.35456000, -49.30001000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q22062438'),
(13446, 'Nova Venécia', 2018, 'ES', 31, 'BR', -18.71056000, -40.40056000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q734635'),
(13447, 'Nova Viçosa', 2002, 'BA', 31, 'BR', -17.89194000, -39.37194000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q1794050'),
(13448, 'Nova Xavantina', 2011, 'MT', 31, 'BR', -14.66463000, -52.35558000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1794050'),
(13449, 'Novais', 2021, 'SP', 31, 'BR', -20.97660000, -48.90985000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1794050'),
(13450, 'Novo Acordo', 2020, 'TO', 31, 'BR', -10.15197000, -47.47165000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1794050'),
(13451, 'Novo Airão', 2004, 'AM', 31, 'BR', -1.98386000, -61.78513000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q1750332'),
(13452, 'Novo Alegre', 2020, 'TO', 31, 'BR', -12.87311000, -46.56936000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1750332'),
(13453, 'Novo Aripuanã', 2004, 'AM', 31, 'BR', -5.12056000, -60.37972000, '2019-10-05 22:35:17', '2020-05-01 17:22:36', 1, 'Q1793549'),
(13454, 'Novo Barreiro', 2001, 'RS', 31, 'BR', -27.89154000, -53.11220000, '2019-10-05 22:35:17', '2019-10-05 22:35:17', 1, 'Q1793549'),
(13455, 'Novo Brasil', 2000, 'GO', 31, 'BR', -16.04806000, -50.63638000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1793549'),
(13456, 'Novo Cabrais', 2001, 'RS', 31, 'BR', -29.76161000, -52.98402000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1793549'),
(13457, 'Novo Cruzeiro', 1998, 'MG', 31, 'BR', -17.39233000, -41.96978000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1755515'),
(13458, 'Novo Gama', 2000, 'GO', 31, 'BR', -16.12535000, -48.07041000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1755515'),
(13459, 'Novo Hamburgo', 2001, 'RS', 31, 'BR', -29.67833000, -51.13056000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q321331'),
(13460, 'Novo Horizonte', 2021, 'SP', 31, 'BR', -21.46806000, -49.22083000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13461, 'Novo Horizonte', 2002, 'BA', 31, 'BR', -12.87886000, -42.13834000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13462, 'Novo Horizonte', 2014, 'SC', 31, 'BR', -26.49656000, -52.78100000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13463, 'Novo Horizonte do Norte', 2011, 'MT', 31, 'BR', -11.37917000, -57.28359000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13464, 'Novo Horizonte do Oeste', 2013, 'RO', 31, 'BR', -11.70769000, -62.07989000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13465, 'Novo Horizonte do Sul', 2010, 'MS', 31, 'BR', -22.63068000, -53.73643000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13466, 'Novo Itacolomi', 2022, 'PR', 31, 'BR', -23.77560000, -51.53874000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13467, 'Novo Jardim', 2020, 'TO', 31, 'BR', -11.75148000, -46.54138000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1815907'),
(13468, 'Novo Lino', 2007, 'AL', 31, 'BR', -8.88091000, -35.61948000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1816167'),
(13469, 'Novo Machado', 2001, 'RS', 31, 'BR', -27.55482000, -54.53613000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1816167'),
(13470, 'Novo Mundo', 2011, 'MT', 31, 'BR', -9.83661000, -55.31349000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1816167'),
(13471, 'Novo Oriente', 2016, 'CE', 31, 'BR', -5.58278000, -40.74014000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q22059540'),
(13472, 'Novo Oriente de Minas', 1998, 'MG', 31, 'BR', -17.25169000, -41.22927000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q22059540'),
(13473, 'Novo Oriente do Piauí', 2008, 'PI', 31, 'BR', -6.55034000, -41.98555000, '2019-10-05 22:35:18', '2020-05-01 17:22:37', 1, 'Q22059540'),
(13474, 'Novo Planalto', 2000, 'GO', 31, 'BR', -13.38362000, -49.76104000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q22059540'),
(13475, 'Novo Progresso', 2009, 'PA', 31, 'BR', -7.75396000, -55.51343000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1787759'),
(13476, 'Novo Repartimento', 2009, 'PA', 31, 'BR', -4.70032000, -50.52220000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q1787759'),
(13477, 'Novo Santo Antônio', 2008, 'PI', 31, 'BR', -5.32848000, -41.96256000, '2019-10-05 22:35:18', '2020-05-01 17:22:37', 1, 'Q2169201'),
(13478, 'Novo Santo Antônio', 2011, 'MT', 31, 'BR', -12.32606000, -50.91431000, '2019-10-05 22:35:18', '2020-05-01 17:22:36', 1, 'Q2107875'),
(13479, 'Novo São Joaquim', 2011, 'MT', 31, 'BR', -15.09570000, -53.29575000, '2019-10-05 22:35:18', '2020-05-01 17:22:36', 1, 'Q2011959'),
(13480, 'Novo Tiradentes', 2001, 'RS', 31, 'BR', -27.55948000, -53.15567000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2011959'),
(13481, 'Novo Triunfo', 2002, 'BA', 31, 'BR', -10.34787000, -38.40211000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2011959'),
(13482, 'Novo Xingu', 2001, 'RS', 31, 'BR', -27.74556000, -53.04043000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2007711'),
(13483, 'Novorizonte', 1998, 'MG', 31, 'BR', -16.01733000, -42.38889000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2007711'),
(13484, 'Nuporanga', 2021, 'SP', 31, 'BR', -20.73095000, -47.75177000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q664158'),
(13485, 'Não Me Toque', 2001, 'RS', 31, 'BR', -28.45917000, -52.82083000, '2019-10-05 22:35:18', '2020-05-01 17:22:38', 1, 'Q22038444'),
(13486, 'Não-Me-Toque', 2001, 'RS', 31, 'BR', -28.47423000, -52.80396000, '2019-10-05 22:35:18', '2020-05-01 17:22:38', 1, 'Q600814'),
(13487, 'Nísia Floresta', 2019, 'RN', 31, 'BR', -6.09111000, -35.20861000, '2019-10-05 22:35:18', '2020-05-01 17:22:38', 1, 'Q1116574'),
(13488, 'Ocara', 2016, 'CE', 31, 'BR', -4.49083000, -38.59667000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2028371'),
(13489, 'Ocauçu', 2021, 'SP', 31, 'BR', -22.43034000, -49.95170000, '2019-10-05 22:35:18', '2020-05-01 17:22:38', 1, 'Q2028371'),
(13490, 'Oeiras', 2008, 'PI', 31, 'BR', -6.90686000, -42.17529000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2077764'),
(13491, 'Oeiras do Pará', 2009, 'PA', 31, 'BR', -2.00306000, -49.85444000, '2019-10-05 22:35:18', '2020-05-01 17:22:37', 1, 'Q2008580'),
(13492, 'Oiapoque', 1999, 'AP', 31, 'BR', 2.70795000, -52.16963000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2008580'),
(13493, 'Olaria', 1998, 'MG', 31, 'BR', -21.90638000, -43.96851000, '2019-10-05 22:35:18', '2019-10-05 22:35:18', 1, 'Q2077934'),
(13494, 'Olho d\'Água', 2005, 'PB', 31, 'BR', -7.26106000, -37.73242000, '2019-10-05 22:35:18', '2020-05-01 17:22:37', 1, 'Q2077934'),
(13495, 'Olho d\'Água Grande', 2007, 'AL', 31, 'BR', -10.05622000, -36.79522000, '2019-10-05 22:35:18', '2020-05-01 17:22:36', 1, 'Q2077934'),
(13496, 'Olho d\'Água das Cunhãs', 2015, 'MA', 31, 'BR', -4.02629000, -45.04831000, '2019-10-05 22:35:18', '2020-05-01 17:22:36', 1, 'Q2077934'),
(13497, 'Olho d\'Água das Flores', 2007, 'AL', 31, 'BR', -9.54034000, -37.25214000, '2019-10-05 22:35:18', '2020-05-01 17:22:36', 1, 'Q2077934'),
(13498, 'Olho d\'Água do Casado', 2007, 'AL', 31, 'BR', -9.45648000, -37.83496000, '2019-10-05 22:35:18', '2020-05-01 17:22:36', 1, 'Q1808294'),
(13499, 'Olho d\'Água do Piauí', 2008, 'PI', 31, 'BR', -5.84854000, -42.53937000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q1940465'),
(13500, 'Olho-d\'Água do Borges', 2019, 'RN', 31, 'BR', -5.99658000, -37.73631000, '2019-10-05 22:35:19', '2020-05-01 17:22:38', 1, 'Q1802421'),
(13501, 'Olhos-d\'Água', 1998, 'MG', 31, 'BR', -17.48888000, -43.54347000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q1790196'),
(13502, 'Olinda', 2006, 'PE', 31, 'BR', -8.00889000, -34.85528000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q28301'),
(13503, 'Olinda Nova do Maranhão', 2015, 'MA', 31, 'BR', -2.99757000, -44.96574000, '2019-10-05 22:35:19', '2020-05-01 17:22:36', 1, 'Q28301'),
(13504, 'Olindina', 2002, 'BA', 31, 'BR', -11.36667000, -38.33333000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1648508'),
(13505, 'Olivedos', 2005, 'PB', 31, 'BR', -6.99403000, -36.23822000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1648508'),
(13506, 'Oliveira', 1998, 'MG', 31, 'BR', -20.73820000, -44.71612000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1754812'),
(13507, 'Oliveira Fortes', 1998, 'MG', 31, 'BR', -21.33869000, -43.51985000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1754812'),
(13508, 'Oliveira de Fátima', 2020, 'TO', 31, 'BR', -10.67053000, -48.90783000, '2019-10-05 22:35:19', '2020-05-01 17:22:38', 1, 'Q1754812'),
(13509, 'Oliveira dos Brejinhos', 2002, 'BA', 31, 'BR', -12.31694000, -42.89611000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1795068'),
(13510, 'Olivença', 2007, 'AL', 31, 'BR', -9.51861000, -37.19056000, '2019-10-05 22:35:19', '2020-05-01 17:22:36', 1, 'Q950712'),
(13511, 'Olímpia', 2021, 'SP', 31, 'BR', -20.73722000, -48.91472000, '2019-10-05 22:35:19', '2020-05-01 17:22:38', 1, 'Q636096'),
(13512, 'Olímpio Noronha', 1998, 'MG', 31, 'BR', -22.08551000, -45.28730000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q636096'),
(13513, 'Onda Verde', 2021, 'SP', 31, 'BR', -20.61043000, -49.24474000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q636096'),
(13514, 'Onça de Pitangui', 1998, 'MG', 31, 'BR', -19.71041000, -44.74654000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q636096'),
(13515, 'Oratórios', 1998, 'MG', 31, 'BR', -20.43729000, -42.79741000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q636096'),
(13516, 'Oriente', 2021, 'SP', 31, 'BR', -22.15005000, -50.09676000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q636096'),
(13517, 'Orindiúva', 2021, 'SP', 31, 'BR', -20.22235000, -49.37594000, '2019-10-05 22:35:19', '2020-05-01 17:22:38', 1, 'Q636096'),
(13518, 'Oriximiná', 2009, 'PA', 31, 'BR', -1.76556000, -55.86611000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q2011919'),
(13519, 'Orizona', 2000, 'GO', 31, 'BR', -17.03139000, -48.29583000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q519154'),
(13520, 'Orizânia', 1998, 'MG', 31, 'BR', -20.50549000, -42.21156000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q519154'),
(13521, 'Orleans', 2014, 'SC', 31, 'BR', -28.30008000, -49.35423000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1784752'),
(13522, 'Orlândia', 2021, 'SP', 31, 'BR', -20.72028000, -47.88667000, '2019-10-05 22:35:19', '2020-05-01 17:22:38', 1, 'Q22038705'),
(13523, 'Orobó', 2006, 'PE', 31, 'BR', -7.74500000, -35.60222000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q2013693'),
(13524, 'Orocó', 2006, 'PE', 31, 'BR', -8.49405000, -39.57790000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q2010863'),
(13525, 'Ortigueira', 2022, 'PR', 31, 'BR', -24.20833000, -50.94944000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q2063986'),
(13526, 'Orós', 2016, 'CE', 31, 'BR', -6.25502000, -38.96761000, '2019-10-05 22:35:19', '2020-05-01 17:22:36', 1, 'Q526215'),
(13527, 'Osasco', 2021, 'SP', 31, 'BR', -23.53250000, -46.79167000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q4035'),
(13528, 'Oscar Bressane', 2021, 'SP', 31, 'BR', -22.29031000, -50.25646000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q4035'),
(13529, 'Osvaldo Cruz', 2021, 'SP', 31, 'BR', -21.67502000, -50.84203000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1761462'),
(13530, 'Osório', 2001, 'RS', 31, 'BR', -29.89454000, -50.23467000, '2019-10-05 22:35:19', '2020-05-01 17:22:38', 1, 'Q983519'),
(13531, 'Otacílio Costa', 2014, 'SC', 31, 'BR', -27.48306000, -50.12194000, '2019-10-05 22:35:19', '2020-05-01 17:22:38', 1, 'Q956998'),
(13532, 'Ouricuri', 2006, 'PE', 31, 'BR', -7.88250000, -40.08167000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q2064430'),
(13533, 'Ourilândia do Norte', 2009, 'PA', 31, 'BR', -7.59565000, -51.43201000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q2064430'),
(13534, 'Ourinhos', 2021, 'SP', 31, 'BR', -22.97889000, -49.87056000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q985605'),
(13535, 'Ourizona', 2022, 'PR', 31, 'BR', -23.48358000, -52.24274000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q985605'),
(13536, 'Ouriçangas', 2002, 'BA', 31, 'BR', -12.00343000, -38.65208000, '2019-10-05 22:35:19', '2020-05-01 17:22:36', 1, 'Q985605'),
(13537, 'Ouro', 2014, 'SC', 31, 'BR', -27.28699000, -51.68164000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q985605'),
(13538, 'Ouro Branco', 2019, 'RN', 31, 'BR', -6.65601000, -36.91455000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q985605'),
(13539, 'Ouro Branco', 2007, 'AL', 31, 'BR', -9.12895000, -37.37367000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q2355814'),
(13540, 'Ouro Branco', 1998, 'MG', 31, 'BR', -20.52994000, -43.69422000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q22062326'),
(13541, 'Ouro Fino', 1998, 'MG', 31, 'BR', -22.25689000, -46.37384000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q22062326'),
(13542, 'Ouro Preto', 1998, 'MG', 31, 'BR', -20.39304000, -43.64191000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q22062326'),
(13543, 'Ouro Preto do Oeste', 2013, 'RO', 31, 'BR', -10.74806000, -62.21583000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1803223'),
(13544, 'Ouro Velho', 2005, 'PB', 31, 'BR', -7.60554000, -37.13565000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1803223'),
(13545, 'Ouro Verde', 2021, 'SP', 31, 'BR', -21.52766000, -51.75119000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1803223'),
(13546, 'Ouro Verde', 2014, 'SC', 31, 'BR', -26.73257000, -52.27153000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1803223'),
(13547, 'Ouro Verde de Goiás', 2000, 'GO', 31, 'BR', -16.23604000, -49.22771000, '2019-10-05 22:35:19', '2020-05-01 17:22:36', 1, 'Q1803223'),
(13548, 'Ouro Verde de Minas', 1998, 'MG', 31, 'BR', -18.03431000, -41.29473000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1803223'),
(13549, 'Ouro Verde do Oeste', 2022, 'PR', 31, 'BR', -24.79690000, -53.93681000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1803223'),
(13550, 'Ouroeste', 2021, 'SP', 31, 'BR', -19.93760000, -50.41455000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1803223'),
(13551, 'Ourolândia', 2002, 'BA', 31, 'BR', -10.83804000, -41.02839000, '2019-10-05 22:35:19', '2020-05-01 17:22:36', 1, 'Q1803223'),
(13552, 'Ourém', 2009, 'PA', 31, 'BR', -1.55194000, -47.11444000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q1829502'),
(13553, 'Ouvidor', 2000, 'GO', 31, 'BR', -18.20107000, -47.71513000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1829502'),
(13554, 'Pacaembu', 2021, 'SP', 31, 'BR', -21.56222000, -51.26056000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q1795852'),
(13555, 'Pacajus', 2016, 'CE', 31, 'BR', -4.17250000, -38.46056000, '2019-10-05 22:35:19', '2019-10-05 22:35:19', 1, 'Q2021230'),
(13556, 'Pacajá', 2009, 'PA', 31, 'BR', -3.70172000, -50.77844000, '2019-10-05 22:35:19', '2020-05-01 17:22:37', 1, 'Q2021230'),
(13557, 'Pacatuba', 2016, 'CE', 31, 'BR', -3.97623000, -38.62922000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2026817'),
(13558, 'Pacatuba', 2003, 'SE', 31, 'BR', -10.49466000, -36.60382000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2307488'),
(13559, 'Pacoti', 2016, 'CE', 31, 'BR', -4.19575000, -38.90252000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2013474'),
(13560, 'Pacujá', 2016, 'CE', 31, 'BR', -3.96747000, -40.67686000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q2028383'),
(13561, 'Padre Bernardo', 2000, 'GO', 31, 'BR', -15.16595000, -48.28281000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q22037037'),
(13562, 'Padre Carvalho', 1998, 'MG', 31, 'BR', -16.26316000, -42.60251000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q22037037'),
(13563, 'Padre Marcos', 2008, 'PI', 31, 'BR', -7.36533000, -40.93416000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q22037037'),
(13564, 'Padre Paraíso', 1998, 'MG', 31, 'BR', -17.06100000, -41.53826000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q1755581'),
(13565, 'Paes Landim', 2008, 'PI', 31, 'BR', -7.77461000, -42.35087000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1755581'),
(13566, 'Pai Pedro', 1998, 'MG', 31, 'BR', -15.32734000, -43.19505000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1755581'),
(13567, 'Paial', 2014, 'SC', 31, 'BR', -27.20297000, -52.47551000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1755581'),
(13568, 'Paim Filho', 2001, 'RS', 31, 'BR', -27.72984000, -51.78102000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1755581'),
(13569, 'Paineiras', 1998, 'MG', 31, 'BR', -18.91817000, -45.53737000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1755581'),
(13570, 'Painel', 2014, 'SC', 31, 'BR', -27.97471000, -50.05976000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1755581'),
(13571, 'Pains', 1998, 'MG', 31, 'BR', -20.37374000, -45.73139000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1791617'),
(13572, 'Paiva', 1998, 'MG', 31, 'BR', -21.28809000, -43.41715000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q772793'),
(13573, 'Paiçandu', 2022, 'PR', 31, 'BR', -23.45750000, -52.04861000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q2005187'),
(13574, 'Pajeú do Piauí', 2008, 'PI', 31, 'BR', -8.05364000, -42.86625000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q2005187'),
(13575, 'Palestina', 2007, 'AL', 31, 'BR', -9.67504000, -37.33100000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2005187'),
(13576, 'Palestina', 2021, 'SP', 31, 'BR', -20.29911000, -49.52000000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2005187'),
(13577, 'Palestina de Goiás', 2000, 'GO', 31, 'BR', -16.72854000, -51.45851000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q2005187'),
(13578, 'Palestina do Pará', 2009, 'PA', 31, 'BR', -5.97518000, -48.38544000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q2005187'),
(13579, 'Palhano', 2016, 'CE', 31, 'BR', -4.66056000, -38.04453000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2005187'),
(13580, 'Palhoça', 2014, 'SC', 31, 'BR', -27.64528000, -48.66778000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q973513'),
(13581, 'Palma', 1998, 'MG', 31, 'BR', -21.43106000, -42.32065000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2007701'),
(13582, 'Palma Sola', 2014, 'SC', 31, 'BR', -26.39154000, -53.33171000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2007701'),
(13583, 'Palmares', 2006, 'PE', 31, 'BR', -8.68333000, -35.59167000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q547095'),
(13584, 'Palmares Paulista', 2021, 'SP', 31, 'BR', -21.10148000, -48.82032000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q547095'),
(13585, 'Palmares do Sul', 2001, 'RS', 31, 'BR', -30.25778000, -50.50972000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1750076'),
(13586, 'Palmas', 2022, 'PR', 31, 'BR', -26.48417000, -51.99056000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q928397'),
(13587, 'Palmas', 2020, 'TO', 31, 'BR', -10.16745000, -48.32766000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q178993'),
(13588, 'Palmas de Monte Alto', 2002, 'BA', 31, 'BR', -14.26722000, -43.16194000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1793599'),
(13589, 'Palmeira', 2022, 'PR', 31, 'BR', -25.42944000, -50.00639000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2039438'),
(13590, 'Palmeira', 2014, 'SC', 31, 'BR', -27.55649000, -50.16497000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2039438'),
(13591, 'Palmeira d\'Oeste', 2021, 'SP', 31, 'BR', -20.45046000, -50.75181000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1645095'),
(13592, 'Palmeira das Missões', 2001, 'RS', 31, 'BR', -27.89944000, -53.31361000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q1750240'),
(13593, 'Palmeira do Piauí', 2008, 'PI', 31, 'BR', -8.51549000, -44.43642000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q1750240'),
(13594, 'Palmeira dos Índios', 2007, 'AL', 31, 'BR', -9.40902000, -36.60651000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q948268'),
(13595, 'Palmeirais', 2008, 'PI', 31, 'BR', -5.78548000, -43.02434000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2064013'),
(13596, 'Palmeirante', 2020, 'TO', 31, 'BR', -7.86937000, -48.17585000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2064013'),
(13597, 'Palmeiras', 2002, 'BA', 31, 'BR', -12.51485000, -41.57707000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2064013'),
(13598, 'Palmeiras de Goiás', 2000, 'GO', 31, 'BR', -16.80500000, -49.92583000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q985972'),
(13599, 'Palmeiras do Tocantins', 2020, 'TO', 31, 'BR', -6.61304000, -47.66259000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1801156'),
(13600, 'Palmeirina', 2006, 'PE', 31, 'BR', -9.02961000, -36.24198000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1801156'),
(13601, 'Palmeirândia', 2015, 'MA', 31, 'BR', -2.65916000, -45.06713000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q1801156'),
(13602, 'Palmeirópolis', 2020, 'TO', 31, 'BR', -13.06026000, -48.38866000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q1801156'),
(13603, 'Palmelo', 2000, 'GO', 31, 'BR', -17.31937000, -48.39429000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1801156'),
(13604, 'Palminópolis', 2000, 'GO', 31, 'BR', -16.83157000, -50.24267000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q1801156'),
(13605, 'Palmital', 2021, 'SP', 31, 'BR', -22.78889000, -50.21750000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760365'),
(13606, 'Palmital', 2022, 'PR', 31, 'BR', -24.84260000, -52.25370000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760365'),
(13607, 'Palmitinho', 2001, 'RS', 31, 'BR', -27.32268000, -53.59220000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760365'),
(13608, 'Palmitos', 2014, 'SC', 31, 'BR', -27.08066000, -53.17812000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760365'),
(13609, 'Palmácia', 2016, 'CE', 31, 'BR', -4.12964000, -38.84258000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q1760365'),
(13610, 'Palmópolis', 1998, 'MG', 31, 'BR', -16.77843000, -40.37475000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q1760365'),
(13611, 'Palotina', 2022, 'PR', 31, 'BR', -24.28389000, -53.84000000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q949670'),
(13612, 'Panambi', 2001, 'RS', 31, 'BR', -28.29250000, -53.50167000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q348206'),
(13613, 'Panamá', 2000, 'GO', 31, 'BR', -18.20132000, -49.38890000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q1004891'),
(13614, 'Pancas', 2018, 'ES', 31, 'BR', -19.22500000, -40.85139000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q505326'),
(13615, 'Panelas', 2006, 'PE', 31, 'BR', -8.66651000, -36.04783000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q505326'),
(13616, 'Panorama', 2021, 'SP', 31, 'BR', -21.35639000, -51.85972000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q955544'),
(13617, 'Pantanal', 2014, 'SC', 31, 'BR', -27.60985000, -48.51648000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q955544'),
(13618, 'Pantano Grande', 2001, 'RS', 31, 'BR', -30.25817000, -52.34168000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q955544'),
(13619, 'Pantano do Sul', 2014, 'SC', 31, 'BR', -27.77972000, -48.50861000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q955544'),
(13620, 'Papagaios', 1998, 'MG', 31, 'BR', -19.39686000, -44.69502000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1789565'),
(13621, 'Papanduva', 2014, 'SC', 31, 'BR', -26.47628000, -50.17467000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1789565'),
(13622, 'Paquetá', 2008, 'PI', 31, 'BR', -7.10746000, -41.64719000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q2065325'),
(13623, 'Paracambi', 1997, 'RJ', 31, 'BR', -22.60829000, -43.70840000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q22037233'),
(13624, 'Paracatu', 1998, 'MG', 31, 'BR', -17.13285000, -46.88258000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1755739'),
(13625, 'Paracuru', 2016, 'CE', 31, 'BR', -3.41000000, -39.03056000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q542347'),
(13626, 'Paragominas', 2009, 'PA', 31, 'BR', -2.96667000, -47.48333000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q22017084'),
(13627, 'Paraguaçu', 1998, 'MG', 31, 'BR', -21.58081000, -45.74296000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q283682'),
(13628, 'Paraguaçu Paulista', 2021, 'SP', 31, 'BR', -22.41278000, -50.57583000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q1760760'),
(13629, 'Paraibano', 2015, 'MA', 31, 'BR', -6.40952000, -43.84714000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2261140'),
(13630, 'Paraibuna', 2021, 'SP', 31, 'BR', -23.48123000, -45.63778000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2261140'),
(13631, 'Paraipaba', 2016, 'CE', 31, 'BR', -3.43944000, -39.14833000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2013436'),
(13632, 'Paraisópolis', 1998, 'MG', 31, 'BR', -22.57668000, -45.84583000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q522717'),
(13633, 'Parambu', 2016, 'CE', 31, 'BR', -6.25405000, -40.60876000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q770577'),
(13634, 'Paramirim', 2002, 'BA', 31, 'BR', -13.44250000, -42.23889000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1793379'),
(13635, 'Paramoti', 2016, 'CE', 31, 'BR', -4.14739000, -39.36219000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1793379'),
(13636, 'Paranacity', 2022, 'PR', 31, 'BR', -22.84648000, -52.13457000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1793379'),
(13637, 'Paranaguá', 2022, 'PR', 31, 'BR', -25.51626000, -48.52537000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q503852'),
(13638, 'Paranaiguara', 2000, 'GO', 31, 'BR', -18.77492000, -50.61841000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q503852'),
(13639, 'Paranapanema', 2021, 'SP', 31, 'BR', -23.38630000, -48.72441000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760996'),
(13640, 'Paranapoema', 2022, 'PR', 31, 'BR', -22.65624000, -52.07598000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760996'),
(13641, 'Paranapuã', 2021, 'SP', 31, 'BR', -20.05940000, -50.59676000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q1760996'),
(13642, 'Paranatama', 2006, 'PE', 31, 'BR', -8.89811000, -36.68199000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760996'),
(13643, 'Paranatinga', 2011, 'MT', 31, 'BR', -13.34326000, -54.01552000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1760996'),
(13644, 'Paranavaí', 2022, 'PR', 31, 'BR', -23.07306000, -52.46528000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q1773355'),
(13645, 'Paranaíba', 2010, 'MS', 31, 'BR', -19.67722000, -51.19083000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q925758'),
(13646, 'Paranaíta', 2011, 'MT', 31, 'BR', -9.56371000, -56.76536000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q925758'),
(13647, 'Paranhos', 2010, 'MS', 31, 'BR', -23.89278000, -55.43111000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q522494'),
(13648, 'Paraná', 2019, 'RN', 31, 'BR', -6.44702000, -38.29980000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q522494'),
(13649, 'Paranã', 2020, 'TO', 31, 'BR', -12.73875000, -47.94338000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q522494'),
(13650, 'Paraopeba', 1998, 'MG', 31, 'BR', -19.27228000, -44.45274000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q522494'),
(13651, 'Parapuã', 2021, 'SP', 31, 'BR', -21.84482000, -50.83410000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q522494'),
(13652, 'Parari', 2005, 'PB', 31, 'BR', -7.31136000, -36.67699000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q2103106'),
(13653, 'Paratinga', 2002, 'BA', 31, 'BR', -12.69056000, -43.18417000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1793885'),
(13654, 'Paraty', 1997, 'RJ', 31, 'BR', -23.21778000, -44.71306000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q22037295'),
(13655, 'Parauapebas', 2009, 'PA', 31, 'BR', -6.18558000, -50.55474000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1806100'),
(13656, 'Parazinho', 2019, 'RN', 31, 'BR', -5.27717000, -35.93239000, '2019-10-05 22:35:20', '2019-10-05 22:35:20', 1, 'Q1806100'),
(13657, 'Paraí', 2001, 'RS', 31, 'BR', -28.59575000, -51.79731000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q1757405'),
(13658, 'Paraíba do Sul', 1997, 'RJ', 31, 'BR', -22.15847000, -43.29321000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q304947'),
(13659, 'Paraíso', 2021, 'SP', 31, 'BR', -21.01639000, -48.77361000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q1760478'),
(13660, 'Paraíso', 2014, 'SC', 31, 'BR', -26.66872000, -53.68550000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q1760478'),
(13661, 'Paraíso das Águas', 2010, 'MS', 31, 'BR', -19.01750000, -53.01222000, '2019-10-05 22:35:20', '2020-05-01 17:22:36', 1, 'Q22037256'),
(13662, 'Paraíso do Norte', 2022, 'PR', 31, 'BR', -23.25410000, -52.63634000, '2019-10-05 22:35:20', '2020-05-01 17:22:37', 1, 'Q22037256'),
(13663, 'Paraíso do Sul', 2001, 'RS', 31, 'BR', -29.72404000, -53.11162000, '2019-10-05 22:35:20', '2020-05-01 17:22:38', 1, 'Q972360'),
(13664, 'Paraíso do Tocantins', 2020, 'TO', 31, 'BR', -10.23212000, -48.88032000, '2019-10-05 22:35:21', '2020-05-01 17:22:38', 1, 'Q972360'),
(13665, 'Paraú', 2019, 'RN', 31, 'BR', -5.73216000, -37.13664000, '2019-10-05 22:35:21', '2020-05-01 17:22:38', 1, 'Q972360'),
(13666, 'Paraúna', 2000, 'GO', 31, 'BR', -16.94778000, -50.44861000, '2019-10-05 22:35:21', '2020-05-01 17:22:36', 1, 'Q913766'),
(13667, 'Pardinho', 2021, 'SP', 31, 'BR', -23.08987000, -48.38420000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q913766'),
(13668, 'Pareci Novo', 2001, 'RS', 31, 'BR', -29.61153000, -51.42691000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q913766'),
(13669, 'Parecis', 2013, 'RO', 31, 'BR', -12.28659000, -61.31662000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q913766'),
(13670, 'Parelhas', 2019, 'RN', 31, 'BR', -6.68778000, -36.65750000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1786896'),
(13671, 'Pariconha', 2007, 'AL', 31, 'BR', -9.22039000, -38.01670000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1786896'),
(13672, 'Parintins', 2004, 'AM', 31, 'BR', -2.62833000, -56.73583000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q926713'),
(13673, 'Paripiranga', 2002, 'BA', 31, 'BR', -10.68750000, -37.86167000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1761881');

