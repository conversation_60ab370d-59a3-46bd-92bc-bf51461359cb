INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(71502, 'La Tigrilla', 3451, 'CHP', 142, 'MX', 15.98769000, -92.85220000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293791'),
(71503, 'La Tinaja', 3464, 'VER', 142, 'MX', 18.76082000, -96.46283000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20293791'),
(71504, 'La Tinaja', 3469, 'GUA', 142, 'MX', 20.49028000, -101.21556000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20208282'),
(71505, 'La Toma', 3464, 'VER', 142, 'MX', 19.45278000, -97.19361000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137470'),
(71506, '<PERSON> Tortuga', 3455, 'QUE', 142, 'MX', 20.56703000, -99.92317000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137470'),
(71507, 'La Trinidad', 3470, 'HID', 142, 'MX', 19.95975000, -98.70942000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137470'),
(71508, 'La Trinidad', 3455, 'QUE', 142, 'MX', 20.51560000, -99.96283000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137470'),
(71509, 'La Trinidad', 3449, 'SIN', 142, 'MX', 25.73367000, -108.48409000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20294266'),
(71510, 'La Trinidad', 3450, 'MEX', 142, 'MX', 18.96639000, -99.58083000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137926'),
(71511, 'La Trinidad', 3469, 'GUA', 142, 'MX', 20.56333000, -100.78361000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137782'),
(71512, 'La Trinidad Chautenco', 3476, 'PUE', 142, 'MX', 19.09000000, -98.26389000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20230667'),
(71513, 'La Trinidad Chimalpa', 3458, 'TLA', 142, 'MX', 19.32944000, -98.24528000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137732'),
(71514, 'La Trinidad Tenexyecac', 3458, 'TLA', 142, 'MX', 19.33596000, -98.31476000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137732'),
(71515, 'La Trinidad Tianguismanalco', 3476, 'PUE', 142, 'MX', 18.89417000, -98.03250000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20137747'),
(71516, 'La Trinitaria', 3451, 'CHP', 142, 'MX', 16.11859000, -92.05201000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q27769368'),
(71517, 'La Unidad Huitzizilapan', 3465, 'MOR', 142, 'MX', 19.42223000, -99.39771000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q27769368'),
(71518, 'La Unión', 3454, 'TAB', 142, 'MX', 17.84839000, -92.49628000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q27769368'),
(71519, 'La Unión', 3467, 'ROO', 142, 'MX', 17.89918000, -88.88092000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q27769368'),
(71520, 'La Unión', 3476, 'PUE', 142, 'MX', 20.26181000, -97.87135000, '2019-10-05 23:08:54', '2020-05-01 17:23:01', 1, 'Q27769368'),
(71521, 'La Unión', 3468, 'SON', 142, 'MX', 26.81961000, -109.60831000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q20255971'),
(71522, 'La Unión', 3459, 'GRO', 142, 'MX', 17.98561000, -101.80655000, '2019-10-05 23:08:54', '2020-05-01 17:23:00', 1, 'Q5398211'),
(71523, 'La Unión', 3464, 'VER', 142, 'MX', 20.89222000, -97.66528000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q20138201'),
(71524, 'La Unión Ejido Mexcaltepec', 3476, 'PUE', 142, 'MX', 19.60806000, -97.71056000, '2019-10-05 23:08:54', '2020-05-01 17:23:01', 1, 'Q20230673'),
(71525, 'La Unión Paso Largo', 3464, 'VER', 142, 'MX', 20.14224000, -96.99995000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q20138180'),
(71526, 'La Valla', 3455, 'QUE', 142, 'MX', 20.49966000, -100.02689000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20138180'),
(71527, 'La Venta', 3448, 'OAX', 142, 'MX', 16.57109000, -94.81773000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20138180'),
(71528, 'La Ventilla', 3461, 'SLP', 142, 'MX', 21.77389000, -101.03028000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20256098'),
(71529, 'La Ventosa', 3448, 'OAX', 142, 'MX', 16.55188000, -94.94762000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20256098'),
(71530, 'La Versolilla', 3455, 'QUE', 142, 'MX', 20.87306000, -100.37889000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20239873'),
(71531, 'La Victoria', 3454, 'TAB', 142, 'MX', 18.59102000, -92.63284000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20239873'),
(71532, 'La Victoria', 3464, 'VER', 142, 'MX', 18.37486000, -95.12213000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20239873'),
(71533, 'La Victoria', 3462, 'ZAC', 142, 'MX', 22.25701000, -101.62957000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20239873'),
(71534, 'La Victoria', 3468, 'SON', 142, 'MX', 29.12215000, -110.89080000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20239873'),
(71535, 'La Victoria (La Peñita)', 3464, 'VER', 142, 'MX', 20.93694000, -97.37250000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q20139095'),
(71536, 'La Y', 3450, 'MEX', 142, 'MX', 19.39806000, -99.58972000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20294598'),
(71537, 'La Yesca', 3477, 'NAY', 142, 'MX', 21.31871000, -104.01161000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20139688'),
(71538, 'La Zacatecana', 3462, 'ZAC', 142, 'MX', 22.73053000, -102.47462000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20139688'),
(71539, 'Labor de Peralta', 3469, 'GUA', 142, 'MX', 20.40885000, -101.40901000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20139688'),
(71540, 'Lacandón', 3451, 'CHP', 142, 'MX', 17.02141000, -91.59523000, '2019-10-05 23:08:54', '2020-05-01 17:22:59', 1, 'Q20216630'),
(71541, 'Lacapan Camallagne', 3476, 'PUE', 142, 'MX', 20.12862000, -97.65343000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20230689'),
(71542, 'Ladrillera (Entronque Pesquería)', 3452, 'NLE', 142, 'MX', 25.80306000, -100.10111000, '2019-10-05 23:08:54', '2020-05-01 17:23:01', 1, 'Q20234582'),
(71543, 'Ladrilleras de Ocoro', 3449, 'SIN', 142, 'MX', 25.59806000, -108.44944000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20294674'),
(71544, 'Ladrilleras del Refugio', 3469, 'GUA', 142, 'MX', 21.08472000, -101.55361000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20139999'),
(71545, 'Lafragua', 3476, 'PUE', 142, 'MX', 19.30684000, -97.29748000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q5479464'),
(71546, 'Lagartera 1ra. Sección', 3454, 'TAB', 142, 'MX', 18.06277000, -92.88563000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q20228135'),
(71547, 'Laguna Chica (Pueblo Nuevo)', 3464, 'VER', 142, 'MX', 18.54306000, -96.73306000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20227219'),
(71548, 'Laguna Larga', 3469, 'GUA', 142, 'MX', 20.54131000, -101.48451000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20227219'),
(71549, 'Laguna Larga de Cortés', 3469, 'GUA', 142, 'MX', 20.39270000, -101.94062000, '2019-10-05 23:08:54', '2020-05-01 17:23:00', 1, 'Q20227219'),
(71550, 'Laguna Seca', 3462, 'ZAC', 142, 'MX', 23.06180000, -102.49558000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20227219'),
(71551, 'Laguna de Farfán', 3464, 'VER', 142, 'MX', 19.80327000, -96.64386000, '2019-10-05 23:08:54', '2020-05-01 17:23:02', 1, 'Q20227219'),
(71552, 'Laguna de Guadalupe', 3469, 'GUA', 142, 'MX', 21.80046000, -101.35689000, '2019-10-05 23:08:54', '2019-10-05 23:08:54', 1, 'Q20227219'),
(71553, 'Laguna de San Vicente', 3461, 'SLP', 142, 'MX', 21.94603000, -100.85926000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20227219'),
(71554, 'Laguna de Santa Rita', 3461, 'SLP', 142, 'MX', 22.11449000, -100.84713000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20227219'),
(71555, 'Laguna de Tabernillas (El Resbaloso)', 3450, 'MEX', 142, 'MX', 19.46139000, -99.87833000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20140169'),
(71556, 'Laguna de Vaquerías', 3455, 'QUE', 142, 'MX', 20.35392000, -100.13515000, '2019-10-05 23:08:55', '2020-05-01 17:23:02', 1, 'Q20140169'),
(71557, 'Laguna del Cofre', 3451, 'CHP', 142, 'MX', 15.62751000, -92.64143000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20140169'),
(71558, 'Laguna del Mante', 3461, 'SLP', 142, 'MX', 22.21954000, -98.98758000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20140169'),
(71559, 'Laguna del Rey (Químicas del Rey)', 3471, 'COA', 142, 'MX', 27.03056000, -103.36667000, '2019-10-05 23:08:55', '2020-05-01 17:23:00', 1, 'Q474420'),
(71560, 'Laguneta', 3464, 'VER', 142, 'MX', 18.34298000, -95.15181000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q474420'),
(71561, 'Lagunilla', 3470, 'HID', 142, 'MX', 20.34306000, -99.02737000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q474420'),
(71562, 'Lagunilla', 3458, 'TLA', 142, 'MX', 19.63607000, -98.27434000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20224606'),
(71563, 'Lagunillas', 3459, 'GRO', 142, 'MX', 17.82438000, -101.71074000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20224606'),
(71564, 'Lagunillas', 3455, 'QUE', 142, 'MX', 20.45876000, -100.29500000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20224606'),
(71565, 'Lagunillas', 3474, 'MIC', 142, 'MX', 19.57070000, -101.41941000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q6029700'),
(71566, 'Lamadrid', 3471, 'COA', 142, 'MX', 27.05087000, -101.79552000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q6480770'),
(71567, 'Lampazos de Naranjo', 3452, 'NLE', 142, 'MX', 27.02549000, -100.50528000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q2991558'),
(71568, 'Landa de Matamoros', 3455, 'QUE', 142, 'MX', 21.18484000, -99.31985000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q2652446'),
(71569, 'Landero y Coss', 3464, 'VER', 142, 'MX', 19.73935000, -96.85009000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q3846402'),
(71570, 'Landín', 3469, 'GUA', 142, 'MX', 20.75392000, -100.90592000, '2019-10-05 23:08:55', '2020-05-01 17:23:00', 1, 'Q3846402'),
(71571, 'Larráinzar', 3451, 'CHP', 142, 'MX', 16.88392000, -92.71329000, '2019-10-05 23:08:55', '2020-05-01 17:22:59', 1, 'Q1173637'),
(71572, 'Las Aguamitas', 3449, 'SIN', 142, 'MX', 24.57556000, -107.80056000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20140907'),
(71573, 'Las Amapolas', 3464, 'VER', 142, 'MX', 19.15194000, -96.19778000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20141092'),
(71574, 'Las Amilpas', 3448, 'OAX', 142, 'MX', 16.36667000, -94.61667000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20294885'),
(71575, 'Las Arenitas', 3449, 'SIN', 142, 'MX', 24.37245000, -107.53612000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20294885'),
(71576, 'Las Armas', 3461, 'SLP', 142, 'MX', 21.69685000, -98.96752000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20294885'),
(71577, 'Las Barrillas', 3464, 'VER', 142, 'MX', 18.18617000, -94.59542000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20294885'),
(71578, 'Las Bocas', 3468, 'SON', 142, 'MX', 26.58821000, -109.33622000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q6492075'),
(71579, 'Las Brisas', 3451, 'CHP', 142, 'MX', 15.51681000, -93.11763000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q6492075'),
(71580, 'Las Brisas', 3449, 'SIN', 142, 'MX', 25.49022000, -108.24587000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q6492075'),
(71581, 'Las Brisas', 3457, 'BCN', 142, 'MX', 30.96056000, -116.16444000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20141653'),
(71582, 'Las Catarinas', 3462, 'ZAC', 142, 'MX', 23.09115000, -102.61597000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20221569'),
(71583, 'Las Cañadas', 3474, 'MIC', 142, 'MX', 20.29750000, -101.97806000, '2019-10-05 23:08:55', '2020-05-01 17:23:01', 1, 'Q20142051'),
(71584, 'Las Cañas', 3469, 'GUA', 142, 'MX', 20.29586000, -101.41823000, '2019-10-05 23:08:55', '2020-05-01 17:23:00', 1, 'Q20142051'),
(71585, 'Las Cañas', 3474, 'MIC', 142, 'MX', 18.56583000, -101.97611000, '2019-10-05 23:08:55', '2020-05-01 17:23:01', 1, 'Q20240451'),
(71586, 'Las Chicharras', 3451, 'CHP', 142, 'MX', 15.67089000, -92.21070000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20240451'),
(71587, 'Las Choapas', 3464, 'VER', 142, 'MX', 17.91177000, -94.09646000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q3847143'),
(71588, 'Las Cieneguitas', 3474, 'MIC', 142, 'MX', 20.24884000, -102.24505000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q3847143'),
(71589, 'Las Colonias de Hidalgo', 3476, 'PUE', 142, 'MX', 20.19611000, -97.97667000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20142798'),
(71590, 'Las Coloradas', 3466, 'YUC', 142, 'MX', 21.60767000, -87.99045000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20142798'),
(71591, 'Las Compuertas', 3459, 'GRO', 142, 'MX', 17.19559000, -100.00840000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20142798'),
(71592, 'Las Compuertas', 3449, 'SIN', 142, 'MX', 25.84202000, -109.01980000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20142798'),
(71593, 'Las Cruces', 3459, 'GRO', 142, 'MX', 16.97304000, -99.44515000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20142798'),
(71594, 'Las Cruces', 3447, 'CHH', 142, 'MX', 29.43278000, -107.38958000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20142798'),
(71595, 'Las Cuevas', 3453, 'DUR', 142, 'MX', 25.49815000, -103.54950000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20219978'),
(71596, 'Las Delicias', 3451, 'CHP', 142, 'MX', 15.96932000, -91.86315000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20219978'),
(71597, 'Las Delicias', 3457, 'BCN', 142, 'MX', 32.40889000, -116.94361000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q1024157'),
(71598, 'Las Esperanzas (El Ranchito)', 3462, 'ZAC', 142, 'MX', 23.84972000, -103.13111000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20221586'),
(71599, 'Las Flechas', 3451, 'CHP', 142, 'MX', 16.70028000, -93.02639000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20216801'),
(71600, 'Las Grullas Margen Derecha', 3449, 'SIN', 142, 'MX', 25.87770000, -109.33773000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20216801'),
(71601, 'Las Grullas Margen Izquierda', 3449, 'SIN', 142, 'MX', 25.85306000, -109.32917000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20216801'),
(71602, 'Las Higueras', 3464, 'VER', 142, 'MX', 20.03693000, -96.62199000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20216801'),
(71603, 'Las Higuerillas', 3463, 'TAM', 142, 'MX', 25.26222000, -97.43611000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20225935'),
(71604, 'Las Huertas', 3450, 'MEX', 142, 'MX', 19.97222000, -99.55611000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20239058'),
(71605, 'Las Huertas Tercera Sección', 3469, 'GUA', 142, 'MX', 20.65278000, -101.39306000, '2019-10-05 23:08:55', '2020-05-01 17:23:00', 1, 'Q20295595'),
(71606, 'Las Jarretaderas', 3477, 'NAY', 142, 'MX', 20.69250000, -105.27389000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20145291'),
(71607, 'Las Lajas', 3476, 'PUE', 142, 'MX', 19.99831000, -98.07332000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20145291'),
(71608, 'Las Lajitas', 3455, 'QUE', 142, 'MX', 20.80360000, -100.40107000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20145746'),
(71609, 'Las Letras', 3474, 'MIC', 142, 'MX', 20.23083000, -101.43361000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20145836'),
(71610, 'Las Liebres', 3469, 'GUA', 142, 'MX', 20.83532000, -101.47062000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20145836'),
(71611, 'Las Lomas', 3476, 'PUE', 142, 'MX', 19.82390000, -97.61227000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20145836'),
(71612, 'Las Lomas', 3464, 'VER', 142, 'MX', 20.26067000, -97.61528000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20227388'),
(71613, 'Las Lomas', 3459, 'GRO', 142, 'MX', 16.98194000, -100.09500000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20146039'),
(71614, 'Las Lomas de Tacamichapan', 3464, 'VER', 142, 'MX', 17.86611000, -94.70833000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20227387'),
(71615, 'Las Manzanas', 3450, 'MEX', 142, 'MX', 19.95069000, -99.56109000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20227387'),
(71616, 'Las Maravillas', 3451, 'CHP', 142, 'MX', 16.95951000, -93.32012000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20227387'),
(71617, 'Las Margaritas', 3451, 'CHP', 142, 'MX', 16.31265000, -91.98107000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q1962619'),
(71618, 'Las Margaritas', 3448, 'OAX', 142, 'MX', 18.25501000, -96.28421000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q1962619'),
(71619, 'Las Margaritas', 3460, 'BCS', 142, 'MX', 27.61833000, -113.45361000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20245590'),
(71620, 'Las Masas (La Luz Masas)', 3469, 'GUA', 142, 'MX', 20.59917000, -101.54278000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q20208457'),
(71621, 'Las Mercedes [Agropecuaria]', 3468, 'SON', 142, 'MX', 29.36389000, -110.95167000, '2019-10-05 23:08:55', '2019-10-05 23:08:55', 1, 'Q56554711'),
(71622, 'Las Mesas', 3459, 'GRO', 142, 'MX', 17.00794000, -99.45786000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q56554711'),
(71623, 'Las Mieleras', 3471, 'COA', 142, 'MX', 25.39948000, -103.26740000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q56554711'),
(71624, 'Las Minas', 3464, 'VER', 142, 'MX', 19.70449000, -97.14699000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q5970594'),
(71625, 'Las Moras', 3449, 'SIN', 142, 'MX', 25.65745000, -108.45807000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q5970594'),
(71626, 'Las Nieves', 3476, 'PUE', 142, 'MX', 18.18361000, -98.04611000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20147317'),
(71627, 'Las Norias de Ojocaliente', 3456, 'AGU', 142, 'MX', 21.88944000, -102.21738000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20147317'),
(71628, 'Las Ollas', 3451, 'CHP', 142, 'MX', 16.78146000, -92.55278000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20147317'),
(71629, 'Las Palmas', 3461, 'SLP', 142, 'MX', 22.03339000, -98.87407000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20147317'),
(71630, 'Las Palmas', 3460, 'BCS', 142, 'MX', 22.93672000, -109.94235000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20147663'),
(71631, 'Las Peñas', 3450, 'MEX', 142, 'MX', 19.44972000, -99.99944000, '2019-10-05 23:08:56', '2020-05-01 17:23:00', 1, 'Q20296495'),
(71632, 'Las Pilas', 3462, 'ZAC', 142, 'MX', 22.84000000, -102.61278000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20296495'),
(71633, 'Las Pintas', 3450, 'MEX', 142, 'MX', 19.89667000, -98.88694000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20239094'),
(71634, 'Las Plazuelas', 3459, 'GRO', 142, 'MX', 16.87417000, -99.78556000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20208238'),
(71635, 'Las Quemazones', 3449, 'SIN', 142, 'MX', 25.68583000, -108.45243000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20149230'),
(71636, 'Las Ranas', 3450, 'MEX', 142, 'MX', 20.20860000, -101.47810000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20149230'),
(71637, 'Las Rosas', 3451, 'CHP', 142, 'MX', 16.36574000, -92.37040000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q49977782'),
(71638, 'Las Rosas', 3450, 'MEX', 142, 'MX', 19.54631000, -100.15986000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20296680'),
(71639, 'Las Sabanetas', 3464, 'VER', 142, 'MX', 18.12661000, -95.82265000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20296680'),
(71640, 'Las Sabinas', 3464, 'VER', 142, 'MX', 21.31806000, -97.88083000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20227482'),
(71641, 'Las Taponas', 3455, 'QUE', 142, 'MX', 20.41747000, -100.38421000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20227482'),
(71642, 'Las Tazas', 3451, 'CHP', 142, 'MX', 16.75722000, -91.61500000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20296745'),
(71643, 'Las Torres', 3452, 'NLE', 142, 'MX', 25.95167000, -100.31806000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20263562'),
(71644, 'Las Torres de Guadalupe', 3452, 'NLE', 142, 'MX', 25.80611000, -100.62250000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20234688'),
(71645, 'Las Trancas', 3464, 'VER', 142, 'MX', 19.50444000, -96.86444000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20227492'),
(71646, 'Las Trojas', 3469, 'GUA', 142, 'MX', 20.71111000, -100.78083000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20208516'),
(71647, 'Las Tunas', 3459, 'GRO', 142, 'MX', 17.11972000, -100.48311000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20208516'),
(71648, 'Las Varas', 3477, 'NAY', 142, 'MX', 21.18021000, -105.13595000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q6492494'),
(71649, 'Las Varas', 3447, 'CHH', 142, 'MX', 28.15659000, -105.34058000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q6492494'),
(71650, 'Las Varas (Estación Babícora)', 3447, 'CHH', 142, 'MX', 29.48083000, -108.02556000, '2019-10-05 23:08:56', '2020-05-01 17:23:00', 1, 'Q20215019'),
(71651, 'Las Veredas', 3460, 'BCS', 142, 'MX', 23.15028000, -109.70611000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20245630'),
(71652, 'Las Vigas', 3459, 'GRO', 142, 'MX', 16.76144000, -99.22942000, '2019-10-05 23:08:56', '2019-10-05 23:08:56', 1, 'Q20208277'),
(71653, 'Las Vigas de Ramírez', 3464, 'VER', 142, 'MX', 19.63722000, -97.09821000, '2019-10-05 23:08:57', '2020-05-01 17:23:02', 1, 'Q20227510'),
(71654, 'Las Yerbitas [Aserradero]', 3447, 'CHH', 142, 'MX', 26.08025000, -106.77969000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20215030'),
(71655, 'Las Ánimas', 3459, 'GRO', 142, 'MX', 16.97284000, -99.31964000, '2019-10-05 23:08:57', '2020-05-01 17:23:00', 1, 'Q20215030'),
(71656, 'Las Ánimas', 3456, 'AGU', 142, 'MX', 22.09057000, -102.26684000, '2019-10-05 23:08:57', '2020-05-01 17:22:59', 1, 'Q20215030'),
(71657, 'Las Ánimas', 3469, 'GUA', 142, 'MX', 20.39709000, -101.79964000, '2019-10-05 23:08:57', '2020-05-01 17:23:00', 1, 'Q20215030'),
(71658, 'Leacaman', 3476, 'PUE', 142, 'MX', 20.09861000, -97.64306000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20230782'),
(71659, 'Leona Vicario', 3467, 'ROO', 142, 'MX', 20.98970000, -87.20224000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q4258726'),
(71660, 'Leona Vicario', 3466, 'YUC', 142, 'MX', 20.90944000, -89.60111000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q5973107'),
(71661, 'Lepan', 3466, 'YUC', 142, 'MX', 20.71243000, -89.49719000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q5973107'),
(71662, 'Lerdo de Tejada', 3464, 'VER', 142, 'MX', 18.62936000, -95.51968000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20227551'),
(71663, 'Lerma', 3475, 'CAM', 142, 'MX', 19.80404000, -90.60088000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q22941041'),
(71664, 'Lerma de Villada', 3450, 'MEX', 142, 'MX', 19.28881000, -99.51163000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q1962179'),
(71665, 'Leyva Solano', 3449, 'SIN', 142, 'MX', 25.66163000, -108.63700000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20259991'),
(71666, 'León', 3469, 'GUA', 142, 'MX', 21.09309000, -101.64501000, '2019-10-05 23:08:57', '2020-05-01 17:23:00', 1, 'Q9022143'),
(71667, 'León Guzmán', 3453, 'DUR', 142, 'MX', 25.49986000, -103.65924000, '2019-10-05 23:08:57', '2020-05-01 17:23:00', 1, 'Q9022143'),
(71668, 'León de los Aldama', 3469, 'GUA', 142, 'MX', 21.12908000, -101.67374000, '2019-10-05 23:08:57', '2020-05-01 17:23:00', 1, 'Q189128'),
(71669, 'Libertad', 3454, 'TAB', 142, 'MX', 17.41027000, -92.71502000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20264569'),
(71670, 'Libertad Melchor Ocampo', 3451, 'CHP', 142, 'MX', 16.37389000, -93.47500000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20282185'),
(71671, 'Libertad Ventanas', 3451, 'CHP', 142, 'MX', 15.53954000, -92.27576000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20282185'),
(71672, 'Libertad Álvaro Obregón', 3476, 'PUE', 142, 'MX', 19.13452000, -97.76728000, '2019-10-05 23:08:57', '2020-05-01 17:23:01', 1, 'Q20282185'),
(71673, 'Libre Unión', 3466, 'YUC', 142, 'MX', 20.70635000, -88.80959000, '2019-10-05 23:08:57', '2020-05-01 17:23:03', 1, 'Q20282185'),
(71674, 'Libres', 3476, 'PUE', 142, 'MX', 19.46574000, -97.68737000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20262455'),
(71675, 'Licenciado Benito Juárez (Campo Gobierno)', 3449, 'SIN', 142, 'MX', 24.65667000, -107.54500000, '2019-10-05 23:08:57', '2020-05-01 17:23:02', 1, 'Q20282194'),
(71676, 'Licenciado Gustavo Díaz Ordaz', 3457, 'BCN', 142, 'MX', 30.97506000, -116.15565000, '2019-10-05 23:08:57', '2020-05-01 17:22:59', 1, 'Q20282194'),
(71677, 'Licenciado Gustavo Díaz Ordaz (18 de Marzo)', 3475, 'CAM', 142, 'MX', 18.63222000, -91.00278000, '2019-10-05 23:08:57', '2020-05-01 17:22:59', 1, 'Q20258442'),
(71678, 'Limones', 3464, 'VER', 142, 'MX', 19.33820000, -96.92336000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20258442'),
(71679, 'Linares', 3452, 'NLE', 142, 'MX', 24.85798000, -99.56768000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q991413'),
(71680, 'Lindavista', 3464, 'VER', 142, 'MX', 18.08899000, -95.77597000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q991413'),
(71681, 'Lindavista', 3470, 'HID', 142, 'MX', 20.02611000, -98.79778000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20243057'),
(71682, 'Lipuntahuaca', 3476, 'PUE', 142, 'MX', 20.09119000, -97.63026000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20230801'),
(71683, 'Llano Grande', 3459, 'GRO', 142, 'MX', 16.94010000, -99.43353000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20208349'),
(71684, 'Llano Grande', 3450, 'MEX', 142, 'MX', 18.82733000, -99.76774000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20208349'),
(71685, 'Llano Grande', 3464, 'VER', 142, 'MX', 19.37035000, -96.88002000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20208349'),
(71686, 'Llano Grande', 3449, 'SIN', 142, 'MX', 25.75187000, -108.08636000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20208349'),
(71687, 'Llano Grande', 3453, 'DUR', 142, 'MX', 23.86367000, -105.20558000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20208349'),
(71688, 'Llano Largo', 3470, 'HID', 142, 'MX', 20.30111000, -99.76556000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20282574'),
(71689, 'Llano Suchiapa', 3448, 'OAX', 142, 'MX', 16.86611000, -95.05389000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20265400'),
(71690, 'Llano de Agua', 3448, 'OAX', 142, 'MX', 18.10611000, -96.81083000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20265284'),
(71691, 'Llano de Enmedio', 3464, 'VER', 142, 'MX', 20.78328000, -98.01364000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20270009'),
(71692, 'Llano de Zacapexco', 3450, 'MEX', 142, 'MX', 19.70405000, -99.48507000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20270009'),
(71693, 'Llano de la Lima', 3451, 'CHP', 142, 'MX', 14.86596000, -92.29714000, '2019-10-05 23:08:57', '2019-10-05 23:08:57', 1, 'Q20282427'),
(71694, 'Llano de la Puerta', 3459, 'GRO', 142, 'MX', 16.74355000, -99.52721000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282433'),
(71695, 'Llano de la Y', 3450, 'MEX', 142, 'MX', 19.40278000, -99.61639000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282447'),
(71696, 'Llano de las Flores (Barrio del Hueso)', 3450, 'MEX', 142, 'MX', 19.43583000, -99.32250000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282457'),
(71697, 'Llano del Tigre', 3477, 'NAY', 142, 'MX', 22.12707000, -105.30581000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282457'),
(71698, 'Llera', 3463, 'TAM', 142, 'MX', 23.26609000, -98.91836000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q2001158'),
(71699, 'Llera de Canales', 3463, 'TAM', 142, 'MX', 23.31774000, -99.02608000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q4271174'),
(71700, 'Lo de Jesús (Campo Romero)', 3449, 'SIN', 142, 'MX', 24.68000000, -107.54556000, '2019-10-05 23:08:58', '2020-05-01 17:23:02', 1, 'Q20282630'),
(71701, 'Lo de Juárez', 3469, 'GUA', 142, 'MX', 20.76833000, -101.34111000, '2019-10-05 23:08:58', '2020-05-01 17:23:00', 1, 'Q20282632'),
(71702, 'Lo de Lamedo', 3477, 'NAY', 142, 'MX', 21.53843000, -104.93945000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282632'),
(71703, 'Lo de Marcos', 3477, 'NAY', 142, 'MX', 20.95509000, -105.35223000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282632'),
(71704, 'Lobatos', 3462, 'ZAC', 142, 'MX', 22.82280000, -103.40580000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282632'),
(71705, 'Loché', 3466, 'YUC', 142, 'MX', 21.38828000, -88.14566000, '2019-10-05 23:08:58', '2020-05-01 17:23:03', 1, 'Q20245000'),
(71706, 'Lodo Grande', 3459, 'GRO', 142, 'MX', 17.60972000, -99.21194000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20208372'),
(71707, 'Lolotla', 3470, 'HID', 142, 'MX', 21.00922000, -98.73116000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q3845465'),
(71708, 'Loma Alta', 3450, 'MEX', 142, 'MX', 19.68944000, -99.44917000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q3845465'),
(71709, 'Loma Alta', 3476, 'PUE', 142, 'MX', 19.79716000, -98.02712000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q3845465'),
(71710, 'Loma Alta Taxhimay', 3450, 'MEX', 142, 'MX', 19.84222000, -99.40806000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282672'),
(71711, 'Loma Angosta', 3464, 'VER', 142, 'MX', 18.78309000, -96.67588000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20282672'),
(71712, 'Loma Blanca', 3447, 'CHH', 142, 'MX', 31.57996000, -106.29604000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q16592452'),
(71713, 'Loma Bonita', 3451, 'CHP', 142, 'MX', 17.78697000, -92.07651000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q16592452'),
(71714, 'Loma Bonita', 3448, 'OAX', 142, 'MX', 18.10721000, -95.87904000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q2448133'),
(71715, 'Loma Bonita', 3465, 'MOR', 142, 'MX', 18.93144000, -99.17564000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20264371'),
(71716, 'Loma Bonita', 3450, 'MEX', 142, 'MX', 19.18722000, -100.21500000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20239157'),
(71717, 'Loma Bonita', 3476, 'PUE', 142, 'MX', 18.51333000, -96.86778000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20230831'),
(71718, 'Loma Chapultepec', 3448, 'OAX', 142, 'MX', 18.11972000, -96.83917000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20233036'),
(71719, 'Loma Grande', 3464, 'VER', 142, 'MX', 18.92250000, -97.23083000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20270191'),
(71720, 'Loma Larga', 3450, 'MEX', 142, 'MX', 19.62750000, -99.34611000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20239224'),
(71721, 'Loma Larga (Barrio de Loma Larga)', 3450, 'MEX', 142, 'MX', 19.86722000, -99.11611000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20239229'),
(71722, 'Loma Linda', 3455, 'QUE', 142, 'MX', 20.40079000, -100.03132000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20239229'),
(71723, 'Loma Pelada', 3469, 'GUA', 142, 'MX', 20.57222000, -101.29417000, '2019-10-05 23:08:58', '2019-10-05 23:08:58', 1, 'Q20128412'),
(71724, 'Loma Tendida', 3469, 'GUA', 142, 'MX', 20.37560000, -101.31057000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20128412'),
(71725, 'Loma de Bácum', 3468, 'SON', 142, 'MX', 27.58707000, -110.08601000, '2019-10-05 23:08:59', '2020-05-01 17:23:02', 1, 'Q27640382'),
(71726, 'Loma de Guadalupe (La Biznaga)', 3450, 'MEX', 142, 'MX', 19.65219000, -99.36873000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20239175'),
(71727, 'Loma de Guamúchil', 3468, 'SON', 142, 'MX', 27.61157000, -109.98730000, '2019-10-05 23:08:59', '2020-05-01 17:23:02', 1, 'Q30119571'),
(71728, 'Loma de Juárez', 3450, 'MEX', 142, 'MX', 19.43523000, -100.09020000, '2019-10-05 23:08:59', '2020-05-01 17:23:00', 1, 'Q30119571'),
(71729, 'Loma de San Francisco', 3450, 'MEX', 142, 'MX', 19.28333000, -99.80917000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20239185'),
(71730, 'Loma de San José', 3450, 'MEX', 142, 'MX', 19.64222000, -99.32778000, '2019-10-05 23:08:59', '2020-05-01 17:23:00', 1, 'Q20282939'),
(71731, 'Loma de San Miguel', 3450, 'MEX', 142, 'MX', 19.34194000, -99.79167000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20282943'),
(71732, 'Loma de Sogotegoyo', 3464, 'VER', 142, 'MX', 18.21896000, -94.97373000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20270136'),
(71733, 'Loma de Yerbabuena', 3469, 'GUA', 142, 'MX', 21.06056000, -101.44944000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20208609'),
(71734, 'Loma de Zempoala', 3469, 'GUA', 142, 'MX', 20.29182000, -101.08023000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20208609'),
(71735, 'Loma de la Esperanza', 3469, 'GUA', 142, 'MX', 20.65927000, -101.55670000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20208609'),
(71736, 'Loma de los Carmona', 3464, 'VER', 142, 'MX', 19.10556000, -96.39369000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20208609'),
(71737, 'Loma de los Hoyos', 3474, 'MIC', 142, 'MX', 19.03556000, -102.44278000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20240822'),
(71738, 'Loma del Refugio', 3468, 'SON', 142, 'MX', 27.12194000, -109.45917000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20283054'),
(71739, 'Loma del Río', 3450, 'MEX', 142, 'MX', 19.60056000, -99.35056000, '2019-10-05 23:08:59', '2020-05-01 17:23:00', 1, 'Q20283058'),
(71740, 'Loma la Paz', 3452, 'NLE', 142, 'MX', 25.71111000, -100.13472000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20234726'),
(71741, 'Lomantán', 3451, 'CHP', 142, 'MX', 16.43999000, -92.06445000, '2019-10-05 23:08:59', '2020-05-01 17:22:59', 1, 'Q20234726'),
(71742, 'Lomas Verdes', 3464, 'VER', 142, 'MX', 19.50446000, -96.88899000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20270224'),
(71743, 'Lomas de Ahuatepec', 3465, 'MOR', 142, 'MX', 18.97139000, -99.18972000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20236212'),
(71744, 'Lomas de Altavista', 3450, 'MEX', 142, 'MX', 19.36139000, -98.94472000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20128638'),
(71745, 'Lomas de Barrillas', 3464, 'VER', 142, 'MX', 18.14806000, -94.52639000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20128655'),
(71746, 'Lomas de Chapultepec', 3459, 'GRO', 142, 'MX', 16.71615000, -99.61021000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20128668'),
(71747, 'Lomas de Romero', 3476, 'PUE', 142, 'MX', 18.91444000, -97.71722000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20230877'),
(71748, 'Lomas de Río Medio Cuatro', 3464, 'VER', 142, 'MX', 19.19333000, -96.21056000, '2019-10-05 23:08:59', '2020-05-01 17:23:02', 1, 'Q20270218'),
(71749, 'Lomas de San Juan', 3459, 'GRO', 142, 'MX', 16.98468000, -99.80384000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20270218'),
(71750, 'Lomas de San Martín', 3452, 'NLE', 142, 'MX', 25.74722000, -100.11972000, '2019-10-05 23:08:59', '2020-05-01 17:23:01', 1, 'Q20263576'),
(71751, 'Lomas de San Pablo', 3450, 'MEX', 142, 'MX', 19.20250000, -98.91778000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20283252'),
(71752, 'Lomas de San Sebastián', 3450, 'MEX', 142, 'MX', 19.38206000, -98.93320000, '2019-10-05 23:08:59', '2020-05-01 17:23:00', 1, 'Q20283252'),
(71753, 'Lomas de Santa Anita', 3457, 'BCN', 142, 'MX', 32.54278000, -116.65139000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20283260'),
(71754, 'Lomas de Santa Cruz', 3476, 'PUE', 142, 'MX', 18.92444000, -97.73000000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20129010'),
(71755, 'Lomas de Tenopalco', 3450, 'MEX', 142, 'MX', 19.70232000, -99.11237000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20129010'),
(71756, 'Lomas de Tepemecatl', 3473, 'CMX', 142, 'MX', 19.23861000, -99.23944000, '2019-10-05 23:08:59', '2024-01-24 12:10:50', 1, 'Q20129023'),
(71757, 'Lomas de Tlatelolco', 3457, 'BCN', 142, 'MX', 32.48306000, -116.80167000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20247816'),
(71758, 'Lomas de la Maestranza', 3474, 'MIC', 142, 'MX', 19.68750000, -101.33028000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20128889'),
(71759, 'Lomas del Real', 3463, 'TAM', 142, 'MX', 22.52052000, -97.89913000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20128889'),
(71760, 'Lombardía', 3474, 'MIC', 142, 'MX', 19.15806000, -102.05111000, '2019-10-05 23:08:59', '2020-05-01 17:23:01', 1, 'Q20129217'),
(71761, 'Lomitas', 3454, 'TAB', 142, 'MX', 18.06434000, -92.96201000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20129217'),
(71762, 'Loreto', 3460, 'BCS', 142, 'MX', 26.01217000, -111.34888000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q1020758'),
(71763, 'Loreto', 3462, 'ZAC', 142, 'MX', 22.27248000, -101.98898000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q1020758'),
(71764, 'Los Achotes', 3459, 'GRO', 142, 'MX', 17.60056000, -101.41750000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20208442'),
(71765, 'Los Aguajes', 3477, 'NAY', 142, 'MX', 21.10729000, -104.35447000, '2019-10-05 23:08:59', '2019-10-05 23:08:59', 1, 'Q20208442'),
(71766, 'Los Algodones', 3457, 'BCN', 142, 'MX', 32.70000000, -114.73333000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20208442'),
(71767, 'Los Almendros', 3459, 'GRO', 142, 'MX', 17.61167000, -101.43917000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20130066'),
(71768, 'Los Altos', 3464, 'VER', 142, 'MX', 21.50976000, -97.75705000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270237'),
(71769, 'Los Angeles', 3453, 'DUR', 142, 'MX', 25.52206000, -103.56757000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270237'),
(71770, 'Los Angeles', 3449, 'SIN', 142, 'MX', 25.60289000, -108.48095000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270237'),
(71771, 'Los Arana', 3450, 'MEX', 142, 'MX', 19.70981000, -99.50222000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270237'),
(71772, 'Los Arcos', 3469, 'GUA', 142, 'MX', 21.04928000, -101.68864000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270237'),
(71773, 'Los Arcos', 3465, 'MOR', 142, 'MX', 18.88722000, -99.02417000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20283636'),
(71774, 'Los Arquitos', 3456, 'AGU', 142, 'MX', 21.92299000, -102.38554000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20283636'),
(71775, 'Los Aztecas', 3463, 'TAM', 142, 'MX', 22.50093000, -98.61417000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20283636'),
(71776, 'Los Bahuises', 3468, 'SON', 142, 'MX', 27.11667000, -109.46722000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20131211'),
(71777, 'Los Barriles', 3460, 'BCS', 142, 'MX', 23.68273000, -109.69953000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20131211'),
(71778, 'Los Baños', 3455, 'QUE', 142, 'MX', 20.79050000, -100.31310000, '2019-10-05 23:09:00', '2020-05-01 17:23:02', 1, 'Q20131211'),
(71779, 'Los Benitos', 3455, 'QUE', 142, 'MX', 20.68500000, -99.95611000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20131520'),
(71780, 'Los Berros', 3450, 'MEX', 142, 'MX', 19.39771000, -100.04840000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20131520'),
(71781, 'Los Cabos', 3460, 'BCS', 142, 'MX', 23.27663000, -109.75322000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q659516'),
(71782, 'Los Cafetales', 3451, 'CHP', 142, 'MX', 14.86745000, -92.30200000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20131867'),
(71783, 'Los Cajones', 3474, 'MIC', 142, 'MX', 19.20964000, -101.91317000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20131867'),
(71784, 'Los Cavazos', 3463, 'TAM', 142, 'MX', 26.14803000, -98.34508000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q27798270'),
(71785, 'Los Caños', 3456, 'AGU', 142, 'MX', 21.78167000, -102.46750000, '2019-10-05 23:09:00', '2020-05-01 17:22:59', 1, 'Q20210814'),
(71786, 'Los Cenotes', 3454, 'TAB', 142, 'MX', 17.63461000, -91.03759000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20210814'),
(71787, 'Los Cerritos', 3464, 'VER', 142, 'MX', 18.07884000, -94.61668000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20210814'),
(71788, 'Los Cerritos', 3455, 'QUE', 142, 'MX', 20.56204000, -100.06338000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20132801'),
(71789, 'Los Charcos', 3474, 'MIC', 142, 'MX', 20.26459000, -102.40815000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20132801'),
(71790, 'Los Cides', 3470, 'HID', 142, 'MX', 19.80439000, -98.51870000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20132801'),
(71791, 'Los Condes', 3462, 'ZAC', 142, 'MX', 23.83889000, -103.07861000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20133857'),
(71792, 'Los Conos', 3456, 'AGU', 142, 'MX', 21.89739000, -101.99487000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20133857'),
(71793, 'Los Corazones', 3451, 'CHP', 142, 'MX', 16.21026000, -94.07391000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20133857'),
(71794, 'Los Cristales (Talican)', 3476, 'PUE', 142, 'MX', 19.87237000, -97.59424000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20230903'),
(71795, 'Los Cues', 3455, 'QUE', 142, 'MX', 20.50194000, -100.26417000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20246588'),
(71796, 'Los Desmontes', 3469, 'GUA', 142, 'MX', 19.95662000, -100.73917000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20246588'),
(71797, 'Los Divorciados', 3467, 'ROO', 142, 'MX', 19.07687000, -88.45647000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20284333'),
(71798, 'Los Dolores (Las Quince Letras)', 3469, 'GUA', 142, 'MX', 21.31778000, -100.56583000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20208699'),
(71799, 'Los Dulces Nombres', 3469, 'GUA', 142, 'MX', 20.64806000, -100.96472000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20208702'),
(71800, 'Los Encinos', 3476, 'PUE', 142, 'MX', 19.17917000, -98.39833000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20135001'),
(71801, 'Los Fierros', 3469, 'GUA', 142, 'MX', 20.19353000, -100.75941000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20208711'),
(71802, 'Los Galvan', 3469, 'GUA', 142, 'MX', 21.06377000, -100.80114000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20208711'),
(71803, 'Los Garcías', 3476, 'PUE', 142, 'MX', 18.80333000, -97.39417000, '2019-10-05 23:09:00', '2020-05-01 17:23:01', 1, 'Q20230906'),
(71804, 'Los Guajes', 3474, 'MIC', 142, 'MX', 20.26111000, -102.14164000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20230906'),
(71805, 'Los Guerra', 3463, 'TAM', 142, 'MX', 26.39429000, -99.07972000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20230906'),
(71806, 'Los Hoyos', 3468, 'SON', 142, 'MX', 30.12444000, -109.78222000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20229280'),
(71807, 'Los Hucuares', 3450, 'MEX', 142, 'MX', 19.89916000, -102.49759000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20229280'),
(71808, 'Los Laureles', 3475, 'CAM', 142, 'MX', 19.48778000, -89.99444000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20136843'),
(71809, 'Los Limones', 3474, 'MIC', 142, 'MX', 19.60032000, -102.53038000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20136843'),
(71810, 'Los Lirios', 3464, 'VER', 142, 'MX', 18.50058000, -95.38828000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20136843'),
(71811, 'Los Liros', 3459, 'GRO', 142, 'MX', 16.73972000, -98.21722000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20137356'),
(71812, 'Los Lorenzos', 3469, 'GUA', 142, 'MX', 21.07229000, -101.37580000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20137356'),
(71813, 'Los Mancera', 3469, 'GUA', 142, 'MX', 20.47028000, -100.80389000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20285155'),
(71814, 'Los Mangos', 3464, 'VER', 142, 'MX', 18.24361000, -95.12222000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270389'),
(71815, 'Los Medranos', 3469, 'GUA', 142, 'MX', 20.88283000, -101.43640000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270389'),
(71816, 'Los Mochis', 3449, 'SIN', 142, 'MX', 25.79302000, -108.99808000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q752460'),
(71817, 'Los Mogotes', 3459, 'GRO', 142, 'MX', 16.94170000, -100.07653000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q752460'),
(71818, 'Los Molinos', 3464, 'VER', 142, 'MX', 19.59565000, -97.21513000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q752460'),
(71819, 'Los Naranjos', 3451, 'CHP', 142, 'MX', 17.26446000, -92.61972000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q752460'),
(71820, 'Los Naranjos', 3464, 'VER', 142, 'MX', 18.33111000, -95.24167000, '2019-10-05 23:09:00', '2019-10-05 23:09:00', 1, 'Q20270417'),
(71821, 'Los Naranjos Esquipulas', 3448, 'OAX', 142, 'MX', 15.98167000, -96.45889000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20138678'),
(71822, 'Los Nicolases', 3469, 'GUA', 142, 'MX', 20.85806000, -101.31750000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285360'),
(71823, 'Los Nogales', 3450, 'MEX', 142, 'MX', 19.85734000, -102.15612000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285372'),
(71824, 'Los Ocotes', 3469, 'GUA', 142, 'MX', 20.41560000, -101.96108000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285372'),
(71825, 'Los Olvera', 3455, 'QUE', 142, 'MX', 20.53434000, -100.40795000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285372'),
(71826, 'Los Otates', 3477, 'NAY', 142, 'MX', 21.70427000, -105.37622000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285372'),
(71827, 'Los Palacios', 3451, 'CHP', 142, 'MX', 14.85718000, -92.25821000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20217686'),
(71828, 'Los Parques', 3452, 'NLE', 142, 'MX', 25.79073000, -100.46992000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285655'),
(71829, 'Los Pescados', 3464, 'VER', 142, 'MX', 19.56114000, -97.14848000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285655'),
(71830, 'Los Pilares', 3458, 'TLA', 142, 'MX', 19.27159000, -97.94703000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20285655'),
(71831, 'Los Pilares', 3474, 'MIC', 142, 'MX', 20.32582000, -102.37322000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20139904'),
(71832, 'Los Pinos', 3451, 'CHP', 142, 'MX', 16.92083000, -92.10861000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20140177'),
(71833, 'Los Pinos', 3464, 'VER', 142, 'MX', 18.44389000, -95.24333000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20270469'),
(71834, 'Los Plátanos', 3451, 'CHP', 142, 'MX', 17.00556000, -92.74264000, '2019-10-05 23:09:01', '2020-05-01 17:22:59', 1, 'Q20270469'),
(71835, 'Los Pocitos', 3455, 'QUE', 142, 'MX', 20.80000000, -100.31408000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20270469'),
(71836, 'Los Pozos', 3451, 'CHP', 142, 'MX', 16.64770000, -92.40726000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20270469'),
(71837, 'Los Pozos', 3449, 'SIN', 142, 'MX', 23.00944000, -106.15250000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20140826'),
(71838, 'Los Prietos', 3469, 'GUA', 142, 'MX', 20.57513000, -101.26574000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20140826'),
(71839, 'Los Ramones', 3452, 'NLE', 142, 'MX', 25.69716000, -99.62529000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q3311869'),
(71840, 'Los Ramírez', 3462, 'ZAC', 142, 'MX', 23.84505000, -103.03447000, '2019-10-05 23:09:01', '2020-05-01 17:23:03', 1, 'Q3311869'),
(71841, 'Los Ramírez', 3469, 'GUA', 142, 'MX', 21.01942000, -101.64411000, '2019-10-05 23:09:01', '2020-05-01 17:23:00', 1, 'Q3311869'),
(71842, 'Los Remedios', 3450, 'MEX', 142, 'MX', 19.97810000, -102.67954000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q3311869'),
(71843, 'Los Remedios', 3469, 'GUA', 142, 'MX', 21.18492000, -100.19919000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q3311869'),
(71844, 'Los Reyes', 3450, 'MEX', 142, 'MX', 19.68524000, -99.76450000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q3311869'),
(71845, 'Los Reyes', 3470, 'HID', 142, 'MX', 20.15529000, -98.16424000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20141126'),
(71846, 'Los Reyes', 3464, 'VER', 142, 'MX', 18.67428000, -97.03851000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q5980299'),
(71847, 'Los Reyes Acaquilpan', 3450, 'MEX', 142, 'MX', 19.36357000, -98.97707000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q2276224'),
(71848, 'Los Reyes Acatlixhuayán', 3450, 'MEX', 142, 'MX', 19.20889000, -98.88242000, '2019-10-05 23:09:01', '2020-05-01 17:23:00', 1, 'Q2276224'),
(71849, 'Los Reyes Quiahuixtlan', 3458, 'TLA', 142, 'MX', 19.33955000, -98.25155000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q2276224'),
(71850, 'Los Reyes Tlanechicolpan', 3476, 'PUE', 142, 'MX', 19.03250000, -98.35250000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20230949'),
(71851, 'Los Reyes de Juárez', 3476, 'PUE', 142, 'MX', 18.96457000, -97.82541000, '2019-10-05 23:09:01', '2020-05-01 17:23:01', 1, 'Q6683220'),
(71852, 'Los Reyes de Salgado', 3474, 'MIC', 142, 'MX', 19.59042000, -102.47242000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q992162'),
(71853, 'Los Riegos', 3451, 'CHP', 142, 'MX', 16.30457000, -92.12425000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q992162'),
(71854, 'Los Robles', 3464, 'VER', 142, 'MX', 18.97305000, -96.11768000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q992162'),
(71855, 'Los Rodríguez', 3469, 'GUA', 142, 'MX', 21.04311000, -100.64361000, '2019-10-05 23:09:01', '2020-05-01 17:23:00', 1, 'Q992162'),
(71856, 'Los Romeros', 3470, 'HID', 142, 'MX', 20.02976000, -98.41496000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q992162'),
(71857, 'Los Sabinos', 3470, 'HID', 142, 'MX', 20.31535000, -98.64862000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q56649485'),
(71858, 'Los Sauces', 3459, 'GRO', 142, 'MX', 18.26656000, -99.83502000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q56649485'),
(71859, 'Los Sauces', 3454, 'TAB', 142, 'MX', 18.03335000, -92.91693000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20141640'),
(71860, 'Los Saucitos', 3459, 'GRO', 142, 'MX', 16.98549000, -99.29998000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20141640'),
(71861, 'Los Saucos', 3450, 'MEX', 142, 'MX', 19.16520000, -99.99895000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20141640'),
(71862, 'Los Tepames', 3472, 'COL', 142, 'MX', 19.09389000, -103.62250000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20237953'),
(71863, 'Los Tepetates', 3459, 'GRO', 142, 'MX', 17.05893000, -99.24728000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20237953'),
(71864, 'Los Tigres (San Marcos)', 3464, 'VER', 142, 'MX', 17.89056000, -95.34944000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20270513'),
(71865, 'Los Torrentes', 3464, 'VER', 142, 'MX', 19.20194000, -96.21139000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20142489'),
(71866, 'Los Tuzos', 3470, 'HID', 142, 'MX', 20.05389000, -98.75639000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20142803'),
(71867, 'Los Valles', 3457, 'BCN', 142, 'MX', 32.39250000, -116.95500000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20219002'),
(71868, 'Los Zacatones', 3461, 'SLP', 142, 'MX', 22.96139000, -102.05556000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20143055'),
(71869, 'Los Álamos', 3471, 'COA', 142, 'MX', 28.35645000, -100.94564000, '2019-10-05 23:09:01', '2020-05-01 17:23:00', 1, 'Q20143055'),
(71870, 'Los Ángeles', 3448, 'OAX', 142, 'MX', 17.39485000, -95.16540000, '2019-10-05 23:09:01', '2020-05-01 17:23:01', 1, 'Q20143055'),
(71871, 'Los Ángeles', 3469, 'GUA', 142, 'MX', 20.55361000, -100.94167000, '2019-10-05 23:09:01', '2020-05-01 17:23:00', 1, 'Q20208663'),
(71872, 'Los Ángeles Tetela', 3476, 'PUE', 142, 'MX', 18.88995000, -98.16712000, '2019-10-05 23:09:01', '2020-05-01 17:23:01', 1, 'Q20208663'),
(71873, 'Los Ídolos', 3464, 'VER', 142, 'MX', 19.41173000, -96.51808000, '2019-10-05 23:09:01', '2020-05-01 17:23:02', 1, 'Q20208663'),
(71874, 'Los Órganos de San Agustín', 3459, 'GRO', 142, 'MX', 16.93488000, -99.82329000, '2019-10-05 23:09:01', '2020-05-01 17:23:00', 1, 'Q20208663'),
(71875, 'Lourdes', 3455, 'QUE', 142, 'MX', 20.51271000, -100.47643000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20208663'),
(71876, 'Loza de Barrera', 3469, 'GUA', 142, 'MX', 20.98461000, -101.52972000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20208663'),
(71877, 'Loza de los Padres', 3469, 'GUA', 142, 'MX', 21.07181000, -101.54603000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20208663'),
(71878, 'Luchanas', 3471, 'COA', 142, 'MX', 25.79091000, -103.19384000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20208663'),
(71879, 'Lucio Blanco (Los Gavilanes)', 3469, 'GUA', 142, 'MX', 21.11528000, -101.55111000, '2019-10-05 23:09:01', '2019-10-05 23:09:01', 1, 'Q20208864'),
(71880, 'Luis Espinoza', 3451, 'CHP', 142, 'MX', 17.03167000, -93.02694000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20143406'),
(71881, 'Luis Gil Pérez', 3454, 'TAB', 142, 'MX', 17.87541000, -93.07124000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q5983555'),
(71882, 'Luis Moya', 3462, 'ZAC', 142, 'MX', 22.43237000, -102.24864000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20143446'),
(71883, 'Luis Rodríguez (El Vergel)', 3457, 'BCN', 142, 'MX', 30.40722000, -115.88722000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q20143464'),
(71884, 'Luvianos', 3450, 'MEX', 142, 'MX', 18.94554000, -100.42448000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q3017880'),
(71885, 'Lázaro Cárdenas', 3470, 'HID', 142, 'MX', 19.64032000, -98.47166000, '2019-10-05 23:09:02', '2020-05-01 17:23:00', 1, 'Q20282031'),
(71886, 'Lázaro Cárdenas', 3458, 'TLA', 142, 'MX', 19.53750000, -97.98222000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q20264309'),
(71887, 'Lázaro Cárdenas', 3448, 'OAX', 142, 'MX', 16.72818000, -94.85981000, '2019-10-05 23:09:02', '2020-05-01 17:23:01', 1, 'Q20232770'),
(71888, 'Lázaro Cárdenas', 3451, 'CHP', 142, 'MX', 16.60417000, -93.79139000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q20217167'),
(71889, 'Lázaro Cárdenas', 3463, 'TAM', 142, 'MX', 22.97090000, -100.05357000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q20217167'),
(71890, 'Lázaro Cárdenas', 3447, 'CHH', 142, 'MX', 28.39051000, -105.62346000, '2019-10-05 23:09:02', '2020-05-01 17:23:00', 1, 'Q5795966'),
(71891, 'Lázaro Cárdenas', 3457, 'BCN', 142, 'MX', 30.52815000, -115.92617000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q5795966'),
(71892, 'Lázaro Cárdenas', 3449, 'SIN', 142, 'MX', 26.04918000, -108.79858000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q5795966'),
(71893, 'Lázaro Cárdenas', 3456, 'AGU', 142, 'MX', 22.17137000, -102.02704000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q5795966'),
(71894, 'Lázaro Cárdenas', 3476, 'PUE', 142, 'MX', 19.14306000, -97.40056000, '2019-10-05 23:09:02', '2020-05-01 17:23:01', 1, 'Q20230781'),
(71895, 'Lázaro Cárdenas', 3467, 'ROO', 142, 'MX', 20.99900000, -87.44275000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q1660184'),
(71896, 'Lázaro Cárdenas (Chilil)', 3451, 'CHP', 142, 'MX', 16.67611000, -92.48917000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q20217174'),
(71897, 'Lázaro Cárdenas (El Empalme)', 3465, 'MOR', 142, 'MX', 18.93222000, -99.02528000, '2019-10-05 23:09:02', '2020-05-01 17:23:01', 1, 'Q20282114'),
(71898, 'Lázaro Cárdenas (La Purísima)', 3474, 'MIC', 142, 'MX', 19.86972000, -101.02306000, '2019-10-05 23:09:02', '2020-05-01 17:23:01', 1, 'Q20240775'),
(71899, 'Lázaro Cárdenas (Rancho Grande)', 3462, 'ZAC', 142, 'MX', 23.45056000, -102.96250000, '2019-10-05 23:09:02', '2020-05-01 17:23:03', 1, 'Q20264788'),
(71900, 'Lázaro Cárdenas (Santana)', 3464, 'VER', 142, 'MX', 20.22500000, -97.59306000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q20227519'),
(71901, 'López Rayón', 3463, 'TAM', 142, 'MX', 22.49758000, -98.45979000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q20227519'),
(71902, 'Macapule', 3449, 'SIN', 142, 'MX', 25.88607000, -109.14820000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20286813'),
(71903, 'Macario J. Gómez [Colonia]', 3456, 'AGU', 142, 'MX', 21.98056000, -102.25139000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q20210863'),
(71904, 'Macedonio Alonso', 3464, 'VER', 142, 'MX', 20.27417000, -97.69194000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20143687'),
(71905, 'Machetla', 3470, 'HID', 142, 'MX', 21.05946000, -98.49445000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20143704'),
(71906, 'Macho de Agua', 3474, 'MIC', 142, 'MX', 19.44184000, -100.24100000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20143704'),
(71907, 'Maclovio Herrera', 3463, 'TAM', 142, 'MX', 22.50062000, -98.08545000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q3851217'),
(71908, 'Maclovio Herrera (Colonia Aviación)', 3457, 'BCN', 142, 'MX', 32.54194000, -116.63611000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q20247871'),
(71909, 'Maclovio Rojas', 3457, 'BCN', 142, 'MX', 32.47306000, -116.80278000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20143769'),
(71910, 'Mactún', 3454, 'TAB', 142, 'MX', 17.58980000, -91.29298000, '2019-10-05 23:09:02', '2020-05-01 17:23:02', 1, 'Q20143769'),
(71911, 'Macuilxóchitl de Artigas Carranza', 3448, 'OAX', 142, 'MX', 17.01583000, -96.54000000, '2019-10-05 23:09:02', '2020-05-01 17:23:01', 1, 'Q20233302'),
(71912, 'Macultepec', 3454, 'TAB', 142, 'MX', 18.15026000, -92.86092000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q6725827'),
(71913, 'Macuspana', 3454, 'TAB', 142, 'MX', 17.76052000, -92.59539000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q1962453'),
(71914, 'Macutzio', 3474, 'MIC', 142, 'MX', 19.49917000, -100.35500000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20250490'),
(71915, 'Macvilhó', 3451, 'CHP', 142, 'MX', 16.85760000, -92.69419000, '2019-10-05 23:09:02', '2020-05-01 17:22:59', 1, 'Q20250490'),
(71916, 'Macín Chico', 3448, 'OAX', 142, 'MX', 18.06820000, -96.27184000, '2019-10-05 23:09:02', '2020-05-01 17:23:01', 1, 'Q20233301'),
(71917, 'Madera', 3447, 'CHH', 142, 'MX', 29.19366000, -108.14684000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q2447269'),
(71918, 'Madrid', 3472, 'COL', 142, 'MX', 19.08459000, -103.87123000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q5638036'),
(71919, 'Mafafas', 3464, 'VER', 142, 'MX', 19.65866000, -96.75591000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q5638036'),
(71920, 'Magdalena', 3464, 'VER', 142, 'MX', 18.76066000, -97.04385000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q2000139'),
(71921, 'Magdalena Apasco', 3448, 'OAX', 142, 'MX', 17.24083000, -96.82083000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q1656601'),
(71922, 'Magdalena Atlicpac', 3450, 'MEX', 142, 'MX', 19.36906000, -98.94935000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q1656601'),
(71923, 'Magdalena Contreras', 3473, 'CMX', 142, 'MX', 19.33212000, -99.21118000, '2019-10-05 23:09:02', '2024-01-24 12:10:50', 1, 'Q20237086'),
(71924, 'Magdalena Cuayucatepec', 3476, 'PUE', 142, 'MX', 18.54972000, -97.48472000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q20143921'),
(71925, 'Magdalena Jaltepec', 3448, 'OAX', 142, 'MX', 17.32318000, -97.22122000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q3889124'),
(71926, 'Magdalena Mixtepec', 3448, 'OAX', 142, 'MX', 16.89659000, -96.90618000, '2019-10-05 23:09:02', '2019-10-05 23:09:02', 1, 'Q3305435'),
(71927, 'Magdalena Ocotlán', 3448, 'OAX', 142, 'MX', 16.70865000, -96.71010000, '2019-10-05 23:09:02', '2020-05-01 17:23:01', 1, 'Q3889046'),
(71928, 'Magdalena Teitipac', 3448, 'OAX', 142, 'MX', 16.90393000, -96.55840000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q3889608'),
(71929, 'Magdalena Tequisistlán', 3448, 'OAX', 142, 'MX', 16.39929000, -95.60204000, '2019-10-05 23:09:03', '2020-05-01 17:23:01', 1, 'Q2447707'),
(71930, 'Magdalena Tlacotepec', 3448, 'OAX', 142, 'MX', 16.50416000, -95.20248000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q3891051'),
(71931, 'Magdalena Yodocono de Porfirio Díaz', 3448, 'OAX', 142, 'MX', 17.38470000, -97.35530000, '2019-10-05 23:09:03', '2020-05-01 17:23:01', 1, 'Q8054290'),
(71932, 'Magdalena Zahuatlán', 3448, 'OAX', 142, 'MX', 17.38978000, -97.22747000, '2019-10-05 23:09:03', '2020-05-01 17:23:01', 1, 'Q3891472'),
(71933, 'Magdalena de Araceo', 3469, 'GUA', 142, 'MX', 20.30935000, -101.17861000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q3891472'),
(71934, 'Magdalena de Kino', 3468, 'SON', 142, 'MX', 30.62789000, -110.96203000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q1572516'),
(71935, 'Magdaleno Cedillo', 3463, 'TAM', 142, 'MX', 22.78882000, -99.93799000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q1572516'),
(71936, 'Magisterio Digno', 3470, 'HID', 142, 'MX', 20.05694000, -98.76889000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20286849'),
(71937, 'Maguarichi', 3447, 'CHH', 142, 'MX', 27.81655000, -107.96866000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q2656449'),
(71938, 'Maguey Blanco', 3470, 'HID', 142, 'MX', 20.42357000, -99.17032000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q2656449'),
(71939, 'Mahuixtlan', 3464, 'VER', 142, 'MX', 19.40948000, -96.91750000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q2656449'),
(71940, 'Mainero', 3463, 'TAM', 142, 'MX', 24.58503000, -99.56313000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q1997488'),
(71941, 'Maitinez', 3461, 'SLP', 142, 'MX', 22.37167000, -99.29306000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20250545'),
(71942, 'Majastic', 3451, 'CHP', 142, 'MX', 17.28944000, -92.62833000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144190'),
(71943, 'Majomut', 3451, 'CHP', 142, 'MX', 16.84917000, -92.65944000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144200'),
(71944, 'Malagana (San Antonio del Monte)', 3469, 'GUA', 142, 'MX', 21.04670000, -101.75350000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20286876'),
(71945, 'Malinalco', 3450, 'MEX', 142, 'MX', 18.94847000, -99.49619000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q1584311'),
(71946, 'Malinaltepec', 3459, 'GRO', 142, 'MX', 17.17825000, -98.70573000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q6743721'),
(71947, 'Malpaso', 3462, 'ZAC', 142, 'MX', 22.62627000, -102.76194000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q6743721'),
(71948, 'Malpaso', 3456, 'AGU', 142, 'MX', 21.85896000, -102.66441000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q6743721'),
(71949, 'Maltrata', 3464, 'VER', 142, 'MX', 18.81091000, -97.27538000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q3846941'),
(71950, 'Mama', 3466, 'YUC', 142, 'MX', 20.47857000, -89.36498000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144341'),
(71951, 'Mamantel', 3475, 'CAM', 142, 'MX', 18.52512000, -91.08885000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144341'),
(71952, 'Mamithi', 3470, 'HID', 142, 'MX', 20.41003000, -99.65797000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144341'),
(71953, 'Manantiales', 3476, 'PUE', 142, 'MX', 20.17316000, -97.11064000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144341'),
(71954, 'Mancuernas', 3448, 'OAX', 142, 'MX', 16.38940000, -98.11018000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144341'),
(71955, 'Mangas', 3470, 'HID', 142, 'MX', 20.18647000, -99.24799000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20144341'),
(71956, 'Mani', 3466, 'YUC', 142, 'MX', 20.38774000, -89.39189000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q957947'),
(71957, 'Manto del Río Ejido', 3450, 'MEX', 142, 'MX', 19.84008000, -99.92729000, '2019-10-05 23:09:03', '2020-05-01 17:23:00', 1, 'Q957947'),
(71958, 'Manuel Benavides', 3447, 'CHH', 142, 'MX', 28.94431000, -103.91889000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q2652280'),
(71959, 'Manuel Doblado', 3469, 'GUA', 142, 'MX', 20.72085000, -101.85600000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q2447549'),
(71960, 'Manuel Edgardo Ávalos (San Isidro)', 3476, 'PUE', 142, 'MX', 19.06778000, -97.39194000, '2019-10-05 23:09:03', '2020-05-01 17:23:01', 1, 'Q20286903'),
(71961, 'Manuel Lazos', 3451, 'CHP', 142, 'MX', 14.97159000, -92.18726000, '2019-10-05 23:09:03', '2019-10-05 23:09:03', 1, 'Q20286907'),
(71962, 'Manuel León', 3464, 'VER', 142, 'MX', 18.85365000, -96.87124000, '2019-10-05 23:09:03', '2020-05-01 17:23:02', 1, 'Q20286907'),
(71963, 'Manuel María Contreras', 3464, 'VER', 142, 'MX', 20.44617000, -97.48329000, '2019-10-05 23:09:04', '2020-05-01 17:23:02', 1, 'Q20270587'),
(71964, 'Manuel Ojinaga', 3447, 'CHH', 142, 'MX', 29.56444000, -104.41639000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1971351'),
(71965, 'Manuel Villalongín', 3474, 'MIC', 142, 'MX', 20.23750000, -101.52667000, '2019-10-05 23:09:04', '2020-05-01 17:23:01', 1, 'Q20286910'),
(71966, 'Manuel Ávila Camacho', 3451, 'CHP', 142, 'MX', 16.14647000, -93.01701000, '2019-10-05 23:09:04', '2020-05-01 17:22:59', 1, 'Q20250699'),
(71967, 'Manuel Ávila Camacho', 3470, 'HID', 142, 'MX', 20.11194000, -98.70694000, '2019-10-05 23:09:04', '2020-05-01 17:23:00', 1, 'Q20144630'),
(71968, 'Manuel Ávila Camacho (Ponte Duro)', 3451, 'CHP', 142, 'MX', 15.80973000, -93.58933000, '2019-10-05 23:09:04', '2020-05-01 17:22:59', 1, 'Q20144644'),
(71969, 'Manzana Quinta (La Cañada)', 3450, 'MEX', 142, 'MX', 19.54222000, -99.60250000, '2019-10-05 23:09:04', '2020-05-01 17:23:00', 1, 'Q20144853'),
(71970, 'Manzana Segunda', 3450, 'MEX', 142, 'MX', 19.56722000, -99.59389000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20144861'),
(71971, 'Manzana Sexta Parte Centro', 3450, 'MEX', 142, 'MX', 19.52750000, -99.60528000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20239321'),
(71972, 'Manzana Tercera de Santa Cruz Tepexpan', 3450, 'MEX', 142, 'MX', 19.56917000, -99.70361000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20144866'),
(71973, 'Manzana de San Luis', 3474, 'MIC', 142, 'MX', 19.56236000, -100.27703000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20144806'),
(71974, 'Manzana la Cofradía', 3474, 'MIC', 142, 'MX', 19.54944000, -100.35583000, '2019-10-05 23:09:04', '2020-05-01 17:23:01', 1, 'Q20144837'),
(71976, 'Mapachapa', 3464, 'VER', 142, 'MX', 18.02979000, -94.56700000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q2000512'),
(71977, 'Mapastepec', 3451, 'CHP', 142, 'MX', 15.43358000, -92.90039000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q3845187'),
(71978, 'Mapimí', 3453, 'DUR', 142, 'MX', 26.19095000, -104.05463000, '2019-10-05 23:09:04', '2020-05-01 17:23:00', 1, 'Q2348927'),
(71979, 'Maravatío de Ocampo', 3474, 'MIC', 142, 'MX', 19.89113000, -100.44443000, '2019-10-05 23:09:04', '2020-05-01 17:23:01', 1, 'Q2990977'),
(71980, 'Maravatío del Encinal', 3469, 'GUA', 142, 'MX', 20.20513000, -100.96211000, '2019-10-05 23:09:04', '2020-05-01 17:23:00', 1, 'Q20145024'),
(71981, 'Maravilla Tenejapa', 3451, 'CHP', 142, 'MX', 16.20391000, -91.26093000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1477420'),
(71982, 'Maravillas', 3451, 'CHP', 142, 'MX', 17.09693000, -92.89107000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1477420'),
(71983, 'Maravillas', 3462, 'ZAC', 142, 'MX', 22.39988000, -102.01676000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1477420'),
(71984, 'Maravillas', 3471, 'COA', 142, 'MX', 25.65872000, -103.34079000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1477420'),
(71985, 'Maravillas', 3461, 'SLP', 142, 'MX', 22.23876000, -101.01073000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1477420'),
(71986, 'Maravillas', 3456, 'AGU', 142, 'MX', 21.95408000, -102.32982000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1477420'),
(71987, 'Marcelino Rodríguez (San Ignacio)', 3465, 'MOR', 142, 'MX', 18.57889000, -98.74361000, '2019-10-05 23:09:04', '2020-05-01 17:23:01', 1, 'Q20250923'),
(71988, 'Marcol', 3449, 'SIN', 142, 'MX', 25.49445000, -108.51586000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20250923'),
(71989, 'Marfil', 3469, 'GUA', 142, 'MX', 20.99165000, -101.28434000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20145219'),
(71990, 'Mariano Balleza', 3447, 'CHH', 142, 'MX', 26.95389000, -106.34611000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q15247333'),
(71991, 'Mariano Escobedo', 3464, 'VER', 142, 'MX', 18.91333000, -97.13000000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q6762347'),
(71992, 'Mariano Escobedo', 3474, 'MIC', 142, 'MX', 19.96481000, -101.06206000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q6762347'),
(71993, 'Mariano Matamoros', 3451, 'CHP', 142, 'MX', 16.48119000, -92.58472000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20251076'),
(71994, 'Maripa', 3449, 'SIN', 142, 'MX', 25.86371000, -108.15737000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20251076'),
(71995, 'Mariscal Subikuski', 3451, 'CHP', 142, 'MX', 17.33250000, -92.38083000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20287038'),
(71996, 'Mariscala de Juárez', 3448, 'OAX', 142, 'MX', 17.85972000, -98.14083000, '2019-10-05 23:09:04', '2020-05-01 17:23:01', 1, 'Q20145723'),
(71997, 'Marquelia', 3459, 'GRO', 142, 'MX', 16.58335000, -98.81686000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q1999520'),
(71998, 'Marqués de Comillas', 3451, 'CHP', 142, 'MX', 16.21471000, -90.75917000, '2019-10-05 23:09:04', '2020-05-01 17:22:59', 1, 'Q1179985'),
(71999, 'Marroquín', 3469, 'GUA', 142, 'MX', 20.51138000, -100.55946000, '2019-10-05 23:09:04', '2020-05-01 17:23:00', 1, 'Q1179985'),
(72000, 'Marte R. Gómez', 3451, 'CHP', 142, 'MX', 14.92653000, -92.44389000, '2019-10-05 23:09:04', '2020-05-01 17:22:59', 1, 'Q1179985'),
(72001, 'Marte R. Gómez (Tobarito)', 3468, 'SON', 142, 'MX', 27.36778000, -109.88583000, '2019-10-05 23:09:04', '2020-05-01 17:23:02', 1, 'Q20145778'),
(72002, 'Martinez', 3461, 'SLP', 142, 'MX', 22.06790000, -99.62546000, '2019-10-05 23:09:04', '2019-10-05 23:09:04', 1, 'Q20145778');

