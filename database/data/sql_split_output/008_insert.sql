INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(3521, 'Steinhaus', 2058, '4', 15, 'AT', 48.11614000, 14.01890000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q643744'),
(3522, 'Stenzengreith', 2059, '6', 15, 'AT', 47.20000000, 15.51667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q643744'),
(3523, '<PERSON><PERSON><PERSON>', 2065, '3', 15, 'AT', 48.15000000, 14.81667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q643744'),
(3524, 'Stetteldorf am Wagram', 2065, '3', 15, 'AT', 48.40815000, 16.01862000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q666571'),
(3525, '<PERSON>etten', 2065, '3', 15, '<PERSON>', 48.36667000, 16.38333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q676618'),
(3526, '<PERSON><PERSON>erberg', 2057, '2', 15, 'AT', 46.78804000, 14.11290000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q692907'),
(3527, 'Steyr', 2058, '4', 15, 'AT', 48.04274000, 14.42127000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q692907'),
(3528, 'Steyr Stadt', 2058, '4', 15, 'AT', 48.03333000, 14.41667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q260320'),
(3529, 'Steyregg', 2058, '4', 15, 'AT', 48.28513000, 14.36995000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q662757'),
(3530, 'Stinatz', 2062, '1', 15, 'AT', 47.20266000, 16.13312000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q301284'),
(3531, 'Stiwoll', 2059, '6', 15, 'AT', 47.10000000, 15.21667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q689686'),
(3532, 'Stockenboi', 2057, '2', 15, 'AT', 46.72611000, 13.52306000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q597549'),
(3533, 'Stockerau', 2065, '3', 15, 'AT', 48.38333000, 16.21667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q494283'),
(3534, 'Stollhofen', 2065, '3', 15, 'AT', 48.35226000, 15.75963000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q494283'),
(3535, 'Stoob', 2062, '1', 15, 'AT', 47.52845000, 16.47760000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q663326'),
(3536, 'Stotzing', 2062, '1', 15, 'AT', 47.90688000, 16.54580000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q592578'),
(3537, 'Straden', 2059, '6', 15, 'AT', 46.80917000, 15.86806000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q167011'),
(3538, 'Strallegg', 2059, '6', 15, 'AT', 47.41165000, 15.72534000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q167011'),
(3539, 'Strass', 2065, '3', 15, 'AT', 48.46667000, 15.73333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q167011'),
(3540, 'Strass im Zillertal', 2064, '7', 15, 'AT', 47.39556000, 11.81966000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674452'),
(3541, 'Strassen', 2064, '7', 15, 'AT', 46.75389000, 12.48417000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674452'),
(3542, 'Strassengel', 2059, '6', 15, 'AT', 47.11573000, 15.33288000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674452'),
(3543, 'Strassham', 2058, '4', 15, 'AT', 48.28348000, 14.14524000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674452'),
(3544, 'Strasshof an der Nordbahn', 2065, '3', 15, 'AT', 48.31667000, 16.66667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q667057'),
(3545, 'Strasswalchen', 2061, '5', 15, 'AT', 47.97947000, 13.25535000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q259019'),
(3546, 'Stratzing', 2065, '3', 15, 'AT', 48.45000000, 15.60000000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q680261'),
(3547, 'Straß in Steiermark', 2059, '6', 15, 'AT', 46.72722000, 15.62444000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q690920'),
(3548, 'Straßburg-Stadt', 2057, '2', 15, 'AT', 46.89444000, 14.32861000, '2019-10-05 22:28:34', '2020-05-01 17:22:34', 1, 'Q690920'),
(3549, 'Straßgang', 2059, '6', 15, 'AT', 47.03333000, 15.40000000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q179891'),
(3550, 'Strem', 2062, '1', 15, 'AT', 47.04498000, 16.41426000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q537140'),
(3551, 'Strengberg', 2065, '3', 15, 'AT', 48.14685000, 14.65147000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q537140'),
(3552, 'Strengen', 2064, '7', 15, 'AT', 47.12589000, 10.46199000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q693995'),
(3553, 'Strobl', 2061, '5', 15, 'AT', 47.71667000, 13.48333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q667278'),
(3554, 'Stronsdorf', 2065, '3', 15, 'AT', 48.65157000, 16.29890000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q655726'),
(3555, 'Stubenberg', 2059, '6', 15, 'AT', 47.24460000, 15.80027000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q876326'),
(3556, 'Studenzen', 2059, '6', 15, 'AT', 47.00583000, 15.75417000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q693223'),
(3557, 'Stuhlfelden', 2061, '5', 15, 'AT', 47.28761000, 12.52755000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q693223'),
(3558, 'Stumm', 2064, '7', 15, 'AT', 47.29052000, 11.88755000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q693223'),
(3559, 'Stummerberg', 2064, '7', 15, 'AT', 47.28333000, 11.91667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q693223'),
(3560, 'Stössing', 2065, '3', 15, 'AT', 48.12267000, 15.81379000, '2019-10-05 22:28:34', '2020-05-01 17:22:34', 1, 'Q674310'),
(3561, 'Sulz', 2063, '8', 15, 'AT', 47.28721000, 9.65183000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674310'),
(3562, 'Sulzberg', 2063, '8', 15, 'AT', 47.52178000, 9.91353000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674310'),
(3563, 'Sulztal an der Weinstraße', 2059, '6', 15, 'AT', 46.68333000, 15.55000000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q674310'),
(3564, 'Söchau', 2059, '6', 15, 'AT', 47.03333000, 16.01667000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q674310'),
(3565, 'Södingberg', 2059, '6', 15, 'AT', 47.10703000, 15.17169000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q674310'),
(3566, 'Sölden', 2064, '7', 15, 'AT', 46.96667000, 11.00000000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q570486'),
(3567, 'Söll', 2064, '7', 15, 'AT', 47.50378000, 12.19221000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q570486'),
(3568, 'Tadten', 2062, '1', 15, 'AT', 47.76667000, 16.98333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q661072'),
(3569, 'Taiskirchen im Innkreis', 2058, '4', 15, 'AT', 48.26468000, 13.57318000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q671307'),
(3570, 'Tamsweg', 2061, '5', 15, 'AT', 47.12808000, 13.81102000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q669681'),
(3571, 'Tannheim', 2064, '7', 15, 'AT', 47.49934000, 10.51641000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q700675'),
(3572, 'Tarrenz', 2064, '7', 15, 'AT', 47.26667000, 10.76667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q700675'),
(3573, 'Tattendorf', 2065, '3', 15, 'AT', 47.95000000, 16.30000000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q876253'),
(3574, 'Taufkirchen an der Trattnach', 2058, '4', 15, 'AT', 48.24731000, 13.74767000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q686331'),
(3575, 'Taugl', 2061, '5', 15, 'AT', 47.64747000, 13.20282000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q686331'),
(3576, 'Tauplitz', 2059, '6', 15, 'AT', 47.56005000, 14.01293000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q672697'),
(3577, 'Taxach', 2061, '5', 15, 'AT', 47.72610000, 13.07184000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q672697'),
(3578, 'Taxenbach', 2061, '5', 15, 'AT', 47.29116000, 12.96215000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q700419'),
(3579, 'Teesdorf', 2065, '3', 15, 'AT', 47.95000000, 16.28333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q677107'),
(3580, 'Telfes im Stubai', 2064, '7', 15, 'AT', 47.16667000, 11.36667000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q684409'),
(3581, 'Telfs', 2064, '7', 15, 'AT', 47.30707000, 11.06817000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q660015'),
(3582, 'Terfens', 2064, '7', 15, 'AT', 47.32355000, 11.64388000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q679455'),
(3583, 'Ternberg', 2058, '4', 15, 'AT', 47.94518000, 14.35870000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q671466'),
(3584, 'Ternitz', 2065, '3', 15, 'AT', 47.71565000, 16.03575000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q671466'),
(3585, 'Teufenbach', 2059, '6', 15, 'AT', 47.12870000, 14.35913000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q688227'),
(3586, 'Thal', 2059, '6', 15, 'AT', 47.07644000, 15.36052000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q688227'),
(3587, 'Thalgau', 2061, '5', 15, 'AT', 47.84142000, 13.25325000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q688579'),
(3588, 'Thalheim bei Wels', 2058, '4', 15, 'AT', 48.15000000, 14.03333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q677506'),
(3589, 'Thaur', 2064, '7', 15, 'AT', 47.29476000, 11.47529000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q694309'),
(3590, 'Thaya', 2065, '3', 15, 'AT', 48.85489000, 15.28902000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q694309'),
(3591, 'Theresienfeld', 2065, '3', 15, 'AT', 47.85000000, 16.23333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674116'),
(3592, 'Thomasberg', 2065, '3', 15, 'AT', 47.56667000, 16.13333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674841'),
(3593, 'Thomatal', 2061, '5', 15, 'AT', 47.06667000, 13.75000000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674841'),
(3594, 'Thumersbach', 2061, '5', 15, 'AT', 47.32952000, 12.81675000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q674841'),
(3595, 'Thurn', 2064, '7', 15, 'AT', 46.85056000, 12.76861000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q700696'),
(3596, 'Thörl', 2059, '6', 15, 'AT', 47.51952000, 15.22276000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q689720'),
(3597, 'Thüringen', 2063, '8', 15, 'AT', 47.20000000, 9.76667000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q689720'),
(3598, 'Tiefenbach', 2065, '3', 15, 'AT', 47.53540000, 16.21346000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q689720'),
(3599, 'Tiefgraben', 2058, '4', 15, 'AT', 47.87385000, 13.30591000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q876218'),
(3600, 'Tieschen', 2059, '6', 15, 'AT', 46.78611000, 15.94222000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q687430'),
(3601, 'Tillmitsch', 2059, '6', 15, 'AT', 46.81195000, 15.51679000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q687430'),
(3602, 'Tillmitsch Links der Laßnitz', 2059, '6', 15, 'AT', 46.80955000, 15.52394000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q687430'),
(3603, 'Timelkam', 2058, '4', 15, 'AT', 48.00394000, 13.60760000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q542915'),
(3604, 'Tobadill', 2064, '7', 15, 'AT', 47.12513000, 10.51404000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q696244'),
(3605, 'Tobaj', 2062, '1', 15, 'AT', 47.08333000, 16.30000000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q542087'),
(3606, 'Traboch', 2059, '6', 15, 'AT', 47.37705000, 14.98647000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q701429'),
(3607, 'Tragwein', 2058, '4', 15, 'AT', 48.33314000, 14.62237000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q683108'),
(3608, 'Trahütten', 2059, '6', 15, 'AT', 46.82500000, 15.15694000, '2019-10-05 22:28:34', '2020-05-01 17:22:35', 1, 'Q687960'),
(3609, 'Traisen', 2065, '3', 15, 'AT', 48.03333000, 15.60000000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q687960'),
(3610, 'Traiskirchen', 2065, '3', 15, 'AT', 48.01485000, 16.29324000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q687960'),
(3611, 'Traismauer', 2065, '3', 15, 'AT', 48.35000000, 15.73333000, '2019-10-05 22:28:34', '2019-10-05 22:28:34', 1, 'Q305159'),
(3612, 'Trattenbach', 2065, '3', 15, 'AT', 47.60000000, 15.86667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q666018'),
(3613, 'Traun', 2058, '4', 15, 'AT', 48.22656000, 14.23459000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q666018'),
(3614, 'Traunkirchen', 2058, '4', 15, 'AT', 47.84462000, 13.78939000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q259710'),
(3615, 'Trausdorf an der Wulka', 2062, '1', 15, 'AT', 47.81350000, 16.55760000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q259710'),
(3616, 'Trautmannsdorf an der Leitha', 2065, '3', 15, 'AT', 48.02357000, 16.63266000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q259710'),
(3617, 'Trautmannsdorf in Oststeiermark', 2059, '6', 15, 'AT', 46.87530000, 15.88451000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q259710'),
(3618, 'Trebesing', 2057, '2', 15, 'AT', 46.88639000, 13.51028000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q259710'),
(3619, 'Treglwang', 2059, '6', 15, 'AT', 47.47458000, 14.59083000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q687443'),
(3620, 'Treibach', 2057, '2', 15, 'AT', 46.86667000, 14.46667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q687443'),
(3621, 'Tribuswinkel', 2065, '3', 15, 'AT', 48.00623000, 16.27075000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q484861'),
(3622, 'Trieben', 2059, '6', 15, 'AT', 47.48574000, 14.48744000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q688726'),
(3623, 'Triebendorf', 2059, '6', 15, 'AT', 47.11667000, 14.23333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q670364'),
(3624, 'Trins', 2064, '7', 15, 'AT', 47.08333000, 11.41667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q670364'),
(3625, 'Tristach', 2064, '7', 15, 'AT', 46.81611000, 12.78972000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q876303'),
(3626, 'Trofaiach', 2059, '6', 15, 'AT', 47.42524000, 15.00681000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3627, 'Trumau', 2065, '3', 15, 'AT', 47.99348000, 16.34268000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3628, 'Trössing', 2059, '6', 15, 'AT', 46.81667000, 15.81667000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q675834'),
(3629, 'Tschagguns', 2063, '8', 15, 'AT', 47.08333000, 9.90000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3630, 'Tschanigraben', 2062, '1', 15, 'AT', 47.01667000, 16.30000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3631, 'Tulbing', 2065, '3', 15, 'AT', 48.29336000, 16.12226000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3632, 'Tulfes', 2064, '7', 15, 'AT', 47.25806000, 11.53333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3633, 'Tulln', 2065, '3', 15, 'AT', 48.32829000, 16.05858000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3634, 'Tullnerbach-Lawies', 2065, '3', 15, 'AT', 48.18912000, 16.09117000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3635, 'Turnau', 2059, '6', 15, 'AT', 47.55776000, 15.33739000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3636, 'Tux', 2064, '7', 15, 'AT', 47.15559000, 11.72872000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3637, 'Tweng', 2061, '5', 15, 'AT', 47.18333000, 13.60000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3638, 'Türnitz', 2065, '3', 15, 'AT', 47.93095000, 15.49295000, '2019-10-05 22:28:35', '2020-05-01 17:22:34', 1, 'Q675834'),
(3639, 'Uderns', 2064, '7', 15, 'AT', 47.31667000, 11.86667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3640, 'Ufer', 2058, '4', 15, 'AT', 48.24842000, 14.49977000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3641, 'Ulrichsberg', 2058, '4', 15, 'AT', 48.67498000, 13.91049000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3642, 'Ulrichskirchen', 2065, '3', 15, 'AT', 48.40000000, 16.48333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3643, 'Umhausen', 2064, '7', 15, 'AT', 47.13503000, 10.92826000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675834'),
(3644, 'Ungenach', 2058, '4', 15, 'AT', 48.04756000, 13.61472000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681320'),
(3645, 'Ungerdorf', 2059, '6', 15, 'AT', 47.08333000, 15.66667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681320'),
(3646, 'Unken', 2061, '5', 15, 'AT', 47.64966000, 12.72946000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681320'),
(3647, 'Unterauersbach', 2059, '6', 15, 'AT', 46.86556000, 15.77028000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681320'),
(3648, 'Unterbergla', 2059, '6', 15, 'AT', 46.80746000, 15.31516000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681320'),
(3649, 'Untere Fellach', 2057, '2', 15, 'AT', 46.62504000, 13.82681000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681320'),
(3650, 'Untereisenfeld', 2058, '4', 15, 'AT', 48.16800000, 14.04100000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681320'),
(3651, 'Unterfladnitz', 2059, '6', 15, 'AT', 47.18333000, 15.66667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q377780'),
(3652, 'Unterfrauenhaid', 2062, '1', 15, 'AT', 47.57116000, 16.49885000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q377780'),
(3653, 'Unterjosefstal', 2058, '4', 15, 'AT', 48.29216000, 14.57525000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q377780'),
(3654, 'Unterkohlstätten', 2062, '1', 15, 'AT', 47.38333000, 16.31667000, '2019-10-05 22:28:35', '2020-05-01 17:22:34', 1, 'Q377780'),
(3655, 'Unterlamm', 2059, '6', 15, 'AT', 46.97694000, 16.06389000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q377780'),
(3656, 'Unterlangkampfen', 2064, '7', 15, 'AT', 47.55000000, 12.10000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q377780'),
(3657, 'Unternberg', 2061, '5', 15, 'AT', 47.11269000, 13.74261000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q377780'),
(3658, 'Unterperfuss', 2064, '7', 15, 'AT', 47.26667000, 11.25000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q377780'),
(3659, 'Unterpremstätten', 2059, '6', 15, 'AT', 46.96472000, 15.40417000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q690041'),
(3660, 'Untersiebenbrunn', 2065, '3', 15, 'AT', 48.25000000, 16.73333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690041'),
(3661, 'Unterstinkenbrunn', 2065, '3', 15, 'AT', 48.66770000, 16.34607000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690041'),
(3662, 'Untertauern', 2061, '5', 15, 'AT', 47.30000000, 13.50000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690041'),
(3663, 'Untertilliach', 2064, '7', 15, 'AT', 46.70346000, 12.67758000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q261028'),
(3664, 'Unterwagram', 2065, '3', 15, 'AT', 48.21243000, 15.64951000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q261028'),
(3665, 'Unterwaltersdorf', 2065, '3', 15, 'AT', 47.95000000, 16.41667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q261028'),
(3666, 'Unterwart', 2062, '1', 15, 'AT', 47.25000000, 16.23333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q261028'),
(3667, 'Utschtal', 2059, '6', 15, 'AT', 47.40000000, 15.20000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q261028'),
(3668, 'Uttendorf', 2058, '4', 15, 'AT', 48.15887000, 13.12180000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q261028'),
(3669, 'Uttendorf', 2061, '5', 15, 'AT', 47.28333000, 12.56667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q261028'),
(3670, 'Utzenaich', 2058, '4', 15, 'AT', 48.27622000, 13.46091000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q682796'),
(3671, 'Vals', 2064, '7', 15, 'AT', 47.04507000, 11.53281000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q682796'),
(3672, 'Vandans', 2063, '8', 15, 'AT', 47.09569000, 9.86525000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q682796'),
(3673, 'Vasoldsberg', 2059, '6', 15, 'AT', 47.01634000, 15.55835000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q682796'),
(3674, 'Veitsch', 2059, '6', 15, 'AT', 47.57815000, 15.49450000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690077'),
(3675, 'Velden am Wörthersee', 2057, '2', 15, 'AT', 46.61301000, 14.04130000, '2019-10-05 22:28:35', '2020-05-01 17:22:34', 1, 'Q690077'),
(3676, 'Velm', 2065, '3', 15, 'AT', 48.03333000, 16.43333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690077'),
(3677, 'Viechtwang', 2058, '4', 15, 'AT', 47.91531000, 13.96345000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690077'),
(3678, 'Viehhausen', 2061, '5', 15, 'AT', 47.78333000, 12.98333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690077'),
(3679, 'Viehhofen', 2061, '5', 15, 'AT', 47.36667000, 12.73333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690077'),
(3680, 'Viehofen', 2065, '3', 15, 'AT', 48.21667000, 15.61667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q690077'),
(3681, 'Vienna', 2060, '9', 15, 'AT', 48.20849000, 16.37208000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q1741'),
(3682, 'Vigaun', 2061, '5', 15, 'AT', 47.66667000, 13.13333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q1741'),
(3683, 'Viktorsberg', 2063, '8', 15, 'AT', 47.30092000, 9.67484000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q693239'),
(3684, 'Viktring', 2057, '2', 15, 'AT', 46.59194000, 14.26917000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q694633'),
(3685, 'Viktringer Vorstadt', 2057, '2', 15, 'AT', 46.61773000, 14.30931000, '2019-10-05 22:28:35', '2020-08-01 11:52:39', 1, ''),
(3686, 'Villach', 2057, '2', 15, 'AT', 46.61028000, 13.85583000, '2019-10-05 22:28:35', '2024-12-21 12:57:32', 1, 'Q483522'),
(3688, 'Villach-Innere Stadt', 2057, '2', 15, 'AT', 46.61275000, 13.84638000, '2019-10-05 22:28:35', '2020-08-01 11:53:38', 1, ''),
(3689, 'Villacher Vorstadt', 2057, '2', 15, 'AT', 46.62368000, 14.28892000, '2019-10-05 22:28:35', '2020-08-01 11:53:39', 1, ''),
(3690, 'Vils', 2064, '7', 15, 'AT', 47.55000000, 10.63333000, '2019-10-05 22:28:35', '2024-12-21 12:57:35', 1, 'Q240637'),
(3691, 'Virgen', 2064, '7', 15, 'AT', 47.00159000, 12.45661000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681850'),
(3692, 'Vitis', 2065, '3', 15, 'AT', 48.75964000, 15.18259000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681850'),
(3693, 'Vogau', 2059, '6', 15, 'AT', 46.73187000, 15.60837000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681850'),
(3694, 'Voitsberg', 2059, '6', 15, 'AT', 47.04445000, 15.15313000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660453'),
(3695, 'Volders', 2064, '7', 15, 'AT', 47.28333000, 11.56667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q675590'),
(3696, 'Voldöpp', 2064, '7', 15, 'AT', 47.44469000, 11.88343000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q675590'),
(3697, 'Vomp', 2064, '7', 15, 'AT', 47.33333000, 11.68333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q202007'),
(3698, 'Vorau', 2059, '6', 15, 'AT', 47.40548000, 15.88754000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q669945'),
(3699, 'Vorchdorf', 2058, '4', 15, 'AT', 48.00388000, 13.92122000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q661811'),
(3700, 'Vorderhornbach', 2064, '7', 15, 'AT', 47.37009000, 10.53947000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q692042'),
(3701, 'Vordernberg', 2059, '6', 15, 'AT', 47.48809000, 14.99436000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q876380'),
(3702, 'Vorderthiersee', 2064, '7', 15, 'AT', 47.58333000, 12.10000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q876380'),
(3703, 'Vöcklabruck', 2058, '4', 15, 'AT', 48.00279000, 13.65652000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q251325'),
(3704, 'Vöcklamarkt', 2058, '4', 15, 'AT', 48.00250000, 13.48383000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q673008'),
(3705, 'Völkendorf', 2057, '2', 15, 'AT', 46.60806000, 13.83153000, '2019-10-05 22:28:35', '2020-05-01 17:22:34', 1, 'Q673008'),
(3706, 'Völkermarkt', 2057, '2', 15, 'AT', 46.66222000, 14.63444000, '2019-10-05 22:28:35', '2020-05-01 17:22:34', 1, 'Q164488'),
(3707, 'Völkermarkter Vorstadt', 2057, '2', 15, 'AT', 46.62418000, 14.32081000, '2019-10-05 22:28:35', '2020-08-01 11:54:11', 1, ''),
(3708, 'Völs', 2064, '7', 15, 'AT', 47.25000000, 11.33333000, '2019-10-05 22:28:35', '2024-12-21 12:57:37', 1, 'Q234292'),
(3709, 'Vösendorf', 2065, '3', 15, 'AT', 48.12107000, 16.34036000, '2019-10-05 22:28:35', '2020-05-01 17:22:34', 1, 'Q661694'),
(3710, 'Wagna', 2059, '6', 15, 'AT', 46.76682000, 15.55906000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q693460'),
(3711, 'Wagnergraben', 2061, '5', 15, 'AT', 48.02036000, 13.02395000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q693460'),
(3712, 'Wagnitz', 2059, '6', 15, 'AT', 46.98333000, 15.46667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q693460'),
(3713, 'Wagrain', 2061, '5', 15, 'AT', 47.33528000, 13.29889000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q265886'),
(3714, 'Wagrain', 2058, '4', 15, 'AT', 48.00503000, 13.67163000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q265886'),
(3715, 'Wagram', 2058, '4', 15, 'AT', 48.23828000, 14.22996000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q265886'),
(3716, 'Waidhofen an der Thaya', 2065, '3', 15, 'AT', 48.81667000, 15.28333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660060'),
(3717, 'Waidhofen an der Ybbs', 2065, '3', 15, 'AT', 47.96004000, 14.77361000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q427385'),
(3718, 'Waidhofen an der Ybbs Stadt', 2065, '3', 15, 'AT', 47.95999000, 14.77438000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q427385'),
(3719, 'Waidmannsfeld', 2065, '3', 15, 'AT', 47.87042000, 15.98116000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q548941'),
(3720, 'Waidring', 2064, '7', 15, 'AT', 47.58333000, 12.56667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q548941'),
(3721, 'Waiern', 2057, '2', 15, 'AT', 46.73028000, 14.08194000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q548941'),
(3722, 'Waizenkirchen', 2058, '4', 15, 'AT', 48.33018000, 13.85754000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681309'),
(3723, 'Walchen', 2061, '5', 15, 'AT', 47.28822000, 12.68739000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q681309'),
(3724, 'Walchsee', 2064, '7', 15, 'AT', 47.65163000, 12.31868000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q388265'),
(3725, 'Wald am Schoberpaß', 2059, '6', 15, 'AT', 47.44936000, 14.67567000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q388265'),
(3726, 'Wald im Pinzgau', 2061, '5', 15, 'AT', 47.25000000, 12.23333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q696348'),
(3727, 'Waldegg', 2065, '3', 15, 'AT', 47.86852000, 16.05154000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q696348'),
(3728, 'Waldenstein', 2065, '3', 15, 'AT', 48.72845000, 15.01419000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q696348'),
(3729, 'Waldhausen', 2065, '3', 15, 'AT', 48.52183000, 15.26250000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q691524'),
(3730, 'Walding', 2058, '4', 15, 'AT', 48.35209000, 14.15760000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q677293'),
(3731, 'Waldkirchen am Wesen', 2058, '4', 15, 'AT', 48.44059000, 13.82174000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q682974'),
(3732, 'Waldkirchen an der Thaya', 2065, '3', 15, 'AT', 48.93333000, 15.35000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q674188'),
(3733, 'Waldneukirchen', 2058, '4', 15, 'AT', 47.99854000, 14.25879000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q667479'),
(3734, 'Waldprechting', 2061, '5', 15, 'AT', 47.90000000, 13.11667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q667479'),
(3735, 'Waldzell', 2058, '4', 15, 'AT', 48.13562000, 13.42701000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q680345'),
(3736, 'Wallern an der Trattnach', 2058, '4', 15, 'AT', 48.23296000, 13.94620000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q667857'),
(3737, 'Wallern im Burgenland', 2062, '1', 15, 'AT', 47.72847000, 16.93706000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q661084'),
(3738, 'Wallsee', 2065, '3', 15, 'AT', 48.16667000, 14.71667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q661084'),
(3739, 'Walpersbach', 2065, '3', 15, 'AT', 47.71667000, 16.23333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q672178'),
(3740, 'Wals', 2061, '5', 15, 'AT', 47.78333000, 12.96667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q672178'),
(3741, 'Walserfeld', 2061, '5', 15, 'AT', 47.79222000, 12.98000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q672178'),
(3742, 'Waltendorf', 2059, '6', 15, 'AT', 47.06667000, 15.46667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q2543329'),
(3743, 'Wang', 2065, '3', 15, 'AT', 48.04538000, 15.02672000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q2543329'),
(3744, 'Warmbad-Judendorf', 2057, '2', 15, 'AT', 46.60126000, 13.82241000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q2543329'),
(3745, 'Wartberg', 2059, '6', 15, 'AT', 47.52717000, 15.48095000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q2543329'),
(3746, 'Wartberg an der Krems', 2058, '4', 15, 'AT', 47.98909000, 14.11863000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q678956'),
(3747, 'Wartberg ob der Aist', 2058, '4', 15, 'AT', 48.34792000, 14.50796000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q683270'),
(3748, 'Warth', 2065, '3', 15, 'AT', 47.65000000, 16.11667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q683270'),
(3749, 'Warth', 2063, '8', 15, 'AT', 47.25000000, 10.18333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q683270'),
(3750, 'Wartmannstetten', 2065, '3', 15, 'AT', 47.69359000, 16.07506000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q876192'),
(3751, 'Wattenberg', 2064, '7', 15, 'AT', 47.28333000, 11.60000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q876192'),
(3752, 'Wattens', 2064, '7', 15, 'AT', 47.29419000, 11.59070000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660935'),
(3753, 'Weer', 2064, '7', 15, 'AT', 47.30375000, 11.64498000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660935'),
(3754, 'Weerberg', 2064, '7', 15, 'AT', 47.29841000, 11.66592000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660935'),
(3755, 'Weibern', 2058, '4', 15, 'AT', 48.18333000, 13.70000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660935'),
(3756, 'Weichselbaum', 2062, '1', 15, 'AT', 46.94250000, 16.18750000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660935'),
(3757, 'Weiden am See', 2062, '1', 15, 'AT', 47.92532000, 16.86899000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q592370'),
(3758, 'Weiden bei Rechnitz', 2062, '1', 15, 'AT', 47.30000000, 16.35000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q476969'),
(3759, 'Weidling', 2065, '3', 15, 'AT', 48.29100000, 16.30865000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q476969'),
(3760, 'Weigelsdorf', 2065, '3', 15, 'AT', 47.95000000, 16.40000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q476969'),
(3761, 'Weikendorf', 2065, '3', 15, 'AT', 48.34438000, 16.76651000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q686346'),
(3762, 'Weikersdorf am Steinfelde', 2065, '3', 15, 'AT', 47.80612000, 16.14389000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q673759'),
(3763, 'Weilbach', 2058, '4', 15, 'AT', 48.27725000, 13.37165000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q673759'),
(3764, 'Weiler', 2063, '8', 15, 'AT', 47.29972000, 9.65000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q699608'),
(3765, 'Weinburg', 2065, '3', 15, 'AT', 48.11351000, 15.53295000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q699608'),
(3766, 'Weinburg am Saßbach', 2059, '6', 15, 'AT', 46.75361000, 15.72111000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q688062'),
(3767, 'Weingraben', 2062, '1', 15, 'AT', 47.51393000, 16.36375000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q663414'),
(3768, 'Weinzierl am Walde', 2065, '3', 15, 'AT', 48.43211000, 15.43210000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q524523'),
(3769, 'Weinzierl bei Krems', 2065, '3', 15, 'AT', 48.40000000, 15.60000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q524523'),
(3770, 'Weissach', 2064, '7', 15, 'AT', 47.57091000, 12.16255000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q524523'),
(3771, 'Weissenbach am Lech', 2064, '7', 15, 'AT', 47.44162000, 10.64071000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q524523'),
(3772, 'Weissenbach an der Triesting', 2065, '3', 15, 'AT', 47.98211000, 16.03935000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q617047'),
(3773, 'Weisskirchen an der Traun', 2058, '4', 15, 'AT', 48.16202000, 14.12395000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q678423'),
(3774, 'Weistrach', 2065, '3', 15, 'AT', 48.05000000, 14.58333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q526021'),
(3775, 'Weiten', 2065, '3', 15, 'AT', 48.29564000, 15.26010000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q526021'),
(3776, 'Weitensfeld', 2057, '2', 15, 'AT', 46.84743000, 14.19213000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q526021'),
(3777, 'Weitersfeld', 2065, '3', 15, 'AT', 48.78097000, 15.81345000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q526021'),
(3778, 'Weitersfelden', 2058, '4', 15, 'AT', 48.47730000, 14.72546000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q687255'),
(3779, 'Weitra', 2065, '3', 15, 'AT', 48.70000000, 14.88333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q661548'),
(3780, 'Weiz', 2059, '6', 15, 'AT', 47.21667000, 15.61667000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q875641'),
(3781, 'Weißenbach bei Liezen', 2059, '6', 15, 'AT', 47.56667000, 14.21667000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q672041'),
(3782, 'Weißenkirchen im Attergau', 2058, '4', 15, 'AT', 47.94964000, 13.41843000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q672041'),
(3783, 'Weißenkirchen in der Wachau', 2065, '3', 15, 'AT', 48.39790000, 15.46931000, '2019-10-05 22:28:35', '2020-05-01 17:22:34', 1, 'Q676503'),
(3784, 'Weißkirchen in Steiermark', 2059, '6', 15, 'AT', 47.15000000, 14.73333000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q688278'),
(3785, 'Wels', 2058, '4', 15, 'AT', 48.16667000, 14.03333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q688278'),
(3786, 'Wels Stadt', 2058, '4', 15, 'AT', 48.16082000, 14.02164000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q178582'),
(3787, 'Wels-Land', 2058, '4', 15, 'AT', 48.11660000, 13.96637000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q660544'),
(3788, 'Wendling', 2058, '4', 15, 'AT', 48.23182000, 13.66622000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q693925'),
(3789, 'Weng im Innkreis', 2058, '4', 15, 'AT', 48.23508000, 13.17801000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q693925'),
(3790, 'Wenns', 2064, '7', 15, 'AT', 47.16667000, 10.73333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q693977'),
(3791, 'Weppersdorf', 2062, '1', 15, 'AT', 47.57954000, 16.42679000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q663238'),
(3792, 'Werfen', 2061, '5', 15, 'AT', 47.47585000, 13.19020000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q255361'),
(3793, 'Werfenweng', 2061, '5', 15, 'AT', 47.46204000, 13.25582000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q255361'),
(3794, 'Wernberg', 2057, '2', 15, 'AT', 46.61667000, 13.93333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q255361'),
(3795, 'Werndorf', 2059, '6', 15, 'AT', 46.92417000, 15.49083000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q687500'),
(3796, 'Wernersdorf', 2059, '6', 15, 'AT', 46.71592000, 15.20718000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q687500'),
(3797, 'Wernstein am Inn', 2058, '4', 15, 'AT', 48.50802000, 13.46100000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q666870'),
(3798, 'Westendorf', 2064, '7', 15, 'AT', 47.43208000, 12.21406000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q682929'),
(3799, 'Wettmannstätten', 2059, '6', 15, 'AT', 46.83056000, 15.38722000, '2019-10-05 22:28:35', '2020-05-01 17:22:35', 1, 'Q689664'),
(3800, 'Wetzelsdorf', 2059, '6', 15, 'AT', 47.05293000, 15.39923000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q730125'),
(3801, 'Weyer', 2058, '4', 15, 'AT', 47.85717000, 14.66409000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q730125'),
(3802, 'Weyregg', 2058, '4', 15, 'AT', 47.90294000, 13.57193000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q730125'),
(3803, 'Wiener Neudorf', 2065, '3', 15, 'AT', 48.08278000, 16.31384000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q666602'),
(3804, 'Wiener Neustadt', 2065, '3', 15, 'AT', 47.80485000, 16.23196000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q666602'),
(3805, 'Wiener Neustadt Stadt', 2065, '3', 15, 'AT', 47.80000000, 16.25000000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q131612'),
(3806, 'Wienersdorf', 2065, '3', 15, 'AT', 48.00819000, 16.29169000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q131612'),
(3807, 'Wies', 2059, '6', 15, 'AT', 46.72028000, 15.27194000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q672707'),
(3808, 'Wieselburg', 2065, '3', 15, 'AT', 48.13333000, 15.13333000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q661734'),
(3809, 'Wiesen', 2062, '1', 15, 'AT', 47.73776000, 16.33798000, '2019-10-05 22:28:35', '2019-10-05 22:28:35', 1, 'Q661734'),
(3810, 'Wiesfleck', 2062, '1', 15, 'AT', 47.38458000, 16.14552000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q661734'),
(3811, 'Wiesing', 2064, '7', 15, 'AT', 47.40486000, 11.79708000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q661734'),
(3812, 'Wiesmath', 2065, '3', 15, 'AT', 47.61667000, 16.28333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q674161'),
(3813, 'Wildalpen', 2059, '6', 15, 'AT', 47.65000000, 14.98333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q671641'),
(3814, 'Wildermieming', 2064, '7', 15, 'AT', 47.31667000, 11.01667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q671641'),
(3815, 'Wildon', 2059, '6', 15, 'AT', 46.88333000, 15.51667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q674516'),
(3816, 'Wildschönau', 2064, '7', 15, 'AT', 47.40619000, 12.03784000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q606713'),
(3817, 'Wilfersdorf', 2065, '3', 15, 'AT', 48.58333000, 16.63333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q606713'),
(3818, 'Wilfleinsdorf', 2065, '3', 15, 'AT', 48.01667000, 16.71667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q606713'),
(3819, 'Wilhelmsburg', 2065, '3', 15, 'AT', 48.10571000, 15.60539000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q606713'),
(3820, 'Willendorf am Steinfelde', 2065, '3', 15, 'AT', 47.78929000, 16.05686000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q606713'),
(3821, 'Wilten', 2064, '7', 15, 'AT', 47.25829000, 11.38808000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q606713'),
(3822, 'Wimpassing', 2058, '4', 15, 'AT', 48.16667000, 13.98333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q606713'),
(3823, 'Wimpassing an der Leitha', 2062, '1', 15, 'AT', 47.91667000, 16.43333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q662709'),
(3824, 'Wimpassing im Schwarzatale', 2065, '3', 15, 'AT', 47.70295000, 16.03334000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q662709'),
(3825, 'Wimsbach', 2058, '4', 15, 'AT', 48.06667000, 13.90000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q671021'),
(3826, 'Winden am See', 2062, '1', 15, 'AT', 47.95000000, 16.75000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q247547'),
(3827, 'Windhaag bei Freistadt', 2058, '4', 15, 'AT', 48.58774000, 14.56186000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q543940'),
(3828, 'Windhaag bei Perg', 2058, '4', 15, 'AT', 48.28582000, 14.68091000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q679371'),
(3829, 'Windhag', 2065, '3', 15, 'AT', 47.97906000, 14.80245000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q679371'),
(3830, 'Windigsteig', 2065, '3', 15, 'AT', 48.76667000, 15.28333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q676277'),
(3831, 'Windischgarsten', 2058, '4', 15, 'AT', 47.72223000, 14.32755000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q681099'),
(3832, 'Winklarn', 2065, '3', 15, 'AT', 48.09135000, 14.84819000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q681099'),
(3833, 'Winklern', 2057, '2', 15, 'AT', 46.87361000, 12.87472000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q669349'),
(3834, 'Winklern bei Oberwölz', 2059, '6', 15, 'AT', 47.20000000, 14.23333000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q696073'),
(3835, 'Winzendorf', 2065, '3', 15, 'AT', 47.81154000, 16.11300000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q696073'),
(3836, 'Wippenham', 2058, '4', 15, 'AT', 48.22250000, 13.37920000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q696073'),
(3837, 'Wispl', 2058, '4', 15, 'AT', 48.16488000, 13.99753000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q696073'),
(3838, 'Wolfau', 2062, '1', 15, 'AT', 47.25000000, 16.10000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q696073'),
(3839, 'Wolfern', 2058, '4', 15, 'AT', 48.08278000, 14.37201000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q696073'),
(3840, 'Wolfpassing', 2065, '3', 15, 'AT', 48.07785000, 15.06453000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q876387'),
(3841, 'Wolfsbach', 2065, '3', 15, 'AT', 48.06667000, 14.66667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q632364'),
(3842, 'Wolfsberg', 2057, '2', 15, 'AT', 46.84056000, 14.84417000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q632364'),
(3843, 'Wolfsberg im Schwarzautal', 2059, '6', 15, 'AT', 46.84389000, 15.65889000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q632364'),
(3844, 'Wolfsegg am Hausruck', 2058, '4', 15, 'AT', 48.10669000, 13.67274000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q304293'),
(3845, 'Wolfsgraben', 2065, '3', 15, 'AT', 48.15870000, 16.12098000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q304293'),
(3846, 'Wolfsthal', 2065, '3', 15, 'AT', 48.13333000, 17.00000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q661180'),
(3847, 'Wolfurt', 2063, '8', 15, 'AT', 47.46667000, 9.75000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q697083'),
(3848, 'Wolkersdorf im Weinviertel', 2065, '3', 15, 'AT', 48.38333000, 16.51667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q21864074'),
(3849, 'Wudmath', 2057, '2', 15, 'AT', 46.59634000, 13.98067000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q21864074'),
(3850, 'Wulkaprodersdorf', 2062, '1', 15, 'AT', 47.79753000, 16.50447000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q663259'),
(3851, 'Wullersdorf', 2065, '3', 15, 'AT', 48.62792000, 16.10089000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q688634'),
(3852, 'Wundschuh', 2059, '6', 15, 'AT', 46.92639000, 15.45111000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q670748'),
(3853, 'Wängle', 2064, '7', 15, 'AT', 47.48658000, 10.68995000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q670748'),
(3854, 'Wölfnitz', 2057, '2', 15, 'AT', 46.66639000, 14.25806000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q670748'),
(3855, 'Wöllersdorf', 2065, '3', 15, 'AT', 47.86500000, 16.17119000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q670748'),
(3856, 'Wördern', 2065, '3', 15, 'AT', 48.33400000, 16.21016000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q670748'),
(3857, 'Wörgl', 2064, '7', 15, 'AT', 47.48906000, 12.06174000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q253639'),
(3858, 'Wörschach', 2059, '6', 15, 'AT', 47.55000000, 14.15000000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q253639'),
(3859, 'Wörterberg', 2062, '1', 15, 'AT', 47.21667000, 16.10000000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q662336'),
(3860, 'Wörth an der Lafnitz', 2059, '6', 15, 'AT', 47.21368000, 16.08081000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q689708'),
(3861, 'Würflach', 2065, '3', 15, 'AT', 47.77648000, 16.05463000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q673674'),
(3862, 'Würmla', 2065, '3', 15, 'AT', 48.25497000, 15.86031000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q673674'),
(3863, 'Ybbs an der Donau', 2065, '3', 15, 'AT', 48.16667000, 15.08333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q278447'),
(3864, 'Ybbsitz', 2065, '3', 15, 'AT', 47.94745000, 14.89180000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q661666'),
(3865, 'Ysper', 2065, '3', 15, 'AT', 48.28865000, 15.06131000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q661666'),
(3866, 'Zagersdorf', 2062, '1', 15, 'AT', 47.76471000, 16.51382000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q662589'),
(3867, 'Zams', 2064, '7', 15, 'AT', 47.15844000, 10.58970000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q145997'),
(3868, 'Zederhaus', 2061, '5', 15, 'AT', 47.15570000, 13.50576000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q145997'),
(3869, 'Zeillern', 2065, '3', 15, 'AT', 48.13029000, 14.80760000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q184939'),
(3870, 'Zeiselmauer', 2065, '3', 15, 'AT', 48.32852000, 16.17565000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q184939'),
(3871, 'Zell', 2064, '7', 15, 'AT', 47.58333000, 12.15000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q184939'),
(3872, 'Zell am Moos', 2058, '4', 15, 'AT', 47.90000000, 13.31667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187540'),
(3873, 'Zell am Pettenfirst', 2058, '4', 15, 'AT', 48.08009000, 13.59936000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187556'),
(3874, 'Zell am See', 2061, '5', 15, 'AT', 47.32556000, 12.79444000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187572'),
(3875, 'Zell am Ziller', 2064, '7', 15, 'AT', 47.23333000, 11.88333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187565'),
(3876, 'Zell an der Pram', 2058, '4', 15, 'AT', 48.31625000, 13.62923000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187574'),
(3877, 'Zell-Arzberg', 2065, '3', 15, 'AT', 47.95191000, 14.79322000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187574'),
(3878, 'Zell-Markt', 2065, '3', 15, 'AT', 47.95878000, 14.78318000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187574'),
(3879, 'Zellberg', 2064, '7', 15, 'AT', 47.23333000, 11.85000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187574'),
(3880, 'Zellerndorf', 2065, '3', 15, 'AT', 48.69657000, 15.95841000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q187779'),
(3881, 'Zeltweg', 2059, '6', 15, 'AT', 47.18333000, 14.75000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q189268'),
(3882, 'Zerlach', 2059, '6', 15, 'AT', 46.94593000, 15.65093000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q191685'),
(3883, 'Zettling', 2059, '6', 15, 'AT', 46.95220000, 15.43420000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q191685'),
(3884, 'Zeutschach', 2059, '6', 15, 'AT', 47.06667000, 14.36667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q197030'),
(3885, 'Ziersdorf', 2065, '3', 15, 'AT', 48.53030000, 15.92691000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q197030'),
(3886, 'Zillingdorf', 2065, '3', 15, 'AT', 47.85000000, 16.33333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q203186'),
(3887, 'Zillingtal', 2062, '1', 15, 'AT', 47.81442000, 16.40928000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q203191'),
(3888, 'Zirl', 2064, '7', 15, 'AT', 47.27410000, 11.23961000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q203191'),
(3889, 'Zistersdorf', 2065, '3', 15, 'AT', 48.54252000, 16.76136000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q206085'),
(3890, 'Zurndorf', 2062, '1', 15, 'AT', 47.98314000, 17.00315000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q230459'),
(3891, 'Zwentendorf', 2065, '3', 15, 'AT', 48.34530000, 15.91026000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q243860'),
(3892, 'Zwettl Stadt', 2065, '3', 15, 'AT', 48.60726000, 15.16714000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q243860'),
(3893, 'Zwettl an der Rodl', 2058, '4', 15, 'AT', 48.46552000, 14.27133000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q244502'),
(3894, 'Zwölfaxing', 2065, '3', 15, 'AT', 48.10990000, 16.46267000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q245210'),
(3895, 'Zöbern', 2065, '3', 15, 'AT', 47.51459000, 16.13111000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q247656'),
(3896, 'Zöblen', 2064, '7', 15, 'AT', 47.50772000, 10.47971000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q247656'),
(3897, 'Öblarn', 2059, '6', 15, 'AT', 47.45938000, 13.99023000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q293898'),
(3898, 'Ötztal-Bahnhof', 2064, '7', 15, 'AT', 47.23333000, 10.85000000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q293898'),
(3899, 'Übelbach', 2059, '6', 15, 'AT', 47.22534000, 15.23615000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q330855'),
(3900, 'Übersaxen', 2063, '8', 15, 'AT', 47.25284000, 9.67080000, '2019-10-05 22:28:36', '2020-05-01 17:22:35', 1, 'Q330855'),
(3901, 'Übersbach', 2059, '6', 15, 'AT', 47.02318000, 16.05517000, '2019-10-05 22:28:36', '2020-05-01 17:22:34', 1, 'Q333230'),
(3902, 'Abbey', 3906, 'WA', 14, 'AU', -33.66364000, 115.25635000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q333230'),
(3903, 'Abbotsbury', 3909, 'NSW', 14, 'AU', -33.87010000, 150.86119000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4664280'),
(3904, 'Abbotsford', 3903, 'VIC', 14, 'AU', -37.80000000, 145.00000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q306710'),
(3905, 'Abbotsford', 3909, 'NSW', 14, 'AU', -33.84889000, 151.12801000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4664298'),
(3906, 'Abercrombie', 3909, 'NSW', 14, 'AU', -33.38867000, 149.54580000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4664298'),
(3907, 'Aberdare', 3909, 'NSW', 14, 'AU', -32.84112000, 151.38168000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4666845'),
(3908, 'Aberdeen', 3909, 'NSW', 14, 'AU', -32.16588000, 150.89003000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q2821571'),
(3909, 'Aberfeldie', 3903, 'VIC', 14, 'AU', -37.75959000, 144.89740000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q319540'),
(3910, 'Aberfoyle Park', 3904, 'SA', 14, 'AU', -35.07680000, 138.59163000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q319540'),
(3911, 'Aberglasslyn', 3909, 'NSW', 14, 'AU', -32.70000000, 151.53333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4667103'),
(3912, 'Abermain', 3909, 'NSW', 14, 'AU', -32.80740000, 151.42750000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4667131'),
(3913, 'Acacia Gardens', 3909, 'NSW', 14, 'AU', -33.73159000, 150.91636000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q965388'),
(3914, 'Acacia Ridge', 3905, 'QLD', 14, 'AU', -27.58333000, 153.03333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4670845'),
(3915, 'Acton', 3907, 'ACT', 14, 'AU', -35.27767000, 149.11829000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q482055'),
(3916, 'Acton Park', 3908, 'TAS', 14, 'AU', -42.87932000, 147.48459000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4677678'),
(3917, 'Adamstown', 3909, 'NSW', 14, 'AU', -32.93824000, 151.72541000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q10400942'),
(3918, 'Adamstown Heights', 3909, 'NSW', 14, 'AU', -32.94906000, 151.71009000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4680523'),
(3919, 'Adelaide', 3904, 'SA', 14, 'AU', -34.92866000, 138.59863000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q5112'),
(3920, 'Adelaide Hills', 3904, 'SA', 14, 'AU', -34.90103000, 138.85457000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q353997'),
(3921, 'Adelaide city centre', 3904, 'SA', 14, 'AU', -34.92873000, 138.60334000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q353997'),
(3922, 'Agnes Water', 3905, 'QLD', 14, 'AU', -24.21190000, 151.90350000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q2826929'),
(3923, 'Ainslie', 3907, 'ACT', 14, 'AU', -35.26255000, 149.14370000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4530072'),
(3924, 'Airds', 3909, 'NSW', 14, 'AU', -34.08599000, 150.83322000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q965457'),
(3925, 'Airlie Beach', 3905, 'QLD', 14, 'AU', -20.26751000, 148.71471000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q408765'),
(3926, 'Airport West', 3903, 'VIC', 14, 'AU', -37.72470000, 144.88126000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4698960'),
(3927, 'Aitkenvale', 3905, 'QLD', 14, 'AU', -19.30142000, 146.77082000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4699323'),
(3928, 'Alawa', 3910, 'NT', 14, 'AU', -12.37954000, 130.87320000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4708892'),
(3929, 'Albanvale', 3903, 'VIC', 14, 'AU', -37.74609000, 144.76856000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q1500097'),
(3930, 'Albany', 3906, 'WA', 14, 'AU', -34.70990000, 118.12345000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q1527935'),
(3931, 'Albany Creek', 3905, 'QLD', 14, 'AU', -27.35364000, 152.96848000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q705686'),
(3932, 'Albany city centre', 3906, 'WA', 14, 'AU', -35.02479000, 117.88472000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q705686'),
(3933, 'Albert Park', 3904, 'SA', 14, 'AU', -34.87201000, 138.51966000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q705686'),
(3934, 'Albert Park', 3903, 'VIC', 14, 'AU', -37.84107000, 144.95198000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q591771'),
(3935, 'Alberton', 3904, 'SA', 14, 'AU', -34.85925000, 138.52138000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4712262'),
(3936, 'Albion', 3903, 'VIC', 14, 'AU', -37.78017000, 144.81724000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4712478'),
(3937, 'Albion Park', 3909, 'NSW', 14, 'AU', -34.57132000, 150.77568000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4712537'),
(3938, 'Albion Park Rail', 3909, 'NSW', 14, 'AU', -34.56634000, 150.79177000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4712542'),
(3939, 'Albury', 3909, 'NSW', 14, 'AU', -36.07482000, 146.92401000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q331764'),
(3940, 'Albury Municipality', 3909, 'NSW', 14, 'AU', -36.01494000, 146.95684000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q1719401'),
(3941, 'Aldavilla', 3909, 'NSW', 14, 'AU', -31.08180000, 152.76790000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q1719401'),
(3942, 'Alderley', 3905, 'QLD', 14, 'AU', -27.42553000, 153.00102000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q1719401'),
(3943, 'Aldershot', 3905, 'QLD', 14, 'AU', -25.46312000, 152.66348000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q1719401'),
(3944, 'Aldgate', 3904, 'SA', 14, 'AU', -35.01667000, 138.73333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3945, 'Aldinga Beach', 3904, 'SA', 14, 'AU', -35.27826000, 138.45802000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3946, 'Alexander Heights', 3906, 'WA', 14, 'AU', -31.82758000, 115.86501000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3947, 'Alexandra', 3903, 'VIC', 14, 'AU', -37.19132000, 145.71120000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3948, 'Alexandra Headland', 3905, 'QLD', 14, 'AU', -26.67154000, 153.10058000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3949, 'Alexandra Hills', 3905, 'QLD', 14, 'AU', -27.53221000, 153.22889000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3950, 'Alexandria', 3909, 'NSW', 14, 'AU', -33.89989000, 151.19951000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3951, 'Alexandrina', 3904, 'SA', 14, 'AU', -35.33769000, 138.83704000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q909559'),
(3952, 'Alfords Point', 3909, 'NSW', 14, 'AU', -33.98744000, 151.02526000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q964242'),
(3953, 'Alfred Cove', 3906, 'WA', 14, 'AU', -32.03316000, 115.81259000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3954, 'Alfredton', 3903, 'VIC', 14, 'AU', -37.56667000, 143.81667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, '********'),
(3955, 'Algester', 3905, 'QLD', 14, 'AU', -27.61279000, 153.03239000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4724190'),
(3956, 'Alice River', 3905, 'QLD', 14, 'AU', -19.33437000, 146.61391000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4726066'),
(3957, 'Alice Springs', 3910, 'NT', 14, 'AU', -23.69748000, 133.88362000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q17872'),
(3958, 'Alkimos', 3906, 'WA', 14, 'AU', -31.63039000, 115.68638000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4727703'),
(3959, 'Allambie Heights', 3909, 'NSW', 14, 'AU', -33.76655000, 151.24981000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q964412'),
(3960, 'Allansford', 3903, 'VIC', 14, 'AU', -38.38639000, 142.59431000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q2648090'),
(3961, 'Allawah', 3909, 'NSW', 14, 'AU', -33.97257000, 151.11440000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q965368'),
(3962, 'Allenby Gardens', 3904, 'SA', 14, 'AU', -34.89766000, 138.55425000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4731978'),
(3963, 'Allenstown', 3905, 'QLD', 14, 'AU', -23.39403000, 150.50393000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4732035'),
(3964, 'Alligator Creek', 3905, 'QLD', 14, 'AU', -19.39176000, 146.93784000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4732035'),
(3965, 'Allora', 3905, 'QLD', 14, 'AU', -28.03484000, 151.98058000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q3049454'),
(3966, 'Alphington', 3903, 'VIC', 14, 'AU', -37.78333000, 145.03333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q2651034'),
(3967, 'Alpine', 3903, 'VIC', 14, 'AU', -36.83412000, 146.97936000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q1419674'),
(3968, 'Alstonville', 3909, 'NSW', 14, 'AU', -28.84186000, 153.44022000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q964216'),
(3969, 'Alton Downs', 3905, 'QLD', 14, 'AU', -23.30000000, 150.35000000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q964216'),
(3970, 'Altona', 3903, 'VIC', 14, 'AU', -37.86964000, 144.83036000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q443946'),
(3971, 'Altona Meadows', 3903, 'VIC', 14, 'AU', -37.88413000, 144.78367000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q443960'),
(3972, 'Altona North', 3903, 'VIC', 14, 'AU', -37.83487000, 144.84735000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q443965'),
(3973, 'Alyangula', 3910, 'NT', 14, 'AU', -13.85413000, 136.42129000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q443965'),
(3974, 'Amaroo', 3907, 'ACT', 14, 'AU', -35.16959000, 149.12802000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4740326'),
(3975, 'Ambarvale', 3909, 'NSW', 14, 'AU', -34.08942000, 150.79656000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q965338'),
(3976, 'Anangu Pitjantjatjara', 3904, 'SA', 14, 'AU', -27.05797000, 131.37314000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q485826'),
(3977, 'Andergrove', 3905, 'QLD', 14, 'AU', -21.08333000, 149.18333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q485826'),
(3978, 'Andrews Farm', 3904, 'SA', 14, 'AU', -34.67632000, 138.66197000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4759071'),
(3979, 'Angaston', 3904, 'SA', 14, 'AU', -34.50129000, 139.04625000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q530643'),
(3980, 'Angle Park', 3904, 'SA', 14, 'AU', -34.85989000, 138.55798000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q530643'),
(3981, 'Angle Vale', 3904, 'SA', 14, 'AU', -34.64098000, 138.64610000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4763354'),
(3982, 'Anglesea', 3903, 'VIC', 14, 'AU', -38.40730000, 144.18587000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q540478'),
(3983, 'Anna Bay', 3909, 'NSW', 14, 'AU', -32.78135000, 152.08586000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4766841'),
(3984, 'Annandale', 3909, 'NSW', 14, 'AU', -33.88333000, 151.16667000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q2915428'),
(3985, 'Annandale', 3905, 'QLD', 14, 'AU', -19.31503000, 146.79069000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4767920'),
(3986, 'Annangrove', 3909, 'NSW', 14, 'AU', -33.65758000, 150.94755000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q965434'),
(3987, 'Annerley', 3905, 'QLD', 14, 'AU', -27.51228000, 153.03248000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4769012'),
(3988, 'Anstead', 3905, 'QLD', 14, 'AU', -27.53781000, 152.86187000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4770762'),
(3989, 'Anula', 3910, 'NT', 14, 'AU', -12.39125000, 130.89047000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4777772'),
(3990, 'Apollo Bay', 3903, 'VIC', 14, 'AU', -38.75940000, 143.67219000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q619097'),
(3991, 'Appin', 3909, 'NSW', 14, 'AU', -34.20347000, 150.78644000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q770484'),
(3992, 'Applecross', 3906, 'WA', 14, 'AU', -32.01667000, 115.83333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4781322'),
(3993, 'Araluen', 3910, 'NT', 14, 'AU', -23.69601000, 133.85400000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4781322'),
(3994, 'Arana Hills', 3905, 'QLD', 14, 'AU', -27.39808000, 152.95797000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4781322'),
(3995, 'Aranda', 3907, 'ACT', 14, 'AU', -35.25817000, 149.08040000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q288948'),
(3996, 'Ararat', 3903, 'VIC', 14, 'AU', -37.48925000, 142.82239000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q626313'),
(3997, 'Arcadia', 3909, 'NSW', 14, 'AU', -33.61667000, 151.03333000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4785110'),
(3998, 'Arcadia vale', 3909, 'NSW', 14, 'AU', -33.06052000, 151.58408000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4785110'),
(3999, 'Ardeer', 3903, 'VIC', 14, 'AU', -37.77586000, 144.80144000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q638779'),
(4000, 'Ardross', 3906, 'WA', 14, 'AU', -32.02696000, 115.83548000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q4788256'),
(4001, 'Ardrossan', 3904, 'SA', 14, 'AU', -34.42217000, 137.91907000, '2019-10-05 22:28:36', '2019-10-05 22:28:36', 1, 'Q138398'),
(4002, 'Argenton', 3909, 'NSW', 14, 'AU', -32.93505000, 151.63064000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4789513'),
(4003, 'Armadale', 3906, 'WA', 14, 'AU', -32.17887000, 116.12750000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q606256'),
(4004, 'Armadale', 3903, 'VIC', 14, 'AU', -37.85544000, 145.02052000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4792515'),
(4005, 'Armidale', 3909, 'NSW', 14, 'AU', -30.50123000, 151.66553000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q606169'),
(4006, 'Armstrong Creek', 3903, 'VIC', 14, 'AU', -38.23121000, 144.37374000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q606169'),
(4007, 'Arncliffe', 3909, 'NSW', 14, 'AU', -33.93627000, 151.14819000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4794512'),
(4008, 'Aroona', 3905, 'QLD', 14, 'AU', -26.78173000, 153.11652000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4795571'),
(4009, 'Artarmon', 3909, 'NSW', 14, 'AU', -33.81667000, 151.18333000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q964478'),
(4010, 'Arundel', 3905, 'QLD', 14, 'AU', -27.93768000, 153.36302000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q964478'),
(4011, 'Ascot', 3905, 'QLD', 14, 'AU', -27.43154000, 153.05800000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q2321177'),
(4012, 'Ascot', 3903, 'VIC', 14, 'AU', -36.70000000, 144.33333000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q2321177'),
(4013, 'Ascot', 3906, 'WA', 14, 'AU', -31.93818000, 115.92258000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q2321177'),
(4014, 'Ascot Park', 3904, 'SA', 14, 'AU', -34.99175000, 138.55768000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4804033'),
(4015, 'Ascot Vale', 3903, 'VIC', 14, 'AU', -37.77988000, 144.92276000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q723342'),
(4016, 'Ashburton', 3903, 'VIC', 14, 'AU', -37.86667000, 145.08333000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q724906'),
(4017, 'Ashburton', 3906, 'WA', 14, 'AU', -22.27674000, 116.90057000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q724939'),
(4018, 'Ashbury', 3909, 'NSW', 14, 'AU', -33.89785000, 151.11960000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q2866493'),
(4019, 'Ashby', 3906, 'WA', 14, 'AU', -31.73255000, 115.79768000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4804697'),
(4020, 'Ashcroft', 3909, 'NSW', 14, 'AU', -33.91598000, 150.89985000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4804757'),
(4021, 'Ashfield', 3906, 'WA', 14, 'AU', -31.91721000, 115.93796000, '2019-10-05 22:28:37', '2019-10-05 22:28:37', 1, 'Q4804950');

