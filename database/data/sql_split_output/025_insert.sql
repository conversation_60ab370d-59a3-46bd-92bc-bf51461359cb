INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(12170, 'Icatu', 2015, 'MA', 31, 'BR', -2.77583000, -44.06583000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2068290'),
(12171, 'Ichu', 2002, 'BA', 31, 'BR', -11.70344000, -39.18722000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2068290'),
(12172, 'Iconha', 2018, 'ES', 31, 'BR', -20.74759000, -40.86570000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q595222'),
(12173, 'I<PERSON>m', 2021, '<PERSON>', 31, '<PERSON>', -20.38003000, -49.20507000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q595222'),
(12174, 'Ic<PERSON>', 2016, 'CE', 31, 'BR', -6.35186000, -38.75674000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1807902'),
(12175, 'Ielmo Marinho', 2019, 'RN', 31, 'BR', -5.76778000, -35.52812000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1807902'),
(12176, 'Iepê', 2021, 'SP', 31, 'BR', -22.65726000, -51.04385000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1807902'),
(12177, 'Igaci', 2007, 'AL', 31, 'BR', -9.53694000, -36.63361000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1808097'),
(12178, 'Igaporã', 2002, 'BA', 31, 'BR', -13.89064000, -42.76046000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1808097'),
(12179, 'Igaracy', 2005, 'PB', 31, 'BR', -7.13856000, -38.13526000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1808097'),
(12180, 'Igarapava', 2021, 'SP', 31, 'BR', -20.03833000, -47.74694000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q578124'),
(12181, 'Igarapé', 1998, 'MG', 31, 'BR', -20.05423000, -44.31636000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q22062513'),
(12182, 'Igarapé Açu', 2009, 'PA', 31, 'BR', -1.12889000, -47.62000000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q22020893'),
(12183, 'Igarapé Grande', 2015, 'MA', 31, 'BR', -4.64974000, -44.83905000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q22020893'),
(12184, 'Igarapé Miri', 2009, 'PA', 31, 'BR', -1.97500000, -48.95972000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q22020894'),
(12185, 'Igarapé do Meio', 2015, 'MA', 31, 'BR', -3.72514000, -45.10975000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q22020894'),
(12186, 'Igarapé-Açu', 2009, 'PA', 31, 'BR', -1.14164000, -47.51366000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q2009058'),
(12187, 'Igarapé-Miri', 2009, 'PA', 31, 'BR', -2.07705000, -49.14772000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q651613'),
(12188, 'Igarassu', 2006, 'PE', 31, 'BR', -7.83417000, -34.90639000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1787619'),
(12189, 'Igaratinga', 1998, 'MG', 31, 'BR', -19.96011000, -44.72106000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1787619'),
(12190, 'Igaratá', 2021, 'SP', 31, 'BR', -23.12994000, -46.14647000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1787619'),
(12191, 'Igaraçu do Tietê', 2021, 'SP', 31, 'BR', -22.50917000, -48.55778000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1120318'),
(12192, 'Igrapiúna', 2002, 'BA', 31, 'BR', -13.85607000, -39.19363000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1120318'),
(12193, 'Igreja Nova', 2007, 'AL', 31, 'BR', -10.16660000, -36.61724000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q594183'),
(12194, 'Igrejinha', 2001, 'RS', 31, 'BR', -29.57444000, -50.79028000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q926647'),
(12195, 'Iguaba Grande', 1997, 'RJ', 31, 'BR', -22.83917000, -42.22889000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1761488'),
(12196, 'Iguape', 2021, 'SP', 31, 'BR', -24.70806000, -47.55528000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1646941'),
(12197, 'Iguaraci', 2006, 'PE', 31, 'BR', -7.84183000, -37.40822000, '2019-10-05 22:35:06', '2025-04-28 16:07:08', 1, 'Q1646941'),
(12198, 'Iguaraçu', 2022, 'PR', 31, 'BR', -23.23375000, -51.85341000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1646941'),
(12199, 'Iguatama', 1998, 'MG', 31, 'BR', -20.14528000, -45.74194000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1646941'),
(12200, 'Iguatemi', 2010, 'MS', 31, 'BR', -23.42596000, -54.55844000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q431778'),
(12201, 'Iguatu', 2016, 'CE', 31, 'BR', -6.35550000, -39.27683000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q931326'),
(12202, 'Iguatu', 2022, 'PR', 31, 'BR', -24.68784000, -53.08691000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2322834'),
(12203, 'Iguaí', 2002, 'BA', 31, 'BR', -14.75639000, -40.08917000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1762385'),
(12204, 'Ijaci', 1998, 'MG', 31, 'BR', -21.18351000, -44.92240000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1762385'),
(12205, 'Ijuí', 2001, 'RS', 31, 'BR', -28.38778000, -53.91472000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q194374'),
(12206, 'Ilha Comprida', 2021, 'SP', 31, 'BR', -24.87792000, -47.74757000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q194374'),
(12207, 'Ilha Grande', 1997, 'RJ', 31, 'BR', -23.15236000, -44.23164000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q194374'),
(12208, 'Ilha Grande', 2008, 'PI', 31, 'BR', -2.84022000, -41.82881000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2078225'),
(12209, 'Ilha Solteira', 2021, 'SP', 31, 'BR', -20.43278000, -51.34250000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1649828'),
(12210, 'Ilha das Flores', 2003, 'SE', 31, 'BR', -10.44455000, -36.56429000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1649828'),
(12211, 'Ilha de Itamaracá', 2006, 'PE', 31, 'BR', -7.74665000, -34.84728000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1649828'),
(12212, 'Ilhabela', 2021, 'SP', 31, 'BR', -23.77806000, -45.35806000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q945061'),
(12213, 'Ilhota', 2014, 'SC', 31, 'BR', -26.85404000, -48.88648000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q945061'),
(12214, 'Ilhéus', 2002, 'BA', 31, 'BR', -14.79364000, -39.03949000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q373705'),
(12215, 'Ilicínea', 1998, 'MG', 31, 'BR', -20.93023000, -45.81821000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q373705'),
(12216, 'Ilópolis', 2001, 'RS', 31, 'BR', -28.91690000, -52.14813000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q373705'),
(12217, 'Imaculada', 2005, 'PB', 31, 'BR', -7.38972000, -37.50917000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2009933'),
(12218, 'Imaruí', 2014, 'SC', 31, 'BR', -28.19624000, -48.82316000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q2009933'),
(12219, 'Imbaú', 2022, 'PR', 31, 'BR', -24.44336000, -50.73882000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q2009933'),
(12220, 'Imbituba', 2014, 'SC', 31, 'BR', -28.24000000, -48.67028000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q986545'),
(12221, 'Imbituva', 2022, 'PR', 31, 'BR', -25.23000000, -50.60444000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2004428'),
(12222, 'Imbuia', 2014, 'SC', 31, 'BR', -27.51118000, -49.40656000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2004428'),
(12223, 'Imbé', 2001, 'RS', 31, 'BR', -29.92719000, -50.12826000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q2004428'),
(12224, 'Imbé de Minas', 1998, 'MG', 31, 'BR', -19.61863000, -41.96773000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2004428'),
(12225, 'Imigrante', 2001, 'RS', 31, 'BR', -29.34023000, -51.75667000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q736649'),
(12226, 'Imperatriz', 2015, 'MA', 31, 'BR', -5.52639000, -47.49167000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q504755'),
(12227, 'Inaciolândia', 2000, 'GO', 31, 'BR', -18.49889000, -49.90164000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q504755'),
(12228, 'Inajá', 2006, 'PE', 31, 'BR', -8.78778000, -37.78853000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q504755'),
(12229, 'Inajá', 2022, 'PR', 31, 'BR', -22.71509000, -52.24126000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q504755'),
(12230, 'Inconfidentes', 1998, 'MG', 31, 'BR', -22.34042000, -46.28495000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q504755'),
(12231, 'Indaiabira', 1998, 'MG', 31, 'BR', -15.57633000, -42.15728000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q504755'),
(12232, 'Indaial', 2014, 'SC', 31, 'BR', -26.89778000, -49.23167000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1784570'),
(12233, 'Indaiatuba', 2021, 'SP', 31, 'BR', -23.08842000, -47.21190000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q616650'),
(12234, 'Independência', 2016, 'CE', 31, 'BR', -5.47187000, -40.32314000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q284334'),
(12235, 'Independência', 2001, 'RS', 31, 'BR', -27.89333000, -54.19784000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q2077905'),
(12236, 'Indiana', 2021, 'SP', 31, 'BR', -22.12587000, -51.26047000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2287960'),
(12237, 'Indianópolis', 1998, 'MG', 31, 'BR', -18.97216000, -47.88555000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q22062505'),
(12238, 'Indianópolis', 2022, 'PR', 31, 'BR', -23.47975000, -52.64153000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2050304'),
(12239, 'Indiaporã', 2021, 'SP', 31, 'BR', -19.95987000, -50.24419000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q2050304'),
(12240, 'Indiara', 2000, 'GO', 31, 'BR', -17.18504000, -49.96826000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2050304'),
(12241, 'Indiaroba', 2003, 'SE', 31, 'BR', -11.51917000, -37.51167000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2013163'),
(12242, 'Indiavaí', 2011, 'MT', 31, 'BR', -15.31484000, -58.62960000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q2013163'),
(12243, 'Ingazeira', 2006, 'PE', 31, 'BR', -7.71728000, -37.42461000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2013163'),
(12244, 'Ingaí', 1998, 'MG', 31, 'BR', -21.41317000, -44.93685000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2013163'),
(12245, 'Ingá', 2005, 'PB', 31, 'BR', -7.25441000, -35.62897000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q929040'),
(12246, 'Inhacorá', 2001, 'RS', 31, 'BR', -27.91435000, -54.03786000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q348258'),
(12247, 'Inhambupe', 2002, 'BA', 31, 'BR', -11.78444000, -38.35306000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q589992'),
(12248, 'Inhangapi', 2009, 'PA', 31, 'BR', -1.46284000, -47.94974000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q589992'),
(12249, 'Inhapi', 2007, 'AL', 31, 'BR', -9.22139000, -37.74861000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2021140'),
(12250, 'Inhapim', 1998, 'MG', 31, 'BR', -19.48349000, -42.10953000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1791428'),
(12251, 'Inhaúma', 1998, 'MG', 31, 'BR', -19.49972000, -44.42415000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q1791428'),
(12252, 'Inhuma', 2008, 'PI', 31, 'BR', -6.66187000, -41.68135000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2104143'),
(12253, 'Inhumas', 2000, 'GO', 31, 'BR', -16.35778000, -49.49611000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q986245'),
(12254, 'Inimutaba', 1998, 'MG', 31, 'BR', -18.71149000, -44.27571000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q986245'),
(12255, 'Inocência', 2010, 'MS', 31, 'BR', -19.67150000, -52.04393000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q986245'),
(12256, 'Inácio Martins', 2022, 'PR', 31, 'BR', -25.64000000, -51.22000000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q986245'),
(12257, 'Inúbia Paulista', 2021, 'SP', 31, 'BR', -21.72002000, -50.94738000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q986245'),
(12258, 'Iomerê', 2014, 'SC', 31, 'BR', -26.98588000, -51.26049000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q986245'),
(12259, 'Ipaba', 1998, 'MG', 31, 'BR', -19.40560000, -42.36072000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1791330'),
(12260, 'Ipameri', 2000, 'GO', 31, 'BR', -17.72194000, -48.15972000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q985981'),
(12261, 'Ipanema', 1998, 'MG', 31, 'BR', -19.75724000, -41.76801000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q782347'),
(12262, 'Ipanguaçu', 2019, 'RN', 31, 'BR', -5.49833000, -36.85500000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q1787080'),
(12263, 'Ipaporanga', 2016, 'CE', 31, 'BR', -4.84741000, -40.81027000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1787080'),
(12264, 'Ipatinga', 1998, 'MG', 31, 'BR', -19.46833000, -42.53667000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q318314'),
(12265, 'Ipaumirim', 2016, 'CE', 31, 'BR', -6.80882000, -38.73227000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1807006'),
(12266, 'Ipaussu', 2021, 'SP', 31, 'BR', -23.08202000, -49.60370000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1807006'),
(12267, 'Ipauçu', 2021, 'SP', 31, 'BR', -23.05667000, -49.62639000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q1648200'),
(12268, 'Ipecaetá', 2002, 'BA', 31, 'BR', -12.33777000, -39.32835000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1648200'),
(12269, 'Iperó', 2021, 'SP', 31, 'BR', -23.35028000, -47.68861000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q1760282'),
(12270, 'Ipeúna', 2021, 'SP', 31, 'BR', -22.44376000, -47.69449000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q1760282'),
(12271, 'Ipiaçu', 1998, 'MG', 31, 'BR', -18.70679000, -49.91749000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q1760282'),
(12272, 'Ipiaú', 2002, 'BA', 31, 'BR', -14.13449000, -39.73948000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1625857'),
(12273, 'Ipiguá', 2021, 'SP', 31, 'BR', -20.63984000, -49.39781000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q1625857'),
(12274, 'Ipira', 2014, 'SC', 31, 'BR', -27.38715000, -51.79708000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1625857'),
(12275, 'Ipiranga', 2022, 'PR', 31, 'BR', -25.01590000, -50.55475000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2064845'),
(12276, 'Ipiranga de Goiás', 2000, 'GO', 31, 'BR', -15.16876000, -49.65047000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q609451'),
(12277, 'Ipiranga do Norte', 2011, 'MT', 31, 'BR', -12.24000000, -56.14000000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2089652'),
(12278, 'Ipiranga do Piauí', 2008, 'PI', 31, 'BR', -6.84634000, -41.74687000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2089652'),
(12279, 'Ipiranga do Sul', 2001, 'RS', 31, 'BR', -27.94208000, -52.43068000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2089652'),
(12280, 'Ipirá', 2002, 'BA', 31, 'BR', -12.15833000, -39.73722000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q2026825'),
(12281, 'Ipixuna', 2004, 'AM', 31, 'BR', -7.11910000, -71.37590000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2026825'),
(12282, 'Ipixuna do Pará', 2009, 'PA', 31, 'BR', -2.98149000, -48.11946000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2026825'),
(12283, 'Ipojuca', 2006, 'PE', 31, 'BR', -8.39889000, -35.06389000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1796462'),
(12284, 'Iporanga', 2021, 'SP', 31, 'BR', -24.50438000, -48.55248000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1796462'),
(12285, 'Iporá', 2000, 'GO', 31, 'BR', -16.45543000, -51.15977000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q675142'),
(12286, 'Iporã', 2022, 'PR', 31, 'BR', -24.04512000, -53.71983000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2027403'),
(12287, 'Iporã do Oeste', 2014, 'SC', 31, 'BR', -26.99157000, -53.49023000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q2027403'),
(12288, 'Ipu', 2016, 'CE', 31, 'BR', -4.34934000, -40.65931000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2026922'),
(12289, 'Ipuaçu', 2014, 'SC', 31, 'BR', -26.67076000, -52.48261000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q2026922'),
(12290, 'Ipubi', 2006, 'PE', 31, 'BR', -7.65194000, -40.14889000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q743369'),
(12291, 'Ipueira', 2019, 'RN', 31, 'BR', -6.78038000, -37.14971000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q743369'),
(12292, 'Ipueiras', 2008, 'PI', 31, 'BR', -7.03333000, -40.45000000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q22021028'),
(12293, 'Ipueiras', 2016, 'CE', 31, 'BR', -4.62127000, -40.82192000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q946408'),
(12294, 'Ipueiras', 2020, 'TO', 31, 'BR', -11.15847000, -48.37886000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q946408'),
(12295, 'Ipuiúna', 1998, 'MG', 31, 'BR', -22.00732000, -46.12468000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q946408'),
(12296, 'Ipumirim', 2014, 'SC', 31, 'BR', -27.04778000, -52.14024000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q946408'),
(12297, 'Ipupiara', 2002, 'BA', 31, 'BR', -11.76946000, -42.43301000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q946408'),
(12298, 'Ipuã', 2021, 'SP', 31, 'BR', -20.43806000, -48.01222000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q1759394'),
(12299, 'Ipê', 2001, 'RS', 31, 'BR', -28.71616000, -51.27787000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q741764'),
(12300, 'Iracema', 2016, 'CE', 31, 'BR', -5.81274000, -38.35285000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2130759'),
(12301, 'Iracema do Oeste', 2022, 'PR', 31, 'BR', -24.43665000, -53.33842000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2130759'),
(12302, 'Iraceminha', 2014, 'SC', 31, 'BR', -26.85495000, -53.31848000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2130759'),
(12303, 'Iracemápolis', 2021, 'SP', 31, 'BR', -22.58056000, -47.51861000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q733787'),
(12304, 'Irajuba', 2002, 'BA', 31, 'BR', -13.17999000, -39.99530000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q733787'),
(12305, 'Iramaia', 2002, 'BA', 31, 'BR', -13.54627000, -40.82834000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q733787'),
(12306, 'Iranduba', 2004, 'AM', 31, 'BR', -3.28472000, -60.18611000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1761947'),
(12307, 'Irani', 2014, 'SC', 31, 'BR', -27.03626000, -51.91783000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1761947'),
(12308, 'Irapuru', 2021, 'SP', 31, 'BR', -21.44903000, -51.33959000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1761947'),
(12309, 'Irapuã', 2021, 'SP', 31, 'BR', -21.25013000, -49.40247000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q1800682'),
(12310, 'Iraquara', 2002, 'BA', 31, 'BR', -12.28624000, -41.59153000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1795271'),
(12311, 'Irará', 2002, 'BA', 31, 'BR', -12.05000000, -38.76667000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1648734'),
(12312, 'Irati', 2022, 'PR', 31, 'BR', -25.46722000, -50.65111000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2064763'),
(12313, 'Irati', 2014, 'SC', 31, 'BR', -26.64113000, -52.89959000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2064763'),
(12314, 'Irauçuba', 2016, 'CE', 31, 'BR', -3.74611000, -39.78333000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q2028660'),
(12315, 'Iraí', 2001, 'RS', 31, 'BR', -27.27078000, -53.23696000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q2028660'),
(12316, 'Iraí de Minas', 1998, 'MG', 31, 'BR', -19.06330000, -47.43698000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2028660'),
(12317, 'Irecê', 2002, 'BA', 31, 'BR', -11.30417000, -41.85583000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1762180'),
(12318, 'Iretama', 2022, 'PR', 31, 'BR', -24.34412000, -52.10655000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1762180'),
(12319, 'Irineópolis', 2014, 'SC', 31, 'BR', -26.33324000, -50.78333000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q1762180'),
(12320, 'Irituia', 2009, 'PA', 31, 'BR', -1.77048000, -47.42145000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2008410'),
(12321, 'Irupi', 2018, 'ES', 31, 'BR', -20.34528000, -41.64111000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1807920'),
(12322, 'Isaías Coelho', 2008, 'PI', 31, 'BR', -7.54521000, -41.64289000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q1807920'),
(12323, 'Israelândia', 2000, 'GO', 31, 'BR', -16.35776000, -50.89499000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1807920'),
(12324, 'Itaara', 2001, 'RS', 31, 'BR', -29.56305000, -53.74584000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1807920'),
(12325, 'Itabaiana', 2005, 'PB', 31, 'BR', -7.32861000, -35.33250000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2103719'),
(12326, 'Itabaiana', 2003, 'SE', 31, 'BR', -10.68500000, -37.42528000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1784050'),
(12327, 'Itabaianinha', 2003, 'SE', 31, 'BR', -11.26994000, -37.79205000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1802007'),
(12328, 'Itabela', 2002, 'BA', 31, 'BR', -16.71635000, -39.57325000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1802007'),
(12329, 'Itaberaba', 2002, 'BA', 31, 'BR', -12.52750000, -40.30694000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1800730'),
(12330, 'Itaberaí', 2000, 'GO', 31, 'BR', -16.02028000, -49.81028000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q527149'),
(12331, 'Itaberá', 2021, 'SP', 31, 'BR', -23.86194000, -49.13722000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q920242'),
(12332, 'Itabi', 2003, 'SE', 31, 'BR', -10.10882000, -37.20004000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1929327'),
(12333, 'Itabira', 1998, 'MG', 31, 'BR', -19.61917000, -43.22694000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1638413'),
(12334, 'Itabirinha', 1998, 'MG', 31, 'BR', -18.52982000, -41.25370000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1638413'),
(12335, 'Itabirito', 1998, 'MG', 31, 'BR', -20.23843000, -43.78016000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q527003'),
(12336, 'Itaboraí', 1997, 'RJ', 31, 'BR', -22.74444000, -42.85944000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q841244'),
(12337, 'Itabuna', 2002, 'BA', 31, 'BR', -14.78556000, -39.28028000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q460750'),
(12338, 'Itacajá', 2020, 'TO', 31, 'BR', -8.55538000, -47.62201000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q460750'),
(12339, 'Itacambira', 1998, 'MG', 31, 'BR', -16.88654000, -43.30766000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q460750'),
(12340, 'Itacarambi', 1998, 'MG', 31, 'BR', -15.10222000, -44.09194000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1790600'),
(12341, 'Itacaré', 2002, 'BA', 31, 'BR', -14.27890000, -38.99584000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q985513'),
(12342, 'Itacoatiara', 2004, 'AM', 31, 'BR', -3.13435000, -58.43353000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1750426'),
(12343, 'Itacorubi', 2014, 'SC', 31, 'BR', -27.58315000, -48.49503000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q10304138'),
(12344, 'Itacuruba', 2006, 'PE', 31, 'BR', -8.76952000, -38.71917000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1804119'),
(12345, 'Itacurubi', 2001, 'RS', 31, 'BR', -28.78540000, -55.26568000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1757590'),
(12346, 'Itaeté', 2002, 'BA', 31, 'BR', -13.08598000, -41.02566000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1757590'),
(12347, 'Itagi', 2002, 'BA', 31, 'BR', -14.16278000, -40.00611000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1795795'),
(12348, 'Itagibá', 2002, 'BA', 31, 'BR', -14.28361000, -39.84278000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q782196'),
(12349, 'Itagimirim', 2002, 'BA', 31, 'BR', -16.13473000, -39.81646000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q782196'),
(12350, 'Itaguajé', 2022, 'PR', 31, 'BR', -22.66099000, -51.98288000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q782196'),
(12351, 'Itaguara', 1998, 'MG', 31, 'BR', -20.37868000, -44.54609000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q782196'),
(12352, 'Itaguari', 2000, 'GO', 31, 'BR', -15.91179000, -49.61834000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q782196'),
(12353, 'Itaguaru', 2000, 'GO', 31, 'BR', -15.76944000, -49.60042000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q782196'),
(12354, 'Itaguatins', 2020, 'TO', 31, 'BR', -5.81408000, -47.65425000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q782196'),
(12355, 'Itaguaçu', 2018, 'ES', 31, 'BR', -19.80194000, -40.85556000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1793795'),
(12356, 'Itaguaçu da Bahia', 2002, 'BA', 31, 'BR', -10.64259000, -42.21083000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1793795'),
(12357, 'Itaguaí', 1997, 'RJ', 31, 'BR', -22.85222000, -43.77528000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q730437'),
(12358, 'Itainópolis', 2008, 'PI', 31, 'BR', -7.45524000, -41.54165000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q662237'),
(12359, 'Itaipava do Grajaú', 2015, 'MA', 31, 'BR', -5.22087000, -45.78512000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q662237'),
(12360, 'Itaipulândia', 2022, 'PR', 31, 'BR', -25.16335000, -54.34354000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q662237'),
(12361, 'Itaipé', 1998, 'MG', 31, 'BR', -17.42738000, -41.65509000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q662237'),
(12362, 'Itaitinga', 2016, 'CE', 31, 'BR', -3.96944000, -38.52806000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2013483'),
(12363, 'Itaituba', 2009, 'PA', 31, 'BR', -5.86018000, -56.23176000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q655734'),
(12364, 'Itaiçaba', 2016, 'CE', 31, 'BR', -4.70171000, -37.82908000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q2006999'),
(12365, 'Itaiópolis', 2014, 'SC', 31, 'BR', -26.47612000, -49.90456000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q2006999'),
(12366, 'Itajaí', 2014, 'SC', 31, 'BR', -26.90778000, -48.66194000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q842713'),
(12367, 'Itajobi', 2021, 'SP', 31, 'BR', -21.31806000, -49.05444000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1759769'),
(12368, 'Itaju', 2021, 'SP', 31, 'BR', -21.92879000, -48.78312000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1759769'),
(12369, 'Itaju do Colônia', 2002, 'BA', 31, 'BR', -15.15505000, -39.71598000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1759769'),
(12370, 'Itajubá', 1998, 'MG', 31, 'BR', -22.42051000, -45.42137000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q985585'),
(12371, 'Itajuípe', 2002, 'BA', 31, 'BR', -14.67806000, -39.37500000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1772508'),
(12372, 'Itajá', 2019, 'RN', 31, 'BR', -5.69944000, -36.79373000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q1772508'),
(12373, 'Itajá', 2000, 'GO', 31, 'BR', -19.11554000, -51.24473000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1772508'),
(12374, 'Italva', 1997, 'RJ', 31, 'BR', -21.38401000, -41.66728000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1772508'),
(12375, 'Itamaracá', 2006, 'PE', 31, 'BR', -7.74778000, -34.82556000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q587114'),
(12376, 'Itamaraju', 2002, 'BA', 31, 'BR', -17.03917000, -39.53111000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q943536'),
(12377, 'Itamarandiba', 1998, 'MG', 31, 'BR', -17.85429000, -42.89409000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1756622'),
(12378, 'Itamarati', 2004, 'AM', 31, 'BR', -6.76472000, -68.03926000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1756622'),
(12379, 'Itamarati de Minas', 1998, 'MG', 31, 'BR', -21.41883000, -42.83387000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1756622'),
(12380, 'Itamari', 2002, 'BA', 31, 'BR', -13.78590000, -39.68416000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1756622'),
(12381, 'Itambacuri', 1998, 'MG', 31, 'BR', -18.18510000, -41.88894000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1754657'),
(12382, 'Itambaracá', 2022, 'PR', 31, 'BR', -22.98385000, -50.42715000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q1754657'),
(12383, 'Itambé', 2002, 'BA', 31, 'BR', -15.24500000, -40.62444000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q686304'),
(12384, 'Itambé', 2022, 'PR', 31, 'BR', -23.68805000, -52.01227000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2283708'),
(12385, 'Itambé', 2006, 'PE', 31, 'BR', -7.44733000, -35.17356000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2283708'),
(12386, 'Itambé do Mato Dentro', 1998, 'MG', 31, 'BR', -19.40318000, -43.34103000, '2019-10-05 22:35:07', '2020-05-01 17:22:37', 1, 'Q2283708'),
(12387, 'Itamogi', 1998, 'MG', 31, 'BR', -21.08608000, -47.05061000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2283708'),
(12388, 'Itamonte', 1998, 'MG', 31, 'BR', -22.28733000, -44.75274000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2283708'),
(12389, 'Itanagra', 2002, 'BA', 31, 'BR', -12.30982000, -38.11328000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q2283708'),
(12390, 'Itanhandu', 1998, 'MG', 31, 'BR', -22.29583000, -44.93472000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q585119'),
(12391, 'Itanhangá', 2011, 'MT', 31, 'BR', -12.17000000, -56.68000000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q2089697'),
(12392, 'Itanhaém', 2021, 'SP', 31, 'BR', -24.18306000, -46.78889000, '2019-10-05 22:35:07', '2020-05-01 17:22:38', 1, 'Q983491'),
(12393, 'Itanhomi', 1998, 'MG', 31, 'BR', -19.15484000, -41.83333000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q983491'),
(12394, 'Itanhém', 2002, 'BA', 31, 'BR', -17.16639000, -40.33000000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q1795733'),
(12395, 'Itaobim', 1998, 'MG', 31, 'BR', -16.56326000, -41.53486000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1795733'),
(12396, 'Itaoca', 2021, 'SP', 31, 'BR', -24.61155000, -48.84390000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1760856'),
(12397, 'Itaocara', 1997, 'RJ', 31, 'BR', -21.77006000, -42.07488000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1804085'),
(12398, 'Itapaci', 2000, 'GO', 31, 'BR', -14.95083000, -49.54944000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1806982'),
(12399, 'Itapagipe', 1998, 'MG', 31, 'BR', -19.75086000, -49.42853000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1806982'),
(12400, 'Itapagé', 2016, 'CE', 31, 'BR', -3.68667000, -39.58611000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q22021101'),
(12401, 'Itapajé', 2016, 'CE', 31, 'BR', -3.73339000, -39.56508000, '2019-10-05 22:35:07', '2020-05-01 17:22:36', 1, 'Q2021022'),
(12402, 'Itaparica', 2002, 'BA', 31, 'BR', -12.90598000, -38.66383000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1772662'),
(12403, 'Itapebi', 2002, 'BA', 31, 'BR', -15.88751000, -39.68792000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q1772662'),
(12404, 'Itapecerica', 1998, 'MG', 31, 'BR', -20.45398000, -45.08814000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q509584'),
(12405, 'Itapecerica da Serra', 2021, 'SP', 31, 'BR', -23.71694000, -46.84917000, '2019-10-05 22:35:07', '2019-10-05 22:35:07', 1, 'Q926662'),
(12406, 'Itapecuru Mirim', 2015, 'MA', 31, 'BR', -3.39250000, -44.35861000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2012733'),
(12407, 'Itapejara d\'Oeste', 2022, 'PR', 31, 'BR', -25.97797000, -52.82583000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2004451'),
(12408, 'Itapema', 2014, 'SC', 31, 'BR', -27.10715000, -48.62440000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1750776'),
(12409, 'Itapemirim', 2018, 'ES', 31, 'BR', -21.01111000, -40.83389000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1806710'),
(12410, 'Itaperuna', 1997, 'RJ', 31, 'BR', -21.20500000, -41.88778000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1675245'),
(12411, 'Itaperuçu', 2022, 'PR', 31, 'BR', -25.22000000, -49.34778000, '2019-10-05 22:35:08', '2020-05-01 17:22:37', 1, 'Q2004535'),
(12412, 'Itapetim', 2006, 'PE', 31, 'BR', -7.39642000, -37.13113000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2004535'),
(12413, 'Itapetinga', 2002, 'BA', 31, 'BR', -15.24889000, -40.24778000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q921827'),
(12414, 'Itapetininga', 2021, 'SP', 31, 'BR', -23.59167000, -48.05306000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q942071'),
(12415, 'Itapeva', 1998, 'MG', 31, 'BR', -22.69829000, -46.21291000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q22062477'),
(12416, 'Itapeva', 2021, 'SP', 31, 'BR', -23.94696000, -48.83835000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1808590'),
(12417, 'Itapevi', 2021, 'SP', 31, 'BR', -23.54889000, -46.93417000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q955510'),
(12418, 'Itapicuru', 2002, 'BA', 31, 'BR', -11.31667000, -38.23333000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1795161'),
(12419, 'Itapipoca', 2016, 'CE', 31, 'BR', -3.49444000, -39.57861000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1797985'),
(12420, 'Itapira', 2021, 'SP', 31, 'BR', -22.43611000, -46.82167000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1760028'),
(12421, 'Itapiranga', 2004, 'AM', 31, 'BR', -2.51600000, -58.55730000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1760028'),
(12422, 'Itapiranga', 2014, 'SC', 31, 'BR', -27.10055000, -53.72164000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1760028'),
(12423, 'Itapirapuã', 2000, 'GO', 31, 'BR', -15.82333000, -50.61333000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q988783'),
(12424, 'Itapirapuã Paulista', 2021, 'SP', 31, 'BR', -24.55985000, -49.22435000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q988783'),
(12425, 'Itapiratins', 2020, 'TO', 31, 'BR', -8.35820000, -47.99703000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q988783'),
(12426, 'Itapissuma', 2006, 'PE', 31, 'BR', -7.77639000, -34.89222000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1804093'),
(12427, 'Itapitanga', 2002, 'BA', 31, 'BR', -14.46369000, -39.62565000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1804093'),
(12428, 'Itapiúna', 2016, 'CE', 31, 'BR', -4.61918000, -38.93130000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q1804093'),
(12429, 'Itaporanga', 2021, 'SP', 31, 'BR', -23.70778000, -49.48972000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1808177'),
(12430, 'Itaporanga', 2005, 'PB', 31, 'BR', -7.30035000, -38.25159000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2079014'),
(12431, 'Itaporanga d\'Ajuda', 2003, 'SE', 31, 'BR', -11.07087000, -37.33296000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2079014'),
(12432, 'Itapororoca', 2005, 'PB', 31, 'BR', -6.81152000, -35.24478000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2079014'),
(12433, 'Itaporã', 2010, 'MS', 31, 'BR', -21.92184000, -54.81409000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q2079014'),
(12434, 'Itaporã do Tocantins', 2020, 'TO', 31, 'BR', -8.50178000, -48.75214000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q2079014'),
(12435, 'Itapoá', 2014, 'SC', 31, 'BR', -26.04546000, -48.66869000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q2079014'),
(12436, 'Itapuca', 2001, 'RS', 31, 'BR', -28.77178000, -52.20012000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2079014'),
(12437, 'Itapura', 2021, 'SP', 31, 'BR', -20.59626000, -51.42537000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2079014'),
(12438, 'Itapuranga', 2000, 'GO', 31, 'BR', -15.56944000, -49.93481000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1806805'),
(12439, 'Itapuã do Oeste', 2013, 'RO', 31, 'BR', -9.03389000, -63.23215000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q1802170'),
(12440, 'Itapuí', 2021, 'SP', 31, 'BR', -22.23333000, -48.71917000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q1759716'),
(12441, 'Itapé', 2002, 'BA', 31, 'BR', -14.91851000, -39.48637000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q1759716'),
(12442, 'Itaquaquecetuba', 2021, 'SP', 31, 'BR', -23.45806000, -46.32917000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q330202'),
(12443, 'Itaquara', 2002, 'BA', 31, 'BR', -13.44720000, -39.91030000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q330202'),
(12444, 'Itaqui', 2001, 'RS', 31, 'BR', -29.12528000, -56.55306000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1750103'),
(12445, 'Itaquiraí', 2010, 'MS', 31, 'BR', -23.47080000, -54.19281000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q1750103'),
(12446, 'Itaquitinga', 2006, 'PE', 31, 'BR', -7.66778000, -35.10167000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2010908'),
(12447, 'Itarana', 2018, 'ES', 31, 'BR', -19.87389000, -40.87528000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1806714'),
(12448, 'Itarantim', 2002, 'BA', 31, 'BR', -15.65972000, -40.06556000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1795784'),
(12449, 'Itararé', 2021, 'SP', 31, 'BR', -24.11250000, -49.33167000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q985581'),
(12450, 'Itarema', 2016, 'CE', 31, 'BR', -3.08336000, -39.89698000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q985581'),
(12451, 'Itariri', 2021, 'SP', 31, 'BR', -24.28880000, -47.13325000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q579753'),
(12452, 'Itarumã', 2000, 'GO', 31, 'BR', -18.87992000, -51.32023000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q579753'),
(12453, 'Itati', 2001, 'RS', 31, 'BR', -29.42840000, -50.17775000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q579753'),
(12454, 'Itatiaia', 1997, 'RJ', 31, 'BR', -22.41099000, -44.57621000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q817223'),
(12455, 'Itatiaiuçu', 1998, 'MG', 31, 'BR', -20.22155000, -44.46588000, '2019-10-05 22:35:08', '2020-05-01 17:22:37', 1, 'Q817223'),
(12456, 'Itatiba', 2021, 'SP', 31, 'BR', -23.00583000, -46.83889000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q773065'),
(12457, 'Itatiba do Sul', 2001, 'RS', 31, 'BR', -27.36303000, -52.49074000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q773065'),
(12458, 'Itatim', 2002, 'BA', 31, 'BR', -12.62835000, -39.68234000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q773065'),
(12459, 'Itatinga', 2021, 'SP', 31, 'BR', -23.10167000, -48.61583000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1648194'),
(12460, 'Itatira', 2016, 'CE', 31, 'BR', -4.56784000, -39.57820000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2014416'),
(12461, 'Itatuba', 2005, 'PB', 31, 'BR', -7.37500000, -35.62833000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2008704'),
(12462, 'Itaubal', 1999, 'AP', 31, 'BR', 0.57732000, -50.67833000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q368933'),
(12463, 'Itaueira', 2008, 'PI', 31, 'BR', -7.60333000, -43.02556000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2079025'),
(12464, 'Itauçu', 2000, 'GO', 31, 'BR', -16.21434000, -49.60066000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q2079025'),
(12465, 'Itaverava', 1998, 'MG', 31, 'BR', -20.70813000, -43.59276000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q2079025'),
(12466, 'Itaí', 2021, 'SP', 31, 'BR', -23.41778000, -49.09056000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q1648305'),
(12467, 'Itaíba', 2006, 'PE', 31, 'BR', -8.94750000, -37.42278000, '2019-10-05 22:35:08', '2020-05-01 17:22:37', 1, 'Q2011784'),
(12468, 'Itaú', 2019, 'RN', 31, 'BR', -5.84190000, -37.92490000, '2019-10-05 22:35:08', '2020-05-01 17:22:37', 1, 'Q2011784'),
(12469, 'Itaú de Minas', 1998, 'MG', 31, 'BR', -20.71790000, -46.77932000, '2019-10-05 22:35:08', '2020-05-01 17:22:37', 1, 'Q1756039'),
(12470, 'Itaúba', 2011, 'MT', 31, 'BR', -11.09537000, -55.58848000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q1756039'),
(12471, 'Itaúna', 1998, 'MG', 31, 'BR', -20.07440000, -44.58626000, '2019-10-05 22:35:08', '2020-05-01 17:22:37', 1, 'Q628877'),
(12472, 'Itaúna do Sul', 2022, 'PR', 31, 'BR', -22.73684000, -52.89236000, '2019-10-05 22:35:08', '2020-05-01 17:22:37', 1, 'Q628877'),
(12473, 'Itinga', 1998, 'MG', 31, 'BR', -16.60721000, -41.83181000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q934924'),
(12474, 'Itinga do Maranhão', 2015, 'MA', 31, 'BR', -4.52832000, -47.48859000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q934924'),
(12475, 'Itiquira', 2011, 'MT', 31, 'BR', -17.39441000, -54.76988000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q934924'),
(12476, 'Itirapina', 2021, 'SP', 31, 'BR', -22.25278000, -47.82278000, '2019-10-05 22:35:08', '2019-10-05 22:35:08', 1, 'Q1759703'),
(12477, 'Itirapuã', 2021, 'SP', 31, 'BR', -20.65755000, -47.16859000, '2019-10-05 22:35:08', '2020-05-01 17:22:38', 1, 'Q1760228'),
(12478, 'Itiruçu', 2002, 'BA', 31, 'BR', -13.53167000, -40.15028000, '2019-10-05 22:35:08', '2020-05-01 17:22:36', 1, 'Q609737'),
(12479, 'Itiúba', 2002, 'BA', 31, 'BR', -10.79357000, -39.80381000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q609737'),
(12480, 'Itobi', 2021, 'SP', 31, 'BR', -21.74902000, -46.93051000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q609737'),
(12481, 'Itororó', 2002, 'BA', 31, 'BR', -15.11694000, -40.07028000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1798854'),
(12482, 'Itu', 2021, 'SP', 31, 'BR', -23.26417000, -47.29917000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q957653'),
(12483, 'Ituaçu', 2002, 'BA', 31, 'BR', -13.81333000, -41.29667000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1795113'),
(12484, 'Ituberá', 2002, 'BA', 31, 'BR', -13.73538000, -39.14785000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1795108'),
(12485, 'Itueta', 1998, 'MG', 31, 'BR', -19.37775000, -41.09757000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1795108'),
(12486, 'Ituiutaba', 1998, 'MG', 31, 'BR', -19.01507000, -49.55042000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1756328'),
(12487, 'Itumbiara', 2000, 'GO', 31, 'BR', -18.41917000, -49.21528000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q193350'),
(12488, 'Itumirim', 1998, 'MG', 31, 'BR', -21.28468000, -44.83316000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q193350'),
(12489, 'Itupeva', 2021, 'SP', 31, 'BR', -23.15306000, -47.05778000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q734333'),
(12490, 'Itupiranga', 2009, 'PA', 31, 'BR', -5.23295000, -49.94952000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2008167'),
(12491, 'Ituporanga', 2014, 'SC', 31, 'BR', -27.48272000, -49.49128000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2008167'),
(12492, 'Iturama', 1998, 'MG', 31, 'BR', -19.69460000, -50.38219000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1780277'),
(12493, 'Itutinga', 1998, 'MG', 31, 'BR', -21.34817000, -44.72387000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1780277'),
(12494, 'Ituverava', 2021, 'SP', 31, 'BR', -20.33944000, -47.78056000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1649852'),
(12495, 'Itá', 2014, 'SC', 31, 'BR', -27.24410000, -52.33213000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q1649852'),
(12496, 'Itápolis', 2021, 'SP', 31, 'BR', -21.59556000, -48.81278000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q745307'),
(12497, 'Iuiu', 2002, 'BA', 31, 'BR', -14.65015000, -43.64870000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q745307'),
(12498, 'Ivaiporã', 2022, 'PR', 31, 'BR', -24.28230000, -51.63284000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q745307'),
(12499, 'Ivatuba', 2022, 'PR', 31, 'BR', -23.59716000, -52.19434000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q745307'),
(12500, 'Ivaté', 2022, 'PR', 31, 'BR', -23.33716000, -53.42407000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q745307'),
(12501, 'Ivaí', 2022, 'PR', 31, 'BR', -24.99384000, -50.86192000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q745307'),
(12502, 'Ivinhema', 2010, 'MS', 31, 'BR', -22.38076000, -53.75565000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q745307'),
(12503, 'Ivolândia', 2000, 'GO', 31, 'BR', -16.68830000, -50.92660000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q745307'),
(12504, 'Ivorá', 2001, 'RS', 31, 'BR', -29.50743000, -53.56819000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q745307'),
(12505, 'Ivoti', 2001, 'RS', 31, 'BR', -29.59111000, -51.16056000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q983487'),
(12506, 'Içara', 2014, 'SC', 31, 'BR', -28.71333000, -49.30000000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q1764963'),
(12507, 'Iúna', 2018, 'ES', 31, 'BR', -20.34583000, -41.53583000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1793709'),
(12508, 'Jaboatão', 2006, 'PE', 31, 'BR', -8.18028000, -35.00139000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q22021169'),
(12509, 'Jaboatão dos Guararapes', 2006, 'PE', 31, 'BR', -8.14568000, -34.97381000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q271393'),
(12510, 'Jaborandi', 2002, 'BA', 31, 'BR', -14.03850000, -45.18408000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q271393'),
(12511, 'Jaborandi', 2021, 'SP', 31, 'BR', -20.66618000, -48.40164000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1808261'),
(12512, 'Jaborá', 2014, 'SC', 31, 'BR', -27.12846000, -51.79869000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q1808261'),
(12513, 'Jaboti', 2022, 'PR', 31, 'BR', -23.69532000, -50.07376000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1808261'),
(12514, 'Jaboticaba', 2001, 'RS', 31, 'BR', -27.60969000, -53.27016000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1758334'),
(12515, 'Jaboticabal', 2021, 'SP', 31, 'BR', -21.25472000, -48.32222000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q938162'),
(12516, 'Jaboticatubas', 1998, 'MG', 31, 'BR', -19.41909000, -43.74554000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q938162'),
(12517, 'Jacaraci', 2002, 'BA', 31, 'BR', -14.83523000, -42.40112000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q938162'),
(12518, 'Jacaraú', 2005, 'PB', 31, 'BR', -6.61222000, -35.29278000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q2010372'),
(12519, 'Jacareacanga', 2009, 'PA', 31, 'BR', -6.22222000, -57.75278000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1805252'),
(12520, 'Jacarezinho', 2022, 'PR', 31, 'BR', -23.19072000, -49.95410000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1814491'),
(12521, 'Jacareí', 2021, 'SP', 31, 'BR', -23.30528000, -45.96583000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q655363'),
(12522, 'Jacaré dos Homens', 2007, 'AL', 31, 'BR', -9.67278000, -37.22275000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q655363'),
(12523, 'Jaci', 2021, 'SP', 31, 'BR', -20.94747000, -49.57606000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1760848'),
(12524, 'Jaciara', 2011, 'MT', 31, 'BR', -15.96528000, -54.96833000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1805634'),
(12525, 'Jacinto', 1998, 'MG', 31, 'BR', -16.19209000, -40.33175000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1791600'),
(12526, 'Jacinto Machado', 2014, 'SC', 31, 'BR', -29.01184000, -49.88785000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1791600'),
(12527, 'Jacobina', 2002, 'BA', 31, 'BR', -11.18143000, -40.51372000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1798719'),
(12528, 'Jacobina do Piauí', 2008, 'PI', 31, 'BR', -7.96488000, -41.17396000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1798719'),
(12529, 'Jacuizinho', 2001, 'RS', 31, 'BR', -29.03095000, -53.06796000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q194286'),
(12530, 'Jacundá', 2009, 'PA', 31, 'BR', -4.56883000, -49.22284000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q194286'),
(12531, 'Jacupiranga', 2021, 'SP', 31, 'BR', -24.69250000, -48.00222000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q985506'),
(12532, 'Jacutinga', 2001, 'RS', 31, 'BR', -27.77469000, -52.52224000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2077978'),
(12533, 'Jacutinga', 1998, 'MG', 31, 'BR', -22.29632000, -46.61044000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1804332'),
(12534, 'Jacuí', 1998, 'MG', 31, 'BR', -21.01103000, -46.71842000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1804332'),
(12535, 'Jacuípe', 2007, 'AL', 31, 'BR', -8.87722000, -35.45885000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1804332'),
(12536, 'Jaguapitã', 2022, 'PR', 31, 'BR', -23.07631000, -51.58296000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1804332'),
(12537, 'Jaguaquara', 2002, 'BA', 31, 'BR', -13.53056000, -39.97083000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q733973'),
(12538, 'Jaguarari', 2002, 'BA', 31, 'BR', -10.26389000, -40.19583000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1070926'),
(12539, 'Jaguaraçu', 1998, 'MG', 31, 'BR', -19.63591000, -42.71705000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1070926'),
(12540, 'Jaguaretama', 2016, 'CE', 31, 'BR', -5.48330000, -38.75595000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2027316'),
(12541, 'Jaguari', 2001, 'RS', 31, 'BR', -29.45245000, -54.64626000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2027316'),
(12542, 'Jaguariaíva', 2022, 'PR', 31, 'BR', -24.24423000, -49.70932000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1803599'),
(12543, 'Jaguaribara', 2016, 'CE', 31, 'BR', -5.65027000, -38.51031000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1803599'),
(12544, 'Jaguaribe', 2016, 'CE', 31, 'BR', -5.97256000, -38.68571000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q653619'),
(12545, 'Jaguaripe', 2002, 'BA', 31, 'BR', -13.12834000, -39.01339000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q653619'),
(12546, 'Jaguariúna', 2021, 'SP', 31, 'BR', -22.70556000, -46.98583000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q1645067'),
(12547, 'Jaguaruana', 2016, 'CE', 31, 'BR', -4.81405000, -37.79248000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2013987'),
(12548, 'Jaguaruna', 2014, 'SC', 31, 'BR', -28.62145000, -49.02529000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1764984'),
(12549, 'Jaguarão', 2001, 'RS', 31, 'BR', -32.56611000, -53.37583000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q942201'),
(12550, 'Jaguaré', 2018, 'ES', 31, 'BR', -18.99187000, -39.97521000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q942201'),
(12551, 'Jaicós', 2008, 'PI', 31, 'BR', -7.45645000, -41.22016000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q953959'),
(12552, 'Jales', 2021, 'SP', 31, 'BR', -20.26889000, -50.54583000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1649949'),
(12553, 'Jambeiro', 2021, 'SP', 31, 'BR', -23.28154000, -45.71217000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1649949'),
(12554, 'Jampruca', 1998, 'MG', 31, 'BR', -18.47226000, -41.75735000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1791230'),
(12555, 'Janaúba', 1998, 'MG', 31, 'BR', -15.77877000, -43.36757000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1791340'),
(12556, 'Jandaia', 2000, 'GO', 31, 'BR', -17.12900000, -50.19476000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1791340'),
(12557, 'Jandaia do Sul', 2022, 'PR', 31, 'BR', -23.60306000, -51.64333000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1779910'),
(12558, 'Jandaíra', 2019, 'RN', 31, 'BR', -5.37894000, -36.09833000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1779910'),
(12559, 'Jandaíra', 2002, 'BA', 31, 'BR', -11.61099000, -37.61845000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1779910'),
(12560, 'Jandira', 2021, 'SP', 31, 'BR', -23.54082000, -46.89571000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1646970'),
(12561, 'Janduís', 2019, 'RN', 31, 'BR', -5.93587000, -37.51496000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1759342'),
(12562, 'Jangada', 2011, 'MT', 31, 'BR', -15.33758000, -56.54282000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1759342'),
(12563, 'Janiópolis', 2022, 'PR', 31, 'BR', -24.08843000, -52.80294000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1759342'),
(12564, 'Januária', 1998, 'MG', 31, 'BR', -15.32133000, -45.20135000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q22062457'),
(12565, 'Boa Saúde', 2019, 'RN', 31, 'BR', -6.15714000, -35.61758000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1802783'),
(12566, 'Japaratinga', 2007, 'AL', 31, 'BR', -9.09900000, -35.29631000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1808087'),
(12567, 'Japaratuba', 2003, 'SE', 31, 'BR', -10.59333000, -36.94028000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2013178'),
(12568, 'Japaraíba', 1998, 'MG', 31, 'BR', -20.12931000, -45.53733000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1755923'),
(12569, 'Japeri', 1997, 'RJ', 31, 'BR', -22.64306000, -43.65333000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1787673'),
(12570, 'Japi', 2019, 'RN', 31, 'BR', -6.43798000, -35.92194000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1787673'),
(12571, 'Japira', 2022, 'PR', 31, 'BR', -23.69047000, -50.18011000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1787673'),
(12572, 'Japoatã', 2003, 'SE', 31, 'BR', -10.34667000, -36.80111000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q1112881'),
(12573, 'Japonvar', 1998, 'MG', 31, 'BR', -15.94720000, -44.34835000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q1112881'),
(12574, 'Japorã', 2010, 'MS', 31, 'BR', -23.79410000, -54.54402000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1112881'),
(12575, 'Japurá', 2004, 'AM', 31, 'BR', -1.55349000, -68.24526000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1112881'),
(12576, 'Japurá', 2022, 'PR', 31, 'BR', -23.41860000, -52.56220000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1112881'),
(12577, 'Jaqueira', 2006, 'PE', 31, 'BR', -8.73880000, -35.79850000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2011203'),
(12578, 'Jaquirana', 2001, 'RS', 31, 'BR', -28.93470000, -50.36430000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2011203'),
(12579, 'Jaraguari', 2010, 'MS', 31, 'BR', -20.27238000, -54.23743000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2011203'),
(12580, 'Jaraguá', 2000, 'GO', 31, 'BR', -15.75694000, -49.33444000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q1022458'),
(12581, 'Jaraguá do Sul', 2014, 'SC', 31, 'BR', -26.48611000, -49.06667000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q849584'),
(12582, 'Jaramataia', 2007, 'AL', 31, 'BR', -9.65053000, -36.96036000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q849584'),
(12583, 'Jardim', 2010, 'MS', 31, 'BR', -21.48028000, -56.13806000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q927504'),
(12584, 'Jardim', 2016, 'CE', 31, 'BR', -7.63821000, -39.24037000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q929116'),
(12585, 'Jardim Alegre', 2022, 'PR', 31, 'BR', -24.21451000, -51.72270000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q929116'),
(12586, 'Jardim Olinda', 2022, 'PR', 31, 'BR', -22.56938000, -52.06520000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q929116'),
(12587, 'Jardim Paulista', 2021, 'SP', 31, 'BR', -23.56675000, -46.66439000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q929116'),
(12588, 'Jardim de Angicos', 2019, 'RN', 31, 'BR', -5.63861000, -35.95027000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q929116'),
(12589, 'Jardim de Piranhas', 2019, 'RN', 31, 'BR', -6.37861000, -37.35194000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q368917'),
(12590, 'Jardim do Mulato', 2008, 'PI', 31, 'BR', -6.15734000, -42.47763000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q368917'),
(12591, 'Jardim do Seridó', 2019, 'RN', 31, 'BR', -6.58444000, -36.77444000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q1786973'),
(12592, 'Jardinópolis', 2021, 'SP', 31, 'BR', -21.01778000, -47.76389000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q1798789'),
(12593, 'Jardinópolis', 2014, 'SC', 31, 'BR', -26.73553000, -52.89460000, '2019-10-05 22:35:09', '2020-05-01 17:22:38', 1, 'Q1815648'),
(12594, 'Jari', 2001, 'RS', 31, 'BR', -29.28796000, -54.29784000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q984569'),
(12595, 'Jarinu', 2021, 'SP', 31, 'BR', -23.10139000, -46.72833000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q639848'),
(12596, 'Jaru', 2013, 'RO', 31, 'BR', -10.43889000, -62.46639000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q988755'),
(12597, 'Jataizinho', 2022, 'PR', 31, 'BR', -23.25417000, -50.98000000, '2019-10-05 22:35:09', '2019-10-05 22:35:09', 1, 'Q2004546'),
(12598, 'Jataí', 2000, 'GO', 31, 'BR', -17.85829000, -51.70531000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q963195'),
(12599, 'Jataúba', 2006, 'PE', 31, 'BR', -8.05299000, -36.57103000, '2019-10-05 22:35:09', '2020-05-01 17:22:37', 1, 'Q963195'),
(12600, 'Jateí', 2010, 'MS', 31, 'BR', -22.73989000, -53.81496000, '2019-10-05 22:35:09', '2020-05-01 17:22:36', 1, 'Q963195'),
(12601, 'Jati', 2016, 'CE', 31, 'BR', -7.70470000, -38.94361000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q963195'),
(12602, 'Jatobá', 2006, 'PE', 31, 'BR', -9.18306000, -38.26889000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q2472554'),
(12603, 'Jatobá', 2015, 'MA', 31, 'BR', -5.86233000, -44.26917000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q2472554'),
(12604, 'Jatobá do Piauí', 2008, 'PI', 31, 'BR', -4.80296000, -41.89545000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q2472554'),
(12605, 'Jaupaci', 2000, 'GO', 31, 'BR', -16.16749000, -51.10168000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2472554'),
(12606, 'Jauru', 2011, 'MT', 31, 'BR', -15.32452000, -58.84580000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2472554'),
(12607, 'Jaçanã', 2019, 'RN', 31, 'BR', -6.41673000, -36.19402000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q2472554'),
(12608, 'Jaíba', 1998, 'MG', 31, 'BR', -15.21645000, -43.67032000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q2472554'),
(12609, 'Jaú', 2021, 'SP', 31, 'BR', -22.29639000, -48.55778000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1759235'),
(12610, 'Jaú do Tocantins', 2020, 'TO', 31, 'BR', -12.83943000, -48.63203000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1759235'),
(12611, 'Jeceaba', 1998, 'MG', 31, 'BR', -20.55578000, -44.03107000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1759235'),
(12612, 'Jenipapo de Minas', 1998, 'MG', 31, 'BR', -17.18727000, -42.21222000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1759235'),
(12613, 'Jenipapo dos Vieiras', 2015, 'MA', 31, 'BR', -5.50699000, -45.54241000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1759235'),
(12614, 'Jequeri', 1998, 'MG', 31, 'BR', -20.48079000, -42.61567000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1759235'),
(12615, 'Jequitaí', 1998, 'MG', 31, 'BR', -17.16894000, -44.46191000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1759235'),
(12616, 'Jequitibá', 1998, 'MG', 31, 'BR', -19.21472000, -44.03049000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1759235'),
(12617, 'Jequitinhonha', 1998, 'MG', 31, 'BR', -16.40683000, -41.05778000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q580753'),
(12618, 'Jequiá da Praia', 2007, 'AL', 31, 'BR', -9.91151000, -36.09896000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1808524'),
(12619, 'Jequié', 2002, 'BA', 31, 'BR', -13.85875000, -40.08512000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q630203'),
(12620, 'Jeremoabo', 2002, 'BA', 31, 'BR', -9.92088000, -38.72629000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1761820'),
(12621, 'Jericó', 2005, 'PB', 31, 'BR', -6.50746000, -37.80665000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1761820'),
(12622, 'Jeriquara', 2021, 'SP', 31, 'BR', -20.33455000, -47.57121000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1761820'),
(12623, 'Jerumenha', 2008, 'PI', 31, 'BR', -7.09885000, -43.54675000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1761820'),
(12624, 'Jerônimo Monteiro', 2018, 'ES', 31, 'BR', -20.81243000, -41.39536000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1806739'),
(12625, 'Jesuânia', 1998, 'MG', 31, 'BR', -22.00846000, -45.28235000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1806739'),
(12626, 'Jesuítas', 2022, 'PR', 31, 'BR', -24.41301000, -53.41344000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1806739'),
(12627, 'Jesúpolis', 2000, 'GO', 31, 'BR', -15.96458000, -49.39461000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1806739'),
(12628, 'Jetibá', 2018, 'ES', 31, 'BR', -20.02069000, -40.68145000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q22039895'),
(12629, 'Ji Paraná', 2013, 'RO', 31, 'BR', -10.88528000, -61.95167000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q983815'),
(12630, 'Ji-Paraná', 2013, 'RO', 31, 'BR', -10.83398000, -61.96014000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q983815'),
(12631, 'Jijoca de Jericoacoara', 2016, 'CE', 31, 'BR', -2.87137000, -40.49161000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q983815'),
(12632, 'Jiquiriçá', 2002, 'BA', 31, 'BR', -13.31081000, -39.58091000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q983815'),
(12633, 'Jitaúna', 2002, 'BA', 31, 'BR', -14.01274000, -39.89833000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1798820'),
(12634, 'Joanésia', 1998, 'MG', 31, 'BR', -19.20319000, -42.70665000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1798820'),
(12635, 'Joanópolis', 2021, 'SP', 31, 'BR', -22.93028000, -46.27556000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1760327'),
(12636, 'Joaquim Felício', 1998, 'MG', 31, 'BR', -17.62324000, -44.09366000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1760327'),
(12637, 'Joaquim Gomes', 2007, 'AL', 31, 'BR', -9.10352000, -35.73739000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2027123'),
(12638, 'Joaquim Nabuco', 2006, 'PE', 31, 'BR', -8.62444000, -35.53333000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1401082'),
(12639, 'Joaquim Pires', 2008, 'PI', 31, 'BR', -3.53692000, -42.07750000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q1401082'),
(12640, 'Joaquim Távora', 2022, 'PR', 31, 'BR', -23.42218000, -49.90251000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1401082'),
(12641, 'Joaçaba', 2014, 'SC', 31, 'BR', -27.17806000, -51.50472000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1758467'),
(12642, 'Joaíma', 1998, 'MG', 31, 'BR', -16.80375000, -41.01105000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1756235'),
(12643, 'Joca Claudino', 2005, 'PB', 31, 'BR', -6.48432000, -38.46407000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2098422'),
(12644, 'Joca Marques', 2008, 'PI', 31, 'BR', -3.54211000, -42.43579000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q2098422'),
(12645, 'Joinville', 2014, 'SC', 31, 'BR', -26.30444000, -48.84556000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q156819'),
(12646, 'Jordânia', 1998, 'MG', 31, 'BR', -15.87487000, -40.30532000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1754836'),
(12647, 'Jordão', 2012, 'AC', 31, 'BR', -9.09166000, -71.84069000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1800793'),
(12648, 'Joselândia', 2015, 'MA', 31, 'BR', -5.00747000, -44.73158000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1800793'),
(12649, 'Josenópolis', 1998, 'MG', 31, 'BR', -16.55307000, -42.51701000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q1800793'),
(12650, 'José Boiteux', 2014, 'SC', 31, 'BR', -26.83852000, -49.64618000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q1800793'),
(12651, 'José Bonifácio', 2021, 'SP', 31, 'BR', -21.08986000, -49.77293000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q595976'),
(12652, 'José Gonçalves de Minas', 1998, 'MG', 31, 'BR', -16.90496000, -42.66905000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q595976'),
(12653, 'José Raydan', 1998, 'MG', 31, 'BR', -18.26371000, -42.46528000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q595976'),
(12654, 'José da Penha', 2019, 'RN', 31, 'BR', -6.32865000, -38.32030000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q595976'),
(12655, 'José de Freitas', 2008, 'PI', 31, 'BR', -4.72992000, -42.61373000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q2078167'),
(12656, 'Joviânia', 2000, 'GO', 31, 'BR', -17.78422000, -49.58440000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q2078167'),
(12657, 'João Alfredo', 2006, 'PE', 31, 'BR', -7.85583000, -35.58833000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q786899'),
(12658, 'João Costa', 2008, 'PI', 31, 'BR', -8.54315000, -42.41513000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q786899'),
(12659, 'João Câmara', 2019, 'RN', 31, 'BR', -5.56075000, -35.81502000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q507247'),
(12660, 'João Dias', 2019, 'RN', 31, 'BR', -6.28506000, -37.79365000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q507247'),
(12661, 'João Dourado', 2002, 'BA', 31, 'BR', -11.14883000, -41.40438000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q507247'),
(12662, 'João Lisboa', 2015, 'MA', 31, 'BR', -5.22723000, -47.18260000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q507247'),
(12663, 'João Monlevade', 1998, 'MG', 31, 'BR', -19.83861000, -43.15561000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q985508'),
(12664, 'João Neiva', 2018, 'ES', 31, 'BR', -19.75750000, -40.38556000, '2019-10-05 22:35:10', '2020-05-01 17:22:36', 1, 'Q1982819'),
(12665, 'João Pessoa', 2005, 'PB', 31, 'BR', -7.11500000, -34.86306000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q167436'),
(12666, 'João Pinheiro', 1998, 'MG', 31, 'BR', -17.55732000, -45.97307000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q675677'),
(12667, 'João Ramalho', 2021, 'SP', 31, 'BR', -22.24440000, -50.78747000, '2019-10-05 22:35:10', '2020-05-01 17:22:38', 1, 'Q675677'),
(12668, 'Juara', 2011, 'MT', 31, 'BR', -11.09828000, -57.49159000, '2019-10-05 22:35:10', '2019-10-05 22:35:10', 1, 'Q675677'),
(12669, 'Juarez Távora', 2005, 'PB', 31, 'BR', -7.15975000, -35.57160000, '2019-10-05 22:35:10', '2020-05-01 17:22:37', 1, 'Q675677');

