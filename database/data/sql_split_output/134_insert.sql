INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(67481, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 645, '22', 129, '<PERSON><PERSON>', 41.23814000, 20.77414000, '2019-10-05 23:08:03', '2020-05-01 17:22:59', 1, 'Q3136344'),
(67482, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 690, '34', 129, '<PERSON><PERSON>', 41.98029000, 21.64982000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932441'),
(67483, 'Mile<PERSON>', 660, '08', 129, 'MK', 41.90753000, 21.01601000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q980976'),
(67484, '<PERSON><PERSON><PERSON>', 648, '18', 129, '<PERSON><PERSON>', 41.30925000, 22.43641000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q3084593'),
(67485, '<PERSON><PERSON>sevo', 700, '72', 129, '<PERSON><PERSON>', 41.18500000, 20.70861000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932446'),
(67486, 'Mogila', 653, '53', 129, 'M<PERSON>', 41.10833000, 21.37861000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q3084463'),
(67487, 'Morani', 711, '74', 129, 'MK', 41.90978000, 21.54997000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932450'),
(67488, 'Murtino', 710, '73', 129, 'MK', 41.41537000, 22.72589000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q3135323'),
(67489, 'Negotino', 689, '16', 129, 'MK', 41.87792000, 20.88389000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932464'),
(67490, 'Negotino', 664, '54', 129, 'MK', 41.48456000, 22.09056000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q164954'),
(67491, 'Nerašte', 654, '75', 129, 'MK', 42.10711000, 21.10810000, '2019-10-05 23:08:03', '2020-05-01 17:22:59', 1, 'Q20932467'),
(67492, 'Novaci', 696, '55', 129, 'MK', 41.04197000, 21.45866000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932467'),
(67493, 'Novo Selo', 718, '56', 129, 'MK', 41.41486000, 22.88164000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q2027841'),
(67494, 'Oblesevo', 644, '81', 129, 'MK', 41.88333000, 22.33389000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932489'),
(67495, 'Obršani', 647, '45', 129, 'MK', 41.28168000, 21.36150000, '2019-10-05 23:08:03', '2020-05-01 17:22:59', 1, 'Q3074771'),
(67496, 'Ohrid', 699, '58', 129, 'MK', 41.11722000, 20.80194000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q1223508'),
(67497, 'Ohrid Opština', 699, '58', 129, 'MK', 41.16667000, 20.83333000, '2019-10-05 23:08:03', '2020-05-01 17:22:59', 1, 'Q1125333'),
(67498, 'Oktisi', 700, '72', 129, 'MK', 41.23250000, 20.60722000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q2017632'),
(67499, 'Oraovica', 709, '64', 129, 'MK', 41.62583000, 22.51333000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932501'),
(67500, 'Orashac', 654, '75', 129, 'MK', 42.06250000, 21.79972000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932502'),
(67501, 'Orizari', 723, '42', 129, 'MK', 41.92262000, 22.44628000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20932509'),
(67502, 'Otlja', 659, '48', 129, 'MK', 42.14315000, 21.58675000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q3071709'),
(67503, 'Pehčevo', 685, '60', 129, 'MK', 41.76226000, 22.88921000, '2019-10-05 23:08:03', '2020-05-01 17:22:59', 1, 'Q162952'),
(67504, 'Pirava', 671, '10', 129, 'MK', 41.32042000, 22.53047000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q3135030'),
(67505, 'Pirok', 663, '76', 129, 'MK', 41.91083000, 20.91056000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q3039876'),
(67506, 'Plasnica', 670, '61', 129, 'MK', 41.46722000, 21.12306000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q3040262'),
(67507, 'Podareš', 709, '64', 129, 'MK', 41.61389000, 22.54222000, '2019-10-05 23:08:03', '2020-05-01 17:22:59', 1, 'Q3134776'),
(67508, 'Poroj', 663, '76', 129, 'MK', 42.02913000, 20.99266000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20933185'),
(67509, 'Prilep', 666, '62', 129, 'MK', 41.34514000, 21.55504000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q157032'),
(67510, 'Probishtip', 646, '63', 129, 'MK', 42.00306000, 22.17861000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q1991722'),
(67511, 'Pršovce', 654, '75', 129, 'MK', 42.08336000, 21.05994000, '2019-10-05 23:08:03', '2020-05-01 17:22:59', 1, 'Q1991722'),
(67512, 'Radishani', 694, '09', 129, 'MK', 42.06111000, 21.44778000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q20936042'),
(67513, 'Radolista', 700, '72', 129, 'MK', 41.16417000, 20.62333000, '2019-10-05 23:08:03', '2019-10-05 23:08:03', 1, 'Q1021618'),
(67514, 'Radovis', 709, '64', 129, 'MK', 41.63833000, 22.46472000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q991318'),
(67515, 'Resen', 712, '66', 129, 'MK', 41.08934000, 21.01092000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q161827'),
(67516, 'Romanovci', 683, '47', 129, 'MK', 42.09472000, 21.69306000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3071683'),
(67517, 'Rosoman', 691, '67', 129, 'MK', 41.51671000, 21.94585000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2990800'),
(67518, 'Rostusa', 669, '50', 129, 'MK', 41.61000000, 20.60000000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, '********'),
(67519, 'Rusinovo', 716, '03', 129, 'MK', 41.68728000, 22.80849000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q221320'),
(67520, 'Samokov', 692, '52', 129, 'MK', 41.68331000, 21.14625000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, '*********'),
(67521, 'Saraj', 684, '85', 129, 'MK', 42.00000000, 21.32778000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, '********'),
(67522, 'Sedlarevo', 668, '30', 129, 'MK', 41.88306000, 21.12750000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, '********'),
(67523, 'Sekirnik', 652, '07', 129, 'MK', 41.43999000, 22.79536000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q667351'),
(67524, 'Selce', 663, '76', 129, 'MK', 42.03482000, 20.94035000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, '*********'),
(67525, 'Shtip', 661, '83', 129, 'MK', 41.74583000, 22.19583000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q147243'),
(67526, 'Skopje', 681, '38', 129, 'MK', 41.99646000, 21.43141000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q384'),
(67527, 'Slatino', 654, '75', 129, 'MK', 42.06784000, 21.03902000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, '*********'),
(67528, 'Slepče', 687, '25', 129, 'MK', 41.23333000, 21.17500000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, '*********'),
(67529, 'Sopotnica', 687, '25', 129, 'MK', 41.29594000, 21.15357000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q20936155'),
(67530, 'Srbica', 688, '40', 129, 'MK', 41.58672000, 21.03027000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q20936156'),
(67531, 'Srbinovo', 693, '19', 129, 'MK', 41.70586000, 20.95859000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1007540'),
(67532, 'Sredno Konjare', 698, '59', 129, 'MK', 41.95743000, 21.71494000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q20936157'),
(67533, 'Star Dojran', 697, '26', 129, 'MK', 41.18647000, 22.72030000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q956963'),
(67534, 'Stojakovo', 649, '05', 129, 'MK', 41.15556000, 22.57750000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3135158'),
(67535, 'Strelci', 688, '40', 129, 'MK', 41.54046000, 21.00563000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q20936199'),
(67536, 'Strimnica', 668, '30', 129, 'MK', 41.96719000, 21.03598000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3077647'),
(67537, 'Struga', 700, '72', 129, 'MK', 41.17799000, 20.67784000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q156858'),
(67538, 'Strumica', 710, '73', 129, 'MK', 41.43750000, 22.64333000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q48814'),
(67539, 'Studeničane', 711, '74', 129, 'MK', 41.92208000, 21.53483000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q48814'),
(67540, 'Sušica', 718, '56', 129, 'MK', 41.43982000, 22.83676000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q20931924'),
(67541, 'Sveti Nikole', 640, '69', 129, 'MK', 41.86956000, 21.95274000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q366907'),
(67542, 'Tearce', 654, '75', 129, 'MK', 42.07666000, 21.05310000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q653186'),
(67543, 'Tetovo', 663, '76', 129, 'MK', 42.00973000, 20.97155000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q157024'),
(67544, 'Topolčani', 666, '62', 129, 'MK', 41.22772000, 21.43113000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3136314'),
(67545, 'Tuin', 688, '40', 129, 'MK', 41.61944000, 21.04528000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3137042'),
(67546, 'Usje', 684, '85', 129, 'MK', 41.95871000, 21.45835000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q20931808'),
(67547, 'Vaksince', 659, '48', 129, 'MK', 42.20611000, 21.66306000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1007227'),
(67548, 'Valandovo', 671, '10', 129, 'MK', 41.31744000, 22.56002000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q756645'),
(67549, 'Vasilevo', 658, '11', 129, 'MK', 41.47408000, 22.64301000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2006315'),
(67550, 'Vataša', 713, '36', 129, 'MK', 41.41694000, 22.01889000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q20931817'),
(67551, 'Veles', 651, '13', 129, 'MK', 41.71556000, 21.77556000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q157050'),
(67552, 'Velesta', 700, '72', 129, 'MK', 41.24083000, 20.64389000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q20931820'),
(67553, 'Veljusa', 710, '73', 129, 'MK', 41.47611000, 22.56750000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3135127'),
(67554, 'Vevčani', 662, '12', 129, 'MK', 41.24056000, 20.59333000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3135127'),
(67555, 'Vinica', 672, '14', 129, 'MK', 41.88278000, 22.50917000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q928405'),
(67556, 'Vladimirovo', 716, '03', 129, 'MK', 41.71000000, 22.79278000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1042326'),
(67557, 'Vraneštica', 688, '40', 129, 'MK', 41.44496000, 21.02683000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3084373'),
(67558, 'Vrapčište', 689, '16', 129, 'MK', 41.83439000, 20.88563000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q2535034'),
(67559, 'Vratnica', 678, '35', 129, 'MK', 42.14333000, 21.11694000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3039697'),
(67560, 'Vrutok', 693, '19', 129, 'MK', 41.76861000, 20.83917000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q915991'),
(67561, 'Zajas', 688, '40', 129, 'MK', 41.60722000, 20.93833000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q144465'),
(67562, 'Zelenikovo', 706, '32', 129, 'MK', 41.88413000, 21.58848000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2479406'),
(67563, 'Zelino', 668, '30', 129, 'MK', 41.98028000, 21.06417000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2740572'),
(67564, 'Zletovo', 646, '63', 129, 'MK', 41.98861000, 22.23611000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2731405'),
(67565, 'Zrnovci', 673, '33', 129, 'MK', 41.85417000, 22.44444000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2990830'),
(67566, 'Čair', 704, '79', 129, 'MK', 42.01528000, 21.44111000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q2990830'),
(67567, 'Čaška', 676, '80', 129, 'MK', 41.65056000, 21.66222000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q2990817'),
(67568, 'Čelopek', 660, '08', 129, 'MK', 41.93167000, 21.01333000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q20935802'),
(67569, 'Češinovo', 644, '81', 129, 'MK', 41.87148000, 22.28961000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q20935802'),
(67570, 'Šipkovica', 663, '76', 129, 'MK', 42.03500000, 20.91556000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q391691'),
(67571, 'Šuto Orizare', 680, '84', 129, 'MK', 42.04000000, 21.42500000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q391691'),
(67572, 'Žitoše', 675, '27', 129, 'MK', 41.41991000, 21.29078000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3074920'),
(67573, 'Јурумлери', 690, '34', 129, 'MK', 41.96722000, 21.55694000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3070894'),
(67574, 'Арачиново', 656, '02', 129, 'MK', 42.02639000, 21.56194000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q626840'),
(67575, 'Идризово', 690, '34', 129, 'MK', 41.96083000, 21.57556000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q1027709'),
(67576, 'Клечовце', 647, '45', 129, 'MK', 42.11611000, 21.85722000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3072576'),
(67577, 'Петровец', 698, '59', 129, 'MK', 41.93889000, 21.61500000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3087537'),
(67578, 'Ранковце', 717, '65', 129, 'MK', 42.16964000, 22.11617000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q2990795'),
(67579, 'Слупчане', 659, '48', 129, 'MK', 42.17333000, 21.62778000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3072536'),
(67580, 'Сопиште', 719, '70', 129, 'MK', 41.95472000, 21.42750000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q2564102'),
(67581, 'Старо Нагоричане', 643, '71', 129, 'MK', 42.19806000, 21.82861000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q2731411'),
(67582, 'Чучер - Сандево', 715, '82', 129, 'MK', 42.10361000, 21.38222000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q341735'),
(67583, 'Abeïbara', 257, '8', 134, 'ML', 19.11667000, 1.75000000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q1813226'),
(67584, 'Ansongo', 258, '7', 134, 'ML', 15.65970000, 0.50220000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2620152'),
(67585, 'Araouane', 248, '6', 134, 'ML', 18.90476000, -3.52649000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q626121'),
(67586, 'Bafoulabé', 252, '1', 134, 'ML', 13.80650000, -10.83210000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q799794'),
(67587, 'Bamako', 253, 'BKO', 134, 'ML', 12.65000000, -8.00000000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q799794'),
(67588, 'Banamba', 250, '2', 134, 'ML', 13.54773000, -7.44808000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q572737'),
(67589, 'Bandiagara', 255, '5', 134, 'ML', 14.35005000, -3.61038000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q538753'),
(67590, 'Baroueli', 249, '4', 134, 'ML', 13.07489000, -6.57171000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q538753'),
(67591, 'Bougouni', 254, '3', 134, 'ML', 11.41769000, -7.48323000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q848574'),
(67592, 'Cercle de Bourem', 258, '7', 134, 'ML', 17.71192000, -0.34284000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q474936'),
(67593, 'Cercle de Goundam', 248, '6', 134, 'ML', 18.60035000, -4.99306000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1499253'),
(67594, 'Cercle de San', 249, '4', 134, 'ML', 13.17895000, -5.01617000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1902391'),
(67595, 'Cercle d’Abeïbara', 257, '8', 134, 'ML', 19.48878000, 2.20025000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q319858'),
(67596, 'Dire', 248, '6', 134, 'ML', 16.28017000, -3.31302000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q319858'),
(67598, 'Djénné', 255, '5', 134, 'ML', 13.90608000, -4.55332000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q213507'),
(67599, 'Douentza', 255, '5', 134, 'ML', 15.00155000, -2.94978000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2358526'),
(67600, 'Douentza Cercle', 255, '5', 134, 'ML', 15.06947000, -2.40875000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q940936'),
(67601, 'Gao', 258, '7', 134, 'ML', 16.27167000, -0.04472000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q188904'),
(67602, 'Gao Cercle', 258, '7', 134, 'ML', 16.72237000, 0.43984000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1493783'),
(67603, 'Goundam', 248, '6', 134, 'ML', 16.41453000, -3.67075000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q976923'),
(67604, 'Gourma-Rharous Cercle', 248, '6', 134, 'ML', 16.07979000, -1.76981000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1540214'),
(67605, 'Inékar', 258, '7', 134, 'ML', 15.95944000, 3.14111000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q1988973'),
(67606, 'Kangaba', 250, '2', 134, 'ML', 11.93333000, -8.41667000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1031919'),
(67607, 'Kati', 250, '2', 134, 'ML', 12.74409000, -8.07257000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q771251'),
(67608, 'Kayes', 252, '1', 134, 'ML', 14.44693000, -11.44448000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q325164'),
(67609, 'Kidal', 257, '8', 134, 'ML', 18.44111000, 1.40778000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q650100'),
(67610, 'Kinmparana', 249, '4', 134, 'ML', 12.84217000, -4.92450000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27690425'),
(67611, 'Kita', 252, '1', 134, 'ML', 13.03490000, -9.48950000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q578014'),
(67612, 'Kokofata', 252, '1', 134, 'ML', 12.88333000, -9.95000000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1760816'),
(67613, 'Kolokani', 250, '2', 134, 'ML', 13.57280000, -8.03390000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q642276'),
(67614, 'Kolondiéba', 254, '3', 134, 'ML', 11.08943000, -6.89290000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q1040225'),
(67615, 'Koulikoro', 250, '2', 134, 'ML', 12.86273000, -7.55985000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q740974'),
(67616, 'Koutiala', 254, '3', 134, 'ML', 12.39173000, -5.46421000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q281000'),
(67617, 'Koutiala Cercle', 254, '3', 134, 'ML', 12.35311000, -5.58390000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1538131'),
(67618, 'Ké-Macina', 249, '4', 134, 'ML', 13.96410000, -5.35791000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q1538131'),
(67619, 'Markala', 249, '4', 134, 'ML', 13.70210000, -6.06590000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q976115'),
(67620, 'Mopti', 255, '5', 134, 'ML', 14.48430000, -4.18296000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q211227'),
(67621, 'Niafunké', 248, '6', 134, 'ML', 15.93220000, -3.99060000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q3100146'),
(67622, 'Ntossoni', 254, '3', 134, 'ML', 12.53040000, -5.77003000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3334119'),
(67623, 'Sagalo', 252, '1', 134, 'ML', 12.20000000, -10.70000000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1759932'),
(67624, 'San', 249, '4', 134, 'ML', 13.30335000, -4.89562000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q950460'),
(67625, 'Sikasso', 254, '3', 134, 'ML', 11.31755000, -5.66654000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q722827'),
(67626, 'Sokolo', 249, '4', 134, 'ML', 14.73280000, -6.12190000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2257485'),
(67627, 'Ségou', 249, '4', 134, 'ML', 13.43170000, -6.21570000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q2257485'),
(67628, 'Timbuktu', 248, '6', 134, 'ML', 16.77348000, -3.00742000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q9427'),
(67629, 'Ténenkou', 255, '5', 134, 'ML', 14.45722000, -4.91690000, '2019-10-05 23:08:04', '2020-05-01 17:22:59', 1, 'Q1031849'),
(67630, 'Yorosso', 254, '3', 134, 'ML', 12.35811000, -4.77688000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1031835'),
(67631, 'Bago', 2141, '02', 151, 'MM', 17.33521000, 96.48135000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q800122'),
(67632, 'Bhamo', 2143, '11', 151, 'MM', 24.25256000, 97.23357000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q854791'),
(67633, 'Bogale', 2142, '07', 151, 'MM', 16.29415000, 95.39742000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3358197'),
(67634, 'Chauk', 2136, '03', 151, 'MM', 20.89921000, 94.81784000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2746921'),
(67635, 'Dawei', 2140, '05', 151, 'MM', 14.08230000, 98.19151000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1018167'),
(67636, 'Dellok', 2133, '13', 151, 'MM', 16.04072000, 97.91773000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1018167'),
(67637, 'Falam', 2137, '14', 151, 'MM', 22.91335000, 93.67779000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1208756'),
(67638, 'Hakha', 2137, '14', 151, 'MM', 22.64452000, 93.61076000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1208756'),
(67639, 'Hinthada', 2142, '07', 151, 'MM', 17.64944000, 95.45705000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1185131'),
(67640, 'Hpa-An', 2133, '13', 151, 'MM', 16.88953000, 97.63482000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1209404'),
(67641, 'Kanbe', 2135, '06', 151, 'MM', 16.70728000, 96.00168000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1209404'),
(67642, 'Kawthoung', 2140, '05', 151, 'MM', 9.98238000, 98.55034000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q978611'),
(67643, 'Kayan', 2135, '06', 151, 'MM', 16.90802000, 96.56037000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27699683'),
(67644, 'Klonhtoug', 2133, '13', 151, 'MM', 15.95411000, 98.43250000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27699683'),
(67645, 'Kyaikkami', 2147, '15', 151, 'MM', 16.07686000, 97.56388000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3200882'),
(67646, 'Kyaiklat', 2142, '07', 151, 'MM', 16.44502000, 95.72373000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q6450659'),
(67647, 'Kyaikto', 2147, '15', 151, 'MM', 17.30858000, 97.01124000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q6450666'),
(67648, 'Kyain Seikgyi Township', 2133, '13', 151, 'MM', 15.82288000, 98.25257000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q6450666'),
(67649, 'Kyaukse', 2134, '04', 151, 'MM', 21.60560000, 96.13508000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q2246703'),
(67650, 'Lashio', 2139, '17', 151, 'MM', 22.93590000, 97.74980000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1209397'),
(67651, 'Letpandan', 2141, '02', 151, 'MM', 17.78664000, 95.75076000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q24038948'),
(67652, 'Loikaw', 2144, '12', 151, 'MM', 19.67798000, 97.20975000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1017971'),
(67653, 'Magway', 2136, '03', 151, 'MM', 20.14956000, 94.93246000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q957326'),
(67654, 'Mandalay', 2134, '04', 151, 'MM', 21.97473000, 96.08359000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q185518'),
(67655, 'Martaban', 2147, '15', 151, 'MM', 16.52834000, 97.61570000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1950091'),
(67656, 'Maubin', 2142, '07', 151, 'MM', 16.73148000, 95.65441000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3300083'),
(67657, 'Mawlaik', 2145, '01', 151, 'MM', 23.64254000, 94.40478000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3300083'),
(67658, 'Mawlamyine', 2147, '15', 151, 'MM', 16.49051000, 97.62825000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q685941'),
(67659, 'Mawlamyinegyunn', 2142, '07', 151, 'MM', 16.37720000, 95.26488000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27698203'),
(67660, 'Meiktila', 2134, '04', 151, 'MM', 20.87776000, 95.85844000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27698203'),
(67661, 'Mikenaungea', 2133, '13', 151, 'MM', 15.95846000, 98.42721000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27698203'),
(67662, 'Minbu', 2136, '03', 151, 'MM', 20.18059000, 94.87595000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3826997'),
(67663, 'Mogok', 2134, '04', 151, 'MM', 22.91766000, 96.50982000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3826997'),
(67664, 'Monywa', 2145, '01', 151, 'MM', 22.10856000, 95.13583000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3826997'),
(67665, 'Mudon', 2147, '15', 151, 'MM', 16.25624000, 97.72460000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q6931858'),
(67666, 'Myanaung', 2142, '07', 151, 'MM', 18.28651000, 95.32014000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q6946728'),
(67667, 'Myawadi', 2133, '13', 151, 'MM', 16.68911000, 98.50893000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q615821'),
(67668, 'Myaydo', 2136, '03', 151, 'MM', 19.36838000, 95.21512000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q6947282'),
(67669, 'Myeik', 2140, '05', 151, 'MM', 12.43954000, 98.60028000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1018163'),
(67670, 'Myingyan', 2134, '04', 151, 'MM', 21.46002000, 95.38840000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q974965'),
(67671, 'Myitkyina', 2143, '11', 151, 'MM', 25.38327000, 97.39637000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1062370'),
(67672, 'Nay Pyi Taw', 2146, '18', 151, 'MM', 19.74500000, 96.12972000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q37400'),
(67673, 'Nyaungdon', 2142, '07', 151, 'MM', 17.04459000, 95.63957000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q7070939'),
(67674, 'Nyaunglebin', 2141, '02', 151, 'MM', 17.95363000, 96.72247000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q11794247'),
(67675, 'Nyaungshwe', 2134, '04', 151, 'MM', 20.66084000, 96.93405000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q652202'),
(67676, 'Pakokku', 2136, '03', 151, 'MM', 21.33489000, 95.08438000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q652202'),
(67677, 'Pathein', 2142, '07', 151, 'MM', 16.77919000, 94.73212000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q870103'),
(67678, 'Paungde', 2141, '02', 151, 'MM', 18.49167000, 95.50591000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27697441'),
(67679, 'Pulei', 2133, '13', 151, 'MM', 16.06243000, 97.88280000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q27697441'),
(67680, 'Pyapon', 2142, '07', 151, 'MM', 16.28543000, 95.67882000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3411169'),
(67681, 'Pyay', 2141, '02', 151, 'MM', 18.82464000, 95.22216000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q691747'),
(67682, 'Pyin Oo Lwin', 2134, '04', 151, 'MM', 22.03501000, 96.45683000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q691747'),
(67683, 'Pyinmana', 2146, '18', 151, 'MM', 19.73810000, 96.20742000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q833073'),
(67684, 'Pyu', 2141, '02', 151, 'MM', 18.48130000, 96.43742000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q7263956'),
(67685, 'Sagaing', 2145, '01', 151, 'MM', 21.87870000, 95.97965000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1018173'),
(67686, 'Shwebo', 2145, '01', 151, 'MM', 22.56925000, 95.69818000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1018173'),
(67687, 'Sittwe', 2138, '16', 151, 'MM', 20.14624000, 92.89835000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q738748'),
(67688, 'Syriam', 2135, '06', 151, 'MM', 16.76887000, 96.24503000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q738748'),
(67689, 'Tachilek', 2139, '17', 151, 'MM', 20.44750000, 99.88083000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1018494'),
(67690, 'Tagondaing', 2133, '13', 151, 'MM', 16.06750000, 97.90694000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q30016638'),
(67691, 'Tamoowoug', 2133, '13', 151, 'MM', 16.03447000, 97.91458000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q30016638'),
(67692, 'Taungdwingyi', 2136, '03', 151, 'MM', 20.00650000, 95.54531000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q30016638'),
(67693, 'Taunggyi', 2139, '17', 151, 'MM', 20.78919000, 97.03776000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1075566'),
(67694, 'Taungoo', 2141, '02', 151, 'MM', 18.94291000, 96.43408000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1193412'),
(67695, 'Thanatpin', 2141, '02', 151, 'MM', 17.29136000, 96.57523000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1193412'),
(67696, 'Tharyarwady', 2141, '02', 151, 'MM', 17.65399000, 95.78813000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q7710820'),
(67697, 'Thaton', 2147, '15', 151, 'MM', 16.91867000, 97.37001000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q1209391'),
(67698, 'Thayetmyo', 2136, '03', 151, 'MM', 19.32076000, 95.18272000, '2019-10-05 23:08:04', '2019-10-05 23:08:04', 1, 'Q3139162'),
(67699, 'Thongwa', 2135, '06', 151, 'MM', 16.75998000, 96.52498000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3139162'),
(67700, 'Twante', 2135, '06', 151, 'MM', 16.71047000, 95.92866000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3139162'),
(67701, 'Wakema', 2142, '07', 151, 'MM', 16.60333000, 95.18278000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q7961116'),
(67702, 'Yamethin', 2134, '04', 151, 'MM', 20.43189000, 96.13875000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3571474'),
(67703, 'Yangon', 2135, '06', 151, 'MM', 16.80528000, 96.15611000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q37995'),
(67704, 'Yenangyaung', 2136, '03', 151, 'MM', 20.46504000, 94.87120000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q1185041'),
(67705, 'Altai', 1972, '065', 146, 'MN', 46.37222000, 96.25833000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q433861'),
(67706, 'Altay', 1969, '071', 146, 'MN', 48.29359000, 89.51488000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q2593952'),
(67707, 'Arvayheer', 1965, '055', 146, 'MN', 46.26389000, 102.77500000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q717684'),
(67708, 'Baruun-Urt', 1977, '051', 146, 'MN', 46.68056000, 113.27917000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q809657'),
(67709, 'Bayanhongor', 1976, '069', 146, 'MN', 46.19444000, 100.71806000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q804117'),
(67710, 'Bulgan', 1961, '067', 146, 'MN', 48.81250000, 103.53472000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q945055'),
(67711, 'Choibalsan', 1963, '061', 146, 'MN', 48.07257000, 114.53264000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q318245'),
(67712, 'Choyr', 1978, '064', 146, 'MN', 46.36111000, 108.36111000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q642346'),
(67713, 'Dalandzadgad', 1967, '053', 146, 'MN', 43.57083000, 104.42500000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q822808'),
(67714, 'Darhan', 1962, '037', 146, 'MN', 49.48667000, 105.92278000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q235571'),
(67715, 'Dzuunmod', 1968, '047', 146, 'MN', 47.70694000, 106.95278000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q917690'),
(67716, 'Dzüünharaa', 1980, '049', 146, 'MN', 48.85229000, 106.45786000, '2019-10-05 23:08:05', '2020-05-01 17:23:03', 1, 'Q917690'),
(67717, 'Erdenet', 1966, '035', 146, 'MN', 49.03333000, 104.08333000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q230628'),
(67718, 'Ereencav', 1963, '061', 146, 'MN', 49.88070000, 115.72526000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q230628'),
(67719, 'Hanh', 1975, '041', 146, 'MN', 51.50265000, 100.66395000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q2607700'),
(67720, 'Hanhongor', 1967, '053', 146, 'MN', 43.77345000, 104.47998000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3402358'),
(67721, 'Harhorin', 1965, '055', 146, 'MN', 47.19753000, 102.82379000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3402358'),
(67722, 'Hovd', 1965, '055', 146, 'MN', 44.67024000, 102.17491000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3402358'),
(67723, 'Kharkhorin', 1965, '055', 146, 'MN', 47.19245000, 102.81349000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3402358'),
(67724, 'Khovd', 1964, '043', 146, 'MN', 48.00556000, 91.64194000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q731256'),
(67725, 'Mandalgovi', 1970, '059', 146, 'MN', 45.76250000, 106.27083000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q1000373'),
(67726, 'Murun-kuren', 1975, '041', 146, 'MN', 49.63417000, 100.16250000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q1000373'),
(67727, 'Möngönmorĭt', 1968, '047', 146, 'MN', 48.19504000, 108.48295000, '2019-10-05 23:08:05', '2020-05-01 17:23:03', 1, 'Q3403761'),
(67728, 'Möst', 1964, '043', 146, 'MN', 46.67712000, 92.78521000, '2019-10-05 23:08:05', '2020-05-01 17:23:03', 1, 'Q10950043'),
(67729, 'Nariynteel', 1965, '055', 146, 'MN', 45.95950000, 101.45977000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3402273'),
(67730, 'Nomgon Sum', 1967, '053', 146, 'MN', 42.41462000, 105.05640000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q3402154'),
(67731, 'Sühbaatar', 1980, '049', 146, 'MN', 50.23139000, 106.20778000, '2019-10-05 23:08:05', '2020-05-01 17:23:03', 1, 'Q46319'),
(67732, 'Tsengel', 1975, '041', 146, 'MN', 49.47833000, 100.88944000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q46319'),
(67733, 'Tsengel', 1969, '071', 146, 'MN', 48.94314000, 89.14358000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q46319'),
(67734, 'Tsetserleg', 1973, '073', 146, 'MN', 47.47500000, 101.45417000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q529834'),
(67735, 'Turt', 1975, '041', 146, 'MN', 51.50725000, 100.66257000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q529834'),
(67736, 'Ulaangom', 1971, '046', 146, 'MN', 49.98111000, 92.06667000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q822815'),
(67737, 'Ulaanhudag', 1968, '047', 146, 'MN', 47.33333000, 104.50000000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q822815'),
(67738, 'Uliastay', 1979, '057', 146, 'MN', 47.74167000, 96.84444000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q943374'),
(67739, 'Undurkhaan', 1974, '039', 146, 'MN', 47.31944000, 110.65556000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q297632'),
(67740, 'Ölgii', 1969, '071', 146, 'MN', 48.96833000, 89.96250000, '2019-10-05 23:08:05', '2020-05-01 17:23:03', 1, 'Q297060'),
(67741, 'Üyönch', 1964, '043', 146, 'MN', 46.04786000, 92.02612000, '2019-10-05 23:08:05', '2020-05-01 17:23:03', 1, 'Q3404181'),
(67742, 'Centipede', 1968, '047', 146, 'MN', 47.70693000, 106.95276000, '2019-10-05 23:08:05', '2024-09-04 19:25:52', 1, 'Q3404181'),
(67743, 'Aioun', 3351, '02', 139, 'MR', 16.66140000, -9.61490000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q175596'),
(67744, 'Akjoujt', 3342, '12', 139, 'MR', 19.74657000, -14.38531000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q175596'),
(67745, 'Aleg', 3339, '05', 139, 'MR', 17.05314000, -13.91312000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q175596'),
(67746, 'Atar', 3344, '07', 139, 'MR', 20.51770000, -13.04857000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q735645'),
(67747, 'Barkéwol', 3349, '03', 139, 'MR', 16.64039000, -12.49849000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q735645'),
(67748, 'Chingueṭṭi', 3344, '07', 139, 'MR', 20.46300000, -12.36200000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q312357'),
(67749, 'Kaédi', 3341, '04', 139, 'MR', 16.15027000, -13.50370000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q312357'),
(67750, 'Kiffa', 3349, '03', 139, 'MR', 16.62073000, -11.40208000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q492778'),
(67751, 'Nouadhibou', 3346, '08', 139, 'MR', 20.94188000, -17.03842000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q184184'),
(67752, 'Néma', 3338, '01', 139, 'MR', 16.61702000, -7.25649000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q184184'),
(67753, 'Rosso', 3348, '06', 139, 'MR', 16.51378000, -15.80503000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q861580'),
(67754, 'Sélibaby', 3350, '10', 139, 'MR', 15.15846000, -12.18430000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q861580'),
(67755, 'Tékane', 3348, '06', 139, 'MR', 16.60175000, -15.34866000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q861580'),
(67756, 'Zouerate', 3340, '11', 139, 'MR', 22.73542000, -12.47134000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q227072'),
(67757, '’Elb el Jmel', 3339, '05', 139, 'MR', 17.01050000, -13.97102000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q227072'),
(67758, 'Attard', 110, '01', 135, 'MT', 35.88972000, 14.44250000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q44557'),
(67759, 'Balzan', 108, '02', 135, 'MT', 35.90028000, 14.45500000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q789794'),
(67760, 'Birkirkara', 97, '04', 135, 'MT', 35.89722000, 14.46111000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q39583'),
(67761, 'Birżebbuġa', 88, '05', 135, 'MT', 35.82583000, 14.52694000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q258028'),
(67762, 'Cospicua', 138, '06', 135, 'MT', 35.88556000, 14.52750000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q757225'),
(67763, 'Dingli', 117, '07', 135, 'MT', 35.86139000, 14.38222000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q905641'),
(67764, 'Fgura', 129, '08', 135, 'MT', 35.87028000, 14.51333000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q44388'),
(67765, 'Floriana', 84, '09', 135, 'MT', 35.89583000, 14.50833000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q832807'),
(67766, 'Fontana', 134, '10', 135, 'MT', 36.03750000, 14.23611000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q20924972'),
(67767, 'Gudja', 106, '16', 135, 'MT', 35.84917000, 14.50306000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q1018153'),
(67768, 'Għajnsielem', 130, '13', 135, 'MT', 36.02639000, 14.28500000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q761675'),
(67769, 'Għarb', 113, '12', 135, 'MT', 36.06000000, 14.20889000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q1026487'),
(67770, 'Ħal Għaxaq', 120, '15', 135, 'MT', 35.84889000, 14.51667000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q426385'),
(67771, 'Gżira', 124, '17', 135, 'MT', 35.90583000, 14.48806000, '2019-10-05 23:08:05', '2020-05-01 17:22:59', 1, 'Q632097'),
(67772, 'Ħal Għargħur', 130, '13', 135, 'MT', 35.92409000, 14.45118000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q1018111'),
(67773, 'Ħaż-Żebbuġ', 85, '65', 135, 'MT', 35.87194000, 14.44111000, '2019-10-05 23:08:05', '2019-10-05 23:08:05', 1, 'Q44573'),
(67774, 'Imdina', 136, '20', 135, 'MT', 35.88694000, 14.40250000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q207277'),
(67775, 'Imsida', 82, '23', 135, 'MT', 35.89250000, 14.48278000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q585187'),
(67776, 'Imtarfa', 126, '24', 135, 'MT', 35.89333000, 14.39889000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q656837'),
(67777, 'Imġarr', 99, '21', 135, 'MT', 35.92056000, 14.36639000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q691220'),
(67778, 'Kalkara', 128, '26', 135, 'MT', 35.88917000, 14.53278000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q533257'),
(67779, 'Kerċem', 137, '27', 135, 'MT', 36.04194000, 14.22667000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20922785'),
(67780, 'Kirkop', 82, '23', 135, 'MT', 35.84222000, 14.48528000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q597464'),
(67781, 'L-Iklin', 93, '19', 135, 'MT', 35.90414000, 14.45415000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q304589'),
(67782, 'Ħal Lija', 126, '24', 135, 'MT', 35.90056000, 14.44639000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q782049'),
(67783, 'Ħal Luqa', 77, '25', 135, 'MT', 35.85889000, 14.48861000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q475585'),
(67784, 'Marsa', 109, '31', 135, 'MT', 35.87917000, 14.49528000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q267431'),
(67785, 'Marsaskala', 137, '27', 135, 'MT', 35.86220000, 14.56701000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q534571'),
(67786, 'Marsaxlokk', 78, '28', 135, 'MT', 35.84194000, 14.54306000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q781921'),
(67787, 'Mellieħa', 96, '34', 135, 'MT', 35.95639000, 14.36222000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20922820'),
(67788, 'Mosta', 139, '51', 135, 'MT', 35.90917000, 14.42556000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q39520'),
(67789, 'Mqabba', 91, '22', 135, 'MT', 35.84763000, 14.46824000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q1017953'),
(67790, 'Munxar', 132, '36', 135, 'MT', 36.03000000, 14.23333000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q587462'),
(67791, 'Mġarr', 99, '21', 135, 'MT', 36.02528000, 14.29500000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q737590'),
(67792, 'Nadur', 133, '37', 135, 'MT', 36.03778000, 14.29417000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q929969'),
(67793, 'Naxxar', 112, '38', 135, 'MT', 35.91361000, 14.44361000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q44397'),
(67794, 'Paola', 115, '39', 135, 'MT', 35.87306000, 14.49889000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q846589'),
(67795, 'Pembroke', 125, '40', 135, 'MT', 35.93056000, 14.47639000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q20924971'),
(67796, 'Pietà', 127, '41', 135, 'MT', 35.89472000, 14.49500000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q744070'),
(67797, 'Qala', 79, '42', 135, 'MT', 36.03611000, 14.30944000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q1088033'),
(67798, 'Qormi', 119, '43', 135, 'MT', 35.87601000, 14.47200000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q39578'),
(67799, 'Qrendi', 111, '44', 135, 'MT', 35.83472000, 14.45833000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q282203'),
(67800, 'Rabat', 101, '45', 135, 'MT', 35.88152000, 14.39872000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q44381'),
(67801, 'San Ġwann', 100, '48', 135, 'MT', 35.90556000, 14.47611000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q39507'),
(67802, 'Santa Luċija', 139, '51', 135, 'MT', 36.04306000, 14.21722000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q1256169'),
(67803, 'San Lawrenz', 87, '50', 135, 'MT', 36.05556000, 14.20361000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q537308'),
(67804, 'San Pawl il-Baħar', 116, '52', 135, 'MT', 35.95064000, 14.41561000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q537308'),
(67805, 'San Ġiljan (St. Julian\'s)', 75, '49', 135, 'MT', 35.91839000, 14.48977000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20922925'),
(67806, 'Sannat', 116, '52', 135, 'MT', 36.02444000, 14.24278000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q602037'),
(67807, 'Santa Luċija, Gozo', 139, '51', 135, 'MT', 35.86278000, 14.50750000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20922931'),
(67808, 'Santa Venera', 94, '53', 135, 'MT', 35.89083000, 14.47417000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q585420'),
(67809, 'Senglea', 77, '25', 135, 'MT', 35.88750000, 14.51694000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q846593'),
(67810, 'Siġġiewi', 98, '55', 135, 'MT', 35.85556000, 14.43639000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20922947'),
(67811, 'Sliema', 104, '56', 135, 'MT', 35.91250000, 14.50194000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q39526'),
(67812, 'Swieqi', 86, '57', 135, 'MT', 35.92250000, 14.48000000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q220667'),
(67813, 'Ħal Tarxien', 103, '59', 135, 'MT', 35.86583000, 14.51500000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q744001'),
(67814, 'Ta’ Xbiex', 122, '58', 135, 'MT', 35.89917000, 14.49444000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20922979'),
(67815, 'Valletta', 95, '60', 135, 'MT', 35.89972000, 14.51472000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q23800'),
(67816, 'Victoria', 101, '45', 135, 'MT', 36.04444000, 14.23972000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q752348'),
(67817, 'Birgu (Vittoriosa)', 107, '03', 135, 'MT', 35.89222000, 14.51833000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q752346'),
(67818, 'Xagħra', 114, '61', 135, 'MT', 36.05000000, 14.26444000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q605343'),
(67819, 'Xewkija', 121, '62', 135, 'MT', 36.03278000, 14.25806000, '2019-10-05 23:08:24', '2019-10-05 23:08:24', 1, 'Q919921'),
(67820, 'Xgħajra', 81, '63', 135, 'MT', 35.88556000, 14.54750000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20923002'),
(67821, 'Ħamrun', 105, '18', 135, 'MT', 35.88472000, 14.48444000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q343001'),
(67822, 'Żabbar', 123, '64', 135, 'MT', 35.87611000, 14.53500000, '2019-10-05 23:08:24', '2020-05-01 17:22:59', 1, 'Q20923006'),
(67823, 'Żebbuġ', 80, '66', 135, 'MT', 36.07222000, 14.23583000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q179243'),
(67824, 'Żejtun', 135, '67', 135, 'MT', 35.85583000, 14.53306000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q44392'),
(67825, 'Żurrieq', 76, '68', 135, 'MT', 35.83111000, 14.47417000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q44417'),
(67826, 'Albion', 3259, 'BL', 140, 'MU', -20.20814000, 57.40766000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764445'),
(67827, 'Amaury', 3261, 'RR', 140, 'MU', -20.13083000, 57.65917000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764414'),
(67828, 'Arsenal', 3250, 'PA', 140, 'MU', -20.10556000, 57.53528000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q24044132'),
(67829, 'Baie aux Huîtres', 3249, 'RO', 140, 'MU', -19.69444000, 63.40833000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q2879542'),
(67830, 'Bambous', 3259, 'BL', 140, 'MU', -20.25667000, 57.40611000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q431990'),
(67831, 'Bambous Virieux', 3264, 'GP', 140, 'MU', -20.34278000, 57.75750000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764389'),
(67832, 'Beau Bassin-Rose Hill', 3263, 'PW', 140, 'MU', -20.23325000, 57.46609000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q813337'),
(67833, 'Beau Vallon', 3264, 'GP', 140, 'MU', -20.41889000, 57.69528000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q813347'),
(67834, 'Bel Air Rivière Sèche', 3254, 'FL', 140, 'MU', -20.25777000, 57.74976000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q4881723'),
(67835, 'Bois des Amourettes', 3264, 'GP', 140, 'MU', -20.36306000, 57.73111000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763578'),
(67836, 'Bon Accueil', 3254, 'FL', 140, 'MU', -20.17083000, 57.65639000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763580'),
(67837, 'Brisée Verdière', 3254, 'FL', 140, 'MU', -20.16389000, 57.64667000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q25406451'),
(67838, 'Calebasses', 3250, 'PA', 140, 'MU', -20.11167000, 57.55389000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764358'),
(67839, 'Camp Diable', 3257, 'SA', 140, 'MU', -20.46278000, 57.57889000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763595'),
(67840, 'Camp Ithier', 3254, 'FL', 140, 'MU', -20.21583000, 57.74556000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764406'),
(67841, 'Camp Thorel', 3253, 'MO', 140, 'MU', -20.21472000, 57.61611000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763596'),
(67842, 'Camp de Masque', 3254, 'FL', 140, 'MU', -20.23694000, 57.66333000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763594'),
(67843, 'Cap Malheureux', 3261, 'RR', 140, 'MU', -19.98417000, 57.61417000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q130238'),
(67844, 'Cargados Carajos', 3251, 'CC', 140, 'MU', -16.60329000, 59.65851000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q130238'),
(67845, 'Cascavelle', 3259, 'BL', 140, 'MU', -20.28694000, 57.40722000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763604'),
(67846, 'Centre de Flacq', 3254, 'FL', 140, 'MU', -20.18972000, 57.71444000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1054191'),
(67847, 'Chamouny', 3257, 'SA', 140, 'MU', -20.48222000, 57.46611000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763611'),
(67848, 'Chemin Grenier', 3257, 'SA', 140, 'MU', -20.48722000, 57.46556000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2962294'),
(67849, 'Cluny', 3264, 'GP', 140, 'MU', -20.36694000, 57.60389000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764431'),
(67850, 'Clémencia', 3254, 'FL', 140, 'MU', -20.26389000, 57.70611000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q27763620'),
(67851, 'Congomah', 3250, 'PA', 140, 'MU', -20.14889000, 57.59083000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764417'),
(67852, 'Cottage', 3261, 'RR', 140, 'MU', -20.05972000, 57.62917000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764429'),
(67853, 'Crève Coeur', 3250, 'PA', 140, 'MU', -20.19111000, 57.55722000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q27763632'),
(67854, 'Curepipe', 3263, 'PW', 140, 'MU', -20.31628000, 57.52594000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1002525'),
(67855, 'Dagotière', 3253, 'MO', 140, 'MU', -20.24476000, 57.56188000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q27763707'),
(67856, 'Dubreuil', 3253, 'MO', 140, 'MU', -20.30132000, 57.59861000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764363'),
(67857, 'Ebene', 3263, 'PW', 140, 'MU', -20.24494000, 57.49163000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q213275'),
(67858, 'Ecroignard', 3254, 'FL', 140, 'MU', -20.22611000, 57.73611000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '*********'),
(67859, 'Espérance Trébuchet', 3261, 'RR', 140, 'MU', -20.06972000, 57.64194000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, '*********'),
(67860, 'Flic en Flac', 3259, 'BL', 140, 'MU', -20.27417000, 57.36306000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67861, 'Fond du Sac', 3250, 'PA', 140, 'MU', -20.04732000, 57.58400000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67862, 'Goodlands', 3261, 'RR', 140, 'MU', -20.03841000, 57.65055000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67863, 'Grand Baie', 3261, 'RR', 140, 'MU', -20.01816000, 57.58015000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q992154'),
(67864, 'Grand Bois', 3257, 'SA', 140, 'MU', -20.41889000, 57.54417000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '*********'),
(67865, 'Grand Gaube', 3261, 'RR', 140, 'MU', -20.00639000, 57.66083000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67866, 'Grand Sable', 3264, 'GP', 140, 'MU', -20.31417000, 57.76444000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '*********'),
(67867, 'Grande Rivière Noire', 3259, 'BL', 140, 'MU', -20.36028000, 57.36611000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, '*********'),
(67868, 'Grande Rivière Sud Est', 3254, 'FL', 140, 'MU', -20.28611000, 57.77583000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, '*********'),
(67869, 'Gros Cailloux', 3259, 'BL', 140, 'MU', -20.20722000, 57.43000000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27764367'),
(67870, 'Lalmatie', 3254, 'FL', 140, 'MU', -20.18972000, 57.66111000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763713'),
(67871, 'Laventure', 3254, 'FL', 140, 'MU', -20.14583000, 57.67667000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27763713'),
(67872, 'Le Hochet', 3250, 'PA', 140, 'MU', -20.13500000, 57.52111000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q11753375'),
(67873, 'Long Mountain', 3250, 'PA', 140, 'MU', -20.14306000, 57.56222000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q11753375'),
(67874, 'Mahébourg', 3264, 'GP', 140, 'MU', -20.40806000, 57.70000000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, '********'),
(67875, 'Mapou', 3261, 'RR', 140, 'MU', -20.07583000, 57.60139000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67876, 'Mare La Chaux', 3254, 'FL', 140, 'MU', -20.19806000, 57.74944000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67877, 'Melrose', 3253, 'MO', 140, 'MU', -20.26972000, 57.63194000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67878, 'Midlands', 3263, 'PW', 140, 'MU', -20.31907000, 57.57016000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, '********'),
(67879, 'Moka', 3253, 'MO', 140, 'MU', -20.21889000, 57.49583000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q282908'),
(67880, 'Morcellement Saint André', 3250, 'PA', 140, 'MU', -20.07199000, 57.56833000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q282908'),
(67881, 'New Grove', 3264, 'GP', 140, 'MU', -20.40861000, 57.61361000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q282908'),
(67882, 'Notre Dame', 3250, 'PA', 140, 'MU', -20.14056000, 57.55306000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q282908'),
(67883, 'Nouvelle France', 3264, 'GP', 140, 'MU', -20.37056000, 57.56111000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q282908'),
(67884, 'Olivia', 3254, 'FL', 140, 'MU', -20.28778000, 57.73097000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q282908'),
(67885, 'Pailles', 3253, 'MO', 140, 'MU', -20.19271000, 57.48826000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q282908'),
(67886, 'Pamplemousses', 3250, 'PA', 140, 'MU', -20.10389000, 57.57028000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q282908'),
(67887, 'Petit Raffray', 3261, 'RR', 140, 'MU', -20.02022000, 57.62296000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q24045809'),
(67888, 'Petite Case Noyale', 3259, 'BL', 140, 'MU', -20.39306000, 57.36500000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q24045809'),
(67889, 'Petite Rivière', 3259, 'BL', 140, 'MU', -20.19551000, 57.44592000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q24045809'),
(67890, 'Piton', 3261, 'RR', 140, 'MU', -20.09028000, 57.63028000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q21402299'),
(67891, 'Plaine Magnien', 3264, 'GP', 140, 'MU', -20.42967000, 57.66968000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q3390948'),
(67892, 'Plaine des Papayes', 3250, 'PA', 140, 'MU', -20.06500000, 57.57250000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q24041555'),
(67893, 'Plaines des Roches', 3261, 'RR', 140, 'MU', -20.11167000, 57.69083000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q24041555'),
(67894, 'Pointe aux Piments', 3250, 'PA', 140, 'MU', -20.06494000, 57.52347000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q24041555'),
(67895, 'Port Louis', 3260, 'PL', 140, 'MU', -20.16194000, 57.49889000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q3929'),
(67896, 'Port Mathurin', 3249, 'RO', 140, 'MU', -19.68333000, 63.41667000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1021638'),
(67897, 'Poste de Flacq', 3254, 'FL', 140, 'MU', -20.16306000, 57.73056000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1021638'),
(67898, 'Providence', 3253, 'MO', 140, 'MU', -20.24472000, 57.61222000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q27762667'),
(67899, 'Quartier Militaire', 3253, 'MO', 140, 'MU', -20.24790000, 57.59737000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q770184'),
(67900, 'Quatre Bornes', 3263, 'PW', 140, 'MU', -20.26381000, 57.47910000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67901, 'Quatre Cocos', 3254, 'FL', 140, 'MU', -20.20151000, 57.77448000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67902, 'Quatre Soeurs', 3254, 'FL', 140, 'MU', -20.29917000, 57.77056000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67903, 'Queen Victoria', 3254, 'FL', 140, 'MU', -20.22000000, 57.70750000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67904, 'Rivière des Anguilles', 3257, 'SA', 140, 'MU', -20.48528000, 57.55083000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q1341194'),
(67905, 'Rivière du Rempart', 3261, 'RR', 140, 'MU', -20.10306000, 57.68472000, '2019-10-05 23:08:25', '2020-05-01 17:22:59', 1, 'Q1341194'),
(67906, 'Roche Terre', 3261, 'RR', 140, 'MU', -20.01861000, 57.64472000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67907, 'Roches Noire', 3261, 'RR', 140, 'MU', -20.11111000, 57.71222000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67908, 'Rose Belle', 3264, 'GP', 140, 'MU', -20.40028000, 57.59667000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67909, 'Saint Aubin', 3257, 'SA', 140, 'MU', -20.49600000, 57.55000000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67910, 'Saint Hubert', 3264, 'GP', 140, 'MU', -20.36417000, 57.63833000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67911, 'Saint Julien', 3254, 'FL', 140, 'MU', -20.22639000, 57.63639000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1341194'),
(67912, 'Saint Pierre', 3253, 'MO', 140, 'MU', -20.21750000, 57.52083000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1154985'),
(67913, 'Sebastopol', 3254, 'FL', 140, 'MU', -20.29070000, 57.68779000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1154985'),
(67914, 'Souillac', 3257, 'SA', 140, 'MU', -20.51667000, 57.51667000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2215428'),
(67915, 'Surinam', 3257, 'SA', 140, 'MU', -20.50972000, 57.50528000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q9348370'),
(67916, 'Tamarin', 3259, 'BL', 140, 'MU', -20.32556000, 57.37056000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2322083'),
(67917, 'Terre Rouge', 3250, 'PA', 140, 'MU', -20.12611000, 57.52444000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2322083'),
(67918, 'The Vale', 3261, 'RR', 140, 'MU', -20.03018000, 57.60219000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2322083'),
(67919, 'Triolet', 3250, 'PA', 140, 'MU', -20.05760000, 57.55025000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q641130'),
(67920, 'Vacoas', 3263, 'PW', 140, 'MU', -20.29806000, 57.47833000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q641130'),
(67921, 'Verdun', 3253, 'MO', 140, 'MU', -20.23417000, 57.55476000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q641130'),
(67922, 'Vingt Cinq', 3248, 'AG', 140, 'MU', -10.38803000, 56.61795000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q641130'),
(67923, 'Dhidhdhoo', 2586, '07', 133, 'MV', 6.88744000, 73.11402000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q641130'),
(67924, 'Felidhoo', 2584, '04', 133, 'MV', 3.47182000, 73.54699000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q4043243'),
(67925, 'Fonadhoo', 2601, '05', 133, 'MV', 1.83243000, 73.50257000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q4043243'),
(67926, 'Funadhoo', 2585, '24', 133, 'MV', 6.15091000, 73.29013000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q4175766'),
(67927, 'Fuvahmulah', 2595, '29', 133, 'MV', -0.29878000, 73.42403000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2060910'),
(67928, 'Guraidhoo', 2596, '26', 133, 'MV', 3.90045000, 73.46623000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2060910'),
(67929, 'Hithadhoo', 2594, '01', 133, 'MV', -0.60000000, 73.08333000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2060910'),
(67930, 'Hulhumale', 2596, '26', 133, 'MV', 4.21169000, 73.54008000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1636296'),
(67931, 'Kudahuvadhoo', 2590, '17', 133, 'MV', 2.67075000, 72.89437000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2339256'),
(67932, 'Kulhudhuffushi', 2597, '23', 133, 'MV', 6.62207000, 73.06998000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q3185775'),
(67933, 'Maafushi', 2596, '26', 133, 'MV', 3.94231000, 73.49070000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q3185775'),
(67934, 'Mahibadhoo', 2606, 'SC', 133, 'MV', 3.75713000, 72.96893000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q3185775'),
(67935, 'Male', 2596, '26', 133, 'MV', 4.17521000, 73.50916000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q9347'),
(67936, 'Manadhoo', 2592, '25', 133, 'MV', 5.76687000, 73.41360000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2599649'),
(67937, 'Meedhoo', 2594, '01', 133, 'MV', -0.58333000, 73.23333000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2599649'),
(67938, 'Muli', 2608, '12', 133, 'MV', 2.91667000, 73.56667000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2599649'),
(67939, 'Naifaru', 2607, '03', 133, 'MV', 5.44438000, 73.36571000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2295035'),
(67940, 'Thinadhoo', 2603, '28', 133, 'MV', 0.53060000, 72.99969000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2147686'),
(67941, 'Ugoofaaru', 2602, '13', 133, 'MV', 5.66812000, 73.03017000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2147686'),
(67942, 'Veymandoo', 2591, '08', 133, 'MV', 2.18772000, 73.09556000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2390425'),
(67943, 'Viligili', 2598, '27', 133, 'MV', 0.75906000, 73.43296000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q2390425'),
(67944, 'Balaka', 3106, 'S', 131, 'MW', -14.97928000, 34.95575000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q804607'),
(67945, 'Balaka District', 3106, 'S', 131, 'MW', -15.04839000, 35.05910000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q870044'),
(67946, 'Blantyre', 3106, 'S', 131, 'MW', -15.78499000, 35.00854000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q188693'),
(67947, 'Blantyre District', 3106, 'S', 131, 'MW', -15.64732000, 34.93956000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1059262'),
(67948, 'Chikwawa', 3106, 'S', 131, 'MW', -16.03352000, 34.80091000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1022541'),
(67949, 'Chikwawa District', 3106, 'S', 131, 'MW', -16.16667000, 34.75000000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q722509'),
(67950, 'Chipoka', 3092, 'C', 131, 'MW', -13.99329000, 34.51566000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1074598'),
(67951, 'Chiradzulu', 3106, 'S', 131, 'MW', -15.67461000, 35.14071000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1022529'),
(67952, 'Chiradzulu District', 3106, 'S', 131, 'MW', -15.75268000, 35.21576000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q722501'),
(67953, 'Chitipa', 3105, 'N', 131, 'MW', -9.70237000, 33.26969000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1074915'),
(67954, 'Chitipa District', 3105, 'N', 131, 'MW', -9.92727000, 33.42541000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1044337'),
(67955, 'Dedza', 3092, 'C', 131, 'MW', -14.37790000, 34.33322000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1182266'),
(67956, 'Dedza District', 3092, 'C', 131, 'MW', -14.26273000, 34.18559000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q667153'),
(67957, 'Dowa', 3092, 'C', 131, 'MW', -13.65399000, 33.93754000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1253056'),
(67958, 'Dowa District', 3092, 'C', 131, 'MW', -13.60098000, 33.82378000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q195356'),
(67959, 'Karonga', 3105, 'N', 131, 'MW', -9.93333000, 33.93333000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q1022512'),
(67960, 'Karonga District', 3105, 'N', 131, 'MW', -10.11153000, 33.88151000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q868819'),
(67961, 'Kasungu', 3092, 'C', 131, 'MW', -13.03333000, 33.48333000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q195309'),
(67962, 'Kasungu District', 3092, 'C', 131, 'MW', -13.00000000, 33.41667000, '2019-10-05 23:08:25', '2019-10-05 23:08:25', 1, 'Q868778'),
(67963, 'Likoma District', 3105, 'N', 131, 'MW', -12.06065000, 34.73608000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q740622'),
(67964, 'Lilongwe', 3092, 'C', 131, 'MW', -13.96692000, 33.78725000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q3876'),
(67965, 'Lilongwe District', 3092, 'C', 131, 'MW', -14.01962000, 33.68407000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1058453'),
(67966, 'Livingstonia', 3105, 'N', 131, 'MW', -10.60602000, 34.10628000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q502416'),
(67967, 'Liwonde', 3106, 'S', 131, 'MW', -15.06665000, 35.22543000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1377361'),
(67968, 'Luchenza', 3106, 'S', 131, 'MW', -16.00693000, 35.30947000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q993322'),
(67969, 'Machinga', 3106, 'S', 131, 'MW', -15.16849000, 35.30002000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q12810684'),
(67970, 'Machinga District', 3106, 'S', 131, 'MW', -14.94583000, 35.57367000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q198585'),
(67971, 'Mangochi', 3106, 'S', 131, 'MW', -14.47815000, 35.26448000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1890257'),
(67972, 'Mangochi District', 3106, 'S', 131, 'MW', -14.39296000, 35.34658000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q722505'),
(67973, 'Mchinji', 3092, 'C', 131, 'MW', -13.79841000, 32.88019000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1188150'),
(67974, 'Mchinji District', 3092, 'C', 131, 'MW', -13.76230000, 33.07998000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q868792'),
(67975, 'Monkey Bay', 3106, 'S', 131, 'MW', -14.08239000, 34.91652000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1944901'),
(67976, 'Mponela', 3092, 'C', 131, 'MW', -13.53194000, 33.74008000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1253227'),
(67977, 'Mulanje', 3106, 'S', 131, 'MW', -16.03163000, 35.50000000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1865092'),
(67978, 'Mulanje District', 3106, 'S', 131, 'MW', -15.93440000, 35.49990000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q509097'),
(67979, 'Mwanza', 3106, 'S', 131, 'MW', -15.60262000, 34.52479000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1416015'),
(67980, 'Mwanza District', 3106, 'S', 131, 'MW', -15.63337000, 34.51682000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q797933'),
(67981, 'Mzimba', 3105, 'N', 131, 'MW', -11.90000000, 33.60000000, '2019-10-05 23:08:26', '2019-10-05 23:08:26', 1, 'Q1666876');

