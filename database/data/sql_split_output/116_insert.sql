INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(58459, 'Pescina', 1679, '65', 107, 'IT', 42.02351000, 13.65116000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233'),
(58460, 'Pesco Sannita', 1669, '72', 107, 'IT', 41.23273000, 14.81122000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233'),
(58461, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 1679, '65', 107, 'IT', 41.88722000, 14.06614000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233'),
(58462, 'Pescolanciano', 1695, '67', 107, 'IT', 41.67972000, 14.33658000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233'),
(58463, 'P<PERSON><PERSON>pa<PERSON>', 1706, '77', 107, 'IT', 40.83494000, 15.39946000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233'),
(58464, 'Pescopennataro', 1695, '67', 107, 'IT', 41.87777000, 14.29398000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233'),
(58465, 'Pescorocchiano', 1678, '62', 107, 'IT', 42.20623000, 13.14715000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233'),
(58466, 'Pescosansonesco Nuovo', 1679, '65', 107, 'IT', 42.25524000, 13.88480000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18427431'),
(58467, 'Pescosolido', 1678, '62', 107, 'IT', 41.74864000, 13.65690000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18427431'),
(58468, 'Peseggia-Gardigiano', 1753, '34', 107, 'IT', 45.56167000, 12.17944000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q740773'),
(58469, 'Pessano Con Bornago', 1705, '25', 107, 'IT', 45.54937000, 9.38145000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q740773'),
(58470, 'Pessina Cremonese', 1705, '25', 107, 'IT', 45.18505000, 10.24815000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q740773'),
(58471, 'Pessinetto', 1702, '21', 107, 'IT', 45.28675000, 7.41531000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q740773'),
(58472, 'Pessione', 1702, '21', 107, 'IT', 44.96504000, 7.84115000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q2028109'),
(58473, 'Petacciato', 1695, '67', 107, 'IT', 42.00827000, 14.86026000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q2028109'),
(58474, 'Petilia Policastro', 1703, '78', 107, 'IT', 39.11293000, 16.78167000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q2028109'),
(58475, 'Petina', 1669, '72', 107, 'IT', 40.53211000, 15.37346000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q2028109'),
(58476, 'Petit Fenis', 1716, '23', 107, 'IT', 45.75115000, 7.46391000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18495770'),
(58477, 'Petralia Soprana', 1709, '82', 107, 'IT', 37.80064000, 14.10813000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18495770'),
(58478, 'Petralia Sottana', 1709, '82', 107, 'IT', 37.80919000, 14.09293000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18495770'),
(58479, 'Petrella Salto', 1678, '62', 107, 'IT', 42.29405000, 13.06794000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18495770'),
(58480, 'Petrella Tifernina', 1695, '67', 107, 'IT', 41.69135000, 14.69751000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18495770'),
(58481, 'Petriano', 1670, '57', 107, 'IT', 43.77935000, 12.73214000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18495770'),
(58482, 'Petrignano', 1683, '55', 107, 'IT', 43.10269000, 12.53239000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58483, 'Petriolo', 1670, '57', 107, 'IT', 43.22141000, 13.46173000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58484, 'Petritoli', 1670, '57', 107, 'IT', 43.06809000, 13.66385000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58485, 'Petrizzi', 1703, '78', 107, 'IT', 38.70179000, 16.47187000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58486, 'Petronà', 1703, '78', 107, 'IT', 39.04370000, 16.75796000, '2019-10-05 23:06:41', '2020-05-01 17:22:55', 1, 'Q775778'),
(58487, 'Petrosino', 1709, '82', 107, 'IT', 37.71271000, 12.49964000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58488, 'Petruro Irpino', 1669, '72', 107, 'IT', 41.03192000, 14.79754000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58489, 'Pettenasco', 1702, '21', 107, 'IT', 45.81657000, 8.40702000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58490, 'Pettinengo', 1702, '21', 107, 'IT', 45.61306000, 8.10422000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58491, 'Pettineo', 1709, '82', 107, 'IT', 37.96793000, 14.29118000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58492, 'Pettoranello del Molise', 1695, '67', 107, 'IT', 41.57331000, 14.27839000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58493, 'Pettorano sul Gizio', 1679, '65', 107, 'IT', 41.97326000, 13.96001000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58494, 'Pettorazza Grimani', 1753, '34', 107, 'IT', 45.13585000, 11.98779000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58495, 'Peveragno', 1702, '21', 107, 'IT', 44.32060000, 7.61859000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q775778'),
(58496, 'Pezzan', 1753, '34', 107, 'IT', 45.69341000, 12.09616000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q25410385'),
(58497, 'Pezzana', 1702, '21', 107, 'IT', 45.26152000, 8.48396000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q25410385'),
(58498, 'Pezzano-Filetta', 1669, '72', 107, 'IT', 40.69773000, 14.86971000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437591'),
(58499, 'Pezzaze', 1705, '25', 107, 'IT', 45.77629000, 10.23597000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437591'),
(58500, 'Pezze di Greco', 1688, '75', 107, 'IT', 40.81004000, 17.41333000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3901221'),
(58501, 'Pezzolo Valle Uzzone', 1702, '21', 107, 'IT', 44.53874000, 8.19394000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3901221'),
(58502, 'Piacenza', 1773, '45', 107, 'IT', 45.05242000, 9.69342000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3901221'),
(58503, 'Piacenza d\'Adige', 1753, '34', 107, 'IT', 45.12749000, 11.54333000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3901221'),
(58504, 'Piadena', 1705, '25', 107, 'IT', 45.12859000, 10.37101000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3901221'),
(58505, 'Piagge', 1670, '57', 107, 'IT', 43.73381000, 12.96851000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q207479'),
(58506, 'Piaggine', 1669, '72', 107, 'IT', 40.34498000, 15.37777000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q207479'),
(58507, 'Piamborno', 1705, '25', 107, 'IT', 45.91969000, 10.22526000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q207479'),
(58508, 'Pian Camuno', 1705, '25', 107, 'IT', 45.84186000, 10.14181000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q207479'),
(58509, 'Pian di Mugnone', 1664, '52', 107, 'IT', 43.81810000, 11.29585000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3901889'),
(58510, 'Pian di Scò', 1664, '52', 107, 'IT', 43.64195000, 11.54523000, '2019-10-05 23:06:41', '2020-05-01 17:22:57', 1, 'Q52093'),
(58511, 'Piana Battolla', 1768, '42', 107, 'IT', 44.19322000, 9.85345000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18460870'),
(58512, 'Piana Crixia', 1768, '42', 107, 'IT', 44.48504000, 8.30805000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18460870'),
(58513, 'Piana San Raffaele', 1702, '21', 107, 'IT', 45.15290000, 7.85013000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18444055'),
(58514, 'Piana degli Albanesi', 1709, '82', 107, 'IT', 37.99372000, 13.28464000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18444055'),
(58515, 'Piana di Monte Verna', 1669, '72', 107, 'IT', 41.16712000, 14.33373000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18444055'),
(58516, 'Pianazzo', 1705, '25', 107, 'IT', 46.42815000, 9.34420000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18494544'),
(58517, 'Piancastagnaio', 1664, '52', 107, 'IT', 42.85067000, 11.69014000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18494544'),
(58518, 'Piandimeleto', 1670, '57', 107, 'IT', 43.72320000, 12.40855000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18494544'),
(58519, 'Piane', 1670, '57', 107, 'IT', 43.09703000, 13.49502000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496926'),
(58520, 'Piane Crati', 1703, '78', 107, 'IT', 39.23472000, 16.32313000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496926'),
(58521, 'Piane di Montegiorgio', 1670, '57', 107, 'IT', 43.11587000, 13.56669000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496921'),
(58522, 'Piane di Morro', 1670, '57', 107, 'IT', 42.82317000, 13.65300000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496621'),
(58523, 'Pianella', 1679, '65', 107, 'IT', 42.39918000, 14.04781000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496621'),
(58524, 'Pianello', 1670, '57', 107, 'IT', 43.62620000, 13.13068000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496022'),
(58525, 'Pianello Val Tidone', 1773, '45', 107, 'IT', 44.94666000, 9.40516000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496022'),
(58526, 'Pianello Vallesina', 1670, '57', 107, 'IT', 43.48817000, 13.16188000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q532704'),
(58527, 'Pianello del Lario', 1705, '25', 107, 'IT', 46.10279000, 9.27694000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q532704'),
(58528, 'Pianengo', 1705, '25', 107, 'IT', 45.40217000, 9.69476000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q532704'),
(58529, 'Pianezza', 1702, '21', 107, 'IT', 45.10420000, 7.55003000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q532704'),
(58530, 'Pianezze', 1753, '34', 107, 'IT', 45.74141000, 11.62700000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q532704'),
(58531, 'Pianfei', 1702, '21', 107, 'IT', 44.37203000, 7.71143000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q532704'),
(58532, 'Piangaiano', 1705, '25', 107, 'IT', 45.78877000, 9.99461000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18468596'),
(58533, 'Piangipane', 1773, '45', 107, 'IT', 44.42198000, 12.09066000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18446150'),
(58534, 'Pianico', 1705, '25', 107, 'IT', 45.80989000, 10.04306000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18446150'),
(58535, 'Pianiga', 1753, '34', 107, 'IT', 45.45708000, 12.00762000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18446150'),
(58536, 'Pianillo', 1669, '72', 107, 'IT', 40.63416000, 14.54922000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437029'),
(58537, 'Piano', 1669, '72', 107, 'IT', 40.82050000, 14.76005000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q55068'),
(58538, 'Piano', 1768, '42', 107, 'IT', 44.31680000, 8.16639000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18462367'),
(58539, 'Piano Maglio-Blandino', 1709, '82', 107, 'IT', 38.05793000, 13.30909000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18468528'),
(58540, 'Piano dei Geli', 1709, '82', 107, 'IT', 38.09919000, 13.27478000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18468522'),
(58541, 'Piano di Conca', 1664, '52', 107, 'IT', 43.89413000, 10.29493000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18488943'),
(58542, 'Piano di Coreglia-Ghivizzano', 1664, '52', 107, 'IT', 44.03333000, 10.51667000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18488945'),
(58543, 'Piano di Follo', 1768, '42', 107, 'IT', 44.16375000, 9.86188000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18460878'),
(58544, 'Piano di Mommio', 1664, '52', 107, 'IT', 43.90954000, 10.27435000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18488947'),
(58545, 'Piano di Sorrento', 1669, '72', 107, 'IT', 40.62806000, 14.41729000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18488947'),
(58546, 'Piano-Molini d\'Isola', 1702, '21', 107, 'IT', 44.83194000, 8.18073000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18503442'),
(58547, 'Pianoconte', 1709, '82', 107, 'IT', 38.47432000, 14.92857000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18464887'),
(58548, 'Pianola', 1679, '65', 107, 'IT', 42.32341000, 13.40394000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q7190216'),
(58549, 'Pianopoli', 1703, '78', 107, 'IT', 38.95246000, 16.38863000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q7190216'),
(58550, 'Pianoro', 1773, '45', 107, 'IT', 44.38893000, 11.34262000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q7190216'),
(58551, 'Piansano', 1678, '62', 107, 'IT', 42.52449000, 11.82978000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q7190216'),
(58552, 'Piantedo', 1705, '25', 107, 'IT', 46.13663000, 9.42770000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q7190216'),
(58553, 'Pianura', 1669, '72', 107, 'IT', 40.85907000, 14.17314000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1101657'),
(58554, 'Pianura Vomano', 1679, '65', 107, 'IT', 42.63028000, 13.91548000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18428443'),
(58555, 'Piario', 1705, '25', 107, 'IT', 45.89649000, 9.92726000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18428443'),
(58556, 'Piasco', 1702, '21', 107, 'IT', 44.56106000, 7.44420000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18428443'),
(58557, 'Piateda Centro', 1705, '25', 107, 'IT', 46.15960000, 9.93495000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18428443'),
(58558, 'Piatto', 1702, '21', 107, 'IT', 45.58986000, 8.13532000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18428443'),
(58559, 'Piattoni-Villa Sant\'Antonio', 1670, '57', 107, 'IT', 42.86630000, 13.71120000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496629'),
(58560, 'Piavon', 1753, '34', 107, 'IT', 45.76429000, 12.52673000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q2103092'),
(58561, 'Piazza', 1705, '25', 107, 'IT', 45.58725000, 10.15526000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18475898'),
(58562, 'Piazza', 1753, '34', 107, 'IT', 45.21585000, 11.14244000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18505318'),
(58563, 'Piazza', 1725, '32', 107, 'IT', 45.87705000, 11.15809000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3902132'),
(58564, 'Piazza', 1702, '21', 107, 'IT', 44.38597000, 7.83163000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18440138'),
(58565, 'Piazza Armerina', 1709, '82', 107, 'IT', 37.38417000, 14.36921000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18440138'),
(58566, 'Piazza Brembana', 1705, '25', 107, 'IT', 45.94769000, 9.67330000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18440138'),
(58567, 'Piazza Caduti', 1705, '25', 107, 'IT', 45.70513000, 9.50251000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18468649'),
(58568, 'Piazza Roma', 1669, '72', 107, 'IT', 40.70434000, 14.54300000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q16590048'),
(58569, 'Piazza al Serchio-San Michele', 1664, '52', 107, 'IT', 44.18455000, 10.29689000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18488950'),
(58570, 'Piazza del Galdo-Sant\'Angelo', 1669, '72', 107, 'IT', 40.77390000, 14.71696000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437605'),
(58571, 'Piazza di Pandola', 1669, '72', 107, 'IT', 40.80748000, 14.77180000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3902471'),
(58572, 'Piazza-Tralia-Pendolo', 1669, '72', 107, 'IT', 40.67351000, 14.50455000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437032'),
(58573, 'Piazzatorre', 1705, '25', 107, 'IT', 45.99273000, 9.68942000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437032'),
(58574, 'Piazzola', 1669, '72', 107, 'IT', 40.87265000, 14.50016000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437033'),
(58575, 'Piazzola sul Brenta', 1753, '34', 107, 'IT', 45.53845000, 11.78437000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437033'),
(58576, 'Piazzolla', 1669, '72', 107, 'IT', 40.88964000, 14.51579000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437034'),
(58577, 'Piazzolo', 1705, '25', 107, 'IT', 45.97979000, 9.67055000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437034'),
(58578, 'Piccarello', 1768, '42', 107, 'IT', 44.48392000, 8.96595000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3902532'),
(58579, 'Picciano', 1679, '65', 107, 'IT', 42.47525000, 13.98990000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3902532'),
(58580, 'Picerno', 1706, '77', 107, 'IT', 40.63975000, 15.64232000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3902532'),
(58581, 'Picinisco', 1678, '62', 107, 'IT', 41.64568000, 13.86791000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3902532'),
(58582, 'Pico', 1678, '62', 107, 'IT', 41.45025000, 13.55894000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3902532'),
(58583, 'Pie\' Falcade', 1753, '34', 107, 'IT', 46.35660000, 11.87207000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496822'),
(58584, 'Pie\' del Colle', 1670, '57', 107, 'IT', 43.02984000, 13.17535000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18497646'),
(58585, 'Pie\' del Sasso', 1670, '57', 107, 'IT', 42.99429000, 12.99889000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18497648'),
(58586, 'Piea', 1702, '21', 107, 'IT', 45.02706000, 8.07146000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18497648'),
(58587, 'Piedicavallo', 1702, '21', 107, 'IT', 45.68986000, 7.95482000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18497648'),
(58588, 'Piedimonte', 1669, '72', 107, 'IT', 41.18886000, 13.89856000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18436693'),
(58589, 'Piedimonte Etneo', 1709, '82', 107, 'IT', 37.80677000, 15.17516000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18436693'),
(58590, 'Piedimonte Matese', 1669, '72', 107, 'IT', 41.35082000, 14.36803000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18436693'),
(58591, 'Piedimonte San Germano', 1678, '62', 107, 'IT', 41.49796000, 13.75017000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18436693'),
(58592, 'Piedimonte San Germano Alta', 1678, '62', 107, 'IT', 41.50446000, 13.74909000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18453296'),
(58593, 'Piedimulera', 1702, '21', 107, 'IT', 46.02408000, 8.25897000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18453296'),
(58594, 'Piediripa', 1670, '57', 107, 'IT', 43.27751000, 13.48712000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18497652'),
(58595, 'Piegaro', 1683, '55', 107, 'IT', 42.96596000, 12.08409000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18497652'),
(58596, 'Piegolelle-San Bartolomeo', 1669, '72', 107, 'IT', 40.68583000, 14.82312000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437611'),
(58597, 'Pienza', 1664, '52', 107, 'IT', 43.07873000, 11.67671000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437611'),
(58598, 'Pieranica', 1705, '25', 107, 'IT', 45.42617000, 9.61026000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18437611'),
(58599, 'Pieria-Prato Carnico', 1756, '36', 107, 'IT', 46.52037000, 12.80328000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18452402'),
(58600, 'Pieris', 1756, '36', 107, 'IT', 45.81243000, 13.44917000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3903065'),
(58601, 'Pietra Ligure', 1768, '42', 107, 'IT', 44.14920000, 8.28206000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3903065'),
(58602, 'Pietra Marazzi', 1702, '21', 107, 'IT', 44.94265000, 8.66854000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3903065'),
(58603, 'Pietra de\' Giorgi', 1705, '25', 107, 'IT', 45.02136000, 9.23036000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q3903065'),
(58604, 'Pietra la Croce', 1670, '57', 107, 'IT', 43.60224000, 13.53887000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496036'),
(58605, 'Pietrabbondante', 1695, '67', 107, 'IT', 41.74527000, 14.38480000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496036'),
(58606, 'Pietrabruna', 1768, '42', 107, 'IT', 43.88927000, 7.90319000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496036'),
(58607, 'Pietracamela', 1679, '65', 107, 'IT', 42.52344000, 13.55431000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496036'),
(58608, 'Pietracatella', 1695, '67', 107, 'IT', 41.58064000, 14.87298000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496036'),
(58609, 'Pietracupa', 1695, '67', 107, 'IT', 41.68253000, 14.51933000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18496036'),
(58610, 'Pietracuta', 1773, '45', 107, 'IT', 43.95400000, 12.37138000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58611, 'Pietradefusi', 1669, '72', 107, 'IT', 41.04316000, 14.88415000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58612, 'Pietraferrazzana', 1679, '65', 107, 'IT', 41.96943000, 14.37451000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58613, 'Pietrafitta', 1703, '78', 107, 'IT', 39.26103000, 16.33940000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58614, 'Pietragalla', 1706, '77', 107, 'IT', 40.74572000, 15.87398000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58615, 'Pietralunga', 1683, '55', 107, 'IT', 43.43609000, 12.42921000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58616, 'Pietramelara', 1669, '72', 107, 'IT', 41.27086000, 14.18711000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58617, 'Pietramontecorvino', 1688, '75', 107, 'IT', 41.54228000, 15.12894000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q1110618'),
(58618, 'Pietramurata', 1725, '32', 107, 'IT', 46.02033000, 10.94316000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18481879'),
(58619, 'Pietranico', 1679, '65', 107, 'IT', 42.27565000, 13.91077000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18481879'),
(58620, 'Pietrapaola', 1703, '78', 107, 'IT', 39.48701000, 16.81597000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18481879'),
(58621, 'Pietrapertosa', 1706, '77', 107, 'IT', 40.51731000, 16.06244000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18481879'),
(58622, 'Pietraperzia', 1709, '82', 107, 'IT', 37.41852000, 14.13739000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18481879'),
(58623, 'Pietraporzio', 1702, '21', 107, 'IT', 44.34352000, 7.03433000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18481879'),
(58624, 'Pietraroja', 1669, '72', 107, 'IT', 41.34782000, 14.54963000, '2019-10-05 23:06:41', '2019-10-05 23:06:41', 1, 'Q18481879'),
(58625, 'Pietrasanta', 1664, '52', 107, 'IT', 43.95952000, 10.22784000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18481879'),
(58626, 'Pietrastornina', 1669, '72', 107, 'IT', 40.99535000, 14.71833000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18481879'),
(58627, 'Pietravairano', 1669, '72', 107, 'IT', 41.32522000, 14.16592000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18481879'),
(58628, 'Pietre', 1669, '72', 107, 'IT', 40.70048000, 14.61993000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18437615'),
(58629, 'Pietrelcina', 1669, '72', 107, 'IT', 41.19942000, 14.84414000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18437615'),
(58630, 'Pieve', 1705, '25', 107, 'IT', 45.77273000, 10.75913000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18509646'),
(58631, 'Pieve', 1753, '34', 107, 'IT', 45.53924000, 11.82930000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58632, 'Pieve Albignola', 1705, '25', 107, 'IT', 45.11316000, 8.95999000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58633, 'Pieve Emanuele', 1705, '25', 107, 'IT', 45.35052000, 9.20268000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58634, 'Pieve Fissiraga', 1705, '25', 107, 'IT', 45.26327000, 9.45836000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58635, 'Pieve Fosciana', 1664, '52', 107, 'IT', 44.13067000, 10.40952000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58636, 'Pieve Ligure', 1768, '42', 107, 'IT', 44.37494000, 9.09410000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58637, 'Pieve Porto Morone', 1705, '25', 107, 'IT', 45.10979000, 9.44019000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58638, 'Pieve San Giacomo', 1705, '25', 107, 'IT', 45.13204000, 10.18775000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58639, 'Pieve Santo Stefano', 1664, '52', 107, 'IT', 43.67145000, 12.04124000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58640, 'Pieve Tesino', 1725, '32', 107, 'IT', 46.06820000, 11.61122000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58641, 'Pieve Torina', 1670, '57', 107, 'IT', 43.04299000, 13.04769000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58642, 'Pieve Vergonte', 1702, '21', 107, 'IT', 46.01298000, 8.26082000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58643, 'Pieve a Nievole', 1664, '52', 107, 'IT', 43.88119000, 10.79990000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18498540'),
(58644, 'Pieve al Toppo', 1664, '52', 107, 'IT', 43.40761000, 11.79686000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58645, 'Pieve d\'Alpago', 1753, '34', 107, 'IT', 46.16658000, 12.35344000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58646, 'Pieve d\'Olmi', 1705, '25', 107, 'IT', 45.08957000, 10.12368000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58647, 'Pieve del Cairo', 1705, '25', 107, 'IT', 45.04920000, 8.80322000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58648, 'Pieve di Bono', 1725, '32', 107, 'IT', 45.94220000, 10.64027000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58649, 'Pieve di Cadore', 1753, '34', 107, 'IT', 46.42466000, 12.36416000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58650, 'Pieve di Cento', 1773, '45', 107, 'IT', 44.71293000, 11.30922000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58651, 'Pieve di Coriano', 1705, '25', 107, 'IT', 45.03388000, 11.10780000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18485797'),
(58652, 'Pieve di Ledro', 1725, '32', 107, 'IT', 45.88848000, 10.73124000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q816769'),
(58653, 'Pieve di Soligo', 1753, '34', 107, 'IT', 45.89836000, 12.17128000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q816769'),
(58654, 'Pieve di Teco', 1768, '42', 107, 'IT', 44.04706000, 7.91564000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q816769'),
(58655, 'Pieve di Zignago', 1768, '42', 107, 'IT', 44.27790000, 9.74573000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18460888'),
(58656, 'Pievebovigliana', 1670, '57', 107, 'IT', 43.06287000, 13.08375000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18460888'),
(58657, 'Pievedizio', 1705, '25', 107, 'IT', 45.46062000, 10.08771000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58658, 'Pievepelago', 1773, '45', 107, 'IT', 44.20475000, 10.61660000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58659, 'Piglio', 1678, '62', 107, 'IT', 41.83006000, 13.13330000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58660, 'Pigna', 1768, '42', 107, 'IT', 43.93205000, 7.66123000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58661, 'Pignataro Interamna', 1678, '62', 107, 'IT', 41.43922000, 13.78621000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58662, 'Pignataro Maggiore', 1669, '72', 107, 'IT', 41.19006000, 14.16978000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58663, 'Pignola', 1706, '77', 107, 'IT', 40.57412000, 15.78295000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58664, 'Pignone', 1768, '42', 107, 'IT', 44.17780000, 9.72327000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58665, 'Pigra', 1705, '25', 107, 'IT', 45.95735000, 9.12649000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475942'),
(58666, 'Pila', 1683, '55', 107, 'IT', 43.05329000, 12.32857000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q3904944'),
(58667, 'Pila', 1702, '21', 107, 'IT', 45.76997000, 8.08122000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q3904944'),
(58668, 'Pilastro', 1773, '45', 107, 'IT', 44.68254000, 10.28879000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18442975'),
(58669, 'Pimentel', 1715, '88', 107, 'IT', 39.48718000, 9.06516000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18442975'),
(58670, 'Pimonte', 1669, '72', 107, 'IT', 40.67361000, 14.50984000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18442975'),
(58671, 'Pinarella', 1773, '45', 107, 'IT', 44.24167000, 12.37111000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18442975'),
(58672, 'Pinarolo Po', 1705, '25', 107, 'IT', 45.06986000, 9.16725000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18442975'),
(58673, 'Pinasca-Dubbione', 1702, '21', 107, 'IT', 44.94264000, 7.22912000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18444107'),
(58674, 'Pincara', 1753, '34', 107, 'IT', 45.00064000, 11.62101000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18444107'),
(58675, 'Pinerolo', 1702, '21', 107, 'IT', 44.88534000, 7.33135000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18444107'),
(58676, 'Pineta', 1725, '32', 107, 'IT', 46.43994000, 11.34727000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475701'),
(58677, 'Pineto', 1679, '65', 107, 'IT', 42.60879000, 14.06639000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475701'),
(58678, 'Pino Torinese', 1702, '21', 107, 'IT', 45.03955000, 7.77712000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475701'),
(58679, 'Pino d\'Asti', 1702, '21', 107, 'IT', 45.05745000, 7.98623000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475701'),
(58680, 'Pino sulla Sponda del Lago Maggiore', 1705, '25', 107, 'IT', 46.10078000, 8.73873000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18475701'),
(58681, 'Pinocchio di Ancona', 1670, '57', 107, 'IT', 43.58986000, 13.49413000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18496042'),
(58682, 'Pinzano al Tagliamento', 1756, '36', 107, 'IT', 46.18216000, 12.94433000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18496042'),
(58683, 'Pinzolo', 1725, '32', 107, 'IT', 46.15976000, 10.76376000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18496042'),
(58684, 'Piobbico', 1670, '57', 107, 'IT', 43.58819000, 12.50962000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18496042'),
(58685, 'Piobesi Torinese', 1702, '21', 107, 'IT', 44.76667000, 7.61667000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440163'),
(58686, 'Piobesi d\'Alba', 1702, '21', 107, 'IT', 44.73444000, 7.97937000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440163'),
(58687, 'Piode', 1702, '21', 107, 'IT', 45.77059000, 8.05265000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440163'),
(58688, 'Pioltello', 1705, '25', 107, 'IT', 45.50147000, 9.33053000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440163'),
(58689, 'Piombino', 1664, '52', 107, 'IT', 42.92554000, 10.52585000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440163'),
(58690, 'Piombino Dese', 1753, '34', 107, 'IT', 45.60964000, 11.99416000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440163'),
(58691, 'Pionca', 1753, '34', 107, 'IT', 45.46591000, 11.96232000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q3080063'),
(58692, 'Pioppo', 1709, '82', 107, 'IT', 38.05000000, 13.23333000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58693, 'Pioraco', 1670, '57', 107, 'IT', 43.18074000, 12.97676000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58694, 'Piossasco', 1702, '21', 107, 'IT', 44.98802000, 7.46010000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58695, 'Piove di Sacco-Piovega', 1753, '34', 107, 'IT', 45.29665000, 12.03683000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58696, 'Piovene Rocchette', 1753, '34', 107, 'IT', 45.75601000, 11.43273000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58697, 'Piovera', 1702, '21', 107, 'IT', 44.95845000, 8.73595000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58698, 'Piovà Massaia', 1702, '21', 107, 'IT', 45.05515000, 8.05013000, '2019-10-05 23:06:42', '2020-05-01 17:22:56', 1, 'Q18468538'),
(58699, 'Piozzano', 1773, '45', 107, 'IT', 44.92533000, 9.49559000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58700, 'Piozzo', 1702, '21', 107, 'IT', 44.51384000, 7.89254000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58701, 'Piraino', 1709, '82', 107, 'IT', 38.16127000, 14.86100000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58702, 'Pisa', 1664, '52', 107, 'IT', 43.70853000, 10.40360000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58703, 'Pisano', 1702, '21', 107, 'IT', 45.79567000, 8.51487000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58704, 'Piscina', 1702, '21', 107, 'IT', 44.91874000, 7.42532000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58705, 'Piscinas', 1715, '88', 107, 'IT', 39.07435000, 8.66634000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18468538'),
(58706, 'Piscinola', 1669, '72', 107, 'IT', 40.89299000, 14.23394000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2096936'),
(58707, 'Pisciotta', 1669, '72', 107, 'IT', 40.10890000, 15.23456000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2096936'),
(58708, 'Piscopio', 1703, '78', 107, 'IT', 38.66361000, 16.11101000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18434055'),
(58709, 'Pisignano', 1688, '75', 107, 'IT', 40.30470000, 18.27155000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2219590'),
(58710, 'Pisignano', 1773, '45', 107, 'IT', 44.23787000, 12.26841000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18446170'),
(58711, 'Pisogne', 1705, '25', 107, 'IT', 45.80777000, 10.11023000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18446170'),
(58712, 'Pisoniano', 1678, '62', 107, 'IT', 41.90816000, 12.95794000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18446170'),
(58713, 'Pisticci', 1706, '77', 107, 'IT', 40.39017000, 16.55919000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18446170'),
(58714, 'Pistoia', 1664, '52', 107, 'IT', 43.93064000, 10.92365000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18446170'),
(58715, 'Pistrino', 1683, '55', 107, 'IT', 43.50966000, 12.15181000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2268273'),
(58716, 'Piteglio', 1664, '52', 107, 'IT', 44.02790000, 10.76571000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2268273'),
(58717, 'Pitelli', 1768, '42', 107, 'IT', 44.09458000, 9.88490000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2684384'),
(58718, 'Pitigliano', 1664, '52', 107, 'IT', 42.63582000, 11.67462000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2684384'),
(58719, 'Piubega', 1705, '25', 107, 'IT', 45.22677000, 10.53195000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2684384'),
(58720, 'Piumazzo', 1773, '45', 107, 'IT', 44.54631000, 11.06727000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2677876'),
(58721, 'Piuro', 1705, '25', 107, 'IT', 46.32979000, 9.42064000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2677876'),
(58722, 'Piverone', 1702, '21', 107, 'IT', 45.44557000, 8.00745000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2677876'),
(58723, 'Pizzale', 1705, '25', 107, 'IT', 45.03856000, 9.04995000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2677876'),
(58724, 'Pizzano', 1773, '45', 107, 'IT', 44.35500000, 11.41499000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58725, 'Pizzighettone', 1705, '25', 107, 'IT', 45.18690000, 9.78781000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58726, 'Pizzo', 1703, '78', 107, 'IT', 38.75259000, 16.18696000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58727, 'Pizzoferrato', 1679, '65', 107, 'IT', 41.92244000, 14.23632000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58728, 'Pizzoli', 1679, '65', 107, 'IT', 42.43603000, 13.29886000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58729, 'Pizzone', 1695, '67', 107, 'IT', 41.67332000, 14.03666000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58730, 'Pizzoni', 1703, '78', 107, 'IT', 38.62286000, 16.24876000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58731, 'Placanica', 1703, '78', 107, 'IT', 38.41078000, 16.45274000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18438575'),
(58732, 'Plan d\'Introd', 1716, '23', 107, 'IT', 45.69203000, 7.18328000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18495789'),
(58733, 'Plataci', 1703, '78', 107, 'IT', 39.90042000, 16.43272000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18495789'),
(58734, 'Platania', 1703, '78', 107, 'IT', 39.00620000, 16.32194000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18495789'),
(58735, 'Platì', 1703, '78', 107, 'IT', 38.22149000, 16.04530000, '2019-10-05 23:06:42', '2020-05-01 17:22:55', 1, 'Q18495789'),
(58736, 'Plaus', 1725, '32', 107, 'IT', 46.65623000, 11.04178000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18495789'),
(58737, 'Plesio', 1705, '25', 107, 'IT', 46.04742000, 9.22882000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18495789'),
(58738, 'Pleyne', 1702, '21', 107, 'IT', 44.58063000, 7.01712000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440181'),
(58739, 'Ploaghe', 1715, '88', 107, 'IT', 40.67068000, 8.74962000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440181'),
(58740, 'Plodio', 1768, '42', 107, 'IT', 44.35394000, 8.24725000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18440181'),
(58741, 'Poasco-Sorigherio', 1705, '25', 107, 'IT', 45.40310000, 9.23092000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18491662'),
(58742, 'Pocapaglia', 1702, '21', 107, 'IT', 44.71524000, 7.88293000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18491662'),
(58743, 'Pocenia', 1756, '36', 107, 'IT', 45.83570000, 13.10145000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18491662'),
(58744, 'Podenzana', 1664, '52', 107, 'IT', 44.20625000, 9.94199000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18491662'),
(58745, 'Podenzano', 1773, '45', 107, 'IT', 44.95484000, 9.68315000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18491662'),
(58746, 'Pofi', 1678, '62', 107, 'IT', 41.56542000, 13.41460000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18491662'),
(58747, 'Poggetto', 1664, '52', 107, 'IT', 43.82790000, 11.03847000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q3907010'),
(58748, 'Poggiardo', 1688, '75', 107, 'IT', 40.05315000, 18.37819000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q3907010'),
(58749, 'Poggibonsi', 1664, '52', 107, 'IT', 43.47064000, 11.14804000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q3907010'),
(58750, 'Poggio A Caiano', 1664, '52', 107, 'IT', 43.81378000, 11.05186000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q3907010'),
(58751, 'Poggio Berni', 1773, '45', 107, 'IT', 44.02639000, 12.40944000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q111883'),
(58752, 'Poggio Bustone', 1678, '62', 107, 'IT', 42.50330000, 12.88524000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q111883'),
(58753, 'Poggio Catino', 1678, '62', 107, 'IT', 42.29420000, 12.69838000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q111883'),
(58754, 'Poggio Ellera', 1678, '62', 107, 'IT', 42.11380000, 12.37174000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58755, 'Poggio Imperiale', 1688, '75', 107, 'IT', 41.82503000, 15.36680000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58756, 'Poggio Mirteto', 1678, '62', 107, 'IT', 42.26763000, 12.68837000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58757, 'Poggio Moiano', 1678, '62', 107, 'IT', 42.20411000, 12.88136000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58758, 'Poggio Nativo', 1678, '62', 107, 'IT', 42.21783000, 12.79691000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58759, 'Poggio Picenze', 1679, '65', 107, 'IT', 42.32061000, 13.54036000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58760, 'Poggio Renatico', 1773, '45', 107, 'IT', 44.76581000, 11.48695000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58761, 'Poggio Rusco', 1705, '25', 107, 'IT', 44.96693000, 11.10419000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58762, 'Poggio San Lorenzo', 1678, '62', 107, 'IT', 42.25174000, 12.84343000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58763, 'Poggio San Marcello', 1670, '57', 107, 'IT', 43.51116000, 13.07367000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58764, 'Poggio San Vicino', 1670, '57', 107, 'IT', 43.37466000, 13.07964000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58765, 'Poggio Sannita', 1695, '67', 107, 'IT', 41.77939000, 14.41485000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q18455144'),
(58766, 'Poggio dei Pini', 1715, '88', 107, 'IT', 39.14717000, 8.97139000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2604595'),
(58767, 'Poggiodomo', 1683, '55', 107, 'IT', 42.71237000, 12.93251000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2604595'),
(58768, 'Poggiofiorito', 1679, '65', 107, 'IT', 42.25414000, 14.32082000, '2019-10-05 23:06:42', '2019-10-05 23:06:42', 1, 'Q2604595'),
(58769, 'Poggiomarino', 1669, '72', 107, 'IT', 40.80114000, 14.54066000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q2604595'),
(58770, 'Poggioreale', 1669, '72', 107, 'IT', 40.86543000, 14.28877000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q2604595'),
(58771, 'Poggiorsini', 1688, '75', 107, 'IT', 40.91403000, 16.25605000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q2604595'),
(58772, 'Poggiridenti Alto', 1705, '25', 107, 'IT', 46.17480000, 9.92605000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18494585'),
(58773, 'Poggiridenti Piano', 1705, '25', 107, 'IT', 46.16991000, 9.92671000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18494588'),
(58774, 'Pogliano Milanese', 1705, '25', 107, 'IT', 45.53786000, 8.99403000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18494588'),
(58775, 'Pognana Lario', 1705, '25', 107, 'IT', 45.88044000, 9.15762000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18494588'),
(58776, 'Pognano', 1705, '25', 107, 'IT', 45.58638000, 9.63996000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18494588'),
(58777, 'Pogno', 1702, '21', 107, 'IT', 45.75806000, 8.38559000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18494588'),
(58778, 'Poiana Maggiore', 1753, '34', 107, 'IT', 45.29039000, 11.50250000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18494588'),
(58779, 'Poianella', 1753, '34', 107, 'IT', 45.63041000, 11.62853000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18506709'),
(58780, 'Poiano', 1753, '34', 107, 'IT', 45.47029000, 11.01875000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58781, 'Poirino', 1702, '21', 107, 'IT', 44.92047000, 7.84465000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58782, 'Polaveno', 1705, '25', 107, 'IT', 45.66148000, 10.12396000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58783, 'Polcenigo', 1756, '36', 107, 'IT', 46.03822000, 12.50321000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58784, 'Polesella', 1753, '34', 107, 'IT', 44.96457000, 11.74887000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58785, 'Polesine Parmense', 1773, '45', 107, 'IT', 45.01682000, 10.09015000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58786, 'Poli', 1678, '62', 107, 'IT', 41.88913000, 12.89037000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58787, 'Polia', 1703, '78', 107, 'IT', 38.75118000, 16.31220000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907084'),
(58788, 'Policastro Bussentino', 1669, '72', 107, 'IT', 40.07511000, 15.52165000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58789, 'Policoro', 1706, '77', 107, 'IT', 40.21280000, 16.67795000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58790, 'Polignano a Mare', 1688, '75', 107, 'IT', 40.99221000, 17.22149000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58791, 'Polinago', 1773, '45', 107, 'IT', 44.34502000, 10.72597000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58792, 'Polino', 1683, '55', 107, 'IT', 42.58423000, 12.84437000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58793, 'Polistena', 1703, '78', 107, 'IT', 38.40544000, 16.07330000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58794, 'Polizzi Generosa', 1709, '82', 107, 'IT', 37.81159000, 14.00268000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58795, 'Polla', 1669, '72', 107, 'IT', 40.51433000, 15.49715000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58796, 'Pollein', 1716, '23', 107, 'IT', 45.72780000, 7.35146000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58797, 'Pollena Trocchia', 1669, '72', 107, 'IT', 40.85134000, 14.37911000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58798, 'Pollenza', 1670, '57', 107, 'IT', 43.26595000, 13.34788000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58799, 'Pollica', 1669, '72', 107, 'IT', 40.19070000, 15.05716000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58800, 'Pollina', 1709, '82', 107, 'IT', 37.99309000, 14.14586000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58801, 'Pollone', 1702, '21', 107, 'IT', 45.57976000, 8.00592000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58802, 'Pollutri', 1679, '65', 107, 'IT', 42.13784000, 14.59172000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58803, 'Polonghera', 1702, '21', 107, 'IT', 44.80234000, 7.59572000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58804, 'Polpenazze del Garda', 1705, '25', 107, 'IT', 45.55118000, 10.50488000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58805, 'Polverara', 1753, '34', 107, 'IT', 45.30945000, 11.95474000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58806, 'Polverigi', 1670, '57', 107, 'IT', 43.52876000, 13.38294000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q610420'),
(58807, 'Polvica', 1669, '72', 107, 'IT', 40.69484000, 14.64019000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18437630'),
(58808, 'Pomarance', 1664, '52', 107, 'IT', 43.29592000, 10.87223000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18437630'),
(58809, 'Pomaretto', 1702, '21', 107, 'IT', 45.15194000, 8.04661000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18437630'),
(58810, 'Pomarico', 1706, '77', 107, 'IT', 40.52804000, 16.52708000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18437630'),
(58811, 'Pomaro Monferrato', 1702, '21', 107, 'IT', 45.06246000, 8.59604000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18437630'),
(58812, 'Pomarolo', 1725, '32', 107, 'IT', 45.92810000, 11.04308000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18437630'),
(58813, 'Pombia', 1702, '21', 107, 'IT', 45.65550000, 8.62846000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18437630'),
(58814, 'Pometo', 1705, '25', 107, 'IT', 44.92788000, 9.27348000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58815, 'Pomezia', 1678, '62', 107, 'IT', 41.66931000, 12.50124000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58816, 'Pomigliano d\'Arco', 1669, '72', 107, 'IT', 40.90975000, 14.38316000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58817, 'Pompei', 1669, '72', 107, 'IT', 40.74574000, 14.49698000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58818, 'Pompeiana', 1768, '42', 107, 'IT', 43.85297000, 7.88836000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58819, 'Pompiano', 1705, '25', 107, 'IT', 45.43116000, 9.98910000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58820, 'Pomponesco', 1705, '25', 107, 'IT', 44.93118000, 10.59402000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58821, 'Pompu', 1715, '88', 107, 'IT', 39.72518000, 8.79640000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58822, 'Poncarale', 1705, '25', 107, 'IT', 45.46089000, 10.17976000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58823, 'Ponderano', 1702, '21', 107, 'IT', 45.53846000, 8.05592000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907798'),
(58824, 'Ponna Superiore', 1705, '25', 107, 'IT', 45.98957000, 9.09406000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18487770'),
(58825, 'Ponsacco', 1664, '52', 107, 'IT', 43.62307000, 10.62661000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18487770'),
(58826, 'Ponso', 1753, '34', 107, 'IT', 45.18232000, 11.57933000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18487770'),
(58827, 'Pont-Bozet', 1716, '23', 107, 'IT', 45.60726000, 7.68641000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18487770'),
(58828, 'Pont-Canavese', 1702, '21', 107, 'IT', 45.42138000, 7.60024000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18487770'),
(58829, 'Pont-Saint-Martin', 1716, '23', 107, 'IT', 45.59536000, 7.79451000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18487770'),
(58830, 'Pontasserchio', 1664, '52', 107, 'IT', 43.77953000, 10.41673000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18490465'),
(58831, 'Pontassieve', 1664, '52', 107, 'IT', 43.77477000, 11.43109000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18490465'),
(58832, 'Ponte', 1669, '72', 107, 'IT', 41.21554000, 14.69826000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q18490465'),
(58833, 'Ponte A Elsa', 1664, '52', 107, 'IT', 43.68907000, 10.89281000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q2079838'),
(58834, 'Ponte A Tressa', 1664, '52', 107, 'IT', 43.25158000, 11.39514000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3908062'),
(58835, 'Ponte Arche', 1725, '32', 107, 'IT', 46.03526000, 10.87320000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907951'),
(58836, 'Ponte Buggianese', 1664, '52', 107, 'IT', 43.84599000, 10.74789000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q3907951'),
(58837, 'Ponte Caffaro', 1705, '25', 107, 'IT', 45.82078000, 10.52707000, '2019-10-05 23:06:43', '2019-10-05 23:06:43', 1, 'Q302316'),
(58838, 'Ponte Cingoli', 1705, '25', 107, 'IT', 45.59751000, 10.10687000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18476113'),
(58839, 'Ponte Felcino', 1683, '55', 107, 'IT', 43.13049000, 12.44914000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18493538'),
(58840, 'Ponte Galeria-La Pisana', 1678, '62', 107, 'IT', 41.84634000, 12.33925000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3907971'),
(58841, 'Ponte Gardena', 1725, '32', 107, 'IT', 46.59712000, 11.53088000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3907971'),
(58842, 'Ponte Lambro', 1705, '25', 107, 'IT', 45.82539000, 9.22455000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3907971'),
(58843, 'Ponte Nizza', 1705, '25', 107, 'IT', 44.85188000, 9.09739000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3907971'),
(58844, 'Ponte Nossa', 1705, '25', 107, 'IT', 45.86611000, 9.88364000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3907971'),
(58845, 'Ponte Pattoli', 1683, '55', 107, 'IT', 43.17807000, 12.42842000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908009'),
(58846, 'Ponte Ronca', 1773, '45', 107, 'IT', 44.50166000, 11.18973000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18438591'),
(58847, 'Ponte San Marco', 1705, '25', 107, 'IT', 45.47686000, 10.41315000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908026'),
(58848, 'Ponte San Nicolò', 1753, '34', 107, 'IT', 45.36718000, 11.92341000, '2019-10-05 23:06:44', '2020-05-01 17:22:57', 1, 'Q3908026'),
(58849, 'Ponte San Pietro', 1705, '25', 107, 'IT', 45.70596000, 9.59050000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908026'),
(58850, 'Ponte Taro', 1773, '45', 107, 'IT', 44.82872000, 10.21037000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908032'),
(58851, 'Ponte a Poppi', 1664, '52', 107, 'IT', 43.73171000, 11.76663000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485845'),
(58852, 'Ponte dell\'Olio', 1773, '45', 107, 'IT', 44.86762000, 9.64433000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485845'),
(58853, 'Ponte della Venturina', 1773, '45', 107, 'IT', 44.13042000, 10.99113000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908109'),
(58854, 'Ponte di Barbarano', 1753, '34', 107, 'IT', 45.39206000, 11.57847000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908124'),
(58855, 'Ponte di Castegnero', 1753, '34', 107, 'IT', 45.43672000, 11.60069000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18506722'),
(58856, 'Ponte di Legno', 1705, '25', 107, 'IT', 46.25896000, 10.51048000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18506722'),
(58857, 'Ponte di Nanto', 1753, '34', 107, 'IT', 45.42427000, 11.59400000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18506724'),
(58858, 'Ponte di Piave', 1753, '34', 107, 'IT', 45.71904000, 12.46186000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18506724'),
(58859, 'Ponte in Valtellina', 1705, '25', 107, 'IT', 46.17500000, 9.97785000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18506724'),
(58860, 'Ponte nelle Alpi', 1753, '34', 107, 'IT', 46.18083000, 12.28333000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18506724'),
(58861, 'Ponte nelle Alpi-Polpet', 1753, '34', 107, 'IT', 46.18833000, 12.27833000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18496856'),
(58862, 'Pontebba', 1756, '36', 107, 'IT', 46.50540000, 13.30622000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18496856'),
(58863, 'Pontecagnano', 1669, '72', 107, 'IT', 40.64448000, 14.87628000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18437639'),
(58864, 'Pontecchio Polesine', 1753, '34', 107, 'IT', 45.02020000, 11.81321000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18437639'),
(58865, 'Pontechianale', 1702, '21', 107, 'IT', 44.62123000, 7.03002000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18437639'),
(58866, 'Pontecorvo', 1678, '62', 107, 'IT', 41.45861000, 13.66618000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18437639'),
(58867, 'Pontecurone', 1702, '21', 107, 'IT', 44.95880000, 8.93289000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18437639'),
(58868, 'Pontedassio', 1768, '42', 107, 'IT', 43.93830000, 8.01464000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18437639'),
(58869, 'Pontedera', 1664, '52', 107, 'IT', 43.66141000, 10.63067000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18437639'),
(58870, 'Pontegradella', 1773, '45', 107, 'IT', 44.83813000, 11.66280000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q2491138'),
(58871, 'Pontelandolfo', 1669, '72', 107, 'IT', 41.29231000, 14.68956000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q2491138'),
(58872, 'Pontelangorino', 1773, '45', 107, 'IT', 44.86087000, 12.14864000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18439570'),
(58873, 'Pontelatone', 1669, '72', 107, 'IT', 41.19460000, 14.24814000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18439570'),
(58874, 'Pontelongo', 1753, '34', 107, 'IT', 45.24590000, 12.02582000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18439570'),
(58875, 'Pontenure', 1773, '45', 107, 'IT', 44.99821000, 9.79139000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18439570'),
(58876, 'Ponteranica', 1705, '25', 107, 'IT', 45.73248000, 9.65175000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18439570'),
(58877, 'Ponterio-Pian di Porto', 1683, '55', 107, 'IT', 42.80769000, 12.41024000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18493570'),
(58878, 'Pontestazzemese', 1664, '52', 107, 'IT', 43.99665000, 10.29439000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908215'),
(58879, 'Pontestura', 1702, '21', 107, 'IT', 45.14245000, 8.33324000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908215'),
(58880, 'Pontevico', 1705, '25', 107, 'IT', 45.27315000, 10.09248000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908215'),
(58881, 'Pontey', 1716, '23', 107, 'IT', 45.73886000, 7.58831000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908215'),
(58882, 'Ponti', 1702, '21', 107, 'IT', 44.62847000, 8.36461000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908215'),
(58883, 'Ponti sul Mincio', 1705, '25', 107, 'IT', 45.41208000, 10.68508000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3908215'),
(58884, 'Ponticella', 1773, '45', 107, 'IT', 44.45446000, 11.37851000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18438593'),
(58885, 'Ponticelli', 1669, '72', 107, 'IT', 40.85420000, 14.33038000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q2103859'),
(58886, 'Ponticino', 1664, '52', 107, 'IT', 43.48012000, 11.71622000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485865'),
(58887, 'Pontida', 1705, '25', 107, 'IT', 45.73129000, 9.51141000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485865'),
(58888, 'Pontinia', 1678, '62', 107, 'IT', 41.41097000, 13.04259000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485865'),
(58889, 'Pontinvrea', 1768, '42', 107, 'IT', 44.44205000, 8.43623000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485865'),
(58890, 'Pontirolo Nuovo', 1705, '25', 107, 'IT', 45.56918000, 9.56935000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485865'),
(58891, 'Pontoglio', 1705, '25', 107, 'IT', 45.56938000, 9.85346000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18485865'),
(58892, 'Ponton dell\'Elce', 1678, '62', 107, 'IT', 42.02270000, 12.23505000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58893, 'Pontremoli', 1664, '52', 107, 'IT', 44.37515000, 9.87888000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58894, 'Ponza', 1678, '62', 107, 'IT', 40.89541000, 12.95889000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58895, 'Ponzano', 1753, '34', 107, 'IT', 45.71541000, 12.20444000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58896, 'Ponzano Monferrato', 1702, '21', 107, 'IT', 45.08485000, 8.26553000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58897, 'Ponzano Romano', 1678, '62', 107, 'IT', 42.25693000, 12.57047000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58898, 'Ponzano di Fermo', 1670, '57', 107, 'IT', 43.10390000, 13.65979000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58899, 'Ponzone', 1702, '21', 107, 'IT', 44.58814000, 8.45935000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58900, 'Popoli', 1679, '65', 107, 'IT', 42.16866000, 13.82997000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58901, 'Poppi', 1664, '52', 107, 'IT', 43.72123000, 11.76642000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58902, 'Porano', 1683, '55', 107, 'IT', 42.68099000, 12.10331000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58903, 'Porcari', 1664, '52', 107, 'IT', 43.84152000, 10.61632000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18455168'),
(58904, 'Porcellengo', 1753, '34', 107, 'IT', 45.70613000, 12.14225000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q17651457'),
(58905, 'Porcia', 1756, '36', 107, 'IT', 45.96301000, 12.61642000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q17651457'),
(58906, 'Pordenone', 1756, '36', 107, 'IT', 45.95689000, 12.66051000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q17651457'),
(58907, 'Porlezza', 1705, '25', 107, 'IT', 46.03714000, 9.12921000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q17651457'),
(58908, 'Pornassio', 1768, '42', 107, 'IT', 44.07053000, 7.86954000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q17651457'),
(58909, 'Porotto-Cassama', 1773, '45', 107, 'IT', 44.85000000, 11.55000000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18439580'),
(58910, 'Porpetto', 1756, '36', 107, 'IT', 45.85700000, 13.21703000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18439580'),
(58911, 'Porporano', 1773, '45', 107, 'IT', 44.75266000, 10.35054000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18443027'),
(58912, 'Porretta Terme', 1773, '45', 107, 'IT', 44.16325000, 10.97432000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18443027'),
(58913, 'Portacomaro', 1702, '21', 107, 'IT', 44.95665000, 8.25804000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18443027'),
(58914, 'Portalbera', 1705, '25', 107, 'IT', 45.09789000, 9.31774000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18443027'),
(58915, 'Porte', 1702, '21', 107, 'IT', 44.88714000, 7.26982000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18443027'),
(58916, 'Portella di Mare', 1709, '82', 107, 'IT', 38.07304000, 13.46199000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3453318'),
(58917, 'Portici', 1669, '72', 107, 'IT', 40.81563000, 14.33716000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3453318'),
(58918, 'Portico di Caserta', 1669, '72', 107, 'IT', 41.05562000, 14.28022000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q3453318'),
(58919, 'Portico di Romagna', 1773, '45', 107, 'IT', 44.02657000, 11.78133000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18440198'),
(58920, 'Portico e San Benedetto', 1773, '45', 107, 'IT', 44.02646000, 11.78153000, '2019-10-05 23:06:44', '2019-10-05 23:06:44', 1, 'Q18440198'),
(58921, 'Portigliola', 1703, '78', 107, 'IT', 38.22712000, 16.20287000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18440198'),
(58922, 'Porto', 1669, '72', 107, 'IT', 40.84395000, 14.25724000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18440198'),
(58923, 'Porto Azzurro', 1664, '52', 107, 'IT', 42.76754000, 10.39723000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18440198'),
(58924, 'Porto Ceresio', 1705, '25', 107, 'IT', 45.91285000, 8.89684000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18440198'),
(58925, 'Porto Cervo', 1715, '88', 107, 'IT', 41.14063000, 9.53267000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q919630'),
(58926, 'Porto Cesareo', 1688, '75', 107, 'IT', 40.26228000, 17.89896000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q919630'),
(58927, 'Porto Corsini', 1773, '45', 107, 'IT', 44.49410000, 12.27875000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909148'),
(58928, 'Porto Empedocle', 1709, '82', 107, 'IT', 37.28942000, 13.52494000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909148'),
(58929, 'Porto Ercole', 1664, '52', 107, 'IT', 42.40171000, 11.20523000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q1394400'),
(58930, 'Porto Fuori', 1773, '45', 107, 'IT', 44.40557000, 12.25218000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18446182'),
(58931, 'Porto Garibaldi', 1773, '45', 107, 'IT', 44.68123000, 12.23678000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q1925645'),
(58932, 'Porto Potenza Picena', 1670, '57', 107, 'IT', 43.35751000, 13.69746000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q2122584'),
(58933, 'Porto Recanati', 1670, '57', 107, 'IT', 43.43296000, 13.66165000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q2122584'),
(58934, 'Porto San Giorgio', 1670, '57', 107, 'IT', 43.17784000, 13.79411000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496931'),
(58935, 'Porto Sant\'Elpidio', 1670, '57', 107, 'IT', 43.25297000, 13.75970000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496931'),
(58936, 'Porto Santo Stefano', 1664, '52', 107, 'IT', 42.43825000, 11.11542000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q1431610'),
(58937, 'Porto Tolle', 1753, '34', 107, 'IT', 44.94969000, 12.32453000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q1431610'),
(58938, 'Porto Torres', 1715, '88', 107, 'IT', 40.83375000, 8.40531000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q1431610'),
(58939, 'Porto Valtravaglia', 1705, '25', 107, 'IT', 45.96088000, 8.68109000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q1431610'),
(58940, 'Porto Viro', 1753, '34', 107, 'IT', 45.02678000, 12.21754000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q1431610'),
(58941, 'Porto d\'Adda', 1705, '25', 107, 'IT', 45.66241000, 9.47563000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58942, 'Portobuffolè', 1753, '34', 107, 'IT', 45.85665000, 12.53761000, '2019-10-05 23:06:45', '2020-05-01 17:22:57', 1, 'Q3909190'),
(58943, 'Portocannone', 1695, '67', 107, 'IT', 41.91441000, 15.00845000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58944, 'Portoferraio', 1664, '52', 107, 'IT', 42.81233000, 10.31297000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58945, 'Portofino', 1768, '42', 107, 'IT', 44.30349000, 9.20942000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58946, 'Portogruaro', 1753, '34', 107, 'IT', 45.78071000, 12.84052000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58947, 'Portomaggiore', 1773, '45', 107, 'IT', 44.69763000, 11.80760000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58948, 'Portopalo di Capo Passero', 1709, '82', 107, 'IT', 36.68219000, 15.13378000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58949, 'Portoscuso', 1715, '88', 107, 'IT', 39.20739000, 8.38086000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58950, 'Portovenere', 1768, '42', 107, 'IT', 44.05083000, 9.83431000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58951, 'Portula', 1702, '21', 107, 'IT', 45.67512000, 8.17244000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58952, 'Posada', 1715, '88', 107, 'IT', 40.63203000, 9.71904000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q3909190'),
(58953, 'Posatora', 1670, '57', 107, 'IT', 43.59917000, 13.48984000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496076'),
(58954, 'Posillipo', 1669, '72', 107, 'IT', 40.81279000, 14.20001000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496076'),
(58955, 'Posina', 1753, '34', 107, 'IT', 45.79106000, 11.26244000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496076'),
(58956, 'Positano', 1669, '72', 107, 'IT', 40.62829000, 14.48427000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496076'),
(58957, 'Possagno', 1753, '34', 107, 'IT', 45.84795000, 11.88467000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496076'),
(58958, 'Posta', 1678, '62', 107, 'IT', 42.52562000, 13.09723000, '2019-10-05 23:06:45', '2019-10-05 23:06:45', 1, 'Q18496076');

