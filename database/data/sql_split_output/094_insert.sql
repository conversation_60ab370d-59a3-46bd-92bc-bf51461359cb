INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(47303, 'Torigni-sur-Vire', 4804, 'NOR', 75, 'FR', 49.03702000, -0.98214000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q282706'),
(47304, 'Torreilles', 4799, 'OCC', 75, 'FR', 42.75433000, 2.99292000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q282706'),
(47305, 'Tosse', 4795, 'NAQ', 75, 'FR', 43.68916000, -1.33262000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q282706'),
(47306, '<PERSON><PERSON><PERSON>', 4798, 'ARA', 75, 'FR', 46.13965000, 5.31158000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q282706'),
(47307, 'Toucy', 4825, 'BFC', 75, 'FR', 47.73602000, 3.29502000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q837929'),
(47308, 'Toufflers', 4828, 'HDF', 75, 'FR', 50.66039000, 3.23358000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q837929'),
(47309, 'Toul', 4820, 'GES', 75, 'FR', 48.68075000, 5.89115000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q154728'),
(47310, 'Toulaud', 4798, 'ARA', 75, 'FR', 44.89773000, 4.81639000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q154728'),
(47311, 'Toulenne', 4795, 'NAQ', 75, 'FR', 44.55665000, -0.26328000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q154728'),
(47312, 'Toulon', 4812, 'PAC', 75, 'FR', 43.12442000, 5.92836000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q44160'),
(47313, 'Toulon-sur-Allier', 4798, 'ARA', 75, 'FR', 46.51845000, 3.35989000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q262979'),
(47314, 'Toulon-sur-Arroux', 4825, 'BFC', 75, 'FR', 46.69345000, 4.13869000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q983445'),
(47315, 'Toulouges', 4799, 'OCC', 75, 'FR', 42.66961000, 2.83008000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q983445'),
(47316, 'Toulouse', 4799, 'OCC', 75, 'FR', 43.60426000, 1.44367000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q7880'),
(47317, 'Touques', 4804, 'NOR', 75, 'FR', 49.34443000, 0.10218000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q7880'),
(47318, 'Touquin', 4796, 'IDF', 75, 'FR', 48.73498000, 3.01222000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q7880'),
(47319, 'Tourbes', 4799, 'OCC', 75, 'FR', 43.44615000, 3.37852000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q7880'),
(47320, 'Tourcoing', 4828, 'HDF', 75, 'FR', 50.72391000, 3.16117000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q182481'),
(47321, 'Tourlaville', 4804, 'NOR', 75, 'FR', 49.63829000, -1.56639000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q627225'),
(47322, 'Tournan-en-Brie', 4796, 'IDF', 75, 'FR', 48.74146000, 2.77200000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q627225'),
(47323, 'Tournay', 4799, 'OCC', 75, 'FR', 43.18438000, 0.24454000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q627225'),
(47324, 'Tournefeuille', 4799, 'OCC', 75, 'FR', 43.58872000, 1.31922000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q328022'),
(47325, 'Tournehem-sur-la-Hem', 4828, 'HDF', 75, 'FR', 50.80000000, 2.05000000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q328022'),
(47326, 'Tournes', 4820, 'GES', 75, 'FR', 49.79700000, 4.63856000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q328022'),
(47327, 'Tournon-Saint-Martin', 4818, 'CVL', 75, 'FR', 46.73423000, 0.95514000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q1072164'),
(47328, 'Tournon-sur-Rhône', 4798, 'ARA', 75, 'FR', 45.06667000, 4.83333000, '2019-10-05 22:49:06', '2020-05-01 17:22:43', 1, 'Q213155'),
(47329, 'Tournus', 4825, 'BFC', 75, 'FR', 46.56758000, 4.90574000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q501900'),
(47330, 'Tourouvre', 4804, 'NOR', 75, 'FR', 48.58951000, 0.65253000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q501900'),
(47331, 'Tourrette-Levens', 4812, 'PAC', 75, 'FR', 43.78640000, 7.27598000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q501900'),
(47332, 'Tourrettes-sur-Loup', 4812, 'PAC', 75, 'FR', 43.71569000, 7.05892000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q466642'),
(47333, 'Tours', 4818, 'CVL', 75, 'FR', 47.39484000, 0.70398000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q288'),
(47334, 'Tours-sur-Marne', 4820, 'GES', 75, 'FR', 49.04873000, 4.12060000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q288'),
(47335, 'Tourves', 4812, 'PAC', 75, 'FR', 43.40803000, 5.92392000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q818280'),
(47336, 'Tourville-la-Rivière', 4804, 'NOR', 75, 'FR', 49.32782000, 1.10551000, '2019-10-05 22:49:06', '2020-05-01 17:22:45', 1, 'Q818280'),
(47337, 'Tourville-sur-Arques', 4804, 'NOR', 75, 'FR', 49.85926000, 1.10238000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q818280'),
(47338, 'Tourville-sur-Odon', 4804, 'NOR', 75, 'FR', 49.14154000, -0.50128000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q818280'),
(47339, 'Toury', 4818, 'CVL', 75, 'FR', 48.19397000, 1.93484000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q818280'),
(47340, 'Toussieu', 4798, 'ARA', 75, 'FR', 45.65443000, 4.98495000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q818280'),
(47341, 'Toutainville', 4804, 'NOR', 75, 'FR', 49.36474000, 0.46538000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q818280'),
(47342, 'Toutlemonde', 4802, 'PDL', 75, 'FR', 47.05488000, -0.76548000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q579699'),
(47343, 'Touvois', 4802, 'PDL', 75, 'FR', 46.90208000, -1.68333000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47344, 'Touvre', 4795, 'NAQ', 75, 'FR', 45.66667000, 0.25000000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q368294'),
(47345, 'Tracy-le-Mont', 4828, 'HDF', 75, 'FR', 49.47225000, 3.00939000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47346, 'Tramoyes', 4798, 'ARA', 75, 'FR', 45.87599000, 4.96502000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q648148'),
(47347, 'Trangé', 4802, 'PDL', 75, 'FR', 48.02706000, 0.11054000, '2019-10-05 22:49:06', '2020-05-01 17:22:46', 1, 'Q648148'),
(47348, 'Trans-en-Provence', 4812, 'PAC', 75, 'FR', 43.50326000, 6.48641000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q817939'),
(47349, 'Trappes', 4796, 'IDF', 75, 'FR', 48.77413000, 2.01781000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q817939'),
(47350, 'Traînou', 4818, 'CVL', 75, 'FR', 47.97353000, 2.10399000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q817939'),
(47351, 'Treffiagat', 4807, 'BRE', 75, 'FR', 47.81667000, -4.26667000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q494984'),
(47352, 'Treffléan', 4807, 'BRE', 75, 'FR', 47.68163000, -2.61287000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q494984'),
(47353, 'Treffort-Cuisiat', 4798, 'ARA', 75, 'FR', 46.26667000, 5.36667000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q733076'),
(47354, 'Treignac', 4795, 'NAQ', 75, 'FR', 45.53696000, 1.79520000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q194853'),
(47355, 'Treillières', 4802, 'PDL', 75, 'FR', 47.33060000, -1.61918000, '2019-10-05 22:49:06', '2020-05-01 17:22:46', 1, 'Q194853'),
(47356, 'Treize-Septiers', 4802, 'PDL', 75, 'FR', 46.98524000, -1.22921000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47357, 'Tremblay', 4807, 'BRE', 75, 'FR', 48.42216000, -1.47555000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47358, 'Tremblay-en-France', 4796, 'IDF', 75, 'FR', 48.94956000, 2.56840000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q242497'),
(47359, 'Trept', 4798, 'ARA', 75, 'FR', 45.68742000, 5.31843000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47360, 'Tresbœuf', 4807, 'BRE', 75, 'FR', 47.88333000, -1.55000000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, '********'),
(47361, 'Tresques', 4799, 'OCC', 75, 'FR', 44.10689000, 4.58739000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47362, 'Tressange', 4820, 'GES', 75, 'FR', 49.40271000, 5.98084000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47363, 'Tresserve', 4798, 'ARA', 75, 'FR', 45.67610000, 5.89906000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47364, 'Tresses', 4795, 'NAQ', 75, 'FR', 44.84781000, -0.46296000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q379602'),
(47365, 'Tressin', 4828, 'HDF', 75, 'FR', 50.61750000, 3.19354000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q379602'),
(47366, 'Trets', 4812, 'PAC', 75, 'FR', 43.44818000, 5.68328000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q640716'),
(47367, 'Triaize', 4802, 'PDL', 75, 'FR', 46.39265000, -1.19785000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47368, 'Tricot', 4828, 'HDF', 75, 'FR', 49.56080000, 2.58789000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47369, 'Trie-Château', 4828, 'HDF', 75, 'FR', 49.28540000, 1.82129000, '2019-10-05 22:49:06', '2020-05-01 17:22:45', 1, 'Q832144'),
(47370, 'Trie-sur-Baïse', 4799, 'OCC', 75, 'FR', 43.33333000, 0.36667000, '2019-10-05 22:49:06', '2020-05-01 17:22:46', 1, 'Q832144'),
(47371, 'Triel-sur-Seine', 4796, 'IDF', 75, 'FR', 48.97818000, 2.00743000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q243605'),
(47372, 'Trieux', 4820, 'GES', 75, 'FR', 49.32462000, 5.93049000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q243605'),
(47373, 'Trignac', 4802, 'PDL', 75, 'FR', 47.31809000, -2.18895000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q243605'),
(47374, 'Triguères', 4818, 'CVL', 75, 'FR', 47.93975000, 2.98570000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q243605'),
(47375, 'Trilport', 4796, 'IDF', 75, 'FR', 48.95685000, 2.95076000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q267551'),
(47376, 'Trith-Saint-Léger', 4828, 'HDF', 75, 'FR', 50.31667000, 3.48333000, '2019-10-05 22:49:06', '2020-05-01 17:22:45', 1, '********'),
(47377, 'Trizay', 4795, 'NAQ', 75, 'FR', 45.88276000, -0.89697000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47378, 'Troarn', 4804, 'NOR', 75, 'FR', 49.17835000, -0.18169000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47379, 'Troissereux', 4828, 'HDF', 75, 'FR', 49.47998000, 2.04485000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47380, 'Tronville-en-Barrois', 4820, 'GES', 75, 'FR', 48.71977000, 5.27808000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47381, 'Trosly-Breuil', 4828, 'HDF', 75, 'FR', 49.40000000, 2.96667000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47382, 'Trouillas', 4799, 'OCC', 75, 'FR', 42.61089000, 2.80821000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, '********'),
(47383, 'Trouville-sur-Mer', 4804, 'NOR', 75, 'FR', 49.36570000, 0.08041000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q272320'),
(47384, 'Trouy', 4818, 'CVL', 75, 'FR', 47.01153000, 2.36018000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q272320'),
(47385, 'Troyes', 4820, 'GES', 75, 'FR', 48.30073000, 4.08524000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q5489'),
(47386, 'Truchtersheim', 4820, 'GES', 75, 'FR', 48.66313000, 7.60752000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q5489'),
(47387, 'Trun', 4804, 'NOR', 75, 'FR', 48.84268000, 0.03268000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q5489'),
(47388, 'Truyes', 4818, 'CVL', 75, 'FR', 47.27299000, 0.85179000, '2019-10-05 22:49:06', '2019-10-05 22:49:06', 1, 'Q5489'),
(47389, 'Trèbes', 4799, 'OCC', 75, 'FR', 43.21064000, 2.44165000, '2019-10-05 22:49:06', '2020-05-01 17:22:46', 1, 'Q187481'),
(47390, 'Trébeurden', 4807, 'BRE', 75, 'FR', 48.76667000, -3.56667000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q626342'),
(47391, 'Trédarzec', 4807, 'BRE', 75, 'FR', 48.78583000, -3.20100000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q626342'),
(47392, 'Trédrez-Locquémeau', 4807, 'BRE', 75, 'FR', 48.70000000, -3.56667000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q193539'),
(47393, 'Trégastel', 4807, 'BRE', 75, 'FR', 48.81667000, -3.50000000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q193539'),
(47394, 'Tréguier', 4807, 'BRE', 75, 'FR', 48.78333000, -3.23333000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q235297'),
(47395, 'Trégunc', 4807, 'BRE', 75, 'FR', 47.85000000, -3.85000000, '2019-10-05 22:49:06', '2020-05-01 17:22:44', 1, 'Q382095'),
(47396, 'Trélazé', 4802, 'PDL', 75, 'FR', 47.44565000, -0.46540000, '2019-10-05 22:49:06', '2020-05-01 17:22:46', 1, '********'),
(47397, 'Trélissac', 4795, 'NAQ', 75, 'FR', 45.19766000, 0.78615000, '2019-10-05 22:49:06', '2020-05-01 17:22:46', 1, '********'),
(47398, 'Trélivan', 4807, 'BRE', 75, 'FR', 48.43198000, -2.11748000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, '********'),
(47399, 'Trélon', 4828, 'HDF', 75, 'FR', 50.05805000, 4.10200000, '2019-10-05 22:49:07', '2020-05-01 17:22:45', 1, 'Q663569'),
(47400, 'Trélévern', 4807, 'BRE', 75, 'FR', 48.81071000, -3.37141000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, 'Q622475'),
(47401, 'Trémentines', 4802, 'PDL', 75, 'FR', 47.12357000, -0.78500000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, '********'),
(47402, 'Trémery', 4820, 'GES', 75, 'FR', 49.24610000, 6.22369000, '2019-10-05 22:49:07', '2020-05-01 17:22:45', 1, '********'),
(47403, 'Trémuson', 4807, 'BRE', 75, 'FR', 48.52325000, -2.84833000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, '********'),
(47404, 'Tréméven', 4807, 'BRE', 75, 'FR', 47.90000000, -3.53333000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, '********'),
(47405, 'Tréon', 4818, 'CVL', 75, 'FR', 48.67709000, 1.32668000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, '********'),
(47406, 'Trévol', 4798, 'ARA', 75, 'FR', 46.62924000, 3.30599000, '2019-10-05 22:49:07', '2020-05-01 17:22:43', 1, '********'),
(47407, 'Trévou-Tréguignec', 4807, 'BRE', 75, 'FR', 48.81869000, -3.34132000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, 'Q471076'),
(47408, 'Trévoux', 4798, 'ARA', 75, 'FR', 45.94281000, 4.77143000, '2019-10-05 22:49:07', '2020-05-01 17:22:43', 1, 'Q244565'),
(47409, 'Trévé', 4807, 'BRE', 75, 'FR', 48.21147000, -2.79317000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, 'Q244565'),
(47410, 'Tucquegnieux', 4820, 'GES', 75, 'FR', 49.31010000, 5.89448000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, '********'),
(47411, 'Tuffé', 4802, 'PDL', 75, 'FR', 48.11319000, 0.51551000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, '********'),
(47412, 'Tulette', 4798, 'ARA', 75, 'FR', 44.28656000, 4.93122000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q266054'),
(47413, 'Tulle', 4795, 'NAQ', 75, 'FR', 45.26582000, 1.77233000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q266054'),
(47414, 'Tullins', 4798, 'ARA', 75, 'FR', 45.30239000, 5.49077000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q470740'),
(47415, 'Turckheim', 4820, 'GES', 75, 'FR', 48.08748000, 7.27707000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q157729'),
(47416, 'Turretot', 4804, 'NOR', 75, 'FR', 49.60923000, 0.23422000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q157729'),
(47417, 'Téteghem', 4828, 'HDF', 75, 'FR', 51.01859000, 2.44454000, '2019-10-05 22:49:07', '2020-05-01 17:22:45', 1, 'Q157729'),
(47418, 'Tôtes', 4804, 'NOR', 75, 'FR', 49.68091000, 1.04649000, '2019-10-05 22:49:07', '2020-05-01 17:22:45', 1, 'Q157729'),
(47419, 'Uberach', 4820, 'GES', 75, 'FR', 48.84990000, 7.62934000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q157729'),
(47420, 'Uchaud', 4799, 'OCC', 75, 'FR', 43.75857000, 4.26843000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q157729'),
(47421, 'Uchaux', 4812, 'PAC', 75, 'FR', 44.21667000, 4.80000000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q960306'),
(47422, 'Uckange', 4820, 'GES', 75, 'FR', 49.30304000, 6.14920000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q21925'),
(47423, 'Uffholtz', 4820, 'GES', 75, 'FR', 47.82082000, 7.17785000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q429411'),
(47424, 'Ugine', 4798, 'ARA', 75, 'FR', 45.75571000, 6.41503000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q694800'),
(47425, 'Ully-Saint-Georges', 4828, 'HDF', 75, 'FR', 49.27914000, 2.28094000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q750669'),
(47426, 'Ungersheim', 4820, 'GES', 75, 'FR', 47.87848000, 7.30797000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q750669'),
(47427, 'Unieux', 4798, 'ARA', 75, 'FR', 45.40395000, 4.27094000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q750669'),
(47428, 'Unverre', 4818, 'CVL', 75, 'FR', 48.19829000, 1.09207000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q1344345'),
(47429, 'Upie', 4798, 'ARA', 75, 'FR', 44.80250000, 4.97673000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q1344345'),
(47430, 'Urcuit', 4795, 'NAQ', 75, 'FR', 43.48594000, -1.33668000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q838728'),
(47431, 'Uriménil', 4820, 'GES', 75, 'FR', 48.10079000, 6.40046000, '2019-10-05 22:49:07', '2020-05-01 17:22:45', 1, 'Q838728'),
(47432, 'Urmatt', 4820, 'GES', 75, 'FR', 48.52752000, 7.32565000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q838728'),
(47433, 'Urrugne', 4795, 'NAQ', 75, 'FR', 43.36361000, -1.69921000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q672401'),
(47434, 'Urt', 4795, 'NAQ', 75, 'FR', 43.49009000, -1.29744000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q842706'),
(47435, 'Urville-Nacqueville', 4804, 'NOR', 75, 'FR', 49.67444000, -1.73664000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q683550'),
(47436, 'Urzy', 4825, 'BFC', 75, 'FR', 47.04877000, 3.20295000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q683550'),
(47437, 'Us', 4796, 'IDF', 75, 'FR', 49.10000000, 1.96667000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q920643'),
(47438, 'Ussac', 4795, 'NAQ', 75, 'FR', 45.19389000, 1.51337000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q920643'),
(47439, 'Ussel', 4795, 'NAQ', 75, 'FR', 45.54804000, 2.30917000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q920643'),
(47440, 'Usson-du-Poitou', 4795, 'NAQ', 75, 'FR', 46.27782000, 0.52816000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q682916'),
(47441, 'Usson-en-Forez', 4798, 'ARA', 75, 'FR', 45.39174000, 3.94142000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q1106328'),
(47442, 'Ustaritz', 4795, 'NAQ', 75, 'FR', 43.39650000, -1.45603000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q269874'),
(47443, 'Uxegney', 4820, 'GES', 75, 'FR', 48.19652000, 6.36971000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q286333'),
(47444, 'Uxem', 4828, 'HDF', 75, 'FR', 51.02170000, 2.48376000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q286333'),
(47445, 'Uzein', 4795, 'NAQ', 75, 'FR', 43.40000000, -0.43333000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q197376'),
(47446, 'Uzemain', 4820, 'GES', 75, 'FR', 48.08572000, 6.34443000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q200354'),
(47447, 'Uzerche', 4795, 'NAQ', 75, 'FR', 45.42462000, 1.56341000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q201906'),
(47448, 'Uzès', 4799, 'OCC', 75, 'FR', 44.01362000, 4.41529000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, 'Q216966'),
(47449, 'Vaas', 4802, 'PDL', 75, 'FR', 47.66890000, 0.31677000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q216966'),
(47450, 'Vacon', 4820, 'GES', 75, 'FR', 48.66865000, 5.60024000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q216966'),
(47451, 'Vacqueyras', 4812, 'PAC', 75, 'FR', 44.13835000, 4.98332000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q216966'),
(47452, 'Vacquiers', 4799, 'OCC', 75, 'FR', 43.77667000, 1.48127000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q216966'),
(47453, 'Vagney', 4820, 'GES', 75, 'FR', 48.00629000, 6.71740000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q922686'),
(47454, 'Vaiges', 4802, 'PDL', 75, 'FR', 48.04025000, -0.47513000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q922686'),
(47455, 'Vailhauquès', 4799, 'OCC', 75, 'FR', 43.67159000, 3.72042000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, 'Q922686'),
(47456, 'Vailly-sur-Aisne', 4828, 'HDF', 75, 'FR', 49.40834000, 3.51631000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q589513'),
(47457, 'Vaires-sur-Marne', 4796, 'IDF', 75, 'FR', 48.87649000, 2.63982000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q675577'),
(47458, 'Vairé', 4802, 'PDL', 75, 'FR', 46.60104000, -1.75538000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, 'Q749114'),
(47459, 'Vaison-la-Romaine', 4812, 'PAC', 75, 'FR', 44.23896000, 5.07461000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q504887'),
(47460, 'Vaivre-et-Montoille', 4825, 'BFC', 75, 'FR', 47.63021000, 6.10362000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q504887'),
(47461, 'Val Thorens', 4798, 'ARA', 75, 'FR', 45.29777000, 6.58377000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q504887'),
(47462, 'Val-de-Marne', 4796, 'IDF', 75, 'FR', 48.78149000, 2.49331000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q12788'),
(47463, 'Val-de-Meuse', 4820, 'GES', 75, 'FR', 48.00000000, 5.50000000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q12788'),
(47464, 'Val-de-Reuil', 4804, 'NOR', 75, 'FR', 49.27385000, 1.21021000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q921780'),
(47465, 'Val-d’Isère', 4798, 'ARA', 75, 'FR', 45.45142000, 6.97455000, '2019-10-05 22:49:07', '2020-05-01 17:22:43', 1, 'Q207589'),
(47466, 'Valady', 4799, 'OCC', 75, 'FR', 44.45633000, 2.42746000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q207589'),
(47467, 'Valbonne', 4812, 'PAC', 75, 'FR', 43.63292000, 6.99911000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q207589'),
(47468, 'Valdahon', 4825, 'BFC', 75, 'FR', 47.15000000, 6.35000000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q207589'),
(47469, 'Valdoie', 4825, 'BFC', 75, 'FR', 47.67041000, 6.84203000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q207589'),
(47470, 'Valence', 4799, 'OCC', 75, 'FR', 44.10823000, 0.89101000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q207589'),
(47471, 'Valence', 4798, 'ARA', 75, 'FR', 44.92560000, 4.90956000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q8848'),
(47472, 'Valence-d’Albigeois', 4799, 'OCC', 75, 'FR', 44.01928000, 2.40534000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, 'Q8848'),
(47473, 'Valence-sur-Baïse', 4799, 'OCC', 75, 'FR', 43.88270000, 0.38111000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, 'Q8848'),
(47474, 'Valenciennes', 4828, 'HDF', 75, 'FR', 50.35909000, 3.52506000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q163795'),
(47475, 'Valencin', 4798, 'ARA', 75, 'FR', 45.61109000, 5.02935000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q163795'),
(47476, 'Valensole', 4812, 'PAC', 75, 'FR', 43.83766000, 5.98392000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q466634'),
(47477, 'Valentigney', 4825, 'BFC', 75, 'FR', 47.46388000, 6.83168000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q466634'),
(47478, 'Valenton', 4796, 'IDF', 75, 'FR', 48.74527000, 2.46467000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q243077'),
(47479, 'Valençay', 4818, 'CVL', 75, 'FR', 47.16207000, 1.56852000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, 'Q474503'),
(47480, 'Valergues', 4799, 'OCC', 75, 'FR', 43.66824000, 4.06124000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q474503'),
(47481, 'Valff', 4820, 'GES', 75, 'FR', 48.42140000, 7.52058000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q21588'),
(47482, 'Valfin-lès-Saint-Claude', 4825, 'BFC', 75, 'FR', 46.43758000, 5.85513000, '2019-10-05 22:49:07', '2020-05-01 17:22:44', 1, 'Q49369833'),
(47483, 'Valframbert', 4804, 'NOR', 75, 'FR', 48.46465000, 0.10828000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q49369833'),
(47484, 'Vallabrègues', 4799, 'OCC', 75, 'FR', 43.85307000, 4.62662000, '2019-10-05 22:49:07', '2020-05-01 17:22:46', 1, 'Q49369833'),
(47485, 'Vallauris', 4812, 'PAC', 75, 'FR', 43.57803000, 7.05451000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q234205'),
(47486, 'Valleiry', 4798, 'ARA', 75, 'FR', 46.11106000, 5.97037000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q234205'),
(47487, 'Valleraugue', 4799, 'OCC', 75, 'FR', 44.08130000, 3.64154000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q322688'),
(47488, 'Valleroy', 4820, 'GES', 75, 'FR', 49.20944000, 5.93703000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q322688'),
(47489, 'Vallet', 4802, 'PDL', 75, 'FR', 47.16227000, -1.26607000, '2019-10-05 22:49:07', '2019-10-05 22:49:07', 1, 'Q322688'),
(47490, 'Valliquerville', 4804, 'NOR', 75, 'FR', 49.61385000, 0.68703000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q322688'),
(47491, 'Vallières', 4798, 'ARA', 75, 'FR', 45.90043000, 5.93863000, '2019-10-05 22:49:08', '2020-05-01 17:22:43', 1, 'Q322688'),
(47492, 'Valloire', 4798, 'ARA', 75, 'FR', 45.16542000, 6.42998000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q819204'),
(47493, 'Vallon-Pont-d’Arc', 4798, 'ARA', 75, 'FR', 44.40685000, 4.39374000, '2019-10-05 22:49:08', '2020-05-01 17:22:43', 1, 'Q238386'),
(47494, 'Vallon-en-Sully', 4798, 'ARA', 75, 'FR', 46.53629000, 2.60804000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q238386'),
(47495, 'Valmondois', 4796, 'IDF', 75, 'FR', 49.09730000, 2.18996000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q258502'),
(47496, 'Valmont', 4820, 'GES', 75, 'FR', 49.08430000, 6.69781000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q258502'),
(47497, 'Valognes', 4804, 'NOR', 75, 'FR', 49.50881000, -1.47047000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q326879'),
(47498, 'Valras-Plage', 4799, 'OCC', 75, 'FR', 43.24808000, 3.29032000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q326879'),
(47499, 'Valros', 4799, 'OCC', 75, 'FR', 43.41956000, 3.36506000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q326879'),
(47500, 'Valréas', 4812, 'PAC', 75, 'FR', 44.38490000, 4.99125000, '2019-10-05 22:49:08', '2020-05-01 17:22:47', 1, 'Q478921'),
(47501, 'Vals-les-Bains', 4798, 'ARA', 75, 'FR', 44.66561000, 4.36615000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q478921'),
(47502, 'Vals-près-le-Puy', 4798, 'ARA', 75, 'FR', 45.03155000, 3.87787000, '2019-10-05 22:49:08', '2020-05-01 17:22:43', 1, 'Q478921'),
(47503, 'Vandœuvre-lès-Nancy', 4820, 'GES', 75, 'FR', 48.65000000, 6.18333000, '2019-10-05 22:49:08', '2020-05-01 17:22:45', 1, 'Q320308'),
(47504, 'Vannes', 4807, 'BRE', 75, 'FR', 47.65790000, -2.75574000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q6593'),
(47505, 'Vanves', 4796, 'IDF', 75, 'FR', 48.82345000, 2.29025000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q234728'),
(47506, 'Var', 4812, 'PAC', 75, 'FR', 43.45860000, 6.29145000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q12789'),
(47507, 'Varades', 4802, 'PDL', 75, 'FR', 47.38458000, -1.02842000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q12789'),
(47508, 'Varangéville', 4820, 'GES', 75, 'FR', 48.63675000, 6.31875000, '2019-10-05 22:49:08', '2020-05-01 17:22:45', 1, 'Q524836'),
(47509, 'Varengeville-sur-Mer', 4804, 'NOR', 75, 'FR', 49.90475000, 0.99479000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q816277'),
(47510, 'Varennes-Jarcy', 4796, 'IDF', 75, 'FR', 48.67914000, 2.56152000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q696041'),
(47511, 'Varennes-Saint-Sauveur', 4825, 'BFC', 75, 'FR', 46.48226000, 5.24349000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q696041'),
(47512, 'Varennes-Vauzelles', 4825, 'BFC', 75, 'FR', 47.01678000, 3.14037000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q27619'),
(47513, 'Varennes-le-Grand', 4825, 'BFC', 75, 'FR', 46.71868000, 4.86872000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q27619'),
(47514, 'Varennes-sur-Allier', 4798, 'ARA', 75, 'FR', 46.31318000, 3.40147000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q512351'),
(47515, 'Varennes-sur-Loire', 4802, 'PDL', 75, 'FR', 47.23767000, 0.05350000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47516, 'Varennes-sur-Seine', 4796, 'IDF', 75, 'FR', 48.37304000, 2.92571000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47517, 'Varetz', 4795, 'NAQ', 75, 'FR', 45.19392000, 1.45063000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47518, 'Varilhes', 4799, 'OCC', 75, 'FR', 43.04514000, 1.62805000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47519, 'Varois-et-Chaignot', 4825, 'BFC', 75, 'FR', 47.35059000, 5.12838000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47520, 'Varrains', 4802, 'PDL', 75, 'FR', 47.22305000, -0.06033000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47521, 'Varreddes', 4796, 'IDF', 75, 'FR', 49.00305000, 2.92788000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47522, 'Vars', 4795, 'NAQ', 75, 'FR', 45.76256000, 0.12478000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47523, 'Varzy', 4825, 'BFC', 75, 'FR', 47.35810000, 3.38619000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47524, 'Vasles', 4795, 'NAQ', 75, 'FR', 46.57618000, -0.02638000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47525, 'Vasselay', 4818, 'CVL', 75, 'FR', 47.15686000, 2.38963000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47526, 'Vassy', 4804, 'NOR', 75, 'FR', 48.85381000, -0.67485000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47527, 'Vatan', 4818, 'CVL', 75, 'FR', 47.07447000, 1.81010000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47528, 'Vauchrétien', 4802, 'PDL', 75, 'FR', 47.33234000, -0.47678000, '2019-10-05 22:49:08', '2020-05-01 17:22:46', 1, '********'),
(47529, 'Vaucouleurs', 4820, 'GES', 75, 'FR', 48.60313000, 5.66659000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47530, 'Vaucresson', 4796, 'IDF', 75, 'FR', 48.84078000, 2.15652000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q638656'),
(47531, 'Vaudelnay', 4802, 'PDL', 75, 'FR', 47.13813000, -0.20677000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q638656'),
(47532, 'Vaudry', 4804, 'NOR', 75, 'FR', 48.84130000, -0.85309000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q217143'),
(47533, 'Vaugneray', 4798, 'ARA', 75, 'FR', 45.73791000, 4.65645000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q217143'),
(47534, 'Vaugrigneuse', 4796, 'IDF', 75, 'FR', 48.60263000, 2.12218000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47535, 'Vauhallan', 4796, 'IDF', 75, 'FR', 48.73354000, 2.20277000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q661059'),
(47536, 'Vaujours', 4796, 'IDF', 75, 'FR', 48.93022000, 2.57110000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q661059'),
(47537, 'Vaulnaveys-le-Bas', 4798, 'ARA', 75, 'FR', 45.10020000, 5.82532000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q661059'),
(47538, 'Vaulnaveys-le-Haut', 4798, 'ARA', 75, 'FR', 45.12524000, 5.81723000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q661059'),
(47539, 'Vaulx-Milieu', 4798, 'ARA', 75, 'FR', 45.61329000, 5.18371000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q661059'),
(47540, 'Vaulx-Vraucourt', 4828, 'HDF', 75, 'FR', 50.14910000, 2.90830000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q661059'),
(47541, 'Vaulx-en-Velin', 4798, 'ARA', 75, 'FR', 45.78693000, 4.92510000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q13596'),
(47542, 'Vaumoise', 4828, 'HDF', 75, 'FR', 49.23525000, 2.98077000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q1335304'),
(47543, 'Vauréal', 4796, 'IDF', 75, 'FR', 49.03333000, 2.03333000, '2019-10-05 22:49:08', '2020-05-01 17:22:43', 1, '********'),
(47544, 'Vauvert', 4799, 'OCC', 75, 'FR', 43.69529000, 4.27705000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47545, 'Vaux-en-Bugey', 4798, 'ARA', 75, 'FR', 45.92671000, 5.35141000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47546, 'Vaux-le-Pénil', 4796, 'IDF', 75, 'FR', 48.52803000, 2.69165000, '2019-10-05 22:49:08', '2020-05-01 17:22:43', 1, 'Q242983'),
(47547, 'Vaux-sur-Mer', 4795, 'NAQ', 75, 'FR', 45.64606000, -1.05841000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q977527'),
(47548, 'Vaux-sur-Seine', 4796, 'IDF', 75, 'FR', 49.01271000, 1.96942000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q266938'),
(47549, 'Vay', 4802, 'PDL', 75, 'FR', 47.55466000, -1.70095000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q266938'),
(47550, 'Vayrac', 4799, 'OCC', 75, 'FR', 44.95337000, 1.70358000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47551, 'Vayres', 4795, 'NAQ', 75, 'FR', 44.90000000, -0.31667000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47552, 'Veauche', 4798, 'ARA', 75, 'FR', 45.56326000, 4.29192000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q686031'),
(47553, 'Vecoux', 4820, 'GES', 75, 'FR', 47.97797000, 6.63651000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q900767'),
(47554, 'Vedène', 4812, 'PAC', 75, 'FR', 43.97744000, 4.90428000, '2019-10-05 22:49:08', '2020-05-01 17:22:47', 1, 'Q507677'),
(47555, 'Veigné', 4818, 'CVL', 75, 'FR', 47.28556000, 0.74079000, '2019-10-05 22:49:08', '2020-05-01 17:22:44', 1, '********'),
(47556, 'Veigy', 4798, 'ARA', 75, 'FR', 46.26787000, 6.26304000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47557, 'Velaine-en-Haye', 4820, 'GES', 75, 'FR', 48.69990000, 6.02754000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47558, 'Velaines', 4820, 'GES', 75, 'FR', 48.69880000, 5.30483000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47559, 'Velars-sur-Ouche', 4825, 'BFC', 75, 'FR', 47.31960000, 4.90594000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47560, 'Velaux', 4812, 'PAC', 75, 'FR', 43.52839000, 5.25661000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q686054'),
(47561, 'Velleron', 4812, 'PAC', 75, 'FR', 43.95742000, 5.02936000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q672387'),
(47562, 'Venansault', 4802, 'PDL', 75, 'FR', 46.68516000, -1.51415000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q616881'),
(47563, 'Venarey-les-Laumes', 4825, 'BFC', 75, 'FR', 47.54202000, 4.46022000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q224460'),
(47564, 'Venasque', 4812, 'PAC', 75, 'FR', 43.99595000, 5.14666000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q475991'),
(47565, 'Vence', 4812, 'PAC', 75, 'FR', 43.72254000, 7.11183000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q192368'),
(47566, 'Vendargues', 4799, 'OCC', 75, 'FR', 43.65833000, 3.97000000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47567, 'Vendat', 4798, 'ARA', 75, 'FR', 46.16387000, 3.35366000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47568, 'Vendays-Montalivet', 4795, 'NAQ', 75, 'FR', 45.35492000, -1.06088000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47569, 'Vendegies-sur-Écaillon', 4828, 'HDF', 75, 'FR', 50.26224000, 3.51256000, '2019-10-05 22:49:08', '2020-05-01 17:22:45', 1, 'Q1105217'),
(47570, 'Vendenheim', 4820, 'GES', 75, 'FR', 48.66948000, 7.70983000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q1105217'),
(47571, 'Vendeuvre-du-Poitou', 4795, 'NAQ', 75, 'FR', 46.73579000, 0.30996000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q739394'),
(47572, 'Vendeuvre-sur-Barse', 4820, 'GES', 75, 'FR', 48.23786000, 4.46905000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q739394'),
(47573, 'Vendeville', 4828, 'HDF', 75, 'FR', 50.57693000, 3.07870000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q739394'),
(47574, 'Vendin-le-Vieil', 4828, 'HDF', 75, 'FR', 50.47385000, 2.86674000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q852562'),
(47575, 'Vendin-lès-Béthune', 4828, 'HDF', 75, 'FR', 50.54012000, 2.60043000, '2019-10-05 22:49:08', '2020-05-01 17:22:45', 1, 'Q852562'),
(47576, 'Vendrennes', 4802, 'PDL', 75, 'FR', 46.82523000, -1.12357000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47577, 'Vendres', 4799, 'OCC', 75, 'FR', 43.26977000, 3.22341000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47578, 'Vendôme', 4818, 'CVL', 75, 'FR', 47.79292000, 1.06556000, '2019-10-05 22:49:08', '2020-05-01 17:22:44', 1, 'Q214575'),
(47579, 'Vendœuvres', 4818, 'CVL', 75, 'FR', 46.80000000, 1.35000000, '2019-10-05 22:49:08', '2020-05-01 17:22:44', 1, 'Q214575'),
(47580, 'Venelles', 4812, 'PAC', 75, 'FR', 43.59859000, 5.47977000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q652042'),
(47581, 'Venerque', 4799, 'OCC', 75, 'FR', 43.43457000, 1.44588000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47582, 'Venette', 4828, 'HDF', 75, 'FR', 49.41705000, 2.80317000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, '********'),
(47583, 'Veneux-les-Sablons', 4796, 'IDF', 75, 'FR', 48.37872000, 2.79499000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q1454790'),
(47584, 'Venizel', 4828, 'HDF', 75, 'FR', 49.36583000, 3.39321000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q1454790'),
(47585, 'Vennecy', 4818, 'CVL', 75, 'FR', 47.95465000, 2.05459000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q1454790'),
(47586, 'Venoy', 4825, 'BFC', 75, 'FR', 47.80518000, 3.63695000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q1454790'),
(47587, 'Ventabren', 4812, 'PAC', 75, 'FR', 43.53847000, 5.29541000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q843934'),
(47588, 'Ventiseri', 4806, '20R', 75, 'FR', 41.94356000, 9.33342000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q648636'),
(47589, 'Ver-sur-Launette', 4828, 'HDF', 75, 'FR', 49.10495000, 2.68409000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q751799'),
(47590, 'Ver-sur-Mer', 4804, 'NOR', 75, 'FR', 49.32987000, -0.53118000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q762471'),
(47591, 'Verberie', 4828, 'HDF', 75, 'FR', 49.31115000, 2.73210000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q815701'),
(47592, 'Vercel-Villedieu-le-Camp', 4825, 'BFC', 75, 'FR', 47.18318000, 6.40082000, '2019-10-05 22:49:08', '2019-10-05 22:49:08', 1, 'Q815701'),
(47593, 'Verdun', 4820, 'GES', 75, 'FR', 49.15964000, 5.38290000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q154748'),
(47594, 'Verdun-sur-Garonne', 4799, 'OCC', 75, 'FR', 43.85446000, 1.23425000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q1626652'),
(47595, 'Verdun-sur-le-Doubs', 4825, 'BFC', 75, 'FR', 46.89692000, 5.02127000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q1626652'),
(47596, 'Verduron', 4812, 'PAC', 75, 'FR', 43.37063000, 5.34513000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q1626652'),
(47597, 'Verfeil', 4799, 'OCC', 75, 'FR', 43.65781000, 1.66340000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q1626652'),
(47598, 'Vergigny', 4825, 'BFC', 75, 'FR', 47.97059000, 3.71861000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q1626652'),
(47599, 'Vergongheon', 4798, 'ARA', 75, 'FR', 45.37107000, 3.31981000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q1322968'),
(47600, 'Vergt', 4795, 'NAQ', 75, 'FR', 45.02695000, 0.71820000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q1011573'),
(47601, 'Vergèze', 4799, 'OCC', 75, 'FR', 43.74404000, 4.22109000, '2019-10-05 22:49:09', '2020-05-01 17:22:46', 1, 'Q1011573'),
(47602, 'Verlinghem', 4828, 'HDF', 75, 'FR', 50.68291000, 2.99907000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47603, 'Vermand', 4828, 'HDF', 75, 'FR', 49.87550000, 3.14959000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47604, 'Vermelles', 4828, 'HDF', 75, 'FR', 50.48949000, 2.74739000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q876024'),
(47605, 'Vermenton', 4825, 'BFC', 75, 'FR', 47.66459000, 3.73501000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q876024'),
(47606, 'Vern-d’Anjou', 4802, 'PDL', 75, 'FR', 47.60119000, -0.83357000, '2019-10-05 22:49:09', '2020-05-01 17:22:46', 1, 'Q876024'),
(47607, 'Vern-sur-Seiche', 4807, 'BRE', 75, 'FR', 48.04514000, -1.60057000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q637241'),
(47608, 'Vernaison', 4798, 'ARA', 75, 'FR', 45.64781000, 4.81140000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47609, 'Vernantes', 4802, 'PDL', 75, 'FR', 47.39320000, 0.05304000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47610, 'Vernet', 4799, 'OCC', 75, 'FR', 43.43333000, 1.41667000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47611, 'Vernet-les-Bains', 4799, 'OCC', 75, 'FR', 42.54834000, 2.38717000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q190011'),
(47612, 'Verneuil-en-Halatte', 4828, 'HDF', 75, 'FR', 49.27914000, 2.52410000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47613, 'Verneuil-sur-Avre', 4804, 'NOR', 75, 'FR', 48.73949000, 0.92731000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q317271'),
(47614, 'Verneuil-sur-Seine', 4796, 'IDF', 75, 'FR', 48.97388000, 1.96480000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q642326'),
(47615, 'Verneuil-sur-Vienne', 4795, 'NAQ', 75, 'FR', 45.85524000, 1.10133000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q194130'),
(47616, 'Verniolle', 4799, 'OCC', 75, 'FR', 43.08162000, 1.64904000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q194130'),
(47617, 'Vernioz', 4798, 'ARA', 75, 'FR', 45.42672000, 4.88267000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q194130'),
(47618, 'Vernoil-le-Fourrier', 4802, 'PDL', 75, 'FR', 47.38333000, 0.08333000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q845564'),
(47619, 'Vernon', 4804, 'NOR', 75, 'FR', 49.09292000, 1.46332000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q845564'),
(47620, 'Vernosc-lès-Annonay', 4798, 'ARA', 75, 'FR', 45.21596000, 4.71310000, '2019-10-05 22:49:09', '2020-05-01 17:22:43', 1, 'Q845564'),
(47621, 'Vernou-la-Celle-sur-Seine', 4796, 'IDF', 75, 'FR', 48.38789000, 2.84718000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q250068'),
(47622, 'Vernou-sur-Brenne', 4818, 'CVL', 75, 'FR', 47.41936000, 0.84757000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q950679'),
(47623, 'Vernouillet', 4796, 'IDF', 75, 'FR', 48.97146000, 1.98082000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q950679'),
(47624, 'Vernouillet', 4818, 'CVL', 75, 'FR', 48.72090000, 1.36951000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q950679'),
(47625, 'Vernoux-en-Vivarais', 4798, 'ARA', 75, 'FR', 44.89577000, 4.64524000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q950679'),
(47626, 'Verny', 4820, 'GES', 75, 'FR', 49.00677000, 6.20350000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q950679'),
(47627, 'Vernègues', 4812, 'PAC', 75, 'FR', 43.68575000, 5.17156000, '2019-10-05 22:49:09', '2020-05-01 17:22:47', 1, 'Q676398'),
(47628, 'Verquin', 4828, 'HDF', 75, 'FR', 50.50240000, 2.63888000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q676398'),
(47629, 'Verrières', 4820, 'GES', 75, 'FR', 48.23334000, 4.14893000, '2019-10-05 22:49:09', '2020-05-01 17:22:45', 1, 'Q676398'),
(47630, 'Verrières-le-Buisson', 4796, 'IDF', 75, 'FR', 48.74565000, 2.26796000, '2019-10-05 22:49:09', '2020-05-01 17:22:43', 1, 'Q747799'),
(47631, 'Vers-Pont-du-Gard', 4799, 'OCC', 75, 'FR', 43.96667000, 4.53333000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q379644'),
(47632, 'Versailles', 4796, 'IDF', 75, 'FR', 48.80359000, 2.13424000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q621'),
(47633, 'Verson', 4804, 'NOR', 75, 'FR', 49.15432000, -0.45628000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q862292'),
(47634, 'Versonnex', 4798, 'ARA', 75, 'FR', 45.92914000, 5.92586000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q862292'),
(47635, 'Vert-Saint-Denis', 4796, 'IDF', 75, 'FR', 48.56818000, 2.62007000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q899554'),
(47636, 'Vert-en-Drouais', 4818, 'CVL', 75, 'FR', 48.76029000, 1.29460000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q899554'),
(47637, 'Vert-le-Grand', 4796, 'IDF', 75, 'FR', 48.57172000, 2.35777000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q630021'),
(47638, 'Vert-le-Petit', 4796, 'IDF', 75, 'FR', 48.55163000, 2.36526000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q265657'),
(47639, 'Vertaizon', 4798, 'ARA', 75, 'FR', 45.76983000, 3.28650000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q265657'),
(47640, 'Vertheuil', 4795, 'NAQ', 75, 'FR', 45.25000000, -0.83333000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q265657'),
(47641, 'Verton', 4828, 'HDF', 75, 'FR', 50.40234000, 1.64766000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47642, 'Vertou', 4802, 'PDL', 75, 'FR', 47.16869000, -1.46929000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q328077'),
(47643, 'Vertus', 4820, 'GES', 75, 'FR', 48.90609000, 4.00216000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q501906'),
(47644, 'Vervins', 4828, 'HDF', 75, 'FR', 49.83510000, 3.90925000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q209840'),
(47645, 'Verzenay', 4820, 'GES', 75, 'FR', 49.15918000, 4.14543000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q535917'),
(47646, 'Verzy', 4820, 'GES', 75, 'FR', 49.14576000, 4.16409000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q535917'),
(47647, 'Vescovato', 4806, '20R', 75, 'FR', 42.49293000, 9.43934000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q535917'),
(47648, 'Vesoul', 4825, 'BFC', 75, 'FR', 47.62604000, 6.14251000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q203309'),
(47649, 'Vesseaux', 4798, 'ARA', 75, 'FR', 44.65152000, 4.44025000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q203309'),
(47650, 'Vestric-et-Candiac', 4799, 'OCC', 75, 'FR', 43.74061000, 4.25914000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q203309'),
(47651, 'Veurey-Voroize', 4798, 'ARA', 75, 'FR', 45.27268000, 5.61372000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q203309'),
(47652, 'Veynes', 4812, 'PAC', 75, 'FR', 44.53406000, 5.82321000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q470679'),
(47653, 'Veyrac', 4795, 'NAQ', 75, 'FR', 45.89521000, 1.10500000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q470679'),
(47654, 'Veyras', 4798, 'ARA', 75, 'FR', 44.73518000, 4.56254000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q470679'),
(47655, 'Veyre-Monton', 4798, 'ARA', 75, 'FR', 45.66866000, 3.17144000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q470679'),
(47656, 'Veyrier-du-Lac', 4798, 'ARA', 75, 'FR', 45.88234000, 6.17709000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q470679'),
(47657, 'Vezin-le-Coquet', 4807, 'BRE', 75, 'FR', 48.11857000, -1.75466000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q470679'),
(47658, 'Vezins', 4802, 'PDL', 75, 'FR', 47.12015000, -0.70971000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q470679'),
(47659, 'Vianne', 4795, 'NAQ', 75, 'FR', 44.19658000, 0.32104000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q224677'),
(47660, 'Viarmes', 4796, 'IDF', 75, 'FR', 49.13082000, 2.37074000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47661, 'Vias', 4799, 'OCC', 75, 'FR', 43.31156000, 3.41774000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47662, 'Vibraye', 4802, 'PDL', 75, 'FR', 48.05607000, 0.74171000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47663, 'Vic-Fezensac', 4799, 'OCC', 75, 'FR', 43.77183000, 0.31368000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q646551'),
(47664, 'Vic-en-Bigorre', 4799, 'OCC', 75, 'FR', 43.38682000, 0.05471000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47665, 'Vic-la-Gardiole', 4799, 'OCC', 75, 'FR', 43.49080000, 3.79750000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q669398'),
(47666, 'Vic-le-Comte', 4798, 'ARA', 75, 'FR', 45.64296000, 3.24607000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q41109'),
(47667, 'Vic-sur-Aisne', 4828, 'HDF', 75, 'FR', 49.40609000, 3.11223000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q990682'),
(47668, 'Vic-sur-Cère', 4798, 'ARA', 75, 'FR', 44.98011000, 2.62505000, '2019-10-05 22:49:09', '2020-05-01 17:22:43', 1, 'Q474476'),
(47669, 'Vic-sur-Seille', 4820, 'GES', 75, 'FR', 48.78195000, 6.53079000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q22351'),
(47670, 'Vichy', 4798, 'ARA', 75, 'FR', 46.12709000, 3.42577000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q93351'),
(47671, 'Vicq', 4828, 'HDF', 75, 'FR', 50.40738000, 3.60348000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q93351'),
(47672, 'Vicq-sur-Breuilh', 4795, 'NAQ', 75, 'FR', 45.64661000, 1.38179000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q93351'),
(47673, 'Vidauban', 4812, 'PAC', 75, 'FR', 43.42721000, 6.43185000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q180000'),
(47674, 'Vieille Chapelle', 4812, 'PAC', 75, 'FR', 43.24963000, 5.38048000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q180000'),
(47675, 'Vieille-Brioude', 4798, 'ARA', 75, 'FR', 45.26470000, 3.40479000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q180000'),
(47676, 'Vieille-Toulouse', 4799, 'OCC', 75, 'FR', 43.52366000, 1.44230000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q180000'),
(47677, 'Vieille-Église', 4828, 'HDF', 75, 'FR', 50.92823000, 2.07671000, '2019-10-05 22:49:09', '2020-05-01 17:22:45', 1, 'Q180000'),
(47678, 'Vieillevigne', 4802, 'PDL', 75, 'FR', 46.97214000, -1.43405000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q180000'),
(47679, 'Vielle-Saint-Girons', 4795, 'NAQ', 75, 'FR', 43.95000000, -1.30000000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q180000'),
(47680, 'Vielmur-sur-Agout', 4799, 'OCC', 75, 'FR', 43.61667000, 2.10000000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47681, 'Viennay', 4795, 'NAQ', 75, 'FR', 46.68711000, -0.24641000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47682, 'Vienne', 4798, 'ARA', 75, 'FR', 45.52569000, 4.87484000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q26849'),
(47683, 'Vienne', 4795, 'NAQ', 75, 'FR', 46.53528000, 0.45201000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q12804'),
(47684, 'Vienne-en-Val', 4818, 'CVL', 75, 'FR', 47.80036000, 2.13460000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, '********'),
(47685, 'Vierzon', 4818, 'CVL', 75, 'FR', 47.22186000, 2.06840000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q208538'),
(47686, 'Viesly', 4828, 'HDF', 75, 'FR', 50.15345000, 3.46236000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q208538'),
(47687, 'Vieux-Berquin', 4828, 'HDF', 75, 'FR', 50.69489000, 2.64444000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q208538'),
(47688, 'Vieux-Boucau-les-Bains', 4795, 'NAQ', 75, 'FR', 43.78947000, -1.39957000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q208538'),
(47689, 'Vieux-Charmont', 4825, 'BFC', 75, 'FR', 47.52190000, 6.83738000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q208538'),
(47690, 'Vieux-Condé', 4828, 'HDF', 75, 'FR', 50.45944000, 3.56738000, '2019-10-05 22:49:09', '2020-05-01 17:22:45', 1, 'Q653467'),
(47691, 'Vieux-Thann', 4820, 'GES', 75, 'FR', 47.80400000, 7.12067000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q653467'),
(47692, 'Vif', 4798, 'ARA', 75, 'FR', 45.05654000, 5.67204000, '2019-10-05 22:49:09', '2019-10-05 22:49:09', 1, 'Q653467'),
(47693, 'Vigeois', 4795, 'NAQ', 75, 'FR', 45.37934000, 1.51731000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q653467'),
(47694, 'Vignacourt', 4828, 'HDF', 75, 'FR', 50.01236000, 2.19743000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q653467'),
(47695, 'Vigneulles-lès-Hattonchâtel', 4820, 'GES', 75, 'FR', 48.98152000, 5.70464000, '2019-10-05 22:49:10', '2020-05-01 17:22:45', 1, 'Q653467'),
(47696, 'Vigneux-de-Bretagne', 4802, 'PDL', 75, 'FR', 47.32547000, -1.73678000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q680923'),
(47697, 'Vigneux-sur-Seine', 4796, 'IDF', 75, 'FR', 48.70291000, 2.41357000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q842037'),
(47698, 'Vignoc', 4807, 'BRE', 75, 'FR', 48.24842000, -1.78169000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q842037'),
(47699, 'Vignot', 4820, 'GES', 75, 'FR', 48.77418000, 5.60904000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q842037'),
(47700, 'Vignoux-sur-Barangeon', 4818, 'CVL', 75, 'FR', 47.20068000, 2.17258000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q842037'),
(47701, 'Vigny', 4796, 'IDF', 75, 'FR', 49.07902000, 1.92806000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q842037'),
(47702, 'Vigy', 4820, 'GES', 75, 'FR', 49.20443000, 6.29906000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q22397'),
(47703, 'Vihiers', 4802, 'PDL', 75, 'FR', 47.14631000, -0.53238000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q749015'),
(47704, 'Vilallonga dels Monts', 4799, 'OCC', 75, 'FR', 42.52557000, 2.90434000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q749015'),
(47705, 'Villabé', 4796, 'IDF', 75, 'FR', 48.58949000, 2.45096000, '2019-10-05 22:49:10', '2020-05-01 17:22:43', 1, 'Q276592'),
(47706, 'Village-Neuf', 4820, 'GES', 75, 'FR', 47.60682000, 7.56964000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q122332'),
(47707, 'Villaines-la-Juhel', 4802, 'PDL', 75, 'FR', 48.34416000, -0.27734000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q753789'),
(47708, 'Villandry', 4818, 'CVL', 75, 'FR', 47.34019000, 0.51050000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, '********'),
(47709, 'Villar-Saint-Pancrace', 4812, 'PAC', 75, 'FR', 44.87318000, 6.62669000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q542509'),
(47710, 'Villard-Bonnot', 4798, 'ARA', 75, 'FR', 45.23460000, 5.88323000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q976866'),
(47711, 'Villard-de-Lans', 4798, 'ARA', 75, 'FR', 45.07156000, 5.55637000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q696499'),
(47712, 'Villargondran', 4798, 'ARA', 75, 'FR', 45.26427000, 6.37668000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q696499'),
(47713, 'Villars', 4798, 'ARA', 75, 'FR', 45.46785000, 4.35539000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q696499'),
(47714, 'Villars-les-Dombes', 4798, 'ARA', 75, 'FR', 46.00208000, 5.03248000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q696499'),
(47715, 'Villaudric', 4799, 'OCC', 75, 'FR', 43.83044000, 1.43166000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q696499'),
(47716, 'Villaz', 4798, 'ARA', 75, 'FR', 45.95127000, 6.19447000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q743454'),
(47717, 'Ville-d’Avray', 4796, 'IDF', 75, 'FR', 48.82358000, 2.19311000, '2019-10-05 22:49:10', '2020-05-01 17:22:43', 1, 'Q456327'),
(47718, 'Ville-la-Grand', 4798, 'ARA', 75, 'FR', 46.20300000, 6.25010000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q829285'),
(47719, 'Ville-sous-Anjou', 4798, 'ARA', 75, 'FR', 45.37274000, 4.85081000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q523513'),
(47720, 'Ville-sous-la-Ferté', 4820, 'GES', 75, 'FR', 48.12060000, 4.78957000, '2019-10-05 22:49:10', '2020-05-01 17:22:45', 1, 'Q523513'),
(47721, 'Villebarou', 4818, 'CVL', 75, 'FR', 47.62344000, 1.32252000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q523513'),
(47722, 'Villebernier', 4802, 'PDL', 75, 'FR', 47.25374000, -0.03229000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, '********'),
(47723, 'Villeblevin', 4825, 'BFC', 75, 'FR', 48.32446000, 3.08038000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, '********'),
(47724, 'Villebois', 4798, 'ARA', 75, 'FR', 45.84822000, 5.43310000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, '********'),
(47725, 'Villebon-sur-Yvette', 4796, 'IDF', 75, 'FR', 48.70594000, 2.24019000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q828625'),
(47726, 'Villebret', 4798, 'ARA', 75, 'FR', 46.26683000, 2.63862000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q828625'),
(47727, 'Villecresnes', 4796, 'IDF', 75, 'FR', 48.72002000, 2.53940000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q751658'),
(47728, 'Villecroze', 4812, 'PAC', 75, 'FR', 43.58223000, 6.27587000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q818173'),
(47729, 'Villedieu-la-Blouère', 4802, 'PDL', 75, 'FR', 47.14738000, -1.06286000, '2019-10-05 22:49:10', '2020-05-01 17:22:46', 1, 'Q818173'),
(47730, 'Villedieu-les-Poêles', 4804, 'NOR', 75, 'FR', 48.83333000, -1.21667000, '2019-10-05 22:49:10', '2020-05-01 17:22:45', 1, 'Q629673'),
(47731, 'Villedieu-sur-Indre', 4818, 'CVL', 75, 'FR', 46.84598000, 1.53975000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q545756'),
(47732, 'Villedômer', 4818, 'CVL', 75, 'FR', 47.54651000, 0.88775000, '2019-10-05 22:49:10', '2020-05-01 17:22:44', 1, 'Q545756'),
(47733, 'Villefagnan', 4795, 'NAQ', 75, 'FR', 46.01140000, 0.07936000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, '********'),
(47734, 'Villefontaine', 4798, 'ARA', 75, 'FR', 45.61278000, 5.15058000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, '********'),
(47735, 'Villefranche-d\'Albigeois', 4799, 'OCC', 75, 'FR', 43.89635000, 2.33022000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, '********'),
(47736, 'Villefranche-de-Lauragais', 4799, 'OCC', 75, 'FR', 43.40000000, 1.71694000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q671059'),
(47737, 'Villefranche-de-Rouergue', 4799, 'OCC', 75, 'FR', 44.35166000, 2.03702000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q213501'),
(47738, 'Villefranche-d’Allier', 4798, 'ARA', 75, 'FR', 46.39652000, 2.85717000, '2019-10-05 22:49:10', '2020-05-01 17:22:43', 1, 'Q213501'),
(47739, 'Villefranche-sur-Cher', 4818, 'CVL', 75, 'FR', 47.30000000, 1.76667000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q732703'),
(47740, 'Villefranche-sur-Mer', 4812, 'PAC', 75, 'FR', 43.70392000, 7.31088000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q207080'),
(47741, 'Villefranche-sur-Saône', 4798, 'ARA', 75, 'FR', 45.98967000, 4.71961000, '2019-10-05 22:49:10', '2020-05-01 17:22:43', 1, 'Q208770'),
(47742, 'Villefranque', 4795, 'NAQ', 75, 'FR', 43.43631000, -1.45324000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q208770'),
(47743, 'Villegailhenc', 4799, 'OCC', 75, 'FR', 43.26867000, 2.35469000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q208770'),
(47744, 'Villegouge', 4795, 'NAQ', 75, 'FR', 44.96667000, -0.30000000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q1005109'),
(47745, 'Villejuif', 4796, 'IDF', 75, 'FR', 48.79390000, 2.35992000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q128466'),
(47746, 'Villejust', 4796, 'IDF', 75, 'FR', 48.68304000, 2.23610000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q276220'),
(47747, 'Villelaure', 4812, 'PAC', 75, 'FR', 43.71075000, 5.43422000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q607254'),
(47748, 'Villelongue-de-la-Salanque', 4799, 'OCC', 75, 'FR', 42.72637000, 2.98240000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q607254'),
(47749, 'Villemandeur', 4818, 'CVL', 75, 'FR', 47.98701000, 2.71802000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q607254'),
(47750, 'Villemeux-sur-Eure', 4818, 'CVL', 75, 'FR', 48.67259000, 1.46470000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q607254'),
(47751, 'Villemoirieu', 4798, 'ARA', 75, 'FR', 45.71884000, 5.22586000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q607254'),
(47752, 'Villemoisson-sur-Orge', 4796, 'IDF', 75, 'FR', 48.66632000, 2.33657000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q1428227'),
(47753, 'Villemolaque', 4799, 'OCC', 75, 'FR', 42.58815000, 2.83890000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q1428227'),
(47754, 'Villemomble', 4796, 'IDF', 75, 'FR', 48.88333000, 2.50000000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q244716'),
(47755, 'Villemoustaussou', 4799, 'OCC', 75, 'FR', 43.25186000, 2.36552000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q244716'),
(47756, 'Villemur-sur-Tarn', 4799, 'OCC', 75, 'FR', 43.86708000, 1.50281000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q1371216'),
(47757, 'Villenauxe-la-Grande', 4820, 'GES', 75, 'FR', 48.58333000, 3.55000000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q1371207'),
(47758, 'Villenave-d’Ornon', 4795, 'NAQ', 75, 'FR', 44.77327000, -0.54420000, '2019-10-05 22:49:10', '2020-05-01 17:22:46', 1, 'Q235733'),
(47759, 'Villeneuve', 4798, 'ARA', 75, 'FR', 46.02096000, 4.83591000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q235733'),
(47760, 'Villeneuve', 4812, 'PAC', 75, 'FR', 44.94488000, 6.56545000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q523824'),
(47761, 'Villeneuve', 4799, 'OCC', 75, 'FR', 44.43333000, 2.03333000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q523824'),
(47762, 'Villeneuve-Loubet', 4812, 'PAC', 75, 'FR', 43.65790000, 7.12233000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q320197'),
(47763, 'Villeneuve-Saint-Georges', 4796, 'IDF', 75, 'FR', 48.73219000, 2.44925000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q244241'),
(47764, 'Villeneuve-Saint-Germain', 4828, 'HDF', 75, 'FR', 49.37976000, 3.35952000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q244241'),
(47765, 'Villeneuve-Tolosane', 4799, 'OCC', 75, 'FR', 43.52316000, 1.34102000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47766, 'Villeneuve-d\'Ascq', 4828, 'HDF', 75, 'FR', 50.61669000, 3.16664000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47767, 'Villeneuve-de-Berg', 4798, 'ARA', 75, 'FR', 44.55699000, 4.50215000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47768, 'Villeneuve-de-Marsan', 4795, 'NAQ', 75, 'FR', 43.88906000, -0.30926000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47769, 'Villeneuve-de-Rivière', 4799, 'OCC', 75, 'FR', 43.12829000, 0.66351000, '2019-10-05 22:49:10', '2020-05-01 17:22:46', 1, 'Q835536'),
(47770, 'Villeneuve-de-la-Raho', 4799, 'OCC', 75, 'FR', 42.63596000, 2.91651000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47771, 'Villeneuve-d’Olmes', 4799, 'OCC', 75, 'FR', 42.90610000, 1.81937000, '2019-10-05 22:49:10', '2020-05-01 17:22:46', 1, 'Q835536'),
(47772, 'Villeneuve-la-Comptal', 4799, 'OCC', 75, 'FR', 43.28833000, 1.91773000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47773, 'Villeneuve-la-Garenne', 4796, 'IDF', 75, 'FR', 48.93935000, 2.31478000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47774, 'Villeneuve-la-Guyard', 4825, 'BFC', 75, 'FR', 48.34093000, 3.06176000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47775, 'Villeneuve-la-Rivière', 4799, 'OCC', 75, 'FR', 42.69366000, 2.80294000, '2019-10-05 22:49:10', '2020-05-01 17:22:46', 1, 'Q835536'),
(47776, 'Villeneuve-le-Comte', 4796, 'IDF', 75, 'FR', 48.81413000, 2.82953000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q835536'),
(47777, 'Villeneuve-le-Roi', 4796, 'IDF', 75, 'FR', 48.73684000, 2.40081000, '2019-10-05 22:49:10', '2019-10-05 22:49:10', 1, 'Q669715'),
(47778, 'Villeneuve-les-Sablons', 4828, 'HDF', 75, 'FR', 49.23753000, 2.07782000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q669715'),
(47779, 'Villeneuve-lès-Avignon', 4799, 'OCC', 75, 'FR', 43.96840000, 4.79630000, '2019-10-05 22:49:11', '2020-05-01 17:22:46', 1, 'Q269113'),
(47780, 'Villeneuve-lès-Bouloc', 4799, 'OCC', 75, 'FR', 43.76769000, 1.42278000, '2019-10-05 22:49:11', '2020-05-01 17:22:46', 1, 'Q269113'),
(47781, 'Villeneuve-lès-Béziers', 4799, 'OCC', 75, 'FR', 43.31517000, 3.28059000, '2019-10-05 22:49:11', '2020-05-01 17:22:46', 1, 'Q269113'),
(47782, 'Villeneuve-lès-Maguelone', 4799, 'OCC', 75, 'FR', 43.53333000, 3.86667000, '2019-10-05 22:49:11', '2020-05-01 17:22:46', 1, 'Q681620'),
(47783, 'Villeneuve-sur-Bellot', 4796, 'IDF', 75, 'FR', 48.86203000, 3.34143000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q681620'),
(47784, 'Villeneuve-sur-Lot', 4795, 'NAQ', 75, 'FR', 44.40854000, 0.70415000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q205638'),
(47785, 'Villeneuve-sur-Yonne', 4825, 'BFC', 75, 'FR', 48.08247000, 3.29688000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q898114'),
(47786, 'Villennes-sur-Seine', 4796, 'IDF', 75, 'FR', 48.94137000, 1.99137000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491908'),
(47787, 'Villenouvelle', 4799, 'OCC', 75, 'FR', 43.43581000, 1.66279000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491908'),
(47788, 'Villenoy', 4796, 'IDF', 75, 'FR', 48.94112000, 2.86020000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491908'),
(47789, 'Villeparisis', 4796, 'IDF', 75, 'FR', 48.94208000, 2.61463000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491908'),
(47790, 'Villepinte', 4796, 'IDF', 75, 'FR', 48.96203000, 2.53253000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491908'),
(47791, 'Villepinte', 4799, 'OCC', 75, 'FR', 43.28213000, 2.08760000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491908'),
(47792, 'Villepreux', 4796, 'IDF', 75, 'FR', 48.82815000, 1.99760000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491492'),
(47793, 'Villerest', 4798, 'ARA', 75, 'FR', 45.99539000, 4.03463000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491492'),
(47794, 'Villereversure', 4798, 'ARA', 75, 'FR', 46.18521000, 5.38262000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491492'),
(47795, 'Villers-Bocage', 4828, 'HDF', 75, 'FR', 49.99810000, 2.31683000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491492'),
(47796, 'Villers-Bocage', 4804, 'NOR', 75, 'FR', 49.07960000, -0.65412000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q491492'),
(47797, 'Villers-Bretonneux', 4828, 'HDF', 75, 'FR', 49.86844000, 2.51688000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q677163'),
(47798, 'Villers-Cotterêts', 4828, 'HDF', 75, 'FR', 49.25311000, 3.09003000, '2019-10-05 22:49:11', '2020-05-01 17:22:45', 1, 'Q213883'),
(47799, 'Villers-Outréaux', 4828, 'HDF', 75, 'FR', 50.03590000, 3.29947000, '2019-10-05 22:49:11', '2020-05-01 17:22:45', 1, 'Q696526'),
(47800, 'Villers-Pol', 4828, 'HDF', 75, 'FR', 50.28460000, 3.61449000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1101376'),
(47801, 'Villers-Saint-Paul', 4828, 'HDF', 75, 'FR', 49.28885000, 2.48968000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1389265'),
(47802, 'Villers-Semeuse', 4820, 'GES', 75, 'FR', 49.74201000, 4.74697000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1389265');

