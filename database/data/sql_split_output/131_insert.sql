INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(65959, 'Annus', 3759, '07', 186, 'LC', 13.77449000, -61.00511000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q26569'),
(65960, '<PERSON><PERSON>', 3756, '05', 186, 'LC', 13.90871000, -60.90440000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q26569'),
(65961, '<PERSON><PERSON>', 3764, '10', 186, 'LC', 13.86426000, -61.07016000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q26569'),
(65962, '<PERSON><PERSON>', 3761, '12', 186, '<PERSON><PERSON>', 13.92261000, -61.05250000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q26569'),
(65963, 'Anse <PERSON>t', 3761, '12', 186, 'LC', 13.93221000, -61.04290000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q26569'),
(65964, 'Anse Ger', 3762, '08', 186, 'LC', 13.79567000, -60.91567000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q26569'),
(65965, 'Anse La Raye', 3757, '01', 186, 'LC', 13.94619000, -61.03879000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65966, 'Anse La Verdue', 3761, '12', 186, 'LC', 13.90964000, -61.04755000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65967, 'Athens', 3756, '05', 186, 'LC', 13.90574000, -60.89184000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65968, 'Au Leon', 3756, '05', 186, 'LC', 13.95245000, -60.90456000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65969, 'Au Tabor', 3757, '01', 186, 'LC', 13.94540000, -61.04112000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65970, 'Au Tabor Hill', 3757, '01', 186, 'LC', 13.94457000, -61.03559000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65971, 'Augier', 3763, '11', 186, 'LC', 13.76669000, -60.98063000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65972, 'Aurendel Hill', 3758, '02', 186, 'LC', 14.00069000, -60.98670000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65973, 'Babonneau Proper', 3758, '02', 186, 'LC', 14.00535000, -60.94640000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65974, 'Bagatelle', 3758, '02', 186, 'LC', 13.99797000, -60.98156000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65975, 'Balata', 3758, '02', 186, 'LC', 14.01471000, -60.95167000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65976, 'Balca', 3759, '07', 186, 'LC', 13.77117000, -61.02389000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65977, 'Balca/En Leur Ba', 3759, '07', 186, 'LC', 13.77638000, -61.01931000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65978, 'Balembouche', 3759, '07', 186, 'LC', 13.75992000, -61.02840000, '2019-10-05 23:07:51', '2019-10-05 23:07:51', 1, 'Q569439'),
(65979, 'Banannes Bay', 3758, '02', 186, 'LC', 14.01087000, -61.00135000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65980, 'Banse', 3759, '07', 186, 'LC', 13.79211000, -60.99282000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65981, 'Banse La Grace', 3759, '07', 186, 'LC', 13.78150000, -60.99840000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65982, 'Bara Bara', 3756, '05', 186, 'LC', 13.95911000, -60.93414000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65983, 'Barnard Hill', 3758, '02', 186, 'LC', 14.01423000, -60.98924000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65984, 'Barons Drive/Coin De L\'Anse', 3764, '10', 186, 'LC', 13.85138000, -61.05939000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65985, 'Barre Denis', 3758, '02', 186, 'LC', 13.96809000, -60.99073000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65986, 'Barre Duchaussee', 3758, '02', 186, 'LC', 13.96190000, -60.99678000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65987, 'Barre St.Joseph', 3758, '02', 186, 'LC', 13.97247000, -61.01829000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65988, 'Bazile', 3756, '05', 186, 'LC', 13.91082000, -60.92430000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65989, 'Beane Field', 3763, '11', 186, 'LC', 13.73976000, -60.94741000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65990, 'Beasejour/Myers Bridge', 3764, '10', 186, 'LC', 13.82147000, -61.03396000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65991, 'Beauchamp', 3762, '08', 186, 'LC', 13.81778000, -60.91528000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65992, 'Beausejour', 3763, '11', 186, 'LC', 13.75229000, -60.95519000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65993, 'Beausejour', 3766, '06', 186, 'LC', 14.07647000, -60.93780000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65994, 'Beausejour/Fostin\'S Development', 3766, '06', 186, 'LC', 14.07445000, -60.92929000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65995, 'Beausejour/Ndc', 3766, '06', 186, 'LC', 14.07733000, -60.92683000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65996, 'Belair', 3758, '02', 186, 'LC', 13.95283000, -60.99747000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65997, 'Bella Rosa', 3766, '06', 186, 'LC', 14.07711000, -60.94568000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65998, 'Bella Rosa', 3758, '02', 186, 'LC', 14.00777000, -60.99628000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(65999, 'Belle Plaine', 3764, '10', 186, 'LC', 13.82700000, -61.03127000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66000, 'Belle Vue', 3760, '03', 186, 'LC', 13.80510000, -61.03615000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66001, 'Belle Vue', 3766, '06', 186, 'LC', 14.08470000, -60.94342000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66002, 'Belle Vue', 3763, '11', 186, 'LC', 13.79218000, -60.95379000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66003, 'Belle Vue Estate', 3766, '06', 186, 'LC', 14.08840000, -60.94613000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66004, 'Bellefond', 3764, '10', 186, 'LC', 13.82753000, -61.04269000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66005, 'Belmont', 3756, '05', 186, 'LC', 13.95044000, -60.93276000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66006, 'Belvedere', 3761, '12', 186, 'LC', 13.89122000, -61.05550000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66007, 'Belvedere', 3764, '10', 186, 'LC', 13.83440000, -61.01771000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66008, 'Bexon', 3758, '02', 186, 'LC', 13.96077000, -60.97497000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66009, 'Bisee', 3758, '02', 186, 'LC', 14.02429000, -60.97445000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66010, 'Bishop\'S Gap', 3758, '02', 186, 'LC', 14.00019000, -60.98498000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66011, 'Bissee', 3758, '02', 186, 'LC', 14.02255000, -60.97489000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66012, 'Black Bay', 3763, '11', 186, 'LC', 13.74330000, -60.98272000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66013, 'Black Mallet', 3758, '02', 186, 'LC', 14.00457000, -60.98811000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66014, 'Blanchard', 3762, '08', 186, 'LC', 13.80616000, -60.94630000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66015, 'Bocage', 3758, '02', 186, 'LC', 14.00199000, -60.96954000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66016, 'Boguis', 3766, '06', 186, 'LC', 14.01548000, -60.92089000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66017, 'Boguis/Desa Blond', 3766, '06', 186, 'LC', 14.01057000, -60.92565000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66018, 'Bois Catchet', 3758, '02', 186, 'LC', 14.00399000, -60.99437000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66019, 'Bois D\'Inde', 3757, '01', 186, 'LC', 13.94148000, -61.00966000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66020, 'Bois D\'Inde', 3764, '10', 186, 'LC', 13.83650000, -61.03491000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66021, 'Bois D\'Orange', 3766, '06', 186, 'LC', 14.05803000, -60.95992000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66022, 'Bois D\'Ornange/Trouya', 3766, '06', 186, 'LC', 14.06198000, -60.96914000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66023, 'Bois Dinde', 3760, '03', 186, 'LC', 13.80463000, -61.04952000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66024, 'Bois Joli', 3756, '05', 186, 'LC', 13.92020000, -60.91409000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66025, 'Bois Patat', 3758, '02', 186, 'LC', 14.00892000, -60.98214000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66026, 'Bongalo', 3759, '07', 186, 'LC', 13.76666000, -61.02991000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66027, 'Bonneterre', 3766, '06', 186, 'LC', 14.06994000, -60.94146000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66028, 'Bonneterre Gardens', 3766, '06', 186, 'LC', 14.06737000, -60.93393000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66029, 'Bordelais', 3756, '05', 186, 'LC', 13.89552000, -60.90092000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66030, 'Bosquet D\'Or', 3756, '05', 186, 'LC', 13.92886000, -60.91127000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66031, 'Bouton', 3764, '10', 186, 'LC', 13.87936000, -61.06449000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66032, 'Bruceville/Shanty Town', 3763, '11', 186, 'LC', 13.72558000, -60.94907000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66033, 'Cabiche/Babonneau', 3758, '02', 186, 'LC', 14.00583000, -60.95432000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66034, 'Cacoa', 3763, '11', 186, 'LC', 13.77889000, -60.93862000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66035, 'Cacoa/Babonneau', 3758, '02', 186, 'LC', 13.98875000, -60.95113000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66036, 'Caffiere', 3760, '03', 186, 'LC', 13.78409000, -61.03554000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66037, 'Caico/Millet', 3757, '01', 186, 'LC', 13.91948000, -60.99264000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66038, 'Calvary', 3758, '02', 186, 'LC', 14.01391000, -60.98664000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66039, 'Calvary/Calvaire', 3764, '10', 186, 'LC', 13.85035000, -61.05620000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q569439'),
(66040, 'Canaries', 3757, '01', 186, 'LC', 13.90224000, -61.06459000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66041, 'Canelles', 3762, '08', 186, 'LC', 13.78515000, -60.91789000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66042, 'Cantonement', 3763, '11', 186, 'LC', 13.75665000, -60.97662000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66043, 'Cap Estate', 3766, '06', 186, 'LC', 14.09410000, -60.93592000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66044, 'Cap Estate/Becune Point', 3766, '06', 186, 'LC', 14.09270000, -60.95161000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66045, 'Cap Estate/Caribbean Park', 3766, '06', 186, 'LC', 14.10019000, -60.92446000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66046, 'Cap Estate/Golf Park', 3766, '06', 186, 'LC', 14.09998000, -60.93705000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66047, 'Cap Estate/Lower Saline Point', 3766, '06', 186, 'LC', 14.10515000, -60.94310000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66048, 'Cap Estate/Mon Du Cap', 3766, '06', 186, 'LC', 14.10554000, -60.93758000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66049, 'Cap Estate/Ranch Site', 3766, '06', 186, 'LC', 14.10437000, -60.93360000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66050, 'Cap Estate/Saddlec Back', 3766, '06', 186, 'LC', 14.10023000, -60.94246000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66051, 'Cap Estate/Upper Saline Point', 3766, '06', 186, 'LC', 14.10740000, -60.94487000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66052, 'Capital Hill', 3758, '02', 186, 'LC', 13.99307000, -60.99097000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66053, 'Carellie', 3758, '02', 186, 'LC', 14.01889000, -60.96851000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66054, 'Carierre', 3763, '11', 186, 'LC', 13.78514000, -60.96721000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66055, 'Cas En Bas', 3766, '06', 186, 'LC', 14.08328000, -60.93209000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66056, 'Castries', 3758, '02', 186, 'LC', 13.99570000, -61.00614000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66057, 'Catin', 3763, '11', 186, 'LC', 13.77694000, -60.97973000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66058, 'Caye Manje\'', 3766, '06', 186, 'LC', 14.06629000, -60.93560000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66059, 'Cazuca', 3762, '08', 186, 'LC', 13.80002000, -60.90481000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66060, 'Cedars', 3758, '02', 186, 'LC', 14.00601000, -60.98232000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66061, 'Cedars/Chu Tigre', 3760, '03', 186, 'LC', 13.76995000, -61.04716000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66062, 'Cenac', 3764, '10', 186, 'LC', 13.87207000, -61.04243000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66063, 'Chalon', 3761, '12', 186, 'LC', 13.92018000, -61.04412000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66064, 'Champen Estate', 3757, '01', 186, 'LC', 13.93575000, -61.02991000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66065, 'Chase Gardens', 3758, '02', 186, 'LC', 14.01481000, -60.97395000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66066, 'Chassin/Babonneau', 3758, '02', 186, 'LC', 13.99100000, -60.92187000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66067, 'Chateau Belair', 3764, '10', 186, 'LC', 13.81830000, -61.05350000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66068, 'Chique/Blanchard', 3762, '08', 186, 'LC', 13.80120000, -60.94970000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, 'Q41699'),
(66069, 'Choiseul', 3760, '03', 186, 'LC', 13.77273000, -61.04931000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66070, 'Choppin Ridge/Sarot', 3758, '02', 186, 'LC', 13.95597000, -60.98624000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66071, 'Christian Hill', 3760, '03', 186, 'LC', 13.77354000, -61.04694000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66072, 'Ciceron', 3758, '02', 186, 'LC', 13.99296000, -61.00878000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66073, 'City', 3758, '02', 186, 'LC', 14.00946000, -60.99027000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66074, 'City Gate/La Clery', 3758, '02', 186, 'LC', 14.02124000, -60.98149000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66075, 'Cocoa Dan', 3763, '11', 186, 'LC', 13.73087000, -60.97195000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66076, 'Colombette', 3764, '10', 186, 'LC', 13.86861000, -61.04990000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66077, 'Compar', 3764, '10', 186, 'LC', 13.83586000, -61.05934000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66078, 'Conway/Waterfront', 3758, '02', 186, 'LC', 14.01389000, -60.99055000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66079, 'Coolie Town', 3762, '08', 186, 'LC', 13.85429000, -60.91380000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66080, 'Coolie Town', 3758, '02', 186, 'LC', 13.95684000, -61.01437000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66081, 'Coolie Town', 3763, '11', 186, 'LC', 13.76911000, -60.96680000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66082, 'Corinth', 3766, '06', 186, 'LC', 14.04710000, -60.96046000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66083, 'Corinth Estate', 3766, '06', 186, 'LC', 14.04336000, -60.95388000, '2019-10-05 23:07:52', '2019-10-05 23:07:52', 1, '********'),
(66084, 'Corinth/La Bel Lair', 3766, '06', 186, 'LC', 14.04469000, -60.94484000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66085, 'Coubaril', 3758, '02', 186, 'LC', 14.00223000, -61.00919000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66086, 'Cresslands', 3764, '10', 186, 'LC', 13.85999000, -61.04032000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66087, 'Crownlands/Marc', 3758, '02', 186, 'LC', 13.95174000, -60.97150000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66088, 'Cul De Sac', 3758, '02', 186, 'LC', 13.98084000, -61.00645000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66089, 'Daban', 3759, '07', 186, 'LC', 13.80490000, -61.00435000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66090, 'Dacretin', 3760, '03', 186, 'LC', 13.79617000, -61.03574000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66091, 'Darling Road', 3758, '02', 186, 'LC', 14.01355000, -60.98779000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66092, 'Dauphin', 3766, '06', 186, 'LC', 14.05647000, -60.90188000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66093, 'De Mailly', 3763, '11', 186, 'LC', 13.79649000, -60.94938000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66094, 'Debreuil', 3760, '03', 186, 'LC', 13.79033000, -61.02585000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66095, 'Deglos', 3758, '02', 186, 'LC', 13.97727000, -60.97329000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66096, 'Delaide', 3756, '05', 186, 'LC', 13.92993000, -60.90584000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66097, 'Delcer', 3760, '03', 186, 'LC', 13.80121000, -61.05892000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66098, 'Delomel', 3762, '08', 186, 'LC', 13.80338000, -60.92927000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66099, 'Dennery', 3756, '05', 186, 'LC', 13.91409000, -60.89132000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66100, 'Dennery By Pass', 3756, '05', 186, 'LC', 13.91164000, -60.89454000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66101, 'Dennery By Pass/Green Mountain', 3756, '05', 186, 'LC', 13.91094000, -60.89810000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66102, 'Dennery By Pass/Rocky Lane', 3756, '05', 186, 'LC', 13.91443000, -60.90017000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66103, 'Dennery By Pass/White Rock Gardens', 3756, '05', 186, 'LC', 13.91424000, -60.89333000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66104, 'Dennery Village', 3756, '05', 186, 'LC', 13.91099000, -60.89122000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66105, 'Derierre Bois', 3763, '11', 186, 'LC', 13.76807000, -60.97725000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66106, 'Derierre Fort/Old Victoria Road', 3758, '02', 186, 'LC', 13.99474000, -60.98513000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66107, 'Derierre Morne', 3763, '11', 186, 'LC', 13.74126000, -60.96057000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66108, 'Derniere Riviere', 3756, '05', 186, 'LC', 13.95679000, -60.92578000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66109, 'Derniere Riviere/Fond Maricient', 3756, '05', 186, 'LC', 13.95130000, -60.92218000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66110, 'Derniere Riviere/Mardi Gras/Morne Caca Cochon', 3756, '05', 186, 'LC', 13.96288000, -60.92029000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66111, 'Derniere Riviere/Morne Panache', 3756, '05', 186, 'LC', 13.95252000, -60.92919000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66112, 'Derriere Lagoon', 3757, '01', 186, 'LC', 13.94168000, -61.01891000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66113, 'Derriere Morne', 3760, '03', 186, 'LC', 13.80916000, -61.05101000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66114, 'Des Barras', 3766, '06', 186, 'LC', 13.99949000, -60.90166000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66115, 'Des Barras/Cacolie', 3766, '06', 186, 'LC', 14.01687000, -60.90097000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66116, 'Des Blanchard', 3762, '08', 186, 'LC', 13.81155000, -60.93423000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66117, 'Despinoze', 3756, '05', 186, 'LC', 13.94971000, -60.91182000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66118, 'Desrameaux', 3766, '06', 186, 'LC', 14.03681000, -60.92697000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66119, 'Desruisseaux', 3762, '08', 186, 'LC', 13.79640000, -60.93540000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66120, 'Deville', 3764, '10', 186, 'LC', 13.81372000, -61.05049000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66121, 'Diamond/Diamond Estate', 3764, '10', 186, 'LC', 13.85168000, -61.04352000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66122, 'Docamel/La Resource', 3763, '11', 186, 'LC', 13.74202000, -60.95604000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66123, 'Dubonnaire', 3756, '05', 186, 'LC', 13.92808000, -60.92336000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66124, 'Dubrassay', 3758, '02', 186, 'LC', 13.98382000, -60.97154000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66125, 'Dugard', 3760, '03', 186, 'LC', 13.80386000, -61.02831000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66126, 'Dugard', 3762, '08', 186, 'LC', 13.81090000, -60.91415000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66127, 'Dupre', 3760, '03', 186, 'LC', 13.79435000, -61.02947000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66128, 'Durandeau', 3757, '01', 186, 'LC', 13.92277000, -60.99512000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66129, 'Eau Piquant/St Urbain', 3763, '11', 186, 'LC', 13.76025000, -60.94012000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66130, 'En Bamboo', 3763, '11', 186, 'LC', 13.78155000, -60.94763000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66131, 'En Pois Doux/Babonneau', 3758, '02', 186, 'LC', 13.98853000, -60.94099000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66132, 'Enbar Pwin', 3757, '01', 186, 'LC', 13.91175000, -60.99266000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66133, 'Entrepot', 3758, '02', 186, 'LC', 13.99978000, -60.97953000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66134, 'Escap', 3762, '08', 186, 'LC', 13.83031000, -60.89673000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66135, 'Esperance', 3763, '11', 186, 'LC', 13.78854000, -60.96108000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66136, 'Esperance', 3760, '03', 186, 'LC', 13.80775000, -61.05331000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66137, 'Esperance', 3764, '10', 186, 'LC', 13.84562000, -61.03713000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66138, 'Etangs', 3764, '10', 186, 'LC', 13.81902000, -61.04056000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66139, 'Faux A Chaud', 3758, '02', 186, 'LC', 14.00878000, -60.99558000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66140, 'Ferrand', 3758, '02', 186, 'LC', 13.98684000, -60.98494000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66141, 'Floissac/Marc', 3758, '02', 186, 'LC', 13.96283000, -60.95880000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66142, 'Fond Assau/Babonneau', 3758, '02', 186, 'LC', 13.99491000, -60.93626000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66143, 'Fond Berange', 3759, '07', 186, 'LC', 13.78238000, -61.00346000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66144, 'Fond Bernier', 3764, '10', 186, 'LC', 13.85845000, -61.05949000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66145, 'Fond Cacoa', 3764, '10', 186, 'LC', 13.85442000, -61.05077000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66146, 'Fond Canie', 3758, '02', 186, 'LC', 13.99195000, -60.95954000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66147, 'Fond Capeche', 3763, '11', 186, 'LC', 13.77449000, -60.94835000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66148, 'Fond D\'Lor/Dugard', 3762, '08', 186, 'LC', 13.81069000, -60.92119000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66149, 'Fond Doux', 3764, '10', 186, 'LC', 13.82096000, -61.04952000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66150, 'Fond Estate', 3762, '08', 186, 'LC', 13.84243000, -60.91862000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66151, 'Fond Gens Libre', 3764, '10', 186, 'LC', 13.80791000, -61.06259000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66152, 'Fond Sabot', 3763, '11', 186, 'LC', 13.77659000, -60.95546000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66153, 'Fond/Desruisseaux', 3762, '08', 186, 'LC', 13.79421000, -60.94128000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66154, 'Forestiere', 3758, '02', 186, 'LC', 13.97775000, -60.95766000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66155, 'Franciou', 3760, '03', 186, 'LC', 13.80400000, -61.05448000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66156, 'Gadette', 3756, '05', 186, 'LC', 13.95937000, -60.91029000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66157, 'Garrand', 3766, '06', 186, 'LC', 14.00424000, -60.93270000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66158, 'Gayabois', 3759, '07', 186, 'LC', 13.78643000, -61.01835000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66159, 'Gentil', 3759, '07', 186, 'LC', 13.76816000, -60.98877000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66160, 'George Charles Boulevard', 3758, '02', 186, 'LC', 14.00499000, -60.98673000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66161, 'Getrine', 3759, '07', 186, 'LC', 13.78192000, -61.00741000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66162, 'Girard', 3758, '02', 186, 'LC', 14.00004000, -60.95960000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66163, 'Giraud', 3759, '07', 186, 'LC', 13.79254000, -61.00843000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66164, 'Goergeville', 3758, '02', 186, 'LC', 14.01311000, -60.98446000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66165, 'Gomier', 3762, '08', 186, 'LC', 13.81485000, -60.95252000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66166, 'Goodlands', 3758, '02', 186, 'LC', 13.99121000, -60.99840000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66167, 'Gouette', 3762, '08', 186, 'LC', 13.80650000, -60.93964000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66168, 'Grace', 3763, '11', 186, 'LC', 13.77678000, -60.96637000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66169, 'Grand Riviere', 3766, '06', 186, 'LC', 14.03546000, -60.95152000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66170, 'Grande Ravine', 3756, '05', 186, 'LC', 13.94263000, -60.93404000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66171, 'Grande Riviere', 3756, '05', 186, 'LC', 13.93306000, -60.93239000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66172, 'Grande Riviere/Assou Canal', 3766, '06', 186, 'LC', 14.03975000, -60.95121000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66173, 'Grande Riviere/Des Branch', 3756, '05', 186, 'LC', 13.93383000, -60.93499000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66174, 'Grande Riviere/En Leur Morne/Discompere', 3756, '05', 186, 'LC', 13.93223000, -60.93461000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66175, 'Grande Riviere/Funier', 3756, '05', 186, 'LC', 13.93240000, -60.93305000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66176, 'Grande Riviere/Ingle Woods', 3766, '06', 186, 'LC', 14.03091000, -60.95592000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66177, 'Grande Riviere/Morne Caca Cochon', 3756, '05', 186, 'LC', 13.92960000, -60.93794000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66178, 'Grande Riviere/Morne Serpent', 3766, '06', 186, 'LC', 14.03509000, -60.96060000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66179, 'Grande Riviere/Norbert', 3766, '06', 186, 'LC', 14.04317000, -60.94933000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66180, 'Grande Riviere/Piat', 3766, '06', 186, 'LC', 14.03926000, -60.94361000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66181, 'Grande Riviere/White Rock', 3766, '06', 186, 'LC', 14.03618000, -60.95556000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66182, 'Grass Street', 3758, '02', 186, 'LC', 14.00706000, -60.98763000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66183, 'Green Gold', 3758, '02', 186, 'LC', 13.99603000, -60.95284000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, '********'),
(66184, 'Gros Islet', 3766, '06', 186, 'LC', 14.06667000, -60.95000000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, 'Q3116994'),
(66185, 'Gros Islet Town', 3766, '06', 186, 'LC', 14.08080000, -60.95312000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, 'Q3116994'),
(66186, 'Gros Islet/Edge Water', 3766, '06', 186, 'LC', 14.07912000, -60.94973000, '2019-10-05 23:07:53', '2019-10-05 23:07:53', 1, 'Q3116994'),
(66187, 'Guesneau', 3758, '02', 186, 'LC', 13.99084000, -60.96486000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66188, 'H\'Erelle', 3759, '07', 186, 'LC', 13.75599000, -60.98698000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66189, 'Hewanorra Orchard', 3763, '11', 186, 'LC', 13.74486000, -60.97284000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66190, 'Hill 20/Babonneau', 3758, '02', 186, 'LC', 13.99941000, -60.94988000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66191, 'Hillcrest Gardens', 3758, '02', 186, 'LC', 14.01115000, -60.97083000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66192, 'Honeymoon Beach', 3762, '08', 186, 'LC', 13.77746000, -60.90493000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66193, 'Hope Estate', 3763, '11', 186, 'LC', 13.76477000, -60.95831000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66194, 'Hospital Road', 3758, '02', 186, 'LC', 14.00914000, -60.99906000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66195, 'Independence City', 3758, '02', 186, 'LC', 14.00199000, -60.97879000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66196, 'Industrial Estate', 3763, '11', 186, 'LC', 13.74096000, -60.97393000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66197, 'Industry', 3760, '03', 186, 'LC', 13.79127000, -61.05992000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66198, 'Jacmel', 3757, '01', 186, 'LC', 13.94796000, -61.01297000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66199, 'Jalousie', 3764, '10', 186, 'LC', 13.82811000, -61.05891000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66200, 'Jean Baptiste', 3757, '01', 186, 'LC', 13.94704000, -61.01578000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66201, 'John Compton Highway', 3758, '02', 186, 'LC', 14.01909000, -60.99110000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66202, 'Joyeux', 3763, '11', 186, 'LC', 13.78313000, -60.96068000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66203, 'Kennedy Highway/Laborie Bypass', 3759, '07', 186, 'LC', 13.75380000, -60.99594000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66204, 'L\'Anse Road', 3758, '02', 186, 'LC', 14.01875000, -60.98799000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66205, 'L\'Eau Mineau', 3762, '08', 186, 'LC', 13.80672000, -60.92103000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66206, 'L\'Hermitage', 3766, '06', 186, 'LC', 14.06521000, -60.93967000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66207, 'La Carierre', 3758, '02', 186, 'LC', 14.01806000, -60.98723000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66208, 'La Caye', 3756, '05', 186, 'LC', 13.93290000, -60.90434000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66209, 'La Clery', 3758, '02', 186, 'LC', 14.01846000, -60.98422000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66210, 'La Courville', 3762, '08', 186, 'LC', 13.81056000, -60.92766000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66211, 'La Croix Chabourgh', 3766, '06', 186, 'LC', 14.01147000, -60.94025000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66212, 'La Croix Maingot', 3758, '02', 186, 'LC', 13.96569000, -61.00582000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66213, 'La Fargue', 3760, '03', 186, 'LC', 13.77197000, -61.04124000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66214, 'La Guerre', 3766, '06', 186, 'LC', 14.02191000, -60.92491000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66215, 'La Guerre/Chicken Back Street', 3766, '06', 186, 'LC', 14.02387000, -60.93108000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66216, 'La Haut', 3759, '07', 186, 'LC', 13.79207000, -61.00014000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66217, 'La Haut', 3764, '10', 186, 'LC', 13.86276000, -61.05584000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66218, 'La Pansee', 3758, '02', 186, 'LC', 14.01368000, -60.98187000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66219, 'La Pearle', 3764, '10', 186, 'LC', 13.85740000, -61.04760000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66220, 'La Pelle', 3756, '05', 186, 'LC', 13.94096000, -60.90569000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66221, 'La Perle', 3759, '07', 186, 'LC', 13.76483000, -61.02088000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66222, 'La Pointe', 3762, '08', 186, 'LC', 13.84593000, -60.89332000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66223, 'La Pointe', 3760, '03', 186, 'LC', 13.79443000, -61.06497000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66224, 'La Pointe', 3756, '05', 186, 'LC', 13.91525000, -60.88830000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66225, 'La Resource', 3763, '11', 186, 'LC', 13.74799000, -60.96237000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66226, 'La Ressource', 3756, '05', 186, 'LC', 13.94293000, -60.91585000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66227, 'La Retraite', 3763, '11', 186, 'LC', 13.75944000, -60.96632000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66228, 'La Tille', 3762, '08', 186, 'LC', 13.82744000, -60.91716000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66229, 'La Toc', 3758, '02', 186, 'LC', 14.00824000, -61.00404000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66230, 'La Tourney/Cedar Heights', 3763, '11', 186, 'LC', 13.74114000, -60.96517000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66231, 'La Trielle', 3757, '01', 186, 'LC', 13.94014000, -60.99729000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66232, 'Labayee', 3758, '02', 186, 'LC', 13.94044000, -60.97316000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q3116994'),
(66233, 'Laborie', 3759, '07', 186, 'LC', 13.75000000, -60.98333000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66234, 'Lamaze', 3760, '03', 186, 'LC', 13.80649000, -61.01906000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66235, 'Lastic Hill', 3758, '02', 186, 'LC', 14.00755000, -60.98321000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66236, 'Le Riche', 3760, '03', 186, 'LC', 13.78365000, -61.04815000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66237, 'Lenny Hill', 3764, '10', 186, 'LC', 13.85059000, -61.05906000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66238, 'Leslie Land', 3758, '02', 186, 'LC', 14.00752000, -60.98674000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66239, 'Lezy', 3762, '08', 186, 'LC', 13.80372000, -60.93906000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66240, 'Lombard', 3762, '08', 186, 'LC', 13.85772000, -60.91510000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66241, 'Londonderry', 3759, '07', 186, 'LC', 13.77623000, -61.01362000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66242, 'Lumiere', 3756, '05', 186, 'LC', 13.93928000, -60.88889000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66243, 'Macdomel', 3759, '07', 186, 'LC', 13.77497000, -60.98508000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66244, 'Maganier', 3763, '11', 186, 'LC', 13.79450000, -60.98513000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66245, 'Mahaut', 3762, '08', 186, 'LC', 13.84391000, -60.95397000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66246, 'Mailly Motete', 3760, '03', 186, 'LC', 13.81788000, -61.02772000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66247, 'Malgretoute', 3762, '08', 186, 'LC', 13.84199000, -60.90411000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66248, 'Malgretoute', 3764, '10', 186, 'LC', 13.84233000, -61.05998000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66249, 'Mamiku', 3762, '08', 186, 'LC', 13.86744000, -60.90184000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66250, 'Marc', 3758, '02', 186, 'LC', 13.95688000, -60.95724000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66251, 'Marchand', 3758, '02', 186, 'LC', 14.00415000, -60.98532000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66252, 'Marigot', 3758, '02', 186, 'LC', 13.96212000, -61.02205000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66253, 'Marisule', 3766, '06', 186, 'LC', 14.04621000, -60.97063000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66254, 'Marisule/Bon Air', 3766, '06', 186, 'LC', 14.05062000, -60.97124000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66255, 'Marisule/East Winds', 3766, '06', 186, 'LC', 14.05407000, -60.96849000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66256, 'Marisule/La Brellotte', 3766, '06', 186, 'LC', 14.05710000, -60.97183000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66257, 'Marisule/Top Of The World', 3766, '06', 186, 'LC', 14.04385000, -60.96885000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66258, 'Marquis Estate', 3766, '06', 186, 'LC', 14.02960000, -60.90732000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66259, 'Martin', 3760, '03', 186, 'LC', 13.78941000, -61.04542000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66260, 'Massacre', 3757, '01', 186, 'LC', 13.94872000, -61.03544000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66261, 'Massade', 3766, '06', 186, 'LC', 14.08292000, -60.94946000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66262, 'Maynard Hill', 3758, '02', 186, 'LC', 14.00314000, -60.98966000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66263, 'Miami/Bexon', 3758, '02', 186, 'LC', 13.92295000, -60.96945000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q13220042'),
(66264, 'Micoud', 3762, '08', 186, 'LC', 13.81667000, -60.90000000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66265, 'Migny', 3764, '10', 186, 'LC', 13.84166000, -61.01808000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66266, 'Millet', 3757, '01', 186, 'LC', 13.90985000, -60.98884000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66267, 'Mocha', 3764, '10', 186, 'LC', 13.83117000, -61.02503000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66268, 'Mon Repos', 3762, '08', 186, 'LC', 13.85865000, -60.89591000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66269, 'Monchy', 3766, '06', 186, 'LC', 14.05502000, -60.92623000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66270, 'Monchy/Careffe', 3766, '06', 186, 'LC', 14.05930000, -60.95069000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66271, 'Monchy/Cletus Village', 3766, '06', 186, 'LC', 14.05137000, -60.92380000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66272, 'Monchy/La Borne', 3766, '06', 186, 'LC', 14.04628000, -60.91961000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66273, 'Monchy/La Borne/Sans Souci', 3766, '06', 186, 'LC', 14.04504000, -60.89961000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66274, 'Monchy/La Retraite', 3766, '06', 186, 'LC', 14.06421000, -60.94564000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66275, 'Monchy/Lafeuillee', 3766, '06', 186, 'LC', 14.05950000, -60.94086000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66276, 'Monchy/Lawi Fwen', 3766, '06', 186, 'LC', 14.05802000, -60.92906000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66277, 'Monchy/Malgretoute', 3766, '06', 186, 'LC', 14.04108000, -60.91912000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66278, 'Monchy/Moulin A Vent', 3766, '06', 186, 'LC', 14.06283000, -60.95025000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66279, 'Monchy/Ravine Macock', 3766, '06', 186, 'LC', 14.04789000, -60.92511000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66280, 'Monchy/Riviere Mitan', 3766, '06', 186, 'LC', 14.04987000, -60.94029000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66281, 'Monchy/Ti Dauphin', 3766, '06', 186, 'LC', 14.04081000, -60.93225000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66282, 'Monchy/Vieux Sucreic', 3766, '06', 186, 'LC', 14.04996000, -60.94992000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66283, 'Monchy/Vieux Sucreic/Bois D\'Inde', 3766, '06', 186, 'LC', 14.05613000, -60.95151000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66284, 'Monchy/Vieux Sucreic/Careffe', 3766, '06', 186, 'LC', 14.05652000, -60.94948000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66285, 'Mongiraud', 3766, '06', 186, 'LC', 14.06287000, -60.95722000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66286, 'Mongouge', 3760, '03', 186, 'LC', 13.80301000, -61.04524000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66287, 'Monier', 3766, '06', 186, 'LC', 14.02951000, -60.94098000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66288, 'Monkey Town Ciceron', 3758, '02', 186, 'LC', 13.98885000, -61.00615000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66289, 'Monzie', 3760, '03', 186, 'LC', 13.80669000, -61.02297000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66290, 'Moreau', 3762, '08', 186, 'LC', 13.82676000, -60.94238000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66291, 'Morne Assau/Babonneau', 3758, '02', 186, 'LC', 13.99079000, -60.93241000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66292, 'Morne Bonin', 3764, '10', 186, 'LC', 13.83174000, -61.03285000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66293, 'Morne Cayenne', 3763, '11', 186, 'LC', 13.78349000, -60.95506000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66294, 'Morne Ciseaux', 3757, '01', 186, 'LC', 13.93597000, -61.00330000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66295, 'Morne Citon', 3766, '06', 186, 'LC', 14.03004000, -60.92813000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66296, 'Morne D\'Or', 3757, '01', 186, 'LC', 13.94931000, -61.00601000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66297, 'Morne Dudon', 3758, '02', 186, 'LC', 14.01194000, -60.96378000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66298, 'Morne Gomier', 3759, '07', 186, 'LC', 13.76552000, -60.99524000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66299, 'Morne Jacques', 3760, '03', 186, 'LC', 13.81162000, -61.03165000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66300, 'Morne La Croix', 3764, '10', 186, 'LC', 13.81762000, -61.06146000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66301, 'Morne Lastic/Desruisseaux', 3764, '10', 186, 'LC', 13.86628000, -61.05894000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66302, 'Morne Le Blanc', 3759, '07', 186, 'LC', 13.75868000, -60.99913000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66303, 'Morne Panache', 3756, '05', 186, 'LC', 13.92167000, -60.93654000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66304, 'Morne Road', 3758, '02', 186, 'LC', 14.00544000, -60.99693000, '2019-10-05 23:07:54', '2019-10-05 23:07:54', 1, 'Q11779522'),
(66305, 'Morne Rouge/Marc', 3758, '02', 186, 'LC', 13.95838000, -60.96972000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66306, 'Morne Sion', 3760, '03', 186, 'LC', 13.79246000, -61.05467000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66307, 'Morne Vert', 3763, '11', 186, 'LC', 13.77899000, -60.97617000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66308, 'Morne Vient', 3762, '08', 186, 'LC', 13.80646000, -60.95241000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66309, 'Motete', 3760, '03', 186, 'LC', 13.81325000, -61.02320000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66310, 'Moule A Chique', 3763, '11', 186, 'LC', 13.71458000, -60.94835000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66311, 'Mount Pleasant', 3758, '02', 186, 'LC', 14.01494000, -60.98576000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66312, 'Myette Gardens', 3762, '08', 186, 'LC', 13.81862000, -60.90440000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66313, 'New Development', 3764, '10', 186, 'LC', 13.86176000, -61.04791000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66314, 'New Field/Fiette', 3760, '03', 186, 'LC', 13.78815000, -61.05216000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66315, 'New Village', 3762, '08', 186, 'LC', 13.82446000, -60.89864000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66316, 'New Village', 3758, '02', 186, 'LC', 14.01104000, -60.98456000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66317, 'Obrier', 3763, '11', 186, 'LC', 13.76831000, -60.97313000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66318, 'Odsan', 3758, '02', 186, 'LC', 13.97479000, -60.98319000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66319, 'Olibo', 3759, '07', 186, 'LC', 13.77628000, -60.99931000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66320, 'Paix Bouche', 3766, '06', 186, 'LC', 14.01755000, -60.93852000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66321, 'Paix Bouche', 3762, '08', 186, 'LC', 13.81556000, -60.94073000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66322, 'Palmiste', 3764, '10', 186, 'LC', 13.85843000, -61.05575000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66323, 'Palmiste Estate', 3762, '08', 186, 'LC', 13.85377000, -60.96007000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66324, 'Parc Estate', 3759, '07', 186, 'LC', 13.79639000, -61.01613000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66325, 'Parker\'S Hill', 3758, '02', 186, 'LC', 14.00378000, -60.99049000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66326, 'Patience', 3762, '08', 186, 'LC', 13.85000000, -60.90874000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66327, 'Patterson\'S Gap', 3758, '02', 186, 'LC', 14.00655000, -60.98473000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66328, 'Pavee', 3758, '02', 186, 'LC', 14.00282000, -60.99191000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66329, 'Peart\'S Gap', 3758, '02', 186, 'LC', 14.01035000, -60.98598000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66330, 'Perou', 3758, '02', 186, 'LC', 13.95960000, -61.00726000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66331, 'Piaye', 3759, '07', 186, 'LC', 13.75628000, -61.02279000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66332, 'Pierrot', 3763, '11', 186, 'LC', 13.76857000, -60.94381000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66333, 'Pigeon Island', 3766, '06', 186, 'LC', 14.08965000, -60.95724000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66334, 'Planard', 3762, '08', 186, 'LC', 13.80521000, -60.91540000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66335, 'Plat Pays', 3764, '10', 186, 'LC', 13.84085000, -61.05571000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66336, 'Plateau', 3766, '06', 186, 'LC', 14.02612000, -60.93474000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66337, 'Plut', 3763, '11', 186, 'LC', 13.77417000, -60.96076000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66338, 'Pointe Seraphine', 3758, '02', 186, 'LC', 14.01575000, -60.99305000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66339, 'Pomme', 3763, '11', 186, 'LC', 13.75792000, -60.98147000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66340, 'Ponyon', 3760, '03', 186, 'LC', 13.79639000, -61.04360000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66341, 'Praslin', 3762, '08', 186, 'LC', 13.87545000, -60.89717000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66342, 'Quarte Chemins', 3758, '02', 186, 'LC', 13.99136000, -60.97630000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66343, 'Rabot', 3764, '10', 186, 'LC', 13.83380000, -61.04540000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66344, 'Rameau', 3762, '08', 186, 'LC', 13.83055000, -60.92375000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66345, 'Raveneau', 3760, '03', 186, 'LC', 13.81018000, -61.04642000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66346, 'Ravine Chabot', 3758, '02', 186, 'LC', 14.00051000, -60.97617000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66347, 'Ravine Claire', 3764, '10', 186, 'LC', 13.85262000, -61.02892000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66348, 'Ravine Poisson', 3758, '02', 186, 'LC', 13.93156000, -60.96849000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66349, 'Ravine Touterelle', 3758, '02', 186, 'LC', 14.00325000, -60.98806000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66350, 'Reduit', 3766, '06', 186, 'LC', 14.06829000, -60.96145000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66351, 'Reduit Orchard', 3766, '06', 186, 'LC', 14.06594000, -60.95340000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66352, 'Reduit Park', 3766, '06', 186, 'LC', 14.06622000, -60.95560000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66353, 'Resinard/Babonneau', 3758, '02', 186, 'LC', 14.00289000, -60.94200000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66354, 'Reunion', 3760, '03', 186, 'LC', 13.77861000, -61.04531000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66355, 'Riche Fond', 3756, '05', 186, 'LC', 13.93644000, -60.92026000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66356, 'Riche Fond/La Belle Vie', 3756, '05', 186, 'LC', 13.93782000, -60.92908000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66357, 'Riche Fond/New Village', 3756, '05', 186, 'LC', 13.93648000, -60.92394000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66358, 'River Doree', 3760, '03', 186, 'LC', 13.76769000, -61.03646000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66359, 'Riverside Road', 3761, '12', 186, 'LC', 13.89408000, -61.04989000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66360, 'Roblot', 3760, '03', 186, 'LC', 13.80323000, -61.02297000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66361, 'Rock Hall', 3758, '02', 186, 'LC', 14.00009000, -60.98928000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66362, 'Rodney Bay', 3766, '06', 186, 'LC', 14.07292000, -60.95443000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66363, 'Rodney Heights', 3766, '06', 186, 'LC', 14.06731000, -60.94906000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66364, 'Rose Hill', 3758, '02', 186, 'LC', 14.00816000, -60.98560000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66365, 'Roseau Valley', 3757, '01', 186, 'LC', 13.95317000, -61.02676000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66366, 'Ruby Estate', 3764, '10', 186, 'LC', 13.85812000, -61.04983000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66367, 'Saint Marie Road', 3762, '08', 186, 'LC', 13.85444000, -60.91803000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66368, 'Saltibus', 3759, '07', 186, 'LC', 13.81211000, -61.00982000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66369, 'San Soucis', 3758, '02', 186, 'LC', 14.01695000, -60.98985000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66370, 'Sand De Feu', 3758, '02', 186, 'LC', 13.92781000, -60.98026000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66371, 'Saphire', 3759, '07', 186, 'LC', 13.75670000, -61.01209000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66372, 'Sarot', 3758, '02', 186, 'LC', 13.94212000, -60.98489000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66373, 'Saut', 3762, '08', 186, 'LC', 13.81272000, -60.94375000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66374, 'Sauzay', 3760, '03', 186, 'LC', 13.77743000, -61.03368000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66375, 'Savannes', 3762, '08', 186, 'LC', 13.77098000, -60.92017000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66376, 'Savannesgeorge/Constitution', 3760, '03', 186, 'LC', 13.77855000, -61.05139000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66377, 'Soufriere Estate', 3764, '10', 186, 'LC', 13.85174000, -61.05420000, '2019-10-05 23:07:55', '2019-10-05 23:07:55', 1, 'Q11779522'),
(66378, 'Soufrière', 3764, '10', 186, 'LC', 13.85616000, -61.05660000, '2019-10-05 23:07:55', '2020-05-01 17:23:14', 1, 'Q493030'),
(66379, 'St Helen Estate', 3762, '08', 186, 'LC', 13.78735000, -60.90726000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66380, 'St Jude\'S Highway', 3763, '11', 186, 'LC', 13.75867000, -60.97360000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66381, 'St Lawrence', 3757, '01', 186, 'LC', 13.93852000, -61.02503000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66382, 'St Lawrence Estate', 3757, '01', 186, 'LC', 13.94236000, -61.04248000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66383, 'St Phillip', 3764, '10', 186, 'LC', 13.84432000, -61.02766000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66384, 'St. Joseph Estate', 3756, '05', 186, 'LC', 13.90066000, -60.91444000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66385, 'Stonefield', 3764, '10', 186, 'LC', 13.84513000, -61.05990000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66386, 'Sulphur Springs', 3764, '10', 186, 'LC', 13.84002000, -61.04512000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66387, 'Summersdale', 3758, '02', 186, 'LC', 14.02582000, -60.97644000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66388, 'Sunbilt', 3758, '02', 186, 'LC', 14.00561000, -60.97922000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66389, 'Sunny Acres', 3758, '02', 186, 'LC', 14.02810000, -60.97361000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66390, 'Talvern', 3758, '02', 186, 'LC', 13.99578000, -60.94410000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66391, 'Tapion', 3758, '02', 186, 'LC', 14.01383000, -61.00489000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66392, 'Tete Chemin', 3757, '01', 186, 'LC', 13.89092000, -60.99785000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66393, 'Tete Morne', 3759, '07', 186, 'LC', 13.76160000, -61.03397000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66394, 'Tete Morne/Morne Andrew', 3763, '11', 186, 'LC', 13.77114000, -60.95194000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66395, 'Thamazo', 3756, '05', 186, 'LC', 13.93245000, -60.94786000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66396, 'The Morne', 3758, '02', 186, 'LC', 13.99778000, -60.99620000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66397, 'Theodrine', 3761, '12', 186, 'LC', 13.92878000, -61.04972000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66398, 'Ti Boug', 3764, '10', 186, 'LC', 13.83744000, -61.02460000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66399, 'Ti Colon', 3758, '02', 186, 'LC', 13.97071000, -61.00229000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66400, 'Ti Delcer', 3764, '10', 186, 'LC', 13.83386000, -61.06410000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66401, 'Ti Riviere', 3762, '08', 186, 'LC', 13.83006000, -60.93774000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66402, 'Ti Rocher', 3758, '02', 186, 'LC', 13.99490000, -60.97110000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66403, 'Ti Rocher', 3762, '08', 186, 'LC', 13.81734000, -60.92858000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66404, 'Toraille', 3764, '10', 186, 'LC', 13.85704000, -61.03296000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66405, 'Town', 3764, '10', 186, 'LC', 13.85491000, -61.05764000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66406, 'Town', 3763, '11', 186, 'LC', 13.72835000, -60.95408000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66407, 'Trois Piton', 3758, '02', 186, 'LC', 13.98890000, -60.97259000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66408, 'Trou Cochan/Marc', 3758, '02', 186, 'LC', 13.94220000, -60.96199000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66409, 'Trou Florent/Marc', 3758, '02', 186, 'LC', 13.94525000, -60.95382000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66410, 'Trou Rouge', 3758, '02', 186, 'LC', 14.00602000, -60.98950000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66411, 'Troumassee', 3762, '08', 186, 'LC', 13.80892000, -60.90341000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66412, 'Union', 3766, '06', 186, 'LC', 14.02907000, -60.96480000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66413, 'Union Terrace', 3766, '06', 186, 'LC', 14.02654000, -60.95710000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66414, 'Union Vale', 3764, '10', 186, 'LC', 13.81103000, -61.05686000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66415, 'Union/Ti Morne', 3766, '06', 186, 'LC', 14.02283000, -60.95272000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66416, 'Vanard', 3757, '01', 186, 'LC', 13.93558000, -60.99363000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66417, 'Venus', 3757, '01', 186, 'LC', 13.91558000, -61.01676000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66418, 'Victoria', 3760, '03', 186, 'LC', 13.81116000, -61.03963000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66419, 'Vide Bouteille', 3758, '02', 186, 'LC', 14.02439000, -60.98066000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q493030'),
(66420, 'Vieux Fort', 3763, '11', 186, 'LC', 13.71667000, -60.95000000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66421, 'Vieux Fort/Laborie Highway', 3763, '11', 186, 'LC', 13.73197000, -60.95477000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66422, 'Vige\'', 3763, '11', 186, 'LC', 13.78692000, -60.93550000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66423, 'Vigie', 3758, '02', 186, 'LC', 14.02053000, -60.99611000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66424, 'Village', 3760, '03', 186, 'LC', 13.77464000, -61.05020000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66425, 'Village', 3762, '08', 186, 'LC', 13.81904000, -60.89692000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66426, 'Village', 3759, '07', 186, 'LC', 13.74927000, -60.99375000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66427, 'Village', 3757, '01', 186, 'LC', 13.93939000, -61.04150000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66428, 'Village', 3761, '12', 186, 'LC', 13.90526000, -61.06108000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66429, 'Village/Petite Bourgh', 3757, '01', 186, 'LC', 13.93801000, -61.04184000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66430, 'Volet', 3762, '08', 186, 'LC', 13.82626000, -60.90789000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66431, 'Warwick/Daban', 3759, '07', 186, 'LC', 13.81084000, -61.00047000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66432, 'Waterworks', 3758, '02', 186, 'LC', 14.00498000, -60.97635000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66433, 'Westall Group/The Mangue', 3763, '11', 186, 'LC', 13.72707000, -60.95312000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66434, 'Wilton\'S Yard/Grave Yard', 3758, '02', 186, 'LC', 14.00968000, -60.98639000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66435, 'Yorke Hill', 3758, '02', 186, 'LC', 14.01694000, -60.97919000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66436, 'Zenon', 3764, '10', 186, 'LC', 13.85953000, -61.03394000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66437, 'Balzers', 458, '01', 125, 'LI', 47.06665000, 9.50251000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66438, 'Eschen', 451, '02', 125, 'LI', 47.21071000, 9.52223000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66439, 'Gamprin', 457, '03', 125, 'LI', 47.22038000, 9.50935000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q3494579'),
(66440, 'Mauren', 455, '04', 125, 'LI', 47.21805000, 9.54420000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q49661'),
(66441, 'Planken', 454, '05', 125, 'LI', 47.18516000, 9.54437000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q49660'),
(66442, 'Ruggell', 453, '06', 125, 'LI', 47.23799000, 9.52540000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q49660'),
(66443, 'Schaan', 450, '07', 125, 'LI', 47.16498000, 9.50867000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q49660'),
(66444, 'Schellenberg', 449, '08', 125, 'LI', 47.23123000, 9.54678000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q49655'),
(66445, 'Triesen', 459, '09', 125, 'LI', 47.10752000, 9.52815000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q49655'),
(66446, 'Triesenberg', 456, '10', 125, 'LI', 47.11815000, 9.54197000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q49655'),
(66447, 'Vaduz', 452, '11', 125, 'LI', 47.14151000, 9.52154000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q1844'),
(66448, 'Ambalangoda', 2801, '3', 208, 'LK', 6.23550000, 80.05380000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q4740977'),
(66449, 'Ampara', 2808, '5', 208, 'LK', 7.29754000, 81.68202000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q696444'),
(66450, 'Ampara District', 2808, '5', 208, 'LK', 7.08330000, 81.75000000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q474395'),
(66451, 'Anuradhapura', 2800, '7', 208, 'LK', 8.31223000, 80.41306000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q5724'),
(66452, 'Anuradhapura District', 2800, '7', 208, 'LK', 8.33333000, 80.50000000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q612614'),
(66453, 'Badulla', 2811, '8', 208, 'LK', 6.98020000, 81.05770000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q390096'),
(66454, 'Badulla District', 2811, '8', 208, 'LK', 6.98472000, 81.05639000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q799713'),
(66455, 'Battaramulla South', 2802, '1', 208, 'LK', 6.89640000, 79.91810000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q4869689'),
(66456, 'Batticaloa', 2808, '5', 208, 'LK', 7.71020000, 81.69240000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q810963'),
(66457, 'Batticaloa District', 2808, '5', 208, 'LK', 7.75000000, 81.49970000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q810960'),
(66458, 'Bentota', 2801, '3', 208, 'LK', 6.42598000, 79.99575000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q4890663');

