INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(32358, 'Amposta', 1203, 'T', 207, 'ES', 40.70995000, 0.57856000, '2019-10-05 22:45:34', '2022-08-29 10:57:32', 1, 'Q427240'),
(32359, 'Ampudia', 1157, 'P', 207, 'ES', 41.91608000, -4.78033000, '2019-10-05 22:45:34', '2022-08-29 11:45:44', 1, 'Q24012244'),
(32360, 'Ampuero', 1170, 'S', 207, 'ES', 43.34268000, -3.41667000, '2019-10-05 22:45:34', '2019-10-05 22:45:34', 1, 'Q24012244'),
(32361, 'Amurrio', 5093, 'VI', 207, 'ES', 43.05000000, -3.00000000, '2019-10-05 22:45:34', '2022-08-28 18:23:49', 1, 'Q481760'),
(32362, 'Amusco', 1157, 'P', 207, 'ES', 42.17306000, -4.47018000, '2019-10-05 22:45:34', '2022-08-29 11:45:44', 1, 'Q1778135'),
(32363, 'Amusquillo', 1183, 'VA', 207, 'ES', 41.74853000, -4.30117000, '2019-10-05 22:45:34', '2022-08-29 11:48:44', 1, 'Q1907334'),
(32364, 'Amés', 5089, 'C', 207, 'ES', 42.90426000, -8.65551000, '2019-10-05 22:45:35', '2022-08-28 13:37:16', 1, 'Q1907334'),
(32365, 'Anadón', 5111, 'TE', 207, 'ES', 40.98225000, -0.98367000, '2019-10-05 22:45:35', '2022-08-29 11:29:43', 1, 'Q1645446'),
(32366, 'Anaya', 1192, 'SG', 207, 'ES', 40.99184000, -4.30950000, '2019-10-05 22:45:35', '2022-08-29 11:50:42', 1, 'Q1917942'),
(32367, 'Anaya de Alba', 1147, 'SA', 207, 'ES', 40.72828000, -5.49266000, '2019-10-05 22:45:35', '2022-08-29 11:44:50', 1, 'Q1765316'),
(32368, 'Anchuelo', 1158, 'M', 207, 'ES', 40.46527000, -3.26838000, '2019-10-05 22:45:35', '2022-08-29 12:04:40', 1, 'Q1647116'),
(32369, 'Anchuras', 5105, 'CR', 207, 'ES', 39.48059000, -4.83381000, '2019-10-05 22:45:35', '2022-08-29 11:03:24', 1, 'Q920865'),
(32370, 'Ancín', 1204, 'NA', 207, 'ES', 42.66047000, -2.18885000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1645748'),
(32371, 'Andilla', 1175, 'V', 207, 'ES', 39.83333000, -0.80000000, '2019-10-05 22:45:35', '2022-08-29 12:05:40', 1, 'Q1917947'),
(32372, 'Andoain', 1191, 'SS', 207, 'ES', 43.21658000, -2.02530000, '2019-10-05 22:45:35', '2022-08-28 18:36:49', 1, 'Q1598976'),
(32373, 'Andorra', 5111, 'TE', 207, 'ES', 40.97655000, -0.44721000, '2019-10-05 22:45:35', '2022-08-29 11:29:43', 1, 'Q24003627'),
(32374, 'Andosilla', 1204, 'NA', 207, 'ES', 42.38144000, -1.67305000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q425775'),
(32375, 'Andratx', 1174, 'PM', 207, 'ES', 39.57553000, 2.42017000, '2019-10-05 22:45:35', '2019-10-05 22:45:35', 1, 'Q425775'),
(32376, 'Andújar', 5100, 'J', 207, 'ES', 38.03922000, -4.05077000, '2019-10-05 22:45:35', '2022-08-28 19:04:30', 1, 'Q528530'),
(32377, 'Anento', 5113, 'Z', 207, 'ES', 41.06958000, -1.33375000, '2019-10-05 22:45:35', '2022-08-29 11:42:53', 1, 'Q1639608'),
(32378, 'Anglesola', 5104, 'L', 207, 'ES', 41.65649000, 1.08286000, '2019-10-05 22:45:35', '2022-08-29 10:55:25', 1, 'Q1778184'),
(32379, 'Anglès', 5103, 'GI', 207, 'ES', 41.95617000, 2.63603000, '2019-10-05 22:45:35', '2022-08-29 10:53:16', 1, 'Q13027'),
(32380, 'Anguciana', 1171, 'LO', 207, 'ES', 42.57421000, -2.90124000, '2019-10-05 22:45:35', '2022-08-29 12:05:09', 1, 'Q1641228'),
(32381, 'Anguiano', 1171, 'LO', 207, 'ES', 42.26381000, -2.76463000, '2019-10-05 22:45:35', '2022-08-29 12:05:09', 1, 'Q1637330'),
(32382, 'Anguita', 5107, 'GU', 207, 'ES', 41.02659000, -2.36849000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q620836'),
(32383, 'Anguix', 1146, 'BU', 207, 'ES', 41.75331000, -3.93125000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1447822'),
(32384, 'Angüés', 1177, 'HU', 207, 'ES', 42.11109000, -0.15298000, '2019-10-05 22:45:35', '2022-08-29 12:06:20', 1, 'Q1769704'),
(32385, 'Anna', 1175, 'V', 207, 'ES', 39.02029000, -0.64621000, '2019-10-05 22:45:35', '2022-08-29 12:05:40', 1, 'Q1769704'),
(32386, 'Anoeta', 1191, 'SS', 207, 'ES', 43.16241000, -2.07107000, '2019-10-05 22:45:35', '2022-08-28 18:36:49', 1, 'Q1640537'),
(32387, 'Anquela del Ducado', 5107, 'GU', 207, 'ES', 40.97084000, -2.13066000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q620829'),
(32388, 'Anquela del Pedregal', 5107, 'GU', 207, 'ES', 40.74336000, -1.73697000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q634254'),
(32389, 'Ansó', 1177, 'HU', 207, 'ES', 42.75785000, -0.82947000, '2019-10-05 22:45:35', '2022-08-29 12:06:20', 1, 'Q570484'),
(32390, 'Antas', 5095, 'AL', 207, 'ES', 37.24536000, -1.91760000, '2019-10-05 22:45:35', '2022-08-28 18:41:41', 1, 'Q1444119'),
(32391, 'Antella', 1175, 'V', 207, 'ES', 39.07977000, -0.59195000, '2019-10-05 22:45:35', '2022-08-29 12:05:40', 1, 'Q1444119'),
(32392, 'Antequera', 5101, 'MA', 207, 'ES', 37.01938000, -4.56123000, '2019-10-05 22:45:35', '2022-08-28 19:06:52', 1, 'Q572041'),
(32393, 'Antigua', 1185, 'GC', 207, 'ES', 28.42307000, -14.01379000, '2019-10-05 22:45:35', '2022-08-29 12:06:32', 1, 'Q575670'),
(32394, 'Antigüedad', 1157, 'P', 207, 'ES', 41.94681000, -4.12058000, '2019-10-05 22:45:35', '2022-08-29 11:45:44', 1, 'Q1922085'),
(32395, 'Antillón', 1177, 'HU', 207, 'ES', 42.03639000, -0.16239000, '2019-10-05 22:45:35', '2022-08-29 12:06:20', 1, 'Q1657221'),
(32396, 'Antzuola', 1191, 'SS', 207, 'ES', 43.09725000, -2.37998000, '2019-10-05 22:45:35', '2022-08-28 18:36:49', 1, 'Q1625886'),
(32397, 'Aoiz', 1204, 'NA', 207, 'ES', 42.78633000, -1.37252000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1625886'),
(32399, 'Aracena', 5099, 'H', 207, 'ES', 37.89396000, -6.56116000, '2019-10-05 22:45:35', '2022-08-28 19:00:43', 1, 'Q624511'),
(32400, 'Arafo', 5112, 'TF', 207, 'ES', 28.33971000, -16.42244000, '2019-10-05 22:45:35', '2022-08-29 11:31:13', 1, 'Q624967'),
(32401, 'Aragüés del Puerto', 1177, 'HU', 207, 'ES', 42.70641000, -0.66999000, '2019-10-05 22:45:35', '2022-08-29 12:06:20', 1, 'Q927458'),
(32402, 'Arahuetes', 1192, 'SG', 207, 'ES', 41.13762000, -3.85664000, '2019-10-05 22:45:35', '2022-08-29 11:50:42', 1, 'Q1776860'),
(32404, 'Arama', 1191, 'SS', 207, 'ES', 43.06335000, -2.16540000, '2019-10-05 22:45:35', '2022-08-28 18:36:49', 1, 'Q1625902'),
(32405, 'Aranarache', 1204, 'NA', 207, 'ES', 42.77979000, -2.22924000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1645785'),
(32406, 'Arancón', 1208, 'SO', 207, 'ES', 41.80017000, -2.28141000, '2019-10-05 22:45:35', '2022-08-29 11:51:23', 1, 'Q36805'),
(32407, 'Aranda de Duero', 1146, 'BU', 207, 'ES', 41.67041000, -3.68920000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q495380'),
(32408, 'Aranda de Moncayo', 5113, 'Z', 207, 'ES', 41.57749000, -1.79171000, '2019-10-05 22:45:35', '2022-08-29 11:42:53', 1, 'Q24016493'),
(32409, 'Arandilla', 1146, 'BU', 207, 'ES', 41.73778000, -3.42914000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1614757'),
(32410, 'Arandilla del Arroyo', 5106, 'CU', 207, 'ES', 40.51056000, -2.38355000, '2019-10-05 22:45:35', '2022-08-29 11:05:00', 1, 'Q1648789'),
(32411, 'Aranga', 5089, 'C', 207, 'ES', 43.23469000, -8.01705000, '2019-10-05 22:45:35', '2022-08-28 13:37:16', 1, 'Q1648789'),
(32412, 'Aranjuez', 1158, 'M', 207, 'ES', 40.03108000, -3.60246000, '2019-10-05 22:45:35', '2022-08-29 12:04:40', 1, 'Q486792'),
(32413, 'Arano', 1204, 'NA', 207, 'ES', 43.19956000, -1.89569000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q768954'),
(32414, 'Aranzueque', 5107, 'GU', 207, 'ES', 40.49094000, -3.07448000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q633917'),
(32415, 'Arapiles', 1147, 'SA', 207, 'ES', 40.89366000, -5.64494000, '2019-10-05 22:45:35', '2022-08-29 11:44:50', 1, 'Q1641154'),
(32416, 'Aras', 1204, 'NA', 207, 'ES', 42.56172000, -2.35600000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1645807'),
(32417, 'Arauzo de Miel', 1146, 'BU', 207, 'ES', 41.85878000, -3.38773000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1447839'),
(32418, 'Arauzo de Salce', 1146, 'BU', 207, 'ES', 41.81919000, -3.41191000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1447859'),
(32419, 'Arauzo de Torre', 1146, 'BU', 207, 'ES', 41.79818000, -3.42314000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1614650'),
(32420, 'Arañuel', 5110, 'CS', 207, 'ES', 40.06667000, -0.48333000, '2019-10-05 22:45:35', '2022-08-29 11:26:42', 1, 'Q766369'),
(32421, 'Arbancón', 5107, 'GU', 207, 'ES', 40.96434000, -3.11505000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q632987'),
(32422, 'Arbeca', 5104, 'L', 207, 'ES', 41.54153000, 0.92457000, '2019-10-05 22:45:35', '2022-08-29 10:55:25', 1, 'Q372836'),
(32423, 'Arbeteta', 5107, 'GU', 207, 'ES', 40.66999000, -2.40236000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q592376'),
(32424, 'Arbizu', 1204, 'NA', 207, 'ES', 42.91545000, -2.03917000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1645754'),
(32425, 'Arbo', 1167, 'PO', 207, 'ES', 42.11667000, -8.31667000, '2019-10-05 22:45:35', '2022-08-28 17:57:54', 1, 'Q630031'),
(32426, 'Arboleas', 5095, 'AL', 207, 'ES', 37.35024000, -2.07384000, '2019-10-05 22:45:35', '2022-08-28 18:41:41', 1, 'Q1157985'),
(32427, 'Arbúcies', 5103, 'GI', 207, 'ES', 41.81667000, 2.51667000, '2019-10-05 22:45:35', '2022-08-29 10:53:16', 1, 'Q13010'),
(32428, 'Arcediano', 1147, 'SA', 207, 'ES', 41.09379000, -5.56055000, '2019-10-05 22:45:35', '2022-08-29 11:44:50', 1, 'Q1777910'),
(32429, 'Arcenillas', 1161, 'ZA', 207, 'ES', 41.45641000, -5.68512000, '2019-10-05 22:45:35', '2022-08-29 11:48:03', 1, 'Q1650433'),
(32430, 'Archena', 1176, 'MU', 207, 'ES', 38.11631000, -1.30043000, '2019-10-05 22:45:35', '2022-08-29 12:05:49', 1, 'Q632621'),
(32431, 'Archidona', 5101, 'MA', 207, 'ES', 37.09654000, -4.38869000, '2019-10-05 22:45:35', '2022-08-28 19:06:52', 1, 'Q920126'),
(32432, 'Arcicóllar', 1205, 'TO', 207, 'ES', 40.05517000, -4.11638000, '2019-10-05 22:45:35', '2022-08-29 11:08:29', 1, 'Q1641209'),
(32433, 'Arconada', 1157, 'P', 207, 'ES', 42.32774000, -4.49617000, '2019-10-05 22:45:35', '2022-08-29 11:45:44', 1, 'Q1641209'),
(32434, 'Arcones', 1192, 'SG', 207, 'ES', 41.11817000, -3.72384000, '2019-10-05 22:45:35', '2022-08-29 11:50:42', 1, 'Q1906360'),
(32435, 'Arcos', 1146, 'BU', 207, 'ES', 42.26664000, -3.75458000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1777841'),
(32436, 'Arcos de Jalón', 1208, 'SO', 207, 'ES', 41.21592000, -2.27470000, '2019-10-05 22:45:35', '2022-08-29 11:51:23', 1, 'Q830147'),
(32437, 'Arcos de la Frontera', 5096, 'CA', 207, 'ES', 36.75075000, -5.81056000, '2019-10-05 22:45:35', '2022-08-28 18:44:29', 1, 'Q637863'),
(32438, 'Arcos de la Polvorosa', 1161, 'ZA', 207, 'ES', 41.94389000, -5.69731000, '2019-10-05 22:45:35', '2022-08-29 11:48:03', 1, 'Q1650463'),
(32439, 'Arcos de la Sierra', 5106, 'CU', 207, 'ES', 40.34664000, -2.11310000, '2019-10-05 22:45:35', '2022-08-29 11:05:00', 1, 'Q1904595'),
(32440, 'Arcos de las Salinas', 5111, 'TE', 207, 'ES', 39.98333000, -1.03333000, '2019-10-05 22:45:35', '2022-08-29 11:29:43', 1, 'Q1904595'),
(32441, 'Ardales', 5101, 'MA', 207, 'ES', 36.87804000, -4.84694000, '2019-10-05 22:45:35', '2022-08-28 19:06:52', 1, 'Q1623994'),
(32442, 'Ardón', 1200, 'LE', 207, 'ES', 42.43629000, -5.56048000, '2019-10-05 22:45:35', '2020-05-01 17:23:16', 1, 'Q1623994'),
(32443, 'Arellano', 1204, 'NA', 207, 'ES', 42.60598000, -2.04653000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q608818'),
(32444, 'Arenas', 5101, 'MA', 207, 'ES', 36.81625000, -4.04411000, '2019-10-05 22:45:35', '2022-08-28 19:06:52', 1, 'Q608818'),
(32445, 'Arenas de Iguña', 1170, 'S', 207, 'ES', 43.18293000, -4.04729000, '2019-10-05 22:45:35', '2020-05-01 17:23:15', 1, 'Q782638'),
(32446, 'Arenas de San Juan', 5105, 'CR', 207, 'ES', 39.21861000, -3.50211000, '2019-10-05 22:45:35', '2022-08-29 11:03:24', 1, 'Q1631935'),
(32447, 'Arenas de San Pedro', 1189, 'AV', 207, 'ES', 40.21041000, -5.08694000, '2019-10-05 22:45:35', '2022-08-29 11:49:56', 1, 'Q641669'),
(32448, 'Arenas del Rey', 5098, 'GR', 207, 'ES', 36.95799000, -3.89362000, '2019-10-05 22:45:35', '2022-08-28 18:52:57', 1, 'Q550981'),
(32449, 'Arenillas', 1208, 'SO', 207, 'ES', 41.34706000, -2.84618000, '2019-10-05 22:45:35', '2022-08-29 11:51:23', 1, 'Q829608'),
(32450, 'Arenys de Lledó / Arens de Lledó', 1177, 'HU', 207, 'ES', 40.99244000, 0.27092000, '2019-10-05 22:45:35', '2022-08-29 12:06:20', 1, 'Q24015179'),
(32451, 'Arenys de Mar', 5102, 'B', 207, 'ES', 41.58190000, 2.54936000, '2019-10-05 22:45:35', '2022-08-29 10:50:00', 1, 'Q24015179'),
(32452, 'Arenys de Munt', 5102, 'B', 207, 'ES', 41.61424000, 2.53972000, '2019-10-05 22:45:35', '2022-08-29 10:50:00', 1, 'Q24015179'),
(32453, 'Arenzana de Abajo', 1171, 'LO', 207, 'ES', 42.38599000, -2.71940000, '2019-10-05 22:45:35', '2022-08-29 12:05:09', 1, 'Q1647666'),
(32454, 'Arenzana de Arriba', 1171, 'LO', 207, 'ES', 42.38745000, -2.69494000, '2019-10-05 22:45:35', '2022-08-29 12:05:09', 1, 'Q513637'),
(32455, 'Ares', 5089, 'C', 207, 'ES', 43.42995000, -8.24254000, '2019-10-05 22:45:35', '2022-08-28 13:37:16', 1, 'Q513637'),
(32456, 'Ares del Maestre', 1175, 'V', 207, 'ES', 40.45675000, -0.13267000, '2019-10-05 22:45:35', '2022-08-29 12:05:40', 1, 'Q785949'),
(32457, 'Areso', 1204, 'NA', 207, 'ES', 43.08209000, -1.95198000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1645791'),
(32458, 'Aretxabaleta', 1191, 'SS', 207, 'ES', 43.03414000, -2.50456000, '2019-10-05 22:45:35', '2022-08-28 18:36:49', 1, 'Q1645791'),
(32459, 'Arevalillo de Cega', 1192, 'SG', 207, 'ES', 41.16174000, -3.88911000, '2019-10-05 22:45:35', '2022-08-29 11:50:42', 1, 'Q1769709'),
(32460, 'Argamasilla de Alba', 5105, 'CR', 207, 'ES', 39.12917000, -3.09247000, '2019-10-05 22:45:35', '2022-08-29 11:03:24', 1, 'Q1632537'),
(32461, 'Argamasilla de Calatrava', 5105, 'CR', 207, 'ES', 38.72985000, -4.07627000, '2019-10-05 22:45:35', '2022-08-29 11:03:24', 1, 'Q1631995'),
(32462, 'Arganda', 1158, 'M', 207, 'ES', 40.30076000, -3.43722000, '2019-10-05 22:45:35', '2022-08-29 12:04:40', 1, 'Q1631995'),
(32463, 'Arganza', 1200, 'LE', 207, 'ES', 42.64077000, -6.68627000, '2019-10-05 22:45:35', '2019-10-05 22:45:35', 1, 'Q1631995'),
(32464, 'Arganzuela', 1158, 'M', 207, 'ES', 40.40021000, -3.69618000, '2019-10-05 22:45:35', '2022-08-29 12:04:40', 1, 'Q2000929'),
(32465, 'Argavieso', 1177, 'HU', 207, 'ES', 42.05275000, -0.27834000, '2019-10-05 22:45:35', '2022-08-29 12:06:20', 1, 'Q1985338'),
(32466, 'Argañín', 1161, 'ZA', 207, 'ES', 41.43948000, -6.20827000, '2019-10-05 22:45:35', '2022-08-29 11:48:03', 1, 'Q1650443'),
(32467, 'Argecilla', 5107, 'GU', 207, 'ES', 40.88151000, -2.82181000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q633895'),
(32468, 'Argelaguer', 5103, 'GI', 207, 'ES', 42.21563000, 2.64193000, '2019-10-05 22:45:35', '2022-08-29 10:53:16', 1, 'Q12582'),
(32469, 'Argelita', 5110, 'CS', 207, 'ES', 40.05000000, -0.35000000, '2019-10-05 22:45:35', '2022-08-29 11:26:42', 1, 'Q1763742'),
(32470, 'Argente', 5111, 'TE', 207, 'ES', 40.68841000, -1.16217000, '2019-10-05 22:45:35', '2022-08-29 11:29:43', 1, 'Q1763742'),
(32471, 'Argentona', 5102, 'B', 207, 'ES', 41.55336000, 2.40114000, '2019-10-05 22:45:35', '2022-08-29 10:50:00', 1, 'Q1763742'),
(32472, 'Argoños', 1170, 'S', 207, 'ES', 43.45740000, -3.49013000, '2019-10-05 22:45:35', '2020-05-01 17:23:15', 1, 'Q1443853'),
(32473, 'Arguedas', 1204, 'NA', 207, 'ES', 42.17759000, -1.59719000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1763705'),
(32474, 'Arguis', 1177, 'HU', 207, 'ES', 42.31476000, -0.43967000, '2019-10-05 22:45:35', '2022-08-29 12:06:20', 1, 'Q1772283'),
(32475, 'Arguisuelas', 5106, 'CU', 207, 'ES', 39.83333000, -1.81667000, '2019-10-05 22:45:35', '2022-08-29 11:05:00', 1, 'Q1983155'),
(32476, 'Argujillo', 1161, 'ZA', 207, 'ES', 41.31156000, -5.58763000, '2019-10-05 22:45:35', '2022-08-29 11:48:03', 1, 'Q1778072'),
(32477, 'Aria', 1204, 'NA', 207, 'ES', 42.95283000, -1.26584000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q1985331'),
(32478, 'Ariany', 1174, 'PM', 207, 'ES', 39.65048000, 3.11055000, '2019-10-05 22:45:35', '2019-10-05 22:45:35', 1, 'Q23987573'),
(32479, 'Arija', 1146, 'BU', 207, 'ES', 42.99350000, -3.94497000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1623158'),
(32480, 'Ariza', 5113, 'Z', 207, 'ES', 41.31442000, -2.05332000, '2019-10-05 22:45:35', '2022-08-29 11:42:53', 1, 'Q24002966'),
(32481, 'Ariño', 5111, 'TE', 207, 'ES', 41.03019000, -0.59206000, '2019-10-05 22:45:35', '2022-08-29 11:29:43', 1, 'Q1772274'),
(32482, 'Arjona', 5100, 'J', 207, 'ES', 37.93493000, -4.05478000, '2019-10-05 22:45:35', '2022-08-28 19:04:30', 1, 'Q1354411'),
(32483, 'Arjonilla', 5100, 'J', 207, 'ES', 37.97422000, -4.10677000, '2019-10-05 22:45:35', '2022-08-28 19:04:30', 1, 'Q1613295'),
(32484, 'Arlanzón', 1146, 'BU', 207, 'ES', 42.32267000, -3.45784000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q1614748'),
(32485, 'Armallones', 5107, 'GU', 207, 'ES', 40.73580000, -2.30257000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q633902'),
(32486, 'Armañanzas', 1204, 'NA', 207, 'ES', 42.55963000, -2.28476000, '2019-10-05 22:45:35', '2022-08-29 12:06:07', 1, 'Q614400'),
(32487, 'Armenteros', 1147, 'SA', 207, 'ES', 40.59320000, -5.44806000, '2019-10-05 22:45:35', '2022-08-29 11:44:50', 1, 'Q1651814'),
(32488, 'Armilla', 5098, 'GR', 207, 'ES', 37.14386000, -3.62534000, '2019-10-05 22:45:35', '2022-08-28 18:52:57', 1, 'Q550985'),
(32489, 'Armiñón', 5093, 'VI', 207, 'ES', 42.72313000, -2.87172000, '2019-10-05 22:45:35', '2022-08-28 18:23:49', 1, 'Q1113519'),
(32490, 'Armuña', 1192, 'SG', 207, 'ES', 41.07715000, -4.31949000, '2019-10-05 22:45:35', '2022-08-29 11:50:42', 1, 'Q2249210'),
(32491, 'Armuña de Almanzora', 5095, 'AL', 207, 'ES', 37.35030000, -2.41150000, '2019-10-05 22:45:35', '2022-08-28 18:41:41', 1, 'Q1358156'),
(32492, 'Armuña de Tajuña', 5107, 'GU', 207, 'ES', 40.52884000, -3.02819000, '2019-10-05 22:45:35', '2022-08-29 11:06:44', 1, 'Q1655124'),
(32493, 'Arnedillo', 1171, 'LO', 207, 'ES', 42.21221000, -2.23602000, '2019-10-05 22:45:35', '2022-08-29 12:05:09', 1, 'Q581814'),
(32494, 'Arnedo', 1171, 'LO', 207, 'ES', 42.22800000, -2.10083000, '2019-10-05 22:45:35', '2022-08-29 12:05:09', 1, 'Q581814'),
(32495, 'Arnuero', 1170, 'S', 207, 'ES', 43.47756000, -3.56959000, '2019-10-05 22:45:35', '2019-10-05 22:45:35', 1, 'Q1443906'),
(32496, 'Aroche', 5099, 'H', 207, 'ES', 37.94213000, -6.95760000, '2019-10-05 22:45:35', '2022-08-28 19:00:43', 1, 'Q1615179'),
(32497, 'Arona', 5112, 'TF', 207, 'ES', 28.09962000, -16.68102000, '2019-10-05 22:45:35', '2022-08-29 11:31:13', 1, 'Q5705046'),
(32498, 'Arquillinos', 1161, 'ZA', 207, 'ES', 41.70990000, -5.65708000, '2019-10-05 22:45:35', '2022-08-29 11:48:03', 1, 'Q1765247'),
(32499, 'Arquillos', 5100, 'J', 207, 'ES', 38.18148000, -3.42827000, '2019-10-05 22:45:35', '2022-08-28 19:04:30', 1, 'Q1611913'),
(32500, 'Arrabalde', 1161, 'ZA', 207, 'ES', 42.10760000, -5.89441000, '2019-10-05 22:45:35', '2022-08-29 11:48:03', 1, 'Q1650448'),
(32501, 'Arrancacepas', 5106, 'CU', 207, 'ES', 40.30320000, -2.35899000, '2019-10-05 22:45:35', '2022-08-29 11:05:00', 1, 'Q1648829'),
(32503, 'Arraya de Oca', 1146, 'BU', 207, 'ES', 42.41488000, -3.39781000, '2019-10-05 22:45:35', '2022-08-29 11:24:19', 1, 'Q384380'),
(32504, 'Arrecife', 1185, 'GC', 207, 'ES', 28.96302000, -13.54769000, '2019-10-05 22:45:35', '2022-08-29 12:06:32', 1, 'Q384380'),
(32505, 'Arredondo', 1170, 'S', 207, 'ES', 43.27325000, -3.60031000, '2019-10-05 22:45:35', '2019-10-05 22:45:35', 1, 'Q384380'),
(32506, 'Arriate', 5101, 'MA', 207, 'ES', 36.80005000, -5.14080000, '2019-10-05 22:45:35', '2022-08-28 19:06:52', 1, 'Q384380'),
(32507, 'Arrigorriaga', 5094, 'BI', 207, 'ES', 43.21006000, -2.88562000, '2019-10-05 22:45:35', '2022-08-28 18:25:55', 1, 'Q283607'),
(32508, 'Arroyo de San Serván', 5092, 'BA', 207, 'ES', 38.85443000, -6.45402000, '2019-10-05 22:45:35', '2022-08-28 18:09:22', 1, 'Q1113385'),
(32509, 'Arroyo de la Encomienda', 1183, 'VA', 207, 'ES', 41.60956000, -4.79692000, '2019-10-05 22:45:35', '2022-08-29 11:48:44', 1, 'Q1113385'),
(32510, 'Arroyo de la Luz', 1190, 'CC', 207, 'ES', 39.48511000, -6.58401000, '2019-10-05 22:45:35', '2022-08-28 18:12:22', 1, 'Q1630474'),
(32511, 'Arroyo de las Fraguas', 5107, 'GU', 207, 'ES', 41.10246000, -3.13005000, '2019-10-05 22:45:35', '2022-08-29 11:06:45', 1, 'Q1654996'),
(32512, 'Arroyo del Ojanco', 5100, 'J', 207, 'ES', 38.32065000, -2.89486000, '2019-10-05 22:45:35', '2022-08-28 19:04:30', 1, 'Q932362'),
(32513, 'Arroyomolinos', 1158, 'M', 207, 'ES', 40.26951000, -3.91946000, '2019-10-05 22:45:36', '2022-08-29 12:04:40', 1, 'Q1778172'),
(32514, 'Arroyomolinos de León', 5099, 'H', 207, 'ES', 38.01667000, -6.41667000, '2019-10-05 22:45:36', '2022-08-28 19:00:43', 1, 'Q1630500'),
(32515, 'Arroyomolinos de la Vera', 1190, 'CC', 207, 'ES', 40.05277000, -5.85111000, '2019-10-05 22:45:36', '2022-08-28 18:12:22', 1, 'Q1638607'),
(32516, 'Arruazu', 1204, 'NA', 207, 'ES', 42.92186000, -2.00157000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1645811'),
(32517, 'Arróniz', 1204, 'NA', 207, 'ES', 42.58823000, -2.09237000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1645816'),
(32518, 'Arrúbal', 1171, 'LO', 207, 'ES', 42.43485000, -2.25143000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q1647643'),
(32519, 'Artajona', 1204, 'NA', 207, 'ES', 42.58867000, -1.76504000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1645743'),
(32520, 'Artana', 5110, 'CS', 207, 'ES', 39.89104000, -0.25758000, '2019-10-05 22:45:36', '2022-08-29 11:26:42', 1, 'Q979628'),
(32521, 'Artazu', 1204, 'NA', 207, 'ES', 42.69228000, -1.83954000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1763735'),
(32522, 'Arteixo', 5089, 'C', 207, 'ES', 43.30482000, -8.50749000, '2019-10-05 22:45:36', '2022-08-28 13:37:16', 1, 'Q12383329'),
(32523, 'Artenara', 1185, 'GC', 207, 'ES', 28.02055000, -15.64693000, '2019-10-05 22:45:36', '2022-08-29 12:06:32', 1, 'Q12383329'),
(32524, 'Artesa de Segre', 5104, 'L', 207, 'ES', 41.89467000, 1.04625000, '2019-10-05 22:45:36', '2022-08-29 10:55:25', 1, 'Q969200'),
(32525, 'Artieda', 5113, 'Z', 207, 'ES', 42.58538000, -0.98422000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q712486'),
(32526, 'Artà', 1174, 'PM', 207, 'ES', 39.69315000, 3.34979000, '2019-10-05 22:45:36', '2020-05-01 17:23:15', 1, 'Q715970'),
(32527, 'Artés', 5102, 'B', 207, 'ES', 41.79800000, 1.95428000, '2019-10-05 22:45:36', '2022-08-29 10:50:00', 1, 'Q715970'),
(32528, 'Arucas', 1185, 'GC', 207, 'ES', 28.11983000, -15.52325000, '2019-10-05 22:45:36', '2022-08-29 12:06:32', 1, 'Q715970'),
(32529, 'Arzúa', 5089, 'C', 207, 'ES', 42.93333000, -8.15000000, '2019-10-05 22:45:36', '2022-08-28 13:37:16', 1, 'Q715970'),
(32530, 'Arándiga', 5113, 'Z', 207, 'ES', 41.50872000, -1.50074000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q24016492'),
(32531, 'Arévalo', 1189, 'AV', 207, 'ES', 41.06255000, -4.72042000, '2019-10-05 22:45:36', '2022-08-29 11:49:56', 1, 'Q476793'),
(32532, 'Arévalo de la Sierra', 1208, 'SO', 207, 'ES', 41.94723000, -2.40033000, '2019-10-05 22:45:36', '2022-08-29 11:51:23', 1, 'Q831144'),
(32533, 'As Pontes de García Rodríguez', 5089, 'C', 207, 'ES', 43.45266000, -7.85178000, '2019-10-05 22:45:36', '2022-08-28 18:01:23', 1, 'Q1442761'),
(32534, 'Ascó', 1203, 'T', 207, 'ES', 41.18333000, 0.56667000, '2019-10-05 22:45:36', '2022-08-29 10:57:32', 1, 'Q1442761'),
(32535, 'Aspa', 5104, 'L', 207, 'ES', 41.49472000, 0.67277000, '2019-10-05 22:45:36', '2022-08-29 10:55:25', 1, 'Q1770861'),
(32536, 'Aspariegos', 1161, 'ZA', 207, 'ES', 41.67458000, -5.59955000, '2019-10-05 22:45:36', '2022-08-29 11:48:03', 1, 'Q1766506'),
(32537, 'Aspe', 5108, 'A', 207, 'ES', 38.34511000, -0.76721000, '2019-10-05 22:45:36', '2022-08-29 11:15:47', 1, 'Q1770153'),
(32538, 'Asteasu', 1191, 'SS', 207, 'ES', 43.19436000, -2.09818000, '2019-10-05 22:45:36', '2022-08-28 18:36:49', 1, 'Q1619436'),
(32539, 'Astigarraga', 1191, 'SS', 207, 'ES', 43.28174000, -1.94634000, '2019-10-05 22:45:36', '2022-08-28 18:36:50', 1, 'Q1639355'),
(32540, 'Astorga', 1200, 'LE', 207, 'ES', 42.45879000, -6.05601000, '2019-10-05 22:45:36', '2019-10-05 22:45:36', 1, 'Q1639355'),
(32541, 'Astudillo', 1157, 'P', 207, 'ES', 42.19330000, -4.29394000, '2019-10-05 22:45:36', '2022-08-29 11:45:44', 1, 'Q1639355'),
(32542, 'Asturianos', 1161, 'ZA', 207, 'ES', 42.05201000, -6.48940000, '2019-10-05 22:45:36', '2022-08-29 11:48:03', 1, 'Q1778061'),
(32543, 'Atajate', 5101, 'MA', 207, 'ES', 36.64017000, -5.24606000, '2019-10-05 22:45:36', '2022-08-28 19:06:52', 1, 'Q540447'),
(32544, 'Atalaya', 5092, 'BA', 207, 'ES', 38.33333000, -6.46667000, '2019-10-05 22:45:36', '2022-08-28 18:09:22', 1, 'Q1620361'),
(32545, 'Atalaya del Cañavate', 5106, 'CU', 207, 'ES', 39.51864000, -2.25175000, '2019-10-05 22:45:36', '2022-08-29 11:05:00', 1, 'Q1769788'),
(32546, 'Atamaría', 1176, 'MU', 207, 'ES', 37.59989000, -0.80682000, '2019-10-05 22:45:36', '2022-08-29 12:05:49', 1, 'Q31912792'),
(32547, 'Atanzón', 5107, 'GU', 207, 'ES', 40.66697000, -2.99686000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q1656217'),
(32548, 'Atapuerca', 1146, 'BU', 207, 'ES', 42.37757000, -3.50790000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q753459'),
(32549, 'Ataquines', 1183, 'VA', 207, 'ES', 41.18247000, -4.80319000, '2019-10-05 22:45:36', '2022-08-29 11:48:44', 1, 'Q1907000'),
(32550, 'Atarfe', 5098, 'GR', 207, 'ES', 37.22479000, -3.68686000, '2019-10-05 22:45:36', '2022-08-28 18:52:57', 1, 'Q550974'),
(32551, 'Atarrabia', 1204, 'NA', 207, 'ES', 42.83230000, -1.60735000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q600671'),
(32552, 'Ataun', 1191, 'SS', 207, 'ES', 43.00612000, -2.17663000, '2019-10-05 22:45:36', '2022-08-28 18:36:49', 1, 'Q1619442'),
(32553, 'Atea', 5113, 'Z', 207, 'ES', 41.16059000, -1.55533000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q1771909'),
(32554, 'Ateca', 5113, 'Z', 207, 'ES', 41.33092000, -1.79353000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q24016487'),
(32555, 'Atienza', 5107, 'GU', 207, 'ES', 41.19722000, -2.87129000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q24011256'),
(32556, 'Atzeneta d\'Albaida', 1175, 'V', 207, 'ES', 38.83333000, -0.50000000, '2019-10-05 22:45:36', '2022-08-29 12:05:40', 1, 'Q24011256'),
(32557, 'Aulesti', 5094, 'BI', 207, 'ES', 43.30000000, -2.56667000, '2019-10-05 22:45:36', '2022-08-28 18:25:56', 1, 'Q4472'),
(32558, 'Ausejo', 1171, 'LO', 207, 'ES', 42.34210000, -2.16710000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q1637007'),
(32559, 'Ausejo de la Sierra', 1208, 'SO', 207, 'ES', 41.89462000, -2.37394000, '2019-10-05 22:45:36', '2022-08-29 11:51:23', 1, 'Q830668'),
(32560, 'Autilla del Pino', 1157, 'P', 207, 'ES', 41.99207000, -4.63393000, '2019-10-05 22:45:36', '2022-08-29 11:45:44', 1, 'Q24012241'),
(32561, 'Autillo de Campos', 1157, 'P', 207, 'ES', 42.08725000, -4.83376000, '2019-10-05 22:45:36', '2022-08-29 11:45:44', 1, 'Q24012240'),
(32562, 'Autol', 1171, 'LO', 207, 'ES', 42.21661000, -2.00525000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q787022'),
(32563, 'Auñón', 5107, 'GU', 207, 'ES', 40.51752000, -2.79250000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q425740'),
(32564, 'Aveinte', 1189, 'AV', 207, 'ES', 40.78227000, -4.83649000, '2019-10-05 22:45:36', '2022-08-29 11:49:56', 1, 'Q1445425'),
(32565, 'Avellaneda', 1189, 'AV', 207, 'ES', 40.38909000, -5.38811000, '2019-10-05 22:45:36', '2022-08-29 11:49:56', 1, 'Q1445425'),
(32567, 'Avellanosa de Muñó', 1146, 'BU', 207, 'ES', 41.98349000, -3.82553000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q610874'),
(32568, 'Avinyó', 5102, 'B', 207, 'ES', 41.86367000, 1.97095000, '2019-10-05 22:45:36', '2022-08-29 10:50:00', 1, 'Q16678'),
(32569, 'Avión', 5091, 'OR', 207, 'ES', 42.38333000, -8.25000000, '2019-10-05 22:45:36', '2022-08-28 17:53:26', 1, 'Q791209'),
(32571, 'Ayamonte', 5099, 'H', 207, 'ES', 37.21329000, -7.40807000, '2019-10-05 22:45:36', '2022-08-28 19:00:43', 1, 'Q736872'),
(32572, 'Ayegui', 1204, 'NA', 207, 'ES', 42.65656000, -2.03934000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q792921'),
(32573, 'Ayerbe', 1177, 'HU', 207, 'ES', 42.27267000, -0.68844000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q189749'),
(32574, 'Ayllón', 1192, 'SG', 207, 'ES', 41.41899000, -3.37537000, '2019-10-05 22:45:36', '2022-08-29 11:50:42', 1, 'Q189749'),
(32576, 'Ayora', 1175, 'V', 207, 'ES', 39.05852000, -1.05635000, '2019-10-05 22:45:36', '2022-08-29 12:05:40', 1, 'Q34343'),
(32577, 'Ayoó de Vidriales', 1161, 'ZA', 207, 'ES', 42.13042000, -6.06550000, '2019-10-05 22:45:36', '2022-08-29 11:48:03', 1, 'Q1765291'),
(32578, 'Ayuela', 1157, 'P', 207, 'ES', 42.62618000, -4.65979000, '2019-10-05 22:45:36', '2022-08-29 11:45:44', 1, 'Q1647078'),
(32579, 'Ayódar', 5110, 'CS', 207, 'ES', 40.00000000, -0.36667000, '2019-10-05 22:45:36', '2022-08-29 11:26:42', 1, 'Q1753077'),
(32580, 'Azagra', 1204, 'NA', 207, 'ES', 42.30000000, -1.90000000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1757486'),
(32581, 'Azara', 1177, 'HU', 207, 'ES', 42.07033000, -0.02921000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q1991028'),
(32582, 'Azkoitia', 1191, 'SS', 207, 'ES', 43.17744000, -2.31129000, '2019-10-05 22:45:36', '2022-08-28 18:36:49', 1, 'Q1991028'),
(32583, 'Azlor', 1177, 'HU', 207, 'ES', 42.09427000, -0.04594000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q1651304'),
(32584, 'Aznalcázar', 1193, 'SE', 207, 'ES', 37.30422000, -6.24963000, '2019-10-05 22:45:36', '2022-08-28 19:08:49', 1, 'Q926144'),
(32585, 'Aznalcóllar', 1193, 'SE', 207, 'ES', 37.51914000, -6.26988000, '2019-10-05 22:45:36', '2022-08-28 19:08:49', 1, 'Q1601679'),
(32586, 'Azofra', 1171, 'LO', 207, 'ES', 42.42400000, -2.80086000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q661720'),
(32587, 'Azpeitia', 1191, 'SS', 207, 'ES', 43.18246000, -2.26693000, '2019-10-05 22:45:36', '2022-08-28 18:36:49', 1, 'Q180611'),
(32588, 'Azuaga', 5092, 'BA', 207, 'ES', 38.26667000, -5.68333000, '2019-10-05 22:45:36', '2022-08-28 18:09:22', 1, 'Q180611'),
(32589, 'Azuara', 5113, 'Z', 207, 'ES', 41.25822000, -0.87078000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q794238'),
(32590, 'Azuelo', 1204, 'NA', 207, 'ES', 42.60844000, -2.34987000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1645770'),
(32591, 'Azuqueca de Henares', 5107, 'GU', 207, 'ES', 40.56566000, -3.26753000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q794315'),
(32592, 'Azután', 1205, 'TO', 207, 'ES', 39.78564000, -5.12730000, '2019-10-05 22:45:36', '2022-08-29 11:08:29', 1, 'Q944625'),
(32593, 'Azuébar', 5110, 'CS', 207, 'ES', 39.83333000, -0.36667000, '2019-10-05 22:45:36', '2022-08-29 11:26:42', 1, 'Q1753047'),
(32594, 'Aínsa', 1177, 'HU', 207, 'ES', 42.41547000, 0.14008000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q13049257'),
(32595, 'Añe', 1192, 'SG', 207, 'ES', 41.03702000, -4.29462000, '2019-10-05 22:45:36', '2022-08-29 11:50:42', 1, 'Q1776963'),
(32596, 'Añora', 5097, 'CO', 207, 'ES', 38.41667000, -4.90000000, '2019-10-05 22:45:36', '2022-08-28 18:49:38', 1, 'Q854390'),
(32597, 'Añorbe', 1204, 'NA', 207, 'ES', 42.65732000, -1.71490000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1763701'),
(32598, 'Añover de Tajo', 1205, 'TO', 207, 'ES', 39.98873000, -3.76579000, '2019-10-05 22:45:36', '2022-08-29 11:08:29', 1, 'Q607669'),
(32599, 'Añover de Tormes', 1147, 'SA', 207, 'ES', 41.13658000, -5.91585000, '2019-10-05 22:45:36', '2022-08-29 11:44:50', 1, 'Q1648537'),
(32600, 'Babilafuente', 1147, 'SA', 207, 'ES', 40.97704000, -5.42554000, '2019-10-05 22:45:36', '2022-08-29 11:44:50', 1, 'Q1606563'),
(32601, 'Badajoz', 5092, 'BA', 207, 'ES', 38.87789000, -6.97061000, '2019-10-05 22:45:36', '2022-08-28 18:09:22', 1, 'Q15679'),
(32602, 'Badalona', 5102, 'B', 207, 'ES', 41.45004000, 2.24741000, '2019-10-05 22:45:36', '2022-08-29 10:50:00', 1, 'Q15679'),
(32603, 'Badarán', 1171, 'LO', 207, 'ES', 42.36794000, -2.81079000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q1647682'),
(32604, 'Badolatosa', 1193, 'SE', 207, 'ES', 37.30785000, -4.67296000, '2019-10-05 22:45:36', '2022-08-28 19:08:49', 1, 'Q1450719'),
(32605, 'Badules', 5113, 'Z', 207, 'ES', 41.13901000, -1.25366000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q1639646'),
(32606, 'Baells', 1177, 'HU', 207, 'ES', 41.95325000, 0.45956000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q812714'),
(32607, 'Baena', 5097, 'CO', 207, 'ES', 37.61670000, -4.32245000, '2019-10-05 22:45:36', '2022-08-28 18:49:38', 1, 'Q854403'),
(32608, 'Baeza', 5100, 'J', 207, 'ES', 37.99384000, -3.47103000, '2019-10-05 22:45:36', '2022-08-28 19:04:30', 1, 'Q854403'),
(32609, 'Bagà', 5102, 'B', 207, 'ES', 42.25289000, 1.86098000, '2019-10-05 22:45:36', '2022-08-29 10:50:00', 1, 'Q854403'),
(32610, 'Bagüés', 5113, 'Z', 207, 'ES', 42.54935000, -0.94577000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q1641134'),
(32611, 'Bahabón', 1183, 'VA', 207, 'ES', 41.48141000, -4.27941000, '2019-10-05 22:45:36', '2022-08-29 11:48:44', 1, 'Q1769701'),
(32612, 'Bahabón de Esgueva', 1146, 'BU', 207, 'ES', 41.86162000, -3.72980000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q1447967'),
(32613, 'Baides', 5107, 'GU', 207, 'ES', 41.00685000, -2.77620000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q601029'),
(32614, 'Bailo', 1177, 'HU', 207, 'ES', 42.50906000, -0.81136000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q1635407'),
(32615, 'Bailén', 5100, 'J', 207, 'ES', 38.09639000, -3.77786000, '2019-10-05 22:45:36', '2022-08-28 19:04:30', 1, 'Q695456'),
(32616, 'Baiona', 1167, 'PO', 207, 'ES', 42.11667000, -8.85000000, '2019-10-05 22:45:36', '2022-08-28 17:57:54', 1, 'Q695456'),
(32617, 'Bakaiku', 1204, 'NA', 207, 'ES', 42.89244000, -2.10251000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1754154'),
(32618, 'Bakio', 5094, 'BI', 207, 'ES', 43.42917000, -2.80881000, '2019-10-05 22:45:36', '2022-08-28 18:25:55', 1, 'Q804466'),
(32619, 'Balaguer', 5104, 'L', 207, 'ES', 41.79117000, 0.81094000, '2019-10-05 22:45:36', '2022-08-29 10:55:25', 1, 'Q804466'),
(32621, 'Balboa', 1200, 'LE', 207, 'ES', 42.70608000, -6.92222000, '2019-10-05 22:45:36', '2019-10-05 22:45:36', 1, 'Q804466'),
(32622, 'Balconchán', 5113, 'Z', 207, 'ES', 41.08737000, -1.45998000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q1639592'),
(32623, 'Baliarrain', 1191, 'SS', 207, 'ES', 43.06928000, -2.12781000, '2019-10-05 22:45:36', '2022-08-28 18:36:50', 1, 'Q385872'),
(32624, 'Ballesteros de Calatrava', 5105, 'CR', 207, 'ES', 38.83460000, -3.94470000, '2019-10-05 22:45:36', '2022-08-29 11:03:24', 1, 'Q953205'),
(32625, 'Ballobar', 1177, 'HU', 207, 'ES', 41.62106000, 0.19200000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q805306'),
(32626, 'Balones', 5108, 'A', 207, 'ES', 38.73726000, -0.34324000, '2019-10-05 22:45:36', '2022-08-29 11:15:47', 1, 'Q1770144'),
(32628, 'Balsareny', 5102, 'B', 207, 'ES', 41.86311000, 1.87356000, '2019-10-05 22:45:36', '2022-08-29 10:50:00', 1, 'Q16679'),
(32629, 'Baltanás', 1157, 'P', 207, 'ES', 41.93775000, -4.24656000, '2019-10-05 22:45:36', '2022-08-29 11:45:44', 1, 'Q1646404'),
(32630, 'Baltar', 5091, 'OR', 207, 'ES', 41.95200000, -7.71581000, '2019-10-05 22:45:36', '2022-08-28 17:53:26', 1, 'Q1646404'),
(32631, 'Banastás', 1177, 'HU', 207, 'ES', 42.18089000, -0.45191000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q2047646'),
(32632, 'Bande', 5091, 'OR', 207, 'ES', 42.03120000, -7.97489000, '2019-10-05 22:45:36', '2022-08-28 17:53:26', 1, 'Q2047646'),
(32633, 'Banyalbufar', 1174, 'PM', 207, 'ES', 39.68734000, 2.51409000, '2019-10-05 22:45:36', '2019-10-05 22:45:36', 1, 'Q616408'),
(32634, 'Banyoles', 5103, 'GI', 207, 'ES', 42.11667000, 2.76667000, '2019-10-05 22:45:36', '2022-08-29 10:53:16', 1, 'Q616408'),
(32635, 'Baquerín de Campos', 1157, 'P', 207, 'ES', 42.01600000, -4.78185000, '2019-10-05 22:45:36', '2022-08-29 11:45:44', 1, 'Q1778120'),
(32636, 'Barajas de Madrid', 1158, 'M', 207, 'ES', 40.47366000, -3.57777000, '2019-10-05 22:45:36', '2022-08-29 12:04:40', 1, 'Q807230'),
(32637, 'Barajas de Melo', 5106, 'CU', 207, 'ES', 40.12340000, -2.91675000, '2019-10-05 22:45:36', '2022-08-29 11:05:00', 1, 'Q1904541'),
(32638, 'Barakaldo', 5094, 'BI', 207, 'ES', 43.29639000, -2.98813000, '2019-10-05 22:45:36', '2022-08-28 18:25:55', 1, 'Q1904541'),
(32639, 'Baralla', 5090, 'LU', 207, 'ES', 42.89207000, -7.25492000, '2019-10-05 22:45:36', '2022-08-28 17:49:37', 1, 'Q1633465'),
(32640, 'Barañáin', 1204, 'NA', 207, 'ES', 42.80567000, -1.67731000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q768691'),
(32641, 'Barbadillo de Herreros', 1146, 'BU', 207, 'ES', 42.14974000, -3.17702000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q1614687'),
(32642, 'Barbadillo del Mercado', 1146, 'BU', 207, 'ES', 42.03858000, -3.35669000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q1614740'),
(32643, 'Barbadillo del Pez', 1146, 'BU', 207, 'ES', 42.11879000, -3.22803000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q1447953'),
(32644, 'Barbalos', 1147, 'SA', 207, 'ES', 40.67678000, -5.94258000, '2019-10-05 22:45:36', '2022-08-29 11:44:50', 1, 'Q919413'),
(32645, 'Barbastro', 1177, 'HU', 207, 'ES', 42.03565000, 0.12686000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q502975'),
(32646, 'Barbate', 5096, 'CA', 207, 'ES', 36.19237000, -5.92186000, '2019-10-05 22:45:36', '2022-08-28 18:44:29', 1, 'Q502975'),
(32647, 'Barberà del Vallès', 5102, 'B', 207, 'ES', 41.51590000, 2.12457000, '2019-10-05 22:45:36', '2022-08-29 10:50:01', 1, 'Q23993262'),
(32648, 'Barbolla', 1192, 'SG', 207, 'ES', 41.32567000, -3.67361000, '2019-10-05 22:45:36', '2022-08-29 11:50:42', 1, 'Q1776911'),
(32649, 'Barbués', 1177, 'HU', 207, 'ES', 41.98085000, -0.41949000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q775912'),
(32650, 'Barbuñales', 1177, 'HU', 207, 'ES', 42.02563000, -0.08659000, '2019-10-05 22:45:36', '2022-08-29 12:06:20', 1, 'Q578162'),
(32651, 'Barca', 1208, 'SO', 207, 'ES', 41.45490000, -2.62217000, '2019-10-05 22:45:36', '2022-08-29 11:51:23', 1, 'Q578162'),
(32652, 'Barcarrota', 5092, 'BA', 207, 'ES', 38.51473000, -6.84923000, '2019-10-05 22:45:36', '2022-08-28 18:09:23', 1, 'Q534483'),
(32653, 'Barcelona', 5102, 'B', 207, 'ES', 41.38879000, 2.15899000, '2019-10-05 22:45:36', '2022-08-29 10:50:00', 1, 'Q1492'),
(32654, 'Barceo', 1147, 'SA', 207, 'ES', 41.06108000, -6.45175000, '2019-10-05 22:45:36', '2022-08-29 11:44:50', 1, 'Q1628733'),
(32655, 'Barchín del Hoyo', 5106, 'CU', 207, 'ES', 39.66667000, -2.06667000, '2019-10-05 22:45:36', '2022-08-29 11:05:00', 1, 'Q1903971'),
(32656, 'Barcial de la Loma', 1183, 'VA', 207, 'ES', 41.95139000, -5.28353000, '2019-10-05 22:45:36', '2022-08-29 11:48:44', 1, 'Q1921091'),
(32657, 'Barcial del Barco', 1161, 'ZA', 207, 'ES', 41.93353000, -5.66268000, '2019-10-05 22:45:36', '2022-08-29 11:48:03', 1, 'Q1766466'),
(32658, 'Barcience', 1205, 'TO', 207, 'ES', 39.98367000, -4.23437000, '2019-10-05 22:45:36', '2022-08-29 11:08:29', 1, 'Q1641347'),
(32659, 'Barcones', 1208, 'SO', 207, 'ES', 41.29146000, -2.81630000, '2019-10-05 22:45:36', '2022-08-29 11:51:23', 1, 'Q830318'),
(32660, 'Bardallur', 5113, 'Z', 207, 'ES', 41.68423000, -1.21183000, '2019-10-05 22:45:36', '2022-08-29 11:42:53', 1, 'Q1640935'),
(32661, 'Bareyo', 1170, 'S', 207, 'ES', 43.46686000, -3.60083000, '2019-10-05 22:45:36', '2019-10-05 22:45:36', 1, 'Q1443992'),
(32662, 'Bargas', 1205, 'TO', 207, 'ES', 39.94113000, -4.01979000, '2019-10-05 22:45:36', '2022-08-29 11:08:29', 1, 'Q1628628'),
(32663, 'Bargota', 1204, 'NA', 207, 'ES', 42.55982000, -2.31067000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q24012496'),
(32664, 'Barillas', 1204, 'NA', 207, 'ES', 41.96667000, -1.63333000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1645760'),
(32665, 'Barjas', 1200, 'LE', 207, 'ES', 42.61182000, -6.97979000, '2019-10-05 22:45:36', '2019-10-05 22:45:36', 1, 'Q1645760'),
(32666, 'Barlovento', 5112, 'TF', 207, 'ES', 28.82708000, -17.80377000, '2019-10-05 22:45:36', '2022-08-29 11:31:13', 1, 'Q1645760'),
(32667, 'Barracas', 5110, 'CS', 207, 'ES', 40.01667000, -0.68333000, '2019-10-05 22:45:36', '2022-08-29 11:26:42', 1, 'Q1645760'),
(32668, 'Barrachina', 5111, 'TE', 207, 'ES', 40.89688000, -1.13858000, '2019-10-05 22:45:36', '2022-08-29 11:29:43', 1, 'Q1653300'),
(32669, 'Barraco', 1200, 'LE', 207, 'ES', 40.47647000, -4.64346000, '2019-10-05 22:45:36', '2019-10-05 22:45:36', 1, 'Q1653300'),
(32670, 'Barrado', 1190, 'CC', 207, 'ES', 40.08477000, -5.88068000, '2019-10-05 22:45:36', '2022-08-28 18:12:22', 1, 'Q808812'),
(32672, 'Barreiros', 5090, 'LU', 207, 'ES', 43.53321000, -7.23342000, '2019-10-05 22:45:36', '2022-08-28 17:49:36', 1, 'Q1634820'),
(32676, 'Barrio de Muñó', 1146, 'BU', 207, 'ES', 42.17584000, -4.00715000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q628122'),
(32677, 'Barrio de la Concepción', 1176, 'MU', 207, 'ES', 37.60000000, -1.00000000, '2019-10-05 22:45:36', '2022-08-29 12:05:49', 1, 'Q628122'),
(32678, 'Barriopedro', 5107, 'GU', 207, 'ES', 40.79026000, -2.75246000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q1655045'),
(32679, 'Barrios de Colina', 1146, 'BU', 207, 'ES', 42.39595000, -3.46001000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q1448007'),
(32680, 'Barromán', 1189, 'AV', 207, 'ES', 41.06536000, -4.93066000, '2019-10-05 22:45:36', '2022-08-29 11:49:56', 1, 'Q1606213'),
(32681, 'Barruecopardo', 1147, 'SA', 207, 'ES', 41.07215000, -6.66423000, '2019-10-05 22:45:36', '2022-08-29 11:44:50', 1, 'Q1640512'),
(32682, 'Barruelo de Santullán', 1157, 'P', 207, 'ES', 42.90641000, -4.28593000, '2019-10-05 22:45:36', '2022-08-29 11:45:44', 1, 'Q1776891'),
(32683, 'Barx', 1175, 'V', 207, 'ES', 39.01667000, -0.30000000, '2019-10-05 22:45:36', '2022-08-29 12:05:40', 1, 'Q1776891'),
(32684, 'Barxeta', 1175, 'V', 207, 'ES', 39.01667000, -0.41667000, '2019-10-05 22:45:36', '2022-08-29 12:05:40', 1, 'Q1776891'),
(32685, 'Barásoain', 1204, 'NA', 207, 'ES', 42.60339000, -1.64658000, '2019-10-05 22:45:36', '2022-08-29 12:06:07', 1, 'Q1776891'),
(32687, 'Basardilla', 1192, 'SG', 207, 'ES', 41.02737000, -4.02598000, '2019-10-05 22:45:36', '2022-08-29 11:50:42', 1, 'Q1906443'),
(32688, 'Basauri', 5094, 'BI', 207, 'ES', 43.23970000, -2.88580000, '2019-10-05 22:45:36', '2022-08-28 18:25:55', 1, 'Q765496'),
(32689, 'Basconcillos del Tozo', 1146, 'BU', 207, 'ES', 42.70236000, -3.98937000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q648723'),
(32690, 'Bascuñana', 1146, 'BU', 207, 'ES', 42.42548000, -3.08231000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q648723'),
(32691, 'Bascuñana de San Pedro', 5106, 'CU', 207, 'ES', 40.21322000, -2.22812000, '2019-10-05 22:45:36', '2022-08-29 11:05:00', 1, 'Q1769850'),
(32693, 'Batea', 1203, 'T', 207, 'ES', 41.09434000, 0.31100000, '2019-10-05 22:45:36', '2022-08-29 10:57:32', 1, 'Q1155351'),
(32694, 'Baterno', 5092, 'BA', 207, 'ES', 38.95578000, -4.91039000, '2019-10-05 22:45:36', '2022-08-28 18:09:23', 1, 'Q1372568'),
(32695, 'Batres', 1158, 'M', 207, 'ES', 40.20981000, -3.92331000, '2019-10-05 22:45:36', '2022-08-29 12:04:40', 1, 'Q1647095'),
(32696, 'Bayarque', 5095, 'AL', 207, 'ES', 37.33062000, -2.43610000, '2019-10-05 22:45:36', '2022-08-28 18:41:41', 1, 'Q1647095'),
(32697, 'Bayubas de Abajo', 1208, 'SO', 207, 'ES', 41.52747000, -2.89586000, '2019-10-05 22:45:36', '2022-08-29 11:51:23', 1, 'Q832324'),
(32698, 'Bayubas de Arriba', 1208, 'SO', 207, 'ES', 41.55750000, -2.88731000, '2019-10-05 22:45:36', '2022-08-29 11:51:23', 1, 'Q831918'),
(32699, 'Bayárcal', 5095, 'AL', 207, 'ES', 37.03073000, -2.99606000, '2019-10-05 22:45:36', '2022-08-28 18:41:41', 1, 'Q23987748'),
(32700, 'Baza', 5098, 'GR', 207, 'ES', 37.49073000, -2.77259000, '2019-10-05 22:45:36', '2022-08-28 18:52:57', 1, 'Q23987748'),
(32701, 'Bañares', 1171, 'LO', 207, 'ES', 42.46838000, -2.91010000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q1647575'),
(32702, 'Bañobárez', 1147, 'SA', 207, 'ES', 40.84825000, -6.61311000, '2019-10-05 22:45:36', '2022-08-29 11:44:50', 1, 'Q1640483'),
(32703, 'Baños de Molgas', 5091, 'OR', 207, 'ES', 42.24148000, -7.67223000, '2019-10-05 22:45:36', '2022-08-28 17:53:26', 1, 'Q1632922'),
(32704, 'Baños de Montemayor', 1190, 'CC', 207, 'ES', 40.31671000, -5.86009000, '2019-10-05 22:45:36', '2022-08-28 18:12:22', 1, 'Q1646296'),
(32705, 'Baños de Rioja', 1171, 'LO', 207, 'ES', 42.51230000, -2.94635000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q1772306'),
(32706, 'Baños de Río Tobía', 1171, 'LO', 207, 'ES', 42.33533000, -2.76099000, '2019-10-05 22:45:36', '2022-08-29 12:05:09', 1, 'Q1626938'),
(32707, 'Baños de Tajo', 5107, 'GU', 207, 'ES', 40.71667000, -1.96667000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q1655314'),
(32708, 'Baños de Valdearados', 1146, 'BU', 207, 'ES', 41.77010000, -3.55589000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q929461'),
(32709, 'Baños de la Encina', 5100, 'J', 207, 'ES', 38.17379000, -3.77477000, '2019-10-05 22:45:36', '2022-08-28 19:04:30', 1, 'Q587590'),
(32710, 'Bañuelos', 5107, 'GU', 207, 'ES', 41.28601000, -2.91477000, '2019-10-05 22:45:36', '2022-08-29 11:06:45', 1, 'Q1655094'),
(32711, 'Bañuelos de Bureba', 1146, 'BU', 207, 'ES', 42.50258000, -3.27957000, '2019-10-05 22:45:36', '2022-08-29 11:24:19', 1, 'Q285235'),
(32712, 'Bañón', 5111, 'TE', 207, 'ES', 40.83825000, -1.19091000, '2019-10-05 22:45:36', '2022-08-29 11:29:43', 1, 'Q784010'),
(32713, 'Bea', 5111, 'TE', 207, 'ES', 41.03633000, -1.14725000, '2019-10-05 22:45:36', '2022-08-29 11:29:43', 1, 'Q784010'),
(32714, 'Beade', 5091, 'OR', 207, 'ES', 42.33074000, -8.12950000, '2019-10-05 22:45:37', '2022-08-28 17:53:26', 1, 'Q784010'),
(32715, 'Beamud', 5106, 'CU', 207, 'ES', 40.18645000, -1.82880000, '2019-10-05 22:45:37', '2022-08-29 11:05:00', 1, 'Q1770946'),
(32716, 'Beas', 5099, 'H', 207, 'ES', 37.42570000, -6.79318000, '2019-10-05 22:45:37', '2022-08-28 19:00:43', 1, 'Q1635967'),
(32717, 'Beas de Granada', 5098, 'GR', 207, 'ES', 37.21803000, -3.48095000, '2019-10-05 22:45:37', '2022-08-28 18:52:57', 1, 'Q550942'),
(32718, 'Beas de Guadix', 5098, 'GR', 207, 'ES', 37.27861000, -3.20579000, '2019-10-05 22:45:37', '2022-08-28 18:52:57', 1, 'Q550952'),
(32719, 'Beas de Segura', 5100, 'J', 207, 'ES', 38.25240000, -2.88875000, '2019-10-05 22:45:37', '2022-08-28 19:04:30', 1, 'Q1313394'),
(32720, 'Beasain', 1191, 'SS', 207, 'ES', 43.05017000, -2.20087000, '2019-10-05 22:45:37', '2022-08-28 18:36:49', 1, 'Q847130'),
(32721, 'Becedas', 1189, 'AV', 207, 'ES', 40.40369000, -5.63577000, '2019-10-05 22:45:37', '2022-08-29 11:49:56', 1, 'Q512237'),
(32722, 'Becedillas', 1189, 'AV', 207, 'ES', 40.53821000, -5.32561000, '2019-10-05 22:45:37', '2022-08-29 11:49:56', 1, 'Q1606676'),
(32723, 'Becerreá', 5090, 'LU', 207, 'ES', 42.85610000, -7.16360000, '2019-10-05 22:45:37', '2022-08-28 17:49:36', 1, 'Q1596031'),
(32724, 'Becerril de Campos', 1157, 'P', 207, 'ES', 42.10841000, -4.64152000, '2019-10-05 22:45:37', '2022-08-29 11:45:44', 1, 'Q1906940'),
(32725, 'Becerril de la Sierra', 1158, 'M', 207, 'ES', 40.71699000, -3.98858000, '2019-10-05 22:45:37', '2022-08-29 12:04:40', 1, 'Q655675'),
(32726, 'Becilla de Valderaduey', 1183, 'VA', 207, 'ES', 42.09905000, -5.21805000, '2019-10-05 22:45:37', '2022-08-29 11:48:44', 1, 'Q655675'),
(32727, 'Begonte', 5090, 'LU', 207, 'ES', 43.15121000, -7.68643000, '2019-10-05 22:45:37', '2022-08-28 17:49:36', 1, 'Q959803'),
(32728, 'Begues', 5102, 'B', 207, 'ES', 41.33333000, 1.93333000, '2019-10-05 22:45:37', '2022-08-29 10:50:00', 1, 'Q959803'),
(32729, 'Begur', 5103, 'GI', 207, 'ES', 41.95000000, 3.21667000, '2019-10-05 22:45:37', '2022-08-29 10:53:16', 1, 'Q24022119'),
(32730, 'Begíjar', 5100, 'J', 207, 'ES', 37.98492000, -3.53094000, '2019-10-05 22:45:37', '2022-08-28 19:04:30', 1, 'Q383763'),
(32731, 'Beire', 1204, 'NA', 207, 'ES', 42.45415000, -1.62101000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q814864'),
(32732, 'Beires', 5095, 'AL', 207, 'ES', 37.01237000, -2.79134000, '2019-10-05 22:45:37', '2022-08-28 18:41:41', 1, 'Q814864'),
(32733, 'Beizama', 1191, 'SS', 207, 'ES', 43.13385000, -2.20001000, '2019-10-05 22:45:37', '2022-08-28 18:36:49', 1, 'Q1625906'),
(32734, 'Belalcázar', 5097, 'CO', 207, 'ES', 38.57566000, -5.16653000, '2019-10-05 22:45:37', '2022-08-28 18:49:38', 1, 'Q854373'),
(32735, 'Belascoáin', 1204, 'NA', 207, 'ES', 42.75619000, -1.83227000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q1645846'),
(32736, 'Belbimbre', 1146, 'BU', 207, 'ES', 42.16873000, -4.01280000, '2019-10-05 22:45:37', '2022-08-29 11:24:19', 1, 'Q1448083'),
(32737, 'Belchite', 5113, 'Z', 207, 'ES', 41.30600000, -0.75400000, '2019-10-05 22:45:37', '2022-08-29 11:42:53', 1, 'Q24016479'),
(32738, 'Beleña', 1147, 'SA', 207, 'ES', 40.75067000, -5.62713000, '2019-10-05 22:45:37', '2022-08-29 11:44:50', 1, 'Q24016479'),
(32739, 'Belinchón', 5106, 'CU', 207, 'ES', 40.04603000, -3.05517000, '2019-10-05 22:45:37', '2022-08-29 11:05:00', 1, 'Q1903985'),
(32741, 'Bello', 5111, 'TE', 207, 'ES', 40.92266000, -1.49850000, '2019-10-05 22:45:37', '2022-08-29 11:29:44', 1, 'Q1903985'),
(32742, 'Bellprat', 5102, 'B', 207, 'ES', 41.51695000, 1.43333000, '2019-10-05 22:45:37', '2022-08-29 10:50:00', 1, 'Q1903985'),
(32743, 'Bellpuig', 5104, 'L', 207, 'ES', 41.62595000, 1.01144000, '2019-10-05 22:45:37', '2022-08-29 10:55:25', 1, 'Q948983'),
(32744, 'Bellreguard', 1175, 'V', 207, 'ES', 38.95000000, -0.16667000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q948983'),
(32745, 'Bellver de Cerdanya', 5104, 'L', 207, 'ES', 42.36667000, 1.78333000, '2019-10-05 22:45:37', '2022-08-29 10:55:25', 1, 'Q948983'),
(32746, 'Bellvís', 5104, 'L', 207, 'ES', 41.67269000, 0.81768000, '2019-10-05 22:45:37', '2022-08-29 10:55:25', 1, 'Q1772309'),
(32747, 'Bellús', 1175, 'V', 207, 'ES', 38.94580000, -0.48697000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1917892'),
(32748, 'Belmonte', 5106, 'CU', 207, 'ES', 39.55746000, -2.70461000, '2019-10-05 22:45:37', '2022-08-29 11:05:00', 1, 'Q1917892'),
(32749, 'Belmonte de Campos', 1157, 'P', 207, 'ES', 41.94255000, -4.98659000, '2019-10-05 22:45:37', '2022-08-29 11:45:44', 1, 'Q816113'),
(32750, 'Belmonte de Tajo', 1158, 'M', 207, 'ES', 40.13162000, -3.33580000, '2019-10-05 22:45:37', '2022-08-29 12:04:40', 1, 'Q1752454'),
(32751, 'Belmontejo', 5106, 'CU', 207, 'ES', 39.82212000, -2.34467000, '2019-10-05 22:45:37', '2022-08-29 11:05:00', 1, 'Q1772323'),
(32752, 'Belorado', 1146, 'BU', 207, 'ES', 42.42048000, -3.19133000, '2019-10-05 22:45:37', '2022-08-29 11:24:19', 1, 'Q536682'),
(32753, 'Belver de Cinca', 1177, 'HU', 207, 'ES', 41.69272000, 0.17827000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q24002618'),
(32754, 'Belver de los Montes', 1161, 'ZA', 207, 'ES', 41.72284000, -5.45182000, '2019-10-05 22:45:37', '2022-08-29 11:48:03', 1, 'Q24016754'),
(32755, 'Belvis de la Jara', 1205, 'TO', 207, 'ES', 39.75768000, -4.94932000, '2019-10-05 22:45:37', '2022-08-29 11:08:29', 1, 'Q1628657'),
(32756, 'Bembibre', 1200, 'LE', 207, 'ES', 42.61771000, -6.41545000, '2019-10-05 22:45:37', '2019-10-05 22:45:37', 1, 'Q1157834'),
(32757, 'Benacazón', 1193, 'SE', 207, 'ES', 37.35289000, -6.19663000, '2019-10-05 22:45:37', '2022-08-28 19:08:49', 1, 'Q1628394'),
(32758, 'Benafarces', 1183, 'VA', 207, 'ES', 41.62133000, -5.29285000, '2019-10-05 22:45:37', '2022-08-29 11:48:44', 1, 'Q609403'),
(32759, 'Benafigos', 5110, 'CS', 207, 'ES', 40.27641000, -0.20772000, '2019-10-05 22:45:37', '2022-08-29 11:26:42', 1, 'Q1753052'),
(32760, 'Benaguasil', 1175, 'V', 207, 'ES', 39.60000000, -0.58333000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1753052'),
(32761, 'Benagéber', 1175, 'V', 207, 'ES', 39.71667000, -1.10000000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1775442'),
(32762, 'Benahadux', 5095, 'AL', 207, 'ES', 36.92493000, -2.45941000, '2019-10-05 22:45:37', '2022-08-28 18:41:41', 1, 'Q1157968'),
(32763, 'Benahavís', 5101, 'MA', 207, 'ES', 36.52361000, -5.04631000, '2019-10-05 22:45:37', '2022-08-28 19:06:52', 1, 'Q816672'),
(32764, 'Benalauría', 5101, 'MA', 207, 'ES', 36.59445000, -5.26099000, '2019-10-05 22:45:37', '2022-08-28 19:06:52', 1, 'Q816675'),
(32765, 'Benalmádena', 5101, 'MA', 207, 'ES', 36.59610000, -4.57267000, '2019-10-05 22:45:37', '2022-08-28 19:06:52', 1, 'Q488869'),
(32766, 'Benalup-Casas Viejas', 5096, 'CA', 207, 'ES', 36.34375000, -5.81280000, '2019-10-05 22:45:37', '2022-08-28 18:44:29', 1, 'Q488869'),
(32768, 'Benalúa de las Villas', 5098, 'GR', 207, 'ES', 37.42742000, -3.68346000, '2019-10-05 22:45:37', '2022-08-28 18:52:57', 1, 'Q554814'),
(32769, 'Benamargosa', 5101, 'MA', 207, 'ES', 36.83499000, -4.19362000, '2019-10-05 22:45:37', '2022-08-28 19:06:52', 1, 'Q970721'),
(32770, 'Benamaurel', 5098, 'GR', 207, 'ES', 37.60826000, -2.70250000, '2019-10-05 22:45:37', '2022-08-28 18:52:57', 1, 'Q554238'),
(32771, 'Benamejí', 5097, 'CO', 207, 'ES', 37.26833000, -4.54123000, '2019-10-05 22:45:37', '2022-08-28 18:49:38', 1, 'Q975728'),
(32772, 'Benamocarra', 5101, 'MA', 207, 'ES', 36.79075000, -4.16146000, '2019-10-05 22:45:37', '2022-08-28 19:06:52', 1, 'Q1624129'),
(32773, 'Benaocaz', 5096, 'CA', 207, 'ES', 36.70069000, -5.42222000, '2019-10-05 22:45:37', '2022-08-28 18:44:29', 1, 'Q741812'),
(32774, 'Benaoján', 5101, 'MA', 207, 'ES', 36.71929000, -5.25220000, '2019-10-05 22:45:37', '2022-08-28 19:06:52', 1, 'Q816694'),
(32775, 'Benarrabá', 5101, 'MA', 207, 'ES', 36.55120000, -5.27608000, '2019-10-05 22:45:37', '2022-08-28 19:06:52', 1, 'Q816696'),
(32776, 'Benasau', 5108, 'A', 207, 'ES', 38.69047000, -0.34278000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1770136'),
(32777, 'Benasque', 1177, 'HU', 207, 'ES', 42.60528000, 0.52305000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q575809'),
(32778, 'Benassal', 1175, 'V', 207, 'ES', 40.37690000, -0.13970000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1635350'),
(32779, 'Benatae', 5100, 'J', 207, 'ES', 38.35323000, -2.65121000, '2019-10-05 22:45:37', '2022-08-28 19:04:30', 1, 'Q1631853'),
(32780, 'Benavarri / Benabarre', 1177, 'HU', 207, 'ES', 42.10586000, 0.48211000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q816666'),
(32781, 'Benavente', 1161, 'ZA', 207, 'ES', 42.00249000, -5.67826000, '2019-10-05 22:45:37', '2022-08-29 11:48:03', 1, 'Q816666'),
(32782, 'Benavides', 1200, 'LE', 207, 'ES', 42.50442000, -5.89442000, '2019-10-05 22:45:37', '2019-10-05 22:45:37', 1, 'Q816666'),
(32783, 'Benavites', 1175, 'V', 207, 'ES', 39.73333000, -0.25000000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q816666'),
(32784, 'Benegiles', 1161, 'ZA', 207, 'ES', 41.62700000, -5.63479000, '2019-10-05 22:45:37', '2022-08-29 11:48:03', 1, 'Q1650421'),
(32785, 'Beneixama', 5108, 'A', 207, 'ES', 38.70000000, -0.76667000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1650421'),
(32786, 'Beneixida', 1175, 'V', 207, 'ES', 39.06667000, -0.55000000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1650421'),
(32787, 'Benejúzar', 5108, 'A', 207, 'ES', 38.07728000, -0.83942000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1752438'),
(32788, 'Benetússer', 1175, 'V', 207, 'ES', 39.42265000, -0.39686000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1752438'),
(32789, 'Benferri', 5108, 'A', 207, 'ES', 38.14129000, -0.96212000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1751451'),
(32790, 'Beniarbeig', 5108, 'A', 207, 'ES', 38.82232000, -0.00210000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1751451'),
(32791, 'Beniardá', 5108, 'A', 207, 'ES', 38.68433000, -0.21629000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1751451'),
(32792, 'Beniarjó', 1175, 'V', 207, 'ES', 38.93249000, -0.18634000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1751451'),
(32793, 'Beniarrés', 5108, 'A', 207, 'ES', 38.82019000, -0.37741000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q621839'),
(32794, 'Beniatjar', 1175, 'V', 207, 'ES', 38.84754000, -0.41736000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q384789'),
(32795, 'Benicarló', 5110, 'CS', 207, 'ES', 40.41650000, 0.42709000, '2019-10-05 22:45:37', '2022-08-29 11:26:42', 1, 'Q384789'),
(32796, 'Benicolet', 1175, 'V', 207, 'ES', 38.91987000, -0.34694000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1919305'),
(32797, 'Benicàssim', 1175, 'V', 207, 'ES', 40.05000000, 0.06667000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q636467'),
(32798, 'Benidoleig', 5108, 'A', 207, 'ES', 38.79278000, -0.02992000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q636467'),
(32799, 'Benidorm', 5108, 'A', 207, 'ES', 38.53816000, -0.13098000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q487981'),
(32800, 'Beniel', 1176, 'MU', 207, 'ES', 38.04636000, -1.00233000, '2019-10-05 22:45:37', '2022-08-29 12:05:49', 1, 'Q23987814'),
(32801, 'Benifairó de les Valls', 1175, 'V', 207, 'ES', 39.73333000, -0.26667000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q23987814'),
(32802, 'Benifaió', 1175, 'V', 207, 'ES', 39.28439000, -0.42598000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q23987814'),
(32803, 'Benifallet', 1203, 'T', 207, 'ES', 40.97422000, 0.51767000, '2019-10-05 22:45:37', '2022-08-29 10:57:32', 1, 'Q23987814'),
(32804, 'Benifallim', 5108, 'A', 207, 'ES', 38.66259000, -0.39994000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q23987814'),
(32805, 'Beniflá', 1175, 'V', 207, 'ES', 38.92813000, -0.17816000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1917869'),
(32806, 'Benigànim', 1175, 'V', 207, 'ES', 38.95000000, -0.43333000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q23987819'),
(32807, 'Benijofar', 5108, 'A', 207, 'ES', 38.07785000, -0.73680000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1650580'),
(32808, 'Benilloba', 5108, 'A', 207, 'ES', 38.70012000, -0.38998000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1770091'),
(32809, 'Benillup', 5108, 'A', 207, 'ES', 38.75397000, -0.37991000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1771932'),
(32810, 'Benimantell', 5108, 'A', 207, 'ES', 38.67709000, -0.21057000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1771932'),
(32811, 'Benimarfull', 5108, 'A', 207, 'ES', 38.77590000, -0.39079000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1770942'),
(32812, 'Benimassot', 5108, 'A', 207, 'ES', 38.75000000, -0.28333000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1770942'),
(32813, 'Benimeli', 5108, 'A', 207, 'ES', 38.82362000, -0.04221000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1637136'),
(32814, 'Benimodo', 1175, 'V', 207, 'ES', 39.21403000, -0.52679000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1917885'),
(32815, 'Benimuslem', 1175, 'V', 207, 'ES', 39.13162000, -0.49288000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1919328'),
(32816, 'Beniparrell', 1175, 'V', 207, 'ES', 39.38333000, -0.41667000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1919328'),
(32817, 'Benirredrà', 1175, 'V', 207, 'ES', 38.96667000, -0.18333000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1919328'),
(32818, 'Benisanó', 1175, 'V', 207, 'ES', 39.61667000, -0.56667000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1772298'),
(32819, 'Benissa', 5108, 'A', 207, 'ES', 38.71492000, 0.04849000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q23987828'),
(32820, 'Benissoda', 1175, 'V', 207, 'ES', 38.83333000, -0.51667000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q23987828'),
(32821, 'Benisuera', 1175, 'V', 207, 'ES', 38.91320000, -0.47784000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q1917939'),
(32822, 'Benitachell', 1175, 'V', 207, 'ES', 38.73273000, 0.14354000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q817349'),
(32823, 'Benitagla', 5095, 'AL', 207, 'ES', 37.23138000, -2.23935000, '2019-10-05 22:45:37', '2022-08-28 18:41:41', 1, 'Q817349'),
(32824, 'Benizalón', 5095, 'AL', 207, 'ES', 37.21213000, -2.24180000, '2019-10-05 22:45:37', '2022-08-28 18:41:41', 1, 'Q1444189'),
(32825, 'Benlloch', 5110, 'CS', 207, 'ES', 40.21075000, 0.02717000, '2019-10-05 22:45:37', '2022-08-29 11:26:42', 1, 'Q1444189'),
(32826, 'Benquerencia', 1190, 'CC', 207, 'ES', 39.30994000, -6.08465000, '2019-10-05 22:45:37', '2022-08-28 18:12:22', 1, 'Q1769733'),
(32827, 'Bentarique', 5095, 'AL', 207, 'ES', 36.98823000, -2.61991000, '2019-10-05 22:45:37', '2022-08-28 18:41:41', 1, 'Q1769733'),
(32828, 'Benuza', 1200, 'LE', 207, 'ES', 42.39941000, -6.70969000, '2019-10-05 22:45:37', '2019-10-05 22:45:37', 1, 'Q972909'),
(32830, 'Bera', 1204, 'NA', 207, 'ES', 43.28177000, -1.68632000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q818675'),
(32831, 'Berango', 5094, 'BI', 207, 'ES', 43.36500000, -2.99601000, '2019-10-05 22:45:37', '2022-08-28 18:25:55', 1, 'Q1242343'),
(32832, 'Berantevilla', 5093, 'VI', 207, 'ES', 42.68254000, -2.85832000, '2019-10-05 22:45:37', '2022-08-28 18:23:49', 1, 'Q1368868'),
(32833, 'Beratón', 1208, 'SO', 207, 'ES', 41.71749000, -1.81092000, '2019-10-05 22:45:37', '2022-08-29 11:51:23', 1, 'Q830206'),
(32834, 'Berbegal', 1177, 'HU', 207, 'ES', 41.95959000, -0.00326000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q1651293'),
(32835, 'Berberana', 1146, 'BU', 207, 'ES', 42.91784000, -3.06063000, '2019-10-05 22:45:37', '2022-08-29 11:24:19', 1, 'Q1448025'),
(32836, 'Berbinzana', 1204, 'NA', 207, 'ES', 42.52779000, -1.83419000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q1645757'),
(32837, 'Berceo', 1171, 'LO', 207, 'ES', 42.33906000, -2.85239000, '2019-10-05 22:45:37', '2022-08-29 12:05:09', 1, 'Q1647620'),
(32838, 'Bercero', 1183, 'VA', 207, 'ES', 41.56397000, -5.05580000, '2019-10-05 22:45:37', '2022-08-29 11:48:44', 1, 'Q1929361'),
(32839, 'Berceruelo', 1183, 'VA', 207, 'ES', 41.58033000, -5.03267000, '2019-10-05 22:45:37', '2022-08-29 11:48:44', 1, 'Q1776841'),
(32840, 'Bercial', 1192, 'SG', 207, 'ES', 40.90711000, -4.43638000, '2019-10-05 22:45:37', '2022-08-29 11:50:42', 1, 'Q1904737'),
(32841, 'Bercial de Zapardiel', 1189, 'AV', 207, 'ES', 41.04675000, -4.96910000, '2019-10-05 22:45:37', '2022-08-29 11:49:56', 1, 'Q1620352'),
(32842, 'Bercianos del Páramo', 1200, 'LE', 207, 'ES', 42.38017000, -5.70834000, '2019-10-05 22:45:37', '2020-05-01 17:23:16', 1, 'Q26646'),
(32843, 'Bercianos del Real Camino', 1200, 'LE', 207, 'ES', 42.38732000, -5.14462000, '2019-10-05 22:45:37', '2019-10-05 22:45:37', 1, 'Q26637'),
(32844, 'Bercimuel', 1192, 'SG', 207, 'ES', 41.39925000, -3.57051000, '2019-10-05 22:45:37', '2022-08-29 11:50:42', 1, 'Q1650159'),
(32845, 'Berdejo', 5113, 'Z', 207, 'ES', 41.56133000, -1.94431000, '2019-10-05 22:45:37', '2022-08-29 11:42:53', 1, 'Q1640971'),
(32846, 'Berga', 5102, 'B', 207, 'ES', 42.10429000, 1.84628000, '2019-10-05 22:45:37', '2022-08-29 10:50:00', 1, 'Q1640971'),
(32847, 'Bergara', 1191, 'SS', 207, 'ES', 43.11510000, -2.41750000, '2019-10-05 22:45:37', '2022-08-28 18:36:50', 1, 'Q1382252'),
(32848, 'Bergasa', 1171, 'LO', 207, 'ES', 42.25285000, -2.13221000, '2019-10-05 22:45:37', '2022-08-29 12:05:09', 1, 'Q1647639'),
(32849, 'Bergasillas Bajera', 1171, 'LO', 207, 'ES', 42.24462000, -2.15830000, '2019-10-05 22:45:37', '2022-08-29 12:05:09', 1, 'Q1637049'),
(32850, 'Berge', 5111, 'TE', 207, 'ES', 40.85791000, -0.42709000, '2019-10-05 22:45:37', '2022-08-29 11:29:44', 1, 'Q1637049'),
(32851, 'Bergondo', 5089, 'C', 207, 'ES', 43.31667000, -8.23333000, '2019-10-05 22:45:37', '2022-08-28 13:37:16', 1, 'Q644018'),
(32852, 'Beriáin', 1204, 'NA', 207, 'ES', 42.73347000, -1.64448000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q820748'),
(32853, 'Berja', 5095, 'AL', 207, 'ES', 36.84693000, -2.94966000, '2019-10-05 22:45:37', '2022-08-28 18:41:41', 1, 'Q491929'),
(32854, 'Berlanga', 5092, 'BA', 207, 'ES', 38.28333000, -5.81667000, '2019-10-05 22:45:37', '2022-08-28 18:09:23', 1, 'Q1610680'),
(32855, 'Berlanga de Duero', 1208, 'SO', 207, 'ES', 41.46560000, -2.86147000, '2019-10-05 22:45:37', '2022-08-29 11:51:23', 1, 'Q178684'),
(32856, 'Berlanga del Bierzo', 1200, 'LE', 207, 'ES', 42.73104000, -6.60565000, '2019-10-05 22:45:37', '2019-10-05 22:45:37', 1, 'Q178684'),
(32857, 'Berlangas de Roa', 1146, 'BU', 207, 'ES', 41.68913000, -3.87284000, '2019-10-05 22:45:37', '2022-08-29 11:24:19', 1, 'Q1448047'),
(32858, 'Bermellar', 1147, 'SA', 207, 'ES', 40.99918000, -6.67014000, '2019-10-05 22:45:37', '2022-08-29 11:44:50', 1, 'Q683499'),
(32859, 'Bermeo', 5094, 'BI', 207, 'ES', 43.42088000, -2.72152000, '2019-10-05 22:45:37', '2022-08-28 18:25:55', 1, 'Q695444'),
(32860, 'Bermillo de Sayago', 1161, 'ZA', 207, 'ES', 41.36648000, -6.11223000, '2019-10-05 22:45:37', '2022-08-29 11:48:03', 1, 'Q1650457'),
(32861, 'Bernardos', 1192, 'SG', 207, 'ES', 41.12787000, -4.35119000, '2019-10-05 22:45:37', '2022-08-29 11:50:42', 1, 'Q1904470'),
(32862, 'Berninches', 5107, 'GU', 207, 'ES', 40.57106000, -2.80121000, '2019-10-05 22:45:37', '2022-08-29 11:06:45', 1, 'Q1656062'),
(32863, 'Berriatua', 5094, 'BI', 207, 'ES', 43.31667000, -2.46667000, '2019-10-05 22:45:37', '2022-08-28 18:25:55', 1, 'Q1228064'),
(32864, 'Berriobeiti', 1204, 'NA', 207, 'ES', 42.85000000, -1.70000000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q1228064'),
(32865, 'Berriozar', 1204, 'NA', 207, 'ES', 42.83067000, -1.66648000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q1645776'),
(32866, 'Berriz', 5094, 'BI', 207, 'ES', 43.16667000, -2.56667000, '2019-10-05 22:45:37', '2022-08-28 18:25:55', 1, 'Q1645776'),
(32867, 'Berrobi', 1191, 'SS', 207, 'ES', 43.14518000, -2.02623000, '2019-10-05 22:45:37', '2022-08-28 18:36:49', 1, 'Q1625846'),
(32868, 'Berrocal', 5099, 'H', 207, 'ES', 37.60863000, -6.54147000, '2019-10-05 22:45:37', '2022-08-28 19:00:43', 1, 'Q1625846'),
(32869, 'Berrocal de Huebra', 1147, 'SA', 207, 'ES', 40.71864000, -6.00054000, '2019-10-05 22:45:37', '2022-08-29 11:44:50', 1, 'Q1640878'),
(32870, 'Berrocal de Salvatierra', 1147, 'SA', 207, 'ES', 40.63331000, -5.69005000, '2019-10-05 22:45:37', '2022-08-29 11:44:50', 1, 'Q1651966'),
(32871, 'Berrocalejo', 1190, 'CC', 207, 'ES', 39.81948000, -5.34951000, '2019-10-05 22:45:37', '2022-08-28 18:12:22', 1, 'Q1658553'),
(32872, 'Berrocalejo de Aragona', 1189, 'AV', 207, 'ES', 40.69436000, -4.59474000, '2019-10-05 22:45:37', '2022-08-29 11:49:56', 1, 'Q1601818'),
(32873, 'Berrueces', 1183, 'VA', 207, 'ES', 41.94606000, -5.09693000, '2019-10-05 22:45:37', '2022-08-29 11:48:44', 1, 'Q1601818'),
(32874, 'Berzosa de Bureba', 1146, 'BU', 207, 'ES', 42.62678000, -3.26652000, '2019-10-05 22:45:37', '2022-08-29 11:24:19', 1, 'Q1614663');

