INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(57425, 'Uíbh Fhailí', 1073, '<PERSON>', 105, 'IE', 53.********, -7.********, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q184445'),
(57426, 'Valleymount', 1073, 'L', 105, 'IE', 53.10389000, -6.55361000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q4102941'),
(57427, 'Virginia', 1086, 'U', 105, 'IE', 53.83389000, -7.07556000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q255427'),
(57428, 'Waterford', 1080, '<PERSON>', 105, 'I<PERSON>', 52.25833000, -7.11194000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q183551'),
(57429, '<PERSON>grasshill', 1080, 'M', 105, 'IE', 52.01139000, -8.34417000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q3776348'),
(57430, 'Westport', 1087, 'C', 105, 'IE', 53.80000000, -9.51667000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1017331'),
(57431, 'Whitegate', 1080, 'M', 105, 'IE', 51.83056000, -8.22972000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q4468449'),
(57432, 'Wicklow', 1073, 'L', 105, 'IE', 53.00000000, -6.41667000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q182591'),
(57433, 'Youghal', 1080, 'M', 105, 'IE', 51.95000000, -7.85056000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1012476'),
(57434, 'Abū Ghaush', 1370, 'JM', 106, 'IL', 31.80592000, 35.10930000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q335023'),
(57435, 'Acre', 1366, 'Z', 106, 'IL', 32.92814000, 35.07647000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q126084'),
(57436, 'Afula', 1366, 'Z', 106, 'IL', 32.60907000, 35.28920000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152462'),
(57437, 'Arad', 1368, 'D', 106, 'IL', 31.25882000, 35.21282000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152473'),
(57438, 'Ashdod', 1368, 'D', 106, 'IL', 31.79213000, 34.64966000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q171639'),
(57439, 'Ashkelon', 1368, 'D', 106, 'IL', 31.66926000, 34.57149000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q60956'),
(57440, 'Atlit', 1369, 'HA', 106, 'IL', 32.68889000, 34.94236000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q757398'),
(57441, 'Azor', 1371, 'TA', 106, 'IL', 32.02430000, 34.80632000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q952186'),
(57442, 'Basmat Ṭab‘ūn', 1366, 'Z', 106, 'IL', 32.73898000, 35.15716000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2651477'),
(57443, 'Bat Yam', 1371, 'TA', 106, 'IL', 32.02379000, 34.75185000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q208725'),
(57444, 'Beersheba', 1368, 'D', 106, 'IL', 31.25181000, 34.79130000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q41843'),
(57445, 'Beit Jann', 1366, 'Z', 106, 'IL', 32.96464000, 35.38152000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q814900'),
(57446, 'Bet Dagan', 1367, 'M', 106, 'IL', 32.00191000, 34.82977000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q814900'),
(57447, 'Bet Shemesh', 1370, 'JM', 106, 'IL', 31.73072000, 34.99293000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152449'),
(57448, 'Bet She’an', 1366, 'Z', 106, 'IL', 32.49728000, 35.49632000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q152368'),
(57449, 'Bet Yiẕẖaq', 1367, 'M', 106, 'IL', 32.32751000, 34.88878000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2920289'),
(57450, 'Bnei Ayish', 1367, 'M', 106, 'IL', 31.78333000, 34.75000000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2915758'),
(57451, 'Bnei Brak', 1371, 'TA', 106, 'IL', 32.08074000, 34.83380000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152467'),
(57452, 'Buqei‘a', 1366, 'Z', 106, 'IL', 32.97747000, 35.33345000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q152467'),
(57453, 'Bu‘eina', 1366, 'Z', 106, 'IL', 32.80636000, 35.36486000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q152467'),
(57454, 'Bīr el Maksūr', 1366, 'Z', 106, 'IL', 32.77732000, 35.22069000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q152467'),
(57455, 'Caesarea', 1369, 'HA', 106, 'IL', 32.51888000, 34.90459000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1615560'),
(57456, 'Dabbūrīya', 1366, 'Z', 106, 'IL', 32.69256000, 35.37123000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q1615560'),
(57457, 'Daliyat al Karmel', 1369, 'HA', 106, 'IL', 32.69383000, 35.04686000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1026821'),
(57458, 'Deir Ḥannā', 1366, 'Z', 106, 'IL', 32.86196000, 35.36365000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q612483'),
(57459, 'Dimona', 1368, 'D', 106, 'IL', 31.07079000, 35.03269000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152396'),
(57460, 'Eilat', 1368, 'D', 106, 'IL', 29.55805000, 34.94821000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q134762'),
(57461, 'El Fureidīs', 1369, 'HA', 106, 'IL', 32.59812000, 34.95153000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q1011538'),
(57462, 'El Mazra‘a', 1366, 'Z', 106, 'IL', 32.98338000, 35.09837000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q1011538'),
(57463, 'Elyakhin', 1367, 'M', 106, 'IL', 32.40793000, 34.92433000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2920239'),
(57464, 'Er Reina', 1366, 'Z', 106, 'IL', 32.72339000, 35.31622000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2655682'),
(57465, 'Esh Sheikh Dannūn', 1366, 'Z', 106, 'IL', 32.99410000, 35.14805000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q135434'),
(57466, 'Even Yehuda', 1367, 'M', 106, 'IL', 32.26959000, 34.88759000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2915760'),
(57467, 'Eṭ Ṭaiyiba', 1367, 'M', 106, 'IL', 32.26616000, 35.00893000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q1022481'),
(57468, 'Gan Yavne', 1367, 'M', 106, 'IL', 31.78737000, 34.70659000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q924849'),
(57469, 'Ganei Tikva', 1367, 'M', 106, 'IL', 32.05971000, 34.87320000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2920123'),
(57470, 'Gedera', 1367, 'M', 106, 'IL', 31.81456000, 34.77998000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q921505'),
(57471, 'Giv\'at Shmuel', 1371, 'TA', 106, 'IL', 32.07817000, 34.84858000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152641'),
(57472, 'Givatayim', 1371, 'TA', 106, 'IL', 32.07225000, 34.81253000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152413'),
(57473, 'Hadera', 1369, 'HA', 106, 'IL', 32.44192000, 34.90390000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q471886'),
(57474, 'Haifa', 1369, 'HA', 106, 'IL', 32.81841000, 34.98850000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q41621'),
(57475, 'Har Adar', 1370, 'JM', 106, 'IL', 31.82754000, 35.13093000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q41621'),
(57476, 'Herzliya', 1371, 'TA', 106, 'IL', 32.16627000, 34.82536000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152491'),
(57477, 'Herzliya Pituah', 1371, 'TA', 106, 'IL', 32.17409000, 34.80280000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152491'),
(57478, 'Hod HaSharon', 1367, 'M', 106, 'IL', 32.15934000, 34.89320000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152379'),
(57479, 'H̱olon', 1371, 'TA', 106, 'IL', 32.01034000, 34.77918000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q192213'),
(57480, 'Ibṭīn', 1369, 'HA', 106, 'IL', 32.76150000, 35.11402000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2889270'),
(57481, 'Iksāl', 1366, 'Z', 106, 'IL', 32.68164000, 35.32365000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2915526'),
(57482, 'Jaffa', 1371, 'TA', 106, 'IL', 32.05043000, 34.75224000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q180294'),
(57483, 'Jaljūlya', 1367, 'M', 106, 'IL', 32.15470000, 34.95372000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q180294'),
(57484, 'Jerusalem', 1370, 'JM', 106, 'IL', 31.76904000, 35.21633000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1218'),
(57485, 'Judeida Makr', 1366, 'Z', 106, 'IL', 32.92820000, 35.15705000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1218'),
(57486, 'Jīsh', 1366, 'Z', 106, 'IL', 33.02216000, 35.44694000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q1527236'),
(57487, 'Kafr Kammā', 1366, 'Z', 106, 'IL', 32.72129000, 35.44122000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2576600'),
(57488, 'Kafr Kannā', 1366, 'Z', 106, 'IL', 32.74660000, 35.34242000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2633158'),
(57489, 'Kafr Mandā', 1366, 'Z', 106, 'IL', 32.81034000, 35.26009000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2915534'),
(57490, 'Kafr Miṣr', 1366, 'Z', 106, 'IL', 32.64521000, 35.42147000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q667103'),
(57491, 'Kafr Qāsim', 1367, 'M', 106, 'IL', 32.11406000, 34.97624000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q152659'),
(57492, 'Karmi’el', 1366, 'Z', 106, 'IL', 32.91708000, 35.30501000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q152418'),
(57493, 'Kaukab Abū el Hījā', 1366, 'Z', 106, 'IL', 32.83155000, 35.24848000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2604424'),
(57494, 'Kefar H̱abad', 1367, 'M', 106, 'IL', 31.98793000, 34.85160000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2570492'),
(57495, 'Kefar Rosh HaNiqra', 1366, 'Z', 106, 'IL', 33.08607000, 35.11348000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2570492'),
(57496, 'Kefar Shemaryahu', 1371, 'TA', 106, 'IL', 32.18529000, 34.82082000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2389508'),
(57497, 'Kefar Tavor', 1366, 'Z', 106, 'IL', 32.68655000, 35.42118000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1186956'),
(57498, 'Kefar Weradim', 1366, 'Z', 106, 'IL', 32.99385000, 35.27793000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2915542'),
(57499, 'Kefar Yona', 1367, 'M', 106, 'IL', 32.31669000, 34.93507000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2640074'),
(57500, 'Kfar Saba', 1367, 'M', 106, 'IL', 32.17500000, 34.90694000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q152436'),
(57501, 'Kfar Yasif', 1366, 'Z', 106, 'IL', 32.95451000, 35.16230000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2632879'),
(57502, 'Kābūl', 1366, 'Z', 106, 'IL', 32.86856000, 35.21171000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q1018884'),
(57503, 'Lapid', 1367, 'M', 106, 'IL', 31.91764000, 35.03222000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2509463'),
(57504, 'Lehavim', 1368, 'D', 106, 'IL', 31.37284000, 34.81619000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1589806'),
(57505, 'Lod', 1367, 'M', 106, 'IL', 31.94670000, 34.89030000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q207540'),
(57506, 'Maghār', 1366, 'Z', 106, 'IL', 32.88984000, 35.40703000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q930847'),
(57507, 'Mazkeret Batya', 1367, 'M', 106, 'IL', 31.85357000, 34.84646000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1914736'),
(57508, 'Metulla', 1366, 'Z', 106, 'IL', 33.27918000, 35.57950000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q1011497'),
(57509, 'Mevasseret Ẕiyyon', 1370, 'JM', 106, 'IL', 31.80186000, 35.15072000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q1025294'),
(57510, 'Midreshet Ben-Gurion', 1368, 'D', 106, 'IL', 30.85154000, 34.78340000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q2776759'),
(57511, 'Migdal Ha‘Emeq', 1366, 'Z', 106, 'IL', 32.67597000, 35.23986000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q168337'),
(57512, 'Mitzpe Ramon', 1368, 'D', 106, 'IL', 30.60944000, 34.80111000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q168337'),
(57513, 'Mi‘ilyā', 1366, 'Z', 106, 'IL', 33.02781000, 35.25658000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q2917297'),
(57514, 'Modiin Ilit', 1370, 'JM', 106, 'IL', 31.93221000, 35.04416000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q168140'),
(57515, 'Modi‘in Makkabbim Re‘ut', 1367, 'M', 106, 'IL', 31.89385000, 35.01504000, '2019-10-05 22:53:42', '2020-05-01 17:22:54', 1, 'Q168140'),
(57516, 'Nahariyya', 1366, 'Z', 106, 'IL', 33.00892000, 35.09814000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q167749'),
(57517, 'Nazareth', 1366, 'Z', 106, 'IL', 32.70056000, 35.29722000, '2019-10-05 22:53:42', '2019-10-05 22:53:42', 1, 'Q430776'),
(57518, 'Naḥf', 1366, 'Z', 106, 'IL', 32.93444000, 35.31679000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q2390257'),
(57519, 'Nefat ‘Akko', 1366, 'Z', 106, 'IL', 32.95000000, 35.23333000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q5423103'),
(57520, 'Nein', 1366, 'Z', 106, 'IL', 32.63063000, 35.34885000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q934904'),
(57521, 'Nesher', 1369, 'HA', 106, 'IL', 32.76622000, 35.04425000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q135432'),
(57522, 'Ness Ziona', 1367, 'M', 106, 'IL', 31.92933000, 34.79868000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q168162'),
(57523, 'Netanya', 1367, 'M', 106, 'IL', 32.33291000, 34.85992000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q192225'),
(57524, 'Netivot', 1368, 'D', 106, 'IL', 31.42305000, 34.58911000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q167649'),
(57525, 'Neẖalim', 1367, 'M', 106, 'IL', 32.06012000, 34.91069000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q2614192'),
(57526, 'Nirit', 1367, 'M', 106, 'IL', 32.14677000, 34.98622000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2614192'),
(57527, 'Nof Ayalon', 1367, 'M', 106, 'IL', 31.87111000, 34.99081000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2614192'),
(57528, 'Nordiyya', 1367, 'M', 106, 'IL', 32.31470000, 34.89617000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2920311'),
(57529, 'Ofaqim', 1368, 'D', 106, 'IL', 31.31410000, 34.62025000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q167632'),
(57530, 'Or Yehuda', 1371, 'TA', 106, 'IL', 32.02923000, 34.85788000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q950347'),
(57531, 'Pardesiyya', 1367, 'M', 106, 'IL', 32.30577000, 34.90911000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q1018875'),
(57532, 'Pasuta', 1366, 'Z', 106, 'IL', 33.04895000, 35.30893000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2917294'),
(57533, 'Petaẖ Tiqwa', 1367, 'M', 106, 'IL', 32.08707000, 34.88747000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q190828'),
(57534, 'Qalansuwa', 1367, 'M', 106, 'IL', 32.28493000, 34.98106000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q190828'),
(57535, 'Qiryat Ata', 1369, 'HA', 106, 'IL', 32.81149000, 35.11323000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q152602'),
(57536, 'Qiryat Bialik', 1369, 'HA', 106, 'IL', 32.82750000, 35.08583000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q152374'),
(57537, 'Qiryat Gat', 1368, 'D', 106, 'IL', 31.60998000, 34.76422000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q152406'),
(57538, 'Qiryat Moẕqin', 1369, 'HA', 106, 'IL', 32.83706000, 35.07760000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q152567'),
(57539, 'Qiryat Shemona', 1366, 'Z', 106, 'IL', 33.20733000, 35.57212000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q327413'),
(57540, 'Qiryat Yam', 1369, 'HA', 106, 'IL', 32.84966000, 35.06973000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q152387'),
(57541, 'Ra\'anana', 1367, 'M', 106, 'IL', 32.18360000, 34.87386000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q309164'),
(57542, 'Rahat', 1368, 'D', 106, 'IL', 31.39547000, 34.75699000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q167617'),
(57543, 'Ramat Gan', 1371, 'TA', 106, 'IL', 32.08227000, 34.81065000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q192807'),
(57544, 'Ramat HaSharon', 1371, 'TA', 106, 'IL', 32.14613000, 34.83940000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q845866'),
(57545, 'Ramat Yishay', 1366, 'Z', 106, 'IL', 32.70444000, 35.17070000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2615322'),
(57546, 'Ramla', 1367, 'M', 106, 'IL', 31.92923000, 34.86563000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q221447'),
(57547, 'Rekhasim', 1369, 'HA', 106, 'IL', 32.74907000, 35.09901000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q135485'),
(57548, 'Reẖovot', 1367, 'M', 106, 'IL', 31.89421000, 34.81199000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q207350'),
(57549, 'Rishon LeẔiyyon', 1367, 'M', 106, 'IL', 31.97102000, 34.78939000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q201051'),
(57550, 'Rosh Ha‘Ayin', 1367, 'M', 106, 'IL', 32.09556000, 34.95664000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q168202'),
(57551, 'Rosh Pinna', 1366, 'Z', 106, 'IL', 32.96894000, 35.54258000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q525649'),
(57552, 'Rumat Heib', 1366, 'Z', 106, 'IL', 32.77802000, 35.30571000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q525649'),
(57553, 'Safed', 1366, 'Z', 106, 'IL', 32.96465000, 35.49600000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q188336'),
(57554, 'Sakhnīn', 1366, 'Z', 106, 'IL', 32.86422000, 35.29707000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q168330'),
(57555, 'Sallama', 1366, 'Z', 106, 'IL', 32.89443000, 35.36931000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2910964'),
(57556, 'Savyon', 1367, 'M', 106, 'IL', 32.04966000, 34.87770000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q592526'),
(57557, 'Sederot', 1368, 'D', 106, 'IL', 31.52500000, 34.59693000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q123196'),
(57558, 'Shelomi', 1366, 'Z', 106, 'IL', 33.07216000, 35.14452000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q135421'),
(57559, 'Shibli', 1366, 'Z', 106, 'IL', 32.69464000, 35.39252000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q135421'),
(57560, 'Shoham', 1367, 'M', 106, 'IL', 31.99866000, 34.94559000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q304448'),
(57561, 'Sājūr', 1366, 'Z', 106, 'IL', 32.94266000, 35.34136000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q732433'),
(57562, 'Sūlam', 1366, 'Z', 106, 'IL', 32.60606000, 35.33408000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q732433'),
(57563, 'Tamra', 1366, 'Z', 106, 'IL', 32.85301000, 35.19870000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q168176'),
(57564, 'Tel Aviv', 1371, 'TA', 106, 'IL', 32.08088000, 34.78057000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q33935'),
(57565, 'Tel Mond', 1367, 'M', 106, 'IL', 32.24995000, 34.91737000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2304754'),
(57566, 'Tiberias', 1366, 'Z', 106, 'IL', 32.79221000, 35.53124000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q151920'),
(57567, 'Timrat', 1366, 'Z', 106, 'IL', 32.70302000, 35.22359000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2889401'),
(57568, 'Tirah', 1367, 'M', 106, 'IL', 32.23410000, 34.95023000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q167594'),
(57569, 'Tirat Karmel', 1369, 'HA', 106, 'IL', 32.76021000, 34.97183000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q168215'),
(57570, 'Umm el Faḥm', 1369, 'HA', 106, 'IL', 32.51725000, 35.15349000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q168245'),
(57571, 'West Jerusalem', 1370, 'JM', 106, 'IL', 31.78199000, 35.21961000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2213440'),
(57572, 'Yavne’el', 1366, 'Z', 106, 'IL', 32.70619000, 35.50435000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q1676894'),
(57573, 'Yavné', 1367, 'M', 106, 'IL', 31.87808000, 34.73983000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q167677'),
(57574, 'Yehud', 1367, 'M', 106, 'IL', 32.03317000, 34.89091000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q1000887'),
(57575, 'Yehud-Monosson', 1371, 'TA', 106, 'IL', 32.02840000, 34.87960000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q1000887'),
(57576, 'Yeroẖam', 1368, 'D', 106, 'IL', 30.98822000, 34.93176000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q1687708'),
(57577, 'maalot Tarshīhā', 1366, 'Z', 106, 'IL', 33.01667000, 35.26667000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q152385'),
(57578, 'Ḥurfeish', 1366, 'Z', 106, 'IL', 33.01711000, 35.34835000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q2655024'),
(57579, 'Ẕur Hadassa', 1370, 'JM', 106, 'IL', 31.71912000, 35.09708000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q2890554'),
(57580, 'Ẕur Moshe', 1367, 'M', 106, 'IL', 32.29819000, 34.91313000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q2888680'),
(57581, '‘Eilabun', 1366, 'Z', 106, 'IL', 32.83693000, 35.40029000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q377111'),
(57582, '‘En Boqeq', 1368, 'D', 106, 'IL', 31.19941000, 35.36253000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q377111'),
(57583, '‘Uzeir', 1366, 'Z', 106, 'IL', 32.79212000, 35.32984000, '2019-10-05 22:53:43', '2020-05-01 17:22:54', 1, 'Q2095241'),
(57584, 'Abhaneri', 4014, 'RJ', 101, 'IN', 27.00743000, 76.60760000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q4667324'),
(57585, 'Abhayapuri', 4027, 'AS', 101, 'IN', 26.32255000, 90.68526000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q490701'),
(57586, 'Abiramam', 4035, 'TN', 101, 'IN', 9.44230000, 78.43990000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q490715'),
(57587, 'Abohar', 4015, 'PB', 101, 'IN', 30.14453000, 74.19552000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490878'),
(57588, 'Abrama', 4030, 'GJ', 101, 'IN', 20.85865000, 72.90648000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490916'),
(57589, 'Achalpur', 4008, 'MH', 101, 'IN', 21.25665000, 77.51006000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490886'),
(57590, 'Achhnera', 4022, 'UP', 101, 'IN', 27.17826000, 77.75674000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490739'),
(57591, 'Adalaj', 4030, 'GJ', 101, 'IN', 23.16453000, 72.58107000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2350169'),
(57592, 'Adampur', 4015, 'PB', 101, 'IN', 31.43224000, 75.71484000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2350169'),
(57593, 'Addanki', 4017, 'AP', 101, 'IN', 15.81061000, 79.97338000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2350169'),
(57594, 'Adirampattinam', 4035, 'TN', 101, 'IN', 10.34059000, 79.37905000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490694'),
(57595, 'Aduthurai', 4035, 'TN', 101, 'IN', 11.01542000, 79.48093000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490694'),
(57596, 'Adoor', 4028, 'KL', 101, 'IN', 9.15595000, 76.73192000, '2019-10-05 22:53:43', '2025-03-31 13:11:37', 1, 'Q490941'),
(57597, 'Afzalgarh', 4022, 'UP', 101, 'IN', 29.39370000, 78.67393000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490688'),
(57598, 'Afzalpur', 4026, 'KA', 101, 'IN', 17.19986000, 76.36018000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490688'),
(57599, 'Agar', 4039, 'MP', 101, 'IN', 23.71177000, 76.01571000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q490688'),
(57600, 'Agartala', 4038, 'TR', 101, 'IN', 23.83605000, 91.27939000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q170454'),
(57601, 'Agra', 4022, 'UP', 101, 'IN', 27.18333000, 78.01667000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q42941'),
(57604, 'Ahmadpur', 4008, 'MH', 101, 'IN', 18.70622000, 76.93731000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q590521'),
(57606, 'Ahmedabad', 4030, 'GJ', 101, 'IN', 23.02579000, 72.58727000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q1070'),
(57607, 'Ahraura', 4022, 'UP', 101, 'IN', 25.01579000, 83.03294000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q587150'),
(57608, 'Ahwa', 4030, 'GJ', 101, 'IN', 20.75718000, 73.68626000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q1964214'),
(57609, 'Airoli', 4008, 'MH', 101, 'IN', 19.15096000, 72.99625000, '2019-10-05 22:53:43', '2021-06-06 13:11:00', 1, 'Q4698841'),
(57610, 'Aizawl', 4036, 'MZ', 101, 'IN', 23.80000000, 92.90000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q1947322'),
(57611, 'Ajaigarh', 4039, 'MP', 101, 'IN', 24.89879000, 80.25921000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q583090'),
(57612, 'Ajitgarh', 4015, 'PB', 101, 'IN', 30.65000000, 76.70000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2037672'),
(57613, 'Ajjampur', 4026, 'KA', 101, 'IN', 13.72794000, 76.00680000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2037672'),
(57614, 'Ajmer', 4014, 'RJ', 101, 'IN', 26.********, 74.66667000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q413037'),
(57615, 'Ajnala', 4015, 'PB', 101, 'IN', 31.84473000, 74.76295000, '2019-10-05 22:53:43', '2020-07-04 11:23:35', 1, 'Q585651'),
(57616, 'Ayodhya', 4022, 'UP', 101, 'IN', 26.79909000, 82.20470000, '2019-10-05 22:53:43', '2021-06-06 11:41:13', 1, 'Q186040'),
(57617, 'Ajara', 4008, 'MH', 101, 'IN', 16.11601000, 74.21097000, '2019-10-05 22:53:43', '2023-03-25 17:21:43', 1, 'Q584021'),
(57618, 'Akalkot', 4008, 'MH', 101, 'IN', 17.52532000, 76.20611000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q239809'),
(57619, 'Akaltara', 4040, 'CT', 101, 'IN', 22.02463000, 82.42641000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q582949'),
(57620, 'Akasahebpet', 4017, 'AP', 101, 'IN', 17.50455000, 82.56597000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q582949'),
(57621, 'Akbarpur', 4022, 'UP', 101, 'IN', 26.42953000, 82.53431000, '2019-10-05 22:53:43', '2021-06-06 11:41:13', 1, 'Q583899'),
(57622, 'Akhnur', 4029, 'JK', 101, 'IN', 32.86667000, 74.73333000, '2019-10-05 22:53:43', '2021-02-20 17:14:02', 1, 'Q416835'),
(57623, 'Akividu', 4017, 'AP', 101, 'IN', 16.58225000, 81.38112000, '2019-10-05 22:53:43', '2021-02-20 17:13:29', 1, 'Q416835'),
(57624, 'Akkarampalle', 4017, 'AP', 101, 'IN', 13.65000000, 79.42000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585782'),
(57625, 'Aklera', 4014, 'RJ', 101, 'IN', 24.41288000, 76.56719000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q587185'),
(57626, 'Akodia', 4039, 'MP', 101, 'IN', 23.38027000, 76.59875000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q582235'),
(57627, 'Akola', 4008, 'MH', 101, 'IN', 20.********, 77.16667000, '2019-10-05 22:53:43', '2021-06-06 13:11:00', 1, 'Q213014'),
(57628, 'Akot', 4008, 'MH', 101, 'IN', 21.09630000, 77.05880000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q589224'),
(57629, 'Akalgarh', 4015, 'PB', 101, 'IN', 29.82074000, 75.89078000, '2019-10-05 22:53:43', '2020-07-04 11:23:38', 1, 'Q585750'),
(57630, 'Alagapuram', 4035, 'TN', 101, 'IN', 11.88705000, 78.91758000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q585750'),
(57631, 'Alampur', 4039, 'MP', 101, 'IN', 26.02514000, 78.79697000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q14201843'),
(57632, 'Aland', 4026, 'KA', 101, 'IN', 17.56425000, 76.56854000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2738477'),
(57633, 'Alandi', 4008, 'MH', 101, 'IN', 18.67756000, 73.89868000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q589653'),
(57634, 'Alandur', 4035, 'TN', 101, 'IN', 13.00250000, 80.20611000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584845'),
(57635, 'Alanganallur', 4035, 'TN', 101, 'IN', 10.04697000, 78.09033000, '2019-10-05 22:53:43', '2021-02-20 17:14:02', 1, 'Q582312'),
(57636, 'Alangayam', 4035, 'TN', 101, 'IN', 12.62235000, 78.75207000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q590029'),
(57637, 'Alappuzha', 4028, 'KL', 101, 'IN', 9.49004000, 76.32640000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585026'),
(57638, 'Aldona', 4009, 'GA', 101, 'IN', 15.59337000, 73.87482000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584939'),
(57639, 'Alirajpur', 4039, 'MP', 101, 'IN', 22.31384000, 74.36452000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2667586'),
(57640, 'Prayagraj (Allahabad)', 4022, 'UP', 101, 'IN', 25.42012000, 81.88385000, '2019-10-05 22:53:43', '2021-05-30 20:01:46', 1, 'Q1773426'),
(57641, 'Allahganj', 4022, 'UP', 101, 'IN', 27.54540000, 79.68715000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q589789'),
(57642, 'Allapalli', 4008, 'MH', 101, 'IN', 19.43172000, 80.06377000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q589789'),
(57643, 'Almora', 4016, 'UK', 101, 'IN', 29.69223000, 79.49789000, '2019-10-05 22:53:43', '2024-02-16 18:35:00', 1, 'Q1805066'),
(57644, 'Alnavar', 4026, 'KA', 101, 'IN', 15.42727000, 74.74111000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q583849'),
(57645, 'Along', 4024, 'AR', 101, 'IN', 28.16951000, 94.80060000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q519770'),
(57646, 'Alot', 4039, 'MP', 101, 'IN', 23.76336000, 75.55662000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q583826'),
(57647, 'Aluva', 4028, 'KL', 101, 'IN', 10.10764000, 76.35158000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588793'),
(57648, 'Alwa Tirunagari', 4035, 'TN', 101, 'IN', 8.60635000, 77.93983000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588793'),
(57649, 'Alwar', 4014, 'RJ', 101, 'IN', 27.********, 76.********, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q449690'),
(57650, 'Alwaye', 4028, 'KL', 101, 'IN', 10.10649000, 76.35484000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q449690'),
(57651, 'Alawalpur', 4015, 'PB', 101, 'IN', 31.43161000, 75.65614000, '2019-10-05 22:53:43', '2020-07-04 11:23:41', 1, 'Q583873'),
(57652, 'Alibag', 4008, 'MH', 101, 'IN', 18.64813000, 72.87579000, '2019-10-05 22:53:43', '2021-02-20 17:13:29', 1, 'Q385394'),
(57653, 'Aliganj', 4022, 'UP', 101, 'IN', 27.49358000, 79.17127000, '2019-10-05 22:53:43', '2021-02-20 17:13:29', 1, 'Q589296'),
(57654, 'Aligarh', 4022, 'UP', 101, 'IN', 27.83333000, 78.16667000, '2019-10-05 22:53:43', '2021-06-06 11:41:13', 1, 'Q201832'),
(57655, 'Alipur', 4021, 'DL', 101, 'IN', 28.79862000, 77.13314000, '2019-10-05 22:53:43', '2021-02-20 17:13:29', 1, 'Q766918'),
(57656, 'Alur', 4026, 'KA', 101, 'IN', 12.97805000, 75.99094000, '2019-10-05 22:53:43', '2021-02-20 17:14:02', 1, '********'),
(57657, 'Amalner', 4008, 'MH', 101, 'IN', 21.03983000, 75.05887000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q589095'),
(57658, 'Amalapuram', 4017, 'AP', 101, 'IN', 16.57868000, 82.00609000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q588625'),
(57659, 'Amarkantak', 4039, 'MP', 101, 'IN', 22.67486000, 81.75908000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588826'),
(57660, 'Amarnath', 4008, 'MH', 101, 'IN', 19.20000000, 73.16667000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q584008'),
(57661, 'Amarpur', 4037, 'BR', 101, 'IN', 25.03967000, 86.90247000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, '********'),
(57662, 'Amarpur', 4038, 'TR', 101, 'IN', 23.52570000, 91.65879000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q589674'),
(57663, 'Amarpatan', 4039, 'MP', 101, 'IN', 24.31371000, 80.97703000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q589830'),
(57664, 'Amarwara', 4039, 'MP', 101, 'IN', 22.29780000, 79.16943000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q585696'),
(57665, 'Ambad', 4008, 'MH', 101, 'IN', 19.61301000, 75.78906000, '2019-10-05 22:53:43', '2021-06-06 13:11:00', 1, 'Q583887'),
(57666, 'Ambahta', 4022, 'UP', 101, 'IN', 29.85706000, 77.33583000, '2019-10-05 22:53:43', '2021-06-06 11:41:13', 1, 'Q26791799'),
(57667, 'Ambasamudram', 4035, 'TN', 101, 'IN', 8.71068000, 77.45190000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585696'),
(57668, 'Ambattur', 4035, 'TN', 101, 'IN', 13.09818000, 80.16152000, '2019-10-05 22:53:43', '2021-02-20 17:14:02', 1, 'Q456737'),
(57669, 'Ambedkar Nagar', 4022, 'UP', 101, 'IN', 26.40544000, 82.69762000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q456764'),
(57670, 'Ambikapur', 4040, 'CT', 101, 'IN', 23.11892000, 83.19537000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q2088480'),
(57671, 'Ambur', 4035, 'TN', 101, 'IN', 12.79163000, 78.71644000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584854'),
(57672, 'Ambagarh Chauki', 4040, 'CT', 101, 'IN', 20.77644000, 80.74608000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q590056'),
(57673, 'Ambah', 4039, 'MP', 101, 'IN', 26.70423000, 78.22678000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q583837'),
(57674, 'Ambajogai', 4008, 'MH', 101, 'IN', 18.73312000, 76.38616000, '2019-10-05 22:53:43', '2021-06-06 13:11:00', 1, 'Q584032'),
(57675, 'Ambala', 4007, 'HR', 101, 'IN', 30.32854000, 76.94220000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q2086226'),
(57676, 'Amet', 4014, 'RJ', 101, 'IN', 25.30609000, 73.92580000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q583756'),
(57677, 'Amethi', 4022, 'UP', 101, 'IN', 28.01667000, 81.05000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q583756'),
(57679, 'Amguri', 4027, 'AS', 101, 'IN', 26.81482000, 94.52614000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585858'),
(57680, 'Amla', 4039, 'MP', 101, 'IN', 21.92485000, 78.12786000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q583603'),
(57681, 'Amloh', 4015, 'PB', 101, 'IN', 30.60837000, 76.23199000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q582278'),
(57682, 'Ammapettai', 4035, 'TN', 101, 'IN', 10.79476000, 79.31986000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q653744'),
(57683, 'Amod', 4030, 'GJ', 101, 'IN', 21.99317000, 72.87047000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2724262'),
(57685, 'Amreli', 4030, 'GJ', 101, 'IN', 21.50789000, 71.18323000, '2019-10-05 22:53:43', '2021-06-06 11:55:54', 1, 'Q589163'),
(57686, 'Amritsar', 4015, 'PB', 101, 'IN', 31.67000000, 74.84000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q202822'),
(57687, 'Amroha', 4022, 'UP', 101, 'IN', 28.90314000, 78.46984000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584515'),
(57688, 'Amroli', 4030, 'GJ', 101, 'IN', 21.25084000, 72.83878000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2722516'),
(57689, 'Amravati', 4008, 'MH', 101, 'IN', 20.93333000, 77.75000000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q269899'),
(57690, 'Amudalavalasa', 4017, 'AP', 101, 'IN', 18.41025000, 83.90295000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q582993'),
(57691, 'Amanganj', 4039, 'MP', 101, 'IN', 24.42664000, 80.03579000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q583720'),
(57692, 'Amanpur', 4022, 'UP', 101, 'IN', 27.71222000, 78.73788000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q582840'),
(57693, 'Anakapalle', 4017, 'AP', 101, 'IN', 17.69134000, 83.00395000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q589133'),
(57694, 'Anamalais', 4035, 'TN', 101, 'IN', 10.58303000, 76.93441000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584411'),
(57695, 'Anand', 4030, 'GJ', 101, 'IN', 22.40000000, 72.75000000, '2019-10-05 22:53:43', '2021-06-06 11:55:54', 1, 'Q1798750'),
(57696, 'Anandnagar', 4022, 'UP', 101, 'IN', 27.10062000, 83.27156000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q581106'),
(57697, 'Anandpur Sahib', 4015, 'PB', 101, 'IN', 31.23926000, 76.50253000, '2019-10-05 22:53:43', '2020-07-04 16:36:55', 1, 'Q589664'),
(57698, 'Anantapur', 4017, 'AP', 101, 'IN', 14.55000000, 77.41667000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q15212'),
(57699, 'Anantnag', 4029, 'JK', 101, 'IN', 33.73068000, 75.15418000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q11360899'),
(57701, 'Andol', 4012, 'TG', 101, 'IN', 17.81458000, 78.07713000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q11360899'),
(57702, 'Anekal', 4026, 'KA', 101, 'IN', 12.71110000, 77.69557000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588701'),
(57703, 'Angamali', 4028, 'KL', 101, 'IN', 10.19055000, 76.38789000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q588701'),
(57704, 'Angul', 4013, 'OR', 101, 'IN', 20.84089000, 85.10192000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q518237'),
(57705, 'Angul District', 4013, 'OR', 101, 'IN', 20.84903000, 85.06079000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q1772807'),
(57706, 'Anjad', 4039, 'MP', 101, 'IN', 22.04171000, 75.05519000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585729'),
(57707, 'Anjangaon', 4008, 'MH', 101, 'IN', 21.16516000, 77.30910000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q583982'),
(57708, 'Anjaw', 4024, 'AR', 101, 'IN', 28.06549000, 96.82878000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q15413'),
(57709, 'Anjar', 4030, 'GJ', 101, 'IN', 23.11316000, 70.02671000, '2019-10-05 22:53:43', '2020-06-14 15:24:08', 1, 'Q1945434'),
(57710, 'Ankleshwar', 4030, 'GJ', 101, 'IN', 21.63236000, 72.99001000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588924'),
(57711, 'Ankola', 4026, 'KA', 101, 'IN', 14.66049000, 74.30470000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q583926'),
(57712, 'Annavasal', 4035, 'TN', 101, 'IN', 10.46060000, 78.70029000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q589776'),
(57713, 'Annigeri', 4026, 'KA', 101, 'IN', 15.42513000, 75.43350000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q589122'),
(57714, 'Annur', 4035, 'TN', 101, 'IN', 11.23616000, 77.10514000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585798'),
(57715, 'Annamalainagar', 4035, 'TN', 101, 'IN', 11.40000000, 79.73333000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q585798'),
(57716, 'Anshing', 4008, 'MH', 101, 'IN', 20.04090000, 77.31501000, '2019-10-05 22:53:43', '2021-06-06 13:11:00', 1, 'Q26791820'),
(57717, 'Anta', 4014, 'RJ', 101, 'IN', 25.15000000, 76.30000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585798'),
(57718, 'Anthiyur', 4035, 'TN', 101, 'IN', 11.57506000, 77.59043000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585742'),
(57719, 'Antri', 4039, 'MP', 101, 'IN', 26.05804000, 78.21027000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q585742'),
(57720, 'Antu', 4022, 'UP', 101, 'IN', 26.05654000, 81.90267000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584960'),
(57721, 'Anuppur', 4039, 'MP', 101, 'IN', 23.05674000, 81.68399000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q2299093'),
(57722, 'Anupgarh', 4014, 'RJ', 101, 'IN', 29.19111000, 73.20861000, '2019-10-05 22:53:43', '2021-02-20 17:14:02', 1, 'Q583742'),
(57724, 'Anupshahr', 4022, 'UP', 101, 'IN', 28.35748000, 78.26914000, '2019-10-05 22:53:43', '2021-02-20 17:14:02', 1, 'Q585153'),
(57725, 'Aonla', 4022, 'UP', 101, 'IN', 28.27402000, 79.16521000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q581791'),
(57726, 'Arakkonam', 4035, 'TN', 101, 'IN', 13.08449000, 79.67053000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584823'),
(57727, 'Arambol', 4009, 'GA', 101, 'IN', 15.68681000, 73.70449000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q584823'),
(57728, 'Arang', 4040, 'CT', 101, 'IN', 21.19639000, 81.96912000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q589854'),
(57729, 'Arantangi', 4035, 'TN', 101, 'IN', 10.17235000, 78.99118000, '2019-10-05 22:53:43', '2021-02-20 17:12:44', 1, 'Q589854'),
(57730, 'Araria', 4037, 'BR', 101, 'IN', 26.20000000, 87.40000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q42901'),
(57731, 'Arcot', 4035, 'TN', 101, 'IN', 12.90569000, 79.31897000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q589185'),
(57732, 'Arimalam', 4035, 'TN', 101, 'IN', 10.25498000, 78.88403000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q582338'),
(57733, 'Ariyalur', 4035, 'TN', 101, 'IN', 11.15000000, 79.********, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q15112'),
(57735, 'Arkalgud', 4026, 'KA', 101, 'IN', 12.76171000, 76.06035000, '2019-10-05 22:53:43', '2021-02-20 17:14:02', 1, 'Q582261'),
(57736, 'Arki', 4020, 'HP', 101, 'IN', 31.15196000, 76.96675000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q582737'),
(57737, 'Arni', 4035, 'TN', 101, 'IN', 12.66771000, 79.28529000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q581497'),
(57738, 'Aroor', 4028, 'KL', 101, 'IN', 9.86940000, 76.30498000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588767'),
(57739, 'Arrah', 4037, 'BR', 101, 'IN', 25.55629000, 84.66335000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588587'),
(57740, 'Arsikere', 4026, 'KA', 101, 'IN', 13.31446000, 76.25704000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588587'),
(57741, 'Artist Village', 4008, 'MH', 101, 'IN', 19.03227000, 73.04276000, '2019-10-05 22:53:43', '2021-06-06 13:11:00', 1, 'Q26794779'),
(57742, 'Arukutti', 4028, 'KL', 101, 'IN', 9.86667000, 76.35000000, '2019-10-05 22:53:43', '2019-10-05 22:53:43', 1, 'Q588587'),
(57743, 'Arumbavur', 4035, 'TN', 101, 'IN', 11.38096000, 78.72965000, '2019-10-05 22:53:44', '2021-02-20 17:14:02', 1, 'Q585033'),
(57744, 'Arumuganeri', 4035, 'TN', 101, 'IN', 8.56880000, 78.09091000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q582527'),
(57745, 'Aruppukkottai', 4035, 'TN', 101, 'IN', 9.50960000, 78.09588000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q584327'),
(57746, 'Aruvankad', 4035, 'TN', 101, 'IN', 11.36315000, 76.75790000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q584458'),
(57747, 'Arwal', 4037, 'BR', 101, 'IN', 25.16158000, 84.69040000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q42917'),
(57749, 'Asarganj', 4037, 'BR', 101, 'IN', 25.15046000, 86.68639000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q589912'),
(57750, 'Ashoknagar', 4039, 'MP', 101, 'IN', 24.58000000, 77.73000000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q2246416'),
(57751, 'Ashta', 4008, 'MH', 101, 'IN', 16.94943000, 74.40936000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q588688'),
(57752, 'Ashta', 4039, 'MP', 101, 'IN', 23.01754000, 76.72208000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q582291'),
(57753, 'Ashti', 4008, 'MH', 101, 'IN', 19.37671000, 76.22520000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q582291'),
(57754, 'Asifabad', 4012, 'TG', 101, 'IN', 19.35851000, 79.28415000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q582291'),
(57755, 'Atarra', 4022, 'UP', 101, 'IN', 25.28618000, 80.57155000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q585869'),
(57756, 'Ateli Mandi', 4007, 'HR', 101, 'IN', 28.10080000, 76.25980000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q590003'),
(57757, 'Athni', 4026, 'KA', 101, 'IN', 16.72613000, 75.06421000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q583995'),
(57758, 'Atmakur', 4017, 'AP', 101, 'IN', 15.88109000, 78.58704000, '2019-10-05 22:53:44', '2021-02-20 17:14:02', 1, 'Q583995'),
(57759, 'Atraulia', 4022, 'UP', 101, 'IN', 26.33330000, 82.94727000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q526685'),
(57760, 'Atrauli', 4022, 'UP', 101, 'IN', 28.02964000, 78.28571000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q585063'),
(57761, 'Attili', 4017, 'AP', 101, 'IN', 16.70000000, 81.60000000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q4818468'),
(57762, 'Attingal', 4028, 'KL', 101, 'IN', 8.69609000, 76.81507000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q534444'),
(57763, 'Attur', 4035, 'TN', 101, 'IN', 11.59414000, 78.60143000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q282637'),
(57764, 'Auraiya', 4022, 'UP', 101, 'IN', 26.64692000, 79.42858000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q582908'),
(57765, 'Aurangabad', 4008, 'MH', 101, 'IN', 19.88467000, 75.33986000, '2019-10-05 22:53:44', '2021-06-06 13:11:00', 1, 'Q200713'),
(57766, 'Aurangabad', 4037, 'BR', 101, 'IN', 24.75204000, 84.37420000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q590550'),
(57767, 'Auroville', 4035, 'TN', 101, 'IN', 12.00549000, 79.80885000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q590361'),
(57768, 'Aurad', 4026, 'KA', 101, 'IN', 18.25397000, 77.41761000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q582573'),
(57769, 'Auras', 4022, 'UP', 101, 'IN', 26.91414000, 80.50792000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q582879'),
(57770, 'Ausa', 4008, 'MH', 101, 'IN', 18.24728000, 76.49930000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q591243'),
(57771, 'Avanigadda', 4017, 'AP', 101, 'IN', 16.02148000, 80.91808000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q3430115'),
(57772, 'Avanoor', 4028, 'KL', 101, 'IN', 10.60826000, 76.17620000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q3430115'),
(57773, 'Avinashi', 4035, 'TN', 101, 'IN', 11.19297000, 77.26865000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q278847'),
(57774, 'Awantipur', 4029, 'JK', 101, 'IN', 33.91978000, 75.01515000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q278847'),
(57775, 'Ayakudi', 4035, 'TN', 101, 'IN', 10.44992000, 77.55198000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q589926'),
(57776, 'Ayyampettai', 4035, 'TN', 101, 'IN', 10.90141000, 79.17984000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q3631399'),
(57777, 'Azamgarh', 4022, 'UP', 101, 'IN', 26.06832000, 83.18358000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q526757'),
(57778, 'Azhikkal', 4028, 'KL', 101, 'IN', 11.91524000, 75.34761000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q526757'),
(57779, 'Baberu', 4022, 'UP', 101, 'IN', 25.54711000, 80.70443000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q590070'),
(57780, 'Babrala', 4022, 'UP', 101, 'IN', 28.26419000, 78.40560000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q589819'),
(57781, 'Babugarh', 4022, 'UP', 101, 'IN', 28.72353000, 77.84677000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q584725'),
(57782, 'Babina', 4022, 'UP', 101, 'IN', 25.23947000, 78.47028000, '2019-10-05 22:53:44', '2021-02-20 17:13:29', 1, 'Q585789'),
(57783, 'Bachhraon', 4022, 'UP', 101, 'IN', 28.92694000, 78.23456000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q582043'),
(57784, 'Bachhrawan', 4022, 'UP', 101, 'IN', 26.47090000, 81.11580000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q582783'),
(57785, 'Bada Barabil', 4013, 'OR', 101, 'IN', 22.11186000, 85.38684000, '2019-10-05 22:53:44', '2021-02-20 17:13:29', 1, 'Q582783'),
(57786, 'Vatakara', 4028, 'KL', 101, 'IN', 11.59776000, 75.58142000, '2019-10-05 22:53:44', '2024-07-07 10:56:10', 1, 'Q2283340'),
(57787, 'Badarpur', 4027, 'AS', 101, 'IN', 24.86852000, 92.59606000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q2563426'),
(57788, 'Badarwas', 4039, 'MP', 101, 'IN', 24.97516000, 77.56490000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q583295'),
(57789, 'Baddi', 4020, 'HP', 101, 'IN', 30.95783000, 76.79136000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q462577'),
(57790, 'Badgam', 4029, 'JK', 101, 'IN', 33.89001000, 74.66297000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, '********'),
(57791, 'Badhni Kalan', 4015, 'PB', 101, 'IN', 30.68130000, 75.29087000, '2019-10-05 22:53:44', '2020-07-04 11:23:44', 1, 'Q585375'),
(57792, 'Badlapur', 4008, 'MH', 101, 'IN', 19.15516000, 73.26553000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q588612'),
(57793, 'Badnawar', 4039, 'MP', 101, 'IN', 23.02181000, 75.23268000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q584989'),
(57794, 'Badvel', 4017, 'AP', 101, 'IN', 14.74510000, 79.06288000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, '********'),
(57795, 'Baga', 4009, 'GA', 101, 'IN', 15.56517000, 73.75517000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, '********'),
(57796, 'Bagaha', 4037, 'BR', 101, 'IN', 27.09918000, 84.09003000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q590593'),
(57797, 'Bagalkot', 4026, 'KA', 101, 'IN', 16.18000000, 75.69000000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, '********'),
(57798, 'Bagar', 4014, 'RJ', 101, 'IN', 28.18784000, 75.50012000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, '********'),
(57799, 'Bagasra', 4030, 'GJ', 101, 'IN', 21.48719000, 70.95516000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q589286'),
(57800, 'Bageshwar', 4016, 'UK', 101, 'IN', 29.97315000, 79.83224000, '2019-10-05 22:53:44', '2024-02-16 18:35:00', 1, 'Q1815313'),
(57801, 'Baghpat', 4022, 'UP', 101, 'IN', 28.95000000, 77.21670000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q624402'),
(57802, 'Bagra', 4025, 'JH', 101, 'IN', 23.73333000, 86.31667000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q1797363'),
(57803, 'Baheri', 4022, 'UP', 101, 'IN', 28.77416000, 79.49740000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q625710'),
(57804, 'Bahjoi', 4022, 'UP', 101, 'IN', 28.39502000, 78.62659000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q632831'),
(57805, 'Bahraich', 4022, 'UP', 101, 'IN', 27.80021000, 81.51855000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q638621'),
(57806, 'Bahraigh', 4022, 'UP', 101, 'IN', 27.57429000, 81.59474000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q638621'),
(57807, 'Bahsuma', 4022, 'UP', 101, 'IN', 29.20063000, 77.97221000, '2019-10-05 22:53:44', '2021-02-20 17:14:02', 1, 'Q287783'),
(57808, 'Bahua', 4022, 'UP', 101, 'IN', 25.83942000, 80.62255000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q26791613'),
(57809, 'Bahadurganj', 4037, 'BR', 101, 'IN', 26.26172000, 87.82443000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q622603'),
(57810, 'Bahadurgarh', 4007, 'HR', 101, 'IN', 28.69287000, 76.93555000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q633471'),
(57811, 'Baihar', 4039, 'MP', 101, 'IN', 22.10133000, 80.54967000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q625392'),
(57812, 'Baikunthpur', 4040, 'CT', 101, 'IN', 23.26206000, 82.56051000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q623920'),
(57813, 'Baikunthpur', 4039, 'MP', 101, 'IN', 24.72768000, 81.40975000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q623920'),
(57814, 'Bail-Hongal', 4026, 'KA', 101, 'IN', 15.81370000, 74.85895000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q636552'),
(57815, 'Bairagnia', 4037, 'BR', 101, 'IN', 26.74063000, 85.27323000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q636552'),
(57816, 'Baisi', 4037, 'BR', 101, 'IN', 25.86302000, 87.74487000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q4848696'),
(57817, 'Bakewar', 4022, 'UP', 101, 'IN', 26.66226000, 79.17625000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q622609'),
(57818, 'Bakhtiyarpur', 4037, 'BR', 101, 'IN', 25.46179000, 85.53179000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q622609'),
(57819, 'Bakloh', 4015, 'PB', 101, 'IN', 32.47939000, 75.91874000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q622831'),
(57820, 'Baksa', 4027, 'AS', 101, 'IN', 26.69804000, 91.15142000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q622831'),
(57821, 'Bakshwaha', 4039, 'MP', 101, 'IN', 24.25106000, 79.28618000, '2019-10-05 22:53:44', '2023-03-25 17:22:34', 1, 'Q622831'),
(57822, 'Bakani', 4014, 'RJ', 101, 'IN', 24.28624000, 76.23709000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q638714'),
(57823, 'Balasore', 4013, 'OR', 101, 'IN', 21.49266000, 86.93348000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q641098'),
(57824, 'Baldeogarh', 4039, 'MP', 101, 'IN', 24.75619000, 79.06715000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q637094'),
(57825, 'Baldev', 4022, 'UP', 101, 'IN', 27.40684000, 77.82214000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q637824'),
(57826, 'Balimila', 4013, 'OR', 101, 'IN', 18.25167000, 82.10659000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q622840'),
(57827, 'Ballari', 4026, 'KA', 101, 'IN', 15.15000000, 76.55000000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q622840'),
(57828, 'Ballia', 4022, 'UP', 101, 'IN', 25.83333000, 84.16667000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q7180187'),
(57829, 'Ballalpur', 4008, 'MH', 101, 'IN', 19.84696000, 79.34578000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q584644'),
(57830, 'Balod', 4040, 'CT', 101, 'IN', 20.73081000, 81.20578000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q921009'),
(57831, 'Baloda', 4040, 'CT', 101, 'IN', 22.13890000, 82.48171000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q622838'),
(57832, 'Baloda Bazar', 4040, 'CT', 101, 'IN', 21.65678000, 82.16062000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q625680'),
(57833, 'Balrampur', 4022, 'UP', 101, 'IN', 27.43449000, 82.40281000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q612328'),
(57835, 'Balangir', 4013, 'OR', 101, 'IN', 20.75000000, 83.********, '2019-10-05 22:53:44', '2021-02-20 17:13:29', 1, 'Q612328'),
(57836, 'Bambolim', 4009, 'GA', 101, 'IN', 15.46361000, 73.85310000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q627037'),
(57837, 'Bamboo Flat', 4023, 'AN', 101, 'IN', 11.70000000, 92.71667000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q627037'),
(57838, 'Bamna', 4039, 'MP', 101, 'IN', 23.09454000, 74.76164000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q627037'),
(57839, 'Bamora', 4039, 'MP', 101, 'IN', 24.05539000, 78.08925000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q636529'),
(57840, 'Banat', 4022, 'UP', 101, 'IN', 29.46355000, 77.35478000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q1320445'),
(57841, 'Banbasa', 4022, 'UP', 101, 'IN', 28.99132000, 80.07608000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q637818'),
(57842, 'Banda', 4039, 'MP', 101, 'IN', 24.04488000, 78.96094000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q637818'),
(57843, 'Bandipore', 4029, 'JK', 101, 'IN', 34.50404000, 74.82832000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q2983553'),
(57845, 'Bandora', 4009, 'GA', 101, 'IN', 15.40823000, 73.98129000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q720252'),
(57846, 'Banga', 4015, 'PB', 101, 'IN', 31.18874000, 75.99495000, '2019-10-05 22:53:44', '2020-07-04 17:33:40', 1, 'Q3634124'),
(57847, 'Bengaluru Rural', 4026, 'KA', 101, 'IN', 13.22567000, 77.57501000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q806464'),
(57848, 'Bengaluru Urban', 4026, 'KA', 101, 'IN', 13.00000000, 77.58333000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q806463'),
(57849, 'Banganapalle', 4017, 'AP', 101, 'IN', 15.31771000, 78.22669000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q1553292'),
(57850, 'Bangaon', 4037, 'BR', 101, 'IN', 25.********, 86.********, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q5266882'),
(57851, 'Bangarapet', 4026, 'KA', 101, 'IN', 12.********, 78.********, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q637792'),
(57852, 'Banihal', 4029, 'JK', 101, 'IN', 33.********, 75.********, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q634172'),
(57853, 'Banjar', 4020, 'HP', 101, 'IN', 31.********, 77.********, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q8180265'),
(57854, 'Banka', 4037, 'BR', 101, 'IN', 24.********, 86.********, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q43097'),
(57855, 'Banmankhi', 4037, 'BR', 101, 'IN', 25.********, 87.********, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q4856831'),
(57856, 'Bannur', 4026, 'KA', 101, 'IN', 12.********, 76.********, '2019-10-05 22:53:44', '2021-02-20 17:14:02', 1, 'Q622613'),
(57857, 'Bantval', 4026, 'KA', 101, 'IN', 12.********, 75.********, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q622613'),
(57858, 'Banas Kantha', 4030, 'GJ', 101, 'IN', 24.********, 72.********, '2019-10-05 22:53:44', '2020-06-14 15:24:21', 1, 'Q622613'),
(57859, 'Banur', 4015, 'PB', 101, 'IN', 30.********, 76.********, '2019-10-05 22:53:44', '2020-07-04 16:31:12', 1, 'Q622613'),
(57860, 'Bar Bigha', 4037, 'BR', 101, 'IN', 25.********, 85.********, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q622613'),
(57861, 'Bara Uchana', 4007, 'HR', 101, 'IN', 29.********, 76.********, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q2557959'),
(57862, 'Baragarh', 4013, 'OR', 101, 'IN', 21.33333000, 83.61667000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q808140'),
(57863, 'Baran', 4014, 'RJ', 101, 'IN', 25.09000000, 76.66000000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q808140'),
(57864, 'Barauli', 4037, 'BR', 101, 'IN', 26.38109000, 84.58648000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q12440787'),
(57865, 'Baraut', 4022, 'UP', 101, 'IN', 29.10199000, 77.26334000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q708505'),
(57866, 'Barbil', 4013, 'OR', 101, 'IN', 22.10194000, 85.37752000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q708505'),
(57867, 'Bareilly', 4022, 'UP', 101, 'IN', 28.41667000, 79.38333000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q213026'),
(57868, 'Barela', 4039, 'MP', 101, 'IN', 23.09678000, 80.05084000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q637110'),
(57869, 'Bargarh', 4013, 'OR', 101, 'IN', 21.33348000, 83.61905000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q637110'),
(57870, 'Barghat', 4039, 'MP', 101, 'IN', 22.03065000, 79.73280000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q613463'),
(57871, 'Bargi', 4039, 'MP', 101, 'IN', 22.99138000, 79.87550000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q613463'),
(57872, 'Barhi', 4039, 'MP', 101, 'IN', 23.90326000, 80.81516000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q709090'),
(57873, 'Barhiya', 4037, 'BR', 101, 'IN', 25.28814000, 86.02055000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q709090'),
(57874, 'Bari Sadri', 4014, 'RJ', 101, 'IN', 24.41339000, 74.47331000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q712396'),
(57875, 'Bariarpur', 4037, 'BR', 101, 'IN', 25.28791000, 86.57643000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q712396'),
(57876, 'Barjala', 4038, 'TR', 101, 'IN', 23.61820000, 91.35596000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q712396'),
(57877, 'Barkhera Kalan', 4022, 'UP', 101, 'IN', 28.45209000, 79.80655000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q712381'),
(57878, 'Barki Saria', 4025, 'JH', 101, 'IN', 24.17594000, 85.88938000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q712381'),
(57879, 'Barkot', 4016, 'UK', 101, 'IN', 30.80861000, 78.20596000, '2019-10-05 22:53:44', '2024-02-16 18:35:00', 1, 'Q2552248'),
(57880, 'Barka Kana', 4025, 'JH', 101, 'IN', 23.62118000, 85.46748000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q2552248'),
(57882, 'Barnala', 4015, 'PB', 101, 'IN', 30.37451000, 75.54870000, '2019-10-05 22:53:44', '2020-07-04 11:23:48', 1, 'Q711996'),
(57883, 'Barpathar', 4027, 'AS', 101, 'IN', 26.28709000, 93.88844000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q707805'),
(57884, 'Barpeta', 4027, 'AS', 101, 'IN', 26.47104000, 91.03080000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q41249'),
(57885, 'Barpeta Road', 4027, 'AS', 101, 'IN', 26.50284000, 90.96937000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q712460'),
(57886, 'Barpali', 4013, 'OR', 101, 'IN', 21.19005000, 83.58721000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q712508'),
(57887, 'Barsana', 4022, 'UP', 101, 'IN', 27.64802000, 77.37640000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q712494'),
(57888, 'Barwani', 4039, 'MP', 101, 'IN', 22.02485000, 74.91805000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q2126754'),
(57889, 'Barwadih', 4025, 'JH', 101, 'IN', 23.84780000, 84.11049000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q712480'),
(57890, 'Barwala', 4007, 'HR', 101, 'IN', 29.36747000, 75.90809000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q712480'),
(57892, 'Baragaon', 4022, 'UP', 101, 'IN', 25.47554000, 78.71224000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q627748'),
(57893, 'Basavakalyan', 4026, 'KA', 101, 'IN', 17.87445000, 76.94972000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q627748'),
(57894, 'Basavana Bagevadi', 4026, 'KA', 101, 'IN', 16.57278000, 75.97252000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q627748'),
(57895, 'Basi', 4014, 'RJ', 101, 'IN', 26.83150000, 76.04856000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q627748'),
(57897, 'Basmat', 4008, 'MH', 101, 'IN', 19.32872000, 77.15746000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q648370'),
(57898, 'Basna', 4040, 'CT', 101, 'IN', 21.27885000, 82.82670000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q708834'),
(57899, 'Basni', 4014, 'RJ', 101, 'IN', 27.17232000, 73.64519000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q708834'),
(57900, 'Bastar', 4040, 'CT', 101, 'IN', 19.26794000, 81.73828000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q100152'),
(57901, 'Basti', 4022, 'UP', 101, 'IN', 26.82816000, 82.77924000, '2019-10-05 22:53:44', '2021-06-06 11:41:13', 1, 'Q2574579'),
(57903, 'Baswa', 4014, 'RJ', 101, 'IN', 27.14955000, 76.58345000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q2574579'),
(57904, 'Bhatinda', 4015, 'PB', 101, 'IN', 30.20747000, 74.93893000, '2019-10-05 22:53:44', '2020-07-04 16:41:27', 1, 'Q33424787'),
(57905, 'Batoti', 4029, 'JK', 101, 'IN', 33.11826000, 75.30889000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q712099'),
(57906, 'Batala', 4015, 'PB', 101, 'IN', 31.80921000, 75.20294000, '2019-10-05 22:53:44', '2020-07-04 11:23:52', 1, 'Q709254'),
(57907, 'Baud', 4013, 'OR', 101, 'IN', 20.83773000, 84.32618000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q303761'),
(57908, 'Baudh', 4013, 'OR', 101, 'IN', 20.83300000, 84.33300000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, '********'),
(57909, 'Bawana', 4021, 'DL', 101, 'IN', 28.79820000, 77.03431000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q760346'),
(57910, 'Bayana', 4014, 'RJ', 101, 'IN', 26.90791000, 77.28985000, '2019-10-05 22:53:44', '2021-02-20 17:12:44', 1, 'Q760257'),
(57911, 'Bedi', 4030, 'GJ', 101, 'IN', 22.50143000, 70.04363000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q516587'),
(57912, 'Beed', 4008, 'MH', 101, 'IN', 18.98921000, 75.75634000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q814033'),
(57913, 'Begamganj', 4039, 'MP', 101, 'IN', 23.59917000, 78.34064000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q760167'),
(57914, 'Begowal', 4015, 'PB', 101, 'IN', 31.61152000, 75.52135000, '2019-10-05 22:53:44', '2020-07-04 11:23:55', 1, 'Q760357'),
(57915, 'Begusarai', 4037, 'BR', 101, 'IN', 25.41853000, 86.13389000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q598985'),
(57917, 'Begun', 4014, 'RJ', 101, 'IN', 24.98333000, 75.00000000, '2019-10-05 22:53:44', '2021-02-20 17:14:02', 1, 'Q759147'),
(57918, 'Behat', 4022, 'UP', 101, 'IN', 30.17180000, 77.61390000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q757842'),
(57919, 'Behror', 4014, 'RJ', 101, 'IN', 27.88832000, 76.28108000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q760169'),
(57920, 'Bela', 4022, 'UP', 101, 'IN', 25.92058000, 81.99629000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q760169'),
(57921, 'Belaguntha', 4013, 'OR', 101, 'IN', 19.88249000, 84.63801000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q2444458'),
(57922, 'Belagavi', 4026, 'KA', 101, 'IN', 16.33333000, 74.75000000, '2019-10-05 22:53:44', '2021-12-11 12:47:33', 1, 'Q815464'),
(57923, 'Bellampalli', 4012, 'TG', 101, 'IN', 19.05577000, 79.49300000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q759677'),
(57925, 'Belluru', 4026, 'KA', 101, 'IN', 12.98140000, 76.73308000, '2019-10-05 22:53:44', '2021-02-20 17:14:02', 1, 'Q429063'),
(57926, 'Belonia', 4038, 'TR', 101, 'IN', 23.25178000, 91.45407000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q757844'),
(57927, 'Belsand', 4037, 'BR', 101, 'IN', 26.44365000, 85.40076000, '2019-10-05 22:53:44', '2019-10-05 22:53:44', 1, 'Q721485'),
(57928, 'Beltangadi', 4026, 'KA', 101, 'IN', 13.98333000, 75.30000000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q721485'),
(57929, 'Belur', 4035, 'TN', 101, 'IN', 11.70752000, 78.41437000, '2019-10-05 22:53:45', '2021-02-20 17:14:02', 1, 'Q721485'),
(57930, 'Belur', 4026, 'KA', 101, 'IN', 13.16558000, 75.86519000, '2019-10-05 22:53:45', '2021-02-20 17:14:02', 1, 'Q760603'),
(57931, 'Bemetara', 4040, 'CT', 101, 'IN', 21.71556000, 81.53423000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q4885104'),
(57932, 'Benaulim', 4009, 'GA', 101, 'IN', 15.26435000, 73.92812000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q2564851'),
(57933, 'Bengaluru', 4026, 'KA', 101, 'IN', 12.97194000, 77.59369000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q1355'),
(57934, 'Beniganj', 4022, 'UP', 101, 'IN', 27.29293000, 80.44364000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q2442072'),
(57935, 'Beohari', 4039, 'MP', 101, 'IN', 24.02423000, 81.37831000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q2314377'),
(57936, 'Berasia', 4039, 'MP', 101, 'IN', 23.63134000, 77.43351000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q2281620'),
(57937, 'Beri Khas', 4007, 'HR', 101, 'IN', 28.70146000, 76.57708000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q2234154'),
(57938, 'Beswan', 4022, 'UP', 101, 'IN', 27.63792000, 77.88019000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q2442016'),
(57939, 'Betamcherla', 4017, 'AP', 101, 'IN', 15.45144000, 78.14797000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q2442016'),
(57940, 'Betma', 4039, 'MP', 101, 'IN', 22.68653000, 75.61456000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q757845'),
(57941, 'Bettiah', 4037, 'BR', 101, 'IN', 26.80229000, 84.50311000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759222');

