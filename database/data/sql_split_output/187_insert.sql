INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(95032, '<PERSON><PERSON><PERSON>', 4732, '<PERSON><PERSON>', 181, 'R<PERSON>', 44.16667000, 26.60000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12131732'),
(95033, '<PERSON><PERSON><PERSON><PERSON>', 4757, 'VL', 181, 'RO', 44.73268000, 24.20910000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2355316'),
(95034, '<PERSON><PERSON><PERSON>', 4729, 'PH', 181, 'RO', 45.01667000, 26.45000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2355316'),
(95035, 'Miș<PERSON>', 4739, 'AR', 181, 'R<PERSON>', 46.60280000, 21.59998000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q1193935'),
(95036, '<PERSON><PERSON><PERSON>', 4723, 'BH', 181, 'R<PERSON>', 47.26291000, 22.25914000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q725226'),
(95037, '<PERSON>len<PERSON>u<PERSON>i', 4740, 'BT', 181, 'RO', 48.12663000, 26.48961000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q12123873'),
(95038, 'Moacşa', 4754, 'CV', 181, 'RO', 45.86667000, 25.96667000, '2019-10-05 23:15:27', '2020-05-01 17:23:09', 1, 'Q3317558'),
(95039, 'Moara Carp', 4720, 'SV', 181, 'RO', 47.56667000, 26.23333000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12123917'),
(95040, 'Moara Domnească', 4752, 'VS', 181, 'RO', 46.71448000, 27.75890000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q12123913'),
(95041, 'Moara Grecilor', 4752, 'VS', 181, 'RO', 46.67145000, 27.74569000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12123910'),
(95042, 'Moara Nica', 4720, 'SV', 181, 'RO', 47.60086000, 26.22262000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12123920'),
(95043, 'Moara Vlăsiei', 4725, 'IF', 181, 'RO', 44.64090000, 26.20616000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q1939409'),
(95044, 'Moceşti', 4729, 'PH', 181, 'RO', 45.05000000, 26.25000000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q12130357'),
(95045, 'Mociu', 4734, 'CJ', 181, 'RO', 46.80000000, 24.03333000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q1076044'),
(95046, 'Modelu', 4732, 'CL', 181, 'RO', 44.19574000, 27.38720000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12124039'),
(95047, 'Moeciu de Jos', 4759, 'BV', 181, 'RO', 45.48333000, 25.31667000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12124039'),
(95048, 'Mofleni', 4742, 'DJ', 181, 'RO', 44.30220000, 23.75848000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12124039'),
(95049, 'Moftinu Mare', 4746, 'SM', 181, 'RO', 47.66667000, 22.66667000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q714053'),
(95050, 'Moftinu Mic', 4746, 'SM', 181, 'RO', 47.68669000, 22.60054000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q839680'),
(95051, 'Mogești', 4757, 'VL', 181, 'RO', 45.11667000, 23.91667000, '2019-10-05 23:15:27', '2020-05-01 17:23:12', 1, 'Q12124070'),
(95052, 'Mogoşani', 4745, 'DB', 181, 'RO', 44.68333000, 25.40000000, '2019-10-05 23:15:27', '2020-05-01 17:23:09', 1, 'Q3726355'),
(95053, 'Mogoşeşti', 4735, 'IS', 181, 'RO', 47.03333000, 27.53333000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q2607474'),
(95054, 'Mogoşeşti-Siret', 4735, 'IS', 181, 'RO', 47.13333000, 26.78333000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q1084772'),
(95055, 'Mogoşoaia', 4725, 'IF', 181, 'RO', 44.52917000, 26.00000000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q2198143'),
(95056, 'Mogoș', 4724, 'AB', 181, 'RO', 46.27554000, 23.28038000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q913650'),
(95057, 'Moieciu de Jos', 4759, 'BV', 181, 'RO', 45.50313000, 25.33719000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q678557'),
(95058, 'Moineşti', 4744, 'BC', 181, 'RO', 46.47523000, 26.48907000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q678557'),
(95059, 'Moisei', 4760, 'MM', 181, 'RO', 47.65562000, 24.54011000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12735837'),
(95060, 'Moișeni', 4746, 'SM', 181, 'RO', 47.92798000, 23.46833000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q726007'),
(95061, 'Moldova Nouă', 4753, 'CS', 181, 'RO', 44.73750000, 21.66694000, '2019-10-05 23:15:27', '2020-05-01 17:23:09', 1, 'Q829051'),
(95062, 'Moldova Suliţa', 4720, 'SV', 181, 'RO', 47.68333000, 25.25000000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q1079473'),
(95063, 'Moldoveneşti', 4734, 'CJ', 181, 'RO', 46.50000000, 23.65000000, '2019-10-05 23:15:27', '2020-05-01 17:23:09', 1, 'Q261616'),
(95064, 'Moldoveni', 4743, 'IL', 181, 'RO', 44.71425000, 26.51879000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2540493'),
(95065, 'Moldoveni', 4728, 'TR', 181, 'RO', 43.77772000, 24.72802000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2540493'),
(95066, 'Moldoveni', 4731, 'NT', 181, 'RO', 46.83333000, 26.76667000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2719011'),
(95067, 'Moldoviţa', 4720, 'SV', 181, 'RO', 47.68333000, 25.53333000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q2465505'),
(95068, 'Moneasa', 4739, 'AR', 181, 'RO', 46.45000000, 22.25000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q1092956'),
(95069, 'Monor', 4733, 'BN', 181, 'RO', 46.96667000, 24.70000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q1077876'),
(95070, 'Moraviţa', 4748, 'TM', 181, 'RO', 45.25583000, 21.27028000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q1079937'),
(95071, 'Moreni', 4745, 'DB', 181, 'RO', 44.98301000, 25.64415000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q16898235'),
(95072, 'Moroeni', 4745, 'DB', 181, 'RO', 45.21667000, 25.43333000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q5070009'),
(95073, 'Morteni', 4745, 'DB', 181, 'RO', 44.66667000, 25.23333000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2606999'),
(95074, 'Morunglav', 4738, 'OT', 181, 'RO', 44.46667000, 24.11667000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2719272'),
(95075, 'Morăreşti', 4722, 'AG', 181, 'RO', 45.01667000, 24.56667000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q2534711'),
(95076, 'Moscu', 4747, 'GL', 181, 'RO', 45.90260000, 27.93120000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12130241'),
(95077, 'Motoşeni', 4744, 'BC', 181, 'RO', 46.33333000, 27.38333000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q15725680'),
(95078, 'Motru', 4750, 'GJ', 181, 'RO', 44.80333000, 22.97194000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q15725680'),
(95079, 'Movila', 4743, 'IL', 181, 'RO', 44.55000000, 27.70000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2291863'),
(95080, 'Movila Banului', 4756, 'BZ', 181, 'RO', 44.98333000, 26.68333000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q3292917'),
(95081, 'Movila Miresii', 4736, 'BR', 181, 'RO', 45.21667000, 27.60000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q8272613'),
(95082, 'Movileni', 4738, 'OT', 181, 'RO', 44.36667000, 24.65000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2719514'),
(95083, 'Movileni', 4735, 'IS', 181, 'RO', 47.31667000, 27.35000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2606002'),
(95084, 'Movileni', 4747, 'GL', 181, 'RO', 45.76154000, 27.37184000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q1159679'),
(95085, 'Moviliţa', 4758, 'VN', 181, 'RO', 45.95000000, 27.10000000, '2019-10-05 23:15:27', '2020-05-01 17:23:12', 1, 'Q2300614'),
(95086, 'Moviliţa', 4743, 'IL', 181, 'RO', 44.65000000, 26.48333000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q2371320'),
(95087, 'Mozăceni', 4722, 'AG', 181, 'RO', 44.56667000, 25.16667000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q2348353'),
(95088, 'Moşna', 4735, 'IS', 181, 'RO', 46.91667000, 27.95000000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q979577'),
(95089, 'Moşna', 4755, 'SB', 181, 'RO', 46.08333000, 24.40000000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q1075766'),
(95090, 'Moşniţa Nouă', 4748, 'TM', 181, 'RO', 45.71722000, 21.32528000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q1195057'),
(95091, 'Moşoaia', 4722, 'AG', 181, 'RO', 44.81667000, 24.76667000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q2534770'),
(95092, 'Moţca', 4735, 'IS', 181, 'RO', 47.25000000, 26.61667000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q1088052'),
(95093, 'Moţăţei', 4742, 'DJ', 181, 'RO', 44.08333000, 23.20000000, '2019-10-05 23:15:27', '2020-05-01 17:23:09', 1, 'Q1953861'),
(95094, 'Moșneni', 4737, 'CT', 181, 'RO', 43.93487000, 28.53132000, '2019-10-05 23:15:27', '2020-05-01 17:23:09', 1, 'Q1179659'),
(95095, 'Moșnița Veche', 4748, 'TM', 181, 'RO', 45.73549000, 21.33396000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q752831'),
(95096, 'Moșteni', 4728, 'TR', 181, 'RO', 44.19787000, 25.50854000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q2721079'),
(95097, 'Moțăieni', 4745, 'DB', 181, 'RO', 45.10000000, 25.41667000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q3726402'),
(95098, 'Muereasca', 4757, 'VL', 181, 'RO', 45.18333000, 24.33333000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, '********'),
(95099, 'Mugeni', 4749, 'HR', 181, 'RO', 46.25000000, 25.21667000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q855021'),
(95100, 'Muncelu de Sus', 4735, 'IS', 181, 'RO', 47.12384000, 26.73518000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, '*********'),
(95101, 'Municipiul  Adjud', 4758, 'VN', 181, 'RO', 46.11464000, 27.19284000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q357784'),
(95102, 'Municipiul  Codlea', 4759, 'BV', 181, 'RO', 45.69944000, 25.44778000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q754733'),
(95103, 'Municipiul  Lupeni', 4721, 'HD', 181, 'RO', 45.35654000, 23.22162000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q754728'),
(95104, 'Municipiul  Topliţa', 4749, 'HR', 181, 'RO', 46.93701000, 25.36052000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q912235'),
(95105, 'Municipiul  Vulcan', 4721, 'HD', 181, 'RO', 45.37985000, 23.27480000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q837964'),
(95106, 'Municipiul Aiud', 4724, 'AB', 181, 'RO', 46.30440000, 23.69751000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q411080'),
(95107, 'Municipiul Alba Iulia', 4724, 'AB', 181, 'RO', 46.06346000, 23.57494000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q174665'),
(95108, 'Municipiul Alexandria', 4728, 'TR', 181, 'RO', 43.96967000, 25.33272000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q209929'),
(95109, 'Municipiul Arad', 4739, 'AR', 181, 'RO', 46.17745000, 21.31348000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q173591'),
(95110, 'Municipiul Bacãu', 4744, 'BC', 181, 'RO', 46.56854000, 26.91135000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q10737004'),
(95112, 'Municipiul Beiuş', 4723, 'BH', 181, 'RO', 46.68074000, 22.34566000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q646065'),
(95113, 'Municipiul Bistriţa', 4733, 'BN', 181, 'RO', 47.13499000, 24.49115000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q187205'),
(95114, 'Municipiul Blaj', 4724, 'AB', 181, 'RO', 46.15084000, 23.91041000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q624236'),
(95115, 'Municipiul Botoşani', 4740, 'BT', 181, 'RO', 47.73984000, 26.67156000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q178855'),
(95116, 'Municipiul Brad', 4721, 'HD', 181, 'RO', 46.14073000, 22.81284000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q841511'),
(95117, 'Municipiul Braşov', 4759, 'BV', 181, 'RO', 45.65053000, 25.60913000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q82174'),
(95118, 'Municipiul Brãila', 4736, 'BR', 181, 'RO', 45.27190000, 27.97500000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q188478'),
(95119, 'Municipiul Buzău', 4756, 'BZ', 181, 'RO', 45.15046000, 26.82136000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q201404'),
(95120, 'Municipiul Bârlad', 4752, 'VS', 181, 'RO', 46.22579000, 27.67070000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q335868'),
(95121, 'Municipiul Bãileşti', 4742, 'DJ', 181, 'RO', 44.02121000, 23.34475000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q756649'),
(95122, 'Municipiul Calafat', 4742, 'DJ', 181, 'RO', 43.98979000, 22.93130000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q652856'),
(95123, 'Municipiul Caracal', 4738, 'OT', 181, 'RO', 44.11273000, 24.35089000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q609044'),
(95124, 'Municipiul Caransebeş', 4753, 'CS', 181, 'RO', 45.43476000, 22.20358000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q33208'),
(95125, 'Municipiul Carei', 4746, 'SM', 181, 'RO', 47.68203000, 22.46635000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q754725'),
(95126, 'Municipiul Cluj-Napoca', 4734, 'CJ', 181, 'RO', 46.77791000, 23.60512000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q100188'),
(95127, 'Municipiul Constanţa', 4737, 'CT', 181, 'RO', 44.21414000, 28.61862000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q79808'),
(95128, 'Municipiul Craiova', 4742, 'DJ', 181, 'RO', 44.31943000, 23.80875000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q168057'),
(95129, 'Municipiul Curtea de Argeș', 4722, 'AG', 181, 'RO', 45.13840000, 24.67512000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q652838'),
(95130, 'Municipiul Câmpia Turzii', 4734, 'CJ', 181, 'RO', 46.54417000, 23.88472000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q458422'),
(95131, 'Municipiul Câmpina', 4729, 'PH', 181, 'RO', 45.12619000, 25.73496000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q756583'),
(95132, 'Municipiul Câmpulung', 4722, 'AG', 181, 'RO', 45.26691000, 25.04419000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q736296'),
(95133, 'Municipiul Câmpulung Moldovenesc', 4720, 'SV', 181, 'RO', 47.52981000, 25.55975000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q652802'),
(95134, 'Municipiul Călăraşi', 4732, 'CL', 181, 'RO', 44.20000000, 27.33333000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q212561'),
(95135, 'Municipiul Dej', 4734, 'CJ', 181, 'RO', 47.12783000, 23.87504000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q273801'),
(95136, 'Municipiul Deva', 4721, 'HD', 181, 'RO', 45.84644000, 22.93123000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q204850'),
(95137, 'Municipiul Dorohoi', 4740, 'BT', 181, 'RO', 47.96772000, 26.41685000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q756587'),
(95138, 'Municipiul Drobeta-Turnu Severin', 4751, 'MH', 181, 'RO', 44.64287000, 22.60669000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q193890'),
(95139, 'Municipiul Drãgãşani', 4757, 'VL', 181, 'RO', 44.64414000, 24.24632000, '2019-10-05 23:15:28', '2020-05-01 17:23:12', 1, 'Q754726'),
(95140, 'Municipiul Feteşti', 4743, 'IL', 181, 'RO', 44.38608000, 27.82483000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q598577'),
(95141, 'Municipiul Focşani', 4758, 'VN', 181, 'RO', 45.69004000, 27.22774000, '2019-10-05 23:15:28', '2020-05-01 17:23:12', 1, 'Q192265'),
(95142, 'Municipiul Fãgãraş', 4759, 'BV', 181, 'RO', 45.84098000, 24.97348000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q685869'),
(95143, 'Municipiul Fãlticeni', 4720, 'SV', 181, 'RO', 47.46189000, 26.31668000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q303015'),
(95144, 'Municipiul Galaţi', 4747, 'GL', 181, 'RO', 45.44078000, 28.04118000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q170996'),
(95145, 'Municipiul Gheorgheni', 4749, 'HR', 181, 'RO', 46.72268000, 25.59917000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q754724'),
(95146, 'Municipiul Gherla', 4734, 'CJ', 181, 'RO', 47.01431000, 23.88134000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q496557'),
(95147, 'Municipiul Giurgiu', 4726, 'GR', 181, 'RO', 43.89051000, 25.96609000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q202351'),
(95148, 'Municipiul Hunedoara', 4721, 'HD', 181, 'RO', 45.76512000, 22.87538000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q215719'),
(95149, 'Municipiul Huşi', 4752, 'VS', 181, 'RO', 46.67320000, 28.05952000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q390162'),
(95150, 'Municipiul Iaşi', 4735, 'IS', 181, 'RO', 47.16184000, 27.58451000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q46852'),
(95151, 'Municipiul Lugoj', 4748, 'TM', 181, 'RO', 45.67678000, 21.95988000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q258981'),
(95152, 'Municipiul Mangalia', 4737, 'CT', 181, 'RO', 43.85085000, 28.59780000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q467498'),
(95153, 'Municipiul Marghita', 4723, 'BH', 181, 'RO', 47.34534000, 22.33452000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q750222'),
(95154, 'Municipiul Medgidia', 4737, 'CT', 181, 'RO', 44.22795000, 28.26801000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q685857'),
(95155, 'Municipiul Mediaş', 4755, 'SB', 181, 'RO', 46.13663000, 24.35377000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q374465'),
(95156, 'Municipiul Miercurea Ciuc', 4749, 'HR', 181, 'RO', 46.36091000, 25.79985000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q193420'),
(95157, 'Municipiul Moineşti', 4744, 'BC', 181, 'RO', 46.47428000, 26.48804000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q476226'),
(95158, 'Municipiul Moreni', 4745, 'DB', 181, 'RO', 44.98010000, 25.64381000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q685811'),
(95159, 'Municipiul Motru', 4750, 'GJ', 181, 'RO', 44.81422000, 22.98229000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q238696'),
(95160, 'Municipiul Odorheiu Secuiesc', 4749, 'HR', 181, 'RO', 46.30646000, 25.29551000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q623099'),
(95161, 'Municipiul Olteniţa', 4732, 'CL', 181, 'RO', 44.09204000, 26.64183000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q737188'),
(95162, 'Municipiul Oneşti', 4744, 'BC', 181, 'RO', 46.25000000, 26.75000000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q543436'),
(95163, 'Municipiul Oradea', 4723, 'BH', 181, 'RO', 47.05353000, 21.93633000, '2019-10-05 23:15:28', '2019-10-05 23:15:28', 1, 'Q93358'),
(95164, 'Municipiul Orãştie', 4721, 'HD', 181, 'RO', 45.83841000, 23.19885000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q262472'),
(95165, 'Municipiul Orşova', 4751, 'MH', 181, 'RO', 44.72520000, 22.39771000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q750243'),
(95166, 'Municipiul Paşcani', 4735, 'IS', 181, 'RO', 47.24736000, 26.71235000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q736305'),
(95167, 'Municipiul Petroşani', 4721, 'HD', 181, 'RO', 45.41963000, 23.35692000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q746041'),
(95168, 'Municipiul Piatra-Neamţ', 4731, 'NT', 181, 'RO', 46.92336000, 26.37380000, '2019-10-05 23:15:28', '2020-05-01 17:23:10', 1, 'Q187945'),
(95169, 'Municipiul Piteşti', 4722, 'AG', 181, 'RO', 44.85782000, 24.87133000, '2019-10-05 23:15:28', '2020-05-01 17:23:08', 1, 'Q187938'),
(95170, 'Municipiul Ploieşti', 4729, 'PH', 181, 'RO', 44.94281000, 26.02116000, '2019-10-05 23:15:28', '2020-05-01 17:23:11', 1, 'Q179585'),
(95171, 'Municipiul Reşiţa', 4753, 'CS', 181, 'RO', 45.29838000, 21.91192000, '2019-10-05 23:15:28', '2020-05-01 17:23:09', 1, 'Q75634'),
(95172, 'Municipiul Roman', 4731, 'NT', 181, 'RO', 46.92973000, 26.93678000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q202458'),
(95173, 'Municipiul Roșiorii de Vede', 4728, 'TR', 181, 'RO', 44.11368000, 24.98722000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q283104'),
(95174, 'Municipiul Râmnicu Sãrat', 4756, 'BZ', 181, 'RO', 45.38324000, 27.05162000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q617624'),
(95175, 'Municipiul Râmnicu Vâlcea', 4757, 'VL', 181, 'RO', 45.09448000, 24.35215000, '2019-10-05 23:15:29', '2020-05-01 17:23:12', 1, 'Q192758'),
(95176, 'Municipiul Rãdãuţi', 4720, 'SV', 181, 'RO', 47.85090000, 25.91570000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q431347'),
(95177, 'Municipiul Salonta', 4723, 'BH', 181, 'RO', 46.80283000, 21.66415000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q754730'),
(95178, 'Municipiul Satu Mare', 4746, 'SM', 181, 'RO', 47.76514000, 22.83565000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q185332'),
(95179, 'Municipiul Sebeş', 4724, 'AB', 181, 'RO', 45.94237000, 23.57681000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q732376'),
(95180, 'Municipiul Sfântu Gheorghe', 4754, 'CV', 181, 'RO', 45.83806000, 25.79730000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q202362'),
(95181, 'Municipiul Sibiu', 4755, 'SB', 181, 'RO', 45.79383000, 24.13533000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q83324'),
(95182, 'Sighetu Marmaţiei', 4760, 'MM', 181, 'RO', 47.91914000, 23.88679000, '2019-10-05 23:15:29', '2021-05-02 17:03:57', 1, 'Q334874'),
(95183, 'Municipiul Slatina', 4738, 'OT', 181, 'RO', 44.44276000, 24.36745000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q207074'),
(95184, 'Municipiul Suceava', 4720, 'SV', 181, 'RO', 47.67457000, 26.28109000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q46400'),
(95185, 'Municipiul Sãcele', 4759, 'BV', 181, 'RO', 45.61772000, 25.69890000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q550055'),
(95186, 'Municipiul Tecuci', 4747, 'GL', 181, 'RO', 45.85155000, 27.42826000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q546686'),
(95187, 'Municipiul Timişoara', 4748, 'TM', 181, 'RO', 45.75641000, 21.22974000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q83404'),
(95188, 'Municipiul Tulcea', 4727, 'TL', 181, 'RO', 45.18325000, 28.79298000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q11585'),
(95189, 'Municipiul Turda', 4734, 'CJ', 181, 'RO', 46.57456000, 23.78574000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q209809'),
(95190, 'Municipiul Turnu Magurele', 4728, 'TR', 181, 'RO', 43.74646000, 24.86893000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q570213'),
(95191, 'Municipiul Târgovişte', 4745, 'DB', 181, 'RO', 44.92616000, 25.45498000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q188673'),
(95192, 'Municipiul Târgu Jiu', 4750, 'GJ', 181, 'RO', 45.05278000, 23.27708000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q202357'),
(95193, 'Municipiul Târgu Secuiesc', 4754, 'CV', 181, 'RO', 46.00589000, 26.17155000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q338066'),
(95194, 'Municipiul Urziceni', 4743, 'IL', 181, 'RO', 44.71842000, 26.64187000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q679928'),
(95195, 'Municipiul Vaslui', 4752, 'VS', 181, 'RO', 46.64683000, 27.73872000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q192269'),
(95196, 'Municipiul Vatra Dornei', 4720, 'SV', 181, 'RO', 47.34443000, 25.33391000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q750239'),
(95197, 'Municipiul Zalãu', 4741, 'SJ', 181, 'RO', 47.16633000, 23.09660000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q145292'),
(95198, 'Muntele Mic', 4753, 'CS', 181, 'RO', 45.38333000, 22.33333000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q145292'),
(95199, 'Munteni', 4735, 'IS', 181, 'RO', 47.31530000, 27.13839000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q12130768'),
(95200, 'Munteni', 4747, 'GL', 181, 'RO', 45.93333000, 27.43333000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q2289304'),
(95201, 'Munteni Buzău', 4743, 'IL', 181, 'RO', 44.63333000, 26.98333000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q12724956'),
(95202, 'Muntenii de Jos', 4752, 'VS', 181, 'RO', 46.61667000, 27.76667000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q2548454'),
(95203, 'Muntenii de Sus', 4752, 'VS', 181, 'RO', 46.68968000, 27.77763000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q2297493'),
(95204, 'Murfatlar', 4737, 'CT', 181, 'RO', 44.18333000, 28.41667000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q2297493'),
(95205, 'Murgași', 4742, 'DJ', 181, 'RO', 44.49968000, 23.86698000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q2043228'),
(95206, 'Murgeni', 4752, 'VS', 181, 'RO', 46.20444000, 28.01972000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q2043228'),
(95207, 'Murgeşti', 4756, 'BZ', 181, 'RO', 45.40000000, 26.88333000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q12130815'),
(95208, 'Murighiol', 4727, 'TL', 181, 'RO', 45.03333000, 29.16667000, '2019-10-05 23:15:29', '2019-10-05 23:15:29', 1, 'Q1066648'),
(95209, 'Muşeniţa', 4720, 'SV', 181, 'RO', 47.96667000, 26.00000000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q369728'),
(95210, 'Muşeteşti', 4750, 'GJ', 181, 'RO', 45.15000000, 23.46667000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q2718386'),
(95211, 'Mușătești', 4722, 'AG', 181, 'RO', 45.21651000, 24.77941000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q2534678'),
(95212, 'Mândra', 4759, 'BV', 181, 'RO', 45.81667000, 25.05000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q15267428'),
(95213, 'Mândrești', 4747, 'GL', 181, 'RO', 45.88333000, 27.70000000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q12123421'),
(95214, 'Mândrești', 4740, 'BT', 181, 'RO', 47.73333000, 26.48333000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q12123419'),
(95215, 'Mândruloc', 4739, 'AR', 181, 'RO', 46.14863000, 21.46570000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q527528'),
(95216, 'Mânzăleşti', 4756, 'BZ', 181, 'RO', 45.50000000, 26.65000000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q12123437'),
(95217, 'Mânăstirea', 4732, 'CL', 181, 'RO', 44.21667000, 26.90000000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q12123431'),
(95218, 'Mânăstirea Caşin', 4744, 'BC', 181, 'RO', 46.15000000, 26.68333000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q15711918'),
(95219, 'Mânăstireni', 4734, 'CJ', 181, 'RO', 46.76667000, 23.08333000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q1077544'),
(95220, 'Mânău', 4760, 'MM', 181, 'RO', 47.48179000, 23.26247000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q746587'),
(95221, 'Mârzăneşti', 4728, 'TR', 181, 'RO', 43.93333000, 25.46667000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q2549798'),
(95222, 'Mârşa', 4726, 'GR', 181, 'RO', 44.37417000, 25.55694000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q2291888'),
(95223, 'Mârşani', 4742, 'DJ', 181, 'RO', 44.01667000, 24.01667000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q773867'),
(95224, 'Mârţeşti', 4722, 'AG', 181, 'RO', 44.71667000, 24.75000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q12123562'),
(95225, 'Mârșa', 4755, 'SB', 181, 'RO', 45.70002000, 24.37046000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q12123562'),
(95226, 'Mâsca', 4739, 'AR', 181, 'RO', 46.30000000, 21.68333000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q683613'),
(95227, 'Măceşu de Jos', 4742, 'DJ', 181, 'RO', 43.88333000, 23.71667000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q2468482'),
(95228, 'Măceşu de Sus', 4742, 'DJ', 181, 'RO', 43.91667000, 23.70000000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q2467020'),
(95229, 'Măcin', 4727, 'TL', 181, 'RO', 45.24371000, 28.13564000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q2467020'),
(95230, 'Măderat', 4739, 'AR', 181, 'RO', 46.29493000, 21.71137000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q714202'),
(95231, 'Mădulari', 4757, 'VL', 181, 'RO', 45.03333000, 24.00000000, '2019-10-05 23:15:29', '2020-05-01 17:23:12', 1, 'Q12122250'),
(95232, 'Mădârjac', 4735, 'IS', 181, 'RO', 47.04994000, 27.26182000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q2718528'),
(95233, 'Mădăras', 4746, 'SM', 181, 'RO', 47.68403000, 22.86554000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q726014'),
(95234, 'Mădăraş', 4723, 'BH', 181, 'RO', 46.83333000, 21.68333000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q1088243'),
(95235, 'Mădăraș', 4749, 'HR', 181, 'RO', 46.49472000, 25.74750000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q875852'),
(95236, 'Măerişte', 4741, 'SJ', 181, 'RO', 47.31667000, 22.80000000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q12698652'),
(95237, 'Măgeşti', 4723, 'BH', 181, 'RO', 47.01667000, 22.45000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q1195369'),
(95238, 'Măgireşti', 4744, 'BC', 181, 'RO', 46.51667000, 26.55000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q15710650'),
(95239, 'Măgura', 4728, 'TR', 181, 'RO', 44.03333000, 25.40000000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q2720649'),
(95240, 'Măgura', 4756, 'BZ', 181, 'RO', 45.26667000, 26.58333000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q3291960'),
(95241, 'Măgura', 4744, 'BC', 181, 'RO', 46.56667000, 26.85000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q3291960'),
(95242, 'Măgura Ilvei', 4733, 'BN', 181, 'RO', 47.38333000, 24.80000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q1161795'),
(95243, 'Măgurele', 4725, 'IF', 181, 'RO', 44.35000000, 26.03333000, '2019-10-05 23:15:29', '2020-05-01 17:23:10', 1, 'Q1161795'),
(95244, 'Măgurele', 4729, 'PH', 181, 'RO', 45.10000000, 26.03333000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q14193686'),
(95245, 'Măgureni', 4729, 'PH', 181, 'RO', 45.06667000, 25.73333000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q14194399'),
(95246, 'Măguri', 4734, 'CJ', 181, 'RO', 46.62849000, 23.10294000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q821296'),
(95247, 'Măguri-Răcătău', 4734, 'CJ', 181, 'RO', 46.64500000, 23.19700000, '2019-10-05 23:15:29', '2020-05-01 17:23:09', 1, 'Q612878'),
(95248, 'Măicăneşti', 4758, 'VN', 181, 'RO', 45.50000000, 27.50000000, '2019-10-05 23:15:29', '2020-05-01 17:23:12', 1, 'Q978451'),
(95249, 'Măieru', 4733, 'BN', 181, 'RO', 47.40000000, 24.75000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q1021599'),
(95250, 'Măieruş', 4759, 'BV', 181, 'RO', 45.90000000, 25.53333000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q15267471'),
(95251, 'Măldăeni', 4728, 'TR', 181, 'RO', 44.11667000, 24.93333000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q2720604'),
(95252, 'Măldăreşti', 4757, 'VL', 181, 'RO', 45.11667000, 24.00000000, '2019-10-05 23:15:29', '2020-05-01 17:23:12', 1, 'Q2347603'),
(95253, 'Mălini', 4720, 'SV', 181, 'RO', 47.46667000, 26.08333000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q1065923'),
(95254, 'Mălureni', 4722, 'AG', 181, 'RO', 45.10000000, 24.80000000, '2019-10-05 23:15:29', '2020-05-01 17:23:08', 1, 'Q2348328'),
(95255, 'Măluşteni', 4752, 'VS', 181, 'RO', 46.18333000, 27.91667000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q2548855'),
(95256, 'Mălâncrav', 4755, 'SB', 181, 'RO', 46.11003000, 24.64810000, '2019-10-05 23:15:29', '2020-05-01 17:23:11', 1, 'Q657068'),
(95257, 'Măneciu-Ungureni', 4729, 'PH', 181, 'RO', 45.31667000, 25.98333000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12122613'),
(95258, 'Măneşti', 4729, 'PH', 181, 'RO', 44.86667000, 25.85000000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q14194591'),
(95259, 'Măneşti', 4745, 'DB', 181, 'RO', 44.96667000, 25.28333000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q2717522'),
(95260, 'Mănăilești', 4757, 'VL', 181, 'RO', 45.02464000, 24.12451000, '2019-10-05 23:15:30', '2020-05-01 17:23:12', 1, 'Q12122597'),
(95261, 'Mănăstirea', 4735, 'IS', 181, 'RO', 46.95000000, 27.20000000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q12122610'),
(95262, 'Mănăstirea Humorului', 4720, 'SV', 181, 'RO', 47.60000000, 25.86667000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q1195099'),
(95263, 'Mănăştiur', 4748, 'TM', 181, 'RO', 45.81667000, 22.06667000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q853952'),
(95264, 'Mănăștur', 4739, 'AR', 181, 'RO', 46.00949000, 21.13362000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q841055'),
(95265, 'Măraşu', 4736, 'BR', 181, 'RO', 44.85000000, 27.96667000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q12122663'),
(95266, 'Mărcești', 4745, 'DB', 181, 'RO', 44.80537000, 25.70951000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12122782'),
(95267, 'Mărculești', 4743, 'IL', 181, 'RO', 44.56698000, 27.51562000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q2291808'),
(95268, 'Mărgineni', 4731, 'NT', 181, 'RO', 46.90000000, 26.63333000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q2541589'),
(95269, 'Mărgineni', 4744, 'BC', 181, 'RO', 46.58760000, 26.85095000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q15712312'),
(95270, 'Mărgineni Slobozia', 4738, 'OT', 181, 'RO', 44.53121000, 24.55675000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12122677'),
(95271, 'Mărgineni-Munteni', 4744, 'BC', 181, 'RO', 46.60000000, 26.90000000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q12122677'),
(95272, 'Mărginenii de Jos', 4729, 'PH', 181, 'RO', 44.95910000, 25.75989000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12122679'),
(95273, 'Mărginenii de Sus', 4745, 'DB', 181, 'RO', 44.93172000, 25.75228000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12122681'),
(95274, 'Mărgăriteşti', 4756, 'BZ', 181, 'RO', 45.43333000, 26.83333000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12122671'),
(95275, 'Mărgău', 4734, 'CJ', 181, 'RO', 46.75000000, 22.96667000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q727447'),
(95276, 'Mărişel', 4734, 'CJ', 181, 'RO', 46.65000000, 23.13333000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q519489'),
(95277, 'Mărişelu', 4733, 'BN', 181, 'RO', 47.01667000, 24.51667000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q1188958'),
(95278, 'Măriței', 4720, 'SV', 181, 'RO', 47.75270000, 26.14626000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12121686'),
(95279, 'Mărtineşti', 4721, 'HD', 181, 'RO', 45.81667000, 23.13333000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q5064776'),
(95280, 'Mărtiniş', 4749, 'HR', 181, 'RO', 46.23333000, 25.38333000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q642273'),
(95281, 'Măru', 4753, 'CS', 181, 'RO', 45.47586000, 22.45039000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q623203'),
(95282, 'Mărunţei', 4738, 'OT', 181, 'RO', 44.20000000, 24.46667000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q678296'),
(95283, 'Mărunțișu', 4756, 'BZ', 181, 'RO', 45.28610000, 26.35421000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12122778'),
(95284, 'Mărăcineni', 4722, 'AG', 181, 'RO', 44.90000000, 24.88333000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q3396114'),
(95285, 'Mărăcineni', 4756, 'BZ', 181, 'RO', 45.20000000, 26.80000000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12122716'),
(95286, 'Mărășești', 4758, 'VN', 181, 'RO', 45.88333000, 27.23333000, '2019-10-05 23:15:30', '2020-05-01 17:23:12', 1, 'Q12122716'),
(95287, 'Măstăcani', 4747, 'GL', 181, 'RO', 45.78333000, 28.03333000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q2717149'),
(95288, 'Mătești', 4756, 'BZ', 181, 'RO', 45.21339000, 26.75607000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12122929'),
(95289, 'Mătăcina', 4758, 'VN', 181, 'RO', 45.88333000, 26.80000000, '2019-10-05 23:15:30', '2020-05-01 17:23:12', 1, 'Q12122928'),
(95290, 'Mătăsari', 4750, 'GJ', 181, 'RO', 44.85000000, 23.08333000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q378063'),
(95291, 'Mătăsaru', 4745, 'DB', 181, 'RO', 44.71667000, 25.41667000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12122922'),
(95292, 'Măureni', 4753, 'CS', 181, 'RO', 45.40389000, 21.50250000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q1076604'),
(95293, 'Măxineni', 4736, 'BR', 181, 'RO', 45.40000000, 27.63333000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q8272849'),
(95294, 'Măzănăești', 4720, 'SV', 181, 'RO', 47.52795000, 26.08401000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12122313'),
(95295, 'Mățău', 4722, 'AG', 181, 'RO', 45.23565000, 25.08286000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q12123146'),
(95296, 'Nadab', 4739, 'AR', 181, 'RO', 46.48195000, 21.51178000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q714199'),
(95297, 'Nadăș', 4739, 'AR', 181, 'RO', 46.22490000, 21.95036000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q755687'),
(95298, 'Naidăș', 4753, 'CS', 181, 'RO', 44.88108000, 21.59061000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q75494'),
(95299, 'Naipu', 4726, 'GR', 181, 'RO', 44.15187000, 25.76175000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12132253'),
(95300, 'Nalbant', 4727, 'TL', 181, 'RO', 45.05000000, 28.61667000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q1091578'),
(95301, 'Nana', 4732, 'CL', 181, 'RO', 44.26667000, 26.58333000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12724986'),
(95302, 'Nanov', 4728, 'TR', 181, 'RO', 44.00000000, 25.30000000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q2549817'),
(95303, 'Neagra Șarului', 4720, 'SV', 181, 'RO', 47.23907000, 25.32923000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12134537'),
(95304, 'Necşeşti', 4728, 'TR', 181, 'RO', 44.25000000, 25.15000000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q2721072'),
(95305, 'Nedeia', 4742, 'DJ', 181, 'RO', 43.84992000, 23.77643000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12133179'),
(95306, 'Nedelea', 4729, 'PH', 181, 'RO', 44.97191000, 25.81647000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12133177'),
(95307, 'Negoești', 4732, 'CL', 181, 'RO', 44.23065000, 26.50151000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q13571294'),
(95308, 'Negoi', 4742, 'DJ', 181, 'RO', 43.91396000, 23.37288000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q2468531'),
(95309, 'Negoiești', 4729, 'PH', 181, 'RO', 44.87393000, 25.98160000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12133124'),
(95310, 'Negoiești', 4742, 'DJ', 181, 'RO', 44.53584000, 23.72690000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12133124'),
(95311, 'Negomir', 4750, 'GJ', 181, 'RO', 44.83333000, 23.16667000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q2718321'),
(95312, 'Negostina', 4720, 'SV', 181, 'RO', 47.92282000, 26.08209000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12133116'),
(95313, 'Negraşi', 4722, 'AG', 181, 'RO', 44.60000000, 25.11667000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q12133126'),
(95314, 'Negreni', 4734, 'CJ', 181, 'RO', 46.95378000, 22.76347000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q1087809'),
(95315, 'Negreni', 4738, 'OT', 181, 'RO', 44.56848000, 24.59535000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12133135'),
(95316, 'Negreşti', 4752, 'VS', 181, 'RO', 46.83333000, 27.43333000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12133135'),
(95317, 'Negreşti-Oaş', 4746, 'SM', 181, 'RO', 47.86667000, 23.43333000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q12133135'),
(95318, 'Negrești', 4731, 'NT', 181, 'RO', 47.02938000, 26.37958000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q2358235'),
(95319, 'Negri', 4744, 'BC', 181, 'RO', 46.70000000, 26.96667000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q15726632'),
(95320, 'Negrileasa', 4720, 'SV', 181, 'RO', 47.42348000, 25.81617000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12133159'),
(95321, 'Negrilești', 4758, 'VN', 181, 'RO', 45.93697000, 26.70635000, '2019-10-05 23:15:30', '2020-05-01 17:23:12', 1, 'Q12724992'),
(95322, 'Negrilești', 4733, 'BN', 181, 'RO', 47.27068000, 24.04936000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q1084745'),
(95323, 'Negrilești', 4747, 'GL', 181, 'RO', 45.95729000, 27.48085000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q2539215'),
(95324, 'Negru Vodă', 4737, 'CT', 181, 'RO', 43.81667000, 28.20000000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q2539215'),
(95325, 'Nehoiu', 4756, 'BZ', 181, 'RO', 45.41667000, 26.30000000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q2539215'),
(95326, 'Nemțișor', 4731, 'NT', 181, 'RO', 47.23962000, 26.27209000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q12133386'),
(95327, 'Nenciulești', 4728, 'TR', 181, 'RO', 44.03486000, 25.20061000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q933609'),
(95328, 'Nepos', 4733, 'BN', 181, 'RO', 47.27750000, 24.53320000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q3892327'),
(95329, 'Nereju', 4758, 'VN', 181, 'RO', 45.71667000, 26.71667000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q5070098'),
(95330, 'Nereju Mic', 4758, 'VN', 181, 'RO', 45.70618000, 26.69641000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12133486'),
(95331, 'Nerău', 4748, 'TM', 181, 'RO', 45.96929000, 20.56070000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q777829'),
(95332, 'Neudorf', 4739, 'AR', 181, 'RO', 46.06960000, 21.61899000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q572409'),
(95333, 'Nicolae Bălcescu', 4740, 'BT', 181, 'RO', 47.56664000, 26.89629000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q572409'),
(95334, 'Nicolae Bălcescu', 4751, 'MH', 181, 'RO', 44.37427000, 22.85837000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q572409'),
(95335, 'Nicolae Bălcescu', 4727, 'TL', 181, 'RO', 44.99714000, 28.58757000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q6273300'),
(95336, 'Nicolae Bălcescu', 4732, 'CL', 181, 'RO', 44.45000000, 26.76667000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q12134618'),
(95337, 'Nicolae Bălcescu', 4737, 'CT', 181, 'RO', 44.38333000, 28.38333000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q1092725'),
(95338, 'Nicolae Bălcescu', 4744, 'BC', 181, 'RO', 46.46667000, 26.91667000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q15728384'),
(95339, 'Nicolae Titulescu', 4738, 'OT', 181, 'RO', 44.30000000, 24.80000000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q949508'),
(95340, 'Nicolești', 4749, 'HR', 181, 'RO', 46.43918000, 25.84446000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q569924'),
(95341, 'Nicoreşti', 4747, 'GL', 181, 'RO', 45.93223000, 27.30866000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q2289436'),
(95342, 'Niculești', 4745, 'DB', 181, 'RO', 44.68266000, 25.94221000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q5065881'),
(95343, 'Niculiţel', 4727, 'TL', 181, 'RO', 45.18333000, 28.48333000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q953724'),
(95344, 'Nicşeni', 4740, 'BT', 181, 'RO', 47.86667000, 26.63333000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q2715840'),
(95345, 'Nimigea de Jos', 4733, 'BN', 181, 'RO', 47.25357000, 24.30084000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q740558'),
(95346, 'Nimigea de Sus', 4733, 'BN', 181, 'RO', 47.28333000, 24.31667000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q3890604'),
(95347, 'Nireș', 4734, 'CJ', 181, 'RO', 47.11499000, 23.98092000, '2019-10-05 23:15:30', '2020-05-01 17:23:09', 1, 'Q660611'),
(95348, 'Nisipari', 4737, 'CT', 181, 'RO', 44.25964000, 28.39840000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12134750'),
(95349, 'Nisiporești', 4731, 'NT', 181, 'RO', 47.07720000, 26.73791000, '2019-10-05 23:15:30', '2020-05-01 17:23:10', 1, 'Q12134754'),
(95350, 'Nistoreşti', 4758, 'VN', 181, 'RO', 45.83333000, 26.73333000, '2019-10-05 23:15:30', '2020-05-01 17:23:12', 1, 'Q12134749'),
(95351, 'Nistru', 4760, 'MM', 181, 'RO', 47.72088000, 23.45495000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12134749'),
(95352, 'Niţchidorf', 4748, 'TM', 181, 'RO', 45.58333000, 21.53333000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, 'Q939009'),
(95353, 'Nocrich', 4755, 'SB', 181, 'RO', 45.90000000, 24.45000000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q1096317'),
(95354, 'Nojorid', 4723, 'BH', 181, 'RO', 46.98333000, 21.88333000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q1194291'),
(95355, 'Nou', 4755, 'SB', 181, 'RO', 45.83333000, 24.28333000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q3891975'),
(95356, 'Novaci', 4726, 'GR', 181, 'RO', 44.30109000, 25.98632000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q12133890'),
(95357, 'Novaci', 4750, 'GJ', 181, 'RO', 45.16462000, 23.66839000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q386091'),
(95358, 'Noşlac', 4724, 'AB', 181, 'RO', 46.40000000, 23.93333000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q949496'),
(95359, 'Nucet', 4745, 'DB', 181, 'RO', 44.78333000, 25.55000000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q2606980'),
(95360, 'Nucet', 4723, 'BH', 181, 'RO', 46.48732000, 22.55850000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q2606980'),
(95361, 'Nuci', 4725, 'IF', 181, 'RO', 44.71667000, 26.30000000, '2019-10-05 23:15:30', '2019-10-05 23:15:30', 1, 'Q1656404'),
(95362, 'Nucşoara', 4722, 'AG', 181, 'RO', 45.33333000, 24.78333000, '2019-10-05 23:15:30', '2020-05-01 17:23:08', 1, 'Q12134488'),
(95363, 'Nufăru', 4727, 'TL', 181, 'RO', 45.15000000, 28.91667000, '2019-10-05 23:15:30', '2020-05-01 17:23:11', 1, '********'),
(95364, 'Nuntași', 4737, 'CT', 181, 'RO', 44.53755000, 28.64880000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, '********'),
(95365, 'Nuşeni', 4733, 'BN', 181, 'RO', 47.10000000, 24.20000000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q773323'),
(95366, 'Nuşfalău', 4741, 'SJ', 181, 'RO', 47.20000000, 22.73333000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, '********'),
(95367, 'Nădlac', 4739, 'AR', 181, 'RO', 46.16667000, 20.75000000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, '********'),
(95368, 'Nădrag', 4748, 'TM', 181, 'RO', 45.65000000, 22.18333000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, '********'),
(95369, 'Năeni', 4756, 'BZ', 181, 'RO', 45.10000000, 26.48333000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, '********'),
(95370, 'Nămoloasa', 4747, 'GL', 181, 'RO', 45.53610000, 27.55226000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, '********'),
(95371, 'Nămoloasa-Sat', 4747, 'GL', 181, 'RO', 45.53333000, 27.58333000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, '*********'),
(95372, 'Năneşti', 4758, 'VN', 181, 'RO', 45.55000000, 27.50000000, '2019-10-05 23:15:31', '2020-05-01 17:23:12', 1, '*********'),
(95373, 'Năpradea', 4741, 'SJ', 181, 'RO', 47.36667000, 23.31667000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, '********'),
(95374, 'Năruja', 4758, 'VN', 181, 'RO', 45.83333000, 26.78333000, '2019-10-05 23:15:31', '2020-05-01 17:23:12', 1, 'Q5070164'),
(95375, 'Năsturelu', 4728, 'TR', 181, 'RO', 43.66667000, 25.46667000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, 'Q2721053'),
(95376, 'Năsăud', 4733, 'BN', 181, 'RO', 47.28333000, 24.40000000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q2721053'),
(95377, 'Năvodari', 4737, 'CT', 181, 'RO', 44.31667000, 28.60000000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, 'Q2721053'),
(95378, 'Năvodari', 4728, 'TR', 181, 'RO', 43.74208000, 25.09410000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, 'Q12133081'),
(95379, 'Oancea', 4747, 'GL', 181, 'RO', 45.91667000, 28.10000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2717167'),
(95380, 'Oanțu', 4731, 'NT', 181, 'RO', 46.90970000, 26.19887000, '2019-10-05 23:15:31', '2020-05-01 17:23:10', 1, 'Q12134798'),
(95381, 'Oar', 4746, 'SM', 181, 'RO', 47.80569000, 22.73085000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q528120'),
(95382, 'Oarda', 4724, 'AB', 181, 'RO', 46.03333000, 23.56667000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q528120'),
(95383, 'Oarja', 4722, 'AG', 181, 'RO', 44.76053000, 24.97596000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2348214'),
(95384, 'Oarja Sat', 4722, 'AG', 181, 'RO', 44.76429000, 24.97141000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2348214'),
(95385, 'Oarţa de Jos', 4760, 'MM', 181, 'RO', 47.45827000, 23.12742000, '2019-10-05 23:15:31', '2020-05-01 17:23:10', 1, 'Q679970'),
(95386, 'Oboga', 4738, 'OT', 181, 'RO', 44.41667000, 24.10000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2542001'),
(95387, 'Obreja', 4753, 'CS', 181, 'RO', 45.48333000, 22.25000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1084698'),
(95388, 'Obrejița', 4758, 'VN', 181, 'RO', 45.50102000, 27.08974000, '2019-10-05 23:15:31', '2020-05-01 17:23:12', 1, 'Q3880618'),
(95389, 'Obârşia', 4738, 'OT', 181, 'RO', 43.88333000, 24.33333000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, 'Q2719483'),
(95390, 'Obârşia-Cloşani', 4751, 'MH', 181, 'RO', 45.01667000, 22.68333000, '2019-10-05 23:15:31', '2020-05-01 17:23:10', 1, 'Q12134898'),
(95391, 'Obârșia de Câmp', 4751, 'MH', 181, 'RO', 44.17222000, 22.98000000, '2019-10-05 23:15:31', '2020-05-01 17:23:10', 1, 'Q12134899'),
(95392, 'Ocina de Sus', 4729, 'PH', 181, 'RO', 45.20060000, 25.59882000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12137064'),
(95393, 'Ocland', 4749, 'HR', 181, 'RO', 46.16667000, 25.41667000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1096376'),
(95394, 'Ocna Dejului', 4734, 'CJ', 181, 'RO', 47.11581000, 23.85934000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q747230'),
(95395, 'Ocna Mureş', 4724, 'AB', 181, 'RO', 46.38333000, 23.85000000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q16898078'),
(95396, 'Ocna Sibiului', 4755, 'SB', 181, 'RO', 45.88333000, 24.05000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q16898078'),
(95397, 'Ocna de Jos', 4749, 'HR', 181, 'RO', 46.52635000, 25.12945000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q679619'),
(95398, 'Ocna de Sus', 4749, 'HR', 181, 'RO', 46.52729000, 25.15131000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q713690'),
(95399, 'Ocna Şugatag', 4760, 'MM', 181, 'RO', 47.78333000, 23.93333000, '2019-10-05 23:15:31', '2020-05-01 17:23:10', 1, 'Q556224'),
(95400, 'Ocniţa', 4745, 'DB', 181, 'RO', 44.98333000, 25.55000000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, 'Q2717154'),
(95401, 'Ocnița', 4733, 'BN', 181, 'RO', 46.86266000, 24.48677000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q546953'),
(95402, 'Ocoliş', 4724, 'AB', 181, 'RO', 46.48333000, 23.46667000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q1191542'),
(95403, 'Ocolna', 4742, 'DJ', 181, 'RO', 43.87917000, 24.13405000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135437'),
(95404, 'Odaia Manolache', 4747, 'GL', 181, 'RO', 45.54897000, 27.99057000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135192'),
(95405, 'Odaia Turcului', 4745, 'DB', 181, 'RO', 44.69777000, 25.45082000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135194'),
(95406, 'Odobeşti', 4745, 'DB', 181, 'RO', 44.61667000, 25.56667000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, 'Q3726367'),
(95407, 'Odobeşti', 4758, 'VN', 181, 'RO', 45.76667000, 27.05000000, '2019-10-05 23:15:31', '2020-05-01 17:23:12', 1, 'Q3726367'),
(95408, 'Odobești', 4744, 'BC', 181, 'RO', 46.67608000, 27.14983000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q15728668'),
(95409, 'Odoreu', 4746, 'SM', 181, 'RO', 47.80000000, 23.00000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1093639'),
(95410, 'Odorheiu Secuiesc', 4749, 'HR', 181, 'RO', 46.30000000, 25.30000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1093639'),
(95411, 'Odăile', 4756, 'BZ', 181, 'RO', 45.38333000, 26.55000000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, 'Q3293904'),
(95412, 'Oeștii Pământeni', 4722, 'AG', 181, 'RO', 45.25241000, 24.65658000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q12137101'),
(95413, 'Oglinzi', 4731, 'NT', 181, 'RO', 47.25055000, 26.35026000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135133'),
(95414, 'Ograda', 4743, 'IL', 181, 'RO', 44.61457000, 27.57047000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2539099'),
(95415, 'Ogretin', 4729, 'PH', 181, 'RO', 45.26483000, 26.09169000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135166'),
(95416, 'Ogrezeni', 4726, 'GR', 181, 'RO', 44.41972000, 25.76833000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2717809'),
(95417, 'Ohaba', 4724, 'AB', 181, 'RO', 46.06667000, 23.78333000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q383997'),
(95418, 'Ohaba Lungă', 4748, 'TM', 181, 'RO', 45.90740000, 21.98535000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, 'Q1055878'),
(95419, 'Oinacu', 4726, 'GR', 181, 'RO', 43.95000000, 26.01667000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2717766'),
(95420, 'Oituz', 4744, 'BC', 181, 'RO', 46.20000000, 26.61667000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q15728749'),
(95421, 'Ojdula', 4754, 'CV', 181, 'RO', 45.98333000, 26.25000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1092880'),
(95422, 'Olanu', 4757, 'VL', 181, 'RO', 44.86667000, 24.30000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q431328'),
(95423, 'Olari', 4729, 'PH', 181, 'RO', 44.78482000, 26.21728000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q14194716'),
(95424, 'Olari', 4739, 'AR', 181, 'RO', 46.38333000, 21.55000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1193952'),
(95425, 'Olari', 4738, 'OT', 181, 'RO', 44.30783000, 24.20857000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135525'),
(95426, 'Olcea', 4723, 'BH', 181, 'RO', 46.68333000, 21.98333000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1195216'),
(95427, 'Oleșești', 4758, 'VN', 181, 'RO', 45.84781000, 27.10097000, '2019-10-05 23:15:31', '2020-05-01 17:23:12', 1, 'Q12135737'),
(95428, 'Ologeni', 4729, 'PH', 181, 'RO', 44.73370000, 25.95059000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135760'),
(95429, 'Olteanca', 4728, 'TR', 181, 'RO', 43.83047000, 24.79597000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135779'),
(95430, 'Olteneşti', 4752, 'VS', 181, 'RO', 46.58333000, 27.90000000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, 'Q2548837'),
(95431, 'Olteni', 4725, 'IF', 181, 'RO', 44.39307000, 25.95021000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135765'),
(95432, 'Olteni', 4728, 'TR', 181, 'RO', 44.18333000, 25.28333000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2721321'),
(95433, 'Olteniţa', 4732, 'CL', 181, 'RO', 44.08333000, 26.63333000, '2019-10-05 23:15:31', '2020-05-01 17:23:09', 1, 'Q2721321'),
(95434, 'Oltina', 4737, 'CT', 181, 'RO', 44.16667000, 27.66667000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q1188214'),
(95435, 'Olănești', 4757, 'VL', 181, 'RO', 45.18209000, 24.25990000, '2019-10-05 23:15:31', '2020-05-01 17:23:12', 1, 'Q1188214'),
(95436, 'Onceşti', 4744, 'BC', 181, 'RO', 46.46667000, 27.25000000, '2019-10-05 23:15:31', '2020-05-01 17:23:08', 1, 'Q15728792'),
(95437, 'Oncești', 4760, 'MM', 181, 'RO', 47.84814000, 23.98183000, '2019-10-05 23:15:31', '2020-05-01 17:23:10', 1, 'Q748214'),
(95438, 'Oneaga', 4740, 'BT', 181, 'RO', 47.57626000, 26.73244000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12135997'),
(95439, 'Onesti', 4744, 'BC', 181, 'RO', 46.25000000, 26.75000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q16898107'),
(95440, 'Oniceni', 4720, 'SV', 181, 'RO', 47.32441000, 26.46217000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q12136009'),
(95441, 'Oniceni', 4731, 'NT', 181, 'RO', 46.81667000, 27.15000000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2605760'),
(95442, 'Oporelu', 4738, 'OT', 181, 'RO', 44.60000000, 24.41667000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q2719489'),
(95443, 'Oprişor', 4751, 'MH', 181, 'RO', 44.28333000, 23.08333000, '2019-10-05 23:15:31', '2020-05-01 17:23:10', 1, 'Q2190792'),
(95444, 'Optaşi', 4738, 'OT', 181, 'RO', 44.58333000, 24.65000000, '2019-10-05 23:15:31', '2020-05-01 17:23:11', 1, 'Q12737214'),
(95445, 'Oradea', 4723, 'BH', 181, 'RO', 47.04580000, 21.91833000, '2019-10-05 23:15:31', '2019-10-05 23:15:31', 1, 'Q16898113'),
(95446, 'Oraviţa', 4753, 'CS', 181, 'RO', 45.03250000, 21.68944000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q16898113'),
(95447, 'Oraş Agnita', 4755, 'SB', 181, 'RO', 45.98952000, 24.61458000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q313689'),
(95448, 'Oraş Amara', 4743, 'IL', 181, 'RO', 44.61793000, 27.33411000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q276009'),
(95449, 'Oraş Anina', 4753, 'CS', 181, 'RO', 45.07683000, 21.85213000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q548502'),
(95450, 'Oraş Aninoasa', 4721, 'HD', 181, 'RO', 45.39316000, 23.33020000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q548575'),
(95451, 'Oraş Avrig', 4755, 'SB', 181, 'RO', 45.73797000, 24.38713000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q791387'),
(95452, 'Oraş Azuga', 4729, 'PH', 181, 'RO', 45.44512000, 25.55616000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q535516'),
(95453, 'Oraş Babadag', 4727, 'TL', 181, 'RO', 44.89092000, 28.71858000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q386945'),
(95454, 'Oraş Baia De Aramã', 4751, 'MH', 181, 'RO', 45.00366000, 22.80709000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q778516'),
(95456, 'Oraş Baia de Arieş', 4724, 'AB', 181, 'RO', 46.37933000, 23.29903000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q775799'),
(95457, 'Oraş Balş', 4738, 'OT', 181, 'RO', 44.36546000, 24.11321000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q590443'),
(95458, 'Oraş Baraolt', 4754, 'CV', 181, 'RO', 46.06250000, 25.60505000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q533078'),
(95459, 'Oraş Bechet', 4742, 'DJ', 181, 'RO', 43.78301000, 23.95870000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q253872'),
(95460, 'Oraş Beclean', 4733, 'BN', 181, 'RO', 47.16124000, 24.17385000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q794200'),
(95461, 'Oraş Berbeşti', 4757, 'VL', 181, 'RO', 44.99915000, 23.88013000, '2019-10-05 23:15:32', '2020-05-01 17:23:12', 1, 'Q276156'),
(95462, 'Oraş Bereşti', 4747, 'GL', 181, 'RO', 46.09648000, 27.88623000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q771191'),
(95463, 'Oraş Bicaz', 4731, 'NT', 181, 'RO', 46.94890000, 26.07982000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q225100'),
(95464, 'Oraş Bocşa', 4753, 'CS', 181, 'RO', 45.37570000, 21.71483000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q75504'),
(95465, 'Oraş Boldeşti-Scãeni', 4729, 'PH', 181, 'RO', 45.02458000, 26.04574000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q571769'),
(95466, 'Oraş Bolintin-Vale', 4726, 'GR', 181, 'RO', 44.44356000, 25.73930000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q834101'),
(95467, 'Oraş Borsec', 4749, 'HR', 181, 'RO', 46.96551000, 25.56173000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q839796'),
(95469, 'Oraş Bragadiru', 4725, 'IF', 181, 'RO', 44.36998000, 25.97871000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q168921'),
(95470, 'Oraş Breaza', 4729, 'PH', 181, 'RO', 45.18710000, 25.65851000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q568376'),
(95471, 'Oraş Brezoi', 4757, 'VL', 181, 'RO', 45.35088000, 24.25681000, '2019-10-05 23:15:32', '2020-05-01 17:23:12', 1, 'Q526019'),
(95472, 'Oraş Broşteni', 4720, 'SV', 181, 'RO', 47.23673000, 25.70028000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q10860283'),
(95473, 'Oraş Bucecea', 4740, 'BT', 181, 'RO', 47.77741000, 26.43966000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q276146'),
(95474, 'Oraş Budeşti', 4732, 'CL', 181, 'RO', 44.24766000, 26.45099000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q851409'),
(95475, 'Oraş Buftea', 4725, 'IF', 181, 'RO', 44.55475000, 25.95628000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q217131'),
(95476, 'Oraş Buhuşi', 4744, 'BC', 181, 'RO', 46.71845000, 26.69952000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q756585'),
(95477, 'Oraş Bumbeşti-Jiu', 4750, 'GJ', 181, 'RO', 45.14429000, 23.38185000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q584531'),
(95478, 'Oraş Buziaş', 4748, 'TM', 181, 'RO', 45.64035000, 21.58934000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q655818'),
(95479, 'Oraş Buşteni', 4729, 'PH', 181, 'RO', 45.40570000, 25.53969000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q265012'),
(95480, 'Oraş Bãbeni', 4757, 'VL', 181, 'RO', 44.97023000, 24.22555000, '2019-10-05 23:15:32', '2020-05-01 17:23:12', 1, 'Q275890'),
(95481, 'Oraş Bãicoi', 4729, 'PH', 181, 'RO', 45.04160000, 25.86964000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q283862'),
(95482, 'Oraş Bãile Govora', 4757, 'VL', 181, 'RO', 45.08174000, 24.18246000, '2019-10-05 23:15:32', '2020-05-01 17:23:12', 1, 'Q855719'),
(95483, 'Oraş Bãile Herculane', 4753, 'CS', 181, 'RO', 44.87326000, 22.41613000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q650981'),
(95484, 'Oraş Bãile Olãneşti', 4757, 'VL', 181, 'RO', 45.20059000, 24.23416000, '2019-10-05 23:15:32', '2020-05-01 17:23:12', 1, 'Q855721'),
(95485, 'Oraş Bãile Tuşnad', 4749, 'HR', 181, 'RO', 46.14707000, 25.86013000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q837968'),
(95486, 'Oraş Bãlan', 4749, 'HR', 181, 'RO', 46.65740000, 25.80554000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q789811'),
(95487, 'Oraş Bãlceşti', 4757, 'VL', 181, 'RO', 44.62941000, 23.94052000, '2019-10-05 23:15:32', '2020-05-01 17:23:12', 1, 'Q276127'),
(95488, 'Oraş Bãneasa', 4737, 'CT', 181, 'RO', 44.05008000, 27.71430000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q276042'),
(95489, 'Oraş Cajvana', 4720, 'SV', 181, 'RO', 47.71394000, 25.99186000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q275810'),
(95490, 'Oraş Cernavodã', 4737, 'CT', 181, 'RO', 44.34331000, 28.03694000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q736276'),
(95491, 'Oraş Chitila', 4725, 'IF', 181, 'RO', 44.49053000, 25.97847000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q169127'),
(95492, 'Oraş Chişineu-Criş', 4739, 'AR', 181, 'RO', 46.52178000, 21.51295000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q508670'),
(95493, 'Oraş Ciacova', 4748, 'TM', 181, 'RO', 45.53242000, 21.10558000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q325736'),
(95494, 'Oraş Cisnãdie', 4755, 'SB', 181, 'RO', 45.70924000, 24.13284000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q837173'),
(95495, 'Oraş Comarnic', 4729, 'PH', 181, 'RO', 45.24962000, 25.63859000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q851037'),
(95496, 'Oraş Comãneşti', 4744, 'BC', 181, 'RO', 46.42348000, 26.42796000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q756590'),
(95497, 'Oraş Copşa Micã', 4755, 'SB', 181, 'RO', 46.11651000, 24.25517000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q850962'),
(95498, 'Oraş Corabia', 4738, 'OT', 181, 'RO', 43.80248000, 24.47028000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q754721'),
(95499, 'Oraş Covasna', 4754, 'CV', 181, 'RO', 45.83527000, 26.16637000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q837157'),
(95500, 'Oraş Cristuru Secuiesc', 4749, 'HR', 181, 'RO', 46.28390000, 25.04583000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q851056'),
(95501, 'Oraş Cugir', 4724, 'AB', 181, 'RO', 45.81371000, 23.40740000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q221214'),
(95502, 'Oraş Curtici', 4739, 'AR', 181, 'RO', 46.34358000, 21.31051000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q850982'),
(95503, 'Oraş Câmpeni', 4724, 'AB', 181, 'RO', 46.38493000, 23.04553000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q855187'),
(95504, 'Oraş Cãlan', 4721, 'HD', 181, 'RO', 45.73940000, 23.01035000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q518992'),
(95505, 'Oraş Cãlimãneşti', 4757, 'VL', 181, 'RO', 45.24252000, 24.34375000, '2019-10-05 23:15:32', '2020-05-01 17:23:12', 1, 'Q375371'),
(95506, 'Oraş Cãzãneşti', 4743, 'IL', 181, 'RO', 44.62514000, 27.01697000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q276164'),
(95507, 'Oraş Darabani', 4740, 'BT', 181, 'RO', 48.17429000, 26.61519000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q899467'),
(95508, 'Oraş Deta', 4748, 'TM', 181, 'RO', 45.40518000, 21.24829000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q851007'),
(95509, 'Oraş Dolhasca', 4720, 'SV', 181, 'RO', 47.41790000, 26.61083000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q276117'),
(95510, 'Oraş Drãgãneşti-Olt', 4738, 'OT', 181, 'RO', 44.17618000, 24.50836000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q508057'),
(95511, 'Oraş Dumbrãveni', 4755, 'SB', 181, 'RO', 46.22258000, 24.55274000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q521108'),
(95512, 'Oraş Dãbuleni', 4742, 'DJ', 181, 'RO', 43.78047000, 24.08653000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q276135'),
(95513, 'Oraş Dãrmãneşti', 4744, 'BC', 181, 'RO', 46.37559000, 26.47673000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q305182'),
(95514, 'Oraş Eforie', 4737, 'CT', 181, 'RO', 44.04431000, 28.64529000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q586387'),
(95515, 'Oraş Fieni', 4745, 'DB', 181, 'RO', 45.13054000, 25.41094000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q678884'),
(95516, 'Oraş Fierbinţi-Târg', 4743, 'IL', 181, 'RO', 44.68292000, 26.38324000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q275645'),
(95517, 'Oraş Filiaşi', 4742, 'DJ', 181, 'RO', 44.56847000, 23.55164000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q756596'),
(95518, 'Oraş Flãmânzi', 4740, 'BT', 181, 'RO', 47.56193000, 26.90381000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q275904'),
(95519, 'Oraş Frasin', 4720, 'SV', 181, 'RO', 47.51236000, 25.79977000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q276128'),
(95520, 'Oraş Fundulea', 4732, 'CL', 181, 'RO', 44.46456000, 26.49901000, '2019-10-05 23:15:32', '2020-05-01 17:23:09', 1, 'Q923254'),
(95521, 'Oraş Fãget', 4748, 'TM', 181, 'RO', 45.85770000, 22.17610000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q851048'),
(95522, 'Oraş Fãurei', 4736, 'BR', 181, 'RO', 45.08534000, 27.27155000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q1006796'),
(95523, 'Oraş Geoagiu', 4721, 'HD', 181, 'RO', 45.94835000, 23.20022000, '2019-10-05 23:15:32', '2020-05-01 17:23:10', 1, 'Q1017982'),
(95524, 'Oraş Ghimbav', 4759, 'BV', 181, 'RO', 45.66397000, 25.50836000, '2019-10-05 23:15:32', '2020-05-01 17:23:08', 1, 'Q916156'),
(95525, 'Oraş Gura Humorului', 4720, 'SV', 181, 'RO', 47.54191000, 25.87765000, '2019-10-05 23:15:32', '2020-05-01 17:23:11', 1, 'Q837170'),
(95526, 'Oraş Gãeşti', 4745, 'DB', 181, 'RO', 44.72003000, 25.32040000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q125604'),
(95527, 'Oraş Gãtaia', 4748, 'TM', 181, 'RO', 45.37402000, 21.40796000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q325732'),
(95528, 'Oraş Haţeg', 4721, 'HD', 181, 'RO', 45.62512000, 22.92275000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q837167'),
(95529, 'Oraş Horezu', 4757, 'VL', 181, 'RO', 45.16962000, 23.98448000, '2019-10-05 23:15:33', '2020-05-01 17:23:12', 1, 'Q899461'),
(95530, 'Oraş Huedin', 4734, 'CJ', 181, 'RO', 46.87204000, 23.03935000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q750220'),
(95531, 'Oraş Hârlãu', 4735, 'IS', 181, 'RO', 47.43153000, 26.87774000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q851042'),
(95532, 'Oraş Hârşova', 4737, 'CT', 181, 'RO', 44.68721000, 27.94785000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q748068'),
(95533, 'Oraş Ianca', 4736, 'BR', 181, 'RO', 45.11446000, 27.50205000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q899551'),
(95534, 'Oraş Ineu', 4739, 'AR', 181, 'RO', 46.42709000, 21.84185000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q835227');

