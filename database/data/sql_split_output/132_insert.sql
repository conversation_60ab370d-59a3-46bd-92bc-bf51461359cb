INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(66459, 'Beruwala', 2802, '1', 208, 'LK', 6.47880000, 79.98280000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q829211'),
(66460, 'Chilaw', 2817, '6', 208, 'LK', 7.57583000, 79.79528000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q2495639'),
(66461, 'Colombo', 2802, '1', 208, 'LK', 6.93548000, 79.84868000, '2019-10-05 23:07:56', '2019-10-05 23:07:56', 1, 'Q35381'),
(66462, 'Colombo District', 2802, '1', 208, '<PERSON><PERSON>', 6.86640000, 80.01660000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q606287'),
(66463, '<PERSON><PERSON>a', 2798, '2', 208, 'L<PERSON>', 7.86000000, 80.65167000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q377343'),
(66464, 'Dehiwala-Mount Lavinia', 2815, '11', 208, 'LK', 6.84019000, 79.87116000, '2019-10-05 23:07:57', '2020-11-29 20:16:54', 1, 'Q24462'),
(66465, 'Devinuwara', 2801, '3', 208, 'LK', 5.92825000, 80.58880000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q24462'),
(66466, 'Ella Town', 2811, '8', 208, 'LK', 6.87560000, 81.04630000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q24462'),
(66467, 'Eravur Town', 2808, '5', 208, 'LK', 7.77820000, 81.60380000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q24462'),
(66468, 'Galle', 2801, '3', 208, 'LK', 6.04610000, 80.21030000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q319366'),
(66469, 'Galle District', 2801, '3', 208, 'LK', 6.25000000, 80.25000000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q647649'),
(66470, 'Gampaha', 2802, '1', 208, 'LK', 7.08970000, 79.99250000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q2616818'),
(66471, 'Gampaha District', 2802, '1', 208, 'LK', 7.13330000, 80.00000000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q206344'),
(66472, 'Gampola', 2788, '21', 208, 'LK', 7.16430000, 80.56960000, '2019-10-05 23:07:57', '2020-11-29 19:57:22', 1, 'Q8264065'),
(66473, 'Hambantota District', 2801, '3', 208, 'LK', 6.25440000, 81.11110000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q723006'),
(66474, 'Hanwella Ihala', 2802, '1', 208, 'LK', 6.90120000, 80.08520000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q723006'),
(66475, 'Haputale', 2811, '8', 208, 'LK', 6.76566000, 80.95104000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q723006'),
(66476, 'Hatton', 2798, '2', 208, 'LK', 6.89160000, 80.59550000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q390564'),
(66477, 'Hendala', 2802, '1', 208, 'LK', 6.99090000, 79.88300000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q390564'),
(66478, 'Hikkaduwa', 2801, '3', 208, 'LK', 6.14070000, 80.10120000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1618065'),
(66479, 'Homagama', 2815, '11', 208, 'LK', 6.84879896, 80.00467300, '2019-10-05 23:07:57', '2020-11-29 20:17:28', 1, 'Q5887377'),
(66480, 'Horana South', 2802, '1', 208, 'LK', 6.71590000, 80.06260000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1618065'),
(66481, 'Horawala Junction', 2802, '1', 208, 'LK', 6.48088000, 80.12708000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1618065'),
(66482, 'Ja Ela', 2802, '1', 208, 'LK', 7.07440000, 79.89190000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1618065'),
(66483, 'Jaffna', 2813, '4', 208, 'LK', 9.66845000, 80.00742000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q215277'),
(66484, 'Jaffna District', 2813, '4', 208, 'LK', 9.75000000, 80.08333000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1520182'),
(66485, 'Kadugannawa', 2788, '21', 208, 'LK', 7.25470000, 80.52420000, '2019-10-05 23:07:57', '2020-11-29 19:59:27', 1, 'Q6345741'),
(66486, 'Kalmunai', 2808, '5', 208, 'LK', 7.40902000, 81.83472000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1101382'),
(66487, 'Kalutara', 2802, '1', 208, 'LK', 6.58310000, 79.95930000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1295530'),
(66488, 'Kandana', 2802, '1', 208, 'LK', 7.04800000, 79.89370000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q6361287'),
(66489, 'Kandy', 2798, '2', 208, 'LK', 7.29060000, 80.63360000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q203197'),
(66490, 'Kandy District', 2798, '2', 208, 'LK', 7.30440000, 80.70730000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q723002'),
(66491, 'Kataragama', 2811, '8', 208, 'LK', 6.41340000, 81.33460000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q302558'),
(66492, 'Katunayaka', 2802, '1', 208, 'LK', 7.16992000, 79.88837000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q2887547'),
(66493, 'Kegalle', 2803, '9', 208, 'LK', 7.25230000, 80.34360000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1968268'),
(66494, 'Kegalle District', 2803, '9', 208, 'LK', 7.11670000, 80.33330000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1737803'),
(66495, 'Kelaniya', 2802, '1', 208, 'LK', 6.95530000, 79.92200000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q3522428'),
(66496, 'Kilinochchi', 2813, '4', 208, 'LK', 9.39610000, 80.39820000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q303317'),
(66497, 'Kilinochchi District', 2813, '4', 208, 'LK', 9.39487000, 80.40894000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1584007'),
(66498, 'Koggala', 2801, '3', 208, 'LK', 5.98860000, 80.32860000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1584007'),
(66499, 'Kolonnawa', 2815, '11', 208, 'LK', 6.92893463, 79.89495168, '2019-10-05 23:07:57', '2020-11-29 20:18:01', 1, 'Q6427785'),
(66500, 'Kotikawatta', 2802, '1', 208, 'LK', 6.92690000, 79.90950000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1153291'),
(66501, 'Kuliyapitiya', 2817, '6', 208, 'LK', 7.46880000, 80.04010000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1153291'),
(66502, 'Kurunegala', 2817, '6', 208, 'LK', 7.48390000, 80.36830000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q776887'),
(66503, 'Kurunegala District', 2817, '6', 208, 'LK', 7.75000000, 80.25000000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q745073'),
(66504, 'Maharagama', 2815, '11', 208, 'LK', 6.84954886, 79.92507313, '2019-10-05 23:07:57', '2020-11-29 20:18:30', 1, 'Q6733192'),
(66505, 'Matale', 2798, '2', 208, 'LK', 7.46980000, 80.62170000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1540498'),
(66506, 'Matale District', 2798, '2', 208, 'LK', 7.65980000, 80.70730000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q787421'),
(66507, 'Matara', 2801, '3', 208, 'LK', 5.94851000, 80.53528000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q13360574'),
(66508, 'Matara District', 2801, '3', 208, 'LK', 6.13290000, 80.52800000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1281285'),
(66509, 'Mihintale', 2800, '7', 208, 'LK', 8.35930000, 80.51030000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1281285'),
(66510, 'Minuwangoda', 2802, '1', 208, 'LK', 7.16630000, 79.95330000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1281285'),
(66511, 'Mirissa city', 2801, '3', 208, 'LK', 5.94655000, 80.45831000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1281285'),
(66512, 'Monaragala', 2811, '8', 208, 'LK', 6.87140000, 81.34870000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1281285'),
(66513, 'Moneragala District', 2811, '8', 208, 'LK', 6.66667000, 81.33333000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q923420'),
(66514, 'Moratuwa', 2815, '11', 208, 'LK', 6.78149272, 79.88347117, '2019-10-05 23:07:57', '2020-11-29 20:18:59', 1, 'Q6909302'),
(66515, 'Mulleriyawa', 2802, '1', 208, 'LK', 6.93300000, 79.92970000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q867213'),
(66516, 'Negombo', 2802, '1', 208, 'LK', 7.20830000, 79.83580000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q668616'),
(66517, 'Nuwara Eliya', 2798, '2', 208, 'LK', 6.97078000, 80.78286000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1340579'),
(66518, 'Nuwara Eliya District', 2798, '2', 208, 'LK', 6.97850000, 80.71330000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1583950'),
(66519, 'Panadura', 2802, '1', 208, 'LK', 6.71320000, 79.90260000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q7129771'),
(66520, 'Peliyagoda', 2802, '1', 208, 'LK', 6.96850000, 79.88360000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q7129771'),
(66521, 'Pita Kotte', 2802, '1', 208, 'LK', 6.89050000, 79.90150000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q7129771'),
(66522, 'Point Pedro', 2813, '4', 208, 'LK', 9.81667000, 80.23333000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q2307889'),
(66523, 'Polonnaruwa', 2800, '7', 208, 'LK', 7.93965000, 81.00274000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q394443'),
(66524, 'Polonnaruwa District', 2800, '7', 208, 'LK', 8.00000000, 81.00000000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q931057'),
(66525, 'Puttalam', 2817, '6', 208, 'LK', 8.03620000, 79.82830000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q3088741'),
(66526, 'Puttalam District', 2817, '6', 208, 'LK', 8.04540000, 79.93190000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1665318'),
(66527, 'Ratnapura', 2803, '9', 208, 'LK', 6.68580000, 80.40360000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q508753'),
(66528, 'Ratnapura District', 2803, '9', 208, 'LK', 6.58310000, 80.58330000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1587175'),
(66529, 'Sigiriya', 2798, '2', 208, 'LK', 7.94946000, 80.75037000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q272153'),
(66530, 'Sri Jayewardenepura Kotte', 2802, '1', 208, 'LK', 6.88297000, 79.90708000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q41963'),
(66531, 'Talawakele', 2798, '2', 208, 'LK', 6.93710000, 80.65810000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q7678986'),
(66532, 'Talpe', 2801, '3', 208, 'LK', 5.99990000, 80.27870000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q7678986'),
(66533, 'Tangalle', 2801, '3', 208, 'LK', 6.02338000, 80.79738000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q598658'),
(66534, 'Trincomalee', 2808, '5', 208, 'LK', 8.57780000, 81.22890000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q323873'),
(66535, 'Trincomalee District', 2808, '5', 208, 'LK', 8.58333000, 81.08333000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1493318'),
(66536, 'Unawatuna', 2801, '3', 208, 'LK', 6.02120000, 80.25030000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q651301'),
(66537, 'Vakarai', 2808, '5', 208, 'LK', 8.13333000, 81.43333000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q7908969'),
(66538, 'Valvedditturai', 2813, '4', 208, 'LK', 9.81667000, 80.16667000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q2688290'),
(66539, 'Vavuniya', 2813, '4', 208, 'LK', 8.75140000, 80.49710000, '2019-10-05 23:07:57', '2019-10-05 23:07:57', 1, 'Q1191330'),
(66540, 'Vavuniya District', 2813, '4', 208, 'LK', 8.86134000, 80.47576000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q527980'),
(66541, 'Wattala', 2802, '1', 208, 'LK', 6.98918000, 79.89167000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q7974929'),
(66542, 'Wattegama', 2788, '21', 208, 'LK', 7.35027800, 80.68250000, '2019-10-05 23:07:58', '2020-11-29 20:00:27', 1, 'Q7974935'),
(66543, 'Weligama', 2801, '3', 208, 'LK', 5.97501000, 80.42968000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q3534843'),
(66544, 'Welisara', 2802, '1', 208, 'LK', 7.02810000, 79.90140000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q3534843'),
(66545, 'Wellawaya', 2811, '8', 208, 'LK', 6.73694000, 81.10279000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q3534843'),
(66546, 'Barclayville', 3045, 'GK', 123, 'LR', 4.67443000, -8.23306000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q807963'),
(66547, 'Bensonville', 3032, 'MO', 123, 'LR', 6.44716000, -10.61283000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q818201'),
(66548, 'Bopolu', 3044, 'GP', 123, 'LR', 7.06667000, -10.48750000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q893242'),
(66549, 'Buchanan', 3040, 'GB', 123, 'LR', 5.87693000, -10.04964000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q269222'),
(66550, 'Cestos City', 3033, 'RI', 123, 'LR', 5.45683000, -9.58167000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1057594'),
(66551, 'Fish Town', 3038, 'RG', 123, 'LR', 5.19739000, -7.87579000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q598740'),
(66552, 'Ganta', 3046, 'NI', 123, 'LR', 7.30222000, -8.53083000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q598740'),
(66553, 'Gbarnga', 3034, 'BG', 123, 'LR', 6.99543000, -9.47122000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q985291'),
(66554, 'Greenville', 3035, 'SI', 123, 'LR', 5.01133000, -9.03880000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1022899'),
(66555, 'Harper', 3042, 'MY', 123, 'LR', 4.37820000, -7.71081000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1019486'),
(66556, 'Kakata', 3043, 'MG', 123, 'LR', 6.53104000, -10.35368000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q426891'),
(66557, 'Monrovia', 3032, 'MO', 123, 'LR', 6.30054000, -10.79690000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q3748'),
(66558, 'New Yekepa', 3046, 'NI', 123, 'LR', 7.57944000, -8.53778000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q3179587'),
(66559, 'Robertsport', 3036, 'CM', 123, 'LR', 6.75329000, -11.36710000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1024892'),
(66560, 'Sanniquellie', 3046, 'NI', 123, 'LR', 7.36215000, -8.71326000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1968465'),
(66561, 'Tubmanburg', 3041, 'BM', 123, 'LR', 6.87064000, -10.82110000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1024906'),
(66562, 'Voinjama', 3037, 'LO', 123, 'LR', 8.42194000, -9.74778000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1024888'),
(66563, 'Zwedru', 3039, 'GG', 123, 'LR', 6.06846000, -8.13559000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q231884'),
(66564, 'Butha-Buthe', 3029, 'B', 122, 'LS', -28.76659000, 28.24937000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q382249'),
(66565, 'Leribe', 3026, 'C', 122, 'LS', -28.87185000, 28.04501000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q993076'),
(66566, 'Mafeteng', 3022, 'E', 122, 'LS', -29.82299000, 27.23744000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1013379'),
(66567, 'Maputsoe', 3026, 'C', 122, 'LS', -28.88660000, 27.89915000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1892158'),
(66568, 'Maseru', 3028, 'A', 122, 'LS', -29.31667000, 27.48333000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q3909'),
(66569, 'Mohale’s Hoek', 3023, 'F', 122, 'LS', -30.15137000, 27.47691000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1818943'),
(66570, 'Mokhotlong', 3024, 'J', 122, 'LS', -29.28939000, 29.06751000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1013428'),
(66571, 'Nako', 3028, 'A', 122, 'LS', -29.61667000, 27.76667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q6739344'),
(66572, 'Qacha’s Nek', 3025, 'H', 122, 'LS', -30.11537000, 28.68936000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q491829'),
(66573, 'Quthing', 3027, 'G', 122, 'LS', -30.40001000, 27.70027000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1003318'),
(66574, 'Teyateyaneng', 3030, 'D', 122, 'LS', -29.14719000, 27.74895000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q844423'),
(66575, 'Thaba-Tseka', 3031, 'K', 122, 'LS', -29.52204000, 28.60840000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q777028'),
(66576, 'Akademija (Kaunas)', 1580, '15', 126, 'LT', 54.89640000, 23.82411000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2869148'),
(66577, 'Akmenė', 1609, '43', 126, 'LT', 56.25000000, 22.75000000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q27485851'),
(66579, 'Aleksotas', 1580, '15', 126, 'LT', 54.88037000, 23.90842000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1023041'),
(66581, 'Alytus', 1605, '02', 126, 'LT', 54.39635000, 24.04142000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q450625'),
(66582, 'Anyksciai', 1621, 'UT', 126, 'LT', 55.52557000, 25.10264000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q27485494'),
(66584, 'Ariogala', 1580, '15', 126, 'LT', 55.26200000, 23.47700000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004320'),
(66585, 'Aukstadvaris', 1606, '57', 126, 'LT', 54.57946000, 24.52683000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2667459'),
(66586, 'Baltoji Vokė', 1606, '57', 126, 'LT', 54.60002000, 25.19318000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q2667459'),
(66587, 'Birzai', 1614, '33', 126, 'LT', 56.20000000, 24.75000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q804035'),
(66588, 'Birštonas', 1580, '15', 126, 'LT', 54.61667000, 24.03333000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q804035'),
(66590, 'Būgai', 1573, 'TA', 126, 'LT', 55.41387000, 22.60894000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q763504'),
(66591, 'Dainava (Kaunas)', 1580, '15', 126, 'LT', 54.91525000, 23.96831000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q763504'),
(66592, 'Daugai', 1605, '02', 126, 'LT', 54.36667000, 24.33333000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q27485543'),
(66593, 'Druskininkai', 1605, '02', 126, 'LT', 54.01573000, 23.98703000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1261509'),
(66594, 'Dūkštas', 1621, 'UT', 126, 'LT', 55.52200000, 26.32100000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q2356406'),
(66595, 'Eiguliai', 1580, '15', 126, 'LT', 54.93133000, 23.93243000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2356406'),
(66596, 'Eišiškės', 1606, '57', 126, 'LT', 54.17414000, 24.99917000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q58880'),
(66597, 'Elektrėnai', 1606, '57', 126, 'LT', 54.78544000, 24.66302000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q286554'),
(66598, 'Ežerėlis', 1580, '15', 126, 'LT', 54.88315000, 23.60396000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q2044597'),
(66599, 'Fabijoniškės', 1606, '57', 126, 'LT', 54.73333000, 25.24167000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1390465'),
(66600, 'Gargždai', 1604, '21', 126, 'LT', 55.70951000, 21.39441000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1390465'),
(66601, 'Garliava', 1580, '15', 126, 'LT', 54.82139000, 23.87130000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004312'),
(66602, 'Gelgaudiškis', 1610, '25', 126, 'LT', 55.07688000, 22.97699000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q3284738'),
(66603, 'Grigiškės', 1606, '57', 126, 'LT', 54.68333000, 25.08333000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q950639'),
(66604, 'Ignalina', 1621, 'UT', 126, 'LT', 55.35000000, 26.16667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q950639'),
(66605, 'Jieznas', 1580, '15', 126, 'LT', 54.59937000, 24.17593000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q47068'),
(66606, 'Jonava', 1580, '15', 126, 'LT', 55.08333000, 24.28333000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1351768'),
(66607, 'Joniškis', 1609, '43', 126, 'LT', 56.23333000, 23.61667000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1351768'),
(66608, 'Juodupė', 1614, '33', 126, 'LT', 56.08700000, 25.60700000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q6314474'),
(66609, 'Jurbarkas', 1573, 'TA', 126, 'LT', 55.10859000, 22.79885000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2089815'),
(66610, 'Justiniškės', 1606, '57', 126, 'LT', 54.71664000, 25.21740000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1260423'),
(66611, 'Kaišiadorys', 1580, '15', 126, 'LT', 54.86667000, 24.45000000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1260423'),
(66612, 'Kalvarija', 1610, '25', 126, 'LT', 54.41700000, 23.22300000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004298'),
(66613, 'Kalvarija Municipality', 1610, '25', 126, 'LT', 54.41468000, 23.22484000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1461987'),
(66614, 'Karmėlava', 1580, '15', 126, 'LT', 54.97055000, 24.06182000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1734094'),
(66615, 'Kaunas', 1580, '15', 126, 'LT', 54.90272000, 23.90961000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q4115712'),
(66617, 'Kazlų Rūda', 1610, '25', 126, 'LT', 54.74900000, 23.49000000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q945473'),
(66618, 'Kazlų Rūda Municipality', 1610, '25', 126, 'LT', 54.74751000, 23.49272000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1461972'),
(66619, 'Kelmė', 1609, '43', 126, 'LT', 55.63333000, 22.93333000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1461972'),
(66620, 'Klaipėda', 1604, '21', 126, 'LT', 55.71667000, 21.11667000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q776965'),
(66622, 'Kretinga', 1604, '21', 126, 'LT', 55.88880000, 21.24448000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1788115'),
(66623, 'Kudirkos Naumiestis', 1610, '25', 126, 'LT', 54.77353000, 22.86138000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q277936'),
(66624, 'Kulautuva', 1580, '15', 126, 'LT', 54.94212000, 23.64218000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2097414'),
(66625, 'Kupiskis', 1614, '33', 126, 'LT', 55.84027000, 24.97976000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q27484028'),
(66627, 'Kuršėnai', 1609, '43', 126, 'LT', 56.00318000, 22.93662000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q835856'),
(66628, 'Kybartai', 1610, '25', 126, 'LT', 54.63858000, 22.76316000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004295'),
(66629, 'Kėdainiai', 1580, '15', 126, 'LT', 55.28333000, 23.96667000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q46786'),
(66630, 'Lazdijai', 1605, '02', 126, 'LT', 54.23333000, 23.51667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q46786'),
(66631, 'Lazdynai', 1606, '57', 126, 'LT', 54.66815000, 25.20684000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1646619'),
(66632, 'Lentvaris', 1606, '57', 126, 'LT', 54.64364000, 25.05162000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q923393'),
(66633, 'Linkuva', 1609, '43', 126, 'LT', 56.08596000, 23.97061000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1656462'),
(66634, 'Marijampolė', 1610, '25', 126, 'LT', 54.55991000, 23.35412000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1351046'),
(66635, 'Marijampolė Municipality', 1610, '25', 126, 'LT', 54.56667000, 23.35000000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1351046'),
(66636, 'Mastaiciai', 1580, '15', 126, 'LT', 54.81998000, 23.84462000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1351046'),
(66637, 'Mazeikiai', 1608, '51', 126, 'LT', 56.31667000, 22.33333000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q27484102'),
(66639, 'Moletai', 1621, 'UT', 126, 'LT', 55.23333000, 25.41667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2089785'),
(66641, 'Naujamiestis', 1606, '57', 126, 'LT', 54.67951000, 25.26855000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q957530'),
(66642, 'Naujoji Akmene', 1609, '43', 126, 'LT', 56.31667000, 22.90000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q835848'),
(66643, 'Nemenčinė', 1606, '57', 126, 'LT', 54.84776000, 25.46992000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q118910'),
(66644, 'Neringa', 1604, '21', 126, 'LT', 55.36667000, 21.06667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q9305847'),
(66645, 'Nida', 1604, '21', 126, 'LT', 55.30860000, 20.99651000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004306'),
(66646, 'Obeliai', 1614, '33', 126, 'LT', 55.96895000, 25.15648000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004306'),
(66647, 'Pabradė', 1606, '57', 126, 'LT', 54.98100000, 25.76100000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1004328'),
(66648, 'Pagėgiai', 1573, 'TA', 126, 'LT', 55.13400000, 21.90446000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q2283574'),
(66649, 'Pakruojis', 1609, '43', 126, 'LT', 55.96667000, 23.86667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1657116'),
(66650, 'Palanga', 1604, '21', 126, 'LT', 55.91750000, 21.06861000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2047414'),
(66651, 'Panevėžys', 1614, '33', 126, 'LT', 55.73333000, 24.35000000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1719466'),
(66652, 'Panevėžys City', 1614, '33', 126, 'LT', 55.73600000, 24.34347000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1719466'),
(66653, 'Panevėžys District Municipality', 1614, '33', 126, 'LT', 55.73301000, 24.37609000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1351758'),
(66654, 'Pasvalys', 1614, '33', 126, 'LT', 56.06667000, 24.40000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1351758'),
(66655, 'Pašilaičiai', 1606, '57', 126, 'LT', 54.73429000, 25.21912000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1351758'),
(66656, 'Pilaitė', 1606, '57', 126, 'LT', 54.69981000, 25.18393000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1351758'),
(66657, 'Plateliai', 1608, '51', 126, 'LT', 56.04657000, 21.81615000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1028246'),
(66658, 'Plunge', 1608, '51', 126, 'LT', 55.91139000, 21.84417000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q977255'),
(66660, 'Priekulė', 1604, '21', 126, 'LT', 55.55427000, 21.31903000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q768416'),
(66661, 'Prienai', 1580, '15', 126, 'LT', 54.63333000, 23.95000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q768416'),
(66662, 'Radviliskis', 1609, '43', 126, 'LT', 55.81667000, 23.53333000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q318686'),
(66664, 'Ramučiai', 1580, '15', 126, 'LT', 54.94830000, 24.03050000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q384446'),
(66665, 'Ramygala', 1614, '33', 126, 'LT', 55.51400000, 24.30000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2089619'),
(66666, 'Raseiniai', 1580, '15', 126, 'LT', 55.36667000, 23.11667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2069355'),
(66667, 'Rasos', 1606, '57', 126, 'LT', 54.67877000, 25.31173000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2094071'),
(66668, 'Rietavas', 1608, '51', 126, 'LT', 55.72375000, 21.93266000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q712011'),
(66669, 'Rokiškis', 1614, '33', 126, 'LT', 55.96667000, 25.58333000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q712011'),
(66670, 'Rusnė', 1604, '21', 126, 'LT', 55.29820000, 21.37120000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q712011'),
(66671, 'Rūdiškės', 1606, '57', 126, 'LT', 54.51609000, 24.83084000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q58790'),
(66672, 'Sakiai', 1610, '25', 126, 'LT', 54.95000000, 23.05000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q58790'),
(66673, 'Salantai', 1604, '21', 126, 'LT', 56.05650000, 21.56890000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q157516'),
(66674, 'Sargėnai', 1580, '15', 126, 'LT', 54.94933000, 23.88316000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q157516'),
(66675, 'Seda', 1608, '51', 126, 'LT', 56.16854000, 22.09071000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q394756'),
(66676, 'Senieji Trakai', 1606, '57', 126, 'LT', 54.60900000, 24.98000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q3446896'),
(66677, 'Silute', 1604, '21', 126, 'LT', 55.34889000, 21.48306000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q391626'),
(66678, 'Simnas', 1605, '02', 126, 'LT', 54.38200000, 23.64600000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1892074'),
(66679, 'Sirvintos', 1606, '57', 126, 'LT', 55.05000000, 24.95000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1892074'),
(66680, 'Skaidiškės', 1606, '57', 126, 'LT', 54.61398000, 25.39573000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q5247193'),
(66681, 'Skuodas', 1604, '21', 126, 'LT', 56.26667000, 21.53333000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q5247193'),
(66682, 'Svencionys', 1606, '57', 126, 'LT', 55.15000000, 26.16667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q5247193'),
(66683, 'Taurage', 1573, 'TA', 126, 'LT', 55.25222000, 22.28972000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q193787'),
(66685, 'Telsiai', 1608, '51', 126, 'LT', 55.98139000, 22.24722000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2222616'),
(66687, 'Trakai', 1606, '57', 126, 'LT', 54.63783000, 24.93433000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q191015'),
(66688, 'Tytuvėnėliai', 1609, '43', 126, 'LT', 55.62345000, 23.17162000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q191015'),
(66689, 'Ukmerge', 1606, '57', 126, 'LT', 55.25000000, 24.75000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q213127'),
(66691, 'Utena', 1621, 'UT', 126, 'LT', 55.49764000, 25.59918000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q189157'),
(66692, 'Vabalninkas', 1614, '33', 126, 'LT', 55.98151000, 24.74828000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2000579'),
(66693, 'Varniai', 1608, '51', 126, 'LT', 55.74435000, 22.37251000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q621181'),
(66694, 'Varėna', 1605, '02', 126, 'LT', 54.21667000, 24.56667000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q621181'),
(66695, 'Veisiejai', 1605, '02', 126, 'LT', 54.10110000, 23.69614000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q2044626'),
(66696, 'Venta', 1609, '43', 126, 'LT', 56.19162000, 22.69528000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1104010'),
(66697, 'Viekšniai', 1608, '51', 126, 'LT', 56.23598000, 22.51667000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q379669'),
(66698, 'Vievis', 1606, '57', 126, 'LT', 54.76667000, 24.80000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004332'),
(66699, 'Vilkaviskis', 1610, '25', 126, 'LT', 54.65167000, 23.03222000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1004332'),
(66700, 'Vilkaviškis District Municipality', 1610, '25', 126, 'LT', 54.65000000, 23.03333000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q1004332'),
(66701, 'Vilkija', 1580, '15', 126, 'LT', 55.04609000, 23.58552000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1994818'),
(66702, 'Vilkpėdė', 1606, '57', 126, 'LT', 54.66969000, 25.24770000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q2577081'),
(66703, 'Vilnius', 1606, '57', 126, 'LT', 54.68916000, 25.27980000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q216'),
(66704, 'Vilnius District Municipality', 1606, '57', 126, 'LT', 54.73333000, 25.38333000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q118903'),
(66705, 'Virbalis', 1610, '25', 126, 'LT', 54.62858000, 22.82272000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q1024673'),
(66706, 'Visaginas', 1621, 'UT', 126, 'LT', 55.60000000, 26.41667000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q203892'),
(66707, 'Zarasai', 1621, 'UT', 126, 'LT', 55.73333000, 26.25000000, '2019-10-05 23:07:58', '2019-10-05 23:07:58', 1, 'Q203892'),
(66709, 'Šalčininkai', 1606, '57', 126, 'LT', 54.30000000, 25.38333000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q203892'),
(66710, 'Šeduva', 1609, '43', 126, 'LT', 55.74930000, 23.75610000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q2356396'),
(66711, 'Šeškinė', 1606, '57', 126, 'LT', 54.70972000, 25.25053000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q2455066'),
(66712, 'Šiauliai', 1609, '43', 126, 'LT', 55.93333000, 23.31667000, '2019-10-05 23:07:58', '2020-05-01 17:22:59', 1, 'Q134712'),
(66714, 'Šilainiai', 1580, '15', 126, 'LT', 54.92911000, 23.88599000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q1417346'),
(66715, 'Šilalė', 1573, 'TA', 126, 'LT', 55.46667000, 22.20000000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q1417346'),
(66718, 'Šventoji', 1604, '21', 126, 'LT', 56.02610000, 21.08411000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q1417346'),
(66720, 'Švenčionėliai', 1606, '57', 126, 'LT', 55.16163000, 26.00177000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q392772'),
(66721, 'Žagarė', 1609, '43', 126, 'LT', 56.36149000, 23.25094000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q393332'),
(66722, 'Žemaičių Naumiestis', 1604, '21', 126, 'LT', 55.35941000, 21.70364000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q393828'),
(66723, 'Žiežmariai', 1580, '15', 126, 'LT', 54.80725000, 24.44073000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q4178675'),
(66724, 'Alzingen', 1514, 'L', 127, 'LU', 49.56500000, 6.16361000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q144260'),
(66725, 'Aspelt', 1517, 'ES', 127, 'LU', 49.52278000, 6.22472000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q642351'),
(66726, 'Bascharage', 1518, 'CA', 127, 'LU', 49.56727000, 5.90730000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q947942'),
(66727, 'Bavigne', 1526, 'WI', 127, 'LU', 49.92194000, 5.84944000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q972183'),
(66728, 'Beaufort', 1515, 'EC', 127, 'LU', 49.83583000, 6.29167000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969250'),
(66729, 'Bech', 1515, 'EC', 127, 'LU', 49.75260000, 6.36379000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969250'),
(66730, 'Beckerich', 1516, 'RD', 127, 'LU', 49.73056000, 5.88722000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q813727'),
(66731, 'Belvaux', 1517, 'ES', 127, 'LU', 49.51014000, 5.92414000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2895177'),
(66732, 'Berdorf', 1515, 'EC', 127, 'LU', 49.82051000, 6.34945000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2895177'),
(66733, 'Bergem', 1517, 'ES', 127, 'LU', 49.52500000, 6.04222000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q819508'),
(66734, 'Bertrange', 1514, 'L', 127, 'LU', 49.61111000, 6.05000000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q809603'),
(66735, 'Bettborn', 1516, 'RD', 127, 'LU', 49.79528000, 5.94111000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q22284'),
(66736, 'Bettembourg', 1517, 'ES', 127, 'LU', 49.51861000, 6.10278000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q22284'),
(66737, 'Bettendorf', 1513, 'DI', 127, 'LU', 49.87667000, 6.21806000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q22284'),
(66738, 'Betzdorf', 1520, 'GR', 127, 'LU', 49.68333000, 6.35000000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q22284'),
(66739, 'Bissen', 1522, 'ME', 127, 'LU', 49.78733000, 6.06540000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q22284'),
(66740, 'Biwer', 1520, 'G', 127, 'LU', 49.70605000, 6.37201000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q22284'),
(66741, 'Boevange-sur-Attert', 1522, 'ME', 127, 'LU', 49.77256000, 6.01532000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q22284'),
(66742, 'Boulaide', 1526, 'WI', 127, 'LU', 49.88778000, 5.81639000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q22284'),
(66743, 'Bourscheid', 1513, 'DI', 127, 'LU', 49.90862000, 6.06750000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969287'),
(66744, 'Bous', 1519, 'RM', 127, 'LU', 49.55389000, 6.32917000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969287'),
(66745, 'Bridel', 1518, 'CA', 127, 'LU', 49.65579000, 6.07999000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2584585'),
(66746, 'Béreldange', 1514, 'L', 127, 'LU', 49.65507000, 6.11874000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q818991'),
(66747, 'Capellen', 1518, 'CA', 127, 'LU', 49.64500000, 5.99083000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1988543'),
(66748, 'Clemency', 1518, 'CA', 127, 'LU', 49.59667000, 5.87583000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1369887'),
(66749, 'Clervaux', 1521, 'CL', 127, 'LU', 50.05472000, 6.03139000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q13103610'),
(66750, 'Colmar', 1522, 'ME', 127, 'LU', 49.81028000, 6.09722000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969306'),
(66751, 'Commune de Préizerdaul', 1516, 'RD', 127, 'LU', 49.80114000, 5.93299000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q1134542'),
(66752, 'Commune de la Vallée de l’Ernz', 1513, 'DI', 127, 'LU', 49.82149000, 6.21746000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q1313580'),
(66753, 'Consdorf', 1515, 'EC', 127, 'LU', 49.78018000, 6.33950000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1313580'),
(66754, 'Contern', 1514, 'L', 127, 'LU', 49.58194000, 6.22667000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q1313580'),
(66755, 'Crauthem', 1517, 'ES', 127, 'LU', 49.53556000, 6.14694000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2702474'),
(66756, 'Dalheim', 1519, 'RM', 127, 'LU', 49.54083000, 6.25972000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969315'),
(66757, 'Diekirch', 1513, 'DI', 127, 'LU', 49.86778000, 6.15583000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q29057521'),
(66758, 'Differdange', 1517, 'ES', 127, 'LU', 49.52417000, 5.89139000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q4844168'),
(66759, 'Dippach', 1518, 'CA', 127, 'LU', 49.58701000, 5.98330000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q13102344'),
(66760, 'Dudelange', 1517, 'ES', 127, 'LU', 49.48056000, 6.08750000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969331'),
(66761, 'Echternach', 1515, 'EC', 127, 'LU', 49.81212000, 6.41846000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q204317'),
(66762, 'Eischen', 1518, 'CA', 127, 'LU', 49.68556000, 5.87861000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1311018'),
(66763, 'Ell', 1516, 'RD', 127, 'LU', 49.76389000, 5.85722000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1311018'),
(66764, 'Erpeldange', 1513, 'DI', 127, 'LU', 49.86472000, 6.11472000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1311018'),
(66765, 'Esch-sur-Alzette', 1517, 'ES', 127, 'LU', 49.49583000, 5.98056000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q16010'),
(66766, 'Esch-sur-Sûre', 1526, 'WI', 127, 'LU', 49.91139000, 5.93639000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q20969348'),
(66767, 'Ettelbruck', 1513, 'DI', 127, 'LU', 49.84750000, 6.10417000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969348'),
(66768, 'Fentange', 1514, 'L', 127, 'LU', 49.56278000, 6.15389000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q2056162'),
(66769, 'Feulen', 1513, 'DI', 127, 'LU', 49.85000000, 6.01667000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1143707'),
(66770, 'Fischbach', 1522, 'ME', 127, 'LU', 49.74600000, 6.18702000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20966342'),
(66771, 'Flaxweiler', 1520, 'G', 127, 'LU', 49.66602000, 6.34321000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q20966342'),
(66772, 'Frisange', 1517, 'ES', 127, 'LU', 49.51616000, 6.18858000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1017402'),
(66773, 'Garnich', 1518, 'CA', 127, 'LU', 49.61667000, 5.95250000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969364'),
(66774, 'Goesdorf', 1526, 'WI', 127, 'LU', 49.92131000, 5.96601000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969364'),
(66775, 'Gonderange', 1520, 'G', 127, 'LU', 49.69537000, 6.24577000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q2574685'),
(66776, 'Grevenmacher', 1520, 'G', 127, 'LU', 49.67751000, 6.44022000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q4132923'),
(66777, 'Grosbous', 1516, 'RD', 127, 'LU', 49.82778000, 5.96722000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q13102873'),
(66778, 'Hautcharage', 1518, 'CA', 127, 'LU', 49.57499000, 5.90970000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2675610'),
(66779, 'Heffingen', 1522, 'ME', 127, 'LU', 49.76907000, 6.24049000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2675610'),
(66780, 'Heisdorf', 1514, 'L', 127, 'LU', 49.67207000, 6.14202000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q1601313'),
(66781, 'Helmsange', 1514, 'L', 127, 'LU', 49.65278000, 6.14139000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q2572503'),
(66782, 'Hesperange', 1514, 'L', 127, 'LU', 49.56806000, 6.15139000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q2572503'),
(66783, 'Hobscheid', 1518, 'CA', 127, 'LU', 49.68861000, 5.91472000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q13102943'),
(66784, 'Hosingen', 1521, 'CL', 127, 'LU', 50.01218000, 6.09089000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1333496'),
(66785, 'Itzig', 1514, 'L', 127, 'LU', 49.58769000, 6.17065000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q3058880'),
(66786, 'Junglinster', 1520, 'G', 127, 'LU', 49.70722000, 6.25306000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q1017404'),
(66787, 'Kayl', 1517, 'ES', 127, 'LU', 49.48917000, 6.03972000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1017404'),
(66788, 'Kehlen', 1518, 'CA', 127, 'LU', 49.66833000, 6.03583000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q16763861'),
(66789, 'Kiischpelt', 1526, 'WI', 127, 'LU', 49.97835000, 6.00760000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1143742'),
(66790, 'Kirchberg', 1514, 'L', 127, 'LU', 49.62389000, 6.14944000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q583718'),
(66791, 'Koerich', 1518, 'CA', 127, 'LU', 49.67000000, 5.95000000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969433'),
(66792, 'Kopstal', 1518, 'CA', 127, 'LU', 49.66444000, 6.07306000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969435'),
(66793, 'Käerjeng', 1518, 'CA', 127, 'LU', 49.58311000, 5.89892000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q776642'),
(66794, 'Lac de la Haute-Sûre', 1526, 'WI', 127, 'LU', 49.91667000, 5.83333000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q1133688'),
(66795, 'Lamadelaine', 1517, 'ES', 127, 'LU', 49.54639000, 5.85639000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2217356'),
(66796, 'Larochette', 1522, 'ME', 127, 'LU', 49.78362000, 6.21891000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969440'),
(66797, 'Lenningen', 1519, 'RM', 127, 'LU', 49.59861000, 6.36806000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1017410'),
(66798, 'Leudelange', 1517, 'ES', 127, 'LU', 49.56833000, 6.06528000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917072'),
(66799, 'Lintgen', 1522, 'ME', 127, 'LU', 49.72243000, 6.13016000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969448'),
(66800, 'Lorentzweiler', 1522, 'ME', 127, 'LU', 49.70131000, 6.14234000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917028'),
(66801, 'Luxembourg', 1514, 'L', 127, 'LU', 49.61167000, 6.13000000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q1842'),
(66802, 'Mamer', 1518, 'CA', 127, 'LU', 49.62750000, 6.02333000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969453'),
(66803, 'Manternach', 1520, 'G', 127, 'LU', 49.70278000, 6.42639000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q20969453'),
(66804, 'Medernach', 1513, 'DI', 127, 'LU', 49.80955000, 6.21521000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969453'),
(66805, 'Mersch', 1522, 'ME', 127, 'LU', 49.74889000, 6.10611000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q23903049'),
(66806, 'Mertert', 1520, 'G', 127, 'LU', 49.70261000, 6.47966000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q3916990'),
(66807, 'Mertzig', 1513, 'DI', 127, 'LU', 49.83389000, 6.00750000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3916990'),
(66808, 'Mompach', 1515, 'EC', 127, 'LU', 49.74611000, 6.46500000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3916990'),
(66809, 'Mondercange', 1517, 'ES', 127, 'LU', 49.53306000, 5.98833000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q39057559'),
(66810, 'Mondorf-les-Bains', 1519, 'RM', 127, 'LU', 49.50500000, 6.28111000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917128'),
(66811, 'Moutfort', 1514, 'L', 127, 'LU', 49.58528000, 6.25556000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q2104959'),
(66812, 'Müllendorf', 1514, 'L', 127, 'LU', 49.68028000, 6.13000000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q2180001'),
(66813, 'Niederanven', 1514, 'L', 127, 'LU', 49.65196000, 6.26199000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q3917188'),
(66814, 'Niedercorn', 1517, 'ES', 127, 'LU', 49.53611000, 5.89306000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q751569'),
(66815, 'Niederfeulen', 1513, 'DI', 127, 'LU', 49.85556000, 6.04722000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3112408'),
(66816, 'Nommern', 1522, 'ME', 127, 'LU', 49.78694000, 6.17472000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3112408'),
(66817, 'Obercorn', 1517, 'ES', 127, 'LU', 49.51361000, 5.89611000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2010019'),
(66818, 'Olm', 1514, 'L', 127, 'LU', 49.65722000, 6.00028000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q2674976'),
(66819, 'Parc Hosingen', 1521, 'CL', 127, 'LU', 49.99744000, 6.09067000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q743393'),
(66820, 'Pontpierre', 1517, 'ES', 127, 'LU', 49.53639000, 6.02944000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1016106'),
(66821, 'Putscheid', 1523, 'VD', 127, 'LU', 49.96083000, 6.14306000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1016106'),
(66822, 'Pétange', 1517, 'ES', 127, 'LU', 49.55833000, 5.88056000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q602441'),
(66823, 'Rambrouch', 1516, 'RD', 127, 'LU', 49.83083000, 5.84500000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q13104719'),
(66824, 'Reckange-sur-Mess', 1517, 'ES', 127, 'LU', 49.56250000, 6.00889000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917049'),
(66825, 'Redange-sur-Attert', 1516, 'RD', 127, 'LU', 49.76437000, 5.88997000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q16764501'),
(66826, 'Reisdorf', 1513, 'DI', 127, 'LU', 49.86861000, 6.26778000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q16764501'),
(66827, 'Remich', 1519, 'RM', 127, 'LU', 49.54500000, 6.36694000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q734284'),
(66828, 'Rodange', 1517, 'ES', 127, 'LU', 49.54639000, 5.84083000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1444135'),
(66829, 'Roeser', 1517, 'ES', 127, 'LU', 49.53721000, 6.14629000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1444135'),
(66830, 'Rollingen', 1522, 'ME', 127, 'LU', 49.74167000, 6.11444000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2640199'),
(66831, 'Rosport', 1515, 'EC', 127, 'LU', 49.80470000, 6.50532000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2640199'),
(66832, 'Rumelange', 1517, 'ES', 127, 'LU', 49.45964000, 6.03089000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q914847'),
(66833, 'Saeul', 1516, 'RD', 127, 'LU', 49.72722000, 5.98639000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969731'),
(66834, 'Sandweiler', 1514, 'L', 127, 'LU', 49.61471000, 6.22221000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q3917065'),
(66835, 'Sanem', 1517, 'ES', 127, 'LU', 49.54806000, 5.92889000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917065'),
(66836, 'Schengen', 1519, 'RM', 127, 'LU', 49.47000000, 6.36200000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3916795'),
(66837, 'Schieren', 1513, 'DI', 127, 'LU', 49.83056000, 6.09861000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969739'),
(66838, 'Schifflange', 1517, 'ES', 127, 'LU', 49.50639000, 6.01278000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969739'),
(66839, 'Schouweiler', 1518, 'CA', 127, 'LU', 49.58250000, 5.95639000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q2609947'),
(66840, 'Schrassig', 1514, 'L', 127, 'LU', 49.61014000, 6.25903000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q1026554'),
(66841, 'Schuttrange', 1514, 'L', 127, 'LU', 49.62056000, 6.26861000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q3917120'),
(66842, 'Septfontaines', 1518, 'CA', 127, 'LU', 49.70111000, 5.96722000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917095'),
(66843, 'Soleuvre', 1517, 'ES', 127, 'LU', 49.52148000, 5.93781000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q219089'),
(66844, 'Stadtbredimus', 1519, 'RM', 127, 'LU', 49.56278000, 6.36444000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q219089'),
(66845, 'Steinfort', 1518, 'CA', 127, 'LU', 49.66139000, 5.91917000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q985478'),
(66846, 'Steinsel', 1514, 'L', 127, 'LU', 49.67694000, 6.12389000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q985478'),
(66847, 'Strassen', 1514, 'L', 127, 'LU', 49.62056000, 6.07333000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q985478'),
(66848, 'Tandel', 1523, 'VD', 127, 'LU', 49.89750000, 6.18333000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q985478'),
(66849, 'Troisvierges', 1521, 'CL', 127, 'LU', 50.12111000, 6.00028000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q985478'),
(66850, 'Tuntange', 1522, 'ME', 127, 'LU', 49.71778000, 6.01028000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917054'),
(66851, 'Tétange', 1517, 'ES', 127, 'LU', 49.47583000, 6.04222000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q1903325'),
(66852, 'Useldange', 1516, 'RD', 127, 'LU', 49.76972000, 5.98222000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917067'),
(66853, 'Vianden', 1523, 'VD', 127, 'LU', 49.93500000, 6.20889000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q836082'),
(66854, 'Vichten', 1516, 'RD', 127, 'LU', 49.80306000, 6.00000000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q836082'),
(66855, 'Wahl', 1516, 'RD', 127, 'LU', 49.83667000, 5.90639000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q836082'),
(66856, 'Waldbillig', 1515, 'EC', 127, 'LU', 49.79636000, 6.28431000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q20969783'),
(66857, 'Waldbredimus', 1519, 'RM', 127, 'LU', 49.55676000, 6.28789000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917667'),
(66858, 'Walferdange', 1514, 'L', 127, 'LU', 49.66321000, 6.13224000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q3917667'),
(66859, 'Warken', 1513, 'DI', 127, 'LU', 49.85918000, 6.08415000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q1828890'),
(66860, 'Wasserbillig', 1520, 'G', 127, 'LU', 49.71534000, 6.49861000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q1026497'),
(66861, 'Weiler-la-Tour', 1514, 'L', 127, 'LU', 49.54083000, 6.20083000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q1026497'),
(66862, 'Weiswampach', 1521, 'CL', 127, 'LU', 50.13722000, 6.07500000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917108'),
(66863, 'Wiltz', 1526, 'WI', 127, 'LU', 49.96547000, 5.93390000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q16765057'),
(66864, 'Wilwerwiltz', 1526, 'WI', 127, 'LU', 49.98889000, 5.99917000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q278454'),
(66865, 'Wincrange', 1521, 'CL', 127, 'LU', 50.05333000, 5.91917000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917114'),
(66866, 'Winseler', 1526, 'WI', 127, 'LU', 49.96778000, 5.89028000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q3917114'),
(66867, 'Wormeldange', 1520, 'G', 127, 'LU', 49.61114000, 6.40546000, '2019-10-05 23:07:59', '2024-12-16 21:17:36', 1, 'Q3917114'),
(66868, 'Aglona', 4483, '001', 120, 'LV', 56.13274000, 27.00682000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q372999'),
(66869, 'Ainaži', 4418, '086', 120, 'LV', 57.86348000, 24.35853000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q405931'),
(66870, 'Aizkraukle', 4472, '002', 120, 'LV', 56.60477000, 25.25534000, '2019-10-05 23:07:59', '2019-10-05 23:07:59', 1, 'Q411715'),
(66871, 'Aizpute', 4508, '003', 120, 'LV', 56.72108000, 21.60156000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q411715'),
(66872, 'Aknīste', 4438, '004', 120, 'LV', 56.16152000, 25.74783000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q420333'),
(66873, 'Aloja', 4418, '005', 120, 'LV', 57.76723000, 24.87743000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q659019'),
(66874, 'Alsunga', 4490, '006', 120, 'LV', 56.98194000, 21.55938000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q432829'),
(66875, 'Alūksne', 4487, '007', 120, 'LV', 57.42162000, 27.04662000, '2019-10-05 23:07:59', '2020-05-01 17:22:59', 1, 'Q291333'),
(66876, 'Ape', 4462, '009', 120, 'LV', 57.53928000, 26.69291000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q618119'),
(66877, 'Auce', 4437, '010', 120, 'LV', 56.45981000, 22.90169000, '2019-10-05 23:07:59', '2024-12-19 16:09:42', 1, 'Q758578'),
(66878, 'Baldone', 4435, '013', 120, 'LV', 56.74451000, 24.40078000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q789351'),
(66879, 'Baloži', 4435, '052', 120, 'LV', 56.87643000, 24.11825000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q757043'),
(66880, 'Baltinava', 4505, '014', 120, 'LV', 56.94394000, 27.64401000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q805759'),
(66881, 'Balvi', 4505, '015', 120, 'LV', 57.13130000, 27.26583000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q218402'),
(66882, 'Bauska', 4465, '016', 120, 'LV', 56.40794000, 24.19443000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q498256'),
(66883, 'Bolderaja', 4491, 'RIX', 120, 'LV', 57.03132000, 24.05571000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q2385722'),
(66884, 'Brocēni', 4439, '018', 120, 'LV', 56.67890000, 22.56945000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q840836'),
(66885, 'Carnikava', 4454, '020', 120, 'LV', 57.12935000, 24.28423000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q1044023'),
(66886, 'Cesvaine', 4433, '021', 120, 'LV', 56.96754000, 26.30764000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q757050'),
(66887, 'Cibla', 4501, '023', 120, 'LV', 56.54980000, 27.88370000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1018485'),
(66888, 'Cēsis', 4469, '022', 120, 'LV', 57.31188000, 25.27456000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q107582'),
(66889, 'Dagda', 4474, '024', 120, 'LV', 56.09512000, 27.53723000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q757101'),
(66890, 'Daugavgrīva', 4491, 'RIX', 120, 'LV', 57.04315000, 24.03613000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q1172924'),
(66891, 'Daugavpils', 4492, '025', 120, 'LV', 55.88333000, 26.53333000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q80021'),
(66892, 'Dobele', 4437, '026', 120, 'LV', 56.62372000, 23.27510000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q729447'),
(66893, 'Dundaga', 4459, '027', 120, 'LV', 57.50498000, 22.35041000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q962653'),
(66894, 'Engure', 4409, '090', 120, 'LV', 57.16061000, 23.22527000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1023733'),
(66895, 'Garkalne', 4493, '031', 120, 'LV', 57.04486000, 24.41951000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1006622'),
(66896, 'Grobiņa', 4508, '032', 120, 'LV', 56.53521000, 21.16782000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q757049'),
(66897, 'Gulbene', 4400, '033', 120, 'LV', 57.17767000, 26.75291000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q753010'),
(66898, 'Iecava', 4465, '034', 120, 'LV', 56.59766000, 24.20763000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1023764'),
(66899, 'Ikšķile', 4416, '035', 120, 'LV', 56.83399000, 24.49679000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1023764'),
(66900, 'Ilūkste', 4492, '036', 120, 'LV', 55.97754000, 26.29655000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q580373'),
(66901, 'Inčukalns', 4476, '037', 120, 'LV', 57.09867000, 24.68630000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q987170'),
(66902, 'Jaunaglona', 4483, '073', 120, 'LV', 56.16066000, 27.00714000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q987170'),
(66903, 'Jaunciems', 4491, 'RIX', 120, 'LV', 57.03910000, 24.17413000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q987170'),
(66904, 'Jaunjelgava', 4472, '038', 120, 'LV', 56.61319000, 25.08316000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q835771'),
(66905, 'Jaunpils', 4409, '040', 120, 'LV', 56.73137000, 23.01247000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1684136'),
(66906, 'Jelgava', 4500, 'JEL', 120, 'LV', 56.65000000, 23.71278000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q1684136'),
(66907, 'Jumprava', 4416, '067', 120, 'LV', 56.67613000, 24.97210000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q15219041'),
(66908, 'Jēkabpils', 4438, '042', 120, 'LV', 56.49903000, 25.85735000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q191120'),
(66909, 'Jūrmala', 4446, 'JUR', 120, 'LV', 56.96800000, 23.77038000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q178382'),
(66910, 'Kandava', 4409, '043', 120, 'LV', 57.04087000, 22.77466000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q849152'),
(66911, 'Karosta', 4460, 'LPX', 120, 'LV', 56.55128000, 21.01287000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q849152'),
(66912, 'Kocēni', 4473, '045', 120, 'LV', 57.52057000, 25.33821000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q987208'),
(66913, 'Koknese', 4472, '046', 120, 'LV', 56.65163000, 25.43637000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q504681'),
(66914, 'Krustpils', 4438, '042', 120, 'LV', 56.51068000, 25.86117000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q745661'),
(66915, 'Krāslava', 4474, '047', 120, 'LV', 55.89514000, 27.16799000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q638592'),
(66916, 'Kuldīga', 4490, '050', 120, 'LV', 56.97399000, 21.95721000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q155281'),
(66917, 'Kārsava', 4501, '044', 120, 'LV', 56.78405000, 27.68829000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q155281'),
(66918, 'Lielvārde', 4416, '053', 120, 'LV', 56.72066000, 24.80743000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q597346'),
(66919, 'Liepāja', 4460, 'LPX', 120, 'LV', 56.50474000, 21.01085000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q167668'),
(66920, 'Lieģi', 4508, '028', 120, 'LV', 56.58173000, 21.33399000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q6544506'),
(66921, 'Limbaži', 4418, '054', 120, 'LV', 57.51287000, 24.71941000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q166684'),
(66922, 'Lubāna', 4433, '057', 120, 'LV', 56.90425000, 26.71606000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q844477'),
(66923, 'Ludza', 4501, '058', 120, 'LV', 56.53958000, 27.71891000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q744259'),
(66924, 'Līgatne', 4469, '055', 120, 'LV', 57.23429000, 25.04059000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q744259'),
(66925, 'Līvāni', 4401, '056', 120, 'LV', 56.35431000, 26.17579000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q849156'),
(66926, 'Madona', 4433, '059', 120, 'LV', 56.85329000, 26.21698000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q586048'),
(66927, 'Mazsalaca', 4473, '060', 120, 'LV', 57.86329000, 25.05475000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q844468'),
(66928, 'Mežaparks', 4491, 'RIX', 120, 'LV', 57.00008000, 24.15997000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q1926523'),
(66929, 'Mālpils', 4476, '061', 120, 'LV', 57.01010000, 24.95783000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1023743'),
(66930, 'Mārupe', 4450, '062', 120, 'LV', 56.90544000, 24.05113000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q987175'),
(66931, 'Mūrmuiža', 4473, '017', 120, 'LV', 57.47312000, 25.49174000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q985288'),
(66932, 'Naukšēni', 4473, '064', 120, 'LV', 57.88349000, 25.45609000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q1712746'),
(66933, 'Nereta', 4472, '065', 120, 'LV', 56.20279000, 25.30752000, '2019-10-05 23:08:00', '2024-12-19 16:09:42', 1, 'Q987161'),
(66934, 'Nīca', 4508, '066', 120, 'LV', 56.34601000, 21.06401000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q987165'),
(66935, 'Ogre', 4416, '067', 120, 'LV', 56.81620000, 24.61401000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q731915'),
(66936, 'Olaine', 4417, '068', 120, 'LV', 56.79472000, 23.93580000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q731915'),
(66937, 'Ozolnieki', 4424, '069', 120, 'LV', 56.68986000, 23.77610000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1023747'),
(66938, 'Pilsrundāle', 4465, '083', 120, 'LV', 56.41812000, 24.01625000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1027033'),
(66939, 'Piltene', 4403, '106', 120, 'LV', 57.22426000, 21.67439000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q838491'),
(66940, 'Piņķi', 4450, '012', 120, 'LV', 56.94189000, 23.91365000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q4363090'),
(66941, 'Preiļi', 4483, '073', 120, 'LV', 56.29444000, 26.72459000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q858476'),
(66942, 'Priekule', 4508, '074', 120, 'LV', 56.44679000, 21.58968000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q908683'),
(66943, 'Priekuļi', 4469, '075', 120, 'LV', 57.31500000, 25.36147000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1240381'),
(66944, 'Pāvilosta', 4508, '071', 120, 'LV', 56.88791000, 21.18593000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q840717'),
(66945, 'Pļaviņas', 4472, '072', 120, 'LV', 56.61780000, 25.72552000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q567747'),
(66946, 'Rauna', 4462, '076', 120, 'LV', 57.33173000, 25.60947000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1493534'),
(66947, 'Riebiņi', 4483, '078', 120, 'LV', 56.34280000, 26.79995000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q942401'),
(66948, 'Riga', 4491, 'RIX', 120, 'LV', 56.94600000, 24.10589000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q1773'),
(66949, 'Roja', 4459, '079', 120, 'LV', 57.50146000, 22.80881000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1773'),
(66950, 'Ropaži', 4493, '080', 120, 'LV', 56.97470000, 24.63295000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q120761'),
(66951, 'Rucava', 4508, '081', 120, 'LV', 56.16314000, 21.16156000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q987180'),
(66952, 'Rugāji', 4505, '082', 120, 'LV', 57.00325000, 27.13371000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q2174949'),
(66953, 'Rēzekne', 4509, 'REZ', 120, 'LV', 56.51028000, 27.34000000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q180379'),
(66954, 'Rūjiena', 4473, '084', 120, 'LV', 57.89752000, 25.33155000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q765976'),
(66955, 'Sabile', 4459, '097', 120, 'LV', 57.04577000, 22.57261000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q650475'),
(66956, 'Salacgrīva', 4418, '086', 120, 'LV', 57.75312000, 24.35895000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q659665'),
(66957, 'Salaspils', 4402, '087', 120, 'LV', 56.86014000, 24.36544000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q732986'),
(66958, 'Saldus', 4439, '088', 120, 'LV', 56.66363000, 22.48807000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q744247'),
(66959, 'Saulkrasti', 4443, '089', 120, 'LV', 57.26224000, 24.41471000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q840823'),
(66960, 'Seda', 4473, '096', 120, 'LV', 57.65042000, 25.75089000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q578527'),
(66961, 'Sigulda', 4476, '091', 120, 'LV', 57.15375000, 24.85953000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q465698'),
(66962, 'Skrunda', 4490, '093', 120, 'LV', 56.67749000, 22.01649000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q267871'),
(66963, 'Skrīveri', 4472, '092', 120, 'LV', 56.64500000, 25.12058000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1023755'),
(66964, 'Smiltene', 4462, '094', 120, 'LV', 57.42444000, 25.90164000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q840806'),
(66965, 'Smārde', 4409, '029', 120, 'LV', 56.95420000, 23.33736000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q7546838'),
(66966, 'Staicele', 4418, '005', 120, 'LV', 57.83579000, 24.74706000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q844464'),
(66967, 'Stalbe', 4469, '070', 120, 'LV', 57.37065000, 25.03106000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q7597217'),
(66968, 'Stende', 4459, '097', 120, 'LV', 57.14497000, 22.53482000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q909654'),
(66969, 'Strenči', 4473, '096', 120, 'LV', 57.62574000, 25.68535000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q851399'),
(66970, 'Talsi', 4459, '097', 120, 'LV', 57.24562000, 22.58137000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q520405'),
(66971, 'Tukums', 4409, '090', 120, 'LV', 56.96764000, 23.15554000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q636935'),
(66972, 'Tērvete', 4437, '098', 120, 'LV', 56.47989000, 23.38895000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1973559'),
(66973, 'Tīreļi', 4424, '041', 120, 'LV', 56.83991000, 23.58902000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q27486331'),
(66974, 'Ulbroka', 4493, '095', 120, 'LV', 56.93630000, 24.30387000, '2019-10-05 23:08:00', '2024-12-19 16:09:43', 1, 'Q1027029'),
(66975, 'Vaiņode', 4508, '100', 120, 'LV', 56.41848000, 21.85405000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q1023741'),
(66976, 'Valdemārpils', 4459, '097', 120, 'LV', 57.37068000, 22.59188000, '2019-10-05 23:08:00', '2020-05-01 17:22:59', 1, 'Q846341'),
(66977, 'Valka', 4425, '101', 120, 'LV', 57.77520000, 26.01013000, '2019-10-05 23:08:00', '2019-10-05 23:08:00', 1, 'Q323774');

