INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(48303, 'Aspatria', 2336, 'ENG', 232, 'GB', 54.76574000, -3.32783000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1857198'),
(48304, 'Aston', 2336, 'ENG', 232, 'GB', 52.50000000, -1.88333000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q649838'),
(48305, 'Aston Clinton', 2336, 'ENG', 232, 'GB', 51.80020000, -0.72540000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2044251'),
(48306, 'Aston-on-Trent', 2336, 'E<PERSON>', 232, 'GB', 52.86172000, -1.38642000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2000768'),
(48307, '<PERSON>t<PERSON> <PERSON>', 2336, 'ENG', 232, 'GB', 52.25993000, -1.93754000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4811794'),
(48308, 'At<PERSON>tone', 2336, 'ENG', 232, 'GB', 52.57536000, -1.54693000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q747778'),
(48309, 'Atherton', 2336, 'ENG', 232, 'GB', 53.52371000, -2.49354000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q755504'),
(48310, 'Attleborough', 2336, 'ENG', 232, 'GB', 52.51779000, 1.01572000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1975526'),
(48311, 'Atworth', 2336, 'ENG', 232, 'GB', 51.39234000, -2.19297000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2199249'),
(48312, 'Auchinleck', 2335, 'SCT', 232, 'GB', 55.47157000, -4.29337000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48313, 'Auchterarder', 2335, 'SCT', 232, 'GB', 56.29612000, -3.70692000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q758623'),
(48314, 'Auchtermuchty', 2335, 'SCT', 232, 'GB', 56.29158000, -3.23428000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q758624'),
(48315, 'Auckley', 2336, 'ENG', 232, 'GB', 53.50386000, -1.02174000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48316, 'Audlem', 2336, 'ENG', 232, 'GB', 52.98956000, -2.50706000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48317, 'Audley', 2336, 'ENG', 232, 'GB', 53.05000000, -2.30000000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48318, 'Aveley', 2336, 'ENG', 232, 'GB', 51.49987000, 0.25174000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48319, 'Avening', 2336, 'ENG', 232, 'GB', 51.68010000, -2.16903000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48320, 'Aviemore', 2335, 'SCT', 232, 'GB', 57.19553000, -3.82590000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q790938'),
(48321, 'Awsworth', 2336, 'ENG', 232, 'GB', 52.98912000, -1.28354000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48322, 'Axbridge', 2336, 'ENG', 232, 'GB', 51.28466000, -2.82078000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q651889'),
(48323, 'Axminster', 2336, 'ENG', 232, 'GB', 50.78259000, -2.99787000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q792596'),
(48324, 'Aylesbury', 2336, 'ENG', 232, 'GB', 51.81665000, -0.81458000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q213474'),
(48325, 'Aylesford', 2336, 'ENG', 232, 'GB', 51.30374000, 0.47936000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q793057'),
(48326, 'Aylesham', 2336, 'ENG', 232, 'GB', 51.22539000, 1.20157000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2485028'),
(48327, 'Aylsham', 2336, 'ENG', 232, 'GB', 52.79672000, 1.25107000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q237625'),
(48328, 'Ayr', 2335, 'SCT', 232, 'GB', 55.46273000, -4.63393000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q654216'),
(48329, 'Babworth', 2336, 'ENG', 232, 'GB', 53.31799000, -0.97583000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2298437'),
(48330, 'Backworth', 2336, 'ENG', 232, 'GB', 55.04229000, -1.52779000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q3632663'),
(48331, 'Bacton', 2336, 'ENG', 232, 'GB', 52.26667000, 1.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2578348'),
(48332, 'Bacup', 2336, 'ENG', 232, 'GB', 53.70336000, -2.20070000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2514944'),
(48333, 'Badsey', 2336, 'ENG', 232, 'GB', 52.08819000, -1.89925000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4841152'),
(48334, 'Badsworth', 2336, 'ENG', 232, 'GB', 53.62876000, -1.30128000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2646554'),
(48335, 'Bagillt', 2338, 'WLS', 232, 'GB', 53.26540000, -3.16551000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2646554'),
(48336, 'Bagshot', 2336, 'ENG', 232, 'GB', 51.36069000, -0.68802000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4842133'),
(48337, 'Bagworth', 2336, 'ENG', 232, 'GB', 52.67265000, -1.34274000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4842193'),
(48338, 'Baildon', 2336, 'ENG', 232, 'GB', 53.84711000, -1.78785000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2575062'),
(48339, 'Bakewell', 2336, 'ENG', 232, 'GB', 53.21338000, -1.67481000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2462719'),
(48340, 'Bala', 2338, 'WLS', 232, 'GB', 52.91111000, -3.59722000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q171923'),
(48341, 'Balcombe', 2336, 'ENG', 232, 'GB', 51.05726000, -0.13450000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2880645'),
(48342, 'Baldock', 2336, 'ENG', 232, 'GB', 51.98781000, -0.18835000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q789348'),
(48343, 'Balerno', 2335, 'SCT', 232, 'GB', 55.88437000, -3.33975000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2880785'),
(48344, 'Balfron', 2335, 'SCT', 232, 'GB', 56.06809000, -4.33559000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1010220'),
(48345, 'Balintore', 2335, 'SCT', 232, 'GB', 57.75564000, -3.91232000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1886025'),
(48346, 'Ballater', 2335, 'SCT', 232, 'GB', 57.05011000, -3.03798000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q805175'),
(48347, 'Ballinamallard', 2337, 'NIR', 232, 'GB', 54.40000000, -7.58333000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q2514196'),
(48348, 'Ballingry', 2335, 'SCT', 232, 'GB', 56.16392000, -3.32841000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1012304'),
(48349, 'Ballintoy Harbour', 2337, 'NIR', 232, 'GB', 55.24422000, -6.36919000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q1012304'),
(48350, 'Balloch', 2335, 'SCT', 232, 'GB', 56.00000000, -4.58333000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q805302'),
(48351, 'Ballycastle', 2337, 'NIR', 232, 'GB', 55.20444000, -6.24298000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q805405'),
(48352, 'Ballyclare', 2337, 'NIR', 232, 'GB', 54.75089000, -5.99944000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q805403'),
(48353, 'Ballygowan', 2337, 'NIR', 232, 'GB', 54.50165000, -5.79168000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q116772'),
(48354, 'Ballykelly', 2337, 'NIR', 232, 'GB', 55.04425000, -7.01855000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q575071'),
(48355, 'Ballymena', 2337, 'NIR', 232, 'GB', 54.86357000, -6.27628000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q805451'),
(48356, 'Ballymoney', 2337, 'NIR', 232, 'GB', 55.07080000, -6.51009000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q805450'),
(48357, 'Ballynahinch', 2337, 'NIR', 232, 'GB', 54.40230000, -5.89717000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q58125'),
(48358, 'Ballypatrick', 2337, 'NIR', 232, 'GB', 55.18112000, -6.15020000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q58125'),
(48359, 'Ballywalter', 2337, 'NIR', 232, 'GB', 54.54329000, -5.48475000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q149569'),
(48360, 'Balmedie', 2335, 'SCT', 232, 'GB', 57.25052000, -2.06163000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q805468'),
(48361, 'Balmullo', 2335, 'SCT', 232, 'GB', 56.37694000, -2.92940000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q955711'),
(48362, 'Balsall Common', 2336, 'ENG', 232, 'GB', 52.39186000, -1.65040000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q3633504'),
(48363, 'Balsham', 2336, 'ENG', 232, 'GB', 52.13238000, 0.31586000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4852574'),
(48364, 'Bamburgh', 2336, 'ENG', 232, 'GB', 55.60652000, -1.71704000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q805983'),
(48365, 'Bampton', 2336, 'ENG', 232, 'GB', 51.72634000, -1.54547000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2012293'),
(48366, 'Banbridge', 2337, 'NIR', 232, 'GB', 54.35000000, -6.28333000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q58270'),
(48367, 'Banbury', 2336, 'ENG', 232, 'GB', 52.06320000, -1.34222000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q806160'),
(48368, 'Banchory', 2335, 'SCT', 232, 'GB', 57.05168000, -2.48824000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q806177'),
(48369, 'Banff', 2335, 'SCT', 232, 'GB', 57.********, -2.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q54809'),
(48370, 'Bangor', 2338, 'WLS', 232, 'GB', 53.********, -4.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q234178'),
(48371, 'Bangor', 2337, 'NIR', 232, 'GB', 54.********, -5.********, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q806551'),
(48372, 'Banham', 2336, 'ENG', 232, 'GB', 52.********, 1.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2700979'),
(48373, 'Bankfoot', 2335, 'SCT', 232, 'GB', 56.********, -3.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1013600'),
(48374, 'Banknock', 2335, 'SCT', 232, 'GB', 55.********, -3.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1013600'),
(48375, 'Banks', 2336, 'ENG', 232, 'GB', 53.********, -2.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4856470'),
(48376, 'Bannockburn', 2335, 'SCT', 232, 'GB', 56.********, -3.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q806906'),
(48377, 'Banstead', 2336, 'ENG', 232, 'GB', 51.********, -0.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2280322'),
(48378, 'Banwell', 2336, 'ENG', 232, 'GB', 51.********, -2.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2229524'),
(48379, 'Bar Hill', 2336, 'ENG', 232, 'GB', 52.********, 0.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2028934'),
(48380, 'Barbican', 2336, 'ENG', 232, 'GB', 51.********, -0.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2028934'),
(48381, 'Bardney', 2336, 'ENG', 232, 'GB', 53.********, -0.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q808025'),
(48382, 'Bardsey', 2336, 'ENG', 232, 'GB', 53.********, -1.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4860222'),
(48383, 'Bargeddie', 2335, 'SCT', 232, 'GB', 55.85366000, -4.07846000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4860222'),
(48384, 'Bargoed', 2338, 'WLS', 232, 'GB', 51.********, -3.23333000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2664179'),
(48385, 'Barham', 2336, 'ENG', 232, 'GB', 51.20570000, 1.15734000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1836548'),
(48386, 'Barking', 2336, 'ENG', 232, 'GB', 51.53333000, 0.08333000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q377720'),
(48387, 'Barkisland', 2336, 'ENG', 232, 'GB', 53.67614000, -1.91840000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4861069'),
(48388, 'Barlaston', 2336, 'ENG', 232, 'GB', 52.94200000, -2.17050000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2353329'),
(48389, 'Barlborough', 2336, 'ENG', 232, 'GB', 53.28795000, -1.28815000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4425599'),
(48390, 'Barlby', 2336, 'ENG', 232, 'GB', 53.79964000, -1.04061000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q693142'),
(48391, 'Barlestone', 2336, 'ENG', 232, 'GB', 52.64718000, -1.37013000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2175610'),
(48392, 'Barmby on the Marsh', 2336, 'ENG', 232, 'GB', 53.74896000, -0.95607000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2415035'),
(48393, 'Barmouth', 2338, 'WLS', 232, 'GB', 52.72377000, -4.05748000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q672545'),
(48394, 'Barnack', 2336, 'ENG', 232, 'GB', 52.63181000, -0.40821000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q3355527'),
(48395, 'Barnard Castle', 2336, 'ENG', 232, 'GB', 54.54150000, -1.91900000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q808437'),
(48396, 'Barnburgh', 2336, 'ENG', 232, 'GB', 53.52408000, -1.27300000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2786745'),
(48397, 'Barnet', 2336, 'ENG', 232, 'GB', 51.65000000, -0.20000000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2786745'),
(48398, 'Barnetby le Wold', 2336, 'ENG', 232, 'GB', 53.57480000, -0.40607000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q602355'),
(48399, 'Barnham', 2336, 'ENG', 232, 'GB', 50.83120000, -0.63789000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q962567'),
(48400, 'Barnoldswick', 2336, 'ENG', 232, 'GB', 53.91711000, -2.18705000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2049578'),
(48401, 'Barnsbury', 2336, 'ENG', 232, 'GB', 51.54067000, -0.11675000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2049578'),
(48402, 'Barnsley', 2336, 'ENG', 232, 'GB', 53.53333000, -1.50000000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1857382'),
(48403, 'Barnstaple', 2336, 'ENG', 232, 'GB', 51.08022000, -4.05808000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q808532'),
(48404, 'Barnt Green', 2336, 'ENG', 232, 'GB', 52.35902000, -2.00715000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q582689'),
(48405, 'Barnwood', 2336, 'ENG', 232, 'GB', 51.86393000, -2.20087000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4862033'),
(48406, 'Barra', 2335, 'SCT', 232, 'GB', 56.98035000, -7.45731000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4862033'),
(48407, 'Barrhead', 2335, 'SCT', 232, 'GB', 55.79916000, -4.39285000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2598252'),
(48408, 'Barrow in Furness', 2336, 'ENG', 232, 'GB', 54.11094000, -3.22758000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q671892'),
(48409, 'Barrow upon Humber', 2336, 'ENG', 232, 'GB', 53.67550000, -0.38062000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1787722'),
(48410, 'Barrow upon Soar', 2336, 'ENG', 232, 'GB', 52.75178000, -1.14601000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q808993'),
(48411, 'Barrowby', 2336, 'ENG', 232, 'GB', 52.91636000, -0.69094000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2491304'),
(48412, 'Barrowford', 2336, 'ENG', 232, 'GB', 53.84650000, -2.21838000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q303243'),
(48413, 'Barry', 2338, 'WLS', 232, 'GB', 51.39979000, -3.28380000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q809009'),
(48414, 'Bartley Green', 2336, 'ENG', 232, 'GB', 52.43532000, -1.99707000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q809009'),
(48415, 'Barton under Needwood', 2336, 'ENG', 232, 'GB', 52.76268000, -1.72400000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4865583'),
(48416, 'Barton upon Humber', 2336, 'ENG', 232, 'GB', 53.68915000, -0.44377000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q809550'),
(48417, 'Barton-le-Clay', 2336, 'ENG', 232, 'GB', 51.96598000, -0.42731000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q790951'),
(48418, 'Baschurch', 2336, 'ENG', 232, 'GB', 52.78848000, -2.85284000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q3812274'),
(48419, 'Basford', 2336, 'ENG', 232, 'GB', 52.96667000, -1.18333000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4866737'),
(48420, 'Basildon', 2336, 'ENG', 232, 'GB', 51.56844000, 0.45782000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q216649'),
(48421, 'Basingstoke', 2336, 'ENG', 232, 'GB', 51.26249000, -1.08708000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q810196'),
(48422, 'Baslow', 2336, 'ENG', 232, 'GB', 53.24811000, -1.62246000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4867787'),
(48423, 'Bassingbourn', 2336, 'ENG', 232, 'GB', 52.07821000, -0.05390000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q23900657'),
(48424, 'Bassingham', 2336, 'ENG', 232, 'GB', 53.12881000, -0.63765000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q927842'),
(48425, 'Baston', 2336, 'ENG', 232, 'GB', 52.71311000, -0.35173000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4868365'),
(48426, 'Bath', 2336, 'ENG', 232, 'GB', 51.37510000, -2.36172000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q22889'),
(48427, 'Bath and North East Somerset', 2336, 'ENG', 232, 'GB', 51.33333000, -2.50000000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q810793'),
(48428, 'Bathgate', 2335, 'SCT', 232, 'GB', 55.90204000, -3.64398000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q810794'),
(48429, 'Batley', 2336, 'ENG', 232, 'GB', 53.70291000, -1.63370000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q810847'),
(48430, 'Battersea', 2336, 'ENG', 232, 'GB', 51.47475000, -0.15547000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q810847'),
(48431, 'Battle', 2336, 'ENG', 232, 'GB', 50.91732000, 0.48417000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q737593'),
(48432, 'Bawtry', 2336, 'ENG', 232, 'GB', 53.43146000, -1.01878000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q586090'),
(48433, 'Bay Horse', 2336, 'ENG', 232, 'GB', 53.96867000, -2.77603000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4874018'),
(48434, 'Bayston Hill', 2336, 'ENG', 232, 'GB', 52.67550000, -2.76156000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q3811893'),
(48435, 'Bayswater', 2336, 'ENG', 232, 'GB', 51.51116000, -0.18426000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q41890'),
(48436, 'Beaconsfield', 2336, 'ENG', 232, 'GB', 51.61219000, -0.64732000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q812906'),
(48437, 'Beadnell', 2336, 'ENG', 232, 'GB', 55.55670000, -1.63250000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2689370'),
(48438, 'Beaminster', 2336, 'ENG', 232, 'GB', 50.80900000, -2.73910000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q812945'),
(48439, 'Bearsden', 2335, 'SCT', 232, 'GB', 55.91536000, -4.33279000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1908426'),
(48440, 'Beauly', 2335, 'SCT', 232, 'GB', 57.48345000, -4.46144000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q523335'),
(48441, 'Beaumaris', 2338, 'WLS', 232, 'GB', 53.26315000, -4.09233000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1992389'),
(48442, 'Bebington', 2336, 'ENG', 232, 'GB', 53.35000000, -3.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q813552'),
(48443, 'Beccles', 2336, 'ENG', 232, 'GB', 52.45936000, 1.56465000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q813574'),
(48444, 'Beckenham', 2336, 'ENG', 232, 'GB', 51.40878000, -0.02526000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q813574'),
(48445, 'Beckingham', 2336, 'ENG', 232, 'GB', 53.40000000, -0.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48446, 'Becontree', 2336, 'ENG', 232, 'GB', 51.55290000, 0.12900000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48447, 'Bedale', 2336, 'ENG', 232, 'GB', 54.28811000, -1.59181000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q813810'),
(48448, 'Beddau', 2338, 'WLS', 232, 'GB', 51.55398000, -3.35814000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48449, 'Bedford', 2336, 'ENG', 232, 'GB', 52.18831000, -0.45316000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q894077'),
(48450, 'Bedlington', 2336, 'ENG', 232, 'GB', 55.13061000, -1.59319000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48451, 'Bedlinog', 2338, 'WLS', 232, 'GB', 51.70432000, -3.31306000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48452, 'Bedwas', 2338, 'WLS', 232, 'GB', 51.59183000, -3.19886000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48453, 'Bedworth', 2336, 'ENG', 232, 'GB', 52.47910000, -1.46909000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q813962'),
(48454, 'Beeford', 2336, 'ENG', 232, 'GB', 53.96999000, -0.28913000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48455, 'Beighton', 2336, 'ENG', 232, 'GB', 53.33333000, -1.33333000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '*********'),
(48456, 'Beith', 2335, 'SCT', 232, 'GB', 55.74923000, -4.63680000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48457, 'Belbroughton', 2336, 'ENG', 232, 'GB', 52.39177000, -2.11884000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48458, 'Belfast', 2337, 'NIR', 232, 'GB', 54.********, -5.********, '2019-10-05 22:49:16', '2021-01-23 14:35:23', 1, 'Q10686'),
(48459, 'Belford', 2336, 'ENG', 232, 'GB', 55.********, -1.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1617115'),
(48460, 'Bellaghy', 2337, 'NIR', 232, 'GB', 54.********, -6.********, '2019-10-05 22:49:16', '2021-01-23 14:35:23', 1, 'Q619278'),
(48461, 'Bellingham', 2336, 'ENG', 232, 'GB', 55.********, -2.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q451687'),
(48462, 'Bellsbank', 2335, 'SCT', 232, 'GB', 55.********, -4.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q451687'),
(48463, 'Bellshill', 2335, 'SCT', 232, 'GB', 55.********, -4.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q680984'),
(48464, 'Belmont', 2336, 'ENG', 232, 'GB', 52.********, -2.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q680984'),
(48465, 'Belper', 2336, 'ENG', 232, 'GB', 53.********, -1.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2080375'),
(48466, 'Belsize Park', 2336, 'ENG', 232, 'GB', 51.********, -0.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2080375'),
(48467, 'Belton', 2336, 'ENG', 232, 'GB', 53.********, -0.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4884762'),
(48468, 'Belvedere', 2336, 'ENG', 232, 'GB', 51.********, 0.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4884762'),
(48469, 'Bembridge', 2336, 'ENG', 232, 'GB', 50.********, -1.********, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2624470'),
(48470, 'Bempton', 2336, 'ENG', 232, 'GB', 54.13036000, -0.17853000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2225262'),
(48471, 'Benbecula', 2335, 'SCT', 232, 'GB', 57.44737000, -7.34273000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2225262'),
(48472, 'Benllech', 2338, 'WLS', 232, 'GB', 53.32044000, -4.22607000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q402177'),
(48473, 'Benson', 2336, 'ENG', 232, 'GB', 51.62073000, -1.10979000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q2351343'),
(48474, 'Bentley', 2336, 'ENG', 232, 'GB', 53.53333000, -1.15000000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q3638330'),
(48475, 'Benwell', 2336, 'ENG', 232, 'GB', 54.97296000, -1.66926000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q3638330'),
(48476, 'Bere Alston', 2336, 'ENG', 232, 'GB', 50.48233000, -4.19034000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1241007'),
(48477, 'Bere Regis', 2336, 'ENG', 232, 'GB', 50.75371000, -2.21553000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1215902'),
(48478, 'Berkeley', 2336, 'ENG', 232, 'GB', 51.69111000, -2.45917000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q584443'),
(48479, 'Berkhamsted', 2336, 'ENG', 232, 'GB', 51.76040000, -0.56528000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q19784'),
(48480, 'Berwick-Upon-Tweed', 2336, 'ENG', 232, 'GB', 55.76536000, -2.01186000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q504678'),
(48481, 'Bethesda', 2338, 'WLS', 232, 'GB', 53.18150000, -4.05828000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q1883524'),
(48482, 'Bethnal Green', 2336, 'ENG', 232, 'GB', 51.52718000, -0.06109000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q124204'),
(48483, 'Betley', 2336, 'ENG', 232, 'GB', 53.03439000, -2.36865000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q4898150'),
(48484, 'Betws', 2338, 'WLS', 232, 'GB', 51.56917000, -3.58833000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48485, 'Betws-y-Coed', 2338, 'WLS', 232, 'GB', 53.09382000, -3.80668000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q203387'),
(48486, 'Beverley', 2336, 'ENG', 232, 'GB', 53.84587000, -0.42332000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q851718'),
(48487, 'Bewbush', 2336, 'ENG', 232, 'GB', 51.10329000, -0.22312000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q851718'),
(48488, 'Bewdley', 2336, 'ENG', 232, 'GB', 52.37570000, -2.31833000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q766594'),
(48489, 'Bexhill-on-Sea', 2336, 'ENG', 232, 'GB', 50.85023000, 0.47095000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q853012'),
(48490, 'Bexley', 2336, 'ENG', 232, 'GB', 51.44162000, 0.14866000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q853012'),
(48491, 'Bicester', 2336, 'ENG', 232, 'GB', 51.89998000, -1.15357000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, 'Q251903'),
(48492, 'Bicknacre', 2336, 'ENG', 232, 'GB', 51.69403000, 0.58519000, '2019-10-05 22:49:16', '2019-10-05 22:49:16', 1, '********'),
(48493, 'Bicton', 2336, 'ENG', 232, 'GB', 52.72829000, -2.81649000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, '********'),
(48494, 'Biddenden', 2336, 'ENG', 232, 'GB', 51.11489000, 0.63819000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q663588'),
(48495, 'Biddestone', 2336, 'ENG', 232, 'GB', 51.46083000, -2.19833000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2405546'),
(48496, 'Biddulph', 2336, 'ENG', 232, 'GB', 53.11724000, -2.17584000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1109566'),
(48497, 'Bideford', 2336, 'ENG', 232, 'GB', 51.01678000, -4.20832000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1234840'),
(48498, 'Bidford-on-Avon', 2336, 'ENG', 232, 'GB', 52.16964000, -1.85955000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1770280'),
(48499, 'Biggar', 2335, 'SCT', 232, 'GB', 55.62297000, -3.52455000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q3250089'),
(48500, 'Biggin Hill', 2336, 'ENG', 232, 'GB', 51.31329000, 0.03433000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q3250089'),
(48501, 'Biggleswade', 2336, 'ENG', 232, 'GB', 52.08652000, -0.26493000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2224167'),
(48502, 'Bildeston', 2336, 'ENG', 232, 'GB', 52.10658000, 0.90916000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q860798'),
(48503, 'Billericay', 2336, 'ENG', 232, 'GB', 51.62867000, 0.41963000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q862832'),
(48504, 'Billingborough', 2336, 'ENG', 232, 'GB', 52.89384000, -0.34186000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1748350'),
(48505, 'Billinge', 2336, 'ENG', 232, 'GB', 53.49795000, -2.70810000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4911915'),
(48506, 'Billingham', 2336, 'ENG', 232, 'GB', 54.58881000, -1.29034000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q862891'),
(48507, 'Billinghay', 2336, 'ENG', 232, 'GB', 53.07959000, -0.27689000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q628445'),
(48508, 'Billingshurst', 2336, 'ENG', 232, 'GB', 51.02312000, -0.45359000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2148483'),
(48509, 'Billington', 2336, 'ENG', 232, 'GB', 53.81570000, -2.42360000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4911982'),
(48510, 'Bilsdale', 2336, 'ENG', 232, 'GB', 54.37356000, -1.11923000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4911982'),
(48511, 'Bilsthorpe', 2336, 'ENG', 232, 'GB', 53.14024000, -1.03392000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4913610'),
(48512, 'Bilston', 2336, 'ENG', 232, 'GB', 52.56568000, -2.07367000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1015967'),
(48513, 'Bilston', 2335, 'SCT', 232, 'GB', 55.87030000, -3.17814000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q3421561'),
(48514, 'Bingham', 2336, 'ENG', 232, 'GB', 52.94978000, -0.95907000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q783269'),
(48515, 'Bingley', 2336, 'ENG', 232, 'GB', 53.84861000, -1.83857000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q863832'),
(48516, 'Birchington-on-Sea', 2336, 'ENG', 232, 'GB', 51.37575000, 1.30480000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q865021'),
(48517, 'Bircotes', 2336, 'ENG', 232, 'GB', 53.41933000, -1.04905000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4454217'),
(48518, 'Birdham', 2336, 'ENG', 232, 'GB', 50.79606000, -0.83067000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2193499'),
(48519, 'Birdwell', 2336, 'ENG', 232, 'GB', 53.51398000, -1.47929000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, '********'),
(48520, 'Birkenhead', 2336, 'ENG', 232, 'GB', 53.39337000, -3.01479000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q746718'),
(48521, 'Birmingham', 2336, 'ENG', 232, 'GB', 52.48142000, -1.89983000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2256'),
(48522, 'Bishop Auckland', 2336, 'ENG', 232, 'GB', 54.65554000, -1.67706000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q866339'),
(48523, 'Bishop Middleham', 2336, 'ENG', 232, 'GB', 54.67778000, -1.48826000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, '********'),
(48524, 'Bishop Sutton', 2336, 'ENG', 232, 'GB', 51.33444000, -2.59472000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, '********'),
(48525, 'Bishop\'s Castle', 2336, 'ENG', 232, 'GB', 52.49208000, -3.00210000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, '********'),
(48526, 'Bishopbriggs', 2335, 'SCT', 232, 'GB', 55.90669000, -4.21869000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q866347'),
(48527, 'Bishops Cleeve', 2336, 'ENG', 232, 'GB', 51.94749000, -2.06277000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, '********'),
(48528, 'Bishops Lydeard', 2336, 'ENG', 232, 'GB', 51.05917000, -3.18778000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, '********'),
(48529, 'Bishops Stortford', 2336, 'ENG', 232, 'GB', 51.87113000, 0.15868000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q19797'),
(48530, 'Bishops Waltham', 2336, 'ENG', 232, 'GB', 50.95595000, -1.21476000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1248930'),
(48531, 'Bishopsteignton', 2336, 'ENG', 232, 'GB', 50.55193000, -3.53852000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1217742'),
(48532, 'Bishopstoke', 2336, 'ENG', 232, 'GB', 50.96643000, -1.32832000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2482964'),
(48533, 'Bishopston', 2338, 'WLS', 232, 'GB', 51.57750000, -4.04806000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4918101'),
(48534, 'Bishopstone', 2336, 'ENG', 232, 'GB', 51.55130000, -1.64701000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4918101'),
(48535, 'Bishopsworth', 2336, 'ENG', 232, 'GB', 51.41479000, -2.62080000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4918101'),
(48536, 'Bishopthorpe', 2336, 'ENG', 232, 'GB', 53.91910000, -1.09915000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2918572'),
(48537, 'Bishopton', 2335, 'SCT', 232, 'GB', 55.90969000, -4.50560000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q2098147'),
(48538, 'Bishopton', 2336, 'ENG', 232, 'GB', 54.58333000, -1.43333000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4918118'),
(48539, 'Bitton', 2336, 'ENG', 232, 'GB', 51.42479000, -2.45965000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4919142'),
(48540, 'Blaby', 2336, 'ENG', 232, 'GB', 52.57577000, -1.16403000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1619060'),
(48541, 'Black Notley', 2336, 'ENG', 232, 'GB', 51.85301000, 0.56846000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q932687'),
(48542, 'Blackburn', 2335, 'SCT', 232, 'GB', 55.86667000, -3.63333000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1016707'),
(48543, 'Blackburn', 2336, 'ENG', 232, 'GB', 53.75000000, -2.48333000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q188313'),
(48544, 'Blackburn with Darwen', 2336, 'ENG', 232, 'GB', 53.66667000, -2.46667000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q880782'),
(48545, 'Blackheath', 2336, 'ENG', 232, 'GB', 51.46470000, 0.00790000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q880782'),
(48546, 'Blackley', 2336, 'ENG', 232, 'GB', 53.51765000, -2.21443000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4923090'),
(48547, 'Blackmoorfoot', 2336, 'ENG', 232, 'GB', 53.61423000, -1.85588000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4923090'),
(48548, 'Blackpool', 2336, 'ENG', 232, 'GB', 53.********, -3.05000000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q170377'),
(48549, 'Blackridge', 2335, 'SCT', 232, 'GB', 55.88523000, -3.77479000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q170377'),
(48550, 'Blackrod', 2336, 'ENG', 232, 'GB', 53.59229000, -2.58026000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1615879'),
(48551, 'Blackwell', 2336, 'ENG', 232, 'GB', 53.11667000, -1.33333000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q1920099'),
(48552, 'Blackwood', 2335, 'SCT', 232, 'GB', 55.66667000, -3.********, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q4923621'),
(48553, 'Blackwood', 2338, 'WLS', 232, 'GB', 51.66778000, -3.20750000, '2019-10-05 22:52:49', '2019-10-05 22:52:49', 1, 'Q880986'),
(48554, 'Blacon', 2336, 'ENG', 232, 'GB', 53.20832000, -2.92530000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4923655'),
(48555, 'Blaenau Gwent', 2338, 'WLS', 232, 'GB', 51.75000000, -3.16667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q596885'),
(48556, 'Blaenau-Ffestiniog', 2338, 'WLS', 232, 'GB', 52.99464000, -3.93697000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q881062'),
(48557, 'Blaenavon', 2338, 'WLS', 232, 'GB', 51.77402000, -3.08537000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q804282'),
(48558, 'Blaengwynfi', 2338, 'WLS', 232, 'GB', 51.65623000, -3.60371000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4923812'),
(48559, 'Blagdon', 2336, 'ENG', 232, 'GB', 51.32688000, -2.71731000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2240982'),
(48560, 'Blairgowrie', 2335, 'SCT', 232, 'GB', 56.59157000, -3.34045000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q24657181'),
(48561, 'Blandford Forum', 2336, 'ENG', 232, 'GB', 50.86073000, -2.16174000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q644530'),
(48562, 'Blantyre', 2335, 'SCT', 232, 'GB', 55.79634000, -4.09485000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q644530'),
(48563, 'Blaydon-on-Tyne', 2336, 'ENG', 232, 'GB', 54.96461000, -1.71392000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q933113'),
(48564, 'Bleadon', 2336, 'ENG', 232, 'GB', 51.30861000, -2.94750000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2458518'),
(48565, 'Blean', 2336, 'ENG', 232, 'GB', 51.30679000, 1.04301000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2741069'),
(48566, 'Bletchingley', 2336, 'ENG', 232, 'GB', 51.24059000, -0.10038000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48567, 'Bletchley', 2336, 'ENG', 232, 'GB', 51.99334000, -0.73471000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q127159'),
(48568, 'Blewbury', 2336, 'ENG', 232, 'GB', 51.56880000, -1.23261000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48569, 'Blidworth', 2336, 'ENG', 232, 'GB', 53.09849000, -1.11689000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q591927'),
(48570, 'Blindley Heath', 2336, 'ENG', 232, 'GB', 51.19344000, -0.05116000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q591927'),
(48571, 'Blisworth', 2336, 'ENG', 232, 'GB', 52.17498000, -0.94131000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48572, 'Blockley', 2336, 'ENG', 232, 'GB', 52.01220000, -1.76268000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48573, 'Bloxham', 2336, 'ENG', 232, 'GB', 52.02039000, -1.37321000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48574, 'Bloxwich', 2336, 'ENG', 232, 'GB', 52.61806000, -2.00431000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48575, 'Blunham', 2336, 'ENG', 232, 'GB', 52.14695000, -0.32178000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48576, 'Bluntisham', 2336, 'ENG', 232, 'GB', 52.35479000, 0.00854000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48577, 'Blyth', 2336, 'ENG', 232, 'GB', 55.12708000, -1.50856000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q886899'),
(48578, 'Blyton', 2336, 'ENG', 232, 'GB', 53.44384000, -0.71753000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48579, 'Boddam', 2335, 'SCT', 232, 'GB', 57.47076000, -1.78009000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48580, 'Bodedern', 2338, 'WLS', 232, 'GB', 53.29232000, -4.50303000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48581, 'Bodelwyddan', 2338, 'WLS', 232, 'GB', 53.26827000, -3.50078000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48582, 'Bodle Street', 2336, 'ENG', 232, 'GB', 50.91299000, 0.34332000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48583, 'Bodmin', 2336, 'ENG', 232, 'GB', 50.47151000, -4.72430000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q889888'),
(48584, 'Bognor Regis', 2336, 'ENG', 232, 'GB', 50.78206000, -0.67978000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q890621'),
(48585, 'Bollington', 2336, 'ENG', 232, 'GB', 53.29446000, -2.10963000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48586, 'Bolsover', 2336, 'ENG', 232, 'GB', 53.22846000, -1.29204000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48587, 'Bolton', 2336, 'ENG', 232, 'GB', 53.58333000, -2.43333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q184579'),
(48588, 'Bolton le Sands', 2336, 'ENG', 232, 'GB', 54.09632000, -2.80017000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48589, 'Bolton upon Dearne', 2336, 'ENG', 232, 'GB', 53.51667000, -1.31667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4940256'),
(48590, 'Bonhill', 2335, 'SCT', 232, 'GB', 55.97944000, -4.56380000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4093370'),
(48591, 'Bonnybridge', 2335, 'SCT', 232, 'GB', 56.00152000, -3.88860000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1014515'),
(48592, 'Bonnyrigg', 2335, 'SCT', 232, 'GB', 55.87329000, -3.10510000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4942522'),
(48593, 'Boosbeck', 2336, 'ENG', 232, 'GB', 54.54265000, -0.98139000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q303161'),
(48594, 'Bootle', 2336, 'ENG', 232, 'GB', 53.46667000, -3.********, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q893159'),
(48595, 'Bordon', 2336, 'ENG', 232, 'GB', 51.11357000, -0.86245000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1613430'),
(48596, 'Boreham', 2336, 'ENG', 232, 'GB', 51.75955000, 0.54116000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1823277'),
(48597, 'Borehamwood', 2336, 'ENG', 232, 'GB', 51.65468000, -0.27762000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q19773'),
(48598, 'Borough Green', 2336, 'ENG', 232, 'GB', 51.29158000, 0.30478000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2001391'),
(48599, 'Borough of Bolton', 2336, 'ENG', 232, 'GB', 53.58333000, -2.50000000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1364541'),
(48600, 'Borough of Bury', 2336, 'ENG', 232, 'GB', 53.58333000, -2.33333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q512036'),
(48601, 'Borough of Halton', 2336, 'ENG', 232, 'GB', 53.33333000, -2.75000000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q376141'),
(48602, 'Borough of North Tyneside', 2336, 'ENG', 232, 'GB', 55.03333000, -1.50000000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1120443'),
(48603, 'Borough of Oldham', 2336, 'ENG', 232, 'GB', 53.********, -2.08333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1278608'),
(48604, 'Borough of Rochdale', 2336, 'ENG', 232, 'GB', 53.58333000, -2.16667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1892596'),
(48605, 'Borough of Stockport', 2336, 'ENG', 232, 'GB', 53.41667000, -2.16667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1752424'),
(48606, 'Borough of Swindon', 2336, 'ENG', 232, 'GB', 51.58333000, -1.75000000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q894093'),
(48607, 'Borough of Tameside', 2336, 'ENG', 232, 'GB', 53.50000000, -2.08333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1423078'),
(48608, 'Borough of Thurrock', 2336, 'ENG', 232, 'GB', 51.50000000, 0.41667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1549155'),
(48609, 'Borough of Torbay', 2336, 'ENG', 232, 'GB', 50.45160000, -3.55785000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1136118'),
(48610, 'Borough of Wigan', 2336, 'ENG', 232, 'GB', 53.50000000, -2.58333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q684307'),
(48611, 'Boroughbridge', 2336, 'ENG', 232, 'GB', 54.08950000, -1.40110000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2569056'),
(48612, 'Borrowash', 2336, 'ENG', 232, 'GB', 52.90673000, -1.38411000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4946610'),
(48613, 'Borth', 2338, 'WLS', 232, 'GB', 52.48887000, -4.05039000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q894252'),
(48614, 'Bosham', 2336, 'ENG', 232, 'GB', 50.83088000, -0.85384000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2143046'),
(48615, 'Boston', 2336, 'ENG', 232, 'GB', 52.97633000, -0.02664000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q311975'),
(48616, 'Boston Spa', 2336, 'ENG', 232, 'GB', 53.90419000, -1.34523000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2223359'),
(48617, 'Botesdale', 2336, 'ENG', 232, 'GB', 52.34216000, 1.00405000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2315208'),
(48618, 'Bothwell', 2335, 'SCT', 232, 'GB', 55.80272000, -4.06835000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1773759'),
(48619, 'Botley', 2336, 'ENG', 232, 'GB', 50.91433000, -1.26984000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1241154'),
(48620, 'Bottesford', 2336, 'ENG', 232, 'GB', 52.94131000, -0.80060000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2603259'),
(48621, 'Bottisham', 2336, 'ENG', 232, 'GB', 52.22280000, 0.25878000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4949032'),
(48622, 'Boughton', 2336, 'ENG', 232, 'GB', 53.20000000, -0.98333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4949032'),
(48623, 'Bourne', 2336, 'ENG', 232, 'GB', 52.76667000, -0.38333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2223229'),
(48624, 'Bourne End', 2336, 'ENG', 232, 'GB', 51.57622000, -0.71291000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2059003'),
(48625, 'Bournemouth', 2336, 'ENG', 232, 'GB', 50.71918000, -1.87806000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q20989094'),
(48626, 'Bourton', 2336, 'ENG', 232, 'GB', 51.07444000, -2.32778000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1771536'),
(48627, 'Bourton on the Water', 2336, 'ENG', 232, 'GB', 51.88584000, -1.75492000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q895264'),
(48628, 'Bovey Tracey', 2336, 'ENG', 232, 'GB', 50.59259000, -3.67543000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q895361'),
(48629, 'Bovingdon', 2336, 'ENG', 232, 'GB', 51.72312000, -0.53670000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q19926'),
(48630, 'Bovington Camp', 2336, 'ENG', 232, 'GB', 50.69782000, -2.23506000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4252369'),
(48631, 'Bow', 2336, 'ENG', 232, 'GB', 50.80000000, -3.********, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1980288'),
(48632, 'Bow Brickhill', 2336, 'ENG', 232, 'GB', 52.00280000, -0.68064000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1770285'),
(48633, 'Bow Street', 2338, 'WLS', 232, 'GB', 52.44213000, -4.02783000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q3401604'),
(48634, 'Bowburn', 2336, 'ENG', 232, 'GB', 54.73850000, -1.52521000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q3401604'),
(48635, 'Bowdon', 2336, 'ENG', 232, 'GB', 53.37644000, -2.36532000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4950774'),
(48636, 'Bowthorpe', 2336, 'ENG', 232, 'GB', 52.63884000, 1.21885000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4951449'),
(48637, 'Box', 2336, 'ENG', 232, 'GB', 51.41472000, -2.24556000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q895512'),
(48638, 'Boxgrove', 2336, 'ENG', 232, 'GB', 50.85884000, -0.71360000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q549078'),
(48639, 'Boxted', 2336, 'ENG', 232, 'GB', 51.94878000, 0.91002000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2064435'),
(48640, 'Bozeat', 2336, 'ENG', 232, 'GB', 52.22270000, -0.67326000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q682861'),
(48641, 'Bo’ness', 2335, 'SCT', 232, 'GB', 56.********, -3.61667000, '2019-10-05 22:52:50', '2020-05-01 17:23:21', 1, 'Q896424'),
(48642, 'Bracebridge Heath', 2336, 'ENG', 232, 'GB', 53.19647000, -0.53421000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4953229'),
(48643, 'Brackley', 2336, 'ENG', 232, 'GB', 52.03333000, -1.15000000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q671557'),
(48644, 'Bracknell', 2336, 'ENG', 232, 'GB', 51.41363000, -0.75054000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q783210'),
(48645, 'Bracknell Forest', 2336, 'ENG', 232, 'GB', 51.41667000, -0.75000000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q896725'),
(48646, 'Bradfield', 2336, 'ENG', 232, 'GB', 51.44914000, -1.13082000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48647, 'Bradford', 2336, 'ENG', 232, 'GB', 53.********, -1.********, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48648, 'Bradford-on-Avon', 2336, 'ENG', 232, 'GB', 51.34772000, -2.25065000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q896940'),
(48649, 'Brading', 2336, 'ENG', 232, 'GB', 50.67990000, -1.14571000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q738184'),
(48650, 'Bradley Cross', 2336, 'ENG', 232, 'GB', 51.27488000, -2.76256000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q738184'),
(48651, 'Bradninch', 2336, 'ENG', 232, 'GB', 50.82491000, -3.42465000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q928614'),
(48652, 'Bradwell', 2336, 'ENG', 232, 'GB', 52.57353000, 1.69979000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q928614'),
(48653, 'Braintree', 2336, 'ENG', 232, 'GB', 51.87819000, 0.55292000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q676067'),
(48654, 'Bramford', 2336, 'ENG', 232, 'GB', 52.07631000, 1.09687000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48655, 'Bramhall', 2336, 'ENG', 232, 'GB', 53.35801000, -2.16539000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48656, 'Bramham', 2336, 'ENG', 232, 'GB', 53.88118000, -1.35452000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48657, 'Bramhope', 2336, 'ENG', 232, 'GB', 53.88489000, -1.61641000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4956178'),
(48658, 'Bramley', 2336, 'ENG', 232, 'GB', 51.32677000, -1.05938000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2618034'),
(48659, 'Brampton', 2336, 'ENG', 232, 'GB', 52.32039000, -0.22007000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2148082'),
(48660, 'Brandesburton', 2336, 'ENG', 232, 'GB', 53.91110000, -0.30122000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q3393011'),
(48661, 'Brandon', 2336, 'ENG', 232, 'GB', 54.75000000, -1.61667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q3393011'),
(48662, 'Bransgore', 2336, 'ENG', 232, 'GB', 50.78153000, -1.73771000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4880945'),
(48663, 'Branston', 2336, 'ENG', 232, 'GB', 53.19544000, -0.47482000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q3545325'),
(48664, 'Bratton', 2336, 'ENG', 232, 'GB', 51.27056000, -2.12444000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1757559'),
(48665, 'Braunston', 2336, 'ENG', 232, 'GB', 52.28979000, -1.20266000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2361652'),
(48666, 'Braunton', 2336, 'ENG', 232, 'GB', 51.10847000, -4.16131000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1226021'),
(48667, 'Brayton', 2336, 'ENG', 232, 'GB', 53.76510000, -1.08921000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q3296763'),
(48668, 'Bream', 2336, 'ENG', 232, 'GB', 51.74822000, -2.57747000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4959718'),
(48669, 'Brechin', 2335, 'SCT', 232, 'GB', 56.72993000, -2.65729000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q45848'),
(48670, 'Brecon', 2338, 'WLS', 232, 'GB', 51.94612000, -3.38887000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q904472'),
(48671, 'Bredbury', 2336, 'ENG', 232, 'GB', 53.41667000, -2.11667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q3303731'),
(48672, 'Bredon', 2336, 'ENG', 232, 'GB', 52.03008000, -2.11671000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1877352'),
(48673, 'Brenchley', 2336, 'ENG', 232, 'GB', 51.15141000, 0.39825000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2197855'),
(48674, 'Brent', 2336, 'ENG', 232, 'GB', 51.55306000, -0.30230000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2197855'),
(48675, 'Brent Knoll', 2336, 'ENG', 232, 'GB', 51.25219000, -2.95744000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2197855'),
(48676, 'Brentford', 2336, 'ENG', 232, 'GB', 51.48619000, -0.30830000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1013528'),
(48677, 'Brentwood', 2336, 'ENG', 232, 'GB', 51.62127000, 0.30556000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q909993'),
(48678, 'Brewood', 2336, 'ENG', 232, 'GB', 52.67712000, -2.17414000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2411070'),
(48679, 'Bridge', 2336, 'ENG', 232, 'GB', 51.24513000, 1.12640000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2302642'),
(48680, 'Bridge of Allan', 2335, 'SCT', 232, 'GB', 56.15402000, -3.94631000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q187892'),
(48681, 'Bridge of Earn', 2335, 'SCT', 232, 'GB', 56.34842000, -3.40650000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1016971'),
(48682, 'Bridge of Weir', 2335, 'SCT', 232, 'GB', 55.85582000, -4.57894000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1009612'),
(48683, 'Bridgend', 2338, 'WLS', 232, 'GB', 51.50583000, -3.57722000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48684, 'Bridgend county borough', 2338, 'WLS', 232, 'GB', 51.********, -3.58333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q697126'),
(48685, 'Bridgnorth', 2336, 'ENG', 232, 'GB', 52.53661000, -2.42033000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q914000'),
(48686, 'Bridgwater', 2336, 'ENG', 232, 'GB', 51.12837000, -3.00356000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q914015'),
(48687, 'Bridlington', 2336, 'ENG', 232, 'GB', 54.08306000, -0.19192000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q527391'),
(48688, 'Bridport', 2336, 'ENG', 232, 'GB', 50.73380000, -2.75831000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q914040'),
(48689, 'Brierfield', 2336, 'ENG', 232, 'GB', 53.82468000, -2.23415000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q523025'),
(48690, 'Brierley Hill', 2336, 'ENG', 232, 'GB', 52.48173000, -2.12139000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48691, 'Brigg', 2336, 'ENG', 232, 'GB', 53.55201000, -0.49214000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48692, 'Brighouse', 2336, 'ENG', 232, 'GB', 53.70322000, -1.78428000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q183296'),
(48693, 'Brighstone', 2336, 'ENG', 232, 'GB', 50.64263000, -1.39479000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, '********'),
(48694, 'Brightlingsea', 2336, 'ENG', 232, 'GB', 51.81164000, 1.02336000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q915751'),
(48695, 'Brighton', 2336, 'ENG', 232, 'GB', 50.82838000, -0.13947000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q131491'),
(48696, 'Brighton and Hove', 2336, 'ENG', 232, 'GB', 50.********, -0.13333000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1022488'),
(48697, 'Brightons', 2335, 'SCT', 232, 'GB', 55.98028000, -3.71613000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1022488'),
(48698, 'Brigstock', 2336, 'ENG', 232, 'GB', 52.45775000, -0.60834000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2407311'),
(48699, 'Brill', 2336, 'ENG', 232, 'GB', 51.********, -1.05000000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2394847'),
(48700, 'Brimscombe', 2336, 'ENG', 232, 'GB', 51.71973000, -2.18553000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2394847'),
(48701, 'Brinklow', 2336, 'ENG', 232, 'GB', 52.41091000, -1.36400000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2925607'),
(48702, 'Brinscall', 2336, 'ENG', 232, 'GB', 53.68900000, -2.57208000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q4968459'),
(48703, 'Bristol', 2336, 'ENG', 232, 'GB', 51.45523000, -2.59665000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q23154'),
(48704, 'Briston', 2336, 'ENG', 232, 'GB', 52.85369000, 1.05899000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q391950'),
(48705, 'Briton Ferry', 2338, 'WLS', 232, 'GB', 51.63106000, -3.81898000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1858117'),
(48706, 'Brixham', 2336, 'ENG', 232, 'GB', 50.39431000, -3.51585000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q921326'),
(48707, 'Brixton', 2336, 'ENG', 232, 'GB', 51.46593000, -0.10652000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q921326'),
(48708, 'Brixton Hill', 2336, 'ENG', 232, 'GB', 51.45213000, -0.12300000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q921326'),
(48709, 'Brixworth', 2336, 'ENG', 232, 'GB', 52.32912000, -0.90350000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q921472'),
(48710, 'Broad Blunsdon', 2336, 'ENG', 232, 'GB', 51.61339000, -1.77870000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q24654406'),
(48711, 'Broadfield', 2336, 'ENG', 232, 'GB', 51.09714000, -0.20664000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q24654406'),
(48712, 'Broadstairs', 2336, 'ENG', 232, 'GB', 51.35908000, 1.43938000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q922739'),
(48713, 'Broadstone', 2336, 'ENG', 232, 'GB', 50.75717000, -1.99406000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2925857'),
(48714, 'Broadwater', 2336, 'ENG', 232, 'GB', 50.82887000, -0.37594000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q9179457'),
(48715, 'Broadway', 2336, 'ENG', 232, 'GB', 52.03825000, -1.86079000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q180356'),
(48716, 'Brockenhurst', 2336, 'ENG', 232, 'GB', 50.81936000, -1.57303000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q1612283'),
(48717, 'Brockley', 2336, 'ENG', 232, 'GB', 51.40000000, -2.76667000, '2019-10-05 22:52:50', '2019-10-05 22:52:50', 1, 'Q2485076'),
(48718, 'Bromborough', 2336, 'ENG', 232, 'GB', 53.34850000, -2.97935000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2338272'),
(48719, 'Bromham', 2336, 'ENG', 232, 'GB', 52.14508000, -0.52906000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1519942'),
(48720, 'Brompton', 2336, 'ENG', 232, 'GB', 54.36015000, -1.42422000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2300765'),
(48721, 'Bromsgrove', 2336, 'ENG', 232, 'GB', 52.33574000, -2.05983000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1190876'),
(48722, 'Bromyard', 2336, 'ENG', 232, 'GB', 52.19019000, -2.50875000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2277559'),
(48723, 'Brooke', 2336, 'ENG', 232, 'GB', 52.54175000, 1.37076000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2350389'),
(48724, 'Brora', 2335, 'SCT', 232, 'GB', 58.00989000, -3.85182000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q990677'),
(48725, 'Broseley', 2336, 'ENG', 232, 'GB', 52.61321000, -2.48269000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q267054'),
(48726, 'Brotton', 2336, 'ENG', 232, 'GB', 54.56661000, -0.93929000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1622799'),
(48727, 'Brough', 2336, 'ENG', 232, 'GB', 53.72861000, -0.57215000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q990921'),
(48728, 'Broughshane', 2337, 'NIR', 232, 'GB', 54.89260000, -6.20899000, '2019-10-05 22:52:51', '2021-01-23 14:35:23', 1, 'Q990932'),
(48729, 'Broughton', 2336, 'ENG', 232, 'GB', 53.56667000, -0.********, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1612226'),
(48730, 'Broughton', 2338, 'WLS', 232, 'GB', 53.16303000, -2.99309000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q990946'),
(48731, 'Broughton Astley', 2336, 'ENG', 232, 'GB', 52.52787000, -1.21768000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q3645408'),
(48732, 'Brownhills', 2336, 'ENG', 232, 'GB', 52.63333000, -1.93333000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q730103'),
(48733, 'Broxbourne', 2336, 'ENG', 232, 'GB', 51.74712000, -0.01923000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q19791'),
(48734, 'Broxburn', 2335, 'SCT', 232, 'GB', 55.93415000, -3.47133000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q991350'),
(48735, 'Brundall', 2336, 'ENG', 232, 'GB', 52.62426000, 1.43509000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2926580'),
(48736, 'Bruton', 2336, 'ENG', 232, 'GB', 51.11250000, -2.45278000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q741415'),
(48737, 'Brymbo', 2338, 'WLS', 232, 'GB', 53.06667000, -3.06667000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q4980803'),
(48738, 'Bryn', 2338, 'WLS', 232, 'GB', 51.61639000, -3.71167000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q4980820'),
(48739, 'Brynamman', 2338, 'WLS', 232, 'GB', 51.80000000, -3.86667000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2733944'),
(48740, 'Brynmawr', 2338, 'WLS', 232, 'GB', 51.80000000, -3.18333000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1980175'),
(48741, 'Brynna', 2338, 'WLS', 232, 'GB', 51.53845000, -3.46378000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q4981027'),
(48742, 'Bubwith', 2336, 'ENG', 232, 'GB', 53.81905000, -0.91968000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2170659'),
(48743, 'Buckden', 2336, 'ENG', 232, 'GB', 52.29415000, -0.24912000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1951638'),
(48744, 'Buckfastleigh', 2336, 'ENG', 232, 'GB', 50.48132000, -3.77913000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q999649'),
(48745, 'Buckhaven', 2335, 'SCT', 232, 'GB', 56.17149000, -3.03377000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1016877'),
(48746, 'Buckhurst Hill', 2336, 'ENG', 232, 'GB', 51.62409000, 0.03262000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2684024'),
(48747, 'Buckie', 2335, 'SCT', 232, 'GB', 57.67570000, -2.96238000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q999708'),
(48748, 'Buckingham', 2336, 'ENG', 232, 'GB', 51.99968000, -0.98779000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q195955'),
(48749, 'Buckinghamshire', 2336, 'ENG', 232, 'GB', 51.75000000, -0.75000000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '*********'),
(48750, 'Buckley', 2338, 'WLS', 232, 'GB', 53.16667000, -3.08333000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48751, 'Bucknell', 2336, 'ENG', 232, 'GB', 52.35997000, -2.95066000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48752, 'Bude', 2336, 'ENG', 232, 'GB', 50.82435000, -4.54130000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q984974'),
(48753, 'Budleigh Salterton', 2336, 'ENG', 232, 'GB', 50.62983000, -3.32181000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48754, 'Bugbrooke', 2336, 'ENG', 232, 'GB', 52.21006000, -1.01304000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48755, 'Bugle', 2336, 'ENG', 232, 'GB', 50.39577000, -4.79334000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48756, 'Builth Wells', 2338, 'WLS', 232, 'GB', 52.14940000, -3.40469000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48757, 'Bulford', 2336, 'ENG', 232, 'GB', 51.18930000, -1.76009000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48758, 'Bulkington', 2336, 'ENG', 232, 'GB', 51.32361000, -2.08361000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48759, 'Bulphan', 2336, 'ENG', 232, 'GB', 51.54612000, 0.36066000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, '********'),
(48760, 'Bunbury', 2336, 'ENG', 232, 'GB', 53.11559000, -2.65151000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1853578'),
(48761, 'Bungay', 2336, 'ENG', 232, 'GB', 52.45434000, 1.43818000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1009468'),
(48762, 'Buntingford', 2336, 'ENG', 232, 'GB', 51.94612000, -0.01841000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1938551'),
(48763, 'Burbage', 2336, 'ENG', 232, 'GB', 51.35184000, -1.67087000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q693399'),
(48764, 'Bures Saint Mary', 2336, 'ENG', 232, 'GB', 51.97240000, 0.77488000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2315726'),
(48765, 'Burford', 2336, 'ENG', 232, 'GB', 51.80915000, -1.63628000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2019274'),
(48766, 'Burgess Hill', 2336, 'ENG', 232, 'GB', 50.95843000, -0.13287000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1014934'),
(48767, 'Burgh le Marsh', 2336, 'ENG', 232, 'GB', 53.16158000, 0.24484000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1612150'),
(48768, 'Burghead', 2335, 'SCT', 232, 'GB', 57.70113000, -3.48992000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1013606'),
(48769, 'Burham', 2336, 'ENG', 232, 'GB', 51.33243000, 0.47833000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2294985'),
(48770, 'Burley', 2336, 'ENG', 232, 'GB', 50.82800000, -1.69977000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q3195509'),
(48771, 'Burley in Wharfedale', 2336, 'ENG', 232, 'GB', 53.91020000, -1.75798000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2390342'),
(48772, 'Burnage', 2336, 'ENG', 232, 'GB', 53.43265000, -2.19967000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2736509'),
(48773, 'Burneside', 2336, 'ENG', 232, 'GB', 54.35271000, -2.76151000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q4999653'),
(48774, 'Burngreave', 2336, 'ENG', 232, 'GB', 53.39302000, -1.45789000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q4999653'),
(48775, 'Burnham-on-Crouch', 2336, 'ENG', 232, 'GB', 51.63272000, 0.81488000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2454022'),
(48776, 'Burnham-on-Sea', 2336, 'ENG', 232, 'GB', 51.23862000, -2.99780000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1247822'),
(48777, 'Burniston', 2336, 'ENG', 232, 'GB', 54.32385000, -0.44813000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2597304'),
(48778, 'Burnley', 2336, 'ENG', 232, 'GB', 53.80000000, -2.23333000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q209096'),
(48779, 'Burnopfield', 2336, 'ENG', 232, 'GB', 54.90624000, -1.72486000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q5000037'),
(48780, 'Burntisland', 2335, 'SCT', 232, 'GB', 56.06248000, -3.23176000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1011521'),
(48781, 'Burntwood', 2336, 'ENG', 232, 'GB', 52.68075000, -1.92759000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q649909'),
(48782, 'Burringham', 2336, 'ENG', 232, 'GB', 53.57402000, -0.73957000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2126974'),
(48783, 'Burrington', 2336, 'ENG', 232, 'GB', 51.32884000, -2.74868000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2368875'),
(48784, 'Burry Port', 2338, 'WLS', 232, 'GB', 51.68435000, -4.24687000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2253279'),
(48785, 'Burscough', 2336, 'ENG', 232, 'GB', 53.59640000, -2.83972000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2390371'),
(48786, 'Bursledon', 2336, 'ENG', 232, 'GB', 50.88658000, -1.31596000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1986252'),
(48787, 'Burstwick', 2336, 'ENG', 232, 'GB', 53.73211000, -0.13956000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2689920'),
(48788, 'Burton', 2336, 'ENG', 232, 'GB', 53.26667000, -0.56667000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q5000775'),
(48789, 'Burton Joyce', 2336, 'ENG', 232, 'GB', 52.98825000, -1.03407000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1869023'),
(48790, 'Burton Latimer', 2336, 'ENG', 232, 'GB', 52.36368000, -0.67853000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1883276'),
(48791, 'Burton Pidsea', 2336, 'ENG', 232, 'GB', 53.76327000, -0.10703000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q2324361'),
(48792, 'Burton on the Wolds', 2336, 'ENG', 232, 'GB', 52.78574000, -1.12988000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q737656'),
(48793, 'Burton upon Stather', 2336, 'ENG', 232, 'GB', 53.64911000, -0.68453000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q746031'),
(48794, 'Burton upon Trent', 2336, 'ENG', 232, 'GB', 52.80728000, -1.64263000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1000597'),
(48795, 'Burtonwood', 2336, 'ENG', 232, 'GB', 53.42948000, -2.65852000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q995213'),
(48796, 'Burwash', 2336, 'ENG', 232, 'GB', 50.99755000, 0.38504000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q5001038'),
(48797, 'Burwell', 2336, 'ENG', 232, 'GB', 52.27632000, 0.32732000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q1982669'),
(48798, 'Bury', 2336, 'ENG', 232, 'GB', 53.********, -2.30000000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q47822'),
(48799, 'Bury St Edmunds', 2336, 'ENG', 232, 'GB', 52.24630000, 0.71111000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q764754'),
(48800, 'Busby', 2335, 'SCT', 232, 'GB', 55.77995000, -4.27711000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q5001296'),
(48801, 'Bushey', 2336, 'ENG', 232, 'GB', 51.64316000, -0.36053000, '2019-10-05 22:52:51', '2019-10-05 22:52:51', 1, 'Q10996'),
(48802, 'Bushmills', 2337, 'NIR', 232, 'GB', 55.20493000, -6.********, '2019-10-05 22:52:51', '2021-01-23 14:35:23', 1, 'Q611570');

