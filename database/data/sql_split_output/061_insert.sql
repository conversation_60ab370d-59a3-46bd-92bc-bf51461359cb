INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(30335, 'Wintersdorf', 3015, 'TH', 82, 'DE', 51.05320000, 12.35445000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q675740'),
(30336, '<PERSON><PERSON>', 3009, 'BY', 82, 'DE', 48.72285000, 13.07751000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q33517632'),
(30337, 'Wipfeld', 3009, 'BY', 82, 'DE', 49.91667000, 10.16667000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q547905'),
(30338, 'Wipperdorf', 3015, 'TH', 82, '<PERSON>', 51.45560000, 10.64388000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q547905'),
(30339, 'Wipperfürth', 3017, 'NW', 82, 'DE', 51.11610000, 7.39865000, '2019-10-05 22:45:07', '2020-05-01 17:22:49', 1, 'Q11045'),
(30340, 'Wippra', 3011, 'ST', 82, 'DE', 51.57300000, 11.27498000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q701343'),
(30341, 'Wirdum', 3008, 'NI', 82, 'DE', 53.47667000, 7.20594000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q701343'),
(30342, 'Wirges', 3019, 'RP', 82, 'DE', 50.47195000, 7.79844000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q542642'),
(30343, 'Wirsberg', 3009, 'BY', 82, 'DE', 50.10572000, 11.60515000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q504068'),
(30344, 'Wischhafen', 3008, 'NI', 82, 'DE', 53.78333000, 9.31667000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q502154'),
(30345, 'Wismar', 3007, 'MV', 82, 'DE', 53.89314000, 11.45286000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q7030'),
(30346, 'Wissen', 3019, 'RP', 82, 'DE', 50.77915000, 7.73466000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q7030'),
(30347, 'Wistedt', 3008, 'NI', 82, 'DE', 53.26667000, 9.68333000, '2019-10-05 22:45:08', '2019-10-05 22:45:08', 1, 'Q668547'),
(30348, 'Wittelshofen', 3009, 'BY', 82, 'DE', 49.06121000, 10.48121000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q122073'),
(30349, 'Witten', 3017, 'NW', 82, 'DE', 51.44362000, 7.35258000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q3764'),
(30350, 'Wittenau', 3010, 'BE', 82, 'DE', 52.59319000, 13.32127000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q564913'),
(30351, 'Wittenberg', 3011, 'ST', 82, 'DE', 51.86610000, 12.64973000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q6837'),
(30352, 'Wittenberge', 3013, 'BB', 82, 'DE', 53.00543000, 11.75032000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q541956'),
(30353, 'Wittenburg', 3007, 'MV', 82, 'DE', 53.50599000, 11.08049000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q506651'),
(30354, 'Wittenförden', 3007, 'MV', 82, 'DE', 53.62917000, 11.32982000, '2019-10-05 22:45:09', '2020-05-01 17:22:49', 1, 'Q629988'),
(30355, 'Wittenhagen', 3007, 'MV', 82, 'DE', 54.18379000, 13.07133000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q629988'),
(30356, 'Witterda', 3015, 'TH', 82, 'DE', 51.03615000, 10.89028000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q546602'),
(30357, 'Wittgensdorf', 3021, 'SN', 82, 'DE', 50.88316000, 12.87031000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q1069559'),
(30358, 'Wittichenau', 3021, 'SN', 82, 'DE', 51.38496000, 14.24403000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q81760'),
(30359, 'Wittingen', 3008, 'NI', 82, 'DE', 52.72694000, 10.73613000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q552390'),
(30360, 'Wittislingen', 3009, 'BY', 82, 'DE', 48.61917000, 10.41722000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q517577'),
(30361, 'Wittlich', 3019, 'RP', 82, 'DE', 49.98596000, 6.89308000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q559514'),
(30362, 'Wittmar', 3008, 'NI', 82, 'DE', 52.12969000, 10.64000000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q686758'),
(30363, 'Wittmund', 3008, 'NI', 82, 'DE', 53.57674000, 7.77839000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q509353'),
(30364, 'Wittnau', 3006, 'BW', 82, 'DE', 47.95000000, 7.81667000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q509353'),
(30365, 'Wittorf', 3008, 'NI', 82, 'DE', 53.33333000, 10.38333000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q502837'),
(30366, 'Wittstock', 3013, 'BB', 82, 'DE', 53.16118000, 12.48287000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q159572'),
(30367, 'Witzenhausen', 3018, 'HE', 82, 'DE', 51.34103000, 9.85540000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q564827'),
(30368, 'Witzhave', 3005, 'SH', 82, 'DE', 53.56667000, 10.33333000, '2019-10-05 22:45:09', '2019-10-05 22:45:09', 1, 'Q564827'),
(30369, 'Witzmannsberg', 3009, 'BY', 82, 'DE', 48.62224000, 13.49444000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q564827'),
(30370, 'Wohldorf-Ohlstedt', 3016, 'HH', 82, 'DE', 53.69282000, 10.13117000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q1837'),
(30371, 'Wohltorf', 3005, 'SH', 82, 'DE', 53.51667000, 10.28333000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q555867'),
(30372, 'Wohnbach', 3018, 'HE', 82, 'DE', 50.42864000, 8.82923000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q555867'),
(30373, 'Woldegk', 3007, 'MV', 82, 'DE', 53.46058000, 13.58356000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q50945'),
(30374, 'Wolfach', 3006, 'BW', 82, 'DE', 48.29319000, 8.21580000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q506780'),
(30375, 'Wolfegg', 3006, 'BW', 82, 'DE', 47.82030000, 9.79491000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q533735'),
(30376, 'Wolfen', 3011, 'ST', 82, 'DE', 51.66122000, 12.26873000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q685939'),
(30377, 'Wolfenbüttel', 3008, 'NI', 82, 'DE', 52.16442000, 10.54095000, '2019-10-05 22:45:10', '2020-05-01 17:22:48', 1, 'Q4122'),
(30378, 'Wolferode', 3011, 'ST', 82, 'DE', 51.50650000, 11.51299000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q4122'),
(30379, 'Wolfersdorf', 3009, 'BY', 82, 'DE', 48.47937000, 11.70949000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q627586'),
(30380, 'Wolferstadt', 3009, 'BY', 82, 'DE', 48.90352000, 10.78129000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q503367'),
(30381, 'Wolfertschwenden', 3009, 'BY', 82, 'DE', 47.89350000, 10.26715000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q547916'),
(30382, 'Wolfhagen', 3018, 'HE', 82, 'DE', 51.32611000, 9.17015000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q539592'),
(30383, 'Wolframs-Eschenbach', 3009, 'BY', 82, 'DE', 49.22675000, 10.72769000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q502591'),
(30384, 'Wolfratshausen', 3009, 'BY', 82, 'DE', 47.91289000, 11.42166000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q503160'),
(30385, 'Wolfsburg', 3008, 'NI', 82, 'DE', 52.42452000, 10.78150000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q3014'),
(30386, 'Wolfschlugen', 3006, 'BW', 82, 'DE', 48.65000000, 9.28333000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q80515'),
(30387, 'Wolfsegg', 3009, 'BY', 82, 'DE', 49.10731000, 11.97810000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q80515'),
(30388, 'Wolfstein', 3019, 'RP', 82, 'DE', 49.58411000, 7.60496000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q529194'),
(30389, 'Wolgast', 3007, 'MV', 82, 'DE', 54.05275000, 13.77201000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q492543'),
(30390, 'Wolken', 3019, 'RP', 82, 'DE', 50.33333000, 7.46667000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q492543'),
(30391, 'Wolkenstein', 3021, 'SN', 82, 'DE', 50.65552000, 13.07132000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q492543'),
(30392, 'Wolkramshausen', 3015, 'TH', 82, 'DE', 51.42185000, 10.73815000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q559982'),
(30393, 'Wollbach', 3009, 'BY', 82, 'DE', 50.36667000, 10.23333000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q559982'),
(30394, 'Wolmirsleben', 3011, 'ST', 82, 'DE', 51.95000000, 11.48333000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q554743'),
(30395, 'Wolmirstedt', 3011, 'ST', 82, 'DE', 52.24856000, 11.62945000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q653962'),
(30396, 'Wolnzach', 3009, 'BY', 82, 'DE', 48.60380000, 11.62568000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q491958'),
(30397, 'Wolpertshausen', 3006, 'BW', 82, 'DE', 49.16750000, 9.84472000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q81088'),
(30398, 'Wolpertswende', 3006, 'BW', 82, 'DE', 47.89471000, 9.61202000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q473710'),
(30399, 'Wolsdorf', 3008, 'NI', 82, 'DE', 52.19098000, 10.93878000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q473710'),
(30400, 'Woltersdorf', 3013, 'BB', 82, 'DE', 52.45554000, 13.74986000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q473710'),
(30401, 'Woltersdorf', 3008, 'NI', 82, 'DE', 52.95000000, 11.21667000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q688809'),
(30402, 'Wonfurt', 3009, 'BY', 82, 'DE', 50.01667000, 10.46667000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q504695'),
(30403, 'Wonsees', 3009, 'BY', 82, 'DE', 49.97614000, 11.30047000, '2019-10-05 22:45:10', '2019-10-05 22:45:10', 1, 'Q503602'),
(30404, 'Worbis', 3015, 'TH', 82, 'DE', 51.41997000, 10.36330000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q2593375'),
(30405, 'Woringen', 3009, 'BY', 82, 'DE', 47.91667000, 10.20000000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q548612'),
(30406, 'Worms', 3019, 'RP', 82, 'DE', 49.63278000, 8.35916000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q548612'),
(30407, 'Worpswede', 3008, 'NI', 82, 'DE', 53.21667000, 8.93333000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q269174'),
(30408, 'Wremen', 3008, 'NI', 82, 'DE', 53.65000000, 8.51667000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q639403'),
(30409, 'Wrestedt', 3008, 'NI', 82, 'DE', 52.90435000, 10.57494000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q700672'),
(30410, 'Wriedel', 3008, 'NI', 82, 'DE', 53.03130000, 10.29848000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q696316'),
(30411, 'Wriezen', 3013, 'BB', 82, 'DE', 52.72091000, 14.13425000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q588598'),
(30412, 'Wrist', 3005, 'SH', 82, 'DE', 53.93333000, 9.76667000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q588598'),
(30413, 'Wulfen', 3011, 'ST', 82, 'DE', 51.81938000, 11.93063000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q688580'),
(30414, 'Wulfsen', 3008, 'NI', 82, 'DE', 53.30000000, 10.15000000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q672050'),
(30415, 'Wulften', 3008, 'NI', 82, 'DE', 51.65999000, 10.17437000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q672050'),
(30416, 'Wulkenzin', 3007, 'MV', 82, 'DE', 53.54445000, 13.16921000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q672050'),
(30417, 'Wulsbüttel', 3008, 'NI', 82, 'DE', 53.31667000, 8.66667000, '2019-10-05 22:45:11', '2020-05-01 17:22:48', 1, 'Q505582'),
(30418, 'Wunsiedel', 3009, 'BY', 82, 'DE', 50.03923000, 12.00342000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q495778'),
(30419, 'Wunstorf', 3008, 'NI', 82, 'DE', 52.42377000, 9.43585000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q14827'),
(30420, 'Wuppertal', 3017, 'NW', 82, 'DE', 51.25627000, 7.14816000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q2107'),
(30421, 'Wurmannsquick', 3009, 'BY', 82, 'DE', 48.35334000, 12.78603000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q583211'),
(30422, 'Wurmberg', 3006, 'BW', 82, 'DE', 48.86667000, 8.81667000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q83079'),
(30423, 'Wurmlingen', 3006, 'BW', 82, 'DE', 48.00000000, 8.78333000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q83079'),
(30424, 'Wurmsham', 3009, 'BY', 82, 'DE', 48.35000000, 12.33333000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q511582'),
(30425, 'Wurzbach', 3015, 'TH', 82, 'DE', 50.46357000, 11.53779000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q530139'),
(30426, 'Wurzen', 3021, 'SN', 82, 'DE', 51.37070000, 12.73939000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q10784'),
(30427, 'Wusterhausen', 3013, 'BB', 82, 'DE', 52.89120000, 12.46021000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q623516'),
(30428, 'Wusterhusen', 3007, 'MV', 82, 'DE', 54.11312000, 13.61608000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q640081'),
(30429, 'Wustermark', 3013, 'BB', 82, 'DE', 52.55000000, 12.95000000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q581938'),
(30430, 'Wusterwitz', 3013, 'BB', 82, 'DE', 52.36666000, 12.38488000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q625379'),
(30431, 'Wustrow', 3007, 'MV', 82, 'DE', 53.47765000, 13.15569000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q625379'),
(30432, 'Wustrow', 3008, 'NI', 82, 'DE', 52.92370000, 11.12846000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q572549'),
(30433, 'Wutöschingen', 3006, 'BW', 82, 'DE', 47.66019000, 8.36755000, '2019-10-05 22:45:11', '2020-05-01 17:22:47', 1, 'Q529068'),
(30434, 'Wyhl', 3006, 'BW', 82, 'DE', 48.16617000, 7.64917000, '2019-10-05 22:45:11', '2019-10-05 22:45:11', 1, 'Q518944'),
(30435, 'Wyk auf Föhr', 3005, 'SH', 82, 'DE', 54.69140000, 8.56702000, '2019-10-05 22:45:11', '2020-05-01 17:22:50', 1, 'Q20771'),
(30436, 'Wächtersbach', 3018, 'HE', 82, 'DE', 50.25511000, 9.29564000, '2019-10-05 22:45:11', '2020-05-01 17:22:48', 1, 'Q20771'),
(30437, 'Wäschenbeuren', 3006, 'BW', 82, 'DE', 48.75991000, 9.68735000, '2019-10-05 22:45:11', '2020-05-01 17:22:47', 1, 'Q83033'),
(30438, 'Wöhrden', 3005, 'SH', 82, 'DE', 54.16667000, 9.00000000, '2019-10-05 22:45:11', '2020-05-01 17:22:50', 1, 'Q549022'),
(30439, 'Wölfersheim', 3018, 'HE', 82, 'DE', 50.40000000, 8.81667000, '2019-10-05 22:45:11', '2020-05-01 17:22:48', 1, 'Q634098'),
(30440, 'Wölfis', 3015, 'TH', 82, 'DE', 50.80825000, 10.77905000, '2019-10-05 22:45:11', '2020-05-01 17:22:50', 1, 'Q546707'),
(30441, 'Wöllstein', 3019, 'RP', 82, 'DE', 49.81667000, 7.96667000, '2019-10-05 22:45:11', '2020-05-01 17:22:49', 1, 'Q567724'),
(30442, 'Wölpinghausen', 3008, 'NI', 82, 'DE', 52.41667000, 9.23333000, '2019-10-05 22:45:11', '2020-05-01 17:22:48', 1, 'Q674943'),
(30443, 'Wörlitz', 3011, 'ST', 82, 'DE', 51.84172000, 12.42116000, '2019-10-05 22:45:11', '2020-05-01 17:22:49', 1, 'Q518275'),
(30444, 'Wörrstadt', 3019, 'RP', 82, 'DE', 49.84861000, 8.12417000, '2019-10-05 22:45:11', '2020-05-01 17:22:49', 1, 'Q543978'),
(30445, 'Wört', 3006, 'BW', 82, 'DE', 49.03047000, 10.27239000, '2019-10-05 22:45:11', '2020-05-01 17:22:47', 1, 'Q551628'),
(30446, 'Wörth', 3009, 'BY', 82, 'DE', 48.24531000, 11.90214000, '2019-10-05 22:45:11', '2020-05-01 17:22:48', 1, 'Q551628'),
(30447, 'Wörth am Main', 3009, 'BY', 82, 'DE', 49.79720000, 9.15389000, '2019-10-05 22:45:11', '2020-05-01 17:22:48', 1, 'Q76995'),
(30448, 'Wörth am Rhein', 3019, 'RP', 82, 'DE', 49.04888000, 8.25959000, '2019-10-05 22:45:12', '2020-05-01 17:22:49', 1, 'Q522165'),
(30449, 'Wörth an der Donau', 3009, 'BY', 82, 'DE', 49.00093000, 12.40539000, '2019-10-05 22:45:12', '2020-05-01 17:22:48', 1, 'Q488592'),
(30450, 'Wörth an der Isar', 3009, 'BY', 82, 'DE', 48.62300000, 12.33944000, '2019-10-05 22:45:12', '2020-05-01 17:22:48', 1, 'Q528579'),
(30451, 'Wörthsee', 3009, 'BY', 82, 'DE', 48.07250000, 11.20175000, '2019-10-05 22:45:12', '2020-05-01 17:22:48', 1, 'Q528579'),
(30452, 'Wössingen', 3006, 'BW', 82, 'DE', 49.01205000, 8.60754000, '2019-10-05 22:45:12', '2020-05-01 17:22:47', 1, 'Q33522007'),
(30453, 'Wülfershausen', 3009, 'BY', 82, 'DE', 50.33201000, 10.34084000, '2019-10-05 22:45:12', '2020-05-01 17:22:48', 1, 'Q581543'),
(30454, 'Wülfrath', 3017, 'NW', 82, 'DE', 51.28195000, 7.03821000, '2019-10-05 22:45:12', '2020-05-01 17:22:49', 1, 'Q161729'),
(30455, 'Wülknitz', 3021, 'SN', 82, 'DE', 51.36667000, 13.40000000, '2019-10-05 22:45:12', '2020-05-01 17:22:49', 1, 'Q161729'),
(30456, 'Wünnenberg', 3017, 'NW', 82, 'DE', 51.52002000, 8.69934000, '2019-10-05 22:45:12', '2020-05-01 17:22:49', 1, 'Q161729'),
(30457, 'Wünschendorf', 3015, 'TH', 82, 'DE', 50.79662000, 12.09824000, '2019-10-05 22:45:12', '2020-05-01 17:22:50', 1, 'Q161729'),
(30458, 'Würselen', 3017, 'NW', 82, 'DE', 50.81809000, 6.13470000, '2019-10-05 22:45:12', '2020-05-01 17:22:49', 1, 'Q14901'),
(30459, 'Würzburg', 3009, 'BY', 82, 'DE', 49.79391000, 9.95121000, '2019-10-05 22:45:12', '2020-05-01 17:22:48', 1, 'Q2999'),
(30460, 'Wüstenrot', 3006, 'BW', 82, 'DE', 49.08083000, 9.46056000, '2019-10-05 22:45:12', '2020-05-01 17:22:47', 1, 'Q503043'),
(30461, 'Xanten', 3017, 'NW', 82, 'DE', 51.65877000, 6.45297000, '2019-10-05 22:45:12', '2019-10-05 22:45:12', 1, 'Q162890'),
(30462, 'Zabeltitz', 3021, 'SN', 82, 'DE', 51.35147000, 13.50462000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q136744'),
(30463, 'Zaberfeld', 3006, 'BW', 82, 'DE', 49.05611000, 8.92694000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q53805'),
(30464, 'Zachenberg', 3009, 'BY', 82, 'DE', 48.96667000, 13.00000000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q139676'),
(30465, 'Zahna', 3011, 'ST', 82, 'DE', 51.91410000, 12.78561000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q140926'),
(30466, 'Zaisenhausen', 3006, 'BW', 82, 'DE', 49.10667000, 8.81278000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q144467'),
(30467, 'Zandt', 3009, 'BY', 82, 'DE', 49.15000000, 12.73333000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q146727'),
(30468, 'Zangberg', 3009, 'BY', 82, 'DE', 48.27480000, 12.42311000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q146846'),
(30469, 'Zapfendorf', 3009, 'BY', 82, 'DE', 50.01783000, 10.93243000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q147332'),
(30470, 'Zappendorf', 3011, 'ST', 82, 'DE', 51.51024000, 11.79514000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q147479'),
(30471, 'Zarpen', 3005, 'SH', 82, 'DE', 53.86667000, 10.51667000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q148045'),
(30472, 'Zarrendorf', 3007, 'MV', 82, 'DE', 54.23689000, 13.10094000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q148045'),
(30473, 'Zarrentin', 3007, 'MV', 82, 'DE', 53.55075000, 10.91550000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q148091'),
(30474, 'Zehdenick', 3013, 'BB', 82, 'DE', 52.97852000, 13.33165000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q184481'),
(30475, 'Zehlendorf', 3010, 'BE', 82, 'DE', 52.43333000, 13.25000000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q324402'),
(30476, 'Zeil', 3009, 'BY', 82, 'DE', 50.00986000, 10.59470000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q324402'),
(30477, 'Zeilarn', 3009, 'BY', 82, 'DE', 48.30003000, 12.84260000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q184877'),
(30478, 'Zeiskam', 3019, 'RP', 82, 'DE', 49.23278000, 8.24722000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q184983'),
(30479, 'Zeithain', 3021, 'SN', 82, 'DE', 51.33356000, 13.33809000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q8838'),
(30480, 'Zeitlarn', 3009, 'BY', 82, 'DE', 49.07848000, 12.11174000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q186132'),
(30481, 'Zeitlofs', 3009, 'BY', 82, 'DE', 50.26189000, 9.67243000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q186158'),
(30482, 'Zeitz', 3011, 'ST', 82, 'DE', 51.04962000, 12.13690000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q16087'),
(30483, 'Zell', 3019, 'RP', 82, 'DE', 50.02918000, 7.18232000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q16087'),
(30484, 'Zell', 3006, 'BW', 82, 'DE', 48.69235000, 8.06301000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q16087'),
(30485, 'Zell am Main', 3009, 'BY', 82, 'DE', 49.81214000, 9.86962000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q32066'),
(30486, 'Zell im Fichtelgebirge', 3009, 'BY', 82, 'DE', 50.13532000, 11.82266000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q31614'),
(30487, 'Zell im Wiesental', 3006, 'BW', 82, 'DE', 47.70555000, 7.85248000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q61618'),
(30488, 'Zell unter Aichelberg', 3006, 'BW', 82, 'DE', 48.64882000, 9.57137000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q80446'),
(30489, 'Zella-Mehlis', 3015, 'TH', 82, 'DE', 50.65642000, 10.66046000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q187636'),
(30490, 'Zellingen', 3009, 'BY', 82, 'DE', 49.89737000, 9.81746000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q189058'),
(30491, 'Zeltingen-Rachtig', 3019, 'RP', 82, 'DE', 49.95000000, 7.01667000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q189241'),
(30492, 'Zemmer', 3019, 'RP', 82, 'DE', 49.90000000, 6.70000000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q189373'),
(30493, 'Zenting', 3009, 'BY', 82, 'DE', 48.79128000, 13.25968000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q189904'),
(30494, 'Zerbst', 3011, 'ST', 82, 'DE', 51.96620000, 12.08517000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q189904'),
(30495, 'Zerf', 3019, 'RP', 82, 'DE', 49.60000000, 6.68333000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q191620'),
(30496, 'Zernien', 3008, 'NI', 82, 'DE', 53.06845000, 10.88325000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q191879'),
(30497, 'Zetel', 3008, 'NI', 82, 'DE', 53.41667000, 7.98333000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q196850'),
(30498, 'Zeulenroda', 3015, 'TH', 82, 'DE', 50.65278000, 11.98377000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q196996'),
(30499, 'Zeuthen', 3013, 'BB', 82, 'DE', 52.34803000, 13.62174000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q180898'),
(30500, 'Zeven', 3008, 'NI', 82, 'DE', 53.29657000, 9.27685000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q197045'),
(30501, 'Zielitz', 3011, 'ST', 82, 'DE', 52.28958000, 11.67572000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q198991'),
(30502, 'Ziemetshausen', 3009, 'BY', 82, 'DE', 48.29244000, 10.53503000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q199053'),
(30503, 'Zierenberg', 3018, 'HE', 82, 'DE', 51.36948000, 9.30164000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q199094'),
(30504, 'Ziertheim', 3009, 'BY', 82, 'DE', 48.65244000, 10.39882000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q199205'),
(30505, 'Ziesar', 3013, 'BB', 82, 'DE', 52.26616000, 12.28997000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q199233'),
(30506, 'Ziesendorf', 3007, 'MV', 82, 'DE', 53.99433000, 12.03933000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q199233'),
(30507, 'Ziltendorf', 3013, 'BB', 82, 'DE', 52.20608000, 14.62411000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q203347'),
(30508, 'Zimmern ob Rottweil', 3006, 'BW', 82, 'DE', 48.16660000, 8.59436000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q81211'),
(30509, 'Zingst', 3007, 'MV', 82, 'DE', 54.43572000, 12.68880000, '2019-10-05 22:45:13', '2019-10-05 22:45:13', 1, 'Q81211'),
(30510, 'Zinna', 3021, 'SN', 82, 'DE', 51.56790000, 12.95354000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q16071'),
(30511, 'Zirndorf', 3009, 'BY', 82, 'DE', 49.44240000, 10.95414000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q72977'),
(30512, 'Zittau', 3021, 'SN', 82, 'DE', 50.89772000, 14.80764000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q157200'),
(30513, 'Zolling', 3009, 'BY', 82, 'DE', 48.45000000, 11.76667000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q218693'),
(30514, 'Zorge', 3008, 'NI', 82, 'DE', 51.63333000, 10.63333000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q218693'),
(30515, 'Zorneding', 3009, 'BY', 82, 'DE', 48.08433000, 11.82446000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q226775'),
(30516, 'Zornheim', 3019, 'RP', 82, 'DE', 49.89000000, 8.22472000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q226785'),
(30517, 'Zossen', 3013, 'BB', 82, 'DE', 52.21600000, 13.44909000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q226884'),
(30518, 'Zschadrass', 3021, 'SN', 82, 'DE', 51.13828000, 12.81848000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q227216'),
(30519, 'Zschepplin', 3021, 'SN', 82, 'DE', 51.50000000, 12.60000000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q16072'),
(30520, 'Zscherben', 3011, 'ST', 82, 'DE', 51.46709000, 11.87058000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q227237'),
(30521, 'Zscherndorf', 3011, 'ST', 82, 'DE', 51.61087000, 12.26755000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q227238'),
(30522, 'Zschopau', 3021, 'SN', 82, 'DE', 50.74818000, 13.07691000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q58006'),
(30523, 'Zschorlau', 3021, 'SN', 82, 'DE', 50.56667000, 12.65000000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q57857'),
(30524, 'Zschornewitz', 3011, 'ST', 82, 'DE', 51.71585000, 12.39998000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q227296'),
(30525, 'Zschortau', 3021, 'SN', 82, 'DE', 51.47930000, 12.35893000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q227297'),
(30526, 'Zulpich', 3017, 'NW', 82, 'DE', 50.69447000, 6.65414000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q5398'),
(30527, 'Zurow', 3007, 'MV', 82, 'DE', 53.86429000, 11.61434000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q230467'),
(30528, 'Zusamaltheim', 3009, 'BY', 82, 'DE', 48.53104000, 10.63562000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q230613'),
(30529, 'Zusmarshausen', 3009, 'BY', 82, 'DE', 48.40014000, 10.59917000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q230860'),
(30530, 'Zuzenhausen', 3006, 'BW', 82, 'DE', 49.29636000, 8.82254000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q83378'),
(30531, 'Zweibrücken', 3019, 'RP', 82, 'DE', 49.24686000, 7.36977000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q16017'),
(30532, 'Zweiflingen', 3006, 'BW', 82, 'DE', 49.25639000, 9.51806000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q232718'),
(30533, 'Zwenkau', 3021, 'SN', 82, 'DE', 51.21872000, 12.33008000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q10779'),
(30534, 'Zwickau', 3021, 'SN', 82, 'DE', 50.72724000, 12.48839000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q3778'),
(30535, 'Zwiefalten', 3006, 'BW', 82, 'DE', 48.23396000, 9.46232000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q83211'),
(30536, 'Zwiesel', 3009, 'BY', 82, 'DE', 49.01693000, 13.23765000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q163091'),
(30537, 'Zwingenberg', 3018, 'HE', 82, 'DE', 49.72389000, 8.61084000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q163091'),
(30538, 'Zwochau', 3021, 'SN', 82, 'DE', 51.46467000, 12.26844000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q16074'),
(30539, 'Zwota', 3021, 'SN', 82, 'DE', 50.35103000, 12.42241000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q46828'),
(30540, 'Zwönitz', 3021, 'SN', 82, 'DE', 50.63027000, 12.80999000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q58002'),
(30541, 'Zöblitz', 3021, 'SN', 82, 'DE', 50.65912000, 13.22981000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q58000'),
(30542, 'Zörbig', 3011, 'ST', 82, 'DE', 51.62894000, 12.11740000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q247868'),
(30543, 'Zöschen', 3011, 'ST', 82, 'DE', 51.35802000, 12.11652000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q247875'),
(30544, 'Züssow', 3007, 'MV', 82, 'DE', 53.97709000, 13.54855000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q247875'),
(30545, 'Öhningen', 3006, 'BW', 82, 'DE', 47.66186000, 8.88674000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q62044'),
(30546, 'Öhringen', 3006, 'BW', 82, 'DE', 49.19884000, 9.50720000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q294783'),
(30547, 'Ölbronn-Dürrn', 3006, 'BW', 82, 'DE', 48.96667000, 8.75000000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q80508'),
(30548, 'Öpfingen', 3006, 'BW', 82, 'DE', 48.28636000, 9.80259000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q297677'),
(30549, 'Östringen', 3006, 'BW', 82, 'DE', 49.21911000, 8.71192000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q307399'),
(30550, 'Ötigheim', 3006, 'BW', 82, 'DE', 48.89085000, 8.23442000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q83202'),
(30551, 'Ötisheim', 3006, 'BW', 82, 'DE', 48.96667000, 8.80000000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q83208'),
(30552, 'Ötzingen', 3019, 'RP', 82, 'DE', 50.50000000, 7.83333000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q307470'),
(30553, 'Übach-Palenberg', 3017, 'NW', 82, 'DE', 50.91775000, 6.12336000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q204966'),
(30554, 'Überherrn', 3020, 'SL', 82, 'DE', 49.24187000, 6.69840000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q332495'),
(30555, 'Überlingen', 3006, 'BW', 82, 'DE', 47.76977000, 9.17136000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q332952'),
(30556, 'Übersee', 3009, 'BY', 82, 'DE', 47.81667000, 12.48333000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q332952'),
(30557, 'Üchtelhausen', 3009, 'BY', 82, 'DE', 50.09184000, 10.26861000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q334666'),
(30558, 'Üdersdorf', 3019, 'RP', 82, 'DE', 50.15000000, 6.80000000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q49294886'),
(30559, 'Ühlingen-Birkendorf', 3006, 'BW', 82, 'DE', 47.71667000, 8.31667000, '2019-10-05 22:45:14', '2020-05-01 17:22:47', 1, 'Q334746'),
(30560, 'Üxheim', 3019, 'RP', 82, 'DE', 50.35000000, 6.75000000, '2019-10-05 22:45:14', '2020-05-01 17:22:49', 1, 'Q335158'),
(30561, '\'Ali Sabieh', 2933, 'AS', 60, 'DJ', 11.15583000, 42.71250000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q842854'),
(30562, 'Alaïli Ḏaḏḏa‘', 2928, 'OB', 60, 'DJ', 12.42167000, 42.89556000, '2019-10-05 22:45:14', '2020-05-01 17:22:42', 1, 'Q1708992'),
(30563, 'Arta', 2932, 'AR', 60, 'DJ', 11.52639000, 42.85194000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q705884'),
(30564, 'Dikhil', 2930, 'DI', 60, 'DJ', 11.10454000, 42.36971000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q620625'),
(30565, 'Djibouti', 2929, 'DJ', 60, 'DJ', 11.58901000, 43.14503000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q3604'),
(30566, 'Dorra', 2931, 'TA', 60, 'DJ', 12.15028000, 42.47624000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q970073'),
(30567, 'Goubétto', 2933, 'AS', 60, 'DJ', 11.42389000, 43.00028000, '2019-10-05 22:45:14', '2020-05-01 17:22:42', 1, 'Q5588130'),
(30568, 'Gâlâfi', 2930, 'DI', 60, 'DJ', 11.71583000, 41.83611000, '2019-10-05 22:45:14', '2020-05-01 17:22:42', 1, 'Q1491656'),
(30569, 'Holhol', 2933, 'AS', 60, 'DJ', 11.31028000, 42.92944000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q924549'),
(30570, 'Loyada', 2929, 'DJ', 60, 'DJ', 11.46111000, 43.25278000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q983900'),
(30571, 'Obock', 2928, 'OB', 60, 'DJ', 11.96693000, 43.28835000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q860179'),
(30572, 'Tadjourah', 2931, 'TA', 60, 'DJ', 11.78778000, 42.88222000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q820972'),
(30573, 'Aabenraa', 1529, '83', 59, 'DK', 55.04434000, 9.41741000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q154897'),
(30574, 'Aabenraa Kommune', 1529, '83', 59, 'DK', 54.98980000, 9.31282000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q21152'),
(30575, 'Aalborg', 1532, '81', 59, 'DK', 57.04800000, 9.91870000, '2019-10-05 22:45:14', '2019-10-05 22:45:14', 1, 'Q25410'),
(30576, 'Aars', 1532, '81', 59, 'DK', 56.80399000, 9.51441000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q303066'),
(30577, 'Albertslund', 1530, '84', 59, 'DK', 55.65691000, 12.36381000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3196726'),
(30578, 'Albertslund Kommune', 1530, '84', 59, 'DK', 55.68022000, 12.34797000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q493833'),
(30579, 'Allerød Kommune', 1530, '84', 59, 'DK', 55.85856000, 12.32558000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q499479'),
(30580, 'Allingåbro', 1531, '82', 59, 'DK', 56.46432000, 10.31957000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3218538'),
(30581, 'Arden', 1532, '81', 59, 'DK', 56.76899000, 9.86184000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q10417379'),
(30582, 'Asnæs', 1528, '85', 59, 'DK', 55.81229000, 11.50129000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2019461'),
(30583, 'Assens', 1529, '83', 59, 'DK', 55.27023000, 9.90081000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q741067'),
(30584, 'Assens Kommune', 1529, '83', 59, 'DK', 55.29958000, 10.07952000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q27967'),
(30585, 'Assentoft', 1531, '82', 59, 'DK', 56.44210000, 10.15170000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2450123'),
(30586, 'Augustenborg', 1529, '83', 59, 'DK', 54.95201000, 9.87216000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q645631'),
(30587, 'Auning', 1531, '82', 59, 'DK', 56.43079000, 10.37818000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2262931'),
(30588, 'Avlum', 1531, '82', 59, 'DK', 56.26539000, 8.79256000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1771824'),
(30589, 'Ballerup', 1530, '84', 59, 'DK', 55.73165000, 12.36328000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q28143'),
(30590, 'Ballerup Kommune', 1530, '84', 59, 'DK', 55.73248000, 12.35793000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q257223'),
(30591, 'Beder', 1531, '82', 59, 'DK', 56.06025000, 10.21179000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q4441429'),
(30592, 'Bellinge', 1529, '83', 59, 'DK', 55.33535000, 10.32045000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2788987'),
(30593, 'Billund', 1529, '83', 59, 'DK', 55.73349000, 9.10785000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1701099'),
(30594, 'Billund Kommune', 1529, '83', 59, 'DK', 55.73079000, 8.96844000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q27875'),
(30595, 'Birkerød', 1530, '84', 59, 'DK', 55.84759000, 12.42791000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q865630'),
(30596, 'Bjerringbro', 1531, '82', 59, 'DK', 56.37797000, 9.66065000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q4919613'),
(30597, 'Bjæverskov', 1528, '85', 59, 'DK', 55.45756000, 12.03651000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2223144'),
(30598, 'Blovstrød', 1530, '84', 59, 'DK', 55.87038000, 12.38640000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3054896'),
(30599, 'Bogense', 1529, '83', 59, 'DK', 55.56691000, 10.08863000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q890536'),
(30600, 'Bording Kirkeby', 1531, '82', 59, 'DK', 56.16871000, 9.24384000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q23749528'),
(30601, 'Bornholm Kommune', 1530, '84', 59, 'DK', 55.12386000, 14.91115000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2598329'),
(30602, 'Borup', 1528, '85', 59, 'DK', 55.49472000, 11.97584000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3265865'),
(30603, 'Bramming', 1529, '83', 59, 'DK', 55.46946000, 8.70007000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q12304252'),
(30604, 'Brande', 1531, '82', 59, 'DK', 55.94316000, 9.12798000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q897848'),
(30605, 'Brejning', 1529, '83', 59, 'DK', 55.66594000, 9.67217000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2445802'),
(30606, 'Brenderup', 1529, '83', 59, 'DK', 55.48407000, 9.97908000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1923876'),
(30607, 'Broager', 1529, '83', 59, 'DK', 54.88940000, 9.67465000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3196551'),
(30608, 'Brovst', 1532, '81', 59, 'DK', 57.09750000, 9.52264000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q8092118'),
(30609, 'Brædstrup', 1531, '82', 59, 'DK', 55.97153000, 9.61129000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2717744'),
(30610, 'Brøndby Kommune', 1530, '84', 59, 'DK', 55.64290000, 12.41101000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q484038'),
(30611, 'Brønderslev', 1532, '81', 59, 'DK', 57.27021000, 9.94102000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q995880'),
(30612, 'Brønderslev Kommune', 1532, '81', 59, 'DK', 57.23506000, 10.10061000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q511376'),
(30613, 'Brørup', 1529, '83', 59, 'DK', 55.48194000, 9.01756000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q12304584'),
(30614, 'Bullerup', 1529, '83', 59, 'DK', 55.42686000, 10.47137000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2340281'),
(30615, 'Børkop', 1529, '83', 59, 'DK', 55.64195000, 9.64958000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q12304863'),
(30616, 'Charlottenlund', 1530, '84', 59, 'DK', 55.75238000, 12.57450000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q12304863'),
(30617, 'Christiansfeld', 1529, '83', 59, 'DK', 55.35817000, 9.48701000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3197288'),
(30618, 'Christianshavn', 1530, '84', 59, 'DK', 55.67383000, 12.59541000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q861363'),
(30619, 'Christiansø', 1530, '84', 59, 'DK', 55.31982000, 15.18783000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q861363'),
(30620, 'Copenhagen', 1530, '84', 59, 'DK', 55.67594000, 12.56553000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1748'),
(30621, 'Dianalund', 1528, '85', 59, 'DK', 55.53133000, 11.49250000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q12308571'),
(30622, 'Dragør', 1530, '84', 59, 'DK', 55.59280000, 12.67221000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1255278'),
(30623, 'Dragør Kommune', 1530, '84', 59, 'DK', 55.58233000, 12.62756000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q838823'),
(30624, 'Dronninglund', 1532, '81', 59, 'DK', 57.16035000, 10.29287000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1260229'),
(30625, 'Dybbøl', 1529, '83', 59, 'DK', 54.91079000, 9.73601000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2567585'),
(30626, 'Ebeltoft', 1531, '82', 59, 'DK', 56.19442000, 10.68210000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q523400'),
(30627, 'Egedal Kommune', 1530, '84', 59, 'DK', 55.75556000, 12.22778000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q505113'),
(30628, 'Egtved', 1529, '83', 59, 'DK', 55.61613000, 9.30763000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3412882'),
(30629, 'Ejby', 1529, '83', 59, 'DK', 55.43010000, 9.92973000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3618954'),
(30630, 'Ejby', 1528, '85', 59, 'DK', 55.48580000, 12.08171000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2609155'),
(30631, 'Esbjerg', 1529, '83', 59, 'DK', 55.47028000, 8.45187000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q26234'),
(30632, 'Esbjerg Kommune', 1529, '83', 59, 'DK', 55.46893000, 8.46222000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q645747'),
(30633, 'Espergærde', 1530, '84', 59, 'DK', 55.99464000, 12.54733000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3058415'),
(30634, 'Faaborg', 1529, '83', 59, 'DK', 55.09510000, 10.24226000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3547421'),
(30635, 'Faaborg-Midtfyn Kommune', 1529, '83', 59, 'DK', 55.22667000, 10.40667000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q27976'),
(30636, 'Fanø Kommune', 1529, '83', 59, 'DK', 55.41667000, 8.41667000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q493285'),
(30637, 'Farsø', 1532, '81', 59, 'DK', 56.77276000, 9.33925000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3413045'),
(30638, 'Farum', 1530, '84', 59, 'DK', 55.80858000, 12.36066000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3554742'),
(30639, 'Favrskov Kommune', 1531, '82', 59, 'DK', 56.31667000, 9.94000000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q512550'),
(30640, 'Faxe', 1528, '85', 59, 'DK', 55.25561000, 12.11926000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q777334'),
(30641, 'Faxe Kommune', 1528, '85', 59, 'DK', 55.29444000, 12.06111000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q505000'),
(30642, 'Faxe Ladeplads', 1528, '85', 59, 'DK', 55.21981000, 12.17013000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q848213'),
(30643, 'Fensmark', 1528, '85', 59, 'DK', 55.27919000, 11.80382000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2792516'),
(30644, 'Fjerritslev', 1532, '81', 59, 'DK', 57.08822000, 9.26622000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3167139'),
(30645, 'Fløng', 1530, '84', 59, 'DK', 55.66212000, 12.18698000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q5463763'),
(30646, 'Forlev', 1528, '85', 59, 'DK', 55.37445000, 11.25966000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q12311988'),
(30647, 'Framlev', 1531, '82', 59, 'DK', 56.15664000, 10.01318000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q23747554'),
(30648, 'Fredensborg', 1530, '84', 59, 'DK', 55.97558000, 12.40314000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q565436'),
(30649, 'Fredensborg Kommune', 1530, '84', 59, 'DK', 55.95000000, 12.45000000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q503045'),
(30650, 'Fredericia', 1529, '83', 59, 'DK', 55.56568000, 9.75257000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q21181'),
(30651, 'Fredericia Kommune', 1529, '83', 59, 'DK', 55.57271000, 9.69489000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q21145'),
(30652, 'Frederiksberg', 1530, '84', 59, 'DK', 55.67938000, 12.53463000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q30096'),
(30653, 'Frederiksberg', 1528, '85', 59, 'DK', 55.41618000, 11.56934000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q5509820'),
(30654, 'Frederiksberg Kommune', 1530, '84', 59, 'DK', 55.67856000, 12.52216000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2488464'),
(30655, 'Frederikshavn', 1532, '81', 59, 'DK', 57.44073000, 10.53661000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q27452'),
(30656, 'Frederikshavn Kommune', 1532, '81', 59, 'DK', 57.43347000, 10.42507000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q502168'),
(30657, 'Frederikssund', 1530, '84', 59, 'DK', 55.83956000, 12.06896000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3223801'),
(30658, 'Frederikssund Kommune', 1530, '84', 59, 'DK', 55.80957000, 12.04038000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q503232'),
(30659, 'Frederiksværk', 1530, '84', 59, 'DK', 55.97073000, 12.02250000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1796191'),
(30660, 'Frejlev', 1532, '81', 59, 'DK', 57.00623000, 9.81711000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2701822'),
(30661, 'Fuglebjerg', 1528, '85', 59, 'DK', 55.30604000, 11.54766000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q12313132'),
(30662, 'Furesø Kommune', 1530, '84', 59, 'DK', 55.78333000, 12.34167000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q503138'),
(30663, 'Galten', 1531, '82', 59, 'DK', 56.15902000, 9.91691000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2372527'),
(30664, 'Ganløse', 1530, '84', 59, 'DK', 55.79124000, 12.26421000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q23747609'),
(30665, 'Gentofte Kommune', 1530, '84', 59, 'DK', 55.75000000, 12.55000000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q500076'),
(30666, 'Gilleleje', 1530, '84', 59, 'DK', 56.12196000, 12.31056000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2687297'),
(30667, 'Gistrup', 1532, '81', 59, 'DK', 56.99430000, 9.99085000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1936506'),
(30668, 'Give', 1529, '83', 59, 'DK', 55.84523000, 9.23769000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q4994802'),
(30669, 'Gjellerup', 1531, '82', 59, 'DK', 56.14616000, 9.05467000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q4994802'),
(30670, 'Gladsaxe Municipality', 1530, '84', 59, 'DK', 55.76667000, 12.43333000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q484018'),
(30671, 'Glamsbjerg', 1529, '83', 59, 'DK', 55.27237000, 10.10483000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3177358'),
(30672, 'Glostrup', 1530, '84', 59, 'DK', 55.66660000, 12.40377000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3226599'),
(30673, 'Glostrup Kommune', 1530, '84', 59, 'DK', 55.68188000, 12.41018000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q21006'),
(30674, 'Gram', 1529, '83', 59, 'DK', 55.28948000, 9.04913000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1025378'),
(30675, 'Grenaa', 1531, '82', 59, 'DK', 56.41578000, 10.87825000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3619316'),
(30676, 'Greve', 1528, '85', 59, 'DK', 55.58333000, 12.30000000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3619316'),
(30677, 'Greve Kommune', 1528, '85', 59, 'DK', 55.58770000, 12.25060000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q504977'),
(30678, 'Gribskov Kommune', 1530, '84', 59, 'DK', 56.05833000, 12.24167000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q494827'),
(30679, 'Grindsted', 1529, '83', 59, 'DK', 55.75726000, 8.92750000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3547368'),
(30680, 'Gråsten', 1529, '83', 59, 'DK', 54.91918000, 9.59523000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3414260'),
(30681, 'Græsted', 1530, '84', 59, 'DK', 56.06558000, 12.28512000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q847888'),
(30682, 'Guderup', 1529, '83', 59, 'DK', 54.98978000, 9.87174000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q821231'),
(30683, 'Guldborgsund Kommune', 1528, '85', 59, 'DK', 54.76944000, 11.83611000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q510997'),
(30684, 'Gundsømagle', 1528, '85', 59, 'DK', 55.73565000, 12.15158000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2612301'),
(30685, 'Gørlev', 1528, '85', 59, 'DK', 55.53926000, 11.22708000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q12314806'),
(30686, 'Haderslev', 1529, '83', 59, 'DK', 55.24943000, 9.48771000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q29868'),
(30687, 'Haderslev Kommune', 1529, '83', 59, 'DK', 55.24441000, 9.32261000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q21143'),
(30688, 'Hadsten', 1531, '82', 59, 'DK', 56.32819000, 10.04931000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3522043'),
(30689, 'Hadsund', 1532, '81', 59, 'DK', 56.71482000, 10.11682000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q663011'),
(30690, 'Hals', 1532, '81', 59, 'DK', 56.99609000, 10.30807000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q678119'),
(30691, 'Halsnæs Kommune', 1530, '84', 59, 'DK', 55.96765000, 11.94214000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q505130'),
(30692, 'Hammel', 1531, '82', 59, 'DK', 56.25762000, 9.86316000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2604983'),
(30693, 'Hammerum', 1531, '82', 59, 'DK', 56.13361000, 9.06121000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2604983'),
(30694, 'Hanstholm', 1532, '81', 59, 'DK', 57.11667000, 8.61667000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1026874'),
(30695, 'Harboøre', 1531, '82', 59, 'DK', 56.61752000, 8.18069000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q12315699'),
(30696, 'Haslev', 1528, '85', 59, 'DK', 55.32346000, 11.96389000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1016690'),
(30697, 'Havdrup', 1528, '85', 59, 'DK', 55.54481000, 12.12392000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2892005'),
(30698, 'Hedensted', 1531, '82', 59, 'DK', 55.77043000, 9.70110000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3500912'),
(30699, 'Hedensted Kommune', 1531, '82', 59, 'DK', 55.79680000, 9.74400000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q515986'),
(30700, 'Hellebæk', 1530, '84', 59, 'DK', 56.06823000, 12.55782000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1025348'),
(30701, 'Helsinge', 1530, '84', 59, 'DK', 56.02283000, 12.19752000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3173563'),
(30702, 'Helsingør', 1530, '84', 59, 'DK', 56.03606000, 12.61360000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q26881'),
(30703, 'Helsingør Kommune', 1530, '84', 59, 'DK', 56.05000000, 12.50000000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q511546'),
(30704, 'Herlev Kommune', 1530, '84', 59, 'DK', 55.73317000, 12.43106000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q164490'),
(30705, 'Herning', 1531, '82', 59, 'DK', 56.13615000, 8.97662000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q27393'),
(30706, 'Herning Kommune', 1531, '82', 59, 'DK', 56.14997000, 8.89712000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q506877'),
(30707, 'Hillerød', 1530, '84', 59, 'DK', 55.92791000, 12.30081000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q27425'),
(30708, 'Hillerød Kommune', 1530, '84', 59, 'DK', 55.92319000, 12.23794000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q221394'),
(30709, 'Hinnerup', 1531, '82', 59, 'DK', 56.26608000, 10.06299000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3175755'),
(30710, 'Hirtshals', 1532, '81', 59, 'DK', 57.58812000, 9.95922000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q846052'),
(30711, 'Hjallerup', 1532, '81', 59, 'DK', 57.16466000, 10.14571000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2326573'),
(30712, 'Hjortshøj', 1531, '82', 59, 'DK', 56.24811000, 10.26533000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2692491'),
(30713, 'Hjørring', 1532, '81', 59, 'DK', 57.46417000, 9.98229000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q21185'),
(30714, 'Hjørring Kommune', 1532, '81', 59, 'DK', 57.45682000, 10.05859000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q511214'),
(30715, 'Hobro', 1532, '81', 59, 'DK', 56.64306000, 9.79029000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q927713'),
(30716, 'Holbæk', 1528, '85', 59, 'DK', 55.71750000, 11.71279000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q28042'),
(30717, 'Holbæk Kommune', 1528, '85', 59, 'DK', 55.65919000, 11.62049000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q503159'),
(30718, 'Holeby', 1528, '85', 59, 'DK', 54.71148000, 11.46416000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1624448'),
(30719, 'Holstebro', 1531, '82', 59, 'DK', 56.36009000, 8.61607000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q27678'),
(30720, 'Holstebro Kommune', 1531, '82', 59, 'DK', 56.35916000, 8.59631000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q506109'),
(30721, 'Holsted', 1529, '83', 59, 'DK', 55.51086000, 8.91872000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q12317155'),
(30722, 'Hornbæk', 1530, '84', 59, 'DK', 56.09027000, 12.45693000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1257552'),
(30723, 'Hornslet', 1531, '82', 59, 'DK', 56.31550000, 10.32041000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1809950'),
(30724, 'Hornsyld', 1531, '82', 59, 'DK', 55.75621000, 9.85643000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q5373628'),
(30725, 'Horsens', 1531, '82', 59, 'DK', 55.86066000, 9.85034000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q190235'),
(30726, 'Horsens Kommune', 1531, '82', 59, 'DK', 55.92740000, 9.77377000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q512064'),
(30727, 'Humlebæk', 1530, '84', 59, 'DK', 55.96180000, 12.53410000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2631512'),
(30728, 'Hundested', 1530, '84', 59, 'DK', 55.96397000, 11.85044000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1020031'),
(30729, 'Hurup', 1532, '81', 59, 'DK', 56.74944000, 8.41953000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1879794'),
(30730, 'Hvide Sande', 1531, '82', 59, 'DK', 55.99866000, 8.12605000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q734408'),
(30731, 'Hvidovre', 1530, '84', 59, 'DK', 55.65719000, 12.47364000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3590079'),
(30732, 'Hvidovre Kommune', 1530, '84', 59, 'DK', 55.63166000, 12.46615000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q512562'),
(30733, 'Hårby', 1529, '83', 59, 'DK', 55.22357000, 10.12268000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q4994795'),
(30734, 'Hårlev', 1528, '85', 59, 'DK', 55.34936000, 12.23382000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1001430'),
(30735, 'Højby', 1529, '83', 59, 'DK', 55.33177000, 10.43725000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2634492'),
(30736, 'Høje-Taastrup Kommune', 1530, '84', 59, 'DK', 55.65643000, 12.24854000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q499400'),
(30737, 'Høng', 1528, '85', 59, 'DK', 55.50736000, 11.28873000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q12317806'),
(30738, 'Hørning', 1531, '82', 59, 'DK', 56.08701000, 10.03716000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3527551'),
(30739, 'Hørsholm', 1530, '84', 59, 'DK', 55.88098000, 12.50111000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q20835'),
(30740, 'Hørsholm Kommune', 1530, '84', 59, 'DK', 55.88759000, 12.48566000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q512540'),
(30741, 'Høruphav', 1529, '83', 59, 'DK', 54.91017000, 9.89872000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q821237'),
(30742, 'Hørve', 1528, '85', 59, 'DK', 55.75292000, 11.45298000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2671662'),
(30743, 'Ikast', 1531, '82', 59, 'DK', 56.13883000, 9.15768000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1021277'),
(30744, 'Ikast-Brande Kommune', 1531, '82', 59, 'DK', 55.98333000, 9.21667000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q512595'),
(30745, 'Ishøj', 1530, '84', 59, 'DK', 55.61543000, 12.35182000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3196979'),
(30746, 'Ishøj Kommune', 1530, '84', 59, 'DK', 55.62299000, 12.30567000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q502040'),
(30747, 'Jammerbugt Kommune', 1532, '81', 59, 'DK', 57.14583000, 9.56250000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q503222'),
(30748, 'Jelling', 1529, '83', 59, 'DK', 55.75588000, 9.42580000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q835708'),
(30749, 'Juelsminde', 1531, '82', 59, 'DK', 55.70876000, 10.01668000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q835708'),
(30750, 'Jyderup', 1528, '85', 59, 'DK', 55.66399000, 11.42029000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1769354'),
(30751, 'Jyllinge', 1528, '85', 59, 'DK', 55.75295000, 12.10315000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2546789'),
(30752, 'Jægerspris', 1530, '84', 59, 'DK', 55.85248000, 11.98565000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q852156'),
(30753, 'Kalundborg', 1528, '85', 59, 'DK', 55.67954000, 11.08864000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q204957'),
(30754, 'Kalundborg Kommune', 1528, '85', 59, 'DK', 55.63545000, 11.19993000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q204976'),
(30755, 'Karup', 1531, '82', 59, 'DK', 56.30673000, 9.16835000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1734691'),
(30756, 'Kerteminde', 1529, '83', 59, 'DK', 55.44903000, 10.65769000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q29969'),
(30757, 'Kerteminde Kommune', 1529, '83', 59, 'DK', 55.45498000, 10.62220000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q612488'),
(30758, 'Kibæk', 1531, '82', 59, 'DK', 56.03170000, 8.85697000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q2693547'),
(30759, 'Kirke Hvalsø', 1528, '85', 59, 'DK', 55.59170000, 11.86253000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1019136'),
(30760, 'Kjellerup', 1531, '82', 59, 'DK', 56.28581000, 9.43528000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2302168'),
(30761, 'Klarup', 1532, '81', 59, 'DK', 57.01194000, 10.05617000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2398552'),
(30762, 'Kokkedal', 1530, '84', 59, 'DK', 55.91179000, 12.49952000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2040882'),
(30763, 'Kolding', 1529, '83', 59, 'DK', 55.49040000, 9.47216000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q27119'),
(30764, 'Kolding Kommune', 1529, '83', 59, 'DK', 55.45006000, 9.45807000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q21101'),
(30765, 'Kolt', 1531, '82', 59, 'DK', 56.10845000, 10.06986000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q23746178'),
(30766, 'Kongens Lyngby', 1530, '84', 59, 'DK', 55.77044000, 12.50378000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2239'),
(30767, 'Korsør', 1528, '85', 59, 'DK', 55.32993000, 11.13857000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1784816'),
(30768, 'Kruså', 1529, '83', 59, 'DK', 54.85097000, 9.40129000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q570436'),
(30769, 'Kås', 1532, '81', 59, 'DK', 57.19774000, 9.67173000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3643248'),
(30770, 'København', 1530, '84', 59, 'DK', 55.67110000, 12.56529000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q504125'),
(30771, 'Køge', 1528, '85', 59, 'DK', 55.45802000, 12.18214000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q21184'),
(30772, 'Køge Kommune', 1528, '85', 59, 'DK', 55.45668000, 12.07332000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q504664'),
(30773, 'Langeland Kommune', 1529, '83', 59, 'DK', 54.90000000, 10.77222000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q506647'),
(30774, 'Langeskov', 1529, '83', 59, 'DK', 55.35655000, 10.58447000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q737985'),
(30775, 'Langå', 1531, '82', 59, 'DK', 56.39026000, 9.89486000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3528917'),
(30776, 'Lejre', 1528, '85', 59, 'DK', 55.60461000, 11.97477000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2689028'),
(30777, 'Lejre Kommune', 1528, '85', 59, 'DK', 55.63375000, 11.92234000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q505369'),
(30778, 'Lemvig', 1531, '82', 59, 'DK', 56.54856000, 8.31019000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q502632'),
(30779, 'Lemvig Kommune', 1531, '82', 59, 'DK', 56.49136000, 8.29927000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q502644'),
(30780, 'Lillerød', 1530, '84', 59, 'DK', 55.87496000, 12.34579000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q656234'),
(30781, 'Lind', 1531, '82', 59, 'DK', 56.10600000, 8.97915000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q23744911'),
(30782, 'Liseleje', 1530, '84', 59, 'DK', 56.01295000, 11.96544000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1827929'),
(30783, 'Lolland Kommune', 1528, '85', 59, 'DK', 54.80238000, 11.29524000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q670153'),
(30784, 'Lunderskov', 1529, '83', 59, 'DK', 55.48375000, 9.29917000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q12325132'),
(30785, 'Lyngby-Tårbæk Kommune', 1530, '84', 59, 'DK', 55.78456000, 12.50508000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q493826'),
(30786, 'Lynge', 1530, '84', 59, 'DK', 55.83941000, 12.27725000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2885506'),
(30787, 'Lystrup', 1531, '82', 59, 'DK', 56.23750000, 10.23778000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q2633183'),
(30788, 'Læso Kommune', 1532, '81', 59, 'DK', 57.26774000, 11.02265000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q498457'),
(30789, 'Løgstør', 1532, '81', 59, 'DK', 56.96245000, 9.25830000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3414045'),
(30790, 'Løgten', 1531, '82', 59, 'DK', 56.27459000, 10.31181000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1865449'),
(30791, 'Løgumkloster', 1529, '83', 59, 'DK', 55.05941000, 8.95508000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q3172924'),
(30792, 'Løjt Kirkeby', 1529, '83', 59, 'DK', 55.08959000, 9.46084000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1880927'),
(30793, 'Løkken', 1532, '81', 59, 'DK', 57.37047000, 9.71466000, '2019-10-05 22:45:15', '2020-05-01 17:22:42', 1, 'Q1021910'),
(30794, 'Malling', 1531, '82', 59, 'DK', 56.03632000, 10.19632000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q1011688'),
(30795, 'Mariager', 1532, '81', 59, 'DK', 56.64985000, 9.97515000, '2019-10-05 22:45:15', '2019-10-05 22:45:15', 1, 'Q3173150'),
(30796, 'Mariagerfjord Kommune', 1532, '81', 59, 'DK', 56.69722000, 9.84722000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q512528'),
(30797, 'Maribo', 1528, '85', 59, 'DK', 54.77662000, 11.50017000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q386402'),
(30798, 'Marstal', 1529, '83', 59, 'DK', 54.85621000, 10.51726000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3413939'),
(30799, 'Middelfart', 1529, '83', 59, 'DK', 55.50591000, 9.73054000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q613441'),
(30800, 'Middelfart Kommune', 1529, '83', 59, 'DK', 55.45782000, 9.88100000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21140'),
(30801, 'Morsø Kommune', 1532, '81', 59, 'DK', 56.79622000, 8.73272000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q502770'),
(30802, 'Munkebo', 1529, '83', 59, 'DK', 55.45553000, 10.55433000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1953365'),
(30803, 'Måløv', 1530, '84', 59, 'DK', 55.75528000, 12.32327000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q6950010'),
(30804, 'Mårslet', 1531, '82', 59, 'DK', 56.06690000, 10.16112000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2038569'),
(30805, 'Nakskov', 1528, '85', 59, 'DK', 54.83038000, 11.13567000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q24231'),
(30806, 'Neder Holluf', 1529, '83', 59, 'DK', 55.36451000, 10.44824000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q12328490'),
(30807, 'Neder Vindinge', 1528, '85', 59, 'DK', 55.03211000, 11.88356000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q12328490'),
(30808, 'Nexø', 1530, '84', 59, 'DK', 55.06067000, 15.13058000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q643410'),
(30809, 'Nibe', 1532, '81', 59, 'DK', 56.98150000, 9.63917000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1019502'),
(30810, 'Nivå', 1530, '84', 59, 'DK', 55.93405000, 12.50485000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2926034'),
(30811, 'Nordborg', 1529, '83', 59, 'DK', 55.05732000, 9.74080000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1997687'),
(30812, 'Nordby', 1529, '83', 59, 'DK', 55.44603000, 8.39786000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2533977'),
(30813, 'Norddjurs Kommune', 1531, '82', 59, 'DK', 56.44124000, 10.76660000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q505156'),
(30814, 'Nordfyns Kommune', 1529, '83', 59, 'DK', 55.52222000, 10.22222000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q20770'),
(30815, 'Nyborg', 1529, '83', 59, 'DK', 55.31274000, 10.78964000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3530435'),
(30816, 'Nyborg Kommune', 1529, '83', 59, 'DK', 55.29473000, 10.70310000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q30013'),
(30817, 'Nykøbing Falster', 1528, '85', 59, 'DK', 54.76906000, 11.87425000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q986435'),
(30818, 'Nykøbing Mors', 1532, '81', 59, 'DK', 56.79334000, 8.85282000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1019481'),
(30819, 'Nykøbing Sjælland', 1528, '85', 59, 'DK', 55.92491000, 11.67109000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2003608'),
(30820, 'Nyråd', 1528, '85', 59, 'DK', 55.00511000, 11.96060000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q4420514'),
(30821, 'Næstved', 1528, '85', 59, 'DK', 55.22992000, 11.76092000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q21178'),
(30822, 'Næstved Kommune', 1528, '85', 59, 'DK', 55.25855000, 11.74615000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q506716'),
(30823, 'Nødebo', 1530, '84', 59, 'DK', 55.97877000, 12.34685000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1946281'),
(30824, 'Nørre Alslev', 1528, '85', 59, 'DK', 54.89784000, 11.88414000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q15142774'),
(30825, 'Nørre Åby', 1529, '83', 59, 'DK', 55.46107000, 9.87940000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q5259773'),
(30826, 'Nørresundby', 1532, '81', 59, 'DK', 57.05877000, 9.92284000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1246235'),
(30827, 'Odder', 1531, '82', 59, 'DK', 55.97313000, 10.15300000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q925736'),
(30828, 'Odder Kommune', 1531, '82', 59, 'DK', 55.92967000, 10.15304000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q500058'),
(30829, 'Odense', 1529, '83', 59, 'DK', 55.39594000, 10.38831000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q25331'),
(30830, 'Odense Kommune', 1529, '83', 59, 'DK', 55.39570000, 10.37761000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21146'),
(30831, 'Odsherred Kommune', 1528, '85', 59, 'DK', 55.88333000, 11.59444000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1425064'),
(30832, 'Oksbøl', 1529, '83', 59, 'DK', 55.62680000, 8.28757000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q202965'),
(30833, 'Osted', 1528, '85', 59, 'DK', 55.56228000, 11.95786000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2361237'),
(30834, 'Otterup', 1529, '83', 59, 'DK', 55.51527000, 10.39756000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2633021');

