INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(60959, '<PERSON><PERSON><PERSON>', 1703, '78', 107, 'IT', 38.60038000, 16.22205000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60960, '<PERSON><PERSON><PERSON>', 1678, '62', 107, 'IT', 42.41820000, 12.23414000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60961, 'Soric<PERSON>', 1705, '25', 107, 'IT', 46.17259000, 9.38404000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60962, 'Soriso', 1702, '21', 107, 'IT', 45.74037000, 8.40953000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60963, '<PERSON>risole', 1705, '25', 107, 'IT', 45.73140000, 9.63715000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60964, 'Sormano', 1705, '25', 107, 'IT', 45.87803000, 9.24582000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60965, 'Sorradile', 1715, '88', 107, 'IT', 40.10634000, 8.93236000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60966, 'Sorrento', 1669, '72', 107, 'IT', 40.62678000, 14.37771000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60967, 'Sorso', 1715, '88', 107, 'IT', 40.79949000, 8.57570000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60968, 'Sortino', 1709, '82', 107, 'IT', 37.15881000, 15.02998000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60969, 'Sospiro', 1705, '25', 107, 'IT', 45.10762000, 10.15853000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60970, 'Sospirolo', 1753, '34', 107, 'IT', 46.14134000, 12.07365000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60971, 'Sossano', 1753, '34', 107, 'IT', 45.35754000, 11.50819000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60972, 'Sostegno', 1702, '21', 107, 'IT', 45.65287000, 8.26982000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60973, 'Sotto il Monte Giovanni XXIII', 1705, '25', 107, 'IT', 45.70568000, 9.50345000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1710080'),
(60974, 'Sottocastello', 1705, '25', 107, 'IT', 45.66720000, 10.39416000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18477516'),
(60975, 'Sottoselva', 1756, '36', 107, 'IT', 45.91134000, 13.32416000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18452549'),
(60976, 'Sover', 1725, '32', 107, 'IT', 46.22171000, 11.31568000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18452549'),
(60977, 'Soverato Marina', 1703, '78', 107, 'IT', 38.68498000, 16.54991000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429488'),
(60978, 'Soverato Superiore', 1703, '78', 107, 'IT', 38.68993000, 16.53391000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60979, 'Sovere', 1705, '25', 107, 'IT', 45.81188000, 10.03438000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60980, 'Soveria Mannelli', 1703, '78', 107, 'IT', 39.07784000, 16.37708000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60981, 'Soveria Simeri', 1703, '78', 107, 'IT', 38.94986000, 16.67854000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60982, 'Soverzene', 1753, '34', 107, 'IT', 46.20331000, 12.30301000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60983, 'Sovicille', 1664, '52', 107, 'IT', 43.28033000, 11.22828000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60984, 'Sovico', 1705, '25', 107, 'IT', 45.64598000, 9.26275000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60985, 'Sovizzo', 1753, '34', 107, 'IT', 45.52669000, 11.44610000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60986, 'Sovramonte', 1753, '34', 107, 'IT', 46.05861000, 11.78660000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60987, 'Sozzago', 1702, '21', 107, 'IT', 45.39832000, 8.72271000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60988, 'Spadafora', 1709, '82', 107, 'IT', 38.22349000, 15.38178000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60989, 'Spadola', 1703, '78', 107, 'IT', 38.60378000, 16.33700000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60990, 'Sparanise', 1669, '72', 107, 'IT', 41.18788000, 14.09628000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60991, 'Sparone', 1702, '21', 107, 'IT', 45.41442000, 7.54516000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429489'),
(60992, 'Sparta\'', 1709, '82', 107, 'IT', 38.29371000, 15.53503000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18466533'),
(60993, 'Spartimento', 1669, '72', 107, 'IT', 40.89105000, 14.47236000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(60994, 'Specchia', 1688, '75', 107, 'IT', 39.93913000, 18.29784000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(60995, 'Spello', 1683, '55', 107, 'IT', 42.99231000, 12.66632000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(60996, 'Spera', 1725, '32', 107, 'IT', 46.07031000, 11.50899000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(60997, 'Sperlinga', 1709, '82', 107, 'IT', 37.76653000, 14.35075000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(60998, 'Sperlonga', 1678, '62', 107, 'IT', 41.25897000, 13.43302000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(60999, 'Sperone', 1669, '72', 107, 'IT', 40.95251000, 14.60524000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(61000, 'Spessa', 1705, '25', 107, 'IT', 45.11356000, 9.34856000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(61001, 'Spezzano Albanese', 1703, '78', 107, 'IT', 39.66854000, 16.30939000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(61002, 'Spezzano Piccolo', 1703, '78', 107, 'IT', 39.29010000, 16.34211000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(61003, 'Spezzano della Sila', 1703, '78', 107, 'IT', 39.30033000, 16.33923000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437102'),
(61004, 'Spianate', 1664, '52', 107, 'IT', 43.81075000, 10.71510000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18489113'),
(61005, 'Spiazzo', 1725, '32', 107, 'IT', 46.10360000, 10.73937000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18489113'),
(61006, 'Spicchio-Sovigliana', 1664, '52', 107, 'IT', 43.73016000, 10.93921000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18487441'),
(61007, 'Spigno Monferrato', 1702, '21', 107, 'IT', 44.54314000, 8.33395000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18487441'),
(61008, 'Spigno Saturnia Inferiore', 1678, '62', 107, 'IT', 41.31146000, 13.73685000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453908'),
(61009, 'Spigno Saturnia Superiore', 1678, '62', 107, 'IT', 41.31425000, 13.70888000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61010, 'Spilamberto', 1773, '45', 107, 'IT', 44.53273000, 11.01697000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61011, 'Spilimbergo', 1756, '36', 107, 'IT', 46.11345000, 12.89241000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61012, 'Spilinga', 1703, '78', 107, 'IT', 38.62825000, 15.90572000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61013, 'Spinadesco', 1705, '25', 107, 'IT', 45.14917000, 9.92637000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61014, 'Spinazzola', 1686, 'BT', 107, 'IT', 40.96399000, 16.09111000, '2019-10-05 23:07:13', '2022-11-06 17:45:20', 1, 'Q18453909'),
(61015, 'Spinea-Orgnano', 1753, '34', 107, 'IT', 45.49107000, 12.15500000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61016, 'Spineda', 1705, '25', 107, 'IT', 45.06140000, 10.51165000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61017, 'Spinete', 1695, '67', 107, 'IT', 41.54431000, 14.48743000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61018, 'Spineto Scrivia', 1702, '21', 107, 'IT', 44.83735000, 8.87375000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61019, 'Spinetoli', 1670, '57', 107, 'IT', 42.88984000, 13.76497000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18453909'),
(61020, 'Spinetta Marengo', 1702, '21', 107, 'IT', 44.88554000, 8.67750000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q748210'),
(61021, 'Spinimbecco', 1753, '34', 107, 'IT', 45.14398000, 11.37202000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61022, 'Spino d\'Adda', 1705, '25', 107, 'IT', 45.40086000, 9.48674000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61023, 'Spinone al Lago', 1705, '25', 107, 'IT', 45.76476000, 9.92157000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61024, 'Spinoso', 1706, '77', 107, 'IT', 40.26921000, 15.96658000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61025, 'Spirano', 1705, '25', 107, 'IT', 45.58144000, 9.66863000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61026, 'Spoleto', 1683, '55', 107, 'IT', 42.74071000, 12.73899000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61027, 'Spoltore', 1679, '65', 107, 'IT', 42.45501000, 14.13988000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61028, 'Spongano', 1688, '75', 107, 'IT', 40.01782000, 18.36563000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61029, 'Spormaggiore', 1725, '32', 107, 'IT', 46.21853000, 11.04805000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61030, 'Sporminore', 1725, '32', 107, 'IT', 46.23692000, 11.02919000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61031, 'Spotorno', 1768, '42', 107, 'IT', 44.22638000, 8.41647000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61032, 'Spresiano', 1753, '34', 107, 'IT', 45.77908000, 12.25628000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61033, 'Spriana', 1705, '25', 107, 'IT', 46.22030000, 9.86425000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61034, 'Squillace', 1703, '78', 107, 'IT', 38.78091000, 16.51175000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q2048225'),
(61035, 'Squillace Lido', 1703, '78', 107, 'IT', 38.77781000, 16.57007000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429493'),
(61036, 'Squinzano', 1688, '75', 107, 'IT', 40.43513000, 18.04086000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18429493'),
(61037, 'Stabbia', 1664, '52', 107, 'IT', 43.78266000, 10.83475000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18487442'),
(61038, 'Staffa', 1702, '21', 107, 'IT', 45.96855000, 7.96708000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18446322'),
(61039, 'Staffolo', 1670, '57', 107, 'IT', 43.43326000, 13.18487000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18446322'),
(61040, 'Staggia', 1664, '52', 107, 'IT', 43.42182000, 11.18364000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1367559'),
(61041, 'Stagno Lombardo', 1705, '25', 107, 'IT', 45.07459000, 10.08890000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1367559'),
(61042, 'Staiti', 1703, '78', 107, 'IT', 37.99989000, 16.03341000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1367559'),
(61043, 'Staletti', 1703, '78', 107, 'IT', 38.76428000, 16.53932000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q1367559'),
(61044, 'Stallavena-Lugo', 1753, '34', 107, 'IT', 45.56289000, 10.99669000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q16608491'),
(61045, 'Stanghe', 1725, '32', 107, 'IT', 46.88219000, 11.37764000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18509453'),
(61046, 'Stanghella', 1753, '34', 107, 'IT', 45.13509000, 11.75741000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18509453'),
(61047, 'Staranzano', 1756, '36', 107, 'IT', 45.80233000, 13.50226000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18509453'),
(61048, 'Starza Vecchia', 1669, '72', 107, 'IT', 40.89035000, 14.40138000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437108'),
(61049, 'Statte', 1688, '75', 107, 'IT', 40.52856000, 17.20122000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18437108'),
(61050, 'Stazione', 1664, '52', 107, 'IT', 43.91460000, 11.01414000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q3970184'),
(61051, 'Stazione', 1670, '57', 107, 'IT', 43.50046000, 13.15149000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18496212'),
(61052, 'Stazione Masotti', 1664, '52', 107, 'IT', 43.90984000, 10.85386000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18491231'),
(61053, 'Stazione Montalto-Coretto', 1703, '78', 107, 'IT', 39.40720000, 16.24313000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18430664'),
(61054, 'Stazione Valmozzola', 1773, '45', 107, 'IT', 44.57879000, 9.94290000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18443325'),
(61055, 'Stazione di Allerona', 1683, '55', 107, 'IT', 42.78550000, 12.03726000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q3968861'),
(61056, 'Stazione di Padule', 1683, '55', 107, 'IT', 43.30766000, 12.61557000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q3968861'),
(61057, 'Stazione-Fornola', 1768, '42', 107, 'IT', 44.12984000, 9.89159000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18461033'),
(61058, 'Stazzano', 1702, '21', 107, 'IT', 44.72728000, 8.86846000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18461033'),
(61059, 'Stazzona', 1705, '25', 107, 'IT', 46.13879000, 9.27524000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18461033'),
(61060, 'Stefanaconi', 1703, '78', 107, 'IT', 38.67788000, 16.12420000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18461033'),
(61061, 'Stella', 1670, '57', 107, 'IT', 42.88698000, 13.80948000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18496758'),
(61062, 'Stella', 1669, '72', 107, 'IT', 40.86027000, 14.25223000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18496758'),
(61063, 'Stella Cilento', 1669, '72', 107, 'IT', 40.23180000, 15.09286000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18496758'),
(61064, 'Stellanello', 1768, '42', 107, 'IT', 43.99993000, 8.06025000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18496758'),
(61065, 'Stelvio', 1725, '32', 107, 'IT', 46.59767000, 10.54580000, '2019-10-05 23:07:13', '2019-10-05 23:07:13', 1, 'Q18496758'),
(61066, 'Stenico', 1725, '32', 107, 'IT', 46.05124000, 10.85562000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18496758'),
(61067, 'Sternatia', 1688, '75', 107, 'IT', 40.22022000, 18.22748000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18496758'),
(61068, 'Stezzano', 1705, '25', 107, 'IT', 45.64968000, 9.65192000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18496758'),
(61069, 'Stia', 1664, '52', 107, 'IT', 43.79877000, 11.70894000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q52100'),
(61070, 'Stiava', 1664, '52', 107, 'IT', 43.89720000, 10.31798000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489123'),
(61071, 'Stienta', 1753, '34', 107, 'IT', 44.93970000, 11.54375000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489123'),
(61072, 'Stigliano', 1706, '77', 107, 'IT', 40.40237000, 16.22983000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489123'),
(61073, 'Stigliano', 1753, '34', 107, 'IT', 45.52362000, 12.04377000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61074, 'Stignano', 1703, '78', 107, 'IT', 38.41705000, 16.47011000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61075, 'Stilo', 1703, '78', 107, 'IT', 38.47628000, 16.46746000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61076, 'Stimigliano', 1678, '62', 107, 'IT', 42.30103000, 12.56337000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61077, 'Stintino', 1715, '88', 107, 'IT', 40.95201000, 8.21978000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61078, 'Stio', 1669, '72', 107, 'IT', 40.31020000, 15.25153000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61079, 'Stornara', 1688, '75', 107, 'IT', 41.28672000, 15.77003000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61080, 'Stornarella', 1688, '75', 107, 'IT', 41.25591000, 15.73023000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61081, 'Storo', 1725, '32', 107, 'IT', 45.84925000, 10.58022000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61082, 'Stra', 1753, '34', 107, 'IT', 45.41123000, 12.01418000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18503692'),
(61083, 'Strada', 1664, '52', 107, 'IT', 43.74452000, 11.71007000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18486142'),
(61084, 'Strada', 1670, '57', 107, 'IT', 43.40337000, 13.22077000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18497912'),
(61085, 'Strada in Chianti', 1664, '52', 107, 'IT', 43.65874000, 11.29678000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q3974648'),
(61086, 'Stradella', 1705, '25', 107, 'IT', 45.07445000, 9.30169000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q3974648'),
(61087, 'Strambinello', 1702, '21', 107, 'IT', 45.42306000, 7.76992000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q3974648'),
(61088, 'Strambino', 1702, '21', 107, 'IT', 45.37990000, 7.88967000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q3974648'),
(61089, 'Strangolagalli', 1678, '62', 107, 'IT', 41.59992000, 13.49228000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q3974648'),
(61090, 'Stravignino', 1705, '25', 107, 'IT', 45.77469000, 10.23838000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18477613'),
(61091, 'Stregna', 1756, '36', 107, 'IT', 46.12689000, 13.57761000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18477613'),
(61092, 'Strembo', 1725, '32', 107, 'IT', 46.12030000, 10.75087000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18477613'),
(61093, 'Stresa', 1702, '21', 107, 'IT', 45.88158000, 8.53834000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18477613'),
(61094, 'Strettoia', 1664, '52', 107, 'IT', 43.98987000, 10.19357000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489126'),
(61095, 'Strevi', 1702, '21', 107, 'IT', 44.69985000, 8.52470000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489126'),
(61096, 'Striano', 1669, '72', 107, 'IT', 40.81543000, 14.57534000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489126'),
(61097, 'Strigno', 1725, '32', 107, 'IT', 46.06690000, 11.52224000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30030942'),
(61098, 'Stroncone', 1683, '55', 107, 'IT', 42.51196000, 12.64770000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30030942'),
(61099, 'Strongoli', 1703, '78', 107, 'IT', 39.26576000, 17.05413000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30030942'),
(61100, 'Stroppiana', 1702, '21', 107, 'IT', 45.23037000, 8.45461000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30030942'),
(61101, 'Stroppo', 1702, '21', 107, 'IT', 44.50653000, 7.12652000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30030942'),
(61102, 'Strozza', 1705, '25', 107, 'IT', 45.77294000, 9.57886000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30030942'),
(61103, 'Strudà', 1688, '75', 107, 'IT', 40.31991000, 18.28176000, '2019-10-05 23:07:14', '2020-05-01 17:22:55', 1, 'Q3147228'),
(61104, 'Strà-Montanara-Pieve', 1753, '34', 107, 'IT', 45.42367000, 11.15749000, '2019-10-05 23:07:14', '2020-05-01 17:22:57', 1, 'Q18505610'),
(61105, 'Sturno', 1669, '72', 107, 'IT', 41.02217000, 15.11248000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18505610'),
(61106, 'Su Planu', 1715, '88', 107, 'IT', 39.25487000, 9.10660000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61107, 'Suardi', 1705, '25', 107, 'IT', 45.03247000, 8.74127000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61108, 'Subbiano', 1664, '52', 107, 'IT', 43.57815000, 11.87140000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61109, 'Subiaco', 1678, '62', 107, 'IT', 41.92619000, 13.08906000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61110, 'Succivo', 1669, '72', 107, 'IT', 40.96801000, 14.25563000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61111, 'Sueglio', 1705, '25', 107, 'IT', 46.08579000, 9.33374000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61112, 'Suelli', 1715, '88', 107, 'IT', 39.56257000, 9.13245000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61113, 'Suello', 1705, '25', 107, 'IT', 45.81678000, 9.31154000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61114, 'Suisio', 1705, '25', 107, 'IT', 45.65708000, 9.50225000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61115, 'Sulbiate', 1705, '25', 107, 'IT', 45.64047000, 9.42762000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61116, 'Sulmona', 1679, '65', 107, 'IT', 42.04945000, 13.92578000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61117, 'Sulzano', 1705, '25', 107, 'IT', 45.68732000, 10.09988000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61118, 'Sumirago', 1705, '25', 107, 'IT', 45.73651000, 8.78356000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18448801'),
(61119, 'Summaga', 1753, '34', 107, 'IT', 45.77406000, 12.79792000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61120, 'Summonte', 1669, '72', 107, 'IT', 40.94874000, 14.74458000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61121, 'Suni', 1715, '88', 107, 'IT', 40.28085000, 8.54942000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61122, 'Suno', 1702, '21', 107, 'IT', 45.63137000, 8.54437000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61123, 'Supersano', 1688, '75', 107, 'IT', 40.01655000, 18.24205000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61124, 'Supino', 1678, '62', 107, 'IT', 41.62135000, 13.23452000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61125, 'Surano', 1688, '75', 107, 'IT', 40.02818000, 18.34591000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61126, 'Surbo', 1688, '75', 107, 'IT', 40.39383000, 18.13456000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q14544570'),
(61127, 'Surdo', 1703, '78', 107, 'IT', 39.32752000, 16.20139000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61128, 'Susa', 1702, '21', 107, 'IT', 45.13677000, 7.05809000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61129, 'Susegana', 1753, '34', 107, 'IT', 45.85604000, 12.25741000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61130, 'Susella', 1705, '25', 107, 'IT', 44.91267000, 9.09651000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61131, 'Sustinente', 1705, '25', 107, 'IT', 45.07096000, 11.02178000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61132, 'Sutera', 1709, '82', 107, 'IT', 37.52493000, 13.73274000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61133, 'Sutri', 1678, '62', 107, 'IT', 42.24596000, 12.21715000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61134, 'Sutrio', 1756, '36', 107, 'IT', 46.51206000, 12.99325000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61135, 'Suvereto', 1664, '52', 107, 'IT', 43.07669000, 10.67740000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61136, 'Suzzara', 1705, '25', 107, 'IT', 44.99242000, 10.74407000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18430681'),
(61137, 'Tabiago-Cibrone', 1705, '25', 107, 'IT', 45.75639000, 9.26684000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489295'),
(61138, 'Taceno', 1705, '25', 107, 'IT', 46.02253000, 9.36400000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489295'),
(61139, 'Tadasuni', 1715, '88', 107, 'IT', 40.10995000, 8.88353000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489295'),
(61140, 'Taggia', 1768, '42', 107, 'IT', 43.84612000, 7.85223000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489295'),
(61141, 'Taggì', 1753, '34', 107, 'IT', 45.46070000, 11.81865000, '2019-10-05 23:07:14', '2020-05-01 17:22:57', 1, 'Q18498953'),
(61142, 'Tagliacozzo', 1679, '65', 107, 'IT', 42.06933000, 13.25469000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18498953'),
(61143, 'Tagliaferro', 1702, '21', 107, 'IT', 44.97850000, 7.66430000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18445189'),
(61144, 'Taglio', 1753, '34', 107, 'IT', 45.02222000, 12.21083000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61145, 'Taglio di Po', 1753, '34', 107, 'IT', 45.00189000, 12.21386000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61146, 'Tagliolo Monferrato', 1702, '21', 107, 'IT', 44.63835000, 8.66585000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61147, 'Taibon Agordino', 1753, '34', 107, 'IT', 46.29838000, 12.01312000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61148, 'Taino', 1705, '25', 107, 'IT', 45.76249000, 8.61654000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61149, 'Taio', 1725, '32', 107, 'IT', 46.32139000, 11.06624000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61150, 'Taipana', 1756, '36', 107, 'IT', 46.24911000, 13.34150000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61151, 'Talamello', 1773, '45', 107, 'IT', 43.90566000, 12.28484000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61152, 'Talamona', 1705, '25', 107, 'IT', 46.13849000, 9.61294000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61153, 'Talana', 1715, '88', 107, 'IT', 40.04157000, 9.49554000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61154, 'Taleggio', 1705, '25', 107, 'IT', 45.89339000, 9.56485000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61155, 'Talla', 1664, '52', 107, 'IT', 43.60185000, 11.78714000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61156, 'Talmassons', 1756, '36', 107, 'IT', 45.92810000, 13.12199000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500406'),
(61157, 'Talponada', 1753, '34', 107, 'IT', 45.70351000, 12.49053000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18502581'),
(61158, 'Tamai', 1756, '36', 107, 'IT', 45.92427000, 12.57187000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18449162'),
(61159, 'Tambre', 1753, '34', 107, 'IT', 46.12632000, 12.41941000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18449162'),
(61160, 'Taneto', 1773, '45', 107, 'IT', 44.77202000, 10.45563000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61161, 'Taormina', 1709, '82', 107, 'IT', 37.85358000, 15.28851000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61162, 'Tarano', 1678, '62', 107, 'IT', 42.35593000, 12.59497000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61163, 'Taranta Peligna', 1679, '65', 107, 'IT', 42.02052000, 14.17103000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61164, 'Tarantasca', 1702, '21', 107, 'IT', 44.49322000, 7.54459000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61165, 'Taranto', 1688, '75', 107, 'IT', 40.46438000, 17.24707000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61166, 'Tarcento', 1756, '36', 107, 'IT', 46.21251000, 13.21514000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61167, 'Tarquinia', 1678, '62', 107, 'IT', 42.25419000, 11.75657000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61168, 'Tarsia', 1703, '78', 107, 'IT', 39.62311000, 16.27337000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61169, 'Tartano', 1705, '25', 107, 'IT', 46.10554000, 9.67864000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61170, 'Tarvisio', 1756, '36', 107, 'IT', 46.50567000, 13.58689000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61171, 'Tarzo', 1753, '34', 107, 'IT', 45.97711000, 12.22917000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61172, 'Tassarolo', 1702, '21', 107, 'IT', 44.72811000, 8.77164000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q1976012'),
(61173, 'Tassullo', 1725, '32', 107, 'IT', 46.34298000, 11.04539000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031013'),
(61174, 'Taurano', 1669, '72', 107, 'IT', 40.88444000, 14.63427000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031013'),
(61175, 'Taurasi', 1669, '72', 107, 'IT', 41.01080000, 14.95980000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031013'),
(61176, 'Taurianova', 1703, '78', 107, 'IT', 38.35525000, 16.01306000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031013'),
(61177, 'Taurisano', 1688, '75', 107, 'IT', 39.95746000, 18.21498000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031013'),
(61178, 'Tavagnacco', 1756, '36', 107, 'IT', 46.10976000, 13.22251000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031019'),
(61179, 'Tavagnasco', 1702, '21', 107, 'IT', 45.54448000, 7.82293000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031019'),
(61180, 'Tavarnelle Val di Pesa', 1664, '52', 107, 'IT', 43.56184000, 11.17152000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q30031019'),
(61181, 'Tavarnuzze', 1664, '52', 107, 'IT', 43.71019000, 11.21721000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q3981579'),
(61182, 'Tavazzano', 1705, '25', 107, 'IT', 45.32783000, 9.40473000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489797'),
(61183, 'Tavenna', 1695, '67', 107, 'IT', 41.90898000, 14.76176000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489797'),
(61184, 'Taverna', 1703, '78', 107, 'IT', 39.02200000, 16.58081000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18489797'),
(61185, 'Taverna Ravindola', 1695, '67', 107, 'IT', 41.52283000, 14.12591000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18500807'),
(61186, 'Taverne D\'Arbia', 1664, '52', 107, 'IT', 43.29490000, 11.39483000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q3981593'),
(61187, 'Tavernelle', 1683, '55', 107, 'IT', 43.00333000, 12.14520000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18494134'),
(61188, 'Tavernelle', 1670, '57', 107, 'IT', 43.73203000, 12.88349000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18498950'),
(61189, 'Tavernelle', 1753, '34', 107, 'IT', 45.51498000, 11.44860000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18506982'),
(61190, 'Tavernerio', 1705, '25', 107, 'IT', 45.80068000, 9.14054000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18506982'),
(61191, 'Tavernola Bergamasca', 1705, '25', 107, 'IT', 45.70846000, 10.04454000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18506982'),
(61192, 'Tavernole', 1705, '25', 107, 'IT', 45.74618000, 10.23952000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18506982'),
(61193, 'Taviano', 1688, '75', 107, 'IT', 39.98224000, 18.08151000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18506982'),
(61194, 'Tavigliano', 1702, '21', 107, 'IT', 45.62206000, 8.05172000, '2019-10-05 23:07:14', '2019-10-05 23:07:14', 1, 'Q18506982'),
(61195, 'Tavo', 1753, '34', 107, 'IT', 45.50066000, 11.86079000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498959'),
(61196, 'Tavoleto', 1670, '57', 107, 'IT', 43.84402000, 12.59418000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498959'),
(61197, 'Tavullia', 1670, '57', 107, 'IT', 43.89791000, 12.75108000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498959'),
(61198, 'Teana', 1706, '77', 107, 'IT', 40.12586000, 16.15281000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498959'),
(61199, 'Teano', 1669, '72', 107, 'IT', 41.25132000, 14.06652000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498959'),
(61200, 'Tecchiena', 1678, '62', 107, 'IT', 41.68822000, 13.34538000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q3982395'),
(61201, 'Teggiano-Macchiaroli', 1669, '72', 107, 'IT', 40.37921000, 15.54046000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18437832'),
(61202, 'Teglio', 1705, '25', 107, 'IT', 46.17235000, 10.06399000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18437832'),
(61203, 'Teglio Veneto', 1753, '34', 107, 'IT', 45.81637000, 12.88414000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18437832'),
(61204, 'Tegoleto', 1664, '52', 107, 'IT', 43.39458000, 11.78530000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18486164'),
(61205, 'Telese', 1669, '72', 107, 'IT', 41.21752000, 14.52681000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18436369'),
(61206, 'Telgate', 1705, '25', 107, 'IT', 45.62792000, 9.84912000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18436369'),
(61207, 'Telti', 1715, '88', 107, 'IT', 40.87575000, 9.35328000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18436369'),
(61208, 'Telve', 1725, '32', 107, 'IT', 46.07011000, 11.48022000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18436369'),
(61209, 'Telve di Sopra', 1725, '32', 107, 'IT', 46.07131000, 11.47179000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18436369'),
(61210, 'Tempio Pausania', 1715, '88', 107, 'IT', 40.90068000, 9.10456000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18436369'),
(61211, 'Temù', 1705, '25', 107, 'IT', 46.25022000, 10.46592000, '2019-10-05 23:07:15', '2020-05-01 17:22:56', 1, 'Q18436369'),
(61212, 'Tencarola', 1753, '34', 107, 'IT', 45.39490000, 11.80919000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498961'),
(61213, 'Tenna', 1725, '32', 107, 'IT', 46.01570000, 11.26428000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498961'),
(61214, 'Tenno', 1725, '32', 107, 'IT', 45.91937000, 10.83160000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498961'),
(61215, 'Teor', 1756, '36', 107, 'IT', 45.85416000, 13.05608000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61216, 'Teora', 1669, '72', 107, 'IT', 40.85282000, 15.25335000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61217, 'Teramo', 1679, '65', 107, 'IT', 42.66123000, 13.69901000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61218, 'Terdobbiate', 1702, '21', 107, 'IT', 45.37588000, 8.69458000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61219, 'Terelle', 1678, '62', 107, 'IT', 41.55212000, 13.77841000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61220, 'Terento', 1725, '32', 107, 'IT', 46.82992000, 11.78288000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61221, 'Terenzo', 1773, '45', 107, 'IT', 44.61048000, 10.08956000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61222, 'Tergu', 1715, '88', 107, 'IT', 40.86652000, 8.71467000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61223, 'Terlago', 1725, '32', 107, 'IT', 46.09737000, 11.04505000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61224, 'Terlano', 1725, '32', 107, 'IT', 46.53216000, 11.24689000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61225, 'Terlizzi', 1688, '75', 107, 'IT', 41.12905000, 16.54536000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q53379'),
(61226, 'Terme', 1709, '82', 107, 'IT', 38.13586000, 15.15658000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61227, 'Termeno', 1725, '32', 107, 'IT', 46.34151000, 11.24228000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61228, 'Termini Imerese', 1709, '82', 107, 'IT', 37.98225000, 13.69729000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61229, 'Termoli', 1695, '67', 107, 'IT', 41.99994000, 14.99389000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61230, 'Ternate', 1705, '25', 107, 'IT', 45.78673000, 8.69931000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61231, 'Ternengo', 1702, '21', 107, 'IT', 45.58896000, 8.11372000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61232, 'Terni', 1683, '55', 107, 'IT', 42.56335000, 12.64329000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61233, 'Terno d\'Isola', 1705, '25', 107, 'IT', 45.68548000, 9.53095000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18466634'),
(61234, 'Terontola', 1664, '52', 107, 'IT', 43.21009000, 12.01113000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q1124819'),
(61235, 'Terracina', 1678, '62', 107, 'IT', 41.29174000, 13.24359000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q1124819'),
(61236, 'Terradura', 1753, '34', 107, 'IT', 45.32290000, 11.82425000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498968'),
(61237, 'Terragnolo', 1725, '32', 107, 'IT', 45.87830000, 11.15468000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498968'),
(61238, 'Terralba', 1715, '88', 107, 'IT', 39.72056000, 8.63504000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498968'),
(61239, 'Terranova Sappo Minulio', 1703, '78', 107, 'IT', 38.32175000, 16.00747000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498968'),
(61240, 'Terranova da Sibari', 1703, '78', 107, 'IT', 39.65548000, 16.33979000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18498968'),
(61241, 'Terranova dei Passerini', 1705, '25', 107, 'IT', 45.21527000, 9.66186000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q30031072'),
(61242, 'Terranova di Pollino', 1706, '77', 107, 'IT', 39.97765000, 16.29583000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q30031072'),
(61243, 'Terranuova Bracciolini', 1664, '52', 107, 'IT', 43.55081000, 11.58075000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q30031072'),
(61244, 'Terrarossa', 1664, '52', 107, 'IT', 44.23362000, 9.96110000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q2309058'),
(61245, 'Terrasini', 1709, '82', 107, 'IT', 38.14621000, 13.08319000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q2309058'),
(61246, 'Terrassa Padovana', 1753, '34', 107, 'IT', 45.24409000, 11.90271000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q2309058'),
(61247, 'Terravecchia', 1703, '78', 107, 'IT', 39.46594000, 16.94623000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q2309058'),
(61248, 'Terraverde-Corte Palasio', 1705, '25', 107, 'IT', 45.31552000, 9.56162000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18489801'),
(61249, 'Terrazzo', 1753, '34', 107, 'IT', 45.17260000, 11.39485000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18489801'),
(61250, 'Terres', 1725, '32', 107, 'IT', 46.30991000, 11.02307000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q30031083'),
(61251, 'Terricciola', 1664, '52', 107, 'IT', 43.52505000, 10.67961000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q30031083'),
(61252, 'Terrossa', 1753, '34', 107, 'IT', 45.46340000, 11.31860000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61253, 'Terruggia', 1702, '21', 107, 'IT', 45.08199000, 8.44428000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61254, 'Tertenia', 1715, '88', 107, 'IT', 39.69518000, 9.57878000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61255, 'Terzigno', 1669, '72', 107, 'IT', 40.80400000, 14.49309000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61256, 'Terzo', 1702, '21', 107, 'IT', 44.67045000, 8.42164000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61257, 'Terzo d\'Aquileia', 1756, '36', 107, 'IT', 45.79994000, 13.34177000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61258, 'Terzolas', 1725, '32', 107, 'IT', 46.36111000, 10.92587000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61259, 'Terzorio', 1768, '42', 107, 'IT', 43.85232000, 7.89835000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61260, 'Tesero', 1725, '32', 107, 'IT', 46.29184000, 11.50946000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61261, 'Tesimo', 1725, '32', 107, 'IT', 46.56542000, 11.16937000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61262, 'Tessennano', 1678, '62', 107, 'IT', 42.47803000, 11.79101000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18505625'),
(61263, 'Tessera', 1753, '34', 107, 'IT', 45.50241000, 12.32632000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18503713'),
(61264, 'Testico', 1768, '42', 107, 'IT', 44.00553000, 8.02639000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18503713'),
(61265, 'Teti', 1715, '88', 107, 'IT', 40.09737000, 9.11923000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18503713'),
(61266, 'Tetti Neirotti', 1702, '21', 107, 'IT', 45.05338000, 7.54077000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18445309'),
(61267, 'Tettorosso', 1702, '21', 107, 'IT', 44.47284000, 7.34291000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61268, 'Teulada', 1715, '88', 107, 'IT', 38.96658000, 8.77149000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61269, 'Teverola', 1669, '72', 107, 'IT', 40.99561000, 14.20763000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61270, 'Tezze', 1753, '34', 107, 'IT', 45.69011000, 11.70672000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61271, 'Thiene', 1753, '34', 107, 'IT', 45.70880000, 11.47959000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61272, 'Thiesi', 1715, '88', 107, 'IT', 40.52398000, 8.72001000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61273, 'Tiana', 1715, '88', 107, 'IT', 40.06746000, 9.14817000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61274, 'Ticengo', 1705, '25', 107, 'IT', 45.36927000, 9.82766000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61275, 'Ticineto', 1702, '21', 107, 'IT', 45.09627000, 8.55315000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61276, 'Tiggiano', 1688, '75', 107, 'IT', 39.90284000, 18.36501000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61277, 'Tiglieto', 1768, '42', 107, 'IT', 44.52324000, 8.61925000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61278, 'Tigliole', 1702, '21', 107, 'IT', 44.88625000, 8.07663000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61279, 'Tinnura', 1715, '88', 107, 'IT', 40.26916000, 8.54815000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61280, 'Tione degli Abruzzi', 1679, '65', 107, 'IT', 42.20393000, 13.63570000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61281, 'Tione di Trento', 1725, '32', 107, 'IT', 46.03550000, 10.72679000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61282, 'Tirano', 1705, '25', 107, 'IT', 46.21482000, 10.16335000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61283, 'Tires', 1725, '32', 107, 'IT', 46.46817000, 11.52704000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61284, 'Tiriolo', 1703, '78', 107, 'IT', 38.94069000, 16.51046000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61285, 'Tirolo', 1725, '32', 107, 'IT', 46.69102000, 11.15427000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61286, 'Tissi', 1715, '88', 107, 'IT', 40.67832000, 8.56127000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61287, 'Tito', 1706, '77', 107, 'IT', 40.58370000, 15.67621000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61288, 'Tivoli', 1678, '62', 107, 'IT', 41.95952000, 12.80160000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18441166'),
(61289, 'Tivolille Pasquali-Merenzata', 1703, '78', 107, 'IT', 39.27953000, 16.20738000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18430784'),
(61290, 'Tizzano Val Parma', 1773, '45', 107, 'IT', 44.51931000, 10.19819000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18430784'),
(61291, 'Toano', 1773, '45', 107, 'IT', 44.37601000, 10.56311000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18430784'),
(61292, 'Tocco Caudio', 1669, '72', 107, 'IT', 41.12587000, 14.63422000, '2019-10-05 23:07:15', '2019-10-05 23:07:15', 1, 'Q18430784'),
(61293, 'Tocco da Casauria', 1679, '65', 107, 'IT', 42.21203000, 13.91547000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61294, 'Toceno', 1702, '21', 107, 'IT', 46.14468000, 8.46902000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61295, 'Todi', 1683, '55', 107, 'IT', 42.77881000, 12.41202000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61296, 'Toffia', 1678, '62', 107, 'IT', 42.21300000, 12.75500000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61297, 'Toirano', 1768, '42', 107, 'IT', 44.12732000, 8.20725000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61298, 'Tolentino', 1670, '57', 107, 'IT', 43.20918000, 13.28524000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61299, 'Tolfa', 1678, '62', 107, 'IT', 42.15023000, 11.93216000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61300, 'Tollegno', 1702, '21', 107, 'IT', 45.59077000, 8.05089000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61301, 'Tollo', 1679, '65', 107, 'IT', 42.34542000, 14.32360000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61302, 'Tolmezzo', 1756, '36', 107, 'IT', 46.39996000, 13.02051000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61303, 'Tolve', 1706, '77', 107, 'IT', 40.69422000, 16.01627000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18430784'),
(61304, 'Tombelle', 1753, '34', 107, 'IT', 45.39845000, 11.98213000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18503718'),
(61305, 'Tombolo', 1753, '34', 107, 'IT', 45.63916000, 11.82435000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18503718'),
(61306, 'Tonadico', 1725, '32', 107, 'IT', 46.18111000, 11.83939000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18503718'),
(61307, 'Tonara', 1715, '88', 107, 'IT', 40.02465000, 9.17204000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18503718'),
(61308, 'Tonco', 1702, '21', 107, 'IT', 45.02344000, 8.18942000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18503718'),
(61309, 'Tonengo', 1702, '21', 107, 'IT', 45.11765000, 8.00213000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18503718'),
(61310, 'Tonengo-Casale', 1702, '21', 107, 'IT', 45.28609000, 7.93964000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18445382'),
(61311, 'Tonezza del Cimone', 1753, '34', 107, 'IT', 45.85889000, 11.34592000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18445382'),
(61312, 'Tonnara di Bonagia', 1709, '82', 107, 'IT', 38.06309000, 12.59434000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18473000'),
(61313, 'Tonnarella', 1709, '82', 107, 'IT', 38.12539000, 15.11348000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q3993151'),
(61314, 'Tor Lupara', 1678, '62', 107, 'IT', 41.99771000, 12.61814000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2043128'),
(61315, 'Tora', 1669, '72', 107, 'IT', 41.33987000, 14.02399000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18436805'),
(61316, 'Torano Castello', 1703, '78', 107, 'IT', 39.50396000, 16.16103000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18436805'),
(61317, 'Torano Nuovo', 1679, '65', 107, 'IT', 42.82305000, 13.77729000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18436805'),
(61318, 'Torbole Casaglia', 1705, '25', 107, 'IT', 45.51279000, 10.11700000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18436805'),
(61319, 'Torcegno', 1725, '32', 107, 'IT', 46.07431000, 11.44979000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18436805'),
(61320, 'Torchiara', 1669, '72', 107, 'IT', 40.32162000, 15.05370000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18436805'),
(61321, 'Torchiarolo', 1688, '75', 107, 'IT', 40.48349000, 18.05122000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18436805'),
(61322, 'Torchiati', 1669, '72', 107, 'IT', 40.81837000, 14.80165000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18435770'),
(61323, 'Torchione-Moia', 1705, '25', 107, 'IT', 46.15486000, 9.85996000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18494797'),
(61324, 'Torella dei Lombardi', 1669, '72', 107, 'IT', 40.93731000, 15.10784000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18494797'),
(61325, 'Torella del Sannio', 1695, '67', 107, 'IT', 41.63878000, 14.52031000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18494797'),
(61326, 'Torelli-Torrette', 1669, '72', 107, 'IT', 40.90777000, 14.75184000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18435771'),
(61327, 'Torgiano', 1683, '55', 107, 'IT', 43.02761000, 12.44015000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18435771'),
(61328, 'Torgnon', 1716, '23', 107, 'IT', 45.80616000, 7.56980000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18435771'),
(61329, 'Torino', 1702, '21', 107, 'IT', 45.13333000, 7.36667000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61330, 'Torino di Sangro', 1679, '65', 107, 'IT', 42.19100000, 14.54272000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61331, 'Toritto', 1688, '75', 107, 'IT', 40.99774000, 16.67945000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61332, 'Torlino Vimercati', 1705, '25', 107, 'IT', 45.41747000, 9.59476000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61333, 'Tornaco', 1702, '21', 107, 'IT', 45.35651000, 8.71745000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61334, 'Tornareccio', 1679, '65', 107, 'IT', 42.03763000, 14.41525000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61335, 'Tornata', 1705, '25', 107, 'IT', 45.10437000, 10.43078000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61336, 'Tornimparte', 1679, '65', 107, 'IT', 42.29007000, 13.30092000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61337, 'Torno', 1705, '25', 107, 'IT', 45.85593000, 9.11707000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61338, 'Tornolo', 1773, '45', 107, 'IT', 44.48524000, 9.62744000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61339, 'Toro', 1695, '67', 107, 'IT', 41.57313000, 14.76183000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61340, 'Torpè', 1715, '88', 107, 'IT', 40.62780000, 9.67916000, '2019-10-05 23:07:16', '2020-05-01 17:22:57', 1, 'Q18288162'),
(61341, 'Torraca', 1669, '72', 107, 'IT', 40.11099000, 15.63632000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61342, 'Torralba', 1715, '88', 107, 'IT', 40.51296000, 8.76532000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61343, 'Torrazza Coste', 1705, '25', 107, 'IT', 44.97820000, 9.07613000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61344, 'Torrazza Piemonte', 1702, '21', 107, 'IT', 45.21535000, 7.97673000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18288162'),
(61345, 'Torrazza dei Mandelli', 1705, '25', 107, 'IT', 45.56629000, 9.39921000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61346, 'Torrazzo', 1702, '21', 107, 'IT', 45.49874000, 7.95428000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61347, 'Torre Annunziata', 1669, '72', 107, 'IT', 40.75337000, 14.45251000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61348, 'Torre Beretti', 1705, '25', 107, 'IT', 45.05996000, 8.67094000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61349, 'Torre Boldone', 1705, '25', 107, 'IT', 45.71725000, 9.70792000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61350, 'Torre Bormida', 1702, '21', 107, 'IT', 44.56274000, 8.15444000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61351, 'Torre Caietani', 1678, '62', 107, 'IT', 41.78672000, 13.26520000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61352, 'Torre Canavese', 1702, '21', 107, 'IT', 45.39206000, 7.75982000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18491765'),
(61353, 'Torre Caracciolo', 1669, '72', 107, 'IT', 40.87551000, 14.17859000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18437114'),
(61354, 'Torre Colonna-Sperone', 1709, '82', 107, 'IT', 38.02986000, 13.57377000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18468913'),
(61355, 'Torre Melissa', 1703, '78', 107, 'IT', 39.31013000, 17.10660000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2194034'),
(61356, 'Torre Mondovì', 1702, '21', 107, 'IT', 44.35283000, 7.89964000, '2019-10-05 23:07:16', '2020-05-01 17:22:56', 1, 'Q2194034'),
(61357, 'Torre Orsaia', 1669, '72', 107, 'IT', 40.13277000, 15.47250000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2194034'),
(61358, 'Torre Pallavicina', 1705, '25', 107, 'IT', 45.44638000, 9.87706000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2194034'),
(61359, 'Torre Pellice', 1702, '21', 107, 'IT', 44.82102000, 7.21672000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2194034'),
(61360, 'Torre San Giorgio', 1702, '21', 107, 'IT', 44.73574000, 7.52813000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2194034'),
(61361, 'Torre San Giovanni', 1688, '75', 107, 'IT', 39.89001000, 18.11159000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61362, 'Torre San Patrizio', 1670, '57', 107, 'IT', 43.18311000, 13.61113000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61363, 'Torre Santa Susanna', 1688, '75', 107, 'IT', 40.46762000, 17.73864000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61364, 'Torre d\'Arese', 1705, '25', 107, 'IT', 45.24277000, 9.31735000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61365, 'Torre d\'Isola', 1705, '25', 107, 'IT', 45.21796000, 9.07651000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61366, 'Torre de\' Busi', 1705, '25', 107, 'IT', 45.77408000, 9.48025000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61367, 'Torre de\' Negri', 1705, '25', 107, 'IT', 45.14966000, 9.33456000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61368, 'Torre de\' Passeri', 1679, '65', 107, 'IT', 42.24324000, 13.93330000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61369, 'Torre de\' Picenardi', 1705, '25', 107, 'IT', 45.14317000, 10.28803000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61370, 'Torre de\' Roveri', 1705, '25', 107, 'IT', 45.69718000, 9.76326000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61371, 'Torre del Greco', 1669, '72', 107, 'IT', 40.78931000, 14.36806000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q2263281'),
(61372, 'Torre del Lago Puccini', 1664, '52', 107, 'IT', 43.82887000, 10.28912000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q597435'),
(61373, 'Torre del Mangano', 1705, '25', 107, 'IT', 45.25208000, 9.13042000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61374, 'Torre di Mosto', 1753, '34', 107, 'IT', 45.68629000, 12.70027000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61375, 'Torre di Ruggiero', 1703, '78', 107, 'IT', 38.65293000, 16.37211000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61376, 'Torre di Santa Maria', 1705, '25', 107, 'IT', 46.23356000, 9.85228000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61377, 'Torre le Nocelle', 1669, '72', 107, 'IT', 41.02312000, 14.90934000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61378, 'Torreano', 1756, '36', 107, 'IT', 46.12786000, 13.42933000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61379, 'Torrebelvicino', 1753, '34', 107, 'IT', 45.71583000, 11.31869000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61380, 'Torrebruna', 1679, '65', 107, 'IT', 41.86742000, 14.54148000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61381, 'Torrecuso', 1669, '72', 107, 'IT', 41.18582000, 14.68126000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61382, 'Torreglia', 1753, '34', 107, 'IT', 45.33617000, 11.73608000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61383, 'Torregrotta', 1709, '82', 107, 'IT', 38.20262000, 15.35010000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61384, 'Torremaggiore', 1688, '75', 107, 'IT', 41.68894000, 15.29408000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18493670'),
(61385, 'Torrenieri', 1664, '52', 107, 'IT', 43.08566000, 11.54839000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q742209'),
(61386, 'Torrenova', 1709, '82', 107, 'IT', 38.09246000, 14.67917000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q742209'),
(61387, 'Torreselle', 1753, '34', 107, 'IT', 45.63026000, 12.02250000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18498995'),
(61388, 'Torresina', 1702, '21', 107, 'IT', 44.43364000, 8.03674000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18498995'),
(61389, 'Torretta', 1709, '82', 107, 'IT', 38.13036000, 13.23549000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18498995'),
(61390, 'Torretta', 1703, '78', 107, 'IT', 39.45358000, 17.03632000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18431628'),
(61391, 'Torretta-Scalzapecora', 1669, '72', 107, 'IT', 40.91015000, 14.13456000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18437115'),
(61392, 'Torrevecchia', 1679, '65', 107, 'IT', 42.38274000, 14.21331000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18425966'),
(61393, 'Torrevecchia Pia', 1705, '25', 107, 'IT', 45.28197000, 9.29635000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18425966'),
(61394, 'Torri del Benaco', 1753, '34', 107, 'IT', 45.61013000, 10.68669000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18425966'),
(61395, 'Torri di Quartesolo', 1753, '34', 107, 'IT', 45.51901000, 11.62469000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18425966'),
(61396, 'Torri in Sabina', 1678, '62', 107, 'IT', 42.35057000, 12.64157000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18425966'),
(61397, 'Torriana', 1773, '45', 107, 'IT', 43.98485000, 12.38541000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61398, 'Torrice', 1678, '62', 107, 'IT', 41.63374000, 13.40375000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61399, 'Torricella', 1679, '65', 107, 'IT', 42.65904000, 13.65719000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61400, 'Torricella', 1688, '75', 107, 'IT', 40.35509000, 17.49846000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61401, 'Torricella Peligna', 1679, '65', 107, 'IT', 42.02397000, 14.25854000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61402, 'Torricella Verzate', 1705, '25', 107, 'IT', 45.01996000, 9.17356000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61403, 'Torricella del Pizzo', 1705, '25', 107, 'IT', 45.02023000, 10.29497000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61404, 'Torricella in Sabina', 1678, '62', 107, 'IT', 42.26252000, 12.87170000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61405, 'Torriglia', 1768, '42', 107, 'IT', 44.51755000, 9.15811000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61406, 'Torrile', 1773, '45', 107, 'IT', 44.92522000, 10.32381000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q111966'),
(61407, 'Torrion Quartara', 1702, '21', 107, 'IT', 45.41819000, 8.61472000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18441845'),
(61408, 'Torrioni', 1669, '72', 107, 'IT', 41.03410000, 14.81351000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18441845'),
(61409, 'Torrita Tiberina', 1678, '62', 107, 'IT', 42.23821000, 12.61633000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18441845'),
(61410, 'Torrita di Siena', 1664, '52', 107, 'IT', 43.17245000, 11.78376000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18441845'),
(61411, 'Tortolì', 1715, '88', 107, 'IT', 39.92626000, 9.65569000, '2019-10-05 23:07:16', '2020-05-01 17:22:57', 1, 'Q18441845'),
(61412, 'Tortona', 1702, '21', 107, 'IT', 44.89784000, 8.86374000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18441845'),
(61413, 'Tortora', 1703, '78', 107, 'IT', 39.94130000, 15.80518000, '2019-10-05 23:07:16', '2019-10-05 23:07:16', 1, 'Q18441845'),
(61414, 'Tortora Marina', 1703, '78', 107, 'IT', 39.92293000, 15.76943000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18430853'),
(61415, 'Tortorella', 1669, '72', 107, 'IT', 40.14236000, 15.60625000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18430853'),
(61416, 'Tortoreto', 1679, '65', 107, 'IT', 42.80371000, 13.91346000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18430853'),
(61417, 'Tortoreto Lido', 1679, '65', 107, 'IT', 42.79956000, 13.94205000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18428585'),
(61418, 'Tortorici', 1709, '82', 107, 'IT', 38.02973000, 14.82212000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18428585'),
(61419, 'Torvaianica', 1678, '62', 107, 'IT', 41.62112000, 12.46197000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q2583824'),
(61420, 'Torvaianica Alta', 1678, '62', 107, 'IT', 41.63162000, 12.49765000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455317'),
(61421, 'Torviscosa', 1756, '36', 107, 'IT', 45.82337000, 13.28050000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455317'),
(61422, 'Toscanella', 1773, '45', 107, 'IT', 44.38248000, 11.63965000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18438761'),
(61423, 'Toscolano Maderno', 1705, '25', 107, 'IT', 45.63977000, 10.60760000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18438761'),
(61424, 'Tossicia', 1679, '65', 107, 'IT', 42.54472000, 13.64595000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18438761'),
(61425, 'Tovo San Giacomo', 1768, '42', 107, 'IT', 44.17593000, 8.27045000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18438761'),
(61426, 'Tovo di Sant\'Agata', 1705, '25', 107, 'IT', 46.24505000, 10.24670000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18438761'),
(61427, 'Trabia', 1709, '82', 107, 'IT', 38.00420000, 13.63393000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18438761'),
(61428, 'Tradate', 1705, '25', 107, 'IT', 45.70843000, 8.90763000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18438761'),
(61429, 'Tragliatella Campitello', 1678, '62', 107, 'IT', 42.01334000, 12.25200000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61430, 'Tramatza', 1715, '88', 107, 'IT', 40.00292000, 8.64944000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61431, 'Trambileno', 1725, '32', 107, 'IT', 45.86890000, 11.07338000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61432, 'Tramonti', 1669, '72', 107, 'IT', 40.69154000, 14.64490000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61433, 'Tramonti di Sopra', 1756, '36', 107, 'IT', 46.31023000, 12.78994000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61434, 'Tramonti di Sotto', 1756, '36', 107, 'IT', 46.28475000, 12.79644000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61435, 'Tramutola', 1706, '77', 107, 'IT', 40.31858000, 15.78753000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61436, 'Trana', 1702, '21', 107, 'IT', 45.03862000, 7.42100000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18455320'),
(61437, 'Trani', 1686, 'BT', 107, 'IT', 41.27733000, 16.41011000, '2019-10-05 23:07:17', '2022-11-06 17:45:20', 1, 'Q18455320'),
(61438, 'Transacqua', 1725, '32', 107, 'IT', 46.17367000, 11.83279000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q327418'),
(61439, 'Traona', 1705, '25', 107, 'IT', 46.14737000, 9.53256000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q327418'),
(61440, 'Trapani', 1709, '82', 107, 'IT', 37.83333000, 12.66667000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q16288'),
(61441, 'Trappeto', 1709, '82', 107, 'IT', 38.06875000, 13.03794000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q16288'),
(61442, 'Trarego', 1702, '21', 107, 'IT', 46.03435000, 8.66998000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18446332'),
(61443, 'Trarivi', 1773, '45', 107, 'IT', 43.93672000, 12.54400000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61444, 'Trasacco', 1679, '65', 107, 'IT', 41.95742000, 13.53270000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61445, 'Trasaghis', 1756, '36', 107, 'IT', 46.28164000, 13.07545000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61446, 'Trasquera', 1702, '21', 107, 'IT', 46.21338000, 8.21271000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61447, 'Tratalias', 1715, '88', 107, 'IT', 39.10347000, 8.57858000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61448, 'Trausella', 1702, '21', 107, 'IT', 45.48996000, 7.76312000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61449, 'Travacò Siccomario', 1705, '25', 107, 'IT', 45.14906000, 9.15965000, '2019-10-05 23:07:17', '2020-05-01 17:22:56', 1, 'Q18448043'),
(61450, 'Travagliato', 1705, '25', 107, 'IT', 45.52391000, 10.08013000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61451, 'Travedona Monate', 1705, '25', 107, 'IT', 45.80427000, 8.67143000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61452, 'Traversella', 1702, '21', 107, 'IT', 45.50907000, 7.74938000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61453, 'Traversetolo', 1773, '45', 107, 'IT', 44.64244000, 10.38036000, '2019-10-05 23:07:17', '2019-10-05 23:07:17', 1, 'Q18448043'),
(61454, 'Traves', 1702, '21', 107, 'IT', 45.26795000, 7.43081000, '2019-10-05 23:07:18', '2019-10-05 23:07:18', 1, 'Q18448043'),
(61455, 'Travesio', 1756, '36', 107, 'IT', 46.19667000, 12.86740000, '2019-10-05 23:07:18', '2019-10-05 23:07:18', 1, 'Q18448043'),
(61456, 'Travo', 1773, '45', 107, 'IT', 44.86321000, 9.54430000, '2019-10-05 23:07:18', '2019-10-05 23:07:18', 1, 'Q18448043'),
(61457, 'Tre Fontane', 1709, '82', 107, 'IT', 37.56989000, 12.72423000, '2019-10-05 23:07:18', '2019-10-05 23:07:18', 1, 'Q18473008'),
(61458, 'Trebaseleghe', 1753, '34', 107, 'IT', 45.59295000, 12.04392000, '2019-10-05 23:07:18', '2019-10-05 23:07:18', 1, 'Q18473008');

