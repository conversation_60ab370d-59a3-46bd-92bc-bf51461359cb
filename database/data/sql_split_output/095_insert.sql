INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(47803, 'Villers-en-Cauchies', 4828, 'HDF', 75, 'FR', 50.22574000, 3.40362000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1389265'),
(47804, 'Villers-la-Montagne', 4820, 'GES', 75, 'FR', 49.47175000, 5.82522000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1389265'),
(47805, 'Villers-le-Lac', 4825, 'BFC', 75, 'FR', 47.06319000, 6.66699000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1389265'),
(47806, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 4820, 'GES', 75, 'FR', 48.67333000, 6.15283000, '2019-10-05 22:49:11', '2020-05-01 17:22:45', 1, 'Q1414358'),
(47807, 'Villers-sous-<PERSON>-Leu', 4828, 'HDF', 75, 'FR', 49.21235000, 2.39485000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1332108'),
(47808, 'Villers-sur-Coudun', 4828, 'HDF', 75, 'FR', 49.48308000, 2.80457000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1332108'),
(47809, 'Villers-sur-Mer', 4804, 'NOR', 75, 'FR', 49.32264000, 0.00027000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q323598'),
(47810, 'Villers-Écalles', 4804, 'NOR', 75, 'FR', 49.54136000, 0.91942000, '2019-10-05 22:49:11', '2020-05-01 17:22:45', 1, 'Q323598'),
(47811, 'Villersexel', 4825, 'BFC', 75, 'FR', 47.55070000, 6.43273000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q989788'),
(47812, 'Villerupt', 4820, 'GES', 75, 'FR', 49.46715000, 5.93202000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q733313'),
(47813, 'Villeréal', 4795, 'NAQ', 75, 'FR', 44.63631000, 0.74326000, '2019-10-05 22:49:11', '2020-05-01 17:22:46', 1, 'Q1104188'),
(47814, 'Villes-sur-Auzon', 4812, 'PAC', 75, 'FR', 44.05669000, 5.23430000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q636415'),
(47815, 'Villetaneuse', 4796, 'IDF', 75, 'FR', 48.95833000, 2.34167000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q275353'),
(47816, 'Villetelle', 4799, 'OCC', 75, 'FR', 43.73105000, 4.13658000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q275353'),
(47817, 'Villette-de-Vienne', 4798, 'ARA', 75, 'FR', 45.59049000, 4.91528000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1066562'),
(47818, 'Villeurbanne', 4798, 'ARA', 75, 'FR', 45.76601000, 4.87950000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47819, 'Villevaudé', 4796, 'IDF', 75, 'FR', 48.91751000, 2.65228000, '2019-10-05 22:49:11', '2020-05-01 17:22:43', 1, 'Q582'),
(47820, 'Villeveyrac', 4799, 'OCC', 75, 'FR', 43.50095000, 3.60723000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47821, 'Villevieille', 4799, 'OCC', 75, 'FR', 43.78795000, 4.09756000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47822, 'Villevocance', 4798, 'ARA', 75, 'FR', 45.22481000, 4.58827000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47823, 'Villevêque', 4802, 'PDL', 75, 'FR', 47.56095000, -0.42383000, '2019-10-05 22:49:11', '2020-05-01 17:22:46', 1, 'Q582'),
(47824, 'Villey-Saint-Étienne', 4820, 'GES', 75, 'FR', 48.73254000, 5.97851000, '2019-10-05 22:49:11', '2020-05-01 17:22:45', 1, 'Q582'),
(47825, 'Villiers-Saint-Fréderic', 4796, 'IDF', 75, 'FR', 48.81667000, 1.88333000, '2019-10-05 22:49:11', '2020-05-01 17:22:43', 1, 'Q582'),
(47826, 'Villiers-Saint-Georges', 4796, 'IDF', 75, 'FR', 48.64998000, 3.40754000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47827, 'Villiers-en-Lieu', 4820, 'GES', 75, 'FR', 48.66785000, 4.89755000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47828, 'Villiers-en-Plaine', 4795, 'NAQ', 75, 'FR', 46.40895000, -0.53756000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47829, 'Villiers-le-Bel', 4796, 'IDF', 75, 'FR', 49.00875000, 2.39819000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q582'),
(47830, 'Villiers-le-Bâcle', 4796, 'IDF', 75, 'FR', 48.72819000, 2.11925000, '2019-10-05 22:49:11', '2020-05-01 17:22:43', 1, 'Q963534'),
(47831, 'Villiers-le-Morhier', 4818, 'CVL', 75, 'FR', 48.62018000, 1.56349000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q963534'),
(47832, 'Villiers-sur-Loir', 4818, 'CVL', 75, 'FR', 47.80507000, 0.99774000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1111803'),
(47833, 'Villiers-sur-Marne', 4796, 'IDF', 75, 'FR', 48.83100000, 2.54844000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q258636'),
(47834, 'Villiers-sur-Morin', 4796, 'IDF', 75, 'FR', 48.86099000, 2.87773000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q271947'),
(47835, 'Villiers-sur-Orge', 4796, 'IDF', 75, 'FR', 48.65953000, 2.30002000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q265788'),
(47836, 'Villié-Morgon', 4798, 'ARA', 75, 'FR', 46.16227000, 4.68069000, '2019-10-05 22:49:11', '2020-05-01 17:22:43', 1, 'Q265788'),
(47837, 'Villé', 4820, 'GES', 75, 'FR', 48.34270000, 7.30260000, '2019-10-05 22:49:11', '2020-05-01 17:22:45', 1, 'Q21606'),
(47838, 'Vimines', 4798, 'ARA', 75, 'FR', 45.54640000, 5.86523000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q21606'),
(47839, 'Vimory', 4818, 'CVL', 75, 'FR', 47.94786000, 2.68701000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q21606'),
(47840, 'Vimoutiers', 4804, 'NOR', 75, 'FR', 48.92772000, 0.19835000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q729239'),
(47841, 'Vimy', 4828, 'HDF', 75, 'FR', 50.37243000, 2.81034000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q318759'),
(47842, 'Vinassan', 4799, 'OCC', 75, 'FR', 43.20443000, 3.07463000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q194040'),
(47843, 'Vinay', 4798, 'ARA', 75, 'FR', 45.20832000, 5.40646000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q194040'),
(47844, 'Vincennes', 4796, 'IDF', 75, 'FR', 48.84860000, 2.43769000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q193819'),
(47845, 'Vincey', 4820, 'GES', 75, 'FR', 48.33749000, 6.33134000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q907079'),
(47846, 'Vineuil', 4818, 'CVL', 75, 'FR', 47.58380000, 1.37601000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q907079'),
(47847, 'Vineuil-Saint-Firmin', 4828, 'HDF', 75, 'FR', 49.20024000, 2.49567000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q752096'),
(47848, 'Vinneuf', 4825, 'BFC', 75, 'FR', 48.34795000, 3.14013000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q752096'),
(47849, 'Vinon-sur-Verdon', 4812, 'PAC', 75, 'FR', 43.72484000, 5.81168000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q221832'),
(47850, 'Vinsobres', 4798, 'ARA', 75, 'FR', 44.33328000, 5.06204000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q221832'),
(47851, 'Vinça', 4799, 'OCC', 75, 'FR', 42.64486000, 2.52830000, '2019-10-05 22:49:11', '2020-05-01 17:22:46', 1, 'Q221832'),
(47852, 'Violaines', 4828, 'HDF', 75, 'FR', 50.54160000, 2.78860000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q221832'),
(47853, 'Violay', 4798, 'ARA', 75, 'FR', 45.85335000, 4.35951000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q666296'),
(47854, 'Violès', 4812, 'PAC', 75, 'FR', 44.16176000, 4.95483000, '2019-10-05 22:49:11', '2020-05-01 17:22:47', 1, '********'),
(47855, 'Vion', 4802, 'PDL', 75, 'FR', 47.81923000, -0.23916000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, '********'),
(47856, 'Virazeil', 4795, 'NAQ', 75, 'FR', 44.50705000, 0.22177000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, '********'),
(47857, 'Vire', 4804, 'NOR', 75, 'FR', 48.83849000, -0.88929000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q220684'),
(47858, 'Vireux-Molhain', 4820, 'GES', 75, 'FR', 50.07874000, 4.72426000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q220684'),
(47859, 'Vireux-Wallerand', 4820, 'GES', 75, 'FR', 50.08196000, 4.73017000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q220684'),
(47860, 'Virey-le-Grand', 4825, 'BFC', 75, 'FR', 46.83333000, 4.86667000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q220684'),
(47861, 'Viriat', 4798, 'ARA', 75, 'FR', 46.25484000, 5.21567000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q838679'),
(47862, 'Virieu', 4798, 'ARA', 75, 'FR', 45.48404000, 5.47586000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q838679'),
(47863, 'Virieu-le-Grand', 4798, 'ARA', 75, 'FR', 45.84766000, 5.65146000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q838679'),
(47864, 'Viriville', 4798, 'ARA', 75, 'FR', 45.31579000, 5.20376000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q838679'),
(47865, 'Viroflay', 4796, 'IDF', 75, 'FR', 48.80078000, 2.16181000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q83432'),
(47866, 'Viry', 4798, 'ARA', 75, 'FR', 46.11291000, 6.03808000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q83432'),
(47867, 'Viry-Châtillon', 4796, 'IDF', 75, 'FR', 48.67211000, 2.39318000, '2019-10-05 22:49:11', '2020-05-01 17:22:43', 1, 'Q83432'),
(47868, 'Viry-Noureuil', 4828, 'HDF', 75, 'FR', 49.63214000, 3.24322000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q83432'),
(47869, 'Visan', 4812, 'PAC', 75, 'FR', 44.31468000, 4.95033000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1092716'),
(47870, 'Vitrolles', 4812, 'PAC', 75, 'FR', 43.46000000, 5.24861000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1092716'),
(47871, 'Vitry-aux-Loges', 4818, 'CVL', 75, 'FR', 47.93333000, 2.26667000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1092716'),
(47872, 'Vitry-en-Artois', 4828, 'HDF', 75, 'FR', 50.32660000, 2.97999000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q1092716'),
(47873, 'Vitry-le-François', 4820, 'GES', 75, 'FR', 48.72472000, 4.58439000, '2019-10-05 22:49:11', '2020-05-01 17:22:45', 1, 'Q623374'),
(47874, 'Vitry-sur-Orne', 4820, 'GES', 75, 'FR', 49.26595000, 6.11074000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q22024'),
(47875, 'Vitry-sur-Seine', 4796, 'IDF', 75, 'FR', 48.78716000, 2.40332000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q72248'),
(47876, 'Vitré', 4807, 'BRE', 75, 'FR', 48.11776000, -1.20577000, '2019-10-05 22:49:11', '2020-05-01 17:22:44', 1, 'Q72248'),
(47877, 'Vitteaux', 4825, 'BFC', 75, 'FR', 47.39732000, 4.54190000, '2019-10-05 22:49:11', '2019-10-05 22:49:11', 1, 'Q380877'),
(47878, 'Vittel', 4820, 'GES', 75, 'FR', 48.20085000, 5.94843000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q453309'),
(47879, 'Viuz-en-Sallaz', 4798, 'ARA', 75, 'FR', 46.14673000, 6.40781000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q453309'),
(47880, 'Viuz-la-Chiésaz', 4798, 'ARA', 75, 'FR', 45.81203000, 6.06645000, '2019-10-05 22:49:12', '2020-05-01 17:22:43', 1, 'Q912314'),
(47881, 'Vivier-au-Court', 4820, 'GES', 75, 'FR', 49.73326000, 4.82939000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q912314'),
(47882, 'Viviers', 4798, 'ARA', 75, 'FR', 44.48280000, 4.68895000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q912314'),
(47883, 'Viviers-du-Lac', 4798, 'ARA', 75, 'FR', 45.65000000, 5.90000000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q912314'),
(47884, 'Viviers-lès-Montagnes', 4799, 'OCC', 75, 'FR', 43.55474000, 2.17672000, '2019-10-05 22:49:12', '2020-05-01 17:22:46', 1, 'Q912314'),
(47885, 'Viviez', 4799, 'OCC', 75, 'FR', 44.55678000, 2.21649000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q912314'),
(47886, 'Vivonne', 4795, 'NAQ', 75, 'FR', 46.42953000, 0.26443000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, '********'),
(47887, 'Vivy', 4802, 'PDL', 75, 'FR', 47.32648000, -0.05531000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, '********'),
(47888, 'Vix', 4802, 'PDL', 75, 'FR', 46.36456000, -0.86072000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, '********'),
(47889, 'Vizille', 4798, 'ARA', 75, 'FR', 45.07819000, 5.77074000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q636262'),
(47890, 'Voglans', 4798, 'ARA', 75, 'FR', 45.61868000, 5.88798000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q636262'),
(47891, 'Void-Vacon', 4820, 'GES', 75, 'FR', 48.68333000, 5.61667000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q753253'),
(47892, 'Voiron', 4798, 'ARA', 75, 'FR', 45.36471000, 5.58560000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q238205'),
(47893, 'Voisenon', 4796, 'IDF', 75, 'FR', 48.57170000, 2.66480000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q238205'),
(47894, 'Voisins-le-Bretonneux', 4796, 'IDF', 75, 'FR', 48.75793000, 2.05137000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q83450'),
(47895, 'Volgelsheim', 4820, 'GES', 75, 'FR', 48.01462000, 7.55456000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q148934'),
(47896, 'Volmerange-les-Mines', 4820, 'GES', 75, 'FR', 49.44326000, 6.08062000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q148934'),
(47897, 'Volonne', 4812, 'PAC', 75, 'FR', 44.11039000, 6.01424000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q326027'),
(47898, 'Volstroff', 4820, 'GES', 75, 'FR', 49.31135000, 6.25976000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q326027'),
(47899, 'Volvic', 4798, 'ARA', 75, 'FR', 45.87196000, 3.03832000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q623376'),
(47900, 'Volx', 4812, 'PAC', 75, 'FR', 43.87787000, 5.84148000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q1016902'),
(47901, 'Vonnas', 4798, 'ARA', 75, 'FR', 46.21727000, 4.99246000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q243104'),
(47902, 'Voreppe', 4798, 'ARA', 75, 'FR', 45.29484000, 5.63192000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q752466'),
(47903, 'Vorey', 4798, 'ARA', 75, 'FR', 45.18638000, 3.90991000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q752466'),
(47904, 'Vosges', 4820, 'GES', 75, 'FR', 48.19161000, 6.40533000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q3105'),
(47905, 'Vougy', 4798, 'ARA', 75, 'FR', 46.10435000, 4.11771000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q3105'),
(47906, 'Vouillé', 4795, 'NAQ', 75, 'FR', 46.64011000, 0.16778000, '2019-10-05 22:49:12', '2020-05-01 17:22:46', 1, 'Q3105'),
(47907, 'Voujeaucourt', 4825, 'BFC', 75, 'FR', 47.47513000, 6.77431000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q3105'),
(47908, 'Voulangis', 4796, 'IDF', 75, 'FR', 48.85252000, 2.89558000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q3105'),
(47909, 'Voulx', 4796, 'IDF', 75, 'FR', 48.28204000, 2.96747000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q3105'),
(47910, 'Vouneuil-sous-Biard', 4795, 'NAQ', 75, 'FR', 46.57387000, 0.26988000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q3105'),
(47911, 'Vouneuil-sur-Vienne', 4795, 'NAQ', 75, 'FR', 46.71793000, 0.53936000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q1470636'),
(47912, 'Vourey', 4798, 'ARA', 75, 'FR', 45.32180000, 5.51931000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q1470636'),
(47913, 'Vourles', 4798, 'ARA', 75, 'FR', 45.65878000, 4.77325000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q1617185'),
(47914, 'Voutezac', 4795, 'NAQ', 75, 'FR', 45.29214000, 1.43721000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q1617185'),
(47915, 'Vouvray', 4818, 'CVL', 75, 'FR', 47.41087000, 0.79892000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q1617185'),
(47916, 'Vouziers', 4820, 'GES', 75, 'FR', 49.39791000, 4.70120000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q209821'),
(47917, 'Vouzon', 4818, 'CVL', 75, 'FR', 47.64515000, 2.05609000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, '********'),
(47918, 'Voves', 4818, 'CVL', 75, 'FR', 48.27138000, 1.62583000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q470959'),
(47919, 'Vred', 4828, 'HDF', 75, 'FR', 50.39455000, 3.23029000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q654381'),
(47920, 'Vrigne-aux-Bois', 4820, 'GES', 75, 'FR', 49.73716000, 4.85567000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q654381'),
(47921, 'Vue', 4802, 'PDL', 75, 'FR', 47.19953000, -1.87750000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q654381'),
(47922, 'Vulaines-sur-Seine', 4796, 'IDF', 75, 'FR', 48.43186000, 2.76476000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q654381'),
(47923, 'Vélines', 4795, 'NAQ', 75, 'FR', 44.85000000, 0.11667000, '2019-10-05 22:49:12', '2020-05-01 17:22:46', 1, 'Q732674'),
(47924, 'Vélizy-Villacoublay', 4796, 'IDF', 75, 'FR', 48.78198000, 2.19395000, '2019-10-05 22:49:12', '2020-05-01 17:22:43', 1, 'Q387447'),
(47925, 'Vémars', 4796, 'IDF', 75, 'FR', 49.06942000, 2.56643000, '2019-10-05 22:49:12', '2020-05-01 17:22:43', 1, 'Q387447'),
(47926, 'Vénissieux', 4798, 'ARA', 75, 'FR', 45.69706000, 4.88593000, '2019-10-05 22:49:12', '2020-05-01 17:22:43', 1, 'Q13598'),
(47927, 'Vénéjan', 4799, 'OCC', 75, 'FR', 44.19729000, 4.65422000, '2019-10-05 22:49:12', '2020-05-01 17:22:46', 1, 'Q13598'),
(47928, 'Véretz', 4818, 'CVL', 75, 'FR', 47.35706000, 0.80575000, '2019-10-05 22:49:12', '2020-05-01 17:22:44', 1, 'Q13598'),
(47929, 'Vérines', 4795, 'NAQ', 75, 'FR', 46.19372000, -0.96683000, '2019-10-05 22:49:12', '2020-05-01 17:22:46', 1, 'Q13598'),
(47930, 'Véron', 4825, 'BFC', 75, 'FR', 48.12853000, 3.30773000, '2019-10-05 22:49:12', '2020-05-01 17:22:44', 1, 'Q13598'),
(47931, 'Vétraz-Monthoux', 4798, 'ARA', 75, 'FR', 46.17430000, 6.25852000, '2019-10-05 22:49:12', '2020-05-01 17:22:43', 1, 'Q659153'),
(47932, 'Vézelise', 4820, 'GES', 75, 'FR', 48.48748000, 6.08825000, '2019-10-05 22:49:12', '2020-05-01 17:22:45', 1, 'Q733184'),
(47933, 'Vézénobres', 4799, 'OCC', 75, 'FR', 44.05130000, 4.13775000, '2019-10-05 22:49:12', '2020-05-01 17:22:46', 1, 'Q733184'),
(47934, 'Vœuil-et-Giget', 4795, 'NAQ', 75, 'FR', 45.58333000, 0.15000000, '2019-10-05 22:49:12', '2020-05-01 17:22:46', 1, 'Q681883'),
(47935, 'Wahagnies', 4828, 'HDF', 75, 'FR', 50.48665000, 3.03448000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q681883'),
(47936, 'Wailly', 4828, 'HDF', 75, 'FR', 50.52287000, 2.06792000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q681883'),
(47937, 'Walbourg', 4820, 'GES', 75, 'FR', 48.88628000, 7.78828000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q21608'),
(47938, 'Waldighofen', 4820, 'GES', 75, 'FR', 47.55045000, 7.31512000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q587939'),
(47939, 'Walincourt-Selvigny', 4828, 'HDF', 75, 'FR', 50.06667000, 3.33333000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q681699'),
(47940, 'Wallers', 4828, 'HDF', 75, 'FR', 50.37432000, 3.39188000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q681699'),
(47941, 'Walscheid', 4820, 'GES', 75, 'FR', 48.65308000, 7.14998000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q681699'),
(47942, 'Wambrechies', 4828, 'HDF', 75, 'FR', 50.68276000, 3.04784000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q979699'),
(47943, 'Wandignies-Hamage', 4828, 'HDF', 75, 'FR', 50.39609000, 3.31450000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q977851'),
(47944, 'Wangenbourg-Engenthal', 4820, 'GES', 75, 'FR', 48.62805000, 7.30471000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q977851'),
(47945, 'Warcq', 4820, 'GES', 75, 'FR', 49.77517000, 4.68175000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q977851'),
(47946, 'Wardrecques', 4828, 'HDF', 75, 'FR', 50.71108000, 2.34483000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q991477'),
(47947, 'Wargnies-le-Grand', 4828, 'HDF', 75, 'FR', 50.30751000, 3.66038000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q687758'),
(47948, 'Warhem', 4828, 'HDF', 75, 'FR', 50.97592000, 2.49303000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q687758'),
(47949, 'Warluis', 4828, 'HDF', 75, 'FR', 49.38874000, 2.14220000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q687758'),
(47950, 'Warmeriville', 4820, 'GES', 75, 'FR', 49.35227000, 4.21882000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q687758'),
(47951, 'Wasquehal', 4828, 'HDF', 75, 'FR', 50.67043000, 3.13382000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q681563'),
(47952, 'Wasselonne', 4820, 'GES', 75, 'FR', 48.63779000, 7.44506000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q22413'),
(47953, 'Wassigny', 4828, 'HDF', 75, 'FR', 50.01334000, 3.59988000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q22413'),
(47954, 'Wassy', 4820, 'GES', 75, 'FR', 48.49811000, 4.94775000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q728872'),
(47955, 'Watten', 4828, 'HDF', 75, 'FR', 50.83685000, 2.21346000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q728872'),
(47956, 'Wattignies', 4828, 'HDF', 75, 'FR', 50.58639000, 3.04394000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q1102131'),
(47957, 'Wattrelos', 4828, 'HDF', 75, 'FR', 50.70118000, 3.21812000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q274290'),
(47958, 'Wattwiller', 4820, 'GES', 75, 'FR', 47.83649000, 7.17785000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q545447'),
(47959, 'Wavignies', 4828, 'HDF', 75, 'FR', 49.54748000, 2.36032000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q545447'),
(47960, 'Wavrechain-sous-Denain', 4828, 'HDF', 75, 'FR', 50.33224000, 3.41252000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q545447'),
(47961, 'Wavrin', 4828, 'HDF', 75, 'FR', 50.57386000, 2.93630000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q545447'),
(47962, 'Waziers', 4828, 'HDF', 75, 'FR', 50.38717000, 3.11490000, '2019-10-05 22:49:12', '2019-10-05 22:49:12', 1, 'Q545447'),
(47963, 'Weitbruch', 4820, 'GES', 75, 'FR', 48.75455000, 7.77935000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22904'),
(47964, 'Wervicq-Sud', 4828, 'HDF', 75, 'FR', 50.77450000, 3.04207000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q539564'),
(47965, 'Westhoffen', 4820, 'GES', 75, 'FR', 48.60352000, 7.44289000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q21562'),
(47966, 'Wettolsheim', 4820, 'GES', 75, 'FR', 48.05755000, 7.29844000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q635229'),
(47967, 'Weyersheim', 4820, 'GES', 75, 'FR', 48.71622000, 7.80127000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q635229'),
(47968, 'Widensolen', 4820, 'GES', 75, 'FR', 48.06377000, 7.48015000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q635229'),
(47969, 'Wiesviller', 4820, 'GES', 75, 'FR', 49.08095000, 7.16415000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q635229'),
(47970, 'Wignehies', 4828, 'HDF', 75, 'FR', 50.01550000, 4.00913000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q1066759'),
(47971, 'Wihr-au-Val', 4820, 'GES', 75, 'FR', 48.05196000, 7.20493000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q635301'),
(47972, 'Willems', 4828, 'HDF', 75, 'FR', 50.63206000, 3.23840000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q635301'),
(47973, 'Willerwald', 4820, 'GES', 75, 'FR', 49.02481000, 7.03726000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q635301'),
(47974, 'Wimereux', 4828, 'HDF', 75, 'FR', 50.76963000, 1.61139000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q383369'),
(47975, 'Wimille', 4828, 'HDF', 75, 'FR', 50.76418000, 1.63137000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q874994'),
(47976, 'Wimmenau', 4820, 'GES', 75, 'FR', 48.91217000, 7.42189000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q874994'),
(47977, 'Wingen-sur-Moder', 4820, 'GES', 75, 'FR', 48.91900000, 7.37955000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22875'),
(47978, 'Wingersheim', 4820, 'GES', 75, 'FR', 48.72149000, 7.63464000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q21349'),
(47979, 'Wingles', 4828, 'HDF', 75, 'FR', 50.49382000, 2.85500000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q21349'),
(47980, 'Winnezeele', 4828, 'HDF', 75, 'FR', 50.84100000, 2.55118000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q21349'),
(47981, 'Wintzenheim', 4820, 'GES', 75, 'FR', 48.07269000, 7.29072000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q495481'),
(47982, 'Wisches', 4820, 'GES', 75, 'FR', 48.50881000, 7.26814000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q495481'),
(47983, 'Wissant', 4828, 'HDF', 75, 'FR', 50.88530000, 1.66263000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q316447'),
(47984, 'Wissembourg', 4820, 'GES', 75, 'FR', 49.03708000, 7.94548000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22636'),
(47985, 'Wissous', 4796, 'IDF', 75, 'FR', 48.73352000, 2.32338000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22636'),
(47986, 'Witry-lès-Reims', 4820, 'GES', 75, 'FR', 49.29162000, 4.11921000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q22636'),
(47987, 'Wittelsheim', 4820, 'GES', 75, 'FR', 47.80947000, 7.24154000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22636'),
(47988, 'Wittenheim', 4820, 'GES', 75, 'FR', 47.81090000, 7.32756000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q659533'),
(47989, 'Wittisheim', 4820, 'GES', 75, 'FR', 48.26451000, 7.58683000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q659533'),
(47990, 'Wizernes', 4828, 'HDF', 75, 'FR', 50.71170000, 2.24316000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q659533'),
(47991, 'Woerth', 4820, 'GES', 75, 'FR', 48.93953000, 7.74279000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22762'),
(47992, 'Woincourt', 4828, 'HDF', 75, 'FR', 50.06630000, 1.53676000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q740259'),
(47993, 'Woippy', 4820, 'GES', 75, 'FR', 49.15111000, 6.15132000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22300'),
(47994, 'Wolfgantzen', 4820, 'GES', 75, 'FR', 48.02805000, 7.50040000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22300'),
(47995, 'Wolfisheim', 4820, 'GES', 75, 'FR', 48.58723000, 7.66708000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q22300'),
(47996, 'Wormhout', 4828, 'HDF', 75, 'FR', 50.88129000, 2.46901000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q670807'),
(47997, 'Woustviller', 4820, 'GES', 75, 'FR', 49.07636000, 7.00487000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q670807'),
(47998, 'Xertigny', 4820, 'GES', 75, 'FR', 48.04394000, 6.40836000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q474291'),
(47999, 'Xonrupt-Longemer', 4820, 'GES', 75, 'FR', 48.08223000, 6.92944000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q520997'),
(48000, 'Yainville', 4804, 'NOR', 75, 'FR', 49.45371000, 0.82920000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q1418809'),
(48001, 'Ychoux', 4795, 'NAQ', 75, 'FR', 44.32869000, -0.95179000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q1418809'),
(48002, 'Ydes', 4798, 'ARA', 75, 'FR', 45.34722000, 2.43727000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q607536'),
(48003, 'Yenne', 4798, 'ARA', 75, 'FR', 45.70420000, 5.75795000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q681482'),
(48004, 'Yerres', 4796, 'IDF', 75, 'FR', 48.71785000, 2.49338000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q681482'),
(48005, 'Yerville', 4804, 'NOR', 75, 'FR', 49.66720000, 0.89594000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q681482'),
(48006, 'Yffiniac', 4807, 'BRE', 75, 'FR', 48.48428000, -2.67647000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q868555'),
(48007, 'Ygos-Saint-Saturnin', 4795, 'NAQ', 75, 'FR', 43.97651000, -0.73780000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q868555'),
(48008, 'Ymare', 4804, 'NOR', 75, 'FR', 49.35060000, 1.17938000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q868555'),
(48009, 'Youx', 4798, 'ARA', 75, 'FR', 46.14467000, 2.79903000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q868555'),
(48010, 'Yport', 4804, 'NOR', 75, 'FR', 49.73716000, 0.31537000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q868555'),
(48011, 'Yssingeaux', 4798, 'ARA', 75, 'FR', 45.14282000, 4.12372000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q217165'),
(48012, 'Ytrac', 4798, 'ARA', 75, 'FR', 44.91200000, 2.36248000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q217165'),
(48013, 'Yutz', 4820, 'GES', 75, 'FR', 49.35571000, 6.19260000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q21983'),
(48014, 'Yvelines', 4796, 'IDF', 75, 'FR', 48.80546000, 1.85696000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q12820'),
(48015, 'Yves', 4795, 'NAQ', 75, 'FR', 46.01922000, -1.04833000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q12820'),
(48016, 'Yvetot', 4804, 'NOR', 75, 'FR', 49.61744000, 0.75814000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q691278'),
(48017, 'Yvignac-la-Tour', 4807, 'BRE', 75, 'FR', 48.35000000, -2.18333000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q216829'),
(48018, 'Yvrac', 4795, 'NAQ', 75, 'FR', 44.87786000, -0.45870000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q216829'),
(48019, 'Yvré-le-Pôlin', 4802, 'PDL', 75, 'FR', 47.82235000, 0.15581000, '2019-10-05 22:49:13', '2020-05-01 17:22:46', 1, '********'),
(48020, 'Yzernay', 4802, 'PDL', 75, 'FR', 47.02229000, -0.70295000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, '********'),
(48021, 'Yzeure', 4798, 'ARA', 75, 'FR', 46.56596000, 3.35446000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q321518'),
(48022, 'Yzeures-sur-Creuse', 4818, 'CVL', 75, 'FR', 46.78609000, 0.87166000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q273886'),
(48023, 'Yèvres', 4818, 'CVL', 75, 'FR', 48.21078000, 1.18717000, '2019-10-05 22:49:13', '2020-05-01 17:22:44', 1, 'Q594021'),
(48024, 'Yébleron', 4804, 'NOR', 75, 'FR', 49.63333000, 0.53746000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q594021'),
(48025, 'Zegerscappel', 4828, 'HDF', 75, 'FR', 50.88333000, 2.40000000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q594021'),
(48026, 'Zillisheim', 4820, 'GES', 75, 'FR', 47.69604000, 7.29736000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q203238'),
(48027, 'Zimmersheim', 4820, 'GES', 75, 'FR', 47.72079000, 7.38847000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q203238'),
(48028, 'Zonza', 4806, '20R', 75, 'FR', 41.74934000, 9.17082000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q964272'),
(48029, 'Zutkerque', 4828, 'HDF', 75, 'FR', 50.85303000, 2.06818000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q964272'),
(48030, 'Zuydcoote', 4828, 'HDF', 75, 'FR', 51.06096000, 2.49338000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q231180'),
(48031, 'el Voló', 4799, 'OCC', 75, 'FR', 42.52424000, 2.83336000, '2019-10-05 22:49:13', '2020-05-01 17:22:46', 1, 'Q231180'),
(48032, 'els Banys d\'Arles', 4799, 'OCC', 75, 'FR', 42.47289000, 2.66916000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q16010679'),
(48033, 'la Guingueta d\'Ix', 4799, 'OCC', 75, 'FR', 42.43416000, 1.94391000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q35708703'),
(48034, 'la Roca d\'Albera', 4799, 'OCC', 75, 'FR', 42.52130000, 2.93374000, '2019-10-05 22:49:13', '2019-10-05 22:49:13', 1, 'Q35709418'),
(48035, 'Èze', 4812, 'PAC', 75, 'FR', 43.72799000, 7.36194000, '2019-10-05 22:49:13', '2020-05-01 17:22:46', 1, 'Q242617'),
(48036, 'Ébreuil', 4798, 'ARA', 75, 'FR', 46.11548000, 3.08677000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273297'),
(48037, 'Écaillon', 4828, 'HDF', 75, 'FR', 50.35034000, 3.21664000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q273297'),
(48038, 'Échalas', 4798, 'ARA', 75, 'FR', 45.55203000, 4.71544000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273297'),
(48039, 'Échenevex', 4798, 'ARA', 75, 'FR', 46.30923000, 6.03963000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273297'),
(48040, 'Échenoz-la-Méline', 4825, 'BFC', 75, 'FR', 47.60086000, 6.13544000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273297'),
(48041, 'Échillais', 4795, 'NAQ', 75, 'FR', 45.90072000, -0.95211000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q273297'),
(48042, 'Échirolles', 4798, 'ARA', 75, 'FR', 45.14603000, 5.71441000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273297'),
(48043, 'Échiré', 4795, 'NAQ', 75, 'FR', 46.38748000, -0.41511000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q273297'),
(48044, 'École-Valentin', 4825, 'BFC', 75, 'FR', 47.26667000, 5.98333000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273297'),
(48045, 'Écommoy', 4802, 'PDL', 75, 'FR', 47.82565000, 0.27422000, '2019-10-05 22:49:13', '2020-05-01 17:22:46', 1, 'Q273670'),
(48046, 'Écouché', 4804, 'NOR', 75, 'FR', 48.71751000, -0.12409000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q273689'),
(48047, 'Écouen', 4796, 'IDF', 75, 'FR', 49.02062000, 2.38309000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273695'),
(48048, 'Écouflant', 4802, 'PDL', 75, 'FR', 47.53101000, -0.52780000, '2019-10-05 22:49:13', '2020-05-01 17:22:46', 1, 'Q609005'),
(48049, 'Écourt-Saint-Quentin', 4828, 'HDF', 75, 'FR', 50.25226000, 3.07331000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q609005'),
(48050, 'Écoyeux', 4795, 'NAQ', 75, 'FR', 45.82231000, -0.50539000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q273706'),
(48051, 'Écrainville', 4804, 'NOR', 75, 'FR', 49.64943000, 0.32488000, '2019-10-05 22:49:13', '2020-05-01 17:22:45', 1, 'Q273706'),
(48052, 'Écrouves', 4820, 'GES', 75, 'FR', 48.67986000, 5.84267000, '2019-10-05 22:49:13', '2020-05-01 17:22:44', 1, 'Q192947'),
(48053, 'Écueillé', 4818, 'CVL', 75, 'FR', 47.08462000, 1.34668000, '2019-10-05 22:49:13', '2020-05-01 17:22:44', 1, 'Q273736'),
(48054, 'Écuelles', 4796, 'IDF', 75, 'FR', 48.35636000, 2.82335000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273736'),
(48055, 'Écuisses', 4825, 'BFC', 75, 'FR', 46.75631000, 4.53845000, '2019-10-05 22:49:13', '2020-05-01 17:22:43', 1, 'Q273736'),
(48056, 'Écully', 4798, 'ARA', 75, 'FR', 45.77437000, 4.77758000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q273748'),
(48057, 'Égletons', 4795, 'NAQ', 75, 'FR', 45.40637000, 2.04518000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q273748'),
(48058, 'Égly', 4796, 'IDF', 75, 'FR', 48.57828000, 2.22417000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q1165689'),
(48059, 'Égreville', 4796, 'IDF', 75, 'FR', 48.17606000, 2.87278000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q1453306'),
(48060, 'Égriselles-le-Bocage', 4825, 'BFC', 75, 'FR', 48.12103000, 3.18233000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q1453306'),
(48061, 'Éguilles', 4812, 'PAC', 75, 'FR', 43.56867000, 5.35575000, '2019-10-05 22:49:14', '2020-05-01 17:22:46', 1, 'Q274553'),
(48062, 'Éguzon-Chantôme', 4818, 'CVL', 75, 'FR', 46.45000000, 1.58333000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q274555'),
(48063, 'Élancourt', 4796, 'IDF', 75, 'FR', 48.78421000, 1.95520000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q240546'),
(48064, 'Éleu-dit-Leauwette', 4828, 'HDF', 75, 'FR', 50.42147000, 2.81079000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q240546'),
(48065, 'Éloyes', 4820, 'GES', 75, 'FR', 48.09793000, 6.60653000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q274801'),
(48066, 'Émerainville', 4796, 'IDF', 75, 'FR', 48.81276000, 2.62139000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q274801'),
(48067, 'Émerchicourt', 4828, 'HDF', 75, 'FR', 50.30786000, 3.24537000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q274801'),
(48068, 'Éoures', 4812, 'PAC', 75, 'FR', 43.30045000, 5.52193000, '2019-10-05 22:49:14', '2020-05-01 17:22:46', 1, 'Q274801'),
(48069, 'Épagny', 4798, 'ARA', 75, 'FR', 45.93584000, 6.08302000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q274801'),
(48070, 'Épaignes', 4804, 'NOR', 75, 'FR', 49.27945000, 0.43980000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q274801'),
(48071, 'Épehy', 4828, 'HDF', 75, 'FR', 50.00000000, 3.13333000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q274801'),
(48072, 'Éperlecques', 4828, 'HDF', 75, 'FR', 50.80566000, 2.15207000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q287123'),
(48073, 'Épernay', 4820, 'GES', 75, 'FR', 49.04000000, 3.95922000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q287123'),
(48074, 'Épernon', 4818, 'CVL', 75, 'FR', 48.61031000, 1.67218000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q287148'),
(48075, 'Épervans', 4825, 'BFC', 75, 'FR', 46.75247000, 4.89930000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q742478'),
(48076, 'Épieds-en-Beauce', 4818, 'CVL', 75, 'FR', 47.95066000, 1.61732000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q742478'),
(48077, 'Épinac', 4825, 'BFC', 75, 'FR', 46.98333000, 4.51667000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q742478'),
(48078, 'Épinal', 4820, 'GES', 75, 'FR', 48.18324000, 6.45304000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q173695'),
(48079, 'Épinay-sous-Sénart', 4796, 'IDF', 75, 'FR', 48.68970000, 2.51186000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q173695'),
(48080, 'Épinay-sur-Orge', 4796, 'IDF', 75, 'FR', 48.67338000, 2.31074000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q661726'),
(48081, 'Épinay-sur-Seine', 4796, 'IDF', 75, 'FR', 48.95350000, 2.31514000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q661726'),
(48082, 'Épinouze', 4798, 'ARA', 75, 'FR', 45.31003000, 4.92936000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q661726'),
(48083, 'Épouville', 4804, 'NOR', 75, 'FR', 49.56349000, 0.22373000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q661726'),
(48084, 'Épron', 4804, 'NOR', 75, 'FR', 49.22174000, -0.37085000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q661726'),
(48085, 'Épône', 4796, 'IDF', 75, 'FR', 48.95476000, 1.82233000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q244122'),
(48086, 'Équemauville', 4804, 'NOR', 75, 'FR', 49.39406000, 0.20760000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q244122'),
(48087, 'Équeurdreville-Hainneville', 4804, 'NOR', 75, 'FR', 49.64868000, -1.65306000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q1071742'),
(48088, 'Équihen-Plage', 4828, 'HDF', 75, 'FR', 50.67557000, 1.57225000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q1060599'),
(48089, 'Éragny', 4796, 'IDF', 75, 'FR', 49.01667000, 2.10000000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q1060599'),
(48090, 'Étables-sur-Mer', 4807, 'BRE', 75, 'FR', 48.63333000, -2.83333000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q274910'),
(48091, 'Étagnac', 4795, 'NAQ', 75, 'FR', 45.89506000, 0.77897000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q1155774'),
(48092, 'Étain', 4820, 'GES', 75, 'FR', 49.21259000, 5.64022000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q1155774'),
(48093, 'Étainhus', 4804, 'NOR', 75, 'FR', 49.56648000, 0.31285000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q1155774'),
(48094, 'Étalans', 4825, 'BFC', 75, 'FR', 47.15125000, 6.27315000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q288832'),
(48095, 'Étampes', 4796, 'IDF', 75, 'FR', 48.43507000, 2.16233000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q205584'),
(48096, 'Étampes-sur-Marne', 4828, 'HDF', 75, 'FR', 49.03464000, 3.41893000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q205584'),
(48097, 'Étang-sur-Arroux', 4825, 'BFC', 75, 'FR', 46.86560000, 4.18988000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q205584'),
(48098, 'Étaples', 4828, 'HDF', 75, 'FR', 50.52194000, 1.63319000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q273522'),
(48099, 'Étaules', 4795, 'NAQ', 75, 'FR', 45.73407000, -1.09918000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q273522'),
(48100, 'Étauliers', 4795, 'NAQ', 75, 'FR', 45.22582000, -0.57243000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q273522'),
(48101, 'Étel', 4807, 'BRE', 75, 'FR', 47.65614000, -3.20018000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q273522'),
(48102, 'Éterville', 4804, 'NOR', 75, 'FR', 49.14423000, -0.42512000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q273522'),
(48103, 'Étiolles', 4796, 'IDF', 75, 'FR', 48.63248000, 2.48226000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, '********'),
(48104, 'Étival-Clairefontaine', 4820, 'GES', 75, 'FR', 48.36519000, 6.86118000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, '********'),
(48105, 'Étival-lès-le-Mans', 4802, 'PDL', 75, 'FR', 47.95000000, 0.08333000, '2019-10-05 22:49:14', '2020-05-01 17:22:46', 1, '********'),
(48106, 'Étoile-sur-Rhône', 4798, 'ARA', 75, 'FR', 44.83883000, 4.89050000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q859997'),
(48107, 'Étreillers', 4828, 'HDF', 75, 'FR', 49.83059000, 3.16029000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q849338'),
(48108, 'Étrelles', 4807, 'BRE', 75, 'FR', 48.06031000, -1.19402000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q849338'),
(48109, 'Étrembières', 4798, 'ARA', 75, 'FR', 46.17923000, 6.22725000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q290880'),
(48110, 'Étretat', 4804, 'NOR', 75, 'FR', 49.70669000, 0.20523000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q290936'),
(48111, 'Étreux', 4828, 'HDF', 75, 'FR', 49.98333000, 3.65000000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q290936'),
(48112, 'Étriché', 4802, 'PDL', 75, 'FR', 47.65087000, -0.44377000, '2019-10-05 22:49:14', '2020-05-01 17:22:46', 1, 'Q290936'),
(48113, 'Étréchy', 4796, 'IDF', 75, 'FR', 48.49465000, 2.19489000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q290936'),
(48114, 'Étrépagny', 4804, 'NOR', 75, 'FR', 49.30623000, 1.61139000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q290936'),
(48115, 'Étupes', 4825, 'BFC', 75, 'FR', 47.50525000, 6.87075000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q291104'),
(48116, 'Évaux-les-Bains', 4795, 'NAQ', 75, 'FR', 46.17346000, 2.48463000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q291343'),
(48117, 'Évenos', 4812, 'PAC', 75, 'FR', 43.16365000, 5.84628000, '2019-10-05 22:49:14', '2020-05-01 17:22:46', 1, 'Q291343'),
(48118, 'Évian-les-Bains', 4798, 'ARA', 75, 'FR', 46.40111000, 6.58793000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q273017'),
(48119, 'Évin-Malmaison', 4828, 'HDF', 75, 'FR', 50.43923000, 3.03139000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q273017'),
(48120, 'Évires', 4798, 'ARA', 75, 'FR', 46.03783000, 6.22453000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q273017'),
(48121, 'Évran', 4807, 'BRE', 75, 'FR', 48.37987000, -1.98076000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, 'Q273017'),
(48122, 'Évrecy', 4804, 'NOR', 75, 'FR', 49.09904000, -0.50421000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q273017'),
(48123, 'Évreux', 4804, 'NOR', 75, 'FR', 49.02414000, 1.15082000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, 'Q171800'),
(48124, 'Évron', 4802, 'PDL', 75, 'FR', 48.15642000, -0.39970000, '2019-10-05 22:49:14', '2020-05-01 17:22:46', 1, 'Q171800'),
(48125, 'Évry', 4796, 'IDF', 75, 'FR', 48.63280000, 2.44049000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q171800'),
(48126, 'Ézanville', 4796, 'IDF', 75, 'FR', 49.02794000, 2.36787000, '2019-10-05 22:49:14', '2020-05-01 17:22:43', 1, 'Q242396'),
(48127, 'Ézy-sur-Eure', 4804, 'NOR', 75, 'FR', 48.86667000, 1.41667000, '2019-10-05 22:49:14', '2020-05-01 17:22:45', 1, '********'),
(48128, 'Œting', 4820, 'GES', 75, 'FR', 49.17291000, 6.91472000, '2019-10-05 22:49:14', '2020-05-01 17:22:44', 1, '********'),
(48129, 'Bitam', 2723, '9', 79, 'GA', 2.07597000, 11.50065000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, '********'),
(48130, 'Booué', 2724, '6', 79, 'GA', -0.09207000, 11.93846000, '2019-10-05 22:49:14', '2020-05-01 17:22:47', 1, '********'),
(48131, 'Cocobeach', 2727, '1', 79, 'GA', 1.00019000, 9.58229000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q491801'),
(48132, 'Fougamou', 2731, '4', 79, 'GA', -1.21544000, 10.58378000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1106169'),
(48133, 'Franceville', 2726, '2', 79, 'GA', -1.63333000, 13.58357000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q459369'),
(48134, 'Gamba', 2728, '8', 79, 'GA', -2.65000000, 10.00000000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1492877'),
(48135, 'Koulamoutou', 2729, '7', 79, 'GA', -1.13667000, 12.46399000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q985614'),
(48136, 'Lambaréné', 2730, '3', 79, 'GA', -0.70010000, 10.24055000, '2019-10-05 22:49:14', '2020-05-01 17:22:47', 1, 'Q874197'),
(48137, 'Lastoursville', 2729, '7', 79, 'GA', -0.81742000, 12.70818000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1818610'),
(48138, 'Libreville', 2727, '1', 79, 'GA', 0.39241000, 9.45356000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q3825'),
(48139, 'Lékoni', 2726, '2', 79, 'GA', -1.58431000, 14.25905000, '2019-10-05 22:49:14', '2020-05-01 17:22:47', 1, 'Q6350743'),
(48140, 'Makokou', 2724, '6', 79, 'GA', 0.57381000, 12.86419000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1002583'),
(48141, 'Mayumba', 2725, '5', 79, 'GA', -3.43198000, 10.65540000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q2738426'),
(48142, 'Mbigou', 2731, '4', 79, 'GA', -1.90046000, 11.90600000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q3303524'),
(48143, 'Mimongo', 2731, '4', 79, 'GA', -1.61952000, 11.60675000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q3314639'),
(48144, 'Mitzic', 2723, '9', 79, 'GA', 0.78205000, 11.54904000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q3135733'),
(48145, 'Moanda', 2726, '2', 79, 'GA', -1.56652000, 13.19870000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1015100'),
(48146, 'Mouila', 2731, '4', 79, 'GA', -1.86846000, 11.05594000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q746670'),
(48147, 'Mounana', 2726, '2', 79, 'GA', -1.40850000, 13.15857000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1200140'),
(48148, 'Ndendé', 2731, '4', 79, 'GA', -2.40077000, 11.35813000, '2019-10-05 22:49:14', '2020-05-01 17:22:47', 1, 'Q1200140'),
(48149, 'Ndjolé', 2730, '3', 79, 'GA', -0.17827000, 10.76488000, '2019-10-05 22:49:14', '2020-05-01 17:22:47', 1, 'Q1316415'),
(48150, 'Ntoum', 2727, '1', 79, 'GA', 0.39051000, 9.76096000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1316415'),
(48151, 'Okondja', 2726, '2', 79, 'GA', -0.65487000, 13.67533000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q4332646'),
(48152, 'Omboué', 2728, '8', 79, 'GA', -1.57464000, 9.26184000, '2019-10-05 22:49:14', '2020-05-01 17:22:47', 1, 'Q2717524'),
(48153, 'Oyem', 2723, '9', 79, 'GA', 1.59950000, 11.57933000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q983886'),
(48154, 'Port-Gentil', 2728, '8', 79, 'GA', -0.71933000, 8.78151000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q501715'),
(48155, 'Tchibanga', 2725, '5', 79, 'GA', -2.93323000, 10.98178000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q690421'),
(48156, 'Zadie', 2724, '6', 79, 'GA', 0.92582000, 13.90813000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q690421'),
(48157, 'Abbey Wood', 2336, 'ENG', 232, 'GB', 51.48688000, 0.10747000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q690421'),
(48158, 'Abbots Bromley', 2336, 'ENG', 232, 'GB', 52.81705000, -1.87694000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q2167664'),
(48159, 'Abbots Langley', 2336, 'ENG', 232, 'GB', 51.70573000, -0.41757000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q2167664'),
(48160, 'Abbotskerswell', 2336, 'ENG', 232, 'GB', 50.50816000, -3.61342000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q2684937'),
(48161, 'Abbotts Ann', 2336, 'ENG', 232, 'GB', 51.19016000, -1.53234000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q660124'),
(48162, 'Aberaeron', 2338, 'WLS', 232, 'GB', 52.24247000, -4.25871000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1765460'),
(48163, 'Abercanaid', 2338, 'WLS', 232, 'GB', 51.72361000, -3.36611000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q4666797'),
(48164, 'Abercarn', 2338, 'WLS', 232, 'GB', 51.64733000, -3.13476000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1868842'),
(48165, 'Aberchirder', 2335, 'SCT', 232, 'GB', 57.56012000, -2.62856000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q1017022'),
(48166, 'Abercynon', 2338, 'WLS', 232, 'GB', 51.64548000, -3.32727000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q3404663'),
(48167, 'Aberdare', 2338, 'WLS', 232, 'GB', 51.71438000, -3.44918000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q319369'),
(48168, 'Aberdeen', 2335, 'SCT', 232, 'GB', 57.14369000, -2.09814000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q36405'),
(48169, 'Aberdeen City', 2335, 'SCT', 232, 'GB', 57.16667000, -2.16667000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q62274582'),
(48170, 'Aberdeenshire', 2335, 'SCT', 232, 'GB', 57.16667000, -2.66667000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q189912'),
(48171, 'Aberdour', 2335, 'SCT', 232, 'GB', 56.05417000, -3.30058000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q2014221'),
(48172, 'Aberfan', 2338, 'WLS', 232, 'GB', 51.68892000, -3.34178000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q3244565'),
(48173, 'Aberfeldy', 2335, 'SCT', 232, 'GB', 56.62196000, -3.86693000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q319566'),
(48174, 'Aberford', 2336, 'ENG', 232, 'GB', 53.82604000, -1.34231000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q2853591'),
(48175, 'Abergavenny', 2338, 'WLS', 232, 'GB', 51.82098000, -3.01743000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q609161'),
(48176, 'Abergele', 2338, 'WLS', 232, 'GB', 53.28436000, -3.58220000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q2771917'),
(48177, 'Aberkenfig', 2338, 'WLS', 232, 'GB', 51.54000000, -3.59556000, '2019-10-05 22:49:14', '2019-10-05 22:49:14', 1, 'Q4667120'),
(48178, 'Aberlady', 2335, 'SCT', 232, 'GB', 56.00884000, -2.85851000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1010412'),
(48179, 'Abernethy', 2335, 'SCT', 232, 'GB', 56.33247000, -3.31226000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q744726'),
(48180, 'Aberporth', 2338, 'WLS', 232, 'GB', 52.13248000, -4.54173000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q319672'),
(48181, 'Abertillery', 2338, 'WLS', 232, 'GB', 51.72981000, -3.13432000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2272537'),
(48182, 'Abertridwr', 2338, 'WLS', 232, 'GB', 51.59583000, -3.26833000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4667208'),
(48183, 'Aberystwyth', 2338, 'WLS', 232, 'GB', 52.41548000, -4.08292000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q213154'),
(48184, 'Abingdon', 2336, 'ENG', 232, 'GB', 51.67109000, -1.28278000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q321381'),
(48185, 'Aboyne', 2335, 'SCT', 232, 'GB', 57.07546000, -2.78023000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q323196'),
(48186, 'Abram', 2336, 'ENG', 232, 'GB', 53.50855000, -2.59266000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4669333'),
(48187, 'Abridge', 2336, 'ENG', 232, 'GB', 51.64950000, 0.12033000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2215683'),
(48188, 'Accrington', 2336, 'ENG', 232, 'GB', 53.75379000, -2.35863000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1622949'),
(48189, 'Acklington', 2336, 'ENG', 232, 'GB', 55.30000000, -1.63333000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q675592'),
(48190, 'Acle', 2336, 'ENG', 232, 'GB', 52.63681000, 1.54757000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q341938'),
(48191, 'Acocks Green', 2336, 'ENG', 232, 'GB', 52.45000000, -1.81667000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4674449'),
(48192, 'Acomb', 2336, 'ENG', 232, 'GB', 54.99229000, -2.11229000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2536739'),
(48193, 'Acton', 2336, 'ENG', 232, 'GB', 51.50901000, -0.27620000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q83609'),
(48194, 'Adderbury', 2336, 'ENG', 232, 'GB', 52.01690000, -1.31192000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q352863'),
(48195, 'Addiebrownhill', 2335, 'SCT', 232, 'GB', 55.84289000, -3.61667000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q352863'),
(48196, 'Addingham', 2336, 'ENG', 232, 'GB', 53.94452000, -1.88424000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q379316'),
(48197, 'Addlestone', 2336, 'ENG', 232, 'GB', 51.37135000, -0.49353000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q353307'),
(48198, 'Adlington', 2336, 'ENG', 232, 'GB', 53.61323000, -2.60676000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48199, 'Adwick le Street', 2336, 'ENG', 232, 'GB', 53.57077000, -1.18454000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48200, 'Ahoghill', 2337, 'NIR', 232, 'GB', 54.86667000, -6.36667000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, '********'),
(48201, 'Airdrie', 2335, 'SCT', 232, 'GB', 55.86602000, -3.98025000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q408536'),
(48202, 'Airmyn', 2336, 'ENG', 232, 'GB', 53.72074000, -0.89959000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48203, 'Airth', 2335, 'SCT', 232, 'GB', 56.06983000, -3.77209000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, '********'),
(48204, 'Albrighton', 2336, 'ENG', 232, 'GB', 52.63640000, -2.27966000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q728994'),
(48205, 'Alcester', 2336, 'ENG', 232, 'GB', 52.********, -1.86667000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1012025'),
(48206, 'Alconbury', 2336, 'ENG', 232, 'GB', 52.36900000, -0.26009000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4713355'),
(48207, 'Aldbourne', 2336, 'ENG', 232, 'GB', 51.48098000, -1.61827000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1215829'),
(48208, 'Aldbrough', 2336, 'ENG', 232, 'GB', 53.82893000, -0.11467000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2651619'),
(48209, 'Aldeburgh', 2336, 'ENG', 232, 'GB', 52.15259000, 1.60124000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2077402'),
(48210, 'Alderbury', 2336, 'ENG', 232, 'GB', 51.04354000, -1.73382000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2491329'),
(48211, 'Alderholt', 2336, 'ENG', 232, 'GB', 50.91195000, -1.83083000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4713675'),
(48212, 'Alderley Edge', 2336, 'ENG', 232, 'GB', 53.30393000, -2.23773000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q986046'),
(48213, 'Aldershot', 2336, 'ENG', 232, 'GB', 51.24827000, -0.76389000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q646980'),
(48214, 'Aldford', 2336, 'ENG', 232, 'GB', 53.12762000, -2.86812000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1866463'),
(48215, 'Aldridge', 2336, 'ENG', 232, 'GB', 52.60549000, -1.91715000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1807596'),
(48216, 'Alexandria', 2335, 'SCT', 232, 'GB', 55.99379000, -4.58640000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1240659'),
(48217, 'Alford', 2336, 'ENG', 232, 'GB', 53.25943000, 0.17625000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1876378'),
(48218, 'Alford', 2335, 'SCT', 232, 'GB', 57.23257000, -2.70298000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2638187'),
(48219, 'Alfreton', 2336, 'ENG', 232, 'GB', 53.********, -1.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2462661'),
(48220, 'Allanton', 2335, 'SCT', 232, 'GB', 55.********, -2.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2462661'),
(48221, 'Allhallows', 2336, 'ENG', 232, 'GB', 51.********, 0.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2927534'),
(48222, 'Alloa', 2335, 'SCT', 232, 'GB', 56.********, -3.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1009331'),
(48223, 'Almondbank', 2335, 'SCT', 232, 'GB', 56.********, -3.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1014075'),
(48224, 'Almondsbury', 2336, 'ENG', 232, 'GB', 51.********, -2.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2665491'),
(48225, 'Alness', 2335, 'SCT', 232, 'GB', 57.********, -4.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q582452'),
(48226, 'Alnwick', 2336, 'ENG', 232, 'GB', 55.********, -1.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1002826'),
(48227, 'Alresford', 2336, 'ENG', 232, 'GB', 51.********, 1.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2397236'),
(48228, 'Alrewas', 2336, 'ENG', 232, 'GB', 52.********, -1.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2507226'),
(48229, 'Alsager', 2336, 'ENG', 232, 'GB', 53.********, -2.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2388660'),
(48230, 'Alston', 2336, 'ENG', 232, 'GB', 54.********, -2.********, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2560190'),
(48231, 'Althorne', 2336, 'ENG', 232, 'GB', 51.65722000, 0.76085000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q653906'),
(48232, 'Alton', 2336, 'ENG', 232, 'GB', 51.14931000, -0.97469000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q443840'),
(48233, 'Altrincham', 2336, 'ENG', 232, 'GB', 53.38752000, -2.34848000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q445058'),
(48234, 'Alva', 2335, 'SCT', 232, 'GB', 56.15284000, -3.80505000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q448127'),
(48235, 'Alvechurch', 2336, 'ENG', 232, 'GB', 52.35173000, -1.96531000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1639867'),
(48236, 'Alveley', 2336, 'ENG', 232, 'GB', 52.45709000, -2.35434000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q3811322'),
(48237, 'Alveston', 2336, 'ENG', 232, 'GB', 51.58806000, -2.53139000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4738005'),
(48238, 'Alyth', 2335, 'SCT', 232, 'GB', 56.62209000, -3.23005000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1012038'),
(48239, 'Amble', 2336, 'ENG', 232, 'GB', 55.33333000, -1.58333000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1940839'),
(48240, 'Ambleside', 2336, 'ENG', 232, 'GB', 54.43261000, -2.96167000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q391805'),
(48241, 'Ambrosden', 2336, 'ENG', 232, 'GB', 51.87087000, -1.12129000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2692883'),
(48242, 'Amersham', 2336, 'ENG', 232, 'GB', 51.66667000, -0.61667000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q470188'),
(48243, 'Amersham on the Hill', 2336, 'ENG', 232, 'GB', 51.67468000, -0.60742000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q470188'),
(48244, 'Amesbury', 2336, 'ENG', 232, 'GB', 51.17509000, -1.78064000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q470299'),
(48245, 'Amlwch', 2338, 'WLS', 232, 'GB', 53.40986000, -4.34712000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q472667'),
(48246, 'Ammanford', 2338, 'WLS', 232, 'GB', 51.79279000, -3.98833000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2472406'),
(48247, 'Ampthill', 2336, 'ENG', 232, 'GB', 52.02694000, -0.49567000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q477350'),
(48248, 'Ancaster', 2336, 'ENG', 232, 'GB', 52.98276000, -0.53593000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2636096'),
(48249, 'Andover', 2336, 'ENG', 232, 'GB', 51.21135000, -1.49393000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q492805'),
(48250, 'Anglesey', 2338, 'WLS', 232, 'GB', 53.25000000, -4.33333000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q492805'),
(48251, 'Angus', 2335, 'SCT', 232, 'GB', 56.66667000, -2.91667000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q202177'),
(48252, 'Anna Valley', 2336, 'ENG', 232, 'GB', 51.19317000, -1.50719000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4767578'),
(48253, 'Annahilt', 2337, 'NIR', 232, 'GB', 54.43333000, -6.00000000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q149542'),
(48254, 'Annalong', 2337, 'NIR', 232, 'GB', 54.10823000, -5.89966000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q60776'),
(48255, 'Annan', 2335, 'SCT', 232, 'GB', 54.98839000, -3.25647000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q564495'),
(48256, 'Annfield Plain', 2336, 'ENG', 232, 'GB', 54.85749000, -1.73827000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4769155'),
(48257, 'Anstey', 2336, 'ENG', 232, 'GB', 52.67368000, -1.18841000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1613213'),
(48258, 'Anstruther', 2335, 'SCT', 232, 'GB', 56.22315000, -2.70229000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q570401'),
(48259, 'Antrim', 2337, 'NIR', 232, 'GB', 54.70000000, -6.20000000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q609235'),
(48260, 'Antrim and Newtownabbey', 2337, 'NIR', 232, 'GB', 54.70177000, -6.19770000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q609235'),
(48261, 'Appleby', 2336, 'ENG', 232, 'GB', 53.62198000, -0.56612000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4781302'),
(48262, 'Appleby-in-Westmorland', 2336, 'ENG', 232, 'GB', 54.57704000, -2.48978000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q621540'),
(48263, 'Appledore', 2336, 'ENG', 232, 'GB', 51.05000000, -4.20000000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1229094'),
(48264, 'Appleton Thorn', 2336, 'ENG', 232, 'GB', 53.35045000, -2.54488000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q621563'),
(48265, 'Appley Bridge', 2336, 'ENG', 232, 'GB', 53.57781000, -2.72090000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4781442'),
(48266, 'Arbroath', 2335, 'SCT', 232, 'GB', 56.56317000, -2.58736000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q630668'),
(48267, 'Archway', 2336, 'ENG', 232, 'GB', 51.56733000, -0.13415000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q630668'),
(48268, 'Ardersier', 2335, 'SCT', 232, 'GB', 57.56681000, -4.03784000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1011351'),
(48269, 'Ardglass', 2337, 'NIR', 232, 'GB', 54.26312000, -5.60981000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q639255'),
(48270, 'Ardingly', 2336, 'ENG', 232, 'GB', 51.04865000, -0.07716000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q639397'),
(48271, 'Ardrishaig', 2335, 'SCT', 232, 'GB', 56.01566000, -5.44806000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q640035'),
(48272, 'Ardrossan', 2335, 'SCT', 232, 'GB', 55.65018000, -4.80659000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q640073'),
(48273, 'Ards and North Down', 2337, 'NIR', 232, 'GB', 54.53439000, -5.62947000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q640073'),
(48274, 'Argyll and Bute', 2335, 'SCT', 232, 'GB', 56.03693000, -5.43679000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q202174'),
(48275, 'Arlesey', 2336, 'ENG', 232, 'GB', 52.00713000, -0.26565000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q777460'),
(48276, 'Armadale', 2335, 'SCT', 232, 'GB', 55.88333000, -3.70000000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2706897'),
(48277, 'Armagh', 2337, 'NIR', 232, 'GB', 54.35000000, -6.66667000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q193452'),
(48278, 'Armagh City Banbridge and Craigavon', 2337, 'NIR', 232, 'GB', 54.36922000, -6.45651000, '2019-10-05 22:49:15', '2021-01-23 14:35:23', 1, 'Q193452'),
(48279, 'Armitage', 2336, 'ENG', 232, 'GB', 52.74193000, -1.88266000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4793618'),
(48280, 'Armthorpe', 2336, 'ENG', 232, 'GB', 53.53518000, -1.05341000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2339006'),
(48281, 'Arnold', 2336, 'ENG', 232, 'GB', 53.00000000, -1.13333000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q694780'),
(48282, 'Arnside', 2336, 'ENG', 232, 'GB', 54.20179000, -2.83374000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q697494'),
(48283, 'Arundel', 2336, 'ENG', 232, 'GB', 50.85423000, -0.55393000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q716658'),
(48284, 'Ascot', 2336, 'ENG', 232, 'GB', 51.41082000, -0.67480000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q723329'),
(48285, 'Asfordby', 2336, 'ENG', 232, 'GB', 52.76331000, -0.95856000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2298445'),
(48286, 'Ash', 2336, 'ENG', 232, 'GB', 51.27883000, 1.27974000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q4804328'),
(48287, 'Ashbourne', 2336, 'ENG', 232, 'GB', 53.01667000, -1.73333000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2190607'),
(48288, 'Ashburton', 2336, 'ENG', 232, 'GB', 50.51559000, -3.75572000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q724915'),
(48289, 'Ashby de la Zouch', 2336, 'ENG', 232, 'GB', 52.74632000, -1.47320000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2046537'),
(48290, 'Ashford', 2336, 'ENG', 232, 'GB', 51.14648000, 0.87376000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q725261'),
(48291, 'Ashgill', 2335, 'SCT', 232, 'GB', 55.73119000, -3.93019000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q725261'),
(48292, 'Ashill', 2336, 'ENG', 232, 'GB', 52.60435000, 0.78574000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q943542'),
(48293, 'Ashington', 2336, 'ENG', 232, 'GB', 55.17719000, -1.56412000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q202348'),
(48294, 'Ashtead', 2336, 'ENG', 232, 'GB', 51.30873000, -0.29972000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2220778'),
(48295, 'Ashton Keynes', 2336, 'ENG', 232, 'GB', 51.64521000, -1.93232000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2539726'),
(48296, 'Ashton in Makerfield', 2336, 'ENG', 232, 'GB', 53.48333000, -2.65000000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2557991'),
(48297, 'Ashton-under-Lyne', 2336, 'ENG', 232, 'GB', 53.48876000, -2.09890000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q659803'),
(48298, 'Ashurst', 2336, 'ENG', 232, 'GB', 50.93236000, -0.32375000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2658590'),
(48299, 'Ashwell', 2336, 'ENG', 232, 'GB', 52.03866000, -0.15398000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q778795'),
(48300, 'Askam in Furness', 2336, 'ENG', 232, 'GB', 54.18718000, -3.20467000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q23309970'),
(48301, 'Askern', 2336, 'ENG', 232, 'GB', 53.61639000, -1.15237000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q2555402'),
(48302, 'Aslockton', 2336, 'ENG', 232, 'GB', 52.95299000, -0.89700000, '2019-10-05 22:49:15', '2019-10-05 22:49:15', 1, 'Q1957196');

