INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(57942, 'Betul', 4039, '<PERSON>', 101, 'IN', 21.83333000, 77.83333000, '2019-10-05 22:53:45', '2021-02-20 17:14:02', 1, 'Q1815279'),
(57943, 'Betul Bazar', 4039, 'MP', 101, 'IN', 21.85572000, 77.92913000, '2019-10-05 22:53:45', '2021-02-20 17:14:02', 1, 'Q2314399'),
(57944, 'Bewar', 4022, 'UP', 101, 'IN', 27.21869000, 79.29761000, '2019-10-05 22:53:45', '2021-06-06 11:41:13', 1, 'Q730492'),
(57945, '<PERSON><PERSON>', 4028, 'K<PERSON>', 101, 'IN', 11.17151000, 75.80611000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759539'),
(57946, '<PERSON>awar', 4014, 'RJ', 101, 'IN', 26.10119000, 74.32028000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q757855'),
(57947, '<PERSON><PERSON>hua', 4037, 'BR', 101, 'IN', 25.04049000, 83.60749000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759544'),
(57948, 'Bhachau', 4030, 'GJ', 101, 'IN', 23.29858000, 70.34279000, '2019-10-05 22:53:45', '2020-06-14 15:24:16', 1, 'Q759550'),
(57949, 'Bhadarwah', 4029, 'JK', 101, 'IN', 32.97941000, 75.71723000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q760372'),
(57950, 'Bhadaur', 4015, 'PB', 101, 'IN', 30.47651000, 75.33049000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q757840'),
(57951, 'Bhadohi', 4022, 'UP', 101, 'IN', 25.39526000, 82.57030000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759217'),
(57952, 'Bhadradri Kothagudem', 4012, 'TG', 101, 'IN', 17.55460000, 80.61976000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759217'),
(57953, 'Bhadrak', 4013, 'OR', 101, 'IN', 21.00000000, 86.60000000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q685638'),
(57954, 'Bhadrakh', 4013, 'OR', 101, 'IN', 21.05447000, 86.51560000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759556'),
(57955, 'Bhadrachalam', 4012, 'TG', 101, 'IN', 17.66846000, 80.88887000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q759204'),
(57956, 'Bhadravati', 4026, 'KA', 101, 'IN', 13.84846000, 75.70502000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q2284099'),
(57957, 'Bhagirathpur', 4037, 'BR', 101, 'IN', 26.26950000, 86.06346000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q2284099'),
(57958, 'Bhagwantnagar', 4022, 'UP', 101, 'IN', 26.22383000, 80.75750000, '2019-10-05 22:53:45', '2021-06-06 11:41:13', 1, 'Q26791707'),
(57959, 'Bhainsdehi', 4039, 'MP', 101, 'IN', 21.64491000, 77.63023000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759139'),
(57960, 'Bhaisa', 4012, 'TG', 101, 'IN', 19.11285000, 77.96336000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q759999'),
(57961, 'Bhandara', 4008, 'MH', 101, 'IN', 21.18333000, 80.00000000, '2019-10-05 22:53:45', '2021-06-06 13:11:00', 1, 'Q33424761'),
(57963, 'Bhanjanagar', 4013, 'OR', 101, 'IN', 19.92719000, 84.58201000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q760179'),
(57964, 'Bharatpur', 4014, 'RJ', 101, 'IN', 27.21000000, 77.29000000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q854846'),
(57965, 'Bharthana', 4022, 'UP', 101, 'IN', 26.75231000, 79.22180000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q662285'),
(57966, 'Bharwari', 4022, 'UP', 101, 'IN', 25.56078000, 81.49164000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q2442005'),
(57967, 'Bharuch', 4030, 'GJ', 101, 'IN', 21.69482000, 72.98050000, '2019-10-05 22:53:45', '2020-06-14 15:24:13', 1, 'Q760153'),
(57968, 'Bhasawar', 4014, 'RJ', 101, 'IN', 27.03895000, 77.04849000, '2019-10-05 22:53:45', '2021-06-06 11:52:22', 1, 'Q26791337'),
(57969, 'Bhatgaon', 4040, 'CT', 101, 'IN', 21.15000000, 81.70000000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q13403597'),
(57970, 'Bhatkal', 4026, 'KA', 101, 'IN', 13.98534000, 74.55531000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q13403597'),
(57971, 'Bhattiprolu', 4017, 'AP', 101, 'IN', 16.10260000, 80.78074000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q3429099'),
(57972, 'Bhavnagar', 4030, 'GJ', 101, 'IN', 21.76287000, 72.15331000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q242992'),
(57973, 'Bhavani', 4035, 'TN', 101, 'IN', 11.44553000, 77.68215000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q1749241'),
(57974, 'Bhawanipur', 4037, 'BR', 101, 'IN', 26.45352000, 87.02744000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q4901597'),
(57975, 'Bhawaniganj', 4039, 'MP', 101, 'IN', 24.41582000, 75.83552000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q4901597'),
(57976, 'Bhawanipatna', 4013, 'OR', 101, 'IN', 19.90717000, 83.16697000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q759348'),
(57977, 'Bhawanigarh', 4015, 'PB', 101, 'IN', 30.26685000, 76.03854000, '2019-10-05 22:53:45', '2020-07-04 11:24:01', 1, 'Q760176'),
(57978, 'Bhayandar', 4008, 'MH', 101, 'IN', 19.30157000, 72.85107000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q4901615'),
(57979, 'Bhigvan', 4008, 'MH', 101, 'IN', 18.30070000, 74.76701000, '2019-10-05 22:53:45', '2021-06-06 13:11:00', 1, 'Q26791344'),
(57980, 'Bhikangaon', 4039, 'MP', 101, 'IN', 21.86764000, 75.96391000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q2314441'),
(57981, 'Bhilai', 4040, 'CT', 101, 'IN', 21.20919000, 81.42850000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q242144'),
(57982, 'Bhind', 4039, 'MP', 101, 'IN', 26.********, 78.********, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q2341700'),
(57983, 'Bhindar', 4014, 'RJ', 101, 'IN', 24.50235000, 74.18551000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q2341700'),
(57984, 'Bhinga', 4022, 'UP', 101, 'IN', 27.70283000, 81.93430000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q795811'),
(57985, 'Bhitarwar', 4039, 'MP', 101, 'IN', 25.79216000, 78.11085000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q796805'),
(57986, 'Bhiwadi', 4014, 'RJ', 101, 'IN', 28.21024000, 76.86056000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q796761'),
(57987, 'Bhiwandi', 4008, 'MH', 101, 'IN', 19.30023000, 73.05881000, '2019-10-05 22:53:45', '2021-06-06 13:11:00', 1, 'Q31856876'),
(57988, 'Bhiwani', 4007, 'HR', 101, 'IN', 28.********, 76.16667000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q1852857'),
(57990, 'Bhogpur', 4015, 'PB', 101, 'IN', 31.55442000, 75.64271000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q796770'),
(57991, 'Bhojpur', 4037, 'BR', 101, 'IN', 25.30886000, 84.44504000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q796770'),
(57992, 'Bhojudih', 4025, 'JH', 101, 'IN', 23.63962000, 86.44105000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q795805'),
(57993, 'Bhongaon', 4022, 'UP', 101, 'IN', 27.25515000, 79.18118000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q4901949'),
(57994, 'Bhongir', 4012, 'TG', 101, 'IN', 17.51544000, 78.88563000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q797132'),
(57995, 'Bhopal', 4039, 'MP', 101, 'IN', 23.25469000, 77.40289000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q80989'),
(57997, 'Bhor', 4008, 'MH', 101, 'IN', 18.14861000, 73.84336000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q797462'),
(57998, 'Bhowali', 4016, 'UK', 101, 'IN', 29.38985000, 79.50481000, '2019-10-05 22:53:45', '2024-02-16 18:35:00', 1, 'Q796801'),
(57999, 'Bhuban', 4013, 'OR', 101, 'IN', 20.88197000, 85.83334000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q796742'),
(58000, 'Bhubaneswar', 4013, 'OR', 101, 'IN', 20.27241000, 85.83385000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q171771'),
(58001, 'Bhudgaon', 4008, 'MH', 101, 'IN', 16.90742000, 74.59954000, '2019-10-05 22:53:45', '2021-06-06 13:11:00', 1, 'Q26791355'),
(58002, 'Bhuj', 4030, 'GJ', 101, 'IN', 23.25397000, 69.66928000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q798382'),
(58004, 'Bhuma', 4014, 'RJ', 101, 'IN', 27.78333000, 74.93333000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q4902137'),
(58005, 'Bhusaval', 4008, 'MH', 101, 'IN', 21.04365000, 75.78506000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q796650'),
(58006, 'Bhabhra', 4039, 'MP', 101, 'IN', 22.53048000, 74.32846000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q796650'),
(58007, 'Bhadra', 4014, 'RJ', 101, 'IN', 29.10298000, 75.17138000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q777668'),
(58008, 'Bhadasar', 4014, 'RJ', 101, 'IN', 28.31457000, 74.28952000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q777668'),
(58009, 'Bhagalpur', 4037, 'BR', 101, 'IN', 25.29023000, 87.06665000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q49155'),
(58010, 'Bhalki', 4026, 'KA', 101, 'IN', 18.04348000, 77.20600000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q49155'),
(58011, 'Bhander', 4039, 'MP', 101, 'IN', 25.73581000, 78.74555000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q759166'),
(58012, 'Bhanpura', 4039, 'MP', 101, 'IN', 24.51300000, 75.74690000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q674268'),
(58013, 'Bhanpuri', 4040, 'CT', 101, 'IN', 21.09190000, 80.93218000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q720123'),
(58014, 'Bhanvad', 4030, 'GJ', 101, 'IN', 21.93053000, 69.78081000, '2019-10-05 22:53:45', '2020-06-14 15:24:33', 1, 'Q720917'),
(58015, 'Bhatapara', 4040, 'CT', 101, 'IN', 21.73500000, 81.94711000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q757847'),
(58017, 'Bhayavadar', 4030, 'GJ', 101, 'IN', 21.85523000, 70.24791000, '2019-10-05 22:53:45', '2020-06-14 15:24:39', 1, 'Q759168'),
(58018, 'Bhikhi', 4015, 'PB', 101, 'IN', 30.05918000, 75.53500000, '2019-10-05 22:53:45', '2020-07-04 11:24:05', 1, 'Q2546717'),
(58019, 'Bhilwara', 4014, 'RJ', 101, 'IN', 25.********, 74.********, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q41991'),
(58020, 'Bhim Tal', 4016, 'UK', 101, 'IN', 29.34447000, 79.56336000, '2019-10-05 22:53:45', '2024-02-16 18:35:00', 1, 'Q41991'),
(58021, 'Bhimavaram', 4017, 'AP', 101, 'IN', 16.54078000, 81.52322000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q757853'),
(58022, 'Bhimunipatnam', 4017, 'AP', 101, 'IN', 17.89017000, 83.45203000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q2483868'),
(58023, 'Bhinmal', 4014, 'RJ', 101, 'IN', 24.99944000, 72.27141000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q797437'),
(58024, 'Bhoom', 4008, 'MH', 101, 'IN', 18.45908000, 75.65877000, '2019-10-05 22:53:45', '2023-03-25 17:22:03', 1, 'Q796520'),
(58025, 'Biaora', 4039, 'MP', 101, 'IN', 23.92050000, 76.91074000, '2019-10-05 22:53:45', '2019-10-05 22:53:45', 1, 'Q796769'),
(58027, 'Bidhuna', 4022, 'UP', 101, 'IN', 26.80172000, 79.50829000, '2019-10-05 22:53:45', '2021-02-20 17:14:02', 1, 'Q796775'),
(58028, 'Bihpuriagaon', 4027, 'AS', 101, 'IN', 27.01718000, 93.91673000, '2019-10-05 22:53:45', '2021-02-20 17:12:44', 1, 'Q796775'),
(58029, 'Bihar Sharif', 4037, 'BR', 101, 'IN', 25.20084000, 85.52389000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q2308343'),
(58030, 'Bihariganj', 4037, 'BR', 101, 'IN', 25.73415000, 86.98837000, '2019-10-05 22:53:45', '2021-02-20 17:13:29', 1, 'Q4907104'),
(58031, 'Vijayapura', 4026, 'KA', 101, 'IN', 16.82442000, 75.71537000, '2019-10-05 23:03:48', '2021-12-11 12:47:20', 1, 'Q3636023'),
(58032, 'Bijapur', 4040, 'CT', 101, 'IN', 18.84322000, 80.77610000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q100164'),
(58033, 'Bijbehara', 4029, 'JK', 101, 'IN', 33.79378000, 75.10700000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q100164'),
(58034, 'Bijni', 4027, 'AS', 101, 'IN', 26.49588000, 90.70298000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q797126'),
(58035, 'Bijnor', 4022, 'UP', 101, 'IN', 29.41667000, 78.51667000, '2019-10-05 23:03:48', '2021-06-06 11:41:13', 1, 'Q796664'),
(58036, 'Bijrauni', 4039, 'MP', 101, 'IN', 24.93296000, 77.64352000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q1937865'),
(58038, 'Bijawar', 4039, 'MP', 101, 'IN', 24.62351000, 79.48994000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q797222'),
(58039, 'Bikramganj', 4037, 'BR', 101, 'IN', 25.21073000, 84.25508000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q796526'),
(58040, 'Bilariaganj', 4022, 'UP', 101, 'IN', 26.19593000, 83.22690000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q795717'),
(58041, 'Bilaspur', 4020, 'HP', 101, 'IN', 31.33027000, 76.75663000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q795717'),
(58042, 'Bilgi', 4026, 'KA', 101, 'IN', 16.34714000, 75.61804000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q796485'),
(58043, 'Bilgram', 4022, 'UP', 101, 'IN', 27.17509000, 80.03201000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q795814'),
(58044, 'Bilhaur', 4022, 'UP', 101, 'IN', 26.84345000, 80.06388000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q858170'),
(58045, 'Bilimora', 4030, 'GJ', 101, 'IN', 20.76957000, 72.96134000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q862141'),
(58046, 'Bilkha', 4030, 'GJ', 101, 'IN', 21.44150000, 70.60063000, '2019-10-05 23:03:48', '2021-06-06 11:55:54', 1, 'Q11909377'),
(58047, 'Biloli', 4008, 'MH', 101, 'IN', 18.77385000, 77.72463000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q861330'),
(58048, 'Bilsanda', 4022, 'UP', 101, 'IN', 28.24341000, 79.95135000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q861856'),
(58049, 'Bilsi', 4022, 'UP', 101, 'IN', 28.12941000, 78.91090000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q859148'),
(58050, 'Bilthra', 4022, 'UP', 101, 'IN', 26.12705000, 83.89148000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q859148'),
(58051, 'Bilara', 4014, 'RJ', 101, 'IN', 26.18067000, 73.70550000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q796497'),
(58052, 'Bilari', 4022, 'UP', 101, 'IN', 28.62146000, 78.80361000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q796749'),
(58053, 'Bilasipara', 4027, 'AS', 101, 'IN', 26.23285000, 90.23410000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q795803'),
(58054, 'Bilaspur', 4007, 'HR', 101, 'IN', 30.30450000, 77.30424000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q3639927'),
(58055, 'Bilaspur', 4040, 'CT', 101, 'IN', 22.38333000, 82.13333000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q100157'),
(58057, 'Bindki', 4022, 'UP', 101, 'IN', 26.03613000, 80.57617000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q858148'),
(58058, 'Binka', 4013, 'OR', 101, 'IN', 21.02626000, 83.81197000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q860226'),
(58059, 'Birbhaddar', 4016, 'UK', 101, 'IN', 30.07120000, 78.28189000, '2019-10-05 23:03:48', '2024-02-16 18:35:00', 1, 'Q860226'),
(58060, 'Birmitrapur', 4013, 'OR', 101, 'IN', 22.40000000, 84.76667000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q860226'),
(58061, 'Birur', 4026, 'KA', 101, 'IN', 13.59723000, 75.97167000, '2019-10-05 23:03:48', '2021-02-20 17:14:02', 1, 'Q861119'),
(58062, 'Bisauli', 4022, 'UP', 101, 'IN', 28.30772000, 78.93678000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q862048'),
(58063, 'Bisenda Buzurg', 4022, 'UP', 101, 'IN', 25.40350000, 80.61889000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q859094'),
(58064, 'Bishnupur', 4010, 'MN', 101, 'IN', 24.60769000, 93.77998000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q859094'),
(58065, 'Bishnah', 4029, 'JK', 101, 'IN', 32.61060000, 74.85557000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q861136'),
(58066, 'Bishunpur Urf Maharajganj', 4022, 'UP', 101, 'IN', 26.25914000, 83.11643000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q2606051'),
(58067, 'Bissau', 4014, 'RJ', 101, 'IN', 28.24737000, 75.07666000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q739697'),
(58068, 'Biswan', 4022, 'UP', 101, 'IN', 27.49581000, 80.99618000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q858291'),
(58069, 'Bithur', 4022, 'UP', 101, 'IN', 26.60664000, 80.27098000, '2019-10-05 23:03:48', '2021-02-20 17:14:02', 1, 'Q858291'),
(58070, 'Bobbili', 4017, 'AP', 101, 'IN', 18.57366000, 83.35925000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q861841'),
(58071, 'Bodhan', 4012, 'TG', 101, 'IN', 18.66208000, 77.88581000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q859167'),
(58072, 'Bodinayakkanur', 4035, 'TN', 101, 'IN', 10.01171000, 77.34976000, '2019-10-05 23:03:48', '2021-02-20 17:14:02', 1, 'Q859153'),
(58073, 'Bodri', 4039, 'MP', 101, 'IN', 23.16524000, 81.43262000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q858405'),
(58074, 'Boisar', 4008, 'MH', 101, 'IN', 19.80362000, 72.75598000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q858592'),
(58075, 'Bokajan', 4027, 'AS', 101, 'IN', 26.02131000, 93.77945000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q633816'),
(58076, 'Bokaro', 4025, 'JH', 101, 'IN', 23.68562000, 85.99026000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q2295925'),
(58077, 'Bokakhat', 4027, 'AS', 101, 'IN', 26.64018000, 93.60052000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q859160'),
(58079, 'Bolanikhodan', 4013, 'OR', 101, 'IN', 22.11312000, 85.33645000, '2019-10-05 23:03:48', '2021-02-20 17:13:29', 1, 'Q1926941'),
(58080, 'Bomdila', 4024, 'AR', 101, 'IN', 27.26475000, 92.42472000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q859141'),
(58081, 'Bongaigaon', 4027, 'AS', 101, 'IN', 26.46030000, 90.64640000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q42197'),
(58083, 'Borkhera', 4014, 'RJ', 101, 'IN', 25.52115000, 75.64028000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q4945504'),
(58084, 'Borsad', 4030, 'GJ', 101, 'IN', 22.40788000, 72.89817000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q858219'),
(58085, 'Botad', 4030, 'GJ', 101, 'IN', 22.16917000, 71.66671000, '2019-10-05 23:03:48', '2020-06-14 15:24:43', 1, 'Q858162'),
(58086, 'Brahmapur', 4013, 'OR', 101, 'IN', 19.31151000, 84.79290000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q860210'),
(58087, 'Brajarajnagar', 4013, 'OR', 101, 'IN', 21.81667000, 83.91667000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q860215'),
(58088, 'Budaun', 4022, 'UP', 101, 'IN', 28.11667000, 78.98333000, '2019-10-05 23:03:48', '2021-06-06 11:41:13', 1, 'Q798839'),
(58089, 'Bodh Gaya', 4037, 'BR', 101, 'IN', 24.69808000, 84.98690000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q176767'),
(58090, 'Budhlada', 4015, 'PB', 101, 'IN', 29.92799000, 75.56205000, '2019-10-05 23:03:48', '2020-07-04 11:24:08', 1, 'Q783187'),
(58091, 'Budhana', 4022, 'UP', 101, 'IN', 29.28805000, 77.47534000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q861968'),
(58092, 'Buguda', 4013, 'OR', 101, 'IN', 19.80806000, 84.79084000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q858514'),
(58093, 'Bulandshahr', 4022, 'UP', 101, 'IN', 28.41667000, 77.83333000, '2019-10-05 23:03:48', '2021-06-06 11:41:13', 1, 'Q861248'),
(58094, 'Buldana', 4008, 'MH', 101, 'IN', 20.58333000, 76.41667000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q1752328'),
(58096, 'Burhanpur', 4039, 'MP', 101, 'IN', 21.31000000, 76.23000000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q2125592'),
(58097, 'Burhar', 4039, 'MP', 101, 'IN', 23.21494000, 81.53204000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q861346'),
(58099, 'Burla', 4013, 'OR', 101, 'IN', 21.50976000, 83.87259000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q1937513'),
(58100, 'Buxar', 4037, 'BR', 101, 'IN', 25.********, 84.10000000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q49161'),
(58101, 'Byndoor', 4026, 'KA', 101, 'IN', 13.86667000, 74.63333000, '2019-10-05 23:03:48', '2019-10-05 23:03:48', 1, 'Q3595293'),
(58102, 'Byadgi', 4026, 'KA', 101, 'IN', 14.67325000, 75.48680000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q858385'),
(58103, 'Babai', 4039, 'MP', 101, 'IN', 22.70256000, 77.93494000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q765367'),
(58104, 'Babra', 4030, 'GJ', 101, 'IN', 21.84577000, 71.30544000, '2019-10-05 23:03:48', '2020-06-14 15:24:46', 1, 'Q8239185'),
(58105, 'Badami', 4026, 'KA', 101, 'IN', 15.91495000, 75.67683000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q8239185'),
(58106, 'Bagh', 4039, 'MP', 101, 'IN', 22.35905000, 74.79052000, '2019-10-05 23:03:48', '2023-03-25 17:22:26', 1, 'Q1924964'),
(58107, 'Bagepalli', 4026, 'KA', 101, 'IN', 13.78338000, 77.79667000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q633164'),
(58109, 'Bagha Purana', 4015, 'PB', 101, 'IN', 30.68809000, 75.09838000, '2019-10-05 23:03:48', '2020-07-04 11:24:13', 1, 'Q625714'),
(58111, 'Bagli', 4039, 'MP', 101, 'IN', 22.64124000, 76.34877000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q2719910'),
(58112, 'Bah', 4022, 'UP', 101, 'IN', 26.86912000, 78.59385000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q633132'),
(58113, 'Bajna', 4022, 'UP', 101, 'IN', 27.89793000, 77.67836000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q2426646'),
(58114, 'Baleshwar', 4013, 'OR', 101, 'IN', 21.********, 86.********, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q2022279'),
(58115, 'Bali', 4014, 'RJ', 101, 'IN', 25.19725000, 73.29117000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q632862'),
(58116, 'Balotra', 4014, 'RJ', 101, 'IN', 25.83242000, 72.24000000, '2019-10-05 23:03:48', '2021-02-20 17:12:44', 1, 'Q638695'),
(58117, 'Balugaon', 4013, 'OR', 101, 'IN', 20.17838000, 85.11327000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q632799'),
(58118, 'Balachor', 4015, 'PB', 101, 'IN', 31.06062000, 76.30166000, '2019-10-05 23:03:49', '2020-07-04 11:24:20', 1, 'Q632799'),
(58119, 'Balaghat', 4039, 'MP', 101, 'IN', 21.96667000, 80.33333000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q641904'),
(58120, 'Balapur', 4012, 'TG', 101, 'IN', 17.31018000, 78.49969000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q641904'),
(58121, 'Balapur', 4008, 'MH', 101, 'IN', 20.66612000, 76.77386000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q2315612'),
(58122, 'Bamor Kalan', 4039, 'MP', 101, 'IN', 24.89298000, 78.15105000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q622611'),
(58123, 'Banapur', 4013, 'OR', 101, 'IN', 19.********, 85.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q632773'),
(58124, 'Banda', 4022, 'UP', 101, 'IN', 25.********, 80.********, '2019-10-05 23:03:49', '2021-06-06 11:41:13', 1, 'Q2575623'),
(58125, 'Bandikui', 4014, 'RJ', 101, 'IN', 27.********, 76.********, '2019-10-05 23:03:49', '2021-02-20 17:14:02', 1, 'Q629203'),
(58126, 'Bangarmau', 4022, 'UP', 101, 'IN', 26.********, 80.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q607333'),
(58128, 'Banki', 4013, 'OR', 101, 'IN', 20.********, 85.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q2563333'),
(58129, 'Banposh', 4013, 'OR', 101, 'IN', 22.********, 84.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q2563333'),
(58130, 'Bansdih', 4022, 'UP', 101, 'IN', 25.********, 84.********, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q637106'),
(58131, 'Bansgaon', 4022, 'UP', 101, 'IN', 26.********, 83.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q625687'),
(58132, 'Bansi', 4022, 'UP', 101, 'IN', 27.********, 82.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q637722'),
(58133, 'Banswada', 4012, 'TG', 101, 'IN', 18.********, 77.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q3428196'),
(58134, 'Banswara', 4014, 'RJ', 101, 'IN', 23.********, 74.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q633308'),
(58135, 'Bantva', 4030, 'GJ', 101, 'IN', 21.********, 70.********, '2019-10-05 23:03:49', '2020-06-14 15:24:49', 1, 'Q625718'),
(58136, 'Banavar', 4026, 'KA', 101, 'IN', 13.********, 76.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q625718'),
(58137, 'Bapatla', 4017, 'AP', 101, 'IN', 15.********, 80.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q633274'),
(58138, 'Bara Banki', 4022, 'UP', 101, 'IN', 26.********, 81.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q633274'),
(58139, 'Baramula', 4029, 'JK', 101, 'IN', 34.********, 74.********, '2019-10-05 23:03:49', '2021-02-20 17:14:02', 1, 'Q633274'),
(58140, 'Bardoli', 4030, 'GJ', 101, 'IN', 21.********, 73.********, '2019-10-05 23:03:49', '2020-06-14 15:24:52', 1, 'Q712003'),
(58141, 'Barh', 4037, 'BR', 101, 'IN', 25.********, 85.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q709313'),
(58142, 'Bari', 4014, 'RJ', 101, 'IN', 26.********, 77.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q707848'),
(58143, 'Barmer', 4014, 'RJ', 101, 'IN', 25.********, 71.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q42016'),
(58144, 'Barsi', 4008, 'MH', 101, 'IN', 18.********, 75.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q42016'),
(58145, 'Baruni', 4037, 'BR', 101, 'IN', 25.********, 85.********, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q42016'),
(58146, 'Baramati', 4008, 'MH', 101, 'IN', 18.15174000, 74.57767000, '2019-10-05 23:03:49', '2021-06-06 13:11:00', 1, 'Q712015'),
(58148, 'Basoda', 4039, 'MP', 101, 'IN', 23.85153000, 77.93652000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q714617'),
(58149, 'Basudebpur', 4013, 'OR', 101, 'IN', 21.11974000, 86.72896000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q712632'),
(58150, 'Basugaon', 4027, 'AS', 101, 'IN', 26.46742000, 90.41951000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q712428'),
(58151, 'Basar', 4024, 'AR', 101, 'IN', 27.99008000, 94.69451000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q712428'),
(58152, 'Bawal', 4007, 'HR', 101, 'IN', 28.07184000, 76.58312000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q759164'),
(58153, 'Bazpur', 4016, 'UK', 101, 'IN', 29.15299000, 79.10814000, '2019-10-05 23:03:49', '2024-02-16 18:35:00', 1, 'Q759164'),
(58154, 'Bidar', 4026, 'KA', 101, 'IN', 18.08333000, 77.33333000, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q1790568'),
(58155, 'Bighapur Khurd', 4022, 'UP', 101, 'IN', 26.34734000, 80.65698000, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q796734'),
(58156, 'Bikaner', 4014, 'RJ', 101, 'IN', 28.01762000, 73.31495000, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q200718'),
(58158, 'Bikapur', 4022, 'UP', 101, 'IN', 26.59534000, 82.13272000, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q795718'),
(58159, 'Bilaspur', 4022, 'UP', 101, 'IN', 28.88655000, 79.27030000, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q795718'),
(58160, 'Birpur', 4037, 'BR', 101, 'IN', 26.50823000, 87.01194000, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q795718'),
(58161, 'Bisalpur', 4022, 'UP', 101, 'IN', 28.29253000, 79.80472000, '2019-10-05 23:03:49', '2021-02-20 17:13:29', 1, 'Q858297'),
(58162, 'Bundi', 4014, 'RJ', 101, 'IN', 25.43855000, 75.63735000, '2019-10-05 23:03:49', '2021-02-20 17:14:02', 1, 'Q862612'),
(58163, 'Bundu', 4025, 'JH', 101, 'IN', 23.16095000, 85.59007000, '2019-10-05 23:03:49', '2021-02-20 17:14:02', 1, 'Q862612'),
(58164, 'Buriya', 4007, 'HR', 101, 'IN', 30.15911000, 77.35814000, '2019-10-05 23:03:49', '2021-02-20 17:14:02', 1, 'Q862612'),
(58165, 'Calangute', 4009, 'GA', 101, 'IN', 15.54390000, 73.75530000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q861976'),
(58166, 'Canacona', 4026, 'KA', 101, 'IN', 14.99590000, 74.05056000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q861976'),
(58167, 'Candolim', 4009, 'GA', 101, 'IN', 15.51807000, 73.76259000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q858424'),
(58168, 'Captainganj', 4022, 'UP', 101, 'IN', 26.92640000, 83.71334000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58169, 'Carapur', 4009, 'GA', 101, 'IN', 15.56588000, 73.98713000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q861379'),
(58170, 'Cavelossim', 4009, 'GA', 101, 'IN', 15.17255000, 73.94194000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58171, 'Central Delhi', 4021, 'DL', 101, 'IN', 28.64857000, 77.21895000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q107941'),
(58172, 'Chail', 4022, 'UP', 101, 'IN', 25.42654000, 81.63198000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58173, 'Chakia', 4022, 'UP', 101, 'IN', 25.04891000, 83.22155000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58174, 'Chaklasi', 4030, 'GJ', 101, 'IN', 22.65320000, 72.94497000, '2019-10-05 23:03:49', '2020-06-14 15:24:55', 1, 'Q607388'),
(58175, 'Chakradharpur', 4025, 'JH', 101, 'IN', 22.67611000, 85.62892000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q278913'),
(58176, 'Chakrata', 4016, 'UK', 101, 'IN', 30.70369000, 77.86386000, '2019-10-05 23:03:49', '2024-02-16 18:35:00', 1, 'Q858555'),
(58177, 'Chaksu', 4014, 'RJ', 101, 'IN', 26.60510000, 75.94814000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q861891'),
(58178, 'Challakere', 4026, 'KA', 101, 'IN', 14.31800000, 76.65165000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q2340109'),
(58179, 'Challapalle', 4017, 'AP', 101, 'IN', 16.11756000, 80.93139000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q2340109'),
(58180, 'Chalala', 4030, 'GJ', 101, 'IN', 21.41073000, 71.16621000, '2019-10-05 23:03:49', '2020-06-14 15:24:57', 1, 'Q858287'),
(58181, 'Chamba', 4020, 'HP', 101, 'IN', 32.57147000, 76.10229000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q1060614'),
(58182, 'Chamoli', 4016, 'UK', 101, 'IN', 30.********, 79.********, '2019-10-05 23:03:49', '2024-02-16 18:35:00', 1, 'Q1797372'),
(58183, 'Champawat', 4016, 'UK', 101, 'IN', 29.28756000, 80.03737000, '2019-10-05 23:03:49', '2024-02-16 18:35:00', 1, 'Q1797372'),
(58184, 'Champhai', 4036, 'MZ', 101, 'IN', 23.47444000, 93.32556000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q1965256'),
(58185, 'Chamrajnagar', 4026, 'KA', 101, 'IN', 11.96000000, 77.09000000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q1965256'),
(58186, 'Chandauli', 4022, 'UP', 101, 'IN', 25.25803000, 83.26825000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58187, 'Chandauli District', 4022, 'UP', 101, 'IN', 25.26134000, 83.26408000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58188, 'Chanderi', 4039, 'MP', 101, 'IN', 24.71312000, 78.13809000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q860192'),
(58189, 'Chandia', 4039, 'MP', 101, 'IN', 23.65647000, 80.70911000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58190, 'Chandigarh', 4031, 'CH', 101, 'IN', 30.73629000, 76.78840000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, '********'),
(58191, 'Chandla', 4039, 'MP', 101, 'IN', 25.07148000, 80.19294000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q858413'),
(58192, 'Chandrapur', 4008, 'MH', 101, 'IN', 20.11793000, 79.44377000, '2019-10-05 23:03:49', '2021-06-06 13:11:00', 1, 'Q861194'),
(58193, 'Chanduasi', 4022, 'UP', 101, 'IN', 28.45178000, 78.78277000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q858672'),
(58194, 'Chandur', 4012, 'TG', 101, 'IN', 17.87455000, 78.10017000, '2019-10-05 23:03:49', '2021-02-20 17:14:02', 1, 'Q721388'),
(58195, 'Changanacheri', 4028, 'KL', 101, 'IN', 9.44203000, 76.53604000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q721388'),
(58196, 'Changlang', 4024, 'AR', 101, 'IN', 27.36265000, 96.34518000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q15427'),
(58197, 'Channagiri', 4026, 'KA', 101, 'IN', 14.02399000, 75.92577000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q15427'),
(58198, 'Channapatna', 4026, 'KA', 101, 'IN', 12.65143000, 77.20672000, '2019-10-05 23:03:49', '2019-10-05 23:03:49', 1, 'Q721354'),
(58199, 'Channarayapatna', 4026, 'KA', 101, 'IN', 12.90642000, 76.38775000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q886632'),
(58200, 'Charkhi Dadri', 4007, 'HR', 101, 'IN', 28.59166000, 76.27161000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q888946'),
(58201, 'Charkhari', 4022, 'UP', 101, 'IN', 25.40304000, 79.74877000, '2019-10-05 23:03:49', '2021-02-20 17:12:44', 1, 'Q886602'),
(58202, 'Paganico Sabino', 1678, '62', 107, 'IT', 42.18963000, 12.99689000, '2019-10-05 23:06:39', '2024-12-21 12:57:42', 1, 'Q224291'),
(58203, 'Pagazzano', 1705, '25', 107, 'IT', 45.53428000, 9.67116000, '2019-10-05 23:06:39', '2024-12-21 12:57:43', 1, 'Q101346'),
(58204, 'Pagliara', 1709, '82', 107, 'IT', 37.98546000, 15.35969000, '2019-10-05 23:06:39', '2024-12-21 12:57:44', 1, 'Q492615'),
(58205, 'Pagliare', 1679, '65', 107, 'IT', 42.64372000, 13.95171000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18428397'),
(58206, 'Pagliare', 1670, '57', 107, 'IT', 42.86906000, 13.76906000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18496595'),
(58207, 'Pagliarelle', 1703, '78', 107, 'IT', 39.14017000, 16.75101000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3888959'),
(58208, 'Pagliarone', 1669, '72', 107, 'IT', 40.63006000, 14.91325000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58209, 'Paglieta', 1679, '65', 107, 'IT', 42.16139000, 14.50306000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58210, 'Pagnacco', 1756, '36', 107, 'IT', 46.11462000, 13.18377000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58211, 'Pagno', 1702, '21', 107, 'IT', 44.61173000, 7.42572000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58212, 'Pagnona', 1705, '25', 107, 'IT', 46.05969000, 9.40274000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58213, 'Pago Veiano', 1669, '72', 107, 'IT', 41.24243000, 14.86497000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58214, 'Pago del Vallo di Lauro', 1669, '72', 107, 'IT', 40.89721000, 14.60764000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58215, 'Paisco Loveno', 1705, '25', 107, 'IT', 46.07910000, 10.29256000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58216, 'Paitone', 1705, '25', 107, 'IT', 45.55208000, 10.40227000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58217, 'Paladina', 1705, '25', 107, 'IT', 45.73068000, 9.60585000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58218, 'Palagano', 1773, '45', 107, 'IT', 44.32152000, 10.64660000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58219, 'Palagianello', 1688, '75', 107, 'IT', 40.60851000, 16.97802000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58220, 'Palagiano', 1688, '75', 107, 'IT', 40.57762000, 17.03812000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58221, 'Palagonia', 1709, '82', 107, 'IT', 37.32955000, 14.74474000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58222, 'Palaia', 1664, '52', 107, 'IT', 43.60567000, 10.77020000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58223, 'Palanzano', 1773, '45', 107, 'IT', 44.43629000, 10.19400000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58224, 'Palata', 1695, '67', 107, 'IT', 41.88790000, 14.78778000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58225, 'Palau', 1715, '88', 107, 'IT', 41.17936000, 9.38190000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58226, 'Palazzago', 1705, '25', 107, 'IT', 45.75298000, 9.53365000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18437567'),
(58227, 'Palazzo', 1683, '55', 107, 'IT', 43.09708000, 12.56436000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3889493'),
(58228, 'Palazzo Adriano', 1709, '82', 107, 'IT', 37.68066000, 13.37941000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3889493'),
(58229, 'Palazzo Canavese', 1702, '21', 107, 'IT', 45.45962000, 7.97776000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3889493'),
(58230, 'Palazzo Pignano', 1705, '25', 107, 'IT', 45.39007000, 9.56956000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3889493'),
(58231, 'Palazzo San Gervasio', 1706, '77', 107, 'IT', 40.93156000, 15.98149000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3889493'),
(58232, 'Palazzolo', 1709, '82', 107, 'IT', 37.56224000, 14.92987000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58233, 'Palazzolo Acreide', 1709, '82', 107, 'IT', 37.06261000, 14.90593000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58234, 'Palazzolo Vercellese', 1702, '21', 107, 'IT', 45.18554000, 8.23302000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58235, 'Palazzolo dello Stella', 1756, '36', 107, 'IT', 45.79823000, 13.08776000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58236, 'Palazzolo sull\'Oglio', 1705, '25', 107, 'IT', 45.59665000, 9.88688000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58237, 'Palazzuolo sul Senio', 1664, '52', 107, 'IT', 44.11298000, 11.54270000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58238, 'Palena', 1679, '65', 107, 'IT', 41.98353000, 14.13696000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58239, 'Palermiti', 1703, '78', 107, 'IT', 38.74868000, 16.45220000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18461454'),
(58240, 'Palermo', 1709, '82', 107, 'IT', 37.81667000, 13.58333000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q20795016'),
(58241, 'Palestrina', 1678, '62', 107, 'IT', 41.83274000, 12.88178000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q20795016'),
(58242, 'Palestro', 1705, '25', 107, 'IT', 45.30302000, 8.53320000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q20795016'),
(58243, 'Paliano', 1678, '62', 107, 'IT', 41.77975000, 13.07661000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q20795016'),
(58244, 'Palidano', 1705, '25', 107, 'IT', 44.97203000, 10.77943000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3891766'),
(58245, 'Palinuro', 1669, '72', 107, 'IT', 40.03681000, 15.28812000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q769262'),
(58246, 'Palizzi', 1703, '78', 107, 'IT', 37.96667000, 15.98670000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q769262'),
(58247, 'Palizzi Marina', 1703, '78', 107, 'IT', 37.91995000, 15.97927000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18432941'),
(58248, 'Pallagorio', 1703, '78', 107, 'IT', 39.30764000, 16.90816000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18432941'),
(58249, 'Pallanza-Intra-Suna', 1702, '21', 107, 'IT', 45.93778000, 8.57088000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18446175'),
(58250, 'Pallanzeno', 1702, '21', 107, 'IT', 46.04202000, 8.25949000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18446175'),
(58251, 'Pallare', 1768, '42', 107, 'IT', 44.32770000, 8.27864000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18446175'),
(58252, 'Pallerone', 1664, '52', 107, 'IT', 44.20485000, 10.00279000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58253, 'Palma Campania', 1669, '72', 107, 'IT', 40.86608000, 14.55170000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58254, 'Palma di Montechiaro', 1709, '82', 107, 'IT', 37.19066000, 13.76603000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58255, 'Palmanova', 1756, '36', 107, 'IT', 45.90540000, 13.30998000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58256, 'Palmariggi', 1688, '75', 107, 'IT', 40.13099000, 18.37863000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58257, 'Palmas Arborea', 1715, '88', 107, 'IT', 39.87670000, 8.64391000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58258, 'Palmi', 1703, '78', 107, 'IT', 38.35943000, 15.85155000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58259, 'Palmiano', 1670, '57', 107, 'IT', 42.89946000, 13.45937000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58260, 'Palmoli', 1679, '65', 107, 'IT', 41.93944000, 14.58142000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58261, 'Palo del Colle', 1688, '75', 107, 'IT', 41.05562000, 16.70321000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18489720'),
(58262, 'Palombaio', 1688, '75', 107, 'IT', 41.07036000, 16.60962000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3155454'),
(58263, 'Palombara Sabina', 1678, '62', 107, 'IT', 42.06909000, 12.76684000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3155454'),
(58264, 'Palombaro', 1679, '65', 107, 'IT', 42.12323000, 14.22989000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3155454'),
(58265, 'Palomonte', 1669, '72', 107, 'IT', 40.66251000, 15.29186000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3155454'),
(58266, 'Palosco', 1705, '25', 107, 'IT', 45.58928000, 9.83646000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q3155454'),
(58267, 'Paludea', 1756, '36', 107, 'IT', 46.19950000, 12.90344000, '2019-10-05 23:06:39', '2019-10-05 23:06:39', 1, 'Q18448976'),
(58268, 'Paludi', 1703, '78', 107, 'IT', 39.52943000, 16.67963000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58269, 'Paluzza', 1756, '36', 107, 'IT', 46.53202000, 13.01586000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58270, 'Palù', 1753, '34', 107, 'IT', 45.32473000, 11.15635000, '2019-10-05 23:06:40', '2020-05-01 17:22:57', 1, 'Q18448976'),
(58271, 'Palù del Fersina', 1725, '32', 107, 'IT', 46.12951000, 11.35088000, '2019-10-05 23:06:40', '2020-05-01 17:22:57', 1, 'Q18448976'),
(58272, 'Pamparato', 1702, '21', 107, 'IT', 44.27666000, 7.91432000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58273, 'Pancalieri', 1702, '21', 107, 'IT', 44.83344000, 7.58592000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58274, 'Pancarana', 1705, '25', 107, 'IT', 45.07523000, 9.05109000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58275, 'Panchià', 1725, '32', 107, 'IT', 46.28648000, 11.54061000, '2019-10-05 23:06:40', '2020-05-01 17:22:57', 1, 'Q18448976'),
(58276, 'Pandino', 1705, '25', 107, 'IT', 45.40559000, 9.55218000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58277, 'Panettieri', 1703, '78', 107, 'IT', 39.06007000, 16.45375000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58278, 'Panicale', 1683, '55', 107, 'IT', 43.02830000, 12.09993000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448976'),
(58279, 'Panighina', 1773, '45', 107, 'IT', 44.17162000, 12.16324000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18440168'),
(58280, 'Pannaconi', 1703, '78', 107, 'IT', 38.69291000, 16.04411000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18433996'),
(58281, 'Pannarano', 1669, '72', 107, 'IT', 41.01082000, 14.70293000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18433996'),
(58282, 'Panni', 1688, '75', 107, 'IT', 41.22082000, 15.27560000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18433996'),
(58283, 'Pantalla', 1683, '55', 107, 'IT', 42.87287000, 12.39861000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18493322'),
(58284, 'Pantelleria', 1709, '82', 107, 'IT', 36.82836000, 11.94611000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18493322'),
(58285, 'Pantigliate', 1705, '25', 107, 'IT', 45.43526000, 9.35220000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18493322'),
(58286, 'Panzano in Chianti', 1664, '52', 107, 'IT', 43.54422000, 11.31438000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3893502'),
(58287, 'Paola', 1703, '78', 107, 'IT', 39.36313000, 16.03691000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3893502'),
(58288, 'Paolisi', 1669, '72', 107, 'IT', 41.03731000, 14.57918000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3893502'),
(58289, 'Paolo VI', 1688, '75', 107, 'IT', 40.52218000, 17.27401000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448209'),
(58290, 'Papanice', 1703, '78', 107, 'IT', 39.07077000, 17.02721000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3894683'),
(58291, 'Papasidero', 1703, '78', 107, 'IT', 39.87159000, 15.90581000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3894683'),
(58292, 'Papozze', 1753, '34', 107, 'IT', 44.98578000, 12.03092000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3894683'),
(58293, 'Paquier', 1716, '23', 107, 'IT', 45.87879000, 7.62575000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18495750'),
(58294, 'Parabiago', 1705, '25', 107, 'IT', 45.56034000, 8.94545000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18491642'),
(58295, 'Parabita', 1688, '75', 107, 'IT', 40.05139000, 18.12651000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18491642'),
(58296, 'Paratico', 1705, '25', 107, 'IT', 45.65879000, 9.95792000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18491642'),
(58297, 'Paravati', 1703, '78', 107, 'IT', 38.59020000, 16.05815000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18434017'),
(58298, 'Parcines', 1725, '32', 107, 'IT', 46.68422000, 11.07337000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18434017'),
(58299, 'Parco Leonardo', 1678, '62', 107, 'IT', 41.80035000, 12.30028000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18454957'),
(58300, 'Parco Scizzo-Parchitello', 1688, '75', 107, 'IT', 41.07457000, 16.99329000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58301, 'Parella', 1702, '21', 107, 'IT', 45.43018000, 7.79128000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58302, 'Parenti', 1703, '78', 107, 'IT', 39.16071000, 16.41140000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58303, 'Parete', 1669, '72', 107, 'IT', 40.95891000, 14.16193000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58304, 'Pareto', 1702, '21', 107, 'IT', 44.51644000, 8.38185000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58305, 'Parghelia', 1703, '78', 107, 'IT', 38.68189000, 15.92075000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58306, 'Parlasco', 1705, '25', 107, 'IT', 46.01789000, 9.34494000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58307, 'Parma', 1773, '45', 107, 'IT', 44.79935000, 10.32618000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18447166'),
(58308, 'Parmezzana Calzana', 1705, '25', 107, 'IT', 45.63350000, 10.09540000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18475668'),
(58309, 'Parodi Ligure', 1702, '21', 107, 'IT', 44.66975000, 8.75865000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18475668'),
(58310, 'Paroldo', 1702, '21', 107, 'IT', 44.43204000, 8.07244000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18475668'),
(58311, 'Parolise', 1669, '72', 107, 'IT', 40.93045000, 14.88172000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18475668'),
(58312, 'Parona', 1753, '34', 107, 'IT', 45.47882000, 10.94414000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58313, 'Parona', 1705, '25', 107, 'IT', 45.28165000, 8.75055000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58314, 'Parrano', 1683, '55', 107, 'IT', 42.86624000, 12.11026000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58315, 'Parre', 1705, '25', 107, 'IT', 45.87459000, 9.89086000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58316, 'Partanna', 1709, '82', 107, 'IT', 37.72680000, 12.88917000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58317, 'Partinico', 1709, '82', 107, 'IT', 38.04657000, 13.11785000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58318, 'Paruzzaro', 1702, '21', 107, 'IT', 45.74818000, 8.51486000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58319, 'Parzanica', 1705, '25', 107, 'IT', 45.73873000, 10.03518000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2703485'),
(58320, 'Parè', 1705, '25', 107, 'IT', 45.81088000, 9.00874000, '2019-10-05 23:06:40', '2020-05-01 17:22:56', 1, 'Q2703485'),
(58321, 'Pascarola', 1669, '72', 107, 'IT', 40.97643000, 14.30500000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18288832'),
(58322, 'Pasian di Prato', 1756, '36', 107, 'IT', 46.04667000, 13.18780000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18288832'),
(58323, 'Pasiano', 1756, '36', 107, 'IT', 45.84972000, 12.62722000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448988'),
(58324, 'Paspardo', 1705, '25', 107, 'IT', 46.03225000, 10.36879000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18448988'),
(58325, 'Passaggio', 1683, '55', 107, 'IT', 43.02239000, 12.50781000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18493339'),
(58326, 'Passarella', 1753, '34', 107, 'IT', 45.59436000, 12.60537000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3897230'),
(58327, 'Passerano Marmorito', 1702, '21', 107, 'IT', 45.05585000, 8.01873000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3897230'),
(58328, 'Passignano sul Trasimeno', 1683, '55', 107, 'IT', 43.19043000, 12.13535000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3897230'),
(58329, 'Passirana', 1705, '25', 107, 'IT', 45.54792000, 9.04412000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2118705'),
(58330, 'Passirano', 1705, '25', 107, 'IT', 45.59673000, 10.06986000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2118705'),
(58331, 'Passo Ripe', 1670, '57', 107, 'IT', 43.65567000, 13.12196000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18496008'),
(58332, 'Passo di Mirabella-Pianopantano', 1669, '72', 107, 'IT', 41.05468000, 15.01649000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18435572'),
(58333, 'Passo di Treia', 1670, '57', 107, 'IT', 43.28647000, 13.33246000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18497597'),
(58334, 'Passons', 1756, '36', 107, 'IT', 46.06889000, 13.18917000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452385'),
(58335, 'Passoscuro', 1678, '62', 107, 'IT', 41.90209000, 12.15723000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3897333'),
(58336, 'Pasta', 1702, '21', 107, 'IT', 45.01131000, 7.55243000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18443960'),
(58337, 'Pastena', 1678, '62', 107, 'IT', 41.46842000, 13.49111000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18443960'),
(58338, 'Pasteria-Lapide', 1709, '82', 107, 'IT', 37.81012000, 15.22818000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58339, 'Pastorano', 1669, '72', 107, 'IT', 41.18082000, 14.19823000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58340, 'Pastrengo', 1753, '34', 107, 'IT', 45.49588000, 10.79897000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58341, 'Pasturana', 1702, '21', 107, 'IT', 44.75085000, 8.74925000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58342, 'Pasturo', 1705, '25', 107, 'IT', 45.95168000, 9.44414000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58343, 'Paterno', 1706, '77', 107, 'IT', 40.37507000, 15.73510000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58344, 'Paterno Calabro', 1703, '78', 107, 'IT', 39.22849000, 16.26489000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58345, 'Paternopoli', 1669, '72', 107, 'IT', 40.97268000, 15.03242000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18461464'),
(58346, 'Paternò', 1709, '82', 107, 'IT', 37.56675000, 14.90254000, '2019-10-05 23:06:40', '2020-05-01 17:22:57', 1, 'Q18461464'),
(58347, 'Patigno', 1664, '52', 107, 'IT', 44.35316000, 9.76297000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58348, 'Patrica', 1678, '62', 107, 'IT', 41.59085000, 13.24363000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58349, 'Pattada', 1715, '88', 107, 'IT', 40.58067000, 9.11129000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58350, 'Patti', 1709, '82', 107, 'IT', 38.14736000, 14.96409000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58351, 'Patù', 1688, '75', 107, 'IT', 39.84078000, 18.33784000, '2019-10-05 23:06:40', '2020-05-01 17:22:55', 1, 'Q18489737'),
(58352, 'Pau', 1715, '88', 107, 'IT', 39.79167000, 8.80222000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58353, 'Paularo', 1756, '36', 107, 'IT', 46.53101000, 13.11886000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58354, 'Pauli Arbarei', 1715, '88', 107, 'IT', 39.66183000, 8.92212000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58355, 'Paulilatino', 1715, '88', 107, 'IT', 40.08470000, 8.76449000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58356, 'Paullo', 1705, '25', 107, 'IT', 45.41834000, 9.40042000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58357, 'Paupisi', 1669, '72', 107, 'IT', 41.19662000, 14.66457000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58358, 'Pavarolo', 1702, '21', 107, 'IT', 45.06665000, 7.83943000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58359, 'Pavia', 1705, '25', 107, 'IT', 45.19205000, 9.15917000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58360, 'Pavia di Udine', 1756, '36', 107, 'IT', 45.99502000, 13.30367000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18489737'),
(58361, 'Pavigliana', 1703, '78', 107, 'IT', 38.10721000, 15.72108000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18432957'),
(58362, 'Pavona', 1678, '62', 107, 'IT', 41.72629000, 12.61642000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2085595'),
(58363, 'Pavone Canavese', 1702, '21', 107, 'IT', 45.44193000, 7.85294000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2085595'),
(58364, 'Pavone del Mella', 1705, '25', 107, 'IT', 45.30147000, 10.20984000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2085595'),
(58365, 'Pavullo nel Frignano', 1773, '45', 107, 'IT', 44.33352000, 10.83544000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2085595'),
(58366, 'Pazzano', 1703, '78', 107, 'IT', 38.47612000, 16.45107000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2085595'),
(58367, 'Peccioli', 1664, '52', 107, 'IT', 43.54963000, 10.71720000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2085595'),
(58368, 'Pecco', 1702, '21', 107, 'IT', 45.45206000, 7.77742000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2085595'),
(58369, 'Pecetto', 1702, '21', 107, 'IT', 45.01760000, 7.75107000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18443969'),
(58370, 'Pecetto di Valenza', 1702, '21', 107, 'IT', 44.98904000, 8.66996000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18443969'),
(58371, 'Pecorara', 1773, '45', 107, 'IT', 44.87582000, 9.38387000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18445082'),
(58372, 'Pedace-Perito', 1703, '78', 107, 'IT', 39.27639000, 16.33639000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18430022'),
(58373, 'Pedagaggi', 1709, '82', 107, 'IT', 37.19182000, 14.93654000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q770694'),
(58374, 'Pedalino', 1709, '82', 107, 'IT', 37.02320000, 14.58116000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2232790'),
(58375, 'Pedara', 1709, '82', 107, 'IT', 37.62386000, 15.05672000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2232790'),
(58376, 'Pedaso', 1670, '57', 107, 'IT', 43.09839000, 13.84083000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2232790'),
(58377, 'Pedavena', 1753, '34', 107, 'IT', 46.03951000, 11.88310000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2232790'),
(58378, 'Pedemonte', 1753, '34', 107, 'IT', 45.90840000, 11.30859000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2232790'),
(58379, 'Pedemonte', 1768, '42', 107, 'IT', 44.50307000, 8.92614000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18458484'),
(58380, 'Pederiva', 1753, '34', 107, 'IT', 45.42254000, 11.46568000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58381, 'Pederobba', 1753, '34', 107, 'IT', 45.87567000, 11.94977000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58382, 'Pedesina', 1705, '25', 107, 'IT', 46.08189000, 9.54944000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58383, 'Pedivigliano', 1703, '78', 107, 'IT', 39.11034000, 16.30486000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58384, 'Pedrengo', 1705, '25', 107, 'IT', 45.69598000, 9.73495000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58385, 'Peglio', 1670, '57', 107, 'IT', 43.69646000, 12.49785000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58386, 'Peglio', 1705, '25', 107, 'IT', 46.16039000, 9.29474000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58387, 'Pegognaga', 1705, '25', 107, 'IT', 44.99456000, 10.85967000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18506658'),
(58388, 'Pegolotte', 1753, '34', 107, 'IT', 45.19178000, 12.03791000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18503505'),
(58389, 'Peia', 1705, '25', 107, 'IT', 45.79949000, 9.89926000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18503505'),
(58390, 'Pelago', 1664, '52', 107, 'IT', 43.77255000, 11.50148000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18503505'),
(58391, 'Pella', 1702, '21', 107, 'IT', 45.79722000, 8.38444000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18503505'),
(58392, 'Pellaro', 1703, '78', 107, 'IT', 38.01667000, 15.65000000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3898899'),
(58393, 'Pellegrino Parmense', 1773, '45', 107, 'IT', 44.72950000, 9.93240000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3898899'),
(58394, 'Pellestrina', 1753, '34', 107, 'IT', 45.27716000, 12.30238000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q686709'),
(58395, 'Pellezzano', 1669, '72', 107, 'IT', 40.72521000, 14.75744000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q686709'),
(58396, 'Pellio Intelvi', 1705, '25', 107, 'IT', 45.97988000, 9.05864000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q686709'),
(58397, 'Pellizzano', 1725, '32', 107, 'IT', 46.30980000, 10.75790000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q686709'),
(58398, 'Pelugo', 1725, '32', 107, 'IT', 46.08850000, 10.72387000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q686709'),
(58399, 'Penango', 1702, '21', 107, 'IT', 45.03295000, 8.25174000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q686709'),
(58400, 'Pendino', 1669, '72', 107, 'IT', 40.84708000, 14.26321000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q686709'),
(58401, 'Penitro', 1678, '62', 107, 'IT', 41.27328000, 13.69262000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58402, 'Penna San Giovanni', 1670, '57', 107, 'IT', 43.05608000, 13.42539000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58403, 'Penna Sant\'Andrea', 1679, '65', 107, 'IT', 42.59403000, 13.77215000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58404, 'Penna in Teverina', 1683, '55', 107, 'IT', 42.49342000, 12.35845000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58405, 'Pennabilli', 1773, '45', 107, 'IT', 43.81746000, 12.26708000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58406, 'Pennadomo', 1679, '65', 107, 'IT', 42.00394000, 14.32338000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58407, 'Pennapiedimonte', 1679, '65', 107, 'IT', 42.15563000, 14.19432000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58408, 'Penne', 1679, '65', 107, 'IT', 42.45474000, 13.92754000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18453835'),
(58409, 'Pennisi', 1709, '82', 107, 'IT', 37.64945000, 15.12385000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2513042'),
(58410, 'Pentone', 1703, '78', 107, 'IT', 38.98579000, 16.58250000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2513042'),
(58411, 'Perano', 1679, '65', 107, 'IT', 42.10460000, 14.39581000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2513042'),
(58412, 'Perarolo di Cadore', 1753, '34', 107, 'IT', 46.39383000, 12.35636000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2513042'),
(58413, 'Perca', 1725, '32', 107, 'IT', 46.79343000, 11.98339000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2513042'),
(58414, 'Percile', 1678, '62', 107, 'IT', 42.09497000, 12.91048000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2513042'),
(58415, 'Percoto', 1756, '36', 107, 'IT', 45.97471000, 13.32304000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452390'),
(58416, 'Perdasdefogu', 1715, '88', 107, 'IT', 39.67959000, 9.44082000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452390'),
(58417, 'Perdaxius', 1715, '88', 107, 'IT', 39.16028000, 8.61083000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452390'),
(58418, 'Perdifumo', 1669, '72', 107, 'IT', 40.26694000, 15.01652000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452390'),
(58419, 'Perego', 1705, '25', 107, 'IT', 45.73758000, 9.36295000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58420, 'Pereto', 1679, '65', 107, 'IT', 42.05821000, 13.10231000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58421, 'Perfugas', 1715, '88', 107, 'IT', 40.83231000, 8.88354000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58422, 'Pergine Valdarno', 1664, '52', 107, 'IT', 43.47028000, 11.68552000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58423, 'Pergine Valsugana', 1725, '32', 107, 'IT', 46.06434000, 11.23758000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58424, 'Pergola', 1670, '57', 107, 'IT', 43.55485000, 12.83633000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58425, 'Perinaldo', 1768, '42', 107, 'IT', 43.86689000, 7.67194000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58426, 'Perito', 1669, '72', 107, 'IT', 40.29854000, 15.14746000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58427, 'Perledo', 1705, '25', 107, 'IT', 46.01529000, 9.29560000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58428, 'Perletto', 1702, '21', 107, 'IT', 44.59934000, 8.21304000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58429, 'Perlo', 1702, '21', 107, 'IT', 44.33183000, 8.08554000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58430, 'Perloz', 1716, '23', 107, 'IT', 45.61386000, 7.80811000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q42801'),
(58431, 'Pernate', 1702, '21', 107, 'IT', 45.45958000, 8.67845000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q3900017'),
(58432, 'Pernocari-Presinaci', 1703, '78', 107, 'IT', 38.61184000, 16.00463000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18434031'),
(58433, 'Pernumia', 1753, '34', 107, 'IT', 45.25899000, 11.78721000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18434031'),
(58434, 'Pero', 1753, '34', 107, 'IT', 45.70635000, 12.34867000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18502186'),
(58435, 'Pero', 1705, '25', 107, 'IT', 45.51017000, 9.08704000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18502186'),
(58436, 'Perosa Argentina', 1702, '21', 107, 'IT', 44.95909000, 7.19167000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18502186'),
(58437, 'Perosa Canavese', 1702, '21', 107, 'IT', 45.39706000, 7.83082000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18502186'),
(58438, 'Perrero', 1702, '21', 107, 'IT', 44.93849000, 7.11263000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18502186'),
(58439, 'Persico Dosimo', 1705, '25', 107, 'IT', 45.18587000, 10.10517000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18502186'),
(58440, 'Pertegada', 1756, '36', 107, 'IT', 45.72179000, 13.04379000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58441, 'Pertengo', 1702, '21', 107, 'IT', 45.23606000, 8.41651000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58442, 'Pertica Alta', 1705, '25', 107, 'IT', 45.74239000, 10.34417000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58443, 'Pertica Bassa', 1705, '25', 107, 'IT', 45.75339000, 10.37247000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58444, 'Pertosa', 1669, '72', 107, 'IT', 40.54346000, 15.45009000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58445, 'Pertusio', 1702, '21', 107, 'IT', 45.35565000, 7.64152000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58446, 'Perugia', 1683, '55', 107, 'IT', 43.11220000, 12.38878000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58447, 'Pesaro', 1670, '57', 107, 'IT', 43.90921000, 12.91640000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58448, 'Pescaglia', 1664, '52', 107, 'IT', 43.96517000, 10.41292000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58449, 'Pescantina', 1753, '34', 107, 'IT', 45.48548000, 10.86796000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58450, 'Pescara', 1679, '65', 107, 'IT', 42.45840000, 14.20283000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58451, 'Pescarolo', 1705, '25', 107, 'IT', 45.19387000, 10.18647000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58452, 'Pescasseroli', 1679, '65', 107, 'IT', 41.80343000, 13.78707000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58453, 'Pescate', 1705, '25', 107, 'IT', 45.83348000, 9.39395000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58454, 'Pesche', 1695, '67', 107, 'IT', 41.60552000, 14.27620000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58455, 'Peschici', 1688, '75', 107, 'IT', 41.94521000, 16.01612000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58456, 'Peschiera del Garda', 1753, '34', 107, 'IT', 45.43912000, 10.68614000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58457, 'Pescia', 1664, '52', 107, 'IT', 43.88710000, 10.68849000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q18452392'),
(58458, 'Pescia Romana', 1678, '62', 107, 'IT', 42.39963000, 11.49651000, '2019-10-05 23:06:40', '2019-10-05 23:06:40', 1, 'Q2784233');

