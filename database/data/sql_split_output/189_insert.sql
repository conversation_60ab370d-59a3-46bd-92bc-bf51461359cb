INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(96038, '<PERSON><PERSON><PERSON>', 4729, 'P<PERSON>', 181, 'R<PERSON>', 44.95438000, 26.12841000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q12146934'),
(96039, '<PERSON><PERSON><PERSON>', 4745, '<PERSON>', 181, 'RO', 44.81791000, 25.44106000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q3726585'),
(96040, '<PERSON><PERSON><PERSON>', 4744, '<PERSON>', 181, 'RO', 46.70000000, 26.75000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q15758000'),
(96041, '<PERSON><PERSON><PERSON>ţ<PERSON>', 4736, '<PERSON>', 181, '<PERSON><PERSON>', 45.30000000, 27.45000000, '2019-10-05 23:15:39', '2020-05-01 17:23:08', 1, 'Q2780051'),
(96042, '<PERSON><PERSON><PERSON><PERSON>a', 4757, 'VL', 181, 'RO', 45.41667000, 24.31667000, '2019-10-05 23:15:39', '2020-05-01 17:23:12', 1, 'Q2332005'),
(96043, 'Racovi<PERSON>a', 4748, 'TM', 181, 'RO', 45.69833000, 21.63583000, '2019-10-05 23:15:39', '2020-05-01 17:23:11', 1, 'Q1084339'),
(96044, 'Racoviţa', 4755, 'SB', 181, 'RO', 45.68333000, 24.35000000, '2019-10-05 23:15:39', '2020-05-01 17:23:11', 1, 'Q1087571'),
(96045, 'Racoviţeni', 4756, 'BZ', 181, 'RO', 45.36667000, 26.90000000, '2019-10-05 23:15:39', '2020-05-01 17:23:09', 1, 'Q13542261'),
(96046, 'Racovița', 4722, 'AG', 181, 'RO', 44.97081000, 24.97582000, '2019-10-05 23:15:39', '2020-05-01 17:23:08', 1, 'Q12146910'),
(96047, 'Racovița', 4745, 'DB', 181, 'RO', 44.83927000, 25.62082000, '2019-10-05 23:15:39', '2020-05-01 17:23:09', 1, 'Q12146914'),
(96048, 'Racoș', 4759, 'BV', 181, 'RO', 46.02524000, 25.41075000, '2019-10-05 23:15:39', '2020-05-01 17:23:08', 1, 'Q3416330'),
(96049, 'Racu', 4749, 'HR', 181, 'RO', 46.45178000, 25.76060000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q875975'),
(96050, 'Racşa', 4746, 'SM', 181, 'RO', 47.82068000, 23.33247000, '2019-10-05 23:15:39', '2020-05-01 17:23:11', 1, 'Q836528'),
(96051, 'Radna', 4739, 'AR', 181, 'RO', 46.09444000, 21.68732000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q896496'),
(96052, 'Radomir', 4742, 'DJ', 181, 'RO', 44.12115000, 24.16832000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12146630'),
(96053, 'Radomireşti', 4738, 'OT', 181, 'RO', 44.11667000, 24.68333000, '2019-10-05 23:15:39', '2020-05-01 17:23:11', 1, 'Q2719496'),
(96054, 'Radovan', 4742, 'DJ', 181, 'RO', 44.16667000, 23.61667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2606367'),
(96055, 'Radovanu', 4732, 'CL', 181, 'RO', 44.20000000, 26.51667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12146615'),
(96056, 'Radu Vodă', 4732, 'CL', 181, 'RO', 44.38563000, 26.92541000, '2019-10-05 23:15:39', '2020-05-01 17:23:09', 1, 'Q12146645'),
(96057, 'Rafaila', 4752, 'VS', 181, 'RO', 46.80064000, 27.36236000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1402731'),
(96058, 'Ramna', 4753, 'CS', 181, 'RO', 45.43833000, 21.68833000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q75416'),
(96059, 'Rapoltu Mare', 4721, 'HD', 181, 'RO', 45.86667000, 23.06667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q5053333'),
(96060, 'Rasa', 4732, 'CL', 181, 'RO', 44.21404000, 27.14717000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12146987'),
(96061, 'Rasova', 4737, 'CT', 181, 'RO', 44.24616000, 27.93466000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1077665'),
(96062, 'Rast', 4742, 'DJ', 181, 'RO', 43.88333000, 23.28333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q972291'),
(96063, 'Rași', 4743, 'IL', 181, 'RO', 44.53534000, 26.92485000, '2019-10-05 23:15:39', '2020-05-01 17:23:10', 1, 'Q12147088'),
(96064, 'Rebra', 4733, 'BN', 181, 'RO', 47.31667000, 24.50000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q383156'),
(96065, 'Rebricea', 4752, 'VS', 181, 'RO', 46.86667000, 27.55000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2547550'),
(96066, 'Rebrişoara', 4733, 'BN', 181, 'RO', 47.28333000, 24.45000000, '2019-10-05 23:15:39', '2020-05-01 17:23:08', 1, 'Q2470249'),
(96067, 'Recaş', 4748, 'TM', 181, 'RO', 45.79889000, 21.50083000, '2019-10-05 23:15:39', '2020-05-01 17:23:11', 1, 'Q16898600'),
(96068, 'Recea', 4751, 'MH', 181, 'RO', 44.31949000, 22.91659000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12147777'),
(96069, 'Recea', 4759, 'BV', 181, 'RO', 45.71334000, 24.93152000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1043874'),
(96070, 'Recea', 4760, 'MM', 181, 'RO', 47.63333000, 23.51667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1160476'),
(96071, 'Recea', 4722, 'AG', 181, 'RO', 44.53333000, 25.01667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2486652'),
(96072, 'Recea Cristur', 4734, 'CJ', 181, 'RO', 47.06667000, 23.53333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1011647'),
(96073, 'Reci', 4754, 'CV', 181, 'RO', 45.85000000, 25.93333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q14253317'),
(96074, 'Redea', 4738, 'OT', 181, 'RO', 44.05000000, 24.30000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2719501'),
(96075, 'Rediu', 4735, 'IS', 181, 'RO', 47.21667000, 27.50000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2607423'),
(96076, 'Rediu', 4731, 'NT', 181, 'RO', 46.75598000, 26.56254000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2542059'),
(96077, 'Rediu', 4752, 'VS', 181, 'RO', 46.63374000, 27.69439000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12147348'),
(96078, 'Rediu', 4740, 'BT', 181, 'RO', 48.23075000, 26.77841000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12147349'),
(96079, 'Rediu', 4747, 'GL', 181, 'RO', 45.71667000, 27.83333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2291376'),
(96080, 'Reghiu', 4758, 'VN', 181, 'RO', 45.78333000, 26.83333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12147281'),
(96081, 'Remetea', 4749, 'HR', 181, 'RO', 46.78333000, 25.45000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1029387'),
(96082, 'Remetea', 4723, 'BH', 181, 'RO', 46.73333000, 22.35000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1187377'),
(96083, 'Remetea Chioarului', 4760, 'MM', 181, 'RO', 47.53333000, 23.55000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1126860'),
(96084, 'Remetea Mare', 4748, 'TM', 181, 'RO', 45.77944000, 21.37611000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1195047'),
(96085, 'Remeţi', 4760, 'MM', 181, 'RO', 47.98333000, 23.63333000, '2019-10-05 23:15:39', '2020-05-01 17:23:10', 1, 'Q1093653'),
(96086, 'Remuș', 4726, 'GR', 181, 'RO', 43.94295000, 25.98039000, '2019-10-05 23:15:39', '2020-05-01 17:23:10', 1, 'Q12147567'),
(96087, 'Repedea', 4760, 'MM', 181, 'RO', 47.83333000, 24.40000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1189083'),
(96088, 'Reteag', 4733, 'BN', 181, 'RO', 47.19582000, 24.01862000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q576889'),
(96089, 'Retevoiești', 4722, 'AG', 181, 'RO', 45.14016000, 24.83797000, '2019-10-05 23:15:39', '2020-05-01 17:23:08', 1, 'Q12147719'),
(96090, 'Reviga', 4743, 'IL', 181, 'RO', 44.71667000, 27.10000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12725086'),
(96091, 'Reşiţa', 4753, 'CS', 181, 'RO', 45.30083000, 21.88917000, '2019-10-05 23:15:39', '2020-05-01 17:23:09', 1, 'Q12725086'),
(96092, 'Ribiţa', 4721, 'HD', 181, 'RO', 46.18333000, 22.76667000, '2019-10-05 23:15:39', '2020-05-01 17:23:10', 1, 'Q1790317'),
(96093, 'Rieni', 4723, 'BH', 181, 'RO', 46.57485000, 22.44584000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1082089'),
(96094, 'Rimetea', 4724, 'AB', 181, 'RO', 46.45000000, 23.56667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q935515'),
(96095, 'Ripiceni', 4740, 'BT', 181, 'RO', 47.95000000, 27.13333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1065914'),
(96096, 'Roata de Jos', 4726, 'GR', 181, 'RO', 44.41083000, 25.54333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2717467'),
(96097, 'Robeasca', 4756, 'BZ', 181, 'RO', 45.16667000, 27.13333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q13542184'),
(96098, 'Rociu', 4722, 'AG', 181, 'RO', 44.66667000, 25.03333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2486677'),
(96099, 'Rodna', 4733, 'BN', 181, 'RO', 47.41667000, 24.81667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q1027868'),
(96100, 'Roeşti', 4757, 'VL', 181, 'RO', 44.91667000, 24.08333000, '2019-10-05 23:15:39', '2020-05-01 17:23:12', 1, 'Q2298496'),
(96101, 'Rogova', 4751, 'MH', 181, 'RO', 44.47167000, 22.80667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2465565'),
(96102, 'Rogoz', 4760, 'MM', 181, 'RO', 47.46749000, 23.93214000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q726363'),
(96103, 'Rojiște', 4742, 'DJ', 181, 'RO', 44.06683000, 23.93898000, '2019-10-05 23:15:39', '2020-05-01 17:23:09', 1, 'Q2539211'),
(96104, 'Roma', 4740, 'BT', 181, 'RO', 47.83333000, 26.60000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2534045'),
(96105, 'Roman', 4731, 'NT', 181, 'RO', 46.92119000, 26.92646000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2534045'),
(96106, 'Romanu', 4736, 'BR', 181, 'RO', 45.28333000, 27.73333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12148689'),
(96107, 'Romos', 4721, 'HD', 181, 'RO', 45.83333000, 23.28333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q5053778'),
(96108, 'Romuli', 4733, 'BN', 181, 'RO', 47.53333000, 24.43333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q221069'),
(96109, 'Românaşi', 4741, 'SJ', 181, 'RO', 47.10000000, 23.18333000, '2019-10-05 23:15:39', '2020-05-01 17:23:11', 1, 'Q1080481'),
(96110, 'Româneşti', 4735, 'IS', 181, 'RO', 47.28333000, 27.31667000, '2019-10-05 23:15:39', '2020-05-01 17:23:10', 1, 'Q2715847'),
(96111, 'Românești', 4745, 'DB', 181, 'RO', 44.56667000, 25.61667000, '2019-10-05 23:15:39', '2020-05-01 17:23:09', 1, 'Q12148770'),
(96112, 'Românești', 4729, 'PH', 181, 'RO', 44.86667000, 26.06667000, '2019-10-05 23:15:39', '2020-05-01 17:23:11', 1, 'Q12148773'),
(96113, 'Românești', 4740, 'BT', 181, 'RO', 47.73141000, 27.24027000, '2019-10-05 23:15:39', '2020-05-01 17:23:08', 1, 'Q2534357'),
(96114, 'Români', 4757, 'VL', 181, 'RO', 44.97310000, 24.20513000, '2019-10-05 23:15:39', '2020-05-01 17:23:12', 1, 'Q2534357'),
(96115, 'Români', 4731, 'NT', 181, 'RO', 46.78333000, 26.68333000, '2019-10-05 23:15:39', '2020-05-01 17:23:10', 1, 'Q2719203'),
(96116, 'Rona de Jos', 4760, 'MM', 181, 'RO', 47.91667000, 24.01667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q679178'),
(96117, 'Rona de Sus', 4760, 'MM', 181, 'RO', 47.90000000, 24.05000000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q999458'),
(96118, 'Roseţi', 4732, 'CL', 181, 'RO', 44.21482000, 27.44848000, '2019-10-05 23:15:39', '2020-05-01 17:23:09', 1, 'Q2717072'),
(96119, 'Rotunda', 4720, 'SV', 181, 'RO', 47.48745000, 26.52466000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12148948'),
(96120, 'Rotunda', 4731, 'NT', 181, 'RO', 47.02210000, 26.96266000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q12148946'),
(96121, 'Rotunda', 4738, 'OT', 181, 'RO', 43.98333000, 24.31667000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2549776'),
(96122, 'Rovinari', 4750, 'GJ', 181, 'RO', 44.91667000, 23.18333000, '2019-10-05 23:15:39', '2019-10-05 23:15:39', 1, 'Q2549776'),
(96123, 'Rovine', 4743, 'IL', 181, 'RO', 44.69540000, 27.09364000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q12148155'),
(96124, 'Rozavlea', 4760, 'MM', 181, 'RO', 47.73333000, 24.21667000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q1195063'),
(96125, 'Roznov', 4731, 'NT', 181, 'RO', 46.83333000, 26.51667000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q1195063'),
(96126, 'Roşia', 4755, 'SB', 181, 'RO', 45.81667000, 24.31667000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q1188233'),
(96127, 'Roşia', 4723, 'BH', 181, 'RO', 46.80000000, 22.40000000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q1088275'),
(96128, 'Roşia de Amaradia', 4750, 'GJ', 181, 'RO', 45.05000000, 23.75000000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q2718340'),
(96129, 'Roşia de Secaş', 4724, 'AB', 181, 'RO', 46.05000000, 23.88333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q936179'),
(96130, 'Roşieşti', 4752, 'VS', 181, 'RO', 46.43333000, 27.88333000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q12149002'),
(96131, 'Roşiile', 4757, 'VL', 181, 'RO', 44.86667000, 23.93333000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q2104955'),
(96132, 'Roşiori', 4736, 'BR', 181, 'RO', 44.80000000, 27.38333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q12148982'),
(96133, 'Roşiori', 4744, 'BC', 181, 'RO', 46.71667000, 27.08333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q12148982'),
(96134, 'Roșcani', 4735, 'IS', 181, 'RO', 47.45581000, 27.40021000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q2718469'),
(96135, 'Roșcani', 4720, 'SV', 181, 'RO', 47.53346000, 26.54909000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q12148963'),
(96136, 'Roșia Montană', 4724, 'AB', 181, 'RO', 46.30669000, 23.13373000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q857008'),
(96137, 'Roșiori', 4751, 'MH', 181, 'RO', 44.25633000, 22.92838000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q857008'),
(96138, 'Roșiori', 4723, 'BH', 181, 'RO', 47.25677000, 21.95209000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q1022809'),
(96139, 'Roșiori', 4740, 'BT', 181, 'RO', 47.76095000, 26.72167000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q12148981'),
(96140, 'Roșiori', 4743, 'IL', 181, 'RO', 44.61265000, 26.53496000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q3785361'),
(96141, 'Roșiorii de Vede', 4728, 'TR', 181, 'RO', 44.11667000, 24.98333000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q3785361'),
(96142, 'Roșu', 4725, 'IF', 181, 'RO', 44.45025000, 26.01122000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q12148972'),
(96143, 'Rubla', 4756, 'BZ', 181, 'RO', 45.35300000, 27.07525000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q12149061'),
(96144, 'Rucăr', 4722, 'AG', 181, 'RO', 45.40000000, 25.16667000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q2534789'),
(96145, 'Rucăreni', 4758, 'VN', 181, 'RO', 45.99438000, 26.65496000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q12149275'),
(96146, 'Rudeni', 4725, 'IF', 181, 'RO', 44.47242000, 25.97325000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q12149116'),
(96147, 'Rugineşti', 4758, 'VN', 181, 'RO', 46.06667000, 27.11667000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q14342804'),
(96148, 'Ruginoasa', 4735, 'IS', 181, 'RO', 47.25000000, 26.85000000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q913417'),
(96149, 'Ruginoasa', 4731, 'NT', 181, 'RO', 46.98622000, 26.70635000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q2542020'),
(96150, 'Rugășești', 4734, 'CJ', 181, 'RO', 47.23147000, 23.87326000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q383895'),
(96151, 'Ruja', 4755, 'SB', 181, 'RO', 46.01175000, 24.65048000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q498546'),
(96152, 'Runcu', 4757, 'VL', 181, 'RO', 45.16667000, 24.45000000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q2691175'),
(96153, 'Runcu', 4745, 'DB', 181, 'RO', 45.18333000, 25.38333000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q12149307'),
(96154, 'Runcu', 4750, 'GJ', 181, 'RO', 45.11667000, 23.13333000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q2717749'),
(96155, 'Runcu Salvei', 4733, 'BN', 181, 'RO', 47.34382000, 24.32529000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q647107'),
(96156, 'Rupea', 4759, 'BV', 181, 'RO', 46.03333000, 25.21667000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q647107'),
(96157, 'Rus', 4741, 'SJ', 181, 'RO', 47.28333000, 23.60000000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q1078879'),
(96158, 'Rusca', 4753, 'CS', 181, 'RO', 45.14517000, 22.33859000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q1392579'),
(96159, 'Rusca Montană', 4753, 'CS', 181, 'RO', 45.56741000, 22.45816000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q1001096'),
(96160, 'Ruscova', 4760, 'MM', 181, 'RO', 47.79289000, 24.28546000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q1089930'),
(96161, 'Ruseni', 4731, 'NT', 181, 'RO', 46.79841000, 26.53943000, '2019-10-05 23:15:40', '2019-10-05 23:15:40', 1, 'Q12149359'),
(96162, 'Rusăneşti', 4738, 'OT', 181, 'RO', 43.93333000, 24.60000000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q959667'),
(96163, 'Ruşeţu', 4756, 'BZ', 181, 'RO', 44.95000000, 27.21667000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q6314940'),
(96164, 'Râca', 4722, 'AG', 181, 'RO', 44.43426000, 25.04333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q2625987'),
(96165, 'Râfov', 4729, 'PH', 181, 'RO', 44.86667000, 26.13333000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q5071438'),
(96166, 'Râmești', 4757, 'VL', 181, 'RO', 44.56667000, 24.10000000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q12147969'),
(96167, 'Râmnicelu', 4756, 'BZ', 181, 'RO', 45.36381000, 27.11121000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q13572042'),
(96168, 'Râmnicelu', 4736, 'BR', 181, 'RO', 45.28333000, 27.53333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q12147973'),
(96169, 'Râmniceni', 4758, 'VN', 181, 'RO', 45.51322000, 27.44843000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q12147974'),
(96170, 'Râmnicu Sărat', 4756, 'BZ', 181, 'RO', 45.38333000, 27.05000000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q12147974'),
(96171, 'Râmnicu Vâlcea', 4757, 'VL', 181, 'RO', 45.10000000, 24.36667000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q12147974'),
(96172, 'Râncăciov', 4745, 'DB', 181, 'RO', 44.88872000, 25.35778000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q12147998'),
(96173, 'Rânzești', 4752, 'VS', 181, 'RO', 46.22101000, 28.09468000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q12147996'),
(96174, 'Râu Alb de Jos', 4745, 'DB', 181, 'RO', 45.13653000, 25.34745000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q12148041'),
(96175, 'Râu Sadului', 4755, 'SB', 181, 'RO', 45.61667000, 24.06667000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q1077587'),
(96176, 'Râu de Mori', 4721, 'HD', 181, 'RO', 45.48333000, 22.85000000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q5062707'),
(96177, 'Râşca', 4720, 'SV', 181, 'RO', 47.36667000, 26.23333000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q731675'),
(96178, 'Râşnov', 4759, 'BV', 181, 'RO', 45.58333000, 25.45000000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q731675'),
(96179, 'Râșca', 4734, 'CJ', 181, 'RO', 46.73967000, 23.10625000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q1077539'),
(96180, 'Râșești', 4752, 'VS', 181, 'RO', 46.77219000, 28.14273000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q12148061'),
(96181, 'Răbăgani', 4723, 'BH', 181, 'RO', 46.75000000, 22.23333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q1195205'),
(96182, 'Răcari', 4745, 'DB', 181, 'RO', 44.63333000, 25.73333000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q1195205'),
(96183, 'Răcarii de Sus', 4742, 'DJ', 181, 'RO', 44.53539000, 23.55746000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q12147457'),
(96184, 'Răchita', 4748, 'TM', 181, 'RO', 45.83827000, 22.10615000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q18890'),
(96185, 'Răchiteni', 4735, 'IS', 181, 'RO', 47.05347000, 26.91730000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q908024'),
(96186, 'Răchitoasa', 4744, 'BC', 181, 'RO', 46.43333000, 27.36667000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q15814151'),
(96187, 'Răchitova', 4721, 'HD', 181, 'RO', 45.60000000, 22.75000000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q1939006'),
(96188, 'Răchiţi', 4740, 'BT', 181, 'RO', 47.76667000, 26.68333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q2716395'),
(96189, 'Răcoasa', 4758, 'VN', 181, 'RO', 46.00000000, 26.88333000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q12725106'),
(96190, 'Răcăciuni', 4744, 'BC', 181, 'RO', 46.33333000, 26.98333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q15765331'),
(96191, 'Răcăuți', 4744, 'BC', 181, 'RO', 46.22476000, 26.78723000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q12147464'),
(96192, 'Răcăşdia', 4753, 'CS', 181, 'RO', 44.99306000, 21.61806000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q75656'),
(96193, 'Rădeni', 4731, 'NT', 181, 'RO', 47.14260000, 26.53703000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q12147293'),
(96194, 'Rădeni', 4740, 'BT', 181, 'RO', 47.51186000, 26.89249000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q12147291'),
(96195, 'Rădeşti', 4724, 'AB', 181, 'RO', 46.26667000, 23.71667000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q937558'),
(96196, 'Rădești', 4747, 'GL', 181, 'RO', 46.07263000, 27.78936000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q2289366'),
(96197, 'Rădești', 4722, 'AG', 181, 'RO', 45.07184000, 25.00207000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q12147301'),
(96198, 'Rădoiești-Deal', 4728, 'TR', 181, 'RO', 44.15000000, 25.13333000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q12147315'),
(96199, 'Răducăneni', 4735, 'IS', 181, 'RO', 46.95000000, 27.93333000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q1972778'),
(96200, 'Rădulești', 4743, 'IL', 181, 'RO', 44.76667000, 26.35000000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q12147323'),
(96201, 'Rădăuți', 4720, 'SV', 181, 'RO', 47.85000000, 25.91667000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q12147323'),
(96202, 'Rădăuți-Prut', 4740, 'BT', 181, 'RO', 48.23333000, 26.80000000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q2715850'),
(96203, 'Rădăşeni', 4720, 'SV', 181, 'RO', 47.46667000, 26.25000000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q2606089'),
(96204, 'Răpănaşu', 4733, 'BN', 181, 'RO', 47.38333000, 24.18333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q2606089'),
(96205, 'Răscruci', 4734, 'CJ', 181, 'RO', 46.90569000, 23.76860000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q714813'),
(96206, 'Răscăeți', 4745, 'DB', 181, 'RO', 44.59221000, 25.26990000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q12147646'),
(96207, 'Răsmireşti', 4728, 'TR', 181, 'RO', 43.98333000, 25.55000000, '2019-10-05 23:15:40', '2020-05-01 17:23:11', 1, 'Q2721395'),
(96208, 'Răstoaca', 4758, 'VN', 181, 'RO', 45.66083000, 27.28723000, '2019-10-05 23:15:40', '2020-05-01 17:23:12', 1, 'Q1405780'),
(96209, 'Răsuceni', 4726, 'GR', 181, 'RO', 44.09167000, 25.66389000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q2717778'),
(96210, 'Răteşti', 4722, 'AG', 181, 'RO', 44.73333000, 25.13333000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q2534722'),
(96211, 'Răuceşti', 4731, 'NT', 181, 'RO', 47.25000000, 26.41667000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q2605784'),
(96212, 'Răuseni', 4740, 'BT', 181, 'RO', 47.56667000, 27.20000000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q1065903'),
(96213, 'Războieni', 4735, 'IS', 181, 'RO', 47.21754000, 27.05039000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q12147390'),
(96214, 'Războieni-Cetate', 4724, 'AB', 181, 'RO', 46.41335000, 23.87158000, '2019-10-05 23:15:40', '2020-05-01 17:23:08', 1, 'Q831423'),
(96215, 'Războienii de Jos', 4731, 'NT', 181, 'RO', 47.06887000, 26.56374000, '2019-10-05 23:15:40', '2020-05-01 17:23:10', 1, 'Q12147392'),
(96216, 'Răzvad', 4745, 'DB', 181, 'RO', 44.93333000, 25.53333000, '2019-10-05 23:15:40', '2020-05-01 17:23:09', 1, 'Q12147393'),
(96217, 'Răzvani', 4732, 'CL', 181, 'RO', 44.42114000, 26.88092000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q12147394'),
(96218, 'Răşinari', 4755, 'SB', 181, 'RO', 45.70000000, 24.06667000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q938640'),
(96219, 'Sabasa', 4731, 'NT', 181, 'RO', 47.20174000, 25.81760000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12149928'),
(96220, 'Sacoşu Turcesc', 4748, 'TM', 181, 'RO', 45.65194000, 21.42889000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q1095881'),
(96221, 'Sacoșu Mare', 4748, 'TM', 181, 'RO', 45.58503000, 21.72958000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q754517'),
(96222, 'Sacu', 4753, 'CS', 181, 'RO', 45.56667000, 22.11667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q579349'),
(96223, 'Sadina', 4726, 'GR', 181, 'RO', 44.41143000, 25.52953000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150182'),
(96224, 'Sadova', 4720, 'SV', 181, 'RO', 47.53333000, 25.50000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2606123'),
(96225, 'Sadova', 4742, 'DJ', 181, 'RO', 43.90000000, 23.95000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q949614'),
(96226, 'Sadu', 4755, 'SB', 181, 'RO', 45.66667000, 24.18333000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1189089'),
(96227, 'Saelele', 4728, 'TR', 181, 'RO', 43.85577000, 24.72789000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2549932'),
(96228, 'Sagna', 4731, 'NT', 181, 'RO', 46.98333000, 27.01667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2542069'),
(96229, 'Salcea', 4720, 'SV', 181, 'RO', 47.65000000, 26.36667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2542069'),
(96230, 'Salcia', 4728, 'TR', 181, 'RO', 43.95000000, 24.91667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2721345'),
(96231, 'Salcia', 4729, 'PH', 181, 'RO', 45.18333000, 26.33333000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2214024'),
(96232, 'Salcia', 4751, 'MH', 181, 'RO', 44.14083000, 22.92778000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q5071275'),
(96233, 'Salcia', 4742, 'DJ', 181, 'RO', 44.48195000, 23.44645000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150292'),
(96234, 'Salcia Tudor', 4736, 'BR', 181, 'RO', 45.36667000, 27.51667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150289'),
(96235, 'Saligny', 4737, 'CT', 181, 'RO', 44.28111000, 28.09092000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1091949'),
(96236, 'Salonta', 4723, 'BH', 181, 'RO', 46.80000000, 21.65000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1091949'),
(96237, 'Salva', 4733, 'BN', 181, 'RO', 47.30000000, 24.35000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1062607'),
(96238, 'Samarineşti', 4750, 'GJ', 181, 'RO', 44.75000000, 23.05000000, '2019-10-05 23:15:41', '2020-05-01 17:23:10', 1, 'Q2717691'),
(96239, 'Sanislău', 4746, 'SM', 181, 'RO', 47.63333000, 22.33333000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q1188269'),
(96240, 'Santa Mare', 4740, 'BT', 181, 'RO', 47.61667000, 27.35000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2716368'),
(96241, 'Santău', 4746, 'SM', 181, 'RO', 47.51667000, 22.51667000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q1188277'),
(96242, 'Sarafinești', 4740, 'BT', 181, 'RO', 47.61708000, 26.60740000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q12150638'),
(96243, 'Saraiu', 4737, 'CT', 181, 'RO', 44.71667000, 28.15000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1188247'),
(96244, 'Sarasău', 4760, 'MM', 181, 'RO', 47.95510000, 23.82874000, '2019-10-05 23:15:41', '2020-05-01 17:23:10', 1, 'Q645730'),
(96245, 'Saravale', 4748, 'TM', 181, 'RO', 46.06929000, 20.74085000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q373372'),
(96246, 'Sarichioi', 4727, 'TL', 181, 'RO', 44.95000000, 28.85000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1065021'),
(96247, 'Sarmizegetusa', 4721, 'HD', 181, 'RO', 45.51667000, 22.78333000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12725121'),
(96248, 'Sasca Montană', 4753, 'CS', 181, 'RO', 44.88546000, 21.70915000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q1076076'),
(96249, 'Sascut', 4744, 'BC', 181, 'RO', 46.20000000, 27.10000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q15818319'),
(96250, 'Sascut-Sat', 4744, 'BC', 181, 'RO', 46.18727000, 27.06851000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150708'),
(96251, 'Sat-Șugatag', 4760, 'MM', 181, 'RO', 47.79796000, 23.90637000, '2019-10-05 23:15:41', '2020-05-01 17:23:10', 1, 'Q602578'),
(96252, 'Satchinez', 4748, 'TM', 181, 'RO', 45.93750000, 21.04056000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q368055'),
(96253, 'Satu Mare', 4720, 'SV', 181, 'RO', 47.83333000, 26.01667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150740'),
(96254, 'Satu Mare', 4739, 'AR', 181, 'RO', 46.06628000, 20.95826000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q221400'),
(96255, 'Satu Mare', 4749, 'HR', 181, 'RO', 46.34082000, 25.38378000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1044067'),
(96256, 'Satu Mare', 4746, 'SM', 181, 'RO', 47.79926000, 22.86255000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1044067'),
(96257, 'Satu Nou', 4744, 'BC', 181, 'RO', 46.24447000, 26.65343000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1416068'),
(96258, 'Satu Nou', 4752, 'VS', 181, 'RO', 46.69608000, 27.74876000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q10967518'),
(96259, 'Satu Nou', 4735, 'IS', 181, 'RO', 47.32147000, 27.09555000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150746'),
(96260, 'Satu Nou', 4737, 'CT', 181, 'RO', 44.26918000, 28.22579000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150761'),
(96261, 'Satu Nou Calopăr', 4742, 'DJ', 181, 'RO', 44.16667000, 23.76667000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q12150761'),
(96262, 'Satulung', 4760, 'MM', 181, 'RO', 47.56667000, 23.43333000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1126840'),
(96263, 'Sauca', 4752, 'VS', 181, 'RO', 46.65268000, 27.60523000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12150782'),
(96264, 'Scheia', 4720, 'SV', 181, 'RO', 47.65000000, 26.23333000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1084353'),
(96265, 'Scheia', 4735, 'IS', 181, 'RO', 47.11725000, 26.88153000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12171992'),
(96266, 'Scheiu de Jos', 4745, 'DB', 181, 'RO', 44.93460000, 25.19986000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12153212'),
(96267, 'Scheiu de Sus', 4745, 'DB', 181, 'RO', 44.95602000, 25.19317000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12153214'),
(96268, 'Schela', 4747, 'GL', 181, 'RO', 45.50000000, 27.81667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2289374'),
(96269, 'Schitu', 4738, 'OT', 181, 'RO', 44.35000000, 24.56667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1145254'),
(96270, 'Schitu', 4726, 'GR', 181, 'RO', 44.14083000, 25.83667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2717791'),
(96271, 'Schitu', 4737, 'CT', 181, 'RO', 43.94279000, 28.62829000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12153509'),
(96272, 'Schitu Frumoasa', 4744, 'BC', 181, 'RO', 46.63541000, 26.48761000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12153502'),
(96273, 'Schitu-Duca', 4735, 'IS', 181, 'RO', 47.03333000, 27.76667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2719113'),
(96274, 'Schitu-Goleşti', 4722, 'AG', 181, 'RO', 45.20000000, 25.00000000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q12153494'),
(96275, 'Schiulești', 4729, 'PH', 181, 'RO', 45.28471000, 25.95403000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q12153511'),
(96276, 'Scoarţa', 4750, 'GJ', 181, 'RO', 45.01667000, 23.45000000, '2019-10-05 23:15:41', '2020-05-01 17:23:10', 1, 'Q2718391'),
(96277, 'Scobinţi', 4735, 'IS', 181, 'RO', 47.38333000, 26.93333000, '2019-10-05 23:15:41', '2020-05-01 17:23:10', 1, 'Q2607459'),
(96278, 'Scorniceşti', 4738, 'OT', 181, 'RO', 44.56667000, 24.55000000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q16898466'),
(96279, 'Scorţaru Nou', 4736, 'BR', 181, 'RO', 45.31667000, 27.60000000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q2780048'),
(96280, 'Scorţeni', 4729, 'PH', 181, 'RO', 45.10000000, 25.85000000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q3183399'),
(96281, 'Scorţeni', 4744, 'BC', 181, 'RO', 46.55000000, 26.65000000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q15883966'),
(96282, 'Scorţoasa', 4756, 'BZ', 181, 'RO', 45.36403000, 26.66116000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q12153370'),
(96283, 'Scorțaru Vechi', 4736, 'BR', 181, 'RO', 45.21886000, 27.75489000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q12153368'),
(96284, 'Scrioaştea', 4728, 'TR', 181, 'RO', 44.15000000, 24.95000000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q2721359'),
(96285, 'Sculia', 4748, 'TM', 181, 'RO', 45.44429000, 21.43225000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q751848'),
(96286, 'Scundu', 4757, 'VL', 181, 'RO', 44.83333000, 24.20000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2602827'),
(96287, 'Scurta', 4744, 'BC', 181, 'RO', 46.26133000, 27.01548000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12153458'),
(96288, 'Scurtești', 4729, 'PH', 181, 'RO', 45.23759000, 25.90264000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q12153461'),
(96289, 'Scurtești', 4756, 'BZ', 181, 'RO', 45.13829000, 26.90397000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q12153460'),
(96290, 'Scurtu Mare', 4728, 'TR', 181, 'RO', 44.35000000, 25.26667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2721499'),
(96291, 'Scutelnici', 4756, 'BZ', 181, 'RO', 44.85000000, 26.91667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12153471'),
(96292, 'Scânteia', 4735, 'IS', 181, 'RO', 46.91667000, 27.56667000, '2019-10-05 23:15:41', '2020-05-01 17:23:10', 1, 'Q2605968'),
(96293, 'Scânteia', 4743, 'IL', 181, 'RO', 44.73404000, 27.46593000, '2019-10-05 23:15:41', '2020-05-01 17:23:10', 1, 'Q12153229'),
(96294, 'Scânteiești', 4747, 'GL', 181, 'RO', 45.68333000, 27.98333000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q3725375'),
(96295, 'Scăeşti', 4742, 'DJ', 181, 'RO', 44.46667000, 23.58333000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q2258614'),
(96296, 'Scărişoara', 4724, 'AB', 181, 'RO', 46.46667000, 22.85000000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q775822'),
(96297, 'Scărişoara', 4738, 'OT', 181, 'RO', 44.00000000, 24.56667000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q952201'),
(96298, 'Seaca', 4738, 'OT', 181, 'RO', 44.16667000, 24.75000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2719845'),
(96299, 'Seaca', 4728, 'TR', 181, 'RO', 43.75000000, 25.06667000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2721518'),
(96300, 'Seaca de Câmp', 4742, 'DJ', 181, 'RO', 43.93333000, 23.21667000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q2465714'),
(96301, 'Seaca de Pădure', 4742, 'DJ', 181, 'RO', 44.36611000, 23.30587000, '2019-10-05 23:15:41', '2020-05-01 17:23:09', 1, 'Q12325414'),
(96302, 'Sebeş', 4724, 'AB', 181, 'RO', 45.95646000, 23.57100000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q12325414'),
(96303, 'Sebeșel', 4724, 'AB', 181, 'RO', 45.88771000, 23.56606000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q757197'),
(96304, 'Sebiş', 4739, 'AR', 181, 'RO', 46.36667000, 22.11667000, '2019-10-05 23:15:41', '2020-05-01 17:23:08', 1, 'Q757197'),
(96305, 'Secaş', 4748, 'TM', 181, 'RO', 45.88500000, 21.81917000, '2019-10-05 23:15:41', '2020-05-01 17:23:11', 1, 'Q1195105'),
(96306, 'Seciu', 4729, 'PH', 181, 'RO', 45.03275000, 26.06474000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q1195105'),
(96307, 'Sector 1', 4730, 'B', 181, 'RO', 44.49239000, 26.04831000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2162427'),
(96308, 'Sector 2', 4730, 'B', 181, 'RO', 44.45280000, 26.13321000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2728790'),
(96309, 'Sector 3', 4730, 'B', 181, 'RO', 44.42340000, 26.16874000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2455368'),
(96310, 'Sector 4', 4730, 'B', 181, 'RO', 44.37571000, 26.12085000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q687235'),
(96311, 'Sector 5', 4730, 'B', 181, 'RO', 44.38808000, 26.07144000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q167630'),
(96312, 'Sector 6', 4730, 'B', 181, 'RO', 44.43579000, 26.01649000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q167280'),
(96313, 'Secu', 4742, 'DJ', 181, 'RO', 44.46667000, 23.30000000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2465726'),
(96314, 'Secui', 4742, 'DJ', 181, 'RO', 44.19186000, 23.86319000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q12151741'),
(96315, 'Secuieni', 4731, 'NT', 181, 'RO', 46.85000000, 26.83333000, '2019-10-05 23:15:41', '2019-10-05 23:15:41', 1, 'Q2605712'),
(96316, 'Secuieni', 4744, 'BC', 181, 'RO', 46.65000000, 27.10000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q15884467'),
(96317, 'Secusigiu', 4739, 'AR', 181, 'RO', 46.08333000, 20.98333000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q1066871'),
(96318, 'Secăria', 4729, 'PH', 181, 'RO', 45.28333000, 25.68333000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q14262133'),
(96319, 'Segarcea', 4742, 'DJ', 181, 'RO', 44.10000000, 23.75000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q14262133'),
(96320, 'Segarcea Vale', 4728, 'TR', 181, 'RO', 43.81667000, 24.80000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q2721510'),
(96321, 'Segarcea-Deal', 4728, 'TR', 181, 'RO', 43.82548000, 24.84020000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12151462'),
(96322, 'Seimeni', 4737, 'CT', 181, 'RO', 44.38333000, 28.06667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q1091890'),
(96324, 'Seleuş', 4739, 'AR', 181, 'RO', 46.38333000, 21.71667000, '2019-10-05 23:15:42', '2020-05-01 17:23:08', 1, 'Q872880'),
(96325, 'Semlac', 4739, 'AR', 181, 'RO', 46.11667000, 20.93333000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q257958'),
(96326, 'Serdanu', 4745, 'DB', 181, 'RO', 44.62732000, 25.63124000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12152274'),
(96327, 'Sfinţeşti', 4728, 'TR', 181, 'RO', 44.18333000, 25.10000000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q2721582'),
(96328, 'Sfântu Gheorghe', 4727, 'TL', 181, 'RO', 44.89600000, 29.59294000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q1187750'),
(96329, 'Sfântu Gheorghe', 4743, 'IL', 181, 'RO', 44.65000000, 26.83333000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q13360897'),
(96330, 'Sfântu Gheorghe', 4754, 'CV', 181, 'RO', 45.86667000, 25.78333000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q13360897'),
(96331, 'Sfârcea', 4742, 'DJ', 181, 'RO', 44.48613000, 23.54661000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q12159046'),
(96332, 'Sibiu', 4755, 'SB', 181, 'RO', 45.80000000, 24.15000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12159046'),
(96333, 'Sic', 4734, 'CJ', 181, 'RO', 46.93333000, 23.88333000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q1090075'),
(96334, 'Sicheviţa', 4753, 'CS', 181, 'RO', 44.73500000, 21.84861000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q1070200'),
(96335, 'Siculeni', 4749, 'HR', 181, 'RO', 46.41667000, 25.75000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q223664'),
(96337, 'Sihlea', 4758, 'VN', 181, 'RO', 45.50000000, 27.11667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12725134'),
(96338, 'Silivașu de Câmpie', 4733, 'BN', 181, 'RO', 46.78333000, 24.30000000, '2019-10-05 23:15:42', '2020-05-01 17:23:08', 1, 'Q1092666'),
(96339, 'Siliştea', 4728, 'TR', 181, 'RO', 44.38333000, 25.35000000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q2721311'),
(96340, 'Siliştea', 4736, 'BR', 181, 'RO', 45.30000000, 27.85000000, '2019-10-05 23:15:42', '2020-05-01 17:23:08', 1, 'Q2780040'),
(96341, 'Siliştea', 4737, 'CT', 181, 'RO', 44.40000000, 28.16667000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q1091917'),
(96342, 'Siliştea Crucii', 4742, 'DJ', 181, 'RO', 44.03333000, 23.48333000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q12725136'),
(96343, 'Siliștea', 4731, 'NT', 181, 'RO', 46.77781000, 26.69303000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q12159374'),
(96344, 'Siliștea Gumești', 4728, 'TR', 181, 'RO', 44.38333000, 25.00000000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q2721573'),
(96345, 'Siliștea Snagovului', 4725, 'IF', 181, 'RO', 44.73920000, 26.17942000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q12159369'),
(96346, 'Simeria', 4721, 'HD', 181, 'RO', 45.85000000, 23.01667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12159369'),
(96347, 'Siminicea', 4720, 'SV', 181, 'RO', 47.70000000, 26.40000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q2606157'),
(96348, 'Siminoc', 4737, 'CT', 181, 'RO', 44.16866000, 28.35277000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12159438'),
(96349, 'Simoneşti', 4749, 'HR', 181, 'RO', 46.33333000, 25.10000000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q371137'),
(96350, 'Sinaia', 4729, 'PH', 181, 'RO', 45.35000000, 25.55000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q16898486'),
(96351, 'Sineşti', 4757, 'VL', 181, 'RO', 44.95000000, 23.85000000, '2019-10-05 23:15:42', '2020-05-01 17:23:12', 1, 'Q2219463'),
(96352, 'Sineşti', 4735, 'IS', 181, 'RO', 47.11667000, 27.18333000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q2718519'),
(96353, 'Sineşti', 4743, 'IL', 181, 'RO', 44.56667000, 26.38333000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q13360903'),
(96354, 'Sinești', 4738, 'OT', 181, 'RO', 44.46777000, 24.66585000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q12159449'),
(96355, 'Singureni', 4726, 'GR', 181, 'RO', 44.23361000, 25.94333000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q2539190'),
(96356, 'Sintea Mare', 4739, 'AR', 181, 'RO', 46.51667000, 21.60000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q1087633'),
(96357, 'Sintești', 4725, 'IF', 181, 'RO', 44.30046000, 26.12126000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q12159463'),
(96358, 'Siret', 4720, 'SV', 181, 'RO', 47.95000000, 26.06667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12159463'),
(96359, 'Sireţel', 4735, 'IS', 181, 'RO', 47.40000000, 26.73333000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q2606020'),
(96360, 'Sita Buzăului', 4754, 'CV', 181, 'RO', 45.65000000, 26.06667000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q1092588'),
(96361, 'Slatina', 4738, 'OT', 181, 'RO', 44.43333000, 24.36667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q1092588'),
(96362, 'Slatina', 4720, 'SV', 181, 'RO', 47.45000000, 26.01667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q1195068'),
(96363, 'Slatina-Timiş', 4753, 'CS', 181, 'RO', 45.25000000, 22.28333000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q75680'),
(96364, 'Slava Cercheză', 4727, 'TL', 181, 'RO', 44.90128000, 28.54753000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q3916986'),
(96365, 'Slava Rusă', 4727, 'TL', 181, 'RO', 44.85029000, 28.60557000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q12153536'),
(96366, 'Slimnic', 4755, 'SB', 181, 'RO', 45.91667000, 24.16667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q1096004'),
(96367, 'Slivileşti', 4750, 'GJ', 181, 'RO', 44.78333000, 23.13333000, '2019-10-05 23:15:42', '2020-05-01 17:23:10', 1, 'Q2717872'),
(96368, 'Slobozia', 4731, 'NT', 181, 'RO', 46.84892000, 26.53344000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12153692'),
(96369, 'Slobozia', 4722, 'AG', 181, 'RO', 44.52527000, 25.24263000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q2534666'),
(96370, 'Slobozia', 4726, 'GR', 181, 'RO', 43.85000000, 25.90000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q2539922'),
(96371, 'Slobozia', 4735, 'IS', 181, 'RO', 47.05758000, 27.41446000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12153683'),
(96372, 'Slobozia', 4744, 'BC', 181, 'RO', 46.47495000, 27.31111000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12153696'),
(96373, 'Slobozia', 4743, 'IL', 181, 'RO', 44.56470000, 27.36330000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12153696'),
(96374, 'Slobozia Bradului', 4758, 'VN', 181, 'RO', 45.50000000, 27.05000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q6581992'),
(96375, 'Slobozia Conachi', 4747, 'GL', 181, 'RO', 45.58333000, 27.78333000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q2539252'),
(96376, 'Slobozia Moara', 4745, 'DB', 181, 'RO', 44.60000000, 25.71667000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q975184'),
(96377, 'Slobozia-Ciorăşti', 4758, 'VN', 181, 'RO', 45.61667000, 27.20000000, '2019-10-05 23:15:42', '2020-05-01 17:23:12', 1, 'Q2300133'),
(96378, 'Slobozia-Câmpineanca', 4758, 'VN', 181, 'RO', 45.70000000, 27.13333000, '2019-10-05 23:15:42', '2020-05-01 17:23:12', 1, 'Q2300133'),
(96379, 'Slobozia-Mândra', 4728, 'TR', 181, 'RO', 43.91667000, 24.70000000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q2606757'),
(96380, 'Slon', 4729, 'PH', 181, 'RO', 45.35521000, 26.04189000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12153795'),
(96381, 'Slănic', 4729, 'PH', 181, 'RO', 45.25000000, 25.93333000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q12153795'),
(96382, 'Slănic', 4722, 'AG', 181, 'RO', 45.23108000, 24.89411000, '2019-10-05 23:15:42', '2020-05-01 17:23:08', 1, 'Q12153607'),
(96383, 'Slănic-Moldova', 4744, 'BC', 181, 'RO', 46.22308000, 26.47413000, '2019-10-05 23:15:42', '2020-05-01 17:23:08', 1, 'Q12153607'),
(96384, 'Slătioara', 4738, 'OT', 181, 'RO', 44.40000000, 24.31667000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q2719518'),
(96385, 'Slăveni', 4738, 'OT', 181, 'RO', 44.08558000, 24.52843000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q12153602'),
(96386, 'Smeeni', 4756, 'BZ', 181, 'RO', 44.98333000, 26.85000000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q2539278'),
(96387, 'Smirna', 4743, 'IL', 181, 'RO', 44.72405000, 27.35478000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12154029'),
(96388, 'Smulţi', 4747, 'GL', 181, 'RO', 45.93333000, 27.75000000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q2539889'),
(96389, 'Smârdan', 4742, 'DJ', 181, 'RO', 43.93382000, 22.96592000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q12153925'),
(96390, 'Smârdan', 4747, 'GL', 181, 'RO', 45.48333000, 27.93333000, '2019-10-05 23:15:42', '2020-05-01 17:23:09', 1, 'Q2289334'),
(96391, 'Smârdan', 4727, 'TL', 181, 'RO', 45.28670000, 28.00368000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q250384'),
(96392, 'Smârdioasa', 4728, 'TR', 181, 'RO', 43.85000000, 25.43333000, '2019-10-05 23:15:42', '2020-05-01 17:23:11', 1, 'Q2723948'),
(96393, 'Snagov', 4725, 'IF', 181, 'RO', 44.70000000, 26.18333000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q914849'),
(96394, 'Socetu', 4728, 'TR', 181, 'RO', 44.19502000, 24.85421000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12154855'),
(96395, 'Soci', 4735, 'IS', 181, 'RO', 47.18573000, 26.64446000, '2019-10-05 23:15:42', '2019-10-05 23:15:42', 1, 'Q12154847'),
(96396, 'Socodor', 4739, 'AR', 181, 'RO', 46.51667000, 21.43333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q1096416'),
(96397, 'Socol', 4753, 'CS', 181, 'RO', 44.86083000, 21.37028000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q943553'),
(96398, 'Socond', 4746, 'SM', 181, 'RO', 47.56667000, 22.95000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q1188171'),
(96399, 'Sodomeni', 4735, 'IS', 181, 'RO', 47.22233000, 26.68805000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12154216'),
(96400, 'Sohatu', 4732, 'CL', 181, 'RO', 44.31667000, 26.50000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12154745'),
(96401, 'Sohodol', 4759, 'BV', 181, 'RO', 45.52829000, 25.40244000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12154745'),
(96402, 'Sohodol', 4750, 'GJ', 181, 'RO', 45.05531000, 22.88194000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12154745'),
(96403, 'Sohodol', 4724, 'AB', 181, 'RO', 46.35581000, 23.02923000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q764699'),
(96404, 'Solca', 4720, 'SV', 181, 'RO', 47.70000000, 25.85000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q764699'),
(96405, 'Soleşti', 4752, 'VS', 181, 'RO', 46.76667000, 27.78333000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q2546872'),
(96406, 'Soloneț', 4720, 'SV', 181, 'RO', 47.68682000, 26.03222000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q12154460'),
(96407, 'Solonţ', 4744, 'BC', 181, 'RO', 46.55000000, 26.51667000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q15884754'),
(96408, 'Someş-Odorhei', 4741, 'SJ', 181, 'RO', 47.31667000, 23.26667000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q1061637'),
(96409, 'Somova', 4727, 'TL', 181, 'RO', 45.18333000, 28.66667000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q1066655'),
(96410, 'Somușca', 4744, 'BC', 181, 'RO', 46.40147000, 26.91189000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q751841'),
(96411, 'Soporu de Câmpie', 4734, 'CJ', 181, 'RO', 46.69618000, 24.00658000, '2019-10-05 23:15:43', '2020-05-01 17:23:09', 1, 'Q753206'),
(96412, 'Sopot', 4742, 'DJ', 181, 'RO', 44.41667000, 23.50000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q1975058'),
(96413, 'Spanţov', 4732, 'CL', 181, 'RO', 44.11667000, 26.78333000, '2019-10-05 23:15:43', '2020-05-01 17:23:09', 1, 'Q12154949'),
(96414, 'Speriețeni', 4745, 'DB', 181, 'RO', 44.76611000, 25.45223000, '2019-10-05 23:15:43', '2020-05-01 17:23:09', 1, 'Q12155007'),
(96415, 'Spermezeu', 4733, 'BN', 181, 'RO', 47.30000000, 24.15000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q1078243'),
(96416, 'Spinuş', 4723, 'BH', 181, 'RO', 47.20000000, 22.20000000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q1195210'),
(96417, 'Spiru Haret', 4736, 'BR', 181, 'RO', 44.86715000, 27.74463000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12157058'),
(96418, 'Sprâncenata', 4738, 'OT', 181, 'RO', 44.08333000, 24.63333000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q2719279'),
(96419, 'Spulber', 4758, 'VN', 181, 'RO', 45.75186000, 26.76080000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q2300595'),
(96420, 'Spătărei', 4728, 'TR', 181, 'RO', 43.88848000, 25.13650000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q12155013'),
(96421, 'Stamate', 4720, 'SV', 181, 'RO', 47.60598000, 26.51698000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12157183'),
(96422, 'Stancea', 4732, 'CL', 181, 'RO', 44.13680000, 26.81226000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12157240'),
(96423, 'Starchiojd', 4729, 'PH', 181, 'RO', 45.31667000, 26.18333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q5071101'),
(96424, 'Steierdorf', 4753, 'CS', 181, 'RO', 45.06359000, 21.85216000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q5071101'),
(96425, 'Stejari', 4750, 'GJ', 181, 'RO', 44.76667000, 23.68333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q769844'),
(96426, 'Stejaru', 4727, 'TL', 181, 'RO', 44.76667000, 28.55000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q1093468'),
(96427, 'Stejaru', 4731, 'NT', 181, 'RO', 46.91888000, 26.18023000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12157639'),
(96428, 'Stejaru', 4728, 'TR', 181, 'RO', 44.18333000, 24.88333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q967348'),
(96429, 'Stelnica', 4743, 'IL', 181, 'RO', 44.41667000, 27.88333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q13366865'),
(96430, 'Sterpoaia', 4750, 'GJ', 181, 'RO', 44.79058000, 23.43601000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12157810'),
(96431, 'Sticlăria', 4735, 'IS', 181, 'RO', 47.40115000, 26.83797000, '2019-10-05 23:15:43', '2020-05-01 17:23:10', 1, 'Q12158470'),
(96432, 'Stoeneşti', 4738, 'OT', 181, 'RO', 44.11667000, 24.50000000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q597056'),
(96433, 'Stoeneşti', 4722, 'AG', 181, 'RO', 45.25000000, 25.16667000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q2486587'),
(96434, 'Stoeneşti', 4757, 'VL', 181, 'RO', 45.11667000, 24.16667000, '2019-10-05 23:15:43', '2020-05-01 17:23:12', 1, 'Q2298902'),
(96435, 'Stoeneşti', 4726, 'GR', 181, 'RO', 44.14444000, 25.89500000, '2019-10-05 23:15:43', '2020-05-01 17:23:10', 1, 'Q2291872'),
(96436, 'Stoenești', 4729, 'PH', 181, 'RO', 44.92422000, 25.85881000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q12158082'),
(96438, 'Stoicăneşti', 4738, 'OT', 181, 'RO', 44.18333000, 24.63333000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q3878950'),
(96439, 'Stoileşti', 4757, 'VL', 181, 'RO', 44.90000000, 24.38333000, '2019-10-05 23:15:43', '2020-05-01 17:23:12', 1, 'Q2280881'),
(96440, 'Stoina', 4750, 'GJ', 181, 'RO', 44.68333000, 23.63333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q2717755'),
(96441, 'Stolniceni', 4757, 'VL', 181, 'RO', 45.03948000, 24.30640000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q2717755'),
(96442, 'Stolniceni-Prăjescu', 4735, 'IS', 181, 'RO', 47.19858000, 26.74810000, '2019-10-05 23:15:43', '2020-05-01 17:23:10', 1, 'Q2607525'),
(96443, 'Stolnici', 4722, 'AG', 181, 'RO', 44.56667000, 24.78333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q2470411'),
(96444, 'Storeşti', 4740, 'BT', 181, 'RO', 47.55000000, 26.86667000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q12158029'),
(96445, 'Stornești', 4735, 'IS', 181, 'RO', 47.11667000, 27.18333000, '2019-10-05 23:15:43', '2020-05-01 17:23:10', 1, 'Q12158031'),
(96446, 'Storobăneasa', 4728, 'TR', 181, 'RO', 43.88333000, 25.45000000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q2721565'),
(96447, 'Straja', 4720, 'SV', 181, 'RO', 47.91667000, 25.55000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q2719738'),
(96448, 'Straja', 4744, 'BC', 181, 'RO', 46.43989000, 26.36258000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12158091'),
(96449, 'Strehaia', 4751, 'MH', 181, 'RO', 44.61667000, 23.20000000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q12158091'),
(96450, 'Strejeşti', 4738, 'OT', 181, 'RO', 44.53333000, 24.26667000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q3879400'),
(96451, 'Strejeștii de Sus', 4738, 'OT', 181, 'RO', 44.54389000, 24.22772000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q12158155'),
(96452, 'Strejnicu', 4729, 'PH', 181, 'RO', 44.91676000, 25.95109000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q3500298'),
(96453, 'Stremţ', 4724, 'AB', 181, 'RO', 46.21667000, 23.63333000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q906696'),
(96454, 'Stroeşti', 4757, 'VL', 181, 'RO', 45.08333000, 23.90000000, '2019-10-05 23:15:43', '2020-05-01 17:23:12', 1, 'Q3915150'),
(96455, 'Stroești', 4722, 'AG', 181, 'RO', 45.12953000, 24.79418000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q3915150'),
(96456, 'Stroiești', 4740, 'BT', 181, 'RO', 47.61042000, 26.96562000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q12158267'),
(96457, 'Stroiești', 4720, 'SV', 181, 'RO', 47.61667000, 26.13333000, '2019-10-05 23:15:43', '2020-05-01 17:23:11', 1, 'Q2604503'),
(96458, 'Strugari', 4744, 'BC', 181, 'RO', 46.53333000, 26.71667000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q15885621'),
(96459, 'Strunga', 4735, 'IS', 181, 'RO', 47.16667000, 26.98333000, '2019-10-05 23:15:43', '2019-10-05 23:15:43', 1, 'Q1084328'),
(96460, 'Strâmbeni', 4722, 'AG', 181, 'RO', 44.46897000, 24.97315000, '2019-10-05 23:15:43', '2020-05-01 17:23:08', 1, 'Q12158230'),
(96461, 'Strâmtura', 4760, 'MM', 181, 'RO', 47.78333000, 24.13333000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q1084708'),
(96462, 'Strâmtura', 4720, 'SV', 181, 'RO', 47.58082000, 25.68144000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q12158235'),
(96463, 'Străoane', 4758, 'VN', 181, 'RO', 45.93333000, 27.05000000, '2019-10-05 23:15:44', '2020-05-01 17:23:12', 1, 'Q13572505'),
(96464, 'Străteni', 4740, 'BT', 181, 'RO', 47.93677000, 26.29664000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q12158183'),
(96465, 'Studina', 4738, 'OT', 181, 'RO', 43.96667000, 24.41667000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q2719870'),
(96466, 'Studinița', 4738, 'OT', 181, 'RO', 43.97691000, 24.42668000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q12158408'),
(96467, 'Stulpicani', 4720, 'SV', 181, 'RO', 47.46667000, 25.76667000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q2604484'),
(96468, 'Stâlpeni', 4722, 'AG', 181, 'RO', 45.05000000, 24.96667000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q12157920'),
(96469, 'Stâlpu', 4756, 'BZ', 181, 'RO', 45.08333000, 26.71667000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q2716796'),
(96470, 'Stâna', 4746, 'SM', 181, 'RO', 47.50757000, 22.96423000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q581688'),
(96471, 'Stânca', 4731, 'NT', 181, 'RO', 47.23069000, 26.13139000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q12157939'),
(96472, 'Stâncești', 4740, 'BT', 181, 'RO', 47.75000000, 26.60000000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q12157946'),
(96473, 'Stângăceaua', 4751, 'MH', 181, 'RO', 44.60896000, 23.31273000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q12157935'),
(96474, 'Stârciu', 4741, 'SJ', 181, 'RO', 47.08747000, 22.91998000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q833336'),
(96475, 'Stăncești', 4756, 'BZ', 181, 'RO', 45.12665000, 26.92319000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q12157698'),
(96476, 'Stăncuţa', 4736, 'BR', 181, 'RO', 44.90000000, 27.83333000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q2780047'),
(96477, 'Stăneşti', 4726, 'GR', 181, 'RO', 43.91667000, 25.83333000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q2606419'),
(96478, 'Stăneşti', 4750, 'GJ', 181, 'RO', 45.11667000, 23.25000000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q2718346'),
(96479, 'Stănești', 4757, 'VL', 181, 'RO', 44.81667000, 24.05000000, '2019-10-05 23:15:44', '2020-05-01 17:23:12', 1, 'Q2298467'),
(96480, 'Stănileşti', 4752, 'VS', 181, 'RO', 46.61833000, 28.17139000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q2722379'),
(96481, 'Stănişeşti', 4744, 'BC', 181, 'RO', 46.43333000, 27.30000000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q15885309'),
(96482, 'Stăniţa', 4731, 'NT', 181, 'RO', 47.01667000, 27.10000000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q2605677'),
(96483, 'Stăuceni', 4740, 'BT', 181, 'RO', 47.71667000, 26.75000000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q2067506'),
(96484, 'Stăvaru', 4738, 'OT', 181, 'RO', 43.86445000, 24.25902000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q12157623'),
(96485, 'Suatu', 4734, 'CJ', 181, 'RO', 46.76667000, 23.96667000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q525843'),
(96486, 'Subcetate', 4749, 'HR', 181, 'RO', 46.85000000, 25.45000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q289568'),
(96487, 'Suceagu', 4734, 'CJ', 181, 'RO', 46.78432000, 23.46511000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q723087'),
(96488, 'Suceava', 4720, 'SV', 181, 'RO', 47.63333000, 26.25000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q723087'),
(96489, 'Suceveni', 4747, 'GL', 181, 'RO', 46.01222000, 28.01806000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q2717128'),
(96490, 'Suceviţa', 4720, 'SV', 181, 'RO', 47.78333000, 25.71667000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q1188191'),
(96491, 'Suciu de Jos', 4760, 'MM', 181, 'RO', 47.44584000, 23.98250000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q281261'),
(96492, 'Suciu de Sus', 4760, 'MM', 181, 'RO', 47.43333000, 24.03333000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q999568'),
(96493, 'Sudiţi', 4743, 'IL', 181, 'RO', 44.58333000, 27.60000000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q2291381'),
(96494, 'Sudiți', 4756, 'BZ', 181, 'RO', 45.26890000, 26.87183000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q12158583'),
(96495, 'Suhaia', 4728, 'TR', 181, 'RO', 43.73333000, 25.25000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q2721048'),
(96496, 'Suharău', 4740, 'BT', 181, 'RO', 48.13333000, 26.41667000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q2715886'),
(96497, 'Suhuleț', 4735, 'IS', 181, 'RO', 46.90368000, 27.25849000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q12158975'),
(96498, 'Suhurlui', 4747, 'GL', 181, 'RO', 45.72873000, 27.82678000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q3725380'),
(96499, 'Sulina', 4727, 'TL', 181, 'RO', 45.15564000, 29.65403000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q3725380'),
(96500, 'Sulița', 4740, 'BT', 181, 'RO', 47.65000000, 26.91667000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q2716375'),
(96501, 'Sultana', 4732, 'CL', 181, 'RO', 44.26062000, 26.85424000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q12158628'),
(96502, 'Suplacu de Barcău', 4723, 'BH', 181, 'RO', 47.25729000, 22.53195000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q1088279'),
(96503, 'Supuru de Jos', 4746, 'SM', 181, 'RO', 47.46667000, 22.80000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q1188626'),
(96504, 'Suraia', 4758, 'VN', 181, 'RO', 45.68333000, 27.40000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q2281894'),
(96505, 'Surani', 4729, 'PH', 181, 'RO', 45.20000000, 26.16667000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q13423454'),
(96506, 'Surdila-Greci', 4736, 'BR', 181, 'RO', 45.06667000, 27.26667000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q12158799'),
(96507, 'Surdila-Găiseanca', 4736, 'BR', 181, 'RO', 45.06667000, 27.33333000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q12158796'),
(96508, 'Surduc', 4741, 'SJ', 181, 'RO', 47.25000000, 23.35000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q1191328'),
(96509, 'Surdulești', 4722, 'AG', 181, 'RO', 44.39217000, 24.95798000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q12158795'),
(96510, 'Suseni', 4749, 'HR', 181, 'RO', 46.66667000, 25.55000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q1029408'),
(96511, 'Suseni', 4722, 'AG', 181, 'RO', 44.71667000, 24.95000000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q2534865'),
(96512, 'Suseni Socetu', 4745, 'DB', 181, 'RO', 44.75618000, 25.79829000, '2019-10-05 23:15:44', '2019-10-05 23:15:44', 1, 'Q12158830'),
(96513, 'Susenii Bârgăului', 4733, 'BN', 181, 'RO', 47.22311000, 24.70730000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q824942'),
(96514, 'Sutești', 4757, 'VL', 181, 'RO', 44.66851000, 24.21476000, '2019-10-05 23:15:44', '2020-05-01 17:23:12', 1, 'Q2298126'),
(96515, 'Sviniţa', 4751, 'MH', 181, 'RO', 44.49972000, 22.10611000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q278492'),
(96516, 'Sâg', 4741, 'SJ', 181, 'RO', 47.07545000, 22.78084000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q1191333'),
(96517, 'Sâmbotin', 4750, 'GJ', 181, 'RO', 45.13333000, 23.33333000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q12152686'),
(96518, 'Sâmbureşti', 4738, 'OT', 181, 'RO', 44.80000000, 24.41667000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q12152685'),
(96519, 'Sâmbăta', 4723, 'BH', 181, 'RO', 46.80173000, 22.20357000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q1189411'),
(96520, 'Sâmbăta de Sus', 4759, 'BV', 181, 'RO', 45.76178000, 24.82053000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q15279376'),
(96521, 'Sâmbăteni', 4739, 'AR', 181, 'RO', 46.14110000, 21.52689000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q714251'),
(96522, 'Sânandrei', 4748, 'TM', 181, 'RO', 45.85306000, 21.16806000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q1022828'),
(96523, 'Sâncel', 4724, 'AB', 181, 'RO', 46.20000000, 23.95000000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q634408'),
(96524, 'Sâncrai', 4749, 'HR', 181, 'RO', 46.36667000, 25.31667000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q714311'),
(96525, 'Sâncrai', 4724, 'AB', 181, 'RO', 46.29394000, 23.75209000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q844633'),
(96526, 'Sâncraiu', 4734, 'CJ', 181, 'RO', 46.83333000, 22.98333000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q249942'),
(96527, 'Sâncrăieni', 4749, 'HR', 181, 'RO', 46.31439000, 25.83941000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q876046'),
(96528, 'Sândominic', 4749, 'HR', 181, 'RO', 46.58400000, 25.78028000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q509836'),
(96529, 'Sângeorz-Băi', 4733, 'BN', 181, 'RO', 47.36850000, 24.67212000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q509836'),
(96530, 'Sângeru', 4729, 'PH', 181, 'RO', 45.13333000, 26.35000000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q6455716'),
(96531, 'Sâniob', 4723, 'BH', 181, 'RO', 47.26461000, 22.12778000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q714390'),
(96532, 'Sânmartin', 4739, 'AR', 181, 'RO', 46.41653000, 21.34821000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q831760'),
(96533, 'Sânmartin', 4749, 'HR', 181, 'RO', 46.26667000, 25.93333000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q876091'),
(96534, 'Sânmihaiu Almaşului', 4741, 'SJ', 181, 'RO', 47.01667000, 23.30000000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q1081428'),
(96535, 'Sânmihaiu Român', 4748, 'TM', 181, 'RO', 45.70444000, 21.08889000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q853960'),
(96536, 'Sânmihaiu de Câmpie', 4733, 'BN', 181, 'RO', 46.89257000, 24.33589000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q1176572'),
(96537, 'Sânmărghita', 4734, 'CJ', 181, 'RO', 47.15649000, 23.99461000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q1297099'),
(96538, 'Sânmărtin', 4734, 'CJ', 181, 'RO', 46.90000000, 23.56667000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q727952'),
(96539, 'Sânnicoară', 4734, 'CJ', 181, 'RO', 46.78990000, 23.72570000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q1294548'),
(96540, 'Sânnicolau Mare', 4748, 'TM', 181, 'RO', 46.08333000, 20.63333000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q837161');

