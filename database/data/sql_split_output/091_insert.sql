INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(45803, 'Saint-Barnabé', 4812, 'PAC', 75, 'FR', 43.30000000, 5.41667000, '2019-10-05 22:48:46', '2020-05-01 17:22:47', 1, '********'),
(45804, 'Saint-Barthélemy-de-Vals', 4798, 'ARA', 75, 'FR', 45.16938000, 4.87160000, '2019-10-05 22:48:46', '2020-05-01 17:22:43', 1, '********'),
(45805, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 4812, 'PAC', 75, 'FR', 43.33105000, 5.40241000, '2019-10-05 22:48:46', '2020-05-01 17:22:47', 1, '********'),
(45806, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 4802, 'PDL', 75, 'FR', 48.28081000, -0.63728000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45807, '<PERSON>-<PERSON><PERSON>ille-de-<PERSON><PERSON>', 4799, '<PERSON><PERSON>', 75, 'FR', 43.89454000, 3.73580000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q144884'),
(45808, '<PERSON>-<PERSON><PERSON>re', 4798, 'ARA', 75, 'FR', 45.84964000, 3.17947000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q144884'),
(45809, 'Saint-Benin-d’Azy', 4825, 'BFC', 75, 'FR', 46.99914000, 3.39267000, '2019-10-05 22:48:46', '2020-05-01 17:22:44', 1, 'Q144884'),
(45810, 'Saint-Benoît', 4795, 'NAQ', 75, 'FR', 46.55315000, 0.34190000, '2019-10-05 22:48:46', '2020-05-01 17:22:46', 1, 'Q144884'),
(45811, 'Saint-Benoît-de-Carmaux', 4799, 'OCC', 75, 'FR', 44.05037000, 2.12911000, '2019-10-05 22:48:46', '2020-05-01 17:22:46', 1, 'Q144884'),
(45812, 'Saint-Benoît-sur-Loire', 4818, 'CVL', 75, 'FR', 47.80561000, 2.31274000, '2019-10-05 22:48:46', '2020-05-01 17:22:44', 1, 'Q144884'),
(45813, 'Saint-Berain-sous-Sanvignes', 4825, 'BFC', 75, 'FR', 46.70678000, 4.29405000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q144884'),
(45814, 'Saint-Berthevin', 4802, 'PDL', 75, 'FR', 48.06667000, -0.83333000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q144884'),
(45815, 'Saint-Bon-Tarentaise', 4798, 'ARA', 75, 'FR', 45.43373000, 6.63686000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q144884'),
(45816, 'Saint-Bonnet-de-Mure', 4798, 'ARA', 75, 'FR', 45.69042000, 5.02912000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q144884'),
(45817, 'Saint-Bonnet-en-Champsaur', 4812, 'PAC', 75, 'FR', 44.68333000, 6.08333000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45818, 'Saint-Bonnet-le-Château', 4798, 'ARA', 75, 'FR', 45.42402000, 4.06706000, '2019-10-05 22:48:46', '2020-05-01 17:22:43', 1, '********'),
(45819, 'Saint-Bonnet-les-Oules', 4798, 'ARA', 75, 'FR', 45.54946000, 4.32522000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45820, 'Saint-Bonnet-près-Riom', 4798, 'ARA', 75, 'FR', 45.92972000, 3.11310000, '2019-10-05 22:48:46', '2020-05-01 17:22:43', 1, '********'),
(45821, 'Saint-Branchs', 4818, 'CVL', 75, 'FR', 47.22655000, 0.77306000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45822, 'Saint-Brandan', 4807, 'BRE', 75, 'FR', 48.39008000, -2.86875000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45823, 'Saint-Brevin-les-Pins', 4802, 'PDL', 75, 'FR', 47.25000000, -2.16667000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q752437'),
(45824, 'Saint-Briac-sur-Mer', 4807, 'BRE', 75, 'FR', 48.62189000, -2.13435000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q193704'),
(45825, 'Saint-Brice', 4795, 'NAQ', 75, 'FR', 45.68333000, -0.28333000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q193704'),
(45826, 'Saint-Brice-Courcelles', 4820, 'GES', 75, 'FR', 49.26667000, 3.98333000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q193704'),
(45827, 'Saint-Brice-en-Coglès', 4807, 'BRE', 75, 'FR', 48.41157000, -1.37019000, '2019-10-05 22:48:46', '2020-05-01 17:22:44', 1, 'Q193704'),
(45828, 'Saint-Brice-sous-Forêt', 4796, 'IDF', 75, 'FR', 49.00132000, 2.35361000, '2019-10-05 22:48:46', '2020-05-01 17:22:43', 1, 'Q193704'),
(45829, 'Saint-Brice-sur-Vienne', 4795, 'NAQ', 75, 'FR', 45.87852000, 0.95594000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45830, 'Saint-Brieuc', 4807, 'BRE', 75, 'FR', 48.51513000, -2.76838000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q29234'),
(45831, 'Saint-Bris-le-Vineux', 4825, 'BFC', 75, 'FR', 47.74396000, 3.64922000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q947049'),
(45832, 'Saint-Brisson-sur-Loire', 4818, 'CVL', 75, 'FR', 47.64686000, 2.68229000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45833, 'Saint-Broladre', 4807, 'BRE', 75, 'FR', 48.58677000, -1.65769000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, '********'),
(45834, 'Saint-Brès', 4799, 'OCC', 75, 'FR', 43.66742000, 4.03105000, '2019-10-05 22:48:46', '2020-05-01 17:22:46', 1, '********'),
(45835, 'Saint-Béron', 4798, 'ARA', 75, 'FR', 45.50308000, 5.72790000, '2019-10-05 22:48:46', '2020-05-01 17:22:43', 1, '********'),
(45836, 'Saint-Calais', 4802, 'PDL', 75, 'FR', 47.92211000, 0.74587000, '2019-10-05 22:48:46', '2019-10-05 22:48:46', 1, 'Q923203'),
(45837, 'Saint-Cannat', 4812, 'PAC', 75, 'FR', 43.62132000, 5.29810000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q640946'),
(45838, 'Saint-Caprais-de-Bordeaux', 4795, 'NAQ', 75, 'FR', 44.74786000, -0.43192000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q640946'),
(45839, 'Saint-Caradec', 4807, 'BRE', 75, 'FR', 48.19429000, -2.84386000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q640946'),
(45840, 'Saint-Carreuc', 4807, 'BRE', 75, 'FR', 48.39889000, -2.73056000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q640946'),
(45841, 'Saint-Cassien', 4798, 'ARA', 75, 'FR', 45.35710000, 5.54793000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45842, 'Saint-Cast-le-Guildo', 4807, 'BRE', 75, 'FR', 48.63028000, -2.25777000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q453076'),
(45843, 'Saint-Cergues', 4798, 'ARA', 75, 'FR', 46.23005000, 6.31924000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q453076'),
(45844, 'Saint-Cernin', 4798, 'ARA', 75, 'FR', 45.05850000, 2.42081000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q453076'),
(45845, 'Saint-Chaffrey', 4812, 'PAC', 75, 'FR', 44.92555000, 6.60768000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q453076'),
(45846, 'Saint-Chamas', 4812, 'PAC', 75, 'FR', 43.55048000, 5.03501000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q453076'),
(45847, 'Saint-Chamond', 4798, 'ARA', 75, 'FR', 45.47590000, 4.51294000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q453076'),
(45848, 'Saint-Chaptes', 4799, 'OCC', 75, 'FR', 43.97172000, 4.27812000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q239795'),
(45849, 'Saint-Charles', 4812, 'PAC', 75, 'FR', 43.30199000, 5.38405000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q239795'),
(45850, 'Saint-Chef', 4798, 'ARA', 75, 'FR', 45.63585000, 5.36465000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q960827'),
(45851, 'Saint-Chinian', 4799, 'OCC', 75, 'FR', 43.42238000, 2.94643000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q476063'),
(45852, 'Saint-Christo-en-Jarez', 4798, 'ARA', 75, 'FR', 45.54508000, 4.48826000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q476063'),
(45853, 'Saint-Christol', 4799, 'OCC', 75, 'FR', 43.72805000, 4.07991000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45854, 'Saint-Christol-lès-Alès', 4799, 'OCC', 75, 'FR', 44.08256000, 4.07506000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, '********'),
(45855, 'Saint-Christoly-de-Blaye', 4795, 'NAQ', 75, 'FR', 45.13179000, -0.50760000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45856, 'Saint-Christophe-Vallon', 4799, 'OCC', 75, 'FR', 44.47072000, 2.41184000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45857, 'Saint-Christophe-du-Bois', 4802, 'PDL', 75, 'FR', 47.03012000, -0.94441000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q541620'),
(45858, 'Saint-Christophe-du-Ligneron', 4802, 'PDL', 75, 'FR', 46.82478000, -1.76586000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q818200'),
(45859, 'Saint-Christophe-sur-le-Nais', 4818, 'CVL', 75, 'FR', 47.61720000, 0.47801000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45860, 'Saint-Chéron', 4796, 'IDF', 75, 'FR', 48.55433000, 2.12403000, '2019-10-05 22:48:47', '2020-05-01 17:22:43', 1, '********'),
(45861, 'Saint-Ciers-d’Abzac', 4795, 'NAQ', 75, 'FR', 45.03333000, -0.26667000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, '********'),
(45862, 'Saint-Ciers-sur-Gironde', 4795, 'NAQ', 75, 'FR', 45.28855000, -0.60794000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q194528'),
(45863, 'Saint-Clair-de-la-Tour', 4798, 'ARA', 75, 'FR', 45.57345000, 5.47741000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q194528'),
(45864, 'Saint-Clair-du-Rhône', 4798, 'ARA', 75, 'FR', 45.44186000, 4.77123000, '2019-10-05 22:48:47', '2020-05-01 17:22:43', 1, 'Q194528'),
(45865, 'Saint-Claud', 4795, 'NAQ', 75, 'FR', 45.89526000, 0.46454000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q753082'),
(45866, 'Saint-Claude', 4825, 'BFC', 75, 'FR', 46.38679000, 5.86473000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q212444'),
(45867, 'Saint-Claude-de-Diray', 4818, 'CVL', 75, 'FR', 47.61651000, 1.41798000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q540209'),
(45868, 'Saint-Cloud', 4796, 'IDF', 75, 'FR', 48.84598000, 2.20289000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q189153'),
(45869, 'Saint-Clément', 4795, 'NAQ', 75, 'FR', 45.34150000, 1.68507000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, 'Q189153'),
(45870, 'Saint-Clément', 4825, 'BFC', 75, 'FR', 48.21724000, 3.29609000, '2019-10-05 22:48:47', '2020-05-01 17:22:44', 1, 'Q189153'),
(45871, 'Saint-Clément-de-Rivière', 4799, 'OCC', 75, 'FR', 43.68333000, 3.83333000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, 'Q189153'),
(45872, 'Saint-Clément-de-la-Place', 4802, 'PDL', 75, 'FR', 47.52744000, -0.74545000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, 'Q189153'),
(45873, 'Saint-Clément-des-Levées', 4802, 'PDL', 75, 'FR', 47.33333000, -0.18222000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, 'Q189153'),
(45874, 'Saint-Contest', 4804, 'NOR', 75, 'FR', 49.21401000, -0.40221000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q647561'),
(45875, 'Saint-Cosme-en-Vairais', 4802, 'PDL', 75, 'FR', 48.26667000, 0.46667000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q647561'),
(45876, 'Saint-Coulomb', 4807, 'BRE', 75, 'FR', 48.67465000, -1.91092000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q216646'),
(45877, 'Saint-Crespin-sur-Moine', 4802, 'PDL', 75, 'FR', 47.09968000, -1.18651000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q216646'),
(45878, 'Saint-Crépin-Ibouvillers', 4828, 'HDF', 75, 'FR', 49.26283000, 2.07793000, '2019-10-05 22:48:47', '2020-05-01 17:22:45', 1, 'Q216646'),
(45879, 'Saint-Cyprien', 4798, 'ARA', 75, 'FR', 45.53450000, 4.23428000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q216646'),
(45880, 'Saint-Cyprien', 4795, 'NAQ', 75, 'FR', 44.86924000, 1.04156000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q216646'),
(45881, 'Saint-Cyprien-Plage', 4799, 'OCC', 75, 'FR', 42.63229000, 3.03333000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q216646'),
(45882, 'Saint-Cyr', 4798, 'ARA', 75, 'FR', 45.25183000, 4.73045000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q866735'),
(45883, 'Saint-Cyr-en-Bourg', 4802, 'PDL', 75, 'FR', 47.19520000, -0.06085000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q866735'),
(45884, 'Saint-Cyr-en-Val', 4818, 'CVL', 75, 'FR', 47.83182000, 1.96672000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45885, 'Saint-Cyr-l’École', 4796, 'IDF', 75, 'FR', 48.79865000, 2.06814000, '2019-10-05 22:48:47', '2020-05-01 17:22:43', 1, '********'),
(45886, 'Saint-Cyr-sous-Dourdan', 4796, 'IDF', 75, 'FR', 48.56667000, 2.03333000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45887, 'Saint-Cyr-sur-Loire', 4818, 'CVL', 75, 'FR', 47.40000000, 0.66667000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, '********'),
(45888, 'Saint-Cyr-sur-Menthon', 4798, 'ARA', 75, 'FR', 46.27502000, 4.97246000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q843587'),
(45889, 'Saint-Cyr-sur-Mer', 4812, 'PAC', 75, 'FR', 43.18048000, 5.70120000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q659335'),
(45890, 'Saint-Cyr-sur-Morin', 4796, 'IDF', 75, 'FR', 48.90664000, 3.18016000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q659335'),
(45891, 'Saint-Cyr-sur-le-Rhône', 4798, 'ARA', 75, 'FR', 45.51585000, 4.85250000, '2019-10-05 22:48:47', '2020-05-01 17:22:43', 1, 'Q659335'),
(45892, 'Saint-Céré', 4799, 'OCC', 75, 'FR', 44.85726000, 1.89415000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, 'Q637612'),
(45893, 'Saint-Cézaire-sur-Siagne', 4812, 'PAC', 75, 'FR', 43.65015000, 6.79219000, '2019-10-05 22:48:47', '2020-05-01 17:22:47', 1, 'Q853458'),
(45894, 'Saint-Denis', 4796, 'IDF', 75, 'FR', 48.93564000, 2.35387000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45895, 'Saint-Denis-de-Cabanne', 4798, 'ARA', 75, 'FR', 46.17249000, 4.21447000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45896, 'Saint-Denis-de-Gastines', 4802, 'PDL', 75, 'FR', 48.34210000, -0.85869000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45897, 'Saint-Denis-de-Pile', 4795, 'NAQ', 75, 'FR', 45.00000000, -0.20000000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45898, 'Saint-Denis-d’Oléron', 4795, 'NAQ', 75, 'FR', 46.03496000, -1.37867000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, 'Q853458'),
(45899, 'Saint-Denis-en-Bugey', 4798, 'ARA', 75, 'FR', 45.94963000, 5.32773000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45900, 'Saint-Denis-en-Val', 4818, 'CVL', 75, 'FR', 47.87321000, 1.96601000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45901, 'Saint-Denis-la-Chevasse', 4802, 'PDL', 75, 'FR', 46.82273000, -1.35749000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45902, 'Saint-Denis-les-Ponts', 4818, 'CVL', 75, 'FR', 48.06692000, 1.29764000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45903, 'Saint-Denis-lès-Bourg', 4798, 'ARA', 75, 'FR', 46.20217000, 5.18924000, '2019-10-05 22:48:47', '2020-05-01 17:22:43', 1, 'Q853458'),
(45904, 'Saint-Denis-sur-Sarthon', 4804, 'NOR', 75, 'FR', 48.45320000, -0.04720000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q853458'),
(45905, 'Saint-Didier', 4812, 'PAC', 75, 'FR', 44.00424000, 5.11053000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q626576'),
(45906, 'Saint-Didier', 4807, 'BRE', 75, 'FR', 48.09506000, -1.37276000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q626576'),
(45907, 'Saint-Didier-de-Formans', 4798, 'ARA', 75, 'FR', 45.95969000, 4.78406000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q626576'),
(45908, 'Saint-Didier-de-la-Tour', 4798, 'ARA', 75, 'FR', 45.55755000, 5.48026000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q626576'),
(45909, 'Saint-Didier-en-Velay', 4798, 'ARA', 75, 'FR', 45.30265000, 4.27514000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q626576'),
(45910, 'Saint-Didier-sous-Riverie', 4798, 'ARA', 75, 'FR', 45.59627000, 4.60650000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q626576'),
(45911, 'Saint-Didier-sur-Chalaronne', 4798, 'ARA', 75, 'FR', 46.17714000, 4.81626000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q626576'),
(45912, 'Saint-Dizier', 4820, 'GES', 75, 'FR', 48.63773000, 4.94892000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q208310'),
(45913, 'Saint-Dié-des-Vosges', 4820, 'GES', 75, 'FR', 48.28333000, 6.95000000, '2019-10-05 22:48:47', '2020-05-01 17:22:45', 1, 'Q34427'),
(45914, 'Saint-Dolay', 4807, 'BRE', 75, 'FR', 47.54476000, -2.15466000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q34427'),
(45915, 'Saint-Domineuc', 4807, 'BRE', 75, 'FR', 48.37191000, -1.87544000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q34427'),
(45916, 'Saint-Donan', 4807, 'BRE', 75, 'FR', 48.46958000, -2.88442000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q34427'),
(45917, 'Saint-Doulchard', 4818, 'CVL', 75, 'FR', 47.10371000, 2.35200000, '2019-10-05 22:48:47', '2019-10-05 22:48:47', 1, 'Q34427'),
(45918, 'Saint-Drézéry', 4799, 'OCC', 75, 'FR', 43.72991000, 3.97620000, '2019-10-05 22:48:47', '2020-05-01 17:22:46', 1, 'Q34427'),
(45919, 'Saint-Dyé-sur-Loire', 4818, 'CVL', 75, 'FR', 47.65595000, 1.48847000, '2019-10-05 22:48:48', '2020-05-01 17:22:44', 1, 'Q829633'),
(45920, 'Saint-Désir', 4804, 'NOR', 75, 'FR', 49.14056000, 0.21398000, '2019-10-05 22:48:48', '2020-05-01 17:22:45', 1, 'Q829633'),
(45921, 'Saint-Erblon', 4807, 'BRE', 75, 'FR', 48.01895000, -1.65162000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q643467'),
(45922, 'Saint-Erme-Outre-et-Ramecourt', 4828, 'HDF', 75, 'FR', 49.51464000, 3.84060000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q643467'),
(45923, 'Saint-Estèphe', 4795, 'NAQ', 75, 'FR', 45.26252000, -0.77237000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q643467'),
(45924, 'Saint-Estève', 4799, 'OCC', 75, 'FR', 42.71175000, 2.84490000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q643467'),
(45925, 'Saint-Etienne-de-Cuines', 4798, 'ARA', 75, 'FR', 45.35000000, 6.28333000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q643467'),
(45926, 'Saint-Etienne-de-Tulmont', 4799, 'OCC', 75, 'FR', 44.05000000, 1.46667000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q643467'),
(45927, 'Saint-Eustache-la-Forêt', 4804, 'NOR', 75, 'FR', 49.55137000, 0.45599000, '2019-10-05 22:48:48', '2020-05-01 17:22:45', 1, 'Q643467'),
(45928, 'Saint-Eusèbe', 4825, 'BFC', 75, 'FR', 46.71346000, 4.46203000, '2019-10-05 22:48:48', '2020-05-01 17:22:44', 1, 'Q643467'),
(45929, 'Saint-Fargeau', 4825, 'BFC', 75, 'FR', 47.64133000, 3.07066000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q622863'),
(45930, 'Saint-Fargeau-Ponthierry', 4796, 'IDF', 75, 'FR', 48.55713000, 2.52840000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q271845'),
(45931, 'Saint-Fiacre-sur-Maine', 4802, 'PDL', 75, 'FR', 47.14255000, -1.41714000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45932, 'Saint-Florent', 4806, '20R', 75, 'FR', 42.68150000, 9.30396000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q677952'),
(45933, 'Saint-Florent-des-Bois', 4802, 'PDL', 75, 'FR', 46.59377000, -1.31580000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45934, 'Saint-Florent-le-Vieil', 4802, 'PDL', 75, 'FR', 47.36069000, -1.01515000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q120161'),
(45935, 'Saint-Florent-sur-Auzonnet', 4799, 'OCC', 75, 'FR', 44.24032000, 4.11252000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q120161'),
(45936, 'Saint-Florent-sur-Cher', 4818, 'CVL', 75, 'FR', 46.99552000, 2.25076000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45937, 'Saint-Florentin', 4825, 'BFC', 75, 'FR', 48.00057000, 3.72489000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q840511'),
(45938, 'Saint-Flour', 4798, 'ARA', 75, 'FR', 45.03374000, 3.09297000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q210763'),
(45939, 'Saint-Folquin', 4828, 'HDF', 75, 'FR', 50.94473000, 2.12433000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q210763'),
(45940, 'Saint-Fons', 4798, 'ARA', 75, 'FR', 45.70875000, 4.85325000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q828676'),
(45941, 'Saint-Forgeux', 4798, 'ARA', 75, 'FR', 45.85895000, 4.47480000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q828676'),
(45942, 'Saint-Fort', 4802, 'PDL', 75, 'FR', 47.79928000, -0.72095000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q651723'),
(45943, 'Saint-François', 4798, 'ARA', 75, 'FR', 46.41537000, 3.90542000, '2019-10-05 22:48:48', '2020-05-01 17:22:43', 1, 'Q651723'),
(45944, 'Saint-Fulgent', 4802, 'PDL', 75, 'FR', 46.85226000, -1.17798000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45945, 'Saint-Félicien', 4798, 'ARA', 75, 'FR', 45.08600000, 4.62738000, '2019-10-05 22:48:48', '2020-05-01 17:22:43', 1, '********'),
(45946, 'Saint-Féliu-d’Avall', 4799, 'OCC', 75, 'FR', 42.68117000, 2.73813000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q191008'),
(45947, 'Saint-Félix', 4798, 'ARA', 75, 'FR', 45.80269000, 5.97714000, '2019-10-05 22:48:48', '2020-05-01 17:22:43', 1, 'Q191008'),
(45948, 'Saint-Félix-Lauragais', 4799, 'OCC', 75, 'FR', 43.44863000, 1.88814000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q191008'),
(45949, 'Saint-Galmier', 4798, 'ARA', 75, 'FR', 45.59787000, 4.31086000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q687892'),
(45950, 'Saint-Gatien-des-Bois', 4804, 'NOR', 75, 'FR', 49.35000000, 0.18333000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q687892'),
(45951, 'Saint-Gaudens', 4799, 'OCC', 75, 'FR', 43.10809000, 0.72345000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q209777'),
(45952, 'Saint-Gaultier', 4818, 'CVL', 75, 'FR', 46.63518000, 1.41289000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q627194'),
(45953, 'Saint-Gelais', 4795, 'NAQ', 75, 'FR', 46.38234000, -0.39084000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q1467481'),
(45954, 'Saint-Gence', 4795, 'NAQ', 75, 'FR', 45.92198000, 1.13726000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q1467481'),
(45955, 'Saint-Genest-Lerpt', 4798, 'ARA', 75, 'FR', 45.44347000, 4.33968000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q1116324'),
(45956, 'Saint-Genest-Malifaux', 4798, 'ARA', 75, 'FR', 45.34064000, 4.41652000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q1116324'),
(45957, 'Saint-Gengoux-le-National', 4825, 'BFC', 75, 'FR', 46.61476000, 4.66261000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q1469211'),
(45958, 'Saint-Geniez-d’Olt', 4799, 'OCC', 75, 'FR', 44.46561000, 2.97261000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q1469211'),
(45959, 'Saint-Genis-Laval', 4798, 'ARA', 75, 'FR', 45.69542000, 4.79316000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q910089'),
(45960, 'Saint-Genis-Pouilly', 4798, 'ARA', 75, 'FR', 46.24356000, 6.02119000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q332416'),
(45961, 'Saint-Genis-de-Saintonge', 4795, 'NAQ', 75, 'FR', 45.48107000, -0.56848000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q1062435'),
(45962, 'Saint-Genis-les-Ollières', 4798, 'ARA', 75, 'FR', 45.75710000, 4.72625000, '2019-10-05 22:48:48', '2020-05-01 17:22:43', 1, 'Q1062435'),
(45963, 'Saint-Genix-sur-Guiers', 4798, 'ARA', 75, 'FR', 45.59864000, 5.63559000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q1062435'),
(45964, 'Saint-Geniès-Bellevue', 4799, 'OCC', 75, 'FR', 43.68409000, 1.48693000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q1062435'),
(45965, 'Saint-Geniès-de-Comolas', 4799, 'OCC', 75, 'FR', 44.06617000, 4.72157000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q1062435'),
(45966, 'Saint-Geniès-de-Fontedit', 4799, 'OCC', 75, 'FR', 43.46667000, 3.18333000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q275399'),
(45967, 'Saint-Geniès-de-Malgoirès', 4799, 'OCC', 75, 'FR', 43.95000000, 4.21667000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q275399'),
(45968, 'Saint-Geniès-des-Mourgues', 4799, 'OCC', 75, 'FR', 43.69780000, 4.03610000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q275399'),
(45969, 'Saint-Genès-Champanelle', 4798, 'ARA', 75, 'FR', 45.72037000, 3.01883000, '2019-10-05 22:48:48', '2020-05-01 17:22:43', 1, 'Q275399'),
(45970, 'Saint-Geoire-en-Valdaine', 4798, 'ARA', 75, 'FR', 45.45668000, 5.63515000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45971, 'Saint-Georges-Buttavent', 4802, 'PDL', 75, 'FR', 48.31018000, -0.69372000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45972, 'Saint-Georges-Haute-Ville', 4798, 'ARA', 75, 'FR', 45.55701000, 4.09801000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45973, 'Saint-Georges-de-Commiers', 4798, 'ARA', 75, 'FR', 45.04546000, 5.70628000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45974, 'Saint-Georges-de-Didonne', 4795, 'NAQ', 75, 'FR', 45.60342000, -1.00487000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q832057'),
(45975, 'Saint-Georges-de-Luzençon', 4799, 'OCC', 75, 'FR', 44.06471000, 2.98597000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q832057'),
(45976, 'Saint-Georges-de-Mons', 4798, 'ARA', 75, 'FR', 45.93941000, 2.83866000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q832057'),
(45977, 'Saint-Georges-de-Montaigu', 4802, 'PDL', 75, 'FR', 46.94655000, -1.29262000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45978, 'Saint-Georges-de-Pointindoux', 4802, 'PDL', 75, 'FR', 46.64462000, -1.62204000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45979, 'Saint-Georges-de-Reintembault', 4807, 'BRE', 75, 'FR', 48.50744000, -1.24328000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q686913'),
(45980, 'Saint-Georges-de-Reneins', 4798, 'ARA', 75, 'FR', 46.06185000, 4.72169000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q686913'),
(45981, 'Saint-Georges-des-Coteaux', 4795, 'NAQ', 75, 'FR', 45.76667000, -0.71667000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q611954'),
(45982, 'Saint-Georges-des-Groseillers', 4804, 'NOR', 75, 'FR', 48.76993000, -0.56768000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q251183'),
(45983, 'Saint-Georges-du-Bois', 4802, 'PDL', 75, 'FR', 47.97242000, 0.10113000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q251183'),
(45984, 'Saint-Georges-du-Bois', 4795, 'NAQ', 75, 'FR', 46.14074000, -0.73393000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q251183'),
(45985, 'Saint-Georges-les-Bains', 4798, 'ARA', 75, 'FR', 44.86100000, 4.80924000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q251183'),
(45986, 'Saint-Georges-lès-Baillargeaux', 4795, 'NAQ', 75, 'FR', 46.67017000, 0.40209000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, 'Q251183'),
(45987, 'Saint-Georges-sur-Allier', 4798, 'ARA', 75, 'FR', 45.71015000, 3.24261000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q251183'),
(45988, 'Saint-Georges-sur-Baulche', 4825, 'BFC', 75, 'FR', 47.80039000, 3.53144000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q251183'),
(45989, 'Saint-Georges-sur-Cher', 4818, 'CVL', 75, 'FR', 47.32996000, 1.13261000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45990, 'Saint-Georges-sur-Eure', 4818, 'CVL', 75, 'FR', 48.41869000, 1.35460000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45991, 'Saint-Georges-sur-Loire', 4802, 'PDL', 75, 'FR', 47.40792000, -0.76194000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q831235'),
(45992, 'Saint-Geours-de-Maremne', 4795, 'NAQ', 75, 'FR', 43.68936000, -1.22937000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q831235'),
(45993, 'Saint-Germain', 4825, 'BFC', 75, 'FR', 47.72496000, 6.53117000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, 'Q831235'),
(45994, 'Saint-Germain', 4820, 'GES', 75, 'FR', 48.25736000, 4.03264000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45995, 'Saint-Germain-Laprade', 4798, 'ARA', 75, 'FR', 45.03862000, 3.97004000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45996, 'Saint-Germain-Laval', 4796, 'IDF', 75, 'FR', 48.39968000, 2.99781000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45997, 'Saint-Germain-Laval', 4798, 'ARA', 75, 'FR', 45.83249000, 4.01444000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45998, 'Saint-Germain-Lembron', 4798, 'ARA', 75, 'FR', 45.45793000, 3.23973000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(45999, 'Saint-Germain-Lespinasse', 4798, 'ARA', 75, 'FR', 46.10342000, 3.96503000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46000, 'Saint-Germain-Nuelles', 4798, 'ARA', 75, 'FR', 45.85242000, 4.61182000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46001, 'Saint-Germain-Village', 4804, 'NOR', 75, 'FR', 49.34908000, 0.50331000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46002, 'Saint-Germain-de-Lusignan', 4795, 'NAQ', 75, 'FR', 45.45011000, -0.46147000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46003, 'Saint-Germain-de-Marencennes', 4795, 'NAQ', 75, 'FR', 46.07733000, -0.79133000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46004, 'Saint-Germain-de-Prinçay', 4802, 'PDL', 75, 'FR', 46.72107000, -1.02153000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, '********'),
(46005, 'Saint-Germain-de-la-Grange', 4796, 'IDF', 75, 'FR', 48.83436000, 1.89884000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46006, 'Saint-Germain-des-Fossés', 4798, 'ARA', 75, 'FR', 46.20682000, 3.43374000, '2019-10-05 22:48:48', '2020-05-01 17:22:43', 1, '********'),
(46007, 'Saint-Germain-des-Prés', 4802, 'PDL', 75, 'FR', 47.40998000, -0.83342000, '2019-10-05 22:48:48', '2020-05-01 17:22:46', 1, '********'),
(46008, 'Saint-Germain-des-Prés', 4818, 'CVL', 75, 'FR', 47.95384000, 2.84846000, '2019-10-05 22:48:48', '2020-05-01 17:22:44', 1, '********'),
(46009, 'Saint-Germain-du-Bois', 4825, 'BFC', 75, 'FR', 46.75287000, 5.24530000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46010, 'Saint-Germain-du-Corbéis', 4804, 'NOR', 75, 'FR', 48.42215000, 0.06193000, '2019-10-05 22:48:48', '2020-05-01 17:22:45', 1, '********'),
(46011, 'Saint-Germain-du-Plain', 4825, 'BFC', 75, 'FR', 46.69938000, 4.98510000, '2019-10-05 22:48:48', '2019-10-05 22:48:48', 1, '********'),
(46012, 'Saint-Germain-du-Puch', 4795, 'NAQ', 75, 'FR', 44.85000000, -0.31667000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, '********'),
(46013, 'Saint-Germain-du-Puy', 4818, 'CVL', 75, 'FR', 47.10000000, 2.48333000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q924210'),
(46014, 'Saint-Germain-en-Coglès', 4807, 'BRE', 75, 'FR', 48.40531000, -1.26369000, '2019-10-05 22:48:49', '2020-05-01 17:22:44', 1, 'Q924210'),
(46015, 'Saint-Germain-en-Laye', 4796, 'IDF', 75, 'FR', 48.89643000, 2.09040000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q185075'),
(46016, 'Saint-Germain-la-Blanche-Herbe', 4804, 'NOR', 75, 'FR', 49.18327000, -0.40426000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q185075'),
(46017, 'Saint-Germain-les-Belles', 4795, 'NAQ', 75, 'FR', 45.61356000, 1.49490000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q185075'),
(46018, 'Saint-Germain-lès-Arpajon', 4796, 'IDF', 75, 'FR', 48.59733000, 2.26481000, '2019-10-05 22:48:49', '2020-05-01 17:22:43', 1, 'Q185075'),
(46019, 'Saint-Germain-lès-Corbeil', 4796, 'IDF', 75, 'FR', 48.62211000, 2.48775000, '2019-10-05 22:48:49', '2020-05-01 17:22:43', 1, 'Q185075'),
(46020, 'Saint-Germain-sur-Avre', 4804, 'NOR', 75, 'FR', 48.76466000, 1.26776000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q185075'),
(46021, 'Saint-Germain-sur-Moine', 4802, 'PDL', 75, 'FR', 47.11746000, -1.12223000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q919429'),
(46022, 'Saint-Germain-sur-Morin', 4796, 'IDF', 75, 'FR', 48.88257000, 2.85127000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q919429'),
(46023, 'Saint-Germer-de-Fly', 4828, 'HDF', 75, 'FR', 49.44281000, 1.77986000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q919429'),
(46024, 'Saint-Gervais', 4795, 'NAQ', 75, 'FR', 45.01583000, -0.45238000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q919429'),
(46025, 'Saint-Gervais', 4802, 'PDL', 75, 'FR', 46.90174000, -2.00210000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q900294'),
(46026, 'Saint-Gervais-d’Auvergne', 4798, 'ARA', 75, 'FR', 46.02916000, 2.81901000, '2019-10-05 22:48:49', '2020-05-01 17:22:43', 1, 'Q900294'),
(46027, 'Saint-Gervais-en-Belin', 4802, 'PDL', 75, 'FR', 47.87720000, 0.21770000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q900294'),
(46028, 'Saint-Gervais-la-Forêt', 4818, 'CVL', 75, 'FR', 47.56705000, 1.35493000, '2019-10-05 22:48:49', '2020-05-01 17:22:44', 1, 'Q900294'),
(46029, 'Saint-Gervais-les-Bains', 4798, 'ARA', 75, 'FR', 45.89126000, 6.71678000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q534206'),
(46030, 'Saint-Gervais-les-Trois-Clochers', 4795, 'NAQ', 75, 'FR', 46.90067000, 0.40766000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q534206'),
(46031, 'Saint-Gervasy', 4799, 'OCC', 75, 'FR', 43.87687000, 4.46588000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q534206'),
(46032, 'Saint-Gildas-de-Rhuys', 4807, 'BRE', 75, 'FR', 47.50060000, -2.83825000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q127932'),
(46033, 'Saint-Gildas-des-Bois', 4802, 'PDL', 75, 'FR', 47.51622000, -2.03659000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q127932'),
(46034, 'Saint-Gilles', 4799, 'OCC', 75, 'FR', 43.67656000, 4.43024000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q243024'),
(46035, 'Saint-Gilles', 4807, 'BRE', 75, 'FR', 48.15381000, -1.82477000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q243024'),
(46036, 'Saint-Gilles-Croix-de-Vie', 4802, 'PDL', 75, 'FR', 46.69761000, -1.94561000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q818702'),
(46037, 'Saint-Giniez', 4812, 'PAC', 75, 'FR', 43.26946000, 5.38566000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q818702'),
(46038, 'Saint-Girons', 4799, 'OCC', 75, 'FR', 42.98491000, 1.14587000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q210776'),
(46039, 'Saint-Gobain', 4828, 'HDF', 75, 'FR', 49.59572000, 3.37750000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q325680'),
(46040, 'Saint-Gonnery', 4807, 'BRE', 75, 'FR', 48.12465000, -2.81848000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q325680'),
(46041, 'Saint-Gratien', 4796, 'IDF', 75, 'FR', 48.97350000, 2.28729000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q325680'),
(46042, 'Saint-Grégoire', 4807, 'BRE', 75, 'FR', 48.15085000, -1.68706000, '2019-10-05 22:48:49', '2020-05-01 17:22:44', 1, 'Q220159'),
(46043, 'Saint-Gély-du-Fesc', 4799, 'OCC', 75, 'FR', 43.69272000, 3.80492000, '2019-10-05 22:48:49', '2020-05-01 17:22:46', 1, 'Q220159'),
(46044, 'Saint-Génis-des-Fontaines', 4799, 'OCC', 75, 'FR', 42.54325000, 2.92060000, '2019-10-05 22:48:49', '2020-05-01 17:22:46', 1, 'Q220159'),
(46045, 'Saint-Gérand-le-Puy', 4798, 'ARA', 75, 'FR', 46.25790000, 3.51210000, '2019-10-05 22:48:49', '2020-05-01 17:22:43', 1, 'Q220159'),
(46046, 'Saint-Géréon', 4802, 'PDL', 75, 'FR', 47.36774000, -1.20262000, '2019-10-05 22:48:49', '2020-05-01 17:22:46', 1, 'Q220159'),
(46047, 'Saint-Henri', 4812, 'PAC', 75, 'FR', 43.36151000, 5.33069000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q220159'),
(46048, 'Saint-Herblain', 4802, 'PDL', 75, 'FR', 47.21765000, -1.64841000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q273922'),
(46049, 'Saint-Herblon', 4802, 'PDL', 75, 'FR', 47.40786000, -1.09738000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q273922'),
(46050, 'Saint-Hilaire', 4798, 'ARA', 75, 'FR', 45.30000000, 5.88333000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q670702'),
(46051, 'Saint-Hilaire', 4795, 'NAQ', 75, 'FR', 44.53333000, 0.71667000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q670702'),
(46052, 'Saint-Hilaire-Petitville', 4804, 'NOR', 75, 'FR', 49.30250000, -1.21995000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q670702'),
(46053, 'Saint-Hilaire-Saint-Mesmin', 4818, 'CVL', 75, 'FR', 47.86614000, 1.83351000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q670702'),
(46054, 'Saint-Hilaire-de-Brethmas', 4799, 'OCC', 75, 'FR', 44.08003000, 4.12478000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q670702'),
(46055, 'Saint-Hilaire-de-Chaléons', 4802, 'PDL', 75, 'FR', 47.10196000, -1.86690000, '2019-10-05 22:48:49', '2020-05-01 17:22:46', 1, 'Q670702'),
(46056, 'Saint-Hilaire-de-Clisson', 4802, 'PDL', 75, 'FR', 47.06222000, -1.30778000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, '********'),
(46057, 'Saint-Hilaire-de-Loulay', 4802, 'PDL', 75, 'FR', 47.00190000, -1.33079000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, '********'),
(46058, 'Saint-Hilaire-de-Riez', 4802, 'PDL', 75, 'FR', 46.71308000, -1.92583000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, '********'),
(46059, 'Saint-Hilaire-de-Talmont', 4802, 'PDL', 75, 'FR', 46.47002000, -1.60359000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, '*********'),
(46060, 'Saint-Hilaire-de-Villefranche', 4795, 'NAQ', 75, 'FR', 45.85091000, -0.52993000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, '*********'),
(46061, 'Saint-Hilaire-de-la-Côte', 4798, 'ARA', 75, 'FR', 45.39162000, 5.31590000, '2019-10-05 22:48:49', '2020-05-01 17:22:43', 1, '*********'),
(46062, 'Saint-Hilaire-des-Loges', 4802, 'PDL', 75, 'FR', 46.47190000, -0.66393000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46063, 'Saint-Hilaire-du-Harcouët', 4804, 'NOR', 75, 'FR', 48.57700000, -1.09004000, '2019-10-05 22:48:49', '2020-05-01 17:22:45', 1, 'Q921819'),
(46064, 'Saint-Hilaire-du-Rosier', 4798, 'ARA', 75, 'FR', 45.09942000, 5.24946000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46065, 'Saint-Hilaire-la-Palud', 4795, 'NAQ', 75, 'FR', 46.26676000, -0.71380000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46066, 'Saint-Hilaire-lez-Cambrai', 4828, 'HDF', 75, 'FR', 50.18419000, 3.41327000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46067, 'Saint-Hippolyte', 4799, 'OCC', 75, 'FR', 42.78550000, 2.96620000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46068, 'Saint-Hippolyte', 4820, 'GES', 75, 'FR', 48.23156000, 7.37121000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46069, 'Saint-Hippolyte', 4825, 'BFC', 75, 'FR', 47.31880000, 6.81617000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46070, 'Saint-Hippolyte', 4795, 'NAQ', 75, 'FR', 45.91884000, -0.89183000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q921819'),
(46071, 'Saint-Hippolyte-du-Fort', 4799, 'OCC', 75, 'FR', 43.96362000, 3.85572000, '2019-10-05 22:48:49', '2019-10-05 22:48:49', 1, 'Q770367'),
(46072, 'Saint-Héand', 4798, 'ARA', 75, 'FR', 45.52740000, 4.37077000, '2019-10-05 22:48:49', '2020-05-01 17:22:43', 1, 'Q770367'),
(46073, 'Saint-Hélen', 4807, 'BRE', 75, 'FR', 48.47002000, -1.95894000, '2019-10-05 22:48:50', '2020-05-01 17:22:44', 1, 'Q770367'),
(46074, 'Saint-Ismier', 4798, 'ARA', 75, 'FR', 45.25227000, 5.83073000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q770367'),
(46075, 'Saint-Jacques-de-la-Lande', 4807, 'BRE', 75, 'FR', 48.06514000, -1.72086000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q535371'),
(46076, 'Saint-Jacques-sur-Darnétal', 4804, 'NOR', 75, 'FR', 49.43980000, 1.20359000, '2019-10-05 22:48:50', '2020-05-01 17:22:45', 1, 'Q535371'),
(46077, 'Saint-Jacut-les-Pins', 4807, 'BRE', 75, 'FR', 47.68581000, -2.21514000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q535371'),
(46078, 'Saint-James', 4804, 'NOR', 75, 'FR', 48.52180000, -1.32629000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q478259'),
(46079, 'Saint-Jans-Cappel', 4828, 'HDF', 75, 'FR', 50.76387000, 2.72227000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q683015'),
(46080, 'Saint-Jean', 4802, 'PDL', 75, 'FR', 47.24553000, -0.38413000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q683015'),
(46081, 'Saint-Jean', 4799, 'OCC', 75, 'FR', 43.66420000, 1.49941000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46082, 'Saint-Jean du Désert', 4812, 'PAC', 75, 'FR', 43.29331000, 5.42134000, '2019-10-05 22:48:50', '2020-05-01 17:22:47', 1, '********'),
(46083, 'Saint-Jean-Bonnefonds', 4798, 'ARA', 75, 'FR', 45.45609000, 4.44223000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46084, 'Saint-Jean-Brévelay', 4807, 'BRE', 75, 'FR', 47.84497000, -2.72231000, '2019-10-05 22:48:50', '2020-05-01 17:22:44', 1, '********'),
(46085, 'Saint-Jean-Cap-Ferrat', 4812, 'PAC', 75, 'FR', 43.68843000, 7.33361000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q243713'),
(46086, 'Saint-Jean-Pied-de-Port', 4795, 'NAQ', 75, 'FR', 43.16363000, -1.23738000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q270294'),
(46087, 'Saint-Jean-de-Boiseau', 4802, 'PDL', 75, 'FR', 47.19315000, -1.72340000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q675750'),
(46088, 'Saint-Jean-de-Bournay', 4798, 'ARA', 75, 'FR', 45.50093000, 5.13818000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q675750'),
(46089, 'Saint-Jean-de-Braye', 4818, 'CVL', 75, 'FR', 47.91303000, 1.97705000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q787817'),
(46090, 'Saint-Jean-de-Fos', 4799, 'OCC', 75, 'FR', 43.70100000, 3.55171000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q787817'),
(46091, 'Saint-Jean-de-Gonville', 4798, 'ARA', 75, 'FR', 46.21298000, 5.95047000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q787817'),
(46092, 'Saint-Jean-de-Liversay', 4795, 'NAQ', 75, 'FR', 46.26894000, -0.87385000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q787817'),
(46093, 'Saint-Jean-de-Losne', 4825, 'BFC', 75, 'FR', 47.10247000, 5.26556000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q16832'),
(46094, 'Saint-Jean-de-Luz', 4795, 'NAQ', 75, 'FR', 43.38871000, -1.66267000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q232135'),
(46095, 'Saint-Jean-de-Maurienne', 4798, 'ARA', 75, 'FR', 45.27534000, 6.35293000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q208860'),
(46096, 'Saint-Jean-de-Moirans', 4798, 'ARA', 75, 'FR', 45.34506000, 5.58536000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q208860'),
(46097, 'Saint-Jean-de-Monts', 4802, 'PDL', 75, 'FR', 46.79088000, -2.08219000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q835202'),
(46098, 'Saint-Jean-de-Muzols', 4798, 'ARA', 75, 'FR', 45.08179000, 4.81396000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q835202'),
(46099, 'Saint-Jean-de-Niost', 4798, 'ARA', 75, 'FR', 45.83333000, 5.21667000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q835202'),
(46100, 'Saint-Jean-de-Sauves', 4795, 'NAQ', 75, 'FR', 46.84130000, 0.09272000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q835202'),
(46101, 'Saint-Jean-de-Sixt', 4798, 'ARA', 75, 'FR', 45.92312000, 6.41180000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q835202'),
(46102, 'Saint-Jean-de-Soudain', 4798, 'ARA', 75, 'FR', 45.56770000, 5.42880000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q835202'),
(46103, 'Saint-Jean-de-Thouars', 4795, 'NAQ', 75, 'FR', 46.96486000, -0.21114000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q835202'),
(46104, 'Saint-Jean-de-Védas', 4799, 'OCC', 75, 'FR', 43.57759000, 3.82603000, '2019-10-05 22:48:50', '2020-05-01 17:22:46', 1, 'Q835202'),
(46105, 'Saint-Jean-de-la-Ruelle', 4818, 'CVL', 75, 'FR', 47.91127000, 1.86483000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46106, 'Saint-Jean-des-Baisants', 4804, 'NOR', 75, 'FR', 49.09371000, -0.97289000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46107, 'Saint-Jean-des-Champs', 4804, 'NOR', 75, 'FR', 48.82800000, -1.46487000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46108, 'Saint-Jean-des-Mauvrets', 4802, 'PDL', 75, 'FR', 47.39857000, -0.44929000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46109, 'Saint-Jean-du-Cardonnay', 4804, 'NOR', 75, 'FR', 49.50455000, 1.01140000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46110, 'Saint-Jean-du-Falga', 4799, 'OCC', 75, 'FR', 43.08647000, 1.62780000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46111, 'Saint-Jean-du-Gard', 4799, 'OCC', 75, 'FR', 44.10523000, 3.88566000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q268590'),
(46112, 'Saint-Jean-d’Aulps', 4798, 'ARA', 75, 'FR', 46.23438000, 6.65327000, '2019-10-05 22:48:50', '2020-05-01 17:22:43', 1, 'Q268590'),
(46113, 'Saint-Jean-d’Illac', 4795, 'NAQ', 75, 'FR', 44.80869000, -0.78565000, '2019-10-05 22:48:50', '2020-05-01 17:22:46', 1, 'Q268590'),
(46114, 'Saint-Jean-en-Royans', 4798, 'ARA', 75, 'FR', 45.01968000, 5.29223000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q524056'),
(46115, 'Saint-Jean-la-Poterie', 4807, 'BRE', 75, 'FR', 47.63581000, -2.12420000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46116, 'Saint-Jean-le-Blanc', 4818, 'CVL', 75, 'FR', 47.89327000, 1.91540000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46117, 'Saint-Jean-le-Vieux', 4798, 'ARA', 75, 'FR', 46.03005000, 5.38798000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46118, 'Saint-Jean-les-Deux-Jumeaux', 4796, 'IDF', 75, 'FR', 48.95140000, 3.01959000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46119, 'Saint-Jean-sur-Couesnon', 4807, 'BRE', 75, 'FR', 48.29013000, -1.36835000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46120, 'Saint-Jean-sur-Mayenne', 4802, 'PDL', 75, 'FR', 48.13522000, -0.75613000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46121, 'Saint-Jean-sur-Veyle', 4798, 'ARA', 75, 'FR', 46.25902000, 4.91712000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46122, 'Saint-Jeannet', 4812, 'PAC', 75, 'FR', 43.74718000, 7.14299000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q128853'),
(46123, 'Saint-Jeoire', 4798, 'ARA', 75, 'FR', 46.13733000, 6.46172000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q769321'),
(46124, 'Saint-Joachim', 4802, 'PDL', 75, 'FR', 47.38349000, -2.19239000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q769321'),
(46125, 'Saint-Jorioz', 4798, 'ARA', 75, 'FR', 45.83074000, 6.15792000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q912337'),
(46126, 'Saint-Jory', 4799, 'OCC', 75, 'FR', 43.74146000, 1.37089000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q912337'),
(46127, 'Saint-Joseph', 4798, 'ARA', 75, 'FR', 45.55757000, 4.62076000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q912337'),
(46128, 'Saint-Joseph', 4812, 'PAC', 75, 'FR', 43.34617000, 5.37976000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q912337'),
(46129, 'Saint-Joseph-de-Rivière', 4798, 'ARA', 75, 'FR', 45.37572000, 5.69643000, '2019-10-05 22:48:50', '2020-05-01 17:22:43', 1, 'Q912337'),
(46130, 'Saint-Josse', 4828, 'HDF', 75, 'FR', 50.46801000, 1.66180000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q912337'),
(46131, 'Saint-Jouan-des-Guérets', 4807, 'BRE', 75, 'FR', 48.59932000, -1.97372000, '2019-10-05 22:48:50', '2020-05-01 17:22:44', 1, 'Q912337'),
(46132, 'Saint-Jouin-Bruneval', 4804, 'NOR', 75, 'FR', 49.64275000, 0.16400000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q831290'),
(46133, 'Saint-Jouvent', 4795, 'NAQ', 75, 'FR', 45.95680000, 1.20500000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q831290'),
(46134, 'Saint-Julien', 4825, 'BFC', 75, 'FR', 47.40029000, 5.14163000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q831290'),
(46135, 'Saint-Julien', 4807, 'BRE', 75, 'FR', 48.45142000, -2.81250000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q765366'),
(46136, 'Saint-Julien', 4812, 'PAC', 75, 'FR', 43.31334000, 5.44935000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q765366'),
(46137, 'Saint-Julien-Chapteuil', 4798, 'ARA', 75, 'FR', 45.03486000, 4.06110000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46138, 'Saint-Julien-Molin-Molette', 4798, 'ARA', 75, 'FR', 45.32148000, 4.61692000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46139, 'Saint-Julien-de-Concelles', 4802, 'PDL', 75, 'FR', 47.25285000, -1.38428000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q745201'),
(46140, 'Saint-Julien-de-Peyrolas', 4799, 'OCC', 75, 'FR', 44.28837000, 4.56507000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q745201'),
(46141, 'Saint-Julien-des-Landes', 4802, 'PDL', 75, 'FR', 46.63940000, -1.71381000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46142, 'Saint-Julien-du-Sault', 4825, 'BFC', 75, 'FR', 48.03188000, 3.29556000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46143, 'Saint-Julien-en-Born', 4795, 'NAQ', 75, 'FR', 44.06311000, -1.22445000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46144, 'Saint-Julien-en-Genevois', 4798, 'ARA', 75, 'FR', 46.14434000, 6.08256000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q368160'),
(46145, 'Saint-Julien-en-Saint-Alban', 4798, 'ARA', 75, 'FR', 44.75417000, 4.69678000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q368160'),
(46146, 'Saint-Julien-les-Rosiers', 4799, 'OCC', 75, 'FR', 44.17445000, 4.10803000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q368160'),
(46147, 'Saint-Julien-les-Villas', 4820, 'GES', 75, 'FR', 48.27127000, 4.09901000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q368160'),
(46148, 'Saint-Julien-lès-Metz', 4820, 'GES', 75, 'FR', 49.13288000, 6.20240000, '2019-10-05 22:48:50', '2020-05-01 17:22:45', 1, 'Q368160'),
(46149, 'Saint-Junien', 4795, 'NAQ', 75, 'FR', 45.88867000, 0.90143000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q472922'),
(46150, 'Saint-Just', 4812, 'PAC', 75, 'FR', 43.31700000, 5.40587000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q472922'),
(46151, 'Saint-Just', 4799, 'OCC', 75, 'FR', 43.65806000, 4.11472000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q196286'),
(46152, 'Saint-Just', 4807, 'BRE', 75, 'FR', 47.76581000, -1.96096000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q196286'),
(46153, 'Saint-Just', 4804, 'NOR', 75, 'FR', 49.10852000, 1.44101000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q196286'),
(46154, 'Saint-Just-Chaleyssin', 4798, 'ARA', 75, 'FR', 45.59421000, 4.99852000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q196286'),
(46155, 'Saint-Just-Luzac', 4795, 'NAQ', 75, 'FR', 45.80000000, -1.03333000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q1441246'),
(46156, 'Saint-Just-Malmont', 4798, 'ARA', 75, 'FR', 45.33932000, 4.31275000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, 'Q1441246'),
(46157, 'Saint-Just-Saint-Rambert', 4798, 'ARA', 75, 'FR', 45.49973000, 4.24141000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46158, 'Saint-Just-Sauvage', 4820, 'GES', 75, 'FR', 48.55478000, 3.78449000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46159, 'Saint-Just-d\'Ardèche', 4798, 'ARA', 75, 'FR', 44.30000000, 4.61667000, '2019-10-05 22:48:50', '2020-05-01 17:22:43', 1, '********'),
(46160, 'Saint-Just-de-Claix', 4798, 'ARA', 75, 'FR', 45.07562000, 5.28309000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46161, 'Saint-Just-en-Chaussée', 4828, 'HDF', 75, 'FR', 49.50503000, 2.43285000, '2019-10-05 22:48:50', '2020-05-01 17:22:45', 1, '********'),
(46162, 'Saint-Just-en-Chevalet', 4798, 'ARA', 75, 'FR', 45.91477000, 3.84234000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46163, 'Saint-Just-la-Pendue', 4798, 'ARA', 75, 'FR', 45.89423000, 4.24129000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46164, 'Saint-Just-le-Martel', 4795, 'NAQ', 75, 'FR', 45.86351000, 1.38829000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46165, 'Saint-Juéry', 4799, 'OCC', 75, 'FR', 43.95000000, 2.21667000, '2019-10-05 22:48:50', '2020-05-01 17:22:46', 1, '********'),
(46166, 'Saint-Jérôme', 4812, 'PAC', 75, 'FR', 43.33404000, 5.41733000, '2019-10-05 22:48:50', '2020-05-01 17:22:47', 1, '********'),
(46167, 'Saint-Lambert', 4812, 'PAC', 75, 'FR', 43.28606000, 5.36000000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46168, 'Saint-Lambert-du-Lattay', 4802, 'PDL', 75, 'FR', 47.30250000, -0.63321000, '2019-10-05 22:48:50', '2019-10-05 22:48:50', 1, '********'),
(46169, 'Saint-Lambert-la-Potherie', 4802, 'PDL', 75, 'FR', 47.48289000, -0.67789000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q522830'),
(46170, 'Saint-Lary-Soulan', 4799, 'OCC', 75, 'FR', 42.81713000, 0.32238000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46171, 'Saint-Lattier', 4798, 'ARA', 75, 'FR', 45.08815000, 5.20352000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46172, 'Saint-Laurent', 4820, 'GES', 75, 'FR', 49.76449000, 4.77193000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46173, 'Saint-Laurent-Blangy', 4828, 'HDF', 75, 'FR', 50.29446000, 2.80698000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46174, 'Saint-Laurent-Nouan', 4818, 'CVL', 75, 'FR', 47.71667000, 1.60000000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q839446'),
(46175, 'Saint-Laurent-de-Brévedent', 4804, 'NOR', 75, 'FR', 49.52564000, 0.25458000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, 'Q839446'),
(46176, 'Saint-Laurent-de-Chamousset', 4798, 'ARA', 75, 'FR', 45.73827000, 4.46415000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q840213'),
(46177, 'Saint-Laurent-de-Mure', 4798, 'ARA', 75, 'FR', 45.68563000, 5.04484000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q840213'),
(46178, 'Saint-Laurent-de-la-Plaine', 4802, 'PDL', 75, 'FR', 47.31738000, -0.80333000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q840213'),
(46179, 'Saint-Laurent-de-la-Prée', 4795, 'NAQ', 75, 'FR', 45.98259000, -1.03625000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, 'Q840213'),
(46180, 'Saint-Laurent-de-la-Salanque', 4799, 'OCC', 75, 'FR', 42.77270000, 2.98998000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q199847'),
(46181, 'Saint-Laurent-des-Arbres', 4799, 'OCC', 75, 'FR', 44.05493000, 4.70026000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q379957'),
(46182, 'Saint-Laurent-des-Autels', 4802, 'PDL', 75, 'FR', 47.28524000, -1.18881000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46183, 'Saint-Laurent-du-Pape', 4798, 'ARA', 75, 'FR', 44.82328000, 4.76679000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46184, 'Saint-Laurent-du-Pont', 4798, 'ARA', 75, 'FR', 45.39240000, 5.73312000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q301496'),
(46185, 'Saint-Laurent-du-Var', 4812, 'PAC', 75, 'FR', 43.67323000, 7.19000000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q217755'),
(46186, 'Saint-Laurent-en-Grandvaux', 4825, 'BFC', 75, 'FR', 46.58333000, 5.95000000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q217755'),
(46187, 'Saint-Laurent-en-Royans', 4798, 'ARA', 75, 'FR', 45.02653000, 5.32669000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q529993'),
(46188, 'Saint-Laurent-sur-Gorre', 4795, 'NAQ', 75, 'FR', 45.77052000, 0.95859000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q529993'),
(46189, 'Saint-Laurent-sur-Saône', 4798, 'ARA', 75, 'FR', 46.30532000, 4.84119000, '2019-10-05 22:48:51', '2020-05-01 17:22:43', 1, 'Q529993'),
(46190, 'Saint-Laurent-sur-Sèvre', 4802, 'PDL', 75, 'FR', 46.95809000, -0.89392000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, 'Q529993'),
(46191, 'Saint-Lazare', 4812, 'PAC', 75, 'FR', 43.30545000, 5.37443000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q529993'),
(46192, 'Saint-Leu', 4825, 'BFC', 75, 'FR', 46.73060000, 4.50083000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '*********'),
(46193, 'Saint-Leu-la-Forêt', 4796, 'IDF', 75, 'FR', 49.01667000, 2.25000000, '2019-10-05 22:48:51', '2020-05-01 17:22:43', 1, '*********'),
(46194, 'Saint-Lizier', 4799, 'OCC', 75, 'FR', 43.00183000, 1.13686000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q322771'),
(46195, 'Saint-Loubès', 4795, 'NAQ', 75, 'FR', 44.91536000, -0.42703000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, 'Q322771'),
(46196, 'Saint-Louis', 4820, 'GES', 75, 'FR', 47.59206000, 7.55923000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q322771'),
(46197, 'Saint-Louis', 4812, 'PAC', 75, 'FR', 43.34809000, 5.35463000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q322771'),
(46198, 'Saint-Louis-de-Montferrand', 4795, 'NAQ', 75, 'FR', 44.95000000, -0.53543000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q322771'),
(46199, 'Saint-Loup', 4812, 'PAC', 75, 'FR', 43.27766000, 5.43133000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q322771'),
(46200, 'Saint-Loup-Cammas', 4799, 'OCC', 75, 'FR', 43.69767000, 1.48127000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q322771'),
(46201, 'Saint-Loup-sur-Semouse', 4825, 'BFC', 75, 'FR', 47.88345000, 6.27530000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q901506'),
(46202, 'Saint-Lubin-des-Joncherets', 4818, 'CVL', 75, 'FR', 48.76667000, 1.21667000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46203, 'Saint-Lumine-de-Clisson', 4802, 'PDL', 75, 'FR', 47.08413000, -1.33524000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46204, 'Saint-Lumine-de-Coutais', 4802, 'PDL', 75, 'FR', 47.05399000, -1.72777000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46205, 'Saint-Lunaire', 4807, 'BRE', 75, 'FR', 48.63811000, -2.11392000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46206, 'Saint-Lupicin', 4825, 'BFC', 75, 'FR', 46.40034000, 5.79220000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46207, 'Saint-Lyphard', 4802, 'PDL', 75, 'FR', 47.39822000, -2.30642000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46208, 'Saint-Lys', 4799, 'OCC', 75, 'FR', 43.51127000, 1.17557000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, '********'),
(46209, 'Saint-Lyé', 4820, 'GES', 75, 'FR', 48.36502000, 3.99900000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, '********'),
(46210, 'Saint-Léger-de-Montbrun', 4795, 'NAQ', 75, 'FR', 47.00000000, -0.13333000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, '********'),
(46211, 'Saint-Léger-des-Bois', 4802, 'PDL', 75, 'FR', 47.46238000, -0.70953000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, '********'),
(46212, 'Saint-Léger-des-Vignes', 4825, 'BFC', 75, 'FR', 46.84063000, 3.45488000, '2019-10-05 22:48:51', '2020-05-01 17:22:44', 1, '********'),
(46213, 'Saint-Léger-du-Bourg-Denis', 4804, 'NOR', 75, 'FR', 49.43345000, 1.15803000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, '********'),
(46214, 'Saint-Léger-en-Yvelines', 4796, 'IDF', 75, 'FR', 48.72173000, 1.76638000, '2019-10-05 22:48:51', '2020-05-01 17:22:43', 1, 'Q274844'),
(46215, 'Saint-Léger-les-Vignes', 4802, 'PDL', 75, 'FR', 47.13587000, -1.73060000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, 'Q274844'),
(46216, 'Saint-Léger-lès-Domart', 4828, 'HDF', 75, 'FR', 50.05208000, 2.14067000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, 'Q274844'),
(46217, 'Saint-Léger-sous-Cholet', 4802, 'PDL', 75, 'FR', 47.09405000, -0.91024000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, '********'),
(46218, 'Saint-Léger-sur-Dheune', 4825, 'BFC', 75, 'FR', 46.84548000, 4.63607000, '2019-10-05 22:48:51', '2020-05-01 17:22:44', 1, '********'),
(46219, 'Saint-Léger-sur-Roanne', 4798, 'ARA', 75, 'FR', 46.04174000, 3.99644000, '2019-10-05 22:48:51', '2020-05-01 17:22:43', 1, '********'),
(46220, 'Saint-Léon-sur-l’Isle', 4795, 'NAQ', 75, 'FR', 45.11491000, 0.50444000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, '********'),
(46221, 'Saint-Léonard', 4828, 'HDF', 75, 'FR', 50.69058000, 1.62536000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, '********'),
(46222, 'Saint-Léonard', 4804, 'NOR', 75, 'FR', 49.74252000, 0.35907000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, '********'),
(46223, 'Saint-Léonard', 4820, 'GES', 75, 'FR', 48.21638000, 6.94355000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, '********'),
(46224, 'Saint-Léonard-de-Noblat', 4795, 'NAQ', 75, 'FR', 45.83566000, 1.49174000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, 'Q200202'),
(46225, 'Saint-Lô', 4804, 'NOR', 75, 'FR', 49.11624000, -1.09031000, '2019-10-05 22:48:51', '2020-05-01 17:22:45', 1, 'Q181704'),
(46226, 'Saint-Macaire', 4795, 'NAQ', 75, 'FR', 44.56527000, -0.22431000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q181704'),
(46227, 'Saint-Macaire-en-Mauges', 4802, 'PDL', 75, 'FR', 47.12357000, -0.99120000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q181704'),
(46228, 'Saint-Maixant', 4795, 'NAQ', 75, 'FR', 44.57868000, -0.25920000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q181704'),
(46229, 'Saint-Malo', 4807, 'BRE', 75, 'FR', 48.64720000, -2.00883000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q163108'),
(46230, 'Saint-Malo-de-Guersac', 4802, 'PDL', 75, 'FR', 47.35345000, -2.17773000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q775568'),
(46231, 'Saint-Malô-du-Bois', 4802, 'PDL', 75, 'FR', 46.93333000, -0.90000000, '2019-10-05 22:48:51', '2020-05-01 17:22:46', 1, 'Q775568'),
(46232, 'Saint-Mamert-du-Gard', 4799, 'OCC', 75, 'FR', 43.88835000, 4.18725000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q775568'),
(46233, 'Saint-Mamet-la-Salvetat', 4798, 'ARA', 75, 'FR', 44.85813000, 2.30688000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q775568'),
(46234, 'Saint-Mammès', 4796, 'IDF', 75, 'FR', 48.38458000, 2.81578000, '2019-10-05 22:48:51', '2020-05-01 17:22:43', 1, 'Q775568'),
(46235, 'Saint-Mandrier-sur-Mer', 4812, 'PAC', 75, 'FR', 43.07800000, 5.92900000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q910071'),
(46236, 'Saint-Mandé', 4796, 'IDF', 75, 'FR', 48.83864000, 2.41579000, '2019-10-05 22:48:51', '2020-05-01 17:22:43', 1, 'Q910071'),
(46237, 'Saint-Manvieu', 4804, 'NOR', 75, 'FR', 49.18067000, -0.50211000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q910071'),
(46238, 'Saint-Manvieu-Norrey', 4804, 'NOR', 75, 'FR', 49.18139000, -0.50000000, '2019-10-05 22:48:51', '2019-10-05 22:48:51', 1, 'Q862757'),
(46239, 'Saint-Marc-Jaumegarde', 4812, 'PAC', 75, 'FR', 43.54718000, 5.52280000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q753842'),
(46240, 'Saint-Marc-le-Blanc', 4807, 'BRE', 75, 'FR', 48.36498000, -1.40938000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q213644'),
(46241, 'Saint-Marcel', 4804, 'NOR', 75, 'FR', 49.10000000, 1.45000000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q648073'),
(46242, 'Saint-Marcel', 4812, 'PAC', 75, 'FR', 43.28745000, 5.46604000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q648073'),
(46243, 'Saint-Marcel', 4825, 'BFC', 75, 'FR', 46.77371000, 4.89205000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q903247'),
(46244, 'Saint-Marcel', 4818, 'CVL', 75, 'FR', 46.60193000, 1.51324000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q903247'),
(46245, 'Saint-Marcel-d\'Ardèche', 4798, 'ARA', 75, 'FR', 44.32726000, 4.61769000, '2019-10-05 22:48:52', '2020-05-01 17:22:43', 1, 'Q903247'),
(46246, 'Saint-Marcel-lès-Annonay', 4798, 'ARA', 75, 'FR', 45.28613000, 4.62616000, '2019-10-05 22:48:52', '2020-05-01 17:22:43', 1, 'Q903247'),
(46247, 'Saint-Marcel-lès-Sauzet', 4798, 'ARA', 75, 'FR', 44.59752000, 4.80541000, '2019-10-05 22:48:52', '2020-05-01 17:22:43', 1, 'Q903247'),
(46248, 'Saint-Marcel-lès-Valence', 4798, 'ARA', 75, 'FR', 44.97132000, 4.95316000, '2019-10-05 22:48:52', '2020-05-01 17:22:43', 1, 'Q903247'),
(46249, 'Saint-Marcel-sur-Aude', 4799, 'OCC', 75, 'FR', 43.25000000, 2.93333000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q903247'),
(46250, 'Saint-Marcellin', 4798, 'ARA', 75, 'FR', 45.14914000, 5.31673000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q521432'),
(46251, 'Saint-Marcellin-en-Forez', 4798, 'ARA', 75, 'FR', 45.49676000, 4.17233000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, '********'),
(46252, 'Saint-Mard', 4796, 'IDF', 75, 'FR', 49.03701000, 2.69645000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, '********'),
(46253, 'Saint-Mariens', 4795, 'NAQ', 75, 'FR', 45.11631000, -0.40084000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, '********'),
(46254, 'Saint-Mars-de-Coutais', 4802, 'PDL', 75, 'FR', 47.11153000, -1.73437000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, '********'),
(46255, 'Saint-Mars-du-Désert', 4802, 'PDL', 75, 'FR', 47.36560000, -1.40678000, '2019-10-05 22:48:52', '2020-05-01 17:22:46', 1, '********'),
(46256, 'Saint-Mars-d’Outillé', 4802, 'PDL', 75, 'FR', 47.87034000, 0.33221000, '2019-10-05 22:48:52', '2020-05-01 17:22:46', 1, '********'),
(46257, 'Saint-Mars-la-Brière', 4802, 'PDL', 75, 'FR', 48.03046000, 0.37319000, '2019-10-05 22:48:52', '2020-05-01 17:22:46', 1, '********'),
(46258, 'Saint-Mars-la-Jaille', 4802, 'PDL', 75, 'FR', 47.52566000, -1.18483000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, '********'),
(46259, 'Saint-Martin-Belle-Roche', 4825, 'BFC', 75, 'FR', 46.38312000, 4.85539000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, '********'),
(46260, 'Saint-Martin-Bellevue', 4798, 'ARA', 75, 'FR', 45.96262000, 6.15763000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, '********'),
(46261, 'Saint-Martin-Boulogne', 4828, 'HDF', 75, 'FR', 50.72691000, 1.61864000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q875983'),
(46262, 'Saint-Martin-Lacaussade', 4795, 'NAQ', 75, 'FR', 45.14631000, -0.64310000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q875983'),
(46263, 'Saint-Martin-Lalande', 4799, 'OCC', 75, 'FR', 43.29928000, 2.02004000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q875983'),
(46264, 'Saint-Martin-Longueau', 4828, 'HDF', 75, 'FR', 49.34281000, 2.60276000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q875983'),
(46265, 'Saint-Martin-Osmonville', 4804, 'NOR', 75, 'FR', 49.63873000, 1.29952000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q875983'),
(46266, 'Saint-Martin-Vésubie', 4812, 'PAC', 75, 'FR', 44.06892000, 7.25583000, '2019-10-05 22:48:52', '2020-05-01 17:22:47', 1, 'Q875983'),
(46267, 'Saint-Martin-au-Laërt', 4828, 'HDF', 75, 'FR', 50.75179000, 2.24051000, '2019-10-05 22:48:52', '2020-05-01 17:22:45', 1, 'Q244295'),
(46268, 'Saint-Martin-de-Belleville', 4798, 'ARA', 75, 'FR', 45.38175000, 6.50519000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q244295'),
(46269, 'Saint-Martin-de-Boscherville', 4804, 'NOR', 75, 'FR', 49.44377000, 0.96271000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q898272'),
(46270, 'Saint-Martin-de-Crau', 4812, 'PAC', 75, 'FR', 43.63955000, 4.81270000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q898272'),
(46271, 'Saint-Martin-de-Fontenay', 4804, 'NOR', 75, 'FR', 49.11523000, -0.37391000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q898272'),
(46272, 'Saint-Martin-de-Hinx', 4795, 'NAQ', 75, 'FR', 43.58238000, -1.26809000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q898272'),
(46273, 'Saint-Martin-de-Landelles', 4804, 'NOR', 75, 'FR', 48.54530000, -1.17240000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q898272'),
(46274, 'Saint-Martin-de-Londres', 4799, 'OCC', 75, 'FR', 43.79040000, 3.73066000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q898272'),
(46275, 'Saint-Martin-de-Nigelles', 4818, 'CVL', 75, 'FR', 48.61234000, 1.60920000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q898272'),
(46276, 'Saint-Martin-de-Queyrières', 4812, 'PAC', 75, 'FR', 44.82319000, 6.57901000, '2019-10-05 22:48:52', '2020-05-01 17:22:47', 1, 'Q1089751'),
(46277, 'Saint-Martin-de-Ré', 4795, 'NAQ', 75, 'FR', 46.20311000, -1.36726000, '2019-10-05 22:48:52', '2020-05-01 17:22:46', 1, 'Q845987'),
(46278, 'Saint-Martin-de-Seignanx', 4795, 'NAQ', 75, 'FR', 43.54283000, -1.38946000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q845987'),
(46279, 'Saint-Martin-de-Valamas', 4798, 'ARA', 75, 'FR', 44.93718000, 4.36865000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q845987'),
(46280, 'Saint-Martin-de-Valgalgues', 4799, 'OCC', 75, 'FR', 44.16315000, 4.08364000, '2019-10-05 22:48:52', '2019-10-05 22:48:52', 1, 'Q845987'),
(46281, 'Saint-Martin-de-la-Place', 4802, 'PDL', 75, 'FR', 47.31747000, -0.14849000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q845987'),
(46282, 'Saint-Martin-des-Besaces', 4804, 'NOR', 75, 'FR', 49.01091000, -0.84500000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q653340'),
(46283, 'Saint-Martin-des-Champs', 4804, 'NOR', 75, 'FR', 48.66841000, -1.33393000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q653340'),
(46284, 'Saint-Martin-des-Champs', 4807, 'BRE', 75, 'FR', 48.58333000, -3.83333000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q653340'),
(46285, 'Saint-Martin-des-Noyers', 4802, 'PDL', 75, 'FR', 46.72226000, -1.17727000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, '********'),
(46286, 'Saint-Martin-du-Fouilloux', 4802, 'PDL', 75, 'FR', 47.43360000, -0.70357000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, '********'),
(46287, 'Saint-Martin-du-Frêne', 4798, 'ARA', 75, 'FR', 46.13591000, 5.55049000, '2019-10-05 22:48:53', '2020-05-01 17:22:43', 1, 'Q275313'),
(46288, 'Saint-Martin-du-Manoir', 4804, 'NOR', 75, 'FR', 49.53213000, 0.23759000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46289, 'Saint-Martin-du-Mont', 4798, 'ARA', 75, 'FR', 46.09800000, 5.31706000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46290, 'Saint-Martin-du-Tertre', 4796, 'IDF', 75, 'FR', 49.10743000, 2.34533000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46291, 'Saint-Martin-du-Tertre', 4825, 'BFC', 75, 'FR', 48.21659000, 3.26151000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46292, 'Saint-Martin-du-Var', 4812, 'PAC', 75, 'FR', 43.81846000, 7.19062000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46293, 'Saint-Martin-du-Vivier', 4804, 'NOR', 75, 'FR', 49.46667000, 1.16667000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46294, 'Saint-Martin-d’Auxigny', 4818, 'CVL', 75, 'FR', 47.20371000, 2.41553000, '2019-10-05 22:48:53', '2020-05-01 17:22:44', 1, 'Q275313'),
(46295, 'Saint-Martin-d’Hères', 4798, 'ARA', 75, 'FR', 45.17870000, 5.76281000, '2019-10-05 22:48:53', '2020-05-01 17:22:43', 1, 'Q275313'),
(46296, 'Saint-Martin-en-Bresse', 4825, 'BFC', 75, 'FR', 46.81725000, 5.06027000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46297, 'Saint-Martin-en-Campagne', 4804, 'NOR', 75, 'FR', 49.95648000, 1.22233000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46298, 'Saint-Martin-en-Haut', 4798, 'ARA', 75, 'FR', 45.65984000, 4.56180000, '2019-10-05 22:48:53', '2019-10-05 22:48:53', 1, 'Q275313'),
(46299, 'Saint-Martin-la-Plaine', 4798, 'ARA', 75, 'FR', 45.54635000, 4.59749000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46300, 'Saint-Martin-le-Beau', 4818, 'CVL', 75, 'FR', 47.35566000, 0.90953000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46301, 'Saint-Martin-le-Vinoux', 4798, 'ARA', 75, 'FR', 45.20297000, 5.71630000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46302, 'Saint-Martin-sous-Mouzeuil', 4802, 'PDL', 75, 'FR', 46.45905000, -0.98893000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********');

