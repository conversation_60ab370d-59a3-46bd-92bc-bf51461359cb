INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(29835, 'Veringenstadt', 3006, 'BW', 82, 'DE', 48.18515000, 9.21079000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q82304'),
(29836, 'Verl', 3017, 'NW', 82, 'DE', 51.88333000, 8.51667000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q225375'),
(29837, 'Versmold', 3017, 'NW', 82, 'DE', 52.04009000, 8.15272000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q225334'),
(29838, 'Vestenbergsgreuth', 3009, 'B<PERSON>', 82, '<PERSON>', 49.68764000, 10.65157000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q505869'),
(29839, '<PERSON>etschau', 3013, 'BB', 82, '<PERSON>', 51.78638000, 14.07941000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q147633'),
(29840, '<PERSON>ettelschoß', 3019, 'RP', 82, 'DE', 50.61667000, 7.35000000, '2019-10-05 22:44:59', '2020-05-01 17:22:49', 1, 'Q569124'),
(29841, 'Vettweiß', 3017, 'NW', 82, 'DE', 50.73333000, 6.60000000, '2019-10-05 22:44:59', '2020-05-01 17:22:49', 1, 'Q200087'),
(29842, 'Viechtach', 3009, 'BY', 82, 'DE', 49.08001000, 12.88566000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q258787'),
(29843, 'Vienenburg', 3008, 'NI', 82, 'DE', 51.95242000, 10.56374000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q570028'),
(29844, 'Viereck', 3007, 'MV', 82, 'DE', 53.54898000, 14.04001000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q570028'),
(29845, 'Viereth-Trunstadt', 3009, 'BY', 82, 'DE', 49.92222000, 10.77716000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q550723'),
(29846, 'Vierkirchen', 3009, 'BY', 82, 'DE', 48.36667000, 11.46667000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q550723'),
(29847, 'Viernau', 3015, 'TH', 82, 'DE', 50.66225000, 10.55778000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q556518'),
(29848, 'Viernheim', 3018, 'HE', 82, 'DE', 49.54033000, 8.57820000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q16055'),
(29849, 'Viersen', 3017, 'NW', 82, 'DE', 51.25435000, 6.39441000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q3902'),
(29850, 'Vilgertshofen', 3009, 'BY', 82, 'DE', 47.95000000, 10.91667000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q49292321'),
(29851, 'Villenbach', 3009, 'BY', 82, 'DE', 48.50887000, 10.61468000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q49292321'),
(29852, 'Villingen-Schwenningen', 3006, 'BW', 82, 'DE', 48.06226000, 8.49358000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q3865'),
(29853, 'Villingendorf', 3006, 'BW', 82, 'DE', 48.20000000, 8.58333000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q83284'),
(29854, 'Villmar', 3018, 'HE', 82, 'DE', 50.39287000, 8.19310000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q621483'),
(29855, 'Vilsbiburg', 3009, 'BY', 82, 'DE', 48.45296000, 12.35604000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q521018'),
(29856, 'Vilseck', 3009, 'BY', 82, 'DE', 49.61480000, 11.80261000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q508107'),
(29857, 'Vilsheim', 3009, 'BY', 82, 'DE', 48.44882000, 12.10686000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q526491'),
(29858, 'Vilshofen', 3009, 'BY', 82, 'DE', 48.62695000, 13.19222000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q526491'),
(29859, 'Vinningen', 3019, 'RP', 82, 'DE', 49.15635000, 7.55191000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q526491'),
(29860, 'Visbek', 3008, 'NI', 82, 'DE', 52.83333000, 8.31667000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q599512'),
(29861, 'Visselhövede', 3008, 'NI', 82, 'DE', 52.98546000, 9.58265000, '2019-10-05 22:45:00', '2020-05-01 17:22:48', 1, 'Q499867'),
(29862, 'Viöl', 3005, 'SH', 82, 'DE', 54.56667000, 9.18333000, '2019-10-05 22:45:00', '2020-05-01 17:22:50', 1, 'Q573073'),
(29863, 'Vlotho', 3017, 'NW', 82, 'DE', 52.16530000, 8.85996000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q53907'),
(29864, 'Vockerode', 3011, 'ST', 82, 'DE', 51.84698000, 12.35208000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q631408'),
(29865, 'Voerde', 3017, 'NW', 82, 'DE', 51.59703000, 6.68630000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q33512381'),
(29866, 'Vogt', 3006, 'BW', 82, 'DE', 47.76667000, 9.76667000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q33512381'),
(29867, 'Vogtareuth', 3009, 'BY', 82, 'DE', 47.94694000, 12.18126000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q551663'),
(29868, 'Vogtsburg', 3006, 'BW', 82, 'DE', 48.09688000, 7.64185000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q505478'),
(29869, 'Vohburg an der Donau', 3009, 'BY', 82, 'DE', 48.76977000, 11.61845000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q505478'),
(29870, 'Vohenstrauß', 3009, 'BY', 82, 'DE', 49.62383000, 12.33808000, '2019-10-05 22:45:00', '2020-05-01 17:22:48', 1, 'Q503140'),
(29871, 'Voigtstedt', 3015, 'TH', 82, 'DE', 51.39169000, 11.30888000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q628626'),
(29872, 'Volkach', 3009, 'BY', 82, 'DE', 49.86352000, 10.22813000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q504787'),
(29873, 'Volkenschwand', 3009, 'BY', 82, 'DE', 48.60000000, 11.88333000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q510530'),
(29874, 'Volkertshausen', 3006, 'BW', 82, 'DE', 47.81667000, 8.86667000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q62003'),
(29875, 'Volkmarsen', 3018, 'HE', 82, 'DE', 51.40895000, 9.11814000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q533535'),
(29876, 'Volkstedt', 3011, 'ST', 82, 'DE', 51.56267000, 11.55658000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q2532245'),
(29877, 'Vollersode', 3008, 'NI', 82, 'DE', 53.33333000, 8.91667000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q636955'),
(29878, 'Voltlage', 3008, 'NI', 82, 'DE', 52.43333000, 7.75000000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q503964'),
(29879, 'Volxheim', 3019, 'RP', 82, 'DE', 49.81667000, 7.93333000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q305142'),
(29880, 'Vorbach', 3009, 'BY', 82, 'DE', 49.82136000, 11.73625000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q547278'),
(29881, 'Vordorf', 3008, 'NI', 82, 'DE', 52.36522000, 10.52035000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q369145'),
(29882, 'Vorra', 3009, 'BY', 82, 'DE', 49.55718000, 11.49419000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q554134'),
(29883, 'Vorwerk', 3008, 'NI', 82, 'DE', 53.18333000, 9.15000000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q503271'),
(29884, 'Vreden', 3017, 'NW', 82, 'DE', 52.03792000, 6.82800000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q33512576'),
(29885, 'Vrees', 3008, 'NI', 82, 'DE', 52.88333000, 7.76667000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q659192'),
(29886, 'Vögelsen', 3008, 'NI', 82, 'DE', 53.27447000, 10.35372000, '2019-10-05 22:45:00', '2020-05-01 17:22:48', 1, 'Q640449'),
(29887, 'Vöhl', 3018, 'HE', 82, 'DE', 51.20565000, 8.94510000, '2019-10-05 22:45:00', '2020-05-01 17:22:48', 1, 'Q624862'),
(29888, 'Vöhrenbach', 3006, 'BW', 82, 'DE', 48.05000000, 8.30000000, '2019-10-05 22:45:00', '2020-05-01 17:22:47', 1, 'Q83117'),
(29889, 'Vöhringen', 3006, 'BW', 82, 'DE', 48.33446000, 8.66392000, '2019-10-05 22:45:00', '2020-05-01 17:22:47', 1, 'Q83117'),
(29890, 'Vöhringen', 3009, 'BY', 82, 'DE', 48.27839000, 10.08236000, '2019-10-05 22:45:00', '2020-05-01 17:22:48', 1, 'Q83117'),
(29891, 'Völkershausen', 3015, 'TH', 82, 'DE', 50.80000000, 10.05000000, '2019-10-05 22:45:00', '2020-05-01 17:22:50', 1, 'Q83117'),
(29892, 'Völklingen', 3020, 'SL', 82, 'DE', 49.25162000, 6.85873000, '2019-10-05 22:45:00', '2020-05-01 17:22:49', 1, 'Q14876'),
(29893, 'Völpke', 3011, 'ST', 82, 'DE', 52.13865000, 11.09877000, '2019-10-05 22:45:00', '2020-05-01 17:22:49', 1, 'Q564319'),
(29894, 'Vörstetten', 3006, 'BW', 82, 'DE', 48.06667000, 7.85000000, '2019-10-05 22:45:00', '2020-05-01 17:22:47', 1, 'Q527733'),
(29895, 'Waabs', 3005, 'SH', 82, 'DE', 54.53333000, 9.98333000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q180480'),
(29896, 'Waake', 3008, 'NI', 82, 'DE', 51.55637000, 10.05713000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q542392'),
(29897, 'Waakirchen', 3009, 'BY', 82, 'DE', 47.77250000, 11.67315000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q517431'),
(29898, 'Waal', 3009, 'BY', 82, 'DE', 47.99679000, 10.77786000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q566569'),
(29899, 'Wabern', 3018, 'HE', 82, 'DE', 51.10000000, 9.35000000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q566569'),
(29900, 'Wachau', 3021, 'SN', 82, 'DE', 51.16172000, 13.90651000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q566569'),
(29901, 'Wachenheim', 3019, 'RP', 82, 'DE', 49.44045000, 8.18041000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q50897'),
(29902, 'Wachenroth', 3009, 'BY', 82, 'DE', 49.75185000, 10.71335000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q506293'),
(29903, 'Wachtberg', 3017, 'NW', 82, 'DE', 50.63333000, 7.10000000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q253498'),
(29904, 'Wachtendonk', 3017, 'NW', 82, 'DE', 51.40916000, 6.33894000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q243139'),
(29905, 'Wacken', 3005, 'SH', 82, 'DE', 54.02078000, 9.37597000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q243139'),
(29906, 'Wackernheim', 3019, 'RP', 82, 'DE', 49.97444000, 8.11667000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q654323'),
(29907, 'Wackerow', 3007, 'MV', 82, 'DE', 53.67683000, 12.98164000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q654323'),
(29908, 'Wackersberg', 3009, 'BY', 82, 'DE', 47.73333000, 11.55000000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q550697'),
(29909, 'Wackersdorf', 3009, 'BY', 82, 'DE', 49.31667000, 12.18333000, '2019-10-05 22:45:00', '2019-10-05 22:45:00', 1, 'Q583351'),
(29910, 'Wadern', 3020, 'SL', 82, 'DE', 49.54122000, 6.88774000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q529164'),
(29911, 'Wadersloh', 3017, 'NW', 82, 'DE', 51.73333000, 8.25000000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q49292460'),
(29912, 'Wadgassen', 3020, 'SL', 82, 'DE', 49.26667000, 6.78333000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q253419'),
(29913, 'Waffenbrunn', 3009, 'BY', 82, 'DE', 49.26667000, 12.66667000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q525992'),
(29914, 'Wagenfeld', 3008, 'NI', 82, 'DE', 52.55000000, 8.58333000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q504059'),
(29915, 'Wagenhoff', 3008, 'NI', 82, 'DE', 52.55233000, 10.52337000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q629959'),
(29916, 'Waghäusel', 3006, 'BW', 82, 'DE', 49.24991000, 8.51257000, '2019-10-05 22:45:01', '2020-05-01 17:22:47', 1, 'Q504457'),
(29917, 'Waging am See', 3009, 'BY', 82, 'DE', 47.93414000, 12.73392000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q259811'),
(29918, 'Wahlitz', 3011, 'ST', 82, 'DE', 52.10376000, 11.77947000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q1504270'),
(29919, 'Wahlstedt', 3005, 'SH', 82, 'DE', 53.95161000, 10.20626000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q536506'),
(29920, 'Wahn-Heide', 3017, 'NW', 82, 'DE', 50.85891000, 7.10662000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q1795774'),
(29921, 'Wahrenholz', 3008, 'NI', 82, 'DE', 52.61667000, 10.60000000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q633518'),
(29922, 'Waiblingen', 3006, 'BW', 82, 'DE', 48.83241000, 9.31641000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q4140'),
(29923, 'Waibstadt', 3006, 'BW', 82, 'DE', 49.29505000, 8.91771000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q82141'),
(29924, 'Waidhaus', 3009, 'BY', 82, 'DE', 49.64218000, 12.49523000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q515763'),
(29925, 'Waidhofen', 3009, 'BY', 82, 'DE', 48.57681000, 11.33537000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q515763'),
(29926, 'Waidmannslust', 3010, 'BE', 82, 'DE', 52.60691000, 13.31968000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q648245'),
(29927, 'Waigolshausen', 3009, 'BY', 82, 'DE', 49.96430000, 10.12001000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q180754'),
(29928, 'Wain', 3006, 'BW', 82, 'DE', 48.18904000, 10.02090000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q180754'),
(29929, 'Waischenfeld', 3009, 'BY', 82, 'DE', 49.84645000, 11.34810000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q503128'),
(29930, 'Walchum', 3008, 'NI', 82, 'DE', 52.92717000, 7.28325000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q592746'),
(29931, 'Wald', 3009, 'BY', 82, 'DE', 49.15000000, 12.35000000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q547243'),
(29932, 'Wald', 3006, 'BW', 82, 'DE', 47.93333000, 9.16667000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q547243'),
(29933, 'Wald-Michelbach', 3018, 'HE', 82, 'DE', 49.57000000, 8.83167000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q2541156'),
(29934, 'Waldalgesheim', 3019, 'RP', 82, 'DE', 49.95000000, 7.83333000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q630756'),
(29935, 'Waldaschaff', 3009, 'BY', 82, 'DE', 49.97528000, 9.30194000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q532891'),
(29936, 'Waldbreitbach', 3019, 'RP', 82, 'DE', 50.55000000, 7.41667000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q566027'),
(29937, 'Waldbrunn', 3009, 'BY', 82, 'DE', 49.75861000, 9.80361000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q548995'),
(29938, 'Waldbrunn', 3018, 'HE', 82, 'DE', 50.51875000, 8.10812000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q568927'),
(29939, 'Waldbröl', 3017, 'NW', 82, 'DE', 50.87576000, 7.61688000, '2019-10-05 22:45:01', '2020-05-01 17:22:49', 1, 'Q11040'),
(29940, 'Waldburg', 3006, 'BW', 82, 'DE', 47.75710000, 9.71342000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q11040'),
(29941, 'Waldböckelheim', 3019, 'RP', 82, 'DE', 49.81667000, 7.71667000, '2019-10-05 22:45:01', '2020-05-01 17:22:49', 1, 'Q554029'),
(29942, 'Waldbüttelbrunn', 3009, 'BY', 82, 'DE', 49.78833000, 9.84667000, '2019-10-05 22:45:01', '2020-05-01 17:22:48', 1, 'Q536490'),
(29943, 'Waldeck', 3018, 'HE', 82, 'DE', 51.20618000, 9.06286000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q536490'),
(29944, 'Waldems', 3018, 'HE', 82, 'DE', 50.25000000, 8.33333000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q622297'),
(29945, 'Waldenbuch', 3006, 'BW', 82, 'DE', 48.63830000, 9.13256000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q515637'),
(29946, 'Waldenburg', 3021, 'SN', 82, 'DE', 50.87653000, 12.59919000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q515637'),
(29947, 'Waldenburg', 3006, 'BW', 82, 'DE', 49.18468000, 9.63994000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q515637'),
(29948, 'Walderbach', 3009, 'BY', 82, 'DE', 49.18333000, 12.38333000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q526007'),
(29949, 'Waldershof', 3009, 'BY', 82, 'DE', 49.98144000, 12.06291000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q505972'),
(29950, 'Waldfeucht', 3017, 'NW', 82, 'DE', 51.06610000, 5.98815000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q204683'),
(29951, 'Waldfischbach-Burgalben', 3019, 'RP', 82, 'DE', 49.28333000, 7.66667000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q22471'),
(29952, 'Waldheim', 3021, 'SN', 82, 'DE', 51.07282000, 13.02004000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q22471'),
(29953, 'Waldkappel', 3018, 'HE', 82, 'DE', 51.14462000, 9.87695000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q550835'),
(29954, 'Waldkirch', 3006, 'BW', 82, 'DE', 48.09585000, 7.96371000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q550835'),
(29955, 'Waldkirchen', 3021, 'SN', 82, 'DE', 50.59667000, 12.37994000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q550835'),
(29956, 'Waldkirchen', 3009, 'BY', 82, 'DE', 48.73270000, 13.60082000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q33513123'),
(29957, 'Waldkraiburg', 3009, 'BY', 82, 'DE', 48.20854000, 12.39893000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q282458'),
(29958, 'Waldmohr', 3019, 'RP', 82, 'DE', 49.38333000, 7.33333000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q552671'),
(29959, 'Waldmünchen', 3009, 'BY', 82, 'DE', 49.37804000, 12.70905000, '2019-10-05 22:45:01', '2020-05-01 17:22:48', 1, 'Q503213'),
(29960, 'Waldrach', 3019, 'RP', 82, 'DE', 49.74623000, 6.74543000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q569807'),
(29961, 'Waldsassen', 3009, 'BY', 82, 'DE', 50.00167000, 12.30434000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q241889'),
(29962, 'Waldsee', 3019, 'RP', 82, 'DE', 49.39528000, 8.44028000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q241889'),
(29963, 'Waldshut-Tiengen', 3006, 'BW', 82, 'DE', 47.62323000, 8.21717000, '2019-10-05 22:45:01', '2019-10-05 22:45:01', 1, 'Q505433'),
(29964, 'Waldsieversdorf', 3013, 'BB', 82, 'DE', 52.54221000, 14.07022000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q585255'),
(29965, 'Waldstetten', 3006, 'BW', 82, 'DE', 48.76615000, 9.82135000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q585255'),
(29966, 'Waldstetten', 3009, 'BY', 82, 'DE', 48.34815000, 10.29385000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q528238'),
(29967, 'Waldthurn', 3009, 'BY', 82, 'DE', 49.67221000, 12.32919000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q289283'),
(29968, 'Walheim', 3006, 'BW', 82, 'DE', 49.01417000, 9.15111000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q61961'),
(29969, 'Walkenried', 3008, 'NI', 82, 'DE', 51.58333000, 10.61667000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q656314'),
(29970, 'Walkertshofen', 3009, 'BY', 82, 'DE', 48.22613000, 10.58836000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q181443'),
(29971, 'Walldorf', 3015, 'TH', 82, 'DE', 50.61667000, 10.38333000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q181443'),
(29972, 'Walldorf', 3006, 'BW', 82, 'DE', 49.30637000, 8.64236000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q181443'),
(29973, 'Walldürn', 3006, 'BW', 82, 'DE', 49.58358000, 9.36642000, '2019-10-05 22:45:02', '2020-05-01 17:22:47', 1, 'Q503285'),
(29974, 'Wallendorf', 3011, 'ST', 82, 'DE', 51.35983000, 12.07537000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q695535'),
(29975, 'Wallenfels', 3009, 'BY', 82, 'DE', 50.26850000, 11.47058000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q502574'),
(29976, 'Wallenhorst', 3008, 'NI', 82, 'DE', 52.35000000, 8.01667000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q628324'),
(29977, 'Wallerfangen', 3020, 'SL', 82, 'DE', 49.32749000, 6.71102000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q665005'),
(29978, 'Wallerfing', 3009, 'BY', 82, 'DE', 48.68416000, 12.88035000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q629556'),
(29979, 'Wallersdorf', 3009, 'BY', 82, 'DE', 48.73767000, 12.74744000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q33513356'),
(29980, 'Wallerstein', 3009, 'BY', 82, 'DE', 48.88741000, 10.47591000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q503441'),
(29981, 'Wallertheim', 3019, 'RP', 82, 'DE', 49.83556000, 8.05139000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q659920'),
(29982, 'Wallgau', 3009, 'BY', 82, 'DE', 47.51667000, 11.28333000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q49292576'),
(29983, 'Wallhausen', 3011, 'ST', 82, 'DE', 51.46117000, 11.20760000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q49292576'),
(29984, 'Wallhausen', 3019, 'RP', 82, 'DE', 49.88333000, 7.76667000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q49292576'),
(29985, 'Wallhausen', 3006, 'BW', 82, 'DE', 49.21042000, 10.06219000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q49292576'),
(29986, 'Wallmenroth', 3019, 'RP', 82, 'DE', 50.80000000, 7.83333000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q567190'),
(29987, 'Wallmerod', 3019, 'RP', 82, 'DE', 50.48333000, 7.95000000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q647711'),
(29988, 'Walluf', 3018, 'HE', 82, 'DE', 50.04003000, 8.15545000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q634161'),
(29989, 'Wallwitz', 3011, 'ST', 82, 'DE', 51.58029000, 11.92888000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q634161'),
(29990, 'Walpertskirchen', 3009, 'BY', 82, 'DE', 48.25840000, 11.97527000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q528284'),
(29991, 'Walschleben', 3015, 'TH', 82, 'DE', 51.06667000, 10.93333000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q552268'),
(29992, 'Walsdorf', 3009, 'BY', 82, 'DE', 49.86667000, 10.78333000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q552268'),
(29993, 'Walsrode', 3008, 'NI', 82, 'DE', 52.86147000, 9.59260000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q520369'),
(29994, 'Waltenhofen', 3009, 'BY', 82, 'DE', 47.67319000, 10.30703000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q534810'),
(29995, 'Waltershausen', 3015, 'TH', 82, 'DE', 50.89827000, 10.55791000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q506743'),
(29996, 'Waltrop', 3017, 'NW', 82, 'DE', 51.62125000, 7.40238000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q506743'),
(29997, 'Wandersleben', 3015, 'TH', 82, 'DE', 50.89946000, 10.84959000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q705011'),
(29998, 'Wanderup', 3005, 'SH', 82, 'DE', 54.68333000, 9.33333000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q705011'),
(29999, 'Wandlitz', 3013, 'BB', 82, 'DE', 52.74196000, 13.45799000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q525555'),
(30000, 'Wandsbek', 3016, 'HH', 82, 'DE', 53.58334000, 10.08305000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q1833'),
(30001, 'Wanfried', 3018, 'HE', 82, 'DE', 51.18207000, 10.17283000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q1833'),
(30002, 'Wang', 3009, 'BY', 82, 'DE', 48.49549000, 11.93641000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q1833'),
(30003, 'Wangels', 3005, 'SH', 82, 'DE', 54.26667000, 10.76667000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q526784'),
(30004, 'Wangen', 3006, 'BW', 82, 'DE', 47.68950000, 9.83247000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q165015'),
(30005, 'Wangerooge', 3008, 'NI', 82, 'DE', 53.79002000, 7.89938000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q25135'),
(30006, 'Wankendorf', 3005, 'SH', 82, 'DE', 54.11224000, 10.20546000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q378474'),
(30007, 'Wanna', 3008, 'NI', 82, 'DE', 53.75000000, 8.80000000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q378474'),
(30008, 'Wannsee', 3010, 'BE', 82, 'DE', 52.41915000, 13.15531000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q1949'),
(30009, 'Wannweil', 3006, 'BW', 82, 'DE', 48.51667000, 9.15000000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q82905'),
(30010, 'Wansleben', 3011, 'ST', 82, 'DE', 51.46034000, 11.75135000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q688238'),
(30011, 'Wanzleben', 3011, 'ST', 82, 'DE', 52.06087000, 11.44080000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q706249'),
(30012, 'Warburg', 3017, 'NW', 82, 'DE', 51.49011000, 9.14641000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q706249'),
(30013, 'Wardenburg', 3008, 'NI', 82, 'DE', 53.06667000, 8.20000000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q627741'),
(30014, 'Wardow', 3007, 'MV', 82, 'DE', 53.93054000, 12.40818000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q699192'),
(30015, 'Waren', 3007, 'MV', 82, 'DE', 53.52040000, 12.67985000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q4346'),
(30016, 'Warendorf', 3017, 'NW', 82, 'DE', 51.95109000, 7.98756000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q2769'),
(30017, 'Warin', 3007, 'MV', 82, 'DE', 53.80026000, 11.70504000, '2019-10-05 22:45:02', '2019-10-05 22:45:02', 1, 'Q536697'),
(30018, 'Warmensteinach', 3009, 'BY', 82, 'DE', 49.99348000, 11.77866000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q504438'),
(30019, 'Warmsen', 3008, 'NI', 82, 'DE', 52.45695000, 8.84949000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q561156'),
(30020, 'Warnemünde', 3007, 'MV', 82, 'DE', 54.17670000, 12.08402000, '2019-10-05 22:45:03', '2020-05-01 17:22:49', 1, 'Q311849'),
(30021, 'Warngau', 3009, 'BY', 82, 'DE', 47.83217000, 11.72173000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q532771'),
(30022, 'Warnow', 3007, 'MV', 82, 'DE', 53.78580000, 11.88106000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q532771'),
(30023, 'Warstein', 3017, 'NW', 82, 'DE', 51.44488000, 8.34851000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q11119'),
(30024, 'Wartenberg', 3009, 'BY', 82, 'DE', 48.40597000, 11.98865000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q551142'),
(30025, 'Wartenberg', 3010, 'BE', 82, 'DE', 52.57520000, 13.51559000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q1760551'),
(30026, 'Warthausen', 3006, 'BW', 82, 'DE', 48.12863000, 9.79749000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q528017'),
(30027, 'Wartmannsroth', 3009, 'BY', 82, 'DE', 50.16667000, 9.78333000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q510064'),
(30028, 'Wasbek', 3005, 'SH', 82, 'DE', 54.07427000, 9.89738000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q510064'),
(30029, 'Wasbüttel', 3008, 'NI', 82, 'DE', 52.41331000, 10.59357000, '2019-10-05 22:45:03', '2020-05-01 17:22:48', 1, 'Q632649'),
(30030, 'Wassenach', 3019, 'RP', 82, 'DE', 50.43333000, 7.28333000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q554295'),
(30031, 'Wassenberg', 3017, 'NW', 82, 'DE', 51.10009000, 6.15484000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q204919'),
(30032, 'Wasserburg', 3009, 'BY', 82, 'DE', 48.44105000, 10.26930000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q204919'),
(30033, 'Wasserburg am Inn', 3009, 'BY', 82, 'DE', 48.05250000, 12.22341000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q259617'),
(30034, 'Wasserleben', 3011, 'ST', 82, 'DE', 51.92089000, 10.75670000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q695620'),
(30035, 'Wasserliesch', 3019, 'RP', 82, 'DE', 49.70739000, 6.53944000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q553357'),
(30036, 'Wasserlosen', 3009, 'BY', 82, 'DE', 50.09476000, 10.03017000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q583371'),
(30037, 'Wassertrüdingen', 3009, 'BY', 82, 'DE', 49.04328000, 10.59906000, '2019-10-05 22:45:03', '2020-05-01 17:22:48', 1, 'Q502762'),
(30038, 'Wasungen', 3015, 'TH', 82, 'DE', 50.66190000, 10.36947000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q546530'),
(30039, 'Wathlingen', 3008, 'NI', 82, 'DE', 52.53690000, 10.15069000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q566296'),
(30040, 'Wattenbek', 3005, 'SH', 82, 'DE', 54.16667000, 10.05000000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q557131'),
(30041, 'Wattenheim', 3019, 'RP', 82, 'DE', 49.52167000, 8.06167000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q654250'),
(30042, 'Wattmannshagen', 3007, 'MV', 82, 'DE', 53.77590000, 12.40701000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q654250'),
(30043, 'Waxweiler', 3019, 'RP', 82, 'DE', 50.09257000, 6.36299000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q567248'),
(30044, 'Wechingen', 3009, 'BY', 82, 'DE', 48.89229000, 10.61331000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q510762'),
(30045, 'Wechselburg', 3021, 'SN', 82, 'DE', 51.00214000, 12.77661000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q71060'),
(30046, 'Weddelbrook', 3005, 'SH', 82, 'DE', 53.90000000, 9.83333000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q71060'),
(30047, 'Weddersleben', 3011, 'ST', 82, 'DE', 51.76533000, 11.08531000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q703853'),
(30048, 'Wedding', 3010, 'BE', 82, 'DE', 52.54734000, 13.35594000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q675187'),
(30049, 'Weddingstedt', 3005, 'SH', 82, 'DE', 54.23428000, 9.09103000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q222600'),
(30050, 'Wedel', 3005, 'SH', 82, 'DE', 53.58374000, 9.69835000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q4356'),
(30051, 'Weener', 3008, 'NI', 82, 'DE', 53.16332000, 7.35052000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q504533'),
(30052, 'Wees', 3005, 'SH', 82, 'DE', 54.80621000, 9.51695000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q504533'),
(30053, 'Weeze', 3017, 'NW', 82, 'DE', 51.62678000, 6.19792000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q243079'),
(30054, 'Wefensleben', 3011, 'ST', 82, 'DE', 52.18319000, 11.16074000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q565905'),
(30055, 'Weferlingen', 3011, 'ST', 82, 'DE', 52.31205000, 11.05791000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q2554197'),
(30056, 'Wegberg', 3017, 'NW', 82, 'DE', 51.14221000, 6.28436000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q204860'),
(30057, 'Wegeleben', 3011, 'ST', 82, 'DE', 51.88376000, 11.17347000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q50854'),
(30058, 'Wegscheid', 3009, 'BY', 82, 'DE', 48.60136000, 13.78733000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q530378'),
(30059, 'Wehingen', 3006, 'BW', 82, 'DE', 48.14533000, 8.79151000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q148343'),
(30060, 'Wehr', 3019, 'RP', 82, 'DE', 50.41667000, 7.21667000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q148343'),
(30061, 'Wehr', 3006, 'BW', 82, 'DE', 47.62983000, 7.90423000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q148343'),
(30062, 'Wehrheim', 3018, 'HE', 82, 'DE', 50.30000000, 8.56667000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q628442'),
(30063, 'Wehringen', 3009, 'BY', 82, 'DE', 48.25000000, 10.80000000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q385664'),
(30064, 'Weibern', 3019, 'RP', 82, 'DE', 50.40808000, 7.14669000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q385664'),
(30065, 'Weibersbrunn', 3009, 'BY', 82, 'DE', 49.93083000, 9.36611000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q525766'),
(30066, 'Weichering', 3009, 'BY', 82, 'DE', 48.71713000, 11.32141000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q73212'),
(30067, 'Weichs', 3009, 'BY', 82, 'DE', 48.38333000, 11.41667000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q551190'),
(30068, 'Weida', 3015, 'TH', 82, 'DE', 50.77449000, 12.06028000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q519751'),
(30069, 'Weiden', 3009, 'BY', 82, 'DE', 49.67682000, 12.15613000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q14803'),
(30070, 'Weidenbach', 3009, 'BY', 82, 'DE', 49.19817000, 10.64489000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q528727'),
(30071, 'Weidenstetten', 3006, 'BW', 82, 'DE', 48.55250000, 9.99610000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q515176'),
(30072, 'Weidenthal', 3019, 'RP', 82, 'DE', 49.41667000, 8.00000000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q566035'),
(30073, 'Weidhausen bei Coburg', 3009, 'BY', 82, 'DE', 50.20354000, 11.14006000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q572674'),
(30074, 'Weiding', 3009, 'BY', 82, 'DE', 49.26282000, 12.76311000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q572674'),
(30075, 'Weigendorf', 3009, 'BY', 82, 'DE', 49.49773000, 11.56869000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q536874'),
(30076, 'Weihenzell', 3009, 'BY', 82, 'DE', 49.35000000, 10.61667000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q510011'),
(30077, 'Weiherhammer', 3009, 'BY', 82, 'DE', 49.63333000, 12.06667000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q510047'),
(30078, 'Weihmichl', 3009, 'BY', 82, 'DE', 48.60000000, 12.05000000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q510047'),
(30079, 'Weikersheim', 3006, 'BW', 82, 'DE', 49.47866000, 9.89977000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q61835'),
(30080, 'Weil', 3009, 'BY', 82, 'DE', 48.11717000, 10.92161000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q61835'),
(30081, 'Weil am Rhein', 3006, 'BW', 82, 'DE', 47.59331000, 7.62082000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q61909'),
(30082, 'Weil der Stadt', 3006, 'BW', 82, 'DE', 48.74953000, 8.87176000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q505147'),
(30083, 'Weil im Schönbuch', 3006, 'BW', 82, 'DE', 48.62270000, 9.06355000, '2019-10-05 22:45:03', '2020-05-01 17:22:47', 1, 'Q527674'),
(30084, 'Weilbach', 3009, 'BY', 82, 'DE', 49.66812000, 9.21639000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q521174'),
(30085, 'Weilburg', 3018, 'HE', 82, 'DE', 50.48438000, 8.26249000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q573599'),
(30086, 'Weiler', 3019, 'RP', 82, 'DE', 49.95696000, 7.86484000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q573599'),
(30087, 'Weiler-Simmerberg', 3009, 'BY', 82, 'DE', 47.58261000, 9.91352000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q537453'),
(30088, 'Weilerbach', 3019, 'RP', 82, 'DE', 49.48333000, 7.63333000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q553992'),
(30089, 'Weilersbach', 3009, 'BY', 82, 'DE', 49.75000000, 11.11667000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q553992'),
(30090, 'Weilerswist', 3017, 'NW', 82, 'DE', 50.75292000, 6.84585000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q240039'),
(30091, 'Weilheim', 3009, 'BY', 82, 'DE', 47.84147000, 11.15484000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q535674'),
(30092, 'Weilheim', 3006, 'BW', 82, 'DE', 47.66667000, 8.23333000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q535674'),
(30093, 'Weilheim an der Teck', 3006, 'BW', 82, 'DE', 48.61572000, 9.53751000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q80678'),
(30094, 'Weilmünster', 3018, 'HE', 82, 'DE', 50.43143000, 8.37673000, '2019-10-05 22:45:03', '2020-05-01 17:22:48', 1, 'Q623420'),
(30095, 'Weiltingen', 3009, 'BY', 82, 'DE', 49.03878000, 10.45052000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q521488'),
(30096, 'Weimar', 3015, 'TH', 82, 'DE', 50.98030000, 11.32903000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q3955'),
(30097, 'Weinbach', 3018, 'HE', 82, 'DE', 50.43843000, 8.29133000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q387346'),
(30098, 'Weinböhla', 3021, 'SN', 82, 'DE', 51.16667000, 13.56667000, '2019-10-05 22:45:03', '2020-05-01 17:22:49', 1, 'Q8831'),
(30099, 'Weingarten', 3019, 'RP', 82, 'DE', 49.25946000, 8.28620000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q8831'),
(30100, 'Weingarten', 3006, 'BW', 82, 'DE', 49.05457000, 8.52678000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q33514395'),
(30101, 'Weinheim', 3006, 'BW', 82, 'DE', 49.54887000, 8.66697000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q7050'),
(30102, 'Weinsberg', 3006, 'BW', 82, 'DE', 49.15127000, 9.28762000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q1584'),
(30103, 'Weinsheim', 3019, 'RP', 82, 'DE', 49.83333000, 7.76667000, '2019-10-05 22:45:03', '2019-10-05 22:45:03', 1, 'Q1584'),
(30104, 'Weinstadt-Endersbach', 3006, 'BW', 82, 'DE', 48.81311000, 9.36387000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q21162566'),
(30105, 'Weischlitz', 3021, 'SN', 82, 'DE', 50.45000000, 12.06667000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q46816'),
(30106, 'Weisel', 3019, 'RP', 82, 'DE', 50.11667000, 7.80000000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q46816'),
(30107, 'Weisen', 3013, 'BB', 82, 'DE', 53.02518000, 11.78710000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q46816'),
(30108, 'Weisenbach', 3006, 'BW', 82, 'DE', 48.72757000, 8.35378000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q80490'),
(30109, 'Weisendorf', 3009, 'BY', 82, 'DE', 49.62283000, 10.82531000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q33514445'),
(30110, 'Weiskirchen', 3020, 'SL', 82, 'DE', 49.55000000, 6.81667000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q570294'),
(30111, 'Weismain', 3009, 'BY', 82, 'DE', 50.08512000, 11.24024000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q502610'),
(30112, 'Weissach', 3006, 'BW', 82, 'DE', 48.84687000, 8.92828000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q520383'),
(30113, 'Weisweil', 3006, 'BW', 82, 'DE', 48.19939000, 7.67713000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q534013'),
(30114, 'Weitefeld', 3019, 'RP', 82, 'DE', 50.72541000, 7.92805000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q561811'),
(30115, 'Weiten', 3020, 'SL', 82, 'DE', 49.53011000, 6.54064000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q561811'),
(30116, 'Weitenhagen', 3007, 'MV', 82, 'DE', 54.05372000, 13.40998000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q561811'),
(30117, 'Weitersburg', 3019, 'RP', 82, 'DE', 50.41667000, 7.60000000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q629075'),
(30118, 'Weiterstadt', 3018, 'HE', 82, 'DE', 49.90390000, 8.58874000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q285140'),
(30119, 'Weitnau', 3009, 'BY', 82, 'DE', 47.64171000, 10.12732000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q285140'),
(30120, 'Weitramsdorf', 3009, 'BY', 82, 'DE', 50.25600000, 10.87989000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q285140'),
(30121, 'Weißandt-Gölzau', 3011, 'ST', 82, 'DE', 51.67070000, 12.07338000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q687903'),
(30122, 'Weißbach', 3006, 'BW', 82, 'DE', 49.29717000, 9.59531000, '2019-10-05 22:45:04', '2020-05-01 17:22:47', 1, 'Q687903'),
(30123, 'Weißdorf', 3009, 'BY', 82, 'DE', 50.18333000, 11.85000000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q503820'),
(30124, 'Weißenberg', 3021, 'SN', 82, 'DE', 51.19644000, 14.65874000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q81753'),
(30125, 'Weißenborn', 3015, 'TH', 82, 'DE', 50.92393000, 11.87947000, '2019-10-05 22:45:04', '2020-05-01 17:22:50', 1, 'Q81753'),
(30126, 'Weißenborn', 3021, 'SN', 82, 'DE', 50.73952000, 12.47051000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q81753'),
(30127, 'Weißenborn-Lüderode', 3015, 'TH', 82, 'DE', 51.53190000, 10.41889000, '2019-10-05 22:45:04', '2020-05-01 17:22:50', 1, 'Q579006'),
(30128, 'Weißenbrunn', 3009, 'BY', 82, 'DE', 50.20000000, 11.35000000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q515775'),
(30129, 'Weißenburg in Bayern', 3009, 'BY', 82, 'DE', 49.03095000, 10.97221000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q44500'),
(30130, 'Weißenfels', 3011, 'ST', 82, 'DE', 51.20148000, 11.96843000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q14815'),
(30131, 'Weißenhorn', 3009, 'BY', 82, 'DE', 48.30501000, 10.16047000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q519677'),
(30132, 'Weißenohe', 3009, 'BY', 82, 'DE', 49.63089000, 11.25369000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q518154'),
(30133, 'Weißensberg', 3009, 'BY', 82, 'DE', 47.58130000, 9.72586000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q73536'),
(30134, 'Weißensee', 3010, 'BE', 82, 'DE', 52.55632000, 13.46649000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q675164'),
(30135, 'Weißensee', 3015, 'TH', 82, 'DE', 51.19989000, 11.06914000, '2019-10-05 22:45:04', '2020-05-01 17:22:50', 1, 'Q675164'),
(30136, 'Weißenstadt', 3009, 'BY', 82, 'DE', 50.10217000, 11.88849000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q515933'),
(30137, 'Weißenthurm', 3019, 'RP', 82, 'DE', 50.41719000, 7.45072000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q541616'),
(30138, 'Weißig', 3021, 'SN', 82, 'DE', 51.29192000, 13.44117000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q541616'),
(30139, 'Weißkeißel', 3021, 'SN', 82, 'DE', 51.49405000, 14.71515000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q506128'),
(30140, 'Weißwasser', 3021, 'SN', 82, 'DE', 51.50403000, 14.64017000, '2019-10-05 22:45:04', '2020-05-01 17:22:49', 1, 'Q44498'),
(30141, 'Welden', 3009, 'BY', 82, 'DE', 48.45505000, 10.66086000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q511540'),
(30142, 'Welle', 3008, 'NI', 82, 'DE', 53.23985000, 9.80178000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q25622035'),
(30143, 'Wellen', 3011, 'ST', 82, 'DE', 52.14518000, 11.44106000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q687916'),
(30144, 'Wellendingen', 3006, 'BW', 82, 'DE', 48.14745000, 8.70375000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q81237'),
(30145, 'Wellingsbüttel', 3016, 'HH', 82, 'DE', 53.64104000, 10.07980000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q1836'),
(30146, 'Welschbillig', 3019, 'RP', 82, 'DE', 49.85000000, 6.56667000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q553409'),
(30147, 'Welsleben', 3011, 'ST', 82, 'DE', 52.00304000, 11.63851000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q702137'),
(30148, 'Welver', 3017, 'NW', 82, 'DE', 51.61667000, 7.96667000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q11145'),
(30149, 'Welzheim', 3006, 'BW', 82, 'DE', 48.87675000, 9.63434000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q82421'),
(30150, 'Welzow', 3013, 'BB', 82, 'DE', 51.58381000, 14.17082000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q572607'),
(30151, 'Wemding', 3009, 'BY', 82, 'DE', 48.87461000, 10.72452000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q502758'),
(30152, 'Wendeburg', 3008, 'NI', 82, 'DE', 52.32957000, 10.39255000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q17461497'),
(30153, 'Wendelsheim', 3019, 'RP', 82, 'DE', 49.76667000, 8.00000000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q555562'),
(30154, 'Wendelstein', 3009, 'BY', 82, 'DE', 49.35230000, 11.15069000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q522236'),
(30155, 'Wenden', 3017, 'NW', 82, 'DE', 50.96667000, 7.86667000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q10912'),
(30156, 'Wendisch Evern', 3008, 'NI', 82, 'DE', 53.21667000, 10.46667000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q654392'),
(30157, 'Wendisch Rietz', 3013, 'BB', 82, 'DE', 52.21613000, 14.00845000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q622866'),
(30158, 'Wendlingen am Neckar', 3006, 'BW', 82, 'DE', 48.67124000, 9.37632000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q81860'),
(30159, 'Wendorf', 3007, 'MV', 82, 'DE', 54.25540000, 13.07682000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q81860'),
(30160, 'Wendtorf', 3005, 'SH', 82, 'DE', 54.41212000, 10.28952000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q638281'),
(30161, 'Weng', 3009, 'BY', 82, 'DE', 48.65866000, 12.36927000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q529250'),
(30162, 'Wennigsen', 3008, 'NI', 82, 'DE', 52.27404000, 9.57287000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, '*********'),
(30163, 'Wentorf bei Hamburg', 3005, 'SH', 82, 'DE', 53.50000000, 10.25000000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q552545'),
(30164, 'Wenzenbach', 3009, 'BY', 82, 'DE', 49.07561000, 12.19954000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q491617'),
(30165, 'Wenzendorf', 3008, 'NI', 82, 'DE', 53.34948000, 9.77234000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q376823'),
(30166, 'Werbach', 3006, 'BW', 82, 'DE', 49.67083000, 9.63944000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q61626'),
(30167, 'Werben', 3013, 'BB', 82, 'DE', 51.81667000, 14.18333000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q574768'),
(30168, 'Werda', 3021, 'SN', 82, 'DE', 50.43811000, 12.30473000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q574768'),
(30169, 'Werdau', 3021, 'SN', 82, 'DE', 50.73604000, 12.37534000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q20077'),
(30170, 'Werder', 3013, 'BB', 82, 'DE', 52.37874000, 12.93400000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q525686'),
(30171, 'Werdervorstadt', 3007, 'MV', 82, 'DE', 53.63909000, 11.42767000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q525686'),
(30172, 'Werdohl', 3017, 'NW', 82, 'DE', 51.26011000, 7.76608000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q5575'),
(30173, 'Werftpfuhl', 3013, 'BB', 82, 'DE', 52.66014000, 13.79351000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q5575'),
(30174, 'Werl', 3017, 'NW', 82, 'DE', 51.55493000, 7.91403000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q11120'),
(30175, 'Werlte', 3008, 'NI', 82, 'DE', 52.85000000, 7.68333000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q673655'),
(30176, 'Wermelskirchen', 3017, 'NW', 82, 'DE', 51.13970000, 7.21583000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q7507'),
(30177, 'Wermsdorf', 3021, 'SN', 82, 'DE', 51.28333000, 12.95000000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q16067'),
(30178, 'Wernau', 3006, 'BW', 82, 'DE', 48.69306000, 9.41533000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q82346'),
(30179, 'Wernberg-Köblitz', 3009, 'BY', 82, 'DE', 49.53931000, 12.16130000, '2019-10-05 22:45:04', '2020-05-01 17:22:48', 1, 'Q553724'),
(30180, 'Werne', 3017, 'NW', 82, 'DE', 51.66446000, 7.63421000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q10936'),
(30181, 'Werneck', 3009, 'BY', 82, 'DE', 49.98201000, 10.09884000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q526691'),
(30182, 'Wernersberg', 3019, 'RP', 82, 'DE', 49.19062000, 7.92756000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q526691'),
(30183, 'Werneuchen', 3013, 'BB', 82, 'DE', 52.63275000, 13.73437000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q502855'),
(30184, 'Wernigerode', 3011, 'ST', 82, 'DE', 51.83652000, 10.78216000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q15982'),
(30185, 'Wernshausen', 3015, 'TH', 82, 'DE', 50.72404000, 10.35086000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q697464'),
(30186, 'Werpeloh', 3008, 'NI', 82, 'DE', 52.87207000, 7.50831000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q639117'),
(30187, 'Wertach', 3009, 'BY', 82, 'DE', 47.60301000, 10.40966000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q549138'),
(30188, 'Wertheim', 3006, 'BW', 82, 'DE', 49.75900000, 9.50852000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q21405656'),
(30189, 'Werther', 3017, 'NW', 82, 'DE', 52.07771000, 8.41793000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q32750'),
(30190, 'Wertingen', 3009, 'BY', 82, 'DE', 48.56314000, 10.68149000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q502930'),
(30191, 'Wesel', 3017, 'NW', 82, 'DE', 51.66690000, 6.62037000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q4011'),
(30192, 'Weselberg', 3019, 'RP', 82, 'DE', 49.33662000, 7.60780000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q565369'),
(30193, 'Wesenberg', 3007, 'MV', 82, 'DE', 53.28030000, 12.96936000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q565369'),
(30194, 'Wesendorf', 3008, 'NI', 82, 'DE', 52.60000000, 10.53333000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q288466'),
(30195, 'Wesselburen', 3005, 'SH', 82, 'DE', 54.21217000, 8.92419000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q518460'),
(30196, 'Wesseling', 3017, 'NW', 82, 'DE', 50.82709000, 6.97470000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q14956'),
(30197, 'Wesseln', 3005, 'SH', 82, 'DE', 54.20985000, 9.07644000, '2019-10-05 22:45:04', '2019-10-05 22:45:04', 1, 'Q14956'),
(30198, 'Wessobrunn', 3009, 'BY', 82, 'DE', 47.87407000, 11.02461000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q14956'),
(30199, 'Weste', 3008, 'NI', 82, 'DE', 53.05943000, 10.70283000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q14956'),
(30200, 'Westend', 3010, 'BE', 82, 'DE', 52.51708000, 13.27636000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q649947'),
(30201, 'Westendorf', 3009, 'BY', 82, 'DE', 47.93333000, 10.71667000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q549052'),
(30202, 'Westensee', 3005, 'SH', 82, 'DE', 54.27452000, 9.89584000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q549052'),
(30203, 'Wester-Ohrstedt', 3005, 'SH', 82, 'DE', 54.50796000, 9.18574000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q572982'),
(30204, 'Westerburg', 3019, 'RP', 82, 'DE', 50.55938000, 7.97482000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q537491'),
(30205, 'Westeregeln', 3011, 'ST', 82, 'DE', 51.96328000, 11.39265000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q687563'),
(30206, 'Westergellersen', 3008, 'NI', 82, 'DE', 53.23333000, 10.25000000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q687563'),
(30207, 'Westerhausen', 3011, 'ST', 82, 'DE', 51.80566000, 11.05631000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q688629'),
(30208, 'Westerheim', 3006, 'BW', 82, 'DE', 48.51511000, 9.62424000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q688629'),
(30209, 'Westerheim', 3009, 'BY', 82, 'DE', 48.01667000, 10.30000000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q688629'),
(30210, 'Westerholt', 3008, 'NI', 82, 'DE', 53.60000000, 7.45000000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q676164'),
(30211, 'Westerhorn', 3005, 'SH', 82, 'DE', 53.85711000, 9.67959000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q676164'),
(30212, 'Westerkappeln', 3017, 'NW', 82, 'DE', 52.31667000, 7.88333000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q49292970'),
(30213, 'Westerland', 3005, 'SH', 82, 'DE', 54.90790000, 8.30326000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q27998'),
(30214, 'Westerrönfeld', 3005, 'SH', 82, 'DE', 54.28333000, 9.65000000, '2019-10-05 22:45:05', '2020-05-01 17:22:50', 1, 'Q632121'),
(30215, 'Westerstede', 3008, 'NI', 82, 'DE', 53.25682000, 7.92737000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q492360'),
(30216, 'Westerstetten', 3006, 'BW', 82, 'DE', 48.51916000, 9.95494000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q515297'),
(30217, 'Westhagen', 3008, 'NI', 82, 'DE', 52.40425000, 10.73939000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q515297'),
(30218, 'Westhausen', 3006, 'BW', 82, 'DE', 48.88333000, 10.18333000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q515297'),
(30219, 'Westheim', 3019, 'RP', 82, 'DE', 49.24640000, 8.32357000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q515297'),
(30220, 'Westheim', 3009, 'BY', 82, 'DE', 49.00000000, 10.66667000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q515297'),
(30221, 'Westhofen', 3019, 'RP', 82, 'DE', 49.70444000, 8.24806000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q502104'),
(30222, 'Weststadt', 3007, 'MV', 82, 'DE', 53.63472000, 11.39565000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q502104'),
(30223, 'Wetschen', 3008, 'NI', 82, 'DE', 52.61295000, 8.44883000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q504742'),
(30224, 'Wetter', 3018, 'HE', 82, 'DE', 50.90254000, 8.72366000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q598058'),
(30225, 'Wetter (Ruhr)', 3017, 'NW', 82, 'DE', 51.38747000, 7.39277000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q11343'),
(30226, 'Wetterzeube', 3011, 'ST', 82, 'DE', 51.00000000, 12.01667000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q564363'),
(30227, 'Wettin', 3011, 'ST', 82, 'DE', 51.58604000, 11.80630000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q694558'),
(30228, 'Wettringen', 3017, 'NW', 82, 'DE', 52.20939000, 7.31895000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q182510'),
(30229, 'Wetzendorf', 3009, 'BY', 82, 'DE', 49.47056000, 11.04148000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q182510'),
(30230, 'Wetzlar', 3018, 'HE', 82, 'DE', 50.56109000, 8.50495000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q4178'),
(30231, 'Wewelsfleth', 3005, 'SH', 82, 'DE', 53.85000000, 9.40000000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q556213'),
(30232, 'Weyarn', 3009, 'BY', 82, 'DE', 47.85838000, 11.79923000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q517534'),
(30233, 'Weyerbusch', 3019, 'RP', 82, 'DE', 50.71667000, 7.55000000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q553834'),
(30234, 'Weyhausen', 3008, 'NI', 82, 'DE', 52.46208000, 10.71699000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q630716'),
(30235, 'Weßling', 3009, 'BY', 82, 'DE', 48.07452000, 11.24820000, '2019-10-05 22:45:05', '2020-05-01 17:22:48', 1, 'Q536419'),
(30236, 'Wickede', 3017, 'NW', 82, 'DE', 51.49640000, 7.86587000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q6136'),
(30237, 'Widdern', 3006, 'BW', 82, 'DE', 49.31825000, 9.42209000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q502408'),
(30238, 'Wieda', 3008, 'NI', 82, 'DE', 51.63400000, 10.58704000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q681125'),
(30239, 'Wiedemar', 3021, 'SN', 82, 'DE', 51.46667000, 12.20000000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q16070'),
(30240, 'Wiedensahl', 3008, 'NI', 82, 'DE', 52.38385000, 9.12019000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q546339'),
(30241, 'Wiedergeltingen', 3009, 'BY', 82, 'DE', 48.03946000, 10.67406000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q553734'),
(30242, 'Wiederstedt', 3011, 'ST', 82, 'DE', 51.66526000, 11.52723000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q819026'),
(30243, 'Wiednitz', 3021, 'SN', 82, 'DE', 51.38608000, 14.02683000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q572589'),
(30244, 'Wiefelstede', 3008, 'NI', 82, 'DE', 53.25000000, 8.11667000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q503388'),
(30245, 'Wiehe', 3015, 'TH', 82, 'DE', 51.26586000, 11.41282000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q518287'),
(30246, 'Wiehl', 3017, 'NW', 82, 'DE', 50.94950000, 7.55062000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q11043'),
(30247, 'Wiek', 3007, 'MV', 82, 'DE', 54.62000000, 13.28914000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q11043'),
(30248, 'Wiemersdorf', 3005, 'SH', 82, 'DE', 53.95815000, 9.90297000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q11043'),
(30249, 'Wienhausen', 3008, 'NI', 82, 'DE', 52.58021000, 10.18862000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q566292'),
(30250, 'Wieren', 3008, 'NI', 82, 'DE', 52.88529000, 10.65871000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q604160'),
(30251, 'Wiernsheim', 3006, 'BW', 82, 'DE', 48.88333000, 8.85000000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q604160'),
(30252, 'Wiesa', 3021, 'SN', 82, 'DE', 50.61114000, 13.01377000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q604160'),
(30253, 'Wiesau', 3009, 'BY', 82, 'DE', 49.90817000, 12.18889000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q548571'),
(30254, 'Wiesbaden', 3018, 'HE', 82, 'DE', 50.08258000, 8.24932000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q1721'),
(30255, 'Wiesen', 3009, 'BY', 82, 'DE', 50.11667000, 9.36667000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q1721'),
(30256, 'Wiesenau', 3013, 'BB', 82, 'DE', 52.23071000, 14.59107000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q622451'),
(30257, 'Wiesenbach', 3006, 'BW', 82, 'DE', 49.36139000, 8.80361000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q622451'),
(30258, 'Wiesenburg', 3013, 'BB', 82, 'DE', 52.11456000, 12.45534000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q623510'),
(30259, 'Wiesenfelden', 3009, 'BY', 82, 'DE', 49.04066000, 12.54008000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q552116'),
(30260, 'Wiesensteig', 3006, 'BW', 82, 'DE', 48.56127000, 9.62540000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q80854'),
(30261, 'Wiesent', 3009, 'BY', 82, 'DE', 49.01667000, 12.38333000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q80854'),
(30262, 'Wiesenthau', 3009, 'BY', 82, 'DE', 49.71326000, 11.13564000, '2019-10-05 22:45:05', '2019-10-05 22:45:05', 1, 'Q503074'),
(30263, 'Wiesentheid', 3009, 'BY', 82, 'DE', 49.79451000, 10.34509000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q166228'),
(30264, 'Wiesloch', 3006, 'BW', 82, 'DE', 49.29504000, 8.69846000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q22874'),
(30265, 'Wiesmoor', 3008, 'NI', 82, 'DE', 53.41667000, 7.73333000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q511989'),
(30266, 'Wiesthal', 3009, 'BY', 82, 'DE', 50.03333000, 9.43333000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q517471'),
(30267, 'Wietmarschen', 3008, 'NI', 82, 'DE', 52.51806000, 7.13408000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q556086'),
(30268, 'Wietze', 3008, 'NI', 82, 'DE', 52.65000000, 9.83333000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q574367'),
(30269, 'Wietzen', 3008, 'NI', 82, 'DE', 52.71667000, 9.08333000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q650986'),
(30270, 'Wietzendorf', 3008, 'NI', 82, 'DE', 52.91667000, 9.98333000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q557883'),
(30271, 'Wiggensbach', 3009, 'BY', 82, 'DE', 47.74781000, 10.22987000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q536377'),
(30272, 'Wilburgstetten', 3009, 'BY', 82, 'DE', 49.02427000, 10.39505000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q507412'),
(30273, 'Wildberg', 3006, 'BW', 82, 'DE', 48.62336000, 8.74518000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q507412'),
(30274, 'Wildemann', 3008, 'NI', 82, 'DE', 51.82810000, 10.28152000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q558584'),
(30275, 'Wildenberg', 3009, 'BY', 82, 'DE', 48.72981000, 11.89845000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q503965'),
(30276, 'Wildenfels', 3021, 'SN', 82, 'DE', 50.66784000, 12.60886000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q20087'),
(30277, 'Wildenhain', 3021, 'SN', 82, 'DE', 51.30760000, 13.47678000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q701114'),
(30278, 'Wildeshausen', 3008, 'NI', 82, 'DE', 52.89446000, 8.43375000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q501626'),
(30279, 'Wildflecken', 3009, 'BY', 82, 'DE', 50.37764000, 9.91092000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q505327'),
(30280, 'Wildpoldsried', 3009, 'BY', 82, 'DE', 47.76667000, 10.40000000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q520426'),
(30281, 'Wildsteig', 3009, 'BY', 82, 'DE', 47.70145000, 10.93753000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q520426'),
(30282, 'Wilgartswiesen', 3019, 'RP', 82, 'DE', 49.20891000, 7.87346000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q565836'),
(30283, 'Wilhelmsdorf', 3006, 'BW', 82, 'DE', 47.86612000, 9.42621000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q565836'),
(30284, 'Wilhelmsdorf', 3009, 'BY', 82, 'DE', 49.56419000, 10.73716000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q565836'),
(30285, 'Wilhelmsfeld', 3006, 'BW', 82, 'DE', 49.47056000, 8.75361000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q22877'),
(30286, 'Wilhelmshaven', 3008, 'NI', 82, 'DE', 53.52998000, 8.11253000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q3857'),
(30287, 'Wilhelmsruh', 3010, 'BE', 82, 'DE', 52.58710000, 13.36855000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q648000'),
(30288, 'Wilhelmstadt', 3010, 'BE', 82, 'DE', 52.52462000, 13.17707000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q563692'),
(30289, 'Wilhelmsthal', 3009, 'BY', 82, 'DE', 50.31113000, 11.37278000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q504933'),
(30290, 'Wilhermsdorf', 3009, 'BY', 82, 'DE', 49.48308000, 10.71555000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q541454'),
(30291, 'Wilkau-Haßlau', 3021, 'SN', 82, 'DE', 50.67504000, 12.51482000, '2019-10-05 22:45:06', '2020-05-01 17:22:49', 1, 'Q20088'),
(30292, 'Willanzheim', 3009, 'BY', 82, 'DE', 49.68011000, 10.23248000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q504003'),
(30293, 'Willebadessen', 3017, 'NW', 82, 'DE', 51.62564000, 9.03694000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q33517347'),
(30294, 'Willich', 3017, 'NW', 82, 'DE', 51.26371000, 6.54734000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q4171'),
(30295, 'Willingen', 3018, 'HE', 82, 'DE', 51.29418000, 8.60910000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q509580'),
(30296, 'Willingshausen', 3018, 'HE', 82, 'DE', 50.85000000, 9.20000000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q624357'),
(30297, 'Willmering', 3009, 'BY', 82, 'DE', 49.25000000, 12.66667000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q510806'),
(30298, 'Willstätt', 3006, 'BW', 82, 'DE', 48.54071000, 7.89314000, '2019-10-05 22:45:06', '2020-05-01 17:22:47', 1, 'Q550656'),
(30299, 'Wilmersdorf', 3010, 'BE', 82, 'DE', 52.48333000, 13.31667000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q693628'),
(30300, 'Wilnsdorf', 3017, 'NW', 82, 'DE', 50.81667000, 8.10000000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q10960'),
(30301, 'Wilsdruff', 3021, 'SN', 82, 'DE', 51.05199000, 13.53657000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q8248'),
(30302, 'Wilstedt', 3008, 'NI', 82, 'DE', 53.19493000, 9.09595000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q502849'),
(30303, 'Wilster', 3005, 'SH', 82, 'DE', 53.92253000, 9.37465000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q506560'),
(30304, 'Wilsum', 3008, 'NI', 82, 'DE', 52.53333000, 6.85000000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q506560'),
(30305, 'Wilthen', 3021, 'SN', 82, 'DE', 51.09745000, 14.39290000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q81756'),
(30306, 'Wiltingen', 3019, 'RP', 82, 'DE', 49.65919000, 6.59255000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q161930'),
(30307, 'Wimmelburg', 3011, 'ST', 82, 'DE', 51.52069000, 11.50696000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q564400'),
(30308, 'Wimsheim', 3006, 'BW', 82, 'DE', 48.85000000, 8.83333000, '2019-10-05 22:45:06', '2019-10-05 22:45:06', 1, 'Q80511'),
(30309, 'Wincheringen', 3019, 'RP', 82, 'DE', 49.60904000, 6.42597000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q543611'),
(30310, 'Windach', 3009, 'BY', 82, 'DE', 48.06667000, 11.03333000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q49293105'),
(30311, 'Windberg', 3009, 'BY', 82, 'DE', 48.94285000, 12.74620000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q548590'),
(30312, 'Windeby', 3005, 'SH', 82, 'DE', 54.46667000, 9.81667000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q552810'),
(30313, 'Windelsbach', 3009, 'BY', 82, 'DE', 49.40000000, 10.30000000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q504080'),
(30314, 'Winden', 3019, 'RP', 82, 'DE', 49.09778000, 8.11694000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q504080'),
(30315, 'Windesheim', 3019, 'RP', 82, 'DE', 49.90000000, 7.81667000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q504080'),
(30316, 'Windhagen', 3019, 'RP', 82, 'DE', 50.64121000, 7.35352000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q566046'),
(30317, 'Windhausen', 3008, 'NI', 82, 'DE', 51.78544000, 10.21218000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q668499'),
(30318, 'Windischeschenbach', 3009, 'BY', 82, 'DE', 49.80108000, 12.15710000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q33517480'),
(30319, 'Windischleuba', 3015, 'TH', 82, 'DE', 51.01556000, 12.46914000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q37607'),
(30320, 'Windsbach', 3009, 'BY', 82, 'DE', 49.24786000, 10.82651000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q502441'),
(30321, 'Wingerode', 3015, 'TH', 82, 'DE', 51.39032000, 10.23954000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q502441'),
(30322, 'Wingst', 3008, 'NI', 82, 'DE', 53.73572000, 9.08054000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q502938'),
(30323, 'Winhöring', 3009, 'BY', 82, 'DE', 48.26667000, 12.65000000, '2019-10-05 22:45:07', '2020-05-01 17:22:48', 1, 'Q265381'),
(30324, 'Winkelhaid', 3009, 'BY', 82, 'DE', 49.38963000, 11.29888000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q524382'),
(30325, 'Winklarn', 3009, 'BY', 82, 'DE', 49.42688000, 12.47986000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q559111'),
(30326, 'Winnenden', 3006, 'BW', 82, 'DE', 48.87563000, 9.39819000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q82741'),
(30327, 'Winningen', 3019, 'RP', 82, 'DE', 50.31667000, 7.51667000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q822358'),
(30328, 'Winnweiler', 3019, 'RP', 82, 'DE', 49.56667000, 7.85000000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q22469'),
(30329, 'Winsen', 3008, 'NI', 82, 'DE', 53.35753000, 10.21282000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q16000'),
(30330, 'Winterbach', 3006, 'BW', 82, 'DE', 48.79918000, 9.47914000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q82825'),
(30331, 'Winterberg', 3017, 'NW', 82, 'DE', 51.19248000, 8.53468000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q10923'),
(30332, 'Winterhausen', 3009, 'BY', 82, 'DE', 49.70683000, 10.01661000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q139025'),
(30333, 'Winterhude', 3016, 'HH', 82, 'DE', 53.60000000, 10.00000000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q1148'),
(30334, 'Winterlingen', 3006, 'BW', 82, 'DE', 48.18333000, 9.11667000, '2019-10-05 22:45:07', '2019-10-05 22:45:07', 1, 'Q539774');

