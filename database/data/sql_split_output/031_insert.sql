INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(15174, 'São Vicente do Sul', 2001, 'RS', 31, 'BR', -29.71725000, -54.76241000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q272254'),
(15175, 'Sério', 2001, 'RS', 31, 'BR', -29.40642000, -52.24358000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q272254'),
(15176, 'Sítio Novo', 2015, 'MA', 31, 'BR', -6.15590000, -46.67216000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q272254'),
(15177, 'S<PERSON><PERSON>o Novo', 2019, '<PERSON><PERSON>', 31, '<PERSON>', -6.12604000, -35.97108000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q272254'),
(15178, '<PERSON><PERSON>ti<PERSON> <PERSON>o do To<PERSON><PERSON>s', 2020, 'TO', 31, '<PERSON>', -5.62990000, -47.68644000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q272254'),
(15179, 'S<PERSON>tio d\'Abadia', 2000, 'GO', 31, '<PERSON>', -14.75201000, -46.27003000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1806986'),
(15180, 'Sítio do Mato', 2002, 'BA', 31, 'BR', -13.00385000, -43.54537000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1806986'),
(15181, 'Sítio do Quinto', 2002, 'BA', 31, 'BR', -10.34321000, -38.07346000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1806986'),
(15182, 'Tabaporã', 2011, 'MT', 31, 'BR', -11.05295000, -56.64189000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1806591'),
(15183, 'Tabapuã', 2021, 'SP', 31, 'BR', -20.92377000, -49.02475000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q301283'),
(15184, 'Tabatinga', 2004, 'AM', 31, 'BR', -3.96298000, -69.60265000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q22060342'),
(15185, 'Tabatinga', 2021, 'SP', 31, 'BR', -21.74210000, -48.64589000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q22064419'),
(15186, 'Tabaí', 2001, 'RS', 31, 'BR', -29.66719000, -51.72796000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q22064419'),
(15187, 'Tabira', 2006, 'PE', 31, 'BR', -7.59075000, -37.49202000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q2078322'),
(15188, 'Tabocas do Brejo Velho', 2002, 'BA', 31, 'BR', -12.36943000, -44.08362000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q2078322'),
(15189, 'Taboleiro Grande', 2019, 'RN', 31, 'BR', -5.92608000, -38.05352000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q2078322'),
(15190, 'Taboão da Serra', 2021, 'SP', 31, 'BR', -23.62611000, -46.79167000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q841231'),
(15191, 'Tabuleiro', 1998, 'MG', 31, 'BR', -21.35779000, -43.24828000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q608059'),
(15192, 'Tabuleiro do Norte', 2016, 'CE', 31, 'BR', -5.29109000, -38.07247000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q608059'),
(15193, 'Tacaimbó', 2006, 'PE', 31, 'BR', -8.32284000, -36.24711000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q608059'),
(15194, 'Tacaratu', 2006, 'PE', 31, 'BR', -8.95680000, -38.07649000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q608059'),
(15195, 'Taciba', 2021, 'SP', 31, 'BR', -22.49676000, -51.33241000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q22064418'),
(15196, 'Tacima', 2005, 'PB', 31, 'BR', -6.53805000, -35.52277000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q22064418'),
(15197, 'Tacuru', 2010, 'MS', 31, 'BR', -23.68722000, -54.91099000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q22064418'),
(15198, 'Taguatinga', 2020, 'TO', 31, 'BR', -12.38568000, -46.57110000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q22060005'),
(15199, 'Taguaí', 2021, 'SP', 31, 'BR', -23.48512000, -49.40781000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1760276'),
(15200, 'Taiaçu', 2021, 'SP', 31, 'BR', -21.13217000, -48.53092000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1760116'),
(15201, 'Tailândia', 2009, 'PA', 31, 'BR', -2.87235000, -48.75748000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1760116'),
(15202, 'Taiobeiras', 1998, 'MG', 31, 'BR', -15.83800000, -42.09039000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q1787178'),
(15203, 'Taipas do Tocantins', 2020, 'TO', 31, 'BR', -12.15677000, -47.03342000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q1787178'),
(15204, 'Taipu', 2019, 'RN', 31, 'BR', -5.51936000, -35.58130000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q1802310'),
(15205, 'Taió', 2014, 'SC', 31, 'BR', -27.08902000, -50.09933000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1802310'),
(15206, 'Taiúva', 2021, 'SP', 31, 'BR', -21.12737000, -48.42696000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1760091'),
(15207, 'Talismã', 2020, 'TO', 31, 'BR', -12.67795000, -49.07863000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1760091'),
(15208, 'Tamandaré', 2006, 'PE', 31, 'BR', -8.74874000, -35.13941000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q2011531'),
(15209, 'Tamarana', 2022, 'PR', 31, 'BR', -23.82556000, -51.04683000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q2011531'),
(15210, 'Tambaú', 2021, 'SP', 31, 'BR', -21.58688000, -47.22761000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q650146'),
(15211, 'Tamboara', 2022, 'PR', 31, 'BR', -23.19886000, -52.46715000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q650146'),
(15212, 'Tamboril', 2016, 'CE', 31, 'BR', -4.83222000, -40.32056000, '2019-10-05 22:35:35', '2019-10-05 22:35:35', 1, 'Q2068962'),
(15213, 'Tamboril do Piauí', 2008, 'PI', 31, 'BR', -8.40278000, -43.09084000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q2068962'),
(15214, 'Tanabi', 2021, 'SP', 31, 'BR', -20.50212000, -49.64584000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q923862'),
(15215, 'Tangará', 2019, 'RN', 31, 'BR', -6.23847000, -35.79625000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q2011631'),
(15216, 'Tangará', 2014, 'SC', 31, 'BR', -27.12587000, -51.09938000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q22063771'),
(15217, 'Tangará da Serra', 2011, 'MT', 31, 'BR', -14.52323000, -58.09862000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q1806373'),
(15218, 'Tanguá', 1997, 'RJ', 31, 'BR', -22.79184000, -42.71941000, '2019-10-05 22:35:36', '2020-05-01 17:22:37', 1, 'Q1787049'),
(15219, 'Tanhaçu', 2002, 'BA', 31, 'BR', -14.07010000, -41.13506000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q22062867'),
(15220, 'Tanque Novo', 2002, 'BA', 31, 'BR', -13.58365000, -42.54311000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q22062867'),
(15221, 'Tanque d\'Arca', 2007, 'AL', 31, 'BR', -9.56069000, -36.40824000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1772722'),
(15222, 'Tanque do Piauí', 2008, 'PI', 31, 'BR', -6.69614000, -42.18310000, '2019-10-05 22:35:36', '2020-05-01 17:22:37', 1, 'Q1772722'),
(15223, 'Tanquinho', 2002, 'BA', 31, 'BR', -11.95674000, -39.09608000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1772722'),
(15224, 'Taparuba', 1998, 'MG', 31, 'BR', -19.72752000, -41.60277000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1787155'),
(15225, 'Tapauá', 2004, 'AM', 31, 'BR', -6.21423000, -65.69985000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q1787155'),
(15226, 'Tapejara', 2022, 'PR', 31, 'BR', -23.62534000, -52.91113000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q22064023'),
(15227, 'Tapejara', 2001, 'RS', 31, 'BR', -28.06426000, -52.00823000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q2045002'),
(15228, 'Tapera', 2014, 'SC', 31, 'BR', -27.68528000, -48.55124000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q2045002'),
(15229, 'Tapera', 2001, 'RS', 31, 'BR', -28.66758000, -52.86620000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1749888'),
(15230, 'Taperoá', 2005, 'PB', 31, 'BR', -7.16870000, -36.79197000, '2019-10-05 22:35:36', '2020-05-01 17:22:37', 1, 'Q2329707'),
(15231, 'Taperoá', 2002, 'BA', 31, 'BR', -13.56959000, -39.22020000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q22062864'),
(15232, 'Tapes', 2001, 'RS', 31, 'BR', -30.67333000, -51.39583000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1785886'),
(15233, 'Tapira', 1998, 'MG', 31, 'BR', -19.93370000, -46.91795000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q22065032'),
(15234, 'Tapira', 2022, 'PR', 31, 'BR', -23.30639000, -53.13632000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1966758'),
(15235, 'Tapiramutá', 2002, 'BA', 31, 'BR', -11.84722000, -40.79139000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q22062863'),
(15236, 'Tapiratiba', 2021, 'SP', 31, 'BR', -21.44656000, -46.73214000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1658532'),
(15237, 'Tapiraí', 1998, 'MG', 31, 'BR', -19.87272000, -46.13291000, '2019-10-05 22:35:36', '2020-05-01 17:22:37', 1, 'Q22081071'),
(15238, 'Tapiraí', 2021, 'SP', 31, 'BR', -24.01487000, -47.62583000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q22064413'),
(15239, 'Tapurah', 2011, 'MT', 31, 'BR', -12.74260000, -56.46149000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q22064413'),
(15240, 'Taquara', 2001, 'RS', 31, 'BR', -29.66336000, -50.76365000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q967715'),
(15241, 'Taquaral', 2021, 'SP', 31, 'BR', -21.06760000, -48.39336000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q22064411'),
(15242, 'Taquaral de Goiás', 2000, 'GO', 31, 'BR', -16.05820000, -49.59040000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q22064411'),
(15243, 'Taquarana', 2007, 'AL', 31, 'BR', -9.64342000, -36.49419000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q22061199'),
(15244, 'Taquaraçu de Minas', 1998, 'MG', 31, 'BR', -19.63354000, -43.69161000, '2019-10-05 22:35:36', '2020-05-01 17:22:37', 1, 'Q22061199'),
(15245, 'Taquari', 2001, 'RS', 31, 'BR', -29.79972000, -51.86444000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1646949'),
(15246, 'Taquaritinga', 2021, 'SP', 31, 'BR', -21.43270000, -48.54735000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1645082'),
(15247, 'Taquaritinga do Norte', 2006, 'PE', 31, 'BR', -7.84571000, -36.12633000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q2011859'),
(15248, 'Taquarituba', 2021, 'SP', 31, 'BR', -23.52998000, -49.21441000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1759780'),
(15249, 'Taquarivaí', 2021, 'SP', 31, 'BR', -23.95095000, -48.67524000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q1759820'),
(15250, 'Taquarussu', 2010, 'MS', 31, 'BR', -22.73419000, -53.48002000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1759820'),
(15251, 'Taquaruçu do Sul', 2001, 'RS', 31, 'BR', -27.40719000, -53.49914000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q1759820'),
(15252, 'Tarabai', 2021, 'SP', 31, 'BR', -22.35043000, -51.62019000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q22064407'),
(15253, 'Tarauacá', 2012, 'AC', 31, 'BR', -8.16139000, -70.76556000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q979910'),
(15254, 'Tarrafas', 2016, 'CE', 31, 'BR', -6.73736000, -39.72329000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q979910'),
(15255, 'Tartarugalzinho', 1999, 'AP', 31, 'BR', 1.26300000, -51.10973000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q979910'),
(15256, 'Tarumirim', 1998, 'MG', 31, 'BR', -19.30041000, -41.91984000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q979910'),
(15257, 'Tarumã', 2021, 'SP', 31, 'BR', -22.76111000, -50.60332000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q1649959'),
(15258, 'Tasso Fragoso', 2015, 'MA', 31, 'BR', -8.28599000, -45.85295000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1649959'),
(15259, 'Tatuí', 2021, 'SP', 31, 'BR', -23.35556000, -47.85694000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q1795613'),
(15260, 'Taubaté', 2021, 'SP', 31, 'BR', -23.02639000, -45.55528000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q170540'),
(15261, 'Tauá', 2016, 'CE', 31, 'BR', -5.90848000, -40.27279000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q170540'),
(15262, 'Tavares', 2005, 'PB', 31, 'BR', -7.61418000, -37.87959000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q2099625'),
(15263, 'Tavares', 2001, 'RS', 31, 'BR', -31.27291000, -51.07758000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1803256'),
(15264, 'Tefé', 2004, 'AM', 31, 'BR', -3.36841000, -64.72054000, '2019-10-05 22:35:36', '2020-05-01 17:22:36', 1, 'Q975545'),
(15265, 'Teixeira', 2005, 'PB', 31, 'BR', -7.24821000, -37.27523000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1004229'),
(15266, 'Teixeira Soares', 2022, 'PR', 31, 'BR', -25.27813000, -50.43158000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q1004229'),
(15267, 'Teixeira de Freitas', 2002, 'BA', 31, 'BR', -17.42402000, -39.78697000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q282335'),
(15268, 'Teixeiras', 1998, 'MG', 31, 'BR', -20.63003000, -42.85443000, '2019-10-05 22:35:36', '2019-10-05 22:35:36', 1, 'Q616133'),
(15269, 'Teixeirópolis', 2013, 'RO', 31, 'BR', -10.99266000, -62.24735000, '2019-10-05 22:35:36', '2020-05-01 17:22:38', 1, 'Q616133'),
(15270, 'Tejupá', 2021, 'SP', 31, 'BR', -23.35231000, -49.30721000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q1760311'),
(15271, 'Tejuçuoca', 2016, 'CE', 31, 'BR', -3.93547000, -39.64972000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q1760311'),
(15272, 'Telha', 2003, 'SE', 31, 'BR', -10.17943000, -36.86754000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22063215'),
(15273, 'Telêmaco Borba', 2022, 'PR', 31, 'BR', -24.22442000, -50.54151000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q391843'),
(15274, 'Tenente Ananias', 2019, 'RN', 31, 'BR', -6.46946000, -38.16177000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q391843'),
(15275, 'Tenente Laurentino Cruz', 2019, 'RN', 31, 'BR', -6.14845000, -36.72240000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q391843'),
(15276, 'Tenente Portela', 2001, 'RS', 31, 'BR', -27.36291000, -53.77115000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q391843'),
(15277, 'Tenório', 2005, 'PB', 31, 'BR', -6.97568000, -36.62216000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q1875004'),
(15278, 'Teodoro Sampaio', 2002, 'BA', 31, 'BR', -12.26547000, -38.61211000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22062859'),
(15279, 'Teodoro Sampaio', 2021, 'SP', 31, 'BR', -22.41617000, -52.36402000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q13109202'),
(15280, 'Teofilândia', 2002, 'BA', 31, 'BR', -11.53412000, -38.94519000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q13109202'),
(15281, 'Teolândia', 2002, 'BA', 31, 'BR', -13.57031000, -39.46492000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q13109202'),
(15282, 'Teotônio Vilela', 2007, 'AL', 31, 'BR', -9.99012000, -36.43273000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q13109202'),
(15283, 'Terenos', 2010, 'MS', 31, 'BR', -20.42068000, -55.10602000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22067369'),
(15284, 'Teresina', 2008, 'PI', 31, 'BR', -5.10252000, -42.74070000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q165341'),
(15285, 'Teresina de Goiás', 2000, 'GO', 31, 'BR', -13.73244000, -47.25266000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q165341'),
(15286, 'Teresópolis', 1997, 'RJ', 31, 'BR', -22.31349000, -42.87414000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q622836'),
(15287, 'Terezinha', 2006, 'PE', 31, 'BR', -9.08773000, -36.61210000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q622836'),
(15288, 'Terezópolis de Goiás', 2000, 'GO', 31, 'BR', -16.42797000, -49.07425000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q986264'),
(15289, 'Terra Alta', 2009, 'PA', 31, 'BR', -0.99306000, -47.84447000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2289951'),
(15290, 'Terra Boa', 2022, 'PR', 31, 'BR', -23.67940000, -52.34426000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q956038'),
(15291, 'Terra Nova', 2006, 'PE', 31, 'BR', -8.16981000, -39.39098000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2604168'),
(15292, 'Terra Nova', 2002, 'BA', 31, 'BR', -12.38537000, -38.61874000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22062856'),
(15293, 'Terra Nova do Norte', 2011, 'MT', 31, 'BR', -10.49541000, -54.95547000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22062856'),
(15294, 'Terra Rica', 2022, 'PR', 31, 'BR', -22.73272000, -52.68457000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1814454'),
(15295, 'Terra Roxa', 2022, 'PR', 31, 'BR', -24.22292000, -54.09087000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22064018'),
(15296, 'Terra Roxa', 2021, 'SP', 31, 'BR', -20.76175000, -48.36899000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1759814'),
(15297, 'Terra Santa', 2009, 'PA', 31, 'BR', -2.10417000, -56.48694000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2003795'),
(15298, 'Terra de Areia', 2001, 'RS', 31, 'BR', -29.58443000, -50.06135000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2003795'),
(15299, 'Tesouro', 2011, 'MT', 31, 'BR', -15.94904000, -53.43342000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2003795'),
(15300, 'Teutônia', 2001, 'RS', 31, 'BR', -29.46814000, -51.77144000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q985568'),
(15301, 'Teófilo Otoni', 1998, 'MG', 31, 'BR', -17.67910000, -41.33738000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q1637552'),
(15302, 'Theobroma', 2013, 'RO', 31, 'BR', -10.10159000, -62.28094000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q647208'),
(15303, 'Tianguá', 2016, 'CE', 31, 'BR', -3.67295000, -40.99014000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q965707'),
(15304, 'Tibagi', 2022, 'PR', 31, 'BR', -24.65805000, -50.51861000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q987328'),
(15305, 'Tibau', 2019, 'RN', 31, 'BR', -4.91150000, -37.31347000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1802343'),
(15306, 'Tibau do Sul', 2019, 'RN', 31, 'BR', -6.18641000, -35.09050000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1802343'),
(15307, 'Tietê', 2021, 'SP', 31, 'BR', -23.04197000, -47.71430000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q542815'),
(15308, 'Tigrinhos', 2014, 'SC', 31, 'BR', -26.66573000, -53.16125000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q542815'),
(15309, 'Tijucas', 2014, 'SC', 31, 'BR', -27.24701000, -48.71619000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q951660'),
(15310, 'Tijucas do Sul', 2022, 'PR', 31, 'BR', -25.89852000, -49.11779000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q951660'),
(15311, 'Timbaúba', 2006, 'PE', 31, 'BR', -7.53194000, -35.35625000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q929867'),
(15312, 'Timbaúba dos Batistas', 2019, 'RN', 31, 'BR', -6.48262000, -37.23278000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q929867'),
(15313, 'Timbiras', 2015, 'MA', 31, 'BR', -4.19692000, -43.82569000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1008188'),
(15314, 'Timburi', 2021, 'SP', 31, 'BR', -23.19265000, -49.61224000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1760263'),
(15315, 'Timbé do Sul', 2014, 'SC', 31, 'BR', -28.79089000, -49.89509000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q1760263'),
(15316, 'Timbó', 2014, 'SC', 31, 'BR', -26.82056000, -49.28734000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q773783'),
(15317, 'Timbó Grande', 2014, 'SC', 31, 'BR', -26.62458000, -50.63553000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q773783'),
(15318, 'Timon', 2015, 'MA', 31, 'BR', -5.19778000, -42.88047000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1017101'),
(15319, 'Timóteo', 1998, 'MG', 31, 'BR', -19.58106000, -42.64953000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q737237'),
(15320, 'Tio Hugo', 2001, 'RS', 31, 'BR', -28.58180000, -52.59736000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q617526'),
(15321, 'Tiradentes', 1998, 'MG', 31, 'BR', -21.11854000, -44.16072000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1003423'),
(15322, 'Tiradentes do Sul', 2001, 'RS', 31, 'BR', -27.38183000, -54.11303000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1003423'),
(15323, 'Tiros', 1998, 'MG', 31, 'BR', -18.85727000, -45.84049000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q516274'),
(15324, 'Tobias Barreto', 2003, 'SE', 31, 'BR', -11.07792000, -38.02178000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22033542'),
(15325, 'Tocantins', 1998, 'MG', 31, 'BR', -21.18115000, -43.02555000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22065025'),
(15326, 'Tocantinópolis', 2020, 'TO', 31, 'BR', -6.26254000, -47.55767000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q22065025'),
(15327, 'Tocantínia', 2020, 'TO', 31, 'BR', -9.56440000, -48.18709000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q22065025'),
(15328, 'Tocos do Moji', 1998, 'MG', 31, 'BR', -22.35665000, -46.14951000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22065025'),
(15329, 'Toledo', 2022, 'PR', 31, 'BR', -24.71361000, -53.74306000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1776774'),
(15330, 'Toledo', 1998, 'MG', 31, 'BR', -22.70569000, -46.39041000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22065023'),
(15331, 'Tomar do Geru', 2003, 'SE', 31, 'BR', -11.37415000, -37.87537000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22033560'),
(15332, 'Tomazina', 2022, 'PR', 31, 'BR', -23.77655000, -49.95552000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22033560'),
(15333, 'Tombos', 1998, 'MG', 31, 'BR', -20.88046000, -42.06821000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22065022'),
(15334, 'Tomé Açu', 2009, 'PA', 31, 'BR', -2.41889000, -48.15222000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q22065022'),
(15335, 'Tomé-Açu', 2009, 'PA', 31, 'BR', -2.62132000, -48.24098000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q1805230'),
(15336, 'Tonantins', 2004, 'AM', 31, 'BR', -2.87306000, -67.80222000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1793355'),
(15337, 'Toritama', 2006, 'PE', 31, 'BR', -7.99823000, -36.06332000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2013594'),
(15338, 'Torixoréu', 2011, 'MT', 31, 'BR', -16.27591000, -52.88117000, '2019-10-05 22:35:37', '2020-05-01 17:22:36', 1, 'Q2013594'),
(15339, 'Toropi', 2001, 'RS', 31, 'BR', -29.47869000, -54.29307000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2013594'),
(15340, 'Torre de Pedra', 2021, 'SP', 31, 'BR', -23.24640000, -48.21328000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1759645'),
(15341, 'Torres', 2001, 'RS', 31, 'BR', -29.29667000, -49.81982000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q952674'),
(15342, 'Torrinha', 2021, 'SP', 31, 'BR', -22.47144000, -48.15338000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1751333'),
(15343, 'Touros', 2019, 'RN', 31, 'BR', -5.26111000, -35.60459000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1797006'),
(15344, 'Trabiju', 2021, 'SP', 31, 'BR', -22.03023000, -48.36861000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22064400'),
(15345, 'Tracuateua', 2009, 'PA', 31, 'BR', -0.98894000, -46.93973000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22064400'),
(15346, 'Tracunhaém', 2006, 'PE', 31, 'BR', -7.72460000, -35.15480000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q22064400'),
(15347, 'Traipu', 2007, 'AL', 31, 'BR', -9.89240000, -36.97849000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1772703'),
(15348, 'Trairi', 2016, 'CE', 31, 'BR', -3.33156000, -39.38032000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q1807039'),
(15349, 'Trairão', 2009, 'PA', 31, 'BR', -5.10250000, -55.95821000, '2019-10-05 22:35:37', '2020-05-01 17:22:37', 1, 'Q2009409'),
(15350, 'Trajano de Moraes', 1997, 'RJ', 31, 'BR', -22.15204000, -42.18834000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2009409'),
(15351, 'Tramandaí', 2001, 'RS', 31, 'BR', -30.03977000, -50.23016000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q187160'),
(15352, 'Travesseiro', 2001, 'RS', 31, 'BR', -29.28808000, -52.09974000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q187160'),
(15353, 'Tremedal', 2002, 'BA', 31, 'BR', -14.90080000, -41.32339000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22060578'),
(15354, 'Tremembé', 2021, 'SP', 31, 'BR', -22.92410000, -45.61503000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q22064399'),
(15355, 'Treviso', 2014, 'SC', 31, 'BR', -28.49941000, -49.50360000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q22063766'),
(15356, 'Treze Tílias', 2014, 'SC', 31, 'BR', -26.95726000, -51.43183000, '2019-10-05 22:35:37', '2020-05-01 17:22:38', 1, 'Q339414'),
(15357, 'Treze de Maio', 2014, 'SC', 31, 'BR', -28.53919000, -49.14504000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q339414'),
(15358, 'Trindade', 2000, 'GO', 31, 'BR', -16.64944000, -49.48889000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q985583'),
(15359, 'Trindade', 2014, 'SC', 31, 'BR', -27.58612000, -48.52335000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q985583'),
(15360, 'Trindade', 2006, 'PE', 31, 'BR', -7.78327000, -40.33408000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2355756'),
(15361, 'Trindade do Sul', 2001, 'RS', 31, 'BR', -27.52679000, -52.91351000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2355756'),
(15362, 'Triunfo', 2005, 'PB', 31, 'BR', -6.58475000, -38.57827000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q2097715'),
(15363, 'Triunfo', 2006, 'PE', 31, 'BR', -7.84766000, -38.05176000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q679786'),
(15364, 'Triunfo', 2001, 'RS', 31, 'BR', -29.82246000, -51.56027000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q606083'),
(15365, 'Triunfo Potiguar', 2019, 'RN', 31, 'BR', -5.94356000, -37.13994000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q606083'),
(15366, 'Trizidela do Vale', 2015, 'MA', 31, 'BR', -4.52222000, -44.61751000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q606083'),
(15367, 'Trombas', 2000, 'GO', 31, 'BR', -13.40409000, -48.77287000, '2019-10-05 22:35:37', '2019-10-05 22:35:37', 1, 'Q606083'),
(15368, 'Trombudo Central', 2014, 'SC', 31, 'BR', -27.30803000, -49.81251000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q606083'),
(15369, 'Três Arroios', 2001, 'RS', 31, 'BR', -27.48222000, -52.18370000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q606083'),
(15370, 'Três Barras', 2014, 'SC', 31, 'BR', -26.16511000, -50.27142000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q22063767'),
(15371, 'Três Barras do Paraná', 2022, 'PR', 31, 'BR', -25.42222000, -53.23148000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q22063767'),
(15372, 'Três Cachoeiras', 2001, 'RS', 31, 'BR', -29.47525000, -49.98487000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q22063767'),
(15373, 'Três Corações', 1998, 'MG', 31, 'BR', -21.70260000, -45.21421000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q1439211'),
(15374, 'Três Coroas', 2001, 'RS', 31, 'BR', -29.46604000, -50.77684000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q984552'),
(15375, 'Três Forquilhas', 2001, 'RS', 31, 'BR', -29.43246000, -50.08341000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q984552'),
(15376, 'Três Fronteiras', 2021, 'SP', 31, 'BR', -20.29093000, -50.87027000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q1750027'),
(15377, 'Três Lagoas', 2010, 'MS', 31, 'BR', -20.37964000, -52.25961000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q22067368'),
(15378, 'Três Marias', 1998, 'MG', 31, 'BR', -18.28891000, -45.02559000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q22067368'),
(15379, 'Três Palmeiras', 2001, 'RS', 31, 'BR', -27.61276000, -52.86635000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q22067368'),
(15380, 'Três Passos', 2001, 'RS', 31, 'BR', -27.43083000, -53.92238000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q728608'),
(15381, 'Três Pontas', 1998, 'MG', 31, 'BR', -21.40521000, -45.49443000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q1749754'),
(15382, 'Três Ranchos', 2000, 'GO', 31, 'BR', -18.36835000, -47.79850000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q1749754'),
(15383, 'Três Rios', 1997, 'RJ', 31, 'BR', -22.12121000, -43.06617000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q1787655'),
(15384, 'Três de Maio', 2001, 'RS', 31, 'BR', -27.73449000, -54.26018000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q1786482'),
(15385, 'Tubarão', 2014, 'SC', 31, 'BR', -28.46667000, -49.00694000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q1815730'),
(15386, 'Tucano', 2002, 'BA', 31, 'BR', -10.99427000, -38.86249000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q22062855'),
(15387, 'Tucumã', 2009, 'PA', 31, 'BR', -6.83470000, -51.44562000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q22060188'),
(15388, 'Tucunduva', 2001, 'RS', 31, 'BR', -27.64923000, -54.43972000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q22060188'),
(15389, 'Tucuruí', 2009, 'PA', 31, 'BR', -3.76585000, -49.67923000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q374432'),
(15390, 'Tufilândia', 2015, 'MA', 31, 'BR', -3.76611000, -45.55606000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q374432'),
(15391, 'Tuiuti', 2021, 'SP', 31, 'BR', -22.84505000, -46.69618000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q22064397'),
(15392, 'Tumiritinga', 1998, 'MG', 31, 'BR', -19.02399000, -41.72297000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q22064397'),
(15393, 'Tunas', 2001, 'RS', 31, 'BR', -29.10586000, -52.89867000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1803262'),
(15394, 'Tunas do Paraná', 2022, 'PR', 31, 'BR', -24.96753000, -48.86604000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q1803262'),
(15395, 'Tuneiras do Oeste', 2022, 'PR', 31, 'BR', -23.90168000, -52.83572000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1803262'),
(15396, 'Tuntum', 2015, 'MA', 31, 'BR', -5.60041000, -44.85537000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1957192'),
(15397, 'Tunápolis', 2014, 'SC', 31, 'BR', -26.99245000, -53.65618000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q1957192'),
(15398, 'Tupaciguara', 1998, 'MG', 31, 'BR', -18.59222000, -48.70500000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1787184'),
(15399, 'Tupanatinga', 2006, 'PE', 31, 'BR', -8.67328000, -37.34532000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1804078'),
(15400, 'Tupanci do Sul', 2001, 'RS', 31, 'BR', -27.92640000, -51.54450000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1804078'),
(15401, 'Tupanciretã', 2001, 'RS', 31, 'BR', -29.02190000, -53.97471000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q1756630'),
(15402, 'Tupandi', 2001, 'RS', 31, 'BR', -29.48000000, -51.43044000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1756630'),
(15403, 'Tuparendi', 2001, 'RS', 31, 'BR', -27.68918000, -54.55912000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1756630'),
(15404, 'Tuparetama', 2006, 'PE', 31, 'BR', -7.71335000, -37.24523000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1756630'),
(15405, 'Tupi Paulista', 2021, 'SP', 31, 'BR', -21.38916000, -51.58294000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1760131'),
(15406, 'Tupirama', 2020, 'TO', 31, 'BR', -8.91920000, -48.27661000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1760131'),
(15407, 'Tupiratins', 2020, 'TO', 31, 'BR', -8.38578000, -48.22299000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1760131'),
(15408, 'Tupã', 2021, 'SP', 31, 'BR', -21.95443000, -50.53493000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q961143'),
(15409, 'Tupãssi', 2022, 'PR', 31, 'BR', -24.63318000, -53.49207000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q961143'),
(15410, 'Turiaçu', 2015, 'MA', 31, 'BR', -1.66333000, -45.37167000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q634717'),
(15411, 'Turilândia', 2015, 'MA', 31, 'BR', -2.12411000, -45.34721000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q634717'),
(15412, 'Turiúba', 2021, 'SP', 31, 'BR', -20.94735000, -50.09803000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q22064394'),
(15413, 'Turmalina', 1998, 'MG', 31, 'BR', -17.25347000, -42.83041000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q22065016'),
(15414, 'Turmalina', 2021, 'SP', 31, 'BR', -20.05953000, -50.45595000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1762903'),
(15415, 'Tururu', 2016, 'CE', 31, 'BR', -3.53335000, -39.37556000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1762903'),
(15416, 'Turuçu', 2001, 'RS', 31, 'BR', -31.52317000, -52.12870000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q1762903'),
(15417, 'Turvelândia', 2000, 'GO', 31, 'BR', -17.79588000, -50.29695000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q1762903'),
(15418, 'Turvo', 2022, 'PR', 31, 'BR', -24.97002000, -51.50860000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1899023'),
(15419, 'Turvo', 2014, 'SC', 31, 'BR', -28.89831000, -49.68268000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1833591'),
(15420, 'Turvolândia', 1998, 'MG', 31, 'BR', -21.87243000, -45.80089000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q1788481'),
(15421, 'Turvânia', 2000, 'GO', 31, 'BR', -16.55807000, -50.16909000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q1788481'),
(15422, 'Tutóia', 2015, 'MA', 31, 'BR', -2.76194000, -42.27444000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q2012483'),
(15423, 'Uarini', 2004, 'AM', 31, 'BR', -3.14736000, -65.42036000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q517607'),
(15424, 'Uauá', 2002, 'BA', 31, 'BR', -9.84143000, -39.23025000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q22062854'),
(15425, 'Ubaitaba', 2002, 'BA', 31, 'BR', -14.31250000, -39.32333000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1762068'),
(15426, 'Ubajara', 2016, 'CE', 31, 'BR', -3.84020000, -40.93422000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1821314'),
(15427, 'Ubaporanga', 1998, 'MG', 31, 'BR', -19.66187000, -42.05903000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1788403'),
(15428, 'Ubarana', 2021, 'SP', 31, 'BR', -21.22036000, -49.72981000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1750070'),
(15429, 'Ubatuba', 2021, 'SP', 31, 'BR', -23.43389000, -45.07111000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q596931'),
(15430, 'Ubatã', 2002, 'BA', 31, 'BR', -14.05629000, -39.52278000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q22062851'),
(15431, 'Ubaí', 1998, 'MG', 31, 'BR', -16.38337000, -44.85525000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q952767'),
(15432, 'Ubaíra', 2002, 'BA', 31, 'BR', -13.28700000, -39.69347000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q22062853'),
(15433, 'Uberaba', 1998, 'MG', 31, 'BR', -19.54826000, -47.94144000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q381733'),
(15434, 'Uberlândia', 1998, 'MG', 31, 'BR', -19.02333000, -48.33477000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q194328'),
(15435, 'Ubirajara', 2021, 'SP', 31, 'BR', -22.55131000, -49.66278000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1646327'),
(15436, 'Ubiratã', 2022, 'PR', 31, 'BR', -24.50395000, -53.01934000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q1646327'),
(15437, 'Ubiretama', 2001, 'RS', 31, 'BR', -28.05186000, -54.66252000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1646327'),
(15438, 'Ubá', 1998, 'MG', 31, 'BR', -21.12000000, -42.94278000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q922944'),
(15439, 'Uchoa', 2021, 'SP', 31, 'BR', -20.91850000, -49.13654000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1658522'),
(15440, 'Uibaí', 2002, 'BA', 31, 'BR', -11.39060000, -42.15615000, '2019-10-05 22:35:38', '2020-05-01 17:22:36', 1, 'Q1658522'),
(15441, 'Uirapuru', 2000, 'GO', 31, 'BR', -14.14128000, -49.93396000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1008332'),
(15442, 'Uiraúna', 2005, 'PB', 31, 'BR', -6.49650000, -38.38015000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q2010393'),
(15443, 'Ulianópolis', 2009, 'PA', 31, 'BR', -3.81225000, -47.50094000, '2019-10-05 22:35:38', '2020-05-01 17:22:37', 1, 'Q2010393'),
(15444, 'Umari', 2016, 'CE', 31, 'BR', -6.60924000, -38.71952000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q2068917'),
(15445, 'Umarizal', 2019, 'RN', 31, 'BR', -6.00593000, -37.80698000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1802407'),
(15446, 'Umbaúba', 2003, 'SE', 31, 'BR', -11.40045000, -37.66270000, '2019-10-05 22:35:38', '2020-05-01 17:22:38', 1, 'Q22063214'),
(15447, 'Umburanas', 2002, 'BA', 31, 'BR', -10.50860000, -41.17419000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q22063214'),
(15448, 'Umburatiba', 1998, 'MG', 31, 'BR', -17.25657000, -40.66878000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1754681'),
(15449, 'Umbuzeiro', 2005, 'PB', 31, 'BR', -7.67111000, -35.73927000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q1754681'),
(15450, 'Umirim', 2016, 'CE', 31, 'BR', -3.70599000, -39.39689000, '2019-10-05 22:35:38', '2019-10-05 22:35:38', 1, 'Q778286'),
(15451, 'Umuarama', 2022, 'PR', 31, 'BR', -23.76639000, -53.32500000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q593948'),
(15452, 'Una', 2002, 'BA', 31, 'BR', -15.16451000, -39.20568000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22060576'),
(15453, 'Unaí', 1998, 'MG', 31, 'BR', -16.58634000, -46.77009000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q1780258'),
(15454, 'Uniflor', 2022, 'PR', 31, 'BR', -23.06016000, -52.09077000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1780258'),
(15455, 'Unistalda', 2001, 'RS', 31, 'BR', -29.08248000, -55.19194000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1780258'),
(15456, 'União', 2008, 'PI', 31, 'BR', -4.59646000, -42.86468000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q2101441'),
(15457, 'União Paulista', 2021, 'SP', 31, 'BR', -20.88795000, -49.89135000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q2101441'),
(15458, 'União da Serra', 2001, 'RS', 31, 'BR', -28.78526000, -52.03971000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q2101441'),
(15459, 'União da Vitória', 2022, 'PR', 31, 'BR', -26.09211000, -51.11212000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q988869'),
(15460, 'União de Minas', 1998, 'MG', 31, 'BR', -19.34087000, -50.35738000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q1787713'),
(15461, 'União do Oeste', 2014, 'SC', 31, 'BR', -26.79980000, -52.89258000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q1787713'),
(15462, 'União do Sul', 2011, 'MT', 31, 'BR', -11.47198000, -54.18806000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q1787713'),
(15463, 'União dos Palmares', 2007, 'AL', 31, 'BR', -9.13251000, -36.08182000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q22063263'),
(15464, 'Upanema', 2019, 'RN', 31, 'BR', -5.69921000, -37.26798000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1786764'),
(15465, 'Urandi', 2002, 'BA', 31, 'BR', -14.73400000, -42.66544000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1786764'),
(15466, 'Uraí', 2022, 'PR', 31, 'BR', -23.19291000, -50.79419000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q1786764'),
(15467, 'Urbano Santos', 2015, 'MA', 31, 'BR', -3.37970000, -43.38142000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q2064632'),
(15468, 'Uru', 2021, 'SP', 31, 'BR', -21.76519000, -49.29720000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q13109246'),
(15469, 'Uruana', 2000, 'GO', 31, 'BR', -15.56368000, -49.64028000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22067077'),
(15470, 'Uruana de Minas', 1998, 'MG', 31, 'BR', -16.07488000, -46.33829000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22067077'),
(15471, 'Uruará', 2009, 'PA', 31, 'BR', -3.63212000, -53.78023000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q22067077'),
(15472, 'Uruaçu', 2000, 'GO', 31, 'BR', -14.42245000, -49.06374000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q22067078'),
(15473, 'Urubici', 2014, 'SC', 31, 'BR', -28.04998000, -49.57417000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22067078'),
(15474, 'Uruburetama', 2016, 'CE', 31, 'BR', -3.62829000, -39.54760000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q2021056'),
(15475, 'Urucará', 2004, 'AM', 31, 'BR', -2.53639000, -57.76000000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q1816370'),
(15476, 'Urucuia', 1998, 'MG', 31, 'BR', -16.01822000, -45.61731000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1816370'),
(15477, 'Urucurituba', 2004, 'AM', 31, 'BR', -3.12845000, -58.15856000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22014945'),
(15478, 'Urucânia', 1998, 'MG', 31, 'BR', -20.32243000, -42.71972000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q22014945'),
(15479, 'Uruguaiana', 2001, 'RS', 31, 'BR', -29.82797000, -56.63224000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q622823'),
(15480, 'Uruoca', 2016, 'CE', 31, 'BR', -3.31983000, -40.72170000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q622823'),
(15481, 'Urupema', 2014, 'SC', 31, 'BR', -28.04160000, -49.92971000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q622823'),
(15482, 'Urupá', 2013, 'RO', 31, 'BR', -11.07917000, -62.38127000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q2089326'),
(15483, 'Urupês', 2021, 'SP', 31, 'BR', -21.18413000, -49.28069000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q1759763'),
(15484, 'Urussanga', 2014, 'SC', 31, 'BR', -28.47460000, -49.31252000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22063758'),
(15485, 'Urutaí', 2000, 'GO', 31, 'BR', -17.41664000, -48.19794000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q22063758'),
(15486, 'Uruçuca', 2002, 'BA', 31, 'BR', -14.59306000, -39.28444000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q22030758'),
(15487, 'Uruçuí', 2008, 'PI', 31, 'BR', -7.34206000, -44.58334000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q22059606'),
(15488, 'Urânia', 2021, 'SP', 31, 'BR', -20.20908000, -50.66093000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q22059606'),
(15489, 'Utinga', 2002, 'BA', 31, 'BR', -12.04274000, -41.19475000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22062847'),
(15490, 'Vacaria', 2001, 'RS', 31, 'BR', -28.41223000, -50.94431000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q941580'),
(15491, 'Vale Real', 2001, 'RS', 31, 'BR', -29.34986000, -51.23013000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q941580'),
(15492, 'Vale Verde', 2001, 'RS', 31, 'BR', -29.83509000, -52.10562000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q594012'),
(15493, 'Vale de São Domingos', 2011, 'MT', 31, 'BR', -14.97590000, -58.96377000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q2081565'),
(15494, 'Vale do Anari', 2013, 'RO', 31, 'BR', -9.73244000, -61.93919000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q2081565'),
(15495, 'Vale do Paraíso', 2013, 'RO', 31, 'BR', -10.20652000, -62.06993000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q2081565'),
(15496, 'Vale do Sol', 2001, 'RS', 31, 'BR', -29.58160000, -52.68161000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q2081565'),
(15497, 'Valente', 2002, 'BA', 31, 'BR', -11.43393000, -39.48472000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22062845'),
(15498, 'Valentim Gentil', 2021, 'SP', 31, 'BR', -20.43138000, -50.12129000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22062845'),
(15499, 'Valença', 2002, 'BA', 31, 'BR', -13.37213000, -39.24002000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q1010498'),
(15500, 'Valença', 1997, 'RJ', 31, 'BR', -22.24020000, -43.87843000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q1005931'),
(15501, 'Valença do Piauí', 2008, 'PI', 31, 'BR', -6.27870000, -41.81385000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q22059605'),
(15502, 'Valinhos', 2021, 'SP', 31, 'BR', -22.97056000, -46.99583000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q983511'),
(15503, 'Valparaíso', 2021, 'SP', 31, 'BR', -21.21512000, -50.94698000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q13109251'),
(15504, 'Valparaíso de Goiás', 2000, 'GO', 31, 'BR', -16.09899000, -47.98813000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q13109251'),
(15505, 'Vanini', 2001, 'RS', 31, 'BR', -28.48831000, -51.83316000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1788290'),
(15506, 'Vargem', 2021, 'SP', 31, 'BR', -22.89117000, -46.41556000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1760599'),
(15507, 'Vargem', 2014, 'SC', 31, 'BR', -27.45675000, -50.95789000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22063756'),
(15508, 'Vargem Alegre', 1998, 'MG', 31, 'BR', -19.60085000, -42.32330000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22063756'),
(15509, 'Vargem Alta', 2018, 'ES', 31, 'BR', -20.64727000, -41.00855000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q762224'),
(15510, 'Vargem Bonita', 1998, 'MG', 31, 'BR', -20.40556000, -46.31426000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1787582'),
(15511, 'Vargem Bonita', 2014, 'SC', 31, 'BR', -26.93296000, -51.74642000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q609989'),
(15512, 'Vargem Grande', 2015, 'MA', 31, 'BR', -3.60095000, -43.85169000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1019640'),
(15513, 'Vargem Grande Paulista', 2021, 'SP', 31, 'BR', -23.62856000, -47.01914000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1750075'),
(15514, 'Vargem Grande do Rio Pardo', 1998, 'MG', 31, 'BR', -15.34182000, -42.30107000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1787599'),
(15515, 'Vargem Grande do Sul', 2021, 'SP', 31, 'BR', -21.87152000, -46.89073000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q767854'),
(15516, 'Vargeão', 2014, 'SC', 31, 'BR', -26.84426000, -52.15332000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q767854'),
(15517, 'Varginha', 1998, 'MG', 31, 'BR', -21.55139000, -45.43028000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q983524'),
(15518, 'Varjota', 2016, 'CE', 31, 'BR', -4.19444000, -40.47667000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1939222'),
(15519, 'Varjão', 2000, 'GO', 31, 'BR', -17.07758000, -49.62111000, '2019-10-05 22:35:39', '2020-05-01 17:22:36', 1, 'Q1939222'),
(15520, 'Varjão de MInas', 1998, 'MG', 31, 'BR', -18.45617000, -45.89854000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q1787279'),
(15521, 'Varre-Sai', 1997, 'RJ', 31, 'BR', -20.89839000, -41.81982000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1803142'),
(15522, 'Varzedo', 2002, 'BA', 31, 'BR', -12.99662000, -39.37091000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1803142'),
(15523, 'Varzelândia', 1998, 'MG', 31, 'BR', -15.65541000, -43.92043000, '2019-10-05 22:35:39', '2020-05-01 17:22:37', 1, 'Q1787139'),
(15524, 'Vassouras', 1997, 'RJ', 31, 'BR', -22.35995000, -43.59809000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1806430'),
(15525, 'Vazante', 1998, 'MG', 31, 'BR', -17.78783000, -46.79546000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1754786'),
(15526, 'Venda Nova do Imigrante', 2018, 'ES', 31, 'BR', -20.38487000, -41.13539000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1754786'),
(15527, 'Venha-Ver', 2019, 'RN', 31, 'BR', -6.34075000, -38.53731000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1802298'),
(15528, 'Ventania', 2022, 'PR', 31, 'BR', -24.14204000, -50.22365000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1802298'),
(15529, 'Venturosa', 2006, 'PE', 31, 'BR', -8.60330000, -36.79818000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1802298'),
(15530, 'Venâncio Aires', 2001, 'RS', 31, 'BR', -29.54206000, -52.21959000, '2019-10-05 22:35:39', '2020-05-01 17:22:38', 1, 'Q983517'),
(15531, 'Vera', 2011, 'MT', 31, 'BR', -12.46612000, -55.34810000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q983517'),
(15532, 'Vera Cruz', 2019, 'RN', 31, 'BR', -6.03261000, -35.44416000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q586682'),
(15533, 'Vera Cruz', 2002, 'BA', 31, 'BR', -13.02550000, -38.70906000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22062840'),
(15534, 'Vera Cruz', 2001, 'RS', 31, 'BR', -29.77275000, -52.52818000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q1802230'),
(15535, 'Vera Cruz', 2021, 'SP', 31, 'BR', -22.23340000, -49.83281000, '2019-10-05 22:35:39', '2019-10-05 22:35:39', 1, 'Q22066862'),
(15536, 'Vera Cruz do Oeste', 2022, 'PR', 31, 'BR', -24.94088000, -53.92809000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22066862'),
(15537, 'Vera Mendes', 2008, 'PI', 31, 'BR', -7.55480000, -41.50596000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22066862'),
(15538, 'Veranópolis', 2001, 'RS', 31, 'BR', -28.97791000, -51.55656000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1101945'),
(15539, 'Verdejante', 2006, 'PE', 31, 'BR', -7.98470000, -38.99816000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1101945'),
(15540, 'Verdelândia', 1998, 'MG', 31, 'BR', -15.54677000, -43.68013000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1788238'),
(15541, 'Vereda', 2002, 'BA', 31, 'BR', -17.14998000, -40.04873000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q631107'),
(15542, 'Veredinha', 1998, 'MG', 31, 'BR', -17.50571000, -42.74858000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q631107'),
(15543, 'Vermelho Novo', 1998, 'MG', 31, 'BR', -20.03087000, -42.27068000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1754736'),
(15544, 'Vertente do Lério', 2006, 'PE', 31, 'BR', -7.78986000, -35.80318000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1754736'),
(15545, 'Vertentes', 2006, 'PE', 31, 'BR', -7.90936000, -35.97775000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q2065306'),
(15546, 'Verê', 2022, 'PR', 31, 'BR', -25.85329000, -52.95920000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q2065306'),
(15547, 'Veríssimo', 1998, 'MG', 31, 'BR', -19.59178000, -48.35893000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q22064998'),
(15548, 'Vespasiano', 1998, 'MG', 31, 'BR', -19.69194000, -43.92333000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1012546'),
(15549, 'Vespasiano Corrêa', 2001, 'RS', 31, 'BR', -29.07064000, -51.86646000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1012546'),
(15550, 'Viadutos', 2001, 'RS', 31, 'BR', -27.57743000, -51.98735000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1012546'),
(15551, 'Viamão', 2001, 'RS', 31, 'BR', -30.08111000, -51.02333000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q732905'),
(15552, 'Viana', 2015, 'MA', 31, 'BR', -3.19698000, -45.00551000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q2106072'),
(15553, 'Viana', 2018, 'ES', 31, 'BR', -20.36757000, -40.51413000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q2020315'),
(15554, 'Vianópolis', 2000, 'GO', 31, 'BR', -16.85286000, -48.45647000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q22067073'),
(15555, 'Vicente Dutra', 2001, 'RS', 31, 'BR', -27.17975000, -53.39966000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22067073'),
(15556, 'Vicentina', 2010, 'MS', 31, 'BR', -22.49155000, -54.46183000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22067073'),
(15557, 'Vicentinópolis', 2000, 'GO', 31, 'BR', -17.70714000, -49.90768000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q22067073'),
(15558, 'Victor Graeff', 2001, 'RS', 31, 'BR', -28.54502000, -52.68069000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22067073'),
(15559, 'Vicência', 2006, 'PE', 31, 'BR', -7.65645000, -35.39117000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q2010783'),
(15560, 'Vidal Ramos', 2014, 'SC', 31, 'BR', -27.38973000, -49.33820000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q2010783'),
(15561, 'Videira', 2014, 'SC', 31, 'BR', -27.03437000, -51.10156000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1799400'),
(15562, 'Vieiras', 1998, 'MG', 31, 'BR', -20.90888000, -42.28015000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1788420'),
(15563, 'Vieirópolis', 2005, 'PB', 31, 'BR', -6.55838000, -38.27091000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1788420'),
(15564, 'Vigia', 2009, 'PA', 31, 'BR', -0.85833000, -48.14167000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1804913'),
(15565, 'Vila Bela da Santíssima Trindade', 2011, 'MT', 31, 'BR', -14.89148000, -60.05777000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1804913'),
(15566, 'Vila Boa', 2000, 'GO', 31, 'BR', -14.97912000, -47.09259000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1025753'),
(15567, 'Vila Flor', 2019, 'RN', 31, 'BR', -6.29375000, -35.08453000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1920821'),
(15568, 'Vila Flores', 2001, 'RS', 31, 'BR', -28.86838000, -51.52935000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1920821'),
(15569, 'Vila Lângaro', 2001, 'RS', 31, 'BR', -28.12778000, -52.14152000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1920821'),
(15570, 'Vila Maria', 2001, 'RS', 31, 'BR', -28.55505000, -52.16380000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1784456'),
(15571, 'Vila Nova do Piauí', 2008, 'PI', 31, 'BR', -7.18671000, -40.93621000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1784456'),
(15572, 'Vila Nova do Sul', 2001, 'RS', 31, 'BR', -30.33711000, -53.88004000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1784456'),
(15573, 'Vila Nova dos Martírios', 2015, 'MA', 31, 'BR', -5.06154000, -48.06797000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q2065169'),
(15574, 'Vila Pavão', 2018, 'ES', 31, 'BR', -18.61186000, -40.65163000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q2065169'),
(15575, 'Vila Propício', 2000, 'GO', 31, 'BR', -15.20221000, -48.77569000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q2065169'),
(15576, 'Vila Rica', 2011, 'MT', 31, 'BR', -10.13413000, -51.41369000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22067275'),
(15577, 'Vila Valério', 2018, 'ES', 31, 'BR', -18.97995000, -40.36633000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q22067275'),
(15578, 'Vila Velha', 2018, 'ES', 31, 'BR', -20.32972000, -40.29250000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q334197'),
(15579, 'Vilhena', 2013, 'RO', 31, 'BR', -12.02062000, -60.27526000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1749958'),
(15580, 'Vinhedo', 2021, 'SP', 31, 'BR', -23.05397000, -46.98135000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q983534'),
(15581, 'Viradouro', 2021, 'SP', 31, 'BR', -20.87625000, -48.31479000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1760032'),
(15582, 'Virgem da Lapa', 1998, 'MG', 31, 'BR', -16.71637000, -42.34853000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1760032'),
(15583, 'Virginópolis', 1998, 'MG', 31, 'BR', -18.81508000, -42.66943000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1787121'),
(15584, 'Virgolândia', 1998, 'MG', 31, 'BR', -18.44284000, -42.30736000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1787121'),
(15585, 'Virgínia', 1998, 'MG', 31, 'BR', -22.35515000, -45.11577000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1787215'),
(15586, 'Virmond', 2022, 'PR', 31, 'BR', -25.43010000, -52.23673000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1787215'),
(15587, 'Visconde do Rio Branco', 1998, 'MG', 31, 'BR', -20.99510000, -42.85289000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1754796'),
(15588, 'Viseu', 2009, 'PA', 31, 'BR', -1.19667000, -46.14000000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q772569'),
(15589, 'Vista Alegre', 2001, 'RS', 31, 'BR', -27.31083000, -53.51572000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1774451'),
(15590, 'Vista Alegre do Alto', 2021, 'SP', 31, 'BR', -21.18019000, -48.65482000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1774451'),
(15591, 'Vista Alegre do Prata', 2001, 'RS', 31, 'BR', -28.83075000, -51.79221000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1774451'),
(15592, 'Vista Gaúcha', 2001, 'RS', 31, 'BR', -27.26738000, -53.69737000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1774460'),
(15593, 'Vista Serrana', 2005, 'PB', 31, 'BR', -6.74167000, -37.57597000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1774460'),
(15594, 'Vitor Meireles', 2014, 'SC', 31, 'BR', -26.82672000, -49.85522000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1774460'),
(15595, 'Vitorino', 2022, 'PR', 31, 'BR', -26.24047000, -52.81976000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1774460'),
(15596, 'Vitorino Freire', 2015, 'MA', 31, 'BR', -4.21879000, -45.31980000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1120902'),
(15597, 'Vitória', 2018, 'ES', 31, 'BR', -20.31944000, -40.33778000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q168376'),
(15598, 'Vitória Brasil', 2021, 'SP', 31, 'BR', -20.19354000, -50.47940000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q168376'),
(15599, 'Vitória da Conquista', 2002, 'BA', 31, 'BR', -15.27953000, -40.96575000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q22062839'),
(15600, 'Vitória das Missões', 2001, 'RS', 31, 'BR', -28.35473000, -54.48174000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1774423'),
(15601, 'Vitória de Santo Antão', 2006, 'PE', 31, 'BR', -8.11806000, -35.29139000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q985685'),
(15602, 'Vitória do Jari', 1999, 'AP', 31, 'BR', -0.95653000, -52.03443000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1772675'),
(15603, 'Vitória do Mearim', 2015, 'MA', 31, 'BR', -3.46222000, -44.87056000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q2065110'),
(15604, 'Vitória do Xingu', 2009, 'PA', 31, 'BR', -3.22032000, -51.88508000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q2065110'),
(15605, 'Viçosa', 2019, 'RN', 31, 'BR', -5.99110000, -37.96495000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q2309735'),
(15606, 'Viçosa', 2007, 'AL', 31, 'BR', -9.36022000, -36.32095000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q22063262'),
(15607, 'Viçosa', 1998, 'MG', 31, 'BR', -20.73527000, -42.89525000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1780182'),
(15608, 'Viçosa do Ceará', 2016, 'CE', 31, 'BR', -3.56222000, -41.09222000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q2021075'),
(15609, 'Volta Grande', 1998, 'MG', 31, 'BR', -21.75298000, -42.57357000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1778624'),
(15610, 'Volta Redonda', 1997, 'RJ', 31, 'BR', -22.52306000, -44.10417000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q276176'),
(15611, 'Votorantim', 2021, 'SP', 31, 'BR', -23.54667000, -47.43778000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q942083'),
(15612, 'Votuporanga', 2021, 'SP', 31, 'BR', -20.48010000, -50.01266000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1759612'),
(15613, 'Várzea', 2005, 'PB', 31, 'BR', -6.80276000, -37.03707000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q2065255'),
(15614, 'Várzea', 2019, 'RN', 31, 'BR', -6.35146000, -35.36839000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q600510'),
(15615, 'Várzea Alegre', 2016, 'CE', 31, 'BR', -5.35000000, -40.38333000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q22014990'),
(15616, 'Várzea Branca', 2008, 'PI', 31, 'BR', -9.34411000, -42.95158000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q22014990'),
(15617, 'Várzea Grande', 2008, 'PI', 31, 'BR', -6.56374000, -42.17729000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q22059603'),
(15618, 'Várzea Grande', 2011, 'MT', 31, 'BR', -15.56582000, -56.24391000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q983884'),
(15619, 'Várzea Nova', 2002, 'BA', 31, 'BR', -11.05523000, -41.23230000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q983884'),
(15620, 'Várzea Paulista', 2021, 'SP', 31, 'BR', -23.21139000, -46.82833000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1759112'),
(15621, 'Várzea da Palma', 1998, 'MG', 31, 'BR', -17.46025000, -44.74043000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1787088'),
(15622, 'Várzea da Roça', 2002, 'BA', 31, 'BR', -11.53905000, -40.07647000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1787088'),
(15623, 'Várzea do Poço', 2002, 'BA', 31, 'BR', -11.53021000, -40.30918000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1787088'),
(15624, 'Wagner', 2002, 'BA', 31, 'BR', -12.25930000, -41.22095000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22062838'),
(15625, 'Wall Ferraz', 2008, 'PI', 31, 'BR', -7.29532000, -41.83730000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22062838'),
(15626, 'Wanderley', 2002, 'BA', 31, 'BR', -11.76500000, -43.99378000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22062838'),
(15627, 'Wanderlândia', 2020, 'TO', 31, 'BR', -6.79803000, -48.00415000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q22062838'),
(15628, 'Wenceslau Braz', 1998, 'MG', 31, 'BR', -22.53974000, -45.40214000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q658813'),
(15629, 'Wenceslau Braz', 2022, 'PR', 31, 'BR', -23.88811000, -49.79148000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q2006930'),
(15630, 'Wenceslau Guimarães', 2002, 'BA', 31, 'BR', -13.61819000, -39.58092000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q2006930'),
(15631, 'Westfália', 2001, 'RS', 31, 'BR', -29.42302000, -51.75617000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q961485'),
(15632, 'Witmarsum', 2014, 'SC', 31, 'BR', -26.95010000, -49.85590000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q961485'),
(15633, 'Xambioá', 2020, 'TO', 31, 'BR', -6.59413000, -48.43142000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q961485'),
(15634, 'Xambrê', 2022, 'PR', 31, 'BR', -23.73853000, -53.60400000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q961485'),
(15635, 'Xangri-lá', 2001, 'RS', 31, 'BR', -29.81049000, -50.09223000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q546227'),
(15636, 'Xanxerê', 2014, 'SC', 31, 'BR', -26.86668000, -52.41490000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q22063751'),
(15637, 'Xapuri', 2012, 'AC', 31, 'BR', -10.59663000, -68.64891000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1012531'),
(15638, 'Xavantina', 2014, 'SC', 31, 'BR', -26.98819000, -52.32905000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1012531'),
(15639, 'Xaxim', 2014, 'SC', 31, 'BR', -26.98685000, -52.51611000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1012531'),
(15640, 'Xexéu', 2006, 'PE', 31, 'BR', -8.86469000, -35.64275000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1012531'),
(15641, 'Xinguara', 2009, 'PA', 31, 'BR', -6.84897000, -49.25573000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1012531'),
(15642, 'Xique Xique', 2002, 'BA', 31, 'BR', -10.82294000, -42.72815000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q22031142'),
(15643, 'Xique-Xique', 2002, 'BA', 31, 'BR', -10.83393000, -42.56320000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1762114'),
(15644, 'Zabelê', 2005, 'PB', 31, 'BR', -8.07634000, -37.05635000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1762114'),
(15645, 'Zacarias', 2021, 'SP', 31, 'BR', -21.11651000, -50.06154000, '2019-10-05 22:35:40', '2019-10-05 22:35:40', 1, 'Q1762114'),
(15646, 'Zortéa', 2014, 'SC', 31, 'BR', -27.49740000, -51.50863000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1762114'),
(15647, 'Zé Doca', 2015, 'MA', 31, 'BR', -3.22589000, -46.05729000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1806488'),
(15648, 'Água Azul do Norte', 2009, 'PA', 31, 'BR', -6.63125000, -50.62146000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1806488'),
(15649, 'Água Boa', 2011, 'MT', 31, 'BR', -14.13584000, -52.49093000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1806488'),
(15650, 'Água Boa', 1998, 'MG', 31, 'BR', -18.06423000, -42.23027000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1806488'),
(15651, 'Água Branca', 2008, 'PI', 31, 'BR', -5.89222000, -42.63611000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q578114'),
(15652, 'Água Branca', 2007, 'AL', 31, 'BR', -9.26988000, -37.91917000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1808899'),
(15653, 'Água Branca', 2005, 'PB', 31, 'BR', -7.46957000, -37.66100000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1808899'),
(15654, 'Água Clara', 2010, 'MS', 31, 'BR', -19.89787000, -52.95089000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1796727'),
(15655, 'Água Comprida', 1998, 'MG', 31, 'BR', -19.99124000, -48.11035000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1796727'),
(15656, 'Água Doce', 2014, 'SC', 31, 'BR', -26.79693000, -51.63639000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1796727'),
(15657, 'Água Doce do Maranhão', 2015, 'MA', 31, 'BR', -2.91251000, -42.14125000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1796727'),
(15658, 'Água Doce do Norte', 2018, 'ES', 31, 'BR', -18.50646000, -40.99190000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1796727'),
(15659, 'Água Fria', 2002, 'BA', 31, 'BR', -11.74762000, -38.73497000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1796727'),
(15660, 'Água Fria de Goiás', 2000, 'GO', 31, 'BR', -14.96056000, -47.84963000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1796727'),
(15661, 'Água Limpa', 2000, 'GO', 31, 'BR', -18.07999000, -48.79951000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1796727'),
(15662, 'Água Nova', 2019, 'RN', 31, 'BR', -6.21312000, -38.30634000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1796727'),
(15663, 'Água Preta', 2006, 'PE', 31, 'BR', -8.70750000, -35.53056000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q1804038'),
(15664, 'Água Santa', 2001, 'RS', 31, 'BR', -28.18543000, -52.01286000, '2019-10-05 22:35:40', '2020-05-01 17:22:38', 1, 'Q1804038'),
(15665, 'Águas Belas', 2006, 'PE', 31, 'BR', -9.11139000, -37.12306000, '2019-10-05 22:35:40', '2020-05-01 17:22:37', 1, 'Q2013722'),
(15666, 'Águas Formosas', 1998, 'MG', 31, 'BR', -17.08222000, -40.93583000, '2019-10-05 22:35:40', '2020-05-01 17:22:36', 1, 'Q1756605'),
(15667, 'Águas Frias', 2014, 'SC', 31, 'BR', -26.85757000, -52.85062000, '2019-10-05 22:35:41', '2020-05-01 17:22:38', 1, 'Q1756605'),
(15668, 'Águas Lindas de Goiás', 2000, 'GO', 31, 'BR', -15.76613000, -48.28611000, '2019-10-05 22:35:41', '2020-05-01 17:22:36', 1, 'Q1756605'),
(15669, 'Águas Mornas', 2014, 'SC', 31, 'BR', -27.74227000, -48.92023000, '2019-10-05 22:35:41', '2020-05-01 17:22:38', 1, 'Q1756605'),
(15670, 'Águas Vermelhas', 1998, 'MG', 31, 'BR', -15.74722000, -41.46000000, '2019-10-05 22:35:41', '2020-05-01 17:22:36', 1, 'Q1756246'),
(15671, 'Águas da Prata', 2021, 'SP', 31, 'BR', -21.91623000, -46.68666000, '2019-10-05 22:35:41', '2020-05-01 17:22:38', 1, 'Q1756246'),
(15672, 'Águas de Chapecó', 2014, 'SC', 31, 'BR', -27.07229000, -52.96389000, '2019-10-05 22:35:41', '2020-05-01 17:22:38', 1, 'Q1756246'),
(15673, 'Águas de Lindóia', 2021, 'SP', 31, 'BR', -22.47639000, -46.63278000, '2019-10-05 22:35:41', '2020-05-01 17:22:38', 1, 'Q249869');

