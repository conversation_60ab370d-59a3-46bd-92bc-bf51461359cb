INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(59959, '<PERSON>', 1703, '78', 107, 'IT', 38.39190000, 16.08790000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18436729'),
(59960, '<PERSON>', 1773, '45', 107, 'IT', 44.95198000, 9.73773000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18436729'),
(59961, '<PERSON>', 1702, '21', 107, 'IT', 44.61094000, 8.24244000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18436729'),
(59962, '<PERSON>', 1705, '25', 107, 'IT', 45.57377000, 8.91374000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18436729'),
(59963, '<PERSON> <PERSON> a <PERSON><PERSON><PERSON>', 1669, '72', 107, '<PERSON>', 40.83261000, 14.34162000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18436729'),
(59964, '<PERSON> <PERSON> a <PERSON>ri', 1678, '62', 107, '<PERSON>', 41.40584000, 13.75979000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18436729'),
(59965, 'San Giorgio al Tagliamento-Pozzi', 1753, '34', 107, 'IT', 45.79639000, 12.96361000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18503595'),
(59966, 'San Giorgio del Sannio', 1669, '72', 107, 'IT', 41.06844000, 14.85322000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18503595'),
(59967, 'San Giorgio della Richinvelda', 1756, '36', 107, 'IT', 46.04778000, 12.86867000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18503595'),
(59968, 'San Giorgio delle Pertiche', 1753, '34', 107, 'IT', 45.54100000, 11.89401000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q18503595'),
(59969, 'San Giorgio di Livenza', 1753, '34', 107, 'IT', 45.65330000, 12.79692000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q1692101'),
(59970, 'San Giorgio di Lomellina', 1705, '25', 107, 'IT', 45.17486000, 8.79014000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q1692101'),
(59971, 'San Giorgio di Nogaro', 1756, '36', 107, 'IT', 45.82745000, 13.21088000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q1692101'),
(59972, 'San Giorgio di Pesaro', 1670, '57', 107, 'IT', 43.72248000, 12.97969000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q1692101'),
(59973, 'San Giorgio di Piano', 1773, '45', 107, 'IT', 44.64724000, 11.37446000, '2019-10-05 23:07:01', '2019-10-05 23:07:01', 1, 'Q1692101'),
(59974, 'San Giorgio in Bosco', 1753, '34', 107, 'IT', 45.58863000, 11.80736000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1692101'),
(59975, 'San Giorgio in Salici', 1753, '34', 107, 'IT', 45.42676000, 10.78906000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505514'),
(59976, 'San Giorgio la Molara', 1669, '72', 107, 'IT', 41.27667000, 14.93099000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505514'),
(59977, 'San Giorio', 1702, '21', 107, 'IT', 45.12752000, 7.17683000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505514'),
(59978, 'San Giovanni', 1756, '36', 107, 'IT', 46.02104000, 12.51556000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18449093'),
(59979, 'San Giovanni', 1709, '82', 107, 'IT', 37.73538000, 15.15887000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2559022'),
(59980, 'San Giovanni', 1768, '42', 107, 'IT', 44.39367000, 8.49701000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59981, 'San Giovanni A Piro', 1669, '72', 107, 'IT', 40.05150000, 15.44620000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59982, 'San Giovanni Bianco', 1705, '25', 107, 'IT', 45.87342000, 9.65420000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59983, 'San Giovanni Gemini', 1709, '82', 107, 'IT', 37.62785000, 13.64357000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59984, 'San Giovanni Ilarione', 1753, '34', 107, 'IT', 45.51927000, 11.23707000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59985, 'San Giovanni Incarico', 1678, '62', 107, 'IT', 41.50001000, 13.55864000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59986, 'San Giovanni Lipioni', 1679, '65', 107, 'IT', 41.84383000, 14.56271000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59987, 'San Giovanni Lupatoto', 1753, '34', 107, 'IT', 45.38193000, 11.04474000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59988, 'San Giovanni Rotondo', 1688, '75', 107, 'IT', 41.70643000, 15.72770000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59989, 'San Giovanni Suergiu', 1715, '88', 107, 'IT', 39.10955000, 8.52039000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59990, 'San Giovanni Valdarno', 1664, '52', 107, 'IT', 43.56757000, 11.52987000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462847'),
(59991, 'San Giovanni a Teduccio', 1669, '72', 107, 'IT', 40.83808000, 14.30606000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59992, 'San Giovanni al Natisone', 1756, '36', 107, 'IT', 45.97079000, 13.40182000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59993, 'San Giovanni d\'Asso', 1664, '52', 107, 'IT', 43.15334000, 11.58938000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59994, 'San Giovanni del Dosso', 1705, '25', 107, 'IT', 44.96627000, 11.08114000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59995, 'San Giovanni di Gerace', 1703, '78', 107, 'IT', 38.36508000, 16.27770000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59996, 'San Giovanni in Croce', 1705, '25', 107, 'IT', 45.07396000, 10.37315000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59997, 'San Giovanni in Fiore', 1703, '78', 107, 'IT', 39.25446000, 16.69699000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59998, 'San Giovanni in Galdo', 1695, '67', 107, 'IT', 41.59110000, 14.75202000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(59999, 'San Giovanni in Marignano', 1773, '45', 107, 'IT', 43.93989000, 12.71166000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(60000, 'San Giovanni in Persiceto', 1773, '45', 107, 'IT', 44.63838000, 11.18419000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(60001, 'San Giovanni la Punta', 1709, '82', 107, 'IT', 37.57690000, 15.09371000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1631800'),
(60002, 'San Giovanni-Patoni', 1678, '62', 107, 'IT', 41.77223000, 13.27510000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18453413'),
(60003, 'San Giovanni-San Bernardino', 1773, '45', 107, 'IT', 44.79297000, 9.60871000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18445405'),
(60004, 'San Giuliano Milanese', 1705, '25', 107, 'IT', 45.39402000, 9.29109000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18445405'),
(60005, 'San Giuliano Terme', 1664, '52', 107, 'IT', 43.76372000, 10.43856000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18445405'),
(60006, 'San Giuliano Vecchio', 1702, '21', 107, 'IT', 44.88902000, 8.76057000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947324'),
(60007, 'San Giuliano del Sannio', 1695, '67', 107, 'IT', 41.45752000, 14.64169000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947324'),
(60008, 'San Giuliano di Puglia', 1695, '67', 107, 'IT', 41.68796000, 14.96231000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947324'),
(60009, 'San Giuseppe', 1773, '45', 107, 'IT', 44.72202000, 12.21712000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q16268731'),
(60010, 'San Giuseppe', 1702, '21', 107, 'IT', 45.14701000, 7.04271000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18444840'),
(60011, 'San Giuseppe', 1669, '72', 107, 'IT', 40.84507000, 14.25170000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18444840'),
(60012, 'San Giuseppe Jato', 1709, '82', 107, 'IT', 37.97331000, 13.18889000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18444840'),
(60013, 'San Giuseppe Vesuviano', 1669, '72', 107, 'IT', 40.83560000, 14.50487000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18444840'),
(60014, 'San Giuseppe le Prata-Cotropagno', 1678, '62', 107, 'IT', 41.67788000, 13.39664000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18453415'),
(60015, 'San Giustino', 1683, '55', 107, 'IT', 43.54660000, 12.17528000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18453415'),
(60016, 'San Giustino Valdarno', 1664, '52', 107, 'IT', 43.55269000, 11.70497000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2054469'),
(60017, 'San Giusto Canavese', 1702, '21', 107, 'IT', 45.31535000, 7.81001000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2054469'),
(60018, 'San Godenzo', 1664, '52', 107, 'IT', 43.92504000, 11.61982000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2054469'),
(60019, 'San Gregorio', 1753, '34', 107, 'IT', 45.34286000, 11.29933000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505516'),
(60020, 'San Gregorio Magno', 1669, '72', 107, 'IT', 40.65992000, 15.39914000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505516'),
(60021, 'San Gregorio Matese', 1669, '72', 107, 'IT', 41.38556000, 14.37217000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505516'),
(60022, 'San Gregorio d\'Ippona', 1703, '78', 107, 'IT', 38.64528000, 16.10356000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505516'),
(60023, 'San Gregorio da Sassola', 1678, '62', 107, 'IT', 41.91881000, 12.87447000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505516'),
(60024, 'San Gregorio di Catania', 1709, '82', 107, 'IT', 37.56764000, 15.11120000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505516'),
(60025, 'San Gregorio nelle Alpi', 1753, '34', 107, 'IT', 46.10391000, 12.02670000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18505516'),
(60026, 'San Jacopo al Girone', 1664, '52', 107, 'IT', 43.76956000, 11.34032000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487352'),
(60027, 'San Lazzaro', 1773, '45', 107, 'IT', 44.47050000, 11.40851000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487352'),
(60028, 'San Leo', 1773, '45', 107, 'IT', 43.89637000, 12.34460000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487352'),
(60029, 'San Leonardo', 1756, '36', 107, 'IT', 46.11913000, 13.53085000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487352'),
(60030, 'San Leonardo', 1716, '23', 107, 'IT', 45.82366000, 7.18140000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18495921'),
(60031, 'San Leonardo in Passiria', 1725, '32', 107, 'IT', 46.81282000, 11.24577000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18495921'),
(60032, 'San Leone Mosè', 1709, '82', 107, 'IT', 37.26497000, 13.58434000, '2019-10-05 23:07:02', '2020-05-01 17:22:57', 1, 'Q18451900'),
(60033, 'San Leucio del Sannio-Cavuoti', 1669, '72', 107, 'IT', 41.07442000, 14.75744000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18436288'),
(60034, 'San Liberale', 1753, '34', 107, 'IT', 45.54778000, 12.34139000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947474'),
(60035, 'San Lorenzello', 1669, '72', 107, 'IT', 41.27602000, 14.54173000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947474'),
(60036, 'San Lorenzo', 1703, '78', 107, 'IT', 38.01097000, 15.83440000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947474'),
(60037, 'San Lorenzo', 1702, '21', 107, 'IT', 46.12720000, 8.20065000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18446281'),
(60038, 'San Lorenzo', 1669, '72', 107, 'IT', 40.75405000, 14.58974000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18437714'),
(60039, 'San Lorenzo', 1768, '42', 107, 'IT', 44.17302000, 8.24424000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462864'),
(60040, 'San Lorenzo Bellizzi', 1703, '78', 107, 'IT', 39.88867000, 16.33029000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462864'),
(60041, 'San Lorenzo Isontino', 1756, '36', 107, 'IT', 45.93020000, 13.52517000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462864'),
(60042, 'San Lorenzo Maggiore', 1669, '72', 107, 'IT', 41.24885000, 14.62438000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462864'),
(60043, 'San Lorenzo Nuovo', 1678, '62', 107, 'IT', 42.68626000, 11.90718000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462864'),
(60044, 'San Lorenzo al Mare', 1768, '42', 107, 'IT', 43.85378000, 7.96406000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462864'),
(60045, 'San Lorenzo del Vallo', 1703, '78', 107, 'IT', 39.66739000, 16.29866000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18462864'),
(60046, 'San Lorenzo di Rovetta', 1705, '25', 107, 'IT', 45.87971000, 9.97537000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18469674'),
(60047, 'San Lorenzo di Sebato', 1725, '32', 107, 'IT', 46.78514000, 11.90812000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18469674'),
(60048, 'San Lorenzo in Banale', 1725, '32', 107, 'IT', 46.07660000, 10.90847000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18469674'),
(60049, 'San Lorenzo in Campo', 1670, '57', 107, 'IT', 43.60149000, 12.94398000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18469674'),
(60050, 'San Luca', 1703, '78', 107, 'IT', 38.14672000, 16.06625000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18469674'),
(60051, 'San Lucido', 1703, '78', 107, 'IT', 39.31012000, 16.05317000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18469674'),
(60052, 'San Lupo', 1669, '72', 107, 'IT', 41.26148000, 14.63528000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18469674'),
(60053, 'San Mamete', 1705, '25', 107, 'IT', 46.02505000, 9.05364000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487815'),
(60054, 'San Mango Piemonte', 1669, '72', 107, 'IT', 40.70024000, 14.83919000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487815'),
(60055, 'San Mango d\'Aquino', 1703, '78', 107, 'IT', 39.06036000, 16.19224000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487815'),
(60056, 'San Mango sul Calore', 1669, '72', 107, 'IT', 40.95890000, 14.97251000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18487815'),
(60057, 'San Marcellino', 1669, '72', 107, 'IT', 40.99001000, 14.17583000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18436734'),
(60058, 'San Marcello', 1670, '57', 107, 'IT', 43.57526000, 13.20827000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18436734'),
(60059, 'San Marcello Pistoiese', 1664, '52', 107, 'IT', 44.05583000, 10.79366000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18436734'),
(60060, 'San Marco', 1669, '72', 107, 'IT', 40.26740000, 14.93897000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q473110'),
(60061, 'San Marco Argentano', 1703, '78', 107, 'IT', 39.55725000, 16.12452000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q473110'),
(60062, 'San Marco Evangelista', 1669, '72', 107, 'IT', 41.03702000, 14.33979000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q473110'),
(60063, 'San Marco d\'Alunzio', 1709, '82', 107, 'IT', 38.07261000, 14.70093000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q473110'),
(60064, 'San Marco dei Cavoti', 1669, '72', 107, 'IT', 41.30881000, 14.87924000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q473110'),
(60065, 'San Marco in Lamis', 1688, '75', 107, 'IT', 41.71210000, 15.63825000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q473110'),
(60066, 'San Marco la Catola', 1688, '75', 107, 'IT', 41.52483000, 15.00594000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q473110'),
(60067, 'San Martino', 1703, '78', 107, 'IT', 38.36157000, 15.97634000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q1158745'),
(60068, 'San Martino', 1773, '45', 107, 'IT', 44.77794000, 11.58751000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2394991'),
(60069, 'San Martino', 1725, '32', 107, 'IT', 46.81097000, 12.22684000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18510375'),
(60070, 'San Martino', 1753, '34', 107, 'IT', 45.90807000, 12.34132000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18502413'),
(60071, 'San Martino Alfieri', 1702, '21', 107, 'IT', 44.81795000, 8.10994000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18502413'),
(60072, 'San Martino Bassa', 1679, '65', 107, 'IT', 42.51827000, 14.13045000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18427498'),
(60073, 'San Martino Buon Albergo', 1753, '34', 107, 'IT', 45.42083000, 11.09562000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18427498'),
(60074, 'San Martino Canavese', 1702, '21', 107, 'IT', 45.39453000, 7.81622000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18427498'),
(60075, 'San Martino Dall\'Argine', 1705, '25', 107, 'IT', 45.09715000, 10.51766000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18427498'),
(60076, 'San Martino Sannita', 1669, '72', 107, 'IT', 41.06546000, 14.83492000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18427498'),
(60077, 'San Martino Siccomario', 1705, '25', 107, 'IT', 45.16340000, 9.14062000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18427498'),
(60078, 'San Martino Valle Caudina', 1669, '72', 107, 'IT', 41.02764000, 14.66446000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18427498'),
(60079, 'San Martino al Cimino', 1678, '62', 107, 'IT', 42.36978000, 12.12515000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2630393'),
(60080, 'San Martino al Tagliamento', 1756, '36', 107, 'IT', 46.01697000, 12.86989000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2630393'),
(60081, 'San Martino d\'Agri', 1706, '77', 107, 'IT', 40.23913000, 16.05216000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q2630393'),
(60082, 'San Martino dei Mulini', 1773, '45', 107, 'IT', 44.03182000, 12.46396000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18447973'),
(60083, 'San Martino del Lago', 1705, '25', 107, 'IT', 45.07243000, 10.31568000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q18447973'),
(60084, 'San Martino della Battaglia', 1705, '25', 107, 'IT', 45.43832000, 10.60053000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947651'),
(60085, 'San Martino di Finita', 1703, '78', 107, 'IT', 39.48988000, 16.10968000, '2019-10-05 23:07:02', '2019-10-05 23:07:02', 1, 'Q3947651'),
(60086, 'San Martino di Lupari', 1753, '34', 107, 'IT', 45.65128000, 11.86004000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947651'),
(60087, 'San Martino di Venezze', 1753, '34', 107, 'IT', 45.12532000, 11.87018000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947651'),
(60088, 'San Martino in Argine', 1773, '45', 107, 'IT', 44.58876000, 11.60650000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q16268732'),
(60089, 'San Martino in Badia', 1725, '32', 107, 'IT', 46.68153000, 11.89809000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q16268732'),
(60090, 'San Martino in Campo', 1683, '55', 107, 'IT', 43.03608000, 12.40265000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947654'),
(60091, 'San Martino in Freddana-Monsagrati', 1664, '52', 107, 'IT', 43.90921000, 10.43778000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18489075'),
(60092, 'San Martino in Passiria', 1725, '32', 107, 'IT', 46.78392000, 11.22727000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18489075'),
(60093, 'San Martino in Pensilis', 1695, '67', 107, 'IT', 41.87814000, 15.01824000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18489075'),
(60094, 'San Martino in Rio', 1773, '45', 107, 'IT', 44.73368000, 10.78490000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q30030029'),
(60095, 'San Martino in Strada', 1705, '25', 107, 'IT', 45.27497000, 9.52636000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q30030029'),
(60096, 'San Martino in Trignano', 1683, '55', 107, 'IT', 42.74125000, 12.66836000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60097, 'San Martino sulla Marrucina', 1679, '65', 107, 'IT', 42.22419000, 14.21577000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60098, 'San Marzano Oliveto', 1702, '21', 107, 'IT', 44.75445000, 8.29534000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60099, 'San Marzano di San Giuseppe', 1688, '75', 107, 'IT', 40.45455000, 17.50351000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60100, 'San Marzano sul Sarno', 1669, '72', 107, 'IT', 40.77801000, 14.58006000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60101, 'San Massimo', 1695, '67', 107, 'IT', 41.49292000, 14.41023000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60102, 'San Maurizio', 1702, '21', 107, 'IT', 45.21705000, 7.63052000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60103, 'San Maurizio D\'Opaglio', 1702, '21', 107, 'IT', 45.77299000, 8.39599000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18493829'),
(60104, 'San Mauro', 1664, '52', 107, 'IT', 43.79418000, 11.12499000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487359'),
(60105, 'San Mauro Castelverde', 1709, '82', 107, 'IT', 37.91478000, 14.18961000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487359'),
(60106, 'San Mauro Cilento', 1669, '72', 107, 'IT', 40.22628000, 15.04476000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487359'),
(60107, 'San Mauro Forte', 1706, '77', 107, 'IT', 40.48333000, 16.25155000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487359'),
(60108, 'San Mauro Marchesato', 1703, '78', 107, 'IT', 39.10559000, 16.92561000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487359'),
(60109, 'San Mauro Pascoli', 1773, '45', 107, 'IT', 44.10890000, 12.41953000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487359'),
(60110, 'San Mauro Torinese', 1702, '21', 107, 'IT', 45.10359000, 7.76803000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487359'),
(60111, 'San Mauro a Mare', 1773, '45', 107, 'IT', 44.16367000, 12.44631000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947670'),
(60112, 'San Mauro di Saline', 1753, '34', 107, 'IT', 45.56452000, 11.11234000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947670'),
(60113, 'San Mauro la Bruca', 1669, '72', 107, 'IT', 40.12246000, 15.29109000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947670'),
(60114, 'San Michele', 1725, '32', 107, 'IT', 46.45472000, 11.26178000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q17127503'),
(60115, 'San Michele', 1670, '57', 107, 'IT', 43.66218000, 12.99797000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18498861'),
(60116, 'San Michele', 1669, '72', 107, 'IT', 40.62023000, 14.55018000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18437723'),
(60117, 'San Michele All\'Adige', 1725, '32', 107, 'IT', 46.18967000, 11.13212000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18437723'),
(60118, 'San Michele Mondovì', 1702, '21', 107, 'IT', 44.37595000, 7.90829000, '2019-10-05 23:07:03', '2020-05-01 17:22:56', 1, 'Q18437723'),
(60119, 'San Michele Salentino', 1688, '75', 107, 'IT', 40.63163000, 17.63254000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18437723'),
(60120, 'San Michele Tiorre', 1773, '45', 107, 'IT', 44.68530000, 10.26317000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18443177'),
(60121, 'San Michele al Tagliamento', 1753, '34', 107, 'IT', 45.76435000, 12.99494000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18443177'),
(60122, 'San Michele dei Mucchietti', 1773, '45', 107, 'IT', 44.50847000, 10.74726000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947693'),
(60123, 'San Michele di Ganzaria', 1709, '82', 107, 'IT', 37.28042000, 14.42633000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947693'),
(60124, 'San Michele di Piave', 1753, '34', 107, 'IT', 45.79667000, 12.34634000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18502415'),
(60125, 'San Michele di Serino', 1669, '72', 107, 'IT', 40.87562000, 14.85464000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18502415'),
(60126, 'San Michele-San Giorgio', 1705, '25', 107, 'IT', 45.69594000, 9.05856000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487817'),
(60127, 'San Miniato', 1664, '52', 107, 'IT', 43.67954000, 10.84975000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487817'),
(60128, 'San Miniato Basso', 1664, '52', 107, 'IT', 43.69332000, 10.84119000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947707'),
(60129, 'San Nazario', 1753, '34', 107, 'IT', 45.83982000, 11.68893000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947707'),
(60130, 'San Nazzaro', 1669, '72', 107, 'IT', 41.05172000, 14.85724000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947707'),
(60131, 'San Nazzaro Sesia', 1702, '21', 107, 'IT', 45.43804000, 8.42498000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947707'),
(60132, 'San Nazzaro Val Cavargna', 1705, '25', 107, 'IT', 46.08939000, 9.12743000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947707'),
(60133, 'San Niccolò di Celle', 1683, '55', 107, 'IT', 43.01667000, 12.38333000, '2019-10-05 23:07:03', '2020-05-01 17:22:57', 1, 'Q3947734'),
(60134, 'San Nico', 1703, '78', 107, 'IT', 39.66906000, 16.43182000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18430253'),
(60135, 'San Nicola', 1715, '88', 107, 'IT', 40.61055000, 8.98841000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60136, 'San Nicola', 1703, '78', 107, 'IT', 38.21199000, 15.69381000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60137, 'San Nicola Arcella', 1703, '78', 107, 'IT', 39.84341000, 15.78634000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60138, 'San Nicola Baronia', 1669, '72', 107, 'IT', 41.05808000, 15.20006000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60139, 'San Nicola Manfredi', 1669, '72', 107, 'IT', 41.07510000, 14.82430000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60140, 'San Nicola da Crissa', 1703, '78', 107, 'IT', 38.66354000, 16.28592000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60141, 'San Nicola dell\'Alto', 1703, '78', 107, 'IT', 39.29094000, 16.97179000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60142, 'San Nicola la Strada', 1669, '72', 107, 'IT', 41.05237000, 14.33334000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18451143'),
(60143, 'San Nicolo\'Gerrei', 1715, '88', 107, 'IT', 39.49833000, 9.30611000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18448561'),
(60144, 'San Nicolò', 1703, '78', 107, 'IT', 38.63426000, 15.85147000, '2019-10-05 23:07:03', '2020-05-01 17:22:55', 1, 'Q18434171'),
(60145, 'San Nicolò', 1705, '25', 107, 'IT', 46.46299000, 10.40998000, '2019-10-05 23:07:03', '2020-05-01 17:22:56', 1, 'Q18494711'),
(60146, 'San Nicolò', 1773, '45', 107, 'IT', 45.05685000, 9.60540000, '2019-10-05 23:07:03', '2020-05-01 17:22:55', 1, 'Q18445420'),
(60147, 'San Nicolò Comelico', 1753, '34', 107, 'IT', 46.58253000, 12.52730000, '2019-10-05 23:07:03', '2020-05-01 17:22:57', 1, 'Q18496951'),
(60148, 'San Nicolò a Tordino', 1679, '65', 107, 'IT', 42.69648000, 13.79708000, '2019-10-05 23:07:03', '2020-05-01 17:22:54', 1, 'Q2905711'),
(60149, 'San Nicolò d\'Arcidano', 1715, '88', 107, 'IT', 39.68417000, 8.64361000, '2019-10-05 23:07:03', '2020-05-01 17:22:57', 1, 'Q2905711'),
(60150, 'San Pancrazio', 1773, '45', 107, 'IT', 44.35810000, 12.07852000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446242'),
(60151, 'San Pancrazio', 1725, '32', 107, 'IT', 46.58600000, 11.08571000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446242'),
(60152, 'San Pancrazio Salentino', 1688, '75', 107, 'IT', 40.41800000, 17.83419000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446242'),
(60153, 'San Panfilo d\'Ocre', 1679, '65', 107, 'IT', 42.28616000, 13.47518000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q49289062'),
(60154, 'San Paolo', 1705, '25', 107, 'IT', 45.37249000, 10.02397000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509711'),
(60155, 'San Paolo', 1725, '32', 107, 'IT', 46.47207000, 11.26021000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q1521361'),
(60156, 'San Paolo', 1688, '75', 107, 'IT', 41.12476000, 16.79258000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60157, 'San Paolo Albanese', 1706, '77', 107, 'IT', 40.03567000, 16.33498000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60158, 'San Paolo Bel Sito', 1669, '72', 107, 'IT', 40.91354000, 14.54862000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60159, 'San Paolo Solbrito', 1702, '21', 107, 'IT', 44.95065000, 7.97073000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60160, 'San Paolo d\'Argon', 1705, '25', 107, 'IT', 45.68838000, 9.80226000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60161, 'San Paolo di Civitate', 1688, '75', 107, 'IT', 41.73893000, 15.26080000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60162, 'San Paolo di Jesi', 1670, '57', 107, 'IT', 43.45456000, 13.17277000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60163, 'San Pellegrino Terme', 1705, '25', 107, 'IT', 45.83443000, 9.66753000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60164, 'San Pier Niceto', 1709, '82', 107, 'IT', 38.16050000, 15.34982000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60165, 'San Pier d\'Isonzo', 1756, '36', 107, 'IT', 45.84452000, 13.46764000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947760'),
(60166, 'San Pierino', 1664, '52', 107, 'IT', 43.71423000, 10.81374000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487365'),
(60167, 'San Piero Patti', 1709, '82', 107, 'IT', 38.05214000, 14.96816000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487365'),
(60168, 'San Piero a Sieve', 1664, '52', 107, 'IT', 43.96251000, 11.32426000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18487365'),
(60169, 'San Piero in Bagno', 1773, '45', 107, 'IT', 43.85793000, 11.97716000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18440301'),
(60170, 'San Pietro', 1725, '32', 107, 'IT', 46.64133000, 11.68277000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18440301'),
(60171, 'San Pietro', 1753, '34', 107, 'IT', 45.44209000, 11.12968000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18505526'),
(60172, 'San Pietro', 1669, '72', 107, 'IT', 40.82398000, 14.78476000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18435673'),
(60173, 'San Pietro Apostolo', 1703, '78', 107, 'IT', 39.00413000, 16.46783000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18435673'),
(60174, 'San Pietro Avellana', 1695, '67', 107, 'IT', 41.78886000, 14.18323000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18435673'),
(60175, 'San Pietro Belvedere', 1664, '52', 107, 'IT', 43.57011000, 10.66604000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q532721'),
(60176, 'San Pietro Capofiume', 1773, '45', 107, 'IT', 44.65040000, 11.64802000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947803'),
(60177, 'San Pietro Clarenza', 1709, '82', 107, 'IT', 37.56885000, 15.02289000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q3947803'),
(60178, 'San Pietro In Vincoli', 1773, '45', 107, 'IT', 44.30051000, 12.14556000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446250'),
(60179, 'San Pietro Infine', 1669, '72', 107, 'IT', 41.44552000, 13.96029000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446250'),
(60180, 'San Pietro Mosezzo', 1702, '21', 107, 'IT', 45.45455000, 8.54450000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446250'),
(60181, 'San Pietro Mussolino', 1753, '34', 107, 'IT', 45.58359000, 11.25930000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446250'),
(60182, 'San Pietro Val Lemina', 1702, '21', 107, 'IT', 44.90654000, 7.31092000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18446250'),
(60183, 'San Pietro Valdastico', 1753, '34', 107, 'IT', 45.88670000, 11.36159000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18506872'),
(60184, 'San Pietro Vernotico', 1688, '75', 107, 'IT', 40.48890000, 17.99752000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18506872'),
(60185, 'San Pietro Viminario', 1753, '34', 107, 'IT', 45.24501000, 11.82049000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18506872'),
(60186, 'San Pietro a Maida', 1703, '78', 107, 'IT', 38.84731000, 16.34116000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18506872'),
(60187, 'San Pietro a Patierno', 1669, '72', 107, 'IT', 40.88566000, 14.29008000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18506872'),
(60188, 'San Pietro al Natisone', 1756, '36', 107, 'IT', 46.11444000, 13.48296000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18506872'),
(60189, 'San Pietro al Tanagro', 1669, '72', 107, 'IT', 40.45488000, 15.48692000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18506872'),
(60190, 'San Pietro d\'Olba', 1768, '42', 107, 'IT', 44.48762000, 8.58951000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60191, 'San Pietro di Cadore', 1753, '34', 107, 'IT', 46.57190000, 12.58690000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60192, 'San Pietro di Caridà', 1703, '78', 107, 'IT', 38.52368000, 16.13530000, '2019-10-05 23:07:03', '2020-05-01 17:22:55', 1, 'Q18509715'),
(60193, 'San Pietro di Feletto', 1753, '34', 107, 'IT', 45.91391000, 12.25101000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60194, 'San Pietro di Morubio', 1753, '34', 107, 'IT', 45.24248000, 11.22700000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60195, 'San Pietro in Amantea', 1703, '78', 107, 'IT', 39.13658000, 16.11243000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60196, 'San Pietro in Cariano', 1753, '34', 107, 'IT', 45.51751000, 10.88624000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60197, 'San Pietro in Casale', 1773, '45', 107, 'IT', 44.70079000, 11.40492000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60198, 'San Pietro in Cerro', 1773, '45', 107, 'IT', 45.02137000, 9.94987000, '2019-10-05 23:07:03', '2019-10-05 23:07:03', 1, 'Q18509715'),
(60199, 'San Pietro in Gu', 1753, '34', 107, 'IT', 45.60879000, 11.67355000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18509715'),
(60200, 'San Pietro in Guarano', 1703, '78', 107, 'IT', 39.34164000, 16.31304000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18509715'),
(60201, 'San Pietro in Lama', 1688, '75', 107, 'IT', 40.30711000, 18.12787000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18509715'),
(60202, 'San Pietro in Volta', 1753, '34', 107, 'IT', 45.31542000, 12.31518000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q2184285'),
(60203, 'San Pio delle Camere', 1679, '65', 107, 'IT', 42.28423000, 13.65570000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q2184285'),
(60204, 'San Polo', 1773, '45', 107, 'IT', 44.97868000, 9.74070000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18445429'),
(60205, 'San Polo d\'Enza', 1773, '45', 107, 'IT', 44.62690000, 10.42667000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18445429'),
(60206, 'San Polo dei Cavalieri', 1678, '62', 107, 'IT', 42.01037000, 12.83989000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18445429'),
(60207, 'San Polo di Piave', 1753, '34', 107, 'IT', 45.78969000, 12.39287000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18445429'),
(60208, 'San Polomatese', 1695, '67', 107, 'IT', 41.45932000, 14.49343000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60209, 'San Ponso', 1702, '21', 107, 'IT', 45.35101000, 7.67111000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60210, 'San Possidonio', 1773, '45', 107, 'IT', 44.87402000, 10.98087000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60211, 'San Potito Sannitico', 1669, '72', 107, 'IT', 41.33698000, 14.39185000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60212, 'San Potito Ultra', 1669, '72', 107, 'IT', 40.92796000, 14.87124000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60213, 'San Prisco', 1669, '72', 107, 'IT', 41.08592000, 14.27675000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60214, 'San Procopio', 1703, '78', 107, 'IT', 38.28214000, 15.89083000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60215, 'San Prospero', 1773, '45', 107, 'IT', 44.78967000, 11.02210000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18499700'),
(60216, 'San Quirico', 1753, '34', 107, 'IT', 45.67685000, 11.27260000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18506875'),
(60217, 'San Quirico d\'Orcia', 1664, '52', 107, 'IT', 43.05782000, 11.60525000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18506875'),
(60218, 'San Quirino', 1756, '36', 107, 'IT', 46.03411000, 12.67846000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18506875'),
(60219, 'San Raffaele Cimena', 1702, '21', 107, 'IT', 45.14665000, 7.84932000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18506875'),
(60220, 'San Remo', 1768, '42', 107, 'IT', 43.81725000, 7.77720000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18506875'),
(60221, 'San Roberto', 1703, '78', 107, 'IT', 38.21114000, 15.73594000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18506875'),
(60222, 'San Rocco', 1679, '65', 107, 'IT', 42.24294000, 14.12891000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18425642'),
(60223, 'San Rocco', 1702, '21', 107, 'IT', 44.39653000, 7.47230000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18440722'),
(60224, 'San Rocco', 1664, '52', 107, 'IT', 43.82316000, 10.86121000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18491194'),
(60225, 'San Rocco', 1705, '25', 107, 'IT', 45.00945000, 9.16191000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18493468'),
(60226, 'San Rocco al Porto', 1705, '25', 107, 'IT', 45.08197000, 9.69717000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18493468'),
(60227, 'San Romano', 1664, '52', 107, 'IT', 43.68922000, 10.76900000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q2431663'),
(60228, 'San Romano in Garfagnana', 1664, '52', 107, 'IT', 44.16945000, 10.34690000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q2431663'),
(60229, 'San Rufo', 1669, '72', 107, 'IT', 40.43451000, 15.46366000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q2431663'),
(60230, 'San Salvatore', 1768, '42', 107, 'IT', 44.33098000, 9.35479000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18459019'),
(60231, 'San Salvatore Monferrato', 1702, '21', 107, 'IT', 44.99489000, 8.56639000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18459019'),
(60232, 'San Salvatore Telesino', 1669, '72', 107, 'IT', 41.23537000, 14.49832000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18459019'),
(60233, 'San Salvatore di Fitalia', 1709, '82', 107, 'IT', 38.06841000, 14.77799000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18459019'),
(60234, 'San Salvo', 1679, '65', 107, 'IT', 42.04413000, 14.73335000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18459019'),
(60235, 'San Sebastiano', 1705, '25', 107, 'IT', 45.65192000, 10.25681000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18477025'),
(60236, 'San Sebastiano Curone', 1702, '21', 107, 'IT', 44.78633000, 9.06446000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18477025'),
(60237, 'San Sebastiano al Vesuvio', 1669, '72', 107, 'IT', 40.84344000, 14.36428000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18477025'),
(60238, 'San Sebastiano da Po', 1702, '21', 107, 'IT', 45.16755000, 7.95723000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18477025'),
(60239, 'San Secondo', 1683, '55', 107, 'IT', 43.40468000, 12.23389000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60240, 'San Secondo Parmense', 1773, '45', 107, 'IT', 44.92218000, 10.23038000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60241, 'San Secondo di Pinerolo', 1702, '21', 107, 'IT', 44.86644000, 7.29842000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60242, 'San Severino Lucano', 1706, '77', 107, 'IT', 40.02021000, 16.13860000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60243, 'San Severino Marche', 1670, '57', 107, 'IT', 43.23028000, 13.17990000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60244, 'San Severo', 1688, '75', 107, 'IT', 41.68564000, 15.38148000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60245, 'San Siro', 1705, '25', 107, 'IT', 46.06573000, 9.26877000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60246, 'San Sossio Baronia', 1669, '72', 107, 'IT', 41.07081000, 15.20122000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60247, 'San Sostene', 1703, '78', 107, 'IT', 38.63733000, 16.48751000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60248, 'San Sosti', 1703, '78', 107, 'IT', 39.65879000, 16.02958000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60249, 'San Sperate', 1715, '88', 107, 'IT', 39.35758000, 9.00814000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60250, 'San Stino di Livenza', 1753, '34', 107, 'IT', 45.72559000, 12.68971000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60251, 'San Tammaro', 1669, '72', 107, 'IT', 41.07588000, 14.23105000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60252, 'San Teodoro', 1709, '82', 107, 'IT', 37.84797000, 14.69878000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60253, 'San Teodoro', 1715, '88', 107, 'IT', 40.77354000, 9.66929000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q7415310'),
(60254, 'San Terenziano', 1683, '55', 107, 'IT', 42.86731000, 12.47319000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18493866'),
(60255, 'San Terenzo', 1768, '42', 107, 'IT', 44.08543000, 9.89662000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q958170'),
(60256, 'San Tommaso Tre Archi', 1670, '57', 107, 'IT', 43.22771000, 13.77503000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18496941'),
(60257, 'San Valentino', 1756, '36', 107, 'IT', 45.79039000, 13.41236000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18452509'),
(60258, 'San Valentino Torio', 1669, '72', 107, 'IT', 40.79109000, 14.60333000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18452509'),
(60259, 'San Valentino in Abruzzo Citeriore', 1679, '65', 107, 'IT', 42.23304000, 13.98561000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18452509'),
(60260, 'San Venanzio', 1773, '45', 107, 'IT', 44.74517000, 11.43727000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438681'),
(60261, 'San Venanzo', 1683, '55', 107, 'IT', 42.86886000, 12.26853000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438681'),
(60262, 'San Vendemiano', 1753, '34', 107, 'IT', 45.88732000, 12.35164000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438681'),
(60263, 'San Vendemiano-Fossamerlo', 1753, '34', 107, 'IT', 45.89180000, 12.32992000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18502438'),
(60264, 'San Vero Milis', 1715, '88', 107, 'IT', 40.01377000, 8.59833000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18502438'),
(60265, 'San Vigilio', 1705, '25', 107, 'IT', 45.60808000, 10.19442000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q3948026'),
(60266, 'San Vigilio', 1725, '32', 107, 'IT', 46.69884000, 11.93104000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q460345'),
(60267, 'San Vincenzo', 1664, '52', 107, 'IT', 43.09061000, 10.54246000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q460345'),
(60268, 'San Vincenzo', 1773, '45', 107, 'IT', 44.73878000, 11.43668000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438682'),
(60269, 'San Vincenzo Valle Roveto', 1679, '65', 107, 'IT', 41.84452000, 13.53566000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438682'),
(60270, 'San Vincenzo la Costa', 1703, '78', 107, 'IT', 39.36544000, 16.15110000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438682'),
(60271, 'San Vitaliano', 1669, '72', 107, 'IT', 40.92442000, 14.47463000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438682'),
(60272, 'San Vito', 1715, '88', 107, 'IT', 39.44142000, 9.54065000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18438682'),
(60273, 'San Vito', 1753, '34', 107, 'IT', 45.75514000, 11.91553000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18502440'),
(60274, 'San Vito', 1773, '45', 107, 'IT', 44.55822000, 10.97432000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18441665'),
(60275, 'San Vito Chietino', 1679, '65', 107, 'IT', 42.29014000, 14.44270000, '2019-10-05 23:07:04', '2019-10-05 23:07:04', 1, 'Q18441665'),
(60276, 'San Vito Lo Capo', 1709, '82', 107, 'IT', 38.17395000, 12.73599000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18441665'),
(60277, 'San Vito Romano', 1678, '62', 107, 'IT', 41.88001000, 12.97793000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18441665'),
(60278, 'San Vito Sullo Ionio', 1703, '78', 107, 'IT', 38.70749000, 16.40862000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18441665'),
(60279, 'San Vito al Mantico', 1753, '34', 107, 'IT', 45.47163000, 10.89243000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948037'),
(60280, 'San Vito al Tagliamento', 1756, '36', 107, 'IT', 45.91680000, 12.85945000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948037'),
(60281, 'San Vito al Torre', 1756, '36', 107, 'IT', 45.89587000, 13.37588000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948037'),
(60282, 'San Vito dei Normanni', 1688, '75', 107, 'IT', 40.65642000, 17.70814000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948037'),
(60283, 'San Vito di Fagagna', 1756, '36', 107, 'IT', 46.09103000, 13.06569000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948037'),
(60284, 'San Vito di Leguzzano', 1753, '34', 107, 'IT', 45.68494000, 11.38872000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948037'),
(60285, 'San Vito-Cerreto', 1664, '52', 107, 'IT', 44.02300000, 10.17212000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18489836'),
(60286, 'San Vittore', 1705, '25', 107, 'IT', 46.30799000, 9.38310000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18494716'),
(60287, 'San Vittore Olona', 1705, '25', 107, 'IT', 45.58577000, 8.94134000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18494716'),
(60288, 'San Vittore del Lazio', 1678, '62', 107, 'IT', 41.46212000, 13.93367000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18494716'),
(60289, 'San Vittoria in Matenano', 1670, '57', 107, 'IT', 43.01975000, 13.49618000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18496943'),
(60290, 'San Zeno', 1753, '34', 107, 'IT', 45.63381000, 10.73046000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505538'),
(60291, 'San Zeno Naviglio', 1705, '25', 107, 'IT', 45.49258000, 10.21847000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505538'),
(60292, 'San Zeno di Montagna', 1753, '34', 107, 'IT', 45.63749000, 10.73218000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505538'),
(60293, 'San Zeno-San Giuseppe', 1753, '34', 107, 'IT', 45.75727000, 11.76208000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18506882'),
(60294, 'San Zenone', 1705, '25', 107, 'IT', 45.64169000, 10.14685000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18477072'),
(60295, 'San Zenone al Lambro', 1705, '25', 107, 'IT', 45.32681000, 9.35598000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18477072'),
(60296, 'San Zenone al Po', 1705, '25', 107, 'IT', 45.10856000, 9.36176000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18477072'),
(60297, 'San Zenone degli Ezzelini', 1753, '34', 107, 'IT', 45.78040000, 11.83720000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18477072'),
(60298, 'Sanarica', 1688, '75', 107, 'IT', 40.08908000, 18.34803000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18477072'),
(60299, 'Sandigliano', 1702, '21', 107, 'IT', 45.52204000, 8.07660000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18477072'),
(60300, 'Sandrigo', 1753, '34', 107, 'IT', 45.66083000, 11.58921000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18477072'),
(60301, 'Sandrà', 1753, '34', 107, 'IT', 45.46067000, 10.78686000, '2019-10-05 23:07:05', '2020-05-01 17:22:57', 1, 'Q18505539'),
(60302, 'Sanfront', 1702, '21', 107, 'IT', 44.64698000, 7.32243000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60303, 'Sanfrè', 1702, '21', 107, 'IT', 44.75180000, 7.80289000, '2019-10-05 23:07:05', '2020-05-01 17:22:56', 1, 'Q18505539'),
(60304, 'Sangano', 1702, '21', 107, 'IT', 45.02508000, 7.44987000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60305, 'Sangiano', 1705, '25', 107, 'IT', 45.87508000, 8.63333000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60306, 'Sangineto', 1703, '78', 107, 'IT', 39.60553000, 15.91481000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60307, 'Sanguinetto', 1753, '34', 107, 'IT', 45.18567000, 11.14412000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60308, 'Sanluri', 1715, '88', 107, 'IT', 39.56176000, 8.89969000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60309, 'Sannazzaro de\' Burgondi', 1705, '25', 107, 'IT', 45.10226000, 8.90635000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60310, 'Sannicandro Garganico', 1688, '75', 107, 'IT', 41.83844000, 15.56535000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60311, 'Sannicandro di Bari', 1688, '75', 107, 'IT', 41.00047000, 16.79714000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60312, 'Sannicola', 1688, '75', 107, 'IT', 40.09244000, 18.06765000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60313, 'Sansepolcro', 1664, '52', 107, 'IT', 43.57258000, 12.13858000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60314, 'Sant\'Agapito', 1695, '67', 107, 'IT', 41.54372000, 14.22204000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60315, 'Sant\'Agata Bolognese', 1773, '45', 107, 'IT', 44.66016000, 11.13292000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60316, 'Sant\'Agata Feltria', 1773, '45', 107, 'IT', 43.86282000, 12.20690000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60317, 'Sant\'Agata Fossili', 1702, '21', 107, 'IT', 44.78475000, 8.92115000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60318, 'Sant\'Agata Li Battiati', 1709, '82', 107, 'IT', 37.55745000, 15.07999000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18505539'),
(60319, 'Sant\'Agata Martesana', 1705, '25', 107, 'IT', 45.52220000, 9.38382000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948636'),
(60320, 'Sant\'Agata de\'Goti', 1669, '72', 107, 'IT', 41.08932000, 14.49743000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948636'),
(60321, 'Sant\'Agata del Bianco', 1703, '78', 107, 'IT', 38.09242000, 16.08251000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948636'),
(60322, 'Sant\'Agata di Esaro', 1703, '78', 107, 'IT', 39.62235000, 15.98295000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948636'),
(60323, 'Sant\'Agata di Militello', 1709, '82', 107, 'IT', 38.06838000, 14.63600000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948636'),
(60324, 'Sant\'Agata di Puglia', 1688, '75', 107, 'IT', 41.15127000, 15.37968000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q3948636'),
(60325, 'Sant\'Agata sui Due Golfi', 1669, '72', 107, 'IT', 40.60565000, 14.37402000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q1860399'),
(60326, 'Sant\'Agata sul Santerno', 1707, 'RA', 107, 'IT', 44.44351000, 11.86112000, '2019-10-05 23:07:05', '2022-05-08 18:48:58', 1, 'Q1860399'),
(60327, 'Sant\'Agnello', 1669, '72', 107, 'IT', 40.62942000, 14.39957000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q1860399'),
(60328, 'Sant\'Agostino', 1773, '45', 107, 'IT', 44.79218000, 11.38522000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q95139'),
(60329, 'Sant\'Albano Stura', 1702, '21', 107, 'IT', 44.50759000, 7.72282000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q95139'),
(60330, 'Sant\'Alberto', 1773, '45', 107, 'IT', 44.53822000, 12.16191000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18446274'),
(60331, 'Sant\'Alberto', 1753, '34', 107, 'IT', 45.61306000, 12.13472000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18502472'),
(60332, 'Sant\'Albino', 1664, '52', 107, 'IT', 43.07098000, 11.80684000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60333, 'Sant\'Alessio Con Vialone', 1705, '25', 107, 'IT', 45.22237000, 9.22605000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60334, 'Sant\'Alessio Siculo', 1709, '82', 107, 'IT', 37.92516000, 15.34968000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60335, 'Sant\'Alessio in Aspromonte', 1703, '78', 107, 'IT', 38.17220000, 15.75730000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60336, 'Sant\'Alfio', 1709, '82', 107, 'IT', 37.74393000, 15.13952000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60337, 'Sant\'Ambrogio di Torino', 1702, '21', 107, 'IT', 45.09622000, 7.36600000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60338, 'Sant\'Ambrogio di Valpollicella', 1753, '34', 107, 'IT', 45.52089000, 10.83618000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60339, 'Sant\'Ambrogio sul Garigliano', 1678, '62', 107, 'IT', 41.39423000, 13.86891000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60340, 'Sant\'Anastasia', 1669, '72', 107, 'IT', 40.86842000, 14.40196000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60341, 'Sant\'Anatolia di Narco', 1683, '55', 107, 'IT', 42.73319000, 12.83576000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600206'),
(60342, 'Sant\'Andrea', 1773, '45', 107, 'IT', 44.03328000, 12.41893000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18448018'),
(60343, 'Sant\'Andrea', 1753, '34', 107, 'IT', 45.64664000, 11.89639000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18502481'),
(60344, 'Sant\'Andrea Apostolo dello Ionio', 1703, '78', 107, 'IT', 38.62165000, 16.53048000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18502481'),
(60345, 'Sant\'Andrea Frius', 1715, '88', 107, 'IT', 39.47917000, 9.17000000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18502481'),
(60346, 'Sant\'Andrea Ionio Marina', 1703, '78', 107, 'IT', 38.62001000, 16.54927000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18429461'),
(60347, 'Sant\'Andrea del Garigliano', 1678, '62', 107, 'IT', 41.36868000, 13.84198000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18429461'),
(60348, 'Sant\'Andrea di Conza', 1669, '72', 107, 'IT', 40.84482000, 15.36965000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18429461'),
(60349, 'Sant\'Andrea in Casale', 1773, '45', 107, 'IT', 43.93333000, 12.65000000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18448015'),
(60350, 'Sant\'Andrea-Pizzone-Ciamprisco', 1669, '72', 107, 'IT', 41.15000000, 14.03333000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18436759'),
(60351, 'Sant\'Angelo', 1753, '34', 107, 'IT', 45.52031000, 12.01862000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18503620'),
(60352, 'Sant\'Angelo', 1669, '72', 107, 'IT', 40.69841000, 13.89316000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60353, 'Sant\'Angelo A Cupolo', 1669, '72', 107, 'IT', 41.06912000, 14.80374000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60354, 'Sant\'Angelo A Fasanella', 1669, '72', 107, 'IT', 40.45724000, 15.34091000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60355, 'Sant\'Angelo A Scala', 1669, '72', 107, 'IT', 40.97465000, 14.74041000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60356, 'Sant\'Angelo All\'Esca', 1669, '72', 107, 'IT', 41.00662000, 14.99305000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60357, 'Sant\'Angelo Limosano', 1695, '67', 107, 'IT', 41.69266000, 14.60346000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60358, 'Sant\'Angelo Lodigiano', 1705, '25', 107, 'IT', 45.23526000, 9.40651000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60359, 'Sant\'Angelo Lomellina', 1705, '25', 107, 'IT', 45.24663000, 8.64317000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60360, 'Sant\'Angelo Muxaro', 1709, '82', 107, 'IT', 37.48014000, 13.54554000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60361, 'Sant\'Angelo Romano', 1678, '62', 107, 'IT', 42.03570000, 12.71342000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60362, 'Sant\'Angelo d\'Alife', 1669, '72', 107, 'IT', 41.36119000, 14.26125000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60363, 'Sant\'Angelo dei Lombardi', 1669, '72', 107, 'IT', 40.92937000, 15.17535000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60364, 'Sant\'Angelo del Pesco', 1695, '67', 107, 'IT', 41.88193000, 14.25354000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60365, 'Sant\'Angelo di Brolo', 1709, '82', 107, 'IT', 38.11518000, 14.88403000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60366, 'Sant\'Angelo di Piove di Sacco', 1753, '34', 107, 'IT', 45.31735000, 12.00941000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60367, 'Sant\'Angelo in Formis', 1669, '72', 107, 'IT', 41.11667000, 14.25000000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437087'),
(60368, 'Sant\'Angelo in Lizzola', 1670, '57', 107, 'IT', 43.82696000, 12.80076000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q207583'),
(60369, 'Sant\'Angelo in Pontano', 1670, '57', 107, 'IT', 43.09780000, 13.39690000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q207583'),
(60370, 'Sant\'Angelo in Vado', 1670, '57', 107, 'IT', 43.66526000, 12.41757000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q207583'),
(60371, 'Sant\'Angelo in Villa-Giglio', 1678, '62', 107, 'IT', 41.66217000, 13.42871000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18453446'),
(60372, 'Sant\'Angelo le Fratte', 1706, '77', 107, 'IT', 40.54490000, 15.56156000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18453446'),
(60373, 'Sant\'Anna', 1753, '34', 107, 'IT', 45.16610000, 12.27355000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18503622'),
(60374, 'Sant\'Anna Arresi', 1715, '88', 107, 'IT', 39.00618000, 8.64236000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18503622'),
(60375, 'Sant\'Anna d\'Alfaedo', 1753, '34', 107, 'IT', 45.62719000, 10.95168000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18503622'),
(60376, 'Sant\'Antimo', 1669, '72', 107, 'IT', 40.94223000, 14.23476000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18503622'),
(60377, 'Sant\'Antioco', 1715, '88', 107, 'IT', 39.07017000, 8.45243000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18503622'),
(60378, 'Sant\'Antonino di Susa', 1702, '21', 107, 'IT', 45.10763000, 7.27333000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18503622'),
(60379, 'Sant\'Antonio', 1705, '25', 107, 'IT', 46.46110000, 10.41738000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18494732'),
(60380, 'Sant\'Antonio', 1669, '72', 107, 'IT', 40.63626000, 14.89845000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18437759'),
(60381, 'Sant\'Antonio', 1702, '21', 107, 'IT', 44.75887000, 8.06996000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18440788'),
(60382, 'Sant\'Antonio', 1773, '45', 107, 'IT', 44.36586000, 10.83646000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18441677'),
(60383, 'Sant\'Antonio Abate', 1669, '72', 107, 'IT', 40.72152000, 14.54021000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18441677'),
(60384, 'Sant\'Antonio di Gallura', 1715, '88', 107, 'IT', 40.99146000, 9.30153000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18441677'),
(60385, 'Sant\'Apollinare', 1678, '62', 107, 'IT', 41.40176000, 13.82992000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18441677'),
(60386, 'Sant\'Apollinare', 1753, '34', 107, 'IT', 45.04115000, 11.82533000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60387, 'Sant\'Arcangelo', 1706, '77', 107, 'IT', 40.24852000, 16.27046000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60388, 'Sant\'Arcangelo Trimonte', 1669, '72', 107, 'IT', 41.16901000, 14.93883000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60389, 'Sant\'Arpino', 1669, '72', 107, 'IT', 40.95716000, 14.25075000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60390, 'Sant\'Arsenio', 1669, '72', 107, 'IT', 40.47109000, 15.48401000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60391, 'Sant\'Egidio alla Vibrata', 1679, '65', 107, 'IT', 42.81705000, 13.72164000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60392, 'Sant\'Egidio del Monte Albino', 1669, '72', 107, 'IT', 40.73871000, 14.59454000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60393, 'Sant\'Elena', 1753, '34', 107, 'IT', 45.18744000, 11.71169000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18500348'),
(60394, 'Sant\'Elena Irpina', 1669, '72', 107, 'IT', 41.03741000, 14.88931000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600234'),
(60395, 'Sant\'Elena Sannita', 1695, '67', 107, 'IT', 41.57525000, 14.47070000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q16600234'),
(60396, 'Sant\'Elia', 1703, '78', 107, 'IT', 38.95788000, 16.58521000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18429462'),
(60397, 'Sant\'Elia Fiumerapido', 1678, '62', 107, 'IT', 41.53304000, 13.86268000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18429462'),
(60398, 'Sant\'Elia a Pianisi', 1695, '67', 107, 'IT', 41.62043000, 14.87523000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18429462'),
(60399, 'Sant\'Elpidio a Mare', 1670, '57', 107, 'IT', 43.23024000, 13.68819000, '2019-10-05 23:07:05', '2019-10-05 23:07:05', 1, 'Q18429462'),
(60400, 'Sant\'Eufemia a Maiella', 1679, '65', 107, 'IT', 42.12613000, 14.02651000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60401, 'Sant\'Eufemia d\'Aspromonte', 1703, '78', 107, 'IT', 38.26314000, 15.85669000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60402, 'Sant\'Eusanio Forconese', 1679, '65', 107, 'IT', 42.28951000, 13.52425000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60403, 'Sant\'Eusanio del Sangro', 1679, '65', 107, 'IT', 42.16908000, 14.32776000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60404, 'Sant\'Ilario d\'Enza', 1773, '45', 107, 'IT', 44.75890000, 10.44737000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60405, 'Sant\'Ilario dello Ionio', 1703, '78', 107, 'IT', 38.21914000, 16.19517000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60406, 'Sant\'Ippolito', 1670, '57', 107, 'IT', 43.67972000, 12.87581000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60407, 'Sant\'Omero', 1679, '65', 107, 'IT', 42.79011000, 13.78906000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60408, 'Sant\'Omobono Terme', 1705, '25', 107, 'IT', 45.80838000, 9.53625000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60409, 'Sant\'Onofrio', 1703, '78', 107, 'IT', 38.69752000, 16.14755000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60410, 'Sant\'Oreste', 1678, '62', 107, 'IT', 42.23298000, 12.51507000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429462'),
(60411, 'Sant\'Orsola', 1725, '32', 107, 'IT', 46.10901000, 11.30201000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18482837'),
(60412, 'Sant\'Orsola Terme', 1725, '32', 107, 'IT', 46.10881000, 11.30238000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18482837'),
(60413, 'Santa Brigida', 1705, '25', 107, 'IT', 45.98489000, 9.62115000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18482837'),
(60414, 'Santa Caterina Albanese', 1703, '78', 107, 'IT', 39.58590000, 16.07039000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18482837'),
(60415, 'Santa Caterina Villarmosa', 1709, '82', 107, 'IT', 37.59034000, 14.03554000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18482837'),
(60416, 'Santa Caterina dello Ionio', 1703, '78', 107, 'IT', 38.53324000, 16.52176000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18482837'),
(60417, 'Santa Caterina dello Ionio Marina', 1703, '78', 107, 'IT', 38.52736000, 16.57071000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429450'),
(60418, 'Santa Cesarea Terme', 1688, '75', 107, 'IT', 40.03607000, 18.45542000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429450'),
(60419, 'Santa Corinna', 1705, '25', 107, 'IT', 45.34018000, 9.08878000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18491736'),
(60420, 'Santa Cristina', 1753, '34', 107, 'IT', 45.64350000, 12.13903000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18502449'),
(60421, 'Santa Cristina', 1705, '25', 107, 'IT', 45.15756000, 9.39976000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18502449'),
(60422, 'Santa Cristina Gela', 1709, '82', 107, 'IT', 37.98514000, 13.32747000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18502449'),
(60423, 'Santa Cristina Valgardena', 1725, '32', 107, 'IT', 46.56299000, 11.73216000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18502449'),
(60424, 'Santa Cristina d\'Aspromonte', 1703, '78', 107, 'IT', 38.25480000, 15.96996000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18502449'),
(60425, 'Santa Croce', 1756, '36', 107, 'IT', 45.73472000, 13.69278000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q3948975'),
(60426, 'Santa Croce Camerina', 1709, '82', 107, 'IT', 36.82842000, 14.52538000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q3948975'),
(60427, 'Santa Croce Scuole', 1773, '45', 107, 'IT', 44.76281000, 10.84944000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18441669'),
(60428, 'Santa Croce del Sannio', 1669, '72', 107, 'IT', 41.38793000, 14.73229000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18441669'),
(60429, 'Santa Croce di Magliano', 1695, '67', 107, 'IT', 41.71223000, 14.98732000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18441669'),
(60430, 'Santa Croce sull\'Arno', 1664, '52', 107, 'IT', 43.71709000, 10.77242000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18441669'),
(60431, 'Santa Domenica', 1703, '78', 107, 'IT', 38.66220000, 15.86289000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18434193'),
(60432, 'Santa Domenica Talao', 1703, '78', 107, 'IT', 39.81875000, 15.85380000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18434193'),
(60433, 'Santa Domenica Vittoria', 1709, '82', 107, 'IT', 37.91624000, 14.96288000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18434193'),
(60434, 'Santa Elisabetta', 1709, '82', 107, 'IT', 37.43171000, 13.55386000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18434193'),
(60435, 'Santa Eufemia Lamezia', 1703, '78', 107, 'IT', 38.91982000, 16.25221000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429452'),
(60436, 'Santa Fiora', 1664, '52', 107, 'IT', 42.83129000, 11.58474000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429452'),
(60437, 'Santa Flavia', 1709, '82', 107, 'IT', 38.10448000, 13.53340000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429452'),
(60438, 'Santa Giuletta', 1705, '25', 107, 'IT', 45.03397000, 9.18126000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429452'),
(60439, 'Santa Giusta', 1715, '88', 107, 'IT', 39.88070000, 8.60916000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429452'),
(60440, 'Santa Giustina', 1753, '34', 107, 'IT', 46.08118000, 12.03822000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18429452'),
(60441, 'Santa Giustina', 1773, '45', 107, 'IT', 44.06778000, 12.48500000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18447995'),
(60442, 'Santa Giustina in Colle', 1753, '34', 107, 'IT', 45.57506000, 11.90039000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18447995'),
(60443, 'Santa Luce', 1664, '52', 107, 'IT', 43.47143000, 10.56269000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18447995'),
(60444, 'Santa Lucia', 1664, '52', 107, 'IT', 43.88296000, 10.70307000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q2343463'),
(60445, 'Santa Lucia', 1669, '72', 107, 'IT', 41.30409000, 14.07937000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18436753'),
(60446, 'Santa Lucia', 1678, '62', 107, 'IT', 41.98240000, 12.65625000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18455264'),
(60447, 'Santa Lucia del Mela', 1709, '82', 107, 'IT', 38.14408000, 15.28055000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18455264'),
(60448, 'Santa Lucia di Piave', 1753, '34', 107, 'IT', 45.86159000, 12.29390000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18455264'),
(60449, 'Santa Lucia di Serino', 1669, '72', 107, 'IT', 40.87042000, 14.87594000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18455264'),
(60450, 'Santa Margherita', 1705, '25', 107, 'IT', 45.63391000, 9.23013000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q2586104'),
(60451, 'Santa Margherita Ligure', 1768, '42', 107, 'IT', 44.33456000, 9.21204000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q2586104'),
(60452, 'Santa Margherita d\'Adige', 1753, '34', 107, 'IT', 45.21210000, 11.55759000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q2586104'),
(60453, 'Santa Margherita di Belice', 1709, '82', 107, 'IT', 37.69281000, 13.01584000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q2586104'),
(60454, 'Santa Margherita di Staffora', 1705, '25', 107, 'IT', 44.77154000, 9.24006000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q2586104'),
(60455, 'Santa Maria', 1669, '72', 107, 'IT', 40.29782000, 14.95214000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q1548190'),
(60456, 'Santa Maria', 1753, '34', 107, 'IT', 45.99115000, 12.22992000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18502464'),
(60457, 'Santa Maria A Vico', 1669, '72', 107, 'IT', 41.02611000, 14.46515000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18502464'),
(60458, 'Santa Maria Apparente', 1670, '57', 107, 'IT', 43.29661000, 13.69223000, '2019-10-05 23:07:06', '2019-10-05 23:07:06', 1, 'Q18497804');

