INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(26833, 'Königslutter am Elm', 3008, 'NI', 82, 'DE', 52.25116000, 10.81683000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q39596'),
(26834, 'Königstein', 3021, 'SN', 82, 'DE', 50.91570000, 14.07186000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q6783'),
(26835, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 3009, 'BY', 82, 'DE', 49.60854000, 11.63143000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q552122'),
(26836, '<PERSON><PERSON><PERSON><PERSON><PERSON> im Taunus', 3018, 'HE', 82, '<PERSON>', 50.17943000, 8.47132000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q569661'),
(26837, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 3021, '<PERSON><PERSON>', 82, '<PERSON>', 50.55000000, 13.05000000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q57797'),
(26838, 'K<PERSON><PERSON>gswartha', 3021, 'S<PERSON>', 82, '<PERSON>', 51.31028000, 14.32797000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q93250'),
(26839, 'Königswinter', 3017, 'NW', 82, 'DE', 50.68376000, 7.18675000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q33453943'),
(26840, 'Könitz', 3015, 'TH', 82, 'DE', 50.64979000, 11.48809000, '2019-10-05 22:41:11', '2020-05-01 17:22:50', 1, 'Q33453943'),
(26841, 'Könnern', 3011, 'ST', 82, 'DE', 51.67120000, 11.77068000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q518419'),
(26842, 'Köpenick', 3010, 'BE', 82, 'DE', 52.44550000, 13.57455000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q521521'),
(26843, 'Körle', 3018, 'HE', 82, 'DE', 51.16667000, 9.51667000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q521521'),
(26844, 'Körner', 3015, 'TH', 82, 'DE', 51.23126000, 10.58878000, '2019-10-05 22:41:11', '2020-05-01 17:22:50', 1, 'Q521521'),
(26845, 'Körperich', 3019, 'RP', 82, 'DE', 49.92449000, 6.25973000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q521521'),
(26846, 'Köthen', 3011, 'ST', 82, 'DE', 51.75185000, 11.97093000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q1796771'),
(26847, 'Kötzschau', 3011, 'ST', 82, 'DE', 51.31244000, 12.13044000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q703831'),
(26848, 'Kötzting', 3009, 'BY', 82, 'DE', 49.17649000, 12.85515000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q703831'),
(26849, 'Kühbach', 3009, 'BY', 82, 'DE', 48.49101000, 11.18691000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q1796912'),
(26850, 'Kühndorf', 3015, 'TH', 82, 'DE', 50.60860000, 10.48940000, '2019-10-05 22:41:11', '2020-05-01 17:22:50', 1, 'Q559805'),
(26851, 'Kührstedt', 3008, 'NI', 82, 'DE', 53.57747000, 8.80091000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q559805'),
(26852, 'Küllstedt', 3015, 'TH', 82, 'DE', 51.27582000, 10.28040000, '2019-10-05 22:41:11', '2020-05-01 17:22:50', 1, 'Q628701'),
(26853, 'Külsheim', 3006, 'BW', 82, 'DE', 49.66942000, 9.52361000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q61711'),
(26854, 'Kümmersbruck', 3009, 'BY', 82, 'DE', 49.41917000, 11.88833000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q513249'),
(26855, 'Künzell', 3018, 'HE', 82, 'DE', 50.54420000, 9.71792000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q639703'),
(26856, 'Künzelsau', 3006, 'BW', 82, 'DE', 49.28180000, 9.68352000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q545177'),
(26857, 'Künzing', 3009, 'BY', 82, 'DE', 48.66667000, 13.08333000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q545177'),
(26858, 'Kürnach', 3009, 'BY', 82, 'DE', 49.85000000, 10.03333000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q545177'),
(26859, 'Kürnbach', 3006, 'BW', 82, 'DE', 49.07750000, 8.84556000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q545177'),
(26860, 'Kürten', 3017, 'NW', 82, 'DE', 51.05000000, 7.26667000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q245542'),
(26861, 'Küsten', 3008, 'NI', 82, 'DE', 52.97779000, 11.06240000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q632635'),
(26862, 'Laage', 3007, 'MV', 82, 'DE', 53.92560000, 12.34694000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q565635'),
(26863, 'Laar', 3008, 'NI', 82, 'DE', 52.35581000, 8.25388000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q32072207'),
(26864, 'Laatzen', 3008, 'NI', 82, 'DE', 52.31506000, 9.79739000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q14856'),
(26865, 'Laberweinting', 3009, 'BY', 82, 'DE', 48.80000000, 12.31667000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q547972'),
(26866, 'Laboe', 3005, 'SH', 82, 'DE', 54.40000000, 10.21667000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q504740'),
(26867, 'Lachen', 3009, 'BY', 82, 'DE', 47.94590000, 10.23943000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q504740'),
(26868, 'Lachen-Speyerdorf', 3019, 'RP', 82, 'DE', 49.33049000, 8.19983000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q1799686'),
(26869, 'Lachendorf', 3008, 'NI', 82, 'DE', 52.61667000, 10.25000000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q566319'),
(26870, 'Ladbergen', 3017, 'NW', 82, 'DE', 52.13333000, 7.75000000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q32072623'),
(26871, 'Ladelund', 3005, 'SH', 82, 'DE', 54.84084000, 9.02135000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q32072623'),
(26872, 'Ladenburg', 3006, 'BW', 82, 'DE', 49.47307000, 8.60896000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q83225'),
(26873, 'Laer', 3017, 'NW', 82, 'DE', 52.05555000, 7.35775000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q181910'),
(26874, 'Lage', 3017, 'NW', 82, 'DE', 51.99223000, 8.79301000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q14953'),
(26875, 'Lahnstein', 3019, 'RP', 82, 'DE', 50.30000000, 7.61667000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q569639'),
(26876, 'Lahr', 3006, 'BW', 82, 'DE', 48.34042000, 7.86886000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q7039'),
(26877, 'Laichingen', 3006, 'BW', 82, 'DE', 48.48939000, 9.68612000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q515438'),
(26878, 'Lalendorf', 3007, 'MV', 82, 'DE', 53.75774000, 12.38983000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q638318'),
(26879, 'Lalling', 3009, 'BY', 82, 'DE', 48.84525000, 13.14008000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32083945'),
(26880, 'Lam', 3009, 'BY', 82, 'DE', 49.19665000, 13.05051000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q310990'),
(26881, 'Lambrecht', 3019, 'RP', 82, 'DE', 49.37055000, 8.07264000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q628198'),
(26882, 'Lambrechtshagen', 3007, 'MV', 82, 'DE', 54.10208000, 12.01645000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q672710'),
(26883, 'Lambsheim', 3019, 'RP', 82, 'DE', 49.51361000, 8.28778000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q22961'),
(26884, 'Lamerdingen', 3009, 'BY', 82, 'DE', 48.09195000, 10.73982000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q531972'),
(26885, 'Lampertheim', 3018, 'HE', 82, 'DE', 49.59786000, 8.47250000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q16099'),
(26886, 'Lampertswalde', 3021, 'SN', 82, 'DE', 51.31108000, 13.67694000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q8730'),
(26887, 'Lamspringe', 3008, 'NI', 82, 'DE', 51.96071000, 10.01105000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q574356'),
(26888, 'Lamstedt', 3008, 'NI', 82, 'DE', 53.63333000, 9.10000000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q503261'),
(26889, 'Landau an der Isar', 3009, 'BY', 82, 'DE', 48.67249000, 12.69316000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32084506'),
(26890, 'Landau in der Pfalz', 3019, 'RP', 82, 'DE', 49.19844000, 8.11692000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32084506'),
(26891, 'Landesbergen', 3008, 'NI', 82, 'DE', 52.55598000, 9.12505000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q557459'),
(26892, 'Landolfshausen', 3008, 'NI', 82, 'DE', 51.53333000, 10.10000000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q552244'),
(26893, 'Landsberg', 3011, 'ST', 82, 'DE', 51.52698000, 12.16076000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32085232'),
(26894, 'Landsberg am Lech', 3009, 'BY', 82, 'DE', 48.04819000, 10.88282000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q494299'),
(26895, 'Landsberied', 3009, 'BY', 82, 'DE', 48.16667000, 11.16667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32085253'),
(26896, 'Landscheid', 3019, 'RP', 82, 'DE', 49.98333000, 6.76667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q337826'),
(26897, 'Landshut', 3009, 'BY', 82, 'DE', 48.52961000, 12.16179000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q3974'),
(26898, 'Landstuhl', 3019, 'RP', 82, 'DE', 49.41306000, 7.57021000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q22950'),
(26899, 'Langballig', 3005, 'SH', 82, 'DE', 54.80000000, 9.63333000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q428981'),
(26900, 'Langeln', 3011, 'ST', 82, 'DE', 51.91188000, 10.79436000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q282704'),
(26901, 'Langelsheim', 3008, 'NI', 82, 'DE', 51.93789000, 10.33264000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q628260'),
(26902, 'Langen', 3018, 'HE', 82, 'DE', 49.98955000, 8.66852000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q14932'),
(26903, 'Langen', 3008, 'NI', 82, 'DE', 53.60554000, 8.59509000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q14932'),
(26904, 'Langenaltheim', 3009, 'BY', 82, 'DE', 48.89318000, 10.93107000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q546335'),
(26905, 'Langenargen', 3006, 'BW', 82, 'DE', 47.59858000, 9.54163000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q532326'),
(26906, 'Langenau', 3006, 'BW', 82, 'DE', 48.49616000, 10.11849000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q559520'),
(26907, 'Langenbach', 3009, 'BY', 82, 'DE', 48.43333000, 11.85000000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q532907'),
(26908, 'Langenbach bei Marienberg', 3019, 'RP', 82, 'DE', 50.63806000, 7.94787000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32086298'),
(26909, 'Langenberg', 3017, 'NW', 82, 'DE', 51.77206000, 8.31809000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32086298'),
(26910, 'Langenbernsdorf', 3021, 'SN', 82, 'DE', 50.75583000, 12.32669000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q17120'),
(26911, 'Langenbogen', 3011, 'ST', 82, 'DE', 51.48300000, 11.77786000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q694943'),
(26912, 'Langenbrettach', 3006, 'BW', 82, 'DE', 49.22601000, 9.41842000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q694943'),
(26913, 'Langenburg', 3006, 'BW', 82, 'DE', 49.25401000, 9.85673000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q82382'),
(26914, 'Langendorf', 3011, 'ST', 82, 'DE', 51.17972000, 11.96140000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q696566'),
(26915, 'Langeneichstädt', 3011, 'ST', 82, 'DE', 51.34537000, 11.74330000, '2019-10-05 22:41:12', '2020-05-01 17:22:49', 1, 'Q685657'),
(26916, 'Langenenslingen', 3006, 'BW', 82, 'DE', 48.14851000, 9.37765000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q538544'),
(26917, 'Langenfeld', 3009, 'BY', 82, 'DE', 49.61667000, 10.51667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q538544'),
(26918, 'Langenfeld', 3017, 'NW', 82, 'DE', 51.10821000, 6.94831000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32086445'),
(26919, 'Langenhagen', 3008, 'NI', 82, 'DE', 52.44758000, 9.73741000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q4158'),
(26920, 'Langenhahn', 3019, 'RP', 82, 'DE', 50.58333000, 7.91667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q676219'),
(26921, 'Langenhorn', 3005, 'SH', 82, 'DE', 54.66667000, 8.91667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q559410'),
(26922, 'Langenhorn', 3016, 'HH', 82, 'DE', 53.66667000, 10.01667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q559410'),
(26923, 'Langenlonsheim', 3019, 'RP', 82, 'DE', 49.90000000, 7.90000000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q631783'),
(26924, 'Langenmosen', 3009, 'BY', 82, 'DE', 48.60658000, 11.21378000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q514438'),
(26925, 'Langenneufnach', 3009, 'BY', 82, 'DE', 48.26667000, 10.60000000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q514438'),
(26926, 'Langenorla', 3015, 'TH', 82, 'DE', 50.74067000, 11.58023000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q665503'),
(26927, 'Langenpreising', 3009, 'BY', 82, 'DE', 48.42569000, 11.97217000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q528673'),
(26928, 'Langenselbold', 3018, 'HE', 82, 'DE', 50.17657000, 9.04003000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q544737'),
(26929, 'Langensendelbach', 3009, 'BY', 82, 'DE', 49.64051000, 11.07104000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q507090'),
(26930, 'Langenstein', 3011, 'ST', 82, 'DE', 51.85551000, 10.98822000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q704109'),
(26931, 'Langenwetzendorf', 3015, 'TH', 82, 'DE', 50.67884000, 12.09407000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q538924'),
(26932, 'Langenzenn', 3009, 'BY', 82, 'DE', 49.49463000, 10.79230000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q2230'),
(26933, 'Langeoog', 3008, 'NI', 82, 'DE', 53.75000000, 7.48333000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q25129'),
(26934, 'Langerringen', 3009, 'BY', 82, 'DE', 48.14590000, 10.75894000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q524691'),
(26935, 'Langerwehe', 3017, 'NW', 82, 'DE', 50.81667000, 6.35000000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q200018'),
(26936, 'Langewiesen', 3015, 'TH', 82, 'DE', 50.67252000, 10.97102000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q538887'),
(26937, 'Langfurth', 3009, 'BY', 82, 'DE', 49.10112000, 10.45359000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q510431'),
(26938, 'Langgöns', 3018, 'HE', 82, 'DE', 50.50000000, 8.66667000, '2019-10-05 22:41:12', '2020-05-01 17:22:48', 1, 'Q622388'),
(26939, 'Langlingen', 3008, 'NI', 82, 'DE', 52.55593000, 10.28291000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q555820'),
(26940, 'Langquaid', 3009, 'BY', 82, 'DE', 48.82318000, 12.05134000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32087620'),
(26941, 'Langstedt', 3005, 'SH', 82, 'DE', 54.61667000, 9.38333000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q545817'),
(26942, 'Langsur', 3019, 'RP', 82, 'DE', 49.72442000, 6.49906000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q610893'),
(26943, 'Langula', 3015, 'TH', 82, 'DE', 51.15001000, 10.41670000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q612722'),
(26944, 'Langwedel', 3005, 'SH', 82, 'DE', 54.21033000, 9.92761000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q612722'),
(26945, 'Langwedel', 3008, 'NI', 82, 'DE', 52.97864000, 9.18542000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32087734'),
(26946, 'Langweid', 3009, 'BY', 82, 'DE', 48.49095000, 10.85310000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32087746'),
(26947, 'Lankow', 3007, 'MV', 82, 'DE', 53.64983000, 11.36913000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q32087746'),
(26948, 'Lankwitz', 3010, 'BE', 82, 'DE', 52.43623000, 13.34590000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q671474'),
(26949, 'Lanstrop', 3017, 'NW', 82, 'DE', 51.57504000, 7.56752000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q1250657'),
(26950, 'Lappersdorf', 3009, 'BY', 82, 'DE', 49.04694000, 12.09130000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q491550'),
(26951, 'Lasbek', 3005, 'SH', 82, 'DE', 53.73333000, 10.36667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q634537'),
(26952, 'Lassan', 3007, 'MV', 82, 'DE', 53.94874000, 13.85219000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q634537'),
(26953, 'Lastrup', 3008, 'NI', 82, 'DE', 52.79468000, 7.86715000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q559578'),
(26954, 'Lathen', 3008, 'NI', 82, 'DE', 52.86667000, 7.31667000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q551087'),
(26955, 'Laubach', 3018, 'HE', 82, 'DE', 50.54196000, 8.99034000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q567010'),
(26956, 'Laubach', 3019, 'RP', 82, 'DE', 50.23277000, 7.07333000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q693007'),
(26957, 'Lauben', 3009, 'BY', 82, 'DE', 48.05928000, 10.29014000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q246939'),
(26958, 'Laucha', 3011, 'ST', 82, 'DE', 51.22422000, 11.67988000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q124803'),
(26959, 'Lauchhammer', 3013, 'BB', 82, 'DE', 51.48813000, 13.76623000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q589247'),
(26960, 'Lauchheim', 3006, 'BW', 82, 'DE', 48.87135000, 10.24222000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q574207'),
(26961, 'Lauchringen', 3006, 'BW', 82, 'DE', 47.62699000, 8.31442000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q527855'),
(26962, 'Lauchröden', 3015, 'TH', 82, 'DE', 50.99371000, 10.15694000, '2019-10-05 22:41:12', '2020-05-01 17:22:50', 1, 'Q1669717'),
(26963, 'Lauda-Königshofen', 3006, 'BW', 82, 'DE', 49.56526000, 9.70816000, '2019-10-05 22:41:12', '2020-05-01 17:22:47', 1, 'Q1669717'),
(26964, 'Laudenbach', 3009, 'BY', 82, 'DE', 49.74583000, 9.17611000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q528301'),
(26965, 'Laudenbach', 3006, 'BW', 82, 'DE', 49.61333000, 8.65389000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q81620'),
(26966, 'Lauenau', 3008, 'NI', 82, 'DE', 52.27393000, 9.36928000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q592405'),
(26967, 'Lauenbrück', 3008, 'NI', 82, 'DE', 53.20000000, 9.56667000, '2019-10-05 22:41:12', '2020-05-01 17:22:48', 1, 'Q592405'),
(26968, 'Lauenburg', 3005, 'SH', 82, 'DE', 53.37199000, 10.55654000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q592405'),
(26969, 'Lauenhagen', 3008, 'NI', 82, 'DE', 52.35547000, 9.20637000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q637015'),
(26970, 'Lauf', 3006, 'BW', 82, 'DE', 48.65000000, 8.13333000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q632914'),
(26971, 'Lauf an der Pegnitz', 3009, 'BY', 82, 'DE', 49.51386000, 11.28247000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q504334'),
(26972, 'Laufach', 3009, 'BY', 82, 'DE', 50.01667000, 9.30000000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q539764'),
(26973, 'Laufdorf', 3018, 'HE', 82, 'DE', 50.51605000, 8.45982000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q1807393'),
(26974, 'Laufen', 3009, 'BY', 82, 'DE', 47.93568000, 12.92856000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q1807393'),
(26975, 'Laufenburg', 3006, 'BW', 82, 'DE', 47.56512000, 8.06045000, '2019-10-05 22:41:12', '2019-10-05 22:41:12', 1, 'Q1807393'),
(26976, 'Lauffen am Neckar', 3006, 'BW', 82, 'DE', 49.07340000, 9.14567000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q525720'),
(26977, 'Laugna', 3009, 'BY', 82, 'DE', 48.53333000, 10.70000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q526102'),
(26978, 'Lauingen', 3009, 'BY', 82, 'DE', 48.56775000, 10.42706000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q572921'),
(26979, 'Laupheim', 3006, 'BW', 82, 'DE', 48.22786000, 9.87874000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q574314'),
(26980, 'Lauscha', 3015, 'TH', 82, 'DE', 50.47687000, 11.15962000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q518383'),
(26981, 'Lautenbach', 3006, 'BW', 82, 'DE', 48.48185000, 8.02732000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q518383'),
(26982, 'Lauter', 3021, 'SN', 82, 'DE', 50.56264000, 12.73513000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q57911'),
(26983, 'Lauter', 3009, 'BY', 82, 'DE', 49.97317000, 10.78842000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q57911'),
(26984, 'Lauterbach', 3018, 'HE', 82, 'DE', 50.63558000, 9.39777000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q622569'),
(26985, 'Lauterbach/Schwarzwald', 3006, 'BW', 82, 'DE', 48.22999000, 8.34240000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q622569'),
(26986, 'Lauterecken', 3019, 'RP', 82, 'DE', 49.64993000, 7.59265000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q240283'),
(26987, 'Lauterhofen', 3009, 'BY', 82, 'DE', 49.36840000, 11.60294000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q517565'),
(26988, 'Lautertal', 3018, 'HE', 82, 'DE', 50.58333000, 9.28333000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q622136'),
(26989, 'Lautertal', 3009, 'BY', 82, 'DE', 50.33333000, 10.96667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q622136'),
(26990, 'Lautrach', 3009, 'BY', 82, 'DE', 47.89805000, 10.11806000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q566615'),
(26991, 'Laußig', 3021, 'SN', 82, 'DE', 51.54676000, 12.62930000, '2019-10-05 22:41:13', '2020-05-01 17:22:49', 1, 'Q16051'),
(26992, 'Laußnitz', 3021, 'SN', 82, 'DE', 51.25000000, 13.88333000, '2019-10-05 22:41:13', '2020-05-01 17:22:49', 1, 'Q93254'),
(26993, 'Lawalde', 3021, 'SN', 82, 'DE', 51.08333000, 14.60000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q544955'),
(26994, 'Lebach', 3020, 'SL', 82, 'DE', 49.41122000, 6.90988000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q558593'),
(26995, 'Lebus', 3013, 'BB', 82, 'DE', 52.42719000, 14.53235000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q26362'),
(26996, 'Lechbruck', 3009, 'BY', 82, 'DE', 47.70162000, 10.79493000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q328714'),
(26997, 'Leck', 3005, 'SH', 82, 'DE', 54.76667000, 8.98333000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q28001'),
(26998, 'Leegebruch', 3013, 'BB', 82, 'DE', 52.72340000, 13.19304000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q531735'),
(26999, 'Leer', 3008, 'NI', 82, 'DE', 53.23157000, 7.46100000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q15984'),
(27000, 'Leese', 3008, 'NI', 82, 'DE', 52.50000000, 9.11667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q613709'),
(27001, 'Leezdorf', 3008, 'NI', 82, 'DE', 53.55000000, 7.30000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q549694'),
(27002, 'Leezen', 3005, 'SH', 82, 'DE', 53.86667000, 10.25000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q566105'),
(27003, 'Leezen', 3007, 'MV', 82, 'DE', 53.66265000, 11.49874000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q566105'),
(27004, 'Legau', 3009, 'BY', 82, 'DE', 47.85628000, 10.12981000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q566548'),
(27005, 'Legden', 3017, 'NW', 82, 'DE', 52.03333000, 7.10000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32091276'),
(27006, 'Lehe', 3005, 'SH', 82, 'DE', 54.34143000, 9.02374000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32091276'),
(27007, 'Lehesten', 3015, 'TH', 82, 'DE', 50.98333000, 11.58333000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32091276'),
(27008, 'Lehmen', 3019, 'RP', 82, 'DE', 50.28333000, 7.45000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q567585'),
(27009, 'Lehmkuhlen', 3005, 'SH', 82, 'DE', 54.23333000, 10.36667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q686715'),
(27010, 'Lehrberg', 3009, 'BY', 82, 'DE', 49.34568000, 10.51101000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q508257'),
(27011, 'Lehre', 3008, 'NI', 82, 'DE', 52.33333000, 10.66667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q508257'),
(27012, 'Lehrensteinsfeld', 3006, 'BW', 82, 'DE', 49.13111000, 9.32722000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q534681'),
(27013, 'Lehrte', 3008, 'NI', 82, 'DE', 52.37193000, 9.97919000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q7057'),
(27014, 'Leibertingen', 3006, 'BW', 82, 'DE', 48.04263000, 9.01308000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q550633'),
(27015, 'Leiblfing', 3009, 'BY', 82, 'DE', 48.77565000, 12.51793000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q582081'),
(27016, 'Leichlingen', 3017, 'NW', 82, 'DE', 51.10628000, 7.01873000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32091947'),
(27017, 'Leidersbach', 3009, 'BY', 82, 'DE', 49.90139000, 9.22167000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q520943'),
(27018, 'Leiferde', 3008, 'NI', 82, 'DE', 52.20445000, 10.50842000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q520943'),
(27019, 'Leimbach', 3015, 'TH', 82, 'DE', 50.81667000, 10.20000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q669035'),
(27020, 'Leimen', 3006, 'BW', 82, 'DE', 49.34737000, 8.68733000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q23007'),
(27021, 'Leimersheim', 3019, 'RP', 82, 'DE', 49.12421000, 8.34531000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q656053'),
(27022, 'Leinburg', 3009, 'BY', 82, 'DE', 49.45167000, 11.31000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q542878'),
(27023, 'Leinefelde-Worbis', 3015, 'TH', 82, 'DE', 51.38796000, 10.32620000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q580115'),
(27024, 'Leinfelden-Echterdingen', 3006, 'BW', 82, 'DE', 48.69406000, 9.16809000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q14905'),
(27025, 'Leingarten', 3006, 'BW', 82, 'DE', 49.14639000, 9.11694000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q521981'),
(27026, 'Leinzell', 3006, 'BW', 82, 'DE', 48.84941000, 9.87750000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q574203'),
(27027, 'Leipheim', 3009, 'BY', 82, 'DE', 48.45004000, 10.22278000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q504354'),
(27028, 'Leipzig', 3021, 'SN', 82, 'DE', 51.33962000, 12.37129000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q2079'),
(27029, 'Leisnig', 3021, 'SN', 82, 'DE', 51.15743000, 12.92790000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q71133'),
(27030, 'Leitzkau', 3011, 'ST', 82, 'DE', 52.05818000, 11.95203000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q874314'),
(27031, 'Leiwen', 3019, 'RP', 82, 'DE', 49.81667000, 6.88333000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q681047'),
(27032, 'Leißling', 3011, 'ST', 82, 'DE', 51.18260000, 11.90954000, '2019-10-05 22:41:13', '2020-05-01 17:22:49', 1, 'Q688622'),
(27033, 'Lemberg', 3019, 'RP', 82, 'DE', 49.17309000, 7.65111000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q688622'),
(27034, 'Lemförde', 3008, 'NI', 82, 'DE', 52.46557000, 8.37621000, '2019-10-05 22:41:13', '2020-05-01 17:22:48', 1, 'Q503697'),
(27035, 'Lemgo', 3017, 'NW', 82, 'DE', 52.02786000, 8.89901000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q14817'),
(27036, 'Lemsahl-Mellingstedt', 3016, 'HH', 82, 'DE', 53.68998000, 10.09648000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q1815'),
(27037, 'Lemwerder', 3008, 'NI', 82, 'DE', 53.16667000, 8.61667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q676883'),
(27038, 'Lengdorf', 3009, 'BY', 82, 'DE', 48.25737000, 12.04973000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q528271'),
(27039, 'Lengede', 3008, 'NI', 82, 'DE', 52.20487000, 10.30775000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q627559'),
(27040, 'Lengefeld', 3021, 'SN', 82, 'DE', 50.71911000, 13.19290000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q57916'),
(27041, 'Lengenfeld', 3021, 'SN', 82, 'DE', 50.56940000, 12.36408000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32093011'),
(27042, 'Lengenwang', 3009, 'BY', 82, 'DE', 47.70000000, 10.60000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32093011'),
(27043, 'Lengerich', 3008, 'NI', 82, 'DE', 52.55381000, 7.53164000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32093011'),
(27044, 'Lengerich', 3017, 'NW', 82, 'DE', 52.18661000, 7.86043000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q32093038'),
(27045, 'Lenggries', 3009, 'BY', 82, 'DE', 47.68333000, 11.56667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q532151'),
(27046, 'Lennestadt', 3017, 'NW', 82, 'DE', 51.11721000, 8.06707000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q10893'),
(27047, 'Lenningen', 3006, 'BW', 82, 'DE', 48.55048000, 9.47674000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q10893'),
(27048, 'Lensahn', 3005, 'SH', 82, 'DE', 54.21652000, 10.88326000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q552941'),
(27049, 'Lentföhrden', 3005, 'SH', 82, 'DE', 53.86667000, 9.88333000, '2019-10-05 22:41:13', '2020-05-01 17:22:49', 1, 'Q552941'),
(27050, 'Lenzen', 3013, 'BB', 82, 'DE', 53.09176000, 11.47453000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q625768'),
(27051, 'Lenzkirch', 3006, 'BW', 82, 'DE', 47.86832000, 8.20211000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q521265'),
(27052, 'Leonberg', 3009, 'BY', 82, 'DE', 49.94708000, 12.28520000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q521265'),
(27053, 'Leonberg', 3006, 'BW', 82, 'DE', 48.80000000, 9.01667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q7010'),
(27054, 'Leopoldshöhe', 3017, 'NW', 82, 'DE', 52.01246000, 8.69834000, '2019-10-05 22:41:13', '2020-05-01 17:22:49', 1, 'Q242385'),
(27055, 'Letschin', 3013, 'BB', 82, 'DE', 52.64379000, 14.36007000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q585206'),
(27056, 'Letzlingen', 3011, 'ST', 82, 'DE', 52.44615000, 11.48518000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q668985'),
(27057, 'Leubnitz', 3021, 'SN', 82, 'DE', 50.72313000, 12.35660000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q1412212'),
(27058, 'Leubsdorf', 3021, 'SN', 82, 'DE', 50.80000000, 13.16667000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q1412212'),
(27059, 'Leubsdorf', 3019, 'RP', 82, 'DE', 50.55000000, 7.30000000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q1412212'),
(27060, 'Leuchtenberg', 3009, 'BY', 82, 'DE', 49.59737000, 12.25840000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q504316'),
(27061, 'Leun', 3018, 'HE', 82, 'DE', 50.55129000, 8.35836000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q571778'),
(27062, 'Leuna', 3011, 'ST', 82, 'DE', 51.31783000, 12.01589000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q505191'),
(27063, 'Leupoldsgrün', 3009, 'BY', 82, 'DE', 50.30000000, 11.80000000, '2019-10-05 22:41:13', '2020-05-01 17:22:47', 1, 'Q505191'),
(27064, 'Leutenbach', 3009, 'BY', 82, 'DE', 49.70950000, 11.17224000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q505191'),
(27065, 'Leutenbach', 3006, 'BW', 82, 'DE', 48.88791000, 9.39267000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q505191'),
(27066, 'Leutenberg', 3015, 'TH', 82, 'DE', 50.56354000, 11.45619000, '2019-10-05 22:41:13', '2019-10-05 22:41:13', 1, 'Q550813'),
(27067, 'Leutersdorf', 3021, 'SN', 82, 'DE', 50.95000000, 14.65000000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q550813'),
(27068, 'Leutershausen', 3009, 'BY', 82, 'DE', 49.29869000, 10.41189000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q389945'),
(27069, 'Leutesdorf', 3019, 'RP', 82, 'DE', 50.45000000, 7.38333000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q659908'),
(27070, 'Leutkirch', 3006, 'BW', 82, 'DE', 47.82672000, 10.02050000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q539645'),
(27071, 'Leverkusen', 3017, 'NW', 82, 'DE', 51.03030000, 6.98432000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q2938'),
(27072, 'Lewenberg', 3007, 'MV', 82, 'DE', 53.64591000, 11.40767000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q23560546'),
(27073, 'Lich', 3018, 'HE', 82, 'DE', 50.52085000, 8.81567000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q23560546'),
(27074, 'Lichte', 3015, 'TH', 82, 'DE', 50.51667000, 11.18333000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q637559'),
(27075, 'Lichtenau', 3017, 'NW', 82, 'DE', 51.61707000, 8.89665000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q32094857'),
(27076, 'Lichtenau', 3006, 'BW', 82, 'DE', 48.72607000, 8.00486000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q550859'),
(27077, 'Lichtenau', 3009, 'BY', 82, 'DE', 49.15979000, 10.37935000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q32094851'),
(27078, 'Lichtenberg', 3010, 'BE', 82, 'DE', 52.51395000, 13.49975000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q9169094'),
(27079, 'Lichtenberg', 3009, 'BY', 82, 'DE', 50.38335000, 11.67624000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q9169094'),
(27080, 'Lichtenberg', 3021, 'SN', 82, 'DE', 50.83492000, 13.42478000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q32094915'),
(27081, 'Lichtenfels', 3009, 'BY', 82, 'DE', 50.14567000, 11.05928000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q32094915'),
(27082, 'Lichtenrade', 3010, 'BE', 82, 'DE', 52.39844000, 13.40637000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q821101'),
(27083, 'Lichtenstein', 3021, 'SN', 82, 'DE', 50.75659000, 12.63025000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q20081'),
(27084, 'Lichtentanne', 3021, 'SN', 82, 'DE', 50.69255000, 12.42585000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q17122'),
(27085, 'Lichterfelde', 3010, 'BE', 82, 'DE', 52.43330000, 13.30762000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q155211'),
(27086, 'Liebenau', 3018, 'HE', 82, 'DE', 51.49699000, 9.28207000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q155211'),
(27087, 'Liebenau', 3008, 'NI', 82, 'DE', 52.60362000, 9.09719000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q543181'),
(27088, 'Liebenburg', 3008, 'NI', 82, 'DE', 52.02176000, 10.43169000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q651194'),
(27089, 'Liebenwalde', 3013, 'BB', 82, 'DE', 52.87125000, 13.39465000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q583458'),
(27090, 'Lieberose', 3013, 'BB', 82, 'DE', 51.98491000, 14.29987000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q584485'),
(27091, 'Liebstadt', 3021, 'SN', 82, 'DE', 50.86417000, 13.85694000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q8199'),
(27092, 'Liederbach', 3018, 'HE', 82, 'DE', 50.12221000, 8.49397000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q32095409'),
(27093, 'Lienen', 3017, 'NW', 82, 'DE', 52.15000000, 7.98333000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q32095436'),
(27094, 'Lieser', 3019, 'RP', 82, 'DE', 49.91667000, 7.01667000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q32095436'),
(27095, 'Lieskau', 3011, 'ST', 82, 'DE', 51.50395000, 11.86208000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q702572'),
(27096, 'Lilienthal', 3008, 'NI', 82, 'DE', 53.14193000, 8.90338000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q499920'),
(27097, 'Limbach', 3021, 'SN', 82, 'DE', 50.58333000, 12.25000000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q46764'),
(27098, 'Limbach-Oberfrohna', 3021, 'SN', 82, 'DE', 50.85882000, 12.76165000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q20076'),
(27099, 'Limburg an der Lahn', 3018, 'HE', 82, 'DE', 50.38360000, 8.05030000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q16026'),
(27100, 'Limburgerhof', 3019, 'RP', 82, 'DE', 49.42444000, 8.39194000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q662258'),
(27101, 'Linau', 3005, 'SH', 82, 'DE', 53.64446000, 10.46853000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q662258'),
(27102, 'Lindau', 3009, 'BY', 82, 'DE', 47.54612000, 9.68431000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q153969'),
(27103, 'Lindau', 3011, 'ST', 82, 'DE', 52.03751000, 12.10788000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q697959'),
(27104, 'Lindau', 3005, 'SH', 82, 'DE', 54.38333000, 9.90000000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q697959'),
(27105, 'Lindberg', 3009, 'BY', 82, 'DE', 49.03680000, 13.25423000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q488718'),
(27106, 'Linden', 3019, 'RP', 82, 'DE', 49.35000000, 7.65000000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q488718'),
(27107, 'Lindenberg', 3019, 'RP', 82, 'DE', 49.38028000, 8.09861000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q685210'),
(27108, 'Lindenfels', 3018, 'HE', 82, 'DE', 49.68369000, 8.78151000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, '********'),
(27109, 'Lindewitt', 3005, 'SH', 82, 'DE', 54.70000000, 9.20000000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q551480'),
(27110, 'Lindhorst', 3008, 'NI', 82, 'DE', 52.35789000, 9.28319000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q551480'),
(27111, 'Lindlar', 3017, 'NW', 82, 'DE', 51.01959000, 7.37758000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q11047'),
(27112, 'Lindow', 3013, 'BB', 82, 'DE', 52.96689000, 12.98498000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q625741'),
(27113, 'Lindwedel', 3008, 'NI', 82, 'DE', 52.60784000, 9.68737000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q625741'),
(27114, 'Lingen', 3008, 'NI', 82, 'DE', 52.52143000, 7.31845000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q625741'),
(27115, 'Lingenfeld', 3019, 'RP', 82, 'DE', 49.25389000, 8.33861000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q22973'),
(27116, 'Linkenheim-Hochstetten', 3006, 'BW', 82, 'DE', 49.13197000, 8.41244000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q542589'),
(27117, 'Linnich', 3017, 'NW', 82, 'DE', 50.98002000, 6.27049000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q200122'),
(27118, 'Lintig', 3008, 'NI', 82, 'DE', 53.60341000, 8.87876000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q509131'),
(27119, 'Linz am Rhein', 3019, 'RP', 82, 'DE', 50.56884000, 7.28445000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q390125'),
(27120, 'Lippstadt', 3017, 'NW', 82, 'DE', 51.67369000, 8.34482000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q3943'),
(27121, 'Lissendorf', 3019, 'RP', 82, 'DE', 50.31667000, 6.60000000, '2019-10-05 22:41:14', '2019-10-05 22:41:14', 1, 'Q567229'),
(27122, 'List', 3005, 'SH', 82, 'DE', 55.01917000, 8.43132000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q567229'),
(27123, 'Litzendorf', 3009, 'BY', 82, 'DE', 49.91444000, 11.01028000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q550700'),
(27124, 'Lobbach', 3006, 'BW', 82, 'DE', 49.37519000, 8.88884000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q550700'),
(27125, 'Lobstädt', 3021, 'SN', 82, 'DE', 51.13361000, 12.44861000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q703880'),
(27126, 'Loburg', 3011, 'ST', 82, 'DE', 52.11509000, 12.07840000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q703877'),
(27127, 'Lochau', 3011, 'ST', 82, 'DE', 51.39738000, 12.05303000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q703877'),
(27128, 'Loddin', 3007, 'MV', 82, 'DE', 54.02252000, 14.00754000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q664950'),
(27129, 'Loffenau', 3006, 'BW', 82, 'DE', 48.77214000, 8.38463000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q80581'),
(27130, 'Lohberg', 3009, 'BY', 82, 'DE', 49.17604000, 13.10549000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q80581'),
(27131, 'Lohe-Rickelshof', 3005, 'SH', 82, 'DE', 54.18803000, 9.07066000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q550364'),
(27132, 'Lohfelden', 3018, 'HE', 82, 'DE', 51.26667000, 9.53333000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q562032'),
(27133, 'Lohmar', 3017, 'NW', 82, 'DE', 50.83868000, 7.21399000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q12449'),
(27134, 'Lohmen', 3021, 'SN', 82, 'DE', 50.98831000, 14.00268000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q12449'),
(27135, 'Lohne', 3008, 'NI', 82, 'DE', 52.66625000, 8.23750000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q12449'),
(27136, 'Lohr am Main', 3009, 'BY', 82, 'DE', 49.98922000, 9.57223000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q504774'),
(27137, 'Lohra', 3018, 'HE', 82, 'DE', 50.73333000, 8.63333000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q555430'),
(27138, 'Lohsa', 3021, 'SN', 82, 'DE', 51.38333000, 14.40000000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q93258'),
(27139, 'Loiching', 3009, 'BY', 82, 'DE', 48.61667000, 12.43333000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q32101788'),
(27140, 'Loitz', 3007, 'MV', 82, 'DE', 53.44205000, 13.38802000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q31434814'),
(27141, 'Lollar', 3018, 'HE', 82, 'DE', 50.64652000, 8.70495000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q625061'),
(27142, 'Lommatzsch', 3021, 'SN', 82, 'DE', 51.19537000, 13.30917000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q8734'),
(27143, 'Longkamp', 3019, 'RP', 82, 'DE', 49.89044000, 7.11764000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q8734'),
(27144, 'Longuich', 3019, 'RP', 82, 'DE', 49.80841000, 6.76832000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q569606'),
(27145, 'Lonnerstadt', 3009, 'BY', 82, 'DE', 49.69882000, 10.76711000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q1869695'),
(27146, 'Lonnig', 3019, 'RP', 82, 'DE', 50.31002000, 7.40509000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q1869695'),
(27147, 'Lonsee', 3006, 'BW', 82, 'DE', 48.54340000, 9.91999000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q540568'),
(27148, 'Lorch', 3006, 'BW', 82, 'DE', 48.79833000, 9.69140000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q574249'),
(27149, 'Lorsch', 3018, 'HE', 82, 'DE', 49.65000000, 8.56667000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q524939'),
(27150, 'Lorup', 3008, 'NI', 82, 'DE', 52.92495000, 7.64339000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q509099'),
(27151, 'Losheim', 3020, 'SL', 82, 'DE', 49.50990000, 6.74549000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q32110632'),
(27152, 'Lostau', 3011, 'ST', 82, 'DE', 52.20871000, 11.73795000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q701356'),
(27153, 'Lotte', 3017, 'NW', 82, 'DE', 52.28333000, 7.91667000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q701356'),
(27154, 'Lottstetten', 3006, 'BW', 82, 'DE', 47.63333000, 8.56667000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q520440'),
(27155, 'Lower Bavaria', 3009, 'BY', 82, 'DE', 48.75000000, 12.83333000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q10559'),
(27156, 'Loxstedt', 3008, 'NI', 82, 'DE', 53.46667000, 8.65000000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q509157'),
(27157, 'Loßburg', 3006, 'BW', 82, 'DE', 48.40000000, 8.45000000, '2019-10-05 22:41:15', '2020-05-01 17:22:47', 1, 'Q81682'),
(27158, 'Lubmin', 3007, 'MV', 82, 'DE', 54.13509000, 13.61687000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q652146'),
(27159, 'Lucka', 3015, 'TH', 82, 'DE', 51.09727000, 12.33336000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q285171'),
(27160, 'Luckau', 3013, 'BB', 82, 'DE', 51.85245000, 13.70735000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q539288'),
(27161, 'Luckenwalde', 3013, 'BB', 82, 'DE', 52.09029000, 13.16772000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q574146'),
(27162, 'Ludweiler-Warndt', 3020, 'SL', 82, 'DE', 49.22074000, 6.81195000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q574146'),
(27163, 'Ludwigsburg', 3006, 'BW', 82, 'DE', 48.89731000, 9.19161000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q622'),
(27164, 'Ludwigsfelde', 3013, 'BB', 82, 'DE', 52.30322000, 13.25405000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q574170'),
(27165, 'Ludwigshafen am Rhein', 3019, 'RP', 82, 'DE', 49.48121000, 8.44641000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q574170'),
(27166, 'Ludwigslust', 3007, 'MV', 82, 'DE', 53.32917000, 11.49714000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q156119'),
(27167, 'Ludwigsstadt', 3009, 'BY', 82, 'DE', 50.48605000, 11.38734000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q163024'),
(27168, 'Luftkurort Arendsee', 3011, 'ST', 82, 'DE', 52.88073000, 11.48621000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q490960'),
(27169, 'Lugau', 3021, 'SN', 82, 'DE', 50.73843000, 12.74861000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q57924'),
(27170, 'Luhden', 3008, 'NI', 82, 'DE', 52.22538000, 9.09256000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q57924'),
(27171, 'Luhe-Wildenau', 3009, 'BY', 82, 'DE', 49.58367000, 12.14921000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q508625'),
(27172, 'Luisenthal', 3015, 'TH', 82, 'DE', 50.78333000, 10.73333000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q546691'),
(27173, 'Luisenthal', 3020, 'SL', 82, 'DE', 49.24932000, 6.90057000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q546691'),
(27174, 'Lunden', 3005, 'SH', 82, 'DE', 54.33131000, 9.02523000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q554187'),
(27175, 'Lunestedt', 3008, 'NI', 82, 'DE', 53.43621000, 8.75193000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q673436'),
(27176, 'Lunzenau', 3021, 'SN', 82, 'DE', 50.96269000, 12.75594000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q71137'),
(27177, 'Lupburg', 3009, 'BY', 82, 'DE', 49.15510000, 11.75640000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q508879'),
(27178, 'Lurup', 3016, 'HH', 82, 'DE', 53.59266000, 9.87697000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q1602'),
(27179, 'Lustadt', 3019, 'RP', 82, 'DE', 49.24476000, 8.27407000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q22970'),
(27180, 'Lutter am Barenberge', 3008, 'NI', 82, 'DE', 51.98943000, 10.26930000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q575676'),
(27181, 'Lutzerath', 3019, 'RP', 82, 'DE', 50.12695000, 7.00740000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q553271'),
(27182, 'Lychen', 3013, 'BB', 82, 'DE', 53.21242000, 13.31483000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q635901'),
(27183, 'Lägerdorf', 3005, 'SH', 82, 'DE', 53.88333000, 9.58333000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q638314'),
(27184, 'Lähden', 3008, 'NI', 82, 'DE', 52.74547000, 7.57036000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q640121'),
(27185, 'Löbau', 3021, 'SN', 82, 'DE', 51.09954000, 14.66738000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q156768'),
(27186, 'Löbejün', 3011, 'ST', 82, 'DE', 51.63533000, 11.90106000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q708720'),
(27187, 'Löberitz', 3011, 'ST', 82, 'DE', 51.65393000, 12.14655000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q1879603'),
(27188, 'Löbichau', 3015, 'TH', 82, 'DE', 50.89383000, 12.26366000, '2019-10-05 22:41:15', '2020-05-01 17:22:50', 1, 'Q47039'),
(27189, 'Löbnitz', 3021, 'SN', 82, 'DE', 51.59238000, 12.46347000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q47039'),
(27190, 'Löchgau', 3006, 'BW', 82, 'DE', 49.00361000, 9.10639000, '2019-10-05 22:41:15', '2020-05-01 17:22:47', 1, 'Q62062'),
(27191, 'Löcknitz', 3007, 'MV', 82, 'DE', 53.28694000, 11.78108000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q62062'),
(27192, 'Löf', 3019, 'RP', 82, 'DE', 50.23333000, 7.43333000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q553970'),
(27193, 'Löffingen', 3006, 'BW', 82, 'DE', 47.88405000, 8.34384000, '2019-10-05 22:41:15', '2020-05-01 17:22:47', 1, 'Q527610'),
(27194, 'Löhnberg', 3018, 'HE', 82, 'DE', 50.51299000, 8.27202000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q599984'),
(27195, 'Löhne', 3017, 'NW', 82, 'DE', 52.18848000, 8.69220000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q14863'),
(27196, 'Löningen', 3008, 'NI', 82, 'DE', 52.73678000, 7.75809000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q559561'),
(27197, 'Lörrach', 3006, 'BW', 82, 'DE', 47.61497000, 7.66457000, '2019-10-05 22:41:15', '2020-05-01 17:22:47', 1, 'Q6855'),
(27198, 'Lörzweiler', 3019, 'RP', 82, 'DE', 49.89833000, 8.29472000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q6855'),
(27199, 'Löwenstein', 3006, 'BW', 82, 'DE', 49.09558000, 9.38000000, '2019-10-05 22:41:15', '2020-05-01 17:22:47', 1, 'Q522474'),
(27200, 'Lößnitz', 3021, 'SN', 82, 'DE', 50.62181000, 12.73147000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q522474'),
(27201, 'Lübars', 3010, 'BE', 82, 'DE', 52.61591000, 13.35350000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q563664'),
(27202, 'Lübbecke', 3017, 'NW', 82, 'DE', 52.30699000, 8.61423000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q33458568'),
(27203, 'Lübben', 3013, 'BB', 82, 'DE', 51.93814000, 13.88826000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q584815'),
(27204, 'Lübbenau', 3013, 'BB', 82, 'DE', 51.86217000, 13.95168000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q147895'),
(27205, 'Lübeck', 3005, 'SH', 82, 'DE', 53.86893000, 10.68729000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q2843'),
(27206, 'Lübow', 3007, 'MV', 82, 'DE', 53.85256000, 11.52440000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q2843'),
(27207, 'Lübstorf', 3007, 'MV', 82, 'DE', 53.72561000, 11.41471000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q2843'),
(27208, 'Lübtheen', 3007, 'MV', 82, 'DE', 53.30118000, 11.08368000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q536722'),
(27209, 'Lübz', 3007, 'MV', 82, 'DE', 53.46261000, 12.02917000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q559033'),
(27210, 'Lüchow', 3008, 'NI', 82, 'DE', 52.96811000, 11.15397000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q705643'),
(27211, 'Lüdenscheid', 3017, 'NW', 82, 'DE', 51.21977000, 7.62730000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q3893'),
(27212, 'Lüder', 3008, 'NI', 82, 'DE', 52.80878000, 10.66609000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q3893'),
(27213, 'Lüderitz', 3011, 'ST', 82, 'DE', 52.50912000, 11.74301000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q205800'),
(27214, 'Lüdersdorf', 3007, 'MV', 82, 'DE', 53.86835000, 11.74902000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q205800'),
(27215, 'Lüdersfeld', 3008, 'NI', 82, 'DE', 52.35993000, 9.25422000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q635994'),
(27216, 'Lüdinghausen', 3017, 'NW', 82, 'DE', 51.76830000, 7.44379000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q635994'),
(27217, 'Lügde', 3017, 'NW', 82, 'DE', 51.95828000, 9.24706000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q1880276'),
(27218, 'Lüneburg', 3008, 'NI', 82, 'DE', 53.25090000, 10.41409000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q3920'),
(27219, 'Lünen', 3017, 'NW', 82, 'DE', 51.61634000, 7.52872000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q3920'),
(27220, 'Lünne', 3008, 'NI', 82, 'DE', 52.42958000, 7.42653000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q560253'),
(27221, 'Lürschau', 3005, 'SH', 82, 'DE', 54.55000000, 9.50000000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q557339'),
(27222, 'Lütjenburg', 3005, 'SH', 82, 'DE', 54.29188000, 10.58945000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q221840'),
(27223, 'Lütjensee', 3005, 'SH', 82, 'DE', 53.65000000, 10.36667000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q567443'),
(27224, 'Lützelbach', 3018, 'HE', 82, 'DE', 49.74233000, 8.76687000, '2019-10-05 22:41:15', '2020-05-01 17:22:48', 1, 'Q567443'),
(27225, 'Lützen', 3011, 'ST', 82, 'DE', 51.25671000, 12.14164000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q517174'),
(27226, 'Lützow', 3007, 'MV', 82, 'DE', 53.65345000, 11.17582000, '2019-10-05 22:41:15', '2020-05-01 17:22:49', 1, 'Q517174'),
(27227, 'Machern', 3021, 'SN', 82, 'DE', 51.36667000, 12.63333000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q10752'),
(27228, 'Mackenbach', 3019, 'RP', 82, 'DE', 49.46667000, 7.58333000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q567199'),
(27229, 'Magdala', 3015, 'TH', 82, 'DE', 50.90698000, 11.44801000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q567199'),
(27230, 'Magdeburg', 3011, 'ST', 82, 'DE', 52.12773000, 11.62916000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q567199'),
(27231, 'Magstadt', 3006, 'BW', 82, 'DE', 48.74471000, 8.96675000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q557651'),
(27232, 'Mahlberg', 3006, 'BW', 82, 'DE', 48.28639000, 7.81411000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q532167'),
(27233, 'Mahlsdorf', 3010, 'BE', 82, 'DE', 52.50935000, 13.61373000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q563837'),
(27234, 'Maierhöfen', 3009, 'BY', 82, 'DE', 47.65000000, 10.05000000, '2019-10-05 22:41:15', '2020-05-01 17:22:47', 1, 'Q563837'),
(27235, 'Maihingen', 3009, 'BY', 82, 'DE', 48.92745000, 10.49867000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q563837'),
(27236, 'Maikammer', 3019, 'RP', 82, 'DE', 49.30528000, 8.13167000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q22969'),
(27237, 'Mainaschaff', 3009, 'BY', 82, 'DE', 49.98167000, 9.09000000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q505209'),
(27238, 'Mainbernheim', 3009, 'BY', 82, 'DE', 49.70794000, 10.21900000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q504861'),
(27239, 'Mainburg', 3009, 'BY', 82, 'DE', 48.64182000, 11.78093000, '2019-10-05 22:41:15', '2019-10-05 22:41:15', 1, 'Q32116889'),
(27240, 'Mainhardt', 3006, 'BW', 82, 'DE', 49.07611000, 9.55639000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q81038'),
(27241, 'Mainleus', 3009, 'BY', 82, 'DE', 50.09987000, 11.37664000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q304916'),
(27242, 'Mainstockheim', 3009, 'BY', 82, 'DE', 49.77156000, 10.14807000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q505612'),
(27243, 'Maintal', 3018, 'HE', 82, 'DE', 50.15000000, 8.83333000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q14897'),
(27244, 'Mainz', 3019, 'RP', 82, 'DE', 49.98419000, 8.27910000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q1720'),
(27245, 'Mainzweiler', 3020, 'SL', 82, 'DE', 49.41714000, 7.11804000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32117045'),
(27246, 'Maisach', 3009, 'BY', 82, 'DE', 48.21667000, 11.26667000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32117082'),
(27247, 'Maitenbeth', 3009, 'BY', 82, 'DE', 48.15053000, 12.09335000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q162003'),
(27248, 'Malborn', 3019, 'RP', 82, 'DE', 49.71667000, 6.98333000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q569220'),
(27249, 'Malchin', 3007, 'MV', 82, 'DE', 53.73990000, 12.76539000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q50957'),
(27250, 'Malching', 3009, 'BY', 82, 'DE', 48.31226000, 13.18746000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q504208'),
(27251, 'Malchow', 3007, 'MV', 82, 'DE', 53.47477000, 12.42210000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q20291'),
(27252, 'Malente', 3005, 'SH', 82, 'DE', 54.17226000, 10.55968000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q522056'),
(27253, 'Malgersdorf', 3009, 'BY', 82, 'DE', 48.53333000, 12.75000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q585563'),
(27254, 'Mallersdorf-Pfaffenberg', 3009, 'BY', 82, 'DE', 48.76637000, 12.23096000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q261440'),
(27255, 'Malliß', 3007, 'MV', 82, 'DE', 53.21132000, 11.32716000, '2019-10-05 22:41:16', '2020-05-01 17:22:49', 1, 'Q673696'),
(27256, 'Malsch', 3006, 'BW', 82, 'DE', 49.24722000, 8.68278000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q83051'),
(27257, 'Malschwitz', 3021, 'SN', 82, 'DE', 51.23759000, 14.52163000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q93260'),
(27258, 'Malsfeld', 3018, 'HE', 82, 'DE', 51.09083000, 9.53889000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q628513'),
(27259, 'Malterdingen', 3006, 'BW', 82, 'DE', 48.15719000, 7.78608000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q550986'),
(27260, 'Mammelzen', 3019, 'RP', 82, 'DE', 50.70000000, 7.66667000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q562708'),
(27261, 'Mammendorf', 3009, 'BY', 82, 'DE', 48.20836000, 11.16326000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32120109'),
(27262, 'Mamming', 3009, 'BY', 82, 'DE', 48.65174000, 12.60784000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32120121'),
(27263, 'Manching', 3009, 'BY', 82, 'DE', 48.71656000, 11.49393000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q252036'),
(27264, 'Manderscheid', 3019, 'RP', 82, 'DE', 50.09672000, 6.80981000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q252036'),
(27265, 'Mannheim', 3006, 'BW', 82, 'DE', 49.48910000, 8.46694000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q2119'),
(27266, 'Manschnow', 3013, 'BB', 82, 'DE', 52.54990000, 14.55332000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q1769078'),
(27267, 'Mansfeld', 3011, 'ST', 82, 'DE', 51.59234000, 11.45223000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q502926'),
(27268, 'Mantel', 3009, 'BY', 82, 'DE', 49.65412000, 12.04074000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q473172'),
(27269, 'Marbach am Neckar', 3006, 'BW', 82, 'DE', 48.93964000, 9.25995000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q61890'),
(27270, 'Marburg an der Lahn', 3018, 'HE', 82, 'DE', 50.80904000, 8.77069000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q3869'),
(27271, 'Margetshöchheim', 3009, 'BY', 82, 'DE', 49.83750000, 9.86389000, '2019-10-05 22:41:16', '2020-05-01 17:22:47', 1, 'Q515786'),
(27272, 'Mariaposching', 3009, 'BY', 82, 'DE', 48.83333000, 12.80000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q187887'),
(27273, 'Marienberg', 3021, 'SN', 82, 'DE', 50.65051000, 13.16122000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q57951'),
(27274, 'Mariendorf', 3010, 'BE', 82, 'DE', 52.43780000, 13.38109000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q602159'),
(27275, 'Marienfelde', 3010, 'BE', 82, 'DE', 52.41868000, 13.36723000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q695777'),
(27276, 'Marienhafe', 3008, 'NI', 82, 'DE', 53.52274000, 7.27306000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q622159'),
(27277, 'Marienheide', 3017, 'NW', 82, 'DE', 51.08317000, 7.53087000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q11049'),
(27278, 'Marienrachdorf', 3019, 'RP', 82, 'DE', 50.55000000, 7.71667000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q564480'),
(27279, 'Mariental', 3008, 'NI', 82, 'DE', 52.27593000, 10.98371000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q564480'),
(27280, 'Marienthal', 3016, 'HH', 82, 'DE', 53.56667000, 10.08333000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q564480'),
(27281, 'Marienwerder', 3013, 'BB', 82, 'DE', 52.84208000, 13.59927000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q326582'),
(27282, 'Maring-Noviand', 3019, 'RP', 82, 'DE', 49.93333000, 7.00000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q566744'),
(27283, 'Markdorf', 3006, 'BW', 82, 'DE', 47.71916000, 9.39028000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q573239'),
(27284, 'Markersbach', 3021, 'SN', 82, 'DE', 50.53545000, 12.86149000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q703777'),
(27285, 'Markersdorf', 3021, 'SN', 82, 'DE', 51.13333000, 14.88333000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q703777'),
(27286, 'Markgröningen', 3006, 'BW', 82, 'DE', 48.90493000, 9.08059000, '2019-10-05 22:41:16', '2020-05-01 17:22:47', 1, 'Q13896'),
(27287, 'Markkleeberg', 3021, 'SN', 82, 'DE', 51.27550000, 12.36906000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32122989'),
(27288, 'Marklkofen', 3009, 'BY', 82, 'DE', 48.55000000, 12.56667000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32123009'),
(27289, 'Marklohe', 3008, 'NI', 82, 'DE', 52.66857000, 9.14219000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q553030'),
(27290, 'Markneukirchen', 3021, 'SN', 82, 'DE', 50.31144000, 12.32951000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q46875'),
(27291, 'Markranstädt', 3021, 'SN', 82, 'DE', 51.30146000, 12.22020000, '2019-10-05 22:41:16', '2020-05-01 17:22:49', 1, 'Q10770'),
(27292, 'Marksuhl', 3015, 'TH', 82, 'DE', 50.91667000, 10.20000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q634918'),
(27293, 'Markt Berolzheim', 3009, 'BY', 82, 'DE', 49.00955000, 10.84473000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q539787'),
(27294, 'Markt Bibart', 3009, 'BY', 82, 'DE', 49.64787000, 10.42492000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q176619'),
(27295, 'Markt Einersheim', 3009, 'BY', 82, 'DE', 49.68663000, 10.29155000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q504866'),
(27296, 'Markt Erlbach', 3009, 'BY', 82, 'DE', 49.49367000, 10.65265000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q254974'),
(27297, 'Markt Indersdorf', 3009, 'BY', 82, 'DE', 48.36058000, 11.37789000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q513161'),
(27298, 'Markt Nordheim', 3009, 'BY', 82, 'DE', 49.59095000, 10.35564000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q505087'),
(27299, 'Markt Rettenbach', 3009, 'BY', 82, 'DE', 47.94733000, 10.39608000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q527766'),
(27300, 'Markt Schwaben', 3009, 'BY', 82, 'DE', 48.18949000, 11.86910000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q552464'),
(27301, 'Markt Taschendorf', 3009, 'BY', 82, 'DE', 49.70111000, 10.55557000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q505079'),
(27302, 'Markt Wald', 3009, 'BY', 82, 'DE', 48.13680000, 10.58198000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q547819'),
(27303, 'Marktbergel', 3009, 'BY', 82, 'DE', 49.44156000, 10.36355000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q503358'),
(27304, 'Marktbreit', 3009, 'BY', 82, 'DE', 49.66541000, 10.14811000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q522651'),
(27305, 'Marktgraitz', 3009, 'BY', 82, 'DE', 50.18358000, 11.19441000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q502806'),
(27306, 'Marktheidenfeld', 3009, 'BY', 82, 'DE', 49.84540000, 9.60359000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q506528'),
(27307, 'Marktl', 3009, 'BY', 82, 'DE', 48.25516000, 12.84470000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q487328'),
(27308, 'Marktleugast', 3009, 'BY', 82, 'DE', 50.17306000, 11.63389000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q503515'),
(27309, 'Marktleuthen', 3009, 'BY', 82, 'DE', 50.13007000, 12.00226000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q506289'),
(27310, 'Marktoberdorf', 3009, 'BY', 82, 'DE', 47.77964000, 10.61713000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q502955'),
(27311, 'Marktoffingen', 3009, 'BY', 82, 'DE', 48.92566000, 10.47078000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q503510'),
(27312, 'Marktredwitz', 3009, 'BY', 82, 'DE', 50.00443000, 12.08593000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q508231'),
(27313, 'Marktrodach', 3009, 'BY', 82, 'DE', 50.25000000, 11.38333000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q505054'),
(27314, 'Marktschellenberg', 3009, 'BY', 82, 'DE', 47.69657000, 13.04340000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q116883'),
(27315, 'Marktschorgast', 3009, 'BY', 82, 'DE', 50.09475000, 11.65465000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q504662'),
(27316, 'Marktsteft', 3009, 'BY', 82, 'DE', 49.69606000, 10.13626000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q502965'),
(27317, 'Marktzeuln', 3009, 'BY', 82, 'DE', 50.16673000, 11.16692000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q503067'),
(27318, 'Marl', 3017, 'NW', 82, 'DE', 51.65671000, 7.09038000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32123602'),
(27319, 'Marloffstein', 3009, 'BY', 82, 'DE', 49.61794000, 11.06323000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32123602'),
(27320, 'Marlow', 3007, 'MV', 82, 'DE', 54.15439000, 12.57261000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q32123602'),
(27321, 'Marnheim', 3019, 'RP', 82, 'DE', 49.63278000, 8.04000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q562676'),
(27322, 'Maroldsweisach', 3009, 'BY', 82, 'DE', 50.19578000, 10.66003000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q503890'),
(27323, 'Marpingen', 3020, 'SL', 82, 'DE', 49.45228000, 7.05820000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q630853'),
(27324, 'Marquartstein', 3009, 'BY', 82, 'DE', 47.75857000, 12.46219000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q279128'),
(27325, 'Marsberg', 3017, 'NW', 82, 'DE', 51.46171000, 8.84949000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q5645'),
(27326, 'Marschacht', 3008, 'NI', 82, 'DE', 53.41520000, 10.37524000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q630826'),
(27327, 'Martensrade', 3005, 'SH', 82, 'DE', 54.28333000, 10.40000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q636541'),
(27328, 'Martfeld', 3008, 'NI', 82, 'DE', 52.87572000, 9.06081000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q280324'),
(27329, 'Martinhagen', 3018, 'HE', 82, 'DE', 51.28688000, 9.28611000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q1905587'),
(27330, 'Martinsheim', 3009, 'BY', 82, 'DE', 49.62350000, 10.14879000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q504052'),
(27331, 'Martinshöhe', 3019, 'RP', 82, 'DE', 49.36667000, 7.48333000, '2019-10-05 22:41:16', '2020-05-01 17:22:49', 1, 'Q567778'),
(27332, 'Marxen', 3008, 'NI', 82, 'DE', 53.31211000, 10.00415000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q633102');

