INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(95535, 'Oraş Isaccea', 4727, 'TL', 181, 'RO', 45.26900000, 28.40446000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q248018'),
(95536, 'Oraş Jimbolia', 4748, 'TM', 181, 'RO', 45.79226000, 20.71997000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q226000'),
(95537, '<PERSON><PERSON><PERSON>hl<PERSON> Garã', 4732, 'CL', 181, 'RO', 44.42178000, 26.86167000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q912204'),
(95538, '<PERSON><PERSON><PERSON>', 4739, 'AR', 181, 'RO', 46.09061000, 21.69465000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q756598'),
(95539, 'Ora<PERSON> Liteni', 4720, 'SV', 181, 'RO', 47.51068000, 26.53599000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q276119'),
(95540, 'Ora<PERSON> Miercurea Sibiului', 4755, 'SB', 181, 'RO', 45.86247000, 23.79766000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q197687'),
(95541, 'Oraş Mihãileşti', 4726, 'GR', 181, 'RO', 44.31666000, 25.94103000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q963829'),
(95542, 'Oraş Milişãuţi', 4720, 'SV', 181, 'RO', 47.78361000, 26.01317000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q276137'),
(95543, 'Oraş Mizil', 4729, 'PH', 181, 'RO', 45.00844000, 26.44410000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q737830'),
(95544, 'Oraş Murfatlar', 4737, 'CT', 181, 'RO', 44.17090000, 28.38185000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q593157'),
(95545, 'Oraş Mãcin', 4727, 'TL', 181, 'RO', 45.24736000, 28.13830000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q851001'),
(95546, 'Oraş Mãgurele', 4725, 'IF', 181, 'RO', 44.33925000, 26.01403000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q2198771'),
(95547, 'Oraş Mãrãşeşti', 4758, 'VN', 181, 'RO', 45.90446000, 27.22305000, '2019-10-05 23:15:33', '2020-05-01 17:23:12', 1, 'Q576804'),
(95548, 'Oraş Negreşti', 4752, 'VS', 181, 'RO', 46.83188000, 27.47810000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q899448'),
(95549, 'Oraş Negreşti-Oaş', 4746, 'SM', 181, 'RO', 47.86801000, 23.41816000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q772576'),
(95550, 'Oraş Negru Vodã', 4737, 'CT', 181, 'RO', 43.79534000, 28.28041000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q305001'),
(95551, 'Oraş Nehoiu', 4756, 'BZ', 181, 'RO', 45.41538000, 26.32089000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q179297'),
(95552, 'Oraş Nucet', 4723, 'BH', 181, 'RO', 46.48496000, 22.58901000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q906711'),
(95553, 'Oraş Nãdlac', 4739, 'AR', 181, 'RO', 46.16664000, 20.74871000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q508313'),
(95554, 'Oraş Nãsãud', 4733, 'BN', 181, 'RO', 47.27491000, 24.41483000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q651322'),
(95555, 'Oraş Nãvodari', 4737, 'CT', 181, 'RO', 44.30511000, 28.61407000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q837960'),
(95556, 'Oraş Ocna Mureş', 4724, 'AB', 181, 'RO', 46.38475000, 23.84070000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q837152'),
(95557, 'Oraş Ocna Sibiului', 4755, 'SB', 181, 'RO', 45.88311000, 24.00095000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q186719'),
(95558, 'Oraş Ocnele Mari', 4757, 'VL', 181, 'RO', 45.08904000, 24.28162000, '2019-10-05 23:15:33', '2020-05-01 17:23:12', 1, 'Q585548'),
(95559, 'Oraş Odobeşti', 4758, 'VN', 181, 'RO', 45.75011000, 27.08754000, '2019-10-05 23:15:33', '2020-05-01 17:23:12', 1, 'Q743752'),
(95560, 'Oraş Oraviţa', 4753, 'CS', 181, 'RO', 45.05918000, 21.70739000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q383085'),
(95561, 'Oraş Otopeni', 4725, 'IF', 181, 'RO', 44.54217000, 26.06526000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q727421'),
(95562, 'Oraş Ovidiu', 4737, 'CT', 181, 'RO', 44.23828000, 28.53210000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q596043'),
(95563, 'Oraş Oţelu Roşu', 4753, 'CS', 181, 'RO', 45.51638000, 22.38135000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q75537'),
(95564, 'Oraş Panciu', 4758, 'VN', 181, 'RO', 45.90661000, 27.09358000, '2019-10-05 23:15:33', '2020-05-01 17:23:12', 1, 'Q899454'),
(95565, 'Oraş Pantelimon', 4725, 'IF', 181, 'RO', 44.45235000, 26.20684000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q169135'),
(95566, 'Oraş Petrila', 4721, 'HD', 181, 'RO', 45.45352000, 23.43697000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q390156'),
(95567, 'Oraş Piatra-Olt', 4738, 'OT', 181, 'RO', 44.37560000, 24.28332000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q591285'),
(95568, 'Oraş Plopeni', 4729, 'PH', 181, 'RO', 45.04406000, 25.95304000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q751352'),
(95569, 'Oraş Podu Iloaiei', 4735, 'IS', 181, 'RO', 47.20807000, 27.26548000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q275981'),
(95570, 'Oraş Pogoanele', 4756, 'BZ', 181, 'RO', 44.90555000, 27.01183000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q610341'),
(95571, 'Oraş Popeşti Leordeni', 4725, 'IF', 181, 'RO', 44.38005000, 26.17134000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q926163'),
(95572, 'Oraş Potcoava', 4738, 'OT', 181, 'RO', 44.47992000, 24.64172000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q276083'),
(95573, 'Oraş Predeal', 4759, 'BV', 181, 'RO', 45.49679000, 25.58778000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q511951'),
(95574, 'Oraş Pucioasa', 4745, 'DB', 181, 'RO', 45.07570000, 25.43335000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q589880'),
(95575, 'Oraş Pâncota', 4739, 'AR', 181, 'RO', 46.31205000, 21.70163000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q851021'),
(95576, 'Oraş Pãtârlagele', 4756, 'BZ', 181, 'RO', 45.32035000, 26.34539000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q276168'),
(95577, 'Oraş Recaş', 4748, 'TM', 181, 'RO', 45.82646000, 21.52629000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q325555'),
(95578, 'Oraş Rovinari', 4750, 'GJ', 181, 'RO', 44.93857000, 23.15598000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q764806'),
(95579, 'Oraş Roznov', 4731, 'NT', 181, 'RO', 46.83850000, 26.51277000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q275642'),
(95580, 'Oraş Rupea', 4759, 'BV', 181, 'RO', 46.05903000, 25.18717000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q225257'),
(95581, 'Oraş Râşnov', 4759, 'BV', 181, 'RO', 45.59108000, 25.46393000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q790338'),
(95582, 'Oraş Rãcari', 4745, 'DB', 181, 'RO', 44.66037000, 25.74703000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q275990'),
(95583, 'Oraş Salcea', 4720, 'SV', 181, 'RO', 47.64509000, 26.35842000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q253825'),
(95584, 'Oraş Scorniceşti', 4738, 'OT', 181, 'RO', 44.56790000, 24.55394000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q584825'),
(95585, 'Oraş Sebiş', 4739, 'AR', 181, 'RO', 46.38771000, 22.15480000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q507846'),
(95586, 'Oraş Segarcea', 4742, 'DJ', 181, 'RO', 44.09434000, 23.74197000, '2019-10-05 23:15:33', '2020-05-01 17:23:09', 1, 'Q850974'),
(95587, 'Oraş Simeria', 4721, 'HD', 181, 'RO', 45.85296000, 23.00748000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q837154'),
(95588, 'Oraş Sinaia', 4729, 'PH', 181, 'RO', 45.34842000, 25.54937000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q213296'),
(95589, 'Oraş Siret', 4720, 'SV', 181, 'RO', 47.94798000, 26.06875000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q587746'),
(95590, 'Oraş Slãnic', 4729, 'PH', 181, 'RO', 45.23029000, 25.94753000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q1018204'),
(95591, 'Oraş Slãnic-Moldova', 4744, 'BC', 181, 'RO', 46.22320000, 26.47774000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q250006'),
(95592, 'Oraş Solca', 4720, 'SV', 181, 'RO', 47.70078000, 25.83488000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q602184'),
(95593, 'Oraş Strehaia', 4751, 'MH', 181, 'RO', 44.62360000, 23.18792000, '2019-10-05 23:15:33', '2020-05-01 17:23:10', 1, 'Q777619'),
(95594, 'Oraş Sulina', 4727, 'TL', 181, 'RO', 45.15608000, 29.65197000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q328647'),
(95595, 'Oraş Sângeorz-Bãi', 4733, 'BN', 181, 'RO', 47.37780000, 24.66979000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q935834'),
(95596, 'Oraş Sânnicolau Mare', 4748, 'TM', 181, 'RO', 46.07292000, 20.62281000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q837161'),
(95597, 'Oraş Sântana', 4739, 'AR', 181, 'RO', 46.34622000, 21.50402000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q275910'),
(95598, 'Oraş Sãcueni', 4723, 'BH', 181, 'RO', 47.32996000, 22.11802000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q376233'),
(95599, 'Oraş Sãlişte', 4755, 'SB', 181, 'RO', 45.78967000, 23.89532000, '2019-10-05 23:15:33', '2020-05-01 17:23:11', 1, 'Q3573941'),
(95600, 'Oraş Sãveni', 4740, 'BT', 181, 'RO', 47.95998000, 26.86677000, '2019-10-05 23:15:33', '2020-05-01 17:23:08', 1, 'Q247807'),
(95601, 'Oraş Techirghiol', 4737, 'CT', 181, 'RO', 44.05497000, 28.59539000, '2019-10-05 23:15:34', '2020-05-01 17:23:09', 1, 'Q912224'),
(95602, 'Oraş Teiuş', 4724, 'AB', 181, 'RO', 46.20959000, 23.70812000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q946893'),
(95603, 'Oraş Tismana', 4750, 'GJ', 181, 'RO', 45.04464000, 22.93803000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q383306'),
(95604, 'Oraş Titu', 4745, 'DB', 181, 'RO', 44.65747000, 25.55549000, '2019-10-05 23:15:34', '2020-05-01 17:23:09', 1, 'Q280364'),
(95605, 'Oraş Turceni', 4750, 'GJ', 181, 'RO', 44.71779000, 23.35419000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q186214'),
(95606, 'Oraş Târgu Cãrbuneşti', 4750, 'GJ', 181, 'RO', 44.96550000, 23.49567000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q666521'),
(95607, 'Oraş Târgu Frumos', 4735, 'IS', 181, 'RO', 47.20788000, 27.01196000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q756578'),
(95608, 'Oraş Târgu Neamţ', 4731, 'NT', 181, 'RO', 47.19698000, 26.36414000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q756593'),
(95609, 'Oraş Târgu Ocna', 4744, 'BC', 181, 'RO', 46.27988000, 26.61129000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q556266'),
(95610, 'Oraş Tãlmaciu', 4755, 'SB', 181, 'RO', 45.66751000, 24.25357000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q679428'),
(95611, 'Ulmeni', 4760, 'MM', 181, 'RO', 47.46209000, 23.27925000, '2019-10-05 23:15:34', '2021-05-02 17:05:01', 1, 'Q276120'),
(95612, 'Oraş Uricani', 4721, 'HD', 181, 'RO', 45.31733000, 23.08005000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q570113'),
(95613, 'Oraş Urlaţi', 4729, 'PH', 181, 'RO', 44.99692000, 26.24377000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q765841'),
(95614, 'Oraş Valea Lui Mihai', 4723, 'BH', 181, 'RO', 47.52099000, 22.13196000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q252061'),
(95615, 'Oraş Vaşcãu', 4723, 'BH', 181, 'RO', 46.47493000, 22.46292000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q851013'),
(95616, 'Oraş Vicovu De Sus', 4720, 'SV', 181, 'RO', 47.92071000, 25.65842000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q276145'),
(95617, 'Oraş Victoria', 4759, 'BV', 181, 'RO', 45.72968000, 24.70280000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q850969'),
(95618, 'Oraş Videle', 4728, 'TR', 181, 'RO', 44.27660000, 25.57844000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q641218'),
(95619, 'Vişeu De Sus', 4760, 'MM', 181, 'RO', 47.71493000, 24.41707000, '2019-10-05 23:15:34', '2021-05-02 17:05:15', 1, 'Q596609'),
(95620, 'Oraş Vlãhiţa', 4749, 'HR', 181, 'RO', 46.37412000, 25.57625000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q935846'),
(95621, 'Oraş Vânju Mare', 4751, 'MH', 181, 'RO', 44.42326000, 22.87340000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q629776'),
(95622, 'Oraş Vãlenii De Munte', 4729, 'PH', 181, 'RO', 45.18771000, 26.03885000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q592497'),
(95623, 'Oraş Zimnicea', 4728, 'TR', 181, 'RO', 43.65660000, 25.36603000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q203929'),
(95624, 'Oraş Zlatna', 4724, 'AB', 181, 'RO', 46.13753000, 23.20215000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q207196'),
(95625, 'Oraş Zãrneşti', 4759, 'BV', 181, 'RO', 45.55650000, 25.35205000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q248959'),
(95626, 'Oraş abrud', 4724, 'AB', 181, 'RO', 46.27409000, 23.06129000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q331577'),
(95627, 'Oraş Însurãţei', 4736, 'BR', 181, 'RO', 44.91928000, 27.64433000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q292759'),
(95628, 'Oraş Întorsura Buzãului', 4754, 'CV', 181, 'RO', 45.68539000, 26.01260000, '2019-10-05 23:15:34', '2020-05-01 17:23:09', 1, 'Q292769'),
(95629, 'Oraş Şimleu Silvaniei', 4741, 'SJ', 181, 'RO', 47.23018000, 22.79440000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q3846983'),
(95630, 'Oraş Ştefãneşti', 4740, 'BT', 181, 'RO', 47.78834000, 27.20601000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q276154'),
(95631, 'Oraş Ştei', 4723, 'BH', 181, 'RO', 46.53767000, 22.45795000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q423371'),
(95632, 'Oraş Ţicleni', 4750, 'GJ', 181, 'RO', 44.88344000, 23.38921000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q423414'),
(95633, 'Oraş Ţãndãrei', 4743, 'IL', 181, 'RO', 44.64491000, 27.65196000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q423441'),
(95634, 'Oraşu Nou', 4746, 'SM', 181, 'RO', 47.83333000, 23.28333000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q782838'),
(95635, 'Oraș Aleşd', 4723, 'BH', 181, 'RO', 47.08772000, 22.41384000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q851029'),
(95636, 'Oraș Ardud', 4746, 'SM', 181, 'RO', 47.63333000, 22.88333000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q275997'),
(95638, 'Oraș Costeşti', 4722, 'AG', 181, 'RO', 44.66667000, 24.88333000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q850992'),
(95640, 'Oraș Jibou', 4741, 'SJ', 181, 'RO', 47.26001000, 23.25488000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q660417'),
(95641, 'Oraș Livada', 4746, 'SM', 181, 'RO', 47.86667000, 23.13333000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q276336'),
(95642, 'Oraș Mioveni', 4722, 'AG', 181, 'RO', 44.95897000, 24.94274000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q732851'),
(95643, 'Oraș Murgeni', 4752, 'VS', 181, 'RO', 46.20160000, 28.00424000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q276004'),
(95644, 'Seini', 4760, 'MM', 181, 'RO', 47.75000000, 23.28333000, '2019-10-05 23:15:34', '2021-05-02 17:03:42', 1, 'Q666373'),
(95645, 'Săliştea de Sus', 4760, 'MM', 181, 'RO', 47.66019000, 24.35336000, '2019-10-05 23:15:34', '2021-05-02 17:04:15', 1, 'Q276141'),
(95646, 'Oraș Topoloveni', 4722, 'AG', 181, 'RO', 44.81667000, 25.08333000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q739444'),
(95647, 'Oraș Târgu Bujor', 4747, 'GL', 181, 'RO', 45.87398000, 27.92304000, '2019-10-05 23:15:34', '2020-05-01 17:23:09', 1, 'Q592480'),
(95648, 'Târgu Lăpuş', 4760, 'MM', 181, 'RO', 47.46124000, 23.85353000, '2019-10-05 23:15:34', '2021-05-02 17:04:32', 1, 'Q386888'),
(95649, 'Tăuţii-Măgherăuş', 4760, 'MM', 181, 'RO', 47.72129000, 23.48601000, '2019-10-05 23:15:34', '2021-05-02 17:04:47', 1, 'Q930102'),
(95650, 'Oraș Tăşnad', 4746, 'SM', 181, 'RO', 47.48333000, 22.58333000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q668701'),
(95651, 'Şomcuta Mare', 4760, 'MM', 181, 'RO', 47.51667000, 23.46667000, '2019-10-05 23:15:34', '2021-05-02 17:05:30', 1, 'Q275895'),
(95652, 'Oraș Ştefăneşti', 4722, 'AG', 181, 'RO', 44.86667000, 24.93333000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q209977'),
(95653, 'Orbeasca de Jos', 4728, 'TR', 181, 'RO', 44.12177000, 25.32952000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12136210'),
(95654, 'Orbeasca de Sus', 4728, 'TR', 181, 'RO', 44.15000000, 25.31667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12136211'),
(95655, 'Orbeni', 4744, 'BC', 181, 'RO', 46.28333000, 27.01667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q15729379'),
(95656, 'Oreavu', 4756, 'BZ', 181, 'RO', 45.36826000, 27.02301000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12136452'),
(95657, 'Orevița Mare', 4751, 'MH', 181, 'RO', 44.45715000, 22.91317000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q12136283'),
(95658, 'Orlat', 4755, 'SB', 181, 'RO', 45.75000000, 23.96667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q1093109'),
(95659, 'Orlea', 4738, 'OT', 181, 'RO', 43.75000000, 24.38333000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q2719251'),
(95660, 'Orlea Nouă', 4738, 'OT', 181, 'RO', 43.75522000, 24.35834000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q12136402'),
(95661, 'Orleşti', 4757, 'VL', 181, 'RO', 44.80000000, 24.21667000, '2019-10-05 23:15:34', '2020-05-01 17:23:12', 1, 'Q12136362'),
(95662, 'Ormeniş', 4759, 'BV', 181, 'RO', 46.01667000, 25.55000000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q15273043'),
(95663, 'Orodel', 4742, 'DJ', 181, 'RO', 44.23333000, 23.23333000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q2468555'),
(95664, 'Oroftiana', 4740, 'BT', 181, 'RO', 48.17758000, 26.34856000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12136421'),
(95665, 'Orăştie', 4721, 'HD', 181, 'RO', 45.83333000, 23.20000000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q16898317'),
(95666, 'Orăştioara de Sus', 4721, 'HD', 181, 'RO', 45.73333000, 23.16667000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q999643'),
(95667, 'Orășa', 4744, 'BC', 181, 'RO', 46.42225000, 26.74370000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q12136312'),
(95668, 'Orășeni-Deal', 4740, 'BT', 181, 'RO', 47.67597000, 26.67671000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q12136316'),
(95669, 'Orşova', 4751, 'MH', 181, 'RO', 44.72299000, 22.39619000, '2019-10-05 23:15:34', '2020-05-01 17:23:10', 1, 'Q12136316'),
(95670, 'Orţişoara', 4748, 'TM', 181, 'RO', 45.96361000, 21.19833000, '2019-10-05 23:15:34', '2020-05-01 17:23:11', 1, 'Q1022876'),
(95671, 'Osebiți', 4744, 'BC', 181, 'RO', 46.53839000, 26.83946000, '2019-10-05 23:15:34', '2020-05-01 17:23:08', 1, 'Q12136548'),
(95672, 'Osica de Jos', 4738, 'OT', 181, 'RO', 44.24439000, 24.27926000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q2549782'),
(95673, 'Osica de Sus', 4738, 'OT', 181, 'RO', 44.25000000, 24.31667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q2719507'),
(95674, 'Osoi', 4735, 'IS', 181, 'RO', 47.09479000, 27.80091000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12136675'),
(95675, 'Ostra', 4720, 'SV', 181, 'RO', 47.40000000, 25.76667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q281545'),
(95676, 'Ostrov', 4737, 'CT', 181, 'RO', 44.11667000, 27.36667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q1188593'),
(95677, 'Ostrov', 4727, 'TL', 181, 'RO', 44.93333000, 28.15000000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q1066290'),
(95678, 'Ostroveni', 4742, 'DJ', 181, 'RO', 43.80000000, 23.90000000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q2465961'),
(95679, 'Ostrovu', 4729, 'PH', 181, 'RO', 45.20351000, 25.90345000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12136789'),
(95680, 'Ostrovu Mare', 4751, 'MH', 181, 'RO', 44.37931000, 22.51581000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12136786'),
(95681, 'Otelec', 4748, 'TM', 181, 'RO', 45.61478000, 20.84952000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q910065'),
(95682, 'Otetelișu', 4757, 'VL', 181, 'RO', 44.63839000, 23.95917000, '2019-10-05 23:15:34', '2020-05-01 17:23:12', 1, 'Q12136907'),
(95683, 'Oteşani', 4757, 'VL', 181, 'RO', 45.06667000, 24.03333000, '2019-10-05 23:15:34', '2020-05-01 17:23:12', 1, 'Q1857158'),
(95684, 'Otopeni', 4725, 'IF', 181, 'RO', 44.55000000, 26.06667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q1857158'),
(95685, 'Oveselu', 4757, 'VL', 181, 'RO', 44.75000000, 24.01667000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12135076'),
(95686, 'Ovidiu', 4737, 'CT', 181, 'RO', 44.25800000, 28.56083000, '2019-10-05 23:15:34', '2019-10-05 23:15:34', 1, 'Q12135076'),
(95687, 'Ozun', 4754, 'CV', 181, 'RO', 45.80000000, 25.85000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1096772'),
(95688, 'Oşeşti', 4752, 'VS', 181, 'RO', 46.76667000, 27.46667000, '2019-10-05 23:15:35', '2020-05-01 17:23:11', 1, 'Q2547469'),
(95689, 'Oţeleni', 4735, 'IS', 181, 'RO', 47.08333000, 27.03333000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q482084'),
(95690, 'Oţelu Roşu', 4753, 'CS', 181, 'RO', 45.53333000, 22.36667000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q482084'),
(95691, 'Oșorhei', 4723, 'BH', 181, 'RO', 47.03333000, 22.05000000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q1413951'),
(95692, 'Padea', 4742, 'DJ', 181, 'RO', 44.02128000, 23.86827000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137447'),
(95693, 'Padeş', 4750, 'GJ', 181, 'RO', 45.01667000, 22.85000000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q3888742'),
(95694, 'Padina', 4756, 'BZ', 181, 'RO', 44.83333000, 27.11667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12737376'),
(95695, 'Padina Matei', 4753, 'CS', 181, 'RO', 44.75843000, 21.75067000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1188719'),
(95696, 'Padina Mică', 4751, 'MH', 181, 'RO', 44.42722000, 22.99250000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12139345'),
(95697, 'Palanca', 4744, 'BC', 181, 'RO', 46.53333000, 26.11667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q15729510'),
(95698, 'Palanca', 4729, 'PH', 181, 'RO', 44.81562000, 26.19995000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137508'),
(95699, 'Palanca', 4726, 'GR', 181, 'RO', 44.47575000, 25.72565000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137507'),
(95700, 'Palazu Mare', 4737, 'CT', 181, 'RO', 44.22902000, 28.60114000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137507'),
(95701, 'Paleu', 4723, 'BH', 181, 'RO', 47.11531000, 21.95929000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q13990616'),
(95702, 'Paltin', 4758, 'VN', 181, 'RO', 45.78333000, 26.71667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2722204'),
(95703, 'Panaci', 4720, 'SV', 181, 'RO', 47.26667000, 25.38333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2606083'),
(95704, 'Panciu', 4758, 'VN', 181, 'RO', 45.90000000, 27.08333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2606083'),
(95705, 'Pantazi', 4729, 'PH', 181, 'RO', 44.93492000, 26.14020000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12138707'),
(95706, 'Pantelimon', 4725, 'IF', 181, 'RO', 44.45000000, 26.20000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12138707'),
(95707, 'Pantelimon', 4737, 'CT', 181, 'RO', 44.54589000, 28.33094000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1188177'),
(95708, 'Pantelimon de Jos', 4737, 'CT', 181, 'RO', 44.56667000, 28.38333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12138719'),
(95709, 'Panticeu', 4734, 'CJ', 181, 'RO', 47.03333000, 23.56667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q976555'),
(95710, 'Parava', 4744, 'BC', 181, 'RO', 46.30000000, 27.00000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q15729529'),
(95711, 'Pardina', 4727, 'TL', 181, 'RO', 45.30000000, 28.96667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1041607'),
(95712, 'Pardoşi', 4756, 'BZ', 181, 'RO', 45.36667000, 26.90000000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q2538647'),
(95713, 'Parepa-Rușani', 4729, 'PH', 181, 'RO', 44.90577000, 26.35478000, '2019-10-05 23:15:35', '2020-05-01 17:23:11', 1, 'Q12138884'),
(95714, 'Parincea', 4744, 'BC', 181, 'RO', 46.48333000, 27.10000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q15729613'),
(95715, 'Parva', 4733, 'BN', 181, 'RO', 47.40000000, 24.55000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q936447'),
(95716, 'Parța', 4748, 'TM', 181, 'RO', 45.62888000, 21.13877000, '2019-10-05 23:15:35', '2020-05-01 17:23:11', 1, 'Q1191114'),
(95717, 'Patru Frați', 4743, 'IL', 181, 'RO', 44.73672000, 26.47059000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12139172'),
(95718, 'Paşcani', 4735, 'IS', 181, 'RO', 47.24690000, 26.72291000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12139172'),
(95719, 'Peceiu', 4741, 'SJ', 181, 'RO', 47.12415000, 22.86238000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q730041'),
(95720, 'Peceneaga', 4727, 'TL', 181, 'RO', 45.01667000, 28.13333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1041601'),
(95721, 'Pechea', 4747, 'GL', 181, 'RO', 45.63333000, 27.80000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1058724'),
(95722, 'Pecica', 4739, 'AR', 181, 'RO', 46.16667000, 21.06667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q276132'),
(95723, 'Pecineaga', 4737, 'CT', 181, 'RO', 43.90000000, 28.50000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1188252'),
(95724, 'Peciu Nou', 4748, 'TM', 181, 'RO', 45.60639000, 21.05778000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1014818'),
(95725, 'Peregu Mare', 4739, 'AR', 181, 'RO', 46.23333000, 20.90000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1082245'),
(95726, 'Peregu Mic', 4739, 'AR', 181, 'RO', 46.25021000, 20.94885000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q749762'),
(95727, 'Peretu', 4728, 'TR', 181, 'RO', 44.05000000, 25.08333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q957788'),
(95728, 'Periam', 4748, 'TM', 181, 'RO', 46.05000000, 20.86667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1022819'),
(95729, 'Pericei', 4741, 'SJ', 181, 'RO', 47.23333000, 22.88333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1189452'),
(95730, 'Perieni', 4735, 'IS', 181, 'RO', 47.38469000, 27.47453000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12140058'),
(95731, 'Perieni', 4752, 'VS', 181, 'RO', 46.30000000, 27.61667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q524823'),
(95732, 'Perieţi', 4743, 'IL', 181, 'RO', 44.55000000, 27.21667000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12725028'),
(95733, 'Perieţi', 4738, 'OT', 181, 'RO', 44.40000000, 24.55000000, '2019-10-05 23:15:35', '2020-05-01 17:23:11', 1, 'Q749730'),
(95734, 'Perii Broșteni', 4728, 'TR', 181, 'RO', 44.20336000, 25.26377000, '2019-10-05 23:15:35', '2020-05-01 17:23:11', 1, 'Q12140037'),
(95735, 'Periş', 4725, 'IF', 181, 'RO', 44.68472000, 26.01389000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q2294074'),
(95736, 'Perişani', 4757, 'VL', 181, 'RO', 45.38333000, 24.40000000, '2019-10-05 23:15:35', '2020-05-01 17:23:12', 1, 'Q12140051'),
(95737, 'Perişoru', 4732, 'CL', 181, 'RO', 44.45000000, 27.55000000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q12140056'),
(95738, 'Perișor', 4742, 'DJ', 181, 'RO', 44.14331000, 23.47509000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q2342655'),
(95739, 'Perișoru', 4736, 'BR', 181, 'RO', 45.11268000, 27.49817000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q12140055'),
(95740, 'Perșani', 4759, 'BV', 181, 'RO', 45.78160000, 25.21285000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q754642'),
(95741, 'Perșinari', 4745, 'DB', 181, 'RO', 44.80185000, 25.49880000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q12737800'),
(95742, 'Perșunari', 4729, 'PH', 181, 'RO', 45.05682000, 26.44784000, '2019-10-05 23:15:35', '2020-05-01 17:23:11', 1, 'Q12140027'),
(95743, 'Pesac', 4748, 'TM', 181, 'RO', 45.99590000, 20.83522000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q767199'),
(95744, 'Pesceana', 4757, 'VL', 181, 'RO', 44.88333000, 24.15000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q523672'),
(95745, 'Petreu', 4723, 'BH', 181, 'RO', 47.33738000, 22.29607000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q690339'),
(95746, 'Petreşti', 4745, 'DB', 181, 'RO', 44.65000000, 25.33333000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q3726389'),
(95747, 'Petreşti', 4746, 'SM', 181, 'RO', 47.60000000, 22.36667000, '2019-10-05 23:15:35', '2020-05-01 17:23:11', 1, 'Q772158'),
(95748, 'Petreştii de Jos', 4734, 'CJ', 181, 'RO', 46.58333000, 23.65000000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q568095'),
(95749, 'Petrești', 4722, 'AG', 181, 'RO', 45.04364000, 24.88989000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q12140150'),
(95750, 'Petrești', 4724, 'AB', 181, 'RO', 45.92102000, 23.56083000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q597592'),
(95751, 'Petricani', 4731, 'NT', 181, 'RO', 47.16667000, 26.46667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2605732'),
(95752, 'Petrila', 4721, 'HD', 181, 'RO', 45.45000000, 23.41667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2605732'),
(95753, 'Petriş', 4739, 'AR', 181, 'RO', 46.05000000, 22.38333000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q1082215'),
(95754, 'Petriș', 4733, 'BN', 181, 'RO', 47.10301000, 24.62314000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q547433'),
(95755, 'Petrova', 4760, 'MM', 181, 'RO', 47.82981000, 24.21533000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q957084'),
(95756, 'Petroşani', 4721, 'HD', 181, 'RO', 45.41667000, 23.36667000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q957084'),
(95757, 'Petroșnița', 4753, 'CS', 181, 'RO', 45.32351000, 22.25920000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q627112'),
(95758, 'Petrăchioaia', 4725, 'IF', 181, 'RO', 44.58099000, 26.31269000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12140124'),
(95759, 'Peştera', 4737, 'CT', 181, 'RO', 44.18333000, 28.13333000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q1188227'),
(95760, 'Peştişani', 4750, 'GJ', 181, 'RO', 45.06667000, 23.03333000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q2717670'),
(95761, 'Peştişu Mic', 4721, 'HD', 181, 'RO', 45.80000000, 22.88333000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12725031'),
(95762, 'Peșteana Jiu', 4750, 'GJ', 181, 'RO', 44.84695000, 23.30401000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12140429'),
(95763, 'Peșteana de Jos', 4750, 'GJ', 181, 'RO', 44.83333000, 23.26974000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12140430'),
(95764, 'Peștiș', 4723, 'BH', 181, 'RO', 47.07252000, 22.41226000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q12140430'),
(95765, 'Peștișu Mare', 4721, 'HD', 181, 'RO', 45.80192000, 22.92704000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q660908'),
(95766, 'Pianu de Jos', 4724, 'AB', 181, 'RO', 45.93528000, 23.48912000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q570790'),
(95767, 'Pianu de Sus', 4724, 'AB', 181, 'RO', 45.90000000, 23.48333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q568352'),
(95768, 'Piatra', 4738, 'OT', 181, 'RO', 44.36054000, 24.29557000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137160'),
(95769, 'Piatra', 4737, 'CT', 181, 'RO', 44.40492000, 28.56143000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q425933'),
(95770, 'Piatra', 4728, 'TR', 181, 'RO', 43.81667000, 25.16667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2549953'),
(95771, 'Piatra Neamţ', 4731, 'NT', 181, 'RO', 46.91667000, 26.33333000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q2549953'),
(95772, 'Piatra Olt', 4738, 'OT', 181, 'RO', 44.36667000, 24.26667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2549953'),
(95773, 'Piatra Şoimului', 4731, 'NT', 181, 'RO', 46.83333000, 26.43333000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q1087037'),
(95774, 'Picior de Munte', 4745, 'DB', 181, 'RO', 44.78247000, 25.39047000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12146423'),
(95775, 'Pieleştí', 4742, 'DJ', 181, 'RO', 44.33333000, 23.95000000, '2019-10-05 23:15:35', '2020-05-01 17:23:09', 1, 'Q2362074'),
(95776, 'Pietrari', 4745, 'DB', 181, 'RO', 45.09377000, 25.29289000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q3725402'),
(95777, 'Pietrari', 4757, 'VL', 181, 'RO', 45.10000000, 24.13333000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q2029422'),
(95778, 'Pietrarii de Sus', 4757, 'VL', 181, 'RO', 45.11697000, 24.09336000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137202'),
(95779, 'Pietrele', 4726, 'GR', 181, 'RO', 44.06258000, 26.12019000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137204'),
(95780, 'Pietreni', 4737, 'CT', 181, 'RO', 44.09609000, 28.06833000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q6273332'),
(95781, 'Pietriceaua', 4729, 'PH', 181, 'RO', 45.21910000, 25.80859000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137229'),
(95782, 'Pietrișu', 4726, 'GR', 181, 'RO', 43.72508000, 25.67464000, '2019-10-05 23:15:35', '2020-05-01 17:23:10', 1, 'Q12137236'),
(95783, 'Pietroasa', 4748, 'TM', 181, 'RO', 45.83333000, 22.40000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1126619'),
(95784, 'Pietroasa', 4723, 'BH', 181, 'RO', 46.58333000, 22.55000000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q1022812'),
(95785, 'Pietroasele', 4756, 'BZ', 181, 'RO', 45.10000000, 26.56667000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q5112342'),
(95786, 'Pietrosu', 4735, 'IS', 181, 'RO', 47.34118000, 26.60817000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137223'),
(95787, 'Pietrosu', 4756, 'BZ', 181, 'RO', 45.05928000, 26.75827000, '2019-10-05 23:15:35', '2019-10-05 23:15:35', 1, 'Q12137222'),
(95788, 'Pietroşani', 4722, 'AG', 181, 'RO', 45.18333000, 24.85000000, '2019-10-05 23:15:35', '2020-05-01 17:23:08', 1, 'Q2534807'),
(95789, 'Pietroşani', 4728, 'TR', 181, 'RO', 43.71383000, 25.64409000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q2721383'),
(95790, 'Pietroşiţa', 4745, 'DB', 181, 'RO', 45.18333000, 25.43333000, '2019-10-05 23:15:36', '2020-05-01 17:23:09', 1, 'Q2606462'),
(95791, 'Pietroșani', 4729, 'PH', 181, 'RO', 44.83963000, 26.10382000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12137228'),
(95792, 'Pildești', 4731, 'NT', 181, 'RO', 46.99675000, 26.82558000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q1106459'),
(95793, 'Pilu', 4739, 'AR', 181, 'RO', 46.56667000, 21.35000000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q1092650'),
(95794, 'Pipirig', 4731, 'NT', 181, 'RO', 47.25000000, 26.06667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2605652'),
(95795, 'Pir', 4746, 'SM', 181, 'RO', 47.46667000, 22.36667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q1076592'),
(95796, 'Piscoiu', 4750, 'GJ', 181, 'RO', 44.85211000, 23.75387000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12146320'),
(95797, 'Piscu', 4747, 'GL', 181, 'RO', 45.50000000, 27.73333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2606395'),
(95798, 'Piscu Nou', 4742, 'DJ', 181, 'RO', 43.92400000, 23.17917000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12146327'),
(95799, 'Piscu Sadovei', 4742, 'DJ', 181, 'RO', 43.87729000, 23.93687000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12146330'),
(95800, 'Piscu Vechi', 4742, 'DJ', 181, 'RO', 43.90000000, 23.16667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2335002'),
(95801, 'Pitaru', 4745, 'DB', 181, 'RO', 44.59061000, 25.58407000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12146375'),
(95802, 'Piteasca', 4725, 'IF', 181, 'RO', 44.49339000, 26.33025000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12146394'),
(95803, 'Piteşti', 4722, 'AG', 181, 'RO', 44.85000000, 24.86667000, '2019-10-05 23:15:36', '2020-05-01 17:23:08', 1, 'Q12146394'),
(95804, 'Pişchia', 4748, 'TM', 181, 'RO', 45.90306000, 21.33722000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q598493'),
(95805, 'Pişcolt', 4746, 'SM', 181, 'RO', 47.58333000, 22.30000000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q1189068'),
(95806, 'Platonești', 4743, 'IL', 181, 'RO', 44.60712000, 27.69706000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q13229911'),
(95807, 'Pleașa', 4729, 'PH', 181, 'RO', 45.00583000, 26.07211000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12141131'),
(95808, 'Pleașov', 4728, 'TR', 181, 'RO', 43.84971000, 24.74886000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12141134'),
(95809, 'Pleniţa', 4742, 'DJ', 181, 'RO', 44.21667000, 23.18333000, '2019-10-05 23:15:36', '2020-05-01 17:23:09', 1, 'Q12140867'),
(95810, 'Plevna', 4756, 'BZ', 181, 'RO', 45.37815000, 27.01310000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12140853'),
(95811, 'Pleşcuţa', 4739, 'AR', 181, 'RO', 46.30000000, 22.43333000, '2019-10-05 23:15:36', '2020-05-01 17:23:08', 1, 'Q15044300'),
(95812, 'Pleşoiu', 4738, 'OT', 181, 'RO', 44.46667000, 24.26667000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q2719156'),
(95813, 'Pleșoi', 4742, 'DJ', 181, 'RO', 44.35077000, 23.53345000, '2019-10-05 23:15:36', '2020-05-01 17:23:09', 1, 'Q12140902'),
(95814, 'Ploieşti', 4729, 'PH', 181, 'RO', 44.95000000, 26.01667000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12140902'),
(95815, 'Ploieștiori', 4729, 'PH', 181, 'RO', 44.98157000, 26.01795000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12141086'),
(95816, 'Plopana', 4744, 'BC', 181, 'RO', 46.68333000, 27.21667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q15732446'),
(95817, 'Plopeni', 4720, 'SV', 181, 'RO', 47.65874000, 26.33913000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q225102'),
(95818, 'Plopeni', 4737, 'CT', 181, 'RO', 43.95089000, 28.19452000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12140938'),
(95819, 'Plopeni', 4729, 'PH', 181, 'RO', 45.06552000, 25.98114000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12140936'),
(95820, 'Plopii Slăviţeştí', 4728, 'TR', 181, 'RO', 43.96667000, 24.68333000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q2720032'),
(95821, 'Plopiş', 4741, 'SJ', 181, 'RO', 47.13333000, 22.68333000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q1085340'),
(95822, 'Plopu', 4736, 'BR', 181, 'RO', 45.14847000, 27.51387000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12140944'),
(95823, 'Plopu', 4729, 'PH', 181, 'RO', 45.01667000, 26.15000000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q14237772'),
(95824, 'Plopu', 4744, 'BC', 181, 'RO', 46.39256000, 26.50268000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12140947'),
(95825, 'Plopşoru', 4750, 'GJ', 181, 'RO', 44.78333000, 23.35000000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q531665'),
(95826, 'Plopșoru', 4726, 'GR', 181, 'RO', 44.01190000, 25.99306000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q12140956'),
(95827, 'Plosca', 4728, 'TR', 181, 'RO', 44.03333000, 25.13333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2721368'),
(95828, 'Ploscoș', 4734, 'CJ', 181, 'RO', 46.64303000, 23.84663000, '2019-10-05 23:15:36', '2020-05-01 17:23:09', 1, 'Q767369'),
(95829, 'Ploscuțeni', 4758, 'VN', 181, 'RO', 46.07859000, 27.27032000, '2019-10-05 23:15:36', '2020-05-01 17:23:12', 1, 'Q5070548'),
(95830, 'Ploștina', 4750, 'GJ', 181, 'RO', 44.82398000, 22.98791000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q5070548'),
(95831, 'Plugari', 4735, 'IS', 181, 'RO', 47.48333000, 27.10000000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2606010'),
(95832, 'Plugova', 4753, 'CS', 181, 'RO', 44.96250000, 22.35864000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q632565'),
(95833, 'Plutonița', 4720, 'SV', 181, 'RO', 47.48081000, 25.79145000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12141107'),
(95834, 'Plătăreşti', 4732, 'CL', 181, 'RO', 44.35000000, 26.36667000, '2019-10-05 23:15:36', '2020-05-01 17:23:09', 1, 'Q12140882'),
(95835, 'Plăvălari', 4720, 'SV', 181, 'RO', 47.56832000, 26.35925000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12140850'),
(95836, 'Poarta Albă', 4737, 'CT', 181, 'RO', 44.21667000, 28.40000000, '2019-10-05 23:15:36', '2020-05-01 17:23:09', 1, 'Q5052748'),
(95837, 'Poboru', 4738, 'OT', 181, 'RO', 44.66667000, 24.50000000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2605134'),
(95838, 'Pochidia', 4752, 'VS', 181, 'RO', 46.04318000, 27.58746000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2297467'),
(95839, 'Pociovaliștea', 4750, 'GJ', 181, 'RO', 45.15315000, 23.64392000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q12143033'),
(95840, 'Pocola', 4723, 'BH', 181, 'RO', 46.68333000, 22.28333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q1195562'),
(95841, 'Pocruia', 4750, 'GJ', 181, 'RO', 45.04470000, 22.92495000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141725'),
(95842, 'Podari', 4742, 'DJ', 181, 'RO', 44.25000000, 23.78333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2465789'),
(95843, 'Podeni', 4751, 'MH', 181, 'RO', 44.88139000, 22.54333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2536675'),
(95844, 'Podenii Noi', 4729, 'PH', 181, 'RO', 45.11667000, 26.16667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q6455482'),
(95845, 'Podenii Vechi', 4729, 'PH', 181, 'RO', 45.08939000, 26.11584000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141420'),
(95846, 'Podgoria', 4756, 'BZ', 181, 'RO', 45.41667000, 27.01667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141409'),
(95847, 'Podoleni', 4747, 'GL', 181, 'RO', 45.74830000, 27.44661000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141440'),
(95848, 'Podoleni', 4731, 'NT', 181, 'RO', 46.80000000, 26.61667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2605634'),
(95849, 'Podolenii de Sus', 4735, 'IS', 181, 'RO', 46.85716000, 28.01545000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141441'),
(95850, 'Podu Iloaiei', 4735, 'IS', 181, 'RO', 47.21667000, 27.26667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141441'),
(95851, 'Podu Turcului', 4744, 'BC', 181, 'RO', 46.20000000, 27.38333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q15735972'),
(95852, 'Poduri', 4744, 'BC', 181, 'RO', 46.46667000, 26.53333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141509'),
(95853, 'Poeni', 4728, 'TR', 181, 'RO', 44.40000000, 25.33333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2549893'),
(95854, 'Pogana', 4752, 'VS', 181, 'RO', 46.31667000, 27.56667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2548911'),
(95855, 'Pogoanele', 4756, 'BZ', 181, 'RO', 44.91667000, 27.00000000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2548911'),
(95856, 'Pogonele', 4756, 'BZ', 181, 'RO', 45.08645000, 26.86454000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12141320'),
(95857, 'Pogonești', 4752, 'VS', 181, 'RO', 46.15229000, 27.53225000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q2298030'),
(95858, 'Pogănești', 4752, 'VS', 181, 'RO', 46.69237000, 28.16779000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12141313'),
(95859, 'Poian', 4754, 'CV', 181, 'RO', 46.06667000, 26.15000000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q1094523'),
(95860, 'Poiana', 4728, 'TR', 181, 'RO', 43.74192000, 24.95244000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q1094523'),
(95861, 'Poiana', 4750, 'GJ', 181, 'RO', 44.65174000, 23.53148000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12143123'),
(95862, 'Poiana', 4735, 'IS', 181, 'RO', 47.49299000, 26.86387000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12143126'),
(95863, 'Poiana', 4740, 'BT', 181, 'RO', 47.60149000, 26.60826000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12143122'),
(95864, 'Poiana', 4729, 'PH', 181, 'RO', 45.24536000, 25.66302000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12143122'),
(95865, 'Poiana', 4745, 'DB', 181, 'RO', 44.56667000, 25.68333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12143127'),
(95866, 'Poiana', 4744, 'BC', 181, 'RO', 46.69819000, 27.01765000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q12143139'),
(95867, 'Poiana', 4747, 'GL', 181, 'RO', 45.99228000, 27.25609000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q913164'),
(95868, 'Poiana Blenchii', 4741, 'SJ', 181, 'RO', 47.30000000, 23.75000000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q1191342'),
(95869, 'Poiana Codrului', 4746, 'SM', 181, 'RO', 47.62667000, 23.25056000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q728800'),
(95870, 'Poiana Copăceni', 4729, 'PH', 181, 'RO', 45.16392000, 26.06918000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12143083'),
(95871, 'Poiana Cristei', 4758, 'VN', 181, 'RO', 45.65000000, 26.98333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q13571725'),
(95872, 'Poiana Crăcăoani', 4731, 'NT', 181, 'RO', 47.06667000, 26.31667000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q12143084'),
(95873, 'Poiana Câmpina', 4729, 'PH', 181, 'RO', 45.13333000, 25.68333000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12143082'),
(95874, 'Poiana Ilvei', 4733, 'BN', 181, 'RO', 47.35749000, 24.74104000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q940659'),
(95875, 'Poiana Lacului', 4722, 'AG', 181, 'RO', 44.81667000, 24.73333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q2348418'),
(95876, 'Poiana Mare', 4742, 'DJ', 181, 'RO', 43.91667000, 23.06667000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q587543'),
(95877, 'Poiana Mărului', 4735, 'IS', 181, 'RO', 47.37951000, 26.89243000, '2019-10-05 23:15:36', '2020-05-01 17:23:10', 1, 'Q12143096'),
(95878, 'Poiana Mărului', 4720, 'SV', 181, 'RO', 47.41188000, 26.04077000, '2019-10-05 23:15:36', '2020-05-01 17:23:11', 1, 'Q12143095'),
(95879, 'Poiana Mărului (Brașov)', 4759, 'BV', 181, 'RO', 45.60000000, 25.30000000, '2019-10-05 23:15:36', '2020-05-01 17:23:08', 1, 'Q15273072'),
(95880, 'Poiana Sibiului', 4755, 'SB', 181, 'RO', 45.80000000, 23.73333000, '2019-10-05 23:15:36', '2019-10-05 23:15:36', 1, 'Q682687'),
(95881, 'Poiana Stampei', 4720, 'SV', 181, 'RO', 47.31667000, 25.13333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1089026'),
(95882, 'Poiana Teiului', 4731, 'NT', 181, 'RO', 47.10000000, 25.96667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2605619'),
(95883, 'Poiana Vadului', 4724, 'AB', 181, 'RO', 46.40000000, 22.88333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q691885'),
(95884, 'Poiana Țapului', 4729, 'PH', 181, 'RO', 45.39344000, 25.54066000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q12143110'),
(95885, 'Poienari', 4731, 'NT', 181, 'RO', 46.88777000, 27.12765000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12143110'),
(95886, 'Poienari', 4722, 'AG', 181, 'RO', 45.21667000, 25.05000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12143163'),
(95887, 'Poienarii Apostoli', 4729, 'PH', 181, 'RO', 44.77204000, 26.05388000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12143166'),
(95888, 'Poienarii Burchii', 4729, 'PH', 181, 'RO', 44.75000000, 26.01667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2720385'),
(95889, 'Poieneşti', 4752, 'VS', 181, 'RO', 46.61667000, 27.53333000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q2547526'),
(95890, 'Poieni', 4731, 'NT', 181, 'RO', 46.83333000, 26.35000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12143180'),
(95891, 'Poieni', 4734, 'CJ', 181, 'RO', 46.91880000, 22.85826000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q367346'),
(95892, 'Poieni-Solca', 4720, 'SV', 181, 'RO', 47.68626000, 25.89374000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2720390'),
(95893, 'Poienile', 4735, 'IS', 181, 'RO', 46.93333000, 27.18333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12143192'),
(95894, 'Poienile Izei', 4760, 'MM', 181, 'RO', 47.70000000, 24.11667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1090023'),
(95895, 'Poienile de sub Munte', 4760, 'MM', 181, 'RO', 47.81667000, 24.43333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1006101'),
(95896, 'Poienile-Mogoş', 4724, 'AB', 181, 'RO', 46.28333000, 23.28333000, '2019-10-05 23:15:37', '2020-05-01 17:23:08', 1, 'Q10862270'),
(95897, 'Pojejena', 4753, 'CS', 181, 'RO', 44.77417000, 21.57917000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q673020'),
(95898, 'Pojogeni', 4750, 'GJ', 181, 'RO', 44.99119000, 23.48425000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12141591'),
(95899, 'Pojorâta', 4720, 'SV', 181, 'RO', 47.51667000, 25.45000000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q2465486'),
(95900, 'Poloboc', 4731, 'NT', 181, 'RO', 46.76109000, 26.54975000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12141788'),
(95901, 'Polovragi', 4750, 'GJ', 181, 'RO', 45.18333000, 23.80000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2717722'),
(95902, 'Pomezeu', 4723, 'BH', 181, 'RO', 46.78333000, 22.28333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1194305'),
(95903, 'Pomi', 4746, 'SM', 181, 'RO', 47.70000000, 23.31667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1188978'),
(95904, 'Pomârla', 4740, 'BT', 181, 'RO', 48.06667000, 26.31667000, '2019-10-05 23:15:37', '2020-05-01 17:23:08', 1, 'Q2715857'),
(95905, 'Ponoarele', 4751, 'MH', 181, 'RO', 44.97389000, 22.76444000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12142246'),
(95906, 'Ponor', 4724, 'AB', 181, 'RO', 46.33333000, 23.41667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q937236'),
(95907, 'Popeni', 4752, 'VS', 181, 'RO', 46.24667000, 27.80520000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12142324'),
(95908, 'Popeşti', 4722, 'AG', 181, 'RO', 44.44181000, 25.10007000, '2019-10-05 23:15:37', '2020-05-01 17:23:08', 1, 'Q2534759'),
(95909, 'Popeşti', 4735, 'IS', 181, 'RO', 47.15000000, 27.23333000, '2019-10-05 23:15:37', '2020-05-01 17:23:10', 1, 'Q2718524'),
(95910, 'Popeşti', 4757, 'VL', 181, 'RO', 44.98333000, 24.10000000, '2019-10-05 23:15:37', '2020-05-01 17:23:12', 1, 'Q2298513'),
(95911, 'Popeşti', 4723, 'BH', 181, 'RO', 47.23333000, 22.41667000, '2019-10-05 23:15:37', '2020-05-01 17:23:08', 1, 'Q1189455'),
(95912, 'Popeşti-Leordeni', 4725, 'IF', 181, 'RO', 44.38333000, 26.16667000, '2019-10-05 23:15:37', '2020-05-01 17:23:10', 1, 'Q16898374'),
(95913, 'Popești', 4726, 'GR', 181, 'RO', 44.30444000, 25.96370000, '2019-10-05 23:15:37', '2020-05-01 17:23:10', 1, 'Q12142346'),
(95914, 'Popești', 4758, 'VN', 181, 'RO', 45.59533000, 27.07916000, '2019-10-05 23:15:37', '2020-05-01 17:23:12', 1, 'Q1405788'),
(95915, 'Popești', 4729, 'PH', 181, 'RO', 44.86076000, 25.99557000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q12142338'),
(95916, 'Poplaca', 4755, 'SB', 181, 'RO', 45.71667000, 24.05000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q250997'),
(95917, 'Poporogi', 4728, 'TR', 181, 'RO', 44.36667000, 25.18333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q250997'),
(95918, 'Popoveni', 4742, 'DJ', 181, 'RO', 44.28800000, 23.78208000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q250997'),
(95919, 'Popricani', 4735, 'IS', 181, 'RO', 47.30000000, 27.51667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2605991'),
(95920, 'Poroina Mare', 4751, 'MH', 181, 'RO', 44.49528000, 22.93639000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q954644'),
(95921, 'Poroschia', 4728, 'TR', 181, 'RO', 43.93333000, 25.36667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2721363'),
(95922, 'Porumbacu de Jos', 4755, 'SB', 181, 'RO', 45.75000000, 24.45000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q678912'),
(95923, 'Porumbenii Mari', 4749, 'HR', 181, 'RO', 46.27549000, 25.13620000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q6302252'),
(95924, 'Porumbești', 4746, 'SM', 181, 'RO', 47.98305000, 22.98076000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q946977'),
(95925, 'Posada', 4729, 'PH', 181, 'RO', 45.28714000, 25.61882000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q946977'),
(95926, 'Poseștii-Pământeni', 4729, 'PH', 181, 'RO', 45.26667000, 26.15000000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q12142639'),
(95927, 'Potcoava', 4732, 'CL', 181, 'RO', 44.26332000, 27.13223000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12738395'),
(95928, 'Potcoava', 4738, 'OT', 181, 'RO', 44.48333000, 24.65000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12738395'),
(95929, 'Potcoava Fălcoeni', 4738, 'OT', 181, 'RO', 44.49484000, 24.61650000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q12142861'),
(95930, 'Potelu', 4738, 'OT', 181, 'RO', 43.76577000, 24.20535000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12142848'),
(95931, 'Potigrafu', 4729, 'PH', 181, 'RO', 44.78504000, 26.10014000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12142905'),
(95932, 'Potlogeni', 4738, 'OT', 181, 'RO', 43.87995000, 24.63092000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q6271730'),
(95933, 'Potlogi', 4745, 'DB', 181, 'RO', 44.55000000, 25.58333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q3726296'),
(95934, 'Potoceni', 4756, 'BZ', 181, 'RO', 45.19660000, 26.77649000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12142881'),
(95935, 'Potău', 4746, 'SM', 181, 'RO', 47.75673000, 23.12090000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q724237'),
(95936, 'Poşaga de Sus', 4724, 'AB', 181, 'RO', 46.46667000, 23.38333000, '2019-10-05 23:15:37', '2020-05-01 17:23:08', 1, 'Q390347'),
(95937, 'Poşta Câlnău', 4756, 'BZ', 181, 'RO', 45.23333000, 26.85000000, '2019-10-05 23:15:37', '2020-05-01 17:23:09', 1, 'Q2716702'),
(95938, 'Praid', 4749, 'HR', 181, 'RO', 46.55000000, 25.13333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1077203'),
(95939, 'Prajila', 4757, 'VL', 181, 'RO', 45.08270000, 24.16949000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1077203'),
(95940, 'Prapor', 4742, 'DJ', 181, 'RO', 43.94487000, 24.17475000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12143321'),
(95941, 'Preajba', 4742, 'DJ', 181, 'RO', 44.26511000, 23.85024000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q390062'),
(95942, 'Predeal', 4759, 'BV', 181, 'RO', 45.50000000, 25.56667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q390062'),
(95943, 'Predeşti', 4742, 'DJ', 181, 'RO', 44.35000000, 23.60000000, '2019-10-05 23:15:37', '2020-05-01 17:23:09', 1, 'Q266526'),
(95944, 'Prejmer', 4759, 'BV', 181, 'RO', 45.71667000, 25.76667000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q15273086'),
(95945, 'Prelipca', 4720, 'SV', 181, 'RO', 47.61085000, 26.35414000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12143860'),
(95946, 'Preuteşti', 4720, 'SV', 181, 'RO', 47.45000000, 26.41667000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q2606106'),
(95947, 'Pribești', 4752, 'VS', 181, 'RO', 46.91001000, 27.79659000, '2019-10-05 23:15:37', '2020-05-01 17:23:11', 1, 'Q12145354'),
(95948, 'Priboieni', 4722, 'AG', 181, 'RO', 44.88333000, 25.08333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1921758'),
(95949, 'Priboiu', 4745, 'DB', 181, 'RO', 45.03006000, 25.25790000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q12145358'),
(95950, 'Pricaz', 4721, 'HD', 181, 'RO', 45.85929000, 23.17266000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q639788'),
(95951, 'Prigor', 4753, 'CS', 181, 'RO', 44.93833000, 22.11333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1084299'),
(95952, 'Prigoria', 4750, 'GJ', 181, 'RO', 45.06667000, 23.68333000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2717907'),
(95953, 'Priponeşti', 4747, 'GL', 181, 'RO', 46.08333000, 27.43333000, '2019-10-05 23:15:37', '2020-05-01 17:23:09', 1, 'Q12725062'),
(95954, 'Priseaca', 4738, 'OT', 181, 'RO', 44.51667000, 24.45000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2719473'),
(95955, 'Pristol', 4751, 'MH', 181, 'RO', 44.22472000, 22.70917000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q1659448'),
(95956, 'Prisăcani', 4735, 'IS', 181, 'RO', 47.08333000, 27.88333000, '2019-10-05 23:15:37', '2020-05-01 17:23:10', 1, 'Q2607545'),
(95957, 'Probota', 4735, 'IS', 181, 'RO', 47.38333000, 27.50000000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q2001420'),
(95958, 'Probota', 4720, 'SV', 181, 'RO', 47.37584000, 26.62415000, '2019-10-05 23:15:37', '2019-10-05 23:15:37', 1, 'Q8273806'),
(95959, 'Produleşti', 4745, 'DB', 181, 'RO', 44.70000000, 25.50000000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q3732803'),
(95960, 'Progresu', 4732, 'CL', 181, 'RO', 44.35886000, 26.46110000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q12144725'),
(95961, 'Prohozești', 4744, 'BC', 181, 'RO', 46.48155000, 26.55170000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q12145189'),
(95962, 'Proviţa de Jos', 4729, 'PH', 181, 'RO', 45.11667000, 25.65000000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q5543718'),
(95963, 'Proviţa de Sus', 4729, 'PH', 181, 'RO', 45.13333000, 25.63333000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q14241492'),
(95964, 'Prundeni', 4757, 'VL', 181, 'RO', 44.73333000, 24.26667000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q2721534'),
(95965, 'Prundu', 4728, 'TR', 181, 'RO', 43.82394000, 24.69196000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q12145284'),
(95966, 'Prundu', 4726, 'GR', 181, 'RO', 44.09472000, 26.22694000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q2717441'),
(95967, 'Prundu Bârgăului', 4733, 'BN', 181, 'RO', 47.21975000, 24.74123000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q834898'),
(95968, 'Prunişor', 4751, 'MH', 181, 'RO', 44.60944000, 22.91528000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q921751'),
(95969, 'Prăjani', 4729, 'PH', 181, 'RO', 45.20422000, 25.95151000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q921751'),
(95970, 'Prăjeni', 4740, 'BT', 181, 'RO', 47.51667000, 27.01667000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q2715845'),
(95971, 'Prăjești', 4744, 'BC', 181, 'RO', 46.65469000, 26.97631000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q2284210'),
(95972, 'Pucheni', 4745, 'DB', 181, 'RO', 45.18333000, 25.28333000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q3732678'),
(95973, 'Puchenii Mari', 4729, 'PH', 181, 'RO', 44.81667000, 26.08333000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q6399490'),
(95974, 'Puchenii Moșneni', 4729, 'PH', 181, 'RO', 44.81213000, 26.08701000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q12145539'),
(95975, 'Pucioasa', 4745, 'DB', 181, 'RO', 45.07807000, 25.43232000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q12145539'),
(95976, 'Pufeşti', 4758, 'VN', 181, 'RO', 46.00000000, 27.20000000, '2019-10-05 23:15:38', '2020-05-01 17:23:12', 1, 'Q12145673'),
(95977, 'Pui', 4721, 'HD', 181, 'RO', 45.51667000, 23.10000000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q5062644'),
(95978, 'Puieni', 4726, 'GR', 181, 'RO', 44.07675000, 26.19114000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q12145741'),
(95979, 'Puieşti', 4752, 'VS', 181, 'RO', 46.41667000, 27.50000000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q2548829'),
(95980, 'Puieștii de Jos', 4756, 'BZ', 181, 'RO', 45.40267000, 27.22013000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q12145744'),
(95981, 'Pungeşti', 4752, 'VS', 181, 'RO', 46.70000000, 27.33333000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q2547302'),
(95982, 'Punghina', 4751, 'MH', 181, 'RO', 44.28194000, 22.93472000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q2465407'),
(95983, 'Purani', 4728, 'TR', 181, 'RO', 43.97989000, 25.43656000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q12145591'),
(95984, 'Purcăreni', 4759, 'BV', 181, 'RO', 45.64476000, 25.79129000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q740551'),
(95985, 'Purcăreni', 4722, 'AG', 181, 'RO', 44.96847000, 24.88736000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q740551'),
(95986, 'Pusta', 4741, 'SJ', 181, 'RO', 47.24596000, 22.72374000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q740551'),
(95987, 'Pustiana', 4744, 'BC', 181, 'RO', 46.58990000, 26.63083000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q1241794'),
(95988, 'Putineiu', 4726, 'GR', 181, 'RO', 43.90000000, 25.73333000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q2717681'),
(95989, 'Putineiu', 4728, 'TR', 181, 'RO', 43.90000000, 24.96667000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q2721391'),
(95990, 'Putna', 4720, 'SV', 181, 'RO', 47.86667000, 25.61667000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q221413'),
(95991, 'Pușcași', 4752, 'VS', 181, 'RO', 46.62154000, 27.64503000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q2298042'),
(95992, 'Puțuri', 4742, 'DJ', 181, 'RO', 44.13835000, 24.01017000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q12145700'),
(95993, 'Pânceşti', 4731, 'NT', 181, 'RO', 46.90296000, 27.15314000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q12140536'),
(95994, 'Pâncota', 4739, 'AR', 181, 'RO', 46.33333000, 21.70000000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q12140536'),
(95995, 'Pângăraţi', 4731, 'NT', 181, 'RO', 46.93333000, 26.15000000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q2719407'),
(95996, 'Pângărăcior', 4731, 'NT', 181, 'RO', 46.93333000, 26.21667000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q12140530'),
(95997, 'Pâraie', 4720, 'SV', 181, 'RO', 47.46299000, 26.06608000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q12140541'),
(95998, 'Pârcovaci', 4735, 'IS', 181, 'RO', 47.43586000, 26.85075000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q12140571'),
(95999, 'Pârgăreşti', 4744, 'BC', 181, 'RO', 46.25000000, 26.65000000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q3600554'),
(96000, 'Pârjol', 4744, 'BC', 181, 'RO', 46.58333000, 26.60000000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q15732413'),
(96001, 'Pârscov', 4756, 'BZ', 181, 'RO', 45.28333000, 26.55000000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q12140589'),
(96002, 'Pârâu', 4759, 'BV', 181, 'RO', 45.85000000, 25.18333000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q15273062'),
(96003, 'Pârşcoveni', 4738, 'OT', 181, 'RO', 44.30000000, 24.23333000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q2739131'),
(96004, 'Păcureţi', 4729, 'PH', 181, 'RO', 45.15000000, 26.13333000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q14195525'),
(96005, 'Pădurea Neagră', 4723, 'BH', 181, 'RO', 47.16554000, 22.41323000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q14195525'),
(96006, 'Pădureni', 4740, 'BT', 181, 'RO', 47.97434000, 26.31672000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q12139329'),
(96007, 'Pădureni', 4752, 'VS', 181, 'RO', 46.62192000, 28.08258000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q2297481'),
(96008, 'Pădureni', 4748, 'TM', 181, 'RO', 45.60039000, 21.21773000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q663305'),
(96009, 'Pădureni', 4726, 'GR', 181, 'RO', 44.34576000, 25.80469000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q663305'),
(96010, 'Pădureni', 4734, 'CJ', 181, 'RO', 46.59316000, 24.02700000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q2585692'),
(96011, 'Pălatca', 4734, 'CJ', 181, 'RO', 46.85000000, 23.98333000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q591983'),
(96012, 'Păltineni', 4756, 'BZ', 181, 'RO', 45.39596000, 26.32334000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q12139401'),
(96013, 'Păltiniş', 4753, 'CS', 181, 'RO', 45.43333000, 22.15000000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q75615'),
(96014, 'Păltiniş', 4740, 'BT', 181, 'RO', 48.21667000, 26.65000000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q2534318'),
(96015, 'Păltinoasa', 4720, 'SV', 181, 'RO', 47.55000000, 25.95000000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q2606100'),
(96016, 'Pănceşti', 4744, 'BC', 181, 'RO', 46.33333000, 27.08333000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q15729572'),
(96018, 'Pănătău', 4756, 'BZ', 181, 'RO', 45.32113000, 26.39442000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q12139431'),
(96019, 'Păpăuți', 4754, 'CV', 181, 'RO', 45.78333000, 26.13333000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q714004'),
(96020, 'Părhăuți', 4720, 'SV', 181, 'RO', 47.71057000, 26.09012000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q12139960'),
(96021, 'Părteştii de Jos', 4720, 'SV', 181, 'RO', 47.63333000, 25.96667000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q2606038'),
(96022, 'Păstrăveni', 4731, 'NT', 181, 'RO', 47.16667000, 26.56667000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q2605725'),
(96023, 'Pătrăuţi', 4720, 'SV', 181, 'RO', 47.71667000, 26.20000000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q977735'),
(96024, 'Pătulele', 4751, 'MH', 181, 'RO', 44.34528000, 22.77278000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q12140340'),
(96025, 'Pătuleni', 4722, 'AG', 181, 'RO', 44.73247000, 25.16495000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q12140342'),
(96026, 'Pătârlagele', 4756, 'BZ', 181, 'RO', 45.32081000, 26.36134000, '2019-10-05 23:15:38', '2020-05-01 17:23:09', 1, 'Q12140342'),
(96027, 'Păuca', 4755, 'SB', 181, 'RO', 46.01667000, 23.90000000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q1188209'),
(96028, 'Păuleni-Ciuc', 4749, 'HR', 181, 'RO', 46.40000000, 25.83333000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q875967'),
(96029, 'Păuleşti', 4729, 'PH', 181, 'RO', 45.00000000, 25.98333000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q14237692'),
(96030, 'Păuleşti', 4746, 'SM', 181, 'RO', 47.78333000, 22.91667000, '2019-10-05 23:15:38', '2020-05-01 17:23:11', 1, 'Q1189000'),
(96031, 'Păulești', 4758, 'VN', 181, 'RO', 45.89154000, 26.68914000, '2019-10-05 23:15:38', '2020-05-01 17:23:12', 1, 'Q14227651'),
(96032, 'Păuliş', 4739, 'AR', 181, 'RO', 46.11667000, 21.58333000, '2019-10-05 23:15:38', '2020-05-01 17:23:08', 1, 'Q1093019'),
(96033, 'Păuneşti', 4758, 'VN', 181, 'RO', 46.03333000, 27.10000000, '2019-10-05 23:15:38', '2020-05-01 17:23:12', 1, 'Q14232840'),
(96034, 'Păuşeşti', 4757, 'VL', 181, 'RO', 45.07358000, 24.13473000, '2019-10-05 23:15:38', '2020-05-01 17:23:12', 1, 'Q2298088'),
(96035, 'Păuşeşti-Măglaşi', 4757, 'VL', 181, 'RO', 45.13333000, 24.25000000, '2019-10-05 23:15:38', '2020-05-01 17:23:12', 1, 'Q3915603'),
(96036, 'Păușești', 4735, 'IS', 181, 'RO', 47.15810000, 27.32594000, '2019-10-05 23:15:38', '2020-05-01 17:23:10', 1, 'Q12140379'),
(96037, 'Rachelu', 4727, 'TL', 181, 'RO', 45.28667000, 28.32349000, '2019-10-05 23:15:38', '2019-10-05 23:15:38', 1, 'Q12146876');

