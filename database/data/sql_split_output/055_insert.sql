INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(27333, 'Marxheim', 3009, 'BY', 82, 'DE', 48.74153000, 10.94504000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q49911'),
(27334, 'Marzahn', 3010, 'BE', 82, 'DE', 52.54525000, 13.56983000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q702521'),
(27335, '<PERSON><PERSON>ling', 3009, 'BY', 82, 'DE', 48.40904000, 11.79382000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q506502'),
(27336, 'Masburg', 3019, 'R<PERSON>', 82, '<PERSON>', 50.24065000, 7.11736000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q564991'),
(27337, '<PERSON>selheim', 3006, 'BW', 82, 'DE', 48.13333000, 9.88333000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q538658'),
(27338, 'Massenbachhausen', 3006, 'BW', 82, 'DE', 49.17778000, 9.04333000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q552214'),
(27339, 'Masserberg', 3015, 'TH', 82, 'DE', 50.51981000, 10.97087000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q559759'),
(27340, 'Massing', 3009, 'BY', 82, 'DE', 48.39118000, 12.60947000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q73551'),
(27341, 'Mastershausen', 3019, 'RP', 82, 'DE', 50.06667000, 7.35000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q562485'),
(27342, 'Mauer', 3006, 'BW', 82, 'DE', 49.34083000, 8.80028000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q81937'),
(27343, 'Mauern', 3009, 'BY', 82, 'DE', 48.51667000, 11.90000000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q255396'),
(27344, 'Mauerstetten', 3009, 'BY', 82, 'DE', 47.89358000, 10.67127000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q514428'),
(27345, 'Maulbronn', 3006, 'BW', 82, 'DE', 48.99958000, 8.80337000, '2019-10-05 22:41:16', '2019-10-05 22:41:16', 1, 'Q80560'),
(27346, 'Maulburg', 3006, 'BW', 82, 'DE', 47.64631000, 7.78210000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q61989'),
(27347, 'Mauth', 3009, 'BY', 82, 'DE', 48.88972000, 13.58459000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q32126095'),
(27348, 'Maxdorf', 3019, 'RP', 82, 'DE', 49.48806000, 8.29167000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q566946'),
(27349, 'Maxhütte-Haidhof', 3009, 'BY', 82, 'DE', 49.19963000, 12.09229000, '2019-10-05 22:41:17', '2020-05-01 17:22:47', 1, 'Q250866'),
(27350, 'Maxsain', 3019, 'RP', 82, 'DE', 50.54316000, 7.78512000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q282605'),
(27351, 'Mayen', 3019, 'RP', 82, 'DE', 50.32797000, 7.22277000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q516611'),
(27352, 'Maßbach', 3009, 'BY', 82, 'DE', 50.18321000, 10.27522000, '2019-10-05 22:41:17', '2020-05-01 17:22:47', 1, 'Q503250'),
(27353, 'Maßweiler', 3019, 'RP', 82, 'DE', 49.26667000, 7.53333000, '2019-10-05 22:41:17', '2020-05-01 17:22:49', 1, 'Q565765'),
(27354, 'Mechernich', 3017, 'NW', 82, 'DE', 50.59304000, 6.65224000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q164458'),
(27355, 'Mechterstädt', 3015, 'TH', 82, 'DE', 50.94201000, 10.52380000, '2019-10-05 22:41:17', '2020-05-01 17:22:50', 1, 'Q631886'),
(27356, 'Meckenbeuren', 3006, 'BW', 82, 'DE', 47.70000000, 9.56667000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q527910'),
(27357, 'Meckenheim', 3019, 'RP', 82, 'DE', 49.40444000, 8.23917000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q660438'),
(27358, 'Meckenheim', 3017, 'NW', 82, 'DE', 50.62388000, 7.02942000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q32127820'),
(27359, 'Meckesheim', 3006, 'BW', 82, 'DE', 49.32167000, 8.81944000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q82127'),
(27360, 'Mecklenburg', 3007, 'MV', 82, 'DE', 53.84270000, 11.46330000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q263981'),
(27361, 'Meddersheim', 3019, 'RP', 82, 'DE', 49.77636000, 7.61708000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q561287'),
(27362, 'Medebach', 3017, 'NW', 82, 'DE', 51.19713000, 8.70635000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, '*********'),
(27363, 'Medlingen', 3009, 'BY', 82, 'DE', 48.57027000, 10.31608000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q547269'),
(27364, 'Meeder', 3009, 'BY', 82, 'DE', 50.32118000, 10.90697000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q547269'),
(27365, 'Meerane', 3021, 'SN', 82, 'DE', 50.84688000, 12.46473000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q20083'),
(27366, 'Meerbeck', 3008, 'NI', 82, 'DE', 52.34174000, 9.15067000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q661964'),
(27367, 'Meerbusch', 3017, 'NW', 82, 'DE', 51.25268000, 6.68807000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q4105'),
(27368, 'Meerdorf', 3008, 'NI', 82, 'DE', 52.34583000, 10.31745000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, '********'),
(27369, 'Meersburg', 3006, 'BW', 82, 'DE', 47.69419000, 9.27113000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q498212'),
(27370, 'Mehlbach', 3019, 'RP', 82, 'DE', 49.51667000, 7.71667000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q567627'),
(27371, 'Mehlingen', 3019, 'RP', 82, 'DE', 49.49136000, 7.85467000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q612785'),
(27372, 'Mehlmeisel', 3009, 'BY', 82, 'DE', 49.97421000, 11.86200000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q503844'),
(27373, 'Mehltheuer', 3021, 'SN', 82, 'DE', 50.54530000, 12.03700000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q599484'),
(27374, 'Mehren', 3019, 'RP', 82, 'DE', 50.18333000, 6.88333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q566816'),
(27375, 'Mehrhoog', 3017, 'NW', 82, 'DE', 51.73826000, 6.51164000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q1917485'),
(27376, 'Mehring', 3019, 'RP', 82, 'DE', 49.80000000, 6.83333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q1917485'),
(27377, 'Mehring', 3009, 'BY', 82, 'DE', 48.18333000, 12.78333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q1917485'),
(27378, 'Mehringen', 3011, 'ST', 82, 'DE', 51.72663000, 11.51264000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q187043'),
(27379, 'Mehrstetten', 3006, 'BW', 82, 'DE', 48.37609000, 9.56600000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q81465'),
(27380, 'Meiderich', 3017, 'NW', 82, 'DE', 51.46667000, 6.76667000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q81465'),
(27381, 'Meinersen', 3008, 'NI', 82, 'DE', 52.47436000, 10.35247000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q638968'),
(27382, 'Meinerzhagen', 3017, 'NW', 82, 'DE', 51.10740000, 7.64838000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q5573'),
(27383, 'Meiningen', 3015, 'TH', 82, 'DE', 50.56787000, 10.41521000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q487278'),
(27384, 'Meisenheim', 3019, 'RP', 82, 'DE', 49.70721000, 7.66765000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q520103'),
(27385, 'Meissen', 3021, 'SN', 82, 'DE', 51.16158000, 13.47370000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q8738'),
(27386, 'Meitingen', 3009, 'BY', 82, 'DE', 48.54586000, 10.85179000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q252494'),
(27387, 'Meitzendorf', 3011, 'ST', 82, 'DE', 52.21308000, 11.56174000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q1918809'),
(27388, 'Meißenheim', 3006, 'BW', 82, 'DE', 48.41035000, 7.77266000, '2019-10-05 22:41:17', '2020-05-01 17:22:47', 1, 'Q550767'),
(27389, 'Melbach', 3018, 'HE', 82, 'DE', 50.37908000, 8.80926000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q550767'),
(27390, 'Melbeck', 3008, 'NI', 82, 'DE', 53.18333000, 10.40000000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q665881'),
(27391, 'Melchow', 3013, 'BB', 82, 'DE', 52.77613000, 13.70647000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q665881'),
(27392, 'Meldorf', 3005, 'SH', 82, 'DE', 54.09182000, 9.06870000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q498545'),
(27393, 'Melle', 3008, 'NI', 82, 'DE', 52.20197000, 8.33826000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q6954'),
(27394, 'Mellenbach-Glasbach', 3015, 'TH', 82, 'DE', 50.61667000, 11.10000000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q565928'),
(27395, 'Mellingen', 3015, 'TH', 82, 'DE', 50.94123000, 11.39640000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q565928'),
(27396, 'Mellinghausen', 3008, 'NI', 82, 'DE', 52.70697000, 8.89704000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q514674'),
(27397, 'Mellrichstadt', 3009, 'BY', 82, 'DE', 50.42849000, 10.30334000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q552523'),
(27398, 'Melsbach', 3019, 'RP', 82, 'DE', 50.48333000, 7.48333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q531792'),
(27399, 'Melsdorf', 3005, 'SH', 82, 'DE', 54.31667000, 10.03333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q531792'),
(27400, 'Melsungen', 3018, 'HE', 82, 'DE', 51.13029000, 9.55236000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q552495'),
(27401, 'Memmelsdorf', 3009, 'BY', 82, 'DE', 49.93012000, 10.95921000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q534442'),
(27402, 'Memmingen', 3009, 'BY', 82, 'DE', 47.98372000, 10.18527000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q14825'),
(27403, 'Memmingerberg', 3009, 'BY', 82, 'DE', 47.98803000, 10.22295000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q547561'),
(27404, 'Menden', 3017, 'NW', 82, 'DE', 51.44337000, 7.77825000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q4086'),
(27405, 'Mendig', 3019, 'RP', 82, 'DE', 50.36667000, 7.28333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q570085'),
(27406, 'Mengen', 3006, 'BW', 82, 'DE', 48.04951000, 9.33005000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q570085'),
(27407, 'Mengenich', 3017, 'NW', 82, 'DE', 50.97850000, 6.86737000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q6089'),
(27408, 'Mengersgereuth-Hämmern', 3015, 'TH', 82, 'DE', 50.39730000, 11.11649000, '2019-10-05 22:41:17', '2020-05-01 17:22:50', 1, 'Q669478'),
(27409, 'Mengerskirchen', 3018, 'HE', 82, 'DE', 50.56392000, 8.15555000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q596662'),
(27410, 'Mengkofen', 3009, 'BY', 82, 'DE', 48.71894000, 12.44047000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q32130893'),
(27411, 'Menslage', 3008, 'NI', 82, 'DE', 52.68333000, 7.81667000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q651619'),
(27412, 'Menteroda', 3015, 'TH', 82, 'DE', 51.30763000, 10.56323000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q581220'),
(27413, 'Meppen', 3008, 'NI', 82, 'DE', 52.69064000, 7.29097000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q15977'),
(27414, 'Merching', 3009, 'BY', 82, 'DE', 48.24594000, 10.98530000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q1921480'),
(27415, 'Merchweiler', 3020, 'SL', 82, 'DE', 49.35000000, 7.05000000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q630725'),
(27416, 'Merdingen', 3006, 'BW', 82, 'DE', 48.01667000, 7.68333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q142169'),
(27417, 'Merenberg', 3018, 'HE', 82, 'DE', 50.50707000, 8.19194000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q621847'),
(27418, 'Mering', 3009, 'BY', 82, 'DE', 48.26560000, 10.98461000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q275579'),
(27419, 'Merkenbach', 3018, 'HE', 82, 'DE', 50.65626000, 8.29513000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q275579'),
(27420, 'Merkendorf', 3009, 'BY', 82, 'DE', 49.20361000, 10.70416000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q275579'),
(27421, 'Merklingen', 3006, 'BW', 82, 'DE', 48.51135000, 9.75496000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q636077'),
(27422, 'Merseburg', 3011, 'ST', 82, 'DE', 51.35478000, 11.98923000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q14945'),
(27423, 'Mertesdorf', 3019, 'RP', 82, 'DE', 49.77212000, 6.73290000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q659878'),
(27424, 'Mertingen', 3009, 'BY', 82, 'DE', 48.65695000, 10.80557000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q506753'),
(27425, 'Mertloch', 3019, 'RP', 82, 'DE', 50.26948000, 7.30814000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q647090'),
(27426, 'Merxheim', 3019, 'RP', 82, 'DE', 49.79297000, 7.56010000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q641481'),
(27427, 'Merzalben', 3019, 'RP', 82, 'DE', 49.24467000, 7.73077000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q23029'),
(27428, 'Merzen', 3008, 'NI', 82, 'DE', 52.48333000, 7.83333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q651171'),
(27429, 'Merzhausen', 3006, 'BW', 82, 'DE', 47.96667000, 7.83333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q632798'),
(27430, 'Merzig', 3020, 'SL', 82, 'DE', 49.44331000, 6.63874000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q632798'),
(27431, 'Meschede', 3017, 'NW', 82, 'DE', 51.35020000, 8.28332000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q5632'),
(27432, 'Mesekenhagen', 3007, 'MV', 82, 'DE', 54.14846000, 13.31843000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q583943'),
(27433, 'Mespelbrunn', 3009, 'BY', 82, 'DE', 49.91444000, 9.29194000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q516376'),
(27434, 'Messel', 3018, 'HE', 82, 'DE', 49.93833000, 8.74056000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q7386758'),
(27435, 'Messingen', 3008, 'NI', 82, 'DE', 52.46667000, 7.46667000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q636852'),
(27436, 'Metelen', 3017, 'NW', 82, 'DE', 52.14434000, 7.21270000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q182054'),
(27437, 'Metten', 3009, 'BY', 82, 'DE', 48.85512000, 12.91554000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q32132945'),
(27438, 'Mettendorf', 3019, 'RP', 82, 'DE', 49.94784000, 6.33003000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q32132945'),
(27439, 'Mettenheim', 3019, 'RP', 82, 'DE', 49.74000000, 8.32583000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q818829'),
(27440, 'Mettenheim', 3009, 'BY', 82, 'DE', 48.26667000, 12.46667000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q818829'),
(27441, 'Mettingen', 3017, 'NW', 82, 'DE', 52.31667000, 7.78333000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q32133031'),
(27442, 'Mettlach', 3020, 'SL', 82, 'DE', 49.49489000, 6.58562000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q543627'),
(27443, 'Mettmann', 3017, 'NW', 82, 'DE', 51.25040000, 6.97536000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q14878'),
(27444, 'Metzingen', 3006, 'BW', 82, 'DE', 48.53695000, 9.28330000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q82881'),
(27445, 'Meudt', 3019, 'RP', 82, 'DE', 50.49889000, 7.89500000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q676345'),
(27446, 'Meuselbach', 3015, 'TH', 82, 'DE', 50.57152000, 11.09143000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q1926121'),
(27447, 'Meuselwitz', 3015, 'TH', 82, 'DE', 51.04315000, 12.29935000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q518356'),
(27448, 'Meyenburg', 3013, 'BB', 82, 'DE', 53.04524000, 14.23691000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q518356'),
(27449, 'Meßkirch', 3006, 'BW', 82, 'DE', 47.99457000, 9.11479000, '2019-10-05 22:41:17', '2020-05-01 17:22:47', 1, 'Q82724'),
(27450, 'Meßstetten', 3006, 'BW', 82, 'DE', 48.18317000, 8.96565000, '2019-10-05 22:41:17', '2020-05-01 17:22:47', 1, 'Q82724'),
(27451, 'Michelau', 3009, 'BY', 82, 'DE', 50.16233000, 11.11207000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q505602'),
(27452, 'Michelbach an der Bilz', 3006, 'BW', 82, 'DE', 49.07028000, 9.76250000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q81036'),
(27453, 'Michelfeld', 3006, 'BW', 82, 'DE', 49.09750000, 9.67861000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q81048'),
(27454, 'Michelsneukirchen', 3009, 'BY', 82, 'DE', 49.12291000, 12.55284000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q31618'),
(27455, 'Michelstadt', 3018, 'HE', 82, 'DE', 49.67569000, 9.00373000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q516517'),
(27456, 'Michendorf', 3013, 'BB', 82, 'DE', 52.31352000, 13.02996000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q612992'),
(27457, 'Mickhausen', 3009, 'BY', 82, 'DE', 48.24171000, 10.64026000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q120802'),
(27458, 'Midlum', 3008, 'NI', 82, 'DE', 53.73135000, 8.61695000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q120802'),
(27459, 'Miehlen', 3019, 'RP', 82, 'DE', 50.22574000, 7.83196000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q631119'),
(27460, 'Mielkendorf', 3005, 'SH', 82, 'DE', 54.28333000, 10.05000000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q631119'),
(27461, 'Miesbach', 3009, 'BY', 82, 'DE', 47.78903000, 11.83382000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q269157'),
(27462, 'Mieste', 3011, 'ST', 82, 'DE', 52.48226000, 11.20640000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q696352'),
(27463, 'Mietingen', 3006, 'BW', 82, 'DE', 48.18333000, 9.90000000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q538737'),
(27464, 'Mihla', 3015, 'TH', 82, 'DE', 51.07617000, 10.33175000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q579812'),
(27465, 'Mildenau', 3021, 'SN', 82, 'DE', 50.58689000, 13.07263000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q57801'),
(27466, 'Mildstedt', 3005, 'SH', 82, 'DE', 54.46667000, 9.10000000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q21011'),
(27467, 'Milmersdorf', 3013, 'BB', 82, 'DE', 53.11185000, 13.64150000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q623967'),
(27468, 'Miltach', 3009, 'BY', 82, 'DE', 49.16196000, 12.76843000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q505991'),
(27469, 'Miltenberg', 3009, 'BY', 82, 'DE', 49.70452000, 9.26725000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q505991'),
(27470, 'Milz', 3015, 'TH', 82, 'DE', 50.37760000, 10.53757000, '2019-10-05 22:41:17', '2019-10-05 22:41:17', 1, 'Q556807'),
(27471, 'Mindelheim', 3009, 'BY', 82, 'DE', 48.04578000, 10.49222000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q518579'),
(27472, 'Minden', 3017, 'NW', 82, 'DE', 52.28953000, 8.91455000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q3846'),
(27473, 'Minfeld', 3019, 'RP', 82, 'DE', 49.07167000, 8.14528000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q600380'),
(27474, 'Mintraching', 3009, 'BY', 82, 'DE', 48.95357000, 12.24209000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q491539'),
(27475, 'Mirow', 3007, 'MV', 82, 'DE', 53.50510000, 11.50294000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32136858'),
(27476, 'Missen-Wilhams', 3009, 'BY', 82, 'DE', 47.60000000, 10.11667000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q532000'),
(27477, 'Mistelgau', 3009, 'BY', 82, 'DE', 49.91260000, 11.46586000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q505319'),
(27478, 'Mitte', 3010, 'BE', 82, 'DE', 52.52003000, 13.40489000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q2013767'),
(27479, 'Mittegroßefehn', 3008, 'NI', 82, 'DE', 53.39165000, 7.56602000, '2019-10-05 22:41:18', '2020-05-01 17:22:48', 1, 'Q773152'),
(27480, 'Mittelbiberach', 3006, 'BW', 82, 'DE', 48.08333000, 9.75000000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q539825'),
(27481, 'Mitteleschenbach', 3009, 'BY', 82, 'DE', 49.21186000, 10.79784000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q503544'),
(27482, 'Mittelherwigsdorf', 3021, 'SN', 82, 'DE', 50.91667000, 14.76667000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q506142'),
(27483, 'Mittelhof', 3019, 'RP', 82, 'DE', 50.77517000, 7.80466000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q652209'),
(27484, 'Mittelneufnach', 3009, 'BY', 82, 'DE', 48.17642000, 10.59754000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q652209'),
(27485, 'Mittelschöntal', 3006, 'BW', 82, 'DE', 48.94615000, 9.39520000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q33462881'),
(27486, 'Mittelstetten', 3009, 'BY', 82, 'DE', 48.25000000, 11.10000000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32138189'),
(27487, 'Mittenaar', 3018, 'HE', 82, 'DE', 50.70000000, 8.38333000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q624070'),
(27488, 'Mittenwald', 3009, 'BY', 82, 'DE', 47.44220000, 11.26187000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q624070'),
(27489, 'Mittenwalde', 3013, 'BB', 82, 'DE', 52.26007000, 13.53945000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q574811'),
(27490, 'Mitterfels', 3009, 'BY', 82, 'DE', 48.97633000, 12.67848000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q258870'),
(27491, 'Mitterskirchen', 3009, 'BY', 82, 'DE', 48.35000000, 12.73333000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q257401'),
(27492, 'Mitterteich', 3009, 'BY', 82, 'DE', 49.95141000, 12.24206000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q253371'),
(27493, 'Mittweida', 3021, 'SN', 82, 'DE', 50.98622000, 12.97537000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q71170'),
(27494, 'Mitwitz', 3009, 'BY', 82, 'DE', 50.25111000, 11.20818000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q506228'),
(27495, 'Mixdorf', 3013, 'BB', 82, 'DE', 52.20000000, 14.40000000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q506228'),
(27496, 'Moabit', 3010, 'BE', 82, 'DE', 52.52635000, 13.33903000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q699800'),
(27497, 'Mochau', 3021, 'SN', 82, 'DE', 51.13333000, 13.18333000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q699800'),
(27498, 'Mockrehna', 3021, 'SN', 82, 'DE', 51.50757000, 12.81418000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q16058'),
(27499, 'Moers', 3017, 'NW', 82, 'DE', 51.45342000, 6.63260000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q3132'),
(27500, 'Mogendorf', 3019, 'RP', 82, 'DE', 50.49444000, 7.75972000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q676280'),
(27501, 'Mohlsdorf', 3015, 'TH', 82, 'DE', 50.66974000, 12.26519000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q83314'),
(27502, 'Mohrkirch', 3005, 'SH', 82, 'DE', 54.66667000, 9.71667000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q560882'),
(27503, 'Moisburg', 3008, 'NI', 82, 'DE', 53.40620000, 9.69880000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q508003'),
(27504, 'Molbergen', 3008, 'NI', 82, 'DE', 52.85805000, 7.92548000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q559616'),
(27505, 'Molfsee', 3005, 'SH', 82, 'DE', 54.26667000, 10.06667000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q642144'),
(27506, 'Molschleben', 3015, 'TH', 82, 'DE', 51.00000000, 10.78333000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q552260'),
(27507, 'Mommenheim', 3019, 'RP', 82, 'DE', 49.88028000, 8.26500000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q552260'),
(27508, 'Monheim', 3009, 'BY', 82, 'DE', 48.84389000, 10.85834000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q504521'),
(27509, 'Monheim am Rhein', 3017, 'NW', 82, 'DE', 51.09162000, 6.89217000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q7062'),
(27510, 'Monschau', 3017, 'NW', 82, 'DE', 50.55462000, 6.24001000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q200783'),
(27511, 'Monsheim', 3019, 'RP', 82, 'DE', 49.63722000, 8.21194000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q643044'),
(27512, 'Montabaur', 3019, 'RP', 82, 'DE', 50.43588000, 7.82320000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q632991'),
(27513, 'Monzelfeld', 3019, 'RP', 82, 'DE', 49.89237000, 7.07318000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q654069'),
(27514, 'Monzingen', 3019, 'RP', 82, 'DE', 49.79763000, 7.59284000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q609223'),
(27515, 'Moorenweis', 3009, 'BY', 82, 'DE', 48.15561000, 11.07851000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32141908'),
(27516, 'Moorrege', 3005, 'SH', 82, 'DE', 53.66667000, 9.66667000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q555079'),
(27517, 'Moos', 3006, 'BW', 82, 'DE', 47.72439000, 8.93479000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q555079'),
(27518, 'Moosach', 3009, 'BY', 82, 'DE', 48.03213000, 11.87510000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q555079'),
(27519, 'Moosbach', 3009, 'BY', 82, 'DE', 49.58909000, 12.41036000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q510024'),
(27520, 'Moosburg', 3009, 'BY', 82, 'DE', 48.47089000, 11.93811000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q31872'),
(27521, 'Moosinning', 3009, 'BY', 82, 'DE', 48.27708000, 11.84446000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q511796'),
(27522, 'Moosthenning', 3009, 'BY', 82, 'DE', 48.67659000, 12.49737000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32142777'),
(27523, 'Morbach', 3019, 'RP', 82, 'DE', 49.80772000, 7.12714000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32142777'),
(27524, 'Moringen', 3008, 'NI', 82, 'DE', 51.69915000, 9.87107000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q520064'),
(27525, 'Moritzburg', 3021, 'SN', 82, 'DE', 51.15922000, 13.68021000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q520064'),
(27526, 'Morsbach', 3017, 'NW', 82, 'DE', 50.86665000, 7.72787000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q11050'),
(27527, 'Morsum', 3008, 'NI', 82, 'DE', 52.95347000, 9.07962000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32147513'),
(27528, 'Mosbach', 3006, 'BW', 82, 'DE', 49.35357000, 9.15106000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q23008'),
(27529, 'Motten', 3009, 'BY', 82, 'DE', 50.39561000, 9.77251000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q23008'),
(27530, 'Much', 3017, 'NW', 82, 'DE', 50.90383000, 7.40306000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q23008'),
(27531, 'Mudau', 3006, 'BW', 82, 'DE', 49.53444000, 9.20444000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q545231'),
(27532, 'Mudersbach', 3019, 'RP', 82, 'DE', 50.82515000, 7.94347000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q545231'),
(27533, 'Muggensturm', 3006, 'BW', 82, 'DE', 48.86667000, 8.28333000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q80708'),
(27534, 'Muhr am See', 3009, 'BY', 82, 'DE', 49.15445000, 10.71845000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q267993'),
(27535, 'Mulda', 3021, 'SN', 82, 'DE', 50.80789000, 13.41477000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32162982'),
(27536, 'Muldenstein', 3011, 'ST', 82, 'DE', 51.67192000, 12.34548000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q695583'),
(27537, 'Mulfingen', 3006, 'BW', 82, 'DE', 49.34028000, 9.80083000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q552108'),
(27538, 'Mundelsheim', 3006, 'BW', 82, 'DE', 49.00083000, 9.20778000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q62101'),
(27539, 'Munderkingen', 3006, 'BW', 82, 'DE', 48.23568000, 9.64398000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q515163'),
(27540, 'Munich', 3009, 'BY', 82, 'DE', 48.13743000, 11.57549000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q1726'),
(27541, 'Munkbrarup', 3005, 'SH', 82, 'DE', 54.80000000, 9.56667000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q559161'),
(27542, 'Munningen', 3009, 'BY', 82, 'DE', 48.91667000, 10.60000000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q559161'),
(27543, 'Munster', 3008, 'NI', 82, 'DE', 52.98569000, 10.08756000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32164050'),
(27544, 'Murg', 3006, 'BW', 82, 'DE', 47.55492000, 8.02182000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32164050'),
(27545, 'Murnau am Staffelsee', 3009, 'BY', 82, 'DE', 47.68085000, 11.20125000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32164050'),
(27546, 'Murr', 3006, 'BW', 82, 'DE', 48.96207000, 9.25924000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q32164050'),
(27547, 'Murrhardt', 3006, 'BW', 82, 'DE', 48.98191000, 9.57047000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q81768'),
(27548, 'Muschwitz', 3011, 'ST', 82, 'DE', 51.19296000, 12.12065000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q696239'),
(27549, 'Mutlangen', 3006, 'BW', 82, 'DE', 48.82588000, 9.79714000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q574197'),
(27550, 'Mutterstadt', 3019, 'RP', 82, 'DE', 49.44139000, 8.35611000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q657074'),
(27551, 'Mutzschen', 3021, 'SN', 82, 'DE', 51.26159000, 12.89125000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q668396'),
(27552, 'Mylau', 3021, 'SN', 82, 'DE', 50.61861000, 12.26535000, '2019-10-05 22:41:18', '2019-10-05 22:41:18', 1, 'Q46885'),
(27553, 'Mähring', 3009, 'BY', 82, 'DE', 49.91095000, 12.52448000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q566632'),
(27554, 'Märkisches Viertel', 3010, 'BE', 82, 'DE', 52.59841000, 13.35766000, '2019-10-05 22:41:18', '2020-05-01 17:22:48', 1, 'Q678200'),
(27555, 'Möckern', 3011, 'ST', 82, 'DE', 52.14099000, 11.95203000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q50872'),
(27556, 'Möckmühl', 3006, 'BW', 82, 'DE', 49.32490000, 9.35837000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q504447'),
(27557, 'Mödingen', 3009, 'BY', 82, 'DE', 48.64327000, 10.43173000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q546448'),
(27558, 'Mögglingen', 3006, 'BW', 82, 'DE', 48.82320000, 9.96250000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q574252'),
(27559, 'Möglingen', 3006, 'BW', 82, 'DE', 48.88741000, 9.12694000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q61738'),
(27560, 'Möhlau', 3011, 'ST', 82, 'DE', 51.73126000, 12.35282000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q649091'),
(27561, 'Möhnesee', 3017, 'NW', 82, 'DE', 51.50000000, 8.13333000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q649091'),
(27562, 'Möhrendorf', 3009, 'BY', 82, 'DE', 49.63333000, 11.00000000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q73206'),
(27563, 'Möllenhagen', 3007, 'MV', 82, 'DE', 53.52388000, 12.92868000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q73206'),
(27564, 'Mölln', 3005, 'SH', 82, 'DE', 53.61702000, 10.68742000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q73206'),
(27565, 'Mömbris', 3009, 'BY', 82, 'DE', 50.06921000, 9.16371000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q506072'),
(27566, 'Mömlingen', 3009, 'BY', 82, 'DE', 49.85972000, 9.08333000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q505250'),
(27567, 'Mönchberg', 3009, 'BY', 82, 'DE', 49.79326000, 9.26858000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q506239'),
(27568, 'Mönchengladbach', 3017, 'NW', 82, 'DE', 51.18539000, 6.44172000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q2758'),
(27569, 'Mönchenholzhausen', 3015, 'TH', 82, 'DE', 50.96667000, 11.15000000, '2019-10-05 22:41:18', '2020-05-01 17:22:50', 1, 'Q2758'),
(27570, 'Mönchhagen', 3007, 'MV', 82, 'DE', 54.15146000, 12.21670000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q33464978'),
(27571, 'Mönchsdeggingen', 3009, 'BY', 82, 'DE', 48.77605000, 10.58043000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q503837'),
(27572, 'Mönchsroth', 3009, 'BY', 82, 'DE', 49.01851000, 10.35856000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q186467'),
(27573, 'Mönchweiler', 3006, 'BW', 82, 'DE', 48.10040000, 8.42219000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q83217'),
(27574, 'Mönkeberg', 3005, 'SH', 82, 'DE', 54.35000000, 10.18333000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q818718'),
(27575, 'Mönsheim', 3006, 'BW', 82, 'DE', 48.86667000, 8.86667000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q81616'),
(27576, 'Mörfelden-Walldorf', 3018, 'HE', 82, 'DE', 49.99472000, 8.58361000, '2019-10-05 22:41:18', '2020-05-01 17:22:48', 1, 'Q81616'),
(27577, 'Mörlenbach', 3018, 'HE', 82, 'DE', 49.59917000, 8.73472000, '2019-10-05 22:41:18', '2020-05-01 17:22:48', 1, 'Q629101'),
(27578, 'Möser', 3011, 'ST', 82, 'DE', 52.21397000, 11.79279000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q565929'),
(27579, 'Mössingen', 3006, 'BW', 82, 'DE', 48.40567000, 9.05419000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q525575'),
(27580, 'Möttingen', 3009, 'BY', 82, 'DE', 48.80924000, 10.59022000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q378270'),
(27581, 'Mötzing', 3009, 'BY', 82, 'DE', 48.89350000, 12.37361000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q491632'),
(27582, 'Mötzingen', 3006, 'BW', 82, 'DE', 48.53320000, 8.77447000, '2019-10-05 22:41:18', '2020-05-01 17:22:47', 1, 'Q550627'),
(27583, 'Mücheln', 3011, 'ST', 82, 'DE', 51.29688000, 11.80759000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q530212'),
(27584, 'Mücka', 3021, 'SN', 82, 'DE', 51.31667000, 14.70000000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q703119'),
(27585, 'Müden', 3008, 'NI', 82, 'DE', 52.52771000, 10.36011000, '2019-10-05 22:41:18', '2020-05-01 17:22:48', 1, 'Q684029'),
(27586, 'Mügeln', 3021, 'SN', 82, 'DE', 51.23619000, 13.04567000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q12047'),
(27587, 'Müggelheim', 3010, 'BE', 82, 'DE', 52.41137000, 13.66403000, '2019-10-05 22:41:18', '2020-05-01 17:22:48', 1, 'Q644380'),
(27588, 'Mühl Rosin', 3007, 'MV', 82, 'DE', 53.76383000, 12.21388000, '2019-10-05 22:41:18', '2020-05-01 17:22:49', 1, 'Q644380'),
(27589, 'Mühlacker', 3006, 'BW', 82, 'DE', 48.94754000, 8.83675000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q80670'),
(27590, 'Mühlanger', 3011, 'ST', 82, 'DE', 51.85000000, 12.75000000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q634731'),
(27591, 'Mühlau', 3021, 'SN', 82, 'DE', 50.90000000, 12.76667000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q71005'),
(27592, 'Mühlberg', 3013, 'BB', 82, 'DE', 51.43453000, 13.22177000, '2019-10-05 22:41:19', '2020-05-01 17:22:48', 1, 'Q170301'),
(27593, 'Mühldorf', 3009, 'BY', 82, 'DE', 48.24670000, 12.52155000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q170301'),
(27594, 'Mühlen Eichsen', 3007, 'MV', 82, 'DE', 53.75775000, 11.25006000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q639005'),
(27595, 'Mühlenbach', 3006, 'BW', 82, 'DE', 48.25000000, 8.11667000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q639005'),
(27596, 'Mühlhausen', 3015, 'TH', 82, 'DE', 51.20896000, 10.45275000, '2019-10-05 22:41:19', '2020-05-01 17:22:50', 1, 'Q14925'),
(27597, 'Mühlhausen', 3009, 'BY', 82, 'DE', 49.75443000, 10.77563000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q1958624'),
(27598, 'Mühlhausen', 3006, 'BW', 82, 'DE', 49.24861000, 8.72667000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q81903'),
(27599, 'Mühlhausen-Ehingen', 3006, 'BW', 82, 'DE', 47.81102000, 8.81224000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q62125'),
(27600, 'Mühlheim am Bach', 3006, 'BW', 82, 'DE', 48.37863000, 8.69735000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q62125'),
(27601, 'Mühlheim am Main', 3018, 'HE', 82, 'DE', 50.11667000, 8.83333000, '2019-10-05 22:41:19', '2020-05-01 17:22:48', 1, 'Q639902'),
(27602, 'Mühlingen', 3006, 'BW', 82, 'DE', 47.91667000, 9.01667000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q62064'),
(27603, 'Mühltroff', 3021, 'SN', 82, 'DE', 50.53919000, 11.92828000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q46880'),
(27604, 'Mülheim', 3017, 'NW', 82, 'DE', 51.43218000, 6.87967000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q46880'),
(27605, 'Mülheim-Kärlich', 3019, 'RP', 82, 'DE', 50.38510000, 7.49890000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q558577'),
(27606, 'Müllheim', 3006, 'BW', 82, 'DE', 47.80820000, 7.63035000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q558577'),
(27607, 'Müllrose', 3013, 'BB', 82, 'DE', 52.24736000, 14.41794000, '2019-10-05 22:41:19', '2020-05-01 17:22:48', 1, 'Q638607'),
(27608, 'Mülsen', 3021, 'SN', 82, 'DE', 50.75000000, 12.56667000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q17123'),
(27609, 'Münchberg', 3009, 'BY', 82, 'DE', 50.18952000, 11.78823000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q160986'),
(27610, 'Müncheberg', 3013, 'BB', 82, 'DE', 52.50704000, 14.13716000, '2019-10-05 22:41:19', '2020-05-01 17:22:48', 1, 'Q202498'),
(27611, 'Münchenbernsdorf', 3015, 'TH', 82, 'DE', 50.82114000, 11.93226000, '2019-10-05 22:41:19', '2020-05-01 17:22:50', 1, 'Q526374'),
(27612, 'Münchhausen', 3018, 'HE', 82, 'DE', 50.96081000, 8.71837000, '2019-10-05 22:41:19', '2020-05-01 17:22:48', 1, 'Q582545'),
(27613, 'Münchsmünster', 3009, 'BY', 82, 'DE', 48.76667000, 11.68333000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q269579'),
(27614, 'Münchsteinach', 3009, 'BY', 82, 'DE', 49.63937000, 10.59502000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q505026'),
(27615, 'Münchweiler an der Alsenz', 3019, 'RP', 82, 'DE', 49.55000000, 7.88333000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q642327'),
(27616, 'Münchweiler an der Rodalbe', 3019, 'RP', 82, 'DE', 49.21798000, 7.70295000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q642327'),
(27617, 'Münnerstadt', 3009, 'BY', 82, 'DE', 50.24636000, 10.20187000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q571922'),
(27618, 'Münsing', 3009, 'BY', 82, 'DE', 47.90000000, 11.36667000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q532939'),
(27619, 'Münsingen', 3006, 'BW', 82, 'DE', 48.41126000, 9.49704000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q532939'),
(27620, 'Münster', 3018, 'HE', 82, 'DE', 49.92278000, 8.86778000, '2019-10-05 22:41:19', '2020-05-01 17:22:48', 1, 'Q532939'),
(27621, 'Münster', 3017, 'NW', 82, 'DE', 51.96236000, 7.62571000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q2742'),
(27622, 'Münster', 3009, 'BY', 82, 'DE', 48.22904000, 10.63623000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q1666065'),
(27623, 'Münster-Sarmsheim', 3019, 'RP', 82, 'DE', 49.94341000, 7.89426000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q665852'),
(27624, 'Münsterdorf', 3005, 'SH', 82, 'DE', 53.90232000, 9.54123000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q635202'),
(27625, 'Münsterhausen', 3009, 'BY', 82, 'DE', 48.30973000, 10.45500000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q505221'),
(27626, 'Münstermaifeld', 3019, 'RP', 82, 'DE', 50.24638000, 7.36208000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q514762'),
(27627, 'Münstertal/Schwarzwald', 3006, 'BW', 82, 'DE', 47.85472000, 7.78417000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q274990'),
(27628, 'Münzenberg', 3018, 'HE', 82, 'DE', 50.45346000, 8.77430000, '2019-10-05 22:41:19', '2020-05-01 17:22:48', 1, 'Q570892'),
(27629, 'Müschenbach', 3019, 'RP', 82, 'DE', 50.68333000, 7.78333000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q646422'),
(27630, 'Nabburg', 3009, 'BY', 82, 'DE', 49.45347000, 12.17996000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q534634'),
(27631, 'Nachrodt-Wiblingwerde', 3017, 'NW', 82, 'DE', 51.31667000, 7.61667000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q5279'),
(27632, 'Nachterstedt', 3011, 'ST', 82, 'DE', 51.80081000, 11.33489000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q673016'),
(27633, 'Nackenheim', 3019, 'RP', 82, 'DE', 49.91528000, 8.33889000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q683933'),
(27634, 'Nagel', 3009, 'BY', 82, 'DE', 49.98333000, 11.91667000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q683933'),
(27635, 'Nagold', 3006, 'BW', 82, 'DE', 48.54980000, 8.72366000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q683933'),
(27636, 'Nahe', 3005, 'SH', 82, 'DE', 53.80000000, 10.13333000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q683933'),
(27637, 'Nahrendorf', 3008, 'NI', 82, 'DE', 53.17389000, 10.81381000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q632564'),
(27638, 'Naila', 3009, 'BY', 82, 'DE', 50.33034000, 11.70463000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q503450'),
(27639, 'Nalbach', 3020, 'SL', 82, 'DE', 49.38333000, 6.78333000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q553221'),
(27640, 'Namborn', 3020, 'SL', 82, 'DE', 49.52166000, 7.14070000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q555251'),
(27641, 'Nandlstadt', 3009, 'BY', 82, 'DE', 48.53644000, 11.80730000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q503330'),
(27642, 'Nanzdietschweiler', 3019, 'RP', 82, 'DE', 49.45000000, 7.45000000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q643174'),
(27643, 'Narsdorf', 3021, 'SN', 82, 'DE', 51.01667000, 12.71667000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q10753'),
(27644, 'Nassau', 3019, 'RP', 82, 'DE', 50.31453000, 7.80025000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q10753'),
(27645, 'Nastätten', 3019, 'RP', 82, 'DE', 50.19883000, 7.85892000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q552491'),
(27646, 'Nattheim', 3006, 'BW', 82, 'DE', 48.69963000, 10.24209000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, '********'),
(27647, 'Nauen', 3013, 'BB', 82, 'DE', 52.60701000, 12.87374000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q581994'),
(27648, 'Nauendorf', 3011, 'ST', 82, 'DE', 51.60178000, 11.88525000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q32174609'),
(27649, 'Naumburg', 3011, 'ST', 82, 'DE', 51.14987000, 11.80979000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q15986'),
(27650, 'Naumburg', 3018, 'HE', 82, 'DE', 51.24816000, 9.16569000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q15986'),
(27651, 'Naundorf', 3021, 'SN', 82, 'DE', 51.25718000, 13.10810000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q15986'),
(27652, 'Naunhof', 3021, 'SN', 82, 'DE', 51.27770000, 12.58827000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q10771'),
(27653, 'Nauort', 3019, 'RP', 82, 'DE', 50.46667000, 7.63333000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q662193'),
(27654, 'Nauroth', 3019, 'RP', 82, 'DE', 50.69927000, 7.87543000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q662193'),
(27655, 'Nauwalde', 3021, 'SN', 82, 'DE', 51.41837000, 13.40974000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q8744'),
(27656, 'Nebelschütz', 3021, 'SN', 82, 'DE', 51.26227000, 14.15849000, '2019-10-05 22:41:19', '2020-05-01 17:22:49', 1, 'Q93264'),
(27657, 'Nebra', 3011, 'ST', 82, 'DE', 51.28810000, 11.57749000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q32175509'),
(27658, 'Neckarbischofsheim', 3006, 'BW', 82, 'DE', 49.29625000, 8.96380000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q82362'),
(27659, 'Neckargemünd', 3006, 'BW', 82, 'DE', 49.38899000, 8.79590000, '2019-10-05 22:41:19', '2020-05-01 17:22:47', 1, 'Q82927'),
(27660, 'Neckargerach', 3006, 'BW', 82, 'DE', 49.40000000, 9.06667000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q525170'),
(27661, 'Neckarsteinach', 3018, 'HE', 82, 'DE', 49.40735000, 8.84342000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q1973855'),
(27662, 'Neckarsulm', 3006, 'BW', 82, 'DE', 49.18912000, 9.22527000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q488708'),
(27663, 'Neckartailfingen', 3006, 'BW', 82, 'DE', 48.61172000, 9.26371000, '2019-10-05 22:41:19', '2019-10-05 22:41:19', 1, 'Q81642'),
(27664, 'Neckartenzlingen', 3006, 'BW', 82, 'DE', 48.58981000, 9.23478000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q80659'),
(27665, 'Neckarwestheim', 3006, 'BW', 82, 'DE', 49.04694000, 9.19000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q160936'),
(27666, 'Neckarzimmern', 3006, 'BW', 82, 'DE', 49.31917000, 9.13278000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q536318'),
(27667, 'Negernbötel', 3005, 'SH', 82, 'DE', 53.98333000, 10.25000000, '2019-10-05 22:41:20', '2020-05-01 17:22:49', 1, 'Q536318'),
(27668, 'Nehren', 3006, 'BW', 82, 'DE', 48.43097000, 9.06990000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q536318'),
(27669, 'Neidenstein', 3006, 'BW', 82, 'DE', 49.31667000, 8.88472000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q82225'),
(27670, 'Neidlingen', 3006, 'BW', 82, 'DE', 48.57865000, 9.56454000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q80649'),
(27671, 'Neinstedt', 3011, 'ST', 82, 'DE', 51.74950000, 11.08721000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q822088'),
(27672, 'Nellingen', 3006, 'BW', 82, 'DE', 48.54196000, 9.79053000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q540835'),
(27673, 'Nennhausen', 3013, 'BB', 82, 'DE', 52.60000000, 12.50000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q585364'),
(27674, 'Nennslingen', 3009, 'BY', 82, 'DE', 49.04709000, 11.13052000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q526087'),
(27675, 'Nentershausen', 3018, 'HE', 82, 'DE', 51.01667000, 9.93333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q526087'),
(27676, 'Nentershausen', 3019, 'RP', 82, 'DE', 50.41667000, 7.93333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q526087'),
(27677, 'Nerchau', 3021, 'SN', 82, 'DE', 51.27094000, 12.78912000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q631108'),
(27678, 'Neresheim', 3006, 'BW', 82, 'DE', 48.75511000, 10.33041000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q572431'),
(27679, 'Nersingen', 3009, 'BY', 82, 'DE', 48.42829000, 10.12356000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q514223'),
(27680, 'Neschwitz', 3021, 'SN', 82, 'DE', 51.27056000, 14.32900000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q93265'),
(27681, 'Nessa', 3011, 'ST', 82, 'DE', 51.14565000, 12.00874000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32177709'),
(27682, 'Nesselwang', 3009, 'BY', 82, 'DE', 47.62342000, 10.50243000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q521510'),
(27683, 'Netphen', 3017, 'NW', 82, 'DE', 50.91667000, 8.10000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q10950'),
(27684, 'Nettersheim', 3017, 'NW', 82, 'DE', 50.49372000, 6.62896000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32177947'),
(27685, 'Nettetal', 3017, 'NW', 82, 'DE', 51.31667000, 6.28333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q14810'),
(27686, 'Netzschkau', 3021, 'SN', 82, 'DE', 50.61411000, 12.24382000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q46890'),
(27687, 'Neu Darchau', 3008, 'NI', 82, 'DE', 53.22901000, 10.88529000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q630467'),
(27688, 'Neu Isenburg', 3018, 'HE', 82, 'DE', 50.04832000, 8.69406000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q14920'),
(27689, 'Neu Kaliß', 3007, 'MV', 82, 'DE', 53.17466000, 11.29446000, '2019-10-05 22:41:20', '2020-05-01 17:22:49', 1, 'Q673683'),
(27690, 'Neu Wulmstorf', 3008, 'NI', 82, 'DE', 53.46667000, 9.80000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q673683'),
(27691, 'Neu Zauche', 3013, 'BB', 82, 'DE', 51.92757000, 14.08812000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q673683'),
(27692, 'Neu-Anspach', 3018, 'HE', 82, 'DE', 50.31667000, 8.50000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q622562'),
(27693, 'Neu-Hohenschönhausen', 3010, 'BE', 82, 'DE', 52.56681000, 13.51255000, '2019-10-05 22:41:20', '2020-05-01 17:22:48', 1, 'Q569866'),
(27694, 'Neu-Pattern', 3017, 'NW', 82, 'DE', 50.88854000, 6.27688000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q569866'),
(27695, 'Neu-Ulm', 3009, 'BY', 82, 'DE', 48.39279000, 10.01112000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q4120'),
(27696, 'Neualbenreuth', 3009, 'BY', 82, 'DE', 49.98037000, 12.44373000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q553761'),
(27697, 'Neuberend', 3005, 'SH', 82, 'DE', 54.55000000, 9.53333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q556904'),
(27698, 'Neubeuern', 3009, 'BY', 82, 'DE', 47.77368000, 12.14002000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q536363'),
(27699, 'Neubiberg', 3009, 'BY', 82, 'DE', 48.07710000, 11.65812000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q258208'),
(27700, 'Neubrandenburg', 3007, 'MV', 82, 'DE', 53.56414000, 13.27532000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q3958'),
(27701, 'Neubrunn', 3009, 'BY', 82, 'DE', 49.73088000, 9.67161000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q431504'),
(27702, 'Neubrück', 3017, 'NW', 82, 'DE', 51.13434000, 6.63857000, '2019-10-05 22:41:20', '2020-05-01 17:22:49', 1, 'Q389803'),
(27703, 'Neubrück', 3008, 'NI', 82, 'DE', 52.37088000, 10.41761000, '2019-10-05 22:41:20', '2020-05-01 17:22:48', 1, 'Q1979311'),
(27704, 'Neubukow', 3007, 'MV', 82, 'DE', 54.03177000, 11.67391000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q9638'),
(27705, 'Neubulach', 3006, 'BW', 82, 'DE', 48.66092000, 8.69611000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q541843'),
(27706, 'Neuburg', 3007, 'MV', 82, 'DE', 53.40677000, 11.91742000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q541843'),
(27707, 'Neuburg', 3009, 'BY', 82, 'DE', 48.50654000, 13.44718000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q506258'),
(27708, 'Neuburg', 3019, 'RP', 82, 'DE', 48.98933000, 8.24715000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32179152'),
(27709, 'Neuburg an der Donau', 3009, 'BY', 82, 'DE', 48.73218000, 11.18709000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q125652'),
(27710, 'Neubörger', 3008, 'NI', 82, 'DE', 52.95815000, 7.44839000, '2019-10-05 22:41:20', '2020-05-01 17:22:48', 1, 'Q376796'),
(27711, 'Neudenau', 3006, 'BW', 82, 'DE', 49.29181000, 9.26975000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q503291'),
(27712, 'Neudietendorf', 3015, 'TH', 82, 'DE', 50.91250000, 10.91346000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q701263'),
(27713, 'Neudorf-Bornstein', 3005, 'SH', 82, 'DE', 54.43333000, 9.95000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q701263'),
(27714, 'Neudrossenfeld', 3009, 'BY', 82, 'DE', 50.01667000, 11.50000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q503569'),
(27715, 'Neue Neustadt', 3011, 'ST', 82, 'DE', 52.15000000, 11.63333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q1979660'),
(27716, 'Neuehrenfeld', 3017, 'NW', 82, 'DE', 50.95753000, 6.93611000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q683292'),
(27717, 'Neuenburg am Rhein', 3006, 'BW', 82, 'DE', 47.81431000, 7.56005000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q509783'),
(27718, 'Neuenbürg', 3006, 'BW', 82, 'DE', 48.84516000, 8.59574000, '2019-10-05 22:41:20', '2020-05-01 17:22:47', 1, 'Q80759'),
(27719, 'Neuendettelsau', 3009, 'BY', 82, 'DE', 49.28333000, 10.78333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q502872'),
(27720, 'Neuenhagen', 3013, 'BB', 82, 'DE', 52.52985000, 13.68914000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q582690'),
(27721, 'Neuenkirchen', 3008, 'NI', 82, 'DE', 52.13778000, 8.38835000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32180245'),
(27722, 'Neuenkirchen', 3007, 'MV', 82, 'DE', 53.59729000, 13.36942000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32180245'),
(27723, 'Neuenkirchen', 3017, 'NW', 82, 'DE', 52.24472000, 7.37183000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32180245'),
(27724, 'Neuenmarkt', 3009, 'BY', 82, 'DE', 50.09193000, 11.58033000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q504925'),
(27725, 'Neuenrade', 3017, 'NW', 82, 'DE', 51.28278000, 7.78250000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q5535'),
(27726, 'Neuensalz', 3021, 'SN', 82, 'DE', 50.50000000, 12.21667000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q46773'),
(27727, 'Neuenstadt am Kocher', 3006, 'BW', 82, 'DE', 49.23498000, 9.33215000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32180501'),
(27728, 'Neuenstein', 3006, 'BW', 82, 'DE', 49.20490000, 9.58000000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32180519'),
(27729, 'Neuental', 3018, 'HE', 82, 'DE', 51.00000000, 9.21667000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q126873'),
(27730, 'Neuerburg', 3019, 'RP', 82, 'DE', 50.00102000, 6.94828000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32180851'),
(27731, 'Neufahrn', 3009, 'BY', 82, 'DE', 48.25587000, 12.44078000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32181008'),
(27732, 'Neufahrn bei Freising', 3009, 'BY', 82, 'DE', 48.31588000, 11.66316000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q31658'),
(27733, 'Neuffen', 3006, 'BW', 82, 'DE', 48.55460000, 9.37550000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q82520'),
(27734, 'Neufra', 3006, 'BW', 82, 'DE', 48.12880000, 9.47554000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q82520'),
(27735, 'Neufraunhofen', 3009, 'BY', 82, 'DE', 48.40000000, 12.21667000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q511656'),
(27736, 'Neugersdorf', 3021, 'SN', 82, 'DE', 50.97729000, 14.60881000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q572595'),
(27737, 'Neuhardenberg', 3013, 'BB', 82, 'DE', 52.59601000, 14.23768000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q582614'),
(27738, 'Neuharlingersiel', 3008, 'NI', 82, 'DE', 53.69993000, 7.70288000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q502108'),
(27739, 'Neuhaus', 3015, 'TH', 82, 'DE', 50.68333000, 10.93333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32181363'),
(27740, 'Neuhaus am Inn', 3009, 'BY', 82, 'DE', 48.46086000, 13.42083000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q517810'),
(27741, 'Neuhaus am Rennweg', 3015, 'TH', 82, 'DE', 50.51006000, 11.13787000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q371158'),
(27742, 'Neuhaus an der Oste', 3008, 'NI', 82, 'DE', 53.80043000, 9.03348000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q509293'),
(27743, 'Neuhaus an der Pegnitz', 3009, 'BY', 82, 'DE', 49.62799000, 11.55066000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q554089'),
(27744, 'Neuhaus-Schierschnitz', 3015, 'TH', 82, 'DE', 50.31237000, 11.24019000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q638071'),
(27745, 'Neuhausen', 3021, 'SN', 82, 'DE', 50.67647000, 13.46750000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32181559'),
(27746, 'Neuhausen', 3006, 'BW', 82, 'DE', 48.79260000, 8.77649000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q32181559'),
(27747, 'Neuhausen auf den Fildern', 3006, 'BW', 82, 'DE', 48.68333000, 9.28333000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q80610'),
(27748, 'Neuhof', 3018, 'HE', 82, 'DE', 50.45313000, 9.61750000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q80610'),
(27749, 'Neuhof an der Zenn', 3009, 'BY', 82, 'DE', 49.45690000, 10.64548000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q504896'),
(27750, 'Neuhofen', 3019, 'RP', 82, 'DE', 49.42778000, 8.42472000, '2019-10-05 22:41:20', '2019-10-05 22:41:20', 1, 'Q504896'),
(27751, 'Neuhäusel', 3019, 'RP', 82, 'DE', 50.38360000, 7.70960000, '2019-10-05 22:41:21', '2020-05-01 17:22:49', 1, 'Q645565'),
(27752, 'Neuhütten', 3009, 'BY', 82, 'DE', 50.00000000, 9.41667000, '2019-10-05 22:41:21', '2020-05-01 17:22:47', 1, 'Q645565'),
(27753, 'Neukalen', 3007, 'MV', 82, 'DE', 53.82275000, 12.79015000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q50958'),
(27754, 'Neukamperfehn', 3008, 'NI', 82, 'DE', 53.33642000, 7.56189000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q505564'),
(27755, 'Neukieritzsch', 3021, 'SN', 82, 'DE', 51.14995000, 12.41090000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q10755'),
(27756, 'Neukirch', 3021, 'SN', 82, 'DE', 51.28333000, 13.98333000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q10755'),
(27757, 'Neukirch', 3006, 'BW', 82, 'DE', 47.65738000, 9.70333000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q10755'),
(27758, 'Neukirch/Lausitz', 3021, 'SN', 82, 'DE', 51.09727000, 14.30789000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q93270'),
(27759, 'Neukirchen', 3009, 'BY', 82, 'DE', 49.26015000, 12.96921000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q32182740'),
(27760, 'Neukirchen', 3005, 'SH', 82, 'DE', 54.21465000, 10.55381000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q32182796'),
(27761, 'Neukirchen', 3018, 'HE', 82, 'DE', 50.86906000, 9.34655000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q624847'),
(27762, 'Neukirchen', 3021, 'SN', 82, 'DE', 50.77955000, 12.86755000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q32182759'),
(27763, 'Neukirchen-Balbini', 3009, 'BY', 82, 'DE', 49.29101000, 12.43643000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q558667'),
(27764, 'Neukloster', 3007, 'MV', 82, 'DE', 53.86548000, 11.68512000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q540063'),
(27765, 'Neukölln', 3010, 'BE', 82, 'DE', 52.47719000, 13.43126000, '2019-10-05 22:41:21', '2020-05-01 17:22:48', 1, 'Q169492'),
(27766, 'Neuler', 3006, 'BW', 82, 'DE', 48.92803000, 10.06888000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q47167'),
(27767, 'Neulewin', 3013, 'BB', 82, 'DE', 52.72434000, 14.27922000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q585188'),
(27768, 'Neulußheim', 3006, 'BW', 82, 'DE', 49.29806000, 8.51833000, '2019-10-05 22:41:21', '2020-05-01 17:22:47', 1, 'Q22965'),
(27769, 'Neumagen-Dhron', 3019, 'RP', 82, 'DE', 49.85553000, 6.89777000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q572352'),
(27770, 'Neumark', 3021, 'SN', 82, 'DE', 50.66010000, 12.35619000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q572352'),
(27771, 'Neumarkt in der Oberpfalz', 3009, 'BY', 82, 'DE', 49.28028000, 11.46278000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q14887'),
(27772, 'Neumarkt-Sankt Veit', 3009, 'BY', 82, 'DE', 48.36051000, 12.50723000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q32183314'),
(27773, 'Neumühle', 3007, 'MV', 82, 'DE', 53.63171000, 11.37085000, '2019-10-05 22:41:21', '2020-05-01 17:22:49', 1, 'Q32183314'),
(27774, 'Neumünster', 3005, 'SH', 82, 'DE', 54.07477000, 9.98195000, '2019-10-05 22:41:21', '2020-05-01 17:22:49', 1, 'Q3879'),
(27775, 'Neunburg vorm Wald', 3009, 'BY', 82, 'DE', 49.34782000, 12.38621000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q522691'),
(27776, 'Neundorf', 3011, 'ST', 82, 'DE', 51.82080000, 11.57484000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q703821'),
(27777, 'Neunkhausen', 3019, 'RP', 82, 'DE', 50.70722000, 7.90278000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q525214'),
(27778, 'Neunkirchen', 3006, 'BW', 82, 'DE', 49.38694000, 9.01056000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q525214'),
(27779, 'Neunkirchen', 3017, 'NW', 82, 'DE', 50.80000000, 8.00000000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q10958'),
(27780, 'Neunkirchen', 3020, 'SL', 82, 'DE', 49.34449000, 7.18045000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q6880'),
(27781, 'Neunkirchen am Brand', 3009, 'BY', 82, 'DE', 49.61204000, 11.12967000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q259289'),
(27782, 'Neunkirchen am Main', 3009, 'BY', 82, 'DE', 49.92300000, 11.64793000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q1981060'),
(27783, 'Neunkirchen am Sand', 3009, 'BY', 82, 'DE', 49.52464000, 11.31955000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q524468'),
(27784, 'Neupotz', 3019, 'RP', 82, 'DE', 49.11778000, 8.31944000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q567253'),
(27785, 'Neureichenau', 3009, 'BY', 82, 'DE', 48.74861000, 13.74699000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q32183850'),
(27786, 'Neuried', 3009, 'BY', 82, 'DE', 48.09322000, 11.46561000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q32183850'),
(27787, 'Neuruppin', 3013, 'BB', 82, 'DE', 52.92815000, 12.80311000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q16085'),
(27788, 'Neusalza-Spremberg', 3021, 'SN', 82, 'DE', 51.03945000, 14.53560000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q165153'),
(27789, 'Neuschönau', 3009, 'BY', 82, 'DE', 48.88419000, 13.47576000, '2019-10-05 22:41:21', '2020-05-01 17:22:47', 1, 'Q162106'),
(27790, 'Neusitz', 3009, 'BY', 82, 'DE', 49.37185000, 10.22559000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q505552'),
(27791, 'Neusorg', 3009, 'BY', 82, 'DE', 49.93333000, 11.96667000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q547837'),
(27792, 'Neuss', 3017, 'NW', 82, 'DE', 51.19807000, 6.68504000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q2948'),
(27793, 'Neustadt', 3016, 'HH', 82, 'DE', 53.55196000, 9.98558000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q2948'),
(27794, 'Neustadt', 3019, 'RP', 82, 'DE', 49.35009000, 8.13886000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q2948'),
(27795, 'Neustadt (Hessen)', 3018, 'HE', 82, 'DE', 50.85000000, 9.11667000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q2948'),
(27796, 'Neustadt Vogtland', 3021, 'SN', 82, 'DE', 50.46100000, 12.33224000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q2948'),
(27797, 'Neustadt am Main', 3009, 'BY', 82, 'DE', 49.93021000, 9.56808000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q508822'),
(27798, 'Neustadt am Rennsteig', 3015, 'TH', 82, 'DE', 50.58333000, 10.93333000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q628013'),
(27799, 'Neustadt am Rübenberge', 3008, 'NI', 82, 'DE', 52.50462000, 9.45871000, '2019-10-05 22:41:21', '2020-05-01 17:22:48', 1, 'Q33466238'),
(27800, 'Neustadt an der Aisch', 3009, 'BY', 82, 'DE', 49.57953000, 10.61126000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q33466238'),
(27801, 'Neustadt an der Donau', 3009, 'BY', 82, 'DE', 48.80705000, 11.76952000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q269010'),
(27802, 'Neustadt an der Orla', 3015, 'TH', 82, 'DE', 50.73640000, 11.74619000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q269010'),
(27803, 'Neustadt an der Waldnaab', 3009, 'BY', 82, 'DE', 49.73287000, 12.17773000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q262594'),
(27804, 'Neustadt bei Coburg', 3009, 'BY', 82, 'DE', 50.32975000, 11.12058000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q262594'),
(27805, 'Neustadt in Holstein', 3005, 'SH', 82, 'DE', 54.10707000, 10.81450000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q499861'),
(27806, 'Neustadt in Sachsen', 3021, 'SN', 82, 'DE', 51.02844000, 14.21785000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q8210'),
(27807, 'Neustadt-Glewe', 3007, 'MV', 82, 'DE', 53.37846000, 11.59264000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q540120'),
(27808, 'Neustadt/Nord', 3017, 'NW', 82, 'DE', 50.94900000, 6.94790000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q540120'),
(27809, 'Neustadt/Süd', 3017, 'NW', 82, 'DE', 50.92509000, 6.94762000, '2019-10-05 22:41:21', '2020-05-01 17:22:49', 1, 'Q540120'),
(27810, 'Neustrelitz', 3007, 'MV', 82, 'DE', 53.36130000, 13.07292000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q20352'),
(27811, 'Neutraubling', 3009, 'BY', 82, 'DE', 48.98737000, 12.20100000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q488625'),
(27812, 'Neutrebbin', 3013, 'BB', 82, 'DE', 52.66482000, 14.22802000, '2019-10-05 22:41:21', '2019-10-05 22:41:21', 1, 'Q583048'),
(27813, 'Neuweiler', 3006, 'BW', 82, 'DE', 48.66667000, 8.60000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q553808'),
(27814, 'Neuwied', 3019, 'RP', 82, 'DE', 50.43360000, 7.47057000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q3967'),
(27815, 'Neuwittenbek', 3005, 'SH', 82, 'DE', 54.36667000, 10.01667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q3967'),
(27816, 'Neuzelle', 3013, 'BB', 82, 'DE', 52.09016000, 14.64804000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q635531'),
(27817, 'Neuötting', 3009, 'BY', 82, 'DE', 48.24102000, 12.68998000, '2019-10-05 22:41:22', '2020-05-01 17:22:47', 1, 'Q269473'),
(27818, 'Neverin', 3007, 'MV', 82, 'DE', 53.62067000, 13.33660000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q49436'),
(27819, 'Newel', 3019, 'RP', 82, 'DE', 49.81240000, 6.58304000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q49436'),
(27820, 'Nickenich', 3019, 'RP', 82, 'DE', 50.41421000, 7.32728000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q567117'),
(27821, 'Nidda', 3018, 'HE', 82, 'DE', 50.41330000, 9.00638000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q567117'),
(27822, 'Nidderau', 3018, 'HE', 82, 'DE', 50.23805000, 8.86704000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q540743'),
(27823, 'Nideggen', 3017, 'NW', 82, 'DE', 50.69268000, 6.48437000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q200105'),
(27824, 'Niebüll', 3005, 'SH', 82, 'DE', 54.78663000, 8.82854000, '2019-10-05 22:41:22', '2020-05-01 17:22:49', 1, 'Q21014'),
(27825, 'Niedenstein', 3018, 'HE', 82, 'DE', 51.23342000, 9.31029000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q570414'),
(27826, 'Nieder-Gründau', 3018, 'HE', 82, 'DE', 50.21097000, 9.10801000, '2019-10-05 22:41:22', '2020-05-01 17:22:48', 1, 'Q570414'),
(27827, 'Nieder-Ingelheim', 3019, 'RP', 82, 'DE', 49.97756000, 8.07246000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q1648323'),
(27828, 'Nieder-Olm', 3019, 'RP', 82, 'DE', 49.91166000, 8.20533000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q552643'),
(27829, 'Niederaichbach', 3009, 'BY', 82, 'DE', 48.60000000, 12.31667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q511685'),
(27830, 'Niederalteich', 3009, 'BY', 82, 'DE', 48.76564000, 13.02412000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32187100'),
(27831, 'Niederau', 3021, 'SN', 82, 'DE', 51.17730000, 13.54563000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32187100'),
(27832, 'Niederaula', 3018, 'HE', 82, 'DE', 50.80000000, 9.60000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q625888');

