INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(92027, '<PERSON><PERSON><PERSON>', 4745, '<PERSON>', 181, '<PERSON><PERSON>', 44.84204000, 25.26253000, '2019-10-05 23:14:56', '2020-05-01 17:23:09', 1, 'Q12168008'),
(92028, '<PERSON><PERSON><PERSON>', 4750, 'GJ', 181, 'RO', 44.80305000, 23.63617000, '2019-10-05 23:14:56', '2019-10-05 23:14:56', 1, 'Q12724858'),
(92029, '<PERSON><PERSON><PERSON>', 4744, '<PERSON>', 181, 'RO', 46.25297000, 27.23828000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q3407894'),
(92030, '<PERSON><PERSON><PERSON>', 4723, '<PERSON><PERSON>', 181, '<PERSON><PERSON>', 46.84896000, 21.91914000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q16425659'),
(92031, '<PERSON><PERSON>na <PERSON>s<PERSON><PERSON><PERSON>', 4751, '<PERSON>H', 181, '<PERSON><PERSON>', 44.66283000, 22.83939000, '2019-10-05 23:14:56', '2019-10-05 23:14:56', 1, 'Q2335259'),
(92032, '<PERSON><PERSON>na H<PERSON><PERSON><PERSON>ti', 4722, 'AG', 181, 'RO', 44.51091000, 24.78294000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q12724860'),
(92033, 'Comuna Hârtieşti', 4722, 'AG', 181, 'RO', 45.12629000, 25.11343000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q12724861'),
(92034, 'Comuna Hârtop', 4720, 'SV', 181, 'RO', 47.49000000, 26.37226000, '2019-10-05 23:14:56', '2020-05-01 17:23:11', 1, 'Q12724862'),
(92035, 'Comuna Hârşeni', 4759, 'BV', 181, 'RO', 45.73420000, 25.02404000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q1081350'),
(92036, 'Comuna Hăghig', 4754, 'CV', 181, 'RO', 45.85362000, 25.59501000, '2019-10-05 23:14:56', '2020-05-01 17:23:09', 1, 'Q16426315'),
(92037, 'Comuna Hălchiu', 4759, 'BV', 181, 'RO', 45.76743000, 25.52991000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q1094277'),
(92038, 'Comuna Hălmagiu', 4739, 'AR', 181, 'RO', 46.27536000, 22.58183000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q12724863'),
(92039, 'Comuna Hălmăgel', 4739, 'AR', 181, 'RO', 46.27662000, 22.63180000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q5060740'),
(92040, 'Comuna Hălăuceşti', 4735, 'IS', 181, 'RO', 47.10593000, 26.80967000, '2019-10-05 23:14:56', '2020-05-01 17:23:10', 1, 'Q5114085'),
(92041, 'Comuna Hăneşti', 4740, 'BT', 181, 'RO', 47.91987000, 27.01151000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q12724864'),
(92042, 'Comuna Hănţeşti', 4720, 'SV', 181, 'RO', 47.76696000, 26.34232000, '2019-10-05 23:14:56', '2020-05-01 17:23:11', 1, 'Q12724865'),
(92043, 'Comuna Hărman', 4759, 'BV', 181, 'RO', 45.73727000, 25.70451000, '2019-10-05 23:14:56', '2020-05-01 17:23:08', 1, 'Q1097253'),
(92044, 'Comuna Hărmăneşti', 4735, 'IS', 181, 'RO', 47.27087000, 26.81322000, '2019-10-05 23:14:56', '2020-05-01 17:23:10', 1, 'Q2718480'),
(92045, 'Comuna Hărău', 4721, 'HD', 181, 'RO', 45.90555000, 22.98136000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q1034251'),
(92046, 'Comuna Hăşmaş', 4739, 'AR', 181, 'RO', 46.52217000, 22.07003000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q10765984'),
(92047, 'Comuna I. L. Caragiale', 4745, 'DB', 181, 'RO', 44.92189000, 25.68409000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2005478'),
(92048, 'Comuna I.C.Brãtianu', 4727, 'TL', 181, 'RO', 45.40648000, 28.05332000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q12724869'),
(92049, 'Comuna Iablaniţa', 4753, 'CS', 181, 'RO', 44.97480000, 22.29180000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q12173234'),
(92050, 'Comuna Iacobeni', 4720, 'SV', 181, 'RO', 47.44112000, 25.32431000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16427516'),
(92051, 'Comuna Iacobeni', 4755, 'SB', 181, 'RO', 46.04440000, 24.75456000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16439999'),
(92052, 'Comuna Iana', 4752, 'VS', 181, 'RO', 46.40438000, 27.55265000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724866'),
(92053, 'Comuna Ianca', 4738, 'OT', 181, 'RO', 43.77472000, 24.19165000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724870'),
(92054, 'Comuna Iancu Jianu', 4738, 'OT', 181, 'RO', 44.51807000, 24.01257000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724867'),
(92055, 'Comuna Iara', 4734, 'CJ', 181, 'RO', 46.54268000, 23.52749000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12173845'),
(92056, 'Comuna Iaslovăţ', 4720, 'SV', 181, 'RO', 47.75862000, 25.97742000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q16427519'),
(92057, 'Comuna Ibăneşti', 4752, 'VS', 181, 'RO', 46.40507000, 27.62043000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q12724871'),
(92058, 'Comuna Ibăneşti', 4740, 'BT', 181, 'RO', 48.04435000, 26.40450000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q12724868'),
(92059, 'Comuna Iclod', 4734, 'CJ', 181, 'RO', 46.99174000, 23.81618000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16426206'),
(92060, 'Comuna Icoana', 4738, 'OT', 181, 'RO', 44.40682000, 24.72551000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16427122'),
(92061, 'Comuna Icuseşti', 4731, 'NT', 181, 'RO', 46.79894000, 26.99076000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q5112393'),
(92062, 'Comuna Iecea Mare', 4748, 'TM', 181, 'RO', 45.85074000, 20.89310000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5059673'),
(92063, 'Comuna Iedera', 4745, 'DB', 181, 'RO', 45.03333000, 25.63333000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2473449'),
(92064, 'Comuna Iepureşti', 4726, 'GR', 181, 'RO', 44.26376000, 25.88040000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q16426599'),
(92065, 'Comuna Ieud', 4760, 'MM', 181, 'RO', 47.67739000, 24.23378000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5055984'),
(92066, 'Comuna Ighiu', 4724, 'AB', 181, 'RO', 46.14854000, 23.51232000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16425280'),
(92067, 'Comuna Igneşti', 4739, 'AR', 181, 'RO', 46.43388000, 22.18227000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q5061552'),
(92068, 'Comuna Ileana', 4732, 'CL', 181, 'RO', 44.52732000, 26.68993000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16426110'),
(92069, 'Comuna Ileanda', 4741, 'SJ', 181, 'RO', 47.34501000, 23.60827000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16427347'),
(92070, 'Comuna Ilia', 4721, 'HD', 181, 'RO', 45.94059000, 22.67861000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q1096212'),
(92071, 'Comuna Ilieni', 4754, 'CV', 181, 'RO', 45.80054000, 25.76436000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5058967'),
(92072, 'Comuna Ilişeşti', 4720, 'SV', 181, 'RO', 47.61665000, 26.05520000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q16427522'),
(92073, 'Comuna Iloviţa', 4751, 'MH', 181, 'RO', 44.78026000, 22.49234000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q12724874'),
(92074, 'Comuna Ilovăt', 4751, 'MH', 181, 'RO', 44.81543000, 22.73749000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q12724877'),
(92075, 'Comuna Ilva Mare', 4733, 'BN', 181, 'RO', 47.35100000, 24.89630000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5070439'),
(92076, 'Comuna Ilva Mică', 4733, 'BN', 181, 'RO', 47.30760000, 24.66035000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q16467093'),
(92077, 'Comuna Independenţa', 4747, 'GL', 181, 'RO', 45.47466000, 27.76025000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q3725478'),
(92078, 'Comuna Independenţa', 4732, 'CL', 181, 'RO', 44.27922000, 27.16053000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q2537466'),
(92079, 'Comuna Independenţa', 4737, 'CT', 181, 'RO', 43.96020000, 28.06290000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q5052508'),
(92080, 'Comuna Ineu', 4723, 'BH', 181, 'RO', 47.09119000, 22.10816000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16425663'),
(92081, 'Comuna Ion Corvin', 4737, 'CT', 181, 'RO', 44.10717000, 27.80268000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5052950'),
(92082, 'Comuna Ion Creangă', 4731, 'NT', 181, 'RO', 46.86069000, 27.00358000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q3878982'),
(92083, 'Comuna Ion Neculce', 4735, 'IS', 181, 'RO', 47.20159000, 27.03221000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5114661'),
(92084, 'Comuna Ion Roată', 4743, 'IL', 181, 'RO', 44.66757000, 26.76803000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q2540525'),
(92085, 'Comuna Ioneşti', 4757, 'VL', 181, 'RO', 44.85429000, 24.23134000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q3915641'),
(92086, 'Comuna Ioneşti', 4750, 'GJ', 181, 'RO', 44.61636000, 23.42688000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q12724876'),
(92087, 'Comuna Iordãcheanu', 4729, 'PH', 181, 'RO', 45.04671000, 26.22605000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q2605195'),
(92088, 'Comuna Ip', 4741, 'SJ', 181, 'RO', 47.22138000, 22.62406000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16427350'),
(92089, 'Comuna Ipatele', 4735, 'IS', 181, 'RO', 46.90390000, 27.43762000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5113946'),
(92090, 'Comuna Ipotesti', 4738, 'OT', 181, 'RO', 44.32529000, 24.40089000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16467196'),
(92091, 'Comuna Ipoteşti', 4720, 'SV', 181, 'RO', 47.62828000, 26.29797000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q16427525'),
(92092, 'Comuna Iratoşu', 4739, 'AR', 181, 'RO', 46.29414000, 21.18750000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q5060876'),
(92093, 'Comuna Islaz', 4728, 'TR', 181, 'RO', 43.75179000, 24.74381000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724880'),
(92094, 'Comuna Istria', 4737, 'CT', 181, 'RO', 44.55487000, 28.68155000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5053165'),
(92095, 'Comuna Isverna', 4751, 'MH', 181, 'RO', 44.96572000, 22.67037000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16426950'),
(92096, 'Comuna Isvoarele', 4726, 'GR', 181, 'RO', 44.16855000, 26.29673000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q3786796'),
(92097, 'Comuna Iteşti', 4744, 'BC', 181, 'RO', 46.67368000, 26.85555000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q2626023'),
(92098, 'Comuna Iveşti', 4747, 'GL', 181, 'RO', 45.66843000, 27.52467000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q3725487'),
(92099, 'Comuna Iveşti', 4752, 'VS', 181, 'RO', 46.19492000, 27.53670000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q13542616'),
(92100, 'Comuna Ivăneşti', 4752, 'VS', 181, 'RO', 46.66216000, 27.44465000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q12724881'),
(92101, 'Comuna Izbiceni', 4738, 'OT', 181, 'RO', 43.82418000, 24.66415000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5116747'),
(92102, 'Comuna Izvoare', 4742, 'DJ', 181, 'RO', 44.15000000, 23.28333000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724883'),
(92103, 'Comuna Izvoarele', 4726, 'GR', 181, 'RO', 44.04869000, 25.80326000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2717580'),
(92104, 'Comuna Izvoarele', 4738, 'OT', 181, 'RO', 44.25958000, 24.53144000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16427126'),
(92105, 'Comuna Izvoarele', 4729, 'PH', 181, 'RO', 45.27102000, 26.00931000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16427224'),
(92106, 'Comuna Izvoarele', 4728, 'TR', 181, 'RO', 43.82717000, 25.39459000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q13710099'),
(92107, 'Comuna Izvoarele', 4727, 'TL', 181, 'RO', 45.03932000, 28.53351000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5112797'),
(92108, 'Comuna Izvoarele Sucevei', 4720, 'SV', 181, 'RO', 47.75766000, 25.19406000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12074009'),
(92109, 'Comuna Izvoru', 4722, 'AG', 181, 'RO', 44.49414000, 25.06470000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724882'),
(92110, 'Comuna Izvoru Berheciului', 4744, 'BC', 181, 'RO', 46.57693000, 27.22842000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2467643'),
(92111, 'Comuna Izvoru Bârzii', 4751, 'MH', 181, 'RO', 44.70830000, 22.67003000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q16426953'),
(92112, 'Comuna Izvoru Crişului', 4734, 'CJ', 181, 'RO', 46.84542000, 23.10025000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q12724885'),
(92113, 'Comuna Işalniţa', 4742, 'DJ', 181, 'RO', 44.39390000, 23.73711000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q12724884'),
(92114, 'Comuna Jamu Mare', 4748, 'TM', 181, 'RO', 45.26768000, 21.44318000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5053719'),
(92115, 'Comuna Jariştea', 4758, 'VN', 181, 'RO', 45.79987000, 27.04257000, '2019-10-05 23:14:57', '2020-05-01 17:23:12', 1, 'Q2721361'),
(92116, 'Comuna Jebel', 4748, 'TM', 181, 'RO', 45.56020000, 21.21805000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5062479'),
(92117, 'Comuna Jegălia', 4732, 'CL', 181, 'RO', 44.29436000, 27.64286000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q2717043'),
(92118, 'Comuna Jiana', 4751, 'MH', 181, 'RO', 44.39156000, 22.71076000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724886'),
(92119, 'Comuna Jibert', 4759, 'BV', 181, 'RO', 46.00173000, 25.07355000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q1190832'),
(92120, 'Comuna Jichişu De Jos', 4734, 'CJ', 181, 'RO', 47.12028000, 23.76788000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q16426209'),
(92121, 'Comuna Jidvei', 4724, 'AB', 181, 'RO', 46.23083000, 24.11475000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16425286'),
(92122, 'Comuna Jijila', 4727, 'TL', 181, 'RO', 45.32554000, 28.15710000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12104506'),
(92123, 'Comuna Jilava', 4725, 'IF', 181, 'RO', 44.32863000, 26.07754000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q1654950'),
(92124, 'Comuna Jilavele', 4743, 'IL', 181, 'RO', 44.77522000, 26.52478000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16426790'),
(92125, 'Comuna Jina', 4755, 'SB', 181, 'RO', 45.78593000, 23.67793000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q16427430'),
(92126, 'Comuna Jirlău', 4736, 'BR', 181, 'RO', 45.15314000, 27.20900000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q2715891'),
(92127, 'Comuna Jitia', 4758, 'VN', 181, 'RO', 45.58480000, 26.74876000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2722279'),
(92128, 'Comuna Joiţa', 4726, 'GR', 181, 'RO', 44.49002000, 25.87205000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q5118573'),
(92129, 'Comuna Jorăşti', 4747, 'GL', 181, 'RO', 45.98544000, 27.87215000, '2019-10-05 23:14:57', '2020-05-01 17:23:09', 1, 'Q12724889'),
(92130, 'Comuna Joseni', 4749, 'HR', 181, 'RO', 46.69525000, 25.46114000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5057410'),
(92131, 'Comuna Josenii Bârgăului', 4733, 'BN', 181, 'RO', 47.21238000, 24.66351000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q5070341'),
(92132, 'Comuna Jucu', 4734, 'CJ', 181, 'RO', 46.84958000, 23.80953000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q672825'),
(92133, 'Comuna Jugureni', 4729, 'PH', 181, 'RO', 45.10156000, 26.42231000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2720356'),
(92134, 'Comuna Jupâneşti', 4750, 'GJ', 181, 'RO', 44.91241000, 23.52325000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q12724890'),
(92135, 'Comuna Jurilovca', 4727, 'TL', 181, 'RO', 44.77261000, 28.86906000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5112770'),
(92136, 'Comuna Laloşu', 4757, 'VL', 181, 'RO', 44.53046000, 24.04689000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q16440124'),
(92137, 'Comuna Lapoş', 4729, 'PH', 181, 'RO', 45.15075000, 26.42796000, '2019-10-05 23:14:57', '2020-05-01 17:23:11', 1, 'Q282378'),
(92138, 'Comuna Largu', 4756, 'BZ', 181, 'RO', 44.97448000, 27.16261000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2536168'),
(92139, 'Comuna Laslea', 4755, 'SB', 181, 'RO', 46.14966000, 24.63839000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12117155'),
(92140, 'Comuna Laza', 4752, 'VS', 181, 'RO', 46.65239000, 27.59843000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724894'),
(92141, 'Comuna Lazuri', 4746, 'SM', 181, 'RO', 47.89143000, 22.87213000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q5054973'),
(92142, 'Comuna Lazuri de Beiuş', 4723, 'BH', 181, 'RO', 46.59381000, 22.39675000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q12116973'),
(92143, 'Comuna Lechinţa', 4733, 'BN', 181, 'RO', 47.01983000, 24.32605000, '2019-10-05 23:14:57', '2020-05-01 17:23:08', 1, 'Q12117554'),
(92144, 'Comuna Lehliu', 4732, 'CL', 181, 'RO', 44.48828000, 26.82729000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q2303849'),
(92145, 'Comuna Leleasca', 4738, 'OT', 181, 'RO', 44.75768000, 24.43708000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q12724893'),
(92146, 'Comuna Lelese', 4721, 'HD', 181, 'RO', 45.73754000, 22.68923000, '2019-10-05 23:14:57', '2019-10-05 23:14:57', 1, 'Q1128833'),
(92147, 'Comuna Leleşti', 4750, 'GJ', 181, 'RO', 45.09875000, 23.19761000, '2019-10-05 23:14:57', '2020-05-01 17:23:10', 1, 'Q16426635'),
(92148, 'Comuna Leliceni', 4749, 'HR', 181, 'RO', 46.34900000, 25.84911000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5056967'),
(92149, 'Comuna Lemnia', 4754, 'CV', 181, 'RO', 46.07078000, 26.27277000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426319'),
(92150, 'Comuna Lenauheim', 4748, 'TM', 181, 'RO', 45.89042000, 20.78901000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5056532'),
(92151, 'Comuna Leorda', 4740, 'BT', 181, 'RO', 47.81862000, 26.45408000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724898'),
(92152, 'Comuna Leordeni', 4722, 'AG', 181, 'RO', 44.79117000, 25.16299000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724899'),
(92153, 'Comuna Leordina', 4760, 'MM', 181, 'RO', 47.78306000, 24.26401000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5063822'),
(92154, 'Comuna Lereşti', 4722, 'AG', 181, 'RO', 45.32789000, 25.06951000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q5067938'),
(92155, 'Comuna Lespezi', 4735, 'IS', 181, 'RO', 47.34957000, 26.68807000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426818'),
(92156, 'Comuna Letca', 4741, 'SJ', 181, 'RO', 47.34948000, 23.42865000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16427354'),
(92157, 'Comuna Letca Nouă', 4726, 'GR', 181, 'RO', 44.23326000, 25.70941000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q5118322'),
(92158, 'Comuna Letea Veche', 4744, 'BC', 181, 'RO', 46.54236000, 26.96869000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2470458'),
(92159, 'Comuna Leu', 4742, 'DJ', 181, 'RO', 44.17207000, 24.04971000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426468'),
(92160, 'Comuna Leşu', 4733, 'BN', 181, 'RO', 47.31077000, 24.76369000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q12117817'),
(92161, 'Comuna Leţcani', 4735, 'IS', 181, 'RO', 47.18012000, 27.41296000, '2019-10-05 23:14:58', '2020-05-01 17:23:10', 1, 'Q5114759'),
(92162, 'Comuna Licurici', 4750, 'GJ', 181, 'RO', 44.87544000, 23.62641000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724900'),
(92163, 'Comuna Liebling', 4748, 'TM', 181, 'RO', 45.57121000, 21.33526000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5062207'),
(92164, 'Comuna Lieşti', 4747, 'GL', 181, 'RO', 45.62018000, 27.54132000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q5053922'),
(92165, 'Comuna Limanu', 4737, 'CT', 181, 'RO', 43.78024000, 28.53931000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5052857'),
(92166, 'Comuna Lipniţa', 4737, 'CT', 181, 'RO', 44.09073000, 27.56683000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q5053057'),
(92167, 'Comuna Lipova', 4744, 'BC', 181, 'RO', 46.72016000, 27.23238000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2470477'),
(92168, 'Comuna Lipovu', 4742, 'DJ', 181, 'RO', 44.11342000, 23.62736000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426471'),
(92169, 'Comuna Lipovăţ', 4752, 'VS', 181, 'RO', 46.56352000, 27.69270000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q12724901'),
(92170, 'Comuna Lipăneşti', 4729, 'PH', 181, 'RO', 45.05799000, 26.02213000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q1078321'),
(92171, 'Comuna Lisa', 4759, 'BV', 181, 'RO', 45.72760000, 24.86876000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q3242363'),
(92172, 'Comuna Lisa', 4728, 'TR', 181, 'RO', 43.77230000, 25.14919000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724902'),
(92173, 'Comuna Livada', 4739, 'AR', 181, 'RO', 46.21924000, 21.38746000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12119542'),
(92174, 'Comuna Livezi', 4757, 'VL', 181, 'RO', 44.83700000, 23.83209000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q596940'),
(92175, 'Comuna Livezi', 4744, 'BC', 181, 'RO', 46.39932000, 26.72438000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2469316'),
(92176, 'Comuna Livezile', 4724, 'AB', 181, 'RO', 46.37559000, 23.58563000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16425290'),
(92177, 'Comuna Livezile', 4733, 'BN', 181, 'RO', 47.17744000, 24.63595000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5055496'),
(92178, 'Comuna Livezile', 4748, 'TM', 181, 'RO', 45.40994000, 21.06179000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5029714'),
(92179, 'Comuna Livezile', 4751, 'MH', 181, 'RO', 44.54482000, 22.86644000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426956'),
(92180, 'Comuna Liţa', 4728, 'TR', 181, 'RO', 43.79545000, 24.82157000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q12724903'),
(92181, 'Comuna Loamneş', 4755, 'SB', 181, 'RO', 45.95467000, 24.04197000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q5066037'),
(92182, 'Comuna Logreşti-Moşteni', 4750, 'GJ', 181, 'RO', 44.88848000, 23.71256000, '2019-10-05 23:14:58', '2020-05-01 17:23:10', 1, 'Q2717408'),
(92183, 'Comuna Lopadea Nouă', 4724, 'AB', 181, 'RO', 46.27782000, 23.84975000, '2019-10-05 23:14:58', '2020-05-01 17:23:07', 1, 'Q16425294'),
(92184, 'Comuna Lopătari', 4756, 'BZ', 181, 'RO', 45.48774000, 26.55233000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q2537630'),
(92185, 'Comuna Lovrin', 4748, 'TM', 181, 'RO', 45.96943000, 20.76953000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5057722'),
(92186, 'Comuna Lozna', 4741, 'SJ', 181, 'RO', 47.31510000, 23.48507000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16427358'),
(92187, 'Comuna Lozna', 4740, 'BT', 181, 'RO', 47.94410000, 26.28694000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724904'),
(92188, 'Comuna Lucieni', 4745, 'DB', 181, 'RO', 44.84766000, 25.41990000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2540310'),
(92189, 'Comuna Luciu', 4756, 'BZ', 181, 'RO', 44.97510000, 27.05781000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2537965'),
(92190, 'Comuna Ludeşti', 4745, 'DB', 181, 'RO', 44.90396000, 25.22557000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q948622'),
(92191, 'Comuna Ludoş', 4755, 'SB', 181, 'RO', 45.93336000, 23.90070000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q5066666'),
(92192, 'Comuna Lueta', 4749, 'HR', 181, 'RO', 46.28724000, 25.53719000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5063395'),
(92193, 'Comuna Lugaşu De Jos', 4723, 'BH', 181, 'RO', 47.06992000, 22.32929000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q12118612'),
(92194, 'Comuna Luica', 4732, 'CL', 181, 'RO', 44.24126000, 26.60071000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2226111'),
(92195, 'Comuna Luizi-Cãlugãra', 4744, 'BC', 181, 'RO', 46.53604000, 26.84012000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q1091647'),
(92196, 'Comuna Lumina', 4737, 'CT', 181, 'RO', 44.32756000, 28.54709000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5054376'),
(92197, 'Comuna Luna', 4734, 'CJ', 181, 'RO', 46.47522000, 23.94928000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724906'),
(92198, 'Comuna Lunca', 4723, 'BH', 181, 'RO', 46.51645000, 22.45486000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16425672'),
(92199, 'Comuna Lunca', 4740, 'BT', 181, 'RO', 47.62751000, 26.99240000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724908'),
(92200, 'Comuna Lunca', 4728, 'TR', 181, 'RO', 43.85788000, 24.78610000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724911'),
(92201, 'Comuna Lunca Banului', 4752, 'VS', 181, 'RO', 46.56640000, 28.19790000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724910'),
(92202, 'Comuna Lunca Cernii De Jos', 4721, 'HD', 181, 'RO', 45.64302000, 22.58438000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q678638'),
(92203, 'Comuna Lunca Corbului', 4722, 'AG', 181, 'RO', 44.68361000, 24.76056000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724909'),
(92204, 'Comuna Lunca Ilvei', 4733, 'BN', 181, 'RO', 47.36409000, 24.97106000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q3843962'),
(92205, 'Comuna Lunca Mureşului', 4724, 'AB', 181, 'RO', 46.42943000, 23.93272000, '2019-10-05 23:14:58', '2020-05-01 17:23:07', 1, 'Q12118816'),
(92206, 'Comuna Lunca de Jos', 4749, 'HR', 181, 'RO', 46.59868000, 25.96582000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5058178'),
(92207, 'Comuna Lunca de Sus', 4749, 'HR', 181, 'RO', 46.51853000, 25.95575000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5059030'),
(92208, 'Comuna Luncaviţa', 4727, 'TL', 181, 'RO', 45.28653000, 28.30029000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q10787203'),
(92209, 'Comuna Luncaviţa', 4753, 'CS', 181, 'RO', 45.08520000, 22.25352000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q12724912'),
(92210, 'Comuna Luncoiu De Jos', 4721, 'HD', 181, 'RO', 46.06807000, 22.79112000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q678325'),
(92211, 'Comuna Lungani', 4735, 'IS', 181, 'RO', 47.17564000, 27.15749000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5069547'),
(92212, 'Comuna Lungeşti', 4757, 'VL', 181, 'RO', 44.60271000, 24.17894000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q770195'),
(92213, 'Comuna Lunguleţu', 4745, 'DB', 181, 'RO', 44.61601000, 25.64340000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q2314919'),
(92214, 'Comuna Lupac', 4753, 'CS', 181, 'RO', 45.25215000, 21.80835000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724913'),
(92215, 'Comuna Lupeni', 4749, 'HR', 181, 'RO', 46.39565000, 25.21905000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5057956'),
(92216, 'Comuna Lupşa', 4724, 'AB', 181, 'RO', 46.34891000, 23.20455000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q12696316'),
(92217, 'Comuna Lupşanu', 4732, 'CL', 181, 'RO', 44.38075000, 26.94842000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q2717055'),
(92218, 'Comuna Lãpugiu De Jos', 4721, 'HD', 181, 'RO', 45.89222000, 22.45747000, '2019-10-05 23:14:58', '2020-05-01 17:23:10', 1, 'Q678246'),
(92219, 'Comuna Lăcusteni', 4757, 'VL', 181, 'RO', 44.70863000, 23.89214000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q12724915'),
(92220, 'Comuna Lădeşti', 4757, 'VL', 181, 'RO', 44.87014000, 24.04350000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q2068040'),
(92221, 'Comuna Lăpuş', 4760, 'MM', 181, 'RO', 47.49363000, 24.00646000, '2019-10-05 23:14:58', '2020-05-01 17:23:10', 1, 'Q5067803'),
(92222, 'Comuna Lăpuşata', 4757, 'VL', 181, 'RO', 44.92591000, 24.00976000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q2482160'),
(92223, 'Comuna Lăpuşnicel', 4753, 'CS', 181, 'RO', 44.98679000, 22.20710000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q12724916'),
(92224, 'Comuna Lăpuşnicu Mare', 4753, 'CS', 181, 'RO', 44.88519000, 21.93321000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q16426025'),
(92225, 'Comuna Lăzarea', 4749, 'HR', 181, 'RO', 46.76626000, 25.53875000, '2019-10-05 23:14:58', '2020-05-01 17:23:10', 1, 'Q5058917'),
(92226, 'Comuna Lăzăreni', 4723, 'BH', 181, 'RO', 46.85956000, 22.06685000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q16425666'),
(92227, 'Comuna Macea', 4739, 'AR', 181, 'RO', 46.40094000, 21.33187000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12122019'),
(92228, 'Comuna Maglavit', 4742, 'DJ', 181, 'RO', 44.04339000, 23.09854000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426474'),
(92229, 'Comuna Mahmudia', 4727, 'TL', 181, 'RO', 45.08582000, 29.08775000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5066090'),
(92230, 'Comuna Maia', 4743, 'IL', 181, 'RO', 44.73616000, 26.40151000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426794'),
(92231, 'Comuna Malaia', 4757, 'VL', 181, 'RO', 45.36429000, 24.04456000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2387281'),
(92232, 'Comuna Maliuc', 4727, 'TL', 181, 'RO', 45.18330000, 29.04828000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5113257'),
(92233, 'Comuna Malnaş', 4754, 'CV', 181, 'RO', 46.01995000, 25.82705000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q24710'),
(92234, 'Comuna Malovãţ', 4751, 'MH', 181, 'RO', 44.72156000, 22.74546000, '2019-10-05 23:14:58', '2020-05-01 17:23:10', 1, 'Q12724922'),
(92235, 'Comuna Malu', 4726, 'GR', 181, 'RO', 43.81518000, 25.81863000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5092240'),
(92236, 'Comuna Malu Mare', 4742, 'DJ', 181, 'RO', 44.23844000, 23.85570000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724918'),
(92237, 'Comuna Malu cu Flori', 4745, 'DB', 181, 'RO', 45.15617000, 25.22589000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5070075'),
(92238, 'Comuna Manasia', 4743, 'IL', 181, 'RO', 44.70648000, 26.67346000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426796'),
(92239, 'Comuna Manoleasa', 4740, 'BT', 181, 'RO', 48.00852000, 27.07951000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724921'),
(92240, 'Comuna Marca', 4741, 'SJ', 181, 'RO', 47.23495000, 22.55089000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16427361'),
(92241, 'Comuna Marga', 4753, 'CS', 181, 'RO', 45.51489000, 22.51537000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724924'),
(92242, 'Comuna Margina', 4748, 'TM', 181, 'RO', 45.86159000, 22.27233000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5053747'),
(92243, 'Comuna Marginea', 4720, 'SV', 181, 'RO', 47.80927000, 25.82296000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724925'),
(92244, 'Comuna Marpod', 4755, 'SB', 181, 'RO', 45.87101000, 24.51544000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5064517'),
(92245, 'Comuna Matca', 4747, 'GL', 181, 'RO', 45.85686000, 27.53660000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5087623'),
(92246, 'Comuna Mateeşti', 4757, 'VL', 181, 'RO', 45.06784000, 23.85490000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q12724926'),
(92247, 'Comuna Matei', 4733, 'BN', 181, 'RO', 46.99930000, 24.26029000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5065428'),
(92248, 'Comuna Mavrodin', 4728, 'TR', 181, 'RO', 44.03559000, 25.24088000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724927'),
(92249, 'Comuna Maşloc', 4748, 'TM', 181, 'RO', 45.99637000, 21.45484000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q16427738'),
(92250, 'Comuna Medieşu Aurit', 4746, 'SM', 181, 'RO', 47.79451000, 23.15360000, '2019-10-05 23:14:58', '2020-05-01 17:23:11', 1, 'Q5055677'),
(92251, 'Comuna Mehadia', 4753, 'CS', 181, 'RO', 44.95509000, 22.37181000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q16426033'),
(92252, 'Comuna Mehadica', 4753, 'CS', 181, 'RO', 45.03894000, 22.26476000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q12724928'),
(92253, 'Comuna Melineşti', 4742, 'DJ', 181, 'RO', 44.56310000, 23.69530000, '2019-10-05 23:14:58', '2020-05-01 17:23:09', 1, 'Q16426479'),
(92254, 'Comuna Mera', 4758, 'VN', 181, 'RO', 45.77327000, 26.93423000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q1038516'),
(92255, 'Comuna Merei', 4756, 'BZ', 181, 'RO', 45.13102000, 26.66127000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q996895'),
(92256, 'Comuna Mereni', 4754, 'CV', 181, 'RO', 46.08496000, 26.23460000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5058879'),
(92257, 'Comuna Mereni', 4728, 'TR', 181, 'RO', 44.22879000, 25.64434000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q2721375'),
(92258, 'Comuna Mereni', 4737, 'CT', 181, 'RO', 44.01545000, 28.33559000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5053191'),
(92259, 'Comuna Mereşti', 4749, 'HR', 181, 'RO', 46.23458000, 25.46099000, '2019-10-05 23:14:58', '2020-05-01 17:23:10', 1, 'Q5057649'),
(92260, 'Comuna Merghindeal', 4755, 'SB', 181, 'RO', 45.97533000, 24.71324000, '2019-10-05 23:14:58', '2019-10-05 23:14:58', 1, 'Q5059234'),
(92261, 'Comuna Merișani', 4722, 'AG', 181, 'RO', 44.96674000, 24.74482000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q16425499'),
(92262, 'Comuna Meteş', 4724, 'AB', 181, 'RO', 46.10543000, 23.41707000, '2019-10-05 23:14:58', '2020-05-01 17:23:08', 1, 'Q16425301'),
(92263, 'Comuna Mica', 4734, 'CJ', 181, 'RO', 47.14436000, 23.97593000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16426220'),
(92264, 'Comuna Miceşti', 4722, 'AG', 181, 'RO', 44.97367000, 24.85577000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q12724930'),
(92265, 'Comuna Miceştii de Câmpie', 4733, 'BN', 181, 'RO', 46.84309000, 24.31568000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q5070431'),
(92266, 'Comuna Micfalău', 4754, 'CV', 181, 'RO', 46.05342000, 25.83737000, '2019-10-05 23:14:59', '2020-05-01 17:23:09', 1, 'Q16426323'),
(92267, 'Comuna Micleşti', 4752, 'VS', 181, 'RO', 46.82203000, 27.84215000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q12724931'),
(92268, 'Comuna Micula', 4746, 'SM', 181, 'RO', 47.91687000, 22.93652000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5055476'),
(92269, 'Comuna Micăsasa', 4755, 'SB', 181, 'RO', 46.10607000, 24.13264000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q5064883'),
(92270, 'Comuna Mihai Bravu', 4726, 'GR', 181, 'RO', 44.14129000, 26.05812000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5118188'),
(92271, 'Comuna Mihai Bravu', 4727, 'TL', 181, 'RO', 44.95974000, 28.65504000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5066879'),
(92272, 'Comuna Mihai Eminescu', 4740, 'BT', 181, 'RO', 47.76632000, 26.56267000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2534156'),
(92273, 'Comuna Mihai Viteazu', 4734, 'CJ', 181, 'RO', 46.53477000, 23.71595000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q12724934'),
(92274, 'Comuna Mihai Viteazu', 4737, 'CT', 181, 'RO', 44.63727000, 28.70460000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5063836'),
(92275, 'Comuna Mihail Kogălniceanu', 4727, 'TL', 181, 'RO', 45.02225000, 28.72861000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q5056157'),
(92276, 'Comuna Mihail Kogălniceanu', 4743, 'IL', 181, 'RO', 44.66216000, 27.73430000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q3786773'),
(92277, 'Comuna Mihail Kogălniceanu', 4737, 'CT', 181, 'RO', 44.40479000, 28.51919000, '2019-10-05 23:14:59', '2020-05-01 17:23:09', 1, 'Q5062278'),
(92278, 'Comuna Mihalţ', 4724, 'AB', 181, 'RO', 46.17641000, 23.73841000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q16425304'),
(92279, 'Comuna Mihăeşti', 4738, 'OT', 181, 'RO', 44.11870000, 24.79663000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q12724935'),
(92280, 'Comuna Mihăeşti', 4757, 'VL', 181, 'RO', 45.03282000, 24.24501000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q12724936'),
(92281, 'Comuna Mihăeşti', 4722, 'AG', 181, 'RO', 45.11563000, 25.01938000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q3407585'),
(92282, 'Comuna Mihăileni', 4749, 'HR', 181, 'RO', 46.48874000, 25.83027000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q5058709'),
(92283, 'Comuna Mihăileni', 4740, 'BT', 181, 'RO', 47.95552000, 26.16204000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q12724937'),
(92284, 'Comuna Mihăileni', 4755, 'SB', 181, 'RO', 45.99998000, 24.36621000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q5065722'),
(92285, 'Comuna Mihăileşti', 4756, 'BZ', 181, 'RO', 44.92518000, 26.67330000, '2019-10-05 23:14:59', '2020-05-01 17:23:09', 1, 'Q2537527'),
(92286, 'Comuna Mihălăşeni', 4740, 'BT', 181, 'RO', 47.87956000, 27.09620000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q12724938'),
(92287, 'Comuna Milas', 4733, 'BN', 181, 'RO', 46.82249000, 24.43371000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5064564'),
(92288, 'Comuna Milcoiu', 4757, 'VL', 181, 'RO', 45.04046000, 24.46786000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5117716'),
(92289, 'Comuna Milcov', 4738, 'OT', 181, 'RO', 44.37396000, 24.38004000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2292754'),
(92290, 'Comuna Milcovul', 4758, 'VN', 181, 'RO', 45.65207000, 27.25928000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q1047633'),
(92291, 'Comuna Mileanca', 4740, 'BT', 181, 'RO', 48.09121000, 26.72965000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q13571224'),
(92292, 'Comuna Miloşeşti', 4743, 'IL', 181, 'RO', 44.75820000, 27.20338000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q16426801'),
(92293, 'Comuna Mintiu Gherlii', 4734, 'CJ', 181, 'RO', 47.07323000, 23.92731000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16426224'),
(92294, 'Comuna Mioarele', 4722, 'AG', 181, 'RO', 45.23396000, 25.10045000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2348270'),
(92295, 'Comuna Mircea Vodă', 4736, 'BR', 181, 'RO', 45.12889000, 27.38258000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q2535675'),
(92296, 'Comuna Mircea Vodă', 4737, 'CT', 181, 'RO', 44.30137000, 28.18647000, '2019-10-05 23:14:59', '2020-05-01 17:23:09', 1, 'Q5065996'),
(92297, 'Comuna Mirceşti', 4735, 'IS', 181, 'RO', 47.05486000, 26.83945000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q5052832'),
(92298, 'Comuna Mireşu Mare', 4760, 'MM', 181, 'RO', 47.50011000, 23.36420000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q16426881'),
(92299, 'Comuna Mironeasa', 4735, 'IS', 181, 'RO', 46.99869000, 27.42519000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5113783'),
(92300, 'Comuna Miroslava', 4735, 'IS', 181, 'RO', 47.13047000, 27.51039000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5066297'),
(92301, 'Comuna Mirosloveşti', 4735, 'IS', 181, 'RO', 47.13966000, 26.64648000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q5114016'),
(92302, 'Comuna Miroşi', 4722, 'AG', 181, 'RO', 44.40316000, 24.95170000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q1896939'),
(92303, 'Comuna Mirăslău', 4724, 'AB', 181, 'RO', 46.38660000, 23.69329000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q16425308'),
(92304, 'Comuna Mirşid', 4741, 'SJ', 181, 'RO', 47.23132000, 23.14308000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q12697875'),
(92305, 'Comuna Mischii', 4742, 'DJ', 181, 'RO', 44.42093000, 23.86238000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16426482'),
(92306, 'Comuna Mitoc', 4740, 'BT', 181, 'RO', 48.11288000, 27.00423000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16425854'),
(92307, 'Comuna Mitocu Dragomirnei', 4720, 'SV', 181, 'RO', 47.73475000, 26.23992000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16427532'),
(92308, 'Comuna Mitreni', 4732, 'CL', 181, 'RO', 44.16339000, 26.61454000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2419303'),
(92309, 'Comuna Mitrofani', 4757, 'VL', 181, 'RO', 44.75104000, 24.20272000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q12724942'),
(92310, 'Comuna Mişca', 4739, 'AR', 181, 'RO', 46.62016000, 21.65353000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q5063311'),
(92311, 'Comuna Moacşa', 4754, 'CV', 181, 'RO', 45.87883000, 25.95270000, '2019-10-05 23:14:59', '2020-05-01 17:23:09', 1, 'Q1097245'),
(92312, 'Comuna Moara', 4720, 'SV', 181, 'RO', 47.58038000, 26.19565000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2720042'),
(92313, 'Comuna Moara Vlãsiei', 4725, 'IF', 181, 'RO', 44.63416000, 26.18935000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q1655134'),
(92314, 'Comuna Mociu', 4734, 'CJ', 181, 'RO', 46.79232000, 24.02853000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q12724941'),
(92315, 'Comuna Modelu', 4732, 'CL', 181, 'RO', 44.19661000, 27.38960000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2052617'),
(92316, 'Comuna Moftinu', 4746, 'SM', 181, 'RO', 47.68333000, 22.60000000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2604612'),
(92317, 'Comuna Mogoş', 4724, 'AB', 181, 'RO', 46.28356000, 23.31873000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q16425313'),
(92318, 'Comuna Mogoşani', 4745, 'DB', 181, 'RO', 44.68319000, 25.38774000, '2019-10-05 23:14:59', '2020-05-01 17:23:09', 1, 'Q2384649'),
(92319, 'Comuna Mogoşeşti', 4735, 'IS', 181, 'RO', 47.04200000, 27.48492000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q5112488'),
(92320, 'Comuna Mogoşeşti-Siret', 4735, 'IS', 181, 'RO', 47.13190000, 26.77101000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q5066535'),
(92321, 'Comuna Mogoşoaia', 4725, 'IF', 181, 'RO', 44.52903000, 26.00178000, '2019-10-05 23:14:59', '2020-05-01 17:23:10', 1, 'Q1655392'),
(92322, 'Comuna Moieciu', 4759, 'BV', 181, 'RO', 45.48848000, 25.30962000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q1617846'),
(92323, 'Comuna Moisei', 4760, 'MM', 181, 'RO', 47.65612000, 24.53988000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q303099'),
(92324, 'Comuna Moldova Suliţa', 4720, 'SV', 181, 'RO', 47.67897000, 25.25421000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q12724944'),
(92325, 'Comuna Moldoveneşti', 4734, 'CJ', 181, 'RO', 46.47350000, 23.68905000, '2019-10-05 23:14:59', '2020-05-01 17:23:09', 1, 'Q12124200'),
(92326, 'Comuna Moldoveni', 4743, 'IL', 181, 'RO', 44.71425000, 26.51879000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16426807'),
(92327, 'Comuna Moldoveni', 4731, 'NT', 181, 'RO', 46.82131000, 26.78791000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5112821'),
(92328, 'Comuna Moldoviţa', 4720, 'SV', 181, 'RO', 47.72325000, 25.50689000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q12724945'),
(92329, 'Comuna Moneasa', 4739, 'AR', 181, 'RO', 46.44443000, 22.25323000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q12124463'),
(92330, 'Comuna Monor', 4733, 'BN', 181, 'RO', 46.95475000, 24.70106000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q5065139'),
(92331, 'Comuna Moraviţa', 4748, 'TM', 181, 'RO', 45.28025000, 21.25570000, '2019-10-05 23:14:59', '2020-05-01 17:23:11', 1, 'Q12724951'),
(92332, 'Comuna Moroeni', 4745, 'DB', 181, 'RO', 45.23406000, 25.44468000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2606852'),
(92333, 'Comuna Morteni', 4745, 'DB', 181, 'RO', 44.66221000, 25.25208000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q11141348'),
(92334, 'Comuna Morunglav', 4738, 'OT', 181, 'RO', 44.47266000, 24.11686000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16427130'),
(92335, 'Comuna Morărești', 4722, 'AG', 181, 'RO', 45.00760000, 24.56079000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q1896993'),
(92336, 'Comuna Motoşeni', 4744, 'BC', 181, 'RO', 46.32779000, 27.41721000, '2019-10-05 23:14:59', '2020-05-01 17:23:08', 1, 'Q2542004'),
(92337, 'Comuna Movila', 4743, 'IL', 181, 'RO', 44.53747000, 27.71771000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q16426810'),
(92338, 'Comuna Movila Banului', 4756, 'BZ', 181, 'RO', 44.97607000, 26.70299000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2537574'),
(92339, 'Comuna Movila Miresii', 4736, 'BR', 181, 'RO', 45.19574000, 27.60368000, '2019-10-05 23:14:59', '2019-10-05 23:14:59', 1, 'Q2716317'),
(92340, 'Comuna Movileni', 4747, 'GL', 181, 'RO', 45.76584000, 27.37587000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q5091667'),
(92341, 'Comuna Movileni', 4735, 'IS', 181, 'RO', 47.32032000, 27.38016000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q12123984'),
(92342, 'Comuna Movileni', 4738, 'OT', 181, 'RO', 44.38368000, 24.64075000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q12724952'),
(92343, 'Comuna Moviliţa', 4743, 'IL', 181, 'RO', 44.61784000, 26.48006000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q3791443'),
(92344, 'Comuna Moviliţa', 4758, 'VN', 181, 'RO', 45.95750000, 27.10487000, '2019-10-05 23:15:00', '2020-05-01 17:23:12', 1, 'Q3915139'),
(92345, 'Comuna Mozăceni', 4722, 'AG', 181, 'RO', 44.56366000, 25.17492000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q12724953'),
(92346, 'Comuna Moşna', 4755, 'SB', 181, 'RO', 46.07534000, 24.42509000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q5057746'),
(92347, 'Comuna Moşna', 4735, 'IS', 181, 'RO', 46.92189000, 27.95956000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q16426819'),
(92348, 'Comuna Moşniţa Nouã', 4748, 'TM', 181, 'RO', 45.71397000, 21.32403000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724955'),
(92349, 'Comuna Moşoaia', 4722, 'AG', 181, 'RO', 44.83013000, 24.79957000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q1884103'),
(92350, 'Comuna Moşteni', 4728, 'TR', 181, 'RO', 44.19787000, 25.50854000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q16427637'),
(92351, 'Comuna Moţca', 4735, 'IS', 181, 'RO', 47.22407000, 26.61632000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q5066579'),
(92352, 'Comuna Moţãieni', 4745, 'DB', 181, 'RO', 45.10631000, 25.41045000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q371693'),
(92353, 'Comuna Moţăţei', 4742, 'DJ', 181, 'RO', 44.08454000, 23.18856000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q16426485'),
(92354, 'Comuna Muereasca', 4757, 'VL', 181, 'RO', 45.20364000, 24.29726000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q12724954'),
(92355, 'Comuna Mugeni', 4749, 'HR', 181, 'RO', 46.26544000, 25.19498000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q16426700'),
(92356, 'Comuna Munteni', 4747, 'GL', 181, 'RO', 45.91503000, 27.44242000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q12724958'),
(92357, 'Comuna Munteni Buzău', 4743, 'IL', 181, 'RO', 44.63838000, 26.97288000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q2718361'),
(92358, 'Comuna Muntenii de Jos', 4752, 'VS', 181, 'RO', 46.60405000, 27.76949000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q12724957'),
(92359, 'Comuna Muntenii de Sus', 4752, 'VS', 181, 'RO', 46.69288000, 27.76320000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q12724959'),
(92360, 'Comuna Murgaşi', 4742, 'DJ', 181, 'RO', 44.54342000, 23.82994000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q16426489'),
(92361, 'Comuna Murgeşti', 4756, 'BZ', 181, 'RO', 45.40612000, 26.88883000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q3867404'),
(92362, 'Comuna Murighiol', 4727, 'TL', 181, 'RO', 45.02504000, 29.15177000, '2019-10-05 23:15:00', '2019-10-05 23:15:00', 1, 'Q5113748'),
(92363, 'Comuna Muşeniţa', 4720, 'SV', 181, 'RO', 47.96020000, 25.98110000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q16427536'),
(92364, 'Comuna Muşeteşti', 4750, 'GJ', 181, 'RO', 45.15231000, 23.45022000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q12724960'),
(92365, 'Comuna Mușătești', 4722, 'AG', 181, 'RO', 45.21667000, 24.78333000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q1896335'),
(92366, 'Comuna Mândra', 4759, 'BV', 181, 'RO', 45.81219000, 25.03262000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q1067729'),
(92367, 'Comuna Mânzăleşti', 4756, 'BZ', 181, 'RO', 45.50414000, 26.64561000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q2716877'),
(92368, 'Comuna Mânăstirea', 4732, 'CL', 181, 'RO', 44.23953000, 26.87940000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q2537556'),
(92369, 'Comuna Mânăstirea Caşin', 4744, 'BC', 181, 'RO', 46.19394000, 26.72923000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q1086336'),
(92370, 'Comuna Mânăstirea Humorului', 4720, 'SV', 181, 'RO', 47.62839000, 25.82206000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724978'),
(92371, 'Comuna Mânăstireni', 4734, 'CJ', 181, 'RO', 46.79131000, 23.09871000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q12724979'),
(92372, 'Comuna Mârzãneşti', 4728, 'TR', 181, 'RO', 43.93154000, 25.46230000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724966'),
(92373, 'Comuna Mârşa', 4726, 'GR', 181, 'RO', 44.37355000, 25.55974000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q5118206'),
(92374, 'Comuna Mârşani', 4742, 'DJ', 181, 'RO', 44.01157000, 24.01919000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q12724962'),
(92375, 'Comuna Mãguri-Rãcãtãu', 4734, 'CJ', 181, 'RO', 46.63948000, 23.19485000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q12724972'),
(92376, 'Comuna Mãieruş', 4759, 'BV', 181, 'RO', 45.89358000, 25.49705000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q1190819'),
(92378, 'Comuna Măceşu de Jos', 4742, 'DJ', 181, 'RO', 43.87966000, 23.68954000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q12724967'),
(92379, 'Comuna Măceşu de Sus', 4742, 'DJ', 181, 'RO', 43.91667000, 23.71126000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q12724968'),
(92380, 'Comuna Măciuca', 4757, 'VL', 181, 'RO', 44.75807000, 24.02408000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q2721521'),
(92381, 'Comuna Mădulari-Beica', 4757, 'VL', 181, 'RO', 44.67820000, 24.10121000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724965'),
(92382, 'Comuna Mădârjac', 4735, 'IS', 181, 'RO', 47.04094000, 27.27716000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q12122217'),
(92383, 'Comuna Mădăraş', 4749, 'HR', 181, 'RO', 46.49472000, 25.74750000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q12724969'),
(92384, 'Comuna Mădăraş', 4723, 'BH', 181, 'RO', 46.84296000, 21.72080000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q16467086'),
(92385, 'Comuna Măerişte', 4741, 'SJ', 181, 'RO', 47.29893000, 22.75444000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q838838'),
(92386, 'Comuna Măgeşti', 4723, 'BH', 181, 'RO', 47.00929000, 22.45330000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q12122207'),
(92387, 'Comuna Măgireşti', 4744, 'BC', 181, 'RO', 46.50965000, 26.53727000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q2534371'),
(92388, 'Comuna Măgura', 4744, 'BC', 181, 'RO', 46.56065000, 26.83900000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q2620899'),
(92389, 'Comuna Măgura', 4756, 'BZ', 181, 'RO', 45.26962000, 26.55104000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q2288033'),
(92390, 'Comuna Măgura', 4728, 'TR', 181, 'RO', 44.04054000, 25.39590000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724971'),
(92391, 'Comuna Măgura Ilvei', 4733, 'BN', 181, 'RO', 47.36625000, 24.82185000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q5070414'),
(92392, 'Comuna Măgurele', 4729, 'PH', 181, 'RO', 45.09476000, 26.04955000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q520593'),
(92393, 'Comuna Măgureni', 4729, 'PH', 181, 'RO', 45.05203000, 25.76233000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q2605163'),
(92394, 'Comuna Măicăneşti', 4758, 'VN', 181, 'RO', 45.50067000, 27.46121000, '2019-10-05 23:15:00', '2020-05-01 17:23:12', 1, 'Q12122338'),
(92395, 'Comuna Măieru', 4733, 'BN', 181, 'RO', 47.40738000, 24.75680000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q5062684'),
(92396, 'Comuna Măldăeni', 4728, 'TR', 181, 'RO', 44.12658000, 24.92327000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q16402795'),
(92397, 'Comuna Măldăreşti', 4757, 'VL', 181, 'RO', 45.10258000, 24.00613000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724974'),
(92398, 'Comuna Mălini', 4720, 'SV', 181, 'RO', 47.40321000, 26.01485000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724973'),
(92399, 'Comuna Mălureni', 4722, 'AG', 181, 'RO', 45.04780000, 24.77875000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q5067074'),
(92400, 'Comuna Măluşteni', 4752, 'VS', 181, 'RO', 46.17587000, 27.91546000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724976'),
(92401, 'Comuna Măneciu', 4729, 'PH', 181, 'RO', 45.33711000, 25.98547000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q946927'),
(92402, 'Comuna Măneşti', 4729, 'PH', 181, 'RO', 44.86043000, 25.83461000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q285781'),
(92403, 'Comuna Măneşti', 4745, 'DB', 181, 'RO', 44.95033000, 25.30034000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q2868630'),
(92404, 'Comuna Mănăştiur', 4748, 'TM', 181, 'RO', 45.86808000, 22.05329000, '2019-10-05 23:15:00', '2020-05-01 17:23:11', 1, 'Q12724983'),
(92405, 'Comuna Măraşu', 4736, 'BR', 181, 'RO', 45.00807000, 27.98062000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q2716399'),
(92406, 'Comuna Mărculeşti', 4743, 'IL', 181, 'RO', 44.56698000, 27.51562000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q5092408'),
(92407, 'Comuna Mărgineni', 4731, 'NT', 181, 'RO', 46.89237000, 26.65742000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q3878836'),
(92408, 'Comuna Mărgineni', 4744, 'BC', 181, 'RO', 46.59779000, 26.80505000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q1087275'),
(92409, 'Comuna Mărgăriteşti', 4756, 'BZ', 181, 'RO', 45.43496000, 26.83619000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q2716435'),
(92410, 'Comuna Mărgău', 4734, 'CJ', 181, 'RO', 46.75677000, 22.94429000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q16426214'),
(92411, 'Comuna Mărişel', 4734, 'CJ', 181, 'RO', 46.68422000, 23.11466000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q16426217'),
(92412, 'Comuna Mărişelu', 4733, 'BN', 181, 'RO', 47.01066000, 24.51260000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q5065752'),
(92413, 'Comuna Mărtineşti', 4721, 'HD', 181, 'RO', 45.79278000, 23.11998000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q1089935'),
(92414, 'Comuna Mărtiniş', 4749, 'HR', 181, 'RO', 46.22996000, 25.38756000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q5057090'),
(92415, 'Comuna Mărunţei', 4738, 'OT', 181, 'RO', 44.23679000, 24.46583000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q3879577'),
(92416, 'Comuna Mărăcineni', 4722, 'AG', 181, 'RO', 44.90857000, 24.87484000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q12724981'),
(92417, 'Comuna Mărăcineni', 4756, 'BZ', 181, 'RO', 45.18902000, 26.79393000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q2289257'),
(92418, 'Comuna Măstăcani', 4747, 'GL', 181, 'RO', 45.76700000, 28.04175000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q12724982'),
(92419, 'Comuna Mătăsari', 4750, 'GJ', 181, 'RO', 44.85764000, 23.06819000, '2019-10-05 23:15:00', '2020-05-01 17:23:10', 1, 'Q12724984'),
(92420, 'Comuna Mătăsaru', 4745, 'DB', 181, 'RO', 44.67584000, 25.44365000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q2607304'),
(92421, 'Comuna Măureni', 4753, 'CS', 181, 'RO', 45.43170000, 21.51348000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q16426030'),
(92422, 'Comuna Măxineni', 4736, 'BR', 181, 'RO', 45.41803000, 27.66470000, '2019-10-05 23:15:00', '2020-05-01 17:23:08', 1, 'Q2716382'),
(92423, 'Comuna Naidaş', 4753, 'CS', 181, 'RO', 44.87584000, 21.56807000, '2019-10-05 23:15:00', '2020-05-01 17:23:09', 1, 'Q12724985'),
(92424, 'Comuna Nalbant', 4727, 'TL', 181, 'RO', 45.04119000, 28.59827000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5065583'),
(92425, 'Comuna Nana', 4732, 'CL', 181, 'RO', 44.26771000, 26.59199000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q2537581'),
(92426, 'Comuna Nanov', 4728, 'TR', 181, 'RO', 43.99171000, 25.28894000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5087650'),
(92427, 'Comuna Necşeşti', 4728, 'TR', 181, 'RO', 44.26085000, 25.13225000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q16427641'),
(92428, 'Comuna Negoi', 4742, 'DJ', 181, 'RO', 43.91396000, 23.37288000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16426493'),
(92429, 'Comuna Negomir', 4750, 'GJ', 181, 'RO', 44.78593000, 23.22744000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16426641'),
(92430, 'Comuna Negraşi', 4722, 'AG', 181, 'RO', 44.61441000, 25.12404000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q1896314'),
(92431, 'Comuna Negreni', 4734, 'CJ', 181, 'RO', 46.95224000, 22.75835000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5059779'),
(92432, 'Comuna Negreşti', 4731, 'NT', 181, 'RO', 47.04779000, 26.35005000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q3878978'),
(92433, 'Comuna Negri', 4744, 'BC', 181, 'RO', 46.70500000, 26.97216000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q2625976'),
(92434, 'Comuna Negrileşti', 4747, 'GL', 181, 'RO', 45.94768000, 27.48018000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q12724989'),
(92435, 'Comuna Negrileşti', 4758, 'VN', 181, 'RO', 45.93697000, 26.70635000, '2019-10-05 23:15:01', '2020-05-01 17:23:12', 1, 'Q2568684'),
(92436, 'Comuna Negrileşti', 4733, 'BN', 181, 'RO', 47.31054000, 24.05557000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q5070451'),
(92437, 'Comuna Nenciuleşti', 4728, 'TR', 181, 'RO', 44.03860000, 25.18551000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12724990'),
(92438, 'Comuna Nereju', 4758, 'VN', 181, 'RO', 45.70833000, 26.69794000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q1063356'),
(92439, 'Comuna Nicolae Bãlcescu', 4757, 'VL', 181, 'RO', 44.98760000, 24.41990000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q940856'),
(92440, 'Comuna Nicolae Bălcescu', 4737, 'CT', 181, 'RO', 44.39829000, 28.35323000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q5053117'),
(92441, 'Comuna Nicolae Bălcescu', 4732, 'CL', 181, 'RO', 44.44506000, 26.75839000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q2537547'),
(92442, 'Comuna Nicolae Bălcescu', 4744, 'BC', 181, 'RO', 46.46687000, 26.88837000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q2621199'),
(92443, 'Comuna Nicolae Titulescu', 4738, 'OT', 181, 'RO', 44.27008000, 24.75897000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16439912'),
(92444, 'Comuna Nicoreşti', 4747, 'GL', 181, 'RO', 45.93603000, 27.29735000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q3726548'),
(92445, 'Comuna Niculeşti', 4745, 'DB', 181, 'RO', 44.68664000, 25.96350000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q2467603'),
(92446, 'Comuna Niculiţel', 4727, 'TL', 181, 'RO', 45.18251000, 28.48139000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12724993'),
(92447, 'Comuna Nicşeni', 4740, 'BT', 181, 'RO', 47.86170000, 26.66023000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q16425857'),
(92448, 'Comuna Nimigea', 4733, 'BN', 181, 'RO', 47.24697000, 24.30623000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q1188906'),
(92449, 'Comuna Nistoreşti', 4758, 'VN', 181, 'RO', 45.81578000, 26.67728000, '2019-10-05 23:15:01', '2020-05-01 17:23:12', 1, 'Q1656319'),
(92450, 'Comuna Niţchidorf', 4748, 'TM', 181, 'RO', 45.57379000, 21.52266000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12724995'),
(92451, 'Comuna Nocrich', 4755, 'SB', 181, 'RO', 45.87075000, 24.43314000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5058399'),
(92452, 'Comuna Nojorid', 4723, 'BH', 181, 'RO', 46.97144000, 21.87747000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16425676'),
(92453, 'Comuna Noşlac', 4724, 'AB', 181, 'RO', 46.41300000, 23.97668000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q16425317'),
(92454, 'Comuna Nucet', 4745, 'DB', 181, 'RO', 44.80328000, 25.55434000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q10292752'),
(92455, 'Comuna Nuci', 4725, 'IF', 181, 'RO', 44.70910000, 26.31770000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q2740633'),
(92456, 'Comuna Nucşoara', 4722, 'AG', 181, 'RO', 45.33798000, 24.78943000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q1465932'),
(92457, 'Comuna Nufăru', 4727, 'TL', 181, 'RO', 45.14333000, 28.92452000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12134510'),
(92458, 'Comuna Nuşeni', 4733, 'BN', 181, 'RO', 47.09063000, 24.17525000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q5065240'),
(92459, 'Comuna Nuşfalău', 4741, 'SJ', 181, 'RO', 47.21355000, 22.70784000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q5056132'),
(92460, 'Comuna Nădrag', 4748, 'TM', 181, 'RO', 45.65064000, 22.15383000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q5054461'),
(92461, 'Comuna Năeni', 4756, 'BZ', 181, 'RO', 45.09968000, 26.48944000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q2538614'),
(92462, 'Comuna Nămoloasa', 4747, 'GL', 181, 'RO', 45.52383000, 27.56307000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q13571305'),
(92463, 'Comuna Năneşti', 4758, 'VN', 181, 'RO', 45.57801000, 27.49988000, '2019-10-05 23:15:01', '2020-05-01 17:23:12', 1, 'Q2604538'),
(92464, 'Comuna Năpradea', 4741, 'SJ', 181, 'RO', 47.34497000, 23.31603000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q16427367'),
(92465, 'Comuna Năruja', 4758, 'VN', 181, 'RO', 45.82308000, 26.76617000, '2019-10-05 23:15:01', '2020-05-01 17:23:12', 1, 'Q1038526'),
(92466, 'Comuna Năsturelu', 4728, 'TR', 181, 'RO', 43.66634000, 25.43856000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12724994'),
(92467, 'Comuna Oancea', 4747, 'GL', 181, 'RO', 45.90648000, 28.11118000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q13571374'),
(92468, 'Comuna Oarja', 4722, 'AG', 181, 'RO', 44.75730000, 24.96935000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16425518'),
(92469, 'Comuna Oarţa De Jos', 4760, 'MM', 181, 'RO', 47.45000000, 23.10000000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q16426885'),
(92470, 'Comuna Oboga', 4738, 'OT', 181, 'RO', 44.42448000, 24.08724000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5116722'),
(92471, 'Comuna Obreja', 4753, 'CS', 181, 'RO', 45.46873000, 22.25570000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5064643'),
(92472, 'Comuna Obrejiţa', 4758, 'VN', 181, 'RO', 45.50102000, 27.08974000, '2019-10-05 23:15:01', '2020-05-01 17:23:12', 1, 'Q3915587'),
(92473, 'Comuna Obârşia', 4738, 'OT', 181, 'RO', 43.89063000, 24.32188000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q12724997'),
(92474, 'Comuna Obârşia De Câmp', 4751, 'MH', 181, 'RO', 44.17507000, 22.97076000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q2605895'),
(92475, 'Comuna Obârşia-Cloşani', 4751, 'MH', 181, 'RO', 45.01667000, 22.68333000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q2605928'),
(92476, 'Comuna Ocland', 4749, 'HR', 181, 'RO', 46.16242000, 25.42557000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5058018'),
(92477, 'Comuna Ocna De Fier', 4753, 'CS', 181, 'RO', 45.34195000, 21.77503000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725000'),
(92478, 'Comuna Ocna Şugatag', 4760, 'MM', 181, 'RO', 47.77493000, 23.91268000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q12135430'),
(92479, 'Comuna Ocniţa', 4745, 'DB', 181, 'RO', 44.99011000, 25.55591000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q16426376'),
(92480, 'Comuna Ocoliş', 4724, 'AB', 181, 'RO', 46.49636000, 23.46581000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q16425320'),
(92481, 'Comuna Odobeşti', 4745, 'DB', 181, 'RO', 44.59883000, 25.54082000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q2509956'),
(92482, 'Comuna Odobeşti', 4744, 'BC', 181, 'RO', 46.66902000, 27.13224000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q2542420'),
(92483, 'Comuna Odoreu', 4746, 'SM', 181, 'RO', 47.79427000, 22.99639000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12135287'),
(92484, 'Comuna Odăile', 4756, 'BZ', 181, 'RO', 45.38458000, 26.53966000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q2537564'),
(92485, 'Comuna Ograda', 4743, 'IL', 181, 'RO', 44.61457000, 27.57047000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16426814'),
(92486, 'Comuna Ogrezeni', 4726, 'GR', 181, 'RO', 44.39727000, 25.77888000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5118232'),
(92487, 'Comuna Ohaba', 4724, 'AB', 181, 'RO', 46.08263000, 23.80106000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16425324'),
(92488, 'Comuna Ohaba Lungă', 4748, 'TM', 181, 'RO', 45.91812000, 21.99094000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q5053535'),
(92489, 'Comuna Oinacu', 4726, 'GR', 181, 'RO', 43.95609000, 26.03403000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5116912'),
(92490, 'Comuna Oituz', 4744, 'BC', 181, 'RO', 46.19045000, 26.56043000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q1027663'),
(92491, 'Comuna Ojdula', 4754, 'CV', 181, 'RO', 45.97871000, 26.24773000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5059285'),
(92492, 'Comuna Olanu', 4757, 'VL', 181, 'RO', 44.87416000, 24.29169000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725003'),
(92493, 'Comuna Olari', 4739, 'AR', 181, 'RO', 46.38673000, 21.54522000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12135529'),
(92494, 'Comuna Olari', 4729, 'PH', 181, 'RO', 44.79262000, 26.20498000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q1077263'),
(92495, 'Comuna Olcea', 4723, 'BH', 181, 'RO', 46.66090000, 21.98452000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16425680'),
(92496, 'Comuna Olteneşti', 4752, 'VS', 181, 'RO', 46.58438000, 27.90180000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q16427829'),
(92497, 'Comuna Olteni', 4728, 'TR', 181, 'RO', 44.18976000, 25.27800000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725004'),
(92498, 'Comuna Oltina', 4737, 'CT', 181, 'RO', 44.13879000, 27.64881000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5062217'),
(92499, 'Comuna Onceşti', 4760, 'MM', 181, 'RO', 47.84814000, 23.98183000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q5065066'),
(92500, 'Comuna Onceşti', 4744, 'BC', 181, 'RO', 46.47537000, 27.25835000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q2542482'),
(92501, 'Comuna Oniceni', 4731, 'NT', 181, 'RO', 46.78926000, 27.16336000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5113611'),
(92502, 'Comuna Oporelu', 4738, 'OT', 181, 'RO', 44.58682000, 24.42410000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725006'),
(92503, 'Comuna Oprişor', 4751, 'MH', 181, 'RO', 44.30105000, 23.08646000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q12725008'),
(92504, 'Comuna Optaşi-Mãgura', 4738, 'OT', 181, 'RO', 44.57944000, 24.65183000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q2292729'),
(92505, 'Comuna Oraşu Nou', 4746, 'SM', 181, 'RO', 47.83470000, 23.30211000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q5055628'),
(92506, 'Comuna Orbeasca', 4728, 'TR', 181, 'RO', 44.11606000, 25.32708000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q2721317'),
(92507, 'Comuna Orbeni', 4744, 'BC', 181, 'RO', 46.26934000, 27.01872000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q2467661'),
(92508, 'Comuna Orlat', 4755, 'SB', 181, 'RO', 45.75550000, 23.96855000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5063967'),
(92509, 'Comuna Orlea', 4738, 'OT', 181, 'RO', 43.75430000, 24.36775000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725010'),
(92510, 'Comuna Orleşti', 4757, 'VL', 181, 'RO', 44.78125000, 24.24623000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q2201998'),
(92511, 'Comuna Ormeniş', 4759, 'BV', 181, 'RO', 46.00473000, 25.55683000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q240473'),
(92512, 'Comuna Orodel', 4742, 'DJ', 181, 'RO', 44.26414000, 23.27390000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5067854'),
(92513, 'Comuna Orăştioara de Sus', 4721, 'HD', 181, 'RO', 45.70150000, 23.18208000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q5053550'),
(92514, 'Comuna Orţişoara', 4748, 'TM', 181, 'RO', 45.95206000, 21.22370000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q16427743'),
(92515, 'Comuna Osica de Jos', 4738, 'OT', 181, 'RO', 44.23800000, 24.29502000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725011'),
(92516, 'Comuna Osica de Sus', 4738, 'OT', 181, 'RO', 44.27268000, 24.33730000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725012'),
(92517, 'Comuna Ostra', 4720, 'SV', 181, 'RO', 47.40480000, 25.75594000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725014'),
(92518, 'Comuna Ostrov', 4737, 'CT', 181, 'RO', 44.08152000, 27.42046000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5062507'),
(92519, 'Comuna Ostrov', 4727, 'TL', 181, 'RO', 44.91052000, 28.14640000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12136761'),
(92520, 'Comuna Ostroveni', 4742, 'DJ', 181, 'RO', 43.82121000, 23.90789000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725015'),
(92521, 'Comuna Otelec', 4748, 'TM', 181, 'RO', 45.59079000, 20.85648000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5117744'),
(92522, 'Comuna Oteşani', 4757, 'VL', 181, 'RO', 45.04807000, 24.03989000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12725016'),
(92523, 'Comuna Ozun', 4754, 'CV', 181, 'RO', 45.78331000, 25.88260000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q16426328'),
(92524, 'Comuna Oşeşti', 4752, 'VS', 181, 'RO', 46.76376000, 27.44832000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12725019'),
(92525, 'Comuna Oşorhei', 4723, 'BH', 181, 'RO', 47.02748000, 22.04490000, '2019-10-05 23:15:01', '2020-05-01 17:23:08', 1, 'Q12137093'),
(92526, 'Comuna Oţeleni', 4735, 'IS', 181, 'RO', 47.08566000, 27.02730000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q5068832'),
(92527, 'Comuna Padeş', 4750, 'GJ', 181, 'RO', 45.05089000, 22.80954000, '2019-10-05 23:15:01', '2020-05-01 17:23:10', 1, 'Q2300999');

