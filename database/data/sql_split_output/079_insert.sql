INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(39800, 'Bassens', 4798, 'ARA', 75, 'FR', 45.57555000, 5.93900000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q1115736'),
(39801, 'Bassens', 4795, 'NAQ', 75, 'FR', 44.90226000, -0.51631000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q1115736'),
(39802, 'Bassillac', 4795, 'NAQ', 75, 'FR', 45.19305000, 0.81528000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q175550'),
(39803, 'Bassussarry', 4795, 'NAQ', 75, 'FR', 43.44272000, -1.51647000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q839448'),
(39804, 'Bastia', 4806, '20R', 75, 'FR', 42.70278000, 9.45000000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q134698'),
(39805, 'Batilly', 4820, 'GES', 75, 'FR', 49.17372000, 5.96869000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q134698'),
(39806, 'Battenheim', 4820, 'GES', 75, 'FR', 47.81999000, 7.38170000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q326411'),
(39807, 'Batz-sur-Mer', 4802, 'PDL', 75, 'FR', 47.27750000, -2.48027000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, '********'),
(39808, 'Baud', 4807, 'BRE', 75, 'FR', 47.87474000, -3.01760000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, '********'),
(39809, 'Baugy', 4818, 'CVL', 75, 'FR', 47.08181000, 2.72848000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, '********'),
(39810, 'Baugé-en-Anjou', 4802, 'PDL', 75, 'FR', 47.54447000, -0.10653000, '2019-10-05 22:47:43', '2020-05-01 17:22:46', 1, 'Q26768'),
(39811, 'Baule', 4818, 'CVL', 75, 'FR', 47.81084000, 1.67259000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q26768'),
(39812, 'Baulne', 4796, 'IDF', 75, 'FR', 48.49303000, 2.36230000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, '********'),
(39813, 'Baulon', 4807, 'BRE', 75, 'FR', 47.98590000, -1.93114000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q548352'),
(39814, 'Baume-les-Dames', 4825, 'BFC', 75, 'FR', 47.35295000, 6.36117000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q548352'),
(39815, 'Bauné', 4802, 'PDL', 75, 'FR', 47.49954000, -0.31906000, '2019-10-05 22:47:43', '2020-05-01 17:22:46', 1, 'Q548352'),
(39816, 'Bauvin', 4828, 'HDF', 75, 'FR', 50.51714000, 2.89404000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q548352'),
(39817, 'Bavans', 4825, 'BFC', 75, 'FR', 47.48528000, 6.73324000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q548352'),
(39818, 'Bavay', 4828, 'HDF', 75, 'FR', 50.29828000, 3.79372000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q477176'),
(39819, 'Bavent', 4804, 'NOR', 75, 'FR', 49.22996000, -0.18675000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q274976'),
(39820, 'Bavilliers', 4825, 'BFC', 75, 'FR', 47.62235000, 6.83543000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q274976'),
(39821, 'Bayard-sur-Marne', 4820, 'GES', 75, 'FR', 48.55410000, 5.07680000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q274976'),
(39822, 'Bayeux', 4804, 'NOR', 75, 'FR', 49.27732000, -0.70390000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q183910'),
(39823, 'Bayon', 4820, 'GES', 75, 'FR', 48.47425000, 6.31631000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q183910'),
(39824, 'Bayonne', 4795, 'NAQ', 75, 'FR', 43.49257000, -1.47624000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q134674'),
(39825, 'Bazainville', 4796, 'IDF', 75, 'FR', 48.80435000, 1.66732000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q83427'),
(39826, 'Bazancourt', 4820, 'GES', 75, 'FR', 49.36562000, 4.17051000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q83427'),
(39827, 'Bazas', 4795, 'NAQ', 75, 'FR', 44.43202000, -0.21327000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q208427'),
(39828, 'Bazeilles', 4820, 'GES', 75, 'FR', 49.67650000, 4.97752000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q383510'),
(39829, 'Bazemont', 4796, 'IDF', 75, 'FR', 48.92726000, 1.86651000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q83446'),
(39830, 'Bazet', 4799, 'OCC', 75, 'FR', 43.29145000, 0.06728000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q83446'),
(39831, 'Baziège', 4799, 'OCC', 75, 'FR', 43.45454000, 1.61399000, '2019-10-05 22:47:43', '2020-05-01 17:22:46', 1, 'Q83446'),
(39832, 'Bazoches-les-Gallerandes', 4818, 'CVL', 75, 'FR', 48.16540000, 2.04319000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q83446'),
(39833, 'Bazoges-en-Pareds', 4802, 'PDL', 75, 'FR', 46.65702000, -0.91654000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, 'Q83446'),
(39834, 'Bazouges-la-Pérouse', 4807, 'BRE', 75, 'FR', 48.42606000, -1.57431000, '2019-10-05 22:47:43', '2020-05-01 17:22:44', 1, 'Q83446'),
(39835, 'Bazouges-sur-le-Loir', 4802, 'PDL', 75, 'FR', 47.68928000, -0.16883000, '2019-10-05 22:47:43', '2019-10-05 22:47:43', 1, '********'),
(39836, 'Beaucaire', 4799, 'OCC', 75, 'FR', 43.80806000, 4.64417000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39837, 'Beaucamps-le-Vieux', 4828, 'HDF', 75, 'FR', 49.84642000, 1.78272000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39838, 'Beauchamp', 4796, 'IDF', 75, 'FR', 49.01667000, 2.20000000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39839, 'Beauchamps', 4828, 'HDF', 75, 'FR', 50.01202000, 1.51764000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39840, 'Beauchastel', 4798, 'ARA', 75, 'FR', 44.82587000, 4.80305000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39841, 'Beaucourt', 4825, 'BFC', 75, 'FR', 47.48878000, 6.92214000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39842, 'Beaucouzé', 4802, 'PDL', 75, 'FR', 47.47444000, -0.63016000, '2019-10-05 22:47:44', '2020-05-01 17:22:46', 1, 'Q813366'),
(39843, 'Beaucroissant', 4798, 'ARA', 75, 'FR', 45.34276000, 5.47102000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q813366'),
(39844, 'Beaucé', 4807, 'BRE', 75, 'FR', 48.33890000, -1.15804000, '2019-10-05 22:47:44', '2020-05-01 17:22:44', 1, 'Q813366'),
(39845, 'Beaufay', 4802, 'PDL', 75, 'FR', 48.14664000, 0.36224000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q813366'),
(39846, 'Beaufort', 4798, 'ARA', 75, 'FR', 45.71915000, 6.57331000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q813366'),
(39847, 'Beaufort-en-Vallée', 4802, 'PDL', 75, 'FR', 47.43965000, -0.21890000, '2019-10-05 22:47:44', '2020-05-01 17:22:46', 1, 'Q813372'),
(39848, 'Beaugency', 4818, 'CVL', 75, 'FR', 47.78019000, 1.62705000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q429910'),
(39849, 'Beaujeu', 4798, 'ARA', 75, 'FR', 46.15428000, 4.58826000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q429910'),
(39850, 'Beaulieu', 4799, 'OCC', 75, 'FR', 43.72861000, 4.02194000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q429910'),
(39851, 'Beaulieu-lès-Loches', 4818, 'CVL', 75, 'FR', 47.12526000, 1.01585000, '2019-10-05 22:47:44', '2020-05-01 17:22:44', 1, '********'),
(39852, 'Beaulieu-sous-la-Roche', 4802, 'PDL', 75, 'FR', 46.67647000, -1.61129000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39853, 'Beaulieu-sur-Dordogne', 4795, 'NAQ', 75, 'FR', 44.97832000, 1.83834000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q271315'),
(39854, 'Beaulieu-sur-Layon', 4802, 'PDL', 75, 'FR', 47.31095000, -0.58988000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q271315'),
(39855, 'Beaulieu-sur-Mer', 4812, 'PAC', 75, 'FR', 43.70692000, 7.33135000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q271315'),
(39856, 'Beaulon', 4798, 'ARA', 75, 'FR', 46.60156000, 3.67314000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q271315'),
(39857, 'Beaumes-de-Venise', 4812, 'PAC', 75, 'FR', 44.12227000, 5.03065000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q381313'),
(39858, 'Beaumetz-lès-Loges', 4828, 'HDF', 75, 'FR', 50.23897000, 2.65818000, '2019-10-05 22:47:44', '2020-05-01 17:22:45', 1, 'Q381313'),
(39859, 'Beaumont', 4798, 'ARA', 75, 'FR', 45.75165000, 3.08294000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q381313'),
(39860, 'Beaumont', 4795, 'NAQ', 75, 'FR', 46.73773000, 0.42961000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q381313'),
(39861, 'Beaumont-Hague', 4804, 'NOR', 75, 'FR', 49.66387000, -1.83822000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q381313'),
(39862, 'Beaumont-Monteux', 4798, 'ARA', 75, 'FR', 45.01980000, 4.91883000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q381313'),
(39863, 'Beaumont-de-Lomagne', 4799, 'OCC', 75, 'FR', 43.88285000, 0.98762000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q381313'),
(39864, 'Beaumont-de-Pertuis', 4812, 'PAC', 75, 'FR', 43.73737000, 5.68959000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q381313'),
(39865, 'Beaumont-du-Gâtinais', 4796, 'IDF', 75, 'FR', 48.13860000, 2.47913000, '2019-10-05 22:47:44', '2020-05-01 17:22:43', 1, 'Q381313'),
(39866, 'Beaumont-du-Périgord', 4795, 'NAQ', 75, 'FR', 44.76662000, 0.76916000, '2019-10-05 22:47:44', '2020-05-01 17:22:45', 1, 'Q177155'),
(39867, 'Beaumont-en-Véron', 4818, 'CVL', 75, 'FR', 47.19397000, 0.18436000, '2019-10-05 22:47:44', '2020-05-01 17:22:44', 1, 'Q177155'),
(39868, 'Beaumont-la-Ronce', 4818, 'CVL', 75, 'FR', 47.56948000, 0.67017000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q177155'),
(39869, 'Beaumont-le-Roger', 4804, 'NOR', 75, 'FR', 49.07839000, 0.78081000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q177155'),
(39870, 'Beaumont-lès-Valence', 4798, 'ARA', 75, 'FR', 44.86203000, 4.94309000, '2019-10-05 22:47:44', '2020-05-01 17:22:43', 1, 'Q177155'),
(39871, 'Beaumont-sur-Lèze', 4799, 'OCC', 75, 'FR', 43.38095000, 1.35826000, '2019-10-05 22:47:44', '2020-05-01 17:22:46', 1, 'Q177155'),
(39872, 'Beaumont-sur-Oise', 4796, 'IDF', 75, 'FR', 49.14232000, 2.28705000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q177155'),
(39873, 'Beaumont-sur-Sarthe', 4802, 'PDL', 75, 'FR', 48.22772000, 0.13186000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q177155'),
(39874, 'Beaune', 4825, 'BFC', 75, 'FR', 47.02413000, 4.83887000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q177155'),
(39875, 'Beaune-la-Rolande', 4818, 'CVL', 75, 'FR', 48.07124000, 2.43140000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q177155'),
(39876, 'Beaupréau', 4802, 'PDL', 75, 'FR', 47.20464000, -0.98703000, '2019-10-05 22:47:44', '2020-05-01 17:22:46', 1, 'Q752918'),
(39877, 'Beaupuy', 4795, 'NAQ', 75, 'FR', 44.53549000, 0.14900000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q752918'),
(39878, 'Beaupuy', 4799, 'OCC', 75, 'FR', 43.64713000, 1.55517000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q752918'),
(39879, 'Beauquesne', 4828, 'HDF', 75, 'FR', 50.08526000, 2.39276000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q752918'),
(39880, 'Beaurains', 4828, 'HDF', 75, 'FR', 50.26599000, 2.79467000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q752918'),
(39881, 'Beaurainville', 4828, 'HDF', 75, 'FR', 50.42432000, 1.89938000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q752918'),
(39882, 'Beaurepaire', 4798, 'ARA', 75, 'FR', 45.33658000, 5.04878000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q752918'),
(39883, 'Beaurepaire', 4802, 'PDL', 75, 'FR', 46.90977000, -1.08928000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q752918'),
(39884, 'Beaurevoir', 4828, 'HDF', 75, 'FR', 49.99714000, 3.30855000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39885, 'Beausemblant', 4798, 'ARA', 75, 'FR', 45.21706000, 4.83241000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39886, 'Beausoleil', 4812, 'PAC', 75, 'FR', 43.74311000, 7.42250000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39887, 'Beautiran', 4795, 'NAQ', 75, 'FR', 44.70393000, -0.45202000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39888, 'Beautor', 4828, 'HDF', 75, 'FR', 49.65214000, 3.34475000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39889, 'Beauvais', 4828, 'HDF', 75, 'FR', 49.43333000, 2.08333000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39890, 'Beauval', 4828, 'HDF', 75, 'FR', 50.10789000, 2.33269000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39891, 'Beauvallon', 4798, 'ARA', 75, 'FR', 44.85593000, 4.90756000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39892, 'Beauvoir-de-Marc', 4798, 'ARA', 75, 'FR', 45.52009000, 5.07906000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q274740'),
(39893, 'Beauvoir-sur-Mer', 4802, 'PDL', 75, 'FR', 46.91274000, -2.04156000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39894, 'Beauvoir-sur-Niort', 4795, 'NAQ', 75, 'FR', 46.18017000, -0.47178000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39895, 'Beauvois-en-Cambrésis', 4828, 'HDF', 75, 'FR', 50.13735000, 3.37870000, '2019-10-05 22:47:44', '2020-05-01 17:22:45', 1, '********'),
(39896, 'Beauvoisin', 4799, 'OCC', 75, 'FR', 43.71835000, 4.32339000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39897, 'Beauzac', 4798, 'ARA', 75, 'FR', 45.25913000, 4.09874000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39898, 'Beauzelle', 4799, 'OCC', 75, 'FR', 43.66713000, 1.37518000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39899, 'Behren-lès-Forbach', 4820, 'GES', 75, 'FR', 49.16949000, 6.93933000, '2019-10-05 22:47:44', '2020-05-01 17:22:44', 1, 'Q21777'),
(39900, 'Beignon', 4807, 'BRE', 75, 'FR', 47.97200000, -2.16933000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62838'),
(39901, 'Belberaud', 4799, 'OCC', 75, 'FR', 43.50557000, 1.56725000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62838'),
(39902, 'Belbeuf', 4804, 'NOR', 75, 'FR', 49.38766000, 1.14245000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62838'),
(39903, 'Belcodène', 4812, 'PAC', 75, 'FR', 43.42612000, 5.58878000, '2019-10-05 22:47:44', '2020-05-01 17:22:46', 1, 'Q62838'),
(39904, 'Belfort', 4825, 'BFC', 75, 'FR', 47.64218000, 6.85385000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q171545'),
(39905, 'Belgentier', 4812, 'PAC', 75, 'FR', 43.24535000, 5.99933000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q687913'),
(39906, 'Belin-Béliet', 4795, 'NAQ', 75, 'FR', 44.50000000, -0.78333000, '2019-10-05 22:47:44', '2020-05-01 17:22:45', 1, 'Q243891'),
(39907, 'Bellac', 4795, 'NAQ', 75, 'FR', 46.12209000, 1.04931000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39908, 'Bellaing', 4828, 'HDF', 75, 'FR', 50.36811000, 3.42515000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39909, 'Belle de Mai', 4812, 'PAC', 75, 'FR', 43.31184000, 5.38541000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39910, 'Belle-Isle-en-Terre', 4807, 'BRE', 75, 'FR', 48.54478000, -3.39500000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39911, 'Belle-Plagne', 4798, 'ARA', 75, 'FR', 45.50932000, 6.70685000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39912, 'Bellegarde', 4818, 'CVL', 75, 'FR', 47.98333000, 2.43333000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39913, 'Bellegarde', 4799, 'OCC', 75, 'FR', 43.75329000, 4.51654000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39914, 'Bellegarde-en-Forez', 4798, 'ARA', 75, 'FR', 45.64469000, 4.29721000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q214597'),
(39915, 'Bellegarde-sur-Valserine', 4798, 'ARA', 75, 'FR', 46.10787000, 5.82421000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q191475'),
(39916, 'Bellenaves', 4798, 'ARA', 75, 'FR', 46.20045000, 3.07995000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q263971'),
(39917, 'Belleneuve', 4825, 'BFC', 75, 'FR', 47.36224000, 5.26393000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q263971'),
(39918, 'Bellengreville', 4804, 'NOR', 75, 'FR', 49.12425000, -0.20961000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q263971'),
(39919, 'Bellerive-sur-Allier', 4798, 'ARA', 75, 'FR', 46.11652000, 3.40406000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q245132'),
(39920, 'Belleu', 4828, 'HDF', 75, 'FR', 49.35917000, 3.33556000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q1321539'),
(39921, 'Bellevaux', 4798, 'ARA', 75, 'FR', 46.25875000, 6.53351000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q1321539'),
(39922, 'Belleville', 4798, 'ARA', 75, 'FR', 46.10867000, 4.74920000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q583071'),
(39923, 'Belleville', 4820, 'GES', 75, 'FR', 48.81817000, 6.10294000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q583071'),
(39924, 'Belleville-sur-Loire', 4818, 'CVL', 75, 'FR', 47.50000000, 2.85000000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39925, 'Belleville-sur-Meuse', 4820, 'GES', 75, 'FR', 49.17863000, 5.37190000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39926, 'Belleville-sur-Vie', 4802, 'PDL', 75, 'FR', 46.78369000, -1.42905000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39927, 'Belley', 4798, 'ARA', 75, 'FR', 45.75917000, 5.68813000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q188496'),
(39928, 'Bellignat', 4798, 'ARA', 75, 'FR', 46.24237000, 5.62843000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q210917'),
(39929, 'Belligné', 4802, 'PDL', 75, 'FR', 47.46774000, -1.02780000, '2019-10-05 22:47:44', '2020-05-01 17:22:46', 1, 'Q932919'),
(39930, 'Belloy-en-France', 4796, 'IDF', 75, 'FR', 49.08837000, 2.37159000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, '********'),
(39931, 'Bellême', 4804, 'NOR', 75, 'FR', 48.37329000, 0.57005000, '2019-10-05 22:47:44', '2020-05-01 17:22:45', 1, 'Q816092'),
(39932, 'Belmont-de-la-Loire', 4798, 'ARA', 75, 'FR', 46.16596000, 4.34737000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q816092'),
(39933, 'Belmont-sur-Rance', 4799, 'OCC', 75, 'FR', 43.81981000, 2.75524000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q816092'),
(39934, 'Belpech', 4799, 'OCC', 75, 'FR', 43.19957000, 1.75157000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q193546'),
(39935, 'Belsunce', 4812, 'PAC', 75, 'FR', 43.29834000, 5.37660000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q193546'),
(39936, 'Belvès', 4795, 'NAQ', 75, 'FR', 44.77632000, 1.00588000, '2019-10-05 22:47:44', '2020-05-01 17:22:45', 1, 'Q273562'),
(39937, 'Belz', 4807, 'BRE', 75, 'FR', 47.67506000, -3.16800000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62856'),
(39938, 'Benet', 4802, 'PDL', 75, 'FR', 46.36972000, -0.59333000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62856'),
(39939, 'Benfeld', 4820, 'GES', 75, 'FR', 48.37062000, 7.59370000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q22776'),
(39940, 'Bennecourt', 4796, 'IDF', 75, 'FR', 49.04148000, 1.55469000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q83441'),
(39941, 'Bennwihr', 4820, 'GES', 75, 'FR', 48.14456000, 7.32445000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q83441'),
(39942, 'Benquet', 4795, 'NAQ', 75, 'FR', 43.82992000, -0.50102000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q223902'),
(39943, 'Berck', 4828, 'HDF', 75, 'FR', 50.40000000, 1.60000000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q223902'),
(39944, 'Berck-Plage', 4828, 'HDF', 75, 'FR', 50.40704000, 1.56446000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q223902'),
(39945, 'Bergerac', 4795, 'NAQ', 75, 'FR', 44.85118000, 0.48200000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q187911'),
(39946, 'Bergheim', 4820, 'GES', 75, 'FR', 48.20540000, 7.36299000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q187911'),
(39947, 'Bergholtz', 4820, 'GES', 75, 'FR', 47.91670000, 7.24651000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q187911'),
(39948, 'Bergues', 4828, 'HDF', 75, 'FR', 50.96882000, 2.43242000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q244934'),
(39949, 'Berlaimont', 4828, 'HDF', 75, 'FR', 50.20155000, 3.81343000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q46007'),
(39950, 'Bernardswiller', 4820, 'GES', 75, 'FR', 48.45035000, 7.46238000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q21247'),
(39951, 'Bernaville', 4828, 'HDF', 75, 'FR', 50.13232000, 2.16300000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q962896'),
(39952, 'Bernay', 4804, 'NOR', 75, 'FR', 49.08888000, 0.59858000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q962896'),
(39953, 'Bernes-sur-Oise', 4796, 'IDF', 75, 'FR', 49.16128000, 2.30000000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q962896'),
(39954, 'Berneval-le-Grand', 4804, 'NOR', 75, 'FR', 49.95328000, 1.18755000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q476813'),
(39955, 'Bernin', 4798, 'ARA', 75, 'FR', 45.26772000, 5.86457000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q476813'),
(39956, 'Bernis', 4799, 'OCC', 75, 'FR', 43.76913000, 4.28713000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q476813'),
(39957, 'Bernières-sur-Mer', 4804, 'NOR', 75, 'FR', 49.33057000, -0.42299000, '2019-10-05 22:47:44', '2020-05-01 17:22:45', 1, 'Q476813'),
(39958, 'Berné', 4807, 'BRE', 75, 'FR', 47.99470000, -3.39421000, '2019-10-05 22:47:44', '2020-05-01 17:22:44', 1, 'Q62872'),
(39959, 'Berre-l\'Étang', 4812, 'PAC', 75, 'FR', 43.47809000, 5.17044000, '2019-10-05 22:47:44', '2020-05-01 17:22:46', 1, 'Q317809'),
(39960, 'Berre-les-Alpes', 4812, 'PAC', 75, 'FR', 43.83052000, 7.32877000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q843023'),
(39961, 'Berric', 4807, 'BRE', 75, 'FR', 47.63326000, -2.52250000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62850'),
(39962, 'Berrien', 4807, 'BRE', 75, 'FR', 48.40278000, -3.75266000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62850'),
(39963, 'Berrwiller', 4820, 'GES', 75, 'FR', 47.84984000, 7.21922000, '2019-10-05 22:47:44', '2019-10-05 22:47:44', 1, 'Q62850'),
(39964, 'Berson', 4795, 'NAQ', 75, 'FR', 45.10679000, -0.58774000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q62850'),
(39965, 'Berstett', 4820, 'GES', 75, 'FR', 48.67900000, 7.65721000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q62850'),
(39966, 'Bersée', 4828, 'HDF', 75, 'FR', 50.47978000, 3.14765000, '2019-10-05 22:47:45', '2020-05-01 17:22:45', 1, 'Q62850'),
(39967, 'Berteaucourt-les-Dames', 4828, 'HDF', 75, 'FR', 50.04697000, 2.15750000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q62850'),
(39968, 'Berthecourt', 4828, 'HDF', 75, 'FR', 49.35097000, 2.22471000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39969, 'Bertrange', 4820, 'GES', 75, 'FR', 49.31368000, 6.19208000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39970, 'Bertry', 4828, 'HDF', 75, 'FR', 50.08718000, 3.44298000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39971, 'Besançon', 4825, 'BFC', 75, 'FR', 47.24878000, 6.01815000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q37776'),
(39972, 'Besné', 4802, 'PDL', 75, 'FR', 47.40000000, -2.08976000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, 'Q37776'),
(39973, 'Bessan', 4799, 'OCC', 75, 'FR', 43.36196000, 3.42288000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q270170'),
(39974, 'Bessancourt', 4796, 'IDF', 75, 'FR', 49.03765000, 2.20936000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39975, 'Bessay-sur-Allier', 4798, 'ARA', 75, 'FR', 46.44199000, 3.36257000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39976, 'Besse-et-Saint-Anastaise', 4798, 'ARA', 75, 'FR', 45.51667000, 2.93333000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39977, 'Besse-sur-Issole', 4812, 'PAC', 75, 'FR', 43.34892000, 6.17656000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39978, 'Bessenay', 4798, 'ARA', 75, 'FR', 45.77661000, 4.55441000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39979, 'Bessines', 4795, 'NAQ', 75, 'FR', 46.30207000, -0.51294000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(39980, 'Bessines-sur-Gartempe', 4795, 'NAQ', 75, 'FR', 46.10799000, 1.36865000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q173866'),
(39981, 'Bessières', 4799, 'OCC', 75, 'FR', 43.79861000, 1.60624000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, 'Q173866'),
(39982, 'Bessèges', 4799, 'OCC', 75, 'FR', 44.29230000, 4.09661000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, 'Q271325'),
(39983, 'Bessé-sur-Braye', 4802, 'PDL', 75, 'FR', 47.83333000, 0.75000000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, 'Q271325'),
(39984, 'Bethoncourt', 4825, 'BFC', 75, 'FR', 47.53512000, 6.80504000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q831409'),
(39985, 'Betschdorf', 4820, 'GES', 75, 'FR', 48.89825000, 7.90196000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22384'),
(39986, 'Bettancourt-la-Ferrée', 4820, 'GES', 75, 'FR', 48.64898000, 4.96971000, '2019-10-05 22:47:45', '2020-05-01 17:22:44', 1, 'Q22384'),
(39987, 'Betton', 4807, 'BRE', 75, 'FR', 48.18048000, -1.63829000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22384'),
(39988, 'Betz', 4828, 'HDF', 75, 'FR', 49.15558000, 2.95584000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22384'),
(39989, 'Beure', 4825, 'BFC', 75, 'FR', 47.20690000, 6.00548000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22384'),
(39990, 'Beuville', 4804, 'NOR', 75, 'FR', 49.24364000, -0.32585000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22384'),
(39991, 'Beuvillers', 4804, 'NOR', 75, 'FR', 49.12961000, 0.25492000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22384'),
(39992, 'Beuvrages', 4828, 'HDF', 75, 'FR', 50.38414000, 3.49420000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22384'),
(39993, 'Beuvry', 4828, 'HDF', 75, 'FR', 50.51674000, 2.68541000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q334249'),
(39994, 'Beuzec-Cap-Sizun', 4807, 'BRE', 75, 'FR', 48.07567000, -4.51200000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(39995, 'Beuzeville', 4804, 'NOR', 75, 'FR', 49.34483000, 0.34254000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(39996, 'Beuzeville-la-Grenier', 4804, 'NOR', 75, 'FR', 49.59157000, 0.42684000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(39997, 'Beychac-et-Caillau', 4795, 'NAQ', 75, 'FR', 44.87781000, -0.40219000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(39998, 'Beynat', 4795, 'NAQ', 75, 'FR', 45.12444000, 1.72323000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(39999, 'Beynes', 4796, 'IDF', 75, 'FR', 48.85626000, 1.87261000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(40000, 'Beynost', 4798, 'ARA', 75, 'FR', 45.83569000, 4.99910000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(40001, 'Bezannes', 4820, 'GES', 75, 'FR', 49.22339000, 3.98892000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q329328'),
(40002, 'Bezons', 4796, 'IDF', 75, 'FR', 48.92426000, 2.21280000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q238429'),
(40003, 'Bezouce', 4799, 'OCC', 75, 'FR', 43.88229000, 4.49072000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q238429'),
(40004, 'Biache-Saint-Vaast', 4828, 'HDF', 75, 'FR', 50.30869000, 2.94777000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q238429'),
(40005, 'Biard', 4795, 'NAQ', 75, 'FR', 46.57889000, 0.30812000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q238429'),
(40006, 'Biarritz', 4795, 'NAQ', 75, 'FR', 43.48012000, -1.55558000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q132790'),
(40007, 'Biars-sur-Cère', 4799, 'OCC', 75, 'FR', 44.92629000, 1.85403000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, 'Q132790'),
(40008, 'Bias', 4795, 'NAQ', 75, 'FR', 44.41655000, 0.66977000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q132790'),
(40009, 'Bidache', 4795, 'NAQ', 75, 'FR', 43.48299000, -1.14121000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q197008'),
(40010, 'Bidart', 4795, 'NAQ', 75, 'FR', 43.43760000, -1.59127000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q932076'),
(40011, 'Bienville', 4820, 'GES', 75, 'FR', 48.57582000, 5.04579000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q932076'),
(40012, 'Bierne', 4828, 'HDF', 75, 'FR', 50.96232000, 2.40963000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q932076'),
(40013, 'Biesheim', 4820, 'GES', 75, 'FR', 48.04118000, 7.54474000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q326568'),
(40014, 'Biesles', 4820, 'GES', 75, 'FR', 48.08597000, 5.29409000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q326568'),
(40015, 'Biganos', 4795, 'NAQ', 75, 'FR', 44.64504000, -0.97367000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q275609'),
(40016, 'Bignan', 4807, 'BRE', 75, 'FR', 47.87935000, -2.77153000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q63013'),
(40017, 'Bignoux', 4795, 'NAQ', 75, 'FR', 46.60008000, 0.46932000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q63013'),
(40018, 'Biguglia', 4806, '20R', 75, 'FR', 42.62692000, 9.42018000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q476364'),
(40019, 'Bihorel', 4804, 'NOR', 75, 'FR', 49.45468000, 1.12230000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q1113714'),
(40020, 'Bilieu', 4798, 'ARA', 75, 'FR', 45.44809000, 5.54268000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q1113714'),
(40021, 'Billom', 4798, 'ARA', 75, 'FR', 45.72267000, 3.33869000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q246257'),
(40022, 'Billy-Berclau', 4828, 'HDF', 75, 'FR', 50.51783000, 2.86613000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q325913'),
(40023, 'Billy-Montigny', 4828, 'HDF', 75, 'FR', 50.41711000, 2.90286000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q772803'),
(40024, 'Billy-sur-Aisne', 4828, 'HDF', 75, 'FR', 49.35632000, 3.38357000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q772803'),
(40025, 'Billère', 4795, 'NAQ', 75, 'FR', 43.30000000, -0.40000000, '2019-10-05 22:47:45', '2020-05-01 17:22:45', 1, 'Q195193'),
(40026, 'Binic', 4807, 'BRE', 75, 'FR', 48.60074000, -2.82602000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q214370'),
(40027, 'Bining', 4820, 'GES', 75, 'FR', 49.03729000, 7.25273000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q21749'),
(40028, 'Biol', 4798, 'ARA', 75, 'FR', 45.49140000, 5.38550000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q21749'),
(40029, 'Biot', 4812, 'PAC', 75, 'FR', 43.62852000, 7.09530000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q21749'),
(40030, 'Biscarrosse', 4795, 'NAQ', 75, 'FR', 44.39454000, -1.16721000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q273644'),
(40031, 'Bischheim', 4820, 'GES', 75, 'FR', 48.61612000, 7.75343000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q273644'),
(40032, 'Bischoffsheim', 4820, 'GES', 75, 'FR', 48.48703000, 7.48967000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q273644'),
(40033, 'Bischwiller', 4820, 'GES', 75, 'FR', 48.76826000, 7.85406000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22431'),
(40034, 'Bitche', 4820, 'GES', 75, 'FR', 49.05232000, 7.42992000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q22754'),
(40035, 'Bitschwiller-lès-Thann', 4820, 'GES', 75, 'FR', 47.82969000, 7.07911000, '2019-10-05 22:47:45', '2020-05-01 17:22:44', 1, 'Q22754'),
(40036, 'Biviers', 4798, 'ARA', 75, 'FR', 45.23333000, 5.80000000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q582635'),
(40037, 'Bizanet', 4799, 'OCC', 75, 'FR', 43.16419000, 2.87034000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q582635'),
(40038, 'Bizanos', 4795, 'NAQ', 75, 'FR', 43.28333000, -0.35000000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q857595'),
(40039, 'Bize-Minervois', 4799, 'OCC', 75, 'FR', 43.31656000, 2.87134000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q1018288'),
(40040, 'Bièvres', 4796, 'IDF', 75, 'FR', 48.75772000, 2.21881000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q1018288'),
(40041, 'Biéville-Beuville', 4804, 'NOR', 75, 'FR', 49.24293000, -0.32762000, '2019-10-05 22:47:45', '2020-05-01 17:22:45', 1, 'Q317610'),
(40042, 'Blacé', 4798, 'ARA', 75, 'FR', 46.03152000, 4.64448000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q317610'),
(40043, 'Blaesheim', 4820, 'GES', 75, 'FR', 48.50648000, 7.60923000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q317610'),
(40044, 'Blagnac', 4799, 'OCC', 75, 'FR', 43.63276000, 1.39399000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q271068'),
(40045, 'Blagny', 4820, 'GES', 75, 'FR', 49.62167000, 5.19194000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q371412'),
(40046, 'Blain', 4802, 'PDL', 75, 'FR', 47.47655000, -1.76285000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q134204'),
(40047, 'Blaincourt-lès-Précy', 4828, 'HDF', 75, 'FR', 49.23333000, 2.35000000, '2019-10-05 22:47:45', '2020-05-01 17:22:45', 1, 'Q946691'),
(40048, 'Blainville-Crevon', 4804, 'NOR', 75, 'FR', 49.50395000, 1.29952000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q946691'),
(40049, 'Blainville-sur-Mer', 4804, 'NOR', 75, 'FR', 49.06599000, -1.58340000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q946691'),
(40050, 'Blainville-sur-Orne', 4804, 'NOR', 75, 'FR', 49.22913000, -0.30061000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q946691'),
(40051, 'Blaison-Gohier', 4802, 'PDL', 75, 'FR', 47.39951000, -0.37723000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q660234'),
(40052, 'Blamont', 4825, 'BFC', 75, 'FR', 47.38513000, 6.84800000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q247806'),
(40053, 'Blancafort', 4818, 'CVL', 75, 'FR', 47.53219000, 2.52981000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q247806'),
(40054, 'Blangy-sur-Bresle', 4804, 'NOR', 75, 'FR', 49.93211000, 1.62514000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q247806'),
(40055, 'Blanquefort', 4795, 'NAQ', 75, 'FR', 44.91248000, -0.63663000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q247806'),
(40056, 'Blanzat', 4798, 'ARA', 75, 'FR', 45.82975000, 3.07794000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q247806'),
(40057, 'Blanzy', 4825, 'BFC', 75, 'FR', 46.70012000, 4.38833000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(40058, 'Blaringhem', 4828, 'HDF', 75, 'FR', 50.69110000, 2.40321000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q1117320'),
(40059, 'Blausasc', 4812, 'PAC', 75, 'FR', 43.80572000, 7.36477000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q775404'),
(40060, 'Blauzac', 4799, 'OCC', 75, 'FR', 43.96151000, 4.36930000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q775404'),
(40061, 'Blavozy', 4798, 'ARA', 75, 'FR', 45.05720000, 3.97993000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q775404'),
(40062, 'Blaye', 4795, 'NAQ', 75, 'FR', 45.12782000, -0.66230000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q202562'),
(40063, 'Blaye-les-Mines', 4799, 'OCC', 75, 'FR', 44.03073000, 2.13166000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q371961'),
(40064, 'Blendecques', 4828, 'HDF', 75, 'FR', 50.71843000, 2.28601000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q599950'),
(40065, 'Bletterans', 4825, 'BFC', 75, 'FR', 46.74673000, 5.45444000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q120341'),
(40066, 'Bliesbruck', 4820, 'GES', 75, 'FR', 49.11543000, 7.18112000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q21694'),
(40067, 'Bligny-lès-Beaune', 4825, 'BFC', 75, 'FR', 46.98631000, 4.82620000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q21694'),
(40068, 'Blodelsheim', 4820, 'GES', 75, 'FR', 47.88538000, 7.53635000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q21694'),
(40069, 'Blois', 4818, 'CVL', 75, 'FR', 47.59432000, 1.32912000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q160927'),
(40070, 'Blonville-sur-Mer', 4804, 'NOR', 75, 'FR', 49.33709000, 0.02709000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q842771'),
(40071, 'Blotzheim', 4820, 'GES', 75, 'FR', 47.60260000, 7.49654000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q467351'),
(40072, 'Bléneau', 4825, 'BFC', 75, 'FR', 47.70000000, 2.95000000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q467351'),
(40073, 'Blénod-lès-Pont-à-Mousson', 4820, 'GES', 75, 'FR', 48.88487000, 6.04844000, '2019-10-05 22:47:45', '2020-05-01 17:22:44', 1, 'Q467351'),
(40074, 'Blénod-lès-Toul', 4820, 'GES', 75, 'FR', 48.59882000, 5.83685000, '2019-10-05 22:47:45', '2020-05-01 17:22:44', 1, 'Q467351'),
(40075, 'Blérancourt', 4828, 'HDF', 75, 'FR', 49.51667000, 3.15000000, '2019-10-05 22:47:45', '2020-05-01 17:22:45', 1, 'Q669867'),
(40076, 'Bléré', 4818, 'CVL', 75, 'FR', 47.32738000, 0.99186000, '2019-10-05 22:47:45', '2020-05-01 17:22:44', 1, 'Q263003'),
(40077, 'Bobigny', 4796, 'IDF', 75, 'FR', 48.90982000, 2.45012000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q154724'),
(40078, 'Bodilis', 4807, 'BRE', 75, 'FR', 48.52978000, -4.11567000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q154724'),
(40079, 'Boeil-Bezing', 4795, 'NAQ', 75, 'FR', 43.21667000, -0.26667000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q843380'),
(40080, 'Boeschepe', 4828, 'HDF', 75, 'FR', 50.80000000, 2.70000000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q843380'),
(40081, 'Bohain-en-Vermandois', 4828, 'HDF', 75, 'FR', 49.98730000, 3.45300000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q271329'),
(40082, 'Bohars', 4807, 'BRE', 75, 'FR', 48.42983000, -4.51292000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q271329'),
(40083, 'Boigny-sur-Bionne', 4818, 'CVL', 75, 'FR', 47.93333000, 2.01667000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q740299'),
(40084, 'Bois-Colombes', 4796, 'IDF', 75, 'FR', 48.91936000, 2.27485000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q212566'),
(40085, 'Bois-Grenier', 4828, 'HDF', 75, 'FR', 50.64985000, 2.87409000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q212566'),
(40086, 'Bois-Guillaume', 4804, 'NOR', 75, 'FR', 49.46020000, 1.12219000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(40087, 'Bois-de-Cené', 4802, 'PDL', 75, 'FR', 46.93689000, -1.88656000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, '********'),
(40088, 'Bois-d’Amont', 4825, 'BFC', 75, 'FR', 46.53280000, 6.13750000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, '********'),
(40089, 'Bois-d’Arcy', 4796, 'IDF', 75, 'FR', 48.79966000, 2.02325000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, '********'),
(40090, 'Bois-le-Roi', 4796, 'IDF', 75, 'FR', 48.47348000, 2.70464000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(40091, 'Boiscommun', 4818, 'CVL', 75, 'FR', 48.03576000, 2.38333000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, '********'),
(40092, 'Boisgervilly', 4807, 'BRE', 75, 'FR', 48.16692000, -2.06426000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q213382'),
(40093, 'Boismé', 4795, 'NAQ', 75, 'FR', 46.77393000, -0.43476000, '2019-10-05 22:47:45', '2020-05-01 17:22:45', 1, '********'),
(40094, 'Boisseron', 4799, 'OCC', 75, 'FR', 43.75795000, 4.07970000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q203083'),
(40095, 'Boisset-et-Gaujac', 4799, 'OCC', 75, 'FR', 44.04749000, 4.00861000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q203083'),
(40096, 'Boisseuil', 4795, 'NAQ', 75, 'FR', 45.76977000, 1.33333000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q203083'),
(40097, 'Boissise-le-Roi', 4796, 'IDF', 75, 'FR', 48.52479000, 2.56971000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q1416731'),
(40098, 'Boissy-Saint-Léger', 4796, 'IDF', 75, 'FR', 48.75149000, 2.51163000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q1416731'),
(40099, 'Boissy-le-Châtel', 4796, 'IDF', 75, 'FR', 48.82073000, 3.13651000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q1416731'),
(40100, 'Boissy-le-Cutté', 4796, 'IDF', 75, 'FR', 48.47020000, 2.28326000, '2019-10-05 22:47:45', '2020-05-01 17:22:43', 1, 'Q1416731'),
(40101, 'Boissy-sous-Saint-Yon', 4796, 'IDF', 75, 'FR', 48.55379000, 2.21212000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q1416731'),
(40102, 'Bolbec', 4804, 'NOR', 75, 'FR', 49.57321000, 0.47339000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q680286'),
(40103, 'Bollezeele', 4828, 'HDF', 75, 'FR', 50.86687000, 2.34751000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q1046977'),
(40104, 'Bollwiller', 4820, 'GES', 75, 'FR', 47.85832000, 7.26179000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q326352'),
(40105, 'Bollène', 4812, 'PAC', 75, 'FR', 44.28124000, 4.74891000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, 'Q326352'),
(40106, 'Bologne', 4820, 'GES', 75, 'FR', 48.20005000, 5.14209000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q665760'),
(40107, 'Bompas', 4799, 'OCC', 75, 'FR', 42.73333000, 2.93333000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q665760'),
(40108, 'Bon-Encontre', 4795, 'NAQ', 75, 'FR', 44.18518000, 0.66759000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q24758'),
(40109, 'Bon-Secours', 4812, 'PAC', 75, 'FR', 43.31923000, 5.38426000, '2019-10-05 22:47:45', '2019-10-05 22:47:45', 1, 'Q24758'),
(40110, 'Bonchamp-lès-Laval', 4802, 'PDL', 75, 'FR', 48.07357000, -0.70000000, '2019-10-05 22:47:45', '2020-05-01 17:22:46', 1, '********'),
(40111, 'Bondoufle', 4796, 'IDF', 75, 'FR', 48.61294000, 2.37775000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q675232'),
(40112, 'Bondues', 4828, 'HDF', 75, 'FR', 50.70196000, 3.09497000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q767186'),
(40113, 'Bondy', 4796, 'IDF', 75, 'FR', 48.90180000, 2.48931000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q237394'),
(40114, 'Bonifacio', 4806, '20R', 75, 'FR', 41.38740000, 9.15941000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q204051'),
(40115, 'Bonnac-la-Côte', 4795, 'NAQ', 75, 'FR', 45.94212000, 1.28417000, '2019-10-05 22:47:46', '2020-05-01 17:22:45', 1, 'Q204051'),
(40116, 'Bonnat', 4795, 'NAQ', 75, 'FR', 46.33333000, 1.90000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q204051'),
(40117, 'Bonne', 4798, 'ARA', 75, 'FR', 46.17207000, 6.32443000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q204051'),
(40118, 'Bonnefamille', 4798, 'ARA', 75, 'FR', 45.59956000, 5.12489000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q204051'),
(40119, 'Bonnelles', 4796, 'IDF', 75, 'FR', 48.61816000, 2.02922000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q204051'),
(40120, 'Bonnemain', 4807, 'BRE', 75, 'FR', 48.46652000, -1.76774000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q922880'),
(40121, 'Bonnes', 4795, 'NAQ', 75, 'FR', 46.60386000, 0.59791000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q922880'),
(40122, 'Bonneuil-Matours', 4795, 'NAQ', 75, 'FR', 46.68155000, 0.57063000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q922880'),
(40123, 'Bonneuil-sur-Marne', 4796, 'IDF', 75, 'FR', 48.76950000, 2.47930000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q670957'),
(40124, 'Bonneval', 4818, 'CVL', 75, 'FR', 48.18312000, 1.38524000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q670957'),
(40125, 'Bonneveine', 4812, 'PAC', 75, 'FR', 43.25000000, 5.38333000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q670957'),
(40126, 'Bonneville', 4798, 'ARA', 75, 'FR', 46.08020000, 6.40726000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q670957'),
(40127, 'Bonnieux', 4812, 'PAC', 75, 'FR', 43.82396000, 5.30759000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q247260'),
(40128, 'Bonnières-sur-Seine', 4796, 'IDF', 75, 'FR', 49.03525000, 1.57830000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q247260'),
(40129, 'Bonny-sur-Loire', 4818, 'CVL', 75, 'FR', 47.56172000, 2.83933000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40130, 'Bonnétable', 4802, 'PDL', 75, 'FR', 48.17908000, 0.42570000, '2019-10-05 22:47:46', '2020-05-01 17:22:46', 1, 'Q687154'),
(40131, 'Bons-en-Chablais', 4798, 'ARA', 75, 'FR', 46.26486000, 6.37129000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q687154'),
(40132, 'Bonson', 4798, 'ARA', 75, 'FR', 45.52291000, 4.21270000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q687154'),
(40133, 'Boofzheim', 4820, 'GES', 75, 'FR', 48.33186000, 7.68071000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q21248'),
(40134, 'Boos', 4804, 'NOR', 75, 'FR', 49.38849000, 1.20348000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q21248'),
(40135, 'Boran-sur-Oise', 4828, 'HDF', 75, 'FR', 49.16715000, 2.35803000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q675687'),
(40136, 'Bordeaux', 4795, 'NAQ', 75, 'FR', 44.84044000, -0.58050000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1479'),
(40137, 'Bordes', 4795, 'NAQ', 75, 'FR', 43.23333000, -0.28333000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1479'),
(40138, 'Bords', 4795, 'NAQ', 75, 'FR', 45.89722000, -0.79528000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1078534'),
(40139, 'Borel', 4812, 'PAC', 75, 'FR', 43.36486000, 5.36681000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1078534'),
(40140, 'Borgo', 4806, '20R', 75, 'FR', 42.55488000, 9.42636000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1078534'),
(40141, 'Bormes-les-Mimosas', 4812, 'PAC', 75, 'FR', 43.15169000, 6.34220000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1078534'),
(40142, 'Bornel', 4828, 'HDF', 75, 'FR', 49.19820000, 2.20912000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q961299'),
(40143, 'Bort-les-Orgues', 4795, 'NAQ', 75, 'FR', 45.39980000, 2.49579000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q203182'),
(40144, 'Bosc-le-Hard', 4804, 'NOR', 75, 'FR', 49.62734000, 1.17483000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q547569'),
(40145, 'Bosdarros', 4795, 'NAQ', 75, 'FR', 43.21667000, -0.36667000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q843372'),
(40146, 'Bosmie-l\'Aiguille', 4795, 'NAQ', 75, 'FR', 45.75000000, 1.20000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q843372'),
(40147, 'Bouafle', 4796, 'IDF', 75, 'FR', 48.96463000, 1.90120000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q399440'),
(40148, 'Bouaye', 4802, 'PDL', 75, 'FR', 47.14263000, -1.69306000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q764460'),
(40149, 'Bouc-Bel-Air', 4812, 'PAC', 75, 'FR', 43.45217000, 5.41300000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q117438'),
(40150, 'Boucau', 4795, 'NAQ', 75, 'FR', 43.52770000, -1.46556000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q320282'),
(40151, 'Bouchain', 4828, 'HDF', 75, 'FR', 50.28519000, 3.31491000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q320282'),
(40152, 'Bouchemaine', 4802, 'PDL', 75, 'FR', 47.42234000, -0.60888000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40153, 'Bouffémont', 4796, 'IDF', 75, 'FR', 49.04377000, 2.29796000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, '********'),
(40154, 'Boufféré', 4802, 'PDL', 75, 'FR', 46.96179000, -1.33965000, '2019-10-05 22:47:46', '2020-05-01 17:22:46', 1, 'Q580213'),
(40155, 'Bougival', 4796, 'IDF', 75, 'FR', 48.86223000, 2.14148000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q399342'),
(40156, 'Bouguenais', 4802, 'PDL', 75, 'FR', 47.17762000, -1.62143000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q291197'),
(40157, 'Bouillargues', 4799, 'OCC', 75, 'FR', 43.79733000, 4.42853000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q379793'),
(40158, 'Bouilly', 4820, 'GES', 75, 'FR', 48.19674000, 4.00011000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q379793'),
(40159, 'Bouillé-Loretz', 4795, 'NAQ', 75, 'FR', 47.07911000, -0.27178000, '2019-10-05 22:47:46', '2020-05-01 17:22:45', 1, '********'),
(40160, 'Bouin', 4802, 'PDL', 75, 'FR', 46.97314000, -1.99830000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40161, 'Boujan-sur-Libron', 4799, 'OCC', 75, 'FR', 43.36996000, 3.24759000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40162, 'Boulange', 4820, 'GES', 75, 'FR', 49.38229000, 5.95000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q22093'),
(40163, 'Boulay-Moselle', 4820, 'GES', 75, 'FR', 49.18333000, 6.50000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q22093'),
(40164, 'Boulazac', 4795, 'NAQ', 75, 'FR', 45.11667000, 0.75000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q22093'),
(40165, 'Boulbon', 4812, 'PAC', 75, 'FR', 43.86232000, 4.69391000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q22093'),
(40166, 'Bouleurs', 4796, 'IDF', 75, 'FR', 48.88181000, 2.90728000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q22093'),
(40167, 'Bouliac', 4795, 'NAQ', 75, 'FR', 44.81724000, -0.50248000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q254934'),
(40168, 'Boulieu-lès-Annonay', 4798, 'ARA', 75, 'FR', 45.27065000, 4.66645000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q254934'),
(40169, 'Bouligny', 4820, 'GES', 75, 'FR', 49.29189000, 5.74248000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q254934'),
(40170, 'Boulleret', 4818, 'CVL', 75, 'FR', 47.42419000, 2.87343000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q254934'),
(40171, 'Bouloc', 4799, 'OCC', 75, 'FR', 43.78163000, 1.40522000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q254934'),
(40172, 'Boulogne-Billancourt', 4796, 'IDF', 75, 'FR', 48.83545000, 2.24128000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q254934'),
(40173, 'Boulogne-sur-Gesse', 4799, 'OCC', 75, 'FR', 43.30000000, 0.65000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q689480'),
(40174, 'Boulogne-sur-Mer', 4828, 'HDF', 75, 'FR', 50.72571000, 1.61392000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q689480'),
(40175, 'Bouloire', 4802, 'PDL', 75, 'FR', 47.97385000, 0.55009000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q689480'),
(40176, 'Boult-sur-Suippe', 4820, 'GES', 75, 'FR', 49.37149000, 4.14632000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40177, 'Bouray-sur-Juine', 4796, 'IDF', 75, 'FR', 48.51981000, 2.30001000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q248375'),
(40178, 'Bourbon-Lancy', 4825, 'BFC', 75, 'FR', 46.62214000, 3.76953000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q829308'),
(40179, 'Bourbon-l’Archambault', 4798, 'ARA', 75, 'FR', 46.58324000, 3.05652000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q245080'),
(40180, 'Bourbonne-les-Bains', 4820, 'GES', 75, 'FR', 47.95305000, 5.74801000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q627061'),
(40181, 'Bourbourg', 4828, 'HDF', 75, 'FR', 50.94783000, 2.19576000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q632822'),
(40182, 'Bourbriac', 4807, 'BRE', 75, 'FR', 48.47384000, -3.18758000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q841084'),
(40183, 'Bourcefranc-le-Chapus', 4795, 'NAQ', 75, 'FR', 45.85000000, -1.15000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40184, 'Bourg', 4795, 'NAQ', 75, 'FR', 45.04062000, -0.55893000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40185, 'Bourg de Joué-sur-Erdre', 4802, 'PDL', 75, 'FR', 47.49596000, -1.42047000, '2019-10-05 22:47:46', '2020-05-01 17:22:46', 1, 'Q304553'),
(40186, 'Bourg-Achard', 4804, 'NOR', 75, 'FR', 49.35322000, 0.81623000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q304553'),
(40187, 'Bourg-Argental', 4798, 'ARA', 75, 'FR', 45.29899000, 4.56830000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q304553'),
(40188, 'Bourg-Blanc', 4807, 'BRE', 75, 'FR', 48.49867000, -4.50406000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q304553'),
(40189, 'Bourg-Saint-Andéol', 4798, 'ARA', 75, 'FR', 44.37338000, 4.64413000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q304553'),
(40190, 'Bourg-Saint-Maurice', 4798, 'ARA', 75, 'FR', 45.61463000, 6.76845000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q304553'),
(40191, 'Bourg-de-Péage', 4798, 'ARA', 75, 'FR', 45.03151000, 5.04993000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q240363'),
(40192, 'Bourg-de-Thizy', 4798, 'ARA', 75, 'FR', 46.03371000, 4.29904000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1364098'),
(40193, 'Bourg-des-Comptes', 4807, 'BRE', 75, 'FR', 47.92934000, -1.74534000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1364098'),
(40194, 'Bourg-en-Bresse', 4798, 'ARA', 75, 'FR', 46.20574000, 5.22580000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1364098'),
(40195, 'Bourg-la-Reine', 4796, 'IDF', 75, 'FR', 48.77888000, 2.31781000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1364098'),
(40196, 'Bourg-lès-Valence', 4798, 'ARA', 75, 'FR', 44.94703000, 4.89463000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q1364098'),
(40197, 'Bourganeuf', 4795, 'NAQ', 75, 'FR', 45.95268000, 1.75520000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q199638'),
(40198, 'Bourgbarré', 4807, 'BRE', 75, 'FR', 47.99515000, -1.61419000, '2019-10-05 22:47:46', '2020-05-01 17:22:44', 1, 'Q199638'),
(40199, 'Bourges', 4818, 'CVL', 75, 'FR', 47.08333000, 2.40000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q132404'),
(40200, 'Bourghelles', 4828, 'HDF', 75, 'FR', 50.56501000, 3.24447000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q132404'),
(40201, 'Bourgneuf', 4795, 'NAQ', 75, 'FR', 46.16766000, -1.02141000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q132404'),
(40202, 'Bourgneuf-en-Retz', 4802, 'PDL', 75, 'FR', 47.04122000, -1.95019000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q132404'),
(40203, 'Bourgogne', 4820, 'GES', 75, 'FR', 49.34962000, 4.07111000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q132404'),
(40204, 'Bourgoin-Jallieu', 4798, 'ARA', 75, 'FR', 45.58611000, 5.27361000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q208893'),
(40205, 'Bourgtheroulde-Infreville', 4804, 'NOR', 75, 'FR', 49.30000000, 0.88333000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q379512'),
(40206, 'Bourgueil', 4818, 'CVL', 75, 'FR', 47.28333000, 0.16612000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q504875'),
(40207, 'Bourguébus', 4804, 'NOR', 75, 'FR', 49.12187000, -0.29786000, '2019-10-05 22:47:46', '2020-05-01 17:22:45', 1, 'Q504875'),
(40208, 'Bourlon', 4828, 'HDF', 75, 'FR', 50.17692000, 3.11425000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q994971'),
(40209, 'Bournezeau', 4802, 'PDL', 75, 'FR', 46.63714000, -1.17107000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40210, 'Bournoncle-Saint-Pierre', 4798, 'ARA', 75, 'FR', 45.34351000, 3.31830000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40211, 'Bourogne', 4825, 'BFC', 75, 'FR', 47.56307000, 6.91654000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40212, 'Bourron-Marlotte', 4796, 'IDF', 75, 'FR', 48.34051000, 2.70041000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q272084'),
(40213, 'Bourth', 4804, 'NOR', 75, 'FR', 48.76846000, 0.80911000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q272084'),
(40214, 'Bousbecque', 4828, 'HDF', 75, 'FR', 50.77123000, 3.08459000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40215, 'Bousies', 4828, 'HDF', 75, 'FR', 50.15097000, 3.61752000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q632387'),
(40216, 'Boussac', 4795, 'NAQ', 75, 'FR', 46.34941000, 2.21474000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q632387'),
(40217, 'Boussay', 4802, 'PDL', 75, 'FR', 47.04476000, -1.18476000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q632387'),
(40218, 'Bousse', 4820, 'GES', 75, 'FR', 49.27786000, 6.19672000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q632387'),
(40219, 'Boussières', 4825, 'BFC', 75, 'FR', 47.15866000, 5.90314000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q632387'),
(40220, 'Boussois', 4828, 'HDF', 75, 'FR', 50.28907000, 4.04117000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q683059'),
(40221, 'Boussy-Saint-Antoine', 4796, 'IDF', 75, 'FR', 48.69101000, 2.53060000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q671091'),
(40222, 'Bout-du-Pont-de-Larn', 4799, 'OCC', 75, 'FR', 43.49738000, 2.41642000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q671091'),
(40223, 'Boutiers-Saint-Trojan', 4795, 'NAQ', 75, 'FR', 45.71667000, -0.30000000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40224, 'Boutigny-sur-Essonne', 4796, 'IDF', 75, 'FR', 48.43333000, 2.38333000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40225, 'Bouttencourt', 4828, 'HDF', 75, 'FR', 49.93725000, 1.63431000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40226, 'Bouvesse-Quirieu', 4798, 'ARA', 75, 'FR', 45.79310000, 5.41496000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40227, 'Bouvignies', 4828, 'HDF', 75, 'FR', 50.43627000, 3.24361000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40228, 'Bouvigny-Boyeffles', 4828, 'HDF', 75, 'FR', 50.42146000, 2.67209000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40229, 'Bouville', 4804, 'NOR', 75, 'FR', 49.56193000, 0.89514000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40230, 'Bouvron', 4802, 'PDL', 75, 'FR', 47.41726000, -1.84679000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40231, 'Bouxières-aux-Chênes', 4820, 'GES', 75, 'FR', 48.77215000, 6.26152000, '2019-10-05 22:47:46', '2020-05-01 17:22:44', 1, '********'),
(40232, 'Bouxières-aux-Dames', 4820, 'GES', 75, 'FR', 48.75441000, 6.16294000, '2019-10-05 22:47:46', '2020-05-01 17:22:44', 1, '********'),
(40233, 'Bouxwiller', 4820, 'GES', 75, 'FR', 48.82495000, 7.48117000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40234, 'Bouzigues', 4799, 'OCC', 75, 'FR', 43.44810000, 3.65781000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40235, 'Bouzillé', 4802, 'PDL', 75, 'FR', 47.33750000, -1.11143000, '2019-10-05 22:47:46', '2020-05-01 17:22:46', 1, '********'),
(40236, 'Bouzonville', 4820, 'GES', 75, 'FR', 49.29188000, 6.53386000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40237, 'Bouzy-la-Forêt', 4818, 'CVL', 75, 'FR', 47.85120000, 2.37773000, '2019-10-05 22:47:46', '2020-05-01 17:22:44', 1, '********'),
(40238, 'Boué', 4828, 'HDF', 75, 'FR', 50.01141000, 3.69608000, '2019-10-05 22:47:46', '2020-05-01 17:22:45', 1, '********'),
(40239, 'Boves', 4828, 'HDF', 75, 'FR', 49.84645000, 2.39605000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40240, 'Boyard-Ville', 4795, 'NAQ', 75, 'FR', 45.96717000, -1.24289000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40241, 'Boynes', 4818, 'CVL', 75, 'FR', 48.11822000, 2.36006000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, '********'),
(40242, 'Bozel', 4798, 'ARA', 75, 'FR', 45.44288000, 6.64896000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1242293'),
(40243, 'Bozouls', 4799, 'OCC', 75, 'FR', 44.47050000, 2.72432000, '2019-10-05 22:47:46', '2019-10-05 22:47:46', 1, 'Q1242293'),
(40244, 'Boé', 4795, 'NAQ', 75, 'FR', 44.16007000, 0.62905000, '2019-10-05 22:47:46', '2020-05-01 17:22:45', 1, 'Q627217'),
(40245, 'Boëge', 4798, 'ARA', 75, 'FR', 46.20711000, 6.40428000, '2019-10-05 22:47:46', '2020-05-01 17:22:43', 1, 'Q627217'),
(40246, 'Boën-sur-Lignon', 4798, 'ARA', 75, 'FR', 45.75114000, 4.00725000, '2019-10-05 22:47:47', '2020-05-01 17:22:43', 1, 'Q627217'),
(40247, 'Bracieux', 4818, 'CVL', 75, 'FR', 47.54895000, 1.54120000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40248, 'Brain-sur-Allonnes', 4802, 'PDL', 75, 'FR', 47.30325000, 0.06514000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40249, 'Braine', 4828, 'HDF', 75, 'FR', 49.34261000, 3.53262000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40250, 'Brains', 4802, 'PDL', 75, 'FR', 47.16850000, -1.72290000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40251, 'Bram', 4799, 'OCC', 75, 'FR', 43.24376000, 2.11341000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40252, 'Brando', 4806, '20R', 75, 'FR', 42.76667000, 9.45000000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40253, 'Brandérion', 4807, 'BRE', 75, 'FR', 47.79412000, -3.19474000, '2019-10-05 22:47:47', '2020-05-01 17:22:44', 1, 'Q62863'),
(40254, 'Branges', 4825, 'BFC', 75, 'FR', 46.64441000, 5.18465000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q62863'),
(40255, 'Branne', 4795, 'NAQ', 75, 'FR', 44.83333000, -0.18333000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q62863'),
(40256, 'Branoux-les-Taillades', 4799, 'OCC', 75, 'FR', 44.21941000, 3.99647000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q266840'),
(40257, 'Brantôme', 4795, 'NAQ', 75, 'FR', 45.36091000, 0.65398000, '2019-10-05 22:47:47', '2020-05-01 17:22:45', 1, 'Q266840'),
(40258, 'Bras', 4812, 'PAC', 75, 'FR', 43.47163000, 5.95486000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q266840'),
(40259, 'Brasles', 4828, 'HDF', 75, 'FR', 49.04810000, 3.43000000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q266840'),
(40260, 'Brasparts', 4807, 'BRE', 75, 'FR', 48.30167000, -3.95516000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q221894'),
(40261, 'Brassac', 4799, 'OCC', 75, 'FR', 43.62959000, 2.49763000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q221894'),
(40262, 'Brassac-les-Mines', 4798, 'ARA', 75, 'FR', 45.41407000, 3.32900000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q221894'),
(40263, 'Braud-et-Saint-Louis', 4795, 'NAQ', 75, 'FR', 45.24703000, -0.62438000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q221894'),
(40264, 'Brax', 4799, 'OCC', 75, 'FR', 43.61793000, 1.23957000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q221894'),
(40265, 'Brax', 4795, 'NAQ', 75, 'FR', 44.20277000, 0.55163000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q221894'),
(40266, 'Bray-Dunes', 4828, 'HDF', 75, 'FR', 51.07786000, 2.51673000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q391365'),
(40267, 'Bray-en-Val', 4818, 'CVL', 75, 'FR', 47.82856000, 2.36644000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q391365'),
(40268, 'Bray-sur-Seine', 4796, 'IDF', 75, 'FR', 48.41371000, 3.23852000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q834110'),
(40269, 'Bray-sur-Somme', 4828, 'HDF', 75, 'FR', 49.94085000, 2.71847000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q251119'),
(40270, 'Brazey-en-Plaine', 4825, 'BFC', 75, 'FR', 47.13784000, 5.21538000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q251119'),
(40271, 'Brebières', 4828, 'HDF', 75, 'FR', 50.33333000, 3.01667000, '2019-10-05 22:47:47', '2020-05-01 17:22:45', 1, 'Q850917'),
(40272, 'Brech', 4807, 'BRE', 75, 'FR', 47.72134000, -2.99862000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q62806'),
(40273, 'Breil-sur-Roya', 4812, 'PAC', 75, 'FR', 43.93755000, 7.51472000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q321111'),
(40274, 'Brenouille', 4828, 'HDF', 75, 'FR', 49.30551000, 2.54437000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q1106709'),
(40275, 'Brens', 4799, 'OCC', 75, 'FR', 43.88725000, 1.90716000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q1106709'),
(40276, 'Bresles', 4828, 'HDF', 75, 'FR', 49.41044000, 2.25024000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q1106709'),
(40277, 'Bressols', 4799, 'OCC', 75, 'FR', 43.96796000, 1.33839000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q1106709'),
(40278, 'Bressuire', 4795, 'NAQ', 75, 'FR', 46.84012000, -0.48851000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q210760'),
(40279, 'Brest', 4807, 'BRE', 75, 'FR', 48.39029000, -4.48628000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q12193'),
(40280, 'Breteil', 4807, 'BRE', 75, 'FR', 48.14534000, -1.89886000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q210664'),
(40281, 'Bretenoux', 4799, 'OCC', 75, 'FR', 44.91468000, 1.84007000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40282, 'Breteuil', 4804, 'NOR', 75, 'FR', 48.83333000, 0.91667000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40283, 'Breteuil', 4828, 'HDF', 75, 'FR', 49.63357000, 2.29509000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40284, 'Bretignolles-sur-Mer', 4802, 'PDL', 75, 'FR', 46.63333000, -1.86667000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40285, 'Bretoncelles', 4804, 'NOR', 75, 'FR', 48.43122000, 0.88775000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40286, 'Brette-les-Pins', 4802, 'PDL', 75, 'FR', 47.91062000, 0.33649000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40287, 'Bretteville', 4804, 'NOR', 75, 'FR', 49.65440000, -1.52884000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40288, 'Bretteville-du-Grand-Caux', 4804, 'NOR', 75, 'FR', 49.66667000, 0.40000000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, '********'),
(40289, 'Bretteville-l’Orgueilleuse', 4804, 'NOR', 75, 'FR', 49.21189000, -0.51428000, '2019-10-05 22:47:47', '2020-05-01 17:22:45', 1, '********'),
(40290, 'Bretteville-sur-Laize', 4804, 'NOR', 75, 'FR', 49.04466000, -0.32639000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q309121'),
(40291, 'Bretteville-sur-Odon', 4804, 'NOR', 75, 'FR', 49.16627000, -0.41662000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q326435'),
(40292, 'Breuil-Magné', 4795, 'NAQ', 75, 'FR', 45.98478000, -0.96000000, '2019-10-05 22:47:47', '2020-05-01 17:22:45', 1, '********'),
(40293, 'Breuil-le-Sec', 4828, 'HDF', 75, 'FR', 49.37135000, 2.45084000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q1346647'),
(40294, 'Breuil-le-Vert', 4828, 'HDF', 75, 'FR', 49.36176000, 2.43633000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q943707'),
(40295, 'Breuillet', 4795, 'NAQ', 75, 'FR', 45.69143000, -1.05175000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q943707'),
(40296, 'Breuillet', 4796, 'IDF', 75, 'FR', 48.57064000, 2.17424000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q943707'),
(40297, 'Breuilpont', 4804, 'NOR', 75, 'FR', 48.96398000, 1.42919000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q266242'),
(40298, 'Breuschwickersheim', 4820, 'GES', 75, 'FR', 48.58007000, 7.60159000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q266242'),
(40299, 'Brezolles', 4818, 'CVL', 75, 'FR', 48.69033000, 1.07404000, '2019-10-05 22:47:47', '2019-10-05 22:47:47', 1, 'Q1011314');

