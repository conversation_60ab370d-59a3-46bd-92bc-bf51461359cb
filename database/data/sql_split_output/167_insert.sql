INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(84960, 'Talaban', 1336, 'ANT', 174, 'PH', 10.12540000, 122.86710000, '2019-10-05 23:13:27', '2019-10-05 23:13:27', 1, 'Q31543149'),
(84961, 'Talacogon', 1314, '13', 174, 'PH', 8.45611000, 125.78417000, '2019-10-05 23:13:27', '2019-10-05 23:13:27', 1, 'Q31543149'),
(84962, 'Talaga', 1345, '03', 174, 'PH', 15.38403000, 120.58933000, '2019-10-05 23:13:27', '2019-10-05 23:13:27', 1, 'Q31543215'),
(84963, 'Talaga', 1356, 'MDC', 174, 'PH', 13.73646000, 120.93618000, '2019-10-05 23:13:27', '2019-10-05 23:13:27', 1, 'Q31543198'),
(84964, '<PERSON>lagutong', 1340, '11', 174, 'PH', 6.26444000, 125.66778000, '2019-10-05 23:13:27', '2019-10-05 23:13:27', 1, 'Q314796'),
(84965, 'Talahib Payap', 1356, 'MDC', 174, 'PH', 13.66083000, 121.13639000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543280'),
(84966, 'Talahiban I', 1356, 'MDC', 174, 'PH', 13.80645000, 121.38116000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543280'),
(84967, 'Talaibon', 1356, 'MDC', 174, 'PH', 13.83780000, 121.13980000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543312'),
(84968, 'Talakag', 1363, 'BEN', 174, 'PH', 8.23361000, 124.60028000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q357420'),
(84969, 'Talalora', 5115, 'WSA', 174, 'PH', 11.50911890, 124.80472930, '2025-03-26 17:30:47', '2025-03-26 17:30:47', 1, 'Q816519'),
(84970, 'Talang', 1345, '03', 174, 'PH', 15.02488000, 120.83782000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543442'),
(84971, 'Talangnan', 1344, 'BAN', 174, 'PH', 9.63400000, 123.33440000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543461'),
(84974, 'Talibon', 1344, 'BAN', 174, 'PH', 10.14917000, 124.32500000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q212823'),
(84975, 'Taligaman', 1314, '13', 174, 'PH', 8.90444000, 125.65583000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543623'),
(84976, 'Talipan', 1356, 'MDC', 174, 'PH', 13.93333000, 121.68333000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543685'),
(84978, 'Talipaw', 1316, '14', 174, 'PH', 5.91000000, 121.09444000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31543717'),
(84979, 'Talisay', 1336, 'ANT', 174, 'PH', 10.73750000, 122.96660000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q1027120'),
(84980, 'Talisay', 1337, 'ALB', 174, 'PH', 14.13430000, 122.92260000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q356878'),
(84981, 'Talisay', 1344, 'BAN', 174, 'PH', 10.24472000, 123.84944000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q316500'),
(84984, 'Talisay', 1359, 'BTG', 174, 'PH', 14.07719980, 120.92668130, '2025-03-20 15:57:29', '2025-03-20 15:57:29', 1, 'Q59825'),
(84985, 'Talisayan', 1348, 'MSR', 174, 'PH', 8.94746290, 124.82674200, '2025-03-24 20:55:51', '2025-03-24 20:55:51', 1, 'Q195917'),
(84988, 'Talitay', 1330, 'MGN', 174, 'PH', 7.03210120, 124.30333570, '2025-03-24 17:12:14', '2025-03-24 17:12:14', 1, 'Q212830'),
(84989, 'Taloc', 1336, 'ANT', 174, 'PH', 10.57460000, 122.89540000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544366'),
(84990, 'Talokgañgan', 1336, 'ANT', 174, 'PH', 11.00778000, 122.84500000, '2019-10-05 23:13:28', '2020-05-01 17:23:05', 1, 'Q31544378'),
(84991, 'Talomo', 1340, '11', 174, 'PH', 7.52861000, 125.72278000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544394'),
(84992, 'Talon', 1336, 'ANT', 174, 'PH', 11.56000000, 122.68111000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544426'),
(84993, 'Talospatang', 1355, '01', 174, 'PH', 15.91701000, 120.44141000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544443'),
(84994, 'Taloy', 1335, '15', 174, 'PH', 16.33333000, 120.50000000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544478'),
(84995, 'Taloy', 1355, '01', 174, 'PH', 15.92410000, 120.39649000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544495'),
(84996, 'Taltal', 1345, '03', 174, 'PH', 15.58780000, 119.94550000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544528'),
(84997, 'Talubatib', 1337, 'ALB', 174, 'PH', 14.18250000, 122.78530000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544543'),
(84999, 'Taluksangay', 1362, 'ZSI', 174, 'PH', 6.95417000, 122.18389000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544577'),
(85000, 'Talusan', 1362, 'ZSI', 174, 'PH', 7.42667000, 122.81028000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q131968'),
(85001, 'Taluya', 1310, '12', 174, 'PH', 5.81167000, 125.17278000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544652'),
(85002, 'Tamayo', 1355, '01', 174, 'PH', 15.87746000, 120.28455000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544686'),
(85003, 'Tamayong', 1340, '11', 174, 'PH', 7.13306000, 125.37972000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544703'),
(85004, 'Tambac', 1336, 'ANT', 174, 'PH', 11.67050000, 122.41120000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544718'),
(85005, 'Tambak', 1310, '12', 174, 'PH', 6.72532000, 124.62342000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544765'),
(85006, 'Tambalan', 1344, 'BAN', 174, 'PH', 9.90850000, 123.08960000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544795'),
(85007, 'Tambalisa', 1336, 'ANT', 174, 'PH', 11.26980000, 123.16500000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544809'),
(85008, 'Tambilil', 1310, '12', 174, 'PH', 5.96222000, 124.66972000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544856'),
(85009, 'Tambo', 1344, 'BAN', 174, 'PH', 9.89500000, 123.05560000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544900'),
(85010, 'Tambo', 1337, 'ALB', 174, 'PH', 13.54680000, 123.03580000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544885'),
(85011, 'Tambo', 1340, '11', 174, 'PH', 7.15694000, 125.69917000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544915'),
(85012, 'Tambong', 1354, 'MDR', 174, 'PH', 12.96391000, 121.48462000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544962'),
(85013, 'Tambongon', 1344, 'BAN', 174, 'PH', 10.96770000, 123.92440000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31544976'),
(85014, 'Tambulig', 1357, 'ZAS', 174, 'PH', 8.07715230, 123.49696700, '2025-03-26 19:38:09', '2025-03-26 19:38:09', 1, 'Q132391'),
(85015, 'Tamisan', 1340, '11', 174, 'PH', 6.84372000, 126.29838000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545031'),
(85016, 'Tamiso', 1344, 'BAN', 174, 'PH', 9.63140000, 123.08730000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545061'),
(85017, 'Tamlang', 1336, 'ANT', 174, 'PH', 10.82544000, 123.45410000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545076'),
(85018, 'Tamnag', 1310, '12', 174, 'PH', 6.57298000, 124.88004000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545092'),
(85019, 'Tamontaka', 1310, '12', 174, 'PH', 7.18056000, 124.22556000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545092'),
(85020, 'Tamorong', 1355, '01', 174, 'PH', 17.33333000, 120.43333000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545153'),
(85024, 'Tampayan', 1354, 'MDR', 174, 'PH', 12.49411000, 122.53309000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q274087'),
(85025, 'Tampilisan', 1302, 'ZAN', 174, 'PH', 7.97363090, 122.59574480, '2025-03-26 18:53:26', '2025-03-26 18:53:26', 1, 'Q132617'),
(85026, 'Tampocon', 1344, 'BAN', 174, 'PH', 9.85000000, 123.13333000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545284'),
(85027, 'Tamugan', 1340, '11', 174, 'PH', 7.23083000, 125.37639000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545301'),
(85028, 'Tanauan', 1359, 'BTG', 174, 'PH', 14.09139490, 121.02628790, '2025-03-20 15:59:51', '2025-03-20 15:59:51', 1, 'Q1730'),
(85030, 'Tanay', 1271, 'RIZ', 174, 'PH', 14.57779400, 121.19182200, '2025-03-25 20:52:01', '2025-03-25 20:52:01', 1, 'Q106839'),
(85031, 'Tandag', 1296, 'SUR', 174, 'PH', 9.09298150, 125.96729090, '2025-03-26 16:21:41', '2025-03-26 16:21:41', 1, 'Q155674'),
(85032, 'Tandayag', 1344, 'BAN', 174, 'PH', 9.45470000, 123.22990000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545462'),
(85033, 'Tandoc', 1355, '01', 174, 'PH', 15.95929000, 120.32914000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545494'),
(85034, 'Tangal', 1354, 'MDR', 174, 'PH', 13.84536000, 120.10082000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545546'),
(85035, 'Tangalan', 1331, 'AKL', 174, 'PH', 11.73931560, 122.16096950, '2019-10-05 23:13:28', '2025-03-17 13:02:19', 1, 'Q627028'),
(85036, 'Tangke', 1344, 'BAN', 174, 'PH', 10.25451000, 123.86516000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545990'),
(85037, 'Tangnan', 1344, 'BAN', 174, 'PH', 9.60722000, 123.77357000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545722'),
(85038, 'Tangub', 1343, 'MSC', 174, 'PH', 8.10038770, 123.62346010, '2025-03-24 20:20:00', '2025-03-24 20:20:00', 1, 'Q1026277'),
(85039, 'Tanjay', 1350, 'NER', 174, 'PH', 9.50451240, 122.87459670, '2025-03-25 13:20:13', '2025-03-25 13:20:13', 1, 'Q1020705'),
(85040, 'Tanlad', 1340, '11', 174, 'PH', 6.60222000, 125.44500000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545855'),
(85041, 'Tanolong', 1355, '01', 174, 'PH', 15.80411000, 120.41409000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31545973'),
(85044, 'Tanza', 1307, 'CAV', 174, 'PH', 14.33946930, 120.74556360, '2025-03-19 18:46:52', '2025-03-19 18:46:52', 1, 'Q63115'),
(85047, 'Tapas', 1336, 'ANT', 174, 'PH', 11.26130000, 122.53640000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q356398'),
(85048, 'Tapayan', 1316, '14', 174, 'PH', 7.29444000, 124.26611000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546273'),
(85049, 'Tapel', 1342, '02', 174, 'PH', 18.28907000, 122.02915000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546308'),
(85050, 'Tapia', 1340, '11', 174, 'PH', 7.75417000, 126.01194000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546325'),
(85051, 'Tapikan', 1316, '14', 174, 'PH', 6.85194000, 124.32250000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546392'),
(85052, 'Tapilon', 1344, 'BAN', 174, 'PH', 11.27740000, 124.03060000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546409'),
(85053, 'Tapon', 1344, 'BAN', 174, 'PH', 10.06310000, 123.44530000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546447'),
(85054, 'Tara', 1337, 'ALB', 174, 'PH', 13.81667000, 122.98333000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546587'),
(85055, 'Tarangnan', 5115, 'WSA', 174, 'PH', 11.90118670, 124.57704840, '2025-03-26 17:31:46', '2025-03-26 17:31:46', 1, 'Q816542'),
(85056, 'Tariji', 1345, '03', 174, 'PH', 15.52112000, 120.61442000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546744'),
(85057, 'Tariric', 1337, 'ALB', 174, 'PH', 13.57230000, 123.22350000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546761'),
(85058, 'Tarlac City', 1295, 'TAR', 174, 'PH', 15.47274830, 120.53060610, '2025-03-26 16:41:23', '2025-03-26 16:41:23', 1, 'Q5285'),
(85059, 'Tarong', 1336, 'ANT', 174, 'PH', 11.53140000, 123.13200000, '2019-10-05 23:13:28', '2019-10-05 23:13:28', 1, 'Q31546844'),
(85061, 'Tartaro', 1345, '03', 174, 'PH', 15.16667000, 121.01667000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q314745'),
(85062, 'Tarusan', 1354, 'MDR', 174, 'PH', 8.65461000, 117.51040000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31546986'),
(85063, 'Taungoh', 1316, '14', 174, 'PH', 4.70654000, 119.49296000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31546986'),
(85064, 'Taviran', 1316, '14', 174, 'PH', 7.12333000, 124.31972000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31547601'),
(85065, 'Tawagan', 1362, 'ZSI', 174, 'PH', 7.84500000, 123.47528000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31547601'),
(85066, 'Tawala', 1344, 'BAN', 174, 'PH', 9.55421000, 123.76955000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31547652'),
(85067, 'Tawan tawan', 1340, '11', 174, 'PH', 7.16917000, 125.37278000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31547669'),
(85068, 'Tayabas', 1275, 'QUE', 174, 'PH', 14.02985140, 121.45854060, '2025-03-25 20:27:21', '2025-03-25 20:27:21', 1, 'Q104113'),
(85069, 'Tayabas Ibaba', 1356, 'MDC', 174, 'PH', 13.62220000, 122.28140000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31547790'),
(85070, 'Tayabo', 1345, '03', 174, 'PH', 15.83450000, 121.03090000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31547807'),
(85071, 'Tayaman', 1354, 'MDR', 174, 'PH', 13.22950000, 120.57260000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31547824'),
(85072, 'Tayasan', 1350, 'NER', 174, 'PH', 9.95619580, 123.00683100, '2025-03-25 13:21:07', '2025-03-25 13:21:07', 1, 'Q195121'),
(85073, 'Taypano', 1363, 'BEN', 174, 'PH', 8.37083000, 124.55944000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q195121'),
(85074, 'Taysan', 1359, 'BTG', 174, 'PH', 13.75887780, 121.14322940, '2025-03-20 16:01:21', '2025-03-20 16:01:21', 1, 'Q59830'),
(85077, 'Taytay', 1361, 'PLW', 174, 'PH', 10.86069400, 119.15181280, '2025-03-25 16:45:08', '2025-03-25 16:45:08', 1, 'Q111730'),
(85078, 'Taytayan', 1340, '11', 174, 'PH', 7.73389000, 126.50194000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31548727'),
(85079, 'Taytayan', 1344, 'BAN', 174, 'PH', 10.87870000, 123.97710000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31548709'),
(85080, 'Tayud', 1344, 'BAN', 174, 'PH', 10.37991000, 124.00522000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31548744'),
(85082, 'Tayum', 1335, '15', 174, 'PH', 17.61720000, 120.65420000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q43148'),
(85083, 'Taywanak Ilaya', 1356, 'MDC', 174, 'PH', 14.15759000, 120.84655000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31548772'),
(85084, 'Tañgo', 1310, '12', 174, 'PH', 5.88083000, 125.22194000, '2019-10-05 23:13:29', '2020-05-01 17:23:06', 1, 'Q31548799'),
(85085, 'Tebag East', 1355, '01', 174, 'PH', 15.98123000, 120.46740000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31549005'),
(85086, 'Telabastagan', 1345, '03', 174, 'PH', 15.11854000, 120.60773000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31549292'),
(85087, 'Telafas', 1310, '12', 174, 'PH', 6.62980000, 124.97294000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31549309'),
(85088, 'Telbang', 1355, '01', 174, 'PH', 16.17241000, 120.06074000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31549342'),
(85089, 'Teresa', 1271, 'RIZ', 174, 'PH', 14.56702670, 121.18751310, '2025-03-25 20:53:51', '2025-03-25 20:53:51', 1, 'Q106848'),
(85090, 'Teresita', 1310, '12', 174, 'PH', 6.41434000, 124.70778000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31549942'),
(85091, 'Ternate', 1307, 'CAV', 174, 'PH', 14.26915070, 120.60185510, '2025-03-19 18:48:36', '2025-03-19 18:48:36', 1, 'Q63124'),
(85092, 'Tiaong', 1275, 'QUE', 174, 'PH', 13.91961930, 121.25294560, '2025-03-25 20:28:10', '2025-03-25 20:28:10', 1, 'Q104092'),
(85093, 'Tibagon', 1340, '11', 174, 'PH', 7.23270000, 125.86280000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555005'),
(85094, 'Tibanbang', 1340, '11', 174, 'PH', 6.63153000, 126.10548000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555021'),
(85095, 'Tibiao', 1336, 'ANT', 174, 'PH', 11.29152000, 122.03541000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q492780'),
(85096, 'Tibigan', 1344, 'BAN', 174, 'PH', 9.95180000, 123.96220000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555104'),
(85097, 'Tiblawan', 1340, '11', 174, 'PH', 6.47834000, 126.10828000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555120'),
(85098, 'Ticala-an', 1363, 'BEN', 174, 'PH', 8.03528000, 124.62083000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555175'),
(85099, 'Tidman', 1314, '13', 174, 'PH', 8.29861000, 126.33889000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555326'),
(85100, 'Tiep', 1355, '01', 174, 'PH', 16.23820000, 119.86090000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555457'),
(85101, 'Tigao', 1314, '13', 174, 'PH', 9.20889000, 126.17611000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555536'),
(85102, 'Tigaon', 1287, 'CAS', 174, 'PH', 13.62027780, 123.37474420, '2019-10-05 23:13:29', '2025-03-17 18:53:59', 1, 'Q209011'),
(85103, 'Tigbao', 1357, 'ZAS', 174, 'PH', 7.84589690, 123.17655840, '2025-03-26 19:39:39', '2025-03-26 19:39:39', 1, 'Q132403'),
(85105, 'Tigbauan', 1315, 'ILI', 174, 'PH', 10.71660410, 122.33398570, '2025-03-22 16:45:42', '2025-03-22 16:45:42', 1, 'Q82497'),
(85106, 'Tigbaw', 1337, 'ALB', 174, 'PH', 12.14830000, 123.59090000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555670'),
(85107, 'Tigbinan', 1337, 'ALB', 174, 'PH', 14.18648000, 122.46923000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555687'),
(85108, 'Tiglauigan', 1336, 'ANT', 174, 'PH', 10.95270000, 123.35750000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555844'),
(85109, 'Tignapalan', 1363, 'BEN', 174, 'PH', 8.26389000, 124.57472000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555844'),
(85110, 'Tignoan', 1356, 'MDC', 174, 'PH', 14.56522000, 121.61786000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555881'),
(85111, 'Tigpalay', 1362, 'ZSI', 174, 'PH', 7.49479000, 122.34496000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555897'),
(85112, 'Tigtabon', 1362, 'ZSI', 174, 'PH', 6.89556000, 122.16694000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555913'),
(85113, 'Tiguha', 1362, 'ZSI', 174, 'PH', 7.70444000, 123.21139000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555929'),
(85114, 'Tigui', 1354, 'MDR', 174, 'PH', 13.26810000, 122.04150000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555945'),
(85115, 'Tiguib', 1344, 'BAN', 174, 'PH', 9.80080000, 123.13520000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555961'),
(85116, 'Tiguion', 1354, 'MDR', 174, 'PH', 13.33470000, 121.86100000, '2019-10-05 23:13:29', '2019-10-05 23:13:29', 1, 'Q31555978'),
(85117, 'Tiguisan', 1354, 'MDR', 174, 'PH', 12.83941000, 121.46052000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31555994'),
(85118, 'Tigum', 1336, 'ANT', 174, 'PH', 10.78333000, 122.56667000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556010'),
(85119, 'Tikiw', 1345, '03', 174, 'PH', 15.31193000, 120.86116000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556107'),
(85120, 'Tilik', 1354, 'MDR', 174, 'PH', 13.81436000, 120.20022000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556141'),
(85121, 'Tiling', 1336, 'ANT', 174, 'PH', 9.97360000, 122.65440000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556148'),
(85122, 'Timonan', 1362, 'ZSI', 174, 'PH', 8.21170000, 123.06820000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556509'),
(85123, 'Timpas', 1336, 'ANT', 174, 'PH', 11.47900000, 122.73240000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556529'),
(85124, 'Tinaan', 1344, 'BAN', 174, 'PH', 11.26667000, 123.75000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556580'),
(85125, 'Tinagacan', 1310, '12', 174, 'PH', 6.19917000, 125.22944000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556580'),
(85126, 'Tinago', 1337, 'ALB', 174, 'PH', 13.18333000, 123.65000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556613'),
(85127, 'Tinalmud', 1337, 'ALB', 174, 'PH', 13.59410000, 122.87800000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556713'),
(85128, 'Tinambac', 1287, 'CAS', 174, 'PH', 13.86395910, 123.18830790, '2019-10-05 23:13:30', '2025-03-17 18:55:10', 1, 'Q209024'),
(85129, 'Tinambacan', 1352, 'BTN', 174, 'PH', 12.09430000, 124.50250000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556764'),
(85130, 'Tinampo', 1337, 'ALB', 174, 'PH', 13.22825000, 123.50723000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556797'),
(85131, 'Tinang', 1345, '03', 174, 'PH', 15.37626000, 120.65343000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556797'),
(85132, 'Tinaogan', 1344, 'BAN', 174, 'PH', 9.77910000, 123.15030000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556882'),
(85133, 'Tinawagan', 1337, 'ALB', 174, 'PH', 13.65000000, 123.48333000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556914'),
(85134, 'Tindog', 1344, 'BAN', 174, 'PH', 11.12470000, 124.01180000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31556979'),
(85136, 'Tingloy', 1359, 'BTG', 174, 'PH', 13.66181700, 120.81492970, '2025-03-20 16:02:44', '2025-03-20 16:02:44', 1, 'Q59835'),
(85137, 'Tiniguiban', 1354, 'MDR', 174, 'PH', 11.35740000, 119.50490000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557092'),
(85138, 'Tinogboc', 1336, 'ANT', 174, 'PH', 12.00737000, 121.41221000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557140'),
(85139, 'Tinongan', 1336, 'ANT', 174, 'PH', 10.21500000, 123.03528000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557156'),
(85140, 'Tinoto', 1310, '12', 174, 'PH', 5.88667000, 125.07056000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557173'),
(85141, 'Tinubuan', 1344, 'BAN', 174, 'PH', 11.18600000, 124.00780000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557223'),
(85142, 'Tinutulan', 1316, '14', 174, 'PH', 7.10000000, 124.65000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557242'),
(85143, 'Tiparak', 1362, 'ZSI', 174, 'PH', 8.03410000, 123.52880000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557289'),
(85144, 'Tipaz', 1356, 'MDC', 174, 'PH', 13.82850000, 121.42506000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557289'),
(85146, 'Tipolo', 1344, 'BAN', 174, 'PH', 10.01960000, 124.51440000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557407'),
(85147, 'Tiring', 1336, 'ANT', 174, 'PH', 10.85000000, 122.50670000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557561'),
(85148, 'Titay', 1362, 'ZSI', 174, 'PH', 7.81250000, 122.53444000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557598'),
(85149, 'Tiwi', 1337, 'ALB', 174, 'PH', 13.45850000, 123.68050000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q493444'),
(85150, 'Tiwi', 1336, 'ANT', 174, 'PH', 10.92972000, 122.73417000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557734'),
(85151, 'Tobias Fornier', 1336, 'ANT', 174, 'PH', 10.51500000, 121.94610000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q492817'),
(85152, 'Toboso', 1351, 'NEC', 174, 'PH', 10.72200770, 123.26553400, '2025-03-25 12:35:22', '2025-03-25 12:35:22', 1, 'Q195549'),
(85153, 'Toboy', 1355, '01', 174, 'PH', 16.02710000, 120.63690000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557872'),
(85154, 'Tobuan', 1355, '01', 174, 'PH', 16.05562000, 120.11024000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557889'),
(85155, 'Tococ East', 1355, '01', 174, 'PH', 15.82498000, 120.42605000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557905'),
(85156, 'Tocok', 1355, '01', 174, 'PH', 15.75000000, 120.30000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31557941'),
(85157, 'Toledo', 1306, 'CEB', 174, 'PH', 10.35607120, 123.51909920, '2025-03-20 20:06:03', '2025-03-20 20:06:03', 1, 'Q316527'),
(85158, 'Tolosa', 1332, 'LEY', 174, 'PH', 11.03557130, 124.96549090, '2025-03-24 16:18:27', '2025-03-24 16:18:27', 1, 'Q213442'),
(85159, 'Tolosa', 1362, 'ZSI', 174, 'PH', 7.03222000, 122.16139000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q213442'),
(85160, 'Tomado', 1310, '12', 174, 'PH', 7.19250000, 124.60806000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558503'),
(85161, 'Tomas Oppus', 1284, 'SLE', 174, 'PH', 10.27468320, 124.90472220, '2025-03-26 13:38:54', '2025-03-26 13:38:54', 1, 'Q173811'),
(85162, 'Tombod', 1355, '01', 174, 'PH', 15.93168000, 120.56721000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558640'),
(85163, 'Tombongon', 1340, '11', 174, 'PH', 7.07920000, 125.94860000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558656'),
(85164, 'Tomingad', 1354, 'MDR', 174, 'PH', 12.44311000, 122.02106000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558671'),
(85165, 'Tominhao', 1344, 'BAN', 174, 'PH', 11.23563000, 124.03526000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558671'),
(85166, 'Tondod', 1345, '03', 174, 'PH', 15.71820000, 120.96800000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558899'),
(85167, 'Tondol', 1355, '01', 174, 'PH', 16.31040000, 120.01310000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558917'),
(85168, 'Tongouson', 1316, '14', 174, 'PH', 5.02140000, 120.13280000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31558917'),
(85169, 'Toong', 1356, 'MDC', 174, 'PH', 14.07063000, 120.76333000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31559109'),
(85170, 'Topdac', 1335, '15', 174, 'PH', 16.55530000, 120.71160000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31559128'),
(85171, 'Toritori', 1355, '01', 174, 'PH', 16.23827000, 119.99561000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31559209'),
(85172, 'Torrijos', 1329, 'MAD', 174, 'PH', 13.31692490, 121.98132600, '2025-03-24 17:24:13', '2025-03-24 17:24:13', 1, 'Q107048'),
(85173, 'Tortosa', 1336, 'ANT', 174, 'PH', 10.93294000, 123.09000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31559430'),
(85174, 'Totolan', 1344, 'BAN', 174, 'PH', 9.63286000, 123.84702000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31559594'),
(85175, 'Tran', 1310, '12', 174, 'PH', 6.72028000, 124.05500000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31560240'),
(85176, 'Tranca', 1356, 'MDC', 174, 'PH', 14.11815000, 121.05741000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31560255'),
(85177, 'Trapiche', 1336, 'ANT', 174, 'PH', 10.68417000, 122.43222000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31560282'),
(85178, 'Trento', 1314, '13', 174, 'PH', 8.04583000, 126.06361000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31560282'),
(85179, 'Trinidad', 1344, 'BAN', 174, 'PH', 10.07955000, 124.34324000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q405749'),
(85180, 'Tuao', 1342, '02', 174, 'PH', 16.58833000, 121.25778000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31561971'),
(85182, 'Tubalan', 1340, '11', 174, 'PH', 6.49500000, 125.56611000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562070'),
(85183, 'Tuban', 1354, 'MDR', 174, 'PH', 12.81220000, 120.83230000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562118'),
(85184, 'Tuban', 1340, '11', 174, 'PH', 6.82278000, 125.38694000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562103'),
(85188, 'Tubay', 1314, '13', 174, 'PH', 9.16694000, 125.52389000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q176975'),
(85189, 'Tubigagmanoc', 1344, 'BAN', 174, 'PH', 10.62820000, 123.74890000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562320'),
(85190, 'Tubigan', 1363, 'BEN', 174, 'PH', 8.53222000, 124.31222000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562338'),
(85191, 'Tublay', 1335, '15', 174, 'PH', 16.54310000, 120.60960000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q30363'),
(85192, 'Tubli', 1337, 'ALB', 174, 'PH', 13.93180000, 124.14780000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562406'),
(85196, 'Tubod', 1327, 'LAN', 174, 'PH', 7.99802050, 123.78557280, '2025-03-24 13:15:02', '2025-03-24 13:15:02', 1, 'Q274439'),
(85197, 'Tubod-dugoan', 1344, 'BAN', 174, 'PH', 10.04430000, 123.49940000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562507'),
(85201, 'Tucdao', 1352, 'BTN', 174, 'PH', 11.70110000, 124.47190000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562669'),
(85202, 'Tucuran', 1362, 'ZSI', 174, 'PH', 7.85240000, 123.57430000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q132417'),
(85203, 'Tudela', 1306, 'BAN', 174, 'PH', 10.66570550, 124.43879020, '2025-03-20 20:10:13', '2025-03-20 20:10:13', 1, 'Q316564'),
(85204, 'Tudela', 1363, 'BEN', 174, 'PH', 8.24720000, 123.84240000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q196248'),
(85205, 'Tuding', 1335, '15', 174, 'PH', 16.41085000, 120.64220000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562922'),
(85206, 'Tuganay', 1340, '11', 174, 'PH', 7.36889000, 125.72306000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562974'),
(85207, 'Tugas', 1336, 'ANT', 174, 'PH', 11.61300000, 122.38220000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31562990'),
(85209, 'Tugbong', 1352, 'BTN', 174, 'PH', 11.01667000, 124.60000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563055'),
(85210, 'Tugdan', 1354, 'MDR', 174, 'PH', 12.31340000, 122.08530000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563071'),
(85211, 'Tugos', 1337, 'ALB', 174, 'PH', 14.26667000, 122.75000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563103'),
(85212, 'Tuguegarao', 1342, '02', 174, 'PH', 17.61577000, 121.72285000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q2200'),
(85213, 'Tuhian', 1356, 'MDC', 174, 'PH', 13.62220000, 122.21780000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563137'),
(85214, 'Tuka', 1310, '12', 174, 'PH', 6.51862000, 124.58736000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563153'),
(85215, 'Tulay', 1356, 'MDC', 174, 'PH', 14.26969000, 120.76925000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563233'),
(85216, 'Tulay na Lupa', 1337, 'ALB', 174, 'PH', 14.09340000, 122.78620000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563251'),
(85217, 'Tuli', 1340, '11', 174, 'PH', 7.23333000, 125.41667000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563411'),
(85219, 'Tumalaytay', 1337, 'ALB', 174, 'PH', 12.27584000, 123.23201000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563551'),
(85220, 'Tumalim', 1356, 'MDC', 174, 'PH', 14.08010000, 120.72310000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563569'),
(85221, 'Tumarbong', 1354, 'MDR', 174, 'PH', 10.37659000, 119.45790000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563633'),
(85223, 'Tumbagaan', 1316, '14', 174, 'PH', 5.36560000, 120.31390000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q50177'),
(85224, 'Tumcon Ilawod', 1336, 'ANT', 174, 'PH', 10.91667000, 122.66667000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563767'),
(85225, 'Tunga', 1332, 'LEY', 174, 'PH', 11.23878550, 124.72708480, '2025-03-24 16:19:33', '2025-03-24 16:19:33', 1, 'Q213455'),
(85226, 'Tungao', 1314, '13', 174, 'PH', 8.78368000, 125.59587000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q213455'),
(85227, 'Tungawan', 1362, 'ZSI', 174, 'PH', 7.50806000, 122.37111000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q131984'),
(85228, 'Tunggol', 1316, '14', 174, 'PH', 5.84458000, 121.15436000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563984'),
(85229, 'Tungol', 1316, '14', 174, 'PH', 7.08083000, 124.75306000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31563999'),
(85230, 'Tuod', 1363, 'BEN', 174, 'PH', 8.34333000, 124.35417000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31564079'),
(85231, 'Tupang', 1342, '02', 174, 'PH', 17.90483000, 121.64099000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31564096'),
(85233, 'Tupsan', 1363, 'BEN', 174, 'PH', 9.19917000, 124.77694000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31564193'),
(85234, 'Tutay', 1344, 'BAN', 174, 'PH', 10.24630000, 123.58050000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31565553'),
(85235, 'Tutubigan', 1352, 'BTN', 174, 'PH', 11.83972000, 125.06139000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31565602'),
(85236, 'Tuy', 1359, 'BTG', 174, 'PH', 14.03082800, 120.65923290, '2025-03-20 16:04:02', '2025-03-20 16:04:02', 1, 'Q59844'),
(85237, 'Tuyan', 1310, '12', 174, 'PH', 6.00000000, 125.28333000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q59844'),
(85238, 'Tuyum', 1336, 'ANT', 174, 'PH', 9.97690000, 122.55820000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31565683'),
(85239, 'Uacon', 1345, '03', 174, 'PH', 15.67870000, 119.94030000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567197'),
(85240, 'Ualog', 1336, 'ANT', 174, 'PH', 10.57396000, 123.39330000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567264'),
(85241, 'Ubay', 1344, 'BAN', 174, 'PH', 10.05600000, 124.47294000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q406015'),
(85242, 'Uddiawan', 1342, '02', 174, 'PH', 16.55888000, 121.15124000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567440'),
(85243, 'Ugac Sur', 1342, '02', 174, 'PH', 17.61325000, 121.71583000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567455'),
(85244, 'Ugad', 1342, '02', 174, 'PH', 17.30750000, 121.80042000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567471'),
(85245, 'Ula', 1340, '11', 174, 'PH', 7.13222000, 125.49222000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567520'),
(85246, 'Uling', 1344, 'BAN', 174, 'PH', 10.28048000, 123.71000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567520'),
(85247, 'Umabay', 1337, 'ALB', 174, 'PH', 12.30954000, 123.67961000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567520'),
(85248, 'Umaganhan', 1352, 'BTN', 174, 'PH', 11.32920000, 124.37700000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567618'),
(85249, 'Umanday Centro', 1355, '01', 174, 'PH', 15.93800000, 120.21530000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567634'),
(85251, 'Umiray', 1345, '03', 174, 'PH', 15.22060000, 121.41110000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567719'),
(85252, 'Ungca', 1336, 'ANT', 174, 'PH', 10.75000000, 122.55000000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567894'),
(85253, 'Ungus-Ungus', 1316, '14', 174, 'PH', 5.05083000, 119.83972000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567911'),
(85254, 'Unidad', 1314, '13', 174, 'PH', 8.53556000, 126.22865000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567927'),
(85255, 'Unidos', 1336, 'ANT', 174, 'PH', 11.90677000, 122.00051000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567927'),
(85256, 'Unidos', 1363, 'BEN', 174, 'PH', 8.59420000, 123.66800000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31567943'),
(85257, 'Union', 1336, 'ANT', 174, 'PH', 11.76120000, 121.89630000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31812900'),
(85258, 'Union', 1344, 'BAN', 174, 'PH', 10.66950000, 124.32520000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31568039'),
(85259, 'Union', 1314, '13', 174, 'PH', 9.75667000, 126.11028000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31568006'),
(85260, 'Unisan', 1275, 'QUE', 174, 'PH', 13.85807970, 121.92587950, '2025-03-25 20:29:00', '2025-03-25 20:29:00', 1, 'Q104099'),
(85261, 'Unzad', 1355, '01', 174, 'PH', 15.91889000, 120.53917000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31568647'),
(85264, 'Upig', 1345, '03', 174, 'PH', 15.02551000, 120.99193000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31568731'),
(85265, 'Upper Klinan', 1310, '12', 174, 'PH', 6.22722000, 125.12083000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31569431'),
(85266, 'Upper San Mateo', 1310, '12', 174, 'PH', 7.17306000, 124.58222000, '2019-10-05 23:13:30', '2019-10-05 23:13:30', 1, 'Q31569595'),
(85269, 'Usab', 1337, 'ALB', 174, 'PH', 12.32974000, 123.58091000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31569869'),
(85270, 'Uson', 1337, 'ALB', 174, 'PH', 12.22530000, 123.78340000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q191636'),
(85271, 'Utabi', 1337, 'ALB', 174, 'PH', 12.64170000, 123.90920000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31569980'),
(85272, 'Uyaan', 1316, '14', 174, 'PH', 7.86306000, 124.04111000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31570060'),
(85273, 'Uyong', 1355, '01', 174, 'PH', 16.05002000, 120.12074000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31570082'),
(85274, 'Uyugan', 1342, '02', 174, 'PH', 20.35045000, 121.93777000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q43469'),
(85275, 'Valderrama', 1336, 'ANT', 174, 'PH', 11.40591000, 122.08371000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31570226'),
(85276, 'Valencia', 1350, 'NER', 174, 'PH', 9.28729930, 123.09086190, '2025-03-25 13:22:14', '2025-03-25 13:22:14', 1, 'Q195140'),
(85277, 'Valencia', 1352, 'BTN', 174, 'PH', 11.10889000, 124.57250000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31570327'),
(85278, 'Valladolid', 1351, 'NEC', 174, 'PH', 10.46999870, 122.81830690, '2025-03-25 12:36:25', '2025-03-25 12:36:25', 1, 'Q195569'),
(85279, 'Valle Hermoso', 1344, 'BAN', 174, 'PH', 9.79390000, 124.23650000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31570426'),
(85280, 'Vallehermoso', 1350, 'NER', 174, 'PH', 10.34193050, 123.25546400, '2025-03-25 13:23:10', '2025-03-25 13:23:10', 1, 'Q195164'),
(85281, 'Vargas', 1345, '03', 174, 'PH', 15.59332000, 120.48709000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31571431'),
(85282, 'Veruela', 1314, '13', 174, 'PH', 8.07310000, 125.95580000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q627349'),
(85283, 'Victoria', 1352, 'BTN', 174, 'PH', 12.44722000, 124.31111000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q175283'),
(85284, 'Victoria', 1356, 'MDC', 174, 'PH', 14.22770000, 121.32920000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q75953'),
(85285, 'Victoria', 1354, 'MDR', 174, 'PH', 13.17722000, 121.27806000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q107618'),
(85286, 'Victorias', 1351, 'NEC', 174, 'PH', 10.86523800, 123.01253880, '2025-03-25 12:37:25', '2025-03-25 12:37:25', 1, 'Q1026021'),
(85287, 'Viejo Daan Banua', 1336, 'ANT', 174, 'PH', 10.90354000, 123.05970000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31572633'),
(85288, 'Viga', 1301, 'CAT', 174, 'PH', 13.85268470, 124.13924760, '2025-03-20 17:50:38', '2025-03-20 17:50:38', 1, 'Q192670'),
(85291, 'Vigo', 1354, 'MDR', 174, 'PH', 13.82666000, 120.17922000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31572791'),
(85292, 'Villa Aglipay', 1345, '03', 174, 'PH', 15.46605000, 120.45273000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31572888'),
(85293, 'Villa Isla', 1345, '03', 174, 'PH', 15.77030000, 120.86660000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31572920'),
(85294, 'Villaba', 1332, 'LEY', 174, 'PH', 11.19362220, 124.35025280, '2025-03-24 16:20:31', '2025-03-24 16:20:31', 1, 'Q213463'),
(85295, 'Villahermosa', 1337, 'ALB', 174, 'PH', 13.04580000, 123.71330000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31573123'),
(85296, 'Villamor', 1310, '12', 174, 'PH', 6.70380000, 124.52009000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31573158'),
(85298, 'Villanueva', 1348, 'MSR', 174, 'PH', 8.57195170, 124.70863900, '2025-03-24 20:56:51', '2025-03-24 20:56:51', 1, 'Q195937'),
(85299, 'Villareal', 5115, 'WSA', 174, 'PH', 11.55254410, 124.85532360, '2025-03-26 17:32:38', '2025-03-26 17:32:38', 1, 'Q816564'),
(85300, 'Villarosa', 1335, '15', 174, 'PH', 15.55000000, 120.75000000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31573308'),
(85302, 'Villaviciosa', 1335, '15', 174, 'PH', 17.43790000, 120.62620000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q29191'),
(85304, 'Vinzons', 1337, 'ALB', 174, 'PH', 14.17370000, 122.90660000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q356898'),
(85305, 'Virac', 1301, 'CAT', 174, 'PH', 13.61058510, 124.11966880, '2025-03-20 17:51:58', '2025-03-20 17:51:58', 1, 'Q192681'),
(85306, 'Viriato', 1352, 'BTN', 174, 'PH', 12.29031000, 124.35333000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31573865'),
(85307, 'Vista Alegre', 1336, 'ANT', 174, 'PH', 9.99840000, 122.76630000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31573897'),
(85308, 'Vitali', 1362, 'ZSI', 174, 'PH', 7.37111000, 122.28861000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31573897'),
(85309, 'Vito', 1336, 'ANT', 174, 'PH', 10.90310000, 123.51540000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31574008'),
(85310, 'Vizal San Pablo', 1345, '03', 174, 'PH', 15.06247000, 120.90021000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31574041'),
(85311, 'Vizal Santo Niño', 1345, '03', 174, 'PH', 15.02924000, 120.90505000, '2019-10-05 23:13:31', '2020-05-01 17:23:05', 1, 'Q31574057'),
(85313, 'Wawa', 1354, 'MDR', 174, 'PH', 13.46320000, 120.74410000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31580136'),
(85314, 'Wawa', 1356, 'MDC', 174, 'PH', 13.74010000, 121.05290000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31580103'),
(85315, 'Wañgan', 1340, '11', 174, 'PH', 7.15972000, 125.44528000, '2019-10-05 23:13:31', '2020-05-01 17:23:05', 1, 'Q31580310'),
(85316, 'Wines', 1340, '11', 174, 'PH', 7.19389000, 125.38917000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31590994'),
(85317, 'Wright', 1352, 'BTN', 174, 'PH', 11.77028000, 125.02194000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31593711'),
(85318, 'Yapak', 1336, 'ANT', 174, 'PH', 11.95000000, 121.93333000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31593711'),
(85319, 'Yeban Norte', 1342, '02', 174, 'PH', 17.02195000, 121.94194000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31594849'),
(85320, 'Yook', 1354, 'MDR', 174, 'PH', 13.22608000, 121.96400000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31595451'),
(85321, 'Yubo', 1336, 'ANT', 174, 'PH', 10.38960000, 123.06900000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31595888'),
(85322, 'Yumbing', 1363, 'BEN', 174, 'PH', 9.23930000, 124.65780000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31595966'),
(85323, 'Yuni', 1356, 'MDC', 174, 'PH', 13.41240000, 122.48460000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q31595996'),
(85324, 'Zamboanga', 1357, 'ZAS', 174, 'PH', 7.16124910, 121.83842940, '2025-03-26 19:43:25', '2025-03-26 19:43:25', 1, 'Q1629'),
(85325, 'Zamboanguita', 1350, 'NER', 174, 'PH', 9.14250760, 123.09765050, '2025-03-25 13:24:05', '2025-03-25 13:24:05', 1, 'Q145673'),
(85327, 'Zarraga', 1315, 'ILI', 174, 'PH', 10.82267190, 122.59420290, '2025-03-22 16:48:22', '2025-03-22 16:48:22', 1, 'Q148064'),
(85328, 'Zumarraga', 5115, 'WSA', 174, 'PH', 11.66240050, 124.81653110, '2025-03-26 17:33:32', '2025-03-26 17:33:32', 1, 'Q229664'),
(85329, 'Abbottabad', 3171, 'KP', 167, 'PK', 34.14630000, 73.21168000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q170315'),
(85330, 'Adilpur', 3175, 'SD', 167, 'PK', 27.93677000, 69.31941000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q170315'),
(85331, 'Ahmedpur East', 3176, 'PB', 167, 'PK', 29.14269000, 71.25771000, '2019-10-05 23:13:31', '2020-07-04 17:35:23', 1, 'Q1250201'),
(85332, 'Ahmadpur Sial', 3176, 'PB', 167, 'PK', 30.67791000, 71.74344000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1250201'),
(85333, 'Akora', 3171, 'KP', 167, 'PK', 34.00337000, 72.12561000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2475251'),
(85334, 'Alik Ghund', 3174, 'BA', 167, 'PK', 30.48976000, 67.52177000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2475251'),
(85335, 'Alipur Chatha', 3176, 'PB', 167, 'PK', 29.38242000, 70.91106000, '2019-10-05 23:13:31', '2020-07-04 16:58:06', 1, 'Q2475251'),
(85336, 'Alizai', 3173, 'TA', 167, 'PK', 33.53613000, 70.34607000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2475251'),
(85337, 'Aman Garh', 3171, 'KP', 167, 'PK', 34.00584000, 71.92971000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2475251'),
(85338, 'Amirabad', 3171, 'KP', 167, 'PK', 34.18729000, 73.09078000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2475251'),
(85339, 'Arifwala', 3176, 'PB', 167, 'PK', 30.29058000, 73.06574000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2984767'),
(85340, 'Ashanagro Koto', 3171, 'KP', 167, 'PK', 34.10773000, 72.24517000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2984767'),
(85341, 'Attock Tehsil', 3176, 'PB', 167, 'PK', 33.76671000, 72.35977000, '2019-10-05 23:13:31', '2020-07-04 16:58:30', 1, 'Q2984767'),
(85342, 'Awārān District', 3174, 'BA', 167, 'PK', 26.21157000, 65.42944000, '2019-10-05 23:13:31', '2020-05-01 17:23:04', 1, 'Q250800'),
(85343, 'Baddomalhi', 3176, 'PB', 167, 'PK', 31.99042000, 74.66410000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q4840703'),
(85344, 'Badin', 3175, 'SD', 167, 'PK', 24.65600000, 68.83700000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2625289'),
(85345, 'Baffa', 3171, 'KP', 167, 'PK', 34.43770000, 73.22368000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2625289'),
(85346, 'Bagarji', 3175, 'SD', 167, 'PK', 27.75431000, 68.75866000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2625289'),
(85347, 'Bahawalnagar', 3176, 'PB', 167, 'PK', 29.99835000, 73.25272000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1249932'),
(85348, 'Bahawalpur', 3176, 'PB', 167, 'PK', 29.39779000, 71.67520000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q318156'),
(85349, 'Bakhri Ahmad Khan', 3176, 'PB', 167, 'PK', 30.73586000, 70.83796000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q318156'),
(85350, 'Bandhi', 3175, 'SD', 167, 'PK', 26.58761000, 68.30215000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q4854564'),
(85351, 'Bannu', 3171, 'KP', 167, 'PK', 32.98527000, 70.60403000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q806916'),
(85352, 'Barishal', 3170, 'GB', 167, 'PK', 36.32162000, 74.69502000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q806916'),
(85353, 'Barkhan', 3174, 'BA', 167, 'PK', 29.89773000, 69.52558000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2640339'),
(85354, 'Basirpur', 3176, 'PB', 167, 'PK', 30.57759000, 73.83912000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2640339'),
(85355, 'Basti Dosa', 3176, 'PB', 167, 'PK', 30.78769000, 70.86853000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2640339'),
(85356, 'Bat Khela', 3171, 'KP', 167, 'PK', 34.61780000, 71.97247000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2582559'),
(85357, 'Battagram', 3171, 'KP', 167, 'PK', 34.67719000, 73.02329000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2582559'),
(85358, 'Battagram District', 3171, 'KP', 167, 'PK', 34.68051000, 73.00535000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3240074'),
(85359, 'Begowala', 3176, 'PB', 167, 'PK', 32.43816000, 74.26794000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3240074'),
(85360, 'Bela', 3174, 'BA', 167, 'PK', 26.22718000, 66.31178000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q815082'),
(85361, 'Berani', 3175, 'SD', 167, 'PK', 25.78497000, 68.80754000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q815082'),
(85362, 'Bhag', 3174, 'BA', 167, 'PK', 29.04174000, 67.82394000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q815082'),
(85363, 'Bhakkar', 3176, 'PB', 167, 'PK', 31.62685000, 71.06471000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2428259'),
(85364, 'Bhalwal', 3176, 'PB', 167, 'PK', 32.26576000, 72.89809000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2791141'),
(85365, 'Bhan', 3175, 'SD', 167, 'PK', 26.55831000, 67.72139000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2791141'),
(85366, 'Bhawana', 3176, 'PB', 167, 'PK', 31.56884000, 72.64917000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q854934'),
(85367, 'Bhera', 3176, 'PB', 167, 'PK', 32.48206000, 72.90865000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3777358'),
(85368, 'Bhimbar', 3172, 'JK', 167, 'PK', 32.97465000, 74.07846000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3631249'),
(85369, 'Bhiria', 3175, 'SD', 167, 'PK', 26.91041000, 68.19466000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3631249'),
(85370, 'Bhit Shah', 3175, 'SD', 167, 'PK', 25.80565000, 68.49143000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3631249'),
(85371, 'Bhopalwala', 3176, 'PB', 167, 'PK', 32.42968000, 74.36350000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3631249'),
(85372, 'Bozdar Wada', 3175, 'SD', 167, 'PK', 27.18300000, 68.63580000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3631249'),
(85373, 'Bulri', 3175, 'SD', 167, 'PK', 24.86667000, 68.33333000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q34784421'),
(85374, 'Buner District', 3171, 'KP', 167, 'PK', 34.44301000, 72.49933000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q2039096'),
(85375, 'Bārkhān District', 3174, 'BA', 167, 'PK', 29.98482000, 69.69944000, '2019-10-05 23:13:31', '2020-05-01 17:23:04', 1, 'Q250810'),
(85376, 'Burewala', 3176, 'PB', 167, 'PK', 30.16667000, 72.65000000, '2019-10-05 23:13:31', '2020-07-04 11:20:19', 1, 'Q250810'),
(85377, 'Chak', 3175, 'SD', 167, 'PK', 27.85838000, 68.83378000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q250810'),
(85378, 'Chak Azam Saffo', 3176, 'PB', 167, 'PK', 30.75202000, 73.02834000, '2019-10-05 23:13:31', '2020-07-04 17:00:42', 1, 'Q250810'),
(85380, 'Chak Jhumra', 3176, 'PB', 167, 'PK', 31.56808000, 73.18317000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q250810'),
(85381, 'Chak One Hundred Twenty Nine Left', 3176, 'PB', 167, 'PK', 30.42919000, 73.04522000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q250810'),
(85382, 'Chak Thirty-one -Eleven Left', 3176, 'PB', 167, 'PK', 30.42388000, 72.69737000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q250810'),
(85383, 'Chak Two Hundred Forty-Nine TDA', 3176, 'PB', 167, 'PK', 31.17772000, 71.20480000, '2019-10-05 23:13:31', '2020-07-04 17:00:29', 1, 'Q250810'),
(85384, 'Chakwal', 3176, 'PB', 167, 'PK', 32.93286000, 72.85394000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1251473'),
(85385, 'Chaman', 3174, 'BA', 167, 'PK', 30.91769000, 66.45259000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1017644'),
(85386, 'Chamber', 3175, 'SD', 167, 'PK', 25.29362000, 68.81176000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1017644'),
(85387, 'Charsadda', 3171, 'KP', 167, 'PK', 34.14822000, 71.74060000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1642808'),
(85388, 'Chawinda', 3176, 'PB', 167, 'PK', 32.34434000, 74.70507000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q5088564'),
(85389, 'Rabwah', 3176, 'PB', 167, 'PK', 31.75511000, 72.91403000, '2019-10-05 23:13:31', '2020-07-04 17:02:55', 1, 'Q1069835'),
(85390, 'Cherat Cantonement', 3171, 'KP', 167, 'PK', 33.82342000, 71.89292000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3695863'),
(85391, 'Chhor', 3175, 'SD', 167, 'PK', 25.51260000, 69.78437000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q15209971'),
(85392, 'Chichawatni', 3176, 'PB', 167, 'PK', 30.53010000, 72.69155000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3321325'),
(85393, 'Chiniot', 3176, 'PB', 167, 'PK', 31.72091000, 72.97836000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1017651'),
(85394, 'Chishtian', 3176, 'PB', 167, 'PK', 29.79713000, 72.85772000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1250229'),
(85395, 'Chitral', 3171, 'KP', 167, 'PK', 35.85180000, 71.78636000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q12126543'),
(85396, 'Choa Saidanshah', 3176, 'PB', 167, 'PK', 32.71962000, 72.98625000, '2019-10-05 23:13:31', '2020-07-04 17:01:33', 1, 'Q3311334'),
(85397, 'Chowki Jamali', 3174, 'BA', 167, 'PK', 28.01944000, 67.92083000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3311334'),
(85399, 'Chuhar Jamali', 3175, 'SD', 167, 'PK', 24.39440000, 67.99298000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3311334'),
(85400, 'Chunian', 3176, 'PB', 167, 'PK', 30.96621000, 73.97908000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3695904'),
(85401, 'Chāgai District', 3174, 'BA', 167, 'PK', 28.98765000, 63.59087000, '2019-10-05 23:13:31', '2020-05-01 17:23:04', 1, 'Q276717'),
(85402, 'Dadhar', 3174, 'BA', 167, 'PK', 29.47489000, 67.65167000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q254504'),
(85403, 'Dadu', 3175, 'SD', 167, 'PK', 26.73033000, 67.77690000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1081635'),
(85404, 'Daira Din Panah', 3176, 'PB', 167, 'PK', 30.57053000, 70.93722000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1081635'),
(85405, 'Dajal', 3176, 'PB', 167, 'PK', 29.55769000, 70.37614000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q1081635'),
(85406, 'Dalbandin', 3174, 'BA', 167, 'PK', 28.88846000, 64.40616000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3235682'),
(85407, 'Dandot RS', 3176, 'PB', 167, 'PK', 32.64167000, 72.97500000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3235682'),
(85408, 'Daromehar', 3175, 'SD', 167, 'PK', 24.79382000, 68.17978000, '2019-10-05 23:13:31', '2019-10-05 23:13:31', 1, 'Q3235682'),
(85409, 'Darya Khan', 3176, 'PB', 167, 'PK', 31.78447000, 71.10197000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3321393'),
(85410, 'Darya Khan Marri', 3175, 'SD', 167, 'PK', 26.67765000, 68.28666000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3321393'),
(85411, 'Daska', 3176, 'PB', 167, 'PK', 32.32422000, 74.35039000, '2019-10-05 23:13:32', '2020-07-04 17:04:55', 1, 'Q2374990'),
(85413, 'Daulatpur', 3175, 'SD', 167, 'PK', 26.50158000, 67.97079000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2374990'),
(85414, 'Daultala', 3176, 'PB', 167, 'PK', 33.19282000, 73.14099000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2374990'),
(85415, 'Daur', 3175, 'SD', 167, 'PK', 26.45528000, 68.31835000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2374990'),
(85416, 'Dera Bugti', 3174, 'BA', 167, 'PK', 29.03619000, 69.15849000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2989478'),
(85417, 'Dera Bugti District', 3174, 'BA', 167, 'PK', 28.94250000, 69.06883000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2315565'),
(85418, 'Dera Ghazi Khan', 3176, 'PB', 167, 'PK', 30.04587000, 70.64029000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q999076'),
(85419, 'Dera Ismail Khan', 3171, 'KP', 167, 'PK', 31.83129000, 70.90170000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1249888'),
(85420, 'Dera Ismāīl Khān District', 3171, 'KP', 167, 'PK', 31.85963000, 70.64879000, '2019-10-05 23:13:32', '2020-05-01 17:23:04', 1, 'Q284210'),
(85421, 'Dhanot', 3176, 'PB', 167, 'PK', 29.57991000, 71.75213000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q284210'),
(85422, 'Dhaunkal', 3176, 'PB', 167, 'PK', 32.40613000, 74.13706000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q284210'),
(85423, 'Dhoro Naro', 3175, 'SD', 167, 'PK', 25.50484000, 69.57090000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q284210'),
(85424, 'Digri', 3175, 'SD', 167, 'PK', 25.15657000, 69.11098000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q284210'),
(85425, 'Dijkot', 3176, 'PB', 167, 'PK', 31.21735000, 72.99621000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q5276491'),
(85426, 'Dinan Bashnoian Wala', 3176, 'PB', 167, 'PK', 29.76584000, 73.26557000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q5276491'),
(85427, 'Dinga', 3176, 'PB', 167, 'PK', 32.64101000, 73.72039000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q5278157'),
(85428, 'Dipalpur', 3176, 'PB', 167, 'PK', 30.66984000, 73.65306000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2985681'),
(85429, 'Diplo', 3175, 'SD', 167, 'PK', 24.46688000, 69.58114000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2985681'),
(85430, 'Doaba', 3171, 'KP', 167, 'PK', 33.42450000, 70.73676000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2985681'),
(85431, 'Dokri', 3175, 'SD', 167, 'PK', 27.37421000, 68.09715000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3249928'),
(85432, 'Duki', 3174, 'BA', 167, 'PK', 30.15307000, 68.57323000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3249928'),
(85433, 'Dullewala', 3176, 'PB', 167, 'PK', 31.83439000, 71.43639000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3694406'),
(85434, 'Dunga Bunga', 3176, 'PB', 167, 'PK', 29.74975000, 73.24294000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1985683'),
(85435, 'Dunyapur', 3176, 'PB', 167, 'PK', 29.80275000, 71.74344000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1985683'),
(85436, 'Eminabad', 3176, 'PB', 167, 'PK', 32.04237000, 74.25996000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q5372475'),
(85437, 'Faisalabad', 3176, 'PB', 167, 'PK', 31.41554000, 73.08969000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q173985'),
(85438, 'Faqirwali', 3176, 'PB', 167, 'PK', 29.46799000, 73.03489000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q5434470'),
(85439, 'Faruka', 3176, 'PB', 167, 'PK', 31.88642000, 72.41362000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q5434470'),
(85440, 'Fazilpur', 3176, 'PB', 167, 'PK', 32.17629000, 75.06583000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q5434470'),
(85441, 'Fort Abbas', 3176, 'PB', 167, 'PK', 29.19344000, 72.85525000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3246255'),
(85442, 'Gadani', 3174, 'BA', 167, 'PK', 25.11879000, 66.73219000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q984103'),
(85443, 'Gambat', 3175, 'SD', 167, 'PK', 27.35170000, 68.52150000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q984103'),
(85444, 'Garh Maharaja', 3176, 'PB', 167, 'PK', 30.83383000, 71.90491000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q984103'),
(85445, 'Garhi Khairo', 3174, 'BA', 167, 'PK', 28.06029000, 67.98033000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q984103'),
(85446, 'Garhiyasin', 3175, 'SD', 167, 'PK', 27.90631000, 68.51210000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q984103'),
(85447, 'Gharo', 3175, 'SD', 167, 'PK', 24.74182000, 67.58534000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q984103'),
(85448, 'Ghauspur', 3175, 'SD', 167, 'PK', 28.13882000, 69.08245000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q984103'),
(85449, 'Ghotki', 3175, 'SD', 167, 'PK', 28.00437000, 69.31569000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2592625'),
(85450, 'Gilgit', 3170, 'GB', 167, 'PK', 35.91869000, 74.31245000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q609024'),
(85451, 'Gojra', 3176, 'PB', 167, 'PK', 31.14926000, 72.68323000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q609024'),
(85452, 'Goth Garelo', 3175, 'SD', 167, 'PK', 27.43521000, 68.07572000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q609024'),
(85453, 'Goth Phulji', 3175, 'SD', 167, 'PK', 26.88099000, 67.68239000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q609024'),
(85454, 'Goth Radhan', 3175, 'SD', 167, 'PK', 27.19846000, 67.95348000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q609024'),
(85455, 'Gujar Khan', 3176, 'PB', 167, 'PK', 33.25411000, 73.30433000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3049021'),
(85456, 'Gujranwala', 3176, 'PB', 167, 'PK', 32.15567000, 74.18705000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q243322'),
(85457, 'Gujrat', 3176, 'PB', 167, 'PK', 32.57420000, 74.07542000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q243322'),
(85458, 'Gulishah Kach', 3173, 'TA', 167, 'PK', 32.67087000, 70.33917000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q243322'),
(85459, 'Gwadar', 3174, 'BA', 167, 'PK', 25.12163000, 62.32541000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q580533'),
(85460, 'Hadali', 3176, 'PB', 167, 'PK', 32.64043000, 74.56898000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q580533'),
(85461, 'Hafizabad', 3176, 'PB', 167, 'PK', 32.07095000, 73.68802000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1253663'),
(85462, 'Hala', 3175, 'SD', 167, 'PK', 25.81459000, 68.42198000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3250492'),
(85463, 'Hangu', 3171, 'KP', 167, 'PK', 33.53198000, 71.05950000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1575544'),
(85464, 'Haripur', 3171, 'KP', 167, 'PK', 33.99783000, 72.93493000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q520821'),
(85465, 'Harnai', 3174, 'BA', 167, 'PK', 30.10077000, 67.93824000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2995546'),
(85466, 'Harnoli', 3176, 'PB', 167, 'PK', 32.27871000, 71.55429000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2995546'),
(85467, 'Harunabad', 3176, 'PB', 167, 'PK', 29.61206000, 73.13802000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2995546'),
(85468, 'Hasilpur', 3176, 'PB', 167, 'PK', 29.69221000, 72.54566000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2603403'),
(85469, 'Haveli Lakha', 3176, 'PB', 167, 'PK', 30.45097000, 73.69371000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2603403'),
(85470, 'Havelian', 3171, 'KP', 167, 'PK', 34.05348000, 73.15993000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2659092'),
(85471, 'Hazro', 3176, 'PB', 167, 'PK', 33.90990000, 72.49179000, '2019-10-05 23:13:32', '2020-07-04 17:09:06', 1, 'Q3695085'),
(85472, 'Hingorja', 3175, 'SD', 167, 'PK', 27.21088000, 68.41598000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3695085'),
(85473, 'Hujra Shah Muqeem', 3176, 'PB', 167, 'PK', 30.74168000, 73.82327000, '2019-10-05 23:13:32', '2020-07-04 17:40:43', 1, 'Q3776565'),
(85474, 'Hyderabad', 3175, 'SD', 167, 'PK', 25.39242000, 68.37366000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1640079'),
(85475, 'Islamabad', 3169, 'IS', 167, 'PK', 33.72148000, 73.04329000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1362'),
(85476, 'Islamkot', 3175, 'SD', 167, 'PK', 24.69904000, 70.17982000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1362'),
(85477, 'Jacobabad', 3175, 'SD', 167, 'PK', 28.28187000, 68.43761000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1017696'),
(85478, 'Jahanian Shah', 3176, 'PB', 167, 'PK', 31.80541000, 72.27740000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1017696'),
(85479, 'Jalalpur Jattan', 3176, 'PB', 167, 'PK', 32.64118000, 74.20561000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q954036'),
(85480, 'Jalalpur Pirwala', 3176, 'PB', 167, 'PK', 29.50510000, 71.22202000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3695832'),
(85481, 'Jampur', 3176, 'PB', 167, 'PK', 29.64235000, 70.59518000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3695832'),
(85482, 'Jamshoro', 3175, 'SD', 167, 'PK', 25.43608000, 68.28017000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q112817'),
(85483, 'Jand', 3176, 'PB', 167, 'PK', 33.43304000, 72.01877000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q112817'),
(85484, 'Jandiala Sher Khan', 3176, 'PB', 167, 'PK', 31.82098000, 73.91815000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q6150959'),
(85485, 'Jaranwala', 3176, 'PB', 167, 'PK', 31.33320000, 73.41868000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1251242'),
(85486, 'Jati', 3175, 'SD', 167, 'PK', 24.35492000, 68.26732000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1251242'),
(85487, 'Jatoi Shimali', 3176, 'PB', 167, 'PK', 29.51827000, 70.84474000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1251242'),
(85488, 'Jauharabad', 3176, 'PB', 167, 'PK', 32.29016000, 72.28182000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3075413'),
(85489, 'Jhal Magsi District', 3174, 'BA', 167, 'PK', 28.36881000, 67.54300000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2633556'),
(85490, 'Jhang', 3176, 'PB', 167, 'PK', 31.30568000, 72.32594000, '2019-10-05 23:13:32', '2020-07-04 17:10:05', 1, 'Q1026616'),
(85491, 'Jhang Sadar', 3176, 'PB', 167, 'PK', 31.26981000, 72.31687000, '2019-10-05 23:13:32', '2020-07-04 17:10:17', 1, 'Q6190853'),
(85492, 'Jhawarian', 3176, 'PB', 167, 'PK', 32.36192000, 72.62275000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q6190853'),
(85493, 'Jhelum', 3176, 'PB', 167, 'PK', 32.93448000, 73.73102000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q121551'),
(85494, 'Jhol', 3175, 'SD', 167, 'PK', 25.95533000, 68.88871000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q121551'),
(85495, 'Jiwani', 3174, 'BA', 167, 'PK', 25.04852000, 61.74573000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2621751'),
(85496, 'Johi', 3175, 'SD', 167, 'PK', 26.69225000, 67.61431000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2621751'),
(85497, 'Jāfarābād District', 3174, 'BA', 167, 'PK', 28.30104000, 68.19783000, '2019-10-05 23:13:32', '2020-05-01 17:23:04', 1, 'Q595921'),
(85498, 'Jām Sāhib', 3175, 'SD', 167, 'PK', 26.29583000, 68.62917000, '2019-10-05 23:13:32', '2020-05-01 17:23:04', 1, 'Q595921'),
(85499, 'Kabirwala', 3176, 'PB', 167, 'PK', 30.40472000, 71.86269000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q595921'),
(85500, 'Kadhan', 3175, 'SD', 167, 'PK', 24.48041000, 68.98551000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q595921'),
(85501, 'Kahna Nau', 3176, 'PB', 167, 'PK', 31.36709000, 74.36899000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q10313603'),
(85503, 'Kahuta', 3176, 'PB', 167, 'PK', 33.59183000, 73.38736000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q988103'),
(85504, 'Kakad Wari Dir Upper', 3171, 'KP', 167, 'PK', 34.99798000, 72.07295000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q988103'),
(85505, 'Kalabagh', 3176, 'PB', 167, 'PK', 32.96164000, 71.54638000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1542553'),
(85506, 'Kalaswala', 3176, 'PB', 167, 'PK', 32.20081000, 74.64858000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1542553'),
(85507, 'Kalat', 3174, 'BA', 167, 'PK', 29.02663000, 66.59361000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1542553'),
(85508, 'Kaleke Mandi', 3176, 'PB', 167, 'PK', 31.97597000, 73.59999000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1542553'),
(85509, 'Kallar Kahar', 3176, 'PB', 167, 'PK', 32.77998000, 72.69793000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q797139'),
(85510, 'Kalur Kot', 3176, 'PB', 167, 'PK', 32.15512000, 71.26631000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q797139'),
(85511, 'Kalāt District', 3174, 'BA', 167, 'PK', 28.88242000, 66.53165000, '2019-10-05 23:13:32', '2020-05-01 17:23:04', 1, 'Q611142'),
(85512, 'Kamalia', 3176, 'PB', 167, 'PK', 30.72708000, 72.64607000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q2573596');

