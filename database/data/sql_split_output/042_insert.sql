INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(20757, 'Cumbitara', 2897, 'NAR', 48, 'CO', 1.64786000, -77.57819000, '2019-10-05 22:39:46', '2019-10-05 22:39:46', 1, 'Q23694047'),
(20758, 'Cunday', 2883, 'TOL', 48, 'CO', 4.00284000, -74.69295000, '2019-10-05 22:39:46', '2019-10-05 22:39:46', 1, 'Q577597'),
(20759, '<PERSON><PERSON><PERSON>', 2891, 'CAQ', 48, 'CO', 1.03327000, -75.91907000, '2019-10-05 22:39:46', '2019-10-05 22:39:46', 1, 'Q660268'),
(20760, '<PERSON><PERSON><PERSON><PERSON>', 2901, '<PERSON><PERSON>', 48, 'CO', 6.60519000, -73.06809000, '2019-10-05 22:39:46', '2020-05-01 17:22:40', 1, 'Q23692688'),
(20761, '<PERSON>uruman<PERSON>', 2899, 'CES', 48, '<PERSON>', 9.19992000, -73.54274000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q23692704'),
(20762, 'Cuítiva', 2903, 'BOY', 48, 'CO', 5.58007000, -72.96687000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1654830'),
(20763, 'Cáceres', 2890, 'ANT', 48, 'CO', 7.68252000, -75.22481000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q2431763'),
(20765, 'Cácota', 2877, 'NSA', 48, 'CO', 7.26787000, -72.64197000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1655073'),
(20767, 'Cértegui', 2876, 'CHO', 48, 'CO', 5.37073000, -76.60440000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q610657'),
(20769, 'Córdoba', 2897, 'NAR', 48, 'CO', 0.85362000, -77.51817000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1524850'),
(20770, 'Córdoba', 2893, 'BOL', 48, 'CO', 9.55149000, -74.89800000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1150886'),
(20771, 'Córdoba', 2874, 'QUI', 48, 'CO', 4.38055000, -75.66685000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q23730122'),
(20772, 'Cúcuta', 2877, 'NSA', 48, 'CO', 7.89391000, -72.50782000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q216847'),
(20773, 'Dabeiba', 2890, 'ANT', 48, 'CO', 7.03367000, -76.34161000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1441773'),
(20774, 'Dagua', 2904, 'VAC', 48, 'CO', 3.65685000, -76.68859000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q2406018'),
(20776, 'Dibulla', 2889, 'LAG', 48, 'CO', 11.27251000, -73.30911000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1441095'),
(20777, 'Distracción', 2889, 'LAG', 48, 'CO', 10.89784000, -72.88666000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1525662'),
(20779, 'Dolores', 2883, 'TOL', 48, 'CO', 3.60534000, -74.80585000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q520486'),
(20781, 'Donmatías', 2890, 'ANT', 48, 'CO', 6.48569000, -75.39496000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1525993'),
(20782, 'Dosquebradas', 2879, 'RIS', 48, 'CO', 4.83916000, -75.66727000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1093584'),
(20783, 'Duitama', 2903, 'BOY', 48, 'CO', 5.82450000, -73.03408000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1011125'),
(20784, 'Durania', 2877, 'NSA', 48, 'CO', 7.71307000, -72.65759000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q510149'),
(20785, 'Ebéjico', 2890, 'ANT', 48, 'CO', 6.32598000, -75.76835000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1525223'),
(20786, 'El Bagre', 2890, 'ANT', 48, 'CO', 7.60347000, -74.80951000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1014722'),
(20787, 'El Banco', 2886, 'MAG', 48, 'CO', 9.00114000, -73.97581000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q2330760'),
(20789, 'El Cairo', 2904, 'VAC', 48, 'CO', 4.74889000, -76.24443000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1577535'),
(20790, 'El Calvario', 2878, 'MET', 48, 'CO', 4.35426000, -73.74431000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1655089'),
(20791, 'El Cantón de San Pablo', 2876, 'CHO', 48, 'CO', 5.33889000, -76.73139000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1576215'),
(20793, 'El Carmen', 2877, 'NSA', 48, 'CO', 8.51064000, -73.44776000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q23693022'),
(20795, 'El Carmen de Atrato', 2876, 'CHO', 48, 'CO', 5.83333000, -76.25000000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q533627'),
(20796, 'El Carmen de Bolívar', 2893, 'BOL', 48, 'CO', 9.71740000, -75.12023000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q1323930'),
(20797, 'El Carmen de Chucurí', 2901, 'SAN', 48, 'CO', 6.69736000, -73.51117000, '2019-10-05 22:39:47', '2020-05-01 17:22:40', 1, 'Q515182'),
(20799, 'El Castillo', 2878, 'MET', 48, 'CO', 3.56363000, -73.79488000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1577456'),
(20800, 'El Cerrito', 2904, 'VAC', 48, 'CO', 3.64273000, -76.20960000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1093505'),
(20801, 'El Charco', 2897, 'NAR', 48, 'CO', 2.47926000, -78.10972000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1441184'),
(20802, 'El Cocuy', 2903, 'BOY', 48, 'CO', 6.32839000, -72.43577000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1655984'),
(20803, 'El Colegio', 2875, 'CUN', 48, 'CO', 4.56047000, -74.42614000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q935205'),
(20804, 'El Copey', 2899, 'CES', 48, 'CO', 10.15031000, -73.96140000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1440746'),
(20805, 'El Doncello', 2891, 'CAQ', 48, 'CO', 1.67817000, -75.28466000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q746033'),
(20806, 'El Dovio', 2904, 'VAC', 48, 'CO', 4.50790000, -76.23619000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q606862'),
(20807, 'El Encanto', 2895, 'AMA', 48, 'CO', -1.56261000, -73.25684000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q665774'),
(20808, 'El Espino', 2903, 'BOY', 48, 'CO', 6.50812000, -72.48036000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1654603'),
(20809, 'El Guacamayo', 2901, 'SAN', 48, 'CO', 6.24856000, -73.52957000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1655487'),
(20810, 'El Guamo', 2893, 'BOL', 48, 'CO', 10.01753000, -74.93483000, '2019-10-05 22:39:47', '2019-10-05 22:39:47', 1, 'Q1576366'),
(20812, 'El Molino', 2889, 'LAG', 48, 'CO', 10.65296000, -72.92461000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q1574943'),
(20813, 'El Paso', 2899, 'CES', 48, 'CO', 9.65724000, -73.74685000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q1441955'),
(20814, 'El Paujil', 2891, 'CAQ', 48, 'CO', 1.57085000, -75.31403000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q1441955'),
(20816, 'El Peñol', 2897, 'NAR', 48, 'CO', 1.45365000, -77.44017000, '2019-10-05 22:39:48', '2020-05-01 17:22:40', 1, 'Q23691789'),
(20817, 'El Peñón', 2875, 'CUN', 48, 'CO', 5.25264000, -74.29069000, '2019-10-05 22:39:48', '2020-05-01 17:22:40', 1, 'Q23691792'),
(20818, 'El Peñón', 2893, 'BOL', 48, 'CO', 8.98691000, -73.94697000, '2019-10-05 22:39:48', '2020-05-01 17:22:40', 1, 'Q662799'),
(20819, 'El Peñón', 2901, 'SAN', 48, 'CO', 6.09900000, -73.92835000, '2019-10-05 22:39:48', '2020-05-01 17:22:40', 1, 'Q662799'),
(20820, 'El Piñon', 2886, 'MAG', 48, 'CO', 10.33333000, -74.66667000, '2019-10-05 22:39:48', '2020-05-01 17:22:40', 1, 'Q614970'),
(20822, 'El Playón', 2901, 'SAN', 48, 'CO', 7.47131000, -73.20310000, '2019-10-05 22:39:48', '2020-05-01 17:22:40', 1, 'Q1525443'),
(20823, 'El Retorno', 2888, 'GUV', 48, 'CO', 2.33022000, -72.62765000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q1526156'),
(20824, 'El Retén', 2886, 'MAG', 48, 'CO', 10.61135000, -74.26824000, '2019-10-05 22:39:48', '2020-05-01 17:22:40', 1, 'Q547081'),
(20825, 'El Roble', 2902, 'SUC', 48, 'CO', 9.10193000, -75.19508000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q1577912'),
(20826, 'El Rosal', 2875, 'CUN', 48, 'CO', 4.85314000, -74.25996000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q602957'),
(20827, 'El Rosario', 2897, 'NAR', 48, 'CO', 1.84593000, -77.40620000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q1574979'),
(20829, 'El Tablón de Gómez', 2897, 'NAR', 48, 'CO', 1.42717000, -77.09693000, '2019-10-05 17:09:48', '2020-05-01 11:52:40', 1, 'Q1525086'),
(20831, 'El Tambo', 2884, 'CAU', 48, 'CO', 2.45199000, -76.81029000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q961294'),
(20832, 'El Tambo', 2897, 'NAR', 48, 'CO', 1.43049000, -77.38326000, '2019-10-05 22:39:48', '2019-10-05 22:39:48', 1, 'Q2431847'),
(20833, 'El Tarra', 2877, 'NSA', 48, 'CO', 8.57562000, -73.09489000, '2019-10-05 22:39:49', '2019-10-05 22:39:49', 1, 'Q23701236'),
(20834, 'El Zulia', 2877, 'NSA', 48, 'CO', 7.93248000, -72.60125000, '2019-10-05 22:39:49', '2019-10-05 22:39:49', 1, 'Q1441107'),
(20835, 'El Águila', 2904, 'VAC', 48, 'CO', 4.91946000, -76.05680000, '2019-10-05 22:39:49', '2020-05-01 17:22:40', 1, 'Q1578199'),
(20836, 'Encino', 2901, 'SAN', 48, 'CO', 6.13735000, -73.09847000, '2019-10-05 22:39:49', '2019-10-05 22:39:49', 1, 'Q1654297'),
(20837, 'Enciso', 2901, 'SAN', 48, 'CO', 6.64651000, -72.70709000, '2019-10-05 22:39:49', '2019-10-05 22:39:49', 1, 'Q1654246'),
(20838, 'Entrerrios', 2890, 'ANT', 48, 'CO', 6.58333000, -75.58333000, '2019-10-05 22:39:49', '2019-10-05 22:39:49', 1, 'Q1654246'),
(20840, 'Envigado', 2890, 'ANT', 48, 'CO', 6.17591000, -75.59174000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1447266'),
(20841, 'Espinal', 2883, 'TOL', 48, 'CO', 4.14924000, -74.88429000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q23691418'),
(20842, 'Facatativá', 2875, 'CUN', 48, 'CO', 4.81367000, -74.35453000, '2019-10-05 22:39:50', '2020-05-01 17:22:40', 1, 'Q1947594'),
(20843, 'Falan', 2883, 'TOL', 48, 'CO', 5.12383000, -74.95181000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1577895'),
(20844, 'Filadelfia', 2887, 'CAL', 48, 'CO', 5.29606000, -75.56120000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1525422'),
(20845, 'Filandia', 2874, 'QUI', 48, 'CO', 4.67525000, -75.67142000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1525384'),
(20846, 'Firavitoba', 2903, 'BOY', 48, 'CO', 5.66885000, -72.99289000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1579392'),
(20847, 'Flandes', 2883, 'TOL', 48, 'CO', 4.25000000, -74.83333000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q764821'),
(20848, 'Florencia', 2884, 'CAU', 48, 'CO', 1.68318000, -77.07331000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q23691480'),
(20849, 'Florencia', 2891, 'CAQ', 48, 'CO', 1.71619000, -75.59624000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q2308980'),
(20850, 'Floresta', 2903, 'BOY', 48, 'CO', 5.85903000, -72.92511000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1656297'),
(20851, 'Florida', 2904, 'VAC', 48, 'CO', 3.30940000, -76.18989000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1093490'),
(20852, 'Floridablanca', 2901, 'SAN', 48, 'CO', 7.06315000, -73.08586000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1006661'),
(20853, 'Florián', 2901, 'SAN', 48, 'CO', 5.80487000, -73.97029000, '2019-10-05 22:39:50', '2020-05-01 17:22:40', 1, 'Q1575413'),
(20854, 'Fomeque', 2875, 'CUN', 48, 'CO', 4.52806000, -73.78879000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1575413'),
(20855, 'Fonseca', 2889, 'LAG', 48, 'CO', 10.82966000, -72.79778000, '2019-10-05 22:39:50', '2019-10-05 22:39:50', 1, 'Q1441074'),
(20856, 'Fortul', 2881, 'ARA', 48, 'CO', 6.74611000, -71.85667000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1442438'),
(20857, 'Fosca', 2875, 'CUN', 48, 'CO', 4.33916000, -73.93852000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1577716'),
(20858, 'Francisco Pizarro', 2897, 'NAR', 48, 'CO', 2.04060000, -78.65877000, '2019-10-05 17:09:55', '2019-10-05 17:09:55', 1, 'Q1578185'),
(20859, 'Fredonia', 2890, 'ANT', 48, 'CO', 5.89239000, -75.68546000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1442002'),
(20860, 'Fresno', 2883, 'TOL', 48, 'CO', 5.15264000, -75.03624000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2431919'),
(20861, 'Frontino', 2890, 'ANT', 48, 'CO', 6.77133000, -76.13324000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q636963'),
(20863, 'Fuente de Oro', 2878, 'MET', 48, 'CO', 3.46263000, -73.62162000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1576131'),
(20864, 'Fundación', 2886, 'MAG', 48, 'CO', 10.52066000, -74.18504000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q303437'),
(20865, 'Funes', 2897, 'NAR', 48, 'CO', 1.00075000, -77.44918000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1574953'),
(20866, 'Funza', 2875, 'CUN', 48, 'CO', 4.71638000, -74.21195000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q608955'),
(20867, 'Fusagasugá', 2875, 'CUN', 48, 'CO', 4.33646000, -74.36378000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q2077525'),
(20869, 'Fúquene', 2875, 'CUN', 48, 'CO', 5.41988000, -73.76997000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1656191'),
(20870, 'Gachala', 2875, 'CUN', 48, 'CO', 4.66667000, -73.50000000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1656191'),
(20872, 'Gachancipá', 2875, 'CUN', 48, 'CO', 4.99111000, -73.87154000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1575577'),
(20873, 'Gachantivá', 2903, 'BOY', 48, 'CO', 5.74417000, -73.54253000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1656242'),
(20874, 'Gachetá', 2875, 'CUN', 48, 'CO', 4.87104000, -73.61730000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1577746'),
(20876, 'Galapa', 2880, 'ATL', 48, 'CO', 10.89790000, -74.88700000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2307260'),
(20877, 'Galeras', 2902, 'SUC', 48, 'CO', 9.16095000, -75.04811000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q389099'),
(20878, 'Galán', 2901, 'SAN', 48, 'CO', 6.66271000, -73.34233000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1492832'),
(20879, 'Gama', 2875, 'CUN', 48, 'CO', 4.76288000, -73.61091000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1656344'),
(20880, 'Gamarra', 2899, 'CES', 48, 'CO', 8.32969000, -73.71773000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1525797'),
(20881, 'Gambita', 2901, 'SAN', 48, 'CO', 5.90273000, -73.36788000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1655623'),
(20882, 'Gameza', 2903, 'BOY', 48, 'CO', 5.80313000, -72.73721000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1655623'),
(20883, 'Garagoa', 2903, 'BOY', 48, 'CO', 5.08236000, -73.36334000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1524837'),
(20884, 'Ginebra', 2904, 'VAC', 48, 'CO', 3.74296000, -76.19412000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1525670'),
(20885, 'Giraldo', 2890, 'ANT', 48, 'CO', 6.66667000, -75.95346000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1655837'),
(20886, 'Girardot', 2875, 'CUN', 48, 'CO', 4.31802000, -74.83504000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q186193'),
(20888, 'Girardota', 2890, 'ANT', 48, 'CO', 6.36789000, -75.46231000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q774725'),
(20889, 'Girón', 2901, 'SAN', 48, 'CO', 7.06820000, -73.16981000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1093497'),
(20890, 'González', 2899, 'CES', 48, 'CO', 8.36735000, -73.40032000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1577059'),
(20891, 'Gramalote', 2877, 'NSA', 48, 'CO', 7.88752000, -72.79749000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1576677'),
(20892, 'Granada', 2875, 'CUN', 48, 'CO', 5.06667000, -74.56667000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q23690741'),
(20893, 'Granada', 2890, 'ANT', 48, 'CO', 6.14353000, -75.18532000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1578735'),
(20894, 'Granada', 2878, 'MET', 48, 'CO', 3.54625000, -73.70687000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2101383'),
(20895, 'Guaca', 2901, 'SAN', 48, 'CO', 6.87621000, -72.85594000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q948769'),
(20896, 'Guacamayas', 2903, 'BOY', 48, 'CO', 6.44599000, -72.51676000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q963379'),
(20897, 'Guacarí', 2904, 'VAC', 48, 'CO', 3.76383000, -76.33292000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q2431973'),
(20898, 'Guachetá', 2875, 'CUN', 48, 'CO', 5.38425000, -73.68617000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q955108'),
(20899, 'Guachucal', 2897, 'NAR', 48, 'CO', 0.96093000, -77.73161000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1526179'),
(20900, 'Guadalajara de Buga', 2904, 'VAC', 48, 'CO', 3.90089000, -76.29783000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q934753'),
(20901, 'Guadalupe', 2890, 'ANT', 48, 'CO', 6.81449000, -75.24063000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2315211'),
(20902, 'Guadalupe', 2901, 'SAN', 48, 'CO', 6.24640000, -73.41833000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2432014'),
(20903, 'Guaduas', 2875, 'CUN', 48, 'CO', 5.07430000, -74.59854000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q685990'),
(20904, 'Guaitarilla', 2897, 'NAR', 48, 'CO', 1.13103000, -77.54815000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1525820'),
(20905, 'Gualmatán', 2897, 'NAR', 48, 'CO', 0.91992000, -77.56738000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1655544'),
(20906, 'Guamal', 2878, 'MET', 48, 'CO', 3.88043000, -73.76566000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q949695'),
(20907, 'Guamal', 2886, 'MAG', 48, 'CO', 9.14334000, -74.22384000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1441081'),
(20908, 'Guamo', 2883, 'TOL', 48, 'CO', 4.07457000, -74.97689000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q664349'),
(20909, 'Guapi', 2884, 'CAU', 48, 'CO', 2.55210000, -77.87865000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2432113'),
(20910, 'Guapotá', 2901, 'SAN', 48, 'CO', 6.30697000, -73.32848000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1655667'),
(20912, 'Guaranda', 2902, 'SUC', 48, 'CO', 8.46746000, -74.53617000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1526247'),
(20913, 'Guarne', 2890, 'ANT', 48, 'CO', 6.28345000, -75.44363000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2088069'),
(20914, 'Guasca', 2875, 'CUN', 48, 'CO', 4.86601000, -73.87748000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q179672'),
(20915, 'Guatapé', 2890, 'ANT', 48, 'CO', 6.25548000, -75.16432000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1578769'),
(20916, 'Guataquí', 2875, 'CUN', 48, 'CO', 4.51573000, -74.78935000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1654896'),
(20917, 'Guatavita', 2875, 'CUN', 48, 'CO', 4.93658000, -73.83314000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1576750'),
(20918, 'Guateque', 2903, 'BOY', 48, 'CO', 5.00619000, -73.47274000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1577072'),
(20919, 'Guavatá', 2901, 'SAN', 48, 'CO', 5.95502000, -73.70018000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q597693'),
(20921, 'Guayabal de Siquima', 2875, 'CUN', 48, 'CO', 4.87881000, -74.48306000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q584880'),
(20923, 'Guayabetal', 2875, 'CUN', 48, 'CO', 4.21472000, -73.81719000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1656121'),
(20924, 'Guayatá', 2903, 'BOY', 48, 'CO', 4.96417000, -73.48750000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1577926'),
(20925, 'Gutiérrez', 2875, 'CUN', 48, 'CO', 4.18486000, -74.01168000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1654682'),
(20926, 'Guática', 2879, 'RIS', 48, 'CO', 5.31569000, -75.79826000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1526252'),
(20930, 'Génova', 2874, 'QUI', 48, 'CO', 4.22743000, -75.75480000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q968063'),
(20931, 'Gómez Plata', 2890, 'ANT', 48, 'CO', 6.68178000, -75.21907000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1576474'),
(20932, 'Güepsa', 2901, 'SAN', 48, 'CO', 6.02505000, -73.57313000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1560045'),
(20933, 'Güicán', 2903, 'BOY', 48, 'CO', 6.46554000, -72.41539000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1577669'),
(20934, 'Hacarí', 2877, 'NSA', 48, 'CO', 8.32333000, -73.14889000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1577521'),
(20935, 'Hatillo de Loba', 2893, 'BOL', 48, 'CO', 8.95635000, -74.07819000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q369599'),
(20936, 'Hato', 2901, 'SAN', 48, 'CO', 6.56113000, -73.35895000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1655700'),
(20937, 'Hato Corozal', 2892, 'CAS', 48, 'CO', 6.15472222, -71.76527888, '2019-10-05 17:09:51', '2019-10-05 17:09:51', 1, 'Q1575569'),
(20938, 'Hatonuevo', 2889, 'LAG', 48, 'CO', 11.06940000, -72.76690000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1655700'),
(20939, 'Heliconia', 2890, 'ANT', 48, 'CO', 6.20831000, -75.73565000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1576041'),
(20940, 'Herrán', 2877, 'NSA', 48, 'CO', 7.50611000, -72.48332000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1655591'),
(20941, 'Herveo', 2883, 'TOL', 48, 'CO', 5.08004000, -75.17556000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q23689015'),
(20942, 'Hispania', 2890, 'ANT', 48, 'CO', 5.79925000, -75.90718000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1654573'),
(20943, 'Honda', 2883, 'TOL', 48, 'CO', 5.20856000, -74.73584000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q23689029'),
(20944, 'Ibagué', 2883, 'TOL', 48, 'CO', 4.43889000, -75.23222000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q222755'),
(20945, 'Icononzo', 2883, 'TOL', 48, 'CO', 4.17698000, -74.53254000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1525390'),
(20946, 'Iles', 2897, 'NAR', 48, 'CO', 0.97040000, -77.52146000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1575358'),
(20947, 'Imués', 2897, 'NAR', 48, 'CO', 1.05516000, -77.49669000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1575329'),
(20948, 'Inzá', 2884, 'CAU', 48, 'CO', 2.55452000, -76.06722000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1441907'),
(20949, 'Inírida', 2882, 'GUA', 48, 'CO', 3.86528000, -67.92389000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q130281'),
(20950, 'Ipiales', 2897, 'NAR', 48, 'CO', 0.82501000, -77.63966000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1010094'),
(20952, 'Istmina', 2876, 'CHO', 48, 'CO', 5.16054000, -76.68397000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1441410'),
(20953, 'Itagui', 2890, 'ANT', 48, 'CO', 6.17874000, -75.63117000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1441410'),
(20955, 'Ituango', 2890, 'ANT', 48, 'CO', 7.17117000, -75.76404000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1441937'),
(20956, 'Iza', 2903, 'BOY', 48, 'CO', 5.61203000, -72.97930000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1655524'),
(20957, 'Jambaló', 2884, 'CAU', 48, 'CO', 2.85065000, -76.32506000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1525285'),
(20958, 'Jamundí', 2904, 'VAC', 48, 'CO', 3.26074000, -76.53499000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1681497'),
(20959, 'Jardín', 2890, 'ANT', 48, 'CO', 5.59038000, -75.81846000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1525138'),
(20960, 'Jenesano', 2903, 'BOY', 48, 'CO', 5.38541000, -73.36364000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1577759'),
(20961, 'Jericó', 2890, 'ANT', 48, 'CO', 5.79211000, -75.78601000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1525982'),
(20962, 'Jericó', 2903, 'BOY', 48, 'CO', 6.14577000, -72.58598000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q2408710'),
(20963, 'Jerusalén', 2875, 'CUN', 48, 'CO', 4.56309000, -74.69519000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q23689265'),
(20964, 'Jesús María', 2901, 'SAN', 48, 'CO', 5.87715000, -73.78097000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q731893'),
(20965, 'Jordán', 2901, 'SAN', 48, 'CO', 6.69857000, -73.10966000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q726802'),
(20966, 'Juan de Acosta', 2880, 'ATL', 48, 'CO', 10.82930000, -75.03346000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q2320402'),
(20968, 'Junín', 2875, 'CUN', 48, 'CO', 4.70725000, -73.69549000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1579341'),
(20969, 'Juradó', 2876, 'CHO', 48, 'CO', 7.10421000, -77.76200000, '2019-10-05 22:39:51', '2020-05-01 17:22:40', 1, 'Q1655005'),
(20970, 'La Apartada', 2898, 'COR', 48, 'CO', 8.04787000, -75.30097000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1525412'),
(20971, 'La Belleza', 2901, 'SAN', 48, 'CO', 5.86371000, -73.96167000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1575306'),
(20972, 'La Calera', 2875, 'CUN', 48, 'CO', 4.68678000, -73.93590000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1440823'),
(20973, 'La Capilla', 2903, 'BOY', 48, 'CO', 5.08076000, -73.48076000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1654986'),
(20974, 'La Ceja', 2890, 'ANT', 48, 'CO', 6.00000000, -75.43311000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1946679'),
(20975, 'La Celia', 2879, 'RIS', 48, 'CO', 5.00332000, -76.00355000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1576175'),
(20977, 'La Chorrera', 2895, 'AMA', 48, 'CO', -1.48894000, -72.72935000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1576175'),
(20978, 'La Cruz', 2897, 'NAR', 48, 'CO', 1.60221000, -76.97130000, '2019-10-05 22:39:51', '2019-10-05 22:39:51', 1, 'Q1525434'),
(20979, 'La Cumbre', 2904, 'VAC', 48, 'CO', 3.72250000, -76.02083000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q23706364'),
(20981, 'La Dorada', 2887, 'CAL', 48, 'CO', 5.53333000, -74.70000000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q2279516'),
(20982, 'La Esperanza', 2877, 'NSA', 48, 'CO', 8.21043000, -72.46399000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q23688398'),
(20983, 'La Estrella', 2890, 'ANT', 48, 'CO', 6.14239000, -75.65257000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q932102'),
(20984, 'La Florida', 2897, 'NAR', 48, 'CO', 1.35693000, -77.41271000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1577471'),
(20985, 'La Gloria', 2899, 'CES', 48, 'CO', 8.61160000, -73.63045000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1526106'),
(20986, 'La Guadalupe', 2882, 'GUA', 48, 'CO', 1.39579000, -67.00150000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1526106'),
(20987, 'La Jagua de Ibirico', 2899, 'CES', 48, 'CO', 9.56228000, -73.33405000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q950053'),
(20988, 'La Jagua del Pilar', 2889, 'LAG', 48, 'CO', 10.51061000, -73.07178000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1656023'),
(20989, 'La Llanada', 2897, 'NAR', 48, 'CO', 1.47310000, -77.58024000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1577486'),
(20990, 'La Macarena', 2878, 'MET', 48, 'CO', 2.00702000, -74.07320000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1577486'),
(20992, 'La Merced', 2887, 'CAL', 48, 'CO', 5.38060000, -75.58842000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q23688526'),
(20993, 'La Mesa', 2875, 'CUN', 48, 'CO', 4.65374000, -74.47316000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q936790'),
(20994, 'La Montañita', 2891, 'CAQ', 48, 'CO', 1.46389000, -75.30363000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q1441926'),
(20995, 'La Palma', 2875, 'CUN', 48, 'CO', 5.31732000, -74.43003000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1578797'),
(20996, 'La Paz', 2901, 'SAN', 48, 'CO', 6.17848000, -73.58948000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q23688578'),
(20997, 'La Paz', 2899, 'CES', 48, 'CO', 10.10487000, -73.22056000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1442342'),
(20998, 'La Pedrera', 2895, 'AMA', 48, 'CO', -1.65596000, -70.22186000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1442342'),
(20999, 'La Peña', 2875, 'CUN', 48, 'CO', 5.19847000, -74.39368000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q23688588'),
(21000, 'La Pintada', 2890, 'ANT', 48, 'CO', 5.74867000, -75.60626000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1576562'),
(21001, 'La Playa', 2877, 'NSA', 48, 'CO', 8.25833000, -73.19222000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1971807'),
(21002, 'La Primavera', 2894, 'VID', 48, 'CO', 5.49056000, -70.40917000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q941906'),
(21003, 'La Sierra', 2884, 'CAU', 48, 'CO', 2.19665000, -76.78590000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q508563'),
(21004, 'La Tebaida', 2874, 'QUI', 48, 'CO', 4.43353000, -75.81476000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q2432300'),
(21005, 'La Tola', 2897, 'NAR', 48, 'CO', 2.39949000, -78.18923000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1577045'),
(21006, 'La Unión', 2904, 'VAC', 48, 'CO', 4.53282000, -76.10318000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q2022508'),
(21007, 'La Unión', 2902, 'SUC', 48, 'CO', 8.84965000, -75.27942000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q684303'),
(21008, 'La Unión', 2897, 'NAR', 48, 'CO', 1.60450000, -77.13152000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q23687682'),
(21009, 'La Unión', 2890, 'ANT', 48, 'CO', 5.94642000, -75.35917000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q2143798'),
(21010, 'La Uvita', 2903, 'BOY', 48, 'CO', 6.24262000, -72.56448000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1654706'),
(21011, 'La Vega', 2884, 'CAU', 48, 'CO', 2.06201000, -76.76808000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q2309024'),
(21012, 'La Vega', 2875, 'CUN', 48, 'CO', 4.97382000, -74.34476000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q2022377'),
(21013, 'La Victoria', 2904, 'VAC', 48, 'CO', 4.52483000, -76.03921000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1526230'),
(21014, 'La Victoria', 2895, 'AMA', 48, 'CO', -0.18311000, -71.03760000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1526230'),
(21015, 'La Victoria', 2903, 'BOY', 48, 'CO', 5.52278000, -74.23280000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1526230'),
(21016, 'La Virginia', 2879, 'RIS', 48, 'CO', 4.89972000, -75.88250000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q23687719'),
(21017, 'Labateca', 2877, 'NSA', 48, 'CO', 7.24558000, -72.55286000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1575676'),
(21018, 'Labranzagrande', 2903, 'BOY', 48, 'CO', 5.56223000, -72.57499000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1654772'),
(21019, 'Landázuri', 2901, 'SAN', 48, 'CO', 6.21826000, -73.81121000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q612555'),
(21021, 'Lebrija', 2901, 'SAN', 48, 'CO', 7.11317000, -73.21780000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q23687730'),
(21024, 'Leiva', 2897, 'NAR', 48, 'CO', 1.93497000, -77.30634000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q1526208'),
(21025, 'Lejanías', 2878, 'MET', 48, 'CO', 3.52762000, -74.02335000, '2019-10-05 22:39:52', '2020-05-01 17:22:40', 1, 'Q1577508'),
(21026, 'Lenguazaque', 2875, 'CUN', 48, 'CO', 5.30711000, -73.71152000, '2019-10-05 22:39:52', '2019-10-05 22:39:52', 1, 'Q516210'),
(21027, 'Leticia', 2895, 'AMA', 48, 'CO', -4.21528000, -69.94056000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q214913'),
(21028, 'Liborina', 2890, 'ANT', 48, 'CO', 6.67790000, -75.81218000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1576529'),
(21029, 'Linares', 2897, 'NAR', 48, 'CO', 1.35078000, -77.52339000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1526262'),
(21030, 'Lloró', 2876, 'CHO', 48, 'CO', 5.49605000, -76.54945000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1575268'),
(21031, 'Lorica', 2898, 'COR', 48, 'CO', 9.23648000, -75.81350000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q23687836'),
(21032, 'Los Andes', 2897, 'NAR', 48, 'CO', 1.49474000, -77.52136000, '2019-10-05 17:09:56', '2019-10-05 17:09:56', 1, 'Q1526257'),
(21033, 'Los Córdobas', 2898, 'COR', 48, 'CO', 8.89403000, -76.35455000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1525401'),
(21034, 'Los Palmitos', 2902, 'SUC', 48, 'CO', 9.37899000, -75.26769000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q595791'),
(21035, 'Los Patios', 2877, 'NSA', 48, 'CO', 7.83793000, -72.50370000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q634066'),
(21036, 'Los Santos', 2901, 'SAN', 48, 'CO', 6.79734000, -73.12490000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1574960'),
(21037, 'Lourdes', 2877, 'NSA', 48, 'CO', 7.96774000, -72.84519000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1656799'),
(21038, 'Luruaco', 2880, 'ATL', 48, 'CO', 10.61712000, -75.15146000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2308661'),
(21039, 'Lérida', 2883, 'TOL', 48, 'CO', 4.86242000, -74.90977000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q23687756'),
(21040, 'Líbano', 2883, 'TOL', 48, 'CO', 4.83560000, -75.10834000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1256428'),
(21041, 'López de Micay', 2884, 'CAU', 48, 'CO', 3.00000000, -77.25000000, '2019-10-05 17:09:53', '2020-05-01 11:52:40', 1, 'Q2297415'),
(21042, 'Macanal', 2903, 'BOY', 48, 'CO', 4.95050000, -73.32029000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1656366'),
(21043, 'Macaravita', 2901, 'SAN', 48, 'CO', 6.50567000, -72.59299000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1655505'),
(21044, 'Maceo', 2890, 'ANT', 48, 'CO', 6.55196000, -74.78741000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1576486'),
(21045, 'Macheta', 2875, 'CUN', 48, 'CO', 5.08333000, -73.61667000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1576486'),
(21047, 'Madrid', 2875, 'CUN', 48, 'CO', 4.75564000, -74.27708000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2316396'),
(21048, 'Magangué', 2893, 'BOL', 48, 'CO', 9.24202000, -74.75467000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1011196'),
(21050, 'Mahates', 2893, 'BOL', 48, 'CO', 10.23293000, -75.18985000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1440795'),
(21051, 'Maicao', 2889, 'LAG', 48, 'CO', 11.37837000, -72.23950000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q23685927'),
(21052, 'Majagual', 2902, 'SUC', 48, 'CO', 8.53500000, -74.71499000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2173993'),
(21053, 'Malambo', 2880, 'ATL', 48, 'CO', 10.85953000, -74.77386000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q23685951'),
(21054, 'Mallama', 2897, 'NAR', 48, 'CO', 1.14109000, -77.86479000, '2019-10-05 17:09:54', '2019-10-05 17:09:54', 1, 'Q1576117'),
(21055, 'Manatí', 2880, 'ATL', 48, 'CO', 10.46402000, -74.98220000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q2432352'),
(21056, 'Manaure', 2889, 'LAG', 48, 'CO', 11.77505000, -72.44447000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q635230'),
(21058, 'Manaure Balcón del Cesar', 2899, 'CES', 48, 'CO', 10.39278000, -73.03250000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q936283'),
(21059, 'Manizales', 2887, 'CAL', 48, 'CO', 5.06889000, -75.51738000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q235190'),
(21060, 'Manta', 2875, 'CUN', 48, 'CO', 4.95720000, -73.58583000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1654947'),
(21061, 'Manzanares', 2887, 'CAL', 48, 'CO', 5.23305000, -75.15168000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1442335'),
(21062, 'Maní', 2892, 'CAS', 48, 'CO', 4.81638000, -72.27946000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1579380'),
(21063, 'Mapiripana', 2882, 'GUA', 48, 'CO', 2.81003000, -70.28568000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1579380'),
(21064, 'Mapiripán', 2878, 'MET', 48, 'CO', 2.89115000, -72.13328000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1525460'),
(21065, 'Margarita', 2893, 'BOL', 48, 'CO', 9.06325000, -74.28134000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1575498'),
(21066, 'Marinilla', 2890, 'ANT', 48, 'CO', 6.19609000, -75.30313000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2088084'),
(21067, 'Maripí', 2903, 'BOY', 48, 'CO', 5.55194000, -74.00861000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1574705'),
(21068, 'Marmato', 2887, 'CAL', 48, 'CO', 5.47554000, -75.59710000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1577890'),
(21069, 'Marquetalia', 2887, 'CAL', 48, 'CO', 5.29069000, -75.08028000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q925735'),
(21070, 'Marsella', 2879, 'RIS', 48, 'CO', 4.95238000, -75.75265000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1441429'),
(21071, 'Marulanda', 2887, 'CAL', 48, 'CO', 5.28393000, -75.26016000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1654886'),
(21072, 'María la Baja', 2893, 'BOL', 48, 'CO', 9.99228000, -75.34364000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q597264'),
(21073, 'Matanza', 2901, 'SAN', 48, 'CO', 7.35605000, -73.05303000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1574806'),
(21074, 'Medellín', 2890, 'ANT', 48, 'CO', 6.25184000, -75.56359000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q48278'),
(21075, 'Medina', 2875, 'CUN', 48, 'CO', 4.51005000, -73.34982000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1579406'),
(21076, 'Medio Atrato', 2876, 'CHO', 48, 'CO', 5.99500000, -76.78250000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1442444'),
(21077, 'Medio Baudó', 2876, 'CHO', 48, 'CO', 5.05000000, -77.05000000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1525732'),
(21078, 'Medio San Juan', 2876, 'CHO', 48, 'CO', 5.09278000, -76.69528000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1526086'),
(21079, 'Melgar', 2883, 'TOL', 48, 'CO', 4.20475000, -74.64075000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q23686372'),
(21080, 'Mercaderes', 2884, 'CAU', 48, 'CO', 1.80175000, -77.17032000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1526078'),
(21081, 'Mesetas', 2878, 'MET', 48, 'CO', 3.38463000, -74.04424000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1574693'),
(21082, 'Milán', 2891, 'CAQ', 48, 'CO', 1.29034000, -75.50757000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1576645'),
(21083, 'Miraflores', 2888, 'GUV', 48, 'CO', 1.33667000, -71.95111000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q980001'),
(21084, 'Miraflores', 2903, 'BOY', 48, 'CO', 5.12216000, -73.21212000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2266341'),
(21085, 'Miranda', 2884, 'CAU', 48, 'CO', 3.23773000, -76.22093000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2308999'),
(21086, 'Miriti - Paraná', 2895, 'AMA', 48, 'CO', -0.81858000, -70.78925000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q2308999'),
(21087, 'Mistrató', 2879, 'RIS', 48, 'CO', 5.29622000, -75.88390000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q923166'),
(21088, 'Mitú', 2885, 'VAU', 48, 'CO', 1.25778000, -70.23472000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q130242'),
(21089, 'Mocoa', 2896, 'PUT', 48, 'CO', 1.15284000, -76.65208000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q579803'),
(21090, 'Mogotes', 2901, 'SAN', 48, 'CO', 6.47559000, -72.97046000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1577177'),
(21091, 'Molagavita', 2901, 'SAN', 48, 'CO', 6.67315000, -72.80875000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1575454'),
(21092, 'Momil', 2898, 'COR', 48, 'CO', 9.23767000, -75.67489000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q24831'),
(21093, 'Mompós', 2893, 'BOL', 48, 'CO', 9.13990000, -74.54513000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q24831'),
(21094, 'Mongua', 2903, 'BOY', 48, 'CO', 5.75084000, -72.80339000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1656199'),
(21095, 'Monguí', 2903, 'BOY', 48, 'CO', 5.69752000, -72.83359000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1654539'),
(21096, 'Moniquirá', 2903, 'BOY', 48, 'CO', 5.84315000, -73.57775000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1440715'),
(21097, 'Montebello', 2890, 'ANT', 48, 'CO', 5.93703000, -75.52741000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1577236'),
(21098, 'Montecristo', 2893, 'BOL', 48, 'CO', 8.29710000, -74.47330000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1526097'),
(21099, 'Montelíbano', 2898, 'COR', 48, 'CO', 7.97917000, -75.42020000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1787546'),
(21100, 'Montenegro', 2874, 'QUI', 48, 'CO', 4.52676000, -75.82265000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1256403'),
(21101, 'Monterrey', 2892, 'CAS', 48, 'CO', 4.82015000, -72.87901000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1525276'),
(21102, 'Montería', 2898, 'COR', 48, 'CO', 8.74798000, -75.88143000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q852725'),
(21103, 'Morales', 2884, 'CAU', 48, 'CO', 2.75446000, -76.62791000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q23704478'),
(21104, 'Morales', 2893, 'BOL', 48, 'CO', 8.27520000, -73.86884000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2432408'),
(21105, 'Morelia', 2891, 'CAQ', 48, 'CO', 1.48747000, -75.72581000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1654632'),
(21106, 'Morichal', 2882, 'GUA', 48, 'CO', 2.42980000, -69.83284000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1654632'),
(21107, 'Morroa', 2902, 'SUC', 48, 'CO', 9.33348000, -75.30542000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1526204'),
(21108, 'Mosquera', 2897, 'NAR', 48, 'CO', 2.49139000, -78.44307000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2432971'),
(21109, 'Mosquera', 2875, 'CUN', 48, 'CO', 4.68935000, -74.23599000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q2432909'),
(21110, 'Motavita', 2903, 'BOY', 48, 'CO', 5.57655000, -73.36696000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1577790'),
(21112, 'Moñitos', 2898, 'COR', 48, 'CO', 9.22615000, -76.13594000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1441218'),
(21117, 'San Pedro de los Milagros', 2890, 'ANT', 48, 'CO', 6.45944444, -75.55777888, '2019-10-05 17:09:53', '2019-10-05 17:09:53', 1, 'Q1441754'),
(21119, 'Murillo', 2883, 'TOL', 48, 'CO', 4.87393000, -75.17151000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q23687025'),
(21120, 'Murindó', 2890, 'ANT', 48, 'CO', 6.84698000, -76.71544000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1655857'),
(21121, 'Mutatá', 2890, 'ANT', 48, 'CO', 7.24407000, -76.43564000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1525997'),
(21122, 'Mutiscua', 2877, 'NSA', 48, 'CO', 7.29810000, -72.79879000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1656003'),
(21123, 'Muzo', 2903, 'BOY', 48, 'CO', 5.53528000, -74.10778000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1431785'),
(21124, 'Málaga', 2901, 'SAN', 48, 'CO', 6.69903000, -72.73233000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q23685947'),
(21125, 'Nariño', 2897, 'NAR', 48, 'CO', 1.28995000, -77.35721000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q2433055'),
(21126, 'Nariño', 2890, 'ANT', 48, 'CO', 5.60893000, -75.17656000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q590382'),
(21127, 'Nariño', 2875, 'CUN', 48, 'CO', 4.39913000, -74.82239000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1847862'),
(21128, 'Natagaima', 2883, 'TOL', 48, 'CO', 3.55212000, -75.11345000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q258673'),
(21129, 'Nechí', 2890, 'ANT', 48, 'CO', 8.09419000, -74.77573000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q1441743'),
(21130, 'Necoclí', 2890, 'ANT', 48, 'CO', 8.42627000, -76.78926000, '2019-10-05 22:39:53', '2020-05-01 17:22:40', 1, 'Q976938'),
(21131, 'Neira', 2887, 'CAL', 48, 'CO', 5.17534000, -75.52405000, '2019-10-05 22:39:53', '2019-10-05 22:39:53', 1, 'Q1393029'),
(21132, 'Nemocón', 2875, 'CUN', 48, 'CO', 5.06767000, -73.87769000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1574741'),
(21133, 'Nilo', 2875, 'CUN', 48, 'CO', 4.30604000, -74.62083000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1524825'),
(21134, 'Nimaima', 2875, 'CUN', 48, 'CO', 5.12614000, -74.38495000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1654819'),
(21135, 'Nobsa', 2903, 'BOY', 48, 'CO', 5.76978000, -72.94099000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1524817'),
(21136, 'Nocaima', 2875, 'CUN', 48, 'CO', 5.06696000, -74.38439000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1577029'),
(21137, 'Norcasia', 2887, 'CAL', 48, 'CO', 5.57535000, -74.88831000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q921734'),
(21138, 'Norosí', 2893, 'BOL', 48, 'CO', 8.52692000, -74.03736000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q2565188'),
(21139, 'Nueva Granada', 2886, 'MAG', 48, 'CO', 9.80168000, -74.39304000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1526240'),
(21140, 'Nuevo Colón', 2903, 'BOY', 48, 'CO', 5.35368000, -73.45660000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1577145'),
(21141, 'Nunchía', 2892, 'CAS', 48, 'CO', 5.63589000, -72.19543000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1575548'),
(21142, 'Nuquí', 2876, 'CHO', 48, 'CO', 5.71250000, -77.27083000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1577833'),
(21143, 'Nóvita', 2876, 'CHO', 48, 'CO', 4.95511000, -76.60526000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1579423'),
(21144, 'Obando', 2904, 'VAC', 48, 'CO', 4.59590000, -75.94879000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q676377'),
(21145, 'Ocamonte', 2901, 'SAN', 48, 'CO', 6.34001000, -73.12205000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1655496'),
(21146, 'Ocaña', 2877, 'NSA', 48, 'CO', 8.23773000, -73.35604000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q2283773'),
(21147, 'Oiba', 2901, 'SAN', 48, 'CO', 6.26387000, -73.29876000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1577577'),
(21148, 'Oicatá', 2903, 'BOY', 48, 'CO', 5.59548000, -73.30820000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1656214'),
(21149, 'Olaya', 2890, 'ANT', 48, 'CO', 6.62773000, -75.81270000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1656222'),
(21150, 'Olaya Herrera', 2897, 'NAR', 48, 'CO', 1.24803000, -77.49085000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q23685413'),
(21151, 'Onzaga', 2901, 'SAN', 48, 'CO', 6.34434000, -72.81726000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2245327'),
(21152, 'Orito', 2896, 'PUT', 48, 'CO', 0.66749000, -76.87297000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q12981826'),
(21153, 'Orocué', 2892, 'CAS', 48, 'CO', 4.79035000, -71.33917000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1576308'),
(21154, 'Ortega', 2883, 'TOL', 48, 'CO', 3.94514000, -75.27245000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2433233'),
(21155, 'Ospina', 2897, 'NAR', 48, 'CO', 1.02962000, -77.55252000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1578953'),
(21156, 'Otanche', 2903, 'BOY', 48, 'CO', 5.77534000, -74.20315000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1577588'),
(21157, 'Ovejas', 2902, 'SUC', 48, 'CO', 9.54083000, -75.18333000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1441134'),
(21158, 'Pachavita', 2903, 'BOY', 48, 'CO', 5.13969000, -73.39739000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1654528'),
(21159, 'Pacho', 2875, 'CUN', 48, 'CO', 5.13055555, -74.15833333, '2019-10-05 17:09:54', '2020-08-15 12:21:47', 1, 'Q680909'),
(21160, 'Pacoa', 2885, 'VAU', 48, 'CO', 0.15636000, -70.89274000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1654444'),
(21161, 'Padilla', 2884, 'CAU', 48, 'CO', 3.22038000, -76.31385000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2433292'),
(21163, 'Páez', 2884, 'CAU', 48, 'CO', 2.64644000, -75.97269000, '2019-10-05 17:09:45', '2020-05-01 11:52:40', 1, 'Q1391859'),
(21164, 'Pailitas', 2899, 'CES', 48, 'CO', 8.95652000, -73.62548000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1525724'),
(21165, 'Paime', 2875, 'CUN', 48, 'CO', 5.37054000, -74.15219000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q597973'),
(21166, 'Paipa', 2903, 'BOY', 48, 'CO', 5.78013000, -73.11708000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1391867'),
(21168, 'Pajarito', 2903, 'BOY', 48, 'CO', 5.34751000, -72.72075000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q935494'),
(21169, 'Palestina', 2887, 'CAL', 48, 'CO', 5.04571000, -75.69552000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1524756'),
(21170, 'Palmar', 2901, 'SAN', 48, 'CO', 6.49740000, -73.30740000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1654701'),
(21171, 'Palmar de Varela', 2880, 'ATL', 48, 'CO', 10.74055000, -74.75443000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1023805'),
(21172, 'Palmas del Socorro', 2901, 'SAN', 48, 'CO', 6.40756000, -73.28824000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1656731'),
(21173, 'Palmira', 2904, 'VAC', 48, 'CO', 3.53969000, -76.22607000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q989858'),
(21174, 'Palmito', 2902, 'SUC', 48, 'CO', 9.33667000, -75.56333000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1577493'),
(21175, 'Palocabildo', 2883, 'TOL', 48, 'CO', 5.11705000, -75.01732000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1575427'),
(21176, 'Pamplona', 2877, 'NSA', 48, 'CO', 7.37565000, -72.64795000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q23685652'),
(21177, 'Pamplonita', 2877, 'NSA', 48, 'CO', 7.45814000, -72.65965000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1655477'),
(21178, 'Pana Pana', 2882, 'GUA', 48, 'CO', 1.96249000, -69.12600000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1655477'),
(21179, 'Pandi', 2875, 'CUN', 48, 'CO', 4.18032000, -74.47099000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1656274'),
(21180, 'Panqueba', 2903, 'BOY', 48, 'CO', 6.44533000, -72.46268000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1656207'),
(21181, 'Papunaua', 2885, 'VAU', 48, 'CO', 1.68335000, -70.70968000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q962671'),
(21182, 'Paratebueno', 2875, 'CUN', 48, 'CO', 4.37575000, -73.21547000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q932870'),
(21183, 'Pasca', 2875, 'CUN', 48, 'CO', 4.30722000, -74.30056000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2241175'),
(21184, 'Pasto', 2897, 'NAR', 48, 'CO', 1.21467000, -77.27865000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q320015'),
(21185, 'Patía', 2884, 'CAU', 48, 'CO', 2.14018000, -77.01744000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1256878'),
(21186, 'Pauna', 2903, 'BOY', 48, 'CO', 5.65861000, -73.98250000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1577120'),
(21187, 'Paya', 2903, 'BOY', 48, 'CO', 5.62492000, -72.42345000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2022761'),
(21189, 'Magüí Payán', 2897, 'NAR', 48, 'CO', 1.76645000, -78.18326000, '2019-10-05 17:09:54', '2020-05-01 11:52:40', 1, 'Q1525449'),
(21190, 'Paz de Río', 2903, 'BOY', 48, 'CO', 6.00219000, -72.78857000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q2279482'),
(21191, 'Pedraza', 2886, 'MAG', 48, 'CO', 10.18739000, -74.91504000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1575673'),
(21192, 'Pelaya', 2899, 'CES', 48, 'CO', 8.68819000, -73.66451000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q962740'),
(21193, 'Pensilvania', 2887, 'CAL', 48, 'CO', 5.38840000, -75.20420000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1440753'),
(21194, 'Peque', 2890, 'ANT', 48, 'CO', 7.02123000, -75.90926000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1577164'),
(21195, 'Pereira', 2879, 'RIS', 48, 'CO', 4.76896000, -75.72222000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q51111'),
(21196, 'Pesca', 2903, 'BOY', 48, 'CO', 5.50213000, -73.08794000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1575463'),
(21197, 'Peñol', 2890, 'ANT', 48, 'CO', 6.23434000, -75.22573000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1525273'),
(21199, 'Piedecuesta', 2901, 'SAN', 48, 'CO', 6.97443000, -73.02284000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2075634'),
(21201, 'Piedras', 2883, 'TOL', 48, 'CO', 4.50000000, -74.91667000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1655981'),
(21202, 'Piendamo', 2884, 'CAU', 48, 'CO', 2.63918000, -76.53055000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1655981'),
(21204, 'Pijao', 2874, 'QUI', 48, 'CO', 4.33350000, -75.70463000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q636738'),
(21205, 'Pijiño del Carmen', 2886, 'MAG', 48, 'CO', 9.32908000, -74.45302000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1525585'),
(21206, 'Pinchote', 2901, 'SAN', 48, 'CO', 6.53226000, -73.17309000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q585762'),
(21207, 'Pinillos', 2893, 'BOL', 48, 'CO', 8.91925000, -74.46771000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1442317'),
(21208, 'Piojó', 2880, 'ATL', 48, 'CO', 10.74846000, -75.10776000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q2320388'),
(21209, 'Pisba', 2903, 'BOY', 48, 'CO', 5.72396000, -72.48646000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1656260'),
(21210, 'Pivijay', 2886, 'MAG', 48, 'CO', 10.46167000, -74.61621000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q929338'),
(21212, 'Planadas', 2883, 'TOL', 48, 'CO', 3.10326000, -75.81680000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1964548'),
(21213, 'Planeta Rica', 2898, 'COR', 48, 'CO', 8.41150000, -75.58508000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q956174'),
(21214, 'Plato', 2886, 'MAG', 48, 'CO', 9.77267000, -74.74548000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1093229'),
(21216, 'Policarpa', 2897, 'NAR', 48, 'CO', 1.62843000, -77.45956000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1525852'),
(21217, 'Polonuevo', 2880, 'ATL', 48, 'CO', 10.77697000, -74.85344000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1526194'),
(21218, 'Ponedera', 2880, 'ATL', 48, 'CO', 10.64297000, -74.75393000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q512871'),
(21219, 'Popayán', 2884, 'CAU', 48, 'CO', 2.43823000, -76.61316000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q335135'),
(21220, 'Pore', 2892, 'CAS', 48, 'CO', 5.72792000, -71.99266000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1555513'),
(21221, 'Potosí', 2897, 'NAR', 48, 'CO', 0.80739000, -77.57216000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q23684150'),
(21222, 'Pradera', 2904, 'VAC', 48, 'CO', 3.42793000, -76.17159000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2433369'),
(21223, 'Prado', 2883, 'TOL', 48, 'CO', 3.73219000, -74.86494000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1576149'),
(21224, 'Providencia', 2900, 'SAP', 48, 'CO', 13.38479000, -81.37468000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q3408299'),
(21225, 'Pueblo Nuevo', 2898, 'COR', 48, 'CO', 8.24110000, -74.95815000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q23684193'),
(21226, 'Pueblo Rico', 2879, 'RIS', 48, 'CO', 5.29453000, -76.06702000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q514908'),
(21227, 'Pueblorrico', 2890, 'ANT', 48, 'CO', 5.80000000, -75.85000000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1576028'),
(21228, 'Puebloviejo', 2886, 'MAG', 48, 'CO', 10.84239000, -74.38538000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1441089'),
(21229, 'Puente Nacional', 2901, 'SAN', 48, 'CO', 5.87739000, -73.67810000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1525489'),
(21230, 'Puerres', 2897, 'NAR', 48, 'CO', 1.19374000, -77.26661000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q23684207'),
(21231, 'Puerto Alegría', 2895, 'AMA', 48, 'CO', -0.96886000, -73.74962000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q23684207'),
(21232, 'Puerto Arica', 2895, 'AMA', 48, 'CO', -1.90677000, -71.14653000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2243950'),
(21233, 'Puerto Asís', 2896, 'PUT', 48, 'CO', 0.50514000, -76.49571000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1018571'),
(21234, 'Puerto Berrío', 2890, 'ANT', 48, 'CO', 6.50749000, -74.52271000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1946640'),
(21236, 'Puerto Boyacá', 2903, 'BOY', 48, 'CO', 5.97214000, -74.46349000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q2335098'),
(21237, 'Puerto Carreño', 2894, 'VID', 48, 'CO', 6.18903000, -67.48588000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q130304'),
(21238, 'Puerto Colombia', 2880, 'ATL', 48, 'CO', 11.00854000, -74.90887000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2186176'),
(21239, 'Puerto Colombia', 2882, 'GUA', 48, 'CO', 2.43903000, -68.16419000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2186176'),
(21240, 'Puerto Concordia', 2878, 'MET', 48, 'CO', 2.62206000, -72.75724000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1525869'),
(21241, 'Puerto Escondido', 2898, 'COR', 48, 'CO', 8.99453000, -76.19351000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1441033'),
(21242, 'Puerto Gaitán', 2878, 'MET', 48, 'CO', 4.31328000, -72.08157000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1524747'),
(21243, 'Puerto Guzmán', 2896, 'PUT', 48, 'CO', 0.97028000, -76.58583000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1440780'),
(21244, 'Puerto Leguízamo', 2896, 'PUT', 48, 'CO', -0.19337000, -74.78189000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q1525407'),
(21245, 'Puerto Libertador', 2898, 'COR', 48, 'CO', 7.68181000, -75.78312000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2433456'),
(21246, 'Puerto Lleras', 2878, 'MET', 48, 'CO', 3.21794000, -73.23215000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1577863'),
(21247, 'Puerto López', 2878, 'MET', 48, 'CO', 4.04426000, -72.65731000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q2143650'),
(21248, 'Puerto Nare', 2890, 'ANT', 48, 'CO', 6.19167000, -74.58670000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q2143650'),
(21249, 'Puerto Nariño', 2895, 'AMA', 48, 'CO', -3.78889000, -70.35584000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q767738'),
(21250, 'Puerto Parra', 2901, 'SAN', 48, 'CO', 6.65149000, -74.05734000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q1575386'),
(21251, 'Puerto Rico', 2891, 'CAQ', 48, 'CO', 1.90999000, -75.15931000, '2019-10-05 22:39:54', '2019-10-05 22:39:54', 1, 'Q938384'),
(21252, 'Puerto Rico', 2878, 'MET', 48, 'CO', 2.93833000, -73.20833000, '2019-10-05 17:09:55', '2019-10-05 17:09:55', 1, 'Q1956919'),
(21253, 'Puerto Rondón', 2881, 'ARA', 48, 'CO', 6.28048000, -71.10000000, '2019-10-05 22:39:54', '2020-05-01 17:22:40', 1, 'Q924553'),
(21254, 'Puerto Salgar', 2875, 'CUN', 48, 'CO', 5.61892000, -74.58483000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1524761'),
(21255, 'Puerto Santander', 2877, 'NSA', 48, 'CO', 8.36361000, -72.40630000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1575400'),
(21256, 'Puerto Santander', 2895, 'AMA', 48, 'CO', -1.09870000, -71.93911000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1575400'),
(21257, 'Puerto Tejada', 2884, 'CAU', 48, 'CO', 3.23393000, -76.41935000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1256377'),
(21258, 'Puerto Triunfo', 2890, 'ANT', 48, 'CO', 5.87259000, -74.64050000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q932785'),
(21259, 'Puerto Wilches', 2901, 'SAN', 48, 'CO', 7.34828000, -73.89601000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q579352'),
(21261, 'Pulí', 2875, 'CUN', 48, 'CO', 4.68116000, -74.71406000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1656309'),
(21262, 'Pupiales', 2897, 'NAR', 48, 'CO', 0.87136000, -77.64027000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q368268'),
(21263, 'Puracé', 2884, 'CAU', 48, 'CO', 2.33851000, -76.38759000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q2317482'),
(21264, 'Purificación', 2883, 'TOL', 48, 'CO', 3.85871000, -74.93129000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1392469'),
(21266, 'Purísima', 2898, 'COR', 48, 'CO', 9.23657000, -75.72191000, '2019-10-05 17:09:55', '2020-05-01 11:52:40', 1, 'Q1525834'),
(21267, 'Pácora', 2887, 'CAL', 48, 'CO', 5.50631000, -75.49054000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1526108'),
(21268, 'Páez', 2903, 'BOY', 48, 'CO', 5.10112000, -73.05123000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q2433466'),
(21269, 'Páramo', 2901, 'SAN', 48, 'CO', 6.43750000, -73.18027000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q366923'),
(21270, 'Quebradanegra', 2875, 'CUN', 48, 'CO', 5.08262000, -74.52117000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1656162'),
(21271, 'Quetame', 2875, 'CUN', 48, 'CO', 4.33234000, -73.86141000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1577699'),
(21272, 'Quibdó', 2876, 'CHO', 48, 'CO', 5.76075000, -76.83701000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q23684295'),
(21273, 'Quimbaya', 2874, 'QUI', 48, 'CO', 4.59342000, -75.83758000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q2433472'),
(21274, 'Quinchía', 2879, 'RIS', 48, 'CO', 5.33957000, -75.73018000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q2433476'),
(21275, 'Quipile', 2875, 'CUN', 48, 'CO', 4.74517000, -74.53378000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q941367'),
(21276, 'Quípama', 2903, 'BOY', 48, 'CO', 5.51940000, -74.17765000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1575472'),
(21277, 'Ragonvalia', 2877, 'NSA', 48, 'CO', 7.57749000, -72.47574000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1576165'),
(21278, 'Ramiriquí', 2903, 'BOY', 48, 'CO', 5.40020000, -73.33544000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1575682'),
(21279, 'Recetor', 2892, 'CAS', 48, 'CO', 5.22947000, -72.76099000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q954584'),
(21280, 'Regidor', 2893, 'BOL', 48, 'CO', 8.72229000, -73.84891000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q23732251'),
(21281, 'Remedios', 2890, 'ANT', 48, 'CO', 7.02835000, -74.69379000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1441733'),
(21282, 'Remolino', 2886, 'MAG', 48, 'CO', 10.65048000, -74.58442000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1575711'),
(21283, 'Repelón', 2880, 'ATL', 48, 'CO', 10.50937000, -75.12830000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q2320315'),
(21284, 'Restrepo', 2878, 'MET', 48, 'CO', 4.25833000, -73.56142000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q2042902'),
(21285, 'Restrepo', 2904, 'VAC', 48, 'CO', 3.82203000, -76.52242000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q751122'),
(21286, 'Retiro', 2890, 'ANT', 48, 'CO', 6.05906000, -75.51488000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1525690'),
(21287, 'Ricaurte', 2875, 'CUN', 48, 'CO', 4.28075000, -74.76469000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q2226527'),
(21288, 'Ricaurte', 2897, 'NAR', 48, 'CO', 1.21474000, -77.99801000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q23684452'),
(21289, 'Rioblanco', 2883, 'TOL', 48, 'CO', 3.50000000, -75.83333000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q2299677'),
(21290, 'Riofrío', 2904, 'VAC', 48, 'CO', 4.15710000, -76.28852000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q23684473'),
(21291, 'Riohacha', 2889, 'LAG', 48, 'CO', 11.54444000, -72.90722000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q826566'),
(21292, 'Rionegro', 2901, 'SAN', 48, 'CO', 7.50000000, -73.33333000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q826566'),
(21293, 'Rionegro', 2890, 'ANT', 48, 'CO', 6.16667000, -75.41667000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1774644'),
(21294, 'Riosucio', 2887, 'CAL', 48, 'CO', 5.42164000, -75.70318000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1093015'),
(21295, 'Riosucio', 2876, 'CHO', 48, 'CO', 7.41667000, -77.16667000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q2196660'),
(21296, 'Risaralda', 2887, 'CAL', 48, 'CO', 5.11237000, -75.75861000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q536797'),
(21297, 'Roberto Payán', 2897, 'NAR', 48, 'CO', 1.91757000, -78.38191000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q536797'),
(21298, 'Roldanillo', 2904, 'VAC', 48, 'CO', 4.41256000, -76.15457000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1146669'),
(21299, 'Roncesvalles', 2883, 'TOL', 48, 'CO', 4.01080000, -75.60493000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q748399'),
(21300, 'Rondón', 2903, 'BOY', 48, 'CO', 5.38173000, -73.19683000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1654613'),
(21301, 'Rosas', 2884, 'CAU', 48, 'CO', 2.26093000, -76.73986000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1525805'),
(21302, 'Rovira', 2883, 'TOL', 48, 'CO', 4.21222000, -75.34210000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1441209'),
(21303, 'Ráquira', 2903, 'BOY', 48, 'CO', 5.53793000, -73.63201000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1526054'),
(21304, 'Río Iro', 2876, 'CHO', 48, 'CO', 5.18333000, -76.48330000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q646865'),
(21305, 'Río Quito', 2876, 'CHO', 48, 'CO', 5.51667000, -76.75000000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q646865'),
(21306, 'Río Viejo', 2893, 'BOL', 48, 'CO', 8.58740000, -73.83901000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1442380'),
(21307, 'Río de Oro', 2899, 'CES', 48, 'CO', 8.17879000, -73.51218000, '2019-10-05 22:39:55', '2020-05-01 17:22:40', 1, 'Q1526621'),
(21308, 'Sabana de Torres', 2901, 'SAN', 48, 'CO', 7.39150000, -73.49574000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1525377'),
(21309, 'Sabanagrande', 2880, 'ATL', 48, 'CO', 10.80077000, -74.77051000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1525377'),
(21310, 'Sabanalarga', 2892, 'CAS', 48, 'CO', 4.85430000, -73.04003000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q2433631'),
(21311, 'Sabanalarga', 2890, 'ANT', 48, 'CO', 6.84893000, -75.81711000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q589862'),
(21312, 'Sabanalarga', 2880, 'ATL', 48, 'CO', 10.61636000, -74.95717000, '2019-10-05 22:39:55', '2019-10-05 22:39:55', 1, 'Q1027007');

