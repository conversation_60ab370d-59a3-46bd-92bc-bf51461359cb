INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(61959, 'Vignate', 1705, '25', 107, 'IT', 45.49939000, 9.37719000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2191011'),
(61960, 'Vignola', 1725, '32', 107, 'IT', 46.04436000, 11.27738000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2191011'),
(61961, 'Vignola', 1773, '45', 107, 'IT', 44.48383000, 11.01096000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2191011'),
(61962, 'Vignola', 1705, '25', 107, 'IT', 46.08815000, 9.27456000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18487886'),
(61963, 'Vignole <PERSON>rbera', 1702, '21', 107, 'IT', 44.70819000, 8.89026000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18487886'),
(61964, 'Vignole-Olmi', 1664, '52', 107, 'IT', 43.86696000, 10.99045000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18491280'),
(61965, 'Vignolo', 1702, '21', 107, 'IT', 44.36364000, 7.47208000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18491280'),
(61966, 'Vignone', 1702, '21', 107, 'IT', 45.96118000, 8.56372000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18491280'),
(61967, 'Vigo di Cadore', 1753, '34', 107, 'IT', 46.49904000, 12.47111000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18491280'),
(61968, 'Vigo di Fassa', 1725, '32', 107, 'IT', 46.41898000, 11.67418000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q30031740'),
(61969, 'Vigo di Ton', 1725, '32', 107, 'IT', 46.26531000, 11.08798000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18483694'),
(61970, 'Vigodarzere', 1753, '34', 107, 'IT', 45.45751000, 11.88555000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18483694'),
(61971, 'Vigolo', 1705, '25', 107, 'IT', 45.71642000, 10.02634000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471216'),
(61972, 'Vigolo Vattaro', 1725, '32', 107, 'IT', 46.00523000, 11.19790000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471216'),
(61973, 'Vigolzone', 1773, '45', 107, 'IT', 44.91394000, 9.66852000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471216'),
(61974, 'Vigone', 1702, '21', 107, 'IT', 44.84236000, 7.49774000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471216'),
(61975, 'Vigonovo', 1753, '34', 107, 'IT', 45.38691000, 12.00642000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471216'),
(61976, 'Vigonovo-Fontanafredda', 1756, '36', 107, 'IT', 45.98927000, 12.54707000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18449257'),
(61977, 'Vigonza', 1753, '34', 107, 'IT', 45.43484000, 11.97465000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18449257'),
(61978, 'Vigonzone', 1705, '25', 107, 'IT', 45.27350000, 9.30980000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q4011587'),
(61979, 'Viguzzolo', 1702, '21', 107, 'IT', 44.90567000, 8.91968000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q4011587'),
(61980, 'Vill\'Albese', 1705, '25', 107, 'IT', 45.79684000, 9.18859000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18487887'),
(61981, 'Villa', 1705, '25', 107, 'IT', 46.20390000, 10.13385000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18494845'),
(61982, 'Villa', 1702, '21', 107, 'IT', 44.74606000, 7.90066000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18441305'),
(61983, 'Villa', 1725, '32', 107, 'IT', 46.74591000, 11.76243000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18478436'),
(61984, 'Villa Adriana', 1678, '62', 107, 'IT', 41.95499000, 12.77216000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q272777'),
(61985, 'Villa Bartolomea', 1753, '34', 107, 'IT', 45.15461000, 11.35380000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q272777'),
(61986, 'Villa Basilica', 1664, '52', 107, 'IT', 43.92572000, 10.64518000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q272777'),
(61987, 'Villa Biscossi', 1705, '25', 107, 'IT', 45.09066000, 8.78715000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q272777'),
(61988, 'Villa Caldari', 1679, '65', 107, 'IT', 42.29566000, 14.36157000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2287431'),
(61989, 'Villa Carcina', 1705, '25', 107, 'IT', 45.63329000, 10.19556000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2287431'),
(61990, 'Villa Castelli', 1688, '75', 107, 'IT', 40.58293000, 17.47468000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2287431'),
(61991, 'Villa Ceccolini', 1670, '57', 107, 'IT', 43.86481000, 12.83605000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18499026'),
(61992, 'Villa Celiera', 1679, '65', 107, 'IT', 42.38184000, 13.85860000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18499026'),
(61993, 'Villa Ciambra', 1709, '82', 107, 'IT', 38.06129000, 13.32000000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18469043'),
(61994, 'Villa Collemandina', 1664, '52', 107, 'IT', 44.15855000, 10.39760000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18469043'),
(61995, 'Villa Cortese', 1705, '25', 107, 'IT', 45.56666000, 8.88712000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18469043'),
(61996, 'Villa Estense', 1753, '34', 107, 'IT', 45.17465000, 11.67022000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18469043'),
(61997, 'Villa Faraldi', 1768, '42', 107, 'IT', 43.96843000, 8.08965000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18469043'),
(61998, 'Villa Fontana', 1773, '45', 107, 'IT', 44.49455000, 11.60885000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18438804'),
(61999, 'Villa Fornace', 1705, '25', 107, 'IT', 44.97194000, 9.25215000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18493841'),
(62000, 'Villa Guardia', 1705, '25', 107, 'IT', 45.77505000, 9.02321000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18493841'),
(62001, 'Villa Lagarina', 1725, '32', 107, 'IT', 45.91843000, 11.03303000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18493841'),
(62002, 'Villa Latina', 1678, '62', 107, 'IT', 41.61522000, 13.83591000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18493841'),
(62003, 'Villa Lempa', 1679, '65', 107, 'IT', 42.79345000, 13.64433000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18428623'),
(62004, 'Villa Literno', 1669, '72', 107, 'IT', 41.00942000, 14.07612000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18428623'),
(62005, 'Villa Minozzo', 1773, '45', 107, 'IT', 44.36480000, 10.46545000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18428623'),
(62006, 'Villa Musone', 1670, '57', 107, 'IT', 43.44624000, 13.60312000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496269'),
(62007, 'Villa Opicina', 1756, '36', 107, 'IT', 45.68780000, 13.78861000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q425566'),
(62008, 'Villa Pedergnano', 1705, '25', 107, 'IT', 45.58528000, 9.99381000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18478288'),
(62009, 'Villa Pigna', 1670, '57', 107, 'IT', 42.83324000, 13.63446000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496517'),
(62010, 'Villa Poma', 1705, '25', 107, 'IT', 45.00238000, 11.11450000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q42368'),
(62011, 'Villa Potenza', 1670, '57', 107, 'IT', 43.32149000, 13.42649000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18498027'),
(62012, 'Villa Raspa', 1679, '65', 107, 'IT', 42.45542000, 14.18457000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427634'),
(62013, 'Villa Raverio', 1705, '25', 107, 'IT', 45.69189000, 9.26239000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q4012456'),
(62014, 'Villa Rendena', 1725, '32', 107, 'IT', 46.06270000, 10.71250000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q4012456'),
(62015, 'Villa Rosa', 1679, '65', 107, 'IT', 42.85059000, 13.92015000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q4012486'),
(62016, 'Villa San Filippo', 1670, '57', 107, 'IT', 43.25981000, 13.59337000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18498029'),
(62017, 'Villa San Giovanni', 1703, '78', 107, 'IT', 38.21991000, 15.63689000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18498029'),
(62018, 'Villa San Giovanni in Tuscia', 1678, '62', 107, 'IT', 42.27882000, 12.05346000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18498029'),
(62019, 'Villa San Giuseppe', 1670, '57', 107, 'IT', 42.86862000, 13.74481000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496832'),
(62020, 'Villa San Pietro', 1715, '88', 107, 'IT', 39.03554000, 8.99695000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496832'),
(62021, 'Villa San Secondo', 1702, '21', 107, 'IT', 45.00513000, 8.13462000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496832'),
(62022, 'Villa Sant\'Angelo', 1679, '65', 107, 'IT', 42.27043000, 13.53710000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62023, 'Villa Sant\'Antonio', 1715, '88', 107, 'IT', 39.85915000, 8.90153000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62024, 'Villa Santa Lucia', 1678, '62', 107, 'IT', 41.51217000, 13.76929000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62025, 'Villa Santa Lucia degli Abruzzi', 1679, '65', 107, 'IT', 42.33286000, 13.77792000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62026, 'Villa Santa Maria', 1679, '65', 107, 'IT', 41.95130000, 14.35148000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62027, 'Villa Santina', 1756, '36', 107, 'IT', 46.41368000, 12.92445000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62028, 'Villa Santo Stefano', 1678, '62', 107, 'IT', 41.51683000, 13.31046000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62029, 'Villa Verde', 1715, '88', 107, 'IT', 39.79551000, 8.82114000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427125'),
(62030, 'Villa Verucchio', 1773, '45', 107, 'IT', 44.00510000, 12.43575000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2310250'),
(62031, 'Villa Vicentina', 1756, '36', 107, 'IT', 45.81378000, 13.39332000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2310250'),
(62032, 'Villa d\'Adda', 1705, '25', 107, 'IT', 45.71398000, 9.46165000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2310250'),
(62033, 'Villa d\'Agri', 1706, '77', 107, 'IT', 40.35426000, 15.82770000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18429174'),
(62034, 'Villa d\'Almè', 1705, '25', 107, 'IT', 45.74843000, 9.61702000, '2019-10-05 23:07:25', '2020-05-01 17:22:56', 1, 'Q18429174'),
(62035, 'Villa d\'Asolo', 1753, '34', 107, 'IT', 45.77193000, 11.89981000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2580067'),
(62036, 'Villa d\'Ogna', 1705, '25', 107, 'IT', 45.90469000, 9.93085000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q2580067'),
(62037, 'Villa d\'Oneta', 1705, '25', 107, 'IT', 45.87074000, 9.81929000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471243'),
(62038, 'Villa del Bosco', 1702, '21', 107, 'IT', 45.61669000, 8.27776000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471243'),
(62039, 'Villa del Conte', 1753, '34', 107, 'IT', 45.58475000, 11.85942000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471243'),
(62040, 'Villa di Briano', 1669, '72', 107, 'IT', 41.00001000, 14.16073000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471243'),
(62041, 'Villa di Chiavenna', 1705, '25', 107, 'IT', 46.33069000, 9.48114000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471243'),
(62042, 'Villa di Serio', 1705, '25', 107, 'IT', 45.72260000, 9.73522000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18471243'),
(62043, 'Villa-Agnedo', 1725, '32', 107, 'IT', 46.05421000, 11.52839000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q327999'),
(62044, 'Villa-Borgo', 1702, '21', 107, 'IT', 44.69865000, 7.93486000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18441306'),
(62045, 'Villa-Nabian', 1716, '23', 107, 'IT', 45.68970000, 7.70470000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496037'),
(62046, 'Villabassa', 1725, '32', 107, 'IT', 46.73781000, 12.17266000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496037'),
(62047, 'Villabate', 1709, '82', 107, 'IT', 38.07789000, 13.44275000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496037'),
(62048, 'Villabruna-Umin', 1753, '34', 107, 'IT', 46.05675000, 11.92700000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18497162'),
(62049, 'Villachiara', 1705, '25', 107, 'IT', 45.35445000, 9.93081000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18497162'),
(62050, 'Villacidro', 1715, '88', 107, 'IT', 39.45734000, 8.74105000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18497162'),
(62051, 'Villadeati', 1702, '21', 107, 'IT', 45.07225000, 8.16793000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18497162'),
(62052, 'Villadose', 1753, '34', 107, 'IT', 45.07219000, 11.89899000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18497162'),
(62053, 'Villadossola', 1702, '21', 107, 'IT', 46.07080000, 8.26709000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18497162'),
(62054, 'Villafalletto', 1702, '21', 107, 'IT', 44.54624000, 7.54069000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18497162'),
(62055, 'Villafranca', 1768, '42', 107, 'IT', 44.03297000, 8.10525000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62056, 'Villafranca Padovana', 1753, '34', 107, 'IT', 45.49189000, 11.79350000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62057, 'Villafranca Piemonte', 1702, '21', 107, 'IT', 44.78824000, 7.50788000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62058, 'Villafranca Sicula', 1709, '82', 107, 'IT', 37.58761000, 13.29048000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62059, 'Villafranca Tirrena', 1709, '82', 107, 'IT', 38.23952000, 15.43885000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62060, 'Villafranca d\'Asti', 1702, '21', 107, 'IT', 44.91539000, 8.02658000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62061, 'Villafranca di Verona', 1753, '34', 107, 'IT', 45.35405000, 10.84462000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62062, 'Villafranca in Lunigiana', 1664, '52', 107, 'IT', 44.29844000, 9.95347000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62063, 'Villafrati', 1709, '82', 107, 'IT', 37.90646000, 13.48486000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62064, 'Villaga', 1753, '34', 107, 'IT', 45.40273000, 11.53468000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18463683'),
(62065, 'Villaganzerla', 1753, '34', 107, 'IT', 45.43612000, 11.61859000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18507105'),
(62066, 'Villaggio Montegrappa', 1753, '34', 107, 'IT', 45.54643000, 11.61428000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18507108'),
(62067, 'Villaggio Residenziale', 1705, '25', 107, 'IT', 45.52826000, 9.46737000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18491824'),
(62068, 'Villaggio del Pino-Le Ginestre', 1709, '82', 107, 'IT', 37.58292000, 15.00377000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18461775'),
(62069, 'Villaggio del Sole', 1705, '25', 107, 'IT', 45.61398000, 9.10725000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18491925'),
(62070, 'Villagrande', 1670, '57', 107, 'IT', 43.84136000, 12.35994000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18499043'),
(62071, 'Villagrande', 1679, '65', 107, 'IT', 42.29533000, 13.29999000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427129'),
(62072, 'Villagrande Strisaili', 1715, '88', 107, 'IT', 39.95929000, 9.50943000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18427129'),
(62073, 'Villaguattera', 1753, '34', 107, 'IT', 45.44409000, 11.81767000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18499101'),
(62074, 'Villair-Amerique', 1716, '23', 107, 'IT', 45.74793000, 7.38982000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496035'),
(62075, 'Villalago', 1679, '65', 107, 'IT', 41.93514000, 13.83868000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496035'),
(62076, 'Villalba', 1709, '82', 107, 'IT', 37.65457000, 13.84452000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18496035'),
(62077, 'Villalba', 1678, '62', 107, 'IT', 41.95387000, 12.72938000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18455377'),
(62078, 'Villalfonsina', 1679, '65', 107, 'IT', 42.16038000, 14.56932000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18455377'),
(62079, 'Villalvernia', 1702, '21', 107, 'IT', 44.81387000, 8.85532000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18455377'),
(62080, 'Villamagna', 1679, '65', 107, 'IT', 42.32984000, 14.23689000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18455377'),
(62081, 'Villamaina', 1669, '72', 107, 'IT', 40.97144000, 15.08855000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18455377'),
(62082, 'Villamar', 1715, '88', 107, 'IT', 39.61884000, 8.95877000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18455377'),
(62083, 'Villamarzana', 1753, '34', 107, 'IT', 45.01457000, 11.69225000, '2019-10-05 23:07:25', '2019-10-05 23:07:25', 1, 'Q18455377'),
(62084, 'Villamassargia', 1715, '88', 107, 'IT', 39.27484000, 8.64110000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18455377'),
(62085, 'Villamiroglio', 1702, '21', 107, 'IT', 45.13455000, 8.17103000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18455377'),
(62086, 'Villammare', 1669, '72', 107, 'IT', 40.07747000, 15.59401000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q1855704'),
(62087, 'Villandro', 1725, '32', 107, 'IT', 46.********, 11.53808000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18510431'),
(62088, 'Villandro - Villanders', 1725, '32', 107, 'IT', 46.63082000, 11.53708000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18510431'),
(62089, 'Villanova', 1773, '45', 107, 'IT', 45.02597000, 9.99865000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18445707'),
(62090, 'Villanova', 1756, '36', 107, 'IT', 46.13226000, 12.97084000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18445707'),
(62091, 'Villanova', 1753, '34', 107, 'IT', 45.48890000, 11.97471000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18445707'),
(62092, 'Villanova', 1670, '57', 107, 'IT', 43.73926000, 12.93529000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18499045'),
(62093, 'Villanova', 1679, '65', 107, 'IT', 42.38251000, 14.12338000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18427644'),
(62094, 'Villanova', 1705, '25', 107, 'IT', 45.64027000, 9.39187000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18491927'),
(62095, 'Villanova', 1678, '62', 107, 'IT', 41.96357000, 12.75633000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62096, 'Villanova Biellese', 1702, '21', 107, 'IT', 45.48156000, 8.19432000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62097, 'Villanova Canavese', 1702, '21', 107, 'IT', 45.24355000, 7.55212000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62098, 'Villanova Marchesana', 1753, '34', 107, 'IT', 44.99231000, 11.96675000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62099, 'Villanova Mondovì', 1702, '21', 107, 'IT', 44.34804000, 7.76752000, '2019-10-05 23:07:26', '2020-05-01 17:22:56', 1, 'Q2526072'),
(62100, 'Villanova Monferrato', 1702, '21', 107, 'IT', 45.18111000, 8.47813000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62101, 'Villanova Monteleone', 1715, '88', 107, 'IT', 40.50264000, 8.47115000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62102, 'Villanova Solaro', 1702, '21', 107, 'IT', 44.72994000, 7.57443000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62103, 'Villanova Truschedu', 1715, '88', 107, 'IT', 39.98842000, 8.75177000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62104, 'Villanova Tulo', 1715, '88', 107, 'IT', 39.77995000, 9.21424000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62105, 'Villanova d\'Albenga', 1768, '42', 107, 'IT', 44.04725000, 8.14336000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62106, 'Villanova d\'Ardenghi', 1705, '25', 107, 'IT', 45.17049000, 9.04078000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62107, 'Villanova d\'Asti', 1702, '21', 107, 'IT', 44.94299000, 7.93671000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62108, 'Villanova del Battista', 1669, '72', 107, 'IT', 41.11897000, 15.15839000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2526072'),
(62109, 'Villanova del Ghebbo Canton', 1753, '34', 107, 'IT', 45.05933000, 11.64314000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18500510'),
(62110, 'Villanova del Sillaro', 1705, '25', 107, 'IT', 45.23797000, 9.48186000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18500510'),
(62111, 'Villanovaforru', 1715, '88', 107, 'IT', 39.63196000, 8.86979000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18500510'),
(62112, 'Villanovafranca', 1715, '88', 107, 'IT', 39.64442000, 9.00244000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18500510'),
(62113, 'Villanterio', 1705, '25', 107, 'IT', 45.21780000, 9.36100000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18500510'),
(62114, 'Villanuova', 1705, '25', 107, 'IT', 45.44245000, 9.86690000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18509498'),
(62115, 'Villanuova sul Clisi', 1705, '25', 107, 'IT', 45.59938000, 10.45275000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18509498'),
(62116, 'Villaperuccio', 1715, '88', 107, 'IT', 39.11183000, 8.67004000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18509498'),
(62117, 'Villapiana', 1703, '78', 107, 'IT', 39.84573000, 16.45528000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18509498'),
(62118, 'Villapiana Lido', 1703, '78', 107, 'IT', 39.81203000, 16.48820000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18431131'),
(62119, 'Villapinta', 1705, '25', 107, 'IT', 46.17508000, 9.67950000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62120, 'Villaputzu', 1715, '88', 107, 'IT', 39.44058000, 9.57564000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62121, 'Villar Dora', 1702, '21', 107, 'IT', 45.11465000, 7.38352000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62122, 'Villar Focchiardo', 1702, '21', 107, 'IT', 45.11244000, 7.23841000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62123, 'Villar Pellice', 1702, '21', 107, 'IT', 44.80893000, 7.15978000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62124, 'Villar Perosa', 1702, '21', 107, 'IT', 44.91849000, 7.24821000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62125, 'Villar San Costanzo', 1702, '21', 107, 'IT', 44.48473000, 7.38223000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62126, 'Villarbasse', 1702, '21', 107, 'IT', 45.04514000, 7.46842000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62127, 'Villarboit', 1702, '21', 107, 'IT', 45.43775000, 8.33694000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62128, 'Villareggia', 1702, '21', 107, 'IT', 45.30885000, 7.97724000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62129, 'Villaricca', 1669, '72', 107, 'IT', 40.92006000, 14.19339000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62130, 'Villaromagnano', 1702, '21', 107, 'IT', 44.84965000, 8.88775000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62131, 'Villarosa', 1709, '82', 107, 'IT', 37.58753000, 14.17089000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18494851'),
(62132, 'Villarotta', 1773, '45', 107, 'IT', 44.92517000, 10.74382000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18447569'),
(62133, 'Villasalto', 1715, '88', 107, 'IT', 39.49209000, 9.39023000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18447569'),
(62134, 'Villasanta', 1705, '25', 107, 'IT', 45.60634000, 9.30797000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18447569'),
(62135, 'Villaseta', 1709, '82', 107, 'IT', 37.29877000, 13.55919000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4012906'),
(62136, 'Villasimius', 1715, '88', 107, 'IT', 39.14481000, 9.51823000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4012906'),
(62137, 'Villasmundo', 1709, '82', 107, 'IT', 37.25132000, 15.09087000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18510326'),
(62138, 'Villasor', 1715, '88', 107, 'IT', 39.38130000, 8.94270000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18510326'),
(62139, 'Villaspeciosa', 1715, '88', 107, 'IT', 39.31142000, 8.92584000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18510326'),
(62140, 'Villassio', 1705, '25', 107, 'IT', 45.86313000, 9.84466000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471301'),
(62141, 'Villastellone', 1702, '21', 107, 'IT', 44.92201000, 7.74360000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471301'),
(62142, 'Villata', 1702, '21', 107, 'IT', 45.38650000, 8.43279000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471301'),
(62143, 'Villatora', 1753, '34', 107, 'IT', 45.38768000, 11.96736000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18499108'),
(62144, 'Villaurbana', 1715, '88', 107, 'IT', 39.88505000, 8.77831000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18499108'),
(62145, 'Villavallelonga', 1679, '65', 107, 'IT', 41.87104000, 13.62088000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18499108'),
(62146, 'Villaverla', 1753, '34', 107, 'IT', 45.64939000, 11.49176000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18499108'),
(62147, 'Ville Sur Sarre', 1716, '23', 107, 'IT', 45.73339000, 7.25857000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496041'),
(62148, 'Villefranche', 1716, '23', 107, 'IT', 45.74064000, 7.41598000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496043'),
(62149, 'Villeneuve', 1716, '23', 107, 'IT', 45.70201000, 7.20682000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496043'),
(62150, 'Villesse', 1756, '36', 107, 'IT', 45.86526000, 13.44380000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496043'),
(62151, 'Villetta Barrea', 1679, '65', 107, 'IT', 41.77646000, 13.93892000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496043'),
(62152, 'Villette', 1702, '21', 107, 'IT', 46.13188000, 8.53422000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496043'),
(62153, 'Villimpenta', 1705, '25', 107, 'IT', 45.14111000, 11.03170000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496043'),
(62154, 'Villongo', 1705, '25', 107, 'IT', 45.66939000, 9.93076000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18496043'),
(62155, 'Villotta', 1756, '36', 107, 'IT', 45.86336000, 12.75418000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18449279'),
(62156, 'Vilminore', 1705, '25', 107, 'IT', 45.99795000, 10.09531000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62157, 'Vilminore di Scalve', 1705, '25', 107, 'IT', 45.99819000, 10.09376000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62158, 'Vimercate', 1705, '25', 107, 'IT', 45.61545000, 9.36801000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62159, 'Vimodrone', 1705, '25', 107, 'IT', 45.51461000, 9.28772000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62160, 'Vinadio', 1702, '21', 107, 'IT', 44.30759000, 7.17628000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62161, 'Vinchiaturo', 1695, '67', 107, 'IT', 41.49282000, 14.59200000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62162, 'Vinchio', 1702, '21', 107, 'IT', 44.81095000, 8.32094000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62163, 'Vinci', 1664, '52', 107, 'IT', 43.78133000, 10.92365000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62164, 'Vinovo', 1702, '21', 107, 'IT', 44.94674000, 7.63252000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62165, 'Vinzaglio', 1702, '21', 107, 'IT', 45.32309000, 8.51979000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62166, 'Viola', 1702, '21', 107, 'IT', 44.29081000, 7.96491000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62167, 'Vione', 1705, '25', 107, 'IT', 46.24808000, 10.44842000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62168, 'Vipiteno', 1725, '32', 107, 'IT', 46.89313000, 11.42961000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62169, 'Virle Piemonte', 1702, '21', 107, 'IT', 44.86369000, 7.57033000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62170, 'Visano', 1705, '25', 107, 'IT', 45.31684000, 10.37092000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62171, 'Vische', 1702, '21', 107, 'IT', 45.33566000, 7.94482000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62172, 'Visciano', 1669, '72', 107, 'IT', 40.92380000, 14.58237000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62173, 'Visco', 1756, '36', 107, 'IT', 45.89194000, 13.34861000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62174, 'Visone', 1702, '21', 107, 'IT', 44.66184000, 8.50075000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62175, 'Visso', 1670, '57', 107, 'IT', 42.93351000, 13.08051000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62176, 'Vistarino', 1705, '25', 107, 'IT', 45.21077000, 9.30825000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62177, 'Vistrorio', 1702, '21', 107, 'IT', 45.44147000, 7.76781000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62178, 'Vita', 1709, '82', 107, 'IT', 37.86886000, 12.82755000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62179, 'Viterbo', 1678, '62', 107, 'IT', 42.41937000, 12.10560000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62180, 'Viticuso', 1678, '62', 107, 'IT', 41.52475000, 13.97027000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18471340'),
(62181, 'Vitigliano', 1688, '75', 107, 'IT', 40.03951000, 18.41170000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q2369296'),
(62182, 'Vitinia', 1678, '62', 107, 'IT', 41.79124000, 12.40810000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62183, 'Vitorchiano', 1678, '62', 107, 'IT', 42.46565000, 12.17151000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62184, 'Vittoria', 1709, '82', 107, 'IT', 36.95151000, 14.52788000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62185, 'Vittorio Veneto', 1753, '34', 107, 'IT', 45.98026000, 12.30065000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62186, 'Vittorito', 1679, '65', 107, 'IT', 42.12703000, 13.81670000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62187, 'Vittuone', 1705, '25', 107, 'IT', 45.48792000, 8.95141000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62188, 'Vitulano', 1669, '72', 107, 'IT', 41.17419000, 14.64821000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62189, 'Vitulazio', 1669, '72', 107, 'IT', 41.16302000, 14.21341000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62190, 'Vivaro', 1756, '36', 107, 'IT', 46.07656000, 12.77912000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62191, 'Vivaro Romano', 1678, '62', 107, 'IT', 42.10095000, 13.00589000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62192, 'Viverone', 1702, '21', 107, 'IT', 45.42726000, 8.04942000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62193, 'Vizzini', 1709, '82', 107, 'IT', 37.16188000, 14.75712000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62194, 'Vizzola Ticino', 1705, '25', 107, 'IT', 45.62615000, 8.69651000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62195, 'Vizzolo Predabissi', 1705, '25', 107, 'IT', 45.35587000, 9.34815000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62196, 'Viù', 1702, '21', 107, 'IT', 45.23785000, 7.37333000, '2019-10-05 23:07:26', '2020-05-01 17:22:56', 1, 'Q4015042'),
(62197, 'Vobarno', 1705, '25', 107, 'IT', 45.64153000, 10.49866000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62198, 'Vobbia', 1768, '42', 107, 'IT', 44.60125000, 9.03866000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62199, 'Vocca', 1702, '21', 107, 'IT', 45.83269000, 8.19580000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q4015042'),
(62200, 'Vodo', 1753, '34', 107, 'IT', 46.41921000, 12.24557000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18497185'),
(62201, 'Voghera', 1705, '25', 107, 'IT', 44.99151000, 9.01175000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18497185'),
(62202, 'Voghiera', 1773, '45', 107, 'IT', 44.75558000, 11.75182000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18497185'),
(62203, 'Vogogna', 1702, '21', 107, 'IT', 46.01002000, 8.29137000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18497185'),
(62204, 'Volano', 1725, '32', 107, 'IT', 45.91718000, 11.06351000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18497185'),
(62205, 'Volargne', 1753, '34', 107, 'IT', 45.53684000, 10.81820000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18505750'),
(62206, 'Volla', 1669, '72', 107, 'IT', 40.87363000, 14.34085000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18505750'),
(62207, 'Volongo', 1705, '25', 107, 'IT', 45.21155000, 10.30242000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18505750'),
(62208, 'Volpago del Montello', 1753, '34', 107, 'IT', 45.77770000, 12.11857000, '2019-10-05 23:07:26', '2019-10-05 23:07:26', 1, 'Q18505750'),
(62209, 'Volpara', 1705, '25', 107, 'IT', 44.95379000, 9.29750000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18505750'),
(62210, 'Volpedo', 1702, '21', 107, 'IT', 44.88887000, 8.98615000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18505750'),
(62211, 'Volpeglino', 1702, '21', 107, 'IT', 44.89296000, 8.95945000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18505750'),
(62212, 'Volpiano', 1702, '21', 107, 'IT', 45.19940000, 7.77546000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18505750'),
(62213, 'Volta Mantovana', 1705, '25', 107, 'IT', 45.32192000, 10.65891000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18505750'),
(62214, 'Voltaggio', 1702, '21', 107, 'IT', 44.62095000, 8.84274000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18505750'),
(62215, 'Voltago', 1753, '34', 107, 'IT', 46.27252000, 12.00640000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18497188'),
(62216, 'Voltago Agordino', 1753, '34', 107, 'IT', 46.27139000, 12.00574000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18497188'),
(62217, 'Voltana', 1773, '45', 107, 'IT', 44.54109000, 11.93533000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4016324'),
(62218, 'Volterra', 1664, '52', 107, 'IT', 43.40251000, 10.86152000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4016324'),
(62219, 'Voltido', 1705, '25', 107, 'IT', 45.11197000, 10.33298000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4016324'),
(62220, 'Volturara Appula', 1688, '75', 107, 'IT', 41.49603000, 15.05224000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4016324'),
(62221, 'Volturara Irpina', 1669, '72', 107, 'IT', 40.88293000, 14.91801000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4016324'),
(62222, 'Volturino', 1688, '75', 107, 'IT', 41.47723000, 15.12424000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4016324'),
(62223, 'Volvera', 1702, '21', 107, 'IT', 44.95484000, 7.51142000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4016324'),
(62224, 'Vomero', 1669, '72', 107, 'IT', 40.84282000, 14.23075000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q671420'),
(62225, 'Vottignasco', 1702, '21', 107, 'IT', 44.56413000, 7.57913000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q671420'),
(62226, 'Vò', 1753, '34', 107, 'IT', 45.32829000, 11.64150000, '2019-10-05 23:07:27', '2020-05-01 17:22:57', 1, 'Q671420'),
(62227, 'Weather Station', 1703, '78', 107, 'IT', 37.96136000, 16.09970000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62228, 'Zaccanopoli', 1703, '78', 107, 'IT', 38.66548000, 15.92874000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62229, 'Zafferana Etnea', 1709, '82', 107, 'IT', 37.67895000, 15.10432000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62230, 'Zagarise', 1703, '78', 107, 'IT', 38.99985000, 16.66420000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62231, 'Zagarolo', 1678, '62', 107, 'IT', 41.84159000, 12.81540000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62232, 'Zambana', 1725, '32', 107, 'IT', 46.15170000, 11.09618000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62233, 'Zambrone', 1703, '78', 107, 'IT', 38.69878000, 15.98982000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62234, 'Zandobbio', 1705, '25', 107, 'IT', 45.68613000, 9.85785000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62235, 'Zanica', 1705, '25', 107, 'IT', 45.64088000, 9.68566000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62236, 'Zanè', 1753, '34', 107, 'IT', 45.72200000, 11.44932000, '2019-10-05 23:07:27', '2020-05-01 17:22:57', 1, 'Q18433442'),
(62237, 'Zapponeta', 1688, '75', 107, 'IT', 41.45710000, 15.95615000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62238, 'Zavattarello', 1705, '25', 107, 'IT', 44.86766000, 9.26662000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62239, 'Zeccone', 1705, '25', 107, 'IT', 45.25787000, 9.20115000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62240, 'Zeddiani', 1715, '88', 107, 'IT', 39.98898000, 8.59580000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18433442'),
(62241, 'Zelarino', 1753, '34', 107, 'IT', 45.51508000, 12.20790000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q187302'),
(62242, 'Zelbio', 1705, '25', 107, 'IT', 45.90458000, 9.18054000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q187302'),
(62243, 'Zelo Buon Persico', 1705, '25', 107, 'IT', 45.41170000, 9.43171000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q187302'),
(62244, 'Zelo Surrigone', 1705, '25', 107, 'IT', 45.38767000, 8.98504000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q187302'),
(62245, 'Zeme', 1705, '25', 107, 'IT', 45.19686000, 8.66733000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q187302'),
(62246, 'Zenevredo', 1705, '25', 107, 'IT', 45.05391000, 9.32667000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q187302'),
(62247, 'Zenson di Piave', 1753, '34', 107, 'IT', 45.67971000, 12.48913000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q187302'),
(62248, 'Zepponami', 1678, '62', 107, 'IT', 42.52518000, 12.05411000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62249, 'Zerba', 1773, '45', 107, 'IT', 44.66551000, 9.28795000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62250, 'Zerbo', 1705, '25', 107, 'IT', 45.11046000, 9.39606000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62251, 'Zerbolò', 1705, '25', 107, 'IT', 45.20676000, 9.01441000, '2019-10-05 23:07:27', '2020-05-01 17:22:56', 1, 'Q4024209'),
(62252, 'Zerfaliu', 1715, '88', 107, 'IT', 39.96088000, 8.70971000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62253, 'Zeri', 1664, '52', 107, 'IT', 44.35375000, 9.76318000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62254, 'Zermeghedo', 1753, '34', 107, 'IT', 45.47585000, 11.37438000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62255, 'Zero Branco', 1753, '34', 107, 'IT', 45.59952000, 12.16381000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62256, 'Zevio', 1753, '34', 107, 'IT', 45.37209000, 11.12929000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62257, 'Ziano Piacentino', 1773, '45', 107, 'IT', 44.99980000, 9.39486000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62258, 'Ziano di Fiemme', 1725, '32', 107, 'IT', 46.28572000, 11.56496000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62259, 'Zibello', 1773, '45', 107, 'IT', 45.01847000, 10.12968000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62260, 'Zibido San Giacomo', 1705, '25', 107, 'IT', 45.36041000, 9.11119000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62261, 'Zimone', 1702, '21', 107, 'IT', 45.44912000, 8.03734000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024209'),
(62262, 'Zinasco Vecchio', 1705, '25', 107, 'IT', 45.12836000, 9.02975000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18493910'),
(62263, 'Zoagli', 1768, '42', 107, 'IT', 44.33668000, 9.26680000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18493910'),
(62264, 'Zocca', 1773, '45', 107, 'IT', 44.34566000, 10.99308000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18493910'),
(62265, 'Zoccorino-Vergo', 1705, '25', 107, 'IT', 45.69735000, 9.25377000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18491930'),
(62266, 'Zogno', 1705, '25', 107, 'IT', 45.79378000, 9.65992000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18491930'),
(62267, 'Zola Predosa', 1773, '45', 107, 'IT', 44.48967000, 11.21831000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18491930'),
(62268, 'Zolla', 1756, '36', 107, 'IT', 45.71698000, 13.81038000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18449615'),
(62269, 'Zollino', 1688, '75', 107, 'IT', 40.20581000, 18.24774000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18449615'),
(62270, 'Zona 179', 1706, '77', 107, 'IT', 41.00313000, 15.61868000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18429182'),
(62271, 'Zona Industriale', 1670, '57', 107, 'IT', 43.26155000, 13.48834000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18498062'),
(62272, 'Zona Industriale', 1669, '72', 107, 'IT', 40.84788000, 14.28355000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18498062'),
(62273, 'Zone', 1705, '25', 107, 'IT', 45.76339000, 10.11586000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18498062'),
(62274, 'Zoppola', 1756, '36', 107, 'IT', 45.96624000, 12.76828000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18498062'),
(62275, 'Zoppè di Cadore', 1753, '34', 107, 'IT', 46.38583000, 12.17368000, '2019-10-05 23:07:27', '2020-05-01 17:22:57', 1, 'Q18498062'),
(62276, 'Zorlesco', 1705, '25', 107, 'IT', 45.20035000, 9.61603000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024817'),
(62277, 'Zovencedo', 1753, '34', 107, 'IT', 45.42893000, 11.50387000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024817'),
(62278, 'Zubiena', 1702, '21', 107, 'IT', 45.49266000, 7.99552000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024817'),
(62279, 'Zuccarello', 1768, '42', 107, 'IT', 44.10973000, 8.11814000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024817'),
(62280, 'Zuclo', 1725, '32', 107, 'IT', 46.03430000, 10.75107000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024817'),
(62281, 'Zugliano', 1753, '34', 107, 'IT', 45.72980000, 11.52399000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4024817'),
(62282, 'Zugliano-Terenzano-Cargnacco', 1756, '36', 107, 'IT', 46.00750000, 13.21694000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18452678'),
(62283, 'Zuglio', 1756, '36', 107, 'IT', 46.45830000, 13.02589000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18452678'),
(62284, 'Zumaglia', 1702, '21', 107, 'IT', 45.59386000, 8.08942000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18452678'),
(62285, 'Zumpano', 1703, '78', 107, 'IT', 39.31053000, 16.29269000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18452678'),
(62286, 'Zungoli', 1669, '72', 107, 'IT', 41.12766000, 15.20255000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18452678'),
(62287, 'Zungri', 1703, '78', 107, 'IT', 38.65668000, 15.98409000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18452678'),
(62288, 'Zuni', 1669, '72', 107, 'IT', 41.21722000, 14.13082000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62289, '12th Street', 3742, '10', 108, 'JM', 18.21097000, -78.13603000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62290, 'Aberdeen', 3743, '11', 108, 'JM', 18.20508000, -77.67932000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62291, 'Aboukir', 3744, '06', 108, 'JM', 18.25090000, -77.34327000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62292, 'Above Rocks', 3746, '14', 108, 'JM', 18.11432000, -76.87590000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62293, 'Accompong', 3743, '11', 108, 'JM', 18.22985000, -77.74816000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62294, 'Adelphi', 3745, '08', 108, 'JM', 18.45060000, -77.78590000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62295, 'Aenon Town', 3753, '13', 108, 'JM', 18.21027000, -77.39851000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62296, 'Airy Castle', 3750, '03', 108, 'JM', 17.92506000, -76.34216000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q18436859'),
(62297, 'Albert Town', 3755, '07', 108, 'JM', 18.29434000, -77.54239000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4711348'),
(62298, 'Albion', 3745, '08', 108, 'JM', 18.48471000, -77.91408000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4711348'),
(62299, 'Albion', 3750, '03', 108, 'JM', 17.90119000, -76.60761000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4711348'),
(62300, 'Alderton', 3744, '06', 108, 'JM', 18.28706000, -77.20832000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4711348'),
(62301, 'Alexandria', 3744, '06', 108, 'JM', 18.30411000, -77.35311000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4711348'),
(62302, 'Alley', 3753, '13', 108, 'JM', 17.79036000, -77.27085000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4711348'),
(62303, 'Alligator Pond', 3743, '11', 108, 'JM', 17.86932000, -77.56769000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62304, 'Alligator Pond', 3754, '12', 108, 'JM', 17.88369000, -77.56068000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62305, 'Allman Town', 3748, '01', 108, 'JM', 17.98297000, -76.78685000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62306, 'Alps', 3755, '07', 108, 'JM', 18.35498000, -77.50897000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62307, 'Alston', 3753, '13', 108, 'JM', 18.17639000, -77.43468000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62308, 'Alva', 3744, '06', 108, 'JM', 18.29679000, -77.31717000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62309, 'Amiel Town', 3747, '05', 108, 'JM', 18.34027000, -76.97612000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62310, 'Amity', 3742, '10', 108, 'JM', 18.27361000, -77.89805000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62311, 'Amity Hall', 3750, '03', 108, 'JM', 17.96759000, -76.25698000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4732659'),
(62312, 'Anchovy', 3745, '08', 108, 'JM', 18.41030000, -77.93166000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4752772'),
(62313, 'Annotto Bay', 3747, '05', 108, 'JM', 18.27170000, -76.76523000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62314, 'Arcadia', 3750, '03', 108, 'JM', 17.92230000, -76.30030000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62315, 'Arcadia', 3745, '08', 108, 'JM', 18.24993000, -77.79228000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62316, 'Arcadia', 3751, '02', 108, 'JM', 18.03737000, -76.78201000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62317, 'Arlene Gardens', 3751, '02', 108, 'JM', 18.03713000, -76.81791000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62318, 'Arnett Gardens', 3751, '02', 108, 'JM', 17.99150000, -76.79829000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62319, 'Ashley', 3753, '13', 108, 'JM', 18.01440000, -77.34019000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62320, 'Ashton', 3742, '10', 108, 'JM', 18.23139000, -77.92074000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62321, 'Asia/Pratville', 3754, '12', 108, 'JM', 17.91584000, -77.41563000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62322, 'Askenish', 3749, '09', 108, 'JM', 18.38492000, -78.14910000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62323, 'Auchtembeddie', 3754, '12', 108, 'JM', 18.22543000, -77.60361000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62324, 'August Town', 3751, '02', 108, 'JM', 17.99556000, -76.73673000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62325, 'Baileys Vale', 3747, '05', 108, 'JM', 18.34527000, -76.92593000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q567350'),
(62326, 'Balaclava', 3743, '11', 108, 'JM', 18.17228000, -77.64375000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4849741'),
(62327, 'Balcarres', 3752, '04', 108, 'JM', 18.********, -76.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4849741'),
(62328, 'Ballards  Valley', 3743, '11', 108, 'JM', 17.********, -77.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q4849741'),
(62329, 'Bamboo', 3744, '06', 108, 'JM', 18.********, -77.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62330, 'Bangor Ridge', 3752, '04', 108, 'JM', 18.********, -76.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62331, 'Banks', 3753, '13', 108, 'JM', 17.********, -77.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62332, 'Baptist', 3750, '03', 108, 'JM', 17.********, -76.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62333, 'Barbary Hall', 3743, '11', 108, 'JM', 17.********, -77.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62334, 'Barbican', 3751, '02', 108, 'JM', 18.********, -76.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62335, 'Barking Lodge', 3750, '03', 108, 'JM', 17.********, -76.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62336, 'Barneyside', 3742, '10', 108, 'JM', 18.********, -77.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62337, 'Barrett Town', 3745, '08', 108, 'JM', 18.********, -77.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62338, 'Bartons', 3746, '14', 108, 'JM', 18.********, -77.********, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62339, 'Bath', 3750, '03', 108, 'JM', 17.94820000, -76.34916000, '2019-10-05 23:07:27', '2019-10-05 23:07:27', 1, 'Q1880599'),
(62340, 'Bath', 3742, '10', 108, 'JM', 18.25734000, -78.11751000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62341, 'Bath Mountain', 3742, '10', 108, 'JM', 18.34428000, -78.09928000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62342, 'Baxter Mountain', 3747, '05', 108, 'JM', 18.20405000, -76.77198000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62343, 'Beaufort', 3742, '10', 108, 'JM', 18.23417000, -77.95007000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62344, 'Beckford Kraal', 3753, '13', 108, 'JM', 18.08545000, -77.32399000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62345, 'Beecher Town', 3744, '06', 108, 'JM', 18.38465000, -77.11617000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62346, 'Beeston Spring', 3742, '10', 108, 'JM', 18.16012000, -77.97545000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62347, 'Belfield', 3747, '05', 108, 'JM', 18.27347000, -76.83574000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62348, 'Bellas Gate', 3746, '14', 108, 'JM', 18.04715000, -77.14906000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62349, 'Bellefield', 3754, '12', 108, 'JM', 18.09266000, -77.44004000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62350, 'Belvedere', 3742, '10', 108, 'JM', 18.25219000, -77.93208000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62351, 'Belvedere', 3752, '04', 108, 'JM', 18.20772000, -76.69671000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62352, 'Bensonton', 3744, '06', 108, 'JM', 18.22315000, -77.20861000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62353, 'Berkshire', 3742, '10', 108, 'JM', 18.21293000, -77.90373000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62354, 'Bethany', 3754, '12', 108, 'JM', 18.15373000, -77.55032000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62355, 'Bethel', 3750, '03', 108, 'JM', 17.98527000, -76.58050000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1880599'),
(62356, 'Bethel Town', 3742, '10', 108, 'JM', 18.29503000, -77.95112000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q4897936'),
(62357, 'Beverley Hills', 3751, '02', 108, 'JM', 18.00833000, -76.76208000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q4897936'),
(62358, 'Bickersteth', 3745, '08', 108, 'JM', 18.36335000, -77.93651000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q4897936'),
(62359, 'Big Bridge', 3742, '10', 108, 'JM', 18.23318000, -78.16717000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q4897936'),
(62360, 'Bigwoods', 3743, '11', 108, 'JM', 17.93338000, -77.71897000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q4897936'),
(62361, 'Bito', 3751, '02', 108, 'JM', 17.97363000, -76.66625000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q4897936'),
(62362, 'Black Hill', 3752, '04', 108, 'JM', 18.21786000, -76.59758000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q4897936'),
(62363, 'Black River', 3743, '11', 108, 'JM', 18.02636000, -77.84873000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q880512'),
(62364, 'Blackstonedge', 3744, '06', 108, 'JM', 18.27357000, -77.04254000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q880512'),
(62365, 'Bloxborough', 3751, '02', 108, 'JM', 17.99581000, -76.64767000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q880512'),
(62366, 'Blue Mountain', 3754, '12', 108, 'JM', 18.07095000, -77.42840000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q880512'),
(62367, 'Bluefields', 3742, '10', 108, 'JM', 18.16602000, -78.02702000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q522341'),
(62368, 'Bog', 3742, '10', 108, 'JM', 18.15631000, -77.94364000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q522341'),
(62369, 'Bog Walk', 3746, '14', 108, 'JM', 18.10205000, -77.00541000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62370, 'Bogue', 3745, '08', 108, 'JM', 18.43722000, -77.92022000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62371, 'Bogue', 3743, '11', 108, 'JM', 18.13619000, -77.66970000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62372, 'Bogwalk', 3746, '14', 108, 'JM', 18.10468000, -76.98792000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62373, 'Bohemia', 3744, '06', 108, 'JM', 18.21239000, -77.46954000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62374, 'Bombay', 3754, '12', 108, 'JM', 18.11780000, -77.44940000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62375, 'Bonnygate', 3747, '05', 108, 'JM', 18.32869000, -76.94606000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62376, 'Borobridge', 3744, '06', 108, 'JM', 18.19779000, -77.42755000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62377, 'Boucher Park', 3751, '02', 108, 'JM', 18.00344000, -76.81257000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62378, 'Boundbrook', 3752, '04', 108, 'JM', 18.17573000, -76.47079000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62379, 'Bounty Hall', 3755, '07', 108, 'JM', 18.45801000, -77.71663000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62380, 'Bournemouth Gardens', 3748, '01', 108, 'JM', 17.97009000, -76.76266000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62381, 'Braes River', 3743, '11', 108, 'JM', 18.09351000, -77.66487000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62382, 'Braeton', 3746, '14', 108, 'JM', 17.94174000, -76.88635000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62383, 'Brainerd', 3747, '05', 108, 'JM', 18.16988000, -76.86890000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62384, 'Brampton', 3755, '07', 108, 'JM', 18.44262000, -77.46424000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62385, 'Brandon Hill', 3751, '02', 108, 'JM', 18.14822000, -76.80513000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62386, 'Brandon Hill', 3745, '08', 108, 'JM', 18.47767000, -77.91117000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62387, 'Brandon Hill', 3753, '13', 108, 'JM', 18.15110000, -77.23663000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62388, 'Breastworks', 3752, '04', 108, 'JM', 18.16000000, -76.46612000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62389, 'Bridgeport', 3746, '14', 108, 'JM', 17.96264000, -76.87809000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62390, 'Brighton', 3743, '11', 108, 'JM', 18.14836000, -77.85604000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62391, 'Brixton Hill', 3753, '13', 108, 'JM', 18.02039000, -77.31744000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62392, 'Broadgate', 3747, '05', 108, 'JM', 18.22847000, -76.80788000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62393, 'Brompton', 3743, '11', 108, 'JM', 18.06795000, -77.88821000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62394, 'Broughton', 3742, '10', 108, 'JM', 18.21747000, -78.22894000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62395, 'Browns Hill', 3746, '14', 108, 'JM', 18.05358000, -77.11855000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62396, 'Browns Town', 3744, '06', 108, 'JM', 18.38464000, -77.35286000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62397, 'Bucknor', 3753, '13', 108, 'JM', 18.00619000, -77.23749000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62398, 'Bucks Common', 3753, '13', 108, 'JM', 17.98228000, -77.23846000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q890358'),
(62399, 'Buff Bay', 3752, '04', 108, 'JM', 18.23287000, -76.66118000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62400, 'Bull Bay/ Seven Mile', 3751, '02', 108, 'JM', 17.95247000, -76.68404000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62401, 'Bull Savanna', 3743, '11', 108, 'JM', 17.88678000, -77.59022000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62402, 'Bull Savannah', 3743, '11', 108, 'JM', 17.88056000, -77.59000000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62403, 'Bunkers Hill', 3755, '07', 108, 'JM', 18.36198000, -77.68612000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62404, 'Burnt Savannah', 3742, '10', 108, 'JM', 18.31676000, -78.12628000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62405, 'Burnt Savannah', 3743, '11', 108, 'JM', 18.03177000, -77.74936000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62406, 'Bushy Park', 3753, '13', 108, 'JM', 17.98243000, -77.25135000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62407, 'Butt-Up', 3754, '12', 108, 'JM', 17.97313000, -77.57777000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62408, 'Bybrook', 3752, '04', 108, 'JM', 18.14641000, -76.65726000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62409, 'Cacoon', 3749, '09', 108, 'JM', 18.41642000, -78.20730000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62410, 'Cacoon Castle', 3749, '09', 108, 'JM', 18.40247000, -78.01336000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62411, 'Cairn Curran', 3742, '10', 108, 'JM', 18.22840000, -78.01130000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62412, 'Calderwood', 3744, '06', 108, 'JM', 18.25641000, -77.27144000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62413, 'Caledonia', 3742, '10', 108, 'JM', 18.25866000, -78.01173000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62414, 'Camberwell', 3747, '05', 108, 'JM', 18.23930000, -76.78088000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62415, 'Cambridge', 3745, '08', 108, 'JM', 18.31308000, -77.89546000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62416, 'Campbell Town', 3748, '01', 108, 'JM', 17.97914000, -76.78134000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62417, 'Cane River', 3751, '02', 108, 'JM', 17.98098000, -76.68624000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62418, 'Canterbury', 3745, '08', 108, 'JM', 18.47919000, -77.91655000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62419, 'Carisbrook', 3743, '11', 108, 'JM', 18.14742000, -77.77367000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62420, 'Carmel', 3742, '10', 108, 'JM', 18.17326000, -77.93929000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62421, 'Carron Hall', 3747, '05', 108, 'JM', 18.27797000, -76.95246000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62422, 'Cascade', 3749, '09', 108, 'JM', 18.39747000, -78.10337000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62423, 'Cascade', 3752, '04', 108, 'JM', 18.09377000, -76.71223000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62424, 'Cascade', 3744, '06', 108, 'JM', 18.23209000, -77.46255000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62425, 'Cash Hill', 3749, '09', 108, 'JM', 18.36920000, -78.10938000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62426, 'Cassia Park', 3751, '02', 108, 'JM', 18.02047000, -76.80665000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62427, 'Castle Comfort', 3752, '04', 108, 'JM', 18.14217000, -76.34634000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62428, 'Castleton', 3747, '05', 108, 'JM', 18.17618000, -76.78340000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62429, 'Catadupa', 3745, '08', 108, 'JM', 18.28052000, -77.86579000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62430, 'Catherine Hall', 3745, '08', 108, 'JM', 18.46258000, -77.92286000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62431, 'Catherine Mount', 3745, '08', 108, 'JM', 18.45672000, -77.89691000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62432, 'Cauldwell', 3749, '09', 108, 'JM', 18.38583000, -78.24110000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62433, 'Cavaliers', 3751, '02', 108, 'JM', 18.09677000, -76.84321000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62434, 'Cave', 3742, '10', 108, 'JM', 18.21019000, -78.04070000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62435, 'Cave Valley', 3744, '06', 108, 'JM', 18.23268000, -77.36337000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62436, 'Caymanas', 3746, '14', 108, 'JM', 18.03824000, -76.89833000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62437, 'Cedar Grove', 3754, '12', 108, 'JM', 18.01225000, -77.49630000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62438, 'Cedar Valley', 3750, '03', 108, 'JM', 18.02815000, -76.59556000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62439, 'Central Down Town', 3748, '01', 108, 'JM', 17.96958000, -76.79264000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62440, 'Central Port Antonio', 3752, '04', 108, 'JM', 18.18763000, -76.45343000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62441, 'Central Village', 3746, '14', 108, 'JM', 17.99992000, -76.92393000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62442, 'Chalky Hill', 3744, '06', 108, 'JM', 18.37964000, -77.17520000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62443, 'Chambers Pen', 3749, '09', 108, 'JM', 18.40520000, -78.15975000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62444, 'Chantilly', 3754, '12', 108, 'JM', 18.10447000, -77.46625000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62445, 'Chantilly', 3742, '10', 108, 'JM', 18.23059000, -78.12462000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, 'Q1001895'),
(62446, 'Chapelton', 3753, '13', 108, 'JM', 18.08333000, -77.26667000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62447, 'Charles Town', 3752, '04', 108, 'JM', 18.20526000, -76.66390000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62448, 'Charles Town', 3747, '05', 108, 'JM', 18.38918000, -77.03978000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62449, 'Chateau', 3753, '13', 108, 'JM', 17.98972000, -77.18737000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62450, 'Cherry Gardens', 3751, '02', 108, 'JM', 18.04963000, -76.77867000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62451, 'Chester', 3744, '06', 108, 'JM', 18.44786000, -77.26503000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62452, 'Chester Castle', 3749, '09', 108, 'JM', 18.33822000, -77.94722000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62453, 'Christiana', 3754, '12', 108, 'JM', 18.18015000, -77.49045000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62454, 'Chudleigh', 3754, '12', 108, 'JM', 18.15148000, -77.50707000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62455, 'Church Corner', 3750, '03', 108, 'JM', 17.88718000, -76.41898000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62456, 'Claremont', 3744, '06', 108, 'JM', 18.32556000, -77.20643000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62457, 'Clarks Town', 3755, '07', 108, 'JM', 18.42244000, -77.53328000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********'),
(62458, 'Claverty Cottage', 3752, '04', 108, 'JM', 18.14876000, -76.63923000, '2019-10-05 23:07:28', '2019-10-05 23:07:28', 1, '********');

