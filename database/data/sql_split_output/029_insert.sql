INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(14174, 'Raposa', 2015, 'MA', 31, 'BR', -6.51667000, -44.18333000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q22015964'),
(14175, 'Raposos', 1998, 'MG', 31, 'BR', -19.97619000, -43.79018000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1780289'),
(14176, '<PERSON><PERSON>', 1998, 'MG', 31, 'BR', -20.01424000, -42.40893000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1780289'),
(14177, '<PERSON><PERSON>a', 2022, '<PERSON>', 31, '<PERSON>', -25.67431000, -53.54659000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1802916'),
(14178, '<PERSON><PERSON><PERSON><PERSON>s', 2022, 'PR', 31, 'BR', -25.68296000, -50.63115000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1802916'),
(14179, '<PERSON>cife', 2006, 'PE', 31, 'BR', -8.05389000, -34.88111000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q48344'),
(14180, 'Recreio', 1998, 'MG', 31, 'BR', -21.52854000, -42.44327000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q614267'),
(14181, 'Recursolândia', 2020, 'TO', 31, 'BR', -8.66852000, -47.07333000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q614267'),
(14182, 'Redentora', 2001, 'RS', 31, 'BR', -27.56833000, -53.60924000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q614267'),
(14183, 'Redenção', 2016, 'CE', 31, 'BR', -4.22583000, -38.73056000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q2266648'),
(14184, 'Redenção', 2009, 'PA', 31, 'BR', -8.06010000, -50.18116000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2266648'),
(14185, 'Redenção da Serra', 2021, 'SP', 31, 'BR', -23.25672000, -45.50200000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q2266648'),
(14186, 'Redenção do Gurguéia', 2008, 'PI', 31, 'BR', -9.58205000, -44.53802000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2266648'),
(14187, 'Reduto', 1998, 'MG', 31, 'BR', -20.22867000, -41.94273000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2266648'),
(14188, 'Regeneração', 2008, 'PI', 31, 'BR', -6.27844000, -42.48165000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1879913'),
(14189, 'Regente Feijó', 2021, 'SP', 31, 'BR', -22.22139000, -51.30278000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1760843'),
(14190, 'Reginópolis', 2021, 'SP', 31, 'BR', -21.87493000, -49.19025000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1760843'),
(14191, 'Registro', 2021, 'SP', 31, 'BR', -24.48750000, -47.84361000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1798084'),
(14192, 'Relvado', 2001, 'RS', 31, 'BR', -29.12666000, -52.05393000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1798084'),
(14193, 'Remanso', 2002, 'BA', 31, 'BR', -9.54115000, -42.36956000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1798084'),
(14194, 'Remígio', 2005, 'PB', 31, 'BR', -6.94646000, -35.78039000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q289067'),
(14195, 'Renascença', 2022, 'PR', 31, 'BR', -26.21457000, -52.93431000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q289067'),
(14196, 'Reriutaba', 2016, 'CE', 31, 'BR', -4.13835000, -40.61603000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q629638'),
(14197, 'Resende', 1997, 'RJ', 31, 'BR', -22.46889000, -44.44667000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q913390'),
(14198, 'Resende Costa', 1998, 'MG', 31, 'BR', -20.84128000, -44.28110000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q913390'),
(14199, 'Reserva', 2022, 'PR', 31, 'BR', -24.57312000, -50.93141000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q177325'),
(14200, 'Reserva do Cabaçal', 2011, 'MT', 31, 'BR', -14.92946000, -58.45849000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q177325'),
(14201, 'Reserva do Iguaçu', 2022, 'PR', 31, 'BR', -25.84778000, -51.94646000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q177325'),
(14202, 'Residencia Moacir PU5BHV', 2014, 'SC', 31, 'BR', -26.90967000, -49.36547000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q177325'),
(14203, 'Resplendor', 1998, 'MG', 31, 'BR', -19.20265000, -41.14336000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q986252'),
(14204, 'Ressaquinha', 1998, 'MG', 31, 'BR', -21.09458000, -43.78435000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q986252'),
(14205, 'Restinga', 2021, 'SP', 31, 'BR', -20.67652000, -47.52058000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q986252'),
(14206, 'Restinga Sêca', 2001, 'RS', 31, 'BR', -29.82715000, -53.34008000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q986252'),
(14207, 'Retirolândia', 2002, 'BA', 31, 'BR', -11.47733000, -39.40649000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q986252'),
(14208, 'Riachinho', 2020, 'TO', 31, 'BR', -6.46034000, -48.13618000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2197944'),
(14209, 'Riachinho', 1998, 'MG', 31, 'BR', -16.24455000, -45.89421000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1789472'),
(14210, 'Riacho Frio', 2008, 'PI', 31, 'BR', -9.83660000, -44.67899000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1936273'),
(14211, 'Riacho da Cruz', 2019, 'RN', 31, 'BR', -5.92628000, -37.97556000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1936273'),
(14212, 'Riacho das Almas', 2006, 'PE', 31, 'BR', -8.05397000, -35.82633000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1936273'),
(14213, 'Riacho de Santana', 2002, 'BA', 31, 'BR', -13.60917000, -42.93889000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2013792'),
(14214, 'Riacho de Santana', 2019, 'RN', 31, 'BR', -6.28279000, -38.33135000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2013792'),
(14215, 'Riacho de Santo Antônio', 2005, 'PB', 31, 'BR', -7.66143000, -36.13551000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2013792'),
(14216, 'Riacho dos Cavalos', 2005, 'PB', 31, 'BR', -6.47140000, -37.64024000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2013792'),
(14217, 'Riacho dos Machados', 1998, 'MG', 31, 'BR', -16.06608000, -42.97549000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2013792'),
(14218, 'Riachuelo', 2003, 'SE', 31, 'BR', -10.71605000, -37.22502000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2013792'),
(14219, 'Riachuelo', 2019, 'RN', 31, 'BR', -5.83598000, -35.88131000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2013792'),
(14220, 'Riachão', 2015, 'MA', 31, 'BR', -7.36194000, -46.61722000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q685271'),
(14221, 'Riachão', 2005, 'PB', 31, 'BR', -6.55515000, -35.62324000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2329696'),
(14222, 'Riachão das Neves', 2002, 'BA', 31, 'BR', -11.74611000, -44.91000000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q1762348'),
(14223, 'Riachão do Bacamarte', 2005, 'PB', 31, 'BR', -7.26337000, -35.67518000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1762348'),
(14224, 'Riachão do Dantas', 2003, 'SE', 31, 'BR', -11.01281000, -37.78679000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q22034271'),
(14225, 'Riachão do Jacuípe', 2002, 'BA', 31, 'BR', -11.80694000, -39.38556000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q1794367'),
(14226, 'Riachão do Poço', 2005, 'PB', 31, 'BR', -7.17041000, -35.30300000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q969630'),
(14227, 'Rialma', 2000, 'GO', 31, 'BR', -15.31500000, -49.58444000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q258992'),
(14228, 'Rianápolis', 2000, 'GO', 31, 'BR', -15.47467000, -49.44786000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q258992'),
(14229, 'Ribamar Fiquene', 2015, 'MA', 31, 'BR', -5.93436000, -47.29792000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q258992'),
(14230, 'Ribas do Rio Pardo', 2010, 'MS', 31, 'BR', -20.44306000, -53.75917000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q691658'),
(14231, 'Ribeira', 2021, 'SP', 31, 'BR', -24.61113000, -49.03616000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q691658'),
(14232, 'Ribeira do Amparo', 2002, 'BA', 31, 'BR', -11.00558000, -38.38732000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q691658'),
(14233, 'Ribeira do Piauí', 2008, 'PI', 31, 'BR', -8.09706000, -42.57609000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q691658'),
(14234, 'Ribeira do Pombal', 2002, 'BA', 31, 'BR', -10.83444000, -38.53583000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1793776'),
(14235, 'Ribeiro Gonçalves', 2008, 'PI', 31, 'BR', -8.08721000, -45.47197000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1793776'),
(14236, 'Ribeirão', 2006, 'PE', 31, 'BR', -8.50755000, -35.39342000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2088935'),
(14237, 'Ribeirão Bonito', 2021, 'SP', 31, 'BR', -22.06667000, -48.17611000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1760595'),
(14238, 'Ribeirão Branco', 2021, 'SP', 31, 'BR', -24.22083000, -48.76556000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q734937'),
(14239, 'Ribeirão Cascalheira', 2011, 'MT', 31, 'BR', -12.73516000, -51.71456000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q734937'),
(14240, 'Ribeirão Claro', 2022, 'PR', 31, 'BR', -23.29564000, -49.78622000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q734937'),
(14241, 'Ribeirão Corrente', 2021, 'SP', 31, 'BR', -20.44247000, -47.57152000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q734937'),
(14242, 'Ribeirão Grande', 2021, 'SP', 31, 'BR', -24.17568000, -48.34008000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q734937'),
(14243, 'Ribeirão Pires', 2021, 'SP', 31, 'BR', -23.70411000, -46.39991000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q927935'),
(14244, 'Ribeirão Preto', 2021, 'SP', 31, 'BR', -21.17750000, -47.81028000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q188892'),
(14245, 'Ribeirão Vermelho', 1998, 'MG', 31, 'BR', -21.15385000, -45.07881000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1754727'),
(14246, 'Ribeirão da Ilha', 2014, 'SC', 31, 'BR', -27.69934000, -48.53219000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1754727'),
(14247, 'Ribeirão das Neves', 1998, 'MG', 31, 'BR', -19.76694000, -44.08667000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q737689'),
(14248, 'Ribeirão do Largo', 2002, 'BA', 31, 'BR', -15.38558000, -40.65886000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q737689'),
(14249, 'Ribeirão do Pinhal', 2022, 'PR', 31, 'BR', -23.38629000, -50.43197000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q737689'),
(14250, 'Ribeirão do Sul', 2021, 'SP', 31, 'BR', -22.74112000, -49.94632000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q737689'),
(14251, 'Ribeirão dos Índios', 2021, 'SP', 31, 'BR', -21.78470000, -51.57840000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q737689'),
(14252, 'Ribeirãozinho', 2011, 'MT', 31, 'BR', -16.50143000, -52.75910000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q737689'),
(14253, 'Ribeirópolis', 2003, 'SE', 31, 'BR', -10.52176000, -37.37833000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q2079747'),
(14254, 'Rifaina', 2021, 'SP', 31, 'BR', -20.07816000, -47.43839000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2079747'),
(14255, 'Rincão', 2021, 'SP', 31, 'BR', -21.57394000, -47.99687000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q2079747'),
(14256, 'Rinópolis', 2021, 'SP', 31, 'BR', -21.66311000, -50.72038000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q2079747'),
(14257, 'Rio Acima', 1998, 'MG', 31, 'BR', -20.11704000, -43.76542000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q732563'),
(14258, 'Rio Azul', 2022, 'PR', 31, 'BR', -25.74072000, -50.73497000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1964938'),
(14259, 'Rio Bananal', 2018, 'ES', 31, 'BR', -19.26500000, -40.33333000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1806671'),
(14260, 'Rio Bom', 2022, 'PR', 31, 'BR', -23.80220000, -51.44616000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1806671'),
(14261, 'Rio Bonito', 1997, 'RJ', 31, 'BR', -22.74390000, -42.61916000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1802187'),
(14262, 'Rio Bonito do Iguaçu', 2022, 'PR', 31, 'BR', -25.54822000, -52.62154000, '2019-10-05 22:35:26', '2020-05-01 17:22:37', 1, 'Q1803367'),
(14263, 'Rio Branco', 2012, 'AC', 31, 'BR', -9.97472000, -67.81000000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q171612'),
(14264, 'Rio Branco', 2011, 'MT', 31, 'BR', -15.31117000, -58.12450000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q171612'),
(14265, 'Rio Branco do Ivaí', 2022, 'PR', 31, 'BR', -24.35602000, -51.34215000, '2019-10-05 22:35:26', '2020-05-01 17:22:37', 1, 'Q1803505'),
(14266, 'Rio Branco do Sul', 2022, 'PR', 31, 'BR', -25.19000000, -49.31417000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2004402'),
(14267, 'Rio Brilhante', 2010, 'MS', 31, 'BR', -21.80194000, -54.54639000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1793219'),
(14268, 'Rio Casca', 1998, 'MG', 31, 'BR', -20.12888000, -42.66076000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1789345'),
(14269, 'Rio Claro', 2021, 'SP', 31, 'BR', -22.41139000, -47.56139000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q291277'),
(14270, 'Rio Claro', 1997, 'RJ', 31, 'BR', -22.77976000, -44.07721000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q953901'),
(14271, 'Rio Crespo', 2013, 'RO', 31, 'BR', -9.71880000, -62.74641000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q953901'),
(14272, 'Rio Doce', 1998, 'MG', 31, 'BR', -20.20614000, -42.89246000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1780194'),
(14273, 'Rio Espera', 1998, 'MG', 31, 'BR', -20.85358000, -43.52051000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1789479'),
(14274, 'Rio Formoso', 2006, 'PE', 31, 'BR', -8.66877000, -35.16277000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2011054'),
(14275, 'Rio Fortuna', 2014, 'SC', 31, 'BR', -28.08443000, -49.20120000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2011054'),
(14276, 'Rio Grande', 2001, 'RS', 31, 'BR', -32.07811000, -52.35014000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q869571'),
(14277, 'Rio Grande da Serra', 2021, 'SP', 31, 'BR', -23.73345000, -46.37351000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q663633'),
(14278, 'Rio Grande do Piauí', 2008, 'PI', 31, 'BR', -7.86062000, -43.14551000, '2019-10-05 22:35:26', '2020-05-01 17:22:37', 1, 'Q629855'),
(14279, 'Rio Largo', 2007, 'AL', 31, 'BR', -9.47833000, -35.85333000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q530211'),
(14280, 'Rio Manso', 1998, 'MG', 31, 'BR', -20.24916000, -44.34028000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1780188'),
(14281, 'Rio Maria', 2009, 'PA', 31, 'BR', -7.40968000, -49.82886000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2008301'),
(14282, 'Rio Negrinho', 2014, 'SC', 31, 'BR', -26.25444000, -49.51833000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1784714'),
(14283, 'Rio Negro', 2022, 'PR', 31, 'BR', -26.09001000, -49.71582000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1878476'),
(14284, 'Rio Negro', 2010, 'MS', 31, 'BR', -19.44620000, -54.97947000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2167306'),
(14285, 'Rio Novo', 1998, 'MG', 31, 'BR', -21.47241000, -43.15069000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1789460'),
(14286, 'Rio Novo do Sul', 2018, 'ES', 31, 'BR', -20.78326000, -40.93976000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1994852'),
(14287, 'Rio Paranaíba', 1998, 'MG', 31, 'BR', -19.19894000, -46.29215000, '2019-10-05 22:35:26', '2020-05-01 17:22:37', 1, 'Q22065225'),
(14288, 'Rio Pardo', 2001, 'RS', 31, 'BR', -29.98972000, -52.37806000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q514256'),
(14289, 'Rio Pardo de Minas', 1998, 'MG', 31, 'BR', -15.73003000, -42.57320000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1789409'),
(14290, 'Rio Piracicaba', 1998, 'MG', 31, 'BR', -19.97486000, -43.16111000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q22065223'),
(14291, 'Rio Pomba', 1998, 'MG', 31, 'BR', -21.25409000, -43.17161000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q22065221'),
(14292, 'Rio Preto', 1998, 'MG', 31, 'BR', -22.04485000, -43.86975000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1789450'),
(14293, 'Rio Preto da Eva', 2004, 'AM', 31, 'BR', -2.69795000, -59.70172000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1793560'),
(14294, 'Rio Quente', 2000, 'GO', 31, 'BR', -17.81769000, -48.80202000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1793560'),
(14295, 'Rio Real', 2002, 'BA', 31, 'BR', -11.48472000, -37.93278000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q965807'),
(14296, 'Rio Rufino', 2014, 'SC', 31, 'BR', -27.95567000, -49.77920000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q965807'),
(14297, 'Rio Sono', 2020, 'TO', 31, 'BR', -9.64479000, -47.38387000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q965807'),
(14298, 'Rio Tavares', 2014, 'SC', 31, 'BR', -27.64529000, -48.47486000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q965807'),
(14299, 'Rio Tinto', 2005, 'PB', 31, 'BR', -6.79673000, -35.03715000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2085541'),
(14300, 'Rio Verde', 2000, 'GO', 31, 'BR', -17.71888000, -51.04215000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q942064'),
(14301, 'Rio Verde de Mato Grosso', 2010, 'MS', 31, 'BR', -18.91806000, -54.84417000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1805638'),
(14302, 'Rio Vermelho', 1998, 'MG', 31, 'BR', -18.26867000, -43.07424000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1789441'),
(14303, 'Rio da Conceição', 2020, 'TO', 31, 'BR', -11.36815000, -46.77445000, '2019-10-05 22:35:26', '2020-05-01 17:22:38', 1, 'Q1801085'),
(14304, 'Rio das Antas', 2014, 'SC', 31, 'BR', -26.90733000, -51.02961000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1801085'),
(14305, 'Rio das Flores', 1997, 'RJ', 31, 'BR', -22.16104000, -43.57579000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1801085'),
(14306, 'Rio das Ostras', 1997, 'RJ', 31, 'BR', -22.52694000, -41.94500000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1787668'),
(14307, 'Rio das Pedras', 2021, 'SP', 31, 'BR', -22.84082000, -47.59724000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1760134'),
(14308, 'Rio de Contas', 2002, 'BA', 31, 'BR', -13.62169000, -41.68702000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1760134'),
(14309, 'Rio de Janeiro', 1997, 'RJ', 31, 'BR', -22.92008000, -43.33069000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1760134'),
(14310, 'Rio do Antônio', 2002, 'BA', 31, 'BR', -14.22674000, -42.16613000, '2019-10-05 22:35:26', '2020-05-01 17:22:36', 1, 'Q2345752'),
(14311, 'Rio do Campo', 2014, 'SC', 31, 'BR', -26.86090000, -50.11202000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2345752'),
(14312, 'Rio do Fogo', 2019, 'RN', 31, 'BR', -5.38232000, -35.38379000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2345752'),
(14313, 'Rio do Oeste', 2014, 'SC', 31, 'BR', -27.14870000, -49.84549000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q775857'),
(14314, 'Rio do Pires', 2002, 'BA', 31, 'BR', -13.11134000, -42.17747000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q775857'),
(14315, 'Rio do Prado', 1998, 'MG', 31, 'BR', -16.68344000, -40.56002000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1755699'),
(14316, 'Rio do Sul', 2014, 'SC', 31, 'BR', -27.21417000, -49.64306000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1784001'),
(14317, 'Rio dos Bois', 2020, 'TO', 31, 'BR', -9.20901000, -48.44308000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1784001'),
(14318, 'Rio dos Cedros', 2014, 'SC', 31, 'BR', -26.61087000, -49.37226000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1784001'),
(14319, 'Rio dos Índios', 2001, 'RS', 31, 'BR', -27.25523000, -52.87641000, '2019-10-05 22:35:26', '2020-05-01 17:22:38', 1, 'Q1758315'),
(14320, 'Riolândia', 2021, 'SP', 31, 'BR', -19.98083000, -49.68194000, '2019-10-05 22:35:26', '2020-05-01 17:22:38', 1, 'Q1759077'),
(14321, 'Riozinho', 2001, 'RS', 31, 'BR', -29.59847000, -50.39225000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1759077'),
(14322, 'Riqueza', 2014, 'SC', 31, 'BR', -26.98193000, -53.33956000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1759077'),
(14323, 'Ritápolis', 1998, 'MG', 31, 'BR', -20.99288000, -44.37052000, '2019-10-05 22:35:26', '2020-05-01 17:22:37', 1, 'Q1759077'),
(14324, 'Riversul', 2021, 'SP', 31, 'BR', -23.84439000, -49.44354000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1759077'),
(14325, 'Roca Sales', 2001, 'RS', 31, 'BR', -29.22637000, -51.82440000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1759077'),
(14326, 'Rochedo', 2010, 'MS', 31, 'BR', -19.98799000, -54.78184000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1759077'),
(14327, 'Rochedo de Minas', 1998, 'MG', 31, 'BR', -21.64268000, -43.03311000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q304119'),
(14328, 'Rodeio', 2014, 'SC', 31, 'BR', -26.92278000, -49.36639000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2012010'),
(14329, 'Rodeio Bonito', 2001, 'RS', 31, 'BR', -27.46968000, -53.17038000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2012010'),
(14330, 'Rodeiro', 1998, 'MG', 31, 'BR', -21.21255000, -42.84086000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2012010'),
(14331, 'Rodelas', 2002, 'BA', 31, 'BR', -9.21767000, -38.64607000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2012010'),
(14332, 'Rodolfo Fernandes', 2019, 'RN', 31, 'BR', -5.85656000, -38.09336000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2012010'),
(14333, 'Rodrigues Alves', 2012, 'AC', 31, 'BR', -7.85286000, -73.23613000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1800776'),
(14334, 'Rolador', 2001, 'RS', 31, 'BR', -28.27096000, -54.85608000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1800776'),
(14335, 'Rolante', 2001, 'RS', 31, 'BR', -29.65056000, -50.57583000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q984550'),
(14336, 'Rolim de Moura', 2013, 'RO', 31, 'BR', -11.75260000, -61.78967000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q984550'),
(14337, 'Rolândia', 2022, 'PR', 31, 'BR', -23.30972000, -51.36917000, '2019-10-05 22:35:26', '2020-05-01 17:22:37', 1, 'Q1008266'),
(14338, 'Romaria', 1998, 'MG', 31, 'BR', -18.89336000, -47.58130000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1789399'),
(14339, 'Romelândia', 2014, 'SC', 31, 'BR', -26.67239000, -53.31093000, '2019-10-05 22:35:26', '2020-05-01 17:22:38', 1, 'Q1789399'),
(14340, 'Roncador', 2022, 'PR', 31, 'BR', -24.56358000, -52.22448000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2003693'),
(14341, 'Ronda Alta', 2001, 'RS', 31, 'BR', -27.82614000, -52.72873000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2003693'),
(14342, 'Rondinha', 2001, 'RS', 31, 'BR', -27.83287000, -52.91661000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2003693'),
(14343, 'Rondolândia', 2011, 'MT', 31, 'BR', -10.27573000, -61.06224000, '2019-10-05 22:35:26', '2020-05-01 17:22:36', 1, 'Q2012042'),
(14344, 'Rondon', 2022, 'PR', 31, 'BR', -23.47457000, -52.84502000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q2083352'),
(14345, 'Rondon do Pará', 2009, 'PA', 31, 'BR', -4.43977000, -48.59233000, '2019-10-05 22:35:26', '2020-05-01 17:22:37', 1, 'Q2008429'),
(14346, 'Rondonópolis', 2011, 'MT', 31, 'BR', -16.47083000, -54.63556000, '2019-10-05 22:35:26', '2020-05-01 17:22:36', 1, 'Q983714'),
(14347, 'Roque Gonzales', 2001, 'RS', 31, 'BR', -28.05673000, -55.12048000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q983714'),
(14348, 'Rosana', 2021, 'SP', 31, 'BR', -22.49250000, -52.81942000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1795604'),
(14349, 'Roseira', 2021, 'SP', 31, 'BR', -22.93295000, -45.29982000, '2019-10-05 22:35:26', '2019-10-05 22:35:26', 1, 'Q1795604'),
(14350, 'Rosário', 2015, 'MA', 31, 'BR', -2.96001000, -44.17429000, '2019-10-05 22:35:26', '2020-05-01 17:22:36', 1, 'Q1795604'),
(14351, 'Rosário Oeste', 2011, 'MT', 31, 'BR', -14.83611000, -56.42750000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q2012402'),
(14352, 'Rosário da Limeira', 1998, 'MG', 31, 'BR', -20.98600000, -42.51507000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1789508'),
(14353, 'Rosário do Catete', 2003, 'SE', 31, 'BR', -10.68551000, -37.03447000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1789508'),
(14354, 'Rosário do Ivaí', 2022, 'PR', 31, 'BR', -24.30569000, -51.22024000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1789508'),
(14355, 'Rosário do Sul', 2001, 'RS', 31, 'BR', -30.25833000, -54.91417000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q266960'),
(14356, 'Roteiro', 2007, 'AL', 31, 'BR', -9.85906000, -35.98311000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q266960'),
(14357, 'Rubelita', 1998, 'MG', 31, 'BR', -16.34214000, -42.22800000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1789418'),
(14358, 'Rubiataba', 2000, 'GO', 31, 'BR', -15.16444000, -49.80333000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1793260'),
(14359, 'Rubim', 1998, 'MG', 31, 'BR', -16.45147000, -40.49301000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1793260'),
(14360, 'Rubinéia', 2021, 'SP', 31, 'BR', -20.24994000, -51.01836000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1793260'),
(14361, 'Rubiácea', 2021, 'SP', 31, 'BR', -21.36114000, -50.78593000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1793260'),
(14362, 'Rurópolis', 2009, 'PA', 31, 'BR', -4.18795000, -55.18406000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1793260'),
(14363, 'Russas', 2016, 'CE', 31, 'BR', -4.83990000, -38.19597000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1806654'),
(14364, 'Ruy Barbosa', 2002, 'BA', 31, 'BR', -12.28389000, -40.49389000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1807858'),
(14365, 'Ruy Barbosa', 2019, 'RN', 31, 'BR', -5.88561000, -35.91551000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1807858'),
(14366, 'Sabará', 1998, 'MG', 31, 'BR', -19.85393000, -43.78326000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1807858'),
(14367, 'Sabino', 2021, 'SP', 31, 'BR', -21.47970000, -49.57650000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1807858'),
(14368, 'Sabinópolis', 1998, 'MG', 31, 'BR', -18.64885000, -43.06554000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1755810'),
(14369, 'Saboeiro', 2016, 'CE', 31, 'BR', -6.47813000, -39.88569000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2021151'),
(14370, 'Sabáudia', 2022, 'PR', 31, 'BR', -23.36584000, -51.60539000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2021151'),
(14371, 'Saco dos Limoes', 2014, 'SC', 31, 'BR', -27.60864000, -48.53605000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q22072145'),
(14372, 'Sacramento', 1998, 'MG', 31, 'BR', -19.88039000, -47.22600000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q22065210'),
(14373, 'Sagrada Família', 2001, 'RS', 31, 'BR', -27.71478000, -53.12850000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q2007720'),
(14374, 'Sagres', 2021, 'SP', 31, 'BR', -21.86529000, -51.00502000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1796404'),
(14375, 'Sairé', 2006, 'PE', 31, 'BR', -8.30128000, -35.68372000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1796404'),
(14376, 'Saldanha Marinho', 2001, 'RS', 31, 'BR', -28.38701000, -53.09584000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1796404'),
(14377, 'Sales', 2021, 'SP', 31, 'BR', -21.33848000, -49.51847000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1796404'),
(14378, 'Sales Oliveira', 2021, 'SP', 31, 'BR', -20.83364000, -47.84079000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1796404'),
(14379, 'Salesópolis', 2021, 'SP', 31, 'BR', -23.58753000, -45.84743000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1645107'),
(14380, 'Salete', 2014, 'SC', 31, 'BR', -26.93760000, -49.99718000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1645107'),
(14381, 'Salgadinho', 2006, 'PE', 31, 'BR', -7.92406000, -35.59677000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2278378'),
(14382, 'Salgadinho', 2005, 'PB', 31, 'BR', -7.08934000, -36.86318000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2278378'),
(14383, 'Salgado', 2003, 'SE', 31, 'BR', -11.03194000, -37.47500000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2013090'),
(14384, 'Salgado Filho', 2022, 'PR', 31, 'BR', -26.12101000, -53.43938000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2013090'),
(14385, 'Salgado de São Félix', 2005, 'PB', 31, 'BR', -7.35694000, -35.44056000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2009391'),
(14386, 'Salgueiro', 2006, 'PE', 31, 'BR', -8.07417000, -39.11917000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2102772'),
(14387, 'Salinas', 1998, 'MG', 31, 'BR', -16.11853000, -42.17403000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q22065209'),
(14388, 'Salinas da Margarida', 2002, 'BA', 31, 'BR', -12.90667000, -38.78043000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q22065209'),
(14389, 'Salinópolis', 2009, 'PA', 31, 'BR', -0.61361000, -47.35611000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1008198'),
(14390, 'Salitre', 2016, 'CE', 31, 'BR', -7.13795000, -40.28978000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2028051'),
(14391, 'Salmourão', 2021, 'SP', 31, 'BR', -21.58291000, -50.87491000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q2028051'),
(14392, 'Saloá', 2006, 'PE', 31, 'BR', -9.06487000, -36.76158000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2028051'),
(14393, 'Saltinho', 2021, 'SP', 31, 'BR', -22.86275000, -47.72668000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2028051'),
(14394, 'Saltinho', 2014, 'SC', 31, 'BR', -26.58366000, -53.02568000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2028051'),
(14395, 'Salto', 2021, 'SP', 31, 'BR', -23.20083000, -47.28694000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1994826'),
(14396, 'Salto Grande', 2021, 'SP', 31, 'BR', -22.87363000, -49.96190000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1994826'),
(14397, 'Salto Veloso', 2014, 'SC', 31, 'BR', -26.90818000, -51.41468000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1994826'),
(14398, 'Salto da Divisa', 1998, 'MG', 31, 'BR', -16.08589000, -40.04463000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1755556'),
(14399, 'Salto de Pirapora', 2021, 'SP', 31, 'BR', -23.64889000, -47.57333000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1760043'),
(14400, 'Salto do Céu', 2011, 'MT', 31, 'BR', -15.13439000, -57.95902000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q1760043'),
(14401, 'Salto do Itararé', 2022, 'PR', 31, 'BR', -23.61939000, -49.68219000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1760043'),
(14402, 'Salto do Jacuí', 2001, 'RS', 31, 'BR', -29.04212000, -53.16909000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1760043'),
(14403, 'Salto do Lontra', 2022, 'PR', 31, 'BR', -25.77035000, -53.29978000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1760043'),
(14404, 'Salvador', 2002, 'BA', 31, 'BR', -12.97111000, -38.51083000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q36947'),
(14405, 'Salvador das Missões', 2001, 'RS', 31, 'BR', -28.07697000, -54.82880000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q36947'),
(14406, 'Salvador do Sul', 2001, 'RS', 31, 'BR', -29.48006000, -51.51407000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q36947'),
(14407, 'Salvaterra', 2009, 'PA', 31, 'BR', -0.78848000, -48.61953000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q36947'),
(14408, 'Sambaíba', 2015, 'MA', 31, 'BR', -7.61486000, -45.67749000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q36947'),
(14409, 'Sampaio', 2020, 'TO', 31, 'BR', -5.36587000, -47.91898000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q36947'),
(14410, 'Sananduva', 2001, 'RS', 31, 'BR', -27.94972000, -51.80667000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1110228'),
(14411, 'Sanclerlândia', 2000, 'GO', 31, 'BR', -16.28223000, -50.37210000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q1110228'),
(14412, 'Sandolândia', 2020, 'TO', 31, 'BR', -12.40743000, -49.85491000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1110228'),
(14413, 'Sandovalina', 2021, 'SP', 31, 'BR', -22.45478000, -51.85322000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1110228'),
(14414, 'Sangão', 2014, 'SC', 31, 'BR', -28.67007000, -49.12209000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1110228'),
(14415, 'Sanharó', 2006, 'PE', 31, 'BR', -8.29191000, -36.52169000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1110228'),
(14416, 'Sant\'Ana do Livramento', 2001, 'RS', 31, 'BR', -30.78069000, -55.48158000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q917693'),
(14417, 'Santa Adélia', 2021, 'SP', 31, 'BR', -21.24278000, -48.80417000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1760559'),
(14418, 'Santa Albertina', 2021, 'SP', 31, 'BR', -20.03496000, -50.71776000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1760559'),
(14419, 'Santa Amélia', 2022, 'PR', 31, 'BR', -23.26039000, -50.41329000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1760559'),
(14420, 'Santa Branca', 2021, 'SP', 31, 'BR', -23.39667000, -45.88389000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1759617'),
(14421, 'Santa Brígida', 2002, 'BA', 31, 'BR', -9.67904000, -38.11371000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q1759617'),
(14422, 'Santa Bárbara', 2002, 'BA', 31, 'BR', -11.91770000, -38.99206000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q1759617'),
(14423, 'Santa Bárbara', 1998, 'MG', 31, 'BR', -20.02361000, -43.44495000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q22065207'),
(14424, 'Santa Bárbara d\'Oeste', 2021, 'SP', 31, 'BR', -22.75361000, -47.41361000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q983747'),
(14425, 'Santa Bárbara de Goiás', 2000, 'GO', 31, 'BR', -16.60228000, -49.68624000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q983747'),
(14426, 'Santa Bárbara do Leste', 1998, 'MG', 31, 'BR', -19.94064000, -42.10329000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1789488'),
(14427, 'Santa Bárbara do Monte Verde', 1998, 'MG', 31, 'BR', -21.97690000, -43.69324000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1789425'),
(14428, 'Santa Bárbara do Pará', 2009, 'PA', 31, 'BR', -1.19832000, -48.25095000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1789425'),
(14429, 'Santa Bárbara do Sul', 2001, 'RS', 31, 'BR', -28.37429000, -53.25738000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1789425'),
(14430, 'Santa Bárbara do Tugúrio', 1998, 'MG', 31, 'BR', -21.23902000, -43.52380000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1789425'),
(14431, 'Santa Carmem', 2011, 'MT', 31, 'BR', -11.90602000, -54.80769000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1789425'),
(14432, 'Santa Cecília', 2014, 'SC', 31, 'BR', -26.96083000, -50.42694000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q931280'),
(14433, 'Santa Cecília', 2005, 'PB', 31, 'BR', -7.72027000, -35.93681000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2102814'),
(14434, 'Santa Cecília do Pavão', 2022, 'PR', 31, 'BR', -23.54436000, -50.82283000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2102814'),
(14435, 'Santa Cecília do Sul', 2001, 'RS', 31, 'BR', -28.17256000, -51.92401000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q2007657'),
(14436, 'Santa Clara d\'Oeste', 2021, 'SP', 31, 'BR', -20.07534000, -50.89870000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1649614'),
(14437, 'Santa Clara do Sul', 2001, 'RS', 31, 'BR', -29.45575000, -52.14362000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1649614'),
(14438, 'Santa Cruz', 2019, 'RN', 31, 'BR', -6.22944000, -36.02278000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1854793'),
(14439, 'Santa Cruz', 2006, 'PE', 31, 'BR', -8.30615000, -40.31890000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1854793'),
(14440, 'Santa Cruz', 2005, 'PB', 31, 'BR', -6.54542000, -38.04968000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2329762'),
(14441, 'Santa Cruz Cabrália', 2002, 'BA', 31, 'BR', -16.27806000, -39.02472000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q993466'),
(14442, 'Santa Cruz da Baixa Verde', 2006, 'PE', 31, 'BR', -7.84821000, -38.15341000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q993466'),
(14443, 'Santa Cruz da Conceição', 2021, 'SP', 31, 'BR', -22.12608000, -47.49276000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q993466'),
(14444, 'Santa Cruz da Esperança', 2021, 'SP', 31, 'BR', -21.26170000, -47.44749000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q993466'),
(14445, 'Santa Cruz da Vitória', 2002, 'BA', 31, 'BR', -14.90311000, -39.79148000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q993466'),
(14446, 'Santa Cruz das Palmeiras', 2021, 'SP', 31, 'BR', -21.82694000, -47.24861000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1648129'),
(14447, 'Santa Cruz de Goiás', 2000, 'GO', 31, 'BR', -17.45147000, -48.57101000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q1648129'),
(14448, 'Santa Cruz de Minas', 1998, 'MG', 31, 'BR', -21.12001000, -44.21369000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1648129'),
(14449, 'Santa Cruz de Monte Castelo', 2022, 'PR', 31, 'BR', -23.14407000, -53.36640000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1648129'),
(14450, 'Santa Cruz de Salinas', 1998, 'MG', 31, 'BR', -16.06175000, -41.79919000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1770708'),
(14451, 'Santa Cruz do Arari', 2009, 'PA', 31, 'BR', -0.59603000, -49.29289000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1770708'),
(14452, 'Santa Cruz do Capibaribe', 2006, 'PE', 31, 'BR', -7.95750000, -36.20472000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2082237'),
(14453, 'Santa Cruz do Escalvado', 1998, 'MG', 31, 'BR', -20.23339000, -42.82110000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1124627'),
(14454, 'Santa Cruz do Piauí', 2008, 'PI', 31, 'BR', -7.26490000, -41.76380000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1124627'),
(14455, 'Santa Cruz do Rio Pardo', 2021, 'SP', 31, 'BR', -22.89889000, -49.63250000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1646321'),
(14456, 'Santa Cruz do Sul', 2001, 'RS', 31, 'BR', -29.71750000, -52.42583000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q781510'),
(14457, 'Santa Cruz do Xingu', 2011, 'MT', 31, 'BR', -10.15595000, -52.49799000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q781510'),
(14458, 'Santa Cruz dos Milagres', 2008, 'PI', 31, 'BR', -5.83227000, -41.95795000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q781510'),
(14459, 'Santa Efigênia de Minas', 1998, 'MG', 31, 'BR', -18.86234000, -42.40032000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1789393'),
(14460, 'Santa Ernestina', 2021, 'SP', 31, 'BR', -21.45303000, -48.37797000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1789393'),
(14461, 'Santa Filomena', 2008, 'PI', 31, 'BR', -8.89582000, -45.66715000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2340263'),
(14462, 'Santa Filomena', 2006, 'PE', 31, 'BR', -8.28797000, -40.59304000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2340263'),
(14463, 'Santa Filomena do Maranhão', 2015, 'MA', 31, 'BR', -5.48267000, -44.55176000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q2065314'),
(14464, 'Santa Fé', 2022, 'PR', 31, 'BR', -23.03990000, -51.81359000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2065314'),
(14465, 'Santa Fé de Goiás', 2000, 'GO', 31, 'BR', -15.62304000, -51.13850000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q2065314'),
(14466, 'Santa Fé de Minas', 1998, 'MG', 31, 'BR', -16.68124000, -45.59725000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1755784'),
(14467, 'Santa Fé do Araguaia', 2020, 'TO', 31, 'BR', -7.10746000, -48.95580000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1755784'),
(14468, 'Santa Fé do Sul', 2021, 'SP', 31, 'BR', -20.21111000, -50.92583000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1648142'),
(14469, 'Santa Gertrudes', 2021, 'SP', 31, 'BR', -22.45667000, -47.53028000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1222916'),
(14470, 'Santa Helena', 2015, 'MA', 31, 'BR', -2.42660000, -45.38362000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2103762'),
(14471, 'Santa Helena', 2005, 'PB', 31, 'BR', -6.72745000, -38.59506000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2103762'),
(14472, 'Santa Helena', 2022, 'PR', 31, 'BR', -24.90689000, -54.28722000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2355851'),
(14473, 'Santa Helena', 2014, 'SC', 31, 'BR', -26.92350000, -53.60898000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2355851'),
(14474, 'Santa Helena de Goiás', 2000, 'GO', 31, 'BR', -17.80542000, -50.53885000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q1795206'),
(14475, 'Santa Helena de Minas', 1998, 'MG', 31, 'BR', -16.90289000, -40.66215000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q970778'),
(14476, 'Santa Inês', 2002, 'BA', 31, 'BR', -13.29222000, -39.81889000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q512109'),
(14477, 'Santa Inês', 2015, 'MA', 31, 'BR', -3.75659000, -45.40393000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q2469099'),
(14478, 'Santa Inês', 2005, 'PB', 31, 'BR', -7.68334000, -38.59346000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2469099'),
(14479, 'Santa Inês', 2022, 'PR', 31, 'BR', -22.68740000, -51.89746000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2283744'),
(14480, 'Santa Isabel', 2021, 'SP', 31, 'BR', -23.29440000, -46.24151000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q952830'),
(14481, 'Santa Isabel', 2000, 'GO', 31, 'BR', -15.26996000, -49.37797000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q952830'),
(14482, 'Santa Isabel do Ivaí', 2022, 'PR', 31, 'BR', -23.13820000, -53.25909000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q952830'),
(14483, 'Santa Isabel do Rio Negro', 2004, 'AM', 31, 'BR', -0.41389000, -65.01917000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1754543'),
(14484, 'Santa Izabel do Oeste', 2022, 'PR', 31, 'BR', -25.77567000, -53.41047000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1754543'),
(14485, 'Santa Isabel do Pará', 2009, 'PA', 31, 'BR', -1.37975000, -48.12221000, '2019-10-05 22:35:27', '2025-04-28 16:16:39', 1, 'Q1754543'),
(14486, 'Santa Juliana', 1998, 'MG', 31, 'BR', -19.35572000, -47.53705000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1789496'),
(14487, 'Santa Leopoldina', 2018, 'ES', 31, 'BR', -20.10056000, -40.52972000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q678885'),
(14488, 'Santa Luz', 2008, 'PI', 31, 'BR', -8.97437000, -44.27778000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1938964'),
(14489, 'Santa Luzia', 2005, 'PB', 31, 'BR', -6.87222000, -36.91861000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2329720'),
(14490, 'Santa Luzia', 1998, 'MG', 31, 'BR', -19.76972000, -43.85139000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q942235'),
(14491, 'Santa Luzia', 2015, 'MA', 31, 'BR', -4.18778000, -45.87797000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1977528'),
(14492, 'Santa Luzia', 2002, 'BA', 31, 'BR', -15.46388000, -39.27671000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1977528'),
(14493, 'Santa Luzia d\'Oeste', 2013, 'RO', 31, 'BR', -12.10467000, -61.79440000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1977528'),
(14494, 'Santa Luzia do Itanhy', 2003, 'SE', 31, 'BR', -11.36149000, -37.47848000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1977528'),
(14495, 'Santa Luzia do Norte', 2007, 'AL', 31, 'BR', -9.61082000, -35.82917000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1977528'),
(14496, 'Santa Luzia do Paruá', 2015, 'MA', 31, 'BR', -2.54684000, -45.75943000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q2064642'),
(14497, 'Santa Luzia do Pará', 2009, 'PA', 31, 'BR', -1.65750000, -46.92715000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q2064642'),
(14498, 'Santa Lúcia', 2021, 'SP', 31, 'BR', -21.66472000, -48.08283000, '2019-10-05 22:35:27', '2020-05-01 17:22:38', 1, 'Q1948975'),
(14499, 'Santa Lúcia', 2022, 'PR', 31, 'BR', -25.41465000, -53.55213000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q1948975'),
(14500, 'Santa Margarida', 1998, 'MG', 31, 'BR', -20.43571000, -42.27056000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1948975'),
(14501, 'Santa Margarida do Sul', 2001, 'RS', 31, 'BR', -30.36744000, -54.10999000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q730999'),
(14502, 'Santa Maria', 2001, 'RS', 31, 'BR', -29.68417000, -53.80694000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q740833'),
(14503, 'Santa Maria', 2019, 'RN', 31, 'BR', -5.84531000, -35.71995000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1954847'),
(14504, 'Santa Maria Madalena', 1997, 'RJ', 31, 'BR', -21.95791000, -41.90903000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1954847'),
(14505, 'Santa Maria da Boa Vista', 2006, 'PE', 31, 'BR', -8.80778000, -39.82556000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2010898'),
(14506, 'Santa Maria da Serra', 2021, 'SP', 31, 'BR', -22.53752000, -48.14455000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q2010898'),
(14507, 'Santa Maria da Vitória', 2002, 'BA', 31, 'BR', -13.38814000, -44.19868000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q1761852'),
(14508, 'Santa Maria das Barreiras', 2009, 'PA', 31, 'BR', -8.63667000, -50.26578000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1761852'),
(14509, 'Santa Maria de Itabira', 1998, 'MG', 31, 'BR', -19.43588000, -43.06843000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q1761852'),
(14510, 'Santa Maria de Jetibá', 2018, 'ES', 31, 'BR', -20.02745000, -40.74336000, '2019-10-05 22:35:27', '2020-05-01 17:22:36', 1, 'Q616486'),
(14511, 'Santa Maria do Cambucá', 2006, 'PE', 31, 'BR', -7.81441000, -35.88175000, '2019-10-05 22:35:27', '2020-05-01 17:22:37', 1, 'Q616486'),
(14512, 'Santa Maria do Herval', 2001, 'RS', 31, 'BR', -29.47954000, -50.98806000, '2019-10-05 22:35:27', '2019-10-05 22:35:27', 1, 'Q616486'),
(14513, 'Santa Maria do Oeste', 2022, 'PR', 31, 'BR', -24.92004000, -51.96628000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q616486'),
(14514, 'Santa Maria do Pará', 2009, 'PA', 31, 'BR', -1.35028000, -47.57556000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1901157'),
(14515, 'Santa Maria do Salto', 1998, 'MG', 31, 'BR', -16.30440000, -40.11096000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1901157'),
(14516, 'Santa Maria do Suaçuí', 1998, 'MG', 31, 'BR', -18.25258000, -42.33882000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q385048'),
(14517, 'Santa Maria do Tocantins', 2020, 'TO', 31, 'BR', -8.81566000, -47.85337000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q385048'),
(14518, 'Santa Mariana', 2022, 'PR', 31, 'BR', -23.04831000, -50.52939000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q385048'),
(14519, 'Santa Mercedes', 2021, 'SP', 31, 'BR', -21.31829000, -51.74637000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q385048'),
(14520, 'Santa Monica', 2014, 'SC', 31, 'BR', -27.59137000, -48.50756000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q385048'),
(14521, 'Santa Mônica', 2022, 'PR', 31, 'BR', -23.16554000, -53.11244000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q2003719'),
(14522, 'Santa Quitéria', 2016, 'CE', 31, 'BR', -4.33194000, -40.15667000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q2027609'),
(14523, 'Santa Quitéria do Maranhão', 2015, 'MA', 31, 'BR', -3.30241000, -42.95484000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q2013052'),
(14524, 'Santa Rita', 2015, 'MA', 31, 'BR', -3.16346000, -44.32883000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1915115'),
(14525, 'Santa Rita', 2005, 'PB', 31, 'BR', -7.10437000, -34.97387000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1937304'),
(14526, 'Santa Rita d\'Oeste', 2021, 'SP', 31, 'BR', -20.08064000, -50.81371000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1937304'),
(14527, 'Santa Rita de Caldas', 1998, 'MG', 31, 'BR', -22.01936000, -46.27369000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1755160'),
(14528, 'Santa Rita de Cássia', 2002, 'BA', 31, 'BR', -11.02715000, -44.59582000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q1755160'),
(14529, 'Santa Rita de Ibitipoca', 1998, 'MG', 31, 'BR', -21.56153000, -43.95797000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789091'),
(14530, 'Santa Rita de Jacutinga', 1998, 'MG', 31, 'BR', -22.10905000, -44.10325000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789091'),
(14531, 'Santa Rita de Minas', 1998, 'MG', 31, 'BR', -19.86922000, -42.12700000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789077'),
(14532, 'Santa Rita do Araguaia', 2000, 'GO', 31, 'BR', -17.23294000, -53.07676000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789077'),
(14533, 'Santa Rita do Itueto', 1998, 'MG', 31, 'BR', -19.41798000, -41.40277000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789077'),
(14534, 'Santa Rita do Novo Destino', 2000, 'GO', 31, 'BR', -14.82658000, -49.05935000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789077'),
(14535, 'Santa Rita do Pardo', 2010, 'MS', 31, 'BR', -21.35479000, -52.69315000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789077'),
(14536, 'Santa Rita do Passa Quatro', 2021, 'SP', 31, 'BR', -21.71028000, -47.47806000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1649619'),
(14537, 'Santa Rita do Sapucaí', 1998, 'MG', 31, 'BR', -22.25623000, -45.67459000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1650861'),
(14538, 'Santa Rita do Tocantins', 2020, 'TO', 31, 'BR', -10.97360000, -49.37112000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1650861'),
(14539, 'Santa Rita do Trivelato', 2011, 'MT', 31, 'BR', -13.71416000, -55.40171000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2065385'),
(14540, 'Santa Rosa', 2001, 'RS', 31, 'BR', -27.87083000, -54.48139000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q995326'),
(14541, 'Santa Rosa da Serra', 1998, 'MG', 31, 'BR', -19.53874000, -45.98000000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789257'),
(14542, 'Santa Rosa de Goiás', 2000, 'GO', 31, 'BR', -16.06989000, -49.48147000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q1789257'),
(14543, 'Santa Rosa de Lima', 2003, 'SE', 31, 'BR', -10.63139000, -37.22866000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789257'),
(14544, 'Santa Rosa de Lima', 2014, 'SC', 31, 'BR', -28.01252000, -49.18941000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q706547'),
(14545, 'Santa Rosa de Viterbo', 2021, 'SP', 31, 'BR', -21.47278000, -47.36306000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1097771'),
(14546, 'Santa Rosa do Piauí', 2008, 'PI', 31, 'BR', -6.82877000, -42.24627000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1097771'),
(14547, 'Santa Rosa do Purus', 2012, 'AC', 31, 'BR', -9.47730000, -70.39032000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1097771'),
(14548, 'Santa Rosa do Sul', 2014, 'SC', 31, 'BR', -29.09751000, -49.73510000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1097771'),
(14549, 'Santa Rosa do Tocantins', 2020, 'TO', 31, 'BR', -11.38278000, -48.07295000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1097771'),
(14550, 'Santa Salete', 2021, 'SP', 31, 'BR', -20.27360000, -50.72826000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1097771'),
(14551, 'Santa Teresa', 2018, 'ES', 31, 'BR', -19.93556000, -40.60028000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2039418'),
(14552, 'Santa Teresinha', 2005, 'PB', 31, 'BR', -7.12089000, -37.49654000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q680275'),
(14553, 'Santa Tereza', 2001, 'RS', 31, 'BR', -29.15063000, -51.72160000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1757691'),
(14554, 'Santa Tereza de Goiás', 2000, 'GO', 31, 'BR', -13.55111000, -48.99503000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q1757691'),
(14555, 'Santa Tereza do Oeste', 2022, 'PR', 31, 'BR', -25.04438000, -53.61939000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1757691'),
(14556, 'Santa Tereza do Tocantins', 2020, 'TO', 31, 'BR', -10.29980000, -47.72597000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1757691'),
(14557, 'Santa Terezinha', 2002, 'BA', 31, 'BR', -12.67120000, -39.55082000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1795259'),
(14558, 'Santa Terezinha', 2006, 'PE', 31, 'BR', -7.42664000, -37.44422000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1795259'),
(14559, 'Santa Terezinha', 2014, 'SC', 31, 'BR', -26.66961000, -50.01340000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2314224'),
(14560, 'Santa Terezinha', 2011, 'MT', 31, 'BR', -10.39138000, -50.83261000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2314224'),
(14561, 'Santa Terezinha de Goiás', 2000, 'GO', 31, 'BR', -14.31049000, -49.71445000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q2314224'),
(14562, 'Santa Terezinha de Itaipu', 2022, 'PR', 31, 'BR', -25.41349000, -54.42251000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1802908'),
(14563, 'Santa Terezinha do Progresso', 2014, 'SC', 31, 'BR', -26.59871000, -53.18418000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1802908'),
(14564, 'Santa Terezinha do Tocantins', 2020, 'TO', 31, 'BR', -6.48040000, -47.70055000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1802908'),
(14565, 'Santa Vitória', 1998, 'MG', 31, 'BR', -19.09193000, -50.29906000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q22065178'),
(14566, 'Santa Vitória do Palmar', 2001, 'RS', 31, 'BR', -33.51889000, -53.36806000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q984562'),
(14567, 'Santaluz', 2002, 'BA', 31, 'BR', -11.25583000, -39.37472000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1793861'),
(14568, 'Santana', 1999, 'AP', 31, 'BR', -0.05833000, -51.18167000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1994845'),
(14569, 'Santana', 2002, 'BA', 31, 'BR', -14.46667000, -41.80000000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q22034774'),
(14570, 'Santana da Boa Vista', 2001, 'RS', 31, 'BR', -30.76195000, -53.18297000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q22034774'),
(14571, 'Santana da Ponte Pensa', 2021, 'SP', 31, 'BR', -20.25649000, -50.79852000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q22034774'),
(14572, 'Santana da Vargem', 1998, 'MG', 31, 'BR', -21.26761000, -45.48969000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q22065194'),
(14573, 'Santana de Cataguases', 1998, 'MG', 31, 'BR', -21.28322000, -42.56580000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789181'),
(14574, 'Santana de Mangueira', 2005, 'PB', 31, 'BR', -7.66593000, -38.35848000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789181'),
(14575, 'Santana de Parnaíba', 2021, 'SP', 31, 'BR', -23.44417000, -46.91778000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q526318'),
(14576, 'Santana de Pirapama', 1998, 'MG', 31, 'BR', -18.90777000, -43.92702000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q526318'),
(14577, 'Santana do Acaraú', 2016, 'CE', 31, 'BR', -3.46056000, -40.21222000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q2027584'),
(14578, 'Santana do Araguaia', 2009, 'PA', 31, 'BR', -9.13867000, -50.67300000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2008589'),
(14579, 'Santana do Cariri', 2016, 'CE', 31, 'BR', -7.17968000, -39.78451000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2008589'),
(14580, 'Santana do Deserto', 1998, 'MG', 31, 'BR', -21.94535000, -43.17445000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1789205'),
(14581, 'Santana do Garambéu', 1998, 'MG', 31, 'BR', -21.64675000, -44.06937000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1789205'),
(14582, 'Santana do Ipanema', 2007, 'AL', 31, 'BR', -9.37833000, -37.24528000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2021116'),
(14583, 'Santana do Itararé', 2022, 'PR', 31, 'BR', -23.73441000, -49.61942000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q2021116'),
(14584, 'Santana do Jacaré', 1998, 'MG', 31, 'BR', -20.88867000, -45.07914000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q678998'),
(14585, 'Santana do Livramento', 2001, 'RS', 31, 'BR', -30.89083000, -55.53278000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q22034828'),
(14586, 'Santana do Manhuaçu', 1998, 'MG', 31, 'BR', -20.05562000, -41.89770000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q675814'),
(14587, 'Santana do Maranhão', 2015, 'MA', 31, 'BR', -3.13508000, -42.74876000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q675814'),
(14588, 'Santana do Matos', 2019, 'RN', 31, 'BR', -5.95750000, -36.65556000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1786881'),
(14589, 'Santana do Mundaú', 2007, 'AL', 31, 'BR', -9.16806000, -36.22222000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q22016386'),
(14590, 'Santana do Paraíso', 1998, 'MG', 31, 'BR', -19.38010000, -42.54068000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q929590'),
(14591, 'Santana do Piauí', 2008, 'PI', 31, 'BR', -6.95247000, -41.46185000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q929590'),
(14592, 'Santana do Riacho', 1998, 'MG', 31, 'BR', -19.18420000, -43.68380000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q929590'),
(14593, 'Santana do Seridó', 2019, 'RN', 31, 'BR', -6.75207000, -36.76599000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q929590'),
(14594, 'Santana do São Francisco', 2003, 'SE', 31, 'BR', -10.27648000, -36.63314000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q929590'),
(14595, 'Santana dos Garrotes', 2005, 'PB', 31, 'BR', -7.38163000, -37.95386000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q929590'),
(14596, 'Santana dos Montes', 1998, 'MG', 31, 'BR', -20.79856000, -43.64577000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q929590'),
(14597, 'Santanópolis', 2002, 'BA', 31, 'BR', -12.02416000, -38.88007000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q929590'),
(14598, 'Santarém', 2009, 'PA', 31, 'BR', -2.44306000, -54.70833000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q659450'),
(14599, 'Santarém Novo', 2009, 'PA', 31, 'BR', -0.88782000, -47.36301000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q659450'),
(14600, 'Santiago', 2001, 'RS', 31, 'BR', -29.11368000, -54.73359000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q996716'),
(14601, 'Santiago do Sul', 2014, 'SC', 31, 'BR', -26.63060000, -52.67466000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q996716'),
(14602, 'Santo Afonso', 2011, 'MT', 31, 'BR', -14.46134000, -57.38575000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q2012245'),
(14603, 'Santo Amaro', 2002, 'BA', 31, 'BR', -12.54667000, -38.71194000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q739789'),
(14604, 'Santo Amaro da Imperatriz', 2014, 'SC', 31, 'BR', -27.68806000, -48.77861000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q22034896'),
(14605, 'Santo Amaro das Brotas', 2003, 'SE', 31, 'BR', -10.78889000, -37.05444000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q371369'),
(14606, 'Santo Amaro do Maranhão', 2015, 'MA', 31, 'BR', -2.62026000, -43.16505000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q371369'),
(14607, 'Santo Anastácio', 2021, 'SP', 31, 'BR', -22.01522000, -51.70097000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q1800625'),
(14608, 'Santo André', 2005, 'PB', 31, 'BR', -7.24178000, -36.61460000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q2078761'),
(14609, 'Santo André', 2021, 'SP', 31, 'BR', -23.71057000, -46.50460000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q191652'),
(14610, 'Santo Antônio', 2019, 'RN', 31, 'BR', -6.31056000, -35.47889000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q2008095'),
(14611, 'Santo Antônio da Alegria', 2021, 'SP', 31, 'BR', -21.09238000, -47.20360000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q2008095'),
(14612, 'Santo Antônio da Barra', 2000, 'GO', 31, 'BR', -17.51225000, -50.63068000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q1805688'),
(14613, 'Santo Antônio da Patrulha', 2001, 'RS', 31, 'BR', -29.82214000, -50.56024000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q1805688'),
(14614, 'Santo Antônio da Platina', 2022, 'PR', 31, 'BR', -23.29500000, -50.07722000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q929328'),
(14615, 'Santo Antônio das Missões', 2001, 'RS', 31, 'BR', -28.47797000, -55.40876000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q929328'),
(14616, 'Santo Antônio de Goiás', 2000, 'GO', 31, 'BR', -16.50007000, -49.31097000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q929328'),
(14617, 'Santo Antônio de Jesus', 2002, 'BA', 31, 'BR', -12.96889000, -39.26139000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q1764163'),
(14618, 'Santo Antônio de Lisboa', 2008, 'PI', 31, 'BR', -6.87642000, -41.18103000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q659267'),
(14619, 'Santo Antônio de Posse', 2021, 'SP', 31, 'BR', -22.60611000, -46.91944000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q1760121'),
(14620, 'Santo Antônio de Pádua', 1997, 'RJ', 31, 'BR', -21.57245000, -42.21090000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1788412'),
(14621, 'Santo Antônio do Amparo', 1998, 'MG', 31, 'BR', -20.91660000, -44.95511000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q986277'),
(14622, 'Santo Antônio do Aracanguá', 2021, 'SP', 31, 'BR', -20.85619000, -50.53106000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q986277'),
(14623, 'Santo Antônio do Aventureiro', 1998, 'MG', 31, 'BR', -21.74424000, -42.81100000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q522097'),
(14624, 'Santo Antônio do Caiuá', 2022, 'PR', 31, 'BR', -22.69373000, -52.30897000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q522097'),
(14625, 'Santo Antônio do Descoberto', 2000, 'GO', 31, 'BR', -16.08073000, -48.29596000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q522097'),
(14626, 'Santo Antônio do Grama', 1998, 'MG', 31, 'BR', -20.32329000, -42.61551000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1789248'),
(14627, 'Santo Antônio do Itambé', 1998, 'MG', 31, 'BR', -18.49169000, -43.26278000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1789248'),
(14628, 'Santo Antônio do Içá', 2004, 'AM', 31, 'BR', -3.10222000, -67.93972000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q970224'),
(14629, 'Santo Antônio do Jacinto', 1998, 'MG', 31, 'BR', -16.50934000, -40.27997000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q970224'),
(14630, 'Santo Antônio do Jardim', 2021, 'SP', 31, 'BR', -22.12625000, -46.67237000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q1658504'),
(14631, 'Santo Antônio do Leste', 2011, 'MT', 31, 'BR', -14.82454000, -53.74136000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q1966520'),
(14632, 'Santo Antônio do Leverger', 2011, 'MT', 31, 'BR', -16.78576000, -55.31327000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q962587'),
(14633, 'Santo Antônio do Monte', 1998, 'MG', 31, 'BR', -20.07993000, -45.30587000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q956065'),
(14634, 'Santo Antônio do Palma', 2001, 'RS', 31, 'BR', -28.48630000, -52.01930000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q956065'),
(14635, 'Santo Antônio do Paraíso', 2022, 'PR', 31, 'BR', -23.56361000, -50.61940000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q956065'),
(14636, 'Santo Antônio do Pinhal', 2021, 'SP', 31, 'BR', -22.82143000, -45.69968000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q956065'),
(14637, 'Santo Antônio do Planalto', 2001, 'RS', 31, 'BR', -28.38607000, -52.66579000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q956065'),
(14638, 'Santo Antônio do Retiro', 1998, 'MG', 31, 'BR', -15.30144000, -42.64417000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q732719'),
(14639, 'Santo Antônio do Rio Abaixo', 1998, 'MG', 31, 'BR', -19.25051000, -43.25300000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q732719'),
(14640, 'Santo Antônio do Sudoeste', 2022, 'PR', 31, 'BR', -26.07361000, -53.72528000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1803373'),
(14641, 'Santo Antônio do Tauá', 2009, 'PA', 31, 'BR', -1.15194000, -48.12944000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q2009548'),
(14642, 'Santo Antônio dos Lopes', 2015, 'MA', 31, 'BR', -4.82567000, -44.47506000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q2009548'),
(14643, 'Santo Antônio dos Milagres', 2008, 'PI', 31, 'BR', -6.05399000, -42.70083000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1984524'),
(14644, 'Santo Augusto', 2001, 'RS', 31, 'BR', -27.85083000, -53.77722000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q347700'),
(14645, 'Santo Cristo', 2001, 'RS', 31, 'BR', -27.78844000, -54.70378000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q988779'),
(14646, 'Santo Estêvão', 2002, 'BA', 31, 'BR', -12.43028000, -39.25139000, '2019-10-05 22:35:28', '2020-05-01 17:22:36', 1, 'Q1794190'),
(14647, 'Santo Expedito', 2021, 'SP', 31, 'BR', -21.82978000, -51.36993000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1798847'),
(14648, 'Santo Expedito do Sul', 2001, 'RS', 31, 'BR', -27.92306000, -51.67042000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1798847'),
(14649, 'Santo Hipólito', 1998, 'MG', 31, 'BR', -18.38378000, -44.17728000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1798847'),
(14650, 'Santo Inácio', 2022, 'PR', 31, 'BR', -22.72365000, -51.80606000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1798847'),
(14651, 'Santo Inácio do Piauí', 2008, 'PI', 31, 'BR', -7.46906000, -41.91794000, '2019-10-05 22:35:28', '2020-05-01 17:22:37', 1, 'Q1798847'),
(14652, 'Santo Ângelo', 2001, 'RS', 31, 'BR', -28.29917000, -54.26306000, '2019-10-05 22:35:28', '2020-05-01 17:22:38', 1, 'Q824031'),
(14653, 'Santos', 2021, 'SP', 31, 'BR', -23.85663000, -46.27055000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q804027'),
(14654, 'Santos Dumont', 1998, 'MG', 31, 'BR', -21.46453000, -43.52573000, '2019-10-05 22:35:28', '2019-10-05 22:35:28', 1, 'Q1754760'),
(14655, 'Santópolis do Aguapeí', 2021, 'SP', 31, 'BR', -21.67058000, -50.52029000, '2019-10-05 22:35:29', '2020-05-01 17:22:38', 1, 'Q1754760'),
(14656, 'Sapeaçu', 2002, 'BA', 31, 'BR', -12.77200000, -39.23848000, '2019-10-05 22:35:29', '2020-05-01 17:22:36', 1, 'Q1754760'),
(14657, 'Sapezal', 2011, 'MT', 31, 'BR', -13.16448000, -58.68788000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1805417'),
(14658, 'Sapiranga', 2001, 'RS', 31, 'BR', -29.63806000, -51.00694000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q983717'),
(14659, 'Sapopema', 2022, 'PR', 31, 'BR', -23.87746000, -50.61022000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q983717'),
(14660, 'Sapucaia', 2001, 'RS', 31, 'BR', -29.83333000, -51.15000000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q983717'),
(14661, 'Sapucaia', 2009, 'PA', 31, 'BR', -6.84790000, -49.50056000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q983717'),
(14662, 'Sapucaia', 1997, 'RJ', 31, 'BR', -22.04085000, -42.81392000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q748985'),
(14663, 'Sapucaia do Sul', 2001, 'RS', 31, 'BR', -29.81826000, -51.15527000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q748985'),
(14664, 'Sapucaí-Mirim', 1998, 'MG', 31, 'BR', -22.79166000, -45.84222000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q748985'),
(14665, 'Sapé', 2005, 'PB', 31, 'BR', -7.07937000, -35.21734000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q2008707'),
(14666, 'Saquarema', 1997, 'RJ', 31, 'BR', -22.92000000, -42.51028000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q653716'),
(14667, 'Sarandi', 2001, 'RS', 31, 'BR', -27.94389000, -52.92306000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q430538'),
(14668, 'Sarandi', 2022, 'PR', 31, 'BR', -23.44361000, -51.87389000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q22032729'),
(14669, 'Sarapuí', 2021, 'SP', 31, 'BR', -23.65898000, -47.77095000, '2019-10-05 22:35:29', '2020-05-01 17:22:38', 1, 'Q22032729'),
(14670, 'Sardoá', 1998, 'MG', 31, 'BR', -18.77353000, -42.40303000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q1789104'),
(14671, 'Sarutaiá', 2021, 'SP', 31, 'BR', -23.25823000, -49.48372000, '2019-10-05 22:35:29', '2020-05-01 17:22:38', 1, 'Q1789104'),
(14672, 'Sarzedo', 1998, 'MG', 31, 'BR', -20.04463000, -44.13442000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1789187'),
(14673, 'Satuba', 2007, 'AL', 31, 'BR', -9.56333000, -35.82444000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1795925');

