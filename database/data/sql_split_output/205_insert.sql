INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(104344, '<PERSON><PERSON><PERSON><PERSON> v Savinj. Dolini', 4258, '190', 201, 'SI', 46.25639000, 15.12194000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q1858063'),
(104345, 'Š<PERSON>ilj v Slov. Goricah', 4324, '118', 201, 'SI', 46.68167000, 15.64806000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q1858063'),
(104346, 'Š<PERSON>jer<PERSON><PERSON>', 4241, '119', 201, 'SI', 45.84000000, 15.33611000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q1809670'),
(104347, '<PERSON><PERSON><PERSON><PERSON>', 4171, '120', 201, 'S<PERSON>', 46.21722000, 15.39750000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q15921'),
(104348, '<PERSON>entrupert', 4311, '211', 201, 'SI', 45.97778000, 15.09556000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2097770'),
(104349, 'Šentvid <PERSON>', 4270, '061', 201, '<PERSON>I', 46.10559000, 14.43329000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q12068719'),
(104350, 'Šentvid pri Stični', 4305, '039', 201, 'SI', 45.95004000, 14.84344000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2613333'),
(104351, 'Šenčur', 4299, '117', 201, 'SI', 46.24556000, 14.41972000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q1810670'),
(104352, 'Škocjan', 4170, '121', 201, 'SI', 45.90667000, 15.29139000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2367062'),
(104353, 'Škofja Loka', 4316, '122', 201, 'SI', 46.16551000, 14.30626000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q15922'),
(104354, 'Škofljica', 4187, '123', 201, 'SI', 45.98333000, 14.57667000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2191620'),
(104355, 'Šmarca', 4180, '043', 201, 'SI', 46.19333000, 14.59667000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q5101569'),
(104356, 'Šmarje pri Jelšah', 4285, '124', 201, 'SI', 46.22722000, 15.51917000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q4489175'),
(104357, 'Šmarje-Sap', 4336, '032', 201, 'SI', 45.97618000, 14.61177000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q8082833'),
(104358, 'Šmarjeta', 4289, '206', 201, 'SI', 45.88333000, 15.25000000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2113343'),
(104359, 'Šmartno ob Paki', 4296, '125', 201, 'SI', 46.33333000, 15.03333000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2647743'),
(104360, 'Šmartno pri Litiji', 4279, '194', 201, 'SI', 46.04444000, 14.84417000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q392192'),
(104361, 'Šmartno pri Slovenj Gradcu', 4169, '112', 201, 'SI', 46.48944000, 15.10667000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2188264'),
(104362, 'Šoštanj', 4248, '126', 201, 'SI', 46.38000000, 15.04861000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q2766344'),
(104363, 'Štore', 4185, '127', 201, 'SI', 46.22083000, 15.31389000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q3124698'),
(104364, 'Žalec', 4258, '190', 201, 'SI', 46.25151000, 15.16482000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q631387'),
(104365, 'Železniki', 4256, '146', 201, 'SI', 46.22482000, 14.17205000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q3440936'),
(104366, 'Žetale', 4249, '191', 201, 'SI', 46.27356000, 15.82658000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q12751744'),
(104367, 'Žiri', 4192, '147', 201, 'SI', 46.04222000, 14.10722000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q15933'),
(104368, 'Žirovnica', 4276, '192', 201, 'SI', 46.40472000, 14.14000000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q3437411'),
(104369, 'Žužemberk', 4307, '193', 201, 'SI', 45.83389000, 14.92917000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q394340'),
(104370, 'Banská Bystrica', 4352, 'BC', 200, 'SK', 48.73946000, 19.15349000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q144983'),
(104371, 'Banská Štiavnica', 4352, 'BC', 200, 'SK', 48.44858000, 18.91003000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q208110'),
(104372, 'Bardejov', 4354, 'PV', 200, 'SK', 49.29175000, 21.27271000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q208110'),
(104373, 'Bojnice', 4358, 'TC', 200, 'SK', 48.78511000, 18.58640000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q788753'),
(104374, 'Bratislava', 4356, 'BL', 200, 'SK', 48.14816000, 17.10674000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q1780'),
(104375, 'Bratislava - Vajnory', 4356, 'BL', 200, 'SK', 48.20563000, 17.20759000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, '********'),
(104376, 'Brezno', 4352, 'BC', 200, 'SK', 48.80431000, 19.63631000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q465312'),
(104377, 'Brezová pod Bradlom', 4358, 'TC', 200, 'SK', 48.66349000, 17.53905000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q135818'),
(104378, 'Bytča', 4359, 'ZI', 200, 'SK', 49.22404000, 18.55878000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q27907'),
(104379, 'Bánovce nad Bebravou', 4358, 'TC', 200, 'SK', 48.72130000, 18.25754000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q549849'),
(104380, 'Chlmec', 4354, 'PV', 200, 'SK', 48.88628000, 21.93930000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, '********'),
(104381, 'Detva', 4352, 'BC', 200, 'SK', 48.56082000, 19.41954000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q11857'),
(104382, 'Dobšiná', 4353, 'KI', 200, 'SK', 48.82073000, 20.36988000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q279005'),
(104383, 'Dolný Kubín', 4359, 'ZI', 200, 'SK', 49.20983000, 19.30341000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q589309'),
(104384, 'Dubnica nad Váhom', 4358, 'TC', 200, 'SK', 48.95981000, 18.16634000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q481528'),
(104385, 'Dudince', 4352, 'BC', 200, 'SK', 48.17135000, 18.88782000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q531445'),
(104386, 'Dunajská Lužná', 4356, 'BL', 200, 'SK', 48.08347000, 17.26072000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q911359'),
(104387, 'Dunajská Streda', 4355, 'TA', 200, 'SK', 47.99268000, 17.61211000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q646393'),
(104388, 'Fiľakovo', 4352, 'BC', 200, 'SK', 48.26757000, 19.82473000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q247620'),
(104389, 'Gabčíkovo', 4355, 'TA', 200, 'SK', 47.89211000, 17.57884000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q389397'),
(104390, 'Galanta', 4355, 'TA', 200, 'SK', 48.19001000, 17.72747000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q460767'),
(104391, 'Gbely', 4355, 'TA', 200, 'SK', 48.71800000, 17.11628000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q846424'),
(104392, 'Gelnica', 4353, 'KI', 200, 'SK', 48.85584000, 20.93713000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q842028'),
(104393, 'Giraltovce', 4354, 'PV', 200, 'SK', 49.11398000, 21.51731000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q550357'),
(104394, 'Handlová', 4358, 'TC', 200, 'SK', 48.72760000, 18.76012000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q842011'),
(104395, 'Hlohovec', 4355, 'TA', 200, 'SK', 48.43174000, 17.80310000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q754773'),
(104396, 'Holíč', 4355, 'TA', 200, 'SK', 48.81105000, 17.16238000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q653326'),
(104397, 'Hriňová', 4352, 'BC', 200, 'SK', 48.57787000, 19.52574000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q846431'),
(104398, 'HrochoťSlovakia', 4352, 'BC', 200, 'SK', 48.65567000, 19.31284000, '2019-10-05 23:18:04', '2022-08-29 11:00:58', 1, '********'),
(104399, 'Humenné', 4354, 'PV', 200, 'SK', 48.93707000, 21.91625000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q502264'),
(104400, 'Hurbanovo', 4357, 'NI', 200, 'SK', 47.86984000, 18.19233000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q856402'),
(104401, 'Hybe', 4359, 'ZI', 200, 'SK', 49.04439000, 19.82895000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q339642'),
(104402, 'Ilava', 4358, 'TC', 200, 'SK', 48.99769000, 18.23530000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q481564'),
(104403, 'Ivanka pri Dunaji', 4356, 'BL', 200, 'SK', 48.18675000, 17.25540000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q302787'),
(104404, 'Kavečany', 4353, 'KI', 200, 'SK', 48.77592000, 21.20587000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q572748'),
(104405, 'Kežmarok', 4354, 'PV', 200, 'SK', 49.13571000, 20.43352000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q327977'),
(104406, 'Kolárovo', 4357, 'NI', 200, 'SK', 47.92294000, 17.98467000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q374095'),
(104407, 'Komárno', 4357, 'NI', 200, 'SK', 47.76356000, 18.12263000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q210796'),
(104408, 'Kováčová', 4352, 'BC', 200, 'SK', 48.60148000, 19.10252000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q262165'),
(104409, 'Košice', 4353, 'KI', 200, 'SK', 48.71395000, 21.25808000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q25409'),
(104410, 'Košice I', 4353, 'KI', 200, 'SK', 48.72914000, 21.25004000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q830995'),
(104411, 'Košice II', 4353, 'KI', 200, 'SK', 48.69753000, 21.22273000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q280424'),
(104412, 'Košice III', 4353, 'KI', 200, 'SK', 48.73242000, 21.29047000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q548619'),
(104413, 'Košice IV', 4353, 'KI', 200, 'SK', 48.68691000, 21.26570000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q650575'),
(104414, 'Krasňany', 4359, 'ZI', 200, 'SK', 49.21491000, 18.88718000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q1071509'),
(104415, 'Kremnica', 4352, 'BC', 200, 'SK', 48.70519000, 18.91834000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q754776'),
(104416, 'Krompachy', 4353, 'KI', 200, 'SK', 48.91447000, 20.87514000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q842018'),
(104417, 'Krupina', 4352, 'BC', 200, 'SK', 48.35540000, 19.06474000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q756667'),
(104418, 'Kysucké Nové Mesto', 4359, 'ZI', 200, 'SK', 49.30000000, 18.78333000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q516578'),
(104419, 'Lehota pod Vtáčnikom', 4358, 'TC', 200, 'SK', 48.69138000, 18.59944000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q516578'),
(104420, 'Leopoldov', 4355, 'TA', 200, 'SK', 48.44575000, 17.76458000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q690807'),
(104421, 'Levice', 4357, 'NI', 200, 'SK', 48.21563000, 18.60705000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q506693'),
(104422, 'Levoča', 4354, 'PV', 200, 'SK', 49.02173000, 20.59212000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q242478'),
(104423, 'Lipany', 4354, 'PV', 200, 'SK', 49.15376000, 20.96382000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q847118'),
(104424, 'Liptovský Hrádok', 4359, 'ZI', 200, 'SK', 49.03962000, 19.72335000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q338528'),
(104425, 'Liptovský Mikuláš', 4359, 'ZI', 200, 'SK', 49.08061000, 19.62218000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q338528'),
(104426, 'Lučenec', 4352, 'BC', 200, 'SK', 48.33249000, 19.66708000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q728965'),
(104427, 'Lúčky', 4359, 'ZI', 200, 'SK', 49.12944000, 19.40228000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q1068179'),
(104428, 'Malacky', 4356, 'BL', 200, 'SK', 48.43604000, 17.02188000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q837464'),
(104429, 'Marianka', 4356, 'BL', 200, 'SK', 48.24903000, 17.06400000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q266939'),
(104430, 'Martin', 4359, 'ZI', 200, 'SK', 49.06651000, 18.92399000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q27001'),
(104431, 'Medzev', 4353, 'KI', 200, 'SK', 48.70041000, 20.89367000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q135727'),
(104432, 'Medzilaborce', 4354, 'PV', 200, 'SK', 49.27195000, 21.90073000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q578686'),
(104433, 'Michalovce', 4353, 'KI', 200, 'SK', 48.75434000, 21.91950000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q460925'),
(104434, 'Modra', 4356, 'BL', 200, 'SK', 48.33397000, 17.30711000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q596044'),
(104435, 'Moldava nad Bodvou', 4353, 'KI', 200, 'SK', 48.61428000, 20.99957000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q846440'),
(104436, 'Myjava', 4358, 'TC', 200, 'SK', 48.75876000, 17.56866000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q846440'),
(104437, 'Nemšová', 4358, 'TC', 200, 'SK', 48.96702000, 18.11892000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q389942'),
(104438, 'Nitra', 4357, 'NI', 200, 'SK', 48.30763000, 18.08453000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q26397'),
(104439, 'Nižná', 4359, 'ZI', 200, 'SK', 49.31046000, 19.52428000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q31926891'),
(104440, 'Nová Baňa', 4352, 'BC', 200, 'SK', 48.42305000, 18.64037000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q268463'),
(104441, 'Nová Dubnica', 4358, 'TC', 200, 'SK', 48.93479000, 18.14632000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q249935'),
(104442, 'Nová Lesná', 4354, 'PV', 200, 'SK', 49.12253000, 20.26737000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q664187'),
(104443, 'Nováky', 4358, 'TC', 200, 'SK', 48.71106000, 18.53389000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q909301'),
(104444, 'Nové Mesto nad Váhom', 4358, 'TC', 200, 'SK', 48.75763000, 17.83090000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q376987'),
(104445, 'Nové Zámky', 4357, 'NI', 200, 'SK', 47.98544000, 18.16195000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q376987'),
(104446, 'Námestovo', 4359, 'ZI', 200, 'SK', 49.40790000, 19.48032000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q377650'),
(104447, 'Okres Banská Bystrica', 4352, 'BC', 200, 'SK', 48.73333000, 19.15000000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q539934'),
(104448, 'Okres Banská Štiavnica', 4352, 'BC', 200, 'SK', 48.44961000, 18.90820000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q836179'),
(104449, 'Okres Bardejov', 4354, 'PV', 200, 'SK', 49.28333000, 21.28333000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q836179'),
(104450, 'Okres Bratislava I', 4356, 'BL', 200, 'SK', 48.14653000, 17.10584000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q737665'),
(104451, 'Okres Bratislava II', 4356, 'BL', 200, 'SK', 48.15280000, 17.17764000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q539732'),
(104452, 'Okres Bratislava III', 4356, 'BL', 200, 'SK', 48.18543000, 17.13790000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q750419'),
(104453, 'Okres Bratislava IV', 4356, 'BL', 200, 'SK', 48.22787000, 16.99722000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q750416'),
(104454, 'Okres Bratislava V', 4356, 'BL', 200, 'SK', 48.11122000, 17.09444000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q750439'),
(104455, 'Okres Brezno', 4352, 'BC', 200, 'SK', 48.80000000, 19.75000000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q784394'),
(104456, 'Okres Bytča', 4359, 'ZI', 200, 'SK', 49.22267000, 18.55844000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q668333'),
(104457, 'Okres Bánovce nad Bebravou', 4358, 'TC', 200, 'SK', 48.71908000, 18.25773000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q837865'),
(104458, 'Okres Detva', 4352, 'BC', 200, 'SK', 48.55985000, 19.42044000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q532137'),
(104459, 'Okres Dolný Kubín', 4359, 'ZI', 200, 'SK', 49.20000000, 19.30000000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q665066'),
(104460, 'Okres Dunajská Streda', 4355, 'TA', 200, 'SK', 47.99635000, 17.60937000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q756662'),
(104461, 'Okres Galanta', 4355, 'TA', 200, 'SK', 48.20000000, 17.71667000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q836182'),
(104462, 'Okres Gelnica', 4353, 'KI', 200, 'SK', 48.85207000, 20.93385000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q220968'),
(104463, 'Okres Hlohovec', 4355, 'TA', 200, 'SK', 48.41667000, 17.75000000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q550271'),
(104464, 'Okres Humenné', 4354, 'PV', 200, 'SK', 48.93333000, 21.91667000, '2019-10-05 23:18:04', '2020-05-01 17:23:14', 1, 'Q837897'),
(104465, 'Okres Ilava', 4358, 'TC', 200, 'SK', 48.99641000, 18.23374000, '2019-10-05 23:18:04', '2019-10-05 23:18:04', 1, 'Q373881'),
(104466, 'Okres Kežmarok', 4354, 'PV', 200, 'SK', 49.13593000, 20.42929000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q636864'),
(104467, 'Okres Komárno', 4357, 'NI', 200, 'SK', 47.76667000, 18.13333000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q586056'),
(104468, 'Okres Kosice-okolie', 4353, 'KI', 200, 'SK', 48.77897000, 21.41373000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q539979'),
(104469, 'Okres Krupina', 4352, 'BC', 200, 'SK', 48.35736000, 19.06334000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q756660'),
(104470, 'Okres Kysucké Nové Mesto', 4359, 'ZI', 200, 'SK', 49.30206000, 18.78603000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q762646'),
(104471, 'Okres Levice', 4357, 'NI', 200, 'SK', 48.21667000, 18.60000000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q777394'),
(104472, 'Okres Levoča', 4354, 'PV', 200, 'SK', 49.01986000, 20.57688000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q837877'),
(104473, 'Okres Liptovský Mikuláš', 4359, 'ZI', 200, 'SK', 49.08333000, 19.61667000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q272040'),
(104474, 'Okres Lučenec', 4352, 'BC', 200, 'SK', 48.33333000, 19.66667000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q836205'),
(104475, 'Okres Malacky', 4356, 'BL', 200, 'SK', 48.43458000, 17.02166000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q590660'),
(104476, 'Okres Martin', 4359, 'ZI', 200, 'SK', 49.06667000, 18.93333000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q756665'),
(104477, 'Okres Medzilaborce', 4354, 'PV', 200, 'SK', 49.27062000, 21.90200000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q837884'),
(104478, 'Okres Michalovce', 4353, 'KI', 200, 'SK', 48.75000000, 21.93333000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q379320'),
(104479, 'Okres Myjava', 4358, 'TC', 200, 'SK', 48.75000000, 17.58333000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q837460'),
(104480, 'Okres Namestovo', 4359, 'ZI', 200, 'SK', 49.40651000, 19.47670000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q388888'),
(104481, 'Okres Nitra', 4357, 'NI', 200, 'SK', 48.31667000, 18.08333000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q761574'),
(104482, 'Okres Nové Mesto nad Váhom', 4358, 'TC', 200, 'SK', 48.75466000, 17.83506000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q282702'),
(104483, 'Okres Nové Zámky', 4357, 'NI', 200, 'SK', 47.98333000, 18.16667000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q276362'),
(104484, 'Okres Partizánske', 4358, 'TC', 200, 'SK', 48.62613000, 18.37957000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q837871'),
(104485, 'Okres Pezinok', 4356, 'BL', 200, 'SK', 48.28785000, 17.26799000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q791591'),
(104486, 'Okres Piešťany', 4355, 'TA', 200, 'SK', 48.59064000, 17.82679000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q837868'),
(104487, 'Okres Poltár', 4352, 'BC', 200, 'SK', 48.42895000, 19.79488000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q256846'),
(104488, 'Okres Poprad', 4354, 'PV', 200, 'SK', 49.05000000, 20.30000000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q830982'),
(104489, 'Okres Považská Bystrica', 4358, 'TC', 200, 'SK', 49.11667000, 18.45000000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q837891'),
(104490, 'Okres Prešov', 4354, 'PV', 200, 'SK', 49.00000000, 21.25000000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q756658'),
(104491, 'Okres Prievidza', 4358, 'TC', 200, 'SK', 48.76667000, 18.63333000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q614140'),
(104492, 'Okres Púchov', 4358, 'TC', 200, 'SK', 49.12406000, 18.32477000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q768310'),
(104493, 'Okres Revúca', 4352, 'BC', 200, 'SK', 48.68300000, 20.11568000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q651878'),
(104494, 'Okres Rožňava', 4353, 'KI', 200, 'SK', 48.66667000, 20.53333000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q756075'),
(104495, 'Okres Ružomberok', 4359, 'ZI', 200, 'SK', 49.07494000, 19.30083000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q655156'),
(104496, 'Okres Sabinov', 4354, 'PV', 200, 'SK', 49.10115000, 21.09844000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q747926'),
(104497, 'Okres Senec', 4356, 'BL', 200, 'SK', 48.22187000, 17.40328000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q513989'),
(104498, 'Okres Senica', 4355, 'TA', 200, 'SK', 48.68333000, 17.36667000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q842557'),
(104499, 'Okres Skalica', 4355, 'TA', 200, 'SK', 48.75000000, 17.16667000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q837894'),
(104500, 'Okres Snina', 4354, 'PV', 200, 'SK', 48.98579000, 22.15059000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q837888'),
(104501, 'Okres Sobrance', 4353, 'KI', 200, 'SK', 48.74247000, 22.18197000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q830991'),
(104502, 'Okres Spišská Nová Ves', 4353, 'KI', 200, 'SK', 48.95000000, 20.56667000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q756077'),
(104503, 'Okres Stará Ĺubovňa', 4354, 'PV', 200, 'SK', 49.30000000, 20.70000000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q831001'),
(104504, 'Okres Stropkov', 4354, 'PV', 200, 'SK', 49.20249000, 21.65025000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q777963'),
(104505, 'Okres Svidník', 4354, 'PV', 200, 'SK', 49.30000000, 21.56667000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q833845'),
(104506, 'Okres Topoľčany', 4357, 'NI', 200, 'SK', 48.56667000, 18.18333000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q599361'),
(104507, 'Okres Trebišov', 4353, 'KI', 200, 'SK', 48.63333000, 21.71667000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q301318'),
(104508, 'Okres Trenčín', 4358, 'TC', 200, 'SK', 48.89520000, 18.04264000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q339239'),
(104509, 'Okres Trnava', 4355, 'TA', 200, 'SK', 48.36667000, 17.60000000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q775484'),
(104510, 'Okres Turčianske Teplice', 4359, 'ZI', 200, 'SK', 48.86283000, 18.85759000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q548293'),
(104511, 'Okres Tvrdošín', 4359, 'ZI', 200, 'SK', 49.33421000, 19.55461000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q756899'),
(104512, 'Okres Veľký Krtíš', 4352, 'BC', 200, 'SK', 48.20000000, 19.35000000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q546630'),
(104513, 'Okres Vranov nad Topľou', 4354, 'PV', 200, 'SK', 48.90000000, 21.68333000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q837874'),
(104514, 'Okres Zlaté Moravce', 4357, 'NI', 200, 'SK', 48.38294000, 18.39842000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q775875'),
(104515, 'Okres Zvolen', 4352, 'BC', 200, 'SK', 48.58333000, 19.13333000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q370161'),
(104516, 'Okres Čadca', 4359, 'ZI', 200, 'SK', 49.43333000, 18.78333000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q337478'),
(104517, 'Okres Šaľa', 4357, 'NI', 200, 'SK', 48.14770000, 17.87306000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q687009'),
(104518, 'Okres Žarnovica', 4352, 'BC', 200, 'SK', 48.48438000, 18.72076000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q633617'),
(104519, 'Okres Žiar nad Hronom', 4352, 'BC', 200, 'SK', 48.58333000, 18.86667000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q836194'),
(104520, 'Okres Žilina', 4359, 'ZI', 200, 'SK', 49.22303000, 18.74044000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q836201'),
(104521, 'Oravská Lesná', 4359, 'ZI', 200, 'SK', 49.36672000, 19.18093000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q836201'),
(104522, 'Oravský Podzámok', 4359, 'ZI', 200, 'SK', 49.25939000, 19.35690000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q1091685'),
(104523, 'Partizánske', 4358, 'TC', 200, 'SK', 48.62861000, 18.38455000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q754768'),
(104524, 'Pezinok', 4356, 'BL', 200, 'SK', 48.28986000, 17.26664000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q659424'),
(104525, 'Piešťany', 4355, 'TA', 200, 'SK', 48.59479000, 17.82591000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q321074'),
(104526, 'Podolínec', 4354, 'PV', 200, 'SK', 49.25869000, 20.53600000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q852324'),
(104527, 'Poltár', 4352, 'BC', 200, 'SK', 48.43094000, 19.79408000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q259694'),
(104528, 'Poprad', 4354, 'PV', 200, 'SK', 49.06144000, 20.29798000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q26393'),
(104529, 'Považská Bystrica', 4358, 'TC', 200, 'SK', 49.12153000, 18.42169000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q319787'),
(104530, 'Prešov', 4354, 'PV', 200, 'SK', 48.99839000, 21.23393000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q26119'),
(104531, 'Pribylina', 4359, 'ZI', 200, 'SK', 49.09950000, 19.79427000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q1091714'),
(104532, 'Prievidza', 4358, 'TC', 200, 'SK', 48.77446000, 18.62750000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q208750'),
(104533, 'Púchov', 4358, 'TC', 200, 'SK', 49.12494000, 18.32597000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q258481'),
(104534, 'Rajec', 4359, 'ZI', 200, 'SK', 49.08899000, 18.64007000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q909333'),
(104535, 'Revúca', 4352, 'BC', 200, 'SK', 48.68346000, 20.11734000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q847213'),
(104536, 'Rimavská Sobota', 4352, 'BC', 200, 'SK', 48.38284000, 20.02239000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q829812'),
(104537, 'Rožňava', 4353, 'KI', 200, 'SK', 48.66009000, 20.53758000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q267888'),
(104538, 'Ružomberok', 4359, 'ZI', 200, 'SK', 49.07480000, 19.30751000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q27541'),
(104539, 'Sabinov', 4354, 'PV', 200, 'SK', 49.10309000, 21.09880000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q837478'),
(104540, 'Senec', 4356, 'BL', 200, 'SK', 48.21951000, 17.40043000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q754790'),
(104541, 'Senica', 4355, 'TA', 200, 'SK', 48.67922000, 17.36697000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q750407'),
(104542, 'Sečovce', 4353, 'KI', 200, 'SK', 48.70074000, 21.66104000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q847336'),
(104543, 'Skalica', 4355, 'TA', 200, 'SK', 48.84490000, 17.22635000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q649076'),
(104544, 'Sládkovičovo', 4355, 'TA', 200, 'SK', 48.20137000, 17.63852000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q568249'),
(104545, 'Smolenice', 4355, 'TA', 200, 'SK', 48.50478000, 17.43067000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q1020253'),
(104546, 'Snina', 4354, 'PV', 200, 'SK', 48.98857000, 22.15099000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q754788'),
(104547, 'Sobrance', 4353, 'KI', 200, 'SK', 48.74455000, 22.18136000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q241125'),
(104548, 'Spišská Belá', 4354, 'PV', 200, 'SK', 49.18725000, 20.45948000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q605059'),
(104549, 'Spišská Nová Ves', 4353, 'KI', 200, 'SK', 48.94464000, 20.56153000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q462840'),
(104550, 'Spišské Podhradie', 4354, 'PV', 200, 'SK', 49.00088000, 20.75307000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q537778'),
(104551, 'Stará Turá', 4358, 'TC', 200, 'SK', 48.77721000, 17.69433000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q512771'),
(104552, 'Stará Ľubovňa', 4354, 'PV', 200, 'SK', 49.29859000, 20.68620000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q605521'),
(104553, 'Stropkov', 4354, 'PV', 200, 'SK', 49.20211000, 21.65216000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q750413'),
(104554, 'Strážske', 4353, 'KI', 200, 'SK', 48.87350000, 21.83668000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q846434'),
(104555, 'Stupava', 4356, 'BL', 200, 'SK', 48.27474000, 17.03173000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q782928'),
(104556, 'Svidník', 4354, 'PV', 200, 'SK', 49.30819000, 21.57030000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q782928'),
(104557, 'Svit', 4354, 'PV', 200, 'SK', 49.06014000, 20.20695000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q852318'),
(104558, 'Svodín', 4357, 'NI', 200, 'SK', 47.91054000, 18.49967000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q1070645'),
(104559, 'Svätý Anton', 4352, 'BC', 200, 'SK', 48.41923000, 18.94010000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q249613'),
(104560, 'Svätý Jur', 4356, 'BL', 200, 'SK', 48.25216000, 17.21539000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q247240'),
(104561, 'Terchová', 4359, 'ZI', 200, 'SK', 49.25895000, 19.02935000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q932854'),
(104562, 'Tisovec', 4352, 'BC', 200, 'SK', 48.67738000, 19.94364000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q370445'),
(104563, 'Tlmače', 4357, 'NI', 200, 'SK', 48.28926000, 18.53152000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q239637'),
(104564, 'Topoľčany', 4357, 'NI', 200, 'SK', 48.56361000, 18.16712000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q750423'),
(104565, 'Trebišov', 4353, 'KI', 200, 'SK', 48.62858000, 21.71954000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q256301'),
(104566, 'Trenčianske Teplice', 4358, 'TC', 200, 'SK', 48.91063000, 18.16691000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q532555'),
(104567, 'Trenčín', 4358, 'TC', 200, 'SK', 48.89452000, 18.04436000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q145002'),
(104568, 'Trnava', 4355, 'TA', 200, 'SK', 48.37741000, 17.58723000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q26175'),
(104569, 'Trstená', 4359, 'ZI', 200, 'SK', 49.36101000, 19.61249000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q847350'),
(104570, 'Turzovka', 4359, 'ZI', 200, 'SK', 49.40429000, 18.62258000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q182903'),
(104571, 'Turčianske Teplice', 4359, 'ZI', 200, 'SK', 48.86225000, 18.86048000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q135647'),
(104572, 'Tvrdošín', 4359, 'ZI', 200, 'SK', 49.33700000, 19.55600000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q509715'),
(104573, 'Veľký Krtíš', 4352, 'BC', 200, 'SK', 48.21059000, 19.35043000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q673741'),
(104574, 'Veľký Meder', 4355, 'TA', 200, 'SK', 47.85798000, 17.76884000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q740469'),
(104575, 'Vinné', 4353, 'KI', 200, 'SK', 48.80965000, 21.96757000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q1187936'),
(104576, 'Vinosady', 4356, 'BL', 200, 'SK', 48.31142000, 17.29042000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q1187936'),
(104577, 'Vranov nad Topľou', 4354, 'PV', 200, 'SK', 48.88836000, 21.68479000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q842005'),
(104578, 'Vrbov', 4354, 'PV', 200, 'SK', 49.08764000, 20.42530000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q545706'),
(104579, 'Vrbové', 4355, 'TA', 200, 'SK', 48.61973000, 17.72260000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q846444'),
(104580, 'Vráble', 4357, 'NI', 200, 'SK', 48.24371000, 18.30846000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q264708'),
(104581, 'Vrútky', 4359, 'ZI', 200, 'SK', 49.11328000, 18.91714000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q837456'),
(104582, 'Vysoké Tatry', 4354, 'PV', 200, 'SK', 49.13637000, 20.24386000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q612516'),
(104583, 'Vyšné Ružbachy', 4354, 'PV', 200, 'SK', 49.30387000, 20.56558000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q746119'),
(104584, 'Zlaté Moravce', 4357, 'NI', 200, 'SK', 48.38553000, 18.40063000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q207226'),
(104585, 'Zvolen', 4352, 'BC', 200, 'SK', 48.57442000, 19.15324000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q207226'),
(104586, 'Čachtice', 4358, 'TC', 200, 'SK', 48.71226000, 17.78704000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q337440'),
(104587, 'Čadca', 4359, 'ZI', 200, 'SK', 49.43503000, 18.78895000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q337440'),
(104588, 'Čierna nad Tisou', 4353, 'KI', 200, 'SK', 48.41704000, 22.08865000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q1192851'),
(104589, 'Čierny Balog', 4352, 'BC', 200, 'SK', 48.74722000, 19.65125000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q341490'),
(104590, 'Ľubica', 4354, 'PV', 200, 'SK', 49.11667000, 20.45000000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q341490'),
(104591, 'Šahy', 4357, 'NI', 200, 'SK', 48.07408000, 18.94946000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q390946'),
(104592, 'Šamorín', 4355, 'TA', 200, 'SK', 48.03015000, 17.30972000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q391068'),
(104593, 'Šaľa', 4357, 'NI', 200, 'SK', 48.15127000, 17.88062000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q391361'),
(104594, 'Štrba', 4354, 'PV', 200, 'SK', 49.05913000, 20.07975000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q373739'),
(104595, 'Štúrovo', 4357, 'NI', 200, 'SK', 47.79495000, 18.71750000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q60038'),
(104596, 'Šurany', 4357, 'NI', 200, 'SK', 48.08613000, 18.18447000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q392704'),
(104597, 'Žarnovica', 4352, 'BC', 200, 'SK', 48.48123000, 18.71565000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q393421'),
(104598, 'Ždiar', 4354, 'PV', 200, 'SK', 49.27100000, 20.26239000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q393435'),
(104599, 'Žehra', 4353, 'KI', 200, 'SK', 48.97960000, 20.79170000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q393479'),
(104600, 'Želiezovce', 4357, 'NI', 200, 'SK', 48.05075000, 18.65421000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q393609'),
(104601, 'Žiar nad Hronom', 4352, 'BC', 200, 'SK', 48.59184000, 18.84958000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q393925'),
(104602, 'Žilina', 4359, 'ZI', 200, 'SK', 49.22315000, 18.73941000, '2019-10-05 23:18:05', '2020-05-01 17:23:14', 1, 'Q25797'),
(104603, 'Alikalia', 911, 'N', 198, 'SL', 9.15356000, -11.38712000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q4726610'),
(104604, 'Baiima', 912, 'S', 198, 'SL', 8.10826000, -11.84772000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q31898780'),
(104605, 'Baoma', 912, 'S', 198, 'SL', 7.99344000, -11.71468000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q4857461'),
(104606, 'Barma', 914, 'E', 198, 'SL', 8.34959000, -11.33059000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q31911183'),
(104607, 'Bindi', 911, 'N', 198, 'SL', 9.91376000, -11.44669000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q31916070'),
(104608, 'Binkolo', 911, 'N', 198, 'SL', 8.95225000, -11.98029000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q9298703'),
(104609, 'Blama', 914, 'E', 198, 'SL', 7.87481000, -11.34548000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q31916963'),
(104610, 'Bo', 912, 'S', 198, 'SL', 7.96472000, -11.73833000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q844878'),
(104611, 'Bo District', 912, 'S', 198, 'SL', 7.98877000, -11.67340000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q887317'),
(104612, 'Boajibu', 914, 'E', 198, 'SL', 8.18763000, -11.34026000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q4931229'),
(104613, 'Bombali District', 911, 'N', 198, 'SL', 9.28444000, -12.16449000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q891767'),
(104614, 'Bomi', 912, 'S', 198, 'SL', 7.24611000, -11.52583000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q31924184'),
(104615, 'Bonthe', 912, 'S', 198, 'SL', 7.52639000, -12.50500000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q892886'),
(104616, 'Bonthe District', 912, 'S', 198, 'SL', 7.51622000, -12.33591000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q892889'),
(104617, 'Buedu', 914, 'E', 198, 'SL', 8.27960000, -10.37135000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q34783819'),
(104618, 'Bumbuna', 911, 'N', 198, 'SL', 9.04466000, -11.74576000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q4817928'),
(104619, 'Bumpe', 912, 'S', 198, 'SL', 7.89209000, -11.90541000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q2946676'),
(104620, 'Bunumbu', 914, 'E', 198, 'SL', 8.17421000, -10.86432000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q34784512'),
(104621, 'Daru', 914, 'E', 198, 'SL', 7.98976000, -10.84223000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q34784512'),
(104622, 'Foindu', 912, 'S', 198, 'SL', 7.40906000, -11.54328000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q34784512'),
(104623, 'Freetown', 913, 'W', 198, 'SL', 8.48714000, -13.23560000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q3780'),
(104624, 'Gandorhun', 912, 'S', 198, 'SL', 7.55502000, -11.69260000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q3012892'),
(104625, 'Gberia Fotombu', 911, 'N', 198, 'SL', 9.87852000, -11.16548000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q3012892'),
(104626, 'Gbewebu', 912, 'S', 198, 'SL', 7.55091000, -11.60750000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q3012892'),
(104627, 'Giehun', 914, 'E', 198, 'SL', 7.88405000, -11.04908000, '2019-10-05 23:18:05', '2019-10-05 23:18:05', 1, 'Q3012892'),
(104628, 'Gorahun', 914, 'E', 198, 'SL', 7.46439000, -11.23952000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3012892'),
(104629, 'Hangha', 914, 'E', 198, 'SL', 7.93974000, -11.14132000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3012892'),
(104630, 'Hastings', 913, 'W', 198, 'SL', 8.37994000, -13.13693000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3012892'),
(104631, 'Jojoima', 914, 'E', 198, 'SL', 7.87815000, -10.78976000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3012892'),
(104632, 'Kabala', 911, 'N', 198, 'SL', 9.58893000, -11.55256000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q930789'),
(104633, 'Kailahun', 914, 'E', 198, 'SL', 8.27890000, -10.57300000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1721432'),
(104634, 'Kailahun District', 914, 'E', 198, 'SL', 8.10768000, -10.75146000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1721443'),
(104635, 'Kamakwie', 911, 'N', 198, 'SL', 9.49689000, -12.24061000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q6355344'),
(104636, 'Kambia', 911, 'N', 198, 'SL', 9.12504000, -12.91816000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1722889'),
(104637, 'Kassiri', 911, 'N', 198, 'SL', 8.93814000, -13.11541000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1722889'),
(104638, 'Kayima', 914, 'E', 198, 'SL', 8.88790000, -11.15932000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1722889'),
(104639, 'Kenema', 914, 'E', 198, 'SL', 7.87687000, -11.19025000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q634698'),
(104640, 'Kenema District', 914, 'E', 198, 'SL', 7.95171000, -11.19004000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1738741'),
(104641, 'Kent', 913, 'W', 198, 'SL', 8.33333000, -13.06667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1738741'),
(104642, 'Koidu', 914, 'E', 198, 'SL', 8.64387000, -10.97140000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1002580'),
(104643, 'Koinadugu District', 911, 'N', 198, 'SL', 9.50991000, -11.34601000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1778860'),
(104644, 'Konakridee', 911, 'N', 198, 'SL', 8.69778000, -13.23917000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1778860'),
(104645, 'Kono District', 914, 'E', 198, 'SL', 8.70687000, -10.93368000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1781849'),
(104646, 'Koribundu', 912, 'S', 198, 'SL', 7.70952000, -11.69354000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1781849'),
(104647, 'Koyima', 914, 'E', 198, 'SL', 8.70552000, -11.02241000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1781849'),
(104648, 'Kukuna', 911, 'N', 198, 'SL', 9.39841000, -12.66476000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1781849'),
(104649, 'Largo', 912, 'S', 198, 'SL', 8.27903000, -12.15780000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1781849'),
(104650, 'Loma', 911, 'N', 198, 'SL', 9.75931000, -12.03383000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1781849'),
(104651, 'Lunsar', 911, 'N', 198, 'SL', 8.68439000, -12.53499000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q995392'),
(104652, 'Magburaka', 911, 'N', 198, 'SL', 8.72306000, -11.94880000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1883766'),
(104653, 'Makali', 911, 'N', 198, 'SL', 8.62964000, -11.66168000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1883766'),
(104654, 'Makeni', 911, 'N', 198, 'SL', 8.88605000, -12.04417000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104655, 'Mambolo', 911, 'N', 198, 'SL', 8.91860000, -13.03674000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104656, 'Mamboma', 912, 'S', 198, 'SL', 8.08742000, -11.68841000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104657, 'Mange', 911, 'N', 198, 'SL', 8.92262000, -12.85688000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104658, 'Manowa', 914, 'E', 198, 'SL', 8.17392000, -10.74834000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104659, 'Masaka', 911, 'N', 198, 'SL', 8.66492000, -11.80260000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104660, 'Masingbi', 911, 'N', 198, 'SL', 8.78197000, -11.95171000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104661, 'Masoyila', 911, 'N', 198, 'SL', 8.61119000, -13.19101000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104662, 'Mobai', 914, 'E', 198, 'SL', 7.99343000, -10.75355000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104663, 'Mogbwemo', 912, 'S', 198, 'SL', 7.76237000, -12.30864000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104664, 'Motema', 914, 'E', 198, 'SL', 8.61427000, -11.01252000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q42678'),
(104665, 'Moyamba', 912, 'S', 198, 'SL', 8.15898000, -12.43168000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q913292'),
(104666, 'Moyamba District', 912, 'S', 198, 'SL', 8.06290000, -12.44401000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q597538'),
(104667, 'Palima', 912, 'S', 198, 'SL', 8.11996000, -11.50702000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q4843232'),
(104668, 'Panguma', 914, 'E', 198, 'SL', 8.18507000, -11.13290000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q4843232'),
(104669, 'Pendembu', 914, 'E', 198, 'SL', 8.09807000, -10.69429000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2913209'),
(104670, 'Pepel', 911, 'N', 198, 'SL', 8.58611000, -13.05444000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3466506'),
(104671, 'Potoru', 912, 'S', 198, 'SL', 7.50596000, -11.47897000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3466506'),
(104672, 'Pujehun', 912, 'S', 198, 'SL', 7.35806000, -11.72083000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q623298'),
(104673, 'Pujehun District', 912, 'S', 198, 'SL', 7.31855000, -11.57920000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q927464'),
(104674, 'Rokupr', 911, 'N', 198, 'SL', 8.67121000, -12.38497000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q927464'),
(104675, 'Rotifunk', 912, 'S', 198, 'SL', 8.22591000, -12.67760000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q7370541'),
(104676, 'Sawkta', 911, 'N', 198, 'SL', 8.63230000, -13.20250000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q7370541'),
(104677, 'Segbwema', 914, 'E', 198, 'SL', 7.99471000, -10.95020000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2946720'),
(104678, 'Seidu', 911, 'N', 198, 'SL', 9.01801000, -10.59496000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2946720'),
(104679, 'Serabu', 912, 'S', 198, 'SL', 7.79311000, -12.05294000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2946720'),
(104680, 'Simbakoro', 914, 'E', 198, 'SL', 8.61243000, -11.00755000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2946720'),
(104681, 'Sumbuya', 912, 'S', 198, 'SL', 7.64789000, -11.96060000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q49368461'),
(104682, 'Tefeya', 914, 'E', 198, 'SL', 8.70395000, -11.21260000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q49368461'),
(104683, 'Tintafor', 911, 'N', 198, 'SL', 8.62667000, -13.21500000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q49368461'),
(104684, 'Tombodu', 914, 'E', 198, 'SL', 8.13526000, -10.61960000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q7818691'),
(104685, 'Tombu', 914, 'E', 198, 'SL', 8.53991000, -10.73132000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q7818691'),
(104686, 'Tongole', 912, 'S', 198, 'SL', 7.45092000, -11.90071000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q7818691'),
(104687, 'Tonkolili District', 911, 'N', 198, 'SL', 8.61885000, -11.84173000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1852020'),
(104688, 'Waterloo', 913, 'W', 198, 'SL', 8.33890000, -13.07091000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q623241'),
(104689, 'Wima', 914, 'E', 198, 'SL', 8.30052000, -11.20455000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q623241'),
(104690, 'Yengema', 914, 'E', 198, 'SL', 8.71441000, -11.17057000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1000327'),
(104691, 'Yonibana', 911, 'N', 198, 'SL', 8.44347000, -12.23929000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104692, 'Zimmi', 912, 'S', 198, 'SL', 7.31356000, -11.30818000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104693, 'Acquaviva', 59, '01', 192, 'SM', 43.94593000, 12.41850000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104694, 'Borgo Maggiore', 61, '06', 192, 'SM', 43.94193000, 12.44738000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104695, 'Domagnano', 64, '03', 192, 'SM', 43.94961000, 12.46828000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104696, 'Faetano', 62, '04', 192, 'SM', 43.92831000, 12.49798000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104697, 'Fiorentino', 66, '05', 192, 'SM', 43.91001000, 12.45738000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104698, 'Monte Giardino', 63, '08', 192, 'SM', 43.90878000, 12.48201000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104699, 'Poggio di Chiesanuova', 60, '02', 192, 'SM', 43.90451000, 12.42142000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q289747'),
(104700, 'San Marino', 58, '07', 192, 'SM', 43.93667000, 12.44639000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1848'),
(104701, 'Serravalle', 65, '09', 192, 'SM', 43.96897000, 12.48167000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1848'),
(104702, 'Adéane', 478, 'ZG', 195, 'SN', 12.63000000, -16.01694000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2825448'),
(104703, 'Bignona', 478, 'ZG', 195, 'SN', 12.81028000, -16.22639000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q286563'),
(104704, 'Dakar', 473, 'DK', 195, 'SN', 14.69370000, -17.44406000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3718'),
(104705, 'Dakar Department', 473, 'DK', 195, 'SN', 14.71403000, -17.45534000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2066372'),
(104706, 'Dara', 485, 'LG', 195, 'SN', 15.34844000, -15.47993000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3012013'),
(104707, 'Diawara', 476, 'MT', 195, 'SN', 15.02196000, -12.54374000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3026446'),
(104708, 'Diofior', 479, 'FK', 195, 'SN', 14.18333000, -16.66667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q685330'),
(104709, 'Département de Salémata', 481, 'KE', 195, 'SN', 12.59971000, -12.77619000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2075446'),
(104710, 'Fatick Department', 479, 'FK', 195, 'SN', 14.25909000, -16.49884000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2068250'),
(104711, 'Foundiougne', 479, 'FK', 195, 'SN', 14.13333000, -16.46667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3079946'),
(104712, 'Gandiaye', 483, 'KL', 195, 'SN', 14.23333000, -16.26667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3095100'),
(104713, 'Goléré', 477, 'SL', 195, 'SN', 16.25575000, -14.10165000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q3095100'),
(104714, 'Goudomp Department', 482, 'SE', 195, 'SN', 12.57778000, -15.87222000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2066279'),
(104715, 'Guinguinéo', 479, 'FK', 195, 'SN', 14.26667000, -15.95000000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q3100518'),
(104716, 'Guédiawaye Department', 473, 'DK', 195, 'SN', 14.77610000, -17.39560000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q957409'),
(104717, 'Guéoul', 485, 'LG', 195, 'SN', 15.48333000, -16.35000000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q3122437'),
(104718, 'Joal-Fadiout', 484, 'TH', 195, 'SN', 14.16667000, -16.83333000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1340331'),
(104719, 'Kaffrine', 475, 'KA', 195, 'SN', 14.10594000, -15.55080000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2623649'),
(104720, 'Kanel', 476, 'MT', 195, 'SN', 15.49160000, -13.17627000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3192625'),
(104721, 'Kaolack', 483, 'KL', 195, 'SN', 14.15197000, -16.07259000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q847677'),
(104722, 'Kayar', 484, 'TH', 195, 'SN', 14.91893000, -17.11978000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q847677'),
(104723, 'Khombole', 484, 'TH', 195, 'SN', 14.76667000, -16.70000000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2277705'),
(104724, 'Kolda', 474, 'KD', 195, 'SN', 12.89390000, -14.94125000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1011890'),
(104725, 'Kolda Department', 474, 'KD', 195, 'SN', 12.88300000, -14.95000000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2084154'),
(104726, 'Koungheul', 475, 'KA', 195, 'SN', 13.98333000, -14.80000000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3100527'),
(104727, 'Kédougou', 481, 'KE', 195, 'SN', 12.55561000, -12.18076000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q586498'),
(104728, 'Kédougou Department', 481, 'KE', 195, 'SN', 12.81716000, -12.17834000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2083190'),
(104729, 'Linguere Department', 485, 'LG', 195, 'SN', 15.35900000, -15.15800000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2084742'),
(104730, 'Louga', 485, 'LG', 195, 'SN', 15.61867000, -16.22436000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q280243'),
(104731, 'Marsassoum', 474, 'KD', 195, 'SN', 12.82750000, -15.98056000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3294983'),
(104732, 'Matam', 476, 'MT', 195, 'SN', 15.65587000, -13.25544000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1151582'),
(104733, 'Matam Department', 476, 'MT', 195, 'SN', 15.73191000, -13.63393000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2085162'),
(104734, 'Mbacké', 480, 'DB', 195, 'SN', 14.80828000, -15.86454000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2068182'),
(104735, 'Mbaké', 480, 'DB', 195, 'SN', 14.79032000, -15.90803000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q965360'),
(104736, 'Mbour', 484, 'TH', 195, 'SN', 14.40569000, -16.85559000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2086960'),
(104737, 'Mermoz Boabab', 473, 'DK', 195, 'SN', 14.70649000, -17.47581000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2086960'),
(104738, 'Mékhé', 484, 'TH', 195, 'SN', 15.10970000, -16.62180000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2981190'),
(104739, 'Ndibène Dahra', 485, 'LG', 195, 'SN', 15.33380000, -15.47660000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2981190'),
(104740, 'Ndioum', 477, 'SL', 195, 'SN', 16.51293000, -14.64706000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3337485'),
(104741, 'Ndofane', 483, 'KL', 195, 'SN', 13.91667000, -15.93333000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3337496'),
(104742, 'Nguékhokh', 484, 'TH', 195, 'SN', 14.51255000, -17.00447000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q3339368'),
(104743, 'Nioro du Rip', 483, 'KL', 195, 'SN', 13.75000000, -15.80000000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3090358'),
(104744, 'N’diareme limamoulaye', 473, 'DK', 195, 'SN', 14.78148000, -17.38410000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q3090358'),
(104745, 'Ouro Sogui', 476, 'MT', 195, 'SN', 15.60588000, -13.32230000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2472697'),
(104746, 'Oussouye', 478, 'ZG', 195, 'SN', 12.48500000, -16.54694000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q768591'),
(104747, 'Passi', 479, 'FK', 195, 'SN', 13.98333000, -16.26667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3368418'),
(104748, 'Pikine', 473, 'DK', 195, 'SN', 14.76457000, -17.39071000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1273644'),
(104749, 'Pikine Department', 473, 'DK', 195, 'SN', 14.76515000, -17.35198000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q255486'),
(104750, 'Polel Diaoubé', 477, 'SL', 195, 'SN', 15.26667000, -13.00000000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q255486'),
(104751, 'Pourham', 479, 'FK', 195, 'SN', 14.35000000, -16.41667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q255486'),
(104752, 'Pout', 484, 'TH', 195, 'SN', 14.77099000, -17.06107000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3400742'),
(104753, 'Ranérou', 476, 'MT', 195, 'SN', 15.30000000, -13.96667000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q3400742'),
(104754, 'Richard-Toll', 477, 'SL', 195, 'SN', 16.46250000, -15.70083000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1153061'),
(104755, 'Rosso', 477, 'SL', 195, 'SN', 16.42028000, -15.79834000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1153061'),
(104756, 'Rufisque Department', 473, 'DK', 195, 'SN', 14.74339000, -17.19841000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2067770'),
(104757, 'Saint-Louis', 477, 'SL', 195, 'SN', 16.01793000, -16.48962000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q273779'),
(104758, 'Saraya', 481, 'KE', 195, 'SN', 13.00150000, -11.79627000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q942594'),
(104759, 'Sokone', 479, 'FK', 195, 'SN', 13.88333000, -16.36667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3489051'),
(104760, 'Sédhiou', 482, 'SE', 195, 'SN', 12.70806000, -15.55694000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q671704'),
(104761, 'Sémé', 476, 'MT', 195, 'SN', 15.19422000, -12.94482000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q671704'),
(104762, 'Tambacounda', 486, 'TC', 195, 'SN', 13.77073000, -13.66734000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q977305'),
(104763, 'Tambacounda Department', 486, 'TC', 195, 'SN', 13.60500000, -13.64700000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2086236'),
(104764, 'Thiès', 484, 'TH', 195, 'SN', 14.73004000, -16.86974000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2087146'),
(104765, 'Thiès Nones', 484, 'TH', 195, 'SN', 14.78333000, -16.96667000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2087146'),
(104766, 'Tiadiaye', 484, 'TH', 195, 'SN', 14.41667000, -16.70000000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3523969'),
(104767, 'Tionk Essil', 478, 'ZG', 195, 'SN', 12.78556000, -16.52167000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3523969'),
(104768, 'Tivaouane', 484, 'TH', 195, 'SN', 15.08519000, -16.71058000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2087361'),
(104769, 'Tiébo', 480, 'DB', 195, 'SN', 14.63333000, -16.23333000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q2087361'),
(104770, 'Touba', 480, 'DB', 195, 'SN', 14.85000000, -15.88333000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q657072'),
(104771, 'Vélingara', 474, 'KD', 195, 'SN', 13.15000000, -14.11667000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q1014390'),
(104772, 'Waoundé', 476, 'MT', 195, 'SN', 15.26367000, -12.86821000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q1014390'),
(104773, 'Warang', 484, 'TH', 195, 'SN', 14.37349000, -16.94366000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1014390'),
(104774, 'Ziguinchor', 478, 'ZG', 195, 'SN', 12.56801000, -16.27326000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q202776'),
(104775, 'Afgooye', 921, 'SH', 203, 'SO', 2.13810000, 45.12120000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q383218'),
(104776, 'Baardheere', 928, 'GE', 203, 'SO', 2.34464000, 42.27644000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q383218'),
(104777, 'Baidoa', 926, 'BY', 203, 'SO', 3.11383000, 43.64980000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q506578'),
(104778, 'Bandarbeyla', 930, 'BR', 203, 'SO', 9.49420000, 50.81220000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2737490'),
(104779, 'Bargaal', 930, 'BR', 203, 'SO', 11.28636000, 51.07730000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q808131'),
(104780, 'Beledweyne', 915, 'HI', 203, 'SO', 4.73583000, 45.20361000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q815370'),
(104781, 'Bereeda', 930, 'BR', 203, 'SO', 11.87037000, 51.05795000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q15873036'),
(104782, 'Bosaso', 930, 'BR', 203, 'SO', 11.28421000, 49.18158000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q331556'),
(104783, 'Burao', 929, 'TO', 203, 'SO', 9.52213000, 45.53363000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q948448'),
(104784, 'Buulobarde', 915, 'HI', 203, 'SO', 3.85375000, 45.56744000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1018265'),
(104785, 'Buur Gaabo', 924, 'JH', 203, 'SO', -1.21917000, 41.83725000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1018265'),
(104786, 'Buurhakaba', 926, 'BY', 203, 'SO', 2.80257000, 44.07805000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1018270'),
(104787, 'Cadale', 923, 'SD', 203, 'SO', 2.76030000, 46.32220000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q348107'),
(104788, 'Caluula', 930, 'BR', 203, 'SO', 11.96611000, 50.75694000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q448076'),
(104789, 'Ceek', 929, 'TO', 203, 'SO', 8.99907000, 45.35824000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q448076'),
(104790, 'Ceelbuur', 918, 'GA', 203, 'SO', 4.68501000, 46.61760000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2737496'),
(104791, 'Ceeldheer', 918, 'GA', 203, 'SO', 3.84878000, 47.18064000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1325602'),
(104792, 'Ceerigaabo', 919, 'SA', 203, 'SO', 10.61616000, 47.36795000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1325602'),
(104793, 'Dhuusamarreeb', 918, 'GA', 203, 'SO', 5.53597000, 46.38666000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q634063'),
(104794, 'Dujuuma', 922, 'JD', 203, 'SO', 1.25311000, 42.57377000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q634063'),
(104795, 'Eyl', 920, 'NU', 203, 'SO', 7.98030000, 49.81640000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1019060'),
(104796, 'Gaalkacyo', 916, 'MU', 203, 'SO', 6.76972000, 47.43083000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q865586'),
(104797, 'Garbahaarrey', 928, 'GE', 203, 'SO', 3.32892000, 42.22091000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1017167'),
(104798, 'Garoowe', 920, 'NU', 203, 'SO', 8.40207000, 48.48284000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q822679'),
(104799, 'Hobyo', 916, 'MU', 203, 'SO', 5.35050000, 48.52680000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1016908'),
(104800, 'Iskushuban', 930, 'BR', 203, 'SO', 10.28370000, 50.23000000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1017200'),
(104801, 'Jalalaqsi', 915, 'HI', 203, 'SO', 3.37660000, 45.59960000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q694851'),
(104802, 'Jamaame', 924, 'JH', 203, 'SO', 0.06968000, 42.74497000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1017154'),
(104803, 'Jawhar', 923, 'SD', 203, 'SO', 2.78087000, 45.50048000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q646574'),
(104804, 'Jilib', 922, 'JD', 203, 'SO', 0.48829000, 42.78535000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1509787'),
(104805, 'Kismayo', 924, 'JH', 203, 'SO', -0.35817000, 42.54536000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q270787'),
(104806, 'Las Khorey', 919, 'SA', 203, 'SO', 11.15950000, 48.19670000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1017225'),
(104807, 'Luuq', 928, 'GE', 203, 'SO', 3.80315000, 42.54417000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1878274'),
(104808, 'Mahaddayweyne', 923, 'SD', 203, 'SO', 2.97260000, 45.53470000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1878274'),
(104809, 'Marka', 921, 'SH', 203, 'SO', 1.71594000, 44.77166000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q657428'),
(104810, 'Mogadishu', 927, 'BN', 203, 'SO', 2.03711000, 45.34375000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2449'),
(104811, 'Oodweyne', 929, 'TO', 203, 'SO', 9.40920000, 45.06397000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q7095260'),
(104812, 'Qandala', 930, 'BR', 203, 'SO', 11.47197000, 49.87282000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1668870'),
(104813, 'Qoryooley', 921, 'SH', 203, 'SO', 1.78784000, 44.52999000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2054709'),
(104814, 'Saacow', 922, 'JD', 203, 'SO', 1.62794000, 42.44067000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2054709'),
(104815, 'Tayeeglow', 917, 'BK', 203, 'SO', 4.01897000, 44.51111000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3991272'),
(104816, 'Waajid', 917, 'BK', 203, 'SO', 3.80958000, 43.24627000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2064657'),
(104817, 'Wanlaweyn', 921, 'SH', 203, 'SO', 2.61850000, 44.89380000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1444410'),
(104818, 'Xarardheere', 916, 'MU', 203, 'SO', 4.65440000, 47.85750000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q598676'),
(104819, 'Xuddur', 917, 'BK', 203, 'SO', 4.12129000, 43.88945000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1017160'),
(104820, 'Yeed', 917, 'BK', 203, 'SO', 4.55000000, 43.03333000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1017160'),
(104821, 'Albina', 2845, 'MA', 210, 'SR', 5.49788000, -54.05522000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q580016'),
(104822, 'Brokopondo', 2846, 'BR', 210, 'SR', 5.05594000, -54.98043000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q926060'),
(104823, 'Brownsweg', 2846, 'BR', 210, 'SR', 5.00435000, -55.15333000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q926060'),
(104824, 'Groningen', 2848, 'SA', 210, 'SR', 5.80000000, -55.46667000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q508935'),
(104825, 'Lelydorp', 2844, 'WA', 210, 'SR', 5.70000000, -55.23333000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q935545'),
(104826, 'Mariënburg', 2839, 'CM', 210, 'SR', 5.87722000, -55.04322000, '2019-10-05 23:18:06', '2020-05-01 17:23:17', 1, 'Q908006'),
(104827, 'Moengo', 2845, 'MA', 210, 'SR', 5.61411000, -54.40121000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q24226'),
(104828, 'Nieuw Amsterdam', 2839, 'CM', 210, 'SR', 5.88573000, -55.08871000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q24226'),
(104829, 'Nieuw Nickerie', 2840, 'NI', 210, 'SR', 5.92606000, -56.97297000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q738313'),
(104830, 'Onverwacht', 2841, 'PR', 210, 'SR', 5.58983000, -55.19462000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1861566'),
(104831, 'Paramaribo', 2843, 'PM', 210, 'SR', 5.86638000, -55.16682000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3001'),
(104832, 'Totness', 2842, 'CR', 210, 'SR', 5.87618000, -56.32572000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3001'),
(104833, 'Wageningen', 2840, 'NI', 210, 'SR', 5.76010000, -56.66523000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q24224'),
(104834, 'Yirol', 2090, 'LK', 206, 'SS', 6.55250000, 30.49806000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q3132425'),
(104835, 'Cantagalo District', 271, 'S', 193, 'ST', 0.21667000, 6.70000000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1033696'),
(104836, 'Caué District', 271, 'S', 193, 'ST', 0.13415000, 6.63825000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q1033696'),
(104837, 'Santo António', 270, 'P', 193, 'ST', 1.63943000, 7.41951000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q973656'),
(104838, 'São Tomé', 271, 'S', 193, 'ST', 0.33654000, 6.72732000, '2019-10-05 23:18:06', '2020-05-01 17:23:14', 1, 'Q3932'),
(104839, 'Trindade', 271, 'S', 193, 'ST', 0.29667000, 6.68139000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q1635802'),
(104840, 'Acajutla', 4140, 'SO', 66, 'SV', 13.59278000, -89.82750000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q937292'),
(104841, 'Aguilares', 4133, 'SS', 66, 'SV', 13.95722000, -89.18972000, '2019-10-05 23:18:06', '2019-10-05 23:18:06', 1, 'Q2289978'),
(104842, 'Ahuachapán', 4139, 'AH', 66, 'SV', 13.92139000, -89.84500000, '2019-10-05 23:18:06', '2020-05-01 17:22:42', 1, 'Q186935'),
(104843, 'Anamorós', 4138, 'UN', 66, 'SV', 13.74056000, -87.87361000, '2019-10-05 23:18:06', '2020-05-01 17:22:42', 1, 'Q3615033');

