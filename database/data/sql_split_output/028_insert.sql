INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(13674, 'Paripueira', 2007, 'AL', 31, 'BR', -9.42940000, -35.58979000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1761881'),
(13675, 'Pariquera Açu', 2021, 'SP', 31, 'BR', -24.71500000, -47.88111000, '2019-10-05 22:35:21', '2020-05-01 17:22:38', 1, 'Q22037342'),
(13676, 'Pariquera-Açu', 2021, 'SP', 31, 'BR', -24.66388000, -47.84553000, '2019-10-05 22:35:21', '2020-05-01 17:22:38', 1, 'Q1760286'),
(13677, 'Parisi', 2021, '<PERSON>', 31, 'BR', -20.26554000, -50.03904000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1760286'),
(13678, '<PERSON>rnaguá', 2008, 'P<PERSON>', 31, '<PERSON>', -10.12155000, -44.56012000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q1760286'),
(13679, 'Parnamirim', 2006, 'PE', 31, 'BR', -8.09056000, -39.57833000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2101450'),
(13680, 'Parnamirim', 2019, 'RN', 31, 'BR', -5.92446000, -35.20566000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q986261'),
(13681, 'Parnarama', 2015, 'MA', 31, 'BR', -5.52914000, -43.60353000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2013043'),
(13682, 'Parnaíba', 2008, 'PI', 31, 'BR', -2.92278000, -41.73536000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q1643864'),
(13683, 'Parobé', 2001, 'RS', 31, 'BR', -29.62861000, -50.83472000, '2019-10-05 22:35:21', '2020-05-01 17:22:38', 1, 'Q983702'),
(13684, 'Pará de Minas', 1998, 'MG', 31, 'BR', -19.82122000, -44.61286000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q1749821'),
(13685, 'Passa Quatro', 1998, 'MG', 31, 'BR', -22.40871000, -44.95994000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1638378'),
(13686, 'Passa Sete', 2001, 'RS', 31, 'BR', -29.43060000, -52.86514000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1638378'),
(13687, 'Passa Tempo', 1998, 'MG', 31, 'BR', -20.64350000, -44.49857000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1638378'),
(13688, 'Passa e Fica', 2019, 'RN', 31, 'BR', -6.45302000, -35.62625000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q772851'),
(13689, 'Passa-Vinte', 1998, 'MG', 31, 'BR', -22.17877000, -44.26002000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1791701'),
(13690, 'Passabém', 1998, 'MG', 31, 'BR', -19.35651000, -43.18735000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q1791701'),
(13691, 'Passagem', 2019, 'RN', 31, 'BR', -6.27254000, -35.39534000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1791701'),
(13692, 'Passagem', 2005, 'PB', 31, 'BR', -7.11889000, -37.03671000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2104849'),
(13693, 'Passagem Franca', 2015, 'MA', 31, 'BR', -6.10747000, -43.75355000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2043365'),
(13694, 'Passagem Franca do Piauí', 2008, 'PI', 31, 'BR', -5.82734000, -42.40584000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q2043365'),
(13695, 'Passira', 2006, 'PE', 31, 'BR', -7.99500000, -35.58056000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2011285'),
(13696, 'Passo Fundo', 2001, 'RS', 31, 'BR', -28.26278000, -52.40667000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q525647'),
(13697, 'Passo de Camaragibe', 2007, 'AL', 31, 'BR', -9.29865000, -35.42788000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q525647'),
(13698, 'Passo de Torres', 2014, 'SC', 31, 'BR', -29.29393000, -49.74094000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q525647'),
(13699, 'Passo do Sobrado', 2001, 'RS', 31, 'BR', -29.73133000, -52.23175000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q525647'),
(13700, 'Passos', 1998, 'MG', 31, 'BR', -20.71889000, -46.60972000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q985579'),
(13701, 'Passos Maia', 2014, 'SC', 31, 'BR', -26.70774000, -51.94716000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q985579'),
(13702, 'Pastos Bons', 2015, 'MA', 31, 'BR', -6.76600000, -44.24234000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2066090'),
(13703, 'Patis', 1998, 'MG', 31, 'BR', -16.05798000, -44.11137000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2066090'),
(13704, 'Pato Bragado', 2022, 'PR', 31, 'BR', -24.64323000, -54.22745000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2066090'),
(13705, 'Pato Branco', 2022, 'PR', 31, 'BR', -26.22861000, -52.67056000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1772650'),
(13706, 'Patos', 2005, 'PB', 31, 'BR', -7.02444000, -37.28000000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1966384'),
(13707, 'Patos de Minas', 1998, 'MG', 31, 'BR', -18.57889000, -46.51806000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q984571'),
(13708, 'Patos do Piauí', 2008, 'PI', 31, 'BR', -7.60918000, -41.29464000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q984571'),
(13709, 'Patrocínio', 1998, 'MG', 31, 'BR', -19.01305000, -47.06790000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q960694'),
(13710, 'Patrocínio Paulista', 2021, 'SP', 31, 'BR', -20.68707000, -47.28437000, '2019-10-05 22:35:21', '2020-05-01 17:22:38', 1, 'Q1759984'),
(13711, 'Patrocínio do Muriaé', 1998, 'MG', 31, 'BR', -21.16714000, -42.25572000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q1759984'),
(13712, 'Patu', 2019, 'RN', 31, 'BR', -6.12305000, -37.62573000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q2078782'),
(13713, 'Paty do Alferes', 1997, 'RJ', 31, 'BR', -22.42861000, -43.41861000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q955927'),
(13714, 'Pau Brasil', 2002, 'BA', 31, 'BR', -15.46417000, -39.65111000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1772557'),
(13715, 'Pau D\'arco do Piauí', 2008, 'PI', 31, 'BR', -5.24511000, -42.45473000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q1933647'),
(13716, 'Pau d\'Arco', 2020, 'TO', 31, 'BR', -7.55877000, -48.93697000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1933647'),
(13717, 'Pau d\'Arco', 2009, 'PA', 31, 'BR', -7.73913000, -50.14638000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1933647'),
(13718, 'Pau dos Ferros', 2019, 'RN', 31, 'BR', -6.11628000, -38.19653000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1933647'),
(13719, 'Paudalho', 2006, 'PE', 31, 'BR', -7.94302000, -35.13559000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1933647'),
(13720, 'Pauini', 2004, 'AM', 31, 'BR', -7.71361000, -66.97639000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1793494'),
(13721, 'Paula Cândido', 1998, 'MG', 31, 'BR', -20.86034000, -42.98154000, '2019-10-05 22:35:21', '2020-05-01 17:22:37', 1, 'Q1793494'),
(13722, 'Paula Freitas', 2022, 'PR', 31, 'BR', -26.17808000, -50.84807000, '2019-10-05 22:35:21', '2019-10-05 22:35:21', 1, 'Q1793494'),
(13723, 'Paulicéia', 2021, 'SP', 31, 'BR', -21.17172000, -51.75418000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q1793494'),
(13724, 'Paulino Neves', 2015, 'MA', 31, 'BR', -2.88588000, -42.58808000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1793494'),
(13725, 'Paulista', 2006, 'PE', 31, 'BR', -7.94083000, -34.87306000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q949833'),
(13726, 'Paulista', 2005, 'PB', 31, 'BR', -6.59504000, -37.60966000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2081885'),
(13727, 'Paulista Flórida', 2021, 'SP', 31, 'BR', -21.60000000, -51.20000000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q22045212'),
(13728, 'Paulistana', 2008, 'PI', 31, 'BR', -8.22526000, -41.22746000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q22045212'),
(13729, 'Paulistas', 1998, 'MG', 31, 'BR', -18.43393000, -42.86603000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1789599'),
(13730, 'Paulistânia', 2021, 'SP', 31, 'BR', -22.57816000, -49.29587000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q1789599'),
(13731, 'Paulo Afonso', 2002, 'BA', 31, 'BR', -9.55234000, -38.16905000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q956008'),
(13732, 'Paulo Bento', 2001, 'RS', 31, 'BR', -27.73673000, -52.39667000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2007799'),
(13733, 'Paulo Frontin', 2022, 'PR', 31, 'BR', -26.04279000, -50.74770000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2007799'),
(13734, 'Paulo Jacinto', 2007, 'AL', 31, 'BR', -9.36843000, -36.39294000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2007799'),
(13735, 'Paulo Lopes', 2014, 'SC', 31, 'BR', -27.94684000, -48.74058000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2007799'),
(13736, 'Paulo Ramos', 2015, 'MA', 31, 'BR', -4.44876000, -45.23758000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2064653'),
(13737, 'Paulo de Faria', 2021, 'SP', 31, 'BR', -20.09639000, -49.48849000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2064653'),
(13738, 'Paulínia', 2021, 'SP', 31, 'BR', -22.76111000, -47.15417000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q40706'),
(13739, 'Paverama', 2001, 'RS', 31, 'BR', -29.59556000, -51.72344000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q40706'),
(13740, 'Pavussu', 2008, 'PI', 31, 'BR', -7.88646000, -43.19694000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q40706'),
(13741, 'Pavão', 1998, 'MG', 31, 'BR', -17.46780000, -41.07743000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q40706'),
(13742, 'Paço do Lumiar', 2015, 'MA', 31, 'BR', -2.48019000, -44.11054000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q40706'),
(13743, 'Peabiru', 2022, 'PR', 31, 'BR', -23.91278000, -52.34306000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1803641'),
(13744, 'Pederneiras', 2021, 'SP', 31, 'BR', -22.35167000, -48.77500000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1795537'),
(13745, 'Pedra', 2006, 'PE', 31, 'BR', -8.49694000, -36.94083000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2013605'),
(13746, 'Pedra Azul', 1998, 'MG', 31, 'BR', -15.96404000, -41.22145000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1650924'),
(13747, 'Pedra Bela', 2021, 'SP', 31, 'BR', -22.76397000, -46.43708000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1650924'),
(13748, 'Pedra Bonita', 1998, 'MG', 31, 'BR', -20.46665000, -42.37756000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q953379'),
(13749, 'Pedra Branca', 2016, 'CE', 31, 'BR', -5.49336000, -39.85259000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2028317'),
(13750, 'Pedra Branca', 2005, 'PB', 31, 'BR', -7.46068000, -38.07996000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13751, 'Pedra Branca do Amapari', 1999, 'AP', 31, 'BR', 1.14843000, -52.40118000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13752, 'Pedra Dourada', 1998, 'MG', 31, 'BR', -20.82636000, -42.15154000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13753, 'Pedra Grande', 2019, 'RN', 31, 'BR', -5.09384000, -35.85092000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13754, 'Pedra Lavrada', 2005, 'PB', 31, 'BR', -6.78024000, -36.44200000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13755, 'Pedra Mole', 2003, 'SE', 31, 'BR', -10.66490000, -37.68505000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13756, 'Pedra Preta', 2019, 'RN', 31, 'BR', -5.52791000, -36.07481000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13757, 'Pedra Preta', 2011, 'MT', 31, 'BR', -16.76381000, -54.16343000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13758, 'Pedra do Anta', 1998, 'MG', 31, 'BR', -20.59796000, -42.71247000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2342000'),
(13759, 'Pedra do Indaiá', 1998, 'MG', 31, 'BR', -20.28200000, -45.21859000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q2342000'),
(13760, 'Pedralva', 1998, 'MG', 31, 'BR', -22.25067000, -45.45324000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q612995'),
(13761, 'Pedranópolis', 2021, 'SP', 31, 'BR', -20.19986000, -50.08987000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q612995'),
(13762, 'Pedras Altas', 2001, 'RS', 31, 'BR', -31.83276000, -53.63272000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1785825'),
(13763, 'Pedras Grandes', 2014, 'SC', 31, 'BR', -28.48270000, -49.22027000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1785825'),
(13764, 'Pedras de Fogo', 2005, 'PB', 31, 'BR', -7.40194000, -35.11639000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2009248'),
(13765, 'Pedras de Maria da Cruz', 1998, 'MG', 31, 'BR', -15.63347000, -44.30841000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2009248'),
(13766, 'Pedregulho', 2021, 'SP', 31, 'BR', -20.25694000, -47.47667000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1760087'),
(13767, 'Pedreira', 2021, 'SP', 31, 'BR', -22.74194000, -46.90139000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q786140'),
(13768, 'Pedreiras', 2015, 'MA', 31, 'BR', -4.58272000, -44.59924000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1957216'),
(13769, 'Pedrinhas', 2003, 'SE', 31, 'BR', -11.21666000, -37.65664000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1957216'),
(13770, 'Pedrinhas Paulista', 2021, 'SP', 31, 'BR', -22.80731000, -50.82055000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1957216'),
(13771, 'Pedrinópolis', 1998, 'MG', 31, 'BR', -19.19064000, -47.50212000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q1957216'),
(13772, 'Pedro Afonso', 2020, 'TO', 31, 'BR', -9.20623000, -47.97805000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1957216'),
(13773, 'Pedro Alexandre', 2002, 'BA', 31, 'BR', -9.97693000, -37.91489000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1957216'),
(13774, 'Pedro Avelino', 2019, 'RN', 31, 'BR', -5.44022000, -36.34590000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1957216'),
(13775, 'Pedro Canário', 2018, 'ES', 31, 'BR', -18.20359000, -40.03155000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q1957216'),
(13776, 'Pedro Gomes', 2010, 'MS', 31, 'BR', -17.83882000, -54.12943000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1957216'),
(13777, 'Pedro II', 2008, 'PI', 31, 'BR', -4.49028000, -41.39962000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1934329'),
(13778, 'Pedro Laurentino', 2008, 'PI', 31, 'BR', -8.11766000, -42.23542000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1934329'),
(13779, 'Pedro Leopoldo', 1998, 'MG', 31, 'BR', -19.64468000, -44.03938000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1638732'),
(13780, 'Pedro Osório', 2001, 'RS', 31, 'BR', -31.95760000, -52.89419000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q1638732'),
(13781, 'Pedro Régis', 2005, 'PB', 31, 'BR', -6.67047000, -35.31031000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q2064424'),
(13782, 'Pedro Teixeira', 1998, 'MG', 31, 'BR', -21.72800000, -43.72046000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1608543'),
(13783, 'Pedro Velho', 2019, 'RN', 31, 'BR', -6.43917000, -35.22139000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1786891'),
(13784, 'Pedro de Toledo', 2021, 'SP', 31, 'BR', -24.17020000, -47.17239000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1786891'),
(13785, 'Pedro do Rosário', 2015, 'MA', 31, 'BR', -2.97390000, -45.45250000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q1786891'),
(13786, 'Pedrão', 2002, 'BA', 31, 'BR', -12.13311000, -38.60621000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q1786891'),
(13787, 'Peixe', 2020, 'TO', 31, 'BR', -11.99612000, -48.53284000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1786891'),
(13788, 'Peixe-Boi', 2009, 'PA', 31, 'BR', -1.10168000, -47.27259000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2009222'),
(13789, 'Peixoto de Azevedo', 2011, 'MT', 31, 'BR', -10.14722000, -53.58639000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q933251'),
(13790, 'Pejuçara', 2001, 'RS', 31, 'BR', -28.45442000, -53.61044000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q933251'),
(13791, 'Pelotas', 2001, 'RS', 31, 'BR', -31.76997000, -52.34101000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q109188'),
(13792, 'Penaforte', 2016, 'CE', 31, 'BR', -7.80046000, -39.04704000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q109188'),
(13793, 'Penalva', 2015, 'MA', 31, 'BR', -3.29417000, -45.17361000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2066111'),
(13794, 'Pendências', 2019, 'RN', 31, 'BR', -5.26000000, -36.72222000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q1786911'),
(13795, 'Penedo', 2007, 'AL', 31, 'BR', -10.24448000, -36.46992000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1772658'),
(13796, 'Penha', 2014, 'SC', 31, 'BR', -26.80226000, -48.62710000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1784611'),
(13797, 'Pentecoste', 2016, 'CE', 31, 'BR', -3.85116000, -39.19850000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1815210'),
(13798, 'Penápolis', 2021, 'SP', 31, 'BR', -21.41972000, -50.07750000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q986508'),
(13799, 'Pequeri', 1998, 'MG', 31, 'BR', -21.81623000, -43.14007000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q986508'),
(13800, 'Pequi', 1998, 'MG', 31, 'BR', -19.61044000, -44.63355000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1813066'),
(13801, 'Pequizeiro', 2020, 'TO', 31, 'BR', -8.40977000, -48.94148000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1813066'),
(13802, 'Perdigão', 1998, 'MG', 31, 'BR', -19.92135000, -45.08070000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q1813066'),
(13803, 'Perdizes', 1998, 'MG', 31, 'BR', -19.36522000, -47.15986000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1749830'),
(13804, 'Perdões', 1998, 'MG', 31, 'BR', -21.07090000, -45.06710000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q1789707'),
(13805, 'Pereira Barreto', 2021, 'SP', 31, 'BR', -20.63833000, -51.10917000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q928154'),
(13806, 'Pereiras', 2021, 'SP', 31, 'BR', -23.12150000, -47.97614000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q928154'),
(13807, 'Pereiro', 2016, 'CE', 31, 'BR', -6.04085000, -38.48892000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2028227'),
(13808, 'Peri Mirim', 2015, 'MA', 31, 'BR', -2.54231000, -44.93384000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2028227'),
(13809, 'Periquito', 1998, 'MG', 31, 'BR', -19.04695000, -42.21606000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2028227'),
(13810, 'Peritiba', 2014, 'SC', 31, 'BR', -27.34845000, -51.88687000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2028227'),
(13811, 'Peritoró', 2015, 'MA', 31, 'BR', -4.44047000, -44.28178000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q2028227'),
(13812, 'Perobal', 2022, 'PR', 31, 'BR', -23.97015000, -53.32376000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q2028227'),
(13813, 'Perolândia', 2000, 'GO', 31, 'BR', -17.52861000, -52.06417000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q2028227'),
(13814, 'Peruíbe', 2021, 'SP', 31, 'BR', -24.32000000, -46.99833000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q912136'),
(13815, 'Pescador', 1998, 'MG', 31, 'BR', -18.32119000, -41.55163000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q629223'),
(13816, 'Pesqueira', 2006, 'PE', 31, 'BR', -8.35778000, -36.69639000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1008230'),
(13817, 'Petrolina', 2006, 'PE', 31, 'BR', -9.39861000, -40.50083000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q842143'),
(13818, 'Petrolina de Goiás', 2000, 'GO', 31, 'BR', -16.09500000, -49.33806000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q991127'),
(13819, 'Petrolândia', 2006, 'PE', 31, 'BR', -8.84265000, -38.30348000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q2106455'),
(13820, 'Petrolândia', 2014, 'SC', 31, 'BR', -27.53735000, -49.67274000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q747168'),
(13821, 'Petrópolis', 1997, 'RJ', 31, 'BR', -22.38219000, -43.15909000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q189043'),
(13822, 'Peçanha', 1998, 'MG', 31, 'BR', -18.54540000, -42.49385000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q584598'),
(13823, 'Piacatu', 2021, 'SP', 31, 'BR', -21.57301000, -50.65835000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q584598'),
(13824, 'Piancó', 2005, 'PB', 31, 'BR', -7.19806000, -37.92917000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q2009920'),
(13825, 'Piatã', 2002, 'BA', 31, 'BR', -13.12099000, -41.89093000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q2009920'),
(13826, 'Piau', 1998, 'MG', 31, 'BR', -21.50206000, -43.31473000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1790460'),
(13827, 'Piaçabuçu', 2007, 'AL', 31, 'BR', -10.40556000, -36.43444000, '2019-10-05 22:35:22', '2020-05-01 17:22:36', 1, 'Q1772595'),
(13828, 'Picada Café', 2001, 'RS', 31, 'BR', -29.44757000, -51.11522000, '2019-10-05 22:35:22', '2020-05-01 17:22:38', 1, 'Q958545'),
(13829, 'Picos', 2008, 'PI', 31, 'BR', -7.10697000, -41.51271000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1810825'),
(13830, 'Picuí', 2005, 'PB', 31, 'BR', -6.51056000, -36.34694000, '2019-10-05 22:35:22', '2020-05-01 17:22:37', 1, 'Q2008685'),
(13831, 'Piedade', 2021, 'SP', 31, 'BR', -23.71194000, -47.42778000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1800748'),
(13832, 'Piedade de Caratinga', 1998, 'MG', 31, 'BR', -19.75899000, -42.03936000, '2019-10-05 22:35:22', '2019-10-05 22:35:22', 1, 'Q1800748'),
(13833, 'Piedade de Ponte Nova', 1998, 'MG', 31, 'BR', -20.24410000, -42.71587000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1800748'),
(13834, 'Piedade do Rio Grande', 1998, 'MG', 31, 'BR', -21.50287000, -44.16154000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1800748'),
(13835, 'Piedade dos Gerais', 1998, 'MG', 31, 'BR', -20.48078000, -44.25310000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1800748'),
(13836, 'Pilar', 2005, 'PB', 31, 'BR', -7.25739000, -35.26971000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q731296'),
(13837, 'Pilar', 2007, 'AL', 31, 'BR', -9.61752000, -36.06323000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2013557'),
(13838, 'Pilar de Goiás', 2000, 'GO', 31, 'BR', -14.52999000, -49.51094000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q2013557'),
(13839, 'Pilar do Sul', 2021, 'SP', 31, 'BR', -23.81306000, -47.71639000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1800635'),
(13840, 'Pilão Arcado', 2002, 'BA', 31, 'BR', -10.00201000, -43.39371000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q1800635'),
(13841, 'Pilões', 2005, 'PB', 31, 'BR', -6.89446000, -35.59432000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q2067408'),
(13842, 'Pilões', 2019, 'RN', 31, 'BR', -6.28833000, -38.01911000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q2008056'),
(13843, 'Pilõezinhos', 2005, 'PB', 31, 'BR', -6.85269000, -35.54455000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q2011307'),
(13844, 'Pimenta', 1998, 'MG', 31, 'BR', -20.48505000, -45.83344000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1635025'),
(13845, 'Pimenta Bueno', 2013, 'RO', 31, 'BR', -11.67250000, -61.19361000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1803175'),
(13846, 'Pimenteiras', 2008, 'PI', 31, 'BR', -6.24528000, -41.41917000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2077685'),
(13847, 'Pimenteiras do Oeste', 2013, 'RO', 31, 'BR', -13.01273000, -61.73762000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2077685'),
(13848, 'Pindamonhangaba', 2021, 'SP', 31, 'BR', -22.92389000, -45.46167000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q974642'),
(13849, 'Pindaré Mirim', 2015, 'MA', 31, 'BR', -3.60833000, -45.34333000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q22015476'),
(13850, 'Pindaré-Mirim', 2015, 'MA', 31, 'BR', -3.62515000, -45.39384000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q2065093'),
(13851, 'Pindaí', 2002, 'BA', 31, 'BR', -14.52319000, -42.67822000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q2065093'),
(13852, 'Pindoba', 2007, 'AL', 31, 'BR', -9.47492000, -36.30343000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2065093'),
(13853, 'Pindobaçu', 2002, 'BA', 31, 'BR', -10.74167000, -40.36083000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q1794448'),
(13854, 'Pindorama', 2021, 'SP', 31, 'BR', -21.18583000, -48.90722000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1648152'),
(13855, 'Pindorama do Tocantins', 2020, 'TO', 31, 'BR', -11.13233000, -47.56170000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1648152'),
(13856, 'Pindoretama', 2016, 'CE', 31, 'BR', -4.06156000, -38.29450000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1648152'),
(13857, 'Pingo-d\'Água', 1998, 'MG', 31, 'BR', -19.73720000, -42.42838000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1789616'),
(13858, 'Pinhais', 2022, 'PR', 31, 'BR', -25.44472000, -49.19250000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1788891'),
(13859, 'Pinhal', 2001, 'RS', 31, 'BR', -27.52636000, -53.23425000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1788891'),
(13860, 'Pinhal Grande', 2001, 'RS', 31, 'BR', -29.27075000, -53.34587000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1788891'),
(13861, 'Pinhal da Serra', 2001, 'RS', 31, 'BR', -27.88164000, -51.22450000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2007765'),
(13862, 'Pinhal de São Bento', 2022, 'PR', 31, 'BR', -26.00683000, -53.48906000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q2007765'),
(13863, 'Pinhalzinho', 2021, 'SP', 31, 'BR', -22.78213000, -46.57177000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2007765'),
(13864, 'Pinhalzinho', 2014, 'SC', 31, 'BR', -26.81930000, -52.97639000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2007765'),
(13865, 'Pinhalão', 2022, 'PR', 31, 'BR', -23.92456000, -50.05444000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q2007765'),
(13866, 'Pinheiral', 1997, 'RJ', 31, 'BR', -22.51278000, -44.00056000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1843603'),
(13867, 'Pinheirinho do Vale', 2001, 'RS', 31, 'BR', -27.22637000, -53.63352000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1843603'),
(13868, 'Pinheiro', 2015, 'MA', 31, 'BR', -2.69907000, -45.12244000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1796449'),
(13869, 'Pinheiro Machado', 2001, 'RS', 31, 'BR', -31.57833000, -53.38111000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q590279'),
(13870, 'Pinheiro Preto', 2014, 'SC', 31, 'BR', -27.05458000, -51.22708000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q590279'),
(13871, 'Pinheiros', 2001, 'RS', 31, 'BR', -29.78333000, -52.73333000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q22036159'),
(13872, 'Pinheiros', 2018, 'ES', 31, 'BR', -18.34486000, -40.20984000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2018987'),
(13873, 'Pinhão', 2022, 'PR', 31, 'BR', -25.69556000, -51.65972000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q935946'),
(13874, 'Pinhão', 2003, 'SE', 31, 'BR', -10.57105000, -37.78582000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q2171696'),
(13875, 'Pintadas', 2002, 'BA', 31, 'BR', -11.92312000, -39.97782000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2171696'),
(13876, 'Pinto Bandeira', 2001, 'RS', 31, 'BR', -29.09289000, -51.46180000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q10349880'),
(13877, 'Pintópolis', 1998, 'MG', 31, 'BR', -16.01120000, -45.21931000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q10349880'),
(13878, 'Pio IX', 2008, 'PI', 31, 'BR', -6.86982000, -40.59717000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2089220'),
(13879, 'Pio XII', 2015, 'MA', 31, 'BR', -3.89451000, -45.16617000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1833985'),
(13880, 'Piquerobi', 2021, 'SP', 31, 'BR', -21.84262000, -51.74581000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1833985'),
(13881, 'Piquet Carneiro', 2016, 'CE', 31, 'BR', -5.86038000, -39.45580000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1833985'),
(13882, 'Piquete', 2021, 'SP', 31, 'BR', -22.61361000, -45.17611000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1760068'),
(13883, 'Piracaia', 2021, 'SP', 31, 'BR', -23.05389000, -46.35806000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1760752'),
(13884, 'Piracanjuba', 2000, 'GO', 31, 'BR', -17.30278000, -49.01667000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q985960'),
(13885, 'Piracema', 1998, 'MG', 31, 'BR', -20.51921000, -44.41751000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1802239'),
(13886, 'Piracicaba', 2021, 'SP', 31, 'BR', -22.71579000, -47.77297000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q330175'),
(13887, 'Piracuruca', 2008, 'PI', 31, 'BR', -3.86656000, -41.74153000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2063996'),
(13888, 'Piraju', 2021, 'SP', 31, 'BR', -23.19361000, -49.38389000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1795515'),
(13889, 'Pirajuba', 1998, 'MG', 31, 'BR', -19.94073000, -48.70306000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1795515'),
(13890, 'Pirajuí', 2021, 'SP', 31, 'BR', -21.99861000, -49.45722000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q985494'),
(13891, 'Pirambu', 2003, 'SE', 31, 'BR', -10.69075000, -36.84859000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q985494'),
(13892, 'Piranga', 1998, 'MG', 31, 'BR', -20.62892000, -43.28354000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1789518'),
(13893, 'Pirangi', 2021, 'SP', 31, 'BR', -21.08566000, -48.67186000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1789518'),
(13894, 'Piranguinho', 1998, 'MG', 31, 'BR', -22.34876000, -45.59462000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1789518'),
(13895, 'Piranguçu', 1998, 'MG', 31, 'BR', -22.56347000, -45.51039000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1789518'),
(13896, 'Piranhas', 2000, 'GO', 31, 'BR', -16.42694000, -51.82222000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1025769'),
(13897, 'Piranhas', 2007, 'AL', 31, 'BR', -9.54806000, -37.74529000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1025769'),
(13898, 'Pirapemas', 2015, 'MA', 31, 'BR', -3.77957000, -44.27746000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2089300'),
(13899, 'Pirapetinga', 1998, 'MG', 31, 'BR', -21.66797000, -42.36515000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2089300'),
(13900, 'Pirapora', 1998, 'MG', 31, 'BR', -17.41957000, -44.85974000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1756358'),
(13901, 'Pirapora do Bom Jesus', 2021, 'SP', 31, 'BR', -23.37201000, -46.97798000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1760867'),
(13902, 'Pirapozinho', 2021, 'SP', 31, 'BR', -22.27528000, -51.50000000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1760765'),
(13903, 'Pirapó', 2001, 'RS', 31, 'BR', -28.05287000, -55.23460000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q596649'),
(13904, 'Piraquara', 2022, 'PR', 31, 'BR', -25.44227000, -49.06795000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2004395'),
(13905, 'Piraquê', 2020, 'TO', 31, 'BR', -6.68862000, -48.23711000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q2004395'),
(13906, 'Pirassununga', 2021, 'SP', 31, 'BR', -21.99611000, -47.42583000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1750431'),
(13907, 'Piratini', 2001, 'RS', 31, 'BR', -31.41655000, -53.11163000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1750431'),
(13908, 'Piratininga', 2021, 'SP', 31, 'BR', -22.42279000, -49.19329000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1750431'),
(13909, 'Piratuba', 2014, 'SC', 31, 'BR', -27.45623000, -51.77474000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1750431'),
(13910, 'Piraí', 1997, 'RJ', 31, 'BR', -22.62917000, -43.89806000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1015780'),
(13911, 'Piraí do Norte', 2002, 'BA', 31, 'BR', -13.84971000, -39.39208000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q1015780'),
(13912, 'Piraí do Sul', 2022, 'PR', 31, 'BR', -24.52611000, -49.94861000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1803311'),
(13913, 'Piraúba', 1998, 'MG', 31, 'BR', -21.26468000, -43.01397000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1803311'),
(13914, 'Pirenópolis', 2000, 'GO', 31, 'BR', -15.85072000, -48.96087000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q988797'),
(13915, 'Pires Ferreira', 2016, 'CE', 31, 'BR', -4.25485000, -40.59869000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q988797'),
(13916, 'Pires do Rio', 2000, 'GO', 31, 'BR', -17.29972000, -48.27944000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q987688'),
(13917, 'Piripiri', 2008, 'PI', 31, 'BR', -4.39488000, -41.78819000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q996763'),
(13918, 'Piripá', 2002, 'BA', 31, 'BR', -14.92617000, -41.75304000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q996763'),
(13919, 'Piritiba', 2002, 'BA', 31, 'BR', -11.73028000, -40.55528000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1807706'),
(13920, 'Pirpirituba', 2005, 'PB', 31, 'BR', -6.78000000, -35.49861000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1870026'),
(13921, 'Pitanga', 2022, 'PR', 31, 'BR', -24.75722000, -51.76139000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1802922'),
(13922, 'Pitangueiras', 2022, 'PR', 31, 'BR', -23.19194000, -51.56836000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1802922'),
(13923, 'Pitangueiras', 2021, 'SP', 31, 'BR', -21.01395000, -48.26197000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q939666'),
(13924, 'Pitangui', 1998, 'MG', 31, 'BR', -19.57287000, -44.87568000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q749248'),
(13925, 'Pitimbu', 2005, 'PB', 31, 'BR', -7.41381000, -34.83686000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2008741'),
(13926, 'Pium', 2020, 'TO', 31, 'BR', -10.07399000, -49.69857000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2008741'),
(13927, 'Piumhi', 1998, 'MG', 31, 'BR', -20.43261000, -46.06077000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2008741'),
(13928, 'Piuí', 1998, 'MG', 31, 'BR', -20.46528000, -45.95806000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1755744'),
(13929, 'Piçarra', 2009, 'PA', 31, 'BR', -6.47338000, -48.95039000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1755744'),
(13930, 'Piên', 2022, 'PR', 31, 'BR', -26.08649000, -49.45641000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1755744'),
(13931, 'Piúma', 2018, 'ES', 31, 'BR', -20.83106000, -40.72932000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q1806645'),
(13932, 'Placas', 2009, 'PA', 31, 'BR', -3.97715000, -54.52418000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1806645'),
(13933, 'Planaltina', 2000, 'GO', 31, 'BR', -15.45278000, -47.61417000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13934, 'Planaltina', 2017, 'DF', 31, 'BR', -15.61791000, -47.64874000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13935, 'Planaltina do Paraná', 2022, 'PR', 31, 'BR', -23.09388000, -52.92823000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1004078'),
(13936, 'Planaltino', 2002, 'BA', 31, 'BR', -13.27349000, -40.22316000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13937, 'Planalto', 2002, 'BA', 31, 'BR', -14.72862000, -40.38025000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13938, 'Planalto', 2022, 'PR', 31, 'BR', -25.72679000, -53.72859000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13939, 'Planalto', 2021, 'SP', 31, 'BR', -21.00411000, -49.93965000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13940, 'Planalto', 2001, 'RS', 31, 'BR', -27.34391000, -53.08947000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13941, 'Planalto Alegre', 2014, 'SC', 31, 'BR', -27.07348000, -52.85503000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1004078'),
(13942, 'Planalto da Serra', 2011, 'MT', 31, 'BR', -14.56524000, -54.67771000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1806584'),
(13943, 'Planura', 1998, 'MG', 31, 'BR', -20.06924000, -48.67523000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1806584'),
(13944, 'Platina', 2021, 'SP', 31, 'BR', -22.62809000, -50.22211000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1806584'),
(13945, 'Plácido de Castro', 2012, 'AC', 31, 'BR', -10.33528000, -67.18556000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q964075'),
(13946, 'Pocinhos', 2005, 'PB', 31, 'BR', -7.07667000, -36.06111000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1816070'),
(13947, 'Poconé', 2011, 'MT', 31, 'BR', -16.25667000, -56.62278000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q166445'),
(13948, 'Pocrane', 1998, 'MG', 31, 'BR', -19.57501000, -41.56271000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q166445'),
(13949, 'Pojuca', 2002, 'BA', 31, 'BR', -12.36588000, -38.24332000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q166445'),
(13950, 'Poloni', 2021, 'SP', 31, 'BR', -20.72938000, -49.81550000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q166445'),
(13951, 'Pombal', 2005, 'PB', 31, 'BR', -6.77028000, -37.80167000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2078751'),
(13952, 'Pombos', 2006, 'PE', 31, 'BR', -8.22536000, -35.41692000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2309699'),
(13953, 'Pomerode', 2014, 'SC', 31, 'BR', -26.72404000, -49.16897000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q326651'),
(13954, 'Pompéia', 2021, 'SP', 31, 'BR', -22.10861000, -50.17167000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q1759694'),
(13955, 'Pompéu', 1998, 'MG', 31, 'BR', -19.14952000, -44.91485000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1789633'),
(13956, 'Pongaí', 2021, 'SP', 31, 'BR', -21.73364000, -49.36396000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q1789633'),
(13957, 'Ponta Grossa', 2022, 'PR', 31, 'BR', -25.09500000, -50.16194000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q242906'),
(13958, 'Ponta Porã', 2010, 'MS', 31, 'BR', -22.53611000, -55.72556000, '2019-10-05 22:35:23', '2020-05-01 17:22:36', 1, 'Q928094'),
(13959, 'Ponta de Pedras', 2009, 'PA', 31, 'BR', -1.05304000, -49.15491000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q928094'),
(13960, 'Pontal', 2021, 'SP', 31, 'BR', -21.02250000, -48.03722000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1759836'),
(13961, 'Pontal do Araguaia', 2011, 'MT', 31, 'BR', -15.96755000, -52.75421000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1759836'),
(13962, 'Pontal do Paraná', 2022, 'PR', 31, 'BR', -25.67361000, -48.51111000, '2019-10-05 22:35:23', '2020-05-01 17:22:37', 1, 'Q1803813'),
(13963, 'Pontalina', 2000, 'GO', 31, 'BR', -17.52500000, -49.44722000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q509804'),
(13964, 'Pontalinda', 2021, 'SP', 31, 'BR', -20.46316000, -50.52208000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q509804'),
(13965, 'Ponte Alta', 2014, 'SC', 31, 'BR', -27.42574000, -50.29365000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q509804'),
(13966, 'Ponte Alta do Bom Jesus', 2020, 'TO', 31, 'BR', -12.09696000, -46.62629000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q509804'),
(13967, 'Ponte Alta do Norte', 2014, 'SC', 31, 'BR', -27.18129000, -50.43580000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q509804'),
(13968, 'Ponte Alta do Tocantins', 2020, 'TO', 31, 'BR', -10.78604000, -47.18130000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q509804'),
(13969, 'Ponte Branca', 2011, 'MT', 31, 'BR', -16.65050000, -52.90238000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q509804'),
(13970, 'Ponte Nova', 1998, 'MG', 31, 'BR', -20.40703000, -42.92058000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q999139'),
(13971, 'Ponte Preta', 2001, 'RS', 31, 'BR', -27.67688000, -52.51736000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q999139'),
(13972, 'Ponte Serrada', 2014, 'SC', 31, 'BR', -26.86053000, -51.91066000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q999139'),
(13973, 'Pontes Gestal', 2021, 'SP', 31, 'BR', -20.17009000, -49.75902000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q999139'),
(13974, 'Pontes e Lacerda', 2011, 'MT', 31, 'BR', -15.22611000, -59.33528000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2011952'),
(13975, 'Ponto Belo', 2018, 'ES', 31, 'BR', -18.25222000, -40.49863000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2011952'),
(13976, 'Ponto Chique', 1998, 'MG', 31, 'BR', -16.63769000, -44.93803000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q2011952'),
(13977, 'Ponto Novo', 2002, 'BA', 31, 'BR', -10.86278000, -40.13361000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1935495'),
(13978, 'Ponto dos Volantes', 1998, 'MG', 31, 'BR', -16.85440000, -41.46344000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1935495'),
(13979, 'Pontão', 2001, 'RS', 31, 'BR', -28.04977000, -52.63740000, '2019-10-05 22:35:23', '2020-05-01 17:22:38', 1, 'Q1935495'),
(13980, 'Populina', 2021, 'SP', 31, 'BR', -19.92212000, -50.51727000, '2019-10-05 22:35:23', '2019-10-05 22:35:23', 1, 'Q1935495'),
(13981, 'Poranga', 2016, 'CE', 31, 'BR', -4.76103000, -40.93222000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1935495'),
(13982, 'Porangaba', 2021, 'SP', 31, 'BR', -23.17583000, -48.12500000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1759470'),
(13983, 'Porangatu', 2000, 'GO', 31, 'BR', -13.44083000, -49.14861000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1806864'),
(13984, 'Porciúncula', 1997, 'RJ', 31, 'BR', -20.96278000, -42.04083000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q1802244'),
(13985, 'Porecatu', 2022, 'PR', 31, 'BR', -22.75583000, -51.37917000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2007362'),
(13986, 'Portalegre', 2019, 'RN', 31, 'BR', -6.00548000, -38.00212000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2007362'),
(13987, 'Porteiras', 2016, 'CE', 31, 'BR', -7.59509000, -39.07443000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2027628'),
(13988, 'Porteirinha', 1998, 'MG', 31, 'BR', -15.68690000, -43.14041000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1789713'),
(13989, 'Porteirão', 2000, 'GO', 31, 'BR', -17.90236000, -50.15667000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1789713'),
(13990, 'Portel', 2009, 'PA', 31, 'BR', -1.93556000, -50.82111000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2089210'),
(13991, 'Portelândia', 2000, 'GO', 31, 'BR', -17.35610000, -52.67861000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1798727'),
(13992, 'Porto', 2008, 'PI', 31, 'BR', -3.94558000, -42.68879000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2113232'),
(13993, 'Porto Acre', 2012, 'AC', 31, 'BR', -9.65038000, -67.77733000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2113232'),
(13994, 'Porto Alegre', 2001, 'RS', 31, 'BR', -30.03283000, -51.23019000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q40269'),
(13995, 'Porto Alegre do Norte', 2011, 'MT', 31, 'BR', -10.85081000, -51.76934000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q40269'),
(13996, 'Porto Alegre do Piauí', 2008, 'PI', 31, 'BR', -6.95931000, -44.07411000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q40269'),
(13997, 'Porto Alegre do Tocantins', 2020, 'TO', 31, 'BR', -11.53239000, -47.03453000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q40269'),
(13998, 'Porto Amazonas', 2022, 'PR', 31, 'BR', -25.53897000, -49.90183000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q40269'),
(13999, 'Porto Barreiro', 2022, 'PR', 31, 'BR', -25.58093000, -52.39206000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q40269'),
(14000, 'Porto Belo', 2014, 'SC', 31, 'BR', -27.15778000, -48.55306000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1799285'),
(14001, 'Porto Calvo', 2007, 'AL', 31, 'BR', -9.04500000, -35.39833000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1772636'),
(14002, 'Porto Esperidião', 2011, 'MT', 31, 'BR', -15.88622000, -58.97107000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1772636'),
(14003, 'Porto Estrela', 2011, 'MT', 31, 'BR', -15.51966000, -57.22942000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q738627'),
(14004, 'Porto Feliz', 2021, 'SP', 31, 'BR', -23.21472000, -47.52389000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1645090'),
(14005, 'Porto Ferreira', 2021, 'SP', 31, 'BR', -21.85389000, -47.47917000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1772643'),
(14006, 'Porto Firme', 1998, 'MG', 31, 'BR', -20.66732000, -43.06871000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1772643'),
(14007, 'Porto Franco', 2015, 'MA', 31, 'BR', -6.34635000, -47.07258000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2082418'),
(14008, 'Porto Grande', 1999, 'AP', 31, 'BR', 0.56553000, -51.71181000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2082418'),
(14009, 'Porto Lucena', 2001, 'RS', 31, 'BR', -27.84663000, -54.96059000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2082418'),
(14010, 'Porto Mauá', 2001, 'RS', 31, 'BR', -27.59808000, -54.65773000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q2082418'),
(14011, 'Porto Murtinho', 2010, 'MS', 31, 'BR', -21.69889000, -57.88250000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1792999'),
(14012, 'Porto Nacional', 2020, 'TO', 31, 'BR', -10.52826000, -48.47443000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1792999'),
(14013, 'Porto Real', 1997, 'RJ', 31, 'BR', -22.41972000, -44.29028000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q765477'),
(14014, 'Porto Real do Colégio', 2007, 'AL', 31, 'BR', -10.10538000, -36.72695000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q22036573'),
(14015, 'Porto Rico', 2022, 'PR', 31, 'BR', -22.82902000, -53.31785000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q22036573'),
(14016, 'Porto Rico do Maranhão', 2015, 'MA', 31, 'BR', -1.85517000, -44.60930000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q22036573'),
(14017, 'Porto Seguro', 2002, 'BA', 31, 'BR', -16.44972000, -39.06472000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q499922'),
(14018, 'Porto União', 2014, 'SC', 31, 'BR', -26.23806000, -51.07833000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q1784679'),
(14019, 'Porto Velho', 2013, 'RO', 31, 'BR', -9.20787000, -64.31064000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q172512'),
(14020, 'Porto Vera Cruz', 2001, 'RS', 31, 'BR', -27.76244000, -54.90094000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q172512'),
(14021, 'Porto Vitória', 2022, 'PR', 31, 'BR', -26.25338000, -51.24188000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q1803844'),
(14022, 'Porto Walter', 2012, 'AC', 31, 'BR', -8.26861000, -72.74389000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1772588'),
(14023, 'Porto Xavier', 2001, 'RS', 31, 'BR', -27.93317000, -55.15422000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1772588'),
(14024, 'Porto da Folha', 2003, 'SE', 31, 'BR', -9.98927000, -37.48059000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2014466'),
(14025, 'Porto de Moz', 2009, 'PA', 31, 'BR', -1.74833000, -52.23833000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2011894'),
(14026, 'Porto de Pedras', 2007, 'AL', 31, 'BR', -9.11562000, -35.39158000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2011894'),
(14027, 'Porto do Mangue', 2019, 'RN', 31, 'BR', -5.07778000, -36.78375000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2011894'),
(14028, 'Porto dos Gaúchos', 2011, 'MT', 31, 'BR', -11.76043000, -56.85721000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q2011894'),
(14029, 'Portão', 2001, 'RS', 31, 'BR', -29.70746000, -51.23572000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q985582'),
(14030, 'Posse', 2000, 'GO', 31, 'BR', -14.09306000, -46.36944000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2021419'),
(14031, 'Posto da Mata', 2002, 'BA', 31, 'BR', -17.88828000, -39.85593000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2021419'),
(14032, 'Potengi', 2016, 'CE', 31, 'BR', -7.03279000, -40.04012000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2021419'),
(14033, 'Potim', 2021, 'SP', 31, 'BR', -22.82497000, -45.30614000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2021419'),
(14034, 'Potiraguá', 2002, 'BA', 31, 'BR', -15.70117000, -39.76689000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q2021419'),
(14035, 'Potirendaba', 2021, 'SP', 31, 'BR', -21.10737000, -49.39808000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2021419'),
(14036, 'Potiretama', 2016, 'CE', 31, 'BR', -5.75401000, -38.18249000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2028629'),
(14037, 'Poté', 1998, 'MG', 31, 'BR', -17.79279000, -41.76522000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q2028629'),
(14038, 'Pouso Alegre', 1998, 'MG', 31, 'BR', -22.23000000, -45.93639000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q949798'),
(14039, 'Pouso Alto', 1998, 'MG', 31, 'BR', -22.17957000, -44.95271000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1789528'),
(14040, 'Pouso Novo', 2001, 'RS', 31, 'BR', -29.16777000, -52.22165000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1789528'),
(14041, 'Pouso Redondo', 2014, 'SC', 31, 'BR', -27.30394000, -49.98473000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1789528'),
(14042, 'Poxoréo', 2011, 'MT', 31, 'BR', -15.83722000, -54.38917000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1920318'),
(14043, 'Poxoréu', 2011, 'MT', 31, 'BR', -15.75635000, -54.11866000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1920318'),
(14044, 'Poá', 2021, 'SP', 31, 'BR', -23.53459000, -46.34662000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q377601'),
(14045, 'Poço Branco', 2019, 'RN', 31, 'BR', -5.62278000, -35.66278000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q1802657'),
(14046, 'Poço Dantas', 2005, 'PB', 31, 'BR', -6.38792000, -38.53359000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q1802657'),
(14047, 'Poço Fundo', 1998, 'MG', 31, 'BR', -21.82067000, -45.98826000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q1802657'),
(14048, 'Poço Redondo', 2003, 'SE', 31, 'BR', -9.88033000, -37.77612000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q430927'),
(14049, 'Poço Verde', 2003, 'SE', 31, 'BR', -10.81636000, -38.15053000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q22036374'),
(14050, 'Poço das Antas', 2001, 'RS', 31, 'BR', -29.45339000, -51.66969000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q22036374'),
(14051, 'Poço das Trincheiras', 2007, 'AL', 31, 'BR', -9.31250000, -37.28556000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1795960'),
(14052, 'Poço de José de Moura', 2005, 'PB', 31, 'BR', -6.59957000, -38.50574000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q1795960'),
(14053, 'Poços de Caldas', 1998, 'MG', 31, 'BR', -21.79340000, -46.53571000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q817216'),
(14054, 'Poção', 2006, 'PE', 31, 'BR', -8.21385000, -36.72266000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q1809609'),
(14055, 'Poção de Pedras', 2015, 'MA', 31, 'BR', -4.78496000, -44.91232000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q2104834'),
(14056, 'Poções', 2002, 'BA', 31, 'BR', -14.52972000, -40.36528000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1793430'),
(14057, 'Pracinha', 2021, 'SP', 31, 'BR', -21.84003000, -51.08033000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1793430'),
(14058, 'Pracuúba', 1999, 'AP', 31, 'BR', 1.67076000, -51.24490000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1793430'),
(14059, 'Prado', 2002, 'BA', 31, 'BR', -17.31655000, -39.23355000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1807887'),
(14060, 'Prado Ferreira', 2022, 'PR', 31, 'BR', -23.02439000, -51.37909000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1807887'),
(14061, 'Prados', 1998, 'MG', 31, 'BR', -21.10761000, -44.06479000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1779977'),
(14062, 'Pradópolis', 2021, 'SP', 31, 'BR', -21.35944000, -48.06556000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q1649607'),
(14063, 'Praia Grande', 2021, 'SP', 31, 'BR', -24.00583000, -46.40278000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q851292'),
(14064, 'Praia Grande', 2014, 'SC', 31, 'BR', -29.20318000, -50.03412000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q851292'),
(14065, 'Praia Norte', 2020, 'TO', 31, 'BR', -5.46958000, -47.79732000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q851292'),
(14066, 'Prainha', 2009, 'PA', 31, 'BR', -2.20147000, -53.49329000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2009663'),
(14067, 'Pranchita', 2022, 'PR', 31, 'BR', -25.96586000, -53.69675000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2009663'),
(14068, 'Prata', 2005, 'PB', 31, 'BR', -7.71250000, -37.09411000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2329634'),
(14069, 'Prata', 1998, 'MG', 31, 'BR', -19.37346000, -48.94362000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q22066969'),
(14070, 'Prata do Piauí', 2008, 'PI', 31, 'BR', -5.71099000, -42.15331000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q22066969'),
(14071, 'Pratinha', 1998, 'MG', 31, 'BR', -19.74704000, -46.42881000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q22066969'),
(14072, 'Pratápolis', 1998, 'MG', 31, 'BR', -20.78572000, -46.84447000, '2019-10-05 22:35:24', '2020-05-01 17:22:37', 1, 'Q22066969'),
(14073, 'Pratânia', 2021, 'SP', 31, 'BR', -22.81737000, -48.68698000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q22066969'),
(14074, 'Presidente Alves', 2021, 'SP', 31, 'BR', -22.10947000, -49.40588000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q22066969'),
(14075, 'Presidente Bernardes', 2021, 'SP', 31, 'BR', -22.00611000, -51.55306000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1816203'),
(14076, 'Presidente Bernardes', 1998, 'MG', 31, 'BR', -20.77329000, -43.16210000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1804762'),
(14077, 'Presidente Castello Branco', 2014, 'SC', 31, 'BR', -27.23647000, -51.77999000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q1804762'),
(14078, 'Presidente Castelo Branco', 2022, 'PR', 31, 'BR', -23.26207000, -52.14511000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q2077944'),
(14079, 'Presidente Dutra', 2015, 'MA', 31, 'BR', -5.30484000, -44.50508000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q637452'),
(14080, 'Presidente Dutra', 2002, 'BA', 31, 'BR', -11.29503000, -41.98563000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q637452'),
(14081, 'Presidente Epitácio', 2021, 'SP', 31, 'BR', -21.76333000, -52.11556000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q983495'),
(14082, 'Presidente Figueiredo', 2004, 'AM', 31, 'BR', -1.28344000, -59.98317000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q983495'),
(14083, 'Presidente Getúlio', 2014, 'SC', 31, 'BR', -27.06188000, -49.71403000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q983495'),
(14084, 'Presidente Juscelino', 2015, 'MA', 31, 'BR', -3.08609000, -44.07739000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q983495'),
(14085, 'Presidente Juscelino', 1998, 'MG', 31, 'BR', -18.73883000, -44.07767000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q948550'),
(14086, 'Presidente Jânio Quadros', 2002, 'BA', 31, 'BR', -14.82942000, -41.78616000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q948550'),
(14087, 'Presidente Kennedy', 2018, 'ES', 31, 'BR', -21.14995000, -41.07293000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q948550'),
(14088, 'Presidente Kennedy', 2020, 'TO', 31, 'BR', -8.48264000, -48.44075000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q948550'),
(14089, 'Presidente Kubitschek', 1998, 'MG', 31, 'BR', -18.64982000, -43.57979000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q948550'),
(14090, 'Presidente Lucena', 2001, 'RS', 31, 'BR', -29.52778000, -51.19285000, '2019-10-05 22:35:24', '2019-10-05 22:35:24', 1, 'Q948550'),
(14091, 'Presidente Médici', 2013, 'RO', 31, 'BR', -11.18523000, -61.94072000, '2019-10-05 22:35:24', '2020-05-01 17:22:38', 1, 'Q1871362'),
(14092, 'Presidente Médici', 2015, 'MA', 31, 'BR', -2.38729000, -45.84126000, '2019-10-05 22:35:24', '2020-05-01 17:22:36', 1, 'Q1871362'),
(14093, 'Presidente Nereu', 2014, 'SC', 31, 'BR', -27.26320000, -49.32267000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1871362'),
(14094, 'Presidente Olegário', 1998, 'MG', 31, 'BR', -18.16191000, -46.40629000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1750034'),
(14095, 'Presidente Prudente', 2021, 'SP', 31, 'BR', -22.12556000, -51.38889000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q525618'),
(14096, 'Presidente Sarney', 2015, 'MA', 31, 'BR', -2.62072000, -45.43931000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q525618'),
(14097, 'Presidente Tancredo Neves', 2002, 'BA', 31, 'BR', -13.46145000, -39.42126000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1796453'),
(14098, 'Presidente Vargas', 2015, 'MA', 31, 'BR', -3.42385000, -44.01015000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1796453'),
(14099, 'Presidente Venceslau', 2021, 'SP', 31, 'BR', -21.87611000, -51.84389000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1645733'),
(14100, 'Primavera', 2009, 'PA', 31, 'BR', -0.94947000, -47.11040000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2329733'),
(14101, 'Primavera', 2006, 'PE', 31, 'BR', -8.33678000, -35.37539000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2099821'),
(14102, 'Primavera de Rondônia', 2013, 'RO', 31, 'BR', -11.92939000, -61.30611000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q2099821'),
(14103, 'Primavera do Leste', 2011, 'MT', 31, 'BR', -15.14064000, -54.19501000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2099821'),
(14104, 'Primeira Cruz', 2015, 'MA', 31, 'BR', -2.65082000, -43.32895000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2099821'),
(14105, 'Primeiro de Maio', 2022, 'PR', 31, 'BR', -22.87095000, -51.08897000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2099821'),
(14106, 'Princesa', 2014, 'SC', 31, 'BR', -26.43219000, -53.62477000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2099821'),
(14107, 'Princesa Isabel', 2005, 'PB', 31, 'BR', -7.73667000, -37.99333000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2095104'),
(14108, 'Professor Jamil', 2000, 'GO', 31, 'BR', -17.29408000, -49.25572000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2095104'),
(14109, 'Progresso', 2001, 'RS', 31, 'BR', -29.22873000, -52.30960000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1803253'),
(14110, 'Promissão', 2021, 'SP', 31, 'BR', -21.53667000, -49.85806000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q431377'),
(14111, 'Propriá', 2003, 'SE', 31, 'BR', -10.25379000, -36.78579000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1807371'),
(14112, 'Protásio Alves', 2001, 'RS', 31, 'BR', -28.74413000, -51.49620000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1807371'),
(14113, 'Prudente de Morais', 1998, 'MG', 31, 'BR', -19.46809000, -44.11363000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1755006'),
(14114, 'Prudentópolis', 2022, 'PR', 31, 'BR', -25.21306000, -50.97778000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q751491'),
(14115, 'Pugmil', 2020, 'TO', 31, 'BR', -10.42875000, -48.85558000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q751491'),
(14116, 'Pureza', 2019, 'RN', 31, 'BR', -5.42517000, -35.59285000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q751491'),
(14117, 'Putinga', 2001, 'RS', 31, 'BR', -29.04776000, -52.16503000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q751491'),
(14118, 'Puxinanã', 2005, 'PB', 31, 'BR', -7.16111000, -35.96056000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2011046'),
(14119, 'Pão de Açúcar', 2007, 'AL', 31, 'BR', -9.68353000, -37.45431000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q2027100'),
(14120, 'Pé de Serra', 2002, 'BA', 31, 'BR', -11.88974000, -39.61937000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q2027100'),
(14121, 'Pérola', 2022, 'PR', 31, 'BR', -23.84076000, -53.70733000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2027100'),
(14122, 'Pérola d\'Oeste', 2022, 'PR', 31, 'BR', -25.84683000, -53.75640000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1803472'),
(14123, 'Pôrto Barra do Ivinheima', 2010, 'MS', 31, 'BR', -22.96667000, -53.66667000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q22036563'),
(14124, 'Pôsto Fiscal Rolim de Moura', 2013, 'RO', 31, 'BR', -13.08271000, -62.27726000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q22036563'),
(14125, 'Quadra', 2021, 'SP', 31, 'BR', -23.30027000, -48.04264000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1795680'),
(14126, 'Quaraí', 2001, 'RS', 31, 'BR', -30.38750000, -56.45139000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1637478'),
(14127, 'Quartel Geral', 1998, 'MG', 31, 'BR', -19.28715000, -45.58434000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1637478'),
(14128, 'Quarto Centenário', 2022, 'PR', 31, 'BR', -24.29608000, -53.11994000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1637478'),
(14129, 'Quatiguá', 2022, 'PR', 31, 'BR', -23.55499000, -49.91985000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1637478'),
(14130, 'Quatipuru', 2009, 'PA', 31, 'BR', -0.82554000, -47.01043000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1637478'),
(14131, 'Quatis', 1997, 'RJ', 31, 'BR', -22.40722000, -44.25806000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1800698'),
(14132, 'Quatro Barras', 2022, 'PR', 31, 'BR', -25.36556000, -49.07694000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q650514'),
(14133, 'Quatro Irmãos', 2001, 'RS', 31, 'BR', -27.81251000, -52.46069000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q596343'),
(14134, 'Quatro Pontes', 2022, 'PR', 31, 'BR', -24.50435000, -54.00566000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q596343'),
(14135, 'Quatá', 2021, 'SP', 31, 'BR', -22.24750000, -50.69833000, '2019-10-05 22:35:25', '2020-05-01 17:22:38', 1, 'Q1760383'),
(14136, 'Quebrangulo', 2007, 'AL', 31, 'BR', -9.31889000, -36.47111000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1772591'),
(14137, 'Quedas do Iguaçu', 2022, 'PR', 31, 'BR', -25.43169000, -52.98265000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1772591'),
(14138, 'Queimada Nova', 2008, 'PI', 31, 'BR', -8.53997000, -41.24753000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1772591'),
(14139, 'Queimadas', 2002, 'BA', 31, 'BR', -10.97833000, -39.62639000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1808402'),
(14140, 'Queimadas', 2005, 'PB', 31, 'BR', -7.42696000, -35.89774000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q782948'),
(14141, 'Queimados', 1997, 'RJ', 31, 'BR', -22.71611000, -43.55528000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1800756'),
(14142, 'Queiroz', 2021, 'SP', 31, 'BR', -21.79287000, -50.23587000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1800756'),
(14143, 'Queluz', 2021, 'SP', 31, 'BR', -22.51320000, -44.78091000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1800756'),
(14144, 'Queluzito', 1998, 'MG', 31, 'BR', -20.73147000, -43.89355000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1800756'),
(14145, 'Querência', 2011, 'MT', 31, 'BR', -12.32120000, -52.54760000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q1800756'),
(14146, 'Querência do Norte', 2022, 'PR', 31, 'BR', -23.10147000, -53.56278000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1800756'),
(14147, 'Quevedos', 2001, 'RS', 31, 'BR', -29.30670000, -54.07002000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1800756'),
(14148, 'Quijingue', 2002, 'BA', 31, 'BR', -10.75250000, -39.20917000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1793720'),
(14149, 'Quilombo', 2014, 'SC', 31, 'BR', -26.72644000, -52.67673000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1793720'),
(14150, 'Quinta do Sol', 2022, 'PR', 31, 'BR', -23.79814000, -52.15746000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1793720'),
(14151, 'Quintana', 2021, 'SP', 31, 'BR', -22.09720000, -50.37082000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1793720'),
(14152, 'Quinze de Novembro', 2001, 'RS', 31, 'BR', -28.74442000, -53.11648000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1793720'),
(14153, 'Quipapá', 2006, 'PE', 31, 'BR', -8.82778000, -36.01167000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q2011039'),
(14154, 'Quirinópolis', 2000, 'GO', 31, 'BR', -18.44833000, -50.45167000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q986268'),
(14155, 'Quissamã', 1997, 'RJ', 31, 'BR', -22.09574000, -41.39137000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q986268'),
(14156, 'Quitandinha', 2022, 'PR', 31, 'BR', -25.90132000, -49.50522000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q986268'),
(14157, 'Quiterianópolis', 2016, 'CE', 31, 'BR', -5.88995000, -40.72808000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q986268'),
(14158, 'Quixaba', 2005, 'PB', 31, 'BR', -7.05081000, -37.11783000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2077775'),
(14159, 'Quixaba', 2006, 'PE', 31, 'BR', -7.72105000, -37.87276000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2034566'),
(14160, 'Quixabeira', 2002, 'BA', 31, 'BR', -11.41345000, -40.13302000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1811500'),
(14161, 'Quixadá', 2016, 'CE', 31, 'BR', -4.97056000, -39.01812000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q2013932'),
(14162, 'Quixelô', 2016, 'CE', 31, 'BR', -6.15064000, -39.11702000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q1808458'),
(14163, 'Quixeramobim', 2016, 'CE', 31, 'BR', -5.13424000, -39.33459000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q999088'),
(14164, 'Quixeré', 2016, 'CE', 31, 'BR', -5.07417000, -37.98861000, '2019-10-05 22:35:25', '2020-05-01 17:22:36', 1, 'Q2028061'),
(14165, 'Rafael Fernandes', 2019, 'RN', 31, 'BR', -6.19713000, -38.19794000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2028061'),
(14166, 'Rafael Godeiro', 2019, 'RN', 31, 'BR', -6.04885000, -37.73693000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1802391'),
(14167, 'Rafael Jambeiro', 2002, 'BA', 31, 'BR', -12.50087000, -39.50915000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1802391'),
(14168, 'Rafard', 2021, 'SP', 31, 'BR', -23.03281000, -47.58676000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1802391'),
(14169, 'Ramilândia', 2022, 'PR', 31, 'BR', -25.09153000, -54.00622000, '2019-10-05 22:35:25', '2020-05-01 17:22:37', 1, 'Q1802391'),
(14170, 'Rancharia', 2021, 'SP', 31, 'BR', -22.22917000, -50.89306000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q1760603'),
(14171, 'Rancho Alegre', 2022, 'PR', 31, 'BR', -23.05892000, -50.91888000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q730276'),
(14172, 'Rancho Alegre d\'Oeste', 2022, 'PR', 31, 'BR', -24.30404000, -52.98349000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2004390'),
(14173, 'Rancho Queimado', 2014, 'SC', 31, 'BR', -27.70731000, -49.07164000, '2019-10-05 22:35:25', '2019-10-05 22:35:25', 1, 'Q2004390');

