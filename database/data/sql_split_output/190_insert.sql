INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(96541, 'Sânnicolau Român', 4723, 'BH', 181, 'R<PERSON>', 46.96101000, 21.71466000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q1194324'),
(96542, 'Sânpaul', 4734, 'CJ', 181, 'RO', 46.88333000, 23.41667000, '2019-10-05 23:15:44', '2020-05-01 17:23:09', 1, 'Q1076069'),
(96543, 'Sânpetru', 4759, 'BV', 181, 'RO', 45.71082000, 25.63678000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q15279385'),
(96544, '<PERSON><PERSON><PERSON>ru German', 4739, 'AR', 181, 'R<PERSON>', 46.11327000, 21.04932000, '2019-10-05 23:15:44', '2020-05-01 17:23:08', 1, 'Q786287'),
(96545, 'S<PERSON>petru Mare', 4748, 'TM', 181, 'RO', 46.04515000, 20.81834000, '2019-10-05 23:15:44', '2020-05-01 17:23:11', 1, 'Q1014865'),
(96546, 'Sânsimion', 4749, 'HR', 181, 'RO', 46.25000000, 25.88333000, '2019-10-05 23:15:44', '2020-05-01 17:23:10', 1, 'Q876097'),
(96547, 'Tuluceşti', 4747, 'GL', 181, 'RO', 45.56667000, 28.03333000, '2019-10-05 23:17:00', '2020-05-01 17:23:09', 1, 'Q3726527'),
(96548, 'Tunari', 4725, 'IF', 181, 'RO', 44.55000000, 26.15000000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1707028'),
(96549, 'Tunelu-Teliu', 4759, 'BV', 181, 'RO', 45.70000000, 25.85000000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1707028'),
(96550, 'Tupilaţi', 4731, 'NT', 181, 'RO', 47.06667000, 26.63333000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q1089944'),
(96551, 'Tur', 4746, 'SM', 181, 'RO', 47.88231000, 23.39045000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q969857'),
(96552, 'Turburea', 4750, 'GJ', 181, 'RO', 44.71667000, 23.51667000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q2606841'),
(96553, 'Turceni', 4750, 'GJ', 181, 'RO', 44.68333000, 23.36667000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q2606841'),
(96554, 'Turcești', 4757, 'VL', 181, 'RO', 45.04023000, 23.86574000, '2019-10-05 23:17:00', '2020-05-01 17:23:12', 1, 'Q2606841'),
(96555, 'Turcineşti', 4750, 'GJ', 181, 'RO', 45.10000000, 23.33333000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q2717399'),
(96556, 'Turcoaia', 4727, 'TL', 181, 'RO', 45.11667000, 28.18333000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1041615'),
(96557, 'Turda', 4727, 'TL', 181, 'RO', 44.97501000, 28.62402000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q3658195'),
(96558, 'Turda', 4734, 'CJ', 181, 'RO', 46.56667000, 23.78333000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q3658195'),
(96559, 'Turdaş', 4721, 'HD', 181, 'RO', 45.85000000, 23.11667000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q15520545'),
(96560, 'Tureac', 4733, 'BN', 181, 'RO', 47.22967000, 24.80589000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1325453'),
(96561, 'Tureni', 4734, 'CJ', 181, 'RO', 46.62934000, 23.70240000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1007278'),
(96562, 'Turia', 4754, 'CV', 181, 'RO', 46.03333000, 26.05000000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1093353'),
(96563, 'Turluianu', 4744, 'BC', 181, 'RO', 46.45298000, 26.64607000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1325556'),
(96564, 'Turnu', 4739, 'AR', 181, 'RO', 46.25764000, 21.12657000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q831720'),
(96565, 'Turnu Măgurele', 4728, 'TR', 181, 'RO', 43.74690000, 24.86846000, '2019-10-05 23:17:00', '2020-05-01 17:23:11', 1, 'Q831720'),
(96566, 'Turnu Roşu', 4755, 'SB', 181, 'RO', 45.63333000, 24.30000000, '2019-10-05 23:17:00', '2020-05-01 17:23:11', 1, 'Q934617'),
(96567, 'Turnu Ruieni', 4753, 'CS', 181, 'RO', 45.39070000, 22.33557000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1069996'),
(96568, 'Turulung', 4746, 'SM', 181, 'RO', 47.93333000, 23.08333000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1189075'),
(96569, 'Turţ', 4746, 'SM', 181, 'RO', 47.98333000, 23.21667000, '2019-10-05 23:17:00', '2020-05-01 17:23:11', 1, 'Q1190764'),
(96570, 'Tuta', 4744, 'BC', 181, 'RO', 46.25643000, 26.69103000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q906817'),
(96571, 'Tutana', 4722, 'AG', 181, 'RO', 45.04286000, 24.65122000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q12163040'),
(96572, 'Tutova', 4752, 'VS', 181, 'RO', 46.11667000, 27.55000000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q2297470'),
(96573, 'Tuzla', 4737, 'CT', 181, 'RO', 44.00000000, 28.63333000, '2019-10-05 23:17:00', '2019-10-05 23:17:00', 1, 'Q1065953'),
(96574, 'Tuşnad', 4749, 'HR', 181, 'RO', 46.20000000, 25.90000000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q743547'),
(96575, 'Tușnadu Nou', 4749, 'HR', 181, 'RO', 46.19364000, 25.88757000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q1412307'),
(96576, 'Tâmboeşti', 4758, 'VN', 181, 'RO', 45.51667000, 27.05000000, '2019-10-05 23:17:00', '2020-05-01 17:23:12', 1, 'Q12725227'),
(96577, 'Tâmna', 4751, 'MH', 181, 'RO', 44.56667000, 23.01667000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q2465554'),
(96578, 'Tânganu', 4725, 'IF', 181, 'RO', 44.40628000, 26.31262000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q12161265'),
(96579, 'Tântava', 4726, 'GR', 181, 'RO', 44.41511000, 25.82450000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q12161273'),
(96580, 'Târgovişte', 4745, 'DB', 181, 'RO', 44.92543000, 25.45670000, '2019-10-05 23:17:00', '2020-05-01 17:23:09', 1, 'Q16898233'),
(96581, 'Târgu Bujor', 4747, 'GL', 181, 'RO', 45.86667000, 27.90000000, '2019-10-05 23:17:00', '2020-05-01 17:23:09', 1, 'Q16898233'),
(96582, 'Târgu Cărbuneşti', 4750, 'GJ', 181, 'RO', 44.95000000, 23.51667000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q16898233'),
(96583, 'Târgu Frumos', 4735, 'IS', 181, 'RO', 47.20000000, 27.00000000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q16898233'),
(96584, 'Târgu Jiu', 4750, 'GJ', 181, 'RO', 45.05000000, 23.28333000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q16898233'),
(96586, 'Târgu Neamţ', 4731, 'NT', 181, 'RO', 47.20000000, 26.36667000, '2019-10-05 23:17:00', '2020-05-01 17:23:10', 1, 'Q16898396'),
(96587, 'Târgu Ocna', 4744, 'BC', 181, 'RO', 46.27899000, 26.61301000, '2019-10-05 23:17:00', '2020-05-01 17:23:08', 1, 'Q16898396'),
(96588, 'Târgu Secuiesc', 4754, 'CV', 181, 'RO', 46.00000000, 26.13333000, '2019-10-05 23:17:00', '2020-05-01 17:23:09', 1, 'Q16898396'),
(96589, 'Târgu Trotuş', 4744, 'BC', 181, 'RO', 46.26667000, 26.66667000, '2019-10-05 23:17:00', '2020-05-01 17:23:08', 1, 'Q15905310'),
(96590, 'Târguşor', 4737, 'CT', 181, 'RO', 44.45000000, 28.41667000, '2019-10-05 23:17:00', '2020-05-01 17:23:09', 1, 'Q1188294'),
(96591, 'Târgşoru Vechi', 4729, 'PH', 181, 'RO', 44.86667000, 25.91667000, '2019-10-05 23:17:00', '2020-05-01 17:23:11', 1, 'Q2720363'),
(96592, 'Târlişua', 4733, 'BN', 181, 'RO', 47.38333000, 24.18333000, '2019-10-05 23:17:00', '2020-05-01 17:23:08', 1, 'Q999223'),
(96593, 'Târnava', 4755, 'SB', 181, 'RO', 46.13975000, 24.28926000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q1077559'),
(96594, 'Târnava', 4728, 'TR', 181, 'RO', 44.14440000, 25.56347000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q12161336'),
(96595, 'Târnova', 4739, 'AR', 181, 'RO', 46.31667000, 21.80000000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q1079692'),
(96596, 'Târnova', 4753, 'CS', 181, 'RO', 45.34250000, 22.00611000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q75498'),
(96597, 'Târşolţ', 4746, 'SM', 181, 'RO', 47.95000000, 23.35000000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q1079047'),
(96598, 'Târşolţel', 4746, 'SM', 181, 'RO', 47.95000000, 23.35000000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q1079047'),
(96599, 'Tăbărăști', 4756, 'BZ', 181, 'RO', 45.07948000, 26.91943000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q12160294'),
(96600, 'Tăcuta', 4752, 'VS', 181, 'RO', 46.91667000, 27.68333000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q2549068'),
(96601, 'Tălmaciu', 4755, 'SB', 181, 'RO', 45.66714000, 24.26464000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q2549068'),
(96602, 'Tălmăcel', 4755, 'SB', 181, 'RO', 45.64712000, 24.24270000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q3893977'),
(96603, 'Tălpaș', 4742, 'DJ', 181, 'RO', 44.68227000, 23.74553000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q2543203'),
(96604, 'Tălpigi', 4747, 'GL', 181, 'RO', 46.00796000, 27.47247000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q12160418'),
(96605, 'Tămașda', 4723, 'BH', 181, 'RO', 46.64453000, 21.55685000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q730033'),
(96606, 'Tămădău Mare', 4732, 'CL', 181, 'RO', 44.46667000, 26.55000000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q2716789'),
(96607, 'Tămăşeni', 4731, 'NT', 181, 'RO', 46.98333000, 26.93333000, '2019-10-05 23:17:01', '2020-05-01 17:23:10', 1, 'Q1195088'),
(96608, 'Tămășeu', 4723, 'BH', 181, 'RO', 47.22116000, 21.92760000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q1022806'),
(96609, 'Tănăsoaia', 4758, 'VN', 181, 'RO', 46.10000000, 27.36667000, '2019-10-05 23:17:01', '2020-05-01 17:23:12', 1, 'Q12725228'),
(96610, 'Tărcaia', 4723, 'BH', 181, 'RO', 46.63333000, 22.36667000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q1088264'),
(96611, 'Tăriceni', 4729, 'PH', 181, 'RO', 44.79624000, 25.96958000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q12160938'),
(96612, 'Tărlungeni', 4759, 'BV', 181, 'RO', 45.63333000, 25.75000000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q15279391'),
(96613, 'Tărtăşeşti', 4745, 'DB', 181, 'RO', 44.57722000, 25.81278000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q12160928'),
(96614, 'Tătaru', 4729, 'PH', 181, 'RO', 45.10000000, 26.33333000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q6455505'),
(96615, 'Tătaru', 4736, 'BR', 181, 'RO', 44.84482000, 27.43376000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q12160965'),
(96616, 'Tătuleşti', 4738, 'OT', 181, 'RO', 44.63333000, 24.63333000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q2719298'),
(96617, 'Tătărani', 4745, 'DB', 181, 'RO', 45.00000000, 25.26667000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q12160972'),
(96618, 'Tătărani', 4729, 'PH', 181, 'RO', 44.89763000, 26.03509000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q12160973'),
(96619, 'Tătăranu', 4758, 'VN', 181, 'RO', 45.51667000, 27.31667000, '2019-10-05 23:17:01', '2020-05-01 17:23:12', 1, 'Q5070122'),
(96620, 'Tătăruşi', 4735, 'IS', 181, 'RO', 47.35000000, 26.60000000, '2019-10-05 23:17:01', '2020-05-01 17:23:10', 1, 'Q2673463'),
(96621, 'Tătărăni', 4752, 'VS', 181, 'RO', 46.69422000, 27.96443000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q2298061'),
(96622, 'Tătărăști', 4744, 'BC', 181, 'RO', 46.21667000, 27.20000000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q15905191'),
(96623, 'Tătărăștii de Jos', 4728, 'TR', 181, 'RO', 44.37501000, 25.17721000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q938018'),
(96624, 'Tătărăștii de Sus', 4728, 'TR', 181, 'RO', 44.40768000, 25.12192000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q2723935'),
(96625, 'Tăureni', 4749, 'HR', 181, 'RO', 46.26667000, 25.25000000, '2019-10-05 23:17:01', '2020-05-01 17:23:10', 1, 'Q15931292'),
(96626, 'Tăut', 4723, 'BH', 181, 'RO', 46.71990000, 21.83845000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q548455'),
(96627, 'Tăuteu', 4723, 'BH', 181, 'RO', 47.26667000, 22.33333000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q1088231'),
(96628, 'Tăuții de Sus', 4760, 'MM', 181, 'RO', 47.65665000, 23.65841000, '2019-10-05 23:17:01', '2020-05-01 17:23:10', 1, 'Q1088231'),
(96630, 'Tăşnad', 4746, 'SM', 181, 'RO', 47.48333000, 22.58333000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q1088231'),
(96631, 'Tășad', 4723, 'BH', 181, 'RO', 46.93773000, 22.12516000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q539144'),
(96632, 'Ucea de Jos', 4759, 'BV', 181, 'RO', 45.78410000, 24.67097000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q679787'),
(96633, 'Ucea de Sus', 4759, 'BV', 181, 'RO', 45.75000000, 24.68333000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q3889990'),
(96634, 'Uda', 4735, 'IS', 181, 'RO', 47.33645000, 26.57165000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12163457'),
(96635, 'Uda', 4722, 'AG', 181, 'RO', 44.91667000, 24.56667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2534597'),
(96636, 'Uda-Clocociov', 4728, 'TR', 181, 'RO', 43.88791000, 24.71437000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2549918'),
(96637, 'Uda-Paciurea', 4728, 'TR', 181, 'RO', 43.87979000, 24.72379000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12163452'),
(96638, 'Udeşti', 4720, 'SV', 181, 'RO', 47.56667000, 26.41667000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q2606120'),
(96639, 'Udupu', 4728, 'TR', 181, 'RO', 44.39657000, 25.14488000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12163521'),
(96640, 'Uileacu de Beiuș', 4723, 'BH', 181, 'RO', 46.68532000, 22.22148000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q611213'),
(96641, 'Uioara de Jos', 4724, 'AB', 181, 'RO', 46.37087000, 23.83863000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q738105'),
(96642, 'Uivar', 4748, 'TM', 181, 'RO', 45.65806000, 20.90583000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q932472'),
(96643, 'Ulieşti', 4745, 'DB', 181, 'RO', 44.58333000, 25.41667000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q12164508'),
(96644, 'Ulma', 4720, 'SV', 181, 'RO', 47.88333000, 25.30000000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q1065999'),
(96645, 'Ulmeni', 4756, 'BZ', 181, 'RO', 45.06667000, 26.65000000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164445'),
(96647, 'Ulmeni', 4732, 'CL', 181, 'RO', 44.15000000, 26.71667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2716801'),
(96648, 'Ulmi', 4726, 'GR', 181, 'RO', 44.48667000, 25.78028000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2717452'),
(96649, 'Ulmi', 4745, 'DB', 181, 'RO', 44.90000000, 25.50000000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164462'),
(96650, 'Ulmu', 4736, 'BR', 181, 'RO', 44.95000000, 27.31667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164457'),
(96651, 'Ulmu', 4732, 'CL', 181, 'RO', 44.26667000, 26.91667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164459'),
(96652, 'Umbrăreşti', 4747, 'GL', 181, 'RO', 45.71667000, 27.46667000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q1065940'),
(96654, 'Umbrărești-Deal', 4747, 'GL', 181, 'RO', 45.70000000, 27.46667000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q12164551'),
(96655, 'Ungheni', 4722, 'AG', 181, 'RO', 44.50000000, 24.96667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2486540'),
(96656, 'Ungra', 4759, 'BV', 181, 'RO', 45.98333000, 25.26667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q15281735'),
(96657, 'Unguraş', 4734, 'CJ', 181, 'RO', 47.11667000, 24.05000000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q368122'),
(96658, 'Ungureni', 4745, 'DB', 181, 'RO', 44.57804000, 25.50006000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164593'),
(96659, 'Ungureni', 4747, 'GL', 181, 'RO', 45.91307000, 27.47000000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164588'),
(96660, 'Ungureni', 4760, 'MM', 181, 'RO', 47.54631000, 23.95241000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q754611'),
(96661, 'Ungureni', 4744, 'BC', 181, 'RO', 46.52283000, 27.11186000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q15928593'),
(96662, 'Ungureni', 4740, 'BT', 181, 'RO', 47.88176000, 26.79899000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q1087469'),
(96663, 'Ungureni-Jianu', 4740, 'BT', 181, 'RO', 47.88333000, 26.78333000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q1087469'),
(96664, 'Unguriu', 4756, 'BZ', 181, 'RO', 45.27933000, 26.63208000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164603'),
(96665, 'Unirea', 4758, 'VN', 181, 'RO', 45.74020000, 27.10741000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164657'),
(96666, 'Unirea', 4732, 'CL', 181, 'RO', 44.26667000, 27.63333000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164656'),
(96667, 'Unirea', 4733, 'BN', 181, 'RO', 47.16060000, 24.53078000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q620165'),
(96668, 'Unirea', 4724, 'AB', 181, 'RO', 46.40387000, 23.81314000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q1024232'),
(96669, 'Unirea', 4736, 'BR', 181, 'RO', 45.11667000, 27.81667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2716458'),
(96670, 'Unirea', 4742, 'DJ', 181, 'RO', 44.15000000, 23.18333000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q1945757'),
(96671, 'Unţeni', 4740, 'BT', 181, 'RO', 47.80000000, 26.78333000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q2716340'),
(96672, 'Urca', 4734, 'CJ', 181, 'RO', 46.54885000, 23.96187000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q755666'),
(96673, 'Urdari', 4750, 'GJ', 181, 'RO', 44.80000000, 23.30000000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2717917'),
(96674, 'Urecheni', 4731, 'NT', 181, 'RO', 47.16667000, 26.51667000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2605237'),
(96675, 'Urecheşti', 4758, 'VN', 181, 'RO', 45.60000000, 27.06667000, '2019-10-05 23:17:01', '2020-05-01 17:23:12', 1, 'Q1403943'),
(96676, 'Urecheşti', 4744, 'BC', 181, 'RO', 46.13333000, 27.08333000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q15928882'),
(96677, 'Uricani', 4721, 'HD', 181, 'RO', 45.33731000, 23.15240000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q15928882'),
(96678, 'Uriu', 4733, 'BN', 181, 'RO', 47.20000000, 24.05000000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q999729'),
(96679, 'Urlaţi', 4729, 'PH', 181, 'RO', 44.98333000, 26.23333000, '2019-10-05 23:17:01', '2020-05-01 17:23:11', 1, 'Q999729'),
(96680, 'Urleasca', 4736, 'BR', 181, 'RO', 45.13697000, 27.64736000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164760'),
(96681, 'Urleta', 4729, 'PH', 181, 'RO', 45.08732000, 25.79354000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164753'),
(96682, 'Urmeniş', 4733, 'BN', 181, 'RO', 46.76667000, 24.36667000, '2019-10-05 23:17:01', '2020-05-01 17:23:08', 1, 'Q1093127'),
(96683, 'Ursa', 4738, 'OT', 181, 'RO', 43.78783000, 24.63787000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q4302748'),
(96684, 'Urseni', 4748, 'TM', 181, 'RO', 45.69269000, 21.30993000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q827813'),
(96685, 'Urzica', 4738, 'OT', 181, 'RO', 43.85670000, 24.28952000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q2719825'),
(96686, 'Urziceanca', 4745, 'DB', 181, 'RO', 44.63052000, 25.84240000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q12164747'),
(96687, 'Urziceni', 4746, 'SM', 181, 'RO', 47.73333000, 22.40000000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q870624'),
(96688, 'Urziceni', 4743, 'IL', 181, 'RO', 44.71667000, 26.63333000, '2019-10-05 23:17:01', '2019-10-05 23:17:01', 1, 'Q870624'),
(96689, 'Urzicuţa', 4742, 'DJ', 181, 'RO', 44.01667000, 23.55000000, '2019-10-05 23:17:01', '2020-05-01 17:23:09', 1, 'Q12164745'),
(96690, 'Urși', 4757, 'VL', 181, 'RO', 44.99404000, 24.08895000, '2019-10-05 23:17:02', '2020-05-01 17:23:12', 1, 'Q12164838'),
(96691, 'Ususău', 4739, 'AR', 181, 'RO', 46.07063000, 21.81371000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q1078055'),
(96692, 'Utvin', 4748, 'TM', 181, 'RO', 45.71298000, 21.13011000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q28338'),
(96693, 'Uzunu', 4726, 'GR', 181, 'RO', 44.14716000, 25.96719000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12163575'),
(96694, 'Vad', 4734, 'CJ', 181, 'RO', 47.20000000, 23.75000000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q737750'),
(96695, 'Vadu Crişului', 4723, 'BH', 181, 'RO', 46.98333000, 22.51667000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q1093254'),
(96696, 'Vadu Izei', 4760, 'MM', 181, 'RO', 47.88821000, 23.93209000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q996521'),
(96697, 'Vadu Moldovei', 4720, 'SV', 181, 'RO', 47.38333000, 26.36667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q649272'),
(96698, 'Vadu Moţilor', 4724, 'AB', 181, 'RO', 46.40000000, 22.96667000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q250889'),
(96699, 'Vadu Paşii', 4756, 'BZ', 181, 'RO', 45.16667000, 26.90000000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12087217'),
(96700, 'Vadu Părului', 4729, 'PH', 181, 'RO', 44.93132000, 26.20940000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q12087216'),
(96701, 'Vadu Roșca', 4758, 'VN', 181, 'RO', 45.63864000, 27.45716000, '2019-10-05 23:17:02', '2020-05-01 17:23:12', 1, 'Q12087218'),
(96702, 'Vadu Sorești', 4756, 'BZ', 181, 'RO', 45.31500000, 26.87748000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12087219'),
(96703, 'Vadu Săpat', 4729, 'PH', 181, 'RO', 45.03973000, 26.38700000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q6465501'),
(96704, 'Vaida-Cămăraș', 4734, 'CJ', 181, 'RO', 46.82823000, 23.94724000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q722493'),
(96705, 'Vaideeni', 4757, 'VL', 181, 'RO', 45.16667000, 23.93333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q1833002'),
(96706, 'Valcău de Jos', 4741, 'SJ', 181, 'RO', 47.11667000, 22.73333000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q1093240'),
(96707, 'Vale', 4749, 'HR', 181, 'RO', 46.94910000, 25.37869000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q1093240'),
(96708, 'Valea Argovei', 4732, 'CL', 181, 'RO', 44.35000000, 26.78333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087402'),
(96709, 'Valea Arini', 4731, 'NT', 181, 'RO', 47.12694000, 26.29377000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087403'),
(96710, 'Valea Arinilor', 4744, 'BC', 181, 'RO', 46.49793000, 26.50918000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087405'),
(96711, 'Valea Bolvașnița', 4753, 'CS', 181, 'RO', 44.94602000, 22.39019000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q633314'),
(96712, 'Valea Borcutului', 4733, 'BN', 181, 'RO', 47.37483000, 24.65293000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087411'),
(96713, 'Valea Caselor', 4722, 'AG', 181, 'RO', 45.35000000, 25.16667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087411'),
(96714, 'Valea Chioarului', 4760, 'MM', 181, 'RO', 47.43333000, 23.48333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q1126836'),
(96715, 'Valea Ciorii', 4743, 'IL', 181, 'RO', 44.71667000, 27.56667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087661'),
(96716, 'Valea Cireșului', 4728, 'TR', 181, 'RO', 44.16699000, 25.56437000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q12087659'),
(96717, 'Valea Crişului', 4754, 'CV', 181, 'RO', 45.91667000, 25.76667000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q1096174'),
(96718, 'Valea Cucului', 4729, 'PH', 181, 'RO', 45.06525000, 26.22793000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087472'),
(96719, 'Valea Cânepii', 4736, 'BR', 181, 'RO', 45.08333000, 27.81667000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q12087460'),
(96720, 'Valea Călugărească', 4729, 'PH', 181, 'RO', 44.96667000, 26.15000000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q6465406'),
(96721, 'Valea Dacilor', 4737, 'CT', 181, 'RO', 44.19495000, 28.31776000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087445'),
(96722, 'Valea Danului', 4722, 'AG', 181, 'RO', 45.18333000, 24.65000000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2484451'),
(96723, 'Valea Dragului', 4726, 'GR', 181, 'RO', 44.21222000, 26.30361000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2717802'),
(96724, 'Valea Dulce', 4729, 'PH', 181, 'RO', 45.11428000, 26.20787000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087447'),
(96725, 'Valea Grecului', 4752, 'VS', 181, 'RO', 46.71985000, 28.10123000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087443'),
(96726, 'Valea Iaşului', 4722, 'AG', 181, 'RO', 45.18333000, 24.71667000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q12087669'),
(96727, 'Valea Ierii', 4734, 'CJ', 181, 'RO', 46.65000000, 23.35000000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q656990'),
(96728, 'Valea Leurzii', 4745, 'DB', 181, 'RO', 45.16186000, 25.48533000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087479'),
(96729, 'Valea Lungă Alba Romania', 4724, 'AB', 181, 'RO', 46.12592000, 24.04541000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q1027068'),
(96730, 'Valea Lungă Ogrea', 4745, 'DB', 181, 'RO', 45.05978000, 25.59024000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12087484'),
(96731, 'Valea Lungă-Cricov', 4745, 'DB', 181, 'RO', 45.06528000, 25.58793000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12087483'),
(96732, 'Valea Lupului', 4735, 'IS', 181, 'RO', 47.17920000, 27.49965000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2537535'),
(96733, 'Valea Mare', 4738, 'OT', 181, 'RO', 44.46667000, 24.43333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2719540'),
(96734, 'Valea Mare', 4745, 'DB', 181, 'RO', 44.78333000, 25.23333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12128509'),
(96735, 'Valea Mare', 4754, 'CV', 181, 'RO', 45.76667000, 26.00000000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q64163'),
(96736, 'Valea Mare', 4757, 'VL', 181, 'RO', 44.66667000, 24.00000000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2192326'),
(96737, 'Valea Mare', 4750, 'GJ', 181, 'RO', 45.11263000, 23.08004000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2192326'),
(96738, 'Valea Mare Pravăț', 4722, 'AG', 181, 'RO', 45.29171000, 25.09250000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q3407923'),
(96739, 'Valea Mare-Podgoria', 4722, 'AG', 181, 'RO', 44.88513000, 24.90613000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087498'),
(96740, 'Valea Merilor', 4738, 'OT', 181, 'RO', 44.47169000, 24.65611000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087530'),
(96741, 'Valea Moldovei', 4720, 'SV', 181, 'RO', 47.46667000, 26.03333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2605707'),
(96742, 'Valea Măcrișului', 4743, 'IL', 181, 'RO', 44.74379000, 26.83015000, '2019-10-05 23:17:02', '2020-05-01 17:23:10', 1, 'Q2291419'),
(96743, 'Valea Mărului', 4747, 'GL', 181, 'RO', 45.83866000, 27.69138000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q2717130'),
(96744, 'Valea Nucarilor', 4727, 'TL', 181, 'RO', 45.03333000, 28.93333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q1066301'),
(96745, 'Valea Popii', 4722, 'AG', 181, 'RO', 45.09253000, 25.02240000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087574'),
(96746, 'Valea Părului', 4728, 'TR', 181, 'RO', 43.91727000, 25.46589000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q12087563'),
(96747, 'Valea Părului', 4756, 'BZ', 181, 'RO', 45.36374000, 26.77706000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12087564'),
(96748, 'Valea Rece', 4749, 'HR', 181, 'RO', 46.62082000, 25.95827000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087564'),
(96749, 'Valea Roșie', 4732, 'CL', 181, 'RO', 44.17755000, 26.63457000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12087597'),
(96750, 'Valea Râmnicului', 4756, 'BZ', 181, 'RO', 45.36395000, 27.04197000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12087592'),
(96751, 'Valea Salciei', 4756, 'BZ', 181, 'RO', 45.49622000, 26.82361000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087604'),
(96752, 'Valea Seacă', 4746, 'SM', 181, 'RO', 48.08080000, 23.16489000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q754239'),
(96753, 'Valea Seacă', 4735, 'IS', 181, 'RO', 47.29219000, 26.66876000, '2019-10-05 23:17:02', '2020-05-01 17:23:10', 1, 'Q2719647'),
(96754, 'Valea Seacă', 4744, 'BC', 181, 'RO', 46.24281000, 27.04594000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q15929154'),
(96755, 'Valea Seacă', 4731, 'NT', 181, 'RO', 47.13944000, 26.29956000, '2019-10-05 23:17:02', '2020-05-01 17:23:10', 1, 'Q12087628'),
(96756, 'Valea Stanciului', 4742, 'DJ', 181, 'RO', 43.98333000, 23.86667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2465429'),
(96757, 'Valea Strâmbă', 4749, 'HR', 181, 'RO', 46.69429000, 25.59956000, '2019-10-05 23:17:02', '2020-05-01 17:23:10', 1, 'Q427384'),
(96758, 'Valea Sării', 4758, 'VN', 181, 'RO', 45.87686000, 26.79873000, '2019-10-05 23:17:02', '2020-05-01 17:23:12', 1, 'Q13570094'),
(96759, 'Valea Teilor', 4727, 'TL', 181, 'RO', 45.11150000, 28.48801000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q593601'),
(96760, 'Valea Târsei', 4729, 'PH', 181, 'RO', 45.17711000, 25.63323000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q593601'),
(96761, 'Valea Ursului', 4731, 'NT', 181, 'RO', 46.81667000, 27.08333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2605319'),
(96762, 'Valea Viilor', 4755, 'SB', 181, 'RO', 46.08333000, 24.28333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q1011619'),
(96763, 'Valea Vinului', 4746, 'SM', 181, 'RO', 47.71667000, 23.18333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q1188972'),
(96764, 'Valea Vișeului', 4760, 'MM', 181, 'RO', 47.91203000, 24.15842000, '2019-10-05 23:17:02', '2020-05-01 17:23:10', 1, 'Q623992'),
(96765, 'Valea Voievozilor', 4745, 'DB', 181, 'RO', 44.93805000, 25.48518000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087432'),
(96766, 'Valea lui Ion', 4744, 'BC', 181, 'RO', 46.70350000, 26.62499000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087670'),
(96767, 'Valea lui Mihai', 4723, 'BH', 181, 'RO', 47.51667000, 22.15000000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12087670'),
(96768, 'Valea Șoșii', 4744, 'BC', 181, 'RO', 46.46180000, 26.54992000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q12087668'),
(96769, 'Valu lui Traian', 4737, 'CT', 181, 'RO', 44.16667000, 28.46667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q931160'),
(96770, 'Vama', 4746, 'SM', 181, 'RO', 47.83333000, 23.40000000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q930497'),
(96771, 'Vama', 4720, 'SV', 181, 'RO', 47.56667000, 25.68333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2465898'),
(96772, 'Vama Buzăului', 4759, 'BV', 181, 'RO', 45.59668000, 25.99359000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q15281747'),
(96773, 'Variaş', 4748, 'TM', 181, 'RO', 46.01667000, 20.95000000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q1018953'),
(96774, 'Varnița', 4729, 'PH', 181, 'RO', 44.80679000, 25.94530000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q12087808'),
(96775, 'Vasilaţi', 4732, 'CL', 181, 'RO', 44.28778000, 26.44750000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12088040'),
(96776, 'Vaslui', 4752, 'VS', 181, 'RO', 46.63333000, 27.73333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12088040'),
(96777, 'Vatra Dornei', 4720, 'SV', 181, 'RO', 47.35000000, 25.36667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12088040'),
(96778, 'Vatra Moldoviţei', 4720, 'SV', 181, 'RO', 47.65000000, 25.56667000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q933684'),
(96779, 'Vaşcău', 4723, 'BH', 181, 'RO', 46.46667000, 22.46667000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q933684'),
(96780, 'Vaţa de Jos', 4721, 'HD', 181, 'RO', 46.18333000, 22.60000000, '2019-10-05 23:17:02', '2020-05-01 17:23:10', 1, 'Q12725259'),
(96781, 'Vedea', 4728, 'TR', 181, 'RO', 44.08333000, 25.06667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2721199'),
(96782, 'Vedea', 4722, 'AG', 181, 'RO', 44.78333000, 24.61667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2535261'),
(96783, 'Vedea', 4726, 'GR', 181, 'RO', 43.78333000, 25.78333000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2718428'),
(96784, 'Vela', 4742, 'DJ', 181, 'RO', 44.28333000, 23.41667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q2136806'),
(96785, 'Verbiţa', 4742, 'DJ', 181, 'RO', 44.30000000, 23.16667000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12088674'),
(96786, 'Verendin', 4753, 'CS', 181, 'RO', 45.07755000, 22.23917000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q648293'),
(96787, 'Vereşti', 4720, 'SV', 181, 'RO', 47.60000000, 26.43333000, '2019-10-05 23:17:02', '2020-05-01 17:23:11', 1, 'Q2606046'),
(96788, 'Verguleasa', 4738, 'OT', 181, 'RO', 44.65000000, 24.31667000, '2019-10-05 23:17:02', '2019-10-05 23:17:02', 1, 'Q12088682'),
(96789, 'Vermeş', 4753, 'CS', 181, 'RO', 45.52056000, 21.65972000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q75619'),
(96790, 'Vermești', 4744, 'BC', 181, 'RO', 46.42172000, 26.47660000, '2019-10-05 23:17:02', '2020-05-01 17:23:08', 1, 'Q12088746'),
(96791, 'Verneşti', 4756, 'BZ', 181, 'RO', 45.21667000, 26.73333000, '2019-10-05 23:17:02', '2020-05-01 17:23:09', 1, 'Q12088760'),
(96792, 'Verșeni', 4735, 'IS', 181, 'RO', 47.11294000, 26.64341000, '2019-10-05 23:17:02', '2020-05-01 17:23:10', 1, 'Q12088922'),
(96793, 'Verșești', 4744, 'BC', 181, 'RO', 46.44904000, 26.70183000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q12088925'),
(96794, 'Veseuș', 4724, 'AB', 181, 'RO', 46.27456000, 24.14276000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q843112'),
(96795, 'Vetiş', 4746, 'SM', 181, 'RO', 47.80000000, 22.76667000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q1188516'),
(96796, 'Vetrişoaia', 4752, 'VS', 181, 'RO', 46.43000000, 28.20833000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q2549105'),
(96797, 'Veza', 4724, 'AB', 181, 'RO', 46.16747000, 23.92470000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2549105'),
(96798, 'Veţel', 4721, 'HD', 181, 'RO', 45.90000000, 22.81667000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12089032'),
(96799, 'Veștem', 4755, 'SB', 181, 'RO', 45.71752000, 24.23958000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q3889873'),
(96800, 'Vicovu de Jos', 4720, 'SV', 181, 'RO', 47.90000000, 25.73333000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2604028'),
(96801, 'Vicovu de Sus', 4720, 'SV', 181, 'RO', 47.93333000, 25.68333000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2604028'),
(96802, 'Victor Vlad Delamarina', 4748, 'TM', 181, 'RO', 45.64056000, 21.89278000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q1084687'),
(96803, 'Victoria', 4759, 'BV', 181, 'RO', 45.72968000, 24.70280000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q1084687'),
(96804, 'Victoria', 4740, 'BT', 181, 'RO', 47.61018000, 27.16730000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12093184'),
(96805, 'Victoria', 4735, 'IS', 181, 'RO', 47.28333000, 27.58333000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2621166'),
(96806, 'Victoria', 4736, 'BR', 181, 'RO', 44.81667000, 27.61667000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2536119'),
(96807, 'Videle', 4728, 'TR', 181, 'RO', 44.27806000, 25.52444000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2536119'),
(96808, 'Vidra', 4724, 'AB', 181, 'RO', 46.36758000, 22.88811000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q920013'),
(96809, 'Vidra', 4725, 'IF', 181, 'RO', 44.26056000, 26.16972000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q3827776'),
(96810, 'Vidra', 4758, 'VN', 181, 'RO', 45.91667000, 26.90000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12725257'),
(96811, 'Vieru', 4726, 'GR', 181, 'RO', 43.88787000, 25.80974000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12093619'),
(96812, 'Vierșani', 4750, 'GJ', 181, 'RO', 44.87875000, 23.53120000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12093621'),
(96813, 'Viforâta', 4745, 'DB', 181, 'RO', 44.96011000, 25.45796000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093588'),
(96814, 'Viile', 4747, 'GL', 181, 'RO', 45.80691000, 27.94748000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12093623'),
(96815, 'Viile', 4737, 'CT', 181, 'RO', 44.16200000, 27.75769000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12093622'),
(96816, 'Viile Satu Mare', 4746, 'SM', 181, 'RO', 47.66667000, 22.95000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q1190759'),
(96817, 'Viile Tecii', 4733, 'BN', 181, 'RO', 46.93333000, 24.48333000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q753214'),
(96818, 'Viişoara', 4731, 'NT', 181, 'RO', 46.93333000, 26.23333000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12093637'),
(96819, 'Viişoara', 4752, 'VS', 181, 'RO', 46.38333000, 27.88333000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q12093628'),
(96820, 'Viişoara', 4734, 'CJ', 181, 'RO', 46.55000000, 23.91667000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q375903'),
(96821, 'Viişoara', 4740, 'BT', 181, 'RO', 48.16667000, 26.73333000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q946989'),
(96822, 'Viişoara', 4728, 'TR', 181, 'RO', 43.78333000, 25.16667000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q2721545'),
(96823, 'Viişoara', 4723, 'BH', 181, 'RO', 47.38333000, 22.45000000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q6308162'),
(96824, 'Viișoara', 4744, 'BC', 181, 'RO', 46.28325000, 26.66187000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q12093638'),
(96825, 'Viișoara', 4733, 'BN', 181, 'RO', 47.10332000, 24.44949000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q725695'),
(96826, 'Viișoara', 4745, 'DB', 181, 'RO', 44.88116000, 25.42730000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093632'),
(96827, 'Viișoara', 4758, 'VN', 181, 'RO', 46.05160000, 27.09593000, '2019-10-05 23:17:03', '2020-05-01 17:23:12', 1, 'Q12093636'),
(96828, 'Viișoara', 4737, 'CT', 181, 'RO', 44.07758000, 28.19424000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093635'),
(96829, 'Vima Mică', 4760, 'MM', 181, 'RO', 47.40000000, 23.71667000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q910148'),
(96830, 'Vinderei', 4752, 'VS', 181, 'RO', 46.15000000, 27.80000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2548666'),
(96831, 'Vinerea', 4724, 'AB', 181, 'RO', 45.88632000, 23.35289000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2548666'),
(96832, 'Vinga', 4739, 'AR', 181, 'RO', 46.01667000, 21.20000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q1066840'),
(96833, 'Vintere', 4723, 'BH', 181, 'RO', 46.76883000, 22.14652000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q725221'),
(96834, 'Vintileanca', 4756, 'BZ', 181, 'RO', 44.98500000, 26.58005000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12093401'),
(96835, 'Vintileasca', 4758, 'VN', 181, 'RO', 45.60000000, 26.73333000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12725261'),
(96836, 'Vintilă Vodă', 4756, 'BZ', 181, 'RO', 45.46667000, 26.71667000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093400'),
(96837, 'Vințu de Jos', 4724, 'AB', 181, 'RO', 45.99462000, 23.48612000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q684638'),
(96838, 'Vipereşti', 4756, 'BZ', 181, 'RO', 45.23333000, 26.46667000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093421'),
(96839, 'Viscri', 4759, 'BV', 181, 'RO', 46.05522000, 25.09389000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q935538'),
(96840, 'Vitomireşti', 4738, 'OT', 181, 'RO', 44.86667000, 24.40000000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q2300944'),
(96841, 'Vităneşti', 4728, 'TR', 181, 'RO', 44.00000000, 25.41667000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q2723941'),
(96842, 'Vitănești', 4738, 'OT', 181, 'RO', 44.52730000, 24.69585000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q12743592'),
(96843, 'Vizantea-Mânăstirească', 4758, 'VN', 181, 'RO', 45.98333000, 26.78333000, '2019-10-05 23:17:03', '2020-05-01 17:23:12', 1, 'Q12093061'),
(96844, 'Viziru', 4736, 'BR', 181, 'RO', 45.01667000, 27.70000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2303235'),
(96845, 'Vizurești', 4745, 'DB', 181, 'RO', 44.64040000, 25.80740000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093078'),
(96846, 'Vişani', 4736, 'BR', 181, 'RO', 45.15000000, 27.28333000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q3691118'),
(96847, 'Vişeu de Jos', 4760, 'MM', 181, 'RO', 47.72558000, 24.36613000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q679924'),
(96849, 'Vişina', 4745, 'DB', 181, 'RO', 44.58333000, 25.33333000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093607'),
(96850, 'Vişina', 4738, 'OT', 181, 'RO', 43.86667000, 24.45000000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q12725262'),
(96851, 'Vişineşti', 4745, 'DB', 181, 'RO', 45.10000000, 25.55000000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12093610'),
(96852, 'Vișeu de Mijloc', 4760, 'MM', 181, 'RO', 47.71562000, 24.40661000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12093610'),
(96853, 'Vișina Nouă', 4738, 'OT', 181, 'RO', 43.87434000, 24.41820000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q5117234'),
(96854, 'Viștea de Jos', 4759, 'BV', 181, 'RO', 45.79582000, 24.72380000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q679883'),
(96855, 'Vlad Ţepeş', 4732, 'CL', 181, 'RO', 44.35000000, 27.08333000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12089806'),
(96856, 'Vlad Țepeș', 4726, 'GR', 181, 'RO', 44.13282000, 26.13355000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12089807'),
(96857, 'Vladimirescu', 4739, 'AR', 181, 'RO', 46.16628000, 21.40102000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q1011092'),
(96858, 'Vlașca', 4743, 'IL', 181, 'RO', 44.39664000, 27.84865000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q1011092'),
(96859, 'Vlădaia', 4751, 'MH', 181, 'RO', 44.35000000, 23.03333000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12089881'),
(96860, 'Vlădeni', 4743, 'IL', 181, 'RO', 44.61667000, 27.88333000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q2291839'),
(96861, 'Vlădeni', 4745, 'DB', 181, 'RO', 44.87550000, 25.77318000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q3725389'),
(96862, 'Vlădeni', 4735, 'IS', 181, 'RO', 47.41667000, 27.33333000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q2719633'),
(96863, 'Vlădeni', 4740, 'BT', 181, 'RO', 47.71667000, 26.51667000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q2534419'),
(96864, 'Vlădeni-Deal', 4740, 'BT', 181, 'RO', 47.52250000, 26.87295000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q12089884'),
(96865, 'Vlădeşti', 4747, 'GL', 181, 'RO', 45.85000000, 28.08333000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q2289419'),
(96866, 'Vlădeşti', 4757, 'VL', 181, 'RO', 45.11667000, 24.30000000, '2019-10-05 23:17:03', '2020-05-01 17:23:12', 1, 'Q1820060'),
(96867, 'Vlădești', 4722, 'AG', 181, 'RO', 45.15000000, 24.91667000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q2535286'),
(96868, 'Vlădila', 4738, 'OT', 181, 'RO', 44.00000000, 24.40000000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q12089908'),
(96869, 'Vlăduleni', 4750, 'GJ', 181, 'RO', 44.88857000, 23.23727000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12089908'),
(96870, 'Vlăhiţa', 4749, 'HR', 181, 'RO', 46.35000000, 25.51667000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12089908'),
(96871, 'Vlăsceni', 4745, 'DB', 181, 'RO', 44.58522000, 25.59931000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12128588'),
(96872, 'Vlăsineşti', 4740, 'BT', 181, 'RO', 47.93333000, 26.88333000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q2715865'),
(96873, 'Voetin', 4758, 'VN', 181, 'RO', 45.44370000, 27.14587000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12090796'),
(96874, 'Voiceşti', 4757, 'VL', 181, 'RO', 44.60000000, 24.28333000, '2019-10-05 23:17:03', '2020-05-01 17:23:12', 1, 'Q3915485'),
(96875, 'Voievodeasa', 4720, 'SV', 181, 'RO', 47.79953000, 25.74939000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12090784'),
(96876, 'Voila', 4759, 'BV', 181, 'RO', 45.81839000, 24.84215000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q608864'),
(96877, 'Voineasa', 4757, 'VL', 181, 'RO', 45.41667000, 23.95000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2548640'),
(96878, 'Voineasa', 4738, 'OT', 181, 'RO', 44.28333000, 24.13333000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12090228'),
(96879, 'Voineşti', 4745, 'DB', 181, 'RO', 45.06667000, 25.25000000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12090218'),
(96880, 'Voineşti', 4752, 'VS', 181, 'RO', 46.56667000, 27.41667000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q2721387'),
(96881, 'Voineşti', 4735, 'IS', 181, 'RO', 47.06667000, 27.41667000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q2719670'),
(96882, 'Voinești', 4722, 'AG', 181, 'RO', 45.30007000, 25.07225000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q12090216'),
(96883, 'Voiteg', 4748, 'TM', 181, 'RO', 45.46889000, 21.23917000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q1022869'),
(96884, 'Voiteștii din Vale', 4750, 'GJ', 181, 'RO', 45.08746000, 23.42145000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q12090246'),
(96885, 'Voitinel', 4720, 'SV', 181, 'RO', 47.88366000, 25.75735000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2720010'),
(96886, 'Voivozi', 4723, 'BH', 181, 'RO', 47.21562000, 22.39024000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q657343'),
(96887, 'Voloiac', 4751, 'MH', 181, 'RO', 44.61667000, 23.10000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2465580'),
(96888, 'Volovăţ', 4720, 'SV', 181, 'RO', 47.81667000, 25.90000000, '2019-10-05 23:17:03', '2020-05-01 17:23:11', 1, 'Q1195081'),
(96889, 'Voluntari', 4725, 'IF', 181, 'RO', 44.49002000, 26.17338000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q1195081'),
(96890, 'Voluntari City', 4725, 'IF', 181, 'RO', 44.49023000, 26.18439000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q639520'),
(96891, 'Vorniceni', 4740, 'BT', 181, 'RO', 47.98333000, 26.66667000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2536063'),
(96892, 'Vorona', 4740, 'BT', 181, 'RO', 47.57714000, 26.63078000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2121427'),
(96893, 'Vorona Teodoru', 4740, 'BT', 181, 'RO', 47.55000000, 26.60000000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12090645'),
(96894, 'Vorţa', 4721, 'HD', 181, 'RO', 46.01667000, 22.68333000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q5065980'),
(96895, 'Voșlăbeni', 4749, 'HR', 181, 'RO', 46.65000000, 25.63333000, '2019-10-05 23:17:03', '2020-05-01 17:23:10', 1, 'Q1092183'),
(96896, 'Vrani', 4753, 'CS', 181, 'RO', 45.03833000, 21.49250000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q977930'),
(96897, 'Vrata', 4751, 'MH', 181, 'RO', 44.19197000, 22.84928000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q2357474'),
(96898, 'Vrâncioaia', 4758, 'VN', 181, 'RO', 45.85000000, 26.73333000, '2019-10-05 23:17:03', '2020-05-01 17:23:12', 1, 'Q3916053'),
(96899, 'Vrănești', 4722, 'AG', 181, 'RO', 44.84595000, 25.00716000, '2019-10-05 23:17:03', '2020-05-01 17:23:08', 1, 'Q12090832'),
(96900, 'Vulcan', 4759, 'BV', 181, 'RO', 45.63333000, 25.41667000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q3563817'),
(96901, 'Vulcan', 4721, 'HD', 181, 'RO', 45.38333000, 23.26667000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q3563817'),
(96902, 'Vulcana Băi', 4745, 'DB', 181, 'RO', 45.07982000, 25.38219000, '2019-10-05 23:17:03', '2020-05-01 17:23:09', 1, 'Q12092770'),
(96903, 'Vulcana de Sus', 4745, 'DB', 181, 'RO', 45.10125000, 25.35826000, '2019-10-05 23:17:03', '2019-10-05 23:17:03', 1, 'Q12092771'),
(96904, 'Vulcana-Pandele', 4745, 'DB', 181, 'RO', 45.01558000, 25.39319000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q12092772'),
(96905, 'Vulcăneasa', 4758, 'VN', 181, 'RO', 45.77388000, 26.90981000, '2019-10-05 23:17:04', '2020-05-01 17:23:12', 1, 'Q12092784'),
(96906, 'Vulpeni', 4738, 'OT', 181, 'RO', 44.46667000, 23.91667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q5117092'),
(96907, 'Vultureni', 4734, 'CJ', 181, 'RO', 46.96667000, 23.56667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q239519'),
(96908, 'Vultureni', 4744, 'BC', 181, 'RO', 46.36667000, 27.28333000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q15929197'),
(96909, 'Vultureşti', 4738, 'OT', 181, 'RO', 44.73333000, 24.31667000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12092796'),
(96910, 'Vultureşti', 4752, 'VS', 181, 'RO', 46.81667000, 27.53333000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q2548723'),
(96911, 'Vultureşti', 4720, 'SV', 181, 'RO', 47.51667000, 26.45000000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q1170587'),
(96912, 'Vulturești', 4722, 'AG', 181, 'RO', 45.06618000, 25.08347000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q2534657'),
(96913, 'Vulturu', 4737, 'CT', 181, 'RO', 44.65000000, 28.26667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q589501'),
(96914, 'Vulturu', 4758, 'VN', 181, 'RO', 45.61667000, 27.41667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q14738917'),
(96915, 'Vurpăr', 4724, 'AB', 181, 'RO', 46.00000000, 23.46667000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q743662'),
(96916, 'Vurpăr', 4755, 'SB', 181, 'RO', 45.90000000, 24.35000000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q1077190'),
(96917, 'Vutcani', 4752, 'VS', 181, 'RO', 46.46667000, 27.96667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q2549087'),
(96918, 'Vâlcele', 4754, 'CV', 181, 'RO', 45.85000000, 25.68333000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q10832330'),
(96919, 'Vâlcele', 4738, 'OT', 181, 'RO', 44.28263000, 24.54376000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12725277'),
(96920, 'Vâlcele', 4744, 'BC', 181, 'RO', 46.29801000, 26.60005000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q12089355'),
(96921, 'Vâlcele', 4756, 'BZ', 181, 'RO', 45.08692000, 26.63353000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12089347'),
(96922, 'Vâlcelele', 4732, 'CL', 181, 'RO', 44.38834000, 27.15022000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12089362'),
(96923, 'Vâlcelele', 4756, 'BZ', 181, 'RO', 45.34430000, 27.34937000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12743733'),
(96924, 'Vâlcelele de Sus', 4738, 'OT', 181, 'RO', 44.28461000, 24.55821000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12089358'),
(96925, 'Vâlcăneşti', 4729, 'PH', 181, 'RO', 45.11667000, 25.93333000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q6468441'),
(96926, 'Vânju-Mare', 4751, 'MH', 181, 'RO', 44.42528000, 22.86972000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q6468441'),
(96927, 'Vânjuleţ', 4751, 'MH', 181, 'RO', 44.44111000, 22.79250000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q12089406'),
(96928, 'Vânători', 4751, 'MH', 181, 'RO', 44.24278000, 22.92889000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q965720'),
(96929, 'Vânători', 4758, 'VN', 181, 'RO', 45.73333000, 27.25000000, '2019-10-05 23:17:04', '2020-05-01 17:23:12', 1, 'Q2722130'),
(96930, 'Vânători', 4735, 'IS', 181, 'RO', 47.23333000, 27.53333000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q12089401'),
(96931, 'Vânători', 4747, 'GL', 181, 'RO', 45.53333000, 28.01667000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q2291891'),
(96932, 'Vânători', 4739, 'AR', 181, 'RO', 46.62456000, 21.67332000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q250929'),
(96933, 'Vânători-Neamţ', 4731, 'NT', 181, 'RO', 47.20000000, 26.31667000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q2719573'),
(96934, 'Vânătorii Mari', 4726, 'GR', 181, 'RO', 44.48898000, 25.54548000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q12089405'),
(96935, 'Vânătorii Mici', 4726, 'GR', 181, 'RO', 44.49028000, 25.55889000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q2539316'),
(96936, 'Vârciorog', 4723, 'BH', 181, 'RO', 46.96667000, 22.30000000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q1087974'),
(96937, 'Vârciorova', 4753, 'CS', 181, 'RO', 45.32710000, 22.35208000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q1442449'),
(96938, 'Vârfu Câmpului', 4740, 'BT', 181, 'RO', 47.84566000, 26.33056000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q2716359'),
(96939, 'Vârfuri', 4745, 'DB', 181, 'RO', 45.10000000, 25.51667000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12089542'),
(96940, 'Vârfurile', 4739, 'AR', 181, 'RO', 46.31667000, 22.51667000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q1033072'),
(96941, 'Vârghiş', 4754, 'CV', 181, 'RO', 46.13333000, 25.53333000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q3563934'),
(96942, 'Vârlezi', 4747, 'GL', 181, 'RO', 45.90000000, 27.85000000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q13570124'),
(96943, 'Vârteju', 4725, 'IF', 181, 'RO', 44.35606000, 25.99183000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q13570124'),
(96944, 'Vârteşcoiu', 4758, 'VN', 181, 'RO', 45.73333000, 27.08333000, '2019-10-05 23:17:04', '2020-05-01 17:23:12', 1, 'Q12089529'),
(96945, 'Vârtoapele de Jos', 4728, 'TR', 181, 'RO', 44.18333000, 25.20000000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12089530'),
(96946, 'Vârtoapele de Sus', 4728, 'TR', 181, 'RO', 44.19182000, 25.20025000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12089532'),
(96947, 'Vârtop', 4742, 'DJ', 181, 'RO', 44.20682000, 23.34869000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q2334983'),
(96948, 'Vârvoru de Jos', 4742, 'DJ', 181, 'RO', 44.24415000, 23.60790000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q2465775'),
(96949, 'Vârşolţ', 4741, 'SJ', 181, 'RO', 47.20000000, 22.93333000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q1081416'),
(96950, 'Văculeşti', 4740, 'BT', 181, 'RO', 47.88333000, 26.41667000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q1040259'),
(96951, 'Văcăreni', 4727, 'TL', 181, 'RO', 45.32415000, 28.19512000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q848236'),
(96952, 'Văcăreşti', 4745, 'DB', 181, 'RO', 44.85000000, 25.48333000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12088182'),
(96953, 'Vădastra', 4738, 'OT', 181, 'RO', 43.86667000, 24.36667000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q14823583'),
(96954, 'Vădeni', 4736, 'BR', 181, 'RO', 45.36667000, 27.93333000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q2535722'),
(96955, 'Vădurele', 4731, 'NT', 181, 'RO', 46.72958000, 26.57173000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q12088155'),
(96956, 'Vădăstriţa', 4738, 'OT', 181, 'RO', 43.85000000, 24.33333000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q14823530'),
(96957, 'Văgiuleşti', 4750, 'GJ', 181, 'RO', 44.71667000, 23.08333000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q2718327'),
(96958, 'Vălcani', 4748, 'TM', 181, 'RO', 46.00357000, 20.39865000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q976479'),
(96959, 'Văleni', 4760, 'MM', 181, 'RO', 47.78559000, 24.01625000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q750740'),
(96960, 'Văleni', 4731, 'NT', 181, 'RO', 46.90291000, 26.38945000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q12088214'),
(96961, 'Văleni', 4738, 'OT', 181, 'RO', 44.23333000, 24.78333000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q2719284'),
(96962, 'Văleni', 4752, 'VS', 181, 'RO', 46.59278000, 28.06250000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12088216'),
(96963, 'Văleni-Dâmbovița', 4745, 'DB', 181, 'RO', 45.16667000, 25.18333000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12088201'),
(96964, 'Văleni-Podgoria', 4722, 'AG', 181, 'RO', 44.85755000, 24.98930000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q12088202'),
(96965, 'Vălenii de Munte', 4729, 'PH', 181, 'RO', 45.18333000, 26.03333000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12088202'),
(96966, 'Vălenii Șomcutei', 4760, 'MM', 181, 'RO', 47.47291000, 23.45138000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q268472'),
(96967, 'Văliug', 4753, 'CS', 181, 'RO', 45.23333000, 22.03333000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q920517'),
(96968, 'Vălişoara', 4721, 'HD', 181, 'RO', 46.05000000, 22.85000000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q5053888'),
(96969, 'Văratec', 4720, 'SV', 181, 'RO', 47.63988000, 26.40312000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12088615'),
(96970, 'Vărbila', 4729, 'PH', 181, 'RO', 45.04654000, 26.19691000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q12088673'),
(96971, 'Vărbilău', 4729, 'PH', 181, 'RO', 45.18333000, 25.95000000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q6467336'),
(96972, 'Vărădia', 4753, 'CS', 181, 'RO', 45.07833000, 21.54694000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q291013'),
(96973, 'Vărădia de Mureş', 4739, 'AR', 181, 'RO', 46.01667000, 22.15000000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q1092976'),
(96974, 'Vărăşti', 4726, 'GR', 181, 'RO', 44.23778000, 26.24861000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q2717739'),
(96975, 'Vărăști', 4732, 'CL', 181, 'RO', 44.21244000, 26.97188000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12088729'),
(96976, 'Vărşag', 4749, 'HR', 181, 'RO', 46.51667000, 25.35000000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q1075741'),
(96977, 'Vășad', 4723, 'BH', 181, 'RO', 47.51860000, 22.25660000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q722465'),
(96978, 'Zagavia', 4735, 'IS', 181, 'RO', 47.41911000, 26.89790000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q12105190'),
(96979, 'Zagon', 4754, 'CV', 181, 'RO', 45.76667000, 26.11667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q82557'),
(96980, 'Zagra', 4733, 'BN', 181, 'RO', 47.33333000, 24.28333000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q1092803'),
(96981, 'Zalha', 4741, 'SJ', 181, 'RO', 47.18333000, 23.53333000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q1191338'),
(96982, 'Zalău', 4741, 'SJ', 181, 'RO', 47.20000000, 23.05000000, '2019-10-05 23:17:04', '2020-05-01 17:23:11', 1, 'Q1191338'),
(96983, 'Zam', 4721, 'HD', 181, 'RO', 46.00000000, 22.45000000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q5062934'),
(96984, 'Zamostea', 4720, 'SV', 181, 'RO', 47.86667000, 26.20000000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q2604047'),
(96985, 'Zdrapți', 4721, 'HD', 181, 'RO', 46.15612000, 22.86954000, '2019-10-05 23:17:04', '2020-05-01 17:23:10', 1, 'Q8274402'),
(96986, 'Zebil', 4727, 'TL', 181, 'RO', 44.94600000, 28.76873000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q3623720'),
(96987, 'Zemeş', 4744, 'BC', 181, 'RO', 46.58333000, 26.41667000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q15933798'),
(96988, 'Zencani', 4749, 'HR', 181, 'RO', 46.92700000, 25.33411000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q15933798'),
(96989, 'Zerind', 4739, 'AR', 181, 'RO', 46.61667000, 21.51667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q1088047'),
(96990, 'Zetea', 4749, 'HR', 181, 'RO', 46.38333000, 25.36667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q1096335'),
(96991, 'Ziduri', 4756, 'BZ', 181, 'RO', 45.30000000, 27.08333000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q12107261'),
(96992, 'Zidurile', 4745, 'DB', 181, 'RO', 44.58176000, 25.55458000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q12107264'),
(96993, 'Zimandu Nou', 4739, 'AR', 181, 'RO', 46.28333000, 21.40000000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q1082229'),
(96994, 'Zimbor', 4741, 'SJ', 181, 'RO', 47.00000000, 23.26667000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q1088010'),
(96995, 'Zimnicea', 4728, 'TR', 181, 'RO', 43.65638000, 25.36454000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q1088010'),
(96996, 'Zimnicele', 4728, 'TR', 181, 'RO', 43.66126000, 25.41291000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q12107281'),
(96997, 'Zizin', 4759, 'BV', 181, 'RO', 45.63697000, 25.77898000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q740224'),
(96998, 'Zlatna', 4724, 'AB', 181, 'RO', 46.10633000, 23.23162000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q740224'),
(96999, 'Zlătunoaia', 4740, 'BT', 181, 'RO', 47.65026000, 27.00743000, '2019-10-05 23:17:04', '2020-05-01 17:23:08', 1, 'Q12106746'),
(97000, 'Zlătărei', 4757, 'VL', 181, 'RO', 44.63057000, 24.25788000, '2019-10-05 23:17:04', '2020-05-01 17:23:12', 1, 'Q12106746'),
(97001, 'Zmeu', 4735, 'IS', 181, 'RO', 47.19161000, 27.17917000, '2019-10-05 23:17:04', '2019-10-05 23:17:04', 1, 'Q12106771'),
(97002, 'Zorești', 4756, 'BZ', 181, 'RO', 45.18177000, 26.70291000, '2019-10-05 23:17:04', '2020-05-01 17:23:09', 1, 'Q12107073'),
(97003, 'Zorile', 4726, 'GR', 181, 'RO', 44.37864000, 25.80678000, '2019-10-05 23:17:05', '2019-10-05 23:17:05', 1, 'Q12107116'),
(97004, 'Zorleni', 4752, 'VS', 181, 'RO', 46.26667000, 27.71667000, '2019-10-05 23:17:05', '2019-10-05 23:17:05', 1, 'Q2548630'),
(97005, 'Zorlenţu Mare', 4753, 'CS', 181, 'RO', 45.45056000, 21.95611000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q75425'),
(97006, 'Zvoriştea', 4720, 'SV', 181, 'RO', 47.83333000, 26.28333000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q2464968'),
(97007, 'Zâmbreasca', 4728, 'TR', 181, 'RO', 44.31667000, 24.98333000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q2545403'),
(97008, 'Zăbala', 4754, 'CV', 181, 'RO', 45.90000000, 26.18333000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q1070545'),
(97009, 'Zăbrani', 4739, 'AR', 181, 'RO', 46.06667000, 21.55000000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q248910'),
(97010, 'Zădăreni', 4739, 'AR', 181, 'RO', 46.13274000, 21.21821000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q1190586'),
(97011, 'Zăneşti', 4731, 'NT', 181, 'RO', 46.81667000, 26.55000000, '2019-10-05 23:17:05', '2020-05-01 17:23:10', 1, 'Q2605252'),
(97012, 'Zănoaga', 4742, 'DJ', 181, 'RO', 44.15511000, 24.09832000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q12106634'),
(97013, 'Zăpodeni', 4752, 'VS', 181, 'RO', 46.75000000, 27.65000000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q2549125'),
(97014, 'Zărand', 4739, 'AR', 181, 'RO', 46.40000000, 21.65000000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q1092997'),
(97015, 'Zărneşti', 4756, 'BZ', 181, 'RO', 45.30000000, 26.86667000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q6314915'),
(97016, 'Zărnești', 4759, 'BV', 181, 'RO', 45.56093000, 25.31787000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q6314915'),
(97017, 'Zărneștii de Slănic', 4756, 'BZ', 181, 'RO', 45.27708000, 26.75549000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q12106659'),
(97018, 'Zătreni', 4757, 'VL', 181, 'RO', 44.76667000, 23.85000000, '2019-10-05 23:17:05', '2020-05-01 17:23:12', 1, 'Q2721673'),
(97019, 'Zăuan', 4741, 'SJ', 181, 'RO', 47.22495000, 22.66328000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q731483'),
(97020, 'Zăval', 4742, 'DJ', 181, 'RO', 43.84717000, 23.84208000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q12106471'),
(97021, 'Zăvoaia', 4736, 'BR', 181, 'RO', 44.95000000, 27.48333000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q12106475'),
(97022, 'Zăvoi', 4753, 'CS', 181, 'RO', 45.51667000, 22.41667000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q541487'),
(97023, 'Zăvoiu', 4745, 'DB', 181, 'RO', 44.66038000, 25.40350000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q12106480'),
(97024, 'Însurăţei', 4736, 'BR', 181, 'RO', 44.91667000, 27.60000000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q12106480'),
(97025, 'Întorsura', 4742, 'DJ', 181, 'RO', 44.11226000, 23.57574000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q2606836'),
(97026, 'Întorsura Buzăului', 4754, 'CV', 181, 'RO', 45.68333000, 26.03333000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q2606836'),
(97027, 'Şag', 4748, 'TM', 181, 'RO', 45.64861000, 21.17556000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q1069832'),
(97028, 'Şagu', 4739, 'AR', 181, 'RO', 46.05000000, 21.28333000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q396871'),
(97029, 'Şamşud', 4741, 'SJ', 181, 'RO', 47.35000000, 22.95000000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q1189668'),
(97030, 'Şandra', 4748, 'TM', 181, 'RO', 45.92500000, 20.89028000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q396872'),
(97031, 'Şaru Dornei', 4720, 'SV', 181, 'RO', 47.28333000, 25.35000000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q757074'),
(97033, 'Şeica Mică', 4755, 'SB', 181, 'RO', 46.05000000, 24.13333000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q396918'),
(97034, 'Şeitin', 4739, 'AR', 181, 'RO', 46.10000000, 20.85000000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q977560'),
(97035, 'Şelaru', 4745, 'DB', 181, 'RO', 44.47667000, 25.29997000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q2717174'),
(97036, 'Şelimbăr', 4755, 'SB', 181, 'RO', 45.76667000, 24.20000000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q14920788'),
(97037, 'Şendreni', 4747, 'GL', 181, 'RO', 45.40000000, 27.91667000, '2019-10-05 23:17:05', '2020-05-01 17:23:09', 1, 'Q2716869'),
(97038, 'Şendriceni', 4740, 'BT', 181, 'RO', 47.95171000, 26.32817000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q2716114'),
(97039, 'Şerbăneşti', 4738, 'OT', 181, 'RO', 44.33333000, 24.70000000, '2019-10-05 23:17:05', '2020-05-01 17:23:10', 1, 'Q396967'),
(97040, 'Şercaia', 4759, 'BV', 181, 'RO', 45.83333000, 25.13333000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q15273167'),
(97041, 'Şeíca Mare', 4755, 'SB', 181, 'RO', 46.01667000, 24.15000000, '2019-10-05 23:17:05', '2020-05-01 17:23:11', 1, 'Q396909'),
(97042, 'Şibot', 4724, 'AB', 181, 'RO', 45.95000000, 23.33333000, '2019-10-05 23:17:05', '2020-05-01 17:23:07', 1, 'Q13482009'),
(97043, 'Şicula', 4739, 'AR', 181, 'RO', 46.43333000, 21.75000000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q1066021'),
(97044, 'Şieu', 4733, 'BN', 181, 'RO', 47.01667000, 24.60000000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q1092990'),
(97045, 'Şieu-Măgheruş', 4733, 'BN', 181, 'RO', 47.08333000, 24.38333000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q1096399'),
(97046, 'Şieu-Odorhei', 4733, 'BN', 181, 'RO', 47.15194000, 24.29159000, '2019-10-05 23:17:05', '2020-05-01 17:23:08', 1, 'Q1093101');

