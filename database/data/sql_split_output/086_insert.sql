INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(43302, 'Le Sourn', 4807, '<PERSON><PERSON>', 75, 'FR', 48.04262000, -2.98790000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q35714344'),
(43303, 'Le Syndicat', 4820, 'GES', 75, 'FR', 48.01733000, 6.68436000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q35714344'),
(43304, '<PERSON>', 4795, 'NAQ', 75, 'FR', 44.90521000, -0.67060000, '2019-10-05 22:48:24', '2020-05-01 17:22:45', 1, 'Q1004131'),
(43305, '<PERSON>', 4795, 'NAQ', 75, 'FR', 46.62911000, -0.29979000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43306, '<PERSON> Te<PERSON>', 4795, 'NAQ', 75, 'FR', 44.63177000, -1.02155000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q174909'),
(43307, 'Le Teil', 4798, 'ARA', 75, 'FR', 44.54531000, 4.68223000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q384423'),
(43308, 'Le Teilleul', 4804, 'NOR', 75, 'FR', 48.53758000, -0.87304000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43309, 'Le Temple-de-Bretagne', 4802, 'PDL', 75, 'FR', 47.32737000, -1.78904000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '********'),
(43310, 'Le Theil-Bocage', 4804, 'NOR', 75, 'FR', 48.88333000, -0.71667000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '********'),
(43311, 'Le Theil-de-Bretagne', 4807, 'BRE', 75, 'FR', 47.91950000, -1.42975000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q213656'),
(43312, 'Le Thillay', 4796, 'IDF', 75, 'FR', 49.00659000, 2.47218000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q213656'),
(43313, 'Le Thillot', 4820, 'GES', 75, 'FR', 47.87941000, 6.76279000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q213656'),
(43314, 'Le Tholonet', 4812, 'PAC', 75, 'FR', 43.52176000, 5.51115000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q734334'),
(43315, 'Le Tholy', 4820, 'GES', 75, 'FR', 48.08229000, 6.74351000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q922980'),
(43316, 'Le Thor', 4812, 'PAC', 75, 'FR', 43.92943000, 4.99543000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q686591'),
(43317, 'Le Thoronet', 4812, 'PAC', 75, 'FR', 43.45174000, 6.30391000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q848676'),
(43318, 'Le Thou', 4795, 'NAQ', 75, 'FR', 46.08333000, -0.91667000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q35714431'),
(43319, 'Le Thuit-Signol', 4804, 'NOR', 75, 'FR', 49.26487000, 0.93910000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q35714431'),
(43320, 'Le Tignet', 4812, 'PAC', 75, 'FR', 43.63979000, 6.84625000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q35714431'),
(43321, 'Le Touquet-Paris-Plage', 4828, 'HDF', 75, 'FR', 50.52432000, 1.58571000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q323164'),
(43322, 'Le Touvet', 4798, 'ARA', 75, 'FR', 45.35909000, 5.95283000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q592837'),
(43323, 'Le Trait', 4804, 'NOR', 75, 'FR', 49.46895000, 0.81634000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q1415989'),
(43324, 'Le Tréport', 4804, 'NOR', 75, 'FR', 50.05979000, 1.37583000, '2019-10-05 22:48:24', '2020-05-01 17:22:45', 1, 'Q473875'),
(43325, 'Le Trévoux', 4807, 'BRE', 75, 'FR', 47.89478000, -3.64080000, '2019-10-05 22:48:24', '2020-05-01 17:22:44', 1, 'Q35714510'),
(43326, 'Le Val', 4812, 'PAC', 75, 'FR', 43.43928000, 6.07335000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q840082'),
(43327, 'Le Val-Saint-Germain', 4796, 'IDF', 75, 'FR', 48.56601000, 2.06471000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q1165924'),
(43328, 'Le Val-Saint-Père', 4804, 'NOR', 75, 'FR', 48.66177000, -1.37653000, '2019-10-05 22:48:24', '2020-05-01 17:22:45', 1, '*********'),
(43329, 'Le Vaudreuil', 4804, 'NOR', 75, 'FR', 49.25407000, 1.20960000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q371094'),
(43330, 'Le Verdon-sur-Mer', 4795, 'NAQ', 75, 'FR', 45.54490000, -1.06225000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q186324'),
(43331, 'Le Versoud', 4798, 'ARA', 75, 'FR', 45.21988000, 5.86502000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q186324'),
(43332, 'Le Vieil-Baugé', 4802, 'PDL', 75, 'FR', 47.53193000, -0.11888000, '2019-10-05 22:48:24', '2020-05-01 17:22:46', 1, '*********'),
(43333, 'Le Vieux-Marché', 4807, 'BRE', 75, 'FR', 48.60000000, -3.45000000, '2019-10-05 22:48:24', '2020-05-01 17:22:44', 1, '*********'),
(43334, 'Le Vigan', 4799, 'OCC', 75, 'FR', 44.74075000, 1.43963000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43335, 'Le Vigen', 4795, 'NAQ', 75, 'FR', 45.75149000, 1.28865000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43336, 'Le Vivier-sur-Mer', 4807, 'BRE', 75, 'FR', 48.60169000, -1.77303000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43337, 'Le Vésinet', 4796, 'IDF', 75, 'FR', 48.89281000, 2.13308000, '2019-10-05 22:48:24', '2020-05-01 17:22:43', 1, 'Q641607'),
(43338, 'Lecelles', 4828, 'HDF', 75, 'FR', 50.46779000, 3.40096000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q641607'),
(43339, 'Lectoure', 4799, 'OCC', 75, 'FR', 43.93464000, 0.62107000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q473919'),
(43340, 'Ledeuix', 4795, 'NAQ', 75, 'FR', 43.21667000, -0.61667000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43341, 'Leers', 4828, 'HDF', 75, 'FR', 50.68217000, 3.24429000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q32870'),
(43342, 'Leffrinckoucke', 4828, 'HDF', 75, 'FR', 51.03457000, 2.46452000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q665411'),
(43343, 'Leforest', 4828, 'HDF', 75, 'FR', 50.43866000, 3.06480000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q665411'),
(43344, 'Legé', 4802, 'PDL', 75, 'FR', 46.88484000, -1.60127000, '2019-10-05 22:48:24', '2020-05-01 17:22:46', 1, '*********'),
(43345, 'Lembach', 4820, 'GES', 75, 'FR', 49.00362000, 7.78986000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43346, 'Lemberg', 4820, 'GES', 75, 'FR', 49.00199000, 7.37889000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '*********'),
(43347, 'Lembras', 4795, 'NAQ', 75, 'FR', 44.88431000, 0.52657000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '********'),
(43348, 'Lempdes-sur-Allagnon', 4798, 'ARA', 75, 'FR', 45.38333000, 3.28333000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, '********'),
(43349, 'Lencloître', 4795, 'NAQ', 75, 'FR', 46.81622000, 0.32827000, '2019-10-05 22:48:24', '2020-05-01 17:22:45', 1, '********'),
(43350, 'Lens', 4828, 'HDF', 75, 'FR', 50.43302000, 2.82791000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q165271'),
(43351, 'Lent', 4798, 'ARA', 75, 'FR', 46.12027000, 5.19352000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q165271'),
(43352, 'Lentigny', 4798, 'ARA', 75, 'FR', 45.99391000, 3.97918000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q165271'),
(43353, 'Lentilly', 4798, 'ARA', 75, 'FR', 45.81873000, 4.66304000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q165271'),
(43354, 'Lepuix', 4825, 'BFC', 75, 'FR', 47.76050000, 6.81434000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q843636'),
(43355, 'Les Abrets', 4798, 'ARA', 75, 'FR', 45.54466000, 5.58021000, '2019-10-05 22:48:24', '2019-10-05 22:48:24', 1, 'Q843636'),
(43356, 'Les Accates', 4812, 'PAC', 75, 'FR', 43.29989000, 5.49726000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q843636'),
(43357, 'Les Ageux', 4828, 'HDF', 75, 'FR', 49.31667000, 2.60000000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q843636'),
(43358, 'Les Aix-d’Angillon', 4818, 'CVL', 75, 'FR', 47.19652000, 2.56506000, '2019-10-05 22:48:25', '2020-05-01 17:22:44', 1, 'Q843636'),
(43359, 'Les Allues', 4798, 'ARA', 75, 'FR', 45.43255000, 6.55558000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q537798'),
(43360, 'Les Alluets-le-Roi', 4796, 'IDF', 75, 'FR', 48.91379000, 1.91810000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q275088'),
(43361, 'Les Ancizes', 4798, 'ARA', 75, 'FR', 45.92524000, 2.81265000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q275088'),
(43362, 'Les Andelys', 4804, 'NOR', 75, 'FR', 49.24557000, 1.41168000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q209770'),
(43363, 'Les Angles', 4799, 'OCC', 75, 'FR', 43.95806000, 4.76342000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35715220'),
(43364, 'Les Arcs', 4812, 'PAC', 75, 'FR', 43.46328000, 6.47876000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35715220'),
(43365, 'Les Arnavaux', 4812, 'PAC', 75, 'FR', 43.33506000, 5.37969000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35715220'),
(43366, 'Les Artigues-de-Lussac', 4795, 'NAQ', 75, 'FR', 44.96667000, -0.15000000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35715228'),
(43367, 'Les Attaques', 4828, 'HDF', 75, 'FR', 50.90962000, 1.92961000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35715228'),
(43368, 'Les Authieux-sur-le-Port-Saint-Ouen', 4804, 'NOR', 75, 'FR', 49.34100000, 1.13465000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43369, 'Les Avenières', 4798, 'ARA', 75, 'FR', 45.63632000, 5.56144000, '2019-10-05 22:48:25', '2020-05-01 17:22:43', 1, '*********'),
(43370, 'Les Aygalades', 4812, 'PAC', 75, 'FR', 43.35711000, 5.37116000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43371, 'Les Baumettes', 4812, 'PAC', 75, 'FR', 43.22641000, 5.41642000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43372, 'Les Brouzils', 4802, 'PDL', 75, 'FR', 46.88571000, -1.32095000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43373, 'Les Caillols', 4812, 'PAC', 75, 'FR', 43.30221000, 5.44811000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43374, 'Les Camoins', 4812, 'PAC', 75, 'FR', 43.30607000, 5.51745000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43375, 'Les Champs-Géraux', 4807, 'BRE', 75, 'FR', 48.41647000, -1.97087000, '2019-10-05 22:48:25', '2020-05-01 17:22:44', 1, '*********'),
(43376, 'Les Chartreux', 4812, 'PAC', 75, 'FR', 43.31112000, 5.40480000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43377, 'Les Chères', 4798, 'ARA', 75, 'FR', 45.88960000, 4.74261000, '2019-10-05 22:48:25', '2020-05-01 17:22:43', 1, '*********'),
(43378, 'Les Clayes-sous-Bois', 4796, 'IDF', 75, 'FR', 48.82206000, 1.98677000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q491948'),
(43379, 'Les Clouzeaux', 4802, 'PDL', 75, 'FR', 46.62881000, -1.50947000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43380, 'Les Contamines-Montjoie', 4798, 'ARA', 75, 'FR', 45.82164000, 6.72865000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q477987'),
(43381, 'Les Crottes', 4812, 'PAC', 75, 'FR', 43.32302000, 5.36809000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q477987'),
(43382, 'Les Côtes-d\'Arey', 4798, 'ARA', 75, 'FR', 45.45640000, 4.86660000, '2019-10-05 22:48:25', '2020-05-01 17:22:43', 1, 'Q477987'),
(43383, 'Les Deux Alpes', 4798, 'ARA', 75, 'FR', 45.01160000, 6.12548000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q477987'),
(43384, 'Les Epesses', 4802, 'PDL', 75, 'FR', 46.88333000, -0.90000000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q288657'),
(43385, 'Les Essarts', 4802, 'PDL', 75, 'FR', 46.77440000, -1.22834000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43386, 'Les Essarts-le-Roi', 4796, 'IDF', 75, 'FR', 48.71673000, 1.90089000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43387, 'Les Fins', 4825, 'BFC', 75, 'FR', 47.07764000, 6.63002000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q983442'),
(43388, 'Les Forges', 4820, 'GES', 75, 'FR', 48.17747000, 6.38846000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q983442'),
(43389, 'Les Fourgs', 4825, 'BFC', 75, 'FR', 46.83455000, 6.39953000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q983442'),
(43390, 'Les Gets', 4798, 'ARA', 75, 'FR', 46.15522000, 6.66492000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q845832'),
(43391, 'Les Gonds', 4795, 'NAQ', 75, 'FR', 45.71437000, -0.61408000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43392, 'Les Grandes-Ventes', 4804, 'NOR', 75, 'FR', 49.78544000, 1.22921000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43393, 'Les Grands Carmes', 4812, 'PAC', 75, 'FR', 43.30038000, 5.37275000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43394, 'Les Hautes-Rivières', 4820, 'GES', 75, 'FR', 49.88595000, 4.84186000, '2019-10-05 22:48:25', '2020-05-01 17:22:44', 1, '*********'),
(43395, 'Les Herbiers', 4802, 'PDL', 75, 'FR', 46.86667000, -1.01667000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43396, 'Les Houches', 4798, 'ARA', 75, 'FR', 45.89267000, 6.80637000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43397, 'Les Landes-Genusson', 4802, 'PDL', 75, 'FR', 46.96667000, -1.11667000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43398, 'Les Lilas', 4796, 'IDF', 75, 'FR', 48.87992000, 2.42057000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q256004'),
(43399, 'Les Loges', 4804, 'NOR', 75, 'FR', 49.69860000, 0.28403000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q256004'),
(43400, 'Les Loges-Marchis', 4804, 'NOR', 75, 'FR', 48.54132000, -1.08754000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43401, 'Les Loges-en-Josas', 4796, 'IDF', 75, 'FR', 48.76377000, 2.14002000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q735509'),
(43402, 'Les Lucs-sur-Boulogne', 4802, 'PDL', 75, 'FR', 46.84478000, -1.49445000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43403, 'Les Mages', 4799, 'OCC', 75, 'FR', 44.22862000, 4.16946000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43404, 'Les Magnils-Reigniers', 4802, 'PDL', 75, 'FR', 46.48333000, -1.21667000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43405, 'Les Marches', 4798, 'ARA', 75, 'FR', 45.50042000, 6.00381000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q969210'),
(43406, 'Les Martres-d\'Artière', 4798, 'ARA', 75, 'FR', 45.83333000, 3.26667000, '2019-10-05 22:48:25', '2020-05-01 17:22:43', 1, 'Q969210'),
(43407, 'Les Matelles', 4799, 'OCC', 75, 'FR', 43.72905000, 3.81360000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43408, 'Les Mathes', 4795, 'NAQ', 75, 'FR', 45.71390000, -1.15497000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43409, 'Les Molières', 4796, 'IDF', 75, 'FR', 48.67306000, 2.06959000, '2019-10-05 22:48:25', '2020-05-01 17:22:43', 1, 'Q744901'),
(43410, 'Les Montils', 4818, 'CVL', 75, 'FR', 47.49499000, 1.29775000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43411, 'Les Moutiers-en-Retz', 4802, 'PDL', 75, 'FR', 47.06375000, -1.99900000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43412, 'Les Mureaux', 4796, 'IDF', 75, 'FR', 48.99173000, 1.90972000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q317433'),
(43413, 'Les Médecins', 4812, 'PAC', 75, 'FR', 43.35417000, 5.45483000, '2019-10-05 22:48:25', '2020-05-01 17:22:46', 1, 'Q317433'),
(43414, 'Les Mées', 4812, 'PAC', 75, 'FR', 44.02961000, 5.97635000, '2019-10-05 22:48:25', '2020-05-01 17:22:46', 1, 'Q317433'),
(43415, 'Les Noës-près-Troyes', 4820, 'GES', 75, 'FR', 48.30358000, 4.04552000, '2019-10-05 22:48:25', '2020-05-01 17:22:44', 1, 'Q317433'),
(43416, 'Les Olives', 4812, 'PAC', 75, 'FR', 43.32393000, 5.45840000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q317433'),
(43417, 'Les Ormes', 4795, 'NAQ', 75, 'FR', 46.97478000, 0.60484000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q317433'),
(43418, 'Les Pavillons-sous-Bois', 4796, 'IDF', 75, 'FR', 48.90683000, 2.50648000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q498043'),
(43419, 'Les Peintures', 4795, 'NAQ', 75, 'FR', 45.06667000, -0.10000000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43420, 'Les Pennes-Mirabeau', 4812, 'PAC', 75, 'FR', 43.41012000, 5.30838000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q243100'),
(43421, 'Les Pieux', 4804, 'NOR', 75, 'FR', 49.51286000, -1.80714000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43422, 'Les Ponts-de-Cé', 4802, 'PDL', 75, 'FR', 47.42315000, -0.52477000, '2019-10-05 22:48:25', '2020-05-01 17:22:46', 1, '*********'),
(43423, 'Les Riceys', 4820, 'GES', 75, 'FR', 47.99390000, 4.36986000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43424, 'Les Roches-de-Condrieu', 4798, 'ARA', 75, 'FR', 45.45281000, 4.76718000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '*********'),
(43425, 'Les Rosiers-sur-Loire', 4802, 'PDL', 75, 'FR', 47.35000000, -0.21667000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43426, 'Les Rousses', 4825, 'BFC', 75, 'FR', 46.48412000, 6.06330000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q818555'),
(43427, 'Les Sables-d’Olonne', 4802, 'PDL', 75, 'FR', 46.49645000, -1.78472000, '2019-10-05 22:48:25', '2020-05-01 17:22:46', 1, 'Q818555'),
(43428, 'Les Salles-du-Gardon', 4799, 'OCC', 75, 'FR', 44.20790000, 4.03689000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q818555'),
(43429, 'Les Sorinières', 4802, 'PDL', 75, 'FR', 47.14831000, -1.52932000, '2019-10-05 22:48:25', '2020-05-01 17:22:46', 1, '********'),
(43430, 'Les Touches', 4802, 'PDL', 75, 'FR', 47.44249000, -1.43097000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43431, 'Les Trois-Lucs', 4812, 'PAC', 75, 'FR', 43.31371000, 5.46248000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, '********'),
(43432, 'Les Ulis', 4796, 'IDF', 75, 'FR', 48.68167000, 2.16944000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q216844'),
(43433, 'Les Vans', 4798, 'ARA', 75, 'FR', 44.40522000, 4.13198000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q324022'),
(43434, 'Les Échelles', 4798, 'ARA', 75, 'FR', 45.44058000, 5.74866000, '2019-10-05 22:48:25', '2020-05-01 17:22:43', 1, 'Q324022'),
(43435, 'Les Églisottes-et-Chalaures', 4795, 'NAQ', 75, 'FR', 45.09811000, -0.03893000, '2019-10-05 22:48:25', '2020-05-01 17:22:45', 1, '*********'),
(43436, 'Lescar', 4795, 'NAQ', 75, 'FR', 43.33333000, -0.41667000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q324551'),
(43437, 'Lesneven', 4807, 'BRE', 75, 'FR', 48.57233000, -4.32133000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q274806'),
(43438, 'Lesparre-Médoc', 4795, 'NAQ', 75, 'FR', 45.30719000, -0.93764000, '2019-10-05 22:48:25', '2020-05-01 17:22:45', 1, 'Q209795'),
(43439, 'Lespignan', 4799, 'OCC', 75, 'FR', 43.27366000, 3.17224000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1148118'),
(43440, 'Lespinasse', 4799, 'OCC', 75, 'FR', 43.71206000, 1.38462000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1148118'),
(43441, 'Lesquin', 4828, 'HDF', 75, 'FR', 50.58232000, 3.11900000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q696996'),
(43442, 'Lessay', 4804, 'NOR', 75, 'FR', 49.21703000, -1.53089000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35715920'),
(43443, 'Lestelle-Bétharram', 4795, 'NAQ', 75, 'FR', 43.13333000, -0.21667000, '2019-10-05 22:48:25', '2020-05-01 17:22:45', 1, 'Q35715927'),
(43444, 'Lestrem', 4828, 'HDF', 75, 'FR', 50.61987000, 2.68646000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1026879'),
(43445, 'Leucate', 4799, 'OCC', 75, 'FR', 42.91056000, 3.02944000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q459673'),
(43446, 'Leudeville', 4796, 'IDF', 75, 'FR', 48.56591000, 2.32676000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1369659'),
(43447, 'Leuville-sur-Orge', 4796, 'IDF', 75, 'FR', 48.61730000, 2.26685000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q669593'),
(43448, 'Leval', 4828, 'HDF', 75, 'FR', 50.18106000, 3.83093000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q669593'),
(43449, 'Levallois-Perret', 4796, 'IDF', 75, 'FR', 48.89389000, 2.28864000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q166640'),
(43450, 'Levens', 4812, 'PAC', 75, 'FR', 43.85948000, 7.22583000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q271337'),
(43451, 'Levet', 4818, 'CVL', 75, 'FR', 46.92605000, 2.40732000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q271337'),
(43452, 'Levier', 4825, 'BFC', 75, 'FR', 46.95302000, 6.12059000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q271337'),
(43453, 'Levroux', 4818, 'CVL', 75, 'FR', 46.97860000, 1.61243000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q628265'),
(43454, 'Lewarde', 4828, 'HDF', 75, 'FR', 50.34173000, 3.16782000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q644716'),
(43455, 'Lexy', 4820, 'GES', 75, 'FR', 49.49996000, 5.72985000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q644716'),
(43456, 'Leyme', 4799, 'OCC', 75, 'FR', 44.78622000, 1.89897000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q644716'),
(43457, 'Leymen', 4820, 'GES', 75, 'FR', 47.49632000, 7.48517000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q644716'),
(43458, 'Leyment', 4798, 'ARA', 75, 'FR', 45.92390000, 5.29241000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q644716'),
(43459, 'Lezay', 4795, 'NAQ', 75, 'FR', 46.26437000, -0.00925000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q644716'),
(43460, 'Lezennes', 4828, 'HDF', 75, 'FR', 50.61553000, 3.11354000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q658561'),
(43461, 'Lezoux', 4798, 'ARA', 75, 'FR', 45.82689000, 3.37924000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1221375'),
(43462, 'Lherm', 4799, 'OCC', 75, 'FR', 43.43127000, 1.22239000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35716593'),
(43463, 'Liancourt', 4828, 'HDF', 75, 'FR', 49.33034000, 2.46595000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q832372'),
(43464, 'Libercourt', 4828, 'HDF', 75, 'FR', 50.48322000, 3.01584000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q471100'),
(43465, 'Libourne', 4795, 'NAQ', 75, 'FR', 44.91530000, -0.24394000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q6544'),
(43466, 'Licques', 4828, 'HDF', 75, 'FR', 50.78416000, 1.93844000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1029747'),
(43467, 'Liergues', 4798, 'ARA', 75, 'FR', 45.97138000, 4.66231000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1029747'),
(43468, 'Liesse-Notre-Dame', 4828, 'HDF', 75, 'FR', 49.61667000, 3.80000000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q35716886'),
(43469, 'Lieu-Saint-Amand', 4828, 'HDF', 75, 'FR', 50.27318000, 3.34624000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q1165164'),
(43470, 'Lieuran-lès-Béziers', 4799, 'OCC', 75, 'FR', 43.41860000, 3.23719000, '2019-10-05 22:48:25', '2020-05-01 17:22:46', 1, 'Q1165164'),
(43471, 'Lieurey', 4804, 'NOR', 75, 'FR', 49.23019000, 0.49879000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q625434'),
(43472, 'Lieusaint', 4796, 'IDF', 75, 'FR', 48.63476000, 2.54806000, '2019-10-05 22:48:25', '2019-10-05 22:48:25', 1, 'Q625434'),
(43473, 'Liffol-le-Grand', 4820, 'GES', 75, 'FR', 48.31771000, 5.58125000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q625434'),
(43474, 'Liffré', 4807, 'BRE', 75, 'FR', 48.21324000, -1.50838000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q1011452'),
(43475, 'Lignan-sur-Orb', 4799, 'OCC', 75, 'FR', 43.38462000, 3.16891000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q1011452'),
(43476, 'Lignières', 4818, 'CVL', 75, 'FR', 46.75152000, 2.17381000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q35716960'),
(43477, 'Ligny-en-Barrois', 4820, 'GES', 75, 'FR', 48.68861000, 5.32543000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q917765'),
(43478, 'Ligny-en-Cambrésis', 4828, 'HDF', 75, 'FR', 50.10123000, 3.37841000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, '*********'),
(43479, 'Ligny-le-Châtel', 4825, 'BFC', 75, 'FR', 47.90022000, 3.75760000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, '*********'),
(43480, 'Ligny-le-Ribault', 4818, 'CVL', 75, 'FR', 47.68419000, 1.78153000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43481, 'Ligné', 4802, 'PDL', 75, 'FR', 47.41096000, -1.37726000, '2019-10-05 22:48:26', '2020-05-01 17:22:46', 1, 'Q632139'),
(43482, 'Ligré', 4818, 'CVL', 75, 'FR', 47.11185000, 0.27562000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q632139'),
(43483, 'Ligueil', 4818, 'CVL', 75, 'FR', 47.04210000, 0.81893000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q632139'),
(43484, 'Ligugé', 4795, 'NAQ', 75, 'FR', 46.52035000, 0.32617000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, '********'),
(43485, 'Lille', 4828, 'HDF', 75, 'FR', 50.63297000, 3.05858000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q648'),
(43486, 'Lillebonne', 4804, 'NOR', 75, 'FR', 49.52030000, 0.53617000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q837563'),
(43487, 'Lillers', 4828, 'HDF', 75, 'FR', 50.56345000, 2.48042000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q383388'),
(43488, 'Limas', 4798, 'ARA', 75, 'FR', 45.97558000, 4.70550000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q383388'),
(43489, 'Limay', 4796, 'IDF', 75, 'FR', 48.99553000, 1.74081000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q383388'),
(43490, 'Limeil-Brévannes', 4796, 'IDF', 75, 'FR', 48.74480000, 2.48705000, '2019-10-05 22:48:26', '2020-05-01 17:22:43', 1, 'Q549536'),
(43491, 'Limerzel', 4807, 'BRE', 75, 'FR', 47.63619000, -2.35448000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35717169'),
(43492, 'Limetz-Villez', 4796, 'IDF', 75, 'FR', 49.06667000, 1.55000000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35717185'),
(43493, 'Limoges', 4795, 'NAQ', 75, 'FR', 45.83153000, 1.25781000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q45656'),
(43494, 'Limonest', 4798, 'ARA', 75, 'FR', 45.83702000, 4.77188000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q45656'),
(43495, 'Limours', 4796, 'IDF', 75, 'FR', 48.64625000, 2.07688000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q752189'),
(43496, 'Limoux', 4799, 'OCC', 75, 'FR', 43.05487000, 2.22173000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q192744'),
(43497, 'Limésy', 4804, 'NOR', 75, 'FR', 49.61271000, 0.92483000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, 'Q192744'),
(43498, 'Linards', 4795, 'NAQ', 75, 'FR', 45.70083000, 1.53259000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q192744'),
(43499, 'Linars', 4795, 'NAQ', 75, 'FR', 45.65000000, 0.08333000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q192744'),
(43500, 'Linas', 4796, 'IDF', 75, 'FR', 48.63041000, 2.26266000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q192744'),
(43501, 'Lingolsheim', 4820, 'GES', 75, 'FR', 48.55752000, 7.68253000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q21388'),
(43502, 'Linguizzetta', 4806, '20R', 75, 'FR', 42.26384000, 9.47297000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35717346'),
(43503, 'Linselles', 4828, 'HDF', 75, 'FR', 50.73708000, 3.07878000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q656403'),
(43504, 'Linxe', 4795, 'NAQ', 75, 'FR', 43.91984000, -1.24619000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43505, 'Lion-sur-Mer', 4804, 'NOR', 75, 'FR', 49.30018000, -0.32157000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q366420'),
(43506, 'Lipsheim', 4820, 'GES', 75, 'FR', 48.49164000, 7.66751000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q366420'),
(43507, 'Liré', 4802, 'PDL', 75, 'FR', 47.34322000, -1.16536000, '2019-10-05 22:48:26', '2020-05-01 17:22:46', 1, '*********'),
(43508, 'Lisieux', 4804, 'NOR', 75, 'FR', 49.14660000, 0.22925000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q188743'),
(43509, 'Lisle-sur-Tarn', 4799, 'OCC', 75, 'FR', 43.85249000, 1.81099000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q188743'),
(43510, 'Lisses', 4796, 'IDF', 75, 'FR', 48.60222000, 2.42245000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q248497'),
(43511, 'Lissieu', 4798, 'ARA', 75, 'FR', 45.86487000, 4.74221000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q248497'),
(43512, 'Listrac-Médoc', 4795, 'NAQ', 75, 'FR', 45.07410000, -0.79132000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, '*********'),
(43513, 'Lit-et-Mixe', 4795, 'NAQ', 75, 'FR', 44.03308000, -1.25330000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43514, 'Livarot', 4804, 'NOR', 75, 'FR', 49.00500000, 0.15020000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q366389'),
(43515, 'Liverdun', 4820, 'GES', 75, 'FR', 48.74973000, 6.06372000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q903665'),
(43516, 'Liverdy-en-Brie', 4796, 'IDF', 75, 'FR', 48.69987000, 2.77606000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q903665'),
(43517, 'Livet-et-Gavet', 4798, 'ARA', 75, 'FR', 45.10782000, 5.93333000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q964062'),
(43518, 'Livinhac-le-Haut', 4799, 'OCC', 75, 'FR', 44.59212000, 2.23117000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q964062'),
(43519, 'Livron-sur-Drôme', 4798, 'ARA', 75, 'FR', 44.77689000, 4.84180000, '2019-10-05 22:48:26', '2020-05-01 17:22:43', 1, 'Q468227'),
(43520, 'Livry-Gargan', 4796, 'IDF', 75, 'FR', 48.91930000, 2.54305000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q180005'),
(43521, 'Livry-sur-Seine', 4796, 'IDF', 75, 'FR', 48.51771000, 2.67879000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q180005'),
(43522, 'Livré-sur-Changeon', 4807, 'BRE', 75, 'FR', 48.21937000, -1.34438000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q223150'),
(43523, 'Lizy-sur-Ourcq', 4796, 'IDF', 75, 'FR', 49.02454000, 3.02178000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q262081'),
(43524, 'Lièpvre', 4820, 'GES', 75, 'FR', 48.27166000, 7.28229000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q124107'),
(43525, 'Liévin', 4828, 'HDF', 75, 'FR', 50.41980000, 2.78068000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, 'Q217841'),
(43526, 'Llupia', 4799, 'OCC', 75, 'FR', 42.62074000, 2.76924000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q217841'),
(43527, 'Loches', 4818, 'CVL', 75, 'FR', 47.12858000, 0.99522000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q213349'),
(43528, 'Locmariaquer', 4807, 'BRE', 75, 'FR', 47.56915000, -2.94468000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35718696'),
(43529, 'Locminé', 4807, 'BRE', 75, 'FR', 47.88619000, -2.83536000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q127163'),
(43530, 'Locmiquélic', 4807, 'BRE', 75, 'FR', 47.72499000, -3.34153000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q35718710'),
(43531, 'Locoal-Mendon', 4807, 'BRE', 75, 'FR', 47.70520000, -3.10796000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35718732'),
(43532, 'Locon', 4828, 'HDF', 75, 'FR', 50.57029000, 2.66629000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q1025308'),
(43533, 'Locquirec', 4807, 'BRE', 75, 'FR', 48.69243000, -3.64554000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35718738'),
(43534, 'Locquémeau', 4807, 'BRE', 75, 'FR', 48.72474000, -3.56275000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q35718744'),
(43535, 'Loctudy', 4807, 'BRE', 75, 'FR', 47.83333000, -4.16667000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43536, 'Lodi', 4812, 'PAC', 75, 'FR', 43.28663000, 5.38882000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43537, 'Lodève', 4799, 'OCC', 75, 'FR', 43.73166000, 3.31941000, '2019-10-05 22:48:26', '2020-05-01 17:22:46', 1, 'Q198650'),
(43538, 'Lognes', 4796, 'IDF', 75, 'FR', 48.83541000, 2.62998000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q268281'),
(43539, 'Logonna-Daoulas', 4807, 'BRE', 75, 'FR', 48.32308000, -4.29675000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43540, 'Loire-Atlantique', 4802, 'PDL', 75, 'FR', 47.32863000, -1.65764000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q3068'),
(43541, 'Loire-sur-Rhône', 4798, 'ARA', 75, 'FR', 45.55000000, 4.80000000, '2019-10-05 22:48:26', '2020-05-01 17:22:43', 1, '*********'),
(43542, 'Loiret', 4818, 'CVL', 75, 'FR', 47.93598000, 2.30173000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q12574'),
(43543, 'Loiron', 4802, 'PDL', 75, 'FR', 48.06667000, -0.93333000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43544, 'Loisin', 4798, 'ARA', 75, 'FR', 46.29227000, 6.31010000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43545, 'Loison-sous-Lens', 4828, 'HDF', 75, 'FR', 50.43796000, 2.85322000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43546, 'Loivre', 4820, 'GES', 75, 'FR', 49.34619000, 3.97978000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43547, 'Lombez', 4799, 'OCC', 75, 'FR', 43.47501000, 0.91119000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43548, 'Lombron', 4802, 'PDL', 75, 'FR', 48.07879000, 0.41869000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '*********'),
(43549, 'Lomme', 4828, 'HDF', 75, 'FR', 50.64358000, 2.98715000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q590292'),
(43550, 'Lompret', 4828, 'HDF', 75, 'FR', 50.66931000, 2.98968000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q590292'),
(43551, 'Londinières', 4804, 'NOR', 75, 'FR', 49.83187000, 1.40232000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, 'Q590292'),
(43552, 'Longages', 4799, 'OCC', 75, 'FR', 43.35398000, 1.23905000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q590292'),
(43553, 'Longchamp', 4825, 'BFC', 75, 'FR', 47.25973000, 5.28694000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q590292'),
(43554, 'Longchaumois', 4825, 'BFC', 75, 'FR', 46.46228000, 5.93052000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q590292'),
(43555, 'Longecourt-en-Plaine', 4825, 'BFC', 75, 'FR', 47.19783000, 5.14956000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q47704'),
(43556, 'Longeville-en-Barrois', 4820, 'GES', 75, 'FR', 48.74242000, 5.20905000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q47704'),
(43557, 'Longeville-lès-Metz', 4820, 'GES', 75, 'FR', 49.11403000, 6.13612000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, 'Q22259'),
(43558, 'Longeville-lès-Saint-Avold', 4820, 'GES', 75, 'FR', 49.11689000, 6.64327000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, 'Q22259'),
(43559, 'Longeville-sur-Mer', 4802, 'PDL', 75, 'FR', 46.41667000, -1.50000000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '********'),
(43560, 'Longfossé', 4828, 'HDF', 75, 'FR', 50.65217000, 1.80693000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, '********'),
(43561, 'Longjumeau', 4796, 'IDF', 75, 'FR', 48.69503000, 2.30735000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q219045'),
(43562, 'Longlaville', 4820, 'GES', 75, 'FR', 49.53443000, 5.80048000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q219045'),
(43563, 'Longnes', 4796, 'IDF', 75, 'FR', 48.92002000, 1.58705000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q219045'),
(43564, 'Longny-au-Perche', 4804, 'NOR', 75, 'FR', 48.52984000, 0.75239000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '********'),
(43565, 'Longperrier', 4796, 'IDF', 75, 'FR', 49.04844000, 2.66571000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, '********'),
(43566, 'Longpont-sur-Orge', 4796, 'IDF', 75, 'FR', 48.64171000, 2.29278000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q247627'),
(43567, 'Longpré-les-Corps-Saints', 4828, 'HDF', 75, 'FR', 50.01239000, 1.99287000, '2019-10-05 22:48:26', '2020-05-01 17:22:45', 1, 'Q314589'),
(43568, 'Longueau', 4828, 'HDF', 75, 'FR', 49.87226000, 2.35880000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q313538'),
(43569, 'Longueil-Annel', 4828, 'HDF', 75, 'FR', 49.46908000, 2.86464000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q1338650'),
(43570, 'Longueil-Sainte-Marie', 4828, 'HDF', 75, 'FR', 49.35723000, 2.71844000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q633884'),
(43571, 'Longuenesse', 4828, 'HDF', 75, 'FR', 50.73395000, 2.23520000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q245186'),
(43572, 'Longueville', 4796, 'IDF', 75, 'FR', 48.51503000, 3.24677000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q245186'),
(43573, 'Longuyon', 4820, 'GES', 75, 'FR', 49.44181000, 5.60508000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q659931'),
(43574, 'Longvic', 4825, 'BFC', 75, 'FR', 47.28754000, 5.06341000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q20321'),
(43575, 'Longwy', 4820, 'GES', 75, 'FR', 49.52170000, 5.76192000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q318535'),
(43576, 'Lons', 4795, 'NAQ', 75, 'FR', 43.31667000, -0.40000000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35719226'),
(43577, 'Lons-le-Saunier', 4825, 'BFC', 75, 'FR', 46.67535000, 5.55575000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q203529'),
(43578, 'Looberghe', 4828, 'HDF', 75, 'FR', 50.91694000, 2.27439000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q203529'),
(43579, 'Loon-Plage', 4828, 'HDF', 75, 'FR', 50.99647000, 2.21770000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q203529'),
(43580, 'Loos', 4828, 'HDF', 75, 'FR', 50.60982000, 3.01874000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q203529'),
(43581, 'Loos-en-Gohelle', 4828, 'HDF', 75, 'FR', 50.45786000, 2.79215000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q323727'),
(43582, 'Loperhet', 4807, 'BRE', 75, 'FR', 48.37725000, -4.30608000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35719274'),
(43583, 'Lopérec', 4807, 'BRE', 75, 'FR', 48.27734000, -4.04756000, '2019-10-05 22:48:26', '2020-05-01 17:22:44', 1, 'Q35719295'),
(43584, 'Lorette', 4798, 'ARA', 75, 'FR', 45.51105000, 4.58242000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35719295'),
(43585, 'Lorgies', 4828, 'HDF', 75, 'FR', 50.56880000, 2.79034000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q35719295'),
(43586, 'Lorgues', 4812, 'PAC', 75, 'FR', 43.49325000, 6.36150000, '2019-10-05 22:48:26', '2019-10-05 22:48:26', 1, 'Q733009'),
(43587, 'Lorient', 4807, 'BRE', 75, 'FR', 47.74589000, -3.36643000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q71724'),
(43588, 'Loriol-du-Comtat', 4812, 'PAC', 75, 'FR', 44.07653000, 5.00065000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q1019124'),
(43589, 'Loriol-sur-Drôme', 4798, 'ARA', 75, 'FR', 44.74703000, 4.81719000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q1019124'),
(43590, 'Lormaison', 4828, 'HDF', 75, 'FR', 49.25647000, 2.10575000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q1019124'),
(43591, 'Lormes', 4825, 'BFC', 75, 'FR', 47.28964000, 3.81714000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q633357'),
(43592, 'Lormont', 4795, 'NAQ', 75, 'FR', 44.87495000, -0.51782000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q235300'),
(43593, 'Lorquin', 4820, 'GES', 75, 'FR', 48.67102000, 6.99915000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q235300'),
(43594, 'Lorrez-le-Bocage-Préaux', 4796, 'IDF', 75, 'FR', 48.23333000, 2.90000000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q1459990'),
(43595, 'Lorris', 4818, 'CVL', 75, 'FR', 47.88950000, 2.51478000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q1168135'),
(43596, 'Losne', 4825, 'BFC', 75, 'FR', 47.09850000, 5.26216000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q1168135'),
(43597, 'Lot', 4799, 'OCC', 75, 'FR', 44.62703000, 1.63461000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q12576'),
(43598, 'Louannec', 4807, 'BRE', 75, 'FR', 48.79423000, -3.41200000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q35719500'),
(43599, 'Louargat', 4807, 'BRE', 75, 'FR', 48.56576000, -3.33750000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q1014743'),
(43600, 'Loubert', 4795, 'NAQ', 75, 'FR', 45.91422000, 0.58617000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q35719522'),
(43601, 'Loudun', 4795, 'NAQ', 75, 'FR', 47.00788000, 0.08296000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q829358'),
(43602, 'Loudéac', 4807, 'BRE', 75, 'FR', 48.17826000, -2.75433000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, '*********'),
(43603, 'Louhans', 4825, 'BFC', 75, 'FR', 46.62637000, 5.22468000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q460787'),
(43604, 'Loupiac', 4795, 'NAQ', 75, 'FR', 44.61667000, -0.30000000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43605, 'Loupian', 4799, 'OCC', 75, 'FR', 43.44886000, 3.61381000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q202620'),
(43606, 'Louplande', 4802, 'PDL', 75, 'FR', 47.95000000, -0.05000000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q202620'),
(43607, 'Lourches', 4828, 'HDF', 75, 'FR', 50.31354000, 3.35258000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q202620'),
(43608, 'Lourdes', 4799, 'OCC', 75, 'FR', 43.10000000, -0.05000000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q3327'),
(43609, 'Lourmarin', 4812, 'PAC', 75, 'FR', 43.76385000, 5.36264000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q458748'),
(43610, 'Loury', 4818, 'CVL', 75, 'FR', 47.99944000, 2.08474000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q169343'),
(43611, 'Louveciennes', 4796, 'IDF', 75, 'FR', 48.86115000, 2.11463000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q762133'),
(43612, 'Louverné', 4802, 'PDL', 75, 'FR', 48.12178000, -0.71721000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, '*********'),
(43613, 'Louvie-Juzon', 4795, 'NAQ', 75, 'FR', 43.08646000, -0.41928000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q179756'),
(43614, 'Louviers', 4804, 'NOR', 75, 'FR', 49.21667000, 1.16667000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q474480'),
(43615, 'Louvigny', 4804, 'NOR', 75, 'FR', 49.15715000, -0.39376000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q474480'),
(43616, 'Louvigné-de-Bais', 4807, 'BRE', 75, 'FR', 48.04898000, -1.33091000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, 'Q549380'),
(43617, 'Louvigné-du-Désert', 4807, 'BRE', 75, 'FR', 48.48047000, -1.12537000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, '*********'),
(43618, 'Louvres', 4796, 'IDF', 75, 'FR', 49.04460000, 2.50479000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q908517'),
(43619, 'Louvroil', 4828, 'HDF', 75, 'FR', 50.26427000, 3.96272000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q908517'),
(43620, 'Louzy', 4795, 'NAQ', 75, 'FR', 47.01201000, -0.18537000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43621, 'Loué', 4802, 'PDL', 75, 'FR', 47.99575000, -0.15450000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, '*********'),
(43622, 'Lovagny', 4798, 'ARA', 75, 'FR', 45.90377000, 6.03281000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43623, 'Loyat', 4807, 'BRE', 75, 'FR', 47.98933000, -2.38344000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43624, 'Loyettes', 4798, 'ARA', 75, 'FR', 45.77663000, 5.20687000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q648628'),
(43625, 'Lozanne', 4798, 'ARA', 75, 'FR', 45.85733000, 4.68070000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q648628'),
(43626, 'Lozinghem', 4828, 'HDF', 75, 'FR', 50.51554000, 2.50209000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q648628'),
(43627, 'Luant', 4818, 'CVL', 75, 'FR', 46.73300000, 1.55793000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q648628'),
(43628, 'Lubersac', 4795, 'NAQ', 75, 'FR', 45.44474000, 1.40457000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '********'),
(43629, 'Luc-la-Primaube', 4799, 'OCC', 75, 'FR', 44.31439000, 2.53552000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43630, 'Luc-sur-Mer', 4804, 'NOR', 75, 'FR', 49.31452000, -0.35499000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q865397'),
(43631, 'Lucciana', 4806, '20R', 75, 'FR', 42.54609000, 9.41865000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43632, 'Luceau', 4802, 'PDL', 75, 'FR', 47.71167000, 0.39734000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43633, 'Lucenay', 4798, 'ARA', 75, 'FR', 45.91274000, 4.70287000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43634, 'Lucenay-lès-Aix', 4825, 'BFC', 75, 'FR', 46.70274000, 3.48308000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, '********'),
(43635, 'Luché-Pringé', 4802, 'PDL', 75, 'FR', 47.70326000, 0.07549000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q735739'),
(43636, 'Lucinges', 4798, 'ARA', 75, 'FR', 46.19181000, 6.31511000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q735739'),
(43637, 'Lucq-de-Béarn', 4795, 'NAQ', 75, 'FR', 43.30000000, -0.66667000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, '*********'),
(43638, 'Lucé', 4818, 'CVL', 75, 'FR', 48.43689000, 1.46359000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, '*********'),
(43639, 'Lucéram', 4812, 'PAC', 75, 'FR', 43.88293000, 7.35988000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q866296'),
(43640, 'Ludon-Médoc', 4795, 'NAQ', 75, 'FR', 44.98118000, -0.60254000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, 'Q175512'),
(43641, 'Ludres', 4820, 'GES', 75, 'FR', 48.62032000, 6.16747000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q175512'),
(43642, 'Lugon-et-l\'Île-du-Carnay', 4795, 'NAQ', 75, 'FR', 44.95556000, -0.33611000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, 'Q35720908'),
(43643, 'Lugrin', 4798, 'ARA', 75, 'FR', 46.40208000, 6.65283000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q639596'),
(43644, 'Luisant', 4818, 'CVL', 75, 'FR', 48.42950000, 1.47383000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q639596'),
(43645, 'Luitré', 4807, 'BRE', 75, 'FR', 48.28247000, -1.11866000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, 'Q35720971'),
(43646, 'Lumbin', 4798, 'ARA', 75, 'FR', 45.30954000, 5.91505000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q35720971'),
(43647, 'Lumbres', 4828, 'HDF', 75, 'FR', 50.70693000, 2.12081000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q261414'),
(43648, 'Lumes', 4820, 'GES', 75, 'FR', 49.73461000, 4.78616000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q261414'),
(43649, 'Lumigny-Nesles-Ormeaux', 4796, 'IDF', 75, 'FR', 48.73333000, 2.95000000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q35721062'),
(43650, 'Lumio', 4806, '20R', 75, 'FR', 42.57894000, 8.83373000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q246410'),
(43651, 'Lunay', 4818, 'CVL', 75, 'FR', 47.80864000, 0.91499000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q246410'),
(43652, 'Lunel', 4799, 'OCC', 75, 'FR', 43.67778000, 4.13611000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q246410'),
(43653, 'Lunel-Viel', 4799, 'OCC', 75, 'FR', 43.67890000, 4.09250000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q246410'),
(43654, 'Luneray', 4804, 'NOR', 75, 'FR', 49.82795000, 0.91581000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q246410'),
(43655, 'Lunery', 4818, 'CVL', 75, 'FR', 46.93569000, 2.26895000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q1173494'),
(43656, 'Lunéville', 4820, 'GES', 75, 'FR', 48.59273000, 6.49383000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, 'Q208794'),
(43657, 'Luray', 4818, 'CVL', 75, 'FR', 48.72035000, 1.39889000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q208794'),
(43658, 'Lurcy-Lévis', 4798, 'ARA', 75, 'FR', 46.72981000, 2.93829000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q271996'),
(43659, 'Lure', 4825, 'BFC', 75, 'FR', 47.68294000, 6.49658000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q271996'),
(43660, 'Lusanger', 4802, 'PDL', 75, 'FR', 47.68153000, -1.58857000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '********'),
(43661, 'Lusignan', 4795, 'NAQ', 75, 'FR', 46.43598000, 0.12620000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '********'),
(43662, 'Lusigny', 4798, 'ARA', 75, 'FR', 46.58776000, 3.49142000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '********'),
(43663, 'Lusigny-sur-Barse', 4820, 'GES', 75, 'FR', 48.25451000, 4.26891000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '********'),
(43664, 'Lussac', 4795, 'NAQ', 75, 'FR', 44.95000000, -0.10000000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43665, 'Lussac-les-Châteaux', 4795, 'NAQ', 75, 'FR', 46.40327000, 0.72524000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, '********'),
(43666, 'Lutterbach', 4820, 'GES', 75, 'FR', 47.75976000, 7.28032000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q124172'),
(43667, 'Lutzelhouse', 4820, 'GES', 75, 'FR', 48.52114000, 7.28700000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q21536'),
(43668, 'Luxeuil-les-Bains', 4825, 'BFC', 75, 'FR', 47.81710000, 6.36500000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q634124'),
(43669, 'Luynes', 4818, 'CVL', 75, 'FR', 47.38441000, 0.55470000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q634124'),
(43670, 'Luz-Saint-Sauveur', 4799, 'OCC', 75, 'FR', 42.87191000, -0.00323000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q35721506'),
(43671, 'Luzarches', 4796, 'IDF', 75, 'FR', 49.11319000, 2.42230000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q145382'),
(43672, 'Luzech', 4799, 'OCC', 75, 'FR', 44.47818000, 1.28704000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q749077'),
(43673, 'Luzinay', 4798, 'ARA', 75, 'FR', 45.58996000, 4.95891000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q749077'),
(43674, 'Luzy', 4825, 'BFC', 75, 'FR', 46.79011000, 3.97036000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q749077'),
(43675, 'Luçay-le-Mâle', 4818, 'CVL', 75, 'FR', 47.12996000, 1.44173000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, '********'),
(43676, 'Luçon', 4802, 'PDL', 75, 'FR', 46.45773000, -1.16512000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, '*********'),
(43677, 'Lyaud', 4798, 'ARA', 75, 'FR', 46.33972000, 6.52595000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q226028'),
(43678, 'Lyon', 4798, 'ARA', 75, 'FR', 45.74846000, 4.84671000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q456'),
(43679, 'Lys-lez-Lannoy', 4828, 'HDF', 75, 'FR', 50.66667000, 3.21667000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q303038'),
(43680, 'Lèves', 4818, 'CVL', 75, 'FR', 48.47065000, 1.48194000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, 'Q303038'),
(43681, 'Lécluse', 4828, 'HDF', 75, 'FR', 50.27671000, 3.04045000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, 'Q303038'),
(43682, 'Lécousse', 4807, 'BRE', 75, 'FR', 48.36458000, -1.21289000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, '*********'),
(43683, 'Lédenon', 4799, 'OCC', 75, 'FR', 43.91349000, 4.51444000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, '*********'),
(43684, 'Lédignan', 4799, 'OCC', 75, 'FR', 43.98690000, 4.10644000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q532580'),
(43685, 'Léguevin', 4799, 'OCC', 75, 'FR', 43.60028000, 1.23236000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q1111289'),
(43686, 'Léhon', 4807, 'BRE', 75, 'FR', 48.44432000, -2.04578000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, 'Q207790'),
(43687, 'Léognan', 4795, 'NAQ', 75, 'FR', 44.73548000, -0.59738000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, 'Q478229'),
(43688, 'Léon', 4795, 'NAQ', 75, 'FR', 43.87676000, -1.30057000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, 'Q35721848'),
(43689, 'Lérouville', 4820, 'GES', 75, 'FR', 48.79261000, 5.54063000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, 'Q35721848'),
(43690, 'Léry', 4804, 'NOR', 75, 'FR', 49.28585000, 1.20768000, '2019-10-05 22:48:27', '2020-05-01 17:22:45', 1, 'Q35721848'),
(43691, 'Léré', 4818, 'CVL', 75, 'FR', 47.46867000, 2.86981000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, 'Q35721848'),
(43692, 'Lésigny', 4796, 'IDF', 75, 'FR', 48.74374000, 2.61518000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q35721848'),
(43693, 'Lévignac', 4799, 'OCC', 75, 'FR', 43.66667000, 1.20000000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q35721848'),
(43694, 'Lévis-Saint-Nom', 4796, 'IDF', 75, 'FR', 48.71667000, 1.95000000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q94709'),
(43695, 'Lézan', 4799, 'OCC', 75, 'FR', 44.01667000, 4.05000000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q94709'),
(43696, 'Lézardrieux', 4807, 'BRE', 75, 'FR', 48.78510000, -3.10588000, '2019-10-05 22:48:27', '2020-05-01 17:22:44', 1, 'Q35721880'),
(43697, 'Lézat-sur-Lèze', 4799, 'OCC', 75, 'FR', 43.27734000, 1.34686000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q35721880'),
(43698, 'Lézignan-Corbières', 4799, 'OCC', 75, 'FR', 43.19765000, 2.76142000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q176341'),
(43699, 'Lézignan-la-Cèbe', 4799, 'OCC', 75, 'FR', 43.49368000, 3.43708000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q1148196'),
(43700, 'Lézigneux', 4798, 'ARA', 75, 'FR', 45.56589000, 4.06542000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q1148196'),
(43701, 'L’Escale', 4812, 'PAC', 75, 'FR', 44.08162000, 6.02379000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q1148196'),
(43702, 'L’Escarène', 4812, 'PAC', 75, 'FR', 43.83492000, 7.35542000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q1002059'),
(43703, 'L’Isle-Adam', 4796, 'IDF', 75, 'FR', 49.10744000, 2.22818000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q1002059'),
(43704, 'L’Isle-Jourdain', 4799, 'OCC', 75, 'FR', 43.61218000, 1.08219000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q1002059'),
(43705, 'L’Isle-d’Abeau', 4798, 'ARA', 75, 'FR', 45.61960000, 5.22466000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q1002059'),
(43706, 'L’Isle-en-Dodon', 4799, 'OCC', 75, 'FR', 43.38428000, 0.83513000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q1002059'),
(43707, 'L’Isle-sur-la-Sorgue', 4812, 'PAC', 75, 'FR', 43.91971000, 5.05141000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, 'Q504204'),
(43708, 'L’Étang-la-Ville', 4796, 'IDF', 75, 'FR', 48.86954000, 2.05732000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q504204'),
(43709, 'L’Étrat', 4798, 'ARA', 75, 'FR', 45.48610000, 4.37586000, '2019-10-05 22:48:27', '2020-05-01 17:22:43', 1, 'Q504204'),
(43710, 'Mably', 4798, 'ARA', 75, 'FR', 46.06484000, 4.06014000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q504204'),
(43711, 'Macau', 4795, 'NAQ', 75, 'FR', 45.00679000, -0.61821000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43712, 'Machecoul', 4802, 'PDL', 75, 'FR', 46.99358000, -1.82352000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43713, 'Macheren', 4820, 'GES', 75, 'FR', 49.10019000, 6.77763000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '*********'),
(43714, 'Maché', 4802, 'PDL', 75, 'FR', 46.75405000, -1.68692000, '2019-10-05 22:48:27', '2020-05-01 17:22:46', 1, '********'),
(43715, 'Maclas', 4798, 'ARA', 75, 'FR', 45.36289000, 4.68200000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, '********'),
(43716, 'Maffliers', 4796, 'IDF', 75, 'FR', 49.07758000, 2.30768000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q900265'),
(43717, 'Magalas', 4799, 'OCC', 75, 'FR', 43.47095000, 3.22338000, '2019-10-05 22:48:27', '2019-10-05 22:48:27', 1, 'Q900265'),
(43718, 'Magenta', 4820, 'GES', 75, 'FR', 49.04899000, 3.96882000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q900265'),
(43719, 'Magescq', 4795, 'NAQ', 75, 'FR', 43.78205000, -1.21652000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43720, 'Magland', 4798, 'ARA', 75, 'FR', 46.02064000, 6.62089000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43721, 'Magnac-Laval', 4795, 'NAQ', 75, 'FR', 46.21514000, 1.16724000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43722, 'Magnac-sur-Touvre', 4795, 'NAQ', 75, 'FR', 45.66667000, 0.23333000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q829952'),
(43723, 'Magnanville', 4796, 'IDF', 75, 'FR', 48.96798000, 1.67842000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q762728'),
(43724, 'Magny-Cours', 4825, 'BFC', 75, 'FR', 46.88333000, 3.15000000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q762728'),
(43725, 'Magny-Vernois', 4825, 'BFC', 75, 'FR', 47.66997000, 6.47545000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q762728'),
(43726, 'Magny-en-Vexin', 4796, 'IDF', 75, 'FR', 49.15515000, 1.78669000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q177554'),
(43727, 'Magny-le-Désert', 4804, 'NOR', 75, 'FR', 48.57015000, -0.32732000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, 'Q35722609'),
(43728, 'Magny-le-Hongre', 4796, 'IDF', 75, 'FR', 48.86325000, 2.81546000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q35722609'),
(43729, 'Magny-les-Hameaux', 4796, 'IDF', 75, 'FR', 48.74345000, 2.06154000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q76817'),
(43730, 'Magné', 4795, 'NAQ', 75, 'FR', 46.31548000, -0.54575000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, '*********'),
(43731, 'Maidières', 4820, 'GES', 75, 'FR', 48.89961000, 6.04005000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, '********'),
(43732, 'Maignelay-Montigny', 4828, 'HDF', 75, 'FR', 49.55000000, 2.51667000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q816294'),
(43733, 'Maillane', 4812, 'PAC', 75, 'FR', 43.83289000, 4.78209000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q498858'),
(43734, 'Maillezais', 4802, 'PDL', 75, 'FR', 46.37267000, -0.73963000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43735, 'Maillot', 4825, 'BFC', 75, 'FR', 48.17557000, 3.30784000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43736, 'Mailly-le-Camp', 4820, 'GES', 75, 'FR', 48.66526000, 4.21303000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43737, 'Maincy', 4796, 'IDF', 75, 'FR', 48.54977000, 2.70017000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43738, 'Maing', 4828, 'HDF', 75, 'FR', 50.30791000, 3.48447000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43739, 'Maintenon', 4818, 'CVL', 75, 'FR', 48.58704000, 1.57847000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q653559'),
(43740, 'Mainvilliers', 4818, 'CVL', 75, 'FR', 48.45003000, 1.45607000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q653559'),
(43741, 'Maisdon-sur-Sèvre', 4802, 'PDL', 75, 'FR', 47.09714000, -1.38000000, '2019-10-05 22:48:28', '2020-05-01 17:22:46', 1, '********'),
(43742, 'Maisnil-lès-Ruitz', 4828, 'HDF', 75, 'FR', 50.45342000, 2.58992000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, '********'),
(43743, 'Maisons-Alfort', 4796, 'IDF', 75, 'FR', 48.81171000, 2.43945000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q208314'),
(43744, 'Maisons-Laffitte', 4796, 'IDF', 75, 'FR', 48.95264000, 2.14521000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q463951'),
(43745, 'Maisse', 4796, 'IDF', 75, 'FR', 48.39525000, 2.37902000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43746, 'Maizières-la-Grande-Paroisse', 4820, 'GES', 75, 'FR', 48.51012000, 3.78573000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, '********'),
(43747, 'Maizières-lès-Metz', 4820, 'GES', 75, 'FR', 49.21335000, 6.15956000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, 'Q21843'),
(43748, 'Malakoff', 4796, 'IDF', 75, 'FR', 48.81999000, 2.29998000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q253721'),
(43749, 'Malansac', 4807, 'BRE', 75, 'FR', 47.67724000, -2.29543000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q35723288'),
(43750, 'Malataverne', 4798, 'ARA', 75, 'FR', 44.48525000, 4.75326000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q302380'),
(43751, 'Malaucène', 4812, 'PAC', 75, 'FR', 44.17393000, 5.13213000, '2019-10-05 22:48:28', '2020-05-01 17:22:46', 1, 'Q302380'),
(43752, 'Malaunay', 4804, 'NOR', 75, 'FR', 49.52710000, 1.04292000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q302380'),
(43753, 'Malay-le-Grand', 4825, 'BFC', 75, 'FR', 48.17589000, 3.34189000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q302380'),
(43754, 'Malemort-du-Comtat', 4812, 'PAC', 75, 'FR', 44.02096000, 5.15961000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q531296'),
(43755, 'Malemort-sur-Corrèze', 4795, 'NAQ', 75, 'FR', 45.17075000, 1.56393000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, 'Q531296'),
(43756, 'Malesherbes', 4818, 'CVL', 75, 'FR', 48.29566000, 2.40935000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q531296'),
(43757, 'Malestroit', 4807, 'BRE', 75, 'FR', 47.80934000, -2.38457000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '*********'),
(43758, 'Malguénac', 4807, 'BRE', 75, 'FR', 48.08008000, -3.05248000, '2019-10-05 22:48:28', '2020-05-01 17:22:44', 1, '*********'),
(43759, 'Malicorne-sur-Sarthe', 4802, 'PDL', 75, 'FR', 47.81317000, -0.08152000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43760, 'Malijai', 4812, 'PAC', 75, 'FR', 44.04600000, 6.03041000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43761, 'Malintrat', 4798, 'ARA', 75, 'FR', 45.81509000, 3.18603000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43762, 'Malissard', 4798, 'ARA', 75, 'FR', 44.89969000, 4.95384000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q668152'),
(43763, 'Mallemoisson', 4812, 'PAC', 75, 'FR', 44.04687000, 6.12557000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43764, 'Mallemort', 4812, 'PAC', 75, 'FR', 43.73172000, 5.17945000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43765, 'Malpassé', 4812, 'PAC', 75, 'FR', 43.32170000, 5.41551000, '2019-10-05 22:48:28', '2020-05-01 17:22:46', 1, '********'),
(43766, 'Malville', 4802, 'PDL', 75, 'FR', 47.35905000, -1.86227000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q743520'),
(43767, 'Malzéville', 4820, 'GES', 75, 'FR', 48.71235000, 6.18468000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, 'Q585128'),
(43768, 'Mamers', 4802, 'PDL', 75, 'FR', 48.34961000, 0.36937000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q645085'),
(43769, 'Mametz', 4828, 'HDF', 75, 'FR', 50.63485000, 2.32478000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q645085'),
(43770, 'Mamirolle', 4825, 'BFC', 75, 'FR', 47.19857000, 6.15924000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q907131'),
(43771, 'Manche', 4804, 'NOR', 75, 'FR', 49.03822000, -1.31865000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q12589'),
(43772, 'Mancieulles', 4820, 'GES', 75, 'FR', 49.28231000, 5.89612000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q12589'),
(43773, 'Mandelieu-la-Napoule', 4812, 'PAC', 75, 'FR', 43.54577000, 6.93734000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q239317'),
(43774, 'Mandeure', 4825, 'BFC', 75, 'FR', 47.44921000, 6.80762000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q983818'),
(43775, 'Mandres-les-Roses', 4796, 'IDF', 75, 'FR', 48.70198000, 2.54662000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q983818'),
(43776, 'Manduel', 4799, 'OCC', 75, 'FR', 43.81855000, 4.47247000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q244249'),
(43777, 'Mane', 4812, 'PAC', 75, 'FR', 43.93795000, 5.76718000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q244249'),
(43778, 'Manneville-sur-Risle', 4804, 'NOR', 75, 'FR', 49.35173000, 0.54526000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q244249'),
(43779, 'Manom', 4820, 'GES', 75, 'FR', 49.37092000, 6.18345000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q244249'),
(43780, 'Manosque', 4812, 'PAC', 75, 'FR', 43.82883000, 5.78688000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q234274'),
(43781, 'Mansac', 4795, 'NAQ', 75, 'FR', 45.16839000, 1.38342000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q234274'),
(43782, 'Mansigné', 4802, 'PDL', 75, 'FR', 47.74833000, 0.13311000, '2019-10-05 22:48:28', '2020-05-01 17:22:46', 1, 'Q234274'),
(43783, 'Mansle', 4795, 'NAQ', 75, 'FR', 45.87526000, 0.17914000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, '********'),
(43784, 'Mantes-la-Jolie', 4796, 'IDF', 75, 'FR', 48.99048000, 1.71670000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q207253'),
(43785, 'Mantes-la-Ville', 4796, 'IDF', 75, 'FR', 48.97374000, 1.70253000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q760624'),
(43786, 'Manthelan', 4818, 'CVL', 75, 'FR', 47.13632000, 0.79319000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q760624'),
(43787, 'Manzat', 4798, 'ARA', 75, 'FR', 45.96160000, 2.94140000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q392197'),
(43788, 'Manziat', 4798, 'ARA', 75, 'FR', 46.36125000, 4.90580000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q392197'),
(43789, 'Manéglise', 4804, 'NOR', 75, 'FR', 49.56578000, 0.25572000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, 'Q831266'),
(43790, 'Marange-Silvange', 4820, 'GES', 75, 'FR', 49.20822000, 6.10426000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q831266'),
(43791, 'Marans', 4795, 'NAQ', 75, 'FR', 46.30811000, -0.99450000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q35724273'),
(43792, 'Maraussan', 4799, 'OCC', 75, 'FR', 43.36895000, 3.15643000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q243611'),
(43793, 'Marbache', 4820, 'GES', 75, 'FR', 48.79867000, 6.10600000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q243611'),
(43794, 'Marboué', 4818, 'CVL', 75, 'FR', 48.11377000, 1.33210000, '2019-10-05 22:48:28', '2020-05-01 17:22:44', 1, 'Q243611'),
(43795, 'Marboz', 4798, 'ARA', 75, 'FR', 46.34290000, 5.25722000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q243611'),
(43796, 'Marcamps', 4795, 'NAQ', 75, 'FR', 45.04045000, -0.49275000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q35724329'),
(43797, 'Marcellaz-Albanais', 4798, 'ARA', 75, 'FR', 45.87546000, 5.99880000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q664076'),
(43798, 'Marcey-les-Grèves', 4804, 'NOR', 75, 'FR', 48.69673000, -1.39156000, '2019-10-05 22:48:28', '2020-05-01 17:22:45', 1, 'Q35724370'),
(43799, 'Marcheprime', 4795, 'NAQ', 75, 'FR', 44.69146000, -0.85496000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q194288'),
(43800, 'Marchiennes', 4828, 'HDF', 75, 'FR', 50.40000000, 3.28333000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q35724385'),
(43801, 'Marciac', 4799, 'OCC', 75, 'FR', 43.52321000, 0.16091000, '2019-10-05 22:48:28', '2019-10-05 22:48:28', 1, 'Q473925');

