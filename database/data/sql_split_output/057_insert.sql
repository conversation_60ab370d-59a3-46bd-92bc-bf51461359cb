INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(28333, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 3011, '<PERSON>', 82, '<PERSON>', 52.05207000, 11.80202000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q703864'),
(28334, 'P<PERSON><PERSON>ßberg', 3009, 'BY', 82, 'DE', 49.91977000, 11.86883000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q703864'),
(28335, '<PERSON><PERSON><PERSON><PERSON>hausen', 3006, 'BW', 82, 'DE', 48.79875000, 9.59587000, '2019-10-05 22:41:29', '2020-05-01 17:22:47', 1, 'Q83214'),
(28336, 'Pobershau', 3021, '<PERSON><PERSON>', 82, '<PERSON>', 50.64079000, 13.21776000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q58022'),
(28337, '<PERSON>ckau', 3021, 'S<PERSON>', 82, 'DE', 50.70000000, 13.23333000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q57822'),
(28338, 'Pocking', 3009, 'BY', 82, 'DE', 48.40149000, 13.31315000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q279629'),
(28339, 'Podelzig', 3013, 'BB', 82, 'DE', 52.47175000, 14.53465000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q585269'),
(28340, 'Poing', 3009, 'BY', 82, 'DE', 48.17004000, 11.81863000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q585269'),
(28341, 'Polch', 3019, 'RP', 82, 'DE', 50.29973000, 7.31315000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q629314'),
(28342, 'Poll', 3017, 'NW', 82, 'DE', 50.91256000, 6.99057000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q316601'),
(28343, 'Polle', 3008, 'NI', 82, 'DE', 51.89871000, 9.40386000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q555106'),
(28344, 'Polleben', 3011, 'ST', 82, 'DE', 51.57966000, 11.59931000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q1602039'),
(28345, 'Pollhagen', 3008, 'NI', 82, 'DE', 52.38529000, 9.18813000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q657625'),
(28346, 'Polling', 3009, 'BY', 82, 'DE', 48.21667000, 12.56667000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q657625'),
(28347, 'Polsingen', 3009, 'BY', 82, 'DE', 48.92067000, 10.71150000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q338094'),
(28348, 'Pommelsbrunn', 3009, 'BY', 82, 'DE', 49.50437000, 11.51101000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q508431'),
(28349, 'Pommersfelden', 3009, 'BY', 82, 'DE', 49.76667000, 10.81667000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q627521'),
(28350, 'Ponitz', 3015, 'TH', 82, 'DE', 50.85762000, 12.42309000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q305056'),
(28351, 'Poppenbüttel', 3016, 'HH', 82, 'DE', 53.65917000, 10.08472000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q1818'),
(28352, 'Poppenhausen', 3009, 'BY', 82, 'DE', 50.09999000, 10.14244000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q1818'),
(28353, 'Poppenhausen', 3018, 'HE', 82, 'DE', 50.48793000, 9.86795000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q636446'),
(28354, 'Poppenricht', 3009, 'BY', 82, 'DE', 49.47557000, 11.79778000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q516816'),
(28355, 'Porschdorf', 3021, 'SN', 82, 'DE', 50.94180000, 14.12687000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q554695'),
(28356, 'Porta Westfalica', 3017, 'NW', 82, 'DE', 52.22961000, 8.91612000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q554695'),
(28357, 'Porz am Rhein', 3017, 'NW', 82, 'DE', 50.88637000, 7.05830000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q458242'),
(28358, 'Poseritz', 3007, 'MV', 82, 'DE', 54.29811000, 13.27492000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q305455'),
(28359, 'Postau', 3009, 'BY', 82, 'DE', 48.65000000, 12.33333000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q511508'),
(28360, 'Postbauer-Heng', 3009, 'BY', 82, 'DE', 49.30528000, 11.35722000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q16799'),
(28361, 'Postmünster', 3009, 'BY', 82, 'DE', 48.41667000, 12.90000000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q559918'),
(28362, 'Potsdam', 3013, 'BB', 82, 'DE', 52.39886000, 13.06566000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q1711'),
(28363, 'Pottenstein', 3009, 'BY', 82, 'DE', 49.77131000, 11.40784000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q32233952'),
(28364, 'Pottum', 3019, 'RP', 82, 'DE', 50.60000000, 8.00000000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q676432'),
(28365, 'Pouch', 3011, 'ST', 82, 'DE', 51.62324000, 12.40133000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q595513'),
(28366, 'Poxdorf', 3009, 'BY', 82, 'DE', 49.93642000, 11.12211000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q595513'),
(28367, 'Pracht', 3019, 'RP', 82, 'DE', 50.76031000, 7.64871000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q665072'),
(28368, 'Prackenbach', 3009, 'BY', 82, 'DE', 49.09543000, 12.82614000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q491445'),
(28369, 'Pratau', 3011, 'ST', 82, 'DE', 51.84226000, 12.64373000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q491445'),
(28370, 'Prebitz', 3009, 'BY', 82, 'DE', 49.83333000, 11.68333000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q503588'),
(28371, 'Preetz', 3005, 'SH', 82, 'DE', 54.23540000, 10.27795000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q500171'),
(28372, 'Preetz', 3007, 'MV', 82, 'DE', 54.35017000, 12.98936000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q500171'),
(28373, 'Premnitz', 3013, 'BB', 82, 'DE', 52.53184000, 12.34845000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q585620'),
(28374, 'Prenzlau', 3013, 'BB', 82, 'DE', 53.31625000, 13.86261000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q537214'),
(28375, 'Prenzlauer Berg', 3010, 'BE', 82, 'DE', 52.53878000, 13.42443000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q313186'),
(28376, 'Pressath', 3009, 'BY', 82, 'DE', 49.76859000, 11.93972000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q502947'),
(28377, 'Presseck', 3009, 'BY', 82, 'DE', 50.22804000, 11.55508000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q503536'),
(28378, 'Pressig', 3009, 'BY', 82, 'DE', 50.35245000, 11.30969000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q504103'),
(28379, 'Prettin', 3011, 'ST', 82, 'DE', 51.66077000, 12.92353000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q573500'),
(28380, 'Pretzfeld', 3009, 'BY', 82, 'DE', 49.75454000, 11.17430000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q506444'),
(28381, 'Pretzier', 3011, 'ST', 82, 'DE', 52.83224000, 11.26105000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q702944'),
(28382, 'Pretzsch', 3011, 'ST', 82, 'DE', 51.71466000, 12.80663000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q699124'),
(28383, 'Pretzschendorf', 3021, 'SN', 82, 'DE', 50.87385000, 13.52499000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q8215'),
(28384, 'Preußisch Oldendorf', 3017, 'NW', 82, 'DE', 52.30589000, 8.49341000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q182938'),
(28385, 'Prichsenstadt', 3009, 'BY', 82, 'DE', 49.81933000, 10.34773000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q502636'),
(28386, 'Prien am Chiemsee', 3009, 'BY', 82, 'DE', 47.85602000, 12.34623000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q18863'),
(28387, 'Priesendorf', 3009, 'BY', 82, 'DE', 49.90750000, 10.71183000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q530556'),
(28388, 'Priestewitz', 3021, 'SN', 82, 'DE', 51.25000000, 13.51667000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q8759'),
(28389, 'Prisdorf', 3005, 'SH', 82, 'DE', 53.68333000, 9.75000000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q8759'),
(28390, 'Prittitz', 3011, 'ST', 82, 'DE', 51.14996000, 11.93020000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q577046'),
(28391, 'Prittriching', 3009, 'BY', 82, 'DE', 48.20074000, 10.92801000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q32236665'),
(28392, 'Pritzwalk', 3013, 'BB', 82, 'DE', 53.14945000, 12.17405000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q509952'),
(28393, 'Probsteierhagen', 3005, 'SH', 82, 'DE', 54.36153000, 10.28772000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q558468'),
(28394, 'Probstzella', 3015, 'TH', 82, 'DE', 50.53333000, 11.38333000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q556416'),
(28395, 'Prohn', 3007, 'MV', 82, 'DE', 54.36901000, 13.02369000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q662403'),
(28396, 'Pronstorf', 3005, 'SH', 82, 'DE', 53.95000000, 10.46667000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q543991'),
(28397, 'Prosselsheim', 3009, 'BY', 82, 'DE', 49.86309000, 10.12666000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q508243'),
(28398, 'Prutting', 3009, 'BY', 82, 'DE', 47.89321000, 12.20238000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q532859'),
(28399, 'Prötzel', 3013, 'BB', 82, 'DE', 52.63723000, 13.98783000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q632427'),
(28400, 'Prüm', 3019, 'RP', 82, 'DE', 50.20785000, 6.42019000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q253696'),
(28401, 'Puchheim', 3009, 'BY', 82, 'DE', 48.15000000, 11.35000000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q32237454'),
(28402, 'Puderbach', 3019, 'RP', 82, 'DE', 50.60000000, 7.61667000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q554263'),
(28403, 'Pulheim', 3017, 'NW', 82, 'DE', 50.99965000, 6.80632000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q4113'),
(28404, 'Pullach im Isartal', 3009, 'BY', 82, 'DE', 48.06122000, 11.52148000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q202116'),
(28405, 'Pullenreuth', 3009, 'BY', 82, 'DE', 49.93333000, 12.00000000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q533149'),
(28406, 'Puschendorf', 3009, 'BY', 82, 'DE', 49.52398000, 10.83192000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q533149'),
(28407, 'Puschwitz', 3021, 'SN', 82, 'DE', 51.25501000, 14.30111000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q533149'),
(28408, 'Putbus', 3007, 'MV', 82, 'DE', 54.35511000, 13.47634000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q572006'),
(28409, 'Putlitz', 3013, 'BB', 82, 'DE', 53.24899000, 12.04179000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q565732'),
(28410, 'Putzbrunn', 3009, 'BY', 82, 'DE', 48.07579000, 11.71572000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q261630'),
(28411, 'Pyrbaum', 3009, 'BY', 82, 'DE', 49.29622000, 11.28655000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q503362'),
(28412, 'Pähl', 3009, 'BY', 82, 'DE', 47.90000000, 11.18333000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q49288278'),
(28414, 'Pöhl', 3021, 'SN', 82, 'DE', 50.39311000, 12.32908000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q49288278'),
(28415, 'Pöhla', 3021, 'SN', 82, 'DE', 50.51143000, 12.81889000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q705414'),
(28416, 'Pölitz', 3005, 'SH', 82, 'DE', 53.77303000, 10.38276000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q651252'),
(28417, 'Pölzig', 3015, 'TH', 82, 'DE', 50.95000000, 12.20000000, '2019-10-05 22:41:29', '2020-05-01 17:22:50', 1, 'Q630301'),
(28418, 'Pörnbach', 3009, 'BY', 82, 'DE', 48.61667000, 11.46667000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q267723'),
(28419, 'Pöttmes', 3009, 'BY', 82, 'DE', 48.58383000, 11.08762000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q251897'),
(28420, 'Pößneck', 3015, 'TH', 82, 'DE', 50.69358000, 11.59229000, '2019-10-05 22:41:29', '2020-05-01 17:22:50', 1, 'Q505185'),
(28421, 'Püchersreuth', 3009, 'BY', 82, 'DE', 49.75000000, 12.23333000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q504374'),
(28422, 'Pürgen', 3009, 'BY', 82, 'DE', 48.02472000, 10.92213000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q504374'),
(28423, 'Püttlingen', 3020, 'SL', 82, 'DE', 49.28550000, 6.88723000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q544908'),
(28424, 'Quakenbrück', 3008, 'NI', 82, 'DE', 52.67502000, 7.94983000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q498219'),
(28425, 'Quarnbek', 3005, 'SH', 82, 'DE', 54.33333000, 9.98333000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q638148'),
(28426, 'Quedlinburg', 3011, 'ST', 82, 'DE', 51.78843000, 11.15006000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q40623'),
(28427, 'Queidersbach', 3019, 'RP', 82, 'DE', 49.36667000, 7.63333000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q23018'),
(28428, 'Queis', 3011, 'ST', 82, 'DE', 51.47626000, 12.13455000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, '*********'),
(28429, 'Quellendorf', 3011, 'ST', 82, 'DE', 51.74916000, 12.12796000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q697580'),
(28430, 'Querfurt', 3011, 'ST', 82, 'DE', 51.38124000, 11.60047000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q518204'),
(28431, 'Quern', 3005, 'SH', 82, 'DE', 54.75000000, 9.71667000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q559199'),
(28432, 'Quickborn', 3005, 'SH', 82, 'DE', 53.72831000, 9.90934000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q516623'),
(28433, 'Quierschied', 3020, 'SL', 82, 'DE', 49.31667000, 7.05000000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q662580'),
(28434, 'Quitzdorf', 3021, 'SN', 82, 'DE', 51.28333000, 14.76667000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q32243269'),
(28435, 'Raben Steinfeld', 3007, 'MV', 82, 'DE', 53.60141000, 11.50487000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q835796'),
(28436, 'Rabenau', 3021, 'SN', 82, 'DE', 50.96484000, 13.64305000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q835796'),
(28437, 'Rabenau', 3018, 'HE', 82, 'DE', 50.67753000, 8.86425000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q835796'),
(28438, 'Rackwitz', 3021, 'SN', 82, 'DE', 51.43333000, 12.38333000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q16063'),
(28439, 'Radbruch', 3008, 'NI', 82, 'DE', 53.31667000, 10.28333000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q643092'),
(28440, 'Raddestorf', 3008, 'NI', 82, 'DE', 52.45000000, 8.96667000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q557395'),
(28441, 'Radeberg', 3021, 'SN', 82, 'DE', 51.11112000, 13.91199000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q81729'),
(28442, 'Radebeul', 3021, 'SN', 82, 'DE', 51.10654000, 13.66047000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q8762'),
(28443, 'Radeburg', 3021, 'SN', 82, 'DE', 51.21516000, 13.72810000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q8770'),
(28444, 'Radegast', 3011, 'ST', 82, 'DE', 51.65654000, 12.09485000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q686363'),
(28445, 'Radevormwald', 3017, 'NW', 82, 'DE', 51.20219000, 7.36027000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q32244428'),
(28446, 'Radibor', 3021, 'SN', 82, 'DE', 51.24481000, 14.39842000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q93286'),
(28447, 'Radis', 3011, 'ST', 82, 'DE', 51.75226000, 12.51453000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q697349'),
(28448, 'Radolfzell', 3006, 'BW', 82, 'DE', 47.74194000, 8.97098000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q16113'),
(28449, 'Raesfeld', 3017, 'NW', 82, 'DE', 51.76667000, 6.85000000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q32244566'),
(28450, 'Raguhn', 3011, 'ST', 82, 'DE', 51.71167000, 12.27531000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q707933'),
(28451, 'Rahden', 3017, 'NW', 82, 'DE', 52.43367000, 8.61263000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q182979'),
(28452, 'Rahnsdorf', 3010, 'BE', 82, 'DE', 52.44115000, 13.68708000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q563583'),
(28453, 'Rain', 3009, 'BY', 82, 'DE', 48.69029000, 10.91611000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q32245234'),
(28454, 'Raisdorf', 3005, 'SH', 82, 'DE', 54.28127000, 10.24915000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q682215'),
(28455, 'Raisting', 3009, 'BY', 82, 'DE', 47.91667000, 11.10000000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q682215'),
(28456, 'Raitenbuch', 3009, 'BY', 82, 'DE', 49.01468000, 11.12486000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q549073'),
(28457, 'Ralingen', 3019, 'RP', 82, 'DE', 49.81667000, 6.50000000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q478527'),
(28458, 'Ramberg', 3019, 'RP', 82, 'DE', 49.26028000, 8.00833000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q478527'),
(28459, 'Rambin', 3007, 'MV', 82, 'DE', 54.35566000, 13.20445000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q639946'),
(28460, 'Ramerberg', 3009, 'BY', 82, 'DE', 48.01806000, 12.14513000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q260613'),
(28461, 'Rammelsbach', 3019, 'RP', 82, 'DE', 49.54170000, 7.44392000, '2019-10-05 22:41:30', '2019-10-05 22:41:30', 1, 'Q560300'),
(28462, 'Rammenau', 3021, 'SN', 82, 'DE', 51.15000000, 14.13333000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q93289'),
(28463, 'Rammingen', 3006, 'BW', 82, 'DE', 48.51814000, 10.17197000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q93289'),
(28464, 'Ramsau', 3009, 'BY', 82, 'DE', 48.17277000, 12.22957000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q32245923'),
(28465, 'Ramsen', 3019, 'RP', 82, 'DE', 49.53694000, 8.01333000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q32245923'),
(28466, 'Ramsin', 3011, 'ST', 82, 'DE', 51.61095000, 12.23812000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q1572272'),
(28467, 'Ramstein-Miesenbach', 3019, 'RP', 82, 'DE', 49.44452000, 7.55533000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q22867'),
(28468, 'Ramsthal', 3009, 'BY', 82, 'DE', 50.14039000, 10.06777000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q504289'),
(28469, 'Randersacker', 3009, 'BY', 82, 'DE', 49.76021000, 9.98277000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q113639'),
(28470, 'Rangendingen', 3006, 'BW', 82, 'DE', 48.38168000, 8.88940000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q113639'),
(28471, 'Rangsdorf', 3013, 'BB', 82, 'DE', 52.29126000, 13.41946000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q32246357'),
(28472, 'Ranis', 3015, 'TH', 82, 'DE', 50.66132000, 11.56912000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q550275'),
(28473, 'Rannungen', 3009, 'BY', 82, 'DE', 50.16537000, 10.20484000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q504270'),
(28474, 'Ransbach-Baumbach', 3019, 'RP', 82, 'DE', 50.46496000, 7.72830000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q552481'),
(28475, 'Ranstadt', 3018, 'HE', 82, 'DE', 50.35739000, 8.98375000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q621899'),
(28476, 'Rantrum', 3005, 'SH', 82, 'DE', 54.44062000, 9.12872000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q21019'),
(28477, 'Raschau', 3021, 'SN', 82, 'DE', 50.53127000, 12.83312000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q701129'),
(28478, 'Rasdorf', 3018, 'HE', 82, 'DE', 50.71667000, 9.90000000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q624495'),
(28479, 'Rastatt', 3006, 'BW', 82, 'DE', 48.85851000, 8.20965000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q6874'),
(28480, 'Rastede', 3008, 'NI', 82, 'DE', 53.25000000, 8.20000000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q512640'),
(28481, 'Rastenberg', 3015, 'TH', 82, 'DE', 51.17496000, 11.42029000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q552604'),
(28482, 'Rastow', 3007, 'MV', 82, 'DE', 53.45709000, 11.43145000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q674879'),
(28483, 'Ratekau', 3005, 'SH', 82, 'DE', 53.95000000, 10.73333000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q629480'),
(28484, 'Rath', 3017, 'NW', 82, 'DE', 50.92379000, 7.09270000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q883320'),
(28485, 'Rathenow', 3013, 'BB', 82, 'DE', 52.60659000, 12.33696000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q525425'),
(28486, 'Rathmannsdorf', 3021, 'SN', 82, 'DE', 50.92993000, 14.14224000, '2019-10-05 22:41:31', '2019-10-05 22:41:31', 1, 'Q8227'),
(28487, 'Ratingen', 3017, 'NW', 82, 'DE', 51.29724000, 6.84929000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q3791'),
(28488, 'Rattelsdorf', 3009, 'BY', 82, 'DE', 50.01502000, 10.88857000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q529428'),
(28489, 'Rattenberg', 3009, 'BY', 82, 'DE', 49.08612000, 12.74912000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q529428'),
(28490, 'Rattiszell', 3009, 'BY', 82, 'DE', 49.02573000, 12.65924000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q559877'),
(28491, 'Ratzeburg', 3005, 'SH', 82, 'DE', 53.69965000, 10.77256000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q490025'),
(28492, 'Raubach', 3019, 'RP', 82, 'DE', 50.57489000, 7.62496000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q654590'),
(28493, 'Raubling', 3009, 'BY', 82, 'DE', 47.79050000, 12.11088000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q538080'),
(28494, 'Rauen', 3013, 'BB', 82, 'DE', 52.33227000, 14.02797000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q622505'),
(28495, 'Rauenberg', 3006, 'BW', 82, 'DE', 49.26943000, 8.70344000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32247714'),
(28496, 'Raunheim', 3018, 'HE', 82, 'DE', 50.01320000, 8.45253000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q623474'),
(28497, 'Rauschenberg', 3018, 'HE', 82, 'DE', 50.88329000, 8.91864000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q627435'),
(28498, 'Ravensburg', 3006, 'BW', 82, 'DE', 47.78198000, 9.61062000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q6832'),
(28499, 'Raßnitz', 3011, 'ST', 82, 'DE', 51.39085000, 12.09406000, '2019-10-05 22:41:32', '2020-05-01 17:22:49', 1, 'Q2134384'),
(28500, 'Rechberghausen', 3006, 'BW', 82, 'DE', 48.73080000, 9.64419000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q81154'),
(28501, 'Rechenberg-Bienenmühle', 3021, 'SN', 82, 'DE', 50.73777000, 13.53502000, '2019-10-05 22:41:32', '2020-05-01 17:22:49', 1, 'Q71036'),
(28502, 'Rechlin', 3007, 'MV', 82, 'DE', 53.33549000, 12.72543000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q20263'),
(28503, 'Rechtenbach', 3009, 'BY', 82, 'DE', 49.98306000, 9.50833000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q508908'),
(28504, 'Rechtmehring', 3009, 'BY', 82, 'DE', 48.11667000, 12.16667000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q521085'),
(28505, 'Rechtsupweg', 3008, 'NI', 82, 'DE', 53.53333000, 7.33333000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q504765'),
(28506, 'Recke', 3017, 'NW', 82, 'DE', 52.36885000, 7.72116000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32249024'),
(28507, 'Recklinghausen', 3017, 'NW', 82, 'DE', 51.61379000, 7.19738000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q3050'),
(28508, 'Rednitzhembach', 3009, 'BY', 82, 'DE', 49.30095000, 11.07997000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q540876'),
(28509, 'Redwitz an der Rodach', 3009, 'BY', 82, 'DE', 50.17323000, 11.20833000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q505809'),
(28510, 'Rees', 3017, 'NW', 82, 'DE', 51.76263000, 6.39778000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32250561'),
(28511, 'Reeßum', 3008, 'NI', 82, 'DE', 53.13333000, 9.21667000, '2019-10-05 22:41:32', '2020-05-01 17:22:48', 1, 'Q32250561'),
(28512, 'Regen', 3009, 'BY', 82, 'DE', 48.97190000, 13.12824000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32250561'),
(28513, 'Regensburg', 3009, 'BY', 82, 'DE', 49.01513000, 12.10161000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q2978'),
(28514, 'Regenstauf', 3009, 'BY', 82, 'DE', 49.12014000, 12.13027000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q491641'),
(28515, 'Regesbostel', 3008, 'NI', 82, 'DE', 53.38333000, 9.65000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q491641'),
(28516, 'Regierungsbezirk Arnsberg', 3017, 'NW', 82, 'DE', 51.32967000, 8.00710000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q7924'),
(28517, 'Regierungsbezirk Darmstadt', 3018, 'HE', 82, 'DE', 50.00000000, 8.75000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q7932'),
(28518, 'Regierungsbezirk Detmold', 3017, 'NW', 82, 'DE', 51.96660000, 8.78333000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q7923'),
(28519, 'Regierungsbezirk Gießen', 3018, 'HE', 82, 'DE', 50.69806000, 8.76861000, '2019-10-05 22:41:32', '2020-05-01 17:22:48', 1, 'Q7931'),
(28520, 'Regierungsbezirk Kassel', 3018, 'HE', 82, 'DE', 51.08333000, 9.40000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q7928'),
(28521, 'Regierungsbezirk Köln', 3017, 'NW', 82, 'DE', 50.78333000, 6.86660000, '2019-10-05 22:41:32', '2020-05-01 17:22:49', 1, 'Q7927'),
(28522, 'Regierungsbezirk Mittelfranken', 3009, 'BY', 82, 'DE', 49.33333000, 10.83333000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q10551'),
(28523, 'Regierungsbezirk Münster', 3017, 'NW', 82, 'DE', 51.96660000, 7.43330000, '2019-10-05 22:41:32', '2020-05-01 17:22:49', 1, 'Q7920'),
(28524, 'Regierungsbezirk Stuttgart', 3006, 'BW', 82, 'DE', 49.08333000, 9.66667000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q8172'),
(28525, 'Regierungsbezirk Unterfranken', 3009, 'BY', 82, 'DE', 50.00000000, 10.00000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q10547'),
(28526, 'Regis-Breitingen', 3021, 'SN', 82, 'DE', 51.08883000, 12.43841000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q10774'),
(28527, 'Regnitzlosau', 3009, 'BY', 82, 'DE', 50.30000000, 12.05000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q504343'),
(28528, 'Rehau', 3009, 'BY', 82, 'DE', 50.24921000, 12.03422000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q49814'),
(28529, 'Rehburg-Loccum', 3008, 'NI', 82, 'DE', 52.46952000, 9.19957000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q541873'),
(28530, 'Rehden', 3008, 'NI', 82, 'DE', 52.61024000, 8.48093000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q502845'),
(28531, 'Rehe', 3019, 'RP', 82, 'DE', 50.63333000, 8.11667000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q502845'),
(28532, 'Rehfelde', 3013, 'BB', 82, 'DE', 52.53417000, 13.90884000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q589571'),
(28533, 'Rehling', 3009, 'BY', 82, 'DE', 48.48333000, 10.93333000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q589571'),
(28534, 'Rehna', 3007, 'MV', 82, 'DE', 53.77877000, 11.04929000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q565584'),
(28535, 'Reichardtswerben', 3011, 'ST', 82, 'DE', 51.24851000, 11.95349000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q612203'),
(28536, 'Reichartshausen', 3006, 'BW', 82, 'DE', 49.35500000, 8.94528000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q82169'),
(28537, 'Reichelsheim', 3018, 'HE', 82, 'DE', 49.71211000, 8.83896000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32251647'),
(28538, 'Reichenau', 3006, 'BW', 82, 'DE', 47.68885000, 9.06355000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32251647'),
(28539, 'Reichenbach', 3009, 'BY', 82, 'DE', 49.18333000, 12.35000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q266039'),
(28540, 'Reichenbach', 3021, 'SN', 82, 'DE', 51.14144000, 14.80270000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32251721'),
(28541, 'Reichenbach an der Fils', 3006, 'BW', 82, 'DE', 48.71011000, 9.46429000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q83080'),
(28542, 'Reichenbach-Steegen', 3019, 'RP', 82, 'DE', 49.50000000, 7.55000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q566948'),
(28543, 'Reichenbach/Vogtland', 3021, 'SN', 82, 'DE', 50.62279000, 12.30344000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32251794'),
(28544, 'Reichenberg', 3009, 'BY', 82, 'DE', 49.73193000, 9.91478000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q264790'),
(28545, 'Reichenschwand', 3009, 'BY', 82, 'DE', 49.51384000, 11.37274000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q508189'),
(28546, 'Reichenwalde', 3013, 'BB', 82, 'DE', 52.26667000, 14.00000000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q621478'),
(28547, 'Reichersbeuern', 3009, 'BY', 82, 'DE', 47.76667000, 11.63333000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q532923'),
(28548, 'Reichertshausen', 3009, 'BY', 82, 'DE', 47.89882000, 11.55843000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q20667881'),
(28549, 'Reichertsheim', 3009, 'BY', 82, 'DE', 48.20000000, 12.28333000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q514340'),
(28550, 'Reichertshofen', 3009, 'BY', 82, 'DE', 48.65775000, 11.46612000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q514340'),
(28551, 'Reichling', 3009, 'BY', 82, 'DE', 47.92280000, 10.92847000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32251998'),
(28552, 'Reil', 3019, 'RP', 82, 'DE', 50.01667000, 7.11667000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q32251998'),
(28553, 'Reilingen', 3006, 'BW', 82, 'DE', 49.29833000, 8.56417000, '2019-10-05 22:41:32', '2019-10-05 22:41:32', 1, 'Q81913'),
(28554, 'Reimlingen', 3009, 'BY', 82, 'DE', 48.81667000, 10.51667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q503504'),
(28555, 'Reinbek', 3005, 'SH', 82, 'DE', 53.51703000, 10.24880000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q516405'),
(28556, 'Reinberg', 3007, 'MV', 82, 'DE', 53.67484000, 13.14417000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q32252408'),
(28557, 'Reinfeld', 3005, 'SH', 82, 'DE', 53.83184000, 10.49126000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q539463'),
(28558, 'Reinhardshausen', 3018, 'HE', 82, 'DE', 51.11166000, 9.07514000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q539463'),
(28559, 'Reinhardtsgrimma', 3021, 'SN', 82, 'DE', 50.89270000, 13.75534000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q700500'),
(28560, 'Reinhausen', 3009, 'BY', 82, 'DE', 49.03092000, 12.11329000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q700500'),
(28561, 'Reinheim', 3018, 'HE', 82, 'DE', 49.82923000, 8.83572000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q122753'),
(28562, 'Reinickendorf', 3010, 'BE', 82, 'DE', 52.56667000, 13.33333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q694034'),
(28563, 'Reinsberg', 3021, 'SN', 82, 'DE', 51.00823000, 13.36542000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q694034'),
(28564, 'Reinsdorf', 3021, 'SN', 82, 'DE', 50.69775000, 12.55555000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q694034'),
(28565, 'Reinsfeld', 3019, 'RP', 82, 'DE', 49.68333000, 6.88333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q553049'),
(28566, 'Reinstorf', 3008, 'NI', 82, 'DE', 53.23445000, 10.57323000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q646415'),
(28567, 'Reisbach', 3009, 'BY', 82, 'DE', 48.57008000, 12.62799000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q32252698'),
(28568, 'Reischach', 3009, 'BY', 82, 'DE', 48.29073000, 12.72620000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q256575'),
(28569, 'Reiskirchen', 3018, 'HE', 82, 'DE', 50.60000000, 8.83333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q387858'),
(28570, 'Reit im Winkl', 3009, 'BY', 82, 'DE', 47.67729000, 12.47086000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q252660'),
(28571, 'Rellingen', 3005, 'SH', 82, 'DE', 53.65000000, 9.81667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q542849'),
(28572, 'Remagen', 3019, 'RP', 82, 'DE', 50.57879000, 7.22703000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q486548'),
(28573, 'Remlingen', 3008, 'NI', 82, 'DE', 52.11367000, 10.67408000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q486548'),
(28574, 'Remlingen', 3009, 'BY', 82, 'DE', 49.80477000, 9.69484000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q532058'),
(28575, 'Remptendorf', 3015, 'TH', 82, 'DE', 50.53333000, 11.65000000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q529774'),
(28576, 'Remscheid', 3017, 'NW', 82, 'DE', 51.17983000, 7.19250000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q3097'),
(28577, 'Remse', 3021, 'SN', 82, 'DE', 50.85000000, 12.56667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q17128'),
(28578, 'Renchen', 3006, 'BW', 82, 'DE', 48.58850000, 8.01321000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q534613'),
(28579, 'Rendsburg', 3005, 'SH', 82, 'DE', 54.30663000, 9.66313000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q490457'),
(28580, 'Rengsdorf', 3019, 'RP', 82, 'DE', 50.50000000, 7.50000000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q566074'),
(28581, 'Rennerod', 3019, 'RP', 82, 'DE', 50.60820000, 8.06697000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q558712'),
(28582, 'Rennertshofen', 3009, 'BY', 82, 'DE', 48.75943000, 11.04544000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q505627'),
(28583, 'Renningen', 3006, 'BW', 82, 'DE', 48.76974000, 8.93871000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q550676'),
(28584, 'Rentweinsdorf', 3009, 'BY', 82, 'DE', 50.06342000, 10.79922000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q504659'),
(28585, 'Reppenstedt', 3008, 'NI', 82, 'DE', 53.25000000, 10.35000000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q631982'),
(28586, 'Retgendorf', 3007, 'MV', 82, 'DE', 53.72922000, 11.50359000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q631982'),
(28587, 'Rethem', 3008, 'NI', 82, 'DE', 52.78621000, 9.37862000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q145741'),
(28588, 'Rethen', 3008, 'NI', 82, 'DE', 52.37707000, 10.47855000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q468363'),
(28589, 'Retschow', 3007, 'MV', 82, 'DE', 54.04552000, 11.87780000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q468363'),
(28590, 'Rettenbach', 3009, 'BY', 82, 'DE', 49.06667000, 12.45000000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q468363'),
(28591, 'Rettenberg', 3009, 'BY', 82, 'DE', 47.57428000, 10.29173000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q529023'),
(28592, 'Retzstadt', 3009, 'BY', 82, 'DE', 49.91250000, 9.88194000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q529023'),
(28593, 'Reut', 3009, 'BY', 82, 'DE', 48.59170000, 13.12332000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q529023'),
(28594, 'Reuth', 3021, 'SN', 82, 'DE', 50.61667000, 12.21667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q32254731'),
(28595, 'Reuth', 3009, 'BY', 82, 'DE', 49.99052000, 11.69480000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q32254724'),
(28596, 'Reutlingen', 3006, 'BW', 82, 'DE', 48.49144000, 9.20427000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q3085'),
(28597, 'Reußen', 3011, 'ST', 82, 'DE', 51.50109000, 12.13044000, '2019-10-05 22:41:33', '2020-05-01 17:22:49', 1, 'Q2160228'),
(28598, 'Rhade', 3008, 'NI', 82, 'DE', 53.32872000, 9.11350000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q32255027'),
(28599, 'Rhaunen', 3019, 'RP', 82, 'DE', 49.86380000, 7.34198000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q569837'),
(28600, 'Rheda-Wiedenbrück', 3017, 'NW', 82, 'DE', 51.84967000, 8.30017000, '2019-10-05 22:41:33', '2020-05-01 17:22:49', 1, 'Q6896'),
(28601, 'Rhede', 3017, 'NW', 82, 'DE', 51.83537000, 6.69602000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q33475245'),
(28602, 'Rhede', 3008, 'NI', 82, 'DE', 53.06020000, 7.27127000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q33475245'),
(28603, 'Rheden', 3008, 'NI', 82, 'DE', 52.05784000, 9.78714000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q684246'),
(28604, 'Rheinau', 3006, 'BW', 82, 'DE', 48.66602000, 7.93659000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q539386'),
(28605, 'Rheinbach', 3017, 'NW', 82, 'DE', 50.62562000, 6.94911000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q12547'),
(28606, 'Rheinberg', 3017, 'NW', 82, 'DE', 51.54649000, 6.59525000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q16086'),
(28607, 'Rheinbreitbach', 3019, 'RP', 82, 'DE', 50.61667000, 7.23333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q566032'),
(28608, 'Rheinbrohl', 3019, 'RP', 82, 'DE', 50.50000000, 7.33333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q260293'),
(28609, 'Rheinböllen', 3019, 'RP', 82, 'DE', 50.01127000, 7.67249000, '2019-10-05 22:41:33', '2020-05-01 17:22:49', 1, 'Q166223'),
(28610, 'Rheine', 3017, 'NW', 82, 'DE', 52.28509000, 7.44055000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q166223'),
(28611, 'Rheinfelden', 3006, 'BW', 82, 'DE', 47.56013000, 7.78715000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q16077'),
(28612, 'Rheinsberg', 3013, 'BB', 82, 'DE', 53.09972000, 12.89885000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q537268'),
(28613, 'Rheinstetten', 3006, 'BW', 82, 'DE', 48.96850000, 8.30704000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q520595'),
(28614, 'Rheinzabern', 3019, 'RP', 82, 'DE', 49.11806000, 8.27806000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q22464'),
(28615, 'Rhens', 3019, 'RP', 82, 'DE', 50.28125000, 7.61750000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q552498'),
(28616, 'Rheurdt', 3017, 'NW', 82, 'DE', 51.46667000, 6.46667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q243206'),
(28617, 'Rhinow', 3013, 'BB', 82, 'DE', 52.75094000, 12.34194000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q532037'),
(28618, 'Rhodt unter Rietburg', 3019, 'RP', 82, 'DE', 49.26972000, 8.10778000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q627967'),
(28619, 'Rhumspringe', 3008, 'NI', 82, 'DE', 51.58333000, 10.30000000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q557454'),
(28620, 'Ribbesbüttel', 3008, 'NI', 82, 'DE', 52.43435000, 10.50997000, '2019-10-05 22:41:33', '2020-05-01 17:22:48', 1, 'Q671840'),
(28621, 'Ribnitz-Damgarten', 3007, 'MV', 82, 'DE', 54.24220000, 12.45666000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q571959'),
(28622, 'Richtenberg', 3007, 'MV', 82, 'DE', 54.20131000, 12.89409000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q571963'),
(28623, 'Rickenbach', 3006, 'BW', 82, 'DE', 47.61895000, 7.97873000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q571963'),
(28624, 'Rickert', 3005, 'SH', 82, 'DE', 54.33333000, 9.66667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q571963'),
(28625, 'Rickling', 3005, 'SH', 82, 'DE', 54.01667000, 10.16667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q571963'),
(28626, 'Ried', 3009, 'BY', 82, 'DE', 48.47698000, 11.26060000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q571963'),
(28627, 'Riede', 3008, 'NI', 82, 'DE', 52.96667000, 8.95000000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q505604'),
(28628, 'Rieden', 3019, 'RP', 82, 'DE', 50.40000000, 7.18333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q505604'),
(28629, 'Rieden', 3009, 'BY', 82, 'DE', 49.32361000, 11.94205000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q549110'),
(28630, 'Rieden an der Kötz', 3009, 'BY', 82, 'DE', 48.38499000, 10.25711000, '2019-10-05 22:41:33', '2020-05-01 17:22:48', 1, 'Q21103602'),
(28631, 'Riedenberg', 3009, 'BY', 82, 'DE', 50.31993000, 9.86100000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q510903'),
(28632, 'Riedenburg', 3009, 'BY', 82, 'DE', 48.96383000, 11.68880000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q503693'),
(28633, 'Rieder', 3011, 'ST', 82, 'DE', 51.73333000, 11.16667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q818998'),
(28634, 'Riederich', 3006, 'BW', 82, 'DE', 48.56029000, 9.26883000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q83072'),
(28635, 'Riedering', 3009, 'BY', 82, 'DE', 47.83874000, 12.20778000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q532692'),
(28636, 'Riedlingen', 3006, 'BW', 82, 'DE', 48.15455000, 9.47558000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q505967'),
(28637, 'Riedstadt', 3018, 'HE', 82, 'DE', 49.83411000, 8.49621000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q569656'),
(28638, 'Riegel', 3006, 'BW', 82, 'DE', 48.15111000, 7.74915000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q557790'),
(28639, 'Riegelsberg', 3020, 'SL', 82, 'DE', 49.30000000, 6.93333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q628341'),
(28640, 'Riegsee', 3009, 'BY', 82, 'DE', 47.69867000, 11.23391000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q628341'),
(28641, 'Riehl', 3017, 'NW', 82, 'DE', 50.96686000, 6.97572000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q883741'),
(28642, 'Rielasingen-Worblingen', 3006, 'BW', 82, 'DE', 47.73465000, 8.84013000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q62010'),
(28643, 'Rieneck', 3009, 'BY', 82, 'DE', 50.09349000, 9.64797000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q507487'),
(28644, 'Riepsdorf', 3005, 'SH', 82, 'DE', 54.22611000, 10.97242000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q507487'),
(28645, 'Riesa', 3021, 'SN', 82, 'DE', 51.30777000, 13.29168000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q8775'),
(28646, 'Rieschweiler-Mühlbach', 3019, 'RP', 82, 'DE', 49.23333000, 7.50000000, '2019-10-05 22:41:33', '2020-05-01 17:22:49', 1, 'Q22985'),
(28647, 'Rieseby', 3005, 'SH', 82, 'DE', 54.54140000, 9.81689000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q544462'),
(28648, 'Rieste', 3008, 'NI', 82, 'DE', 52.48333000, 8.01667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q649903'),
(28649, 'Riestedt', 3011, 'ST', 82, 'DE', 51.49496000, 11.36023000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q2152552'),
(28650, 'Rietberg', 3017, 'NW', 82, 'DE', 51.80924000, 8.42841000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q225621'),
(28651, 'Rietheim-Weilheim', 3006, 'BW', 82, 'DE', 48.01667000, 8.76667000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q552134'),
(28652, 'Riethnordhausen', 3015, 'TH', 82, 'DE', 51.08333000, 11.00000000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q552134'),
(28653, 'Rietschen', 3021, 'SN', 82, 'DE', 51.40000000, 14.78333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q502512'),
(28654, 'Rietz Neuendorf', 3013, 'BB', 82, 'DE', 52.22758000, 14.17463000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q502512'),
(28655, 'Rimbach', 3018, 'HE', 82, 'DE', 49.62500000, 8.76306000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q502512'),
(28656, 'Rimbach', 3009, 'BY', 82, 'DE', 49.23333000, 12.88333000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q502512'),
(28657, 'Rimpar', 3009, 'BY', 82, 'DE', 49.85692000, 9.95705000, '2019-10-05 22:41:33', '2019-10-05 22:41:33', 1, 'Q521160'),
(28658, 'Rimschweiler', 3019, 'RP', 82, 'DE', 49.21880000, 7.37571000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q521160'),
(28659, 'Rimsting', 3009, 'BY', 82, 'DE', 47.88078000, 12.33713000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q532970'),
(28660, 'Rinchnach', 3009, 'BY', 82, 'DE', 48.94956000, 13.20102000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q488641'),
(28661, 'Ringelai', 3009, 'BY', 82, 'DE', 48.81336000, 13.47130000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32258708'),
(28662, 'Ringleben', 3015, 'TH', 82, 'DE', 51.36667000, 11.21667000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32258708'),
(28663, 'Ringsheim', 3006, 'BW', 82, 'DE', 48.24765000, 7.77823000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q557798'),
(28664, 'Rinteln', 3008, 'NI', 82, 'DE', 52.18604000, 9.07917000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q498571'),
(28665, 'Riol', 3019, 'RP', 82, 'DE', 49.79301000, 6.79211000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q553441'),
(28666, 'Risum-Lindholm', 3005, 'SH', 82, 'DE', 54.75802000, 8.86906000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q28038'),
(28667, 'Ritterhude', 3008, 'NI', 82, 'DE', 53.18289000, 8.73550000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q657288'),
(28668, 'Rittersdorf', 3019, 'RP', 82, 'DE', 50.00000000, 6.50000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q657288'),
(28669, 'Rittersgrün', 3021, 'SN', 82, 'DE', 50.48031000, 12.79336000, '2019-10-05 22:41:34', '2020-05-01 17:22:49', 1, 'Q1284571'),
(28670, 'Rochlitz', 3021, 'SN', 82, 'DE', 51.05007000, 12.79754000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q71180'),
(28671, 'Rockenberg', 3018, 'HE', 82, 'DE', 50.43050000, 8.73688000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q617180'),
(28672, 'Rockenhausen', 3019, 'RP', 82, 'DE', 49.62974000, 7.82134000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q22955'),
(28673, 'Rodalben', 3019, 'RP', 82, 'DE', 49.23940000, 7.63962000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q536247'),
(28674, 'Roden', 3009, 'BY', 82, 'DE', 49.89556000, 9.62639000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q536247'),
(28675, 'Rodenbach', 3019, 'RP', 82, 'DE', 49.57414000, 8.10695000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32261974'),
(28676, 'Rodenbach', 3018, 'HE', 82, 'DE', 50.15000000, 9.03333000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32261974'),
(28677, 'Rodenberg', 3008, 'NI', 82, 'DE', 52.31152000, 9.35640000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q565492'),
(28678, 'Rodewisch', 3021, 'SN', 82, 'DE', 50.53083000, 12.41329000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q46900'),
(28679, 'Rodgau', 3018, 'HE', 82, 'DE', 50.02627000, 8.88588000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q7053'),
(28680, 'Roding', 3009, 'BY', 82, 'DE', 49.19426000, 12.51956000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, '*********'),
(28681, 'Rodleben', 3011, 'ST', 82, 'DE', 51.89607000, 12.20061000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, '********'),
(28682, 'Roetgen', 3017, 'NW', 82, 'DE', 50.65000000, 6.20000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, '*********'),
(28683, 'Roggenburg', 3009, 'BY', 82, 'DE', 48.27588000, 10.23136000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, '*********'),
(28684, 'Roggendorf', 3007, 'MV', 82, 'DE', 53.69292000, 11.01440000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, '*********'),
(28685, 'Roggentin', 3007, 'MV', 82, 'DE', 54.07070000, 12.20424000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, '*********'),
(28686, 'Rogätz', 3011, 'ST', 82, 'DE', 52.31667000, 11.76667000, '2019-10-05 22:41:34', '2020-05-01 17:22:49', 1, 'Q555036'),
(28687, 'Rohr', 3009, 'BY', 82, 'DE', 48.76768000, 11.97152000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q529002'),
(28688, 'Rohr', 3015, 'TH', 82, 'DE', 50.57644000, 10.49725000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q529002'),
(28689, 'Rohrbach', 3009, 'BY', 82, 'DE', 48.28933000, 12.55603000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32262614'),
(28690, 'Rohrbach', 3019, 'RP', 82, 'DE', 49.13667000, 8.12861000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32262614'),
(28691, 'Rohrdorf', 3009, 'BY', 82, 'DE', 47.79713000, 12.17010000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32262614'),
(28692, 'Rohrdorf', 3006, 'BW', 82, 'DE', 48.56667000, 8.70000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32262614'),
(28693, 'Rohrenfels', 3009, 'BY', 82, 'DE', 48.68654000, 11.15619000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q503825'),
(28694, 'Roigheim', 3006, 'BW', 82, 'DE', 49.36472000, 9.34000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q504910'),
(28695, 'Roitzsch', 3011, 'ST', 82, 'DE', 51.57526000, 12.26331000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q701886'),
(28696, 'Rommerskirchen', 3017, 'NW', 82, 'DE', 51.03333000, 6.68333000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q255235'),
(28697, 'Romrod', 3018, 'HE', 82, 'DE', 50.71341000, 9.22010000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q175771'),
(28698, 'Ronneburg', 3015, 'TH', 82, 'DE', 50.86340000, 12.18666000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32263553'),
(28699, 'Ronnenberg', 3008, 'NI', 82, 'DE', 52.31939000, 9.65544000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q555643'),
(28700, 'Ronsberg', 3009, 'BY', 82, 'DE', 47.89580000, 10.41571000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q511987'),
(28701, 'Ronshausen', 3018, 'HE', 82, 'DE', 50.95000000, 9.85000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q507500'),
(28702, 'Rosbach vor der Höhe', 3018, 'HE', 82, 'DE', 50.30332000, 8.68976000, '2019-10-05 22:41:34', '2020-05-01 17:22:48', 1, 'Q621237'),
(28703, 'Rosche', 3008, 'NI', 82, 'DE', 52.98663000, 10.75184000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q694339'),
(28704, 'Rosdorf', 3008, 'NI', 82, 'DE', 51.50000000, 9.90000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q630129'),
(28705, 'Rosenberg', 3006, 'BW', 82, 'DE', 49.01892000, 10.02960000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q630129'),
(28706, 'Rosenfeld', 3006, 'BW', 82, 'DE', 48.28639000, 8.72357000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q630129'),
(28707, 'Rosenheim', 3009, 'BY', 82, 'DE', 47.85637000, 12.12247000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q4007'),
(28708, 'Rosenow', 3007, 'MV', 82, 'DE', 53.62966000, 13.03849000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q50983'),
(28709, 'Rosenthal', 3018, 'HE', 82, 'DE', 50.97439000, 8.86736000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32264698'),
(28710, 'Rosenthal', 3010, 'BE', 82, 'DE', 52.59976000, 13.37774000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q693521'),
(28711, 'Rositz', 3015, 'TH', 82, 'DE', 51.01728000, 12.36354000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q34093'),
(28712, 'Roskow', 3013, 'BB', 82, 'DE', 52.47237000, 12.71886000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q34093'),
(28713, 'Rossau', 3021, 'SN', 82, 'DE', 51.00000000, 13.06667000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q34093'),
(28714, 'Rossbach', 3009, 'BY', 82, 'DE', 48.58333000, 12.95000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q34093'),
(28715, 'Rostock', 3007, 'MV', 82, 'DE', 54.08870000, 12.14049000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q34093'),
(28716, 'Rot', 3006, 'BW', 82, 'DE', 47.92278000, 9.10485000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q34093'),
(28717, 'Rot am See', 3006, 'BW', 82, 'DE', 49.25000000, 10.01667000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q81921'),
(28718, 'Rotenburg', 3008, 'NI', 82, 'DE', 53.11125000, 9.41082000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q81921'),
(28719, 'Rotenburg an der Fulda', 3018, 'HE', 82, 'DE', 50.99556000, 9.72838000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q558631'),
(28720, 'Roth', 3009, 'BY', 82, 'DE', 49.24762000, 11.09111000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q558631'),
(28721, 'Roth', 3019, 'RP', 82, 'DE', 50.76230000, 7.70016000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q558631'),
(28722, 'Rothenberg', 3018, 'HE', 82, 'DE', 49.49917000, 8.91917000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q634743'),
(28723, 'Rothenbuch', 3009, 'BY', 82, 'DE', 49.96528000, 9.39389000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q525824'),
(28724, 'Rothenburg', 3021, 'SN', 82, 'DE', 51.33400000, 14.96874000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32266509'),
(28725, 'Rothenburg ob der Tauber', 3009, 'BY', 82, 'DE', 49.37885000, 10.18711000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q153494'),
(28726, 'Rothenburgsort', 3016, 'HH', 82, 'DE', 53.53500000, 10.04082000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q1580'),
(28727, 'Rothenfels', 3009, 'BY', 82, 'DE', 49.89135000, 9.59260000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q628163'),
(28728, 'Rothenstein', 3015, 'TH', 82, 'DE', 50.85000000, 11.60000000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q628163'),
(28729, 'Rott', 3009, 'BY', 82, 'DE', 47.92008000, 12.08771000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q628163'),
(28730, 'Rottach-Egern', 3009, 'BY', 82, 'DE', 47.68966000, 11.77065000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q504255'),
(28731, 'Rottenacker', 3006, 'BW', 82, 'DE', 48.23485000, 9.68956000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q521356'),
(28732, 'Rottenbach', 3015, 'TH', 82, 'DE', 50.68782000, 11.16674000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32266950'),
(28733, 'Rottenbuch', 3009, 'BY', 82, 'DE', 47.73333000, 10.96667000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q32266967'),
(28734, 'Rottenburg', 3006, 'BW', 82, 'DE', 48.47629000, 8.93528000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q7078'),
(28735, 'Rottenburg an der Laaber', 3009, 'BY', 82, 'DE', 48.70233000, 12.02717000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q170243'),
(28736, 'Rottendorf', 3009, 'BY', 82, 'DE', 49.79230000, 10.02593000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q521196'),
(28737, 'Rotthalmünster', 3009, 'BY', 82, 'DE', 48.35825000, 13.20162000, '2019-10-05 22:41:34', '2020-05-01 17:22:48', 1, 'Q517994'),
(28738, 'Rottleberode', 3011, 'ST', 82, 'DE', 51.51636000, 10.94658000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q699137'),
(28739, 'Rottweil', 3006, 'BW', 82, 'DE', 48.16783000, 8.62719000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q82960'),
(28740, 'Roxheim', 3019, 'RP', 82, 'DE', 49.86374000, 7.80980000, '2019-10-05 22:41:34', '2019-10-05 22:41:34', 1, 'Q82960'),
(28741, 'Roßdorf', 3018, 'HE', 82, 'DE', 49.85972000, 8.76167000, '2019-10-05 22:41:34', '2020-05-01 17:22:48', 1, 'Q82960'),
(28742, 'Roßhaupten', 3009, 'BY', 82, 'DE', 48.42943000, 10.46911000, '2019-10-05 22:41:34', '2020-05-01 17:22:48', 1, 'Q82960'),
(28743, 'Roßla', 3011, 'ST', 82, 'DE', 51.46331000, 11.07576000, '2019-10-05 22:41:34', '2020-05-01 17:22:49', 1, 'Q699082'),
(28744, 'Roßlau', 3011, 'ST', 82, 'DE', 51.88736000, 12.24192000, '2019-10-05 22:41:34', '2020-05-01 17:22:49', 1, 'Q822148'),
(28745, 'Roßleben', 3015, 'TH', 82, 'DE', 51.29886000, 11.43435000, '2019-10-05 22:41:34', '2020-05-01 17:22:50', 1, 'Q558607'),
(28746, 'Roßtal', 3009, 'BY', 82, 'DE', 49.39567000, 10.88848000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q521661'),
(28747, 'Roßwein', 3021, 'SN', 82, 'DE', 51.06589000, 13.18308000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q71148'),
(28748, 'Rudelzhausen', 3009, 'BY', 82, 'DE', 48.60000000, 11.76667000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q507874'),
(28749, 'Ruderatshofen', 3009, 'BY', 82, 'DE', 47.81667000, 10.58333000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q512016'),
(28750, 'Rudersberg', 3006, 'BW', 82, 'DE', 48.88515000, 9.52927000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q81261'),
(28751, 'Ruderting', 3009, 'BY', 82, 'DE', 48.65000000, 13.41667000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q517953'),
(28752, 'Rudolstadt', 3015, 'TH', 82, 'DE', 50.72043000, 11.34046000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q517107'),
(28753, 'Rudow', 3010, 'BE', 82, 'DE', 52.42407000, 13.48529000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q614664'),
(28754, 'Rugendorf', 3009, 'BY', 82, 'DE', 50.20000000, 11.46667000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q504407'),
(28755, 'Ruhla', 3015, 'TH', 82, 'DE', 50.89296000, 10.36573000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q494627'),
(28756, 'Ruhland', 3013, 'BB', 82, 'DE', 51.45755000, 13.86643000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q572601'),
(28757, 'Ruhmannsfelden', 3009, 'BY', 82, 'DE', 48.98327000, 12.98347000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q489100'),
(28758, 'Ruhpolding', 3009, 'BY', 82, 'DE', 47.76667000, 12.65000000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q73532'),
(28759, 'Ruhstorf', 3009, 'BY', 82, 'DE', 48.53976000, 12.68305000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32269227'),
(28760, 'Ruhwinkel', 3005, 'SH', 82, 'DE', 54.10000000, 10.21667000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q636423'),
(28761, 'Rullstorf', 3008, 'NI', 82, 'DE', 53.28645000, 10.52972000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q632579'),
(28762, 'Rummelsburg', 3010, 'BE', 82, 'DE', 52.50146000, 13.49340000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q561880'),
(28763, 'Runding', 3009, 'BY', 82, 'DE', 49.21513000, 12.76208000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q520970'),
(28764, 'Runkel', 3018, 'HE', 82, 'DE', 50.40574000, 8.15457000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q635806'),
(28765, 'Ruppach-Goldhausen', 3019, 'RP', 82, 'DE', 50.46667000, 7.88333000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q646377'),
(28766, 'Ruppertsberg', 3019, 'RP', 82, 'DE', 49.40028000, 8.19611000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q617940'),
(28767, 'Ruppertshofen', 3006, 'BW', 82, 'DE', 48.88275000, 9.81506000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q617940'),
(28768, 'Ruppertsweiler', 3019, 'RP', 82, 'DE', 49.19707000, 7.68957000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q617940'),
(28769, 'Ruppichteroth', 3017, 'NW', 82, 'DE', 50.84367000, 7.48409000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q617940'),
(28770, 'Rust', 3006, 'BW', 82, 'DE', 48.26667000, 7.73333000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q617940'),
(28771, 'Rutesheim', 3006, 'BW', 82, 'DE', 48.80808000, 8.94536000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q617940'),
(28772, 'Räckelwitz', 3021, 'SN', 82, 'DE', 51.25000000, 14.23333000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q93285'),
(28773, 'Röbel', 3007, 'MV', 82, 'DE', 53.37555000, 12.60372000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q20265'),
(28774, 'Röblingen am See', 3011, 'ST', 82, 'DE', 51.45944000, 11.68231000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q689577'),
(28775, 'Röchling-Höhe', 3020, 'SL', 82, 'DE', 49.27102000, 6.83457000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q689577'),
(28776, 'Rödelsee', 3009, 'BY', 82, 'DE', 49.72840000, 10.24360000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q167661'),
(28777, 'Rödental', 3009, 'BY', 82, 'DE', 50.29516000, 11.04122000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q572701'),
(28778, 'Rödersheim-Gronau', 3019, 'RP', 82, 'DE', 49.43000000, 8.26139000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q654731'),
(28779, 'Rödinghausen', 3017, 'NW', 82, 'DE', 52.25000000, 8.48333000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q53913'),
(28780, 'Röfingen', 3009, 'BY', 82, 'DE', 48.42442000, 10.44268000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q511294'),
(28781, 'Röhrmoos', 3009, 'BY', 82, 'DE', 48.32966000, 11.44672000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q511495'),
(28782, 'Röhrnbach', 3009, 'BY', 82, 'DE', 48.73859000, 13.52271000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q503417'),
(28783, 'Röhrsen', 3008, 'NI', 82, 'DE', 52.71078000, 9.23139000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q33477076'),
(28784, 'Röllbach', 3009, 'BY', 82, 'DE', 49.77306000, 9.24611000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q512766'),
(28785, 'Römhild', 3015, 'TH', 82, 'DE', 50.39639000, 10.53889000, '2019-10-05 22:41:35', '2020-05-01 17:22:50', 1, 'Q535758'),
(28786, 'Röslau', 3009, 'BY', 82, 'DE', 50.08333000, 11.98333000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q513710'),
(28787, 'Rösrath', 3017, 'NW', 82, 'DE', 50.89559000, 7.18175000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q245376'),
(28788, 'Rötgesbüttel', 3008, 'NI', 82, 'DE', 52.41667000, 10.53333000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q503772'),
(28789, 'Rötha', 3021, 'SN', 82, 'DE', 51.19783000, 12.41447000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q10776'),
(28790, 'Röthenbach', 3009, 'BY', 82, 'DE', 47.62303000, 9.97387000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q537429'),
(28791, 'Röthenbach an der Pegnitz', 3009, 'BY', 82, 'DE', 49.48301000, 11.24116000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q537429'),
(28792, 'Röthlein', 3009, 'BY', 82, 'DE', 49.98333000, 10.21667000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q257890'),
(28793, 'Röttenbach', 3009, 'BY', 82, 'DE', 49.66429000, 10.92607000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q257890'),
(28794, 'Röttingen', 3009, 'BY', 82, 'DE', 49.50969000, 9.97082000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q506515'),
(28795, 'Rötz', 3009, 'BY', 82, 'DE', 49.34316000, 12.52963000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q506515'),
(28796, 'Rövershagen', 3007, 'MV', 82, 'DE', 54.17668000, 12.24276000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q33477191'),
(28797, 'Rübeland', 3011, 'ST', 82, 'DE', 51.75591000, 10.84660000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q679561'),
(28798, 'Rückersdorf', 3009, 'BY', 82, 'DE', 49.49757000, 11.24751000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q679561'),
(28799, 'Rückersdorf', 3013, 'BB', 82, 'DE', 51.56955000, 13.57226000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q679561'),
(28800, 'Rüdesheim', 3019, 'RP', 82, 'DE', 49.84546000, 7.81452000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q679561'),
(28801, 'Rüdesheim am Rhein', 3018, 'HE', 82, 'DE', 49.97890000, 7.92442000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q628118'),
(28802, 'Rüdnitz', 3013, 'BB', 82, 'DE', 52.72137000, 13.62502000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q628118'),
(28803, 'Rügland', 3009, 'BY', 82, 'DE', 49.40000000, 10.58333000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q504074'),
(28804, 'Rühen', 3008, 'NI', 82, 'DE', 52.48560000, 10.88642000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q640156'),
(28805, 'Rülzheim', 3019, 'RP', 82, 'DE', 49.15312000, 8.29287000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q23032'),
(28806, 'Rümmelsheim', 3019, 'RP', 82, 'DE', 49.93307000, 7.85977000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q654707'),
(28807, 'Rümmingen', 3006, 'BW', 82, 'DE', 47.64120000, 7.64198000, '2019-10-05 22:41:35', '2020-05-01 17:22:47', 1, 'Q62027'),
(28808, 'Rümpel', 3005, 'SH', 82, 'DE', 53.78333000, 10.35000000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q659428'),
(28809, 'Rüsselsheim', 3018, 'HE', 82, 'DE', 49.98955000, 8.42251000, '2019-10-05 22:41:35', '2020-05-01 17:22:48', 1, 'Q4031'),
(28810, 'Rüthen', 3017, 'NW', 82, 'DE', 51.49090000, 8.43596000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q33477350'),
(28811, 'Saal', 3007, 'MV', 82, 'DE', 54.31051000, 12.49935000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32273373'),
(28812, 'Saal', 3009, 'BY', 82, 'DE', 48.90099000, 11.93196000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32273365'),
(28813, 'Saal an der Saale', 3009, 'BY', 82, 'DE', 50.31673000, 10.35769000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q566558'),
(28814, 'Saalfeld', 3015, 'TH', 82, 'DE', 50.64826000, 11.36536000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q155984'),
(28815, 'Saara', 3015, 'TH', 82, 'DE', 50.93284000, 12.42096000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32273576'),
(28816, 'Saarbrücken', 3020, 'SL', 82, 'DE', 49.23262000, 7.00982000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q1724'),
(28817, 'Saarburg', 3019, 'RP', 82, 'DE', 49.60641000, 6.54365000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q543639'),
(28818, 'Saarhölzbach', 3020, 'SL', 82, 'DE', 49.51585000, 6.60742000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q543639'),
(28819, 'Saarlouis', 3020, 'SL', 82, 'DE', 49.31366000, 6.75154000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q14912'),
(28820, 'Saarwellingen', 3020, 'SL', 82, 'DE', 49.35430000, 6.80487000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q555648'),
(28821, 'Sachsen', 3009, 'BY', 82, 'DE', 49.28999000, 10.65971000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32273906'),
(28822, 'Sachsenhagen', 3008, 'NI', 82, 'DE', 52.39734000, 9.26791000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q628865'),
(28823, 'Sachsenhausen', 3018, 'HE', 82, 'DE', 51.24338000, 9.00973000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q628865'),
(28824, 'Sachsenheim', 3006, 'BW', 82, 'DE', 48.96000000, 9.06472000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q628865'),
(28825, 'Sachsenkam', 3009, 'BY', 82, 'DE', 47.80543000, 11.64396000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q834894'),
(28826, 'Saerbeck', 3017, 'NW', 82, 'DE', 52.17372000, 7.63395000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32274297'),
(28827, 'Saffig', 3019, 'RP', 82, 'DE', 50.38333000, 7.41667000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q654480'),
(28828, 'Sagard', 3007, 'MV', 82, 'DE', 54.52556000, 13.55387000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q551205'),
(28829, 'Sailauf', 3009, 'BY', 82, 'DE', 50.02461000, 9.25932000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q512943'),
(28830, 'Salach', 3006, 'BW', 82, 'DE', 48.69198000, 9.73715000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q80630'),
(28831, 'Salching', 3009, 'BY', 82, 'DE', 48.81047000, 12.57043000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q559925'),
(28832, 'Saldenburg', 3009, 'BY', 82, 'DE', 48.77310000, 13.35586000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32274880'),
(28833, 'Salem', 3006, 'BW', 82, 'DE', 47.76258000, 9.29031000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32274880');

