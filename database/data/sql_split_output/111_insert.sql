INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(55814, 'Perkáta', 1044, 'FE', 99, 'HU', 47.04701000, 18.78734000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q339890'),
(55815, 'Petneháza', 1045, 'SZ', 99, 'HU', 48.06084000, 22.07485000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1079827'),
(55816, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 1040, 'HE', 99, 'HU', 47.76960000, 19.69988000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185245'),
(55817, '<PERSON><PERSON>', 1059, 'P<PERSON>', 99, 'HU', 47.28904000, 19.54848000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q845575'),
(55818, '<PERSON><PERSON><PERSON><PERSON><PERSON>n<PERSON>', 1059, 'PE', 99, 'HU', 47.60744000, 18.99322000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q934564'),
(55819, 'Piliscsaba', 1059, 'PE', 99, 'HU', 47.63417000, 18.82886000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1076130'),
(55820, 'Pilisszentiván', 1059, 'PE', 99, 'HU', 47.60964000, 18.89940000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1186565'),
(55821, 'Pilisszentkereszt', 1059, 'PE', 99, 'HU', 47.69143000, 18.90503000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1095184'),
(55822, 'Pilisszántó', 1059, 'PE', 99, 'HU', 47.66909000, 18.88762000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1236038'),
(55823, 'Pilisvörösvár', 1059, 'PE', 99, 'HU', 47.61386000, 18.90893000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q530634'),
(55824, 'Pilisvörösvári Járás', 1059, 'PE', 99, 'HU', 47.60925000, 18.86658000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q853874'),
(55825, 'Pincehely', 1038, 'TO', 99, 'HU', 46.68095000, 18.43935000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1072159'),
(55826, 'Pocsaj', 1063, 'HB', 99, 'HU', 47.28333000, 21.81667000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q281575'),
(55827, 'Polgár', 1063, 'HB', 99, 'HU', 47.86667000, 21.11667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q2419449'),
(55828, 'Polgárdi', 1044, 'FE', 99, 'HU', 47.06099000, 18.30200000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q932806'),
(55829, 'Pomáz', 1059, 'PE', 99, 'HU', 47.64227000, 19.02784000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q842923'),
(55830, 'Porcsalma', 1045, 'SZ', 99, 'HU', 47.88333000, 22.56667000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q768702'),
(55831, 'Poroszló', 1040, 'HE', 99, 'HU', 47.65000000, 20.66667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q780264'),
(55832, 'Prügy', 1058, 'BZ', 99, 'HU', 48.08333000, 21.25000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q368355'),
(55833, 'Pusztaföldvár', 1060, 'BE', 99, 'HU', 46.53333000, 20.80000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1075542'),
(55834, 'Pusztaszabolcs', 1044, 'FE', 99, 'HU', 47.13718000, 18.76704000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q935250'),
(55835, 'Pusztaszer', 1031, 'CS', 99, 'HU', 46.55083000, 19.98823000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q977829'),
(55836, 'Pusztavám', 1044, 'FE', 99, 'HU', 47.42948000, 18.22648000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1095334'),
(55837, 'Putnok', 1058, 'BZ', 99, 'HU', 48.30000000, 20.43333000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q911558'),
(55838, 'Putnoki Járás', 1058, 'BZ', 99, 'HU', 48.33694000, 20.46384000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14371286'),
(55839, 'Pákozd', 1044, 'FE', 99, 'HU', 47.21369000, 18.53306000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1084400'),
(55840, 'Pálmonostora', 1048, 'BK', 99, 'HU', 46.62417000, 19.95156000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q247862'),
(55841, 'Pánd', 1059, 'PE', 99, 'HU', 47.35333000, 19.63571000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1186528'),
(55842, 'Pápa', 1054, 'VE', 99, 'HU', 47.33004000, 17.46740000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q214945'),
(55843, 'Pápai Járás', 1054, 'VE', 99, 'HU', 47.33769000, 17.49663000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q238748'),
(55844, 'Pásztó', 1051, 'NO', 99, 'HU', 47.92019000, 19.69829000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q637647'),
(55845, 'Pásztói Járás', 1051, 'NO', 99, 'HU', 47.87012000, 19.60648000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q921286'),
(55846, 'Pátroha', 1045, 'SZ', 99, 'HU', 48.16667000, 22.00000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1088821'),
(55847, 'Páty', 1059, 'PE', 99, 'HU', 47.51709000, 18.82851000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1186235'),
(55848, 'Pázmánd', 1044, 'FE', 99, 'HU', 47.28755000, 18.65356000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q372938'),
(55849, 'Pécel', 1059, 'PE', 99, 'HU', 47.48962000, 19.34162000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q222964'),
(55850, 'Pécs', 1055, 'BA', 99, 'HU', 46.08333000, 18.23333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q45779'),
(55851, 'Pécsi Járás', 1055, 'BA', 99, 'HU', 46.07990000, 18.25253000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q594019'),
(55852, 'Pécsvárad', 1055, 'BA', 99, 'HU', 46.16033000, 18.42321000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q842996'),
(55853, 'Pécsváradi Járás', 1055, 'BA', 99, 'HU', 46.16782000, 18.45456000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q615758'),
(55854, 'Pér', 1042, 'GS', 99, 'HU', 47.61153000, 17.80632000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q426000'),
(55855, 'Péteri', 1059, 'PE', 99, 'HU', 47.39104000, 19.40981000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1186205'),
(55856, 'Pétervására', 1040, 'HE', 99, 'HU', 48.01667000, 20.10000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q185719'),
(55857, 'Pétervásárai Járás', 1040, 'HE', 99, 'HU', 48.00589000, 20.09466000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q733824'),
(55858, 'Pétfürdő', 1054, 'VE', 99, 'HU', 47.16667000, 18.11667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q515474'),
(55859, 'Püspökladány', 1063, 'HB', 99, 'HU', 47.31667000, 21.11667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q842934'),
(55860, 'Püspökladányi Járás', 1063, 'HB', 99, 'HU', 47.31279000, 21.21177000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q831002'),
(55861, 'Rajka', 1042, 'GS', 99, 'HU', 47.99643000, 17.19821000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1009081'),
(55862, 'Rakamaz', 1045, 'SZ', 99, 'HU', 48.12372000, 21.46429000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q911541'),
(55863, 'Recsk', 1040, 'HE', 99, 'HU', 47.93333000, 20.11667000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q582533'),
(55864, 'Ricse', 1058, 'BZ', 99, 'HU', 48.32565000, 21.97069000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q586702'),
(55865, 'Rimóc', 1051, 'NO', 99, 'HU', 48.03695000, 19.53010000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1186152'),
(55866, 'Romhány', 1051, 'NO', 99, 'HU', 47.92618000, 19.25723000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q339421'),
(55867, 'Rudabánya', 1058, 'BZ', 99, 'HU', 48.38333000, 20.63333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1008635'),
(55868, 'Ruzsa', 1031, 'CS', 99, 'HU', 46.28806000, 19.74714000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1184414'),
(55869, 'Rábapatona', 1042, 'GS', 99, 'HU', 47.63224000, 17.48004000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q367101'),
(55870, 'Rácalmás', 1044, 'FE', 99, 'HU', 47.02263000, 18.94056000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1075327'),
(55871, 'Ráckeresztúr', 1044, 'FE', 99, 'HU', 47.27360000, 18.83343000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q261855'),
(55872, 'Ráckeve', 1059, 'PE', 99, 'HU', 47.16095000, 18.94478000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q755701'),
(55873, 'Ráckevei Járás', 1059, 'PE', 99, 'HU', 47.15158000, 19.01456000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q538324'),
(55874, 'Rákóczifalva', 1043, 'JN', 99, 'HU', 47.08333000, 20.23333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q945191'),
(55875, 'Rákócziújfalu', 1043, 'JN', 99, 'HU', 47.06667000, 20.26667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185313'),
(55876, 'Répcelak', 1039, 'VA', 99, 'HU', 47.42105000, 17.01795000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q287306'),
(55877, 'Rétság', 1051, 'NO', 99, 'HU', 47.92816000, 19.13720000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q911576'),
(55878, 'Rétsági Járás', 1051, 'NO', 99, 'HU', 47.91741000, 19.16101000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q776225'),
(55879, 'Révfülöp', 1054, 'VE', 99, 'HU', 46.82573000, 17.61967000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1089659'),
(55880, 'Rózsaszentmárton', 1040, 'HE', 99, 'HU', 47.78200000, 19.74210000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1072238'),
(55881, 'Röszke', 1031, 'CS', 99, 'HU', 46.18796000, 20.03372000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q682563'),
(55882, 'Sajóbábony', 1058, 'BZ', 99, 'HU', 48.16667000, 20.73333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1008708'),
(55883, 'Sajókaza', 1058, 'BZ', 99, 'HU', 48.28333000, 20.58333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1016519'),
(55884, 'Sajólád', 1058, 'BZ', 99, 'HU', 48.05000000, 20.90000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q282953'),
(55885, 'Sajószentpéter', 1058, 'BZ', 99, 'HU', 48.21667000, 20.71667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q855086'),
(55886, 'Sajószöged', 1058, 'BZ', 99, 'HU', 47.95000000, 21.00000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q263570'),
(55887, 'Sajóvámos', 1058, 'BZ', 99, 'HU', 48.18171000, 20.83149000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q689099'),
(55888, 'Sajóörös', 1058, 'BZ', 99, 'HU', 47.95000000, 21.03333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q970031'),
(55889, 'Salgótarján', 1051, 'NO', 99, 'HU', 48.09872000, 19.80303000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q189769'),
(55890, 'Salgótarjáni Járás', 1051, 'NO', 99, 'HU', 48.10048000, 19.81548000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q285489'),
(55891, 'Sarkad', 1060, 'BE', 99, 'HU', 46.75000000, 21.38333000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q188247'),
(55892, 'Sarkadi Járás', 1060, 'BE', 99, 'HU', 46.86154000, 21.44875000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14629787'),
(55893, 'Segesd', 1035, 'SO', 99, 'HU', 46.34142000, 17.35132000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q938008'),
(55894, 'Sellye', 1055, 'BA', 99, 'HU', 45.87247000, 17.84711000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q842971'),
(55895, 'Sellyei Járás', 1055, 'BA', 99, 'HU', 45.86955000, 17.89827000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1070194'),
(55896, 'Seregélyes', 1044, 'FE', 99, 'HU', 47.11050000, 18.56500000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q920824'),
(55897, 'Siklós', 1055, 'BA', 99, 'HU', 45.85499000, 18.29752000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q728438'),
(55898, 'Siklósi Járás', 1055, 'BA', 99, 'HU', 45.84981000, 18.31052000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q667734'),
(55899, 'Simontornya', 1038, 'TO', 99, 'HU', 46.75462000, 18.55490000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q911674'),
(55900, 'Sirok', 1040, 'HE', 99, 'HU', 47.93333000, 20.20000000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1089593'),
(55901, 'Siófok', 1035, 'SO', 99, 'HU', 46.90413000, 18.05800000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q245126'),
(55902, 'Siófoki Járás', 1035, 'SO', 99, 'HU', 46.85060000, 17.99625000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q853254'),
(55903, 'Solt', 1048, 'BK', 99, 'HU', 46.80101000, 19.00098000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q855073'),
(55904, 'Soltvadkert', 1048, 'BK', 99, 'HU', 46.57889000, 19.39389000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q854987'),
(55905, 'Solymár', 1059, 'PE', 99, 'HU', 47.59246000, 18.93212000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1184326'),
(55906, 'Somogyvár', 1035, 'SO', 99, 'HU', 46.58140000, 17.66289000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1074133'),
(55907, 'Somoskőújfalu', 1051, 'NO', 99, 'HU', 48.16374000, 19.82303000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q930566'),
(55908, 'Soponya', 1044, 'FE', 99, 'HU', 47.01485000, 18.45343000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1084395'),
(55909, 'Sopron', 1042, 'GS', 99, 'HU', 47.68501000, 16.59049000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q168648'),
(55910, 'Soproni Járás', 1042, 'GS', 99, 'HU', 47.60385000, 16.75654000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q694723'),
(55911, 'Szabadbattyán', 1044, 'FE', 99, 'HU', 47.11902000, 18.36823000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1095006'),
(55912, 'Szabadkígyós', 1060, 'BE', 99, 'HU', 46.61667000, 21.10000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1076471'),
(55913, 'Szabadszállás', 1048, 'BK', 99, 'HU', 46.87575000, 19.22324000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q855001'),
(55914, 'Szada', 1059, 'PE', 99, 'HU', 47.63333000, 19.31667000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q576287'),
(55915, 'Szajol', 1043, 'JN', 99, 'HU', 47.18333000, 20.30000000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1185433'),
(55916, 'Szakoly', 1045, 'SZ', 99, 'HU', 47.76667000, 21.91667000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1186240'),
(55917, 'Szalkszentmárton', 1048, 'BK', 99, 'HU', 46.97565000, 19.01178000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1063925'),
(55918, 'Szamosszeg', 1045, 'SZ', 99, 'HU', 48.04561000, 22.36582000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1186706'),
(55919, 'Szank', 1048, 'BK', 99, 'HU', 46.55713000, 19.66103000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q144121'),
(55920, 'Szany', 1042, 'GS', 99, 'HU', 47.46423000, 17.30402000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1184662'),
(55921, 'Szarvas', 1060, 'BE', 99, 'HU', 46.86667000, 20.55000000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q843014'),
(55922, 'Szarvasi Járás', 1060, 'BE', 99, 'HU', 46.82891000, 20.63707000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14629803'),
(55923, 'Szatymaz', 1031, 'CS', 99, 'HU', 46.34306000, 20.04020000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1184341'),
(55924, 'Szedres', 1038, 'TO', 99, 'HU', 46.47551000, 18.68305000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1184310'),
(55925, 'Szeged', 1031, 'CS', 99, 'HU', 46.25300000, 20.14824000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q81581'),
(55926, 'Szegedi Járás', 1031, 'CS', 99, 'HU', 46.29536000, 20.13758000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q260215'),
(55927, 'Szeghalmi Járás', 1060, 'BE', 99, 'HU', 47.00000000, 21.17000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14629807'),
(55928, 'Szeghalom', 1060, 'BE', 99, 'HU', 47.03333000, 21.16667000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q780731'),
(55929, 'Szegvár', 1031, 'CS', 99, 'HU', 46.58740000, 20.22408000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q594757'),
(55930, 'Szekszárd', 1038, 'TO', 99, 'HU', 46.35014000, 18.70905000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q189761'),
(55931, 'Szekszárdi Járás', 1038, 'TO', 99, 'HU', 46.33998000, 18.67893000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q668323'),
(55932, 'Szelevény', 1043, 'JN', 99, 'HU', 46.80259000, 20.20283000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q668323'),
(55933, 'Szendrő', 1058, 'BZ', 99, 'HU', 48.40000000, 20.73333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q590583'),
(55934, 'Szentendre', 1059, 'PE', 99, 'HU', 47.66943000, 19.07561000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q390798'),
(55935, 'Szentendrei Járás', 1059, 'PE', 99, 'HU', 47.71146000, 19.02524000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q854326'),
(55936, 'Szentes', 1031, 'CS', 99, 'HU', 46.65834000, 20.26080000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q377834'),
(55937, 'Szentesi Járás', 1031, 'CS', 99, 'HU', 46.66005000, 20.37314000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q921275'),
(55938, 'Szentgotthárd', 1039, 'VA', 99, 'HU', 46.95261000, 16.27358000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q604516'),
(55939, 'Szentgotthárdi Járás', 1039, 'VA', 99, 'HU', 46.93674000, 16.30362000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q830857'),
(55940, 'Szentgálpuszta', 1038, 'TO', 99, 'HU', 46.37479000, 18.62601000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q830857'),
(55941, 'Szentistván', 1058, 'BZ', 99, 'HU', 47.76667000, 20.66667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q248474'),
(55942, 'Szentkirály', 1048, 'BK', 99, 'HU', 46.91892000, 19.91846000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1076071'),
(55943, 'Szentkirályszabadja', 1054, 'VE', 99, 'HU', 47.05760000, 17.97052000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1089978'),
(55944, 'Szentlőrinc', 1055, 'BA', 99, 'HU', 46.04016000, 17.98719000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q578343'),
(55945, 'Szentlőrinci Járás', 1055, 'BA', 99, 'HU', 46.04694000, 18.01528000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q923858'),
(55946, 'Szentlőrinckáta', 1059, 'PE', 99, 'HU', 47.51947000, 19.75286000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1186512'),
(55947, 'Szentmártonkáta', 1059, 'PE', 99, 'HU', 47.45419000, 19.70143000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1186519'),
(55948, 'Szerencs', 1058, 'BZ', 99, 'HU', 48.15993000, 21.20970000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q854996'),
(55949, 'Szerencsi Járás', 1058, 'BZ', 99, 'HU', 48.14929000, 21.16865000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q633324'),
(55950, 'Szigetcsép', 1059, 'PE', 99, 'HU', 47.26492000, 18.97048000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1076191'),
(55951, 'Szigethalom', 1059, 'PE', 99, 'HU', 47.32228000, 19.00262000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q784229'),
(55952, 'Szigetszentmiklós', 1059, 'PE', 99, 'HU', 47.34382000, 19.04335000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q740853'),
(55953, 'Szigetszentmiklósi Járás', 1059, 'PE', 99, 'HU', 47.30566000, 19.02829000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14942540'),
(55954, 'Szigetvár', 1055, 'BA', 99, 'HU', 46.04865000, 17.80554000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q373542'),
(55955, 'Szigetvári Járás', 1055, 'BA', 99, 'HU', 46.07336000, 17.80391000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1070189'),
(55956, 'Szigetújfalu', 1059, 'PE', 99, 'HU', 47.23417000, 18.92746000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q531717'),
(55957, 'Szihalom', 1040, 'HE', 99, 'HU', 47.76667000, 20.48333000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q782269'),
(55958, 'Szikszó', 1058, 'BZ', 99, 'HU', 48.20000000, 20.93333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q854913'),
(55959, 'Szikszói Járás', 1058, 'BZ', 99, 'HU', 48.30032000, 20.95507000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q854336'),
(55960, 'Szilvásvárad', 1040, 'HE', 99, 'HU', 48.10000000, 20.40000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1089496'),
(55961, 'Szirmabesenyő', 1058, 'BZ', 99, 'HU', 48.15000000, 20.80000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q248566'),
(55962, 'Szob', 1059, 'PE', 99, 'HU', 47.81921000, 18.87020000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q429409'),
(55963, 'Szobi Járás', 1059, 'PE', 99, 'HU', 47.93419000, 18.85438000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q853088'),
(55964, 'Szolnok', 1043, 'JN', 99, 'HU', 47.18333000, 20.20000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q181276'),
(55965, 'Szolnoki Járás', 1043, 'JN', 99, 'HU', 47.22480000, 20.19839000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q831079'),
(55966, 'Szombathely', 1039, 'VA', 99, 'HU', 47.23088000, 16.62155000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q42007'),
(55967, 'Szombathelyi Járás', 1039, 'VA', 99, 'HU', 47.21037000, 16.63954000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q830871'),
(55968, 'Szurdokpüspöki', 1051, 'NO', 99, 'HU', 47.85923000, 19.69218000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186325'),
(55969, 'Szárliget', 1044, 'FE', 99, 'HU', 47.51581000, 18.49480000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186590'),
(55970, 'Szászvár', 1055, 'BA', 99, 'HU', 46.27673000, 18.37566000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q430096'),
(55971, 'Százhalombatta', 1059, 'PE', 99, 'HU', 47.32949000, 18.93878000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q773203'),
(55972, 'Szécsény', 1051, 'NO', 99, 'HU', 48.08057000, 19.52019000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q854918'),
(55973, 'Szécsényi Járás', 1051, 'NO', 99, 'HU', 48.07301000, 19.55156000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q667225'),
(55974, 'Székesfehérvár', 1044, 'FE', 99, 'HU', 47.18995000, 18.41034000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q130212'),
(55975, 'Székesfehérvári Járás', 1044, 'FE', 99, 'HU', 47.16531000, 18.41349000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q853167'),
(55976, 'Székkutas', 1031, 'CS', 99, 'HU', 46.50000000, 20.53333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q938117'),
(55977, 'Sződ', 1059, 'PE', 99, 'HU', 47.72439000, 19.17046000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, '********'),
(55978, 'Sződliget', 1059, 'PE', 99, 'HU', 47.73259000, 19.14749000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q659772'),
(55979, 'Sály', 1058, 'BZ', 99, 'HU', 47.95000000, 20.66667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q607253'),
(55980, 'Sándorfalva', 1031, 'CS', 99, 'HU', 46.36087000, 20.09889000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q933775'),
(55981, 'Sárbogárd', 1044, 'FE', 99, 'HU', 46.88692000, 18.62041000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q593314'),
(55982, 'Sárbogárdi Járás', 1044, 'FE', 99, 'HU', 46.84036000, 18.60723000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q852250'),
(55983, 'Sárkeresztúr', 1044, 'FE', 99, 'HU', 47.00540000, 18.54391000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, '********'),
(55984, 'Sármellék', 1046, 'ZA', 99, 'HU', 46.71221000, 17.16865000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, '********'),
(55985, 'Sárosd', 1044, 'FE', 99, 'HU', 47.04273000, 18.64357000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1084388'),
(55986, 'Sárospatak', 1058, 'BZ', 99, 'HU', 48.32450000, 21.57383000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q668318'),
(55987, 'Sárospataki Járás', 1058, 'BZ', 99, 'HU', 48.29575000, 21.52716000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q853145'),
(55988, 'Sárrétudvari', 1063, 'HB', 99, 'HU', 47.23333000, 21.20000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1021667'),
(55989, 'Sárszentmihály', 1044, 'FE', 99, 'HU', 47.15321000, 18.33879000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1084379'),
(55990, 'Sárvár', 1039, 'VA', 99, 'HU', 47.25395000, 16.93525000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q843043'),
(55991, 'Sárvári Járás', 1039, 'VA', 99, 'HU', 47.23868000, 16.93299000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q831501'),
(55992, 'Sáránd', 1063, 'HB', 99, 'HU', 47.40000000, 21.63333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1185440'),
(55993, 'Sásd', 1055, 'BA', 99, 'HU', 46.25520000, 18.10776000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1185440'),
(55994, 'Sátoraljaújhely', 1058, 'BZ', 99, 'HU', 48.39492000, 21.65871000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q116417'),
(55995, 'Sátoraljaújhelyi Járás', 1058, 'BZ', 99, 'HU', 48.45898000, 21.54394000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q630821'),
(55996, 'Sóskút', 1059, 'PE', 99, 'HU', 47.40665000, 18.82247000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q590540'),
(55997, 'Sükösd', 1048, 'BK', 99, 'HU', 46.28181000, 18.99524000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q221288'),
(55998, 'Sülysáp', 1059, 'PE', 99, 'HU', 47.45206000, 19.53369000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1072216'),
(55999, 'Sümeg', 1054, 'VE', 99, 'HU', 46.97703000, 17.28209000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q680656'),
(56000, 'Sümegi Járás', 1054, 'VE', 99, 'HU', 47.02648000, 17.26779000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q176677'),
(56001, 'Tab', 1035, 'SO', 99, 'HU', 46.73135000, 18.03201000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q302526'),
(56002, 'Tabi Járás', 1035, 'SO', 99, 'HU', 46.68037000, 18.00560000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q853866'),
(56003, 'Tahitótfalu', 1059, 'PE', 99, 'HU', 47.75000000, 19.10000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q967910'),
(56004, 'Taksony', 1059, 'PE', 99, 'HU', 47.32968000, 19.06695000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q921821'),
(56005, 'Taktaharkány', 1058, 'BZ', 99, 'HU', 48.08333000, 21.13333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1089620'),
(56006, 'Taktaszada', 1058, 'BZ', 99, 'HU', 48.11667000, 21.18333000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q123719'),
(56007, 'Tamási', 1038, 'TO', 99, 'HU', 46.63333000, 18.28333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q607625'),
(56008, 'Tamási Járás', 1038, 'TO', 99, 'HU', 46.65424000, 18.35857000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q606365'),
(56009, 'Tapolca', 1054, 'VE', 99, 'HU', 46.88152000, 17.44117000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q288413'),
(56010, 'Tapolcai Járás', 1054, 'VE', 99, 'HU', 46.89833000, 17.49510000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q830847'),
(56011, 'Tar', 1051, 'NO', 99, 'HU', 47.95371000, 19.74610000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q429772'),
(56012, 'Tarcal', 1058, 'BZ', 99, 'HU', 48.13333000, 21.35000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q923050'),
(56013, 'Tarnalelesz', 1040, 'HE', 99, 'HU', 48.05000000, 20.18333000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1185533'),
(56014, 'Tarnaörs', 1040, 'HE', 99, 'HU', 47.59499000, 20.05254000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1072281'),
(56015, 'Tarpa', 1045, 'SZ', 99, 'HU', 48.10480000, 22.53744000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q369571'),
(56016, 'Tass', 1048, 'BK', 99, 'HU', 47.02095000, 19.02988000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q651978'),
(56017, 'Taszár', 1035, 'SO', 99, 'HU', 46.37467000, 17.90594000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1187131'),
(56018, 'Telki', 1059, 'PE', 99, 'HU', 47.54791000, 18.82816000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q571329'),
(56019, 'Tengelic', 1038, 'TO', 99, 'HU', 46.52878000, 18.71117000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q642349'),
(56020, 'Tihany', 1054, 'VE', 99, 'HU', 46.91369000, 17.88918000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q75732'),
(56021, 'Tiszaalpár', 1048, 'BK', 99, 'HU', 46.81279000, 19.99841000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q914786'),
(56022, 'Tiszabercel', 1045, 'SZ', 99, 'HU', 48.15000000, 21.65000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1187137'),
(56023, 'Tiszabezdéd', 1045, 'SZ', 99, 'HU', 48.36667000, 22.15000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q742840'),
(56024, 'Tiszabura', 1043, 'JN', 99, 'HU', 47.45000000, 20.46667000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1185499'),
(56025, 'Tiszabő', 1043, 'JN', 99, 'HU', 47.30000000, 20.48333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q634388'),
(56026, 'Tiszacsege', 1063, 'HB', 99, 'HU', 47.70000000, 21.00000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q962014'),
(56027, 'Tiszadada', 1045, 'SZ', 99, 'HU', 48.03333000, 21.25000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1187150'),
(56028, 'Tiszadob', 1045, 'SZ', 99, 'HU', 48.01667000, 21.16667000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q253508'),
(56029, 'Tiszaeszlár', 1045, 'SZ', 99, 'HU', 48.05000000, 21.46667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q429997'),
(56030, 'Tiszaföldvár', 1043, 'JN', 99, 'HU', 46.98333000, 20.25000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q541550'),
(56031, 'Tiszafüred', 1043, 'JN', 99, 'HU', 47.61667000, 20.76667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q903177'),
(56032, 'Tiszafüredi Járás', 1043, 'JN', 99, 'HU', 47.53907000, 20.78879000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q628690'),
(56033, 'Tiszakarád', 1058, 'BZ', 99, 'HU', 48.20000000, 21.71667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q572103'),
(56034, 'Tiszakeszi', 1058, 'BZ', 99, 'HU', 47.78333000, 21.00000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q368964'),
(56035, 'Tiszakécske', 1048, 'BK', 99, 'HU', 46.93261000, 20.10349000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q841603'),
(56036, 'Tiszakécskei Járás', 1048, 'BK', 99, 'HU', 46.86013000, 19.97298000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q14427041'),
(56037, 'Tiszalök', 1045, 'SZ', 99, 'HU', 48.01667000, 21.38333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q615233'),
(56038, 'Tiszalúc', 1058, 'BZ', 99, 'HU', 48.03774000, 21.07261000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q239610'),
(56039, 'Tiszanagyfalu', 1045, 'SZ', 99, 'HU', 48.10000000, 21.48333000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1028269'),
(56040, 'Tiszanána', 1040, 'HE', 99, 'HU', 47.56667000, 20.53333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q543964'),
(56041, 'Tiszapüspöki', 1043, 'JN', 99, 'HU', 47.21667000, 20.31667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q379221'),
(56042, 'Tiszaroff', 1043, 'JN', 99, 'HU', 47.40000000, 20.45000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1185187'),
(56043, 'Tiszaszentimre', 1043, 'JN', 99, 'HU', 47.48333000, 20.73333000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q937701'),
(56044, 'Tiszaszőlős', 1043, 'JN', 99, 'HU', 47.55707000, 20.71949000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q937701'),
(56045, 'Tiszasüly', 1043, 'JN', 99, 'HU', 47.38333000, 20.40000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1047658'),
(56046, 'Tiszavasvári', 1045, 'SZ', 99, 'HU', 47.96667000, 21.35000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q278017'),
(56047, 'Tiszavasvári Járás', 1045, 'SZ', 99, 'HU', 47.98606000, 21.36545000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q265901'),
(56048, 'Tiszaújváros', 1058, 'BZ', 99, 'HU', 47.93333000, 21.08333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q375097'),
(56049, 'Tiszaújvárosi Járás', 1058, 'BZ', 99, 'HU', 47.92656000, 20.99321000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q853879'),
(56050, 'Tokaj', 1058, 'BZ', 99, 'HU', 48.11667000, 21.41667000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q582209'),
(56051, 'Tokaji Járás', 1058, 'BZ', 99, 'HU', 48.14014000, 21.37002000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q547193'),
(56052, 'Tolcsva', 1058, 'BZ', 99, 'HU', 48.28333000, 21.45000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1008032'),
(56053, 'Tolna', 1038, 'TO', 99, 'HU', 46.42677000, 18.78248000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q843021'),
(56054, 'Tolnai Járás', 1038, 'TO', 99, 'HU', 46.42541000, 18.82691000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q14948493'),
(56055, 'Tompa', 1048, 'BK', 99, 'HU', 46.20605000, 19.53910000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q222782'),
(56056, 'Tornyospálca', 1045, 'SZ', 99, 'HU', 48.26667000, 22.18333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q948452'),
(56057, 'Tunyogmatolcs', 1045, 'SZ', 99, 'HU', 47.96667000, 22.46667000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q660842'),
(56058, 'Tura', 1059, 'PE', 99, 'HU', 47.60924000, 19.60279000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1064032'),
(56059, 'Tuzsér', 1045, 'SZ', 99, 'HU', 48.34407000, 22.11762000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1187117'),
(56060, 'Tyukod', 1045, 'SZ', 99, 'HU', 47.85378000, 22.56330000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q544303'),
(56061, 'Táborfalva', 1059, 'PE', 99, 'HU', 47.09942000, 19.47837000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186452'),
(56062, 'Tállya', 1058, 'BZ', 99, 'HU', 48.23333000, 21.23333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q304304'),
(56063, 'Tápióbicske', 1059, 'PE', 99, 'HU', 47.36096000, 19.68609000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q643517'),
(56064, 'Tápiógyörgye', 1059, 'PE', 99, 'HU', 47.33505000, 19.95276000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186555'),
(56065, 'Tápiószecső', 1059, 'PE', 99, 'HU', 47.45000000, 19.60923000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q515144'),
(56066, 'Tápiószele', 1059, 'PE', 99, 'HU', 47.33609000, 19.87724000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q979340'),
(56067, 'Tápiószentmárton', 1059, 'PE', 99, 'HU', 47.33990000, 19.74648000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186781'),
(56068, 'Tápiószőlős', 1059, 'PE', 99, 'HU', 47.30248000, 19.85133000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1187125'),
(56069, 'Tápióság', 1059, 'PE', 99, 'HU', 47.40200000, 19.63047000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q685084'),
(56070, 'Táplánszentkereszt', 1039, 'VA', 99, 'HU', 47.19496000, 16.69613000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q602807'),
(56071, 'Tárnok', 1059, 'PE', 99, 'HU', 47.37327000, 18.84579000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q765023'),
(56072, 'Tázlár', 1048, 'BK', 99, 'HU', 46.54824000, 19.51436000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1063907'),
(56073, 'Téglás', 1063, 'HB', 99, 'HU', 47.71667000, 21.68333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q916679'),
(56074, 'Tét', 1042, 'GS', 99, 'HU', 47.51922000, 17.50802000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q632080'),
(56075, 'Téti Járás', 1042, 'GS', 99, 'HU', 47.51515000, 17.51612000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q266289'),
(56076, 'Tóalmás', 1059, 'PE', 99, 'HU', 47.50782000, 19.66657000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186789'),
(56077, 'Tószeg', 1043, 'JN', 99, 'HU', 47.10000000, 20.15000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q527690'),
(56078, 'Tótkomlós', 1060, 'BE', 99, 'HU', 46.41667000, 20.73333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q937008'),
(56079, 'Tököl', 1059, 'PE', 99, 'HU', 47.32178000, 18.96249000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q916987'),
(56080, 'Töltéstava', 1042, 'GS', 99, 'HU', 47.62609000, 17.73376000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q284939'),
(56081, 'Tömörkény', 1031, 'CS', 99, 'HU', 46.61716000, 20.04357000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q933430'),
(56082, 'Törtel', 1059, 'PE', 99, 'HU', 47.12209000, 19.93714000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1187106'),
(56083, 'Törökbálint', 1059, 'PE', 99, 'HU', 47.42931000, 18.91356000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q645567'),
(56084, 'Törökszentmiklós', 1043, 'JN', 99, 'HU', 47.18333000, 20.41667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q840986'),
(56085, 'Törökszentmiklósi Járás', 1043, 'JN', 99, 'HU', 47.18209000, 20.44859000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q16629364'),
(56086, 'Túrkeve', 1043, 'JN', 99, 'HU', 47.10000000, 20.75000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q645014'),
(56087, 'Türje', 1046, 'ZA', 99, 'HU', 46.98366000, 17.10742000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1027980'),
(56088, 'Vaja', 1045, 'SZ', 99, 'HU', 48.00574000, 22.16761000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1088783'),
(56089, 'Vajszló', 1055, 'BA', 99, 'HU', 45.85957000, 17.98406000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q543396'),
(56090, 'Valkó', 1059, 'PE', 99, 'HU', 47.56391000, 19.51267000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1072182'),
(56091, 'Vaskút', 1048, 'BK', 99, 'HU', 46.10782000, 18.98514000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q914528'),
(56092, 'Vasvár', 1039, 'VA', 99, 'HU', 47.04928000, 16.79954000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q843031'),
(56093, 'Vasvári Járás', 1039, 'VA', 99, 'HU', 47.03487000, 16.85939000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q774552'),
(56094, 'Vecsés', 1059, 'PE', 99, 'HU', 47.40705000, 19.28648000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q36063'),
(56095, 'Vecsési Járás', 1059, 'PE', 99, 'HU', 47.42923000, 19.30136000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q16523679'),
(56096, 'Velence', 1044, 'FE', 99, 'HU', 47.23855000, 18.65484000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q1065977'),
(56097, 'Veresegyház', 1059, 'PE', 99, 'HU', 47.64590000, 19.29536000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q843039'),
(56098, 'Verpelét', 1040, 'HE', 99, 'HU', 47.85000000, 20.23333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1047661'),
(56099, 'Verőce', 1059, 'PE', 99, 'HU', 47.82468000, 19.03484000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1047624'),
(56100, 'Veszprém', 1054, 'VE', 99, 'HU', 47.09327000, 17.91149000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q146317'),
(56101, 'Veszprémi Járás', 1054, 'VE', 99, 'HU', 47.09666000, 17.82157000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q585975'),
(56102, 'Villány', 1055, 'BA', 99, 'HU', 45.86889000, 18.45389000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q657642'),
(56103, 'Visegrád', 1059, 'PE', 99, 'HU', 47.78526000, 18.97090000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q548097'),
(56104, 'Vonyarcvashegy', 1046, 'ZA', 99, 'HU', 46.75742000, 17.31172000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q938532'),
(56105, 'Vác', 1059, 'PE', 99, 'HU', 47.77591000, 19.13612000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q207408'),
(56106, 'Váci Járás', 1059, 'PE', 99, 'HU', 47.78316000, 19.23882000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q742957'),
(56107, 'Vácszentlászló', 1059, 'PE', 99, 'HU', 47.57400000, 19.53771000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186050'),
(56108, 'Vál', 1044, 'FE', 99, 'HU', 47.36264000, 18.67931000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q431919'),
(56109, 'Vámosgyörk', 1040, 'HE', 99, 'HU', 47.68429000, 19.92924000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q375741'),
(56110, 'Vámospércs', 1063, 'HB', 99, 'HU', 47.53333000, 21.90000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q537690'),
(56111, 'Városföld', 1048, 'BK', 99, 'HU', 46.81674000, 19.75668000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q951375'),
(56112, 'Várpalota', 1054, 'VE', 99, 'HU', 47.19936000, 18.13954000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q755681'),
(56113, 'Várpalotai Járás', 1054, 'VE', 99, 'HU', 47.18514000, 18.12211000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q668436'),
(56114, 'Vásárosnamény', 1045, 'SZ', 99, 'HU', 48.12542000, 22.31325000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q911600'),
(56115, 'Vásárosnaményi Járás', 1045, 'SZ', 99, 'HU', 48.17981000, 22.35859000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q831234'),
(56116, 'Vép', 1039, 'VA', 99, 'HU', 47.23041000, 16.72248000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1183686'),
(56117, 'Vésztő', 1060, 'BE', 99, 'HU', 46.91667000, 21.26667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q855066'),
(56118, 'Zagyvarékas', 1043, 'JN', 99, 'HU', 47.26667000, 20.13333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q631531'),
(56119, 'Zagyvaszántó', 1040, 'HE', 99, 'HU', 47.77703000, 19.67092000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1072177'),
(56120, 'Zalaegerszeg', 1046, 'ZA', 99, 'HU', 46.84000000, 16.84389000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q60037'),
(56121, 'Zalaegerszegi Járás', 1046, 'ZA', 99, 'HU', 46.80000000, 16.83000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q656093'),
(56122, 'Zalakomár', 1046, 'ZA', 99, 'HU', 46.53795000, 17.18094000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1186373'),
(56123, 'Zalalövő', 1046, 'ZA', 99, 'HU', 46.84802000, 16.58750000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q913754'),
(56124, 'Zalaszentgrót', 1046, 'ZA', 99, 'HU', 46.94474000, 17.07925000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q379030'),
(56125, 'Zalaszentgróti Járás', 1046, 'ZA', 99, 'HU', 46.92070000, 17.10095000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q3032008'),
(56126, 'Zamárdi', 1035, 'SO', 99, 'HU', 46.88488000, 17.95366000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q146033'),
(56127, 'Zirc', 1054, 'VE', 99, 'HU', 47.26362000, 17.87373000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q205607'),
(56128, 'Zirci Járás', 1054, 'VE', 99, 'HU', 47.28583000, 17.88412000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q3032015'),
(56129, 'Zomba', 1038, 'TO', 99, 'HU', 46.41084000, 18.56577000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q576672'),
(56130, 'Zsombó', 1031, 'CS', 99, 'HU', 46.32566000, 19.97464000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1184540'),
(56131, 'Zsámbok', 1059, 'PE', 99, 'HU', 47.54381000, 19.61048000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q924012'),
(56132, 'Zsámbék', 1059, 'PE', 99, 'HU', 47.54814000, 18.72011000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1088790'),
(56133, 'Zugló', 1064, 'BU', 99, 'HU', 47.51758000, 19.10549000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q228445'),
(56134, 'Záhony', 1045, 'SZ', 99, 'HU', 48.40906000, 22.17614000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q246723'),
(56135, 'Záhonyi Járás', 1045, 'SZ', 99, 'HU', 48.34914000, 22.18925000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1413457'),
(56136, 'Zákányszék', 1031, 'CS', 99, 'HU', 46.27453000, 19.88975000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1184548'),
(56137, 'Zámoly', 1044, 'FE', 99, 'HU', 47.31667000, 18.40810000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1084371'),
(56138, 'Zánka', 1054, 'VE', 99, 'HU', 46.87146000, 17.68473000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q683947'),
(56139, 'dunaújváros', 1044, 'FE', 99, 'HU', 46.96737000, 18.93288000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q187812'),
(56140, 'Ádánd', 1035, 'SO', 99, 'HU', 46.85931000, 18.16442000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1184420'),
(56141, 'Ágasegyháza', 1048, 'BK', 99, 'HU', 46.84025000, 19.45208000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q597231'),
(56142, 'Ágfalva', 1042, 'GS', 99, 'HU', 47.68991000, 16.51658000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q249575'),
(56143, 'Ásotthalom', 1031, 'CS', 99, 'HU', 46.19875000, 19.78334000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q288772'),
(56144, 'Ásványráró', 1042, 'GS', 99, 'HU', 47.82733000, 17.49418000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1075646'),
(56145, 'Érd', 1059, 'PE', 99, 'HU', 47.39489000, 18.91361000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q193050'),
(56146, 'Érdi Járás', 1059, 'PE', 99, 'HU', 47.35600000, 18.90167000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q722573'),
(56147, 'Érsekcsanád', 1048, 'BK', 99, 'HU', 46.25352000, 18.98457000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q645142'),
(56148, 'Érsekvadkert', 1051, 'NO', 99, 'HU', 47.99619000, 19.20231000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q288638'),
(56149, 'Ócsa', 1059, 'PE', 99, 'HU', 47.29986000, 19.23057000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q281625'),
(56150, 'Ófehértó', 1045, 'SZ', 99, 'HU', 47.93333000, 22.05000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1071750'),
(56151, 'Ónod', 1058, 'BZ', 99, 'HU', 48.00000000, 20.91667000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q293081'),
(56152, 'Ópusztaszer', 1031, 'CS', 99, 'HU', 46.48592000, 20.08722000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q293086'),
(56153, 'Ópályi', 1045, 'SZ', 99, 'HU', 47.99771000, 22.32617000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1093658'),
(56154, 'Ózd', 1058, 'BZ', 99, 'HU', 48.21667000, 20.30000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q293558'),
(56155, 'Ózdi Járás', 1058, 'BZ', 99, 'HU', 48.18133000, 20.24874000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q265020'),
(56156, 'Öcsöd', 1043, 'JN', 99, 'HU', 46.90000000, 20.40000000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q427680'),
(56157, 'Ököritófülpös', 1045, 'SZ', 99, 'HU', 47.91862000, 22.50810000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1089699'),
(56158, 'Örkény', 1059, 'PE', 99, 'HU', 47.12991000, 19.43324000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q727143'),
(56159, 'Öttevény', 1042, 'GS', 99, 'HU', 47.71946000, 17.48474000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1089740'),
(56160, 'Újfehértó', 1045, 'SZ', 99, 'HU', 47.80000000, 21.68333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q330171'),
(56161, 'Újhartyán', 1059, 'PE', 99, 'HU', 47.21981000, 19.38638000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q330162'),
(56162, 'Újkígyós', 1060, 'BE', 99, 'HU', 46.58333000, 21.03333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q223674'),
(56163, 'Újszilvás', 1059, 'PE', 99, 'HU', 47.27477000, 19.92477000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1185655'),
(56164, 'Újszász', 1043, 'JN', 99, 'HU', 47.30000000, 20.08333000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q603565'),
(56165, 'Úri', 1059, 'PE', 99, 'HU', 47.41429000, 19.52762000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q763119'),
(56166, 'Úrkút', 1054, 'VE', 99, 'HU', 47.08505000, 17.64393000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1089646'),
(56167, 'Üllés', 1031, 'CS', 99, 'HU', 46.33611000, 19.84454000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q538202'),
(56168, 'Üllő', 1059, 'PE', 99, 'HU', 47.38771000, 19.35533000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q933467'),
(56169, 'Üröm', 1059, 'PE', 99, 'HU', 47.59674000, 19.01583000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1069219'),
(56170, 'Őcsény', 1038, 'TO', 99, 'HU', 46.31370000, 18.75749000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1070473'),
(56171, 'Őrbottyán', 1059, 'PE', 99, 'HU', 47.68711000, 19.28239000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q1071613'),
(56172, 'Ősi', 1054, 'VE', 99, 'HU', 47.14722000, 18.18833000, '2019-10-05 22:53:37', '2020-05-01 17:22:51', 1, 'Q383063'),
(56173, 'Abepura', 1798, 'PA', 102, 'ID', -2.59640000, 140.63240000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q383063'),
(56174, 'Adiwerna', 1802, 'JT', 102, 'ID', -6.93750000, 109.13250000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25017837'),
(56175, 'Amahai', 1800, 'MA', 102, 'ID', -3.33984000, 128.91975000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25017837'),
(56176, 'Ambarawa', 1802, 'JT', 102, 'ID', -7.26333000, 110.39750000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25017837'),
(56177, 'Ambarita', 1792, 'SU', 102, 'ID', 2.68140000, 98.83110000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25017837'),
(56178, 'Ambon', 1800, 'MA', 102, 'ID', -3.69583000, 128.18333000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q18970'),
(56179, 'Amlapura', 1826, 'BA', 102, 'ID', -8.45000000, 115.61667000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q18970'),
(56180, 'Amlapura city', 1826, 'BA', 102, 'ID', -8.44869000, 115.60621000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q18970'),
(56181, 'Amuntai', 1819, 'KS', 102, 'ID', -2.41773000, 115.24941000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q18970'),
(56182, 'Arjawinangun', 1825, 'JB', 102, 'ID', -6.64528000, 108.41028000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25016835'),
(56183, 'Astanajapura', 1825, 'JB', 102, 'ID', -6.80170000, 108.63110000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25016302'),
(56184, 'Atambua', 1818, 'NT', 102, 'ID', -9.10611000, 124.89250000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25016302'),
(56185, 'Babat', 1827, 'JI', 102, 'ID', -7.11282000, 112.16354000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25016302'),
(56186, 'Baekrajan', 1802, 'JT', 102, 'ID', -6.76740000, 110.85410000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25016421'),
(56187, 'Baki', 1802, 'JT', 102, 'ID', -7.61278000, 110.78389000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25016477'),
(56188, 'Balaipungut', 1809, 'RI', 102, 'ID', 1.05949000, 101.29054000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25015779'),
(56189, 'Balapulang', 1802, 'JT', 102, 'ID', -7.04858000, 109.10056000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25015779'),
(56190, 'Balikpapan', 1804, 'KI', 102, 'ID', -1.26753000, 116.82887000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q14492'),
(56191, 'Balung', 1827, 'JI', 102, 'ID', -7.73333000, 113.91667000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012183'),
(56193, 'Banda Aceh', 1822, 'AC', 102, 'ID', 5.54167000, 95.33333000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q5779'),
(56194, 'Bandar', 1792, 'SU', 102, 'ID', 2.05000000, 99.75000000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q24821695'),
(56195, 'Bandar Lampung', 1811, 'LA', 102, 'ID', -5.42917000, 105.26111000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q8156'),
(56196, 'Bandung', 1825, 'JB', 102, 'ID', -6.92222000, 107.60694000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q10389'),
(56197, 'Bangil', 1827, 'JI', 102, 'ID', -7.59939000, 112.81860000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q10389'),
(56198, 'Bangkalan', 1827, 'JI', 102, 'ID', -7.04550000, 112.73510000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q10389'),
(56199, 'Banjar', 1825, 'JB', 102, 'ID', -7.19550000, 107.43130000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012319'),
(56200, 'Banjar', 1826, 'BA', 102, 'ID', -8.19000000, 114.96750000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012325'),
(56201, 'Banjar Wangsian', 1826, 'BA', 102, 'ID', -8.49497000, 115.42342000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012325'),
(56202, 'Banjaran', 1825, 'JB', 102, 'ID', -7.04528000, 107.58778000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012347'),
(56203, 'Banjarmasin', 1819, 'KS', 102, 'ID', -3.31987000, 114.59075000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012347'),
(56205, 'Banyumas', 1802, 'JT', 102, 'ID', -7.51417000, 109.29417000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012517'),
(56206, 'Banyuwangi', 1827, 'JI', 102, 'ID', -8.23250000, 114.35755000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012517'),
(56207, 'Barabai', 1819, 'KS', 102, 'ID', -2.58333000, 115.38333000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25012517'),
(56208, 'Batam', 1809, 'RI', 102, 'ID', 1.14937000, 104.02491000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q10103'),
(56209, 'Batang', 1802, 'JT', 102, 'ID', -6.48460000, 110.70830000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q25005023'),
(56210, 'Batu', 1827, 'JI', 102, 'ID', -7.87000000, 112.52833000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q11434'),
(56211, 'Baturaden', 1802, 'JT', 102, 'ID', -7.30000000, 109.21667000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q11434'),
(56212, 'Baturaja', 1816, 'SS', 102, 'ID', -4.12891000, 104.16695000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q11434'),
(56213, 'Bedugul', 1826, 'BA', 102, 'ID', -8.45040000, 115.59250000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q11434'),
(56214, 'Bejubang Dua', 1815, 'JA', 102, 'ID', -1.79230000, 103.31670000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q4881557'),
(56215, 'Bekasi', 1825, 'JB', 102, 'ID', -6.23490000, 106.98960000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q10392'),
(56216, 'Belawan', 1792, 'SU', 102, 'ID', 3.77550000, 98.68320000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q682657'),
(56217, 'Bengkulu', 1793, 'BE', 102, 'ID', -3.80044000, 102.26554000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q8042'),
(56218, 'Berastagi', 1792, 'SU', 102, 'ID', 3.19468000, 98.50889000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q8042'),
(56219, 'Besuki', 1827, 'JI', 102, 'ID', -7.73379000, 113.69785000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q8042'),
(56220, 'Biak', 1798, 'PA', 102, 'ID', -1.17670000, 136.08200000, '2019-10-05 22:53:37', '2019-10-05 22:53:37', 1, 'Q8042'),
(56221, 'Bima', 1814, 'NB', 102, 'ID', -8.46006000, 118.72667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q14128'),
(56222, 'Binjai', 1792, 'SU', 102, 'ID', 3.60010000, 98.48540000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5954'),
(56223, 'Bireun', 1822, 'AC', 102, 'ID', 5.20300000, 96.70090000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24821904'),
(56224, 'Blitar', 1827, 'JI', 102, 'ID', -8.09830000, 112.16810000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q11441'),
(56225, 'Blora', 1802, 'JT', 102, 'ID', -6.96980000, 111.41860000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q11441'),
(56226, 'Bogor', 1825, 'JB', 102, 'ID', -6.59444000, 106.78917000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q10393'),
(56227, 'Bojonegoro', 1827, 'JI', 102, 'ID', -7.15020000, 111.88170000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q10393'),
(56228, 'Bondowoso', 1827, 'JI', 102, 'ID', -7.91346000, 113.82145000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q10393'),
(56229, 'Bontang', 1804, 'KI', 102, 'ID', 0.13240000, 117.48540000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q14495'),
(56230, 'Boyolali', 1802, 'JT', 102, 'ID', -7.53306000, 110.59583000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q14495'),
(56231, 'Boyolangu', 1827, 'JI', 102, 'ID', -8.11810000, 111.89350000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q14495'),
(56232, 'Buaran', 1802, 'JT', 102, 'ID', -7.05000000, 109.55000000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24992475'),
(56233, 'Buduran', 1827, 'JI', 102, 'ID', -7.42810000, 112.72340000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24992536'),
(56234, 'Bukittinggi', 1828, 'SB', 102, 'ID', -0.30907000, 100.37055000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q7248'),
(56235, 'Bulakamba', 1802, 'JT', 102, 'ID', -6.87480000, 108.95590000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24992578'),
(56236, 'Candi Prambanan', 1802, 'JT', 102, 'ID', -7.75000000, 110.49417000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24847591'),
(56237, 'Caringin', 1825, 'JB', 102, 'ID', -6.70611000, 106.82139000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24992785'),
(56238, 'Ceper', 1802, 'JT', 102, 'ID', -7.67417000, 110.67889000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989720'),
(56239, 'Cepu', 1802, 'JT', 102, 'ID', -7.14750000, 111.59060000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989720'),
(56240, 'Ciamis', 1825, 'JB', 102, 'ID', -7.32570000, 108.35340000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989720'),
(56241, 'Ciampea', 1825, 'JB', 102, 'ID', -6.55472000, 106.70083000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989478'),
(56242, 'Cibinong', 1825, 'JB', 102, 'ID', -6.48167000, 106.85417000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989478'),
(56243, 'Cicurug', 1825, 'JB', 102, 'ID', -6.78139000, 106.78250000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24992793'),
(56244, 'Cikampek', 1825, 'JB', 102, 'ID', -6.41972000, 107.45583000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989872'),
(56245, 'Cikarang', 1825, 'JB', 102, 'ID', -6.26111000, 107.15278000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989872'),
(56246, 'Cikupa', 1825, 'JB', 102, 'ID', -6.23639000, 106.50833000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q25068841'),
(56247, 'Cileungsir', 1825, 'JB', 102, 'ID', -6.39472000, 106.95917000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989928'),
(56248, 'Cileunyi', 1825, 'JB', 102, 'ID', -6.93889000, 107.75278000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24989929'),
(56249, 'Cimahi', 1825, 'JB', 102, 'ID', -6.87222000, 107.54250000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q31544434'),
(56250, 'Ciputat', 1825, 'JB', 102, 'ID', -6.23750000, 106.69556000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24982547'),
(56251, 'Ciranjang-hilir', 1825, 'JB', 102, 'ID', -6.82000000, 107.25722000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q25033630'),
(56252, 'Cirebon', 1825, 'JB', 102, 'ID', -6.70630000, 108.55700000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q10395'),
(56253, 'Citeureup', 1825, 'JB', 102, 'ID', -6.48556000, 106.88194000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24982576'),
(56254, 'City of Balikpapan', 1804, 'KI', 102, 'ID', -1.24204000, 116.89419000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24982576'),
(56255, 'Colomadu', 1802, 'JT', 102, 'ID', -7.53333000, 110.75000000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24982613'),
(56256, 'Comal', 1802, 'JT', 102, 'ID', -6.90530000, 109.53470000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24982616'),
(56257, 'Kota Cilegon', 1810, 'BT', 102, 'ID', -5.96849850, 105.92142320, '2019-10-05 22:53:38', '2024-12-23 15:36:56', 1, 'Q10124'),
(56258, 'Curup', 1793, 'BE', 102, 'ID', -3.47030000, 102.52070000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24982677'),
(56259, 'Dampit', 1827, 'JI', 102, 'ID', -8.21162000, 112.74934000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24982677'),
(56260, 'Delanggu', 1802, 'JT', 102, 'ID', -7.61667000, 110.68333000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24976154'),
(56261, 'Deli Tua', 1792, 'SU', 102, 'ID', 3.50780000, 98.68390000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24821924'),
(56262, 'Demak', 1802, 'JT', 102, 'ID', -6.89090000, 110.63960000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24821924'),
(56263, 'Denpasar', 1826, 'BA', 102, 'ID', -8.65000000, 115.21667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q11506'),
(56265, 'Depok', 1825, 'JB', 102, 'ID', -6.40000000, 106.81861000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24976272'),
(56266, 'Diwek', 1827, 'JI', 102, 'ID', -7.57897000, 112.23109000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24976272'),
(56267, 'Dompu', 1814, 'NB', 102, 'ID', -8.53650000, 118.46340000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24976272'),
(56268, 'Driyorejo', 1827, 'JI', 102, 'ID', -7.36590000, 112.62190000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24977114'),
(56269, 'Dukuhturi', 1802, 'JT', 102, 'ID', -6.90000000, 109.08333000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24977271'),
(56270, 'Dumai', 1809, 'RI', 102, 'ID', 1.66711000, 101.44316000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q7782'),
(56271, 'East Halmahera Regency', 1801, 'MU', 102, 'ID', 1.33517000, 128.48627000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q19145'),
(56272, 'Ende', 1818, 'NT', 102, 'ID', -8.84320000, 121.66230000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q1340301'),
(56273, 'Galesong', 1795, 'SN', 102, 'ID', -5.31660000, 119.36610000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24966245'),
(56274, 'Gambiran Satu', 1827, 'JI', 102, 'ID', -8.39390000, 114.14640000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24966342'),
(56275, 'Gampengrejo', 1827, 'JI', 102, 'ID', -7.76667000, 112.01667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24966418'),
(56277, 'Gatak', 1802, 'JT', 102, 'ID', -7.59083000, 110.70444000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q25080270'),
(56278, 'Gebog', 1802, 'JT', 102, 'ID', -6.73500000, 110.84440000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24966647'),
(56279, 'Gedangan', 1827, 'JI', 102, 'ID', -7.39083000, 112.72667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24966669'),
(56280, 'Genteng', 1827, 'JI', 102, 'ID', -8.36667000, 114.15000000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24959350'),
(56281, 'Gili Air', 1814, 'NB', 102, 'ID', -8.35783000, 116.08240000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q25175448'),
(56283, 'Gombong', 1802, 'JT', 102, 'ID', -7.60722000, 109.51417000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24959493'),
(56284, 'Gongdanglegi Kulon', 1827, 'JI', 102, 'ID', -8.17529000, 112.63594000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24959493'),
(56285, 'Gorontalo', 1812, 'GO', 102, 'ID', 0.53750000, 123.06250000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q14525'),
(56286, 'Gresik', 1827, 'JI', 102, 'ID', -7.15389000, 112.65611000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q14525'),
(56287, 'Gresik Regency', 1827, 'JI', 102, 'ID', -7.19330000, 112.55300000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q11077'),
(56288, 'Grogol', 1802, 'JT', 102, 'ID', -7.60111000, 110.81861000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24959676'),
(56289, 'Gunung Kendil', 1802, 'JT', 102, 'ID', -7.33167000, 110.40417000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24959676'),
(56290, 'Gunungsitoli', 1792, 'SU', 102, 'ID', 1.28880000, 97.61430000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5967'),
(56291, 'Indramayu', 1825, 'JB', 102, 'ID', -6.32639000, 108.32000000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5967'),
(56292, 'Insrom', 1798, 'PA', 102, 'ID', -1.14473000, 136.03134000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5967'),
(56294, 'Jambi City', 1815, 'JA', 102, 'ID', -1.60000000, 103.61667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q3630'),
(56295, 'Jaten', 1802, 'JT', 102, 'ID', -7.57722000, 110.89750000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24924065'),
(56296, 'Jatibarang', 1825, 'JB', 102, 'ID', -6.47472000, 108.31528000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24924073'),
(56297, 'Jatiroto', 1802, 'JT', 102, 'ID', -7.88333000, 111.11667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24924081'),
(56298, 'Jatiwangi', 1825, 'JB', 102, 'ID', -6.73361000, 108.26278000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24924084'),
(56299, 'Jayapura', 1798, 'PA', 102, 'ID', -2.53371000, 140.71813000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q27860'),
(56300, 'Jekulo', 1802, 'JT', 102, 'ID', -6.80570000, 110.92620000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24920459'),
(56301, 'Jember', 1827, 'JI', 102, 'ID', -8.17211000, 113.69953000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24920459'),
(56302, 'Jimbaran', 1826, 'BA', 102, 'ID', -8.79093000, 115.16006000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q670243'),
(56303, 'Jogonalan', 1802, 'JT', 102, 'ID', -7.70361000, 110.53611000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24920614'),
(56304, 'Jombang', 1827, 'JI', 102, 'ID', -7.54595000, 112.23307000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24920614'),
(56305, 'Juwana', 1802, 'JT', 102, 'ID', -6.71500000, 111.15140000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24920614'),
(56306, 'Kabanjahe', 1792, 'SU', 102, 'ID', 3.10010000, 98.49080000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q24920614'),
(56307, 'Kabupaten Aceh Barat', 1822, 'AC', 102, 'ID', 4.45000000, 96.16667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5778'),
(56308, 'Kabupaten Aceh Barat Daya', 1822, 'AC', 102, 'ID', 3.83333000, 96.88333000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5775'),
(56309, 'Kabupaten Aceh Besar', 1822, 'AC', 102, 'ID', 5.38333000, 95.51667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5662'),
(56310, 'Kabupaten Aceh Jaya', 1822, 'AC', 102, 'ID', 4.86000000, 95.64000000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5667'),
(56311, 'Kabupaten Aceh Selatan', 1822, 'AC', 102, 'ID', 3.16667000, 97.41667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5759'),
(56312, 'Kabupaten Aceh Singkil', 1822, 'AC', 102, 'ID', 2.41667000, 97.91667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5672'),
(56313, 'Kabupaten Aceh Tamiang', 1822, 'AC', 102, 'ID', 4.25000000, 97.96667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5678'),
(56314, 'Kabupaten Aceh Tengah', 1822, 'AC', 102, 'ID', 4.51000000, 96.85500000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5675'),
(56315, 'Kabupaten Aceh Tenggara', 1822, 'AC', 102, 'ID', 3.36667000, 97.70000000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5763'),
(56316, 'Kabupaten Aceh Timur', 1822, 'AC', 102, 'ID', 4.63333000, 97.63333000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5674'),
(56317, 'Kabupaten Aceh Utara', 1822, 'AC', 102, 'ID', 4.97000000, 97.14000000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q5750'),
(56318, 'Kabupaten Agam', 1828, 'SB', 102, 'ID', -0.25000000, 100.16667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q6019'),
(56319, 'Kabupaten Alor', 1818, 'NT', 102, 'ID', -8.30000000, 124.56667000, '2019-10-05 22:53:38', '2019-10-05 22:53:38', 1, 'Q14131');

