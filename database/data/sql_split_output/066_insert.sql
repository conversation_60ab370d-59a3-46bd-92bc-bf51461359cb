INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(32875, 'Berzosa del Lozoya', 1158, 'M', 207, 'ES', 40.97320000, -3.52009000, '2019-10-05 22:45:37', '2022-08-29 12:04:40', 1, 'Q1772302'),
(32876, 'Berzosilla', 1157, 'P', 207, 'ES', 42.78053000, -4.03753000, '2019-10-05 22:45:37', '2022-08-29 11:45:44', 1, 'Q1907531'),
(32877, 'Besalú', 5103, 'GI', 207, 'ES', 42.19893000, 2.69953000, '2019-10-05 22:45:37', '2022-08-29 10:53:16', 1, 'Q1907531'),
(32878, '<PERSON><PERSON><PERSON><PERSON>', 5103, 'G<PERSON>', 207, 'ES', 41.96603000, 2.73922000, '2019-10-05 22:45:37', '2022-08-29 10:53:16', 1, 'Q13417'),
(32879, 'Betanzos', 5089, 'C', 207, 'ES', 43.28042000, -8.21467000, '2019-10-05 22:45:37', '2022-08-28 13:37:16', 1, 'Q13417'),
(32880, 'Betelu', 1204, 'NA', 207, 'ES', 43.02555000, -1.98029000, '2019-10-05 22:45:37', '2022-08-29 12:06:07', 1, 'Q610479'),
(32881, 'Beteta', 5106, 'CU', 207, 'ES', 40.57191000, -2.07588000, '2019-10-05 22:45:37', '2022-08-29 11:05:00', 1, 'Q1902704'),
(32882, 'Betxí', 5110, 'CS', 207, 'ES', 39.93333000, -0.20000000, '2019-10-05 22:45:37', '2022-08-29 11:26:42', 1, 'Q1902704'),
(32883, 'Beuda', 5103, 'GI', 207, 'ES', 42.23722000, 2.70942000, '2019-10-05 22:45:37', '2022-08-29 10:53:16', 1, 'Q12593'),
(32884, 'Bezas', 5111, 'TE', 207, 'ES', 40.33076000, -1.32511000, '2019-10-05 22:45:37', '2022-08-29 11:29:44', 1, 'Q1651630'),
(32885, 'Biar', 5108, 'A', 207, 'ES', 38.63117000, -0.76458000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1763764'),
(32886, 'Bicorp', 1175, 'V', 207, 'ES', 39.13215000, -0.78720000, '2019-10-05 22:45:37', '2022-08-29 12:05:40', 1, 'Q727183'),
(32887, 'Biel', 5113, 'Z', 207, 'ES', 42.38763000, -0.94154000, '2019-10-05 22:45:37', '2022-08-29 11:42:55', 1, 'Q857308'),
(32888, 'Bielsa', 1177, 'HU', 207, 'ES', 42.63347000, 0.21858000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q1657142'),
(32890, 'Bienvenida', 5092, 'BA', 207, 'ES', 38.30000000, -6.20000000, '2019-10-05 22:45:37', '2022-08-28 18:09:23', 1, 'Q535145'),
(32891, 'Bierge', 1177, 'HU', 207, 'ES', 42.16216000, -0.08326000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q1769772'),
(32892, 'Bigastro', 5108, 'A', 207, 'ES', 38.06237000, -0.89841000, '2019-10-05 22:45:37', '2022-08-29 11:15:47', 1, 'Q1769772'),
(32893, 'Bigues i Riells', 5102, 'B', 207, 'ES', 41.68333000, 2.23333000, '2019-10-05 22:45:37', '2022-08-29 10:50:00', 1, 'Q1769772'),
(32894, 'Bijuesca', 5113, 'Z', 207, 'ES', 41.54083000, -1.92039000, '2019-10-05 22:45:37', '2022-08-29 11:42:53', 1, 'Q1651278'),
(32895, 'Bilbao', 5094, 'BI', 207, 'ES', 43.26271000, -2.92528000, '2019-10-05 22:45:37', '2022-08-28 18:25:55', 1, 'Q8692'),
(32896, 'Binaced', 1177, 'HU', 207, 'ES', 41.82564000, 0.20084000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q24013408'),
(32897, 'Binissalem', 1174, 'PM', 207, 'ES', 39.68744000, 2.84396000, '2019-10-05 22:45:37', '2019-10-05 22:45:37', 1, 'Q837121'),
(32898, 'Binéfar', 1177, 'HU', 207, 'ES', 41.85141000, 0.29433000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q864175'),
(32899, 'Biota', 5113, 'Z', 207, 'ES', 42.26214000, -1.18735000, '2019-10-05 22:45:37', '2022-08-29 11:42:53', 1, 'Q511881'),
(32900, 'Bisaurri', 1177, 'HU', 207, 'ES', 42.49677000, 0.50647000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q866052'),
(32901, 'Biscarrués', 1177, 'HU', 207, 'ES', 42.22867000, -0.74967000, '2019-10-05 22:45:37', '2022-08-29 12:06:20', 1, 'Q599094'),
(32902, 'Bisimbre', 5113, 'Z', 207, 'ES', 41.85607000, -1.44268000, '2019-10-05 22:45:37', '2022-08-29 11:42:53', 1, 'Q1629943'),
(32904, 'Blacos', 1208, 'SO', 207, 'ES', 41.68090000, -2.85826000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q833428'),
(32905, 'Blanca', 1176, 'MU', 207, 'ES', 38.17910000, -1.37473000, '2019-10-05 22:45:38', '2022-08-29 12:05:49', 1, 'Q833428'),
(32906, 'Blancafort', 1203, 'T', 207, 'ES', 41.43740000, 1.15983000, '2019-10-05 22:45:38', '2022-08-29 10:57:32', 1, 'Q654198'),
(32907, 'Blancas', 5111, 'TE', 207, 'ES', 40.81392000, -1.48208000, '2019-10-05 22:45:38', '2022-08-29 11:29:44', 1, 'Q1653314'),
(32909, 'Blanes', 5103, 'GI', 207, 'ES', 41.67419000, 2.79036000, '2019-10-05 22:45:38', '2022-08-29 10:53:16', 1, 'Q1627544'),
(32910, 'Blascomillán', 1189, 'AV', 207, 'ES', 40.80146000, -5.08703000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1620327'),
(32911, 'Blascosancho', 1189, 'AV', 207, 'ES', 40.87744000, -4.63743000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1606949'),
(32912, 'Blesa', 5111, 'TE', 207, 'ES', 41.05150000, -0.88500000, '2019-10-05 22:45:38', '2022-08-29 11:29:44', 1, 'Q379234'),
(32913, 'Bliecos', 1208, 'SO', 207, 'ES', 41.52790000, -2.27135000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q831949'),
(32914, 'Boada', 1147, 'SA', 207, 'ES', 40.81569000, -6.30611000, '2019-10-05 22:45:38', '2022-08-29 11:44:50', 1, 'Q1777965'),
(32915, 'Boada de Campos', 1157, 'P', 207, 'ES', 41.98957000, -4.87730000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q775826'),
(32916, 'Boadilla del Camino', 1157, 'P', 207, 'ES', 42.25934000, -4.34525000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q592699'),
(32917, 'Boadilla del Monte', 1158, 'M', 207, 'ES', 40.40500000, -3.87835000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q1641013'),
(32918, 'Boalo', 1158, 'M', 207, 'ES', 40.71603000, -3.91656000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q1641013'),
(32919, 'Bobadilla', 1171, 'LO', 207, 'ES', 42.31873000, -2.75992000, '2019-10-05 22:45:38', '2022-08-29 12:05:09', 1, 'Q1641013'),
(32920, 'Bobadilla del Campo', 1183, 'VA', 207, 'ES', 41.20432000, -5.02294000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q1907081'),
(32921, 'Boborás', 5091, 'OR', 207, 'ES', 42.43332000, -8.14431000, '2019-10-05 22:45:38', '2022-08-28 17:53:26', 1, 'Q1627596'),
(32922, 'Boca de Huérgano', 1200, 'LE', 207, 'ES', 42.97287000, -4.92419000, '2019-10-05 22:45:38', '2020-05-01 17:23:16', 1, 'Q26592'),
(32923, 'Bocairent', 1175, 'V', 207, 'ES', 38.76667000, -0.61667000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q26592'),
(32924, 'Boceguillas', 1192, 'SG', 207, 'ES', 41.33641000, -3.63828000, '2019-10-05 22:45:38', '2022-08-29 11:50:42', 1, 'Q1776849'),
(32925, 'Bocigas', 1183, 'VA', 207, 'ES', 41.23070000, -4.68128000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q1916411'),
(32926, 'Bocos de Duero', 1183, 'VA', 207, 'ES', 41.62339000, -4.07048000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q1776942'),
(32927, 'Bodonal de la Sierra', 5092, 'BA', 207, 'ES', 38.14751000, -6.55950000, '2019-10-05 22:45:38', '2022-08-28 18:09:23', 1, 'Q1066654'),
(32928, 'Boecillo', 1183, 'VA', 207, 'ES', 41.54090000, -4.69940000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q949199'),
(32929, 'Bogajo', 1147, 'SA', 207, 'ES', 40.90710000, -6.53065000, '2019-10-05 22:45:38', '2022-08-29 11:44:50', 1, 'Q1648226'),
(32931, 'Bohonal de Ibor', 1190, 'CC', 207, 'ES', 39.78457000, -5.48500000, '2019-10-05 22:45:38', '2022-08-28 18:12:22', 1, 'Q1645052'),
(32932, 'Bohoyo', 1189, 'AV', 207, 'ES', 40.31584000, -5.44294000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1620302'),
(32933, 'Boimorto', 5089, 'C', 207, 'ES', 43.00000000, -8.13333000, '2019-10-05 22:45:38', '2022-08-28 13:37:16', 1, 'Q692787'),
(32934, 'Boiro', 5089, 'C', 207, 'ES', 42.64725000, -8.88459000, '2019-10-05 22:45:38', '2022-08-28 13:37:16', 1, 'Q735282'),
(32935, 'Bolaños de Calatrava', 5105, 'CR', 207, 'ES', 38.90690000, -3.66345000, '2019-10-05 22:45:38', '2022-08-29 11:03:24', 1, 'Q1613546'),
(32936, 'Bolaños de Campos', 1183, 'VA', 207, 'ES', 42.00680000, -5.28428000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q1922886'),
(32937, 'Bolbaite', 1175, 'V', 207, 'ES', 39.06041000, -0.67466000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q937923'),
(32938, 'Bollullos de la Mitación', 1193, 'SE', 207, 'ES', 37.34014000, -6.13719000, '2019-10-05 22:45:38', '2022-08-28 19:08:49', 1, 'Q1605640'),
(32939, 'Bollullos par del Condado', 5099, 'H', 207, 'ES', 37.34127000, -6.53970000, '2019-10-05 22:45:38', '2022-08-28 19:00:43', 1, 'Q23987938'),
(32940, 'Boltaña', 1177, 'HU', 207, 'ES', 42.44550000, 0.06802000, '2019-10-05 22:45:38', '2022-08-29 12:06:20', 1, 'Q24013404'),
(32941, 'Bolulla', 5108, 'A', 207, 'ES', 38.67529000, -0.11184000, '2019-10-05 22:45:38', '2022-08-29 11:15:47', 1, 'Q891663'),
(32942, 'Bolvir', 5103, 'GI', 207, 'ES', 42.41775000, 1.87986000, '2019-10-05 22:45:38', '2022-08-29 10:53:16', 1, 'Q13733'),
(32943, 'Bonansa', 1177, 'HU', 207, 'ES', 42.42691000, 0.66692000, '2019-10-05 22:45:38', '2022-08-29 12:06:20', 1, 'Q24013403'),
(32944, 'Bonares', 5099, 'H', 207, 'ES', 37.32423000, -6.68073000, '2019-10-05 22:45:38', '2022-08-28 19:00:43', 1, 'Q1615150'),
(32945, 'Bonastre', 1203, 'T', 207, 'ES', 41.22031000, 1.43936000, '2019-10-05 22:45:38', '2022-08-29 10:57:32', 1, 'Q1615150'),
(32947, 'Boniches', 5106, 'CU', 207, 'ES', 39.98333000, -1.61667000, '2019-10-05 22:45:38', '2022-08-29 11:05:00', 1, 'Q1902627'),
(32948, 'Bonilla de la Sierra', 1189, 'AV', 207, 'ES', 40.53063000, -5.26452000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1606930'),
(32949, 'Boquiñeni', 5113, 'Z', 207, 'ES', 41.84804000, -1.25246000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q1650872'),
(32950, 'Borau', 1177, 'HU', 207, 'ES', 42.65858000, -0.58846000, '2019-10-05 22:45:38', '2022-08-29 12:06:20', 1, 'Q1657127'),
(32951, 'Bordalba', 5113, 'Z', 207, 'ES', 41.41667000, -2.06667000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q1641108'),
(32952, 'Bordils', 5103, 'GI', 207, 'ES', 42.04336000, 2.91088000, '2019-10-05 22:45:38', '2022-08-29 10:53:16', 1, 'Q24019222'),
(32953, 'Bordón', 5111, 'TE', 207, 'ES', 40.68650000, -0.32216000, '2019-10-05 22:45:38', '2022-08-29 11:29:44', 1, 'Q1652476'),
(32954, 'Borja', 5113, 'Z', 207, 'ES', 41.83412000, -1.53271000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q53018328'),
(32955, 'Borjabad', 1208, 'SO', 207, 'ES', 41.55245000, -2.36625000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q833583'),
(32956, 'Bormujos', 1193, 'SE', 207, 'ES', 37.37358000, -6.07233000, '2019-10-05 22:45:38', '2022-08-28 19:08:49', 1, 'Q1605629'),
(32957, 'Bornos', 5096, 'CA', 207, 'ES', 36.81677000, -5.74448000, '2019-10-05 22:45:38', '2022-08-28 18:44:29', 1, 'Q1451570'),
(32958, 'Borobia', 1208, 'SO', 207, 'ES', 41.66481000, -1.89615000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q831935'),
(32959, 'Borox', 1205, 'TO', 207, 'ES', 40.06795000, -3.73804000, '2019-10-05 22:45:38', '2022-08-29 11:08:29', 1, 'Q1628592'),
(32960, 'Borrassà', 5103, 'GI', 207, 'ES', 42.22316000, 2.92610000, '2019-10-05 22:45:38', '2022-08-29 10:53:16', 1, 'Q1628592'),
(32961, 'Borrenes', 1200, 'LE', 207, 'ES', 42.49019000, -6.72338000, '2019-10-05 22:45:38', '2019-10-05 22:45:38', 1, 'Q1628592'),
(32962, 'Borriol', 5110, 'CS', 207, 'ES', 40.04249000, -0.07025000, '2019-10-05 22:45:38', '2022-08-29 11:26:42', 1, 'Q1628592'),
(32963, 'Bot', 1203, 'T', 207, 'ES', 41.00916000, 0.38392000, '2019-10-05 22:45:38', '2022-08-29 10:57:32', 1, 'Q589488'),
(32964, 'Botarell', 1203, 'T', 207, 'ES', 41.13627000, 0.98919000, '2019-10-05 22:45:38', '2022-08-29 10:57:32', 1, 'Q1248482'),
(32965, 'Botija', 1190, 'CC', 207, 'ES', 39.34498000, -6.07318000, '2019-10-05 22:45:38', '2022-08-28 18:12:22', 1, 'Q1248482'),
(32966, 'Botorrita', 5113, 'Z', 207, 'ES', 41.50706000, -1.03104000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q1629957'),
(32967, 'Boñar', 1200, 'LE', 207, 'ES', 42.86680000, -5.32386000, '2019-10-05 22:45:38', '2020-05-01 17:23:16', 1, 'Q26602'),
(32968, 'Brabos', 1189, 'AV', 207, 'ES', 40.77846000, -4.93934000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q26602'),
(32969, 'Braojos', 1158, 'M', 207, 'ES', 41.04040000, -3.64329000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q1915186'),
(32970, 'Brazacorta', 1146, 'BU', 207, 'ES', 41.71737000, -3.36711000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q1447883'),
(32971, 'Brazatortas', 5105, 'CR', 207, 'ES', 38.65869000, -4.29368000, '2019-10-05 22:45:38', '2022-08-29 11:03:24', 1, 'Q1631975'),
(32972, 'Brazuelo', 1200, 'LE', 207, 'ES', 42.49702000, -6.15734000, '2019-10-05 22:45:38', '2019-10-05 22:45:38', 1, 'Q26606'),
(32973, 'Brañosera', 1157, 'P', 207, 'ES', 42.93620000, -4.30833000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q1923970'),
(32974, 'Brea de Aragón', 5113, 'Z', 207, 'ES', 41.52387000, -1.60261000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q1639394'),
(32975, 'Brea de Tajo', 1158, 'M', 207, 'ES', 40.23333000, -3.10000000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q1772144'),
(32976, 'Breda', 5103, 'GI', 207, 'ES', 41.74833000, 2.55964000, '2019-10-05 22:45:38', '2022-08-29 10:53:16', 1, 'Q13638'),
(32977, 'Brenes', 1193, 'SE', 207, 'ES', 37.54944000, -5.87139000, '2019-10-05 22:45:38', '2022-08-28 19:08:49', 1, 'Q579216'),
(32978, 'Bretocino', 1161, 'ZA', 207, 'ES', 41.88430000, -5.75473000, '2019-10-05 22:45:38', '2022-08-29 11:48:03', 1, 'Q1765324'),
(32979, 'Bretó', 1161, 'ZA', 207, 'ES', 41.87917000, -5.73902000, '2019-10-05 22:45:38', '2022-08-29 11:48:03', 1, 'Q1752375'),
(32980, 'Breña Alta', 5112, 'TF', 207, 'ES', 28.65000000, -17.78333000, '2019-10-05 22:45:38', '2022-08-29 11:31:13', 1, 'Q433958'),
(32981, 'Breña Baja', 5112, 'TF', 207, 'ES', 28.63011000, -17.78953000, '2019-10-05 22:45:38', '2022-08-29 11:31:13', 1, 'Q433986'),
(32982, 'Brieva', 1192, 'SG', 207, 'ES', 41.03483000, -4.05364000, '2019-10-05 22:45:38', '2022-08-29 11:50:42', 1, 'Q433986'),
(32983, 'Brieva de Cameros', 1171, 'LO', 207, 'ES', 42.16462000, -2.79450000, '2019-10-05 22:45:38', '2022-08-29 12:05:09', 1, 'Q600764'),
(32984, 'Brihuega', 5107, 'GU', 207, 'ES', 40.76049000, -2.86966000, '2019-10-05 22:45:38', '2022-08-29 11:06:45', 1, 'Q56014864'),
(32985, 'Brime de Sog', 1161, 'ZA', 207, 'ES', 42.06162000, -6.04791000, '2019-10-05 22:45:38', '2022-08-29 11:48:03', 1, 'Q1765309'),
(32986, 'Brime de Urz', 1161, 'ZA', 207, 'ES', 42.03794000, -5.87326000, '2019-10-05 22:45:38', '2022-08-29 11:48:03', 1, 'Q1652206'),
(32987, 'Brincones', 1147, 'SA', 207, 'ES', 41.11339000, -6.34865000, '2019-10-05 22:45:38', '2022-08-29 11:44:50', 1, 'Q1625911'),
(32988, 'Briones', 1171, 'LO', 207, 'ES', 42.54397000, -2.78572000, '2019-10-05 22:45:38', '2022-08-29 12:05:09', 1, 'Q584251'),
(32989, 'Briviesca', 1146, 'BU', 207, 'ES', 42.54993000, -3.32315000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q24010575'),
(32990, 'Briñas', 1171, 'LO', 207, 'ES', 42.60106000, -2.83192000, '2019-10-05 22:45:38', '2022-08-29 12:05:09', 1, 'Q1647257'),
(32991, 'Bronchales', 5111, 'TE', 207, 'ES', 40.51171000, -1.58821000, '2019-10-05 22:45:38', '2022-08-29 11:29:44', 1, 'Q24015165'),
(32992, 'Broto', 1177, 'HU', 207, 'ES', 42.60434000, -0.12351000, '2019-10-05 22:45:38', '2022-08-29 12:06:20', 1, 'Q24013401'),
(32993, 'Brozas', 1190, 'CC', 207, 'ES', 39.61278000, -6.77770000, '2019-10-05 22:45:38', '2022-08-28 18:12:22', 1, 'Q1630506'),
(32994, 'Brunete', 1158, 'M', 207, 'ES', 40.40532000, -3.99850000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q1647090'),
(32995, 'Buberos', 1208, 'SO', 207, 'ES', 41.64692000, -2.19406000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q831987'),
(32996, 'Bubierca', 5113, 'Z', 207, 'ES', 41.31482000, -1.85386000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q22828756'),
(32997, 'Bubión', 5098, 'GR', 207, 'ES', 36.94900000, -3.35615000, '2019-10-05 22:45:38', '2022-08-28 18:52:57', 1, 'Q554265'),
(32998, 'Buciegas', 5106, 'CU', 207, 'ES', 40.33622000, -2.46250000, '2019-10-05 22:45:38', '2022-08-29 11:05:00', 1, 'Q554265'),
(32999, 'Budia', 5107, 'GU', 207, 'ES', 40.62734000, -2.75846000, '2019-10-05 22:45:38', '2022-08-29 11:06:45', 1, 'Q1656450'),
(33000, 'Buenache de Alarcón', 5106, 'CU', 207, 'ES', 39.65000000, -2.16667000, '2019-10-05 22:45:38', '2022-08-29 11:05:00', 1, 'Q1902599'),
(33001, 'Buenamadre', 1147, 'SA', 207, 'ES', 40.85705000, -6.24987000, '2019-10-05 22:45:38', '2022-08-29 11:44:50', 1, 'Q1650519'),
(33002, 'Buenaventura', 1205, 'TO', 207, 'ES', 40.17705000, -4.85003000, '2019-10-05 22:45:38', '2022-08-29 11:08:29', 1, 'Q1983162'),
(33003, 'Buenavista', 1147, 'SA', 207, 'ES', 40.76763000, -5.61139000, '2019-10-05 22:45:38', '2022-08-29 11:44:50', 1, 'Q2000956'),
(33004, 'Buenavista de Valdavia', 1157, 'P', 207, 'ES', 42.63788000, -4.61446000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q1923852'),
(33005, 'Buenavista del Norte', 5112, 'TF', 207, 'ES', 28.37458000, -16.86098000, '2019-10-05 22:45:38', '2022-08-29 11:31:13', 1, 'Q594961'),
(33006, 'Buendía', 5106, 'CU', 207, 'ES', 40.36643000, -2.75645000, '2019-10-05 22:45:38', '2022-08-29 11:05:00', 1, 'Q513533'),
(33007, 'Bueu', 1167, 'PO', 207, 'ES', 42.32458000, -8.78497000, '2019-10-05 22:45:38', '2022-08-28 17:57:54', 1, 'Q1632909'),
(33008, 'Bueña', 5111, 'TE', 207, 'ES', 40.70881000, -1.26742000, '2019-10-05 22:45:38', '2022-08-29 11:29:44', 1, 'Q1653339'),
(33009, 'Bufali', 1175, 'V', 207, 'ES', 38.86775000, -0.51617000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q591535'),
(33010, 'Bugarra', 1175, 'V', 207, 'ES', 39.61667000, -0.76667000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q472307'),
(33011, 'Bugedo', 1146, 'BU', 207, 'ES', 42.64912000, -3.01786000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q386872'),
(33012, 'Buitrago', 1208, 'SO', 207, 'ES', 41.84735000, -2.40858000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q832158'),
(33013, 'Buitrago del Lozoya', 1158, 'M', 207, 'ES', 40.99090000, -3.63365000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q1648730'),
(33014, 'Bujalance', 5097, 'CO', 207, 'ES', 37.89556000, -4.38074000, '2019-10-05 22:45:38', '2022-08-28 18:49:38', 1, 'Q975711'),
(33015, 'Bujalaro', 5107, 'GU', 207, 'ES', 40.93687000, -2.88333000, '2019-10-05 22:45:38', '2022-08-29 11:06:45', 1, 'Q1656423'),
(33016, 'Bujaraloz', 5113, 'Z', 207, 'ES', 41.49806000, -0.15290000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q24016468'),
(33017, 'Bulbuente', 5113, 'Z', 207, 'ES', 41.81970000, -1.60238000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q24016467'),
(33018, 'Bullas', 1176, 'MU', 207, 'ES', 38.04667000, -1.67227000, '2019-10-05 22:45:38', '2022-08-29 12:05:49', 1, 'Q986086'),
(33019, 'Buniel', 1146, 'BU', 207, 'ES', 42.31197000, -3.82230000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q1614638'),
(33020, 'Bunyola', 1174, 'PM', 207, 'ES', 39.69634000, 2.69955000, '2019-10-05 22:45:38', '2019-10-05 22:45:38', 1, 'Q837125'),
(33021, 'Burbáguena', 5111, 'TE', 207, 'ES', 41.01783000, -1.33825000, '2019-10-05 22:45:38', '2022-08-29 11:29:44', 1, 'Q24015162'),
(33023, 'Bureta', 5113, 'Z', 207, 'ES', 41.81572000, -1.48819000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q24016466'),
(33024, 'Burganes de Valverde', 1161, 'ZA', 207, 'ES', 41.92130000, -5.78244000, '2019-10-05 22:45:38', '2022-08-29 11:48:03', 1, 'Q642892'),
(33025, 'Burgohondo', 1189, 'AV', 207, 'ES', 40.41416000, -4.78509000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1601831'),
(33026, 'Burgos', 1146, 'BU', 207, 'ES', 42.34106000, -3.70184000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q24010572'),
(33027, 'Burguillos', 1193, 'SE', 207, 'ES', 37.58440000, -5.96654000, '2019-10-05 22:45:38', '2022-08-28 19:08:49', 1, 'Q1633443'),
(33028, 'Burguillos de Toledo', 1205, 'TO', 207, 'ES', 39.79635000, -3.99254000, '2019-10-05 22:45:38', '2022-08-29 11:08:29', 1, 'Q1629972'),
(33029, 'Burguillos del Cerro', 5092, 'BA', 207, 'ES', 38.38008000, -6.59037000, '2019-10-05 22:45:38', '2022-08-28 18:09:23', 1, 'Q1100437'),
(33030, 'Burjassot', 1175, 'V', 207, 'ES', 39.50984000, -0.41327000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q1100437'),
(33031, 'Burlata', 1204, 'NA', 207, 'ES', 42.82562000, -1.61671000, '2019-10-05 22:45:38', '2022-08-29 12:06:07', 1, 'Q732730'),
(33032, 'Burriana', 1175, 'V', 207, 'ES', 39.88901000, -0.08499000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q385070'),
(33033, 'Burujón', 1205, 'TO', 207, 'ES', 39.90113000, -4.29730000, '2019-10-05 22:45:38', '2022-08-29 11:08:29', 1, 'Q960799'),
(33034, 'Burón', 1200, 'LE', 207, 'ES', 43.02486000, -5.05119000, '2019-10-05 22:45:38', '2020-05-01 17:23:16', 1, 'Q960799'),
(33035, 'Busot', 5108, 'A', 207, 'ES', 38.48206000, -0.41918000, '2019-10-05 22:45:38', '2022-08-29 11:15:47', 1, 'Q960799'),
(33036, 'Busquístar', 5098, 'GR', 207, 'ES', 36.93796000, -3.29444000, '2019-10-05 22:45:38', '2022-08-28 18:52:57', 1, 'Q554478'),
(33037, 'Bustares', 5107, 'GU', 207, 'ES', 41.13333000, -3.06667000, '2019-10-05 22:45:38', '2022-08-29 11:06:45', 1, 'Q1654987'),
(33038, 'Bustarviejo', 1158, 'M', 207, 'ES', 40.85720000, -3.70766000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q55487'),
(33039, 'Bustillo de Chaves', 1183, 'VA', 207, 'ES', 42.13114000, -5.09169000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q55487'),
(33040, 'Bustillo de la Vega', 1157, 'P', 207, 'ES', 42.45605000, -4.74115000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q1923886'),
(33041, 'Bustillo del Oro', 1161, 'ZA', 207, 'ES', 41.67460000, -5.46158000, '2019-10-05 22:45:38', '2022-08-29 11:48:03', 1, 'Q1652237'),
(33042, 'Bustillo del Páramo', 1200, 'LE', 207, 'ES', 42.44126000, -5.79280000, '2019-10-05 22:45:38', '2020-05-01 17:23:16', 1, 'Q26594'),
(33043, 'Bustillo del Páramo de Carrión', 1157, 'P', 207, 'ES', 42.35509000, -4.73980000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q1907581'),
(33044, 'Busto de Bureba', 1146, 'BU', 207, 'ES', 42.65910000, -3.26512000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q1623172'),
(33045, 'Buñol', 1175, 'V', 207, 'ES', 39.41667000, -0.78333000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q853762'),
(33046, 'Buñuel', 1204, 'NA', 207, 'ES', 41.98009000, -1.44503000, '2019-10-05 22:45:38', '2022-08-29 12:06:07', 1, 'Q327740'),
(33047, 'Bàscara', 5103, 'GI', 207, 'ES', 42.15998000, 2.91028000, '2019-10-05 22:45:38', '2022-08-29 10:53:16', 1, 'Q327740'),
(33048, 'Bádenas', 5111, 'TE', 207, 'ES', 41.09216000, -1.12241000, '2019-10-05 22:45:38', '2022-08-29 11:29:43', 1, 'Q1653357'),
(33049, 'Báguena', 5111, 'TE', 207, 'ES', 41.04181000, -1.35767000, '2019-10-05 22:45:38', '2022-08-29 11:29:43', 1, 'Q936045'),
(33050, 'Bárboles', 5113, 'Z', 207, 'ES', 41.70898000, -1.18594000, '2019-10-05 22:45:38', '2022-08-29 11:42:53', 1, 'Q1650575'),
(33051, 'Bárcabo', 1177, 'HU', 207, 'ES', 42.24219000, 0.06934000, '2019-10-05 22:45:38', '2022-08-29 12:06:20', 1, 'Q1769767'),
(33052, 'Bárcena de Campos', 1157, 'P', 207, 'ES', 42.48428000, -4.49875000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q1907591'),
(33053, 'Bárcena de Cicero', 1170, 'S', 207, 'ES', 43.42160000, -3.51030000, '2019-10-05 22:45:38', '2020-05-01 17:23:15', 1, 'Q1443842'),
(33054, 'Bárcena de Pie de Concha', 1170, 'S', 207, 'ES', 43.12580000, -4.05662000, '2019-10-05 22:45:38', '2020-05-01 17:23:15', 1, 'Q1606550'),
(33055, 'Báscones de Ojeda', 1157, 'P', 207, 'ES', 42.67064000, -4.52693000, '2019-10-05 22:45:38', '2022-08-29 11:45:44', 1, 'Q934968'),
(33056, 'Bèlgida', 1175, 'V', 207, 'ES', 38.85000000, -0.46667000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q934968'),
(33057, 'Bédar', 5095, 'AL', 207, 'ES', 37.19389000, -1.98166000, '2019-10-05 22:45:38', '2022-08-28 18:41:41', 1, 'Q934968'),
(33058, 'Béjar', 1147, 'SA', 207, 'ES', 40.38641000, -5.76341000, '2019-10-05 22:45:38', '2022-08-29 11:44:50', 1, 'Q695375'),
(33059, 'Bélmez', 5097, 'CO', 207, 'ES', 38.26667000, -5.20000000, '2019-10-05 22:45:38', '2022-08-28 18:49:38', 1, 'Q970766'),
(33060, 'Bélmez de la Moraleda', 5100, 'J', 207, 'ES', 37.72382000, -3.38207000, '2019-10-05 22:45:38', '2022-08-28 19:04:30', 1, 'Q283549'),
(33061, 'Bérchules', 5098, 'GR', 207, 'ES', 36.97678000, -3.19067000, '2019-10-05 22:45:38', '2022-08-28 18:52:57', 1, 'Q554275'),
(33062, 'Bétera', 1175, 'V', 207, 'ES', 39.59111000, -0.46151000, '2019-10-05 22:45:38', '2022-08-29 12:05:40', 1, 'Q554275'),
(33063, 'Bóveda del Río Almar', 1147, 'SA', 207, 'ES', 40.85719000, -5.21030000, '2019-10-05 22:45:38', '2022-08-29 11:44:50', 1, 'Q1640620'),
(33064, 'Búger', 1174, 'PM', 207, 'ES', 39.75809000, 2.98349000, '2019-10-05 22:45:38', '2020-05-01 17:23:15', 1, 'Q652988'),
(33065, 'Caballar', 1192, 'SG', 207, 'ES', 41.12159000, -3.96420000, '2019-10-05 22:45:38', '2022-08-29 11:50:42', 1, 'Q1906422'),
(33066, 'Cabanelles', 5103, 'GI', 207, 'ES', 42.23068000, 2.81997000, '2019-10-05 22:45:38', '2022-08-29 10:53:16', 1, 'Q11247'),
(33067, 'Cabanes', 5110, 'CS', 207, 'ES', 40.15600000, 0.04325000, '2019-10-05 22:45:38', '2022-08-29 11:26:42', 1, 'Q11247'),
(33068, 'Cabanillas de la Sierra', 1158, 'M', 207, 'ES', 40.82009000, -3.62438000, '2019-10-05 22:45:38', '2022-08-29 12:04:40', 1, 'Q1772200'),
(33069, 'Cabanillas del Campo', 5107, 'GU', 207, 'ES', 40.63376000, -3.22937000, '2019-10-05 22:45:38', '2022-08-29 11:06:45', 1, 'Q1641907'),
(33070, 'Cabañas de Ebro', 5113, 'Z', 207, 'ES', 41.80000000, -1.20000000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q1650854'),
(33071, 'Cabañas de Polendos', 1192, 'SG', 207, 'ES', 41.06667000, -4.11010000, '2019-10-05 22:45:38', '2022-08-29 11:50:42', 1, 'Q1906368'),
(33072, 'Cabañas de Sayago', 1161, 'ZA', 207, 'ES', 41.33333000, -5.78333000, '2019-10-05 22:45:38', '2022-08-29 11:48:03', 1, 'Q1652210'),
(33073, 'Cabañas de Yepes', 1205, 'TO', 207, 'ES', 39.89051000, -3.53502000, '2019-10-05 22:45:38', '2022-08-29 11:08:29', 1, 'Q1632897'),
(33074, 'Cabañas de la Sagra', 1205, 'TO', 207, 'ES', 40.00610000, -3.94560000, '2019-10-05 22:45:38', '2022-08-29 11:08:29', 1, 'Q1628856'),
(33075, 'Cabañas del Castillo', 1190, 'CC', 207, 'ES', 39.54804000, -5.51203000, '2019-10-05 22:45:38', '2022-08-28 18:12:22', 1, 'Q1643637'),
(33076, 'Cabañes de Esgueva', 1146, 'BU', 207, 'ES', 41.83023000, -3.78824000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q1633997'),
(33077, 'Cabeza del Buey', 5092, 'BA', 207, 'ES', 38.72227000, -5.21946000, '2019-10-05 22:45:38', '2022-08-28 18:09:23', 1, 'Q1100433'),
(33078, 'Cabeza del Caballo', 1147, 'SA', 207, 'ES', 41.12982000, -6.55742000, '2019-10-05 22:45:38', '2022-08-29 11:44:51', 1, 'Q1639363'),
(33079, 'Cabeza la Vaca', 5092, 'BA', 207, 'ES', 38.08333000, -6.41667000, '2019-10-05 22:45:38', '2022-08-28 18:09:23', 1, 'Q1372523'),
(33080, 'Cabezabellosa', 1190, 'CC', 207, 'ES', 40.13763000, -6.00086000, '2019-10-05 22:45:38', '2022-08-28 18:12:22', 1, 'Q1648618'),
(33081, 'Cabezabellosa de la Calzada', 1147, 'SA', 207, 'ES', 41.04395000, -5.48866000, '2019-10-05 22:45:38', '2022-08-29 11:44:51', 1, 'Q1651906'),
(33082, 'Cabezamesada', 1205, 'TO', 207, 'ES', 39.81630000, -3.10236000, '2019-10-05 22:45:38', '2022-08-29 11:08:29', 1, 'Q1641976'),
(33083, 'Cabezarados', 5105, 'CR', 207, 'ES', 38.84469000, -4.29830000, '2019-10-05 22:45:38', '2022-08-29 11:03:24', 1, 'Q1644839'),
(33084, 'Cabezas Rubias', 5099, 'H', 207, 'ES', 37.72660000, -7.08738000, '2019-10-05 22:45:38', '2022-08-28 19:00:43', 1, 'Q1644839'),
(33085, 'Cabezas de Alambre', 1189, 'AV', 207, 'ES', 40.94218000, -4.84184000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1607109'),
(33086, 'Cabezas del Pozo', 1189, 'AV', 207, 'ES', 41.00139000, -4.95453000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1606919'),
(33087, 'Cabezas del Villar', 1189, 'AV', 207, 'ES', 40.71557000, -5.20956000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1620309'),
(33088, 'Cabezuela', 1192, 'SG', 207, 'ES', 41.23590000, -3.93173000, '2019-10-05 22:45:38', '2022-08-29 11:50:42', 1, 'Q1776854'),
(33089, 'Cabezuela del Valle', 1190, 'CC', 207, 'ES', 40.19364000, -5.80650000, '2019-10-05 22:45:38', '2022-08-28 18:12:22', 1, 'Q726538'),
(33090, 'Cabezón', 1200, 'LE', 207, 'ES', 41.73369000, -4.64510000, '2019-10-05 22:45:38', '2020-05-01 17:23:16', 1, 'Q726538'),
(33091, 'Cabezón de Cameros', 1171, 'LO', 207, 'ES', 42.19716000, -2.51989000, '2019-10-05 22:45:38', '2022-08-29 12:05:09', 1, 'Q1637059'),
(33092, 'Cabezón de Liébana', 1170, 'S', 207, 'ES', 43.13437000, -4.57630000, '2019-10-05 22:45:38', '2020-05-01 17:23:15', 1, 'Q1606045'),
(33093, 'Cabezón de Valderaduey', 1183, 'VA', 207, 'ES', 42.16793000, -5.15892000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q1916529'),
(33094, 'Cabezón de la Sal', 1170, 'S', 207, 'ES', 43.30824000, -4.23571000, '2019-10-05 22:45:38', '2020-05-01 17:23:15', 1, 'Q593831'),
(33095, 'Cabezón de la Sierra', 1146, 'BU', 207, 'ES', 41.93434000, -3.24153000, '2019-10-05 22:45:38', '2022-08-29 11:24:19', 1, 'Q1633987'),
(33096, 'Cabizuela', 1189, 'AV', 207, 'ES', 40.90090000, -4.80212000, '2019-10-05 22:45:38', '2022-08-29 11:49:56', 1, 'Q1620291'),
(33097, 'Cabolafuente', 5113, 'Z', 207, 'ES', 41.21053000, -2.04133000, '2019-10-05 22:45:38', '2022-08-29 11:42:54', 1, 'Q1641779'),
(33098, 'Cabra', 5097, 'CO', 207, 'ES', 37.47249000, -4.44206000, '2019-10-05 22:45:38', '2022-08-28 18:49:38', 1, 'Q1641779'),
(33099, 'Cabra de Mora', 5111, 'TE', 207, 'ES', 40.31696000, -0.80678000, '2019-10-05 22:45:38', '2022-08-29 11:29:44', 1, 'Q1653469'),
(33100, 'Cabra del Santo Cristo', 5100, 'J', 207, 'ES', 37.70379000, -3.28765000, '2019-10-05 22:45:38', '2022-08-28 19:04:30', 1, 'Q731592'),
(33101, 'Cabredo', 1204, 'NA', 207, 'ES', 42.62966000, -2.41133000, '2019-10-05 22:45:38', '2022-08-29 12:06:07', 1, 'Q1637015'),
(33102, 'Cabrejas del Campo', 1208, 'SO', 207, 'ES', 41.68096000, -2.26964000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q832022'),
(33103, 'Cabrejas del Pinar', 1208, 'SO', 207, 'ES', 41.79596000, -2.84945000, '2019-10-05 22:45:38', '2022-08-29 11:51:23', 1, 'Q831223'),
(33104, 'Cabrera de Mar', 5102, 'B', 207, 'ES', 41.51667000, 2.40000000, '2019-10-05 22:45:38', '2022-08-29 10:50:00', 1, 'Q12824'),
(33105, 'Cabrerizos', 1147, 'SA', 207, 'ES', 40.97808000, -5.60907000, '2019-10-05 22:45:38', '2022-08-29 11:44:51', 1, 'Q1628707'),
(33106, 'Cabrero', 1190, 'CC', 207, 'ES', 40.11324000, -5.89293000, '2019-10-05 22:45:38', '2022-08-28 18:12:22', 1, 'Q1776883'),
(33107, 'Cabreros del Monte', 1183, 'VA', 207, 'ES', 41.84906000, -5.27016000, '2019-10-05 22:45:38', '2022-08-29 11:48:44', 1, 'Q1907065'),
(33108, 'Cabreros del Río', 1200, 'LE', 207, 'ES', 42.40205000, -5.54154000, '2019-10-05 22:45:38', '2020-05-01 17:23:16', 1, 'Q26627'),
(33109, 'Cabrillanes', 1200, 'LE', 207, 'ES', 42.95343000, -6.14849000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q26627'),
(33110, 'Cabrillas', 1147, 'SA', 207, 'ES', 40.73977000, -6.17873000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1640871'),
(33111, 'Cabrils', 5102, 'B', 207, 'ES', 41.52760000, 2.36996000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q1640871'),
(33112, 'Cacabelos', 1200, 'LE', 207, 'ES', 42.60021000, -6.72373000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q24013197'),
(33113, 'Cachorrilla', 1190, 'CC', 207, 'ES', 39.91557000, -6.66909000, '2019-10-05 22:45:39', '2022-08-28 18:12:22', 1, 'Q640211'),
(33114, 'Cadalso', 1190, 'CC', 207, 'ES', 40.23743000, -6.54083000, '2019-10-05 22:45:39', '2022-08-28 18:12:22', 1, 'Q640211'),
(33115, 'Cadalso de los Vidrios', 1158, 'M', 207, 'ES', 40.30067000, -4.43348000, '2019-10-05 22:45:39', '2022-08-29 12:04:40', 1, 'Q1648736'),
(33116, 'Cadaqués', 5103, 'GI', 207, 'ES', 42.28856000, 3.27706000, '2019-10-05 22:45:39', '2022-08-29 10:53:16', 1, 'Q1648736'),
(33117, 'Cadiz', 5096, 'CA', 207, 'ES', 36.52672000, -6.28910000, '2019-10-05 22:45:39', '2022-08-28 18:44:29', 1, 'Q15682'),
(33118, 'Cadreita', 1204, 'NA', 207, 'ES', 42.21667000, -1.68333000, '2019-10-05 22:45:39', '2022-08-29 12:06:07', 1, 'Q1648483'),
(33119, 'Cadrete', 5113, 'Z', 207, 'ES', 41.55575000, -0.96013000, '2019-10-05 22:45:39', '2022-08-29 11:42:54', 1, 'Q1639202'),
(33120, 'Cala', 5099, 'H', 207, 'ES', 37.96667000, -6.31667000, '2019-10-05 22:45:39', '2022-08-28 19:00:43', 1, 'Q913228'),
(33121, 'Cala Rajada', 1174, 'PM', 207, 'ES', 39.71174000, 3.46310000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q913228'),
(33122, 'Cala d\'Or', 1174, 'PM', 207, 'ES', 39.37810000, 3.23479000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q857257'),
(33123, 'Calaceite', 5111, 'TE', 207, 'ES', 41.01625000, 0.18876000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q24015160'),
(33124, 'Calaf', 5102, 'B', 207, 'ES', 41.73289000, 1.51375000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q24015160'),
(33125, 'Calafell', 1203, 'T', 207, 'ES', 41.19997000, 1.56830000, '2019-10-05 22:45:39', '2022-08-29 10:57:32', 1, 'Q983637'),
(33126, 'Calahorra', 1171, 'LO', 207, 'ES', 42.30506000, -1.96521000, '2019-10-05 22:45:39', '2022-08-29 12:05:09', 1, 'Q52555450'),
(33127, 'Calahorra de Boedo', 1157, 'P', 207, 'ES', 42.57409000, -4.38485000, '2019-10-05 22:45:39', '2022-08-29 11:45:44', 1, 'Q1923939'),
(33128, 'Calamocha', 5111, 'TE', 207, 'ES', 40.91933000, -1.29750000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q24015159'),
(33129, 'Calamonte', 5092, 'BA', 207, 'ES', 38.88861000, -6.38791000, '2019-10-05 22:45:39', '2022-08-28 18:09:23', 1, 'Q1020436'),
(33130, 'Calanda', 5111, 'TE', 207, 'ES', 40.94153000, -0.23243000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q1020436'),
(33131, 'Calasparra', 1176, 'MU', 207, 'ES', 38.22997000, -1.69986000, '2019-10-05 22:45:39', '2022-08-29 12:05:49', 1, 'Q1313139'),
(33132, 'Calatayud', 5113, 'Z', 207, 'ES', 41.35353000, -1.64318000, '2019-10-05 22:45:39', '2022-08-29 11:42:54', 1, 'Q24016460'),
(33133, 'Calatañazor', 1208, 'SO', 207, 'ES', 41.69879000, -2.81837000, '2019-10-05 22:45:39', '2022-08-29 11:51:23', 1, 'Q24016460'),
(33134, 'Calatorao', 5113, 'Z', 207, 'ES', 41.52228000, -1.34702000, '2019-10-05 22:45:39', '2022-08-29 11:42:54', 1, 'Q1641454'),
(33135, 'Calañas', 5099, 'H', 207, 'ES', 37.65568000, -6.88050000, '2019-10-05 22:45:39', '2022-08-28 19:00:43', 1, 'Q913249'),
(33136, 'Calcena', 5113, 'Z', 207, 'ES', 41.65515000, -1.71764000, '2019-10-05 22:45:39', '2022-08-29 11:42:54', 1, 'Q1650833'),
(33137, 'Caldas de Reis', 1167, 'PO', 207, 'ES', 42.60473000, -8.64230000, '2019-10-05 22:45:39', '2022-08-28 17:57:54', 1, 'Q1650833'),
(33138, 'Caldes de Montbui', 5102, 'B', 207, 'ES', 41.63333000, 2.16667000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q1650833'),
(33139, 'Calella', 5102, 'B', 207, 'ES', 41.61381000, 2.65423000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q1650833'),
(33140, 'Calera de León', 5092, 'BA', 207, 'ES', 38.10000000, -6.33333000, '2019-10-05 22:45:39', '2022-08-28 18:09:23', 1, 'Q1074219'),
(33141, 'Calera y Chozas', 1205, 'TO', 207, 'ES', 39.88278000, -4.98213000, '2019-10-05 22:45:39', '2022-08-29 11:08:29', 1, 'Q945487'),
(33142, 'Caleruega', 1146, 'BU', 207, 'ES', 41.82548000, -3.48593000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q848172'),
(33143, 'Caleruela', 1205, 'TO', 207, 'ES', 39.87483000, -5.25693000, '2019-10-05 22:45:39', '2022-08-29 11:08:29', 1, 'Q1641381'),
(33144, 'Calicasas', 5098, 'GR', 207, 'ES', 37.27130000, -3.61345000, '2019-10-05 22:45:39', '2022-08-28 18:52:57', 1, 'Q554225'),
(33145, 'Calles', 1175, 'V', 207, 'ES', 39.72118000, -0.97057000, '2019-10-05 22:45:39', '2022-08-29 12:05:40', 1, 'Q554225'),
(33146, 'Callosa de Segura', 5108, 'A', 207, 'ES', 38.12497000, -0.87822000, '2019-10-05 22:45:39', '2022-08-29 11:15:47', 1, 'Q1750915'),
(33147, 'Calmarza', 5113, 'Z', 207, 'ES', 41.15810000, -1.91196000, '2019-10-05 22:45:39', '2022-08-29 11:42:54', 1, 'Q63159178'),
(33148, 'Calomarde', 5111, 'TE', 207, 'ES', 40.37264000, -1.57435000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q63159178'),
(33149, 'Calonge', 1174, 'PM', 207, 'ES', 39.40039000, 3.20335000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q63159178'),
(33150, 'Calonge', 5103, 'GI', 207, 'ES', 41.85869000, 3.07926000, '2019-10-05 22:45:39', '2022-08-29 10:53:16', 1, 'Q63159178'),
(33151, 'Calp', 5108, 'A', 207, 'ES', 38.64470000, 0.04450000, '2019-10-05 22:45:39', '2022-08-29 11:15:47', 1, 'Q671272'),
(33152, 'Caltojar', 1208, 'SO', 207, 'ES', 41.40226000, -2.76436000, '2019-10-05 22:45:39', '2022-08-29 11:51:23', 1, 'Q177501'),
(33153, 'Calvarrasa de Abajo', 1147, 'SA', 207, 'ES', 40.94638000, -5.55258000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1640886'),
(33154, 'Calvarrasa de Arriba', 1147, 'SA', 207, 'ES', 40.90672000, -5.59199000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q24014172'),
(33155, 'Calvià', 1174, 'PM', 207, 'ES', 39.56570000, 2.50621000, '2019-10-05 22:45:39', '2020-05-01 17:23:15', 1, 'Q491786'),
(33157, 'Calzada de Don Diego', 1147, 'SA', 207, 'ES', 40.90500000, -5.90279000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1648516'),
(33158, 'Calzada de Valdunciel', 1147, 'SA', 207, 'ES', 41.08663000, -5.70219000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q24014169'),
(33159, 'Calzada de los Molinos', 1157, 'P', 207, 'ES', 42.32724000, -4.65293000, '2019-10-05 22:45:39', '2022-08-29 11:45:44', 1, 'Q1929306'),
(33160, 'Calzada del Coto', 1200, 'LE', 207, 'ES', 42.38613000, -5.07809000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q926796'),
(33161, 'Calzadilla', 1190, 'CC', 207, 'ES', 40.06014000, -6.53328000, '2019-10-05 22:45:39', '2022-08-28 18:12:22', 1, 'Q1646361'),
(33162, 'Calzadilla de Tera', 1161, 'ZA', 207, 'ES', 41.97899000, -6.08243000, '2019-10-05 22:45:39', '2022-08-29 11:48:03', 1, 'Q1652984'),
(33163, 'Camarasa', 5104, 'L', 207, 'ES', 41.87486000, 0.87814000, '2019-10-05 22:45:39', '2022-08-29 10:55:25', 1, 'Q1652984'),
(33164, 'Camarena', 1205, 'TO', 207, 'ES', 40.09311000, -4.11927000, '2019-10-05 22:45:39', '2022-08-29 11:08:29', 1, 'Q763344'),
(33165, 'Camarena de la Sierra', 5111, 'TE', 207, 'ES', 40.15000000, -1.03333000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q983675'),
(33166, 'Camarenilla', 1205, 'TO', 207, 'ES', 40.01672000, -4.07624000, '2019-10-05 22:45:39', '2022-08-29 11:08:29', 1, 'Q969339'),
(33167, 'Camargo', 1170, 'S', 207, 'ES', 43.40744000, -3.88498000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q969339'),
(33168, 'Camarillas', 5111, 'TE', 207, 'ES', 40.61242000, -0.75416000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q1652442'),
(33169, 'Camariñas', 5089, 'C', 207, 'ES', 43.13115000, -9.18172000, '2019-10-05 22:45:39', '2022-08-28 13:37:16', 1, 'Q1652442'),
(33170, 'Camarma de Esteruelas', 1158, 'M', 207, 'ES', 40.55032000, -3.37295000, '2019-10-05 22:45:39', '2022-08-29 12:04:40', 1, 'Q1648727'),
(33171, 'Camarzana de Tera', 1161, 'ZA', 207, 'ES', 41.99471000, -6.02657000, '2019-10-05 22:45:39', '2022-08-29 11:48:03', 1, 'Q597421'),
(33172, 'Camas', 1193, 'SE', 207, 'ES', 37.40202000, -6.03314000, '2019-10-05 22:45:39', '2022-08-28 19:08:49', 1, 'Q752127'),
(33173, 'Camañas', 5111, 'TE', 207, 'ES', 40.64308000, -1.13752000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q24002293'),
(33174, 'Cambados', 1167, 'PO', 207, 'ES', 42.51222000, -8.81310000, '2019-10-05 22:45:39', '2022-08-28 17:57:54', 1, 'Q695477'),
(33175, 'Cambil', 5100, 'J', 207, 'ES', 37.67934000, -3.56537000, '2019-10-05 22:45:39', '2022-08-28 19:04:30', 1, 'Q1611875'),
(33176, 'Cambre', 5089, 'C', 207, 'ES', 43.29438000, -8.34736000, '2019-10-05 22:45:39', '2022-08-28 13:37:16', 1, 'Q1611875'),
(33177, 'Cambrils', 1203, 'T', 207, 'ES', 41.06997000, 1.05949000, '2019-10-05 22:45:39', '2022-08-29 10:57:32', 1, 'Q828572'),
(33178, 'Caminomorisco', 1190, 'CC', 207, 'ES', 40.32719000, -6.28923000, '2019-10-05 22:45:39', '2022-08-28 18:12:22', 1, 'Q1613482'),
(33179, 'Caminreal', 5111, 'TE', 207, 'ES', 40.83883000, -1.32416000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q1653275'),
(33180, 'Camp de Mar', 1174, 'PM', 207, 'ES', 39.53827000, 2.42386000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q5742505'),
(33181, 'Campanario', 5092, 'BA', 207, 'ES', 38.86440000, -5.61744000, '2019-10-05 22:45:39', '2022-08-28 18:09:23', 1, 'Q1605982'),
(33182, 'Campanet', 1174, 'PM', 207, 'ES', 39.77470000, 2.96506000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q850989'),
(33183, 'Campaspero', 1183, 'VA', 207, 'ES', 41.49208000, -4.19608000, '2019-10-05 22:45:39', '2022-08-29 11:48:44', 1, 'Q850989'),
(33184, 'Campazas', 1200, 'LE', 207, 'ES', 42.14219000, -5.49349000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q850989'),
(33185, 'Campdevànol', 5103, 'GI', 207, 'ES', 42.22445000, 2.16860000, '2019-10-05 22:45:39', '2022-08-29 10:53:16', 1, 'Q850989'),
(33186, 'Campillo de Altobuey', 5106, 'CU', 207, 'ES', 39.60000000, -1.80000000, '2019-10-05 22:45:39', '2022-08-29 11:05:00', 1, 'Q1771918'),
(33187, 'Campillo de Aragón', 5113, 'Z', 207, 'ES', 41.12648000, -1.84393000, '2019-10-05 22:45:39', '2022-08-29 11:42:54', 1, 'Q24016456'),
(33188, 'Campillo de Aranda', 1146, 'BU', 207, 'ES', 41.61015000, -3.73017000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q1637075'),
(33189, 'Campillo de Arenas', 5100, 'J', 207, 'ES', 37.55535000, -3.63552000, '2019-10-05 22:45:39', '2022-08-28 19:04:30', 1, 'Q780608'),
(33190, 'Campillo de Azaba', 1147, 'SA', 207, 'ES', 40.50951000, -6.68705000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1648490'),
(33191, 'Campillo de Deleitosa', 1190, 'CC', 207, 'ES', 39.70297000, -5.57436000, '2019-10-05 22:45:39', '2022-08-28 18:12:22', 1, 'Q940738'),
(33192, 'Campillo de Dueñas', 5107, 'GU', 207, 'ES', 40.88376000, -1.68505000, '2019-10-05 22:45:39', '2022-08-29 11:06:45', 1, 'Q1655088'),
(33193, 'Campillo de Llerena', 5092, 'BA', 207, 'ES', 38.50196000, -5.83139000, '2019-10-05 22:45:39', '2022-08-28 18:09:23', 1, 'Q1372581'),
(33194, 'Campillo de Ranas', 5107, 'GU', 207, 'ES', 41.08601000, -3.31431000, '2019-10-05 22:45:39', '2022-08-29 11:06:45', 1, 'Q1655938'),
(33195, 'Campillos', 5101, 'MA', 207, 'ES', 37.04826000, -4.86308000, '2019-10-05 22:45:39', '2022-08-28 19:06:52', 1, 'Q944101'),
(33196, 'Campillos-Paravientos', 5106, 'CU', 207, 'ES', 39.98333000, -1.55000000, '2019-10-05 22:45:39', '2022-08-29 11:05:00', 1, 'Q692316'),
(33197, 'Campillos-Sierra', 5106, 'CU', 207, 'ES', 40.10000000, -1.70000000, '2019-10-05 22:45:39', '2022-08-29 11:05:00', 1, 'Q513693'),
(33198, 'Campins', 5102, 'B', 207, 'ES', 41.71667000, 2.46667000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q15401'),
(33199, 'Campisábalos', 5107, 'GU', 207, 'ES', 41.26667000, -3.13333000, '2019-10-05 22:45:39', '2022-08-29 11:06:45', 1, 'Q15401'),
(33201, 'Campo Real', 1158, 'M', 207, 'ES', 40.33333000, -3.38333000, '2019-10-05 22:45:39', '2022-08-29 12:04:40', 1, 'Q1999732'),
(33202, 'Campo de Criptana', 5105, 'CR', 207, 'ES', 39.40463000, -3.12492000, '2019-10-05 22:45:39', '2022-08-29 11:03:24', 1, 'Q24011808'),
(33203, 'Campo de San Pedro', 1192, 'SG', 207, 'ES', 41.42992000, -3.54599000, '2019-10-05 22:45:39', '2022-08-29 11:50:42', 1, 'Q1904484'),
(33204, 'Campo de Villavidel', 1200, 'LE', 207, 'ES', 42.43906000, -5.52808000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q1904484'),
(33205, 'Campolara', 1146, 'BU', 207, 'ES', 42.11953000, -3.42740000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q1633479'),
(33206, 'Camponaraya', 1200, 'LE', 207, 'ES', 42.57799000, -6.66709000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q256740'),
(33207, 'Camporredondo', 1183, 'VA', 207, 'ES', 41.47333000, -4.50503000, '2019-10-05 22:45:39', '2022-08-29 11:48:44', 1, 'Q256740'),
(33208, 'Camporrells', 1177, 'HU', 207, 'ES', 41.95856000, 0.52136000, '2019-10-05 22:45:39', '2022-08-29 12:06:20', 1, 'Q256740'),
(33209, 'Camporrobles', 1175, 'V', 207, 'ES', 39.65000000, -1.40000000, '2019-10-05 22:45:39', '2022-08-29 12:05:40', 1, 'Q1648520'),
(33210, 'Campos', 1174, 'PM', 207, 'ES', 39.43099000, 3.01935000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q1648520'),
(33211, 'Campos del Río', 1176, 'MU', 207, 'ES', 38.03966000, -1.35306000, '2019-10-05 22:45:39', '2022-08-29 12:05:49', 1, 'Q1648520'),
(33212, 'Campotéjar', 5098, 'GR', 207, 'ES', 37.48235000, -3.61771000, '2019-10-05 22:45:39', '2022-08-28 18:52:57', 1, 'Q1648520'),
(33213, 'Camprodon', 5103, 'GI', 207, 'ES', 42.31185000, 2.36506000, '2019-10-05 22:45:39', '2022-08-29 10:53:16', 1, 'Q1648520'),
(33214, 'Camuñas', 1205, 'TO', 207, 'ES', 39.42704000, -3.45503000, '2019-10-05 22:45:39', '2022-08-29 11:08:29', 1, 'Q1628637'),
(33215, 'Camós', 5103, 'GI', 207, 'ES', 42.08776000, 2.76288000, '2019-10-05 22:45:39', '2022-08-29 10:53:16', 1, 'Q5495'),
(33217, 'Can Pastilla', 1174, 'PM', 207, 'ES', 39.53615000, 2.71766000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q5495'),
(33219, 'Can Picafort', 1174, 'PM', 207, 'ES', 39.76567000, 3.15488000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q5495'),
(33220, 'Canalejas de Peñafiel', 1183, 'VA', 207, 'ES', 41.52513000, -4.11546000, '2019-10-05 22:45:39', '2022-08-29 11:48:44', 1, 'Q607434'),
(33221, 'Canalejas del Arroyo', 5106, 'CU', 207, 'ES', 40.36850000, -2.49408000, '2019-10-05 22:45:39', '2022-08-29 11:05:00', 1, 'Q1902640'),
(33222, 'Canales', 1189, 'AV', 207, 'ES', 41.00331000, -4.90163000, '2019-10-05 22:45:39', '2022-08-29 11:49:56', 1, 'Q1902640'),
(33223, 'Canales de la Sierra', 1171, 'LO', 207, 'ES', 42.14233000, -3.02458000, '2019-10-05 22:45:39', '2022-08-29 12:05:09', 1, 'Q1647634'),
(33224, 'Canals', 1175, 'V', 207, 'ES', 38.96251000, -0.58443000, '2019-10-05 22:45:39', '2022-08-29 12:05:40', 1, 'Q1647634'),
(33225, 'Candasnos', 1177, 'HU', 207, 'ES', 41.50213000, 0.06425000, '2019-10-05 22:45:39', '2022-08-29 12:06:20', 1, 'Q788796'),
(33226, 'Candelaria', 5112, 'TF', 207, 'ES', 28.35480000, -16.37268000, '2019-10-05 22:45:39', '2022-08-29 11:31:13', 1, 'Q487268'),
(33227, 'Candelario', 1147, 'SA', 207, 'ES', 40.36806000, -5.74499000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q487268'),
(33228, 'Candeleda', 1189, 'AV', 207, 'ES', 40.15521000, -5.24045000, '2019-10-05 22:45:39', '2022-08-29 11:49:56', 1, 'Q1442696'),
(33229, 'Candilichera', 1208, 'SO', 207, 'ES', 41.70434000, -2.30123000, '2019-10-05 22:45:39', '2022-08-29 11:51:23', 1, 'Q830624'),
(33230, 'Candín', 1200, 'LE', 207, 'ES', 42.81695000, -6.72848000, '2019-10-05 22:45:39', '2020-05-01 17:23:16', 1, 'Q830624'),
(33231, 'Canena', 5100, 'J', 207, 'ES', 38.04930000, -3.48310000, '2019-10-05 22:45:39', '2022-08-28 19:04:30', 1, 'Q1611920'),
(33232, 'Canencia', 1158, 'M', 207, 'ES', 40.90728000, -3.73523000, '2019-10-05 22:45:39', '2022-08-29 12:04:40', 1, 'Q985038'),
(33233, 'Canet d\'En Berenguer', 1175, 'V', 207, 'ES', 39.68333000, -0.21667000, '2019-10-05 22:45:39', '2022-08-29 12:05:40', 1, 'Q985038'),
(33234, 'Canet de Mar', 5102, 'B', 207, 'ES', 41.59054000, 2.58116000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q985038'),
(33235, 'Canet lo Roig', 5110, 'CS', 207, 'ES', 40.55142000, 0.24308000, '2019-10-05 22:45:39', '2022-08-29 11:26:42', 1, 'Q1646978'),
(33236, 'Canfranc', 1177, 'HU', 207, 'ES', 42.71628000, -0.52563000, '2019-10-05 22:45:39', '2022-08-29 12:06:20', 1, 'Q24013398'),
(33238, 'Canicosa de la Sierra', 1146, 'BU', 207, 'ES', 41.93673000, -3.04089000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q1633527'),
(33239, 'Caniles', 5098, 'GR', 207, 'ES', 37.43671000, -2.72482000, '2019-10-05 22:45:39', '2022-08-28 18:52:57', 1, 'Q555929'),
(33240, 'Canillas de Abajo', 1147, 'SA', 207, 'ES', 40.92266000, -5.92905000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1640649'),
(33241, 'Canillas de Aceituno', 5101, 'MA', 207, 'ES', 36.87303000, -4.08254000, '2019-10-05 22:45:39', '2022-08-28 19:06:52', 1, 'Q1647722'),
(33242, 'Canillas de Albaida', 5101, 'MA', 207, 'ES', 36.84665000, -3.98678000, '2019-10-05 22:45:39', '2022-08-28 19:06:52', 1, 'Q1647757'),
(33243, 'Canillas de Esgueva', 1183, 'VA', 207, 'ES', 41.75464000, -4.12502000, '2019-10-05 22:45:39', '2022-08-29 11:48:44', 1, 'Q1907128'),
(33244, 'Canillas de Río Tuerto', 1171, 'LO', 207, 'ES', 42.39950000, -2.84091000, '2019-10-05 22:45:39', '2022-08-29 12:05:09', 1, 'Q929835'),
(33245, 'Canjáyar', 5095, 'AL', 207, 'ES', 37.00959000, -2.73943000, '2019-10-05 22:45:39', '2022-08-28 18:41:41', 1, 'Q1157972'),
(33246, 'Canovelles', 5102, 'B', 207, 'ES', 41.61667000, 2.28333000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q1157972'),
(33247, 'Canredondo', 5107, 'GU', 207, 'ES', 40.81257000, -2.49377000, '2019-10-05 22:45:39', '2022-08-29 11:06:45', 1, 'Q950807'),
(33248, 'Cantabrana', 1146, 'BU', 207, 'ES', 42.73422000, -3.46704000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q568730'),
(33249, 'Cantagallo', 1147, 'SA', 207, 'ES', 40.37239000, -5.81890000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1939628'),
(33250, 'Cantalapiedra', 1147, 'SA', 207, 'ES', 41.12630000, -5.18351000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1628716'),
(33251, 'Cantalejo', 1192, 'SG', 207, 'ES', 41.25760000, -3.92791000, '2019-10-05 22:45:39', '2022-08-29 11:50:42', 1, 'Q1650171'),
(33252, 'Cantallops', 5103, 'GI', 207, 'ES', 42.42216000, 2.92524000, '2019-10-05 22:45:39', '2022-08-29 10:53:16', 1, 'Q1650171'),
(33253, 'Cantalojas', 5107, 'GU', 207, 'ES', 41.23472000, -3.24629000, '2019-10-05 22:45:39', '2022-08-29 11:06:45', 1, 'Q1656094'),
(33254, 'Cantalpino', 1147, 'SA', 207, 'ES', 41.05341000, -5.33045000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1640807'),
(33255, 'Cantaracillo', 1147, 'SA', 207, 'ES', 40.90376000, -5.16291000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1640695'),
(33256, 'Cantavieja', 5111, 'TE', 207, 'ES', 40.52642000, -0.40558000, '2019-10-05 22:45:39', '2022-08-29 11:29:44', 1, 'Q24015155'),
(33257, 'Cantillana', 1193, 'SE', 207, 'ES', 37.61032000, -5.82472000, '2019-10-05 22:45:39', '2022-08-28 19:08:49', 1, 'Q683023'),
(33258, 'Cantimpalos', 1192, 'SG', 207, 'ES', 41.07406000, -4.15988000, '2019-10-05 22:45:39', '2022-08-29 11:50:42', 1, 'Q594928'),
(33259, 'Cantiveros', 1189, 'AV', 207, 'ES', 40.95215000, -4.95455000, '2019-10-05 22:45:39', '2022-08-29 11:49:56', 1, 'Q1620339'),
(33260, 'Cantoria', 5095, 'AL', 207, 'ES', 37.35146000, -2.19209000, '2019-10-05 22:45:39', '2022-08-28 18:41:41', 1, 'Q1620339'),
(33261, 'Canyelles', 5102, 'B', 207, 'ES', 41.44509000, 2.16346000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q1620339'),
(33262, 'Capafonts', 1203, 'T', 207, 'ES', 41.30000000, 1.03333000, '2019-10-05 22:45:39', '2022-08-29 10:57:32', 1, 'Q645266'),
(33263, 'Caparroso', 1204, 'NA', 207, 'ES', 42.34129000, -1.64962000, '2019-10-05 22:45:39', '2022-08-29 12:06:07', 1, 'Q1641846'),
(33264, 'Capdepera', 1174, 'PM', 207, 'ES', 39.70237000, 3.43532000, '2019-10-05 22:45:39', '2019-10-05 22:45:39', 1, 'Q920971'),
(33265, 'Capdesaso', 1177, 'HU', 207, 'ES', 41.84287000, -0.18316000, '2019-10-05 22:45:39', '2022-08-29 12:06:20', 1, 'Q939623'),
(33266, 'Capella', 1177, 'HU', 207, 'ES', 42.19635000, 0.39637000, '2019-10-05 22:45:39', '2022-08-29 12:06:20', 1, 'Q939623'),
(33267, 'Capellades', 5102, 'B', 207, 'ES', 41.53005000, 1.68651000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q939623'),
(33268, 'Capileira', 5098, 'GR', 207, 'ES', 36.96148000, -3.35864000, '2019-10-05 22:45:39', '2022-08-28 18:52:57', 1, 'Q555140'),
(33269, 'Capilla', 5092, 'BA', 207, 'ES', 38.82037000, -5.08417000, '2019-10-05 22:45:39', '2022-08-28 18:09:23', 1, 'Q1639065'),
(33270, 'Capillas', 1157, 'P', 207, 'ES', 42.01362000, -4.89051000, '2019-10-05 22:45:39', '2022-08-29 11:45:44', 1, 'Q1923879'),
(33271, 'Capmany', 5103, 'GI', 207, 'ES', 42.37351000, 2.92026000, '2019-10-05 22:45:39', '2022-08-29 10:53:16', 1, 'Q7106'),
(33272, 'Carabanchel', 1158, 'M', 207, 'ES', 40.39094000, -3.72420000, '2019-10-05 22:45:39', '2022-08-29 12:04:40', 1, 'Q1001991'),
(33273, 'Carabantes', 1208, 'SO', 207, 'ES', 41.55211000, -1.99810000, '2019-10-05 22:45:39', '2022-08-29 11:51:23', 1, 'Q24015506'),
(33274, 'Carabaña', 1158, 'M', 207, 'ES', 40.25424000, -3.23572000, '2019-10-05 22:45:39', '2022-08-29 12:04:40', 1, 'Q664618'),
(33275, 'Caracena', 1208, 'SO', 207, 'ES', 41.38321000, -3.09146000, '2019-10-05 22:45:39', '2022-08-29 11:51:23', 1, 'Q664618'),
(33276, 'Carataunas', 5098, 'GR', 207, 'ES', 36.92204000, -3.40834000, '2019-10-05 22:45:39', '2022-08-28 18:52:57', 1, 'Q543960'),
(33277, 'Caravaca', 1176, 'MU', 207, 'ES', 38.10558000, -1.86343000, '2019-10-05 22:45:39', '2022-08-29 12:05:49', 1, 'Q23986652'),
(33278, 'Carazo', 1146, 'BU', 207, 'ES', 41.96832000, -3.35310000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q23986652'),
(33279, 'Carbajales de Alba', 1161, 'ZA', 207, 'ES', 41.65347000, -5.99706000, '2019-10-05 22:45:39', '2022-08-29 11:48:03', 1, 'Q1650438'),
(33280, 'Carbajo', 1190, 'CC', 207, 'ES', 39.60333000, -7.19565000, '2019-10-05 22:45:39', '2022-08-28 18:12:22', 1, 'Q384303'),
(33281, 'Carbajosa de la Sagrada', 1147, 'SA', 207, 'ES', 40.93305000, -5.65026000, '2019-10-05 22:45:39', '2022-08-29 11:44:51', 1, 'Q1640862'),
(33282, 'Carballeda de Avia', 5091, 'OR', 207, 'ES', 42.32140000, -8.16456000, '2019-10-05 22:45:39', '2022-08-28 17:53:26', 1, 'Q940960'),
(33283, 'Carballo', 5089, 'C', 207, 'ES', 43.21300000, -8.69104000, '2019-10-05 22:45:39', '2022-08-28 13:37:16', 1, 'Q940960'),
(33284, 'Carbellino', 1161, 'ZA', 207, 'ES', 41.22975000, -6.14901000, '2019-10-05 22:45:39', '2022-08-29 11:48:03', 1, 'Q1778126'),
(33285, 'Carboneras', 5095, 'AL', 207, 'ES', 36.99666000, -1.89651000, '2019-10-05 22:45:39', '2022-08-28 18:41:41', 1, 'Q1110337'),
(33286, 'Carboneras de Guadazaón', 5106, 'CU', 207, 'ES', 39.88333000, -1.80000000, '2019-10-05 22:45:39', '2022-08-29 11:05:01', 1, 'Q1771143'),
(33287, 'Carbonero el Mayor', 1192, 'SG', 207, 'ES', 41.12257000, -4.26478000, '2019-10-05 22:45:39', '2022-08-29 11:50:42', 1, 'Q1776836'),
(33288, 'Carboneros', 5100, 'J', 207, 'ES', 38.22958000, -3.63139000, '2019-10-05 22:45:39', '2022-08-28 19:04:30', 1, 'Q1635984'),
(33289, 'Carcaboso', 1190, 'CC', 207, 'ES', 40.04968000, -6.21375000, '2019-10-05 22:45:39', '2022-08-28 18:12:22', 1, 'Q1613467'),
(33290, 'Carcabuey', 5097, 'CO', 207, 'ES', 37.44420000, -4.27471000, '2019-10-05 22:45:39', '2022-08-28 18:49:38', 1, 'Q842679'),
(33291, 'Carcaixent', 1175, 'V', 207, 'ES', 39.12180000, -0.44812000, '2019-10-05 22:45:39', '2022-08-29 12:05:40', 1, 'Q842679'),
(33292, 'Carcastillo', 1204, 'NA', 207, 'ES', 42.37908000, -1.44376000, '2019-10-05 22:45:39', '2022-08-29 12:06:07', 1, 'Q1648542'),
(33293, 'Carcedo de Bureba', 1146, 'BU', 207, 'ES', 42.57818000, -3.49831000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q1637088'),
(33294, 'Carcedo de Burgos', 1146, 'BU', 207, 'ES', 42.28696000, -3.62274000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q543897'),
(33296, 'Cardedeu', 5102, 'B', 207, 'ES', 41.63976000, 2.35739000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q24008371'),
(33297, 'Cardenete', 5106, 'CU', 207, 'ES', 39.76667000, -1.68333000, '2019-10-05 22:45:39', '2022-08-29 11:05:01', 1, 'Q1902609'),
(33298, 'Cardeña', 5097, 'CO', 207, 'ES', 38.27023000, -4.32358000, '2019-10-05 22:45:39', '2022-08-28 18:49:38', 1, 'Q1645727'),
(33299, 'Cardeñadijo', 1146, 'BU', 207, 'ES', 42.30217000, -3.66779000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q1634005'),
(33300, 'Cardeñajimeno', 1146, 'BU', 207, 'ES', 42.33038000, -3.62103000, '2019-10-05 22:45:39', '2022-08-29 11:24:19', 1, 'Q1607122'),
(33301, 'Cardeñosa', 1189, 'AV', 207, 'ES', 40.74240000, -4.74579000, '2019-10-05 22:45:39', '2022-08-29 11:49:56', 1, 'Q285411'),
(33302, 'Cardeñosa de Volpejera', 1157, 'P', 207, 'ES', 42.23214000, -4.70197000, '2019-10-05 22:45:39', '2022-08-29 11:45:44', 1, 'Q922250'),
(33303, 'Cardiel de los Montes', 1205, 'TO', 207, 'ES', 40.06348000, -4.65488000, '2019-10-05 22:45:39', '2022-08-29 11:08:29', 1, 'Q1642068'),
(33304, 'Cardona', 5102, 'B', 207, 'ES', 41.91371000, 1.67855000, '2019-10-05 22:45:39', '2022-08-29 10:50:00', 1, 'Q1642068'),
(33305, 'Carenas', 5113, 'Z', 207, 'ES', 41.27755000, -1.79754000, '2019-10-05 22:45:39', '2022-08-29 11:42:54', 1, 'Q24020003'),
(33306, 'Cariñena', 5113, 'Z', 207, 'ES', 41.33733000, -1.22444000, '2019-10-05 22:45:40', '2022-08-29 11:42:54', 1, 'Q24002479'),
(33307, 'Cariño', 5089, 'C', 207, 'ES', 43.74134000, -7.86715000, '2019-10-05 22:45:40', '2022-08-28 13:37:17', 1, 'Q24002479'),
(33308, 'Carlet', 1175, 'V', 207, 'ES', 39.22660000, -0.52142000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q1312654'),
(33309, 'Carmena', 1205, 'TO', 207, 'ES', 39.95562000, -4.40149000, '2019-10-05 22:45:40', '2022-08-29 11:08:29', 1, 'Q1641218'),
(33310, 'Carmona', 1193, 'SE', 207, 'ES', 37.47125000, -5.64608000, '2019-10-05 22:45:40', '2022-08-28 19:08:49', 1, 'Q23986680'),
(33311, 'Carmonita', 5092, 'BA', 207, 'ES', 39.15455000, -6.33864000, '2019-10-05 22:45:40', '2022-08-28 18:09:23', 1, 'Q1372506'),
(33312, 'Carnota', 5089, 'C', 207, 'ES', 42.82330000, -9.08913000, '2019-10-05 22:45:40', '2022-08-28 13:37:16', 1, 'Q1372506'),
(33313, 'Carpio', 1183, 'VA', 207, 'ES', 41.21329000, -5.10907000, '2019-10-05 22:45:40', '2022-08-29 11:48:44', 1, 'Q2000446'),
(33314, 'Carpio de Azaba', 1147, 'SA', 207, 'ES', 40.59602000, -6.64680000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1640680'),
(33315, 'Carracedelo', 1200, 'LE', 207, 'ES', 42.55602000, -6.73317000, '2019-10-05 22:45:40', '2019-10-05 22:45:40', 1, 'Q1640680'),
(33316, 'Carral', 5089, 'C', 207, 'ES', 43.22860000, -8.35545000, '2019-10-05 22:45:40', '2022-08-28 13:37:16', 1, 'Q601352'),
(33317, 'Carranque', 1205, 'TO', 207, 'ES', 40.16976000, -3.90092000, '2019-10-05 22:45:40', '2022-08-29 11:08:29', 1, 'Q1642057'),
(33318, 'Carrascal de Barregas', 1147, 'SA', 207, 'ES', 40.97868000, -5.76227000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1648479'),
(33319, 'Carrascal del Obispo', 1147, 'SA', 207, 'ES', 40.76376000, -5.99913000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1640566'),
(33320, 'Carrascalejo', 1190, 'CC', 207, 'ES', 39.63333000, -5.21667000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1646388'),
(33321, 'Carrascosa', 5106, 'CU', 207, 'ES', 40.59092000, -2.16305000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q2571463'),
(33322, 'Carrascosa de Abajo', 1208, 'SO', 207, 'ES', 41.42338000, -3.08955000, '2019-10-05 22:45:40', '2022-08-29 11:51:23', 1, 'Q833076'),
(33323, 'Carrascosa de Haro', 5106, 'CU', 207, 'ES', 39.59757000, -2.54182000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1902650'),
(33324, 'Carrascosa de la Sierra', 1208, 'SO', 207, 'ES', 41.89535000, -2.28003000, '2019-10-05 22:45:40', '2022-08-29 11:51:23', 1, 'Q831636'),
(33325, 'Carratraca', 5101, 'MA', 207, 'ES', 36.85290000, -4.81998000, '2019-10-05 22:45:40', '2022-08-28 19:06:52', 1, 'Q1650447'),
(33326, 'Carrias', 1146, 'BU', 207, 'ES', 42.48141000, -3.28319000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q1633492'),
(33327, 'Carriches', 1205, 'TO', 207, 'ES', 39.96417000, -4.45864000, '2019-10-05 22:45:40', '2022-08-29 11:08:29', 1, 'Q1644871'),
(33328, 'Carrizal', 1185, 'GC', 207, 'ES', 27.91161000, -15.40558000, '2019-10-05 22:45:40', '2022-08-29 12:06:32', 1, 'Q1644871'),
(33329, 'Carrizo de la Ribera', 1200, 'LE', 207, 'ES', 42.58395000, -5.82881000, '2019-10-05 22:45:40', '2019-10-05 22:45:40', 1, 'Q1650252'),
(33330, 'Carrizosa', 5105, 'CR', 207, 'ES', 38.84204000, -2.99250000, '2019-10-05 22:45:40', '2022-08-29 11:03:24', 1, 'Q1642284'),
(33331, 'Carrión de Calatrava', 5105, 'CR', 207, 'ES', 39.01897000, -3.81683000, '2019-10-05 22:45:40', '2022-08-29 11:03:24', 1, 'Q1614497'),
(33332, 'Carrión de los Condes', 1157, 'P', 207, 'ES', 42.34130000, -4.60071000, '2019-10-05 22:45:40', '2022-08-29 11:45:44', 1, 'Q24012217'),
(33333, 'Carrión de los Céspedes', 1193, 'SE', 207, 'ES', 37.37007000, -6.32923000, '2019-10-05 22:45:40', '2022-08-28 19:08:49', 1, 'Q1447344'),
(33334, 'Carrocera', 1200, 'LE', 207, 'ES', 42.79605000, -5.74374000, '2019-10-05 22:45:40', '2019-10-05 22:45:40', 1, 'Q137207'),
(33335, 'Carrícola', 1175, 'V', 207, 'ES', 38.84133000, -0.47260000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q137207'),
(33336, 'Cartagena', 1176, 'MU', 207, 'ES', 37.60512000, -0.98623000, '2019-10-05 22:45:40', '2022-08-29 12:05:49', 1, 'Q162615'),
(33337, 'Cartajima', 5101, 'MA', 207, 'ES', 36.64548000, -5.15410000, '2019-10-05 22:45:40', '2022-08-28 19:06:52', 1, 'Q1630168'),
(33338, 'Cartaya', 5099, 'H', 207, 'ES', 37.28114000, -7.15071000, '2019-10-05 22:45:40', '2022-08-28 19:00:43', 1, 'Q1630168'),
(33339, 'Cartelle', 5091, 'OR', 207, 'ES', 42.25109000, -8.07062000, '2019-10-05 22:45:40', '2022-08-28 17:53:26', 1, 'Q1630168'),
(33340, 'Cartes', 1170, 'S', 207, 'ES', 43.32596000, -4.06893000, '2019-10-05 22:45:40', '2019-10-05 22:45:40', 1, 'Q1630168'),
(33341, 'Carucedo', 1200, 'LE', 207, 'ES', 42.49029000, -6.76565000, '2019-10-05 22:45:40', '2019-10-05 22:45:40', 1, 'Q1630168'),
(33342, 'Casa de Uceda', 5107, 'GU', 207, 'ES', 40.84115000, -3.36849000, '2019-10-05 22:45:40', '2022-08-29 11:06:45', 1, 'Q1630168'),
(33343, 'Casabermeja', 5101, 'MA', 207, 'ES', 36.89260000, -4.42938000, '2019-10-05 22:45:40', '2022-08-28 19:06:52', 1, 'Q1630405'),
(33344, 'Casafranca', 1147, 'SA', 207, 'ES', 40.59267000, -5.76039000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1628754'),
(33345, 'Casalarreina', 1171, 'LO', 207, 'ES', 42.54911000, -2.90994000, '2019-10-05 22:45:40', '2022-08-29 12:05:09', 1, 'Q1636990'),
(33346, 'Casar de Cáceres', 1190, 'CC', 207, 'ES', 39.56106000, -6.41944000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q578081'),
(33347, 'Casar de Palomero', 1190, 'CC', 207, 'ES', 40.29483000, -6.25698000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1630093'),
(33348, 'Casarabonela', 5101, 'MA', 207, 'ES', 36.78616000, -4.84276000, '2019-10-05 22:45:40', '2022-08-28 19:06:52', 1, 'Q1630355'),
(33349, 'Casarejos', 1208, 'SO', 207, 'ES', 41.79651000, -3.03251000, '2019-10-05 22:45:40', '2022-08-29 11:51:23', 1, 'Q829772'),
(33350, 'Casares', 5101, 'MA', 207, 'ES', 36.44689000, -5.28580000, '2019-10-05 22:45:40', '2022-08-28 19:06:52', 1, 'Q913483'),
(33351, 'Casares de las Hurdes', 1190, 'CC', 207, 'ES', 40.43333000, -6.28333000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1613360'),
(33352, 'Casariche', 1193, 'SE', 207, 'ES', 37.29389000, -4.75972000, '2019-10-05 22:45:40', '2022-08-28 19:08:49', 1, 'Q1445334'),
(33353, 'Casarrubios del Monte', 1205, 'TO', 207, 'ES', 40.18697000, -4.03644000, '2019-10-05 22:45:40', '2022-08-29 11:08:29', 1, 'Q1628648'),
(33354, 'Casarrubuelos', 1158, 'M', 207, 'ES', 40.17146000, -3.83105000, '2019-10-05 22:45:40', '2022-08-29 12:04:40', 1, 'Q1772185'),
(33355, 'Casas Altas', 1175, 'V', 207, 'ES', 40.03333000, -1.26667000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q1917899'),
(33356, 'Casas Bajas', 1175, 'V', 207, 'ES', 40.01667000, -1.26667000, '2019-10-05 22:45:40', '2022-08-29 12:05:40', 1, 'Q1917899'),
(33358, 'Casas de Benítez', 5106, 'CU', 207, 'ES', 39.36667000, -2.13333000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1770932'),
(33359, 'Casas de Don Antonio', 1190, 'CC', 207, 'ES', 39.23578000, -6.29143000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1648589'),
(33360, 'Casas de Don Gómez', 1190, 'CC', 207, 'ES', 40.00932000, -6.60073000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1646339'),
(33361, 'Casas de Don Pedro', 5092, 'BA', 207, 'ES', 39.10822000, -5.33077000, '2019-10-05 22:45:40', '2022-08-28 18:09:23', 1, 'Q23986732'),
(33362, 'Casas de Fernando Alonso', 5106, 'CU', 207, 'ES', 39.35065000, -2.32402000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q931490'),
(33363, 'Casas de Garcimolina', 5106, 'CU', 207, 'ES', 40.00000000, -1.41667000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q301086'),
(33364, 'Casas de Guijarro', 5106, 'CU', 207, 'ES', 39.35000000, -2.16667000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1903489'),
(33365, 'Casas de Haro', 5106, 'CU', 207, 'ES', 39.33355000, -2.27273000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1920004'),
(33368, 'Casas de Millán', 1190, 'CC', 207, 'ES', 39.81757000, -6.32966000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1658755'),
(33369, 'Casas de Miravete', 1190, 'CC', 207, 'ES', 39.72687000, -5.74363000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1645489'),
(33370, 'Casas de Reina', 5092, 'BA', 207, 'ES', 38.20000000, -5.96667000, '2019-10-05 22:45:40', '2022-08-28 18:09:23', 1, 'Q1157670'),
(33371, 'Casas de San Galindo', 5107, 'GU', 207, 'ES', 40.87234000, -2.95750000, '2019-10-05 22:45:40', '2022-08-29 11:06:45', 1, 'Q1655931'),
(33373, 'Casas de los Pinos', 5106, 'CU', 207, 'ES', 39.33375000, -2.36984000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q609780'),
(33374, 'Casas del Castañar', 1190, 'CC', 207, 'ES', 40.10785000, -5.90509000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q944113'),
(33375, 'Casas del Monte', 1190, 'CC', 207, 'ES', 40.20320000, -5.96152000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1643605'),
(33376, 'Casasbuenas', 1205, 'TO', 207, 'ES', 39.76117000, -4.12558000, '2019-10-05 22:45:40', '2022-08-29 11:08:29', 1, 'Q427980'),
(33377, 'Casaseca de Campeán', 1161, 'ZA', 207, 'ES', 41.37429000, -5.74648000, '2019-10-05 22:45:40', '2022-08-29 11:48:03', 1, 'Q630629'),
(33378, 'Casaseca de las Chanas', 1161, 'ZA', 207, 'ES', 41.43870000, -5.67547000, '2019-10-05 22:45:40', '2022-08-29 11:48:03', 1, 'Q1765357'),
(33379, 'Casasimarro', 5106, 'CU', 207, 'ES', 39.36667000, -2.03333000, '2019-10-05 22:45:40', '2022-08-29 11:05:01', 1, 'Q1647141'),
(33380, 'Casasola de Arión', 1183, 'VA', 207, 'ES', 41.57825000, -5.24076000, '2019-10-05 22:45:40', '2022-08-29 11:48:44', 1, 'Q2046747'),
(33381, 'Casatejada', 1190, 'CC', 207, 'ES', 39.88642000, -5.68193000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q1613382'),
(33382, 'Casavieja', 1189, 'AV', 207, 'ES', 40.28325000, -4.76670000, '2019-10-05 22:45:40', '2022-08-29 11:49:56', 1, 'Q1445437'),
(33383, 'Casbas de Huesca', 1177, 'HU', 207, 'ES', 42.15550000, -0.13990000, '2019-10-05 22:45:40', '2022-08-29 12:06:20', 1, 'Q598350'),
(33384, 'Cascajares de Bureba', 1146, 'BU', 207, 'ES', 42.67884000, -3.23768000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q1645356'),
(33385, 'Cascajares de la Sierra', 1146, 'BU', 207, 'ES', 42.06177000, -3.39936000, '2019-10-05 22:45:40', '2022-08-29 11:24:19', 1, 'Q1626948'),
(33386, 'Cascante', 1204, 'NA', 207, 'ES', 41.99769000, -1.68098000, '2019-10-05 22:45:40', '2022-08-29 12:06:07', 1, 'Q986089'),
(33387, 'Cascante del Río', 5111, 'TE', 207, 'ES', 40.19652000, -1.11414000, '2019-10-05 22:45:40', '2022-08-29 11:29:44', 1, 'Q1652429'),
(33388, 'Casillas', 1189, 'AV', 207, 'ES', 40.32610000, -4.57182000, '2019-10-05 22:45:40', '2022-08-29 11:49:56', 1, 'Q1652429'),
(33389, 'Casillas de Coria', 1190, 'CC', 207, 'ES', 39.96543000, -6.63711000, '2019-10-05 22:45:40', '2022-08-28 18:12:22', 1, 'Q975013'),
(33390, 'Casillas de Flores', 1147, 'SA', 207, 'ES', 40.38108000, -6.75602000, '2019-10-05 22:45:40', '2022-08-29 11:44:51', 1, 'Q1628765');

