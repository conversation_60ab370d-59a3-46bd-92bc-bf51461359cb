INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(51803, 'Wenvoe', 2338, 'WLS', 232, 'GB', 51.44776000, -3.26369000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7983059'),
(51804, 'We<PERSON><PERSON>', 2336, 'ENG', 232, 'GB', 52.15960000, -2.87500000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1998959'),
(51805, 'West Bergholt', 2336, 'ENG', 232, 'GB', 51.91221000, 0.84986000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q784107'),
(51806, 'West Berkshire', 2336, 'ENG', 232, 'GB', 51.41667000, -1.25000000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1473780'),
(51807, 'West Bridgford', 2336, 'ENG', 232, 'GB', 52.92979000, -1.12537000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2079944'),
(51808, 'West Bromwich', 2336, 'ENG', 232, 'GB', 52.51868000, -1.99450000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q212826'),
(51809, 'West Byfleet', 2336, 'ENG', 232, 'GB', 51.33764000, -0.50649000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2564185'),
(51810, 'West Calder', 2335, 'SCT', 232, 'GB', 55.85188000, -3.56981000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1011751'),
(51811, 'West Clandon', 2336, 'ENG', 232, 'GB', 51.26063000, -0.50323000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2270647'),
(51812, 'West Coker', 2336, 'ENG', 232, 'GB', 50.91912000, -2.68707000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1825283'),
(51813, 'West Cornforth', 2336, 'ENG', 232, 'GB', 54.70286000, -1.51938000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7984873'),
(51814, 'West Drayton', 2336, 'ENG', 232, 'GB', 51.50000000, -0.46667000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7984873'),
(51815, 'West Dunbartonshire', 2335, 'SCT', 232, 'GB', 55.96667000, -4.53333000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q208121'),
(51816, 'West End', 2336, 'ENG', 232, 'GB', 50.92741000, -1.33282000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2528648'),
(51817, 'West End of London', 2336, 'ENG', 232, 'GB', 51.51414000, -0.15510000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2528648'),
(51818, 'West Haddon', 2336, 'ENG', 232, 'GB', 52.34168000, -1.07804000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2134047'),
(51819, 'West Hallam', 2336, 'ENG', 232, 'GB', 52.97093000, -1.35846000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q593594'),
(51820, 'West Horsley', 2336, 'ENG', 232, 'GB', 51.26033000, -0.45563000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q4540047'),
(51821, 'West Ilsley', 2336, 'ENG', 232, 'GB', 51.53993000, -1.32368000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q4540047'),
(51822, 'West Kilbride', 2335, 'SCT', 232, 'GB', 55.69004000, -4.85771000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1010320'),
(51823, 'West Kingsdown', 2336, 'ENG', 232, 'GB', 51.34281000, 0.26127000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2353204'),
(51824, 'West Kirby', 2336, 'ENG', 232, 'GB', 53.37302000, -3.18417000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q633627'),
(51825, 'West Linton', 2335, 'SCT', 232, 'GB', 55.74972000, -3.35607000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1011845'),
(51826, 'West Lothian', 2335, 'SCT', 232, 'GB', 55.91667000, -3.50000000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q204940'),
(51827, 'West Malling', 2336, 'ENG', 232, 'GB', 51.29273000, 0.40907000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1356429'),
(51828, 'West Mersea', 2336, 'ENG', 232, 'GB', 51.77798000, 0.91873000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2343199'),
(51829, 'West Molesey', 2336, 'ENG', 232, 'GB', 51.39985000, -0.37997000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2343199'),
(51830, 'West Rainton', 2336, 'ENG', 232, 'GB', 54.81667000, -1.50000000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2178603'),
(51831, 'West Sussex', 2336, 'ENG', 232, 'GB', 51.00000000, -0.41667000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q23287'),
(51832, 'West Thurrock', 2336, 'ENG', 232, 'GB', 51.47828000, 0.27672000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q971550'),
(51833, 'West Walton', 2336, 'ENG', 232, 'GB', 52.69782000, 0.17406000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2111159'),
(51834, 'West Wellow', 2336, 'ENG', 232, 'GB', 50.97273000, -1.58293000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2111159'),
(51835, 'West Wickham', 2336, 'ENG', 232, 'GB', 51.36667000, -0.01667000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2111159'),
(51836, 'Westbury', 2336, 'ENG', 232, 'GB', 51.26000000, -2.18750000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1234797'),
(51837, 'Westcliff-on-Sea', 2336, 'ENG', 232, 'GB', 51.54424000, 0.69179000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2584915'),
(51838, 'Westcott', 2336, 'ENG', 232, 'GB', 51.22438000, -0.37195000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7987310'),
(51839, 'Westergate', 2336, 'ENG', 232, 'GB', 50.83988000, -0.67123000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7987310'),
(51840, 'Westerham', 2336, 'ENG', 232, 'GB', 51.26632000, 0.06892000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2321393'),
(51841, 'Westfield', 2336, 'ENG', 232, 'GB', 51.88333000, -1.86667000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2321393'),
(51842, 'Westgate on Sea', 2336, 'ENG', 232, 'GB', 51.38239000, 1.33673000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1812807'),
(51843, 'Westhill', 2335, 'SCT', 232, 'GB', 57.15263000, -2.27966000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1002453'),
(51844, 'Westhoughton', 2336, 'ENG', 232, 'GB', 53.54899000, -2.52464000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q686319'),
(51845, 'Weston', 2336, 'ENG', 232, 'GB', 53.06667000, -2.40000000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7989350'),
(51846, 'Weston Turville', 2336, 'ENG', 232, 'GB', 51.79168000, -0.75767000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q3079423'),
(51847, 'Weston-super-Mare', 2336, 'ENG', 232, 'GB', 51.34603000, -2.97665000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q845623'),
(51848, 'Westoning', 2336, 'ENG', 232, 'GB', 51.98140000, -0.49698000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1519188'),
(51849, 'Westonzoyland', 2336, 'ENG', 232, 'GB', 51.10854000, -2.92843000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2337112'),
(51850, 'Westquarter', 2335, 'SCT', 232, 'GB', 55.99142000, -3.74016000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2337112'),
(51851, 'Westwood', 2336, 'ENG', 232, 'GB', 51.33462000, -2.27975000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7989852'),
(51852, 'Wetheral', 2336, 'ENG', 232, 'GB', 54.88401000, -2.83327000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2391907'),
(51853, 'Wetherby', 2336, 'ENG', 232, 'GB', 53.92836000, -1.38672000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q817481'),
(51854, 'Wetwang', 2336, 'ENG', 232, 'GB', 54.01750000, -0.57738000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2793942'),
(51855, 'Weybridge', 2336, 'ENG', 232, 'GB', 51.37178000, -0.45975000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1754096'),
(51856, 'Weymouth', 2336, 'ENG', 232, 'GB', 50.61448000, -2.45991000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q661619'),
(51857, 'Whaley Bridge', 2336, 'ENG', 232, 'GB', 53.33031000, -1.98260000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q652597'),
(51858, 'Whalley', 2336, 'ENG', 232, 'GB', 53.82209000, -2.40712000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1009524'),
(51859, 'Whaplode', 2336, 'ENG', 232, 'GB', 52.79934000, -0.03639000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7990637'),
(51860, 'Wheathampstead', 2336, 'ENG', 232, 'GB', 51.81148000, -0.29371000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q19947'),
(51861, 'Wheatley', 2336, 'ENG', 232, 'GB', 51.74731000, -1.13936000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1997905'),
(51862, 'Wheaton Aston', 2336, 'ENG', 232, 'GB', 52.71145000, -2.22064000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7991955'),
(51863, 'Wheldrake', 2336, 'ENG', 232, 'GB', 53.89624000, -0.96303000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7992390'),
(51864, 'Whickham', 2336, 'ENG', 232, 'GB', 54.94561000, -1.67635000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2573389'),
(51865, 'Whimple', 2336, 'ENG', 232, 'GB', 50.76649000, -3.35655000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2458749'),
(51866, 'Whitburn', 2335, 'SCT', 232, 'GB', 55.86667000, -3.68333000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q840684'),
(51867, 'Whitburn', 2336, 'ENG', 232, 'GB', 54.95333000, -1.36861000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7994159'),
(51868, 'Whitby', 2336, 'ENG', 232, 'GB', 54.48774000, -0.61498000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q852778'),
(51869, 'Whitchurch', 2336, 'ENG', 232, 'GB', 52.96667000, -2.68333000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q636627'),
(51870, 'White Waltham', 2336, 'ENG', 232, 'GB', 51.49225000, -0.77239000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2011783'),
(51871, 'Whitecraig', 2335, 'SCT', 232, 'GB', 55.91937000, -3.04231000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2011783'),
(51872, 'Whitefield', 2336, 'ENG', 232, 'GB', 53.55000000, -2.30000000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1615791'),
(51873, 'Whitehaven', 2336, 'ENG', 232, 'GB', 54.54897000, -3.58412000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1012481'),
(51874, 'Whitehead', 2337, 'NIR', 232, 'GB', 54.75371000, -5.70933000, '2019-10-05 22:53:18', '2021-01-23 14:35:23', 1, 'Q1233469'),
(51875, 'Whitehills', 2335, 'SCT', 232, 'GB', 57.67730000, -2.57863000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1009701'),
(51876, 'Whiteparish', 2336, 'ENG', 232, 'GB', 51.01041000, -1.64855000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2730786'),
(51877, 'Whitland', 2338, 'WLS', 232, 'GB', 51.81889000, -4.61528000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1996667'),
(51878, 'Whitley', 2336, 'ENG', 232, 'GB', 51.39528000, -2.16444000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7996551'),
(51879, 'Whitley Bay', 2336, 'ENG', 232, 'GB', 55.03973000, -1.44713000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2575441'),
(51880, 'Whitstable', 2336, 'ENG', 232, 'GB', 51.36070000, 1.02570000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q964785'),
(51881, 'Whittingham', 2336, 'ENG', 232, 'GB', 55.40115000, -1.89340000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q4338665'),
(51882, 'Whittington', 2336, 'ENG', 232, 'GB', 52.67372000, -1.76091000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2582510'),
(51883, 'Whittlesey', 2336, 'ENG', 232, 'GB', 52.55804000, -0.13016000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2335054'),
(51884, 'Whittlesford', 2336, 'ENG', 232, 'GB', 52.11345000, 0.14969000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2238306'),
(51885, 'Whitwell', 2336, 'ENG', 232, 'GB', 53.28333000, -1.21667000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2137106'),
(51886, 'Whitworth', 2336, 'ENG', 232, 'GB', 53.65601000, -2.17710000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q338864'),
(51887, 'Whyteleafe', 2336, 'ENG', 232, 'GB', 51.30808000, -0.08429000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1610387'),
(51888, 'Wick', 2338, 'WLS', 232, 'GB', 51.43944000, -3.54944000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q3400611'),
(51889, 'Wick', 2336, 'ENG', 232, 'GB', 51.45306000, -2.42361000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q7998220'),
(51890, 'Wick', 2335, 'SCT', 232, 'GB', 58.43906000, -3.09424000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1012502'),
(51891, 'Wickford', 2336, 'ENG', 232, 'GB', 51.61101000, 0.52331000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q932841'),
(51892, 'Wickham', 2336, 'ENG', 232, 'GB', 50.89924000, -1.18815000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q642227'),
(51893, 'Wickham Bishops', 2336, 'ENG', 232, 'GB', 51.77830000, 0.66823000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2245657'),
(51894, 'Wickham Market', 2336, 'ENG', 232, 'GB', 52.15298000, 1.36299000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q2001797'),
(51895, 'Wickwar', 2336, 'ENG', 232, 'GB', 51.59404000, -2.39968000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q1222802'),
(51896, 'Wideopen', 2336, 'ENG', 232, 'GB', 55.04514000, -1.62246000, '2019-10-05 22:53:18', '2019-10-05 22:53:18', 1, 'Q9372539'),
(51897, 'Widnes', 2336, 'ENG', 232, 'GB', 53.36180000, -2.73406000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1022741'),
(51898, 'Wigan', 2336, 'ENG', 232, 'GB', 53.54296000, -2.63706000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q208143'),
(51899, 'Wigmore', 2336, 'ENG', 232, 'GB', 52.31474000, -2.85802000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q608317'),
(51900, 'Wigston Magna', 2336, 'ENG', 232, 'GB', 52.58128000, -1.09248000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2223506'),
(51901, 'Wigton', 2336, 'ENG', 232, 'GB', 54.82482000, -3.16114000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2227457'),
(51902, 'Wilberfoss', 2336, 'ENG', 232, 'GB', 53.94854000, -0.88945000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2674633'),
(51903, 'Wilburton', 2336, 'ENG', 232, 'GB', 52.35191000, 0.17673000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2224144'),
(51904, 'Willand', 2336, 'ENG', 232, 'GB', 50.88333000, -3.36667000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1932472'),
(51905, 'Willaston', 2336, 'ENG', 232, 'GB', 53.29550000, -2.99732000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q8003516'),
(51906, 'Willenhall', 2336, 'ENG', 232, 'GB', 52.58514000, -2.05934000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q617799'),
(51907, 'Willingham', 2336, 'ENG', 232, 'GB', 52.31404000, 0.05776000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q401649'),
(51908, 'Willington', 2336, 'ENG', 232, 'GB', 54.71667000, -1.70000000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1613441'),
(51909, 'Williton', 2336, 'ENG', 232, 'GB', 51.16236000, -3.32208000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1613441'),
(51910, 'Wilmcote', 2336, 'ENG', 232, 'GB', 52.22081000, -1.76528000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q746002'),
(51911, 'Wilmslow', 2336, 'ENG', 232, 'GB', 53.32803000, -2.23148000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2011497'),
(51912, 'Wilsden', 2336, 'ENG', 232, 'GB', 53.82084000, -1.85959000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q8022880'),
(51913, 'Wilstead', 2336, 'ENG', 232, 'GB', 52.08088000, -0.44889000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1519179'),
(51914, 'Wilton', 2336, 'ENG', 232, 'GB', 51.07926000, -1.86210000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1018668'),
(51915, 'Wiltshire', 2336, 'ENG', 232, 'GB', 51.25000000, -1.91667000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q21694746'),
(51916, 'Wimblington', 2336, 'ENG', 232, 'GB', 52.50925000, 0.08416000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q546711'),
(51917, 'Wimborne Minster', 2336, 'ENG', 232, 'GB', 50.78333000, -1.98333000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q535233'),
(51918, 'Wincanton', 2336, 'ENG', 232, 'GB', 51.05676000, -2.40574000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1026640'),
(51919, 'Winchburgh', 2335, 'SCT', 232, 'GB', 55.95795000, -3.46464000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1026640'),
(51920, 'Winchcombe', 2336, 'ENG', 232, 'GB', 51.95363000, -1.96398000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, '********'),
(51921, 'Winchelsea Beach', 2336, 'ENG', 232, 'GB', 50.91691000, 0.72158000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, '********'),
(51922, 'Winchester', 2336, 'ENG', 232, 'GB', 51.06513000, -1.31870000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q172157'),
(51923, 'Windermere', 2336, 'ENG', 232, 'GB', 54.38086000, -2.90709000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q119936'),
(51924, 'Windlesham', 2336, 'ENG', 232, 'GB', 51.36509000, -0.65476000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, '********'),
(51925, 'Windsor', 2336, 'ENG', 232, 'GB', 51.48333000, -0.60000000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q464955'),
(51926, 'Windygates', 2335, 'SCT', 232, 'GB', 56.19546000, -3.05274000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, '********'),
(51927, 'Winford', 2336, 'ENG', 232, 'GB', 51.38333000, -2.66111000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q795358'),
(51928, 'Wing', 2336, 'ENG', 232, 'GB', 51.89524000, -0.71956000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, '********'),
(51929, 'Wingate', 2336, 'ENG', 232, 'GB', 54.73242000, -1.37896000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, '********'),
(51930, 'Wingerworth', 2336, 'ENG', 232, 'GB', 53.20200000, -1.43359000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, '********'),
(51931, 'Wingham', 2336, 'ENG', 232, 'GB', 51.27168000, 1.21463000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2632094'),
(51932, 'Wingrave', 2336, 'ENG', 232, 'GB', 51.86494000, -0.74244000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2278882'),
(51933, 'Winkleigh', 2336, 'ENG', 232, 'GB', 50.85581000, -3.94300000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q643534'),
(51934, 'Winscombe', 2336, 'ENG', 232, 'GB', 51.31810000, -2.83224000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q953300'),
(51935, 'Winsford', 2336, 'ENG', 232, 'GB', 53.19146000, -2.52398000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2011679'),
(51936, 'Winslow', 2336, 'ENG', 232, 'GB', 51.94284000, -0.88131000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1113295'),
(51937, 'Winterbourne', 2336, 'ENG', 232, 'GB', 51.44609000, -1.34660000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1113295'),
(51938, 'Winterton', 2336, 'ENG', 232, 'GB', 53.65497000, -0.59885000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q728129'),
(51939, 'Winwick', 2336, 'ENG', 232, 'GB', 53.43333000, -2.60000000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q821720'),
(51940, 'Wirksworth', 2336, 'ENG', 232, 'GB', 53.08232000, -1.57391000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1612220'),
(51941, 'Wisbech', 2336, 'ENG', 232, 'GB', 52.66622000, 0.15938000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1012031'),
(51942, 'Wishaw', 2335, 'SCT', 232, 'GB', 55.76667000, -3.91667000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2305226'),
(51943, 'Witchford', 2336, 'ENG', 232, 'GB', 52.38699000, 0.20602000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q3488171'),
(51944, 'Witham', 2336, 'ENG', 232, 'GB', 51.80007000, 0.64038000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1861278'),
(51945, 'Witheridge', 2336, 'ENG', 232, 'GB', 50.91743000, -3.70351000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q4593355'),
(51946, 'Withernsea', 2336, 'ENG', 232, 'GB', 53.73110000, 0.03347000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1025503'),
(51947, 'Witley', 2336, 'ENG', 232, 'GB', 51.14993000, -0.64768000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2644460'),
(51948, 'Witney', 2336, 'ENG', 232, 'GB', 51.78360000, -1.48540000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q990166'),
(51949, 'Wittering', 2336, 'ENG', 232, 'GB', 52.60698000, -0.44048000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1990514'),
(51950, 'Witton Gilbert', 2336, 'ENG', 232, 'GB', 54.80572000, -1.63686000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q778444'),
(51951, 'Wiveliscombe', 2336, 'ENG', 232, 'GB', 51.04139000, -3.31278000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1225663'),
(51952, 'Wivelsfield Green', 2336, 'ENG', 232, 'GB', 50.96313000, -0.07133000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1225663'),
(51953, 'Wivenhoe', 2336, 'ENG', 232, 'GB', 51.85553000, 0.95796000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1757774'),
(51954, 'Woburn', 2336, 'ENG', 232, 'GB', 51.98865000, -0.61903000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1881422'),
(51955, 'Woburn Sands', 2336, 'ENG', 232, 'GB', 52.01579000, -0.64982000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2334917'),
(51956, 'Woking', 2336, 'ENG', 232, 'GB', 51.31903000, -0.55893000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q646225'),
(51957, 'Wokingham', 2336, 'ENG', 232, 'GB', 51.41667000, -0.91667000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1758432'),
(51958, 'Wold Newton', 2336, 'ENG', 232, 'GB', 54.14280000, -0.39993000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1900151'),
(51959, 'Woldingham', 2336, 'ENG', 232, 'GB', 51.28527000, -0.03372000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2679683'),
(51960, 'Wollaston', 2336, 'ENG', 232, 'GB', 52.25794000, -0.67038000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2276908'),
(51961, 'Wolsingham', 2336, 'ENG', 232, 'GB', 54.73085000, -1.88319000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1613466'),
(51962, 'Wolston', 2336, 'ENG', 232, 'GB', 52.37717000, -1.39544000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2096723'),
(51963, 'Wolvercote', 2336, 'ENG', 232, 'GB', 51.78406000, -1.29338000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q7481520'),
(51964, 'Wolverhampton', 2336, 'ENG', 232, 'GB', 52.58333000, -2.11667000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q21885975'),
(51965, 'Wombourn', 2336, 'ENG', 232, 'GB', 52.53333000, -2.18333000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2768862'),
(51966, 'Wombwell', 2336, 'ENG', 232, 'GB', 53.52189000, -1.39698000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2096359'),
(51967, 'Wood Street Village', 2336, 'ENG', 232, 'GB', 51.25098000, -0.63695000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q8032282'),
(51968, 'Woodborough', 2336, 'ENG', 232, 'GB', 51.33852000, -1.83976000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q583272'),
(51969, 'Woodbridge', 2336, 'ENG', 232, 'GB', 52.09332000, 1.32042000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1021350'),
(51970, 'Woodbury', 2336, 'ENG', 232, 'GB', 50.67664000, -3.40160000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2380248'),
(51971, 'Woodchurch', 2336, 'ENG', 232, 'GB', 51.07605000, 0.77346000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1874946'),
(51972, 'Woodcote', 2336, 'ENG', 232, 'GB', 52.73333000, -2.33333000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1874946'),
(51973, 'Woodford', 2336, 'ENG', 232, 'GB', 52.38231000, -0.58099000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q45603'),
(51974, 'Woodford Green', 2336, 'ENG', 232, 'GB', 51.60938000, 0.02329000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q45603'),
(51975, 'Woodhall Spa', 2336, 'ENG', 232, 'GB', 53.15215000, -0.21453000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q983619'),
(51976, 'Woodsetts', 2336, 'ENG', 232, 'GB', 53.34804000, -1.17204000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1617091'),
(51977, 'Woodstock', 2336, 'ENG', 232, 'GB', 51.84850000, -1.35132000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q752111'),
(51978, 'Wool', 2336, 'ENG', 232, 'GB', 50.67966000, -2.21890000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1218503'),
(51979, 'Woolavington', 2336, 'ENG', 232, 'GB', 51.16493000, -2.93814000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2081120'),
(51980, 'Wooler', 2336, 'ENG', 232, 'GB', 55.54755000, -2.01186000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2663783'),
(51981, 'Woolley', 2336, 'ENG', 232, 'GB', 53.61339000, -1.51457000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q4355527'),
(51982, 'Woolpit', 2336, 'ENG', 232, 'GB', 52.22454000, 0.88826000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2739818'),
(51983, 'Wootton', 2336, 'ENG', 232, 'GB', 51.17380000, 1.17940000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q8034080'),
(51984, 'Worcester', 2336, 'ENG', 232, 'GB', 52.18935000, -2.22001000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1646181'),
(51985, 'Worcester Park', 2336, 'ENG', 232, 'GB', 51.37992000, -0.24445000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1646181'),
(51986, 'Worcestershire', 2336, 'ENG', 232, 'GB', 52.16667000, -2.16667000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q23135'),
(51987, 'Workington', 2336, 'ENG', 232, 'GB', 54.64250000, -3.54413000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q1538358'),
(51988, 'Worksop', 2336, 'ENG', 232, 'GB', 53.30182000, -1.12404000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q2281091'),
(51989, 'Worlaby', 2336, 'ENG', 232, 'GB', 53.61130000, -0.46685000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q8035183'),
(51990, 'Wormley', 2336, 'ENG', 232, 'GB', 51.13622000, -0.64673000, '2019-10-05 22:53:19', '2019-10-05 22:53:19', 1, 'Q8036878'),
(51991, 'Worthing', 2336, 'ENG', 232, 'GB', 50.81795000, -0.37538000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q671348'),
(51992, 'Worton', 2336, 'ENG', 232, 'GB', 51.31611000, -2.04111000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2744759'),
(51993, 'Wotton-under-Edge', 2336, 'ENG', 232, 'GB', 51.63242000, -2.34512000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q29471'),
(51994, 'Wouldham', 2336, 'ENG', 232, 'GB', 51.34986000, 0.45816000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1855812'),
(51995, 'Wragby', 2336, 'ENG', 232, 'GB', 53.28333000, -0.30000000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q610691'),
(51996, 'Wrawby', 2336, 'ENG', 232, 'GB', 53.56672000, -0.46194000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q8037638'),
(51997, 'Wrea Green', 2336, 'ENG', 232, 'GB', 53.77651000, -2.91573000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3570101'),
(51998, 'Wrexham', 2338, 'WLS', 232, 'GB', 53.00000000, -3.00000000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q843868'),
(51999, 'Wrington', 2336, 'ENG', 232, 'GB', 51.36173000, -2.76319000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1825419'),
(52000, 'Writtle', 2336, 'ENG', 232, 'GB', 51.72906000, 0.42938000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1013057'),
(52001, 'Wrotham', 2336, 'ENG', 232, 'GB', 51.30856000, 0.30899000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2740296'),
(52002, 'Wroughton', 2336, 'ENG', 232, 'GB', 51.52411000, -1.79559000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1863505'),
(52003, 'Wroxall', 2336, 'ENG', 232, 'GB', 52.33791000, -1.66898000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q9378174'),
(52004, 'Wychbold', 2336, 'ENG', 232, 'GB', 52.29045000, -2.11555000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q8039557'),
(52005, 'Wye', 2336, 'ENG', 232, 'GB', 51.18249000, 0.93678000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q590063'),
(52006, 'Wylam', 2336, 'ENG', 232, 'GB', 54.97654000, -1.82187000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1025063'),
(52007, 'Wymeswold', 2336, 'ENG', 232, 'GB', 52.80536000, -1.11288000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q8039980'),
(52008, 'Y Felinheli', 2338, 'WLS', 232, 'GB', 53.18737000, -4.20476000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3398922'),
(52009, 'Yalding', 2336, 'ENG', 232, 'GB', 51.22387000, 0.42920000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2051722'),
(52010, 'Yapton', 2336, 'ENG', 232, 'GB', 50.82090000, -0.61300000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2202952'),
(52011, 'Yarm', 2336, 'ENG', 232, 'GB', 54.50364000, -1.35793000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2462654'),
(52012, 'Yarmouth', 2336, 'ENG', 232, 'GB', 50.70529000, -1.49929000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q921984'),
(52013, 'Yarnton', 2336, 'ENG', 232, 'GB', 51.80448000, -1.31149000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2616249'),
(52014, 'Yate', 2336, 'ENG', 232, 'GB', 51.54074000, -2.41839000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1025095'),
(52015, 'Yateley', 2336, 'ENG', 232, 'GB', 51.34305000, -0.82985000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q507125'),
(52016, 'Yatton', 2336, 'ENG', 232, 'GB', 51.38839000, -2.82353000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2499687'),
(52017, 'Yaxley', 2336, 'ENG', 232, 'GB', 52.51768000, -0.25852000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q643760'),
(52018, 'Yeadon', 2336, 'ENG', 232, 'GB', 53.86437000, -1.68743000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2281068'),
(52019, 'Yealmpton', 2336, 'ENG', 232, 'GB', 50.34856000, -3.99877000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4354400'),
(52020, 'Yelverton', 2336, 'ENG', 232, 'GB', 50.49290000, -4.08382000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q540088'),
(52021, 'Yeovil', 2336, 'ENG', 232, 'GB', 50.94159000, -2.63211000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q550110'),
(52022, 'Yetminster', 2336, 'ENG', 232, 'GB', 50.89579000, -2.57959000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1213862'),
(52023, 'Ynysybwl', 2338, 'WLS', 232, 'GB', 51.63922000, -3.36036000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3399762'),
(52024, 'York', 2336, 'ENG', 232, 'GB', 53.95763000, -1.08271000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q42462'),
(52025, 'Youlgreave', 2336, 'ENG', 232, 'GB', 53.17399000, -1.69044000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1822924'),
(52026, 'Yoxall', 2336, 'ENG', 232, 'GB', 52.76659000, -1.79068000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2989704'),
(52027, 'Ystalyfera', 2338, 'WLS', 232, 'GB', 51.76716000, -3.78082000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q8059797'),
(52028, 'Gouyave', 3868, '04', 87, 'GD', 12.16462000, -61.72965000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q375507'),
(52029, 'Grenville', 3865, '01', 87, 'GD', 12.12278000, -61.62498000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3116519'),
(52030, 'Hillsborough', 3867, '10', 87, 'GD', 12.48292000, -61.45597000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3267362'),
(52031, 'Saint David’s', 3869, '02', 87, 'GD', 12.04903000, -61.66875000, '2019-10-05 22:53:20', '2020-05-01 17:22:50', 1, 'Q3267362'),
(52032, 'Saint George\'s', 3864, '03', 87, 'GD', 12.05288000, -61.75226000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q41547'),
(52033, 'Sauteurs', 3863, '06', 87, 'GD', 12.21833000, -61.63917000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1318103'),
(52034, 'Victoria', 3866, '05', 87, 'GD', 12.19021000, -61.70677000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q12874663'),
(52035, 'Abasha', 908, 'SZ', 81, 'GE', 42.20000000, 42.20000000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2513666'),
(52036, 'Adigeni', 906, 'SJ', 81, 'GE', 41.68191000, 42.69867000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2513666'),
(52037, 'Adigeni Municipality', 906, 'SJ', 81, 'GE', 41.71667000, 42.73333000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2382197'),
(52038, 'Agara', 903, 'SK', 81, 'GE', 42.03761000, 43.82382000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2382197'),
(52039, 'Akhaldaba', 906, 'SJ', 81, 'GE', 41.92945000, 43.48762000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q31845177'),
(52040, 'Akhaldaba', 900, 'AJ', 81, 'GE', 41.65395000, 42.15163000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q31845177'),
(52041, 'Akhalgori', 902, 'MM', 81, 'GE', 42.12597000, 44.48333000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1010939'),
(52042, 'Akhalk’alak’i', 906, 'SJ', 81, 'GE', 41.40514000, 43.48629000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q500888'),
(52043, 'Akhaltsikhe', 906, 'SJ', 81, 'GE', 41.63901000, 42.98262000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q212546'),
(52044, 'Akhaltsikhis Munitsip’alit’et’i', 906, 'SJ', 81, 'GE', 41.63333000, 43.00000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2513568'),
(52045, 'Akhmet’a', 910, 'KA', 81, 'GE', 42.03111000, 45.20750000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2513568'),
(52046, 'Akhmet’is Munitsip’alit’et’i', 910, 'KA', 81, 'GE', 42.25000000, 45.33333000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q748753'),
(52047, 'Ambrolauri', 909, 'RL', 81, 'GE', 42.52111000, 43.16222000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q456842'),
(52048, 'Ambrolauris Munitsip’alit’et’i', 909, 'RL', 81, 'GE', 42.56667000, 43.10000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2464159'),
(52049, 'Aspindza', 906, 'SJ', 81, 'GE', 41.57389000, 43.24826000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2344454'),
(52050, 'Asp’indzis Munitsip’alit’et’i', 906, 'SJ', 81, 'GE', 41.50000000, 43.25000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2305733'),
(52051, 'Baghdatis Munitsip’alit’et’i', 905, 'IM', 81, 'GE', 42.00000000, 42.90000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2018619'),
(52052, 'Bakuriani', 906, 'SJ', 81, 'GE', 41.74972000, 43.53250000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q789104'),
(52053, 'Batumi', 900, 'AJ', 81, 'GE', 41.64228000, 41.63392000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q25475'),
(52054, 'Bich’vinta', 901, 'AB', 81, 'GE', 43.16197000, 40.34102000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q211318'),
(52055, 'Bolnisi', 904, 'KK', 81, 'GE', 41.44794000, 44.53838000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q653420'),
(52056, 'Bolnisis Munitsip’alit’et’i', 904, 'KK', 81, 'GE', 41.36667000, 44.51667000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2513580'),
(52057, 'Borjomi', 906, 'SJ', 81, 'GE', 41.85272000, 43.41284000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q208352'),
(52058, 'Chakvi', 900, 'AJ', 81, 'GE', 41.72528000, 41.73278000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3646262'),
(52059, 'Chiat’ura', 905, 'IM', 81, 'GE', 42.29806000, 43.29889000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q217443'),
(52060, 'Didi Lilo', 904, 'KK', 81, 'GE', 41.73611000, 44.96472000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4161357'),
(52061, 'Dioknisi', 900, 'AJ', 81, 'GE', 41.62933000, 42.39171000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4161357'),
(52062, 'Dmanisis Munitsip’alit’et’i', 904, 'KK', 81, 'GE', 41.35000000, 44.13333000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q681371'),
(52063, 'Dranda', 901, 'AB', 81, 'GE', 42.87167000, 41.15333000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3951176'),
(52064, 'Dzegvi', 902, 'MM', 81, 'GE', 41.84569000, 44.60097000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3951176'),
(52065, 'Gagra', 901, 'AB', 81, 'GE', 43.27858000, 40.27124000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q44811'),
(52066, 'Gali', 901, 'AB', 81, 'GE', 42.62655000, 41.73808000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q236673'),
(52067, 'Gantiadi', 901, 'AB', 81, 'GE', 43.38111000, 40.07944000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2508003'),
(52068, 'Gardabani', 904, 'KK', 81, 'GE', 41.46054000, 45.09283000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1010960'),
(52069, 'Gardabnis Munitsip’alit’et’i', 904, 'KK', 81, 'GE', 41.61667000, 45.00000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2512996'),
(52070, 'Gori', 903, 'SK', 81, 'GE', 41.98422000, 44.11578000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q19583'),
(52071, 'Goris Munitsip’alit’et’i', 903, 'SK', 81, 'GE', 42.06667000, 44.11667000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q1417170'),
(52072, 'Gudauri', 902, 'MM', 81, 'GE', 42.47797000, 44.47616000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1553013'),
(52073, 'Gudauta', 901, 'AB', 81, 'GE', 43.10547000, 40.62067000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1553013'),
(52074, 'Gurjaani', 910, 'KA', 81, 'GE', 41.74292000, 45.80111000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1011047'),
(52075, 'Java', 902, 'MM', 81, 'GE', 42.39972000, 43.93667000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1025834'),
(52076, 'Jvari', 908, 'SZ', 81, 'GE', 42.71693000, 42.05200000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q853261'),
(52077, 'Kaspi', 903, 'SK', 81, 'GE', 41.92520000, 44.42568000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q956954'),
(52078, 'Kelasuri', 901, 'AB', 81, 'GE', 42.97877000, 41.07067000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q956954'),
(52079, 'Kharagauli', 905, 'IM', 81, 'GE', 42.02137000, 43.19773000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q14645211'),
(52080, 'Khashuri', 903, 'SK', 81, 'GE', 41.99414000, 43.59994000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q932901'),
(52081, 'Khelvachauri', 900, 'AJ', 81, 'GE', 41.58556000, 41.66889000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1025120'),
(52082, 'Khobi', 908, 'SZ', 81, 'GE', 42.31558000, 41.89871000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1989822'),
(52083, 'Khoni', 905, 'IM', 81, 'GE', 42.32260000, 42.42061000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1011011'),
(52084, 'Khulo', 900, 'AJ', 81, 'GE', 41.64353000, 42.30397000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1089456'),
(52085, 'Kobuleti', 900, 'AJ', 81, 'GE', 41.82143000, 41.77921000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q328975'),
(52086, 'Kutaisi', 905, 'IM', 81, 'GE', 42.26791000, 42.69459000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q328975'),
(52087, 'Kveda Chkhorots’q’u', 908, 'SZ', 81, 'GE', 42.48103000, 42.09661000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q328975'),
(52088, 'K’alak’i Chiat’ura', 905, 'IM', 81, 'GE', 42.28333000, 43.25000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q269506'),
(52089, 'K’ulashi', 905, 'IM', 81, 'GE', 42.20405000, 42.34289000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2245433'),
(52090, 'Lagodekhi', 910, 'KA', 81, 'GE', 41.82681000, 46.27667000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q956966'),
(52091, 'Lanchkhuti', 907, 'GU', 81, 'GE', 42.09027000, 42.03239000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q956922'),
(52092, 'Lent’ekhi', 909, 'RL', 81, 'GE', 42.78893000, 42.72226000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q956922'),
(52093, 'Makhinjauri', 900, 'AJ', 81, 'GE', 41.67385000, 41.69401000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3646259'),
(52094, 'Manglisi', 904, 'KK', 81, 'GE', 41.69698000, 44.38448000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2029774'),
(52095, 'Marneuli', 904, 'KK', 81, 'GE', 41.47588000, 44.80895000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2029774'),
(52096, 'Marneulis Munitsip’alit’et’i', 904, 'KK', 81, 'GE', 41.38333000, 44.85000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2410306'),
(52097, 'Mart’vili', 908, 'SZ', 81, 'GE', 42.41436000, 42.37924000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q179290'),
(52098, 'Mest’ia', 908, 'SZ', 81, 'GE', 43.04581000, 42.72780000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q179290'),
(52099, 'Mest’iis Munitsip’alit’et’i', 908, 'SZ', 81, 'GE', 43.05000000, 42.55000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2490962'),
(52100, 'Mtskheta', 902, 'MM', 81, 'GE', 41.84514000, 44.71875000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q180810'),
(52101, 'Naghvarevi', 904, 'KK', 81, 'GE', 41.35272000, 44.76178000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3653114'),
(52102, 'Naruja', 907, 'GU', 81, 'GE', 41.90694000, 41.95417000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1898132'),
(52103, 'Ninotsminda', 906, 'SJ', 81, 'GE', 41.26458000, 43.59161000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q10600995'),
(52104, 'Ochkhamuri', 900, 'AJ', 81, 'GE', 41.85975000, 41.85309000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q10600995'),
(52105, 'Och’amch’ire', 901, 'AB', 81, 'GE', 42.71232000, 41.46863000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q211600'),
(52106, 'Oni', 909, 'RL', 81, 'GE', 42.57944000, 43.44250000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q941948'),
(52107, 'Orsant’ia', 908, 'SZ', 81, 'GE', 42.46777000, 41.67377000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q941948'),
(52108, 'Ozurgeti', 907, 'GU', 81, 'GE', 41.92442000, 42.00682000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q317145'),
(52109, 'P’asanauri', 902, 'MM', 81, 'GE', 42.35060000, 44.68912000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q317145'),
(52110, 'P’ot’i', 908, 'SZ', 81, 'GE', 42.14616000, 41.67197000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q185345'),
(52111, 'P’rimorsk’oe', 901, 'AB', 81, 'GE', 43.09236000, 40.69650000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q185345'),
(52112, 'Qvareli', 910, 'KA', 81, 'GE', 41.95493000, 45.81716000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1011016'),
(52113, 'Rust’avi', 904, 'KK', 81, 'GE', 41.54949000, 44.99323000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q182521'),
(52114, 'Sach’khere', 905, 'IM', 81, 'GE', 42.34528000, 43.41944000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q474669'),
(52115, 'Sagarejo', 910, 'KA', 81, 'GE', 41.73397000, 45.33149000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q956932'),
(52116, 'Samtredia', 905, 'IM', 81, 'GE', 42.15370000, 42.33517000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q956932'),
(52117, 'Senak’i', 908, 'SZ', 81, 'GE', 42.27042000, 42.06750000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q320519'),
(52118, 'Shorapani', 905, 'IM', 81, 'GE', 42.08980000, 43.08662000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1944249'),
(52119, 'Shuakhevi', 900, 'AJ', 81, 'GE', 41.63000000, 42.19083000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q161941'),
(52120, 'Sighnaghi', 910, 'KA', 81, 'GE', 41.62046000, 45.92198000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q946143'),
(52121, 'Sighnaghis Munitsip’alit’et’i', 910, 'KA', 81, 'GE', 41.56667000, 45.85000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q1953363'),
(52122, 'Sokhumi', 901, 'AB', 81, 'GE', 43.00697000, 40.98930000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q40811'),
(52123, 'Stantsiya Novyy Afon', 901, 'AB', 81, 'GE', 43.08056000, 40.83833000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q42194'),
(52124, 'Step’antsminda', 902, 'MM', 81, 'GE', 42.65667000, 44.64333000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q1025823'),
(52125, 'Surami', 903, 'SK', 81, 'GE', 42.02431000, 43.55556000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2474275'),
(52126, 'Tbilisi', 899, 'TB', 81, 'GE', 41.69411000, 44.83368000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2474275'),
(52127, 'Telavi', 910, 'KA', 81, 'GE', 41.91978000, 45.47315000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2474275'),
(52128, 'Tetrits’q’alos Munitsip’alit’et’i', 904, 'KK', 81, 'GE', 41.60000000, 44.50000000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q1954407'),
(52129, 'Tqibuli', 905, 'IM', 81, 'GE', 42.35121000, 42.99874000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1954407'),
(52130, 'Tqvarch\'eli', 901, 'AB', 81, 'GE', 42.84035000, 41.68007000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q270535'),
(52131, 'Tsaghveri', 906, 'SJ', 81, 'GE', 41.80365000, 43.48194000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q270535'),
(52132, 'Tsalenjikha', 908, 'SZ', 81, 'GE', 42.60444000, 42.06825000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1017210'),
(52133, 'Tsalka', 904, 'KK', 81, 'GE', 41.59460000, 44.08909000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q145127'),
(52134, 'Tsikhisdziri', 900, 'AJ', 81, 'GE', 41.76659000, 41.75517000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q145127'),
(52135, 'Tsinandali', 910, 'KA', 81, 'GE', 41.89315000, 45.57129000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q118953'),
(52136, 'Tsnori', 910, 'KA', 81, 'GE', 41.62088000, 45.96943000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q217990'),
(52137, 'Tsqaltubo', 905, 'IM', 81, 'GE', 42.34129000, 42.59760000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q217990'),
(52138, 'Ts’alk’is Munitsip’alit’et’i', 904, 'KK', 81, 'GE', 41.63333000, 43.96667000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q2238147'),
(52139, 'Ts’khinvali', 903, 'SK', 81, 'GE', 42.22764000, 43.96861000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q79863'),
(52140, 'T’et’ri Tsqaro', 904, 'KK', 81, 'GE', 41.54448000, 44.46153000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q1010985'),
(52141, 'Urek’i', 907, 'GU', 81, 'GE', 41.99556000, 41.77861000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q3651285'),
(52142, 'Vale', 906, 'SJ', 81, 'GE', 41.61558000, 42.87224000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q783749'),
(52143, 'Vani', 905, 'IM', 81, 'GE', 42.08320000, 42.52163000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1011003'),
(52144, 'Zestap’oni', 905, 'IM', 81, 'GE', 42.11000000, 43.05250000, '2019-10-05 22:53:20', '2020-05-01 17:22:47', 1, 'Q734315'),
(52145, 'Zhinvali', 902, 'MM', 81, 'GE', 42.13181000, 44.77264000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2073374'),
(52146, 'Zugdidi', 908, 'SZ', 81, 'GE', 42.50880000, 41.87088000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q185336'),
(52147, 'Aboso', 49, 'WP', 83, 'GH', 5.36073000, -1.94856000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q185336'),
(52148, 'Aburi', 50, 'EP', 83, 'GH', 5.84802000, -0.17449000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q336029'),
(52149, 'Accra', 54, 'AA', 83, 'GH', 5.55602000, -0.19690000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q3761'),
(52150, 'Aflao', 56, 'TV', 83, 'GH', 6.11982000, 1.19012000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q383333'),
(52151, 'Agogo', 48, 'AH', 83, 'GH', 6.80004000, -1.08193000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q395201'),
(52152, 'Akim Oda', 50, 'EP', 83, 'GH', 5.92665000, -0.98577000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q418402'),
(52153, 'Akim Swedru', 50, 'EP', 83, 'GH', 5.89380000, -1.01636000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4701024'),
(52154, 'Akropong', 50, 'EP', 83, 'GH', 5.97462000, -0.08542000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4701024'),
(52155, 'Akwatia', 50, 'EP', 83, 'GH', 6.04024000, -0.80876000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4702013'),
(52156, 'Anloga', 56, 'TV', 83, 'GH', 5.79473000, 0.89728000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q557912'),
(52157, 'Apam', 52, 'CP', 83, 'GH', 5.28483000, -0.73711000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q617438'),
(52158, 'Asamankese', 50, 'EP', 83, 'GH', 5.86006000, -0.66350000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q720732'),
(52159, 'Atsiaman', 54, 'AA', 83, 'GH', 5.69775000, -0.32824000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q720732'),
(52160, 'Axim', 49, 'WP', 83, 'GH', 4.86641000, -2.24181000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q792509'),
(52161, 'Bawku', 55, 'UE', 83, 'GH', 11.06160000, -0.24169000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q812036'),
(52163, 'Begoro', 50, 'EP', 83, 'GH', 6.38706000, -0.37738000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q813582'),
(52164, 'Bekwai', 48, 'AH', 83, 'GH', 6.45195000, -1.57866000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q815055'),
(52166, 'Bibiani', 49, 'WP', 83, 'GH', 6.46346000, -2.31938000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4903040'),
(52167, 'Bolgatanga', 55, 'UE', 83, 'GH', 10.78556000, -0.85139000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q166513'),
(52168, 'Cape Coast', 52, 'CP', 83, 'GH', 5.10535000, -1.24660000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q221283'),
(52169, 'Dome', 54, 'AA', 83, 'GH', 5.65003000, -0.23610000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1236987'),
(52171, 'Dunkwa', 52, 'CP', 83, 'GH', 5.95996000, -1.77792000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q5315398'),
(52172, 'Ejura', 48, 'AH', 83, 'GH', 7.38558000, -1.35617000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1322026'),
(52173, 'Elmina', 52, 'CP', 83, 'GH', 5.08470000, -1.35093000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q968662'),
(52174, 'Foso', 52, 'CP', 83, 'GH', 5.70119000, -1.28657000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q4808889'),
(52175, 'Gbawe', 54, 'AA', 83, 'GH', 5.57692000, -0.31038000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1496746'),
(52176, 'Ho', 56, 'TV', 83, 'GH', 6.60084000, 0.47130000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q615006'),
(52177, 'Hohoe', 56, 'TV', 83, 'GH', 7.15181000, 0.47362000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1017008'),
(52179, 'Kasoa', 52, 'CP', 83, 'GH', 5.53449000, -0.41679000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q656652'),
(52180, 'Keta', 56, 'TV', 83, 'GH', 5.91793000, 0.98789000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1471408'),
(52181, 'Kete Krachi', 56, 'TV', 83, 'GH', 7.79391000, -0.04980000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1739861'),
(52182, 'Kibi', 50, 'EP', 83, 'GH', 6.16494000, -0.55376000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q6404011'),
(52184, 'Koforidua', 50, 'EP', 83, 'GH', 6.09408000, -0.25913000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q406358'),
(52185, 'Konongo', 48, 'AH', 83, 'GH', 6.61667000, -1.21667000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1016732'),
(52186, 'Kpandae', 51, 'NP', 83, 'GH', 8.46885000, -0.01127000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1016732'),
(52187, 'Kpandu', 56, 'TV', 83, 'GH', 6.99536000, 0.29306000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1785891'),
(52188, 'Kumasi', 48, 'AH', 83, 'GH', 6.68848000, -1.62443000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q182059'),
(52189, 'Mampong', 48, 'AH', 83, 'GH', 7.06273000, -1.40010000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q992747'),
(52190, 'Medina Estates', 54, 'AA', 83, 'GH', 5.66580000, -0.16307000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1316141'),
(52191, 'Mpraeso', 50, 'EP', 83, 'GH', 6.59321000, -0.73462000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1316141'),
(52192, 'Mumford', 52, 'CP', 83, 'GH', 5.26176000, -0.75897000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1316141'),
(52193, 'Navrongo', 55, 'UE', 83, 'GH', 10.89557000, -1.09210000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q995548'),
(52194, 'Nsawam', 50, 'EP', 83, 'GH', 5.80893000, -0.35026000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2724141'),
(52195, 'Nungua', 54, 'AA', 83, 'GH', 5.60105000, -0.07713000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q493990'),
(52196, 'Obuase', 48, 'AH', 83, 'GH', 6.20228000, -1.66796000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1020901'),
(52197, 'Prestea', 49, 'WP', 83, 'GH', 5.43385000, -2.14295000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2109005'),
(52198, 'Salaga', 51, 'NP', 83, 'GH', 8.55083000, -0.51875000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q995578'),
(52199, 'Saltpond', 52, 'CP', 83, 'GH', 5.20913000, -1.06058000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q340149'),
(52200, 'Savelugu', 51, 'NP', 83, 'GH', 9.62441000, -0.82530000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q2228059'),
(52201, 'Sekondi-Takoradi', 49, 'WP', 83, 'GH', 4.92678000, -1.75773000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q243293'),
(52202, 'Shama Junction', 49, 'WP', 83, 'GH', 5.01806000, -1.66437000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q243293'),
(52203, 'Suhum', 50, 'EP', 83, 'GH', 6.04089000, -0.45004000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q1450481'),
(52205, 'Swedru', 52, 'CP', 83, 'GH', 5.53711000, -0.69984000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q395265'),
(52206, 'Tafo', 48, 'AH', 83, 'GH', 6.73156000, -1.61370000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q7674984'),
(52207, 'Takoradi', 49, 'WP', 83, 'GH', 4.89816000, -1.76029000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q7674984'),
(52208, 'Tamale', 51, 'NP', 83, 'GH', 9.40079000, -0.83930000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q7674984'),
(52209, 'Tarkwa', 49, 'WP', 83, 'GH', 5.30383000, -1.98956000, '2019-10-05 22:53:20', '2019-10-05 22:53:20', 1, 'Q995439'),
(52211, 'Tema', 54, 'AA', 83, 'GH', 5.66980000, -0.01657000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q726143'),
(52212, 'Teshi Old Town', 54, 'AA', 83, 'GH', 5.58365000, -0.10722000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1062602'),
(52213, 'Wa', 57, 'UW', 83, 'GH', 10.06069000, -2.50192000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q250733'),
(52215, 'Winneba', 52, 'CP', 83, 'GH', 5.35113000, -0.62313000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1188560'),
(52216, 'Yendi', 51, 'NP', 83, 'GH', 9.44272000, -0.00991000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q639066'),
(52217, 'Abuko', 2667, 'W', 80, 'GM', 13.40417000, -16.65583000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q335811'),
(52218, 'Bakadagy', 2668, 'U', 80, 'GM', 13.30000000, -14.38333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q804224'),
(52219, 'Bakau', 2666, 'B', 80, 'GM', 13.47806000, -16.68194000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q804275'),
(52220, 'Bambali', 2671, 'N', 80, 'GM', 13.48333000, -15.33333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q805926'),
(52221, 'Banjul', 2666, 'B', 80, 'GM', 13.45274000, -16.57803000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q3726'),
(52222, 'Bansang', 2669, 'M', 80, 'GM', 13.43333000, -14.65000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q806951'),
(52223, 'Baro Kunda', 2670, 'L', 80, 'GM', 13.48333000, -15.26667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q808540'),
(52224, 'Barra', 2671, 'N', 80, 'GM', 13.48278000, -16.54556000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q450062'),
(52225, 'Basse Santa Su', 2668, 'U', 80, 'GM', 13.30995000, -14.21373000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q153498'),
(52226, 'Brifu', 2668, 'U', 80, 'GM', 13.50000000, -13.93333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q915266'),
(52227, 'Brikama', 2667, 'W', 80, 'GM', 13.27136000, -16.64944000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q916988'),
(52228, 'Brikama Nding', 2669, 'M', 80, 'GM', 13.53333000, -14.93333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q916988'),
(52229, 'Bureng', 2670, 'L', 80, 'GM', 13.41667000, -15.28333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q916988'),
(52230, 'Central Baddibu', 2671, 'N', 80, 'GM', 13.53333000, -15.91667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1053702'),
(52231, 'Chilla', 2671, 'N', 80, 'GM', 13.55000000, -16.28333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1053702'),
(52232, 'Daba Kunda', 2668, 'U', 80, 'GM', 13.31667000, -14.30000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1053702'),
(52233, 'Dankunku', 2669, 'M', 80, 'GM', 13.56667000, -15.31667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1164277'),
(52234, 'Daru Rilwan', 2671, 'N', 80, 'GM', 13.55000000, -15.98333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1164277'),
(52235, 'Demba Kunda', 2668, 'U', 80, 'GM', 13.25000000, -14.26667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1185218'),
(52236, 'Denton', 2669, 'M', 80, 'GM', 13.50000000, -14.93333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1185218'),
(52237, 'Diabugu', 2668, 'U', 80, 'GM', 13.38333000, -14.40000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q269070'),
(52238, 'Diabugu Basilla', 2668, 'U', 80, 'GM', 13.33333000, -13.95000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q269070'),
(52239, 'Essau', 2671, 'N', 80, 'GM', 13.48389000, -16.53472000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1368528'),
(52240, 'Farafenni', 2671, 'N', 80, 'GM', 13.56667000, -15.60000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1200999'),
(52241, 'Foni Bondali', 2667, 'W', 80, 'GM', 13.21667000, -15.93333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1435751'),
(52242, 'Foni Brefet', 2667, 'W', 80, 'GM', 13.21667000, -16.33333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1435749'),
(52243, 'Foni Jarrol', 2667, 'W', 80, 'GM', 13.23333000, -15.83333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q124275'),
(52244, 'Foni Kansala', 2667, 'W', 80, 'GM', 13.21667000, -16.05000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1435754'),
(52245, 'Fulladu East', 2668, 'U', 80, 'GM', 13.33333000, -14.25000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1473721'),
(52246, 'Fulladu West', 2669, 'M', 80, 'GM', 13.50000000, -14.75000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1473729'),
(52247, 'Galleh Manda', 2669, 'M', 80, 'GM', 13.43333000, -14.78333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1492266'),
(52248, 'Georgetown', 2669, 'M', 80, 'GM', 13.54039000, -14.76374000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1002786'),
(52249, 'Gunjur', 2667, 'W', 80, 'GM', 13.20194000, -16.73389000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q687816'),
(52250, 'Gunjur', 2671, 'N', 80, 'GM', 13.52278000, -16.02778000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1554693'),
(52251, 'Gunjur Kuta', 2668, 'U', 80, 'GM', 13.53333000, -14.11667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1554695'),
(52252, 'Jakhaly', 2669, 'M', 80, 'GM', 13.55000000, -14.96667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1678844'),
(52253, 'Jali', 2670, 'L', 80, 'GM', 13.35000000, -15.96667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1669376'),
(52254, 'Janjanbureh', 2669, 'M', 80, 'GM', 13.53564000, -14.76515000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1669376'),
(52255, 'Jarra Central', 2670, 'L', 80, 'GM', 13.41667000, -15.41667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572024'),
(52256, 'Jarra East', 2670, 'L', 80, 'GM', 13.41667000, -15.25000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q617233'),
(52257, 'Jarra West', 2670, 'L', 80, 'GM', 13.41667000, -15.53333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572209'),
(52258, 'Jarreng', 2669, 'M', 80, 'GM', 13.61667000, -15.18333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572209'),
(52259, 'Jenoi', 2670, 'L', 80, 'GM', 13.48333000, -15.56667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q110618'),
(52260, 'Jifarong', 2670, 'L', 80, 'GM', 13.30000000, -15.86667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1688943'),
(52261, 'Jokadu', 2671, 'N', 80, 'GM', 13.53333000, -16.18333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572430'),
(52262, 'Kaiaf', 2670, 'L', 80, 'GM', 13.40000000, -15.61667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1721371'),
(52263, 'Kantora', 2668, 'U', 80, 'GM', 13.41667000, -13.91667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q611397'),
(52264, 'Karantaba', 2670, 'L', 80, 'GM', 13.43333000, -15.51667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1728955'),
(52265, 'Karantaba', 2669, 'M', 80, 'GM', 13.66667000, -15.03333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1728955'),
(52266, 'Kass Wollof', 2669, 'M', 80, 'GM', 13.78333000, -14.93333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1728955'),
(52267, 'Katchang', 2671, 'N', 80, 'GM', 13.50000000, -15.75000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1735666'),
(52268, 'Keneba', 2670, 'L', 80, 'GM', 13.32889000, -16.01500000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1738721'),
(52269, 'Kerewan', 2671, 'N', 80, 'GM', 13.48980000, -16.08879000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1478583'),
(52270, 'Kiang Central', 2670, 'L', 80, 'GM', 13.40000000, -15.75000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572242'),
(52271, 'Kiang East', 2670, 'L', 80, 'GM', 13.41667000, -15.63333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572748'),
(52272, 'Kiang West District', 2670, 'L', 80, 'GM', 13.33333000, -16.00000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572726'),
(52273, 'Koina', 2668, 'U', 80, 'GM', 13.48333000, -13.86667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1778826'),
(52274, 'Kombo Central District', 2667, 'W', 80, 'GM', 13.25000000, -16.66667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572741'),
(52275, 'Kombo East District', 2667, 'W', 80, 'GM', 13.21667000, -16.51667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1548433'),
(52276, 'Kombo North District', 2667, 'W', 80, 'GM', 13.36667000, -16.66667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572418'),
(52277, 'Kombo Saint Mary District', 2666, 'B', 80, 'GM', 13.44389000, -16.64583000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572418'),
(52278, 'Kombo South District', 2667, 'W', 80, 'GM', 13.25000000, -16.75000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572017'),
(52279, 'Kumbija', 2668, 'U', 80, 'GM', 13.26667000, -14.18333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572017'),
(52280, 'Kuntaur', 2669, 'M', 80, 'GM', 13.67085000, -14.88977000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1188137'),
(52281, 'Kunting', 2669, 'M', 80, 'GM', 13.53333000, -14.66667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1319580'),
(52282, 'Lamin', 2671, 'N', 80, 'GM', 13.35222000, -16.43389000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1319580'),
(52283, 'Lower Baddibu District', 2671, 'N', 80, 'GM', 13.50000000, -16.05000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572733'),
(52284, 'Lower Niumi District', 2671, 'N', 80, 'GM', 13.53333000, -16.41667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q915733'),
(52285, 'Lower Saloum', 2669, 'M', 80, 'GM', 13.71667000, -15.36667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572755'),
(52286, 'Mansa Konko', 2670, 'L', 80, 'GM', 13.44325000, -15.53570000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q972281'),
(52287, 'Niamina East District', 2669, 'M', 80, 'GM', 13.63333000, -15.08333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1626738'),
(52288, 'Niamina West District', 2669, 'M', 80, 'GM', 13.58333000, -15.25000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572232'),
(52289, 'Niani', 2669, 'M', 80, 'GM', 13.66667000, -14.91667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572266'),
(52290, 'Nianija District', 2669, 'M', 80, 'GM', 13.72900000, -15.09100000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572202'),
(52291, 'Nioro', 2670, 'L', 80, 'GM', 13.35000000, -15.75000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1572202'),
(52292, 'No Kunda', 2671, 'N', 80, 'GM', 13.56667000, -15.83333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1994522'),
(52293, 'Nyamanari', 2668, 'U', 80, 'GM', 13.33333000, -13.86667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1994522'),
(52294, 'Pateh Sam', 2669, 'M', 80, 'GM', 13.61667000, -15.06667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2056669'),
(52295, 'Perai', 2668, 'U', 80, 'GM', 13.38333000, -14.03333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1475266'),
(52296, 'Saba', 2671, 'N', 80, 'GM', 13.51639000, -16.04917000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2209357'),
(52297, 'Sabi', 2668, 'U', 80, 'GM', 13.23333000, -14.20000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1753996'),
(52298, 'Sami', 2669, 'M', 80, 'GM', 13.58333000, -15.20000000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1694805'),
(52299, 'Sami District', 2669, 'M', 80, 'GM', 13.58333000, -14.58333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q728332'),
(52300, 'Sandu', 2668, 'U', 80, 'GM', 13.41667000, -14.36667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1136959'),
(52301, 'Sankwia', 2670, 'L', 80, 'GM', 13.46667000, -15.51667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2222431'),
(52302, 'Sara Kunda', 2671, 'N', 80, 'GM', 13.53333000, -15.41667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2222431'),
(52303, 'Saruja', 2669, 'M', 80, 'GM', 13.55000000, -14.91667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1455939'),
(52304, 'Serekunda', 2666, 'B', 80, 'GM', 13.43833000, -16.67806000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q217568'),
(52305, 'Si Kunda', 2670, 'L', 80, 'GM', 13.43333000, -15.56667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2280637'),
(52306, 'Soma', 2670, 'L', 80, 'GM', 13.43333000, -15.53333000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1318028'),
(52307, 'Somita', 2667, 'W', 80, 'GM', 13.20583000, -16.30556000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2299731'),
(52308, 'Sudowol', 2668, 'U', 80, 'GM', 13.36667000, -13.96667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2363327'),
(52309, 'Sukuta', 2669, 'M', 80, 'GM', 13.61667000, -14.91667000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q2364187'),
(52310, 'Sukuta', 2667, 'W', 80, 'GM', 13.41033000, -16.70815000, '2019-10-05 22:53:21', '2019-10-05 22:53:21', 1, 'Q1013940');

