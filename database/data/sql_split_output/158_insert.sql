INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(79863, 'Portobello', 4062, 'OTA', 158, 'NZ', -45.85000000, 170.65000000, '2019-10-05 23:12:16', '2019-10-05 23:12:16', 1, 'Q1322072'),
(79864, 'Prebbleton', 4066, 'CAN', 158, 'NZ', -43.********, 172.51667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q176330'),
(79865, 'Pukekohe East', 4072, 'AUK', 158, 'NZ', -37.20000000, 174.95000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q176330'),
(79866, 'Queenstown', 4062, 'OTA', 158, 'NZ', -45.03023000, 168.66271000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q613602'),
(79867, 'Raglan', 4061, 'WKO', 158, 'NZ', -37.80000000, 174.88333000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q984803'),
(79868, 'Rakaia', 4066, 'CAN', 158, 'NZ', -43.75000000, 172.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1015735'),
(79869, 'Red Hill', 4072, 'AUK', 158, 'NZ', -37.********, 174.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1015735'),
(79870, 'Richmond', 4073, 'TAS', 158, 'NZ', -41.********, 173.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1208462'),
(79871, 'Riverton', 4071, 'STL', 158, 'NZ', -46.********, 168.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q152152'),
(79872, 'Rolleston', 4066, 'CAN', 158, 'NZ', -43.********, 172.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1099966'),
(79873, 'Rosebank', 4072, 'AUK', 158, 'NZ', -36.********, 174.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1099966'),
(79874, 'Rothesay Bay', 4072, 'AUK', 158, 'NZ', -36.********, 174.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q3941757'),
(79875, 'Rotorua', 4074, 'BOP', 158, 'NZ', -38.********, 176.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q208948'),
(79876, 'Ruakaka', 4059, 'NTL', 158, 'NZ', -35.********, 174.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q208948'),
(79877, 'Selwyn District', 4066, 'CAN', 158, 'NZ', -43.********, 171.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1210763'),
(79878, 'South Taranaki District', 4069, 'TKI', 158, 'NZ', -39.********, 174.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1972712'),
(79879, 'South Waikato District', 4061, 'WKO', 158, 'NZ', -38.********, 175.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1760808'),
(79880, 'South Wairarapa District', 4065, 'WGN', 158, 'NZ', -41.26731000, 175.38442000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1760817'),
(79881, 'Southland District', 4071, 'STL', 158, 'NZ', -45.77256000, 167.85266000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1972706'),
(79882, 'Taipa', 4059, 'NTL', 158, 'NZ', -34.99604000, 173.46665000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1972706'),
(79883, 'Tairua', 4061, 'WKO', 158, 'NZ', -37.********, 175.85000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q2388791'),
(79884, 'Takaka', 4073, 'TAS', 158, 'NZ', -40.85000000, 172.80000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q151841'),
(79885, 'Takanini', 4072, 'AUK', 158, 'NZ', -37.04820000, 174.90019000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q151841'),
(79886, 'Tamaki', 4072, 'AUK', 158, 'NZ', -36.88820000, 174.86019000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q151841'),
(79887, 'Taradale', 4075, 'HKB', 158, 'NZ', -39.53333000, 176.85000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q151841'),
(79888, 'Taupo', 4061, 'WKO', 158, 'NZ', -38.68333000, 176.08333000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q2397257'),
(79889, 'Tauranga', 4074, 'BOP', 158, 'NZ', -37.68611000, 176.16667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q207756'),
(79890, 'Te Anau', 4071, 'STL', 158, 'NZ', -45.41667000, 167.71667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1154973'),
(79891, 'Te Kauwhata', 4061, 'WKO', 158, 'NZ', -37.40000000, 175.15000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1526617'),
(79892, 'Thames', 4061, 'WKO', 158, 'NZ', -37.13832000, 175.54011000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1691479'),
(79893, 'Timaru', 4066, 'CAN', 158, 'NZ', -44.39672000, 171.25364000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1016574'),
(79894, 'Timaru District', 4066, 'CAN', 158, 'NZ', -44.40000000, 171.21667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q14395599'),
(79895, 'Tinwald', 4066, 'CAN', 158, 'NZ', -43.91667000, 171.71667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q14395599'),
(79896, 'Titirangi', 4072, 'AUK', 158, 'NZ', -36.93754000, 174.65584000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q6517338'),
(79897, 'Tokoroa', 4061, 'WKO', 158, 'NZ', -38.23333000, 175.86667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q112685'),
(79898, 'Turangi', 4061, 'WKO', 158, 'NZ', -38.99037000, 175.80837000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1026386'),
(79899, 'Upper Hutt', 4065, 'WGN', 158, 'NZ', -41.13827000, 175.05020000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q166002'),
(79900, 'Waihi', 4061, 'WKO', 158, 'NZ', -37.********, 175.83333000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q498833'),
(79901, 'Waihi Beach', 4074, 'BOP', 158, 'NZ', -37.40000000, 175.93333000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1520662'),
(79902, 'Waimakariri District', 4066, 'CAN', 158, 'NZ', -43.20750000, 172.33286000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1542322'),
(79903, 'Waimate North', 4059, 'NTL', 158, 'NZ', -35.31667000, 173.88333000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1542322'),
(79904, 'Waiouru', 4060, 'MWT', 158, 'NZ', -39.47753000, 175.66834000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q912379'),
(79905, 'Waipawa', 4065, 'WGN', 158, 'NZ', -41.41222000, 175.51528000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q912379'),
(79906, 'Wairoa', 4075, 'HKB', 158, 'NZ', -39.03333000, 177.36667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q599594'),
(79907, 'Waitakere', 4072, 'AUK', 158, 'NZ', -36.91754000, 174.65773000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q782760'),
(79908, 'Waitangi', 4067, 'CIT', 158, 'NZ', -43.95353000, -176.55973000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1809774'),
(79909, 'Waitara', 4069, 'TKI', 158, 'NZ', -39.00158000, 174.23836000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q727199'),
(79910, 'Waiuku', 4072, 'AUK', 158, 'NZ', -37.24806000, 174.73489000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q584671'),
(79911, 'Wakefield', 4073, 'TAS', 158, 'NZ', -41.40000000, 173.05000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q683323'),
(79912, 'Wanaka', 4062, 'OTA', 158, 'NZ', -44.70000000, 169.15000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q930344'),
(79913, 'Wanganui', 4060, 'MWT', 158, 'NZ', -39.93333000, 175.05000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1015672'),
(79914, 'Warkworth', 4072, 'AUK', 158, 'NZ', -36.40000000, 174.66667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q590475'),
(79915, 'Wellington', 4065, 'WGN', 158, 'NZ', -41.28664000, 174.77557000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q23661'),
(79916, 'Wellington City', 4065, 'WGN', 158, 'NZ', -41.25300000, 174.75424000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q47037646'),
(79917, 'Wellsford', 4072, 'AUK', 158, 'NZ', -36.28333000, 174.51667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q618344'),
(79918, 'Westport', 4064, 'WTC', 158, 'NZ', -41.75262000, 171.60370000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1349217'),
(79919, 'Whakatane', 4074, 'BOP', 158, 'NZ', -37.95855000, 176.98545000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q974074'),
(79920, 'Whangamata', 4061, 'WKO', 158, 'NZ', -37.20000000, 175.86667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q917136'),
(79921, 'Whangarei', 4059, 'NTL', 158, 'NZ', -35.73167000, 174.32391000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q744239'),
(79922, 'Whitianga', 4061, 'WKO', 158, 'NZ', -36.83333000, 175.70000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1375183'),
(79923, 'Winton', 4071, 'STL', 158, 'NZ', -46.15000000, 168.********, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1721802'),
(79924, 'Wiri', 4072, 'AUK', 158, 'NZ', -36.99820000, 174.86019000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1721802'),
(79925, 'Woodend', 4066, 'CAN', 158, 'NZ', -43.31667000, 172.66667000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q8032610'),
(79926, 'Adam', 3058, 'DA', 166, 'OM', 22.37934000, 57.52718000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q8032610'),
(79927, 'Al Buraymī', 3059, 'BU', 166, 'OM', 24.25088000, 55.79312000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q8032610'),
(79928, 'Al Khābūrah', 3048, 'BS', 166, 'OM', 23.97144000, 57.09313000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q8032610'),
(79929, 'As Suwayq', 3048, 'BS', 166, 'OM', 23.84944000, 57.43861000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q8032610'),
(79930, 'Bahlā’', 3058, 'DA', 166, 'OM', 22.97886000, 57.30470000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q49345172'),
(79931, 'Barkā’', 3050, 'BA', 166, 'OM', 23.67872000, 57.88605000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q49345172'),
(79932, 'Bawshar', 3055, 'MA', 166, 'OM', 23.57769000, 58.39982000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q2892151'),
(79933, 'Bayt al ‘Awābī', 3050, 'BA', 166, 'OM', 23.30324000, 57.52459000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q2892151'),
(79934, 'Bidbid', 3058, 'DA', 166, 'OM', 23.40787000, 58.12830000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q2892151'),
(79935, 'Dib Dibba', 3052, 'MU', 166, 'OM', 26.19778000, 56.25778000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q2892151'),
(79936, 'Haymā’', 3056, 'WU', 166, 'OM', 19.95931000, 56.27575000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q2892151'),
(79937, 'Izkī', 3058, 'DA', 166, 'OM', 22.93333000, 57.76667000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q2709635'),
(79938, 'Khasab', 3052, 'MU', 166, 'OM', 26.17993000, 56.24774000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q426646'),
(79939, 'Liwá', 3048, 'BS', 166, 'OM', 24.53077000, 56.56300000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q426646'),
(79940, 'Madḩā’ al Jadīdah', 3052, 'MU', 166, 'OM', 25.28345000, 56.33280000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1883385'),
(79941, 'Muscat', 3055, 'MA', 166, 'OM', 23.58413000, 58.40778000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q3826'),
(79942, 'Nizwá', 3058, 'DA', 166, 'OM', 22.93333000, 57.53333000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1915327'),
(79943, 'Oman Smart Future City', 3050, 'BA', 166, 'OM', 23.65270000, 57.59926000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1915327'),
(79944, 'Rustaq', 3050, 'BA', 166, 'OM', 23.39083000, 57.42444000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q542678'),
(79945, 'Seeb', 3055, 'MA', 166, 'OM', 23.67027000, 58.18911000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q948050'),
(79946, 'Shināş', 3048, 'BS', 166, 'OM', 24.74260000, 56.46698000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q948050'),
(79947, 'Sohar', 3048, 'BS', 166, 'OM', 24.34745000, 56.70937000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q943270'),
(79948, 'Sufālat Samā’il', 3058, 'DA', 166, 'OM', 23.31667000, 58.********, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q943270'),
(79949, 'Sur', 3051, 'SH', 166, 'OM', 22.56667000, 59.52889000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q737270'),
(79950, 'Yanqul', 3047, 'ZA', 166, 'OM', 23.58645000, 56.53969000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q737270'),
(79951, 'Şalālah', 3057, 'ZU', 166, 'OM', 17.01505000, 54.09237000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1294439'),
(79952, 'Şaḩam', 3048, 'BS', 166, 'OM', 24.17222000, 56.88861000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1294439'),
(79953, '‘Ibrī', 3047, 'ZA', 166, 'OM', 23.22573000, 56.51572000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q225376'),
(79954, 'Achutupo', 1388, 'KY', 170, 'PA', 9.19827000, -77.98729000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79955, 'Agua Buena', 1390, '7', 170, 'PA', 7.83465000, -80.39405000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79956, 'Aguadulce', 1387, '2', 170, 'PA', 8.24183000, -80.54609000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79957, 'Aguas Blancas', 1387, '2', 170, 'PA', 8.50351000, -80.31169000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79958, 'Ailigandí', 1388, 'KY', 170, 'PA', 9.22810000, -78.02778000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79959, 'Alanje', 1397, '4', 170, 'PA', 8.39791000, -82.55947000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79960, 'Alcalde Díaz', 1395, '8', 170, 'PA', 9.12016000, -79.55641000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79961, 'Algarrobos Arriba', 1397, '4', 170, 'PA', 8.51550000, -82.42263000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79962, 'Almirante', 1393, '1', 170, 'PA', 9.30091000, -82.40180000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79963, 'Alto Boquete', 1397, '4', 170, 'PA', 8.73458000, -82.43213000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79964, 'Alto de Jesús', 1392, '9', 170, 'PA', 8.26152000, -81.48412000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79965, 'Alto de La Estancia', 1387, '2', 170, 'PA', 8.58792000, -80.18443000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79966, 'Alto del Espino', 1394, '10', 170, 'PA', 8.84213000, -79.84551000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79967, 'Altos de San Francisco', 1394, '10', 170, 'PA', 8.86167000, -79.79000000, '2019-10-05 23:12:17', '2019-10-05 23:12:17', 1, 'Q1956983'),
(79968, 'Ancón', 1395, '8', 170, 'PA', 8.96015000, -79.55140000, '2019-10-05 23:12:17', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79969, 'Antón', 1387, '2', 170, 'PA', 8.39733000, -80.26063000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79970, 'Arenas', 1392, '9', 170, 'PA', 7.36865000, -80.86268000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79971, 'Arenosa', 1394, '10', 170, 'PA', 9.03978000, -79.95128000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79972, 'Arosemena', 1394, '10', 170, 'PA', 8.97091000, -79.96641000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79973, 'Arraiján', 1394, '10', 170, 'PA', 8.95187000, -79.66011000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79974, 'Aserrío de Gariché', 1397, '4', 170, 'PA', 8.48257000, -82.79086000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79975, 'Atalaya', 1392, '9', 170, 'PA', 8.04213000, -80.92528000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79976, 'Ave María', 1390, '7', 170, 'PA', 7.32481000, -80.45361000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79977, 'Bahía Azul', 1391, 'NB', 170, 'PA', 9.14176000, -81.89425000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79978, 'Bahía Honda', 1390, '7', 170, 'PA', 7.70517000, -80.45342000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79979, 'Bajo Boquete', 1397, '4', 170, 'PA', 8.77058000, -82.43306000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79980, 'Bajo Corral', 1390, '7', 170, 'PA', 7.60463000, -80.26016000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79981, 'Barnizal', 1392, '9', 170, 'PA', 8.40460000, -80.77765000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79982, 'Barranco', 1393, '1', 170, 'PA', 9.51984000, -82.70424000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79983, 'Barranco Adentro', 1393, '1', 170, 'PA', 9.52757000, -82.73344000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79984, 'Barranco Colorado', 1387, '2', 170, 'PA', 8.38937000, -80.63546000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79985, 'Barrio Guadalupe', 1397, '4', 170, 'PA', 8.86482000, -82.56523000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79986, 'Bastimentos', 1393, '1', 170, 'PA', 9.34707000, -82.20880000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79987, 'Bayamón', 1396, 'EM', 170, 'PA', 7.96817000, -78.21648000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79988, 'Bayano', 1390, '7', 170, 'PA', 7.63014000, -80.38201000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79989, 'Bejuco', 1394, '10', 170, 'PA', 8.60037000, -79.88988000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79990, 'Bella Vista', 1390, '7', 170, 'PA', 7.75000000, -80.23333000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79991, 'Besiko', 1391, 'NB', 170, 'PA', 8.54863000, -82.08980000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79992, 'Bijagual', 1397, '4', 170, 'PA', 8.51486000, -82.33361000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79993, 'Bisira', 1391, 'NB', 170, 'PA', 8.89553000, -81.85352000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79994, 'Bisvalles', 1392, '9', 170, 'PA', 8.18298000, -81.22092000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79995, 'Boca Chica', 1397, '4', 170, 'PA', 8.21911000, -82.21592000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79996, 'Boca de Balsa', 1391, 'NB', 170, 'PA', 8.52995000, -82.03132000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79997, 'Boca de Cupé', 1385, '5', 170, 'PA', 8.03003000, -77.58978000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1956983'),
(79998, 'Boca de Parita', 1389, '6', 170, 'PA', 8.00796000, -80.45320000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(79999, 'Boca del Monte', 1397, '4', 170, 'PA', 8.35296000, -82.11379000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1956983'),
(80000, 'Bocas del Toro', 1393, '1', 170, 'PA', 9.34031000, -82.24204000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q888971'),
(80001, 'Boquerón', 1392, '9', 170, 'PA', 8.24414000, -80.85897000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q888971'),
(80002, 'Boquerón', 1397, '4', 170, 'PA', 8.50510000, -82.57025000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q888971'),
(80003, 'Boquete', 1397, '4', 170, 'PA', 8.78024000, -82.44136000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80004, 'Boró', 1392, '9', 170, 'PA', 8.16933000, -81.30071000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80005, 'Breñón', 1397, '4', 170, 'PA', 8.62491000, -82.81277000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80006, 'Brujas', 1395, '8', 170, 'PA', 8.58536000, -78.53008000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80007, 'Buena Vista', 1386, '3', 170, 'PA', 9.27356000, -79.69551000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80008, 'Buenos Aires', 1394, '10', 170, 'PA', 8.63146000, -79.94775000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80009, 'Buenos Aires', 1391, 'NB', 170, 'PA', 8.41384000, -81.48440000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80010, 'Bugaba', 1397, '4', 170, 'PA', 8.48255000, -82.61991000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80011, 'Bugabita Arriba', 1397, '4', 170, 'PA', 8.52143000, -82.63638000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80012, 'Bágala', 1397, '4', 170, 'PA', 8.46399000, -82.52617000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80013, 'Caballero', 1387, '2', 170, 'PA', 8.54343000, -80.19769000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80014, 'Cabra Número Uno', 1395, '8', 170, 'PA', 9.10863000, -79.33694000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80015, 'Cabuya', 1389, '6', 170, 'PA', 8.03138000, -80.63227000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80016, 'Cabuya', 1394, '10', 170, 'PA', 8.57472000, -79.92714000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80017, 'Cabuya', 1387, '2', 170, 'PA', 8.54703000, -80.16756000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80018, 'Caimitillo', 1395, '8', 170, 'PA', 9.15559000, -79.53974000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80019, 'Caimito', 1387, '2', 170, 'PA', 8.62271000, -80.23919000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80020, 'Caimito', 1394, '10', 170, 'PA', 8.81143000, -79.94738000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80021, 'Caimito', 1397, '4', 170, 'PA', 8.54162000, -82.41900000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80022, 'Calabacito', 1392, '9', 170, 'PA', 8.24817000, -81.08187000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80023, 'Caldera', 1397, '4', 170, 'PA', 8.64907000, -82.38058000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80024, 'Calidonia', 1392, '9', 170, 'PA', 7.95756000, -81.38633000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80025, 'Calobre', 1392, '9', 170, 'PA', 8.31886000, -80.84067000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80026, 'Calovébora', 1392, '9', 170, 'PA', 8.78703000, -81.21056000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80027, 'Calzada Larga', 1395, '8', 170, 'PA', 9.17272000, -79.56212000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80028, 'Camarón Arriba', 1391, 'NB', 170, 'PA', 8.38324000, -81.99345000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80029, 'Cambutal', 1390, '7', 170, 'PA', 7.26534000, -80.49105000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80030, 'Camogantí', 1385, '5', 170, 'PA', 8.04171000, -77.88682000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80031, 'Canto del Llano', 1392, '9', 170, 'PA', 8.12472000, -80.96374000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80032, 'Capellanía', 1387, '2', 170, 'PA', 8.29858000, -80.55480000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80033, 'Capira', 1394, '10', 170, 'PA', 8.75636000, -79.87996000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80034, 'Cartí Sugdup', 1388, 'KY', 170, 'PA', 9.46460000, -78.95931000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80035, 'Cativá', 1386, '3', 170, 'PA', 9.36218000, -79.83232000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80036, 'Cauchero', 1393, '1', 170, 'PA', 9.15226000, -82.26450000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80037, 'Cañas', 1390, '7', 170, 'PA', 7.44713000, -80.26480000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80038, 'Cañas Gordas', 1397, '4', 170, 'PA', 8.74309000, -82.91275000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80039, 'Cañaveral', 1387, '2', 170, 'PA', 8.51700000, -80.42916000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80040, 'Cañazas', 1392, '9', 170, 'PA', 8.32004000, -81.21152000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80041, 'Cañita', 1395, '8', 170, 'PA', 9.22236000, -78.89509000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80042, 'Cedro Arriba', 1390, '7', 170, 'PA', 7.78097000, -80.53057000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80043, 'Celmira', 1397, '4', 170, 'PA', 8.54026000, -82.80022000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80044, 'Cermeño', 1394, '10', 170, 'PA', 8.74082000, -79.85299000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80045, 'Cerro Azul', 1395, '8', 170, 'PA', 9.15735000, -79.42097000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80046, 'Cerro Banco', 1391, 'NB', 170, 'PA', 8.45797000, -82.03081000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80047, 'Cerro Cama', 1394, '10', 170, 'PA', 9.02802000, -79.90744000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80048, 'Cerro Caña', 1391, 'NB', 170, 'PA', 8.35174000, -81.61050000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80049, 'Cerro Largo', 1389, '6', 170, 'PA', 7.83377000, -80.83168000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80050, 'Cerro Plata', 1391, 'NB', 170, 'PA', 8.47733000, -81.54585000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80051, 'Cerro Punta', 1397, '4', 170, 'PA', 8.84968000, -82.57261000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80052, 'Cerro Viejo', 1397, '4', 170, 'PA', 8.25349000, -81.57658000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80053, 'Chame', 1394, '10', 170, 'PA', 8.57753000, -79.88595000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80054, 'Changuinola', 1393, '1', 170, 'PA', 9.43000000, -82.52000000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80055, 'Chepillo', 1395, '8', 170, 'PA', 8.95396000, -79.12856000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80056, 'Chepo', 1395, '8', 170, 'PA', 9.17019000, -79.10083000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80057, 'Chichica', 1391, 'NB', 170, 'PA', 8.35885000, -81.66582000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80058, 'Chigoré', 1387, '2', 170, 'PA', 8.53035000, -80.35117000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80059, 'Chiguirí Arriba', 1387, '2', 170, 'PA', 8.67187000, -80.18975000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80060, 'Chilibre', 1395, '8', 170, 'PA', 9.15093000, -79.62098000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q804144'),
(80061, 'Chimán', 1395, '8', 170, 'PA', 8.69125000, -78.63570000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80062, 'Chiriquí', 1397, '4', 170, 'PA', 8.39291000, -82.31993000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80063, 'Chiriquí Grande', 1393, '1', 170, 'PA', 8.94557000, -82.11769000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q804144'),
(80064, 'Chitré', 1389, '6', 170, 'PA', 7.96082000, -80.42944000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q657763'),
(80065, 'Chumical', 1389, '6', 170, 'PA', 7.84482000, -80.72627000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q657763'),
(80066, 'Chupampa', 1389, '6', 170, 'PA', 8.07620000, -80.77656000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q657763'),
(80067, 'Churuquita Chiquita', 1387, '2', 170, 'PA', 8.57555000, -80.27161000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q5118336'),
(80068, 'Churuquita Grande', 1387, '2', 170, 'PA', 8.59449000, -80.27182000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q5118336'),
(80069, 'Cirí de Los Sotos', 1394, '10', 170, 'PA', 8.97206000, -80.09209000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q5118336'),
(80070, 'Cochea Abajo', 1397, '4', 170, 'PA', 8.50512000, -82.35878000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q5118336'),
(80071, 'Coclé', 1387, '2', 170, 'PA', 8.45601000, -80.42899000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q34795886'),
(80072, 'Coclé del Norte', 1386, '3', 170, 'PA', 9.07540000, -80.57177000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q34795886'),
(80073, 'Colón', 1386, '3', 170, 'PA', 9.35451000, -79.90011000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q627852'),
(80074, 'Contadora', 1395, '8', 170, 'PA', 8.62483000, -79.03748000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q627852'),
(80075, 'Cordillera', 1397, '4', 170, 'PA', 8.70683000, -82.60327000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q627852'),
(80076, 'Corozal', 1396, 'EM', 170, 'PA', 8.20108000, -77.59637000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q627852'),
(80077, 'Corozal', 1392, '9', 170, 'PA', 8.07712000, -81.43990000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q627852'),
(80078, 'Cristóbal', 1386, '3', 170, 'PA', 9.35222000, -79.90444000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q80777'),
(80079, 'Cucunatí', 1385, '5', 170, 'PA', 8.57508000, -78.25671000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q80777'),
(80080, 'Curundú', 1395, '8', 170, 'PA', 8.97092000, -79.54612000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q80777'),
(80081, 'Cémaco', 1396, 'EM', 170, 'PA', 8.08285000, -77.54210000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q80777'),
(80082, 'David', 1397, '4', 170, 'PA', 8.42729000, -82.43085000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q279009'),
(80083, 'Distrito Arraiján', 1394, '10', 170, 'PA', 8.95000000, -79.70000000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216621'),
(80084, 'Distrito Chiriquí Grande', 1393, '1', 170, 'PA', 8.94748000, -82.12418000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q515794'),
(80085, 'Distrito Penonomé', 1387, '2', 170, 'PA', 8.63000000, -80.28333000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216569'),
(80086, 'Distrito de Aguadulce', 1387, '2', 170, 'PA', 8.21270000, -80.61441000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216595'),
(80087, 'Distrito de Alanje', 1397, '4', 170, 'PA', 8.39842000, -82.64065000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q8193110'),
(80088, 'Distrito de Antón', 1387, '2', 170, 'PA', 8.45394000, -80.18361000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216997'),
(80089, 'Distrito de Atalaya', 1392, '9', 170, 'PA', 8.02899000, -80.91739000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1650993'),
(80090, 'Distrito de Balboa', 1395, '8', 170, 'PA', 8.46667000, -79.00000000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216400'),
(80091, 'Distrito de Barú', 1397, '4', 170, 'PA', 8.29482000, -82.92726000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q522183'),
(80092, 'Distrito de Bocas del Toro', 1393, '1', 170, 'PA', 9.36235000, -82.26288000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2702428'),
(80093, 'Distrito de Boquerón', 1397, '4', 170, 'PA', 8.61667000, -82.56667000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216576'),
(80094, 'Distrito de Boquete', 1397, '4', 170, 'PA', 8.74896000, -82.36842000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q893253'),
(80095, 'Distrito de Bugaba', 1397, '4', 170, 'PA', 8.68786000, -82.67937000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q955035'),
(80096, 'Distrito de Calobre', 1392, '9', 170, 'PA', 8.39684000, -80.85124000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q921735'),
(80097, 'Distrito de Capira', 1394, '10', 170, 'PA', 8.76228000, -79.88275000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216086'),
(80098, 'Distrito de Cañazas', 1392, '9', 170, 'PA', 8.35596000, -81.28633000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1651005'),
(80099, 'Distrito de Chagres', 1386, '3', 170, 'PA', 9.12300000, -80.11841000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2786644'),
(80100, 'Distrito de Chame', 1394, '10', 170, 'PA', 8.60000000, -79.91667000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q431342'),
(80101, 'Distrito de Changuinola', 1393, '1', 170, 'PA', 9.25000000, -82.63333000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216073'),
(80102, 'Distrito de Chepigana', 1385, '5', 170, 'PA', 7.84374000, -77.83830000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1651404'),
(80103, 'Distrito de Chepo', 1395, '8', 170, 'PA', 9.17391000, -78.70374000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2215925'),
(80104, 'Distrito de Chimán', 1395, '8', 170, 'PA', 8.76043000, -78.56470000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216594'),
(80105, 'Distrito de Chitré', 1389, '6', 170, 'PA', 7.98333000, -80.43333000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216071'),
(80106, 'Distrito de Colón', 1386, '3', 170, 'PA', 9.24313000, -79.79463000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2071684'),
(80107, 'Distrito de David', 1397, '4', 170, 'PA', 8.40000000, -82.40000000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2215578'),
(80108, 'Distrito de Dolega', 1397, '4', 170, 'PA', 8.61667000, -82.45000000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216989'),
(80109, 'Distrito de Donoso', 1386, '3', 170, 'PA', 9.01999000, -80.42967000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2217007'),
(80110, 'Distrito de Gualaca', 1397, '4', 170, 'PA', 8.57398000, -82.22207000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216432'),
(80111, 'Distrito de Guararé', 1390, '7', 170, 'PA', 7.77021000, -80.37515000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q3544497'),
(80112, 'Distrito de La Chorrera', 1394, '10', 170, 'PA', 8.95000000, -79.85000000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2217021'),
(80113, 'Distrito de La Mesa', 1392, '9', 170, 'PA', 8.10976000, -81.17767000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216424'),
(80114, 'Distrito de La Pintada', 1387, '2', 170, 'PA', 8.71801000, -80.53946000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2215435'),
(80115, 'Distrito de Las Minas', 1389, '6', 170, 'PA', 7.76525000, -80.82249000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216080'),
(80116, 'Distrito de Las Palmas', 1392, '9', 170, 'PA', 8.14532000, -81.42849000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1650981'),
(80117, 'Distrito de Las Tablas', 1390, '7', 170, 'PA', 7.64843000, -80.29933000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2217049'),
(80118, 'Distrito de Los Pozos', 1389, '6', 170, 'PA', 7.70944000, -80.64311000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2217030'),
(80119, 'Distrito de Los Santos', 1390, '7', 170, 'PA', 7.88333000, -80.45000000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216602'),
(80120, 'Distrito de Macaracas', 1390, '7', 170, 'PA', 7.65817000, -80.53526000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216055'),
(80121, 'Distrito de Montijo', 1392, '9', 170, 'PA', 7.95316000, -81.05301000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1650967'),
(80122, 'Distrito de Natá', 1387, '2', 170, 'PA', 8.34416000, -80.60934000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2215891'),
(80123, 'Distrito de Ocú', 1389, '6', 170, 'PA', 7.90641000, -80.79752000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216607'),
(80124, 'Distrito de Olá', 1387, '2', 170, 'PA', 8.48053000, -80.65324000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2217017'),
(80125, 'Distrito de Panamá', 1395, '8', 170, 'PA', 9.30612000, -79.45246000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216411'),
(80126, 'Distrito de Parita', 1389, '6', 170, 'PA', 8.06240000, -80.56373000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216637'),
(80127, 'Distrito de Pedasí', 1390, '7', 170, 'PA', 7.48528000, -80.12747000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216610'),
(80128, 'Distrito de Pesé', 1389, '6', 170, 'PA', 7.88194000, -80.63261000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2217039'),
(80129, 'Distrito de Pinogana', 1385, '5', 170, 'PA', 7.89343000, -77.52562000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216562'),
(80130, 'Distrito de Pocrí', 1390, '7', 170, 'PA', 7.63721000, -80.16470000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216539'),
(80131, 'Distrito de Portobelo', 1386, '3', 170, 'PA', 9.49748000, -79.60730000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1650732'),
(80132, 'Distrito de Remedios', 1397, '4', 170, 'PA', 8.20253000, -81.81163000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216582'),
(80133, 'Distrito de Renacimiento', 1397, '4', 170, 'PA', 8.71667000, -82.76667000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216549'),
(80134, 'Distrito de Río de Jesús', 1392, '9', 170, 'PA', 7.91438000, -81.16133000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1650989'),
(80135, 'Distrito de San Carlos', 1394, '10', 170, 'PA', 8.53196000, -80.06906000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1650989'),
(80136, 'Distrito de San Francisco', 1392, '9', 170, 'PA', 8.26965000, -81.00108000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q1650989'),
(80137, 'Distrito de San Félix', 1397, '4', 170, 'PA', 8.27768000, -81.87377000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2217004'),
(80138, 'Distrito de San Lorenzo', 1397, '4', 170, 'PA', 8.29366000, -82.08922000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2217004'),
(80139, 'Distrito de Santa Fé', 1392, '9', 170, 'PA', 8.72063000, -80.95858000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2217004'),
(80140, 'Distrito de Santa Isabel', 1386, '3', 170, 'PA', 9.48333000, -79.31667000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q3585237'),
(80141, 'Distrito de Santa María', 1389, '6', 170, 'PA', 8.11667000, -80.68333000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q3585237'),
(80142, 'Distrito de Santiago', 1392, '9', 170, 'PA', 8.12425000, -80.87827000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q3585237'),
(80143, 'Distrito de Soná', 1392, '9', 170, 'PA', 7.84378000, -81.36612000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1651000'),
(80144, 'Distrito de Taboga', 1395, '8', 170, 'PA', 8.78333000, -79.55833000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2072534'),
(80145, 'Distrito de Tolé', 1397, '4', 170, 'PA', 8.19015000, -81.66007000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q1014642'),
(80146, 'Distrito de Tonosí', 1390, '7', 170, 'PA', 7.43597000, -80.45828000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80147, 'Divalá', 1397, '4', 170, 'PA', 8.41066000, -82.71332000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80148, 'Dolega District', 1397, '4', 170, 'PA', 8.56667000, -82.41407000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80149, 'El Alto', 1392, '9', 170, 'PA', 8.51191000, -81.03646000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80150, 'El Barrito', 1392, '9', 170, 'PA', 7.96059000, -80.94801000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80151, 'El Cacao', 1390, '7', 170, 'PA', 7.44570000, -80.40938000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80152, 'El Cacao', 1394, '10', 170, 'PA', 8.76134000, -80.01294000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80153, 'El Calabacito', 1389, '6', 170, 'PA', 7.71843000, -80.59757000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80154, 'El Capurí', 1389, '6', 170, 'PA', 7.72571000, -80.64349000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80155, 'El Carate', 1390, '7', 170, 'PA', 7.73124000, -80.29691000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80156, 'El Cañafístulo', 1390, '7', 170, 'PA', 7.61768000, -80.23322000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80157, 'El Caño', 1387, '2', 170, 'PA', 8.40195000, -80.51791000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80158, 'El Cedro', 1389, '6', 170, 'PA', 7.68565000, -80.65927000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80159, 'El Chirú', 1387, '2', 170, 'PA', 8.39876000, -80.18721000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80160, 'El Chorrillo', 1395, '8', 170, 'PA', 8.94964000, -79.54715000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80161, 'El Cocal', 1390, '7', 170, 'PA', 7.73697000, -80.27980000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80162, 'El Coclá', 1392, '9', 170, 'PA', 8.32143000, -80.92069000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80163, 'El Coco', 1387, '2', 170, 'PA', 8.39716000, -80.********, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80164, 'El Coco', 1394, '10', 170, 'PA', 8.87009000, -79.80415000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80165, 'El Copé', 1387, '2', 170, 'PA', 8.62036000, -80.58433000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80166, 'El Cortezo', 1390, '7', 170, 'PA', 7.42661000, -80.63311000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80167, 'El Cortezo', 1387, '2', 170, 'PA', 8.34556000, -80.58721000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80168, 'El Cristo', 1387, '2', 170, 'PA', 8.25071000, -80.62093000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80169, 'El Ejido', 1390, '7', 170, 'PA', 7.91634000, -80.38686000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80170, 'El Empalme', 1393, '1', 170, 'PA', 9.41667000, -82.51667000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80171, 'El Espavé', 1394, '10', 170, 'PA', 8.66161000, -79.87584000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80172, 'El Espino', 1394, '10', 170, 'PA', 8.83795000, -79.84925000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80173, 'El Espino de Santa Rosa', 1392, '9', 170, 'PA', 8.09286000, -80.82380000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80174, 'El Giral', 1386, '3', 170, 'PA', 9.24509000, -79.69266000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80175, 'El Guabo', 1386, '3', 170, 'PA', 9.07842000, -80.08316000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80176, 'El Guásimo', 1390, '7', 170, 'PA', 7.80640000, -80.52991000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80177, 'El Hato', 1390, '7', 170, 'PA', 7.79316000, -80.38267000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80178, 'El Higo', 1394, '10', 170, 'PA', 8.44731000, -80.03603000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80179, 'El Líbano', 1394, '10', 170, 'PA', 8.61685000, -79.83483000, '2019-10-05 23:12:18', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80180, 'El Macano', 1390, '7', 170, 'PA', 7.68489000, -80.40309000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80181, 'El Manantial', 1390, '7', 170, 'PA', 7.78245000, -80.24713000, '2019-10-05 23:12:18', '2019-10-05 23:12:18', 1, 'Q2216393'),
(80182, 'El Marañón', 1392, '9', 170, 'PA', 8.03582000, -81.21489000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80183, 'El María', 1392, '9', 170, 'PA', 8.02905000, -81.44384000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80184, 'El Muñoz', 1390, '7', 170, 'PA', 7.67582000, -80.32130000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80185, 'El Nancito', 1397, '4', 170, 'PA', 8.23811000, -81.73218000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2216393'),
(80186, 'El Palmar', 1397, '4', 170, 'PA', 8.30098000, -82.85344000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2216393'),
(80187, 'El Pantano', 1392, '9', 170, 'PA', 8.53755000, -81.06941000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2216393'),
(80188, 'El Pedregoso', 1389, '6', 170, 'PA', 7.93642000, -80.63807000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2216393'),
(80189, 'El Pedregoso', 1390, '7', 170, 'PA', 7.69173000, -80.32793000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2216393'),
(80190, 'El Peñón', 1392, '9', 170, 'PA', 8.********, -80.97620000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2216393'),
(80191, 'El Picador', 1392, '9', 170, 'PA', 8.37803000, -81.25621000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2216393'),
(80192, 'El Porvenir', 1388, 'KY', 170, 'PA', 9.55276000, -78.95230000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80193, 'El Porvenir', 1397, '4', 170, 'PA', 8.22919000, -81.83278000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80194, 'El Potrero', 1387, '2', 170, 'PA', 8.52146000, -80.51681000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80195, 'El Potrero', 1392, '9', 170, 'PA', 8.37692000, -80.79434000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80196, 'El Progreso', 1395, '8', 170, 'PA', 9.26667000, -79.11667000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80197, 'El Pájaro', 1392, '9', 170, 'PA', 7.95342000, -81.12154000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q1018577'),
(80198, 'El Retiro', 1387, '2', 170, 'PA', 8.48014000, -80.15426000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80199, 'El Rincón', 1389, '6', 170, 'PA', 8.11923000, -80.61707000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q1018577'),
(80200, 'El Rincón', 1392, '9', 170, 'PA', 8.18422000, -81.43333000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q1018577'),
(80201, 'El Roble', 1387, '2', 170, 'PA', 8.16850000, -80.65897000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80202, 'El Silencio', 1393, '1', 170, 'PA', 9.37222000, -82.52877000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80203, 'El Tejar', 1397, '4', 170, 'PA', 8.42973000, -82.57294000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80204, 'El Toro', 1389, '6', 170, 'PA', 7.75110000, -80.86943000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q1018577'),
(80205, 'El Uvito', 1392, '9', 170, 'PA', 8.11648000, -80.98461000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80206, 'El Valle de la Unión', 1386, '3', 170, 'PA', 9.23630000, -79.65986000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q5352312'),
(80207, 'Entradero', 1387, '2', 170, 'PA', 8.56880000, -80.20743000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80208, 'Escobal', 1386, '3', 170, 'PA', 9.14373000, -79.96439000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80209, 'Farallón', 1387, '2', 170, 'PA', 8.35658000, -80.13723000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q5352312'),
(80210, 'Finca Blanco', 1397, '4', 170, 'PA', 8.37979000, -82.87385000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80211, 'Flores', 1390, '7', 170, 'PA', 7.48157000, -80.40695000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80212, 'Garachiné', 1385, '5', 170, 'PA', 8.06684000, -78.36436000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q5352312'),
(80213, 'Gatuncillo', 1386, '3', 170, 'PA', 9.24346000, -79.64856000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80214, 'Gatún', 1386, '3', 170, 'PA', 9.28922000, -79.77204000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q5352312'),
(80215, 'Gonzalillo', 1395, '8', 170, 'PA', 9.09183000, -79.51928000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80216, 'Guabal', 1397, '4', 170, 'PA', 8.57466000, -82.53730000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q5352312'),
(80217, 'Guabito', 1393, '1', 170, 'PA', 9.48968000, -82.61279000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80218, 'Guaca Arriba', 1397, '4', 170, 'PA', 8.53733000, -82.49281000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80219, 'Guadalupe', 1394, '10', 170, 'PA', 8.85452000, -79.81408000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80220, 'Gualaca', 1397, '4', 170, 'PA', 8.53006000, -82.29959000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80221, 'Guararé', 1390, '7', 170, 'PA', 7.81531000, -80.28345000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80222, 'Guararé Arriba', 1390, '7', 170, 'PA', 7.80023000, -80.35983000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80223, 'Guarumal', 1397, '4', 170, 'PA', 8.34523000, -82.53205000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80224, 'Guarumal', 1392, '9', 170, 'PA', 7.79404000, -81.25904000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80225, 'Guayabal', 1397, '4', 170, 'PA', 8.62193000, -82.57935000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80226, 'Guayabito', 1391, 'NB', 170, 'PA', 8.53949000, -81.48225000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80227, 'Guayabito', 1394, '10', 170, 'PA', 8.54828000, -80.01350000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80228, 'Guzman', 1387, '2', 170, 'PA', 8.51969000, -80.58405000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80229, 'Gómez', 1397, '4', 170, 'PA', 8.56085000, -82.74142000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80230, 'Hato Chami', 1391, 'NB', 170, 'PA', 8.44281000, -81.77196000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80231, 'Hato Corotú', 1391, 'NB', 170, 'PA', 8.33516000, -81.97030000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80232, 'Hato Montaña', 1394, '10', 170, 'PA', 8.91938000, -79.73915000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80233, 'Horconcitos', 1397, '4', 170, 'PA', 8.31142000, -82.15102000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80234, 'Howard', 1395, '8', 170, 'PA', 8.93942000, -79.59097000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80235, 'Isla Bastimentos', 1393, '1', 170, 'PA', 9.30000000, -82.13333000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2437057'),
(80236, 'Jaqué', 1385, '5', 170, 'PA', 7.51826000, -78.16343000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80237, 'Juan Díaz', 1387, '2', 170, 'PA', 8.46240000, -80.28280000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80238, 'Juan Díaz', 1395, '8', 170, 'PA', 9.05000000, -79.45000000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80239, 'Kankintú', 1391, 'NB', 170, 'PA', 8.89503000, -81.85750000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2437057'),
(80240, 'Kusapín', 1391, 'NB', 170, 'PA', 9.17119000, -81.89621000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q751010'),
(80241, 'La Arena', 1389, '6', 170, 'PA', 7.96766000, -80.46523000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q751010'),
(80242, 'La Cabima', 1395, '8', 170, 'PA', 9.11865000, -79.53660000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q751010'),
(80243, 'La Chorrera', 1394, '10', 170, 'PA', 8.88028000, -79.78333000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80244, 'La Colorada', 1392, '9', 170, 'PA', 8.01319000, -80.98590000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80245, 'La Colorada', 1395, '8', 170, 'PA', 9.10224000, -79.41600000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80246, 'La Colorada', 1390, '7', 170, 'PA', 7.82362000, -80.55552000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80247, 'La Concepción', 1397, '4', 170, 'PA', 8.51212000, -82.61858000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q774405'),
(80248, 'La Enea', 1390, '7', 170, 'PA', 7.83333000, -80.27417000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80249, 'La Ensenada', 1395, '8', 170, 'PA', 8.36618000, -78.84606000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80250, 'La Ermita', 1394, '10', 170, 'PA', 8.45622000, -80.06835000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80251, 'La Esmeralda', 1395, '8', 170, 'PA', 8.26817000, -78.92460000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80252, 'La Esperanza', 1397, '4', 170, 'PA', 8.40344000, -82.79197000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80253, 'La Espigadilla', 1390, '7', 170, 'PA', 7.88389000, -80.39039000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80254, 'La Estrella', 1397, '4', 170, 'PA', 8.51572000, -82.67212000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80255, 'La Garceana', 1392, '9', 170, 'PA', 7.93708000, -81.01643000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80256, 'La Guinea', 1395, '8', 170, 'PA', 8.34250000, -78.93430000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80257, 'La Herradura', 1394, '10', 170, 'PA', 8.84848000, -79.80404000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80258, 'La Laguna', 1394, '10', 170, 'PA', 9.04404000, -79.84215000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80259, 'La Laguna', 1392, '9', 170, 'PA', 8.33882000, -80.72949000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80260, 'La Laja', 1390, '7', 170, 'PA', 7.73102000, -80.25298000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q774405'),
(80261, 'La Loma', 1387, '2', 170, 'PA', 8.16667000, -80.63742000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q6463469'),
(80262, 'La Loma', 1392, '9', 170, 'PA', 7.43708000, -80.89118000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q6463469'),
(80263, 'La Mesa', 1392, '9', 170, 'PA', 8.14707000, -81.18114000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q6463469'),
(80264, 'La Mesa', 1395, '8', 170, 'PA', 9.17308000, -79.27401000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q6463469'),
(80265, 'La Mesa', 1393, '1', 170, 'PA', 9.49587000, -82.67534000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q6463469'),
(80266, 'La Mesa', 1390, '7', 170, 'PA', 7.65670000, -80.61794000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q6463469'),
(80267, 'La Miel', 1390, '7', 170, 'PA', 7.56022000, -80.32736000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q6463469'),
(80268, 'La Mitra', 1394, '10', 170, 'PA', 8.84091000, -79.78648000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80269, 'La Montañuela', 1392, '9', 170, 'PA', 8.02487000, -80.86325000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q10315061'),
(80270, 'La Palma', 1385, '5', 170, 'PA', 8.40608000, -78.13964000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80271, 'La Palma', 1390, '7', 170, 'PA', 7.71667000, -80.********, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80272, 'La Pasera', 1390, '7', 170, 'PA', 7.79757000, -80.30342000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80273, 'La Pesa', 1394, '10', 170, 'PA', 8.85073000, -79.82374000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80274, 'La Peña', 1392, '9', 170, 'PA', 8.12784000, -81.02693000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q10315061'),
(80275, 'La Pintada', 1387, '2', 170, 'PA', 8.59299000, -80.44349000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80276, 'La Raya de Calobre', 1392, '9', 170, 'PA', 8.21630000, -80.82729000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80277, 'La Raya de Santa María', 1392, '9', 170, 'PA', 8.16429000, -80.82143000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q10315061'),
(80278, 'La Tiza', 1390, '7', 170, 'PA', 7.75110000, -80.29216000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80279, 'La Trinidad', 1389, '6', 170, 'PA', 7.91457000, -80.70332000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80280, 'La Tronosa', 1390, '7', 170, 'PA', 7.43609000, -80.58698000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80281, 'La Yeguada', 1392, '9', 170, 'PA', 8.43060000, -80.85752000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80282, 'Lajamina', 1390, '7', 170, 'PA', 7.58587000, -80.13212000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80283, 'Lajas Adentro', 1397, '4', 170, 'PA', 8.24981000, -81.87779000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80284, 'Lajas de Tolé', 1397, '4', 170, 'PA', 8.16994000, -81.69654000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q10315061'),
(80285, 'Lajero Arriba', 1391, 'NB', 170, 'PA', 8.29060000, -81.77047000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80286, 'Las Colinas', 1394, '10', 170, 'PA', 8.55000000, -80.08333000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80287, 'Las Cruces', 1390, '7', 170, 'PA', 7.82285000, -80.43003000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q10315061'),
(80288, 'Las Cumbres', 1395, '8', 170, 'PA', 9.08916000, -79.52809000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80289, 'Las Guabas', 1389, '6', 170, 'PA', 7.90949000, -80.80706000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80290, 'Las Guabas', 1390, '7', 170, 'PA', 7.81469000, -80.50155000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80291, 'Las Guías Abajo', 1392, '9', 170, 'PA', 8.19451000, -80.75494000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q3916792'),
(80292, 'Las Huacas', 1392, '9', 170, 'PA', 7.90167000, -81.14056000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80293, 'Las Huacas del Quije', 1387, '2', 170, 'PA', 8.46702000, -80.75089000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80294, 'Las Lajas', 1394, '10', 170, 'PA', 8.54963000, -79.93521000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80295, 'Las Lajas', 1397, '4', 170, 'PA', 8.24184000, -81.86931000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80296, 'Las Lomas', 1397, '4', 170, 'PA', 8.42927000, -82.38743000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80297, 'Las Margaritas', 1395, '8', 170, 'PA', 9.18426000, -79.08730000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80298, 'Las Margaritas', 1386, '3', 170, 'PA', 9.32603000, -79.89028000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80299, 'Las Marias', 1387, '2', 170, 'PA', 8.88809000, -80.21893000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80300, 'Las Minas', 1389, '6', 170, 'PA', 7.79581000, -80.74570000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80301, 'Las Minas', 1387, '2', 170, 'PA', 8.64118000, -80.39432000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80302, 'Las Palmas', 1392, '9', 170, 'PA', 8.13564000, -81.45674000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80303, 'Las Palmas', 1390, '7', 170, 'PA', 7.68314000, -80.49728000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80304, 'Las Palmitas', 1390, '7', 170, 'PA', 7.76523000, -80.29350000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80305, 'Las Sabanas', 1387, '2', 170, 'PA', 8.58141000, -80.67978000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q3916792'),
(80306, 'Las Tablas', 1390, '7', 170, 'PA', 7.76472000, -80.27483000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80307, 'Las Trancas', 1390, '7', 170, 'PA', 7.73400000, -80.37384000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80308, 'Las Uvas', 1394, '10', 170, 'PA', 8.45937000, -80.00075000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80309, 'Leones Arriba', 1389, '6', 170, 'PA', 7.76590000, -80.84515000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80310, 'Leones Arriba', 1392, '9', 170, 'PA', 7.75415000, -81.11556000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80311, 'Limones', 1397, '4', 170, 'PA', 8.10007000, -82.86679000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80312, 'Limón de Tijeras', 1389, '6', 170, 'PA', 7.81756000, -80.91335000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2712890'),
(80313, 'Llano Abajo', 1390, '7', 170, 'PA', 7.78598000, -80.41060000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80314, 'Llano Bonito', 1389, '6', 170, 'PA', 7.97637000, -80.41646000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80315, 'Llano Grande', 1389, '6', 170, 'PA', 7.97406000, -80.71103000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80316, 'Llano Grande', 1392, '9', 170, 'PA', 8.07473000, -81.13626000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80317, 'Llano Grande', 1387, '2', 170, 'PA', 8.63817000, -80.43848000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80318, 'Llano Largo', 1394, '10', 170, 'PA', 8.83463000, -79.80765000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80319, 'Llano Largo', 1390, '7', 170, 'PA', 7.90607000, -80.42561000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80320, 'Llano Marín', 1387, '2', 170, 'PA', 8.47696000, -80.32499000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2712890'),
(80321, 'Llano de La Cruz', 1389, '6', 170, 'PA', 7.95178000, -80.64239000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80322, 'Llano de Piedra', 1390, '7', 170, 'PA', 7.65972000, -80.56347000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80323, 'Llano Ñopo', 1391, 'NB', 170, 'PA', 8.41996000, -81.61865000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2712890'),
(80324, 'Lolá', 1392, '9', 170, 'PA', 8.09326000, -81.47163000, '2019-10-05 23:12:19', '2020-05-01 17:23:04', 1, 'Q2712890'),
(80325, 'Loma Yuca', 1391, 'NB', 170, 'PA', 8.71817000, -81.40492000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80326, 'Los Algarrobos', 1397, '4', 170, 'PA', 8.49601000, -82.42417000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80327, 'Los Algarrobos', 1392, '9', 170, 'PA', 8.10898000, -81.01355000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80328, 'Los Anastacios', 1397, '4', 170, 'PA', 8.53027000, -82.42295000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80329, 'Los Asientos', 1390, '7', 170, 'PA', 7.51656000, -80.13521000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80330, 'Los Canelos', 1389, '6', 170, 'PA', 8.11722000, -80.70769000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80331, 'Los Castillos', 1389, '6', 170, 'PA', 7.98565000, -80.61886000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80332, 'Los Castillos', 1392, '9', 170, 'PA', 8.02326000, -81.11941000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80333, 'Los Cerritos', 1389, '6', 170, 'PA', 7.79746000, -80.61071000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80334, 'Los Cerros de Paja', 1389, '6', 170, 'PA', 7.71821000, -80.67273000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80335, 'Los Higos', 1392, '9', 170, 'PA', 8.20553000, -80.86022000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80336, 'Los Llanitos', 1394, '10', 170, 'PA', 8.59787000, -80.09503000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80337, 'Los Lotes', 1395, '8', 170, 'PA', 9.12425000, -79.28929000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80338, 'Los Naranjos', 1397, '4', 170, 'PA', 8.79210000, -82.44665000, '2019-10-05 23:12:19', '2019-10-05 23:12:19', 1, 'Q2712890'),
(80339, 'Los Olivos', 1390, '7', 170, 'PA', 7.91589000, -80.48884000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q2712890'),
(80340, 'Los Pollos', 1387, '2', 170, 'PA', 8.39898000, -80.15731000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q2712890'),
(80341, 'Los Pozos', 1389, '6', 170, 'PA', 7.78300000, -80.64524000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q2712890'),
(80342, 'Los Santos', 1390, '7', 170, 'PA', 7.93333000, -80.41667000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q2712890'),
(80343, 'Los Ángeles', 1390, '7', 170, 'PA', 7.88422000, -80.35772000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q2712890'),
(80344, 'Los Ángeles', 1397, '4', 170, 'PA', 8.52271000, -82.19997000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q2712890'),
(80345, 'Lídice', 1395, '8', 170, 'PA', 8.74869000, -79.90974000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q931897'),
(80346, 'Macaracas', 1390, '7', 170, 'PA', 7.73168000, -80.55364000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q931897'),
(80347, 'Manaca Civil', 1397, '4', 170, 'PA', 8.32458000, -82.81570000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q931897'),
(80348, 'Manaca Norte', 1397, '4', 170, 'PA', 8.33419000, -82.81003000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q931897'),
(80349, 'Mariato District', 1392, '9', 170, 'PA', 7.67810000, -81.00409000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80350, 'María Chiquita', 1386, '3', 170, 'PA', 9.43991000, -79.75455000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q3711142'),
(80351, 'Mata del Nance', 1397, '4', 170, 'PA', 8.45056000, -82.40057000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80352, 'Mendoza', 1394, '10', 170, 'PA', 9.01471000, -79.85073000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80353, 'Metetí', 1385, '5', 170, 'PA', 8.49909000, -77.97897000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q3711142'),
(80354, 'Miramar', 1386, '3', 170, 'PA', 9.57544000, -79.33573000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80355, 'Miramar', 1393, '1', 170, 'PA', 8.99482000, -82.24147000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80356, 'Mirono', 1391, 'NB', 170, 'PA', 8.40469000, -81.83064000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80357, 'Mogollón', 1390, '7', 170, 'PA', 7.64261000, -80.45950000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q3711142'),
(80358, 'Monagrillo', 1389, '6', 170, 'PA', 7.98179000, -80.43764000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80359, 'Monjarás', 1392, '9', 170, 'PA', 8.36942000, -80.86667000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q3711142'),
(80360, 'Monte Lirio', 1397, '4', 170, 'PA', 8.78935000, -82.82865000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80361, 'Montijo', 1392, '9', 170, 'PA', 7.98858000, -81.05643000, '2019-10-05 23:12:20', '2019-10-05 23:12:20', 1, 'Q3711142'),
(80362, 'Mortí', 1385, '5', 170, 'PA', 8.84246000, -77.97539000, '2019-10-05 23:12:20', '2020-05-01 17:23:04', 1, 'Q3711142');

