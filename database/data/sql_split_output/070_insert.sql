INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(35059, 'Lizartza', 1191, '<PERSON>', 207, 'ES', 43.10236000, -2.03489000, '2019-10-05 22:45:55', '2022-08-28 18:36:49', 1, 'Q1633857'),
(35060, 'Lizo<PERSON>in', 1204, 'NA', 207, 'ES', 42.79868000, -1.46753000, '2019-10-05 22:45:55', '2022-08-29 12:06:07', 1, 'Q1633857'),
(35061, 'Liédena', 1204, 'NA', 207, 'ES', 42.61943000, -1.27579000, '2019-10-05 22:45:55', '2022-08-29 12:06:07', 1, 'Q1647968'),
(35063, '<PERSON><PERSON><PERSON>', 5103, 'G<PERSON>', 207, 'ES', 42.24769000, 2.81373000, '2019-10-05 22:45:55', '2022-08-29 10:53:16', 1, 'Q11334'),
(35064, '<PERSON>lagost<PERSON>', 5103, '<PERSON>I', 207, 'ES', 41.82688000, 2.89365000, '2019-10-05 22:45:55', '2022-08-29 10:53:16', 1, 'Q11334'),
(35065, 'Llamas de la Ribera', 1200, 'LE', 207, 'ES', 42.63504000, -5.82524000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q137174'),
(35066, 'Llambilles', 5103, 'GI', 207, 'ES', 41.92106000, 2.85078000, '2019-10-05 22:45:55', '2022-08-29 10:53:16', 1, 'Q13438'),
(35067, 'Llanera de Ranes', 1175, 'V', 207, 'ES', 38.99507000, -0.57534000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q1807484'),
(35068, 'Llano de Bureba', 1146, 'BU', 207, 'ES', 42.62435000, -3.45890000, '2019-10-05 22:45:55', '2022-08-29 11:24:20', 1, 'Q1113409'),
(35069, 'Llano de Olmedo', 1183, 'VA', 207, 'ES', 41.26727000, -4.61386000, '2019-10-05 22:45:55', '2022-08-29 11:48:45', 1, 'Q640487'),
(35070, 'Llançà', 5103, 'GI', 207, 'ES', 42.36241000, 3.15213000, '2019-10-05 22:45:55', '2022-08-29 10:53:16', 1, 'Q640487'),
(35071, 'Llaurí', 1175, 'V', 207, 'ES', 39.14671000, -0.32944000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q2041947'),
(35072, 'Lledó', 5111, 'TE', 207, 'ES', 40.95498000, 0.27749000, '2019-10-05 22:45:55', '2022-08-29 11:29:44', 1, 'Q1644835'),
(35074, 'Lleida', 5104, 'L', 207, 'ES', 41.61674000, 0.62218000, '2019-10-05 22:45:55', '2022-08-29 10:55:25', 1, 'Q9023235'),
(35075, 'Llera', 5092, 'BA', 207, 'ES', 38.45000000, -6.05000000, '2019-10-05 22:45:55', '2022-08-28 18:09:23', 1, 'Q1645899'),
(35076, 'Llerena', 5092, 'BA', 207, 'ES', 38.23333000, -6.01667000, '2019-10-05 22:45:55', '2022-08-28 18:09:23', 1, 'Q686184'),
(35077, 'Llers', 5103, 'GI', 207, 'ES', 42.29571000, 2.91183000, '2019-10-05 22:45:55', '2022-08-29 10:53:16', 1, 'Q686184'),
(35078, 'Lles de Cerdanya', 5104, 'L', 207, 'ES', 42.39025000, 1.68692000, '2019-10-05 22:45:55', '2022-08-29 10:55:25', 1, 'Q686184'),
(35079, 'Llimiana', 5104, 'L', 207, 'ES', 42.07476000, 0.91621000, '2019-10-05 22:45:55', '2022-08-29 10:55:25', 1, 'Q18264'),
(35080, 'Llinars del Vallès', 5102, 'B', 207, 'ES', 41.63333000, 2.40000000, '2019-10-05 22:45:55', '2022-08-29 10:50:00', 1, 'Q15423'),
(35082, 'Llocnou de Sant Jeroni', 1175, 'V', 207, 'ES', 38.91667000, -0.28333000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q15423'),
(35083, 'Llombai', 1175, 'V', 207, 'ES', 39.28333000, -0.56667000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q15423'),
(35084, 'Lloret de Mar', 5103, 'GI', 207, 'ES', 41.69993000, 2.84565000, '2019-10-05 22:45:55', '2022-08-29 10:53:16', 1, 'Q15423'),
(35085, 'Lloret de Vistalegre', 1174, 'PM', 207, 'ES', 39.61835000, 2.97493000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q15423'),
(35086, 'Llosa de Ranes', 1175, 'V', 207, 'ES', 39.02163000, -0.53803000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q524464'),
(35087, 'Lloseta', 1174, 'PM', 207, 'ES', 39.71849000, 2.86690000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q847317'),
(35088, 'Llubí', 1174, 'PM', 207, 'ES', 39.69933000, 3.00681000, '2019-10-05 22:45:55', '2020-05-01 17:23:15', 1, 'Q185409'),
(35089, 'Llucmajor', 1174, 'PM', 207, 'ES', 39.49093000, 2.89108000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q185409'),
(35090, 'Llutxent', 1175, 'V', 207, 'ES', 38.93333000, -0.35000000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q185409'),
(35091, 'Llíria', 1175, 'V', 207, 'ES', 39.62894000, -0.59783000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q185409'),
(35092, 'Lo Pagán', 1176, 'MU', 207, 'ES', 37.81761000, -0.78832000, '2019-10-05 22:45:55', '2022-08-29 12:05:49', 1, 'Q185409'),
(35093, 'Loarre', 1177, 'HU', 207, 'ES', 42.31432000, -0.62588000, '2019-10-05 22:45:55', '2022-08-29 12:06:20', 1, 'Q24013354'),
(35094, 'Lobera de Onsella', 5113, 'Z', 207, 'ES', 42.47848000, -1.02208000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q1639598'),
(35095, 'Lobios', 5091, 'OR', 207, 'ES', 42.40746000, -7.53164000, '2019-10-05 22:45:55', '2022-08-28 17:53:26', 1, 'Q1639598'),
(35096, 'Lobras', 5098, 'GR', 207, 'ES', 36.92849000, -3.21230000, '2019-10-05 22:45:55', '2022-08-28 18:52:58', 1, 'Q582987'),
(35097, 'Lobón', 5092, 'BA', 207, 'ES', 38.84876000, -6.62365000, '2019-10-05 22:45:55', '2022-08-28 18:09:23', 1, 'Q582987'),
(35098, 'Lodosa', 1204, 'NA', 207, 'ES', 42.42269000, -2.07741000, '2019-10-05 22:45:55', '2022-08-29 12:06:07', 1, 'Q984882'),
(35099, 'Loeches', 1158, 'M', 207, 'ES', 40.38343000, -3.41460000, '2019-10-05 22:45:55', '2022-08-29 12:04:40', 1, 'Q1646206'),
(35100, 'Logrosán', 1190, 'CC', 207, 'ES', 39.33641000, -5.49281000, '2019-10-05 22:45:55', '2022-08-28 18:12:22', 1, 'Q1632442'),
(35101, 'Logroño', 1171, 'LO', 207, 'ES', 42.46667000, -2.45000000, '2019-10-05 22:45:55', '2022-08-29 12:05:09', 1, 'Q31917806'),
(35102, 'Loja', 5098, 'GR', 207, 'ES', 37.16887000, -4.15129000, '2019-10-05 22:45:55', '2022-08-28 18:52:58', 1, 'Q31917806'),
(35103, 'Lomas', 1157, 'P', 207, 'ES', 42.27362000, -4.55095000, '2019-10-05 22:45:55', '2022-08-29 11:45:45', 1, 'Q31917806'),
(35104, 'Lominchar', 1205, 'TO', 207, 'ES', 40.09061000, -3.96713000, '2019-10-05 22:45:55', '2022-08-29 11:08:30', 1, 'Q1630039'),
(35105, 'Lomo de Arico', 1185, 'GC', 207, 'ES', 28.16667000, -16.48333000, '2019-10-05 22:45:55', '2022-08-29 12:06:32', 1, 'Q657738'),
(35106, 'Longares', 5113, 'Z', 207, 'ES', 41.40308000, -1.16876000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q1650702'),
(35107, 'Longás', 5113, 'Z', 207, 'ES', 42.48092000, -0.93420000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q644246'),
(35108, 'Lopera', 5100, 'J', 207, 'ES', 37.94542000, -4.21463000, '2019-10-05 22:45:55', '2022-08-28 19:04:30', 1, 'Q782775'),
(35109, 'Loporzano', 1177, 'HU', 207, 'ES', 42.16137000, -0.32337000, '2019-10-05 22:45:55', '2022-08-29 12:06:20', 1, 'Q591645'),
(35110, 'Lora de Estepa', 1193, 'SE', 207, 'ES', 37.26926000, -4.82759000, '2019-10-05 22:45:55', '2022-08-28 19:08:49', 1, 'Q1605597'),
(35111, 'Lora del Río', 1193, 'SE', 207, 'ES', 37.65896000, -5.52751000, '2019-10-05 22:45:55', '2022-08-28 19:08:49', 1, 'Q1161395'),
(35112, 'Loranca de Tajuña', 5107, 'GU', 207, 'ES', 40.44368000, -3.11082000, '2019-10-05 22:45:55', '2022-08-29 11:06:45', 1, 'Q974638'),
(35113, 'Lorca', 1176, 'MU', 207, 'ES', 37.67119000, -1.70170000, '2019-10-05 22:45:55', '2022-08-29 12:05:49', 1, 'Q24012545'),
(35114, 'Loriguilla', 1175, 'V', 207, 'ES', 39.68333000, -0.91667000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q2041849'),
(35115, 'Lorquí', 1176, 'MU', 207, 'ES', 38.08261000, -1.25103000, '2019-10-05 22:45:55', '2022-08-29 12:05:49', 1, 'Q1982966'),
(35116, 'Los Alcázares', 1176, 'MU', 207, 'ES', 37.74425000, -0.85041000, '2019-10-05 22:45:55', '2022-08-29 12:05:49', 1, 'Q1982975'),
(35117, 'Los Arcos', 1204, 'NA', 207, 'ES', 42.57076000, -2.19275000, '2019-10-05 22:45:55', '2022-08-29 12:06:07', 1, 'Q742713'),
(35120, 'Los Corrales de Buelna', 1170, 'S', 207, 'ES', 43.26358000, -4.07262000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q1443867'),
(35121, 'Los Gabatos', 1176, 'MU', 207, 'ES', 37.63333000, -1.00000000, '2019-10-05 22:45:55', '2022-08-29 12:05:49', 1, 'Q1443867'),
(35122, 'Los Gigantes', 1185, 'GC', 207, 'ES', 28.24361000, -16.84153000, '2019-10-05 22:45:55', '2022-08-29 12:06:32', 1, 'Q1489481'),
(35124, 'Los Llanos de Aridane', 1185, 'GC', 207, 'ES', 28.65851000, -17.91821000, '2019-10-05 22:45:55', '2022-08-29 12:06:32', 1, 'Q366948'),
(35125, 'Los Martínez', 1176, 'MU', 207, 'ES', 37.81841000, -1.07880000, '2019-10-05 22:45:55', '2022-08-29 12:05:49', 1, 'Q366948'),
(35127, 'Los Molinos', 1158, 'M', 207, 'ES', 40.71578000, -4.07402000, '2019-10-05 22:45:55', '2022-08-29 12:04:40', 1, 'Q963615'),
(35128, 'Los Montesinos', 1175, 'V', 207, 'ES', 38.02822000, -0.74501000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q1983327'),
(35132, 'Los Realejos', 1185, 'GC', 207, 'ES', 28.38487000, -16.58275000, '2019-10-05 22:45:55', '2022-08-29 12:06:32', 1, 'Q953876'),
(35134, 'Los Santos de la Humosa', 1158, 'M', 207, 'ES', 40.49968000, -3.25332000, '2019-10-05 22:45:55', '2022-08-29 12:04:40', 1, 'Q1648733'),
(35135, 'Los Silos', 1185, 'GC', 207, 'ES', 28.36610000, -16.81552000, '2019-10-05 22:45:55', '2022-08-29 12:06:32', 1, 'Q682964'),
(35138, 'Losa del Obispo', 1175, 'V', 207, 'ES', 39.70000000, -0.86667000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q2269866'),
(35139, 'Losacino', 1161, 'ZA', 207, 'ES', 41.68084000, -6.07986000, '2019-10-05 22:45:55', '2022-08-29 11:48:03', 1, 'Q931686'),
(35140, 'Losacio', 1161, 'ZA', 207, 'ES', 41.71092000, -6.04059000, '2019-10-05 22:45:55', '2022-08-29 11:48:03', 1, 'Q961848'),
(35141, 'Losar de la Vera', 1190, 'CC', 207, 'ES', 40.12158000, -5.60454000, '2019-10-05 22:45:55', '2022-08-28 18:12:22', 1, 'Q1631182'),
(35142, 'Loscorrales', 1177, 'HU', 207, 'ES', 42.25451000, -0.64296000, '2019-10-05 22:45:55', '2022-08-29 12:06:20', 1, 'Q1906720'),
(35143, 'Loscos', 5111, 'TE', 207, 'ES', 41.08108000, -1.04433000, '2019-10-05 22:45:55', '2022-08-29 11:29:44', 1, 'Q1651451'),
(35144, 'Lousame', 5089, 'C', 207, 'ES', 42.76477000, -8.84926000, '2019-10-05 22:45:55', '2022-08-28 13:37:17', 1, 'Q1450842'),
(35145, 'Lozoya', 1158, 'M', 207, 'ES', 40.94949000, -3.79086000, '2019-10-05 22:45:55', '2022-08-29 12:04:40', 1, 'Q599830'),
(35146, 'Lubián', 1161, 'ZA', 207, 'ES', 42.03569000, -6.90541000, '2019-10-05 22:45:55', '2022-08-29 11:48:03', 1, 'Q1646337'),
(35147, 'Lubrín', 5095, 'AL', 207, 'ES', 37.21538000, -2.06677000, '2019-10-05 22:45:55', '2022-08-28 18:41:41', 1, 'Q1646337'),
(35148, 'Lucainena de las Torres', 5095, 'AL', 207, 'ES', 37.04037000, -2.20095000, '2019-10-05 22:45:55', '2022-08-28 18:41:41', 1, 'Q1358027'),
(35149, 'Lucena', 5097, 'CO', 207, 'ES', 37.40881000, -4.48522000, '2019-10-05 22:45:55', '2022-08-28 18:49:38', 1, 'Q1358027'),
(35150, 'Lucena de Jalón', 5113, 'Z', 207, 'ES', 41.55301000, -1.31305000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q1639376'),
(35151, 'Lucena del Cid', 5110, 'CS', 207, 'ES', 40.13333000, -0.28333000, '2019-10-05 22:45:55', '2022-08-29 11:26:42', 1, 'Q1639376'),
(35152, 'Lucena del Puerto', 5099, 'H', 207, 'ES', 37.30396000, -6.72926000, '2019-10-05 22:45:55', '2022-08-28 19:00:43', 1, 'Q1614539'),
(35153, 'Luceni', 5113, 'Z', 207, 'ES', 41.82857000, -1.23889000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q776889'),
(35154, 'Lucillos', 1205, 'TO', 207, 'ES', 39.98510000, -4.61279000, '2019-10-05 22:45:55', '2022-08-29 11:08:30', 1, 'Q1641371'),
(35155, 'Ludiente', 5110, 'CS', 207, 'ES', 40.08333000, -0.36667000, '2019-10-05 22:45:55', '2022-08-29 11:26:42', 1, 'Q1769737'),
(35156, 'Luelmo', 1161, 'ZA', 207, 'ES', 41.44030000, -6.13338000, '2019-10-05 22:45:55', '2022-08-29 11:48:03', 1, 'Q1765362'),
(35157, 'Luesia', 5113, 'Z', 207, 'ES', 42.36974000, -1.02421000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q983546'),
(35158, 'Luesma', 5113, 'Z', 207, 'ES', 41.16583000, -1.14575000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q1650612'),
(35159, 'Lugo', 5090, 'LU', 207, 'ES', 43.00992000, -7.55602000, '2019-10-05 22:45:55', '2022-08-28 17:49:36', 1, 'Q1650612'),
(35160, 'Lugros', 5098, 'GR', 207, 'ES', 37.22942000, -3.24150000, '2019-10-05 22:45:55', '2022-08-28 18:52:58', 1, 'Q1635929'),
(35161, 'Luintra', 5091, 'OR', 207, 'ES', 42.40964000, -7.72682000, '2019-10-05 22:45:55', '2022-08-28 18:05:08', 1, 'Q1635929'),
(35162, 'Lumbier', 1204, 'NA', 207, 'ES', 42.65303000, -1.30669000, '2019-10-05 22:45:55', '2022-08-29 12:06:07', 1, 'Q584132'),
(35163, 'Lumbrales', 1147, 'SA', 207, 'ES', 40.93501000, -6.71948000, '2019-10-05 22:45:55', '2022-08-29 11:44:51', 1, 'Q1639439'),
(35164, 'Lumbreras', 1171, 'LO', 207, 'ES', 42.10477000, -2.62189000, '2019-10-05 22:45:55', '2022-08-29 12:05:09', 1, 'Q1646969'),
(35165, 'Lumpiaque', 5113, 'Z', 207, 'ES', 41.62953000, -1.30156000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q1641002'),
(35166, 'Luna', 5113, 'Z', 207, 'ES', 42.16760000, -0.93292000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q1641002'),
(35167, 'Lupiana', 5107, 'GU', 207, 'ES', 40.60846000, -3.05118000, '2019-10-05 22:45:55', '2022-08-29 11:06:45', 1, 'Q24011172'),
(35168, 'Lupión', 5100, 'J', 207, 'ES', 37.99653000, -3.54699000, '2019-10-05 22:45:55', '2022-08-28 19:04:30', 1, 'Q1641680'),
(35169, 'Luque', 5097, 'CO', 207, 'ES', 37.55797000, -4.27974000, '2019-10-05 22:45:55', '2022-08-28 18:49:38', 1, 'Q986552'),
(35170, 'Luyego', 1200, 'LE', 207, 'ES', 42.36667000, -6.23333000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q141204'),
(35171, 'Luzaga', 5107, 'GU', 207, 'ES', 40.97337000, -2.44497000, '2019-10-05 22:45:55', '2022-08-29 11:06:45', 1, 'Q1635212'),
(35172, 'Luzmela', 1170, 'S', 207, 'ES', 43.29685000, -4.20884000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q1635212'),
(35173, 'Luzón', 5107, 'GU', 207, 'ES', 41.02691000, -2.27691000, '2019-10-05 22:45:55', '2022-08-29 11:06:45', 1, 'Q1775905'),
(35174, 'Láchar', 5098, 'GR', 207, 'ES', 37.19519000, -3.83277000, '2019-10-05 22:45:55', '2022-08-28 18:52:58', 1, 'Q484981'),
(35175, 'Láncara', 5090, 'LU', 207, 'ES', 42.86425000, -7.33667000, '2019-10-05 22:45:55', '2022-08-28 17:49:36', 1, 'Q484981'),
(35176, 'Lécera', 5113, 'Z', 207, 'ES', 41.20491000, -0.71133000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q24019939'),
(35177, 'Lúcar', 5095, 'AL', 207, 'ES', 37.40035000, -2.42496000, '2019-10-05 22:45:55', '2022-08-28 18:41:41', 1, 'Q24019939'),
(35178, 'Lújar', 5098, 'GR', 207, 'ES', 36.78831000, -3.40400000, '2019-10-05 22:45:55', '2022-08-28 18:52:58', 1, 'Q24019939'),
(35179, 'Macael', 5095, 'AL', 207, 'ES', 37.33318000, -2.30087000, '2019-10-05 22:45:55', '2022-08-28 18:41:41', 1, 'Q1161455'),
(35180, 'Macastre', 1175, 'V', 207, 'ES', 39.38333000, -0.78333000, '2019-10-05 22:45:55', '2022-08-29 12:05:40', 1, 'Q23068'),
(35181, 'Maceda', 5091, 'OR', 207, 'ES', 42.26972000, -7.65052000, '2019-10-05 22:45:55', '2022-08-28 17:53:26', 1, 'Q1617896'),
(35182, 'Machacón', 1147, 'SA', 207, 'ES', 40.92623000, -5.52430000, '2019-10-05 22:45:55', '2022-08-29 11:44:51', 1, 'Q1911047'),
(35183, 'Macotera', 1147, 'SA', 207, 'ES', 40.83105000, -5.28526000, '2019-10-05 22:45:55', '2022-08-29 11:44:51', 1, 'Q31917914'),
(35184, 'Maderuelo', 1192, 'SG', 207, 'ES', 41.48644000, -3.52218000, '2019-10-05 22:45:55', '2022-08-29 11:50:42', 1, 'Q1948893'),
(35185, 'Madremanya', 5103, 'GI', 207, 'ES', 41.98333000, 2.96667000, '2019-10-05 22:45:55', '2022-08-29 10:53:16', 1, 'Q13441'),
(35186, 'Madrid', 1158, 'M', 207, 'ES', 40.41650000, -3.70256000, '2019-10-05 22:45:55', '2022-08-29 12:04:40', 1, 'Q2807'),
(35187, 'Madridanos', 1161, 'ZA', 207, 'ES', 41.47967000, -5.60459000, '2019-10-05 22:45:55', '2022-08-29 11:48:03', 1, 'Q1652345'),
(35188, 'Madridejos', 1205, 'TO', 207, 'ES', 39.46823000, -3.53196000, '2019-10-05 22:45:55', '2022-08-29 11:08:30', 1, 'Q1607392'),
(35189, 'Madrigal de la Vera', 1190, 'CC', 207, 'ES', 40.14760000, -5.36818000, '2019-10-05 22:45:55', '2022-08-28 18:12:22', 1, 'Q1630737'),
(35190, 'Madrigal de las Altas Torres', 1189, 'AV', 207, 'ES', 41.08968000, -4.99863000, '2019-10-05 22:45:55', '2022-08-29 11:49:56', 1, 'Q1406727'),
(35191, 'Madrigal del Monte', 1146, 'BU', 207, 'ES', 42.14464000, -3.67571000, '2019-10-05 22:45:55', '2022-08-29 11:24:20', 1, 'Q1643684'),
(35192, 'Madrigalejo', 1190, 'CC', 207, 'ES', 39.13858000, -5.62740000, '2019-10-05 22:45:55', '2022-08-28 18:12:22', 1, 'Q1643765'),
(35193, 'Madrigalejo del Monte', 1146, 'BU', 207, 'ES', 42.12442000, -3.72509000, '2019-10-05 22:45:55', '2022-08-29 11:24:20', 1, 'Q1643642'),
(35195, 'Madroñal', 1147, 'SA', 207, 'ES', 40.46407000, -6.06271000, '2019-10-05 22:45:55', '2022-08-29 11:44:51', 1, 'Q582891'),
(35196, 'Madroñera', 1190, 'CC', 207, 'ES', 39.42526000, -5.75568000, '2019-10-05 22:45:55', '2022-08-28 18:12:22', 1, 'Q1613503'),
(35197, 'Maella', 5113, 'Z', 207, 'ES', 41.12251000, 0.13926000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q956524'),
(35198, 'Maello', 1189, 'AV', 207, 'ES', 40.80929000, -4.51186000, '2019-10-05 22:45:55', '2022-08-29 11:49:56', 1, 'Q1606813'),
(35199, 'Magacela', 5092, 'BA', 207, 'ES', 38.89648000, -5.73437000, '2019-10-05 22:45:55', '2022-08-28 18:09:23', 1, 'Q1615390'),
(35200, 'Magallón', 5113, 'Z', 207, 'ES', 41.83248000, -1.45979000, '2019-10-05 22:45:55', '2022-08-29 11:42:54', 1, 'Q1640136'),
(35201, 'Magaluf', 1174, 'PM', 207, 'ES', 39.51110000, 2.53530000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q239017'),
(35202, 'Magaz de Cepeda', 1200, 'LE', 207, 'ES', 42.53967000, -6.07170000, '2019-10-05 22:45:55', '2019-10-05 22:45:55', 1, 'Q141235'),
(35203, 'Magaña', 1208, 'SO', 207, 'ES', 41.90089000, -2.16269000, '2019-10-05 22:45:55', '2022-08-29 11:51:23', 1, 'Q653876'),
(35204, 'Maguilla', 5092, 'BA', 207, 'ES', 38.36667000, -5.83333000, '2019-10-05 22:45:55', '2022-08-28 18:09:23', 1, 'Q1372788'),
(35205, 'Magán', 1205, 'TO', 207, 'ES', 39.96138000, -3.93164000, '2019-10-05 22:45:55', '2022-08-29 11:08:30', 1, 'Q1372788'),
(35206, 'Mahamud', 1146, 'BU', 207, 'ES', 42.11981000, -3.94060000, '2019-10-05 22:45:55', '2022-08-29 11:24:20', 1, 'Q1643651'),
(35207, 'Mahide', 1161, 'ZA', 207, 'ES', 41.86917000, -6.37784000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q1643651'),
(35209, 'Maicas', 5111, 'TE', 207, 'ES', 40.96708000, -0.89041000, '2019-10-05 22:45:56', '2022-08-29 11:29:44', 1, 'Q875871'),
(35210, 'Mainar', 5113, 'Z', 207, 'ES', 41.19275000, -1.30292000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q1639405'),
(35211, 'Maire de Castroponce', 1161, 'ZA', 207, 'ES', 42.11283000, -5.78475000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q1652181'),
(35212, 'Mairena del Alcor', 1193, 'SE', 207, 'ES', 37.37301000, -5.74951000, '2019-10-05 22:45:56', '2022-08-28 19:08:49', 1, 'Q1628379'),
(35213, 'Mairena del Aljarafe', 1193, 'SE', 207, 'ES', 37.34461000, -6.06391000, '2019-10-05 22:45:56', '2022-08-28 19:08:49', 1, 'Q1447391'),
(35214, 'Majadahonda', 1158, 'M', 207, 'ES', 40.47353000, -3.87182000, '2019-10-05 22:45:56', '2022-08-29 12:04:40', 1, 'Q427505'),
(35215, 'Majadas', 1190, 'CC', 207, 'ES', 39.94291000, -5.74589000, '2019-10-05 22:45:56', '2022-08-28 18:12:22', 1, 'Q427505'),
(35216, 'Majaelrayo', 5107, 'GU', 207, 'ES', 41.11260000, -3.30257000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q1649399'),
(35217, 'Maján', 1208, 'SO', 207, 'ES', 41.46888000, -2.30268000, '2019-10-05 22:45:56', '2022-08-29 11:51:23', 1, 'Q830282'),
(35218, 'Malaguilla', 5107, 'GU', 207, 'ES', 40.81956000, -3.25450000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q24011167'),
(35219, 'Malagón', 5105, 'CR', 207, 'ES', 39.16668000, -3.85419000, '2019-10-05 22:45:56', '2022-08-29 11:03:25', 1, 'Q1613270'),
(35220, 'Malanquilla', 5113, 'Z', 207, 'ES', 41.56856000, -1.87481000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q24019920'),
(35221, 'Malcocinado', 5092, 'BA', 207, 'ES', 38.11667000, -5.68333000, '2019-10-05 22:45:56', '2022-08-28 18:09:23', 1, 'Q1446416'),
(35222, 'Maleján', 5113, 'Z', 207, 'ES', 41.82762000, -1.54906000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q1646178'),
(35223, 'Malgrat de Mar', 5102, 'B', 207, 'ES', 41.64662000, 2.74135000, '2019-10-05 22:45:56', '2022-08-29 10:50:01', 1, 'Q1646178'),
(35224, 'Mallén', 5113, 'Z', 207, 'ES', 41.90064000, -1.41994000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q1639432'),
(35225, 'Malpartida', 1147, 'SA', 207, 'ES', 40.76269000, -5.23149000, '2019-10-05 22:45:56', '2022-08-29 11:44:51', 1, 'Q1639432'),
(35226, 'Malpartida de Corneja', 1189, 'AV', 207, 'ES', 40.52208000, -5.35026000, '2019-10-05 22:45:56', '2022-08-29 11:49:56', 1, 'Q1632316'),
(35227, 'Malpartida de Cáceres', 1190, 'CC', 207, 'ES', 39.44664000, -6.50760000, '2019-10-05 22:45:56', '2022-08-28 18:12:22', 1, 'Q946208'),
(35228, 'Malpartida de Plasencia', 1190, 'CC', 207, 'ES', 39.97962000, -6.04609000, '2019-10-05 22:45:56', '2022-08-28 18:12:22', 1, 'Q1643692'),
(35229, 'Malpartida de la Serena', 5092, 'BA', 207, 'ES', 38.67470000, -5.64054000, '2019-10-05 22:45:56', '2022-08-28 18:09:23', 1, 'Q1615324'),
(35232, 'Maluenda', 5113, 'Z', 207, 'ES', 41.28772000, -1.61603000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q1640382'),
(35233, 'Malva', 1161, 'ZA', 207, 'ES', 41.65467000, -5.48657000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q1922240'),
(35234, 'Malón', 5113, 'Z', 207, 'ES', 41.95235000, -1.67199000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q1768945'),
(35235, 'Mamblas', 1189, 'AV', 207, 'ES', 41.01925000, -5.00873000, '2019-10-05 22:45:56', '2022-08-29 11:49:56', 1, 'Q1632369'),
(35236, 'Mambrilla de Castrejón', 1146, 'BU', 207, 'ES', 41.66634000, -3.98448000, '2019-10-05 22:45:56', '2022-08-29 11:24:20', 1, 'Q1630618'),
(35237, 'Mambrillas de Lara', 1146, 'BU', 207, 'ES', 42.09446000, -3.46195000, '2019-10-05 22:45:56', '2022-08-29 11:24:20', 1, 'Q1644733'),
(35238, 'Mamolar', 1146, 'BU', 207, 'ES', 41.92762000, -3.36228000, '2019-10-05 22:45:56', '2022-08-29 11:24:20', 1, 'Q1630597'),
(35239, 'Manacor', 1174, 'PM', 207, 'ES', 39.56964000, 3.20955000, '2019-10-05 22:45:56', '2019-10-05 22:45:56', 1, 'Q1630597'),
(35240, 'Mancera de Abajo', 1147, 'SA', 207, 'ES', 40.83974000, -5.19933000, '2019-10-05 22:45:56', '2022-08-29 11:44:51', 1, 'Q1769823'),
(35241, 'Mancera de Arriba', 1189, 'AV', 207, 'ES', 40.79139000, -5.14772000, '2019-10-05 22:45:56', '2022-08-29 11:49:56', 1, 'Q1632398'),
(35242, 'Mancha Real', 5100, 'J', 207, 'ES', 37.78627000, -3.61226000, '2019-10-05 22:45:56', '2022-08-28 19:04:30', 1, 'Q936886'),
(35243, 'Manchita', 5092, 'BA', 207, 'ES', 38.81404000, -6.02041000, '2019-10-05 22:45:56', '2022-08-28 18:09:23', 1, 'Q1446428'),
(35244, 'Manchones', 5113, 'Z', 207, 'ES', 41.15000000, -1.46667000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q1639285'),
(35245, 'Manciles', 1146, 'BU', 207, 'ES', 42.45728000, -3.94461000, '2019-10-05 22:45:56', '2022-08-29 11:24:20', 1, 'Q1643701'),
(35246, 'Mancor de la Vall', 1174, 'PM', 207, 'ES', 39.74966000, 2.87284000, '2019-10-05 22:45:56', '2019-10-05 22:45:56', 1, 'Q988696'),
(35247, 'Mandayona', 5107, 'GU', 207, 'ES', 40.95610000, -2.75021000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q605073'),
(35248, 'Manganeses de la Lampreana', 1161, 'ZA', 207, 'ES', 41.75085000, -5.71048000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q1769451'),
(35249, 'Manganeses de la Polvorosa', 1161, 'ZA', 207, 'ES', 42.03563000, -5.74694000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q1652096'),
(35250, 'Manilva', 5101, 'MA', 207, 'ES', 36.37645000, -5.25026000, '2019-10-05 22:45:56', '2022-08-28 19:06:53', 1, 'Q913492'),
(35251, 'Manises', 1175, 'V', 207, 'ES', 39.49139000, -0.46349000, '2019-10-05 22:45:56', '2022-08-29 12:05:40', 1, 'Q1648590'),
(35252, 'Manjabálago', 1189, 'AV', 207, 'ES', 40.66441000, -5.07719000, '2019-10-05 22:45:56', '2022-08-29 11:49:56', 1, 'Q1611043'),
(35253, 'Manjarrés', 1171, 'LO', 207, 'ES', 42.39152000, -2.67512000, '2019-10-05 22:45:56', '2022-08-29 12:05:09', 1, 'Q1646003'),
(35254, 'Manlleu', 5102, 'B', 207, 'ES', 42.00228000, 2.28476000, '2019-10-05 22:45:56', '2022-08-29 10:50:01', 1, 'Q24009572'),
(35255, 'Manquillos', 1157, 'P', 207, 'ES', 42.20518000, -4.56841000, '2019-10-05 22:45:56', '2022-08-29 11:45:45', 1, 'Q783867'),
(35256, 'Manresa', 5102, 'B', 207, 'ES', 41.72815000, 1.82399000, '2019-10-05 22:45:56', '2022-08-29 10:50:01', 1, 'Q783867'),
(35257, 'Mansilla Mayor', 1200, 'LE', 207, 'ES', 42.50943000, -5.44271000, '2019-10-05 22:45:56', '2019-10-05 22:45:56', 1, 'Q141177'),
(35258, 'Mansilla de las Mulas', 1200, 'LE', 207, 'ES', 42.49886000, -5.41738000, '2019-10-05 22:45:56', '2019-10-05 22:45:56', 1, 'Q24013155'),
(35259, 'Mantiel', 5107, 'GU', 207, 'ES', 40.61892000, -2.66324000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q1649409'),
(35260, 'Mantinos', 1157, 'P', 207, 'ES', 42.75259000, -4.84213000, '2019-10-05 22:45:56', '2022-08-29 11:45:45', 1, 'Q1918548'),
(35261, 'Manuel', 1175, 'V', 207, 'ES', 39.05059000, -0.48978000, '2019-10-05 22:45:56', '2022-08-29 12:05:40', 1, 'Q1918548'),
(35262, 'Manzanal de Arriba', 1161, 'ZA', 207, 'ES', 41.99217000, -6.44012000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q1777979'),
(35263, 'Manzanal de los Infantes', 1161, 'ZA', 207, 'ES', 42.05428000, -6.38288000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q738050'),
(35264, 'Manzanal del Barco', 1161, 'ZA', 207, 'ES', 41.63501000, -5.94671000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q385842'),
(35265, 'Manzanares', 5105, 'CR', 207, 'ES', 38.99915000, -3.36991000, '2019-10-05 22:45:56', '2022-08-29 11:03:25', 1, 'Q385842'),
(35266, 'Manzanares de Rioja', 1171, 'LO', 207, 'ES', 42.39610000, -2.89590000, '2019-10-05 22:45:56', '2022-08-29 12:05:09', 1, 'Q1646930'),
(35267, 'Manzanares el Real', 1158, 'M', 207, 'ES', 40.72627000, -3.86265000, '2019-10-05 22:45:56', '2022-08-29 12:04:40', 1, 'Q55483'),
(35268, 'Manzaneda', 5091, 'OR', 207, 'ES', 42.30962000, -7.23209000, '2019-10-05 22:45:56', '2022-08-28 17:53:26', 1, 'Q55483'),
(35269, 'Manzaneque', 1205, 'TO', 207, 'ES', 39.63549000, -3.79249000, '2019-10-05 22:45:56', '2022-08-29 11:08:30', 1, 'Q1642083'),
(35270, 'Manzanera', 5111, 'TE', 207, 'ES', 40.05000000, -0.83333000, '2019-10-05 22:45:56', '2022-08-29 11:29:44', 1, 'Q509788'),
(35271, 'Manzanilla', 5099, 'H', 207, 'ES', 37.38987000, -6.43295000, '2019-10-05 22:45:56', '2022-08-28 19:00:43', 1, 'Q629590'),
(35272, 'Manzanillo', 1183, 'VA', 207, 'ES', 41.58617000, -4.18826000, '2019-10-05 22:45:56', '2022-08-29 11:48:45', 1, 'Q1995205'),
(35273, 'Maqueda', 1205, 'TO', 207, 'ES', 40.06614000, -4.37066000, '2019-10-05 22:45:56', '2022-08-29 11:08:30', 1, 'Q1631202'),
(35274, 'Mara', 5113, 'Z', 207, 'ES', 41.28950000, -1.51842000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q949866'),
(35275, 'Maracena', 5098, 'GR', 207, 'ES', 37.20764000, -3.63493000, '2019-10-05 22:45:56', '2022-08-28 18:52:58', 1, 'Q583032'),
(35276, 'Maranchón', 5107, 'GU', 207, 'ES', 41.04754000, -2.20482000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q1654697'),
(35277, 'Marazoleja', 1192, 'SG', 207, 'ES', 40.96086000, -4.33882000, '2019-10-05 22:45:56', '2022-08-29 11:50:43', 1, 'Q1939171'),
(35278, 'Marazuela', 1192, 'SG', 207, 'ES', 40.97912000, -4.36506000, '2019-10-05 22:45:56', '2022-08-29 11:50:42', 1, 'Q1919615'),
(35279, 'Maraña', 1200, 'LE', 207, 'ES', 43.04991000, -5.17726000, '2019-10-05 22:45:56', '2020-05-01 17:23:16', 1, 'Q1919615'),
(35280, 'Marañón', 1204, 'NA', 207, 'ES', 42.62941000, -2.43931000, '2019-10-05 22:45:56', '2022-08-29 12:06:07', 1, 'Q644914'),
(35281, 'Marbella', 5101, 'MA', 207, 'ES', 36.51543000, -4.88583000, '2019-10-05 22:45:56', '2022-08-28 19:06:53', 1, 'Q484799'),
(35282, 'Marchagaz', 1190, 'CC', 207, 'ES', 40.26769000, -6.27485000, '2019-10-05 22:45:56', '2022-08-28 18:12:22', 1, 'Q1643628'),
(35283, 'Marchal', 5098, 'GR', 207, 'ES', 37.29639000, -3.20353000, '2019-10-05 22:45:56', '2022-08-28 18:52:58', 1, 'Q581216'),
(35284, 'Marchamalo', 5107, 'GU', 207, 'ES', 40.66677000, -3.19914000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q1769777'),
(35285, 'Marchena', 1193, 'SE', 207, 'ES', 37.32900000, -5.41681000, '2019-10-05 22:45:56', '2022-08-28 19:08:49', 1, 'Q1769777'),
(35286, 'Marcilla', 1204, 'NA', 207, 'ES', 42.32794000, -1.73714000, '2019-10-05 22:45:56', '2022-08-29 12:06:07', 1, 'Q1636217'),
(35287, 'Marcilla de Campos', 1157, 'P', 207, 'ES', 42.31770000, -4.39670000, '2019-10-05 22:45:56', '2022-08-29 11:45:45', 1, 'Q1907507'),
(35288, 'Margalef', 1203, 'T', 207, 'ES', 41.28496000, 0.75331000, '2019-10-05 22:45:56', '2022-08-29 10:57:33', 1, 'Q1907507'),
(35289, 'Maria de la Salut', 1174, 'PM', 207, 'ES', 39.66306000, 3.07300000, '2019-10-05 22:45:56', '2019-10-05 22:45:56', 1, 'Q669078'),
(35290, 'Mariana', 5106, 'CU', 207, 'ES', 40.16717000, -2.14601000, '2019-10-05 22:45:56', '2022-08-29 11:05:01', 1, 'Q1993921'),
(35291, 'Marinaleda', 1193, 'SE', 207, 'ES', 37.37120000, -4.95949000, '2019-10-05 22:45:56', '2022-08-28 19:08:49', 1, 'Q671610'),
(35292, 'Marines', 1175, 'V', 207, 'ES', 39.74165000, -0.53103000, '2019-10-05 22:45:56', '2022-08-29 12:05:40', 1, 'Q23984049'),
(35293, 'Marjaliza', 1205, 'TO', 207, 'ES', 39.56329000, -3.93499000, '2019-10-05 22:45:56', '2022-08-29 11:08:30', 1, 'Q1641298'),
(35294, 'Markina-Xemein', 5094, 'BI', 207, 'ES', 43.26667000, -2.50000000, '2019-10-05 22:45:56', '2022-08-28 18:25:56', 1, 'Q604810'),
(35295, 'Marmolejo', 5100, 'J', 207, 'ES', 38.04549000, -4.17029000, '2019-10-05 22:45:56', '2022-08-28 19:04:30', 1, 'Q1756694'),
(35296, 'Marracos', 5113, 'Z', 207, 'ES', 42.08826000, -0.77587000, '2019-10-05 22:45:56', '2022-08-29 11:42:55', 1, 'Q1769840'),
(35297, 'Marratxí', 1174, 'PM', 207, 'ES', 39.62142000, 2.72530000, '2019-10-05 22:45:56', '2020-05-01 17:23:15', 1, 'Q1769840'),
(35298, 'Martiago', 1147, 'SA', 207, 'ES', 40.45420000, -6.49010000, '2019-10-05 22:45:56', '2022-08-29 11:44:51', 1, 'Q1769438'),
(35299, 'Martiherrero', 1189, 'AV', 207, 'ES', 40.67392000, -4.78156000, '2019-10-05 22:45:56', '2022-08-29 11:49:56', 1, 'Q1632641'),
(35300, 'Martinamor', 1147, 'SA', 207, 'ES', 40.80676000, -5.59913000, '2019-10-05 22:45:56', '2022-08-29 11:44:51', 1, 'Q1910968'),
(35301, 'Martorell', 5102, 'B', 207, 'ES', 41.47402000, 1.93062000, '2019-10-05 22:45:56', '2022-08-29 10:50:01', 1, 'Q15623'),
(35302, 'Martos', 5100, 'J', 207, 'ES', 37.72107000, -3.97264000, '2019-10-05 22:45:56', '2022-08-28 19:04:30', 1, 'Q24013248'),
(35303, 'Martín Miguel', 1192, 'SG', 207, 'ES', 40.95220000, -4.27166000, '2019-10-05 22:45:56', '2022-08-29 11:50:42', 1, 'Q1948760'),
(35304, 'Martín Muñoz de la Dehesa', 1192, 'SG', 207, 'ES', 41.06652000, -4.68676000, '2019-10-05 22:45:56', '2022-08-29 11:50:42', 1, 'Q1948556'),
(35305, 'Martín Muñoz de las Posadas', 1192, 'SG', 207, 'ES', 40.99546000, -4.59672000, '2019-10-05 22:45:56', '2022-08-29 11:50:42', 1, 'Q1939184'),
(35306, 'Martín de Yeltes', 1147, 'SA', 207, 'ES', 40.77594000, -6.29162000, '2019-10-05 22:45:56', '2022-08-29 11:44:51', 1, 'Q1910944'),
(35307, 'Martín de la Jara', 1193, 'SE', 207, 'ES', 37.10867000, -4.96347000, '2019-10-05 22:45:56', '2022-08-28 19:08:49', 1, 'Q1605621'),
(35308, 'Martín del Río', 5111, 'TE', 207, 'ES', 40.84459000, -0.89549000, '2019-10-05 22:45:56', '2022-08-29 11:29:44', 1, 'Q1650939'),
(35309, 'Martínez', 1189, 'AV', 207, 'ES', 40.63046000, -5.34801000, '2019-10-05 22:45:56', '2022-08-29 11:49:56', 1, 'Q1650939'),
(35310, 'Marugán', 1192, 'SG', 207, 'ES', 40.89943000, -4.38383000, '2019-10-05 22:45:56', '2022-08-29 11:50:42', 1, 'Q1948746'),
(35311, 'Marzales', 1183, 'VA', 207, 'ES', 41.58644000, -5.13455000, '2019-10-05 22:45:56', '2022-08-29 11:48:45', 1, 'Q1908584'),
(35312, 'María', 5095, 'AL', 207, 'ES', 37.71023000, -2.16454000, '2019-10-05 22:45:56', '2022-08-28 18:41:41', 1, 'Q1908584'),
(35313, 'María de Huerva', 5113, 'Z', 207, 'ES', 41.53880000, -0.99615000, '2019-10-05 22:45:56', '2022-08-29 11:42:54', 1, 'Q1640944'),
(35314, 'Marín', 1167, 'PO', 207, 'ES', 42.39145000, -8.70136000, '2019-10-05 22:45:56', '2022-08-28 17:57:54', 1, 'Q1640944'),
(35315, 'Mas de Barberans', 1203, 'T', 207, 'ES', 40.73333000, 0.36667000, '2019-10-05 22:45:56', '2022-08-29 10:57:33', 1, 'Q1640944'),
(35316, 'Mas de las Matas', 5111, 'TE', 207, 'ES', 40.83408000, -0.24292000, '2019-10-05 22:45:56', '2022-08-29 11:29:44', 1, 'Q1651220'),
(35317, 'Masalavés', 1175, 'V', 207, 'ES', 39.14377000, -0.52260000, '2019-10-05 22:45:56', '2022-08-29 12:05:40', 1, 'Q1998512'),
(35318, 'Mascaraque', 1205, 'TO', 207, 'ES', 39.71643000, -3.81254000, '2019-10-05 22:45:56', '2022-08-29 11:08:30', 1, 'Q923358'),
(35319, 'Masdenverge', 1203, 'T', 207, 'ES', 40.71600000, 0.53009000, '2019-10-05 22:45:56', '2022-08-29 10:57:33', 1, 'Q766912'),
(35320, 'Masegosa', 5106, 'CU', 207, 'ES', 40.54689000, -2.02588000, '2019-10-05 22:45:56', '2022-08-29 11:05:01', 1, 'Q1768930'),
(35322, 'Masegoso de Tajuña', 5107, 'GU', 207, 'ES', 40.82552000, -2.69532000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q1654362'),
(35323, 'Maside', 5091, 'OR', 207, 'ES', 42.41031000, -8.02582000, '2019-10-05 22:45:56', '2022-08-28 17:53:26', 1, 'Q1654362'),
(35324, 'Maspalomas', 1185, 'GC', 207, 'ES', 27.76056000, -15.58602000, '2019-10-05 22:45:56', '2022-08-29 12:06:32', 1, 'Q580743'),
(35325, 'Masquefa', 5102, 'B', 207, 'ES', 41.50353000, 1.81136000, '2019-10-05 22:45:56', '2022-08-29 10:50:01', 1, 'Q580743'),
(35326, 'Massamagrell', 1175, 'V', 207, 'ES', 39.56667000, -0.33333000, '2019-10-05 22:45:56', '2022-08-29 12:05:40', 1, 'Q1313044'),
(35327, 'Massanes', 5103, 'GI', 207, 'ES', 41.76536000, 2.65324000, '2019-10-05 22:45:56', '2022-08-29 10:53:16', 1, 'Q13637'),
(35328, 'Masueco', 1147, 'SA', 207, 'ES', 41.20352000, -6.58938000, '2019-10-05 22:45:56', '2022-08-29 11:44:51', 1, 'Q1640520'),
(35329, 'Mata de Alcántara', 1190, 'CC', 207, 'ES', 39.71738000, -6.81825000, '2019-10-05 22:45:56', '2022-08-28 18:12:22', 1, 'Q1640520'),
(35330, 'Mata de Cuéllar', 1192, 'SG', 207, 'ES', 41.39655000, -4.47167000, '2019-10-05 22:45:56', '2022-08-29 11:50:42', 1, 'Q1948843'),
(35331, 'Matabuena', 1192, 'SG', 207, 'ES', 41.09590000, -3.75827000, '2019-10-05 22:45:56', '2022-08-29 11:50:42', 1, 'Q1948843'),
(35332, 'Matadepera', 5102, 'B', 207, 'ES', 41.59886000, 2.02648000, '2019-10-05 22:45:56', '2022-08-29 10:50:01', 1, 'Q1948843'),
(35333, 'Matadeón de los Oteros', 1200, 'LE', 207, 'ES', 42.33776000, -5.36887000, '2019-10-05 22:45:56', '2020-05-01 17:23:16', 1, 'Q141244'),
(35334, 'Matalebreras', 1208, 'SO', 207, 'ES', 41.84116000, -2.04644000, '2019-10-05 22:45:56', '2022-08-29 11:51:23', 1, 'Q834234'),
(35335, 'Matallana de Torío', 1200, 'LE', 207, 'ES', 42.86560000, -5.52034000, '2019-10-05 22:45:56', '2020-05-01 17:23:16', 1, 'Q80143'),
(35336, 'Matamala de Almazán', 1208, 'SO', 207, 'ES', 41.50563000, -2.64122000, '2019-10-05 22:45:56', '2022-08-29 11:51:23', 1, 'Q836175'),
(35337, 'Mataporquera', 1170, 'S', 207, 'ES', 42.87486000, -4.16276000, '2019-10-05 22:45:56', '2019-10-05 22:45:56', 1, 'Q6005750'),
(35338, 'Matapozuelos', 1183, 'VA', 207, 'ES', 41.41423000, -4.79122000, '2019-10-05 22:45:56', '2022-08-29 11:48:45', 1, 'Q1651689'),
(35339, 'Matarrubia', 5107, 'GU', 207, 'ES', 40.86463000, -3.28944000, '2019-10-05 22:45:56', '2022-08-29 11:06:45', 1, 'Q1653820'),
(35340, 'Mataró', 5102, 'B', 207, 'ES', 41.54211000, 2.44450000, '2019-10-05 22:45:56', '2022-08-29 10:50:01', 1, 'Q11492'),
(35341, 'Matet', 5110, 'CS', 207, 'ES', 39.93333000, -0.46667000, '2019-10-05 22:45:56', '2022-08-29 11:26:42', 1, 'Q1983295'),
(35342, 'Matilla de Arzón', 1161, 'ZA', 207, 'ES', 42.10499000, -5.64156000, '2019-10-05 22:45:56', '2022-08-29 11:48:03', 1, 'Q1765170'),
(35343, 'Matilla de los Caños', 1183, 'VA', 207, 'ES', 41.54818000, -4.96761000, '2019-10-05 22:45:56', '2022-08-29 11:48:45', 1, 'Q1765170'),
(35344, 'Matilla de los Caños del Río', 1147, 'SA', 207, 'ES', 40.82539000, -5.94276000, '2019-10-05 22:45:56', '2022-08-29 11:44:51', 1, 'Q389043'),
(35345, 'Matilla la Seca', 1161, 'ZA', 207, 'ES', 41.57935000, -5.50030000, '2019-10-05 22:45:57', '2022-08-29 11:48:03', 1, 'Q1646354'),
(35346, 'Matillas', 5107, 'GU', 207, 'ES', 40.94415000, -2.83590000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q24011161'),
(35347, 'Matute', 1171, 'LO', 207, 'ES', 42.29888000, -2.79564000, '2019-10-05 22:45:57', '2022-08-29 12:05:09', 1, 'Q1646908'),
(35348, 'Mayalde', 1161, 'ZA', 207, 'ES', 41.25080000, -5.79767000, '2019-10-05 22:45:57', '2022-08-29 11:48:03', 1, 'Q1646394'),
(35349, 'Mayorga', 1183, 'VA', 207, 'ES', 42.16687000, -5.26304000, '2019-10-05 22:45:57', '2022-08-29 11:48:45', 1, 'Q62129335'),
(35351, 'Mazaleón', 5111, 'TE', 207, 'ES', 41.05056000, 0.10290000, '2019-10-05 22:45:57', '2022-08-29 11:29:44', 1, 'Q24015085'),
(35352, 'Mazarambroz', 1205, 'TO', 207, 'ES', 39.69411000, -4.01962000, '2019-10-05 22:45:57', '2022-08-29 11:08:30', 1, 'Q1630055'),
(35353, 'Mazarete', 5107, 'GU', 207, 'ES', 41.00086000, -2.15921000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1653684'),
(35354, 'Mazaricos', 5089, 'C', 207, 'ES', 42.94033000, -8.97187000, '2019-10-05 22:45:57', '2022-08-28 13:37:17', 1, 'Q1442814'),
(35355, 'Mazariegos', 1157, 'P', 207, 'ES', 42.02691000, -4.71542000, '2019-10-05 22:45:57', '2022-08-29 11:45:45', 1, 'Q1918869'),
(35356, 'Mazarrón', 1176, 'MU', 207, 'ES', 37.59920000, -1.31493000, '2019-10-05 22:45:57', '2022-08-29 12:05:49', 1, 'Q913277'),
(35357, 'Mazo', 1185, 'GC', 207, 'ES', 28.60906000, -17.77801000, '2019-10-05 22:45:57', '2022-08-29 12:06:32', 1, 'Q23984118'),
(35358, 'Mazuecos', 5107, 'GU', 207, 'ES', 40.26020000, -3.00755000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q24011159'),
(35359, 'Mazuecos de Valdeginate', 1157, 'P', 207, 'ES', 42.16814000, -4.84059000, '2019-10-05 22:45:57', '2022-08-29 11:45:45', 1, 'Q1918560'),
(35360, 'Mazuela', 1146, 'BU', 207, 'ES', 42.20767000, -3.91920000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q1630729'),
(35361, 'Mañaria', 5094, 'BI', 207, 'ES', 43.13819000, -2.66104000, '2019-10-05 22:45:57', '2022-08-28 18:25:56', 1, 'Q913283'),
(35362, 'Mañeru', 1204, 'NA', 207, 'ES', 42.67001000, -1.86297000, '2019-10-05 22:45:57', '2022-08-29 12:06:07', 1, 'Q964849'),
(35363, 'Maó', 1174, 'PM', 207, 'ES', 39.88853000, 4.26583000, '2019-10-05 22:45:57', '2020-05-01 17:23:15', 1, 'Q964849'),
(35364, 'Meaño', 1167, 'PO', 207, 'ES', 42.44661000, -8.78122000, '2019-10-05 22:45:57', '2022-08-28 17:57:54', 1, 'Q1607447'),
(35365, 'Mecerreyes', 1146, 'BU', 207, 'ES', 42.09536000, -3.57393000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q962962'),
(35366, 'Meco', 1158, 'M', 207, 'ES', 40.55274000, -3.32894000, '2019-10-05 22:45:57', '2022-08-29 12:04:40', 1, 'Q962962'),
(35367, 'Medellín', 5092, 'BA', 207, 'ES', 38.96265000, -5.95785000, '2019-10-05 22:45:57', '2022-08-28 18:09:23', 1, 'Q23984122'),
(35368, 'Mediana de Voltoya', 1189, 'AV', 207, 'ES', 40.70104000, -4.56341000, '2019-10-05 22:45:57', '2022-08-29 11:49:56', 1, 'Q1632673'),
(35370, 'Medina de Pomar', 1146, 'BU', 207, 'ES', 42.92938000, -3.48804000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q1614754'),
(35371, 'Medina de Ríoseco', 1183, 'VA', 207, 'ES', 41.88327000, -5.04405000, '2019-10-05 22:45:57', '2022-08-29 11:48:45', 1, 'Q24017025'),
(35372, 'Medina de las Torres', 5092, 'BA', 207, 'ES', 38.33333000, -6.40000000, '2019-10-05 22:45:57', '2022-08-28 18:09:23', 1, 'Q1406716'),
(35373, 'Medina del Campo', 1183, 'VA', 207, 'ES', 41.31239000, -4.91413000, '2019-10-05 22:45:57', '2022-08-29 11:48:45', 1, 'Q24017026'),
(35374, 'Medinaceli', 1208, 'SO', 207, 'ES', 41.17224000, -2.43476000, '2019-10-05 22:45:57', '2022-08-29 11:51:23', 1, 'Q484628'),
(35375, 'Medinilla', 1189, 'AV', 207, 'ES', 40.43967000, -5.61772000, '2019-10-05 22:45:57', '2022-08-29 11:49:56', 1, 'Q484628'),
(35376, 'Mediona', 5102, 'B', 207, 'ES', 41.47798000, 1.61222000, '2019-10-05 22:45:57', '2022-08-29 10:50:01', 1, 'Q686790'),
(35377, 'Medranda', 5107, 'GU', 207, 'ES', 40.98333000, -2.93719000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1389059'),
(35378, 'Medrano', 1171, 'LO', 207, 'ES', 42.38361000, -2.55394000, '2019-10-05 22:45:57', '2022-08-29 12:05:09', 1, 'Q1646962'),
(35379, 'Megeces', 1183, 'VA', 207, 'ES', 41.40828000, -4.56166000, '2019-10-05 22:45:57', '2022-08-29 11:48:45', 1, 'Q1929259'),
(35380, 'Megina', 5107, 'GU', 207, 'ES', 40.63911000, -1.87041000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1653778'),
(35381, 'Meira', 5090, 'LU', 207, 'ES', 43.21337000, -7.29372000, '2019-10-05 22:45:57', '2022-08-28 17:49:36', 1, 'Q1618958'),
(35382, 'Meis', 1167, 'PO', 207, 'ES', 42.50000000, -8.75000000, '2019-10-05 22:45:57', '2022-08-28 17:57:54', 1, 'Q1618958'),
(35383, 'Mejorada', 1205, 'TO', 207, 'ES', 40.00991000, -4.88506000, '2019-10-05 22:45:57', '2022-08-29 11:08:30', 1, 'Q1629664'),
(35384, 'Mejorada del Campo', 1158, 'M', 207, 'ES', 40.39283000, -3.48194000, '2019-10-05 22:45:57', '2022-08-29 12:04:40', 1, 'Q1984852'),
(35385, 'Melgar de Abajo', 1183, 'VA', 207, 'ES', 42.24344000, -5.14216000, '2019-10-05 22:45:57', '2022-08-29 11:48:45', 1, 'Q1984852'),
(35386, 'Melgar de Arriba', 1183, 'VA', 207, 'ES', 42.26806000, -5.09712000, '2019-10-05 22:45:57', '2022-08-29 11:48:45', 1, 'Q1984852'),
(35387, 'Melgar de Fernamental', 1146, 'BU', 207, 'ES', 42.40445000, -4.24484000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q1984852'),
(35388, 'Melgar de Tera', 1161, 'ZA', 207, 'ES', 41.96618000, -6.01390000, '2019-10-05 22:45:57', '2022-08-29 11:48:03', 1, 'Q1984852'),
(35389, 'Melgar de Yuso', 1157, 'P', 207, 'ES', 42.25381000, -4.25394000, '2019-10-05 22:45:57', '2022-08-29 11:45:45', 1, 'Q1984852'),
(35390, 'Meliana', 1175, 'V', 207, 'ES', 39.53333000, -0.33333000, '2019-10-05 22:45:57', '2022-08-29 12:05:40', 1, 'Q1984852'),
(35392, 'Melón', 5091, 'OR', 207, 'ES', 42.26016000, -8.21400000, '2019-10-05 22:45:57', '2022-08-28 17:53:26', 1, 'Q1644993'),
(35393, 'Membibre de la Hoz', 1192, 'SG', 207, 'ES', 41.44958000, -4.09571000, '2019-10-05 22:45:57', '2022-08-29 11:50:42', 1, 'Q1919709'),
(35394, 'Membribe de la Sierra', 1147, 'SA', 207, 'ES', 40.69123000, -5.80568000, '2019-10-05 22:45:57', '2022-08-29 11:44:51', 1, 'Q1911024'),
(35395, 'Membrilla', 5105, 'CR', 207, 'ES', 38.97198000, -3.34330000, '2019-10-05 22:45:57', '2022-08-29 11:03:25', 1, 'Q1642197'),
(35396, 'Membrillera', 5107, 'GU', 207, 'ES', 40.94807000, -2.97969000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q654834'),
(35397, 'Menasalbas', 1205, 'TO', 207, 'ES', 39.63954000, -4.28418000, '2019-10-05 22:45:57', '2022-08-29 11:08:30', 1, 'Q1628818'),
(35398, 'Mendaro', 1191, 'SS', 207, 'ES', 43.25326000, -2.38568000, '2019-10-05 22:45:57', '2022-08-28 18:36:50', 1, 'Q629193'),
(35399, 'Mendavia', 1204, 'NA', 207, 'ES', 42.44335000, -2.20087000, '2019-10-05 22:45:57', '2022-08-29 12:06:07', 1, 'Q1648554'),
(35400, 'Mendexa', 5094, 'BI', 207, 'ES', 43.34590000, -2.48420000, '2019-10-05 22:45:57', '2022-08-28 18:25:56', 1, 'Q1227761'),
(35401, 'Mendigorría', 1204, 'NA', 207, 'ES', 42.62822000, -1.83450000, '2019-10-05 22:45:57', '2022-08-29 12:06:07', 1, 'Q1648110'),
(35402, 'Meneses de Campos', 1157, 'P', 207, 'ES', 41.94142000, -4.91927000, '2019-10-05 22:45:57', '2022-08-29 11:45:45', 1, 'Q24012173'),
(35403, 'Mengabril', 5092, 'BA', 207, 'ES', 38.93554000, -5.93335000, '2019-10-05 22:45:57', '2022-08-28 18:09:23', 1, 'Q1455235'),
(35404, 'Mengamuñoz', 1189, 'AV', 207, 'ES', 40.50006000, -4.99983000, '2019-10-05 22:45:57', '2022-08-29 11:49:56', 1, 'Q1606346'),
(35405, 'Mengibar', 5100, 'J', 207, 'ES', 37.96978000, -3.80884000, '2019-10-05 22:45:57', '2022-08-28 19:04:30', 1, 'Q1645553'),
(35406, 'Mequinensa / Mequinenza', 1177, 'HU', 207, 'ES', 41.37211000, 0.30169000, '2019-10-05 22:45:57', '2022-08-29 12:06:20', 1, 'Q1645553'),
(35407, 'Mercadal', 1174, 'PM', 207, 'ES', 39.99014000, 4.09387000, '2019-10-05 22:45:57', '2019-10-05 22:45:57', 1, 'Q1645553'),
(35408, 'Mesas de Ibor', 1190, 'CC', 207, 'ES', 39.75587000, -5.54637000, '2019-10-05 22:45:57', '2022-08-28 18:12:22', 1, 'Q1644461'),
(35409, 'Mesegar de Corneja', 1189, 'AV', 207, 'ES', 40.50209000, -5.30131000, '2019-10-05 22:45:57', '2022-08-29 11:49:56', 1, 'Q784095'),
(35410, 'Mesia', 5089, 'C', 207, 'ES', 43.10000000, -8.26667000, '2019-10-05 22:45:57', '2022-08-28 13:37:17', 1, 'Q1606528'),
(35411, 'Mesones de Isuela', 5113, 'Z', 207, 'ES', 41.55119000, -1.53858000, '2019-10-05 22:45:57', '2022-08-29 11:42:54', 1, 'Q1650623'),
(35412, 'Mestanza', 5105, 'CR', 207, 'ES', 38.57616000, -4.07096000, '2019-10-05 22:45:57', '2022-08-29 11:03:25', 1, 'Q1658049'),
(35413, 'Metauten', 1204, 'NA', 207, 'ES', 42.67714000, -2.13015000, '2019-10-05 22:45:57', '2022-08-29 12:06:07', 1, 'Q1647918'),
(35414, 'Mezalocha', 5113, 'Z', 207, 'ES', 41.42581000, -1.08276000, '2019-10-05 22:45:57', '2022-08-29 11:42:54', 1, 'Q24019910'),
(35415, 'Mezquita de Jarque', 5111, 'TE', 207, 'ES', 40.72100000, -0.86700000, '2019-10-05 22:45:57', '2022-08-29 11:29:44', 1, 'Q1650810'),
(35416, 'Miajadas', 1190, 'CC', 207, 'ES', 39.15127000, -5.90841000, '2019-10-05 22:45:57', '2022-08-28 18:12:22', 1, 'Q371790'),
(35417, 'Mianos', 5113, 'Z', 207, 'ES', 42.58544000, -0.95478000, '2019-10-05 22:45:57', '2022-08-29 11:42:54', 1, 'Q55842986'),
(35418, 'Micereces de Tera', 1161, 'ZA', 207, 'ES', 41.98886000, -5.87133000, '2019-10-05 22:45:57', '2022-08-29 11:48:03', 1, 'Q1765343'),
(35419, 'Micieces de Ojeda', 1157, 'P', 207, 'ES', 42.69078000, -4.46166000, '2019-10-05 22:45:57', '2022-08-29 11:45:45', 1, 'Q390005'),
(35420, 'Miedes de Atienza', 5107, 'GU', 207, 'ES', 41.26672000, -2.96375000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1644244'),
(35421, 'Miengo', 1170, 'S', 207, 'ES', 43.42861000, -3.99866000, '2019-10-05 22:45:57', '2019-10-05 22:45:57', 1, 'Q1644244'),
(35422, 'Mieza', 1147, 'SA', 207, 'ES', 41.16424000, -6.69137000, '2019-10-05 22:45:57', '2022-08-29 11:44:51', 1, 'Q1770832'),
(35423, 'Miguel Esteban', 1205, 'TO', 207, 'ES', 39.52448000, -3.07618000, '2019-10-05 22:45:57', '2022-08-29 11:08:30', 1, 'Q24017329'),
(35424, 'Miguelturra', 5105, 'CR', 207, 'ES', 38.96442000, -3.89047000, '2019-10-05 22:45:57', '2022-08-29 11:03:25', 1, 'Q1632172'),
(35425, 'Migueláñez', 1192, 'SG', 207, 'ES', 41.12155000, -4.36410000, '2019-10-05 22:45:57', '2022-08-29 11:50:42', 1, 'Q784611'),
(35426, 'Mijares', 1189, 'AV', 207, 'ES', 40.29655000, -4.83651000, '2019-10-05 22:45:57', '2022-08-29 11:49:56', 1, 'Q784611'),
(35427, 'Mijas', 5101, 'MA', 207, 'ES', 36.59575000, -4.63728000, '2019-10-05 22:45:57', '2022-08-28 19:06:53', 1, 'Q492744'),
(35428, 'Milagro', 1204, 'NA', 207, 'ES', 42.24160000, -1.76588000, '2019-10-05 22:45:57', '2022-08-29 12:06:07', 1, 'Q644537'),
(35429, 'Milagros', 1146, 'BU', 207, 'ES', 41.57500000, -3.69907000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q1634531'),
(35430, 'Millana', 5107, 'GU', 207, 'ES', 40.50717000, -2.57116000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1654826'),
(35431, 'Millanes', 1190, 'CC', 207, 'ES', 39.84917000, -5.58079000, '2019-10-05 22:45:57', '2022-08-28 18:12:22', 1, 'Q650873'),
(35432, 'Millares', 1175, 'V', 207, 'ES', 39.25000000, -0.76667000, '2019-10-05 22:45:57', '2022-08-29 12:05:40', 1, 'Q34254'),
(35433, 'Millena', 5108, 'A', 207, 'ES', 38.73082000, -0.36274000, '2019-10-05 22:45:57', '2022-08-29 11:15:47', 1, 'Q1770111'),
(35434, 'Milles de la Polvorosa', 1161, 'ZA', 207, 'ES', 41.92339000, -5.73309000, '2019-10-05 22:45:57', '2022-08-29 11:48:03', 1, 'Q974299'),
(35435, 'Milmarcos', 5107, 'GU', 207, 'ES', 41.08622000, -1.87652000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q24011153'),
(35437, 'Minglanilla', 5106, 'CU', 207, 'ES', 39.53333000, -1.60000000, '2019-10-05 22:45:57', '2022-08-29 11:05:01', 1, 'Q682640'),
(35438, 'Mingorría', 1189, 'AV', 207, 'ES', 40.75192000, -4.66583000, '2019-10-05 22:45:57', '2022-08-29 11:49:56', 1, 'Q764795'),
(35439, 'Mira', 5106, 'CU', 207, 'ES', 39.71667000, -1.43333000, '2019-10-05 22:45:57', '2022-08-29 11:05:01', 1, 'Q1902619'),
(35440, 'Mirabel', 1190, 'CC', 207, 'ES', 39.86230000, -6.23274000, '2019-10-05 22:45:57', '2022-08-28 18:12:22', 1, 'Q1764584'),
(35441, 'Mirabueno', 5107, 'GU', 207, 'ES', 40.94479000, -2.72438000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1654496'),
(35442, 'Miraflores de la Sierra', 1158, 'M', 207, 'ES', 40.81550000, -3.76213000, '2019-10-05 22:45:57', '2022-08-29 12:04:40', 1, 'Q24012725'),
(35443, 'Mirafuentes', 1204, 'NA', 207, 'ES', 42.62297000, -2.27966000, '2019-10-05 22:45:57', '2022-08-29 12:06:07', 1, 'Q1648015'),
(35444, 'Miralcamp', 5104, 'L', 207, 'ES', 41.60516000, 0.87987000, '2019-10-05 22:45:57', '2022-08-29 10:55:25', 1, 'Q1769676'),
(35445, 'Miralrío', 5107, 'GU', 207, 'ES', 40.88901000, -2.94340000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1654832'),
(35446, 'Miramar', 1175, 'V', 207, 'ES', 38.95036000, -0.14007000, '2019-10-05 22:45:57', '2022-08-29 12:05:40', 1, 'Q1654832'),
(35447, 'Mirambel', 5111, 'TE', 207, 'ES', 40.58733000, -0.34266000, '2019-10-05 22:45:57', '2022-08-29 11:29:44', 1, 'Q1645435'),
(35448, 'Miranda de Arga', 1204, 'NA', 207, 'ES', 42.48337000, -1.82759000, '2019-10-05 22:45:57', '2022-08-29 12:06:07', 1, 'Q1641891'),
(35449, 'Miranda de Azán', 1147, 'SA', 207, 'ES', 40.88752000, -5.68182000, '2019-10-05 22:45:57', '2022-08-29 11:44:51', 1, 'Q1911063'),
(35450, 'Miranda de Ebro', 1146, 'BU', 207, 'ES', 42.68650000, -2.94695000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q24010459'),
(35451, 'Miranda del Castañar', 1147, 'SA', 207, 'ES', 40.48448000, -5.99581000, '2019-10-05 22:45:57', '2022-08-29 11:44:51', 1, 'Q1769783'),
(35452, 'Mirandilla', 5092, 'BA', 207, 'ES', 39.00200000, -6.28893000, '2019-10-05 22:45:57', '2022-08-28 18:09:23', 1, 'Q527689'),
(35453, 'Miraveche', 1146, 'BU', 207, 'ES', 42.67396000, -3.19947000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q1643895'),
(35454, 'Miravet', 1203, 'T', 207, 'ES', 41.03930000, 0.59665000, '2019-10-05 22:45:57', '2022-08-29 10:57:33', 1, 'Q962371'),
(35455, 'Mironcillo', 1189, 'AV', 207, 'ES', 40.55435000, -4.82449000, '2019-10-05 22:45:57', '2022-08-29 11:49:56', 1, 'Q23994751'),
(35456, 'Mislata', 1175, 'V', 207, 'ES', 39.47523000, -0.41825000, '2019-10-05 22:45:57', '2022-08-29 12:05:40', 1, 'Q12442'),
(35457, 'Miño', 5089, 'C', 207, 'ES', 43.35000000, -8.20000000, '2019-10-05 22:45:57', '2022-08-28 13:37:17', 1, 'Q12442'),
(35458, 'Miño de San Esteban', 1208, 'SO', 207, 'ES', 41.53584000, -3.34579000, '2019-10-05 22:45:57', '2022-08-29 11:51:23', 1, 'Q835851'),
(35459, 'Moaña', 1167, 'PO', 207, 'ES', 42.27807000, -8.73921000, '2019-10-05 22:45:57', '2022-08-28 17:57:54', 1, 'Q1617430'),
(35460, 'Mocejón', 1205, 'TO', 207, 'ES', 39.93934000, -3.91716000, '2019-10-05 22:45:57', '2022-08-29 11:08:30', 1, 'Q24017328'),
(35461, 'Mochales', 5107, 'GU', 207, 'ES', 41.09647000, -2.01560000, '2019-10-05 22:45:57', '2022-08-29 11:06:45', 1, 'Q1654316'),
(35462, 'Moclinejo', 5101, 'MA', 207, 'ES', 36.77134000, -4.25514000, '2019-10-05 22:45:57', '2022-08-28 19:06:53', 1, 'Q13152'),
(35463, 'Moclín', 5098, 'GR', 207, 'ES', 37.33959000, -3.78651000, '2019-10-05 22:45:57', '2022-08-28 18:52:58', 1, 'Q593637'),
(35464, 'Modúbar de la Emparedada', 1146, 'BU', 207, 'ES', 42.26118000, -3.65965000, '2019-10-05 22:45:57', '2022-08-29 11:24:20', 1, 'Q1645207'),
(35465, 'Moeche', 5089, 'C', 207, 'ES', 43.55000000, -8.01667000, '2019-10-05 22:45:57', '2022-08-28 13:37:17', 1, 'Q1645207'),
(35466, 'Mogarraz', 1147, 'SA', 207, 'ES', 40.49264000, -6.05327000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q1910893'),
(35467, 'Mogente', 1175, 'V', 207, 'ES', 38.87598000, -0.75150000, '2019-10-05 22:45:58', '2022-08-29 12:05:40', 1, 'Q23984222'),
(35468, 'Moguer', 5099, 'H', 207, 'ES', 37.27559000, -6.83851000, '2019-10-05 22:45:58', '2022-08-28 19:00:43', 1, 'Q785888'),
(35469, 'Mogán', 1185, 'GC', 207, 'ES', 27.88385000, -15.72538000, '2019-10-05 22:45:58', '2022-08-29 12:06:32', 1, 'Q785888'),
(35470, 'Mohedas de la Jara', 1205, 'TO', 207, 'ES', 39.60417000, -5.14247000, '2019-10-05 22:45:58', '2022-08-29 11:08:30', 1, 'Q1641713'),
(35471, 'Mohernando', 5107, 'GU', 207, 'ES', 40.80129000, -3.17234000, '2019-10-05 22:45:58', '2022-08-29 11:06:45', 1, 'Q1654773'),
(35472, 'Moià', 5102, 'B', 207, 'ES', 41.81112000, 2.09839000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q16702'),
(35473, 'Mojacar', 5095, 'AL', 207, 'ES', 37.14020000, -1.85102000, '2019-10-05 22:45:58', '2022-08-28 18:41:41', 1, 'Q496968'),
(35474, 'Mojados', 1183, 'VA', 207, 'ES', 41.43237000, -4.66490000, '2019-10-05 22:45:58', '2022-08-29 11:48:45', 1, 'Q1651697'),
(35475, 'Molacillos', 1161, 'ZA', 207, 'ES', 41.58269000, -5.66046000, '2019-10-05 22:45:58', '2022-08-29 11:48:03', 1, 'Q1765299'),
(35476, 'Molezuelas de la Carballeda', 1161, 'ZA', 207, 'ES', 42.08166000, -6.18723000, '2019-10-05 22:45:58', '2022-08-29 11:48:03', 1, 'Q1653133'),
(35477, 'Molina de Aragón', 5107, 'GU', 207, 'ES', 40.84358000, -1.88762000, '2019-10-05 22:45:58', '2022-08-29 11:06:45', 1, 'Q24011146'),
(35478, 'Molina de Segura', 1176, 'MU', 207, 'ES', 38.05456000, -1.20763000, '2019-10-05 22:45:58', '2022-08-29 12:05:49', 1, 'Q917883'),
(35479, 'Molinaseca', 1200, 'LE', 207, 'ES', 42.53829000, -6.51997000, '2019-10-05 22:45:58', '2019-10-05 22:45:58', 1, 'Q44873'),
(35481, 'Molinillo', 1147, 'SA', 207, 'ES', 40.46898000, -5.94493000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q44873'),
(35482, 'Molinos', 5111, 'TE', 207, 'ES', 40.82100000, -0.45017000, '2019-10-05 22:45:58', '2022-08-29 11:29:44', 1, 'Q44873'),
(35483, 'Molinos de Duero', 1208, 'SO', 207, 'ES', 41.88603000, -2.78682000, '2019-10-05 22:45:58', '2022-08-29 11:51:23', 1, 'Q985457'),
(35484, 'Molins de Rei', 5102, 'B', 207, 'ES', 41.41667000, 2.01667000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q985457'),
(35485, 'Molledo', 1170, 'S', 207, 'ES', 43.14974000, -4.04239000, '2019-10-05 22:45:58', '2019-10-05 22:45:58', 1, 'Q985457'),
(35486, 'Mollerussa', 5104, 'L', 207, 'ES', 41.63333000, 0.90000000, '2019-10-05 22:45:58', '2022-08-29 10:55:25', 1, 'Q985457'),
(35487, 'Mollet de Peralada', 5103, 'GI', 207, 'ES', 42.35959000, 3.00034000, '2019-10-05 22:45:58', '2022-08-29 10:53:16', 1, 'Q11355'),
(35488, 'Mollet del Vallès', 5102, 'B', 207, 'ES', 41.54026000, 2.21306000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q11355'),
(35489, 'Mollina', 5101, 'MA', 207, 'ES', 37.12534000, -4.65686000, '2019-10-05 22:45:58', '2022-08-28 19:06:53', 1, 'Q1630374'),
(35490, 'Molvízar', 5098, 'GR', 207, 'ES', 36.78592000, -3.60783000, '2019-10-05 22:45:58', '2022-08-28 18:52:58', 1, 'Q578183'),
(35491, 'Mombeltrán', 1189, 'AV', 207, 'ES', 40.25987000, -5.01749000, '2019-10-05 22:45:58', '2022-08-29 11:49:56', 1, 'Q1445660'),
(35492, 'Momblona', 1208, 'SO', 207, 'ES', 41.44420000, -2.34660000, '2019-10-05 22:45:58', '2022-08-29 11:51:23', 1, 'Q832817'),
(35493, 'Mombuey', 1161, 'ZA', 207, 'ES', 42.02283000, -6.33027000, '2019-10-05 22:45:58', '2022-08-29 11:48:03', 1, 'Q1765192'),
(35494, 'Monachil', 5098, 'GR', 207, 'ES', 37.13320000, -3.53724000, '2019-10-05 22:45:58', '2022-08-28 18:52:58', 1, 'Q593620'),
(35495, 'Monasterio', 5107, 'GU', 207, 'ES', 40.98547000, -3.09711000, '2019-10-05 22:45:58', '2022-08-29 11:06:45', 1, 'Q686549'),
(35496, 'Monasterio de Rodilla', 1146, 'BU', 207, 'ES', 42.45775000, -3.46965000, '2019-10-05 22:45:58', '2022-08-29 11:24:20', 1, 'Q1765206'),
(35497, 'Monasterio de Vega', 1183, 'VA', 207, 'ES', 42.23015000, -5.18095000, '2019-10-05 22:45:58', '2022-08-29 11:48:45', 1, 'Q1921039'),
(35498, 'Monasterio de la Sierra', 1146, 'BU', 207, 'ES', 42.05180000, -3.19314000, '2019-10-05 22:45:58', '2022-08-29 11:24:20', 1, 'Q613630'),
(35499, 'Moncada', 1175, 'V', 207, 'ES', 39.54555000, -0.39551000, '2019-10-05 22:45:58', '2022-08-29 12:05:40', 1, 'Q613630'),
(35500, 'Moncalvillo', 1146, 'BU', 207, 'ES', 41.95413000, -3.19886000, '2019-10-05 22:45:58', '2022-08-29 11:24:20', 1, 'Q1649205'),
(35501, 'Moncloa-Aravaca', 1158, 'M', 207, 'ES', 40.43547000, -3.73170000, '2019-10-05 22:45:58', '2022-08-29 12:04:40', 1, 'Q2017682'),
(35502, 'Moncofa', 5110, 'CS', 207, 'ES', 39.80907000, -0.14701000, '2019-10-05 22:45:58', '2022-08-29 11:26:42', 1, 'Q2017682'),
(35503, 'Monda', 5101, 'MA', 207, 'ES', 36.63027000, -4.83192000, '2019-10-05 22:45:58', '2022-08-28 19:06:53', 1, 'Q1624023'),
(35504, 'Mondariz', 1167, 'PO', 207, 'ES', 42.23110000, -8.45839000, '2019-10-05 22:45:58', '2022-08-28 17:57:54', 1, 'Q1624023'),
(35505, 'Mondariz-Balneario', 1167, 'PO', 207, 'ES', 42.22686000, -8.46728000, '2019-10-05 22:45:58', '2022-08-28 17:57:54', 1, 'Q23994672'),
(35506, 'Mondoñedo', 5090, 'LU', 207, 'ES', 43.40000000, -7.40000000, '2019-10-05 22:45:58', '2022-08-28 17:49:36', 1, 'Q23994676'),
(35507, 'Mondéjar', 5107, 'GU', 207, 'ES', 40.32095000, -3.10686000, '2019-10-05 22:45:58', '2022-08-29 11:06:45', 1, 'Q946761'),
(35508, 'Monegrillo', 5113, 'Z', 207, 'ES', 41.63856000, -0.41570000, '2019-10-05 22:45:58', '2022-08-29 11:42:54', 1, 'Q24019908'),
(35509, 'Monesterio', 5092, 'BA', 207, 'ES', 38.08333000, -6.26667000, '2019-10-05 22:45:58', '2022-08-28 18:09:23', 1, 'Q733580'),
(35510, 'Moneva', 5113, 'Z', 207, 'ES', 41.12817000, -0.83591000, '2019-10-05 22:45:58', '2022-08-29 11:42:54', 1, 'Q1639514'),
(35511, 'Monfarracinos', 1161, 'ZA', 207, 'ES', 41.55468000, -5.70795000, '2019-10-05 22:45:58', '2022-08-29 11:48:03', 1, 'Q1652360'),
(35512, 'Monfero', 5089, 'C', 207, 'ES', 43.33333000, -8.01667000, '2019-10-05 22:45:58', '2022-08-28 13:37:17', 1, 'Q1652360'),
(35513, 'Monforte de Lemos', 5090, 'LU', 207, 'ES', 42.52165000, -7.51422000, '2019-10-05 22:45:58', '2022-08-28 17:49:36', 1, 'Q24012806'),
(35514, 'Monforte de Moyuela', 5111, 'TE', 207, 'ES', 41.05512000, -1.01411000, '2019-10-05 22:45:58', '2022-08-29 11:29:44', 1, 'Q1650923'),
(35515, 'Monforte de la Sierra', 1147, 'SA', 207, 'ES', 40.48264000, -6.05671000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q1768879'),
(35516, 'Monforte del Cid', 5108, 'A', 207, 'ES', 38.38027000, -0.72850000, '2019-10-05 22:45:58', '2022-08-29 11:15:47', 1, 'Q1769358'),
(35517, 'Monistrol de Montserrat', 5102, 'B', 207, 'ES', 41.61667000, 1.85000000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q1769358'),
(35518, 'Monleras', 1147, 'SA', 207, 'ES', 41.18704000, -6.22622000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q1911000'),
(35519, 'Monleón', 1147, 'SA', 207, 'ES', 40.58192000, -5.84312000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q1768753'),
(35520, 'Monreal', 1204, 'NA', 207, 'ES', 42.70421000, -1.50785000, '2019-10-05 22:45:58', '2022-08-29 12:06:07', 1, 'Q1914401'),
(35521, 'Monreal de Ariza', 5113, 'Z', 207, 'ES', 41.29165000, -2.10493000, '2019-10-05 22:45:58', '2022-08-29 11:42:54', 1, 'Q1650635'),
(35522, 'Monreal del Campo', 5111, 'TE', 207, 'ES', 40.78800000, -1.35541000, '2019-10-05 22:45:58', '2022-08-29 11:29:44', 1, 'Q1644430'),
(35523, 'Monreal del Llano', 5106, 'CU', 207, 'ES', 39.56888000, -2.76046000, '2019-10-05 22:45:58', '2022-08-29 11:05:01', 1, 'Q541302'),
(35524, 'Monroyo', 5111, 'TE', 207, 'ES', 40.78775000, -0.03550000, '2019-10-05 22:45:58', '2022-08-29 11:29:44', 1, 'Q1651239'),
(35525, 'Monsagro', 1147, 'SA', 207, 'ES', 40.50343000, -6.27110000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q1769428'),
(35526, 'Monsalupe', 1189, 'AV', 207, 'ES', 40.76953000, -4.78131000, '2019-10-05 22:45:58', '2022-08-29 11:49:56', 1, 'Q1611004'),
(35527, 'Monserrat', 1175, 'V', 207, 'ES', 39.36667000, -0.60000000, '2019-10-05 22:45:58', '2022-08-29 12:05:40', 1, 'Q23984302'),
(35528, 'Mont-roig del Camp', 1203, 'T', 207, 'ES', 41.08675000, 0.95925000, '2019-10-05 22:45:58', '2022-08-29 10:57:33', 1, 'Q929794'),
(35529, 'Montaberner', 1175, 'V', 207, 'ES', 38.89021000, -0.49582000, '2019-10-05 22:45:58', '2022-08-29 12:05:40', 1, 'Q23984303'),
(35530, 'Montalbanejo', 5106, 'CU', 207, 'ES', 39.73369000, -2.49911000, '2019-10-05 22:45:58', '2022-08-29 11:05:01', 1, 'Q1647572'),
(35531, 'Montalbo', 5106, 'CU', 207, 'ES', 39.87994000, -2.67038000, '2019-10-05 22:45:58', '2022-08-29 11:05:01', 1, 'Q1647659'),
(35532, 'Montalbán', 5111, 'TE', 207, 'ES', 40.83278000, -0.80178000, '2019-10-05 22:45:58', '2022-08-29 11:29:44', 1, 'Q1650927'),
(35533, 'Montalbán de Córdoba', 5097, 'CO', 207, 'ES', 37.57996000, -4.74935000, '2019-10-05 22:45:58', '2022-08-28 18:49:38', 1, 'Q1443804'),
(35535, 'Montamarta', 1161, 'ZA', 207, 'ES', 41.64724000, -5.80402000, '2019-10-05 22:45:58', '2022-08-29 11:48:03', 1, 'Q1765337'),
(35536, 'Montanejos', 5110, 'CS', 207, 'ES', 40.06667000, -0.51667000, '2019-10-05 22:45:58', '2022-08-29 11:26:42', 1, 'Q1768950'),
(35537, 'Montarrón', 5107, 'GU', 207, 'ES', 40.90675000, -3.11495000, '2019-10-05 22:45:58', '2022-08-29 11:06:45', 1, 'Q1653837'),
(35539, 'Montblanc', 1203, 'T', 207, 'ES', 41.37636000, 1.16163000, '2019-10-05 22:45:58', '2022-08-29 10:57:33', 1, 'Q1653837'),
(35540, 'Montcada i Reixac', 5102, 'B', 207, 'ES', 41.48333000, 2.18333000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q1653837'),
(35541, 'Monteagudo de las Salinas', 5106, 'CU', 207, 'ES', 39.80000000, -1.90000000, '2019-10-05 22:45:58', '2022-08-29 11:05:01', 1, 'Q1752014'),
(35542, 'Monteagudo de las Vicarías', 1208, 'SO', 207, 'ES', 41.36485000, -2.16960000, '2019-10-05 22:45:58', '2022-08-29 11:51:23', 1, 'Q24015322'),
(35543, 'Monteagudo del Castillo', 5111, 'TE', 207, 'ES', 40.45656000, -0.81781000, '2019-10-05 22:45:58', '2022-08-29 11:29:44', 1, 'Q1651011'),
(35545, 'Montearagón', 1205, 'TO', 207, 'ES', 39.96425000, -4.63214000, '2019-10-05 22:45:58', '2022-08-29 11:08:30', 1, 'Q1641360'),
(35546, 'Montecanal', 1177, 'HU', 207, 'ES', 41.62965000, -0.93873000, '2019-10-05 22:45:58', '2022-08-29 12:06:20', 1, 'Q13049643'),
(35547, 'Montederramo', 5091, 'OR', 207, 'ES', 42.27554000, -7.50138000, '2019-10-05 22:45:58', '2022-08-28 17:53:26', 1, 'Q1606231'),
(35548, 'Montefrío', 5098, 'GR', 207, 'ES', 37.32308000, -4.00898000, '2019-10-05 22:45:58', '2022-08-28 18:52:58', 1, 'Q593696'),
(35549, 'Montehermoso', 1190, 'CC', 207, 'ES', 40.08796000, -6.34984000, '2019-10-05 22:45:58', '2022-08-28 18:12:22', 1, 'Q43713'),
(35550, 'Montejaque', 5101, 'MA', 207, 'ES', 36.73684000, -5.24990000, '2019-10-05 22:45:58', '2022-08-28 19:06:53', 1, 'Q842247'),
(35551, 'Montejicar', 5098, 'GR', 207, 'ES', 37.57223000, -3.50527000, '2019-10-05 22:45:58', '2022-08-28 18:52:58', 1, 'Q593505'),
(35552, 'Montejo', 1147, 'SA', 207, 'ES', 40.63246000, -5.62287000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q942920'),
(35553, 'Montejo de Arévalo', 1192, 'SG', 207, 'ES', 41.14027000, -4.66414000, '2019-10-05 22:45:58', '2022-08-29 11:50:42', 1, 'Q940935'),
(35554, 'Montejo de Tiermes', 1208, 'SO', 207, 'ES', 41.36838000, -3.20016000, '2019-10-05 22:45:58', '2022-08-29 11:51:23', 1, 'Q837348'),
(35555, 'Montejo de la Sierra', 1158, 'M', 207, 'ES', 41.05955000, -3.52993000, '2019-10-05 22:45:58', '2022-08-29 12:04:40', 1, 'Q1772215'),
(35556, 'Montejo de la Vega de la Serrezuela', 1192, 'SG', 207, 'ES', 41.55032000, -3.65406000, '2019-10-05 22:45:58', '2022-08-29 11:50:42', 1, 'Q1919785'),
(35557, 'Montellano', 1193, 'SE', 207, 'ES', 36.99531000, -5.57145000, '2019-10-05 22:45:58', '2022-08-28 19:08:49', 1, 'Q1605609'),
(35558, 'Montemayor', 5097, 'CO', 207, 'ES', 37.64790000, -4.69779000, '2019-10-05 22:45:58', '2022-08-28 18:49:38', 1, 'Q1606495'),
(35559, 'Montemayor de Pililla', 1183, 'VA', 207, 'ES', 41.50874000, -4.45745000, '2019-10-05 22:45:58', '2022-08-29 11:48:45', 1, 'Q1606495'),
(35560, 'Montemayor del Río', 1147, 'SA', 207, 'ES', 40.34836000, -5.89427000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q1910888'),
(35561, 'Montemolín', 5092, 'BA', 207, 'ES', 38.15000000, -6.20000000, '2019-10-05 22:45:58', '2022-08-28 18:09:23', 1, 'Q1372801'),
(35562, 'Montenegro de Cameros', 1208, 'SO', 207, 'ES', 42.08924000, -2.75406000, '2019-10-05 22:45:58', '2022-08-29 11:51:23', 1, 'Q834963'),
(35563, 'Monterde', 5113, 'Z', 207, 'ES', 41.17434000, -1.73505000, '2019-10-05 22:45:58', '2022-08-29 11:42:54', 1, 'Q23994715'),
(35564, 'Monterde de Albarracín', 5111, 'TE', 207, 'ES', 40.49708000, -1.49216000, '2019-10-05 22:45:58', '2022-08-29 11:29:44', 1, 'Q1650883'),
(35565, 'Monterroso', 5090, 'LU', 207, 'ES', 42.79250000, -7.83425000, '2019-10-05 22:45:58', '2022-08-28 17:49:36', 1, 'Q1122290'),
(35566, 'Monterrubio', 1192, 'SG', 207, 'ES', 40.84914000, -4.35001000, '2019-10-05 22:45:58', '2022-08-29 11:50:42', 1, 'Q1939232'),
(35567, 'Monterrubio de Armuña', 1147, 'SA', 207, 'ES', 41.02651000, -5.64389000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q936575'),
(35568, 'Monterrubio de la Serena', 5092, 'BA', 207, 'ES', 38.58876000, -5.44569000, '2019-10-05 22:45:58', '2022-08-28 18:09:23', 1, 'Q587935'),
(35569, 'Monterrubio de la Sierra', 1147, 'SA', 207, 'ES', 40.75592000, -5.69329000, '2019-10-05 22:45:58', '2022-08-29 11:44:51', 1, 'Q513531'),
(35570, 'Montesa', 1175, 'V', 207, 'ES', 38.95030000, -0.65200000, '2019-10-05 22:45:58', '2022-08-29 12:05:40', 1, 'Q513531'),
(35571, 'Montesclaros', 1205, 'TO', 207, 'ES', 40.10646000, -4.93849000, '2019-10-05 22:45:58', '2022-08-29 11:08:30', 1, 'Q24017325'),
(35572, 'Montferri', 1203, 'T', 207, 'ES', 41.26545000, 1.36517000, '2019-10-05 22:45:58', '2022-08-29 10:57:33', 1, 'Q24017325'),
(35573, 'Montgat', 5102, 'B', 207, 'ES', 41.46859000, 2.28001000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q12022'),
(35574, 'Montichelvo', 1175, 'V', 207, 'ES', 38.89129000, -0.34123000, '2019-10-05 22:45:58', '2022-08-29 12:05:40', 1, 'Q2264698'),
(35575, 'Montiel', 5105, 'CR', 207, 'ES', 38.69802000, -2.86441000, '2019-10-05 22:45:58', '2022-08-29 11:03:25', 1, 'Q1610078'),
(35576, 'Montijo', 5092, 'BA', 207, 'ES', 38.90839000, -6.61785000, '2019-10-05 22:45:58', '2022-08-28 18:09:23', 1, 'Q1610078'),
(35577, 'Montilla', 5097, 'CO', 207, 'ES', 37.58627000, -4.63805000, '2019-10-05 22:45:58', '2022-08-28 18:49:38', 1, 'Q13156'),
(35578, 'Montillana', 5098, 'GR', 207, 'ES', 37.50168000, -3.67368000, '2019-10-05 22:45:58', '2022-08-28 18:52:58', 1, 'Q593679'),
(35579, 'Montizón', 5100, 'J', 207, 'ES', 38.34249000, -3.10404000, '2019-10-05 22:45:58', '2022-08-28 19:04:30', 1, 'Q945226'),
(35580, 'Montmeló', 5102, 'B', 207, 'ES', 41.55002000, 2.24190000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q945226'),
(35581, 'Montorio', 1146, 'BU', 207, 'ES', 42.58469000, -3.77724000, '2019-10-05 22:45:58', '2022-08-29 11:24:20', 1, 'Q945226'),
(35582, 'Montornès del Vallès', 5102, 'B', 207, 'ES', 41.54206000, 2.26748000, '2019-10-05 22:45:58', '2022-08-29 10:50:01', 1, 'Q15433'),
(35583, 'Montoro', 5097, 'CO', 207, 'ES', 38.02409000, -4.38340000, '2019-10-05 22:45:58', '2022-08-28 18:49:38', 1, 'Q1375851'),
(35584, 'Montroy', 1175, 'V', 207, 'ES', 39.33333000, -0.61667000, '2019-10-05 22:45:58', '2022-08-29 12:05:40', 1, 'Q1989384');

