INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(94031, 'Dridu', 4743, 'IL', 181, 'RO', 44.70000000, 26.45000000, '2019-10-05 23:15:16', '2019-10-05 23:15:16', 1, 'Q773407'),
(94032, 'Dr<PERSON><PERSON>-<PERSON><PERSON>', 4751, 'MH', 181, 'RO', 44.62693000, 22.65288000, '2019-10-05 23:15:16', '2019-10-05 23:15:16', 1, 'Q773407'),
(94033, '<PERSON><PERSON><PERSON><PERSON>', 4752, 'VS', 181, 'RO', 46.80146000, 28.13264000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q2298068'),
(94034, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 4728, 'TR', 181, 'RO', 44.21667000, 24.98333000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q12102408'),
(94035, '<PERSON><PERSON>c<PERSON><PERSON>i', 4728, 'TR', 181, 'R<PERSON>', 44.22676000, 24.98542000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q2720592'),
(94036, 'Dr<PERSON>ganu-O<PERSON>i', 4722, 'AG', 181, 'R<PERSON>', 44.93333000, 24.71667000, '2019-10-05 23:15:16', '2020-05-01 17:23:08', 1, 'Q12102354'),
(94037, 'Drăgeşti', 4723, 'BH', 181, 'RO', 46.88333000, 22.13333000, '2019-10-05 23:15:16', '2020-05-01 17:23:08', 1, 'Q4683543'),
(94038, 'Drăghiceni', 4738, 'OT', 181, 'RO', 44.13333000, 24.25000000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q2719280'),
(94039, 'Drăghici', 4722, 'AG', 181, 'RO', 45.12865000, 25.04508000, '2019-10-05 23:15:16', '2020-05-01 17:23:08', 1, 'Q12102394'),
(94040, 'Drăghinești', 4728, 'TR', 181, 'RO', 44.43093000, 25.42608000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q12102392'),
(94041, 'Drăgoeşti', 4757, 'VL', 181, 'RO', 44.81667000, 24.30000000, '2019-10-05 23:15:16', '2020-05-01 17:23:12', 1, 'Q2180423'),
(94042, 'Drăgoeşti-Snagov', 4743, 'IL', 181, 'RO', 44.56667000, 26.53333000, '2019-10-05 23:15:16', '2020-05-01 17:23:10', 1, 'Q2180423'),
(94043, 'Drăgoești', 4743, 'IL', 181, 'RO', 44.56786000, 26.53950000, '2019-10-05 23:15:16', '2020-05-01 17:23:10', 1, 'Q12102384'),
(94044, 'Drăgoiești', 4720, 'SV', 181, 'RO', 47.55000000, 26.08333000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q910381'),
(94045, 'Drăgoteşti', 4742, 'DJ', 181, 'RO', 44.25000000, 24.10000000, '2019-10-05 23:15:16', '2020-05-01 17:23:09', 1, 'Q1847408'),
(94046, 'Drăgoteşti', 4750, 'GJ', 181, 'RO', 44.80000000, 23.16667000, '2019-10-05 23:15:16', '2020-05-01 17:23:10', 1, 'Q2717501'),
(94047, 'Drăgugești', 4744, 'BC', 181, 'RO', 46.36796000, 26.77433000, '2019-10-05 23:15:16', '2020-05-01 17:23:08', 1, 'Q12102385'),
(94048, 'Drăguşeni', 4747, 'GL', 181, 'RO', 45.96667000, 27.75000000, '2019-10-05 23:15:16', '2020-05-01 17:23:09', 1, 'Q910394'),
(94049, 'Drăguşeni', 4740, 'BT', 181, 'RO', 48.01667000, 26.81667000, '2019-10-05 23:15:16', '2020-05-01 17:23:08', 1, 'Q2534846'),
(94050, 'Drăguşeni', 4720, 'SV', 181, 'RO', 47.28333000, 26.48333000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q910405'),
(94051, 'Drăguţeşti', 4750, 'GJ', 181, 'RO', 44.96667000, 23.23333000, '2019-10-05 23:15:16', '2020-05-01 17:23:10', 1, 'Q2717762'),
(94052, 'Drăguș', 4759, 'BV', 181, 'RO', 45.76146000, 24.77906000, '2019-10-05 23:15:16', '2020-05-01 17:23:08', 1, 'Q15244381'),
(94053, 'Drăgușeni', 4735, 'IS', 181, 'RO', 46.90116000, 27.51260000, '2019-10-05 23:15:16', '2020-05-01 17:23:10', 1, 'Q2718529'),
(94054, 'Drăgușeni', 4746, 'SM', 181, 'RO', 47.90541000, 23.07780000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q754234'),
(94055, 'Drăgăești Ungureni', 4745, 'DB', 181, 'RO', 44.94557000, 25.31048000, '2019-10-05 23:15:16', '2020-05-01 17:23:09', 1, 'Q12102372'),
(94056, 'Drăgăneasa', 4729, 'PH', 181, 'RO', 45.10467000, 25.68138000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q12102365'),
(94057, 'Drăgăneşti', 4729, 'PH', 181, 'RO', 44.81667000, 26.30000000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q12102361'),
(94058, 'Drăgăneşti', 4747, 'GL', 181, 'RO', 45.78333000, 27.46667000, '2019-10-05 23:15:16', '2020-05-01 17:23:09', 1, 'Q2538641'),
(94059, 'Drăgăneşti', 4723, 'BH', 181, 'RO', 46.61667000, 22.38333000, '2019-10-05 23:15:16', '2020-05-01 17:23:08', 1, 'Q1082425'),
(94060, 'Drăgăneşti de Vede', 4728, 'TR', 181, 'RO', 44.13333000, 25.05000000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q2721062'),
(94061, 'Drăgăneşti-Olt', 4738, 'OT', 181, 'RO', 44.16667000, 24.53333000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q2721062'),
(94062, 'Drăgăneşti-Vlaşca', 4728, 'TR', 181, 'RO', 44.10139000, 25.59806000, '2019-10-05 23:15:16', '2020-05-01 17:23:11', 1, 'Q12102357'),
(94063, 'Drăgănești', 4731, 'NT', 181, 'RO', 47.30886000, 26.40551000, '2019-10-05 23:15:16', '2020-05-01 17:23:10', 1, 'Q2292684'),
(94064, 'Drăgăşani', 4757, 'VL', 181, 'RO', 44.65000000, 24.26667000, '2019-10-05 23:15:16', '2020-05-01 17:23:12', 1, 'Q2292684'),
(94065, 'Drănic', 4742, 'DJ', 181, 'RO', 44.05519000, 23.84678000, '2019-10-05 23:15:16', '2020-05-01 17:23:09', 1, 'Q2468491'),
(94066, 'Dubova', 4751, 'MH', 181, 'RO', 44.61667000, 22.26667000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q910564'),
(94067, 'Duda', 4752, 'VS', 181, 'RO', 46.75250000, 28.03722000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12102722'),
(94068, 'Dudașu', 4751, 'MH', 181, 'RO', 44.65253000, 22.69683000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q12102726'),
(94069, 'Dudeşti', 4736, 'BR', 181, 'RO', 44.90000000, 27.43333000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q13542466'),
(94070, 'Dudeştii Vechi', 4748, 'TM', 181, 'RO', 46.05000000, 20.48333000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q768926'),
(94071, 'Dudeștii Noi', 4748, 'TM', 181, 'RO', 45.83989000, 21.10215000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q949293'),
(94072, 'Dudu', 4728, 'TR', 181, 'RO', 43.97897000, 24.67786000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12102757'),
(94073, 'Dulceşti', 4731, 'NT', 181, 'RO', 46.96667000, 26.76667000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q2540541'),
(94074, 'Dulcești', 4737, 'CT', 181, 'RO', 43.90506000, 28.54796000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q568052'),
(94075, 'Dumbrava', 4748, 'TM', 181, 'RO', 45.81667000, 22.11667000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q1096093'),
(94076, 'Dumbrava', 4735, 'IS', 181, 'RO', 47.07578000, 27.55174000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12102849'),
(94077, 'Dumbrava', 4729, 'PH', 181, 'RO', 44.88333000, 26.18333000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q14070464'),
(94078, 'Dumbrava', 4746, 'SM', 181, 'RO', 47.84418000, 23.08060000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12102848'),
(94079, 'Dumbrava', 4744, 'BC', 181, 'RO', 46.30969000, 26.89087000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12102836'),
(94080, 'Dumbrava', 4731, 'NT', 181, 'RO', 47.21620000, 26.46309000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12102842'),
(94081, 'Dumbrava Roşie', 4731, 'NT', 181, 'RO', 46.88333000, 26.43333000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q911095'),
(94082, 'Dumbrava de Sus', 4751, 'MH', 181, 'RO', 44.51667000, 23.11667000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q911095'),
(94083, 'Dumbrăveni', 4737, 'CT', 181, 'RO', 43.93333000, 27.98333000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q911106'),
(94084, 'Dumbrăveni', 4755, 'SB', 181, 'RO', 46.23333000, 24.56667000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q911106'),
(94085, 'Dumbrăveni', 4720, 'SV', 181, 'RO', 47.65000000, 26.41667000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q911099'),
(94086, 'Dumbrăveni', 4758, 'VN', 181, 'RO', 45.53333000, 27.11667000, '2019-10-05 23:15:17', '2020-05-01 17:23:12', 1, 'Q13965077'),
(94087, 'Dumbrăveşti', 4729, 'PH', 181, 'RO', 45.08333000, 26.00000000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q14070574'),
(94088, 'Dumbrăviţa', 4759, 'BV', 181, 'RO', 45.76667000, 25.43333000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q1081319'),
(94089, 'Dumbrăviţa', 4760, 'MM', 181, 'RO', 47.60000000, 23.65000000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q1079485'),
(94090, 'Dumbrăviţa', 4748, 'TM', 181, 'RO', 45.79694000, 21.24250000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q1044083'),
(94091, 'Dumbrăvița', 4735, 'IS', 181, 'RO', 47.25483000, 26.84169000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q12102867'),
(94092, 'Dumbrăvița', 4720, 'SV', 181, 'RO', 47.39845000, 26.34578000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q12102865'),
(94093, 'Dumbrăvița', 4740, 'BT', 181, 'RO', 48.01900000, 26.42724000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q12102864'),
(94094, 'Dumeni', 4740, 'BT', 181, 'RO', 48.00724000, 26.54287000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12102870'),
(94095, 'Dumeşti', 4752, 'VS', 181, 'RO', 46.85000000, 27.28333000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q2546984'),
(94096, 'Dumeşti', 4735, 'IS', 181, 'RO', 47.18333000, 27.35000000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q2718831'),
(94097, 'Dumitra', 4733, 'BN', 181, 'RO', 47.21667000, 24.48333000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q1188933'),
(94098, 'Dumitrești', 4738, 'OT', 181, 'RO', 44.68370000, 24.31628000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q12102887'),
(94099, 'Dumitrești', 4758, 'VN', 181, 'RO', 45.55243000, 26.92727000, '2019-10-05 23:15:17', '2020-05-01 17:23:12', 1, 'Q13966908'),
(94100, 'Dumitrița', 4733, 'BN', 181, 'RO', 47.07143000, 24.62579000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q1110621'),
(94101, 'Dunăreni', 4737, 'CT', 181, 'RO', 44.20463000, 27.79058000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12102913'),
(94102, 'Durneşti', 4740, 'BT', 181, 'RO', 47.76667000, 27.10000000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q2716070'),
(94103, 'Dâlga-Gară', 4732, 'CL', 181, 'RO', 44.43333000, 27.05000000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12101058'),
(94104, 'Dâmbovicioara', 4745, 'DB', 181, 'RO', 44.67569000, 25.55374000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12101078'),
(94105, 'Dâmbovicioara', 4722, 'AG', 181, 'RO', 45.45000000, 25.23333000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q2348441'),
(94106, 'Dâmbroca', 4756, 'BZ', 181, 'RO', 45.12328000, 26.95578000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12101075'),
(94107, 'Dâmbu', 4729, 'PH', 181, 'RO', 45.04533000, 25.88854000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q12101075'),
(94108, 'Dângeni', 4740, 'BT', 181, 'RO', 47.85000000, 26.96667000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q2715876'),
(94109, 'Dârjiu', 4749, 'HR', 181, 'RO', 46.20000000, 25.20000000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q1011624'),
(94110, 'Dârlos', 4755, 'SB', 181, 'RO', 46.18333000, 24.40000000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q902160'),
(94111, 'Dârvari', 4751, 'MH', 181, 'RO', 44.20000000, 23.05000000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q2465426'),
(94112, 'Dârvari', 4725, 'IF', 181, 'RO', 44.42108000, 25.87989000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q12101155'),
(94113, 'Dârza', 4745, 'DB', 181, 'RO', 44.60175000, 25.94956000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12101166'),
(94114, 'Dăbuleni', 4742, 'DJ', 181, 'RO', 43.80000000, 24.08333000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12101166'),
(94115, 'Dăbâca', 4734, 'CJ', 181, 'RO', 46.98333000, 23.66667000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q1018474'),
(94116, 'Dăeni', 4727, 'TL', 181, 'RO', 44.83333000, 28.11667000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q987294'),
(94117, 'Dăeşti', 4757, 'VL', 181, 'RO', 44.96667000, 24.13333000, '2019-10-05 23:15:17', '2020-05-01 17:23:12', 1, 'Q12100655'),
(94119, 'Dămieneşti', 4744, 'BC', 181, 'RO', 46.73333000, 26.98333000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q15633787'),
(94120, 'Dămoc', 4744, 'BC', 181, 'RO', 46.46667000, 26.86667000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q15633787'),
(94121, 'Dămuc', 4731, 'NT', 181, 'RO', 46.80000000, 25.90000000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q901465'),
(94122, 'Dămăcușeni', 4760, 'MM', 181, 'RO', 47.45709000, 23.90914000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q389739'),
(94123, 'Dănceu', 4751, 'MH', 181, 'RO', 44.36416000, 22.72098000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q12100120'),
(94124, 'Dănciuleşti', 4750, 'GJ', 181, 'RO', 44.73333000, 23.75000000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q2717656'),
(94125, 'Dăneasa', 4738, 'OT', 181, 'RO', 44.15000000, 24.56667000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q2719592'),
(94126, 'Dăneşti', 4752, 'VS', 181, 'RO', 46.85000000, 27.66667000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q2547539'),
(94127, 'Dăneşti', 4750, 'GJ', 181, 'RO', 44.96667000, 23.33333000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q2717843'),
(94128, 'Dăneşti', 4749, 'HR', 181, 'RO', 46.51667000, 25.75000000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q875814'),
(94129, 'Dăneţi', 4742, 'DJ', 181, 'RO', 43.98333000, 24.05000000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q2285798'),
(94130, 'Dărmăneşti', 4720, 'SV', 181, 'RO', 47.73333000, 26.15000000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q1041659'),
(94131, 'Dărmăneşti', 4744, 'BC', 181, 'RO', 46.36667000, 26.48333000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q1041659'),
(94132, 'Dărmăneşti', 4745, 'DB', 181, 'RO', 44.91667000, 25.78333000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12100549'),
(94133, 'Dărmăneşti', 4722, 'AG', 181, 'RO', 45.01667000, 24.90000000, '2019-10-05 23:15:17', '2020-05-01 17:23:08', 1, 'Q3394124'),
(94134, 'Dărăşti-Ilfov', 4725, 'IF', 181, 'RO', 44.30889000, 26.01833000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q1653572'),
(94135, 'Dărăști-Vlașca', 4726, 'GR', 181, 'RO', 44.29401000, 26.01119000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12100283'),
(94136, 'Dăscălești', 4756, 'BZ', 181, 'RO', 45.40626000, 27.25039000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q12100577'),
(94137, 'Eforie Nord', 4737, 'CT', 181, 'RO', 44.06530000, 28.63211000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q1296448'),
(94138, 'Eforie Sud', 4737, 'CT', 181, 'RO', 44.02294000, 28.64943000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q734438'),
(94139, 'Eftimie Murgu', 4753, 'CS', 181, 'RO', 44.90028000, 22.09278000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q75716'),
(94140, 'Eliseni', 4749, 'HR', 181, 'RO', 46.29235000, 24.93724000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q714072'),
(94141, 'Emil Racoviță', 4752, 'VS', 181, 'RO', 46.87147000, 27.67937000, '2019-10-05 23:15:17', '2020-05-01 17:23:11', 1, 'Q12103873'),
(94142, 'Enisala', 4727, 'TL', 181, 'RO', 44.87956000, 28.81872000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q989764'),
(94143, 'Epureni', 4752, 'VS', 181, 'RO', 46.70769000, 28.03829000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q12073387'),
(94144, 'Erbiceni', 4735, 'IS', 181, 'RO', 47.26667000, 27.23333000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q2621224'),
(94145, 'Estelnic', 4754, 'CV', 181, 'RO', 46.10445000, 26.21262000, '2019-10-05 23:15:17', '2019-10-05 23:15:17', 1, 'Q991762'),
(94146, 'Ezeriş', 4753, 'CS', 181, 'RO', 45.41083000, 21.88583000, '2019-10-05 23:15:17', '2020-05-01 17:23:09', 1, 'Q75528'),
(94147, 'Eşelniţa', 4751, 'MH', 181, 'RO', 44.69917000, 22.36222000, '2019-10-05 23:15:17', '2020-05-01 17:23:10', 1, 'Q586861'),
(94148, 'Falaștoaca', 4726, 'GR', 181, 'RO', 44.19098000, 26.18475000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q12165224'),
(94149, 'Falcău', 4720, 'SV', 181, 'RO', 47.91087000, 25.46908000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q12165227'),
(94150, 'Faraoaní', 4744, 'BC', 181, 'RO', 46.43333000, 26.90000000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q1215007'),
(94151, 'Feisa', 4724, 'AB', 181, 'RO', 46.22608000, 24.14492000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q251676'),
(94152, 'Feldioara', 4759, 'BV', 181, 'RO', 45.81667000, 25.60000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q15244452'),
(94153, 'Feldru', 4733, 'BN', 181, 'RO', 47.28333000, 24.60000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q775498'),
(94154, 'Feleacu', 4734, 'CJ', 181, 'RO', 46.71667000, 23.61667000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1012571'),
(94155, 'Feliceni', 4749, 'HR', 181, 'RO', 46.26667000, 25.26667000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q999077'),
(94156, 'Felnac', 4739, 'AR', 181, 'RO', 46.11667000, 21.15000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1006225'),
(94157, 'Feneș', 4724, 'AB', 181, 'RO', 46.10431000, 23.29511000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q752223'),
(94158, 'Ferestrău-Oituz', 4744, 'BC', 181, 'RO', 46.20249000, 26.57629000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q12165609'),
(94159, 'Ferești', 4752, 'VS', 181, 'RO', 46.78179000, 27.70093000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q3294218'),
(94160, 'Feteşti', 4743, 'IL', 181, 'RO', 44.38333000, 27.83333000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q3294218'),
(94161, 'Fetești', 4735, 'IS', 181, 'RO', 47.40932000, 26.90885000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q12165675'),
(94162, 'Fetești-Gară', 4743, 'IL', 181, 'RO', 44.41960000, 27.82536000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q12165675'),
(94163, 'Fibiș', 4748, 'TM', 181, 'RO', 45.97280000, 21.42355000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q24328'),
(94164, 'Fieni', 4745, 'DB', 181, 'RO', 45.13333000, 25.41667000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q16898237'),
(94165, 'Fierbinţi-Târg', 4743, 'IL', 181, 'RO', 44.70000000, 26.35000000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q16898346'),
(94166, 'Fierbinți', 4745, 'DB', 181, 'RO', 44.50747000, 25.28760000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q12165081'),
(94167, 'Fierbinții de Jos', 4743, 'IL', 181, 'RO', 44.69523000, 26.39629000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q12165082'),
(94168, 'Fierbinții de Sus', 4743, 'IL', 181, 'RO', 44.68186000, 26.40200000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q12165083'),
(94169, 'Fildu de Jos', 4741, 'SJ', 181, 'RO', 46.93333000, 23.06667000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1188619'),
(94170, 'Filia', 4754, 'CV', 181, 'RO', 46.14409000, 25.62140000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q713350'),
(94171, 'Filiaşi', 4742, 'DJ', 181, 'RO', 44.55000000, 23.51667000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q713350'),
(94172, 'Filiaș', 4749, 'HR', 181, 'RO', 46.27075000, 25.02120000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q713350'),
(94173, 'Filioara', 4731, 'NT', 181, 'RO', 47.16040000, 26.27920000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q12166606'),
(94174, 'Filipeni', 4744, 'BC', 181, 'RO', 46.53333000, 27.18333000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q15637442'),
(94175, 'Filipeşti', 4744, 'BC', 181, 'RO', 46.75000000, 26.88333000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q15637494'),
(94176, 'Filipeştii de Pădure', 4729, 'PH', 181, 'RO', 45.00000000, 25.75000000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q14070781'),
(94177, 'Filipeştii de Târg', 4729, 'PH', 181, 'RO', 44.98333000, 25.78333000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q14071145'),
(94178, 'Finiş', 4723, 'BH', 181, 'RO', 46.63333000, 22.31667000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q1093260'),
(94179, 'Finta Mare', 4745, 'DB', 181, 'RO', 44.80000000, 25.80000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q12166688'),
(94180, 'Fitioneşti', 4758, 'VN', 181, 'RO', 45.98333000, 27.05000000, '2019-10-05 23:15:18', '2020-05-01 17:23:12', 1, 'Q13968266'),
(94181, 'Fizeşu Gherlii', 4734, 'CJ', 181, 'RO', 47.01667000, 23.98333000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q212296'),
(94182, 'Fizeș', 4753, 'CS', 181, 'RO', 45.36403000, 21.59517000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q554400'),
(94183, 'Fișer', 4759, 'BV', 181, 'RO', 46.07912000, 25.15103000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q3894304'),
(94184, 'Floreşti', 4751, 'MH', 181, 'RO', 44.76306000, 22.94778000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q2316523'),
(94185, 'Floreşti', 4734, 'CJ', 181, 'RO', 46.74574000, 23.49375000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q913997'),
(94186, 'Floreşti', 4729, 'PH', 181, 'RO', 45.05000000, 25.78333000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q14071362'),
(94187, 'Florești', 4726, 'GR', 181, 'RO', 44.51313000, 25.69658000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q12165794'),
(94188, 'Florești', 4750, 'GJ', 181, 'RO', 44.61918000, 23.52838000, '2019-10-05 23:15:18', '2020-05-01 17:23:10', 1, 'Q12165793'),
(94189, 'Florica', 4756, 'BZ', 181, 'RO', 44.90802000, 26.76627000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q2288678'),
(94190, 'Floroaia', 4754, 'CV', 181, 'RO', 45.69497000, 26.01563000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q262890'),
(94191, 'Flămânzi', 4740, 'BT', 181, 'RO', 47.55000000, 26.86667000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q16898136'),
(94192, 'Focuri', 4735, 'IS', 181, 'RO', 47.35000000, 27.21667000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q683517'),
(94193, 'Focșani', 4758, 'VN', 181, 'RO', 45.70000000, 27.18333000, '2019-10-05 23:15:18', '2020-05-01 17:23:12', 1, 'Q683517'),
(94194, 'Foeni', 4748, 'TM', 181, 'RO', 45.49861000, 20.87583000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1004774'),
(94195, 'Foglaş', 4723, 'BH', 181, 'RO', 47.25000000, 22.53333000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q1002511'),
(94196, 'Foieni', 4746, 'SM', 181, 'RO', 47.70000000, 22.38333000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q942181'),
(94197, 'Folteşti', 4747, 'GL', 181, 'RO', 45.75000000, 28.05000000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q2716893'),
(94198, 'Forotic', 4753, 'CS', 181, 'RO', 45.23028000, 21.58750000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1003036'),
(94199, 'Forău', 4723, 'BH', 181, 'RO', 46.71070000, 22.20113000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q285073'),
(94200, 'Forăşti', 4720, 'SV', 181, 'RO', 47.35000000, 26.46667000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q2606053'),
(94201, 'Frasin', 4720, 'SV', 181, 'RO', 47.53333000, 25.80000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q2606053'),
(94202, 'Frata', 4734, 'CJ', 181, 'RO', 46.70000000, 24.05000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1076080'),
(94203, 'Fratoștița', 4742, 'DJ', 181, 'RO', 44.58671000, 23.57044000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q12166186'),
(94204, 'Frecăţei', 4727, 'TL', 181, 'RO', 45.10000000, 28.66667000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q1003713'),
(94205, 'Frecăţei', 4736, 'BR', 181, 'RO', 44.90000000, 28.11667000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q2780030'),
(94206, 'Frumoasa', 4749, 'HR', 181, 'RO', 46.45000000, 25.85000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q876125'),
(94207, 'Frumoasa', 4728, 'TR', 181, 'RO', 43.80000000, 25.46667000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q2720948'),
(94208, 'Frumoasa', 4744, 'BC', 181, 'RO', 46.66298000, 26.54196000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q751886'),
(94209, 'Frumosu', 4720, 'SV', 181, 'RO', 47.61667000, 25.65000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1004126'),
(94210, 'Frumuşani', 4732, 'CL', 181, 'RO', 44.29611000, 26.32556000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q12166271'),
(94211, 'Frumuşica', 4740, 'BT', 181, 'RO', 47.53333000, 26.90000000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q1040244'),
(94212, 'Frumuşiţa', 4747, 'GL', 181, 'RO', 45.66667000, 28.06667000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q2289406'),
(94213, 'Frumușelu', 4744, 'BC', 181, 'RO', 46.27796000, 27.35203000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q12166274'),
(94214, 'Frumușeni', 4739, 'AR', 181, 'RO', 46.10252000, 21.46167000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q1082237'),
(94215, 'Fruntișeni', 4752, 'VS', 181, 'RO', 46.20634000, 27.76904000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q2297500'),
(94216, 'Frunzi', 4733, 'BN', 181, 'RO', 46.90000000, 24.33333000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q2297500'),
(94217, 'Frăsinet', 4732, 'CL', 181, 'RO', 44.31667000, 26.80000000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q12166217'),
(94218, 'Frăsinet', 4728, 'TR', 181, 'RO', 44.18038000, 25.38238000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q2549505'),
(94219, 'Frăteşti', 4726, 'GR', 181, 'RO', 43.96667000, 25.96667000, '2019-10-05 23:15:18', '2020-05-01 17:23:09', 1, 'Q2289410'),
(94220, 'Frătăuţii Noi', 4720, 'SV', 181, 'RO', 47.94257000, 25.84465000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q2606135'),
(94221, 'Frătăuţii Vechi', 4720, 'SV', 181, 'RO', 47.90000000, 25.88333000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q269099'),
(94222, 'Fulga de Jos', 4729, 'PH', 181, 'RO', 44.88152000, 26.44258000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q12166327'),
(94223, 'Fulga de Sus', 4729, 'PH', 181, 'RO', 44.90000000, 26.45000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q12166326'),
(94224, 'Fundata', 4759, 'BV', 181, 'RO', 45.45000000, 25.30000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q15244495'),
(94225, 'Fundeni', 4732, 'CL', 181, 'RO', 44.38333000, 26.35000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q2716722'),
(94226, 'Fundeni', 4725, 'IF', 181, 'RO', 44.45892000, 26.16565000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q12166351'),
(94227, 'Fundeni', 4747, 'GL', 181, 'RO', 45.54123000, 27.54056000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1159690'),
(94228, 'Fundeni', 4756, 'BZ', 181, 'RO', 45.29141000, 26.88028000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q12166352'),
(94229, 'Fundu Moldovei', 4720, 'SV', 181, 'RO', 47.53333000, 25.40000000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q253340'),
(94230, 'Fundu Răcăciuni', 4744, 'BC', 181, 'RO', 46.35864000, 26.88987000, '2019-10-05 23:15:18', '2020-05-01 17:23:08', 1, 'Q1126460'),
(94231, 'Fundulea', 4732, 'CL', 181, 'RO', 44.46667000, 26.51667000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q1126460'),
(94232, 'Furcenii Noi', 4747, 'GL', 181, 'RO', 45.81118000, 27.33954000, '2019-10-05 23:15:18', '2019-10-05 23:15:18', 1, 'Q12166430'),
(94233, 'Furculești', 4728, 'TR', 181, 'RO', 43.87202000, 25.14285000, '2019-10-05 23:15:18', '2020-05-01 17:23:11', 1, 'Q2720630'),
(94234, 'Furtunești', 4756, 'BZ', 181, 'RO', 45.45940000, 26.39760000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q12166429'),
(94235, 'Fântâna Mare', 4720, 'SV', 181, 'RO', 47.41432000, 26.30015000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2720646'),
(94236, 'Fântânele', 4720, 'SV', 181, 'RO', 47.57463000, 26.53277000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2720004'),
(94237, 'Fântânele', 4728, 'TR', 181, 'RO', 43.72104000, 25.29265000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2549516'),
(94238, 'Fântânele', 4739, 'AR', 181, 'RO', 46.12370000, 21.38420000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q770159'),
(94239, 'Fântânele', 4737, 'CT', 181, 'RO', 44.61278000, 28.57655000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q1004656'),
(94240, 'Fântânele', 4729, 'PH', 181, 'RO', 45.00700000, 26.37720000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q14070679'),
(94241, 'Fântânele', 4735, 'IS', 181, 'RO', 47.41498000, 27.18066000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q2540437'),
(94242, 'Fântânele', 4747, 'GL', 181, 'RO', 45.70152000, 28.00354000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q12165735'),
(94243, 'Fântânele', 4745, 'DB', 181, 'RO', 44.71667000, 25.86667000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q12165736'),
(94244, 'Fârdea', 4748, 'TM', 181, 'RO', 45.73333000, 22.16667000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q1000362'),
(94245, 'Fârliug', 4753, 'CS', 181, 'RO', 45.48833000, 21.84944000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q256526'),
(94246, 'Fârţăneşti', 4747, 'GL', 181, 'RO', 45.81667000, 27.98333000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q2289275'),
(94247, 'Fâstâci', 4752, 'VS', 181, 'RO', 46.73058000, 27.44901000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q12165755'),
(94248, 'Făclia', 4737, 'CT', 181, 'RO', 44.28039000, 28.10583000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q12165539'),
(94249, 'Făcăeni', 4743, 'IL', 181, 'RO', 44.56667000, 27.90000000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q304495'),
(94250, 'Făcăi', 4742, 'DJ', 181, 'RO', 44.27356000, 23.82029000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q1483859'),
(94251, 'Făget', 4748, 'TM', 181, 'RO', 45.85000000, 22.18333000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q1483859'),
(94252, 'Făget', 4744, 'BC', 181, 'RO', 46.58891000, 26.05349000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q12165377'),
(94253, 'Făgetu', 4729, 'PH', 181, 'RO', 45.13668000, 26.02018000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q12165386'),
(94254, 'Făgetu', 4722, 'AG', 181, 'RO', 44.90616000, 24.91159000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q12165384'),
(94255, 'Făgeţelu', 4738, 'OT', 181, 'RO', 44.78333000, 24.53333000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q388519'),
(94256, 'Făgăraș', 4759, 'BV', 181, 'RO', 45.85000000, 24.96667000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q388519'),
(94257, 'Fălciu', 4752, 'VS', 181, 'RO', 46.29611000, 28.14083000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2662039'),
(94258, 'Fălcoiu', 4738, 'OT', 181, 'RO', 44.23333000, 24.36667000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2718880'),
(94259, 'Fălticeni', 4720, 'SV', 181, 'RO', 47.45000000, 26.30000000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2718880'),
(94260, 'Fărcaşa', 4731, 'NT', 181, 'RO', 47.16667000, 25.83333000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q2719100'),
(94261, 'Fărcaşa', 4760, 'MM', 181, 'RO', 47.58333000, 23.33333000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q996446'),
(94262, 'Fărcaşele', 4738, 'OT', 181, 'RO', 44.15000000, 24.43333000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2292713'),
(94263, 'Fărcaș', 4742, 'DJ', 181, 'RO', 44.60000000, 23.75000000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q2606831'),
(94264, 'Fărcașu de Jos', 4738, 'OT', 181, 'RO', 44.13539000, 24.45901000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q12165617'),
(94265, 'Fărcăşeşti', 4750, 'GJ', 181, 'RO', 44.86667000, 23.20000000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q943100'),
(94266, 'Fărcășeni', 4735, 'IS', 181, 'RO', 47.15987000, 26.89615000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q12165619'),
(94267, 'Fărtăţeşti', 4757, 'VL', 181, 'RO', 44.78333000, 24.00000000, '2019-10-05 23:15:19', '2020-05-01 17:23:12', 1, 'Q2297655'),
(94268, 'Fărău', 4724, 'AB', 181, 'RO', 46.33333000, 24.01667000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q936137'),
(94269, 'Făurei', 4736, 'BR', 181, 'RO', 45.08333000, 27.26667000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q936137'),
(94270, 'Făurei', 4731, 'NT', 181, 'RO', 46.91667000, 26.68333000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q2541988'),
(94271, 'Făureşti', 4757, 'VL', 181, 'RO', 44.56667000, 24.01667000, '2019-10-05 23:15:19', '2020-05-01 17:23:12', 1, 'Q2341339'),
(94272, 'Galaţi', 4747, 'GL', 181, 'RO', 45.43687000, 28.05028000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q2341339'),
(94273, 'Galaţii Bistriţei', 4733, 'BN', 181, 'RO', 46.98333000, 24.40000000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q1007430'),
(94274, 'Galbeni', 4744, 'BC', 181, 'RO', 46.45661000, 26.94664000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q1324527'),
(94275, 'Galbenu', 4736, 'BR', 181, 'RO', 45.21667000, 27.16667000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12094025'),
(94276, 'Galda de Jos', 4724, 'AB', 181, 'RO', 46.18333000, 23.61667000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q664479'),
(94277, 'Galicea', 4757, 'VL', 181, 'RO', 44.91667000, 24.28333000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q2109923'),
(94278, 'Galicea Mare', 4742, 'DJ', 181, 'RO', 44.10000000, 23.30000000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q2466000'),
(94279, 'Galiciuica', 4742, 'DJ', 181, 'RO', 44.10324000, 23.39067000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12728783'),
(94280, 'Galoșpetreu', 4723, 'BH', 181, 'RO', 47.48345000, 22.21730000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q721582'),
(94281, 'Galșa', 4739, 'AR', 181, 'RO', 46.28810000, 21.65137000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q724045'),
(94282, 'Garoafa', 4758, 'VN', 181, 'RO', 45.78333000, 27.20000000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q13970573'),
(94283, 'Garvăn', 4727, 'TL', 181, 'RO', 45.34710000, 28.16187000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q12094349'),
(94284, 'Gavojdia', 4748, 'TM', 181, 'RO', 45.61667000, 22.01667000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q1008607'),
(94285, 'Geaca', 4734, 'CJ', 181, 'RO', 46.86667000, 24.10000000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q1029287'),
(94286, 'Geamăna', 4722, 'AG', 181, 'RO', 44.82005000, 24.89109000, '2019-10-05 23:15:19', '2020-05-01 17:23:08', 1, 'Q1029287'),
(94287, 'Gelu', 4748, 'TM', 181, 'RO', 46.00591000, 21.06002000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q852367'),
(94288, 'Gemenea Brătulești', 4745, 'DB', 181, 'RO', 45.10839000, 25.23155000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q12100725'),
(94289, 'Gemenele', 4736, 'BR', 181, 'RO', 45.28333000, 27.61667000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q8272872'),
(94290, 'Gemeni', 4751, 'MH', 181, 'RO', 44.17265000, 23.08251000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12100726'),
(94291, 'General Berthelot', 4721, 'HD', 181, 'RO', 45.61667000, 22.88333000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q5062771'),
(94292, 'Geoagiu', 4721, 'HD', 181, 'RO', 45.91667000, 23.20000000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q5062771'),
(94293, 'George Enescu', 4740, 'BT', 181, 'RO', 48.03333000, 26.48333000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q947001'),
(94294, 'Gepiu', 4723, 'BH', 181, 'RO', 46.93034000, 21.79016000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q1066264'),
(94295, 'Gheaba', 4729, 'PH', 181, 'RO', 45.30916000, 26.00626000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12098677'),
(94296, 'Gheboaia', 4745, 'DB', 181, 'RO', 44.80203000, 25.74875000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12094507'),
(94297, 'Gheboieni', 4745, 'DB', 181, 'RO', 44.98701000, 25.30670000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12094508'),
(94298, 'Ghelari', 4721, 'HD', 181, 'RO', 45.71667000, 22.78333000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12094572'),
(94299, 'Ghelinţa', 4754, 'CV', 181, 'RO', 45.95000000, 26.23333000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q1008918'),
(94300, 'Gheorghe Doja', 4744, 'BC', 181, 'RO', 46.37831000, 26.96248000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q12094920'),
(94301, 'Gheorghe Doja', 4743, 'IL', 181, 'RO', 44.61622000, 27.18740000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q2291816'),
(94302, 'Gheorghe Lazăr', 4743, 'IL', 181, 'RO', 44.63333000, 27.45000000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q2541058'),
(94303, 'Gheorgheni', 4749, 'HR', 181, 'RO', 46.72292000, 25.60055000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q2541058'),
(94304, 'Gheorghieni', 4734, 'CJ', 181, 'RO', 46.71393000, 23.68853000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q590945'),
(94305, 'Ghergheasa', 4756, 'BZ', 181, 'RO', 45.26667000, 27.20000000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q3292937'),
(94306, 'Ghergheşti', 4752, 'VS', 181, 'RO', 46.50000000, 27.51667000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q2547290'),
(94307, 'Gherghiţa', 4729, 'PH', 181, 'RO', 44.80000000, 26.26667000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q14077756'),
(94308, 'Gherla', 4734, 'CJ', 181, 'RO', 47.03333000, 23.91667000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q14077756'),
(94309, 'Ghermănești', 4725, 'IF', 181, 'RO', 44.68829000, 26.14962000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q12095840'),
(94310, 'Ghermănești', 4752, 'VS', 181, 'RO', 46.82998000, 28.08105000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q12095841'),
(94311, 'Gherteniș', 4753, 'CS', 181, 'RO', 45.43315000, 21.58126000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q636567'),
(94312, 'Gherăeşti', 4731, 'NT', 181, 'RO', 47.01667000, 26.81667000, '2019-10-05 23:15:19', '2020-05-01 17:23:10', 1, 'Q2605771'),
(94313, 'Gherăseni', 4756, 'BZ', 181, 'RO', 45.02589000, 26.79128000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q3293922'),
(94314, 'Gherţa Mică', 4746, 'SM', 181, 'RO', 47.93333000, 23.23333000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q1079496'),
(94315, 'Gherța Mare', 4746, 'SM', 181, 'RO', 47.96884000, 23.20853000, '2019-10-05 23:15:19', '2020-05-01 17:23:11', 1, 'Q338380'),
(94316, 'Ghidfalău', 4754, 'CV', 181, 'RO', 45.90000000, 25.85000000, '2019-10-05 23:15:19', '2020-05-01 17:23:09', 1, 'Q1010410'),
(94317, 'Ghidici', 4742, 'DJ', 181, 'RO', 43.89103000, 23.19454000, '2019-10-05 23:15:19', '2019-10-05 23:15:19', 1, 'Q2608684'),
(94318, 'Ghidigeni', 4747, 'GL', 181, 'RO', 46.05000000, 27.50000000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1010196'),
(94319, 'Ghighișeni', 4723, 'BH', 181, 'RO', 46.55141000, 22.43148000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q754503'),
(94320, 'Ghilad', 4748, 'TM', 181, 'RO', 45.46501000, 21.13824000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1010520'),
(94321, 'Ghimbav', 4759, 'BV', 181, 'RO', 45.66667000, 25.50000000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1010520'),
(94322, 'Ghimeş-Făget', 4744, 'BC', 181, 'RO', 46.58333000, 26.06667000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q1010520'),
(94323, 'Ghimeș', 4744, 'BC', 181, 'RO', 46.56821000, 26.08886000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q1029547'),
(94324, 'Ghimpați', 4738, 'OT', 181, 'RO', 44.15359000, 24.42698000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q12098986'),
(94325, 'Ghimpați', 4726, 'GR', 181, 'RO', 44.19586000, 25.78336000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q2717216'),
(94326, 'Ghimpețeni', 4738, 'OT', 181, 'RO', 44.28101000, 24.77458000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q2549834'),
(94327, 'Ghindeni', 4742, 'DJ', 181, 'RO', 44.21223000, 23.92336000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2608690'),
(94328, 'Ghindăoani', 4731, 'NT', 181, 'RO', 47.10983000, 26.33909000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q2542013'),
(94329, 'Ghindăreşti', 4737, 'CT', 181, 'RO', 44.65000000, 28.03333000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q242006'),
(94330, 'Ghinești', 4745, 'DB', 181, 'RO', 44.74774000, 25.55300000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q12098996'),
(94331, 'Ghioca', 4738, 'OT', 181, 'RO', 44.31667000, 24.73333000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12098996'),
(94332, 'Ghiorac', 4723, 'BH', 181, 'RO', 46.71340000, 21.68309000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q716200'),
(94333, 'Ghioroc', 4739, 'AR', 181, 'RO', 46.15000000, 21.58333000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1029709'),
(94334, 'Ghioroiu', 4757, 'VL', 181, 'RO', 44.70000000, 23.83333000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2205585'),
(94335, 'Ghioșești', 4729, 'PH', 181, 'RO', 45.24235000, 25.62671000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q2205585'),
(94336, 'Ghirdoveni', 4745, 'DB', 181, 'RO', 44.94059000, 25.67368000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12099068'),
(94337, 'Ghiroda', 4748, 'TM', 181, 'RO', 45.76389000, 21.30028000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1030777'),
(94338, 'Ghizdăvești', 4742, 'DJ', 181, 'RO', 44.05757000, 24.10811000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q12098922'),
(94339, 'Ghizela', 4748, 'TM', 181, 'RO', 45.82417000, 21.73722000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1010907'),
(94340, 'Giarmata', 4748, 'TM', 181, 'RO', 45.83833000, 21.31083000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1027863'),
(94341, 'Giarmata-Vii', 4748, 'TM', 181, 'RO', 45.79960000, 21.30026000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q754605'),
(94342, 'Giera', 4748, 'TM', 181, 'RO', 45.40722000, 20.99222000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1019977'),
(94343, 'Gighera', 4742, 'DJ', 181, 'RO', 43.85000000, 23.80000000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2081901'),
(94344, 'Gilău', 4734, 'CJ', 181, 'RO', 46.73245000, 23.36174000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q2924205'),
(94345, 'Gioseni', 4744, 'BC', 181, 'RO', 46.42925000, 26.99239000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2620847'),
(94346, 'Girișu de Criș', 4723, 'BH', 181, 'RO', 47.06416000, 21.76108000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q1022793'),
(94347, 'Giroc', 4748, 'TM', 181, 'RO', 45.69417000, 21.23583000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1030603'),
(94348, 'Girov', 4731, 'NT', 181, 'RO', 46.95000000, 26.51667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2605664'),
(94349, 'Giubega', 4742, 'DJ', 181, 'RO', 44.12740000, 23.40757000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q268932'),
(94350, 'Giuleşti', 4760, 'MM', 181, 'RO', 47.81667000, 23.93333000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q752829'),
(94351, 'Giulvăz', 4748, 'TM', 181, 'RO', 45.54889000, 20.98444000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q1146461'),
(94352, 'Giurgeni', 4743, 'IL', 181, 'RO', 44.76667000, 27.88333000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1528079'),
(94353, 'Giurgiu', 4726, 'GR', 181, 'RO', 43.88664000, 25.96270000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1528079'),
(94354, 'Giurgiţa', 4742, 'DJ', 181, 'RO', 44.01667000, 23.63333000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q2426242'),
(94355, 'Giurtelecu Șimleului', 4741, 'SJ', 181, 'RO', 47.29680000, 22.79557000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q43136'),
(94356, 'Giuvărăşti', 4738, 'OT', 181, 'RO', 43.80000000, 24.70000000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q2719180'),
(94357, 'Glimboca', 4753, 'CS', 181, 'RO', 45.48333000, 22.31667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q960344'),
(94358, 'Glina', 4725, 'IF', 181, 'RO', 44.38333000, 26.25000000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2605914'),
(94359, 'Glod', 4745, 'DB', 181, 'RO', 45.24163000, 25.44994000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q627385'),
(94360, 'Glodeanu-Siliştea', 4756, 'BZ', 181, 'RO', 44.83333000, 26.80000000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q2292265'),
(94361, 'Glodeanu-Sărat', 4756, 'BZ', 181, 'RO', 44.86667000, 26.65000000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q3292576'),
(94362, 'Glodeni', 4745, 'DB', 181, 'RO', 45.01667000, 25.46667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q3732858'),
(94363, 'Glodenii Gândului', 4735, 'IS', 181, 'RO', 46.95158000, 27.29938000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q12096159'),
(94364, 'Glogova', 4750, 'GJ', 181, 'RO', 44.92806000, 22.90667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12096151'),
(94365, 'Glăvile', 4757, 'VL', 181, 'RO', 44.81667000, 24.15000000, '2019-10-05 23:15:20', '2020-05-01 17:23:12', 1, 'Q2721408'),
(94366, 'Glăvăneşti', 4744, 'BC', 181, 'RO', 46.25000000, 27.38333000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q15651139'),
(94367, 'Godeanu', 4751, 'MH', 181, 'RO', 44.80111000, 22.60861000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12096432'),
(94368, 'Godeni', 4722, 'AG', 181, 'RO', 45.23333000, 24.98333000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12096413'),
(94369, 'Godineşti', 4750, 'GJ', 181, 'RO', 45.00000000, 22.96667000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q2717774'),
(94370, 'Goești', 4735, 'IS', 181, 'RO', 47.15654000, 27.13659000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q12097309'),
(94371, 'Gogoşari', 4726, 'GR', 181, 'RO', 43.86667000, 25.70000000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q2717356'),
(94372, 'Gogoşu', 4751, 'MH', 181, 'RO', 44.37389000, 22.59306000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q2605935'),
(94373, 'Gogoşu', 4742, 'DJ', 181, 'RO', 44.41667000, 23.36667000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q2361921'),
(94374, 'Gohor', 4747, 'GL', 181, 'RO', 46.06667000, 27.40000000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2717179'),
(94375, 'Goicea', 4742, 'DJ', 181, 'RO', 43.91667000, 23.61667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1011377'),
(94376, 'Goiești', 4742, 'DJ', 181, 'RO', 44.48333000, 23.76667000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q1846314'),
(94377, 'Goleşti', 4758, 'VN', 181, 'RO', 45.66667000, 27.13333000, '2019-10-05 23:15:20', '2020-05-01 17:23:12', 1, 'Q13979699'),
(94378, 'Golești', 4722, 'AG', 181, 'RO', 44.83970000, 24.96530000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q12096479'),
(94379, 'Gologanu', 4758, 'VN', 181, 'RO', 45.60979000, 27.27091000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1405784'),
(94380, 'Golăiești', 4735, 'IS', 181, 'RO', 47.23333000, 27.70000000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q2607538'),
(94381, 'Goranu', 4757, 'VL', 181, 'RO', 45.10523000, 24.38431000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q15931660'),
(94382, 'Gorban', 4735, 'IS', 181, 'RO', 46.87611000, 28.07889000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2607508'),
(94383, 'Gorbăneşti', 4740, 'BT', 181, 'RO', 47.78333000, 26.85000000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q2715897'),
(94384, 'Gorganu', 4722, 'AG', 181, 'RO', 44.84241000, 25.03838000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12096907'),
(94385, 'Gorgota', 4729, 'PH', 181, 'RO', 44.78333000, 26.08333000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q14077859'),
(94386, 'Gorgota', 4745, 'DB', 181, 'RO', 44.97626000, 25.50837000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12096913'),
(94387, 'Gornet', 4729, 'PH', 181, 'RO', 45.11667000, 26.06667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q14077934'),
(94388, 'Gornet-Cricov', 4729, 'PH', 181, 'RO', 45.08333000, 26.26667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q14077986'),
(94389, 'Goruia', 4753, 'CS', 181, 'RO', 45.18722000, 21.77917000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1027649'),
(94390, 'Goruna', 4729, 'PH', 181, 'RO', 45.08696000, 25.94202000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12097160'),
(94391, 'Goruni', 4735, 'IS', 181, 'RO', 47.10389000, 27.71854000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12097166'),
(94392, 'Gostavăţu', 4738, 'OT', 181, 'RO', 44.08333000, 24.53333000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q2719274'),
(94393, 'Gostinari', 4726, 'GR', 181, 'RO', 44.18624000, 26.22807000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2717523'),
(94394, 'Gostinu', 4726, 'GR', 181, 'RO', 44.00000000, 26.11667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2539448'),
(94395, 'Gottlob', 4748, 'TM', 181, 'RO', 45.93600000, 20.71168000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1084879'),
(94396, 'Goştinari-Văcăreşti', 4726, 'GR', 181, 'RO', 44.18333000, 26.21667000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q1084879'),
(94397, 'Goșmani', 4731, 'NT', 181, 'RO', 46.83620000, 26.71522000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q12097289'),
(94398, 'Grabăț', 4748, 'TM', 181, 'RO', 45.87861000, 20.74238000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q917779'),
(94399, 'Gradiștea', 4729, 'PH', 181, 'RO', 44.88803000, 26.51520000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q12097571'),
(94400, 'Grajduri', 4735, 'IS', 181, 'RO', 46.96667000, 27.51667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2487222'),
(94401, 'Gratia', 4728, 'TR', 181, 'RO', 44.43333000, 25.45000000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q514351'),
(94402, 'Greaca', 4726, 'GR', 181, 'RO', 44.10944000, 26.33944000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2717549'),
(94403, 'Greblești', 4757, 'VL', 181, 'RO', 45.47514000, 24.31182000, '2019-10-05 23:15:20', '2020-05-01 17:23:12', 1, 'Q12097547'),
(94404, 'Grebănu', 4756, 'BZ', 181, 'RO', 45.38333000, 26.96667000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q3293623'),
(94405, 'Greceanca', 4756, 'BZ', 181, 'RO', 45.07863000, 26.54468000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12097632'),
(94406, 'Greceşti', 4742, 'DJ', 181, 'RO', 44.45000000, 23.28333000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q1499136'),
(94407, 'Greci', 4745, 'DB', 181, 'RO', 44.66132000, 25.34346000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12097626'),
(94408, 'Greci', 4751, 'MH', 181, 'RO', 44.55000000, 23.11667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2605908'),
(94409, 'Greci', 4727, 'TL', 181, 'RO', 45.18333000, 28.23333000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1028082'),
(94410, 'Greci', 4738, 'OT', 181, 'RO', 44.33025000, 24.55923000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q12097629'),
(94411, 'Greoni', 4753, 'CS', 181, 'RO', 45.08901000, 21.61731000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q757214'),
(94412, 'Grindu', 4743, 'IL', 181, 'RO', 44.76667000, 26.91667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2539903'),
(94413, 'Grindu', 4727, 'TL', 181, 'RO', 45.40000000, 28.21667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q1028207'),
(94414, 'Grinţieş', 4731, 'NT', 181, 'RO', 47.05000000, 25.86667000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q1009837'),
(94415, 'Griviţa', 4743, 'IL', 181, 'RO', 44.73333000, 27.28333000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q8272803'),
(94416, 'Griviţa', 4752, 'VS', 181, 'RO', 46.15000000, 27.65000000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q2546845'),
(94417, 'Griviţa', 4747, 'GL', 181, 'RO', 45.71667000, 27.65000000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q2538772'),
(94418, 'Grojdibodu', 4738, 'OT', 181, 'RO', 43.75000000, 24.26667000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2719167'),
(94419, 'Gropeni', 4736, 'BR', 181, 'RO', 45.07547000, 27.87969000, '2019-10-05 23:15:20', '2019-10-05 23:15:20', 1, 'Q2716160'),
(94420, 'Gropniţa', 4735, 'IS', 181, 'RO', 47.35000000, 27.25000000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q2607452'),
(94421, 'Gropșani', 4738, 'OT', 181, 'RO', 44.44072000, 23.97047000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q12097974'),
(94422, 'Grozeşti', 4735, 'IS', 181, 'RO', 46.99083000, 28.05056000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q2718189'),
(94423, 'Grozeşti', 4751, 'MH', 181, 'RO', 44.65000000, 23.31667000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q2410884'),
(94424, 'Grozăvești', 4745, 'DB', 181, 'RO', 44.56882000, 25.45360000, '2019-10-05 23:15:20', '2020-05-01 17:23:09', 1, 'Q12097898'),
(94425, 'Groşi', 4760, 'MM', 181, 'RO', 47.61667000, 23.61667000, '2019-10-05 23:15:20', '2020-05-01 17:23:10', 1, 'Q1189062'),
(94426, 'Groșani', 4729, 'PH', 181, 'RO', 45.24822000, 25.94795000, '2019-10-05 23:15:20', '2020-05-01 17:23:11', 1, 'Q1189062'),
(94427, 'Groși', 4723, 'BH', 181, 'RO', 47.04525000, 22.47965000, '2019-10-05 23:15:20', '2020-05-01 17:23:08', 1, 'Q725229'),
(94428, 'Groșii Țibleșului', 4760, 'MM', 181, 'RO', 47.48983000, 24.06368000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q595613'),
(94429, 'Gruia', 4751, 'MH', 181, 'RO', 44.26750000, 22.70472000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q1550352'),
(94430, 'Gruiu', 4725, 'IF', 181, 'RO', 44.73333000, 26.23333000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12729584'),
(94431, 'Grumăzeşti', 4731, 'NT', 181, 'RO', 47.13333000, 26.36667000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q2605644'),
(94432, 'Grunji', 4756, 'BZ', 181, 'RO', 45.50000000, 26.65000000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q2605644'),
(94433, 'Grădina', 4737, 'CT', 181, 'RO', 44.55520000, 28.43236000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q1028459'),
(94434, 'Grădinari', 4738, 'OT', 181, 'RO', 44.56667000, 24.26667000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2719161'),
(94435, 'Grădinari', 4726, 'GR', 181, 'RO', 44.39222000, 25.81556000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q2717783'),
(94436, 'Grădinari', 4753, 'CS', 181, 'RO', 45.11861000, 21.59889000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q75530'),
(94437, 'Grădiştea', 4732, 'CL', 181, 'RO', 44.23333000, 27.21667000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q12097569'),
(94438, 'Grădiştea', 4725, 'IF', 181, 'RO', 44.66667000, 26.28333000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12097564'),
(94439, 'Grădiştea', 4757, 'VL', 181, 'RO', 44.86667000, 23.81667000, '2019-10-05 23:15:21', '2020-05-01 17:23:12', 1, 'Q919424'),
(94440, 'Grădiştea', 4736, 'BR', 181, 'RO', 45.26667000, 27.38333000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q13542612'),
(94441, 'Grădiștea', 4726, 'GR', 181, 'RO', 44.21062000, 26.16547000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12097568'),
(94442, 'Grăjdana', 4756, 'BZ', 181, 'RO', 45.20865000, 26.63114000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q12097574'),
(94443, 'Grămeşti', 4720, 'SV', 181, 'RO', 47.91667000, 26.15000000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2606074'),
(94444, 'Grăniceri', 4739, 'AR', 181, 'RO', 46.51667000, 21.30000000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q1077581'),
(94445, 'Grăniceşti', 4720, 'SV', 181, 'RO', 47.81667000, 26.06667000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2719750'),
(94446, 'Gugeşti', 4758, 'VN', 181, 'RO', 45.56667000, 27.13333000, '2019-10-05 23:15:21', '2020-05-01 17:23:12', 1, 'Q14024085'),
(94447, 'Gugești', 4752, 'VS', 181, 'RO', 46.76342000, 27.90050000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q12098275'),
(94448, 'Gulia', 4720, 'SV', 181, 'RO', 47.41203000, 26.60854000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098384'),
(94449, 'Gulia', 4745, 'DB', 181, 'RO', 44.54687000, 25.87304000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098385'),
(94450, 'Gura Beliei', 4729, 'PH', 181, 'RO', 45.21064000, 25.64946000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098385'),
(94451, 'Gura Caliţei', 4758, 'VN', 181, 'RO', 45.58333000, 27.01667000, '2019-10-05 23:15:21', '2020-05-01 17:23:12', 1, 'Q14025903'),
(94452, 'Gura Căluiu', 4738, 'OT', 181, 'RO', 44.45903000, 24.03547000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q12098474'),
(94453, 'Gura Foii', 4745, 'DB', 181, 'RO', 44.75000000, 25.28333000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098495'),
(94454, 'Gura Humorului', 4720, 'SV', 181, 'RO', 47.55000000, 25.90000000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098495'),
(94455, 'Gura Ialomiței', 4743, 'IL', 181, 'RO', 44.71608000, 27.75310000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12724825'),
(94456, 'Gura Ocniței', 4745, 'DB', 181, 'RO', 44.94166000, 25.56846000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q4684614'),
(94457, 'Gura Padinii', 4738, 'OT', 181, 'RO', 43.75704000, 24.31615000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q2549528'),
(94458, 'Gura Putnei', 4720, 'SV', 181, 'RO', 47.89951000, 25.59510000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098484'),
(94459, 'Gura Râului', 4755, 'SB', 181, 'RO', 45.73333000, 23.98333000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q281607'),
(94460, 'Gura Suhașului', 4757, 'VL', 181, 'RO', 45.08803000, 24.29886000, '2019-10-05 23:15:21', '2020-05-01 17:23:12', 1, 'Q281607'),
(94461, 'Gura Teghii', 4756, 'BZ', 181, 'RO', 45.48333000, 26.41667000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q6316106'),
(94462, 'Gura Vadului', 4729, 'PH', 181, 'RO', 45.05000000, 26.46667000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q14078051'),
(94463, 'Gura Viţioarei', 4729, 'PH', 181, 'RO', 45.15000000, 26.03333000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q14078162'),
(94464, 'Gura Vulcanei', 4745, 'DB', 181, 'RO', 45.02442000, 25.39596000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098465'),
(94465, 'Gura Văii', 4744, 'BC', 181, 'RO', 46.27234000, 26.82604000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q15651296'),
(94466, 'Gura Văii', 4751, 'MH', 181, 'RO', 44.66732000, 22.55646000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q15651296'),
(94467, 'Gura Şuţii', 4745, 'DB', 181, 'RO', 44.75000000, 25.51667000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q3734001'),
(94468, 'Gurahonţ', 4739, 'AR', 181, 'RO', 46.26667000, 22.35000000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q1028874'),
(94469, 'Gurasada', 4721, 'HD', 181, 'RO', 45.95483000, 22.59491000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q5062241'),
(94470, 'Gurba', 4739, 'AR', 181, 'RO', 46.46756000, 21.78789000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q267706'),
(94471, 'Gurbediu', 4723, 'BH', 181, 'RO', 46.79379000, 21.86480000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q667859'),
(94472, 'Gurbăneşti', 4732, 'CL', 181, 'RO', 44.38333000, 26.70000000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q12098502'),
(94473, 'Guruieni', 4728, 'TR', 181, 'RO', 44.05845000, 25.39656000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12098549'),
(94474, 'Guşoeni', 4757, 'VL', 181, 'RO', 44.73333000, 24.11667000, '2019-10-05 23:15:21', '2020-05-01 17:23:12', 1, 'Q2069743'),
(94475, 'Gvardinița', 4751, 'MH', 181, 'RO', 44.39505000, 23.14635000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12094474'),
(94476, 'Gâdinţi', 4731, 'NT', 181, 'RO', 46.93333000, 27.01667000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12729766'),
(94477, 'Gâldău', 4732, 'CL', 181, 'RO', 44.30544000, 27.66972000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q12095937'),
(94478, 'Gâlgău', 4741, 'SJ', 181, 'RO', 47.28183000, 23.70157000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q1079547'),
(94479, 'Gâlgău Almaşului', 4741, 'SJ', 181, 'RO', 47.20000000, 23.33333000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q658265'),
(94480, 'Gângiova', 4742, 'DJ', 181, 'RO', 43.90000000, 23.85000000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q1818721'),
(94481, 'Gârbești', 4735, 'IS', 181, 'RO', 46.98333000, 27.30000000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12095955'),
(94482, 'Gârbou', 4741, 'SJ', 181, 'RO', 47.15000000, 23.43333000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q1093193'),
(94483, 'Gârbova', 4724, 'AB', 181, 'RO', 45.86667000, 23.73333000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q949473'),
(94484, 'Gârbovi', 4743, 'IL', 181, 'RO', 44.78333000, 26.76667000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12724830'),
(94485, 'Gârbău', 4734, 'CJ', 181, 'RO', 46.83305000, 23.35240000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q969012'),
(94486, 'Gârceni', 4752, 'VS', 181, 'RO', 46.76667000, 27.28333000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2547494'),
(94487, 'Gârcina', 4731, 'NT', 181, 'RO', 46.97691000, 26.34573000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q2605730'),
(94488, 'Gârcov', 4738, 'OT', 181, 'RO', 43.75910000, 24.61577000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2543054'),
(94489, 'Gârda de Sus', 4724, 'AB', 181, 'RO', 46.45000000, 22.81667000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q936118'),
(94490, 'Gârdani', 4760, 'MM', 181, 'RO', 47.55135000, 23.30880000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q283920'),
(94491, 'Gârla-Mare', 4751, 'MH', 181, 'RO', 44.20917000, 22.77500000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q2176284'),
(94492, 'Gârleni', 4744, 'BC', 181, 'RO', 46.66667000, 26.80000000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q15642804'),
(94493, 'Gârlenii de Sus', 4744, 'BC', 181, 'RO', 46.65414000, 26.79491000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q12095976'),
(94494, 'Gârliciu', 4737, 'CT', 181, 'RO', 44.76667000, 28.08333000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q934014'),
(94495, 'Gârnic', 4753, 'CS', 181, 'RO', 44.78639000, 21.79444000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q12724834'),
(94496, 'Găeşti', 4745, 'DB', 181, 'RO', 44.71667000, 25.31667000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q16898239'),
(94497, 'Găgeni', 4729, 'PH', 181, 'RO', 45.02164000, 25.94585000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q12094534'),
(94498, 'Găgeşti', 4752, 'VS', 181, 'RO', 46.33333000, 27.96667000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2547267'),
(94499, 'Găgești', 4758, 'VN', 181, 'RO', 45.85598000, 27.05540000, '2019-10-05 23:15:21', '2020-05-01 17:23:12', 1, 'Q12094537'),
(94500, 'Găiceana', 4744, 'BC', 181, 'RO', 46.33333000, 27.21667000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q15642783'),
(94501, 'Găinești', 4720, 'SV', 181, 'RO', 47.41968000, 25.91809000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q12094556'),
(94502, 'Găiseni', 4726, 'GR', 181, 'RO', 44.51500000, 25.64528000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q2717345'),
(94503, 'Gălbinaşi', 4756, 'BZ', 181, 'RO', 45.08333000, 26.91667000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q2291182'),
(94504, 'Gălbinași', 4732, 'CL', 181, 'RO', 44.31724000, 26.41941000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q2288662'),
(94505, 'Gălăneşti', 4720, 'SV', 181, 'RO', 47.91667000, 25.80000000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q1031848'),
(94506, 'Gălăuţaş', 4749, 'HR', 181, 'RO', 46.91667000, 25.43333000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q1007891'),
(94507, 'Gălăţeni', 4728, 'TR', 181, 'RO', 44.21667000, 25.35000000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2721354'),
(94508, 'Gălășești', 4722, 'AG', 181, 'RO', 44.95593000, 24.78602000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q12094585'),
(94509, 'Găneasa', 4738, 'OT', 181, 'RO', 44.41667000, 24.28333000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2719016'),
(94510, 'Găneasa', 4725, 'IF', 181, 'RO', 44.48333000, 26.28333000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q1656427'),
(94511, 'Gănești', 4747, 'GL', 181, 'RO', 46.08970000, 27.99337000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q12094704'),
(94512, 'Gărăgău', 4728, 'TR', 181, 'RO', 44.23186000, 25.18920000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q12095804'),
(94513, 'Gătaia', 4748, 'TM', 181, 'RO', 45.43111000, 21.43000000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q12095804'),
(94514, 'Găujani', 4726, 'GR', 181, 'RO', 43.73333000, 25.70000000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q2717384'),
(94515, 'Găvănești', 4756, 'BZ', 181, 'RO', 45.08825000, 27.06499000, '2019-10-05 23:15:21', '2020-05-01 17:23:09', 1, 'Q12094517'),
(94516, 'Găvănești', 4738, 'OT', 181, 'RO', 44.41724000, 24.01654000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q2549883'),
(94517, 'Găzărie', 4744, 'BC', 181, 'RO', 46.48928000, 26.51877000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q12094544'),
(94518, 'Halmeu', 4746, 'SM', 181, 'RO', 47.96667000, 23.01667000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q1033053'),
(94519, 'Halmăşd', 4741, 'SJ', 181, 'RO', 47.15000000, 22.61667000, '2019-10-05 23:15:21', '2020-05-01 17:23:11', 1, 'Q1191831'),
(94520, 'Halânga', 4751, 'MH', 181, 'RO', 44.68116000, 22.69037000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12166822'),
(94521, 'Hamcearca', 4727, 'TL', 181, 'RO', 45.11667000, 28.36667000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q1033365'),
(94522, 'Hangu', 4731, 'NT', 181, 'RO', 47.05000000, 26.03333000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q2719398'),
(94523, 'Hanu Conachi', 4747, 'GL', 181, 'RO', 45.58650000, 27.59481000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12166870'),
(94524, 'Havârna', 4740, 'BT', 181, 'RO', 48.06667000, 26.65000000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q2715832'),
(94525, 'Haţeg', 4721, 'HD', 181, 'RO', 45.61667000, 22.95000000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q2715832'),
(94526, 'Heci', 4735, 'IS', 181, 'RO', 47.34325000, 26.66226000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12167221'),
(94527, 'Helegiu', 4744, 'BC', 181, 'RO', 46.35000000, 26.75000000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q15651341'),
(94528, 'Heleșteni', 4735, 'IS', 181, 'RO', 47.20303000, 26.87755000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q2718809'),
(94529, 'Hemeiuș', 4744, 'BC', 181, 'RO', 46.62643000, 26.85440000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q15662238'),
(94530, 'Herculian', 4754, 'CV', 181, 'RO', 46.13402000, 25.70977000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q713958'),
(94531, 'Hereclean', 4741, 'SJ', 181, 'RO', 47.25000000, 23.01667000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q1191355');

