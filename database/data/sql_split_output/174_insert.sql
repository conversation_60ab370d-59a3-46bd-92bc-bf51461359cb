INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(88516, 'Wier<PERSON>wice', 1626, '18', 176, 'PL', 50.23623000, 22.45090000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q7999197'),
(88517, 'Wierzbica', 1637, '14', 176, 'PL', 51.24940000, 21.08259000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q7999197'),
(88518, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 1634, '30', 176, 'PL', 52.44026000, 18.51085000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q7999197'),
(88519, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 1626, '18', 176, 'PL', 50.03311000, 22.60128000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q7999246'),
(88520, 'Wierzbno', 1637, '14', 176, 'PL', 52.31008000, 21.85902000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q2445819'),
(88521, 'Wierzbno', 1629, '02', 176, 'PL', 50.93674000, 17.17961000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q9373319'),
(88522, 'Wierzchlas', 1636, '10', 176, 'PL', 51.20457000, 18.66543000, '2019-10-05 23:14:30', '2025-03-19 16:09:33', 1, 'Q9373319'),
(88523, 'Wierzchosławice', 1635, '12', 176, 'PL', 50.02478000, 20.85677000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q1877335'),
(88524, 'Wierzchosławice', 1625, '04', 176, 'PL', 52.86923000, 18.35609000, '2019-10-05 23:14:30', '2025-03-19 16:06:21', 1, 'Q9373336'),
(88525, 'Wierzchowo', 1633, '32', 176, 'PL', 53.46013000, 16.09961000, '2019-10-05 23:14:30', '2025-03-19 16:10:04', 1, 'Q9373336'),
(88526, 'Wierzchucino', 1624, '22', 176, 'PL', 54.78797000, 18.00307000, '2019-10-05 23:14:30', '2025-03-19 16:12:14', 1, 'Q2543767'),
(88527, 'Wieszowa', 1623, '24', 176, 'PL', 50.38444000, 18.75924000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q130108'),
(88528, 'Wietrzychowice', 1635, '12', 176, 'PL', 50.19097000, 20.76502000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q2150881'),
(88529, 'Wijewo', 1634, '30', 176, 'PL', 51.91627000, 16.18552000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q2150881'),
(88530, 'Wilamowice', 1623, '24', 176, 'PL', 49.91701000, 19.15237000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q948267'),
(88531, 'Wilanów', 1637, '14', 176, 'PL', 52.16311000, 21.08748000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q1887504'),
(88532, 'Wilcza', 1623, '24', 176, 'PL', 50.18899000, 18.59668000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q1570938'),
(88533, 'Wilczogóra', 1634, '30', 176, 'PL', 52.47348000, 18.16744000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q1570938'),
(88534, 'Wilczyce', 1630, '26', 176, 'PL', 50.74694000, 21.65783000, '2019-10-05 23:14:30', '2025-03-19 16:12:25', 1, 'Q554357'),
(88535, 'Wilczyce', 1629, '02', 176, 'PL', 51.12944000, 17.15472000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q9374363'),
(88536, 'Wilczyn', 1634, '30', 176, 'PL', 52.48816000, 18.16126000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q553957'),
(88537, 'Wilga', 1637, '14', 176, 'PL', 51.85211000, 21.37751000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q553957'),
(88538, 'Wilkowice', 1623, '24', 176, 'PL', 49.76282000, 19.08973000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q553957'),
(88539, 'Wilkowice', 1634, '30', 176, 'PL', 51.88513000, 16.53417000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q8002577'),
(88540, 'Wilkołaz', 1638, '06', 176, 'PL', 51.01473000, 22.35014000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q2832384'),
(88541, 'Wilków', 1638, '06', 176, 'PL', 51.26224000, 21.87756000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q2832384'),
(88542, 'Wilków', 1622, '16', 176, 'PL', 51.10158000, 17.66284000, '2019-10-05 23:14:30', '2025-03-19 16:11:35', 1, 'Q2605852'),
(88543, 'Wilków', 1629, '02', 176, 'PL', 51.09212000, 15.92824000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q2589647'),
(88544, 'Winnica', 1637, '14', 176, 'PL', 52.64306000, 20.94114000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q2751357'),
(88545, 'Wiskitki', 1637, '14', 176, 'PL', 52.08831000, 20.38708000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q655956'),
(88546, 'Wisznice', 1638, '06', 176, 'PL', 51.78924000, 23.20836000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q655956'),
(88547, 'Wisła', 1623, '24', 176, 'PL', 49.65629000, 18.85910000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q655956'),
(88548, 'Witanowice', 1635, '12', 176, 'PL', 49.91796000, 19.52579000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q2145459'),
(88549, 'Witaszyce', 1634, '30', 176, 'PL', 51.94151000, 17.56182000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q8027815'),
(88550, 'Witkowice', 1635, '12', 176, 'PL', 49.90746000, 19.27963000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8028329'),
(88551, 'Witkowo', 1634, '30', 176, 'PL', 52.43964000, 17.77264000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q1122800'),
(88552, 'Witnica', 1631, '08', 176, 'PL', 52.67318000, 14.89765000, '2019-10-05 23:14:30', '2025-03-19 16:08:41', 1, 'Q988636'),
(88553, 'Witonia', 1636, '10', 176, 'PL', 52.14655000, 19.30049000, '2019-10-05 23:14:30', '2025-03-19 16:09:33', 1, 'Q1009554'),
(88554, 'Wizna', 1632, '20', 176, 'PL', 53.19518000, 22.38241000, '2019-10-05 23:14:30', '2025-03-19 16:11:50', 1, 'Q2237742'),
(88555, 'Wiązownica', 1626, '18', 176, 'PL', 50.08066000, 22.70668000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q2237742'),
(88556, 'Wiązów', 1629, '02', 176, 'PL', 50.81399000, 17.20214000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q999604'),
(88557, 'Więcbork', 1625, '04', 176, 'PL', 53.35384000, 17.49064000, '2019-10-05 23:14:30', '2025-03-19 16:06:21', 1, 'Q991829'),
(88558, 'Większyce', 1622, '16', 176, 'PL', 50.33659000, 18.10221000, '2019-10-05 23:14:30', '2025-03-19 16:11:35', 1, 'Q3887061'),
(88559, 'Wińsko', 1629, '02', 176, 'PL', 51.47032000, 16.61390000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q365081'),
(88560, 'Wiślica', 1630, '26', 176, 'PL', 50.34891000, 20.67438000, '2019-10-05 23:14:30', '2025-03-19 16:12:25', 1, 'Q945288'),
(88561, 'Wiśniew', 1637, '14', 176, 'PL', 52.07265000, 22.29392000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q4409365'),
(88562, 'Wiśniewo', 1637, '14', 176, 'PL', 53.06474000, 20.34805000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q2706617'),
(88563, 'Wiśniowa', 1635, '12', 176, 'PL', 49.78780000, 20.11502000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q2706617'),
(88564, 'Wiśniowa', 1626, '18', 176, 'PL', 49.86897000, 21.65508000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q2706617'),
(88565, 'Wleń', 1629, '02', 176, 'PL', 51.01635000, 15.67474000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q2328035'),
(88566, 'Wodynie', 1637, '14', 176, 'PL', 52.04040000, 21.95575000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q2387940'),
(88567, 'Wodzierady', 1636, '10', 176, 'PL', 51.71826000, 19.15123000, '2019-10-05 23:14:30', '2025-03-19 16:09:33', 1, 'Q985845'),
(88568, 'Wodzisław', 1630, '26', 176, 'PL', 50.52047000, 20.19150000, '2019-10-05 23:14:30', '2025-03-19 16:12:25', 1, 'Q553760'),
(88569, 'Wodzisław Śląski', 1623, '24', 176, 'PL', 50.00377000, 18.47205000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q500260'),
(88570, 'Wohyń', 1638, '06', 176, 'PL', 51.75642000, 22.78582000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q321946'),
(88571, 'Wojaszówka', 1626, '18', 176, 'PL', 49.77777000, 21.67079000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q3039554'),
(88572, 'Wojciechowice', 1630, '26', 176, 'PL', 50.84226000, 21.58942000, '2019-10-05 23:14:30', '2025-03-19 16:12:25', 1, 'Q553422'),
(88573, 'Wojciechów', 1638, '06', 176, 'PL', 51.23543000, 22.24551000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q2335641'),
(88574, 'Wojcieszków', 1638, '06', 176, 'PL', 51.76922000, 22.31589000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q2879227'),
(88575, 'Wojcieszów', 1629, '02', 176, 'PL', 50.95194000, 15.92185000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q987981'),
(88576, 'Wojkowice', 1623, '24', 176, 'PL', 50.36509000, 19.03652000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q985640'),
(88577, 'Wojnicz', 1635, '12', 176, 'PL', 49.95800000, 20.83785000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q1089052'),
(88578, 'Wojszyce', 1629, '02', 176, 'PL', 51.05837000, 17.04513000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q1089052'),
(88579, 'Wojsławice', 1638, '06', 176, 'PL', 50.91916000, 23.54602000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q2212388'),
(88580, 'Wola', 1637, '14', 176, 'PL', 52.24010000, 20.98869000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q1020136'),
(88581, 'Wola', 1623, '24', 176, 'PL', 50.01745000, 19.12333000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q8029372'),
(88582, 'Wola Batorska', 1635, '12', 176, 'PL', 50.05262000, 20.26617000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8029380'),
(88583, 'Wola Dębińska', 1635, '12', 176, 'PL', 49.98214000, 20.68777000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8029426'),
(88584, 'Wola Filipowska', 1635, '12', 176, 'PL', 50.13433000, 19.58013000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q3495136'),
(88585, 'Wola Jachowa', 1630, '26', 176, 'PL', 50.84524000, 20.85814000, '2019-10-05 23:14:30', '2025-03-19 16:12:25', 1, 'Q8029453'),
(88586, 'Wola Krzysztoporska', 1636, '10', 176, 'PL', 51.34418000, 19.58090000, '2019-10-05 23:14:30', '2025-03-19 16:09:33', 1, 'Q8029492'),
(88587, 'Wola Radziszowska', 1635, '12', 176, 'PL', 49.90558000, 19.78827000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8029585'),
(88588, 'Wola Rębkowska', 1637, '14', 176, 'PL', 51.90181000, 21.55818000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q8029612'),
(88589, 'Wola Sernicka', 1638, '06', 176, 'PL', 51.44978000, 22.68351000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q9377601'),
(88590, 'Wola Uhruska', 1638, '06', 176, 'PL', 51.32139000, 23.62627000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q819176'),
(88591, 'Wola Zabierzowska', 1635, '12', 176, 'PL', 50.07257000, 20.33217000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8029665'),
(88592, 'Wola Żarczycka', 1626, '18', 176, 'PL', 50.29120000, 22.25023000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q592357'),
(88593, 'Wolanów', 1637, '14', 176, 'PL', 51.38030000, 20.97702000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q2363230'),
(88594, 'Wolbrom', 1635, '12', 176, 'PL', 50.37957000, 19.75831000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q1906323'),
(88595, 'Wolbórz', 1636, '10', 176, 'PL', 51.50196000, 19.83049000, '2019-10-05 23:14:30', '2025-03-19 16:09:33', 1, 'Q386659'),
(88596, 'Wolin', 1633, '32', 176, 'PL', 53.84214000, 14.61465000, '2019-10-05 23:14:30', '2025-03-19 16:10:04', 1, 'Q1862'),
(88597, 'Wolsztyn', 1634, '30', 176, 'PL', 52.11552000, 16.11712000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q852363'),
(88598, 'Wołczyn', 1622, '16', 176, 'PL', 51.01845000, 18.04994000, '2019-10-05 23:14:30', '2025-03-19 16:11:35', 1, 'Q1005384'),
(88599, 'Wołomin', 1637, '14', 176, 'PL', 52.34006000, 21.24207000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q930149'),
(88600, 'Wołowice', 1635, '12', 176, 'PL', 49.98876000, 19.72630000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8037508'),
(88601, 'Wołów', 1629, '02', 176, 'PL', 51.33656000, 16.64429000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q983726'),
(88602, 'Woźniki', 1635, '12', 176, 'PL', 49.93774000, 19.49078000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8037524'),
(88603, 'Wożniki', 1623, '24', 176, 'PL', 50.58934000, 19.05991000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q554829'),
(88604, 'Wrocław', 1629, '02', 176, 'PL', 51.10810000, 17.03859000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q715974'),
(88605, 'Wronki', 1634, '30', 176, 'PL', 52.71051000, 16.38044000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q52900'),
(88606, 'Września', 1634, '30', 176, 'PL', 52.32512000, 17.56519000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q52902'),
(88607, 'Wrząsowice', 1635, '12', 176, 'PL', 49.95862000, 19.94654000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q692491'),
(88608, 'Wróblew', 1636, '10', 176, 'PL', 51.61215000, 18.61487000, '2019-10-05 23:14:30', '2025-03-19 16:09:33', 1, 'Q2180454'),
(88609, 'Wręczyca Wielka', 1623, '24', 176, 'PL', 50.84589000, 18.92086000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q922279'),
(88610, 'Wschowa', 1631, '08', 176, 'PL', 51.80705000, 16.31663000, '2019-10-05 23:14:30', '2025-03-19 16:08:41', 1, 'Q717938'),
(88611, 'Wydminy', 1628, '28', 176, 'PL', 53.98194000, 22.03239000, '2019-10-05 23:14:30', '2025-03-19 16:12:57', 1, 'Q1003621'),
(88612, 'Wymiarki', 1631, '08', 176, 'PL', 51.51109000, 15.08208000, '2019-10-05 23:14:30', '2025-03-19 16:08:41', 1, 'Q1809663'),
(88613, 'Wyry', 1623, '24', 176, 'PL', 50.13296000, 18.90052000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q1809663'),
(88614, 'Wyrzysk', 1634, '30', 176, 'PL', 53.15300000, 17.26802000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q988588'),
(88615, 'Wysoka', 1626, '18', 176, 'PL', 50.04474000, 22.26002000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q8040546'),
(88616, 'Wysoka', 1635, '12', 176, 'PL', 49.90713000, 19.60356000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q1974857'),
(88617, 'Wysoka', 1634, '30', 176, 'PL', 53.18091000, 17.08353000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q556344'),
(88618, 'Wysoka', 1623, '24', 176, 'PL', 50.42989000, 19.35368000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q556344'),
(88619, 'Wysoka Głogowska', 1626, '18', 176, 'PL', 50.16019000, 22.02124000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q8040556'),
(88620, 'Wysoka Strzyżowska', 1626, '18', 176, 'PL', 49.83056000, 21.74074000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, 'Q8040563'),
(88621, 'Wysokie', 1638, '06', 176, 'PL', 50.91093000, 22.66600000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q8040563'),
(88622, 'Wysokie Mazowieckie', 1632, '20', 176, 'PL', 52.91661000, 22.51712000, '2019-10-05 23:14:30', '2025-03-19 16:11:50', 1, 'Q335580'),
(88623, 'Wyszki', 1632, '20', 176, 'PL', 52.84129000, 22.98119000, '2019-10-05 23:14:30', '2025-03-19 16:11:50', 1, 'Q615398'),
(88624, 'Wyszków', 1637, '14', 176, 'PL', 52.59278000, 21.45840000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q910997'),
(88625, 'Wyszogród', 1637, '14', 176, 'PL', 52.38988000, 20.19081000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q569804'),
(88626, 'Wyśmierzyce', 1637, '14', 176, 'PL', 51.62494000, 20.81394000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q432353'),
(88627, 'Wólka Niedźwiedzka', 1626, '18', 176, 'PL', 50.24226000, 22.18826000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, '********'),
(88628, 'Wólka Pełkińska', 1626, '18', 176, 'PL', 50.09553000, 22.62342000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, '********'),
(88629, 'Wólka Podleśna', 1626, '18', 176, 'PL', 50.11785000, 22.11213000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, '********'),
(88630, 'Wólka Tanewska', 1626, '18', 176, 'PL', 50.50005000, 22.26113000, '2019-10-05 23:14:30', '2025-03-19 16:12:02', 1, '********'),
(88631, 'Wąbrzeźno', 1625, '04', 176, 'PL', 53.27989000, 18.94773000, '2019-10-05 23:14:30', '2025-03-19 16:06:21', 1, 'Q324912'),
(88632, 'Wąchock', 1630, '26', 176, 'PL', 51.07391000, 21.01243000, '2019-10-05 23:14:30', '2025-03-19 16:12:25', 1, 'Q439'),
(88633, 'Wągrowiec', 1634, '30', 176, 'PL', 52.80842000, 17.19961000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q645022'),
(88634, 'Wąpielsk', 1625, '04', 176, 'PL', 53.13761000, 19.27792000, '2019-10-05 23:14:30', '2025-03-19 16:06:21', 1, '********'),
(88635, 'Wąsosz', 1632, '20', 176, 'PL', 53.52209000, 22.31915000, '2019-10-05 23:14:30', '2025-03-19 16:11:50', 1, '********'),
(88636, 'Wąsosz', 1629, '02', 176, 'PL', 51.56224000, 16.69059000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q1006065'),
(88637, 'Wąwolnica', 1638, '06', 176, 'PL', 51.29467000, 22.14681000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q2303915'),
(88638, 'Węgierska Górka', 1623, '24', 176, 'PL', 49.60776000, 19.11638000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q1018841'),
(88639, 'Węgliniec', 1629, '02', 176, 'PL', 51.28753000, 15.22894000, '2019-10-05 23:14:30', '2025-03-19 16:05:21', 1, 'Q328402'),
(88640, 'Węglówka', 1635, '12', 176, 'PL', 49.73424000, 20.08575000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q2589007'),
(88641, 'Węgorzewo', 1628, '28', 176, 'PL', 54.21567000, 21.73720000, '2019-10-05 23:14:30', '2025-03-19 16:12:57', 1, 'Q751153'),
(88642, 'Węgorzyno', 1633, '32', 176, 'PL', 53.54101000, 15.55964000, '2019-10-05 23:14:30', '2025-03-19 16:10:04', 1, 'Q997447'),
(88643, 'Węgry', 1622, '16', 176, 'PL', 50.74319000, 18.01741000, '2019-10-05 23:14:30', '2025-03-19 16:11:35', 1, 'Q2542066'),
(88644, 'Węgrzce Wielkie', 1635, '12', 176, 'PL', 50.01491000, 20.11082000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8041163'),
(88645, 'Węgrów', 1637, '14', 176, 'PL', 52.39954000, 22.01634000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q953156'),
(88646, 'Władysławowo', 1624, '22', 176, 'PL', 54.79086000, 18.40090000, '2019-10-05 23:14:30', '2025-03-19 16:12:14', 1, 'Q836965'),
(88647, 'Władysławów', 1634, '30', 176, 'PL', 52.10313000, 18.47626000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q556581'),
(88648, 'Włochy', 1637, '14', 176, 'PL', 52.17941000, 20.94612000, '2019-10-05 23:14:30', '2025-03-19 16:11:22', 1, 'Q1671187'),
(88649, 'Włocławek', 1625, '04', 176, 'PL', 52.64817000, 19.06780000, '2019-10-05 23:14:30', '2025-03-19 16:06:21', 1, 'Q106681'),
(88650, 'Włodawa', 1638, '06', 176, 'PL', 51.55000000, 23.55000000, '2019-10-05 23:14:30', '2025-03-19 16:07:51', 1, 'Q751076'),
(88651, 'Włodowice', 1623, '24', 176, 'PL', 50.55560000, 19.45155000, '2019-10-05 23:14:30', '2025-03-19 16:12:46', 1, 'Q2405929'),
(88652, 'Włosienica', 1635, '12', 176, 'PL', 50.01822000, 19.31671000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8041361'),
(88653, 'Włoszakowice', 1634, '30', 176, 'PL', 51.92754000, 16.36456000, '2019-10-05 23:14:30', '2025-03-19 16:13:08', 1, 'Q985092'),
(88654, 'Włoszczowa', 1630, '26', 176, 'PL', 50.85256000, 19.96593000, '2019-10-05 23:14:30', '2025-03-19 16:12:25', 1, 'Q611784'),
(88655, 'Zabierzów', 1635, '12', 176, 'PL', 50.11425000, 19.79788000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q2230168'),
(88656, 'Zabierzów Bocheński', 1635, '12', 176, 'PL', 50.06821000, 20.31896000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q2230168'),
(88657, 'Zaborze', 1635, '12', 176, 'PL', 50.02175000, 19.24067000, '2019-10-05 23:14:30', '2025-03-19 16:10:40', 1, 'Q8063569'),
(88658, 'Zaborze', 1623, '24', 176, 'PL', 49.87279000, 18.80370000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q8063561'),
(88659, 'Zabrze', 1623, '24', 176, 'PL', 50.32492000, 18.78576000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q103892'),
(88660, 'Zabrzeg', 1623, '24', 176, 'PL', 49.91619000, 18.94292000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q8063632'),
(88661, 'Zabór', 1631, '08', 176, 'PL', 51.95194000, 15.71676000, '2019-10-05 23:14:31', '2025-03-19 16:08:41', 1, 'Q2161966'),
(88662, 'Zabłocie', 1623, '24', 176, 'PL', 49.90282000, 18.78147000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q8063676'),
(88663, 'Zabłudów', 1632, '20', 176, 'PL', 53.01442000, 23.33831000, '2019-10-05 23:14:31', '2025-03-19 16:11:50', 1, 'Q1906311'),
(88664, 'Zacisze', 1629, '02', 176, 'PL', 51.12303000, 17.07465000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q1906311'),
(88665, 'Zadzim', 1636, '10', 176, 'PL', 51.77666000, 18.84928000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q140115'),
(88666, 'Zagnańsk', 1630, '26', 176, 'PL', 50.98037000, 20.66314000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q2800089'),
(88667, 'Zagrodno', 1629, '02', 176, 'PL', 51.19134000, 15.86533000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q2458421'),
(88668, 'Zagórnik', 1635, '12', 176, 'PL', 49.83710000, 19.37868000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q2317828'),
(88669, 'Zagórz', 1626, '18', 176, 'PL', 49.51457000, 22.26706000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q1946049'),
(88670, 'Zagórze', 1635, '12', 176, 'PL', 50.09449000, 19.40357000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q8064556'),
(88671, 'Zagórzyce', 1626, '18', 176, 'PL', 50.01690000, 21.67517000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q8064437'),
(88672, 'Zagórów', 1634, '30', 176, 'PL', 52.16835000, 17.89561000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q140476'),
(88673, 'Zakliczyn', 1635, '12', 176, 'PL', 49.85589000, 20.80935000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q2458136'),
(88674, 'Zaklików', 1626, '18', 176, 'PL', 50.75769000, 22.10226000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q2319835'),
(88675, 'Zakopane', 1635, '12', 176, 'PL', 49.29899000, 19.94885000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q144786'),
(88676, 'Zakroczym', 1637, '14', 176, 'PL', 52.43351000, 20.61207000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q144773'),
(88677, 'Zakrzew', 1637, '14', 176, 'PL', 51.44095000, 21.00105000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q144773'),
(88678, 'Zakrzew', 1638, '06', 176, 'PL', 50.89004000, 22.59115000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q2534290'),
(88679, 'Zakrzewo', 1634, '30', 176, 'PL', 53.41186000, 17.15472000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q2534290'),
(88680, 'Zakrzów', 1635, '12', 176, 'PL', 49.82558000, 19.64973000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q2897131'),
(88681, 'Zakrzów', 1629, '02', 176, 'PL', 51.16624000, 17.13688000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q2897131'),
(88682, 'Zakrzówek', 1638, '06', 176, 'PL', 50.95124000, 22.38138000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q2458228'),
(88683, 'Zalas', 1635, '12', 176, 'PL', 50.08033000, 19.62132000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q8065289'),
(88684, 'Zalesie', 1626, '18', 176, 'PL', 50.01226000, 22.53262000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q8065455'),
(88685, 'Zalesie Górne', 1637, '14', 176, 'PL', 52.02768000, 21.03659000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q2403872'),
(88686, 'Zalesie i Stadion', 1629, '02', 176, 'PL', 51.11970000, 17.09194000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q2403872'),
(88687, 'Zaleszany', 1626, '18', 176, 'PL', 50.64799000, 21.89069000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q1918447'),
(88688, 'Zalewo', 1628, '28', 176, 'PL', 53.84534000, 19.60519000, '2019-10-05 23:14:31', '2025-03-19 16:12:57', 1, 'Q1001571'),
(88689, 'Zamarski', 1623, '24', 176, 'PL', 49.78254000, 18.66972000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q3497494'),
(88690, 'Zambrów', 1632, '20', 176, 'PL', 52.98550000, 22.24319000, '2019-10-05 23:14:31', '2025-03-19 16:11:50', 1, 'Q145719'),
(88691, 'Zamch', 1638, '06', 176, 'PL', 50.31713000, 23.02786000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q3574721'),
(88692, 'Zamość', 1638, '06', 176, 'PL', 50.72314000, 23.25196000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q145972'),
(88693, 'Zaniemyśl', 1634, '30', 176, 'PL', 52.15561000, 17.16228000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q146933'),
(88694, 'Zapolice', 1636, '10', 176, 'PL', 51.54319000, 18.88335000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q2458160'),
(88695, 'Zarszyn', 1626, '18', 176, 'PL', 49.58178000, 22.01283000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q2458160'),
(88696, 'Zarzecze', 1626, '18', 176, 'PL', 50.52767000, 22.19522000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q8066970'),
(88697, 'Zarzecze', 1623, '24', 176, 'PL', 49.71932000, 19.17526000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q8066983'),
(88698, 'Zarzecze', 1635, '12', 176, 'PL', 50.36715000, 19.69591000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q8066972'),
(88699, 'Zator', 1635, '12', 176, 'PL', 49.99604000, 19.43799000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q8066972'),
(88700, 'Zatory', 1637, '14', 176, 'PL', 52.59929000, 21.18258000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q2514690'),
(88701, 'Zawadzkie', 1622, '16', 176, 'PL', 50.60503000, 18.48467000, '2019-10-05 23:14:31', '2025-03-19 16:11:35', 1, 'Q167731'),
(88702, 'Zawichost', 1630, '26', 176, 'PL', 50.80743000, 21.85408000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q1922299'),
(88703, 'Zawidz', 1637, '14', 176, 'PL', 52.82745000, 19.87367000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q1922299'),
(88704, 'Zawidów', 1623, '24', 176, 'PL', 51.02546000, 15.06213000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q167756'),
(88705, 'Zawiercie', 1623, '24', 176, 'PL', 50.48766000, 19.41679000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q148827'),
(88706, 'Zawoja', 1635, '12', 176, 'PL', 49.64396000, 19.54227000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q167783'),
(88707, 'Załuski', 1637, '14', 176, 'PL', 52.51152000, 20.52864000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q167783'),
(88708, 'Zblewo', 1624, '22', 176, 'PL', 53.93366000, 18.32262000, '2019-10-05 23:14:31', '2025-03-19 16:12:14', 1, 'Q168231'),
(88709, 'Zbrosławice', 1623, '24', 176, 'PL', 50.41612000, 18.75443000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q168231'),
(88710, 'Zbuczyn', 1637, '14', 176, 'PL', 52.08974000, 22.43829000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q2188646'),
(88711, 'Zbytków', 1623, '24', 176, 'PL', 49.92288000, 18.72697000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q8067893'),
(88712, 'Zbójna', 1632, '20', 176, 'PL', 53.24293000, 21.78812000, '2019-10-05 23:14:31', '2025-03-19 16:11:50', 1, 'Q8067893'),
(88713, 'Zbójno', 1625, '04', 176, 'PL', 53.00848000, 19.15750000, '2019-10-05 23:14:31', '2025-03-19 16:06:21', 1, 'Q618409'),
(88714, 'Zbąszynek', 1631, '08', 176, 'PL', 52.24315000, 15.81654000, '2019-10-05 23:14:31', '2025-03-19 16:08:41', 1, 'Q168902'),
(88715, 'Zbąszyń', 1634, '30', 176, 'PL', 52.25090000, 15.92520000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q168905'),
(88716, 'Zduny', 1634, '30', 176, 'PL', 51.64580000, 17.37694000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q169224'),
(88717, 'Zduńska Wola', 1636, '10', 176, 'PL', 51.59915000, 18.93974000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q169237'),
(88718, 'Zdziechowice Drugie', 1626, '18', 176, 'PL', 50.78478000, 22.10999000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q8068079'),
(88719, 'Zdzieszowice', 1622, '16', 176, 'PL', 50.42482000, 18.12349000, '2019-10-05 23:14:31', '2025-03-19 16:11:35', 1, 'Q1002944'),
(88720, 'Zebrzydowice', 1635, '12', 176, 'PL', 49.89032000, 19.67291000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q4305418'),
(88721, 'Zebrzydowice', 1623, '24', 176, 'PL', 49.87793000, 18.61127000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q553629'),
(88722, 'Zelów', 1636, '10', 176, 'PL', 51.46452000, 19.21972000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q189273'),
(88723, 'Zembrzyce', 1635, '12', 176, 'PL', 49.77517000, 19.60120000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q2565965'),
(88724, 'Zgierz', 1636, '10', 176, 'PL', 51.85561000, 19.40623000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q104407'),
(88725, 'Zgorzelec', 1629, '02', 176, 'PL', 51.14942000, 15.00835000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q147929'),
(88726, 'Zgorzelisko', 1629, '02', 176, 'PL', 51.13833000, 17.13365000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q147929'),
(88727, 'Zgłobień', 1626, '18', 176, 'PL', 50.01270000, 21.85490000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q1823397'),
(88728, 'Zielona Góra', 1631, '08', 176, 'PL', 51.93768000, 15.51216000, '2019-10-05 23:14:31', '2025-03-19 16:08:41', 1, 'Q318384'),
(88729, 'Zielonka', 1637, '14', 176, 'PL', 52.30376000, 21.16018000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q199017'),
(88730, 'Zielonki', 1635, '12', 176, 'PL', 50.12091000, 19.92156000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q2273055'),
(88731, 'Ziębice', 1629, '02', 176, 'PL', 50.60122000, 17.04065000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q207100'),
(88732, 'Zmiennica', 1626, '18', 176, 'PL', 49.67652000, 21.96596000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q8073099'),
(88733, 'Zubrzyca Dolna', 1635, '12', 176, 'PL', 49.52688000, 19.67342000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q4177830'),
(88734, 'Zubrzyca Górna', 1635, '12', 176, 'PL', 49.56174000, 19.64973000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q223265'),
(88735, 'Zwierzyniec', 1638, '06', 176, 'PL', 50.61400000, 22.97512000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q1946056'),
(88736, 'Zwierzyń', 1631, '08', 176, 'PL', 52.83212000, 15.56763000, '2019-10-05 23:14:31', '2025-03-19 16:08:41', 1, 'Q7274482'),
(88737, 'Zwoleń', 1637, '14', 176, 'PL', 51.35542000, 21.58768000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q1897640'),
(88738, 'Ząbki', 1637, '14', 176, 'PL', 52.29271000, 21.10539000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q248999'),
(88739, 'Ząbkowice Śląskie', 1629, '02', 176, 'PL', 50.58969000, 16.81239000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q246693'),
(88740, 'Zębowice', 1622, '16', 176, 'PL', 50.76290000, 18.34434000, '2019-10-05 23:14:31', '2025-03-19 16:11:35', 1, 'Q2060573'),
(88741, 'Zławieś Wielka', 1625, '04', 176, 'PL', 53.09562000, 18.32897000, '2019-10-05 23:14:31', '2025-03-19 16:06:21', 1, 'Q2458261'),
(88742, 'Złocieniec', 1633, '32', 176, 'PL', 53.53286000, 16.01132000, '2019-10-05 23:14:31', '2025-03-19 16:10:04', 1, 'Q249049'),
(88743, 'Złoczew', 1636, '10', 176, 'PL', 51.41719000, 18.60363000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q249059'),
(88744, 'Złota', 1630, '26', 176, 'PL', 50.38160000, 20.59361000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q553416'),
(88745, 'Złota', 1635, '12', 176, 'PL', 49.88059000, 20.69326000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q8076167'),
(88746, 'Złotniki', 1634, '30', 176, 'PL', 52.49407000, 16.84496000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q8076202'),
(88747, 'Złotniki', 1629, '02', 176, 'PL', 51.13769000, 16.88923000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q8076202'),
(88748, 'Złotniki Kujawskie', 1625, '04', 176, 'PL', 52.89943000, 18.14564000, '2019-10-05 23:14:31', '2025-03-19 16:06:21', 1, 'Q8076202'),
(88749, 'Złotoryja', 1629, '02', 176, 'PL', 51.12637000, 15.91979000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q161899'),
(88750, 'Złoty Stok', 1629, '02', 176, 'PL', 50.44472000, 16.87586000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q249122'),
(88751, 'Złotów', 1634, '30', 176, 'PL', 53.36346000, 17.04082000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q249166'),
(88752, 'powiat Łowicki', 1636, '10', 176, 'PL', 52.10627000, 19.94606000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q1144093'),
(88754, 'Ćmielów', 1630, '26', 176, 'PL', 50.89028000, 21.51426000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q336448'),
(88755, 'Łabiszyn', 1625, '04', 176, 'PL', 52.95210000, 17.91971000, '2019-10-05 23:14:31', '2025-03-19 16:06:21', 1, 'Q345163'),
(88756, 'Łabowa', 1635, '12', 176, 'PL', 49.52766000, 20.85497000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q345163'),
(88757, 'Łabunie', 1638, '06', 176, 'PL', 50.65517000, 23.36620000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q2138970'),
(88758, 'Łagiewniki', 1629, '02', 176, 'PL', 50.79088000, 16.84457000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q2138970'),
(88759, 'Łagów', 1630, '26', 176, 'PL', 50.77517000, 21.08431000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q2138970'),
(88760, 'Łagów', 1629, '02', 176, 'PL', 51.15835000, 15.04372000, '2019-10-05 23:14:31', '2025-03-19 16:05:21', 1, 'Q276235'),
(88761, 'Łagów', 1631, '08', 176, 'PL', 52.33429000, 15.29769000, '2019-10-05 23:14:31', '2025-03-19 16:08:41', 1, 'Q2460708'),
(88762, 'Łajski', 1637, '14', 176, 'PL', 52.42873000, 20.94946000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q2561273'),
(88763, 'Łambinowice', 1622, '16', 176, 'PL', 50.53869000, 17.56096000, '2019-10-05 23:14:31', '2025-03-19 16:11:35', 1, 'Q345210'),
(88764, 'Łanięta', 1636, '10', 176, 'PL', 52.36203000, 19.28032000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q2326176'),
(88765, 'Łapanów', 1635, '12', 176, 'PL', 49.86538000, 20.29149000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q960664'),
(88766, 'Łapczyca', 1635, '12', 176, 'PL', 49.95994000, 20.38445000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q8080451'),
(88767, 'Łapsze Niżne', 1635, '12', 176, 'PL', 49.39807000, 20.24343000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q345216'),
(88768, 'Łapy', 1632, '20', 176, 'PL', 52.99110000, 22.88422000, '2019-10-05 23:14:31', '2025-03-19 16:11:50', 1, 'Q345243'),
(88769, 'Łasin', 1625, '04', 176, 'PL', 53.51794000, 19.08832000, '2019-10-05 23:14:31', '2025-03-19 16:06:21', 1, 'Q345233'),
(88770, 'Łask', 1636, '10', 176, 'PL', 51.59056000, 19.13278000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q324897'),
(88771, 'Łaskarzew', 1637, '14', 176, 'PL', 51.78993000, 21.59122000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q1753147'),
(88772, 'Łaszczów', 1638, '06', 176, 'PL', 50.53332000, 23.72562000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q2167485'),
(88773, 'Łaziska', 1638, '06', 176, 'PL', 51.14226000, 21.87919000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q2316344'),
(88774, 'Łaziska', 1623, '24', 176, 'PL', 49.93570000, 18.44707000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q3502984'),
(88775, 'Łaziska Górne', 1623, '24', 176, 'PL', 50.14952000, 18.84215000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q345293'),
(88776, 'Łazy', 1623, '24', 176, 'PL', 50.42769000, 19.39465000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q345293'),
(88777, 'Łańcut', 1626, '18', 176, 'PL', 50.06871000, 22.22912000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q345331'),
(88778, 'Łeba', 1624, '22', 176, 'PL', 54.76099000, 17.55547000, '2019-10-05 23:14:31', '2025-03-19 16:12:14', 1, 'Q345339'),
(88779, 'Łobez', 1633, '32', 176, 'PL', 53.63918000, 15.62129000, '2019-10-05 23:14:31', '2025-03-19 16:10:04', 1, 'Q196163'),
(88780, 'Łobodno', 1623, '24', 176, 'PL', 50.93079000, 18.99090000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q8080639'),
(88781, 'Łobżenica', 1634, '30', 176, 'PL', 53.26244000, 17.25574000, '2019-10-05 23:14:31', '2025-03-19 16:13:08', 1, 'Q345386'),
(88782, 'Łochów', 1637, '14', 176, 'PL', 52.53076000, 21.68158000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q345389'),
(88783, 'Łodygowice', 1623, '24', 176, 'PL', 49.72992000, 19.13939000, '2019-10-05 23:14:31', '2025-03-19 16:12:46', 1, 'Q3079106'),
(88784, 'Łomazy', 1638, '06', 176, 'PL', 51.90435000, 23.17656000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q152112'),
(88785, 'Łomianki', 1637, '14', 176, 'PL', 52.33413000, 20.88602000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q1906235'),
(88786, 'Łomża', 1632, '20', 176, 'PL', 53.17806000, 22.05935000, '2019-10-05 23:14:31', '2025-03-19 16:11:50', 1, 'Q215633'),
(88787, 'Łoniów', 1630, '26', 176, 'PL', 50.56443000, 21.52599000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q2127392'),
(88788, 'Łopiennik Górny', 1638, '06', 176, 'PL', 51.04080000, 23.01833000, '2019-10-05 23:14:31', '2025-03-19 16:07:51', 1, 'Q1895208'),
(88789, 'Łopuszka Wielka', 1626, '18', 176, 'PL', 49.93454000, 22.39305000, '2019-10-05 23:14:31', '2025-03-19 16:12:02', 1, 'Q8080749'),
(88790, 'Łopuszna', 1635, '12', 176, 'PL', 49.47281000, 20.13021000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q8080751'),
(88791, 'Łopuszno', 1630, '26', 176, 'PL', 50.94864000, 20.25081000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q2475995'),
(88792, 'Łosice', 1637, '14', 176, 'PL', 52.21129000, 22.71801000, '2019-10-05 23:14:31', '2025-03-19 16:11:22', 1, 'Q1913327'),
(88793, 'Łosiów', 1622, '16', 176, 'PL', 50.79096000, 17.56594000, '2019-10-05 23:14:31', '2025-03-19 16:11:35', 1, 'Q345442'),
(88794, 'Łososina Dolna', 1635, '12', 176, 'PL', 49.74977000, 20.63129000, '2019-10-05 23:14:31', '2025-03-19 16:10:40', 1, 'Q345442'),
(88795, 'Łowicz', 1636, '10', 176, 'PL', 52.10714000, 19.94525000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q109992'),
(88796, 'Łubianka', 1625, '04', 176, 'PL', 53.13864000, 18.48115000, '2019-10-05 23:14:31', '2025-03-19 16:06:21', 1, 'Q345492'),
(88797, 'Łubniany', 1622, '16', 176, 'PL', 50.78597000, 18.00110000, '2019-10-05 23:14:31', '2025-03-19 16:11:35', 1, 'Q661872'),
(88798, 'Łubnice', 1630, '26', 176, 'PL', 50.41164000, 21.15014000, '2019-10-05 23:14:31', '2025-03-19 16:12:25', 1, 'Q2290091'),
(88799, 'Łubnice', 1636, '10', 176, 'PL', 51.16406000, 18.29069000, '2019-10-05 23:14:31', '2025-03-19 16:09:33', 1, 'Q345499'),
(88800, 'Łubowo', 1633, '32', 176, 'PL', 53.58634000, 16.39177000, '2019-10-05 23:14:31', '2025-03-19 16:10:04', 1, 'Q8080868'),
(88801, 'Łubowo', 1634, '30', 176, 'PL', 52.51168000, 17.45333000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q556545'),
(88802, 'Łukowa', 1635, '12', 176, 'PL', 50.09300000, 20.97548000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q8081013'),
(88803, 'Łukowa', 1638, '06', 176, 'PL', 50.37426000, 22.94349000, '2019-10-05 23:14:32', '2025-03-19 16:07:51', 1, 'Q2460478'),
(88804, 'Łukowica', 1635, '12', 176, 'PL', 49.61110000, 20.48289000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q2460478'),
(88805, 'Łuków', 1638, '06', 176, 'PL', 51.92900000, 22.37956000, '2019-10-05 23:14:32', '2025-03-19 16:07:51', 1, 'Q384244'),
(88806, 'Łużna', 1635, '12', 176, 'PL', 49.71288000, 21.04637000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q2717964'),
(88807, 'Łyse', 1637, '14', 176, 'PL', 53.36443000, 21.56487000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q2192423'),
(88808, 'Łysomice', 1625, '04', 176, 'PL', 53.08629000, 18.62002000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q384287'),
(88809, 'Łyszkowice', 1636, '10', 176, 'PL', 51.98551000, 19.90654000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q1946915'),
(88810, 'Łódź', 1636, '10', 176, 'PL', 51.77058000, 19.47395000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q580'),
(88811, 'Łąck', 1637, '14', 176, 'PL', 52.46621000, 19.61137000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q429376'),
(88812, 'Łącko', 1635, '12', 176, 'PL', 49.55757000, 20.43586000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q8081149'),
(88813, 'Łączany', 1635, '12', 176, 'PL', 49.98407000, 19.57867000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q1921107'),
(88814, 'Łączna', 1630, '26', 176, 'PL', 51.00231000, 20.79712000, '2019-10-05 23:14:32', '2025-03-19 16:12:25', 1, 'Q1921107'),
(88815, 'Łąka Prudnicka', 1622, '16', 176, 'PL', 50.31061000, 17.52809000, '2019-10-05 23:14:32', '2025-03-19 16:11:35', 1, 'Q384365'),
(88816, 'Łęczna', 1638, '06', 176, 'PL', 51.30121000, 22.88135000, '2019-10-05 23:14:32', '2025-03-19 16:07:51', 1, 'Q384433'),
(88817, 'Łęczyca', 1636, '10', 176, 'PL', 52.05959000, 19.19972000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q325727'),
(88818, 'Łęczyce', 1624, '22', 176, 'PL', 54.59405000, 17.85931000, '2019-10-05 23:14:32', '2025-03-19 16:12:14', 1, 'Q2307853'),
(88819, 'Łęgowo', 1624, '22', 176, 'PL', 54.22641000, 18.64277000, '2019-10-05 23:14:32', '2025-03-19 16:12:14', 1, 'Q8081315'),
(88820, 'Łęka Opatowska', 1634, '30', 176, 'PL', 51.21231000, 18.10710000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q384519'),
(88821, 'Łękawica', 1623, '24', 176, 'PL', 49.72209000, 19.26496000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q2181417'),
(88822, 'Łęki', 1626, '18', 176, 'PL', 49.80996000, 21.66023000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q8081364'),
(88823, 'Łęki Dolne', 1626, '18', 176, 'PL', 49.97386000, 21.24739000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q2694097'),
(88824, 'Łęki Górne', 1626, '18', 176, 'PL', 49.97386000, 21.17426000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q585998'),
(88825, 'Łęki Szlacheckie', 1636, '10', 176, 'PL', 51.18774000, 19.79796000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q8081366'),
(88826, 'Łęknica', 1631, '08', 176, 'PL', 51.54148000, 14.73584000, '2019-10-05 23:14:32', '2025-03-19 16:08:41', 1, 'Q318346'),
(88827, 'Łętownia', 1626, '18', 176, 'PL', 50.32481000, 22.23401000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q8081385'),
(88828, 'Łętownia', 1635, '12', 176, 'PL', 49.69745000, 19.87109000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q8081385'),
(88829, 'Ścinawa', 1629, '02', 176, 'PL', 51.41626000, 16.42510000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q387095'),
(88830, 'Ślemień', 1623, '24', 176, 'PL', 49.71826000, 19.36735000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q2477043'),
(88831, 'Ślesin', 1625, '04', 176, 'PL', 53.16514000, 17.70258000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q8081892'),
(88832, 'Ślesin', 1634, '30', 176, 'PL', 52.37039000, 18.30644000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q387117'),
(88833, 'Śliwice', 1625, '04', 176, 'PL', 53.70875000, 18.17370000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q2458679'),
(88834, 'Śmigiel', 1634, '30', 176, 'PL', 52.01339000, 16.52704000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q387194'),
(88835, 'Śmiłowo', 1634, '30', 176, 'PL', 53.13647000, 16.92075000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q8081981'),
(88836, 'Śniadowo', 1632, '20', 176, 'PL', 53.03874000, 21.99077000, '2019-10-05 23:14:32', '2025-03-19 16:11:50', 1, 'Q8081981'),
(88837, 'Śrem', 1634, '30', 176, 'PL', 52.08868000, 17.01508000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q387260'),
(88838, 'Środa Wielkopolska', 1634, '30', 176, 'PL', 52.22843000, 17.27617000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q52903'),
(88839, 'Środa Śląska', 1629, '02', 176, 'PL', 51.16406000, 16.59508000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q387307'),
(88840, 'Śródmieście', 1637, '14', 176, 'PL', 52.22904000, 21.01644000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q7095'),
(88841, 'Świdnica', 1631, '08', 176, 'PL', 51.88836000, 15.39013000, '2019-10-05 23:14:32', '2025-03-19 16:08:41', 1, 'Q7095'),
(88842, 'Świdnica', 1629, '02', 176, 'PL', 50.84378000, 16.48859000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q387396'),
(88843, 'Świdnik', 1638, '06', 176, 'PL', 51.21898000, 22.69621000, '2019-10-05 23:14:32', '2025-03-19 16:07:51', 1, 'Q8082073'),
(88844, 'Świdwin', 1633, '32', 176, 'PL', 53.77464000, 15.77671000, '2019-10-05 23:14:32', '2025-03-19 16:10:04', 1, 'Q387406'),
(88845, 'Świebodzice', 1629, '02', 176, 'PL', 50.85974000, 16.31966000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q387449'),
(88846, 'Świebodzin', 1631, '08', 176, 'PL', 52.24751000, 15.53355000, '2019-10-05 23:14:32', '2025-03-19 16:08:41', 1, 'Q773270'),
(88847, 'Świecie', 1625, '04', 176, 'PL', 53.40953000, 18.44742000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q1817381'),
(88848, 'Świecie nad Osą', 1625, '04', 176, 'PL', 53.44400000, 19.10171000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q387467'),
(88849, 'Świedziebnia', 1625, '04', 176, 'PL', 53.15207000, 19.55463000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q387489'),
(88850, 'Świekatowo', 1625, '04', 176, 'PL', 53.41864000, 18.09731000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q387495'),
(88851, 'Świeradów-Zdrój', 1629, '02', 176, 'PL', 50.90920000, 15.34309000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q387561'),
(88852, 'Świercze', 1637, '14', 176, 'PL', 52.67055000, 20.76390000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q676351'),
(88853, 'Świerczów', 1622, '16', 176, 'PL', 50.96016000, 17.75880000, '2019-10-05 23:14:32', '2025-03-19 16:11:35', 1, 'Q676351'),
(88854, 'Świerklaniec', 1623, '24', 176, 'PL', 50.44237000, 18.93734000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q387571'),
(88855, 'Świerklany Dolne', 1623, '24', 176, 'PL', 50.01827000, 18.57702000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q8082164'),
(88856, 'Świerklany Górne', 1623, '24', 176, 'PL', 50.02765000, 18.59050000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q7483914'),
(88857, 'Świerzawa', 1629, '02', 176, 'PL', 51.01375000, 15.89516000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q2458724'),
(88858, 'Świerzno', 1633, '32', 176, 'PL', 53.96497000, 14.96544000, '2019-10-05 23:14:32', '2025-03-19 16:10:04', 1, 'Q558813'),
(88859, 'Świlcza', 1626, '18', 176, 'PL', 50.07179000, 21.89798000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q2133589'),
(88860, 'Świnice Warckie', 1636, '10', 176, 'PL', 52.04072000, 18.91786000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q387626'),
(88861, 'Świnna', 1623, '24', 176, 'PL', 49.65802000, 19.25406000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q2051836'),
(88862, 'Świnoujście', 1633, '32', 176, 'PL', 53.91053000, 14.24712000, '2019-10-05 23:14:32', '2025-03-19 16:10:04', 1, 'Q41599'),
(88863, 'Świątniki Górne', 1635, '12', 176, 'PL', 49.93429000, 19.95364000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q1280261'),
(88864, 'Święciechowa', 1634, '30', 176, 'PL', 51.85503000, 16.49805000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q387686'),
(88865, 'Święta Katarzyna', 1629, '02', 176, 'PL', 51.02596000, 17.11464000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q2867610'),
(88867, 'Świętochłowice', 1623, '24', 176, 'PL', 50.29636000, 18.91726000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q45197'),
(88868, 'Żabia Wola', 1637, '14', 176, 'PL', 52.03169000, 20.69112000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q338905'),
(88869, 'Żabieniec', 1637, '14', 176, 'PL', 52.05856000, 21.04817000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q8082981'),
(88870, 'Żabnica', 1623, '24', 176, 'PL', 49.58139000, 19.15621000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q8083013'),
(88871, 'Żabno', 1635, '12', 176, 'PL', 50.13334000, 20.88615000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q392988'),
(88872, 'Żagań', 1631, '08', 176, 'PL', 51.61759000, 15.31486000, '2019-10-05 23:14:32', '2025-03-19 16:08:41', 1, 'Q158459'),
(88873, 'Żarki', 1623, '24', 176, 'PL', 50.62518000, 19.36357000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q149411'),
(88874, 'Żarki', 1635, '12', 176, 'PL', 50.08259000, 19.35199000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q8083086'),
(88875, 'Żarki-Letnisko', 1623, '24', 176, 'PL', 50.62295000, 19.27508000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q8083087'),
(88876, 'Żarnów', 1636, '10', 176, 'PL', 51.24607000, 20.17476000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q2075387'),
(88877, 'Żary', 1631, '08', 176, 'PL', 51.64205000, 15.13727000, '2019-10-05 23:14:32', '2025-03-19 16:08:41', 1, 'Q148849'),
(88878, 'Żarów', 1629, '02', 176, 'PL', 50.94116000, 16.49466000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q393043'),
(88879, 'Żegocina', 1635, '12', 176, 'PL', 49.81395000, 20.41964000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q2096731'),
(88880, 'Żelazków', 1634, '30', 176, 'PL', 51.85418000, 18.17430000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q555064'),
(88881, 'Żelechlinek', 1636, '10', 176, 'PL', 51.71214000, 20.03460000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q2510300'),
(88882, 'Żelechów', 1637, '14', 176, 'PL', 51.81051000, 21.89721000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q393091'),
(88883, 'Żelistrzewo', 1624, '22', 176, 'PL', 54.67770000, 18.41738000, '2019-10-05 23:14:32', '2025-03-19 16:12:14', 1, 'Q4977079'),
(88884, 'Żerków', 1634, '30', 176, 'PL', 52.06877000, 17.56349000, '2019-10-05 23:14:32', '2025-03-19 16:13:08', 1, 'Q393119'),
(88885, 'Żernica', 1623, '24', 176, 'PL', 50.24770000, 18.61547000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q8083272'),
(88886, 'Żerniki Wrocławskie', 1629, '02', 176, 'PL', 51.03308000, 17.05662000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q8083293'),
(88887, 'Żmigród', 1629, '02', 176, 'PL', 51.46672000, 16.90564000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q393138'),
(88888, 'Żnin', 1625, '04', 176, 'PL', 52.84958000, 17.71992000, '2019-10-05 23:14:32', '2025-03-19 16:06:21', 1, 'Q324941'),
(88889, 'Żoliborz', 1637, '14', 176, 'PL', 52.26896000, 20.98644000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q127098'),
(88890, 'Żory', 1623, '24', 176, 'PL', 50.04523000, 18.70062000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q393171'),
(88891, 'Żołynia', 1626, '18', 176, 'PL', 50.16200000, 22.30825000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q2363612'),
(88892, 'Żukowo', 1624, '22', 176, 'PL', 54.34220000, 18.36476000, '2019-10-05 23:14:32', '2025-03-19 16:12:14', 1, 'Q161893'),
(88893, 'Żurawica', 1626, '18', 176, 'PL', 49.82348000, 22.78925000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q161893'),
(88894, 'Żurawiczki', 1626, '18', 176, 'PL', 50.01370000, 22.49949000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q8083422'),
(88895, 'Żuromin', 1637, '14', 176, 'PL', 53.06611000, 19.90894000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q393220'),
(88896, 'Żurowa', 1635, '12', 176, 'PL', 49.82636000, 21.16894000, '2019-10-05 23:14:32', '2025-03-19 16:10:40', 1, 'Q8083453'),
(88897, 'Żychlin', 1636, '10', 176, 'PL', 52.24404000, 19.62613000, '2019-10-05 23:14:32', '2025-03-19 16:09:33', 1, 'Q393228'),
(88898, 'Żyraków', 1626, '18', 176, 'PL', 50.08545000, 21.39622000, '2019-10-05 23:14:32', '2025-03-19 16:12:02', 1, 'Q8083507'),
(88899, 'Żyrardów', 1637, '14', 176, 'PL', 52.04880000, 20.44599000, '2019-10-05 23:14:32', '2025-03-19 16:11:22', 1, 'Q393251'),
(88900, 'Żyrzyn', 1638, '06', 176, 'PL', 51.49918000, 22.09170000, '2019-10-05 23:14:32', '2025-03-19 16:07:51', 1, 'Q1971654'),
(88901, 'Żywiec', 1623, '24', 176, 'PL', 49.68529000, 19.19243000, '2019-10-05 23:14:32', '2025-03-19 16:12:46', 1, 'Q373059'),
(88902, 'Żórawina', 1629, '02', 176, 'PL', 50.98080000, 17.03671000, '2019-10-05 23:14:32', '2025-03-19 16:05:21', 1, 'Q393270'),
(88903, 'Żółkiewka', 1638, '06', 176, 'PL', 50.90991000, 22.83465000, '2019-10-05 23:14:32', '2025-03-19 16:07:51', 1, 'Q393275'),
(88904, 'A dos Cunhados', 2228, '11', 177, 'PT', 39.15237000, -9.29720000, '2019-10-05 23:14:32', '2019-10-05 23:14:32', 1, 'Q300626'),
(88905, 'A dos Francos', 2240, '10', 177, 'PT', 39.32272000, -9.04743000, '2019-10-05 23:14:32', '2019-10-05 23:14:32', 1, 'Q300627'),
(88906, 'Abrantes', 2238, '14', 177, 'PT', 39.46667000, -8.20000000, '2019-10-05 23:14:32', '2019-10-05 23:14:32', 1, 'Q331191'),
(88907, 'Abraveses', 2237, '18', 177, 'PT', 40.68137000, -7.92102000, '2019-10-05 23:14:32', '2019-10-05 23:14:32', 1, 'Q168332'),
(88908, 'Abrigada', 2228, '11', 177, 'PT', 39.14416000, -9.01853000, '2019-10-05 23:14:32', '2019-10-05 23:14:32', 1, 'Q331438'),
(88909, 'Adaúfe', 2244, '03', 177, 'PT', 41.58732000, -8.39817000, '2019-10-05 23:14:32', '2020-05-01 17:23:07', 1, 'Q49343987'),
(88910, 'Aguada de Cima', 2235, '01', 177, 'PT', 40.52291000, -8.42700000, '2019-10-05 23:14:32', '2019-10-05 23:14:32', 1, 'Q338043'),
(88911, 'Aguçadoura', 2243, '13', 177, 'PT', 41.43116000, -8.77844000, '2019-10-05 23:14:32', '2020-05-01 17:23:07', 1, 'Q49344006'),
(88912, 'Alandroal', 2236, '07', 177, 'PT', 38.62924000, -7.36599000, '2019-10-05 23:14:32', '2019-10-05 23:14:32', 1, 'Q726910'),
(88913, 'Albergaria-a-Velha', 2235, '01', 177, 'PT', 40.68706000, -8.50399000, '2019-10-05 23:14:32', '2019-10-05 23:14:33', 1, 'Q1870321'),
(88914, 'Albufeira', 2239, '08', 177, 'PT', 37.08819000, -8.25030000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31877390'),
(88915, 'Alcabideche', 2228, '11', 177, 'PT', 38.73366000, -9.40928000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31877422'),
(88916, 'Alcanede', 2238, '14', 177, 'PT', 39.41501000, -8.82189000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31877466'),
(88917, 'Alcanena', 2238, '14', 177, 'PT', 39.45900000, -8.66892000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31877477'),
(88918, 'Alcanhões', 2238, '14', 177, 'PT', 39.29603000, -8.65847000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q2296664'),
(88919, 'Alcantarilha', 2239, '08', 177, 'PT', 37.13044000, -8.34623000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q2302141'),
(88920, 'Alcobaça', 2240, '10', 177, 'PT', 39.55223000, -8.97749000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q3344440'),
(88921, 'Alcochete', 2242, '15', 177, 'PT', 38.73827000, -8.97936000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q605319'),
(88922, 'Alcoentre', 2228, '11', 177, 'PT', 39.20857000, -8.95953000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31877572'),
(88923, 'Alcoutim', 2239, '08', 177, 'PT', 37.42400000, -7.65456000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1000041'),
(88924, 'Alcácer do Sal', 2242, '15', 177, 'PT', 38.37326000, -8.51444000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q850697'),
(88925, 'Aldeia Nova de São Bento', 2230, '02', 177, 'PT', 37.92603000, -7.40804000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q850697'),
(88926, 'Aldeia de Joanes', 2241, '05', 177, 'PT', 40.13905000, -7.51694000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31877731'),
(88927, 'Aldeia de Paio Pires', 2242, '15', 177, 'PT', 38.61667000, -9.08333000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31877755'),
(88928, 'Alenquer', 2228, '11', 177, 'PT', 39.05315000, -9.00928000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31878333'),
(88929, 'Alfarelos', 2246, '06', 177, 'PT', 40.15057000, -8.65326000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q2094565'),
(88930, 'Alfeizerão', 2240, '10', 177, 'PT', 39.49971000, -9.10341000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q2035332'),
(88931, 'Alfena', 2243, '13', 177, 'PT', 41.23671000, -8.52454000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1971893'),
(88932, 'Alferrarede', 2238, '14', 177, 'PT', 39.48333000, -8.16667000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q2296644'),
(88933, 'Alfragide', 2228, '11', 177, 'PT', 38.73203000, -9.21920000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q2296644'),
(88934, 'Alfândega da Fé', 2229, '04', 177, 'PT', 41.34315000, -6.96112000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q551059'),
(88935, 'Algoz', 2239, '08', 177, 'PT', 37.16301000, -8.30359000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q2652888'),
(88936, 'Algueirão', 2228, '11', 177, 'PT', 38.79764000, -9.34370000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q2652888'),
(88937, 'Algés', 2228, '11', 177, 'PT', 38.70245000, -9.22936000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q49344139'),
(88938, 'Alhadas', 2246, '06', 177, 'PT', 40.18607000, -8.79057000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1024149'),
(88939, 'Alhandra', 2228, '11', 177, 'PT', 38.92732000, -9.00864000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1803660'),
(88940, 'Alhos Vedros', 2242, '15', 177, 'PT', 38.65486000, -9.02368000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q955793'),
(88941, 'Alijó', 2234, '17', 177, 'PT', 41.28447000, -7.48545000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q712064'),
(88942, 'Aljezur', 2239, '08', 177, 'PT', 37.31745000, -8.80147000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q986095'),
(88943, 'Aljubarrota', 2240, '10', 177, 'PT', 39.56715000, -8.92925000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q2623372'),
(88944, 'Aljustrel', 2230, '02', 177, 'PT', 37.87759000, -8.16516000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31879046'),
(88945, 'Almada', 2242, '15', 177, 'PT', 38.67902000, -9.15690000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q328015'),
(88946, 'Almancil', 2239, '08', 177, 'PT', 37.08686000, -8.03074000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q374030'),
(88947, 'Almargem', 2228, '11', 177, 'PT', 38.84485000, -9.27315000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31879382'),
(88948, 'Almeirim', 2238, '14', 177, 'PT', 39.20837000, -8.62635000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31879456'),
(88949, 'Almodôvar', 2230, '02', 177, 'PT', 37.49590000, -8.09372000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q936943'),
(88950, 'Almograve', 2230, '02', 177, 'PT', 37.65665000, -8.79214000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q936943'),
(88951, 'Alpendurada', 2243, '13', 177, 'PT', 41.08944000, -8.24643000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q936943'),
(88952, 'Alpiarça', 2238, '14', 177, 'PT', 39.25712000, -8.58187000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q49280913'),
(88953, 'Alter do Chão', 2232, '12', 177, 'PT', 39.23098000, -7.74430000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q438263'),
(88954, 'Altura', 2239, '08', 177, 'PT', 37.17560000, -7.50064000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q438263'),
(88955, 'Alvaiázere', 2240, '10', 177, 'PT', 39.81951000, -8.38858000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q448229'),
(88956, 'Alvalade', 2228, '11', 177, 'PT', 38.75328000, -9.14397000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q448229'),
(88957, 'Alvito', 2230, '02', 177, 'PT', 38.24653000, -8.05038000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q449526'),
(88958, 'Alvor', 2239, '08', 177, 'PT', 37.12994000, -8.59174000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31880703'),
(88959, 'Alvorninha', 2240, '10', 177, 'PT', 39.38224000, -9.03674000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31880703'),
(88960, 'Amadora', 2228, '11', 177, 'PT', 38.75382000, -9.23083000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31880872'),
(88961, 'Amarante', 2243, '13', 177, 'PT', 41.27271000, -8.08245000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31880915'),
(88962, 'Amares', 2244, '03', 177, 'PT', 41.64718000, -8.35558000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q455012'),
(88963, 'Amiães de Baixo', 2238, '14', 177, 'PT', 39.44296000, -8.73358000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q455012'),
(88964, 'Amor', 2240, '10', 177, 'PT', 39.80404000, -8.85984000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31881200'),
(88965, 'Amora', 2242, '15', 177, 'PT', 38.62961000, -9.11557000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q474039'),
(88966, 'Amorim', 2243, '13', 177, 'PT', 41.40503000, -8.75046000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q474039'),
(88967, 'Anadia', 2235, '01', 177, 'PT', 40.43841000, -8.43352000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q395919'),
(88968, 'Angra do Heroísmo', 2233, '20', 177, 'PT', 38.65483000, -27.21734000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q193677'),
(88969, 'Angústias', 2233, '20', 177, 'PT', 38.52547000, -28.63132000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q193677'),
(88970, 'Ansião', 2240, '10', 177, 'PT', 39.93424000, -8.42045000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q569929'),
(88971, 'Anta', 2243, '13', 177, 'PT', 41.26634000, -8.62844000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31884098'),
(88972, 'Ançã', 2246, '06', 177, 'PT', 40.27161000, -8.52090000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q31884098'),
(88973, 'Apelação', 2228, '11', 177, 'PT', 38.81387000, -9.13225000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q49344405'),
(88974, 'Apúlia', 2244, '03', 177, 'PT', 41.48512000, -8.76413000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q622686'),
(88975, 'Apúlia e Fão', 2244, '03', 177, 'PT', 41.51194000, -8.77288000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q622686'),
(88976, 'Aradas', 2235, '01', 177, 'PT', 40.62084000, -8.64195000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31886714'),
(88977, 'Arazede', 2246, '06', 177, 'PT', 40.28627000, -8.64999000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q626815'),
(88978, 'Arco da Calheta', 2231, '30', 177, 'PT', 32.71502000, -17.14974000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q241974'),
(88979, 'Arcos', 2244, '03', 177, 'PT', 41.52185000, -8.42151000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q241974'),
(88980, 'Arcos', 2235, '01', 177, 'PT', 40.44779000, -8.44171000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31887166'),
(88981, 'Arcos de Valdevez', 2245, '16', 177, 'PT', 41.84668000, -8.41905000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31887240'),
(88982, 'Arcozelo', 2243, '13', 177, 'PT', 41.06187000, -8.63192000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31887272'),
(88984, 'Arganil', 2246, '06', 177, 'PT', 40.21826000, -8.05403000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31887734'),
(88985, 'Argivai', 2243, '13', 177, 'PT', 41.37744000, -8.72987000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31887734'),
(88986, 'Argoncilhe', 2235, '01', 177, 'PT', 41.02541000, -8.53885000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31887846'),
(88987, 'Armamar', 2237, '18', 177, 'PT', 41.09718000, -7.68553000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q558610'),
(88988, 'Armação de Pêra', 2239, '08', 177, 'PT', 37.10256000, -8.35695000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q680029'),
(88989, 'Arouca', 2235, '01', 177, 'PT', 40.92658000, -8.27251000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q565302'),
(88990, 'Arraiolos', 2236, '07', 177, 'PT', 38.76774000, -7.95831000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q699141'),
(88991, 'Arranhó', 2228, '11', 177, 'PT', 38.95378000, -9.13465000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q699206'),
(88992, 'Arrentela', 2242, '15', 177, 'PT', 38.62500000, -9.10151000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31889379'),
(88993, 'Arrifana', 2235, '01', 177, 'PT', 40.91565000, -8.49657000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31889400'),
(88994, 'Arrifes', 2233, '20', 177, 'PT', 37.76667000, -25.70000000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31889400'),
(88995, 'Arronches', 2232, '12', 177, 'PT', 39.12242000, -7.28619000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q700311'),
(88996, 'Arruda dos Vinhos', 2228, '11', 177, 'PT', 38.97172000, -9.08807000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q700311'),
(88997, 'Arões', 2244, '03', 177, 'PT', 41.45553000, -8.21419000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q49344656'),
(88998, 'Assafarge', 2246, '06', 177, 'PT', 40.15895000, -8.43167000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q738614'),
(88999, 'Atalaia', 2232, '12', 177, 'PT', 39.45551000, -7.87295000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q738614'),
(89000, 'Atouguia da Baleia', 2240, '10', 177, 'PT', 39.33814000, -9.32630000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31892943'),
(89001, 'Avanca', 2235, '01', 177, 'PT', 40.80771000, -8.57220000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31893496'),
(89002, 'Aveiras de Cima', 2228, '11', 177, 'PT', 39.13796000, -8.89932000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q790340'),
(89003, 'Aveiro', 2235, '01', 177, 'PT', 40.64427000, -8.64554000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q3344926'),
(89004, 'Aveleda', 2244, '03', 177, 'PT', 41.52113000, -8.46682000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q3344926'),
(89005, 'Aver-o-Mar', 2243, '13', 177, 'PT', 41.40607000, -8.77958000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31893706'),
(89006, 'Aves', 2243, '13', 177, 'PT', 41.37034000, -8.41010000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31893729'),
(89007, 'Avintes', 2243, '13', 177, 'PT', 41.10711000, -8.55131000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31893832'),
(89008, 'Avis', 2232, '12', 177, 'PT', 39.06448000, -7.89560000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q791134'),
(89009, 'Azambuja', 2228, '11', 177, 'PT', 39.07029000, -8.86822000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31894609'),
(89010, 'Azeitão', 2242, '15', 177, 'PT', 38.51919000, -9.01390000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q31894609'),
(89011, 'Azenha', 2243, '13', 177, 'PT', 41.07651000, -8.62468000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31894654'),
(89012, 'Azinhaga', 2238, '14', 177, 'PT', 39.34877000, -8.53005000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31894654'),
(89013, 'Baguim do Monte', 2243, '13', 177, 'PT', 41.19203000, -8.54118000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31897433'),
(89014, 'Baião', 2243, '13', 177, 'PT', 41.16384000, -8.03581000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q804064'),
(89015, 'Balazar', 2243, '13', 177, 'PT', 41.40435000, -8.62386000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q804064'),
(89016, 'Baltar', 2243, '13', 177, 'PT', 41.19272000, -8.38768000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31909395'),
(89017, 'Barcarena', 2228, '11', 177, 'PT', 38.73245000, -9.28000000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31910854'),
(89018, 'Barcelos', 2244, '03', 177, 'PT', 41.53174000, -8.61843000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31910871');

