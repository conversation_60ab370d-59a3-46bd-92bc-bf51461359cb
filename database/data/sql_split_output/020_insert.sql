INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(9666, 'Rakitovo', 4712, '13', 34, 'BG', 41.99012000, 24.08730000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405554'),
(9667, 'Rakovski', 4701, '16', 34, 'BG', 42.27408000, 24.94083000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q167953'),
(9668, 'Ravda', 4715, '02', 34, 'BG', 42.64185000, 27.67713000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q167953'),
(9669, 'Razgrad', 4698, '17', 34, 'B<PERSON>', 43.53333000, 26.51667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q189650'),
(9670, '<PERSON>zlog', 4699, '01', 34, 'BG', 41.88630000, 23.46714000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q929605'),
(9671, 'Rila', 4703, '10', 34, 'BG', 42.13333000, 23.13333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406517'),
(9672, 'Roman', 4711, '06', 34, 'BG', 43.15000000, 23.91667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406506'),
(9673, 'Rudozem', 4694, '21', 34, 'BG', 41.48751000, 24.84945000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405334'),
(9674, 'Ruen', 4715, '02', 34, 'BG', 42.80000000, 27.28333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q935493'),
(9675, 'Ruse', 4713, '18', 34, 'BG', 43.84872000, 25.95340000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q160173'),
(9676, 'Ruzhintsi', 4697, '05', 34, 'BG', 43.62179000, 22.83286000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q160173'),
(9677, 'Sadovo', 4701, '16', 34, 'BG', 42.13178000, 24.93999000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405215'),
(9678, 'Saedinenie', 4701, '16', 34, 'BG', 42.26667000, 24.55000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405965'),
(9679, 'Samokov', 4719, '23', 34, 'BG', 42.33700000, 23.55280000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q388070'),
(9680, 'Samuil', 4698, '17', 34, 'BG', 43.51667000, 26.75000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q388070'),
(9681, 'Sandanski', 4699, '01', 34, 'BG', 41.56667000, 23.28333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q331566'),
(9682, 'Sapareva Banya', 4703, '10', 34, 'BG', 42.28333000, 23.26667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405166'),
(9683, 'Sarafovo', 4715, '02', 34, 'BG', 42.56079000, 27.52195000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q2224290'),
(9684, 'Sarnitsa', 4712, '13', 34, 'BG', 41.73835000, 24.02435000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405972'),
(9685, 'Sarnitsa Obshtina', 4712, '13', 34, 'BG', 41.73333000, 24.03333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405972'),
(9686, 'Satovcha', 4699, '01', 34, 'BG', 41.61667000, 23.98333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q2043469'),
(9687, 'Senovo', 4713, '18', 34, 'BG', 43.65000000, 26.36667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q404734'),
(9688, 'Septemvri', 4712, '13', 34, 'BG', 42.21138000, 24.12946000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406523'),
(9689, 'Sevlievo', 4693, '07', 34, 'BG', 43.02583000, 25.11361000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q326312'),
(9690, 'Shabla', 4718, '08', 34, 'BG', 43.53983000, 28.53429000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q326312'),
(9691, 'Shipka', 4707, '24', 34, 'BG', 42.71667000, 25.33333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q133961'),
(9692, 'Silistra', 4708, '19', 34, 'BG', 44.11710000, 27.26056000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q182428'),
(9693, 'Simeonovgrad', 4704, '26', 34, 'BG', 42.03333000, 25.83333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q5474197'),
(9694, 'Simitli', 4699, '01', 34, 'BG', 41.88333000, 23.11667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q1897942'),
(9695, 'Sitovo', 4708, '19', 34, 'BG', 44.02730000, 27.01982000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q1897942'),
(9696, 'Slavyanovo', 4706, '15', 34, 'BG', 43.46667000, 24.86667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405978'),
(9697, 'Sliven', 4700, '20', 34, 'BG', 42.68583000, 26.32917000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q178385'),
(9698, 'Slivnitsa', 4719, '23', 34, 'BG', 42.85182000, 23.03792000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406470'),
(9699, 'Slivo Pole', 4713, '18', 34, 'BG', 43.94248000, 26.20464000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406747'),
(9700, 'Smolyan', 4694, '21', 34, 'BG', 41.57439000, 24.71204000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q192888'),
(9701, 'Sofia', 4705, '22', 34, 'BG', 42.69751000, 23.32415000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q472'),
(9702, 'Sozopol', 4715, '02', 34, 'BG', 42.41801000, 27.69560000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q271925'),
(9703, 'Sredets', 4715, '02', 34, 'BG', 42.34747000, 27.17898000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405646'),
(9704, 'Stamboliyski', 4701, '16', 34, 'BG', 42.13333000, 24.53333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405708'),
(9705, 'Stara Kresna', 4699, '01', 34, 'BG', 41.80000000, 23.18333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q2598096'),
(9706, 'Stara Zagora', 4707, '24', 34, 'BG', 42.43278000, 25.64194000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q170415'),
(9707, 'Stolichna Obshtina', 4705, '22', 34, 'BG', 42.68647000, 23.30561000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q4442915'),
(9708, 'Straldzha', 4716, '28', 34, 'BG', 42.60000000, 26.68333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q404981'),
(9709, 'Strazhitsa', 4709, '04', 34, 'BG', 43.23333000, 25.96667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q404981'),
(9710, 'Strelcha', 4712, '13', 34, 'BG', 42.50000000, 24.31667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405507'),
(9711, 'Strumyani', 4699, '01', 34, 'BG', 41.63333000, 23.20000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q2046578'),
(9712, 'Suhindol', 4709, '04', 34, 'BG', 43.19167000, 25.18111000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405562'),
(9713, 'Sungurlare', 4715, '02', 34, 'BG', 42.76667000, 26.78333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q990310'),
(9714, 'Suvorovo', 4717, '03', 34, 'BG', 43.33058000, 27.59908000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405550'),
(9715, 'Sveti Vlas', 4715, '02', 34, 'BG', 42.71360000, 27.75867000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406657'),
(9716, 'Svilengrad', 4704, '26', 34, 'BG', 41.76667000, 26.20000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405789'),
(9717, 'Svishtov', 4709, '04', 34, 'BG', 43.61875000, 25.35033000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q193082'),
(9718, 'Svoge', 4719, '23', 34, 'BG', 42.96667000, 23.35000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406143'),
(9719, 'Targovishte', 4714, '25', 34, 'BG', 43.25120000, 26.57215000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q185114'),
(9720, 'Tervel', 4718, '08', 34, 'BG', 43.74789000, 27.40911000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q185114'),
(9721, 'Teteven', 4710, '11', 34, 'BG', 42.91667000, 24.26667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q185114'),
(9722, 'Topolovgrad', 4704, '26', 34, 'BG', 42.08333000, 26.33333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405440'),
(9723, 'Topolovo', 4701, '16', 34, 'BG', 41.90000000, 25.00000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405440'),
(9724, 'Tran', 4695, '14', 34, 'BG', 42.83528000, 22.65167000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q406863'),
(9725, 'Troyan', 4710, '11', 34, 'BG', 42.89427000, 24.71589000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q851855'),
(9726, 'Tryavna', 4693, '07', 34, 'BG', 42.86667000, 25.50000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405136'),
(9727, 'Tsar Kaloyan', 4698, '17', 34, 'BG', 43.61667000, 26.25000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q407121'),
(9728, 'Tsarevo', 4715, '02', 34, 'BG', 42.16955000, 27.84541000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q147938'),
(9729, 'Tsenovo', 4713, '18', 34, 'BG', 43.53588000, 25.65764000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q147938'),
(9730, 'Tutrakan', 4708, '19', 34, 'BG', 44.04906000, 26.61206000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q404939'),
(9731, 'Tvarditsa', 4700, '20', 34, 'BG', 42.70000000, 25.90000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405445'),
(9732, 'Ugarchin', 4710, '11', 34, 'BG', 43.10000000, 24.41667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405567'),
(9733, 'Valchedram', 4696, '12', 34, 'BG', 43.69281000, 23.44518000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405620'),
(9734, 'Valchidol', 4717, '03', 34, 'BG', 43.40000000, 27.55000000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405620'),
(9735, 'Varna', 4717, '03', 34, 'BG', 43.21667000, 27.91667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q6506'),
(9736, 'Varshets', 4696, '12', 34, 'BG', 43.19356000, 23.28680000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q404958'),
(9737, 'Veliko Tŭrnovo', 4709, '04', 34, 'BG', 43.08124000, 25.62904000, '2019-10-05 22:34:50', '2020-05-01 17:22:39', 1, 'Q404958'),
(9738, 'Velingrad', 4712, '13', 34, 'BG', 42.02754000, 23.99155000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405114'),
(9739, 'Vetovo', 4713, '18', 34, 'BG', 43.70000000, 26.26667000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405114'),
(9740, 'Vetrino', 4717, '03', 34, 'BG', 43.31667000, 27.43333000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q405114'),
(9741, 'Vidin', 4697, '05', 34, 'BG', 43.99159000, 22.88236000, '2019-10-05 22:34:50', '2019-10-05 22:34:50', 1, 'Q178531'),
(9742, 'Vratsa', 4711, '06', 34, 'BG', 43.21000000, 23.56250000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q189502'),
(9743, 'Yablanitsa', 4710, '11', 34, 'BG', 43.03139000, 24.11278000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q405874'),
(9744, 'Yakimovo', 4696, '12', 34, 'BG', 43.63472000, 23.35350000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2994055'),
(9745, 'Yakoruda', 4699, '01', 34, 'BG', 42.02528000, 23.68417000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q406493'),
(9746, 'Yambol', 4716, '28', 34, 'BG', 42.48333000, 26.50000000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q186569'),
(9747, 'Zavet', 4698, '17', 34, 'BG', 43.76036000, 26.68063000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2995730'),
(9748, 'Zemen', 4695, '14', 34, 'BG', 42.47889000, 22.74917000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q405329'),
(9749, 'Zlataritsa', 4709, '04', 34, 'BG', 43.05000000, 25.90000000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q405323'),
(9750, 'Zlatitsa', 4719, '23', 34, 'BG', 42.71667000, 24.13333000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q405262'),
(9751, 'Zlatni Pyasatsi', 4717, '03', 34, 'BG', 43.28500000, 28.04180000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1026748'),
(9752, 'Zlatograd', 4694, '21', 34, 'BG', 41.37950000, 25.09605000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1790862'),
(9753, 'Al Muharraq', 1995, '15', 18, 'BH', 26.25722000, 50.61194000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q270500'),
(9754, 'Al Ḩadd', 1995, '15', 18, 'BH', 26.24556000, 50.65417000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q3360322'),
(9755, 'Ar Rifā‘', 1993, '14', 18, 'BH', 26.13000000, 50.55500000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q1771398'),
(9756, 'Dār Kulayb', 1993, '14', 18, 'BH', 26.06861000, 50.50389000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q5221908'),
(9757, 'Jidd Ḩafş', 1992, '13', 18, 'BH', 26.21861000, 50.54778000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q248302'),
(9758, 'Madīnat Ḩamad', 1996, '16', 18, 'BH', 26.11528000, 50.50694000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q2088687'),
(9759, 'Madīnat ‘Īsá', 1993, '14', 18, 'BH', 26.17361000, 50.54778000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q1017673'),
(9760, 'Manama', 1992, '13', 18, 'BH', 26.22787000, 50.58565000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3882'),
(9761, 'Sitrah', 1992, '13', 18, 'BH', 26.15472000, 50.62056000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1416753'),
(9762, 'Bubanza', 3196, 'BB', 36, 'BI', -3.08040000, 29.39100000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2020432'),
(9763, 'Bujumbura', 3198, 'BM', 36, 'BI', -3.38193000, 29.36142000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3854'),
(9764, 'Bururi', 3202, 'BR', 36, 'BI', -3.94877000, 29.62438000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q992247'),
(9765, 'Cankuzo', 3201, 'CA', 36, 'BI', -3.21860000, 30.55280000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2669640'),
(9766, 'Cibitoke', 3190, 'CI', 36, 'BI', -2.88690000, 29.12480000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q746329'),
(9767, 'Gitega', 3197, 'GI', 36, 'BI', -3.42708000, 29.92463000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q746329'),
(9768, 'Karuzi', 3194, 'KR', 36, 'BI', -3.10139000, 30.16278000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q684152'),
(9769, 'Kayanza', 3192, 'KY', 36, 'BI', -2.92210000, 29.62930000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2305839'),
(9770, 'Kirundo', 3195, 'KI', 36, 'BI', -2.58450000, 30.09590000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2640808'),
(9771, 'Makamba', 3188, 'MA', 36, 'BI', -4.13480000, 29.80400000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1286083'),
(9772, 'Muramvya', 3193, 'MU', 36, 'BI', -3.26820000, 29.60790000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q557529'),
(9773, 'Muyinga', 3186, 'MY', 36, 'BI', -2.84510000, 30.34140000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2006076'),
(9774, 'Mwaro', 3187, 'MW', 36, 'BI', -3.51128000, 29.70334000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2458075'),
(9775, 'Ngozi', 3199, 'NG', 36, 'BI', -2.90750000, 29.83060000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q929689'),
(9776, 'Rumonge', 3185, 'RM', 36, 'BI', -3.97360000, 29.43860000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3110231'),
(9777, 'Rutana', 3189, 'RT', 36, 'BI', -3.92790000, 29.99200000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q246069'),
(9778, 'Ruyigi', 3191, 'RY', 36, 'BI', -3.47639000, 30.24861000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1018116'),
(9779, 'Abomey', 3073, 'ZO', 24, 'BJ', 7.18286000, 1.99119000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q189685'),
(9780, 'Abomey-Calavi', 3079, 'AQ', 24, 'BJ', 6.44852000, 2.35566000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q189685'),
(9781, 'Allada', 3079, 'AQ', 24, 'BJ', 6.66547000, 2.15138000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q189685'),
(9782, 'Banikoara', 3077, 'AL', 24, 'BJ', 11.29845000, 2.43856000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q189685'),
(9783, 'Bassila', 3072, 'DO', 24, 'BJ', 9.00814000, 1.66540000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q189685'),
(9784, 'Bembèrèkè', 3078, 'BO', 24, 'BJ', 10.22827000, 2.66335000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q189685'),
(9785, 'Bohicon', 3073, 'ZO', 24, 'BJ', 7.17826000, 2.06670000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q189685'),
(9786, 'Bétérou', 3078, 'BO', 24, 'BJ', 9.19916000, 2.25855000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q2756784'),
(9787, 'Commune of Agbangnizoun', 3073, 'ZO', 24, 'BJ', 7.07600000, 1.96100000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q527734'),
(9788, 'Commune of Athieme', 3075, 'MO', 24, 'BJ', 6.56924000, 1.70259000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2682057'),
(9789, 'Commune of Djougou', 3072, 'DO', 24, 'BJ', 9.64300000, 1.89600000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2682057'),
(9790, 'Comé', 3070, 'CO', 24, 'BJ', 6.40764000, 1.88198000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q2682057'),
(9791, 'Cotonou', 3081, 'LI', 24, 'BJ', 6.36536000, 2.41833000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q43595'),
(9792, 'Cové', 3073, 'ZO', 24, 'BJ', 7.22097000, 2.34017000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q43595'),
(9793, 'Dassa-Zoumé', 3070, 'CO', 24, 'BJ', 7.75000000, 2.18333000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q43595'),
(9794, 'Djakotomey', 3071, 'KO', 24, 'BJ', 6.90000000, 1.71667000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3032740'),
(9795, 'Djougou', 3072, 'DO', 24, 'BJ', 9.70853000, 1.66598000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3032740'),
(9796, 'Dogbo', 3071, 'KO', 24, 'BJ', 6.79911000, 1.78073000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3032740'),
(9797, 'Guilmaro', 3076, 'AK', 24, 'BJ', 10.56583000, 1.72444000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3032740'),
(9798, 'Hinvi', 3079, 'AQ', 24, 'BJ', 6.76667000, 2.16667000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3032740'),
(9799, 'Hévié', 3079, 'AQ', 24, 'BJ', 6.41667000, 2.25000000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q3032740'),
(9800, 'Kandi', 3077, 'AL', 24, 'BJ', 11.13417000, 2.93861000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3032740'),
(9801, 'Kétou', 3074, 'PL', 24, 'BJ', 7.36332000, 2.59978000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q3032740'),
(9802, 'Lokossa', 3075, 'MO', 24, 'BJ', 6.63869000, 1.71674000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3032740'),
(9803, 'Malanville', 3077, 'AL', 24, 'BJ', 11.86819000, 3.38327000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2337446'),
(9804, 'Natitingou', 3076, 'AK', 24, 'BJ', 10.30416000, 1.37962000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2337446'),
(9805, 'Nikki', 3078, 'BO', 24, 'BJ', 9.94009000, 3.21075000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2337446'),
(9806, 'Ouidah', 3079, 'AQ', 24, 'BJ', 6.36307000, 2.08506000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2337446'),
(9807, 'Parakou', 3078, 'BO', 24, 'BJ', 9.33716000, 2.63031000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2337446'),
(9808, 'Pobé', 3074, 'PL', 24, 'BJ', 6.98008000, 2.66490000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q2337446'),
(9809, 'Porto-Novo', 3080, 'OU', 24, 'BJ', 6.49646000, 2.60359000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3799'),
(9810, 'Sakété', 3074, 'PL', 24, 'BJ', 6.73618000, 2.65866000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q3799'),
(9811, 'Savalou', 3070, 'CO', 24, 'BJ', 7.92807000, 1.97558000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q3799'),
(9812, 'Savé', 3070, 'CO', 24, 'BJ', 8.03424000, 2.48660000, '2019-10-05 22:34:51', '2020-05-01 17:22:35', 1, 'Q21788742'),
(9813, 'Tanguieta', 3076, 'AK', 24, 'BJ', 11.03621000, 1.41757000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2269248'),
(9815, 'Tchaourou', 3078, 'BO', 24, 'BJ', 8.88649000, 2.59753000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q2269248'),
(9816, 'Bandar Seri Begawan', 1216, 'BM', 33, 'BN', 4.89035000, 114.94006000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q9279'),
(9817, 'Bangar', 1218, 'TE', 33, 'BN', 4.70861000, 115.07167000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1125479'),
(9818, 'Berakas A', 1216, 'BM', 33, 'BN', 4.97032000, 114.92989000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1125479'),
(9819, 'Kapok', 1216, 'BM', 33, 'BN', 5.02447000, 115.04664000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q1125479'),
(9820, 'Kuala Belait', 1217, 'BE', 33, 'BN', 4.58361000, 114.23120000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q588045'),
(9821, 'Mentiri', 1216, 'BM', 33, 'BN', 4.97058000, 115.02078000, '2019-10-05 22:34:51', '2019-10-05 22:34:51', 1, 'Q588045'),
(9822, 'Serasa', 1216, 'BM', 33, 'BN', 5.01718000, 115.05841000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q588045'),
(9823, 'Seria', 1217, 'BE', 33, 'BN', 4.60637000, 114.32476000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q2159472'),
(9824, 'Tutong', 1219, 'TU', 33, 'BN', 4.80278000, 114.64917000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q2299301'),
(9825, 'Abapó', 3377, 'S', 27, 'BO', -18.88279000, -63.38026000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q305531'),
(9826, 'Achacachi', 3380, 'L', 27, 'BO', -16.05000000, -68.68333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1553175'),
(9827, 'Aiquile', 3381, 'C', 27, 'BO', -18.20408000, -65.18068000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1617416'),
(9828, 'Amarete', 3380, 'L', 27, 'BO', -15.23675000, -68.98462000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q454982'),
(9829, 'Arani', 3381, 'C', 27, 'BO', -17.56854000, -65.76883000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1026281'),
(9830, 'Ascención de Guarayos', 3377, 'S', 27, 'BO', -15.89299000, -63.18855000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1026281'),
(9831, 'Ascensión', 3377, 'S', 27, 'BO', -15.70000000, -63.08333000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1026281'),
(9832, 'Atocha', 3383, 'P', 27, 'BO', -20.93515000, -66.22139000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q637550'),
(9833, 'Batallas', 3380, 'L', 27, 'BO', -16.30000000, -68.53333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q810710'),
(9834, 'Bermejo', 3378, 'T', 27, 'BO', -22.73206000, -64.33724000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q634479'),
(9835, 'Betanzos', 3383, 'P', 27, 'BO', -19.55293000, -65.45395000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q634479'),
(9836, 'Bolivar', 3381, 'C', 27, 'BO', -17.96667000, -66.53333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q785395'),
(9837, 'Boyuibe', 3377, 'S', 27, 'BO', -20.43227000, -63.28147000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q896073'),
(9838, 'Buena Vista', 3377, 'S', 27, 'BO', -17.45830000, -63.67126000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1001758'),
(9839, 'Camargo', 3382, 'H', 27, 'BO', -20.64064000, -65.20893000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1642851'),
(9840, 'Camiri', 3377, 'S', 27, 'BO', -20.03849000, -63.51833000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1026256'),
(9841, 'Capinota', 3381, 'C', 27, 'BO', -17.71113000, -66.26082000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1034638'),
(9842, 'Caranavi', 3380, 'L', 27, 'BO', -15.83652000, -67.56901000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1034638'),
(9843, 'Challapata', 3376, 'O', 27, 'BO', -18.90208000, -66.77048000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q2307361'),
(9844, 'Charagua', 3377, 'S', 27, 'BO', -19.79151000, -63.19864000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q598031'),
(9845, 'Chimoré', 3381, 'C', 27, 'BO', -16.99417000, -65.15330000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q990746'),
(9846, 'Chulumani', 3380, 'L', 27, 'BO', -16.40855000, -67.52940000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1766619'),
(9847, 'Cliza', 3381, 'C', 27, 'BO', -17.58777000, -65.93253000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q780815'),
(9848, 'Cobija', 3379, 'N', 27, 'BO', -11.02671000, -68.76918000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q34995'),
(9849, 'Cochabamba', 3381, 'C', 27, 'BO', -17.38950000, -66.15680000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q34995'),
(9850, 'Colchani', 3381, 'C', 27, 'BO', -17.31667000, -66.71667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q34995'),
(9851, 'Colchani', 3383, 'P', 27, 'BO', -20.30000000, -66.93333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1107702'),
(9852, 'Colomi', 3381, 'C', 27, 'BO', -17.35000000, -65.86667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q990698'),
(9853, 'Colquechaca', 3383, 'P', 27, 'BO', -18.70031000, -66.00397000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q338235'),
(9854, 'Colquiri', 3380, 'L', 27, 'BO', -17.38918000, -67.12671000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1014448'),
(9855, 'Comarapa', 3377, 'S', 27, 'BO', -17.91537000, -64.53163000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1002560'),
(9856, 'Concepción', 3377, 'S', 27, 'BO', -16.43333000, -60.90000000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1002560'),
(9857, 'Coripata', 3380, 'L', 27, 'BO', -16.30000000, -67.60000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1014537'),
(9858, 'Coroico', 3380, 'L', 27, 'BO', -16.19386000, -67.72998000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1026263'),
(9859, 'Cotoca', 3377, 'S', 27, 'BO', -17.81667000, -63.05000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1641337'),
(9860, 'Curahuara de Carangas', 3380, 'L', 27, 'BO', -17.86667000, -68.43333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q647877'),
(9861, 'Enrique Baldivieso', 3383, 'P', 27, 'BO', -21.38323000, -67.60368000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q647877'),
(9862, 'Entre Ríos', 3378, 'T', 27, 'BO', -21.52661000, -64.17299000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q328674'),
(9863, 'Eucaliptus', 3380, 'L', 27, 'BO', -17.58333000, -67.51667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1016533'),
(9864, 'German Busch', 3377, 'S', 27, 'BO', -17.77317000, -63.19087000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1016533'),
(9865, 'Guanay', 3380, 'L', 27, 'BO', -15.49756000, -67.88332000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q959746'),
(9866, 'Guarayos', 3377, 'S', 27, 'BO', -15.82132000, -63.24280000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q959746'),
(9867, 'Guayaramerín', 3375, 'B', 27, 'BO', -10.82580000, -65.35810000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1026253'),
(9868, 'Huanuni', 3376, 'O', 27, 'BO', -18.28900000, -66.83583000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1632857'),
(9869, 'Huarina', 3380, 'L', 27, 'BO', -16.20000000, -68.63333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1591297'),
(9870, 'Huatajata', 3380, 'L', 27, 'BO', -16.20000000, -68.68333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q21801004'),
(9871, 'Independencia', 3381, 'C', 27, 'BO', -17.08389000, -66.81804000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1661081'),
(9872, 'Irpa Irpa', 3381, 'C', 27, 'BO', -17.71667000, -66.26667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1673170'),
(9873, 'Jorochito', 3377, 'S', 27, 'BO', -18.05514000, -63.42821000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q976975'),
(9874, 'José Manuel Pando', 3380, 'L', 27, 'BO', -16.18970000, -67.72664000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q976975'),
(9875, 'La Bélgica', 3377, 'S', 27, 'BO', -17.55000000, -63.21667000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1798658'),
(9876, 'La Paz', 3380, 'L', 27, 'BO', -16.50000000, -68.15000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1491'),
(9877, 'Lahuachaca', 3380, 'L', 27, 'BO', -17.37054000, -67.67501000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1800652'),
(9878, 'Limoncito', 3377, 'S', 27, 'BO', -18.03104000, -63.40523000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1804166'),
(9879, 'Litoral de Atacama', 3376, 'O', 27, 'BO', -18.76071000, -68.24295000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1804166'),
(9880, 'Llallagua', 3383, 'P', 27, 'BO', -18.42426000, -66.58388000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q283043'),
(9881, 'Los Negros', 3377, 'S', 27, 'BO', -17.73333000, -63.43333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q283043'),
(9882, 'Machacamarca', 3376, 'O', 27, 'BO', -18.17251000, -67.02099000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1312330'),
(9883, 'Mairana', 3377, 'S', 27, 'BO', -18.11919000, -63.95965000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1003511'),
(9884, 'Mapiri', 3380, 'L', 27, 'BO', -15.25000000, -68.16667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1014433'),
(9885, 'Mineros', 3377, 'S', 27, 'BO', -17.11876000, -63.23100000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1153550'),
(9886, 'Mizque', 3381, 'C', 27, 'BO', -17.94101000, -65.34016000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1896917'),
(9887, 'Monteagudo', 3382, 'H', 27, 'BO', -19.79989000, -63.95461000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1819486'),
(9888, 'Montero', 3377, 'S', 27, 'BO', -17.33866000, -63.25050000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q990706'),
(9889, 'Nor Carangas Province', 3376, 'O', 27, 'BO', -17.83750000, -67.94330000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q990706'),
(9890, 'Okinawa Número Uno', 3377, 'S', 27, 'BO', -17.23333000, -62.81667000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1108266'),
(9891, 'Oruro', 3376, 'O', 27, 'BO', -17.98333000, -67.15000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q35246'),
(9892, 'Padilla', 3382, 'H', 27, 'BO', -19.30878000, -64.30273000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1584051'),
(9893, 'Pailón', 3377, 'S', 27, 'BO', -17.65000000, -62.75000000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1725562'),
(9894, 'Patacamaya', 3380, 'L', 27, 'BO', -17.23580000, -67.92169000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q490991'),
(9895, 'Paurito', 3377, 'S', 27, 'BO', -17.88333000, -62.96667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1368696'),
(9896, 'Poopó', 3376, 'O', 27, 'BO', -18.38026000, -66.96695000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1368696'),
(9897, 'Portachuelo', 3377, 'S', 27, 'BO', -17.35168000, -63.39282000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1626079'),
(9898, 'Potosí', 3383, 'P', 27, 'BO', -19.58361000, -65.75306000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q35045'),
(9899, 'Provincia Abuná', 3379, 'N', 27, 'BO', -10.50000000, -66.50000000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1471235'),
(9900, 'Provincia Alonzo de Ibáñez', 3383, 'P', 27, 'BO', -18.50000000, -66.33333000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1360913'),
(9901, 'Provincia Andrés Ibáñez', 3377, 'S', 27, 'BO', -17.75000000, -63.25000000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q582935'),
(9902, 'Provincia Arani', 3381, 'C', 27, 'BO', -17.50000000, -65.50000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1325748'),
(9903, 'Provincia Arce', 3378, 'T', 27, 'BO', -22.16667000, -64.33333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q679350'),
(9904, 'Provincia Aroma', 3380, 'L', 27, 'BO', -17.50000000, -68.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q490997'),
(9905, 'Provincia Arque', 3381, 'C', 27, 'BO', -17.75000000, -66.66667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1366072'),
(9906, 'Provincia Avaroa', 3376, 'O', 27, 'BO', -19.00000000, -66.66667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1366072'),
(9907, 'Provincia Avilez', 3378, 'T', 27, 'BO', -21.66667000, -65.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1366072'),
(9908, 'Provincia Ayopaya', 3381, 'C', 27, 'BO', -16.50000000, -66.58333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1365999'),
(9909, 'Provincia Azurduy', 3382, 'H', 27, 'BO', -20.00000000, -64.50000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1477938'),
(9910, 'Provincia Bautista Saavedra', 3380, 'L', 27, 'BO', -15.00000000, -68.91667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1166108'),
(9911, 'Provincia Belisario Boeto', 3382, 'H', 27, 'BO', -18.91667000, -64.33333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q638697'),
(9912, 'Provincia Camacho', 3380, 'L', 27, 'BO', -15.50000000, -69.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q638697'),
(9913, 'Provincia Campero', 3381, 'C', 27, 'BO', -18.33333000, -65.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q638697'),
(9914, 'Provincia Capinota', 3381, 'C', 27, 'BO', -17.83333000, -66.16667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1366004'),
(9915, 'Provincia Carangas', 3376, 'O', 27, 'BO', -18.33333000, -67.75000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1420332'),
(9916, 'Provincia Carrasco', 3381, 'C', 27, 'BO', -17.50000000, -65.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1366060'),
(9917, 'Provincia Cercado', 3376, 'O', 27, 'BO', -17.91667000, -67.08333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q590859'),
(9918, 'Provincia Cercado', 3381, 'C', 27, 'BO', -17.33333000, -66.16667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q200795'),
(9919, 'Provincia Cercado', 3378, 'T', 27, 'BO', -21.58333000, -64.58333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q328661'),
(9920, 'Provincia Cercado', 3375, 'B', 27, 'BO', -14.50000000, -64.33333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q328661'),
(9921, 'Provincia Chaparé', 3381, 'C', 27, 'BO', -16.50000000, -65.50000000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1434538'),
(9922, 'Provincia Charcas', 3383, 'P', 27, 'BO', -18.33333000, -65.83333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q936944'),
(9923, 'Provincia Chayanta', 3383, 'P', 27, 'BO', -18.91667000, -66.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q733870'),
(9924, 'Provincia Chiquitos', 3377, 'S', 27, 'BO', -18.00000000, -60.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1147271'),
(9925, 'Provincia Cordillera', 3377, 'S', 27, 'BO', -19.00000000, -61.50000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q652371'),
(9926, 'Provincia Daniel Campos', 3383, 'P', 27, 'BO', -20.25000000, -68.25000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q938354'),
(9927, 'Provincia Esteban Arce', 3381, 'C', 27, 'BO', -17.66667000, -66.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q283889'),
(9928, 'Provincia Florida', 3377, 'S', 27, 'BO', -18.00000000, -63.91667000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q818'),
(9929, 'Provincia Franz Tamayo', 3380, 'L', 27, 'BO', -14.50000000, -68.33333000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1355903'),
(9930, 'Provincia General Bilbao', 3383, 'P', 27, 'BO', -18.08333000, -66.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q1355903'),
(9931, 'Provincia General Federico Román', 3379, 'N', 27, 'BO', -10.33333000, -65.88333000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1473313'),
(9932, 'Provincia General José Ballivián', 3375, 'B', 27, 'BO', -14.00000000, -67.08333000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1473313'),
(9933, 'Provincia Germán Jordán', 3381, 'C', 27, 'BO', -17.58333000, -65.91667000, '2019-10-05 22:34:52', '2020-05-01 17:22:35', 1, 'Q1295948'),
(9934, 'Provincia Gran Chaco', 3378, 'T', 27, 'BO', -21.66667000, -63.00000000, '2019-10-05 22:34:52', '2019-10-05 22:34:52', 1, 'Q267695'),
(9935, 'Provincia Gualberto Villarroel', 3380, 'L', 27, 'BO', -17.58333000, -68.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q267695'),
(9936, 'Provincia Hernando Siles', 3382, 'H', 27, 'BO', -20.16667000, -64.25000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q267695'),
(9937, 'Provincia Ichilo', 3377, 'S', 27, 'BO', -17.00000000, -64.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1215525'),
(9938, 'Provincia Ingavi', 3380, 'L', 27, 'BO', -16.83333000, -68.66667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1355971'),
(9939, 'Provincia Inquisivi', 3380, 'L', 27, 'BO', -17.00000000, -67.08333000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1325725'),
(9940, 'Provincia Iturralde', 3380, 'L', 27, 'BO', -13.00000000, -68.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1325725'),
(9941, 'Provincia Iténez', 3375, 'B', 27, 'BO', -13.41667000, -63.50000000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1325725'),
(9942, 'Provincia Ladislao Cabrera', 3376, 'O', 27, 'BO', -19.50000000, -67.66667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1420326'),
(9943, 'Provincia Larecaja', 3380, 'L', 27, 'BO', -15.50000000, -68.33333000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1325760'),
(9944, 'Provincia Linares', 3383, 'P', 27, 'BO', -19.83333000, -65.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1325760'),
(9945, 'Provincia Loayza', 3380, 'L', 27, 'BO', -17.00000000, -67.66667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1424412'),
(9946, 'Provincia Los Andes', 3380, 'L', 27, 'BO', -16.33333000, -68.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1355950'),
(9947, 'Provincia Luis Calvo', 3382, 'H', 27, 'BO', -20.66667000, -63.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1355950'),
(9948, 'Provincia Madre de Dios', 3379, 'N', 27, 'BO', -11.58333000, -67.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q241217'),
(9949, 'Provincia Mamoré', 3375, 'B', 27, 'BO', -13.00000000, -64.91667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q241217'),
(9950, 'Provincia Manco Kapac', 3380, 'L', 27, 'BO', -16.00000000, -69.16667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1325738'),
(9951, 'Provincia Manuel María Caballero', 3377, 'S', 27, 'BO', -17.83333000, -64.41667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1215606'),
(9952, 'Provincia Manuripi', 3379, 'N', 27, 'BO', -11.33333000, -67.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1473356'),
(9953, 'Provincia Marbán', 3375, 'B', 27, 'BO', -15.66667000, -64.33333000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1473356'),
(9954, 'Provincia Mizque', 3381, 'C', 27, 'BO', -17.90655000, -65.39440000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1427763'),
(9955, 'Provincia Modesto Omiste', 3383, 'P', 27, 'BO', -21.91667000, -65.58333000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q120514'),
(9956, 'Provincia Moxos', 3375, 'B', 27, 'BO', -15.16667000, -65.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q120514'),
(9957, 'Provincia Murillo', 3380, 'L', 27, 'BO', -16.33333000, -68.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q120514'),
(9958, 'Provincia Muñecas', 3380, 'L', 27, 'BO', -15.33333000, -68.66667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1355881'),
(9959, 'Provincia Méndez', 3378, 'T', 27, 'BO', -21.16667000, -64.91667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1355881'),
(9960, 'Provincia Nicolás Suárez', 3379, 'N', 27, 'BO', -11.33333000, -68.50000000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1473307'),
(9961, 'Provincia Nor Chichas', 3383, 'P', 27, 'BO', -20.66667000, -66.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1360821'),
(9962, 'Provincia Nor Cinti', 3382, 'H', 27, 'BO', -20.33333000, -65.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1420337'),
(9963, 'Provincia Nor Lípez', 3383, 'P', 27, 'BO', -20.50000000, -67.83333000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1319477'),
(9964, 'Provincia Nor Yungas', 3380, 'L', 27, 'BO', -16.00000000, -67.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q389311'),
(9965, 'Provincia Omasuyos', 3380, 'L', 27, 'BO', -15.91667000, -68.83333000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q951271'),
(9966, 'Provincia Oropeza', 3382, 'H', 27, 'BO', -18.66667000, -65.16667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q21799670'),
(9967, 'Provincia O’Connor', 3378, 'T', 27, 'BO', -21.58333000, -64.16667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q21799670'),
(9968, 'Provincia Pacajes', 3380, 'L', 27, 'BO', -17.50000000, -69.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1355941'),
(9969, 'Provincia Pantaleón Dalence', 3376, 'O', 27, 'BO', -18.33333000, -66.91667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1355941'),
(9970, 'Provincia Poopó', 3376, 'O', 27, 'BO', -18.50000000, -66.91667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1420274'),
(9971, 'Provincia Punata', 3381, 'C', 27, 'BO', -17.50000000, -65.91667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1420274'),
(9972, 'Provincia Quijarro', 3383, 'P', 27, 'BO', -20.00000000, -66.33333000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1360766'),
(9973, 'Provincia Quillacollo', 3381, 'C', 27, 'BO', -17.50000000, -66.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1360766'),
(9974, 'Provincia Rafael Bustillo', 3383, 'P', 27, 'BO', -18.33333000, -66.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1360897'),
(9975, 'Provincia Saavedra', 3383, 'P', 27, 'BO', -19.25000000, -65.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q339416'),
(9976, 'Provincia Sabaya', 3376, 'O', 27, 'BO', -19.00000000, -68.58333000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1425469'),
(9977, 'Provincia Sajama', 3376, 'O', 27, 'BO', -18.20000000, -68.55000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1427724'),
(9978, 'Provincia San Pedro de Totora', 3376, 'O', 27, 'BO', -17.83333000, -68.20000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1427704'),
(9979, 'Provincia Santiesteban', 3377, 'S', 27, 'BO', -16.50000000, -63.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1427704'),
(9980, 'Provincia Sara', 3377, 'S', 27, 'BO', -16.83333000, -63.91667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1215541'),
(9981, 'Provincia Saucari', 3376, 'O', 27, 'BO', -18.25000000, -67.41667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1427714'),
(9982, 'Provincia Sud Chichas', 3383, 'P', 27, 'BO', -21.50000000, -66.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1427714'),
(9983, 'Provincia Sud Cinti', 3382, 'H', 27, 'BO', -20.91667000, -64.91667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1427714'),
(9984, 'Provincia Sud Lípez', 3383, 'P', 27, 'BO', -22.00000000, -67.50000000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q537564'),
(9985, 'Provincia Sud Yungas', 3380, 'L', 27, 'BO', -16.50000000, -67.50000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q609394'),
(9986, 'Provincia Tapacarí', 3381, 'C', 27, 'BO', -17.51146000, -66.61965000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q277886'),
(9987, 'Provincia Tomina', 3382, 'H', 27, 'BO', -19.50000000, -64.16667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q277886'),
(9988, 'Provincia Tomás Barron', 3376, 'O', 27, 'BO', -17.83333000, -68.25000000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q658062'),
(9989, 'Provincia Tomás Frías', 3383, 'P', 27, 'BO', -19.33333000, -66.00000000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1339683'),
(9990, 'Provincia Vaca Diez', 3375, 'B', 27, 'BO', -11.66667000, -66.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1339683'),
(9991, 'Provincia Vallegrande', 3377, 'S', 27, 'BO', -18.50000000, -64.16667000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1215562'),
(9992, 'Provincia Velasco', 3377, 'S', 27, 'BO', -15.50000000, -61.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1215518'),
(9993, 'Provincia Warnes', 3377, 'S', 27, 'BO', -17.33333000, -63.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1215506'),
(9994, 'Provincia Yacuma', 3375, 'B', 27, 'BO', -13.50000000, -66.00000000, '2019-10-05 22:34:53', '2019-10-05 22:34:53', 1, 'Q1215506'),
(9995, 'Provincia Yamparáez', 3382, 'H', 27, 'BO', -19.16667000, -64.91667000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1420314'),
(9996, 'Provincia Zudáñez', 3382, 'H', 27, 'BO', -18.91667000, -64.83333000, '2019-10-05 22:34:53', '2020-05-01 17:22:35', 1, 'Q1420314'),
(9997, 'Provincia Ángel Sandoval', 3377, 'S', 27, 'BO', -17.50000000, -58.50000000, '2019-10-05 22:34:54', '2020-05-01 17:22:35', 1, 'Q1215599'),
(9998, 'Provincia Ñuflo de Chávez', 3377, 'S', 27, 'BO', -15.91667000, -62.50000000, '2019-10-05 22:34:54', '2020-05-01 17:22:35', 1, 'Q1215592'),
(9999, 'Puerto Quijarro', 3377, 'S', 27, 'BO', -17.78333000, -57.76667000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2117434'),
(10000, 'Puerto de Mejillones', 3376, 'O', 27, 'BO', -18.99266000, -68.68446000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2117434'),
(10001, 'Puesto de Pailas', 3377, 'S', 27, 'BO', -17.65000000, -62.80000000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1506112'),
(10002, 'Punata', 3381, 'C', 27, 'BO', -17.54234000, -65.83472000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1026219'),
(10003, 'Quillacollo', 3381, 'C', 27, 'BO', -17.39228000, -66.27838000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1002090'),
(10004, 'Quime', 3380, 'L', 27, 'BO', -16.98333000, -67.21667000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q590404'),
(10005, 'Reyes', 3375, 'B', 27, 'BO', -14.29520000, -67.33624000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q913102'),
(10006, 'Riberalta', 3375, 'B', 27, 'BO', -11.00654000, -66.06312000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q941357'),
(10007, 'Roboré', 3377, 'S', 27, 'BO', -18.33473000, -59.76142000, '2019-10-05 22:34:54', '2020-05-01 17:22:35', 1, 'Q941418'),
(10008, 'Rurrenabaque', 3375, 'B', 27, 'BO', -14.44125000, -67.52781000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q749372'),
(10009, 'Sacaba', 3381, 'C', 27, 'BO', -17.39799000, -66.03825000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q995727'),
(10010, 'Samaipata', 3377, 'S', 27, 'BO', -18.18005000, -63.87552000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1014461'),
(10011, 'San Borja', 3375, 'B', 27, 'BO', -14.81667000, -66.85000000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2307472'),
(10012, 'San Carlos', 3377, 'S', 27, 'BO', -17.40000000, -63.75000000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2307472'),
(10013, 'San Ignacio de Velasco', 3377, 'S', 27, 'BO', -16.36667000, -60.95000000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q995752'),
(10014, 'San Juan del Surutú', 3377, 'S', 27, 'BO', -17.48333000, -63.70000000, '2019-10-05 22:34:54', '2020-05-01 17:22:35', 1, 'Q995752'),
(10015, 'San Julian', 3377, 'S', 27, 'BO', -17.78333000, -62.86667000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q995752'),
(10016, 'San Matías', 3377, 'S', 27, 'BO', -16.36667000, -58.40000000, '2019-10-05 22:34:54', '2020-05-01 17:22:35', 1, 'Q176823'),
(10017, 'San Pablo', 3380, 'L', 27, 'BO', -16.21667000, -68.83333000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2068055'),
(10018, 'San Pedro', 3377, 'S', 27, 'BO', -18.28333000, -59.81667000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2068055'),
(10019, 'San Pedro', 3380, 'L', 27, 'BO', -16.23717000, -68.85063000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1753041'),
(10020, 'San Ramón', 3375, 'B', 27, 'BO', -13.28333000, -64.71667000, '2019-10-05 22:34:54', '2020-05-01 17:22:35', 1, 'Q849354'),
(10021, 'Santa Ana de Yacuma', 3375, 'B', 27, 'BO', -13.74406000, -65.42688000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q849354'),
(10022, 'Santa Bárbara', 3383, 'P', 27, 'BO', -20.91667000, -66.05000000, '2019-10-05 22:34:54', '2020-05-01 17:22:35', 1, 'Q258809'),
(10023, 'Santa Cruz de la Sierra', 3377, 'S', 27, 'BO', -17.78629000, -63.18117000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q170688'),
(10024, 'Santa Rita', 3377, 'S', 27, 'BO', -17.96667000, -63.35000000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q170688'),
(10025, 'Santa Rosa', 3375, 'B', 27, 'BO', -14.16667000, -66.88333000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q170688'),
(10026, 'Santa Rosa del Sara', 3377, 'S', 27, 'BO', -17.10916000, -63.59514000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2223250'),
(10027, 'Santiago del Torno', 3377, 'S', 27, 'BO', -17.98674000, -63.38118000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q21794389'),
(10028, 'Sebastian Pagador Province', 3376, 'O', 27, 'BO', -19.21667000, -66.21667000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q21794389'),
(10029, 'Sipe Sipe', 3381, 'C', 27, 'BO', -17.45000000, -66.38333000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q990709'),
(10030, 'Sorata', 3380, 'L', 27, 'BO', -15.77305000, -68.64973000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1428069'),
(10031, 'Sucre', 3382, 'H', 27, 'BO', -19.03332000, -65.26274000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q2907'),
(10032, 'Sud Carangas Province', 3376, 'O', 27, 'BO', -18.50000000, -68.25000000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1360805'),
(10033, 'Tarabuco', 3382, 'H', 27, 'BO', -19.18168000, -64.91517000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1982115'),
(10034, 'Tarata', 3381, 'C', 27, 'BO', -17.60898000, -66.02135000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q1016289'),
(10035, 'Tarija', 3378, 'T', 27, 'BO', -21.53549000, -64.72956000, '2019-10-05 22:34:54', '2019-10-05 22:34:54', 1, 'Q35004'),
(10036, 'Tiahuanaco', 3380, 'L', 27, 'BO', -16.55228000, -68.67953000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q61750'),
(10037, 'Tiquipaya', 3381, 'C', 27, 'BO', -17.33801000, -66.21579000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1020288'),
(10038, 'Tiraque Province', 3381, 'C', 27, 'BO', -17.33333000, -65.91667000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q979768'),
(10039, 'Totora', 3381, 'C', 27, 'BO', -17.72662000, -65.19320000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2038566'),
(10040, 'Totoral', 3376, 'O', 27, 'BO', -18.49587000, -66.87380000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2038566'),
(10041, 'Trinidad', 3375, 'B', 27, 'BO', -14.83333000, -64.90000000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q34986'),
(10042, 'Tupiza', 3383, 'P', 27, 'BO', -21.44345000, -65.71875000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1014481'),
(10043, 'Urubichá', 3377, 'S', 27, 'BO', -15.39286000, -62.94661000, '2019-10-05 22:34:55', '2020-05-01 17:22:35', 1, 'Q1805205'),
(10044, 'Uyuni', 3383, 'P', 27, 'BO', -20.45967000, -66.82503000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1014511'),
(10045, 'Vallegrande', 3377, 'S', 27, 'BO', -18.48923000, -64.10609000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q735765'),
(10046, 'Villa Yapacaní', 3377, 'S', 27, 'BO', -17.40000000, -63.83333000, '2019-10-05 22:34:55', '2020-05-01 17:22:35', 1, 'Q1506556'),
(10047, 'Villamontes', 3378, 'T', 27, 'BO', -21.26235000, -63.46903000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1014504'),
(10048, 'Villazón', 3383, 'P', 27, 'BO', -22.08659000, -65.59422000, '2019-10-05 22:34:55', '2020-05-01 17:22:35', 1, 'Q431553'),
(10049, 'Viloco', 3380, 'L', 27, 'BO', -16.95000000, -67.55000000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2526299'),
(10050, 'Warnes', 3377, 'S', 27, 'BO', -17.51630000, -63.16778000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q973385'),
(10051, 'Yacuiba', 3378, 'T', 27, 'BO', -22.01643000, -63.67753000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q990743'),
(10052, 'Yumani', 3380, 'L', 27, 'BO', -16.03574000, -69.14843000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q21801305'),
(10053, 'Abadia de Goiás', 2000, 'GO', 31, 'BR', -16.77762000, -49.46841000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q304652'),
(10054, 'Abadia dos Dourados', 1998, 'MG', 31, 'BR', -18.36347000, -47.46997000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q582223'),
(10055, 'Abadiânia', 2000, 'GO', 31, 'BR', -16.22632000, -48.62681000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q304716'),
(10056, 'Abaetetuba', 2009, 'PA', 31, 'BR', -1.71806000, -48.88250000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1615298'),
(10057, 'Abaeté', 1998, 'MG', 31, 'BR', -19.11099000, -45.43051000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1615444'),
(10058, 'Abaiara', 2016, 'CE', 31, 'BR', -7.33642000, -39.06129000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1796441'),
(10059, 'Abaré', 2002, 'BA', 31, 'BR', -8.80961000, -39.27062000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1647789'),
(10060, 'Abatiá', 2022, 'PR', 31, 'BR', -23.29903000, -50.32176000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q784855'),
(10061, 'Abaíra', 2002, 'BA', 31, 'BR', -13.32129000, -41.69759000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1645077'),
(10062, 'Abdon Batista', 2014, 'SC', 31, 'BR', -27.58981000, -51.04023000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1637414'),
(10063, 'Abel Figueiredo', 2009, 'PA', 31, 'BR', -4.94378000, -48.42275000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q942803'),
(10064, 'Abelardo Luz', 2014, 'SC', 31, 'BR', -26.59825000, -52.22321000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1637506'),
(10065, 'Abre Campo', 1998, 'MG', 31, 'BR', -20.27265000, -42.43908000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1749816'),
(10066, 'Abreu e Lima', 2006, 'PE', 31, 'BR', -7.86865000, -35.08171000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q536569'),
(10067, 'Abreulândia', 2020, 'TO', 31, 'BR', -9.43672000, -49.31733000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q1784836'),
(10068, 'Acaiaca', 1998, 'MG', 31, 'BR', -20.40360000, -43.10077000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1784836'),
(10069, 'Acajutiba', 2002, 'BA', 31, 'BR', -11.60682000, -38.03594000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1761962'),
(10070, 'Acarape', 2016, 'CE', 31, 'BR', -4.20565000, -38.69160000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1808379'),
(10071, 'Acaraú', 2016, 'CE', 31, 'BR', -2.95424000, -40.08597000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1796706'),
(10072, 'Acari', 2019, 'RN', 31, 'BR', -6.38547000, -36.63908000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2008611'),
(10073, 'Acará', 2009, 'PA', 31, 'BR', -1.96083000, -48.19667000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q1804839'),
(10074, 'Acauã', 2008, 'PI', 31, 'BR', -8.31552000, -40.91283000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q1804839'),
(10075, 'Aceguá', 2001, 'RS', 31, 'BR', -31.64597000, -54.10715000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q913259'),
(10076, 'Acopiara', 2016, 'CE', 31, 'BR', -6.11312000, -39.51756000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q544694'),
(10077, 'Acorizal', 2011, 'MT', 31, 'BR', -15.17336000, -56.30434000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q507567'),
(10078, 'Acrelândia', 2012, 'AC', 31, 'BR', -9.98045000, -66.84388000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q507567'),
(10079, 'Acreúna', 2000, 'GO', 31, 'BR', -17.43605000, -50.26696000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q507567'),
(10080, 'Adamantina', 2021, 'SP', 31, 'BR', -21.59136000, -51.06669000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1637498'),
(10081, 'Adelândia', 2000, 'GO', 31, 'BR', -16.38577000, -50.18846000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1637498'),
(10082, 'Adolfo', 2021, 'SP', 31, 'BR', -21.28535000, -49.65497000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q676044'),
(10083, 'Adrianópolis', 2022, 'PR', 31, 'BR', -24.76632000, -48.79998000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q676044'),
(10084, 'Adustina', 2002, 'BA', 31, 'BR', -10.48227000, -37.98446000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q676044'),
(10085, 'Afogados da Ingazeira', 2006, 'PE', 31, 'BR', -7.72298000, -37.61781000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1761348'),
(10086, 'Afonso Bezerra', 2019, 'RN', 31, 'BR', -5.43183000, -36.65619000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1788960'),
(10087, 'Afonso Cláudio', 2018, 'ES', 31, 'BR', -20.10512000, -41.12422000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1793672'),
(10088, 'Afonso Cunha', 2015, 'MA', 31, 'BR', -4.21479000, -43.29743000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1793672'),
(10089, 'Afrânio', 2006, 'PE', 31, 'BR', -8.62589000, -41.05768000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q2065815'),
(10090, 'Afuá', 2009, 'PA', 31, 'BR', -0.15667000, -50.38667000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q389536'),
(10091, 'Agrestina', 2006, 'PE', 31, 'BR', -8.45191000, -35.93238000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1787629'),
(10092, 'Agricolândia', 2008, 'PI', 31, 'BR', -5.74819000, -42.67458000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q1787629'),
(10093, 'Agrolândia', 2014, 'SC', 31, 'BR', -27.46387000, -49.83759000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q397387'),
(10094, 'Agronômica', 2014, 'SC', 31, 'BR', -27.31658000, -49.73117000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q1754594'),
(10095, 'Aguanil', 1998, 'MG', 31, 'BR', -20.96959000, -45.41717000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1754594'),
(10096, 'Aguaí', 2021, 'SP', 31, 'BR', -22.02590000, -47.06702000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q1760037'),
(10097, 'Agudo', 2001, 'RS', 31, 'BR', -29.62631000, -53.22404000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q398173'),
(10098, 'Agudos', 2021, 'SP', 31, 'BR', -22.58942000, -49.16164000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1750166'),
(10099, 'Agudos do Sul', 2022, 'PR', 31, 'BR', -26.04616000, -49.31186000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q398170'),
(10100, 'Aguiar', 2005, 'PB', 31, 'BR', -7.07620000, -38.24255000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1998462'),
(10101, 'Aguiarnópolis', 2020, 'TO', 31, 'BR', -6.48176000, -47.51435000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q1998462'),
(10102, 'Aimorés', 1998, 'MG', 31, 'BR', -19.62552000, -41.20955000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1650790'),
(10103, 'Aiquara', 2002, 'BA', 31, 'BR', -14.11135000, -39.87641000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1650790'),
(10104, 'Aiuaba', 2016, 'CE', 31, 'BR', -6.58896000, -40.23948000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q641440'),
(10105, 'Aiuruoca', 1998, 'MG', 31, 'BR', -21.94703000, -44.64779000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q641440'),
(10106, 'Ajuricaba', 2001, 'RS', 31, 'BR', -28.20723000, -53.74458000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q413408'),
(10107, 'Alagoa', 1998, 'MG', 31, 'BR', -22.18147000, -44.66024000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q678420'),
(10108, 'Alagoa Grande', 2005, 'PB', 31, 'BR', -7.07831000, -35.59525000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1998976'),
(10109, 'Alagoa Nova', 2005, 'PB', 31, 'BR', -7.04733000, -35.75397000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1816238'),
(10110, 'Alagoinha', 2005, 'PB', 31, 'BR', -6.96256000, -35.51465000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2065239'),
(10111, 'Alagoinha', 2006, 'PE', 31, 'BR', -8.53497000, -36.75607000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q938278'),
(10112, 'Alagoinha do Piauí', 2008, 'PI', 31, 'BR', -6.98016000, -40.91814000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q938278'),
(10113, 'Alagoinhas', 2002, 'BA', 31, 'BR', -12.00580000, -38.36146000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q22050101'),
(10114, 'Alambari', 2021, 'SP', 31, 'BR', -23.53745000, -47.86273000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q513601'),
(10115, 'Albertina', 1998, 'MG', 31, 'BR', -22.19908000, -46.62076000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q986243'),
(10116, 'Alcantil', 2005, 'PB', 31, 'BR', -7.69816000, -36.06604000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q986243'),
(10117, 'Alcinópolis', 2010, 'MS', 31, 'BR', -18.19955000, -53.75814000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q986243'),
(10118, 'Alcobaça', 2002, 'BA', 31, 'BR', -17.46365000, -39.37401000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q22063205'),
(10119, 'Alcântara', 2015, 'MA', 31, 'BR', -2.32768000, -44.50617000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q939522'),
(10120, 'Alcântaras', 2016, 'CE', 31, 'BR', -3.58733000, -40.55517000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q939522'),
(10121, 'Aldeias Altas', 2015, 'MA', 31, 'BR', -4.58522000, -43.46277000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q939522'),
(10122, 'Alecrim', 2001, 'RS', 31, 'BR', -27.65897000, -54.75943000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q995308'),
(10123, 'Alegre', 2018, 'ES', 31, 'BR', -20.70897000, -41.51937000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1807909'),
(10124, 'Alegrete', 2001, 'RS', 31, 'BR', -29.78306000, -55.79194000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q851276'),
(10125, 'Alegrete do Piauí', 2008, 'PI', 31, 'BR', -7.16226000, -40.82488000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q851276'),
(10126, 'Alegria', 2001, 'RS', 31, 'BR', -27.79967000, -54.05855000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q601127'),
(10127, 'Alenquer', 2009, 'PA', 31, 'BR', -0.85422000, -54.94506000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2289869'),
(10128, 'Alexandria', 2019, 'RN', 31, 'BR', -6.39998000, -37.96862000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2099372'),
(10129, 'Alexânia', 2000, 'GO', 31, 'BR', -16.14926000, -48.45973000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q2099372'),
(10130, 'Alfenas', 1998, 'MG', 31, 'BR', -21.39287000, -45.99521000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1755151'),
(10131, 'Alfredo Chaves', 2018, 'ES', 31, 'BR', -20.54245000, -40.82368000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q596538'),
(10132, 'Alfredo Marcondes', 2021, 'SP', 31, 'BR', -21.92954000, -51.39519000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q596538'),
(10133, 'Alfredo Vasconcelos', 1998, 'MG', 31, 'BR', -21.14240000, -43.71114000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q596538'),
(10134, 'Alfredo Wagner', 2014, 'SC', 31, 'BR', -27.70481000, -49.34069000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q596538'),
(10135, 'Algodão de Jandaíra', 2005, 'PB', 31, 'BR', -6.86987000, -35.97724000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q1759357'),
(10136, 'Alhandra', 2005, 'PB', 31, 'BR', -7.35043000, -34.92835000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q385529'),
(10137, 'Aliança', 2006, 'PE', 31, 'BR', -7.59698000, -35.16536000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q2010570'),
(10138, 'Aliança do Tocantins', 2020, 'TO', 31, 'BR', -11.32864000, -48.95855000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q2010570'),
(10139, 'Almadina', 2002, 'BA', 31, 'BR', -14.67608000, -39.69066000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2010570'),
(10140, 'Almas', 2020, 'TO', 31, 'BR', -11.43527000, -47.23635000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q22060165'),
(10141, 'Almeirim', 2009, 'PA', 31, 'BR', 0.47621000, -53.87297000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q22067014'),
(10142, 'Almenara', 1998, 'MG', 31, 'BR', -16.10053000, -40.71006000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q979377'),
(10143, 'Almino Afonso', 2019, 'RN', 31, 'BR', -6.16686000, -37.77116000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1802697'),
(10144, 'Almirante Tamandaré', 2022, 'PR', 31, 'BR', -25.32472000, -49.31000000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q1650078'),
(10145, 'Almirante Tamandaré do Sul', 2001, 'RS', 31, 'BR', -28.15104000, -52.91142000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q957758'),
(10146, 'Aloândia', 2000, 'GO', 31, 'BR', -17.69411000, -49.44985000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q988880'),
(10147, 'Alpercata', 1998, 'MG', 31, 'BR', -18.98379000, -42.00135000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q988880'),
(10148, 'Alpestre', 2001, 'RS', 31, 'BR', -27.22135000, -53.05749000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q957796'),
(10149, 'Alpinópolis', 1998, 'MG', 31, 'BR', -20.81822000, -46.37937000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q1757102'),
(10150, 'Alta Floresta', 2011, 'MT', 31, 'BR', -9.87556000, -56.08611000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q433574'),
(10151, 'Alta Floresta d\'Oeste', 2013, 'RO', 31, 'BR', -12.47107000, -62.13705000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q433574'),
(10152, 'Altair', 2021, 'SP', 31, 'BR', -20.53889000, -49.10219000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q305132'),
(10153, 'Altamira', 2009, 'PA', 31, 'BR', -3.20333000, -52.20639000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q434285'),
(10154, 'Altamira do Maranhão', 2015, 'MA', 31, 'BR', -4.14072000, -45.46154000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q2013259'),
(10155, 'Altamira do Paraná', 2022, 'PR', 31, 'BR', -24.81257000, -52.70740000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q2013259'),
(10156, 'Altaneira', 2016, 'CE', 31, 'BR', -6.98737000, -39.72740000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q2013259'),
(10157, 'Alterosa', 1998, 'MG', 31, 'BR', -21.21904000, -46.18694000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1756931'),
(10158, 'Altinho', 2006, 'PE', 31, 'BR', -8.45151000, -36.08155000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1844246'),
(10159, 'Altinópolis', 2021, 'SP', 31, 'BR', -21.00412000, -47.39795000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q1112482'),
(10160, 'Alto Alegre', 2021, 'SP', 31, 'BR', -21.63149000, -50.19601000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1112482'),
(10161, 'Alto Alegre', 2001, 'RS', 31, 'BR', -28.79752000, -52.98177000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1806753'),
(10162, 'Alto Alegre do Maranhão', 2015, 'MA', 31, 'BR', -4.20288000, -44.41966000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q373783'),
(10163, 'Alto Alegre do Pindaré', 2015, 'MA', 31, 'BR', -3.83592000, -46.03871000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q373783'),
(10164, 'Alto Alegre dos Parecis', 2013, 'RO', 31, 'BR', -12.75601000, -61.97971000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q1802050'),
(10165, 'Alto Araguaia', 2011, 'MT', 31, 'BR', -17.48438000, -53.40594000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q22067364'),
(10166, 'Alto Bela Vista', 2014, 'SC', 31, 'BR', -27.40999000, -51.93145000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q22067364');

