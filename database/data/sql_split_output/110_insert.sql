INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(55314, '<PERSON><PERSON>', 1044, 'FE', 99, 'HU', 46.77056000, 18.62826000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q378836'),
(55315, 'Cegléd', 1059, 'PE', 99, 'HU', 47.17266000, 19.79952000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q214948'),
(55316, 'Ceg<PERSON><PERSON><PERSON>cel', 1059, 'PE', 99, 'HU', 47.22370000, 19.66828000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q519585'),
(55317, '<PERSON><PERSON><PERSON><PERSON>', 1059, 'P<PERSON>', 99, 'HU', 47.20000000, 19.82463000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q851960'),
(55318, '<PERSON>d<PERSON>m<PERSON><PERSON>', 1039, 'VA', 99, 'HU', 47.25713000, 17.15027000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q843035'),
(55319, 'Celld<PERSON>m<PERSON>lki J<PERSON>rás', 1039, 'VA', 99, 'HU', 47.25001000, 17.13209000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q3031098'),
(55320, 'Cibakháza', 1043, 'JN', 99, 'HU', 46.95976000, 20.19753000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q867175'),
(55321, 'Cigánd', 1058, 'BZ', 99, 'HU', 48.25561000, 21.89195000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q846652'),
(55322, 'Cigándi Járás', 1058, 'BZ', 99, 'HU', 48.30338000, 21.87651000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q642533'),
(55323, 'Csabrendek', 1054, 'VE', 99, 'HU', 47.01356000, 17.29108000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q870202'),
(55324, 'Csanytelek', 1031, 'CS', 99, 'HU', 46.59501000, 20.12342000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q652132'),
(55325, 'Csanádapáca', 1060, 'BE', 99, 'HU', 46.55000000, 20.88333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q240047'),
(55326, 'Csanádpalota', 1031, 'CS', 99, 'HU', 46.25000000, 20.73333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q870608'),
(55327, 'Csemő', 1059, 'PE', 99, 'HU', 47.11799000, 19.69092000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q377504'),
(55328, 'Csengele', 1031, 'CS', 99, 'HU', 46.54234000, 19.86358000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q871583'),
(55329, 'Csenger', 1045, 'SZ', 99, 'HU', 47.83582000, 22.68096000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q855082'),
(55330, 'Csengeri Járás', 1045, 'SZ', 99, 'HU', 47.83045000, 22.59810000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q694024'),
(55331, 'Csengőd', 1048, 'BK', 99, 'HU', 46.71543000, 19.26802000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q871659'),
(55332, 'Csepreg', 1039, 'VA', 99, 'HU', 47.40098000, 16.70881000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q520019'),
(55333, 'Cserkeszőlő', 1043, 'JN', 99, 'HU', 46.86320000, 20.18701000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q872113'),
(55334, 'Cserszegtomaj', 1046, 'ZA', 99, 'HU', 46.80165000, 17.22096000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q872445'),
(55335, 'Csetény', 1054, 'VE', 99, 'HU', 47.31806000, 17.99208000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q872654'),
(55336, 'Csobánka', 1059, 'PE', 99, 'HU', 47.64637000, 18.96189000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q370208'),
(55337, 'Csongrád', 1031, 'CS', 99, 'HU', 46.71332000, 20.14241000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q649214'),
(55338, 'Csongrádi Járás', 1031, 'CS', 99, 'HU', 46.66146000, 20.08792000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q853114'),
(55339, 'Csopak', 1054, 'VE', 99, 'HU', 46.97709000, 17.91819000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q874343'),
(55340, 'Csorna', 1042, 'GS', 99, 'HU', 47.61155000, 17.25012000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q843001'),
(55341, 'Csornai Járás', 1042, 'GS', 99, 'HU', 47.58403000, 17.26041000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q874532'),
(55342, 'Csorvás', 1060, 'BE', 99, 'HU', 46.63333000, 20.83333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q677698'),
(55343, 'Csurgó', 1035, 'SO', 99, 'HU', 46.25314000, 17.10060000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q855016'),
(55344, 'Csurgói Járás', 1035, 'SO', 99, 'HU', 46.29502000, 17.10021000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q548991'),
(55345, 'Csákvár', 1044, 'FE', 99, 'HU', 47.39184000, 18.46501000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q875122'),
(55346, 'Csány', 1040, 'HE', 99, 'HU', 47.64829000, 19.82972000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q672245'),
(55347, 'Császártöltés', 1048, 'BK', 99, 'HU', 46.42194000, 19.18361000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q373905'),
(55348, 'Csávoly', 1048, 'BK', 99, 'HU', 46.18917000, 19.14667000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q875575'),
(55349, 'Csökmő', 1063, 'HB', 99, 'HU', 47.03333000, 21.30000000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q591468'),
(55350, 'Csömör', 1059, 'PE', 99, 'HU', 47.55000000, 19.23333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q876321'),
(55351, 'Dabas', 1059, 'PE', 99, 'HU', 47.18594000, 19.31091000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q854940'),
(55352, 'Dabasi Járás', 1059, 'PE', 99, 'HU', 47.19000000, 19.33000000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q854287'),
(55353, 'Debrecen', 1063, 'HB', 99, 'HU', 47.53333000, 21.63333000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q79880'),
(55354, 'Debreceni Járás', 1063, 'HB', 99, 'HU', 47.52706000, 21.66869000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q903107'),
(55355, 'Decs', 1038, 'TO', 99, 'HU', 46.28428000, 18.76000000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q591329'),
(55356, 'Demecser', 1045, 'SZ', 99, 'HU', 48.11648000, 21.92624000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q903691'),
(55357, 'Derecske', 1063, 'HB', 99, 'HU', 47.35000000, 21.56667000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q781435'),
(55358, 'Derecskei Járás', 1063, 'HB', 99, 'HU', 47.38194000, 21.74836000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q635166'),
(55359, 'Deszk', 1031, 'CS', 99, 'HU', 46.21802000, 20.24322000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q904442'),
(55360, 'Devecser', 1054, 'VE', 99, 'HU', 47.10316000, 17.43802000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q853744'),
(55361, 'Devecseri Járás', 1054, 'VE', 99, 'HU', 47.16836000, 17.32271000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q904584'),
(55362, 'Diósd', 1059, 'PE', 99, 'HU', 47.40950000, 18.94898000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q126670'),
(55363, 'Diósjenő', 1051, 'NO', 99, 'HU', 47.93968000, 19.04317000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q265916'),
(55364, 'Doboz', 1060, 'BE', 99, 'HU', 46.73333000, 21.25000000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q907228'),
(55365, 'Domaszék', 1031, 'CS', 99, 'HU', 46.24917000, 20.01111000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q908081'),
(55366, 'Dombegyház', 1060, 'BE', 99, 'HU', 46.33333000, 21.13333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q908113'),
(55367, 'Dombrád', 1045, 'SZ', 99, 'HU', 48.23333000, 21.93333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q908173'),
(55368, 'Dombóvár', 1038, 'TO', 99, 'HU', 46.37657000, 18.13696000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q633789'),
(55369, 'Dombóvári Járás', 1038, 'TO', 99, 'HU', 46.45592000, 18.19076000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q831508'),
(55370, 'Domony', 1059, 'PE', 99, 'HU', 47.65552000, 19.43229000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q908466'),
(55371, 'Domoszló', 1040, 'HE', 99, 'HU', 47.83333000, 20.11667000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q780659'),
(55372, 'Dunabogdány', 1059, 'PE', 99, 'HU', 47.79052000, 19.04125000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q734021'),
(55373, 'Dunaföldvár', 1038, 'TO', 99, 'HU', 46.80713000, 18.92763000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q911301'),
(55374, 'Dunaharaszti', 1059, 'PE', 99, 'HU', 47.35450000, 19.09822000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q854933'),
(55375, 'Dunakeszi', 1059, 'PE', 99, 'HU', 47.63641000, 19.13864000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q547479'),
(55376, 'Dunakeszi Járás', 1059, 'PE', 99, 'HU', 47.63365000, 19.16254000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q853136'),
(55377, 'Dunapataj', 1048, 'BK', 99, 'HU', 46.64400000, 18.99632000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q911434'),
(55378, 'Dunaszekcső', 1055, 'BA', 99, 'HU', 46.08740000, 18.75870000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q659869'),
(55379, 'Dunaszentgyörgy', 1038, 'TO', 99, 'HU', 46.52852000, 18.81771000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q682302'),
(55380, 'Dunavarsány', 1059, 'PE', 99, 'HU', 47.27859000, 19.06617000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q738159'),
(55381, 'Dunavecse', 1048, 'BK', 99, 'HU', 46.91478000, 18.97127000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q613187'),
(55382, 'Dunaújvárosi Járás', 1044, 'FE', 99, 'HU', 47.02801000, 18.82997000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q549258'),
(55383, 'Dusnok', 1048, 'BK', 99, 'HU', 46.39085000, 18.96296000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q911773'),
(55384, 'Dánszentmiklós', 1059, 'PE', 99, 'HU', 47.21486000, 19.54695000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q768477'),
(55385, 'Dány', 1059, 'PE', 99, 'HU', 47.52000000, 19.54400000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q912580'),
(55386, 'Dávod', 1048, 'BK', 99, 'HU', 45.99500000, 18.91722000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q595231'),
(55387, 'Dég', 1044, 'FE', 99, 'HU', 46.86807000, 18.45042000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q912764'),
(55388, 'Délegyháza', 1059, 'PE', 99, 'HU', 47.24135000, 19.09019000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q986788'),
(55389, 'Dévaványa', 1060, 'BE', 99, 'HU', 47.03333000, 20.96667000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q583620'),
(55390, 'Döbrököz', 1038, 'TO', 99, 'HU', 46.42178000, 18.23953000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q937317'),
(55391, 'Döge', 1045, 'SZ', 99, 'HU', 48.26246000, 22.06339000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q987171'),
(55392, 'Dömsöd', 1059, 'PE', 99, 'HU', 47.09005000, 19.01106000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q241396'),
(55393, 'Ebes', 1063, 'HB', 99, 'HU', 47.46667000, 21.50000000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q743124'),
(55394, 'Ecser', 1059, 'PE', 99, 'HU', 47.44389000, 19.32450000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q30317'),
(55395, 'Ecséd', 1040, 'HE', 99, 'HU', 47.73267000, 19.76696000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q282039'),
(55396, 'Edelény', 1058, 'BZ', 99, 'HU', 48.30000000, 20.73333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q842925'),
(55397, 'Edelényi Járás', 1058, 'BZ', 99, 'HU', 48.40864000, 20.77506000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q987635'),
(55398, 'Eger', 1040, 'HE', 99, 'HU', 47.90265000, 20.37329000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q167109'),
(55399, 'Egerszalók', 1040, 'HE', 99, 'HU', 47.86667000, 20.33333000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q987849'),
(55400, 'Egri Járás', 1040, 'HE', 99, 'HU', 47.92961000, 20.37383000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q854361'),
(55401, 'Egyek', 1063, 'HB', 99, 'HU', 47.63333000, 20.90000000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q944327'),
(55402, 'Elek', 1060, 'BE', 99, 'HU', 46.53333000, 21.25000000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q911667'),
(55403, 'Előszállás', 1044, 'FE', 99, 'HU', 46.83094000, 18.83481000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q989222'),
(55404, 'Emőd', 1058, 'BZ', 99, 'HU', 47.93333000, 20.81667000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q854923'),
(55405, 'Encs', 1058, 'BZ', 99, 'HU', 48.33333000, 21.13333000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q855060'),
(55406, 'Encsencs', 1045, 'SZ', 99, 'HU', 47.73333000, 22.11667000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q263002'),
(55407, 'Encsi Járás', 1058, 'BZ', 99, 'HU', 48.39948000, 21.08169000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q921237'),
(55408, 'Enying', 1044, 'FE', 99, 'HU', 46.93046000, 18.24202000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q632146'),
(55409, 'Enyingi Járás', 1044, 'FE', 99, 'HU', 46.87240000, 18.30975000, '2019-10-05 22:53:34', '2020-05-01 17:22:51', 1, 'Q659118'),
(55410, 'Ercsi', 1044, 'FE', 99, 'HU', 47.25194000, 18.89623000, '2019-10-05 22:53:34', '2019-10-05 22:53:34', 1, 'Q855040'),
(55411, 'Erdőkertes', 1059, 'PE', 99, 'HU', 47.67261000, 19.30786000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q510886'),
(55412, 'Erdőtelek', 1040, 'HE', 99, 'HU', 47.68333000, 20.31667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q249661'),
(55413, 'Erzsébetváros', 1064, 'BU', 99, 'HU', 47.50207000, 19.07218000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q284541'),
(55414, 'Etyek', 1044, 'FE', 99, 'HU', 47.44794000, 18.75328000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q204834'),
(55415, 'Fadd', 1038, 'TO', 99, 'HU', 46.46476000, 18.81925000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q747724'),
(55416, 'Farkaslyuk', 1058, 'BZ', 99, 'HU', 48.18333000, 20.31667000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q610280'),
(55417, 'Farmos', 1059, 'PE', 99, 'HU', 47.36067000, 19.84619000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q608728'),
(55418, 'Farád', 1042, 'GS', 99, 'HU', 47.60633000, 17.20024000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q996627'),
(55419, 'Fegyvernek', 1043, 'JN', 99, 'HU', 47.26667000, 20.53333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q997079'),
(55420, 'Fehérgyarmat', 1045, 'SZ', 99, 'HU', 47.98333000, 22.51667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q745657'),
(55421, 'Fehérgyarmati Járás', 1045, 'SZ', 99, 'HU', 48.00436000, 22.63014000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q694044'),
(55422, 'Fehérvárcsurgó', 1044, 'FE', 99, 'HU', 47.29349000, 18.26460000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q997777'),
(55423, 'Felsőpakony', 1059, 'PE', 99, 'HU', 47.34329000, 19.23698000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q976187'),
(55424, 'Felsőszentiván', 1048, 'BK', 99, 'HU', 46.19713000, 19.18686000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q289274'),
(55425, 'Felsőtárkány', 1040, 'HE', 99, 'HU', 47.96667000, 20.41667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q514643'),
(55426, 'Felsőzsolca', 1058, 'BZ', 99, 'HU', 48.10000000, 20.86667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q368429'),
(55427, 'Fertőd', 1042, 'GS', 99, 'HU', 47.62173000, 16.87088000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q667208'),
(55428, 'Fertőrákos', 1042, 'GS', 99, 'HU', 47.72017000, 16.65040000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q766583'),
(55429, 'Fertőszentmiklós', 1042, 'GS', 99, 'HU', 47.58996000, 16.87517000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1000813'),
(55430, 'Fonyód', 1035, 'SO', 99, 'HU', 46.75552000, 17.57945000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q588606'),
(55431, 'Fonyódi Járás', 1035, 'SO', 99, 'HU', 46.67289000, 17.70000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q47515'),
(55432, 'Forráskút', 1031, 'CS', 99, 'HU', 46.36528000, 19.90973000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q685121'),
(55433, 'Forrópuszta', 1059, 'PE', 99, 'HU', 47.45504000, 19.66001000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q685121'),
(55434, 'Fábiánsebestyén', 1031, 'CS', 99, 'HU', 46.68333000, 20.46667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1004527'),
(55435, 'Fényeslitke', 1045, 'SZ', 99, 'HU', 48.27133000, 22.10009000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q540007'),
(55436, 'Fót', 1059, 'PE', 99, 'HU', 47.61770000, 19.18870000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q830652'),
(55437, 'Földes', 1063, 'HB', 99, 'HU', 47.30000000, 21.36667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q260132'),
(55438, 'Földeák', 1031, 'CS', 99, 'HU', 46.31667000, 20.50000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q933653'),
(55439, 'Fülöpjakab', 1048, 'BK', 99, 'HU', 46.74221000, 19.72132000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1006578'),
(55440, 'Fülöpszállás', 1048, 'BK', 99, 'HU', 46.82075000, 19.23748000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1006094'),
(55441, 'Füzesabony', 1040, 'HE', 99, 'HU', 47.75000000, 20.41667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911642'),
(55442, 'Füzesabonyi Járás', 1040, 'HE', 99, 'HU', 47.72671000, 20.42875000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853176'),
(55443, 'Füzesgyarmat', 1060, 'BE', 99, 'HU', 47.10000000, 21.21667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q286225'),
(55444, 'Galgahévíz', 1059, 'PE', 99, 'HU', 47.61667000, 19.56667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q221851'),
(55445, 'Galgamácsa', 1059, 'PE', 99, 'HU', 47.69562000, 19.38724000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q580799'),
(55446, 'Gara', 1048, 'BK', 99, 'HU', 46.03194000, 19.04278000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1008106'),
(55447, 'Gencsapáti', 1039, 'VA', 99, 'HU', 47.28496000, 16.59575000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q543263'),
(55448, 'Gesztely', 1058, 'BZ', 99, 'HU', 48.10000000, 20.96667000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1007962'),
(55449, 'Gomba', 1059, 'PE', 99, 'HU', 47.37095000, 19.53057000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1011712'),
(55450, 'Gyenesdiás', 1046, 'ZA', 99, 'HU', 46.77058000, 17.28660000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q374942'),
(55451, 'Gyomaendrőd', 1060, 'BE', 99, 'HU', 46.93333000, 20.83333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q732300'),
(55452, 'Gyomaendrődi Járás', 1060, 'BE', 99, 'HU', 46.98216000, 20.84851000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q14301659'),
(55453, 'Gyula', 1060, 'BE', 99, 'HU', 46.65000000, 21.28333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q214938'),
(55454, 'Gyulaháza', 1045, 'SZ', 99, 'HU', 48.13333000, 22.11667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q613034'),
(55455, 'Gyulai Járás', 1060, 'BE', 99, 'HU', 46.58626000, 21.22454000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q14629767'),
(55456, 'Gyál', 1059, 'PE', 99, 'HU', 47.38449000, 19.22140000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q842908'),
(55457, 'Gyáli Járás', 1059, 'PE', 99, 'HU', 47.36821000, 19.27543000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853181'),
(55458, 'Gyömrő', 1059, 'PE', 99, 'HU', 47.42733000, 19.40133000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q854906'),
(55459, 'Gyöngyös', 1040, 'HE', 99, 'HU', 47.78257000, 19.92800000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q209983'),
(55460, 'Gyöngyöshalász', 1040, 'HE', 99, 'HU', 47.74161000, 19.92876000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q729853'),
(55461, 'Gyöngyösi Járás', 1040, 'HE', 99, 'HU', 47.79079000, 19.95430000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853127'),
(55462, 'Gyöngyöspata', 1040, 'HE', 99, 'HU', 47.81505000, 19.78925000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1030385'),
(55463, 'Gyöngyössolymos', 1040, 'HE', 99, 'HU', 47.81724000, 19.93619000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q784268'),
(55464, 'Gyöngyöstarján', 1040, 'HE', 99, 'HU', 47.81369000, 19.86724000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1030405'),
(55465, 'Gyönk', 1038, 'TO', 99, 'HU', 46.55603000, 18.47694000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1011718'),
(55466, 'Győr', 1042, 'GS', 99, 'HU', 47.68333000, 17.63512000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q134494'),
(55467, 'Győri Járás', 1042, 'GS', 99, 'HU', 47.67273000, 17.67936000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1030726'),
(55468, 'Győrszemere', 1042, 'GS', 99, 'HU', 47.55256000, 17.56356000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1030750'),
(55469, 'Győrújbarát', 1042, 'GS', 99, 'HU', 47.60643000, 17.64875000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q290366'),
(55470, 'Gádoros', 1060, 'BE', 99, 'HU', 46.66667000, 20.60000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1030980'),
(55471, 'Gárdony', 1044, 'FE', 99, 'HU', 47.20942000, 18.63607000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q642031'),
(55472, 'Gárdonyi Járás', 1044, 'FE', 99, 'HU', 47.22785000, 18.63622000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853097'),
(55473, 'Gégény', 1045, 'SZ', 99, 'HU', 48.15000000, 21.95000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1031315'),
(55474, 'Göd', 1059, 'PE', 99, 'HU', 47.68324000, 19.13417000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q604258'),
(55475, 'Gödöllő', 1059, 'PE', 99, 'HU', 47.59657000, 19.35515000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q209988'),
(55476, 'Gödöllői Járás', 1059, 'PE', 99, 'HU', 47.55127000, 19.39595000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853188'),
(55477, 'Gönc', 1058, 'BZ', 99, 'HU', 48.46667000, 21.28333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q842967'),
(55478, 'Gönci Járás', 1058, 'BZ', 99, 'HU', 48.39948000, 21.28307000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q15037608'),
(55479, 'Görbeháza', 1063, 'HB', 99, 'HU', 47.83333000, 21.23333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q685324'),
(55480, 'Hadjúszoboszlói Járás', 1063, 'HB', 99, 'HU', 47.48110000, 21.31000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q831178'),
(55481, 'Hajdúbagos', 1063, 'HB', 99, 'HU', 47.39295000, 21.66551000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q773722'),
(55482, 'Hajdúböszörmény', 1063, 'HB', 99, 'HU', 47.66667000, 21.51667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q679224'),
(55483, 'Hajdúböszörményi Járás', 1063, 'HB', 99, 'HU', 47.74000000, 21.50000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q831062'),
(55484, 'Hajdúdorog', 1063, 'HB', 99, 'HU', 47.81667000, 21.50000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q252325'),
(55485, 'Hajdúhadház', 1063, 'HB', 99, 'HU', 47.68333000, 21.66667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911530'),
(55486, 'Hajdúhadházi Járás', 1063, 'HB', 99, 'HU', 47.67000000, 21.70000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q282274'),
(55487, 'Hajdúnánás', 1063, 'HB', 99, 'HU', 47.85000000, 21.43333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q739304'),
(55488, 'Hajdúnánási Járás', 1063, 'HB', 99, 'HU', 47.83350000, 21.25256000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q14425545'),
(55489, 'Hajdúszoboszló', 1063, 'HB', 99, 'HU', 47.45000000, 21.40000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q830404'),
(55490, 'Hajdúszovát', 1063, 'HB', 99, 'HU', 47.38333000, 21.48333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q537859'),
(55491, 'Hajdúsámson', 1063, 'HB', 99, 'HU', 47.60000000, 21.76667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911650'),
(55492, 'Hajmáskér', 1054, 'VE', 99, 'HU', 47.14513000, 18.01964000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q602736'),
(55493, 'Hajós', 1048, 'BK', 99, 'HU', 46.39861000, 19.12056000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q933411'),
(55494, 'Halmaj', 1058, 'BZ', 99, 'HU', 48.25000000, 21.00000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1006959'),
(55495, 'Halászi', 1042, 'GS', 99, 'HU', 47.88930000, 17.32615000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q580088'),
(55496, 'Halásztelek', 1059, 'PE', 99, 'HU', 47.36173000, 18.98119000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q378591'),
(55497, 'Harkány', 1055, 'BA', 99, 'HU', 45.85002000, 18.23668000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q842984'),
(55498, 'Harsány', 1058, 'BZ', 99, 'HU', 47.96667000, 20.75000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1006728'),
(55499, 'Harta', 1048, 'BK', 99, 'HU', 46.69758000, 19.03110000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q646203'),
(55500, 'Hatvan', 1040, 'HE', 99, 'HU', 47.66667000, 19.68333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q629849'),
(55501, 'Hatvani Járás', 1040, 'HE', 99, 'HU', 47.69468000, 19.72852000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q16629260'),
(55502, 'Hegyháti Járás', 1055, 'BA', 99, 'HU', 46.27247000, 18.13758000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q3031288'),
(55503, 'Hejőbába', 1058, 'BZ', 99, 'HU', 47.90000000, 20.95000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1006944'),
(55504, 'Helvécia', 1048, 'BK', 99, 'HU', 46.83661000, 19.62251000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1035210'),
(55505, 'Hercegszántó', 1048, 'BK', 99, 'HU', 45.95000000, 18.93917000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q205462'),
(55506, 'Herend', 1054, 'VE', 99, 'HU', 47.13333000, 17.75000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q385982'),
(55507, 'Hernád', 1059, 'PE', 99, 'HU', 47.16238000, 19.43295000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q856693'),
(55508, 'Hernádnémeti', 1058, 'BZ', 99, 'HU', 48.06667000, 20.98333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q259390'),
(55509, 'Heréd', 1040, 'HE', 99, 'HU', 47.70638000, 19.63314000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q771769'),
(55510, 'Heves', 1040, 'HE', 99, 'HU', 47.60000000, 20.28333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q855011'),
(55511, 'Hevesi Járás', 1040, 'HE', 99, 'HU', 47.61535000, 20.31739000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q854348'),
(55512, 'Hidas', 1055, 'BA', 99, 'HU', 46.25680000, 18.49540000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1036139'),
(55513, 'Hodász', 1045, 'SZ', 99, 'HU', 47.91834000, 22.20153000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1037269'),
(55514, 'Hort', 1040, 'HE', 99, 'HU', 47.69081000, 19.78930000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q10719398'),
(55515, 'Hortobágy', 1063, 'HB', 99, 'HU', 47.58278000, 21.15108000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q10719398'),
(55516, 'Hosszúhetény', 1055, 'BA', 99, 'HU', 46.16414000, 18.35077000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q749947'),
(55517, 'Hosszúpályi', 1063, 'HB', 99, 'HU', 47.39303000, 21.73280000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1021859'),
(55518, 'Héhalom', 1051, 'NO', 99, 'HU', 47.78017000, 19.58519000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1057383'),
(55519, 'Hévíz', 1046, 'ZA', 99, 'HU', 46.79031000, 17.18408000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q377929'),
(55520, 'Hévízgyörk', 1059, 'PE', 99, 'HU', 47.63333000, 19.51667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1057545'),
(55521, 'Hódmezővásárhely', 1031, 'CS', 99, 'HU', 46.41667000, 20.33333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q124375'),
(55522, 'Hódmezővásárhelyi Járás', 1031, 'CS', 99, 'HU', 46.43301000, 20.37598000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853858'),
(55523, 'Hőgyész', 1038, 'TO', 99, 'HU', 46.49697000, 18.41841000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q427620'),
(55524, 'Ibrány', 1045, 'SZ', 99, 'HU', 48.12329000, 21.70953000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q854956'),
(55525, 'Ibrányi Járás', 1045, 'SZ', 99, 'HU', 48.12794000, 21.69344000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q714943'),
(55526, 'Iklad', 1059, 'PE', 99, 'HU', 47.66533000, 19.43610000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1089636'),
(55527, 'Inárcs', 1059, 'PE', 99, 'HU', 47.26200000, 19.32700000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q509995'),
(55528, 'Iregszemcse', 1038, 'TO', 99, 'HU', 46.69286000, 18.18581000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1097837'),
(55529, 'Isaszeg', 1059, 'PE', 99, 'HU', 47.53011000, 19.40205000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q515791'),
(55530, 'Iváncsa', 1044, 'FE', 99, 'HU', 47.15670000, 18.82030000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1089641'),
(55531, 'Izsák', 1048, 'BK', 99, 'HU', 46.80454000, 19.35172000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q627878'),
(55532, 'Izsófalva', 1058, 'BZ', 99, 'HU', 48.30000000, 20.66667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q124807'),
(55533, 'Jobbágyi', 1051, 'NO', 99, 'HU', 47.83238000, 19.67762000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1100896'),
(55534, 'Ják', 1039, 'VA', 99, 'HU', 47.14249000, 16.58148000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q50045'),
(55535, 'Jánoshalma', 1048, 'BK', 99, 'HU', 46.29861000, 19.32583000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q549427'),
(55536, 'Jánoshalmai Járás', 1048, 'BK', 99, 'HU', 46.28283000, 19.31742000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1070352'),
(55537, 'Jánoshida', 1043, 'JN', 99, 'HU', 47.38333000, 20.06667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q585316'),
(55538, 'Jánosháza', 1039, 'VA', 99, 'HU', 47.11937000, 17.16503000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1070861'),
(55539, 'Jánossomorja', 1042, 'GS', 99, 'HU', 47.78621000, 17.13603000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911550'),
(55540, 'Járdánháza', 1058, 'BZ', 99, 'HU', 48.15000000, 20.25000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1006968'),
(55541, 'Jászalsószentgyörgy', 1043, 'JN', 99, 'HU', 47.36667000, 20.10000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1102526'),
(55542, 'Jászapáti', 1043, 'JN', 99, 'HU', 47.51667000, 20.15000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q240026'),
(55543, 'Jászapáti Járás', 1043, 'JN', 99, 'HU', 47.46126000, 20.06567000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1102512'),
(55544, 'Jászberény', 1043, 'JN', 99, 'HU', 47.50000000, 19.91667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q281371'),
(55545, 'Jászberényi Járás', 1043, 'JN', 99, 'HU', 47.56500000, 19.86887000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q854302'),
(55546, 'Jászdózsa', 1043, 'JN', 99, 'HU', 47.56610000, 20.01534000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1102540'),
(55547, 'Jászjákóhalma', 1043, 'JN', 99, 'HU', 47.52038000, 19.99086000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q509856'),
(55548, 'Jászkarajenő', 1059, 'PE', 99, 'HU', 47.05000000, 20.06667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1102553'),
(55549, 'Jászkisér', 1043, 'JN', 99, 'HU', 47.45000000, 20.21667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1096156'),
(55550, 'Jászladány', 1043, 'JN', 99, 'HU', 47.36667000, 20.16667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1102564'),
(55551, 'Jászszentandrás', 1043, 'JN', 99, 'HU', 47.58333000, 20.18333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q527857'),
(55552, 'Jászszentlászló', 1048, 'BK', 99, 'HU', 46.56685000, 19.76065000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q782069'),
(55553, 'Jászárokszállás', 1043, 'JN', 99, 'HU', 47.64238000, 19.98038000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q933597'),
(55554, 'Józsefváros', 1064, 'BU', 99, 'HU', 47.48938000, 19.07292000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q933597'),
(55555, 'Kaba', 1063, 'HB', 99, 'HU', 47.35000000, 21.28333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q247387'),
(55556, 'Kadarkút', 1035, 'SO', 99, 'HU', 46.23623000, 17.62014000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q263757'),
(55557, 'Kakucs', 1059, 'PE', 99, 'HU', 47.24200000, 19.36467000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1103372'),
(55558, 'Kalocsa', 1048, 'BK', 99, 'HU', 46.52981000, 18.97283000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q679249'),
(55559, 'Kalocsai Járás', 1048, 'BK', 99, 'HU', 46.55646000, 19.04211000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q912473'),
(55560, 'Kaposmérő', 1035, 'SO', 99, 'HU', 46.36167000, 17.70400000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1104312'),
(55561, 'Kaposvár', 1035, 'SO', 99, 'HU', 46.36667000, 17.80000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q184998'),
(55562, 'Kaposvári Járás', 1035, 'SO', 99, 'HU', 46.39791000, 17.76990000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q385784'),
(55563, 'Kapuvár', 1042, 'GS', 99, 'HU', 47.59224000, 17.02886000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q783255'),
(55564, 'Kapuvári Járás', 1042, 'GS', 99, 'HU', 47.56705000, 17.04056000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q681098'),
(55565, 'Karancskeszi', 1051, 'NO', 99, 'HU', 48.16353000, 19.69686000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q247015'),
(55566, 'Karancslapujtő', 1051, 'NO', 99, 'HU', 48.15000000, 19.73333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1104493'),
(55567, 'Karcag', 1043, 'JN', 99, 'HU', 47.31667000, 20.93333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q756273'),
(55568, 'Karcagi Járás', 1043, 'JN', 99, 'HU', 47.28583000, 20.84218000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853885'),
(55569, 'Karcsa', 1058, 'BZ', 99, 'HU', 48.31130000, 21.80537000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1008016'),
(55570, 'Kartal', 1059, 'PE', 99, 'HU', 47.67133000, 19.54200000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q925492'),
(55571, 'Karácsond', 1040, 'HE', 99, 'HU', 47.72962000, 20.03076000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q278275'),
(55572, 'Karád', 1035, 'SO', 99, 'HU', 46.69076000, 17.84136000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1091464'),
(55573, 'Kaszaper', 1060, 'BE', 99, 'HU', 46.46667000, 20.83333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q924532'),
(55574, 'Katymár', 1048, 'BK', 99, 'HU', 46.03398000, 19.20935000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q387290'),
(55575, 'Kazincbarcika', 1058, 'BZ', 99, 'HU', 48.25000000, 20.63333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q214942'),
(55576, 'Kazincbarcikai Járás', 1058, 'BZ', 99, 'HU', 48.26371000, 20.57978000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1043071'),
(55577, 'Kazár', 1051, 'NO', 99, 'HU', 48.04952000, 19.86143000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q636284'),
(55578, 'Kecel', 1048, 'BK', 99, 'HU', 46.52528000, 19.25194000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q855008'),
(55579, 'Kecskemét', 1048, 'BK', 99, 'HU', 46.90618000, 19.69128000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q171357'),
(55580, 'Kecskeméti Járás', 1048, 'BK', 99, 'HU', 46.88283000, 19.58701000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1070345'),
(55581, 'Kelebia', 1048, 'BK', 99, 'HU', 46.19680000, 19.61659000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q914507'),
(55582, 'Kemecse', 1045, 'SZ', 99, 'HU', 48.07532000, 21.80625000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q911628'),
(55583, 'Kemecsei Járás', 1045, 'SZ', 99, 'HU', 48.10048000, 21.88414000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q16521198'),
(55584, 'Kenderes', 1043, 'JN', 99, 'HU', 47.25000000, 20.68333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q655417'),
(55585, 'Kengyel', 1043, 'JN', 99, 'HU', 47.08333000, 20.33333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1106960'),
(55586, 'Kerecsend', 1040, 'HE', 99, 'HU', 47.80000000, 20.35000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1070818'),
(55587, 'Kerekegyháza', 1048, 'BK', 99, 'HU', 46.93722000, 19.47806000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q854977'),
(55588, 'Kerepes', 1059, 'PE', 99, 'HU', 47.56008000, 19.28289000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q374203'),
(55589, 'Keszthely', 1046, 'ZA', 99, 'HU', 46.76812000, 17.24317000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q526335'),
(55590, 'Keszthelyi Járás', 1046, 'ZA', 99, 'HU', 46.78356000, 17.22381000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q15075381'),
(55591, 'Kevermes', 1060, 'BE', 99, 'HU', 46.41667000, 21.18333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q926132'),
(55592, 'Kimle', 1042, 'GS', 99, 'HU', 47.81726000, 17.36642000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1089628'),
(55593, 'Kincsesbánya', 1044, 'FE', 99, 'HU', 47.26444000, 18.27790000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1108750'),
(55594, 'Kiskunfélegyháza', 1048, 'BK', 99, 'HU', 46.71213000, 19.84458000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q478038'),
(55595, 'Kiskunfélegyházi Járás', 1048, 'BK', 99, 'HU', 46.65721000, 19.77006000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q692204'),
(55596, 'Kiskunhalas', 1048, 'BK', 99, 'HU', 46.43402000, 19.48479000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q329775'),
(55597, 'Kiskunhalasi Járás', 1048, 'BK', 99, 'HU', 46.35832000, 19.53067000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q594485'),
(55598, 'Kiskunlacháza', 1059, 'PE', 99, 'HU', 47.18839000, 19.00930000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1095159'),
(55599, 'Kiskunmajsa', 1048, 'BK', 99, 'HU', 46.49028000, 19.74000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q629471'),
(55600, 'Kiskunmajsai Járás', 1048, 'BK', 99, 'HU', 46.49608000, 19.72316000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1070041'),
(55601, 'Kisköre', 1040, 'HE', 99, 'HU', 47.50000000, 20.50000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q222242'),
(55602, 'Kiskőrös', 1048, 'BK', 99, 'HU', 46.62139000, 19.28528000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q646850'),
(55603, 'Kiskőrösi Járás', 1048, 'BK', 99, 'HU', 46.63309000, 19.33726000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1070417'),
(55604, 'Kisláng', 1044, 'FE', 99, 'HU', 46.95744000, 18.38813000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1111189'),
(55605, 'Kisléta', 1045, 'SZ', 99, 'HU', 47.84244000, 22.00393000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q740899'),
(55606, 'Kismaros', 1059, 'PE', 99, 'HU', 47.83742000, 19.00463000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1089453'),
(55607, 'Kispest', 1064, 'BU', 99, 'HU', 47.45150000, 19.14017000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q857943'),
(55608, 'Kisszállás', 1048, 'BK', 99, 'HU', 46.28009000, 19.48954000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q589975'),
(55609, 'Kistarcsa', 1059, 'PE', 99, 'HU', 47.54757000, 19.26247000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q512073'),
(55610, 'Kistelek', 1031, 'CS', 99, 'HU', 46.47250000, 19.97972000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q855529'),
(55611, 'Kisteleki Járás', 1031, 'CS', 99, 'HU', 46.48835000, 19.99568000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853898'),
(55612, 'Kisvárda', 1045, 'SZ', 99, 'HU', 48.21667000, 22.08333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q674311'),
(55613, 'Kisvárdai Járás', 1045, 'SZ', 99, 'HU', 48.22100000, 22.06415000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q282774'),
(55614, 'Kiszombor', 1031, 'CS', 99, 'HU', 46.18333000, 20.43333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1112590'),
(55615, 'Kisújszállás', 1043, 'JN', 99, 'HU', 47.21667000, 20.76667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911634'),
(55616, 'Kocsord', 1045, 'SZ', 99, 'HU', 47.93912000, 22.38333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q587123'),
(55617, 'Kocsér', 1059, 'PE', 99, 'HU', 47.00165000, 19.92067000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1114801'),
(55618, 'Komló', 1055, 'BA', 99, 'HU', 46.19278000, 18.26494000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q430207'),
(55619, 'Komlói Járás', 1055, 'BA', 99, 'HU', 46.22019000, 18.28620000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1070048'),
(55620, 'Kompolt', 1040, 'HE', 99, 'HU', 47.73333000, 20.25000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q936331'),
(55621, 'Komádi', 1063, 'HB', 99, 'HU', 47.00000000, 21.50000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q591386'),
(55622, 'Kondoros', 1060, 'BE', 99, 'HU', 46.76667000, 20.80000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1090008'),
(55623, 'Konyár', 1063, 'HB', 99, 'HU', 47.31667000, 21.66667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1089253'),
(55624, 'Kosd', 1059, 'PE', 99, 'HU', 47.80791000, 19.17821000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q265671'),
(55625, 'Kozármisleny', 1055, 'BA', 99, 'HU', 46.02967000, 18.29210000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q941032'),
(55626, 'Kunfehértó', 1048, 'BK', 99, 'HU', 46.36091000, 19.41454000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q933764'),
(55627, 'Kunhegyes', 1043, 'JN', 99, 'HU', 47.36667000, 20.63333000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q933645'),
(55628, 'Kunhegyesi Járás', 1043, 'JN', 99, 'HU', 47.40329000, 20.59199000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1121931'),
(55629, 'Kunmadaras', 1043, 'JN', 99, 'HU', 47.43333000, 20.80000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1021844'),
(55630, 'Kunszentmiklós', 1048, 'BK', 99, 'HU', 47.02699000, 19.12575000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q843018'),
(55631, 'Kunszentmiklósi Járás', 1048, 'BK', 99, 'HU', 46.94810000, 19.15563000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1121984'),
(55632, 'Kunszentmárton', 1043, 'JN', 99, 'HU', 46.83916000, 20.28879000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q379160'),
(55633, 'Kunszentmártoni Járás', 1043, 'JN', 99, 'HU', 46.87698000, 20.25179000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q853199'),
(55634, 'Kunágota', 1060, 'BE', 99, 'HU', 46.43333000, 21.05000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1076687'),
(55635, 'Kál', 1040, 'HE', 99, 'HU', 47.73333000, 20.26667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1043477'),
(55636, 'Kállósemjén', 1045, 'SZ', 99, 'HU', 47.86081000, 21.93929000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q369917'),
(55637, 'Kálmánháza', 1045, 'SZ', 99, 'HU', 47.88333000, 21.58333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q632835'),
(55638, 'Káloz', 1044, 'FE', 99, 'HU', 46.95464000, 18.48259000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1075976'),
(55639, 'Kántorjánosi', 1045, 'SZ', 99, 'HU', 47.93333000, 22.15000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q632023'),
(55640, 'Kápolnásnyék', 1044, 'FE', 99, 'HU', 47.24004000, 18.67564000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1066039'),
(55641, 'Kék', 1045, 'SZ', 99, 'HU', 48.11667000, 21.88333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q742817'),
(55642, 'Kétegyháza', 1060, 'BE', 99, 'HU', 46.53333000, 21.18333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q654192'),
(55643, 'Kéthely', 1035, 'SO', 99, 'HU', 46.64605000, 17.39362000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q570622'),
(55644, 'Kóka', 1059, 'PE', 99, 'HU', 47.48552000, 19.57876000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1124858'),
(55645, 'Kóny', 1042, 'GS', 99, 'HU', 47.63053000, 17.35717000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q427433'),
(55646, 'Kótaj', 1045, 'SZ', 99, 'HU', 48.05000000, 21.71667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1125048'),
(55647, 'Körmend', 1039, 'VA', 99, 'HU', 47.01096000, 16.60596000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q830411'),
(55648, 'Körmendi Járás', 1039, 'VA', 99, 'HU', 46.92164000, 16.53291000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q536394'),
(55649, 'Körösladány', 1060, 'BE', 99, 'HU', 46.96667000, 21.08333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911620'),
(55650, 'Köröstarcsa', 1060, 'BE', 99, 'HU', 46.88333000, 21.03333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1075945'),
(55651, 'Kőszeg', 1039, 'VA', 99, 'HU', 47.38922000, 16.54100000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q613813'),
(55652, 'Kőszegi Járás', 1039, 'VA', 99, 'HU', 47.37500000, 16.65650000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q831516'),
(55653, 'Lajoskomárom', 1044, 'FE', 99, 'HU', 46.84201000, 18.33763000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1127495'),
(55654, 'Lajosmizse', 1048, 'BK', 99, 'HU', 47.02133000, 19.56171000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q843025'),
(55655, 'Lakitelek', 1048, 'BK', 99, 'HU', 46.87601000, 19.99504000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q277798'),
(55656, 'Lengyeltóti', 1035, 'SO', 99, 'HU', 46.67013000, 17.64398000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911610'),
(55657, 'Lenti', 1046, 'ZA', 99, 'HU', 46.62403000, 16.53863000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q854965'),
(55658, 'Lenti Járás', 1046, 'ZA', 99, 'HU', 46.65649000, 16.57443000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q262110'),
(55659, 'Lepsény', 1044, 'FE', 99, 'HU', 46.99036000, 18.24357000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q370974'),
(55660, 'Letenye', 1046, 'ZA', 99, 'HU', 46.43301000, 16.72583000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q854969'),
(55661, 'Letenyei Járás', 1046, 'ZA', 99, 'HU', 46.50552000, 16.76503000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q656097'),
(55662, 'Levelek', 1045, 'SZ', 99, 'HU', 47.96282000, 21.98537000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1089876'),
(55663, 'Leányfalu', 1059, 'PE', 99, 'HU', 47.71778000, 19.08585000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q373512'),
(55664, 'Litér', 1054, 'VE', 99, 'HU', 47.10104000, 18.00454000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1089824'),
(55665, 'Lovasberény', 1044, 'FE', 99, 'HU', 47.30997000, 18.55177000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q951765'),
(55666, 'Lábod', 1035, 'SO', 99, 'HU', 46.20535000, 17.45419000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q666905'),
(55667, 'Lánycsók', 1055, 'BA', 99, 'HU', 46.00543000, 18.62526000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1075484'),
(55668, 'Lébény', 1042, 'GS', 99, 'HU', 47.73574000, 17.39076000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1160417'),
(55669, 'Létavértes', 1063, 'HB', 99, 'HU', 47.38333000, 21.90000000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q911517'),
(55670, 'Lőkösháza', 1060, 'BE', 99, 'HU', 46.43333000, 21.23333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q948356'),
(55671, 'Lőrinci', 1040, 'HE', 99, 'HU', 47.73295000, 19.67867000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q1088716'),
(55672, 'Madaras', 1048, 'BK', 99, 'HU', 46.05870000, 19.26121000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1091456'),
(55673, 'Madocsa', 1038, 'TO', 99, 'HU', 46.68790000, 18.95791000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1161473'),
(55674, 'Maglód', 1059, 'PE', 99, 'HU', 47.44258000, 19.36438000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q283023'),
(55675, 'Magyarbánhegyes', 1060, 'BE', 99, 'HU', 46.45000000, 20.96667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q773272'),
(55676, 'Maklár', 1040, 'HE', 99, 'HU', 47.80000000, 20.41667000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q521373'),
(55677, 'Makó', 1031, 'CS', 99, 'HU', 46.21667000, 20.48333000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q240504'),
(55678, 'Makói Járás', 1031, 'CS', 99, 'HU', 46.26698000, 20.54200000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q920588'),
(55679, 'Marcali', 1035, 'SO', 99, 'HU', 46.58498000, 17.41196000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q843009'),
(55680, 'Marcali Járás', 1035, 'SO', 99, 'HU', 46.54382000, 17.39203000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q598885'),
(55681, 'Maroslele', 1031, 'CS', 99, 'HU', 46.26667000, 20.35000000, '2019-10-05 22:53:35', '2019-10-05 22:53:35', 1, 'Q1165687'),
(55682, 'Martonvásár', 1044, 'FE', 99, 'HU', 47.31601000, 18.79045000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q933641'),
(55683, 'Martonvásári Járás', 1044, 'FE', 99, 'HU', 47.29040000, 18.79945000, '2019-10-05 22:53:35', '2020-05-01 17:22:51', 1, 'Q852256'),
(55684, 'Mecseknádasd', 1055, 'BA', 99, 'HU', 46.22468000, 18.47076000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q930968'),
(55685, 'Medgyesegyháza', 1060, 'BE', 99, 'HU', 46.50000000, 21.03333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q609257'),
(55686, 'Megyaszó', 1058, 'BZ', 99, 'HU', 48.18333000, 21.05000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1018346'),
(55687, 'Mende', 1059, 'PE', 99, 'HU', 47.43133000, 19.45628000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q773365'),
(55688, 'Mezőberény', 1060, 'BE', 99, 'HU', 46.81667000, 21.03333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q842961'),
(55689, 'Mezőcsát', 1058, 'BZ', 99, 'HU', 47.81667000, 20.91667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q304803'),
(55690, 'Mezőcsáti Járás', 1058, 'BZ', 99, 'HU', 47.79841000, 20.91693000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14630100'),
(55691, 'Mezőfalva', 1044, 'FE', 99, 'HU', 46.93184000, 18.77177000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1072292'),
(55692, 'Mezőhegyes', 1060, 'BE', 99, 'HU', 46.31667000, 20.81667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q509923'),
(55693, 'Mezőkeresztes', 1058, 'BZ', 99, 'HU', 47.83333000, 20.70000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q966970'),
(55694, 'Mezőkovácsháza', 1060, 'BE', 99, 'HU', 46.40000000, 20.91667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q855032'),
(55695, 'Mezőkovácsházai Járás', 1060, 'BE', 99, 'HU', 46.40888000, 21.00318000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14301645'),
(55696, 'Mezőkövesd', 1058, 'BZ', 99, 'HU', 47.81667000, 20.58333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q933638'),
(55697, 'Mezőkövesdi Járás', 1058, 'BZ', 99, 'HU', 47.84876000, 20.63623000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14630116'),
(55698, 'Mezőszilas', 1044, 'FE', 99, 'HU', 46.81109000, 18.47789000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1176573'),
(55699, 'Mezőtúr', 1043, 'JN', 99, 'HU', 47.00000000, 20.63333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q855052'),
(55700, 'Mezőtúri Járás', 1043, 'JN', 99, 'HU', 47.02495000, 20.59046000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q831151'),
(55701, 'Mezőzombor', 1058, 'BZ', 99, 'HU', 48.15000000, 21.26667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1017840'),
(55702, 'Mihályi', 1042, 'GS', 99, 'HU', 47.51384000, 17.09507000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q283505'),
(55703, 'Mikepércs', 1063, 'HB', 99, 'HU', 47.45000000, 21.63333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1177020'),
(55704, 'Mindszent', 1031, 'CS', 99, 'HU', 46.52362000, 20.19038000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q933424'),
(55705, 'Miskolc', 1058, 'BZ', 99, 'HU', 48.10000000, 20.78333000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q102397'),
(55706, 'Miskolci Járás', 1058, 'BZ', 99, 'HU', 48.08675000, 20.77353000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14630137'),
(55707, 'Mogyoród', 1059, 'PE', 99, 'HU', 47.59748000, 19.24070000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q649514'),
(55708, 'Mohács', 1055, 'BA', 99, 'HU', 45.99020000, 18.68621000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q320798'),
(55709, 'Mohácsi Járás', 1055, 'BA', 99, 'HU', 46.02004000, 18.68000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q948656'),
(55710, 'Monok', 1058, 'BZ', 99, 'HU', 48.21102000, 21.15052000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1019082'),
(55711, 'Monor', 1059, 'PE', 99, 'HU', 47.35133000, 19.44733000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q749320'),
(55712, 'Monori Járás', 1059, 'PE', 99, 'HU', 47.34990000, 19.47680000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q612006'),
(55713, 'Monostorpályi', 1063, 'HB', 99, 'HU', 47.40000000, 21.78333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q947311'),
(55714, 'Mosonmagyaróvár', 1042, 'GS', 99, 'HU', 47.86789000, 17.26994000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q207406'),
(55715, 'Mosonmagyaróvári Járás', 1042, 'GS', 99, 'HU', 47.85767000, 17.28212000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q921261'),
(55716, 'Mosonszentmiklós', 1042, 'GS', 99, 'HU', 47.72778000, 17.42784000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q922488'),
(55717, 'Murakeresztúr', 1046, 'ZA', 99, 'HU', 46.36422000, 16.88177000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1179923'),
(55718, 'Mád', 1058, 'BZ', 99, 'HU', 48.19442000, 21.28208000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1018357'),
(55719, 'Mágocs', 1055, 'BA', 99, 'HU', 46.34998000, 18.23240000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q584114'),
(55720, 'Mályi', 1058, 'BZ', 99, 'HU', 48.01667000, 20.83333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q250239'),
(55721, 'Mándok', 1045, 'SZ', 99, 'HU', 48.32149000, 22.19107000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1075540'),
(55722, 'Mány', 1044, 'FE', 99, 'HU', 47.53352000, 18.65627000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185341'),
(55723, 'Máriapócs', 1045, 'SZ', 99, 'HU', 47.88303000, 22.02501000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q577290'),
(55724, 'Mátraderecske', 1040, 'HE', 99, 'HU', 47.95000000, 20.08333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q603285'),
(55725, 'Mátranovák', 1051, 'NO', 99, 'HU', 48.03809000, 19.98257000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185150'),
(55726, 'Mátraterenye', 1051, 'NO', 99, 'HU', 48.03267000, 19.94762000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185150'),
(55727, 'Mátraverebély', 1051, 'NO', 99, 'HU', 47.97421000, 19.78049000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1075176'),
(55728, 'Mátészalka', 1045, 'SZ', 99, 'HU', 47.95528000, 22.32348000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q842976'),
(55729, 'Mátészalkai Járás', 1045, 'SZ', 99, 'HU', 47.92961000, 22.31892000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q694051'),
(55730, 'Méhkerék', 1060, 'BE', 99, 'HU', 46.78333000, 21.45000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1075840'),
(55731, 'Mélykút', 1048, 'BK', 99, 'HU', 46.21509000, 19.38102000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q977027'),
(55732, 'Mérk', 1045, 'SZ', 99, 'HU', 47.78824000, 22.38038000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1187296'),
(55733, 'Mór', 1044, 'FE', 99, 'HU', 47.37787000, 18.20353000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q680963'),
(55734, 'Mórahalmi Járás', 1031, 'CS', 99, 'HU', 46.26840000, 19.82256000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q268820'),
(55735, 'Mórahalom', 1031, 'CS', 99, 'HU', 46.21806000, 19.88510000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q946769'),
(55736, 'Móri Járás', 1044, 'FE', 99, 'HU', 47.34685000, 18.19838000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q659414'),
(55737, 'Múcsony', 1058, 'BZ', 99, 'HU', 48.26667000, 20.68333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1089706'),
(55738, 'Nagyatád', 1035, 'SO', 99, 'HU', 46.22961000, 17.35788000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q295367'),
(55739, 'Nagyatádi Járás', 1035, 'SO', 99, 'HU', 46.25200000, 17.37520000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q853205'),
(55740, 'Nagybajom', 1035, 'SO', 99, 'HU', 46.39232000, 17.51147000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q933374'),
(55741, 'Nagybaracska', 1048, 'BK', 99, 'HU', 46.04248000, 18.90590000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q513824'),
(55742, 'Nagycenk', 1042, 'GS', 99, 'HU', 47.60435000, 16.69732000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q370831'),
(55743, 'Nagycserkesz', 1045, 'SZ', 99, 'HU', 47.96667000, 21.53333000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1185424'),
(55744, 'Nagydobos', 1045, 'SZ', 99, 'HU', 48.05759000, 22.30423000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1185308'),
(55745, 'Nagydorog', 1038, 'TO', 99, 'HU', 46.62749000, 18.65565000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1071254'),
(55746, 'Nagyecsed', 1045, 'SZ', 99, 'HU', 47.86547000, 22.39159000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q932823'),
(55747, 'Nagyhalász', 1045, 'SZ', 99, 'HU', 48.13266000, 21.76104000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q933442'),
(55748, 'Nagykanizsa', 1046, 'ZA', 99, 'HU', 46.45347000, 16.99104000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q14424'),
(55749, 'Nagykanizsai Járás', 1046, 'ZA', 99, 'HU', 46.51873000, 17.04433000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q388398'),
(55750, 'Nagykovácsi', 1059, 'PE', 99, 'HU', 47.65000000, 19.01667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q388398'),
(55751, 'Nagykálló', 1045, 'SZ', 99, 'HU', 47.87491000, 21.84082000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q388398'),
(55752, 'Nagykállói Járás', 1045, 'SZ', 99, 'HU', 47.79994000, 21.84753000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q694064'),
(55753, 'Nagykáta', 1059, 'PE', 99, 'HU', 47.41514000, 19.74410000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q766168'),
(55754, 'Nagykátai Járás', 1059, 'PE', 99, 'HU', 47.39109000, 19.73920000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q831162'),
(55755, 'Nagykőrös', 1059, 'PE', 99, 'HU', 47.03419000, 19.77857000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q830394'),
(55756, 'Nagykőrösi Járás', 1059, 'PE', 99, 'HU', 47.03563000, 19.79259000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q16522077'),
(55757, 'Nagymaros', 1059, 'PE', 99, 'HU', 47.79280000, 18.95984000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q282460'),
(55758, 'Nagymányok', 1038, 'TO', 99, 'HU', 46.27911000, 18.45489000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1096203'),
(55759, 'Nagyoroszi', 1051, 'NO', 99, 'HU', 48.00503000, 19.09050000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q510644'),
(55760, 'Nagyrábé', 1063, 'HB', 99, 'HU', 47.20000000, 21.33333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1021867'),
(55761, 'Nagyréde', 1040, 'HE', 99, 'HU', 47.76543000, 19.84819000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q371004'),
(55762, 'Nagyszénás', 1060, 'BE', 99, 'HU', 46.68333000, 20.66667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1090004'),
(55763, 'Nagytarcsa', 1059, 'PE', 99, 'HU', 47.53128000, 19.28343000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q685439'),
(55764, 'Napkor', 1045, 'SZ', 99, 'HU', 47.93797000, 21.86763000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q221440'),
(55765, 'Nemesnádudvar', 1048, 'BK', 99, 'HU', 46.34051000, 19.05115000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q934974'),
(55766, 'Nemesvámos', 1054, 'VE', 99, 'HU', 47.05514000, 17.87477000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185333'),
(55767, 'Nyáregyháza', 1059, 'PE', 99, 'HU', 47.26175000, 19.50146000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185674'),
(55768, 'Nyárlőrinc', 1048, 'BK', 99, 'HU', 46.86017000, 19.87836000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1051493'),
(55769, 'Nyékládháza', 1058, 'BZ', 99, 'HU', 47.98333000, 20.83333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q911567'),
(55770, 'Nyíracsád', 1063, 'HB', 99, 'HU', 47.60330000, 21.97208000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q377879'),
(55771, 'Nyíradony', 1063, 'HB', 99, 'HU', 47.69746000, 21.91878000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q375862'),
(55772, 'Nyíradonyi Járás', 1063, 'HB', 99, 'HU', 47.57873000, 22.01381000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14424877'),
(55773, 'Nyírbogdány', 1045, 'SZ', 99, 'HU', 48.05723000, 21.88242000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1184682'),
(55774, 'Nyírbogát', 1045, 'SZ', 99, 'HU', 47.80340000, 22.06561000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185749'),
(55775, 'Nyírbátor', 1045, 'SZ', 99, 'HU', 47.83333000, 22.13333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q755686'),
(55776, 'Nyírbátori Járás', 1045, 'SZ', 99, 'HU', 47.77553000, 22.11755000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q570468'),
(55777, 'Nyírbéltek', 1045, 'SZ', 99, 'HU', 47.70000000, 22.13333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185472'),
(55778, 'Nyírcsaholy', 1045, 'SZ', 99, 'HU', 47.90383000, 22.33630000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q647474'),
(55779, 'Nyíregyháza', 1045, 'SZ', 99, 'HU', 47.95539000, 21.71671000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q171223'),
(55780, 'Nyíregyházi Járás', 1045, 'SZ', 99, 'HU', 47.97691000, 21.64463000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q694056'),
(55781, 'Nyírgyulaj', 1045, 'SZ', 99, 'HU', 47.88622000, 22.09781000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185987'),
(55782, 'Nyírkarász', 1045, 'SZ', 99, 'HU', 48.09477000, 22.10463000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185959'),
(55783, 'Nyírlugos', 1045, 'SZ', 99, 'HU', 47.69315000, 22.04476000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q933454'),
(55784, 'Nyírmada', 1045, 'SZ', 99, 'HU', 48.06667000, 22.20000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1073325'),
(55785, 'Nyírmeggyes', 1045, 'SZ', 99, 'HU', 47.91667000, 22.26667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1089284'),
(55786, 'Nyírmihálydi', 1045, 'SZ', 99, 'HU', 47.73976000, 21.96445000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1071994'),
(55787, 'Nyírmártonfalva', 1063, 'HB', 99, 'HU', 47.58333000, 21.90000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185652'),
(55788, 'Nyírpazony', 1045, 'SZ', 99, 'HU', 47.98333000, 21.80000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q514273'),
(55789, 'Nyírtass', 1045, 'SZ', 99, 'HU', 48.11667000, 22.03333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1079857'),
(55790, 'Nyírtelek', 1045, 'SZ', 99, 'HU', 48.01667000, 21.63333000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q933345'),
(55791, 'Nyírvasvári', 1045, 'SZ', 99, 'HU', 47.81667000, 22.18683000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185762'),
(55792, 'Nyírábrány', 1063, 'HB', 99, 'HU', 47.55311000, 22.02401000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q737046'),
(55793, 'Nyúl', 1042, 'GS', 99, 'HU', 47.59047000, 17.68904000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q935309'),
(55794, 'Nádudvar', 1063, 'HB', 99, 'HU', 47.41667000, 21.16667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q933389'),
(55795, 'Németkér', 1038, 'TO', 99, 'HU', 46.71637000, 18.76311000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1097093'),
(55796, 'Okány', 1060, 'BE', 99, 'HU', 46.90000000, 21.35000000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1075972'),
(55797, 'Olaszliszka', 1058, 'BZ', 99, 'HU', 48.25000000, 21.43333000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1017864'),
(55798, 'Onga', 1058, 'BZ', 99, 'HU', 48.11667000, 20.91667000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1184433'),
(55799, 'Orgovány', 1048, 'BK', 99, 'HU', 46.75087000, 19.47259000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1051888'),
(55800, 'Orosháza', 1060, 'BE', 99, 'HU', 46.56667000, 20.66667000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q390796'),
(55801, 'Orosházi Járás', 1060, 'BE', 99, 'HU', 46.56355000, 20.73357000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q14629781'),
(55802, 'Ostoros', 1040, 'HE', 99, 'HU', 47.86667000, 20.43333000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q1071246'),
(55803, 'Ozora', 1038, 'TO', 99, 'HU', 46.75133000, 18.40010000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q222100'),
(55804, 'Pacsa', 1046, 'ZA', 99, 'HU', 46.71981000, 17.01401000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q519596'),
(55805, 'Paks', 1038, 'TO', 99, 'HU', 46.62210000, 18.85569000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q740800'),
(55806, 'Paksi Járás', 1038, 'TO', 99, 'HU', 46.66035000, 18.78572000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q830865'),
(55807, 'Palotás', 1051, 'NO', 99, 'HU', 47.79528000, 19.59618000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q631749'),
(55808, 'Pannonhalma', 1042, 'GS', 99, 'HU', 47.54946000, 17.75535000, '2019-10-05 22:53:36', '2019-10-05 22:53:36', 1, 'Q650015'),
(55809, 'Pannonhalmi Járás', 1042, 'GS', 99, 'HU', 47.45571000, 17.81335000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q15275777'),
(55810, 'Parád', 1040, 'HE', 99, 'HU', 47.92323000, 20.02972000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q935066'),
(55811, 'Parádsasvár', 1040, 'HE', 99, 'HU', 47.91260000, 19.97709000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1185447'),
(55812, 'Pellérd', 1055, 'BA', 99, 'HU', 46.03438000, 18.15403000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1074853'),
(55813, 'Perbál', 1059, 'PE', 99, 'HU', 47.58957000, 18.76099000, '2019-10-05 22:53:36', '2020-05-01 17:22:51', 1, 'Q1027271');

