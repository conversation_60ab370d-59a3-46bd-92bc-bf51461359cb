INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(69490, 'Corralejo de Arriba', 3469, 'GUA', 142, 'MX', 20.89667000, -100.65444000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q20134699'),
(69491, 'Corralero', 3448, 'OAX', 142, 'MX', 16.23846000, -98.18952000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q20134699'),
(69492, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 3464, 'VER', 142, 'MX', 20.48273000, -97.52582000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q20222152'),
(69493, '<PERSON><PERSON><PERSON> 1ra. Sección', 3454, 'T<PERSON>', 142, 'MX', 18.15767000, -92.99372000, '2019-10-05 23:08:35', '2020-05-01 17:23:02', 1, 'Q20227752'),
(69494, 'Cortazar', 3469, 'GUA', 142, 'MX', 20.48362000, -100.96237000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q25425552'),
(69495, 'Corupo', 3474, 'MIC', 142, 'MX', 19.60912000, -102.23479000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q25425552'),
(69496, 'Cosalá', 3449, 'SIN', 142, 'MX', 24.41501000, -106.69073000, '2019-10-05 23:08:35', '2020-05-01 17:23:02', 1, 'Q1135979'),
(69497, 'Cosamaloapan', 3464, 'VER', 142, 'MX', 18.36759000, -95.79857000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q506848'),
(69498, 'Cosamaloapan de Carpio', 3464, 'VER', 142, 'MX', 18.30163000, -95.95967000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q5655703'),
(69499, 'Cosautlán', 3464, 'VER', 142, 'MX', 19.33196000, -96.99030000, '2019-10-05 23:08:35', '2020-05-01 17:23:02', 1, 'Q20269086'),
(69500, 'Cosautlán de Carvajal', 3464, 'VER', 142, 'MX', 19.32878000, -96.97719000, '2019-10-05 23:08:35', '2020-05-01 17:23:02', 1, 'Q3847170'),
(69501, 'Coscomate del Progreso', 3450, 'MEX', 142, 'MX', 19.93416000, -99.51902000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q3847170'),
(69502, 'Coscomatepec de Bravo', 3464, 'VER', 142, 'MX', 19.07275000, -97.04685000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q543592'),
(69503, 'Cosolapa', 3464, 'VER', 142, 'MX', 18.60055000, -96.68524000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q20265355'),
(69504, 'Cosoleacaque', 3464, 'VER', 142, 'MX', 17.99913000, -94.63590000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q27769965'),
(69505, 'Cosoltepec', 3448, 'OAX', 142, 'MX', 18.14255000, -97.79084000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q3893403'),
(69506, 'Costa Azul', 3449, 'SIN', 142, 'MX', 25.10183000, -108.13735000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q3893403'),
(69507, 'Cosío', 3456, 'AGU', 142, 'MX', 22.36667000, -102.30000000, '2019-10-05 23:08:35', '2020-05-01 17:22:59', 1, 'Q3893403'),
(69508, 'Cotaxtla', 3464, 'VER', 142, 'MX', 18.83555000, -96.39640000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q3847123'),
(69509, 'Cotija de la Paz', 3474, 'MIC', 142, 'MX', 19.80922000, -102.70198000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q2411306'),
(69510, 'Cotorina', 3456, 'AGU', 142, 'MX', 21.75154000, -102.26832000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q2411306'),
(69511, 'Coxcatlán', 3476, 'PUE', 142, 'MX', 18.26709000, -97.15078000, '2019-10-05 23:08:35', '2020-05-01 17:23:01', 1, 'Q20135264'),
(69512, 'Coxolico', 3476, 'PUE', 142, 'MX', 18.45104000, -97.02991000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q20135292'),
(69513, 'Coxquihui', 3464, 'VER', 142, 'MX', 20.18398000, -97.58568000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q20135304'),
(69514, 'Coyahualco', 3459, 'GRO', 142, 'MX', 17.74139000, -98.56667000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q20276628'),
(69515, 'Coyame', 3447, 'CHH', 142, 'MX', 29.46141000, -105.09404000, '2019-10-05 23:08:35', '2019-10-05 23:08:35', 1, 'Q1997768'),
(69516, 'Coyame del Sotol', 3447, 'CHH', 142, 'MX', 29.68793000, -105.13855000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3308551'),
(69517, 'Coyoacán', 3473, 'CMX', 142, 'MX', 19.34670000, -99.16174000, '2019-10-05 23:08:36', '2024-01-24 12:10:50', 1, 'Q661315'),
(69518, 'Coyolito', 3464, 'VER', 142, 'MX', 19.22777000, -96.87039000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q661315'),
(69519, 'Coyomeapan', 3476, 'PUE', 142, 'MX', 18.28206000, -96.99336000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q1998751'),
(69520, 'Coyote', 3471, 'COA', 142, 'MX', 25.69510000, -103.28420000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20135480'),
(69521, 'Coyotepec', 3450, 'MEX', 142, 'MX', 19.77722000, -99.21295000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20264008'),
(69522, 'Coyotepec', 3476, 'PUE', 142, 'MX', 18.41656000, -97.82703000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5180099'),
(69523, 'Coyotillos', 3470, 'HID', 142, 'MX', 19.99980000, -99.15817000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5180099'),
(69524, 'Coyotillos', 3455, 'QUE', 142, 'MX', 20.59938000, -100.20898000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5180099'),
(69525, 'Coyotitán', 3449, 'SIN', 142, 'MX', 23.79416000, -106.60080000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q5180099'),
(69526, 'Coyuca de Benítez', 3459, 'GRO', 142, 'MX', 17.00895000, -100.08714000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q5180109'),
(69527, 'Coyuca de Catalán', 3459, 'GRO', 142, 'MX', 18.32614000, -100.69898000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q9176109'),
(69528, 'Coyuquilla Norte', 3459, 'GRO', 142, 'MX', 17.38027000, -101.05631000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q9176109'),
(69529, 'Coyutla', 3464, 'VER', 142, 'MX', 20.24720000, -97.65824000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3847216'),
(69530, 'Cozotlán Norte', 3450, 'MEX', 142, 'MX', 19.70833000, -98.86000000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q20135615'),
(69531, 'Cozumel', 3467, 'ROO', 142, 'MX', 20.50038000, -86.94272000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q929946'),
(69532, 'Creel', 3447, 'CHH', 142, 'MX', 27.75054000, -107.63520000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q1020789'),
(69533, 'Crescencio Morales (San Mateo)', 3474, 'MIC', 142, 'MX', 19.49111000, -100.24306000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20236611'),
(69534, 'Cristo Rey', 3449, 'SIN', 142, 'MX', 22.58167000, -105.72444000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20276639'),
(69535, 'Cristóbal Colón', 3451, 'CHP', 142, 'MX', 17.19194000, -91.62917000, '2019-10-05 23:08:36', '2020-05-01 17:22:59', 1, 'Q20211779'),
(69536, 'Cristóbal Obregón', 3451, 'CHP', 142, 'MX', 16.43246000, -93.45689000, '2019-10-05 23:08:36', '2020-05-01 17:22:59', 1, 'Q20211782'),
(69537, 'Crisóstomos', 3462, 'ZAC', 142, 'MX', 22.22961000, -101.99615000, '2019-10-05 23:08:36', '2020-05-01 17:23:03', 1, 'Q20211782'),
(69538, 'Crucecita', 3448, 'OAX', 142, 'MX', 15.76889000, -96.13500000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20262542'),
(69539, 'Crucero Las Pilas', 3456, 'AGU', 142, 'MX', 21.82722000, -102.76033000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20262542'),
(69540, 'Cruillas', 3463, 'TAM', 142, 'MX', 24.75658000, -98.53739000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20226524'),
(69541, 'Cruz Blanca', 3464, 'VER', 142, 'MX', 19.63744000, -97.16691000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20226524'),
(69542, 'Cruz Blanca', 3449, 'SIN', 142, 'MX', 25.67460000, -108.37090000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20226524'),
(69543, 'Cruz Grande', 3459, 'GRO', 142, 'MX', 16.72241000, -99.12356000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5190309'),
(69544, 'Cruz Quemada', 3459, 'GRO', 142, 'MX', 16.99722000, -99.17964000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5190309'),
(69545, 'Cruz Verde', 3464, 'VER', 142, 'MX', 19.13750000, -97.10861000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20269091'),
(69546, 'Cruz de Huanacaxtle', 3477, 'NAY', 142, 'MX', 20.75415000, -105.37734000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20269091'),
(69547, 'Cruz del Milagro', 3464, 'VER', 142, 'MX', 17.90833000, -95.01083000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20136343'),
(69548, 'Cruztón', 3451, 'CHP', 142, 'MX', 16.76167000, -92.57917000, '2019-10-05 23:08:36', '2020-05-01 17:22:59', 1, 'Q20276709'),
(69549, 'Cuacnopalan', 3476, 'PUE', 142, 'MX', 18.81758000, -97.51116000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20261722'),
(69550, 'Cuacuila', 3476, 'PUE', 142, 'MX', 20.17205000, -98.03150000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20261722'),
(69551, 'Cuadrilla Vieja', 3450, 'MEX', 142, 'MX', 19.51205000, -99.98692000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20237756'),
(69552, 'Cuadrilla de Dolores', 3450, 'MEX', 142, 'MX', 19.14000000, -100.06944000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20276715'),
(69553, 'Cuajinicuilapa', 3459, 'GRO', 142, 'MX', 16.47347000, -98.41389000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3311914'),
(69554, 'Cualác', 3459, 'GRO', 142, 'MX', 17.72928000, -98.67254000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q3888087'),
(69555, 'Cuamiles', 3477, 'NAY', 142, 'MX', 21.92347000, -105.25405000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3888087'),
(69556, 'Cuamio', 3474, 'MIC', 142, 'MX', 20.03390000, -101.15167000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3888087'),
(69557, 'Cuanacaxtitlán', 3459, 'GRO', 142, 'MX', 16.79953000, -98.63992000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q3888087'),
(69558, 'Cuanajo', 3474, 'MIC', 142, 'MX', 19.48485000, -101.50794000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20136884'),
(69559, 'Cuanalá', 3476, 'PUE', 142, 'MX', 19.10917000, -98.32788000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q20261739'),
(69560, 'Cuapaxtitla', 3470, 'HID', 142, 'MX', 21.13292000, -98.55264000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20261739'),
(69561, 'Cuapiaxtla', 3458, 'TLA', 142, 'MX', 19.29588000, -97.76880000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20136919'),
(69562, 'Cuapiaxtla de Madero', 3476, 'PUE', 142, 'MX', 18.91544000, -97.82382000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5191855'),
(69563, 'Cuapinolito (Azulillo)', 3448, 'OAX', 142, 'MX', 15.82194000, -96.31833000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20262569'),
(69564, 'Cuaracurío', 3474, 'MIC', 142, 'MX', 20.06102000, -101.14774000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q20262569'),
(69565, 'Cuarta Brigada', 3469, 'GUA', 142, 'MX', 20.62194000, -101.28001000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20205980'),
(69566, 'Cuartel la Mesa (El Asoleadero)', 3474, 'MIC', 142, 'MX', 19.56972000, -100.28694000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20136992'),
(69567, 'Cuatolol', 3470, 'HID', 142, 'MX', 21.11967000, -98.82563000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20136992'),
(69568, 'Cuatro Caminos', 3474, 'MIC', 142, 'MX', 18.98826000, -102.10360000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20136992'),
(69569, 'Cuatro Caminos', 3476, 'PUE', 142, 'MX', 18.49130000, -97.12901000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20229497'),
(69570, 'Cuatro Ciénegas de Carranza', 3471, 'COA', 142, 'MX', 26.98542000, -102.06386000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q992866'),
(69571, 'Cuatzoquitengo', 3459, 'GRO', 142, 'MX', 17.28822000, -98.61523000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20206264'),
(69572, 'Cuauchichinola', 3465, 'MOR', 142, 'MX', 18.66389000, -99.37583000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20235806'),
(69573, 'Cuauhtamazaco', 3476, 'PUE', 142, 'MX', 20.02500000, -97.48722000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20229502'),
(69574, 'Cuauhtamingo', 3464, 'VER', 142, 'MX', 19.76472000, -97.31417000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137392'),
(69575, 'Cuauhtemoc', 3453, 'DUR', 142, 'MX', 24.29037000, -103.80913000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137392'),
(69576, 'Cuauhtempan (San Andrés Cuauhtempan)', 3465, 'MOR', 142, 'MX', 18.97472000, -98.94639000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q20235809'),
(69577, 'Cuauhtenango', 3459, 'GRO', 142, 'MX', 17.54625000, -99.20683000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20235809'),
(69578, 'Cuauhtémoc', 3463, 'TAM', 142, 'MX', 22.54408000, -98.15074000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q20224559'),
(69579, 'Cuauhtémoc', 3448, 'OAX', 142, 'MX', 17.10149000, -94.88532000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q20224559'),
(69580, 'Cuauhtémoc', 3451, 'CHP', 142, 'MX', 16.55000000, -93.71611000, '2019-10-05 23:08:36', '2020-05-01 17:22:59', 1, 'Q20276784'),
(69581, 'Cuauhtémoc', 3473, 'CMX', 142, 'MX', 19.44506000, -99.14612000, '2019-10-05 23:08:36', '2024-01-24 12:10:50', 1, 'Q20236988'),
(69582, 'Cuauhtémoc', 3472, 'COL', 142, 'MX', 19.32861000, -103.60284000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q5191888'),
(69583, 'Cuauhtémoc', 3447, 'CHH', 142, 'MX', 28.40884000, -106.86319000, '2019-10-05 23:08:36', '2020-05-01 17:22:59', 1, 'Q147955'),
(69584, 'Cuauhtémoc', 3476, 'PUE', 142, 'MX', 19.22306000, -97.33833000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q20261763'),
(69585, 'Cuauhtémoc (Campo Cinco)', 3468, 'SON', 142, 'MX', 27.43333000, -110.01667000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q20137446'),
(69586, 'Cuautempan', 3476, 'PUE', 142, 'MX', 19.91612000, -97.79097000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5191896'),
(69587, 'Cuautepec', 3459, 'GRO', 142, 'MX', 16.70520000, -98.98471000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5191898'),
(69588, 'Cuautepec de Hinojosa', 3470, 'HID', 142, 'MX', 20.03557000, -98.31015000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20241694'),
(69589, 'Cuautinchán', 3476, 'PUE', 142, 'MX', 18.95463000, -98.01566000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q5191852'),
(69590, 'Cuautitlán', 3450, 'MEX', 142, 'MX', 19.67052000, -99.17989000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q27769681'),
(69591, 'Cuautla', 3465, 'MOR', 142, 'MX', 18.81060000, -98.93525000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q939959'),
(69592, 'Cuautlacingo', 3450, 'MEX', 142, 'MX', 19.69405000, -98.78385000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q939959'),
(69593, 'Cuautlancingo', 3476, 'PUE', 142, 'MX', 19.08995000, -98.27319000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q1998658'),
(69594, 'Cuautlapán', 3464, 'VER', 142, 'MX', 18.87747000, -97.02291000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q20222295'),
(69595, 'Cuautotola', 3476, 'PUE', 142, 'MX', 20.06841000, -97.79806000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20222295'),
(69596, 'Cuautotolapan (San José)', 3476, 'PUE', 142, 'MX', 18.43299000, -97.12091000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q20229518'),
(69597, 'Cuaxomulco', 3458, 'TLA', 142, 'MX', 19.35264000, -98.09670000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5191900'),
(69598, 'Cuaxoxpan', 3476, 'PUE', 142, 'MX', 19.80806000, -97.34583000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137645'),
(69599, 'Cuaxuxpa', 3476, 'PUE', 142, 'MX', 18.46211000, -97.03556000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20229523'),
(69600, 'Cuayuca de Andrade', 3476, 'PUE', 142, 'MX', 18.44416000, -98.19193000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5138674'),
(69601, 'Cuazahuatl', 3470, 'HID', 142, 'MX', 21.01637000, -98.88691000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5138674'),
(69602, 'Cubiri de Portelas', 3449, 'SIN', 142, 'MX', 25.78677000, -108.25966000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5138674'),
(69603, 'Cuch Holoch', 3466, 'YUC', 142, 'MX', 20.43450000, -90.09694000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5138674'),
(69604, 'Cucharas', 3464, 'VER', 142, 'MX', 21.61568000, -97.66099000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5138674'),
(69605, 'Cuchicuato', 3469, 'GUA', 142, 'MX', 20.66092000, -101.46203000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20205992'),
(69606, 'Cuchulumtic', 3451, 'CHP', 142, 'MX', 16.77722000, -92.70417000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137783'),
(69607, 'Cucuchucho', 3474, 'MIC', 142, 'MX', 19.58275000, -101.63111000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137783'),
(69608, 'Cucurpe', 3468, 'SON', 142, 'MX', 30.33049000, -110.70609000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3844262'),
(69609, 'Cucuyulapa Primera Sección', 3454, 'TAB', 142, 'MX', 17.99082000, -93.25619000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q3844262'),
(69610, 'Cuecuecuatitla', 3450, 'MEX', 142, 'MX', 19.01349000, -98.84351000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3844262'),
(69611, 'Cuencamé', 3453, 'DUR', 142, 'MX', 24.64123000, -103.73772000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q5192589'),
(69612, 'Cuencamé de Ceniceros', 3453, 'DUR', 142, 'MX', 24.87116000, -103.69731000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q933194'),
(69613, 'Cuendo', 3450, 'MEX', 142, 'MX', 19.80028000, -99.94778000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20276801'),
(69614, 'Cuentepec', 3465, 'MOR', 142, 'MX', 18.86159000, -99.32611000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20276801'),
(69615, 'Cuernavaca', 3465, 'MOR', 142, 'MX', 18.95532000, -99.24002000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5793859'),
(69616, 'Cuerámaro', 3469, 'GUA', 142, 'MX', 20.62546000, -101.67242000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q2651369'),
(69617, 'Cuesta Amarilla', 3464, 'VER', 142, 'MX', 18.34667000, -95.23389000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137897'),
(69618, 'Cuesta Blanca', 3476, 'PUE', 142, 'MX', 18.84536000, -97.46753000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137897'),
(69619, 'Cuesta de Laja', 3464, 'VER', 142, 'MX', 18.29333000, -95.22111000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20137970'),
(69620, 'Cuesta de Peñones', 3469, 'GUA', 142, 'MX', 21.08400000, -100.23671000, '2019-10-05 23:08:36', '2020-05-01 17:23:00', 1, 'Q20137970'),
(69621, 'Cuetzala del Progreso', 3459, 'GRO', 142, 'MX', 18.13472000, -99.83190000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q4250391'),
(69622, 'Cuetzalan', 3476, 'PUE', 142, 'MX', 20.01766000, -97.52277000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q2651808'),
(69623, 'Cuetzalan del Progreso', 3476, 'PUE', 142, 'MX', 20.03857000, -97.49268000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5192626'),
(69624, 'Cuexpala', 3476, 'PUE', 142, 'MX', 18.62720000, -98.55842000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5192626'),
(69625, 'Cuicatlan', 3448, 'OAX', 142, 'MX', 17.80272000, -96.95939000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q10812437'),
(69626, 'Cuichapa', 3464, 'VER', 142, 'MX', 17.93889000, -94.28000000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20138157'),
(69627, 'Cuilapan de Guerrero', 3448, 'OAX', 142, 'MX', 16.97756000, -96.78081000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3625184'),
(69628, 'Cuitláhuac', 3464, 'VER', 142, 'MX', 18.81429000, -96.72281000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q20222319'),
(69629, 'Cuitzeo del Porvenir', 3474, 'MIC', 142, 'MX', 19.97025000, -101.14290000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5899035'),
(69630, 'Cuiyachapa', 3464, 'VER', 142, 'MX', 19.04569000, -97.17436000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20265505'),
(69631, 'Culiacancito', 3449, 'SIN', 142, 'MX', 24.82538000, -107.53445000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5192929'),
(69632, 'Culiacán', 3449, 'SIN', 142, 'MX', 24.79032000, -107.38782000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q211760'),
(69633, 'Cumbre de Barranca Honda', 3459, 'GRO', 142, 'MX', 16.73167000, -98.33667000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20138337'),
(69634, 'Cumbres III', 3456, 'AGU', 142, 'MX', 21.92222000, -102.23750000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20138401'),
(69635, 'Cumpas', 3468, 'SON', 142, 'MX', 29.99587000, -109.78087000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3844664'),
(69636, 'Cumpich', 3475, 'CAM', 142, 'MX', 20.18612000, -89.97081000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q3844664'),
(69637, 'Cumuapa 1ra. Sección', 3454, 'TAB', 142, 'MX', 17.98519000, -93.13806000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q20264405'),
(69638, 'Cumuato', 3474, 'MIC', 142, 'MX', 20.25724000, -102.59060000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20264405'),
(69639, 'Cuncunul', 3466, 'YUC', 142, 'MX', 20.62333000, -88.34637000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q2577445'),
(69640, 'Cunduacán', 3454, 'TAB', 142, 'MX', 18.06557000, -93.17302000, '2019-10-05 23:08:36', '2020-05-01 17:23:02', 1, 'Q4247181'),
(69641, 'Cuonetzingo', 3459, 'GRO', 142, 'MX', 17.52722000, -99.26583000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20206284'),
(69642, 'Cupareo', 3469, 'GUA', 142, 'MX', 20.22876000, -101.01806000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5194433'),
(69643, 'Cuparátaro', 3474, 'MIC', 142, 'MX', 19.85071000, -101.08903000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q5194433'),
(69644, 'Cupilco', 3454, 'TAB', 142, 'MX', 18.23947000, -93.12767000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q5194433'),
(69645, 'Cupuán del Río', 3474, 'MIC', 142, 'MX', 18.77806000, -102.18778000, '2019-10-05 23:08:36', '2020-05-01 17:23:01', 1, 'Q20138436'),
(69646, 'Curimeo', 3474, 'MIC', 142, 'MX', 20.02043000, -101.69534000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20138436'),
(69647, 'Curungueo', 3474, 'MIC', 142, 'MX', 19.46459000, -100.34340000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q20138436'),
(69648, 'Cusihuiriachi', 3447, 'CHH', 142, 'MX', 28.23980000, -106.83477000, '2019-10-05 23:08:36', '2019-10-05 23:08:36', 1, 'Q2652340'),
(69649, 'Cuto de la Esperanza', 3474, 'MIC', 142, 'MX', 19.73083000, -101.34028000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20236675'),
(69650, 'Cuto del Porvenir', 3474, 'MIC', 142, 'MX', 19.86976000, -101.14339000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20236675'),
(69651, 'Cutzamala de Pinzón', 3459, 'GRO', 142, 'MX', 18.46783000, -100.58089000, '2019-10-05 23:08:37', '2020-05-01 17:23:00', 1, 'Q20138554'),
(69652, 'Cuyoaco', 3476, 'PUE', 142, 'MX', 19.60192000, -97.62024000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q608391'),
(69653, 'Cuyutlán', 3472, 'COL', 142, 'MX', 18.91937000, -104.06873000, '2019-10-05 23:08:37', '2020-05-01 17:23:00', 1, 'Q608391'),
(69654, 'Cuyuxtlahuaca', 3459, 'GRO', 142, 'MX', 17.48213000, -98.34456000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q608391'),
(69655, 'Cuzama', 3466, 'YUC', 142, 'MX', 20.74202000, -89.31732000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5408098'),
(69657, 'Cálido', 3451, 'CHP', 142, 'MX', 17.09889000, -92.78750000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20147641'),
(69658, 'Cárdenas', 3461, 'SLP', 142, 'MX', 22.00144000, -99.64247000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q20275246'),
(69659, 'Cárdenas', 3454, 'TAB', 142, 'MX', 18.00135000, -93.37559000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q1962471'),
(69660, 'Cárdenas', 3469, 'GUA', 142, 'MX', 20.63012000, -101.22040000, '2019-10-05 23:08:37', '2020-05-01 17:23:00', 1, 'Q1962471'),
(69661, 'Córdoba', 3464, 'VER', 142, 'MX', 18.88420000, -96.92559000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q27769955'),
(69662, 'Córdoba (Santa Leticia)', 3464, 'VER', 142, 'MX', 18.90861000, -96.97889000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q20265333'),
(69663, 'Cúlico 2da. Sección', 3454, 'TAB', 142, 'MX', 18.14116000, -93.13222000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q20138328'),
(69664, 'Dajiedi', 3470, 'HID', 142, 'MX', 20.31114000, -98.94006000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q30303840'),
(69665, 'Damasco', 3451, 'CHP', 142, 'MX', 17.16161000, -91.60058000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q30303840'),
(69666, 'Damian Carmona', 3461, 'SLP', 142, 'MX', 22.09723000, -99.29216000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q30303840'),
(69667, 'Dantzibojay', 3470, 'HID', 142, 'MX', 20.43934000, -99.58587000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q30303840'),
(69668, 'Dautillos', 3449, 'SIN', 142, 'MX', 24.72167000, -107.97528000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5228110'),
(69669, 'Daxtha', 3470, 'HID', 142, 'MX', 20.27994000, -98.97964000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q30303706'),
(69670, 'De Parral', 3469, 'GUA', 142, 'MX', 20.45611000, -101.00778000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20138703'),
(69671, 'Dehesa', 3464, 'VER', 142, 'MX', 17.94167000, -94.99806000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20222342'),
(69672, 'Delegación Cuajimalpa de Morelos', 3473, 'CMX', 142, 'MX', 19.36920000, -99.29089000, '2019-10-05 23:08:37', '2024-01-24 12:10:50', 1, 'Q20136808'),
(69673, 'Delfino Victoria (Santa Fe)', 3464, 'VER', 142, 'MX', 19.20750000, -96.27333000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20269102'),
(69674, 'Delgado de Abajo', 3469, 'GUA', 142, 'MX', 20.71749000, -100.89338000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20269102'),
(69675, 'Delgado de Arriba', 3469, 'GUA', 142, 'MX', 20.72222000, -100.90080000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20269102'),
(69676, 'Delicias', 3447, 'CHH', 142, 'MX', 28.16948000, -105.44913000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q2652248'),
(69677, 'Delta', 3457, 'BCN', 142, 'MX', 32.35497000, -115.19617000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20138783'),
(69678, 'Dengantzha', 3470, 'HID', 142, 'MX', 20.27211000, -99.12098000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q49911779'),
(69679, 'Denjhi', 3450, 'MEX', 142, 'MX', 19.92116000, -99.53561000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q49911779'),
(69680, 'Derramadero Segundo (Infiernillo)', 3469, 'GUA', 142, 'MX', 21.09750000, -100.51417000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20276863'),
(69681, 'Derramaderos', 3461, 'SLP', 142, 'MX', 22.57025000, -100.93226000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20276863'),
(69682, 'Detiña (San Antonio Detiña)', 3450, 'MEX', 142, 'MX', 19.97028000, -99.88389000, '2019-10-05 23:08:37', '2020-05-01 17:23:00', 1, 'Q20237772'),
(69683, 'Diamante de Echeverría', 3451, 'CHP', 142, 'MX', 16.11181000, -92.87931000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20237772'),
(69684, 'Dieciocho de Marzo', 3474, 'MIC', 142, 'MX', 19.23138000, -102.70570000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20237772'),
(69685, 'Dieciséis de Septiembre', 3451, 'CHP', 142, 'MX', 16.29715000, -93.17490000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20237772'),
(69686, 'Diez de Octubre', 3453, 'DUR', 142, 'MX', 24.72849000, -104.63626000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20237772'),
(69687, 'Dios Padre', 3470, 'HID', 142, 'MX', 20.46469000, -99.20132000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20237772'),
(69688, 'Dios Padre', 3450, 'MEX', 142, 'MX', 19.61474000, -100.06499000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20237772'),
(69689, 'División del Norte', 3475, 'CAM', 142, 'MX', 18.52946000, -90.76416000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20237772'),
(69690, 'División del Norte', 3474, 'MIC', 142, 'MX', 19.08193000, -102.61218000, '2019-10-05 23:08:37', '2020-05-01 17:23:01', 1, 'Q20237772'),
(69691, 'Doctor Alberto Oviedo Mota', 3457, 'BCN', 142, 'MX', 32.22756000, -115.16792000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5399798'),
(69692, 'Doctor Arroyo', 3452, 'NLE', 142, 'MX', 23.67211000, -100.18134000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20233592'),
(69693, 'Doctor Belisario Domínguez (La Barra)', 3451, 'CHP', 142, 'MX', 15.88947000, -93.70404000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20276910'),
(69694, 'Doctor Coss', 3452, 'NLE', 142, 'MX', 25.92492000, -99.18316000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q3311232'),
(69695, 'Doctor Domingo Chanona', 3451, 'CHP', 142, 'MX', 16.34611000, -93.41667000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20211927'),
(69696, 'Doctor González', 3452, 'NLE', 142, 'MX', 25.85983000, -99.94409000, '2019-10-05 23:08:37', '2020-05-01 17:23:01', 1, 'Q3849497'),
(69697, 'Doctor Miguel Silva (San Guillermo)', 3474, 'MIC', 142, 'MX', 19.96583000, -101.17417000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139320'),
(69698, 'Doctor Montes de Oca', 3464, 'VER', 142, 'MX', 20.93477000, -97.55585000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139320'),
(69699, 'Doctor Mora', 3469, 'GUA', 142, 'MX', 21.14246000, -100.31966000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q2448224'),
(69700, 'Doctor Rodulfo Figueroa (Tierra Blanca)', 3451, 'CHP', 142, 'MX', 15.85083000, -92.06222000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139339'),
(69701, 'Doctor Samuel León Brindis', 3451, 'CHP', 142, 'MX', 17.43528000, -91.93194000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20139347'),
(69702, 'Dolores', 3450, 'MEX', 142, 'MX', 20.06059000, -100.32211000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139347'),
(69703, 'Dolores Cuadrilla de Enmedio', 3455, 'QUE', 142, 'MX', 20.35257000, -100.07967000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139347'),
(69704, 'Dolores Hidalgo', 3450, 'MEX', 142, 'MX', 19.65528000, -99.92639000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20237779'),
(69705, 'Dolores Hidalgo', 3476, 'PUE', 142, 'MX', 18.75697000, -97.86062000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139391'),
(69706, 'Dolores Hidalgo Cuna de la Independencia Nacional', 3469, 'GUA', 142, 'MX', 21.15611000, -100.93250000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q17487868'),
(69707, 'Dolores Jaltenango', 3451, 'CHP', 142, 'MX', 15.89694000, -92.77777000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q17487868'),
(69708, 'Domingo Arenas', 3476, 'PUE', 142, 'MX', 19.13998000, -98.45678000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5290343'),
(69709, 'Don Antonio', 3470, 'HID', 142, 'MX', 19.87444000, -98.93472000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139549'),
(69710, 'Don Diego', 3469, 'GUA', 142, 'MX', 20.69791000, -100.90209000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139549'),
(69711, 'Don Francisco', 3469, 'GUA', 142, 'MX', 20.90281000, -100.91873000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20139549'),
(69712, 'Don Samuel', 3475, 'CAM', 142, 'MX', 18.36000000, -90.85722000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69713, 'Donaciano Ojeda', 3474, 'MIC', 142, 'MX', 19.49093000, -100.27952000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69714, 'Donaji', 3448, 'OAX', 142, 'MX', 17.22950000, -95.05295000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69715, 'Dongu', 3450, 'MEX', 142, 'MX', 19.84295000, -99.58651000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69716, 'Dos Arroyos', 3459, 'GRO', 142, 'MX', 17.02060000, -99.64966000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69717, 'Dos Bocas', 3464, 'VER', 142, 'MX', 18.41087000, -95.70732000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69718, 'Dos Ceibas', 3454, 'TAB', 142, 'MX', 18.04112000, -93.03561000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69719, 'Dos Montes', 3454, 'TAB', 142, 'MX', 17.98774000, -92.82832000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258219'),
(69720, 'Dos Ríos', 3464, 'VER', 142, 'MX', 19.48417000, -96.79944000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q20140402'),
(69721, 'Dos Ríos', 3465, 'MOR', 142, 'MX', 19.37467000, -99.34359000, '2019-10-05 23:08:37', '2020-05-01 17:23:01', 1, 'Q20140402'),
(69722, 'Dos de Abril', 3449, 'SIN', 142, 'MX', 25.91116000, -108.94077000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140402'),
(69723, 'Dotegiare', 3450, 'MEX', 142, 'MX', 19.70705000, -100.04354000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140402'),
(69724, 'Doxey', 3470, 'HID', 142, 'MX', 20.09169000, -99.24362000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5802194'),
(69725, 'Doxhicho', 3450, 'MEX', 142, 'MX', 19.89999000, -99.55727000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140458'),
(69726, 'Doxteje Barrio Primero', 3450, 'MEX', 142, 'MX', 19.95972000, -99.95861000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20276984'),
(69727, 'Doxteje Centro', 3450, 'MEX', 142, 'MX', 19.97528000, -99.95472000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20276985'),
(69729, 'Dr. Belisario Domínguez', 3447, 'CHH', 142, 'MX', 27.98462000, -106.45234000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q2606300'),
(69730, 'Duarte', 3469, 'GUA', 142, 'MX', 21.08712000, -101.52431000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20206023'),
(69731, 'Dulce Grande', 3461, 'SLP', 142, 'MX', 23.00175000, -102.17405000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20276987'),
(69732, 'Durango', 3457, 'BCN', 142, 'MX', 32.24865000, -115.25227000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20276987'),
(69733, 'Dzan', 3466, 'YUC', 142, 'MX', 20.38888000, -89.46855000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5815421'),
(69734, 'Dzemul', 3466, 'YUC', 142, 'MX', 21.21036000, -89.30956000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q921976'),
(69735, 'Dzibalchén', 3475, 'CAM', 142, 'MX', 19.45847000, -89.73111000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20258224'),
(69736, 'Dzibikak', 3466, 'YUC', 142, 'MX', 20.90046000, -89.79532000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20258224'),
(69737, 'Dzidzantun', 3466, 'YUC', 142, 'MX', 21.24898000, -89.04205000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5319753'),
(69739, 'Dzilam González', 3466, 'YUC', 142, 'MX', 21.28098000, -88.92957000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q5815455'),
(69740, 'Dzilam de Bravo', 3466, 'YUC', 142, 'MX', 21.43361000, -88.66838000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q2577531'),
(69741, 'Dzitbalché', 3475, 'CAM', 142, 'MX', 20.31857000, -90.05611000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q4505924'),
(69742, 'Dzitnup', 3466, 'YUC', 142, 'MX', 20.64725000, -88.24453000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5815462'),
(69743, 'Dzityá', 3466, 'YUC', 142, 'MX', 21.05186000, -89.67851000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q5815462'),
(69744, 'Dzitás', 3466, 'YUC', 142, 'MX', 20.84089000, -88.52908000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q5815469'),
(69745, 'Dziuche', 3467, 'ROO', 142, 'MX', 19.89744000, -88.80949000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q5815469'),
(69746, 'Dzoncauich', 3466, 'YUC', 142, 'MX', 21.09826000, -88.85402000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q2576165'),
(69747, 'Dzonot Carretero', 3466, 'YUC', 142, 'MX', 21.40084000, -87.87860000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q2576165'),
(69748, 'Dzula', 3467, 'ROO', 142, 'MX', 19.60328000, -88.41566000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q2576165'),
(69749, 'Dzununcan', 3466, 'YUC', 142, 'MX', 20.86469000, -89.65380000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q2576165'),
(69751, 'Déxtho de Victoria', 3470, 'HID', 142, 'MX', 20.32528000, -99.02389000, '2019-10-05 23:08:37', '2020-05-01 17:23:00', 1, 'Q20138937'),
(69752, 'Ecatepec de Morelos', 3450, 'MEX', 142, 'MX', 19.60378000, -99.05514000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q15121208'),
(69753, 'Ecatzingo de Hidalgo', 3450, 'MEX', 142, 'MX', 18.95508000, -98.75198000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140670'),
(69754, 'Ecuandureo', 3474, 'MIC', 142, 'MX', 20.16188000, -102.19340000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140685'),
(69755, 'Efraín A. Gutiérrez', 3451, 'CHP', 142, 'MX', 16.34442000, -92.19927000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20140685'),
(69756, 'Egipto', 3451, 'CHP', 142, 'MX', 17.26207000, -91.97150000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140685'),
(69757, 'Ejidal', 3464, 'VER', 142, 'MX', 18.90380000, -96.22583000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140685'),
(69758, 'Ejido Benito Juárez', 3447, 'CHH', 142, 'MX', 30.14937000, -106.88362000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20248604'),
(69759, 'Ejido Cajón Ojo de Agua Número Dos', 3449, 'SIN', 142, 'MX', 22.90000000, -105.97139000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q20140873'),
(69760, 'Ejido Cinco de Mayo', 3449, 'SIN', 142, 'MX', 25.85055000, -108.95932000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140873'),
(69761, 'Ejido Cohuibampo', 3449, 'SIN', 142, 'MX', 25.94094000, -109.15880000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20140873'),
(69762, 'Ejido Doctor Alberto Oviedo Mota (El Indiviso)', 3457, 'BCN', 142, 'MX', 32.02444000, -114.98361000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20247341'),
(69763, 'Ejido El Largo', 3447, 'CHH', 142, 'MX', 29.68333000, -108.26667000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20212462'),
(69764, 'Ejido El Vergel', 3447, 'CHH', 142, 'MX', 26.47133000, -106.38320000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20212462'),
(69765, 'Ejido General Leandro Valle', 3457, 'BCN', 142, 'MX', 30.62167000, -115.96583000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20141284'),
(69766, 'Ejido Gogorrón (Ex-Hacienda de Gogorrón)', 3461, 'SLP', 142, 'MX', 21.84019000, -100.91138000, '2019-10-05 23:08:37', '2020-05-01 17:23:02', 1, 'Q20277043'),
(69767, 'Ejido Hidalgo', 3462, 'ZAC', 142, 'MX', 22.30083000, -102.03861000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20264352'),
(69768, 'Ejido Javier Rojo Gómez', 3457, 'BCN', 142, 'MX', 32.49389000, -116.82222000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20218090'),
(69769, 'Ejido Jesús María', 3469, 'GUA', 142, 'MX', 21.14194000, -100.91167000, '2019-10-05 23:08:37', '2020-05-01 17:23:00', 1, 'Q20141362'),
(69770, 'Ejido Jiquilpan', 3457, 'BCN', 142, 'MX', 32.50131000, -115.06382000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20247346'),
(69771, 'Ejido La Quemada', 3447, 'CHH', 142, 'MX', 28.84761000, -107.02152000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20247346'),
(69772, 'Ejido Lagunitas', 3468, 'SON', 142, 'MX', 32.31956000, -114.89568000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q56475540'),
(69773, 'Ejido Loma de Malacota', 3450, 'MEX', 142, 'MX', 19.65753000, -99.67122000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q56475540'),
(69774, 'Ejido Lázaro Cárdenas', 3457, 'BCN', 142, 'MX', 32.60794000, -115.02076000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q56475540'),
(69775, 'Ejido Mayocoba', 3449, 'SIN', 142, 'MX', 25.93824000, -109.22130000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q56475540'),
(69776, 'Ejido Michoacán de Ocampo', 3457, 'BCN', 142, 'MX', 32.46512000, -115.31003000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q56475540'),
(69777, 'Ejido Miraflores', 3450, 'MEX', 142, 'MX', 19.57000000, -99.39750000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20141603'),
(69778, 'Ejido Netzahualcóyotl', 3457, 'BCN', 142, 'MX', 32.62444000, -115.06222000, '2019-10-05 23:08:37', '2020-05-01 17:22:59', 1, 'Q20218101'),
(69779, 'Ejido Nuevo', 3459, 'GRO', 142, 'MX', 16.97405000, -99.73702000, '2019-10-05 23:08:37', '2019-10-05 23:08:37', 1, 'Q20218101'),
(69780, 'Ejido Nuevo León', 3457, 'BCN', 142, 'MX', 32.41103000, -115.18827000, '2019-10-05 23:08:38', '2020-05-01 17:22:59', 1, 'Q20218101'),
(69781, 'Ejido Ohuira', 3449, 'SIN', 142, 'MX', 25.71663000, -108.97850000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20218101'),
(69782, 'Ejido Ojo de Agua', 3457, 'BCN', 142, 'MX', 32.47556000, -116.78917000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20218105'),
(69783, 'Ejido Palma (Ejido San Francisco)', 3450, 'MEX', 142, 'MX', 19.56889000, -99.40889000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20141664'),
(69784, 'Ejido Plan de Ayala', 3457, 'BCN', 142, 'MX', 32.22023000, -115.03158000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20247353'),
(69785, 'Ejido Pátzcuaro', 3457, 'BCN', 142, 'MX', 32.35139000, -115.06583000, '2019-10-05 23:08:38', '2020-05-01 17:22:59', 1, 'Q20218106'),
(69786, 'Ejido Quintana Roo', 3457, 'BCN', 142, 'MX', 32.46844000, -115.08250000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20218106'),
(69787, 'Ejido Saltillo', 3457, 'BCN', 142, 'MX', 32.42430000, -115.12480000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277089'),
(69788, 'Ejido San Cristóbal', 3450, 'MEX', 142, 'MX', 19.54250000, -98.89417000, '2019-10-05 23:08:38', '2020-05-01 17:23:00', 1, 'Q20141758'),
(69789, 'Ejido San Diego', 3450, 'MEX', 142, 'MX', 19.41329000, -99.82537000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20141758'),
(69790, 'Ejido San José Xilatzén', 3461, 'SLP', 142, 'MX', 21.67472000, -98.92750000, '2019-10-05 23:08:38', '2020-05-01 17:23:02', 1, 'Q20141788'),
(69791, 'Ejido San Lorenzo Cuauhtenco', 3450, 'MEX', 142, 'MX', 19.30139000, -99.75028000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277095'),
(69792, 'Ejido Sinaloa', 3457, 'BCN', 142, 'MX', 32.54734000, -115.27041000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277095'),
(69793, 'Ejido Sonora', 3457, 'BCN', 142, 'MX', 32.28749000, -115.23134000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277095'),
(69794, 'Ejido Tabasco', 3457, 'BCN', 142, 'MX', 32.56187000, -114.92659000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1321929'),
(69795, 'Ejido Toluca', 3457, 'BCN', 142, 'MX', 32.43050000, -115.08316000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1321929'),
(69796, 'Ejido Tosalibampo', 3449, 'SIN', 142, 'MX', 25.98100000, -109.11270000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1321929'),
(69797, 'Ejido Venustiano Carranza', 3471, 'COA', 142, 'MX', 25.35091000, -102.95698000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277109'),
(69798, 'Ejido Vicente Guerrero', 3457, 'BCN', 142, 'MX', 32.39593000, -115.14026000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277109'),
(69799, 'Ejido Viejo', 3459, 'GRO', 142, 'MX', 16.94812000, -99.94606000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277109'),
(69800, 'Ejido Yucatán', 3457, 'BCN', 142, 'MX', 32.60435000, -115.09380000, '2019-10-05 23:08:38', '2020-05-01 17:22:59', 1, 'Q20247359'),
(69801, 'Ejido Zaragoza', 3462, 'ZAC', 142, 'MX', 23.94786000, -103.66322000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20247359'),
(69802, 'Ejido de Coscomate del Progreso', 3450, 'MEX', 142, 'MX', 19.93379000, -99.54720000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20247359'),
(69803, 'Ejido de Dolores', 3450, 'MEX', 142, 'MX', 19.43083000, -99.61278000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20140983'),
(69804, 'Ejido de Guadalupe', 3450, 'MEX', 142, 'MX', 19.63765000, -99.27178000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20140983'),
(69805, 'Ejido de Mozoquilpan', 3450, 'MEX', 142, 'MX', 19.42417000, -99.52833000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20237803'),
(69806, 'Ejido de San Mateo Coapexco', 3450, 'MEX', 142, 'MX', 18.94889000, -99.64249000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20237803'),
(69807, 'Ejido de la Finca', 3450, 'MEX', 142, 'MX', 18.88635000, -99.62693000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20237803'),
(69808, 'Ejido del Tejocote', 3450, 'MEX', 142, 'MX', 19.48474000, -99.27847000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277029'),
(69809, 'Ejido del Tunal Nenaxi', 3450, 'MEX', 142, 'MX', 19.74389000, -99.90889000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20237831'),
(69810, 'Ejido el Castillo', 3450, 'MEX', 142, 'MX', 19.43583000, -99.30583000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277031'),
(69811, 'Ejido el Rosario', 3474, 'MIC', 142, 'MX', 19.57185000, -100.27673000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277031'),
(69812, 'Ejido el Saucillo', 3461, 'SLP', 142, 'MX', 21.83666000, -100.99490000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277031'),
(69813, 'Ejido la Guayana (Rancho Seco)', 3456, 'AGU', 142, 'MX', 21.98028000, -102.27306000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20141408'),
(69814, 'Ejido la Joya', 3469, 'GUA', 142, 'MX', 21.14325000, -101.73922000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20141412'),
(69815, 'Ejido la Pitahaya (Santo Domingo)', 3461, 'SLP', 142, 'MX', 21.60611000, -100.76111000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277060'),
(69816, 'Ejido la Piña (Maromilla)', 3464, 'VER', 142, 'MX', 19.02861000, -96.78361000, '2019-10-05 23:08:38', '2020-05-01 17:23:02', 1, 'Q20141428'),
(69817, 'Ejido la Soledad', 3450, 'MEX', 142, 'MX', 19.64500000, -100.10639000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20141451'),
(69818, 'Ejido los Huastecos', 3461, 'SLP', 142, 'MX', 21.95917000, -98.65028000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277068'),
(69819, 'Ejutla de Crespo', 3448, 'OAX', 142, 'MX', 16.56623000, -96.73123000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69820, 'Ekmul', 3466, 'YUC', 142, 'MX', 20.96460000, -89.35004000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69821, 'Ekpedz', 3466, 'YUC', 142, 'MX', 20.31647000, -88.43064000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69822, 'El Abra', 3463, 'TAM', 142, 'MX', 22.62058000, -99.02142000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69823, 'El Acebuche', 3469, 'GUA', 142, 'MX', 20.22311000, -100.74196000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69824, 'El Aguacatal', 3475, 'CAM', 142, 'MX', 18.21361000, -91.51095000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69825, 'El Aguacate', 3464, 'VER', 142, 'MX', 18.19808000, -94.97053000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69826, 'El Aguajito', 3449, 'SIN', 142, 'MX', 25.95965000, -109.32970000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69827, 'El Ahuacate', 3477, 'NAY', 142, 'MX', 21.51535000, -104.94436000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69828, 'El Alvareño', 3474, 'MIC', 142, 'MX', 20.30078000, -102.43723000, '2019-10-05 23:08:38', '2020-05-01 17:23:01', 1, 'Q1144317'),
(69829, 'El Anono', 3464, 'VER', 142, 'MX', 21.25559000, -97.65648000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q1144317'),
(69830, 'El Arador', 3448, 'OAX', 142, 'MX', 16.54111000, -97.18194000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20262679'),
(69831, 'El Arco', 3450, 'MEX', 142, 'MX', 19.22528000, -100.13167000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20144273'),
(69832, 'El Arenal', 3451, 'CHP', 142, 'MX', 15.17273000, -92.69923000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20144273'),
(69833, 'El Arenal', 3470, 'HID', 142, 'MX', 20.22258000, -98.90957000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q2652273'),
(69834, 'El Arenal', 3464, 'VER', 142, 'MX', 20.26829000, -97.59250000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q2652273'),
(69835, 'El Arenal', 3453, 'DUR', 142, 'MX', 24.06527000, -104.43387000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q2652273'),
(69836, 'El Bajío', 3448, 'OAX', 142, 'MX', 16.88917000, -95.03528000, '2019-10-05 23:08:38', '2020-05-01 17:23:01', 1, 'Q20262710'),
(69837, 'El Bajío', 3456, 'AGU', 142, 'MX', 22.24558000, -102.30825000, '2019-10-05 23:08:38', '2020-05-01 17:22:59', 1, 'Q20262710'),
(69838, 'El Bajío', 3454, 'TAB', 142, 'MX', 17.98173000, -92.78229000, '2019-10-05 23:08:38', '2020-05-01 17:23:02', 1, 'Q20145161'),
(69839, 'El Barrancón del Tío Blas (El Barrancón)', 3463, 'TAM', 142, 'MX', 25.01556000, -97.71694000, '2019-10-05 23:08:38', '2020-05-01 17:23:02', 1, 'Q20145576'),
(69840, 'El Barrido', 3470, 'HID', 142, 'MX', 20.45028000, -99.19083000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20145660'),
(69841, 'El Barril', 3461, 'SLP', 142, 'MX', 23.07509000, -102.15374000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20145660'),
(69842, 'El Barrio de la Soledad', 3448, 'OAX', 142, 'MX', 16.80131000, -95.03909000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q949138'),
(69843, 'El Bejuco', 3459, 'GRO', 142, 'MX', 16.82488000, -99.70720000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q949138'),
(69844, 'El Bellote (Miguel de la Madrid)', 3454, 'TAB', 142, 'MX', 18.42528000, -93.15083000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20227781'),
(69845, 'El Bingú', 3470, 'HID', 142, 'MX', 20.59296000, -99.14315000, '2019-10-05 23:08:38', '2020-05-01 17:23:00', 1, 'Q56650617'),
(69846, 'El Blanco', 3464, 'VER', 142, 'MX', 18.07418000, -95.28053000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q56650617'),
(69847, 'El Blanco', 3455, 'QUE', 142, 'MX', 20.61119000, -100.10159000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q56650617'),
(69848, 'El Bosque', 3451, 'CHP', 142, 'MX', 17.06273000, -92.72147000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q27769743'),
(69849, 'El Botadero', 3477, 'NAY', 142, 'MX', 21.75404000, -105.28640000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q27769743'),
(69850, 'El Boxtha', 3470, 'HID', 142, 'MX', 20.25972000, -98.97417000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277446'),
(69851, 'El Burrión', 3449, 'SIN', 142, 'MX', 25.53878000, -108.41286000, '2019-10-05 23:08:38', '2020-05-01 17:23:02', 1, 'Q20277477'),
(69852, 'El CERESO', 3469, 'GUA', 142, 'MX', 21.02222000, -101.68917000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277795'),
(69853, 'El Cabi', 3450, 'MEX', 142, 'MX', 19.18986000, -98.85371000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20237954'),
(69854, 'El Cahulote de Santa Ana', 3474, 'MIC', 142, 'MX', 19.07611000, -101.58250000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20236837'),
(69855, 'El Calvario', 3451, 'CHP', 142, 'MX', 17.21750000, -92.55694000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20212202'),
(69856, 'El Camalote', 3448, 'OAX', 142, 'MX', 15.87250000, -96.62361000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20230502'),
(69857, 'El Camarón', 3448, 'OAX', 142, 'MX', 16.55745000, -96.02868000, '2019-10-05 23:08:38', '2020-05-01 17:23:01', 1, 'Q20230502'),
(69858, 'El Cambio', 3471, 'COA', 142, 'MX', 25.64048000, -103.32710000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20230502'),
(69859, 'El Campanario y Oradel', 3463, 'TAM', 142, 'MX', 27.47306000, -99.62000000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q4531373'),
(69860, 'El Canario', 3469, 'GUA', 142, 'MX', 20.15694000, -101.33556000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20147644'),
(69861, 'El Capomal', 3477, 'NAY', 142, 'MX', 21.83935000, -105.12116000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20233627'),
(69862, 'El Capulo (La Quebradora)', 3476, 'PUE', 142, 'MX', 18.94944000, -98.26889000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20261896'),
(69863, 'El Capulín', 3470, 'HID', 142, 'MX', 20.00898000, -98.27613000, '2019-10-05 23:08:38', '2020-05-01 17:23:00', 1, 'Q50012184'),
(69864, 'El Capulín', 3461, 'SLP', 142, 'MX', 21.82806000, -100.03743000, '2019-10-05 23:08:38', '2020-05-01 17:23:02', 1, 'Q50012184'),
(69865, 'El Capulín', 3469, 'GUA', 142, 'MX', 21.04043000, -100.32220000, '2019-10-05 23:08:38', '2020-05-01 17:23:00', 1, 'Q50012184'),
(69866, 'El Capulín', 3474, 'MIC', 142, 'MX', 20.22795000, -102.48991000, '2019-10-05 23:08:38', '2020-05-01 17:23:01', 1, 'Q50012184'),
(69867, 'El Capulín (La Nueva Pochota)', 3448, 'OAX', 142, 'MX', 18.22972000, -96.27806000, '2019-10-05 23:08:38', '2020-05-01 17:23:01', 1, 'Q20262807'),
(69868, 'El Caracol', 3469, 'GUA', 142, 'MX', 20.56718000, -100.98290000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20262807'),
(69869, 'El Carmen', 3459, 'GRO', 142, 'MX', 16.95036000, -98.23764000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20262807'),
(69870, 'El Carmen', 3451, 'CHP', 142, 'MX', 15.57549000, -93.12865000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20262807'),
(69871, 'El Carmen', 3465, 'MOR', 142, 'MX', 19.88796000, -98.95732000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20262807'),
(69872, 'El Carmen', 3469, 'GUA', 142, 'MX', 20.64009000, -101.37259000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20262807'),
(69873, 'El Carmen', 3455, 'QUE', 142, 'MX', 20.57445000, -100.28804000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20262807'),
(69874, 'El Carmen', 3476, 'PUE', 142, 'MX', 19.24167000, -97.20111000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20229653'),
(69875, 'El Carmen (El Desierto del Carmen)', 3450, 'MEX', 142, 'MX', 18.92028000, -99.55667000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20148998'),
(69876, 'El Carmen (El Limón)', 3451, 'CHP', 142, 'MX', 17.89056000, -93.16528000, '2019-10-05 23:08:38', '2020-05-01 17:22:59', 1, 'Q20148955'),
(69877, 'El Carmen Aztama', 3458, 'TLA', 142, 'MX', 19.22972000, -98.21944000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20148737'),
(69878, 'El Carmen Ocotepec', 3450, 'MEX', 142, 'MX', 19.68722000, -99.97417000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277649'),
(69879, 'El Carmen Xalpatlahuaya', 3458, 'TLA', 142, 'MX', 19.39193000, -97.98167000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277649'),
(69880, 'El Carretón', 3469, 'GUA', 142, 'MX', 21.63045000, -100.98064000, '2019-10-05 23:08:38', '2020-05-01 17:23:00', 1, 'Q20277649'),
(69881, 'El Carrizal', 3448, 'OAX', 142, 'MX', 16.06438000, -95.39308000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q27767960'),
(69882, 'El Carrizal', 3461, 'SLP', 142, 'MX', 22.34146000, -101.16778000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q27767960'),
(69883, 'El Carrizalito', 3469, 'GUA', 142, 'MX', 20.70371000, -101.32439000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q27767960'),
(69884, 'El Carrizo', 3448, 'OAX', 142, 'MX', 16.25453000, -98.03080000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q27767960'),
(69885, 'El Carrizo', 3449, 'SIN', 142, 'MX', 24.01416000, -106.85196000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277720'),
(69886, 'El Castillo', 3464, 'VER', 142, 'MX', 19.54661000, -96.86425000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20222798'),
(69887, 'El Castillo', 3469, 'GUA', 142, 'MX', 20.58072000, -100.49297000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20222798'),
(69888, 'El Castillo', 3449, 'SIN', 142, 'MX', 24.54353000, -107.70364000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20222798'),
(69889, 'El Cazadero', 3455, 'QUE', 142, 'MX', 20.30011000, -99.87186000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20246220'),
(69890, 'El Cazadero', 3462, 'ZAC', 142, 'MX', 23.67250000, -103.12220000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20246220'),
(69891, 'El Cedro', 3454, 'TAB', 142, 'MX', 18.02699000, -92.94760000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20246220'),
(69892, 'El Censo', 3451, 'CHP', 142, 'MX', 16.86287000, -91.55988000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20246220'),
(69893, 'El Centenario', 3460, 'BCS', 142, 'MX', 24.10250000, -110.41444000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q5350973'),
(69894, 'El Cerrillo Vista Hermosa', 3450, 'MEX', 142, 'MX', 19.32337000, -99.54250000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277797'),
(69895, 'El Cerrito', 3450, 'MEX', 142, 'MX', 19.35528000, -99.34833000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277814'),
(69896, 'El Ceñidor', 3474, 'MIC', 142, 'MX', 19.00276000, -102.19035000, '2019-10-05 23:08:38', '2020-05-01 17:23:01', 1, 'Q20277814'),
(69897, 'El Chacal', 3476, 'PUE', 142, 'MX', 20.22228000, -97.46025000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277814'),
(69898, 'El Chauz', 3474, 'MIC', 142, 'MX', 18.89195000, -102.03978000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20236963'),
(69899, 'El Chayote', 3456, 'AGU', 142, 'MX', 22.28667000, -102.23900000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277919'),
(69900, 'El Chico', 3464, 'VER', 142, 'MX', 19.46570000, -96.83642000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20277919'),
(69901, 'El Chinaco (El Pujido)', 3469, 'GUA', 142, 'MX', 20.52222000, -100.91972000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20206225'),
(69902, 'El Chocolate', 3448, 'OAX', 142, 'MX', 16.93191000, -95.08946000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20206225'),
(69903, 'El Chote', 3464, 'VER', 142, 'MX', 20.40167000, -97.34389000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20222925'),
(69904, 'El Cid', 3470, 'HID', 142, 'MX', 19.86722000, -98.92694000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20241854'),
(69905, 'El Ciervo', 3455, 'QUE', 142, 'MX', 20.61834000, -99.87242000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20241854'),
(69906, 'El Ciruelo', 3448, 'OAX', 142, 'MX', 16.31780000, -98.25707000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20241854'),
(69907, 'El Coacoyul', 3459, 'GRO', 142, 'MX', 17.63810000, -101.47571000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20206482'),
(69908, 'El Cocuite', 3464, 'VER', 142, 'MX', 18.71765000, -96.08442000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20206482'),
(69909, 'El Coecillo', 3469, 'GUA', 142, 'MX', 20.97651000, -101.45010000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20206482'),
(69910, 'El Colegio', 3474, 'MIC', 142, 'MX', 19.77333000, -101.17750000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20237047'),
(69911, 'El Colomo', 3472, 'COL', 142, 'MX', 19.06101000, -104.25853000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20237650'),
(69912, 'El Colorado', 3455, 'QUE', 142, 'MX', 20.56185000, -100.24520000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20246234'),
(69913, 'El Colorado', 3470, 'HID', 142, 'MX', 20.21528000, -99.00222000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20241861'),
(69914, 'El Colorado', 3450, 'MEX', 142, 'MX', 20.09639000, -99.76278000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20238018'),
(69915, 'El Coloso', 3450, 'MEX', 142, 'MX', 19.09778000, -99.59778000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278144'),
(69916, 'El Conchero', 3459, 'GRO', 142, 'MX', 16.94266000, -99.96233000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278144'),
(69917, 'El Conejo', 3464, 'VER', 142, 'MX', 19.53140000, -97.15383000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278144'),
(69918, 'El Congo', 3454, 'TAB', 142, 'MX', 17.82522000, -92.43887000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278144'),
(69919, 'El Consuelo', 3471, 'COA', 142, 'MX', 25.57131000, -103.27620000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278144'),
(69920, 'El Consuelo Ulapa', 3451, 'CHP', 142, 'MX', 15.38662000, -92.79228000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278144'),
(69921, 'El Contadero de Matamoros', 3450, 'MEX', 142, 'MX', 19.23734000, -99.80990000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278144'),
(69922, 'El Control', 3463, 'TAM', 142, 'MX', 25.95867000, -97.81281000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20224791'),
(69923, 'El Copal', 3451, 'CHP', 142, 'MX', 16.96729000, -92.92625000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20224791'),
(69924, 'El Copalar', 3451, 'CHP', 142, 'MX', 16.87694000, -93.21389000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278187'),
(69925, 'El Copalillo', 3469, 'GUA', 142, 'MX', 20.72745000, -101.34570000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20278187'),
(69926, 'El Copetillo', 3462, 'ZAC', 142, 'MX', 22.14333000, -102.00139000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20220716'),
(69927, 'El Corte', 3464, 'VER', 142, 'MX', 18.45226000, -95.73510000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20220716'),
(69928, 'El Corte', 3477, 'NAY', 142, 'MX', 21.72025000, -105.24488000, '2019-10-05 23:08:38', '2019-10-05 23:08:38', 1, 'Q20220716'),
(69929, 'El Cortijo', 3459, 'GRO', 142, 'MX', 16.98972000, -99.14889000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206514'),
(69930, 'El Cortés', 3459, 'GRO', 142, 'MX', 16.78063000, -99.50210000, '2019-10-05 23:08:39', '2020-05-01 17:23:00', 1, 'Q20206514'),
(69931, 'El Coto', 3455, 'QUE', 142, 'MX', 20.39491000, -100.13465000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206514'),
(69932, 'El Coyote', 3468, 'SON', 142, 'MX', 30.82877000, -112.61531000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20228147'),
(69933, 'El Coyul', 3448, 'OAX', 142, 'MX', 15.91472000, -95.81028000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20262981'),
(69934, 'El Cuenqueño', 3474, 'MIC', 142, 'MX', 20.32745000, -102.45760000, '2019-10-05 23:08:39', '2020-05-01 17:23:01', 1, 'Q20262981'),
(69935, 'El Cuervero (Cuerveros)', 3456, 'AGU', 142, 'MX', 21.86056000, -102.68389000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q8772991'),
(69936, 'El Cuije', 3471, 'COA', 142, 'MX', 25.69670000, -103.34107000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q8772991'),
(69937, 'El Cuitzillo Grande', 3474, 'MIC', 142, 'MX', 19.77972000, -101.11694000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20237204'),
(69938, 'El Curtidor', 3450, 'MEX', 142, 'MX', 19.29167000, -99.83500000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20278807'),
(69939, 'El Cuyo', 3466, 'YUC', 142, 'MX', 21.51568000, -87.67878000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20278807'),
(69940, 'El Césped', 3474, 'MIC', 142, 'MX', 19.88250000, -100.13972000, '2019-10-05 23:08:39', '2020-05-01 17:23:01', 1, 'Q20236945'),
(69941, 'El Cóporo', 3450, 'MEX', 142, 'MX', 19.28306000, -99.83528000, '2019-10-05 23:08:39', '2020-05-01 17:23:00', 1, 'Q20278190'),
(69942, 'El Diamante (La Retranca)', 3468, 'SON', 142, 'MX', 30.84556000, -112.63972000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20261571'),
(69943, 'El Diez', 3449, 'SIN', 142, 'MX', 24.72389000, -107.45194000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20258900'),
(69944, 'El Divisador', 3469, 'GUA', 142, 'MX', 20.57722000, -101.15806000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206330'),
(69945, 'El Dorado', 3449, 'SIN', 142, 'MX', 24.32283000, -107.36298000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q5351160'),
(69946, 'El Dorado', 3454, 'TAB', 142, 'MX', 17.88323000, -93.36788000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20227832'),
(69947, 'El Durazno', 3459, 'GRO', 142, 'MX', 17.52828000, -99.29057000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20227832'),
(69948, 'El Durazno', 3453, 'DUR', 142, 'MX', 25.47042000, -106.92742000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20227832'),
(69949, 'El Durazno', 3474, 'MIC', 142, 'MX', 19.65314000, -101.17139000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20227832'),
(69950, 'El Edén', 3451, 'CHP', 142, 'MX', 16.29191000, -91.63755000, '2019-10-05 23:08:39', '2020-05-01 17:22:59', 1, 'Q20227832'),
(69951, 'El Embarcadero', 3459, 'GRO', 142, 'MX', 16.96417000, -100.00167000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20278971'),
(69952, 'El Encanto', 3451, 'CHP', 142, 'MX', 14.73083000, -92.41028000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20278992'),
(69953, 'El Encanto del Cerril', 3476, 'PUE', 142, 'MX', 18.93194000, -98.42556000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20229740'),
(69954, 'El Esclavo', 3450, 'MEX', 142, 'MX', 19.67667000, -99.37139000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238057'),
(69955, 'El Escobillo', 3464, 'VER', 142, 'MX', 19.51450000, -97.19791000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238057'),
(69956, 'El Escondido', 3459, 'GRO', 142, 'MX', 18.22068000, -100.31208000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238057'),
(69957, 'El Escoplo', 3469, 'GUA', 142, 'MX', 20.91004000, -101.51127000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238057'),
(69958, 'El Escribano', 3454, 'TAB', 142, 'MX', 18.41028000, -93.22167000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20227835'),
(69959, 'El Espejo', 3469, 'GUA', 142, 'MX', 20.44094000, -100.55417000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20227835'),
(69960, 'El Espinal', 3451, 'CHP', 142, 'MX', 16.36862000, -94.10449000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20263100'),
(69961, 'El Espinal', 3464, 'VER', 142, 'MX', 19.62444000, -96.87250000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20223249'),
(69962, 'El Espinal', 3450, 'MEX', 142, 'MX', 19.46756000, -100.06051000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20223249'),
(69963, 'El Espinal', 3448, 'OAX', 142, 'MX', 16.46751000, -95.04198000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q4968496'),
(69964, 'El Espinalillo', 3459, 'GRO', 142, 'MX', 16.98472000, -100.12694000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206593'),
(69965, 'El Estudiante', 3465, 'MOR', 142, 'MX', 18.57760000, -99.29712000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206593'),
(69966, 'El Fortín', 3474, 'MIC', 142, 'MX', 20.18005000, -102.60051000, '2019-10-05 23:08:39', '2020-05-01 17:23:01', 1, 'Q20206593'),
(69967, 'El Fresno', 3459, 'GRO', 142, 'MX', 17.43143000, -99.58605000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206608'),
(69968, 'El Fresno', 3469, 'GUA', 142, 'MX', 20.27322000, -100.49154000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206608'),
(69969, 'El Fuerte', 3474, 'MIC', 142, 'MX', 20.37215000, -102.06709000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206608'),
(69970, 'El Fuerte', 3449, 'SIN', 142, 'MX', 26.41686000, -108.61828000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q1324181'),
(69971, 'El Fuerte', 3462, 'ZAC', 142, 'MX', 23.85414000, -103.12207000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q1324181'),
(69972, 'El Fuerte', 3461, 'SLP', 142, 'MX', 21.71289000, -100.66445000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q1324181'),
(69973, 'El Fuerte de la Unión', 3476, 'PUE', 142, 'MX', 19.43077000, -97.56064000, '2019-10-05 23:08:39', '2020-05-01 17:23:01', 1, 'Q1324181'),
(69974, 'El Galaneño', 3463, 'TAM', 142, 'MX', 25.76091000, -97.54507000, '2019-10-05 23:08:39', '2020-05-01 17:23:02', 1, 'Q1324181'),
(69975, 'El Gallinero', 3469, 'GUA', 142, 'MX', 21.18208000, -100.95996000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q1324181'),
(69976, 'El Gallo', 3455, 'QUE', 142, 'MX', 20.66973000, -100.04677000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q1324181'),
(69977, 'El Guaco', 3474, 'MIC', 142, 'MX', 19.06711000, -102.05760000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q1324181'),
(69978, 'El Guayabo', 3449, 'SIN', 142, 'MX', 25.94132000, -109.13880000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q1324181'),
(69979, 'El Habal', 3449, 'SIN', 142, 'MX', 23.35014000, -106.41834000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20279358'),
(69980, 'El Habillal', 3450, 'MEX', 142, 'MX', 18.00699000, -102.37263000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20279358'),
(69981, 'El Hatito', 3464, 'VER', 142, 'MX', 19.27750000, -96.39361000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20223364'),
(69982, 'El Hielo', 3450, 'MEX', 142, 'MX', 19.42583000, -99.35556000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20279378'),
(69983, 'El Higo', 3464, 'VER', 142, 'MX', 21.76708000, -98.45186000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q3891411'),
(69984, 'El Higueral', 3449, 'SIN', 142, 'MX', 24.31044000, -107.34800000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20279386'),
(69985, 'El Huajote', 3449, 'SIN', 142, 'MX', 23.12889000, -106.05889000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20259002'),
(69986, 'El Huarache (El Guarache)', 3453, 'DUR', 142, 'MX', 25.52000000, -103.48667000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20218588'),
(69987, 'El Huaxtho', 3470, 'HID', 142, 'MX', 20.23444000, -98.96222000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20241988'),
(69988, 'El Huexco', 3461, 'SLP', 142, 'MX', 21.34075000, -98.75350000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20241988'),
(69989, 'El Huidero', 3464, 'VER', 142, 'MX', 18.38750000, -95.17917000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20223402'),
(69990, 'El Huitusi', 3449, 'SIN', 142, 'MX', 25.50974000, -108.77875000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20279421'),
(69991, 'El Huixmí', 3470, 'HID', 142, 'MX', 20.07569000, -98.81972000, '2019-10-05 23:08:39', '2020-05-01 17:23:00', 1, 'Q5822143'),
(69992, 'El Huizache', 3469, 'GUA', 142, 'MX', 20.41934000, -100.95978000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q5822143'),
(69993, 'El Humo', 3464, 'VER', 142, 'MX', 21.17568000, -97.94169000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q5822143');

