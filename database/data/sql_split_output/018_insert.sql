INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(8665, 'B<PERSON>beek', 1373, 'VLG', 22, 'BE', 50.82876000, 4.75949000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770318'),
(8666, 'B<PERSON>zen', 1373, 'VLG', 22, 'BE', 50.87325000, 5.51840000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31501520'),
(8667, 'Binche', 1380, 'WAL', 22, 'BE', 50.41155000, 4.16469000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31502281'),
(8668, '<PERSON><PERSON><PERSON><PERSON>', 1380, '<PERSON><PERSON>', 22, '<PERSON><PERSON>', 49.94085000, 5.01591000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q21770327'),
(8669, '<PERSON>lankenberge', 1373, 'VLG', 22, '<PERSON>E', 51.31306000, 3.13227000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q328104'),
(8670, 'Blégny', 1380, 'WAL', 22, '<PERSON>E', 50.67255000, 5.72508000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q21770345'),
(8671, 'Bocholt', 1373, 'VLG', 22, 'BE', 51.17337000, 5.57994000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770354'),
(8672, 'Boechout', 1373, 'VLG', 22, 'BE', 51.15959000, 4.49195000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770358'),
(8673, 'Bonheiden', 1373, 'VLG', 22, 'BE', 51.02261000, 4.54714000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770391'),
(8674, 'Boom', 1373, 'VLG', 22, 'BE', 51.09242000, 4.37170000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770396'),
(8675, 'Boortmeerbeek', 1373, 'VLG', 22, 'BE', 50.97929000, 4.57443000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770398'),
(8676, 'Borgloon', 1373, 'VLG', 22, 'BE', 50.80505000, 5.34366000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770402'),
(8677, 'Bornem', 1373, 'VLG', 22, 'BE', 51.09716000, 4.24364000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31510162'),
(8678, 'Borsbeek', 1373, 'VLG', 22, 'BE', 51.19661000, 4.48543000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770407'),
(8679, 'Bouillon', 1380, 'WAL', 22, 'BE', 49.79324000, 5.06703000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q12360419'),
(8680, 'Boussu', 1380, 'WAL', 22, 'BE', 50.43417000, 3.79440000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31510518'),
(8681, 'Boutersem', 1373, 'VLG', 22, 'BE', 50.83511000, 4.83450000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770446'),
(8682, 'Braine-l\'Alleud', 1380, 'WAL', 22, 'BE', 50.68363000, 4.36784000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31511377'),
(8683, 'Braine-le-Château', 1380, 'WAL', 22, 'BE', 50.67990000, 4.27385000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q31511393'),
(8684, 'Braine-le-Comte', 1380, 'WAL', 22, 'BE', 50.60979000, 4.14658000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31511408'),
(8685, 'Braives', 1380, 'WAL', 22, 'BE', 50.61745000, 5.13302000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770459'),
(8686, 'Brasschaat', 1373, 'VLG', 22, 'BE', 51.29120000, 4.49182000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31511597'),
(8687, 'Brecht', 1373, 'VLG', 22, 'BE', 51.35024000, 4.63829000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31511665'),
(8688, 'Bredene', 1373, 'VLG', 22, 'BE', 51.23489000, 2.97559000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770466'),
(8689, 'Bree', 1373, 'VLG', 22, 'BE', 51.14152000, 5.59690000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770467'),
(8690, 'Brugelette', 1380, 'WAL', 22, 'BE', 50.59577000, 3.85363000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770494'),
(8691, 'Brugge', 1373, 'VLG', 22, 'BE', 51.20892000, 3.22424000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q12994'),
(8692, 'Brunehault', 1380, 'WAL', 22, 'BE', 50.50524000, 4.43209000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q12994'),
(8693, 'Brussels', 1376, 'BRU', 22, 'BE', 50.85045000, 4.34878000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q239'),
(8694, 'Buggenhout', 1373, 'VLG', 22, 'BE', 51.01590000, 4.20173000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770518'),
(8695, 'Bullange', 1380, 'WAL', 22, 'BE', 50.40731000, 6.25749000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770526'),
(8696, 'Burdinne', 1380, 'WAL', 22, 'BE', 50.58454000, 5.07663000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770530'),
(8697, 'Butgenbach', 1380, 'WAL', 22, 'BE', 50.42689000, 6.20504000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770544'),
(8698, 'Celles', 1380, 'WAL', 22, 'BE', 50.71229000, 3.45733000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770563'),
(8699, 'Cerfontaine', 1380, 'WAL', 22, 'BE', 50.17047000, 4.41028000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770566'),
(8700, 'Chapelle-lez-Herlaimont', 1380, 'WAL', 22, 'BE', 50.47130000, 4.28227000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770582'),
(8701, 'Charleroi', 1380, 'WAL', 22, 'BE', 50.41136000, 4.44448000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770582'),
(8702, 'Chastre-Villeroux-Blanmont', 1380, 'WAL', 22, 'BE', 50.60857000, 4.64198000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21770582'),
(8703, 'Chaudfontaine', 1380, 'WAL', 22, 'BE', 50.58280000, 5.63410000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31541936'),
(8704, 'Chaumont-Gistoux', 1380, 'WAL', 22, 'BE', 50.67753000, 4.72120000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31541958'),
(8705, 'Chimay', 1380, 'WAL', 22, 'BE', 50.04856000, 4.31712000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31543353'),
(8706, 'Chiny', 1380, 'WAL', 22, 'BE', 49.73833000, 5.34104000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768505'),
(8707, 'Chièvres', 1380, 'WAL', 22, 'BE', 50.58787000, 3.80711000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q21768502'),
(8708, 'Châtelet', 1380, 'WAL', 22, 'BE', 50.40338000, 4.52826000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q31544320'),
(8709, 'Ciney', 1380, 'WAL', 22, 'BE', 50.29449000, 5.10015000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768514'),
(8710, 'Clavier', 1380, 'WAL', 22, 'BE', 50.40069000, 5.35154000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768522'),
(8711, 'Colfontaine', 1380, 'WAL', 22, 'BE', 50.41410000, 3.85569000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31549500'),
(8712, 'Comblain-au-Pont', 1380, 'WAL', 22, 'BE', 50.47488000, 5.57711000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768534'),
(8713, 'Courcelles', 1380, 'WAL', 22, 'BE', 50.46379000, 4.37470000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31554196'),
(8714, 'Court-Saint-Étienne', 1380, 'WAL', 22, 'BE', 50.63378000, 4.56851000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q31554212'),
(8715, 'Couvin', 1380, 'WAL', 22, 'BE', 50.05284000, 4.49495000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768574'),
(8716, 'Crisnée', 1380, 'WAL', 22, 'BE', 50.71703000, 5.39802000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q21768581'),
(8717, 'Dalhem', 1380, 'WAL', 22, 'BE', 50.71315000, 5.72774000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768594'),
(8718, 'Damme', 1373, 'VLG', 22, 'BE', 51.25147000, 3.28144000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31560704'),
(8719, 'Daverdisse', 1380, 'WAL', 22, 'BE', 50.02161000, 5.11811000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768605'),
(8720, 'De Haan', 1373, 'VLG', 22, 'BE', 51.27261000, 3.03446000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768617'),
(8721, 'De Panne', 1373, 'VLG', 22, 'BE', 51.09793000, 2.59368000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768633'),
(8722, 'De Pinte', 1373, 'VLG', 22, 'BE', 50.99339000, 3.64747000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768637'),
(8723, 'Deerlijk', 1373, 'VLG', 22, 'BE', 50.85337000, 3.35416000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768611'),
(8724, 'Deinze', 1373, 'VLG', 22, 'BE', 50.98175000, 3.53096000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31564462'),
(8725, 'Denderleeuw', 1373, 'VLG', 22, 'BE', 50.88506000, 4.07601000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768627'),
(8726, 'Dendermonde', 1373, 'VLG', 22, 'BE', 51.02869000, 4.10106000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31565231'),
(8727, 'Dentergem', 1373, 'VLG', 22, 'BE', 50.96429000, 3.41617000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768632'),
(8728, 'Dessel', 1373, 'VLG', 22, 'BE', 51.23855000, 5.11448000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768646'),
(8729, 'Destelbergen', 1373, 'VLG', 22, 'BE', 51.05952000, 3.79899000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768651'),
(8730, 'Deurne', 1373, 'VLG', 22, 'BE', 51.22134000, 4.46595000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768651'),
(8731, 'Diegem', 1373, 'VLG', 22, 'BE', 50.89727000, 4.43354000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q723990'),
(8732, 'Diepenbeek', 1373, 'VLG', 22, 'BE', 50.90769000, 5.41875000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768660'),
(8733, 'Diest', 1373, 'VLG', 22, 'BE', 50.98923000, 5.05062000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31566756'),
(8734, 'Diksmuide', 1373, 'VLG', 22, 'BE', 51.03248000, 2.86384000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768668'),
(8735, 'Dilbeek', 1373, 'VLG', 22, 'BE', 50.84799000, 4.25972000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31567150'),
(8736, 'Dinant', 1380, 'WAL', 22, 'BE', 50.25807000, 4.91166000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31567774'),
(8737, 'Dison', 1380, 'WAL', 22, 'BE', 50.61004000, 5.85340000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768672'),
(8738, 'Doische', 1380, 'WAL', 22, 'BE', 50.13356000, 4.73545000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q22088310'),
(8739, 'Donceel', 1380, 'WAL', 22, 'BE', 50.64827000, 5.32000000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768678'),
(8740, 'Dour', 1380, 'WAL', 22, 'BE', 50.39583000, 3.77792000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768698'),
(8741, 'Drogenbos', 1373, 'VLG', 22, 'BE', 50.78733000, 4.31471000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31571519'),
(8742, 'Duffel', 1373, 'VLG', 22, 'BE', 51.09554000, 4.50903000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768707'),
(8743, 'Durbuy', 1380, 'WAL', 22, 'BE', 50.35291000, 5.45631000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768712'),
(8744, 'Edegem', 1373, 'VLG', 22, 'BE', 51.15662000, 4.44504000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31575484'),
(8745, 'Eeklo', 1373, 'VLG', 22, 'BE', 51.18703000, 3.55654000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31575952'),
(8746, 'Ellezelles', 1380, 'WAL', 22, 'BE', 50.73512000, 3.67985000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768742'),
(8747, 'Enghien', 1380, 'WAL', 22, 'BE', 50.68373000, 4.03284000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31578134'),
(8748, 'Engis', 1380, 'WAL', 22, 'BE', 50.58156000, 5.39916000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768761'),
(8749, 'Erquelinnes', 1380, 'WAL', 22, 'BE', 50.30688000, 4.11129000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768777'),
(8750, 'Esneux', 1380, 'WAL', 22, 'BE', 50.53596000, 5.56775000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q12362368'),
(8751, 'Essen', 1373, 'VLG', 22, 'BE', 51.46791000, 4.46901000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768786'),
(8752, 'Estaimpuis', 1380, 'WAL', 22, 'BE', 50.70485000, 3.26785000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768790'),
(8753, 'Estinnes-au-Val', 1380, 'WAL', 22, 'BE', 50.41016000, 4.10477000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768790'),
(8754, 'Eupen', 1380, 'WAL', 22, 'BE', 50.62790000, 6.03647000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31579678'),
(8755, 'Evergem', 1373, 'VLG', 22, 'BE', 51.11306000, 3.70976000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31579974'),
(8756, 'Faimes', 1380, 'WAL', 22, 'BE', 50.66252000, 5.26005000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768817'),
(8757, 'Farciennes', 1380, 'WAL', 22, 'BE', 50.43006000, 4.54152000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768828'),
(8758, 'Fauvillers', 1380, 'WAL', 22, 'BE', 49.85116000, 5.66405000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768831'),
(8759, 'Ferrières', 1380, 'WAL', 22, 'BE', 50.40157000, 5.61092000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q21768849'),
(8760, 'Fexhe-le-Haut-Clocher', 1380, 'WAL', 22, 'BE', 50.66540000, 5.39978000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768852'),
(8761, 'Fleurus', 1380, 'WAL', 22, 'BE', 50.48351000, 4.55006000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q31583129'),
(8762, 'Flobecq', 1380, 'WAL', 22, 'BE', 50.73733000, 3.73876000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768868'),
(8763, 'Floreffe', 1380, 'WAL', 22, 'BE', 50.43452000, 4.75960000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768870'),
(8764, 'Florennes', 1380, 'WAL', 22, 'BE', 50.25127000, 4.60636000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q21768873'),
(8765, 'Florenville', 1380, 'WAL', 22, 'BE', 49.69983000, 5.30740000, '2019-10-05 22:29:25', '2019-10-05 22:29:25', 1, 'Q13212919'),
(8766, 'Flémalle-Haute', 1380, 'WAL', 22, 'BE', 50.59994000, 5.44471000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q2356647'),
(8767, 'Fléron', 1380, 'WAL', 22, 'BE', 50.61516000, 5.68062000, '2019-10-05 22:29:25', '2020-05-01 17:22:35', 1, 'Q21768865'),
(8768, 'Forville', 1380, 'WAL', 22, 'BE', 50.57424000, 4.99861000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q1866454'),
(8769, 'Fosses-la-Ville', 1380, 'WAL', 22, 'BE', 50.39517000, 4.69623000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q720478'),
(8770, 'Frameries', 1380, 'WAL', 22, 'BE', 50.40578000, 3.89603000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q720478'),
(8771, 'Frasnes-lez-Buissenal', 1380, 'WAL', 22, 'BE', 50.66783000, 3.62047000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q3086686'),
(8772, 'Froidchapelle', 1380, 'WAL', 22, 'BE', 50.15106000, 4.32742000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21768962'),
(8773, 'Galmaarden', 1373, 'VLG', 22, 'BE', 50.75389000, 3.97121000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21768971'),
(8774, 'Gavere', 1373, 'VLG', 22, 'BE', 50.92917000, 3.66184000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21768983'),
(8775, 'Gedinne', 1380, 'WAL', 22, 'BE', 49.98037000, 4.93674000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21768986'),
(8776, 'Geel', 1373, 'VLG', 22, 'BE', 51.16557000, 4.98917000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31589706'),
(8777, 'Geer', 1380, 'WAL', 22, 'BE', 50.66990000, 5.17364000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21768987'),
(8778, 'Geetbets', 1373, 'VLG', 22, 'BE', 50.89431000, 5.11199000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21768989'),
(8779, 'Gembloux', 1380, 'WAL', 22, 'BE', 50.56149000, 4.69889000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31589725'),
(8780, 'Genappe', 1380, 'WAL', 22, 'BE', 50.61173000, 4.45152000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31589760'),
(8781, 'Genk', 1373, 'VLG', 22, 'BE', 50.96500000, 5.50082000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769011'),
(8782, 'Gent', 1373, 'VLG', 22, 'BE', 51.05000000, 3.71667000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31590146'),
(8783, 'Geraardsbergen', 1373, 'VLG', 22, 'BE', 50.77343000, 3.88223000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31590351'),
(8784, 'Gerpinnes', 1380, 'WAL', 22, 'BE', 50.33789000, 4.52731000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769026'),
(8785, 'Gesves', 1380, 'WAL', 22, 'BE', 50.40146000, 5.07457000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769032'),
(8786, 'Gingelom', 1373, 'VLG', 22, 'BE', 50.74792000, 5.13422000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769045'),
(8787, 'Gistel', 1373, 'VLG', 22, 'BE', 51.15612000, 2.96387000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769047'),
(8788, 'Glabbeek', 1373, 'VLG', 22, 'BE', 50.87267000, 4.95615000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769047'),
(8789, 'Gooik', 1373, 'VLG', 22, 'BE', 50.79443000, 4.11378000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824012'),
(8790, 'Gouvy', 1380, 'WAL', 22, 'BE', 50.18600000, 5.93917000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769078'),
(8791, 'Grez-Doiceau', 1380, 'WAL', 22, 'BE', 50.73901000, 4.69829000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q13212992'),
(8792, 'Grimbergen', 1373, 'VLG', 22, 'BE', 50.93409000, 4.37213000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824015'),
(8793, 'Grobbendonk', 1373, 'VLG', 22, 'BE', 51.19043000, 4.73562000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21769133'),
(8794, 'Haacht', 1373, 'VLG', 22, 'BE', 50.97737000, 4.63777000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767623'),
(8795, 'Haaltert', 1373, 'VLG', 22, 'BE', 50.90634000, 4.00093000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767627'),
(8796, 'Habay-la-Vieille', 1380, 'WAL', 22, 'BE', 49.72329000, 5.61999000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767627'),
(8797, 'Halen', 1373, 'VLG', 22, 'BE', 50.94837000, 5.11096000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824016'),
(8798, 'Halle', 1373, 'VLG', 22, 'BE', 50.73385000, 4.23454000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824017'),
(8799, 'Hamme', 1373, 'VLG', 22, 'BE', 51.09822000, 4.13705000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31811416'),
(8800, 'Hamoir', 1380, 'WAL', 22, 'BE', 50.42675000, 5.53304000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767669'),
(8801, 'Hamois', 1380, 'WAL', 22, 'BE', 50.34020000, 5.15619000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767671'),
(8802, 'Hannut', 1380, 'WAL', 22, 'BE', 50.67142000, 5.07898000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q683141'),
(8803, 'Harelbeke', 1373, 'VLG', 22, 'BE', 50.85343000, 3.30935000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824027'),
(8804, 'Hasselt', 1373, 'VLG', 22, 'BE', 50.93106000, 5.33781000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767697'),
(8805, 'Hastière-Lavaux', 1380, 'WAL', 22, 'BE', 50.21849000, 4.82446000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q2186694'),
(8806, 'Havelange', 1380, 'WAL', 22, 'BE', 50.38931000, 5.23816000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767720'),
(8807, 'Heers', 1373, 'VLG', 22, 'BE', 50.75383000, 5.30210000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767739'),
(8808, 'Heist-op-den-Berg', 1373, 'VLG', 22, 'BE', 51.07537000, 4.72827000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824028'),
(8809, 'Helchteren', 1373, 'VLG', 22, 'BE', 51.05591000, 5.38244000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824028'),
(8810, 'Hemiksem', 1373, 'VLG', 22, 'BE', 51.14484000, 4.33874000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767764'),
(8811, 'Hensies', 1380, 'WAL', 22, 'BE', 50.43263000, 3.68411000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767768'),
(8812, 'Herbeumont', 1380, 'WAL', 22, 'BE', 49.78086000, 5.23580000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767768'),
(8813, 'Herent', 1373, 'VLG', 22, 'BE', 50.90861000, 4.67056000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824029'),
(8814, 'Herentals', 1373, 'VLG', 22, 'BE', 51.17655000, 4.83248000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824030'),
(8815, 'Herenthout', 1373, 'VLG', 22, 'BE', 51.14010000, 4.75572000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767782'),
(8816, 'Herk-de-Stad', 1373, 'VLG', 22, 'BE', 50.94013000, 5.16636000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767785'),
(8817, 'Herne', 1373, 'VLG', 22, 'BE', 50.72423000, 4.03481000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767790'),
(8818, 'Herselt', 1373, 'VLG', 22, 'BE', 51.05159000, 4.88231000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767799'),
(8819, 'Herstal', 1380, 'WAL', 22, 'BE', 50.66415000, 5.62346000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767799'),
(8820, 'Herve', 1380, 'WAL', 22, 'BE', 50.64083000, 5.79353000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767807'),
(8821, 'Herzele', 1373, 'VLG', 22, 'BE', 50.88681000, 3.89014000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767811'),
(8822, 'Heusden', 1373, 'VLG', 22, 'BE', 51.03664000, 5.28013000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767811'),
(8823, 'Hoboken', 1373, 'VLG', 22, 'BE', 51.17611000, 4.34844000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q267663'),
(8824, 'Hoegaarden', 1373, 'VLG', 22, 'BE', 50.77560000, 4.88952000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824032'),
(8825, 'Hoeilaart', 1373, 'VLG', 22, 'BE', 50.76730000, 4.46835000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767840'),
(8826, 'Hoeselt', 1373, 'VLG', 22, 'BE', 50.84714000, 5.48767000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767843'),
(8827, 'Holsbeek', 1373, 'VLG', 22, 'BE', 50.92097000, 4.75747000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767868'),
(8828, 'Hooglede', 1373, 'VLG', 22, 'BE', 50.98333000, 3.08333000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767880'),
(8829, 'Hoogstraten', 1373, 'VLG', 22, 'BE', 51.40029000, 4.76034000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824034'),
(8830, 'Hotton', 1380, 'WAL', 22, 'BE', 50.26742000, 5.44609000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767891'),
(8831, 'Houffalize', 1380, 'WAL', 22, 'BE', 50.13235000, 5.78962000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767894'),
(8832, 'Houthalen', 1373, 'VLG', 22, 'BE', 51.03427000, 5.37429000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767894'),
(8833, 'Houthulst', 1373, 'VLG', 22, 'BE', 50.97824000, 2.95050000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767908'),
(8834, 'Houyet', 1380, 'WAL', 22, 'BE', 50.18619000, 5.00762000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767911'),
(8835, 'Hove', 1373, 'VLG', 22, 'BE', 51.15446000, 4.47070000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767915'),
(8836, 'Huldenberg', 1373, 'VLG', 22, 'BE', 50.78939000, 4.58310000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767922'),
(8837, 'Hulshout', 1373, 'VLG', 22, 'BE', 51.07451000, 4.79081000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767926'),
(8838, 'Huy', 1380, 'WAL', 22, 'BE', 50.51894000, 5.23284000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q18542624'),
(8839, 'Héron', 1380, 'WAL', 22, 'BE', 50.54731000, 5.09774000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q21767794'),
(8840, 'Ichtegem', 1373, 'VLG', 22, 'BE', 51.09572000, 3.01549000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767935'),
(8841, 'Ieper', 1373, 'VLG', 22, 'BE', 50.85114000, 2.88569000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824036'),
(8842, 'Incourt', 1380, 'WAL', 22, 'BE', 50.69151000, 4.79816000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824037'),
(8843, 'Ingelmunster', 1373, 'VLG', 22, 'BE', 50.92081000, 3.25571000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767948'),
(8844, 'Ittre', 1380, 'WAL', 22, 'BE', 50.64396000, 4.26476000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767953'),
(8845, 'Izegem', 1373, 'VLG', 22, 'BE', 50.91396000, 3.21378000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31811524'),
(8846, 'Jabbeke', 1373, 'VLG', 22, 'BE', 51.18185000, 3.08935000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767957'),
(8847, 'Jalhay', 1380, 'WAL', 22, 'BE', 50.55876000, 5.96764000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767964'),
(8848, 'Jodoigne', 1380, 'WAL', 22, 'BE', 50.72357000, 4.86914000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824038'),
(8849, 'Juprelle', 1380, 'WAL', 22, 'BE', 50.70760000, 5.53127000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767127'),
(8850, 'Jurbise', 1380, 'WAL', 22, 'BE', 50.53100000, 3.90942000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767129'),
(8851, 'Kalmthout', 1373, 'VLG', 22, 'BE', 51.38442000, 4.47556000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767145'),
(8852, 'Kampenhout', 1373, 'VLG', 22, 'BE', 50.94210000, 4.55103000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824039'),
(8853, 'Kapelle-op-den-Bos', 1373, 'VLG', 22, 'BE', 51.00970000, 4.36303000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767154'),
(8854, 'Kapellen', 1373, 'VLG', 22, 'BE', 51.31377000, 4.43539000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824040'),
(8855, 'Kaprijke', 1373, 'VLG', 22, 'BE', 51.21720000, 3.61519000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767157'),
(8856, 'Kasterlee', 1373, 'VLG', 22, 'BE', 51.24118000, 4.96651000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767175'),
(8857, 'Keerbergen', 1373, 'VLG', 22, 'BE', 51.00295000, 4.63434000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767185'),
(8858, 'Kinrooi', 1373, 'VLG', 22, 'BE', 51.14543000, 5.74207000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767215'),
(8859, 'Knesselare', 1373, 'VLG', 22, 'BE', 51.13932000, 3.41282000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767265'),
(8860, 'Knokke-Heist', 1373, 'VLG', 22, 'BE', 51.35000000, 3.26667000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824041'),
(8861, 'Koekelare', 1373, 'VLG', 22, 'BE', 51.09047000, 2.97830000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767270'),
(8862, 'Koksijde', 1373, 'VLG', 22, 'BE', 51.11642000, 2.63772000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q15071298'),
(8863, 'Kontich', 1373, 'VLG', 22, 'BE', 51.13213000, 4.44706000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824055'),
(8864, 'Kortemark', 1373, 'VLG', 22, 'BE', 51.02951000, 3.04112000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767292'),
(8865, 'Kortenaken', 1373, 'VLG', 22, 'BE', 50.90862000, 5.05968000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767293'),
(8866, 'Kortenberg', 1373, 'VLG', 22, 'BE', 50.88982000, 4.54353000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767295'),
(8867, 'Kortessem', 1373, 'VLG', 22, 'BE', 50.85890000, 5.38974000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767297'),
(8868, 'Kortrijk', 1373, 'VLG', 22, 'BE', 50.82803000, 3.26487000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767298'),
(8869, 'Kraainem', 1373, 'VLG', 22, 'BE', 50.86155000, 4.46946000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824063'),
(8870, 'Kruibeke', 1373, 'VLG', 22, 'BE', 51.17048000, 4.31444000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767327'),
(8871, 'Kruishoutem', 1373, 'VLG', 22, 'BE', 50.90168000, 3.52588000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767332'),
(8872, 'Kuurne', 1373, 'VLG', 22, 'BE', 50.85143000, 3.28240000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767334'),
(8873, 'La Bruyère', 1380, 'WAL', 22, 'BE', 50.39478000, 4.61444000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q21767334'),
(8874, 'La Calamine', 1380, 'WAL', 22, 'BE', 50.71809000, 6.01107000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q16216319'),
(8875, 'La Hulpe', 1380, 'WAL', 22, 'BE', 50.73091000, 4.48577000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824072'),
(8876, 'La Louvière', 1380, 'WAL', 22, 'BE', 50.48657000, 4.18785000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q21921673'),
(8877, 'La Roche-en-Ardenne', 1380, 'WAL', 22, 'BE', 50.18361000, 5.57547000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765473'),
(8878, 'Laarne', 1373, 'VLG', 22, 'BE', 51.03078000, 3.85077000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21767343'),
(8879, 'Lanaken', 1373, 'VLG', 22, 'BE', 50.89318000, 5.64680000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824076'),
(8880, 'Landen', 1373, 'VLG', 22, 'BE', 50.75267000, 5.08200000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765449'),
(8881, 'Lebbeke', 1373, 'VLG', 22, 'BE', 51.00464000, 4.13457000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765489'),
(8882, 'Lede', 1373, 'VLG', 22, 'BE', 50.96626000, 3.98594000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765497'),
(8883, 'Ledeberg', 1373, 'VLG', 22, 'BE', 51.03859000, 3.74458000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q2802143'),
(8884, 'Ledegem', 1373, 'VLG', 22, 'BE', 50.85785000, 3.12409000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765501'),
(8885, 'Lendelede', 1373, 'VLG', 22, 'BE', 50.88626000, 3.23747000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765541'),
(8886, 'Lens', 1380, 'WAL', 22, 'BE', 50.55696000, 3.89946000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765542'),
(8887, 'Leopoldsburg', 1373, 'VLG', 22, 'BE', 51.11667000, 5.25000000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765544'),
(8888, 'Lessines', 1380, 'WAL', 22, 'BE', 50.71104000, 3.83579000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824077'),
(8889, 'Leuven', 1373, 'VLG', 22, 'BE', 50.87959000, 4.70093000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765562'),
(8890, 'Libin', 1380, 'WAL', 22, 'BE', 49.98107000, 5.25612000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765567'),
(8891, 'Lichtervelde', 1373, 'VLG', 22, 'BE', 51.03333000, 3.15000000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765568'),
(8892, 'Liedekerke', 1373, 'VLG', 22, 'BE', 50.86892000, 4.08743000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824078'),
(8893, 'Lier', 1373, 'VLG', 22, 'BE', 51.13128000, 4.57041000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824079'),
(8894, 'Lierneux', 1380, 'WAL', 22, 'BE', 50.28477000, 5.79236000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765575'),
(8895, 'Lille', 1373, 'VLG', 22, 'BE', 51.24197000, 4.82313000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765583'),
(8896, 'Limbourg', 1380, 'WAL', 22, 'BE', 50.61222000, 5.94120000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765585'),
(8897, 'Lincent', 1380, 'WAL', 22, 'BE', 50.71222000, 5.03654000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765588'),
(8898, 'Linkebeek', 1373, 'VLG', 22, 'BE', 50.76781000, 4.33688000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824080'),
(8899, 'Lint', 1373, 'VLG', 22, 'BE', 51.12707000, 4.49669000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765593'),
(8900, 'Liège', 1380, 'WAL', 22, 'BE', 50.63373000, 5.56749000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q21765593'),
(8901, 'Lobbes', 1380, 'WAL', 22, 'BE', 50.35258000, 4.26716000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765602'),
(8902, 'Lochristi', 1373, 'VLG', 22, 'BE', 51.09644000, 3.83194000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824081'),
(8903, 'Lokeren', 1373, 'VLG', 22, 'BE', 51.10364000, 3.99339000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824081'),
(8904, 'Lommel', 1373, 'VLG', 22, 'BE', 51.23074000, 5.31349000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824084'),
(8905, 'Londerzeel', 1373, 'VLG', 22, 'BE', 51.00468000, 4.30304000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824085'),
(8906, 'Lontzen', 1380, 'WAL', 22, 'BE', 50.68126000, 6.00712000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765621'),
(8907, 'Louvain-la-Neuve', 1380, 'WAL', 22, 'BE', 50.66829000, 4.61443000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q665868'),
(8908, 'Lovendegem', 1373, 'VLG', 22, 'BE', 51.10168000, 3.61298000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765635'),
(8909, 'Lubbeek', 1373, 'VLG', 22, 'BE', 50.88278000, 4.83896000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765640'),
(8910, 'Lummen', 1373, 'VLG', 22, 'BE', 50.98772000, 5.19121000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765647'),
(8911, 'Léglise', 1380, 'WAL', 22, 'BE', 49.79985000, 5.53652000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q21765515'),
(8912, 'Maaseik', 1373, 'VLG', 22, 'BE', 51.09802000, 5.78379000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824086'),
(8913, 'Maasmechelen', 1373, 'VLG', 22, 'BE', 50.96545000, 5.69452000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824086'),
(8914, 'Machelen', 1373, 'VLG', 22, 'BE', 50.91061000, 4.44174000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765652'),
(8915, 'Maldegem', 1373, 'VLG', 22, 'BE', 51.20737000, 3.44511000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824087'),
(8916, 'Malmédy', 1380, 'WAL', 22, 'BE', 50.42686000, 6.02794000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q31824088'),
(8917, 'Manage', 1380, 'WAL', 22, 'BE', 50.50312000, 4.23589000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824089'),
(8918, 'Manhay', 1380, 'WAL', 22, 'BE', 50.29219000, 5.67562000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765693'),
(8919, 'Marche-en-Famenne', 1380, 'WAL', 22, 'BE', 50.22678000, 5.34416000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765709'),
(8920, 'Marchin', 1380, 'WAL', 22, 'BE', 50.46707000, 5.24280000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765713'),
(8921, 'Martelange', 1380, 'WAL', 22, 'BE', 49.83195000, 5.73655000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765733'),
(8922, 'Mechelen', 1373, 'VLG', 22, 'BE', 51.02574000, 4.47762000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765751'),
(8923, 'Meerhout', 1373, 'VLG', 22, 'BE', 51.13210000, 5.07842000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765759'),
(8924, 'Meise', 1373, 'VLG', 22, 'BE', 50.93934000, 4.32655000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824090'),
(8925, 'Meix-devant-Virton', 1380, 'WAL', 22, 'BE', 49.60581000, 5.48045000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765765'),
(8926, 'Melle', 1373, 'VLG', 22, 'BE', 51.00232000, 3.80526000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765771'),
(8927, 'Menen', 1373, 'VLG', 22, 'BE', 50.79722000, 3.12245000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824091'),
(8928, 'Merbes-le-Château', 1380, 'WAL', 22, 'BE', 50.32449000, 4.16489000, '2019-10-05 22:29:26', '2020-05-01 17:22:35', 1, 'Q21765785'),
(8929, 'Merchtem', 1373, 'VLG', 22, 'BE', 50.95129000, 4.23197000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765786'),
(8930, 'Merelbeke', 1373, 'VLG', 22, 'BE', 50.99447000, 3.74621000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824092'),
(8931, 'Merksplas', 1373, 'VLG', 22, 'BE', 51.35851000, 4.86513000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765789'),
(8932, 'Messancy', 1380, 'WAL', 22, 'BE', 49.59201000, 5.81879000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765791'),
(8933, 'Mettet', 1380, 'WAL', 22, 'BE', 50.32119000, 4.66232000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765794'),
(8934, 'Meulebeke', 1373, 'VLG', 22, 'BE', 50.95136000, 3.28804000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765800'),
(8935, 'Middelkerke', 1373, 'VLG', 22, 'BE', 51.18532000, 2.82077000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765816'),
(8936, 'Modave', 1380, 'WAL', 22, 'BE', 50.44614000, 5.29532000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765828'),
(8937, 'Moerbeke', 1373, 'VLG', 22, 'BE', 51.17409000, 3.93001000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765833'),
(8938, 'Mol', 1373, 'VLG', 22, 'BE', 51.19188000, 5.11662000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q31824099'),
(8939, 'Momignies', 1380, 'WAL', 22, 'BE', 50.02710000, 4.16519000, '2019-10-05 22:29:26', '2019-10-05 22:29:26', 1, 'Q21765930'),
(8940, 'Mons', 1380, 'WAL', 22, 'BE', 50.45413000, 3.95229000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21765930'),
(8941, 'Mons-lez-Liège', 1380, 'WAL', 22, 'BE', 50.61667000, 5.46667000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21765930'),
(8942, 'Mont-Saint-Guibert', 1380, 'WAL', 22, 'BE', 50.63427000, 4.61061000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31824108'),
(8943, 'Moorslede', 1373, 'VLG', 22, 'BE', 50.89190000, 3.06117000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21765968'),
(8944, 'Morlanwelz-Mariemont', 1380, 'WAL', 22, 'BE', 50.45502000, 4.24519000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q2358095'),
(8945, 'Mortsel', 1373, 'VLG', 22, 'BE', 51.16697000, 4.45127000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31824113'),
(8946, 'Mouscron', 1380, 'WAL', 22, 'BE', 50.74497000, 3.20639000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q37736670'),
(8947, 'Musson', 1380, 'WAL', 22, 'BE', 49.55835000, 5.70525000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766012'),
(8948, 'Namur', 1380, 'WAL', 22, 'BE', 50.46690000, 4.86746000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q51389725'),
(8949, 'Nandrin', 1380, 'WAL', 22, 'BE', 50.50675000, 5.41905000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766022'),
(8950, 'Nassogne', 1380, 'WAL', 22, 'BE', 50.12849000, 5.34274000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q20621818'),
(8951, 'Nazareth', 1373, 'VLG', 22, 'BE', 50.95686000, 3.59425000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766031'),
(8952, 'Neerpelt', 1373, 'VLG', 22, 'BE', 51.22807000, 5.44270000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q16481661'),
(8953, 'Neufchâteau', 1380, 'WAL', 22, 'BE', 49.84074000, 5.43535000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21766053'),
(8954, 'Nevele', 1373, 'VLG', 22, 'BE', 51.03531000, 3.54574000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766064'),
(8955, 'Niel', 1373, 'VLG', 22, 'BE', 51.11096000, 4.33428000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766070'),
(8956, 'Nieuwerkerken', 1373, 'VLG', 22, 'BE', 50.86380000, 5.19467000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766078'),
(8957, 'Nieuwpoort', 1373, 'VLG', 22, 'BE', 51.13008000, 2.75135000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766081'),
(8958, 'Nijlen', 1373, 'VLG', 22, 'BE', 51.16096000, 4.67008000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31824122'),
(8959, 'Ninove', 1373, 'VLG', 22, 'BE', 50.82776000, 4.02657000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31812647'),
(8960, 'Nivelles', 1380, 'WAL', 22, 'BE', 50.59833000, 4.32848000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31824124'),
(8961, 'Noville-les-Bois', 1380, 'WAL', 22, 'BE', 50.55702000, 4.98466000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q3247707'),
(8962, 'Ohey', 1380, 'WAL', 22, 'BE', 50.43570000, 5.12375000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q13216634'),
(8963, 'Olen', 1373, 'VLG', 22, 'BE', 51.14391000, 4.85980000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766128'),
(8964, 'Olne', 1380, 'WAL', 22, 'BE', 50.58994000, 5.74662000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766135'),
(8965, 'Onhaye', 1380, 'WAL', 22, 'BE', 50.24148000, 4.84069000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766148'),
(8966, 'Oostduinkerke', 1373, 'VLG', 22, 'BE', 51.11565000, 2.68217000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766148'),
(8967, 'Oosterzele', 1373, 'VLG', 22, 'BE', 50.95261000, 3.79826000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764228'),
(8968, 'Oostkamp', 1373, 'VLG', 22, 'BE', 51.15432000, 3.23128000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q18479689'),
(8969, 'Oostmalle', 1373, 'VLG', 22, 'BE', 51.30000000, 4.73333000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q18479689'),
(8970, 'Oostrozebeke', 1373, 'VLG', 22, 'BE', 50.92093000, 3.33799000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764231'),
(8971, 'Opglabbeek', 1373, 'VLG', 22, 'BE', 51.04258000, 5.58346000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764233'),
(8972, 'Opwijk', 1373, 'VLG', 22, 'BE', 50.96724000, 4.18442000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764240'),
(8973, 'Oreye', 1380, 'WAL', 22, 'BE', 50.71749000, 5.34880000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764245'),
(8974, 'Ostend', 1373, 'VLG', 22, 'BE', 51.21551000, 2.92700000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21766156'),
(8975, 'Ottignies', 1380, 'WAL', 22, 'BE', 50.66535000, 4.56679000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q3357775'),
(8976, 'Oud-Heverlee', 1373, 'VLG', 22, 'BE', 50.83522000, 4.66421000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764275'),
(8977, 'Oud-Turnhout', 1373, 'VLG', 22, 'BE', 51.31978000, 4.98410000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764277'),
(8978, 'Oudenaarde', 1373, 'VLG', 22, 'BE', 50.85168000, 3.60891000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31824126'),
(8979, 'Oudenburg', 1373, 'VLG', 22, 'BE', 51.18489000, 3.00035000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764272'),
(8980, 'Ouffet', 1380, 'WAL', 22, 'BE', 50.43870000, 5.46570000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764278'),
(8981, 'Oupeye', 1380, 'WAL', 22, 'BE', 50.71184000, 5.64680000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31824127'),
(8982, 'Overijse', 1373, 'VLG', 22, 'BE', 50.77436000, 4.53461000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31824128'),
(8983, 'Overpelt', 1373, 'VLG', 22, 'BE', 51.21038000, 5.41557000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764289'),
(8984, 'Paliseul', 1380, 'WAL', 22, 'BE', 49.90395000, 5.13537000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q12371903'),
(8985, 'Pecq', 1380, 'WAL', 22, 'BE', 50.68619000, 3.33789000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764309'),
(8986, 'Peer', 1373, 'VLG', 22, 'BE', 51.13030000, 5.45952000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764310'),
(8987, 'Pepingen', 1373, 'VLG', 22, 'BE', 50.75922000, 4.15983000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764319'),
(8988, 'Pepinster', 1380, 'WAL', 22, 'BE', 50.57375000, 5.80490000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764321'),
(8989, 'Perre', 1373, 'VLG', 22, 'BE', 50.88914000, 3.86098000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764321'),
(8990, 'Perwez', 1380, 'WAL', 22, 'BE', 50.62426000, 4.81354000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31466803'),
(8991, 'Philippeville', 1380, 'WAL', 22, 'BE', 50.19612000, 4.54374000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764350'),
(8992, 'Pittem', 1373, 'VLG', 22, 'BE', 50.99279000, 3.26317000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764368'),
(8993, 'Plombières', 1380, 'WAL', 22, 'BE', 50.73656000, 5.95922000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21764380'),
(8994, 'Pont-à-Celles', 1380, 'WAL', 22, 'BE', 50.50518000, 4.36887000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21764393'),
(8995, 'Poperinge', 1373, 'VLG', 22, 'BE', 50.85386000, 2.72659000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31475234'),
(8996, 'Profondeville', 1380, 'WAL', 22, 'BE', 50.37581000, 4.86506000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764434'),
(8997, 'Province de Liège', 1380, 'WAL', 22, 'BE', 50.63427000, 5.56543000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q1127'),
(8998, 'Province de Namur', 1380, 'WAL', 22, 'BE', 50.33333000, 4.83333000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1125'),
(8999, 'Province du Brabant Wallon', 1380, 'WAL', 22, 'BE', 50.75000000, 4.58333000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1122'),
(9000, 'Province du Hainaut', 1380, 'WAL', 22, 'BE', 50.50000000, 3.83333000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1129'),
(9002, 'Provincie Antwerpen', 1373, 'VLG', 22, 'BE', 51.16558000, 4.83402000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1116'),
(9003, 'Provincie Limburg', 1373, 'VLG', 22, 'BE', 51.00000000, 5.50000000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1095'),
(9004, 'Provincie Oost-Vlaanderen', 1373, 'VLG', 22, 'BE', 51.00000000, 3.75000000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1114'),
(9005, 'Provincie Vlaams-Brabant', 1373, 'VLG', 22, 'BE', 50.91667000, 4.58333000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1118'),
(9006, 'Provincie West-Vlaanderen', 1373, 'VLG', 22, 'BE', 51.00000000, 3.00000000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q1113'),
(9007, 'Putte', 1373, 'VLG', 22, 'BE', 51.05337000, 4.63263000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764443'),
(9008, 'Puurs', 1373, 'VLG', 22, 'BE', 51.07409000, 4.28844000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764446'),
(9009, 'Péruwelz', 1380, 'WAL', 22, 'BE', 50.50819000, 3.59373000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21764327'),
(9010, 'Quaregnon', 1380, 'WAL', 22, 'BE', 50.44067000, 3.86530000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31481282'),
(9011, 'Quiévrain', 1380, 'WAL', 22, 'BE', 50.40737000, 3.68351000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21764452'),
(9012, 'Quévy-le-Petit', 1380, 'WAL', 22, 'BE', 50.36879000, 3.93602000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21764452'),
(9013, 'Raeren', 1380, 'WAL', 22, 'BE', 50.66720000, 6.11535000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764462'),
(9014, 'Ramillies', 1380, 'WAL', 22, 'BE', 50.63395000, 4.90119000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764462'),
(9015, 'Ranst', 1373, 'VLG', 22, 'BE', 51.18983000, 4.56533000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764475'),
(9016, 'Ravels', 1373, 'VLG', 22, 'BE', 51.37274000, 4.99210000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764482'),
(9017, 'Rebecq-Rognon', 1380, 'WAL', 22, 'BE', 50.65147000, 4.10683000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764482'),
(9018, 'Remicourt', 1380, 'WAL', 22, 'BE', 50.68069000, 5.32785000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764500'),
(9019, 'Rendeux', 1380, 'WAL', 22, 'BE', 50.23423000, 5.50414000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764509'),
(9020, 'Retie', 1373, 'VLG', 22, 'BE', 51.26652000, 5.08242000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764513'),
(9021, 'Riemst', 1373, 'VLG', 22, 'BE', 50.80995000, 5.60131000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764528'),
(9022, 'Rijkevorsel', 1373, 'VLG', 22, 'BE', 51.34795000, 4.76053000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764534'),
(9023, 'Rixensart', 1380, 'WAL', 22, 'BE', 50.71229000, 4.52529000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31490281'),
(9024, 'Rochefort', 1380, 'WAL', 22, 'BE', 50.16310000, 5.22160000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764555'),
(9025, 'Roeselare', 1373, 'VLG', 22, 'BE', 50.94653000, 3.12269000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764555'),
(9026, 'Roeulx', 1380, 'WAL', 22, 'BE', 50.50365000, 4.11163000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q34481995'),
(9027, 'Ronse', 1373, 'VLG', 22, 'BE', 50.74574000, 3.60050000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31493468'),
(9028, 'Rotselaar', 1373, 'VLG', 22, 'BE', 50.95302000, 4.71665000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764627'),
(9029, 'Rouvroy', 1380, 'WAL', 22, 'BE', 49.53771000, 5.49031000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764632'),
(9030, 'Ruiselede', 1373, 'VLG', 22, 'BE', 51.04039000, 3.39416000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764643'),
(9031, 'Rumes', 1380, 'WAL', 22, 'BE', 50.55450000, 3.30535000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764647'),
(9032, 'Rumst', 1373, 'VLG', 22, 'BE', 51.08153000, 4.42217000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764648'),
(9033, 'Saint-Ghislain', 1380, 'WAL', 22, 'BE', 50.44816000, 3.81886000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31499143'),
(9034, 'Saint-Hubert', 1380, 'WAL', 22, 'BE', 50.02668000, 5.37401000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764663'),
(9035, 'Saint-Léger', 1380, 'WAL', 22, 'BE', 49.61196000, 5.65688000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21764678'),
(9036, 'Saint-Nicolas', 1380, 'WAL', 22, 'BE', 50.62837000, 5.53243000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764685'),
(9037, 'Saint-Vith', 1380, 'WAL', 22, 'BE', 50.28146000, 6.12724000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764698'),
(9038, 'Sainte-Ode', 1380, 'WAL', 22, 'BE', 50.01723000, 5.51926000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764661'),
(9039, 'Schelle', 1373, 'VLG', 22, 'BE', 51.12615000, 4.34114000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764741'),
(9040, 'Schilde', 1373, 'VLG', 22, 'BE', 51.24107000, 4.58336000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31514505'),
(9041, 'Schoten', 1373, 'VLG', 22, 'BE', 51.25251000, 4.50268000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31514684'),
(9042, 'Seneffe', 1380, 'WAL', 22, 'BE', 50.53135000, 4.26301000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764775'),
(9043, 'Seraing', 1380, 'WAL', 22, 'BE', 50.58362000, 5.50115000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31516025'),
(9044, 'Silly', 1380, 'WAL', 22, 'BE', 50.64877000, 3.92363000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764794'),
(9045, 'Sint-Amands', 1373, 'VLG', 22, 'BE', 51.05645000, 4.20957000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764799'),
(9046, 'Sint-Genesius-Rode', 1373, 'VLG', 22, 'BE', 50.74645000, 4.35754000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764808'),
(9047, 'Sint-Gillis-Waas', 1373, 'VLG', 22, 'BE', 51.21914000, 4.12374000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764811'),
(9048, 'Sint-Joris', 1373, 'VLG', 22, 'BE', 50.87117000, 5.27200000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764811'),
(9049, 'Sint-Katelijne-Waver', 1373, 'VLG', 22, 'BE', 51.06691000, 4.53469000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31521084'),
(9050, 'Sint-Kruis', 1373, 'VLG', 22, 'BE', 51.21399000, 3.24949000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q2505670'),
(9051, 'Sint-Laureins', 1373, 'VLG', 22, 'BE', 51.24202000, 3.52441000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764820'),
(9052, 'Sint-Lievens-Houtem', 1373, 'VLG', 22, 'BE', 50.91970000, 3.86225000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764821'),
(9053, 'Sint-Maria-Lierde', 1373, 'VLG', 22, 'BE', 50.81867000, 3.84436000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764821'),
(9054, 'Sint-Martens-Latem', 1373, 'VLG', 22, 'BE', 51.01459000, 3.63779000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764825'),
(9055, 'Sint-Martens-Lennik', 1373, 'VLG', 22, 'BE', 50.81158000, 4.16965000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764825'),
(9056, 'Sint-Niklaas', 1373, 'VLG', 22, 'BE', 51.16509000, 4.14370000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31521097'),
(9057, 'Sint-Pieters-Leeuw', 1373, 'VLG', 22, 'BE', 50.77926000, 4.24355000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31521115'),
(9058, 'Sint-Pieters-Voeren', 1373, 'VLG', 22, 'BE', 50.73863000, 5.82224000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31521115'),
(9059, 'Sint-Truiden', 1373, 'VLG', 22, 'BE', 50.81679000, 5.18647000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q15281225'),
(9060, 'Soignies', 1380, 'WAL', 22, 'BE', 50.57904000, 4.07129000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764852'),
(9061, 'Sombreffe', 1380, 'WAL', 22, 'BE', 50.52865000, 4.60087000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21764861'),
(9062, 'Somme-Leuze', 1380, 'WAL', 22, 'BE', 50.33699000, 5.36705000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763286'),
(9063, 'Soumagne', 1380, 'WAL', 22, 'BE', 50.61385000, 5.74679000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763297'),
(9064, 'Spa', 1380, 'WAL', 22, 'BE', 50.48375000, 5.86674000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763303'),
(9065, 'Sprimont', 1380, 'WAL', 22, 'BE', 50.50922000, 5.65950000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763331'),
(9066, 'Stabroek', 1373, 'VLG', 22, 'BE', 51.33189000, 4.37127000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763334'),
(9067, 'Staden', 1373, 'VLG', 22, 'BE', 50.97456000, 3.01469000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763337'),
(9068, 'Stavelot', 1380, 'WAL', 22, 'BE', 50.39500000, 5.93124000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31532444'),
(9069, 'Steenokkerzeel', 1373, 'VLG', 22, 'BE', 50.91851000, 4.50989000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31532875'),
(9070, 'Stekene', 1373, 'VLG', 22, 'BE', 51.20990000, 4.03651000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763382'),
(9071, 'Stoumont', 1380, 'WAL', 22, 'BE', 50.40667000, 5.80838000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31534739'),
(9072, 'Tellin', 1380, 'WAL', 22, 'BE', 50.08038000, 5.21638000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763428'),
(9073, 'Temse', 1373, 'VLG', 22, 'BE', 51.12794000, 4.21372000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31549581'),
(9074, 'Tenneville', 1380, 'WAL', 22, 'BE', 50.09501000, 5.52895000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q12376730'),
(9075, 'Terkoest', 1373, 'VLG', 22, 'BE', 50.89832000, 5.27623000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q12376730'),
(9076, 'Ternat', 1373, 'VLG', 22, 'BE', 50.86654000, 4.16682000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763440'),
(9077, 'Tervuren', 1373, 'VLG', 22, 'BE', 50.82372000, 4.51418000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31550469'),
(9078, 'Tessenderlo', 1373, 'VLG', 22, 'BE', 51.06513000, 5.08856000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763441'),
(9079, 'Theux', 1380, 'WAL', 22, 'BE', 50.53323000, 5.81245000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763446'),
(9080, 'Thuin', 1380, 'WAL', 22, 'BE', 50.33933000, 4.28604000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763461'),
(9081, 'Tielt', 1373, 'VLG', 22, 'BE', 50.99931000, 3.32707000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31555423'),
(9082, 'Tienen', 1373, 'VLG', 22, 'BE', 50.80745000, 4.93780000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31555441'),
(9083, 'Tinlot', 1380, 'WAL', 22, 'BE', 50.47493000, 5.37755000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763481'),
(9084, 'Tintigny', 1380, 'WAL', 22, 'BE', 49.68326000, 5.51349000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763483'),
(9085, 'Tongeren', 1373, 'VLG', 22, 'BE', 50.78054000, 5.46484000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31558934'),
(9086, 'Torhout', 1373, 'VLG', 22, 'BE', 51.06560000, 3.10085000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31559193'),
(9087, 'Tournai', 1380, 'WAL', 22, 'BE', 50.60715000, 3.38932000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q51462855'),
(9088, 'Tremelo', 1373, 'VLG', 22, 'BE', 50.99231000, 4.70807000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763503'),
(9089, 'Trois-Ponts', 1380, 'WAL', 22, 'BE', 50.37128000, 5.87146000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763511'),
(9090, 'Trooz', 1380, 'WAL', 22, 'BE', 50.57026000, 5.69521000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763520'),
(9091, 'Tubize', 1380, 'WAL', 22, 'BE', 50.69059000, 4.20090000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31562355'),
(9092, 'Turnhout', 1373, 'VLG', 22, 'BE', 51.32254000, 4.94471000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31565056'),
(9093, 'Vaux-sur-Sûre', 1380, 'WAL', 22, 'BE', 49.91100000, 5.57848000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21763552'),
(9094, 'Verlaine', 1380, 'WAL', 22, 'BE', 50.60743000, 5.31740000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21763570'),
(9095, 'Verviers', 1380, 'WAL', 22, 'BE', 50.58907000, 5.86241000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31571899'),
(9096, 'Veurne', 1373, 'VLG', 22, 'BE', 51.07316000, 2.66803000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31572147'),
(9097, 'Vielsalm', 1380, 'WAL', 22, 'BE', 50.28407000, 5.91502000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760040'),
(9098, 'Villers-la-Ville', 1380, 'WAL', 22, 'BE', 50.56667000, 4.51667000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31573389'),
(9099, 'Villers-le-Bouillet', 1380, 'WAL', 22, 'BE', 50.57708000, 5.25945000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760061'),
(9100, 'Vilvoorde', 1373, 'VLG', 22, 'BE', 50.92814000, 4.42938000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31573419'),
(9101, 'Virton', 1380, 'WAL', 22, 'BE', 49.56824000, 5.53259000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q51367625'),
(9102, 'Visé', 1380, 'WAL', 22, 'BE', 50.73760000, 5.69907000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21760086'),
(9103, 'Vorselaar', 1373, 'VLG', 22, 'BE', 51.20243000, 4.77259000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760123'),
(9104, 'Vosselaar', 1373, 'VLG', 22, 'BE', 51.30856000, 4.88960000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760126'),
(9105, 'Waarschoot', 1373, 'VLG', 22, 'BE', 51.15250000, 3.60500000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760149'),
(9106, 'Waasmunster', 1373, 'VLG', 22, 'BE', 51.10572000, 4.08573000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760151'),
(9107, 'Wachtebeke', 1373, 'VLG', 22, 'BE', 51.16852000, 3.87183000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760152'),
(9108, 'Waimes', 1380, 'WAL', 22, 'BE', 50.41488000, 6.11207000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760162'),
(9109, 'Walcourt', 1380, 'WAL', 22, 'BE', 50.25401000, 4.43796000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760163'),
(9110, 'Walhain-Saint-Paul', 1380, 'WAL', 22, 'BE', 50.62627000, 4.69837000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q3544632'),
(9111, 'Wanze', 1380, 'WAL', 22, 'BE', 50.53907000, 5.20846000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760185'),
(9112, 'Waregem', 1373, 'VLG', 22, 'BE', 50.88898000, 3.42756000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31578256'),
(9113, 'Waremme', 1380, 'WAL', 22, 'BE', 50.69760000, 5.25524000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q711474'),
(9114, 'Wasseiges', 1380, 'WAL', 22, 'BE', 50.62186000, 5.00528000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760198'),
(9115, 'Waterloo', 1380, 'WAL', 22, 'BE', 50.71469000, 4.39910000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31579578'),
(9116, 'Wavre', 1380, 'WAL', 22, 'BE', 50.71717000, 4.60138000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760204'),
(9117, 'Welkenraedt', 1380, 'WAL', 22, 'BE', 50.66050000, 5.97034000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760236'),
(9118, 'Wellen', 1373, 'VLG', 22, 'BE', 50.84096000, 5.33867000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760239'),
(9119, 'Wellin', 1380, 'WAL', 22, 'BE', 50.08133000, 5.11413000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760242'),
(9120, 'Wemmel', 1373, 'VLG', 22, 'BE', 50.90812000, 4.30613000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31581536'),
(9121, 'Wenduine', 1373, 'VLG', 22, 'BE', 51.29830000, 3.08213000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31581536'),
(9122, 'Wervik', 1373, 'VLG', 22, 'BE', 50.78069000, 3.03854000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760253'),
(9123, 'Westerlo', 1373, 'VLG', 22, 'BE', 51.09049000, 4.91544000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q26235476'),
(9124, 'Wetteren', 1373, 'VLG', 22, 'BE', 51.00526000, 3.88341000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31584348'),
(9125, 'Wevelgem', 1373, 'VLG', 22, 'BE', 50.80000000, 3.16667000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31584391'),
(9126, 'Wezembeek-Oppem', 1373, 'VLG', 22, 'BE', 50.83950000, 4.49427000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760265'),
(9127, 'Wichelen', 1373, 'VLG', 22, 'BE', 51.00526000, 3.97683000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760268'),
(9128, 'Wielsbeke', 1373, 'VLG', 22, 'BE', 50.90000000, 3.36667000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760270'),
(9129, 'Wijnegem', 1373, 'VLG', 22, 'BE', 51.22787000, 4.51895000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760276'),
(9130, 'Willebroek', 1373, 'VLG', 22, 'BE', 51.06041000, 4.36019000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31588325'),
(9131, 'Wingene', 1373, 'VLG', 22, 'BE', 51.05782000, 3.27359000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760299'),
(9132, 'Wommelgem', 1373, 'VLG', 22, 'BE', 51.20452000, 4.52250000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760331'),
(9133, 'Wuustwezel', 1373, 'VLG', 22, 'BE', 51.39214000, 4.59546000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760336'),
(9134, 'Yvoir', 1380, 'WAL', 22, 'BE', 50.32790000, 4.88059000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760343'),
(9135, 'Zandhoven', 1373, 'VLG', 22, 'BE', 51.21488000, 4.66164000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760351'),
(9136, 'Zaventem', 1373, 'VLG', 22, 'BE', 50.88365000, 4.47298000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596381'),
(9137, 'Zedelgem', 1373, 'VLG', 22, 'BE', 51.14236000, 3.13680000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596396'),
(9138, 'Zeebrugge', 1373, 'VLG', 22, 'BE', 51.32901000, 3.18188000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q184383'),
(9139, 'Zele', 1373, 'VLG', 22, 'BE', 51.06566000, 4.04030000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596471'),
(9140, 'Zelzate', 1373, 'VLG', 22, 'BE', 51.18963000, 3.80777000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760359'),
(9141, 'Zemst', 1373, 'VLG', 22, 'BE', 50.98318000, 4.46079000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596500'),
(9142, 'Zingem', 1373, 'VLG', 22, 'BE', 50.90409000, 3.65305000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760366'),
(9143, 'Zoersel', 1373, 'VLG', 22, 'BE', 51.26825000, 4.71296000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596621'),
(9144, 'Zomergem', 1373, 'VLG', 22, 'BE', 51.11994000, 3.56496000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596621'),
(9145, 'Zonhoven', 1373, 'VLG', 22, 'BE', 50.99064000, 5.36819000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596651'),
(9146, 'Zonnebeke', 1373, 'VLG', 22, 'BE', 50.87260000, 2.98725000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596665'),
(9147, 'Zottegem', 1373, 'VLG', 22, 'BE', 50.86955000, 3.81052000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596711'),
(9148, 'Zoutleeuw', 1373, 'VLG', 22, 'BE', 50.83316000, 5.10376000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760368'),
(9149, 'Zuienkerke', 1373, 'VLG', 22, 'BE', 51.26511000, 3.15506000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596742'),
(9150, 'Zulte', 1373, 'VLG', 22, 'BE', 50.91954000, 3.44859000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760371'),
(9151, 'Zutendaal', 1373, 'VLG', 22, 'BE', 50.93306000, 5.57530000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21760375'),
(9152, 'Zwevegem', 1373, 'VLG', 22, 'BE', 50.81268000, 3.33848000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q31596787'),
(9153, 'Zwijndrecht', 1373, 'VLG', 22, 'BE', 51.21979000, 4.32664000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q21756899'),
(9154, 'Écaussinnes-d’Enghien', 1380, 'WAL', 22, 'BE', 50.56822000, 4.16580000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q1910691'),
(9155, 'Éghezée', 1380, 'WAL', 22, 'BE', 50.59076000, 4.91175000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21768726'),
(9156, 'Érezée', 1380, 'WAL', 22, 'BE', 50.29292000, 5.55815000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21768770'),
(9157, 'Étalle', 1380, 'WAL', 22, 'BE', 49.67385000, 5.60019000, '2019-10-05 22:29:27', '2020-05-01 17:22:35', 1, 'Q21768795'),
(9158, 'Banfora', 3153, '02', 35, 'BF', 10.63333000, -4.76667000, '2019-10-05 22:29:27', '2019-10-05 22:29:27', 1, 'Q639674'),
(9159, 'Barani', 3138, '01', 35, 'BF', 13.16910000, -3.88990000, '2019-10-05 22:29:28', '2019-10-05 22:29:28', 1, 'Q639674'),
(9160, 'Batié', 3140, '13', 35, 'BF', 9.88333000, -2.91667000, '2019-10-05 22:29:28', '2020-05-01 17:22:39', 1, 'Q639674'),
(9161, 'Bazega Province', 3149, '07', 35, 'BF', 11.91667000, -1.50000000, '2019-10-05 22:29:28', '2019-10-05 22:29:28', 1, 'Q619764'),
(9162, 'Bobo-Dioulasso', 3165, '09', 35, 'BF', 11.17715000, -4.29790000, '2019-10-05 22:29:28', '2019-10-05 22:29:28', 1, 'Q223761'),
(9163, 'Bogandé', 3158, '08', 35, 'BF', 12.97040000, -0.14953000, '2019-10-05 22:29:28', '2020-05-01 17:22:39', 1, 'Q890373'),
(9164, 'Boromo', 3138, '01', 35, 'BF', 11.74542000, -2.93006000, '2019-10-05 22:29:28', '2019-10-05 22:29:28', 1, 'Q894055'),
(9165, 'Boulsa', 3127, '05', 35, 'BF', 12.66664000, -0.57469000, '2019-10-05 22:29:28', '2019-10-05 22:29:28', 1, 'Q895110');

