INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(36643, 'Roa', 1146, 'B<PERSON>', 207, 'ES', 41.69699000, -3.92782000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q430454'),
(36644, 'R<PERSON><PERSON>', 1161, 'ZA', 207, 'ES', 41.55170000, -5.77211000, '2019-10-05 22:46:08', '2022-08-29 11:48:04', 1, 'Q1646390'),
(36645, '<PERSON><PERSON><PERSON>', 1183, 'VA', 207, 'ES', 41.60838000, -4.90992000, '2019-10-05 22:46:08', '2022-08-29 11:48:45', 1, 'Q1907020'),
(36646, '<PERSON>led<PERSON>', 1147, '<PERSON>', 207, 'ES', 40.38469000, -6.60726000, '2019-10-05 22:46:08', '2022-08-29 11:44:51', 1, 'Q1646395'),
(36647, '<PERSON>leda-Ce<PERSON><PERSON>', 1161, 'ZA', 207, 'ES', 42.08246000, -6.59414000, '2019-10-05 22:46:08', '2022-08-29 11:48:04', 1, 'Q1752418'),
(36648, '<PERSON>led<PERSON> de Gata', 1190, '<PERSON>', 207, 'ES', 40.32234000, -6.47129000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q1120025'),
(36649, 'Robledillo de Mohernando', 5107, 'GU', 207, 'ES', 40.85118000, -3.23162000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1655656'),
(36650, 'Robledillo de Trujillo', 1190, 'CC', 207, 'ES', 39.26956000, -5.98001000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q976530'),
(36651, 'Robledillo de la Jara', 1158, 'M', 207, 'ES', 40.95054000, -3.52181000, '2019-10-05 22:46:08', '2022-08-29 12:04:40', 1, 'Q1980536'),
(36652, 'Robledillo de la Vera', 1190, 'CC', 207, 'ES', 40.10093000, -5.58896000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q1642754'),
(36654, 'Robledo de Chavela', 1158, 'M', 207, 'ES', 40.50062000, -4.23635000, '2019-10-05 22:46:08', '2022-08-29 12:04:40', 1, 'Q24012696'),
(36655, 'Robledo de Corpes', 5107, 'GU', 207, 'ES', 41.11838000, -2.95000000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1655883'),
(36656, 'Robledollano', 1190, 'CC', 207, 'ES', 39.60962000, -5.50855000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q730201'),
(36657, 'Robliza de Cojos', 1147, 'SA', 207, 'ES', 40.86766000, -5.97786000, '2019-10-05 22:46:08', '2022-08-29 11:44:51', 1, 'Q1767853'),
(36658, 'Robres', 1177, 'HU', 207, 'ES', 41.86746000, -0.46094000, '2019-10-05 22:46:08', '2022-08-29 12:06:20', 1, 'Q987412'),
(36659, 'Robres del Castillo', 1171, 'LO', 207, 'ES', 42.27593000, -2.29245000, '2019-10-05 22:46:08', '2022-08-29 12:05:09', 1, 'Q1636274'),
(36660, 'Rocafort', 1175, 'V', 207, 'ES', 39.53333000, -0.40000000, '2019-10-05 22:46:08', '2022-08-29 12:05:40', 1, 'Q1636274'),
(36661, 'Roda de Barà', 1203, 'T', 207, 'ES', 41.18645000, 1.45893000, '2019-10-05 22:46:08', '2022-08-29 10:57:33', 1, 'Q1636274'),
(36662, 'Roda de Eresma', 1192, 'SG', 207, 'ES', 41.02852000, -4.18130000, '2019-10-05 22:46:08', '2022-08-29 11:50:42', 1, 'Q1939023'),
(36663, 'Rodeiro', 1167, 'PO', 207, 'ES', 42.65193000, -7.95519000, '2019-10-05 22:46:08', '2022-08-28 17:57:54', 1, 'Q1939023'),
(36664, 'Rodezno', 1171, 'LO', 207, 'ES', 42.52576000, -2.84597000, '2019-10-05 22:46:08', '2022-08-29 12:05:09', 1, 'Q1645942'),
(36665, 'Rojales', 5108, 'A', 207, 'ES', 38.08799000, -0.72544000, '2019-10-05 22:46:08', '2022-08-29 11:15:47', 1, 'Q606240'),
(36666, 'Rojas', 1146, 'BU', 207, 'ES', 42.57781000, -3.44195000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1994190'),
(36667, 'Rollamienta', 1208, 'SO', 207, 'ES', 41.92592000, -2.53125000, '2019-10-05 22:46:08', '2022-08-29 11:51:23', 1, 'Q832706'),
(36668, 'Rollán', 1147, 'SA', 207, 'ES', 40.96230000, -5.91753000, '2019-10-05 22:46:08', '2022-08-29 11:44:51', 1, 'Q612729'),
(36669, 'Romangordo', 1190, 'CC', 207, 'ES', 39.74199000, -5.70081000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q1642781'),
(36670, 'Romanillos de Atienza', 5107, 'GU', 207, 'ES', 41.26667000, -2.90000000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1641447'),
(36671, 'Romanones', 5107, 'GU', 207, 'ES', 40.57149000, -2.99072000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1913812'),
(36672, 'Romanos', 5113, 'Z', 207, 'ES', 41.12636000, -1.27502000, '2019-10-05 22:46:08', '2022-08-29 11:42:54', 1, 'Q23993110'),
(36673, 'Ronda', 5101, 'MA', 207, 'ES', 36.74231000, -5.16709000, '2019-10-05 22:46:08', '2022-08-28 19:06:53', 1, 'Q23993110'),
(36674, 'Roperuelos del Páramo', 1200, 'LE', 207, 'ES', 42.23780000, -5.78235000, '2019-10-05 22:46:08', '2020-05-01 17:23:17', 1, 'Q23993110'),
(36675, 'Roquetas de Mar', 5095, 'AL', 207, 'ES', 36.76419000, -2.61475000, '2019-10-05 22:46:08', '2022-08-28 18:41:41', 1, 'Q499184'),
(36676, 'Rosal de la Frontera', 5099, 'H', 207, 'ES', 37.96754000, -7.21889000, '2019-10-05 22:46:08', '2022-08-28 19:00:43', 1, 'Q537499'),
(36677, 'Rosalejo', 1190, 'CC', 207, 'ES', 39.43660000, -4.90821000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q23979863'),
(36678, 'Rosell', 1175, 'V', 207, 'ES', 40.61792000, 0.22133000, '2019-10-05 22:46:08', '2022-08-29 12:05:40', 1, 'Q37416935'),
(36679, 'Roses', 5103, 'GI', 207, 'ES', 42.26199000, 3.17689000, '2019-10-05 22:46:08', '2022-08-29 10:53:16', 1, 'Q37416935'),
(36680, 'Rota', 5096, 'CA', 207, 'ES', 36.62545000, -6.36220000, '2019-10-05 22:46:08', '2022-08-28 18:44:29', 1, 'Q15907'),
(36681, 'Rotglá y Corbera', 1175, 'V', 207, 'ES', 39.00465000, -0.56482000, '2019-10-05 22:46:08', '2022-08-29 12:05:40', 1, 'Q23979871'),
(36682, 'Roturas', 1183, 'VA', 207, 'ES', 41.66791000, -4.11901000, '2019-10-05 22:46:08', '2022-08-29 11:48:45', 1, 'Q1651674'),
(36683, 'Royuela', 5111, 'TE', 207, 'ES', 40.37846000, -1.51337000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q1650795'),
(36684, 'Royuela de Río Franco', 1146, 'BU', 207, 'ES', 42.00217000, -3.95547000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1650795'),
(36685, 'Rozalén del Monte', 5106, 'CU', 207, 'ES', 39.99083000, -2.80525000, '2019-10-05 22:46:08', '2022-08-29 11:05:01', 1, 'Q1640458'),
(36686, 'Rozas de Puerto Real', 1158, 'M', 207, 'ES', 40.31667000, -4.48333000, '2019-10-05 22:46:08', '2022-08-29 12:04:40', 1, 'Q770499'),
(36687, 'Ruanes', 1190, 'CC', 207, 'ES', 39.32814000, -6.01347000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q1642766'),
(36688, 'Rubena', 1146, 'BU', 207, 'ES', 42.38746000, -3.57485000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1630697'),
(36689, 'Rubiales', 5111, 'TE', 207, 'ES', 40.27623000, -1.27167000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q1630697'),
(36690, 'Rubielos de Mora', 5111, 'TE', 207, 'ES', 40.18894000, -0.65307000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q1552349'),
(36691, 'Rubielos de la Cérida', 5111, 'TE', 207, 'ES', 40.77058000, -1.21291000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q1552349'),
(36692, 'Rubite', 5098, 'GR', 207, 'ES', 36.80947000, -3.34816000, '2019-10-05 22:46:08', '2022-08-28 18:52:58', 1, 'Q615061'),
(36693, 'Rublacedo de Abajo', 1146, 'BU', 207, 'ES', 42.55332000, -3.50236000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1642487'),
(36694, 'Rubí', 5102, 'B', 207, 'ES', 41.49226000, 2.03305000, '2019-10-05 22:46:08', '2022-08-29 10:50:01', 1, 'Q1642487'),
(36695, 'Rubí de Bracamonte', 1183, 'VA', 207, 'ES', 41.21421000, -4.92491000, '2019-10-05 22:46:08', '2022-08-29 11:48:45', 1, 'Q1913807'),
(36696, 'Rucandio', 1146, 'BU', 207, 'ES', 42.75111000, -3.54166000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1767883'),
(36697, 'Rueda', 1183, 'VA', 207, 'ES', 41.41231000, -4.95885000, '2019-10-05 22:46:08', '2022-08-29 11:48:45', 1, 'Q987366'),
(36698, 'Rueda de la Sierra', 5107, 'GU', 207, 'ES', 40.91795000, -1.85439000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1655596'),
(36699, 'Ruente', 1170, 'S', 207, 'ES', 43.25826000, -4.26791000, '2019-10-05 22:46:08', '2019-10-05 22:46:08', 1, 'Q1655596'),
(36700, 'Ruesca', 5113, 'Z', 207, 'ES', 41.28350000, -1.48142000, '2019-10-05 22:46:08', '2022-08-29 11:42:54', 1, 'Q1640959'),
(36701, 'Ruesga', 1200, 'LE', 207, 'ES', 42.86413000, -4.52942000, '2019-10-05 22:46:08', '2019-10-05 22:46:08', 1, 'Q1640959'),
(36702, 'Rugat', 1175, 'V', 207, 'ES', 38.87933000, -0.36115000, '2019-10-05 22:46:08', '2022-08-29 12:05:40', 1, 'Q1313164'),
(36703, 'Ruidera', 5105, 'CR', 207, 'ES', 38.97775000, -2.88321000, '2019-10-05 22:46:08', '2022-08-29 11:03:25', 1, 'Q1657922'),
(36704, 'Rupià', 5103, 'GI', 207, 'ES', 42.01667000, 3.01667000, '2019-10-05 22:46:08', '2022-08-29 10:53:16', 1, 'Q13520'),
(36705, 'Rus', 5100, 'J', 207, 'ES', 38.04759000, -3.46254000, '2019-10-05 22:46:08', '2022-08-28 19:04:30', 1, 'Q13520'),
(36706, 'Rute', 5097, 'CO', 207, 'ES', 37.32690000, -4.36827000, '2019-10-05 22:46:08', '2022-08-28 18:49:38', 1, 'Q592982'),
(36707, 'Rábade', 5090, 'LU', 207, 'ES', 43.11700000, -7.61714000, '2019-10-05 22:46:08', '2022-08-28 17:49:36', 1, 'Q592982'),
(36708, 'Rábano', 1183, 'VA', 207, 'ES', 41.53314000, -4.06214000, '2019-10-05 22:46:08', '2022-08-29 11:48:45', 1, 'Q1923980'),
(36709, 'Rábano de Aliste', 1161, 'ZA', 207, 'ES', 41.74430000, -6.43302000, '2019-10-05 22:46:08', '2022-08-29 11:48:04', 1, 'Q1922156'),
(36710, 'Rábanos', 1146, 'BU', 207, 'ES', 42.31971000, -3.27029000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1751241'),
(36711, 'Ráfales', 5111, 'TE', 207, 'ES', 40.83770000, 0.01923000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q1651261'),
(36712, 'Ráfol de Salem', 1175, 'V', 207, 'ES', 38.86651000, -0.39991000, '2019-10-05 22:46:08', '2022-08-29 12:05:40', 1, 'Q2046233'),
(36713, 'Rágama', 1147, 'SA', 207, 'ES', 40.99768000, -5.12724000, '2019-10-05 22:46:08', '2022-08-29 11:44:51', 1, 'Q1769997'),
(36714, 'Rágol', 5095, 'AL', 207, 'ES', 36.99523000, -2.68178000, '2019-10-05 22:46:08', '2022-08-28 18:41:41', 1, 'Q1769997'),
(36715, 'Ríofrío de Aliste', 1161, 'ZA', 207, 'ES', 41.81724000, -6.17741000, '2019-10-05 22:46:08', '2022-08-29 11:48:04', 1, 'Q1653184'),
(36716, 'Ríogordo', 5101, 'MA', 207, 'ES', 36.91727000, -4.29318000, '2019-10-05 22:46:08', '2022-08-28 19:06:53', 1, 'Q1630306'),
(36717, 'Ríolobos', 1190, 'CC', 207, 'ES', 39.92074000, -6.30397000, '2019-10-05 22:46:08', '2022-08-28 18:12:22', 1, 'Q1613493'),
(36718, 'Ríotorto', 5090, 'LU', 207, 'ES', 43.35000000, -7.23333000, '2019-10-05 22:46:08', '2022-08-28 17:49:36', 1, 'Q23993044'),
(36719, 'Ródenas', 5111, 'TE', 207, 'ES', 40.64076000, -1.51617000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q55473125'),
(36720, 'Rótova', 1175, 'V', 207, 'ES', 38.93205000, -0.25765000, '2019-10-05 22:46:08', '2022-08-29 12:05:40', 1, 'Q55473125'),
(36722, 'Sabadell', 5102, 'B', 207, 'ES', 41.54329000, 2.10942000, '2019-10-05 22:46:08', '2022-08-29 10:50:01', 1, 'Q3920538'),
(36723, 'Sabero', 1200, 'LE', 207, 'ES', 42.83593000, -5.14875000, '2019-10-05 22:46:08', '2019-10-05 22:46:08', 1, 'Q1615381'),
(36724, 'Sabiote', 5100, 'J', 207, 'ES', 38.06916000, -3.31448000, '2019-10-05 22:46:08', '2022-08-28 19:04:30', 1, 'Q1641690'),
(36725, 'Sabiñánigo', 1177, 'HU', 207, 'ES', 42.51924000, -0.36607000, '2019-10-05 22:46:08', '2022-08-29 12:06:20', 1, 'Q24013331'),
(36726, 'Sacañet', 5110, 'CS', 207, 'ES', 39.86667000, -0.71667000, '2019-10-05 22:46:08', '2022-08-29 11:26:42', 1, 'Q1645715'),
(36727, 'Sacecorbo', 5107, 'GU', 207, 'ES', 40.83280000, -2.41838000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q764816'),
(36728, 'Saceda-Trasierra', 5106, 'CU', 207, 'ES', 40.15525000, -2.85369000, '2019-10-05 22:46:08', '2022-08-29 11:05:01', 1, 'Q1648566'),
(36729, 'Sacedón', 5107, 'GU', 207, 'ES', 40.48076000, -2.73337000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1641334'),
(36730, 'Saceruela', 5105, 'CR', 207, 'ES', 38.94382000, -4.60768000, '2019-10-05 22:46:08', '2022-08-29 11:03:25', 1, 'Q1657948'),
(36731, 'Sacramenia', 1192, 'SG', 207, 'ES', 41.49419000, -3.96210000, '2019-10-05 22:46:08', '2022-08-29 11:50:42', 1, 'Q1939060'),
(36732, 'Sada', 5089, 'C', 207, 'ES', 43.35619000, -8.25796000, '2019-10-05 22:46:08', '2022-08-28 13:37:17', 1, 'Q1616081'),
(36733, 'Saelices', 5106, 'CU', 207, 'ES', 39.92061000, -2.80502000, '2019-10-05 22:46:08', '2022-08-29 11:05:01', 1, 'Q1616081'),
(36734, 'Saelices de Mayorga', 1183, 'VA', 207, 'ES', 42.21206000, -5.20534000, '2019-10-05 22:46:08', '2022-08-29 11:48:45', 1, 'Q1919601'),
(36735, 'Saelices de la Sal', 5107, 'GU', 207, 'ES', 40.90723000, -2.32325000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1656248'),
(36736, 'Saelices el Chico', 1147, 'SA', 207, 'ES', 40.66998000, -6.63271000, '2019-10-05 22:46:08', '2022-08-29 11:44:52', 1, 'Q1766606'),
(36737, 'Sagra', 5108, 'A', 207, 'ES', 38.81102000, -0.06559000, '2019-10-05 22:46:08', '2022-08-29 11:15:47', 1, 'Q1766606'),
(36739, 'Sagunto', 1175, 'V', 207, 'ES', 39.68333000, -0.26667000, '2019-10-05 22:46:08', '2022-08-29 12:05:40', 1, 'Q47483'),
(36740, 'Sagàs', 5102, 'B', 207, 'ES', 42.05000000, 1.96667000, '2019-10-05 22:46:08', '2022-08-29 10:50:01', 1, 'Q47483'),
(36741, 'Sahagún', 1200, 'LE', 207, 'ES', 42.37085000, -5.02942000, '2019-10-05 22:46:08', '2020-05-01 17:23:17', 1, 'Q47483'),
(36742, 'Sahún', 1177, 'HU', 207, 'ES', 42.57590000, 0.46546000, '2019-10-05 22:46:08', '2022-08-29 12:06:20', 1, 'Q477007'),
(36743, 'Sajazarra', 1171, 'LO', 207, 'ES', 42.58884000, -2.96124000, '2019-10-05 22:46:08', '2022-08-29 12:05:09', 1, 'Q577585'),
(36744, 'Salamanca', 1147, 'SA', 207, 'ES', 40.96882000, -5.66388000, '2019-10-05 22:46:08', '2022-08-29 11:44:52', 1, 'Q577585'),
(36746, 'Salar', 5098, 'GR', 207, 'ES', 37.15036000, -4.06576000, '2019-10-05 22:46:08', '2022-08-28 18:52:58', 1, 'Q1773521'),
(36747, 'Salares', 5101, 'MA', 207, 'ES', 36.85463000, -4.02434000, '2019-10-05 22:46:08', '2022-08-28 19:06:53', 1, 'Q1647747'),
(36748, 'Salas Altas', 1177, 'HU', 207, 'ES', 42.11407000, 0.06821000, '2019-10-05 22:46:08', '2022-08-29 12:06:20', 1, 'Q1651354'),
(36749, 'Salas Bajas', 1177, 'HU', 207, 'ES', 42.10046000, 0.08349000, '2019-10-05 22:46:08', '2022-08-29 12:06:20', 1, 'Q1651328'),
(36750, 'Salas de Bureba', 1146, 'BU', 207, 'ES', 42.69190000, -3.47359000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1751267'),
(36751, 'Salas de los Infantes', 1146, 'BU', 207, 'ES', 42.02242000, -3.28631000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q378299'),
(36752, 'Salce', 1161, 'ZA', 207, 'ES', 41.26996000, -6.21875000, '2019-10-05 22:46:08', '2022-08-29 11:48:04', 1, 'Q1766821'),
(36753, 'Salcedillo', 5111, 'TE', 207, 'ES', 40.96200000, -1.00433000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q1766821'),
(36754, 'Saldaña', 1157, 'P', 207, 'ES', 42.52146000, -4.73605000, '2019-10-05 22:46:08', '2022-08-29 11:45:45', 1, 'Q64447517'),
(36755, 'Saldaña de Burgos', 1146, 'BU', 207, 'ES', 42.25838000, -3.69707000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1641344'),
(36756, 'Saldeana', 1147, 'SA', 207, 'ES', 41.02068000, -6.64015000, '2019-10-05 22:46:08', '2022-08-29 11:44:52', 1, 'Q1904304'),
(36757, 'Salduero', 1208, 'SO', 207, 'ES', 41.88946000, -2.79670000, '2019-10-05 22:46:08', '2022-08-29 11:51:23', 1, 'Q832188'),
(36758, 'Saldías', 1204, 'NA', 207, 'ES', 43.08858000, -1.77946000, '2019-10-05 22:46:08', '2022-08-29 12:06:07', 1, 'Q1648100'),
(36759, 'Saldón', 5111, 'TE', 207, 'ES', 40.32513000, -1.42782000, '2019-10-05 22:46:08', '2022-08-29 11:29:44', 1, 'Q1651152'),
(36760, 'Sales de Llierca', 5103, 'GI', 207, 'ES', 42.23333000, 2.65000000, '2019-10-05 22:46:08', '2022-08-29 10:53:16', 1, 'Q12643'),
(36761, 'Salillas', 1177, 'HU', 207, 'ES', 41.99498000, -0.22278000, '2019-10-05 22:46:08', '2022-08-29 12:06:20', 1, 'Q1766682'),
(36762, 'Salillas de Jalón', 5113, 'Z', 207, 'ES', 41.56789000, -1.32344000, '2019-10-05 22:46:08', '2022-08-29 11:42:54', 1, 'Q1640997'),
(36763, 'Salinas', 5108, 'A', 207, 'ES', 38.52025000, -0.91202000, '2019-10-05 22:46:08', '2022-08-29 11:15:47', 1, 'Q1640997'),
(36764, 'Salinas de Oro', 1204, 'NA', 207, 'ES', 42.77487000, -1.88999000, '2019-10-05 22:46:08', '2022-08-29 12:06:07', 1, 'Q1648042'),
(36765, 'Salinas de Pisuerga', 1157, 'P', 207, 'ES', 42.85046000, -4.37783000, '2019-10-05 22:46:08', '2022-08-29 11:45:45', 1, 'Q1918470'),
(36766, 'Salinas del Manzano', 5106, 'CU', 207, 'ES', 40.08333000, -1.55000000, '2019-10-05 22:46:08', '2022-08-29 11:05:01', 1, 'Q949890'),
(36767, 'Salinillas de Bureba', 1146, 'BU', 207, 'ES', 42.55243000, -3.38753000, '2019-10-05 22:46:08', '2022-08-29 11:24:20', 1, 'Q1641323'),
(36768, 'Sallent', 5102, 'B', 207, 'ES', 41.82602000, 1.89550000, '2019-10-05 22:46:08', '2022-08-29 10:50:01', 1, 'Q1641323'),
(36769, 'Sallent de Gállego', 1177, 'HU', 207, 'ES', 42.77127000, -0.33448000, '2019-10-05 22:46:08', '2022-08-29 12:06:20', 1, 'Q1648397'),
(36770, 'Salmerón', 5107, 'GU', 207, 'ES', 40.54529000, -2.49315000, '2019-10-05 22:46:08', '2022-08-29 11:06:45', 1, 'Q1766745'),
(36771, 'Salmoral', 1147, 'SA', 207, 'ES', 40.80137000, -5.21910000, '2019-10-05 22:46:08', '2022-08-29 11:44:52', 1, 'Q1766739'),
(36772, 'Salobral', 1189, 'AV', 207, 'ES', 40.61179000, -4.81013000, '2019-10-05 22:46:08', '2022-08-29 11:49:57', 1, 'Q1630524'),
(36774, 'Salobreña', 5098, 'GR', 207, 'ES', 36.74277000, -3.58717000, '2019-10-05 22:46:08', '2022-08-28 18:52:58', 1, 'Q427330'),
(36775, 'Salomó', 1203, 'T', 207, 'ES', 41.22955000, 1.37445000, '2019-10-05 22:46:09', '2022-08-29 10:57:33', 1, 'Q427330'),
(36776, 'Salorino', 1190, 'CC', 207, 'ES', 39.48018000, -7.00758000, '2019-10-05 22:46:09', '2022-08-28 18:12:22', 1, 'Q2572619'),
(36777, 'Salou', 1203, 'T', 207, 'ES', 41.07663000, 1.14163000, '2019-10-05 22:46:09', '2022-08-29 10:57:33', 1, 'Q662789'),
(36778, 'Salt', 5103, 'GI', 207, 'ES', 41.97489000, 2.79281000, '2019-10-05 22:46:09', '2022-08-29 10:53:16', 1, 'Q662789'),
(36779, 'Salteras', 1193, 'SE', 207, 'ES', 37.42060000, -6.11049000, '2019-10-05 22:46:09', '2022-08-28 19:08:49', 1, 'Q1629807'),
(36780, 'Salvacañete', 5106, 'CU', 207, 'ES', 40.10000000, -1.50000000, '2019-10-05 22:46:09', '2022-08-29 11:05:01', 1, 'Q1752006'),
(36781, 'Salvadiós', 1189, 'AV', 207, 'ES', 40.87836000, -5.09629000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1607985'),
(36782, 'Salvador de Zapardiel', 1183, 'VA', 207, 'ES', 41.11696000, -4.87486000, '2019-10-05 22:46:09', '2022-08-29 11:48:45', 1, 'Q1916502'),
(36783, 'Salvaleón', 5092, 'BA', 207, 'ES', 38.51020000, -6.78627000, '2019-10-05 22:46:09', '2022-08-28 18:09:23', 1, 'Q1443665'),
(36784, 'Salvatierra de Esca', 5113, 'Z', 207, 'ES', 42.67002000, -1.00475000, '2019-10-05 22:46:09', '2022-08-29 11:42:54', 1, 'Q1640389'),
(36786, 'Salvatierra de Santiago', 1190, 'CC', 207, 'ES', 39.30363000, -6.03206000, '2019-10-05 22:46:09', '2022-08-28 18:12:22', 1, 'Q1658566'),
(36787, 'Salvatierra de Tormes', 1147, 'SA', 207, 'ES', 40.59007000, -5.59763000, '2019-10-05 22:46:09', '2022-08-29 11:44:52', 1, 'Q1766309'),
(36788, 'Salvatierra de los Barros', 5092, 'BA', 207, 'ES', 38.49091000, -6.68423000, '2019-10-05 22:46:09', '2022-08-28 18:09:23', 1, 'Q1443754'),
(36789, 'Samaniego', 5093, 'VI', 207, 'ES', 42.56854000, -2.67974000, '2019-10-05 22:46:09', '2022-08-28 18:23:49', 1, 'Q943857'),
(36790, 'Samboal', 1192, 'SG', 207, 'ES', 41.25758000, -4.41727000, '2019-10-05 22:46:09', '2022-08-29 11:50:42', 1, 'Q605824'),
(36791, 'Samir de los Caños', 1161, 'ZA', 207, 'ES', 41.67280000, -6.16415000, '2019-10-05 22:46:09', '2022-08-29 11:48:04', 1, 'Q1766862'),
(36792, 'Samos', 5090, 'LU', 207, 'ES', 42.73100000, -7.32582000, '2019-10-05 22:46:09', '2022-08-28 17:49:36', 1, 'Q917876'),
(36793, 'Samper de Calanda', 5111, 'TE', 207, 'ES', 41.18933000, -0.38883000, '2019-10-05 22:46:09', '2022-08-29 11:29:44', 1, 'Q1641141'),
(36794, 'Samper del Salz', 5113, 'Z', 207, 'ES', 41.23458000, -0.82510000, '2019-10-05 22:46:09', '2022-08-29 11:42:54', 1, 'Q1640147'),
(36795, 'San Adrián', 1204, 'NA', 207, 'ES', 42.33433000, -1.93509000, '2019-10-05 22:46:09', '2022-08-29 12:06:07', 1, 'Q1640147'),
(36796, 'San Adrián de Juarros', 1146, 'BU', 207, 'ES', 42.27427000, -3.47551000, '2019-10-05 22:46:09', '2022-08-29 11:24:20', 1, 'Q1641372'),
(36797, 'San Adrián del Valle', 1200, 'LE', 207, 'ES', 42.13033000, -5.72905000, '2019-10-05 22:46:09', '2020-05-01 17:23:17', 1, 'Q1635334'),
(36798, 'San Agustín', 5111, 'TE', 207, 'ES', 40.05929000, -0.69254000, '2019-10-05 22:46:09', '2022-08-29 11:29:44', 1, 'Q1635334'),
(36799, 'San Agustín del Guadalix', 1158, 'M', 207, 'ES', 40.67882000, -3.61639000, '2019-10-05 22:46:09', '2022-08-29 12:04:40', 1, 'Q985045'),
(36800, 'San Agustín del Pozo', 1161, 'ZA', 207, 'ES', 41.88613000, -5.59351000, '2019-10-05 22:46:09', '2022-08-29 11:48:04', 1, 'Q1766839'),
(36801, 'San Amaro', 5091, 'OR', 207, 'ES', 42.37385000, -8.07347000, '2019-10-05 22:46:09', '2022-08-28 17:53:26', 1, 'Q1766839'),
(36802, 'San Andrés del Congosto', 5107, 'GU', 207, 'ES', 40.99775000, -3.02423000, '2019-10-05 22:46:09', '2022-08-29 11:06:45', 1, 'Q1655370'),
(36803, 'San Andrés del Rabanedo', 1200, 'LE', 207, 'ES', 42.61174000, -5.61671000, '2019-10-05 22:46:09', '2020-05-01 17:23:17', 1, 'Q1615486'),
(36804, 'San Andrés del Rey', 5107, 'GU', 207, 'ES', 40.63837000, -2.82020000, '2019-10-05 22:46:09', '2022-08-29 11:06:45', 1, 'Q1655636'),
(36805, 'San Asensio', 1171, 'LO', 207, 'ES', 42.49677000, -2.75057000, '2019-10-05 22:46:09', '2022-08-29 12:05:09', 1, 'Q1636238'),
(36806, 'San Bartolomé', 1185, 'GC', 207, 'ES', 29.00093000, -13.61300000, '2019-10-05 22:46:09', '2022-08-29 12:06:32', 1, 'Q1636238'),
(36807, 'San Bartolomé de Béjar', 1189, 'AV', 207, 'ES', 40.40783000, -5.66261000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1606329'),
(36808, 'San Bartolomé de Corneja', 1189, 'AV', 207, 'ES', 40.49257000, -5.38519000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1607832'),
(36809, 'San Bartolomé de Tirajana', 1185, 'GC', 207, 'ES', 27.92481000, -15.57329000, '2019-10-05 22:46:09', '2022-08-29 12:06:32', 1, 'Q693258'),
(36810, 'San Bartolomé de la Torre', 5099, 'H', 207, 'ES', 37.44515000, -7.10597000, '2019-10-05 22:46:09', '2022-08-28 19:00:43', 1, 'Q1614531'),
(36811, 'San Bartolomé de las Abiertas', 1205, 'TO', 207, 'ES', 39.82972000, -4.71901000, '2019-10-05 22:46:09', '2022-08-29 11:08:30', 1, 'Q1641133'),
(36812, 'San Blas-Canillejas', 1158, 'M', 207, 'ES', 40.43893000, -3.61537000, '2019-10-05 22:46:09', '2022-08-29 12:04:40', 1, 'Q2001937'),
(36813, 'San Carlos del Valle', 5105, 'CR', 207, 'ES', 38.84399000, -3.24148000, '2019-10-05 22:46:09', '2022-08-29 11:03:25', 1, 'Q1628769'),
(36814, 'San Cebrián de Campos', 1157, 'P', 207, 'ES', 42.20063000, -4.53127000, '2019-10-05 22:46:09', '2022-08-29 11:45:45', 1, 'Q24012135'),
(36815, 'San Cebrián de Castro', 1161, 'ZA', 207, 'ES', 41.70632000, -5.75605000, '2019-10-05 22:46:09', '2022-08-29 11:48:04', 1, 'Q1766853'),
(36816, 'San Cebrián de Mazote', 1183, 'VA', 207, 'ES', 41.68022000, -5.14847000, '2019-10-05 22:46:09', '2022-08-29 11:48:45', 1, 'Q1919492'),
(36817, 'San Cebrián de Mudá', 1157, 'P', 207, 'ES', 42.89261000, -4.38719000, '2019-10-05 22:46:09', '2022-08-29 11:45:45', 1, 'Q1995261'),
(36818, 'San Clemente', 5106, 'CU', 207, 'ES', 39.40410000, -2.42819000, '2019-10-05 22:46:09', '2022-08-29 11:05:01', 1, 'Q1995261'),
(36819, 'San Cristóbal de Boedo', 1157, 'P', 207, 'ES', 42.54194000, -4.35325000, '2019-10-05 22:46:09', '2022-08-29 11:45:45', 1, 'Q1906833'),
(36820, 'San Cristóbal de Cuéllar', 1192, 'SG', 207, 'ES', 41.40591000, -4.40445000, '2019-10-05 22:46:09', '2022-08-29 11:50:42', 1, 'Q1938529'),
(36821, 'San Cristóbal de Entreviñas', 1161, 'ZA', 207, 'ES', 42.04575000, -5.63468000, '2019-10-05 22:46:09', '2022-08-29 11:48:04', 1, 'Q1653158'),
(36822, 'San Cristóbal de Segovia', 1192, 'SG', 207, 'ES', 40.95225000, -4.07652000, '2019-10-05 22:46:09', '2022-08-29 11:50:43', 1, 'Q2047578'),
(36823, 'San Cristóbal de la Cuesta', 1147, 'SA', 207, 'ES', 41.02919000, -5.61771000, '2019-10-05 22:46:09', '2022-08-29 11:44:52', 1, 'Q1766229'),
(36824, 'San Cristóbal de la Polantera', 1200, 'LE', 207, 'ES', 42.39041000, -5.90732000, '2019-10-05 22:46:09', '2020-05-01 17:23:17', 1, 'Q61021'),
(36825, 'San Cristóbal de la Vega', 1192, 'SG', 207, 'ES', 41.11214000, -4.64448000, '2019-10-05 22:46:09', '2022-08-29 11:50:42', 1, 'Q581687'),
(36826, 'San Emiliano', 1200, 'LE', 207, 'ES', 42.97164000, -6.00075000, '2019-10-05 22:46:09', '2019-10-05 22:46:09', 1, 'Q141211'),
(36828, 'San Esteban de Gormaz', 1208, 'SO', 207, 'ES', 41.57436000, -3.20418000, '2019-10-05 22:46:09', '2022-08-29 11:51:23', 1, 'Q487352'),
(36829, 'San Esteban de Nogales', 1200, 'LE', 207, 'ES', 42.15995000, -5.93065000, '2019-10-05 22:46:09', '2019-10-05 22:46:09', 1, 'Q141191'),
(36830, 'San Esteban de la Sierra', 1147, 'SA', 207, 'ES', 40.50680000, -5.90619000, '2019-10-05 22:46:09', '2022-08-29 11:44:52', 1, 'Q1755939'),
(36831, 'San Esteban de los Patos', 1189, 'AV', 207, 'ES', 40.74705000, -4.62413000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1607891'),
(36832, 'San Esteban del Molar', 1161, 'ZA', 207, 'ES', 41.93717000, -5.55158000, '2019-10-05 22:46:09', '2022-08-29 11:48:04', 1, 'Q1766939'),
(36833, 'San Esteban del Valle', 1189, 'AV', 207, 'ES', 40.27510000, -4.98215000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q523673'),
(36834, 'San Felices de los Gallegos', 1147, 'SA', 207, 'ES', 40.84954000, -6.70804000, '2019-10-05 22:46:09', '2022-08-29 11:44:52', 1, 'Q1766223'),
(36835, 'San Fernando', 5096, 'CA', 207, 'ES', 36.47590000, -6.19817000, '2019-10-05 22:46:09', '2022-08-28 18:44:29', 1, 'Q741765'),
(36836, 'San Fernando de Henares', 1158, 'M', 207, 'ES', 40.42386000, -3.53261000, '2019-10-05 22:46:09', '2022-08-29 12:04:40', 1, 'Q741765'),
(36837, 'San García de Ingelmos', 1189, 'AV', 207, 'ES', 40.76912000, -5.11523000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1630497'),
(36838, 'San Ildefonso', 1200, 'LE', 207, 'ES', 40.90182000, -4.00685000, '2019-10-05 22:46:09', '2019-10-05 22:46:09', 1, 'Q1125500'),
(36839, 'San Isidro', 5108, 'A', 207, 'ES', 28.07617000, -16.55800000, '2019-10-05 22:46:09', '2022-08-29 11:15:48', 1, 'Q23980292'),
(36840, 'San Javier', 1176, 'MU', 207, 'ES', 37.80626000, -0.83736000, '2019-10-05 22:46:09', '2022-08-29 12:05:49', 1, 'Q23980292'),
(36842, 'San José del Valle', 5096, 'CA', 207, 'ES', 36.60554000, -5.79895000, '2019-10-05 22:46:09', '2022-08-28 18:44:29', 1, 'Q1750939'),
(36843, 'San Juan de Alicante', 1175, 'V', 207, 'ES', 38.40148000, -0.43623000, '2019-10-05 22:46:09', '2022-08-29 12:05:40', 1, 'Q1750939'),
(36844, 'San Juan de Aznalfarache', 1193, 'SE', 207, 'ES', 37.35813000, -6.03731000, '2019-10-05 22:46:09', '2022-08-28 19:08:49', 1, 'Q1611693'),
(36845, 'San Juan de Moró', 1175, 'V', 207, 'ES', 40.05990000, -0.13691000, '2019-10-05 22:46:09', '2022-08-29 12:05:40', 1, 'Q23992364'),
(36846, 'San Juan de la Encinilla', 1189, 'AV', 207, 'ES', 40.83020000, -4.83957000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1630457'),
(36847, 'San Juan de la Nava', 1189, 'AV', 207, 'ES', 40.47880000, -4.68238000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1630609'),
(36848, 'San Juan de la Rambla', 5112, 'TF', 207, 'ES', 28.39276000, -16.65015000, '2019-10-05 22:46:09', '2022-08-29 11:31:13', 1, 'Q23980336'),
(36849, 'San Juan de Énova', 1175, 'V', 207, 'ES', 39.07104000, -0.48705000, '2019-10-05 22:46:09', '2022-08-29 12:05:40', 1, 'Q23980336'),
(36850, 'San Juan del Molinillo', 1189, 'AV', 207, 'ES', 40.45909000, -4.81711000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1630594'),
(36851, 'San Juan del Monte', 1146, 'BU', 207, 'ES', 41.68313000, -3.52337000, '2019-10-05 22:46:09', '2022-08-29 11:24:20', 1, 'Q1630594'),
(36852, 'San Juan del Puerto', 5099, 'H', 207, 'ES', 37.31667000, -6.84139000, '2019-10-05 22:46:09', '2022-08-28 19:00:43', 1, 'Q1444174'),
(36853, 'San Justo', 1161, 'ZA', 207, 'ES', 42.13321000, -6.62388000, '2019-10-05 22:46:09', '2022-08-29 11:48:04', 1, 'Q1444174'),
(36854, 'San Justo de la Vega', 1200, 'LE', 207, 'ES', 42.45696000, -6.01825000, '2019-10-05 22:46:09', '2019-10-05 22:46:09', 1, 'Q68572'),
(36855, 'San Leonardo de Yagüe', 1208, 'SO', 207, 'ES', 41.83034000, -3.06880000, '2019-10-05 22:46:09', '2022-08-29 11:51:23', 1, 'Q833629'),
(36856, 'San Llorente', 1183, 'VA', 207, 'ES', 41.68631000, -4.06600000, '2019-10-05 22:46:09', '2022-08-29 11:48:45', 1, 'Q1907107'),
(36857, 'San Lorenzo de Calatrava', 5105, 'CR', 207, 'ES', 38.47681000, -3.82605000, '2019-10-05 22:46:09', '2022-08-29 11:03:25', 1, 'Q1907107'),
(36858, 'San Lorenzo de El Escorial', 1158, 'M', 207, 'ES', 40.59144000, -4.14738000, '2019-10-05 22:46:09', '2022-08-29 12:04:40', 1, 'Q31923752'),
(36859, 'San Lorenzo de Tormes', 1189, 'AV', 207, 'ES', 40.36984000, -5.48848000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1606412'),
(36860, 'San Lorenzo de la Parrilla', 5106, 'CU', 207, 'ES', 39.85129000, -2.36079000, '2019-10-05 22:46:09', '2022-08-29 11:05:01', 1, 'Q1648636'),
(36861, 'San Mamés de Burgos', 1146, 'BU', 207, 'ES', 42.33685000, -3.79397000, '2019-10-05 22:46:09', '2022-08-29 11:24:20', 1, 'Q1641293'),
(36862, 'San Mamés de Campos', 1157, 'P', 207, 'ES', 42.35512000, -4.56565000, '2019-10-05 22:46:09', '2022-08-29 11:45:45', 1, 'Q1907490'),
(36863, 'San Martín de Boniches', 5106, 'CU', 207, 'ES', 39.90000000, -1.56667000, '2019-10-05 22:46:09', '2022-08-29 11:05:01', 1, 'Q115784'),
(36864, 'San Martín de Elines', 1170, 'S', 207, 'ES', 42.82864000, -3.86865000, '2019-10-05 22:46:09', '2020-05-01 17:23:15', 1, 'Q3395164'),
(36865, 'San Martín de Montalbán', 1205, 'TO', 207, 'ES', 39.70186000, -4.38796000, '2019-10-05 22:46:09', '2022-08-29 11:08:30', 1, 'Q1640784'),
(36866, 'San Martín de Pusa', 1205, 'TO', 207, 'ES', 39.78404000, -4.63252000, '2019-10-05 22:46:09', '2022-08-29 11:08:30', 1, 'Q1641416'),
(36867, 'San Martín de Rubiales', 1146, 'BU', 207, 'ES', 41.64191000, -3.99134000, '2019-10-05 22:46:09', '2022-08-29 11:24:20', 1, 'Q1628787'),
(36868, 'San Martín de Trevejo', 1190, 'CC', 207, 'ES', 40.21241000, -6.79535000, '2019-10-05 22:46:09', '2022-08-28 18:12:22', 1, 'Q1628492'),
(36869, 'San Martín de Unx', 1204, 'NA', 207, 'ES', 42.52473000, -1.56091000, '2019-10-05 22:46:09', '2022-08-29 12:06:07', 1, 'Q1628492'),
(36870, 'San Martín de Valdeiglesias', 1158, 'M', 207, 'ES', 40.36185000, -4.39831000, '2019-10-05 22:46:09', '2022-08-29 12:04:40', 1, 'Q1751908'),
(36871, 'San Martín de Valderaduey', 1161, 'ZA', 207, 'ES', 41.81414000, -5.47249000, '2019-10-05 22:46:09', '2022-08-29 11:48:04', 1, 'Q653513'),
(36872, 'San Martín de Valvení', 1183, 'VA', 207, 'ES', 41.75336000, -4.56724000, '2019-10-05 22:46:09', '2022-08-29 11:48:45', 1, 'Q1770811'),
(36873, 'San Martín de la Vega', 1158, 'M', 207, 'ES', 40.20735000, -3.57063000, '2019-10-05 22:46:09', '2022-08-29 12:04:40', 1, 'Q24012691'),
(36874, 'San Martín de la Vega del Alberche', 1189, 'AV', 207, 'ES', 40.43053000, -5.15500000, '2019-10-05 22:46:09', '2022-08-29 11:49:57', 1, 'Q1630563'),
(36875, 'San Martín del Castañar', 1147, 'SA', 207, 'ES', 40.52264000, -6.06387000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1643355'),
(36876, 'San Martín del Pimpollar', 1189, 'AV', 207, 'ES', 40.36830000, -5.05443000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1630378'),
(36877, 'San Martín del Río', 5111, 'TE', 207, 'ES', 41.06633000, -1.38733000, '2019-10-05 22:46:10', '2022-08-29 11:29:44', 1, 'Q1651214'),
(36878, 'San Mateo de Gállego', 5113, 'Z', 207, 'ES', 41.83043000, -0.76578000, '2019-10-05 22:46:10', '2022-08-29 11:42:54', 1, 'Q1639441'),
(36879, 'San Miguel De Abona', 5112, 'TF', 207, 'ES', 28.09826000, -16.61708000, '2019-10-05 22:46:10', '2022-08-29 11:31:13', 1, 'Q1639441'),
(36880, 'San Miguel de Aguayo', 1170, 'S', 207, 'ES', 43.05383000, -4.02550000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q1639441'),
(36881, 'San Miguel de Corneja', 1189, 'AV', 207, 'ES', 40.48722000, -5.28654000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1606963'),
(36882, 'San Miguel de Meruelo', 1170, 'S', 207, 'ES', 43.46211000, -3.58877000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q6119459'),
(36883, 'San Miguel de Salinas', 5108, 'A', 207, 'ES', 37.97972000, -0.78904000, '2019-10-05 22:46:10', '2022-08-29 11:15:47', 1, 'Q984531'),
(36884, 'San Miguel de Serrezuela', 1189, 'AV', 207, 'ES', 40.67064000, -5.28857000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1630415'),
(36885, 'San Miguel de Valero', 1147, 'SA', 207, 'ES', 40.54361000, -5.92280000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1648214'),
(36886, 'San Miguel de la Ribera', 1161, 'ZA', 207, 'ES', 41.33301000, -5.57689000, '2019-10-05 22:46:10', '2022-08-29 11:48:04', 1, 'Q1766460'),
(36887, 'San Miguel del Arroyo', 1183, 'VA', 207, 'ES', 41.44328000, -4.45990000, '2019-10-05 22:46:10', '2022-08-29 11:48:45', 1, 'Q1907203'),
(36888, 'San Miguel del Pino', 1183, 'VA', 207, 'ES', 41.50941000, -4.91149000, '2019-10-05 22:46:10', '2022-08-29 11:48:45', 1, 'Q1907148'),
(36889, 'San Millán de Lara', 1146, 'BU', 207, 'ES', 42.13621000, -3.34515000, '2019-10-05 22:46:10', '2022-08-29 11:24:20', 1, 'Q1641176'),
(36890, 'San Millán de Yécora', 1171, 'LO', 207, 'ES', 42.54705000, -3.09681000, '2019-10-05 22:46:10', '2022-08-29 12:05:09', 1, 'Q1646764'),
(36891, 'San Millán de la Cogolla', 1171, 'LO', 207, 'ES', 42.32974000, -2.86185000, '2019-10-05 22:46:10', '2022-08-29 12:05:09', 1, 'Q842220'),
(36892, 'San Millán de los Caballeros', 1200, 'LE', 207, 'ES', 42.28520000, -5.56107000, '2019-10-05 22:46:10', '2020-05-01 17:23:17', 1, 'Q137141'),
(36893, 'San Morales', 1147, 'SA', 207, 'ES', 40.99358000, -5.50234000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1905253'),
(36894, 'San Muñoz', 1147, 'SA', 207, 'ES', 40.78334000, -6.12758000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1905345'),
(36895, 'San Nicolás', 1185, 'GC', 207, 'ES', 27.98910000, -15.78126000, '2019-10-05 22:46:10', '2022-08-29 12:06:32', 1, 'Q1905345'),
(36896, 'San Nicolás del Puerto', 1193, 'SE', 207, 'ES', 38.00000000, -5.65000000, '2019-10-05 22:46:10', '2022-08-28 19:08:49', 1, 'Q943968'),
(36897, 'San Pablo de la Moraleja', 1183, 'VA', 207, 'ES', 41.16100000, -4.77761000, '2019-10-05 22:46:10', '2022-08-29 11:48:45', 1, 'Q1916546'),
(36898, 'San Pascual', 1189, 'AV', 207, 'ES', 40.88141000, -4.75612000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1916546'),
(36900, 'San Pedro Bercianos', 1200, 'LE', 207, 'ES', 42.39145000, -5.71341000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q138220'),
(36901, 'San Pedro Manrique', 1208, 'SO', 207, 'ES', 42.02860000, -2.23104000, '2019-10-05 22:46:10', '2022-08-29 11:51:23', 1, 'Q836559'),
(36902, 'San Pedro Palmiches', 5106, 'CU', 207, 'ES', 40.42956000, -2.40602000, '2019-10-05 22:46:10', '2022-08-29 11:05:01', 1, 'Q115959'),
(36904, 'San Pedro de Ceque', 1161, 'ZA', 207, 'ES', 42.04334000, -6.07242000, '2019-10-05 22:46:10', '2022-08-29 11:48:04', 1, 'Q1766495'),
(36905, 'San Pedro de Gaíllos', 1192, 'SG', 207, 'ES', 41.22662000, -3.80921000, '2019-10-05 22:46:10', '2022-08-29 11:50:42', 1, 'Q1917445'),
(36906, 'San Pedro de Latarce', 1183, 'VA', 207, 'ES', 41.73556000, -5.32592000, '2019-10-05 22:46:10', '2022-08-29 11:48:45', 1, 'Q24016901'),
(36907, 'San Pedro de Mérida', 5092, 'BA', 207, 'ES', 38.95007000, -6.18645000, '2019-10-05 22:46:10', '2022-08-28 18:09:23', 1, 'Q1443685'),
(36908, 'San Pedro de Rozados', 1147, 'SA', 207, 'ES', 40.79008000, -5.73716000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1906455'),
(36909, 'San Pedro del Arroyo', 1189, 'AV', 207, 'ES', 40.80213000, -4.87075000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1630369'),
(36910, 'San Pedro del Pinatar', 1176, 'MU', 207, 'ES', 37.83568000, -0.79102000, '2019-10-05 22:46:10', '2022-08-29 12:05:49', 1, 'Q1630369'),
(36911, 'San Pedro del Romeral', 1170, 'S', 207, 'ES', 43.11514000, -3.81860000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q1630369'),
(36912, 'San Pedro del Valle', 1147, 'SA', 207, 'ES', 41.03332000, -5.86025000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1906397'),
(36913, 'San Pelayo', 1183, 'VA', 207, 'ES', 41.68033000, -5.03403000, '2019-10-05 22:46:10', '2022-08-29 11:48:45', 1, 'Q1906397'),
(36914, 'San Pelayo de Guareña', 1147, 'SA', 207, 'ES', 41.11579000, -5.85702000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q24014755'),
(36916, 'San Román de Cameros', 1171, 'LO', 207, 'ES', 42.23255000, -2.47436000, '2019-10-05 22:46:10', '2022-08-29 12:05:09', 1, 'Q1645908'),
(36917, 'San Román de Hornija', 1183, 'VA', 207, 'ES', 41.48131000, -5.28454000, '2019-10-05 22:46:10', '2022-08-29 11:48:45', 1, 'Q1907267'),
(36918, 'San Román de la Cuba', 1157, 'P', 207, 'ES', 42.26257000, -4.85724000, '2019-10-05 22:46:10', '2022-08-29 11:45:45', 1, 'Q1906480'),
(36919, 'San Roque', 5096, 'CA', 207, 'ES', 36.21067000, -5.38415000, '2019-10-05 22:46:10', '2022-08-28 18:44:29', 1, 'Q1906480'),
(36920, 'San Sebastián de la Gomera', 5112, 'TF', 207, 'ES', 28.09163000, -17.11331000, '2019-10-05 22:46:10', '2022-08-29 11:31:13', 1, 'Q1906480'),
(36921, 'San Sebastián de los Ballesteros', 5097, 'CO', 207, 'ES', 37.65376000, -4.82409000, '2019-10-05 22:46:10', '2022-08-28 18:49:38', 1, 'Q1613327'),
(36922, 'San Sebastián de los Reyes', 1158, 'M', 207, 'ES', 40.55555000, -3.62733000, '2019-10-05 22:46:10', '2022-08-29 12:04:40', 1, 'Q24012689'),
(36923, 'San Silvestre de Guzmán', 5099, 'H', 207, 'ES', 37.38770000, -7.34908000, '2019-10-05 22:46:10', '2022-08-28 19:00:43', 1, 'Q1630178'),
(36924, 'San Torcuato', 1171, 'LO', 207, 'ES', 42.48219000, -2.88923000, '2019-10-05 22:46:10', '2022-08-29 12:05:09', 1, 'Q1636292'),
(36925, 'San Vicent del Raspeig', 1175, 'V', 207, 'ES', 38.39640000, -0.52550000, '2019-10-05 22:46:10', '2022-08-29 12:05:40', 1, 'Q491667'),
(36926, 'San Vicente de Alcántara', 5092, 'BA', 207, 'ES', 39.36132000, -7.13766000, '2019-10-05 22:46:10', '2022-08-28 18:09:23', 1, 'Q1442479'),
(36927, 'San Vicente de Arévalo', 1189, 'AV', 207, 'ES', 40.96773000, -4.80194000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1628864'),
(36928, 'San Vicente de la Barquera', 1170, 'S', 207, 'ES', 43.38509000, -4.39934000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q384817'),
(36929, 'San Vicente de la Cabeza', 1161, 'ZA', 207, 'ES', 41.80696000, -6.24993000, '2019-10-05 22:46:10', '2022-08-29 11:48:04', 1, 'Q1766453'),
(36930, 'San Vicente de la Sonsierra', 1171, 'LO', 207, 'ES', 42.56321000, -2.76071000, '2019-10-05 22:46:10', '2022-08-29 12:05:09', 1, 'Q761879'),
(36931, 'San Vicente del Palacio', 1183, 'VA', 207, 'ES', 41.21901000, -4.85158000, '2019-10-05 22:46:10', '2022-08-29 11:48:45', 1, 'Q1908571'),
(36932, 'San Vicente del Valle', 1146, 'BU', 207, 'ES', 42.33756000, -3.16210000, '2019-10-05 22:46:10', '2022-08-29 11:24:20', 1, 'Q1641308'),
(36933, 'San Vitero', 1161, 'ZA', 207, 'ES', 41.77740000, -6.34877000, '2019-10-05 22:46:10', '2022-08-29 11:48:04', 1, 'Q1766812'),
(36934, 'Sancedo', 1200, 'LE', 207, 'ES', 42.66666000, -6.63456000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q44930'),
(36935, 'Sanchidrián', 1189, 'AV', 207, 'ES', 40.89347000, -4.58132000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1630640'),
(36936, 'Sanchonuño', 1192, 'SG', 207, 'ES', 41.32325000, -4.30531000, '2019-10-05 22:46:10', '2022-08-29 11:50:42', 1, 'Q1917663'),
(36937, 'Sanchorreja', 1189, 'AV', 207, 'ES', 40.66475000, -4.91494000, '2019-10-05 22:46:10', '2022-08-29 11:49:57', 1, 'Q1630617'),
(36938, 'Sanchotello', 1147, 'SA', 207, 'ES', 40.43812000, -5.75394000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1904722'),
(36939, 'Sanchón de la Ribera', 1147, 'SA', 207, 'ES', 41.08844000, -6.41314000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1766089'),
(36940, 'Sanchón de la Sagrada', 1147, 'SA', 207, 'ES', 40.74287000, -6.02502000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1906470'),
(36941, 'Sando', 1147, 'SA', 207, 'ES', 40.96773000, -6.11136000, '2019-10-05 22:46:10', '2022-08-29 11:44:52', 1, 'Q1922040'),
(36942, 'Sanet y Negrals', 5108, 'A', 207, 'ES', 38.81967000, -0.03406000, '2019-10-05 22:46:10', '2022-08-29 11:15:47', 1, 'Q1983216'),
(36943, 'Sangarcía', 1192, 'SG', 207, 'ES', 40.95023000, -4.41085000, '2019-10-05 22:46:10', '2022-08-29 11:50:42', 1, 'Q1938456'),
(36944, 'Sangarrén', 1177, 'HU', 207, 'ES', 42.01921000, -0.43323000, '2019-10-05 22:46:10', '2022-08-29 12:06:20', 1, 'Q24013324'),
(36945, 'Sangonera la Verde', 1176, 'MU', 207, 'ES', 37.92862000, -1.20794000, '2019-10-05 22:46:10', '2022-08-29 12:05:49', 1, 'Q970554'),
(36946, 'Sangüesa/Zangoza', 1204, 'NA', 207, 'ES', 42.57483000, -1.28283000, '2019-10-05 22:46:10', '2022-08-29 12:06:07', 1, 'Q24012374'),
(36947, 'Sanlúcar de Barrameda', 5096, 'CA', 207, 'ES', 36.77808000, -6.35150000, '2019-10-05 22:46:10', '2022-08-28 18:44:29', 1, 'Q210887'),
(36948, 'Sanlúcar de Guadiana', 5099, 'H', 207, 'ES', 37.47268000, -7.46546000, '2019-10-05 22:46:10', '2022-08-28 19:00:43', 1, 'Q1630199'),
(36949, 'Sanlúcar la Mayor', 1193, 'SE', 207, 'ES', 37.38758000, -6.20346000, '2019-10-05 22:46:10', '2022-08-28 19:08:49', 1, 'Q1446577'),
(36950, 'Sansol', 1204, 'NA', 207, 'ES', 42.55362000, -2.26676000, '2019-10-05 22:46:10', '2022-08-29 12:06:07', 1, 'Q984870'),
(36951, 'Sant Adrià de Besòs', 5102, 'B', 207, 'ES', 41.43073000, 2.21855000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q15471'),
(36953, 'Sant Andreu Salou', 5103, 'GI', 207, 'ES', 41.86667000, 2.83333000, '2019-10-05 22:46:10', '2022-08-29 10:53:16', 1, 'Q13449'),
(36954, 'Sant Andreu de Llavaneres', 5102, 'B', 207, 'ES', 41.56667000, 2.48333000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q13449'),
(36955, 'Sant Andreu de la Barca', 5102, 'B', 207, 'ES', 41.44659000, 1.97187000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q13449'),
(36957, 'Sant Antoni de Portmany', 1174, 'PM', 207, 'ES', 38.98068000, 1.30362000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q734805'),
(36958, 'Sant Boi de Llobregat', 5102, 'B', 207, 'ES', 41.34357000, 2.03659000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q23992295'),
(36959, 'Sant Carles de la Ràpita', 1203, 'T', 207, 'ES', 40.61667000, 0.60000000, '2019-10-05 22:46:10', '2022-08-29 10:57:33', 1, 'Q23992295'),
(36960, 'Sant Celoni', 5102, 'B', 207, 'ES', 41.68921000, 2.48965000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q23992295'),
(36962, 'Sant Cugat del Vallès', 5102, 'B', 207, 'ES', 41.47063000, 2.08611000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q23992295'),
(36964, 'Sant Feliu de Guíxols', 5103, 'GI', 207, 'ES', 41.78333000, 3.03333000, '2019-10-05 22:46:10', '2022-08-29 10:53:16', 1, 'Q23992324'),
(36965, 'Sant Feliu de Llobregat', 5102, 'B', 207, 'ES', 41.38333000, 2.05000000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q23992324'),
(36966, 'Sant Ferriol', 5103, 'GI', 207, 'ES', 42.20000000, 2.66667000, '2019-10-05 22:46:10', '2022-08-29 10:53:16', 1, 'Q23992324'),
(36967, 'Sant Francesc de Formentera', 1174, 'PM', 207, 'ES', 38.70566000, 1.42893000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q1016611'),
(36970, 'Sant Hilari Sacalm', 5103, 'GI', 207, 'ES', 41.88333000, 2.51667000, '2019-10-05 22:46:10', '2022-08-29 10:53:16', 1, 'Q13639'),
(36971, 'Sant Joan', 1174, 'PM', 207, 'ES', 39.59621000, 3.03920000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q692920'),
(36972, 'Sant Joan Despí', 5102, 'B', 207, 'ES', 41.36718000, 2.05740000, '2019-10-05 22:46:10', '2022-08-29 10:50:01', 1, 'Q692920'),
(36973, 'Sant Joan de Labritja', 1174, 'PM', 207, 'ES', 39.07891000, 1.51397000, '2019-10-05 22:46:10', '2019-10-05 22:46:10', 1, 'Q983628'),
(36975, 'Sant Joan de Vilatorrada', 5102, 'B', 207, 'ES', 41.74549000, 1.80476000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q24019070'),
(36976, 'Sant Joan de les Abadesses', 5103, 'GI', 207, 'ES', 42.23332000, 2.28524000, '2019-10-05 22:46:11', '2022-08-29 10:53:16', 1, 'Q23992362'),
(36977, 'Sant Joan les Fonts', 5103, 'GI', 207, 'ES', 42.21186000, 2.51291000, '2019-10-05 22:46:11', '2022-08-29 10:53:16', 1, 'Q23992362'),
(36978, 'Sant Jordi', 1175, 'V', 207, 'ES', 40.50982000, 0.33208000, '2019-10-05 22:46:11', '2022-08-29 12:05:40', 1, 'Q23992362'),
(36979, 'Sant Jordi Desvalls', 5103, 'GI', 207, 'ES', 42.06667000, 2.95000000, '2019-10-05 22:46:11', '2022-08-29 10:53:16', 1, 'Q13454'),
(36980, 'Sant Josep de sa Talaia', 1174, 'PM', 207, 'ES', 38.92239000, 1.29437000, '2019-10-05 22:46:11', '2019-10-05 22:46:11', 1, 'Q983635'),
(36981, 'Sant Julià de Cerdanyola', 5102, 'B', 207, 'ES', 42.22350000, 1.89308000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q983635'),
(36982, 'Sant Just Desvern', 5102, 'B', 207, 'ES', 41.38389000, 2.06758000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q983635'),
(36983, 'Sant Llorenç des Cardassar', 1174, 'PM', 207, 'ES', 39.61009000, 3.28380000, '2019-10-05 22:46:11', '2020-05-01 17:23:15', 1, 'Q983463'),
(36984, 'Sant Lluís', 1174, 'PM', 207, 'ES', 39.84939000, 4.25819000, '2019-10-05 22:46:11', '2020-05-01 17:23:15', 1, 'Q983463'),
(36986, 'Sant Martí Sarroca', 5102, 'B', 207, 'ES', 41.38576000, 1.61121000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q983463'),
(36987, 'Sant Martí Vell', 5103, 'GI', 207, 'ES', 42.01667000, 2.93333000, '2019-10-05 22:46:11', '2022-08-29 10:53:16', 1, 'Q13458'),
(36988, 'Sant Martí de Centelles', 5102, 'B', 207, 'ES', 41.76617000, 2.20566000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q13458'),
(36990, 'Sant Martí de Tous', 5102, 'B', 207, 'ES', 41.55000000, 1.51667000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q15966'),
(36991, 'Sant Miquel de Campmajor', 5103, 'GI', 207, 'ES', 42.13333000, 2.68333000, '2019-10-05 22:46:11', '2022-08-29 10:53:16', 1, 'Q13612'),
(36992, 'Sant Pere Pescador', 5103, 'GI', 207, 'ES', 42.18812000, 3.08212000, '2019-10-05 22:46:11', '2022-08-29 10:53:16', 1, 'Q11852'),
(36993, 'Sant Pere de Ribes', 5102, 'B', 207, 'ES', 41.26045000, 1.77391000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q11852'),
(36994, 'Sant Pere de Riudebitlles', 5102, 'B', 207, 'ES', 41.45000000, 1.70000000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q633095'),
(36996, 'Sant Pol de Mar', 5102, 'B', 207, 'ES', 41.60177000, 2.61741000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q2442135'),
(36997, 'Sant Quirze del Vallès', 5102, 'B', 207, 'ES', 41.53333000, 2.08333000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q2442135'),
(36999, 'Sant Salvador de Guardiola', 5102, 'B', 207, 'ES', 41.68333000, 1.76667000, '2019-10-05 22:46:11', '2022-08-29 10:50:00', 1, 'Q2442135'),
(37000, 'Sant Vicenç de Castellet', 5102, 'B', 207, 'ES', 41.66667000, 1.86667000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q2442135'),
(37001, 'Sant Vicenç de Montalt', 5102, 'B', 207, 'ES', 41.57853000, 2.50879000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q2442135'),
(37002, 'Sant Vicenç dels Horts', 5102, 'B', 207, 'ES', 41.39317000, 2.00689000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q23992544'),
(37003, 'Santa Amalia', 5092, 'BA', 207, 'ES', 39.01118000, -6.01158000, '2019-10-05 22:46:11', '2022-08-28 18:09:23', 1, 'Q1407143'),
(37004, 'Santa Ana', 1190, 'CC', 207, 'ES', 39.30823000, -5.99027000, '2019-10-05 22:46:11', '2022-08-28 18:12:22', 1, 'Q1407143'),
(37005, 'Santa Ana de Pusa', 1205, 'TO', 207, 'ES', 39.76238000, -4.70904000, '2019-10-05 22:46:11', '2022-08-29 11:08:30', 1, 'Q24017281'),
(37006, 'Santa Ana la Real', 5099, 'H', 207, 'ES', 37.86245000, -6.72385000, '2019-10-05 22:46:11', '2022-08-28 19:00:43', 1, 'Q1630172'),
(37007, 'Santa Brígida', 1185, 'GC', 207, 'ES', 28.03197000, -15.50425000, '2019-10-05 22:46:11', '2022-08-29 12:06:32', 1, 'Q1630172'),
(37008, 'Santa Bárbara', 1203, 'T', 207, 'ES', 40.71542000, 0.49292000, '2019-10-05 22:46:11', '2022-08-29 10:57:33', 1, 'Q1630172'),
(37009, 'Santa Bárbara de Casa', 5099, 'H', 207, 'ES', 37.79665000, -7.18735000, '2019-10-05 22:46:11', '2022-08-28 19:00:43', 1, 'Q1618178'),
(37010, 'Santa Cecilia', 1146, 'BU', 207, 'ES', 42.05225000, -3.80345000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q1618178'),
(37011, 'Santa Cecilia del Alcor', 1157, 'P', 207, 'ES', 41.93227000, -4.65546000, '2019-10-05 22:46:11', '2022-08-29 11:45:45', 1, 'Q24012130'),
(37012, 'Santa Cecília de Voltregà', 5102, 'B', 207, 'ES', 42.00000000, 2.23333000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q24012130'),
(37013, 'Santa Clara de Avedillo', 1161, 'ZA', 207, 'ES', 41.33877000, -5.67692000, '2019-10-05 22:46:11', '2022-08-29 11:48:04', 1, 'Q1752378'),
(37014, 'Santa Coloma', 1171, 'LO', 207, 'ES', 42.36717000, -2.65598000, '2019-10-05 22:46:11', '2022-08-29 12:05:09', 1, 'Q1766212'),
(37015, 'Santa Coloma de Cervelló', 5102, 'B', 207, 'ES', 41.36736000, 2.01426000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q1766212'),
(37016, 'Santa Coloma de Farners', 5103, 'GI', 207, 'ES', 41.86667000, 2.66667000, '2019-10-05 22:46:11', '2022-08-29 10:53:16', 1, 'Q1766212'),
(37017, 'Santa Coloma de Gramenet', 5102, 'B', 207, 'ES', 41.45152000, 2.20810000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q1766212'),
(37018, 'Santa Colomba de Curueño', 1200, 'LE', 207, 'ES', 42.75058000, -5.41201000, '2019-10-05 22:46:11', '2020-05-01 17:23:17', 1, 'Q60983'),
(37019, 'Santa Colomba de Somoza', 1200, 'LE', 207, 'ES', 42.44466000, -6.24483000, '2019-10-05 22:46:11', '2019-10-05 22:46:11', 1, 'Q141080'),
(37020, 'Santa Colomba de las Monjas', 1161, 'ZA', 207, 'ES', 41.95833000, -5.68389000, '2019-10-05 22:46:11', '2022-08-29 11:48:04', 1, 'Q141080'),
(37021, 'Santa Comba', 5089, 'C', 207, 'ES', 43.03306000, -8.80925000, '2019-10-05 22:46:11', '2022-08-28 13:37:17', 1, 'Q141080'),
(37023, 'Santa Cristina de Valmadrigal', 1200, 'LE', 207, 'ES', 42.35446000, -5.30929000, '2019-10-05 22:46:11', '2019-10-05 22:46:11', 1, 'Q13569'),
(37024, 'Santa Cristina de la Polvorosa', 1161, 'ZA', 207, 'ES', 41.99985000, -5.71355000, '2019-10-05 22:46:11', '2022-08-29 11:48:04', 1, 'Q1646332'),
(37025, 'Santa Croya de Tera', 1161, 'ZA', 207, 'ES', 41.98334000, -5.97725000, '2019-10-05 22:46:11', '2022-08-29 11:48:04', 1, 'Q1766906'),
(37026, 'Santa Cruz', 1176, 'MU', 207, 'ES', 38.02180000, -1.05749000, '2019-10-05 22:46:11', '2022-08-29 12:05:49', 1, 'Q1766906'),
(37027, 'Santa Cruz de Bezana', 1170, 'S', 207, 'ES', 43.44370000, -3.90324000, '2019-10-05 22:46:11', '2019-10-05 22:46:11', 1, 'Q950156'),
(37028, 'Santa Cruz de Boedo', 1157, 'P', 207, 'ES', 42.52531000, -4.37398000, '2019-10-05 22:46:11', '2022-08-29 11:45:45', 1, 'Q1906821'),
(37029, 'Santa Cruz de Grío', 5113, 'Z', 207, 'ES', 41.37050000, -1.43009000, '2019-10-05 22:46:11', '2022-08-29 11:42:54', 1, 'Q23992481'),
(37030, 'Santa Cruz de Moncayo', 5113, 'Z', 207, 'ES', 41.88240000, -1.75637000, '2019-10-05 22:46:11', '2022-08-29 11:42:54', 1, 'Q1639625'),
(37031, 'Santa Cruz de Moya', 5106, 'CU', 207, 'ES', 39.95000000, -1.26667000, '2019-10-05 22:46:11', '2022-08-29 11:05:01', 1, 'Q115955'),
(37032, 'Santa Cruz de Mudela', 5105, 'CR', 207, 'ES', 38.64241000, -3.46650000, '2019-10-05 22:46:11', '2022-08-29 11:03:25', 1, 'Q958776'),
(37033, 'Santa Cruz de Nogueras', 5111, 'TE', 207, 'ES', 41.11475000, -1.08933000, '2019-10-05 22:46:11', '2022-08-29 11:29:44', 1, 'Q978565'),
(37034, 'Santa Cruz de Paniagua', 1190, 'CC', 207, 'ES', 40.19167000, -6.34003000, '2019-10-05 22:46:11', '2022-08-28 18:12:22', 1, 'Q976610'),
(37035, 'Santa Cruz de Pinares', 1189, 'AV', 207, 'ES', 40.54265000, -4.58036000, '2019-10-05 22:46:11', '2022-08-29 11:49:57', 1, 'Q1628870'),
(37036, 'Santa Cruz de Tenerife', 5112, 'TF', 207, 'ES', 28.46824000, -16.25462000, '2019-10-05 22:46:11', '2022-08-29 11:31:13', 1, 'Q14328'),
(37037, 'Santa Cruz de Yanguas', 1208, 'SO', 207, 'ES', 42.06250000, -2.44894000, '2019-10-05 22:46:11', '2022-08-29 11:51:23', 1, 'Q835432'),
(37038, 'Santa Cruz de la Palma', 5112, 'TF', 207, 'ES', 28.68351000, -17.76421000, '2019-10-05 22:46:11', '2022-08-29 11:31:13', 1, 'Q835432'),
(37039, 'Santa Cruz de la Salceda', 1146, 'BU', 207, 'ES', 41.59591000, -3.59317000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q1641208'),
(37040, 'Santa Cruz de la Serós', 1177, 'HU', 207, 'ES', 42.52271000, -0.67515000, '2019-10-05 22:46:11', '2022-08-29 12:06:20', 1, 'Q24013322'),
(37041, 'Santa Cruz de la Sierra', 1190, 'CC', 207, 'ES', 39.33635000, -5.84561000, '2019-10-05 22:46:11', '2022-08-28 18:12:22', 1, 'Q973409'),
(37042, 'Santa Cruz de la Zarza', 1205, 'TO', 207, 'ES', 39.98104000, -3.18787000, '2019-10-05 22:46:11', '2022-08-29 11:08:30', 1, 'Q24017280'),
(37043, 'Santa Cruz de los Cáñamos', 5105, 'CR', 207, 'ES', 38.63759000, -2.86618000, '2019-10-05 22:46:11', '2022-08-29 11:03:25', 1, 'Q24011761'),
(37044, 'Santa Cruz del Retamar', 1205, 'TO', 207, 'ES', 40.11917000, -4.24158000, '2019-10-05 22:46:11', '2022-08-29 11:08:30', 1, 'Q1607695'),
(37045, 'Santa Cruz del Valle', 1189, 'AV', 207, 'ES', 40.25160000, -5.00133000, '2019-10-05 22:46:11', '2022-08-29 11:49:57', 1, 'Q1628238'),
(37046, 'Santa Cruz del Valle Urbión', 1146, 'BU', 207, 'ES', 42.30558000, -3.22140000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q1751234'),
(37047, 'Santa Elena', 5100, 'J', 207, 'ES', 38.34162000, -3.53953000, '2019-10-05 22:46:11', '2022-08-28 19:04:30', 1, 'Q23979178'),
(37048, 'Santa Elena de Jamuz', 1200, 'LE', 207, 'ES', 42.26064000, -5.88762000, '2019-10-05 22:46:11', '2019-10-05 22:46:11', 1, 'Q68564'),
(37049, 'Santa Eufemia', 5097, 'CO', 207, 'ES', 38.60000000, -4.90000000, '2019-10-05 22:46:11', '2022-08-28 18:49:38', 1, 'Q68564'),
(37050, 'Santa Eufemia del Arroyo', 1183, 'VA', 207, 'ES', 41.89505000, -5.26575000, '2019-10-05 22:46:11', '2022-08-29 11:48:45', 1, 'Q1919555'),
(37051, 'Santa Eufemia del Barco', 1161, 'ZA', 207, 'ES', 41.67808000, -5.89831000, '2019-10-05 22:46:11', '2022-08-29 11:48:04', 1, 'Q24016587'),
(37052, 'Santa Eugènia', 1174, 'PM', 207, 'ES', 39.62361000, 2.83864000, '2019-10-05 22:46:11', '2020-05-01 17:23:15', 1, 'Q983631'),
(37053, 'Santa Eugènia de Berga', 5102, 'B', 207, 'ES', 41.90000000, 2.28333000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q983631'),
(37054, 'Santa Eulalia', 5111, 'TE', 207, 'ES', 40.56742000, -1.31354000, '2019-10-05 22:46:11', '2022-08-29 11:29:44', 1, 'Q1765482'),
(37055, 'Santa Eulalia Bajera', 1171, 'LO', 207, 'ES', 42.20960000, -2.19174000, '2019-10-05 22:46:11', '2022-08-29 12:05:09', 1, 'Q1646017'),
(37056, 'Santa Eulalia de Gállego', 5113, 'Z', 207, 'ES', 42.28700000, -0.76065000, '2019-10-05 22:46:11', '2022-08-29 11:42:54', 1, 'Q1650647'),
(37057, 'Santa Eulàlia de Ronçana', 5102, 'B', 207, 'ES', 41.65000000, 2.23333000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q1650647'),
(37058, 'Santa Eulària des Riu', 1174, 'PM', 207, 'ES', 38.98457000, 1.53409000, '2019-10-05 22:46:11', '2020-05-01 17:23:15', 1, 'Q983465'),
(37059, 'Santa Fe de Mondújar', 5095, 'AL', 207, 'ES', 36.97479000, -2.53126000, '2019-10-05 22:46:11', '2022-08-28 18:41:41', 1, 'Q983465'),
(37060, 'Santa Gadea del Cid', 1146, 'BU', 207, 'ES', 42.71531000, -3.05885000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q1641214'),
(37061, 'Santa Inés', 1146, 'BU', 207, 'ES', 42.04013000, -3.70322000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q2000928'),
(37062, 'Santa Lucía', 1185, 'GC', 207, 'ES', 27.91174000, -15.54071000, '2019-10-05 22:46:11', '2022-08-29 12:06:32', 1, 'Q738674'),
(37063, 'Santa Magdalena de Pulpis', 5110, 'CS', 207, 'ES', 40.35625000, 0.30258000, '2019-10-05 22:46:11', '2022-08-29 11:26:42', 1, 'Q738674'),
(37064, 'Santa Margalida', 1174, 'PM', 207, 'ES', 39.70143000, 3.10215000, '2019-10-05 22:46:11', '2019-10-05 22:46:11', 1, 'Q984491'),
(37066, 'Santa Maria de Corcó', 5102, 'B', 207, 'ES', 42.03333000, 2.36667000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q13837'),
(37067, 'Santa Maria de Palautordera', 5102, 'B', 207, 'ES', 41.69417000, 2.44566000, '2019-10-05 22:46:11', '2022-08-29 10:50:01', 1, 'Q13837'),
(37068, 'Santa Marina del Rey', 1200, 'LE', 207, 'ES', 42.51334000, -5.86065000, '2019-10-05 22:46:11', '2019-10-05 22:46:11', 1, 'Q138239'),
(37069, 'Santa Marta', 5092, 'BA', 207, 'ES', 38.61421000, -6.62799000, '2019-10-05 22:46:11', '2022-08-28 18:09:23', 1, 'Q138239'),
(37070, 'Santa Marta de Magasca', 1190, 'CC', 207, 'ES', 39.51150000, -6.09881000, '2019-10-05 22:46:11', '2022-08-28 18:12:22', 1, 'Q1658734'),
(37072, 'Santa Marta de Tormes', 1147, 'SA', 207, 'ES', 40.95065000, -5.62723000, '2019-10-05 22:46:11', '2022-08-29 11:44:52', 1, 'Q1648843'),
(37073, 'Santa Marta del Cerro', 1192, 'SG', 207, 'ES', 41.21843000, -3.68582000, '2019-10-05 22:46:11', '2022-08-29 11:50:43', 1, 'Q1905622'),
(37074, 'Santa María de Cayón', 1170, 'S', 207, 'ES', 43.30849000, -3.83680000, '2019-10-05 22:46:11', '2020-05-01 17:23:15', 1, 'Q1603040'),
(37075, 'Santa María de Huerta', 1208, 'SO', 207, 'ES', 41.26667000, -2.16667000, '2019-10-05 22:46:11', '2022-08-29 11:51:23', 1, 'Q836523'),
(37076, 'Santa María de Ordás', 1200, 'LE', 207, 'ES', 42.72653000, -5.82301000, '2019-10-05 22:46:11', '2020-05-01 17:23:17', 1, 'Q137163'),
(37077, 'Santa María de Sando', 1147, 'SA', 207, 'ES', 40.97909000, -6.12904000, '2019-10-05 22:46:11', '2022-08-29 11:44:52', 1, 'Q1905427'),
(37078, 'Santa María de Valverde', 1161, 'ZA', 207, 'ES', 41.93479000, -5.93560000, '2019-10-05 22:46:11', '2022-08-29 11:48:04', 1, 'Q1653177'),
(37079, 'Santa María de la Alameda', 1158, 'M', 207, 'ES', 40.59492000, -4.25772000, '2019-10-05 22:46:11', '2022-08-29 12:04:40', 1, 'Q1769405'),
(37080, 'Santa María de la Isla', 1200, 'LE', 207, 'ES', 42.35533000, -5.92870000, '2019-10-05 22:46:11', '2020-05-01 17:23:17', 1, 'Q137704'),
(37081, 'Santa María de la Vega', 1161, 'ZA', 207, 'ES', 42.08497000, -5.80851000, '2019-10-05 22:46:11', '2022-08-29 11:48:04', 1, 'Q137704'),
(37082, 'Santa María de las Hoyas', 1208, 'SO', 207, 'ES', 41.77145000, -3.14172000, '2019-10-05 22:46:11', '2022-08-29 11:51:23', 1, 'Q837624'),
(37083, 'Santa María de los Caballeros', 1189, 'AV', 207, 'ES', 40.38925000, -5.45112000, '2019-10-05 22:46:11', '2022-08-29 11:49:57', 1, 'Q1630468'),
(37084, 'Santa María del Berrocal', 1189, 'AV', 207, 'ES', 40.50845000, -5.40483000, '2019-10-05 22:46:11', '2022-08-29 11:49:57', 1, 'Q1628256'),
(37085, 'Santa María del Campo', 1146, 'BU', 207, 'ES', 42.13304000, -3.97283000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q1641301'),
(37086, 'Santa María del Campo Rus', 5106, 'CU', 207, 'ES', 39.55942000, -2.42306000, '2019-10-05 22:46:11', '2022-08-29 11:05:01', 1, 'Q1648624'),
(37087, 'Santa María del Invierno', 1146, 'BU', 207, 'ES', 42.44288000, -3.43782000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q1641378'),
(37088, 'Santa María del Monte de Cea', 1200, 'LE', 207, 'ES', 42.49137000, -5.11689000, '2019-10-05 22:46:11', '2020-05-01 17:23:17', 1, 'Q60990'),
(37089, 'Santa María del Páramo', 1200, 'LE', 207, 'ES', 42.35512000, -5.75151000, '2019-10-05 22:46:11', '2020-05-01 17:23:17', 1, 'Q68561'),
(37090, 'Santa María del Val', 5106, 'CU', 207, 'ES', 40.50439000, -2.04115000, '2019-10-05 22:46:11', '2022-08-29 11:05:01', 1, 'Q196172'),
(37091, 'Santa María la Real de Nieva', 1192, 'SG', 207, 'ES', 41.06998000, -4.40709000, '2019-10-05 22:46:11', '2022-08-29 11:50:42', 1, 'Q139988'),
(37092, 'Santa Olalla', 1205, 'TO', 207, 'ES', 40.02348000, -4.43025000, '2019-10-05 22:46:11', '2022-08-29 11:08:30', 1, 'Q23992499'),
(37093, 'Santa Olalla de Bureba', 1146, 'BU', 207, 'ES', 42.47721000, -3.44109000, '2019-10-05 22:46:11', '2022-08-29 11:24:20', 1, 'Q1641364'),
(37094, 'Santa Olalla del Cala', 5099, 'H', 207, 'ES', 37.90000000, -6.21667000, '2019-10-05 22:46:12', '2022-08-28 19:00:43', 1, 'Q1614548'),
(37095, 'Santa Oliva', 1203, 'T', 207, 'ES', 41.25357000, 1.55086000, '2019-10-05 22:46:12', '2022-08-29 10:57:33', 1, 'Q1229647'),
(37096, 'Santa Pau', 5103, 'GI', 207, 'ES', 42.14430000, 2.57123000, '2019-10-05 22:46:12', '2022-08-29 10:53:16', 1, 'Q12691'),
(37097, 'Santa Perpètua de Mogoda', 5102, 'B', 207, 'ES', 41.53333000, 2.18333000, '2019-10-05 22:46:12', '2022-08-29 10:50:01', 1, 'Q12691'),
(37098, 'Santa Pola', 5108, 'A', 207, 'ES', 38.19165000, -0.56580000, '2019-10-05 22:46:12', '2022-08-29 11:15:48', 1, 'Q12691'),
(37099, 'Santa Ponsa', 1174, 'PM', 207, 'ES', 39.50868000, 2.47660000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q1941746'),
(37100, 'Santa Susanna', 5102, 'B', 207, 'ES', 41.63333000, 2.71667000, '2019-10-05 22:46:12', '2022-08-29 10:50:01', 1, 'Q1941746'),
(37102, 'Santa Úrsula', 5112, 'TF', 207, 'ES', 28.42613000, -16.48876000, '2019-10-05 22:46:12', '2022-08-29 11:31:13', 1, 'Q657800'),
(37103, 'Santacara', 1204, 'NA', 207, 'ES', 42.36667000, -1.63333000, '2019-10-05 22:46:12', '2022-08-29 12:06:07', 1, 'Q1647926'),
(37104, 'Santaella', 5097, 'CO', 207, 'ES', 37.56294000, -4.84362000, '2019-10-05 22:46:12', '2022-08-28 18:49:38', 1, 'Q970749'),
(37106, 'Santander', 1170, 'S', 207, 'ES', 43.46472000, -3.80444000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q23992498'),
(37107, 'Santanyí', 1174, 'PM', 207, 'ES', 39.35461000, 3.12907000, '2019-10-05 22:46:12', '2020-05-01 17:23:15', 1, 'Q983633'),
(37108, 'Santas Martas', 1200, 'LE', 207, 'ES', 42.43220000, -5.37066000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q24013103'),
(37109, 'Santed', 5113, 'Z', 207, 'ES', 41.03172000, -1.51028000, '2019-10-05 22:46:12', '2022-08-29 11:42:54', 1, 'Q951119'),
(37110, 'Santervás de Campos', 1183, 'VA', 207, 'ES', 42.21820000, -5.10027000, '2019-10-05 22:46:12', '2022-08-29 11:48:45', 1, 'Q951119'),
(37111, 'Santervás de la Vega', 1157, 'P', 207, 'ES', 42.50677000, -4.80002000, '2019-10-05 22:46:12', '2022-08-29 11:45:45', 1, 'Q977008'),
(37112, 'Santiago Millas', 1200, 'LE', 207, 'ES', 42.38379000, -6.10538000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q1635252'),
(37113, 'Santiago de Calatrava', 5100, 'J', 207, 'ES', 37.75382000, -4.17093000, '2019-10-05 22:46:12', '2022-08-28 19:04:30', 1, 'Q1751774'),
(37114, 'Santiago de Compostela', 5089, 'C', 207, 'ES', 42.88052000, -8.54569000, '2019-10-05 22:46:12', '2022-08-28 13:37:17', 1, 'Q23992507'),
(37115, 'Santiago de la Puebla', 1147, 'SA', 207, 'ES', 40.80132000, -5.28073000, '2019-10-05 22:46:12', '2022-08-29 11:44:52', 1, 'Q1650219'),
(37116, 'Santiago de la Ribera', 1176, 'MU', 207, 'ES', 37.79681000, -0.80544000, '2019-10-05 22:46:12', '2022-08-29 12:05:49', 1, 'Q1650219'),
(37117, 'Santiago del Campo', 1190, 'CC', 207, 'ES', 39.62840000, -6.36362000, '2019-10-05 22:46:12', '2022-08-28 18:12:22', 1, 'Q1658560'),
(37118, 'Santiago del Collado', 1189, 'AV', 207, 'ES', 40.43326000, -5.35631000, '2019-10-05 22:46:12', '2022-08-29 11:49:57', 1, 'Q1628852'),
(37119, 'Santiago del Teide', 5112, 'TF', 207, 'ES', 28.29400000, -16.81618000, '2019-10-05 22:46:12', '2022-08-29 11:31:13', 1, 'Q915283'),
(37120, 'Santibáñez de Béjar', 1147, 'SA', 207, 'ES', 40.48785000, -5.61110000, '2019-10-05 22:46:12', '2022-08-29 11:44:52', 1, 'Q1766216'),
(37121, 'Santibáñez de Ecla', 1157, 'P', 207, 'ES', 42.70784000, -4.37263000, '2019-10-05 22:46:12', '2022-08-29 11:45:45', 1, 'Q1906868'),
(37122, 'Santibáñez de Tera', 1161, 'ZA', 207, 'ES', 41.98563000, -5.92257000, '2019-10-05 22:46:12', '2022-08-29 11:48:04', 1, 'Q1766501'),
(37123, 'Santibáñez de Valcorba', 1183, 'VA', 207, 'ES', 41.56983000, -4.44938000, '2019-10-05 22:46:12', '2022-08-29 11:48:45', 1, 'Q1907282'),
(37124, 'Santibáñez de Vidriales', 1161, 'ZA', 207, 'ES', 42.07362000, -6.01549000, '2019-10-05 22:46:12', '2022-08-29 11:48:04', 1, 'Q1653139'),
(37125, 'Santibáñez de la Peña', 1157, 'P', 207, 'ES', 42.80929000, -4.73026000, '2019-10-05 22:46:12', '2022-08-29 11:45:45', 1, 'Q1653139'),
(37126, 'Santibáñez de la Sierra', 1147, 'SA', 207, 'ES', 40.49464000, -5.91576000, '2019-10-05 22:46:12', '2022-08-29 11:44:52', 1, 'Q1770816'),
(37127, 'Santibáñez del Val', 1146, 'BU', 207, 'ES', 41.97354000, -3.48142000, '2019-10-05 22:46:12', '2022-08-29 11:24:20', 1, 'Q1770816'),
(37128, 'Santibáñez el Alto', 1190, 'CC', 207, 'ES', 40.18525000, -6.54782000, '2019-10-05 22:46:12', '2022-08-28 18:12:22', 1, 'Q1628837'),
(37129, 'Santibáñez el Bajo', 1190, 'CC', 207, 'ES', 40.17637000, -6.22441000, '2019-10-05 22:46:12', '2022-08-28 18:12:22', 1, 'Q522058'),
(37130, 'Santillana', 1170, 'S', 207, 'ES', 43.38903000, -4.10844000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q522058'),
(37131, 'Santiponce', 1193, 'SE', 207, 'ES', 37.43553000, -6.04106000, '2019-10-05 22:46:12', '2022-08-28 19:08:49', 1, 'Q568278'),
(37132, 'Santiso', 5089, 'C', 207, 'ES', 42.87388000, -8.05497000, '2019-10-05 22:46:12', '2022-08-28 13:37:17', 1, 'Q1442623'),
(37133, 'Santisteban del Puerto', 5100, 'J', 207, 'ES', 38.24829000, -3.20762000, '2019-10-05 22:46:12', '2022-08-28 19:04:30', 1, 'Q24013233'),
(37134, 'Santiurde de Reinosa', 1170, 'S', 207, 'ES', 43.06144000, -4.08351000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q24013233'),
(37135, 'Santiurde de Toranzo', 1170, 'S', 207, 'ES', 43.23819000, -3.93947000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q1607337'),
(37136, 'Santiuste', 5107, 'GU', 207, 'ES', 41.08462000, -2.80953000, '2019-10-05 22:46:12', '2022-08-29 11:06:45', 1, 'Q1908252'),
(37137, 'Santiuste de San Juan Bautista', 1192, 'SG', 207, 'ES', 41.15593000, -4.57202000, '2019-10-05 22:46:12', '2022-08-29 11:50:43', 1, 'Q1938420'),
(37138, 'Santiz', 1147, 'SA', 207, 'ES', 41.20579000, -5.89654000, '2019-10-05 22:46:12', '2022-08-29 11:44:52', 1, 'Q1906411'),
(37139, 'Santo Domingo de Pirón', 1192, 'SG', 207, 'ES', 41.04104000, -3.98933000, '2019-10-05 22:46:12', '2022-08-29 11:50:43', 1, 'Q1919684'),
(37140, 'Santo Domingo de Silos', 1146, 'BU', 207, 'ES', 41.96400000, -3.41740000, '2019-10-05 22:46:12', '2022-08-29 11:24:20', 1, 'Q1766072'),
(37141, 'Santo Domingo de la Calzada', 1171, 'LO', 207, 'ES', 42.44015000, -2.95365000, '2019-10-05 22:46:12', '2022-08-29 12:05:09', 1, 'Q646564'),
(37142, 'Santo Domingo de las Posadas', 1189, 'AV', 207, 'ES', 40.81200000, -4.63383000, '2019-10-05 22:46:12', '2022-08-29 11:49:57', 1, 'Q385629'),
(37143, 'Santo Tomé', 5100, 'J', 207, 'ES', 38.02861000, -3.10092000, '2019-10-05 22:46:12', '2022-08-28 19:04:30', 1, 'Q385629'),
(37144, 'Santo Tomé de Zabarcos', 1189, 'AV', 207, 'ES', 40.78726000, -4.91048000, '2019-10-05 22:46:12', '2022-08-29 11:49:57', 1, 'Q749685'),
(37145, 'Santomera', 1176, 'MU', 207, 'ES', 38.06147000, -1.04877000, '2019-10-05 22:46:12', '2022-08-29 12:05:49', 1, 'Q1765918'),
(37146, 'Santorcaz', 1158, 'M', 207, 'ES', 40.47176000, -3.23462000, '2019-10-05 22:46:12', '2022-08-29 12:04:40', 1, 'Q1765918'),
(37147, 'Santovenia', 1161, 'ZA', 207, 'ES', 41.87857000, -5.71003000, '2019-10-05 22:46:12', '2022-08-29 11:48:04', 1, 'Q1765918'),
(37148, 'Santovenia de Pisuerga', 1183, 'VA', 207, 'ES', 41.69457000, -4.69029000, '2019-10-05 22:46:12', '2022-08-29 11:48:45', 1, 'Q24016896'),
(37149, 'Santoyo', 1157, 'P', 207, 'ES', 42.21473000, -4.34296000, '2019-10-05 22:46:12', '2022-08-29 11:45:45', 1, 'Q1918060'),
(37150, 'Santoña', 1170, 'S', 207, 'ES', 43.44386000, -3.45757000, '2019-10-05 22:46:12', '2020-05-01 17:23:15', 1, 'Q1442470'),
(37154, 'Santurdejo', 1171, 'LO', 207, 'ES', 42.37696000, -2.95437000, '2019-10-05 22:46:12', '2022-08-29 12:05:09', 1, 'Q1645895'),
(37155, 'Santurtzi', 5094, 'BI', 207, 'ES', 43.32842000, -3.03248000, '2019-10-05 22:46:12', '2022-08-28 18:25:56', 1, 'Q1645895'),
(37157, 'Sanxenxo', 1167, 'PO', 207, 'ES', 42.39996000, -8.80698000, '2019-10-05 22:46:12', '2022-08-28 17:57:54', 1, 'Q1645895'),
(37158, 'Sanzoles', 1161, 'ZA', 207, 'ES', 41.43235000, -5.56710000, '2019-10-05 22:46:12', '2022-08-29 11:48:04', 1, 'Q1652334'),
(37159, 'Sardón de Duero', 1183, 'VA', 207, 'ES', 41.60847000, -4.43397000, '2019-10-05 22:46:12', '2022-08-29 11:48:45', 1, 'Q1907352'),
(37160, 'Sardón de los Frailes', 1147, 'SA', 207, 'ES', 41.21373000, -6.27101000, '2019-10-05 22:46:12', '2022-08-29 11:44:52', 1, 'Q1766065'),
(37161, 'Sargentes de la Lora', 1146, 'BU', 207, 'ES', 42.76917000, -3.87278000, '2019-10-05 22:46:12', '2022-08-29 11:24:20', 1, 'Q302600'),
(37162, 'Sariegos', 1200, 'LE', 207, 'ES', 42.65000000, -5.63333000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q1615338'),
(37163, 'Sariñena', 1177, 'HU', 207, 'ES', 41.79128000, -0.15804000, '2019-10-05 22:46:12', '2022-08-29 12:06:20', 1, 'Q478178'),
(37164, 'Saro', 1170, 'S', 207, 'ES', 43.25961000, -3.84283000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q478178'),
(37165, 'Sarracín', 1146, 'BU', 207, 'ES', 42.25813000, -3.68608000, '2019-10-05 22:46:12', '2022-08-29 11:24:20', 1, 'Q1641316'),
(37166, 'Sarratella', 5110, 'CS', 207, 'ES', 40.31284000, 0.03150000, '2019-10-05 22:46:12', '2022-08-29 11:26:42', 1, 'Q1766247'),
(37167, 'Sarreaus', 5091, 'OR', 207, 'ES', 42.08784000, -7.60348000, '2019-10-05 22:46:12', '2022-08-28 17:53:26', 1, 'Q1607778'),
(37168, 'Sarria', 5090, 'LU', 207, 'ES', 42.78148000, -7.41431000, '2019-10-05 22:46:12', '2022-08-28 17:49:36', 1, 'Q1607778'),
(37169, 'Sarriguren', 1204, 'NA', 207, 'ES', 42.81292000, -1.59815000, '2019-10-05 22:46:12', '2022-08-29 12:06:07', 1, 'Q1607778'),
(37171, 'Sarrià de Ter', 5103, 'GI', 207, 'ES', 42.01667000, 2.83333000, '2019-10-05 22:46:12', '2022-08-29 10:53:16', 1, 'Q13456'),
(37173, 'Sarrión', 5111, 'TE', 207, 'ES', 40.14175000, -0.81533000, '2019-10-05 22:46:12', '2022-08-29 11:29:44', 1, 'Q24015035'),
(37174, 'Sartaguda', 1204, 'NA', 207, 'ES', 42.38333000, -2.05709000, '2019-10-05 22:46:12', '2022-08-29 12:06:07', 1, 'Q1767920'),
(37175, 'Sartajada', 1205, 'TO', 207, 'ES', 40.21320000, -4.79427000, '2019-10-05 22:46:12', '2022-08-29 11:08:30', 1, 'Q1640777');

