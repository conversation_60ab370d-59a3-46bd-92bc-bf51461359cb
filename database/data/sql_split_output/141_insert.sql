INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(71001, 'Janitzio', 3474, 'MIC', 142, 'MX', 19.57327000, -101.65189000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q2448285'),
(71002, '<PERSON><PERSON>', 3447, 'CHH', 142, 'MX', 30.88909000, -108.19431000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q11076576'),
(71003, '<PERSON><PERSON><PERSON><PERSON>', 3465, 'MOR', 142, 'MX', 18.71828000, -98.77760000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q2652084'),
(71004, '<PERSON><PERSON><PERSON> del Progreso', 3469, '<PERSON>U<PERSON>', 142, 'MX', 20.37237000, -101.06249000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q2651205'),
(71005, 'Jard<PERSON> de la <PERSON><PERSON> (<PERSON>ard<PERSON>)', 3452, 'NLE', 142, 'MX', 25.62944000, -100.18778000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20148675'),
(71006, 'Jardínes del G<PERSON><PERSON>l<PERSON>', 3451, 'CHP', 142, 'MX', 16.72847000, -93.03463000, '2019-10-05 23:08:48', '2020-05-01 17:22:59', 1, 'Q20148703'),
(71007, 'Jarillas', 3456, 'AGU', 142, 'MX', 22.29611000, -102.06333000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20284154'),
(71008, 'Jaripeo', 3474, 'MIC', 142, 'MX', 19.68026000, -101.08545000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20284154'),
(71009, 'Jarácuaro', 3474, 'MIC', 142, 'MX', 19.56058000, -101.67785000, '2019-10-05 23:08:48', '2020-05-01 17:23:01', 1, 'Q20148625'),
(71010, 'Jauja', 3453, 'DUR', 142, 'MX', 25.92894000, -103.37382000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20148625'),
(71011, 'Jaula de Abajo', 3462, 'ZAC', 142, 'MX', 22.02824000, -101.59193000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20148625'),
(71012, 'Jaumave', 3463, 'TAM', 142, 'MX', 23.40621000, -99.38072000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q27769282'),
(71013, 'Jauregui', 3469, 'GUA', 142, 'MX', 20.59280000, -100.71940000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20148773'),
(71014, 'Javier Rojo Gómez', 3470, 'HID', 142, 'MX', 20.06972000, -98.41778000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q20242413'),
(71015, 'Javier Rojo Gómez', 3467, 'ROO', 142, 'MX', 18.27389000, -88.68111000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q20148821'),
(71016, 'Javier Rojo Gómez', 3449, 'SIN', 142, 'MX', 25.49472000, -108.37750000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q20148814'),
(71017, 'Jecopaco', 3468, 'SON', 142, 'MX', 27.19831000, -109.76946000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q5928926'),
(71018, 'Jerez de García Salinas', 3462, 'ZAC', 142, 'MX', 22.64971000, -102.99032000, '2019-10-05 23:08:48', '2020-05-01 17:23:03', 1, 'Q2225397'),
(71019, 'Jericó', 3451, 'CHP', 142, 'MX', 16.28713000, -92.96630000, '2019-10-05 23:08:48', '2020-05-01 17:22:59', 1, 'Q2225397'),
(71020, 'Jerusalén', 3451, 'CHP', 142, 'MX', 17.36806000, -92.06083000, '2019-10-05 23:08:48', '2020-05-01 17:22:59', 1, 'Q20284204'),
(71021, 'Jerécuaro', 3469, 'GUA', 142, 'MX', 20.15467000, -100.50860000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q2651217'),
(71022, 'Jesús Carranza', 3464, 'VER', 142, 'MX', 17.43526000, -95.02637000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q20225433'),
(71023, 'Jesús Carranza (Rancho de Jesús)', 3450, 'MEX', 142, 'MX', 18.94528000, -99.63056000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q20148992'),
(71024, 'Jesús Gómez Portugal', 3456, 'AGU', 142, 'MX', 21.99607000, -102.29095000, '2019-10-05 23:08:48', '2020-05-01 17:22:59', 1, 'Q20284217'),
(71025, 'Jesús Gómez Portugal (Margaritas)', 3456, 'AGU', 142, 'MX', 21.99917000, -102.29139000, '2019-10-05 23:08:48', '2020-05-01 17:22:59', 1, 'Q20284223'),
(71026, 'Jesús Huitznahuac', 3458, 'TLA', 142, 'MX', 19.34251000, -98.12807000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q20284223'),
(71027, 'Jesús María', 3450, 'MEX', 142, 'MX', 19.41773000, -99.99696000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q20284223'),
(71028, 'Jesús María', 3461, 'SLP', 142, 'MX', 23.15488000, -102.19253000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q20284223'),
(71029, 'Jesús María', 3449, 'SIN', 142, 'MX', 25.08897000, -107.45220000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q20284223'),
(71030, 'Jesús María', 3477, 'NAY', 142, 'MX', 22.25056000, -104.51792000, '2019-10-05 23:08:48', '2020-05-01 17:23:01', 1, 'Q20284223'),
(71031, 'Jesús María', 3455, 'QUE', 142, 'MX', 20.62274000, -100.25631000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q20284223'),
(71032, 'Jesús María', 3456, 'AGU', 142, 'MX', 21.96667000, -102.35000000, '2019-10-05 23:08:48', '2020-05-01 17:22:59', 1, 'Q931879'),
(71033, 'Jesús María Garza', 3451, 'CHP', 142, 'MX', 16.39669000, -93.29368000, '2019-10-05 23:08:48', '2020-05-01 17:22:59', 1, 'Q931879'),
(71034, 'Jesús Nazareno', 3476, 'PUE', 142, 'MX', 18.87667000, -97.62306000, '2019-10-05 23:08:48', '2020-05-01 17:23:01', 1, 'Q20149182'),
(71035, 'Jesús Tepactepec', 3458, 'TLA', 142, 'MX', 19.22917000, -98.31417000, '2019-10-05 23:08:48', '2020-05-01 17:23:02', 1, 'Q9011693'),
(71036, 'Jesús del Monte', 3450, 'MEX', 142, 'MX', 19.37500000, -99.29444000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q20149010'),
(71037, 'Jesús del Monte', 3474, 'MIC', 142, 'MX', 19.65151000, -101.15227000, '2019-10-05 23:08:48', '2020-05-01 17:23:01', 1, 'Q20149010'),
(71038, 'Jesús del Monte', 3469, 'GUA', 142, 'MX', 20.91504000, -101.74129000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q20149010'),
(71039, 'Jicaltepec Autopan', 3450, 'MEX', 142, 'MX', 19.37167000, -99.64083000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20149255'),
(71040, 'Jicaltepec Cuexcontitlán', 3450, 'MEX', 142, 'MX', 19.37306000, -99.62750000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q20149259'),
(71041, 'Jicarero', 3465, 'MOR', 142, 'MX', 18.61488000, -99.22344000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20149259'),
(71042, 'Jicayán de Tovar', 3459, 'GRO', 142, 'MX', 17.12716000, -98.23622000, '2019-10-05 23:08:48', '2020-05-01 17:23:00', 1, 'Q20149259'),
(71043, 'Jicolapa', 3476, 'PUE', 142, 'MX', 19.96596000, -97.97345000, '2019-10-05 23:08:48', '2019-10-05 23:08:48', 1, 'Q20149259'),
(71044, 'Jilotepec', 3464, 'VER', 142, 'MX', 19.61184000, -96.95169000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20269360'),
(71045, 'Jilotepec de Molina Enríquez', 3450, 'MEX', 142, 'MX', 19.95194000, -99.53278000, '2019-10-05 23:08:49', '2020-05-01 17:23:00', 1, 'Q20149297'),
(71046, 'Jilotzingo', 3450, 'MEX', 142, 'MX', 19.86889000, -99.06194000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20284304'),
(71047, 'Jilotzingo', 3476, 'PUE', 142, 'MX', 20.03250000, -97.90250000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20230322'),
(71048, 'Jiménez', 3454, 'TAB', 142, 'MX', 18.16043000, -92.93336000, '2019-10-05 23:08:49', '2020-05-01 17:23:02', 1, 'Q20230322'),
(71049, 'Jiménez', 3447, 'CHH', 142, 'MX', 28.33333000, -105.40000000, '2019-10-05 23:08:49', '2020-05-01 17:23:00', 1, 'Q27784548'),
(71050, 'Jiménez', 3453, 'DUR', 142, 'MX', 25.83051000, -103.35733000, '2019-10-05 23:08:49', '2020-05-01 17:23:00', 1, 'Q27784548'),
(71051, 'Jiménez', 3471, 'COA', 142, 'MX', 29.06975000, -100.67895000, '2019-10-05 23:08:49', '2020-05-01 17:23:00', 1, 'Q6201745'),
(71052, 'Jiménez', 3463, 'TAM', 142, 'MX', 24.26262000, -98.49656000, '2019-10-05 23:08:49', '2020-05-01 17:23:02', 1, 'Q3850556'),
(71053, 'Jiménez del Teul', 3462, 'ZAC', 142, 'MX', 23.25378000, -103.79855000, '2019-10-05 23:08:49', '2020-05-01 17:23:03', 1, 'Q3850556'),
(71054, 'Jiquilpan (Estación Bonanza)', 3451, 'CHP', 142, 'MX', 15.34194000, -92.74750000, '2019-10-05 23:08:49', '2020-05-01 17:22:59', 1, 'Q20215064'),
(71055, 'Jiquipilas', 3451, 'CHP', 142, 'MX', 16.66806000, -93.64667000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q27769290'),
(71056, 'Jiquipilco', 3450, 'MEX', 142, 'MX', 19.55644000, -99.60833000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q3300374'),
(71057, 'Jiquílpan de Juárez', 3474, 'MIC', 142, 'MX', 19.99482000, -102.71969000, '2019-10-05 23:08:49', '2020-05-01 17:23:01', 1, 'Q2333867'),
(71058, 'Jitonhueca', 3468, 'SON', 142, 'MX', 27.06167000, -109.60500000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20228695'),
(71059, 'Jitotol', 3451, 'CHP', 142, 'MX', 17.06723000, -92.86111000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q27769292'),
(71060, 'Jitzamuri', 3449, 'SIN', 142, 'MX', 26.21508000, -109.26330000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q27769292'),
(71061, 'Jiutepec', 3465, 'MOR', 142, 'MX', 18.88139000, -99.17778000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q984646'),
(71062, 'Joachín', 3464, 'VER', 142, 'MX', 18.63855000, -96.23318000, '2019-10-05 23:08:49', '2020-05-01 17:23:02', 1, 'Q984646'),
(71063, 'Joaquín Herrera', 3455, 'QUE', 142, 'MX', 20.48300000, -100.43854000, '2019-10-05 23:08:49', '2020-05-01 17:23:02', 1, 'Q984646'),
(71064, 'Joaquín Miguel Gutiérrez', 3451, 'CHP', 142, 'MX', 16.37000000, -93.36556000, '2019-10-05 23:08:49', '2020-05-01 17:22:59', 1, 'Q20215075'),
(71065, 'Joaquín Miguel Gutiérrez (Margaritas)', 3451, 'CHP', 142, 'MX', 15.55028000, -93.09306000, '2019-10-05 23:08:49', '2020-05-01 17:22:59', 1, 'Q20284346'),
(71066, 'Jocoqui', 3469, 'GUA', 142, 'MX', 20.58201000, -100.69870000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20284346'),
(71067, 'Jocotitlán', 3450, 'MEX', 142, 'MX', 19.70991000, -99.78867000, '2019-10-05 23:08:49', '2020-05-01 17:23:00', 1, 'Q3241862'),
(71068, 'Jofrito', 3455, 'QUE', 142, 'MX', 20.86154000, -100.41884000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q3241862'),
(71069, 'Jojutla', 3465, 'MOR', 142, 'MX', 18.61472000, -99.18028000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20236046'),
(71070, 'Jolalpan', 3476, 'PUE', 142, 'MX', 18.32495000, -98.84489000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20149551'),
(71071, 'Joljá', 3451, 'CHP', 142, 'MX', 17.20459000, -92.49946000, '2019-10-05 23:08:49', '2020-05-01 17:22:59', 1, 'Q20149551'),
(71072, 'Jolochero 2da. Sección', 3454, 'TAB', 142, 'MX', 18.12000000, -92.79833000, '2019-10-05 23:08:49', '2020-05-01 17:23:02', 1, 'Q20228022'),
(71073, 'Jolotichán', 3459, 'GRO', 142, 'MX', 16.75222000, -98.73056000, '2019-10-05 23:08:49', '2020-05-01 17:23:00', 1, 'Q20284389'),
(71074, 'Jolsibaquil', 3451, 'CHP', 142, 'MX', 17.25972000, -92.48167000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20149601'),
(71075, 'Joltealal', 3451, 'CHP', 142, 'MX', 17.07683000, -92.62163000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20215106'),
(71076, 'Jomanichim', 3451, 'CHP', 142, 'MX', 16.90056000, -92.47222000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20149631'),
(71077, 'Jomulco', 3477, 'NAY', 142, 'MX', 21.10394000, -104.42378000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20149631'),
(71078, 'Jonacapa', 3470, 'HID', 142, 'MX', 20.43358000, -99.53306000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20149631'),
(71079, 'Jonacatepec', 3465, 'MOR', 142, 'MX', 18.68081000, -98.80336000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q3311632'),
(71080, 'Jonotla', 3476, 'PUE', 142, 'MX', 20.03032000, -97.57530000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q5934131'),
(71081, 'Jonuta', 3454, 'TAB', 142, 'MX', 18.08984000, -92.13807000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20149648'),
(71082, 'Jopala', 3476, 'PUE', 142, 'MX', 20.16353000, -97.69285000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q5818509'),
(71083, 'Joquicingo', 3450, 'MEX', 142, 'MX', 19.05032000, -99.53375000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q3308471'),
(71084, 'Jorge Jiménez Cantú', 3450, 'MEX', 142, 'MX', 19.30972000, -98.84667000, '2019-10-05 23:08:49', '2020-05-01 17:23:00', 1, 'Q20149707'),
(71085, 'Jorobas', 3450, 'MEX', 142, 'MX', 19.82389000, -99.24833000, '2019-10-05 23:08:49', '2019-10-05 23:08:49', 1, 'Q20149755'),
(71086, 'Jose Narciso Rovirosa', 3467, 'ROO', 142, 'MX', 18.10620000, -88.72565000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20149755'),
(71087, 'Joshil', 3451, 'CHP', 142, 'MX', 17.24667000, -92.36278000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20284654'),
(71088, 'José Cardel', 3464, 'VER', 142, 'MX', 19.36810000, -96.36951000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q20284654'),
(71089, 'José Colomo', 3454, 'TAB', 142, 'MX', 17.94691000, -92.46659000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q20261575'),
(71090, 'José Esteban Coronado', 3447, 'CHH', 142, 'MX', 26.73722000, -105.15833000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q4594274'),
(71091, 'José F. Gutiérrez', 3464, 'VER', 142, 'MX', 18.02526000, -94.61028000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q4594274'),
(71092, 'José Guadalupe Aguilera (Santa Lucía)', 3453, 'DUR', 142, 'MX', 24.45420000, -104.70888000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20219255'),
(71093, 'José Guadalupe Rodríguez', 3453, 'DUR', 142, 'MX', 24.31912000, -104.08002000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20219255'),
(71094, 'José Mariano Jiménez', 3447, 'CHH', 142, 'MX', 27.13076000, -104.92391000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q3699278'),
(71095, 'José María Morelos', 3448, 'OAX', 142, 'MX', 16.20389000, -97.93000000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q20264248'),
(71096, 'José María Morelos', 3458, 'TLA', 142, 'MX', 19.31750000, -97.97789000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q20264248'),
(71097, 'José María Morelos', 3451, 'CHP', 142, 'MX', 16.04189000, -91.83201000, '2019-10-05 23:08:50', '2020-05-01 17:22:59', 1, 'Q20264248'),
(71098, 'José María Morelos', 3467, 'ROO', 142, 'MX', 19.74887000, -88.70866000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q284406'),
(71099, 'José María Morelos', 3476, 'PUE', 142, 'MX', 19.14147000, -97.46809000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q20230332'),
(71100, 'José María Morelos', 3453, 'DUR', 142, 'MX', 25.01270000, -105.20882000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20230332'),
(71101, 'José María Morelos', 3462, 'ZAC', 142, 'MX', 23.32278000, -103.01444000, '2019-10-05 23:08:50', '2020-05-01 17:23:03', 1, 'Q20221206'),
(71102, 'José María Morelos (La Yegüería)', 3474, 'MIC', 142, 'MX', 19.86389000, -100.87917000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q20239082'),
(71103, 'José María Morelos (San José)', 3470, 'HID', 142, 'MX', 19.68250000, -98.57639000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20242420'),
(71104, 'José María Morelos (Santa Mónica)', 3462, 'ZAC', 142, 'MX', 23.60556000, -103.11028000, '2019-10-05 23:08:50', '2020-05-01 17:23:03', 1, 'Q20221207'),
(71105, 'José María Morelos y Pavón', 3451, 'CHP', 142, 'MX', 17.24105000, -92.70231000, '2019-10-05 23:08:50', '2020-05-01 17:22:59', 1, 'Q20284570'),
(71106, 'José María Morelos y Pavón', 3462, 'ZAC', 142, 'MX', 23.86288000, -103.14193000, '2019-10-05 23:08:50', '2020-05-01 17:23:03', 1, 'Q20284570'),
(71107, 'José María Morelos y Pavón', 3468, 'SON', 142, 'MX', 28.09786000, -110.69030000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q20284570'),
(71108, 'José María Pino Suárez', 3451, 'CHP', 142, 'MX', 16.50571000, -93.73910000, '2019-10-05 23:08:50', '2020-05-01 17:22:59', 1, 'Q20215154'),
(71109, 'José María Pino Suárez', 3453, 'DUR', 142, 'MX', 23.87733000, -104.49104000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20215154'),
(71110, 'José María Pino Suárez', 3470, 'HID', 142, 'MX', 20.24722000, -99.42111000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20242422'),
(71111, 'José Refugio Salcido', 3453, 'DUR', 142, 'MX', 23.96444000, -104.51806000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20284614'),
(71112, 'Juan Aldama', 3451, 'CHP', 142, 'MX', 17.60939000, -93.02826000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20284614'),
(71113, 'Juan Aldama', 3462, 'ZAC', 142, 'MX', 24.29188000, -103.39272000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q6298792'),
(71114, 'Juan Aldama (El Tigre)', 3449, 'SIN', 142, 'MX', 24.93972000, -107.82083000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20259435'),
(71115, 'Juan C. Bonilla', 3476, 'PUE', 142, 'MX', 19.12101000, -98.34091000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q6299095'),
(71116, 'Juan Díaz Covarrubias', 3464, 'VER', 142, 'MX', 18.15964000, -95.18675000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q6299095'),
(71117, 'Juan E. García', 3453, 'DUR', 142, 'MX', 25.49206000, -103.69170000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q6299095'),
(71118, 'Juan Escutia (Borbollón)', 3477, 'NAY', 142, 'MX', 21.16741000, -104.86698000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q20234034'),
(71119, 'Juan Galindo', 3476, 'PUE', 142, 'MX', 20.21980000, -97.99083000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q4501994'),
(71120, 'Juan Jacobo Torres [Bodega de Totontepec]', 3464, 'VER', 142, 'MX', 18.33645000, -95.26033000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20269404'),
(71121, 'Juan José Ríos', 3449, 'SIN', 142, 'MX', 25.75781000, -108.82420000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q13986486'),
(71122, 'Juan Marcos (San José Buenavista)', 3464, 'VER', 142, 'MX', 19.72917000, -97.19667000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q20269406'),
(71123, 'Juan Martín', 3469, 'GUA', 142, 'MX', 20.47301000, -100.74691000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20269406'),
(71124, 'Juan Morales', 3465, 'MOR', 142, 'MX', 18.81139000, -98.92194000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20236053'),
(71125, 'Juan N. Méndez', 3476, 'PUE', 142, 'MX', 18.52411000, -97.74829000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q15153937'),
(71126, 'Juan Rodríguez Clara', 3464, 'VER', 142, 'MX', 17.99283000, -95.40099000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q15153937'),
(71127, 'Juan Sarabia', 3467, 'ROO', 142, 'MX', 18.50035000, -88.48013000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q15153937'),
(71128, 'Juan de la Granja', 3476, 'PUE', 142, 'MX', 19.22118000, -97.80884000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q15153937'),
(71129, 'Juan del Grijalva', 3451, 'CHP', 142, 'MX', 16.72806000, -92.97750000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20215193'),
(71130, 'Juchipila', 3462, 'ZAC', 142, 'MX', 21.40862000, -103.11663000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20221212'),
(71131, 'Juchique de Ferrer', 3464, 'VER', 142, 'MX', 19.83985000, -96.69463000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q3846692'),
(71132, 'Juchitepec', 3450, 'MEX', 142, 'MX', 19.10059000, -98.87880000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q2393522'),
(71133, 'Juchitán de Zaragoza', 3448, 'OAX', 142, 'MX', 16.43603000, -95.01975000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q1710837'),
(71134, 'Jucutacato', 3474, 'MIC', 142, 'MX', 19.37771000, -102.07924000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q1710837'),
(71135, 'Julimes', 3447, 'CHH', 142, 'MX', 28.42376000, -105.42727000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20214043'),
(71136, 'Julián Blanco (Dos Caminos)', 3459, 'GRO', 142, 'MX', 17.21889000, -99.52472000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20285047'),
(71137, 'Julián Grajales', 3451, 'CHP', 142, 'MX', 16.42917000, -93.71417000, '2019-10-05 23:08:50', '2020-05-01 17:22:59', 1, 'Q20215235'),
(71138, 'Julián Villagrán', 3470, 'HID', 142, 'MX', 20.39645000, -99.10431000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20215235'),
(71139, 'Jumiltepec', 3465, 'MOR', 142, 'MX', 18.91359000, -98.77666000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20215235'),
(71140, 'Jungapeo de Juárez', 3474, 'MIC', 142, 'MX', 19.45915000, -100.49483000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q4967875'),
(71141, 'Juraré', 3468, 'SON', 142, 'MX', 26.80191000, -109.70198000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q50414282'),
(71142, 'Juriquilla', 3455, 'QUE', 142, 'MX', 20.71277000, -100.45568000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q50414282'),
(71143, 'Justo Sierra', 3451, 'CHP', 142, 'MX', 16.54248000, -92.05737000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q50414282'),
(71144, 'Juventino Rosas', 3469, 'GUA', 142, 'MX', 20.64337000, -100.99286000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q20207490'),
(71145, 'Juárez', 3451, 'CHP', 142, 'MX', 17.60572000, -93.19433000, '2019-10-05 23:08:50', '2020-05-01 17:22:59', 1, 'Q5957239'),
(71146, 'Juárez', 3447, 'CHH', 142, 'MX', 31.72024000, -106.46084000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q26590'),
(71147, 'Juárez Coronaco', 3476, 'PUE', 142, 'MX', 19.36056000, -98.52500000, '2019-10-05 23:08:50', '2020-05-01 17:23:01', 1, 'Q20230341'),
(71148, 'Jáltipan de Morelos', 3464, 'VER', 142, 'MX', 17.96542000, -94.71396000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q20230341'),
(71149, 'Jícamas', 3469, 'GUA', 142, 'MX', 20.27602000, -101.35916000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20230341'),
(71150, 'Kanasín', 3466, 'YUC', 142, 'MX', 20.93482000, -89.55871000, '2019-10-05 23:08:50', '2020-05-01 17:23:03', 1, 'Q613003'),
(71151, 'Kancab', 3466, 'YUC', 142, 'MX', 20.19571000, -89.34563000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q613003'),
(71152, 'Kancabchén', 3467, 'ROO', 142, 'MX', 19.71492000, -88.86211000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q613003'),
(71153, 'Kantunil', 3466, 'YUC', 142, 'MX', 20.79645000, -89.03539000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q5957979'),
(71154, 'Kantunilkín', 3467, 'ROO', 142, 'MX', 21.10182000, -87.48644000, '2019-10-05 23:08:50', '2020-05-01 17:23:02', 1, 'Q1709319'),
(71155, 'Kanxoc', 3466, 'YUC', 142, 'MX', 20.61555000, -88.09764000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q1709319'),
(71156, 'Kaua', 3466, 'YUC', 142, 'MX', 20.62476000, -88.42440000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q2466571'),
(71157, 'Kilómetro 30', 3459, 'GRO', 142, 'MX', 16.99778000, -99.78000000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q2876014'),
(71158, 'Kilómetro 40', 3459, 'GRO', 142, 'MX', 17.04944000, -99.77056000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20285304'),
(71159, 'Kilómetro Noventa y Nueve', 3447, 'CHH', 142, 'MX', 28.12668000, -105.58018000, '2019-10-05 23:08:50', '2020-05-01 17:23:00', 1, 'Q20285304'),
(71160, 'Kimbila', 3466, 'YUC', 142, 'MX', 20.93403000, -89.12470000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q2887461'),
(71161, 'Kinchil', 3466, 'YUC', 142, 'MX', 20.91519000, -89.94825000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q5478261'),
(71162, 'Kini', 3466, 'YUC', 142, 'MX', 21.13557000, -89.31691000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q5478261'),
(71163, 'Kinil', 3466, 'YUC', 142, 'MX', 20.32379000, -89.13285000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q5959874'),
(71164, 'Kochol', 3466, 'YUC', 142, 'MX', 20.61867000, -90.15906000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q5959874'),
(71165, 'Komchén', 3466, 'YUC', 142, 'MX', 21.10342000, -89.66242000, '2019-10-05 23:08:50', '2020-05-01 17:23:03', 1, 'Q1770500'),
(71166, 'Kopoma', 3466, 'YUC', 142, 'MX', 20.64906000, -89.89989000, '2019-10-05 23:08:50', '2019-10-05 23:08:50', 1, 'Q1770500'),
(71168, 'Kotolte', 3451, 'CHP', 142, 'MX', 16.86444000, -92.46111000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20285438'),
(71169, 'La Albarrada (San Francisco la Albarrada)', 3450, 'MEX', 142, 'MX', 19.06750000, -100.07528000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20238575'),
(71170, 'La Aldea', 3469, 'GUA', 142, 'MX', 20.90166000, -101.47852000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20285593'),
(71171, 'La Aldea', 3474, 'MIC', 142, 'MX', 19.74306000, -101.13500000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20239140'),
(71172, 'La Amistad', 3470, 'HID', 142, 'MX', 20.00833000, -99.32028000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20242435'),
(71173, 'La Angostura', 3474, 'MIC', 142, 'MX', 20.19639000, -102.44167000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20285739'),
(71174, 'La Angostura', 3469, 'GUA', 142, 'MX', 20.27733000, -101.11005000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20285739'),
(71175, 'La Antigua', 3464, 'VER', 142, 'MX', 19.31619000, -96.31736000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q1002990'),
(71176, 'La Ascensión', 3452, 'NLE', 142, 'MX', 24.32392000, -99.91410000, '2019-10-05 23:08:51', '2020-05-01 17:23:01', 1, 'Q1002990'),
(71177, 'La Aurora', 3458, 'TLA', 142, 'MX', 19.25680000, -98.22320000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q1002990'),
(71178, 'La Aurora', 3469, 'GUA', 142, 'MX', 20.62667000, -100.75750000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20285931'),
(71179, 'La Azozuca', 3459, 'GRO', 142, 'MX', 16.82139000, -99.04194000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20286020'),
(71180, 'La Ballena', 3462, 'ZAC', 142, 'MX', 22.45572000, -101.70809000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20286020'),
(71181, 'La Barra de Colotepec', 3448, 'OAX', 142, 'MX', 15.82546000, -97.02882000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20286020'),
(71182, 'La Barreta', 3455, 'QUE', 142, 'MX', 20.82806000, -100.50647000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20286020'),
(71183, 'La Blanca', 3448, 'OAX', 142, 'MX', 16.59482000, -94.69256000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264380'),
(71184, 'La Bocana', 3468, 'SON', 142, 'MX', 26.88711000, -109.66797000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q50415358'),
(71185, 'La Boveda', 3470, 'HID', 142, 'MX', 20.12496000, -98.12468000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q50415358'),
(71186, 'La Brecha', 3449, 'SIN', 142, 'MX', 25.37078000, -108.41922000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q129915'),
(71187, 'La Cabecera', 3450, 'MEX', 142, 'MX', 19.35611000, -99.74806000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20238611'),
(71188, 'La Cabecera Concepción', 3450, 'MEX', 142, 'MX', 19.70361000, -99.94111000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20286483'),
(71189, 'La Caja', 3469, 'GUA', 142, 'MX', 20.71146000, -101.43769000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20286483'),
(71190, 'La Calera', 3469, 'GUA', 142, 'MX', 20.80119000, -101.33207000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207583'),
(71191, 'La Calera (Nacimientos)', 3474, 'MIC', 142, 'MX', 20.27528000, -101.57556000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20239236'),
(71192, 'La Calle', 3450, 'MEX', 142, 'MX', 20.28628000, -101.63679000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20239236'),
(71193, 'La Camelia', 3464, 'VER', 142, 'MX', 20.89631000, -97.77247000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20239236'),
(71194, 'La Campanilla', 3450, 'MEX', 142, 'MX', 19.50983000, -99.94230000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20239236'),
(71195, 'La Candelaria', 3451, 'CHP', 142, 'MX', 16.74289000, -92.52156000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20239236'),
(71196, 'La Candelaria', 3448, 'OAX', 142, 'MX', 17.21833000, -95.93306000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20286898'),
(71197, 'La Candelaria Teotlalpan', 3458, 'TLA', 142, 'MX', 19.33306000, -98.22556000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264226'),
(71198, 'La Candelaria Tlapala', 3450, 'MEX', 142, 'MX', 19.24042000, -98.84766000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264226'),
(71199, 'La Cantera', 3450, 'MEX', 142, 'MX', 19.86375000, -102.40838000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264226'),
(71200, 'La Cantera', 3477, 'NAY', 142, 'MX', 21.50308000, -104.82237000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264226'),
(71201, 'La Capilla', 3464, 'VER', 142, 'MX', 18.89442000, -96.24132000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264226'),
(71202, 'La Capilla', 3462, 'ZAC', 142, 'MX', 22.68642000, -102.23750000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264226'),
(71203, 'La Capilla', 3469, 'GUA', 142, 'MX', 20.55655000, -101.30019000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207631'),
(71204, 'La Caridad', 3450, 'MEX', 142, 'MX', 19.94778000, -99.83694000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20238650'),
(71205, 'La Cañada', 3476, 'PUE', 142, 'MX', 19.49898000, -97.73400000, '2019-10-05 23:08:51', '2020-05-01 17:23:01', 1, 'Q20238650'),
(71206, 'La Cañada', 3455, 'QUE', 142, 'MX', 20.60805000, -100.33275000, '2019-10-05 23:08:51', '2020-05-01 17:23:02', 1, 'Q20246402'),
(71207, 'La Cañada', 3450, 'MEX', 142, 'MX', 19.48639000, -99.59889000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20238637'),
(71208, 'La Cañada', 3448, 'OAX', 142, 'MX', 17.07000000, -96.77806000, '2019-10-05 23:08:51', '2020-05-01 17:23:01', 1, 'Q20264429'),
(71209, 'La Ceiba', 3454, 'TAB', 142, 'MX', 18.14436000, -92.77805000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264429'),
(71210, 'La Ceiba', 3451, 'CHP', 142, 'MX', 17.13371000, -92.51887000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264429'),
(71211, 'La Ceja', 3455, 'QUE', 142, 'MX', 20.38026000, -100.26505000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264429'),
(71212, 'La Central', 3472, 'COL', 142, 'MX', 19.14276000, -104.43819000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264429'),
(71213, 'La Cerquilla', 3464, 'VER', 142, 'MX', 17.90786000, -95.05497000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264429'),
(71214, 'La Cieneguita', 3469, 'GUA', 142, 'MX', 20.94630000, -100.78966000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264429'),
(71215, 'La Ciudad', 3453, 'DUR', 142, 'MX', 23.73052000, -105.68882000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20219425'),
(71216, 'La Ciénega', 3469, 'GUA', 142, 'MX', 21.30050000, -100.48027000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20219425'),
(71217, 'La Ciénega', 3450, 'MEX', 142, 'MX', 18.95611000, -99.58139000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20238678'),
(71218, 'La Colonia', 3463, 'TAM', 142, 'MX', 22.43880000, -98.01729000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q27775117'),
(71219, 'La Colonia', 3470, 'HID', 142, 'MX', 20.05472000, -98.77028000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20242483'),
(71220, 'La Colonia', 3450, 'MEX', 142, 'MX', 19.14444000, -98.78667000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20287902'),
(71221, 'La Colonia Guadalupe', 3464, 'VER', 142, 'MX', 20.34387000, -97.62223000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20287902'),
(71222, 'La Colorada', 3468, 'SON', 142, 'MX', 28.80348000, -110.57994000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q1798725'),
(71223, 'La Compañia', 3476, 'PUE', 142, 'MX', 18.97080000, -97.72280000, '2019-10-05 23:08:51', '2020-05-01 17:23:01', 1, 'Q1798725'),
(71224, 'La Compañía', 3469, 'GUA', 142, 'MX', 20.37760000, -101.15852000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q1798725'),
(71225, 'La Compañía', 3450, 'MEX', 142, 'MX', 19.16460000, -100.07966000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q1798725'),
(71226, 'La Competencia', 3451, 'CHP', 142, 'MX', 17.23588000, -92.75718000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q1798725'),
(71227, 'La Comunidad', 3450, 'MEX', 142, 'MX', 20.02111000, -99.57611000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20287965'),
(71228, 'La Concepción', 3459, 'GRO', 142, 'MX', 16.87867000, -99.65140000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20287965'),
(71229, 'La Concepción', 3464, 'VER', 142, 'MX', 19.60620000, -96.90016000, '2019-10-05 23:08:51', '2020-05-01 17:23:02', 1, 'Q20287965'),
(71230, 'La Concepción', 3449, 'SIN', 142, 'MX', 23.39450000, -106.16890000, '2019-10-05 23:08:51', '2020-05-01 17:23:02', 1, 'Q20287965'),
(71231, 'La Concepción', 3462, 'ZAC', 142, 'MX', 22.51574000, -102.26604000, '2019-10-05 23:08:51', '2020-05-01 17:23:03', 1, 'Q20287965'),
(71232, 'La Concepción', 3456, 'AGU', 142, 'MX', 22.03537000, -102.29989000, '2019-10-05 23:08:51', '2020-05-01 17:22:59', 1, 'Q20287965'),
(71233, 'La Concepción', 3450, 'MEX', 142, 'MX', 19.69611000, -99.30028000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20238713'),
(71234, 'La Concepción (La Concha)', 3459, 'GRO', 142, 'MX', 18.76778000, -99.69250000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20288007'),
(71235, 'La Concepción Coatipac (La Conchita)', 3450, 'MEX', 142, 'MX', 19.18188000, -99.56833000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20238701'),
(71236, 'La Concepción Enyege', 3450, 'MEX', 142, 'MX', 19.56472000, -99.84139000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20287978'),
(71237, 'La Concepción Xochicuautla', 3450, 'MEX', 142, 'MX', 19.38226000, -99.44050000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20287978'),
(71238, 'La Concepción de Hidalgo', 3450, 'MEX', 142, 'MX', 19.45054000, -99.53139000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20287978'),
(71239, 'La Concepción de los Baños', 3450, 'MEX', 142, 'MX', 19.68249000, -99.86104000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20238702'),
(71240, 'La Concha', 3471, 'COA', 142, 'MX', 25.63524000, -103.38136000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20238702'),
(71241, 'La Concha (La Concepción)', 3449, 'SIN', 142, 'MX', 22.53250000, -105.45250000, '2019-10-05 23:08:51', '2020-05-01 17:23:02', 1, 'Q20288033'),
(71242, 'La Concordia', 3451, 'CHP', 142, 'MX', 16.11774000, -92.69018000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q2007922'),
(71243, 'La Concordia', 3459, 'GRO', 142, 'MX', 16.90583000, -98.97583000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207661'),
(71244, 'La Constitución', 3447, 'CHH', 142, 'MX', 29.92077000, -106.79620000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20207661'),
(71245, 'La Constitución Toltepec', 3450, 'MEX', 142, 'MX', 19.34861000, -99.54917000, '2019-10-05 23:08:51', '2020-05-01 17:23:00', 1, 'Q20288120'),
(71246, 'La Corregidora', 3477, 'NAY', 142, 'MX', 21.44417000, -104.81222000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20288182'),
(71247, 'La Cruz', 3470, 'HID', 142, 'MX', 20.17603000, -99.24156000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20242503'),
(71248, 'La Cruz', 3471, 'COA', 142, 'MX', 26.99339000, -101.49580000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20242503'),
(71249, 'La Cruz', 3447, 'CHH', 142, 'MX', 27.86359000, -105.19596000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q3383260'),
(71250, 'La Cruz', 3449, 'SIN', 142, 'MX', 23.92126000, -106.89250000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q2012391'),
(71251, 'La Cruz', 3469, 'GUA', 142, 'MX', 20.45694000, -100.79583000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207715'),
(71252, 'La Cruz del Palmar', 3469, 'GUA', 142, 'MX', 20.96973000, -100.84354000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207715'),
(71253, 'La Cruz y Carrizal', 3450, 'MEX', 142, 'MX', 19.83021000, -99.42168000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207715'),
(71254, 'La Cuesta', 3464, 'VER', 142, 'MX', 18.79028000, -97.16806000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20269663'),
(71255, 'La Cuevita', 3469, 'GUA', 142, 'MX', 20.33872000, -100.57891000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20269663'),
(71256, 'La Curva', 3454, 'TAB', 142, 'MX', 17.86604000, -92.48814000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20269663'),
(71257, 'La D', 3455, 'QUE', 142, 'MX', 20.44046000, -100.15296000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20269663'),
(71258, 'La Defensa', 3464, 'VER', 142, 'MX', 20.10507000, -96.96079000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20269680'),
(71259, 'La Despensa', 3449, 'SIN', 142, 'MX', 25.99137000, -109.27274000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20269680'),
(71260, 'La Dicha', 3459, 'GRO', 142, 'MX', 16.76720000, -99.01058000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207691'),
(71261, 'La Encarnación', 3474, 'MIC', 142, 'MX', 19.41242000, -100.39211000, '2019-10-05 23:08:51', '2020-05-01 17:23:01', 1, 'Q20207691'),
(71262, 'La Ermita', 3469, 'GUA', 142, 'MX', 21.15194000, -101.73306000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20207750'),
(71263, 'La Ermita (Nueva Jerusalén)', 3474, 'MIC', 142, 'MX', 19.07444000, -101.50667000, '2019-10-05 23:08:51', '2020-05-01 17:23:01', 1, 'Q20289005'),
(71264, 'La Erradura', 3448, 'OAX', 142, 'MX', 15.83402000, -96.33478000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20264749'),
(71265, 'La Escalera', 3454, 'TAB', 142, 'MX', 17.84361000, -92.52556000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20228063'),
(71266, 'La Escondida', 3469, 'GUA', 142, 'MX', 21.68723000, -101.53446000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20228063'),
(71267, 'La Escondida', 3456, 'AGU', 142, 'MX', 22.03573000, -102.25931000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20228063'),
(71268, 'La Esmeralda', 3471, 'COA', 142, 'MX', 27.28735000, -103.66283000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20228063'),
(71269, 'La Esperanza', 3459, 'GRO', 142, 'MX', 17.76022000, -99.24726000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20228063'),
(71270, 'La Esperanza', 3450, 'MEX', 142, 'MX', 19.68377000, -99.51575000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20228063'),
(71271, 'La Esperanza', 3451, 'CHP', 142, 'MX', 16.15191000, -91.86874000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20228063'),
(71272, 'La Esperanza', 3461, 'SLP', 142, 'MX', 22.00788000, -100.76496000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20259510'),
(71273, 'La Esperanza', 3471, 'COA', 142, 'MX', 25.56631000, -103.28123000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20259510'),
(71274, 'La Esperanza', 3449, 'SIN', 142, 'MX', 25.34872000, -108.18678000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20259510'),
(71275, 'La Esperanza', 3474, 'MIC', 142, 'MX', 20.05559000, -102.41104000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20259510'),
(71276, 'La Esperanza (El Zapotal)', 3451, 'CHP', 142, 'MX', 15.44917000, -93.16417000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20215764'),
(71277, 'La Esperanza [Granjas Familiares]', 3457, 'BCN', 142, 'MX', 32.46278000, -117.10500000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20247738'),
(71278, 'La Esquina', 3470, 'HID', 142, 'MX', 20.54712000, -99.70770000, '2019-10-05 23:08:51', '2019-10-05 23:08:51', 1, 'Q20247738'),
(71279, 'La Estacada', 3455, 'QUE', 142, 'MX', 20.82151000, -100.40763000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289591'),
(71280, 'La Estación', 3459, 'GRO', 142, 'MX', 16.76734000, -99.66716000, '2019-10-05 23:08:52', '2020-05-01 17:23:00', 1, 'Q20289591'),
(71281, 'La Estancia', 3455, 'QUE', 142, 'MX', 20.42123000, -100.06616000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20246444'),
(71282, 'La Estancia', 3470, 'HID', 142, 'MX', 20.29889000, -98.68944000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289656'),
(71283, 'La Estancia Sector Uno (La Estancia)', 3450, 'MEX', 142, 'MX', 19.87056000, -99.79167000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289643'),
(71284, 'La Estancia de Amezcua', 3474, 'MIC', 142, 'MX', 20.00735000, -102.24487000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289643'),
(71285, 'La Estancia de San José del Carmen', 3469, 'GUA', 142, 'MX', 20.29555000, -100.86083000, '2019-10-05 23:08:52', '2020-05-01 17:23:00', 1, 'Q20289643'),
(71286, 'La Estanzuela', 3470, 'HID', 142, 'MX', 20.17828000, -98.75703000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289643'),
(71287, 'La Estrella', 3454, 'TAB', 142, 'MX', 18.60302000, -92.58256000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289643'),
(71288, 'La Estrella', 3469, 'GUA', 142, 'MX', 20.39050000, -101.92109000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289643'),
(71289, 'La Fe', 3471, 'COA', 142, 'MX', 25.84743000, -103.20117000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289643'),
(71290, 'La Finca', 3450, 'MEX', 142, 'MX', 18.89047000, -99.64028000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20289919'),
(71291, 'La Flor', 3453, 'DUR', 142, 'MX', 25.68138000, -103.36837000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20219531'),
(71292, 'La Floresta', 3451, 'CHP', 142, 'MX', 16.50915000, -92.31251000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20219531'),
(71293, 'La Florida', 3471, 'COA', 142, 'MX', 25.76811000, -103.32424000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20219531'),
(71294, 'La Florida', 3462, 'ZAC', 142, 'MX', 23.80474000, -103.07648000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20219531'),
(71295, 'La Fortuna', 3477, 'NAY', 142, 'MX', 21.55285000, -104.95077000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20219531'),
(71296, 'La Fuente', 3455, 'QUE', 142, 'MX', 20.55255000, -100.03591000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20239735'),
(71297, 'La Fundición (Quinta Manzana)', 3474, 'MIC', 142, 'MX', 19.45528000, -100.30361000, '2019-10-05 23:08:52', '2020-05-01 17:23:01', 1, 'Q20290290'),
(71298, 'La Galarza', 3476, 'PUE', 142, 'MX', 18.66778000, -98.45528000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q15241114'),
(71299, 'La Gavia', 3469, 'GUA', 142, 'MX', 20.39431000, -100.88122000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q15241114'),
(71300, 'La Gloria', 3464, 'VER', 142, 'MX', 19.42631000, -96.40211000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q15241114'),
(71301, 'La Gloria', 3476, 'PUE', 142, 'MX', 19.39561000, -97.28246000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q2427917'),
(71302, 'La Gloria', 3451, 'CHP', 142, 'MX', 15.96389000, -91.96500000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20290419'),
(71303, 'La Glorieta', 3450, 'MEX', 142, 'MX', 19.41154000, -99.38562000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20238803'),
(71304, 'La Goleta', 3474, 'MIC', 142, 'MX', 19.73904000, -101.08127000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20238803'),
(71305, 'La Goma', 3453, 'DUR', 142, 'MX', 25.48460000, -103.68884000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20248348'),
(71306, 'La Gotera', 3455, 'QUE', 142, 'MX', 20.86319000, -100.37575000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20248348'),
(71307, 'La Grandeza', 3451, 'CHP', 142, 'MX', 15.51033000, -92.22603000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q48535418'),
(71308, 'La Griega', 3455, 'QUE', 142, 'MX', 20.66642000, -100.24012000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q48535418'),
(71309, 'La Guadalupana', 3450, 'MEX', 142, 'MX', 19.55414000, -99.90922000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q48535418'),
(71310, 'La Guadalupe', 3459, 'GRO', 142, 'MX', 16.68883000, -98.26222000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20250596'),
(71311, 'La Guadalupe', 3464, 'VER', 142, 'MX', 20.37336000, -96.91925000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20250596'),
(71312, 'La Guásima', 3477, 'NAY', 142, 'MX', 22.40607000, -105.38767000, '2019-10-05 23:08:52', '2020-05-01 17:23:01', 1, 'Q20250596'),
(71313, 'La Guásima', 3464, 'VER', 142, 'MX', 20.53973000, -97.24950000, '2019-10-05 23:08:52', '2020-05-01 17:23:02', 1, 'Q20250596'),
(71314, 'La Herradura', 3461, 'SLP', 142, 'MX', 21.44250000, -98.94917000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20259554'),
(71315, 'La Herradura', 3450, 'MEX', 142, 'MX', 19.04000000, -99.58472000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20250900'),
(71316, 'La Higuerilla (Los Lirios)', 3474, 'MIC', 142, 'MX', 19.92250000, -100.27639000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20250983'),
(71317, 'La Higuerita (Colonia San Rafael)', 3474, 'MIC', 142, 'MX', 19.61861000, -102.48444000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20239725'),
(71318, 'La Horqueta (Poblado Doce)', 3464, 'VER', 142, 'MX', 17.21012000, -94.20249000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20239725'),
(71319, 'La Huacana', 3474, 'MIC', 142, 'MX', 18.96312000, -101.80720000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20290874'),
(71320, 'La Huanica', 3465, 'MOR', 142, 'MX', 19.45956000, -99.50079000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20290874'),
(71321, 'La Humedad', 3448, 'OAX', 142, 'MX', 16.58222000, -97.66724000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20290874'),
(71322, 'La Ibérica (La Gotera)', 3474, 'MIC', 142, 'MX', 19.16472000, -101.91611000, '2019-10-05 23:08:52', '2020-05-01 17:23:01', 1, 'Q20251158'),
(71323, 'La Independencia', 3451, 'CHP', 142, 'MX', 16.25263000, -92.02371000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q48820340'),
(71324, 'La Independencia (Las Pilas)', 3451, 'CHP', 142, 'MX', 16.67750000, -93.24944000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20216074'),
(71325, 'La Isla', 3464, 'VER', 142, 'MX', 18.60000000, -96.15000000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20269861'),
(71326, 'La Isla (Kilómetro 10)', 3464, 'VER', 142, 'MX', 20.60806000, -97.50139000, '2019-10-05 23:08:52', '2020-05-01 17:23:02', 1, 'Q20251261'),
(71327, 'La Isla Km 10', 3464, 'VER', 142, 'MX', 20.60677000, -97.49950000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251261'),
(71328, 'La Isla de Chapachapa', 3464, 'VER', 142, 'MX', 20.12186000, -96.88208000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251261'),
(71329, 'La Joya', 3464, 'VER', 142, 'MX', 19.61099000, -97.02687000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20269867'),
(71330, 'La Joya', 3465, 'MOR', 142, 'MX', 18.66667000, -99.46667000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20264319'),
(71331, 'La Joya', 3475, 'CAM', 142, 'MX', 19.48028000, -90.67333000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251416'),
(71332, 'La Joya', 3453, 'DUR', 142, 'MX', 23.83750000, -103.99861000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20219630'),
(71333, 'La Joya', 3450, 'MEX', 142, 'MX', 19.27639000, -99.75889000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20238842'),
(71334, 'La Joya de Calvillo', 3469, 'GUA', 142, 'MX', 20.74883000, -101.60847000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20238842'),
(71335, 'La Junta', 3464, 'VER', 142, 'MX', 18.49136000, -96.65720000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20238842'),
(71336, 'La Junta', 3447, 'CHH', 142, 'MX', 28.48003000, -107.32948000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q4251764'),
(71337, 'La Junta', 3448, 'OAX', 142, 'MX', 17.82778000, -97.74556000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251578'),
(71338, 'La Labor', 3469, 'GUA', 142, 'MX', 20.53388000, -100.70200000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251616'),
(71339, 'La Labor', 3477, 'NAY', 142, 'MX', 21.37299000, -104.71910000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251616'),
(71340, 'La Labor', 3456, 'AGU', 142, 'MX', 21.96207000, -102.69626000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251621'),
(71341, 'La Laborcita', 3469, 'GUA', 142, 'MX', 21.10636000, -101.55170000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20251621'),
(71342, 'La Laguna', 3470, 'HID', 142, 'MX', 19.73447000, -98.49989000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q56527491'),
(71343, 'La Laguna', 3451, 'CHP', 142, 'MX', 16.69357000, -91.94929000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q56527491'),
(71344, 'La Laguna', 3464, 'VER', 142, 'MX', 19.48214000, -96.93366000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q56527491'),
(71345, 'La Laguna', 3462, 'ZAC', 142, 'MX', 24.21351000, -103.18280000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q56527491'),
(71346, 'La Laguna', 3476, 'PUE', 142, 'MX', 18.79944000, -97.73667000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20230535'),
(71347, 'La Laguna y Monte del Castillo', 3464, 'VER', 142, 'MX', 18.98334000, -96.07806000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20226680'),
(71348, 'La Lagunilla', 3470, 'HID', 142, 'MX', 20.06787000, -98.47801000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20226680'),
(71349, 'La Laja', 3455, 'QUE', 142, 'MX', 20.57468000, -99.98070000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20226680'),
(71350, 'La Laja', 3469, 'GUA', 142, 'MX', 20.52946000, -100.76170000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20226680'),
(71351, 'La Libertad', 3451, 'CHP', 142, 'MX', 14.59167000, -92.19315000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20216167'),
(71352, 'La Libertad', 3463, 'TAM', 142, 'MX', 23.79079000, -99.19132000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20216167'),
(71353, 'La Libertad', 3464, 'VER', 142, 'MX', 20.05345000, -96.97314000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20216167'),
(71354, 'La Libertad', 3475, 'CAM', 142, 'MX', 18.57042000, -90.51399000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20216167'),
(71355, 'La Libertad', 3477, 'NAY', 142, 'MX', 21.59347000, -105.17365000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20234170'),
(71356, 'La Libertad', 3468, 'SON', 142, 'MX', 29.91313000, -112.69179000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q6092093'),
(71357, 'La Libertad', 3476, 'PUE', 142, 'MX', 19.75583000, -97.60722000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20262372'),
(71358, 'La Lima', 3454, 'TAB', 142, 'MX', 17.91056000, -92.93389000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20252259'),
(71359, 'La Lima', 3461, 'SLP', 142, 'MX', 21.92722000, -99.10278000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20259591'),
(71360, 'La Lira', 3455, 'QUE', 142, 'MX', 20.47554000, -100.16145000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20239766'),
(71361, 'La Llave', 3455, 'QUE', 142, 'MX', 20.46628000, -99.99373000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20246456'),
(71362, 'La Lobera', 3469, 'GUA', 142, 'MX', 20.35815000, -101.54030000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20246456'),
(71363, 'La Loma', 3470, 'HID', 142, 'MX', 20.16959000, -99.37882000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20246456'),
(71364, 'La Loma', 3453, 'DUR', 142, 'MX', 25.46401000, -103.67351000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20246456'),
(71365, 'La Loma', 3450, 'MEX', 142, 'MX', 20.01966000, -100.01027000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20246456'),
(71366, 'La Loma', 3455, 'QUE', 142, 'MX', 20.60000000, -100.23889000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20239772'),
(71367, 'La Loma (La Loma de Quila)', 3449, 'SIN', 142, 'MX', 24.38061000, -107.22649000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291811'),
(71368, 'La Loma Cuexcontitlán', 3450, 'MEX', 142, 'MX', 19.38000000, -99.64889000, '2019-10-05 23:08:52', '2020-05-01 17:23:00', 1, 'Q20238873'),
(71369, 'La Loma de los Negritos', 3456, 'AGU', 142, 'MX', 21.87056000, -102.35000000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291739'),
(71370, 'La Lucha', 3454, 'TAB', 142, 'MX', 18.18069000, -93.32176000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291739'),
(71371, 'La Luz', 3448, 'OAX', 142, 'MX', 16.11477000, -97.59570000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291739'),
(71372, 'La Luz', 3462, 'ZAC', 142, 'MX', 22.88000000, -102.31028000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20221430'),
(71373, 'La Luz', 3453, 'DUR', 142, 'MX', 25.63481000, -103.61093000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20221430'),
(71374, 'La Luz', 3471, 'COA', 142, 'MX', 25.72621000, -103.24540000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20221430'),
(71375, 'La Luz', 3474, 'MIC', 142, 'MX', 20.15234000, -102.48528000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20221430'),
(71376, 'La Luz', 3469, 'GUA', 142, 'MX', 20.55386000, -101.18097000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20221430'),
(71377, 'La Luz', 3455, 'QUE', 142, 'MX', 20.86857000, -100.43839000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20246465'),
(71378, 'La Luz de Juárez', 3459, 'GRO', 142, 'MX', 17.53500000, -98.34833000, '2019-10-05 23:08:52', '2020-05-01 17:23:00', 1, 'Q20291934'),
(71379, 'La Línea', 3451, 'CHP', 142, 'MX', 16.13078000, -94.03563000, '2019-10-05 23:08:52', '2020-05-01 17:22:59', 1, 'Q20291934'),
(71380, 'La Magdalena', 3476, 'PUE', 142, 'MX', 20.25698000, -98.27534000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291934'),
(71381, 'La Magdalena', 3450, 'MEX', 142, 'MX', 19.94796000, -99.99947000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291934'),
(71382, 'La Magdalena Chichicaspa', 3450, 'MEX', 142, 'MX', 19.41237000, -99.32555000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q21209740'),
(71383, 'La Magdalena Tenexpan', 3450, 'MEX', 142, 'MX', 19.46333000, -99.58861000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291963'),
(71384, 'La Magdalena Tetela Morelos', 3476, 'PUE', 142, 'MX', 19.04922000, -97.94131000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20291963'),
(71385, 'La Magdalena Tlaltelulco', 3458, 'TLA', 142, 'MX', 19.28287000, -98.19609000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20224581'),
(71386, 'La Magdalena Yancuitlalpan', 3476, 'PUE', 142, 'MX', 18.91083000, -98.58944000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20262378'),
(71387, 'La Magdalena de los Reyes (La Magdalena)', 3450, 'MEX', 142, 'MX', 19.18972000, -99.41000000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20238912'),
(71388, 'La Manga 2da. Sección (El Jobal)', 3454, 'TAB', 142, 'MX', 18.01917000, -92.86944000, '2019-10-05 23:08:52', '2020-05-01 17:23:02', 1, 'Q20228104'),
(71389, 'La Mesa', 3450, 'MEX', 142, 'MX', 19.58748000, -100.17373000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20228104'),
(71390, 'La Mesa', 3468, 'SON', 142, 'MX', 31.15972000, -110.97444000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20262271'),
(71391, 'La Mesa (La Mesa de Cedano)', 3474, 'MIC', 142, 'MX', 19.44861000, -100.32722000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20253474'),
(71392, 'La Mesa de Chosto', 3450, 'MEX', 142, 'MX', 19.85250000, -99.89028000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20292215'),
(71393, 'La Mesa de los Conejos', 3461, 'SLP', 142, 'MX', 22.10316000, -101.05309000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20292215'),
(71394, 'La Mesilla', 3470, 'HID', 142, 'MX', 20.48949000, -99.67676000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20292215'),
(71395, 'La Mina', 3448, 'OAX', 142, 'MX', 17.98689000, -96.09978000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20292215'),
(71396, 'La Mintzita (Piedra Dura)', 3474, 'MIC', 142, 'MX', 19.65194000, -101.27750000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20292480'),
(71397, 'La Mira', 3474, 'MIC', 142, 'MX', 18.03349000, -102.32566000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20292480'),
(71398, 'La Mira Tumbiscatio', 3474, 'MIC', 142, 'MX', 18.68333000, -102.28333000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20292488'),
(71399, 'La Misión', 3463, 'TAM', 142, 'MX', 23.80326000, -99.17025000, '2019-10-05 23:08:52', '2020-05-01 17:23:02', 1, 'Q15241140'),
(71400, 'La Mohonera', 3459, 'GRO', 142, 'MX', 17.63839000, -99.20222000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20207919'),
(71401, 'La Mojonera', 3474, 'MIC', 142, 'MX', 19.70366000, -101.83343000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q20207919'),
(71402, 'La Moncada', 3469, 'GUA', 142, 'MX', 20.28348000, -100.80502000, '2019-10-05 23:08:52', '2019-10-05 23:08:52', 1, 'Q4251713'),
(71403, 'La Monja', 3455, 'QUE', 142, 'MX', 20.84275000, -100.49754000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q4251713'),
(71404, 'La Negreta', 3455, 'QUE', 142, 'MX', 20.52647000, -100.45053000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q4251713'),
(71405, 'La Noria', 3449, 'SIN', 142, 'MX', 25.70909000, -108.49185000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q4251713'),
(71406, 'La Noria', 3455, 'QUE', 142, 'MX', 20.51261000, -100.34610000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q4251713'),
(71407, 'La Noria', 3448, 'OAX', 142, 'MX', 16.25444000, -95.22056000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20264981'),
(71408, 'La Noria de San Antonio', 3449, 'SIN', 142, 'MX', 23.50653000, -106.31459000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20264981'),
(71409, 'La Norita', 3469, 'GUA', 142, 'MX', 20.55409000, -100.51660000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20264981'),
(71410, 'La Nueva Era', 3464, 'VER', 142, 'MX', 17.69861000, -95.80833000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20128665'),
(71411, 'La Nueva Victoria', 3464, 'VER', 142, 'MX', 18.65639000, -95.25806000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129072'),
(71412, 'La Ordeña', 3469, 'GUA', 142, 'MX', 20.68988000, -101.11830000, '2019-10-05 23:08:53', '2020-05-01 17:23:00', 1, 'Q20129072'),
(71413, 'La Orduña', 3464, 'VER', 142, 'MX', 19.45141000, -96.93598000, '2019-10-05 23:08:53', '2020-05-01 17:23:02', 1, 'Q20129072'),
(71414, 'La Orilla', 3474, 'MIC', 142, 'MX', 17.99583000, -102.22694000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129255'),
(71415, 'La Ortiga', 3469, 'GUA', 142, 'MX', 19.96222000, -101.00056000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129271'),
(71416, 'La Pahua', 3476, 'PUE', 142, 'MX', 20.61181000, -97.85358000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129271'),
(71417, 'La Palma', 3454, 'TAB', 142, 'MX', 17.97980000, -92.80555000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129271'),
(71418, 'La Palma', 3459, 'GRO', 142, 'MX', 17.08148000, -99.50994000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129271'),
(71419, 'La Palma', 3464, 'VER', 142, 'MX', 20.17941000, -97.06718000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129271'),
(71420, 'La Palma', 3455, 'QUE', 142, 'MX', 20.52219000, -100.17392000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20246488'),
(71421, 'La Palma', 3469, 'GUA', 142, 'MX', 20.51560000, -100.69787000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20246488'),
(71422, 'La Palma', 3477, 'NAY', 142, 'MX', 21.49172000, -105.17817000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20246488'),
(71423, 'La Palma', 3474, 'MIC', 142, 'MX', 20.14780000, -102.75850000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20246488'),
(71424, 'La Palma', 3449, 'SIN', 142, 'MX', 25.38286000, -108.15909000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20246488'),
(71425, 'La Palma', 3471, 'COA', 142, 'MX', 25.60830000, -103.32228000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20246488'),
(71426, 'La Palma', 3468, 'SON', 142, 'MX', 28.05028000, -110.70111000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20129687'),
(71427, 'La Palma (Las Palmas)', 3474, 'MIC', 142, 'MX', 19.77111000, -101.14083000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20292994'),
(71428, 'La Palmilla', 3464, 'VER', 142, 'MX', 20.02041000, -97.14553000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20292994'),
(71429, 'La Palmita (La Palmita de San Gabriel)', 3469, 'GUA', 142, 'MX', 20.49806000, -100.87806000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20208146'),
(71430, 'La Palmita y Anexos (La Presita)', 3449, 'SIN', 142, 'MX', 24.90917000, -107.45194000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20254367'),
(71431, 'La Panadera', 3456, 'AGU', 142, 'MX', 21.86398000, -102.69832000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20254367'),
(71432, 'La Partida', 3471, 'COA', 142, 'MX', 25.59181000, -103.29994000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20254367'),
(71433, 'La Pastoría', 3450, 'MEX', 142, 'MX', 19.54306000, -98.91111000, '2019-10-05 23:08:53', '2020-05-01 17:23:00', 1, 'Q20130683'),
(71434, 'La Patria', 3451, 'CHP', 142, 'MX', 16.22975000, -91.91148000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20130683'),
(71435, 'La Paz', 3471, 'COA', 142, 'MX', 25.59390000, -103.35852000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20130683'),
(71436, 'La Paz', 3460, 'BCS', 142, 'MX', 24.14437000, -110.30050000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q214215'),
(71437, 'La Pe', 3448, 'OAX', 142, 'MX', 16.63027000, -96.79766000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q3892800'),
(71438, 'La Pedrera', 3463, 'TAM', 142, 'MX', 22.39353000, -97.88210000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20130895'),
(71439, 'La Pendencia', 3462, 'ZAC', 142, 'MX', 22.43780000, -101.57606000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20130895'),
(71440, 'La Península', 3454, 'TAB', 142, 'MX', 17.94347000, -93.36148000, '2019-10-05 23:08:53', '2020-05-01 17:23:02', 1, 'Q20131144'),
(71441, 'La Perla', 3464, 'VER', 142, 'MX', 18.92817000, -97.13362000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20131414'),
(71442, 'La Perla', 3471, 'COA', 142, 'MX', 25.49696000, -103.35088000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20131414'),
(71443, 'La Pesca', 3463, 'TAM', 142, 'MX', 23.78660000, -97.77712000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20131414'),
(71444, 'La Peñita de Jaltomba', 3477, 'NAY', 142, 'MX', 21.03884000, -105.24792000, '2019-10-05 23:08:53', '2020-05-01 17:23:01', 1, 'Q20131414'),
(71445, 'La Piedad', 3474, 'MIC', 142, 'MX', 20.34239000, -102.03050000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20240187'),
(71446, 'La Piedad', 3455, 'QUE', 142, 'MX', 20.57890000, -100.25573000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20240187'),
(71447, 'La Pila', 3461, 'SLP', 142, 'MX', 22.03425000, -100.86786000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20131841'),
(71448, 'La Pimienta', 3451, 'CHP', 142, 'MX', 17.14111000, -92.76222000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20254618'),
(71449, 'La Pinta', 3471, 'COA', 142, 'MX', 25.90281000, -103.26476000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20254618'),
(71450, 'La Placita de Morelos', 3474, 'MIC', 142, 'MX', 18.53270000, -103.58841000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20254618'),
(71451, 'La Planada', 3450, 'MEX', 142, 'MX', 19.78129000, -99.18267000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20254654'),
(71452, 'La Planada (El Arenal)', 3450, 'MEX', 142, 'MX', 19.33333000, -99.67694000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20132201'),
(71453, 'La Playa', 3460, 'BCS', 142, 'MX', 23.06417000, -109.66833000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20245516'),
(71454, 'La Plaza del Limón', 3474, 'MIC', 142, 'MX', 20.13524000, -102.43117000, '2019-10-05 23:08:53', '2020-05-01 17:23:01', 1, 'Q20245516'),
(71455, 'La Popular', 3453, 'DUR', 142, 'MX', 25.67951000, -103.46621000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20245516'),
(71456, 'La Pradera', 3455, 'QUE', 142, 'MX', 20.65889000, -100.34222000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q5964910'),
(71457, 'La Presa', 3477, 'NAY', 142, 'MX', 22.34358000, -105.39350000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20132913'),
(71458, 'La Presita', 3449, 'SIN', 142, 'MX', 25.70734000, -108.58523000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20132913'),
(71459, 'La Presita Segundo Cuartel', 3450, 'MEX', 142, 'MX', 20.16278000, -99.87278000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20133016'),
(71460, 'La Presumida', 3467, 'ROO', 142, 'MX', 19.80083000, -88.75389000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293414'),
(71461, 'La Providencia', 3468, 'SON', 142, 'MX', 28.72268000, -111.58687000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20262308'),
(71462, 'La Providencia', 3450, 'MEX', 142, 'MX', 19.75833000, -99.14667000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20133505'),
(71463, 'La Providencia', 3457, 'BCN', 142, 'MX', 30.96889000, -116.15861000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20133535'),
(71464, 'La Providencia', 3464, 'VER', 142, 'MX', 18.75304000, -96.77087000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20133535'),
(71465, 'La Providencia Siglo XXI', 3470, 'HID', 142, 'MX', 20.06417000, -98.71722000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293446'),
(71466, 'La Puerta del Pilar', 3450, 'MEX', 142, 'MX', 19.46018000, -100.01577000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293446'),
(71467, 'La Punta', 3456, 'AGU', 142, 'MX', 22.32411000, -102.29212000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293446'),
(71468, 'La Purificación', 3450, 'MEX', 142, 'MX', 19.52441000, -98.81731000, '2019-10-05 23:08:53', '2020-05-01 17:23:00', 1, 'Q20293446'),
(71469, 'La Purísima', 3476, 'PUE', 142, 'MX', 18.82974000, -97.57301000, '2019-10-05 23:08:53', '2020-05-01 17:23:01', 1, 'Q20293446'),
(71470, 'La Purísima', 3469, 'GUA', 142, 'MX', 20.51444000, -100.67250000, '2019-10-05 23:08:53', '2020-05-01 17:23:00', 1, 'Q20134250'),
(71471, 'La Reforma', 3448, 'OAX', 142, 'MX', 16.62414000, -97.84544000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q7731295'),
(71472, 'La Reforma', 3470, 'HID', 142, 'MX', 21.07327000, -98.87057000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q7731295'),
(71473, 'La Reforma', 3464, 'VER', 142, 'MX', 19.59664000, -96.63302000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q7731295'),
(71474, 'La Reforma', 3461, 'SLP', 142, 'MX', 21.99879000, -100.09452000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q7731295'),
(71475, 'La Reforma', 3449, 'SIN', 142, 'MX', 25.08164000, -108.05673000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293692'),
(71476, 'La Resurrección', 3476, 'PUE', 142, 'MX', 19.10279000, -98.13013000, '2019-10-05 23:08:53', '2020-05-01 17:23:01', 1, 'Q20293692'),
(71477, 'La Rinconada', 3451, 'CHP', 142, 'MX', 15.59333000, -92.26806000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135280'),
(71478, 'La Rinconoda', 3474, 'MIC', 142, 'MX', 20.04363000, -102.28850000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135280'),
(71479, 'La Rivera', 3460, 'BCS', 142, 'MX', 23.59458000, -109.58523000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135280'),
(71480, 'La Rosa', 3450, 'MEX', 142, 'MX', 19.48519000, -99.32984000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293754'),
(71481, 'La Sabana', 3469, 'GUA', 142, 'MX', 21.19956000, -100.68956000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293754'),
(71482, 'La Sabinita', 3470, 'HID', 142, 'MX', 20.36286000, -99.63423000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293754'),
(71483, 'La Sala (La Caliente)', 3470, 'HID', 142, 'MX', 20.20722000, -98.87250000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135754'),
(71484, 'La Sardina', 3469, 'GUA', 142, 'MX', 20.88118000, -101.66440000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135754'),
(71485, 'La Sauceda', 3469, 'GUA', 142, 'MX', 20.89831000, -101.19124000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135754'),
(71486, 'La Sauceda', 3474, 'MIC', 142, 'MX', 20.09390000, -102.34512000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135754'),
(71487, 'La Selva', 3454, 'TAB', 142, 'MX', 18.02333000, -92.96306000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135979'),
(71488, 'La Sidra', 3464, 'VER', 142, 'MX', 18.93380000, -97.07989000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135979'),
(71489, 'La Solana', 3455, 'QUE', 142, 'MX', 20.73835000, -100.39469000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20135979'),
(71490, 'La Soledad', 3458, 'TLA', 142, 'MX', 19.53841000, -98.61750000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20264282'),
(71491, 'La Soledad', 3469, 'GUA', 142, 'MX', 20.60792000, -101.41558000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20264282'),
(71492, 'La Soledad', 3459, 'GRO', 142, 'MX', 16.92000000, -98.16694000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20208026'),
(71493, 'La Soledad', 3476, 'PUE', 142, 'MX', 18.81000000, -97.76417000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20230653'),
(71494, 'La Soledad', 3474, 'MIC', 142, 'MX', 19.52111000, -100.48333000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20255551'),
(71495, 'La Soledad', 3455, 'QUE', 142, 'MX', 20.16944000, -100.19111000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20136324'),
(71496, 'La Soledad Barrio', 3450, 'MEX', 142, 'MX', 20.09194000, -99.82306000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20136257'),
(71497, 'La Sombra', 3464, 'VER', 142, 'MX', 19.75076000, -96.71420000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20136257'),
(71498, 'La Subida', 3461, 'SLP', 142, 'MX', 21.90506000, -99.09892000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20136257'),
(71499, 'La Sábana', 3454, 'TAB', 142, 'MX', 18.44461000, -92.88411000, '2019-10-05 23:08:53', '2020-05-01 17:23:02', 1, 'Q20293791'),
(71500, 'La Tabaquera', 3448, 'OAX', 142, 'MX', 18.33582000, -96.45129000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293791'),
(71501, 'La Tesorera', 3462, 'ZAC', 142, 'MX', 22.69707000, -102.11670000, '2019-10-05 23:08:53', '2019-10-05 23:08:53', 1, 'Q20293791');

