INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(46803, 'Sarcelles', 4796, 'IDF', 75, 'FR', 48.99739000, 2.37821000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q107501'),
(46804, 'Sare', 4795, 'NAQ', 75, 'FR', 43.31260000, -1.58012000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q274155'),
(46805, 'Sargé-lès-le-Mans', 4802, 'PDL', 75, 'FR', 48.03333000, 0.23333000, '2019-10-05 22:49:00', '2020-05-01 17:22:46', 1, 'Q274155'),
(46806, 'Sargé-sur-Braye', 4818, '<PERSON>V<PERSON>', 75, 'FR', 47.92352000, 0.85340000, '2019-10-05 22:49:00', '2020-05-01 17:22:44', 1, 'Q818047'),
(46807, 'Sari-Solenzara', 4806, '20R', 75, 'FR', 41.83519000, 9.37470000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q273710'),
(46808, 'Sarlat-la-Canéda', 4795, 'NAQ', 75, 'FR', 44.88902000, 1.21656000, '2019-10-05 22:49:00', '2020-05-01 17:22:46', 1, 'Q6381'),
(46809, 'Sarralbe', 4820, 'GES', 75, 'FR', 48.99858000, 7.03074000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q21979'),
(46810, 'Sarras', 4798, 'ARA', 75, 'FR', 45.18679000, 4.80004000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q21979'),
(46811, 'Sarre-Union', 4820, 'GES', 75, 'FR', 48.93818000, 7.09373000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q21569'),
(46812, 'Sarrebourg', 4820, 'GES', 75, 'FR', 48.73558000, 7.05720000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q22544'),
(46813, 'Sarreguemines', 4820, 'GES', 75, 'FR', 49.10995000, 7.06747000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q22642'),
(46814, 'Sarreinsming', 4820, 'GES', 75, 'FR', 49.09121000, 7.11053000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q22642'),
(46815, 'Sarrewerden', 4820, 'GES', 75, 'FR', 48.92261000, 7.08412000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q22642'),
(46816, 'Sarrians', 4812, 'PAC', 75, 'FR', 44.08312000, 4.97111000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q471305'),
(46817, 'Sarrola', 4806, '20R', 75, 'FR', 42.02828000, 8.84241000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q471305'),
(46818, 'Sarry', 4820, 'GES', 75, 'FR', 48.91852000, 4.40621000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q471305'),
(46819, 'Sars-Poteries', 4828, 'HDF', 75, 'FR', 50.16928000, 4.02676000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46820, 'Sarthe', 4802, 'PDL', 75, 'FR', 48.00493000, 0.26516000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q12740'),
(46821, 'Sartilly', 4804, 'NOR', 75, 'FR', 48.75239000, -1.45678000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q12740'),
(46822, 'Sartrouville', 4796, 'IDF', 75, 'FR', 48.94820000, 2.19169000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q209101'),
(46823, 'Sartène', 4806, '20R', 75, 'FR', 41.61667000, 8.98333000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q217459'),
(46824, 'Sarzeau', 4807, 'BRE', 75, 'FR', 47.52772000, -2.76933000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q128716'),
(46825, 'Sassenage', 4798, 'ARA', 75, 'FR', 45.21266000, 5.66290000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q694818'),
(46826, 'Sassenay', 4825, 'BFC', 75, 'FR', 46.83074000, 4.92523000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q694818'),
(46827, 'Sathonay-Camp', 4798, 'ARA', 75, 'FR', 45.82462000, 4.87453000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1617699'),
(46828, 'Sathonay-Village', 4798, 'ARA', 75, 'FR', 45.83337000, 4.87821000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46829, 'Satillieu', 4798, 'ARA', 75, 'FR', 45.15041000, 4.61410000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46830, 'Satolas-et-Bonce', 4798, 'ARA', 75, 'FR', 45.69368000, 5.13032000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46831, 'Saubens', 4799, 'OCC', 75, 'FR', 43.47984000, 1.35189000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46832, 'Saubion', 4795, 'NAQ', 75, 'FR', 43.67140000, -1.34821000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46833, 'Saubrigues', 4795, 'NAQ', 75, 'FR', 43.60989000, -1.31381000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46834, 'Saucats', 4795, 'NAQ', 75, 'FR', 44.65405000, -0.59643000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46835, 'Saugnac-et-Cambran', 4795, 'NAQ', 75, 'FR', 43.67098000, -0.99495000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q3473934'),
(46836, 'Saugues', 4798, 'ARA', 75, 'FR', 44.96008000, 3.54737000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q752540'),
(46837, 'Saujon', 4795, 'NAQ', 75, 'FR', 45.67309000, -0.92620000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q599075'),
(46838, 'Saulce-sur-Rhône', 4798, 'ARA', 75, 'FR', 44.70430000, 4.80061000, '2019-10-05 22:49:01', '2020-05-01 17:22:43', 1, 'Q599075'),
(46839, 'Saulcy-sur-Meurthe', 4820, 'GES', 75, 'FR', 48.23758000, 6.96443000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1410001'),
(46840, 'Saulgé', 4795, 'NAQ', 75, 'FR', 46.37758000, 0.87577000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q1614204'),
(46841, 'Saulieu', 4825, 'BFC', 75, 'FR', 47.28023000, 4.22857000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1614204'),
(46842, 'Saulnes', 4820, 'GES', 75, 'FR', 49.53189000, 5.82130000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1614204'),
(46843, 'Saulny', 4820, 'GES', 75, 'FR', 49.15770000, 6.10929000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1614204'),
(46844, 'Sault', 4812, 'PAC', 75, 'FR', 44.08968000, 5.40836000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q631582'),
(46845, 'Sault-Brénaz', 4798, 'ARA', 75, 'FR', 45.86132000, 5.39954000, '2019-10-05 22:49:01', '2020-05-01 17:22:43', 1, 'Q631582'),
(46846, 'Sault-lès-Rethel', 4820, 'GES', 75, 'FR', 49.49980000, 4.36227000, '2019-10-05 22:49:01', '2020-05-01 17:22:45', 1, 'Q631582'),
(46847, 'Saultain', 4828, 'HDF', 75, 'FR', 50.33676000, 3.57723000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q691961'),
(46848, 'Saulx-les-Chartreux', 4796, 'IDF', 75, 'FR', 48.69062000, 2.26727000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q249201'),
(46849, 'Saulxures-lès-Nancy', 4820, 'GES', 75, 'FR', 48.68872000, 6.24353000, '2019-10-05 22:49:01', '2020-05-01 17:22:45', 1, 'Q249201'),
(46850, 'Saulxures-sur-Moselotte', 4820, 'GES', 75, 'FR', 47.94894000, 6.77040000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q200080'),
(46851, 'Saulzoir', 4828, 'HDF', 75, 'FR', 50.24057000, 3.44430000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, '********'),
(46852, 'Saumur', 4802, 'PDL', 75, 'FR', 47.25908000, -0.07796000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q193821'),
(46853, 'Sausheim', 4820, 'GES', 75, 'FR', 47.78711000, 7.37267000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q495474'),
(46854, 'Saussan', 4799, 'OCC', 75, 'FR', 43.57220000, 3.77500000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q980365'),
(46855, 'Saussay', 4818, 'CVL', 75, 'FR', 48.85571000, 1.40889000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q980365'),
(46856, 'Sausset-les-Pins', 4812, 'PAC', 75, 'FR', 43.33136000, 5.10431000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q498204'),
(46857, 'Sautron', 4802, 'PDL', 75, 'FR', 47.26274000, -1.67107000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q498204'),
(46858, 'Sauvagnon', 4795, 'NAQ', 75, 'FR', 43.40000000, -0.38333000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q858097'),
(46859, 'Sauve', 4799, 'OCC', 75, 'FR', 43.94150000, 3.94903000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q477766'),
(46860, 'Sauverny', 4798, 'ARA', 75, 'FR', 46.31514000, 6.11827000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q477766'),
(46861, 'Sauveterre', 4799, 'OCC', 75, 'FR', 44.02282000, 4.79347000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q477766'),
(46862, 'Sauveterre-de-Béarn', 4795, 'NAQ', 75, 'FR', 43.40000000, -0.93333000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q195656'),
(46863, 'Sauveterre-de-Guyenne', 4795, 'NAQ', 75, 'FR', 44.69300000, -0.08549000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q379921'),
(46864, 'Sauvian', 4799, 'OCC', 75, 'FR', 43.29293000, 3.26024000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q379921'),
(46865, 'Sauviat-sur-Vige', 4795, 'NAQ', 75, 'FR', 45.90720000, 1.60827000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q690367'),
(46866, 'Sauvigny-les-Bois', 4825, 'BFC', 75, 'FR', 46.96676000, 3.27190000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q690367'),
(46867, 'Sauxillanges', 4798, 'ARA', 75, 'FR', 45.55109000, 3.37147000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q626541'),
(46868, 'Sauzet', 4798, 'ARA', 75, 'FR', 44.60397000, 4.82091000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q626541'),
(46869, 'Sauzé-Vaussais', 4795, 'NAQ', 75, 'FR', 46.13369000, 0.10673000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, '********'),
(46870, 'Savasse', 4798, 'ARA', 75, 'FR', 44.60428000, 4.77653000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, '********'),
(46871, 'Savenay', 4802, 'PDL', 75, 'FR', 47.36072000, -1.94215000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q816233'),
(46872, 'Savennières', 4802, 'PDL', 75, 'FR', 47.38225000, -0.65708000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q387232'),
(46873, 'Saverdun', 4799, 'OCC', 75, 'FR', 43.23526000, 1.57398000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q387232'),
(46874, 'Saverne', 4820, 'GES', 75, 'FR', 48.74164000, 7.36221000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q22741'),
(46875, 'Savigneux', 4798, 'ARA', 75, 'FR', 45.61670000, 4.08330000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q22741'),
(46876, 'Savigny', 4798, 'ARA', 75, 'FR', 45.81595000, 4.57410000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q22741'),
(46877, 'Savigny-Lévescault', 4795, 'NAQ', 75, 'FR', 46.53502000, 0.47719000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q22741'),
(46878, 'Savigny-en-Sancerre', 4818, 'CVL', 75, 'FR', 47.44142000, 2.80953000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q22741'),
(46879, 'Savigny-en-Véron', 4818, 'CVL', 75, 'FR', 47.20071000, 0.14457000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q22741'),
(46880, 'Savigny-le-Temple', 4796, 'IDF', 75, 'FR', 48.57409000, 2.58287000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q328010'),
(46881, 'Savigny-lès-Beaune', 4825, 'BFC', 75, 'FR', 47.06357000, 4.81821000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q328010'),
(46882, 'Savigny-sur-Braye', 4818, 'CVL', 75, 'FR', 47.87923000, 0.80981000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1383584'),
(46883, 'Savigny-sur-Orge', 4796, 'IDF', 75, 'FR', 48.67677000, 2.34835000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q243956'),
(46884, 'Savigné', 4795, 'NAQ', 75, 'FR', 46.15950000, 0.31937000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q243956'),
(46885, 'Savigné-sur-Lathan', 4818, 'CVL', 75, 'FR', 47.44483000, 0.32093000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q607158'),
(46886, 'Savoie', 4798, 'ARA', 75, 'FR', 45.46805000, 6.48547000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q12745'),
(46887, 'Savonnières', 4818, 'CVL', 75, 'FR', 47.34769000, 0.54961000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q748742'),
(46888, 'Savy-Berlette', 4828, 'HDF', 75, 'FR', 50.35249000, 2.56456000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q748742'),
(46889, 'Sayat', 4798, 'ARA', 75, 'FR', 45.82640000, 3.05250000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q748742'),
(46890, 'Saze', 4799, 'OCC', 75, 'FR', 43.94340000, 4.68096000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q748742'),
(46891, 'Saâcy-sur-Marne', 4796, 'IDF', 75, 'FR', 48.96210000, 3.21083000, '2019-10-05 22:49:01', '2020-05-01 17:22:43', 1, 'Q748742'),
(46892, 'Saïx', 4799, 'OCC', 75, 'FR', 43.58333000, 2.18333000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q748742'),
(46893, 'Saône', 4825, 'BFC', 75, 'FR', 47.22311000, 6.11682000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q748742'),
(46894, 'Scaër', 4807, 'BRE', 75, 'FR', 48.03333000, -3.70000000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q317493'),
(46895, 'Sceaux', 4796, 'IDF', 75, 'FR', 48.77644000, 2.29026000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q317493'),
(46896, 'Scey-sur-Saône-et-Saint-Albin', 4825, 'BFC', 75, 'FR', 47.66398000, 5.97451000, '2019-10-05 22:49:01', '2020-05-01 17:22:44', 1, 'Q317493'),
(46897, 'Scherwiller', 4820, 'GES', 75, 'FR', 48.28713000, 7.42135000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q21434'),
(46898, 'Schiltigheim', 4820, 'GES', 75, 'FR', 48.60749000, 7.74931000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q22639'),
(46899, 'Schirmeck', 4820, 'GES', 75, 'FR', 48.48313000, 7.22004000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q22509'),
(46900, 'Schirrhein', 4820, 'GES', 75, 'FR', 48.80161000, 7.90588000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q21484'),
(46901, 'Schnersheim', 4820, 'GES', 75, 'FR', 48.65741000, 7.56701000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q21484'),
(46902, 'Schweighouse-sur-Moder', 4820, 'GES', 75, 'FR', 48.82009000, 7.72810000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q21484'),
(46903, 'Schwindratzheim', 4820, 'GES', 75, 'FR', 48.75582000, 7.59898000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q21295'),
(46904, 'Schœneck', 4820, 'GES', 75, 'FR', 49.21667000, 6.91667000, '2019-10-05 22:49:01', '2020-05-01 17:22:45', 1, 'Q21295'),
(46905, 'Sciez', 4798, 'ARA', 75, 'FR', 46.33251000, 6.38413000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q740710'),
(46906, 'Scionzier', 4798, 'ARA', 75, 'FR', 46.06010000, 6.55271000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q740710'),
(46907, 'Scorbé-Clairvaux', 4795, 'NAQ', 75, 'FR', 46.81061000, 0.41369000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q1439932'),
(46908, 'Scy-Chazelles', 4820, 'GES', 75, 'FR', 49.11340000, 6.11470000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1439932'),
(46909, 'Seboncourt', 4828, 'HDF', 75, 'FR', 49.95273000, 3.47586000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q1439932'),
(46910, 'Sebourg', 4828, 'HDF', 75, 'FR', 50.34249000, 3.64352000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q817366'),
(46911, 'Seclin', 4828, 'HDF', 75, 'FR', 50.54873000, 3.02731000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q630627'),
(46912, 'Secondigny', 4795, 'NAQ', 75, 'FR', 46.61024000, -0.41679000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q630627'),
(46913, 'Sedan', 4820, 'GES', 75, 'FR', 49.70187000, 4.94028000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q184371'),
(46914, 'Segonzac', 4795, 'NAQ', 75, 'FR', 45.61667000, -0.21667000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q184371'),
(46915, 'Segré', 4802, 'PDL', 75, 'FR', 47.68646000, -0.87237000, '2019-10-05 22:49:01', '2020-05-01 17:22:46', 1, 'Q549330'),
(46916, 'Seichamps', 4820, 'GES', 75, 'FR', 48.71492000, 6.26139000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q818892'),
(46917, 'Seiches-sur-le-Loir', 4802, 'PDL', 75, 'FR', 47.57351000, -0.35628000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q767553'),
(46918, 'Seignelay', 4825, 'BFC', 75, 'FR', 47.90542000, 3.60136000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q767553'),
(46919, 'Seignosse', 4795, 'NAQ', 75, 'FR', 43.68774000, -1.37000000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q429860'),
(46920, 'Seigy', 4818, 'CVL', 75, 'FR', 47.25688000, 1.39964000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, '********'),
(46921, 'Seilh', 4799, 'OCC', 75, 'FR', 43.69469000, 1.35509000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, '********'),
(46922, 'Seilhac', 4795, 'NAQ', 75, 'FR', 45.36709000, 1.71350000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, '********'),
(46923, 'Seillans', 4812, 'PAC', 75, 'FR', 43.63623000, 6.64332000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, '********'),
(46924, 'Seine-Maritime', 4804, 'NOR', 75, 'FR', 49.67278000, 1.12519000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q12758'),
(46925, 'Seine-Port', 4796, 'IDF', 75, 'FR', 48.55743000, 2.55316000, '2019-10-05 22:49:01', '2019-10-05 22:49:01', 1, 'Q254398'),
(46926, 'Seine-Saint-Denis', 4796, 'IDF', 75, 'FR', 48.91421000, 2.47604000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q12761'),
(46927, 'Seine-et-Marne', 4796, 'IDF', 75, 'FR', 48.64599000, 2.95905000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q12753'),
(46928, 'Seingbouse', 4820, 'GES', 75, 'FR', 49.11341000, 6.83186000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q12753'),
(46929, 'Seissan', 4799, 'OCC', 75, 'FR', 43.49215000, 0.59250000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q12753'),
(46930, 'Selles-Saint-Denis', 4818, 'CVL', 75, 'FR', 47.38695000, 1.92295000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q12753'),
(46931, 'Selles-sur-Cher', 4818, 'CVL', 75, 'FR', 47.27904000, 1.55387000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q832386'),
(46932, 'Seloncourt', 4825, 'BFC', 75, 'FR', 47.45989000, 6.85535000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q832386'),
(46933, 'Selongey', 4825, 'BFC', 75, 'FR', 47.58846000, 5.18483000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q832386'),
(46934, 'Seltz', 4820, 'GES', 75, 'FR', 48.89520000, 8.10757000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q21430'),
(46935, 'Selvigny', 4828, 'HDF', 75, 'FR', 50.07971000, 3.34899000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1876051'),
(46936, 'Semblançay', 4818, 'CVL', 75, 'FR', 47.50000000, 0.58333000, '2019-10-05 22:49:02', '2020-05-01 17:22:44', 1, 'Q1876051'),
(46937, 'Semoy', 4818, 'CVL', 75, 'FR', 47.93333000, 1.95000000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1876051'),
(46938, 'Semur-en-Auxois', 4825, 'BFC', 75, 'FR', 47.48333000, 4.33333000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q236579'),
(46939, 'Semussac', 4795, 'NAQ', 75, 'FR', 45.60000000, -0.91667000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1439071'),
(46940, 'Senlis', 4828, 'HDF', 75, 'FR', 49.20724000, 2.58661000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q126668'),
(46941, 'Sennecey-le-Grand', 4825, 'BFC', 75, 'FR', 46.64137000, 4.86707000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q126668'),
(46942, 'Sennecey-lès-Dijon', 4825, 'BFC', 75, 'FR', 47.28984000, 5.10485000, '2019-10-05 22:49:02', '2020-05-01 17:22:44', 1, 'Q126668'),
(46943, 'Senonches', 4818, 'CVL', 75, 'FR', 48.55999000, 1.03069000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q693987'),
(46944, 'Senones', 4820, 'GES', 75, 'FR', 48.39475000, 6.97818000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q693987'),
(46945, 'Sens', 4825, 'BFC', 75, 'FR', 48.19738000, 3.28328000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q212420'),
(46946, 'Sens-de-Bretagne', 4807, 'BRE', 75, 'FR', 48.33245000, -1.53535000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q212420'),
(46947, 'Sentheim', 4820, 'GES', 75, 'FR', 47.75560000, 7.05305000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q212420'),
(46948, 'Septeuil', 4796, 'IDF', 75, 'FR', 48.89245000, 1.68357000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46949, 'Septfonds', 4799, 'OCC', 75, 'FR', 44.17813000, 1.61806000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46950, 'Septème', 4798, 'ARA', 75, 'FR', 45.55294000, 5.00596000, '2019-10-05 22:49:02', '2020-05-01 17:22:43', 1, 'Q1461215'),
(46951, 'Septèmes-les-Vallons', 4812, 'PAC', 75, 'FR', 43.39834000, 5.36596000, '2019-10-05 22:49:02', '2020-05-01 17:22:47', 1, 'Q1461215'),
(46952, 'Sequedin', 4828, 'HDF', 75, 'FR', 50.62575000, 2.98276000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46953, 'Seraincourt', 4796, 'IDF', 75, 'FR', 49.03573000, 1.86703000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46954, 'Sergines', 4825, 'BFC', 75, 'FR', 48.34203000, 3.26213000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46955, 'Sergy', 4798, 'ARA', 75, 'FR', 46.25069000, 6.00183000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46956, 'Sermaises', 4818, 'CVL', 75, 'FR', 48.29650000, 2.20546000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46957, 'Sermaize-les-Bains', 4820, 'GES', 75, 'FR', 48.78507000, 4.91169000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46958, 'Sermoise-sur-Loire', 4825, 'BFC', 75, 'FR', 46.95000000, 3.18333000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46959, 'Sermérieu', 4798, 'ARA', 75, 'FR', 45.66995000, 5.41110000, '2019-10-05 22:49:02', '2020-05-01 17:22:43', 1, 'Q1461215'),
(46960, 'Sernhac', 4799, 'OCC', 75, 'FR', 43.91116000, 4.55039000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46961, 'Serpaize', 4798, 'ARA', 75, 'FR', 45.55824000, 4.91764000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46962, 'Serques', 4828, 'HDF', 75, 'FR', 50.79338000, 2.20134000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46963, 'Serqueux', 4804, 'NOR', 75, 'FR', 49.63202000, 1.54005000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1461215'),
(46964, 'Serquigny', 4804, 'NOR', 75, 'FR', 49.10943000, 0.71016000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q662658'),
(46965, 'Serre-les-Sapins', 4825, 'BFC', 75, 'FR', 47.25000000, 5.93333000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q662658'),
(46966, 'Serres', 4812, 'PAC', 75, 'FR', 44.42753000, 5.71458000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1019240'),
(46967, 'Serres-Castet', 4795, 'NAQ', 75, 'FR', 43.38333000, -0.35000000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q175965'),
(46968, 'Serris', 4796, 'IDF', 75, 'FR', 48.84528000, 2.78611000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q175965'),
(46969, 'Serrières', 4798, 'ARA', 75, 'FR', 45.31799000, 4.76327000, '2019-10-05 22:49:02', '2020-05-01 17:22:43', 1, 'Q175965'),
(46970, 'Serrières-de-Briord', 4798, 'ARA', 75, 'FR', 45.80706000, 5.44795000, '2019-10-05 22:49:02', '2020-05-01 17:22:43', 1, 'Q175965'),
(46971, 'Servas', 4798, 'ARA', 75, 'FR', 46.13317000, 5.16510000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q175965'),
(46972, 'Servian', 4799, 'OCC', 75, 'FR', 43.42716000, 3.30032000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q175965'),
(46973, 'Servon', 4796, 'IDF', 75, 'FR', 48.71662000, 2.58737000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q175965'),
(46974, 'Servon-sur-Vilaine', 4807, 'BRE', 75, 'FR', 48.12114000, -1.45971000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q175965'),
(46975, 'Serémange-Erzange', 4820, 'GES', 75, 'FR', 49.32062000, 6.09092000, '2019-10-05 22:49:02', '2020-05-01 17:22:45', 1, 'Q175965'),
(46976, 'Sessenheim', 4820, 'GES', 75, 'FR', 48.79652000, 7.98719000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q175965'),
(46977, 'Seugy', 4796, 'IDF', 75, 'FR', 49.12182000, 2.39377000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1647493'),
(46978, 'Seurre', 4825, 'BFC', 75, 'FR', 46.99923000, 5.15138000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1647493'),
(46979, 'Sevran', 4796, 'IDF', 75, 'FR', 48.94472000, 2.52746000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q387630'),
(46980, 'Sevrey', 4825, 'BFC', 75, 'FR', 46.73825000, 4.84030000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q387630'),
(46981, 'Sevrier', 4798, 'ARA', 75, 'FR', 45.86442000, 6.13990000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q690457'),
(46982, 'Seyne-les-Alpes', 4812, 'PAC', 75, 'FR', 44.35042000, 6.35634000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q842778'),
(46983, 'Seynod', 4798, 'ARA', 75, 'FR', 45.88549000, 6.08831000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q818981'),
(46984, 'Seysses', 4799, 'OCC', 75, 'FR', 43.49801000, 1.31081000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q818981'),
(46985, 'Seyssinet-Pariset', 4798, 'ARA', 75, 'FR', 45.17675000, 5.69387000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q818981'),
(46986, 'Seyssins', 4798, 'ARA', 75, 'FR', 45.16224000, 5.68673000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q818981'),
(46987, 'Seyssuel', 4798, 'ARA', 75, 'FR', 45.55826000, 4.84313000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1067719'),
(46988, 'Sibiril', 4807, 'BRE', 75, 'FR', 48.66667000, -4.06667000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1067719'),
(46989, 'Sierck-les-Bains', 4820, 'GES', 75, 'FR', 49.43942000, 6.35816000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q22299'),
(46990, 'Sierentz', 4820, 'GES', 75, 'FR', 47.65834000, 7.45387000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q635223'),
(46991, 'Sigean', 4799, 'OCC', 75, 'FR', 43.02777000, 2.97916000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q380977'),
(46992, 'Signes', 4812, 'PAC', 75, 'FR', 43.29080000, 5.86284000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q380977'),
(46993, 'Signy-le-Petit', 4820, 'GES', 75, 'FR', 49.90277000, 4.27990000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q380977'),
(46994, 'Sigolsheim', 4820, 'GES', 75, 'FR', 48.13493000, 7.29980000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q635255'),
(46995, 'Sillans', 4798, 'ARA', 75, 'FR', 45.34261000, 5.38864000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q635255'),
(46996, 'Sillery', 4820, 'GES', 75, 'FR', 49.19822000, 4.13244000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q635255'),
(46997, 'Sillingy', 4798, 'ARA', 75, 'FR', 45.94781000, 6.04415000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q635255'),
(46998, 'Silly-le-Long', 4828, 'HDF', 75, 'FR', 49.10749000, 2.79226000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q635255'),
(46999, 'Sillé-le-Guillaume', 4802, 'PDL', 75, 'FR', 48.18266000, -0.12642000, '2019-10-05 22:49:02', '2020-05-01 17:22:46', 1, '********'),
(47000, 'Simandre', 4825, 'BFC', 75, 'FR', 46.62364000, 4.98777000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, '********'),
(47001, 'Simandres', 4798, 'ARA', 75, 'FR', 45.61955000, 4.87314000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, '********'),
(47002, 'Simiane-Collongue', 4812, 'PAC', 75, 'FR', 43.43067000, 5.43454000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q640934'),
(47003, 'Sin-le-Noble', 4828, 'HDF', 75, 'FR', 50.36159000, 3.13113000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q48543'),
(47004, 'Sinceny', 4828, 'HDF', 75, 'FR', 49.59619000, 3.24679000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q48543'),
(47005, 'Sion-les-Mines', 4802, 'PDL', 75, 'FR', 47.73476000, -1.59190000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q48543'),
(47006, 'Sireuil', 4795, 'NAQ', 75, 'FR', 45.61667000, 0.01667000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q48543'),
(47007, 'Sisco', 4806, '20R', 75, 'FR', 42.80000000, 9.43333000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q48543'),
(47008, 'Sissonne', 4828, 'HDF', 75, 'FR', 49.57107000, 3.89369000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q48543'),
(47009, 'Sisteron', 4812, 'PAC', 75, 'FR', 44.19002000, 5.94643000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q271085'),
(47010, 'Six-Fours-les-Plages', 4812, 'PAC', 75, 'FR', 43.09174000, 5.82465000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q327993'),
(47011, 'Sixt-sur-Aff', 4807, 'BRE', 75, 'FR', 47.77648000, -2.07867000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q327993'),
(47012, 'Sizun', 4807, 'BRE', 75, 'FR', 48.40000000, -4.08333000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q329647'),
(47013, 'Smarves', 4795, 'NAQ', 75, 'FR', 46.51078000, 0.34980000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q953026'),
(47014, 'Sochaux', 4825, 'BFC', 75, 'FR', 47.50808000, 6.82748000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q281863'),
(47015, 'Socx', 4828, 'HDF', 75, 'FR', 50.93571000, 2.42422000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q281863'),
(47016, 'Soignolles-en-Brie', 4796, 'IDF', 75, 'FR', 48.65350000, 2.69968000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q281863'),
(47017, 'Soings-en-Sologne', 4818, 'CVL', 75, 'FR', 47.41289000, 1.52452000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q1169886'),
(47018, 'Soissons', 4828, 'HDF', 75, 'FR', 49.38167000, 3.32361000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q167152'),
(47019, 'Soisy-sous-Montmorency', 4796, 'IDF', 75, 'FR', 48.98813000, 2.30156000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q839549'),
(47020, 'Soisy-sur-Seine', 4796, 'IDF', 75, 'FR', 48.64875000, 2.45223000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, 'Q578822'),
(47021, 'Soisy-sur-École', 4796, 'IDF', 75, 'FR', 48.47637000, 2.49301000, '2019-10-05 22:49:02', '2020-05-01 17:22:43', 1, 'Q248024'),
(47022, 'Solaize', 4798, 'ARA', 75, 'FR', 45.63970000, 4.84038000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, '********'),
(47023, 'Solers', 4796, 'IDF', 75, 'FR', 48.65919000, 2.71617000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, '********'),
(47024, 'Solesmes', 4828, 'HDF', 75, 'FR', 50.18468000, 3.49799000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, '********'),
(47025, 'Solesmes', 4802, 'PDL', 75, 'FR', 47.85009000, -0.29806000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, '********'),
(47026, 'Solgne', 4820, 'GES', 75, 'FR', 48.96543000, 6.29429000, '2019-10-05 22:49:02', '2019-10-05 22:49:02', 1, '********'),
(47027, 'Soliers', 4804, 'NOR', 75, 'FR', 49.13440000, -0.29613000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47028, 'Solignac', 4795, 'NAQ', 75, 'FR', 45.75528000, 1.27563000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q632541'),
(47029, 'Solignac-sur-Loire', 4798, 'ARA', 75, 'FR', 44.96833000, 3.88635000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q632541'),
(47030, 'Solliès-Pont', 4812, 'PAC', 75, 'FR', 43.19009000, 6.04115000, '2019-10-05 22:49:03', '2020-05-01 17:22:47', 1, 'Q817597'),
(47031, 'Solliès-Toucas', 4812, 'PAC', 75, 'FR', 43.20579000, 6.02485000, '2019-10-05 22:49:03', '2020-05-01 17:22:47', 1, 'Q817597'),
(47032, 'Solliès-Ville', 4812, 'PAC', 75, 'FR', 43.18256000, 6.03849000, '2019-10-05 22:49:03', '2020-05-01 17:22:47', 1, 'Q816098'),
(47033, 'Solre-le-Château', 4828, 'HDF', 75, 'FR', 50.17432000, 4.08898000, '2019-10-05 22:49:03', '2020-05-01 17:22:45', 1, 'Q605337'),
(47034, 'Somain', 4828, 'HDF', 75, 'FR', 50.35961000, 3.28108000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q605337'),
(47035, 'Somme', 4828, 'HDF', 75, 'FR', 49.93141000, 2.27639000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q12770'),
(47036, 'Sommières', 4799, 'OCC', 75, 'FR', 43.78534000, 4.08973000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q637676'),
(47037, 'Sonchamp', 4796, 'IDF', 75, 'FR', 48.57590000, 1.87753000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q543656'),
(47038, 'Songeons', 4828, 'HDF', 75, 'FR', 49.54916000, 1.85361000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q548190'),
(47039, 'Sonnay', 4798, 'ARA', 75, 'FR', 45.35809000, 4.90479000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q548190'),
(47040, 'Sonnaz', 4798, 'ARA', 75, 'FR', 45.61765000, 5.91505000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q548190'),
(47041, 'Sonzay', 4818, 'CVL', 75, 'FR', 47.52687000, 0.46203000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q548190'),
(47042, 'Soorts-Hossegor', 4795, 'NAQ', 75, 'FR', 43.66490000, -1.39717000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q546832'),
(47043, 'Sorbiers', 4798, 'ARA', 75, 'FR', 45.49111000, 4.44163000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q546832'),
(47044, 'Sorel-Moussel', 4818, 'CVL', 75, 'FR', 48.83391000, 1.36699000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q546832'),
(47045, 'Sorges', 4795, 'NAQ', 75, 'FR', 45.30563000, 0.87328000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q175153'),
(47046, 'Sorgues', 4812, 'PAC', 75, 'FR', 44.01023000, 4.87381000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q175153'),
(47047, 'Sorigny', 4818, 'CVL', 75, 'FR', 47.24329000, 0.69520000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q175153'),
(47048, 'Sormiou', 4812, 'PAC', 75, 'FR', 43.20959000, 5.41872000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q175153'),
(47049, 'Sornay', 4825, 'BFC', 75, 'FR', 46.63023000, 5.17999000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q175153'),
(47050, 'Sorède', 4799, 'OCC', 75, 'FR', 42.53069000, 2.95708000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q190385'),
(47051, 'Sorèze', 4799, 'OCC', 75, 'FR', 43.45241000, 2.06799000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q190385'),
(47052, 'Sospel', 4812, 'PAC', 75, 'FR', 43.87792000, 7.44788000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q467020'),
(47053, 'Sottevast', 4804, 'NOR', 75, 'FR', 49.52374000, -1.59169000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47054, 'Sotteville-lès-Rouen', 4804, 'NOR', 75, 'FR', 49.40972000, 1.09005000, '2019-10-05 22:49:03', '2020-05-01 17:22:45', 1, 'Q317082'),
(47055, 'Soual', 4799, 'OCC', 75, 'FR', 43.55530000, 2.11679000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q317082'),
(47056, 'Soubise', 4795, 'NAQ', 75, 'FR', 45.92395000, -1.00938000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q317082'),
(47057, 'Soucelles', 4802, 'PDL', 75, 'FR', 47.56848000, -0.41819000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q765008'),
(47058, 'Souchez', 4828, 'HDF', 75, 'FR', 50.39291000, 2.73984000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47059, 'Soucht', 4820, 'GES', 75, 'FR', 48.95902000, 7.33434000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47060, 'Soucieu-en-Jarrest', 4798, 'ARA', 75, 'FR', 45.67771000, 4.70379000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47061, 'Soucy', 4825, 'BFC', 75, 'FR', 48.24956000, 3.32385000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47062, 'Soudan', 4802, 'PDL', 75, 'FR', 47.73777000, -1.30566000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47063, 'Soues', 4799, 'OCC', 75, 'FR', 43.20736000, 0.09874000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47064, 'Souesmes', 4818, 'CVL', 75, 'FR', 47.45638000, 2.17495000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q375463'),
(47065, 'Souffelweyersheim', 4820, 'GES', 75, 'FR', 48.63540000, 7.74141000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q22505'),
(47066, 'Soufflenheim', 4820, 'GES', 75, 'FR', 48.83026000, 7.96268000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q22437'),
(47067, 'Souillac', 4799, 'OCC', 75, 'FR', 44.89720000, 1.47224000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q22437'),
(47068, 'Soulac-sur-Mer', 4795, 'NAQ', 75, 'FR', 45.51068000, -1.12524000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q478237'),
(47069, 'Soulaines-sur-Aubance', 4802, 'PDL', 75, 'FR', 47.36381000, -0.52265000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q478237'),
(47070, 'Soulaire-et-Bourg', 4802, 'PDL', 75, 'FR', 47.57896000, -0.55232000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q478237'),
(47071, 'Soulgé-sur-Ouette', 4802, 'PDL', 75, 'FR', 48.06667000, -0.56667000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q478237'),
(47072, 'Souligné-sous-Ballon', 4802, 'PDL', 75, 'FR', 48.13758000, 0.23572000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q949796'),
(47073, 'Soullans', 4802, 'PDL', 75, 'FR', 46.79624000, -1.90106000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47074, 'Soultz-Haut-Rhin', 4820, 'GES', 75, 'FR', 47.88849000, 7.22860000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q501798'),
(47075, 'Soultz-sous-Forêts', 4820, 'GES', 75, 'FR', 48.93693000, 7.88110000, '2019-10-05 22:49:03', '2020-05-01 17:22:45', 1, 'Q381659'),
(47076, 'Soultzeren', 4820, 'GES', 75, 'FR', 48.06394000, 7.10233000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q635509'),
(47077, 'Soultzmatt', 4820, 'GES', 75, 'FR', 47.96188000, 7.23759000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q263758'),
(47078, 'Soumoulou', 4795, 'NAQ', 75, 'FR', 43.26667000, -0.18333000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q197451'),
(47079, 'Souppes-sur-Loing', 4796, 'IDF', 75, 'FR', 48.18297000, 2.73521000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q271562'),
(47080, 'Souprosse', 4795, 'NAQ', 75, 'FR', 43.78869000, -0.71035000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q271562'),
(47081, 'Souraïde', 4795, 'NAQ', 75, 'FR', 43.34154000, -1.47559000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q645206'),
(47082, 'Sourcieux-les-Mines', 4798, 'ARA', 75, 'FR', 45.80606000, 4.62254000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q645206'),
(47083, 'Sourdeval', 4804, 'NOR', 75, 'FR', 48.72312000, -0.92223000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q629791'),
(47084, 'Sourdun', 4796, 'IDF', 75, 'FR', 48.53688000, 3.35200000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q629791'),
(47085, 'Sours', 4818, 'CVL', 75, 'FR', 48.41043000, 1.59889000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q777769'),
(47086, 'Sourzac', 4795, 'NAQ', 75, 'FR', 45.04978000, 0.39598000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q201677'),
(47087, 'Soussans', 4795, 'NAQ', 75, 'FR', 45.05619000, -0.69916000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q201677'),
(47088, 'Soustons', 4795, 'NAQ', 75, 'FR', 43.75328000, -1.32780000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q606970'),
(47089, 'Souvigny', 4798, 'ARA', 75, 'FR', 46.53480000, 3.19205000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q606970'),
(47090, 'Soyaux', 4795, 'NAQ', 75, 'FR', 45.65000000, 0.20000000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47091, 'Soyons', 4798, 'ARA', 75, 'FR', 44.88914000, 4.85026000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47092, 'Spay', 4802, 'PDL', 75, 'FR', 47.92384000, 0.15258000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47093, 'Spicheren', 4820, 'GES', 75, 'FR', 49.19252000, 6.96611000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47094, 'Spycker', 4828, 'HDF', 75, 'FR', 50.96915000, 2.32184000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47095, 'Spéracèdes', 4812, 'PAC', 75, 'FR', 43.64850000, 6.85889000, '2019-10-05 22:49:03', '2020-05-01 17:22:47', 1, '********'),
(47096, 'Spézet', 4807, 'BRE', 75, 'FR', 48.20000000, -3.71667000, '2019-10-05 22:49:03', '2020-05-01 17:22:44', 1, '********'),
(47097, 'Staffelfelden', 4820, 'GES', 75, 'FR', 47.82647000, 7.25235000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, '********'),
(47098, 'Stains', 4796, 'IDF', 75, 'FR', 48.95000000, 2.38333000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q258878'),
(47099, 'Steenbecque', 4828, 'HDF', 75, 'FR', 50.67421000, 2.48442000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q258878'),
(47100, 'Steene', 4828, 'HDF', 75, 'FR', 50.95228000, 2.36813000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q888314'),
(47101, 'Steenvoorde', 4828, 'HDF', 75, 'FR', 50.81046000, 2.58244000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q6603'),
(47102, 'Steenwerck', 4828, 'HDF', 75, 'FR', 50.70199000, 2.77829000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q679993'),
(47103, 'Steinbach', 4820, 'GES', 75, 'FR', 47.82140000, 7.15279000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q679993'),
(47104, 'Steinbourg', 4820, 'GES', 75, 'FR', 48.77028000, 7.41354000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q21452'),
(47105, 'Stenay', 4820, 'GES', 75, 'FR', 49.49489000, 5.18606000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q846090'),
(47106, 'Still', 4820, 'GES', 75, 'FR', 48.54982000, 7.40444000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q846090'),
(47107, 'Stiring-Wendel', 4820, 'GES', 75, 'FR', 49.20198000, 6.93170000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q22879'),
(47108, 'Stosswihr', 4820, 'GES', 75, 'FR', 48.05389000, 7.09964000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q22879'),
(47109, 'Stotzheim', 4820, 'GES', 75, 'FR', 48.37868000, 7.49235000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q22879'),
(47110, 'Strasbourg', 4820, 'GES', 75, 'FR', 48.58392000, 7.74553000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q6602'),
(47111, 'Sucy-en-Brie', 4796, 'IDF', 75, 'FR', 48.76872000, 2.53474000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q6602'),
(47112, 'Sucé-sur-Erdre', 4802, 'PDL', 75, 'FR', 47.33333000, -1.53333000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q829373'),
(47113, 'Suippes', 4820, 'GES', 75, 'FR', 49.12886000, 4.53446000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q829373'),
(47114, 'Sully-sur-Loire', 4818, 'CVL', 75, 'FR', 47.76372000, 2.37238000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q504924'),
(47115, 'Sulniac', 4807, 'BRE', 75, 'FR', 47.67375000, -2.57136000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q504924'),
(47116, 'Sumène', 4799, 'OCC', 75, 'FR', 43.98057000, 3.71575000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q323638'),
(47117, 'Sundhoffen', 4820, 'GES', 75, 'FR', 48.04263000, 7.41320000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q323638'),
(47118, 'Sundhouse', 4820, 'GES', 75, 'FR', 48.25110000, 7.60427000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q21532'),
(47119, 'Surbourg', 4820, 'GES', 75, 'FR', 48.91009000, 7.84716000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q21498'),
(47120, 'Suresnes', 4796, 'IDF', 75, 'FR', 48.87143000, 2.22929000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q193929'),
(47121, 'Surgères', 4795, 'NAQ', 75, 'FR', 46.10820000, -0.75148000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q828767'),
(47122, 'Surtainville', 4804, 'NOR', 75, 'FR', 49.45977000, -1.81298000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q828767'),
(47123, 'Survilliers', 4796, 'IDF', 75, 'FR', 49.09712000, 2.54449000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q1624089'),
(47124, 'Sury-le-Comtal', 4798, 'ARA', 75, 'FR', 45.53735000, 4.18573000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q1155235'),
(47125, 'Surzur', 4807, 'BRE', 75, 'FR', 47.57850000, -2.62892000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q128950'),
(47126, 'Sussargues', 4799, 'OCC', 75, 'FR', 43.71250000, 4.00310000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q173565'),
(47127, 'Suze-la-Rousse', 4798, 'ARA', 75, 'FR', 44.28761000, 4.84161000, '2019-10-05 22:49:03', '2019-10-05 22:49:03', 1, 'Q173565'),
(47128, 'Suèvres', 4818, 'CVL', 75, 'FR', 47.66655000, 1.46153000, '2019-10-05 22:49:03', '2020-05-01 17:22:44', 1, 'Q1169770'),
(47129, 'Sète', 4799, 'OCC', 75, 'FR', 43.40280000, 3.69278000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q191141'),
(47130, 'Sèvres', 4796, 'IDF', 75, 'FR', 48.82292000, 2.21757000, '2019-10-05 22:49:03', '2020-05-01 17:22:43', 1, 'Q206493'),
(47131, 'Sèvres-Anxaumont', 4795, 'NAQ', 75, 'FR', 46.57036000, 0.46603000, '2019-10-05 22:49:03', '2020-05-01 17:22:46', 1, 'Q206493'),
(47132, 'Sébazac-Concourès', 4799, 'OCC', 75, 'FR', 44.40484000, 2.60324000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q206493'),
(47133, 'Sées', 4804, 'NOR', 75, 'FR', 48.60403000, 0.17244000, '2019-10-05 22:49:04', '2020-05-01 17:22:45', 1, 'Q818913'),
(47134, 'Séez', 4798, 'ARA', 75, 'FR', 45.62368000, 6.80149000, '2019-10-05 22:49:04', '2020-05-01 17:22:43', 1, 'Q818913'),
(47135, 'Ségny', 4798, 'ARA', 75, 'FR', 46.29571000, 6.07257000, '2019-10-05 22:49:04', '2020-05-01 17:22:43', 1, 'Q818913'),
(47136, 'Sélestat', 4820, 'GES', 75, 'FR', 48.26195000, 7.44890000, '2019-10-05 22:49:04', '2020-05-01 17:22:45', 1, 'Q22737'),
(47137, 'Sémalens', 4799, 'OCC', 75, 'FR', 43.59225000, 2.11208000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q22737'),
(47138, 'Séméac', 4799, 'OCC', 75, 'FR', 43.22915000, 0.10602000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q1324360'),
(47139, 'Sénas', 4812, 'PAC', 75, 'FR', 43.74375000, 5.07800000, '2019-10-05 22:49:04', '2020-05-01 17:22:47', 1, 'Q640681'),
(47140, 'Séné', 4807, 'BRE', 75, 'FR', 47.61900000, -2.73700000, '2019-10-05 22:49:04', '2020-05-01 17:22:44', 1, 'Q640681'),
(47141, 'Séreilhac', 4795, 'NAQ', 75, 'FR', 45.76843000, 1.08052000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q640681'),
(47142, 'Sérent', 4807, 'BRE', 75, 'FR', 47.82333000, -2.50571000, '2019-10-05 22:49:04', '2020-05-01 17:22:44', 1, 'Q128287'),
(47143, 'Sérifontaine', 4828, 'HDF', 75, 'FR', 49.35440000, 1.76873000, '2019-10-05 22:49:04', '2020-05-01 17:22:45', 1, 'Q750692'),
(47144, 'Sérignan', 4799, 'OCC', 75, 'FR', 43.27873000, 3.27712000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q751619'),
(47145, 'Sérignan-du-Comtat', 4812, 'PAC', 75, 'FR', 44.18915000, 4.84379000, '2019-10-05 22:49:04', '2020-05-01 17:22:47', 1, '********'),
(47146, 'Sérigné', 4802, 'PDL', 75, 'FR', 46.50107000, -0.84453000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, '********'),
(47147, 'Sérézin-du-Rhône', 4798, 'ARA', 75, 'FR', 45.62871000, 4.82400000, '2019-10-05 22:49:04', '2020-05-01 17:22:43', 1, '********'),
(47148, 'Sévignac', 4807, 'BRE', 75, 'FR', 48.33297000, -2.33915000, '2019-10-05 22:49:04', '2020-05-01 17:22:44', 1, '********'),
(47149, 'Sévérac', 4802, 'PDL', 75, 'FR', 47.55008000, -2.07496000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, '********'),
(47150, 'Sévérac-le-Château', 4799, 'OCC', 75, 'FR', 44.32429000, 3.05929000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q753077'),
(47151, 'Sézanne', 4820, 'GES', 75, 'FR', 48.72047000, 3.72339000, '2019-10-05 22:49:04', '2020-05-01 17:22:45', 1, 'Q839650'),
(47152, 'Tabanac', 4795, 'NAQ', 75, 'FR', 44.72059000, -0.40513000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q839650'),
(47153, 'Tacoignières', 4796, 'IDF', 75, 'FR', 48.83619000, 1.67501000, '2019-10-05 22:49:04', '2020-05-01 17:22:43', 1, 'Q372476'),
(47154, 'Taden', 4807, 'BRE', 75, 'FR', 48.47530000, -2.01946000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q225211'),
(47155, 'Taillades', 4812, 'PAC', 75, 'FR', 43.83762000, 5.08951000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q142178'),
(47156, 'Taillecourt', 4825, 'BFC', 75, 'FR', 47.49521000, 6.85442000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q142178'),
(47157, 'Tain-l’Hermitage', 4798, 'ARA', 75, 'FR', 45.06672000, 4.85590000, '2019-10-05 22:49:04', '2020-05-01 17:22:43', 1, 'Q280296'),
(47158, 'Taintrux', 4820, 'GES', 75, 'FR', 48.24944000, 6.89963000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1158045'),
(47159, 'Taissy', 4820, 'GES', 75, 'FR', 49.21509000, 4.09406000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1158045'),
(47160, 'Talange', 4820, 'GES', 75, 'FR', 49.23577000, 6.17167000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1158045'),
(47161, 'Talant', 4825, 'BFC', 75, 'FR', 47.33693000, 5.00888000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1158045'),
(47162, 'Talence', 4795, 'NAQ', 75, 'FR', 44.80477000, -0.59543000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q192481'),
(47163, 'Talensac', 4807, 'BRE', 75, 'FR', 48.10847000, -1.92829000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q230358'),
(47164, 'Tallard', 4812, 'PAC', 75, 'FR', 44.46200000, 6.05205000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q840061'),
(47165, 'Tallende', 4798, 'ARA', 75, 'FR', 45.67040000, 3.12460000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q840061'),
(47166, 'Talloires', 4798, 'ARA', 75, 'FR', 45.84098000, 6.21374000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q542296'),
(47167, 'Talmas', 4828, 'HDF', 75, 'FR', 50.03049000, 2.32554000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q542296'),
(47168, 'Taluyers', 4798, 'ARA', 75, 'FR', 45.63973000, 4.72203000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q542296'),
(47169, 'Tancarville', 4804, 'NOR', 75, 'FR', 49.48550000, 0.45765000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q839520'),
(47170, 'Taninges', 4798, 'ARA', 75, 'FR', 46.10883000, 6.59231000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q839520'),
(47171, 'Tanlay', 4825, 'BFC', 75, 'FR', 47.84488000, 4.08504000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q839520'),
(47172, 'Tanneron', 4812, 'PAC', 75, 'FR', 43.59057000, 6.87541000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q839520'),
(47173, 'Taponnat-Fleurignac', 4795, 'NAQ', 75, 'FR', 45.77868000, 0.40932000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1440571'),
(47174, 'Taradeau', 4812, 'PAC', 75, 'FR', 43.45444000, 6.42729000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1440571'),
(47175, 'Tarare', 4798, 'ARA', 75, 'FR', 45.89614000, 4.43300000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1440571'),
(47176, 'Tarascon', 4812, 'PAC', 75, 'FR', 43.80583000, 4.66028000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1440571'),
(47177, 'Tarascon-sur-Ariège', 4799, 'OCC', 75, 'FR', 42.84545000, 1.60332000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q1440571'),
(47178, 'Tarbes', 4799, 'OCC', 75, 'FR', 43.23333000, 0.08333000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q184023'),
(47179, 'Targon', 4795, 'NAQ', 75, 'FR', 44.73495000, -0.26351000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q471990'),
(47180, 'Tarn', 4799, 'OCC', 75, 'FR', 43.78170000, 2.16317000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q12772'),
(47181, 'Tarnos', 4795, 'NAQ', 75, 'FR', 43.54170000, -1.46281000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q266364'),
(47182, 'Tartas', 4795, 'NAQ', 75, 'FR', 43.83248000, -0.80895000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1427129'),
(47183, 'Tassin-la-Demi-Lune', 4798, 'ARA', 75, 'FR', 45.75499000, 4.78812000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1647506'),
(47184, 'Tatinghem', 4828, 'HDF', 75, 'FR', 50.74317000, 2.20724000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1647506'),
(47185, 'Taulignan', 4798, 'ARA', 75, 'FR', 44.44419000, 4.97057000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1647506'),
(47186, 'Taulé', 4807, 'BRE', 75, 'FR', 48.60000000, -3.90000000, '2019-10-05 22:49:04', '2020-05-01 17:22:44', 1, 'Q328746'),
(47187, 'Taupont', 4807, 'BRE', 75, 'FR', 47.95961000, -2.43933000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q328746'),
(47188, 'Tauriac', 4795, 'NAQ', 75, 'FR', 45.04915000, -0.50048000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q328746'),
(47189, 'Tauxigny', 4818, 'CVL', 75, 'FR', 47.21337000, 0.83479000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q328746'),
(47190, 'Tavaux', 4825, 'BFC', 75, 'FR', 47.03376000, 5.40500000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q768163'),
(47191, 'Tavel', 4799, 'OCC', 75, 'FR', 44.01270000, 4.69835000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q768163'),
(47192, 'Taverny', 4796, 'IDF', 75, 'FR', 49.02542000, 2.21691000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q275589'),
(47193, 'Tavers', 4818, 'CVL', 75, 'FR', 47.75921000, 1.61267000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q275589'),
(47194, 'Teillé', 4802, 'PDL', 75, 'FR', 47.46170000, -1.27810000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q275589'),
(47195, 'Telgruc-sur-Mer', 4807, 'BRE', 75, 'FR', 48.23333000, -4.35000000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q275589'),
(47196, 'Teloché', 4802, 'PDL', 75, 'FR', 47.88819000, 0.27086000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q618027'),
(47197, 'Templemars', 4828, 'HDF', 75, 'FR', 50.57387000, 3.05437000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q763901'),
(47198, 'Templeuve-en-Pévèle', 4828, 'HDF', 75, 'FR', 50.52336000, 3.17809000, '2019-10-05 22:49:04', '2020-05-01 17:22:45', 1, 'Q763901'),
(47199, 'Tenay', 4798, 'ARA', 75, 'FR', 45.91884000, 5.50797000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q763901'),
(47200, 'Tence', 4798, 'ARA', 75, 'FR', 45.11423000, 4.29097000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q815587'),
(47201, 'Tencin', 4798, 'ARA', 75, 'FR', 45.31105000, 5.95752000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q815587'),
(47202, 'Tende', 4812, 'PAC', 75, 'FR', 44.08752000, 7.59366000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q815587'),
(47203, 'Tennie', 4802, 'PDL', 75, 'FR', 48.10769000, -0.07626000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q815587'),
(47204, 'Tenteling', 4820, 'GES', 75, 'FR', 49.12544000, 6.93751000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q815587'),
(47205, 'Tercis-les-Bains', 4795, 'NAQ', 75, 'FR', 43.67048000, -1.10738000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q815587'),
(47206, 'Tercé', 4795, 'NAQ', 75, 'FR', 46.51667000, 0.56386000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q815587'),
(47207, 'Tergnier', 4828, 'HDF', 75, 'FR', 49.65607000, 3.30107000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q378973'),
(47208, 'Ternay', 4798, 'ARA', 75, 'FR', 45.60234000, 4.81064000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q378973'),
(47209, 'Terrasson-Lavilledieu', 4795, 'NAQ', 75, 'FR', 45.13011000, 1.30136000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q627200'),
(47210, 'Territoire de Belfort', 4825, 'BFC', 75, 'FR', 47.63578000, 6.88843000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q12782'),
(47211, 'Terville', 4820, 'GES', 75, 'FR', 49.34668000, 6.13346000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q12782'),
(47212, 'Tessy-sur-Vire', 4804, 'NOR', 75, 'FR', 48.97381000, -1.06087000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q775592'),
(47213, 'Tessé-la-Madeleine', 4804, 'NOR', 75, 'FR', 48.54963000, -0.42521000, '2019-10-05 22:49:04', '2020-05-01 17:22:45', 1, 'Q775592'),
(47214, 'Teting-sur-Nied', 4820, 'GES', 75, 'FR', 49.05704000, 6.66294000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q775592'),
(47215, 'Teyran', 4799, 'OCC', 75, 'FR', 43.68528000, 3.92889000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q925523'),
(47216, 'Thairé', 4795, 'NAQ', 75, 'FR', 46.07341000, -1.00230000, '2019-10-05 22:49:04', '2020-05-01 17:22:46', 1, 'Q925523'),
(47217, 'Thann', 4820, 'GES', 75, 'FR', 47.80789000, 7.10301000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q925523'),
(47218, 'Thaon', 4804, 'NOR', 75, 'FR', 49.25796000, -0.45605000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q925523'),
(47219, 'Thaon-les-Vosges', 4820, 'GES', 75, 'FR', 48.25000000, 6.41667000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q829640'),
(47220, 'Theillay', 4818, 'CVL', 75, 'FR', 47.31429000, 2.04028000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q1169891'),
(47221, 'Theix', 4807, 'BRE', 75, 'FR', 47.62916000, -2.65186000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q128456'),
(47222, 'Theizé', 4798, 'ARA', 75, 'FR', 45.93963000, 4.61634000, '2019-10-05 22:49:04', '2020-05-01 17:22:43', 1, 'Q1388804'),
(47223, 'Thenon', 4795, 'NAQ', 75, 'FR', 45.13897000, 1.07211000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q176334'),
(47224, 'Theys', 4798, 'ARA', 75, 'FR', 45.30132000, 5.99848000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q176334'),
(47225, 'Thiais', 4796, 'IDF', 75, 'FR', 48.76496000, 2.39610000, '2019-10-05 22:49:04', '2019-10-05 22:49:04', 1, 'Q316452'),
(47226, 'Thiant', 4828, 'HDF', 75, 'FR', 50.30480000, 3.44796000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1160392'),
(47227, 'Thiberville', 4804, 'NOR', 75, 'FR', 49.13768000, 0.45502000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1160392'),
(47228, 'Thiers', 4798, 'ARA', 75, 'FR', 45.85654000, 3.54758000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1160392'),
(47229, 'Thiers', 4812, 'PAC', 75, 'FR', 43.29748000, 5.38198000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1160392'),
(47230, 'Thiers-sur-Thève', 4828, 'HDF', 75, 'FR', 49.15252000, 2.57051000, '2019-10-05 22:49:05', '2020-05-01 17:22:45', 1, 'Q1332127'),
(47231, 'Thierville-sur-Meuse', 4820, 'GES', 75, 'FR', 49.17050000, 5.35266000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q223869'),
(47232, 'Thil', 4820, 'GES', 75, 'FR', 49.47300000, 5.90821000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q223869'),
(47233, 'Thil', 4798, 'ARA', 75, 'FR', 45.81368000, 5.02323000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q223869'),
(47234, 'Thilay', 4820, 'GES', 75, 'FR', 49.87345000, 4.80772000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q223869'),
(47235, 'Thilouze', 4818, 'CVL', 75, 'FR', 47.22415000, 0.57963000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q223869'),
(47236, 'Thionville', 4820, 'GES', 75, 'FR', 49.35994000, 6.16044000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q22640'),
(47237, 'Thiron Gardais', 4818, 'CVL', 75, 'FR', 48.31667000, 0.98333000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q22640'),
(47238, 'Thise', 4825, 'BFC', 75, 'FR', 47.28486000, 6.08127000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q22640'),
(47239, 'Thiverny', 4828, 'HDF', 75, 'FR', 49.25180000, 2.43609000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1335068'),
(47240, 'Thiverval-Grignon', 4796, 'IDF', 75, 'FR', 48.84964000, 1.91729000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1624176'),
(47241, 'Thiviers', 4795, 'NAQ', 75, 'FR', 45.41542000, 0.91963000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q629705'),
(47242, 'Thizy-les-Bourgs', 4798, 'ARA', 75, 'FR', 46.02995000, 4.31299000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1388744'),
(47243, 'Thoiry', 4798, 'ARA', 75, 'FR', 46.23762000, 5.98111000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1388744'),
(47244, 'Thoiry', 4796, 'IDF', 75, 'FR', 48.86715000, 1.79760000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1388744'),
(47245, 'Thoissey', 4798, 'ARA', 75, 'FR', 46.17222000, 4.80251000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q843583'),
(47246, 'Thomery', 4796, 'IDF', 75, 'FR', 48.40723000, 2.78852000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q899588'),
(47247, 'Thonon-les-Bains', 4798, 'ARA', 75, 'FR', 46.37049000, 6.47985000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q206165'),
(47248, 'Thorens-Glières', 4798, 'ARA', 75, 'FR', 45.99510000, 6.24557000, '2019-10-05 22:49:05', '2020-05-01 17:22:43', 1, 'Q206165'),
(47249, 'Thorigny-sur-Marne', 4796, 'IDF', 75, 'FR', 48.88689000, 2.71806000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q206165'),
(47250, 'Thorigny-sur-Oreuse', 4825, 'BFC', 75, 'FR', 48.29297000, 3.40128000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, '********'),
(47251, 'Thorigné', 4795, 'NAQ', 75, 'FR', 46.29149000, -0.25122000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, '********'),
(47252, 'Thorigné-sur-Dué', 4802, 'PDL', 75, 'FR', 48.03920000, 0.53554000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, '********'),
(47253, 'Thouarcé', 4802, 'PDL', 75, 'FR', 47.26734000, -0.50186000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q577088'),
(47254, 'Thouars', 4795, 'NAQ', 75, 'FR', 46.97602000, -0.21507000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q279681'),
(47255, 'Thouaré-sur-Loire', 4802, 'PDL', 75, 'FR', 47.26893000, -1.43834000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, '********'),
(47256, 'Thourotte', 4828, 'HDF', 75, 'FR', 49.47591000, 2.88210000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q669333'),
(47257, 'Thuellin', 4798, 'ARA', 75, 'FR', 45.63964000, 5.50840000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q669333'),
(47258, 'Thueyts', 4798, 'ARA', 75, 'FR', 44.67628000, 4.22146000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q669333'),
(47259, 'Thuir', 4799, 'OCC', 75, 'FR', 42.63290000, 2.75471000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q199697'),
(47260, 'Thumeries', 4828, 'HDF', 75, 'FR', 50.47662000, 3.05500000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q630984'),
(47261, 'Thun-Saint-Amand', 4828, 'HDF', 75, 'FR', 50.47276000, 3.45115000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q630984'),
(47262, 'Thurins', 4798, 'ARA', 75, 'FR', 45.68179000, 4.64079000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q630984'),
(47263, 'Thury-Harcourt', 4804, 'NOR', 75, 'FR', 48.98434000, -0.47519000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q839963'),
(47264, 'Thuré', 4795, 'NAQ', 75, 'FR', 46.83221000, 0.45797000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q839963'),
(47265, 'Thyez', 4798, 'ARA', 75, 'FR', 46.08249000, 6.53777000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q839963'),
(47266, 'Théding', 4820, 'GES', 75, 'FR', 49.12847000, 6.89220000, '2019-10-05 22:49:05', '2020-05-01 17:22:45', 1, 'Q839963'),
(47267, 'Thélus', 4828, 'HDF', 75, 'FR', 50.35441000, 2.80146000, '2019-10-05 22:49:05', '2020-05-01 17:22:45', 1, 'Q1022602'),
(47268, 'Thénac', 4795, 'NAQ', 75, 'FR', 45.66705000, -0.65345000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q1022602'),
(47269, 'Thénezay', 4795, 'NAQ', 75, 'FR', 46.71879000, -0.02883000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q1022602'),
(47270, 'Théoule-sur-Mer', 4812, 'PAC', 75, 'FR', 43.50780000, 6.94080000, '2019-10-05 22:49:05', '2020-05-01 17:22:47', 1, 'Q471530'),
(47271, 'Thérouanne', 4828, 'HDF', 75, 'FR', 50.63695000, 2.25838000, '2019-10-05 22:49:05', '2020-05-01 17:22:45', 1, 'Q261399'),
(47272, 'Thésée', 4818, 'CVL', 75, 'FR', 47.32345000, 1.30882000, '2019-10-05 22:49:05', '2020-05-01 17:22:44', 1, 'Q261399'),
(47273, 'Théza', 4799, 'OCC', 75, 'FR', 42.63797000, 2.95108000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q261399'),
(47274, 'Thézan-lès-Béziers', 4799, 'OCC', 75, 'FR', 43.41667000, 3.16667000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q261399'),
(47275, 'Thônes', 4798, 'ARA', 75, 'FR', 45.88123000, 6.32572000, '2019-10-05 22:49:05', '2020-05-01 17:22:43', 1, 'Q840190'),
(47276, 'Tiercé', 4802, 'PDL', 75, 'FR', 47.61587000, -0.46609000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q840190'),
(47277, 'Tiffauges', 4802, 'PDL', 75, 'FR', 47.01080000, -1.10999000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q815886'),
(47278, 'Tigery', 4796, 'IDF', 75, 'FR', 48.64257000, 2.50779000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, '********'),
(47279, 'Tignes', 4798, 'ARA', 75, 'FR', 45.49604000, 6.92463000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q330899'),
(47280, 'Tignieu', 4798, 'ARA', 75, 'FR', 45.75164000, 5.18911000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q330899'),
(47281, 'Tigy', 4818, 'CVL', 75, 'FR', 47.79365000, 2.19767000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q330899'),
(47282, 'Tillières', 4802, 'PDL', 75, 'FR', 47.14397000, -1.16334000, '2019-10-05 22:49:05', '2020-05-01 17:22:46', 1, 'Q330899'),
(47283, 'Tillières-sur-Avre', 4804, 'NOR', 75, 'FR', 48.75585000, 1.05388000, '2019-10-05 22:49:05', '2020-05-01 17:22:45', 1, 'Q330899'),
(47284, 'Tilloy-lès-Mofflaines', 4828, 'HDF', 75, 'FR', 50.27519000, 2.81456000, '2019-10-05 22:49:05', '2020-05-01 17:22:45', 1, 'Q330899'),
(47285, 'Tilly-sur-Seulles', 4804, 'NOR', 75, 'FR', 49.17598000, -0.62605000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q331551'),
(47286, 'Tillé', 4828, 'HDF', 75, 'FR', 49.46415000, 2.11038000, '2019-10-05 22:49:05', '2020-05-01 17:22:45', 1, 'Q331551'),
(47287, 'Tilques', 4828, 'HDF', 75, 'FR', 50.77712000, 2.19948000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q331551'),
(47288, 'Tinchebray', 4804, 'NOR', 75, 'FR', 48.76437000, -0.73333000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, '********'),
(47289, 'Tinqueux', 4820, 'GES', 75, 'FR', 49.25000000, 3.98333000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q593503'),
(47290, 'Tinténiac', 4807, 'BRE', 75, 'FR', 48.32860000, -1.83630000, '2019-10-05 22:49:05', '2020-05-01 17:22:44', 1, 'Q470697'),
(47291, 'Tocane-Saint-Apre', 4795, 'NAQ', 75, 'FR', 45.25404000, 0.49682000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q770049'),
(47292, 'Tollevast', 4804, 'NOR', 75, 'FR', 49.57437000, -1.62746000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q770049'),
(47293, 'Tomblaine', 4820, 'GES', 75, 'FR', 48.68378000, 6.21620000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q642356'),
(47294, 'Tonnay-Boutonne', 4795, 'NAQ', 75, 'FR', 45.96815000, -0.70847000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q666557'),
(47295, 'Tonnay-Charente', 4795, 'NAQ', 75, 'FR', 45.94900000, -0.89350000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q390905'),
(47296, 'Tonneins', 4795, 'NAQ', 75, 'FR', 44.39206000, 0.31241000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1070001'),
(47297, 'Tonnerre', 4825, 'BFC', 75, 'FR', 47.85628000, 3.97369000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q1070001'),
(47298, 'Tonquédec', 4807, 'BRE', 75, 'FR', 48.66886000, -3.39712000, '2019-10-05 22:49:05', '2020-05-01 17:22:44', 1, 'Q383198'),
(47299, 'Torcy', 4796, 'IDF', 75, 'FR', 48.85064000, 2.65078000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q383198'),
(47300, 'Torcy', 4825, 'BFC', 75, 'FR', 46.76857000, 4.45333000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q383198'),
(47301, 'Torcé', 4807, 'BRE', 75, 'FR', 48.06120000, -1.26708000, '2019-10-05 22:49:05', '2020-05-01 17:22:44', 1, 'Q383198'),
(47302, 'Torfou', 4802, 'PDL', 75, 'FR', 47.03682000, -1.11635000, '2019-10-05 22:49:05', '2019-10-05 22:49:05', 1, 'Q383198');

