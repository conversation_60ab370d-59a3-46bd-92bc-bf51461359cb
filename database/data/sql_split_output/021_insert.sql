INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(10167, 'Alto Boa Vista', 2011, 'MT', 31, 'BR', -11.82825000, -51.74596000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q22067364'),
(10168, 'Alto Caparaó', 1998, 'MG', 31, 'BR', -20.44669000, -41.87096000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q118945'),
(10169, 'Alto Feliz', 2001, 'RS', 31, 'BR', -29.36438000, -51.30068000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q118945'),
(10170, 'Alto Garças', 2011, 'MT', 31, 'BR', -16.81961000, -53.62461000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q443458'),
(10171, 'Alto Horizonte', 2000, 'GO', 31, 'BR', -14.20236000, -49.43500000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q443458'),
(10172, '<PERSON> Jequitibá', 1998, 'MG', 31, 'BR', -20.43703000, -41.95113000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q443518'),
(10173, 'Alto Longá', 2008, 'PI', 31, 'BR', -5.40788000, -42.06612000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q636564'),
(10174, 'Alto Paraguai', 2011, 'MT', 31, 'BR', -14.76851000, -56.72113000, '2019-10-05 22:34:55', '2019-10-05 22:34:55', 1, 'Q636564'),
(10175, 'Alto Paraná', 2022, 'PR', 31, 'BR', -23.06783000, -52.31196000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q2075653'),
(10176, 'Alto Paraíso', 2013, 'RO', 31, 'BR', -9.65996000, -63.58719000, '2019-10-05 22:34:55', '2020-05-01 17:22:38', 1, 'Q607036'),
(10177, 'Alto Paraíso', 2022, 'PR', 31, 'BR', -23.55498000, -53.80770000, '2019-10-05 22:34:55', '2020-05-01 17:22:37', 1, 'Q2064812'),
(10178, 'Alto Paraíso de Goiás', 2000, 'GO', 31, 'BR', -14.18198000, -47.45902000, '2019-10-05 22:34:55', '2020-05-01 17:22:36', 1, 'Q22060523'),
(10179, 'Alto Parnaíba', 2015, 'MA', 31, 'BR', -9.53480000, -46.13899000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22060523'),
(10180, 'Alto Piquiri', 2022, 'PR', 31, 'BR', -24.12973000, -53.34661000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1804740'),
(10181, 'Alto Rio Doce', 1998, 'MG', 31, 'BR', -21.01929000, -43.40538000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1804740'),
(10182, 'Alto Rio Novo', 2018, 'ES', 31, 'BR', -19.03248000, -40.99144000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1804740'),
(10183, 'Alto Santo', 2016, 'CE', 31, 'BR', -5.52410000, -38.20916000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1804740'),
(10184, 'Alto Taquari', 2011, 'MT', 31, 'BR', -17.76618000, -53.28010000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1804740'),
(10185, 'Alto do Rodrigues', 2019, 'RN', 31, 'BR', -5.34902000, -36.79683000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1804740'),
(10186, 'Altos', 2008, 'PI', 31, 'BR', -5.03806000, -42.46000000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22060469'),
(10187, 'Altãnia', 2022, 'PR', 31, 'BR', -23.87444000, -53.90167000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q22050222'),
(10188, 'Altônia', 2022, 'PR', 31, 'BR', -23.88648000, -53.96852000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q1805388'),
(10189, 'Alumínio', 2021, 'SP', 31, 'BR', -23.52259000, -47.28373000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q642894'),
(10190, 'Alvarenga', 1998, 'MG', 31, 'BR', -19.39870000, -41.68138000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22062812'),
(10191, 'Alvarães', 2004, 'AM', 31, 'BR', -3.22083000, -64.80417000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1808573'),
(10192, 'Alvinlândia', 2021, 'SP', 31, 'BR', -22.45669000, -49.76243000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1750195'),
(10193, 'Alvinópolis', 1998, 'MG', 31, 'BR', -20.12078000, -43.15399000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q370048'),
(10194, 'Alvorada', 2020, 'TO', 31, 'BR', -12.48000000, -49.12472000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22060164'),
(10195, 'Alvorada', 2001, 'RS', 31, 'BR', -29.98897000, -51.03717000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q449593'),
(10196, 'Alvorada d\'Oeste', 2013, 'RO', 31, 'BR', -11.29925000, -62.50152000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q449593'),
(10197, 'Alvorada de Minas', 1998, 'MG', 31, 'BR', -18.79532000, -43.37002000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q449593'),
(10198, 'Alvorada do Gurguéia', 2008, 'PI', 31, 'BR', -8.38553000, -43.83237000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q449593'),
(10199, 'Alvorada do Norte', 2000, 'GO', 31, 'BR', -14.52424000, -46.64133000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q449593'),
(10200, 'Alvorada do Sul', 2022, 'PR', 31, 'BR', -22.80928000, -51.27421000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q449593'),
(10201, 'Além Paraíba', 1998, 'MG', 31, 'BR', -21.80936000, -42.75740000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1756032'),
(10202, 'Amambai', 2010, 'MS', 31, 'BR', -23.12710000, -54.95790000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1756032'),
(10203, 'Amaporã', 2022, 'PR', 31, 'BR', -23.13633000, -52.83810000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q1756032'),
(10204, 'Amapá', 1999, 'AP', 31, 'BR', 1.85706000, -50.84374000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q586263'),
(10205, 'Amapá do Maranhão', 2015, 'MA', 31, 'BR', -1.69470000, -45.92994000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q2013296'),
(10206, 'Amaraji', 2006, 'PE', 31, 'BR', -8.37192000, -35.48991000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1839783'),
(10207, 'Amaral Ferrador', 2001, 'RS', 31, 'BR', -30.80769000, -52.29964000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q33955'),
(10208, 'Amaralina', 2000, 'GO', 31, 'BR', -13.81390000, -49.63205000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q33955'),
(10209, 'Amarante', 2008, 'PI', 31, 'BR', -6.37020000, -42.78806000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22059801'),
(10210, 'Amarante do Maranhão', 2015, 'MA', 31, 'BR', -5.56939000, -46.64105000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q947834'),
(10211, 'Amargosa', 2002, 'BA', 31, 'BR', -13.04970000, -39.60720000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1648060'),
(10212, 'Amaturá', 2004, 'AM', 31, 'BR', -3.38926000, -68.22698000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1648060'),
(10213, 'Americana', 2021, 'SP', 31, 'BR', -22.71408000, -47.29009000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q467421'),
(10214, 'Americano do Brasil', 2000, 'GO', 31, 'BR', -16.26498000, -49.99408000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q467421'),
(10215, 'Ametista do Sul', 2001, 'RS', 31, 'BR', -27.36698000, -53.18701000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q33945'),
(10216, 'Amontada', 2016, 'CE', 31, 'BR', -3.27941000, -39.80582000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q947378'),
(10217, 'Amorinópolis', 2000, 'GO', 31, 'BR', -16.63420000, -51.09852000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q947378'),
(10218, 'Amparo', 2021, 'SP', 31, 'BR', -22.70111000, -46.76444000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q930321'),
(10219, 'Amparo', 2005, 'PB', 31, 'BR', -7.55889000, -37.03372000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2010915'),
(10220, 'Amparo da Serra', 1998, 'MG', 31, 'BR', -20.52383000, -42.79878000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2010915'),
(10221, 'Amparo de São Francisco', 2003, 'SE', 31, 'BR', -10.13736000, -36.92219000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q2010915'),
(10222, 'Ampére', 2022, 'PR', 31, 'BR', -25.91242000, -53.49366000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q2004241'),
(10223, 'Amélia Rodrigues', 2002, 'BA', 31, 'BR', -12.38975000, -38.75153000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q2004241'),
(10224, 'América Dourada', 2002, 'BA', 31, 'BR', -11.46940000, -41.47335000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1762100'),
(10225, 'Américo Brasiliense', 2021, 'SP', 31, 'BR', -21.71797000, -48.01568000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q376503'),
(10226, 'Américo de Campos', 2021, 'SP', 31, 'BR', -20.27453000, -49.75539000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q376503'),
(10227, 'Anadia', 2007, 'AL', 31, 'BR', -9.67495000, -36.33790000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2028005'),
(10228, 'Anagé', 2002, 'BA', 31, 'BR', -14.66168000, -41.14703000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1772546'),
(10229, 'Anahy', 2022, 'PR', 31, 'BR', -24.66517000, -53.13229000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1772546'),
(10230, 'Anajatuba', 2015, 'MA', 31, 'BR', -3.27409000, -44.53278000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2013314'),
(10231, 'Anajás', 2009, 'PA', 31, 'BR', -0.86015000, -50.03970000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q2013314'),
(10232, 'Analândia', 2021, 'SP', 31, 'BR', -22.12002000, -47.68938000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q2013314'),
(10233, 'Anamã', 2004, 'AM', 31, 'BR', -3.47990000, -61.71689000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q2013314'),
(10234, 'Ananindeua', 2009, 'PA', 31, 'BR', -1.34611000, -48.38287000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q485887'),
(10235, 'Ananás', 2020, 'TO', 31, 'BR', -6.14621000, -48.21505000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q485887'),
(10236, 'Anapu', 2009, 'PA', 31, 'BR', -3.88583000, -51.33796000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q486142'),
(10237, 'Anapurus', 2015, 'MA', 31, 'BR', -3.56073000, -43.04307000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q486142'),
(10238, 'Anastácio', 2010, 'MS', 31, 'BR', -20.76269000, -55.73052000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067433'),
(10239, 'Anaurilândia', 2010, 'MS', 31, 'BR', -22.14199000, -52.72011000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067433'),
(10240, 'Anchieta', 2018, 'ES', 31, 'BR', -20.73420000, -40.70056000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2019024'),
(10241, 'Anchieta', 2014, 'SC', 31, 'BR', -26.52666000, -53.35459000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1935710'),
(10242, 'Andaraí', 2002, 'BA', 31, 'BR', -12.84167000, -41.27679000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1935710'),
(10243, 'Andirá', 2022, 'PR', 31, 'BR', -23.03931000, -50.27404000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q1935710'),
(10244, 'Andorinha', 2002, 'BA', 31, 'BR', -10.22216000, -39.86387000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1935710'),
(10245, 'Andradas', 1998, 'MG', 31, 'BR', -22.06984000, -46.57230000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1754887'),
(10246, 'Andradina', 2021, 'SP', 31, 'BR', -20.86545000, -51.31567000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1646937'),
(10247, 'Andrelândia', 1998, 'MG', 31, 'BR', -21.71132000, -44.27912000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q503236'),
(10248, 'André da Rocha', 2001, 'RS', 31, 'BR', -28.58832000, -51.49976000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q523535'),
(10249, 'Angatuba', 2021, 'SP', 31, 'BR', -23.47450000, -48.42565000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1750222'),
(10250, 'Angelim', 2006, 'PE', 31, 'BR', -8.89154000, -36.27674000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1750222'),
(10251, 'Angelina', 2014, 'SC', 31, 'BR', -27.53450000, -49.08558000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22063988'),
(10252, 'Angelândia', 1998, 'MG', 31, 'BR', -17.72394000, -42.26234000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1756210'),
(10253, 'Angical', 2002, 'BA', 31, 'BR', -11.96280000, -44.74980000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1756210'),
(10254, 'Angical do Piauí', 2008, 'PI', 31, 'BR', -6.09884000, -42.72022000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q1756210'),
(10255, 'Angico', 2020, 'TO', 31, 'BR', -6.34335000, -47.93112000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1756210'),
(10256, 'Angicos', 2019, 'RN', 31, 'BR', -5.67403000, -36.54881000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1989884'),
(10257, 'Angra dos Reis', 1997, 'RJ', 31, 'BR', -23.00667000, -44.31806000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q542829'),
(10258, 'Anguera', 2002, 'BA', 31, 'BR', -12.18221000, -39.21384000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1761950'),
(10259, 'Angélica', 2010, 'MS', 31, 'BR', -22.02780000, -53.86668000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1793031'),
(10260, 'Anhanguera', 2000, 'GO', 31, 'BR', -18.31505000, -48.22190000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1793031'),
(10261, 'Anhembi', 2021, 'SP', 31, 'BR', -22.83293000, -48.16384000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q942392'),
(10262, 'Anhumas', 2021, 'SP', 31, 'BR', -22.34571000, -51.42761000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22066885'),
(10263, 'Anicuns', 2000, 'GO', 31, 'BR', -16.38582000, -49.97942000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22067260'),
(10264, 'Anita Garibaldi', 2014, 'SC', 31, 'BR', -27.72616000, -51.05838000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q617657'),
(10265, 'Anitápolis', 2014, 'SC', 31, 'BR', -27.90194000, -49.12861000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q22063986'),
(10266, 'Anori', 2004, 'AM', 31, 'BR', -3.77278000, -61.64417000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q931090'),
(10267, 'Anta Gorda', 2001, 'RS', 31, 'BR', -28.96836000, -51.97107000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q931090'),
(10268, 'Antas', 2002, 'BA', 31, 'BR', -10.42138000, -38.30341000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22063195'),
(10269, 'Antonina', 2022, 'PR', 31, 'BR', -25.28425000, -48.72037000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q599068'),
(10270, 'Antonina do Norte', 2016, 'CE', 31, 'BR', -6.73622000, -39.97882000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q599068'),
(10271, 'Antônio Almeida', 2008, 'PI', 31, 'BR', -7.12211000, -44.25307000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q599068'),
(10272, 'Antônio Cardoso', 2002, 'BA', 31, 'BR', -12.38641000, -39.14549000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q599068'),
(10273, 'Antônio Carlos', 1998, 'MG', 31, 'BR', -21.40838000, -43.76937000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22062805'),
(10274, 'Antônio Carlos', 2014, 'SC', 31, 'BR', -27.49807000, -48.83918000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q22063985'),
(10275, 'Antônio Dias', 1998, 'MG', 31, 'BR', -19.56225000, -42.88855000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q611911'),
(10276, 'Antônio Gonçalves', 2002, 'BA', 31, 'BR', -10.62282000, -40.40933000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q611911'),
(10277, 'Antônio João', 2010, 'MS', 31, 'BR', -22.20114000, -55.93897000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067430'),
(10278, 'Antônio Martins', 2019, 'RN', 31, 'BR', -6.20609000, -37.92136000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q22067430'),
(10279, 'Antônio Olinto', 2022, 'PR', 31, 'BR', -25.96435000, -50.12992000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q1809601'),
(10280, 'Antônio Prado', 2001, 'RS', 31, 'BR', -28.87322000, -51.31744000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1809601'),
(10281, 'Antônio Prado de Minas', 1998, 'MG', 31, 'BR', -21.02658000, -42.15497000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1791977'),
(10282, 'Anápolis', 2000, 'GO', 31, 'BR', -16.32667000, -48.95278000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q223422'),
(10283, 'Anísio de Abreu', 2008, 'PI', 31, 'BR', -9.24779000, -43.05238000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q223422'),
(10284, 'Aparecida', 2005, 'PB', 31, 'BR', -6.80925000, -38.07182000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2077855'),
(10285, 'Aparecida', 2021, 'SP', 31, 'BR', -22.90434000, -45.23562000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q617702'),
(10286, 'Aparecida d\'Oeste', 2021, 'SP', 31, 'BR', -20.48414000, -50.91883000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1759370'),
(10287, 'Aparecida de Goiânia', 2000, 'GO', 31, 'BR', -16.82333000, -49.24389000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q459711'),
(10288, 'Aparecida do Rio Doce', 2000, 'GO', 31, 'BR', -18.22963000, -51.24140000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q459711'),
(10289, 'Aparecida do Rio Negro', 2020, 'TO', 31, 'BR', -10.06183000, -47.96514000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q459711'),
(10290, 'Aparecida do Taboado', 2010, 'MS', 31, 'BR', -20.00909000, -51.34632000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22067429'),
(10291, 'Aperibé', 1997, 'RJ', 31, 'BR', -21.64148000, -42.12753000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q22067429'),
(10292, 'Apiacá', 2018, 'ES', 31, 'BR', -21.08025000, -41.56100000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067429'),
(10293, 'Apiacás', 2011, 'MT', 31, 'BR', -9.03962000, -57.75949000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067429'),
(10294, 'Apiaí', 2021, 'SP', 31, 'BR', -24.39767000, -48.82237000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1760259'),
(10295, 'Apicum-Açu', 2015, 'MA', 31, 'BR', -1.48035000, -45.08540000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q2013356'),
(10296, 'Apiúna', 2014, 'SC', 31, 'BR', -27.10736000, -49.38129000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q2013356'),
(10297, 'Apodi', 2019, 'RN', 31, 'BR', -5.66690000, -37.92339000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2008084'),
(10298, 'Aporá', 2002, 'BA', 31, 'BR', -11.75439000, -38.22784000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q2008084'),
(10299, 'Aporé', 2000, 'GO', 31, 'BR', -18.74325000, -52.06942000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067258'),
(10300, 'Apuarema', 2002, 'BA', 31, 'BR', -13.83005000, -39.73444000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1772630'),
(10301, 'Apucarana', 2022, 'PR', 31, 'BR', -23.56159000, -51.45358000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1638385'),
(10302, 'Apuiarés', 2016, 'CE', 31, 'BR', -3.95004000, -39.30373000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1638385'),
(10303, 'Apuí', 2004, 'AM', 31, 'BR', -7.78922000, -59.34104000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1638385'),
(10304, 'Aquidabã', 2003, 'SE', 31, 'BR', -10.31619000, -37.10451000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q2013098'),
(10305, 'Aquidauana', 2010, 'MS', 31, 'BR', -20.45790000, -55.83801000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1792157'),
(10306, 'Aquiraz', 2016, 'CE', 31, 'BR', -3.90157000, -38.39127000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1794121'),
(10307, 'Arabutã', 2014, 'SC', 31, 'BR', -27.14142000, -52.18125000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1794121'),
(10308, 'Aracaju', 2003, 'SE', 31, 'BR', -10.98232000, -37.10333000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q170578'),
(10309, 'Aracati', 2016, 'CE', 31, 'BR', -4.56513000, -37.76688000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1754397'),
(10310, 'Aracatu', 2002, 'BA', 31, 'BR', -14.39011000, -41.36331000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1762365'),
(10311, 'Araci', 2002, 'BA', 31, 'BR', -11.13564000, -39.07003000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22063188'),
(10312, 'Aracitaba', 1998, 'MG', 31, 'BR', -21.35243000, -43.40862000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22063188'),
(10313, 'Aracoiaba', 2016, 'CE', 31, 'BR', -4.49045000, -38.67765000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2289917'),
(10314, 'Aracruz', 2018, 'ES', 31, 'BR', -19.81952000, -40.19097000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1794219'),
(10315, 'Aragarças', 2000, 'GO', 31, 'BR', -15.94743000, -52.08069000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067256'),
(10316, 'Aragoiânia', 2000, 'GO', 31, 'BR', -16.95418000, -49.40598000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q22067256'),
(10317, 'Aragominas', 2020, 'TO', 31, 'BR', -6.89259000, -48.61589000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22067256'),
(10318, 'Araguacema', 2020, 'TO', 31, 'BR', -8.91352000, -49.40915000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22067256'),
(10319, 'Araguaiana', 2011, 'MT', 31, 'BR', -15.73389000, -51.83139000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1792161'),
(10320, 'Araguainha', 2011, 'MT', 31, 'BR', -16.78427000, -53.07981000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1792161'),
(10321, 'Araguanã', 2015, 'MA', 31, 'BR', -3.04178000, -45.75874000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q2209464'),
(10322, 'Araguanã', 2020, 'TO', 31, 'BR', -6.76634000, -48.52702000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q957649'),
(10323, 'Araguapaz', 2000, 'GO', 31, 'BR', -15.12986000, -50.49840000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q957649'),
(10324, 'Araguari', 1998, 'MG', 31, 'BR', -18.64722000, -48.18722000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1637522'),
(10325, 'Araguatins', 2020, 'TO', 31, 'BR', -5.64071000, -48.08859000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1637522'),
(10326, 'Araguaçu', 2020, 'TO', 31, 'BR', -12.72579000, -49.74942000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q593716'),
(10327, 'Araguaína', 2020, 'TO', 31, 'BR', -7.31139000, -48.62113000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q625279'),
(10328, 'Araioses', 2015, 'MA', 31, 'BR', -2.89792000, -42.02298000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2041199'),
(10329, 'Aral Moreira', 2010, 'MS', 31, 'BR', -22.90946000, -55.37007000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22067428'),
(10330, 'Aramari', 2002, 'BA', 31, 'BR', -12.04618000, -38.55762000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q22067428'),
(10331, 'Arambaré', 2001, 'RS', 31, 'BR', -30.91331000, -51.55614000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q22067428'),
(10332, 'Arame', 2015, 'MA', 31, 'BR', -5.03155000, -45.86324000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2036277'),
(10333, 'Aramina', 2021, 'SP', 31, 'BR', -20.16724000, -47.82645000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q930883'),
(10334, 'Arandu', 2021, 'SP', 31, 'BR', -23.17065000, -49.05861000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1754457'),
(10335, 'Arantina', 1998, 'MG', 31, 'BR', -21.89632000, -44.22633000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1754457'),
(10336, 'Arapeí', 2021, 'SP', 31, 'BR', -22.66504000, -44.43858000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1749953'),
(10337, 'Arapiraca', 2007, 'AL', 31, 'BR', -9.74380000, -36.59315000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q626175'),
(10338, 'Arapoema', 2020, 'TO', 31, 'BR', -7.71941000, -49.03285000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q626175'),
(10339, 'Araponga', 1998, 'MG', 31, 'BR', -20.66607000, -42.51113000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1749824'),
(10340, 'Arapongas', 2022, 'PR', 31, 'BR', -23.41944000, -51.42444000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1650073'),
(10341, 'Araporã', 1998, 'MG', 31, 'BR', -18.49061000, -49.13761000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1650073'),
(10342, 'Arapoti', 2022, 'PR', 31, 'BR', -24.06902000, -50.03733000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1650073'),
(10343, 'Araputanga', 2011, 'MT', 31, 'BR', -15.17477000, -58.49729000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1650073'),
(10344, 'Arapuá', 1998, 'MG', 31, 'BR', -19.03065000, -46.09585000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1650073'),
(10345, 'Arapuã', 2022, 'PR', 31, 'BR', -24.31991000, -51.81138000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q1650073'),
(10346, 'Araquari', 2014, 'SC', 31, 'BR', -26.46833000, -48.80083000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q607184'),
(10347, 'Arara', 2005, 'PB', 31, 'BR', -6.82833000, -35.75833000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q37378'),
(10348, 'Araranguá', 2014, 'SC', 31, 'BR', -28.93954000, -49.51119000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q626231'),
(10349, 'Araraquara', 2021, 'SP', 31, 'BR', -21.79444000, -48.17556000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q626247'),
(10350, 'Araras', 2021, 'SP', 31, 'BR', -22.35694000, -47.38417000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q580366'),
(10351, 'Ararendá', 2016, 'CE', 31, 'BR', -4.77058000, -40.75389000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q580366'),
(10352, 'Arari', 2015, 'MA', 31, 'BR', -3.45361000, -44.78000000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1813008'),
(10353, 'Araricá', 2001, 'RS', 31, 'BR', -29.64987000, -50.93350000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1813008'),
(10354, 'Araripe', 2016, 'CE', 31, 'BR', -7.20462000, -40.11426000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2021237'),
(10355, 'Araripina', 2006, 'PE', 31, 'BR', -7.57611000, -40.49833000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q626344'),
(10356, 'Araruama', 1997, 'RJ', 31, 'BR', -22.87278000, -42.34306000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1761549'),
(10357, 'Araruna', 2005, 'PB', 31, 'BR', -6.55833000, -35.74167000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2065337'),
(10358, 'Araruna', 2022, 'PR', 31, 'BR', -23.97219000, -52.59131000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2065337'),
(10359, 'Arataca', 2002, 'BA', 31, 'BR', -15.24607000, -39.42481000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2065337'),
(10360, 'Aratiba', 2001, 'RS', 31, 'BR', -27.37708000, -52.29009000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2065337'),
(10361, 'Aratuba', 2016, 'CE', 31, 'BR', -4.41898000, -39.04818000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q2065337'),
(10362, 'Aratuípe', 2002, 'BA', 31, 'BR', -13.09363000, -39.08259000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q2065337'),
(10363, 'Araucária', 2022, 'PR', 31, 'BR', -25.59306000, -49.41028000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q1773313'),
(10364, 'Arauá', 2003, 'SE', 31, 'BR', -11.26222000, -37.61972000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1802037'),
(10365, 'Araxá', 1998, 'MG', 31, 'BR', -19.59333000, -46.94056000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q586057'),
(10366, 'Araçagi', 2005, 'PB', 31, 'BR', -6.85419000, -35.36047000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q586057'),
(10367, 'Araçariguama', 2021, 'SP', 31, 'BR', -23.42294000, -47.07816000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q765185'),
(10368, 'Araçatuba', 2021, 'SP', 31, 'BR', -21.14986000, -50.57470000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q626835'),
(10369, 'Araçaí', 1998, 'MG', 31, 'BR', -19.24200000, -44.22429000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q626835'),
(10370, 'Araçoiaba', 2006, 'PE', 31, 'BR', -7.79387000, -35.07645000, '2019-10-05 22:34:56', '2020-05-01 17:22:37', 1, 'Q2010873'),
(10371, 'Araçoiaba da Serra', 2021, 'SP', 31, 'BR', -23.56602000, -47.66733000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1754621'),
(10372, 'Araçu', 2000, 'GO', 31, 'BR', -16.38304000, -49.70782000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1754621'),
(10373, 'Araçuaí', 1998, 'MG', 31, 'BR', -16.90822000, -41.98596000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1749750'),
(10374, 'Araçás', 2002, 'BA', 31, 'BR', -12.14681000, -38.17233000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1749750'),
(10375, 'Araújos', 1998, 'MG', 31, 'BR', -19.87542000, -45.15682000, '2019-10-05 22:34:56', '2020-05-01 17:22:36', 1, 'Q1749750'),
(10376, 'Arceburgo', 1998, 'MG', 31, 'BR', -21.36655000, -46.94472000, '2019-10-05 22:34:56', '2019-10-05 22:34:56', 1, 'Q1749750'),
(10377, 'Arco-Íris', 2021, 'SP', 31, 'BR', -21.74342000, -50.42979000, '2019-10-05 22:34:56', '2020-05-01 17:22:38', 1, 'Q1750192'),
(10378, 'Arcos', 1998, 'MG', 31, 'BR', -20.24351000, -45.56760000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q22062794'),
(10379, 'Arcoverde', 2006, 'PE', 31, 'BR', -8.41889000, -37.05389000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q637902'),
(10380, 'Areado', 1998, 'MG', 31, 'BR', -21.33401000, -46.16798000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1756284'),
(10381, 'Areal', 1997, 'RJ', 31, 'BR', -22.23056000, -43.10556000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1761573'),
(10382, 'Arealva', 2021, 'SP', 31, 'BR', -22.06881000, -48.99294000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1761573'),
(10383, 'Areia', 2005, 'PB', 31, 'BR', -6.94723000, -35.66927000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1816283'),
(10384, 'Areia Branca', 2019, 'RN', 31, 'BR', -4.95611000, -37.13694000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2072691'),
(10385, 'Areia Branca', 2003, 'SE', 31, 'BR', -10.75778000, -37.31528000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q949854'),
(10386, 'Areia de Baraúnas', 2005, 'PB', 31, 'BR', -7.11328000, -36.97177000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q949854'),
(10387, 'Areial', 2005, 'PB', 31, 'BR', -7.04397000, -35.92622000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q949854'),
(10388, 'Areias', 2021, 'SP', 31, 'BR', -22.66811000, -44.71651000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q949854'),
(10389, 'Areiópolis', 2021, 'SP', 31, 'BR', -22.60627000, -48.65273000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q949854'),
(10390, 'Arenápolis', 2011, 'MT', 31, 'BR', -14.45028000, -56.84611000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1806305'),
(10391, 'Arenópolis', 2000, 'GO', 31, 'BR', -16.36096000, -51.60464000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1806305'),
(10392, 'Argirita', 1998, 'MG', 31, 'BR', -21.63912000, -42.82857000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1806305'),
(10393, 'Aricanduva', 1998, 'MG', 31, 'BR', -17.85789000, -42.59791000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1806305'),
(10394, 'Arinos', 1998, 'MG', 31, 'BR', -15.80332000, -45.94198000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q617058'),
(10395, 'Aripuanã', 2011, 'MT', 31, 'BR', -9.16667000, -60.63333000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q22055824'),
(10396, 'Ariquemes', 2013, 'RO', 31, 'BR', -9.91333000, -63.04083000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1646975'),
(10397, 'Ariranha', 2021, 'SP', 31, 'BR', -21.17685000, -48.77771000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1646975'),
(10398, 'Ariranha do Ivaí', 2022, 'PR', 31, 'BR', -24.35185000, -51.53194000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1646975'),
(10399, 'Armazém', 2014, 'SC', 31, 'BR', -28.22409000, -49.00779000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1764990'),
(10400, 'Armação', 2014, 'SC', 31, 'BR', -27.74963000, -48.50713000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q22047876'),
(10402, 'Armação dos Búzios', 1997, 'RJ', 31, 'BR', -22.76948000, -41.90965000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q680119'),
(10403, 'Arneiroz', 2016, 'CE', 31, 'BR', -6.23607000, -40.09400000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q680119'),
(10404, 'Aroazes', 2008, 'PI', 31, 'BR', -6.20348000, -41.86926000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q680119'),
(10405, 'Aroeiras', 2005, 'PB', 31, 'BR', -7.52057000, -35.69927000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1808029'),
(10406, 'Aroeiras do Itaim', 2008, 'PI', 31, 'BR', -7.27000000, -41.56000000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2095297'),
(10407, 'Arraial', 2008, 'PI', 31, 'BR', -6.62721000, -42.49393000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2095297'),
(10408, 'Arraial do Cabo', 1997, 'RJ', 31, 'BR', -22.95505000, -42.06098000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1750235'),
(10409, 'Arraias', 2020, 'TO', 31, 'BR', -12.84110000, -46.90868000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1750235'),
(10410, 'Arroio Grande', 2001, 'RS', 31, 'BR', -32.17945000, -52.86652000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q950879'),
(10411, 'Arroio Trinta', 2014, 'SC', 31, 'BR', -26.91617000, -51.34527000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q950879'),
(10412, 'Arroio do Meio', 2001, 'RS', 31, 'BR', -29.40111000, -51.94500000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1758806'),
(10413, 'Arroio do Padre', 2001, 'RS', 31, 'BR', -31.43927000, -52.39642000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q510698'),
(10414, 'Arroio do Sal', 2001, 'RS', 31, 'BR', -29.52930000, -49.89148000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1648525'),
(10415, 'Arroio do Tigre', 2001, 'RS', 31, 'BR', -29.26209000, -53.06060000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1648525'),
(10416, 'Arroio dos Ratos', 2001, 'RS', 31, 'BR', -30.07722000, -51.72917000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1750199'),
(10417, 'Artur Nogueira', 2021, 'SP', 31, 'BR', -22.57306000, -47.17250000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1754345'),
(10418, 'Aruanã', 2000, 'GO', 31, 'BR', -14.81020000, -50.94501000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1754345'),
(10419, 'Arujá', 2021, 'SP', 31, 'BR', -23.39611000, -46.32083000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1750250'),
(10420, 'Arvoredo', 2014, 'SC', 31, 'BR', -27.06908000, -52.44892000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1758063'),
(10421, 'Arvorezinha', 2001, 'RS', 31, 'BR', -28.86971000, -52.19631000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1758063'),
(10422, 'Arês', 2019, 'RN', 31, 'BR', -6.19444000, -35.16028000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q928712'),
(10423, 'Ascurra', 2014, 'SC', 31, 'BR', -26.98578000, -49.37084000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1758057'),
(10424, 'Aspásia', 2021, 'SP', 31, 'BR', -20.18385000, -50.73117000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1758057'),
(10425, 'Assaré', 2016, 'CE', 31, 'BR', -6.89746000, -39.82906000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1808367'),
(10426, 'Assaí', 2022, 'PR', 31, 'BR', -23.37333000, -50.84139000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q2005491'),
(10427, 'Assis', 2021, 'SP', 31, 'BR', -22.66167000, -50.41222000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q742227'),
(10428, 'Assis Brasil', 2012, 'AC', 31, 'BR', -10.88334000, -70.01314000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q742227'),
(10429, 'Assis Chateaubriand', 2022, 'PR', 31, 'BR', -24.40824000, -53.53328000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q742227'),
(10430, 'Assunção', 2005, 'PB', 31, 'BR', -7.07371000, -36.69894000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q742227'),
(10431, 'Assunção do Piauí', 2008, 'PI', 31, 'BR', -5.85575000, -40.94796000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q742227'),
(10432, 'Astolfo Dutra', 1998, 'MG', 31, 'BR', -21.30413000, -42.88194000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1756260'),
(10433, 'Astorga', 2022, 'PR', 31, 'BR', -23.24011000, -51.69541000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2007427'),
(10434, 'Atalaia', 2007, 'AL', 31, 'BR', -9.50194000, -36.02278000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q928274'),
(10435, 'Atalaia', 2022, 'PR', 31, 'BR', -23.12728000, -52.04286000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q928274'),
(10436, 'Atalaia do Norte', 2004, 'AM', 31, 'BR', -5.66448000, -71.82776000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q928274'),
(10437, 'Atalanta', 2014, 'SC', 31, 'BR', -27.44296000, -49.76089000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1815704'),
(10438, 'Ataléia', 1998, 'MG', 31, 'BR', -18.18353000, -41.16721000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1815704'),
(10439, 'Atibaia', 2021, 'SP', 31, 'BR', -23.13099000, -46.58896000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1637405'),
(10440, 'Atins', 2015, 'MA', 31, 'BR', -2.57017000, -42.74229000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q22074324'),
(10441, 'Atílio Vivacqua', 2018, 'ES', 31, 'BR', -20.95472000, -41.19096000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q22074324'),
(10442, 'Augustinópolis', 2020, 'TO', 31, 'BR', -5.51080000, -47.91538000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q22074324'),
(10443, 'Augusto Corrêa', 2009, 'PA', 31, 'BR', -1.02167000, -46.63500000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1804338'),
(10444, 'Augusto Pestana', 2001, 'RS', 31, 'BR', -28.54034000, -53.97927000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1804338'),
(10445, 'Augusto Severo', 2019, 'RN', 31, 'BR', -5.90516000, -37.30121000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1804338'),
(10446, 'Augusto de Lima', 1998, 'MG', 31, 'BR', -18.10720000, -44.16792000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1804338'),
(10447, 'Aurelino Leal', 2002, 'BA', 31, 'BR', -14.36310000, -39.47961000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1804338'),
(10448, 'Auriflama', 2021, 'SP', 31, 'BR', -20.68556000, -50.55472000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q268956'),
(10449, 'Aurilândia', 2000, 'GO', 31, 'BR', -16.67681000, -50.52582000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q268956'),
(10450, 'Aurora', 2016, 'CE', 31, 'BR', -7.00011000, -38.97042000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2289941'),
(10451, 'Aurora', 2014, 'SC', 31, 'BR', -27.33930000, -49.57823000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2012016'),
(10452, 'Aurora do Pará', 2009, 'PA', 31, 'BR', -2.30323000, -47.75621000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q2012016'),
(10453, 'Aurora do Tocantins', 2020, 'TO', 31, 'BR', -12.61624000, -46.44441000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2012016'),
(10454, 'Autazes', 2004, 'AM', 31, 'BR', -3.57972000, -59.13056000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1772695'),
(10455, 'Avanhandava', 2021, 'SP', 31, 'BR', -21.46596000, -49.94791000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1772695'),
(10456, 'Avaré', 2021, 'SP', 31, 'BR', -23.03402000, -48.88802000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q782812'),
(10457, 'Avaí', 2021, 'SP', 31, 'BR', -22.17789000, -49.30511000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q782812'),
(10458, 'Aveiro', 2009, 'PA', 31, 'BR', -3.66721000, -56.01637000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q782812'),
(10459, 'Avelino Lopes', 2008, 'PI', 31, 'BR', -10.11463000, -43.89286000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q782812'),
(10460, 'Avelinópolis', 2000, 'GO', 31, 'BR', -16.49121000, -49.76368000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q782812'),
(10461, 'Axixá', 2015, 'MA', 31, 'BR', -2.84586000, -44.10516000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q630926'),
(10462, 'Axixá do Tocantins', 2020, 'TO', 31, 'BR', -5.64850000, -47.77278000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q630926'),
(10463, 'Açailândia', 2015, 'MA', 31, 'BR', -4.69214000, -47.34302000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1763534'),
(10464, 'Açu', 2019, 'RN', 31, 'BR', -5.57667000, -36.90861000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1788873'),
(10465, 'Açucena', 1998, 'MG', 31, 'BR', -19.07306000, -42.54639000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1791887'),
(10466, 'Babaçulândia', 2020, 'TO', 31, 'BR', -7.20516000, -47.76821000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1791887'),
(10467, 'Bacabal', 2015, 'MA', 31, 'BR', -4.29167000, -44.79167000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1807313'),
(10468, 'Bacabeira', 2015, 'MA', 31, 'BR', -2.86866000, -44.34505000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1807313'),
(10469, 'Bacuri', 2015, 'MA', 31, 'BR', -1.65142000, -45.21995000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1807313'),
(10470, 'Bacurituba', 2015, 'MA', 31, 'BR', -2.64645000, -44.65477000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1807313'),
(10471, 'Bady Bassitt', 2021, 'SP', 31, 'BR', -20.91806000, -49.44528000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q268782'),
(10472, 'Baependi', 1998, 'MG', 31, 'BR', -21.97623000, -44.85558000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q947342'),
(10473, 'Bagre', 2009, 'PA', 31, 'BR', -2.40166000, -50.16628000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1804902'),
(10474, 'Bagé', 2001, 'RS', 31, 'BR', -31.33139000, -54.10694000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q119140'),
(10475, 'Baianópolis', 2002, 'BA', 31, 'BR', -12.48331000, -44.53105000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q119140'),
(10476, 'Baixa Grande', 2002, 'BA', 31, 'BR', -11.97735000, -40.17993000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1762467'),
(10477, 'Baixa Grande do Ribeiro', 2008, 'PI', 31, 'BR', -8.69171000, -45.12583000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1762467'),
(10478, 'Baixio', 2016, 'CE', 31, 'BR', -6.70726000, -38.75748000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1808481'),
(10479, 'Baixo Guandu', 2018, 'ES', 31, 'BR', -19.51889000, -41.01583000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q804041'),
(10480, 'Baião', 2009, 'PA', 31, 'BR', -2.79056000, -49.67167000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q804068'),
(10481, 'Balbinos', 2021, 'SP', 31, 'BR', -21.90089000, -49.33509000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q804068'),
(10482, 'Baldim', 1998, 'MG', 31, 'BR', -19.23659000, -43.84684000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q804068'),
(10483, 'Baliza', 2000, 'GO', 31, 'BR', -16.36672000, -52.43611000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q804068'),
(10484, 'Balneário Arroio do Silva', 2014, 'SC', 31, 'BR', -29.00699000, -49.47849000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1758002'),
(10485, 'Balneário Barra do Sul', 2014, 'SC', 31, 'BR', -26.41890000, -48.65147000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1758002'),
(10486, 'Balneário Camboriú', 2014, 'SC', 31, 'BR', -26.99056000, -48.63472000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q598016'),
(10487, 'Balneário Gaivota', 2014, 'SC', 31, 'BR', -29.13006000, -49.61187000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1757752'),
(10488, 'Balneário Pinhal', 2001, 'RS', 31, 'BR', -30.21275000, -50.29309000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1757752'),
(10489, 'Balneário Piçarras', 2014, 'SC', 31, 'BR', -26.75580000, -48.76000000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1757752'),
(10490, 'Balneário Rincão', 2014, 'SC', 31, 'BR', -28.82679000, -49.25425000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q2881477'),
(10491, 'Balsa Nova', 2022, 'PR', 31, 'BR', -25.52151000, -49.67738000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2881477'),
(10492, 'Balsas', 2015, 'MA', 31, 'BR', -7.53250000, -46.03556000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1813001'),
(10493, 'Bambuí', 1998, 'MG', 31, 'BR', -20.10451000, -45.98743000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1756027'),
(10494, 'Banabuiú', 2016, 'CE', 31, 'BR', -5.23555000, -38.87457000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1756027'),
(10495, 'Bananal', 2021, 'SP', 31, 'BR', -22.73751000, -44.33413000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1756027'),
(10496, 'Bananeiras', 2005, 'PB', 31, 'BR', -6.75000000, -35.63333000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q525672'),
(10497, 'Bandeira', 1998, 'MG', 31, 'BR', -15.88042000, -40.59969000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1756019'),
(10498, 'Bandeira do Sul', 1998, 'MG', 31, 'BR', -21.72907000, -46.38283000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1756019'),
(10499, 'Bandeirante', 2014, 'SC', 31, 'BR', -26.76857000, -53.64907000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q941669'),
(10500, 'Bandeirantes', 2022, 'PR', 31, 'BR', -23.15516000, -50.33873000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2064780'),
(10501, 'Bandeirantes', 2010, 'MS', 31, 'BR', -19.80863000, -54.33095000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2064780'),
(10502, 'Bandeirantes do Tocantins', 2020, 'TO', 31, 'BR', -8.00418000, -48.68223000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2064780'),
(10503, 'Bannach', 2009, 'PA', 31, 'BR', -7.46864000, -50.65904000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2064780'),
(10504, 'Banzaê', 2002, 'BA', 31, 'BR', -10.61578000, -38.62688000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q2064780'),
(10505, 'Baraúna', 2005, 'PB', 31, 'BR', -6.61853000, -36.26964000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q2662559'),
(10506, 'Baraúna', 2019, 'RN', 31, 'BR', -4.97189000, -37.52234000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1970827'),
(10507, 'Barbacena', 1998, 'MG', 31, 'BR', -21.25031000, -43.84171000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1970827'),
(10508, 'Barbalha', 2016, 'CE', 31, 'BR', -7.43604000, -39.35273000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q602143'),
(10509, 'Barbosa', 2021, 'SP', 31, 'BR', -21.29619000, -49.92293000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q602143'),
(10510, 'Barbosa Ferraz', 2022, 'PR', 31, 'BR', -24.06134000, -52.05494000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q602143'),
(10511, 'Barcarena', 2009, 'PA', 31, 'BR', -1.54656000, -48.63248000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2009595'),
(10512, 'Barcelona', 2019, 'RN', 31, 'BR', -5.94268000, -35.92019000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2009595'),
(10513, 'Barcelos', 2004, 'AM', 31, 'BR', -0.97357000, -62.92690000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q807939'),
(10514, 'Bariri', 2021, 'SP', 31, 'BR', -22.07444000, -48.74028000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1759107'),
(10515, 'Barra', 2002, 'BA', 31, 'BR', -11.08944000, -43.14167000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2013948'),
(10516, 'Barra Bonita', 2021, 'SP', 31, 'BR', -22.49472000, -48.55806000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q22048157'),
(10517, 'Barra Bonita', 2014, 'SC', 31, 'BR', -26.68537000, -53.41393000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1815639'),
(10518, 'Barra Funda', 2001, 'RS', 31, 'BR', -27.91562000, -53.03286000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1815639'),
(10519, 'Barra Longa', 1998, 'MG', 31, 'BR', -20.27861000, -43.07170000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1815639'),
(10520, 'Barra Mansa', 1997, 'RJ', 31, 'BR', -22.54417000, -44.17139000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q808797'),
(10521, 'Barra Velha', 2014, 'SC', 31, 'BR', -26.63222000, -48.68472000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q808787'),
(10522, 'Barra d\'Alcântara', 2008, 'PI', 31, 'BR', -6.54095000, -42.11504000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1938757'),
(10523, 'Barra da Estiva', 2002, 'BA', 31, 'BR', -13.62611000, -41.32694000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1795377'),
(10524, 'Barra de Guabiraba', 2006, 'PE', 31, 'BR', -8.39450000, -35.62054000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1795377'),
(10525, 'Barra de Santa Rosa', 2005, 'PB', 31, 'BR', -6.79042000, -36.00175000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1795377'),
(10526, 'Barra de Santana', 2005, 'PB', 31, 'BR', -7.59054000, -35.98824000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1795377'),
(10527, 'Barra de Santo Antônio', 2007, 'AL', 31, 'BR', -9.40472000, -35.50722000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1795908'),
(10528, 'Barra de São Francisco', 2018, 'ES', 31, 'BR', -18.75500000, -40.89083000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1794183'),
(10529, 'Barra de São Miguel', 2007, 'AL', 31, 'BR', -9.81230000, -35.96087000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1794183'),
(10530, 'Barra de São Miguel', 2005, 'PB', 31, 'BR', -7.71640000, -36.25481000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1794183'),
(10531, 'Barra do Bugres', 2011, 'MT', 31, 'BR', -15.07250000, -57.18111000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1805676'),
(10532, 'Barra do Chapéu', 2021, 'SP', 31, 'BR', -24.44047000, -49.08615000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1805676'),
(10533, 'Barra do Choça', 2002, 'BA', 31, 'BR', -15.01024000, -40.66731000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q1805676'),
(10534, 'Barra do Corda', 2015, 'MA', 31, 'BR', -5.50556000, -45.24333000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1807301'),
(10535, 'Barra do Garças', 2011, 'MT', 31, 'BR', -15.89000000, -52.25667000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q577720'),
(10536, 'Barra do Guarita', 2001, 'RS', 31, 'BR', -27.21739000, -53.76461000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q577720'),
(10537, 'Barra do Jacaré', 2022, 'PR', 31, 'BR', -23.11066000, -50.15719000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q577720'),
(10538, 'Barra do Mendes', 2002, 'BA', 31, 'BR', -12.16094000, -42.03542000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q577720'),
(10539, 'Barra do Ouro', 2020, 'TO', 31, 'BR', -7.75845000, -47.58781000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q577720'),
(10540, 'Barra do Piraí', 1997, 'RJ', 31, 'BR', -22.47000000, -43.82556000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1750175'),
(10541, 'Barra do Quaraí', 2001, 'RS', 31, 'BR', -30.17445000, -57.31007000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q1750175'),
(10542, 'Barra do Ribeiro', 2001, 'RS', 31, 'BR', -30.34435000, -51.36125000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1750175'),
(10543, 'Barra do Rio Azul', 2001, 'RS', 31, 'BR', -27.39848000, -52.40887000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1750175'),
(10544, 'Barra do Rocha', 2002, 'BA', 31, 'BR', -14.07510000, -39.59068000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1750175'),
(10545, 'Barra do Turvo', 2021, 'SP', 31, 'BR', -24.89045000, -48.40790000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1750175'),
(10546, 'Barra dos Coqueiros', 2003, 'SE', 31, 'BR', -10.90889000, -37.03861000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1784872'),
(10547, 'Barracão', 2022, 'PR', 31, 'BR', -26.24009000, -53.52586000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q1784872'),
(10548, 'Barracão', 2001, 'RS', 31, 'BR', -27.73726000, -51.44229000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q970961'),
(10549, 'Barras', 2008, 'PI', 31, 'BR', -4.24444000, -42.29444000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q808836'),
(10550, 'Barreira', 2016, 'CE', 31, 'BR', -4.37049000, -38.61021000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q808836'),
(10551, 'Barreiras', 2002, 'BA', 31, 'BR', -12.15278000, -44.99000000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q31859686'),
(10552, 'Barreiras do Piauí', 2008, 'PI', 31, 'BR', -9.99042000, -45.69329000, '2019-10-05 22:34:57', '2020-05-01 17:22:37', 1, 'Q31859686'),
(10553, 'Barreirinha', 2004, 'AM', 31, 'BR', -2.79333000, -57.07000000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1772565'),
(10554, 'Barreirinhas', 2015, 'MA', 31, 'BR', -2.75136000, -42.83432000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2040600'),
(10555, 'Barreiro do Jaíba', 1998, 'MG', 31, 'BR', -15.61532000, -43.59187000, '2019-10-05 22:34:57', '2020-05-01 17:22:36', 1, 'Q22048258'),
(10556, 'Barreiros', 2006, 'PE', 31, 'BR', -8.81833000, -35.18639000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2102404'),
(10557, 'Barretos', 2021, 'SP', 31, 'BR', -20.55722000, -48.56778000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1637488'),
(10558, 'Barrinha', 2021, 'SP', 31, 'BR', -21.19361000, -48.16389000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1760074'),
(10559, 'Barro', 2016, 'CE', 31, 'BR', -7.13537000, -38.73373000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2028645'),
(10560, 'Barro Alto', 2002, 'BA', 31, 'BR', -11.76083000, -41.91167000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q22048272'),
(10561, 'Barro Alto', 2000, 'GO', 31, 'BR', -14.91276000, -48.87739000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2155729'),
(10562, 'Barro Duro', 2008, 'PI', 31, 'BR', -5.85063000, -42.45878000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2155729'),
(10563, 'Barro Preto', 2002, 'BA', 31, 'BR', -14.75795000, -39.40711000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2155729'),
(10564, 'Barrocas', 2002, 'BA', 31, 'BR', -11.60190000, -39.12267000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2155729'),
(10565, 'Barrolândia', 2020, 'TO', 31, 'BR', -9.84999000, -48.83437000, '2019-10-05 22:34:57', '2020-05-01 17:22:38', 1, 'Q2155729'),
(10566, 'Barroquinha', 2016, 'CE', 31, 'BR', -3.01889000, -41.13611000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2020987'),
(10567, 'Barros Cassal', 2001, 'RS', 31, 'BR', -29.13534000, -52.57508000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q2020987'),
(10568, 'Barroso', 1998, 'MG', 31, 'BR', -21.17886000, -43.95708000, '2019-10-05 22:34:57', '2019-10-05 22:34:57', 1, 'Q1792962'),
(10569, 'Barueri', 2021, 'SP', 31, 'BR', -23.51056000, -46.87611000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q461773'),
(10570, 'Barão', 2001, 'RS', 31, 'BR', -29.39636000, -51.54620000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q1758344'),
(10571, 'Barão de Antonina', 2021, 'SP', 31, 'BR', -23.57097000, -49.56660000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q1758344'),
(10572, 'Barão de Cocais', 1998, 'MG', 31, 'BR', -19.87980000, -43.49476000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q770910'),
(10573, 'Barão de Cotegipe', 2001, 'RS', 31, 'BR', -27.55108000, -52.44751000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q770910'),
(10574, 'Barão de Grajaú', 2015, 'MA', 31, 'BR', -6.62978000, -43.20317000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q770910'),
(10575, 'Barão de Melgaço', 2011, 'MT', 31, 'BR', -16.19444000, -55.96750000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q1806364'),
(10576, 'Barão de Monte Alto', 1998, 'MG', 31, 'BR', -21.26536000, -42.27886000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q1806364'),
(10577, 'Barão do Triunfo', 2001, 'RS', 31, 'BR', -30.39257000, -51.79493000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q1806364'),
(10578, 'Bastos', 2021, 'SP', 31, 'BR', -21.92194000, -50.73389000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q971397'),
(10579, 'Bataguassu', 2010, 'MS', 31, 'BR', -21.78550000, -52.56542000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q971397'),
(10580, 'Bataiporã', 2010, 'MS', 31, 'BR', -22.29528000, -53.27111000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q1796657'),
(10581, 'Batalha', 2007, 'AL', 31, 'BR', -9.73256000, -37.08877000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2028288'),
(10582, 'Batalha', 2008, 'PI', 31, 'BR', -3.99736000, -42.10645000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2012017'),
(10583, 'Batatais', 2021, 'SP', 31, 'BR', -20.83515000, -47.56742000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q650500'),
(10584, 'Batayporã', 2010, 'MS', 31, 'BR', -22.46496000, -53.17683000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q650500'),
(10585, 'Baturité', 2016, 'CE', 31, 'BR', -4.37308000, -38.85915000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q2021082'),
(10586, 'Bauru', 2021, 'SP', 31, 'BR', -22.31472000, -49.06056000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q241645'),
(10587, 'Bayeux', 2005, 'PB', 31, 'BR', -7.12136000, -34.91725000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2065978'),
(10588, 'Baía Formosa', 2019, 'RN', 31, 'BR', -6.43006000, -35.05050000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q2065978'),
(10589, 'Baía da Traição', 2005, 'PB', 31, 'BR', -6.66287000, -34.97272000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q2065978'),
(10590, 'Bebedouro', 2021, 'SP', 31, 'BR', -20.94944000, -48.47917000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1817181'),
(10591, 'Beberibe', 2016, 'CE', 31, 'BR', -4.17972000, -38.13056000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2028035'),
(10592, 'Beira Rio', 2002, 'BA', 31, 'BR', -12.25438000, -42.75529000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q22074198'),
(10593, 'Bela Cruz', 2016, 'CE', 31, 'BR', -3.05056000, -40.16778000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2021089'),
(10594, 'Bela Vista', 2010, 'MS', 31, 'BR', -22.10889000, -56.52111000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1148023'),
(10595, 'Bela Vista da Caroba', 2022, 'PR', 31, 'BR', -25.87398000, -53.62755000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1148023'),
(10596, 'Bela Vista de Goiás', 2000, 'GO', 31, 'BR', -16.97278000, -48.95333000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q815101'),
(10597, 'Bela Vista de Minas', 1998, 'MG', 31, 'BR', -19.80136000, -43.10273000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q815101'),
(10598, 'Bela Vista do Maranhão', 2015, 'MA', 31, 'BR', -3.79140000, -45.29546000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q815101'),
(10599, 'Bela Vista do Paraíso', 2022, 'PR', 31, 'BR', -22.99667000, -51.19056000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q1805395'),
(10600, 'Bela Vista do Piauí', 2008, 'PI', 31, 'BR', -7.91606000, -41.90091000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q1805395'),
(10601, 'Bela Vista do Toldo', 2014, 'SC', 31, 'BR', -26.45944000, -50.51061000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1805395'),
(10602, 'Belford Roxo', 1997, 'RJ', 31, 'BR', -22.76417000, -43.39944000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q737636'),
(10603, 'Belmiro Braga', 1998, 'MG', 31, 'BR', -21.96440000, -43.45933000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q737636'),
(10604, 'Belmonte', 2002, 'BA', 31, 'BR', -15.86126000, -38.87982000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q816112'),
(10605, 'Belmonte', 2014, 'SC', 31, 'BR', -26.85317000, -53.61399000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q816112'),
(10606, 'Belo Campo', 2002, 'BA', 31, 'BR', -14.87419000, -41.22671000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q816112'),
(10607, 'Belo Horizonte', 1998, 'MG', 31, 'BR', -19.92083000, -43.93778000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q42800'),
(10608, 'Belo Jardim', 2006, 'PE', 31, 'BR', -8.33556000, -36.42417000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q949079'),
(10609, 'Belo Monte', 2007, 'AL', 31, 'BR', -9.80521000, -37.19157000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q949079'),
(10610, 'Belo Oriente', 1998, 'MG', 31, 'BR', -19.25558000, -42.44289000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q816123'),
(10611, 'Belo Vale', 1998, 'MG', 31, 'BR', -20.41584000, -44.06470000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q816123'),
(10612, 'Belterra', 2009, 'PA', 31, 'BR', -3.01258000, -54.99128000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q609824'),
(10613, 'Belágua', 2015, 'MA', 31, 'BR', -3.08799000, -43.45673000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q609824'),
(10614, 'Belém', 2005, 'PB', 31, 'BR', -6.69167000, -35.53333000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q792606'),
(10615, 'Belém', 2009, 'PA', 31, 'BR', -1.45583000, -48.50444000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q12829733'),
(10616, 'Belém', 2007, 'AL', 31, 'BR', -9.54424000, -36.50196000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q12829733'),
(10617, 'Belém de Maria', 2006, 'PE', 31, 'BR', -8.57849000, -35.82495000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q12829733'),
(10618, 'Belém de São Francisco', 2006, 'PE', 31, 'BR', -8.75389000, -38.96583000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q1811159'),
(10619, 'Belém do Brejo do Cruz', 2005, 'PB', 31, 'BR', -6.18861000, -37.53583000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q285051'),
(10620, 'Belém do Piauí', 2008, 'PI', 31, 'BR', -7.40657000, -40.98873000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q285051'),
(10621, 'Belém do São Francisco', 2006, 'PE', 31, 'BR', -8.53386000, -38.98166000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q285051'),
(10622, 'Beneditinos', 2008, 'PI', 31, 'BR', -5.45000000, -42.36667000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1981008'),
(10623, 'Benedito Leite', 2015, 'MA', 31, 'BR', -7.10697000, -44.58763000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1981008'),
(10624, 'Benedito Novo', 2014, 'SC', 31, 'BR', -26.78592000, -49.43603000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1981008'),
(10625, 'Benevides', 2009, 'PA', 31, 'BR', -1.36139000, -48.24472000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1804361'),
(10626, 'Benjamin Constant', 2004, 'AM', 31, 'BR', -4.37555000, -70.03179000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1767926'),
(10627, 'Benjamin Constant do Sul', 2001, 'RS', 31, 'BR', -27.48726000, -52.65470000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1767926'),
(10628, 'Bento Fernandes', 2019, 'RN', 31, 'BR', -5.63426000, -35.81702000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1767926'),
(10629, 'Bento Gonçalves', 2001, 'RS', 31, 'BR', -29.09958000, -51.54344000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q548652'),
(10630, 'Bento de Abreu', 2021, 'SP', 31, 'BR', -21.34057000, -50.86542000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q548652'),
(10631, 'Bequimão', 2015, 'MA', 31, 'BR', -2.44889000, -44.78250000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q2068952'),
(10632, 'Berilo', 1998, 'MG', 31, 'BR', -16.86340000, -42.48889000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2068952'),
(10633, 'Berizal', 1998, 'MG', 31, 'BR', -15.67761000, -41.77137000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2068952'),
(10634, 'Bernardino Batista', 2005, 'PB', 31, 'BR', -6.47656000, -38.56998000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2068952'),
(10635, 'Bernardino de Campos', 2021, 'SP', 31, 'BR', -23.02698000, -49.49277000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q920204'),
(10636, 'Bernardo Sayão', 2020, 'TO', 31, 'BR', -7.97377000, -48.98609000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q920204'),
(10637, 'Bernardo do Mearim', 2015, 'MA', 31, 'BR', -4.67175000, -44.64279000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q920204'),
(10638, 'Bertioga', 2021, 'SP', 31, 'BR', -23.85444000, -46.13861000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q828319'),
(10639, 'Bertolínia', 2008, 'PI', 31, 'BR', -7.75477000, -43.79588000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q828319'),
(10640, 'Bertópolis', 1998, 'MG', 31, 'BR', -16.95442000, -40.56435000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q828319'),
(10641, 'Beruri', 2004, 'AM', 31, 'BR', -4.34371000, -61.75126000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q828319'),
(10642, 'Betim', 1998, 'MG', 31, 'BR', -19.96778000, -44.19833000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q737644'),
(10643, 'Betânia', 2006, 'PE', 31, 'BR', -8.28789000, -37.97622000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q737644'),
(10644, 'Betânia do Piauí', 2008, 'PI', 31, 'BR', -8.06430000, -40.85924000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q737644'),
(10645, 'Bezerros', 2006, 'PE', 31, 'BR', -8.23333000, -35.79694000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2011830'),
(10646, 'Bias Fortes', 1998, 'MG', 31, 'BR', -21.63008000, -43.75508000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q2011830'),
(10647, 'Bicas', 1998, 'MG', 31, 'BR', -21.73237000, -43.10454000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1792282'),
(10648, 'Biguaçu', 2014, 'SC', 31, 'BR', -27.49417000, -48.65556000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q1754582'),
(10649, 'Bilac', 2021, 'SP', 31, 'BR', -21.42076000, -50.47862000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1754582'),
(10650, 'Biquinhas', 1998, 'MG', 31, 'BR', -18.76231000, -45.54853000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1754582'),
(10651, 'Birigui', 2021, 'SP', 31, 'BR', -21.28861000, -50.34000000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q963648'),
(10652, 'Biritiba Mirim', 2021, 'SP', 31, 'BR', -23.57250000, -46.03861000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q518845'),
(10653, 'Biritiba-Mirim', 2021, 'SP', 31, 'BR', -23.62009000, -46.01938000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q518845'),
(10654, 'Biritinga', 2002, 'BA', 31, 'BR', -11.67075000, -38.78931000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q518845'),
(10655, 'Bituruna', 2022, 'PR', 31, 'BR', -26.18538000, -51.54497000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q518845'),
(10656, 'Blumenau', 2014, 'SC', 31, 'BR', -26.91944000, -49.06611000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q155557'),
(10657, 'Boa Esperança', 2018, 'ES', 31, 'BR', -18.54000000, -40.29583000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q2089691'),
(10658, 'Boa Esperança', 1998, 'MG', 31, 'BR', -21.08132000, -45.62433000, '2019-10-05 22:34:58', '2020-05-01 17:22:36', 1, 'Q22062767'),
(10659, 'Boa Esperança', 2022, 'PR', 31, 'BR', -24.24372000, -52.73574000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q22062767'),
(10660, 'Boa Esperança do Iguaçu', 2022, 'PR', 31, 'BR', -25.63520000, -53.22621000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q963673'),
(10661, 'Boa Esperança do Sul', 2021, 'SP', 31, 'BR', -21.99250000, -48.39083000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q268938'),
(10662, 'Boa Hora', 2008, 'PI', 31, 'BR', -4.35576000, -42.14109000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q268938'),
(10663, 'Boa Nova', 2002, 'BA', 31, 'BR', -14.36412000, -40.22378000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q268938'),
(10664, 'Boa Ventura', 2005, 'PB', 31, 'BR', -7.45676000, -38.17678000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q268938'),
(10665, 'Boa Ventura de São Roque', 2022, 'PR', 31, 'BR', -24.84082000, -51.55638000, '2019-10-05 22:34:58', '2020-05-01 17:22:37', 1, 'Q268938'),
(10666, 'Boa Viagem', 2016, 'CE', 31, 'BR', -5.02536000, -39.84169000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1808337'),
(10667, 'Boa Vista', 2005, 'PB', 31, 'BR', -7.26995000, -36.15741000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1808337');

