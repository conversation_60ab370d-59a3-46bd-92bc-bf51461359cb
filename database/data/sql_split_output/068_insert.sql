INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(33913, 'Daroca', 5113, 'Z', 207, 'ES', 41.11475000, -1.41492000, '2019-10-05 22:45:43', '2022-08-29 11:42:54', 1, 'Q1110389'),
(33914, 'Darro', 5098, 'GR', 207, 'ES', 37.34987000, -3.29465000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q559665'),
(33915, '<PERSON><PERSON> Nueva', 5108, 'A', 207, 'ES', 38.11313000, -0.76028000, '2019-10-05 22:45:43', '2022-08-29 11:15:47', 1, 'Q1752092'),
(33916, 'Daya Vieja', 5108, 'A', 207, 'ES', 38.10480000, -0.73804000, '2019-10-05 22:45:43', '2022-08-29 11:15:47', 1, 'Q1768811'),
(33917, '<PERSON><PERSON>', 1191, 'SS', 207, 'ES', 43.29571000, -2.35213000, '2019-10-05 22:45:43', '2022-08-28 18:36:49', 1, 'Q1768811'),
(33918, 'Dehesa de <PERSON>jo', 1157, 'P', 207, 'ES', 42.81939000, -4.51019000, '2019-10-05 22:45:43', '2022-08-29 11:45:44', 1, 'Q1119428'),
(33919, 'Dehesa de Romanos', 1157, 'P', 207, 'ES', 42.63876000, -4.43518000, '2019-10-05 22:45:43', '2022-08-29 11:45:44', 1, 'Q1906992'),
(33920, 'Dehesas de Guadix', 5098, 'GR', 207, 'ES', 37.58876000, -3.10317000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q559308'),
(33921, 'Deifontes', 5098, 'GR', 207, 'ES', 37.32620000, -3.59568000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q559275'),
(33922, 'Deià', 1174, 'PM', 207, 'ES', 39.74806000, 2.64823000, '2019-10-05 22:45:43', '2020-05-01 17:23:15', 1, 'Q233337'),
(33923, 'Deleitosa', 1190, 'CC', 207, 'ES', 39.64359000, -5.64576000, '2019-10-05 22:45:43', '2022-08-28 18:12:22', 1, 'Q1644267'),
(33924, 'Delicias', 1177, 'HU', 207, 'ES', 41.64928000, -0.90757000, '2019-10-05 22:45:43', '2022-08-29 12:06:20', 1, 'Q1644267'),
(33925, 'Deltebre', 1203, 'T', 207, 'ES', 40.71944000, 0.70835000, '2019-10-05 22:45:43', '2022-08-29 10:57:33', 1, 'Q24015201'),
(33926, 'Denia', 5108, 'A', 207, 'ES', 38.84078000, 0.10574000, '2019-10-05 22:45:43', '2022-08-29 11:15:47', 1, 'Q646725'),
(33927, 'Derio', 5094, 'BI', 207, 'ES', 43.30544000, -2.88116000, '2019-10-05 22:45:43', '2022-08-28 18:25:56', 1, 'Q574712'),
(33928, 'Descargamaría', 1190, 'CC', 207, 'ES', 40.30446000, -6.48666000, '2019-10-05 22:45:43', '2022-08-28 18:12:22', 1, 'Q1638600'),
(33929, 'Desojo', 1204, 'NA', 207, 'ES', 42.58790000, -2.27438000, '2019-10-05 22:45:43', '2022-08-29 12:06:07', 1, 'Q546937'),
(33930, 'Destriana', 1200, 'LE', 207, 'ES', 42.32729000, -6.09573000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q1622295'),
(33931, 'Deza', 1208, 'SO', 207, 'ES', 41.46373000, -2.02046000, '2019-10-05 22:45:43', '2022-08-29 11:51:23', 1, 'Q24001863'),
(33933, 'Dicastillo', 1204, 'NA', 207, 'ES', 42.59647000, -2.02666000, '2019-10-05 22:45:43', '2022-08-29 12:06:07', 1, 'Q1627528'),
(33934, 'Diezma', 5098, 'GR', 207, 'ES', 37.31982000, -3.33256000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q1627528'),
(33935, 'Dios le Guarde', 1147, 'SA', 207, 'ES', 40.64283000, -6.31511000, '2019-10-05 22:45:43', '2022-08-29 11:44:51', 1, 'Q1650163'),
(33936, 'Dolores', 5108, 'A', 207, 'ES', 38.14002000, -0.77088000, '2019-10-05 22:45:43', '2022-08-29 11:15:47', 1, 'Q1650163'),
(33937, 'Domeño', 1175, 'V', 207, 'ES', 39.66115000, -0.67077000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q1998540'),
(33938, 'Domingo García', 1192, 'SG', 207, 'ES', 41.11528000, -4.37927000, '2019-10-05 22:45:43', '2022-08-29 11:50:42', 1, 'Q1998540'),
(33939, 'Domingo Pérez', 1205, 'TO', 207, 'ES', 39.97661000, -4.50554000, '2019-10-05 22:45:43', '2022-08-29 11:08:29', 1, 'Q1998540'),
(33941, 'Don Benito', 5092, 'BA', 207, 'ES', 38.95627000, -5.86162000, '2019-10-05 22:45:43', '2022-08-28 18:09:23', 1, 'Q24008796'),
(33942, 'Don Álvaro', 5092, 'BA', 207, 'ES', 38.84837000, -6.27475000, '2019-10-05 22:45:43', '2022-08-28 18:09:23', 1, 'Q1387108'),
(33943, 'Doneztebe', 1204, 'NA', 207, 'ES', 43.13333000, -1.66667000, '2019-10-05 22:45:43', '2022-08-29 12:06:07', 1, 'Q1647359'),
(33944, 'Donhierro', 1192, 'SG', 207, 'ES', 41.11642000, -4.69670000, '2019-10-05 22:45:43', '2022-08-29 11:50:42', 1, 'Q427935'),
(33945, 'Donjimeno', 1189, 'AV', 207, 'ES', 40.95978000, -4.84627000, '2019-10-05 22:45:43', '2022-08-29 11:49:56', 1, 'Q1607934'),
(33947, 'Donvidas', 1189, 'AV', 207, 'ES', 41.08954000, -4.80634000, '2019-10-05 22:45:43', '2022-08-29 11:49:56', 1, 'Q10313'),
(33948, 'Dos Aguas', 1175, 'V', 207, 'ES', 39.28333000, -0.80000000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q680697'),
(33949, 'Dos Hermanas', 1193, 'SE', 207, 'ES', 37.28287000, -5.92088000, '2019-10-05 22:45:43', '2022-08-28 19:08:49', 1, 'Q680697'),
(33950, 'Dos Torres', 5097, 'CO', 207, 'ES', 38.45000000, -4.90000000, '2019-10-05 22:45:43', '2022-08-28 18:49:38', 1, 'Q1373904'),
(33951, 'Dosbarrios', 1205, 'TO', 207, 'ES', 39.88199000, -3.48419000, '2019-10-05 22:45:43', '2022-08-29 11:08:29', 1, 'Q669145'),
(33952, 'Dosrius', 5102, 'B', 207, 'ES', 41.58333000, 2.41667000, '2019-10-05 22:45:43', '2022-08-29 10:50:00', 1, 'Q11968'),
(33953, 'Dozón', 1167, 'PO', 207, 'ES', 42.58333000, -8.01667000, '2019-10-05 22:45:43', '2022-08-28 17:57:54', 1, 'Q1614585'),
(33954, 'Doña Mencía', 5097, 'CO', 207, 'ES', 37.55346000, -4.35602000, '2019-10-05 22:45:43', '2022-08-28 18:49:38', 1, 'Q24011723'),
(33955, 'Doñinos de Ledesma', 1147, 'SA', 207, 'ES', 41.01251000, -6.03412000, '2019-10-05 22:45:43', '2022-08-29 11:44:51', 1, 'Q1640625'),
(33956, 'Doñinos de Salamanca', 1147, 'SA', 207, 'ES', 40.95978000, -5.74349000, '2019-10-05 22:45:43', '2022-08-29 11:44:51', 1, 'Q1640688'),
(33958, 'Driebes', 5107, 'GU', 207, 'ES', 40.24498000, -3.04165000, '2019-10-05 22:45:43', '2022-08-29 11:06:45', 1, 'Q1654309'),
(33959, 'Dueñas', 1157, 'P', 207, 'ES', 41.87717000, -4.54714000, '2019-10-05 22:45:43', '2022-08-29 11:45:44', 1, 'Q1657014'),
(33960, 'Dumbría', 5089, 'C', 207, 'ES', 43.00836000, -9.11328000, '2019-10-05 22:45:43', '2022-08-28 13:37:17', 1, 'Q549319'),
(33961, 'Durango', 5094, 'BI', 207, 'ES', 43.17124000, -2.63380000, '2019-10-05 22:45:43', '2022-08-28 18:25:55', 1, 'Q549319'),
(33962, 'Duruelo', 1192, 'SG', 207, 'ES', 41.23622000, -3.64914000, '2019-10-05 22:45:43', '2022-08-29 11:50:42', 1, 'Q31916001'),
(33963, 'Duruelo de la Sierra', 1208, 'SO', 207, 'ES', 41.95448000, -2.93086000, '2019-10-05 22:45:43', '2022-08-29 11:51:23', 1, 'Q832246'),
(33964, 'Durón', 5107, 'GU', 207, 'ES', 40.62530000, -2.72662000, '2019-10-05 22:45:43', '2022-08-29 11:06:45', 1, 'Q24011213'),
(33965, 'Dílar', 5098, 'GR', 207, 'ES', 37.07282000, -3.60134000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q559313'),
(33966, 'Dúdar', 5098, 'GR', 207, 'ES', 37.18579000, -3.48347000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q559119'),
(33967, 'Dúrcal', 5098, 'GR', 207, 'ES', 36.98788000, -3.56601000, '2019-10-05 22:45:43', '2022-08-28 18:52:57', 1, 'Q559839'),
(33968, 'Ea', 5094, 'BI', 207, 'ES', 43.37985000, -2.58556000, '2019-10-05 22:45:43', '2022-08-28 18:25:55', 1, 'Q559839'),
(33969, 'Echarri', 1204, 'NA', 207, 'ES', 42.78017000, -1.82525000, '2019-10-05 22:45:43', '2022-08-29 12:06:07', 1, 'Q943700'),
(33970, 'Echarri-Aranaz', 1204, 'NA', 207, 'ES', 42.90791000, -2.06474000, '2019-10-05 22:45:43', '2022-08-29 12:06:07', 1, 'Q1648046'),
(33971, 'Echo', 1177, 'HU', 207, 'ES', 42.73844000, -0.75016000, '2019-10-05 22:45:43', '2022-08-29 12:06:20', 1, 'Q33244378'),
(33972, 'Eibar', 1191, 'SS', 207, 'ES', 43.18493000, -2.47158000, '2019-10-05 22:45:43', '2022-08-28 18:36:49', 1, 'Q33244378'),
(33974, 'Ejea de los Caballeros', 5113, 'Z', 207, 'ES', 42.12632000, -1.13716000, '2019-10-05 22:45:43', '2022-08-29 11:42:54', 1, 'Q24019979'),
(33975, 'Ejeme', 1147, 'SA', 207, 'ES', 40.76685000, -5.53828000, '2019-10-05 22:45:43', '2022-08-29 11:44:51', 1, 'Q1648510'),
(33976, 'Ejulve', 5111, 'TE', 207, 'ES', 40.77633000, -0.55344000, '2019-10-05 22:45:43', '2022-08-29 11:29:44', 1, 'Q512992'),
(33978, 'El Astillero', 1170, 'S', 207, 'ES', 43.40094000, -3.82051000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q1443915'),
(33979, 'El Barco de Ávila', 1200, 'LE', 207, 'ES', 40.35710000, -5.52365000, '2019-10-05 22:45:43', '2020-05-01 17:23:16', 1, 'Q1381880'),
(33982, 'El Burgo de Ebro', 1177, 'HU', 207, 'ES', 41.57157000, -0.74128000, '2019-10-05 22:45:43', '2022-08-29 12:06:20', 1, 'Q1641115'),
(33983, 'El Burgo de Osma', 1200, 'LE', 207, 'ES', 41.58619000, -3.06522000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q485129'),
(33984, 'El Campello', 1175, 'V', 207, 'ES', 38.42885000, -0.39774000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q842224'),
(33990, 'El Castellar', 1177, 'HU', 207, 'ES', 40.36531000, -0.81734000, '2019-10-05 22:45:43', '2022-08-29 12:06:20', 1, 'Q1651555'),
(33994, 'El Cotillo', 1185, 'GC', 207, 'ES', 28.68264000, -14.00637000, '2019-10-05 22:45:43', '2022-08-29 12:06:32', 1, 'Q1324017'),
(33997, 'El Escorial', 1158, 'M', 207, 'ES', 40.58254000, -4.12846000, '2019-10-05 22:45:43', '2022-08-29 12:04:40', 1, 'Q48886443'),
(33999, 'El Grao', 1175, 'V', 207, 'ES', 39.97358000, 0.01284000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q1444358'),
(34000, 'El Hoyo de Pinares', 1200, 'LE', 207, 'ES', 40.50084000, -4.42357000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q1606989'),
(34002, 'El Molar', 1158, 'M', 207, 'ES', 40.73215000, -3.57969000, '2019-10-05 22:45:43', '2022-08-29 12:04:40', 1, 'Q37419614'),
(34003, 'El Pardo', 1158, 'M', 207, 'ES', 40.51454000, -3.77253000, '2019-10-05 22:45:43', '2022-08-29 12:04:40', 1, 'Q3314337'),
(34004, 'El Paso', 1185, 'GC', 207, 'ES', 28.65007000, -17.88274000, '2019-10-05 22:45:43', '2022-08-29 12:06:32', 1, 'Q434123'),
(34006, 'El Perelló', 1175, 'V', 207, 'ES', 39.27718000, -0.27569000, '2019-10-05 22:45:43', '2022-08-29 12:05:40', 1, 'Q1768717'),
(34009, 'El Plan', 1176, 'MU', 207, 'ES', 37.65000000, -1.01667000, '2019-10-05 22:45:43', '2022-08-29 12:05:49', 1, 'Q23993878'),
(34020, 'El Tiemblo', 1200, 'LE', 207, 'ES', 40.41533000, -4.50156000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q842675'),
(34022, 'El Toro', 1174, 'PM', 207, 'ES', 39.48477000, 2.48222000, '2019-10-05 22:45:43', '2019-10-05 22:45:43', 1, 'Q3817467'),
(34024, 'El Vellón', 1158, 'M', 207, 'ES', 40.76495000, -3.57969000, '2019-10-05 22:45:43', '2022-08-29 12:04:40', 1, 'Q1647849'),
(34029, 'El Álamo', 1158, 'M', 207, 'ES', 40.23066000, -3.99447000, '2019-10-05 22:45:44', '2022-08-29 12:04:40', 1, 'Q1984836'),
(34030, 'Elche', 1175, 'V', 207, 'ES', 38.26218000, -0.70107000, '2019-10-05 22:45:44', '2022-08-29 12:05:40', 1, 'Q10509'),
(34032, 'Elciego', 5093, 'VI', 207, 'ES', 42.51569000, -2.61897000, '2019-10-05 22:45:44', '2022-08-28 18:23:49', 1, 'Q1368834'),
(34033, 'Elda', 5108, 'A', 207, 'ES', 38.47783000, -0.79157000, '2019-10-05 22:45:44', '2022-08-29 11:15:47', 1, 'Q608985'),
(34035, 'Elgoibar', 1191, 'SS', 207, 'ES', 43.21601000, -2.41334000, '2019-10-05 22:45:44', '2022-08-28 18:36:49', 1, 'Q971263'),
(34036, 'Elgorriaga', 1204, 'NA', 207, 'ES', 43.13858000, -1.68657000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q1641834'),
(34037, 'Eljas', 1190, 'CC', 207, 'ES', 40.21648000, -6.84616000, '2019-10-05 22:45:44', '2022-08-28 18:12:22', 1, 'Q1613259'),
(34038, 'Elorrio', 5094, 'BI', 207, 'ES', 43.12924000, -2.54056000, '2019-10-05 22:45:44', '2022-08-28 18:25:55', 1, 'Q1242382'),
(34039, 'Elorz', 1204, 'NA', 207, 'ES', 42.73258000, -1.56152000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q1242382'),
(34040, 'Embid', 5107, 'GU', 207, 'ES', 40.97256000, -1.71206000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q1242382'),
(34041, 'Embid de Ariza', 5113, 'Z', 207, 'ES', 41.37830000, -1.97373000, '2019-10-05 22:45:44', '2022-08-29 11:42:54', 1, 'Q1638871'),
(34042, 'Emperador', 1175, 'V', 207, 'ES', 39.55000000, -0.33333000, '2019-10-05 22:45:44', '2022-08-29 12:05:40', 1, 'Q1638871'),
(34044, 'Encina de San Silvestre', 1147, 'SA', 207, 'ES', 41.01442000, -6.09268000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1640631'),
(34045, 'Encinacorba', 5113, 'Z', 207, 'ES', 41.28516000, -1.27516000, '2019-10-05 22:45:44', '2022-08-29 11:42:54', 1, 'Q24019977'),
(34046, 'Encinas', 1192, 'SG', 207, 'ES', 41.37511000, -3.66762000, '2019-10-05 22:45:44', '2022-08-29 11:50:42', 1, 'Q1919734'),
(34047, 'Encinas Reales', 5097, 'CO', 207, 'ES', 37.27419000, -4.48828000, '2019-10-05 22:45:44', '2022-08-28 18:49:38', 1, 'Q1919734'),
(34048, 'Encinas de Abajo', 1147, 'SA', 207, 'ES', 40.93493000, -5.47037000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1651897'),
(34049, 'Encinas de Arriba', 1147, 'SA', 207, 'ES', 40.77120000, -5.55661000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1651833'),
(34050, 'Encinas de Esgueva', 1183, 'VA', 207, 'ES', 41.75653000, -4.10357000, '2019-10-05 22:45:44', '2022-08-29 11:48:44', 1, 'Q1907397'),
(34051, 'Encinasola', 5099, 'H', 207, 'ES', 38.13413000, -6.86675000, '2019-10-05 22:45:44', '2022-08-28 19:00:43', 1, 'Q1445340'),
(34052, 'Encinasola de los Comendadores', 1147, 'SA', 207, 'ES', 41.03089000, -6.53261000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1648257'),
(34053, 'Encinedo', 1200, 'LE', 207, 'ES', 42.27049000, -6.59368000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q1618261'),
(34054, 'Encinillas', 1192, 'SG', 207, 'ES', 41.01809000, -4.15784000, '2019-10-05 22:45:44', '2022-08-29 11:50:42', 1, 'Q1938517'),
(34055, 'Enciso', 1171, 'LO', 207, 'ES', 42.14946000, -2.26953000, '2019-10-05 22:45:44', '2022-08-29 12:05:09', 1, 'Q954951'),
(34056, 'Encío', 1146, 'BU', 207, 'ES', 42.67115000, -3.08680000, '2019-10-05 22:45:44', '2022-08-29 11:24:20', 1, 'Q1607299'),
(34057, 'Endrinal', 1147, 'SA', 207, 'ES', 40.59109000, -5.80411000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1769898'),
(34058, 'Enguera', 1175, 'V', 207, 'ES', 38.97974000, -0.68683000, '2019-10-05 22:45:44', '2022-08-29 12:05:40', 1, 'Q24017179'),
(34059, 'Enguídanos', 5106, 'CU', 207, 'ES', 39.66667000, -1.60000000, '2019-10-05 22:45:44', '2022-08-29 11:05:01', 1, 'Q1771149'),
(34060, 'Enix', 5095, 'AL', 207, 'ES', 36.87732000, -2.60180000, '2019-10-05 22:45:44', '2022-08-28 18:41:41', 1, 'Q1771149'),
(34061, 'Entrala', 1161, 'ZA', 207, 'ES', 41.43006000, -5.75556000, '2019-10-05 22:45:44', '2022-08-29 11:48:03', 1, 'Q1652387'),
(34062, 'Entrena', 1171, 'LO', 207, 'ES', 42.38782000, -2.53066000, '2019-10-05 22:45:44', '2022-08-29 12:05:09', 1, 'Q23535'),
(34063, 'Entrimo', 5091, 'OR', 207, 'ES', 41.93297000, -8.11725000, '2019-10-05 22:45:44', '2022-08-28 17:53:26', 1, 'Q1606243'),
(34064, 'Entrín Bajo', 5092, 'BA', 207, 'ES', 38.71855000, -6.71349000, '2019-10-05 22:45:44', '2022-08-28 18:09:23', 1, 'Q1605952'),
(34065, 'Enériz', 1204, 'NA', 207, 'ES', 42.67095000, -1.72794000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q24012463'),
(34066, 'Erandio', 5094, 'BI', 207, 'ES', 43.30438000, -2.97352000, '2019-10-05 22:45:44', '2022-08-28 18:25:56', 1, 'Q24012463'),
(34067, 'Erla', 5113, 'Z', 207, 'ES', 42.11732000, -0.95019000, '2019-10-05 22:45:44', '2022-08-29 11:42:54', 1, 'Q1768732'),
(34068, 'Ermitagaña', 1204, 'NA', 207, 'ES', 42.81084000, -1.66409000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q2899165'),
(34069, 'Ermua', 5094, 'BI', 207, 'ES', 43.18725000, -2.50261000, '2019-10-05 22:45:44', '2022-08-28 18:25:55', 1, 'Q1242400'),
(34070, 'Errenteria', 1191, 'SS', 207, 'ES', 43.31195000, -1.90234000, '2019-10-05 22:45:44', '2022-08-28 18:36:49', 1, 'Q1242400'),
(34071, 'Errigoiti', 5094, 'BI', 207, 'ES', 43.31667000, -2.71667000, '2019-10-05 22:45:44', '2022-08-28 18:25:56', 1, 'Q1228030'),
(34072, 'Erustes', 1205, 'TO', 207, 'ES', 39.95667000, -4.49656000, '2019-10-05 22:45:44', '2022-08-29 11:08:29', 1, 'Q1644295'),
(34073, 'Es Castell', 1174, 'PM', 207, 'ES', 39.87760000, 4.28990000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q1644295'),
(34074, 'Es Molinar', 1174, 'PM', 207, 'ES', 39.56140000, 2.67517000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q1644295'),
(34075, 'Escacena del Campo', 5099, 'H', 207, 'ES', 37.40837000, -6.38870000, '2019-10-05 22:45:44', '2022-08-28 19:00:43', 1, 'Q970693'),
(34076, 'Escalante', 1170, 'S', 207, 'ES', 43.43678000, -3.51347000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q970693'),
(34077, 'Escalona', 1205, 'TO', 207, 'ES', 40.16690000, -4.40484000, '2019-10-05 22:45:44', '2022-08-29 11:08:29', 1, 'Q970693'),
(34078, 'Escalona del Prado', 1192, 'SG', 207, 'ES', 41.16682000, -4.12272000, '2019-10-05 22:45:44', '2022-08-29 11:50:42', 1, 'Q970693'),
(34079, 'Escalonilla', 1205, 'TO', 207, 'ES', 39.92570000, -4.35095000, '2019-10-05 22:45:44', '2022-08-29 11:08:29', 1, 'Q1630044'),
(34080, 'Escamilla', 5107, 'GU', 207, 'ES', 40.54979000, -2.56292000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q24011212'),
(34081, 'Escarabajosa de Cabezas', 1192, 'SG', 207, 'ES', 41.10470000, -4.19411000, '2019-10-05 22:45:44', '2022-08-29 11:50:42', 1, 'Q1939129'),
(34082, 'Escariche', 5107, 'GU', 207, 'ES', 40.40553000, -3.05310000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q1653750'),
(34083, 'Escatrón', 5113, 'Z', 207, 'ES', 41.29149000, -0.32308000, '2019-10-05 22:45:44', '2022-08-29 11:42:54', 1, 'Q572795'),
(34084, 'Escañuela', 5100, 'J', 207, 'ES', 37.87885000, -4.03376000, '2019-10-05 22:45:44', '2022-08-28 19:04:30', 1, 'Q765784'),
(34085, 'Escobar de Campos', 1200, 'LE', 207, 'ES', 42.31337000, -4.96573000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q1622282'),
(34086, 'Escobar de Polendos', 1192, 'SG', 207, 'ES', 41.09079000, -4.13117000, '2019-10-05 22:45:44', '2022-08-29 11:50:42', 1, 'Q1939245'),
(34087, 'Escobosa de Almazán', 1208, 'SO', 207, 'ES', 41.48626000, -2.37140000, '2019-10-05 22:45:44', '2022-08-29 11:51:23', 1, 'Q831414'),
(34088, 'Escopete', 5107, 'GU', 207, 'ES', 40.41324000, -3.00460000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q1653851'),
(34089, 'Escorca', 1174, 'PM', 207, 'ES', 39.82138000, 2.86941000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q950951'),
(34090, 'Escorihuela', 5111, 'TE', 207, 'ES', 40.54400000, -0.97078000, '2019-10-05 22:45:44', '2022-08-29 11:29:44', 1, 'Q1653504'),
(34091, 'Escucha', 5111, 'TE', 207, 'ES', 40.79467000, -0.81012000, '2019-10-05 22:45:44', '2022-08-29 11:29:44', 1, 'Q1653504'),
(34092, 'Escurial', 1190, 'CC', 207, 'ES', 39.16857000, -5.88465000, '2019-10-05 22:45:44', '2022-08-28 18:12:22', 1, 'Q1643658'),
(34093, 'Escurial de la Sierra', 1147, 'SA', 207, 'ES', 40.61731000, -5.95520000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1648441'),
(34094, 'Escúzar', 5098, 'GR', 207, 'ES', 37.06219000, -3.76126000, '2019-10-05 22:45:44', '2022-08-28 18:52:57', 1, 'Q559286'),
(34095, 'Esgos', 5091, 'OR', 207, 'ES', 42.32549000, -7.69691000, '2019-10-05 22:45:44', '2022-08-28 17:53:26', 1, 'Q559286'),
(34096, 'Esguevillas de Esgueva', 1183, 'VA', 207, 'ES', 41.75018000, -4.38062000, '2019-10-05 22:45:44', '2022-08-29 11:48:44', 1, 'Q614231'),
(34097, 'Eskoriatza', 1191, 'SS', 207, 'ES', 43.01829000, -2.52598000, '2019-10-05 22:45:44', '2022-08-28 18:36:49', 1, 'Q1633894'),
(34099, 'Eslava', 1204, 'NA', 207, 'ES', 42.56459000, -1.45947000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q1641801'),
(34100, 'Eslida', 5110, 'CS', 207, 'ES', 39.88333000, -0.30000000, '2019-10-05 22:45:44', '2022-08-29 11:26:42', 1, 'Q1769981'),
(34101, 'Espadaña', 1147, 'SA', 207, 'ES', 41.06085000, -6.28457000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1640849'),
(34102, 'Espadañedo', 1161, 'ZA', 207, 'ES', 42.11618000, -6.39419000, '2019-10-05 22:45:44', '2022-08-29 11:48:03', 1, 'Q1769076'),
(34103, 'Espadilla', 5110, 'CS', 207, 'ES', 40.03333000, -0.35000000, '2019-10-05 22:45:44', '2022-08-29 11:26:42', 1, 'Q577501'),
(34104, 'Esparragal', 1176, 'MU', 207, 'ES', 38.03333000, -1.08333000, '2019-10-05 22:45:44', '2022-08-29 12:05:49', 1, 'Q577501'),
(34105, 'Esparragalejo', 5092, 'BA', 207, 'ES', 38.94330000, -6.43549000, '2019-10-05 22:45:44', '2022-08-28 18:09:23', 1, 'Q1446351'),
(34106, 'Esparragosa de Lares', 5092, 'BA', 207, 'ES', 38.97517000, -5.26968000, '2019-10-05 22:45:44', '2022-08-28 18:09:23', 1, 'Q1407179'),
(34107, 'Esparragosa de la Serena', 5092, 'BA', 207, 'ES', 38.65100000, -5.60657000, '2019-10-05 22:45:44', '2022-08-28 18:09:23', 1, 'Q1372885'),
(34108, 'Esparreguera', 5102, 'B', 207, 'ES', 41.53809000, 1.87025000, '2019-10-05 22:45:44', '2022-08-29 10:50:00', 1, 'Q1372885'),
(34109, 'Espartinas', 1193, 'SE', 207, 'ES', 37.38154000, -6.12578000, '2019-10-05 22:45:44', '2022-08-28 19:08:49', 1, 'Q1628327'),
(34110, 'Espeja', 1147, 'SA', 207, 'ES', 40.56565000, -6.71582000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1628327'),
(34111, 'Espeja de San Marcelino', 1208, 'SO', 207, 'ES', 41.80227000, -3.22230000, '2019-10-05 22:45:44', '2022-08-29 11:51:23', 1, 'Q836172'),
(34112, 'Espejo', 5097, 'CO', 207, 'ES', 37.67980000, -4.55355000, '2019-10-05 22:45:44', '2022-08-28 18:49:38', 1, 'Q836172'),
(34113, 'Espejón', 1208, 'SO', 207, 'ES', 41.83023000, -3.25936000, '2019-10-05 22:45:44', '2022-08-29 11:51:23', 1, 'Q56241215'),
(34114, 'Espelúy', 5100, 'J', 207, 'ES', 38.03180000, -3.86309000, '2019-10-05 22:45:44', '2022-08-28 19:04:30', 1, 'Q1756703'),
(34115, 'Espera', 5096, 'CA', 207, 'ES', 36.87446000, -5.80600000, '2019-10-05 22:45:44', '2022-08-28 18:44:29', 1, 'Q1448444'),
(34116, 'Espiel', 5097, 'CO', 207, 'ES', 38.20000000, -5.01667000, '2019-10-05 22:45:44', '2022-08-28 18:49:38', 1, 'Q1447434'),
(34117, 'Espino de la Orbada', 1147, 'SA', 207, 'ES', 41.10693000, -5.42515000, '2019-10-05 22:45:44', '2022-08-29 11:44:51', 1, 'Q1447434'),
(34118, 'Espinosa de Cerrato', 1157, 'P', 207, 'ES', 41.96679000, -3.95346000, '2019-10-05 22:45:44', '2022-08-29 11:45:44', 1, 'Q1929219'),
(34119, 'Espinosa de Cervera', 1146, 'BU', 207, 'ES', 41.89683000, -3.46858000, '2019-10-05 22:45:44', '2022-08-29 11:24:20', 1, 'Q1650262'),
(34120, 'Espinosa de Henares', 5107, 'GU', 207, 'ES', 40.90053000, -3.06956000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q24011209'),
(34121, 'Espinosa de Villagonzalo', 1157, 'P', 207, 'ES', 42.47949000, -4.37218000, '2019-10-05 22:45:44', '2022-08-29 11:45:44', 1, 'Q1923896'),
(34122, 'Espinosa de los Monteros', 1146, 'BU', 207, 'ES', 43.07754000, -3.55365000, '2019-10-05 22:45:44', '2022-08-29 11:24:20', 1, 'Q24010525'),
(34123, 'Espinosa del Camino', 1146, 'BU', 207, 'ES', 42.40590000, -3.28019000, '2019-10-05 22:45:44', '2022-08-29 11:24:20', 1, 'Q946759'),
(34124, 'Espinoso del Rey', 1205, 'TO', 207, 'ES', 39.65333000, -4.78371000, '2019-10-05 22:45:44', '2022-08-29 11:08:29', 1, 'Q1656589'),
(34125, 'Espirdo', 1192, 'SG', 207, 'ES', 40.99764000, -4.07331000, '2019-10-05 22:45:44', '2022-08-29 11:50:42', 1, 'Q1939048'),
(34126, 'Esplegares', 5107, 'GU', 207, 'ES', 40.85650000, -2.37084000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q1654342'),
(34127, 'Esplugues de Llobregat', 5102, 'B', 207, 'ES', 41.37732000, 2.08809000, '2019-10-05 22:45:44', '2022-08-29 10:50:00', 1, 'Q1654342'),
(34128, 'Esplús', 1177, 'HU', 207, 'ES', 41.79870000, 0.27586000, '2019-10-05 22:45:44', '2022-08-29 12:06:20', 1, 'Q984480'),
(34129, 'Espolla', 5103, 'GI', 207, 'ES', 42.39120000, 3.00064000, '2019-10-05 22:45:44', '2022-08-29 10:53:16', 1, 'Q7079'),
(34130, 'Esponellà', 5103, 'GI', 207, 'ES', 42.16667000, 2.80000000, '2019-10-05 22:45:44', '2022-08-29 10:53:16', 1, 'Q13611'),
(34131, 'Esporles', 1174, 'PM', 207, 'ES', 39.66794000, 2.57867000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q847321'),
(34132, 'Espot', 5104, 'L', 207, 'ES', 42.57838000, 1.08666000, '2019-10-05 22:45:44', '2022-08-29 10:55:25', 1, 'Q847321'),
(34133, 'Espronceda', 1204, 'NA', 207, 'ES', 42.59707000, -2.30524000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q1922560'),
(34134, 'Esquivias', 1205, 'TO', 207, 'ES', 40.10440000, -3.76677000, '2019-10-05 22:45:44', '2022-08-29 11:08:29', 1, 'Q1630025'),
(34135, 'Establés', 5107, 'GU', 207, 'ES', 41.00767000, -2.02438000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q1654761'),
(34137, 'Estada', 1177, 'HU', 207, 'ES', 42.07201000, 0.23219000, '2019-10-05 22:45:44', '2022-08-29 12:06:20', 1, 'Q1651360'),
(34138, 'Estadilla', 1177, 'HU', 207, 'ES', 42.05657000, 0.24343000, '2019-10-05 22:45:44', '2022-08-29 12:06:20', 1, 'Q934991'),
(34139, 'Estella-Lizarra', 1204, 'NA', 207, 'ES', 42.67182000, -2.03226000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q934991'),
(34140, 'Estellencs', 1174, 'PM', 207, 'ES', 39.65338000, 2.48130000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q934991'),
(34141, 'Estepa', 1193, 'SE', 207, 'ES', 37.29263000, -4.87896000, '2019-10-05 22:45:44', '2022-08-28 19:08:49', 1, 'Q966470'),
(34142, 'Estepa de San Juan', 1208, 'SO', 207, 'ES', 41.92661000, -2.33322000, '2019-10-05 22:45:44', '2022-08-29 11:51:23', 1, 'Q831491'),
(34143, 'Estepona', 5101, 'MA', 207, 'ES', 36.42764000, -5.14589000, '2019-10-05 22:45:44', '2022-08-28 19:06:53', 1, 'Q492748'),
(34144, 'Estercuel', 5111, 'TE', 207, 'ES', 40.85500000, -0.63208000, '2019-10-05 22:45:44', '2022-08-29 11:29:44', 1, 'Q1651625'),
(34145, 'Estivella', 1175, 'V', 207, 'ES', 39.71667000, -0.35000000, '2019-10-05 22:45:44', '2022-08-29 12:05:40', 1, 'Q1989401'),
(34146, 'Estollo', 1171, 'LO', 207, 'ES', 42.32953000, -2.85092000, '2019-10-05 22:45:44', '2022-08-29 12:05:09', 1, 'Q1646938'),
(34147, 'Estremera', 1158, 'M', 207, 'ES', 40.18333000, -3.10000000, '2019-10-05 22:45:44', '2022-08-29 12:04:40', 1, 'Q1615876'),
(34148, 'Estriégana', 5107, 'GU', 207, 'ES', 41.05821000, -2.52363000, '2019-10-05 22:45:44', '2022-08-29 11:06:45', 1, 'Q1653790'),
(34149, 'Estubeny', 1175, 'V', 207, 'ES', 39.01792000, -0.62379000, '2019-10-05 22:45:44', '2022-08-29 12:05:40', 1, 'Q1998575'),
(34150, 'Estépar', 1146, 'BU', 207, 'ES', 42.27731000, -3.89946000, '2019-10-05 22:45:44', '2022-08-29 11:24:20', 1, 'Q24010523'),
(34151, 'Etayo', 1204, 'NA', 207, 'ES', 42.61695000, -2.15447000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q1648475'),
(34152, 'Eulate', 1204, 'NA', 207, 'ES', 42.77641000, -2.20638000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q1641873'),
(34153, 'Ezcaray', 1171, 'LO', 207, 'ES', 42.32536000, -3.01309000, '2019-10-05 22:45:44', '2022-08-29 12:05:09', 1, 'Q1637309'),
(34154, 'Fabara', 5113, 'Z', 207, 'ES', 41.17750000, 0.16908000, '2019-10-05 22:45:44', '2022-08-29 11:42:54', 1, 'Q956450'),
(34155, 'Fabero', 1200, 'LE', 207, 'ES', 42.76803000, -6.62651000, '2019-10-05 22:45:44', '2019-10-05 22:45:44', 1, 'Q956450'),
(34156, 'Facheca', 5108, 'A', 207, 'ES', 38.73501000, -0.26766000, '2019-10-05 22:45:44', '2022-08-29 11:15:47', 1, 'Q426123'),
(34157, 'Fago', 1177, 'HU', 207, 'ES', 42.73440000, -0.88131000, '2019-10-05 22:45:44', '2022-08-29 12:06:20', 1, 'Q987441'),
(34158, 'Falces', 1204, 'NA', 207, 'ES', 42.38966000, -1.79321000, '2019-10-05 22:45:44', '2022-08-29 12:06:07', 1, 'Q1641884'),
(34159, 'Falset', 1203, 'T', 207, 'ES', 41.14576000, 0.81979000, '2019-10-05 22:45:44', '2022-08-29 10:57:32', 1, 'Q1641884'),
(34160, 'Famorca', 5108, 'A', 207, 'ES', 38.73101000, -0.24726000, '2019-10-05 22:45:44', '2022-08-29 11:15:47', 1, 'Q1751994'),
(34161, 'Fanzara', 5110, 'CS', 207, 'ES', 40.01667000, -0.31667000, '2019-10-05 22:45:44', '2022-08-29 11:26:42', 1, 'Q1645852'),
(34162, 'Faraján', 5101, 'MA', 207, 'ES', 36.61695000, -5.18839000, '2019-10-05 22:45:44', '2022-08-28 19:06:53', 1, 'Q1645852'),
(34163, 'Faramontanos de Tábara', 1161, 'ZA', 207, 'ES', 41.83469000, -5.88883000, '2019-10-05 22:45:44', '2022-08-29 11:48:03', 1, 'Q734801'),
(34164, 'Fariza', 1161, 'ZA', 207, 'ES', 41.41667000, -6.26667000, '2019-10-05 22:45:44', '2022-08-29 11:48:03', 1, 'Q1922223'),
(34165, 'Farlete', 5113, 'Z', 207, 'ES', 41.68122000, -0.50678000, '2019-10-05 22:45:44', '2022-08-29 11:42:54', 1, 'Q930157'),
(34166, 'Farrera', 5104, 'L', 207, 'ES', 42.49654000, 1.27216000, '2019-10-05 22:45:44', '2022-08-29 10:55:25', 1, 'Q12676'),
(34167, 'Fasnia', 5112, 'TF', 207, 'ES', 28.23638000, -16.43886000, '2019-10-05 22:45:44', '2022-08-29 11:31:13', 1, 'Q524431'),
(34168, 'Faura', 1175, 'V', 207, 'ES', 39.71667000, -0.25000000, '2019-10-05 22:45:44', '2022-08-29 12:05:40', 1, 'Q23985846'),
(34169, 'Favara', 1175, 'V', 207, 'ES', 39.11667000, -0.28333000, '2019-10-05 22:45:45', '2022-08-29 12:05:40', 1, 'Q23985846'),
(34170, 'Fayón', 5113, 'Z', 207, 'ES', 41.23851000, 0.33302000, '2019-10-05 22:45:45', '2022-08-29 11:42:54', 1, 'Q1639270'),
(34171, 'Felanitx', 1174, 'PM', 207, 'ES', 39.46960000, 3.14831000, '2019-10-05 22:45:45', '2019-10-05 22:45:45', 1, 'Q1639270'),
(34172, 'Fene', 5089, 'C', 207, 'ES', 43.45000000, -8.15000000, '2019-10-05 22:45:45', '2022-08-28 13:37:17', 1, 'Q1639270'),
(34173, 'Feria', 5092, 'BA', 207, 'ES', 38.51151000, -6.56416000, '2019-10-05 22:45:45', '2022-08-28 18:09:23', 1, 'Q1447714'),
(34174, 'Fermoselle', 1161, 'ZA', 207, 'ES', 41.31738000, -6.39584000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q744993'),
(34175, 'Fernán-Núñez', 5097, 'CO', 207, 'ES', 37.67044000, -4.72640000, '2019-10-05 22:45:45', '2022-08-28 18:49:38', 1, 'Q1617311'),
(34176, 'Ferreira', 5098, 'GR', 207, 'ES', 37.17247000, -3.03539000, '2019-10-05 22:45:45', '2022-08-28 18:52:57', 1, 'Q574333'),
(34177, 'Ferreras de Abajo', 1161, 'ZA', 207, 'ES', 41.89651000, -6.07904000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q956480'),
(34178, 'Ferreras de Arriba', 1161, 'ZA', 207, 'ES', 41.89878000, -6.19461000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1752387'),
(34179, 'Ferreries', 1174, 'PM', 207, 'ES', 39.98324000, 4.01181000, '2019-10-05 22:45:45', '2019-10-05 22:45:45', 1, 'Q1752387'),
(34180, 'Ferreruela', 1161, 'ZA', 207, 'ES', 41.76673000, -6.07215000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1752387'),
(34181, 'Ferreruela de Huerva', 5111, 'TE', 207, 'ES', 41.06325000, -1.23350000, '2019-10-05 22:45:45', '2022-08-29 11:29:44', 1, 'Q1768510'),
(34182, 'Ferrol', 5089, 'C', 207, 'ES', 43.48961000, -8.21940000, '2019-10-05 22:45:45', '2022-08-28 13:37:17', 1, 'Q485329'),
(34184, 'Figueres', 5103, 'GI', 207, 'ES', 42.26645000, 2.96163000, '2019-10-05 22:45:45', '2022-08-29 10:53:16', 1, 'Q485329'),
(34185, 'Figueroles', 5110, 'CS', 207, 'ES', 40.11667000, -0.23333000, '2019-10-05 22:45:45', '2022-08-29 11:26:42', 1, 'Q1646916'),
(34186, 'Figueruela de Arriba', 1161, 'ZA', 207, 'ES', 41.86867000, -6.44454000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1646916'),
(34187, 'Figueruelas', 5113, 'Z', 207, 'ES', 41.76651000, -1.17473000, '2019-10-05 22:45:45', '2022-08-29 11:42:54', 1, 'Q1650603'),
(34188, 'Fines', 5095, 'AL', 207, 'ES', 37.36074000, -2.25810000, '2019-10-05 22:45:45', '2022-08-28 18:41:41', 1, 'Q1650603'),
(34189, 'Finestrat', 5108, 'A', 207, 'ES', 38.56737000, -0.21235000, '2019-10-05 22:45:45', '2022-08-29 11:15:47', 1, 'Q1646107'),
(34190, 'Firgas', 1185, 'GC', 207, 'ES', 28.10711000, -15.56299000, '2019-10-05 22:45:45', '2022-08-29 12:06:32', 1, 'Q1646107'),
(34191, 'Fiscal', 1177, 'HU', 207, 'ES', 42.49561000, -0.12094000, '2019-10-05 22:45:45', '2022-08-29 12:06:20', 1, 'Q598606'),
(34192, 'Fisterra', 5089, 'C', 207, 'ES', 42.90492000, -9.26289000, '2019-10-05 22:45:45', '2022-08-28 13:37:17', 1, 'Q598606'),
(34193, 'Fitero', 1204, 'NA', 207, 'ES', 42.05770000, -1.85756000, '2019-10-05 22:45:45', '2022-08-29 12:06:07', 1, 'Q1773030'),
(34194, 'Fiñana', 5095, 'AL', 207, 'ES', 37.17150000, -2.84011000, '2019-10-05 22:45:45', '2022-08-28 18:41:41', 1, 'Q1157964'),
(34195, 'Flix', 1203, 'T', 207, 'ES', 41.23074000, 0.55008000, '2019-10-05 22:45:45', '2022-08-29 10:57:32', 1, 'Q1157964'),
(34196, 'Flores de Ávila', 1189, 'AV', 207, 'ES', 40.93358000, -5.07914000, '2019-10-05 22:45:45', '2022-08-29 11:49:56', 1, 'Q1618199'),
(34197, 'Florida de Liébana', 1147, 'SA', 207, 'ES', 41.02361000, -5.76252000, '2019-10-05 22:45:45', '2022-08-29 11:44:51', 1, 'Q1768634'),
(34198, 'Foios', 1175, 'V', 207, 'ES', 39.53333000, -0.35000000, '2019-10-05 22:45:45', '2022-08-29 12:05:40', 1, 'Q1768634'),
(34199, 'Foixà', 5103, 'GI', 207, 'ES', 42.03626000, 3.00021000, '2019-10-05 22:45:45', '2022-08-29 10:53:16', 1, 'Q1768634'),
(34200, 'Folgoso de la Ribera', 1200, 'LE', 207, 'ES', 42.64693000, -6.32035000, '2019-10-05 22:45:45', '2019-10-05 22:45:45', 1, 'Q1768634'),
(34201, 'Fombellida', 1183, 'VA', 207, 'ES', 41.76693000, -4.18409000, '2019-10-05 22:45:45', '2022-08-29 11:48:45', 1, 'Q1922913'),
(34202, 'Fombuena', 5113, 'Z', 207, 'ES', 41.14442000, -1.19266000, '2019-10-05 22:45:45', '2022-08-29 11:42:54', 1, 'Q672176'),
(34203, 'Fompedraza', 1183, 'VA', 207, 'ES', 41.53541000, -4.14483000, '2019-10-05 22:45:45', '2022-08-29 11:48:45', 1, 'Q1767943'),
(34204, 'Foncea', 1171, 'LO', 207, 'ES', 42.61529000, -3.03897000, '2019-10-05 22:45:45', '2022-08-29 12:05:09', 1, 'Q652162'),
(34205, 'Fondón', 5095, 'AL', 207, 'ES', 36.98020000, -2.85957000, '2019-10-05 22:45:45', '2022-08-28 18:41:41', 1, 'Q23985864'),
(34206, 'Fonelas', 5098, 'GR', 207, 'ES', 37.41042000, -3.17158000, '2019-10-05 22:45:45', '2022-08-28 18:52:57', 1, 'Q574337'),
(34207, 'Fonfría', 1161, 'ZA', 207, 'ES', 40.99566000, -1.08475000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q574337'),
(34209, 'Fonollosa', 5102, 'B', 207, 'ES', 41.76303000, 1.66867000, '2019-10-05 22:45:45', '2022-08-29 10:50:00', 1, 'Q16695'),
(34211, 'Fontanar', 5107, 'GU', 207, 'ES', 40.72466000, -3.17309000, '2019-10-05 22:45:45', '2022-08-29 11:06:45', 1, 'Q1465213'),
(34212, 'Fontanarejo', 5105, 'CR', 207, 'ES', 39.22014000, -4.51753000, '2019-10-05 22:45:45', '2022-08-29 11:03:25', 1, 'Q1658056'),
(34213, 'Fontanars dels Alforins', 1175, 'V', 207, 'ES', 38.78423000, -0.78667000, '2019-10-05 22:45:45', '2022-08-29 12:05:40', 1, 'Q1658056'),
(34214, 'Fontellas', 1204, 'NA', 207, 'ES', 42.02694000, -1.57648000, '2019-10-05 22:45:45', '2022-08-29 12:06:07', 1, 'Q1768078'),
(34215, 'Fontihoyuelo', 1183, 'VA', 207, 'ES', 42.16192000, -5.05791000, '2019-10-05 22:45:45', '2022-08-29 11:48:45', 1, 'Q1657083'),
(34216, 'Fontioso', 1146, 'BU', 207, 'ES', 41.94273000, -3.73872000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1633244'),
(34217, 'Fontiveros', 1189, 'AV', 207, 'ES', 40.92997000, -4.96445000, '2019-10-05 22:45:45', '2022-08-29 11:49:56', 1, 'Q1442826'),
(34218, 'Fonz', 1177, 'HU', 207, 'ES', 42.01015000, 0.25878000, '2019-10-05 22:45:45', '2022-08-29 12:06:20', 1, 'Q1993784'),
(34219, 'Fonzaleche', 1171, 'LO', 207, 'ES', 42.58114000, -3.01218000, '2019-10-05 22:45:45', '2022-08-29 12:05:09', 1, 'Q1637372'),
(34220, 'Forcall', 5110, 'CS', 207, 'ES', 40.64542000, -0.19992000, '2019-10-05 22:45:45', '2022-08-29 11:26:42', 1, 'Q780945'),
(34221, 'Forfoleda', 1147, 'SA', 207, 'ES', 41.09779000, -5.74979000, '2019-10-05 22:45:45', '2022-08-29 11:44:51', 1, 'Q24014123'),
(34222, 'Formentera de Segura', 1175, 'V', 207, 'ES', 38.08509000, -0.74604000, '2019-10-05 22:45:45', '2022-08-29 12:05:40', 1, 'Q23985875'),
(34223, 'Formiche Alto', 5111, 'TE', 207, 'ES', 40.32367000, -0.89241000, '2019-10-05 22:45:45', '2022-08-29 11:29:44', 1, 'Q1657098'),
(34224, 'Fornalutx', 1174, 'PM', 207, 'ES', 39.78232000, 2.74107000, '2019-10-05 22:45:45', '2019-10-05 22:45:45', 1, 'Q950901'),
(34225, 'Fornells de la Selva', 5103, 'GI', 207, 'ES', 41.93159000, 2.80907000, '2019-10-05 22:45:45', '2022-08-29 10:53:16', 1, 'Q24022116'),
(34226, 'Fornelos de Montes', 1167, 'PO', 207, 'ES', 42.34126000, -8.45291000, '2019-10-05 22:45:45', '2022-08-28 17:57:54', 1, 'Q525124'),
(34228, 'Fortaleny', 1175, 'V', 207, 'ES', 39.18333000, -0.30000000, '2019-10-05 22:45:45', '2022-08-29 12:05:40', 1, 'Q525124'),
(34229, 'Fortanete', 5111, 'TE', 207, 'ES', 40.50533000, -0.52283000, '2019-10-05 22:45:45', '2022-08-29 11:29:44', 1, 'Q1768071'),
(34230, 'Fortià', 5103, 'GI', 207, 'ES', 42.24320000, 3.03881000, '2019-10-05 22:45:45', '2022-08-29 10:53:16', 1, 'Q1768071'),
(34231, 'Fortuna', 1176, 'MU', 207, 'ES', 38.18140000, -1.12590000, '2019-10-05 22:45:45', '2022-08-29 12:05:49', 1, 'Q1768071'),
(34232, 'Forua', 5094, 'BI', 207, 'ES', 43.33343000, -2.67504000, '2019-10-05 22:45:45', '2022-08-28 18:25:56', 1, 'Q679513'),
(34233, 'Forès', 1203, 'T', 207, 'ES', 41.48333000, 1.23333000, '2019-10-05 22:45:45', '2022-08-29 10:57:32', 1, 'Q1230137'),
(34234, 'Foz', 5090, 'LU', 207, 'ES', 43.56920000, -7.25441000, '2019-10-05 22:45:45', '2022-08-28 17:49:36', 1, 'Q1230137'),
(34235, 'Foz-Calanda', 5111, 'TE', 207, 'ES', 40.92208000, -0.26483000, '2019-10-05 22:45:45', '2022-08-29 11:29:44', 1, 'Q1768500'),
(34236, 'Frades de la Sierra', 1147, 'SA', 207, 'ES', 40.65687000, -5.78223000, '2019-10-05 22:45:45', '2022-08-29 11:44:51', 1, 'Q24014122'),
(34237, 'Fraga', 1177, 'HU', 207, 'ES', 41.52294000, 0.34894000, '2019-10-05 22:45:45', '2022-08-29 12:06:20', 1, 'Q904212'),
(34238, 'Frailes', 5100, 'J', 207, 'ES', 37.48617000, -3.83743000, '2019-10-05 22:45:45', '2022-08-28 19:04:30', 1, 'Q2047553'),
(34239, 'Frandovínez', 1146, 'BU', 207, 'ES', 42.31032000, -3.83783000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1751523'),
(34240, 'Frechilla', 1157, 'P', 207, 'ES', 42.13768000, -4.84112000, '2019-10-05 22:45:45', '2022-08-29 11:45:44', 1, 'Q24001466'),
(34241, 'Frechilla de Almazán', 1208, 'SO', 207, 'ES', 41.42656000, -2.51444000, '2019-10-05 22:45:45', '2022-08-29 11:51:23', 1, 'Q62100135'),
(34242, 'Fregenal de la Sierra', 5092, 'BA', 207, 'ES', 38.16922000, -6.65370000, '2019-10-05 22:45:45', '2022-08-28 18:09:23', 1, 'Q1613587'),
(34243, 'Freila', 5098, 'GR', 207, 'ES', 37.52990000, -2.90828000, '2019-10-05 22:45:45', '2022-08-28 18:52:57', 1, 'Q574350'),
(34244, 'Fresneda de Altarejos', 5106, 'CU', 207, 'ES', 39.92577000, -2.31498000, '2019-10-05 22:45:45', '2022-08-29 11:05:01', 1, 'Q1903504'),
(34245, 'Fresneda de Cuéllar', 1192, 'SG', 207, 'ES', 41.31905000, -4.44938000, '2019-10-05 22:45:45', '2022-08-29 11:50:42', 1, 'Q1939012'),
(34246, 'Fresneda de la Sierra', 5106, 'CU', 207, 'ES', 40.39101000, -2.14233000, '2019-10-05 22:45:45', '2022-08-29 11:05:01', 1, 'Q932674'),
(34247, 'Fresneda de la Sierra Tirón', 1146, 'BU', 207, 'ES', 42.31535000, -3.13596000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q541178'),
(34248, 'Fresnedilla', 1189, 'AV', 207, 'ES', 40.23233000, -4.62201000, '2019-10-05 22:45:45', '2022-08-29 11:49:56', 1, 'Q1632633'),
(34249, 'Fresnedillas', 1158, 'M', 207, 'ES', 40.48704000, -4.17146000, '2019-10-05 22:45:45', '2022-08-29 12:04:40', 1, 'Q1632633'),
(34250, 'Fresnedoso', 1147, 'SA', 207, 'ES', 40.43619000, -5.70969000, '2019-10-05 22:45:45', '2022-08-29 11:44:51', 1, 'Q1768614'),
(34251, 'Fresnedoso de Ibor', 1190, 'CC', 207, 'ES', 39.68418000, -5.50899000, '2019-10-05 22:45:45', '2022-08-28 18:12:22', 1, 'Q1643018'),
(34252, 'Fresneña', 1146, 'BU', 207, 'ES', 42.41366000, -3.13453000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1765200'),
(34253, 'Fresnillo de las Dueñas', 1146, 'BU', 207, 'ES', 41.64700000, -3.64558000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1643897'),
(34254, 'Fresno El Viejo', 1183, 'VA', 207, 'ES', 41.19752000, -5.14413000, '2019-10-05 22:45:45', '2022-08-29 11:48:45', 1, 'Q735777'),
(34255, 'Fresno de Cantespino', 1192, 'SG', 207, 'ES', 41.36820000, -3.49950000, '2019-10-05 22:45:45', '2022-08-29 11:50:42', 1, 'Q1939147'),
(34256, 'Fresno de Caracena', 1208, 'SO', 207, 'ES', 41.45247000, -3.09123000, '2019-10-05 22:45:45', '2022-08-29 11:51:23', 1, 'Q832840'),
(34257, 'Fresno de Rodilla', 1146, 'BU', 207, 'ES', 42.42054000, -3.48507000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1643560'),
(34258, 'Fresno de Sayago', 1161, 'ZA', 207, 'ES', 41.31848000, -5.97129000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1769013'),
(34259, 'Fresno de Torote', 1158, 'M', 207, 'ES', 40.59040000, -3.41003000, '2019-10-05 22:45:45', '2022-08-29 12:04:40', 1, 'Q1772191'),
(34260, 'Fresno de la Fuente', 1192, 'SG', 207, 'ES', 41.39243000, -3.64491000, '2019-10-05 22:45:45', '2022-08-29 11:50:42', 1, 'Q1917560'),
(34261, 'Fresno de la Polvorosa', 1161, 'ZA', 207, 'ES', 42.08310000, -5.76969000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1917560'),
(34262, 'Fresno de la Ribera', 1161, 'ZA', 207, 'ES', 41.52926000, -5.56658000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1652227'),
(34263, 'Fresno de la Vega', 1200, 'LE', 207, 'ES', 42.34545000, -5.53587000, '2019-10-05 22:45:45', '2019-10-05 22:45:45', 1, 'Q26445'),
(34264, 'Fresno del Río', 1157, 'P', 207, 'ES', 42.68176000, -4.81734000, '2019-10-05 22:45:45', '2022-08-29 11:45:44', 1, 'Q950285'),
(34265, 'Friera de Valverde', 1161, 'ZA', 207, 'ES', 41.91214000, -5.84153000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1752425'),
(34266, 'Frigiliana', 5101, 'MA', 207, 'ES', 36.78747000, -3.89441000, '2019-10-05 22:45:45', '2022-08-28 19:06:53', 1, 'Q944137'),
(34267, 'Friol', 5090, 'LU', 207, 'ES', 43.03213000, -7.79514000, '2019-10-05 22:45:45', '2022-08-28 17:49:36', 1, 'Q576916'),
(34268, 'Frontera', 5112, 'TF', 207, 'ES', 27.75404000, -18.00367000, '2019-10-05 22:45:45', '2022-08-29 11:31:13', 1, 'Q1798827'),
(34269, 'Frumales', 1192, 'SG', 207, 'ES', 41.38333000, -4.18631000, '2019-10-05 22:45:45', '2022-08-29 11:50:42', 1, 'Q601460'),
(34270, 'Fréscano', 5113, 'Z', 207, 'ES', 41.88333000, -1.45000000, '2019-10-05 22:45:45', '2022-08-29 11:42:54', 1, 'Q1639221'),
(34271, 'Frías', 1146, 'BU', 207, 'ES', 42.76225000, -3.29394000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1639221'),
(34272, 'Frías de Albarracín', 5111, 'TE', 207, 'ES', 40.33754000, -1.61470000, '2019-10-05 22:45:45', '2022-08-29 11:29:44', 1, 'Q1653290'),
(34273, 'Frómista', 1157, 'P', 207, 'ES', 42.26667000, -4.40546000, '2019-10-05 22:45:45', '2022-08-29 11:45:44', 1, 'Q988834'),
(34274, 'Fuembellida', 5107, 'GU', 207, 'ES', 40.75589000, -1.99861000, '2019-10-05 22:45:45', '2022-08-29 11:06:45', 1, 'Q1768248'),
(34275, 'Fuencaliente', 5105, 'CR', 207, 'ES', 39.18624000, -4.02576000, '2019-10-05 22:45:45', '2022-08-29 11:03:25', 1, 'Q23985942'),
(34276, 'Fuencaliente de la Palma', 5112, 'TF', 207, 'ES', 28.49236000, -17.84529000, '2019-10-05 22:45:45', '2022-08-29 11:31:13', 1, 'Q434019'),
(34277, 'Fuencarral-El Pardo', 1158, 'M', 207, 'ES', 40.49840000, -3.73140000, '2019-10-05 22:45:45', '2022-08-29 12:04:40', 1, 'Q656196'),
(34278, 'Fuencemillán', 5107, 'GU', 207, 'ES', 40.92063000, -3.09818000, '2019-10-05 22:45:45', '2022-08-29 11:06:45', 1, 'Q1654710'),
(34279, 'Fuendejalón', 5113, 'Z', 207, 'ES', 41.76050000, -1.47213000, '2019-10-05 22:45:45', '2022-08-29 11:42:54', 1, 'Q541606'),
(34280, 'Fuendetodos', 5113, 'Z', 207, 'ES', 41.34245000, -0.95988000, '2019-10-05 22:45:45', '2022-08-29 11:42:54', 1, 'Q1372769'),
(34281, 'Fuenferrada', 5111, 'TE', 207, 'ES', 40.86923000, -1.01179000, '2019-10-05 22:45:45', '2022-08-29 11:29:44', 1, 'Q1653283'),
(34282, 'Fuengirola', 5101, 'MA', 207, 'ES', 36.53998000, -4.62473000, '2019-10-05 22:45:45', '2022-08-28 19:06:53', 1, 'Q492735'),
(34283, 'Fuenlabrada', 1158, 'M', 207, 'ES', 40.28419000, -3.79415000, '2019-10-05 22:45:45', '2022-08-29 12:04:40', 1, 'Q54902'),
(34284, 'Fuenlabrada de los Montes', 5092, 'BA', 207, 'ES', 39.13288000, -4.93513000, '2019-10-05 22:45:45', '2022-08-28 18:09:23', 1, 'Q54902'),
(34285, 'Fuenllana', 5105, 'CR', 207, 'ES', 38.75590000, -2.95814000, '2019-10-05 22:45:45', '2022-08-29 11:03:25', 1, 'Q1642185'),
(34286, 'Fuenmayor', 1171, 'LO', 207, 'ES', 42.46729000, -2.56153000, '2019-10-05 22:45:45', '2022-08-29 12:05:09', 1, 'Q755695'),
(34287, 'Fuensaldaña', 1183, 'VA', 207, 'ES', 41.70617000, -4.76547000, '2019-10-05 22:45:45', '2022-08-29 11:48:45', 1, 'Q1651705'),
(34288, 'Fuensalida', 1205, 'TO', 207, 'ES', 40.05288000, -4.20718000, '2019-10-05 22:45:45', '2022-08-29 11:08:29', 1, 'Q1630082'),
(34290, 'Fuensanta de Martos', 5100, 'J', 207, 'ES', 37.64756000, -3.90846000, '2019-10-05 22:45:45', '2022-08-28 19:04:30', 1, 'Q1751769'),
(34291, 'Fuente Encalada', 1161, 'ZA', 207, 'ES', 42.11011000, -5.99622000, '2019-10-05 22:45:45', '2022-08-29 11:48:03', 1, 'Q1751769'),
(34292, 'Fuente Obejuna', 5097, 'CO', 207, 'ES', 38.26667000, -5.41667000, '2019-10-05 22:45:45', '2022-08-28 18:49:38', 1, 'Q1443636'),
(34293, 'Fuente Palmera', 5097, 'CO', 207, 'ES', 37.70494000, -5.09965000, '2019-10-05 22:45:45', '2022-08-28 18:49:38', 1, 'Q1443622'),
(34294, 'Fuente Vaqueros', 5098, 'GR', 207, 'ES', 37.22019000, -3.78294000, '2019-10-05 22:45:45', '2022-08-28 18:52:57', 1, 'Q574513'),
(34295, 'Fuente de Cantos', 5092, 'BA', 207, 'ES', 38.25000000, -6.30000000, '2019-10-05 22:45:45', '2022-08-28 18:09:23', 1, 'Q574513'),
(34296, 'Fuente de Pedro Naharro', 5106, 'CU', 207, 'ES', 39.92438000, -3.00916000, '2019-10-05 22:45:45', '2022-08-29 11:05:01', 1, 'Q1903594'),
(34297, 'Fuente de Piedra', 5101, 'MA', 207, 'ES', 37.13526000, -4.73000000, '2019-10-05 22:45:45', '2022-08-28 19:06:53', 1, 'Q922378'),
(34298, 'Fuente de Santa Cruz', 1192, 'SG', 207, 'ES', 41.20972000, -4.63530000, '2019-10-05 22:45:45', '2022-08-29 11:50:42', 1, 'Q24014529'),
(34299, 'Fuente del Maestre', 5092, 'BA', 207, 'ES', 38.52656000, -6.44782000, '2019-10-05 22:45:45', '2022-08-28 18:09:23', 1, 'Q1407463'),
(34300, 'Fuente el Fresno', 5105, 'CR', 207, 'ES', 39.22839000, -3.77487000, '2019-10-05 22:45:45', '2022-08-29 11:03:25', 1, 'Q1642113'),
(34301, 'Fuente el Olmo de Fuentidueña', 1192, 'SG', 207, 'ES', 41.37929000, -4.00086000, '2019-10-05 22:45:45', '2022-08-29 11:50:42', 1, 'Q1948582'),
(34302, 'Fuente el Saz', 1158, 'M', 207, 'ES', 40.63215000, -3.51146000, '2019-10-05 22:45:45', '2022-08-29 12:04:40', 1, 'Q1948582'),
(34303, 'Fuente el Sol', 1183, 'VA', 207, 'ES', 41.17603000, -4.93430000, '2019-10-05 22:45:45', '2022-08-29 11:48:45', 1, 'Q1907228'),
(34304, 'Fuente la Lancha', 5097, 'CO', 207, 'ES', 38.41667000, -5.03333000, '2019-10-05 22:45:45', '2022-08-28 18:49:38', 1, 'Q1907228'),
(34305, 'Fuente la Reina', 5110, 'CS', 207, 'ES', 40.06667000, -0.60000000, '2019-10-05 22:45:45', '2022-08-29 11:26:42', 1, 'Q745282'),
(34306, 'Fuente-Tójar', 5097, 'CO', 207, 'ES', 37.51095000, -4.14631000, '2019-10-05 22:45:45', '2022-08-28 18:49:38', 1, 'Q339790'),
(34308, 'Fuente-Álamo de Murcia', 1176, 'MU', 207, 'ES', 37.72389000, -1.16972000, '2019-10-05 22:45:45', '2022-08-29 12:05:49', 1, 'Q23985956'),
(34310, 'Fuentearmegil', 1208, 'SO', 207, 'ES', 41.71496000, -3.18362000, '2019-10-05 22:45:45', '2022-08-29 11:51:23', 1, 'Q832389'),
(34311, 'Fuentebureba', 1146, 'BU', 207, 'ES', 42.63400000, -3.23510000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1643709'),
(34312, 'Fuentecambrón', 1208, 'SO', 207, 'ES', 41.50579000, -3.32872000, '2019-10-05 22:45:45', '2022-08-29 11:51:23', 1, 'Q832009'),
(34313, 'Fuentecantos', 1208, 'SO', 207, 'ES', 41.84929000, -2.42846000, '2019-10-05 22:45:45', '2022-08-29 11:51:23', 1, 'Q832914'),
(34314, 'Fuentecén', 1146, 'BU', 207, 'ES', 41.62852000, -3.86867000, '2019-10-05 22:45:45', '2022-08-29 11:24:20', 1, 'Q1643883'),
(34315, 'Fuenteguinaldo', 1147, 'SA', 207, 'ES', 40.42876000, -6.67528000, '2019-10-05 22:45:45', '2022-08-29 11:44:51', 1, 'Q1640604'),
(34316, 'Fuenteheridos', 5099, 'H', 207, 'ES', 37.90545000, -6.66108000, '2019-10-05 22:45:45', '2022-08-28 19:00:43', 1, 'Q1632851'),
(34317, 'Fuentelahiguera de Albatages', 5107, 'GU', 207, 'ES', 40.78312000, -3.30492000, '2019-10-05 22:45:46', '2022-08-29 11:06:45', 1, 'Q1654767'),
(34318, 'Fuentelapeña', 1161, 'ZA', 207, 'ES', 41.25144000, -5.38325000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q1769064'),
(34319, 'Fuentelcésped', 1146, 'BU', 207, 'ES', 41.59162000, -3.64064000, '2019-10-05 22:45:46', '2022-08-29 11:24:20', 1, 'Q1631194'),
(34320, 'Fuentelencina', 5107, 'GU', 207, 'ES', 40.51781000, -2.88226000, '2019-10-05 22:45:46', '2022-08-29 11:06:45', 1, 'Q1645691'),
(34321, 'Fuentelespino de Haro', 5106, 'CU', 207, 'ES', 39.68934000, -2.66869000, '2019-10-05 22:45:46', '2022-08-29 11:05:01', 1, 'Q1920039'),
(34322, 'Fuentelespino de Moya', 5106, 'CU', 207, 'ES', 39.91667000, -1.46667000, '2019-10-05 22:45:46', '2022-08-29 11:05:01', 1, 'Q1916233'),
(34323, 'Fuentelisendo', 1146, 'BU', 207, 'ES', 41.62274000, -3.90129000, '2019-10-05 22:45:46', '2022-08-29 11:24:20', 1, 'Q1614765'),
(34324, 'Fuentelsaz', 5107, 'GU', 207, 'ES', 41.07359000, -1.83108000, '2019-10-05 22:45:46', '2022-08-29 11:06:45', 1, 'Q24011201'),
(34325, 'Fuentelsaz de Soria', 1208, 'SO', 207, 'ES', 41.86586000, -2.41525000, '2019-10-05 22:45:46', '2022-08-29 11:51:23', 1, 'Q833604'),
(34326, 'Fuentelviejo', 5107, 'GU', 207, 'ES', 40.52637000, -2.98430000, '2019-10-05 22:45:46', '2022-08-29 11:06:45', 1, 'Q1654328'),
(34327, 'Fuentemolinos', 1146, 'BU', 207, 'ES', 41.60477000, -3.85005000, '2019-10-05 22:45:46', '2022-08-29 11:24:20', 1, 'Q1633314'),
(34328, 'Fuentenebro', 1146, 'BU', 207, 'ES', 41.52873000, -3.75449000, '2019-10-05 22:45:46', '2022-08-29 11:24:20', 1, 'Q1643049'),
(34329, 'Fuentenovilla', 5107, 'GU', 207, 'ES', 40.36248000, -3.09102000, '2019-10-05 22:45:46', '2022-08-29 11:06:45', 1, 'Q1644105'),
(34330, 'Fuentepelayo', 1192, 'SG', 207, 'ES', 41.22249000, -4.17570000, '2019-10-05 22:45:46', '2022-08-29 11:50:42', 1, 'Q24014527'),
(34331, 'Fuentepinilla', 1208, 'SO', 207, 'ES', 41.56683000, -2.76288000, '2019-10-05 22:45:46', '2022-08-29 11:51:23', 1, 'Q837631'),
(34332, 'Fuentepiñel', 1192, 'SG', 207, 'ES', 41.39892000, -4.04295000, '2019-10-05 22:45:46', '2022-08-29 11:50:42', 1, 'Q1948851'),
(34333, 'Fuenterroble de Salvatierra', 1147, 'SA', 207, 'ES', 40.56466000, -5.73379000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q1650181'),
(34334, 'Fuenterrobles', 1175, 'V', 207, 'ES', 39.58333000, -1.35000000, '2019-10-05 22:45:46', '2022-08-29 12:05:40', 1, 'Q1650181'),
(34335, 'Fuentes', 5106, 'CU', 207, 'ES', 39.96667000, -2.01667000, '2019-10-05 22:45:46', '2022-08-29 11:05:01', 1, 'Q2047688'),
(34336, 'Fuentes Calientes', 5111, 'TE', 207, 'ES', 40.70000000, -0.96667000, '2019-10-05 22:45:46', '2022-08-29 11:29:44', 1, 'Q1653451'),
(34337, 'Fuentes Claras', 5111, 'TE', 207, 'ES', 40.86375000, -1.32266000, '2019-10-05 22:45:46', '2022-08-29 11:29:44', 1, 'Q1643299'),
(34338, 'Fuentes de Andalucía', 1193, 'SE', 207, 'ES', 37.46409000, -5.34615000, '2019-10-05 22:45:46', '2022-08-28 19:08:49', 1, 'Q383897'),
(34339, 'Fuentes de Ayódar', 5110, 'CS', 207, 'ES', 40.03333000, -0.41667000, '2019-10-05 22:45:46', '2022-08-29 11:26:42', 1, 'Q1768676'),
(34340, 'Fuentes de Año', 1189, 'AV', 207, 'ES', 41.01769000, -4.89907000, '2019-10-05 22:45:46', '2022-08-29 11:49:56', 1, 'Q1632373'),
(34341, 'Fuentes de Béjar', 1147, 'SA', 207, 'ES', 40.50815000, -5.69270000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q1632373'),
(34342, 'Fuentes de Carbajal', 1200, 'LE', 207, 'ES', 42.17776000, -5.44606000, '2019-10-05 22:45:46', '2019-10-05 22:45:46', 1, 'Q611235'),
(34343, 'Fuentes de Ebro', 5113, 'Z', 207, 'ES', 41.51250000, -0.63159000, '2019-10-05 22:45:46', '2022-08-29 11:42:54', 1, 'Q1639187'),
(34344, 'Fuentes de Jiloca', 5113, 'Z', 207, 'ES', 41.22859000, -1.53616000, '2019-10-05 22:45:46', '2022-08-29 11:42:54', 1, 'Q1650339'),
(34345, 'Fuentes de León', 5092, 'BA', 207, 'ES', 38.06866000, -6.53884000, '2019-10-05 22:45:46', '2022-08-28 18:09:23', 1, 'Q1098694'),
(34346, 'Fuentes de Magaña', 1208, 'SO', 207, 'ES', 41.93521000, -2.17950000, '2019-10-05 22:45:46', '2022-08-29 11:51:23', 1, 'Q832203'),
(34347, 'Fuentes de Nava', 1157, 'P', 207, 'ES', 42.08333000, -4.78333000, '2019-10-05 22:45:46', '2022-08-29 11:45:45', 1, 'Q936286'),
(34348, 'Fuentes de Oñoro', 1147, 'SA', 207, 'ES', 40.59102000, -6.81144000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q927947'),
(34349, 'Fuentes de Ropel', 1161, 'ZA', 207, 'ES', 42.00377000, -5.54532000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q1652139'),
(34350, 'Fuentes de Rubielos', 5111, 'TE', 207, 'ES', 40.16667000, -0.61667000, '2019-10-05 22:45:46', '2022-08-29 11:29:44', 1, 'Q602657'),
(34351, 'Fuentes de Valdepero', 1157, 'P', 207, 'ES', 42.07532000, -4.50050000, '2019-10-05 22:45:46', '2022-08-29 11:45:45', 1, 'Q1923930'),
(34352, 'Fuentesaúco', 1161, 'ZA', 207, 'ES', 41.23043000, -5.49722000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q1765240'),
(34353, 'Fuentesaúco de Fuentidueña', 1192, 'SG', 207, 'ES', 41.42445000, -4.06215000, '2019-10-05 22:45:46', '2022-08-29 11:50:42', 1, 'Q1948546'),
(34354, 'Fuentesecas', 1161, 'ZA', 207, 'ES', 41.63077000, -5.47252000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q953786'),
(34355, 'Fuentesoto', 1192, 'SG', 207, 'ES', 41.45664000, -3.91835000, '2019-10-05 22:45:46', '2022-08-29 11:50:42', 1, 'Q1917695'),
(34356, 'Fuentespalda', 5111, 'TE', 207, 'ES', 40.80666000, 0.06517000, '2019-10-05 22:45:46', '2022-08-29 11:29:44', 1, 'Q1653347'),
(34357, 'Fuentespina', 1146, 'BU', 207, 'ES', 41.63079000, -3.68475000, '2019-10-05 22:45:46', '2022-08-29 11:24:20', 1, 'Q1633201'),
(34358, 'Fuentespreadas', 1161, 'ZA', 207, 'ES', 41.32627000, -5.62798000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q1752411'),
(34359, 'Fuentestrún', 1208, 'SO', 207, 'ES', 41.87466000, -2.08283000, '2019-10-05 22:45:46', '2022-08-29 11:51:23', 1, 'Q835985'),
(34360, 'Fuentidueña', 1192, 'SG', 207, 'ES', 41.44226000, -3.97899000, '2019-10-05 22:45:46', '2022-08-29 11:50:42', 1, 'Q1919353'),
(34361, 'Fuentidueña de Tajo', 1158, 'M', 207, 'ES', 40.11574000, -3.15718000, '2019-10-05 22:45:46', '2022-08-29 12:04:40', 1, 'Q1228889'),
(34362, 'Fuerte del Rey', 5100, 'J', 207, 'ES', 37.87492000, -3.88389000, '2019-10-05 22:45:46', '2022-08-28 19:04:30', 1, 'Q1751785'),
(34363, 'Fuertescusa', 5106, 'CU', 207, 'ES', 40.47552000, -2.17620000, '2019-10-05 22:45:46', '2022-08-29 11:05:01', 1, 'Q1903149'),
(34364, 'Fulleda', 5104, 'L', 207, 'ES', 41.46334000, 1.02395000, '2019-10-05 22:45:46', '2022-08-29 10:55:25', 1, 'Q1650308'),
(34365, 'Funes', 1204, 'NA', 207, 'ES', 42.31562000, -1.80017000, '2019-10-05 22:45:46', '2022-08-29 12:06:07', 1, 'Q1767897'),
(34366, 'Fustiñana', 1204, 'NA', 207, 'ES', 42.02087000, -1.48526000, '2019-10-05 22:45:46', '2022-08-29 12:06:07', 1, 'Q1773016'),
(34368, 'Fórnoles', 5111, 'TE', 207, 'ES', 40.89359000, -0.00383000, '2019-10-05 22:45:46', '2022-08-29 11:29:44', 1, 'Q2409408'),
(34369, 'Gabaldón', 5106, 'CU', 207, 'ES', 39.61667000, -1.93333000, '2019-10-05 22:45:46', '2022-08-29 11:05:01', 1, 'Q1768695'),
(34370, 'Gaibiel', 5110, 'CS', 207, 'ES', 39.93333000, -0.48333000, '2019-10-05 22:45:46', '2022-08-29 11:26:42', 1, 'Q1768700'),
(34371, 'Gajanejos', 5107, 'GU', 207, 'ES', 40.84147000, -2.89229000, '2019-10-05 22:45:46', '2022-08-29 11:06:45', 1, 'Q1654894'),
(34372, 'Gajates', 1147, 'SA', 207, 'ES', 40.78254000, -5.36506000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q1654894'),
(34373, 'Galapagar', 1158, 'M', 207, 'ES', 40.57830000, -4.00426000, '2019-10-05 22:45:46', '2022-08-29 12:04:40', 1, 'Q1615953'),
(34374, 'Galar', 1204, 'NA', 207, 'ES', 42.76147000, -1.69881000, '2019-10-05 22:45:46', '2022-08-29 12:06:07', 1, 'Q1752994'),
(34375, 'Galaroza', 5099, 'H', 207, 'ES', 37.92864000, -6.70730000, '2019-10-05 22:45:46', '2022-08-28 19:00:43', 1, 'Q1615091'),
(34376, 'Galbarros', 1146, 'BU', 207, 'ES', 42.52729000, -3.43827000, '2019-10-05 22:45:46', '2022-08-29 11:24:20', 1, 'Q951812'),
(34377, 'Galbárruli', 1171, 'LO', 207, 'ES', 42.62181000, -2.96087000, '2019-10-05 22:45:46', '2022-08-29 12:05:09', 1, 'Q928037'),
(34378, 'Galdakao', 5094, 'BI', 207, 'ES', 43.23073000, -2.84290000, '2019-10-05 22:45:46', '2022-08-28 18:25:55', 1, 'Q928037'),
(34379, 'Galende', 1161, 'ZA', 207, 'ES', 42.10440000, -6.66252000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q1646379'),
(34380, 'Galera', 5098, 'GR', 207, 'ES', 37.74262000, -2.55175000, '2019-10-05 22:45:46', '2022-08-28 18:52:57', 1, 'Q574343'),
(34381, 'Galilea', 1171, 'LO', 207, 'ES', 42.34703000, -2.23605000, '2019-10-05 22:45:46', '2022-08-29 12:05:09', 1, 'Q1769826'),
(34382, 'Galindo y Perahuy', 1147, 'SA', 207, 'ES', 40.94397000, -5.87410000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q1777817'),
(34383, 'Galinduste', 1147, 'SA', 207, 'ES', 40.66266000, -5.54003000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q1777833'),
(34384, 'Galisancho', 1147, 'SA', 207, 'ES', 40.74418000, -5.55471000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q1650167'),
(34385, 'Galisteo', 1190, 'CC', 207, 'ES', 39.97642000, -6.26782000, '2019-10-05 22:45:46', '2022-08-28 18:12:22', 1, 'Q1613373'),
(34386, 'Gallegos', 1192, 'SG', 207, 'ES', 41.07508000, -3.78591000, '2019-10-05 22:45:46', '2022-08-29 11:50:42', 1, 'Q1613373'),
(34387, 'Gallegos de Argañán', 1147, 'SA', 207, 'ES', 40.63146000, -6.70246000, '2019-10-05 22:45:46', '2022-08-29 11:44:51', 1, 'Q1777885'),
(34388, 'Gallegos de Hornija', 1183, 'VA', 207, 'ES', 41.60980000, -5.09768000, '2019-10-05 22:45:46', '2022-08-29 11:48:45', 1, 'Q1907362'),
(34389, 'Gallegos de Sobrinos', 1189, 'AV', 207, 'ES', 40.71693000, -5.11224000, '2019-10-05 22:45:46', '2022-08-29 11:49:56', 1, 'Q1611031'),
(34390, 'Gallegos del Pan', 1161, 'ZA', 207, 'ES', 41.59985000, -5.58117000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q1441320'),
(34391, 'Gallegos del Río', 1161, 'ZA', 207, 'ES', 41.73497000, -6.17435000, '2019-10-05 22:45:46', '2022-08-29 11:48:03', 1, 'Q507089'),
(34392, 'Gallifa', 5102, 'B', 207, 'ES', 41.69243000, 2.11346000, '2019-10-05 22:45:46', '2022-08-29 10:50:00', 1, 'Q507089'),
(34393, 'Gallinero de Cameros', 1171, 'LO', 207, 'ES', 42.17229000, -2.61793000, '2019-10-05 22:45:46', '2022-08-29 12:05:09', 1, 'Q1637628'),
(34394, 'Gallipienzo', 1204, 'NA', 207, 'ES', 42.52450000, -1.41115000, '2019-10-05 22:45:46', '2022-08-29 12:06:07', 1, 'Q593987'),
(34395, 'Gallocanta', 5113, 'Z', 207, 'ES', 40.99624000, -1.50774000, '2019-10-05 22:45:46', '2022-08-29 11:42:54', 1, 'Q1635952'),
(34396, 'Gallur', 5113, 'Z', 207, 'ES', 41.86830000, -1.31577000, '2019-10-05 22:45:47', '2022-08-29 11:42:54', 1, 'Q572779'),
(34397, 'Galve', 5111, 'TE', 207, 'ES', 40.65591000, -0.88217000, '2019-10-05 22:45:47', '2022-08-29 11:29:44', 1, 'Q787403'),
(34398, 'Galve de Sorbe', 5107, 'GU', 207, 'ES', 41.21667000, -3.18333000, '2019-10-05 22:45:47', '2022-08-29 11:06:45', 1, 'Q1653637'),
(34399, 'Galápagos', 5107, 'GU', 207, 'ES', 40.69347000, -3.33537000, '2019-10-05 22:45:47', '2022-08-29 11:06:45', 1, 'Q24011197'),
(34400, 'Gamonal', 1200, 'LE', 207, 'ES', 42.35664000, -3.67321000, '2019-10-05 22:45:47', '2019-10-05 22:45:47', 1, 'Q3095047'),
(34401, 'Gamones', 1161, 'ZA', 207, 'ES', 41.46690000, -6.17621000, '2019-10-05 22:45:47', '2022-08-29 11:48:03', 1, 'Q1778033'),
(34402, 'Gandesa', 1203, 'T', 207, 'ES', 41.05375000, 0.43850000, '2019-10-05 22:45:47', '2022-08-29 10:57:32', 1, 'Q1778033'),
(34403, 'Gandia', 1175, 'V', 207, 'ES', 38.96667000, -0.18333000, '2019-10-05 22:45:47', '2022-08-29 12:05:40', 1, 'Q1778033'),
(34404, 'Garaballa', 5106, 'CU', 207, 'ES', 39.81667000, -1.36667000, '2019-10-05 22:45:47', '2022-08-29 11:05:01', 1, 'Q1769810'),
(34405, 'Garachico', 5112, 'TF', 207, 'ES', 28.37365000, -16.76342000, '2019-10-05 22:45:47', '2022-08-29 11:31:13', 1, 'Q612815'),
(34406, 'Garafía', 5112, 'TF', 207, 'ES', 28.81667000, -17.93333000, '2019-10-05 22:45:47', '2022-08-29 11:31:13', 1, 'Q434053'),
(34407, 'Garbayuela', 5092, 'BA', 207, 'ES', 39.04956000, -4.99856000, '2019-10-05 22:45:47', '2022-08-28 18:09:23', 1, 'Q1447660'),
(34408, 'Garcia', 1203, 'T', 207, 'ES', 41.13333000, 0.65000000, '2019-10-05 22:45:47', '2022-08-29 10:57:32', 1, 'Q1447660'),
(34409, 'Garcibuey', 1147, 'SA', 207, 'ES', 40.51552000, -5.99439000, '2019-10-05 22:45:47', '2022-08-29 11:44:51', 1, 'Q611412'),
(34410, 'Garcihernández', 1147, 'SA', 207, 'ES', 40.86096000, -5.43567000, '2019-10-05 22:45:47', '2022-08-29 11:44:51', 1, 'Q1012957'),
(34411, 'Garcillán', 1192, 'SG', 207, 'ES', 40.97771000, -4.26577000, '2019-10-05 22:45:47', '2022-08-29 11:50:42', 1, 'Q24014520'),
(34412, 'Garcirrey', 1147, 'SA', 207, 'ES', 40.90049000, -6.13120000, '2019-10-05 22:45:47', '2022-08-29 11:44:51', 1, 'Q1648373'),
(34413, 'Garde', 1204, 'NA', 207, 'ES', 42.78937000, -0.92589000, '2019-10-05 22:45:47', '2022-08-29 12:06:07', 1, 'Q1648373'),
(34414, 'Gargallo', 5111, 'TE', 207, 'ES', 40.83583000, -0.58442000, '2019-10-05 22:45:47', '2022-08-29 11:29:44', 1, 'Q1648373'),
(34415, 'Garganta de los Montes', 1158, 'M', 207, 'ES', 40.91992000, -3.68375000, '2019-10-05 22:45:47', '2022-08-29 12:04:40', 1, 'Q1752777'),
(34416, 'Garganta del Villar', 1189, 'AV', 207, 'ES', 40.44965000, -5.10420000, '2019-10-05 22:45:47', '2022-08-29 11:49:56', 1, 'Q1609897'),
(34417, 'Garganta la Olla', 1190, 'CC', 207, 'ES', 40.11049000, -5.77665000, '2019-10-05 22:45:47', '2022-08-28 18:12:22', 1, 'Q253672'),
(34418, 'Gargantilla', 1190, 'CC', 207, 'ES', 40.24835000, -5.92068000, '2019-10-05 22:45:47', '2022-08-28 18:12:22', 1, 'Q1643602'),
(34419, 'Gargüera', 1190, 'CC', 207, 'ES', 40.06130000, -5.92781000, '2019-10-05 22:45:47', '2022-08-28 18:12:22', 1, 'Q3197342'),
(34420, 'Garlitos', 5092, 'BA', 207, 'ES', 38.88022000, -5.04759000, '2019-10-05 22:45:47', '2022-08-28 18:09:23', 1, 'Q1446316'),
(34421, 'Garrafe de Torío', 1200, 'LE', 207, 'ES', 42.73285000, -5.52360000, '2019-10-05 22:45:47', '2020-05-01 17:23:16', 1, 'Q1446316'),
(34422, 'Garralda', 1204, 'NA', 207, 'ES', 42.94818000, -1.28624000, '2019-10-05 22:45:47', '2022-08-29 12:06:07', 1, 'Q1753012'),
(34423, 'Garray', 1208, 'SO', 207, 'ES', 41.81617000, -2.44592000, '2019-10-05 22:45:47', '2022-08-29 11:51:23', 1, 'Q1049602'),
(34424, 'Garriguella', 5103, 'GI', 207, 'ES', 42.34392000, 3.06506000, '2019-10-05 22:45:47', '2022-08-29 10:53:16', 1, 'Q11324'),
(34425, 'Garrigàs', 5103, 'GI', 207, 'ES', 42.19343000, 2.95438000, '2019-10-05 22:45:47', '2022-08-29 10:53:16', 1, 'Q11324'),
(34427, 'Garrucha', 5095, 'AL', 207, 'ES', 37.18141000, -1.82252000, '2019-10-05 22:45:47', '2022-08-28 18:41:41', 1, 'Q904768'),
(34428, 'Garvín', 1190, 'CC', 207, 'ES', 39.71984000, -5.34678000, '2019-10-05 22:45:47', '2022-08-28 18:12:22', 1, 'Q1658763'),
(34429, 'Garínoain', 1204, 'NA', 207, 'ES', 42.60036000, -1.64434000, '2019-10-05 22:45:47', '2022-08-29 12:06:07', 1, 'Q1753016'),
(34430, 'Gascones', 1158, 'M', 207, 'ES', 41.01843000, -3.64217000, '2019-10-05 22:45:47', '2022-08-29 12:04:40', 1, 'Q1772211'),
(34431, 'Gascueña', 5106, 'CU', 207, 'ES', 40.29937000, -2.51856000, '2019-10-05 22:45:47', '2022-08-29 11:05:01', 1, 'Q1647522'),
(34432, 'Gascueña de Bornova', 5107, 'GU', 207, 'ES', 41.14243000, -3.01924000, '2019-10-05 22:45:47', '2022-08-29 11:06:45', 1, 'Q1654686'),
(34434, 'Gata', 1190, 'CC', 207, 'ES', 40.23758000, -6.59684000, '2019-10-05 22:45:47', '2022-08-28 18:12:22', 1, 'Q14318'),
(34435, 'Gata de Gorgos', 5108, 'A', 207, 'ES', 38.77443000, 0.08538000, '2019-10-05 22:45:47', '2022-08-29 11:15:47', 1, 'Q376236'),
(34436, 'Gatika', 5094, 'BI', 207, 'ES', 43.36313000, -2.87294000, '2019-10-05 22:45:47', '2022-08-28 18:25:55', 1, 'Q376236'),
(34437, 'Gatón de Campos', 1183, 'VA', 207, 'ES', 42.04984000, -4.98078000, '2019-10-05 22:45:47', '2022-08-29 11:48:45', 1, 'Q62128592'),
(34438, 'Gaucín', 5101, 'MA', 207, 'ES', 36.51784000, -5.31581000, '2019-10-05 22:45:47', '2022-08-28 19:06:53', 1, 'Q944119'),
(34439, 'Gavarda', 1175, 'V', 207, 'ES', 39.08333000, -0.55000000, '2019-10-05 22:45:47', '2022-08-29 12:05:40', 1, 'Q944119'),
(34440, 'Gavilanes', 1189, 'AV', 207, 'ES', 40.27791000, -4.85321000, '2019-10-05 22:45:47', '2022-08-29 11:49:56', 1, 'Q944119'),
(34441, 'Gavà', 5102, 'B', 207, 'ES', 41.30605000, 2.00123000, '2019-10-05 22:45:47', '2022-08-29 10:50:00', 1, 'Q944119'),
(34442, 'Gaztelu', 1191, 'SS', 207, 'ES', 43.11622000, -2.02439000, '2019-10-05 22:45:47', '2022-08-28 18:36:50', 1, 'Q1641862'),
(34443, 'Gea de Albarracín', 5111, 'TE', 207, 'ES', 40.41114000, -1.34823000, '2019-10-05 22:45:47', '2022-08-29 11:29:44', 1, 'Q1641862'),
(34444, 'Gejuelo del Barro', 1147, 'SA', 207, 'ES', 41.07633000, -6.12332000, '2019-10-05 22:45:47', '2022-08-29 11:44:51', 1, 'Q1648239'),
(34445, 'Geldo', 5110, 'CS', 207, 'ES', 39.83333000, -0.46667000, '2019-10-05 22:45:47', '2022-08-29 11:26:42', 1, 'Q1769741'),
(34446, 'Gelida', 5102, 'B', 207, 'ES', 41.43333000, 1.86667000, '2019-10-05 22:45:47', '2022-08-29 10:50:00', 1, 'Q763729'),
(34447, 'Gelsa', 5113, 'Z', 207, 'ES', 41.40766000, -0.46158000, '2019-10-05 22:45:47', '2022-08-29 11:42:54', 1, 'Q1751975'),
(34448, 'Gelves', 1193, 'SE', 207, 'ES', 37.33481000, -6.02601000, '2019-10-05 22:45:47', '2022-08-28 19:08:49', 1, 'Q1628386'),
(34449, 'Gema', 1161, 'ZA', 207, 'ES', 41.41855000, -5.64906000, '2019-10-05 22:45:47', '2022-08-29 11:48:03', 1, 'Q1998982'),
(34450, 'Gemuño', 1189, 'AV', 207, 'ES', 40.59159000, -4.78178000, '2019-10-05 22:45:47', '2022-08-29 11:49:56', 1, 'Q659661'),
(34451, 'Genalguacil', 5101, 'MA', 207, 'ES', 36.54546000, -5.23572000, '2019-10-05 22:45:47', '2022-08-28 19:06:53', 1, 'Q1630292'),
(34452, 'Genevilla', 1204, 'NA', 207, 'ES', 42.64520000, -2.39021000, '2019-10-05 22:45:48', '2022-08-29 12:06:07', 1, 'Q1751480'),
(34453, 'Genovés', 1175, 'V', 207, 'ES', 38.98915000, -0.46992000, '2019-10-05 22:45:48', '2022-08-29 12:05:40', 1, 'Q1751480'),
(34454, 'Gerena', 1193, 'SE', 207, 'ES', 37.52957000, -6.15479000, '2019-10-05 22:45:48', '2022-08-28 19:08:49', 1, 'Q1601614'),
(34455, 'Geria', 1183, 'VA', 207, 'ES', 41.57868000, -4.87663000, '2019-10-05 22:45:48', '2022-08-29 11:48:45', 1, 'Q1919486'),
(34456, 'Gerindote', 1205, 'TO', 207, 'ES', 39.96594000, -4.30278000, '2019-10-05 22:45:48', '2022-08-29 11:08:30', 1, 'Q371770'),
(34457, 'Gernika-Lumo', 5094, 'BI', 207, 'ES', 43.31667000, -2.68333000, '2019-10-05 22:45:48', '2022-08-28 18:25:55', 1, 'Q189848'),
(34458, 'Gestalgar', 1175, 'V', 207, 'ES', 39.60000000, -0.83333000, '2019-10-05 22:45:48', '2022-08-29 12:05:40', 1, 'Q189848'),
(34459, 'Getafe', 1158, 'M', 207, 'ES', 40.30571000, -3.73295000, '2019-10-05 22:45:48', '2022-08-29 12:04:40', 1, 'Q8802'),
(34460, 'Getaria', 1191, 'SS', 207, 'ES', 43.30326000, -2.20444000, '2019-10-05 22:45:48', '2022-08-28 18:36:49', 1, 'Q849330'),
(34461, 'Getxo', 5094, 'BI', 207, 'ES', 43.35689000, -3.01146000, '2019-10-05 22:45:48', '2022-08-28 18:25:55', 1, 'Q849330'),
(34462, 'Gibraleón', 5099, 'H', 207, 'ES', 37.37628000, -6.96895000, '2019-10-05 22:45:48', '2022-08-28 19:00:43', 1, 'Q913215'),
(34463, 'Gilena', 1193, 'SE', 207, 'ES', 37.25150000, -4.91442000, '2019-10-05 22:45:48', '2022-08-28 19:08:49', 1, 'Q1448419'),
(34464, 'Gilet', 1175, 'V', 207, 'ES', 39.68333000, -0.31667000, '2019-10-05 22:45:48', '2022-08-29 12:05:40', 1, 'Q55839'),
(34465, 'Gimialcón', 1189, 'AV', 207, 'ES', 40.87705000, -5.12308000, '2019-10-05 22:45:48', '2022-08-29 11:49:56', 1, 'Q1632465'),
(34466, 'Gimileo', 1171, 'LO', 207, 'ES', 42.54992000, -2.82237000, '2019-10-05 22:45:48', '2022-08-29 12:05:09', 1, 'Q1646942');

