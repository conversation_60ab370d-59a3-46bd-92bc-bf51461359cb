INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(14674, 'Satubinha', 2015, 'MA', 31, 'BR', -3.84804000, -45.25543000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2064663'),
(14675, 'Saubara', 2002, 'BA', 31, 'BR', -12.73750000, -38.76861000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1793933'),
(14676, 'Saudade do Iguaçu', 2022, 'PR', 31, 'BR', -25.70473000, -52.60816000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q1793933'),
(14677, '<PERSON>uda<PERSON>', 2014, 'SC', 31, '<PERSON>', -26.87900000, -53.04038000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1793933'),
(14678, '<PERSON><PERSON>de', 2002, '<PERSON>', 31, 'BR', -10.84463000, -40.37510000, '2019-10-05 22:35:29', '2020-05-01 17:22:36', 1, 'Q2247752'),
(14679, 'Schroeder', 2014, 'SC', 31, 'BR', -26.41250000, -49.07306000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q398844'),
(14680, 'Seabra', 2002, 'BA', 31, 'BR', -12.41713000, -41.77049000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1794971'),
(14681, 'Seara', 2014, 'SC', 31, 'BR', -27.14478000, -52.34857000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1794971'),
(14682, 'Sebastianópolis do Sul', 2021, 'SP', 31, 'BR', -20.61558000, -49.90753000, '2019-10-05 22:35:29', '2020-05-01 17:22:38', 1, 'Q1794971'),
(14683, 'Sebastião Barros', 2008, 'PI', 31, 'BR', -10.59755000, -44.83609000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q1794971'),
(14684, 'Sebastião Laranjeiras', 2002, 'BA', 31, 'BR', -14.56848000, -43.13729000, '2019-10-05 22:35:29', '2020-05-01 17:22:36', 1, 'Q1794971'),
(14685, 'Sebastião Leal', 2008, 'PI', 31, 'BR', -7.51324000, -44.02545000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q1794971'),
(14686, 'Seberi', 2001, 'RS', 31, 'BR', -27.50884000, -53.36482000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1794971'),
(14687, 'Sede Nova', 2001, 'RS', 31, 'BR', -27.64490000, -53.96149000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1794971'),
(14688, 'Segredo', 2001, 'RS', 31, 'BR', -29.29705000, -52.92281000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1794971'),
(14689, 'Selbach', 2001, 'RS', 31, 'BR', -28.67641000, -52.98236000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1794971'),
(14690, 'Selvíria', 2010, 'MS', 31, 'BR', -20.24958000, -51.83228000, '2019-10-05 22:35:29', '2020-05-01 17:22:36', 1, 'Q1794971'),
(14691, 'Sem-Peixe', 1998, 'MG', 31, 'BR', -20.07337000, -42.81556000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1789146'),
(14692, 'Sena Madureira', 2012, 'AC', 31, 'BR', -9.06341000, -68.67245000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1754389'),
(14693, 'Senador Alexandre Costa', 2015, 'MA', 31, 'BR', -5.29207000, -43.87491000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1754389'),
(14694, 'Senador Amaral', 1998, 'MG', 31, 'BR', -22.55358000, -46.22421000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1788677'),
(14695, 'Senador Canedo', 2000, 'GO', 31, 'BR', -16.70806000, -49.09306000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q985503'),
(14696, 'Senador Cortes', 1998, 'MG', 31, 'BR', -21.76164000, -42.91138000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1789116'),
(14697, 'Senador Elói de Souza', 2019, 'RN', 31, 'BR', -6.04008000, -35.65731000, '2019-10-05 22:35:29', '2020-05-01 17:22:38', 1, 'Q1789116'),
(14698, 'Senador Firmino', 1998, 'MG', 31, 'BR', -20.89680000, -43.11033000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1789127'),
(14699, 'Senador Georgino Avelino', 2019, 'RN', 31, 'BR', -6.14835000, -35.13347000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1789127'),
(14700, 'Senador Guiomard', 2012, 'AC', 31, 'BR', -10.14970000, -67.73741000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q771763'),
(14701, 'Senador José Bento', 1998, 'MG', 31, 'BR', -22.15837000, -46.14282000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q921202'),
(14702, 'Senador José Porfírio', 2009, 'PA', 31, 'BR', -2.59083000, -51.95417000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q609027'),
(14703, 'Senador La Rocque', 2015, 'MA', 31, 'BR', -5.42661000, -47.17194000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q609027'),
(14704, 'Senador Modestino Gonçalves', 1998, 'MG', 31, 'BR', -17.91341000, -43.21328000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q609027'),
(14705, 'Senador Pompeu', 2016, 'CE', 31, 'BR', -5.58806000, -39.37167000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1828350'),
(14706, 'Senador Rui Palmeira', 2007, 'AL', 31, 'BR', -9.33827000, -37.55968000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1795898'),
(14707, 'Senador Salgado Filho', 2001, 'RS', 31, 'BR', -28.02951000, -54.52396000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1795898'),
(14708, 'Senador Sá', 2016, 'CE', 31, 'BR', -3.27654000, -40.44418000, '2019-10-05 22:35:29', '2020-05-01 17:22:36', 1, 'Q1795898'),
(14709, 'Sengés', 2022, 'PR', 31, 'BR', -24.11335000, -49.46315000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q1803479'),
(14710, 'Senhor do Bonfim', 2002, 'BA', 31, 'BR', -10.46139000, -40.18944000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1808316'),
(14711, 'Senhora de Oliveira', 1998, 'MG', 31, 'BR', -20.80180000, -43.34536000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1788595'),
(14712, 'Senhora do Porto', 1998, 'MG', 31, 'BR', -18.90274000, -43.08578000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1788604'),
(14713, 'Senhora dos Remédios', 1998, 'MG', 31, 'BR', -21.02878000, -43.59680000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q1788604'),
(14714, 'Sentinela do Sul', 2001, 'RS', 31, 'BR', -30.60341000, -51.62096000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1788604'),
(14715, 'Sento Sé', 2002, 'BA', 31, 'BR', -10.21251000, -41.57947000, '2019-10-05 22:35:29', '2020-05-01 17:22:36', 1, 'Q1788604'),
(14716, 'Serafina Corrêa', 2001, 'RS', 31, 'BR', -28.69115000, -51.92671000, '2019-10-05 22:35:29', '2020-05-01 17:22:38', 1, 'Q1788604'),
(14717, 'Sericita', 1998, 'MG', 31, 'BR', -20.49536000, -42.45991000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1788604'),
(14718, 'Seringueiras', 2013, 'RO', 31, 'BR', -11.92491000, -63.18938000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1788604'),
(14719, 'Seritinga', 1998, 'MG', 31, 'BR', -21.91887000, -44.46300000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1789225'),
(14720, 'Seropédica', 1997, 'RJ', 31, 'BR', -22.73876000, -43.70855000, '2019-10-05 22:35:29', '2020-05-01 17:22:37', 1, 'Q988677'),
(14721, 'Serra', 2018, 'ES', 31, 'BR', -20.10822000, -40.30186000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q927537'),
(14722, 'Serra Alta', 2014, 'SC', 31, 'BR', -26.70236000, -53.01399000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q927537'),
(14723, 'Serra Azul', 2021, 'SP', 31, 'BR', -21.28755000, -47.53885000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q927537'),
(14724, 'Serra Azul de Minas', 1998, 'MG', 31, 'BR', -18.38225000, -43.20251000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q927537'),
(14725, 'Serra Branca', 2005, 'PB', 31, 'BR', -6.53333000, -38.26667000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q22014132'),
(14726, 'Serra Caiada', 2019, 'RN', 31, 'BR', -6.14234000, -35.66845000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2298053'),
(14727, 'Serra Dourada', 2002, 'BA', 31, 'BR', -12.91499000, -43.79600000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2298053'),
(14728, 'Serra Grande', 2005, 'PB', 31, 'BR', -7.25090000, -38.39588000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2298053'),
(14729, 'Serra Negra', 2021, 'SP', 31, 'BR', -22.61222000, -46.70056000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1439179'),
(14730, 'Serra Negra do Norte', 2019, 'RN', 31, 'BR', -6.57598000, -37.39418000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1439179'),
(14731, 'Serra Nova Dourada', 2011, 'MT', 31, 'BR', -12.02901000, -51.33412000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2065364'),
(14732, 'Serra Preta', 2002, 'BA', 31, 'BR', -12.06913000, -39.30050000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2065364'),
(14733, 'Serra Redonda', 2005, 'PB', 31, 'BR', -7.15665000, -35.67628000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2065364'),
(14734, 'Serra Talhada', 2006, 'PE', 31, 'BR', -7.99194000, -38.29833000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2000133'),
(14735, 'Serra da Raiz', 2005, 'PB', 31, 'BR', -6.69703000, -35.43579000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q2000133'),
(14736, 'Serra da Saudade', 1998, 'MG', 31, 'BR', -19.38394000, -45.80002000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1754911'),
(14737, 'Serra de São Bento', 2019, 'RN', 31, 'BR', -6.43702000, -35.71194000, '2019-10-05 22:35:29', '2020-05-01 17:22:38', 1, 'Q1754911'),
(14738, 'Serra do Mel', 2019, 'RN', 31, 'BR', -5.12203000, -37.03121000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1754911'),
(14739, 'Serra do Navio', 1999, 'AP', 31, 'BR', 1.65803000, -52.28195000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1754911'),
(14740, 'Serra do Ramalho', 2002, 'BA', 31, 'BR', -13.41101000, -43.77849000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q600869'),
(14741, 'Serra do Salitre', 1998, 'MG', 31, 'BR', -19.14990000, -46.65307000, '2019-10-05 22:35:29', '2019-10-05 22:35:29', 1, 'Q1788636'),
(14742, 'Serra dos Aimorés', 1998, 'MG', 31, 'BR', -17.75022000, -40.27113000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q543282'),
(14743, 'Serrana', 2021, 'SP', 31, 'BR', -21.21139000, -47.59556000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q22032853'),
(14744, 'Serrania', 1998, 'MG', 31, 'BR', -21.55510000, -46.10712000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q593028'),
(14745, 'Serrano do Maranhão', 2015, 'MA', 31, 'BR', -1.85659000, -45.08514000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q593028'),
(14746, 'Serranos', 1998, 'MG', 31, 'BR', -21.82389000, -44.54236000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1788624'),
(14747, 'Serranópolis', 2000, 'GO', 31, 'BR', -18.27044000, -52.24749000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q1788624'),
(14748, 'Serranópolis de Minas', 1998, 'MG', 31, 'BR', -15.89984000, -42.85134000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1788624'),
(14749, 'Serranópolis do Iguaçu', 2022, 'PR', 31, 'BR', -25.47001000, -54.01644000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1788624'),
(14750, 'Serraria', 2005, 'PB', 31, 'BR', -6.86365000, -35.66926000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2064614'),
(14751, 'Serrinha', 2002, 'BA', 31, 'BR', -11.66417000, -39.00750000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1008215'),
(14752, 'Serrinha', 2019, 'RN', 31, 'BR', -6.25327000, -35.59475000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1008215'),
(14753, 'Serrinha dos Pintos', 2019, 'RN', 31, 'BR', -6.15256000, -37.98860000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1008215'),
(14754, 'Serrita', 2006, 'PE', 31, 'BR', -7.84008000, -39.40812000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1008215'),
(14755, 'Serro', 1998, 'MG', 31, 'BR', -18.52041000, -43.38075000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q919904'),
(14756, 'Serrolândia', 2002, 'BA', 31, 'BR', -11.54018000, -40.24783000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q919904'),
(14757, 'Sertaneja', 2022, 'PR', 31, 'BR', -22.95832000, -50.88804000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q919904'),
(14758, 'Sertanópolis', 2022, 'PR', 31, 'BR', -23.05861000, -51.03639000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q2005080'),
(14759, 'Sertânia', 2006, 'PE', 31, 'BR', -8.07361000, -37.26444000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q2011023'),
(14760, 'Sertão', 2001, 'RS', 31, 'BR', -28.00412000, -52.35664000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q2011023'),
(14761, 'Sertão Santana', 2001, 'RS', 31, 'BR', -30.49105000, -51.66541000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q2011023'),
(14762, 'Sertãozinho', 2021, 'SP', 31, 'BR', -21.13778000, -47.99028000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q1795634'),
(14763, 'Sertãozinho', 2005, 'PB', 31, 'BR', -6.73939000, -35.41628000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1973538'),
(14764, 'Sete Barras', 2021, 'SP', 31, 'BR', -24.27723000, -47.95013000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1973538'),
(14765, 'Sete Lagoas', 1998, 'MG', 31, 'BR', -19.46583000, -44.24667000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q841226'),
(14766, 'Sete Quedas', 2010, 'MS', 31, 'BR', -23.88066000, -55.02727000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q841226'),
(14767, 'Sete de Setembro', 2001, 'RS', 31, 'BR', -28.12546000, -54.47905000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q841226'),
(14768, 'Setubinha', 1998, 'MG', 31, 'BR', -17.66692000, -42.19505000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q588111'),
(14769, 'Severiano Melo', 2019, 'RN', 31, 'BR', -5.77722000, -37.95778000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1802204'),
(14770, 'Severiano de Almeida', 2001, 'RS', 31, 'BR', -27.40649000, -52.10747000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1802204'),
(14771, 'Severínia', 2021, 'SP', 31, 'BR', -20.78066000, -48.79037000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q1760500'),
(14772, 'Siderópolis', 2014, 'SC', 31, 'BR', -28.59778000, -49.42444000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q1759010'),
(14773, 'Sidrolândia', 2010, 'MS', 31, 'BR', -20.93194000, -54.96139000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q1792185'),
(14774, 'Sigefredo Pacheco', 2008, 'PI', 31, 'BR', -4.79886000, -41.78459000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1792185'),
(14775, 'Silva Jardim', 1997, 'RJ', 31, 'BR', -22.65083000, -42.39167000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q927311'),
(14776, 'Silvanópolis', 2020, 'TO', 31, 'BR', -11.11343000, -48.22341000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q927311'),
(14777, 'Silveira Martins', 2001, 'RS', 31, 'BR', -29.63635000, -53.54609000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q927311'),
(14778, 'Silveiras', 2021, 'SP', 31, 'BR', -22.73649000, -44.84778000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1761063'),
(14779, 'Silveirânia', 1998, 'MG', 31, 'BR', -21.14447000, -43.19826000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1788659'),
(14780, 'Silves', 2004, 'AM', 31, 'BR', -2.76846000, -58.62751000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1788659'),
(14781, 'Silvianópolis', 1998, 'MG', 31, 'BR', -22.05297000, -45.81579000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1788659'),
(14782, 'Silvânia', 2000, 'GO', 31, 'BR', -16.65889000, -48.60806000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q985489'),
(14783, 'Simolândia', 2000, 'GO', 31, 'BR', -14.43819000, -46.59232000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q985489'),
(14784, 'Simonésia', 1998, 'MG', 31, 'BR', -19.99894000, -41.98726000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q767863'),
(14785, 'Simplício Mendes', 2008, 'PI', 31, 'BR', -7.85389000, -41.91028000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1943081'),
(14786, 'Simão Dias', 2003, 'SE', 31, 'BR', -10.71163000, -37.77142000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q1848973'),
(14787, 'Simão Pereira', 1998, 'MG', 31, 'BR', -21.96631000, -43.29030000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1788499'),
(14788, 'Simões', 2008, 'PI', 31, 'BR', -7.59889000, -40.81778000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q2089169'),
(14789, 'Simões Filho', 2002, 'BA', 31, 'BR', -12.78444000, -38.40389000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q1645080'),
(14790, 'Sinimbu', 2001, 'RS', 31, 'BR', -29.41512000, -52.60499000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1645080'),
(14791, 'Sinop', 2011, 'MT', 31, 'BR', -11.86417000, -55.50250000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q180192'),
(14792, 'Siqueira Campos', 2022, 'PR', 31, 'BR', -23.68889000, -49.83389000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2064771'),
(14793, 'Sirinhaém', 2006, 'PE', 31, 'BR', -8.59083000, -35.11611000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q929655'),
(14794, 'Siriri', 2003, 'SE', 31, 'BR', -10.58724000, -37.12123000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q929655'),
(14795, 'Sobradinho', 2002, 'BA', 31, 'BR', -12.83333000, -39.10000000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q22032938'),
(14796, 'Sobradinho', 2001, 'RS', 31, 'BR', -29.41325000, -53.02018000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2008068'),
(14797, 'Sobrado', 2005, 'PB', 31, 'BR', -7.16887000, -35.23251000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2078739'),
(14798, 'Sobral', 2016, 'CE', 31, 'BR', -3.85932000, -40.04376000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1815899'),
(14799, 'Sobrália', 1998, 'MG', 31, 'BR', -19.21309000, -42.14818000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1815899'),
(14800, 'Socorro', 2021, 'SP', 31, 'BR', -22.59139000, -46.52889000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1797503'),
(14801, 'Socorro do Piauí', 2008, 'PI', 31, 'BR', -7.89820000, -42.50745000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q1797503'),
(14802, 'Soledade', 2005, 'PB', 31, 'BR', -7.05722000, -36.36278000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2066120'),
(14803, 'Soledade', 2001, 'RS', 31, 'BR', -28.84047000, -52.51015000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1785706'),
(14804, 'Soledade de Minas', 1998, 'MG', 31, 'BR', -22.02216000, -45.04271000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1788572'),
(14805, 'Solidão', 2006, 'PE', 31, 'BR', -7.59117000, -37.65945000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q2064859'),
(14806, 'Solonópole', 2016, 'CE', 31, 'BR', -5.87073000, -39.01210000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q975946'),
(14807, 'Solânea', 2005, 'PB', 31, 'BR', -6.73321000, -35.69495000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q2010744'),
(14808, 'Sombrio', 2014, 'SC', 31, 'BR', -29.11389000, -49.61667000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1784827'),
(14809, 'Sonora', 2010, 'MS', 31, 'BR', -17.67403000, -54.45326000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1784827'),
(14810, 'Sooretama', 2018, 'ES', 31, 'BR', -19.08218000, -40.14134000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1784827'),
(14811, 'Sorocaba', 2021, 'SP', 31, 'BR', -23.50167000, -47.45806000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q188901'),
(14812, 'Sorriso', 2011, 'MT', 31, 'BR', -12.68167000, -55.69953000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q188901'),
(14813, 'Sossêgo', 2005, 'PB', 31, 'BR', -6.67592000, -36.17875000, '2019-10-05 22:35:30', '2020-05-01 17:22:37', 1, 'Q188901'),
(14814, 'Soure', 2009, 'PA', 31, 'BR', -0.71667000, -48.52333000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q1016643'),
(14815, 'Sousa', 2005, 'PB', 31, 'BR', -6.73098000, -38.18614000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2011653'),
(14816, 'Souto Soares', 2002, 'BA', 31, 'BR', -11.94603000, -41.94815000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2011653'),
(14817, 'Sucupira', 2020, 'TO', 31, 'BR', -12.01900000, -48.84477000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2011653'),
(14818, 'Sucupira do Norte', 2015, 'MA', 31, 'BR', -6.46683000, -44.28011000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2011653'),
(14819, 'Sucupira do Riachão', 2015, 'MA', 31, 'BR', -6.46791000, -43.49722000, '2019-10-05 22:35:30', '2020-05-01 17:22:36', 1, 'Q2011653'),
(14820, 'Sud Mennucci', 2021, 'SP', 31, 'BR', -20.61860000, -50.88011000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2011653'),
(14821, 'Sul Brasil', 2014, 'SC', 31, 'BR', -26.70686000, -52.94355000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2011653'),
(14822, 'Sulina', 2022, 'PR', 31, 'BR', -25.67144000, -52.68373000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q2011653'),
(14823, 'Sumaré', 2021, 'SP', 31, 'BR', -22.82194000, -47.26694000, '2019-10-05 22:35:30', '2020-05-01 17:22:38', 1, 'Q942135'),
(14824, 'Sumidouro', 1997, 'RJ', 31, 'BR', -22.13179000, -42.66127000, '2019-10-05 22:35:30', '2019-10-05 22:35:30', 1, 'Q942135'),
(14825, 'Sumé', 2005, 'PB', 31, 'BR', -7.67167000, -36.88000000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2008324'),
(14826, 'Surubim', 2006, 'PE', 31, 'BR', -7.83306000, -35.75472000, '2019-10-05 22:35:31', '2019-10-05 22:35:31', 1, 'Q2064701'),
(14827, 'Sussuapara', 2008, 'PI', 31, 'BR', -7.00335000, -41.39182000, '2019-10-05 22:35:31', '2019-10-05 22:35:31', 1, 'Q2064701'),
(14828, 'Suzano', 2021, 'SP', 31, 'BR', -23.54250000, -46.31083000, '2019-10-05 22:35:31', '2019-10-05 22:35:31', 1, 'Q737670'),
(14829, 'Suzanápolis', 2021, 'SP', 31, 'BR', -20.51030000, -51.07079000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q737670'),
(14830, 'Sátiro Dias', 2002, 'BA', 31, 'BR', -11.63166000, -38.48886000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q737670'),
(14831, 'São Benedito', 2016, 'CE', 31, 'BR', -4.04957000, -40.94585000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q737670'),
(14832, 'São Benedito do Rio Preto', 2015, 'MA', 31, 'BR', -3.33098000, -43.74036000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1806571'),
(14833, 'São Benedito do Sul', 2006, 'PE', 31, 'BR', -8.77212000, -35.90168000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1806571'),
(14834, 'São Bentinho', 2005, 'PB', 31, 'BR', -6.88452000, -37.75898000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2009398'),
(14835, 'São Bento', 2015, 'MA', 31, 'BR', -2.69583000, -44.82139000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q2104116'),
(14836, 'São Bento', 2005, 'PB', 31, 'BR', -6.47344000, -37.47144000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q767395'),
(14837, 'São Bento Abade', 1998, 'MG', 31, 'BR', -21.55981000, -45.06653000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1755164'),
(14838, 'São Bento do Norte', 2019, 'RN', 31, 'BR', -5.14414000, -35.98910000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1786782'),
(14839, 'São Bento do Sapucaí', 2021, 'SP', 31, 'BR', -22.67091000, -45.68394000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1786782'),
(14840, 'São Bento do Sul', 2014, 'SC', 31, 'BR', -26.25028000, -49.37861000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1765057'),
(14841, 'São Bento do Tocantins', 2020, 'TO', 31, 'BR', -5.95028000, -47.99630000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1801161'),
(14842, 'São Bento do Trairí', 2019, 'RN', 31, 'BR', -6.39968000, -36.05920000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1802608'),
(14843, 'São Bento do Una', 2006, 'PE', 31, 'BR', -8.53733000, -36.48122000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1802608'),
(14844, 'São Bernardino', 2014, 'SC', 31, 'BR', -26.46259000, -52.96921000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1802608'),
(14845, 'São Bernardo', 2015, 'MA', 31, 'BR', -3.43614000, -42.40555000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q2065297'),
(14846, 'São Bernardo do Campo', 2021, 'SP', 31, 'BR', -23.69389000, -46.56500000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q184403'),
(14847, 'São Bonifácio', 2014, 'SC', 31, 'BR', -27.95656000, -48.93970000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q184403'),
(14848, 'São Borja', 2001, 'RS', 31, 'BR', -28.66056000, -56.00444000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q385327'),
(14849, 'São Braz do Piauí', 2008, 'PI', 31, 'BR', -8.97380000, -42.97568000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2078851'),
(14850, 'São Brás', 2007, 'AL', 31, 'BR', -10.12409000, -36.85043000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q22035039'),
(14851, 'São Brás do Suaçuí', 1998, 'MG', 31, 'BR', -20.62195000, -43.96923000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q22035039'),
(14852, 'São Caetano de Odivelas', 2009, 'PA', 31, 'BR', -0.75000000, -48.02000000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q632789'),
(14853, 'São Caetano do Sul', 2021, 'SP', 31, 'BR', -23.62429000, -46.56241000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q967648'),
(14854, 'São Caitano', 2006, 'PE', 31, 'BR', -8.33931000, -36.16156000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q967648'),
(14855, 'São Carlos', 2021, 'SP', 31, 'BR', -22.01750000, -47.89083000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q194338'),
(14856, 'São Carlos', 2014, 'SC', 31, 'BR', -27.04450000, -53.03549000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q194338'),
(14857, 'São Carlos do Ivaí', 2022, 'PR', 31, 'BR', -23.35478000, -52.52074000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q194338'),
(14858, 'São Cristóvão', 2003, 'SE', 31, 'BR', -11.01472000, -37.20639000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q839003'),
(14859, 'São Cristóvão do Sul', 2014, 'SC', 31, 'BR', -27.26707000, -50.37731000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q839003'),
(14860, 'São Desidério', 2002, 'BA', 31, 'BR', -12.36333000, -44.97333000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1761582'),
(14861, 'São Domingos', 2000, 'GO', 31, 'BR', -13.39833000, -46.31833000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1025764'),
(14862, 'São Domingos', 2005, 'PB', 31, 'BR', -6.80733000, -37.91657000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1025764'),
(14863, 'São Domingos', 2002, 'BA', 31, 'BR', -11.48464000, -39.59916000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q2019014'),
(14864, 'São Domingos', 2014, 'SC', 31, 'BR', -26.56428000, -52.55695000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q2019014'),
(14865, 'São Domingos', 2003, 'SE', 31, 'BR', -10.78191000, -37.56218000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q2019014'),
(14866, 'São Domingos das Dores', 1998, 'MG', 31, 'BR', -19.51985000, -42.03401000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1754673'),
(14867, 'São Domingos do Araguaia', 2009, 'PA', 31, 'BR', -5.71930000, -48.72755000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1754673'),
(14868, 'São Domingos do Azeitão', 2015, 'MA', 31, 'BR', -6.85798000, -44.58281000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1754673'),
(14869, 'São Domingos do Capim', 2009, 'PA', 31, 'BR', -1.87163000, -47.77837000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1754673'),
(14870, 'São Domingos do Cariri', 2005, 'PB', 31, 'BR', -7.56749000, -36.36393000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1754673'),
(14871, 'São Domingos do Maranhão', 2015, 'MA', 31, 'BR', -5.57583000, -44.38528000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q2036695'),
(14872, 'São Domingos do Norte', 2018, 'ES', 31, 'BR', -19.12970000, -40.55454000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1806625'),
(14873, 'São Domingos do Prata', 1998, 'MG', 31, 'BR', -19.89785000, -42.88422000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q635947'),
(14874, 'São Domingos do Sul', 2001, 'RS', 31, 'BR', -28.54446000, -51.87330000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q635947'),
(14875, 'São Felipe', 2002, 'BA', 31, 'BR', -14.83860000, -41.39174000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q22035103'),
(14876, 'São Felipe d\'Oeste', 2013, 'RO', 31, 'BR', -11.89774000, -61.47739000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q22035103'),
(14877, 'São Fernando', 2019, 'RN', 31, 'BR', -6.29726000, -37.13895000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q22035103'),
(14878, 'São Fidélis', 1997, 'RJ', 31, 'BR', -21.64611000, -41.74694000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1787793'),
(14879, 'São Francisco', 2005, 'PB', 31, 'BR', -6.63302000, -38.03780000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1787793'),
(14880, 'São Francisco', 2003, 'SE', 31, 'BR', -10.33062000, -36.86390000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q2331416'),
(14881, 'São Francisco', 2021, 'SP', 31, 'BR', -20.36457000, -50.67463000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q2331416'),
(14882, 'São Francisco', 1998, 'MG', 31, 'BR', -15.88365000, -44.84306000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q22066968'),
(14883, 'São Francisco de Assis', 2001, 'RS', 31, 'BR', -29.55028000, -55.13111000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1750146'),
(14884, 'São Francisco de Assis do Piauí', 2008, 'PI', 31, 'BR', -8.12334000, -41.48056000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1750146'),
(14885, 'São Francisco de Goiás', 2000, 'GO', 31, 'BR', -15.95223000, -49.24888000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1750146'),
(14886, 'São Francisco de Itabapoana', 1997, 'RJ', 31, 'BR', -21.38002000, -41.14905000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1787681'),
(14887, 'São Francisco de Paula', 2001, 'RS', 31, 'BR', -29.24246000, -50.44928000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q22035149'),
(14888, 'São Francisco de Paula', 1998, 'MG', 31, 'BR', -20.71868000, -44.99234000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2382290'),
(14889, 'São Francisco de Sales', 1998, 'MG', 31, 'BR', -19.77179000, -49.81040000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q643392'),
(14890, 'São Francisco do Brejão', 2015, 'MA', 31, 'BR', -5.13880000, -47.34887000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q643392'),
(14891, 'São Francisco do Conde', 2002, 'BA', 31, 'BR', -12.64556000, -38.63335000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1648073'),
(14892, 'São Francisco do Glória', 1998, 'MG', 31, 'BR', -20.78528000, -42.28488000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788549'),
(14893, 'São Francisco do Guaporé', 2013, 'RO', 31, 'BR', -12.38313000, -63.12648000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1788549'),
(14894, 'São Francisco do Maranhão', 2015, 'MA', 31, 'BR', -6.23353000, -42.95249000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1788549'),
(14895, 'São Francisco do Oeste', 2019, 'RN', 31, 'BR', -5.96836000, -38.16251000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1788549'),
(14896, 'São Francisco do Pará', 2009, 'PA', 31, 'BR', -1.14641000, -47.75713000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2008769'),
(14897, 'São Francisco do Piauí', 2008, 'PI', 31, 'BR', -7.16899000, -42.55086000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2008769'),
(14898, 'São Francisco do Sul', 2014, 'SC', 31, 'BR', -26.24333000, -48.63806000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q986536'),
(14899, 'São Félix', 2002, 'BA', 31, 'BR', -12.67807000, -38.99979000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q986536'),
(14900, 'São Félix de Balsas', 2015, 'MA', 31, 'BR', -6.94884000, -44.83461000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q986536'),
(14901, 'São Félix de Minas', 1998, 'MG', 31, 'BR', -18.55894000, -41.44459000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788630'),
(14902, 'São Félix do Araguaia', 2011, 'MT', 31, 'BR', -11.44230000, -52.13916000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1788630'),
(14903, 'São Félix do Coribe', 2002, 'BA', 31, 'BR', -13.41471000, -43.98138000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1788630'),
(14904, 'São Félix do Piauí', 2008, 'PI', 31, 'BR', -5.88422000, -42.10830000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788630'),
(14905, 'São Félix do Tocantins', 2020, 'TO', 31, 'BR', -10.06488000, -46.72778000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q1788630'),
(14906, 'São Félix do Xingu', 2009, 'PA', 31, 'BR', -6.64472000, -51.99500000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1807317'),
(14907, 'São Gabriel', 2002, 'BA', 31, 'BR', -10.95576000, -41.55411000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1807317'),
(14908, 'São Gabriel', 2001, 'RS', 31, 'BR', -30.33369000, -54.33029000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q929890'),
(14909, 'São Gabriel da Cachoeira', 2004, 'AM', 31, 'BR', -0.11810000, -67.08527000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q694840'),
(14910, 'São Gabriel da Palha', 2018, 'ES', 31, 'BR', -18.97356000, -40.52522000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1798735'),
(14911, 'São Gabriel do Oeste', 2010, 'MS', 31, 'BR', -19.13170000, -54.44865000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1798735'),
(14912, 'São Geraldo', 1998, 'MG', 31, 'BR', -20.90833000, -42.82833000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788508'),
(14913, 'São Geraldo da Piedade', 1998, 'MG', 31, 'BR', -18.91396000, -42.31056000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788508'),
(14914, 'São Geraldo do Araguaia', 2009, 'PA', 31, 'BR', -6.40056000, -48.55500000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2008501'),
(14915, 'São Geraldo do Baixio', 1998, 'MG', 31, 'BR', -18.91798000, -41.36253000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2008501'),
(14916, 'São Gonçalo', 1997, 'RJ', 31, 'BR', -22.82694000, -43.05389000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q83114'),
(14917, 'São Gonçalo do Abaeté', 1998, 'MG', 31, 'BR', -18.23315000, -45.61317000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q83114'),
(14918, 'São Gonçalo do Amarante', 2016, 'CE', 31, 'BR', -3.60722000, -38.96833000, '2019-10-05 22:35:31', '2020-05-01 17:22:36', 1, 'Q1999934'),
(14919, 'São Gonçalo do Amarante', 2019, 'RN', 31, 'BR', -5.77241000, -35.33245000, '2019-10-05 22:35:31', '2020-05-01 17:22:38', 1, 'Q2088944'),
(14920, 'São Gonçalo do Gurguéia', 2008, 'PI', 31, 'BR', -10.07630000, -45.42387000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q2088944'),
(14921, 'São Gonçalo do Pará', 1998, 'MG', 31, 'BR', -19.97290000, -44.84024000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788460'),
(14922, 'São Gonçalo do Piauí', 2008, 'PI', 31, 'BR', -6.02033000, -42.67020000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788460'),
(14923, 'São Gonçalo do Rio Abaixo', 1998, 'MG', 31, 'BR', -19.81182000, -43.32838000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788460'),
(14924, 'São Gonçalo do Rio Preto', 1998, 'MG', 31, 'BR', -18.07630000, -43.35534000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q1788460'),
(14925, 'São Gonçalo do Sapucaí', 1998, 'MG', 31, 'BR', -21.91053000, -45.60119000, '2019-10-05 22:35:31', '2020-05-01 17:22:37', 1, 'Q952405'),
(14926, 'São Gonçalo dos Campos', 2002, 'BA', 31, 'BR', -12.43333000, -38.96667000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1793783'),
(14927, 'São Gotardo', 1998, 'MG', 31, 'BR', -19.31111000, -46.04889000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1788283'),
(14928, 'São Jerônimo', 2001, 'RS', 31, 'BR', -29.95917000, -51.72222000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1639741'),
(14929, 'São Jerônimo da Serra', 2022, 'PR', 31, 'BR', -23.68708000, -50.78987000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1639741'),
(14930, 'São Joaquim', 2014, 'SC', 31, 'BR', -28.29389000, -49.93167000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q904782'),
(14931, 'São Joaquim da Barra', 2021, 'SP', 31, 'BR', -20.58139000, -47.85472000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1759699'),
(14932, 'São Joaquim de Bicas', 1998, 'MG', 31, 'BR', -20.05781000, -44.24979000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q666247'),
(14933, 'São Joaquim do Monte', 2006, 'PE', 31, 'BR', -8.43250000, -35.80444000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q2013565'),
(14934, 'São Jorge', 2001, 'RS', 31, 'BR', -28.49890000, -51.73076000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q2006981'),
(14935, 'São Jorge d\'Oeste', 2022, 'PR', 31, 'BR', -25.66317000, -52.94857000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1803364'),
(14936, 'São Jorge do Ivaí', 2022, 'PR', 31, 'BR', -23.43841000, -52.30359000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1803364'),
(14937, 'São Jorge do Patrocínio', 2022, 'PR', 31, 'BR', -23.74952000, -53.88553000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1803364'),
(14938, 'São José', 2014, 'SC', 31, 'BR', -28.21171000, -49.16320000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q22035261'),
(14939, 'São José da Barra', 1998, 'MG', 31, 'BR', -20.75050000, -46.28526000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q22035261'),
(14940, 'São José da Bela Vista', 2021, 'SP', 31, 'BR', -20.59934000, -47.62940000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q22035261'),
(14941, 'São José da Boa Vista', 2022, 'PR', 31, 'BR', -23.96483000, -49.65190000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q22035261'),
(14942, 'São José da Coroa Grande', 2006, 'PE', 31, 'BR', -8.89778000, -35.14778000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q573729'),
(14943, 'São José da Lagoa Tapada', 2005, 'PB', 31, 'BR', -6.94245000, -38.09393000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q573729'),
(14944, 'São José da Laje', 2007, 'AL', 31, 'BR', -9.00972000, -36.05833000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1808170'),
(14945, 'São José da Lapa', 1998, 'MG', 31, 'BR', -19.69017000, -43.99106000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1787340'),
(14946, 'São José da Safira', 1998, 'MG', 31, 'BR', -18.33059000, -42.10391000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1787414'),
(14947, 'São José da Tapera', 2007, 'AL', 31, 'BR', -9.55833000, -37.38111000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1808190'),
(14948, 'São José da Varginha', 1998, 'MG', 31, 'BR', -19.68910000, -44.55308000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1808190'),
(14949, 'São José da Vitória', 2002, 'BA', 31, 'BR', -15.07185000, -39.35016000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1808190'),
(14950, 'São José das Missões', 2001, 'RS', 31, 'BR', -27.79738000, -53.12963000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1808190'),
(14951, 'São José das Palmeiras', 2022, 'PR', 31, 'BR', -24.83457000, -54.12721000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1808190'),
(14952, 'São José de Caiana', 2005, 'PB', 31, 'BR', -7.25916000, -38.34483000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1808190'),
(14953, 'São José de Espinharas', 2005, 'PB', 31, 'BR', -6.80244000, -37.38224000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1808190'),
(14954, 'São José de Mipibu', 2019, 'RN', 31, 'BR', -6.04284000, -35.30608000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q734571'),
(14955, 'São José de Piranhas', 2005, 'PB', 31, 'BR', -7.12056000, -38.50194000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q2010773'),
(14956, 'São José de Princesa', 2005, 'PB', 31, 'BR', -7.70649000, -38.08465000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q2010773'),
(14957, 'São José de Ribamar', 2015, 'MA', 31, 'BR', -2.56194000, -44.05417000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1006367'),
(14958, 'São José de Ubá', 1997, 'RJ', 31, 'BR', -21.37831000, -41.95256000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1006367'),
(14959, 'São José do Alegre', 1998, 'MG', 31, 'BR', -22.34058000, -45.52023000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1788432'),
(14960, 'São José do Barreiro', 2021, 'SP', 31, 'BR', -22.73717000, -44.58545000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1788432'),
(14961, 'São José do Belmonte', 2006, 'PE', 31, 'BR', -7.86139000, -38.75972000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q2011317'),
(14962, 'São José do Bonfim', 2005, 'PB', 31, 'BR', -7.13715000, -37.31607000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q2011317'),
(14963, 'São José do Brejo do Cruz', 2005, 'PB', 31, 'BR', -6.23731000, -37.37932000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q2011317'),
(14964, 'São José do Calçado', 2018, 'ES', 31, 'BR', -20.98599000, -41.66419000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1806727'),
(14965, 'São José do Campestre', 2019, 'RN', 31, 'BR', -6.31556000, -35.71389000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1802815'),
(14966, 'São José do Cedro', 2014, 'SC', 31, 'BR', -26.48127000, -53.56285000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1802815'),
(14967, 'São José do Cerrito', 2014, 'SC', 31, 'BR', -27.60401000, -50.65663000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1802815'),
(14968, 'São José do Divino', 2008, 'PI', 31, 'BR', -3.74297000, -41.90119000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1802815'),
(14969, 'São José do Divino', 1998, 'MG', 31, 'BR', -18.40103000, -41.37248000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q22065088'),
(14970, 'São José do Egito', 2006, 'PE', 31, 'BR', -7.47889000, -37.27444000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1911145'),
(14971, 'São José do Goiabal', 1998, 'MG', 31, 'BR', -19.92360000, -42.69184000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1911145'),
(14972, 'São José do Herval', 2001, 'RS', 31, 'BR', -29.06441000, -52.27281000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1911145'),
(14973, 'São José do Hortêncio', 2001, 'RS', 31, 'BR', -29.54364000, -51.24977000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1911145'),
(14974, 'São José do Inhacorá', 2001, 'RS', 31, 'BR', -27.73525000, -54.12155000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1911145'),
(14975, 'São José do Jacuri', 1998, 'MG', 31, 'BR', -18.25160000, -42.67396000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1788487'),
(14976, 'São José do Jacuípe', 2002, 'BA', 31, 'BR', -11.46833000, -39.90377000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1807737'),
(14977, 'São José do Mantimento', 1998, 'MG', 31, 'BR', -20.02453000, -41.76334000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1760621'),
(14978, 'São José do Norte', 2001, 'RS', 31, 'BR', -31.80089000, -51.76198000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1760621'),
(14979, 'São José do Ouro', 2001, 'RS', 31, 'BR', -27.75580000, -51.55722000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q1760621'),
(14980, 'São José do Peixe', 2008, 'PI', 31, 'BR', -7.49544000, -42.49355000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1760621'),
(14981, 'São José do Piauí', 2008, 'PI', 31, 'BR', -6.84096000, -41.51702000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q1760621'),
(14982, 'São José do Povo', 2011, 'MT', 31, 'BR', -16.44547000, -54.28499000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1760621'),
(14983, 'São José do Rio Claro', 2011, 'MT', 31, 'BR', -13.52704000, -56.85065000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q1760621'),
(14984, 'São José do Rio Pardo', 2021, 'SP', 31, 'BR', -21.59556000, -46.88861000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q984555'),
(14985, 'São José do Rio Preto', 2021, 'SP', 31, 'BR', -20.81972000, -49.37944000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q192181'),
(14986, 'São José do Sabugi', 2005, 'PB', 31, 'BR', -6.82784000, -36.80335000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q192181'),
(14987, 'São José do Seridó', 2019, 'RN', 31, 'BR', -6.50251000, -36.85104000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q192181'),
(14988, 'São José do Sul', 2001, 'RS', 31, 'BR', -29.54416000, -51.49254000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q944254'),
(14989, 'São José do Vale do Rio Preto', 1997, 'RJ', 31, 'BR', -22.17062000, -42.91651000, '2019-10-05 22:35:32', '2020-05-01 17:22:37', 1, 'Q944254'),
(14990, 'São José do Xingu', 2011, 'MT', 31, 'BR', -10.72794000, -52.62112000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q944254'),
(14991, 'São José dos Ausentes', 2001, 'RS', 31, 'BR', -28.68307000, -49.94674000, '2019-10-05 22:35:32', '2020-05-01 17:22:38', 1, 'Q944254'),
(14992, 'São José dos Basílios', 2015, 'MA', 31, 'BR', -5.05917000, -44.59458000, '2019-10-05 22:35:32', '2020-05-01 17:22:36', 1, 'Q944254'),
(14993, 'São José dos Campos', 2021, 'SP', 31, 'BR', -23.17944000, -45.88694000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q191642'),
(14994, 'São José dos Cordeiros', 2005, 'PB', 31, 'BR', -7.41638000, -36.84773000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q191642'),
(14995, 'São José dos Pinhais', 2022, 'PR', 31, 'BR', -25.53020000, -49.20836000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q478818'),
(14996, 'São José dos Quatro Marcos', 2011, 'MT', 31, 'BR', -15.56664000, -58.29717000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q478818'),
(14997, 'São José dos Ramos', 2005, 'PB', 31, 'BR', -7.25103000, -35.37410000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q478818'),
(14998, 'São João', 2022, 'PR', 31, 'BR', -25.78385000, -52.80418000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q478818'),
(14999, 'São João', 2006, 'PE', 31, 'BR', -8.86322000, -36.39102000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q2355766'),
(15000, 'São João Batista', 2015, 'MA', 31, 'BR', -2.95528000, -44.80694000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q1919029'),
(15001, 'São João Batista', 2014, 'SC', 31, 'BR', -27.27611000, -48.84944000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q2023825'),
(15002, 'São João Batista do Glória', 1998, 'MG', 31, 'BR', -20.53122000, -46.44580000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787319'),
(15003, 'São João Evangelista', 1998, 'MG', 31, 'BR', -18.50849000, -42.78221000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q22065095'),
(15004, 'São João Nepomuceno', 1998, 'MG', 31, 'BR', -21.58615000, -43.01503000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q919616'),
(15005, 'São João d\'Aliança', 2000, 'GO', 31, 'BR', -14.42907000, -47.41407000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q989515'),
(15006, 'São João da Barra', 1997, 'RJ', 31, 'BR', -21.64028000, -41.05111000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q923201'),
(15007, 'São João da Boa Vista', 2021, 'SP', 31, 'BR', -21.96917000, -46.79806000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q925419'),
(15008, 'São João da Canabrava', 2008, 'PI', 31, 'BR', -6.72756000, -41.38226000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q925419'),
(15009, 'São João da Fronteira', 2008, 'PI', 31, 'BR', -4.09363000, -41.21552000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q925419'),
(15010, 'São João da Lagoa', 1998, 'MG', 31, 'BR', -16.87872000, -44.34205000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q785452'),
(15011, 'São João da Mata', 1998, 'MG', 31, 'BR', -21.93521000, -45.93300000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787264'),
(15012, 'São João da Paraúna', 2000, 'GO', 31, 'BR', -16.82392000, -50.35316000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q1787264'),
(15013, 'São João da Ponta', 2009, 'PA', 31, 'BR', -0.85361000, -47.97803000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787264'),
(15014, 'São João da Ponte', 1998, 'MG', 31, 'BR', -15.90314000, -43.83053000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787105'),
(15015, 'São João da Serra', 2008, 'PI', 31, 'BR', -5.44173000, -41.85793000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q2099643'),
(15016, 'São João da Urtiga', 2001, 'RS', 31, 'BR', -27.79677000, -51.83882000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q2099643'),
(15017, 'São João da Varjota', 2008, 'PI', 31, 'BR', -6.94733000, -41.92917000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q2099643'),
(15018, 'São João das Duas Pontes', 2021, 'SP', 31, 'BR', -20.41912000, -50.38960000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q2099643'),
(15019, 'São João das Missões', 1998, 'MG', 31, 'BR', -14.88665000, -44.21265000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q2099643'),
(15020, 'São João de Iracema', 2021, 'SP', 31, 'BR', -20.51199000, -50.36102000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q2099643'),
(15021, 'São João de Meriti', 1997, 'RJ', 31, 'BR', -22.80389000, -43.37222000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q459690'),
(15022, 'São João de Pirabas', 2009, 'PA', 31, 'BR', -0.77472000, -47.17722000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q2008440'),
(15023, 'São João del Rei', 1998, 'MG', 31, 'BR', -21.25033000, -44.24481000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q740965'),
(15024, 'São João do Araguaia', 2009, 'PA', 31, 'BR', -5.44300000, -48.74932000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q740965'),
(15025, 'São João do Arraial', 2008, 'PI', 31, 'BR', -3.80325000, -42.46973000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q740965'),
(15026, 'São João do Caiuá', 2022, 'PR', 31, 'BR', -22.82937000, -52.28543000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q740965'),
(15027, 'São João do Cariri', 2005, 'PB', 31, 'BR', -7.48465000, -36.48602000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q740965'),
(15028, 'São João do Carú', 2015, 'MA', 31, 'BR', -3.55923000, -46.39242000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q740965'),
(15029, 'São João do Itaperiú', 2014, 'SC', 31, 'BR', -26.60265000, -48.80051000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q740965'),
(15030, 'São João do Ivaí', 2022, 'PR', 31, 'BR', -23.97916000, -51.88263000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q740965'),
(15031, 'São João do Jaguaribe', 2016, 'CE', 31, 'BR', -5.31884000, -38.26878000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q740965'),
(15032, 'São João do Manhuaçu', 1998, 'MG', 31, 'BR', -20.38351000, -42.15632000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787194'),
(15033, 'São João do Manteninha', 1998, 'MG', 31, 'BR', -18.73950000, -41.15749000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787661'),
(15034, 'São João do Oeste', 2014, 'SC', 31, 'BR', -27.08310000, -53.59218000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q1787661'),
(15035, 'São João do Oriente', 1998, 'MG', 31, 'BR', -19.34848000, -42.17407000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787661'),
(15036, 'São João do Pacuí', 1998, 'MG', 31, 'BR', -16.53392000, -44.54062000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1787332'),
(15037, 'São João do Paraíso', 2015, 'MA', 31, 'BR', -6.40174000, -46.88929000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q1787332'),
(15038, 'São João do Paraíso', 1998, 'MG', 31, 'BR', -15.37652000, -41.95693000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q22065096'),
(15039, 'São João do Pau d\'Alho', 2021, 'SP', 31, 'BR', -21.20519000, -51.66857000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q1760232'),
(15040, 'São João do Piauí', 2008, 'PI', 31, 'BR', -8.27575000, -42.34005000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1937319'),
(15041, 'São João do Polêsine', 2001, 'RS', 31, 'BR', -29.64559000, -53.47291000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q1937319'),
(15042, 'São João do Rio do Peixe', 2005, 'PB', 31, 'BR', -6.71851000, -38.42942000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1937319'),
(15043, 'São João do Sabugi', 2019, 'RN', 31, 'BR', -6.69650000, -37.15433000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q1937319'),
(15044, 'São João do Soter', 2015, 'MA', 31, 'BR', -4.96497000, -43.73953000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q1937319'),
(15045, 'São João do Sul', 2014, 'SC', 31, 'BR', -29.20713000, -49.85103000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q1937319'),
(15046, 'São João do Tigre', 2005, 'PB', 31, 'BR', -8.10906000, -36.79766000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1937319'),
(15047, 'São João do Triunfo', 2022, 'PR', 31, 'BR', -25.70410000, -50.26243000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1937319'),
(15048, 'São João dos Inhamuns', 2016, 'CE', 31, 'BR', -6.00000000, -40.28333000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q22014537'),
(15049, 'São João dos Patos', 2015, 'MA', 31, 'BR', -6.49500000, -43.70222000, '2019-10-05 22:35:33', '2020-05-01 17:22:36', 1, 'Q2064621'),
(15050, 'São Julião', 2008, 'PI', 31, 'BR', -7.05987000, -40.79725000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1987363'),
(15051, 'São Leopoldo', 2001, 'RS', 31, 'BR', -29.76028000, -51.14722000, '2019-10-05 22:35:33', '2020-05-01 17:22:38', 1, 'Q688275'),
(15052, 'São Lourenço', 1998, 'MG', 31, 'BR', -22.11746000, -45.03226000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q985496'),
(15053, 'São Lourenço da Mata', 2006, 'PE', 31, 'BR', -8.00222000, -35.01833000, '2019-10-05 22:35:33', '2020-05-01 17:22:37', 1, 'Q1796467'),
(15054, 'São Lourenço da Serra', 2021, 'SP', 31, 'BR', -23.84783000, -46.93930000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q581348'),
(15055, 'São Lourenço do Oeste', 2014, 'SC', 31, 'BR', -26.35917000, -52.85111000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q1759029'),
(15056, 'São Lourenço do Piauí', 2008, 'PI', 31, 'BR', -9.13336000, -42.40297000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q1759029'),
(15057, 'São Lourenço do Sul', 2001, 'RS', 31, 'BR', -31.36528000, -51.97833000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q326349'),
(15058, 'São Ludgero', 2014, 'SC', 31, 'BR', -28.34254000, -49.17537000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q326349'),
(15059, 'São Luis do Piauí', 2008, 'PI', 31, 'BR', -6.77801000, -41.27650000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q326349'),
(15060, 'São Luiz Gonzaga', 2001, 'RS', 31, 'BR', -28.40833000, -54.96083000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q647406'),
(15061, 'São Luiz do Norte', 2000, 'GO', 31, 'BR', -14.89284000, -49.28212000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1805766'),
(15062, 'São Luiz do Paraitinga', 2021, 'SP', 31, 'BR', -23.26061000, -45.22724000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q1805766'),
(15063, 'São Luís', 2015, 'MA', 31, 'BR', -2.52972000, -44.30278000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q28441'),
(15064, 'São Luís Gonzaga do Maranhão', 2015, 'MA', 31, 'BR', -4.37871000, -44.71595000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q2066188'),
(15065, 'São Luís de Montes Belos', 2000, 'GO', 31, 'BR', -16.52500000, -50.37222000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1008345'),
(15066, 'São Luís do Curu', 2016, 'CE', 31, 'BR', -3.64119000, -39.24877000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1008345'),
(15067, 'São Luís do Quitunde', 2007, 'AL', 31, 'BR', -9.31833000, -35.56111000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1808105'),
(15068, 'São Mamede', 2005, 'PB', 31, 'BR', -6.92111000, -37.09131000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q1999321'),
(15069, 'São Manoel do Paraná', 2022, 'PR', 31, 'BR', -23.38356000, -52.58889000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q1999321'),
(15070, 'São Manuel', 2021, 'SP', 31, 'BR', -22.73111000, -48.57056000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q1760298'),
(15071, 'São Marcos', 2001, 'RS', 31, 'BR', -28.96872000, -51.04900000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q671925'),
(15072, 'São Martinho', 2001, 'RS', 31, 'BR', -27.72779000, -53.96611000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q671925'),
(15073, 'São Martinho', 2014, 'SC', 31, 'BR', -28.10511000, -48.98564000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q671925'),
(15074, 'São Martinho da Serra', 2001, 'RS', 31, 'BR', -29.45793000, -53.88452000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q671925'),
(15075, 'São Mateus', 2018, 'ES', 31, 'BR', -18.72011000, -39.85891000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1763574'),
(15076, 'São Mateus do Maranhão', 2015, 'MA', 31, 'BR', -4.04167000, -44.47500000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q2065276'),
(15077, 'São Mateus do Sul', 2022, 'PR', 31, 'BR', -25.87417000, -50.38278000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q1803791'),
(15078, 'São Miguel', 2019, 'RN', 31, 'BR', -5.46667000, -35.36667000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q22013968'),
(15079, 'São Miguel Arcanjo', 2021, 'SP', 31, 'BR', -23.93766000, -47.99988000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q22013968'),
(15080, 'São Miguel da Baixa Grande', 2008, 'PI', 31, 'BR', -5.81791000, -42.27077000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q22013968'),
(15081, 'São Miguel da Boa Vista', 2014, 'SC', 31, 'BR', -26.68603000, -53.25718000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q22013968'),
(15082, 'São Miguel das Matas', 2002, 'BA', 31, 'BR', -13.04308000, -39.43403000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q22013968'),
(15083, 'São Miguel das Missões', 2001, 'RS', 31, 'BR', -28.72430000, -54.50818000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q2107884'),
(15084, 'São Miguel de Taipu', 2005, 'PB', 31, 'BR', -7.21706000, -35.20175000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q2107884'),
(15085, 'São Miguel do Aleixo', 2003, 'SE', 31, 'BR', -10.36859000, -37.35856000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q2107884'),
(15086, 'São Miguel do Anta', 1998, 'MG', 31, 'BR', -20.73482000, -42.69789000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q1755544'),
(15087, 'São Miguel do Araguaia', 2000, 'GO', 31, 'BR', -13.27500000, -50.16278000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q989524'),
(15088, 'São Miguel do Fidalgo', 2008, 'PI', 31, 'BR', -7.59935000, -42.38796000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q2079673'),
(15089, 'São Miguel do Gostoso', 2019, 'RN', 31, 'BR', -5.18658000, -35.71303000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q2079673'),
(15090, 'São Miguel do Guamá', 2009, 'PA', 31, 'BR', -1.62667000, -47.48333000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q2008532'),
(15091, 'São Miguel do Guaporé', 2013, 'RO', 31, 'BR', -11.70783000, -62.93532000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q2008532'),
(15092, 'São Miguel do Iguaçu', 2022, 'PR', 31, 'BR', -25.34806000, -54.23778000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q1773060'),
(15093, 'São Miguel do Oeste', 2014, 'SC', 31, 'BR', -26.71868000, -53.51940000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q1773060'),
(15094, 'São Miguel do Passa Quatro', 2000, 'GO', 31, 'BR', -16.99955000, -48.66188000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1773060'),
(15095, 'São Miguel do Tapuio', 2008, 'PI', 31, 'BR', -5.70486000, -41.61634000, '2019-10-05 22:35:34', '2020-05-01 17:22:37', 1, 'Q2078456'),
(15096, 'São Miguel do Tocantins', 2020, 'TO', 31, 'BR', -5.53458000, -47.60820000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q2078456'),
(15097, 'São Miguel dos Campos', 2007, 'AL', 31, 'BR', -9.78111000, -36.09361000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1816037'),
(15098, 'São Miguel dos Milagres', 2007, 'AL', 31, 'BR', -9.25423000, -35.38419000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1816037'),
(15099, 'São Nicolau', 2001, 'RS', 31, 'BR', -28.22363000, -55.25589000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q1816037'),
(15100, 'São Patrício', 2000, 'GO', 31, 'BR', -15.34426000, -49.83291000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q726966'),
(15101, 'São Paulo', 2021, 'SP', 31, 'BR', -23.54750000, -46.63611000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q174'),
(15102, 'São Paulo das Missões', 2001, 'RS', 31, 'BR', -27.96215000, -54.95513000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q174'),
(15103, 'São Paulo de Olivença', 2004, 'AM', 31, 'BR', -3.37833000, -68.87250000, '2019-10-05 22:35:34', '2020-05-01 17:22:36', 1, 'Q1794983'),
(15104, 'São Paulo do Potengi', 2019, 'RN', 31, 'BR', -5.88988000, -35.75325000, '2019-10-05 22:35:34', '2020-05-01 17:22:38', 1, 'Q1802399'),
(15105, 'São Pedro', 1997, 'RJ', 31, 'BR', -22.42313000, -42.96612000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1802399'),
(15106, 'São Pedro', 2021, 'SP', 31, 'BR', -22.55963000, -47.90443000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q924342'),
(15107, 'São Pedro', 2019, 'RN', 31, 'BR', -5.85598000, -35.62554000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q924342'),
(15108, 'São Pedro da Aldeia', 1997, 'RJ', 31, 'BR', -22.83917000, -42.10278000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q581213'),
(15109, 'São Pedro da Cipa', 2011, 'MT', 31, 'BR', -15.95162000, -54.77958000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q581213'),
(15110, 'São Pedro da Serra', 2001, 'RS', 31, 'BR', -29.42131000, -51.51442000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1750059'),
(15111, 'São Pedro da União', 1998, 'MG', 31, 'BR', -21.11290000, -46.64760000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1788043'),
(15112, 'São Pedro da Água Branca', 2015, 'MA', 31, 'BR', -5.16149000, -48.36104000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1788043'),
(15113, 'São Pedro das Missões', 2001, 'RS', 31, 'BR', -27.79277000, -53.24444000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1802212'),
(15114, 'São Pedro de Alcântara', 2014, 'SC', 31, 'BR', -27.57276000, -48.83917000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1750756'),
(15115, 'São Pedro do Butiá', 2001, 'RS', 31, 'BR', -28.15561000, -54.90336000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1750756'),
(15116, 'São Pedro do Iguaçu', 2022, 'PR', 31, 'BR', -24.90931000, -53.88765000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1750756'),
(15117, 'São Pedro do Ivaí', 2022, 'PR', 31, 'BR', -23.83342000, -51.86952000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1750756'),
(15118, 'São Pedro do Paraná', 2022, 'PR', 31, 'BR', -22.76847000, -53.16580000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1750756'),
(15119, 'São Pedro do Piauí', 2008, 'PI', 31, 'BR', -5.82135000, -42.77168000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q2077678'),
(15120, 'São Pedro do Suaçuí', 1998, 'MG', 31, 'BR', -18.35451000, -42.59570000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787699'),
(15121, 'São Pedro do Sul', 2001, 'RS', 31, 'BR', -29.62056000, -54.17889000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q2007743'),
(15122, 'São Pedro do Turvo', 2021, 'SP', 31, 'BR', -22.69638000, -49.74295000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q2007743'),
(15123, 'São Pedro dos Crentes', 2015, 'MA', 31, 'BR', -6.81958000, -46.70736000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q2007743'),
(15124, 'São Pedro dos Ferros', 1998, 'MG', 31, 'BR', -20.06522000, -42.56304000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q979308'),
(15125, 'São Rafael', 2019, 'RN', 31, 'BR', -5.85664000, -36.89520000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q979308'),
(15126, 'São Raimundo Nonato', 2008, 'PI', 31, 'BR', -9.05220000, -42.60836000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1935702'),
(15127, 'São Raimundo das Mangabeiras', 2015, 'MA', 31, 'BR', -7.02194000, -45.48111000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q2065231'),
(15128, 'São Raimundo do Doca Bezerra', 2015, 'MA', 31, 'BR', -5.11025000, -45.06894000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1947813'),
(15129, 'São Roberto', 2015, 'MA', 31, 'BR', -4.98137000, -44.99100000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1947813'),
(15130, 'São Romão', 1998, 'MG', 31, 'BR', -16.35912000, -45.46775000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q539353'),
(15131, 'São Roque', 2021, 'SP', 31, 'BR', -23.54011000, -47.11280000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1815958'),
(15132, 'São Roque de Minas', 1998, 'MG', 31, 'BR', -20.16741000, -46.47292000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1755169'),
(15133, 'São Roque do Canaã', 2018, 'ES', 31, 'BR', -19.73027000, -40.67143000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1755169'),
(15134, 'São Salvador do Tocantins', 2020, 'TO', 31, 'BR', -12.54945000, -48.40184000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1755169'),
(15135, 'São Sebastião', 2021, 'SP', 31, 'BR', -23.76000000, -45.40972000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q587013'),
(15136, 'São Sebastião', 2007, 'AL', 31, 'BR', -9.96698000, -36.55280000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q2014339'),
(15137, 'São Sebastião da Amoreira', 2022, 'PR', 31, 'BR', -23.45392000, -50.71309000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q2014339'),
(15138, 'São Sebastião da Bela Vista', 1998, 'MG', 31, 'BR', -22.16425000, -45.78515000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q954848'),
(15139, 'São Sebastião da Boa Vista', 2009, 'PA', 31, 'BR', -1.47873000, -49.62629000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q954848'),
(15140, 'São Sebastião da Grama', 2021, 'SP', 31, 'BR', -21.74730000, -46.75934000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q954848'),
(15141, 'São Sebastião da Vargem Alegre', 1998, 'MG', 31, 'BR', -21.02882000, -42.59877000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787237'),
(15142, 'São Sebastião de Lagoa de Roça', 2005, 'PB', 31, 'BR', -7.08497000, -35.84445000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787237'),
(15143, 'São Sebastião do Alto', 1997, 'RJ', 31, 'BR', -21.86591000, -42.09585000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787237'),
(15144, 'São Sebastião do Anta', 1998, 'MG', 31, 'BR', -19.51787000, -41.93921000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787309'),
(15145, 'São Sebastião do Caí', 2001, 'RS', 31, 'BR', -29.58667000, -51.37556000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1750236'),
(15146, 'São Sebastião do Maranhão', 1998, 'MG', 31, 'BR', -18.06833000, -42.53730000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1788027'),
(15147, 'São Sebastião do Oeste', 1998, 'MG', 31, 'BR', -20.24378000, -45.04068000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787492'),
(15148, 'São Sebastião do Paraíso', 1998, 'MG', 31, 'BR', -20.91694000, -46.99139000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1755708'),
(15149, 'São Sebastião do Passé', 2002, 'BA', 31, 'BR', -12.51250000, -38.49528000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1793748'),
(15150, 'São Sebastião do Rio Preto', 1998, 'MG', 31, 'BR', -19.29918000, -43.22996000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1793748'),
(15151, 'São Sebastião do Rio Verde', 1998, 'MG', 31, 'BR', -22.21276000, -45.02049000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787301'),
(15152, 'São Sebastião do Tocantins', 2020, 'TO', 31, 'BR', -5.25181000, -48.34602000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1787301'),
(15153, 'São Sebastião do Uatumã', 2004, 'AM', 31, 'BR', -1.93197000, -58.74216000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q1787301'),
(15154, 'São Sebastião do Umbuzeiro', 2005, 'PB', 31, 'BR', -8.15690000, -37.02220000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787301'),
(15155, 'São Sepé', 2001, 'RS', 31, 'BR', -30.16056000, -53.56528000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1757144'),
(15156, 'São Simão', 2021, 'SP', 31, 'BR', -21.47917000, -47.55083000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1808922'),
(15157, 'São Simão', 2000, 'GO', 31, 'BR', -19.01080000, -50.60612000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q681631'),
(15158, 'São Thomé das Letras', 1998, 'MG', 31, 'BR', -21.73939000, -44.96812000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q681631'),
(15159, 'São Tiago', 1998, 'MG', 31, 'BR', -20.93607000, -44.55961000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1787130'),
(15160, 'São Tomás de Aquino', 1998, 'MG', 31, 'BR', -20.80317000, -47.13081000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q1788424'),
(15161, 'São Tomé', 2019, 'RN', 31, 'BR', -5.97250000, -36.07528000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q2016592'),
(15162, 'São Tomé', 2022, 'PR', 31, 'BR', -23.52364000, -52.51400000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q2019197'),
(15163, 'São Valentim', 2001, 'RS', 31, 'BR', -27.55106000, -52.58722000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1803245'),
(15164, 'São Valentim do Sul', 2001, 'RS', 31, 'BR', -29.06303000, -51.73687000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1803245'),
(15165, 'São Valério da Natividade', 2020, 'TO', 31, 'BR', -11.86029000, -48.13995000, '2019-10-05 22:35:35', '2025-04-28 16:29:28', 1, 'Q1803245'),
(15166, 'São Valério do Sul', 2001, 'RS', 31, 'BR', -27.81235000, -53.92050000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1803245'),
(15167, 'São Vendelino', 2001, 'RS', 31, 'BR', -29.38319000, -51.37289000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q1803245'),
(15168, 'São Vicente', 2021, 'SP', 31, 'BR', -23.96506000, -46.49683000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q272254'),
(15169, 'São Vicente', 2019, 'RN', 31, 'BR', -6.22380000, -36.65694000, '2019-10-05 22:35:35', '2020-05-01 17:22:38', 1, 'Q272254'),
(15170, 'São Vicente Ferrer', 2015, 'MA', 31, 'BR', -2.87717000, -44.93960000, '2019-10-05 22:35:35', '2020-05-01 17:22:36', 1, 'Q272254'),
(15171, 'São Vicente Férrer', 2006, 'PE', 31, 'BR', -7.60222000, -35.50216000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q272254'),
(15172, 'São Vicente de Minas', 1998, 'MG', 31, 'BR', -21.67023000, -44.46650000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q272254'),
(15173, 'São Vicente do Seridó', 2005, 'PB', 31, 'BR', -6.88683000, -36.42652000, '2019-10-05 22:35:35', '2020-05-01 17:22:37', 1, 'Q272254');

