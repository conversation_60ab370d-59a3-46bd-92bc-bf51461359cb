INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(89019, 'Barqueiros', 2244, '03', 177, 'PT', 41.49111000, -8.73192000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31910871'),
(89020, 'Barrancos', 2230, '02', 177, 'PT', 38.13446000, -6.97604000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q368867'),
(89021, '<PERSON><PERSON>', 2242, '15', 177, 'PT', 38.66314000, -9.07240000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q217394'),
(89022, 'Barrosas', 2243, '13', 177, '<PERSON>', 41.35534000, -8.29943000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31911628'),
(89023, '<PERSON><PERSON><PERSON>', 2240, '10', 177, '<PERSON>', 39.66025000, -8.82475000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31912265'),
(89024, '<PERSON><PERSON> António', 2228, '11', 177, 'PT', 38.73327000, -9.10335000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q31912265'),
(89025, 'Beduido', 2235, '01', 177, 'PT', 40.76427000, -8.56110000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31913149'),
(89026, 'Beiriz de Baixo', 2243, '13', 177, 'PT', 41.39727000, -8.72385000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31913149'),
(89027, 'Beja', 2230, '02', 177, 'PT', 38.01506000, -7.86323000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q3344424'),
(89028, 'Belas', 2228, '11', 177, 'PT', 38.77670000, -9.26353000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31913383'),
(89029, 'Belmonte', 2241, '05', 177, 'PT', 40.36181000, -7.35157000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q816115'),
(89030, 'Belver', 2229, '04', 177, 'PT', 41.24696000, -7.27594000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q816270'),
(89031, 'Bemfica', 2238, '14', 177, 'PT', 39.14325000, -8.68717000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q816270'),
(89032, 'Benavente', 2238, '14', 177, 'PT', 38.90800000, -8.76898000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q571140'),
(89033, 'Benedita', 2240, '10', 177, 'PT', 39.42470000, -8.96996000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31914083'),
(89034, 'Benfica', 2228, '11', 177, 'PT', 38.75087000, -9.20282000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31914083'),
(89035, 'Bensafrim', 2239, '08', 177, 'PT', 37.15583000, -8.73520000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q793072'),
(89036, 'Beringel', 2230, '02', 177, 'PT', 38.05656000, -7.98427000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q820659'),
(89037, 'Biscoitos', 2233, '20', 177, 'PT', 38.78333000, -27.25000000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q616959'),
(89038, 'Boaventura', 2231, '30', 177, 'PT', 32.81846000, -16.97268000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q616959'),
(89039, 'Boavista dos Pinheiros', 2230, '02', 177, 'PT', 37.58058000, -8.66441000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q616959'),
(89040, 'Bobadela', 2228, '11', 177, 'PT', 38.80774000, -9.09925000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31917267'),
(89041, 'Boliqueime', 2239, '08', 177, 'PT', 37.13718000, -8.15820000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31917267'),
(89042, 'Bombarral', 2240, '10', 177, 'PT', 39.26723000, -9.15795000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31924170'),
(89043, 'Borba', 2236, '07', 177, 'PT', 38.80553000, -7.45465000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31924457'),
(89044, 'Boticas', 2234, '17', 177, 'PT', 41.68939000, -7.66914000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q894746'),
(89045, 'Bougado', 2243, '13', 177, 'PT', 41.33979000, -8.55180000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31925314'),
(89046, 'Braga', 2244, '03', 177, 'PT', 41.55032000, -8.42005000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q3344946'),
(89047, 'Bragança Municipality', 2229, '04', 177, 'PT', 41.75608000, -6.75535000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q768261'),
(89048, 'Branca', 2235, '01', 177, 'PT', 40.76653000, -8.48262000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q31925680'),
(89049, 'Brito', 2244, '03', 177, 'PT', 41.45821000, -8.36103000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q49347217'),
(89050, 'Buarcos', 2246, '06', 177, 'PT', 40.16604000, -8.87680000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q33790615'),
(89051, 'Bucelas', 2228, '11', 177, 'PT', 38.90193000, -9.11885000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34787544'),
(89052, 'Cabanas de Tavira', 2239, '08', 177, 'PT', 37.13521000, -7.60048000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q2652869'),
(89053, 'Cabanas de Torres', 2228, '11', 177, 'PT', 39.15581000, -9.06588000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1024717'),
(89054, 'Cabanas de Viriato', 2237, '18', 177, 'PT', 40.47662000, -7.97445000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q64444'),
(89055, 'Cabeceiras de Basto', 2244, '03', 177, 'PT', 41.52079000, -7.97198000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q551048'),
(89056, 'Cabeça Gorda', 2230, '02', 177, 'PT', 37.92401000, -7.79290000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q551048'),
(89057, 'Cabouco', 2233, '20', 177, 'PT', 37.76667000, -25.56667000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1997739'),
(89058, 'Cacilhas', 2242, '15', 177, 'PT', 38.68638000, -9.14938000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789094'),
(89059, 'Cacém', 2228, '11', 177, 'PT', 38.76698000, -9.29793000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q34789102'),
(89060, 'Cadafais', 2228, '11', 177, 'PT', 39.00461000, -9.00419000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q687504'),
(89061, 'Cadaval', 2228, '11', 177, 'PT', 39.24621000, -9.06738000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q33661928'),
(89062, 'Cais do Pico', 2233, '20', 177, 'PT', 38.52531000, -28.32074000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q33661928'),
(89063, 'Caldas da Rainha', 2240, '10', 177, 'PT', 39.40326000, -9.13839000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q275689'),
(89064, 'Caldas das Taipas', 2244, '03', 177, 'PT', 41.48465000, -8.34850000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q49347629'),
(89065, 'Caldas de Vizela', 2244, '03', 177, 'PT', 41.38212000, -8.30890000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789245'),
(89066, 'Caldelas', 2244, '03', 177, 'PT', 41.67101000, -8.38148000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789250'),
(89067, 'Calendário', 2244, '03', 177, 'PT', 41.40361000, -8.52967000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q34789263'),
(89068, 'Calheta', 2231, '30', 177, 'PT', 32.71667000, -17.18333000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q757078'),
(89069, 'Calheta', 2233, '20', 177, 'PT', 38.60186000, -28.01792000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q757078'),
(89070, 'Calheta de São Jorge', 2233, '20', 177, 'PT', 38.59767000, -27.91459000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q1019047'),
(89071, 'Camacha', 2231, '30', 177, 'PT', 32.67919000, -16.84462000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789334'),
(89072, 'Camarate', 2228, '11', 177, 'PT', 38.80358000, -9.12809000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1028029'),
(89073, 'Caminha', 2245, '16', 177, 'PT', 41.84647000, -8.80133000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q1029544'),
(89074, 'Campanário', 2231, '30', 177, 'PT', 32.66578000, -17.03576000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q34789497'),
(89075, 'Campelos', 2228, '11', 177, 'PT', 39.19678000, -9.23519000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q582153'),
(89076, 'Campo', 2237, '18', 177, 'PT', 40.71086000, -7.91445000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789546'),
(89077, 'Campo', 2243, '13', 177, 'PT', 41.18516000, -8.46493000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789552'),
(89078, 'Campo Maior', 2232, '12', 177, 'PT', 39.02935000, -7.06479000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q995297'),
(89079, 'Campo de Besteiros', 2237, '18', 177, 'PT', 40.55655000, -8.13432000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q995297'),
(89080, 'Canas de Senhorim', 2237, '18', 177, 'PT', 40.50012000, -7.89874000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q995297'),
(89081, 'Candoso', 2244, '03', 177, 'PT', 41.42768000, -8.32166000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789683'),
(89082, 'Canelas', 2243, '13', 177, 'PT', 41.08333000, -8.60000000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789702'),
(89083, 'Caneças', 2228, '11', 177, 'PT', 38.81321000, -9.22679000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q34789729'),
(89084, 'Canhas', 2231, '30', 177, 'PT', 32.69465000, -17.09867000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789729'),
(89085, 'Canidelo', 2243, '13', 177, 'PT', 41.12314000, -8.64654000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q34789734'),
(89086, 'Caniçal', 2231, '30', 177, 'PT', 32.73834000, -16.73836000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q34789749'),
(89087, 'Caniço', 2231, '30', 177, 'PT', 32.65078000, -16.83749000, '2019-10-05 23:14:33', '2020-05-01 17:23:07', 1, 'Q34789749'),
(89088, 'Cantanhede', 2246, '06', 177, 'PT', 40.36354000, -8.60549000, '2019-10-05 23:14:33', '2019-10-05 23:14:33', 1, 'Q426945'),
(89089, 'Caparica', 2242, '15', 177, 'PT', 38.66179000, -9.20032000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q34789837'),
(89090, 'Caranguejeira', 2240, '10', 177, 'PT', 39.74619000, -8.70740000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1035405'),
(89091, 'Carapinheira', 2246, '06', 177, 'PT', 40.20620000, -8.64810000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1035405'),
(89092, 'Carcavelos', 2228, '11', 177, 'PT', 38.69105000, -9.32215000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q663893'),
(89093, 'Carnaxide', 2228, '11', 177, 'PT', 38.72706000, -9.24671000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q34790026'),
(89094, 'Carrazeda de Anciães', 2229, '04', 177, 'PT', 41.24247000, -7.30721000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q34790026'),
(89095, 'Carrazeda de Ansiães', 2229, '04', 177, 'PT', 41.23424000, -7.31129000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q551666'),
(89096, 'Carregado', 2228, '11', 177, 'PT', 39.02362000, -8.97692000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1044914'),
(89097, 'Carregal do Sal', 2237, '18', 177, 'PT', 40.45007000, -7.99819000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q796938'),
(89098, 'Cartaxo', 2238, '14', 177, 'PT', 39.15377000, -8.81373000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q841304'),
(89099, 'Carvalhosa', 2243, '13', 177, 'PT', 41.30057000, -8.36080000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q34790160'),
(89100, 'Carvoeiro', 2239, '08', 177, 'PT', 37.09736000, -8.46846000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1046217'),
(89101, 'Cascais', 2228, '11', 177, 'PT', 38.69681000, -9.42147000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q273059'),
(89102, 'Castanheira de Pêra', 2240, '10', 177, 'PT', 40.00717000, -8.21048000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1013140'),
(89103, 'Castanheira do Ribatejo', 2228, '11', 177, 'PT', 38.99298000, -8.97346000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1048511'),
(89104, 'Castelo Branco', 2241, '05', 177, 'PT', 39.82219000, -7.49087000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q12899232'),
(89105, 'Castelo Branco', 2233, '20', 177, 'PT', 38.52198000, -28.71365000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q12899232'),
(89106, 'Castelo de Paiva', 2235, '01', 177, 'PT', 41.03353000, -8.29822000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q397379'),
(89107, 'Castelo de Vide', 2232, '12', 177, 'PT', 39.41624000, -7.45680000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q34790334'),
(89108, 'Castelões de Cepeda', 2243, '13', 177, 'PT', 41.20265000, -8.33516000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1049053'),
(89109, 'Castro Daire', 2237, '18', 177, 'PT', 40.92279000, -7.93878000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q861199'),
(89110, 'Castro Marim', 2239, '08', 177, 'PT', 37.25865000, -7.50732000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q986079'),
(89111, 'Castro Verde', 2230, '02', 177, 'PT', 37.69828000, -8.08581000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q551696'),
(89112, 'Caxias', 2228, '11', 177, 'PT', 38.70314000, -9.27666000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q551696'),
(89113, 'Cela', 2240, '10', 177, 'PT', 39.54075000, -9.03449000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1052741'),
(89114, 'Celorico de Basto', 2244, '03', 177, 'PT', 41.40278000, -8.03346000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q551653'),
(89115, 'Cercal', 2242, '15', 177, 'PT', 37.80129000, -8.67400000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q34790997'),
(89116, 'Chamusca', 2238, '14', 177, 'PT', 39.29900000, -8.39933000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1000093'),
(89117, 'Charneca', 2228, '11', 177, 'PT', 38.78351000, -9.14348000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1000093'),
(89118, 'Charneca de Caparica', 2242, '15', 177, 'PT', 38.62032000, -9.19426000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q34792022'),
(89119, 'Chaves', 2234, '17', 177, 'PT', 41.73961000, -7.45030000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q504580'),
(89120, 'Chavão', 2244, '03', 177, 'PT', 41.45235000, -8.60194000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q504580'),
(89121, 'Cinfães', 2237, '18', 177, 'PT', 41.03986000, -8.11218000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q861464'),
(89122, 'Coimbra', 2246, '06', 177, 'PT', 40.20564000, -8.41955000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q3348861'),
(89123, 'Colares', 2228, '11', 177, 'PT', 38.79921000, -9.44691000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1107623'),
(89124, 'Comporta', 2242, '15', 177, 'PT', 38.38059000, -8.78608000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1107623'),
(89125, 'Conceição', 2239, '08', 177, 'PT', 37.14789000, -7.60426000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q540357'),
(89126, 'Condeixa-a-Nova', 2246, '06', 177, 'PT', 40.10639000, -8.49632000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q138830'),
(89127, 'Constância', 2238, '14', 177, 'PT', 39.45718000, -8.30368000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1003157'),
(89128, 'Corroios', 2242, '15', 177, 'PT', 38.64004000, -9.15080000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1135381'),
(89129, 'Cortegaça', 2235, '01', 177, 'PT', 40.94883000, -8.62130000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q34796743'),
(89130, 'Coruche', 2238, '14', 177, 'PT', 38.95950000, -8.52745000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1000052'),
(89131, 'Corvo', 2233, '20', 177, 'PT', 39.69771000, -31.10550000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1006836'),
(89132, 'Costa de Caparica', 2242, '15', 177, 'PT', 38.64458000, -9.23556000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q34796808'),
(89133, 'Covilhã', 2241, '05', 177, 'PT', 40.28106000, -7.50504000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q34797275'),
(89134, 'Crato', 2232, '12', 177, 'PT', 39.28657000, -7.64408000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1005138'),
(89135, 'Cristelo', 2244, '03', 177, 'PT', 41.47964000, -8.69785000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1005138'),
(89136, 'Cuba', 2230, '02', 177, 'PT', 38.21972000, -7.92114000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q552580'),
(89137, 'Cucujães', 2235, '01', 177, 'PT', 40.87413000, -8.50687000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1954238'),
(89138, 'Curral das Freiras', 2231, '30', 177, 'PT', 32.72029000, -16.96993000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q602368'),
(89139, 'Câmara de Lobos', 2231, '30', 177, 'PT', 32.65043000, -16.97718000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q34799706'),
(89141, 'Eixo', 2235, '01', 177, 'PT', 40.62758000, -8.56922000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q2257537'),
(89142, 'Elvas', 2232, '12', 177, 'PT', 38.88150000, -7.16282000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q243849'),
(89143, 'Entroncamento', 2238, '14', 177, 'PT', 39.46667000, -8.46667000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q990422'),
(89144, 'Ericeira', 2228, '11', 177, 'PT', 38.96275000, -9.41563000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1351868'),
(89145, 'Ermesinde', 2243, '13', 177, 'PT', 41.21653000, -8.55318000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1020811'),
(89146, 'Ervedosa do Douro', 2237, '18', 177, 'PT', 41.16626000, -7.47305000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q93629'),
(89147, 'Esgueira', 2235, '01', 177, 'PT', 40.64899000, -8.62943000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q93629'),
(89148, 'Esmoriz', 2235, '01', 177, 'PT', 40.95773000, -8.62753000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1020815'),
(89149, 'Espargo', 2235, '01', 177, 'PT', 40.92462000, -8.57495000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q2737655'),
(89150, 'Espinho', 2235, '01', 177, 'PT', 41.00763000, -8.64125000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q13225053'),
(89151, 'Esporões', 2244, '03', 177, 'PT', 41.51006000, -8.41729000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q13225053'),
(89152, 'Esposende', 2244, '03', 177, 'PT', 41.53610000, -8.78201000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49349843'),
(89153, 'Estarreja', 2235, '01', 177, 'PT', 40.75463000, -8.57917000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q381337'),
(89154, 'Estela', 2243, '13', 177, 'PT', 41.44944000, -8.75166000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q381337'),
(89155, 'Estoril', 2228, '11', 177, 'PT', 38.70571000, -9.39773000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q193305'),
(89156, 'Estreito da Calheta', 2231, '30', 177, 'PT', 32.73704000, -17.18674000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49349878'),
(89157, 'Estremoz', 2236, '07', 177, 'PT', 38.84996000, -7.60117000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q625157'),
(89158, 'Estói', 2239, '08', 177, 'PT', 37.09503000, -7.89445000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q625157'),
(89159, 'Estômbar', 2239, '08', 177, 'PT', 37.14629000, -8.48505000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q2453756'),
(89160, 'Fafe', 2244, '03', 177, 'PT', 41.46837000, -8.15863000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q551080'),
(89161, 'Faial', 2231, '30', 177, 'PT', 32.78333000, -16.85000000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q551080'),
(89162, 'Fajã da Ovelha', 2231, '30', 177, 'PT', 32.77457000, -17.23412000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q551080'),
(89163, 'Fajã de Baixo', 2233, '20', 177, 'PT', 37.75000000, -25.65000000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q49350011'),
(89164, 'Famalicão', 2240, '10', 177, 'PT', 39.53642000, -9.08308000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q49350011'),
(89165, 'Famões', 2228, '11', 177, 'PT', 38.78804000, -9.21033000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q49350043'),
(89166, 'Faria', 2244, '03', 177, 'PT', 41.48298000, -8.67152000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350043'),
(89167, 'Faro', 2239, '08', 177, 'PT', 37.01869000, -7.92716000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q159457'),
(89168, 'Favaios', 2234, '17', 177, 'PT', 41.26876000, -7.50465000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q168690'),
(89169, 'Fazendas de Almeirim', 2238, '14', 177, 'PT', 39.17553000, -8.56927000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1399148'),
(89170, 'Feira', 2235, '01', 177, 'PT', 40.92535000, -8.54277000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q2015920'),
(89171, 'Felgueiras', 2243, '13', 177, 'PT', 41.34774000, -8.20808000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q936904'),
(89172, 'Fenais da Ajuda', 2233, '20', 177, 'PT', 37.85128000, -25.32406000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q936904'),
(89173, 'Fenais da Luz', 2233, '20', 177, 'PT', 37.82490000, -25.64229000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q936904'),
(89174, 'Ferragudo', 2239, '08', 177, 'PT', 37.12474000, -8.51915000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1023960'),
(89175, 'Ferreira', 2243, '13', 177, 'PT', 41.26718000, -8.34434000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350135'),
(89176, 'Ferreira do Alentejo', 2230, '02', 177, 'PT', 38.05000000, -8.03333000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q2258063'),
(89177, 'Ferreira do Zêzere', 2238, '14', 177, 'PT', 39.72303000, -8.31661000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q995202'),
(89178, 'Ferreiras', 2239, '08', 177, 'PT', 37.12926000, -8.23759000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q995202'),
(89179, 'Ferreiros', 2244, '03', 177, 'PT', 41.35000000, -8.55000000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350137'),
(89180, 'Ferrel', 2240, '10', 177, 'PT', 39.36398000, -9.31541000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350137'),
(89181, 'Feteira', 2233, '20', 177, 'PT', 38.65429000, -27.14995000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350137'),
(89182, 'Figueira da Foz', 2246, '06', 177, 'PT', 40.15085000, -8.86179000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q428459'),
(89183, 'Figueiró', 2243, '13', 177, 'PT', 41.29922000, -8.16779000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q49350156'),
(89184, 'Figueiró Dos Vinhos', 2240, '10', 177, 'PT', 39.90617000, -8.27753000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q679642'),
(89185, 'Fiães', 2235, '01', 177, 'PT', 40.99446000, -8.52537000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1257752'),
(89186, 'Fontanelas', 2228, '11', 177, 'PT', 38.84806000, -9.43942000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q5465145'),
(89187, 'Fonte Bastardo', 2233, '20', 177, 'PT', 38.69201000, -27.07942000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q5465145'),
(89188, 'Foz do Arelho', 2240, '10', 177, 'PT', 39.43672000, -9.21374000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q429713'),
(89189, 'Foz do Douro', 2243, '13', 177, 'PT', 41.15119000, -8.67125000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1397371'),
(89190, 'Foz do Sousa', 2243, '13', 177, 'PT', 41.09668000, -8.50184000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350450'),
(89191, 'Fradelos', 2244, '03', 177, 'PT', 41.36846000, -8.59887000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350450'),
(89192, 'Frazão', 2243, '13', 177, 'PT', 41.25866000, -8.40014000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1452112'),
(89193, 'Freamunde', 2243, '13', 177, 'PT', 41.28835000, -8.33533000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1020833'),
(89194, 'Freixo de Espada à Cinta', 2229, '04', 177, 'PT', 41.09033000, -6.80648000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q551684'),
(89195, 'Fronteira', 2232, '12', 177, 'PT', 39.07179000, -7.61052000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q647467'),
(89196, 'Frossos', 2244, '03', 177, 'PT', 41.56590000, -8.45134000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q647467'),
(89197, 'Funchal', 2231, '30', 177, 'PT', 32.66568000, -16.92547000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q25444'),
(89198, 'Fundão', 2241, '05', 177, 'PT', 40.12412000, -7.49542000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1024927'),
(89199, 'Furnas', 2233, '20', 177, 'PT', 37.77567000, -25.31035000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350690'),
(89200, 'Fuzeta', 2239, '08', 177, 'PT', 37.05429000, -7.74699000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q2652799'),
(89201, 'Fátima', 2238, '14', 177, 'PT', 39.62071000, -8.65237000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q49350714'),
(89202, 'Fânzeres', 2243, '13', 177, 'PT', 41.16754000, -8.52981000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1478690'),
(89203, 'Gafanha da Encarnação', 2235, '01', 177, 'PT', 40.61806000, -8.73303000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q49350760'),
(89204, 'Gafanha da Nazaré', 2235, '01', 177, 'PT', 40.63621000, -8.71338000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1020821'),
(89205, 'Galegos', 2244, '03', 177, 'PT', 41.56268000, -8.57204000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49350805'),
(89206, 'Gandra', 2243, '13', 177, 'PT', 41.20116000, -8.43376000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1020848'),
(89207, 'Gandra', 2244, '03', 177, 'PT', 41.52185000, -8.76185000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1023200'),
(89208, 'Gavião', 2232, '12', 177, 'PT', 39.44462000, -7.89532000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1005090'),
(89209, 'Gemunde', 2243, '13', 177, 'PT', 41.26766000, -8.64515000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49351116'),
(89210, 'Godim', 2234, '17', 177, 'PT', 41.17104000, -7.80303000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49351560'),
(89211, 'Golegã', 2238, '14', 177, 'PT', 39.38683000, -8.50591000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1012522'),
(89212, 'Gondizalves', 2244, '03', 177, 'PT', 41.54158000, -8.45570000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1012522'),
(89213, 'Gondomar', 2243, '13', 177, 'PT', 41.12015000, -8.49595000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q983483'),
(89214, 'Grijó', 2243, '13', 177, 'PT', 41.02836000, -8.58017000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1546770'),
(89215, 'Grândola', 2242, '15', 177, 'PT', 38.18999000, -8.61197000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q986019'),
(89216, 'Gueral', 2244, '03', 177, 'PT', 41.45859000, -8.62946000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q986019'),
(89217, 'Guia', 2239, '08', 177, 'PT', 37.12959000, -8.29963000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q986019'),
(89218, 'Guifões', 2243, '13', 177, 'PT', 41.19748000, -8.66899000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q49352679'),
(89219, 'Guimarães', 2244, '03', 177, 'PT', 41.44384000, -8.28918000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q170903'),
(89220, 'Guisande', 2244, '03', 177, 'PT', 41.48264000, -8.44564000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q170903'),
(89221, 'Gulpilhares', 2243, '13', 177, 'PT', 41.08292000, -8.62679000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1265336'),
(89222, 'Góis', 2246, '06', 177, 'PT', 40.12535000, -8.08340000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q425410'),
(89223, 'Horta', 2233, '20', 177, 'PT', 38.53737000, -28.62615000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49354018'),
(89224, 'Idanha-A-Nova', 2241, '05', 177, 'PT', 39.92957000, -7.23690000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q999446'),
(89225, 'Joane', 2244, '03', 177, 'PT', 41.43906000, -8.40846000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1690801'),
(89226, 'Jovim', 2243, '13', 177, 'PT', 41.11102000, -8.51903000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49355583'),
(89227, 'Lago', 2244, '03', 177, 'PT', 41.62148000, -8.41258000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q49355583'),
(89228, 'Lagoa', 2233, '20', 177, 'PT', 37.74486000, -25.57184000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q564759'),
(89229, 'Lagoa', 2239, '08', 177, 'PT', 37.10505000, -8.45974000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q985519'),
(89230, 'Lagos', 2239, '08', 177, 'PT', 37.10202000, -8.67422000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35710384'),
(89231, 'Lajes', 2233, '20', 177, 'PT', 38.76352000, -27.10336000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q2652876'),
(89232, 'Lajes das Flores', 2233, '20', 177, 'PT', 39.41619000, -31.21725000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1021452'),
(89233, 'Lajes do Pico', 2233, '20', 177, 'PT', 38.42797000, -28.17402000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1019053'),
(89234, 'Lamas', 2235, '01', 177, 'PT', 40.98597000, -8.56923000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35711021'),
(89235, 'Lamego', 2237, '18', 177, 'PT', 41.10229000, -7.81025000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q117591'),
(89236, 'Laranjeira', 2242, '15', 177, 'PT', 38.65599000, -9.15376000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q117591'),
(89237, 'Laranjeiro', 2239, '08', 177, 'PT', 37.06799000, -7.80780000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35711970'),
(89238, 'Lavos', 2246, '06', 177, 'PT', 40.09363000, -8.82826000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35712820'),
(89239, 'Lavra', 2243, '13', 177, 'PT', 41.25935000, -8.71849000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35712828'),
(89240, 'Lavradio', 2242, '15', 177, 'PT', 38.66808000, -9.05204000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35712828'),
(89241, 'Leiria', 2240, '10', 177, 'PT', 39.74362000, -8.80705000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q3348464'),
(89242, 'Leça da Palmeira', 2243, '13', 177, 'PT', 41.19100000, -8.70027000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q35716314'),
(89243, 'Leça do Bailio', 2243, '13', 177, 'PT', 41.21201000, -8.63422000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q35716323'),
(89244, 'Linda-a-Velha', 2228, '11', 177, 'PT', 38.71446000, -9.24220000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1013294'),
(89245, 'Lisbon', 2228, '11', 177, 'PT', 38.72635000, -9.14843000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q33690701'),
(89246, 'Lobão', 2235, '01', 177, 'PT', 40.98664000, -8.48566000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q35718617'),
(89247, 'Lordelo', 2243, '13', 177, 'PT', 41.23451000, -8.40297000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35719316'),
(89248, 'Lordelo', 2244, '03', 177, 'PT', 41.37411000, -8.38016000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q994626'),
(89249, 'Lorvão', 2246, '06', 177, 'PT', 40.25938000, -8.31683000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1454968'),
(89250, 'Loulé', 2239, '08', 177, 'PT', 37.14399000, -8.02345000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q586722'),
(89251, 'Loures', 2228, '11', 177, 'PT', 38.83091000, -9.16845000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35719701'),
(89252, 'Lourinhã', 2228, '11', 177, 'PT', 39.24745000, -9.31194000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q986017'),
(89253, 'Louriçal', 2240, '10', 177, 'PT', 40.00390000, -8.73736000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1872318'),
(89254, 'Lourosa', 2235, '01', 177, 'PT', 40.98420000, -8.55142000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1872343'),
(89255, 'Lousada', 2243, '13', 177, 'PT', 41.28355000, -8.27437000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q986741'),
(89256, 'Lousã', 2246, '06', 177, 'PT', 40.11673000, -8.24921000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q35719737'),
(89257, 'Luso', 2235, '01', 177, 'PT', 40.38429000, -8.37845000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1024828'),
(89258, 'Lustosa', 2243, '13', 177, 'PT', 41.34081000, -8.31715000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35721286'),
(89259, 'Luz', 2239, '08', 177, 'PT', 37.09216000, -7.70433000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35721498'),
(89260, 'Macedo de Cavaleiros', 2229, '04', 177, 'PT', 41.55132000, -6.93355000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q551693'),
(89261, 'Maceira', 2240, '10', 177, 'PT', 39.68853000, -8.89423000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35722040'),
(89262, 'Machico', 2231, '30', 177, 'PT', 32.71620000, -16.76758000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35722090'),
(89263, 'Macieira de Cambra', 2235, '01', 177, 'PT', 40.85939000, -8.37338000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35722148'),
(89264, 'Macieira de Rates', 2244, '03', 177, 'PT', 41.44092000, -8.63426000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35722148'),
(89265, 'Madalena', 2238, '14', 177, 'PT', 39.57133000, -8.44635000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q579519'),
(89266, 'Madalena', 2243, '13', 177, 'PT', 41.21616000, -8.33451000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35722279'),
(89267, 'Madalena', 2233, '20', 177, 'PT', 38.53642000, -28.52660000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1002565'),
(89268, 'Mafra', 2228, '11', 177, 'PT', 38.94107000, -9.32636000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q986419'),
(89269, 'Maia', 2233, '20', 177, 'PT', 37.83247000, -25.38976000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q986419'),
(89270, 'Maia', 2243, '13', 177, 'PT', 41.24254000, -8.60257000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q990403'),
(89271, 'Malveira', 2228, '11', 177, 'PT', 38.93213000, -9.25779000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35723684'),
(89272, 'Mangualde', 2237, '18', 177, 'PT', 40.60425000, -7.76115000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q597789'),
(89273, 'Manta Rota', 2239, '08', 177, 'PT', 37.16849000, -7.51804000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q10323868'),
(89274, 'Marco de Canaveses', 2243, '13', 177, 'PT', 41.15545000, -8.16954000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q188050'),
(89275, 'Marco de Canavezes', 2243, '13', 177, 'PT', 41.18389000, -8.14864000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q188050'),
(89276, 'Margaride', 2243, '13', 177, 'PT', 41.36478000, -8.19999000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35724678'),
(89277, 'Marinha Grande', 2240, '10', 177, 'PT', 39.77681000, -8.95005000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q289852'),
(89278, 'Marinhais', 2238, '14', 177, 'PT', 39.04730000, -8.70236000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q1898548'),
(89279, 'Marinhas', 2244, '03', 177, 'PT', 41.55906000, -8.78297000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35724951'),
(89280, 'Mariz', 2244, '03', 177, 'PT', 41.52823000, -8.67081000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35724951'),
(89281, 'Marmelete', 2239, '08', 177, 'PT', 37.31014000, -8.66813000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35724951'),
(89282, 'Marvão', 2232, '12', 177, 'PT', 39.39377000, -7.37663000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q35725662'),
(89283, 'Massamá', 2228, '11', 177, 'PT', 38.75279000, -9.28110000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q35725662'),
(89284, 'Matosinhos', 2243, '13', 177, 'PT', 41.18207000, -8.68908000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q3349324'),
(89285, 'Mação', 2238, '14', 177, 'PT', 39.59331000, -7.99772000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q1012723'),
(89287, 'Mealhada', 2235, '01', 177, 'PT', 40.36273000, -8.44781000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q381340'),
(89288, 'Meca', 2228, '11', 177, 'PT', 39.08178000, -9.03459000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q381340'),
(89289, 'Meinedo', 2243, '13', 177, 'PT', 41.24827000, -8.25807000, '2019-10-05 23:14:34', '2019-10-05 23:14:34', 1, 'Q35727654'),
(89290, 'Melgaço', 2245, '16', 177, 'PT', 42.08067000, -8.24844000, '2019-10-05 23:14:34', '2020-05-01 17:23:07', 1, 'Q491888'),
(89291, 'Melres', 2243, '13', 177, 'PT', 41.06989000, -8.40091000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1432562'),
(89292, 'Mem Martins', 2228, '11', 177, 'PT', 38.79443000, -9.34284000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1432562'),
(89293, 'Merelim', 2244, '03', 177, 'PT', 41.58568000, -8.46555000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1432562'),
(89294, 'Mesão Frio', 2234, '17', 177, 'PT', 41.16190000, -7.87284000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1006385'),
(89295, 'Mexilhoeira Grande', 2239, '08', 177, 'PT', 37.15858000, -8.61487000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1006385'),
(89296, 'Milharado', 2228, '11', 177, 'PT', 38.94732000, -9.19914000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35729849'),
(89297, 'Milheirós', 2243, '13', 177, 'PT', 41.21478000, -8.58837000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q35729856'),
(89298, 'Milheirós de Poiares', 2235, '01', 177, 'PT', 40.92163000, -8.46788000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q35729864'),
(89299, 'Minas de São Domingos', 2230, '02', 177, 'PT', 37.67322000, -7.49765000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1518830'),
(89300, 'Minde', 2238, '14', 177, 'PT', 39.51631000, -8.68799000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1717071'),
(89301, 'Mindelo', 2243, '13', 177, 'PT', 41.31527000, -8.72124000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1717071'),
(89302, 'Mira', 2240, '10', 177, 'PT', 39.54315000, -8.71505000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1717071'),
(89303, 'Mira', 2246, '06', 177, 'PT', 40.44559000, -8.73849000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1006418'),
(89304, 'Miranda do Corvo', 2246, '06', 177, 'PT', 40.09318000, -8.33261000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q370187'),
(89305, 'Miranda do Douro', 2229, '04', 177, 'PT', 41.49692000, -6.27308000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q373475'),
(89306, 'Mirandela', 2229, '04', 177, 'PT', 41.50098000, -7.19185000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q551502'),
(89307, 'Mogadouro', 2229, '04', 177, 'PT', 41.34034000, -6.71187000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q551680'),
(89308, 'Moimenta da Beira', 2237, '18', 177, 'PT', 40.97134000, -7.62959000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q862822'),
(89309, 'Moita', 2242, '15', 177, 'PT', 38.65854000, -9.01040000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q933912'),
(89310, 'Moita dos Ferreiros', 2228, '11', 177, 'PT', 39.24825000, -9.22355000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q933912'),
(89311, 'Moncarapacho', 2239, '08', 177, 'PT', 37.08360000, -7.78763000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35732069'),
(89312, 'Monchique', 2239, '08', 177, 'PT', 37.31664000, -8.58340000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q957663'),
(89313, 'Mondim de Basto', 2234, '17', 177, 'PT', 41.41347000, -7.95479000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q932771'),
(89314, 'Monforte', 2232, '12', 177, 'PT', 39.04960000, -7.44428000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q769062'),
(89315, 'Monsanto', 2238, '14', 177, 'PT', 39.46203000, -8.71180000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35732321'),
(89316, 'Montalegre', 2234, '17', 177, 'PT', 41.83180000, -7.78999000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q757860'),
(89317, 'Montalvinho', 2238, '14', 177, 'PT', 39.48333000, -8.30000000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q757860'),
(89318, 'Montalvo', 2238, '14', 177, 'PT', 39.48333000, -8.30000000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q757860'),
(89319, 'Montargil', 2232, '12', 177, 'PT', 39.07771000, -8.17044000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q757860'),
(89320, 'Monte Estoril', 2228, '11', 177, 'PT', 38.70636000, -9.40595000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q6022399'),
(89321, 'Monte Gordo', 2239, '08', 177, 'PT', 37.18192000, -7.45225000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35732663'),
(89322, 'Monte Real', 2240, '10', 177, 'PT', 39.85210000, -8.86349000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1945916'),
(89323, 'Monte Redondo', 2240, '10', 177, 'PT', 39.89930000, -8.83171000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35732679'),
(89324, 'Montemor-o-Novo', 2236, '07', 177, 'PT', 38.67103000, -8.29956000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q538407'),
(89325, 'Montemor-o-Velho', 2246, '06', 177, 'PT', 40.19385000, -8.66696000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q538407'),
(89326, 'Montijo', 2242, '15', 177, 'PT', 38.72990000, -8.69364000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q826511'),
(89327, 'Monção', 2245, '16', 177, 'PT', 42.07892000, -8.48076000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q35733554'),
(89328, 'Mora', 2236, '07', 177, 'PT', 38.92174000, -8.09972000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q174800'),
(89329, 'Moreira', 2243, '13', 177, 'PT', 41.05600000, -8.38939000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35733707'),
(89330, 'Moreira de Conegos', 2244, '03', 177, 'PT', 41.38680000, -8.33940000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1947515'),
(89331, 'Mortágua', 2237, '18', 177, 'PT', 40.39434000, -8.25811000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q861592'),
(89332, 'Moscavide e Portela', 2228, '11', 177, 'PT', 38.77929000, -9.10222000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35734150'),
(89333, 'Mosteiros', 2233, '20', 177, 'PT', 37.89017000, -25.81999000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q35734150'),
(89334, 'Mosteirô', 2235, '01', 177, 'PT', 40.89843000, -8.53196000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q35734150'),
(89335, 'Moura', 2230, '02', 177, 'PT', 38.14010000, -7.44856000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q551662'),
(89336, 'Mourão', 2236, '07', 177, 'PT', 38.29778000, -7.22230000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1005121'),
(89338, 'Murtosa', 2235, '01', 177, 'PT', 40.74301000, -8.64023000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2142313'),
(89339, 'Murça', 2234, '17', 177, 'PT', 41.41825000, -7.45121000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q513049'),
(89340, 'Mértola', 2230, '02', 177, 'PT', 37.65918000, -7.66434000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q551056'),
(89341, 'Nadadouro', 2240, '10', 177, 'PT', 39.41927000, -9.19091000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q551056'),
(89342, 'Nazaré', 2240, '10', 177, 'PT', 39.59965000, -9.07162000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q497179'),
(89343, 'Negreiros', 2244, '03', 177, 'PT', 41.43552000, -8.61398000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q497179'),
(89344, 'Negrelos', 2243, '13', 177, 'PT', 41.34946000, -8.40145000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1600659'),
(89345, 'Nelas', 2237, '18', 177, 'PT', 40.53216000, -7.85152000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q687790'),
(89346, 'Neves', 2230, '02', 177, 'PT', 38.02270000, -7.81344000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q687790'),
(89347, 'Nisa', 2232, '12', 177, 'PT', 39.51828000, -7.67496000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1013628'),
(89348, 'Nogueira', 2243, '13', 177, 'PT', 41.24246000, -8.58685000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358045'),
(89349, 'Nogueira da Regedoura', 2235, '01', 177, 'PT', 41.00530000, -8.59195000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358048'),
(89350, 'Nordeste', 2233, '20', 177, 'PT', 37.82721000, -25.22863000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q564762'),
(89351, 'Nossa Senhora do Monte', 2231, '30', 177, 'PT', 32.66667000, -16.90000000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q564762'),
(89352, 'Odemira', 2230, '02', 177, 'PT', 37.59798000, -8.63972000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358759'),
(89353, 'Odivelas', 2228, '11', 177, 'PT', 38.79269000, -9.18380000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q850837'),
(89354, 'Oeiras', 2228, '11', 177, 'PT', 38.71371000, -9.26832000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q926700'),
(89355, 'Oiã', 2235, '01', 177, 'PT', 40.54264000, -8.53856000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49358832'),
(89356, 'Oleiros', 2241, '05', 177, 'PT', 39.94948000, -7.88592000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1012719'),
(89357, 'Olhos de Água', 2239, '08', 177, 'PT', 37.09024000, -8.19168000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1344764'),
(89358, 'Olhão', 2239, '08', 177, 'PT', 37.04509000, -7.81032000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q740845'),
(89359, 'Olivais', 2228, '11', 177, 'PT', 38.76994000, -9.10674000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q740845'),
(89360, 'Olival', 2243, '13', 177, 'PT', 41.05024000, -8.54416000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358931'),
(89361, 'Olival do Basto', 2228, '11', 177, 'PT', 38.79079000, -9.16621000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358934'),
(89362, 'Oliveira', 2244, '03', 177, 'PT', 41.47819000, -8.46965000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358935'),
(89363, 'Oliveira de Azemeis', 2235, '01', 177, 'PT', 40.84101000, -8.47555000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358935'),
(89365, 'Oliveira de Frades', 2237, '18', 177, 'PT', 40.73357000, -8.17481000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q861584'),
(89366, 'Oliveira do Bairro', 2235, '01', 177, 'PT', 40.50977000, -8.56374000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q405241'),
(89367, 'Oliveira do Douro', 2243, '13', 177, 'PT', 41.12466000, -8.58463000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358938'),
(89368, 'Oliveira do Hospital', 2246, '06', 177, 'PT', 40.35522000, -7.86481000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q990381'),
(89369, 'Oliveira do Mondego', 2246, '06', 177, 'PT', 40.32305000, -8.22367000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q990381'),
(89370, 'Oliveirinha', 2235, '01', 177, 'PT', 40.60715000, -8.59198000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358940'),
(89371, 'Ota', 2228, '11', 177, 'PT', 39.11199000, -8.99105000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49358940'),
(89372, 'Ourique', 2230, '02', 177, 'PT', 37.69156000, -8.31031000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q519052'),
(89373, 'Ourém', 2238, '14', 177, 'PT', 39.66179000, -8.57895000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q771546'),
(89374, 'Ovar', 2235, '01', 177, 'PT', 40.85862000, -8.62513000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49359643'),
(89375, 'Paderne', 2239, '08', 177, 'PT', 37.17935000, -8.20150000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49359643'),
(89376, 'Paialvo', 2238, '14', 177, 'PT', 39.56399000, -8.46835000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49359643'),
(89377, 'Palmela', 2242, '15', 177, 'PT', 38.56902000, -8.90126000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q850855'),
(89378, 'Pampilhosa da Serra', 2246, '06', 177, 'PT', 40.04620000, -7.95182000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1000099'),
(89379, 'Pampilhosa do Botão', 2235, '01', 177, 'PT', 40.33580000, -8.42738000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49359892'),
(89380, 'Parchal', 2239, '08', 177, 'PT', 37.13827000, -8.51703000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2341999'),
(89381, 'Pardilhó', 2235, '01', 177, 'PT', 40.79925000, -8.62598000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49360003'),
(89382, 'Parede', 2228, '11', 177, 'PT', 38.69282000, -9.35412000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2052148'),
(89383, 'Paredes', 2243, '13', 177, 'PT', 41.20501000, -8.37506000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q631788'),
(89384, 'Paredes de Coura', 2245, '16', 177, 'PT', 41.90995000, -8.57034000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q489105'),
(89385, 'Pataias', 2240, '10', 177, 'PT', 39.66978000, -8.99580000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49360154'),
(89386, 'Paço de Arcos', 2228, '11', 177, 'PT', 38.69569000, -9.29143000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q2065632'),
(89387, 'Paços de Brandão', 2235, '01', 177, 'PT', 40.97541000, -8.58350000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q4346748'),
(89388, 'Paços de Ferreira', 2243, '13', 177, 'PT', 41.28964000, -8.37584000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q946398'),
(89389, 'Pedroso', 2243, '13', 177, 'PT', 41.41103000, -8.74897000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49360292'),
(89390, 'Pedrouços', 2243, '13', 177, 'PT', 41.18880000, -8.58624000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49360293'),
(89391, 'Pedrógão Grande', 2240, '10', 177, 'PT', 39.92682000, -8.20093000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1013094'),
(89392, 'Pego', 2238, '14', 177, 'PT', 39.46146000, -8.14955000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1013094'),
(89393, 'Penacova', 2246, '06', 177, 'PT', 40.26884000, -8.28237000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1005038'),
(89394, 'Penafiel', 2243, '13', 177, 'PT', 41.20931000, -8.26996000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q984656'),
(89395, 'Penalva do Castelo', 2237, '18', 177, 'PT', 40.66791000, -7.66015000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q780374'),
(89396, 'Penamacor', 2241, '05', 177, 'PT', 40.15495000, -7.17149000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q995183'),
(89397, 'Penedo Gordo', 2230, '02', 177, 'PT', 37.98477000, -7.91651000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q995183'),
(89398, 'Penedono', 2237, '18', 177, 'PT', 40.98875000, -7.39386000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q861589'),
(89399, 'Penela', 2246, '06', 177, 'PT', 40.03333000, -8.38333000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q576924'),
(89400, 'Peniche', 2240, '10', 177, 'PT', 39.35580000, -9.38112000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49360358'),
(89401, 'Perafita', 2243, '13', 177, 'PT', 41.18252000, -8.25450000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q40234338'),
(89402, 'Perelhal', 2244, '03', 177, 'PT', 41.53075000, -8.68982000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q40234338'),
(89403, 'Pero Pinheiro', 2228, '11', 177, 'PT', 38.85783000, -9.32352000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49360424'),
(89404, 'Perozinho', 2243, '13', 177, 'PT', 41.06513000, -8.58531000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2071644'),
(89405, 'Peso da Régua', 2234, '17', 177, 'PT', 41.19157000, -7.83668000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q757858'),
(89406, 'Piedade', 2242, '15', 177, 'PT', 38.67005000, -9.15852000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1137973'),
(89407, 'Pinhal Novo', 2242, '15', 177, 'PT', 38.63106000, -8.91376000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q179045'),
(89408, 'Pinheiro Grande', 2238, '14', 177, 'PT', 39.39212000, -8.43410000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q179045'),
(89409, 'Poceirão', 2238, '14', 177, 'PT', 38.83095000, -8.79365000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49361444'),
(89410, 'Poiares', 2246, '06', 177, 'PT', 40.21026000, -8.25746000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49361444'),
(89411, 'Pombal', 2240, '10', 177, 'PT', 39.90735000, -8.66949000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q966825'),
(89412, 'Ponta Delgada', 2233, '20', 177, 'PT', 37.74230000, -25.67093000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q208149'),
(89413, 'Ponta Garça', 2233, '20', 177, 'PT', 37.71667000, -25.36667000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q208149'),
(89414, 'Ponta do Sol', 2231, '30', 177, 'PT', 32.67980000, -17.10000000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49361861'),
(89415, 'Ponte', 2244, '03', 177, 'PT', 41.47057000, -8.32990000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49361870'),
(89416, 'Ponte da Barca', 2245, '16', 177, 'PT', 41.80669000, -8.31014000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q491484'),
(89417, 'Ponte de Lima', 2245, '16', 177, 'PT', 41.74682000, -8.57637000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q490180'),
(89418, 'Ponte de Sor', 2232, '12', 177, 'PT', 39.21441000, -8.05420000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q990365'),
(89419, 'Pontinha', 2228, '11', 177, 'PT', 38.76771000, -9.19935000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2103898'),
(89420, 'Pontével', 2238, '14', 177, 'PT', 39.14945000, -8.83880000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49361889'),
(89421, 'Porches', 2239, '08', 177, 'PT', 37.12665000, -8.40162000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2632134'),
(89422, 'Portalegre', 2232, '12', 177, 'PT', 39.29740000, -7.41538000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q622819'),
(89423, 'Portel', 2236, '07', 177, 'PT', 38.29385000, -7.72762000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q591694'),
(89424, 'Portimão', 2239, '08', 177, 'PT', 37.17544000, -8.58420000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q543542'),
(89425, 'Porto', 2243, '13', 177, 'PT', 41.14961000, -8.61099000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q33707919'),
(89426, 'Porto Covo', 2242, '15', 177, 'PT', 37.85256000, -8.79018000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49362017'),
(89427, 'Porto Judeu', 2233, '20', 177, 'PT', 38.64814000, -27.11944000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1999450'),
(89428, 'Porto Martins', 2233, '20', 177, 'PT', 38.68146000, -27.05835000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1999450'),
(89429, 'Porto Moniz', 2231, '30', 177, 'PT', 32.86681000, -17.16667000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49362020'),
(89430, 'Porto Salvo', 2228, '11', 177, 'PT', 38.72293000, -9.30473000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2093255'),
(89431, 'Porto Santo', 2231, '30', 177, 'PT', 33.07145000, -16.34304000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q27320'),
(89432, 'Porto da Cruz', 2231, '30', 177, 'PT', 32.76667000, -16.83333000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q932115'),
(89433, 'Porto de Mós', 2240, '10', 177, 'PT', 39.60191000, -8.81839000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1012710'),
(89434, 'Povoação', 2233, '20', 177, 'PT', 37.76471000, -25.24487000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q565726'),
(89435, 'Prado', 2244, '03', 177, 'PT', 41.60246000, -8.46297000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49362263'),
(89436, 'Pragal', 2242, '15', 177, 'PT', 38.67459000, -9.16981000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2035294'),
(89437, 'Praia da Vitória', 2233, '20', 177, 'PT', 38.73333000, -27.06667000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49362272'),
(89438, 'Praia do Ribatejo', 2238, '14', 177, 'PT', 39.46667000, -8.35000000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49362272'),
(89439, 'Prior Velho', 2228, '11', 177, 'PT', 38.79174000, -9.12119000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1024836'),
(89440, 'Proença-a-Nova', 2241, '05', 177, 'PT', 39.75700000, -7.92595000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1003172'),
(89441, 'Pêra', 2239, '08', 177, 'PT', 37.12296000, -8.34115000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q2991238'),
(89442, 'Póvoa de Lanhoso', 2244, '03', 177, 'PT', 41.57599000, -8.27008000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49362874'),
(89443, 'Póvoa de Santa Iria', 2228, '11', 177, 'PT', 38.86101000, -9.06453000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49362876'),
(89444, 'Póvoa de Santo Adrião', 2228, '11', 177, 'PT', 38.80000000, -9.16667000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q49362878'),
(89445, 'Póvoa de Varzim', 2243, '13', 177, 'PT', 41.38344000, -8.76364000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q200459'),
(89446, 'Quarteira', 2239, '08', 177, 'PT', 37.06946000, -8.10064000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1020826'),
(89447, 'Queijas', 2228, '11', 177, 'PT', 38.71925000, -9.26255000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49363043'),
(89448, 'Queluz', 2228, '11', 177, 'PT', 38.75657000, -9.25451000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1003060'),
(89449, 'Quinta Do Conde', 2242, '15', 177, 'PT', 38.56528000, -9.04316000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1003060'),
(89450, 'Quinta do Anjo', 2242, '15', 177, 'PT', 38.56753000, -8.94228000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49363085'),
(89451, 'Rabo de Peixe', 2233, '20', 177, 'PT', 37.81022000, -25.58263000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2652576'),
(89452, 'Ramada', 2228, '11', 177, 'PT', 38.80368000, -9.18770000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49363374'),
(89453, 'Real', 2244, '03', 177, 'PT', 41.55841000, -8.44330000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49363374'),
(89454, 'Rebordosa', 2243, '13', 177, 'PT', 41.22405000, -8.40669000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1023518'),
(89455, 'Rebordões', 2243, '13', 177, 'PT', 41.35509000, -8.42355000, '2019-10-05 23:14:35', '2020-05-01 17:23:07', 1, 'Q1696865'),
(89456, 'Recarei', 2243, '13', 177, 'PT', 41.15356000, -8.41178000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q2135181'),
(89457, 'Redondo', 2236, '07', 177, 'PT', 38.58010000, -7.59659000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q997608'),
(89458, 'Refojos de Basto', 2244, '03', 177, 'PT', 41.51318000, -7.99517000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q49363569'),
(89459, 'Reguengos de Monsaraz', 2236, '07', 177, 'PT', 38.42529000, -7.53494000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q990439'),
(89460, 'Relva', 2233, '20', 177, 'PT', 37.75271000, -25.71848000, '2019-10-05 23:14:35', '2019-10-05 23:14:35', 1, 'Q1997694'),
(89461, 'Rendufe', 2244, '03', 177, 'PT', 41.62898000, -8.40858000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1997694'),
(89462, 'Resende', 2237, '18', 177, 'PT', 41.07688000, -7.95123000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q863692'),
(89463, 'Riachos', 2238, '14', 177, 'PT', 39.44472000, -8.51420000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49363702'),
(89464, 'Riba de Ave', 2244, '03', 177, 'PT', 41.39648000, -8.38685000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q2023873'),
(89465, 'Ribeira Brava', 2231, '30', 177, 'PT', 32.67483000, -17.06288000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49363709'),
(89466, 'Ribeira Grande', 2233, '20', 177, 'PT', 38.51667000, -28.70000000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49363712'),
(89467, 'Ribeira Seca', 2233, '20', 177, 'PT', 37.81667000, -25.53333000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49363712'),
(89468, 'Ribeira de Pena', 2234, '17', 177, 'PT', 41.51021000, -7.79673000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1006378'),
(89469, 'Ribeirinha', 2233, '20', 177, 'PT', 38.66203000, -27.18093000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1006378'),
(89470, 'Ribeirão', 2244, '03', 177, 'PT', 41.36081000, -8.56774000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49363713'),
(89471, 'Rio Maior', 2238, '14', 177, 'PT', 39.34288000, -8.93806000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q941958'),
(89472, 'Rio Mau', 2243, '13', 177, 'PT', 41.40422000, -8.67994000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q941958'),
(89473, 'Rio Meão', 2235, '01', 177, 'PT', 40.95775000, -8.57818000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49363788'),
(89474, 'Rio Tinto', 2243, '13', 177, 'PT', 41.17872000, -8.55953000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1020836'),
(89475, 'Rio de Loba', 2237, '18', 177, 'PT', 40.66596000, -7.87781000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49363784'),
(89476, 'Rio de Mouro', 2228, '11', 177, 'PT', 38.76613000, -9.32804000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1631953'),
(89477, 'Ronfe', 2244, '03', 177, 'PT', 41.44146000, -8.38412000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49364106'),
(89478, 'Rosairinho', 2242, '15', 177, 'PT', 38.67734000, -9.00836000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49364106'),
(89479, 'Rosto de Cão', 2233, '20', 177, 'PT', 37.75000000, -25.65000000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49364193'),
(89480, 'Sabrosa', 2234, '17', 177, 'PT', 41.26702000, -7.57601000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1006389'),
(89481, 'Sacavém', 2228, '11', 177, 'PT', 38.79202000, -9.10801000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q126681'),
(89482, 'Sagres', 2239, '08', 177, 'PT', 37.00864000, -8.94311000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49364942'),
(89483, 'Salir de Matos', 2240, '10', 177, 'PT', 39.43186000, -9.09479000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49364942'),
(89484, 'Salreu', 2235, '01', 177, 'PT', 40.73971000, -8.55720000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366795'),
(89485, 'Salvada', 2230, '02', 177, 'PT', 37.93771000, -7.77448000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366795'),
(89486, 'Salvaterra de Magos', 2238, '14', 177, 'PT', 39.04482000, -8.68903000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1027809'),
(89487, 'Samil', 2229, '04', 177, 'PT', 41.77632000, -6.75698000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1027809'),
(89488, 'Samora Correia', 2238, '14', 177, 'PT', 38.93709000, -8.87178000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366838'),
(89489, 'Samouco', 2242, '15', 177, 'PT', 38.72035000, -9.00471000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366838'),
(89490, 'Sande', 2244, '03', 177, 'PT', 41.70190000, -8.39246000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366935'),
(89491, 'Sandim', 2243, '13', 177, 'PT', 41.03508000, -8.50700000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366944'),
(89492, 'Sanfins', 2235, '01', 177, 'PT', 40.92979000, -8.52563000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366944'),
(89493, 'Sangalhos', 2235, '01', 177, 'PT', 40.48678000, -8.46968000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q2233176'),
(89494, 'Santa Bárbara', 2233, '20', 177, 'PT', 38.69608000, -27.33907000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q2233176'),
(89495, 'Santa Bárbara de Nexe', 2239, '08', 177, 'PT', 37.10619000, -7.96648000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q2427281'),
(89496, 'Santa Catarina da Serra', 2240, '10', 177, 'PT', 39.67960000, -8.68679000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366995'),
(89497, 'Santa Clara', 2246, '06', 177, 'PT', 40.19985000, -8.44018000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366995'),
(89498, 'Santa Comba Dão', 2237, '18', 177, 'PT', 40.40442000, -8.11380000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q861208'),
(89499, 'Santa Cruz', 2231, '30', 177, 'PT', 32.68806000, -16.79388000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366997'),
(89500, 'Santa Cruz', 2234, '17', 177, 'PT', 41.76007000, -7.46834000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366997'),
(89501, 'Santa Cruz das Flores', 2233, '20', 177, 'PT', 39.46984000, -31.18514000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1003134'),
(89502, 'Santa Cruz da Graciosa', 2233, '20', 177, 'PT', 39.04946000, -28.00432000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q1010463'),
(89503, 'Santa Cruz do Bispo', 2243, '13', 177, 'PT', 41.21462000, -8.67406000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49366999'),
(89504, 'Santa Iria da Azóia', 2228, '11', 177, 'PT', 38.84110000, -9.09908000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49367001'),
(89505, 'Santa Luzia', 2239, '08', 177, 'PT', 37.10224000, -7.66202000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q2687695'),
(89506, 'Santa Margarida da Coutada', 2238, '14', 177, 'PT', 39.45000000, -8.31667000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q2687695'),
(89507, 'Santa Maria da Feira', 2235, '01', 177, 'PT', 40.96043000, -8.51545000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q564813'),
(89508, 'Santa Marta de Penaguião', 2234, '17', 177, 'PT', 41.20991000, -7.78386000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q1006372'),
(89509, 'Santana', 2231, '30', 177, 'PT', 32.80000000, -16.88333000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49367020'),
(89510, 'Santarém', 2238, '14', 177, 'PT', 39.23333000, -8.68333000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q863744'),
(89511, 'Santiago do Cacém', 2242, '15', 177, 'PT', 38.02502000, -8.67577000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q990433'),
(89512, 'Santo André', 2242, '15', 177, 'PT', 38.06078000, -8.78220000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49367030'),
(89513, 'Santo André', 2232, '12', 177, 'PT', 39.05532000, -8.24414000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49367029'),
(89514, 'Santo Antão do Tojal', 2228, '11', 177, 'PT', 38.85151000, -9.13975000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49367032'),
(89515, 'Santo António da Charneca', 2242, '15', 177, 'PT', 38.62561000, -9.03043000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49367033'),
(89516, 'Santo António dos Olivais', 2246, '06', 177, 'PT', 40.21805000, -8.40523000, '2019-10-05 23:14:36', '2020-05-01 17:23:07', 1, 'Q49367033'),
(89517, 'Santo Isidoro', 2228, '11', 177, 'PT', 38.99593000, -9.39940000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49367033'),
(89518, 'Santo Izidoro', 2243, '13', 177, 'PT', 41.21083000, -8.14224000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q49367033'),
(89519, 'Santo Tirso', 2243, '13', 177, 'PT', 41.33014000, -8.43827000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q917330'),
(89520, 'Santos-o-Velho', 2228, '11', 177, 'PT', 38.70690000, -9.15611000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q917330'),
(89521, 'Sardoal', 2238, '14', 177, 'PT', 39.55643000, -8.14323000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q635165'),
(89522, 'Sarilhos Pequenos', 2242, '15', 177, 'PT', 38.68176000, -8.98231000, '2019-10-05 23:14:36', '2019-10-05 23:14:36', 1, 'Q2451289');

