INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(10668, 'Boa Vista da Aparecida', 2022, 'PR', 31, 'BR', -25.43709000, -53.41293000, '2019-10-05 22:34:58', '2019-10-05 22:34:58', 1, 'Q1808337'),
(10669, 'Boa Vista das Missões', 2001, 'RS', 31, 'BR', -27.69239000, -53.35253000, '2019-10-05 22:34:58', '2020-05-01 17:22:38', 1, 'Q1808337'),
(10670, 'Boa Vista do Buricá', 2001, 'RS', 31, 'BR', -27.68021000, -54.10928000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1808337'),
(10671, 'Boa Vista do Cadeado', 2001, 'RS', 31, 'BR', -28.62211000, -53.81616000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2007792'),
(10672, 'Boa <PERSON> do Gurupi', 2015, 'MA', 31, 'BR', -1.73082000, -46.19635000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2007792'),
(10673, '<PERSON>a <PERSON> do Incra', 2001, 'RS', 31, 'BR', -28.84193000, -53.45649000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1803204'),
(10674, 'Boa Vista do Ramos', 2004, 'AM', 31, 'BR', -3.15779000, -57.90517000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1803204'),
(10675, 'Boa Vista do Sul', 2001, 'RS', 31, 'BR', -29.34197000, -51.68079000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1803204'),
(10676, 'Boa Vista do Tupim', 2002, 'BA', 31, 'BR', -12.87313000, -40.62823000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1803204'),
(10677, 'Boca da Mata', 2007, 'AL', 31, 'BR', -9.64651000, -36.14134000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1803204'),
(10678, 'Boca do Acre', 2004, 'AM', 31, 'BR', -8.75222000, -67.39778000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1793334'),
(10679, 'Bocaina', 2008, 'PI', 31, 'BR', -6.89508000, -41.32746000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1793334'),
(10680, 'Bocaina', 2021, 'SP', 31, 'BR', -22.09830000, -48.51952000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1759088'),
(10681, 'Bocaina de Minas', 1998, 'MG', 31, 'BR', -22.23436000, -44.49342000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1759088'),
(10682, 'Bocaina do Sul', 2014, 'SC', 31, 'BR', -27.73891000, -49.91707000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1759088'),
(10683, 'Bocaiúva', 1998, 'MG', 31, 'BR', -17.38916000, -43.83571000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1756928'),
(10684, 'Bocaiúva do Sul', 2022, 'PR', 31, 'BR', -25.10040000, -48.87116000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1756928'),
(10685, 'Bodocó', 2006, 'PE', 31, 'BR', -7.78881000, -39.95255000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q524487'),
(10686, 'Bodoquena', 2010, 'MS', 31, 'BR', -20.48226000, -56.65893000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q524487'),
(10687, 'Bodó', 2019, 'RN', 31, 'BR', -5.93390000, -36.39716000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1802663'),
(10688, 'Bofete', 2021, 'SP', 31, 'BR', -23.12244000, -48.28058000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1802663'),
(10689, 'Boituva', 2021, 'SP', 31, 'BR', -23.28333000, -47.67222000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1759350'),
(10690, 'Bom Conselho', 2006, 'PE', 31, 'BR', -9.16972000, -36.67972000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q568749'),
(10691, 'Bom Despacho', 1998, 'MG', 31, 'BR', -19.69145000, -45.25297000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891713'),
(10692, 'Bom Jardim', 2006, 'PE', 31, 'BR', -7.79583000, -35.58722000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2097751'),
(10693, 'Bom Jardim', 2015, 'MA', 31, 'BR', -3.77409000, -46.21707000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2279959'),
(10694, 'Bom Jardim', 1997, 'RJ', 31, 'BR', -22.17871000, -42.33515000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2279959'),
(10695, 'Bom Jardim da Serra', 2014, 'SC', 31, 'BR', -28.36743000, -49.64216000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2279959'),
(10696, 'Bom Jardim de Goiás', 2000, 'GO', 31, 'BR', -16.27033000, -52.05682000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2279959'),
(10697, 'Bom Jardim de Minas', 1998, 'MG', 31, 'BR', -21.94390000, -44.12193000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2279959'),
(10698, 'Bom Jesus', 2008, 'PI', 31, 'BR', -9.07444000, -44.35861000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891715'),
(10699, 'Bom Jesus', 2019, 'RN', 31, 'BR', -6.00882000, -35.58384000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891715'),
(10700, 'Bom Jesus', 2005, 'PB', 31, 'BR', -6.81845000, -38.62549000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2066065'),
(10701, 'Bom Jesus', 2001, 'RS', 31, 'BR', -28.59981000, -50.46197000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891720'),
(10702, 'Bom Jesus', 2014, 'SC', 31, 'BR', -26.74082000, -52.39184000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q769692'),
(10703, 'Bom Jesus da Lapa', 2002, 'BA', 31, 'BR', -13.25500000, -43.41806000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891727'),
(10704, 'Bom Jesus da Penha', 1998, 'MG', 31, 'BR', -20.99902000, -46.54769000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891727'),
(10705, 'Bom Jesus da Serra', 2002, 'BA', 31, 'BR', -14.38380000, -40.53982000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891727'),
(10706, 'Bom Jesus das Selvas', 2015, 'MA', 31, 'BR', -4.51333000, -46.60686000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891727'),
(10707, 'Bom Jesus de Goiás', 2000, 'GO', 31, 'BR', -18.19213000, -49.89536000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q891727'),
(10708, 'Bom Jesus do Amparo', 1998, 'MG', 31, 'BR', -19.71668000, -43.46840000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891727'),
(10709, 'Bom Jesus do Araguaia', 2011, 'MT', 31, 'BR', -12.27879000, -51.75156000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2012959'),
(10710, 'Bom Jesus do Galho', 1998, 'MG', 31, 'BR', -19.71687000, -42.37558000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q891733'),
(10711, 'Bom Jesus do Itabapoana', 1997, 'RJ', 31, 'BR', -21.13389000, -41.67972000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1750011'),
(10712, 'Bom Jesus do Norte', 2018, 'ES', 31, 'BR', -21.08831000, -41.63939000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1807022'),
(10713, 'Bom Jesus do Oeste', 2014, 'SC', 31, 'BR', -26.69047000, -53.09669000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1807022'),
(10714, 'Bom Jesus do Sul', 2022, 'PR', 31, 'BR', -26.15280000, -53.54963000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1807022'),
(10715, 'Bom Jesus do Tocantins', 2020, 'TO', 31, 'BR', -9.01838000, -47.86969000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1807022'),
(10716, 'Bom Jesus do Tocantins', 2009, 'PA', 31, 'BR', -4.99184000, -48.82370000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1807022'),
(10717, 'Bom Jesus dos Perdões', 2021, 'SP', 31, 'BR', -23.13500000, -46.46528000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1760959'),
(10718, 'Bom Lugar', 2015, 'MA', 31, 'BR', -4.34667000, -45.02098000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1760959'),
(10719, 'Bom Princípio', 2001, 'RS', 31, 'BR', -29.46171000, -51.36405000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1648506'),
(10720, 'Bom Princípio do Piauí', 2008, 'PI', 31, 'BR', -3.23751000, -41.64572000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1648506'),
(10721, 'Bom Progresso', 2001, 'RS', 31, 'BR', -27.54223000, -53.82889000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1648506'),
(10722, 'Bom Repouso', 1998, 'MG', 31, 'BR', -22.44963000, -46.18632000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1648506'),
(10723, 'Bom Retiro', 2014, 'SC', 31, 'BR', -27.76046000, -49.61964000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1648506'),
(10724, 'Bom Retiro do Sul', 2001, 'RS', 31, 'BR', -29.63558000, -51.91097000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1648506'),
(10725, 'Bom Sucesso', 2005, 'PB', 31, 'BR', -6.47349000, -37.95770000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2087575'),
(10726, 'Bom Sucesso', 1998, 'MG', 31, 'BR', -21.03029000, -44.79501000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1805732'),
(10727, 'Bom Sucesso', 2022, 'PR', 31, 'BR', -23.69506000, -51.81945000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2355826'),
(10728, 'Bom Sucesso de Itararé', 2021, 'SP', 31, 'BR', -24.31225000, -49.16700000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q2355826'),
(10729, 'Bom Sucesso do Sul', 2022, 'PR', 31, 'BR', -26.08239000, -52.85103000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2004481'),
(10730, 'Bombinhas', 2014, 'SC', 31, 'BR', -27.17876000, -48.50195000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2004481'),
(10731, 'Bonfim', 1998, 'MG', 31, 'BR', -20.31591000, -44.20740000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22062759'),
(10732, 'Bonfim do Piauí', 2008, 'PI', 31, 'BR', -9.16462000, -42.88253000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q2089257'),
(10733, 'Bonfinópolis', 2000, 'GO', 31, 'BR', -16.59726000, -49.01486000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2089257'),
(10734, 'Bonfinópolis de Minas', 1998, 'MG', 31, 'BR', -16.54416000, -46.13266000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2089257'),
(10735, 'Boninal', 2002, 'BA', 31, 'BR', -12.80363000, -41.68338000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2089257'),
(10736, 'Bonito', 2006, 'PE', 31, 'BR', -8.47028000, -35.72861000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2341964'),
(10737, 'Bonito', 2010, 'MS', 31, 'BR', -21.12111000, -56.48194000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q768535'),
(10738, 'Bonito', 2009, 'PA', 31, 'BR', -1.42927000, -47.33735000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2078868'),
(10739, 'Bonito', 2002, 'BA', 31, 'BR', -12.00488000, -41.31779000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1808128'),
(10740, 'Bonito de Minas', 1998, 'MG', 31, 'BR', -14.91015000, -44.88471000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1808128'),
(10741, 'Bonito de Santa Fé', 2005, 'PB', 31, 'BR', -7.28940000, -38.47977000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q776733'),
(10742, 'Bonópolis', 2000, 'GO', 31, 'BR', -13.57725000, -49.89702000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q776733'),
(10743, 'Boqueirão', 2005, 'PB', 31, 'BR', -7.48237000, -36.13422000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q22027642'),
(10744, 'Boqueirão do Leão', 2001, 'RS', 31, 'BR', -29.31252000, -52.39841000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q22027642'),
(10745, 'Boqueirão do Piauí', 2008, 'PI', 31, 'BR', -4.56650000, -42.13601000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q22027642'),
(10746, 'Boquim', 2003, 'SE', 31, 'BR', -11.14694000, -37.62056000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q746187'),
(10747, 'Boquira', 2002, 'BA', 31, 'BR', -12.82306000, -42.73056000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1772521'),
(10748, 'Boracéia', 2021, 'SP', 31, 'BR', -22.19306000, -48.77889000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q776912'),
(10749, 'Borba', 2004, 'AM', 31, 'BR', -4.39143000, -59.58864000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2089660'),
(10750, 'Borborema', 2021, 'SP', 31, 'BR', -21.61972000, -49.07361000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q978870'),
(10751, 'Borborema', 2005, 'PB', 31, 'BR', -6.79913000, -35.62094000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q338286'),
(10752, 'Borda da Mata', 1998, 'MG', 31, 'BR', -22.25106000, -46.16707000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1792820'),
(10753, 'Borebi', 2021, 'SP', 31, 'BR', -22.67635000, -48.98476000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1792820'),
(10754, 'Borrazópolis', 2022, 'PR', 31, 'BR', -23.93931000, -51.60141000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1792820'),
(10755, 'Borá', 2021, 'SP', 31, 'BR', -22.23881000, -50.49252000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1792820'),
(10756, 'Bossoroca', 2001, 'RS', 31, 'BR', -28.67113000, -54.96188000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1786682'),
(10757, 'Botelhos', 1998, 'MG', 31, 'BR', -21.64903000, -46.42831000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1756413'),
(10758, 'Botucatu', 2021, 'SP', 31, 'BR', -22.88583000, -48.44500000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1625598'),
(10759, 'Botumirim', 1998, 'MG', 31, 'BR', -16.90950000, -43.00848000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1625598'),
(10760, 'Botuporã', 2002, 'BA', 31, 'BR', -13.29549000, -42.52593000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1625598'),
(10761, 'Botuverá', 2014, 'SC', 31, 'BR', -27.20456000, -49.11175000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1625598'),
(10762, 'Bozano', 2001, 'RS', 31, 'BR', -28.36580000, -53.76893000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q347692'),
(10763, 'Braga', 2001, 'RS', 31, 'BR', -27.57218000, -53.76214000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q614230'),
(10764, 'Braganey', 2022, 'PR', 31, 'BR', -24.79019000, -53.08864000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q614230'),
(10765, 'Bragança', 2009, 'PA', 31, 'BR', -0.96813000, -46.73377000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q320487'),
(10766, 'Bragança Paulista', 2021, 'SP', 31, 'BR', -22.95270000, -46.54418000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q897232'),
(10767, 'Branquinha', 2007, 'AL', 31, 'BR', -9.21168000, -36.08956000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q897232'),
(10768, 'Brasil Novo', 2009, 'PA', 31, 'BR', -3.33767000, -52.57451000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2008779'),
(10769, 'Brasileira', 2008, 'PI', 31, 'BR', -4.13325000, -41.58774000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2329749'),
(10770, 'Brasilândia', 2010, 'MS', 31, 'BR', -21.11993000, -52.43829000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2329749'),
(10771, 'Brasilândia de Minas', 1998, 'MG', 31, 'BR', -16.93061000, -45.96385000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2329749'),
(10772, 'Brasilândia do Sul', 2022, 'PR', 31, 'BR', -24.21682000, -53.57250000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q2329749'),
(10773, 'Brasilândia do Tocantins', 2020, 'TO', 31, 'BR', -8.26045000, -48.43450000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q2329749'),
(10774, 'Brasiléia', 2012, 'AC', 31, 'BR', -11.01611000, -68.74806000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q899406'),
(10775, 'Brasnorte', 2011, 'MT', 31, 'BR', -12.24899000, -58.00289000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q899406'),
(10776, 'Brasília', 2017, 'DF', 31, 'BR', -15.77972000, -47.92972000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2844'),
(10777, 'Brasília de Minas', 1998, 'MG', 31, 'BR', -16.25246000, -44.45712000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q22062752'),
(10778, 'Brazabrantes', 2000, 'GO', 31, 'BR', -16.37909000, -49.38445000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22062752'),
(10779, 'Brazópolis', 1998, 'MG', 31, 'BR', -22.48169000, -45.62802000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q22062752'),
(10780, 'Braço do Norte', 2014, 'SC', 31, 'BR', -28.27500000, -49.16556000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q903373'),
(10781, 'Braço do Trombudo', 2014, 'SC', 31, 'BR', -27.37888000, -49.91379000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q903373'),
(10782, 'Braúna', 2021, 'SP', 31, 'BR', -21.56582000, -50.35227000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q903373'),
(10783, 'Braúnas', 1998, 'MG', 31, 'BR', -19.02181000, -42.71399000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q903373'),
(10784, 'Brejetuba', 2018, 'ES', 31, 'BR', -20.15621000, -41.30650000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q903373'),
(10785, 'Brejinho', 2019, 'RN', 31, 'BR', -6.19083000, -35.35667000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2088956'),
(10786, 'Brejinho', 2006, 'PE', 31, 'BR', -7.34462000, -37.33463000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q682185'),
(10787, 'Brejinho de Nazaré', 2020, 'TO', 31, 'BR', -11.01872000, -48.64695000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q682185'),
(10788, 'Brejo', 2015, 'MA', 31, 'BR', -3.68444000, -42.75028000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q659888'),
(10789, 'Brejo Alegre', 2021, 'SP', 31, 'BR', -21.17757000, -50.21805000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q659888'),
(10790, 'Brejo Grande', 2003, 'SE', 31, 'BR', -10.49241000, -36.45883000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q659888'),
(10791, 'Brejo Grande do Araguaia', 2009, 'PA', 31, 'BR', -5.74369000, -48.45573000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q659888'),
(10792, 'Brejo Santo', 2016, 'CE', 31, 'BR', -7.62229000, -38.88195000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2027070'),
(10793, 'Brejo da Madre de Deus', 2006, 'PE', 31, 'BR', -8.14583000, -36.37111000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1807223'),
(10794, 'Brejo de Areia', 2015, 'MA', 31, 'BR', -4.32450000, -45.53970000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2068871'),
(10795, 'Brejo do Cruz', 2005, 'PB', 31, 'BR', -6.32578000, -37.49716000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1808002'),
(10796, 'Brejo do Piauí', 2008, 'PI', 31, 'BR', -8.26858000, -42.78801000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1808002'),
(10797, 'Brejo dos Santos', 2005, 'PB', 31, 'BR', -6.39546000, -37.86070000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1808002'),
(10798, 'Brejolândia', 2002, 'BA', 31, 'BR', -12.41237000, -43.92822000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1808002'),
(10799, 'Brejão', 2006, 'PE', 31, 'BR', -9.03600000, -36.56056000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1808002'),
(10800, 'Brejões', 2002, 'BA', 31, 'BR', -13.07059000, -39.82099000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1808002'),
(10801, 'Breu Branco', 2009, 'PA', 31, 'BR', -3.73348000, -49.37122000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1808002'),
(10802, 'Breves', 2009, 'PA', 31, 'BR', -1.68222000, -50.48028000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1804770'),
(10803, 'Britânia', 2000, 'GO', 31, 'BR', -15.21115000, -51.14785000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q780494'),
(10804, 'Brochier', 2001, 'RS', 31, 'BR', -29.55754000, -51.59193000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q780494'),
(10805, 'Brodowski', 2021, 'SP', 31, 'BR', -21.05060000, -47.61728000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q924792'),
(10806, 'Brodósqui', 2021, 'SP', 31, 'BR', -20.99139000, -47.65861000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q22048849'),
(10807, 'Brotas', 2021, 'SP', 31, 'BR', -22.27942000, -48.08744000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1648408'),
(10808, 'Brotas de Macaúbas', 2002, 'BA', 31, 'BR', -12.10605000, -42.51108000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1648408'),
(10809, 'Brumadinho', 1998, 'MG', 31, 'BR', -20.20447000, -44.15388000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1751212'),
(10810, 'Brumado', 2002, 'BA', 31, 'BR', -14.20361000, -41.66528000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1808298'),
(10811, 'Brunópolis', 2014, 'SC', 31, 'BR', -27.34781000, -50.83631000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1808298'),
(10812, 'Brusque', 2014, 'SC', 31, 'BR', -27.09795000, -48.91281000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q329488'),
(10813, 'Brás Pires', 1998, 'MG', 31, 'BR', -20.87837000, -43.22442000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q329488'),
(10814, 'Bueno Brandão', 1998, 'MG', 31, 'BR', -22.50459000, -46.35271000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1792008'),
(10815, 'Buenos Aires', 2006, 'PE', 31, 'BR', -7.72000000, -35.32000000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2101554'),
(10816, 'Buenópolis', 1998, 'MG', 31, 'BR', -17.87455000, -44.02374000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2101554'),
(10817, 'Buerarema', 2002, 'BA', 31, 'BR', -14.95944000, -39.29972000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1761955'),
(10818, 'Bugre', 1998, 'MG', 31, 'BR', -19.36701000, -42.30815000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1793226'),
(10819, 'Bujari', 2012, 'AC', 31, 'BR', -9.57859000, -68.17197000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1793226'),
(10820, 'Bujaru', 2009, 'PA', 31, 'BR', -1.51500000, -48.04472000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2009108'),
(10821, 'Buri', 2021, 'SP', 31, 'BR', -23.79750000, -48.59278000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1816107'),
(10822, 'Buritama', 2021, 'SP', 31, 'BR', -21.06611000, -50.14722000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1750100'),
(10823, 'Buriti', 2015, 'MA', 31, 'BR', -3.94799000, -42.85810000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2135872'),
(10824, 'Buriti Alegre', 2000, 'GO', 31, 'BR', -18.14000000, -49.04028000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q779122'),
(10825, 'Buriti Bravo', 2015, 'MA', 31, 'BR', -5.83722000, -43.83361000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2013234'),
(10826, 'Buriti de Goiás', 2000, 'GO', 31, 'BR', -16.16439000, -50.42918000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2013234'),
(10827, 'Buriti do Tocantins', 2020, 'TO', 31, 'BR', -5.37057000, -48.13476000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2013234'),
(10828, 'Buriti dos Lopes', 2008, 'PI', 31, 'BR', -3.17500000, -41.86694000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2064004'),
(10829, 'Buriti dos Montes', 2008, 'PI', 31, 'BR', -5.20888000, -41.25078000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2064004'),
(10830, 'Buriticupu', 2015, 'MA', 31, 'BR', -4.51967000, -46.37712000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2064004'),
(10831, 'Buritinópolis', 2000, 'GO', 31, 'BR', -14.40974000, -46.31759000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q2064004'),
(10832, 'Buritirama', 2002, 'BA', 31, 'BR', -10.57081000, -43.68863000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2064004'),
(10833, 'Buritirana', 2015, 'MA', 31, 'BR', -5.59248000, -46.99721000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2013324'),
(10834, 'Buritis', 2013, 'RO', 31, 'BR', -10.09215000, -63.96964000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2013324'),
(10835, 'Buritis', 1998, 'MG', 31, 'BR', -15.41389000, -46.55470000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1792148'),
(10836, 'Buritizal', 2021, 'SP', 31, 'BR', -20.21345000, -47.70776000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1792148'),
(10837, 'Buritizeiro', 1998, 'MG', 31, 'BR', -17.40582000, -45.30446000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1792028'),
(10838, 'Butiá', 2001, 'RS', 31, 'BR', -30.11972000, -51.96222000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q640384'),
(10839, 'Buíque', 2006, 'PE', 31, 'BR', -8.62306000, -37.15583000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1809636'),
(10840, 'Bálsamo', 2021, 'SP', 31, 'BR', -20.70239000, -49.55045000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1750436'),
(10841, 'Caapiranga', 2004, 'AM', 31, 'BR', -3.12981000, -61.74406000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1750436'),
(10842, 'Caaporã', 2005, 'PB', 31, 'BR', -7.51556000, -34.90833000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1816118'),
(10843, 'Caarapó', 2010, 'MS', 31, 'BR', -22.63417000, -54.82222000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q279492'),
(10844, 'Caatiba', 2002, 'BA', 31, 'BR', -14.97610000, -40.39048000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q279492'),
(10845, 'Cabaceiras', 2005, 'PB', 31, 'BR', -7.50856000, -36.33351000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q279492'),
(10846, 'Cabaceiras do Paraguaçu', 2002, 'BA', 31, 'BR', -12.55423000, -39.19596000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q279492'),
(10847, 'Cabeceira Grande', 1998, 'MG', 31, 'BR', -16.06178000, -47.17573000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q279492'),
(10848, 'Cabeceiras', 2000, 'GO', 31, 'BR', -15.73747000, -47.03428000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q279492'),
(10849, 'Cabeceiras do Piauí', 2008, 'PI', 31, 'BR', -4.43347000, -42.23060000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q279492'),
(10850, 'Cabedelo', 2005, 'PB', 31, 'BR', -6.98111000, -34.83389000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q986350'),
(10851, 'Cabixi', 2013, 'RO', 31, 'BR', -13.51083000, -60.60257000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q986350'),
(10852, 'Cabo', 2006, 'PE', 31, 'BR', -8.28333000, -35.03333000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22027822'),
(10853, 'Cabo Frio', 1997, 'RJ', 31, 'BR', -22.88717000, -42.02622000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q735025'),
(10854, 'Cabo Verde', 1998, 'MG', 31, 'BR', -21.48702000, -46.37999000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22062741'),
(10855, 'Cabo de Santo Agostinho', 2006, 'PE', 31, 'BR', -8.27727000, -35.09090000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22062741'),
(10856, 'Cabreúva', 2021, 'SP', 31, 'BR', -23.30750000, -47.13278000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1760058'),
(10857, 'Cabrobó', 2006, 'PE', 31, 'BR', -8.51417000, -39.31000000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q2010855'),
(10858, 'Cabrália Paulista', 2021, 'SP', 31, 'BR', -22.49155000, -49.38637000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q2010855'),
(10859, 'Cacaulândia', 2013, 'RO', 31, 'BR', -10.32583000, -63.14953000, '2019-10-05 22:34:59', '2020-05-01 17:22:38', 1, 'Q1750177'),
(10860, 'Cacequi', 2001, 'RS', 31, 'BR', -29.88361000, -54.82500000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1757709'),
(10861, 'Cachoeira', 2002, 'BA', 31, 'BR', -12.60139000, -38.96576000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q985576'),
(10862, 'Cachoeira Alta', 2000, 'GO', 31, 'BR', -18.54284000, -50.98208000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q985576'),
(10863, 'Cachoeira Dourada', 1998, 'MG', 31, 'BR', -18.59094000, -49.47013000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22062738'),
(10864, 'Cachoeira Dourada', 2000, 'GO', 31, 'BR', -18.49516000, -49.65104000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22062738'),
(10865, 'Cachoeira Grande', 2015, 'MA', 31, 'BR', -3.12007000, -43.93132000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q22062738'),
(10866, 'Cachoeira Paulista', 2021, 'SP', 31, 'BR', -22.70115000, -44.99229000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1760112'),
(10867, 'Cachoeira da Prata', 1998, 'MG', 31, 'BR', -19.51865000, -44.46250000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1760112'),
(10868, 'Cachoeira de Goiás', 2000, 'GO', 31, 'BR', -16.72507000, -50.68613000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1760112'),
(10869, 'Cachoeira de Minas', 1998, 'MG', 31, 'BR', -22.35563000, -45.79447000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1760112'),
(10870, 'Cachoeira de Pajeú', 1998, 'MG', 31, 'BR', -15.97163000, -41.49303000, '2019-10-05 22:34:59', '2020-05-01 17:22:36', 1, 'Q1760112'),
(10871, 'Cachoeira do Arari', 2009, 'PA', 31, 'BR', -0.84609000, -48.93118000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1760112'),
(10872, 'Cachoeira do Piriá', 2009, 'PA', 31, 'BR', -1.94037000, -46.46914000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q947745'),
(10873, 'Cachoeira do Sul', 2001, 'RS', 31, 'BR', -30.20808000, -52.97067000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q166378'),
(10874, 'Cachoeira dos Índios', 2005, 'PB', 31, 'BR', -6.94486000, -38.69902000, '2019-10-05 22:34:59', '2020-05-01 17:22:37', 1, 'Q1816046'),
(10875, 'Cachoeiras de Macacu', 1997, 'RJ', 31, 'BR', -22.46250000, -42.65306000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q1983181'),
(10876, 'Cachoeirinha', 2006, 'PE', 31, 'BR', -8.48639000, -36.23306000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q2099535'),
(10877, 'Cachoeirinha', 2001, 'RS', 31, 'BR', -29.95111000, -51.09389000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q957697'),
(10878, 'Cachoeirinha', 2020, 'TO', 31, 'BR', -6.09807000, -47.87888000, '2019-10-05 22:34:59', '2019-10-05 22:34:59', 1, 'Q957697'),
(10879, 'Cachoeiro de Itapemirim', 2018, 'ES', 31, 'BR', -20.84889000, -41.11278000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1439193'),
(10880, 'Cacimba de Areia', 2005, 'PB', 31, 'BR', -7.13451000, -37.15370000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1439193'),
(10881, 'Cacimba de Dentro', 2005, 'PB', 31, 'BR', -6.64167000, -35.79000000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1816227'),
(10882, 'Cacimbas', 2005, 'PB', 31, 'BR', -7.23180000, -37.09984000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1816227'),
(10883, 'Cacimbinhas', 2007, 'AL', 31, 'BR', -9.40028000, -36.99028000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808791'),
(10884, 'Cacique Doble', 2001, 'RS', 31, 'BR', -27.81730000, -51.68271000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808791'),
(10885, 'Cacoal', 2013, 'RO', 31, 'BR', -11.43861000, -61.44722000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1761637'),
(10886, 'Caconde', 2021, 'SP', 31, 'BR', -21.52944000, -46.64389000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q983521'),
(10887, 'Caculé', 2002, 'BA', 31, 'BR', -14.38829000, -42.41516000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1795313'),
(10888, 'Caetanos', 2002, 'BA', 31, 'BR', -14.29234000, -41.01004000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1795313'),
(10889, 'Caetanópolis', 1998, 'MG', 31, 'BR', -19.33473000, -44.41002000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1792016'),
(10890, 'Caetité', 2002, 'BA', 31, 'BR', -13.97883000, -42.49334000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1795371'),
(10891, 'Caeté', 1998, 'MG', 31, 'BR', -19.87017000, -43.65060000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1755373'),
(10892, 'Caetés', 2006, 'PE', 31, 'BR', -8.77306000, -36.62250000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1008259'),
(10893, 'Cafarnaum', 2002, 'BA', 31, 'BR', -11.69361000, -41.46833000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808307'),
(10894, 'Cafeara', 2022, 'PR', 31, 'BR', -22.79980000, -51.71428000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808307'),
(10895, 'Cafelândia', 2021, 'SP', 31, 'BR', -21.74295000, -49.57061000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q1759688'),
(10896, 'Cafelândia', 2022, 'PR', 31, 'BR', -24.69297000, -53.35090000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q2040790'),
(10897, 'Cafezal do Sul', 2022, 'PR', 31, 'BR', -23.95645000, -53.60910000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2040790'),
(10898, 'Caiabu', 2021, 'SP', 31, 'BR', -21.93822000, -51.23232000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2040790'),
(10899, 'Caiana', 1998, 'MG', 31, 'BR', -20.73080000, -41.90016000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2040790'),
(10900, 'Caiapônia', 2000, 'GO', 31, 'BR', -16.95987000, -51.79148000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1761543'),
(10901, 'Caibaté', 2001, 'RS', 31, 'BR', -28.30429000, -54.65585000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q742521'),
(10902, 'Caibi', 2014, 'SC', 31, 'BR', -27.03422000, -53.27842000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q742521'),
(10903, 'Caicó', 2019, 'RN', 31, 'BR', -6.45833000, -37.09778000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1751350'),
(10904, 'Caieiras', 2021, 'SP', 31, 'BR', -23.36417000, -46.74056000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22046952'),
(10905, 'Cairu', 2002, 'BA', 31, 'BR', -13.52662000, -38.94814000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22046952'),
(10906, 'Caiuá', 2021, 'SP', 31, 'BR', -21.76518000, -51.95903000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q22046952'),
(10907, 'Caiçara', 2005, 'PB', 31, 'BR', -6.59631000, -35.39497000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q22046952'),
(10908, 'Caiçara', 2001, 'RS', 31, 'BR', -27.24828000, -53.45824000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q22046952'),
(10909, 'Caiçara do Norte', 2019, 'RN', 31, 'BR', -5.17530000, -36.08369000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1787018'),
(10910, 'Caiçara do Rio do Vento', 2019, 'RN', 31, 'BR', -5.80605000, -36.02549000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1787018'),
(10911, 'Cajamar', 2021, 'SP', 31, 'BR', -23.35193000, -46.88129000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1760376'),
(10912, 'Cajapió', 2015, 'MA', 31, 'BR', -2.86374000, -44.57287000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q934637'),
(10913, 'Cajari', 2015, 'MA', 31, 'BR', -3.38514000, -45.02197000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q934637'),
(10914, 'Cajati', 2021, 'SP', 31, 'BR', -24.73611000, -48.12278000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1760947'),
(10915, 'Cajazeiras', 2005, 'PB', 31, 'BR', -6.89028000, -38.55528000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808637'),
(10916, 'Cajazeiras do Piauí', 2008, 'PI', 31, 'BR', -6.78182000, -42.39243000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1808637'),
(10917, 'Cajazeirinhas', 2005, 'PB', 31, 'BR', -6.94633000, -37.81454000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808637'),
(10918, 'Cajobi', 2021, 'SP', 31, 'BR', -20.88622000, -48.85175000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808637'),
(10919, 'Cajueiro', 2007, 'AL', 31, 'BR', -9.71667000, -36.45000000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22023546'),
(10920, 'Cajueiro da Praia', 2008, 'PI', 31, 'BR', -2.98747000, -41.35439000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22023546'),
(10921, 'Cajuri', 1998, 'MG', 31, 'BR', -20.78744000, -42.76259000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22023546'),
(10922, 'Cajuru', 2021, 'SP', 31, 'BR', -21.27528000, -47.30417000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1760370'),
(10923, 'Caldas', 1998, 'MG', 31, 'BR', -21.88671000, -46.35924000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22062733'),
(10924, 'Caldas Brandão', 2005, 'PB', 31, 'BR', -7.15105000, -35.34870000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q22062733'),
(10925, 'Caldas Novas', 2000, 'GO', 31, 'BR', -17.73126000, -48.65479000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q120708'),
(10926, 'Caldazinha', 2000, 'GO', 31, 'BR', -16.74858000, -49.00064000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q120708'),
(10927, 'Caldeirão Grande', 2002, 'BA', 31, 'BR', -11.04347000, -40.29515000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q283971'),
(10928, 'Caldeirão Grande do Piauí', 2008, 'PI', 31, 'BR', -7.34361000, -40.58774000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q2064592'),
(10929, 'Califórnia', 2022, 'PR', 31, 'BR', -23.66340000, -51.32824000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q2077893'),
(10930, 'Calmon', 2014, 'SC', 31, 'BR', -26.64037000, -51.02439000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2077893'),
(10931, 'Calumbi', 2006, 'PE', 31, 'BR', -8.01910000, -38.09799000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2077893'),
(10932, 'Calçado', 2006, 'PE', 31, 'BR', -8.73580000, -36.33149000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q2088907'),
(10933, 'Calçoene', 1999, 'AP', 31, 'BR', 2.36098000, -51.45285000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q2088907'),
(10934, 'Camacan', 2002, 'BA', 31, 'BR', -15.42769000, -39.50818000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2088907'),
(10935, 'Camacho', 1998, 'MG', 31, 'BR', -20.64216000, -45.14334000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1756303'),
(10936, 'Camalaú', 2005, 'PB', 31, 'BR', -7.92695000, -36.74211000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1816125'),
(10937, 'Camamu', 2002, 'BA', 31, 'BR', -13.94472000, -39.10389000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1795776'),
(10938, 'Camanducaia', 1998, 'MG', 31, 'BR', -22.75528000, -46.14472000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1754774'),
(10939, 'Camapuã', 2010, 'MS', 31, 'BR', -19.05769000, -53.85781000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1797146'),
(10940, 'Camaquã', 2001, 'RS', 31, 'BR', -30.91965000, -51.85302000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q986275'),
(10941, 'Camaragibe', 2006, 'PE', 31, 'BR', -7.98780000, -34.99136000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q986275'),
(10942, 'Camargo', 2001, 'RS', 31, 'BR', -28.61437000, -52.21986000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q986275'),
(10943, 'Camaçari', 2002, 'BA', 31, 'BR', -12.69750000, -38.32417000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1136204'),
(10944, 'Cambará', 2022, 'PR', 31, 'BR', -23.00420000, -50.08819000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q2007353'),
(10945, 'Cambará do Sul', 2001, 'RS', 31, 'BR', -29.06727000, -50.11848000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q612092'),
(10946, 'Cambira', 2022, 'PR', 31, 'BR', -23.63164000, -51.56671000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q612092'),
(10947, 'Camboriú', 2014, 'SC', 31, 'BR', -27.07562000, -48.71530000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q744097'),
(10948, 'Cambuci', 1997, 'RJ', 31, 'BR', -21.57528000, -41.91111000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1787650'),
(10949, 'Cambuquira', 1998, 'MG', 31, 'BR', -21.85379000, -45.27170000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1754942'),
(10950, 'Cambuí', 1998, 'MG', 31, 'BR', -22.58243000, -46.06121000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1756818'),
(10951, 'Cambé', 2022, 'PR', 31, 'BR', -23.27583000, -51.27833000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1773340'),
(10952, 'Cametá', 2009, 'PA', 31, 'BR', -2.29582000, -49.49213000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1805206'),
(10953, 'Camocim', 2016, 'CE', 31, 'BR', -2.90222000, -40.84111000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1807057'),
(10954, 'Camocim de São Félix', 2006, 'PE', 31, 'BR', -8.35861000, -35.76194000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1811052'),
(10955, 'Campanha', 1998, 'MG', 31, 'BR', -21.81412000, -45.39753000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1780173'),
(10956, 'Campanário', 1998, 'MG', 31, 'BR', -18.27490000, -41.73907000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q22062728'),
(10957, 'Campestre', 2007, 'AL', 31, 'BR', -8.89393000, -35.53237000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2028637'),
(10958, 'Campestre', 1998, 'MG', 31, 'BR', -21.73436000, -46.22571000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22062719'),
(10959, 'Campestre da Serra', 2001, 'RS', 31, 'BR', -28.70645000, -51.11921000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22062719'),
(10960, 'Campestre de Goiás', 2000, 'GO', 31, 'BR', -16.78337000, -49.70958000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q22062719'),
(10961, 'Campestre do Maranhão', 2015, 'MA', 31, 'BR', -6.15156000, -47.23427000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q22062719'),
(10962, 'Campina Grande', 2005, 'PB', 31, 'BR', -7.23056000, -35.88111000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q193016'),
(10963, 'Campina Grande do Sul', 2022, 'PR', 31, 'BR', -25.30556000, -49.05528000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1773372'),
(10964, 'Campina Verde', 1998, 'MG', 31, 'BR', -19.46113000, -49.73967000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1756064'),
(10965, 'Campina da Lagoa', 2022, 'PR', 31, 'BR', -24.60154000, -52.78545000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1756064'),
(10966, 'Campina das Missões', 2001, 'RS', 31, 'BR', -27.98729000, -54.83020000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q1756064'),
(10967, 'Campina do Monte Alegre', 2021, 'SP', 31, 'BR', -23.61364000, -48.45388000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1756064'),
(10968, 'Campina do Simão', 2022, 'PR', 31, 'BR', -25.08478000, -51.78423000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1756064'),
(10970, 'Campinas', 2021, 'SP', 31, 'BR', -22.90556000, -47.06083000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q171617'),
(10971, 'Campinas do Piauí', 2008, 'PI', 31, 'BR', -7.67905000, -41.87938000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q171617'),
(10972, 'Campinas do Sul', 2001, 'RS', 31, 'BR', -27.70629000, -52.63937000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q171617'),
(10973, 'Campinaçu', 2000, 'GO', 31, 'BR', -13.86371000, -48.56754000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q2365292'),
(10974, 'Campinorte', 2000, 'GO', 31, 'BR', -14.04500000, -48.97524000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2365292'),
(10975, 'Campinápolis', 2011, 'MT', 31, 'BR', -14.30712000, -53.15586000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q955067'),
(10976, 'Campo Alegre', 2007, 'AL', 31, 'BR', -9.78194000, -36.35083000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2028275'),
(10977, 'Campo Alegre', 2014, 'SC', 31, 'BR', -26.08532000, -49.18746000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2028275'),
(10978, 'Campo Alegre de Goiás', 2000, 'GO', 31, 'BR', -17.67336000, -47.77801000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q2028275'),
(10979, 'Campo Alegre de Lourdes', 2002, 'BA', 31, 'BR', -9.50097000, -42.98275000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2028275'),
(10980, 'Campo Alegre do Fidalgo', 2008, 'PI', 31, 'BR', -8.31452000, -41.79184000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2028275'),
(10981, 'Campo Azul', 1998, 'MG', 31, 'BR', -16.52161000, -44.80679000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1792812'),
(10982, 'Campo Belo', 1998, 'MG', 31, 'BR', -20.89722000, -45.27722000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q595804'),
(10983, 'Campo Belo do Sul', 2014, 'SC', 31, 'BR', -27.83940000, -50.76583000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q595804'),
(10984, 'Campo Bom', 2001, 'RS', 31, 'BR', -29.66716000, -51.05054000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q595804'),
(10985, 'Campo Bonito', 2022, 'PR', 31, 'BR', -24.88808000, -53.01009000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q595804'),
(10986, 'Campo Erê', 2014, 'SC', 31, 'BR', -26.45834000, -53.18257000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q1757768'),
(10987, 'Campo Florido', 1998, 'MG', 31, 'BR', -19.69262000, -48.65664000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1757768'),
(10988, 'Campo Formoso', 2002, 'BA', 31, 'BR', -10.50750000, -40.32139000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808046'),
(10989, 'Campo Grande', 2010, 'MS', 31, 'BR', -20.44278000, -54.64639000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q168929'),
(10990, 'Campo Grande', 2007, 'AL', 31, 'BR', -9.94997000, -36.75593000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1814259'),
(10991, 'Campo Grande do Piauí', 2008, 'PI', 31, 'BR', -7.22070000, -41.04511000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1814259'),
(10992, 'Campo Largo', 2022, 'PR', 31, 'BR', -25.45955000, -49.53014000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1026376'),
(10993, 'Campo Largo do Piauí', 2008, 'PI', 31, 'BR', -3.85518000, -42.60317000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1026376'),
(10994, 'Campo Limpo Paulista', 2021, 'SP', 31, 'BR', -23.21599000, -46.75842000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1754452'),
(10995, 'Campo Limpo de Goiás', 2000, 'GO', 31, 'BR', -16.30048000, -49.09041000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q989794'),
(10996, 'Campo Magro', 2022, 'PR', 31, 'BR', -25.26822000, -49.47580000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q989794'),
(10997, 'Campo Maior', 2008, 'PI', 31, 'BR', -4.82778000, -42.16861000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2105005'),
(10998, 'Campo Mourão', 2022, 'PR', 31, 'BR', -24.04309000, -52.37929000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1773327'),
(10999, 'Campo Novo', 2001, 'RS', 31, 'BR', -27.67313000, -53.81745000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1773327'),
(11000, 'Campo Novo de Rondônia', 2013, 'RO', 31, 'BR', -10.48146000, -63.85192000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q1773327'),
(11001, 'Campo Novo do Parecis', 2011, 'MT', 31, 'BR', -13.62657000, -58.02125000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1773327'),
(11002, 'Campo Redondo', 2019, 'RN', 31, 'BR', -6.24582000, -36.21969000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1773327'),
(11003, 'Campo Verde', 2010, 'MS', 31, 'BR', -20.41667000, -54.06667000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1773327'),
(11004, 'Campo Verde', 2011, 'MT', 31, 'BR', -15.37926000, -54.92950000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1773327'),
(11005, 'Campo do Brito', 2003, 'SE', 31, 'BR', -10.73333000, -37.49333000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1801972'),
(11006, 'Campo do Meio', 1998, 'MG', 31, 'BR', -21.12604000, -45.79572000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1801972'),
(11007, 'Campo do Tenente', 2022, 'PR', 31, 'BR', -25.95791000, -49.65936000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1801972'),
(11008, 'Campos Altos', 1998, 'MG', 31, 'BR', -19.63694000, -46.19815000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q585412'),
(11009, 'Campos Belos', 2000, 'GO', 31, 'BR', -13.03667000, -46.77167000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q991108'),
(11010, 'Campos Borges', 2001, 'RS', 31, 'BR', -28.89387000, -53.02543000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q991108'),
(11011, 'Campos Gerais', 1998, 'MG', 31, 'BR', -21.23500000, -45.75861000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1791960'),
(11012, 'Campos Lindos', 2020, 'TO', 31, 'BR', -8.22553000, -46.84507000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1791960'),
(11013, 'Campos Novos', 2014, 'SC', 31, 'BR', -27.40167000, -51.22500000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1764972'),
(11014, 'Campos Novos Paulista', 2021, 'SP', 31, 'BR', -22.62889000, -50.01282000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1764972'),
(11015, 'Campos Sales', 2016, 'CE', 31, 'BR', -6.92527000, -40.22188000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1811948'),
(11016, 'Campos Verdes', 2000, 'GO', 31, 'BR', -14.19022000, -49.65835000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1811948'),
(11017, 'Campos de Júlio', 2011, 'MT', 31, 'BR', -13.63297000, -59.17849000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1811948'),
(11018, 'Campos do Jordão', 2021, 'SP', 31, 'BR', -22.73944000, -45.59139000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q851253'),
(11019, 'Campos dos Goytacazes', 1997, 'RJ', 31, 'BR', -21.75227000, -41.33044000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22047120'),
(11020, 'Camutanga', 2006, 'PE', 31, 'BR', -7.43515000, -35.30172000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22047120'),
(11021, 'Cana Verde', 1998, 'MG', 31, 'BR', -21.02062000, -45.18335000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22047120'),
(11022, 'CanaBrava do Norte', 2011, 'MT', 31, 'BR', -11.18113000, -51.86378000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1805653'),
(11023, 'Cananéia', 2021, 'SP', 31, 'BR', -25.01472000, -47.92667000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q22047157'),
(11024, 'Canapi', 2007, 'AL', 31, 'BR', -9.17327000, -37.51985000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22047157'),
(11025, 'Canarana', 2002, 'BA', 31, 'BR', -11.68472000, -41.76889000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q674476'),
(11026, 'Canarana', 2011, 'MT', 31, 'BR', -13.17747000, -52.37104000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q519706'),
(11027, 'Canas', 2021, 'SP', 31, 'BR', -22.73999000, -45.03208000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1795838'),
(11028, 'Canavieira', 2008, 'PI', 31, 'BR', -7.50756000, -43.68630000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1795838'),
(11029, 'Canavieiras', 2002, 'BA', 31, 'BR', -15.67500000, -38.94722000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1772515'),
(11030, 'Canaã', 1998, 'MG', 31, 'BR', -20.66128000, -42.63787000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q610966'),
(11031, 'Canaã dos Carajás', 2009, 'PA', 31, 'BR', -6.49873000, -50.09334000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q610966'),
(11032, 'Candeal', 2002, 'BA', 31, 'BR', -11.89504000, -39.20390000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q610966'),
(11033, 'Candeias', 2002, 'BA', 31, 'BR', -12.65569000, -38.48700000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1762379'),
(11034, 'Candeias', 1998, 'MG', 31, 'BR', -20.73894000, -45.28719000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1906206'),
(11035, 'Candeias do Jamari', 2013, 'RO', 31, 'BR', -8.94512000, -63.35731000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1906206'),
(11036, 'Candelária', 2001, 'RS', 31, 'BR', -29.71623000, -52.80968000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q1786799'),
(11037, 'Candiba', 2002, 'BA', 31, 'BR', -14.46972000, -42.87386000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1786799'),
(11038, 'Candiota', 2001, 'RS', 31, 'BR', -31.49089000, -53.70274000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1786799'),
(11039, 'Candói', 2022, 'PR', 31, 'BR', -25.51315000, -52.02243000, '2019-10-05 22:35:00', '2020-05-01 17:22:37', 1, 'Q1786799'),
(11040, 'Canela', 2001, 'RS', 31, 'BR', -29.34715000, -50.77536000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q995318'),
(11041, 'Canelinha', 2014, 'SC', 31, 'BR', -27.24874000, -48.78781000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q995318'),
(11042, 'Canguaretama', 2019, 'RN', 31, 'BR', -6.38000000, -35.12889000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1787055'),
(11043, 'Canguçu', 2001, 'RS', 31, 'BR', -31.39500000, -52.67556000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q984559'),
(11044, 'Canhoba', 2003, 'SE', 31, 'BR', -10.14847000, -37.00031000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1801984'),
(11045, 'Canhotinho', 2006, 'PE', 31, 'BR', -8.88222000, -36.19111000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2064820'),
(11046, 'Canindé', 2016, 'CE', 31, 'BR', -4.40473000, -39.41746000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1008278'),
(11047, 'Canindé de São Francisco', 2003, 'SE', 31, 'BR', -9.66000000, -37.78944000, '2019-10-05 22:35:00', '2020-05-01 17:22:38', 1, 'Q1807399'),
(11048, 'Canitar', 2021, 'SP', 31, 'BR', -23.01421000, -49.78705000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1807399'),
(11049, 'Canoas', 2001, 'RS', 31, 'BR', -29.91778000, -51.18361000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q194325'),
(11050, 'Canoinhas', 2014, 'SC', 31, 'BR', -26.17722000, -50.39000000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1799304'),
(11051, 'Cansanção', 2002, 'BA', 31, 'BR', -10.73105000, -39.47418000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1799304'),
(11052, 'Cantagalo', 1997, 'RJ', 31, 'BR', -21.98111000, -42.36806000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1763072'),
(11053, 'Cantagalo', 1998, 'MG', 31, 'BR', -18.51033000, -42.64980000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22062707'),
(11054, 'Cantagalo', 2022, 'PR', 31, 'BR', -25.29530000, -52.15269000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q22062707'),
(11055, 'Cantanhede', 2015, 'MA', 31, 'BR', -3.63333000, -44.37667000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2103724'),
(11056, 'Canto do Buriti', 2008, 'PI', 31, 'BR', -8.11000000, -42.94444000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q2079682'),
(11057, 'Canudos', 2002, 'BA', 31, 'BR', -9.87981000, -39.14723000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q653656'),
(11058, 'Canudos do Vale', 2001, 'RS', 31, 'BR', -29.32055000, -52.22476000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q645413'),
(11059, 'Canutama', 2004, 'AM', 31, 'BR', -6.53389000, -64.38306000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1807050'),
(11060, 'Canápolis', 2002, 'BA', 31, 'BR', -13.12516000, -44.25095000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q1807050'),
(11061, 'Canápolis', 1998, 'MG', 31, 'BR', -18.77777000, -49.27579000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q22062710'),
(11062, 'Capanema', 2009, 'PA', 31, 'BR', -1.19583000, -47.18083000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1947031'),
(11063, 'Capanema', 2022, 'PR', 31, 'BR', -25.61839000, -53.79262000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1947031'),
(11064, 'Caparaó', 1998, 'MG', 31, 'BR', -20.52613000, -41.90220000, '2019-10-05 22:35:00', '2020-05-01 17:22:36', 1, 'Q742409'),
(11065, 'Capela', 2003, 'SE', 31, 'BR', -10.50333000, -37.05278000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q726730'),
(11066, 'Capela', 2007, 'AL', 31, 'BR', -9.39342000, -36.12390000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1955261'),
(11067, 'Capela Nova', 1998, 'MG', 31, 'BR', -20.91843000, -43.61587000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1955261'),
(11068, 'Capela de Santana', 2001, 'RS', 31, 'BR', -29.71236000, -51.38078000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1955261'),
(11069, 'Capela do Alto', 2021, 'SP', 31, 'BR', -23.47056000, -47.73472000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q610390'),
(11070, 'Capela do Alto Alegre', 2002, 'BA', 31, 'BR', -11.61838000, -39.83227000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q610390'),
(11071, 'Capelinha', 1998, 'MG', 31, 'BR', -17.69167000, -42.50214000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1756225'),
(11072, 'Capetinga', 1998, 'MG', 31, 'BR', -20.65301000, -47.01826000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1756225'),
(11073, 'Capim', 2005, 'PB', 31, 'BR', -6.89150000, -35.18300000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1808017'),
(11074, 'Capim Branco', 1998, 'MG', 31, 'BR', -19.57826000, -44.16662000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1792057'),
(11075, 'Capim Grosso', 2002, 'BA', 31, 'BR', -11.38111000, -40.01278000, '2019-10-05 22:35:00', '2019-10-05 22:35:00', 1, 'Q1795362'),
(11076, 'Capinzal', 2014, 'SC', 31, 'BR', -27.34361000, -51.61194000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q780452'),
(11077, 'Capinzal do Norte', 2015, 'MA', 31, 'BR', -4.74767000, -44.25167000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q780452'),
(11078, 'Capinópolis', 1998, 'MG', 31, 'BR', -18.69222000, -49.57943000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1791729'),
(11079, 'Capistrano', 2016, 'CE', 31, 'BR', -4.46230000, -38.92072000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2021203'),
(11080, 'Capitão', 2001, 'RS', 31, 'BR', -29.28020000, -51.98317000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1758752'),
(11081, 'Capitão Andrade', 1998, 'MG', 31, 'BR', -19.04293000, -41.82608000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1791913'),
(11082, 'Capitão Enéas', 1998, 'MG', 31, 'BR', -16.05629000, -43.67470000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1791913'),
(11083, 'Capitão Gervásio Oliveira', 2008, 'PI', 31, 'BR', -8.52938000, -41.90126000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1791913'),
(11084, 'Capitão Leônidas Marques', 2022, 'PR', 31, 'BR', -25.47998000, -53.58797000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1791913'),
(11085, 'Capitão Poço', 2009, 'PA', 31, 'BR', -1.74639000, -47.05944000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q955914'),
(11086, 'Capitão de Campos', 2008, 'PI', 31, 'BR', -4.47203000, -41.88512000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q955914'),
(11087, 'Capitólio', 1998, 'MG', 31, 'BR', -20.61528000, -46.05000000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1257195'),
(11088, 'Capivari', 2021, 'SP', 31, 'BR', -22.99500000, -47.50778000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1750439'),
(11089, 'Capivari de Baixo', 2014, 'SC', 31, 'BR', -28.45237000, -48.95364000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1750439'),
(11090, 'Capivari do Sul', 2001, 'RS', 31, 'BR', -30.17289000, -50.48702000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1750439'),
(11091, 'Capixaba', 2012, 'AC', 31, 'BR', -10.48782000, -67.84831000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1750439'),
(11092, 'Capoeiras', 2006, 'PE', 31, 'BR', -8.73472000, -36.62667000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2064882'),
(11093, 'Caputira', 1998, 'MG', 31, 'BR', -20.18512000, -42.25284000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2064882'),
(11094, 'Capão Bonito', 2021, 'SP', 31, 'BR', -24.00583000, -48.34944000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1798802'),
(11095, 'Capão Alto', 2014, 'SC', 31, 'BR', -28.06834000, -50.62921000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1798802'),
(11097, 'Capão Bonito do Sul', 2001, 'RS', 31, 'BR', -28.17432000, -51.42298000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q765830'),
(11098, 'Capão da Canoa', 2001, 'RS', 31, 'BR', -29.66118000, -50.01422000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q958607'),
(11099, 'Capão do Cipó', 2001, 'RS', 31, 'BR', -28.94987000, -54.60368000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1803147'),
(11100, 'Capão do Leão', 2001, 'RS', 31, 'BR', -31.84324000, -52.55323000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1803147'),
(11101, 'Caracol', 2008, 'PI', 31, 'BR', -9.34943000, -43.27078000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1803147'),
(11102, 'Caracol', 2010, 'MS', 31, 'BR', -21.96712000, -57.12672000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1803147'),
(11103, 'Caraguatatuba', 2021, 'SP', 31, 'BR', -23.62028000, -45.41306000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q983498'),
(11104, 'Carambeí', 2022, 'PR', 31, 'BR', -24.95260000, -50.11590000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1803923'),
(11105, 'Caranaíba', 1998, 'MG', 31, 'BR', -20.88526000, -43.71231000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1803923'),
(11106, 'Carandaí', 1998, 'MG', 31, 'BR', -20.98840000, -43.83413000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1792927'),
(11107, 'Carangola', 1998, 'MG', 31, 'BR', -20.70784000, -42.10486000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1793063'),
(11108, 'Carapebus', 1997, 'RJ', 31, 'BR', -22.22429000, -41.61306000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1793063'),
(11109, 'Carapicuíba', 2021, 'SP', 31, 'BR', -23.55339000, -46.84852000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q176891'),
(11110, 'Caratinga', 1998, 'MG', 31, 'BR', -19.68877000, -41.88778000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q905175'),
(11111, 'Carauari', 2004, 'AM', 31, 'BR', -4.88278000, -66.89583000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1794236'),
(11112, 'Caravelas', 2002, 'BA', 31, 'BR', -17.65842000, -39.35989000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1794236'),
(11113, 'Carazinho', 2001, 'RS', 31, 'BR', -28.28389000, -52.78639000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q772910'),
(11114, 'Caraá', 2001, 'RS', 31, 'BR', -29.77397000, -50.35369000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q772910'),
(11115, 'Caraí', 1998, 'MG', 31, 'BR', -17.18089000, -41.54964000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q584168'),
(11116, 'Caraíbas', 2002, 'BA', 31, 'BR', -14.60631000, -41.25763000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1800637'),
(11117, 'Caraúbas', 2019, 'RN', 31, 'BR', -5.79250000, -37.55667000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q2078721'),
(11118, 'Caraúbas', 2005, 'PB', 31, 'BR', -7.79092000, -36.50278000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q2078721'),
(11119, 'Caraúbas do Piauí', 2008, 'PI', 31, 'BR', -3.53699000, -41.84588000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q2078721'),
(11120, 'Carbonita', 1998, 'MG', 31, 'BR', -17.47884000, -43.04885000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2078721'),
(11121, 'Cardeal da Silva', 2002, 'BA', 31, 'BR', -11.99798000, -37.92468000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2078721'),
(11122, 'Cardoso', 2021, 'SP', 31, 'BR', -20.08194000, -49.91417000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1649954'),
(11123, 'Cardoso Moreira', 1997, 'RJ', 31, 'BR', -21.51166000, -41.46121000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1649954'),
(11124, 'Careaçu', 1998, 'MG', 31, 'BR', -22.07964000, -45.66541000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1649954'),
(11125, 'Careiro', 2004, 'AM', 31, 'BR', -3.78706000, -60.34790000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1649954'),
(11126, 'Careiro da Várzea', 2004, 'AM', 31, 'BR', -3.19695000, -59.82674000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1807045'),
(11127, 'Cariacica', 2018, 'ES', 31, 'BR', -20.30050000, -40.46919000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1807045'),
(11128, 'Carianos', 2014, 'SC', 31, 'BR', -27.66088000, -48.54318000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1807045'),
(11129, 'Caridade', 2016, 'CE', 31, 'BR', -4.20100000, -39.14149000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2027136'),
(11130, 'Caridade do Piauí', 2008, 'PI', 31, 'BR', -7.70139000, -40.89597000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q2027136'),
(11131, 'Carinhanha', 2002, 'BA', 31, 'BR', -14.30472000, -43.76500000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q985608'),
(11132, 'Carira', 2003, 'SE', 31, 'BR', -10.36555000, -37.74990000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2013076'),
(11133, 'Cariri do Tocantins', 2020, 'TO', 31, 'BR', -11.94500000, -49.20796000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2013076'),
(11134, 'Caririaçu', 2016, 'CE', 31, 'BR', -7.04170000, -39.25423000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q2028263'),
(11135, 'Cariré', 2016, 'CE', 31, 'BR', -3.93586000, -40.54679000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q2021245'),
(11136, 'Cariús', 2016, 'CE', 31, 'BR', -6.65619000, -39.47496000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1811382'),
(11137, 'Carlinda', 2011, 'MT', 31, 'BR', -10.03792000, -55.85086000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1811382'),
(11138, 'Carlos Barbosa', 2001, 'RS', 31, 'BR', -29.29750000, -51.50361000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1757704'),
(11139, 'Carlos Chagas', 1998, 'MG', 31, 'BR', -17.67817000, -40.88519000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1650857'),
(11140, 'Carlos Gomes', 2001, 'RS', 31, 'BR', -27.71183000, -51.90645000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1758557'),
(11141, 'Carlópolis', 2022, 'PR', 31, 'BR', -23.44971000, -49.70142000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1758557'),
(11142, 'Carmo', 1997, 'RJ', 31, 'BR', -21.89966000, -42.53760000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1765089'),
(11143, 'Carmo da Cachoeira', 1998, 'MG', 31, 'BR', -21.43323000, -45.18036000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1765089'),
(11144, 'Carmo da Mata', 1998, 'MG', 31, 'BR', -20.56847000, -44.88516000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1765089'),
(11145, 'Carmo de Minas', 1998, 'MG', 31, 'BR', -22.08863000, -45.14949000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1765089'),
(11146, 'Carmo do Cajuru', 1998, 'MG', 31, 'BR', -20.18782000, -44.71399000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22062685'),
(11147, 'Carmo do Paranaíba', 1998, 'MG', 31, 'BR', -18.87335000, -46.14425000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1792297'),
(11148, 'Carmo do Rio Claro', 1998, 'MG', 31, 'BR', -20.97974000, -46.10445000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1756452'),
(11149, 'Carmo do Rio Verde', 2000, 'GO', 31, 'BR', -15.35361000, -49.70750000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1805778'),
(11150, 'Carmolândia', 2020, 'TO', 31, 'BR', -7.03001000, -48.35627000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1805778'),
(11151, 'Carmésia', 1998, 'MG', 31, 'BR', -19.06534000, -43.18402000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1805778'),
(11152, 'Carmópolis', 2003, 'SE', 31, 'BR', -10.66710000, -36.95751000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1805778'),
(11153, 'Carmópolis de Minas', 1998, 'MG', 31, 'BR', -20.54914000, -44.64527000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1805778'),
(11154, 'Carnaubais', 2019, 'RN', 31, 'BR', -5.26459000, -36.79459000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1805778'),
(11155, 'Carnaubal', 2016, 'CE', 31, 'BR', -4.12097000, -41.01368000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1805778'),
(11156, 'Carnaubeira da Penha', 2006, 'PE', 31, 'BR', -8.42648000, -38.76523000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1805778'),
(11157, 'Carnaíba', 2006, 'PE', 31, 'BR', -8.70000000, -37.66667000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q22024027'),
(11158, 'Carnaúba dos Dantas', 2019, 'RN', 31, 'BR', -6.56162000, -36.52903000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q22024027'),
(11159, 'Carneirinho', 1998, 'MG', 31, 'BR', -19.71424000, -50.80874000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22024027'),
(11160, 'Carneiros', 2007, 'AL', 31, 'BR', -9.46467000, -37.35559000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22024027'),
(11161, 'Carolina', 2015, 'MA', 31, 'BR', -7.33561000, -47.46218000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q651807'),
(11162, 'Carpina', 2006, 'PE', 31, 'BR', -7.85083000, -35.25472000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1993877'),
(11163, 'Carrancas', 1998, 'MG', 31, 'BR', -21.49589000, -44.61042000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1756048'),
(11164, 'Carrapateira', 2005, 'PB', 31, 'BR', -7.02919000, -38.32951000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q749757'),
(11165, 'Carrasco Bonito', 2020, 'TO', 31, 'BR', -5.31027000, -48.03360000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q749757'),
(11166, 'Caruaru', 2006, 'PE', 31, 'BR', -8.17924000, -36.02794000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q622830'),
(11167, 'Carutapera', 2015, 'MA', 31, 'BR', -1.18025000, -45.95966000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2016480'),
(11168, 'Carvalhos', 1998, 'MG', 31, 'BR', -22.03096000, -44.47544000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q741443'),
(11169, 'Carvalhópolis', 1998, 'MG', 31, 'BR', -21.77488000, -45.82688000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q741443');

