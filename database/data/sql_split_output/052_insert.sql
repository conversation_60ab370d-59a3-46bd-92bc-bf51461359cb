INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(25833, 'Haiming', 3009, 'BY', 82, 'DE', 48.21667000, 12.90000000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q512178'),
(25834, 'Hain-G<PERSON><PERSON>ndau', 3018, 'HE', 82, 'DE', 50.24284000, 9.14287000, '2019-10-05 22:41:00', '2020-05-01 17:22:48', 1, 'Q1569331'),
(25835, 'Haina', 3015, 'TH', 82, 'DE', 50.98971000, 10.51774000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1569331'),
(25836, 'Haina', 3018, '<PERSON><PERSON>', 82, '<PERSON>', 51.02774000, 8.97441000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q636145'),
(25837, '<PERSON><PERSON><PERSON><PERSON>', 3021, 'S<PERSON>', 82, '<PERSON>', 50.91014000, 14.70387000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q502493'),
(25838, '<PERSON><PERSON><PERSON>', 3021, 'SN', 82, 'DE', 50.97044000, 13.12287000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q502493'),
(25839, 'Hainsfarth', 3009, 'BY', 82, 'DE', 48.95839000, 10.62491000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q502493'),
(25840, 'Haiterbach', 3006, 'BW', 82, 'DE', 48.52066000, 8.64435000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q239370'),
(25841, 'Hakenfelde', 3010, 'BE', 82, 'DE', 52.55877000, 13.20831000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q569895'),
(25842, 'Halbe', 3013, 'BB', 82, 'DE', 52.11667000, 13.70000000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q531652'),
(25843, 'Halbemond', 3008, 'NI', 82, 'DE', 53.56710000, 7.29038000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q634380'),
(25844, 'Halberstadt', 3011, 'ST', 82, 'DE', 51.89562000, 11.05622000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q7072'),
(25845, 'Halblech', 3009, 'BY', 82, 'DE', 47.63155000, 10.82024000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q514471'),
(25846, 'Haldensleben I', 3011, 'ST', 82, 'DE', 52.28906000, 11.40982000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q186557'),
(25847, 'Haldenwang', 3009, 'BY', 82, 'DE', 47.80000000, 10.35000000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q566563'),
(25848, 'Halensee', 3010, 'BE', 82, 'DE', 52.49005000, 13.29602000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q651009'),
(25849, 'Halfing', 3009, 'BY', 82, 'DE', 47.95197000, 12.27525000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q651009'),
(25850, 'Hallbergmoos', 3009, 'BY', 82, 'DE', 48.32747000, 11.75142000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q285955'),
(25851, 'Halle', 3017, 'NW', 82, 'DE', 52.06007000, 8.36083000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q225774'),
(25852, 'Halle', 3008, 'NI', 82, 'DE', 51.99122000, 9.56532000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q32051125'),
(25853, 'Halle (Saale)', 3011, 'ST', 82, 'DE', 51.48158000, 11.97947000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2814'),
(25854, 'Halle Neustadt', 3011, 'ST', 82, 'DE', 51.47924000, 11.91605000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2814'),
(25855, 'Hallenberg', 3017, 'NW', 82, 'DE', 51.11115000, 8.62008000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q5644'),
(25856, 'Hallerndorf', 3009, 'BY', 82, 'DE', 49.75909000, 10.97946000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q502796'),
(25857, 'Hallstadt', 3009, 'BY', 82, 'DE', 49.92901000, 10.87539000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q302632'),
(25858, 'Halsbrücke', 3021, 'SN', 82, 'DE', 50.95000000, 13.35000000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q70964'),
(25859, 'Halsenbach', 3019, 'RP', 82, 'DE', 50.17174000, 7.55673000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q561631'),
(25860, 'Halstenbek', 3005, 'SH', 82, 'DE', 53.63333000, 9.85000000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q542824'),
(25861, 'Haltern am See', 3017, 'NW', 82, 'DE', 51.74297000, 7.18163000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q14900'),
(25862, 'Halver', 3017, 'NW', 82, 'DE', 51.18612000, 7.49817000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q5567'),
(25863, 'Hamberge', 3005, 'SH', 82, 'DE', 53.83333000, 10.58333000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q118546'),
(25864, 'Hambergen', 3008, 'NI', 82, 'DE', 53.30826000, 8.82520000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q645177'),
(25865, 'Hambrücken', 3006, 'BW', 82, 'DE', 49.19000000, 8.54056000, '2019-10-05 22:41:00', '2020-05-01 17:22:47', 1, 'Q520473'),
(25866, 'Hamburg', 3016, 'HH', 82, 'DE', 53.57532000, 10.01534000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1055'),
(25867, 'Hamburg-Altstadt', 3016, 'HH', 82, 'DE', 53.55000000, 10.00000000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1055'),
(25868, 'Hamburg-Mitte', 3016, 'HH', 82, 'DE', 53.55000000, 10.01667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1626'),
(25869, 'Hamburg-Nord', 3016, 'HH', 82, 'DE', 53.58935000, 9.98400000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1627'),
(25870, 'Hambühren', 3008, 'NI', 82, 'DE', 52.63333000, 9.98333000, '2019-10-05 22:41:00', '2020-05-01 17:22:48', 1, 'Q371560'),
(25871, 'Hamdorf', 3005, 'SH', 82, 'DE', 54.22522000, 9.51866000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q371560'),
(25872, 'Hameln', 3008, 'NI', 82, 'DE', 52.10397000, 9.35623000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q4062'),
(25873, 'Hamm', 3017, 'NW', 82, 'DE', 51.68033000, 7.82089000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2880'),
(25874, 'Hamm', 3019, 'RP', 82, 'DE', 50.76531000, 7.67761000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2880'),
(25875, 'Hammah', 3008, 'NI', 82, 'DE', 53.61667000, 9.36667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2880'),
(25876, 'Hammelburg', 3009, 'BY', 82, 'DE', 50.11633000, 9.89143000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q302785'),
(25877, 'Hammerbrook', 3016, 'HH', 82, 'DE', 53.54527000, 10.03042000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1570'),
(25878, 'Hammerbrücke', 3021, 'SN', 82, 'DE', 50.43504000, 12.41500000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q695636'),
(25879, 'Hammersbach', 3018, 'HE', 82, 'DE', 50.21667000, 8.98333000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q695636'),
(25880, 'Hamminkeln', 3017, 'NW', 82, 'DE', 51.73262000, 6.59031000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q22692196'),
(25881, 'Hammoor', 3005, 'SH', 82, 'DE', 53.71378000, 10.32200000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q641623'),
(25882, 'Hanau am Main', 3018, 'HE', 82, 'DE', 50.13423000, 8.91418000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q3802'),
(25883, 'Handeloh', 3008, 'NI', 82, 'DE', 53.24563000, 9.83923000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q25622034'),
(25884, 'Handewitt', 3005, 'SH', 82, 'DE', 54.76667000, 9.33333000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q551497'),
(25885, 'Handorf', 3008, 'NI', 82, 'DE', 53.34084000, 10.34652000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q658681'),
(25886, 'Hanerau-Hademarschen', 3005, 'SH', 82, 'DE', 54.13333000, 9.41667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q631638'),
(25887, 'Hangard', 3020, 'SL', 82, 'DE', 49.38260000, 7.21046000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q631638'),
(25888, 'Hanhofen', 3019, 'RP', 82, 'DE', 49.31667000, 8.34083000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q567293'),
(25889, 'Hankensbüttel', 3008, 'NI', 82, 'DE', 52.73333000, 10.60000000, '2019-10-05 22:41:00', '2020-05-01 17:22:48', 1, 'Q633148'),
(25890, 'Hannover', 3008, 'NI', 82, 'DE', 52.37052000, 9.73322000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1715'),
(25891, 'Hannoversch Münden', 3008, 'NI', 82, 'DE', 51.41505000, 9.65046000, '2019-10-05 22:41:00', '2020-05-01 17:22:48', 1, 'Q165380'),
(25892, 'Hansaviertel', 3010, 'BE', 82, 'DE', 52.51853000, 13.34178000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q427582'),
(25893, 'Hanstedt', 3008, 'NI', 82, 'DE', 53.26667000, 10.01667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q427582'),
(25894, 'Hanstedt Eins', 3008, 'NI', 82, 'DE', 53.04642000, 10.37444000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q427582'),
(25895, 'Happurg', 3009, 'BY', 82, 'DE', 49.49372000, 11.47119000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q552430'),
(25896, 'Harbke', 3011, 'ST', 82, 'DE', 52.18877000, 11.04624000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q630423'),
(25897, 'Harburg', 3016, 'HH', 82, 'DE', 53.46057000, 9.98388000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q1635'),
(25898, 'Harburg', 3009, 'BY', 82, 'DE', 48.78674000, 10.68927000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q503226'),
(25899, 'Hardegsen', 3008, 'NI', 82, 'DE', 51.65227000, 9.83050000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q425877'),
(25900, 'Hardheim', 3006, 'BW', 82, 'DE', 49.61194000, 9.47194000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q539852'),
(25901, 'Hardt', 3006, 'BW', 82, 'DE', 48.18333000, 8.41667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q539852'),
(25902, 'Haren', 3008, 'NI', 82, 'DE', 52.79262000, 7.24142000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q32052824'),
(25903, 'Hargesheim', 3019, 'RP', 82, 'DE', 49.86230000, 7.82881000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q567710'),
(25904, 'Harpstedt', 3008, 'NI', 82, 'DE', 52.90942000, 8.58962000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q628274'),
(25905, 'Harrislee', 3005, 'SH', 82, 'DE', 54.80000000, 9.38333000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q539243'),
(25906, 'Harsdorf', 3009, 'BY', 82, 'DE', 50.03333000, 11.56667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q511032'),
(25907, 'Harsefeld', 3008, 'NI', 82, 'DE', 53.45399000, 9.50297000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q503819'),
(25908, 'Harsewinkel', 3017, 'NW', 82, 'DE', 51.96224000, 8.22766000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q225729'),
(25909, 'Harsleben', 3011, 'ST', 82, 'DE', 51.86667000, 11.10000000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q564281'),
(25910, 'Harsum', 3008, 'NI', 82, 'DE', 52.21045000, 9.96486000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q531536'),
(25911, 'Hartenholm', 3005, 'SH', 82, 'DE', 53.89856000, 10.05953000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q531536'),
(25912, 'Hartenstein', 3009, 'BY', 82, 'DE', 49.60000000, 11.51667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q531536'),
(25913, 'Hartenstein', 3021, 'SN', 82, 'DE', 50.66236000, 12.66966000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q32053289'),
(25914, 'Hartha', 3021, 'SN', 82, 'DE', 51.09863000, 12.97391000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q32053323'),
(25915, 'Harthausen', 3019, 'RP', 82, 'DE', 49.29417000, 8.34500000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q566942'),
(25916, 'Hartheim', 3006, 'BW', 82, 'DE', 47.93333000, 7.63333000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q566942'),
(25917, 'Hartmannsdorf', 3021, 'SN', 82, 'DE', 50.75804000, 12.42687000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q32053384'),
(25918, 'Harxheim', 3019, 'RP', 82, 'DE', 49.90528000, 8.26417000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q630902'),
(25919, 'Harzgerode', 3011, 'ST', 82, 'DE', 51.64189000, 11.14330000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q526353'),
(25920, 'Hasbergen', 3008, 'NI', 82, 'DE', 52.23754000, 7.96114000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q685358'),
(25921, 'Hasel', 3006, 'BW', 82, 'DE', 47.65342000, 7.89720000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q685358'),
(25922, 'Haselau', 3005, 'SH', 82, 'DE', 53.66181000, 9.62010000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q566114'),
(25923, 'Haselbach', 3009, 'BY', 82, 'DE', 48.64564000, 13.38956000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q32053546'),
(25924, 'Haselbachtal', 3021, 'SN', 82, 'DE', 51.23570000, 14.02576000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q93247'),
(25925, 'Haseldorf', 3005, 'SH', 82, 'DE', 53.63780000, 9.59151000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q93247'),
(25926, 'Haselhorst', 3010, 'BE', 82, 'DE', 52.54409000, 13.23743000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q266024'),
(25927, 'Haselünne', 3008, 'NI', 82, 'DE', 52.67412000, 7.48460000, '2019-10-05 22:41:00', '2020-05-01 17:22:48', 1, 'Q498521'),
(25928, 'Haslach', 3006, 'BW', 82, 'DE', 48.56531000, 8.05658000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q498521'),
(25929, 'Hasloch', 3009, 'BY', 82, 'DE', 49.79194000, 9.49361000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q572318'),
(25930, 'Hasloh', 3005, 'SH', 82, 'DE', 53.69471000, 9.91682000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q566123'),
(25931, 'Hassel', 3008, 'NI', 82, 'DE', 52.69688000, 8.83198000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q566123'),
(25932, 'Hasselfelde', 3011, 'ST', 82, 'DE', 51.69051000, 10.85373000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q50867'),
(25933, 'Hassendorf', 3008, 'NI', 82, 'DE', 53.10859000, 9.26482000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q506853'),
(25934, 'Haste', 3008, 'NI', 82, 'DE', 52.38105000, 9.39280000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q506853'),
(25935, 'Hatten', 3008, 'NI', 82, 'DE', 53.05000000, 8.38333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q506853'),
(25936, 'Hattenhofen', 3009, 'BY', 82, 'DE', 48.22231000, 11.11551000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q506853'),
(25937, 'Hattenhofen', 3006, 'BW', 82, 'DE', 48.66648000, 9.57456000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q506853'),
(25938, 'Hattersheim', 3018, 'HE', 82, 'DE', 50.07854000, 8.47552000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q569626'),
(25939, 'Hattert', 3019, 'RP', 82, 'DE', 50.66667000, 7.76667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q640369'),
(25940, 'Hattingen', 3017, 'NW', 82, 'DE', 51.39894000, 7.18557000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32054087'),
(25941, 'Hattorf', 3008, 'NI', 82, 'DE', 51.65035000, 10.23681000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32054087'),
(25942, 'Hattstedt', 3005, 'SH', 82, 'DE', 54.53333000, 9.03333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q21135'),
(25943, 'Hatzenbühl', 3019, 'RP', 82, 'DE', 49.11111000, 8.24528000, '2019-10-05 22:41:01', '2020-05-01 17:22:49', 1, 'Q567280'),
(25944, 'Hatzfeld', 3018, 'HE', 82, 'DE', 50.99334000, 8.54570000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q565504'),
(25945, 'Hauenstein', 3019, 'RP', 82, 'DE', 49.19211000, 7.85492000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q565504'),
(25946, 'Haundorf', 3009, 'BY', 82, 'DE', 49.17598000, 10.77124000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q525879'),
(25947, 'Haunsheim', 3009, 'BY', 82, 'DE', 48.59977000, 10.37401000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q174658'),
(25948, 'Hauptstuhl', 3019, 'RP', 82, 'DE', 49.40000000, 7.48333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q567693'),
(25949, 'Hausach', 3006, 'BW', 82, 'DE', 48.28426000, 8.17602000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q506856'),
(25950, 'Hausen', 3009, 'BY', 82, 'DE', 48.85953000, 12.00630000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q261177'),
(25951, 'Hausen', 3019, 'RP', 82, 'DE', 50.54242000, 7.40703000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32054294'),
(25952, 'Hausen', 3006, 'BW', 82, 'DE', 47.68128000, 7.83986000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32054294'),
(25953, 'Hausham', 3009, 'BY', 82, 'DE', 47.74660000, 11.84069000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q507380'),
(25954, 'Hauswalde', 3021, 'SN', 82, 'DE', 51.15212000, 14.08630000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q93211'),
(25955, 'Hauzenberg', 3009, 'BY', 82, 'DE', 48.64957000, 13.62645000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q255888'),
(25956, 'Havelberg', 3011, 'ST', 82, 'DE', 52.83088000, 12.07552000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q504040'),
(25957, 'Haverlah', 3008, 'NI', 82, 'DE', 52.06667000, 10.16667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32054451'),
(25958, 'Havixbeck', 3017, 'NW', 82, 'DE', 51.98333000, 7.41667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32054469'),
(25959, 'Hawangen', 3009, 'BY', 82, 'DE', 47.96667000, 10.26667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q529033'),
(25960, 'Hayingen', 3006, 'BW', 82, 'DE', 48.27531000, 9.47760000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q82453'),
(25961, 'Haßbergen', 3008, 'NI', 82, 'DE', 52.73333000, 9.23333000, '2019-10-05 22:41:01', '2020-05-01 17:22:48', 1, 'Q82453'),
(25962, 'Haßfurt', 3009, 'BY', 82, 'DE', 50.03521000, 10.51560000, '2019-10-05 22:41:01', '2020-05-01 17:22:47', 1, 'Q492659'),
(25963, 'Haßleben', 3015, 'TH', 82, 'DE', 51.10878000, 10.99637000, '2019-10-05 22:41:01', '2020-05-01 17:22:50', 1, 'Q556513'),
(25964, 'Haßloch', 3019, 'RP', 82, 'DE', 49.36278000, 8.25806000, '2019-10-05 22:41:01', '2020-05-01 17:22:49', 1, 'Q664857'),
(25965, 'Haßmersheim', 3006, 'BW', 82, 'DE', 49.30000000, 9.15000000, '2019-10-05 22:41:01', '2020-05-01 17:22:47', 1, 'Q537756'),
(25966, 'Hebertsfelden', 3009, 'BY', 82, 'DE', 48.40596000, 12.82259000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q553780'),
(25967, 'Hebertshausen', 3009, 'BY', 82, 'DE', 48.28959000, 11.46526000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q509243'),
(25968, 'Hechingen', 3006, 'BW', 82, 'DE', 48.35149000, 8.96317000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q494529'),
(25969, 'Hechthausen', 3008, 'NI', 82, 'DE', 53.64041000, 9.23942000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q687692'),
(25970, 'Hecklingen', 3011, 'ST', 82, 'DE', 51.84705000, 11.53416000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q374159'),
(25971, 'Heddesheim', 3006, 'BW', 82, 'DE', 49.50556000, 8.60361000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q83154'),
(25972, 'Hedersleben', 3011, 'ST', 82, 'DE', 51.54891000, 11.64983000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q701866'),
(25973, 'Heede', 3008, 'NI', 82, 'DE', 52.99205000, 7.29830000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q701866'),
(25974, 'Heek', 3017, 'NW', 82, 'DE', 52.11667000, 7.10000000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q701866'),
(25975, 'Heemsen', 3008, 'NI', 82, 'DE', 52.70000000, 9.26667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q557407'),
(25976, 'Heere', 3008, 'NI', 82, 'DE', 52.06667000, 10.25000000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q633835'),
(25977, 'Heeslingen', 3008, 'NI', 82, 'DE', 53.31667000, 9.33333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q633835'),
(25978, 'Heeßen', 3008, 'NI', 82, 'DE', 52.23211000, 9.09642000, '2019-10-05 22:41:01', '2020-05-01 17:22:48', 1, 'Q670395'),
(25979, 'Hehlen', 3008, 'NI', 82, 'DE', 51.98858000, 9.47004000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q238513'),
(25980, 'Heide', 3005, 'SH', 82, 'DE', 54.19579000, 9.09880000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q238513'),
(25981, 'Heideck', 3009, 'BY', 82, 'DE', 49.13365000, 11.12726000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q502445'),
(25982, 'Heidelberg', 3006, 'BW', 82, 'DE', 49.40768000, 8.69079000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q2966'),
(25983, 'Heiden', 3017, 'NW', 82, 'DE', 51.83333000, 6.93333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q1594131'),
(25984, 'Heidenau', 3008, 'NI', 82, 'DE', 53.31667000, 9.66667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q1594131'),
(25985, 'Heidenau', 3021, 'SN', 82, 'DE', 50.97221000, 13.86741000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q6715'),
(25986, 'Heidenheim', 3009, 'BY', 82, 'DE', 49.01719000, 10.74347000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q548982'),
(25987, 'Heidenheim an der Brenz', 3006, 'BW', 82, 'DE', 48.67798000, 10.15162000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q6869'),
(25988, 'Heidesheim', 3019, 'RP', 82, 'DE', 49.58247000, 8.19537000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32055492'),
(25989, 'Heidgraben', 3005, 'SH', 82, 'DE', 53.70787000, 9.68099000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q524535'),
(25990, 'Heidstock', 3020, 'SL', 82, 'DE', 49.25571000, 6.88156000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q524535'),
(25991, 'Heigenbrücken', 3009, 'BY', 82, 'DE', 50.02820000, 9.37116000, '2019-10-05 22:41:01', '2020-05-01 17:22:47', 1, 'Q504311'),
(25992, 'Heikendorf', 3005, 'SH', 82, 'DE', 54.36667000, 10.20000000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q629415'),
(25993, 'Heilbad Heiligenstadt', 3015, 'TH', 82, 'DE', 51.37819000, 10.13744000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q564765'),
(25994, 'Heilbronn', 3006, 'BW', 82, 'DE', 49.13995000, 9.22054000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q715'),
(25995, 'Heiligenberg', 3006, 'BW', 82, 'DE', 47.82094000, 9.31280000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q715'),
(25996, 'Heiligengrabe', 3013, 'BB', 82, 'DE', 53.14461000, 12.36254000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q520280'),
(25997, 'Heiligenhafen', 3005, 'SH', 82, 'DE', 54.36964000, 10.98022000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q516540'),
(25998, 'Heiligenhaus', 3017, 'NW', 82, 'DE', 51.32662000, 6.97106000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q161673'),
(25999, 'Heiligenroth', 3019, 'RP', 82, 'DE', 50.45000000, 7.86667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q564596'),
(26000, 'Heiligensee', 3010, 'BE', 82, 'DE', 52.61449000, 13.24501000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q513099'),
(26001, 'Heiligenstadt', 3009, 'BY', 82, 'DE', 49.86303000, 11.17185000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q628981'),
(26002, 'Heiligenstedten', 3005, 'SH', 82, 'DE', 53.93264000, 9.47462000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q628981'),
(26003, 'Heiligkreuzsteinach', 3006, 'BW', 82, 'DE', 49.48333000, 8.79500000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q81108'),
(26004, 'Heilsbronn', 3009, 'BY', 82, 'DE', 49.33572000, 10.78741000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q427153'),
(26005, 'Heimbach', 3017, 'NW', 82, 'DE', 50.63693000, 6.46896000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32055810'),
(26006, 'Heimbach', 3019, 'RP', 82, 'DE', 50.45000000, 7.53333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32055810'),
(26007, 'Heimbuchenthal', 3009, 'BY', 82, 'DE', 49.88917000, 9.29556000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q504985'),
(26008, 'Heimenkirch', 3009, 'BY', 82, 'DE', 47.62959000, 9.90304000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q552434'),
(26009, 'Heimertingen', 3009, 'BY', 82, 'DE', 48.03333000, 10.15000000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q548558'),
(26010, 'Heimsheim', 3006, 'BW', 82, 'DE', 48.80656000, 8.86744000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q80675'),
(26011, 'Heinade', 3008, 'NI', 82, 'DE', 51.83333000, 9.63333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q61615'),
(26012, 'Heinböckel', 3008, 'NI', 82, 'DE', 53.57457000, 9.32644000, '2019-10-05 22:41:01', '2020-05-01 17:22:48', 1, 'Q21013404'),
(26013, 'Heinersdorf', 3010, 'BE', 82, 'DE', 52.57173000, 13.43757000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q693540'),
(26014, 'Heiningen', 3006, 'BW', 82, 'DE', 48.66177000, 9.64977000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q693540'),
(26015, 'Heinsberg', 3017, 'NW', 82, 'DE', 51.06358000, 6.09980000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q14833'),
(26016, 'Heinsen', 3008, 'NI', 82, 'DE', 52.06257000, 9.66316000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q14833'),
(26017, 'Heist', 3005, 'SH', 82, 'DE', 53.65000000, 9.65000000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q14833'),
(26018, 'Heistenbach', 3019, 'RP', 82, 'DE', 50.37933000, 7.98629000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q553295'),
(26019, 'Heitersheim', 3006, 'BW', 82, 'DE', 47.87468000, 7.65721000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q505862'),
(26020, 'Helbra', 3011, 'ST', 82, 'DE', 51.55000000, 11.50000000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q555270'),
(26021, 'Heldrungen', 3015, 'TH', 82, 'DE', 51.30208000, 11.21816000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q529787'),
(26022, 'Helferskirchen', 3019, 'RP', 82, 'DE', 50.51028000, 7.81184000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q564488'),
(26023, 'Helgoland', 3005, 'SH', 82, 'DE', 54.18143000, 7.88630000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q564488'),
(26024, 'Hellenhahn-Schellenberg', 3019, 'RP', 82, 'DE', 50.61278000, 8.02639000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q564581'),
(26025, 'Hellenthal', 3017, 'NW', 82, 'DE', 50.48333000, 6.43333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q240964'),
(26026, 'Hellersdorf', 3010, 'BE', 82, 'DE', 52.53319000, 13.60880000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q821089'),
(26027, 'Hellingen', 3015, 'TH', 82, 'DE', 50.25000000, 10.68333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q637597'),
(26028, 'Hellwege', 3008, 'NI', 82, 'DE', 53.07346000, 9.23566000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q637597'),
(26029, 'Helmbrechts', 3009, 'BY', 82, 'DE', 50.23557000, 11.71589000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q494110'),
(26030, 'Helmstadt', 3009, 'BY', 82, 'DE', 49.76180000, 9.70803000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q504431'),
(26031, 'Helmstadt-Bargen', 3006, 'BW', 82, 'DE', 49.31427000, 8.99712000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q504431'),
(26032, 'Helmstedt', 3008, 'NI', 82, 'DE', 52.22790000, 11.00985000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q486562'),
(26033, 'Helpsen', 3008, 'NI', 82, 'DE', 52.31131000, 9.11676000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q585277'),
(26034, 'Helsa', 3018, 'HE', 82, 'DE', 51.25981000, 9.68872000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q622846'),
(26035, 'Heltersberg', 3019, 'RP', 82, 'DE', 49.31667000, 7.71667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q553322'),
(26036, 'Hemau', 3009, 'BY', 82, 'DE', 49.05399000, 11.78195000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q491402'),
(26037, 'Hemdingen', 3005, 'SH', 82, 'DE', 53.76667000, 9.83333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q491402'),
(26038, 'Hemer', 3017, 'NW', 82, 'DE', 51.38707000, 7.77019000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q1104'),
(26039, 'Hemhofen', 3009, 'BY', 82, 'DE', 49.68333000, 10.93333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q1604803'),
(26040, 'Hemmingen', 3008, 'NI', 82, 'DE', 52.31425000, 9.72359000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q1604803'),
(26041, 'Hemmingen', 3006, 'BW', 82, 'DE', 48.86667000, 9.03333000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q1604803'),
(26042, 'Hemmingstedt', 3005, 'SH', 82, 'DE', 54.15000000, 9.06667000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q522133'),
(26043, 'Hemmoor', 3008, 'NI', 82, 'DE', 53.68702000, 9.15492000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q505729'),
(26044, 'Hemsbach', 3006, 'BW', 82, 'DE', 49.59075000, 8.64779000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q81012'),
(26045, 'Hemsbünde', 3008, 'NI', 82, 'DE', 53.08998000, 9.47261000, '2019-10-05 22:41:01', '2020-05-01 17:22:48', 1, 'Q81012'),
(26046, 'Hemslingen', 3008, 'NI', 82, 'DE', 53.08333000, 9.60000000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q506818'),
(26047, 'Hendungen', 3009, 'BY', 82, 'DE', 50.39462000, 10.35204000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q558968'),
(26048, 'Henfenfeld', 3009, 'BY', 82, 'DE', 49.49846000, 11.39059000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q524350'),
(26049, 'Hengersberg', 3009, 'BY', 82, 'DE', 48.77255000, 13.05485000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q32056664'),
(26050, 'Hennef', 3017, 'NW', 82, 'DE', 50.77555000, 7.28308000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q6961'),
(26051, 'Hennigsdorf', 3013, 'BB', 82, 'DE', 52.63598000, 13.20419000, '2019-10-05 22:41:01', '2019-10-05 22:41:01', 1, 'Q583462'),
(26052, 'Hennstedt', 3005, 'SH', 82, 'DE', 54.28333000, 9.16667000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q583462'),
(26053, 'Hennweiler', 3019, 'RP', 82, 'DE', 49.81667000, 7.43333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q555047'),
(26054, 'Henstedt-Ulzburg', 3005, 'SH', 82, 'DE', 53.80500000, 9.97452000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q508077'),
(26055, 'Heppenheim an der Bergstrasse', 3018, 'HE', 82, 'DE', 49.64145000, 8.63206000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q524407'),
(26056, 'Hepstedt', 3008, 'NI', 82, 'DE', 53.25874000, 9.08209000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q505540'),
(26057, 'Herbertingen', 3006, 'BW', 82, 'DE', 48.06667000, 9.43333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q81999'),
(26058, 'Herbolzheim', 3006, 'BW', 82, 'DE', 48.21884000, 7.77746000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q506864'),
(26059, 'Herborn', 3018, 'HE', 82, 'DE', 50.68135000, 8.30369000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q18998'),
(26060, 'Herbrechtingen', 3006, 'BW', 82, 'DE', 48.62173000, 10.17600000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q1609522'),
(26061, 'Herbsleben', 3015, 'TH', 82, 'DE', 51.11667000, 10.83333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q668972'),
(26062, 'Herbstein', 3018, 'HE', 82, 'DE', 50.56105000, 9.34592000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q565528'),
(26063, 'Herdecke', 3017, 'NW', 82, 'DE', 51.39999000, 7.43584000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q565528'),
(26064, 'Herdorf', 3019, 'RP', 82, 'DE', 50.77704000, 7.95366000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q520240'),
(26065, 'Herdwangen-Schönach', 3006, 'BW', 82, 'DE', 47.85000000, 9.20000000, '2019-10-05 22:41:02', '2020-05-01 17:22:47', 1, 'Q83146'),
(26066, 'Heretsried', 3009, 'BY', 82, 'DE', 48.45972000, 10.73601000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q83146'),
(26067, 'Herford', 3017, 'NW', 82, 'DE', 52.11457000, 8.67343000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q3971'),
(26068, 'Herforst', 3019, 'RP', 82, 'DE', 49.95000000, 6.70000000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q182947'),
(26069, 'Hergensweiler', 3009, 'BY', 82, 'DE', 47.61667000, 9.78333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q513198'),
(26070, 'Hergisdorf', 3011, 'ST', 82, 'DE', 51.53333000, 11.48333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q513198'),
(26071, 'Heringen', 3015, 'TH', 82, 'DE', 51.44705000, 10.87612000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q167528'),
(26072, 'Heringen', 3018, 'HE', 82, 'DE', 50.88798000, 10.00717000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q570334'),
(26073, 'Heringsdorf', 3005, 'SH', 82, 'DE', 54.30100000, 11.00658000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q570334'),
(26074, 'Herleshausen', 3018, 'HE', 82, 'DE', 51.00629000, 10.16731000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q628498'),
(26075, 'Hermannsburg', 3008, 'NI', 82, 'DE', 52.83254000, 10.08957000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q549731'),
(26076, 'Hermaringen', 3006, 'BW', 82, 'DE', 48.59544000, 10.26065000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q83025'),
(26077, 'Hermersberg', 3019, 'RP', 82, 'DE', 49.31667000, 7.63333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q22463'),
(26078, 'Hermeskeil', 3019, 'RP', 82, 'DE', 49.65528000, 6.94407000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q519344'),
(26079, 'Hermsdorf', 3015, 'TH', 82, 'DE', 50.89694000, 11.85549000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q519344'),
(26080, 'Hermsdorf', 3011, 'ST', 82, 'DE', 52.18740000, 11.47556000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q696881'),
(26081, 'Hermsdorf', 3021, 'SN', 82, 'DE', 50.92338000, 13.73480000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q696881'),
(26082, 'Hermsdorf', 3010, 'BE', 82, 'DE', 52.61421000, 13.30587000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q565018'),
(26083, 'Herne', 3017, 'NW', 82, 'DE', 51.53880000, 7.22572000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q565018'),
(26084, 'Heroldsbach', 3009, 'BY', 82, 'DE', 49.69342000, 10.99882000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q503019'),
(26085, 'Heroldsberg', 3009, 'BY', 82, 'DE', 49.53247000, 11.15551000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q73542'),
(26086, 'Herrenberg', 3006, 'BW', 82, 'DE', 48.59523000, 8.86648000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q16100'),
(26087, 'Herrieden', 3009, 'BY', 82, 'DE', 49.23779000, 10.50350000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q628190'),
(26088, 'Herrischried', 3006, 'BW', 82, 'DE', 47.66667000, 8.00000000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q834905'),
(26089, 'Herrngiersdorf', 3009, 'BY', 82, 'DE', 48.78863000, 12.07200000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q260271'),
(26090, 'Herrnhut', 3021, 'SN', 82, 'DE', 51.01624000, 14.74381000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q165140'),
(26091, 'Herrsching am Ammersee', 3009, 'BY', 82, 'DE', 47.99888000, 11.17679000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q170020'),
(26092, 'Hersbruck', 3009, 'BY', 82, 'DE', 49.51083000, 11.43151000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q502865'),
(26093, 'Herschdorf', 3015, 'TH', 82, 'DE', 50.71168000, 11.53736000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q502865'),
(26094, 'Herscheid', 3017, 'NW', 82, 'DE', 51.17901000, 7.74355000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q5839'),
(26095, 'Herschweiler-Pettersheim', 3019, 'RP', 82, 'DE', 49.48333000, 7.35000000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q563841'),
(26096, 'Herten', 3017, 'NW', 82, 'DE', 51.59638000, 7.14387000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q3985'),
(26097, 'Herxheim am Berg', 3019, 'RP', 82, 'DE', 49.50917000, 8.17917000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q3985'),
(26098, 'Herzberg', 3013, 'BB', 82, 'DE', 51.68692000, 13.22016000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q544699'),
(26099, 'Herzberg am Harz', 3008, 'NI', 82, 'DE', 51.65546000, 10.33938000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q503181'),
(26100, 'Herzhorn', 3005, 'SH', 82, 'DE', 53.78333000, 9.48333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q560923'),
(26101, 'Herzlake', 3008, 'NI', 82, 'DE', 52.68530000, 7.59946000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q629886'),
(26102, 'Herzogenaurach', 3009, 'BY', 82, 'DE', 49.56798000, 10.88565000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q1615401'),
(26103, 'Herzogenrath', 3017, 'NW', 82, 'DE', 50.86874000, 6.09317000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q6916'),
(26104, 'Hesel', 3008, 'NI', 82, 'DE', 53.30000000, 7.60000000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q508039'),
(26105, 'Hespe', 3008, 'NI', 82, 'DE', 52.32999000, 9.10818000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q508039'),
(26106, 'Hessigheim', 3006, 'BW', 82, 'DE', 48.99407000, 9.18629000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q61960'),
(26107, 'Hessisch Lichtenau', 3018, 'HE', 82, 'DE', 51.19954000, 9.71857000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q558563'),
(26108, 'Hessisch Oldendorf', 3008, 'NI', 82, 'DE', 52.17269000, 9.24913000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q533826'),
(26109, 'Hetlingen', 3005, 'SH', 82, 'DE', 53.60712000, 9.63718000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q546300'),
(26110, 'Hettenleidelheim', 3019, 'RP', 82, 'DE', 49.53556000, 8.07361000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q629699'),
(26111, 'Hettenshausen', 3009, 'BY', 82, 'DE', 48.50000000, 11.50000000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q32057794'),
(26112, 'Hettingen', 3006, 'BW', 82, 'DE', 48.21601000, 9.23169000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q82292'),
(26113, 'Hettstadt', 3009, 'BY', 82, 'DE', 49.79944000, 9.81500000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q82292'),
(26114, 'Hettstedt', 3011, 'ST', 82, 'DE', 51.65030000, 11.51146000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q50883'),
(26115, 'Hetzerath', 3019, 'RP', 82, 'DE', 49.88333000, 6.81667000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q50883'),
(26116, 'Hetzles', 3009, 'BY', 82, 'DE', 49.63333000, 11.13333000, '2019-10-05 22:41:02', '2019-10-05 22:41:02', 1, 'Q503237'),
(26117, 'Heubach', 3006, 'BW', 82, 'DE', 48.79267000, 9.93370000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q326437'),
(26118, 'Heuchelheim', 3018, 'HE', 82, 'DE', 50.58333000, 8.63333000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q326437'),
(26119, 'Heuchelheim bei Frankenthal', 3019, 'RP', 82, 'DE', 49.56306000, 8.29083000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q566956'),
(26120, 'Heuchlingen', 3006, 'BW', 82, 'DE', 48.85215000, 9.94391000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q705521'),
(26121, 'Heudeber', 3011, 'ST', 82, 'DE', 51.90245000, 10.84321000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q694947'),
(26122, 'Heuerßen', 3008, 'NI', 82, 'DE', 52.32946000, 9.27718000, '2019-10-05 22:41:03', '2020-05-01 17:22:48', 1, 'Q694947'),
(26123, 'Heusenstamm', 3018, 'HE', 82, 'DE', 50.05553000, 8.80076000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q569659'),
(26124, 'Heustreu', 3009, 'BY', 82, 'DE', 50.35485000, 10.26069000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q558914'),
(26125, 'Heusweiler', 3020, 'SL', 82, 'DE', 49.33632000, 6.93036000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q558914'),
(26126, 'Heyerode', 3015, 'TH', 82, 'DE', 51.16439000, 10.32009000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q696361'),
(26127, 'Heyersum', 3008, 'NI', 82, 'DE', 52.15678000, 9.81265000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q696361'),
(26128, 'Heßdorf', 3009, 'BY', 82, 'DE', 49.62728000, 10.91002000, '2019-10-05 22:41:03', '2020-05-01 17:22:47', 1, 'Q1617121'),
(26129, 'Heßheim', 3019, 'RP', 82, 'DE', 49.54583000, 8.30778000, '2019-10-05 22:41:03', '2020-05-01 17:22:49', 1, 'Q559641'),
(26130, 'Hiddenhausen', 3017, 'NW', 82, 'DE', 52.16667000, 8.61667000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q53908'),
(26131, 'Hiddensee', 3007, 'MV', 82, 'DE', 54.56689000, 13.10411000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q53908'),
(26132, 'Hilbersdorf', 3021, 'SN', 82, 'DE', 50.85400000, 12.94776000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q53908'),
(26133, 'Hilchenbach', 3017, 'NW', 82, 'DE', 50.99687000, 8.11062000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q10949'),
(26134, 'Hildburghausen', 3015, 'TH', 82, 'DE', 50.42553000, 10.73184000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q504348'),
(26135, 'Hilden', 3017, 'NW', 82, 'DE', 51.16818000, 6.93093000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q4094'),
(26136, 'Hilders', 3018, 'HE', 82, 'DE', 50.57190000, 10.00297000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q636117'),
(26137, 'Hildesheim', 3008, 'NI', 82, 'DE', 52.15077000, 9.95112000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q3185'),
(26138, 'Hildrizhausen', 3006, 'BW', 82, 'DE', 48.62423000, 8.96605000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q550689'),
(26139, 'Hilgermissen', 3008, 'NI', 82, 'DE', 52.85000000, 9.16667000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q564562'),
(26140, 'Hilgert', 3019, 'RP', 82, 'DE', 50.45634000, 7.68735000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q654759'),
(26141, 'Hilgertshausen-Tandern', 3009, 'BY', 82, 'DE', 48.42960000, 11.35428000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q524247'),
(26142, 'Hille', 3017, 'NW', 82, 'DE', 52.33333000, 8.75000000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q524247'),
(26143, 'Hillerse', 3008, 'NI', 82, 'DE', 51.68558000, 9.94973000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q32058476'),
(26144, 'Hillesheim', 3019, 'RP', 82, 'DE', 50.29177000, 6.66963000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q570035'),
(26145, 'Hillscheid', 3019, 'RP', 82, 'DE', 50.40639000, 7.69861000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q570035'),
(26146, 'Hilpoltstein', 3009, 'BY', 82, 'DE', 49.19047000, 11.19060000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q521132'),
(26147, 'Hiltenfingen', 3009, 'BY', 82, 'DE', 48.16085000, 10.71750000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q521132'),
(26148, 'Hilter', 3008, 'NI', 82, 'DE', 52.13573000, 8.14715000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q544357'),
(26149, 'Hiltpoltstein', 3009, 'BY', 82, 'DE', 49.66075000, 11.32272000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q503055'),
(26150, 'Hilzingen', 3006, 'BW', 82, 'DE', 47.76667000, 8.78333000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q62039'),
(26151, 'Himbergen', 3008, 'NI', 82, 'DE', 53.09418000, 10.72909000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q680510'),
(26152, 'Himmelkron', 3009, 'BY', 82, 'DE', 50.06667000, 11.60000000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q504197'),
(26153, 'Himmelpforten', 3008, 'NI', 82, 'DE', 53.61413000, 9.30516000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q503853'),
(26154, 'Himmelstadt', 3009, 'BY', 82, 'DE', 49.92389000, 9.80167000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q508177'),
(26155, 'Hinte', 3008, 'NI', 82, 'DE', 53.41667000, 7.18333000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q552451'),
(26156, 'Hinterschmiding', 3009, 'BY', 82, 'DE', 48.82337000, 13.60369000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q258978'),
(26157, 'Hinterweidenthal', 3019, 'RP', 82, 'DE', 49.20000000, 7.75000000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q566039'),
(26158, 'Hinterzarten', 3006, 'BW', 82, 'DE', 47.90276000, 8.10701000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q515356'),
(26159, 'Hipstedt', 3008, 'NI', 82, 'DE', 53.48333000, 8.96667000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q515356'),
(26160, 'Hirrlingen', 3006, 'BW', 82, 'DE', 48.41245000, 8.88742000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q507682'),
(26161, 'Hirschaid', 3009, 'BY', 82, 'DE', 49.81789000, 10.98918000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q538066'),
(26162, 'Hirschau', 3009, 'BY', 82, 'DE', 49.54396000, 11.94617000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q502960'),
(26163, 'Hirschbach', 3009, 'BY', 82, 'DE', 49.55000000, 11.53333000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q502960'),
(26164, 'Hirschberg an der Bergstraße', 3006, 'BW', 82, 'DE', 49.50710000, 8.65693000, '2019-10-05 22:41:03', '2020-05-01 17:22:47', 1, 'Q502960'),
(26165, 'Hirschfeld', 3021, 'SN', 82, 'DE', 50.62453000, 12.45722000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q502960'),
(26166, 'Hirschfeld', 3013, 'BB', 82, 'DE', 51.38333000, 13.61667000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q502960'),
(26167, 'Hirschfelde', 3021, 'SN', 82, 'DE', 50.94437000, 14.88510000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q1620417'),
(26168, 'Hirschhorn', 3018, 'HE', 82, 'DE', 49.44566000, 8.89594000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q32058875'),
(26169, 'Hirzenhain', 3018, 'HE', 82, 'DE', 50.40000000, 9.13333000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q628421'),
(26170, 'Hitzacker', 3008, 'NI', 82, 'DE', 53.15254000, 11.04418000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q572535'),
(26171, 'Hitzhusen', 3005, 'SH', 82, 'DE', 53.92397000, 9.85262000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q572535'),
(26172, 'Hochdonn', 3005, 'SH', 82, 'DE', 54.02663000, 9.27624000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q572535'),
(26173, 'Hochdorf', 3006, 'BW', 82, 'DE', 48.02560000, 9.78778000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q534187'),
(26174, 'Hochdorf-Assenheim', 3019, 'RP', 82, 'DE', 49.41944000, 8.28167000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q567274'),
(26175, 'Hochfeld', 3017, 'NW', 82, 'DE', 51.41943000, 6.75462000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q567274'),
(26176, 'Hochheim am Main', 3018, 'HE', 82, 'DE', 50.01436000, 8.35218000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q632750'),
(26177, 'Hochkirch', 3021, 'SN', 82, 'DE', 51.14898000, 14.57063000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q93248'),
(26178, 'Hochspeyer', 3019, 'RP', 82, 'DE', 49.44207000, 7.89504000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q567730'),
(26179, 'Hochstadt', 3019, 'RP', 82, 'DE', 49.24750000, 8.20889000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q32059330'),
(26180, 'Hochstadt am Main', 3009, 'BY', 82, 'DE', 50.15009000, 11.17116000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q503039'),
(26181, 'Hochstetten-Dhaun', 3019, 'RP', 82, 'DE', 49.80000000, 7.50000000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q563032'),
(26182, 'Hockenheim', 3006, 'BW', 82, 'DE', 49.32334000, 8.55194000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q8336'),
(26183, 'Hodenhagen', 3008, 'NI', 82, 'DE', 52.76506000, 9.59495000, '2019-10-05 22:41:03', '2019-10-05 22:41:03', 1, 'Q639047'),
(26184, 'Hof', 3019, 'RP', 82, 'DE', 50.66667000, 8.01667000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q818672'),
(26185, 'Hof', 3009, 'BY', 82, 'DE', 50.31297000, 11.91261000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q818672'),
(26186, 'Hofbieber', 3018, 'HE', 82, 'DE', 50.58629000, 9.83534000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q621939'),
(26187, 'Hofgeismar', 3018, 'HE', 82, 'DE', 51.49607000, 9.38500000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q490449'),
(26188, 'Hofheim am Taunus', 3018, 'HE', 82, 'DE', 50.09019000, 8.44930000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q14896'),
(26189, 'Hofheim in Unterfranken', 3009, 'BY', 82, 'DE', 50.13675000, 10.52321000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q14896'),
(26190, 'Hofkirchen', 3009, 'BY', 82, 'DE', 48.67805000, 13.11917000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q530456'),
(26191, 'Hofstetten', 3006, 'BW', 82, 'DE', 48.25846000, 8.06595000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q530456'),
(26192, 'Hofstetten', 3009, 'BY', 82, 'DE', 48.00847000, 10.97114000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q530456'),
(26193, 'Hohberg', 3006, 'BW', 82, 'DE', 48.32270000, 7.89972000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q530456'),
(26194, 'Hohburg', 3021, 'SN', 82, 'DE', 51.41168000, 12.80508000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q631428'),
(26195, 'Hohen Neuendorf', 3013, 'BB', 82, 'DE', 52.67631000, 13.27775000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q545340'),
(26196, 'Hohenaspe', 3005, 'SH', 82, 'DE', 53.98898000, 9.52774000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q545568'),
(26197, 'Hohenau', 3009, 'BY', 82, 'DE', 48.84983000, 13.48825000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q32059751'),
(26198, 'Hohenberg an der Eger', 3009, 'BY', 82, 'DE', 50.09501000, 12.22008000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q170405'),
(26199, 'Hohenbocka', 3013, 'BB', 82, 'DE', 51.43099000, 14.00982000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q177052'),
(26200, 'Hohenbrunn', 3009, 'BY', 82, 'DE', 48.04785000, 11.70224000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q505339'),
(26201, 'Hohenburg', 3009, 'BY', 82, 'DE', 49.29507000, 11.79906000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q518772'),
(26202, 'Hohendodeleben', 3011, 'ST', 82, 'DE', 52.10412000, 11.50473000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q695555'),
(26203, 'Hohenfelde', 3005, 'SH', 82, 'DE', 54.36667000, 10.50000000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q695555'),
(26204, 'Hohenfels', 3009, 'BY', 82, 'DE', 49.20397000, 11.84841000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q524364'),
(26205, 'Hohenfurch', 3009, 'BY', 82, 'DE', 47.85000000, 10.90000000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q32059809'),
(26206, 'Hohenhameln', 3008, 'NI', 82, 'DE', 52.25755000, 10.06416000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q569132'),
(26207, 'Hohenkammer', 3009, 'BY', 82, 'DE', 48.42406000, 11.52522000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q569132'),
(26208, 'Hohenleipisch', 3013, 'BB', 82, 'DE', 51.50000000, 13.55000000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q551451'),
(26209, 'Hohenleuben', 3015, 'TH', 82, 'DE', 50.71128000, 12.05427000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q526430'),
(26210, 'Hohenlinden', 3009, 'BY', 82, 'DE', 48.15612000, 11.99458000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q524334'),
(26211, 'Hohenlockstedt', 3005, 'SH', 82, 'DE', 53.96667000, 9.61667000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q428389'),
(26212, 'Hohenmölsen', 3011, 'ST', 82, 'DE', 51.15769000, 12.10000000, '2019-10-05 22:41:04', '2020-05-01 17:22:49', 1, 'Q522390'),
(26213, 'Hohenpeißenberg', 3009, 'BY', 82, 'DE', 47.80000000, 11.00000000, '2019-10-05 22:41:04', '2020-05-01 17:22:47', 1, 'Q533232'),
(26214, 'Hohenpolding', 3009, 'BY', 82, 'DE', 48.38333000, 12.13333000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q512892'),
(26215, 'Hohenstein-Ernstthal', 3021, 'SN', 82, 'DE', 50.80064000, 12.71287000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q20073'),
(26216, 'Hohentengen', 3006, 'BW', 82, 'DE', 47.57005000, 8.43250000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q282294'),
(26217, 'Hohenthann', 3009, 'BY', 82, 'DE', 48.66115000, 12.09251000, '2019-10-05 22:41:04', '2019-10-05 22:41:04', 1, 'Q512719'),
(26218, 'Hohenthurm', 3011, 'ST', 82, 'DE', 51.51807000, 12.09749000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q673176'),
(26219, 'Hohenwarsleben', 3011, 'ST', 82, 'DE', 52.17898000, 11.49994000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q689982'),
(26220, 'Hohenwarth', 3009, 'BY', 82, 'DE', 49.20000000, 12.93333000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q689982'),
(26221, 'Hohenwarthe', 3011, 'ST', 82, 'DE', 52.22968000, 11.71528000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q702067'),
(26222, 'Hohenwestedt', 3005, 'SH', 82, 'DE', 54.08886000, 9.65359000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q206686'),
(26223, 'Hohn', 3005, 'SH', 82, 'DE', 54.30000000, 9.50000000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q206686'),
(26224, 'Hohndorf', 3021, 'SN', 82, 'DE', 50.74044000, 12.68058000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q206686'),
(26225, 'Hohnhorst', 3008, 'NI', 82, 'DE', 52.36942000, 9.37168000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q636701'),
(26226, 'Hohnstein', 3021, 'SN', 82, 'DE', 50.97991000, 14.11408000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q636701'),
(26227, 'Hoisdorf', 3005, 'SH', 82, 'DE', 53.65000000, 10.31667000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q514911'),
(26228, 'Holdorf', 3008, 'NI', 82, 'DE', 52.58333000, 8.11667000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q514911'),
(26229, 'Holle', 3008, 'NI', 82, 'DE', 52.08721000, 10.16012000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q566284'),
(26230, 'Holleben', 3011, 'ST', 82, 'DE', 51.44047000, 11.89915000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060045'),
(26231, 'Hollenbach', 3009, 'BY', 82, 'DE', 48.48333000, 11.06667000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060045'),
(26232, 'Hollenstedt', 3008, 'NI', 82, 'DE', 53.36667000, 9.71667000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q570120'),
(26233, 'Holler', 3019, 'RP', 82, 'DE', 50.41667000, 7.83333000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q570120'),
(26234, 'Hollfeld', 3009, 'BY', 82, 'DE', 49.93793000, 11.29153000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q503048'),
(26235, 'Hollingstedt', 3005, 'SH', 82, 'DE', 54.45934000, 9.33695000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q503048'),
(26236, 'Hollstadt', 3009, 'BY', 82, 'DE', 50.35000000, 10.30000000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q558852'),
(26237, 'Holm', 3005, 'SH', 82, 'DE', 53.61667000, 9.66667000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q691934'),
(26238, 'Holtland', 3008, 'NI', 82, 'DE', 53.28333000, 7.58333000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q691934'),
(26239, 'Holtsee', 3005, 'SH', 82, 'DE', 54.40000000, 9.85000000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q567450'),
(26240, 'Holzappel', 3019, 'RP', 82, 'DE', 50.35000000, 7.90000000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q565333'),
(26241, 'Holzdorf', 3011, 'ST', 82, 'DE', 51.77873000, 13.12756000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q186280'),
(26242, 'Holzgerlingen', 3006, 'BW', 82, 'DE', 48.63969000, 9.01149000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q527883'),
(26243, 'Holzgünz', 3009, 'BY', 82, 'DE', 48.02503000, 10.25901000, '2019-10-05 22:41:05', '2020-05-01 17:22:47', 1, 'Q548535'),
(26244, 'Holzhausen an der Haide', 3019, 'RP', 82, 'DE', 50.21855000, 7.90973000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q550842'),
(26245, 'Holzheim', 3009, 'BY', 82, 'DE', 48.51422000, 10.53057000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q550842'),
(26246, 'Holzkirchen', 3009, 'BY', 82, 'DE', 47.87663000, 11.70181000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q260479'),
(26247, 'Holzmaden', 3006, 'BW', 82, 'DE', 48.63333000, 9.51667000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q80654'),
(26248, 'Holzminden', 3008, 'NI', 82, 'DE', 51.82798000, 9.44550000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q55425'),
(26249, 'Holzweißig', 3011, 'ST', 82, 'DE', 51.59819000, 12.30842000, '2019-10-05 22:41:05', '2020-05-01 17:22:49', 1, 'Q836702'),
(26250, 'Holzwickede', 3017, 'NW', 82, 'DE', 51.50000000, 7.63333000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060194'),
(26251, 'Homberg', 3018, 'HE', 82, 'DE', 50.73108000, 8.99644000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q624176'),
(26252, 'Homburg', 3020, 'SL', 82, 'DE', 49.32637000, 7.33867000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q624176'),
(26253, 'Hoogstede', 3008, 'NI', 82, 'DE', 52.58333000, 6.95000000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q623853'),
(26254, 'Hopferau', 3009, 'BY', 82, 'DE', 47.61667000, 10.63333000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q205497'),
(26255, 'Hoppstädten-Weiersbach', 3019, 'RP', 82, 'DE', 49.61667000, 7.20000000, '2019-10-05 22:41:05', '2020-05-01 17:22:49', 1, 'Q473558'),
(26256, 'Horb am Neckar', 3006, 'BW', 82, 'DE', 48.44423000, 8.69130000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q82690'),
(26257, 'Horben', 3006, 'BW', 82, 'DE', 47.93333000, 7.86667000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q515988'),
(26258, 'Hordel', 3017, 'NW', 82, 'DE', 51.49784000, 7.17620000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q162089'),
(26259, 'Horgau', 3009, 'BY', 82, 'DE', 48.39507000, 10.68283000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q532260'),
(26260, 'Horgenzell', 3006, 'BW', 82, 'DE', 47.80534000, 9.49727000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q534060'),
(26261, 'Horhausen', 3019, 'RP', 82, 'DE', 50.58745000, 7.53028000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060343'),
(26262, 'Horka', 3021, 'SN', 82, 'DE', 51.26342000, 14.25523000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q160797'),
(26263, 'Hormersdorf', 3021, 'SN', 82, 'DE', 50.67406000, 12.88194000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q57786'),
(26264, 'Horn', 3017, 'NW', 82, 'DE', 51.87151000, 8.94510000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q57786'),
(26265, 'Hornbach', 3019, 'RP', 82, 'DE', 49.18778000, 7.36883000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q628147'),
(26266, 'Hornberg', 3006, 'BW', 82, 'DE', 48.21068000, 8.23275000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q507581'),
(26267, 'Hornburg', 3008, 'NI', 82, 'DE', 52.03095000, 10.60490000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q507581'),
(26268, 'Horneburg', 3008, 'NI', 82, 'DE', 53.50672000, 9.57546000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q507581'),
(26269, 'Hornhausen', 3011, 'ST', 82, 'DE', 52.04510000, 11.17104000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q585774'),
(26270, 'Hornstorf', 3007, 'MV', 82, 'DE', 53.90994000, 11.53214000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060408'),
(26271, 'Horrenberg', 3006, 'BW', 82, 'DE', 49.28040000, 8.78087000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060408'),
(26272, 'Horst', 3005, 'SH', 82, 'DE', 53.81195000, 9.62307000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060526'),
(26273, 'Horstedt', 3008, 'NI', 82, 'DE', 53.18333000, 9.23333000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q503294'),
(26274, 'Horstmar', 3017, 'NW', 82, 'DE', 52.08098000, 7.30539000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q32060553'),
(26275, 'Hosenfeld', 3018, 'HE', 82, 'DE', 50.50502000, 9.47966000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q637007'),
(26276, 'Hoya', 3008, 'NI', 82, 'DE', 52.80781000, 9.14028000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q168701'),
(26277, 'Hoyerswerda', 3021, 'SN', 82, 'DE', 51.43787000, 14.23549000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q14904'),
(26278, 'Hoym', 3011, 'ST', 82, 'DE', 51.78292000, 11.31244000, '2019-10-05 22:41:05', '2019-10-05 22:41:05', 1, 'Q703487'),
(26279, 'Hude', 3008, 'NI', 82, 'DE', 53.10766000, 8.46322000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q32060689'),
(26280, 'Huede', 3008, 'NI', 82, 'DE', 52.49588000, 8.35804000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q503280'),
(26281, 'Huglfing', 3009, 'BY', 82, 'DE', 47.76667000, 11.16667000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q503280'),
(26282, 'Huisheim', 3009, 'BY', 82, 'DE', 48.82510000, 10.70331000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q503339'),
(26283, 'Humboldtkolonie', 3017, 'NW', 82, 'DE', 50.93182000, 6.99469000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q636321'),
(26284, 'Hummelsbüttel', 3016, 'HH', 82, 'DE', 53.64773000, 10.04149000, '2019-10-05 22:41:06', '2020-05-01 17:22:48', 1, 'Q636321'),
(26285, 'Hunderdorf', 3009, 'BY', 82, 'DE', 48.89162000, 12.67382000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q636321'),
(26286, 'Hundeshagen', 3015, 'TH', 82, 'DE', 51.43333000, 10.28333000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q552690'),
(26287, 'Hunding', 3009, 'BY', 82, 'DE', 48.84227000, 13.17681000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q32060764'),
(26288, 'Hundsangen', 3019, 'RP', 82, 'DE', 50.45000000, 7.98333000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q23011'),
(26289, 'Hungen', 3018, 'HE', 82, 'DE', 50.47368000, 8.89326000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q602629'),
(26290, 'Hurlach', 3009, 'BY', 82, 'DE', 48.11838000, 10.81115000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q602629'),
(26291, 'Husum', 3008, 'NI', 82, 'DE', 52.56667000, 9.25000000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q630743'),
(26292, 'Husum', 3005, 'SH', 82, 'DE', 54.48580000, 9.05239000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q21159'),
(26293, 'Hutthurm', 3009, 'BY', 82, 'DE', 48.67276000, 13.47146000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q504225'),
(26294, 'Huy-Neinstedt', 3011, 'ST', 82, 'DE', 51.96585000, 10.91457000, '2019-10-05 22:41:06', '2019-10-05 22:41:06', 1, 'Q1387060'),
(26295, 'Hähnichen', 3021, 'SN', 82, 'DE', 51.36540000, 14.86098000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q544944'),
(26296, 'Hänigsen', 3008, 'NI', 82, 'DE', 52.48425000, 10.09129000, '2019-10-05 22:41:06', '2020-05-01 17:22:48', 1, 'Q878220'),
(26297, 'Häusern', 3006, 'BW', 82, 'DE', 47.75000000, 8.16667000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q878220'),
(26298, 'Höchberg', 3009, 'BY', 82, 'DE', 49.78446000, 9.88223000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q508445'),
(26299, 'Höchenschwand', 3006, 'BW', 82, 'DE', 47.73333000, 8.16667000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q515648'),
(26300, 'Höchheim', 3009, 'BY', 82, 'DE', 50.36667000, 10.45000000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q589530'),
(26301, 'Höchst im Odenwald', 3018, 'HE', 82, 'DE', 49.79972000, 8.99944000, '2019-10-05 22:41:06', '2020-05-01 17:22:48', 1, 'Q634819'),
(26302, 'Höchstadt an der Aisch', 3009, 'BY', 82, 'DE', 49.70617000, 10.81329000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q510543'),
(26303, 'Höchstädt an der Donau', 3009, 'BY', 82, 'DE', 48.61124000, 10.56816000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q505909'),
(26304, 'Höchstädt bei Thiersheim', 3009, 'BY', 82, 'DE', 50.10157000, 12.08711000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q505909'),
(26305, 'Höckendorf', 3021, 'SN', 82, 'DE', 51.22423000, 13.90935000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q6775'),
(26306, 'Höfen an der Enz', 3006, 'BW', 82, 'DE', 48.80000000, 8.58333000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q6775'),
(26307, 'Höheinöd', 3019, 'RP', 82, 'DE', 49.28966000, 7.60673000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q6775'),
(26308, 'Höheischweiler', 3019, 'RP', 82, 'DE', 49.23333000, 7.55000000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q6775'),
(26309, 'Höhenberg', 3017, 'NW', 82, 'DE', 50.93323000, 7.04138000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q6775'),
(26310, 'Höhenkirchen-Siegertsbrunn', 3009, 'BY', 82, 'DE', 48.01932000, 11.71906000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q504885'),
(26311, 'Höhn', 3019, 'RP', 82, 'DE', 50.61667000, 7.98333000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q818645'),
(26312, 'Höhnstedt', 3011, 'ST', 82, 'DE', 51.50268000, 11.73844000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q689571'),
(26313, 'Höhr-Grenzhausen', 3019, 'RP', 82, 'DE', 50.43474000, 7.66903000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q50899'),
(26314, 'Höingen', 3018, 'HE', 82, 'DE', 50.71910000, 8.91961000, '2019-10-05 22:41:06', '2020-05-01 17:22:48', 1, 'Q50899'),
(26315, 'Höpfingen', 3006, 'BW', 82, 'DE', 49.60250000, 9.42861000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q534293'),
(26316, 'Hörden', 3008, 'NI', 82, 'DE', 51.66835000, 10.28372000, '2019-10-05 22:41:06', '2020-05-01 17:22:48', 1, 'Q534293'),
(26317, 'Hördt', 3019, 'RP', 82, 'DE', 49.16583000, 8.32639000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q654264'),
(26318, 'Hörgertshausen', 3009, 'BY', 82, 'DE', 48.55000000, 11.86667000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q506541'),
(26319, 'Hörnsheim', 3018, 'HE', 82, 'DE', 50.51828000, 8.62976000, '2019-10-05 22:41:06', '2020-05-01 17:22:48', 1, 'Q506541'),
(26320, 'Hörselgau', 3015, 'TH', 82, 'DE', 50.91988000, 10.58444000, '2019-10-05 22:41:06', '2020-05-01 17:22:50', 1, 'Q659100'),
(26321, 'Hörstel', 3017, 'NW', 82, 'DE', 52.29763000, 7.58382000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q52159678'),
(26322, 'Hösbach', 3009, 'BY', 82, 'DE', 50.00646000, 9.20765000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q503312'),
(26323, 'Höslwang', 3009, 'BY', 82, 'DE', 47.95378000, 12.32915000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q554161'),
(26324, 'Hötensleben', 3011, 'ST', 82, 'DE', 52.11974000, 11.02238000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q529831'),
(26325, 'Höttingen', 3009, 'BY', 82, 'DE', 49.06262000, 11.00530000, '2019-10-05 22:41:06', '2020-05-01 17:22:47', 1, 'Q525864'),
(26326, 'Hövelhof', 3017, 'NW', 82, 'DE', 51.81667000, 8.65000000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q183449'),
(26327, 'Höxter', 3017, 'NW', 82, 'DE', 51.77501000, 9.38155000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q16107'),
(26328, 'Hückelhoven', 3017, 'NW', 82, 'DE', 51.05550000, 6.22658000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q14884'),
(26329, 'Hückeswagen', 3017, 'NW', 82, 'DE', 51.14979000, 7.34471000, '2019-10-05 22:41:06', '2020-05-01 17:22:49', 1, 'Q11038'),
(26330, 'Hüffelsheim', 3019, 'RP', 82, 'DE', 49.81667000, 7.80000000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q561282'),
(26331, 'Hüffenhardt', 3006, 'BW', 82, 'DE', 49.29194000, 9.08167000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q179762'),
(26332, 'Hüfingen', 3006, 'BW', 82, 'DE', 47.92543000, 8.48831000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q83132');

