INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(11670, 'Durandé', 1998, 'MG', 31, 'BR', -20.15179000, -41.78240000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1756936'),
(11671, '<PERSON><PERSON><PERSON>', 2002, 'BA', 31, 'BR', -14.42340000, -39.95935000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1756936'),
(11672, 'Echaporã', 2021, 'SP', 31, 'BR', -22.41080000, -50.18262000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1756936'),
(11673, 'Ecoporanga', 2018, 'ES', 31, 'BR', -18.37333000, -40.83056000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1794439'),
(11674, '<PERSON><PERSON><PERSON>', 2000, 'GO', 31, 'BR', -17.43990000, -49.73319000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1794439'),
(11675, 'Edéia', 2000, '<PERSON>', 31, 'BR', -17.33833000, -49.93139000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1794351'),
(11676, 'Eirunepé', 2004, 'AM', 31, 'BR', -6.66028000, -69.87361000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1646910'),
(11677, 'Eldorado', 2010, 'MS', 31, 'BR', -23.78694000, -54.28361000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2019293'),
(11678, 'Eldorado', 2021, 'SP', 31, 'BR', -24.50057000, -48.25601000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2019293'),
(11679, 'Eldorado do Carajás', 2009, 'PA', 31, 'BR', -6.10586000, -49.30329000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q2019293'),
(11680, 'Eldorado do Sul', 2001, 'RS', 31, 'BR', -30.08896000, -51.53400000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2019293'),
(11681, 'Elesbão Veloso', 2008, 'PI', 31, 'BR', -6.19359000, -42.16607000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q2078352'),
(11682, 'Elias Fausto', 2021, 'SP', 31, 'BR', -23.05880000, -47.38776000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1750262'),
(11683, 'Eliseu Martins', 2008, 'PI', 31, 'BR', -7.90155000, -43.77769000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1750262'),
(11684, 'Elisiário', 2021, 'SP', 31, 'BR', -21.15517000, -49.09106000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1750262'),
(11685, 'Elísio Medrado', 2002, 'BA', 31, 'BR', -12.95431000, -39.50579000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1750262'),
(11686, 'Elói Mendes', 1998, 'MG', 31, 'BR', -21.60067000, -45.59466000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q939717'),
(11687, 'Emas', 2005, 'PB', 31, 'BR', -7.12680000, -37.73881000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q939717'),
(11688, 'Embaúba', 2021, 'SP', 31, 'BR', -20.94301000, -48.85819000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q939717'),
(11689, 'Embu', 2021, 'SP', 31, 'BR', -23.64889000, -46.85222000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651860'),
(11690, 'Embu Guaçu', 2021, 'SP', 31, 'BR', -23.83222000, -46.81139000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q22044752'),
(11691, 'Embu das Artes', 2021, 'SP', 31, 'BR', -23.64664000, -46.85386000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22044752'),
(11692, 'Embu-Guaçu', 2021, 'SP', 31, 'BR', -23.85336000, -46.83957000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1749956'),
(11693, 'Emilianópolis', 2021, 'SP', 31, 'BR', -21.78179000, -51.47959000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1749956'),
(11694, 'Encantado', 2001, 'RS', 31, 'BR', -29.23611000, -51.86972000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q226009'),
(11695, 'Encanto', 2019, 'RN', 31, 'BR', -6.11638000, -38.31133000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2089248'),
(11696, 'Encruzilhada', 2002, 'BA', 31, 'BR', -15.56839000, -40.90789000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1795353'),
(11697, 'Encruzilhada do Sul', 2001, 'RS', 31, 'BR', -30.60368000, -52.68280000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q600808'),
(11698, 'Engenheiro Beltrão', 2022, 'PR', 31, 'BR', -23.79722000, -52.26917000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1803653'),
(11699, 'Engenheiro Caldas', 1998, 'MG', 31, 'BR', -19.11350000, -42.01786000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1803653'),
(11700, 'Engenheiro Coelho', 2021, 'SP', 31, 'BR', -22.48904000, -47.17256000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1803653'),
(11701, 'Engenheiro Navarro', 1998, 'MG', 31, 'BR', -17.30933000, -44.03465000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1803653'),
(11702, 'Engenheiro Paulo de Frontin', 1997, 'RJ', 31, 'BR', -22.54807000, -43.67270000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1803653'),
(11703, 'Engenho Velho', 2001, 'RS', 31, 'BR', -27.67698000, -52.91040000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1803653'),
(11704, 'Entre Folhas', 1998, 'MG', 31, 'BR', -19.65964000, -42.24102000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1791121'),
(11705, 'Entre Rios', 2002, 'BA', 31, 'BR', -11.94194000, -38.08444000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1806968'),
(11706, 'Entre Rios', 2014, 'SC', 31, 'BR', -26.73140000, -52.59697000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1815627'),
(11707, 'Entre Rios de Minas', 1998, 'MG', 31, 'BR', -20.70275000, -44.10687000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1815627'),
(11708, 'Entre Rios do Oeste', 2022, 'PR', 31, 'BR', -24.70408000, -54.21562000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1815627'),
(11709, 'Entre Rios do Sul', 2001, 'RS', 31, 'BR', -27.50821000, -52.72855000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1815627'),
(11710, 'Entre-Ijuís', 2001, 'RS', 31, 'BR', -28.46066000, -54.30321000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q590326'),
(11711, 'Envira', 2004, 'AM', 31, 'BR', -7.30000000, -70.21667000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22054061'),
(11712, 'Enéas Marques', 2022, 'PR', 31, 'BR', -25.86454000, -53.15902000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q22054061'),
(11713, 'Epitaciolândia', 2012, 'AC', 31, 'BR', -10.93542000, -68.44411000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q22054061'),
(11714, 'Equador', 2019, 'RN', 31, 'BR', -6.88585000, -36.66174000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2002138'),
(11715, 'Erebango', 2001, 'RS', 31, 'BR', -27.82558000, -52.31909000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2002138'),
(11716, 'Erechim', 2001, 'RS', 31, 'BR', -27.63461000, -52.27540000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q642088'),
(11717, 'Ererê', 2016, 'CE', 31, 'BR', -5.99731000, -38.31180000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q642088'),
(11718, 'Ermo', 2014, 'SC', 31, 'BR', -28.97440000, -49.64940000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q642088'),
(11719, 'Ernestina', 2001, 'RS', 31, 'BR', -28.42323000, -52.57365000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q174286'),
(11720, 'Erval Grande', 2001, 'RS', 31, 'BR', -27.36138000, -52.57486000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q174286'),
(11721, 'Erval Seco', 2001, 'RS', 31, 'BR', -27.49433000, -53.52792000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q174286'),
(11722, 'Erval Velho', 2014, 'SC', 31, 'BR', -27.29027000, -51.38648000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q174286'),
(11723, 'Ervália', 1998, 'MG', 31, 'BR', -20.84684000, -42.61968000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q174286'),
(11724, 'Escada', 2006, 'PE', 31, 'BR', -8.35917000, -35.22361000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2102479'),
(11725, 'Esmeralda', 2001, 'RS', 31, 'BR', -27.98347000, -51.19413000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1803126'),
(11726, 'Esmeraldas', 1998, 'MG', 31, 'BR', -19.76250000, -44.31389000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22044873'),
(11727, 'Espera Feliz', 1998, 'MG', 31, 'BR', -20.59349000, -41.91981000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1790572'),
(11728, 'Esperantina', 2020, 'TO', 31, 'BR', -5.30432000, -48.54762000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q977277'),
(11729, 'Esperantina', 2008, 'PI', 31, 'BR', -3.84898000, -42.17102000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2009973'),
(11730, 'Esperantinópolis', 2015, 'MA', 31, 'BR', -4.86667000, -44.70833000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q2013171'),
(11731, 'Esperança', 2005, 'PB', 31, 'BR', -6.99864000, -35.90777000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1816186'),
(11732, 'Esperança Nova', 2022, 'PR', 31, 'BR', -23.71008000, -53.79362000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1816186'),
(11733, 'Esperança do Sul', 2001, 'RS', 31, 'BR', -27.32100000, -54.01108000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1784225'),
(11734, 'Espigão Alto do Iguaçu', 2022, 'PR', 31, 'BR', -25.35011000, -52.77923000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1784225'),
(11735, 'Espigão d\'Oeste', 2013, 'RO', 31, 'BR', -11.46622000, -60.69924000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q616498'),
(11736, 'Espinosa', 1998, 'MG', 31, 'BR', -14.85978000, -42.99177000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22062572'),
(11737, 'Esplanada', 2002, 'BA', 31, 'BR', -11.79611000, -37.94500000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1772504'),
(11738, 'Espumoso', 2001, 'RS', 31, 'BR', -28.72472000, -52.84972000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1749871'),
(11739, 'Espírito Santo', 2019, 'RN', 31, 'BR', -6.28385000, -35.32578000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q2064219'),
(11740, 'Espírito Santo do Dourado', 1998, 'MG', 31, 'BR', -22.00600000, -45.99000000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q2064219'),
(11741, 'Espírito Santo do Pinhal', 2021, 'SP', 31, 'BR', -22.18900000, -46.78421000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1649813'),
(11742, 'Espírito Santo do Turvo', 2021, 'SP', 31, 'BR', -22.66810000, -49.42175000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1649813'),
(11743, 'Estação', 2001, 'RS', 31, 'BR', -27.93562000, -52.29516000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1803196'),
(11744, 'Esteio', 2001, 'RS', 31, 'BR', -29.86139000, -51.17917000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q958661'),
(11745, 'Estiva', 1998, 'MG', 31, 'BR', -22.45283000, -46.02238000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1790777'),
(11746, 'Estiva Gerbi', 2021, 'SP', 31, 'BR', -22.22052000, -46.93878000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1790777'),
(11747, 'Estreito', 2015, 'MA', 31, 'BR', -5.78333000, -43.25000000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22025015'),
(11748, 'Estrela', 2001, 'RS', 31, 'BR', -28.06111000, -50.93833000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22044973'),
(11749, 'Estrela Dalva', 1998, 'MG', 31, 'BR', -21.69328000, -42.46834000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22044973'),
(11750, 'Estrela Velha', 2001, 'RS', 31, 'BR', -29.22360000, -53.17076000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22044973'),
(11751, 'Estrela d\'Oeste', 2021, 'SP', 31, 'BR', -20.27767000, -50.40807000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1648136'),
(11752, 'Estrela de Alagoas', 2007, 'AL', 31, 'BR', -9.39858000, -36.76219000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1648136'),
(11753, 'Estrela do Indaiá', 1998, 'MG', 31, 'BR', -19.58083000, -45.81189000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1648136'),
(11754, 'Estrela do Norte', 2021, 'SP', 31, 'BR', -22.47843000, -51.67954000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1795726'),
(11755, 'Estrela do Norte', 2000, 'GO', 31, 'BR', -13.78262000, -49.10026000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1795726'),
(11756, 'Estrela do Sul', 1998, 'MG', 31, 'BR', -18.72229000, -47.69732000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1795726'),
(11757, 'Estância', 2003, 'SE', 31, 'BR', -11.23831000, -37.42046000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1005942'),
(11758, 'Estância Velha', 2001, 'RS', 31, 'BR', -29.64833000, -51.17389000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q985499'),
(11759, 'Euclides da Cunha', 2002, 'BA', 31, 'BR', -10.50750000, -39.01583000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2013802'),
(11760, 'Euclides da Cunha Paulista', 2021, 'SP', 31, 'BR', -22.51710000, -52.59099000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2013802'),
(11761, 'Eugenópolis', 1998, 'MG', 31, 'BR', -20.********, -42.********, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q2013802'),
(11762, 'Eugênio de Castro', 2001, 'RS', 31, 'BR', -28.********, -54.********, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q2013802'),
(11763, 'Eunápolis', 2002, 'BA', 31, 'BR', -16.********, -39.********, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1796277'),
(11764, 'Eusébio', 2016, 'CE', 31, 'BR', -3.********, -38.********, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q2027112'),
(11765, 'Ewbank da Câmara', 1998, 'MG', 31, 'BR', -21.********, -43.********, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q2027112'),
(11766, 'Extrema', 2013, 'RO', 31, 'BR', -9.********, -66.********, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2027112'),
(11767, 'Extrema', 1998, 'MG', 31, 'BR', -22.********, -46.********, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2008030'),
(11768, 'Extremoz', 2019, 'RN', 31, 'BR', -5.********, -35.********, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q591577'),
(11769, 'Exu', 2006, 'PE', 31, 'BR', -7.********, -39.********, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2064727'),
(11770, 'Fagundes', 2005, 'PB', 31, 'BR', -7.********, -35.********, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2011192'),
(11771, 'Fagundes Varela', 2001, 'RS', 31, 'BR', -28.********, -51.********, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1786383'),
(11772, 'Faina', 2000, 'GO', 31, 'BR', -15.********, -50.********, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q988788'),
(11773, 'Fama', 1998, 'MG', 31, 'BR', -21.46845000, -45.82005000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22062565'),
(11774, 'Faria Lemos', 1998, 'MG', 31, 'BR', -20.78262000, -42.02787000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q22062565'),
(11775, 'Farias Brito', 2016, 'CE', 31, 'BR', -6.88662000, -39.53406000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2021026'),
(11776, 'Faro', 2009, 'PA', 31, 'BR', -1.11536000, -57.77412000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2246005'),
(11777, 'Farol', 2022, 'PR', 31, 'BR', -24.10348000, -52.66314000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2246005'),
(11778, 'Farroupilha', 2001, 'RS', 31, 'BR', -29.22500000, -51.34778000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q535531'),
(11779, 'Fartura', 2021, 'SP', 31, 'BR', -23.38833000, -49.51000000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q536230'),
(11780, 'Fartura do Piauí', 2008, 'PI', 31, 'BR', -9.52033000, -42.78927000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1936286'),
(11781, 'Faxinal', 2022, 'PR', 31, 'BR', -24.00028000, -51.31944000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2005159'),
(11782, 'Faxinal do Soturno', 2001, 'RS', 31, 'BR', -29.57197000, -53.47332000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2005159'),
(11783, 'Faxinal dos Guedes', 2014, 'SC', 31, 'BR', -26.83150000, -52.26214000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2005159'),
(11784, 'Faxinalzinho', 2001, 'RS', 31, 'BR', -27.37178000, -52.66899000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2005159'),
(11785, 'Fazenda Nova', 2000, 'GO', 31, 'BR', -16.12866000, -50.92604000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2005159'),
(11786, 'Fazenda Rio Grande', 2022, 'PR', 31, 'BR', -25.66466000, -49.30426000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2005159'),
(11787, 'Fazenda Vilanova', 2001, 'RS', 31, 'BR', -29.59817000, -51.84244000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2005159'),
(11788, 'Feijó', 2012, 'AC', 31, 'BR', -8.16540000, -70.35486000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1994833'),
(11789, 'Feira Grande', 2007, 'AL', 31, 'BR', -9.92470000, -36.66953000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1994833'),
(11790, 'Feira Nova', 2006, 'PE', 31, 'BR', -7.93474000, -35.39248000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1869240'),
(11791, 'Feira Nova', 2003, 'SE', 31, 'BR', -10.31724000, -37.33850000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1869240'),
(11792, 'Feira Nova do Maranhão', 2015, 'MA', 31, 'BR', -6.99561000, -46.64999000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1869240'),
(11793, 'Feira da Mata', 2002, 'BA', 31, 'BR', -14.13203000, -44.25281000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1869240'),
(11794, 'Feira de Santana', 2002, 'BA', 31, 'BR', -12.26667000, -38.96667000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11795, 'Felipe Guerra', 2019, 'RN', 31, 'BR', -5.53917000, -37.64271000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11796, 'Felisburgo', 1998, 'MG', 31, 'BR', -16.66153000, -40.71814000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11797, 'Felixlândia', 1998, 'MG', 31, 'BR', -18.69406000, -44.91938000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q194343'),
(11798, 'Feliz', 2001, 'RS', 31, 'BR', -29.45459000, -51.27775000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11799, 'Feliz Deserto', 2007, 'AL', 31, 'BR', -10.29080000, -36.35367000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11800, 'Feliz Natal', 2011, 'MT', 31, 'BR', -11.93599000, -54.23925000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11801, 'Felício dos Santos', 1998, 'MG', 31, 'BR', -18.15506000, -43.24046000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q194343'),
(11802, 'Fernandes Pinheiro', 2022, 'PR', 31, 'BR', -25.44403000, -50.51811000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11803, 'Fernandes Tourinho', 1998, 'MG', 31, 'BR', -19.10298000, -42.09459000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q194343'),
(11804, 'Fernando Falcão', 2015, 'MA', 31, 'BR', -6.35293000, -45.32810000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q194343'),
(11805, 'Fernando Pedroza', 2019, 'RN', 31, 'BR', -5.72020000, -36.39743000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q588380'),
(11806, 'Fernando Prestes', 2021, 'SP', 31, 'BR', -21.30945000, -48.69583000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q588380'),
(11807, 'Fernando de Noronha', 2006, 'PE', 31, 'BR', -3.85071000, -32.41997000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q175057'),
(11808, 'Fernando de Noronha (Distrito Estadual)', 2006, 'PE', 31, 'BR', -3.84028000, -32.41083000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q32003866'),
(11809, 'Fernandópolis', 2021, 'SP', 31, 'BR', -20.28389000, -50.24639000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1648179'),
(11810, 'Fernão', 2021, 'SP', 31, 'BR', -22.37381000, -49.55434000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1648179'),
(11811, 'Ferraz de Vasconcelos', 2021, 'SP', 31, 'BR', -23.56153000, -46.37490000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1646968'),
(11812, 'Ferreira Gomes', 1999, 'AP', 31, 'BR', 0.91012000, -51.35442000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1646968'),
(11813, 'Ferreiros', 2006, 'PE', 31, 'BR', -7.48644000, -35.19997000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1646968'),
(11814, 'Ferros', 1998, 'MG', 31, 'BR', -19.23796000, -42.97023000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1791559'),
(11815, 'Fervedouro', 1998, 'MG', 31, 'BR', -20.68788000, -42.34214000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1791559'),
(11816, 'Figueira', 2022, 'PR', 31, 'BR', -23.86125000, -50.42076000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2029914'),
(11817, 'Figueirão', 2010, 'MS', 31, 'BR', -18.67500000, -53.64250000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q2029914'),
(11818, 'Figueirópolis', 2020, 'TO', 31, 'BR', -12.26857000, -49.28976000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q2029914'),
(11819, 'Figueirópolis d\'Oeste', 2011, 'MT', 31, 'BR', -15.48700000, -58.69723000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q2013037'),
(11820, 'Filadélfia', 2020, 'TO', 31, 'BR', -7.48804000, -47.85623000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q2013037'),
(11821, 'Filadélfia', 2002, 'BA', 31, 'BR', -10.76635000, -40.20555000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q2019006'),
(11822, 'Firmino Alves', 2002, 'BA', 31, 'BR', -14.91360000, -39.91192000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2019006'),
(11823, 'Firminópolis', 2000, 'GO', 31, 'BR', -16.58194000, -50.30500000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q991093'),
(11824, 'Flexeiras', 2007, 'AL', 31, 'BR', -9.20061000, -35.78000000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1816177'),
(11825, 'Flor da Serra do Sul', 2022, 'PR', 31, 'BR', -26.23081000, -53.30368000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1816177'),
(11826, 'Flor do Sertão', 2014, 'SC', 31, 'BR', -26.75638000, -53.33675000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1816177'),
(11827, 'Flora Rica', 2021, 'SP', 31, 'BR', -21.70103000, -51.37786000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1816177'),
(11828, 'Floraí', 2022, 'PR', 31, 'BR', -23.32830000, -52.32422000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1816177'),
(11829, 'Floreal', 2021, 'SP', 31, 'BR', -20.65394000, -50.15844000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1816177'),
(11830, 'Flores', 2006, 'PE', 31, 'BR', -7.86806000, -37.97472000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2105990'),
(11831, 'Flores da Cunha', 2001, 'RS', 31, 'BR', -29.02889000, -51.18167000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1757597'),
(11832, 'Flores de Goiás', 2000, 'GO', 31, 'BR', -14.59003000, -46.87853000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1757597'),
(11833, 'Flores do Piauí', 2008, 'PI', 31, 'BR', -7.64121000, -42.84551000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1757597'),
(11834, 'Floresta', 2006, 'PE', 31, 'BR', -8.60111000, -38.56861000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2464842'),
(11835, 'Floresta', 2022, 'PR', 31, 'BR', -23.63014000, -52.07630000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2464842'),
(11836, 'Floresta Azul', 2002, 'BA', 31, 'BR', -14.84450000, -39.75503000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2464842'),
(11837, 'Floresta do Araguaia', 2009, 'PA', 31, 'BR', -7.57944000, -49.52862000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2464842'),
(11838, 'Floresta do Piauí', 2008, 'PI', 31, 'BR', -7.47628000, -41.84428000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q2464842'),
(11839, 'Florestal', 1998, 'MG', 31, 'BR', -19.86521000, -44.44251000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2464842'),
(11840, 'Florestópolis', 2022, 'PR', 31, 'BR', -22.86333000, -51.38722000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1863917'),
(11841, 'Floriano', 2008, 'PI', 31, 'BR', -6.76694000, -43.02250000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2008644'),
(11842, 'Floriano Peixoto', 2001, 'RS', 31, 'BR', -27.85241000, -52.03361000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1786719'),
(11843, 'Florianópolis', 2014, 'SC', 31, 'BR', -27.59667000, -48.54917000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q132997'),
(11844, 'Florânia', 2019, 'RN', 31, 'BR', -6.15287000, -36.80058000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q132997'),
(11845, 'Florínea', 2021, 'SP', 31, 'BR', -22.87222000, -50.68979000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1761119'),
(11846, 'Flórida', 2022, 'PR', 31, 'BR', -23.11265000, -51.97861000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1761119'),
(11847, 'Flórida Paulista', 2021, 'SP', 31, 'BR', -21.54193000, -51.18066000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q1761119'),
(11848, 'Fonte Boa', 2004, 'AM', 31, 'BR', -2.51389000, -66.09167000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1793768'),
(11849, 'Fontoura Xavier', 2001, 'RS', 31, 'BR', -29.01054000, -52.36771000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1793768'),
(11850, 'Formiga', 1998, 'MG', 31, 'BR', -20.46444000, -45.42639000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1791720'),
(11851, 'Formigueiro', 2001, 'RS', 31, 'BR', -29.96944000, -53.46743000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1791720'),
(11852, 'Formosa', 2000, 'GO', 31, 'BR', -15.53722000, -47.33444000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1003392'),
(11853, 'Formosa da Serra Negra', 2015, 'MA', 31, 'BR', -6.69535000, -46.19540000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1003392'),
(11854, 'Formosa do Oeste', 2022, 'PR', 31, 'BR', -24.30836000, -53.32776000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1003392'),
(11855, 'Formosa do Rio Preto', 2002, 'BA', 31, 'BR', -11.04833000, -45.19306000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1762128'),
(11856, 'Formosa do Sul', 2014, 'SC', 31, 'BR', -26.64697000, -52.80427000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1762128'),
(11857, 'Formoso', 1998, 'MG', 31, 'BR', -15.14747000, -46.09371000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1762128'),
(11858, 'Formoso', 2000, 'GO', 31, 'BR', -13.70339000, -48.87538000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1762128'),
(11859, 'Formoso do Araguaia', 2020, 'TO', 31, 'BR', -11.95898000, -50.09809000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1762128'),
(11860, 'Forquetinha', 2001, 'RS', 31, 'BR', -29.39695000, -52.11762000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1762128'),
(11861, 'Forquilha', 2016, 'CE', 31, 'BR', -3.83354000, -40.23431000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q587627'),
(11862, 'Forquilhinha', 2014, 'SC', 31, 'BR', -28.74750000, -49.47222000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1784729'),
(11863, 'Fortaleza', 2016, 'CE', 31, 'BR', -3.71722000, -38.54306000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q43463'),
(11864, 'Fortaleza de Minas', 1998, 'MG', 31, 'BR', -20.88169000, -46.77437000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q43463'),
(11865, 'Fortaleza do Tabocão', 2020, 'TO', 31, 'BR', -9.08805000, -48.55638000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q43463'),
(11866, 'Fortaleza dos Nogueiras', 2015, 'MA', 31, 'BR', -6.86722000, -46.02100000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q43463'),
(11867, 'Fortaleza dos Valos', 2001, 'RS', 31, 'BR', -28.91268000, -53.33891000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q43463'),
(11868, 'Fortim', 2016, 'CE', 31, 'BR', -4.44714000, -37.85289000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q43463'),
(11869, 'Fortuna', 2015, 'MA', 31, 'BR', -5.73333000, -44.15833000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2034097'),
(11870, 'Fortuna de Minas', 1998, 'MG', 31, 'BR', -19.54845000, -44.50230000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2034097'),
(11871, 'Foz do Iguaçu', 2022, 'PR', 31, 'BR', -25.54778000, -54.58806000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q202316'),
(11872, 'Foz do Jordão', 2022, 'PR', 31, 'BR', -25.69523000, -52.06075000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q1804288'),
(11873, 'Fraiburgo', 2014, 'SC', 31, 'BR', -27.04790000, -50.80694000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1804288'),
(11874, 'Franca', 2021, 'SP', 31, 'BR', -20.53861000, -47.40083000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q504587'),
(11875, 'Francinópolis', 2008, 'PI', 31, 'BR', -6.41032000, -42.24638000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q504587'),
(11876, 'Francisco Alves', 2022, 'PR', 31, 'BR', -24.06510000, -53.88444000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q504587'),
(11877, 'Francisco Ayres', 2008, 'PI', 31, 'BR', -6.69130000, -42.69202000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q504587'),
(11878, 'Francisco Badaró', 1998, 'MG', 31, 'BR', -16.95297000, -42.28055000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q504587'),
(11879, 'Francisco Beltrão', 2022, 'PR', 31, 'BR', -26.08111000, -53.05500000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q985577'),
(11880, 'Francisco Dantas', 2019, 'RN', 31, 'BR', -6.01644000, -38.12624000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q985577'),
(11881, 'Francisco Dumont', 1998, 'MG', 31, 'BR', -17.39766000, -44.21775000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q985577'),
(11882, 'Francisco Macedo', 2008, 'PI', 31, 'BR', -7.35845000, -40.77056000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2064534'),
(11883, 'Francisco Morato', 2021, 'SP', 31, 'BR', -23.26924000, -46.70889000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1646103'),
(11884, 'Francisco Santos', 2008, 'PI', 31, 'BR', -7.10293000, -41.14500000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2078798'),
(11885, 'Francisco Sá', 1998, 'MG', 31, 'BR', -16.47583000, -43.48833000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q936907'),
(11886, 'Franciscópolis', 1998, 'MG', 31, 'BR', -18.00982000, -41.98437000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q936907'),
(11887, 'Franco da Rocha', 2021, 'SP', 31, 'BR', -23.30277000, -46.73089000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q657199'),
(11888, 'Frecheirinha', 2016, 'CE', 31, 'BR', -3.72816000, -40.82027000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2027699'),
(11889, 'Frederico Westphalen', 2001, 'RS', 31, 'BR', -27.35917000, -53.39444000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q375650'),
(11890, 'Freguesia do Ribeirao da Ilha', 2014, 'SC', 31, 'BR', -27.71773000, -48.56266000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q375650'),
(11891, 'Frei Gaspar', 1998, 'MG', 31, 'BR', -18.14006000, -41.49292000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q375650'),
(11892, 'Frei Inocêncio', 1998, 'MG', 31, 'BR', -18.51403000, -41.87101000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q375650'),
(11893, 'Frei Lagonegro', 1998, 'MG', 31, 'BR', -18.14293000, -42.76064000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1790769'),
(11894, 'Frei Martinho', 2005, 'PB', 31, 'BR', -6.44605000, -36.48061000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1790769'),
(11895, 'Frei Miguelinho', 2006, 'PE', 31, 'BR', -7.94208000, -35.89073000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1790769'),
(11896, 'Frei Paulo', 2003, 'SE', 31, 'BR', -10.52224000, -37.********, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q588822'),
(11897, 'Frei Rogério', 2014, 'SC', 31, 'BR', -27.21783000, -50.77022000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q588822'),
(11898, 'Fronteira', 1998, 'MG', 31, 'BR', -20.22276000, -49.17640000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q588822'),
(11899, 'Fronteira dos Vales', 1998, 'MG', 31, 'BR', -16.89172000, -40.83008000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q588822'),
(11900, 'Fronteiras', 2008, 'PI', 31, 'BR', -7.01250000, -40.56213000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q588822'),
(11901, 'Fruta de Leite', 1998, 'MG', 31, 'BR', -16.16212000, -42.52859000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q588822'),
(11902, 'Frutal', 1998, 'MG', 31, 'BR', -20.02472000, -48.94056000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1790819'),
(11903, 'Frutuoso Gomes', 2019, 'RN', 31, 'BR', -6.16261000, -37.84430000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1790819'),
(11904, 'Fundão', 2018, 'ES', 31, 'BR', -19.98985000, -40.31579000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q593133'),
(11905, 'Funilândia', 1998, 'MG', 31, 'BR', -19.35187000, -44.08205000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q593133'),
(11906, 'Fátima', 2020, 'TO', 31, 'BR', -10.81953000, -48.86757000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q593133'),
(11907, 'Fátima', 2002, 'BA', 31, 'BR', -10.54600000, -38.22367000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q593133'),
(11908, 'Fátima do Sul', 2010, 'MS', 31, 'BR', -22.34424000, -54.37064000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q593133'),
(11909, 'Fênix', 2022, 'PR', 31, 'BR', -23.90551000, -52.03998000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q593133'),
(11910, 'Gabriel Monteiro', 2021, 'SP', 31, 'BR', -21.49989000, -50.56997000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q593133'),
(11911, 'Gado Bravo', 2005, 'PB', 31, 'BR', -7.59984000, -35.81967000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q593133'),
(11912, 'Galiléia', 1998, 'MG', 31, 'BR', -18.86897000, -41.52503000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q593133'),
(11913, 'Galinhos', 2019, 'RN', 31, 'BR', -5.12167000, -36.26313000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q593133'),
(11914, 'Galvão', 2014, 'SC', 31, 'BR', -26.45882000, -52.68535000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q593133'),
(11915, 'Gameleira', 2006, 'PE', 31, 'BR', -8.58444000, -35.38667000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2064836'),
(11916, 'Gameleira de Goiás', 2000, 'GO', 31, 'BR', -16.43131000, -48.67456000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q301331'),
(11917, 'Gameleiras', 1998, 'MG', 31, 'BR', -14.97096000, -43.30610000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q301331'),
(11918, 'Gandu', 2002, 'BA', 31, 'BR', -13.74389000, -39.48667000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q727944'),
(11919, 'Garanhuns', 2006, 'PE', 31, 'BR', -8.88202000, -36.50216000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q771575'),
(11920, 'Gararu', 2003, 'SE', 31, 'BR', -9.96750000, -37.08333000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2013193'),
(11921, 'Garibaldi', 2001, 'RS', 31, 'BR', -29.25611000, -51.53361000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q385465'),
(11922, 'Garopaba', 2014, 'SC', 31, 'BR', -28.02744000, -48.61450000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q385465'),
(11923, 'Garrafão do Norte', 2009, 'PA', 31, 'BR', -2.21516000, -47.11722000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q385465'),
(11924, 'Garruchos', 2001, 'RS', 31, 'BR', -28.25602000, -55.51556000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1754446'),
(11925, 'Garuva', 2014, 'SC', 31, 'BR', -26.06047000, -48.87082000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1754446'),
(11926, 'Garça', 2021, 'SP', 31, 'BR', -22.21056000, -49.65611000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q940614'),
(11927, 'Gaspar', 2014, 'SC', 31, 'BR', -26.93139000, -48.95889000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q856583'),
(11928, 'Gastão Vidigal', 2021, 'SP', 31, 'BR', -20.80633000, -50.19238000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q856583'),
(11929, 'Gaurama', 2001, 'RS', 31, 'BR', -27.61693000, -52.10405000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q856583'),
(11930, 'Gavião', 2002, 'BA', 31, 'BR', -11.48891000, -39.77412000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q856583'),
(11931, 'Gavião Peixoto', 2021, 'SP', 31, 'BR', -21.80879000, -48.46904000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q856583'),
(11932, 'Gaúcha do Norte', 2011, 'MT', 31, 'BR', -12.99365000, -53.52264000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q856583'),
(11933, 'Geminiano', 2008, 'PI', 31, 'BR', -7.16595000, -41.38802000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1852620'),
(11934, 'General Carneiro', 2022, 'PR', 31, 'BR', -26.43749000, -51.40075000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1852620'),
(11935, 'General Carneiro', 2011, 'MT', 31, 'BR', -15.51539000, -53.29790000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1852620'),
(11936, 'General Câmara', 2001, 'RS', 31, 'BR', -29.83922000, -51.94718000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1754588'),
(11937, 'General Maynard', 2003, 'SE', 31, 'BR', -10.69412000, -36.98304000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1754588'),
(11938, 'General Salgado', 2021, 'SP', 31, 'BR', -20.64833000, -50.36056000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1649675'),
(11939, 'General Sampaio', 2016, 'CE', 31, 'BR', -4.05016000, -39.44451000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1649675'),
(11940, 'Gentil', 2001, 'RS', 31, 'BR', -28.38797000, -52.04926000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1649675'),
(11941, 'Gentio do Ouro', 2002, 'BA', 31, 'BR', -11.36279000, -42.54827000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1649675'),
(11942, 'Getulina', 2021, 'SP', 31, 'BR', -21.76343000, -50.06225000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1649675'),
(11943, 'Getúlio Vargas', 2001, 'RS', 31, 'BR', -27.87411000, -52.16526000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1750452'),
(11944, 'Gilbués', 2008, 'PI', 31, 'BR', -9.72737000, -45.52017000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q677076'),
(11945, 'Girau do Ponciano', 2007, 'AL', 31, 'BR', -9.88417000, -36.82889000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q768122'),
(11946, 'Giruá', 2001, 'RS', 31, 'BR', -28.01046000, -54.30456000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1785990'),
(11947, 'Glaucilândia', 1998, 'MG', 31, 'BR', -16.89532000, -43.66081000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q1785990'),
(11948, 'Glicério', 2021, 'SP', 31, 'BR', -21.31866000, -50.17782000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1785990'),
(11949, 'Glorinha', 2001, 'RS', 31, 'BR', -29.85685000, -50.75086000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1785990'),
(11950, 'Glória', 2002, 'BA', 31, 'BR', -9.11097000, -38.40655000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q1785990'),
(11951, 'Glória d\'Oeste', 2011, 'MT', 31, 'BR', -15.91309000, -58.30449000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q2012397'),
(11952, 'Glória de Dourados', 2010, 'MS', 31, 'BR', -22.48388000, -54.09006000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q2012397'),
(11953, 'Glória do Goitá', 2006, 'PE', 31, 'BR', -8.03865000, -35.34006000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q1804101'),
(11954, 'Godofredo Viana', 2015, 'MA', 31, 'BR', -1.27605000, -45.76550000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1804101'),
(11955, 'Godoy Moreira', 2022, 'PR', 31, 'BR', -24.14813000, -51.91555000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1804101'),
(11956, 'Goiabeira', 1998, 'MG', 31, 'BR', -19.02964000, -41.22725000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1805781'),
(11957, 'Goiana', 2006, 'PE', 31, 'BR', -7.56056000, -35.00250000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1767210'),
(11958, 'Goiandira', 2000, 'GO', 31, 'BR', -18.10979000, -48.14934000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1767210'),
(11959, 'Goianinha', 2019, 'RN', 31, 'BR', -6.26977000, -35.18265000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1802289'),
(11960, 'Goianira', 2000, 'GO', 31, 'BR', -16.49611000, -49.42639000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q281011'),
(11961, 'Goianorte', 2020, 'TO', 31, 'BR', -8.83664000, -48.99356000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q281011'),
(11962, 'Goianá', 1998, 'MG', 31, 'BR', -21.56063000, -43.18511000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q2007679'),
(11963, 'Goianápolis', 2000, 'GO', 31, 'BR', -16.53041000, -49.08769000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q985611'),
(11964, 'Goianésia', 2000, 'GO', 31, 'BR', -15.31750000, -49.11750000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q988771'),
(11965, 'Goianésia do Pará', 2009, 'PA', 31, 'BR', -3.94920000, -48.87355000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q988771'),
(11966, 'Goiatins', 2020, 'TO', 31, 'BR', -8.04432000, -47.47183000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q988771'),
(11967, 'Goiatuba', 2000, 'GO', 31, 'BR', -18.01250000, -49.35472000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1797061'),
(11968, 'Goioerê', 2022, 'PR', 31, 'BR', -24.18868000, -53.09916000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q1797061'),
(11969, 'Goioxim', 2022, 'PR', 31, 'BR', -25.09533000, -52.00812000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1797061'),
(11970, 'Goiás', 2000, 'GO', 31, 'BR', -15.93444000, -50.14028000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q427697'),
(11971, 'Goiânia', 2000, 'GO', 31, 'BR', -16.67861000, -49.25389000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q83189'),
(11972, 'Gongogi', 2002, 'BA', 31, 'BR', -14.29995000, -39.60138000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q83189'),
(11973, 'Gonzaga', 1998, 'MG', 31, 'BR', -18.87874000, -42.49507000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q745774'),
(11974, 'Gonçalves', 1998, 'MG', 31, 'BR', -22.67479000, -45.83780000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q745774'),
(11975, 'Gonçalves Dias', 2015, 'MA', 31, 'BR', -5.15671000, -44.28658000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q14629944'),
(11976, 'Gouveia', 1998, 'MG', 31, 'BR', -18.53580000, -43.85363000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q22066972'),
(11977, 'Gouvelândia', 2000, 'GO', 31, 'BR', -18.50777000, -50.17554000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q22066972'),
(11978, 'Governador Archer', 2015, 'MA', 31, 'BR', -4.98399000, -44.20513000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q22066972'),
(11979, 'Governador Celso Ramos', 2014, 'SC', 31, 'BR', -27.37201000, -48.57908000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q22066972'),
(11980, 'Governador Dix Sept Rosado', 2019, 'RN', 31, 'BR', -5.45889000, -37.52083000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q22020541'),
(11981, 'Campo Grande', 2019, 'RN', 31, 'BR', -5.89722070, -37.48004980, '2025-04-28 16:14:37', '2025-04-28 16:14:37', 1, 'Q1802671'),
(11982, 'Governador Edison Lobão', 2015, 'MA', 31, 'BR', -5.74311000, -47.32545000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q1787040'),
(11983, 'Governador Eugênio Barros', 2015, 'MA', 31, 'BR', -5.42581000, -43.88031000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q1787040'),
(11984, 'Governador Jorge Teixeira', 2013, 'RO', 31, 'BR', -10.78462000, -63.04433000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1787040'),
(11985, 'Governador Lindenberg', 2018, 'ES', 31, 'BR', -19.20034000, -40.49510000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1985082'),
(11986, 'Governador Luiz Rocha', 2015, 'MA', 31, 'BR', -5.52826000, -44.11154000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1985082'),
(11987, 'Governador Mangabeira', 2002, 'BA', 31, 'BR', -12.57719000, -39.03115000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1985082'),
(11988, 'Governador Newton Bello', 2015, 'MA', 31, 'BR', -3.36796000, -45.65707000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1985082'),
(11989, 'Governador Nunes Freire', 2015, 'MA', 31, 'BR', -2.00992000, -45.84278000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1985082'),
(11990, 'Governador Valadares', 1998, 'MG', 31, 'BR', -18.85111000, -41.94944000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q462218'),
(11991, 'Gracho Cardoso', 2003, 'SE', 31, 'BR', -10.23500000, -37.20389000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q647366'),
(11992, 'Grajaú', 2015, 'MA', 31, 'BR', -5.81944000, -46.13861000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q2043418'),
(11993, 'Gramado', 2001, 'RS', 31, 'BR', -29.36252000, -50.90318000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2043418'),
(11994, 'Gramado Xavier', 2001, 'RS', 31, 'BR', -29.28655000, -52.61202000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2043418'),
(11995, 'Gramado dos Loureiros', 2001, 'RS', 31, 'BR', -27.45441000, -52.91705000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2043418'),
(11996, 'Grandes Rios', 2022, 'PR', 31, 'BR', -24.18645000, -51.43460000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2043418'),
(11997, 'Granito', 2006, 'PE', 31, 'BR', -7.74380000, -39.64021000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2043418'),
(11998, 'Granja', 2016, 'CE', 31, 'BR', -3.12028000, -40.82611000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2028672'),
(11999, 'Granjeiro', 2016, 'CE', 31, 'BR', -6.91745000, -39.27944000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2028672'),
(12000, 'Gravatal', 2014, 'SC', 31, 'BR', -28.31534000, -49.04351000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2028672'),
(12001, 'Gravataí', 2001, 'RS', 31, 'BR', -29.94218000, -50.99278000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q694822'),
(12002, 'Gravatá', 2006, 'PE', 31, 'BR', -8.20111000, -35.56472000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q535373'),
(12003, 'Graça', 2016, 'CE', 31, 'BR', -4.06621000, -40.79501000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q2089608'),
(12004, 'Graça Aranha', 2015, 'MA', 31, 'BR', -5.44492000, -44.25169000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q2082297'),
(12005, 'Groaíras', 2016, 'CE', 31, 'BR', -3.92113000, -40.37696000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q2082297'),
(12006, 'Grossos', 2019, 'RN', 31, 'BR', -4.95242000, -37.20023000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2082297'),
(12007, 'Grupiara', 1998, 'MG', 31, 'BR', -18.47962000, -47.77141000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2082297'),
(12008, 'Grão Mogol', 1998, 'MG', 31, 'BR', -16.49009000, -42.96535000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q2082297'),
(12009, 'Grão Pará', 2014, 'SC', 31, 'BR', -28.13580000, -49.31612000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q2082297'),
(12010, 'Guabiju', 2001, 'RS', 31, 'BR', -28.58817000, -51.64765000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2082297'),
(12011, 'Guabiraba', 2006, 'PE', 31, 'BR', -8.41667000, -35.66667000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q22020770'),
(12012, 'Guabiruba', 2014, 'SC', 31, 'BR', -27.11189000, -49.02215000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1758034'),
(12013, 'Guadalupe', 2008, 'PI', 31, 'BR', -6.82297000, -43.77762000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2104157'),
(12014, 'Guaimbê', 2021, 'SP', 31, 'BR', -21.86204000, -49.84959000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q2104157'),
(12015, 'Guairaçá', 2022, 'PR', 31, 'BR', -22.90548000, -52.74878000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q2104157'),
(12016, 'Guaiçara', 2021, 'SP', 31, 'BR', -21.55410000, -49.76263000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q2104157'),
(12017, 'Guaiúba', 2016, 'CE', 31, 'BR', -4.03972000, -38.63722000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q2028404'),
(12018, 'Guajará', 2004, 'AM', 31, 'BR', -2.96667000, -57.66667000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q22020773'),
(12019, 'Guajará Mirim', 2013, 'RO', 31, 'BR', -10.78356000, -65.33552000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q22056905'),
(12020, 'Guajará-Mirim', 2013, 'RO', 31, 'BR', -11.33108000, -64.44095000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q983810'),
(12021, 'Guajeru', 2002, 'BA', 31, 'BR', -14.57881000, -42.03986000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q983810'),
(12022, 'Guamaré', 2019, 'RN', 31, 'BR', -5.13423000, -36.31854000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q983810'),
(12023, 'Guamiranga', 2022, 'PR', 31, 'BR', -25.15028000, -50.86182000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q983810'),
(12024, 'Guanambi', 2002, 'BA', 31, 'BR', -14.22333000, -42.78139000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q786924'),
(12025, 'Guanhães', 1998, 'MG', 31, 'BR', -18.85705000, -42.80520000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q1756864'),
(12026, 'Guapiara', 2021, 'SP', 31, 'BR', -24.21726000, -48.54523000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1756864'),
(12027, 'Guapiaçu', 2021, 'SP', 31, 'BR', -20.79500000, -49.22028000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1649865'),
(12028, 'Guapimirim', 1997, 'RJ', 31, 'BR', -22.58914000, -42.97530000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1761565'),
(12029, 'Guapirama', 2022, 'PR', 31, 'BR', -23.46840000, -50.09128000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1761565'),
(12030, 'Guaporema', 2022, 'PR', 31, 'BR', -23.31022000, -52.82479000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1761565'),
(12031, 'Guaporé', 2001, 'RS', 31, 'BR', -28.84191000, -51.91880000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1758136'),
(12032, 'Guapé', 1998, 'MG', 31, 'BR', -20.77404000, -45.89935000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q1758136'),
(12033, 'Guapó', 2000, 'GO', 31, 'BR', -16.83056000, -49.53194000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q986256'),
(12034, 'Guarabira', 2005, 'PB', 31, 'BR', -6.85472000, -35.49000000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1827121'),
(12035, 'Guaraci', 2021, 'SP', 31, 'BR', -20.41648000, -49.00521000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1827121'),
(12036, 'Guaraci', 2022, 'PR', 31, 'BR', -22.97343000, -51.69739000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1827121'),
(12037, 'Guaraciaba', 1998, 'MG', 31, 'BR', -20.56919000, -43.01146000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2008016'),
(12038, 'Guaraciaba', 2014, 'SC', 31, 'BR', -26.57386000, -53.59786000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2008016'),
(12039, 'Guaraciaba do Norte', 2016, 'CE', 31, 'BR', -4.16694000, -40.74750000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2027331'),
(12040, 'Guaraciama', 1998, 'MG', 31, 'BR', -17.08074000, -43.60308000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2027331'),
(12041, 'Guaramiranga', 2016, 'CE', 31, 'BR', -4.22177000, -38.96451000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q2027331'),
(12042, 'Guaramirim', 2014, 'SC', 31, 'BR', -26.47306000, -49.00278000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1799377'),
(12043, 'Guarani', 1998, 'MG', 31, 'BR', -21.35038000, -43.05888000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1998473'),
(12044, 'Guarani d\'Oeste', 2021, 'SP', 31, 'BR', -20.07032000, -50.36292000, '2019-10-05 22:35:05', '2019-10-05 22:35:05', 1, 'Q1649834'),
(12045, 'Guarani das Missões', 2001, 'RS', 31, 'BR', -28.15071000, -54.60026000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1649834'),
(12046, 'Guarani de Goiás', 2000, 'GO', 31, 'BR', -13.88485000, -46.50711000, '2019-10-05 22:35:05', '2020-05-01 17:22:36', 1, 'Q1649834'),
(12047, 'Guaraniaçu', 2022, 'PR', 31, 'BR', -25.10083000, -52.87806000, '2019-10-05 22:35:05', '2020-05-01 17:22:37', 1, 'Q1803606'),
(12048, 'Guarantã', 2021, 'SP', 31, 'BR', -21.92281000, -49.58519000, '2019-10-05 22:35:05', '2020-05-01 17:22:38', 1, 'Q1803606'),
(12049, 'Guarantã do Norte', 2011, 'MT', 31, 'BR', -9.75597000, -54.72890000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1803606'),
(12050, 'Guaranésia', 1998, 'MG', 31, 'BR', -21.28820000, -46.82229000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1790645'),
(12051, 'Guarapari', 2018, 'ES', 31, 'BR', -20.67182000, -40.50196000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q267439'),
(12052, 'Guarapuava', 2022, 'PR', 31, 'BR', -25.39048000, -51.46541000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q326354'),
(12053, 'Guaraqueçaba', 2022, 'PR', 31, 'BR', -25.15156000, -48.35949000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q326354'),
(12054, 'Guararapes', 2021, 'SP', 31, 'BR', -21.26083000, -50.64278000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q739292'),
(12055, 'Guararema', 2021, 'SP', 31, 'BR', -23.41500000, -46.03500000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q404820'),
(12056, 'Guarará', 1998, 'MG', 31, 'BR', -21.75802000, -43.02469000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q404820'),
(12057, 'Guaratinga', 2002, 'BA', 31, 'BR', -16.58564000, -39.78189000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q22042268'),
(12058, 'Guaratinguetá', 2021, 'SP', 31, 'BR', -22.81639000, -45.19250000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q905157'),
(12059, 'Guaratuba', 2022, 'PR', 31, 'BR', -25.88278000, -48.57472000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1779983'),
(12060, 'Guaraçaí', 2021, 'SP', 31, 'BR', -21.10820000, -51.30124000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1779983'),
(12061, 'Guaraí', 2020, 'TO', 31, 'BR', -8.70233000, -48.40251000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1779983'),
(12062, 'Guaraíta', 2000, 'GO', 31, 'BR', -15.65961000, -50.06663000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1779983'),
(12063, 'Guarda-Mor', 1998, 'MG', 31, 'BR', -17.76393000, -47.14784000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1790555'),
(12064, 'Guareí', 2021, 'SP', 31, 'BR', -23.37338000, -48.22466000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1790555'),
(12065, 'Guariba', 2021, 'SP', 31, 'BR', -21.39568000, -48.20427000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1759773'),
(12066, 'Guaribas', 2008, 'PI', 31, 'BR', -9.28870000, -43.58188000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1759773'),
(12067, 'Guarinos', 2000, 'GO', 31, 'BR', -14.69976000, -49.73100000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1759773'),
(12068, 'Guarujá', 2021, 'SP', 31, 'BR', -23.99306000, -46.25639000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q271374'),
(12069, 'Guarujá do Sul', 2014, 'SC', 31, 'BR', -26.40561000, -53.47207000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q271374'),
(12070, 'Guarulhos', 2021, 'SP', 31, 'BR', -23.46278000, -46.53333000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q184409'),
(12071, 'Guará', 2021, 'SP', 31, 'BR', -20.42833000, -47.82417000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q985569'),
(12072, 'Guatambú', 2014, 'SC', 31, 'BR', -27.11045000, -52.78938000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1784804'),
(12073, 'Guatapará', 2021, 'SP', 31, 'BR', -21.47231000, -47.99142000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1784804'),
(12074, 'Guaxupé', 1998, 'MG', 31, 'BR', -21.29181000, -46.68110000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1791417'),
(12075, 'Guaçuí', 2018, 'ES', 31, 'BR', -20.76804000, -41.71116000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1807792'),
(12076, 'Guaíba', 2001, 'RS', 31, 'BR', -30.11389000, -51.32500000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q957668'),
(12077, 'Guaíra', 2021, 'SP', 31, 'BR', -20.31833000, -48.31056000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q2013622'),
(12078, 'Guaíra', 2022, 'PR', 31, 'BR', -24.26175000, -54.23821000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q2013622'),
(12079, 'Guia Lopes da Laguna', 2010, 'MS', 31, 'BR', -21.61081000, -55.93458000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1793002'),
(12080, 'Guidoval', 1998, 'MG', 31, 'BR', -21.17678000, -42.79015000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1793002'),
(12081, 'Guimarânia', 1998, 'MG', 31, 'BR', -18.81689000, -46.73553000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1793002'),
(12082, 'Guimarães', 2015, 'MA', 31, 'BR', -2.11990000, -44.63479000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q2101408'),
(12083, 'Guiratinga', 2011, 'MT', 31, 'BR', -16.34534000, -53.76177000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2011948'),
(12084, 'Guiricema', 1998, 'MG', 31, 'BR', -21.01294000, -42.69893000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2011948'),
(12085, 'Gurinhatã', 1998, 'MG', 31, 'BR', -19.06643000, -49.86875000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q2011948'),
(12086, 'Gurinhém', 2005, 'PB', 31, 'BR', -7.12389000, -35.42444000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1991293'),
(12087, 'Gurjão', 2005, 'PB', 31, 'BR', -7.26100000, -36.50008000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1991293'),
(12088, 'Gurupi', 2020, 'TO', 31, 'BR', -11.72917000, -49.06861000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1784265'),
(12089, 'Gurupá', 2009, 'PA', 31, 'BR', -1.16555000, -51.62743000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q2011913'),
(12090, 'Guzolândia', 2021, 'SP', 31, 'BR', -20.62040000, -50.71925000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q2011913'),
(12091, 'Gália', 2021, 'SP', 31, 'BR', -22.34323000, -49.57865000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q2011913'),
(12092, 'Harmonia', 2001, 'RS', 31, 'BR', -29.54904000, -51.42357000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1151685'),
(12093, 'Heitoraí', 2000, 'GO', 31, 'BR', -15.73416000, -49.83108000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1151685'),
(12094, 'Heliodora', 1998, 'MG', 31, 'BR', -22.04159000, -45.54430000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2007752'),
(12095, 'Heliópolis', 2002, 'BA', 31, 'BR', -10.72863000, -38.25490000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q2019055'),
(12096, 'Herculândia', 2021, 'SP', 31, 'BR', -21.96198000, -50.38482000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1759097'),
(12097, 'Herval', 2001, 'RS', 31, 'BR', -32.02361000, -53.39556000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q22042330'),
(12098, 'Herval d\'Oeste', 2014, 'SC', 31, 'BR', -27.20408000, -51.42029000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1784481'),
(12099, 'Herveiras', 2001, 'RS', 31, 'BR', -29.43864000, -52.67366000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1784481'),
(12100, 'Hidrolina', 2000, 'GO', 31, 'BR', -14.75739000, -49.35965000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1784481'),
(12101, 'Hidrolândia', 2016, 'CE', 31, 'BR', -4.40806000, -40.43778000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q2347636'),
(12102, 'Hidrolândia', 2000, 'GO', 31, 'BR', -16.96222000, -49.22806000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q988811'),
(12103, 'Holambra', 2021, 'SP', 31, 'BR', -22.63825000, -47.06501000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q988811'),
(12104, 'Honório Serpa', 2022, 'PR', 31, 'BR', -26.13779000, -52.39614000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q687445'),
(12105, 'Horizonte', 2016, 'CE', 31, 'BR', -4.11458000, -38.51498000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q22059559'),
(12106, 'Horizontina', 2001, 'RS', 31, 'BR', -27.62583000, -54.30778000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q785177'),
(12107, 'Hortolândia', 2021, 'SP', 31, 'BR', -22.85833000, -47.22000000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q952617'),
(12108, 'Hugo Napoleão', 2008, 'PI', 31, 'BR', -6.03293000, -42.46773000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q952617'),
(12109, 'Hulha Negra', 2001, 'RS', 31, 'BR', -31.57199000, -53.86271000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q952617'),
(12110, 'Humaitá', 2004, 'AM', 31, 'BR', -7.51651000, -63.03105000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1025760'),
(12111, 'Humaitá', 2001, 'RS', 31, 'BR', -27.58561000, -53.99627000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1025760'),
(12112, 'Humberto de Campos', 2015, 'MA', 31, 'BR', -2.59833000, -43.46111000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1814500'),
(12113, 'Iacanga', 2021, 'SP', 31, 'BR', -21.90745000, -49.05917000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1814500'),
(12114, 'Iaciara', 2000, 'GO', 31, 'BR', -14.09583000, -46.63167000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1806877'),
(12115, 'Iacri', 2021, 'SP', 31, 'BR', -21.77798000, -50.61517000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1806877'),
(12116, 'Iapu', 1998, 'MG', 31, 'BR', -19.35090000, -42.23727000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1806877'),
(12117, 'Iaras', 2021, 'SP', 31, 'BR', -22.83679000, -49.10928000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1806877'),
(12118, 'Iati', 2006, 'PE', 31, 'BR', -9.19129000, -36.94946000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1806877'),
(12119, 'Iaçu', 2002, 'BA', 31, 'BR', -12.76722000, -40.21167000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1762080'),
(12120, 'Ibaiti', 2022, 'PR', 31, 'BR', -23.84861000, -50.18778000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2005227'),
(12121, 'Ibarama', 2001, 'RS', 31, 'BR', -29.40889000, -53.18252000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2005227'),
(12122, 'Ibaretama', 2016, 'CE', 31, 'BR', -4.75045000, -38.66571000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2005227'),
(12123, 'Ibateguara', 2007, 'AL', 31, 'BR', -8.97250000, -35.93944000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2021130'),
(12124, 'Ibatiba', 2018, 'ES', 31, 'BR', -20.23389000, -41.51056000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1795195'),
(12125, 'Ibaté', 2021, 'SP', 31, 'BR', -21.95472000, -47.99667000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1760989'),
(12126, 'Ibema', 2022, 'PR', 31, 'BR', -25.16493000, -53.01268000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1760989'),
(12127, 'Ibertioga', 1998, 'MG', 31, 'BR', -21.45140000, -43.94316000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1760989'),
(12128, 'Ibiam', 2014, 'SC', 31, 'BR', -27.20944000, -51.22230000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1760989'),
(12129, 'Ibiapina', 2016, 'CE', 31, 'BR', -3.93574000, -40.92758000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2028027'),
(12130, 'Ibiara', 2005, 'PB', 31, 'BR', -7.48251000, -38.38051000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2028027'),
(12131, 'Ibiassucê', 2002, 'BA', 31, 'BR', -14.20285000, -42.30214000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q2028027'),
(12132, 'Ibiaçá', 2001, 'RS', 31, 'BR', -28.10474000, -51.80283000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q2028027'),
(12133, 'Ibiaí', 1998, 'MG', 31, 'BR', -16.81004000, -44.79235000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q2028027'),
(12134, 'Ibicaraí', 2002, 'BA', 31, 'BR', -14.86500000, -39.58750000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q741470'),
(12135, 'Ibicaré', 2014, 'SC', 31, 'BR', -27.09743000, -51.37144000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q741470'),
(12136, 'Ibicoara', 2002, 'BA', 31, 'BR', -13.37664000, -41.34081000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q741470'),
(12137, 'Ibicuitinga', 2016, 'CE', 31, 'BR', -4.96187000, -38.52019000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q741470'),
(12138, 'Ibicuí', 2002, 'BA', 31, 'BR', -14.84167000, -39.98667000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1795367'),
(12139, 'Ibimirim', 2006, 'PE', 31, 'BR', -8.54056000, -37.69028000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1804109'),
(12140, 'Ibipeba', 2002, 'BA', 31, 'BR', -11.64083000, -42.01111000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1798809'),
(12141, 'Ibipitanga', 2002, 'BA', 31, 'BR', -12.88655000, -42.37115000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1798809'),
(12142, 'Ibiporã', 2022, 'PR', 31, 'BR', -23.26917000, -51.04806000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q2006392'),
(12143, 'Ibiquera', 2002, 'BA', 31, 'BR', -12.57265000, -40.84158000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2006392'),
(12144, 'Ibiracatu', 1998, 'MG', 31, 'BR', -15.66695000, -44.13281000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2006392'),
(12145, 'Ibiraci', 1998, 'MG', 31, 'BR', -20.40137000, -47.14267000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2006392'),
(12146, 'Ibiraiaras', 2001, 'RS', 31, 'BR', -28.38729000, -51.62710000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2006392'),
(12147, 'Ibirajuba', 2006, 'PE', 31, 'BR', -8.61290000, -36.15399000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q2006392'),
(12148, 'Ibirama', 2014, 'SC', 31, 'BR', -27.05694000, -49.51778000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q747403'),
(12149, 'Ibirapitanga', 2002, 'BA', 31, 'BR', -14.16417000, -39.37361000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1647812'),
(12150, 'Ibirapuitã', 2001, 'RS', 31, 'BR', -28.61696000, -52.47077000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1647812'),
(12151, 'Ibirapuã', 2002, 'BA', 31, 'BR', -17.75567000, -39.97365000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1647812'),
(12152, 'Ibirarema', 2021, 'SP', 31, 'BR', -22.80911000, -50.07816000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1647812'),
(12153, 'Ibirataia', 2002, 'BA', 31, 'BR', -14.06694000, -39.64056000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1772538'),
(12154, 'Ibiraçu', 2018, 'ES', 31, 'BR', -19.83194000, -40.36972000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1807804'),
(12155, 'Ibirité', 1998, 'MG', 31, 'BR', -20.02194000, -44.05889000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1751207'),
(12156, 'Ibirubá', 2001, 'RS', 31, 'BR', -28.62750000, -53.08972000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q194423'),
(12157, 'Ibirá', 2021, 'SP', 31, 'BR', -21.08880000, -49.21879000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q194423'),
(12158, 'Ibitiara', 2002, 'BA', 31, 'BR', -12.56582000, -42.38990000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q194423'),
(12159, 'Ibitinga', 2021, 'SP', 31, 'BR', -21.75778000, -48.82889000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1649661'),
(12160, 'Ibitirama', 2018, 'ES', 31, 'BR', -20.51148000, -41.66962000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1807726'),
(12161, 'Ibititá', 2002, 'BA', 31, 'BR', -11.69354000, -41.84148000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1807726'),
(12162, 'Ibitiúra de Minas', 1998, 'MG', 31, 'BR', -22.06721000, -46.40735000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1807726'),
(12163, 'Ibituruna', 1998, 'MG', 31, 'BR', -21.16363000, -44.77552000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1807726'),
(12164, 'Ibiá', 1998, 'MG', 31, 'BR', -19.54368000, -46.61732000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1756458'),
(12165, 'Ibiúna', 2021, 'SP', 31, 'BR', -23.65639000, -47.22250000, '2019-10-05 22:35:06', '2020-05-01 17:22:38', 1, 'Q1795639'),
(12166, 'Ibotirama', 2002, 'BA', 31, 'BR', -12.18528000, -43.22056000, '2019-10-05 22:35:06', '2019-10-05 22:35:06', 1, 'Q1761863'),
(12167, 'Icapuí', 2016, 'CE', 31, 'BR', -4.72337000, -37.41335000, '2019-10-05 22:35:06', '2020-05-01 17:22:36', 1, 'Q1761863'),
(12168, 'Icaraí de Minas', 1998, 'MG', 31, 'BR', -16.22108000, -44.85922000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1761863'),
(12169, 'Icaraíma', 2022, 'PR', 31, 'BR', -23.37235000, -53.59492000, '2019-10-05 22:35:06', '2020-05-01 17:22:37', 1, 'Q1761863');

