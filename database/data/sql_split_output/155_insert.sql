INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(78351, '<PERSON>elo', 2618, 'O<PERSON>', 156, 'NL', 52.23500000, 6.49861000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q852674'),
(78352, 'Marken', 2612, 'NH', 156, 'NL', 52.45833000, 5.10278000, '2019-10-05 23:11:51', '2022-05-08 22:22:07', 1, 'Q33460541'),
(78353, 'Marrum', 2622, 'FR', 156, 'NL', 53.32277000, 5.80198000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2304407'),
(78354, 'Marsdijk', 2613, 'DR', 156, '<PERSON>', 53.01766000, 6.58527000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2304407'),
(78355, '<PERSON><PERSON>', 2622, 'FR', 156, 'NL', 53.21050000, 5.72637000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78356, 'Marum', 2617, 'GR', 156, 'NL', 53.14417000, 6.26250000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78357, 'Matendonk', 2611, 'GE', 156, 'NL', 52.19766000, 6.01115000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78358, 'Matendreef', 2611, 'GE', 156, 'NL', 52.19993000, 5.98592000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78359, 'Matengaarde', 2611, 'GE', 156, 'NL', 52.19793000, 5.99570000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78360, 'Matenhoeve', 2611, 'GE', 156, 'NL', 52.19056000, 6.01338000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78361, 'Matenhorst', 2611, 'GE', 156, 'NL', 52.20482000, 5.99304000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78362, 'Matenveld', 2611, 'GE', 156, 'NL', 52.18767000, 5.99879000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2527568'),
(78363, 'Maurik', 2611, 'GE', 156, 'NL', 51.96083000, 5.42222000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2453631'),
(78364, 'Mechelen', 2615, 'LI', 156, 'NL', 50.79583000, 5.92639000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q100020'),
(78365, 'Medemblik', 2612, 'NH', 156, 'NL', 52.77167000, 5.10556000, '2019-10-05 23:11:51', '2022-05-08 22:22:07', 1, 'Q9947'),
(78366, 'Meeden', 2617, 'GR', 156, 'NL', 53.14000000, 6.92639000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q534822'),
(78367, 'Meerhoven', 2623, 'NB', 156, 'NL', 51.44267000, 5.41102000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q4488950'),
(78368, 'Meerlo', 2615, 'LI', 156, 'NL', 51.51333000, 6.08472000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2672250'),
(78369, 'Meerssen', 2615, 'LI', 156, 'NL', 50.88750000, 5.75000000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2452442'),
(78370, 'Meerveldhoven', 2623, 'NB', 156, 'NL', 51.41705000, 5.41618000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2452442'),
(78371, 'Meezenbroek', 2615, 'LI', 156, 'NL', 50.89680000, 5.99051000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2452442'),
(78372, 'Megen', 2623, 'NB', 156, 'NL', 51.82167000, 5.56250000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1862257'),
(78373, 'Meierijstad', 2623, 'NB', 156, 'NL', 51.59968000, 5.50278000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q18655255'),
(78374, 'Meijel', 2615, 'LI', 156, 'NL', 51.34417000, 5.88472000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1007185'),
(78375, 'Mekkelholt', 2618, 'OV', 156, 'NL', 52.23645000, 6.89058000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1007185'),
(78376, 'Melderslo', 2615, 'LI', 156, 'NL', 51.46167000, 6.08611000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1827878'),
(78377, 'Melick', 2615, 'LI', 156, 'NL', 51.15917000, 6.01667000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1019508'),
(78378, 'Meliskerke', 2620, 'ZE', 156, 'NL', 51.51417000, 3.50972000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2157654'),
(78379, 'Melle', 2623, 'NB', 156, 'NL', 51.66244000, 5.63367000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2157654'),
(78380, 'Menaam', 2622, 'FR', 156, 'NL', 53.21797000, 5.66124000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2246377'),
(78381, 'Meppel', 2613, 'DR', 156, 'NL', 52.69583000, 6.19444000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2246377'),
(78382, 'Merenwijk', 2614, 'ZH', 156, 'NL', 52.17655000, 4.50885000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2246377'),
(78383, 'Merkelbeek', 2615, 'LI', 156, 'NL', 50.95389000, 5.94069000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3030556'),
(78384, 'Merselo', 2615, 'LI', 156, 'NL', 51.53000000, 5.92778000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3030556'),
(78385, 'Merum', 2615, 'LI', 156, 'NL', 51.17333000, 5.95972000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3030556'),
(78386, 'Meteren', 2611, 'GE', 156, 'NL', 51.86500000, 5.28333000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3112511'),
(78387, 'Middelbeers', 2623, 'NB', 156, 'NL', 51.46667000, 5.25000000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q600691'),
(78388, 'Middelburg', 2620, 'ZE', 156, 'NL', 51.50000000, 3.61389000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q52101'),
(78389, 'Middelharnis', 2614, 'ZH', 156, 'NL', 51.75750000, 4.16528000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q848898'),
(78390, 'Middelrode', 2623, 'NB', 156, 'NL', 51.66417000, 5.41944000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q848898'),
(78391, 'Middelsluis', 2614, 'ZH', 156, 'NL', 51.74250000, 4.44167000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q6841164'),
(78392, 'Middelstum', 2617, 'GR', 156, 'NL', 53.34667000, 6.64167000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q847063'),
(78393, 'Midden-Groningen', 2617, 'GR', 156, 'NL', 53.15113000, 6.83313000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q23991032'),
(78394, 'Middenbeemster', 2612, 'NH', 156, 'NL', 52.54917000, 4.91250000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2681548'),
(78395, 'Middenmeer', 2612, 'NH', 156, 'NL', 52.80667000, 4.99861000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3312626'),
(78396, 'Midwolda', 2617, 'GR', 156, 'NL', 53.19500000, 7.01389000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1879065'),
(78397, 'Midwoud', 2612, 'NH', 156, 'NL', 52.71667000, 5.07500000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3312746'),
(78398, 'Mierlo', 2623, 'NB', 156, 'NL', 51.44000000, 5.61944000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1614337'),
(78399, 'Mijdrecht', 2610, 'UT', 156, 'NL', 52.20667000, 4.86250000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q666498'),
(78400, 'Mijnsheerenland', 2614, 'ZH', 156, 'NL', 51.79667000, 4.48750000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q922981'),
(78401, 'Milheeze', 2623, 'NB', 156, 'NL', 51.50167000, 5.77917000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3314115'),
(78402, 'Milsbeek', 2615, 'LI', 156, 'NL', 51.72500000, 5.94861000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q3030568'),
(78403, 'Minnertsga', 2622, 'FR', 156, 'NL', 53.25104000, 5.59513000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2634202'),
(78404, 'Moerdijk', 2623, 'NB', 156, 'NL', 51.70167000, 4.62639000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q9854'),
(78405, 'Moesel', 2615, 'LI', 156, 'NL', 51.23828000, 5.71778000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q9854'),
(78406, 'Molenhoek', 2623, 'NB', 156, 'NL', 51.71113000, 5.36809000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q9854'),
(78407, 'Monnickendam', 2612, 'NH', 156, 'NL', 52.45833000, 5.03750000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q81276'),
(78408, 'Monster', 2614, 'ZH', 156, 'NL', 52.02583000, 4.17500000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q969531'),
(78409, 'Montfoort', 2610, 'UT', 156, 'NL', 52.04583000, 4.95278000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q969531'),
(78410, 'Montfort', 2615, 'LI', 156, 'NL', 51.12583000, 5.94861000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q21054'),
(78411, 'Mook', 2615, 'LI', 156, 'NL', 51.75250000, 5.88194000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q21054'),
(78412, 'Muiden', 2612, 'NH', 156, 'NL', 52.33000000, 5.06944000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q9948'),
(78413, 'Muiderberg', 2612, 'NH', 156, 'NL', 52.32583000, 5.12083000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1807382'),
(78414, 'Munstergeleen', 2615, 'LI', 156, 'NL', 50.97500000, 5.86389000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q769641'),
(78415, 'Muschberg en Geestenberg', 2623, 'NB', 156, 'NL', 51.44384000, 5.52329000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q2167703'),
(78416, 'Naaldwijk', 2614, 'ZH', 156, 'NL', 51.99417000, 4.20972000, '2019-10-05 23:11:51', '2019-10-05 23:11:51', 1, 'Q1613459'),
(78417, 'Naarden', 2612, 'NH', 156, 'NL', 52.29583000, 5.16250000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q9949'),
(78418, 'Naastenbest', 2623, 'NB', 156, 'NL', 51.50484000, 5.38579000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q9949'),
(78419, 'Neder-Hardinxveld', 2614, 'ZH', 156, 'NL', 51.82879000, 4.85489000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2645142'),
(78420, 'Nederhemert', 2611, 'GE', 156, 'NL', 51.76518000, 5.16817000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2677372'),
(78421, 'Nederhemert-Noord', 2611, 'GE', 156, 'NL', 51.76322000, 5.17305000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2677372'),
(78422, 'Nederweert', 2615, 'LI', 156, 'NL', 51.28583000, 5.74861000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q24084713'),
(78423, 'Nederwoud', 2611, 'GE', 156, 'NL', 52.10083000, 5.57083000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q24084713'),
(78424, 'Neede', 2611, 'GE', 156, 'NL', 52.13417000, 6.61389000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1350047'),
(78425, 'Neerbeek', 2615, 'LI', 156, 'NL', 50.95000000, 5.81528000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1350047'),
(78426, 'Neerkant', 2623, 'NB', 156, 'NL', 51.36833000, 5.86667000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1350047'),
(78427, 'Nes', 2622, 'FR', 156, 'NL', 53.44502000, 5.77400000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1000685'),
(78428, 'Niekerk', 2617, 'GR', 156, 'NL', 53.22500000, 6.35278000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1000685'),
(78429, 'Nieuw- en Sint Joosland', 2620, 'ZE', 156, 'NL', 51.48333000, 3.65694000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1025089'),
(78430, 'Nieuw-Beijerland', 2614, 'ZH', 156, 'NL', 51.81250000, 4.34306000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2060859'),
(78431, 'Nieuw-Buinen', 2613, 'DR', 156, 'NL', 52.96250000, 6.95000000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2281401'),
(78432, 'Nieuw-Dordrecht', 2613, 'DR', 156, 'NL', 52.74833000, 6.96806000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3030491'),
(78433, 'Nieuw-Lekkerland', 2614, 'ZH', 156, 'NL', 51.88915000, 4.68653000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q846242'),
(78434, 'Nieuw-Loosdrecht', 2612, 'NH', 156, 'NL', 52.19917000, 5.13889000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3341323'),
(78435, 'Nieuw-Lotbroek', 2615, 'LI', 156, 'NL', 50.91283000, 5.92798000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3341323'),
(78436, 'Nieuw-Roden', 2613, 'DR', 156, 'NL', 53.13167000, 6.39722000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2231797'),
(78437, 'Nieuw-Vossemeer', 2623, 'NB', 156, 'NL', 51.59000000, 4.21806000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2576055'),
(78438, 'Nieuwdorp', 2615, 'LI', 156, 'NL', 50.96216000, 5.77297000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2576055'),
(78439, 'Nieuwe Pekela', 2617, 'GR', 156, 'NL', 53.07917000, 6.96528000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2900922'),
(78440, 'Nieuwe Niedorp', 2612, 'NH', 156, 'NL', 52.74000000, 4.89861000, '2019-10-05 23:11:52', '2022-05-08 22:30:41', 1, 'Q971130'),
(78441, 'Nieuwegein', 2610, 'UT', 156, 'NL', 52.02917000, 5.08056000, '2019-10-05 23:11:52', '2022-05-08 22:31:01', 1, 'Q10050'),
(78442, 'Nieuwehorne', 2622, 'FR', 156, 'NL', 52.95113000, 6.06342000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q977728'),
(78443, 'Nieuwenhoorn', 2614, 'ZH', 156, 'NL', 51.85417000, 4.14306000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2060530'),
(78444, 'Nieuwerkerk', 2620, 'ZE', 156, 'NL', 51.65083000, 4.00139000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2595170'),
(78445, 'Nieuwkoop', 2614, 'ZH', 156, 'NL', 52.15083000, 4.77639000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q33467336'),
(78446, 'Nieuwkuijk', 2623, 'NB', 156, 'NL', 51.69000000, 5.18194000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2376492'),
(78447, 'Nieuwolda', 2617, 'GR', 156, 'NL', 53.24417000, 6.97500000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2483664'),
(78448, 'Nieuwoord', 2618, 'OV', 156, 'NL', 52.46667000, 6.55000000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2483664'),
(78449, 'Nieuwpoort', 2614, 'ZH', 156, 'NL', 51.93583000, 4.86806000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2181664'),
(78450, 'Nieuwveen', 2614, 'ZH', 156, 'NL', 52.19667000, 4.75694000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2177358'),
(78451, 'Nijenheim', 2610, 'UT', 156, 'NL', 52.08689000, 5.21852000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2177358'),
(78452, 'Nijkerk', 2611, 'GE', 156, 'NL', 52.22000000, 5.48611000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q16220881'),
(78453, 'Nijkerkerveen', 2611, 'GE', 156, 'NL', 52.19500000, 5.46667000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3056786'),
(78454, 'Nijmegen', 2611, 'GE', 156, 'NL', 51.84250000, 5.85278000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3056786'),
(78455, 'Nijnsel', 2623, 'NB', 156, 'NL', 51.55083000, 5.48333000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2999962'),
(78456, 'Nijrees', 2618, 'OV', 156, 'NL', 52.33500000, 6.66389000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2999962'),
(78457, 'Nispen', 2623, 'NB', 156, 'NL', 51.48331000, 4.46131000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q670577'),
(78458, 'Nissewaard', 2614, 'ZH', 156, 'NL', 51.83716000, 4.27540000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q14853527'),
(78459, 'Nistelrode', 2623, 'NB', 156, 'NL', 51.70417000, 5.56250000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2218972'),
(78460, 'Noardburgum', 2622, 'FR', 156, 'NL', 53.22135000, 6.00523000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2203454'),
(78461, 'Noord-Hofland', 2614, 'ZH', 156, 'NL', 52.14059000, 4.45864000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2203454'),
(78462, 'Noord-Scharwoude', 2612, 'NH', 156, 'NL', 52.69833000, 4.81111000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2925960'),
(78463, 'Noordbarge', 2613, 'DR', 156, 'NL', 52.77237000, 6.88713000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2191445'),
(78464, 'Noordbroek', 2617, 'GR', 156, 'NL', 53.19500000, 6.87361000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1924750'),
(78465, 'Noordeinde', 2614, 'ZH', 156, 'NL', 52.01667000, 4.48333000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1924750'),
(78466, 'Noordeloos', 2614, 'ZH', 156, 'NL', 51.90333000, 4.94167000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1866380'),
(78467, 'Noordgeest', 2623, 'NB', 156, 'NL', 51.50917000, 4.27917000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1866380'),
(78468, 'Noordhoek', 2623, 'NB', 156, 'NL', 51.64250000, 4.53194000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2854358'),
(78469, 'Noordhorn', 2617, 'GR', 156, 'NL', 53.26167000, 6.39583000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3030548'),
(78470, 'Noordwijk-Binnen', 2614, 'ZH', 156, 'NL', 52.23400000, 4.44474000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q33467683'),
(78471, 'Noordwijkerhout', 2614, 'ZH', 156, 'NL', 52.26167000, 4.49306000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q33467683'),
(78472, 'Noordwolde', 2622, 'FR', 156, 'NL', 52.88964000, 6.14153000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2673803'),
(78473, 'Norg', 2613, 'DR', 156, 'NL', 53.06667000, 6.45833000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q934932'),
(78474, 'Nuenen', 2623, 'NB', 156, 'NL', 51.47000000, 5.55278000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q153516'),
(78475, 'Numansdorp', 2614, 'ZH', 156, 'NL', 51.73167000, 4.43750000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q518691'),
(78476, 'Nunspeet', 2611, 'GE', 156, 'NL', 52.37917000, 5.78611000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q16070330'),
(78477, 'Nuth', 2615, 'LI', 156, 'NL', 50.91750000, 5.88611000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q13905945'),
(78478, 'Obbicht', 2615, 'LI', 156, 'NL', 51.02833000, 5.78056000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q13905945'),
(78479, 'Obdam', 2612, 'NH', 156, 'NL', 52.67583000, 4.90694000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q649748'),
(78480, 'Obergum', 2617, 'GR', 156, 'NL', 53.33333000, 6.51667000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2675632'),
(78481, 'Ochten', 2611, 'GE', 156, 'NL', 51.91000000, 5.56944000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2525207'),
(78482, 'Odijk', 2610, 'UT', 156, 'NL', 52.05250000, 5.23611000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2124349'),
(78483, 'Odiliapeel', 2623, 'NB', 156, 'NL', 51.64333000, 5.70556000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2176041'),
(78484, 'Odoorn', 2613, 'DR', 156, 'NL', 52.84917000, 6.85139000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2694139'),
(78485, 'Oegstgeest', 2614, 'ZH', 156, 'NL', 52.18000000, 4.46944000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2694139'),
(78486, 'Oentsjerk', 2622, 'FR', 156, 'NL', 53.25000000, 5.90000000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3032454'),
(78487, 'Oerle', 2623, 'NB', 156, 'NL', 51.42255000, 5.37163000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2127697'),
(78488, 'Offenbeek', 2615, 'LI', 156, 'NL', 51.28225000, 6.09500000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2202050'),
(78489, 'Oirlo', 2615, 'LI', 156, 'NL', 51.51167000, 6.03750000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2202050'),
(78490, 'Oirsbeek', 2615, 'LI', 156, 'NL', 50.95083000, 5.90833000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2640688'),
(78491, 'Oirschot', 2623, 'NB', 156, 'NL', 51.50500000, 5.31389000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2414583'),
(78492, 'Oisterwijk', 2623, 'NB', 156, 'NL', 51.57917000, 5.18889000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3299324'),
(78493, 'Oldeberkoop', 2622, 'FR', 156, 'NL', 52.93788000, 6.13089000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2634690'),
(78494, 'Oldebroek', 2611, 'GE', 156, 'NL', 52.44500000, 5.90139000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2634690'),
(78495, 'Oldehove', 2617, 'GR', 156, 'NL', 53.30333000, 6.39583000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3030112'),
(78496, 'Oldemarkt', 2618, 'OV', 156, 'NL', 52.82083000, 5.97500000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3117018'),
(78497, 'Oldenzaal', 2618, 'OV', 156, 'NL', 52.31333000, 6.92917000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q33469242'),
(78498, 'Olst', 2618, 'OV', 156, 'NL', 52.33750000, 6.10972000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3032495'),
(78499, 'Ommelanderwijk', 2617, 'GR', 156, 'NL', 53.08917000, 6.90556000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3032495'),
(78500, 'Ommen', 2618, 'OV', 156, 'NL', 52.52083000, 6.42083000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3032495'),
(78501, 'Ommoord', 2614, 'ZH', 156, 'NL', 51.95951000, 4.54533000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3144479'),
(78502, 'Onderdijk', 2612, 'NH', 156, 'NL', 52.74333000, 5.13750000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q3144479'),
(78503, 'Onstwedde', 2617, 'GR', 156, 'NL', 53.03500000, 7.04028000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2382045'),
(78504, 'Oog in Al', 2610, 'UT', 156, 'NL', 52.08636000, 5.08470000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2382045'),
(78505, 'Ooij', 2611, 'GE', 156, 'NL', 51.85465000, 5.93915000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2382045'),
(78506, 'Oost Gelre', 2611, 'GE', 156, 'NL', 52.00425000, 6.54958000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1147580'),
(78507, 'Oost-Vlieland', 2622, 'FR', 156, 'NL', 53.29703000, 5.07431000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q2719861'),
(78508, 'Oostburg', 2620, 'ZE', 156, 'NL', 51.32583000, 3.48750000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1615032'),
(78509, 'Oostdorp', 2614, 'ZH', 156, 'NL', 52.14994000, 4.39319000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1615032'),
(78510, 'Oosteind', 2623, 'NB', 156, 'NL', 51.64418000, 4.89784000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q1615032'),
(78511, 'Oosteinde', 2612, 'NH', 156, 'NL', 52.27917000, 4.79583000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q20991207'),
(78512, 'Oostelbeers', 2623, 'NB', 156, 'NL', 51.47171000, 5.26897000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q784019'),
(78513, 'Oostendorp', 2611, 'GE', 156, 'NL', 52.44722000, 5.85443000, '2019-10-05 23:11:52', '2019-10-05 23:11:52', 1, 'Q784019'),
(78514, 'Oosterbeek', 2611, 'GE', 156, 'NL', 51.98583000, 5.84583000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2218481'),
(78515, 'Blokker', 2612, 'NH', 156, 'NL', 52.66917000, 5.11806000, '2019-10-05 23:11:53', '2022-05-08 22:28:04', 1, 'Q1991993'),
(78516, 'Oosterhesselen', 2613, 'DR', 156, 'NL', 52.75417000, 6.72222000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q523812'),
(78517, 'Oosterholt', 2618, 'OV', 156, 'NL', 52.55917000, 5.95556000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q523812'),
(78518, 'Oosterhoogebrug', 2617, 'GR', 156, 'NL', 53.22892000, 6.60141000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q523812'),
(78519, 'Oosterhout', 2623, 'NB', 156, 'NL', 51.64500000, 4.85972000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q12912978'),
(78520, 'Oosterhout', 2611, 'GE', 156, 'NL', 51.88000000, 5.82639000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2439969'),
(78521, 'Oosterland', 2620, 'ZE', 156, 'NL', 51.65000000, 4.03611000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2299625'),
(78522, 'Oostermeenthe', 2618, 'OV', 156, 'NL', 52.79319000, 6.13138000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2299625'),
(78523, 'Oosterpark', 2617, 'GR', 156, 'NL', 53.22370000, 6.58530000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2299625'),
(78524, 'Oosterwolde', 2622, 'FR', 156, 'NL', 52.99164000, 6.29096000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2678235'),
(78525, 'Oosterzij', 2612, 'NH', 156, 'NL', 52.58500000, 4.70556000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2678235'),
(78526, 'Oosthuizen', 2612, 'NH', 156, 'NL', 52.57250000, 4.99583000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2504814'),
(78527, 'Oostkapelle', 2620, 'ZE', 156, 'NL', 51.56667000, 3.55139000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1112099'),
(78528, 'Oostrum', 2615, 'LI', 156, 'NL', 51.52917000, 6.01667000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1112099'),
(78529, 'Oostvoorne', 2614, 'ZH', 156, 'NL', 51.91250000, 4.09861000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2738927'),
(78530, 'Ootmarsum', 2618, 'OV', 156, 'NL', 52.40833000, 6.90139000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1615365'),
(78531, 'Ooy', 2611, 'GE', 156, 'NL', 51.91917000, 6.05833000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1615365'),
(78532, 'Ooyerhoek', 2611, 'GE', 156, 'NL', 52.12838000, 6.22307000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1615365'),
(78533, 'Opeinde', 2622, 'FR', 156, 'NL', 53.13410000, 6.05656000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3030193'),
(78534, 'Ophemert', 2611, 'GE', 156, 'NL', 51.84500000, 5.38750000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3110411'),
(78535, 'Opheusden', 2611, 'GE', 156, 'NL', 51.93167000, 5.63194000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3183256'),
(78536, 'Ophoven', 2615, 'LI', 156, 'NL', 50.99036000, 5.85868000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3183256'),
(78537, 'Opijnen', 2611, 'GE', 156, 'NL', 51.82917000, 5.29861000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2098932'),
(78538, 'Opmeer', 2612, 'NH', 156, 'NL', 52.70667000, 4.94444000, '2019-10-05 23:11:53', '2022-05-08 22:22:07', 1, 'Q9952'),
(78539, 'Oppenhuizen', 2622, 'FR', 156, 'NL', 53.01194000, 5.69495000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q639019'),
(78540, 'Opperdoes', 2612, 'NH', 156, 'NL', 52.75915000, 5.07534000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3354353'),
(78541, 'Oranjewijk', 2614, 'ZH', 156, 'NL', 52.04922000, 4.65374000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3354353'),
(78542, 'Oranjewoud', 2622, 'FR', 156, 'NL', 52.94579000, 5.95038000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3117008'),
(78543, 'Orden', 2611, 'GE', 156, 'NL', 52.20113000, 5.93417000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3117008'),
(78544, 'Orthen', 2623, 'NB', 156, 'NL', 51.70613000, 5.30468000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3117008'),
(78545, 'Ospel', 2615, 'LI', 156, 'NL', 51.29750000, 5.78472000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2752526'),
(78546, 'Oss', 2623, 'NB', 156, 'NL', 51.76500000, 5.51806000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q13971034'),
(78547, 'Ossendrecht', 2623, 'NB', 156, 'NL', 51.39417000, 4.32639000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q579722'),
(78548, 'Othene', 2620, 'ZE', 156, 'NL', 51.33083000, 3.85972000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q579722'),
(78549, 'Otterlo', 2611, 'GE', 156, 'NL', 52.10000000, 5.77222000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1507955'),
(78550, 'Ottersum', 2615, 'LI', 156, 'NL', 51.70333000, 5.98333000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2189395'),
(78551, 'Oud Gastel', 2623, 'NB', 156, 'NL', 51.58667000, 4.45972000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2189395'),
(78552, 'Oud-Beijerland', 2614, 'ZH', 156, 'NL', 51.82417000, 4.41250000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2189395'),
(78553, 'Oud-Caberg', 2615, 'LI', 156, 'NL', 50.86539000, 5.66444000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2189395'),
(78554, 'Oud-Loosdrecht', 2612, 'NH', 156, 'NL', 52.20667000, 5.08056000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2001691'),
(78555, 'Oud-Vossemeer', 2620, 'ZE', 156, 'NL', 51.57083000, 4.19861000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q984957'),
(78556, 'Ouddorp', 2614, 'ZH', 156, 'NL', 51.81167000, 3.93472000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q21060'),
(78557, 'Oude IJsselstreek', 2611, 'GE', 156, 'NL', 51.89963000, 6.38078000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q932031'),
(78558, 'Oude Pekela', 2617, 'GR', 156, 'NL', 53.10417000, 7.00972000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2096106'),
(78559, 'Oude Wetering', 2614, 'ZH', 156, 'NL', 52.21417000, 4.64444000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2265226'),
(78560, 'Oudega', 2622, 'FR', 156, 'NL', 53.12504000, 5.99888000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1974327'),
(78561, 'Oudehaske', 2622, 'FR', 156, 'NL', 52.95709000, 5.87095000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3035552'),
(78562, 'Oudemirdum', 2622, 'FR', 156, 'NL', 52.85019000, 5.53544000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2138390'),
(78563, 'Ouderkerk aan de Amstel', 2612, 'NH', 156, 'NL', 52.29504000, 4.90746000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q282729'),
(78564, 'Ouderkerk aan den IJssel', 2614, 'ZH', 156, 'NL', 51.93417000, 4.63611000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2319907'),
(78565, 'Oudeschild', 2612, 'NH', 156, 'NL', 53.03917000, 4.84722000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1012380'),
(78566, 'Oudeschoot', 2622, 'FR', 156, 'NL', 52.93343000, 5.95579000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q943332'),
(78567, 'Oudewater', 2610, 'UT', 156, 'NL', 52.02500000, 4.86806000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q33470616'),
(78568, 'Oudkarspel', 2612, 'NH', 156, 'NL', 52.71583000, 4.80556000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2427079'),
(78569, 'Overasselt', 2611, 'GE', 156, 'NL', 51.76000000, 5.78889000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1013416'),
(78570, 'Overberg', 2610, 'UT', 156, 'NL', 52.04000000, 5.49444000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3358892'),
(78571, 'Overhoven', 2615, 'LI', 156, 'NL', 51.00869000, 5.86628000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3358892'),
(78572, 'Overloon', 2623, 'NB', 156, 'NL', 51.57167000, 5.94722000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q337385'),
(78573, 'Overschie', 2614, 'ZH', 156, 'NL', 51.93863000, 4.42766000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2389241'),
(78574, 'Overveen', 2612, 'NH', 156, 'NL', 52.39167000, 4.61389000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1847815'),
(78575, 'Palenstein', 2614, 'ZH', 156, 'NL', 52.05579000, 4.50869000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1847815'),
(78576, 'Pannerden', 2611, 'GE', 156, 'NL', 51.89083000, 6.03889000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q1918938'),
(78577, 'Panningen', 2615, 'LI', 156, 'NL', 51.32667000, 5.97917000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3019356'),
(78578, 'Papendrecht', 2614, 'ZH', 156, 'NL', 51.83167000, 4.68750000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q3019356'),
(78579, 'Papenveer', 2614, 'ZH', 156, 'NL', 52.18500000, 4.72500000, '2019-10-05 23:11:53', '2019-10-05 23:11:53', 1, 'Q2335140'),
(78580, 'Passart', 2615, 'LI', 156, 'NL', 50.92389000, 5.94674000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q2335140'),
(78581, 'Paterswolde', 2613, 'DR', 156, 'NL', 53.14500000, 6.56528000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q939394'),
(78582, 'Pathmos', 2618, 'OV', 156, 'NL', 52.21397000, 6.87555000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q939394'),
(78583, 'Peelo', 2613, 'DR', 156, 'NL', 53.01719000, 6.56208000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q939394'),
(78584, 'Peij', 2615, 'LI', 156, 'NL', 51.09417000, 5.89583000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q939394'),
(78585, 'Peize', 2613, 'DR', 156, 'NL', 53.14667000, 6.49722000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q1985729'),
(78586, 'Pendrecht', 2614, 'ZH', 156, 'NL', 51.87152000, 4.46901000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q582028'),
(78587, 'Pernis', 2614, 'ZH', 156, 'NL', 51.88833000, 4.38889000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q178782'),
(78588, 'Petten', 2612, 'NH', 156, 'NL', 52.76667000, 4.66111000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q446232'),
(78589, 'Pierik', 2618, 'OV', 156, 'NL', 52.50141000, 6.11170000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q446232'),
(78590, 'Piershil', 2614, 'ZH', 156, 'NL', 51.79333000, 4.31389000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q2225490'),
(78591, 'Pijnacker', 2614, 'ZH', 156, 'NL', 52.01954000, 4.42946000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q968044'),
(78592, 'Poeldijk', 2614, 'ZH', 156, 'NL', 52.02417000, 4.21944000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q3112494'),
(78593, 'Poortvliet', 2620, 'ZE', 156, 'NL', 51.54417000, 4.14306000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q3183251'),
(78594, 'Posterholt', 2615, 'LI', 156, 'NL', 51.12333000, 6.03472000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q2064352'),
(78595, 'Pottenberg', 2615, 'LI', 156, 'NL', 50.85135000, 5.65668000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q2064352'),
(78596, 'Princenhage', 2623, 'NB', 156, 'NL', 51.57632000, 4.73906000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q3252438'),
(78597, 'Prinsenbeek', 2623, 'NB', 156, 'NL', 51.59833000, 4.71250000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q2764979'),
(78598, 'Puiflijk', 2611, 'GE', 156, 'NL', 51.87833000, 5.59028000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q942490'),
(78599, 'Purmerend', 2612, 'NH', 156, 'NL', 52.50500000, 4.95972000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q33474175'),
(78600, 'Puth', 2615, 'LI', 156, 'NL', 50.95417000, 5.87361000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q2479146'),
(78601, 'Putte', 2623, 'NB', 156, 'NL', 51.36000000, 4.39583000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q2413102'),
(78602, 'Putten', 2611, 'GE', 156, 'NL', 52.25917000, 5.60694000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q15881071'),
(78603, 'Quirijnstok', 2623, 'NB', 156, 'NL', 51.58430000, 5.09810000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q15881071'),
(78604, 'Raalte', 2618, 'OV', 156, 'NL', 52.38583000, 6.27500000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q18012068'),
(78605, 'Raam', 2623, 'NB', 156, 'NL', 51.65967000, 5.63637000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q18012068'),
(78606, 'Raamsdonk', 2623, 'NB', 156, 'NL', 51.68750000, 4.90833000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q3415830'),
(78607, 'Raamsdonksveer', 2623, 'NB', 156, 'NL', 51.69667000, 4.87361000, '2019-10-05 23:11:54', '2019-10-05 23:11:54', 1, 'Q3415830'),
(78608, 'Randenbroek', 2610, 'UT', 156, 'NL', 52.14863000, 5.40120000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3415830'),
(78609, 'Ravenstein', 2623, 'NB', 156, 'NL', 51.79667000, 5.65000000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q589056'),
(78610, 'Reek', 2623, 'NB', 156, 'NL', 51.74583000, 5.68194000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2574931'),
(78611, 'Reeuwijk', 2614, 'ZH', 156, 'NL', 52.04667000, 4.72500000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q985664'),
(78612, 'Reitdiep', 2617, 'GR', 156, 'NL', 53.24252000, 6.51957000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q985664'),
(78613, 'Renesse', 2620, 'ZE', 156, 'NL', 51.73250000, 3.77500000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1024702'),
(78614, 'Renkum', 2611, 'GE', 156, 'NL', 51.97667000, 5.73333000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q15881216'),
(78615, 'Renswoude', 2610, 'UT', 156, 'NL', 52.07333000, 5.54028000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q15881216'),
(78616, 'Reusel', 2623, 'NB', 156, 'NL', 51.36250000, 5.16528000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3428345'),
(78617, 'Reuver', 2615, 'LI', 156, 'NL', 51.28417000, 6.07778000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1019527'),
(78618, 'Rheden', 2611, 'GE', 156, 'NL', 52.00500000, 6.02917000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q13137607'),
(78619, 'Rhenen', 2610, 'UT', 156, 'NL', 51.95917000, 5.56806000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q13137607'),
(78620, 'Rhoon', 2614, 'ZH', 156, 'NL', 51.85750000, 4.42222000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q687584'),
(78621, 'Ridderkerk', 2614, 'ZH', 156, 'NL', 51.87250000, 4.60278000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q33475383'),
(78622, 'Riethoven', 2623, 'NB', 156, 'NL', 51.35417000, 5.38750000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q595499'),
(78623, 'Rijen', 2623, 'NB', 156, 'NL', 51.59083000, 4.91944000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2516342'),
(78624, 'Rijnsaterwoude', 2614, 'ZH', 156, 'NL', 52.19583000, 4.67083000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3432013'),
(78625, 'Rijnsburg', 2614, 'ZH', 156, 'NL', 52.19000000, 4.44167000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1615333'),
(78626, 'Rijpwetering', 2614, 'ZH', 156, 'NL', 52.19250000, 4.58333000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3432014'),
(78627, 'Rijsbergen', 2623, 'NB', 156, 'NL', 51.51750000, 4.69722000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3432017'),
(78628, 'Rijsoord', 2614, 'ZH', 156, 'NL', 51.85083000, 4.59583000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3432018'),
(78629, 'Rijswijk', 2623, 'NB', 156, 'NL', 51.79750000, 5.02500000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1839958'),
(78630, 'Rijswijk', 2614, 'ZH', 156, 'NL', 52.03634000, 4.32501000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q33475563'),
(78631, 'Rivierenkwartier', 2611, 'GE', 156, 'NL', 52.18914000, 5.97665000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q33475563'),
(78632, 'Rockanje', 2614, 'ZH', 156, 'NL', 51.87167000, 4.07083000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3029661'),
(78633, 'Roden', 2613, 'DR', 156, 'NL', 53.13750000, 6.42083000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2405988'),
(78634, 'Roelofarendsveen', 2614, 'ZH', 156, 'NL', 52.20333000, 4.63333000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2735708'),
(78635, 'Roermond', 2615, 'LI', 156, 'NL', 51.19417000, 5.98750000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q9783'),
(78636, 'Rolde', 2613, 'DR', 156, 'NL', 52.98417000, 6.64861000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2182709'),
(78637, 'Roosendaal', 2623, 'NB', 156, 'NL', 51.53083000, 4.46528000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q6903267'),
(78638, 'Roosteren', 2615, 'LI', 156, 'NL', 51.08333000, 5.81806000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2675589'),
(78639, 'Rossum', 2611, 'GE', 156, 'NL', 51.80083000, 5.33333000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2918470'),
(78640, 'Rossum', 2618, 'OV', 156, 'NL', 52.35167000, 6.92222000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q765030'),
(78641, 'Rothem', 2615, 'LI', 156, 'NL', 50.87667000, 5.73889000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q765030'),
(78642, 'Rotterdam', 2614, 'ZH', 156, 'NL', 51.92250000, 4.47917000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q34370'),
(78643, 'Rottevalle', 2622, 'FR', 156, 'NL', 53.14523000, 6.10411000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1841127'),
(78644, 'Rozenburg', 2614, 'ZH', 156, 'NL', 51.90417000, 4.24861000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1019529'),
(78645, 'Rozendaal', 2611, 'GE', 156, 'NL', 52.00583000, 5.96250000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q33476623'),
(78646, 'Rucphen', 2623, 'NB', 156, 'NL', 51.53167000, 4.55833000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2789907'),
(78647, 'Rugge', 2614, 'ZH', 156, 'NL', 51.89921000, 4.15231000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2789907'),
(78648, 'Ruinen', 2613, 'DR', 156, 'NL', 52.76250000, 6.35417000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1007156'),
(78649, 'Ruinerwold', 2613, 'DR', 156, 'NL', 52.72333000, 6.24861000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2215451'),
(78650, 'Ruurlo', 2611, 'GE', 156, 'NL', 52.08833000, 6.45000000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1025683'),
(78651, 'Salderes', 2623, 'NB', 156, 'NL', 51.51505000, 5.38991000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1025683'),
(78652, 'Sappemeer', 2617, 'GR', 156, 'NL', 53.16417000, 6.79028000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2056073'),
(78653, 'Sas van Gent', 2620, 'ZE', 156, 'NL', 51.22750000, 3.79861000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q81086'),
(78654, 'Sassenheim', 2614, 'ZH', 156, 'NL', 52.22500000, 4.52222000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1615386'),
(78655, 'Schagen', 2612, 'NH', 156, 'NL', 52.78750000, 4.79861000, '2019-10-05 23:11:55', '2022-05-08 22:22:07', 1, 'Q9955'),
(78656, 'Schagerbrug', 2612, 'NH', 156, 'NL', 52.80250000, 4.75833000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q844520'),
(78657, 'Schaijk', 2623, 'NB', 156, 'NL', 51.74583000, 5.63194000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3475289'),
(78658, 'Schalkhaar', 2618, 'OV', 156, 'NL', 52.26833000, 6.19444000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2454474'),
(78659, 'Scharendijke', 2620, 'ZE', 156, 'NL', 51.73583000, 3.84306000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q660156'),
(78660, 'Scharn', 2615, 'LI', 156, 'NL', 50.85000000, 5.73333000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q660156'),
(78661, 'Scharnegoutum', 2622, 'FR', 156, 'NL', 53.06051000, 5.67822000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2667816'),
(78662, 'Scheemda', 2617, 'GR', 156, 'NL', 53.17333000, 6.97222000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1010188'),
(78663, 'Schelluinen', 2614, 'ZH', 156, 'NL', 51.84333000, 4.92639000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3026320'),
(78664, 'Schermerhorn', 2612, 'NH', 156, 'NL', 52.60083000, 4.89167000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2202614'),
(78665, 'Scherpenisse', 2620, 'ZE', 156, 'NL', 51.54667000, 4.10556000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2381830'),
(78666, 'Scherpenzeel', 2611, 'GE', 156, 'NL', 52.08000000, 5.48889000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2381830'),
(78667, 'Scheveningen', 2614, 'ZH', 156, 'NL', 52.10461000, 4.27557000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q837211'),
(78668, 'Schiebroek', 2614, 'ZH', 156, 'NL', 51.95838000, 4.47124000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q837211'),
(78669, 'Schiedam', 2614, 'ZH', 156, 'NL', 51.91917000, 4.38889000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q33482242'),
(78670, 'Schiermonnikoog', 2622, 'FR', 156, 'NL', 53.48025000, 6.15209000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q33482242'),
(78671, 'Schijndel', 2623, 'NB', 156, 'NL', 51.62250000, 5.43194000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q9864'),
(78672, 'Schilberg', 2615, 'LI', 156, 'NL', 51.09917000, 5.88611000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q9864'),
(78673, 'Schildwolde', 2617, 'GR', 156, 'NL', 53.23327000, 6.81566000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q9864'),
(78674, 'Schimmert', 2615, 'LI', 156, 'NL', 50.90667000, 5.82361000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2665102'),
(78675, 'Schinnen', 2615, 'LI', 156, 'NL', 50.94333000, 5.88889000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q14318879'),
(78676, 'Schinveld', 2615, 'LI', 156, 'NL', 50.96917000, 5.97917000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1961936'),
(78677, 'Schipluiden', 2614, 'ZH', 156, 'NL', 51.97583000, 4.31389000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q1616337'),
(78678, 'Schoondijke', 2620, 'ZE', 156, 'NL', 51.35417000, 3.55556000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q75967'),
(78679, 'Schoonebeek', 2613, 'DR', 156, 'NL', 52.66250000, 6.88472000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2006601'),
(78680, 'Schoonhoven', 2614, 'ZH', 156, 'NL', 51.94750000, 4.84861000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q478036'),
(78681, 'Schoonoord', 2613, 'DR', 156, 'NL', 52.84583000, 6.75556000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2040082'),
(78682, 'Schoonrewoerd', 2610, 'UT', 156, 'NL', 51.92083000, 5.11667000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q3113902'),
(78683, 'Schouwen-Duiveland', 2620, 'ZE', 156, 'NL', 51.69294000, 3.88676000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q10079'),
(78684, 'Schutsboom', 2623, 'NB', 156, 'NL', 51.46152000, 5.62260000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q10079'),
(78685, 'Sellingen', 2617, 'GR', 156, 'NL', 52.94583000, 7.15139000, '2019-10-05 23:11:55', '2019-10-05 23:11:55', 1, 'Q2236110'),
(78686, 'Selwerd', 2617, 'GR', 156, 'NL', 53.23520000, 6.55450000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2236110'),
(78687, 'Serooskerke', 2620, 'ZE', 156, 'NL', 51.54833000, 3.59444000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1850261'),
(78688, 'Sevenum', 2615, 'LI', 156, 'NL', 51.41250000, 6.03750000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1019525'),
(78689, 'Sexbierum', 2622, 'FR', 156, 'NL', 53.21823000, 5.48402000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1000617'),
(78690, 'Sibbe', 2615, 'LI', 156, 'NL', 50.84417000, 5.82639000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q100098'),
(78691, 'Siddeburen', 2617, 'GR', 156, 'NL', 53.25000000, 6.86806000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2569089'),
(78692, 'Silvolde', 2611, 'GE', 156, 'NL', 51.90917000, 6.38750000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q252781'),
(78693, 'Simpelveld', 2615, 'LI', 156, 'NL', 50.83417000, 5.98194000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q14339968'),
(78694, 'Sint Annaparochie', 2622, 'FR', 156, 'NL', 53.27620000, 5.65727000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2840555'),
(78695, 'Sint Anthonis', 2623, 'NB', 156, 'NL', 51.62667000, 5.88194000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2840555'),
(78696, 'Sint Jacobiparochie', 2622, 'FR', 156, 'NL', 53.27291000, 5.60354000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2367586'),
(78697, 'Sint Jansklooster', 2618, 'OV', 156, 'NL', 52.67750000, 6.00556000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1979634'),
(78698, 'Sint Joost', 2615, 'LI', 156, 'NL', 51.11750000, 5.89861000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2245196'),
(78699, 'Sint Laurens', 2620, 'ZE', 156, 'NL', 51.52750000, 3.60278000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1025070'),
(78700, 'Sint Nicolaasga', 2622, 'FR', 156, 'NL', 52.92293000, 5.74242000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1861097'),
(78701, 'Sint Odiliënberg', 2615, 'LI', 156, 'NL', 51.14333000, 6.00000000, '2019-10-05 23:11:56', '2020-05-01 17:23:03', 1, 'Q985024'),
(78702, 'Sint Pancras', 2612, 'NH', 156, 'NL', 52.66000000, 4.78333000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2282220'),
(78703, 'Sint Philipsland', 2620, 'ZE', 156, 'NL', 51.61667000, 4.16528000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q690882'),
(78704, 'Sint Willebrord', 2623, 'NB', 156, 'NL', 51.54833000, 4.58889000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2181249'),
(78705, 'Sint-Michielsgestel', 2623, 'NB', 156, 'NL', 51.64167000, 5.35278000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q12013374'),
(78706, 'Sint-Oedenrode', 2623, 'NB', 156, 'NL', 51.56750000, 5.45972000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q9867'),
(78707, 'Sintjohannesga', 2622, 'FR', 156, 'NL', 52.93157000, 5.85588000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1812895'),
(78708, 'Sittard', 2615, 'LI', 156, 'NL', 50.99833000, 5.86944000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q73022'),
(78709, 'Sleen', 2613, 'DR', 156, 'NL', 52.77167000, 6.80278000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2781496'),
(78710, 'Sliedrecht', 2614, 'ZH', 156, 'NL', 51.82083000, 4.77639000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2781496'),
(78711, 'Slikkerveer', 2614, 'ZH', 156, 'NL', 51.88531000, 4.60494000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2587009'),
(78712, 'Slochteren', 2617, 'GR', 156, 'NL', 53.22078000, 6.80547000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q4325851'),
(78713, 'Slootdorp', 2612, 'NH', 156, 'NL', 52.84250000, 4.97222000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2534620'),
(78714, 'Sluis', 2620, 'ZE', 156, 'NL', 51.30833000, 3.38611000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2065987'),
(78715, 'Sluiskil', 2620, 'ZE', 156, 'NL', 51.27833000, 3.83611000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2627134'),
(78716, 'Sluisoord', 2611, 'GE', 156, 'NL', 52.22291000, 5.98145000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2627134'),
(78717, 'Sneek', 2622, 'FR', 156, 'NL', 53.03297000, 5.65890000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q23070'),
(78718, 'Soerendonk', 2623, 'NB', 156, 'NL', 51.30083000, 5.57500000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q23070'),
(78719, 'Soest', 2610, 'UT', 156, 'NL', 52.17333000, 5.29167000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q23070'),
(78720, 'Soestdijk', 2610, 'UT', 156, 'NL', 52.19083000, 5.28472000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2214216'),
(78721, 'Someren', 2623, 'NB', 156, 'NL', 51.38500000, 5.71111000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q7476457'),
(78722, 'Someren-Eind', 2623, 'NB', 156, 'NL', 51.35750000, 5.73333000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2724393'),
(78723, 'Son', 2623, 'NB', 156, 'NL', 51.51136000, 5.49282000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q628616'),
(78724, 'Spainkbos', 2611, 'GE', 156, 'NL', 52.22228000, 5.94738000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q628616'),
(78725, 'Spakenburg', 2610, 'UT', 156, 'NL', 52.25000000, 5.36667000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q385386'),
(78726, 'Spangen', 2614, 'ZH', 156, 'NL', 51.91688000, 4.43539000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2096672'),
(78727, 'Spaubeek', 2615, 'LI', 156, 'NL', 50.94000000, 5.84306000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q3030263'),
(78728, 'Spechtenkamp', 2610, 'UT', 156, 'NL', 52.13926000, 5.01758000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q3030263'),
(78729, 'Speelheide', 2623, 'NB', 156, 'NL', 51.50607000, 5.40519000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q3030263'),
(78730, 'Spekholzerheide', 2615, 'LI', 156, 'NL', 50.85559000, 6.02471000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q3030263'),
(78731, 'Spierdijk', 2612, 'NH', 156, 'NL', 52.65083000, 4.94306000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q613967'),
(78732, 'Spijk', 2617, 'GR', 156, 'NL', 53.39000000, 6.83750000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2694306'),
(78733, 'Spijkenisse', 2614, 'ZH', 156, 'NL', 51.84500000, 4.32917000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2694306'),
(78734, 'Spoorwijk', 2614, 'ZH', 156, 'NL', 52.05347000, 4.31339000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2694306'),
(78735, 'Sprengenbos', 2611, 'GE', 156, 'NL', 52.21902000, 5.94163000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2694306'),
(78736, 'Sprengenweg-Noord', 2611, 'GE', 156, 'NL', 52.21839000, 5.95068000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2694306'),
(78737, 'Sprenkelaar', 2611, 'GE', 156, 'NL', 52.22512000, 5.99922000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2694306'),
(78738, 'Sprundel', 2623, 'NB', 156, 'NL', 51.53750000, 4.59722000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2489064'),
(78739, 'Staatsliedenkwartier', 2611, 'GE', 156, 'NL', 52.19951000, 5.97691000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2489064'),
(78740, 'Stadbroek', 2615, 'LI', 156, 'NL', 51.00187000, 5.87656000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2489064'),
(78741, 'Stadsfenne', 2622, 'FR', 156, 'NL', 53.03987000, 5.67844000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2489064'),
(78742, 'Stadskanaal', 2617, 'GR', 156, 'NL', 52.98947000, 6.95040000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q485699'),
(78743, 'Stampersgat', 2623, 'NB', 156, 'NL', 51.61333000, 4.44444000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2888351'),
(78744, 'Standdaarbuiten', 2623, 'NB', 156, 'NL', 51.61333000, 4.51389000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2314541'),
(78745, 'Staphorst', 2618, 'OV', 156, 'NL', 52.64500000, 6.21111000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2314541'),
(78746, 'Statenkwartier', 2614, 'ZH', 156, 'NL', 52.09311000, 4.27577000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2314541'),
(78747, 'Stavenisse', 2620, 'ZE', 156, 'NL', 51.58750000, 4.01250000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q918449'),
(78748, 'Steenbergen', 2623, 'NB', 156, 'NL', 51.58417000, 4.31944000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q14389109'),
(78749, 'Steenderen', 2611, 'GE', 156, 'NL', 52.06417000, 6.18750000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q285907'),
(78750, 'Steensel', 2623, 'NB', 156, 'NL', 51.37667000, 5.35278000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2107841'),
(78751, 'Steenwijk', 2618, 'OV', 156, 'NL', 52.78750000, 6.12083000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q81175'),
(78752, 'Steenwijkerwold', 2618, 'OV', 156, 'NL', 52.80417000, 6.06389000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q3116953'),
(78753, 'Stegeslag', 2611, 'GE', 156, 'NL', 51.94151000, 6.06194000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q3116953'),
(78754, 'Stein', 2615, 'LI', 156, 'NL', 50.96917000, 5.76667000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q14389950'),
(78755, 'Stein', 2614, 'ZH', 156, 'NL', 52.00333000, 4.78194000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2318374'),
(78756, 'Stepekolk', 2623, 'NB', 156, 'NL', 51.45500000, 5.61251000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2318374'),
(78757, 'Sterrenberg', 2611, 'GE', 156, 'NL', 51.99350000, 5.88387000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q2318374'),
(78758, 'Stichtse Vecht', 2610, 'UT', 156, 'NL', 52.17679000, 5.01259000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q10042'),
(78759, 'Stiens', 2622, 'FR', 156, 'NL', 53.26234000, 5.75769000, '2019-10-05 23:11:56', '2019-10-05 23:11:56', 1, 'Q1915794'),
(78760, 'Stolwijk', 2614, 'ZH', 156, 'NL', 51.97250000, 4.77361000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2675598'),
(78761, 'Stompetoren', 2612, 'NH', 156, 'NL', 52.61333000, 4.82083000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2854513'),
(78762, 'Stramproy', 2615, 'LI', 156, 'NL', 51.19417000, 5.71944000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2679562'),
(78763, 'Strijen', 2614, 'ZH', 156, 'NL', 51.74521000, 4.55083000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2679562'),
(78764, 'Strijp', 2614, 'ZH', 156, 'NL', 52.03083000, 4.30139000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2679562'),
(78765, 'Surhuisterveen', 2622, 'FR', 156, 'NL', 53.18477000, 6.17031000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2662899'),
(78766, 'Sûdwest Fryslân', 2622, 'FR', 156, 'NL', 53.01056000, 5.52580000, '2019-10-05 23:11:57', '2020-05-01 17:23:03', 1, 'Q1473276'),
(78767, 'Tegelen', 2615, 'LI', 156, 'NL', 51.34417000, 6.13611000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q1019522'),
(78768, 'Ten Boer', 2617, 'GR', 156, 'NL', 53.27583000, 6.69444000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q1019522'),
(78769, 'Ter Apel', 2617, 'GR', 156, 'NL', 52.87667000, 7.05972000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q475483'),
(78770, 'Terborg', 2611, 'GE', 156, 'NL', 51.92000000, 6.35417000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q1618053'),
(78771, 'Terbregge', 2614, 'ZH', 156, 'NL', 51.95328000, 4.51537000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q1618053'),
(78772, 'Terheijden', 2623, 'NB', 156, 'NL', 51.64333000, 4.75417000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2488235'),
(78773, 'Ternaard', 2622, 'FR', 156, 'NL', 53.38203000, 5.96523000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q1000723'),
(78774, 'Terneuzen', 2620, 'ZE', 156, 'NL', 51.33583000, 3.82778000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2207185'),
(78775, 'Terschuur', 2611, 'GE', 156, 'NL', 52.16500000, 5.51667000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2403755'),
(78776, 'Terwinselen', 2615, 'LI', 156, 'NL', 50.86659000, 6.02471000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2403755'),
(78777, 'Terwolde', 2611, 'GE', 156, 'NL', 52.28333000, 6.10000000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2403755'),
(78778, 'Teteringen', 2623, 'NB', 156, 'NL', 51.60917000, 4.82083000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q3519142'),
(78779, 'The Hague', 2614, 'ZH', 156, 'NL', 52.07667000, 4.29861000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q41795540'),
(78780, 'Theereheide', 2623, 'NB', 156, 'NL', 51.63843000, 5.33729000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q41795540'),
(78781, 'Tholen', 2620, 'ZE', 156, 'NL', 51.53167000, 4.22083000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q312776'),
(78782, 'Thorn', 2615, 'LI', 156, 'NL', 51.16167000, 5.84167000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q72842'),
(78783, 'Tiel', 2611, 'GE', 156, 'NL', 51.88667000, 5.42917000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q72842'),
(78784, 'Tienray', 2615, 'LI', 156, 'NL', 51.49500000, 6.09306000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q72842'),
(78785, 'Tijnje', 2622, 'FR', 156, 'NL', 53.03058000, 5.99193000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2743749'),
(78786, 'Tilburg', 2623, 'NB', 156, 'NL', 51.55551000, 5.09130000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q9871'),
(78787, 'Tinga', 2622, 'FR', 156, 'NL', 53.02064000, 5.64575000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q9871'),
(78788, 'Tivoli', 2623, 'NB', 156, 'NL', 51.42069000, 5.50818000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q9871'),
(78789, 'Tolkamer', 2611, 'GE', 156, 'NL', 51.85500000, 6.10278000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q9871'),
(78790, 'Tongelre', 2623, 'NB', 156, 'NL', 51.44889000, 5.51978000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2322681'),
(78791, 'Tricht', 2611, 'GE', 156, 'NL', 51.89083000, 5.26806000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q3112503'),
(78792, 'Tubbergen', 2618, 'OV', 156, 'NL', 52.40750000, 6.78472000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q27825709'),
(78793, 'Tuikwerd', 2617, 'GR', 156, 'NL', 53.31667000, 6.90000000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q27825709'),
(78794, 'Tuindorp', 2614, 'ZH', 156, 'NL', 51.93032000, 4.37840000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q27825709'),
(78795, 'Tuk', 2618, 'OV', 156, 'NL', 52.79667000, 6.09444000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2428342'),
(78796, 'Tweede Exloërmond', 2613, 'DR', 156, 'NL', 52.90917000, 6.93333000, '2019-10-05 23:11:57', '2020-05-01 17:23:03', 1, 'Q585186'),
(78797, 'Twekkelerveld', 2618, 'OV', 156, 'NL', 52.23064000, 6.86004000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q585186'),
(78798, 'Twello', 2611, 'GE', 156, 'NL', 52.23667000, 6.10278000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2840627'),
(78799, 'Twijzel', 2622, 'FR', 156, 'NL', 53.23152000, 6.08952000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q3027960'),
(78800, 'Twijzelerheide', 2622, 'FR', 156, 'NL', 53.24015000, 6.04591000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q630597'),
(78801, 'Twisk', 2612, 'NH', 156, 'NL', 52.74083000, 5.05278000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2928626'),
(78802, 'Tynaarlo', 2613, 'DR', 156, 'NL', 53.07750000, 6.61667000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q462504'),
(78803, 'Tytsjerk', 2622, 'FR', 156, 'NL', 53.21343000, 5.90961000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q182853'),
(78804, 'Tzummarum', 2622, 'FR', 156, 'NL', 53.23733000, 5.54612000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2377346'),
(78805, 'Ubachsberg', 2615, 'LI', 156, 'NL', 50.85333000, 5.94861000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q3030277'),
(78806, 'Uddel', 2611, 'GE', 156, 'NL', 52.25917000, 5.78056000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2692088'),
(78807, 'Uden', 2623, 'NB', 156, 'NL', 51.66083000, 5.61944000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q528819'),
(78808, 'Udenhout', 2623, 'NB', 156, 'NL', 51.60917000, 5.14306000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q3030229'),
(78809, 'Uffelte', 2613, 'DR', 156, 'NL', 52.79000000, 6.28056000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q593294'),
(78810, 'Ugchelen', 2611, 'GE', 156, 'NL', 52.18464000, 5.93177000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q1915779'),
(78811, 'Uitgeest', 2612, 'NH', 156, 'NL', 52.52917000, 4.70972000, '2019-10-05 23:11:57', '2022-05-08 22:22:07', 1, 'Q9967'),
(78812, 'Uithoorn', 2612, 'NH', 156, 'NL', 52.23750000, 4.82639000, '2019-10-05 23:11:57', '2022-05-08 22:22:07', 1, 'Q9969'),
(78813, 'Uithuizen', 2617, 'GR', 156, 'NL', 53.40750000, 6.67083000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2223679'),
(78814, 'Uithuizermeeden', 2617, 'GR', 156, 'NL', 53.41417000, 6.72361000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q3101185'),
(78815, 'Ulestraten', 2615, 'LI', 156, 'NL', 50.90583000, 5.78194000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2164942'),
(78816, 'Ulrum', 2617, 'GR', 156, 'NL', 53.35917000, 6.33333000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2498325'),
(78817, 'Ulvenhout', 2623, 'NB', 156, 'NL', 51.54907000, 4.79931000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2232725'),
(78818, 'Ureterp', 2622, 'FR', 156, 'NL', 53.09244000, 6.16718000, '2019-10-05 23:11:57', '2019-10-05 23:11:57', 1, 'Q2331369'),
(78819, 'Urk', 2619, 'FL', 156, 'NL', 52.66250000, 5.60139000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q2331369'),
(78820, 'Urmond', 2615, 'LI', 156, 'NL', 50.99083000, 5.77222000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q3113131'),
(78821, 'Usquert', 2617, 'GR', 156, 'NL', 53.40250000, 6.61111000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q1889562'),
(78822, 'Utrecht', 2610, 'UT', 156, 'NL', 52.09083000, 5.12222000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q39297398'),
(78823, 'Vaals', 2615, 'LI', 156, 'NL', 50.77083000, 6.01806000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q1923104'),
(78824, 'Vaartbroek', 2623, 'NB', 156, 'NL', 51.47954000, 5.49806000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q1923104'),
(78825, 'Vaassen', 2611, 'GE', 156, 'NL', 52.28583000, 5.96667000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q3112535'),
(78826, 'Valburg', 2611, 'GE', 156, 'NL', 51.91167000, 5.79028000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q1889496'),
(78827, 'Valendries', 2611, 'GE', 156, 'NL', 51.80658000, 5.73445000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q1889496'),
(78828, 'Valkenburg', 2614, 'ZH', 156, 'NL', 52.18000000, 4.43194000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q1615405'),
(78829, 'Valkenburg', 2615, 'LI', 156, 'NL', 50.86523000, 5.83205000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q100076'),
(78830, 'Valkenburg aan de Geul', 2615, 'LI', 156, 'NL', 50.85711000, 5.83489000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q9771'),
(78831, 'Valkenswaard', 2623, 'NB', 156, 'NL', 51.35083000, 5.45972000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q1869375'),
(78832, 'Valthe', 2613, 'DR', 156, 'NL', 52.84583000, 6.89444000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q2784673'),
(78833, 'Valthermond', 2613, 'DR', 156, 'NL', 52.88167000, 6.96250000, '2019-10-05 23:11:58', '2019-10-05 23:11:58', 1, 'Q2786054'),
(78834, 'Varsseveld', 2611, 'GE', 156, 'NL', 51.94333000, 6.45833000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q153518'),
(78835, 'Veen', 2623, 'NB', 156, 'NL', 51.77750000, 5.10833000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q3128465'),
(78836, 'Veendam', 2617, 'GR', 156, 'NL', 53.10667000, 6.87917000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q14212973'),
(78837, 'Veenendaal', 2610, 'UT', 156, 'NL', 52.02863000, 5.55891000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q14212973'),
(78838, 'Veenoord', 2613, 'DR', 156, 'NL', 52.71087000, 6.84869000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q2409225'),
(78839, 'Veghel', 2623, 'NB', 156, 'NL', 51.61667000, 5.54861000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q12557060'),
(78840, 'Veldhoven', 2623, 'NB', 156, 'NL', 51.41833000, 5.40278000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q33511501'),
(78841, 'Veldhuizen', 2610, 'UT', 156, 'NL', 52.07537000, 5.01234000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q33511501'),
(78842, 'Velp', 2611, 'GE', 156, 'NL', 51.99500000, 5.97361000, '2019-10-05 23:11:59', '2019-10-05 23:11:59', 1, 'Q2375289'),
(78843, 'Velsen-Zuid', 2612, 'NH', 156, 'NL', 52.46000000, 4.65000000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q3330170'),
(78844, 'Veltum', 2615, 'LI', 156, 'NL', 51.51954000, 5.96032000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q23046155'),
(78845, 'Ven', 2623, 'NB', 156, 'NL', 51.64000000, 5.55000000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q23046155'),
(78846, 'Venhorst', 2623, 'NB', 156, 'NL', 51.60833000, 5.73750000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q23046155'),
(78847, 'Venlo', 2615, 'LI', 156, 'NL', 51.37000000, 6.16806000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q3322237'),
(78848, 'Venray', 2615, 'LI', 156, 'NL', 51.52500000, 5.97500000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q23015555'),
(78849, 'Vianen', 2610, 'UT', 156, 'NL', 51.99250000, 5.09167000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q23015555'),
(78850, 'Vierpolders', 2614, 'ZH', 156, 'NL', 51.87917000, 4.17917000, '2019-10-05 23:12:00', '2019-10-05 23:12:00', 1, 'Q3028978');

