INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(28834, 'Salgen', 3009, 'BY', 82, 'DE', 48.13069000, 10.47890000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q547116'),
(28835, 'Sallgast', 3013, 'BB', 82, 'DE', 51.58870000, 13.84861000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q528850'),
(28836, 'Salm<PERSON>', 3019, 'RP', 82, 'DE', 49.93333000, 6.85000000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q555526'),
(28837, '<PERSON><PERSON>', 3009, 'B<PERSON>', 82, '<PERSON>', 50.30842000, 10.21205000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q555526'),
(28838, '<PERSON>z<PERSON><PERSON>', 3008, 'N<PERSON>', 82, 'DE', 52.33333000, 7.35000000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q512612'),
(28839, 'Salzgitter', 3008, 'NI', 82, 'DE', 52.15705000, 10.41540000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q3200'),
(28840, 'Salzhausen', 3008, 'NI', 82, 'DE', 53.22339000, 10.16981000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q667370'),
(28841, 'Salzhemmendorf', 3008, 'NI', 82, 'DE', 52.06700000, 9.58720000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q555604'),
(28842, 'Salzkotten', 3017, 'NW', 82, 'DE', 51.67170000, 8.60092000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q183515'),
(28843, 'Salzmünde', 3011, 'ST', 82, 'DE', 51.52662000, 11.82650000, '2019-10-05 22:41:35', '2020-05-01 17:22:49', 1, 'Q699133'),
(28844, 'Salzwedel', 3011, 'ST', 82, 'DE', 52.85435000, 11.15250000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q486985'),
(28845, 'Salzweg', 3009, 'BY', 82, 'DE', 48.61667000, 13.48333000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q504950'),
(28846, 'Samerberg', 3009, 'BY', 82, 'DE', 47.77654000, 12.19139000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q533300'),
(28847, 'Samswegen', 3011, 'ST', 82, 'DE', 52.26059000, 11.56174000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q2217773'),
(28848, 'Samtens', 3007, 'MV', 82, 'DE', 54.35481000, 13.29311000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q561146'),
(28849, 'Sand', 3009, 'BY', 82, 'DE', 49.98585000, 10.58620000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q32276895'),
(28850, 'Sandau', 3011, 'ST', 82, 'DE', 52.78968000, 12.04582000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q529820'),
(28851, 'Sandberg', 3009, 'BY', 82, 'DE', 50.34824000, 10.00814000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q529820'),
(28852, 'Sande', 3008, 'NI', 82, 'DE', 53.50489000, 8.01418000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q529820'),
(28853, 'Sandersdorf', 3011, 'ST', 82, 'DE', 51.62841000, 12.26492000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q3187639'),
(28854, 'Sandersleben', 3011, 'ST', 82, 'DE', 51.67745000, 11.56795000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q696853'),
(28855, 'Sandesneben', 3005, 'SH', 82, 'DE', 53.68333000, 10.50000000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q579973'),
(28856, 'Sandhausen', 3006, 'BW', 82, 'DE', 49.34278000, 8.65917000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q22960'),
(28857, 'Sandstedt', 3008, 'NI', 82, 'DE', 53.35981000, 8.52137000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q624645'),
(28858, 'Sangerhausen', 3011, 'ST', 82, 'DE', 51.47221000, 11.29533000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q502434'),
(28859, 'Sankelmark', 3005, 'SH', 82, 'DE', 54.71803000, 9.42344000, '2019-10-05 22:41:35', '2019-10-05 22:41:35', 1, 'Q705271'),
(28860, 'Sankt Andreasberg', 3008, 'NI', 82, 'DE', 51.71004000, 10.51867000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q661700'),
(28861, 'Sankt Augustin', 3017, 'NW', 82, 'DE', 50.77538000, 7.19700000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q4090'),
(28862, 'Sankt Blasien', 3006, 'BW', 82, 'DE', 47.76252000, 8.12714000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q145008'),
(28863, 'Sankt Egidien', 3021, 'SN', 82, 'DE', 50.78617000, 12.62395000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q17130'),
(28864, 'Sankt Englmar', 3009, 'BY', 82, 'DE', 49.00298000, 12.82658000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q548674'),
(28865, 'Sankt Gangloff', 3015, 'TH', 82, 'DE', 50.85522000, 11.89446000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q818883'),
(28866, 'Sankt Georgen im Schwarzwald', 3006, 'BW', 82, 'DE', 48.12716000, 8.33513000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q83036'),
(28867, 'Sankt Goar', 3019, 'RP', 82, 'DE', 50.14878000, 7.70720000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q186037'),
(28868, 'Sankt Goarshausen', 3019, 'RP', 82, 'DE', 50.15839000, 7.71374000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q564757'),
(28869, 'Sankt Ingbert', 3020, 'SL', 82, 'DE', 49.27697000, 7.11672000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32278322'),
(28870, 'Sankt Johann', 3006, 'BW', 82, 'DE', 48.45397000, 9.34396000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32278322'),
(28871, 'Sankt Julian', 3019, 'RP', 82, 'DE', 49.61667000, 7.51667000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q653875'),
(28872, 'Sankt Katharinen', 3019, 'RP', 82, 'DE', 50.58333000, 7.38333000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32278387'),
(28873, 'Sankt Kilian', 3015, 'TH', 82, 'DE', 50.52749000, 10.76301000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q377676'),
(28874, 'Sankt Leon-Rot', 3006, 'BW', 82, 'DE', 49.26593000, 8.61803000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q81625'),
(28875, 'Sankt Leonhard am Wonneberg', 3009, 'BY', 82, 'DE', 47.91533000, 12.71926000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32278417'),
(28876, 'Sankt Margarethen', 3005, 'SH', 82, 'DE', 53.89199000, 9.25301000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32278417'),
(28877, 'Sankt Martin', 3019, 'RP', 82, 'DE', 49.30028000, 8.10528000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32278417'),
(28878, 'Sankt Michaelisdonn', 3005, 'SH', 82, 'DE', 53.98333000, 9.11667000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q528499'),
(28879, 'Sankt Märgen', 3006, 'BW', 82, 'DE', 48.00805000, 8.09283000, '2019-10-05 22:41:36', '2020-05-01 17:22:47', 1, 'Q550625'),
(28880, 'Sankt Peter', 3006, 'BW', 82, 'DE', 48.01475000, 8.03294000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q557671'),
(28881, 'Sankt Peter-Ording', 3005, 'SH', 82, 'DE', 54.30363000, 8.64138000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q20749'),
(28882, 'Sankt Sebastian', 3019, 'RP', 82, 'DE', 50.41077000, 7.56175000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q20749'),
(28883, 'Sankt Wendel', 3020, 'SL', 82, 'DE', 49.46633000, 7.16814000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q20749'),
(28884, 'Sankt Wolfgang', 3009, 'BY', 82, 'DE', 48.21667000, 12.13333000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q20749'),
(28885, 'Sarstedt', 3008, 'NI', 82, 'DE', 52.23495000, 9.85411000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32280279'),
(28886, 'Sasbach', 3006, 'BW', 82, 'DE', 48.63971000, 8.09375000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32280279'),
(28887, 'Sasbachwalden', 3006, 'BW', 82, 'DE', 48.61667000, 8.13333000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q533808'),
(28888, 'Sasel', 3016, 'HH', 82, 'DE', 53.65385000, 10.11184000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q533808'),
(28889, 'Sassenberg', 3017, 'NW', 82, 'DE', 51.99223000, 8.04068000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q2794'),
(28890, 'Sassenburg', 3008, 'NI', 82, 'DE', 52.51667000, 10.63333000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q475659'),
(28891, 'Satow-Oberhagen', 3007, 'MV', 82, 'DE', 53.99545000, 11.88466000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32280560'),
(28892, 'Satrup', 3005, 'SH', 82, 'DE', 54.69237000, 9.60549000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q552908'),
(28893, 'Satteldorf', 3006, 'BW', 82, 'DE', 49.16953000, 10.07957000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q81994'),
(28894, 'Sauensiek', 3008, 'NI', 82, 'DE', 53.38333000, 9.60000000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q284153'),
(28895, 'Sauerlach', 3009, 'BY', 82, 'DE', 47.97171000, 11.65383000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q507232'),
(28896, 'Sauldorf', 3006, 'BW', 82, 'DE', 47.94371000, 9.10833000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q122188'),
(28897, 'Saulgau', 3006, 'BW', 82, 'DE', 48.01676000, 9.50064000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q83190'),
(28898, 'Saulgrub', 3009, 'BY', 82, 'DE', 47.66643000, 11.02469000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32280925'),
(28899, 'Sayda', 3021, 'SN', 82, 'DE', 50.71123000, 13.42172000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32281242'),
(28900, 'Saßnitz', 3007, 'MV', 82, 'DE', 54.51570000, 13.64451000, '2019-10-05 22:41:36', '2020-05-01 17:22:49', 1, 'Q494631'),
(28901, 'Schaafheim', 3018, 'HE', 82, 'DE', 49.92417000, 9.00944000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q551226'),
(28902, 'Schaalby', 3005, 'SH', 82, 'DE', 54.55000000, 9.63333000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q568952'),
(28903, 'Schacht-Audorf', 3005, 'SH', 82, 'DE', 54.31282000, 9.71586000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q568952'),
(28904, 'Schafflund', 3005, 'SH', 82, 'DE', 54.75845000, 9.18329000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q551483'),
(28905, 'Schafstädt', 3011, 'ST', 82, 'DE', 51.38131000, 11.77302000, '2019-10-05 22:41:36', '2020-05-01 17:22:49', 1, 'Q696912'),
(28906, 'Schalkau', 3015, 'TH', 82, 'DE', 50.39536000, 11.00732000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q535603'),
(28907, 'Schalksmühle', 3017, 'NW', 82, 'DE', 51.24120000, 7.52790000, '2019-10-05 22:41:36', '2020-05-01 17:22:49', 1, 'Q58456'),
(28908, 'Schallstadt', 3006, 'BW', 82, 'DE', 47.95855000, 7.75755000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q572443'),
(28909, 'Schapen', 3008, 'NI', 82, 'DE', 52.40000000, 7.56667000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q671802'),
(28910, 'Scharbeutz', 3005, 'SH', 82, 'DE', 54.03333000, 10.75000000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q541628'),
(28911, 'Scharfenstein', 3021, 'SN', 82, 'DE', 50.70405000, 13.05654000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32282420'),
(28912, 'Scharnebeck', 3008, 'NI', 82, 'DE', 53.29237000, 10.50188000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q632660'),
(28913, 'Schashagen', 3005, 'SH', 82, 'DE', 54.13333000, 10.88333000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q632660'),
(28914, 'Schauenstein', 3009, 'BY', 82, 'DE', 50.27826000, 11.74169000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q502580'),
(28915, 'Schaufling', 3009, 'BY', 82, 'DE', 48.85000000, 13.06667000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q32282662'),
(28916, 'Schechen', 3009, 'BY', 82, 'DE', 47.92911000, 12.12393000, '2019-10-05 22:41:36', '2019-10-05 22:41:36', 1, 'Q252794'),
(28917, 'Schechingen', 3006, 'BW', 82, 'DE', 48.87341000, 9.91744000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q550773'),
(28918, 'Scheden', 3008, 'NI', 82, 'DE', 51.45000000, 9.73333000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q550773'),
(28919, 'Scheer', 3006, 'BW', 82, 'DE', 48.07292000, 9.29486000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q550773'),
(28920, 'Scheeßel', 3008, 'NI', 82, 'DE', 53.16667000, 9.48333000, '2019-10-05 22:41:37', '2020-05-01 17:22:48', 1, 'Q501679'),
(28921, 'Scheibenberg', 3021, 'SN', 82, 'DE', 50.54023000, 12.91215000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q32282890'),
(28922, 'Scheidegg', 3009, 'BY', 82, 'DE', 47.58141000, 9.84829000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q505440'),
(28923, 'Scheinfeld', 3009, 'BY', 82, 'DE', 49.66931000, 10.46554000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q504872'),
(28924, 'Schelfstadt', 3007, 'MV', 82, 'DE', 53.63382000, 11.41711000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q831989'),
(28925, 'Schelklingen', 3006, 'BW', 82, 'DE', 48.37575000, 9.73273000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q515086'),
(28926, 'Schellerten', 3008, 'NI', 82, 'DE', 52.18530000, 10.10227000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q694793'),
(28927, 'Schellhorn', 3005, 'SH', 82, 'DE', 54.22952000, 10.29402000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q694793'),
(28928, 'Schenefeld', 3005, 'SH', 82, 'DE', 54.05000000, 9.48333000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q682133'),
(28929, 'Schenkendöbern', 3013, 'BB', 82, 'DE', 51.95723000, 14.63541000, '2019-10-05 22:41:37', '2020-05-01 17:22:48', 1, 'Q636094'),
(28930, 'Schenkenzell', 3006, 'BW', 82, 'DE', 48.31202000, 8.37212000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q81350'),
(28931, 'Schenklengsfeld', 3018, 'HE', 82, 'DE', 50.81667000, 9.85000000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q625904'),
(28932, 'Schermbeck', 3017, 'NW', 82, 'DE', 51.68333000, 6.86667000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q32283596'),
(28933, 'Schermen', 3011, 'ST', 82, 'DE', 52.23206000, 11.81382000, '2019-10-05 22:41:37', '2019-10-05 22:41:37', 1, 'Q701380'),
(28934, 'Schernberg', 3015, 'TH', 82, 'DE', 51.32774000, 10.76928000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q702034'),
(28935, 'Scherstetten', 3009, 'BY', 82, 'DE', 48.18032000, 10.64005000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q702034'),
(28936, 'Scheuerfeld', 3019, 'RP', 82, 'DE', 50.78736000, 7.84128000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q653224'),
(28937, 'Scheuring', 3009, 'BY', 82, 'DE', 48.16769000, 10.89569000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q32283815'),
(28938, 'Scheyern', 3009, 'BY', 82, 'DE', 48.50000000, 11.46667000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q491974'),
(28939, 'Scheßlitz', 3009, 'BY', 82, 'DE', 49.97568000, 11.03299000, '2019-10-05 22:44:43', '2020-05-01 17:22:48', 1, 'Q238951'),
(28940, 'Schieder-Schwalenberg', 3017, 'NW', 82, 'DE', 51.87713000, 9.19538000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q50889'),
(28941, 'Schierling', 3009, 'BY', 82, 'DE', 48.83422000, 12.13946000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q490201'),
(28942, 'Schiffdorf', 3008, 'NI', 82, 'DE', 53.53333000, 8.65000000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q505594'),
(28943, 'Schifferstadt', 3019, 'RP', 82, 'DE', 49.38417000, 8.37750000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q23002'),
(28944, 'Schiffweiler', 3020, 'SL', 82, 'DE', 49.36667000, 7.13333000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q631957'),
(28945, 'Schildau', 3021, 'SN', 82, 'DE', 51.45721000, 12.93024000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q12048'),
(28946, 'Schillig', 3008, 'NI', 82, 'DE', 53.70378000, 8.02170000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q254449'),
(28947, 'Schillingen', 3019, 'RP', 82, 'DE', 49.63333000, 6.78333000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q254449'),
(28948, 'Schillingsfürst', 3009, 'BY', 82, 'DE', 49.28775000, 10.26276000, '2019-10-05 22:44:43', '2020-05-01 17:22:48', 1, 'Q502971'),
(28949, 'Schiltach', 3006, 'BW', 82, 'DE', 48.28932000, 8.34169000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q83039'),
(28950, 'Schiltberg', 3009, 'BY', 82, 'DE', 48.46293000, 11.24876000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q32070'),
(28951, 'Schipkau', 3013, 'BB', 82, 'DE', 51.51766000, 13.89738000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q574443'),
(28952, 'Schirgiswalde', 3021, 'SN', 82, 'DE', 51.07641000, 14.42834000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q572568'),
(28953, 'Schirmitz', 3009, 'BY', 82, 'DE', 49.65000000, 12.16667000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q169573'),
(28954, 'Schirnding', 3009, 'BY', 82, 'DE', 50.08205000, 12.22742000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q31655'),
(28955, 'Schkeuditz', 3021, 'SN', 82, 'DE', 51.39678000, 12.22141000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q12058'),
(28956, 'Schkopau', 3011, 'ST', 82, 'DE', 51.39186000, 11.95224000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q526388'),
(28957, 'Schkölen', 3015, 'TH', 82, 'DE', 51.04166000, 11.82141000, '2019-10-05 22:44:43', '2020-05-01 17:22:50', 1, 'Q552485'),
(28958, 'Schladen', 3008, 'NI', 82, 'DE', 52.02218000, 10.53967000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q630264'),
(28959, 'Schlagsdorf', 3007, 'MV', 82, 'DE', 53.73363000, 10.82556000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q638895'),
(28960, 'Schlaitdorf', 3006, 'BW', 82, 'DE', 48.60431000, 9.22268000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q83148'),
(28961, 'Schlaitz', 3011, 'ST', 82, 'DE', 51.65871000, 12.42839000, '2019-10-05 22:44:43', '2019-10-05 22:44:43', 1, 'Q2091660'),
(28962, 'Schlangen', 3017, 'NW', 82, 'DE', 51.80978000, 8.84605000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q2091660'),
(28963, 'Schlangenbad', 3018, 'HE', 82, 'DE', 50.09322000, 8.10312000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q624446'),
(28964, 'Schlat', 3006, 'BW', 82, 'DE', 48.65321000, 9.70625000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q80713'),
(28965, 'Schleching', 3009, 'BY', 82, 'DE', 47.72098000, 12.39481000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q258672'),
(28966, 'Schlegel', 3021, 'SN', 82, 'DE', 50.97775000, 14.87686000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q32285412'),
(28967, 'Schlehdorf', 3009, 'BY', 82, 'DE', 47.65795000, 11.31494000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q550790'),
(28968, 'Schleid', 3015, 'TH', 82, 'DE', 50.70000000, 9.96667000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q634853'),
(28969, 'Schleiden', 3017, 'NW', 82, 'DE', 50.52896000, 6.47692000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q241358'),
(28970, 'Schleife', 3021, 'SN', 82, 'DE', 51.53333000, 14.53333000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q32285567'),
(28971, 'Schleiz', 3015, 'TH', 82, 'DE', 50.57866000, 11.81024000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q506738'),
(28972, 'Schleswig', 3005, 'SH', 82, 'DE', 54.52156000, 9.55860000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q506738'),
(28973, 'Schlettau', 3021, 'SN', 82, 'DE', 50.55882000, 12.95269000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q57939'),
(28974, 'Schleusingen', 3015, 'TH', 82, 'DE', 50.51076000, 10.75658000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q504914'),
(28975, 'Schlieben', 3013, 'BB', 82, 'DE', 51.72379000, 13.38304000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q507017'),
(28976, 'Schliengen', 3006, 'BW', 82, 'DE', 47.75698000, 7.57645000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q62037'),
(28977, 'Schlier', 3006, 'BW', 82, 'DE', 47.77004000, 9.67354000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q62037'),
(28978, 'Schlierbach', 3006, 'BW', 82, 'DE', 48.67362000, 9.51811000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q62037'),
(28979, 'Schliersee', 3009, 'BY', 82, 'DE', 47.73621000, 11.85936000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q260306'),
(28980, 'Schlitz', 3018, 'HE', 82, 'DE', 50.67416000, 9.56102000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q552477'),
(28981, 'Schlotheim', 3015, 'TH', 82, 'DE', 51.24643000, 10.65842000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q378737'),
(28982, 'Schloßvippach', 3015, 'TH', 82, 'DE', 51.10499000, 11.14512000, '2019-10-05 22:44:44', '2020-05-01 17:22:50', 1, 'Q546574'),
(28983, 'Schluchsee', 3006, 'BW', 82, 'DE', 47.81667000, 8.18333000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q546574'),
(28984, 'Schlüchtern', 3018, 'HE', 82, 'DE', 50.34891000, 9.52532000, '2019-10-05 22:44:44', '2020-05-01 17:22:48', 1, 'Q569671'),
(28985, 'Schlüsselfeld', 3009, 'BY', 82, 'DE', 49.75621000, 10.61873000, '2019-10-05 22:44:44', '2020-05-01 17:22:48', 1, 'Q515958'),
(28986, 'Schmalfeld', 3005, 'SH', 82, 'DE', 53.88333000, 9.96667000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q515958'),
(28987, 'Schmalkalden', 3015, 'TH', 82, 'DE', 50.72136000, 10.44386000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q489147'),
(28988, 'Schmallenberg', 3017, 'NW', 82, 'DE', 51.15473000, 8.28505000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q5628'),
(28989, 'Schmargendorf', 3010, 'BE', 82, 'DE', 52.47517000, 13.29071000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q641689'),
(28990, 'Schmelz', 3020, 'SL', 82, 'DE', 49.43333000, 6.85000000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q641689'),
(28991, 'Schmidgaden', 3009, 'BY', 82, 'DE', 49.42340000, 12.09247000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q546435'),
(28992, 'Schmidmühlen', 3009, 'BY', 82, 'DE', 49.26898000, 11.92429000, '2019-10-05 22:44:44', '2020-05-01 17:22:48', 1, 'Q516827'),
(28993, 'Schmiechen', 3009, 'BY', 82, 'DE', 48.21667000, 10.96667000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q203121'),
(28994, 'Schmiedeberg', 3021, 'SN', 82, 'DE', 50.83644000, 13.67622000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q8239'),
(28995, 'Schmiedefeld', 3015, 'TH', 82, 'DE', 50.53333000, 11.21667000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q201280'),
(28996, 'Schmiedefeld am Rennsteig', 3015, 'TH', 82, 'DE', 50.60863000, 10.81284000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q631546'),
(28997, 'Schmitten', 3018, 'HE', 82, 'DE', 50.26667000, 8.45000000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q622207'),
(28998, 'Schmöckwitz', 3010, 'BE', 82, 'DE', 52.37513000, 13.64948000, '2019-10-05 22:44:44', '2020-05-01 17:22:48', 1, 'Q563573'),
(28999, 'Schmölln', 3015, 'TH', 82, 'DE', 50.89678000, 12.35339000, '2019-10-05 22:44:44', '2020-05-01 17:22:50', 1, 'Q572864'),
(29000, 'Schnaitsee', 3009, 'BY', 82, 'DE', 48.06667000, 12.36667000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q280425'),
(29001, 'Schnaittach', 3009, 'BY', 82, 'DE', 49.55958000, 11.34328000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q520932'),
(29002, 'Schnaittenbach', 3009, 'BY', 82, 'DE', 49.54692000, 12.00184000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q506887'),
(29003, 'Schneckenlohe', 3009, 'BY', 82, 'DE', 50.21184000, 11.19395000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q504095'),
(29004, 'Schneeberg', 3021, 'SN', 82, 'DE', 50.59465000, 12.64139000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, '*********'),
(29005, 'Schnega', 3008, 'NI', 82, 'DE', 52.89130000, 10.89226000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q759412'),
(29006, 'Schneidlingen', 3011, 'ST', 82, 'DE', 51.89454000, 11.44487000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, '********'),
(29007, 'Schneizlreuth', 3009, 'BY', 82, 'DE', 47.68333000, 12.80000000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q253867'),
(29008, 'Schnelldorf', 3009, 'BY', 82, 'DE', 49.20000000, 10.18333000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q504124'),
(29009, 'Schneverdingen', 3008, 'NI', 82, 'DE', 53.11685000, 9.79524000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, '*********'),
(29010, 'Schnürpflingen', 3006, 'BW', 82, 'DE', 48.27280000, 9.99292000, '2019-10-05 22:44:44', '2020-05-01 17:22:47', 1, 'Q540822'),
(29011, 'Schobüll', 3005, 'SH', 82, 'DE', 54.50920000, 9.00621000, '2019-10-05 22:44:44', '2020-05-01 17:22:50', 1, 'Q317644'),
(29012, 'Schochwitz', 3011, 'ST', 82, 'DE', 51.53174000, 11.75485000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q819063'),
(29013, 'Schonach im Schwarzwald', 3006, 'BW', 82, 'DE', 48.14229000, 8.20289000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q81817'),
(29014, 'Schondorf am Ammersee', 3009, 'BY', 82, 'DE', 48.05296000, 11.09138000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q505583'),
(29015, 'Schondra', 3009, 'BY', 82, 'DE', 50.26814000, 9.86277000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q504328'),
(29016, 'Schongau', 3009, 'BY', 82, 'DE', 47.81240000, 10.89664000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q32289294'),
(29017, 'Schonstett', 3009, 'BY', 82, 'DE', 47.98333000, 12.25000000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q551645'),
(29018, 'Schonungen', 3009, 'BY', 82, 'DE', 50.05008000, 10.30809000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q553789'),
(29019, 'Schopfheim', 3006, 'BW', 82, 'DE', 47.65105000, 7.82089000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q61734'),
(29020, 'Schopfloch', 3009, 'BY', 82, 'DE', 49.11917000, 10.30774000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q506082'),
(29021, 'Schopfloch', 3006, 'BW', 82, 'DE', 48.45477000, 8.55131000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q506082'),
(29022, 'Schopp', 3019, 'RP', 82, 'DE', 49.35000000, 7.68333000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q23027'),
(29023, 'Schorndorf', 3009, 'BY', 82, 'DE', 49.16041000, 12.59316000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q23027'),
(29024, 'Schorndorf', 3006, 'BW', 82, 'DE', 48.80537000, 9.52721000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q14882'),
(29025, 'Schornsheim', 3019, 'RP', 82, 'DE', 49.84583000, 8.17500000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q641590'),
(29026, 'Schortens', 3008, 'NI', 82, 'DE', 53.53333000, 7.95000000, '2019-10-05 22:44:44', '2019-10-05 22:44:44', 1, 'Q641590'),
(29027, 'Schotten', 3018, 'HE', 82, 'DE', 50.50346000, 9.12516000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q32289666'),
(29028, 'Schramberg', 3006, 'BW', 82, 'DE', 48.22399000, 8.38583000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q82886'),
(29029, 'Schraplau', 3011, 'ST', 82, 'DE', 51.43750000, 11.66823000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q550853'),
(29030, 'Schrecksbach', 3018, 'HE', 82, 'DE', 50.83333000, 9.28333000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q622471'),
(29031, 'Schriesheim', 3006, 'BW', 82, 'DE', 49.47372000, 8.66360000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q83292'),
(29032, 'Schrobenhausen', 3009, 'BY', 82, 'DE', 48.56067000, 11.26071000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q504746'),
(29033, 'Schrozberg', 3006, 'BW', 82, 'DE', 49.34528000, 9.97944000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q83135'),
(29034, 'Schuby', 3005, 'SH', 82, 'DE', 54.51667000, 9.48333000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q561176'),
(29035, 'Schulzendorf', 3013, 'BB', 82, 'DE', 52.35818000, 13.59842000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q581119'),
(29036, 'Schuttertal', 3006, 'BW', 82, 'DE', 48.26667000, 7.95000000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q539876'),
(29037, 'Schutterwald', 3006, 'BW', 82, 'DE', 48.45000000, 7.88333000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q533982'),
(29038, 'Schwaan', 3007, 'MV', 82, 'DE', 53.94047000, 12.10715000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q536809'),
(29039, 'Schwabach', 3009, 'BY', 82, 'DE', 49.33047000, 11.02346000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q14889'),
(29040, 'Schwabenheim', 3019, 'RP', 82, 'DE', 49.92879000, 8.09525000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q32290518'),
(29041, 'Schwabhausen', 3009, 'BY', 82, 'DE', 48.40157000, 11.35729000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q32290518'),
(29042, 'Schwabmünchen', 3009, 'BY', 82, 'DE', 48.17928000, 10.75675000, '2019-10-05 22:44:45', '2020-05-01 17:22:48', 1, 'Q509676'),
(29043, 'Schwabsoien', 3009, 'BY', 82, 'DE', 47.83333000, 10.83333000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q32290598'),
(29044, 'Schwabstedt', 3005, 'SH', 82, 'DE', 54.39705000, 9.18646000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q28050'),
(29045, 'Schwaförden', 3008, 'NI', 82, 'DE', 52.73775000, 8.83026000, '2019-10-05 22:44:45', '2020-05-01 17:22:48', 1, 'Q502803'),
(29046, 'Schwaig', 3009, 'BY', 82, 'DE', 49.46955000, 11.20064000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q508122'),
(29047, 'Schwaigern', 3006, 'BW', 82, 'DE', 49.14494000, 9.05525000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q507592'),
(29048, 'Schwaikheim', 3006, 'BW', 82, 'DE', 48.87733000, 9.34958000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q81712'),
(29049, 'Schwalbach', 3020, 'SL', 82, 'DE', 49.30000000, 6.81667000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q81712'),
(29050, 'Schwalbach', 3018, 'HE', 82, 'DE', 50.49672000, 8.46943000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q81712'),
(29051, 'Schwalbach am Taunus', 3018, 'HE', 82, 'DE', 50.15000000, 8.53333000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q81712'),
(29052, 'Schwallungen', 3015, 'TH', 82, 'DE', 50.69260000, 10.35706000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q556598'),
(29053, 'Schwalmstadt', 3018, 'HE', 82, 'DE', 50.93333000, 9.21667000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q516599'),
(29054, 'Schwalmtal', 3017, 'NW', 82, 'DE', 51.21667000, 6.26667000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q516599'),
(29055, 'Schwalmtal', 3018, 'HE', 82, 'DE', 50.68333000, 9.21667000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q516599'),
(29056, 'Schwanau', 3006, 'BW', 82, 'DE', 48.36669000, 7.76244000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q550771'),
(29057, 'Schwandorf in Bayern', 3009, 'BY', 82, 'DE', 49.32534000, 12.10980000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q504768'),
(29058, 'Schwanebeck', 3011, 'ST', 82, 'DE', 51.96790000, 11.12393000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q50862'),
(29059, 'Schwanewede', 3008, 'NI', 82, 'DE', 53.23333000, 8.60000000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q686830'),
(29060, 'Schwanfeld', 3009, 'BY', 82, 'DE', 49.92166000, 10.13866000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q558936'),
(29061, 'Schwangau', 3009, 'BY', 82, 'DE', 47.57722000, 10.73416000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q558936'),
(29062, 'Schwarme', 3008, 'NI', 82, 'DE', 52.90000000, 9.01667000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q502853'),
(29063, 'Schwarmstedt', 3008, 'NI', 82, 'DE', 52.67794000, 9.61767000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q664377'),
(29064, 'Schwarza', 3015, 'TH', 82, 'DE', 50.85386000, 11.32433000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q664377'),
(29065, 'Schwarzach', 3006, 'BW', 82, 'DE', 48.74738000, 8.03738000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q664377'),
(29066, 'Schwarzach', 3009, 'BY', 82, 'DE', 48.91567000, 12.81143000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q581023'),
(29067, 'Schwarzenbach', 3009, 'BY', 82, 'DE', 49.83876000, 12.38005000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q581023'),
(29068, 'Schwarzenbach an der Saale', 3009, 'BY', 82, 'DE', 50.22279000, 11.93504000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q581023'),
(29069, 'Schwarzenbek', 3005, 'SH', 82, 'DE', 53.50303000, 10.48055000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q569643'),
(29070, 'Schwarzenberg', 3021, 'SN', 82, 'DE', 50.53791000, 12.78522000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q57953'),
(29071, 'Schwarzenborn', 3018, 'HE', 82, 'DE', 50.90977000, 9.44658000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q32291781'),
(29072, 'Schwarzenbruck', 3009, 'BY', 82, 'DE', 49.35778000, 11.24333000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q508456'),
(29073, 'Schwarzenfeld', 3009, 'BY', 82, 'DE', 49.38774000, 12.13484000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q566553'),
(29074, 'Schwarzheide', 3013, 'BB', 82, 'DE', 51.47671000, 13.85559000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q584474'),
(29075, 'Schwarzhofen', 3009, 'BY', 82, 'DE', 49.37749000, 12.34490000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q559101'),
(29076, 'Schwebheim', 3009, 'BY', 82, 'DE', 49.99036000, 10.24776000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q553794'),
(29077, 'Schwedelbach', 3019, 'RP', 82, 'DE', 49.49557000, 7.59366000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q553794'),
(29078, 'Schwedt (Oder)', 3013, 'BB', 82, 'DE', 53.05963000, 14.28154000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q16008'),
(29079, 'Schwegenheim', 3019, 'RP', 82, 'DE', 49.27000000, 8.32861000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q16008'),
(29080, 'Schweich', 3019, 'RP', 82, 'DE', 49.82215000, 6.75256000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q212570'),
(29081, 'Schweigen-Rechtenbach', 3019, 'RP', 82, 'DE', 49.05314000, 7.95638000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q212570'),
(29082, 'Schweina', 3015, 'TH', 82, 'DE', 50.82502000, 10.33788000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q559694'),
(29083, 'Schweinfurt', 3009, 'BY', 82, 'DE', 50.04937000, 10.22175000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q4126'),
(29084, 'Schweitenkirchen', 3009, 'BY', 82, 'DE', 48.50333000, 11.60451000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q489303'),
(29085, 'Schwelm', 3017, 'NW', 82, 'DE', 51.28635000, 7.29388000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q11293'),
(29086, 'Schwendi', 3006, 'BW', 82, 'DE', 48.17424000, 9.97541000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q11293'),
(29087, 'Schwenningen', 3009, 'BY', 82, 'DE', 48.65000000, 10.65000000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q11293'),
(29088, 'Schwenningen', 3006, 'BW', 82, 'DE', 48.10000000, 9.00000000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q11293'),
(29089, 'Schwepnitz', 3021, 'SN', 82, 'DE', 51.32809000, 13.95772000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q93294'),
(29090, 'Schwerin', 3007, 'MV', 82, 'DE', 53.62937000, 11.41316000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q93294'),
(29091, 'Schwerte', 3017, 'NW', 82, 'DE', 51.44387000, 7.56750000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q32293226'),
(29092, 'Schwetzingen', 3006, 'BW', 82, 'DE', 49.38217000, 8.58230000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q3232'),
(29093, 'Schwieberdingen', 3006, 'BW', 82, 'DE', 48.87644000, 9.07439000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q61930'),
(29094, 'Schwindegg', 3009, 'BY', 82, 'DE', 48.27155000, 12.25978000, '2019-10-05 22:44:45', '2019-10-05 22:44:45', 1, 'Q508758'),
(29095, 'Schwäbisch Gmünd', 3006, 'BW', 82, 'DE', 48.79947000, 9.79809000, '2019-10-05 22:44:45', '2020-05-01 17:22:47', 1, 'Q4037'),
(29096, 'Schwäbisch Hall', 3006, 'BW', 82, 'DE', 49.11127000, 9.73908000, '2019-10-05 22:44:46', '2020-05-01 17:22:47', 1, 'Q14910'),
(29097, 'Schwörstadt', 3006, 'BW', 82, 'DE', 47.59314000, 7.87840000, '2019-10-05 22:44:46', '2020-05-01 17:22:47', 1, 'Q62001'),
(29098, 'Schäftlarn', 3009, 'BY', 82, 'DE', 47.99027000, 11.45591000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q62001'),
(29099, 'Schöffengrund', 3018, 'HE', 82, 'DE', 50.49345000, 8.47183000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q633679'),
(29100, 'Schöfweg', 3009, 'BY', 82, 'DE', 48.83882000, 13.22861000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q503403'),
(29101, 'Schöllkrippen', 3009, 'BY', 82, 'DE', 50.08545000, 9.24697000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q520820'),
(29102, 'Schöllnach', 3009, 'BY', 82, 'DE', 48.75412000, 13.17781000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q33482607'),
(29103, 'Schömberg', 3006, 'BW', 82, 'DE', 48.78713000, 8.64495000, '2019-10-05 22:44:46', '2020-05-01 17:22:47', 1, 'Q33482607'),
(29104, 'Schönaich', 3006, 'BW', 82, 'DE', 48.65871000, 9.06012000, '2019-10-05 22:44:46', '2020-05-01 17:22:47', 1, 'Q557816'),
(29105, 'Schönau', 3009, 'BY', 82, 'DE', 48.48333000, 12.85000000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q266721'),
(29106, 'Schönau', 3006, 'BW', 82, 'DE', 49.43665000, 8.80880000, '2019-10-05 22:44:46', '2020-05-01 17:22:47', 1, 'Q82366'),
(29107, 'Schönau am Königssee', 3009, 'BY', 82, 'DE', 47.60055000, 12.98704000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q503682'),
(29108, 'Schönau im Schwarzwald', 3006, 'BW', 82, 'DE', 47.78623000, 7.89445000, '2019-10-05 22:44:46', '2020-05-01 17:22:47', 1, 'Q61731'),
(29109, 'Schönau-Berzdorf', 3021, 'SN', 82, 'DE', 51.06402000, 14.88402000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q503163'),
(29110, 'Schönbach', 3021, 'SN', 82, 'DE', 51.06667000, 14.56667000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q503163'),
(29111, 'Schönberg', 3005, 'SH', 82, 'DE', 53.68319000, 10.42671000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q503163'),
(29112, 'Schönberg', 3021, 'SN', 82, 'DE', 50.86752000, 12.49126000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q17129'),
(29113, 'Schönborn', 3013, 'BB', 82, 'DE', 51.60016000, 13.48967000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q17129'),
(29114, 'Schönburg', 3011, 'ST', 82, 'DE', 51.16667000, 11.86667000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q564347'),
(29115, 'Schönebeck', 3011, 'ST', 82, 'DE', 52.01682000, 11.73070000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q16020'),
(29116, 'Schöneberg', 3010, 'BE', 82, 'DE', 52.46667000, 13.35000000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q16020'),
(29117, 'Schöneck', 3021, 'SN', 82, 'DE', 50.39052000, 12.32731000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q46903'),
(29118, 'Schönecken', 3019, 'RP', 82, 'DE', 50.15909000, 6.46820000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q584179'),
(29119, 'Schönefeld', 3013, 'BB', 82, 'DE', 52.38897000, 13.50374000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q521475'),
(29120, 'Schöneiche', 3013, 'BB', 82, 'DE', 52.47286000, 13.69226000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q33482829'),
(29121, 'Schönenberg-Kübelberg', 3019, 'RP', 82, 'DE', 49.40740000, 7.37233000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q678360'),
(29122, 'Schönewalde', 3013, 'BB', 82, 'DE', 51.67901000, 13.60247000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q678360'),
(29123, 'Schönfeld', 3021, 'SN', 82, 'DE', 51.30000000, 13.70000000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q678360'),
(29124, 'Schöngeising', 3009, 'BY', 82, 'DE', 48.14135000, 11.20399000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q49289355'),
(29125, 'Schönhausen', 3011, 'ST', 82, 'DE', 52.58076000, 12.03923000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q59974'),
(29126, 'Schönheide', 3021, 'SN', 82, 'DE', 50.50458000, 12.52158000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q57828'),
(29127, 'Schöningen', 3008, 'NI', 82, 'DE', 52.13802000, 10.96745000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q628304'),
(29128, 'Schönkirchen', 3005, 'SH', 82, 'DE', 54.33333000, 10.23333000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q505736'),
(29129, 'Schönsee', 3009, 'BY', 82, 'DE', 49.51030000, 12.54763000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q529137'),
(29130, 'Schönstedt', 3015, 'TH', 82, 'DE', 51.12000000, 10.57743000, '2019-10-05 22:44:46', '2020-05-01 17:22:50', 1, 'Q260769'),
(29131, 'Schönthal', 3009, 'BY', 82, 'DE', 49.35000000, 12.60000000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q546149'),
(29132, 'Schönwald', 3009, 'BY', 82, 'DE', 50.19970000, 12.08503000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q546149'),
(29133, 'Schönwald', 3006, 'BW', 82, 'DE', 48.10549000, 8.20387000, '2019-10-05 22:44:46', '2020-05-01 17:22:47', 1, 'Q82920'),
(29134, 'Schönwalde am Bungsberg', 3005, 'SH', 82, 'DE', 54.18333000, 10.75000000, '2019-10-05 22:44:46', '2020-05-01 17:22:50', 1, 'Q82920'),
(29135, 'Schöppenstedt', 3008, 'NI', 82, 'DE', 52.14308000, 10.77450000, '2019-10-05 22:44:46', '2020-05-01 17:22:48', 1, 'Q262655'),
(29136, 'Schöppingen', 3017, 'NW', 82, 'DE', 52.10000000, 7.23333000, '2019-10-05 22:44:46', '2020-05-01 17:22:49', 1, 'Q49289366'),
(29137, 'Schülp', 3005, 'SH', 82, 'DE', 54.25896000, 9.63034000, '2019-10-05 22:44:46', '2020-05-01 17:22:50', 1, 'Q49289366'),
(29138, 'Schüttorf', 3008, 'NI', 82, 'DE', 52.32281000, 7.22176000, '2019-10-05 22:44:47', '2020-05-01 17:22:48', 1, 'Q515046'),
(29139, 'Sebnitz', 3021, 'SN', 82, 'DE', 50.97540000, 14.27579000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q6483'),
(29140, 'Seck', 3019, 'RP', 82, 'DE', 50.57722000, 8.04972000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q699034'),
(29141, 'Seckach', 3006, 'BW', 82, 'DE', 49.44222000, 9.33417000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q553224'),
(29142, 'Seebach', 3015, 'TH', 82, 'DE', 51.16506000, 10.51428000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32296699'),
(29143, 'Seebach', 3006, 'BW', 82, 'DE', 48.57621000, 8.17048000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32296699'),
(29144, 'Seebad Bansin', 3007, 'MV', 82, 'DE', 53.97102000, 14.14147000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32296737'),
(29145, 'Seebad Heringsdorf', 3007, 'MV', 82, 'DE', 53.95379000, 14.16852000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32296746'),
(29146, 'Seebergen', 3015, 'TH', 82, 'DE', 50.92077000, 10.79920000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q701125'),
(29147, 'Seeburg', 3008, 'NI', 82, 'DE', 51.56667000, 10.15000000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q701125'),
(29148, 'Seedorf', 3006, 'BW', 82, 'DE', 48.24908000, 8.48993000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q701125'),
(29149, 'Seedorf', 3005, 'SH', 82, 'DE', 54.05000000, 10.41667000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q701125'),
(29150, 'Seefeld', 3009, 'BY', 82, 'DE', 48.03505000, 11.21395000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q701125'),
(29151, 'Seeg', 3009, 'BY', 82, 'DE', 47.65000000, 10.60000000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q511857'),
(29152, 'Seehausen', 3011, 'ST', 82, 'DE', 52.88872000, 11.75236000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32297288'),
(29153, 'Seehausen am Staffelsee', 3009, 'BY', 82, 'DE', 47.68928000, 11.18498000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q280952'),
(29154, 'Seeheilbad Graal-Müritz', 3007, 'MV', 82, 'DE', 54.25124000, 12.25139000, '2019-10-05 22:44:47', '2020-05-01 17:22:49', 1, 'Q280952'),
(29155, 'Seeheim-Jugenheim', 3018, 'HE', 82, 'DE', 49.76500000, 8.65194000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q574429'),
(29156, 'Seehof', 3007, 'MV', 82, 'DE', 53.69249000, 11.43256000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q574429'),
(29157, 'Seelbach', 3006, 'BW', 82, 'DE', 48.31055000, 7.94069000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q574429'),
(29158, 'Seelingstädt', 3015, 'TH', 82, 'DE', 50.77266000, 12.24361000, '2019-10-05 22:44:47', '2020-05-01 17:22:50', 1, 'Q549954'),
(29159, 'Seelitz', 3021, 'SN', 82, 'DE', 51.03333000, 12.81667000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q71050'),
(29160, 'Seelow', 3013, 'BB', 82, 'DE', 52.53392000, 14.38128000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q587069'),
(29161, 'Seelze', 3008, 'NI', 82, 'DE', 52.39635000, 9.59727000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32297661'),
(29162, 'Seeon-Seebruck', 3009, 'BY', 82, 'DE', 47.96667000, 12.46667000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q250213'),
(29163, 'Seesen', 3008, 'NI', 82, 'DE', 51.89095000, 10.17847000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q540934'),
(29164, 'Seeshaupt', 3009, 'BY', 82, 'DE', 47.82468000, 11.30219000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q518061'),
(29165, 'Seevetal', 3008, 'NI', 82, 'DE', 53.40000000, 9.96667000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q7084'),
(29166, 'Seggebruch', 3008, 'NI', 82, 'DE', 52.30050000, 9.09462000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q590635'),
(29167, 'Sehlde', 3008, 'NI', 82, 'DE', 52.03887000, 10.26569000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q583086'),
(29168, 'Sehlem', 3008, 'NI', 82, 'DE', 52.01236000, 9.97593000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q583086'),
(29169, 'Sehlen', 3007, 'MV', 82, 'DE', 54.37971000, 13.38907000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q583086'),
(29170, 'Sehnde', 3008, 'NI', 82, 'DE', 52.31394000, 9.96820000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q625095'),
(29171, 'Seibersbach', 3019, 'RP', 82, 'DE', 49.96667000, 7.71667000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q630841'),
(29172, 'Seiffen', 3021, 'SN', 82, 'DE', 50.65000000, 13.45000000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q57837'),
(29173, 'Seifhennersdorf', 3021, 'SN', 82, 'DE', 50.93492000, 14.60194000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q165195'),
(29174, 'Seinsheim', 3009, 'BY', 82, 'DE', 49.64073000, 10.22038000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q504968'),
(29175, 'Seitingen-Oberflacht', 3006, 'BW', 82, 'DE', 48.01667000, 8.71667000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q628990'),
(29176, 'Selb', 3009, 'BY', 82, 'DE', 50.17058000, 12.13054000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q49812'),
(29177, 'Selbitz', 3009, 'BY', 82, 'DE', 50.31702000, 11.75019000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32298546'),
(29178, 'Selent', 3005, 'SH', 82, 'DE', 54.28893000, 10.42702000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q631654'),
(29179, 'Seligenstadt', 3018, 'HE', 82, 'DE', 50.04320000, 8.97394000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q516529'),
(29180, 'Sellin', 3007, 'MV', 82, 'DE', 53.88495000, 11.60790000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32298727'),
(29181, 'Selm', 3017, 'NW', 82, 'DE', 51.69689000, 7.46809000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q10935'),
(29182, 'Selmsdorf', 3007, 'MV', 82, 'DE', 53.88224000, 10.85997000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q638518'),
(29183, 'Selsingen', 3008, 'NI', 82, 'DE', 53.37329000, 9.21289000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32298823'),
(29184, 'Selters', 3019, 'RP', 82, 'DE', 50.53253000, 7.75577000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q32298846'),
(29185, 'Selters', 3018, 'HE', 82, 'DE', 50.51681000, 8.28953000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q2268705'),
(29186, 'Selzen', 3019, 'RP', 82, 'DE', 49.86056000, 8.25528000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q678449'),
(29187, 'Sembach', 3019, 'RP', 82, 'DE', 49.51177000, 7.86661000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q680987'),
(29188, 'Senden', 3009, 'BY', 82, 'DE', 48.32441000, 10.04442000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q680987'),
(29189, 'Senden', 3017, 'NW', 82, 'DE', 51.85645000, 7.48327000, '2019-10-05 22:44:47', '2019-10-05 22:44:47', 1, 'Q680987'),
(29190, 'Sendenhorst', 3017, 'NW', 82, 'DE', 51.84303000, 7.82996000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q32299135'),
(29191, 'Senftenberg', 3013, 'BB', 82, 'DE', 51.52517000, 14.00164000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q491754'),
(29192, 'Sengenthal', 3009, 'BY', 82, 'DE', 49.23333000, 11.46667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q505589'),
(29193, 'Sennewitz', 3011, 'ST', 82, 'DE', 51.54326000, 11.95218000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q1243845'),
(29194, 'Sennfeld', 3009, 'BY', 82, 'DE', 50.03895000, 10.25986000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q32299313'),
(29195, 'Serrig', 3019, 'RP', 82, 'DE', 49.57519000, 6.57454000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q656819'),
(29196, 'Sersheim', 3006, 'BW', 82, 'DE', 48.96667000, 9.01667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q61709'),
(29197, 'Seth', 3005, 'SH', 82, 'DE', 53.84718000, 10.17421000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q61709'),
(29198, 'Seubersdorf', 3009, 'BY', 82, 'DE', 49.16212000, 11.62714000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q504359'),
(29199, 'Seukendorf', 3009, 'BY', 82, 'DE', 49.48844000, 10.87999000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q383134'),
(29200, 'Seulingen', 3008, 'NI', 82, 'DE', 51.54129000, 10.16263000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q634135'),
(29201, 'Sexau', 3006, 'BW', 82, 'DE', 48.10209000, 7.90757000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q550991'),
(29202, 'Seybothenreuth', 3009, 'BY', 82, 'DE', 49.89372000, 11.70531000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q504691'),
(29203, 'Seyda', 3011, 'ST', 82, 'DE', 51.88163000, 12.90812000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q504691'),
(29204, 'Seßlach', 3009, 'BY', 82, 'DE', 50.18969000, 10.84197000, '2019-10-05 22:44:48', '2020-05-01 17:22:48', 1, 'Q572667'),
(29205, 'Sibbesse', 3008, 'NI', 82, 'DE', 52.05000000, 9.90000000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q649621'),
(29206, 'Sickenhausen', 3006, 'BW', 82, 'DE', 48.53252000, 9.18114000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q1843556'),
(29207, 'Sickte', 3008, 'NI', 82, 'DE', 52.21847000, 10.64240000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q605484'),
(29208, 'Siebeldingen', 3019, 'RP', 82, 'DE', 49.20889000, 8.05139000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q23033'),
(29209, 'Siedenburg', 3008, 'NI', 82, 'DE', 52.69293000, 8.93961000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q504180'),
(29210, 'Siefersheim', 3019, 'RP', 82, 'DE', 49.80000000, 7.95000000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q504180'),
(29211, 'Siegburg', 3017, 'NW', 82, 'DE', 50.80019000, 7.20769000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q14870'),
(29212, 'Siegelsbach', 3006, 'BW', 82, 'DE', 49.26972000, 9.08972000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q507113'),
(29213, 'Siegen', 3017, 'NW', 82, 'DE', 50.87481000, 8.02431000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q3167'),
(29214, 'Siegenburg', 3009, 'BY', 82, 'DE', 48.75421000, 11.84831000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q503945'),
(29215, 'Siegsdorf', 3009, 'BY', 82, 'DE', 47.82278000, 12.64277000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q266915'),
(29216, 'Siek', 3005, 'SH', 82, 'DE', 53.63333000, 10.30000000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q669139'),
(29217, 'Sielenbach', 3009, 'BY', 82, 'DE', 48.40000000, 11.16667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q669139'),
(29218, 'Siemensstadt', 3010, 'BE', 82, 'DE', 52.54053000, 13.26294000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q3510657'),
(29219, 'Sierksdorf', 3005, 'SH', 82, 'DE', 54.06667000, 10.76667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q629468'),
(29220, 'Siersdorf', 3017, 'NW', 82, 'DE', 50.89827000, 6.22684000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q1395453'),
(29221, 'Siershahn', 3019, 'RP', 82, 'DE', 50.48639000, 7.77972000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q601446'),
(29222, 'Siersleben', 3011, 'ST', 82, 'DE', 51.60411000, 11.54334000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q248360'),
(29223, 'Sievershütten', 3005, 'SH', 82, 'DE', 53.84238000, 10.11216000, '2019-10-05 22:44:48', '2020-05-01 17:22:50', 1, 'Q554102'),
(29224, 'Sieverstedt', 3005, 'SH', 82, 'DE', 54.64145000, 9.46949000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q551167'),
(29225, 'Siggelkow', 3007, 'MV', 82, 'DE', 53.38742000, 11.93802000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q574786'),
(29226, 'Sigmaringen', 3006, 'BW', 82, 'DE', 48.08829000, 9.23033000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q82858'),
(29227, 'Sigmaringendorf', 3006, 'BW', 82, 'DE', 48.06586000, 9.26208000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q550644'),
(29228, 'Sigmarszell', 3009, 'BY', 82, 'DE', 47.58333000, 9.76667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q505842'),
(29229, 'Silberstedt', 3005, 'SH', 82, 'DE', 54.51667000, 9.38333000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q556887'),
(29230, 'Sillenstede', 3008, 'NI', 82, 'DE', 53.57437000, 7.98500000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q1496648'),
(29231, 'Simbach', 3009, 'BY', 82, 'DE', 48.56606000, 12.73888000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q33487044'),
(29232, 'Simbach am Inn', 3009, 'BY', 82, 'DE', 48.26548000, 13.02309000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q253236'),
(29233, 'Simmelsdorf', 3009, 'BY', 82, 'DE', 49.59775000, 11.33901000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q552412'),
(29234, 'Simmerath', 3017, 'NW', 82, 'DE', 50.60000000, 6.30000000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q49290245'),
(29235, 'Simmern', 3019, 'RP', 82, 'DE', 49.98198000, 7.52351000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q275636'),
(29236, 'Simmersfeld', 3006, 'BW', 82, 'DE', 48.61667000, 8.51667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q552099'),
(29237, 'Simmertal', 3019, 'RP', 82, 'DE', 49.80981000, 7.52282000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q523147'),
(29238, 'Simmozheim', 3006, 'BW', 82, 'DE', 48.75127000, 8.81142000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q553770'),
(29239, 'Sindelfingen', 3006, 'BW', 82, 'DE', 48.70000000, 9.01667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q4019'),
(29240, 'Sindelsdorf', 3009, 'BY', 82, 'DE', 47.72458000, 11.33295000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q4019'),
(29241, 'Singen', 3006, 'BW', 82, 'DE', 47.75935000, 8.84030000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q6979'),
(29242, 'Singhofen', 3019, 'RP', 82, 'DE', 50.27440000, 7.83333000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q640414'),
(29243, 'Sinn', 3018, 'HE', 82, 'DE', 50.65000000, 8.33333000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q627276'),
(29244, 'Sinnersdorf', 3017, 'NW', 82, 'DE', 51.02445000, 6.81787000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q627276'),
(29245, 'Sinsheim', 3006, 'BW', 82, 'DE', 49.25290000, 8.87867000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q14950'),
(29246, 'Sinzheim', 3006, 'BW', 82, 'DE', 48.76667000, 8.16667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q81210'),
(29247, 'Sinzig', 3019, 'RP', 82, 'DE', 50.54384000, 7.24639000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q548989'),
(29248, 'Sinzing', 3009, 'BY', 82, 'DE', 49.00000000, 12.03333000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q491611'),
(29249, 'Sippersfeld', 3019, 'RP', 82, 'DE', 49.55000000, 7.93333000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q818571'),
(29250, 'Sipplingen', 3006, 'BW', 82, 'DE', 47.79678000, 9.09737000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q557628'),
(29251, 'Sittensen', 3008, 'NI', 82, 'DE', 53.27615000, 9.50429000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q557628'),
(29252, 'Sitzendorf', 3015, 'TH', 82, 'DE', 50.63182000, 11.17215000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q557628'),
(29253, 'Soderstorf', 3008, 'NI', 82, 'DE', 53.14275000, 10.14807000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q667441'),
(29254, 'Soest', 3017, 'NW', 82, 'DE', 51.57558000, 8.10619000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q6149'),
(29255, 'Sohland', 3021, 'SN', 82, 'DE', 51.04089000, 14.41897000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q6149'),
(29256, 'Sohland am Rotstein', 3021, 'SN', 82, 'DE', 51.11643000, 14.78372000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q525896'),
(29257, 'Sohren', 3019, 'RP', 82, 'DE', 49.93333000, 7.31667000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q646714'),
(29258, 'Solingen', 3017, 'NW', 82, 'DE', 51.17343000, 7.08450000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q2942'),
(29259, 'Sollstedt', 3015, 'TH', 82, 'DE', 51.30976000, 10.48810000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q1615197'),
(29260, 'Solms', 3018, 'HE', 82, 'DE', 50.53620000, 8.40704000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q1615197'),
(29261, 'Solnhofen', 3009, 'BY', 82, 'DE', 48.89582000, 10.99560000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q572949'),
(29262, 'Soltau', 3008, 'NI', 82, 'DE', 52.98638000, 9.84338000, '2019-10-05 22:44:48', '2019-10-05 22:44:48', 1, 'Q33491041'),
(29263, 'Soltendieck', 3008, 'NI', 82, 'DE', 52.87369000, 10.76162000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q686912'),
(29264, 'Sommerach', 3009, 'BY', 82, 'DE', 49.82923000, 10.20792000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q316088'),
(29265, 'Sommerhausen', 3009, 'BY', 82, 'DE', 49.70361000, 10.02605000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q511929'),
(29266, 'Sommerkahl', 3009, 'BY', 82, 'DE', 50.06961000, 9.25676000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q513182'),
(29267, 'Sommersdorf', 3011, 'ST', 82, 'DE', 52.16667000, 11.08333000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q564307'),
(29268, 'Sondershausen', 3015, 'TH', 82, 'DE', 51.36973000, 10.87011000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q517407'),
(29269, 'Sondheim vor der Rhön', 3009, 'BY', 82, 'DE', 50.46510000, 10.15675000, '2019-10-05 22:44:49', '2020-05-01 17:22:48', 1, 'Q583359'),
(29270, 'Sonneberg', 3015, 'TH', 82, 'DE', 50.35920000, 11.17463000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q499278'),
(29271, 'Sonneborn', 3015, 'TH', 82, 'DE', 50.99270000, 10.59173000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q499278'),
(29272, 'Sonnefeld', 3009, 'BY', 82, 'DE', 50.21667000, 11.13333000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q572628'),
(29273, 'Sonnen', 3009, 'BY', 82, 'DE', 48.68333000, 13.71667000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q504251'),
(29274, 'Sonnewalde', 3013, 'BB', 82, 'DE', 51.69223000, 13.64730000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q544969'),
(29275, 'Sonsbeck', 3017, 'NW', 82, 'DE', 51.60741000, 6.37916000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q163100'),
(29276, 'Sontheim', 3009, 'BY', 82, 'DE', 48.00704000, 10.35461000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q163100'),
(29277, 'Sontheim an der Brenz', 3006, 'BW', 82, 'DE', 48.55235000, 10.29097000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q2302546'),
(29278, 'Sonthofen', 3009, 'BY', 82, 'DE', 47.51821000, 10.28262000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q162845'),
(29279, 'Sontra', 3018, 'HE', 82, 'DE', 51.07171000, 9.93558000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q549971'),
(29280, 'Sosa', 3021, 'SN', 82, 'DE', 50.49917000, 12.65120000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q549971'),
(29281, 'Sottrum', 3008, 'NI', 82, 'DE', 53.11667000, 9.23333000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q505526'),
(29282, 'Soyen', 3009, 'BY', 82, 'DE', 48.10808000, 12.21006000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q257949'),
(29283, 'Spabrücken', 3019, 'RP', 82, 'DE', 49.90000000, 7.71667000, '2019-10-05 22:44:49', '2020-05-01 17:22:49', 1, 'Q257949'),
(29284, 'Spaichingen', 3006, 'BW', 82, 'DE', 48.07477000, 8.73508000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q519688'),
(29285, 'Spalt', 3009, 'BY', 82, 'DE', 49.17553000, 10.92453000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q312335'),
(29286, 'Spandau', 3010, 'BE', 82, 'DE', 52.55110000, 13.19921000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q569827'),
(29287, 'Spangenberg', 3018, 'HE', 82, 'DE', 51.11644000, 9.66270000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q628667'),
(29288, 'Spardorf', 3009, 'BY', 82, 'DE', 49.60854000, 11.05585000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q2307369'),
(29289, 'Sparneck', 3009, 'BY', 82, 'DE', 50.16214000, 11.84349000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q72731'),
(29290, 'Spay', 3019, 'RP', 82, 'DE', 50.25881000, 7.64839000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q573555'),
(29291, 'Spechbach', 3006, 'BW', 82, 'DE', 49.34639000, 8.88333000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q82220'),
(29292, 'Speicher', 3019, 'RP', 82, 'DE', 49.93333000, 6.63333000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q82220'),
(29293, 'Speichersdorf', 3009, 'BY', 82, 'DE', 49.87133000, 11.78123000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q503565'),
(29294, 'Speinshart', 3009, 'BY', 82, 'DE', 49.79002000, 11.81949000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q507938'),
(29295, 'Spelle', 3008, 'NI', 82, 'DE', 52.36667000, 7.46667000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q672822'),
(29296, 'Spenge', 3017, 'NW', 82, 'DE', 52.14021000, 8.48475000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q53904'),
(29297, 'Sperenberg', 3013, 'BB', 82, 'DE', 52.14113000, 13.36500000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q882100'),
(29298, 'Spergau', 3011, 'ST', 82, 'DE', 51.29347000, 12.02292000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q689552'),
(29299, 'Speyer', 3019, 'RP', 82, 'DE', 49.32083000, 8.43111000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q6829'),
(29300, 'Spiegelau', 3009, 'BY', 82, 'DE', 48.91517000, 13.36229000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q528154'),
(29301, 'Spiegelberg', 3006, 'BW', 82, 'DE', 49.04083000, 9.44444000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q81390'),
(29302, 'Spiesen-Elversberg', 3020, 'SL', 82, 'DE', 49.31667000, 7.13333000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q629932'),
(29303, 'Spiesheim', 3019, 'RP', 82, 'DE', 49.81028000, 8.12750000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q641594'),
(29304, 'Spornitz', 3007, 'MV', 82, 'DE', 53.40749000, 11.71864000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q370571'),
(29305, 'Spraitbach', 3006, 'BW', 82, 'DE', 48.88065000, 9.76217000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q550711'),
(29306, 'Sprakensehl', 3008, 'NI', 82, 'DE', 52.76668000, 10.49177000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q671766'),
(29307, 'Spreenhagen', 3013, 'BB', 82, 'DE', 52.34325000, 13.87663000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q624106'),
(29308, 'Sprendlingen', 3019, 'RP', 82, 'DE', 49.86667000, 7.98333000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q678738'),
(29309, 'Springe', 3008, 'NI', 82, 'DE', 52.20845000, 9.55416000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, '*********'),
(29310, 'Sprockhövel', 3017, 'NW', 82, 'DE', 51.34669000, 7.24343000, '2019-10-05 22:44:49', '2020-05-01 17:22:49', 1, '*********'),
(29311, 'St. Georg', 3016, 'HH', 82, 'DE', 53.55513000, 10.01231000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q1560'),
(29312, 'St. Pauli', 3016, 'HH', 82, 'DE', 53.55700000, 9.96400000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q1556'),
(29313, 'Staaken', 3010, 'BE', 82, 'DE', 52.53661000, 13.15057000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q641926'),
(29314, 'Stade', 3008, 'NI', 82, 'DE', 53.59337000, 9.47629000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q6949'),
(29315, 'Stadecken-Elsheim', 3019, 'RP', 82, 'DE', 49.91222000, 8.12528000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q683833'),
(29316, 'Stadelhofen', 3009, 'BY', 82, 'DE', 50.00317000, 11.19757000, '2019-10-05 22:44:49', '2019-10-05 22:44:49', 1, 'Q183261'),
(29317, 'Stadensen', 3008, 'NI', 82, 'DE', 52.87571000, 10.55619000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q700681'),
(29318, 'Stadt Wehlen', 3021, 'SN', 82, 'DE', 50.95821000, 14.03091000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q8240'),
(29319, 'Stadtallendorf', 3018, 'HE', 82, 'DE', 50.82261000, 9.01294000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q573514'),
(29320, 'Stadtbergen', 3009, 'BY', 82, 'DE', 48.36641000, 10.84636000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q503300'),
(29321, 'Stadthagen', 3008, 'NI', 82, 'DE', 52.32333000, 9.20311000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q544501'),
(29322, 'Stadtilm', 3015, 'TH', 82, 'DE', 50.77596000, 11.08262000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q552637'),
(29323, 'Stadtkyll', 3019, 'RP', 82, 'DE', 50.35000000, 6.53333000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q653314'),
(29324, 'Stadtlauringen', 3009, 'BY', 82, 'DE', 50.18708000, 10.36164000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q385427'),
(29325, 'Stadtlengsfeld', 3015, 'TH', 82, 'DE', 50.78329000, 10.12918000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q530059'),
(29326, 'Stadtlohn', 3017, 'NW', 82, 'DE', 51.99399000, 6.91918000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, '*********'),
(29327, 'Stadtoldendorf', 3008, 'NI', 82, 'DE', 51.88242000, 9.62650000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, '*********'),
(29328, 'Stadtprozelten', 3009, 'BY', 82, 'DE', 49.78466000, 9.41184000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q502939'),
(29329, 'Stadtrandsiedlung Malchow', 3010, 'BE', 82, 'DE', 52.58295000, 13.47811000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q644396'),
(29330, 'Stadtroda', 3015, 'TH', 82, 'DE', 50.85684000, 11.72677000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q520088'),
(29331, 'Stadtsteinach', 3009, 'BY', 82, 'DE', 50.16433000, 11.50349000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q427292'),
(29332, 'Stadum', 3005, 'SH', 82, 'DE', 54.73333000, 9.05000000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q427292'),
(29333, 'Stahnsdorf', 3013, 'BB', 82, 'DE', 52.38333000, 13.21667000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q640426');

