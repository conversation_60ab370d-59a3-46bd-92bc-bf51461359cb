INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(23832, 'Bad Lobenstein', 3015, 'TH', 82, 'DE', 50.45223000, 11.63930000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q505656'),
(23833, 'Bad Marienberg', 3019, 'RP', 82, 'DE', 50.64947000, 7.94958000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q534491'),
(23834, '<PERSON>', 3017, 'NW', 82, 'DE', 51.89588000, 8.98313000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q798730'),
(23835, 'Bad Mergentheim', 3006, '<PERSON><PERSON>', 82, '<PERSON>', 49.49250000, 9.77361000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q61942'),
(23836, '<PERSON> <PERSON><PERSON>u', 3021, 'SN', 82, 'DE', 51.55051000, 14.71240000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q165131'),
(23837, 'Bad M<PERSON>nder am <PERSON><PERSON>', 3008, 'NI', 82, 'DE', 52.19551000, 9.46421000, '2019-10-05 22:40:36', '2020-05-01 17:22:48', 1, 'Q39779'),
(23838, 'Bad Münster am Stein-Ebernburg', 3019, 'RP', 82, 'DE', 49.81159000, 7.84523000, '2019-10-05 22:40:36', '2020-05-01 17:22:49', 1, 'Q50894'),
(23839, 'Bad Münstereifel', 3017, 'NW', 82, 'DE', 50.55667000, 6.76424000, '2019-10-05 22:40:36', '2020-05-01 17:22:49', 1, 'Q241450'),
(23840, 'Bad Nauheim', 3018, 'HE', 82, 'DE', 50.36463000, 8.73859000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q16105'),
(23841, 'Bad Nenndorf', 3008, 'NI', 82, 'DE', 52.33703000, 9.37904000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q543342'),
(23842, 'Bad Neuenahr-Ahrweiler', 3019, 'RP', 82, 'DE', 50.54322000, 7.11130000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q522620'),
(23843, 'Bad Neustadt an der Saale', 3009, 'BY', 82, 'DE', 50.32174000, 10.20673000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q522620'),
(23844, 'Bad Oeynhausen', 3017, 'NW', 82, 'DE', 52.20699000, 8.80365000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q6858'),
(23845, 'Bad Oldesloe', 3005, 'SH', 82, 'DE', 53.81167000, 10.37417000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q155226'),
(23846, 'Bad Orb', 3018, 'HE', 82, 'DE', 50.22788000, 9.34782000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q155226'),
(23847, 'Bad Peterstal-Griesbach', 3006, 'BW', 82, 'DE', 48.43333000, 8.21667000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q527937'),
(23848, 'Bad Pyrmont', 3008, 'NI', 82, 'DE', 51.98589000, 9.25246000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q556352'),
(23849, 'Bad Rappenau', 3006, 'BW', 82, 'DE', 49.23848000, 9.10180000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q27918'),
(23850, 'Bad Reichenhall', 3009, 'BY', 82, 'DE', 47.72947000, 12.87819000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q487971'),
(23851, 'Bad Rippoldsau-Schapbach', 3006, 'BW', 82, 'DE', 48.42883000, 8.32584000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q80477'),
(23852, 'Bad Rothenfelde', 3008, 'NI', 82, 'DE', 52.11667000, 8.16667000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q186168'),
(23853, 'Bad Saarow', 3013, 'BB', 82, 'DE', 52.28333000, 14.06667000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q49421'),
(23854, 'Bad Sachsa', 3008, 'NI', 82, 'DE', 51.59499000, 10.55546000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q541860'),
(23855, 'Bad Salzdetfurth', 3008, 'NI', 82, 'DE', 52.05777000, 10.00580000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q633004'),
(23856, 'Bad Salzschlirf', 3018, 'HE', 82, 'DE', 50.62388000, 9.50815000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q624093'),
(23857, 'Bad Salzuflen', 3017, 'NW', 82, 'DE', 52.08620000, 8.74434000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q4108'),
(23858, 'Bad Salzungen', 3015, 'TH', 82, 'DE', 50.81342000, 10.23610000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q502662'),
(23859, 'Bad Sassendorf', 3017, 'NW', 82, 'DE', 51.58333000, 8.16667000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q11130'),
(23860, 'Bad Schandau', 3021, 'SN', 82, 'DE', 50.91743000, 14.15494000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q6406'),
(23861, 'Bad Schlema', 3021, 'SN', 82, 'DE', 50.60257000, 12.67288000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q57711'),
(23862, 'Bad Schmiedeberg', 3011, 'ST', 82, 'DE', 51.68516000, 12.73483000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q522557'),
(23863, 'Bad Schussenried', 3006, 'BW', 82, 'DE', 48.00468000, 9.65741000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q522513'),
(23864, 'Bad Schwalbach', 3018, 'HE', 82, 'DE', 50.14196000, 8.06964000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q522541'),
(23865, 'Bad Schwartau', 3005, 'SH', 82, 'DE', 53.91887000, 10.69691000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q516432'),
(23866, 'Bad Segeberg', 3005, 'SH', 82, 'DE', 53.94313000, 10.30215000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q496050'),
(23867, 'Bad Soden am Taunus', 3018, 'HE', 82, 'DE', 50.14080000, 8.50449000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q39785'),
(23868, 'Bad Soden-Salmünster', 3018, 'HE', 82, 'DE', 50.27574000, 9.36705000, '2019-10-05 22:40:36', '2020-05-01 17:22:48', 1, 'Q558600'),
(23869, 'Bad Sooden-Allendorf', 3018, 'HE', 82, 'DE', 51.27092000, 9.97483000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q564821'),
(23870, 'Bad Staffelstein', 3009, 'BY', 82, 'DE', 50.10199000, 11.00128000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q504634'),
(23871, 'Bad Steben', 3009, 'BY', 82, 'DE', 50.36648000, 11.64438000, '2019-10-05 22:40:36', '2019-10-05 22:40:36', 1, 'Q522305'),
(23872, 'Bad Suderode', 3011, 'ST', 82, 'DE', 51.73333000, 11.11667000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q574468'),
(23873, 'Bad Sulza', 3015, 'TH', 82, 'DE', 51.08929000, 11.62474000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q124840'),
(23874, 'Bad Säckingen', 3006, 'BW', 82, 'DE', 47.55371000, 7.94612000, '2019-10-05 22:40:37', '2020-05-01 17:22:47', 1, 'Q522362'),
(23875, 'Bad Sülze', 3007, 'MV', 82, 'DE', 54.11084000, 12.66054000, '2019-10-05 22:40:37', '2020-05-01 17:22:48', 1, 'Q507967'),
(23876, 'Bad Teinach-Zavelstein', 3006, 'BW', 82, 'DE', 48.69051000, 8.69285000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q503266'),
(23877, 'Bad Tennstedt', 3015, 'TH', 82, 'DE', 51.15447000, 10.83873000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q534508'),
(23878, 'Bad Tölz', 3009, 'BY', 82, 'DE', 47.76111000, 11.55890000, '2019-10-05 22:40:37', '2020-05-01 17:22:47', 1, 'Q280491'),
(23879, 'Bad Urach', 3006, 'BW', 82, 'DE', 48.49107000, 9.40009000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q83032'),
(23880, 'Bad Vilbel', 3018, 'HE', 82, 'DE', 50.17866000, 8.73756000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q16082'),
(23881, 'Bad Waldsee', 3006, 'BW', 82, 'DE', 47.92027000, 9.75490000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q515423'),
(23882, 'Bad Wiessee', 3009, 'BY', 82, 'DE', 47.71667000, 11.71667000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q502921'),
(23883, 'Bad Wildbad', 3006, 'BW', 82, 'DE', 48.75071000, 8.55040000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q502755'),
(23884, 'Bad Wildungen', 3018, 'HE', 82, 'DE', 51.11963000, 9.12475000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q516462'),
(23885, 'Bad Wilsnack', 3013, 'BB', 82, 'DE', 52.95607000, 11.94903000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q570108'),
(23886, 'Bad Wimpfen', 3006, 'BW', 82, 'DE', 49.22971000, 9.15648000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q491535'),
(23887, 'Bad Windsheim', 3009, 'BY', 82, 'DE', 49.50274000, 10.41539000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q132278'),
(23888, 'Bad Wurzach', 3006, 'BW', 82, 'DE', 47.90799000, 9.89686000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q132278'),
(23889, 'Bad Wörishofen', 3009, 'BY', 82, 'DE', 48.00674000, 10.59666000, '2019-10-05 22:40:37', '2020-05-01 17:22:47', 1, 'Q504579'),
(23890, 'Bad Zwischenahn', 3008, 'NI', 82, 'DE', 53.18333000, 8.00000000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q504526'),
(23891, 'Bad Überkingen', 3006, 'BW', 82, 'DE', 48.59991000, 9.79586000, '2019-10-05 22:40:37', '2020-05-01 17:22:47', 1, 'Q83098'),
(23892, 'Badbergen', 3008, 'NI', 82, 'DE', 52.63333000, 7.98333000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q649551'),
(23893, 'Baddeckenstedt', 3008, 'NI', 82, 'DE', 52.08333000, 10.23333000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q638289'),
(23894, 'Badem', 3019, 'RP', 82, 'DE', 50.00000000, 6.61667000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q553458'),
(23895, 'Baden-Baden', 3006, 'BW', 82, 'DE', 48.76060000, 8.23975000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q4100'),
(23896, 'Badenhausen', 3008, 'NI', 82, 'DE', 51.76996000, 10.20493000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q545814'),
(23897, 'Badenweiler', 3006, 'BW', 82, 'DE', 47.80223000, 7.67236000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q25430'),
(23898, 'Baesweiler', 3017, 'NW', 82, 'DE', 50.90964000, 6.18874000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q200894'),
(23899, 'Bahlingen', 3006, 'BW', 82, 'DE', 48.12064000, 7.73982000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q518801'),
(23900, 'Bahrdorf', 3008, 'NI', 82, 'DE', 52.38587000, 11.00040000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q518801'),
(23901, 'Bahrenborstel', 3008, 'NI', 82, 'DE', 52.57009000, 8.80863000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q518801'),
(23902, 'Baienfurt', 3006, 'BW', 82, 'DE', 47.82857000, 9.65157000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q632905'),
(23903, 'Baierbrunn', 3009, 'BY', 82, 'DE', 48.02030000, 11.48689000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q503705'),
(23904, 'Baiersbronn', 3006, 'BW', 82, 'DE', 48.50318000, 8.37699000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q81241'),
(23905, 'Baiersdorf', 3009, 'BY', 82, 'DE', 49.65807000, 11.03594000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q163423'),
(23906, 'Baindt', 3006, 'BW', 82, 'DE', 47.85000000, 9.66667000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q527565'),
(23907, 'Baisweil', 3009, 'BY', 82, 'DE', 47.94439000, 10.54007000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q507804'),
(23908, 'Bakum', 3008, 'NI', 82, 'DE', 52.74118000, 8.19546000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q657321'),
(23909, 'Balge', 3008, 'NI', 82, 'DE', 52.71667000, 9.16667000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q657321'),
(23910, 'Balingen', 3006, 'BW', 82, 'DE', 48.27525000, 8.85464000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q16015'),
(23911, 'Balje', 3008, 'NI', 82, 'DE', 53.83333000, 9.13333000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q500277'),
(23912, 'Ballenstedt', 3011, 'ST', 82, 'DE', 51.71900000, 11.23265000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q50886'),
(23913, 'Baltmannsweiler', 3006, 'BW', 82, 'DE', 48.74215000, 9.44940000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q80464'),
(23914, 'Balve', 3017, 'NW', 82, 'DE', 51.33150000, 7.86424000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q31901955'),
(23915, 'Balzfeld', 3006, 'BW', 82, 'DE', 49.26892000, 8.78919000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q31901955'),
(23916, 'Balzhausen', 3009, 'BY', 82, 'DE', 48.24228000, 10.49366000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q503768'),
(23917, 'Bamberg', 3009, 'BY', 82, 'DE', 49.89873000, 10.90067000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q3936'),
(23918, 'Bamenohl', 3017, 'NW', 82, 'DE', 51.16527000, 7.98412000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q3936'),
(23919, 'Bammental', 3006, 'BW', 82, 'DE', 49.35611000, 8.77944000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q81117'),
(23920, 'Bann', 3019, 'RP', 82, 'DE', 49.38333000, 7.61667000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q81117'),
(23921, 'Bannewitz', 3021, 'SN', 82, 'DE', 50.99291000, 13.71712000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q6448'),
(23922, 'Banteln', 3008, 'NI', 82, 'DE', 52.06667000, 9.75000000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q112937'),
(23923, 'Banzkow', 3007, 'MV', 82, 'DE', 53.52497000, 11.52306000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q499879'),
(23924, 'Barbing', 3009, 'BY', 82, 'DE', 49.00000000, 12.20000000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q489142'),
(23925, 'Barby', 3011, 'ST', 82, 'DE', 51.96711000, 11.88261000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q31903450'),
(23926, 'Barchfeld', 3015, 'TH', 82, 'DE', 50.82872000, 11.17955000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q31903502'),
(23927, 'Bardowick', 3008, 'NI', 82, 'DE', 53.29354000, 10.38811000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q508028'),
(23928, 'Barenburg', 3008, 'NI', 82, 'DE', 52.61953000, 8.79999000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q503843'),
(23929, 'Barendorf', 3008, 'NI', 82, 'DE', 53.22896000, 10.52158000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q127351'),
(23930, 'Bargfeld-Stegen', 3005, 'SH', 82, 'DE', 53.76778000, 10.18750000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q565481'),
(23931, 'Bargstedt', 3008, 'NI', 82, 'DE', 53.46667000, 9.45000000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q565481'),
(23932, 'Bargteheide', 3005, 'SH', 82, 'DE', 53.72856000, 10.26695000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q149921'),
(23933, 'Bark', 3005, 'SH', 82, 'DE', 53.91146000, 10.17949000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q149921'),
(23934, 'Barkelsby', 3005, 'SH', 82, 'DE', 54.50000000, 9.83333000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q149921'),
(23935, 'Barleben', 3011, 'ST', 82, 'DE', 52.20193000, 11.61770000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q550241'),
(23936, 'Barmbek-Nord', 3016, 'HH', 82, 'DE', 53.60520000, 10.03988000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q1138'),
(23937, 'Barmstedt', 3005, 'SH', 82, 'DE', 53.79209000, 9.76739000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q516930'),
(23938, 'Barnstorf', 3008, 'NI', 82, 'DE', 52.71009000, 8.50256000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q504752'),
(23939, 'Barnstädt', 3011, 'ST', 82, 'DE', 51.34401000, 11.63735000, '2019-10-05 22:40:37', '2020-05-01 17:22:49', 1, 'Q634992'),
(23940, 'Barntrup', 3017, 'NW', 82, 'DE', 51.99038000, 9.11642000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q31904611'),
(23941, 'Barsbüttel', 3005, 'SH', 82, 'DE', 53.56667000, 10.16667000, '2019-10-05 22:40:37', '2020-05-01 17:22:49', 1, 'Q629424'),
(23942, 'Barsinghausen', 3008, 'NI', 82, 'DE', 52.30000000, 9.45000000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q16022'),
(23943, 'Bartenshagen-Parkentin', 3007, 'MV', 82, 'DE', 54.08116000, 11.97849000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q690547'),
(23944, 'Barth', 3007, 'MV', 82, 'DE', 54.36346000, 12.72491000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q161943'),
(23945, 'Bartholomä', 3006, 'BW', 82, 'DE', 48.75350000, 9.98752000, '2019-10-05 22:40:37', '2020-05-01 17:22:47', 1, 'Q513376'),
(23946, 'Barum', 3008, 'NI', 82, 'DE', 53.35000000, 10.40000000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q513376'),
(23947, 'Baruth', 3013, 'BB', 82, 'DE', 52.04468000, 13.50270000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q311543'),
(23948, 'Barver', 3008, 'NI', 82, 'DE', 52.62066000, 8.59251000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q515956'),
(23949, 'Barwedel', 3008, 'NI', 82, 'DE', 52.52192000, 10.77488000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q515590'),
(23950, 'Barßel', 3008, 'NI', 82, 'DE', 53.16981000, 7.75012000, '2019-10-05 22:40:37', '2020-05-01 17:22:48', 1, 'Q559525'),
(23951, 'Basdahl', 3008, 'NI', 82, 'DE', 53.44502000, 9.00025000, '2019-10-05 22:40:37', '2019-10-05 22:40:37', 1, 'Q514556'),
(23952, 'Bassenheim', 3019, 'RP', 82, 'DE', 50.35820000, 7.45961000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q566930'),
(23953, 'Bassum', 3008, 'NI', 82, 'DE', 52.85059000, 8.72791000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q501865'),
(23954, 'Bastheim', 3009, 'BY', 82, 'DE', 50.40080000, 10.20372000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q569552'),
(23955, 'Bastorf', 3007, 'MV', 82, 'DE', 54.12566000, 11.69695000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q690589'),
(23956, 'Battenberg', 3018, 'HE', 82, 'DE', 51.01391000, 8.64603000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31906529'),
(23957, 'Baudenbach', 3009, 'BY', 82, 'DE', 49.62512000, 10.53598000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q505345'),
(23958, 'Baumholder', 3019, 'RP', 82, 'DE', 49.61738000, 7.33381000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q541832'),
(23959, 'Baumschulenweg', 3010, 'BE', 82, 'DE', 52.46583000, 13.48523000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q644363'),
(23960, 'Baunach', 3009, 'BY', 82, 'DE', 49.98592000, 10.85179000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q504808'),
(23961, 'Baunatal', 3018, 'HE', 82, 'DE', 51.25182000, 9.40747000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q516581'),
(23962, 'Bausendorf', 3019, 'RP', 82, 'DE', 50.01667000, 7.00000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q569296'),
(23963, 'Bautzen', 3021, 'SN', 82, 'DE', 51.18035000, 14.43494000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q14835'),
(23964, 'Bawinkel', 3008, 'NI', 82, 'DE', 52.60000000, 7.40000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q566896'),
(23965, 'Bayenthal', 3017, 'NW', 82, 'DE', 50.91218000, 6.96799000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q3853'),
(23966, 'Bayerbach', 3009, 'BY', 82, 'DE', 48.70450000, 12.29750000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q253521'),
(23967, 'Bayerisch Eisenstein', 3009, 'BY', 82, 'DE', 49.11667000, 13.20000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q491435'),
(23968, 'Bayerisch Gmain', 3009, 'BY', 82, 'DE', 47.71667000, 12.90000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q263586'),
(23969, 'Bayreuth', 3009, 'BY', 82, 'DE', 49.94782000, 11.57893000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q3923'),
(23970, 'Bayrischzell', 3009, 'BY', 82, 'DE', 47.67440000, 12.01449000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q506244'),
(23971, 'Bebra', 3018, 'HE', 82, 'DE', 50.97445000, 9.79562000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q558530'),
(23972, 'Bechhofen', 3019, 'RP', 82, 'DE', 49.35000000, 7.40000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q558530'),
(23973, 'Bechtheim', 3019, 'RP', 82, 'DE', 49.72778000, 8.29222000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q569599'),
(23974, 'Bechtolsheim', 3019, 'RP', 82, 'DE', 49.80417000, 8.19389000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q554268'),
(23975, 'Bechtsrieth', 3009, 'BY', 82, 'DE', 49.64487000, 12.20997000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q503999'),
(23976, 'Beckdorf', 3008, 'NI', 82, 'DE', 53.41667000, 9.61667000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q502158'),
(23977, 'Beckedorf', 3008, 'NI', 82, 'DE', 52.35000000, 9.31667000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q670312'),
(23978, 'Beckingen', 3020, 'SL', 82, 'DE', 49.40000000, 6.70000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q559689'),
(23979, 'Beckum', 3017, 'NW', 82, 'DE', 51.75571000, 8.04075000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q559689'),
(23980, 'Bedburg', 3017, 'NW', 82, 'DE', 50.99258000, 6.57128000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q245292'),
(23981, 'Beedenbostel', 3008, 'NI', 82, 'DE', 52.64288000, 10.25907000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q566343'),
(23982, 'Beelen', 3017, 'NW', 82, 'DE', 51.92906000, 8.11117000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q2815'),
(23983, 'Beelitz', 3013, 'BB', 82, 'DE', 52.23812000, 12.97140000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q544439'),
(23984, 'Beerfelden', 3018, 'HE', 82, 'DE', 49.56858000, 8.97444000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q552508'),
(23985, 'Beesenlaublingen', 3011, 'ST', 82, 'DE', 51.71274000, 11.69729000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q2789462'),
(23986, 'Beesenstedt', 3011, 'ST', 82, 'DE', 51.56828000, 11.73323000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q599776'),
(23987, 'Beeskow', 3013, 'BB', 82, 'DE', 52.17291000, 14.24597000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q572615'),
(23988, 'Beesten', 3008, 'NI', 82, 'DE', 52.43333000, 7.50000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q630999'),
(23989, 'Beetzendorf', 3011, 'ST', 82, 'DE', 52.70226000, 11.08890000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q551981'),
(23990, 'Behringen', 3015, 'TH', 82, 'DE', 50.77283000, 11.01403000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q551981'),
(23991, 'Bei der Höhne', 3008, 'NI', 82, 'DE', 53.51351000, 9.11191000, '2019-10-05 22:40:38', '2020-05-01 17:22:48', 1, 'Q551981'),
(23992, 'Beierfeld', 3021, 'SN', 82, 'DE', 50.56529000, 12.79049000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q814665'),
(23993, 'Beiersdorf', 3021, 'SN', 82, 'DE', 51.07405000, 14.53828000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q814665'),
(23994, 'Beilrode', 3021, 'SN', 82, 'DE', 51.56667000, 13.06667000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q814665'),
(23995, 'Beilstein', 3006, 'BW', 82, 'DE', 49.04140000, 9.31370000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31908847'),
(23996, 'Beimerstetten', 3006, 'BW', 82, 'DE', 48.48333000, 9.98333000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q515002'),
(23997, 'Beindersheim', 3019, 'RP', 82, 'DE', 49.56056000, 8.31944000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q22987'),
(23998, 'Belgern', 3021, 'SN', 82, 'DE', 51.48263000, 13.12382000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q12042'),
(23999, 'Belgershain', 3021, 'SN', 82, 'DE', 51.23333000, 12.55000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q10740'),
(24000, 'Bell', 3019, 'RP', 82, 'DE', 50.06139000, 7.41515000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31909048'),
(24001, 'Belleben', 3011, 'ST', 82, 'DE', 51.67499000, 11.63420000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q2694018'),
(24002, 'Bellenberg', 3009, 'BY', 82, 'DE', 48.25726000, 10.09094000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q2694018'),
(24003, 'Bellheim', 3019, 'RP', 82, 'DE', 49.19833000, 8.27944000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q552382'),
(24004, 'Belm', 3008, 'NI', 82, 'DE', 52.30000000, 8.13333000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q570299'),
(24005, 'Beltheim', 3019, 'RP', 82, 'DE', 50.10606000, 7.46208000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q562462'),
(24006, 'Bempflingen', 3006, 'BW', 82, 'DE', 48.57178000, 9.26834000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q80483'),
(24007, 'Bendestorf', 3008, 'NI', 82, 'DE', 53.33583000, 9.96154000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q631951'),
(24008, 'Bendorf', 3019, 'RP', 82, 'DE', 50.42289000, 7.57924000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q708708'),
(24009, 'Benediktbeuern', 3009, 'BY', 82, 'DE', 47.70624000, 11.41522000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q503628'),
(24010, 'Benndorf', 3011, 'ST', 82, 'DE', 51.57035000, 11.49290000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q503628'),
(24011, 'Benneckenstein', 3011, 'ST', 82, 'DE', 51.66823000, 10.71716000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q700388'),
(24012, 'Bennewitz', 3021, 'SN', 82, 'DE', 51.36074000, 12.71376000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q10741'),
(24013, 'Benningen', 3009, 'BY', 82, 'DE', 47.96667000, 10.21667000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q547187'),
(24014, 'Benningen am Neckar', 3006, 'BW', 82, 'DE', 48.94672000, 9.24212000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q61653'),
(24015, 'Bennstedt', 3011, 'ST', 82, 'DE', 51.48201000, 11.82620000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q701140'),
(24016, 'Bennungen', 3011, 'ST', 82, 'DE', 51.46085000, 11.11816000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q695574'),
(24017, 'Bensdorf', 3013, 'BB', 82, 'DE', 52.41667000, 12.33333000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q695574'),
(24018, 'Benshausen', 3015, 'TH', 82, 'DE', 50.65000000, 10.60000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q556781'),
(24019, 'Bensheim', 3018, 'HE', 82, 'DE', 49.68369000, 8.61839000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q14872'),
(24020, 'Bentwisch', 3007, 'MV', 82, 'DE', 54.11503000, 12.20465000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31910192'),
(24021, 'Bentzin', 3007, 'MV', 82, 'DE', 53.94608000, 13.27320000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q689676'),
(24022, 'Beratzhausen', 3009, 'BY', 82, 'DE', 49.09517000, 11.80970000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q429492'),
(24023, 'Berching', 3009, 'BY', 82, 'DE', 49.10695000, 11.44138000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q248062'),
(24024, 'Berchtesgaden', 3009, 'BY', 82, 'DE', 47.63236000, 13.00187000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q278221'),
(24025, 'Berg', 3009, 'BY', 82, 'DE', 49.81417000, 12.14161000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q278221'),
(24026, 'Berg', 3019, 'RP', 82, 'DE', 48.98403000, 8.20232000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31910376'),
(24027, 'Berg im Gau', 3009, 'BY', 82, 'DE', 48.63333000, 11.25000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q504700'),
(24028, 'Berga', 3011, 'ST', 82, 'DE', 51.45561000, 11.00710000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q504700'),
(24029, 'Berga', 3015, 'TH', 82, 'DE', 50.75184000, 12.16445000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q276150'),
(24030, 'Bergatreute', 3006, 'BW', 82, 'DE', 47.85000000, 9.75000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q533761'),
(24031, 'Berge', 3008, 'NI', 82, 'DE', 52.62326000, 7.74550000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q533761'),
(24032, 'Bergedorf', 3016, 'HH', 82, 'DE', 53.48462000, 10.22904000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q1637'),
(24033, 'Bergen', 3008, 'NI', 82, 'DE', 52.80837000, 9.96374000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31910477'),
(24034, 'Bergen', 3021, 'SN', 82, 'DE', 50.47100000, 12.27448000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31910471'),
(24035, 'Bergen', 3009, 'BY', 82, 'DE', 47.80837000, 12.58982000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31910464'),
(24036, 'Bergen an der Dumme', 3008, 'NI', 82, 'DE', 52.89109000, 10.95629000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q705616'),
(24037, 'Bergen auf Rügen', 3007, 'MV', 82, 'DE', 54.41823000, 13.43349000, '2019-10-05 22:40:38', '2020-05-01 17:22:48', 1, 'Q492538'),
(24038, 'Berghaupten', 3006, 'BW', 82, 'DE', 48.40602000, 7.98672000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q515800'),
(24039, 'Bergheim', 3017, 'NW', 82, 'DE', 50.95572000, 6.63986000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q515800'),
(24040, 'Berghülen', 3006, 'BW', 82, 'DE', 48.46399000, 9.76110000, '2019-10-05 22:40:38', '2020-05-01 17:22:47', 1, 'Q515191'),
(24041, 'Bergisch Gladbach', 3017, 'NW', 82, 'DE', 50.98560000, 7.13298000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31910602'),
(24042, 'Bergkamen', 3017, 'NW', 82, 'DE', 51.61633000, 7.64451000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q4192'),
(24043, 'Bergkirchen', 3009, 'BY', 82, 'DE', 48.25671000, 11.36488000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q4192'),
(24044, 'Berglern', 3009, 'BY', 82, 'DE', 48.38407000, 11.93012000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q511811'),
(24045, 'Bergneustadt', 3017, 'NW', 82, 'DE', 51.02496000, 7.65599000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q10976'),
(24046, 'Bergrheinfeld', 3009, 'BY', 82, 'DE', 50.01028000, 10.18089000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q553854'),
(24047, 'Bergstedt', 3016, 'HH', 82, 'DE', 53.67111000, 10.12694000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q1641'),
(24048, 'Bergtheim', 3009, 'BY', 82, 'DE', 49.90000000, 10.06667000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q255903'),
(24049, 'Bergwitz', 3011, 'ST', 82, 'DE', 51.79479000, 12.58954000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q633869'),
(24050, 'Berkenbrück', 3013, 'BB', 82, 'DE', 52.35000000, 14.15000000, '2019-10-05 22:40:38', '2020-05-01 17:22:48', 1, 'Q633869'),
(24051, 'Berkenthin', 3005, 'SH', 82, 'DE', 53.73333000, 10.65000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q641603'),
(24052, 'Berkheim', 3006, 'BW', 82, 'DE', 48.04263000, 10.08227000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q586965'),
(24053, 'Berlin', 3010, 'BE', 82, 'DE', 52.52437000, 13.41053000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q64'),
(24054, 'Berlin Köpenick', 3010, 'BE', 82, 'DE', 52.44254000, 13.58228000, '2019-10-05 22:40:38', '2020-05-01 17:22:48', 1, 'Q64'),
(24055, 'Berlin Treptow', 3010, 'BE', 82, 'DE', 52.49376000, 13.44469000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q677833'),
(24056, 'Berlingerode', 3015, 'TH', 82, 'DE', 51.45775000, 10.23840000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q552675'),
(24057, 'Berlstedt', 3015, 'TH', 82, 'DE', 51.06142000, 11.24288000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q676926'),
(24058, 'Bermatingen', 3006, 'BW', 82, 'DE', 47.73333000, 9.35000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q507274'),
(24059, 'Bernau', 3006, 'BW', 82, 'DE', 47.80018000, 8.03830000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q537241'),
(24060, 'Bernau am Chiemsee', 3009, 'BY', 82, 'DE', 47.81167000, 12.37566000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q126960'),
(24061, 'Bernau bei Berlin', 3013, 'BB', 82, 'DE', 52.67982000, 13.58708000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q9300'),
(24062, 'Bernbeuren', 3009, 'BY', 82, 'DE', 47.73696000, 10.77707000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q9300'),
(24063, 'Bernburg', 3011, 'ST', 82, 'DE', 51.79464000, 11.74010000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q14938'),
(24064, 'Berngau', 3009, 'BY', 82, 'DE', 49.25000000, 11.40000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q504711'),
(24065, 'Bernhardswald', 3009, 'BY', 82, 'DE', 49.09123000, 12.24744000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q489134'),
(24066, 'Bernitt', 3007, 'MV', 82, 'DE', 53.90403000, 11.88669000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q657440'),
(24067, 'Bernkastel-Kues', 3019, 'RP', 82, 'DE', 49.91602000, 7.07664000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q155622'),
(24068, 'Bernried', 3009, 'BY', 82, 'DE', 48.91667000, 12.88333000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31911096'),
(24069, 'Bernsbach', 3021, 'SN', 82, 'DE', 50.57624000, 12.76751000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q57717'),
(24070, 'Bernsdorf', 3021, 'SN', 82, 'DE', 51.37350000, 14.06886000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q81732'),
(24071, 'Bernstadt', 3021, 'SN', 82, 'DE', 51.04736000, 14.82784000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31911187'),
(24072, 'Bernstadt', 3006, 'BW', 82, 'DE', 48.49957000, 10.02575000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q311771'),
(24073, 'Bersenbrück', 3008, 'NI', 82, 'DE', 52.55160000, 7.94836000, '2019-10-05 22:40:38', '2020-05-01 17:22:48', 1, 'Q520011'),
(24074, 'Berstadt', 3018, 'HE', 82, 'DE', 50.42606000, 8.86621000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q520011'),
(24075, 'Berthelsdorf', 3021, 'SN', 82, 'DE', 51.04679000, 14.22197000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31911413'),
(24076, 'Bertsdorf-Hörnitz', 3021, 'SN', 82, 'DE', 50.88593000, 14.73696000, '2019-10-05 22:40:38', '2020-05-01 17:22:49', 1, 'Q502461'),
(24077, 'Berumbur', 3008, 'NI', 82, 'DE', 53.60000000, 7.31667000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q625891'),
(24078, 'Besigheim', 3006, 'BW', 82, 'DE', 48.99797000, 9.14268000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q61825'),
(24079, 'Bestensee', 3013, 'BB', 82, 'DE', 52.23978000, 13.63732000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q582789'),
(24080, 'Bestwig', 3017, 'NW', 82, 'DE', 51.36081000, 8.40082000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q10928'),
(24081, 'Betheln', 3008, 'NI', 82, 'DE', 52.11431000, 9.79397000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q555790'),
(24082, 'Bettingen', 3019, 'RP', 82, 'DE', 49.94211000, 6.40160000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q555790'),
(24083, 'Betzdorf', 3019, 'RP', 82, 'DE', 50.79094000, 7.87189000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31911781'),
(24084, 'Betzendorf', 3008, 'NI', 82, 'DE', 53.13750000, 10.31273000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q631997'),
(24085, 'Betzigau', 3009, 'BY', 82, 'DE', 47.73333000, 10.38333000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q631997'),
(24086, 'Beuern', 3018, 'HE', 82, 'DE', 50.62745000, 8.82108000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q631997'),
(24087, 'Beuna', 3011, 'ST', 82, 'DE', 51.31667000, 11.95000000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q702094'),
(24088, 'Beuren', 3006, 'BW', 82, 'DE', 48.56927000, 9.40406000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q702094'),
(24089, 'Beutelsbach', 3009, 'BY', 82, 'DE', 48.55000000, 13.11667000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q702094'),
(24090, 'Bevenrode', 3008, 'NI', 82, 'DE', 52.34026000, 10.57743000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q702094'),
(24091, 'Bevern', 3008, 'NI', 82, 'DE', 51.85826000, 9.49408000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q649769'),
(24092, 'Beverstedt', 3008, 'NI', 82, 'DE', 53.43413000, 8.81915000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q509145'),
(24093, 'Beverungen', 3017, 'NW', 82, 'DE', 51.66801000, 9.37417000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31911989'),
(24094, 'Bexbach', 3020, 'SL', 82, 'DE', 49.34615000, 7.25527000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q504022'),
(24095, 'Biberach', 3006, 'BW', 82, 'DE', 48.61008000, 8.27704000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31912124'),
(24096, 'Biberach an der Riß', 3006, 'BW', 82, 'DE', 48.09345000, 9.79053000, '2019-10-05 22:40:38', '2020-05-01 17:22:47', 1, 'Q16069'),
(24097, 'Biberbach', 3009, 'BY', 82, 'DE', 48.51778000, 10.81139000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q526553'),
(24098, 'Biblis', 3018, 'HE', 82, 'DE', 49.69167000, 8.45861000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q375875'),
(24099, 'Biburg', 3009, 'BY', 82, 'DE', 48.79066000, 11.85726000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q375875'),
(24100, 'Bichl', 3009, 'BY', 82, 'DE', 47.72010000, 11.41231000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q509296'),
(24101, 'Bickenbach', 3018, 'HE', 82, 'DE', 49.75917000, 8.61750000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q509296'),
(24102, 'Bidingen', 3009, 'BY', 82, 'DE', 47.82974000, 10.72623000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q549554'),
(24103, 'Biebelried', 3009, 'BY', 82, 'DE', 49.76667000, 10.08333000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q512905'),
(24104, 'Biebesheim', 3018, 'HE', 82, 'DE', 49.78088000, 8.46696000, '2019-10-05 22:40:38', '2019-10-05 22:40:38', 1, 'Q31912329'),
(24105, 'Biedenkopf', 3018, 'HE', 82, 'DE', 50.91125000, 8.53016000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q552554'),
(24106, 'Biederitz', 3011, 'ST', 82, 'DE', 52.15000000, 11.71667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q551174'),
(24107, 'Bielefeld', 3017, 'NW', 82, 'DE', 52.03333000, 8.53333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q2112'),
(24108, 'Biendorf', 3007, 'MV', 82, 'DE', 54.07520000, 11.70127000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31912426'),
(24109, 'Bienenbüttel', 3008, 'NI', 82, 'DE', 53.14157000, 10.48679000, '2019-10-05 22:40:39', '2020-05-01 17:22:48', 1, 'Q503244'),
(24110, 'Biere', 3011, 'ST', 82, 'DE', 51.97431000, 11.65443000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q699129'),
(24111, 'Biesdorf', 3010, 'BE', 82, 'DE', 52.50906000, 13.55340000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q666141'),
(24112, 'Biesenthal', 3013, 'BB', 82, 'DE', 52.76616000, 13.64416000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q541642'),
(24113, 'Biessenhofen', 3009, 'BY', 82, 'DE', 47.83057000, 10.64022000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q531945'),
(24114, 'Bietigheim', 3006, 'BW', 82, 'DE', 48.90919000, 8.25202000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q531945'),
(24115, 'Bietigheim-Bissingen', 3006, 'BW', 82, 'DE', 48.94407000, 9.11755000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q7067'),
(24116, 'Bilderstoeckchen', 3017, 'NW', 82, 'DE', 50.96983000, 6.92997000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q5657'),
(24117, 'Billerbeck', 3017, 'NW', 82, 'DE', 51.97829000, 7.29261000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31913206'),
(24118, 'Billigheim', 3006, 'BW', 82, 'DE', 49.34861000, 9.25389000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q537729'),
(24119, 'Billigheim-Ingenheim', 3019, 'RP', 82, 'DE', 49.13667000, 8.09056000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q566068'),
(24120, 'Bilshausen', 3008, 'NI', 82, 'DE', 51.62974000, 10.15859000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q566419'),
(24121, 'Binau', 3006, 'BW', 82, 'DE', 49.36833000, 9.05806000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q537696'),
(24122, 'Bindlach', 3009, 'BY', 82, 'DE', 49.98167000, 11.61389000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q505002'),
(24123, 'Bingen', 3006, 'BW', 82, 'DE', 48.11127000, 9.27238000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q505002'),
(24124, 'Bingen am Rhein', 3019, 'RP', 82, 'DE', 49.96675000, 7.89920000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q285879'),
(24125, 'Binnen', 3008, 'NI', 82, 'DE', 52.61667000, 9.13333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q554015'),
(24126, 'Binsfeld', 3019, 'RP', 82, 'DE', 49.96667000, 6.71667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q569315'),
(24127, 'Binswangen', 3009, 'BY', 82, 'DE', 48.55798000, 10.64249000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q569315'),
(24128, 'Binzen', 3006, 'BW', 82, 'DE', 47.63333000, 7.61667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q62034'),
(24129, 'Bippen', 3008, 'NI', 82, 'DE', 52.58333000, 7.73333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q118139'),
(24130, 'Birenbach', 3006, 'BW', 82, 'DE', 48.74732000, 9.66115000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q80455'),
(24131, 'Birgte', 3017, 'NW', 82, 'DE', 52.24790000, 7.64833000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q865412'),
(24132, 'Birken-Honigsessen', 3019, 'RP', 82, 'DE', 50.81667000, 7.73333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q567715'),
(24133, 'Birkenau', 3018, 'HE', 82, 'DE', 49.56250000, 8.70694000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q512538'),
(24134, 'Birkenfeld', 3006, 'BW', 82, 'DE', 48.86667000, 8.63333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q512538'),
(24135, 'Birkenfeld', 3019, 'RP', 82, 'DE', 49.65245000, 7.16668000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q541850'),
(24136, 'Birkenfeld', 3009, 'BY', 82, 'DE', 49.85861000, 9.69556000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q17310554'),
(24137, 'Birkenheide', 3019, 'RP', 82, 'DE', 49.48139000, 8.26194000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q553983'),
(24138, 'Birkenwerder', 3013, 'BB', 82, 'DE', 52.68333000, 13.28333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q587077'),
(24139, 'Birlenbach', 3019, 'RP', 82, 'DE', 50.35815000, 8.00273000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q642839'),
(24140, 'Birresborn', 3019, 'RP', 82, 'DE', 50.18333000, 6.63333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q560493'),
(24141, 'Birstein', 3018, 'HE', 82, 'DE', 50.35000000, 9.30000000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q552289'),
(24142, 'Bischberg', 3009, 'BY', 82, 'DE', 49.91087000, 10.83212000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q530525'),
(24143, 'Bischbrunn', 3009, 'BY', 82, 'DE', 49.87028000, 9.48917000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q17122482'),
(24144, 'Bischoffen', 3018, 'HE', 82, 'DE', 50.70000000, 8.45000000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q623759'),
(24145, 'Bischofferode', 3015, 'TH', 82, 'DE', 51.49656000, 10.44396000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q866142'),
(24146, 'Bischofsgrün', 3009, 'BY', 82, 'DE', 50.05122000, 11.79997000, '2019-10-05 22:40:39', '2020-05-01 17:22:47', 1, 'Q253123'),
(24147, 'Bischofsheim', 3018, 'HE', 82, 'DE', 49.99389000, 8.36722000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q253123'),
(24148, 'Bischofsheim an der Rhön', 3009, 'BY', 82, 'DE', 50.40239000, 10.00751000, '2019-10-05 22:40:39', '2020-05-01 17:22:47', 1, 'Q253123'),
(24149, 'Bischofsmais', 3009, 'BY', 82, 'DE', 48.91796000, 13.08184000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q491426'),
(24150, 'Bischofswerda', 3021, 'SN', 82, 'DE', 51.12771000, 14.17974000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q81717'),
(24151, 'Bischofswiesen', 3009, 'BY', 82, 'DE', 47.63115000, 12.98305000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q256985'),
(24152, 'Bischweier', 3006, 'BW', 82, 'DE', 48.83766000, 8.28412000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q256985'),
(24153, 'Bisingen', 3006, 'BW', 82, 'DE', 48.31012000, 8.91738000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q538290'),
(24154, 'Bismark', 3011, 'ST', 82, 'DE', 52.66195000, 11.55638000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31914651'),
(24155, 'Bispingen', 3008, 'NI', 82, 'DE', 53.08312000, 9.99772000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q503255'),
(24156, 'Bissendorf', 3008, 'NI', 82, 'DE', 52.23333000, 8.16667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q569596'),
(24157, 'Bissingen', 3009, 'BY', 82, 'DE', 48.71694000, 10.61766000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q515410'),
(24158, 'Bissingen an der Teck', 3006, 'BW', 82, 'DE', 48.59915000, 9.49146000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q80496'),
(24159, 'Bitburg', 3019, 'RP', 82, 'DE', 49.96794000, 6.52734000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q258974'),
(24160, 'Bitterfeld-Wolfen', 3011, 'ST', 82, 'DE', 51.62355000, 12.32395000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q836932'),
(24161, 'Bitz', 3006, 'BW', 82, 'DE', 48.24400000, 9.09144000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q516090'),
(24162, 'Blaibach', 3009, 'BY', 82, 'DE', 49.16667000, 12.81667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q508744'),
(24163, 'Blaichach', 3009, 'BY', 82, 'DE', 47.54208000, 10.25846000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q513048'),
(24164, 'Blankenbach', 3009, 'BY', 82, 'DE', 50.06667000, 9.23333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q506590'),
(24165, 'Blankenburg', 3010, 'BE', 82, 'DE', 52.59293000, 13.45516000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q693582'),
(24166, 'Blankenburg', 3011, 'ST', 82, 'DE', 51.79025000, 10.95509000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q525960'),
(24167, 'Blankenfelde', 3010, 'BE', 82, 'DE', 52.61863000, 13.39057000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q702621'),
(24168, 'Blankenhain', 3015, 'TH', 82, 'DE', 50.85993000, 11.34390000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q539185'),
(24169, 'Blankenheim', 3017, 'NW', 82, 'DE', 50.43333000, 6.65000000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q539185'),
(24170, 'Blankenheim', 3011, 'ST', 82, 'DE', 51.50717000, 11.42878000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q539185'),
(24171, 'Blankenrath', 3019, 'RP', 82, 'DE', 50.03898000, 7.30214000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q539185'),
(24172, 'Blankensee', 3007, 'MV', 82, 'DE', 53.40390000, 13.26836000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31915899'),
(24173, 'Blaubeuren', 3006, 'BW', 82, 'DE', 48.41215000, 9.78427000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31915899'),
(24174, 'Blaufelden', 3006, 'BW', 82, 'DE', 49.29778000, 9.97389000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q83074'),
(24175, 'Bleckede', 3008, 'NI', 82, 'DE', 53.28972000, 10.73372000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q567445'),
(24176, 'Bleialf', 3019, 'RP', 82, 'DE', 50.23333000, 6.28333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q553361'),
(24177, 'Bleicherode', 3015, 'TH', 82, 'DE', 51.44026000, 10.57202000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q517311'),
(24178, 'Blekendorf', 3005, 'SH', 82, 'DE', 54.28211000, 10.65820000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q517311'),
(24179, 'Blender', 3008, 'NI', 82, 'DE', 52.91667000, 9.13333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q506791'),
(24180, 'Bliedersdorf', 3008, 'NI', 82, 'DE', 53.48333000, 9.56667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q505569'),
(24181, 'Bliesdorf', 3013, 'BB', 82, 'DE', 52.69298000, 14.15949000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q589608'),
(24182, 'Blieskastel', 3020, 'SL', 82, 'DE', 49.23724000, 7.25617000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q523709'),
(24183, 'Blindheim', 3009, 'BY', 82, 'DE', 48.63138000, 10.61992000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q514914'),
(24184, 'Blomberg', 3017, 'NW', 82, 'DE', 51.94331000, 9.09067000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31916122'),
(24185, 'Blomberg', 3008, 'NI', 82, 'DE', 53.57678000, 7.55824000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31916122'),
(24186, 'Blowatz', 3007, 'MV', 82, 'DE', 53.98972000, 11.53360000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31916167'),
(24187, 'Blumberg', 3006, 'BW', 82, 'DE', 47.84058000, 8.53329000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q82430'),
(24188, 'Bobbau', 3011, 'ST', 82, 'DE', 51.68747000, 12.27253000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q687880'),
(24189, 'Bobenheim-Roxheim', 3019, 'RP', 82, 'DE', 49.58750000, 8.35778000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q553997'),
(24190, 'Bobingen', 3009, 'BY', 82, 'DE', 48.27091000, 10.83390000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q254801'),
(24191, 'Bobitz', 3007, 'MV', 82, 'DE', 53.80450000, 11.35914000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q671920'),
(24192, 'Bocholt', 3017, 'NW', 82, 'DE', 51.83879000, 6.61531000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q33425275'),
(24193, 'Bochum', 3017, 'NW', 82, 'DE', 51.48165000, 7.21648000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q2103'),
(24194, 'Bochum-Hordel', 3017, 'NW', 82, 'DE', 51.50168000, 7.17560000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q2103'),
(24195, 'Bockau', 3021, 'SN', 82, 'DE', 50.54209000, 12.68639000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q57720'),
(24196, 'Bockelwitz', 3021, 'SN', 82, 'DE', 51.19942000, 12.95618000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q542047'),
(24197, 'Bockenau', 3019, 'RP', 82, 'DE', 49.83333000, 7.68333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q390399'),
(24198, 'Bockenem', 3008, 'NI', 82, 'DE', 52.00993000, 10.13197000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q503392'),
(24199, 'Bockenheim', 3019, 'RP', 82, 'DE', 49.60669000, 8.18486000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q22531'),
(24200, 'Bockhorn', 3008, 'NI', 82, 'DE', 53.40000000, 8.01667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q22531'),
(24201, 'Bockhorn', 3009, 'BY', 82, 'DE', 48.31471000, 11.98694000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q22531'),
(24202, 'Bockhorst', 3008, 'NI', 82, 'DE', 53.03296000, 7.57576000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q523189'),
(24203, 'Bodelshausen', 3006, 'BW', 82, 'DE', 48.38886000, 8.97703000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q507671'),
(24204, 'Bodenfelde', 3008, 'NI', 82, 'DE', 51.64044000, 9.55569000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q549815'),
(24205, 'Bodenheim', 3019, 'RP', 82, 'DE', 49.93528000, 8.32000000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q178532'),
(24206, 'Bodenkirchen', 3009, 'BY', 82, 'DE', 48.38333000, 12.38333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q261280'),
(24207, 'Bodenmais', 3009, 'BY', 82, 'DE', 49.06667000, 13.10000000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q513654'),
(24208, 'Bodenwerder', 3008, 'NI', 82, 'DE', 51.97156000, 9.51931000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q58566'),
(24209, 'Bodenwöhr', 3009, 'BY', 82, 'DE', 49.27082000, 12.30146000, '2019-10-05 22:40:39', '2020-05-01 17:22:47', 1, 'Q545325'),
(24210, 'Bodman-Ludwigshafen', 3006, 'BW', 82, 'DE', 47.81817000, 9.05540000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q62013'),
(24211, 'Bodnegg', 3006, 'BW', 82, 'DE', 47.71014000, 9.68841000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q533682'),
(24212, 'Bodolz', 3009, 'BY', 82, 'DE', 47.56667000, 9.66667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q552427'),
(24213, 'Boffzen', 3008, 'NI', 82, 'DE', 51.75000000, 9.38333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q555638'),
(24214, 'Bogen', 3009, 'BY', 82, 'DE', 48.91122000, 12.68955000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q555638'),
(24215, 'Bogenhausen', 3009, 'BY', 82, 'DE', 48.15221000, 11.61585000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q329707'),
(24216, 'Bohmte', 3008, 'NI', 82, 'DE', 52.36667000, 8.31667000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q555202'),
(24217, 'Bohnsdorf', 3010, 'BE', 82, 'DE', 52.39434000, 13.57339000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q562450'),
(24218, 'Boizenburg', 3007, 'MV', 82, 'DE', 53.38153000, 10.72375000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q160879'),
(24219, 'Bokel', 3008, 'NI', 82, 'DE', 53.39326000, 8.76803000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31917321'),
(24220, 'Bokholt-Hanredder', 3005, 'SH', 82, 'DE', 53.78808000, 9.73586000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31917321'),
(24221, 'Bolanden', 3019, 'RP', 82, 'DE', 49.63917000, 8.01194000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q565866'),
(24222, 'Bolheim', 3006, 'BW', 82, 'DE', 48.63141000, 10.14995000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q565866'),
(24223, 'Boll', 3006, 'BW', 82, 'DE', 48.64357000, 9.61295000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q565866'),
(24224, 'Bollendorf', 3019, 'RP', 82, 'DE', 49.85268000, 6.35795000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q544146'),
(24225, 'Bollingstedt', 3005, 'SH', 82, 'DE', 54.59229000, 9.41738000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q557182'),
(24226, 'Bollschweil', 3006, 'BW', 82, 'DE', 47.92143000, 7.78986000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q632838'),
(24227, 'Bolsterlang', 3009, 'BY', 82, 'DE', 47.46667000, 10.23333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q203754'),
(24228, 'Bomlitz', 3008, 'NI', 82, 'DE', 52.90000000, 9.65000000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q686853'),
(24229, 'Bondorf', 3006, 'BW', 82, 'DE', 48.52064000, 8.83704000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q200744'),
(24230, 'Bonefeld', 3019, 'RP', 82, 'DE', 50.52388000, 7.48932000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q565769'),
(24231, 'Bonn', 3017, 'NW', 82, 'DE', 50.73438000, 7.09549000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q586'),
(24232, 'Bonndorf', 3006, 'BW', 82, 'DE', 47.81863000, 8.34139000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q178703'),
(24233, 'Bonstetten', 3009, 'BY', 82, 'DE', 48.44002000, 10.70532000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q178703'),
(24234, 'Boos', 3009, 'BY', 82, 'DE', 48.07521000, 10.19523000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q178703'),
(24235, 'Boostedt', 3005, 'SH', 82, 'DE', 54.01667000, 10.03333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q554107'),
(24236, 'Bopfingen', 3006, 'BW', 82, 'DE', 48.85847000, 10.35417000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q628178'),
(24237, 'Boppard', 3019, 'RP', 82, 'DE', 50.23085000, 7.58992000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q521148'),
(24238, 'Bordelum', 3005, 'SH', 82, 'DE', 54.63333000, 8.93333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q573015'),
(24239, 'Bordesholm', 3005, 'SH', 82, 'DE', 54.17611000, 10.03146000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q570362'),
(24240, 'Borgentreich', 3017, 'NW', 82, 'DE', 51.56917000, 9.24113000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q241978'),
(24241, 'Borgfelde', 3016, 'HH', 82, 'DE', 53.55475000, 10.03447000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q1564'),
(24242, 'Borgholzhausen', 3017, 'NW', 82, 'DE', 52.10343000, 8.30211000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q225794'),
(24243, 'Borgstedt', 3005, 'SH', 82, 'DE', 54.33268000, 9.70954000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q225794'),
(24244, 'Borken', 3017, 'NW', 82, 'DE', 51.84382000, 6.85774000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q225794'),
(24245, 'Borken', 3018, 'HE', 82, 'DE', 51.04501000, 9.28440000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q565488'),
(24246, 'Borkheide', 3013, 'BB', 82, 'DE', 52.21667000, 12.85000000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q624241'),
(24247, 'Borkum', 3008, 'NI', 82, 'DE', 53.58094000, 6.69153000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q624241'),
(24248, 'Borkwalde', 3013, 'BB', 82, 'DE', 52.25000000, 12.83333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q623947'),
(24249, 'Born', 3007, 'MV', 82, 'DE', 54.38536000, 12.53051000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q623947'),
(24250, 'Borna', 3021, 'SN', 82, 'DE', 51.12416000, 12.49639000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q623947'),
(24251, 'Borne', 3011, 'ST', 82, 'DE', 51.94881000, 11.55865000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q623947'),
(24252, 'Bornheim', 3019, 'RP', 82, 'DE', 49.22250000, 8.16333000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q508870'),
(24253, 'Bornheim', 3017, 'NW', 82, 'DE', 50.76313000, 6.99089000, '2019-10-05 22:40:39', '2019-10-05 22:40:39', 1, 'Q31918506'),
(24254, 'Bornhöved', 3005, 'SH', 82, 'DE', 54.06667000, 10.23333000, '2019-10-05 22:40:40', '2020-05-01 17:22:49', 1, 'Q520795'),
(24255, 'Bornich', 3019, 'RP', 82, 'DE', 50.12687000, 7.76580000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q627780'),
(24256, 'Borrentin', 3007, 'MV', 82, 'DE', 53.80968000, 12.96718000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q667467'),
(24257, 'Borsdorf', 3021, 'SN', 82, 'DE', 51.35000000, 12.53333000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q10745'),
(24258, 'Borstel', 3008, 'NI', 82, 'DE', 52.67034000, 8.96896000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q10745'),
(24259, 'Borstel-Hohenraden', 3005, 'SH', 82, 'DE', 53.68333000, 9.81667000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q574233'),
(24260, 'Borstendorf', 3021, 'SN', 82, 'DE', 50.77323000, 13.17918000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q57729'),
(24261, 'Bosau', 3005, 'SH', 82, 'DE', 54.09787000, 10.43570000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q554043'),
(24262, 'Bothel', 3008, 'NI', 82, 'DE', 53.06667000, 9.50000000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q554043'),
(24263, 'Bottrop', 3017, 'NW', 82, 'DE', 51.52392000, 6.92850000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q3069'),
(24264, 'Bous', 3020, 'SL', 82, 'DE', 49.27732000, 6.80131000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q3069'),
(24265, 'Bovenau', 3005, 'SH', 82, 'DE', 54.33333000, 9.83333000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q641638'),
(24266, 'Bovenden', 3008, 'NI', 82, 'DE', 51.58836000, 9.92220000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q621193'),
(24267, 'Boxberg', 3021, 'SN', 82, 'DE', 51.40373000, 14.57598000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q544833'),
(24268, 'Boxberg', 3006, 'BW', 82, 'DE', 49.47965000, 9.64006000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q544833'),
(24269, 'Brachbach', 3019, 'RP', 82, 'DE', 50.81667000, 7.93333000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q553698'),
(24270, 'Brackel', 3008, 'NI', 82, 'DE', 53.29974000, 10.04860000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q553698'),
(24271, 'Brackenheim', 3006, 'BW', 82, 'DE', 49.07787000, 9.06601000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q53751'),
(24272, 'Brahmenau', 3015, 'TH', 82, 'DE', 50.92359000, 12.15886000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q539003'),
(24273, 'Brake (Unterweser)', 3008, 'NI', 82, 'DE', 53.33333000, 8.48333000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q539003'),
(24274, 'Brakel', 3017, 'NW', 82, 'DE', 51.71750000, 9.18596000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q31919909'),
(24275, 'Bramsche', 3008, 'NI', 82, 'DE', 52.40881000, 7.97288000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q16109'),
(24276, 'Bramstedt', 3008, 'NI', 82, 'DE', 53.36603000, 8.69005000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q16109'),
(24277, 'Brand', 3009, 'BY', 82, 'DE', 49.95972000, 11.91072000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q16109'),
(24278, 'Brand-Erbisdorf', 3021, 'SN', 82, 'DE', 50.86643000, 13.32285000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q71159'),
(24279, 'Brande-Hörnerkirchen', 3005, 'SH', 82, 'DE', 53.85000000, 9.71667000, '2019-10-05 22:40:40', '2020-05-01 17:22:49', 1, 'Q71159'),
(24280, 'Brandenburg an der Havel', 3013, 'BB', 82, 'DE', 52.41667000, 12.55000000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q3931'),
(24281, 'Brandis', 3021, 'SN', 82, 'DE', 51.33597000, 12.61024000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q3931'),
(24282, 'Brandshagen', 3007, 'MV', 82, 'DE', 54.23945000, 13.16917000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q651220'),
(24283, 'Brannenburg', 3009, 'BY', 82, 'DE', 47.73996000, 12.09166000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q533000'),
(24284, 'Braschwitz', 3011, 'ST', 82, 'DE', 51.52284000, 12.05867000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q689924'),
(24285, 'Braubach', 3019, 'RP', 82, 'DE', 50.27360000, 7.64508000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q509038'),
(24286, 'Brauneberg', 3019, 'RP', 82, 'DE', 49.90583000, 6.98127000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q567156'),
(24287, 'Braunfels', 3018, 'HE', 82, 'DE', 50.51545000, 8.38918000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q525612'),
(24288, 'Braunlage', 3008, 'NI', 82, 'DE', 51.72651000, 10.61090000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q522992'),
(24289, 'Braunsbach', 3006, 'BW', 82, 'DE', 49.19917000, 9.79056000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q80995'),
(24290, 'Braunsbedra', 3011, 'ST', 82, 'DE', 51.28602000, 11.88987000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q519640'),
(24291, 'Braunschweig', 3008, 'NI', 82, 'DE', 52.26594000, 10.52673000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q2773'),
(24292, 'Breckerfeld', 3017, 'NW', 82, 'DE', 51.25926000, 7.46807000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q11323'),
(24293, 'Breddin', 3013, 'BB', 82, 'DE', 52.88560000, 12.22366000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q625070'),
(24294, 'Breddorf', 3008, 'NI', 82, 'DE', 53.29262000, 9.08089000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q625070'),
(24295, 'Bredenbek', 3005, 'SH', 82, 'DE', 54.32247000, 9.87293000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q641700'),
(24296, 'Bredstedt', 3005, 'SH', 82, 'DE', 54.61868000, 8.96441000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q20753'),
(24297, 'Breese', 3013, 'BB', 82, 'DE', 53.00000000, 11.80000000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q20753'),
(24298, 'Brehme', 3015, 'TH', 82, 'DE', 51.49421000, 10.35908000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q20753'),
(24299, 'Brehna', 3011, 'ST', 82, 'DE', 51.55790000, 12.21276000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q119311'),
(24300, 'Breidenbach', 3018, 'HE', 82, 'DE', 50.88734000, 8.45748000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q564579'),
(24301, 'Breiholz', 3005, 'SH', 82, 'DE', 54.20554000, 9.52371000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q564579'),
(24302, 'Breisach am Rhein', 3006, 'BW', 82, 'DE', 48.03279000, 7.58294000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q489911'),
(24303, 'Breitbrunn', 3009, 'BY', 82, 'DE', 48.04320000, 12.15431000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q489911'),
(24304, 'Breitenbach', 3018, 'HE', 82, 'DE', 50.95655000, 9.78285000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q489911'),
(24305, 'Breitenbach', 3015, 'TH', 82, 'DE', 50.68895000, 10.49148000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q31920973'),
(24306, 'Breitenberg', 3009, 'BY', 82, 'DE', 48.70439000, 13.79333000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q31920973'),
(24307, 'Breitenbrunn', 3009, 'BY', 82, 'DE', 49.36388000, 12.02013000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q31921016'),
(24308, 'Breitenbrunn', 3021, 'SN', 82, 'DE', 50.47553000, 12.76649000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q57731'),
(24309, 'Breitenburg', 3005, 'SH', 82, 'DE', 53.90572000, 9.57041000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q635210'),
(24310, 'Breitenfelde', 3005, 'SH', 82, 'DE', 53.60592000, 10.63339000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q632515'),
(24311, 'Breitengüßbach', 3009, 'BY', 82, 'DE', 49.97209000, 10.88591000, '2019-10-05 22:40:40', '2020-05-01 17:22:47', 1, 'Q529392'),
(24312, 'Breitenthal', 3009, 'BY', 82, 'DE', 48.23782000, 10.29951000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q529392'),
(24313, 'Breitenworbis', 3015, 'TH', 82, 'DE', 51.41267000, 10.42820000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q544476'),
(24314, 'Breitnau', 3006, 'BW', 82, 'DE', 47.93333000, 8.08333000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q534202'),
(24315, 'Breitscheid', 3018, 'HE', 82, 'DE', 50.68507000, 8.19120000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q622253'),
(24316, 'Breitscheidt', 3019, 'RP', 82, 'DE', 50.75000000, 7.65000000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q563914'),
(24317, 'Breitungen', 3015, 'TH', 82, 'DE', 50.76355000, 10.32724000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q312658'),
(24318, 'Brekendorf', 3005, 'SH', 82, 'DE', 54.42120000, 9.63350000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q312658'),
(24319, 'Breklum', 3005, 'SH', 82, 'DE', 54.60000000, 8.98333000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q559468'),
(24320, 'Bremen', 3014, 'HB', 82, 'DE', 53.07516000, 8.80777000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q24879'),
(24321, 'Bremerhaven', 3014, 'HB', 82, 'DE', 53.55021000, 8.57673000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q2706'),
(24322, 'Bremervörde', 3008, 'NI', 82, 'DE', 53.48458000, 9.14306000, '2019-10-05 22:40:40', '2020-05-01 17:22:48', 1, 'Q501875'),
(24323, 'Brennberg', 3009, 'BY', 82, 'DE', 49.06667000, 12.40000000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q489160'),
(24324, 'Brensbach', 3018, 'HE', 82, 'DE', 49.77389000, 8.88444000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q640603'),
(24325, 'Bretten', 3006, 'BW', 82, 'DE', 49.03685000, 8.70745000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q495428'),
(24326, 'Bretzenheim', 3019, 'RP', 82, 'DE', 49.87796000, 7.89653000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q567288'),
(24327, 'Bretzfeld', 3006, 'BW', 82, 'DE', 49.17944000, 9.43833000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q567288'),
(24328, 'Breuna', 3018, 'HE', 82, 'DE', 51.41575000, 9.18500000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q567288'),
(24329, 'Brey', 3019, 'RP', 82, 'DE', 50.27253000, 7.62645000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q678831'),
(24330, 'Briedel', 3019, 'RP', 82, 'DE', 50.01667000, 7.15000000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q565192'),
(24331, 'Brieselang', 3013, 'BB', 82, 'DE', 52.58333000, 13.00000000, '2019-10-05 22:40:40', '2019-10-05 22:40:40', 1, 'Q582671');

