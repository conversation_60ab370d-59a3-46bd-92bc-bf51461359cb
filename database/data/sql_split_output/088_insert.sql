INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(44302, 'Montpont-en-Bresse', 4825, 'B<PERSON>', 75, 'FR', 46.55630000, 5.16459000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, 'Q839877'),
(44303, '<PERSON><PERSON><PERSON>', 4799, 'OCC', 75, 'FR', 43.64477000, 1.52384000, '2019-10-05 22:48:32', '2020-05-01 17:22:46', 1, 'Q839877'),
(44304, 'Montredon', 4812, 'PAC', 75, 'FR', 43.24016000, 5.36629000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, 'Q839877'),
(44305, 'Montredon-<PERSON><PERSON><PERSON><PERSON>', 4799, '<PERSON><PERSON>', 75, 'FR', 43.71723000, 2.32454000, '2019-10-05 22:48:32', '2020-05-01 17:22:46', 1, 'Q839877'),
(44306, 'Montrem', 4795, 'NAQ', 75, 'FR', 45.13417000, 0.59029000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, '********'),
(44307, 'Montreuil', 4796, 'IDF', 75, 'FR', 48.86415000, 2.44322000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, 'Q193370'),
(44308, 'Montreuil', 4828, 'HDF', 75, 'FR', 50.46374000, 1.76348000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, 'Q193370'),
(44309, 'Montreuil-Bellay', 4802, 'PDL', 75, 'FR', 47.13106000, -0.15209000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, '*********'),
(44310, 'Montreuil-aux-Lions', 4828, 'HDF', 75, 'FR', 49.02124000, 3.19543000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, '*********'),
(44311, 'Montreuil-le-Gast', 4807, 'BRE', 75, 'FR', 48.24647000, -1.72498000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, '*********'),
(44312, 'Montreuil-sous-Pérouse', 4807, 'BRE', 75, 'FR', 48.15176000, -1.23946000, '2019-10-05 22:48:32', '2020-05-01 17:22:44', 1, '********'),
(44313, 'Montreuil-sur-Ille', 4807, 'BRE', 75, 'FR', 48.30788000, -1.66880000, '2019-10-05 22:48:32', '2019-10-05 22:48:32', 1, '*********'),
(44314, 'Montreux-Château', 4825, 'BFC', 75, 'FR', 47.61003000, 6.99923000, '2019-10-05 22:48:32', '2020-05-01 17:22:44', 1, '*********'),
(44315, 'Montrevault', 4802, 'PDL', 75, 'FR', 47.25965000, -1.04679000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44316, 'Montrevel-en-Bresse', 4798, 'ARA', 75, 'FR', 46.33527000, 5.12269000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44317, 'Montrichard', 4818, 'CVL', 75, 'FR', 47.34310000, 1.18653000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q633743'),
(44318, 'Montricoux', 4799, 'OCC', 75, 'FR', 44.07589000, 1.61946000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q633743'),
(44319, 'Montrodat', 4799, 'OCC', 75, 'FR', 44.55038000, 3.32929000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q633743'),
(44320, 'Montrond-les-Bains', 4798, 'ARA', 75, 'FR', 45.64374000, 4.23752000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q633743'),
(44321, 'Montrottier', 4798, 'ARA', 75, 'FR', 45.79010000, 4.46660000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q633743'),
(44322, 'Montrouge', 4796, 'IDF', 75, 'FR', 48.81620000, 2.31393000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q241021'),
(44323, 'Montry', 4796, 'IDF', 75, 'FR', 48.88409000, 2.82915000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q241021'),
(44324, 'Montréal', 4799, 'OCC', 75, 'FR', 43.19980000, 2.14122000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, 'Q241021'),
(44325, 'Montréal-la-Cluse', 4798, 'ARA', 75, 'FR', 46.18333000, 5.58333000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q271354'),
(44326, 'Montréjeau', 4799, 'OCC', 75, 'FR', 43.08555000, 0.56470000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, 'Q508546'),
(44327, 'Monts', 4818, 'CVL', 75, 'FR', 47.27723000, 0.62473000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q508546'),
(44328, 'Montsoult', 4796, 'IDF', 75, 'FR', 49.06942000, 2.31966000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q273098'),
(44329, 'Montségur-sur-Lauzon', 4798, 'ARA', 75, 'FR', 44.36098000, 4.85468000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q468233'),
(44330, 'Montsûrs', 4802, 'PDL', 75, 'FR', 48.13604000, -0.55413000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, '*********'),
(44331, 'Montussan', 4795, 'NAQ', 75, 'FR', 44.88057000, -0.42181000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44332, 'Montélier', 4798, 'ARA', 75, 'FR', 44.93441000, 5.03126000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q936509'),
(44333, 'Montélimar', 4798, 'ARA', 75, 'FR', 44.55468000, 4.75469000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q234270'),
(44334, 'Montéléger', 4798, 'ARA', 75, 'FR', 44.85296000, 4.93598000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q234270'),
(44335, 'Montévrain', 4796, 'IDF', 75, 'FR', 48.87415000, 2.75114000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q385659'),
(44336, 'Monéteau', 4825, 'BFC', 75, 'FR', 47.84923000, 3.58178000, '2019-10-05 22:48:33', '2020-05-01 17:22:44', 1, 'Q385659'),
(44337, 'Moosch', 4820, 'GES', 75, 'FR', 47.86035000, 7.04870000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q385659'),
(44338, 'Morainvilliers', 4796, 'IDF', 75, 'FR', 48.92902000, 1.93621000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q385659'),
(44339, 'Morancez', 4818, 'CVL', 75, 'FR', 48.40051000, 1.49388000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q385659'),
(44340, 'Morancé', 4798, 'ARA', 75, 'FR', 45.89815000, 4.70051000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q385659'),
(44341, 'Morangis', 4796, 'IDF', 75, 'FR', 48.70383000, 2.33908000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q385659'),
(44342, 'Morannes', 4802, 'PDL', 75, 'FR', 47.74364000, -0.41604000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44343, 'Morbecque', 4828, 'HDF', 75, 'FR', 50.69365000, 2.51787000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44344, 'Morbier', 4825, 'BFC', 75, 'FR', 46.53693000, 6.01532000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44345, 'Morbihan', 4807, 'BRE', 75, 'FR', 47.84765000, -2.77760000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q12642'),
(44346, 'Morcenx', 4795, 'NAQ', 75, 'FR', 44.03536000, -0.91375000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44347, 'Mordelles', 4807, 'BRE', 75, 'FR', 48.07571000, -1.84286000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44348, 'Morestel', 4798, 'ARA', 75, 'FR', 45.67900000, 5.46479000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44349, 'Moret-sur-Loing', 4796, 'IDF', 75, 'FR', 48.37239000, 2.81713000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q839537'),
(44350, 'Moreuil', 4828, 'HDF', 75, 'FR', 49.77457000, 2.48273000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q115426'),
(44351, 'Morez', 4825, 'BFC', 75, 'FR', 46.52540000, 6.02589000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q115426'),
(44352, 'Morhange', 4820, 'GES', 75, 'FR', 48.92359000, 6.64163000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q115426'),
(44353, 'Morienval', 4828, 'HDF', 75, 'FR', 49.29770000, 2.92078000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q115426'),
(44354, 'Morigny-Champigny', 4796, 'IDF', 75, 'FR', 48.44685000, 2.18351000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q594652'),
(44355, 'Morières-lès-Avignon', 4812, 'PAC', 75, 'FR', 43.94030000, 4.90110000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, 'Q35733807'),
(44356, 'Morlaas', 4795, 'NAQ', 75, 'FR', 43.35000000, -0.26667000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q197850'),
(44357, 'Morlaix', 4807, 'BRE', 75, 'FR', 48.57742000, -3.82788000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q202368'),
(44358, 'Mormant', 4796, 'IDF', 75, 'FR', 48.60901000, 2.89025000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q202368'),
(44359, 'Mormoiron', 4812, 'PAC', 75, 'FR', 44.06810000, 5.18312000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q472409'),
(44360, 'Mornac', 4795, 'NAQ', 75, 'FR', 45.68333000, 0.26667000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q472409'),
(44361, 'Mornant', 4798, 'ARA', 75, 'FR', 45.61885000, 4.67231000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q1389013'),
(44362, 'Mornas', 4812, 'PAC', 75, 'FR', 44.20242000, 4.72763000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q470952'),
(44363, 'Morosaglia', 4806, '20R', 75, 'FR', 42.43511000, 9.30009000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q270675'),
(44364, 'Morre', 4825, 'BFC', 75, 'FR', 47.22595000, 6.07512000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q270675'),
(44365, 'Morsang-sur-Orge', 4796, 'IDF', 75, 'FR', 48.66181000, 2.35338000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q990453'),
(44366, 'Morsbach', 4820, 'GES', 75, 'FR', 49.16806000, 6.87190000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q990453'),
(44367, 'Morschwiller-le-Bas', 4820, 'GES', 75, 'FR', 47.73615000, 7.26954000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q148741'),
(44368, 'Mortagne-au-Perche', 4804, 'NOR', 75, 'FR', 48.52018000, 0.54734000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q476288'),
(44369, 'Mortagne-du-Nord', 4828, 'HDF', 75, 'FR', 50.50352000, 3.45352000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q682171'),
(44370, 'Mortagne-sur-Gironde', 4795, 'NAQ', 75, 'FR', 45.48139000, -0.78702000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44371, 'Mortagne-sur-Sèvre', 4802, 'PDL', 75, 'FR', 46.99203000, -0.94738000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, '********'),
(44372, 'Mortain', 4804, 'NOR', 75, 'FR', 48.64782000, -0.94055000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q325187'),
(44373, 'Mortcerf', 4796, 'IDF', 75, 'FR', 48.78879000, 2.91692000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q580928'),
(44374, 'Morteau', 4825, 'BFC', 75, 'FR', 47.05784000, 6.60716000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q200832'),
(44375, 'Mortrée', 4804, 'NOR', 75, 'FR', 48.63899000, 0.07909000, '2019-10-05 22:48:33', '2020-05-01 17:22:45', 1, 'Q200832'),
(44376, 'Morvillars', 4825, 'BFC', 75, 'FR', 47.54854000, 6.93450000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q200832'),
(44377, 'Morzine', 4798, 'ARA', 75, 'FR', 46.18149000, 6.70664000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q472179'),
(44378, 'Moréac', 4807, 'BRE', 75, 'FR', 47.91967000, -2.81934000, '2019-10-05 22:48:33', '2020-05-01 17:22:44', 1, '*********'),
(44379, 'Morée', 4818, 'CVL', 75, 'FR', 47.90397000, 1.23421000, '2019-10-05 22:48:33', '2020-05-01 17:22:44', 1, '*********'),
(44380, 'Moselle', 4820, 'GES', 75, 'FR', 49.04177000, 6.58355000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q12652'),
(44381, 'Mouans-Sartoux', 4812, 'PAC', 75, 'FR', 43.62101000, 6.97139000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q322723'),
(44382, 'Mouchamps', 4802, 'PDL', 75, 'FR', 46.78131000, -1.06179000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44383, 'Mouchard', 4825, 'BFC', 75, 'FR', 46.97259000, 5.79626000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44384, 'Mouchin', 4828, 'HDF', 75, 'FR', 50.51639000, 3.29627000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44385, 'Mouen', 4804, 'NOR', 75, 'FR', 49.14696000, -0.48366000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44386, 'Mougins', 4812, 'PAC', 75, 'FR', 43.60068000, 6.99523000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q220673'),
(44387, 'Mougon', 4795, 'NAQ', 75, 'FR', 46.29561000, -0.28659000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44388, 'Mouguerre', 4795, 'NAQ', 75, 'FR', 43.46795000, -1.41824000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44389, 'Mouilleron-en-Pareds', 4802, 'PDL', 75, 'FR', 46.67630000, -0.84940000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44390, 'Mouilleron-le-Captif', 4802, 'PDL', 75, 'FR', 46.71898000, -1.45463000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44391, 'Moulay', 4802, 'PDL', 75, 'FR', 48.27245000, -0.62734000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44392, 'Mouleydier', 4795, 'NAQ', 75, 'FR', 44.85572000, 0.59759000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q625196'),
(44393, 'Mouliets-et-Villemartin', 4795, 'NAQ', 75, 'FR', 44.83333000, -0.01667000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q625196'),
(44394, 'Moulins', 4798, 'ARA', 75, 'FR', 46.56459000, 3.33243000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q184883'),
(44395, 'Moulins-Engilbert', 4825, 'BFC', 75, 'FR', 46.98821000, 3.81084000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q540240'),
(44396, 'Moulins-lès-Metz', 4820, 'GES', 75, 'FR', 49.10434000, 6.10832000, '2019-10-05 22:48:33', '2020-05-01 17:22:45', 1, 'Q22396'),
(44397, 'Moulis-en-Médoc', 4795, 'NAQ', 75, 'FR', 45.05938000, -0.77033000, '2019-10-05 22:48:33', '2020-05-01 17:22:45', 1, '*********'),
(44398, 'Moult', 4804, 'NOR', 75, 'FR', 49.11494000, -0.16472000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44399, 'Mourenx', 4795, 'NAQ', 75, 'FR', 43.38333000, -0.60000000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q321209'),
(44400, 'Mouret', 4812, 'PAC', 75, 'FR', 43.36126000, 5.43006000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q321209'),
(44401, 'Mouriès', 4812, 'PAC', 75, 'FR', 43.68997000, 4.87089000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, 'Q829392'),
(44402, 'Mourmelon-le-Grand', 4820, 'GES', 75, 'FR', 49.13256000, 4.36420000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q829392'),
(44403, 'Mouroux', 4796, 'IDF', 75, 'FR', 48.82263000, 3.03879000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q829392'),
(44404, 'Mours', 4796, 'IDF', 75, 'FR', 49.13077000, 2.26761000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q1457775'),
(44405, 'Mours-Saint-Eusèbe', 4798, 'ARA', 75, 'FR', 45.06513000, 5.05776000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q1457775'),
(44406, 'Moussac', 4799, 'OCC', 75, 'FR', 43.98119000, 4.22647000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q1457775'),
(44407, 'Moussan', 4799, 'OCC', 75, 'FR', 43.23091000, 2.95000000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q1457775'),
(44408, 'Moussy-le-Neuf', 4796, 'IDF', 75, 'FR', 49.06427000, 2.60252000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q900249'),
(44409, 'Moussy-le-Vieux', 4796, 'IDF', 75, 'FR', 49.04712000, 2.62493000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q900249'),
(44410, 'Moustoir-Ac', 4807, 'BRE', 75, 'FR', 47.85461000, -2.83481000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44411, 'Mouthiers-sur-Boëme', 4795, 'NAQ', 75, 'FR', 45.55000000, 0.11667000, '2019-10-05 22:48:33', '2020-05-01 17:22:45', 1, '********'),
(44412, 'Moutiers', 4820, 'GES', 75, 'FR', 49.23302000, 5.96553000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44413, 'Moutiers-les-Mauxfaits', 4802, 'PDL', 75, 'FR', 46.48333000, -1.41667000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44414, 'Mouvaux', 4828, 'HDF', 75, 'FR', 50.69944000, 3.13429000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q638560'),
(44415, 'Mouxy', 4798, 'ARA', 75, 'FR', 45.68399000, 5.93538000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q736601'),
(44416, 'Mouy', 4828, 'HDF', 75, 'FR', 49.31535000, 2.31954000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q750702'),
(44417, 'Mouzeil', 4802, 'PDL', 75, 'FR', 47.44738000, -1.34786000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44418, 'Mouzillon', 4802, 'PDL', 75, 'FR', 47.14096000, -1.28191000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44419, 'Mouzon', 4820, 'GES', 75, 'FR', 49.60706000, 5.07569000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44420, 'Moyaux', 4804, 'NOR', 75, 'FR', 49.19511000, 0.35603000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44421, 'Moye', 4798, 'ARA', 75, 'FR', 45.87566000, 5.91289000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '*********'),
(44422, 'Moyenmoutier', 4820, 'GES', 75, 'FR', 48.37730000, 6.90047000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q211815'),
(44423, 'Moyeuvre-Grande', 4820, 'GES', 75, 'FR', 49.25294000, 6.04467000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q22020'),
(44424, 'Moyrazès', 4799, 'OCC', 75, 'FR', 44.34204000, 2.43933000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, 'Q22020'),
(44425, 'Mozac', 4798, 'ARA', 75, 'FR', 45.89393000, 3.09512000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q673842'),
(44426, 'Mozé-sur-Louet', 4802, 'PDL', 75, 'FR', 47.35856000, -0.55295000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, '*********'),
(44427, 'Moëlan-sur-Mer', 4807, 'BRE', 75, 'FR', 47.81418000, -3.62892000, '2019-10-05 22:48:33', '2020-05-01 17:22:44', 1, 'Q216405'),
(44428, 'Moûtiers', 4798, 'ARA', 75, 'FR', 45.48459000, 6.53146000, '2019-10-05 22:48:33', '2020-05-01 17:22:43', 1, 'Q818032'),
(44429, 'Mudaison', 4799, 'OCC', 75, 'FR', 43.63333000, 4.03333000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q818032'),
(44430, 'Mugron', 4795, 'NAQ', 75, 'FR', 43.74952000, -0.75179000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q187356'),
(44431, 'Muides-sur-Loire', 4818, 'CVL', 75, 'FR', 47.66958000, 1.52694000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q596329'),
(44432, 'Muizon', 4820, 'GES', 75, 'FR', 49.27499000, 3.89083000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q596329'),
(44433, 'Mulhouse', 4820, 'GES', 75, 'FR', 47.75205000, 7.32866000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q79815'),
(44434, 'Mulsanne', 4802, 'PDL', 75, 'FR', 47.91172000, 0.24938000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q79815'),
(44435, 'Munchhouse', 4820, 'GES', 75, 'FR', 47.86930000, 7.45233000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q79815'),
(44436, 'Mundolsheim', 4820, 'GES', 75, 'FR', 48.64215000, 7.71378000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q22870'),
(44437, 'Munster', 4820, 'GES', 75, 'FR', 48.04048000, 7.13933000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q22870'),
(44438, 'Mur-de-Sologne', 4818, 'CVL', 75, 'FR', 47.41239000, 1.60832000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44439, 'Murat', 4798, 'ARA', 75, 'FR', 45.11011000, 2.86859000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, '********'),
(44440, 'Muret', 4799, 'OCC', 75, 'FR', 43.46027000, 1.32571000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q207593'),
(44441, 'Muron', 4795, 'NAQ', 75, 'FR', 46.03444000, -0.82867000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q35735815'),
(44442, 'Murviel-lès-Béziers', 4799, 'OCC', 75, 'FR', 43.43333000, 3.13333000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, 'Q239986'),
(44443, 'Murviel-lès-Montpellier', 4799, 'OCC', 75, 'FR', 43.60528000, 3.73750000, '2019-10-05 22:48:33', '2020-05-01 17:22:46', 1, 'Q239986'),
(44444, 'Mus', 4799, 'OCC', 75, 'FR', 43.73919000, 4.20257000, '2019-10-05 22:48:33', '2019-10-05 22:48:33', 1, 'Q778685'),
(44445, 'Mussidan', 4795, 'NAQ', 75, 'FR', 45.03542000, 0.36290000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q494972'),
(44446, 'Mussig', 4820, 'GES', 75, 'FR', 48.22993000, 7.51963000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q494972'),
(44447, 'Mussy-sur-Seine', 4820, 'GES', 75, 'FR', 47.97791000, 4.49743000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q740144'),
(44448, 'Muttersholtz', 4820, 'GES', 75, 'FR', 48.26761000, 7.53567000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q21494'),
(44449, 'Mutzig', 4820, 'GES', 75, 'FR', 48.53974000, 7.45594000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q22390'),
(44450, 'Muzillac', 4807, 'BRE', 75, 'FR', 47.55451000, -2.48041000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q35736025'),
(44451, 'Mâcon', 4825, 'BFC', 75, 'FR', 46.31407000, 4.82823000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q174247'),
(44452, 'Mâcot-la-Plagne', 4798, 'ARA', 75, 'FR', 45.55000000, 6.66667000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q1217251'),
(44453, 'Mèze', 4799, 'OCC', 75, 'FR', 43.42504000, 3.60590000, '2019-10-05 22:48:34', '2020-05-01 17:22:46', 1, 'Q385616'),
(44454, 'Méaudre', 4798, 'ARA', 75, 'FR', 45.13020000, 5.52853000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q1151420'),
(44455, 'Méaulte', 4828, 'HDF', 75, 'FR', 49.98167000, 2.66121000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q397415'),
(44456, 'Mécleuves', 4820, 'GES', 75, 'FR', 49.04429000, 6.27051000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q21929'),
(44457, 'Médan', 4796, 'IDF', 75, 'FR', 48.95539000, 1.99494000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q21929'),
(44458, 'Médis', 4795, 'NAQ', 75, 'FR', 45.64337000, -0.96298000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q35736453'),
(44459, 'Médréac', 4807, 'BRE', 75, 'FR', 48.26703000, -2.06618000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q35736461'),
(44460, 'Mées', 4795, 'NAQ', 75, 'FR', 43.70452000, -1.10952000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q35736476'),
(44461, 'Mélisey', 4825, 'BFC', 75, 'FR', 47.75229000, 6.58014000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q35736476'),
(44462, 'Ménerbes', 4812, 'PAC', 75, 'FR', 43.83242000, 5.20597000, '2019-10-05 22:48:34', '2020-05-01 17:22:46', 1, 'Q470889'),
(44463, 'Ménesplet', 4795, 'NAQ', 75, 'FR', 45.01667000, 0.11667000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q1078115'),
(44464, 'Ménestreau-en-Villette', 4818, 'CVL', 75, 'FR', 47.69962000, 2.02333000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q1078115'),
(44465, 'Ménilles', 4804, 'NOR', 75, 'FR', 49.03333000, 1.36667000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q1073797'),
(44466, 'Ménéac', 4807, 'BRE', 75, 'FR', 48.13972000, -2.46134000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q35736543'),
(44467, 'Ménétrol', 4798, 'ARA', 75, 'FR', 45.87155000, 3.12422000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q35736543'),
(44468, 'Méounes-lès-Montrieux', 4812, 'PAC', 75, 'FR', 43.28102000, 5.96986000, '2019-10-05 22:48:34', '2020-05-01 17:22:46', 1, 'Q35736543'),
(44469, 'Méreau', 4818, 'CVL', 75, 'FR', 47.16295000, 2.05086000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q35736543'),
(44470, 'Méribel', 4798, 'ARA', 75, 'FR', 45.41497000, 6.56500000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q35736543'),
(44471, 'Méricourt', 4828, 'HDF', 75, 'FR', 50.40116000, 2.86246000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q35736543'),
(44472, 'Mériel', 4796, 'IDF', 75, 'FR', 49.07614000, 2.21054000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q1457512'),
(44473, 'Mérignac', 4795, 'NAQ', 75, 'FR', 44.83248000, -0.63381000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q35736574'),
(44474, 'Mérignies', 4828, 'HDF', 75, 'FR', 50.50609000, 3.11043000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q656271'),
(44475, 'Mérindol', 4812, 'PAC', 75, 'FR', 43.75516000, 5.20357000, '2019-10-05 22:48:34', '2020-05-01 17:22:46', 1, 'Q470556'),
(44476, 'Méru', 4828, 'HDF', 75, 'FR', 49.23333000, 2.13333000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q816227'),
(44477, 'Méry', 4798, 'ARA', 75, 'FR', 45.64139000, 5.93550000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q816227'),
(44478, 'Méry-sur-Oise', 4796, 'IDF', 75, 'FR', 49.05876000, 2.19113000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q910807'),
(44479, 'Méry-sur-Seine', 4820, 'GES', 75, 'FR', 48.50937000, 3.89129000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q976011'),
(44480, 'Méré', 4796, 'IDF', 75, 'FR', 48.78445000, 1.81247000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q976011'),
(44481, 'Méréville', 4820, 'GES', 75, 'FR', 48.59113000, 6.15106000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q976011'),
(44482, 'Méréville', 4796, 'IDF', 75, 'FR', 48.31476000, 2.08609000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q976011'),
(44483, 'Mésanger', 4802, 'PDL', 75, 'FR', 47.43131000, -1.22798000, '2019-10-05 22:48:34', '2020-05-01 17:22:46', 1, '********'),
(44484, 'Méteren', 4828, 'HDF', 75, 'FR', 50.73333000, 2.70000000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, '********'),
(44485, 'Mézeray', 4802, 'PDL', 75, 'FR', 47.82682000, -0.01770000, '2019-10-05 22:48:34', '2020-05-01 17:22:46', 1, '********'),
(44486, 'Mézin', 4795, 'NAQ', 75, 'FR', 44.05668000, 0.25874000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, '********'),
(44487, 'Méziré', 4825, 'BFC', 75, 'FR', 47.53336000, 6.92072000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, '********'),
(44488, 'Mézières-en-Brenne', 4818, 'CVL', 75, 'FR', 46.81979000, 1.21123000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q631453'),
(44489, 'Mézières-en-Drouais', 4818, 'CVL', 75, 'FR', 48.72413000, 1.42440000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q631453'),
(44490, 'Mézières-sur-Seine', 4796, 'IDF', 75, 'FR', 48.96128000, 1.79245000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, '********'),
(44491, 'Mézy-sur-Seine', 4796, 'IDF', 75, 'FR', 49.00000000, 1.88333000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, '*********'),
(44492, 'Mézériat', 4798, 'ARA', 75, 'FR', 46.23701000, 5.04615000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, '*********'),
(44493, 'Mûr-de-Bretagne', 4807, 'BRE', 75, 'FR', 48.20000000, -2.98333000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, '*********'),
(44494, 'Mûrs-Erigné', 4802, 'PDL', 75, 'FR', 47.40000000, -0.55000000, '2019-10-05 22:48:34', '2020-05-01 17:22:46', 1, '********'),
(44495, 'Nages-et-Solorgues', 4799, 'OCC', 75, 'FR', 43.79010000, 4.23027000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, '********'),
(44496, 'Nailloux', 4799, 'OCC', 75, 'FR', 43.35718000, 1.62302000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q581875'),
(44497, 'Naintré', 4795, 'NAQ', 75, 'FR', 46.76354000, 0.48683000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q753358'),
(44498, 'Naizin', 4807, 'BRE', 75, 'FR', 47.98993000, -2.83287000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q70018'),
(44499, 'Nalliers', 4802, 'PDL', 75, 'FR', 46.47071000, -1.02774000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q70018'),
(44500, 'Nancray', 4825, 'BFC', 75, 'FR', 47.24536000, 6.18283000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q70018'),
(44501, 'Nancy', 4820, 'GES', 75, 'FR', 48.68439000, 6.18496000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q40898'),
(44502, 'Nandy', 4796, 'IDF', 75, 'FR', 48.58301000, 2.56292000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q40898'),
(44503, 'Nangis', 4796, 'IDF', 75, 'FR', 48.55535000, 3.01306000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q40898'),
(44504, 'Nans-les-Pins', 4812, 'PAC', 75, 'FR', 43.37066000, 5.78189000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q579138'),
(44505, 'Nanterre', 4796, 'IDF', 75, 'FR', 48.89198000, 2.20675000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q170507'),
(44506, 'Nantes', 4802, 'PDL', 75, 'FR', 47.21725000, -1.55336000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q12191'),
(44507, 'Nanteuil', 4795, 'NAQ', 75, 'FR', 46.41172000, -0.17461000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q12191'),
(44508, 'Nanteuil-en-Vallée', 4795, 'NAQ', 75, 'FR', 46.00089000, 0.32206000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, '********'),
(44509, 'Nanteuil-le-Haudouin', 4828, 'HDF', 75, 'FR', 49.14082000, 2.81142000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q753495'),
(44510, 'Nanteuil-lès-Meaux', 4796, 'IDF', 75, 'FR', 48.92940000, 2.89594000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, '********'),
(44511, 'Nantiat', 4795, 'NAQ', 75, 'FR', 46.00910000, 1.17308000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, '********'),
(44512, 'Nantua', 4798, 'ARA', 75, 'FR', 46.15343000, 5.60601000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q193937'),
(44513, 'Naours', 4828, 'HDF', 75, 'FR', 50.03682000, 2.27691000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q193937'),
(44514, 'Narbonne', 4799, 'OCC', 75, 'FR', 43.18396000, 3.00141000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q6555'),
(44515, 'Nargis', 4818, 'CVL', 75, 'FR', 48.11106000, 2.75597000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q6555'),
(44516, 'Narrosse', 4795, 'NAQ', 75, 'FR', 43.70381000, -1.00742000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q6555'),
(44517, 'Nassandres', 4804, 'NOR', 75, 'FR', 49.12632000, 0.73597000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q627281'),
(44518, 'Naucelle', 4799, 'OCC', 75, 'FR', 44.19810000, 2.34310000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q627281'),
(44519, 'Naucelles', 4798, 'ARA', 75, 'FR', 44.95898000, 2.41884000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q627281'),
(44520, 'Navailles-Angos', 4795, 'NAQ', 75, 'FR', 43.41667000, -0.33333000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q188616'),
(44521, 'Navarrenx', 4795, 'NAQ', 75, 'FR', 43.32135000, -0.75927000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q179949'),
(44522, 'Naveil', 4818, 'CVL', 75, 'FR', 47.79576000, 1.03222000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q1170373'),
(44523, 'Navenne', 4825, 'BFC', 75, 'FR', 47.60873000, 6.16176000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q904006'),
(44524, 'Naves', 4795, 'NAQ', 75, 'FR', 45.31395000, 1.76708000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q904006'),
(44525, 'Nay', 4795, 'NAQ', 75, 'FR', 43.18333000, -0.26667000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q904006'),
(44526, 'Nazelles-Négron', 4818, 'CVL', 75, 'FR', 47.43333000, 0.95000000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q904006'),
(44527, 'Neaufles-Saint-Martin', 4804, 'NOR', 75, 'FR', 49.27650000, 1.72794000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q904006'),
(44528, 'Neauphle-le-Château', 4796, 'IDF', 75, 'FR', 48.81418000, 1.90567000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q816117'),
(44529, 'Nemours', 4796, 'IDF', 75, 'FR', 48.27511000, 2.69078000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q321067'),
(44530, 'Nercillac', 4795, 'NAQ', 75, 'FR', 45.71667000, -0.25000000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q1624421'),
(44531, 'Nersac', 4795, 'NAQ', 75, 'FR', 45.63333000, 0.05000000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q1624264'),
(44532, 'Nesle', 4828, 'HDF', 75, 'FR', 49.75888000, 2.91133000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q585873'),
(44533, 'Nesles', 4828, 'HDF', 75, 'FR', 50.62588000, 1.65641000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q585873'),
(44534, 'Nesles-la-Montagne', 4828, 'HDF', 75, 'FR', 49.01964000, 3.42607000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q585873'),
(44535, 'Nesles-la-Vallée', 4796, 'IDF', 75, 'FR', 49.13202000, 2.17099000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q257850'),
(44536, 'Nesmy', 4802, 'PDL', 75, 'FR', 46.59078000, -1.40074000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, '********'),
(44537, 'Neuf-Berquin', 4828, 'HDF', 75, 'FR', 50.66065000, 2.67213000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, '********'),
(44538, 'Neuf-Brisach', 4820, 'GES', 75, 'FR', 48.01783000, 7.52795000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q150039'),
(44539, 'Neufchef', 4820, 'GES', 75, 'FR', 49.31678000, 6.02416000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q22041'),
(44540, 'Neufchâteau', 4820, 'GES', 75, 'FR', 48.35557000, 5.69602000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q22041'),
(44541, 'Neufchâtel-Hardelot', 4828, 'HDF', 75, 'FR', 50.62018000, 1.64223000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, '********'),
(44542, 'Neufchâtel-en-Bray', 4804, 'NOR', 75, 'FR', 49.73315000, 1.43956000, '2019-10-05 22:48:34', '2020-05-01 17:22:45', 1, 'Q659248'),
(44543, 'Neufgrange', 4820, 'GES', 75, 'FR', 49.07679000, 7.06526000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q659248'),
(44544, 'Neufmanil', 4820, 'GES', 75, 'FR', 49.81096000, 4.79673000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q659248'),
(44545, 'Neufmoutiers-en-Brie', 4796, 'IDF', 75, 'FR', 48.76882000, 2.83156000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q659248'),
(44546, 'Neuilly-Plaisance', 4796, 'IDF', 75, 'FR', 48.86342000, 2.50600000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q685597'),
(44547, 'Neuilly-Saint-Front', 4828, 'HDF', 75, 'FR', 49.17012000, 3.26393000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q685597'),
(44548, 'Neuilly-en-Thelle', 4828, 'HDF', 75, 'FR', 49.22445000, 2.28525000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q685597'),
(44549, 'Neuilly-le-Réal', 4798, 'ARA', 75, 'FR', 46.46413000, 3.43197000, '2019-10-05 22:48:34', '2020-05-01 17:22:43', 1, 'Q685597'),
(44550, 'Neuilly-lès-Dijon', 4825, 'BFC', 75, 'FR', 47.27943000, 5.10645000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q685597'),
(44551, 'Neuilly-sous-Clermont', 4828, 'HDF', 75, 'FR', 49.34437000, 2.41030000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q602066'),
(44552, 'Neuilly-sur-Marne', 4796, 'IDF', 75, 'FR', 48.85373000, 2.54903000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q220906'),
(44553, 'Neuilly-sur-Seine', 4796, 'IDF', 75, 'FR', 48.88460000, 2.26965000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q48958'),
(44554, 'Neuillé-Pont-Pierre', 4818, 'CVL', 75, 'FR', 47.54840000, 0.54962000, '2019-10-05 22:48:34', '2020-05-01 17:22:44', 1, 'Q48958'),
(44555, 'Neulise', 4798, 'ARA', 75, 'FR', 45.89811000, 4.18134000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q48958'),
(44556, 'Neulliac', 4807, 'BRE', 75, 'FR', 48.12836000, -2.98289000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q48958'),
(44557, 'Neung-sur-Beuvron', 4818, 'CVL', 75, 'FR', 47.53486000, 1.80514000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q1169924'),
(44558, 'Neussargues', 4798, 'ARA', 75, 'FR', 45.12826000, 2.97627000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q1169924'),
(44559, 'Neuvecelle', 4798, 'ARA', 75, 'FR', 46.39502000, 6.61257000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q1169924'),
(44560, 'Neuves-Maisons', 4820, 'GES', 75, 'FR', 48.61811000, 6.10544000, '2019-10-05 22:48:34', '2019-10-05 22:48:34', 1, 'Q1169924'),
(44561, 'Neuvic', 4795, 'NAQ', 75, 'FR', 45.10033000, 0.46901000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1169924'),
(44562, 'Neuvic-Entier', 4795, 'NAQ', 75, 'FR', 45.72206000, 1.61303000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1169924'),
(44563, 'Neuville-Saint-Rémy', 4828, 'HDF', 75, 'FR', 50.18618000, 3.22404000, '2019-10-05 22:48:35', '2020-05-01 17:22:45', 1, 'Q1063646'),
(44564, 'Neuville-Saint-Vaast', 4828, 'HDF', 75, 'FR', 50.35756000, 2.76261000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1063646'),
(44565, 'Neuville-aux-Bois', 4818, 'CVL', 75, 'FR', 48.06813000, 2.05372000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1063646'),
(44566, 'Neuville-de-Poitou', 4795, 'NAQ', 75, 'FR', 46.68333000, 0.25000000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q733770'),
(44567, 'Neuville-en-Ferrain', 4828, 'HDF', 75, 'FR', 50.74839000, 3.15676000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44568, 'Neuville-les-Dames', 4798, 'ARA', 75, 'FR', 46.16230000, 5.00667000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44569, 'Neuville-sur-Ain', 4798, 'ARA', 75, 'FR', 46.07144000, 5.37491000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44570, 'Neuville-sur-Escaut', 4828, 'HDF', 75, 'FR', 50.30000000, 3.35000000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44571, 'Neuville-sur-Oise', 4796, 'IDF', 75, 'FR', 49.01667000, 2.06667000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44572, 'Neuville-sur-Sarthe', 4802, 'PDL', 75, 'FR', 48.07615000, 0.19264000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44573, 'Neuville-sur-Saône', 4798, 'ARA', 75, 'FR', 45.87615000, 4.84099000, '2019-10-05 22:48:35', '2020-05-01 17:22:43', 1, 'Q522234'),
(44574, 'Neuvy', 4798, 'ARA', 75, 'FR', 46.56191000, 3.29038000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q522234'),
(44575, 'Neuvy-Pailloux', 4818, 'CVL', 75, 'FR', 46.88486000, 1.86152000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q662596'),
(44576, 'Neuvy-Saint-Sépulchre', 4818, 'CVL', 75, 'FR', 46.59781000, 1.80601000, '2019-10-05 22:48:35', '2020-05-01 17:22:44', 1, 'Q472855'),
(44577, 'Neuvy-Sautour', 4825, 'BFC', 75, 'FR', 48.04190000, 3.79472000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q472855'),
(44578, 'Neuvy-le-Roi', 4818, 'CVL', 75, 'FR', 47.60386000, 0.59472000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q472855'),
(44579, 'Neuvy-sur-Barangeon', 4818, 'CVL', 75, 'FR', 47.31495000, 2.25343000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q472855'),
(44580, 'Neuvy-sur-Loire', 4825, 'BFC', 75, 'FR', 47.52323000, 2.88333000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q472855'),
(44581, 'Neuvéglise', 4798, 'ARA', 75, 'FR', 44.92809000, 2.98328000, '2019-10-05 22:48:35', '2020-05-01 17:22:43', 1, 'Q472855'),
(44582, 'Neuwiller-lès-Saverne', 4820, 'GES', 75, 'FR', 48.82506000, 7.40513000, '2019-10-05 22:48:35', '2020-05-01 17:22:45', 1, 'Q21417'),
(44583, 'Nevers', 4825, 'BFC', 75, 'FR', 46.98956000, 3.15900000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q166997'),
(44584, 'Nevoy', 4818, 'CVL', 75, 'FR', 47.71591000, 2.57791000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q166997'),
(44585, 'Nexon', 4795, 'NAQ', 75, 'FR', 45.67962000, 1.18555000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q166997'),
(44586, 'Neydens', 4798, 'ARA', 75, 'FR', 46.12162000, 6.10436000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q166997'),
(44587, 'Nice', 4812, 'PAC', 75, 'FR', 43.70313000, 7.26608000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q33959'),
(44588, 'Niderviller', 4820, 'GES', 75, 'FR', 48.71294000, 7.10627000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q33959'),
(44589, 'Niederbronn-les-Bains', 4820, 'GES', 75, 'FR', 48.95165000, 7.64271000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q21387'),
(44590, 'Niederhaslach', 4820, 'GES', 75, 'FR', 48.54264000, 7.34282000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q22520'),
(44591, 'Niederhausbergen', 4820, 'GES', 75, 'FR', 48.62400000, 7.70210000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q22520'),
(44592, 'Niedernai', 4820, 'GES', 75, 'FR', 48.45101000, 7.51635000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q21369'),
(44593, 'Niederschaeffolsheim', 4820, 'GES', 75, 'FR', 48.77241000, 7.73862000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q21369'),
(44594, 'Nieppe', 4828, 'HDF', 75, 'FR', 50.70425000, 2.83506000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q681803'),
(44595, 'Nieul', 4795, 'NAQ', 75, 'FR', 45.92668000, 1.17494000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q681803'),
(44596, 'Nieul-le-Dolent', 4802, 'PDL', 75, 'FR', 46.57379000, -1.50808000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44597, 'Nieul-lès-Saintes', 4795, 'NAQ', 75, 'FR', 45.76021000, -0.73137000, '2019-10-05 22:48:35', '2020-05-01 17:22:45', 1, '********'),
(44598, 'Nieul-sur-Mer', 4795, 'NAQ', 75, 'FR', 46.20583000, -1.16449000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q989360'),
(44599, 'Niherne', 4818, 'CVL', 75, 'FR', 46.82768000, 1.56384000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44600, 'Nilvange', 4820, 'GES', 75, 'FR', 49.34224000, 6.04964000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44601, 'Niort', 4795, 'NAQ', 75, 'FR', 46.32313000, -0.45877000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q184159'),
(44602, 'Nissan-lez-Enserune', 4799, 'OCC', 75, 'FR', 43.28961000, 3.12705000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q270195'),
(44603, 'Nivillac', 4807, 'BRE', 75, 'FR', 47.53371000, -2.28298000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q71343'),
(44604, 'Nivolas-Vermelle', 4798, 'ARA', 75, 'FR', 45.55885000, 5.30578000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q71343'),
(44605, 'Niévroz', 4798, 'ARA', 75, 'FR', 45.82576000, 5.06108000, '2019-10-05 22:48:35', '2020-05-01 17:22:43', 1, 'Q328367'),
(44606, 'Noaillan', 4795, 'NAQ', 75, 'FR', 44.48057000, -0.36640000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44607, 'Noailles', 4828, 'HDF', 75, 'FR', 49.32866000, 2.20024000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44608, 'Noailles', 4812, 'PAC', 75, 'FR', 43.29604000, 5.37959000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44609, 'Nogaro', 4799, 'OCC', 75, 'FR', 43.75856000, -0.03293000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44610, 'Nogent-le-Bas', 4820, 'GES', 75, 'FR', 48.01974000, 5.33323000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44611, 'Nogent-le-Phaye', 4818, 'CVL', 75, 'FR', 48.44528000, 1.57777000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44612, 'Nogent-le-Roi', 4818, 'CVL', 75, 'FR', 48.64785000, 1.52933000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44613, 'Nogent-le-Rotrou', 4818, 'CVL', 75, 'FR', 48.32157000, 0.82177000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q1007331'),
(44614, 'Nogent-l’Artaud', 4828, 'HDF', 75, 'FR', 48.96560000, 3.32178000, '2019-10-05 22:48:35', '2020-05-01 17:22:45', 1, 'Q1007331'),
(44615, 'Nogent-sur-Marne', 4796, 'IDF', 75, 'FR', 48.83669000, 2.48255000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q212274'),
(44616, 'Nogent-sur-Oise', 4828, 'HDF', 75, 'FR', 49.27158000, 2.47074000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q693488'),
(44617, 'Nogent-sur-Seine', 4820, 'GES', 75, 'FR', 48.49372000, 3.50262000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q476895'),
(44618, 'Nogent-sur-Vernisson', 4818, 'CVL', 75, 'FR', 47.84603000, 2.74267000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44619, 'Nogentel', 4828, 'HDF', 75, 'FR', 49.01750000, 3.40250000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44620, 'Nohanent', 4798, 'ARA', 75, 'FR', 45.80815000, 3.05507000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44621, 'Nohic', 4799, 'OCC', 75, 'FR', 43.88970000, 1.43741000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44622, 'Noidans-lès-Vesoul', 4825, 'BFC', 75, 'FR', 47.61299000, 6.12571000, '2019-10-05 22:48:35', '2020-05-01 17:22:44', 1, 'Q600920'),
(44623, 'Nointot', 4804, 'NOR', 75, 'FR', 49.59822000, 0.47674000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44624, 'Noirmoutier-en-l’Île', 4802, 'PDL', 75, 'FR', 46.99733000, -2.27374000, '2019-10-05 22:48:35', '2020-05-01 17:22:46', 1, 'Q600920'),
(44625, 'Noirétable', 4798, 'ARA', 75, 'FR', 45.81671000, 3.76247000, '2019-10-05 22:48:35', '2020-05-01 17:22:43', 1, 'Q600920'),
(44626, 'Noiseau', 4796, 'IDF', 75, 'FR', 48.77589000, 2.54892000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44627, 'Noisiel', 4796, 'IDF', 75, 'FR', 48.84868000, 2.62435000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44628, 'Noisy-le-Grand', 4796, 'IDF', 75, 'FR', 48.84979000, 2.56266000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44629, 'Noisy-le-Roi', 4796, 'IDF', 75, 'FR', 48.84445000, 2.06345000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44630, 'Noisy-le-Sec', 4796, 'IDF', 75, 'FR', 48.89148000, 2.46451000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44631, 'Noisy-sur-École', 4796, 'IDF', 75, 'FR', 48.36701000, 2.50804000, '2019-10-05 22:48:35', '2020-05-01 17:22:43', 1, 'Q600920'),
(44632, 'Noizay', 4818, 'CVL', 75, 'FR', 47.42188000, 0.89201000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44633, 'Nolay', 4825, 'BFC', 75, 'FR', 46.95202000, 4.63405000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44634, 'Nomain', 4828, 'HDF', 75, 'FR', 50.49857000, 3.24991000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q600920'),
(44635, 'Nomeny', 4820, 'GES', 75, 'FR', 48.88977000, 6.22635000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q688130'),
(44636, 'Nomexy', 4820, 'GES', 75, 'FR', 48.30741000, 6.38572000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q688130'),
(44637, 'Nommay', 4825, 'BFC', 75, 'FR', 47.53780000, 6.84219000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q688130'),
(44638, 'Nonancourt', 4804, 'NOR', 75, 'FR', 48.77086000, 1.19799000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q688130'),
(44639, 'Nontron', 4795, 'NAQ', 75, 'FR', 45.52950000, 0.66179000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q467342'),
(44640, 'Nord', 4828, 'HDF', 75, 'FR', 50.33333000, 3.36197000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q12661'),
(44641, 'Nordhouse', 4820, 'GES', 75, 'FR', 48.44833000, 7.67326000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q12661'),
(44642, 'Normanville', 4804, 'NOR', 75, 'FR', 49.07868000, 1.15915000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q12661'),
(44643, 'Norrent-Fontes', 4828, 'HDF', 75, 'FR', 50.58333000, 2.40000000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q877969'),
(44644, 'Norroy-lès-Pont-à-Mousson', 4820, 'GES', 75, 'FR', 48.93495000, 6.03078000, '2019-10-05 22:48:35', '2020-05-01 17:22:45', 1, 'Q877969'),
(44645, 'Nort-sur-Erdre', 4802, 'PDL', 75, 'FR', 47.43881000, -1.49833000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q818800'),
(44646, 'Nortkerque', 4828, 'HDF', 75, 'FR', 50.87519000, 2.02464000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q818800'),
(44647, 'Nostang', 4807, 'BRE', 75, 'FR', 47.74870000, -3.18639000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q818800'),
(44648, 'Notre-Dame Limite', 4812, 'PAC', 75, 'FR', 43.38165000, 5.36269000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q818800'),
(44649, 'Notre-Dame du Mont', 4812, 'PAC', 75, 'FR', 43.29293000, 5.38203000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q818800'),
(44650, 'Notre-Dame-de-Bondeville', 4804, 'NOR', 75, 'FR', 49.48333000, 1.05000000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q818800'),
(44651, 'Notre-Dame-de-Gravenchon', 4804, 'NOR', 75, 'FR', 49.48940000, 0.57188000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44652, 'Notre-Dame-de-Monts', 4802, 'PDL', 75, 'FR', 46.83100000, -2.13100000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44653, 'Notre-Dame-de-Mésage', 4798, 'ARA', 75, 'FR', 45.06241000, 5.75880000, '2019-10-05 22:48:35', '2020-05-01 17:22:43', 1, '********'),
(44654, 'Notre-Dame-de-Riez', 4802, 'PDL', 75, 'FR', 46.74445000, -1.90857000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44655, 'Notre-Dame-de-Sanilhac', 4795, 'NAQ', 75, 'FR', 45.12121000, 0.71157000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44656, 'Notre-Dame-des-Landes', 4802, 'PDL', 75, 'FR', 47.38048000, -1.70904000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q733497'),
(44657, 'Nouaillé-Maupertuis', 4795, 'NAQ', 75, 'FR', 46.51113000, 0.41667000, '2019-10-05 22:48:35', '2020-05-01 17:22:45', 1, 'Q646530'),
(44658, 'Nouan-le-Fuzelier', 4818, 'CVL', 75, 'FR', 47.53600000, 2.03647000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, '********'),
(44659, 'Nousty', 4795, 'NAQ', 75, 'FR', 43.26667000, -0.21667000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q179709'),
(44660, 'Nouvion', 4828, 'HDF', 75, 'FR', 50.20000000, 1.78333000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q389471'),
(44661, 'Nouvion-sur-Meuse', 4820, 'GES', 75, 'FR', 49.69949000, 4.79562000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q389471'),
(44662, 'Nouvoitou', 4807, 'BRE', 75, 'FR', 48.04105000, -1.54714000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q389471'),
(44663, 'Nouzilly', 4818, 'CVL', 75, 'FR', 47.54499000, 0.74623000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q788866'),
(44664, 'Nouzonville', 4820, 'GES', 75, 'FR', 49.81369000, 4.74736000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q788866'),
(44665, 'Novalaise', 4798, 'ARA', 75, 'FR', 45.59480000, 5.77767000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q788866'),
(44666, 'Noves', 4812, 'PAC', 75, 'FR', 43.87736000, 4.90248000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q474034'),
(44667, 'Novillars', 4825, 'BFC', 75, 'FR', 47.28465000, 6.12878000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q474034'),
(44668, 'Novéant-sur-Moselle', 4820, 'GES', 75, 'FR', 49.02366000, 6.04795000, '2019-10-05 22:48:35', '2020-05-01 17:22:45', 1, 'Q474034'),
(44669, 'Noyal-Muzillac', 4807, 'BRE', 75, 'FR', 47.59169000, -2.45557000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q474034'),
(44670, 'Noyal-Pontivy', 4807, 'BRE', 75, 'FR', 48.06667000, -2.88333000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q71692'),
(44671, 'Noyal-sur-Vilaine', 4807, 'BRE', 75, 'FR', 48.11219000, -1.52333000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q545801'),
(44672, 'Noyant', 4802, 'PDL', 75, 'FR', 47.51048000, 0.11771000, '2019-10-05 22:48:35', '2019-10-05 22:48:35', 1, 'Q845638'),
(44673, 'Noyant-la-Gravoyère', 4802, 'PDL', 75, 'FR', 47.70348000, -0.95730000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q845638'),
(44674, 'Noyarey', 4798, 'ARA', 75, 'FR', 45.24421000, 5.63366000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, '********'),
(44675, 'Noyelles-Godault', 4828, 'HDF', 75, 'FR', 50.41753000, 2.99306000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, '********'),
(44676, 'Noyelles-lès-Vermelles', 4828, 'HDF', 75, 'FR', 50.49001000, 2.72628000, '2019-10-05 22:48:36', '2020-05-01 17:22:45', 1, '********'),
(44677, 'Noyelles-sous-Lens', 4828, 'HDF', 75, 'FR', 50.43084000, 2.87221000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q877853'),
(44678, 'Noyen-sur-Sarthe', 4802, 'PDL', 75, 'FR', 47.87187000, -0.09916000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q877853'),
(44679, 'Noyers-sur-Cher', 4818, 'CVL', 75, 'FR', 47.27701000, 1.40320000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, '********'),
(44680, 'Noyon', 4828, 'HDF', 75, 'FR', 49.58333000, 3.00000000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q49831'),
(44681, 'Nozay', 4796, 'IDF', 75, 'FR', 48.65921000, 2.24151000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q49831'),
(44682, 'Nozay', 4802, 'PDL', 75, 'FR', 47.56495000, -1.62629000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q49831'),
(44683, 'Noé', 4799, 'OCC', 75, 'FR', 43.35814000, 1.27709000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q49831'),
(44684, 'Nuaillé', 4802, 'PDL', 75, 'FR', 47.09524000, -0.79477000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q49831'),
(44685, 'Nueil-les-Aubiers', 4795, 'NAQ', 75, 'FR', 46.93333000, -0.58333000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q779573'),
(44686, 'Nueil-sur-Layon', 4802, 'PDL', 75, 'FR', 47.11811000, -0.36573000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q650510'),
(44687, 'Nuillé-sur-Vicoin', 4802, 'PDL', 75, 'FR', 47.98556000, -0.78351000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q650510'),
(44688, 'Nuits-Saint-Georges', 4825, 'BFC', 75, 'FR', 47.13683000, 4.94900000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q16622'),
(44689, 'Nyoiseau', 4802, 'PDL', 75, 'FR', 47.71667000, -0.91667000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q16622'),
(44690, 'Nyons', 4798, 'ARA', 75, 'FR', 44.36082000, 5.14052000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q209810'),
(44691, 'Nègrepelisse', 4799, 'OCC', 75, 'FR', 44.07436000, 1.51978000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q209810'),
(44692, 'Nébian', 4799, 'OCC', 75, 'FR', 43.60657000, 3.43133000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q209810'),
(44693, 'Néoules', 4812, 'PAC', 75, 'FR', 43.30844000, 6.00798000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q209810'),
(44694, 'Nérac', 4795, 'NAQ', 75, 'FR', 44.13613000, 0.33934000, '2019-10-05 22:48:36', '2020-05-01 17:22:45', 1, 'Q210769'),
(44695, 'Néris-les-Bains', 4798, 'ARA', 75, 'FR', 46.28688000, 2.65869000, '2019-10-05 22:48:36', '2020-05-01 17:22:43', 1, 'Q467294'),
(44696, 'Nérondes', 4818, 'CVL', 75, 'FR', 46.99758000, 2.81834000, '2019-10-05 22:48:36', '2020-05-01 17:22:44', 1, 'Q467294'),
(44697, 'Névez', 4807, 'BRE', 75, 'FR', 47.81924000, -3.79225000, '2019-10-05 22:48:36', '2020-05-01 17:22:44', 1, 'Q477751'),
(44698, 'Névian', 4799, 'OCC', 75, 'FR', 43.21238000, 2.90286000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q477751'),
(44699, 'Néville', 4804, 'NOR', 75, 'FR', 49.82472000, 0.70862000, '2019-10-05 22:48:36', '2020-05-01 17:22:45', 1, 'Q477751'),
(44700, 'Nîmes', 4799, 'OCC', 75, 'FR', 43.83378000, 4.35962000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q42807'),
(44701, 'Nœux-les-Mines', 4828, 'HDF', 75, 'FR', 50.48333000, 2.66667000, '2019-10-05 22:48:36', '2020-05-01 17:22:45', 1, 'Q161950'),
(44702, 'Obenheim', 4820, 'GES', 75, 'FR', 48.35975000, 7.69200000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q161950'),
(44703, 'Oberbronn', 4820, 'GES', 75, 'FR', 48.94020000, 7.60700000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q161950'),
(44704, 'Oberhaslach', 4820, 'GES', 75, 'FR', 48.55039000, 7.33213000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q161950'),
(44705, 'Oberhausbergen', 4820, 'GES', 75, 'FR', 48.60607000, 7.68846000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q21511'),
(44706, 'Oberhergheim', 4820, 'GES', 75, 'FR', 47.96668000, 7.39516000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q21511'),
(44707, 'Oberhoffen-sur-Moder', 4820, 'GES', 75, 'FR', 48.78366000, 7.86347000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q21511'),
(44708, 'Obermodern-Zutzendorf', 4820, 'GES', 75, 'FR', 48.85000000, 7.53333000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q21511'),
(44709, 'Obernai', 4820, 'GES', 75, 'FR', 48.46313000, 7.48100000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q22727'),
(44710, 'Oberschaeffolsheim', 4820, 'GES', 75, 'FR', 48.58643000, 7.65018000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q22727'),
(44711, 'Objat', 4795, 'NAQ', 75, 'FR', 45.26302000, 1.40826000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q22727'),
(44712, 'Octeville', 4804, 'NOR', 75, 'FR', 49.62612000, -1.64349000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q2420328'),
(44713, 'Octeville-sur-Mer', 4804, 'NOR', 75, 'FR', 49.55496000, 0.11660000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q2420328'),
(44714, 'Oderen', 4820, 'GES', 75, 'FR', 47.91036000, 6.97463000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148086'),
(44715, 'Odos', 4799, 'OCC', 75, 'FR', 43.19622000, 0.05693000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148086'),
(44716, 'Oermingen', 4820, 'GES', 75, 'FR', 49.00043000, 7.12900000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148086'),
(44717, 'Oeyreluy', 4795, 'NAQ', 75, 'FR', 43.66876000, -1.08276000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148086'),
(44718, 'Offemont', 4825, 'BFC', 75, 'FR', 47.66278000, 6.87764000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148086'),
(44719, 'Offenheim', 4820, 'GES', 75, 'FR', 48.63208000, 7.61668000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148086'),
(44720, 'Offranville', 4804, 'NOR', 75, 'FR', 49.87208000, 1.04817000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148086'),
(44721, 'Ogeu-les-Bains', 4795, 'NAQ', 75, 'FR', 43.15000000, -0.50000000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q858915'),
(44722, 'Ognes', 4828, 'HDF', 75, 'FR', 49.61244000, 3.19340000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q858915'),
(44723, 'Ohlungen', 4820, 'GES', 75, 'FR', 48.81352000, 7.70225000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q858915'),
(44724, 'Oignies', 4828, 'HDF', 75, 'FR', 50.46331000, 2.99376000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q858915'),
(44725, 'Oinville-sur-Montcient', 4796, 'IDF', 75, 'FR', 49.02722000, 1.84928000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q858915'),
(44726, 'Oisemont', 4828, 'HDF', 75, 'FR', 49.95587000, 1.76703000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q388107'),
(44727, 'Oisseau', 4802, 'PDL', 75, 'FR', 48.35559000, -0.67148000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q388107'),
(44728, 'Oissel', 4804, 'NOR', 75, 'FR', 49.33309000, 1.09413000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q752885'),
(44729, 'Oissery', 4796, 'IDF', 75, 'FR', 49.07047000, 2.81819000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q752885'),
(44730, 'Oisy-le-Verger', 4828, 'HDF', 75, 'FR', 50.25047000, 3.12330000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q495694'),
(44731, 'Olemps', 4799, 'OCC', 75, 'FR', 44.34638000, 2.55140000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, '********'),
(44732, 'Oletta', 4806, '20R', 75, 'FR', 42.62991000, 9.35431000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q650588'),
(44733, 'Olivet', 4818, 'CVL', 75, 'FR', 47.86219000, 1.89910000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q650588'),
(44734, 'Ollainville', 4796, 'IDF', 75, 'FR', 48.59081000, 2.21936000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q650588'),
(44735, 'Ollioules', 4812, 'PAC', 75, 'FR', 43.13990000, 5.84766000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q840237'),
(44736, 'Olmeto', 4806, '20R', 75, 'FR', 41.71724000, 8.91783000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q840237'),
(44737, 'Olonne-sur-Mer', 4802, 'PDL', 75, 'FR', 46.53524000, -1.77293000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q627175'),
(44738, 'Olonzac', 4799, 'OCC', 75, 'FR', 43.28294000, 2.73098000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q627175'),
(44739, 'Oloron-Sainte-Marie', 4795, 'NAQ', 75, 'FR', 43.19441000, -0.61069000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q208407'),
(44740, 'Omissy', 4828, 'HDF', 75, 'FR', 49.87760000, 3.31235000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q208407'),
(44741, 'Ondes', 4799, 'OCC', 75, 'FR', 43.78250000, 1.30823000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q208407'),
(44742, 'Ondres', 4795, 'NAQ', 75, 'FR', 43.56461000, -1.44449000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q208407'),
(44743, 'Onesse-Laharie', 4795, 'NAQ', 75, 'FR', 44.06146000, -1.06984000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q208407'),
(44744, 'Onnaing', 4828, 'HDF', 75, 'FR', 50.38584000, 3.59963000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q208407'),
(44745, 'Ons-en-Bray', 4828, 'HDF', 75, 'FR', 49.41631000, 1.92302000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q818291'),
(44746, 'Onzain', 4818, 'CVL', 75, 'FR', 47.49956000, 1.17701000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q752907'),
(44747, 'Opio', 4812, 'PAC', 75, 'FR', 43.66862000, 6.98212000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q752907'),
(44748, 'Oppède le Vieux', 4812, 'PAC', 75, 'FR', 43.82844000, 5.16132000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q625964'),
(44749, 'Opéra', 4812, 'PAC', 75, 'FR', 43.29401000, 5.37610000, '2019-10-05 22:48:36', '2020-05-01 17:22:46', 1, 'Q625964'),
(44750, 'Oradour-sur-Glane', 4795, 'NAQ', 75, 'FR', 45.93405000, 1.03170000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q190974'),
(44751, 'Oradour-sur-Vayres', 4795, 'NAQ', 75, 'FR', 45.73286000, 0.86457000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q1076759'),
(44752, 'Oraison', 4812, 'PAC', 75, 'FR', 43.91726000, 5.91836000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q1076759'),
(44753, 'Orange', 4812, 'PAC', 75, 'FR', 44.13806000, 4.81025000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q187796'),
(44754, 'Orbec', 4804, 'NOR', 75, 'FR', 49.01667000, 0.41667000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q187796'),
(44755, 'Orbey', 4820, 'GES', 75, 'FR', 48.12651000, 7.16455000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148855'),
(44756, 'Orcet', 4798, 'ARA', 75, 'FR', 45.70355000, 3.16884000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148855'),
(44757, 'Orchamps', 4825, 'BFC', 75, 'FR', 47.14751000, 5.65873000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q148855'),
(44758, 'Orchamps-Vennes', 4825, 'BFC', 75, 'FR', 47.13042000, 6.52468000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q204119'),
(44759, 'Orchies', 4828, 'HDF', 75, 'FR', 50.46667000, 3.23333000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q389524'),
(44760, 'Orcines', 4798, 'ARA', 75, 'FR', 45.78246000, 3.01234000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q1098277'),
(44761, 'Orgelet', 4825, 'BFC', 75, 'FR', 46.52096000, 5.61049000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q1098277'),
(44762, 'Orgerus', 4796, 'IDF', 75, 'FR', 48.83851000, 1.70132000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q960891'),
(44763, 'Orgeval', 4796, 'IDF', 75, 'FR', 48.92162000, 1.97790000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q960891'),
(44764, 'Orgon', 4812, 'PAC', 75, 'FR', 43.79108000, 5.03869000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q269268'),
(44765, 'Orgueil', 4799, 'OCC', 75, 'FR', 43.90618000, 1.41071000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q269268'),
(44766, 'Orgères', 4807, 'BRE', 75, 'FR', 47.99886000, -1.66781000, '2019-10-05 22:48:36', '2020-05-01 17:22:44', 1, 'Q269268'),
(44767, 'Orgères-en-Beauce', 4818, 'CVL', 75, 'FR', 48.14636000, 1.68380000, '2019-10-05 22:48:36', '2020-05-01 17:22:44', 1, 'Q269268'),
(44768, 'Origny-Sainte-Benoite', 4828, 'HDF', 75, 'FR', 49.83333000, 3.50000000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q269268'),
(44769, 'Origny-en-Thiérache', 4828, 'HDF', 75, 'FR', 49.89327000, 4.01644000, '2019-10-05 22:48:36', '2020-05-01 17:22:45', 1, 'Q269268'),
(44770, 'Orival', 4804, 'NOR', 75, 'FR', 49.72429000, 1.20512000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q269268'),
(44771, 'Orleix', 4799, 'OCC', 75, 'FR', 43.27932000, 0.12033000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q269268'),
(44772, 'Orliénas', 4798, 'ARA', 75, 'FR', 45.65886000, 4.71832000, '2019-10-05 22:48:36', '2020-05-01 17:22:43', 1, 'Q269268'),
(44773, 'Orly', 4796, 'IDF', 75, 'FR', 48.74792000, 2.39253000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q236058'),
(44774, 'Orléans', 4818, 'CVL', 75, 'FR', 47.90289000, 1.90389000, '2019-10-05 22:48:36', '2020-05-01 17:22:44', 1, 'Q6548'),
(44775, 'Orléat', 4798, 'ARA', 75, 'FR', 45.86059000, 3.42083000, '2019-10-05 22:48:36', '2020-05-01 17:22:43', 1, 'Q6548'),
(44776, 'Ormes', 4818, 'CVL', 75, 'FR', 47.94152000, 1.81823000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q6548'),
(44777, 'Ormesson-sur-Marne', 4796, 'IDF', 75, 'FR', 48.78630000, 2.54471000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q6548'),
(44778, 'Ormoy', 4796, 'IDF', 75, 'FR', 48.57489000, 2.45206000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q6548'),
(44779, 'Ornaisons', 4799, 'OCC', 75, 'FR', 43.18040000, 2.83689000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q6548'),
(44780, 'Ornans', 4825, 'BFC', 75, 'FR', 47.10749000, 6.14306000, '2019-10-05 22:48:36', '2019-10-05 22:48:36', 1, 'Q499537'),
(44781, 'Ornex', 4798, 'ARA', 75, 'FR', 46.27270000, 6.09982000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q499537'),
(44782, 'Orry-la-Ville', 4828, 'HDF', 75, 'FR', 49.13345000, 2.51139000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q834094'),
(44783, 'Orsan', 4799, 'OCC', 75, 'FR', 44.13106000, 4.66520000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q834094'),
(44784, 'Orsay', 4796, 'IDF', 75, 'FR', 48.69572000, 2.18727000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q372281'),
(44785, 'Ortaffa', 4799, 'OCC', 75, 'FR', 42.57977000, 2.92653000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q372281'),
(44786, 'Orthez', 4795, 'NAQ', 75, 'FR', 43.48839000, -0.77244000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q244248'),
(44787, 'Orval', 4818, 'CVL', 75, 'FR', 46.72581000, 2.47144000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q244248'),
(44788, 'Orvault', 4802, 'PDL', 75, 'FR', 47.27095000, -1.62190000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q271407'),
(44789, 'Osny', 4796, 'IDF', 75, 'FR', 49.07010000, 2.06277000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q271407'),
(44790, 'Ossun', 4799, 'OCC', 75, 'FR', 43.18333000, -0.03333000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q777902'),
(44791, 'Ossé', 4807, 'BRE', 75, 'FR', 48.05542000, -1.45029000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, 'Q624139'),
(44792, 'Osséja', 4799, 'OCC', 75, 'FR', 42.41383000, 1.98192000, '2019-10-05 22:48:37', '2020-05-01 17:22:46', 1, 'Q624139'),
(44793, 'Ostheim', 4820, 'GES', 75, 'FR', 48.15995000, 7.36976000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q624139'),
(44794, 'Ostricourt', 4828, 'HDF', 75, 'FR', 50.45145000, 3.03417000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44795, 'Ostwald', 4820, 'GES', 75, 'FR', 48.54369000, 7.71099000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44796, 'Othis', 4796, 'IDF', 75, 'FR', 49.07387000, 2.67502000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1459593'),
(44797, 'Ottange', 4820, 'GES', 75, 'FR', 49.44307000, 6.01988000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1459593'),
(44798, 'Otterswiller', 4820, 'GES', 75, 'FR', 48.72568000, 7.37878000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1459593'),
(44799, 'Ottmarsheim', 4820, 'GES', 75, 'FR', 47.78802000, 7.50777000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1459593'),
(44800, 'Ottrott', 4820, 'GES', 75, 'FR', 48.45642000, 7.42556000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q22434'),
(44801, 'Ouches', 4798, 'ARA', 75, 'FR', 46.01582000, 3.98828000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q22434');

