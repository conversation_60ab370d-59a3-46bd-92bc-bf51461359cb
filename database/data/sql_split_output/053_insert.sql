INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(26333, 'Hügelsheim', 3006, 'B<PERSON>', 82, 'DE', 48.80000000, 8.11667000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q83092'),
(26334, '<PERSON><PERSON><PERSON><PERSON>', 3006, 'BW', 82, 'DE', 48.51982000, 9.40790000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q81501'),
(26335, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 3017, 'NW', 82, 'DE', 52.28333000, 8.66667000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q182753'),
(26336, '<PERSON><PERSON><PERSON><PERSON>', 3008, 'N<PERSON>', 82, '<PERSON>', 52.24951000, 9.36147000, '2019-10-05 22:41:07', '2020-05-01 17:22:48', 1, 'Q584663'),
(26337, '<PERSON><PERSON>n<PERSON>', 3018, 'HE', 82, 'DE', 50.67966000, 9.76727000, '2019-10-05 22:41:07', '2020-05-01 17:22:48', 1, 'Q569692'),
(26338, 'H<PERSON>nxe', 3017, 'NW', 82, 'DE', 51.63405000, 6.69741000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q49284524'),
(26339, 'Hürtgenwald', 3017, 'NW', 82, 'DE', 50.71667000, 6.36667000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q199974'),
(26340, 'Hürth', 3017, 'NW', 82, 'DE', 50.87079000, 6.86761000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q4060'),
(26341, 'Hürup', 3005, 'SH', 82, 'DE', 54.75000000, 9.53333000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q164363'),
(26342, 'Hütschenhausen', 3019, 'RP', 82, 'DE', 49.41667000, 7.48333000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q560181'),
(26343, 'Hüttenrode', 3011, 'ST', 82, 'DE', 51.76880000, 10.90384000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q698858'),
(26344, 'Hüttisheim', 3006, 'BW', 82, 'DE', 48.27965000, 9.94246000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q698858'),
(26345, 'Hüttlingen', 3006, 'BW', 82, 'DE', 48.89266000, 10.10064000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q698858'),
(26346, 'Ibbenbüren', 3017, 'NW', 82, 'DE', 52.27964000, 7.71457000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q698858'),
(26347, 'Ichenhausen', 3009, 'BY', 82, 'DE', 48.37119000, 10.30706000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q503202'),
(26348, 'Ichtershausen', 3015, 'TH', 82, 'DE', 50.87602000, 10.97028000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q559841'),
(26349, 'Icking', 3009, 'BY', 82, 'DE', 47.95000000, 11.43333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q254148'),
(26350, 'Idar-Oberstein', 3019, 'RP', 82, 'DE', 49.71443000, 7.30776000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q16119'),
(26351, 'Idstein', 3018, 'HE', 82, 'DE', 50.21773000, 8.26679000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q525659'),
(26352, 'Iffeldorf', 3009, 'BY', 82, 'DE', 47.76667000, 11.31667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q265707'),
(26353, 'Iffezheim', 3006, 'BW', 82, 'DE', 48.82167000, 8.14310000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q81169'),
(26354, 'Ifta', 3015, 'TH', 82, 'DE', 51.06667000, 10.18333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q665813'),
(26355, 'Igel', 3019, 'RP', 82, 'DE', 49.71033000, 6.55498000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q543497'),
(26356, 'Igensdorf', 3009, 'BY', 82, 'DE', 49.62322000, 11.23137000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q502705'),
(26357, 'Igersheim', 3006, 'BW', 82, 'DE', 49.49444000, 9.81694000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q61925'),
(26358, 'Iggensbach', 3009, 'BY', 82, 'DE', 48.73014000, 13.14229000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32061518'),
(26359, 'Iggingen', 3006, 'BW', 82, 'DE', 48.83167000, 9.87894000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q513598'),
(26360, 'Ihlienworth', 3008, 'NI', 82, 'DE', 53.73333000, 8.91667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q621225'),
(26361, 'Ihringen', 3006, 'BW', 82, 'DE', 48.04303000, 7.64760000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q550630'),
(26362, 'Ihrlerstein', 3009, 'BY', 82, 'DE', 48.93333000, 11.86667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32061532'),
(26363, 'Ilberstedt', 3011, 'ST', 82, 'DE', 51.80000000, 11.66667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32061532'),
(26364, 'Ilbesheim', 3019, 'RP', 82, 'DE', 49.18277000, 8.05363000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32061542'),
(26365, 'Ilfeld', 3015, 'TH', 82, 'DE', 51.57570000, 10.78469000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q684876'),
(26366, 'Illerrieden', 3006, 'BW', 82, 'DE', 48.27017000, 10.05155000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q515510'),
(26367, 'Illertissen', 3009, 'BY', 82, 'DE', 48.22336000, 10.10347000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q503295'),
(26368, 'Illingen', 3006, 'BW', 82, 'DE', 48.95616000, 8.92459000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q503295'),
(26369, 'Illingen', 3020, 'SL', 82, 'DE', 49.37362000, 7.04758000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q517653'),
(26370, 'Illmensee', 3006, 'BW', 82, 'DE', 47.86229000, 9.37235000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q83035'),
(26371, 'Illschwang', 3009, 'BY', 82, 'DE', 49.45000000, 11.68333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q512127'),
(26372, 'Ilmenau', 3015, 'TH', 82, 'DE', 50.68322000, 10.91858000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q205929'),
(26373, 'Ilmmünster', 3009, 'BY', 82, 'DE', 48.48333000, 11.50000000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q49284540'),
(26374, 'Ilsede', 3008, 'NI', 82, 'DE', 52.26340000, 10.19922000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q428515'),
(26375, 'Ilsenburg', 3011, 'ST', 82, 'DE', 51.86695000, 10.67817000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q529928'),
(26376, 'Ilsfeld', 3006, 'BW', 82, 'DE', 49.05548000, 9.24598000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q519972'),
(26377, 'Ilshofen', 3006, 'BW', 82, 'DE', 49.17015000, 9.91825000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q81070'),
(26378, 'Ilvesheim', 3006, 'BW', 82, 'DE', 49.47404000, 8.56740000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q22958'),
(26379, 'Immelborn', 3015, 'TH', 82, 'DE', 50.79229000, 10.27812000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q635635'),
(26380, 'Immendingen', 3006, 'BW', 82, 'DE', 47.93333000, 8.73333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q520808'),
(26381, 'Immenhausen', 3018, 'HE', 82, 'DE', 51.42763000, 9.48017000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q570052'),
(26382, 'Immenreuth', 3009, 'BY', 82, 'DE', 49.90000000, 11.86667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q547548'),
(26383, 'Immenstaad am Bodensee', 3006, 'BW', 82, 'DE', 47.66667000, 9.36667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q528029'),
(26384, 'Immenstadt im Allgäu', 3009, 'BY', 82, 'DE', 47.55996000, 10.21394000, '2019-10-05 22:41:07', '2020-05-01 17:22:47', 1, 'Q506278'),
(26385, 'Imsbach', 3019, 'RP', 82, 'DE', 49.58333000, 7.88333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q569166'),
(26386, 'Inchenhofen', 3009, 'BY', 82, 'DE', 48.51288000, 11.11458000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q259473'),
(26387, 'Inden', 3017, 'NW', 82, 'DE', 50.84306000, 6.36004000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q259473'),
(26388, 'Ingelfingen', 3006, 'BW', 82, 'DE', 49.30028000, 9.65303000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q509666'),
(26389, 'Ingelheim am Rhein', 3019, 'RP', 82, 'DE', 49.97078000, 8.05883000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q159548'),
(26390, 'Ingersleben', 3015, 'TH', 82, 'DE', 50.92167000, 10.93646000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q263260'),
(26391, 'Ingoldingen', 3006, 'BW', 82, 'DE', 48.02552000, 9.74195000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q518927'),
(26392, 'Ingolstadt', 3009, 'BY', 82, 'DE', 48.76508000, 11.42372000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q3004'),
(26393, 'Innernzell', 3009, 'BY', 82, 'DE', 48.85150000, 13.27539000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32062439'),
(26394, 'Inning am Ammersee', 3009, 'BY', 82, 'DE', 48.07636000, 11.15232000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q310856'),
(26395, 'Inning am Holz', 3009, 'BY', 82, 'DE', 48.34687000, 12.07506000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q512860'),
(26396, 'Insheim', 3019, 'RP', 82, 'DE', 49.15806000, 8.14722000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q569100'),
(26397, 'Insingen', 3009, 'BY', 82, 'DE', 49.30000000, 10.16667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q503574'),
(26398, 'Inzell', 3009, 'BY', 82, 'DE', 47.76302000, 12.75146000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q252537'),
(26399, 'Inzigkofen', 3006, 'BW', 82, 'DE', 48.07034000, 9.17998000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q83046'),
(26400, 'Inzlingen', 3006, 'BW', 82, 'DE', 47.58851000, 7.69094000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q61645'),
(26401, 'Iphofen', 3009, 'BY', 82, 'DE', 49.70239000, 10.26037000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q502741'),
(26402, 'Ippesheim', 3009, 'BY', 82, 'DE', 49.60214000, 10.22552000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q505372'),
(26403, 'Ipsheim', 3009, 'BY', 82, 'DE', 49.52750000, 10.48176000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q505288'),
(26404, 'Irchenrieth', 3009, 'BY', 82, 'DE', 49.62286000, 12.22495000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q503479'),
(26405, 'Irlbach', 3009, 'BY', 82, 'DE', 48.84128000, 12.75135000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q583216'),
(26406, 'Irrel', 3019, 'RP', 82, 'DE', 49.84539000, 6.45705000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q553341'),
(26407, 'Irsch', 3019, 'RP', 82, 'DE', 49.72620000, 6.69806000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q553341'),
(26408, 'Irschenberg', 3009, 'BY', 82, 'DE', 47.83333000, 11.91667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q165036'),
(26409, 'Irsee', 3009, 'BY', 82, 'DE', 47.90815000, 10.57177000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q510845'),
(26410, 'Irxleben', 3011, 'ST', 82, 'DE', 52.16689000, 11.48064000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q818993'),
(26411, 'Isen', 3009, 'BY', 82, 'DE', 48.21203000, 12.05672000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q127882'),
(26412, 'Isenbüttel', 3008, 'NI', 82, 'DE', 52.43333000, 10.58333000, '2019-10-05 22:41:07', '2020-05-01 17:22:48', 1, 'Q640185'),
(26413, 'Iserlohn', 3017, 'NW', 82, 'DE', 51.37547000, 7.70281000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q1130'),
(26414, 'Isernhagen Farster Bauerschaft', 3008, 'NI', 82, 'DE', 52.47093000, 9.84179000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q1397170'),
(26415, 'Ismaning', 3009, 'BY', 82, 'DE', 48.23333000, 11.68333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q254897'),
(26416, 'Isny', 3006, 'BW', 82, 'DE', 47.69260000, 10.03857000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q513446'),
(26417, 'Ispringen', 3006, 'BW', 82, 'DE', 48.91667000, 8.66667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q81719'),
(26418, 'Isselburg', 3017, 'NW', 82, 'DE', 51.83232000, 6.46428000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32062704'),
(26419, 'Issum', 3017, 'NW', 82, 'DE', 51.53333000, 6.43333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q243337'),
(26420, 'Itterbeck', 3008, 'NI', 82, 'DE', 52.50618000, 6.80354000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q606082'),
(26421, 'Ittlingen', 3006, 'BW', 82, 'DE', 49.19083000, 8.93083000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q519997'),
(26422, 'Itzehoe', 3005, 'SH', 82, 'DE', 53.92099000, 9.51529000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q16073'),
(26423, 'Itzstedt', 3005, 'SH', 82, 'DE', 53.80800000, 10.15797000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q16073'),
(26424, 'Jacobsdorf', 3013, 'BB', 82, 'DE', 52.33333000, 14.35000000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q636991'),
(26425, 'Jagsthausen', 3006, 'BW', 82, 'DE', 49.31056000, 9.46833000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q167823'),
(26426, 'Jagstzell', 3006, 'BW', 82, 'DE', 49.03055000, 10.09751000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q513462'),
(26427, 'Jahnsdorf', 3021, 'SN', 82, 'DE', 50.74509000, 12.85414000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q57790'),
(26428, 'Jameln', 3008, 'NI', 82, 'DE', 53.05000000, 11.08333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q573615'),
(26429, 'Jandelsbrunn', 3009, 'BY', 82, 'DE', 48.73333000, 13.70000000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32062950'),
(26430, 'Jarmen', 3007, 'MV', 82, 'DE', 53.92385000, 13.34032000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q571979'),
(26431, 'Jarplund-Weding', 3005, 'SH', 82, 'DE', 54.73714000, 9.41813000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q819221'),
(26432, 'Jatznick', 3007, 'MV', 82, 'DE', 53.57951000, 13.93955000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32063009'),
(26433, 'Jembke', 3008, 'NI', 82, 'DE', 52.50000000, 10.76667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q630769'),
(26434, 'Jemgum', 3008, 'NI', 82, 'DE', 53.26667000, 7.38333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q559501'),
(26435, 'Jena', 3015, 'TH', 82, 'DE', 50.92878000, 11.58990000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q559501'),
(26436, 'Jengen', 3009, 'BY', 82, 'DE', 47.99934000, 10.72575000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q513796'),
(26437, 'Jerichow', 3011, 'ST', 82, 'DE', 52.50049000, 12.02383000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q530090'),
(26438, 'Jerrishoe', 3005, 'SH', 82, 'DE', 54.65670000, 9.36918000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q530090'),
(26439, 'Jersbek', 3005, 'SH', 82, 'DE', 53.73333000, 10.21667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q567419'),
(26440, 'Jerxheim', 3008, 'NI', 82, 'DE', 52.08172000, 10.89844000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q605531'),
(26441, 'Jesberg', 3018, 'HE', 82, 'DE', 51.00000000, 9.15000000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q605531'),
(26442, 'Jesenwang', 3009, 'BY', 82, 'DE', 48.16667000, 11.13333000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32063115'),
(26443, 'Jesewitz', 3021, 'SN', 82, 'DE', 51.42276000, 12.56312000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q16047'),
(26444, 'Jessen', 3011, 'ST', 82, 'DE', 51.79337000, 12.95762000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32063121'),
(26445, 'Jesteburg', 3008, 'NI', 82, 'DE', 53.30966000, 9.95262000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q638271'),
(26446, 'Jestetten', 3006, 'BW', 82, 'DE', 47.65000000, 8.56667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q181389'),
(26447, 'Jettingen-Scheppach', 3009, 'BY', 82, 'DE', 48.38960000, 10.43810000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q505985'),
(26448, 'Jetzendorf', 3009, 'BY', 82, 'DE', 48.43333000, 11.41667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q32063159'),
(26449, 'Jevenstedt', 3005, 'SH', 82, 'DE', 54.23333000, 9.66667000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q556659'),
(26450, 'Jever', 3008, 'NI', 82, 'DE', 53.57368000, 7.89806000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q31084'),
(26451, 'Jeßnitz', 3011, 'ST', 82, 'DE', 51.68307000, 12.29992000, '2019-10-05 22:41:07', '2020-05-01 17:22:49', 1, 'Q701018'),
(26452, 'Joachimsthal', 3013, 'BB', 82, 'DE', 52.97945000, 13.74493000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q535551'),
(26453, 'Jockgrim', 3019, 'RP', 82, 'DE', 49.09288000, 8.27468000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q22974'),
(26454, 'Johannesberg', 3009, 'BY', 82, 'DE', 50.03159000, 9.14252000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q22974'),
(26455, 'Johanngeorgenstadt', 3021, 'SN', 82, 'DE', 50.43254000, 12.71140000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q57903'),
(26456, 'Johanniskirchen', 3009, 'BY', 82, 'DE', 48.53333000, 12.95000000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q553798'),
(26457, 'Johannisthal', 3010, 'BE', 82, 'DE', 52.44653000, 13.50660000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q655012'),
(26458, 'Jork', 3008, 'NI', 82, 'DE', 53.53198000, 9.68076000, '2019-10-05 22:41:07', '2019-10-05 22:41:07', 1, 'Q504279'),
(26459, 'Judenbach', 3015, 'TH', 82, 'DE', 50.39591000, 11.22099000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q637466'),
(26460, 'Jugenheim', 3019, 'RP', 82, 'DE', 49.89362000, 8.08468000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32063375'),
(26461, 'Juist', 3008, 'NI', 82, 'DE', 53.67787000, 6.99575000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q24846'),
(26462, 'Julbach', 3009, 'BY', 82, 'DE', 48.25430000, 12.95793000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q24846'),
(26463, 'Jungingen', 3006, 'BW', 82, 'DE', 48.32787000, 9.04095000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q538630'),
(26464, 'Jänschwalde', 3013, 'BB', 82, 'DE', 51.86066000, 14.49813000, '2019-10-05 22:41:08', '2020-05-01 17:22:48', 1, 'Q572540'),
(26465, 'Jävenitz', 3011, 'ST', 82, 'DE', 52.52410000, 11.49909000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q675920'),
(26466, 'Jöhlingen', 3006, 'BW', 82, 'DE', 49.03218000, 8.57350000, '2019-10-05 22:41:08', '2020-05-01 17:22:47', 1, 'Q18018048'),
(26467, 'Jöhstadt', 3021, 'SN', 82, 'DE', 50.51229000, 13.09460000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q57908'),
(26468, 'Jördenstorf', 3007, 'MV', 82, 'DE', 53.87823000, 12.61619000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q671011'),
(26469, 'Jübek', 3005, 'SH', 82, 'DE', 54.55709000, 9.39773000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q556966'),
(26470, 'Jüchen', 3017, 'NW', 82, 'DE', 51.10000000, 6.50000000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q255416'),
(26471, 'Jüchsen', 3015, 'TH', 82, 'DE', 50.47993000, 10.50183000, '2019-10-05 22:41:08', '2020-05-01 17:22:50', 1, 'Q519182'),
(26472, 'Jühnde', 3008, 'NI', 82, 'DE', 51.46667000, 9.80000000, '2019-10-05 22:41:08', '2020-05-01 17:22:48', 1, 'Q552285'),
(26473, 'Jülich', 3017, 'NW', 82, 'DE', 50.92149000, 6.36267000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q16045'),
(26474, 'Jünkerath', 3019, 'RP', 82, 'DE', 50.34412000, 6.58138000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q553521'),
(26475, 'Jürgenshagen', 3007, 'MV', 82, 'DE', 53.95417000, 11.89665000, '2019-10-05 22:41:08', '2020-05-01 17:22:49', 1, 'Q669552'),
(26476, 'Jüterbog', 3013, 'BB', 82, 'DE', 51.99607000, 13.07979000, '2019-10-05 22:41:08', '2020-05-01 17:22:48', 1, 'Q486541'),
(26477, 'Kaarst', 3017, 'NW', 82, 'DE', 51.22929000, 6.61883000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q7088'),
(26478, 'Kadenbach', 3019, 'RP', 82, 'DE', 50.38333000, 7.73333000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q640427'),
(26479, 'Kahl am Main', 3009, 'BY', 82, 'DE', 50.06981000, 9.00553000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q175618'),
(26480, 'Kahla', 3015, 'TH', 82, 'DE', 50.80651000, 11.58516000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q550808'),
(26481, 'Kaisersbach', 3006, 'BW', 82, 'DE', 48.93023000, 9.63898000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q81124'),
(26482, 'Kaisersesch', 3019, 'RP', 82, 'DE', 50.23148000, 7.13864000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q552516'),
(26483, 'Kaiserslautern', 3019, 'RP', 82, 'DE', 49.44300000, 7.77161000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q552516'),
(26484, 'Kaisheim', 3009, 'BY', 82, 'DE', 48.76751000, 10.79639000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q505166'),
(26485, 'Kakenstorf', 3008, 'NI', 82, 'DE', 53.30317000, 9.76289000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q633344'),
(26486, 'Kalbe', 3011, 'ST', 82, 'DE', 52.65656000, 11.38456000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32063702'),
(26487, 'Kalchreuth', 3009, 'BY', 82, 'DE', 49.55785000, 11.13350000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q507773'),
(26488, 'Kalefeld', 3008, 'NI', 82, 'DE', 51.80000000, 10.03333000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q559428'),
(26489, 'Kalk', 3017, 'NW', 82, 'DE', 50.94013000, 7.00605000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q559428'),
(26490, 'Kalkar', 3017, 'NW', 82, 'DE', 51.73907000, 6.29101000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q243554'),
(26491, 'Kalkhorst', 3007, 'MV', 82, 'DE', 53.97012000, 11.04469000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q566703'),
(26492, 'Kall', 3017, 'NW', 82, 'DE', 50.54223000, 6.56297000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32063745'),
(26493, 'Kallmünz', 3009, 'BY', 82, 'DE', 49.16094000, 11.96051000, '2019-10-05 22:41:08', '2020-05-01 17:22:47', 1, 'Q490185'),
(26494, 'Kallstadt', 3019, 'RP', 82, 'DE', 49.49083000, 8.17611000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q22537'),
(26495, 'Kaltenengers', 3019, 'RP', 82, 'DE', 50.41667000, 7.55000000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q566947'),
(26496, 'Kaltenkirchen', 3005, 'SH', 82, 'DE', 53.83292000, 9.95810000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q492390'),
(26497, 'Kaltennordheim', 3015, 'TH', 82, 'DE', 50.62649000, 10.15915000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q372482'),
(26498, 'Kaltenwestheim', 3015, 'TH', 82, 'DE', 50.60991000, 10.11692000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q372482'),
(26499, 'Kamen', 3017, 'NW', 82, 'DE', 51.59231000, 7.66380000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q7028'),
(26500, 'Kamenz', 3021, 'SN', 82, 'DE', 51.26798000, 14.09374000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q81722'),
(26501, 'Kammerstein', 3009, 'BY', 82, 'DE', 49.29317000, 10.97277000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q527430'),
(26502, 'Kamp-Bornhofen', 3019, 'RP', 82, 'DE', 50.22282000, 7.62364000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q567183'),
(26503, 'Kamp-Lintfort', 3017, 'NW', 82, 'DE', 51.50467000, 6.54587000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q14895'),
(26504, 'Kamsdorf', 3015, 'TH', 82, 'DE', 50.64312000, 11.45401000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q554935'),
(26505, 'Kandel', 3019, 'RP', 82, 'DE', 49.08277000, 8.19720000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32063873'),
(26506, 'Kandern', 3006, 'BW', 82, 'DE', 47.71393000, 7.66237000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q61661'),
(26507, 'Kannawurf', 3015, 'TH', 82, 'DE', 51.26667000, 11.13333000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q556468'),
(26508, 'Kappel-Grafenhausen', 3006, 'BW', 82, 'DE', 48.28382000, 7.76605000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q527580'),
(26509, 'Kappeln', 3005, 'SH', 82, 'DE', 54.66122000, 9.93130000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q516567'),
(26510, 'Kappelrodeck', 3006, 'BW', 82, 'DE', 48.59182000, 8.11692000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q538599'),
(26511, 'Kapsweyer', 3019, 'RP', 82, 'DE', 49.04389000, 8.02167000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q564476'),
(26512, 'Karbach', 3009, 'BY', 82, 'DE', 49.86701000, 9.63806000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q546100'),
(26513, 'Karben', 3018, 'HE', 82, 'DE', 50.23019000, 8.77155000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q570057'),
(26514, 'Karlsdorf-Neuthard', 3006, 'BW', 82, 'DE', 49.13472000, 8.53028000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q542470'),
(26515, 'Karlsfeld', 3009, 'BY', 82, 'DE', 48.22697000, 11.47573000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q268707'),
(26516, 'Karlshagen', 3007, 'MV', 82, 'DE', 54.11107000, 13.83193000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q660806'),
(26517, 'Karlshorst', 3010, 'BE', 82, 'DE', 52.48419000, 13.53185000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q703218'),
(26518, 'Karlshuld', 3009, 'BY', 82, 'DE', 48.68190000, 11.28503000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q505424'),
(26519, 'Karlskron', 3009, 'BY', 82, 'DE', 48.68333000, 11.41667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q505278'),
(26520, 'Karlsruhe', 3006, 'BW', 82, 'DE', 49.00937000, 8.40444000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q1040'),
(26521, 'Karlsruhe Region', 3006, 'BW', 82, 'DE', 49.00000000, 8.66667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q1040'),
(26522, 'Karlstadt', 3009, 'BY', 82, 'DE', 49.96034000, 9.77239000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q507854'),
(26523, 'Karow', 3010, 'BE', 82, 'DE', 52.60904000, 13.48117000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q700184'),
(26524, 'Karsbach', 3009, 'BY', 82, 'DE', 50.04091000, 9.78534000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q35618610'),
(26525, 'Karsdorf', 3011, 'ST', 82, 'DE', 51.27134000, 11.65775000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q555245'),
(26526, 'Karstädt', 3013, 'BB', 82, 'DE', 53.16215000, 11.74242000, '2019-10-05 22:41:08', '2020-05-01 17:22:48', 1, 'Q627161'),
(26527, 'Kasbach-Ohlenberg', 3019, 'RP', 82, 'DE', 50.58333000, 7.26667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q283876'),
(26528, 'Kasel', 3019, 'RP', 82, 'DE', 49.76170000, 6.73222000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q283876'),
(26529, 'Kasendorf', 3009, 'BY', 82, 'DE', 50.03665000, 11.35203000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q504673'),
(26530, 'Kasseedorf', 3005, 'SH', 82, 'DE', 54.16667000, 10.71667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q546789'),
(26531, 'Kassel', 3018, 'HE', 82, 'DE', 51.31667000, 9.50000000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q2865'),
(26532, 'Kastellaun', 3019, 'RP', 82, 'DE', 50.06922000, 7.44154000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q539726'),
(26533, 'Kastl', 3009, 'BY', 82, 'DE', 49.36960000, 11.68261000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q705730'),
(26534, 'Kastorf', 3005, 'SH', 82, 'DE', 53.74569000, 10.56723000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q705730'),
(26535, 'Katharinenberg', 3015, 'TH', 82, 'DE', 51.18137000, 10.26084000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32064103'),
(26536, 'Katlenburg-Lindau', 3008, 'NI', 82, 'DE', 51.68333000, 10.10000000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q501616'),
(26537, 'Katzenelnbogen', 3019, 'RP', 82, 'DE', 50.26745000, 7.97322000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q636133'),
(26538, 'Katzhütte', 3015, 'TH', 82, 'DE', 50.55191000, 11.05293000, '2019-10-05 22:41:08', '2020-05-01 17:22:50', 1, 'Q559825'),
(26539, 'Katzweiler', 3019, 'RP', 82, 'DE', 49.50000000, 7.70000000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q567122'),
(26540, 'Katzwinkel', 3019, 'RP', 82, 'DE', 50.81470000, 7.82236000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q656023'),
(26541, 'Kaub', 3019, 'RP', 82, 'DE', 50.08831000, 7.76069000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q540860'),
(26542, 'Kaufbeuren', 3009, 'BY', 82, 'DE', 47.88238000, 10.62192000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q14805'),
(26543, 'Kaufering', 3009, 'BY', 82, 'DE', 48.09121000, 10.87913000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q14805'),
(26544, 'Kaufungen', 3018, 'HE', 82, 'DE', 51.28111000, 9.61861000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q564457'),
(26545, 'Kaulsdorf', 3010, 'BE', 82, 'DE', 52.51732000, 13.58871000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q665953'),
(26546, 'Kaulsdorf', 3015, 'TH', 82, 'DE', 50.61670000, 11.43295000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q559705'),
(26547, 'Kavelstorf', 3007, 'MV', 82, 'DE', 54.00596000, 12.19082000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q679276'),
(26548, 'Kayhude', 3005, 'SH', 82, 'DE', 53.75629000, 10.13232000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q679276'),
(26549, 'Kayna', 3011, 'ST', 82, 'DE', 50.99414000, 12.23710000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q704407'),
(26550, 'Kefenrod', 3018, 'HE', 82, 'DE', 50.34475000, 9.21141000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q625373'),
(26551, 'Kehl', 3006, 'BW', 82, 'DE', 48.57297000, 7.81523000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q15979'),
(26552, 'Kehrig', 3019, 'RP', 82, 'DE', 50.28333000, 7.21667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q565808'),
(26553, 'Keitum', 3005, 'SH', 82, 'DE', 54.89333000, 8.37083000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q20767'),
(26554, 'Kelberg', 3019, 'RP', 82, 'DE', 50.29164000, 6.91950000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q552009'),
(26555, 'Kelbra', 3011, 'ST', 82, 'DE', 51.43528000, 11.04143000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q539059'),
(26556, 'Kelheim', 3009, 'BY', 82, 'DE', 48.91725000, 11.88618000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q494305'),
(26557, 'Kelkheim (Taunus)', 3018, 'HE', 82, 'DE', 50.13703000, 8.45020000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q639667'),
(26558, 'Kell', 3019, 'RP', 82, 'DE', 49.63425000, 6.82390000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32064544'),
(26559, 'Kellenhusen', 3005, 'SH', 82, 'DE', 54.19338000, 11.06165000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q551387'),
(26560, 'Kellinghusen', 3005, 'SH', 82, 'DE', 53.95202000, 9.71959000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q515943'),
(26561, 'Kellmünz', 3009, 'BY', 82, 'DE', 48.12159000, 10.12811000, '2019-10-05 22:41:08', '2020-05-01 17:22:47', 1, 'Q515825'),
(26562, 'Kelsterbach', 3018, 'HE', 82, 'DE', 50.06135000, 8.52916000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q635547'),
(26563, 'Kemberg', 3011, 'ST', 82, 'DE', 51.77189000, 12.63227000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q534792'),
(26564, 'Kemmern', 3009, 'BY', 82, 'DE', 49.95486000, 10.87784000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q529400'),
(26565, 'Kemnath', 3009, 'BY', 82, 'DE', 49.87007000, 11.89077000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q516262'),
(26566, 'Kemnitz', 3007, 'MV', 82, 'DE', 54.07723000, 13.53528000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32064598'),
(26567, 'Kempen', 3017, 'NW', 82, 'DE', 51.36432000, 6.41858000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32064602'),
(26568, 'Kempenich', 3019, 'RP', 82, 'DE', 50.42024000, 7.11698000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q563845'),
(26569, 'Kempten (Allgäu)', 3009, 'BY', 82, 'DE', 47.72674000, 10.31389000, '2019-10-05 22:41:08', '2020-05-01 17:22:47', 1, 'Q563845'),
(26570, 'Kenn', 3019, 'RP', 82, 'DE', 49.80000000, 6.71667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q563845'),
(26571, 'Kenzingen', 3006, 'BW', 82, 'DE', 48.19630000, 7.76974000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q550623'),
(26572, 'Kerpen', 3017, 'NW', 82, 'DE', 50.86991000, 6.69691000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q3959'),
(26573, 'Kerzenheim', 3019, 'RP', 82, 'DE', 49.57639000, 8.05972000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q566736'),
(26574, 'Kessin', 3007, 'MV', 82, 'DE', 53.72928000, 13.30773000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32064788'),
(26575, 'Ketsch', 3006, 'BW', 82, 'DE', 49.36778000, 8.53111000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q22959'),
(26576, 'Kettenkamp', 3008, 'NI', 82, 'DE', 52.58333000, 7.83333000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q649927'),
(26577, 'Kettershausen', 3009, 'BY', 82, 'DE', 48.18333000, 10.26667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q628957'),
(26578, 'Kettig', 3019, 'RP', 82, 'DE', 50.40000000, 7.46667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q628957'),
(26579, 'Ketzin', 3013, 'BB', 82, 'DE', 52.47809000, 12.84530000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q583450'),
(26580, 'Kevelaer', 3017, 'NW', 82, 'DE', 51.58243000, 6.24603000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q243526'),
(26581, 'Kiebitzreihe', 3005, 'SH', 82, 'DE', 53.78333000, 9.61667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q243526'),
(26582, 'Kiedrich', 3018, 'HE', 82, 'DE', 50.03965000, 8.08531000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q627351'),
(26583, 'Kiefersfelden', 3009, 'BY', 82, 'DE', 47.61409000, 12.19096000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q509310'),
(26584, 'Kiel', 3005, 'SH', 82, 'DE', 54.32133000, 10.13489000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q1707'),
(26585, 'Kienberg', 3009, 'BY', 82, 'DE', 48.03481000, 12.46330000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q1707'),
(26586, 'Kierspe', 3017, 'NW', 82, 'DE', 51.13403000, 7.59075000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q5569'),
(26587, 'Kieselbronn', 3006, 'BW', 82, 'DE', 48.93333000, 8.75000000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q80587'),
(26588, 'Kindelbrück', 3015, 'TH', 82, 'DE', 51.26171000, 11.08999000, '2019-10-05 22:41:08', '2020-05-01 17:22:50', 1, 'Q530154'),
(26589, 'Kindenheim', 3019, 'RP', 82, 'DE', 49.61111000, 8.16417000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q22535'),
(26590, 'Kinderbeuern', 3019, 'RP', 82, 'DE', 50.00935000, 7.02355000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q569288'),
(26591, 'Kindsbach', 3019, 'RP', 82, 'DE', 49.41667000, 7.61667000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q567142'),
(26592, 'Kippenheim', 3006, 'BW', 82, 'DE', 48.29564000, 7.82510000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q527898'),
(26593, 'Kirchanschöring', 3009, 'BY', 82, 'DE', 47.95303000, 12.83435000, '2019-10-05 22:41:08', '2020-05-01 17:22:47', 1, 'Q265312'),
(26594, 'Kirchardt', 3006, 'BW', 82, 'DE', 49.20500000, 8.99167000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q539754'),
(26595, 'Kirchberg', 3021, 'SN', 82, 'DE', 50.62190000, 12.52449000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q32065157'),
(26596, 'Kirchberg', 3019, 'RP', 82, 'DE', 49.94395000, 7.40700000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q632275'),
(26597, 'Kirchberg', 3009, 'BY', 82, 'DE', 48.90006000, 13.18200000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q491396'),
(26598, 'Kirchberg an der Iller', 3006, 'BW', 82, 'DE', 48.13333000, 10.08333000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q520769'),
(26599, 'Kirchberg an der Jagst', 3006, 'BW', 82, 'DE', 49.20056000, 9.98226000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q82372'),
(26600, 'Kirchberg an der Murr', 3006, 'BW', 82, 'DE', 48.94297000, 9.34083000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q81216'),
(26601, 'Kirchbrak', 3008, 'NI', 82, 'DE', 51.96585000, 9.57510000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q549830'),
(26602, 'Kirchdorf', 3009, 'BY', 82, 'DE', 48.45942000, 11.65438000, '2019-10-05 22:41:08', '2019-10-05 22:41:08', 1, 'Q506009'),
(26603, 'Kirchdorf', 3006, 'BW', 82, 'DE', 48.07667000, 10.12629000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q506009'),
(26604, 'Kirchdorf', 3008, 'NI', 82, 'DE', 52.59523000, 8.83490000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q506009'),
(26605, 'Kirchdorf am Inn', 3009, 'BY', 82, 'DE', 48.24755000, 12.98453000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q583404'),
(26606, 'Kirchdorf im Wald', 3009, 'BY', 82, 'DE', 48.91048000, 13.26614000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q489164'),
(26607, 'Kirchehrenbach', 3009, 'BY', 82, 'DE', 49.73333000, 11.15000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q489164'),
(26608, 'Kirchen', 3019, 'RP', 82, 'DE', 50.80849000, 7.88634000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q532613'),
(26609, 'Kirchenlamitz', 3009, 'BY', 82, 'DE', 50.15190000, 11.94831000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q507741'),
(26610, 'Kirchenpingarten', 3009, 'BY', 82, 'DE', 49.93333000, 11.78333000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q507741'),
(26611, 'Kirchensittenbach', 3009, 'BY', 82, 'DE', 49.55601000, 11.42226000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q373332'),
(26612, 'Kirchentellinsfurt', 3006, 'BW', 82, 'DE', 48.53315000, 9.14732000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q521318'),
(26613, 'Kirchenthumbach', 3009, 'BY', 82, 'DE', 49.74866000, 11.72542000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q503393'),
(26614, 'Kirchgellersen', 3008, 'NI', 82, 'DE', 53.23333000, 10.30000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q476856'),
(26615, 'Kirchhain', 3018, 'HE', 82, 'DE', 50.82720000, 8.92806000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q569684'),
(26616, 'Kirchham', 3009, 'BY', 82, 'DE', 48.34638000, 13.26719000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q569684'),
(26617, 'Kirchhaslach', 3009, 'BY', 82, 'DE', 48.15040000, 10.31015000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q548525'),
(26618, 'Kirchheim', 3015, 'TH', 82, 'DE', 50.88333000, 11.01667000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q548525'),
(26619, 'Kirchheim', 3009, 'BY', 82, 'DE', 48.17490000, 10.47461000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q548525'),
(26620, 'Kirchheim', 3018, 'HE', 82, 'DE', 50.83333000, 9.56667000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q548525'),
(26621, 'Kirchheim am Neckar', 3006, 'BW', 82, 'DE', 49.04500000, 9.14222000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q62055'),
(26622, 'Kirchheim am Ries', 3006, 'BW', 82, 'DE', 48.87922000, 10.40028000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q282312'),
(26623, 'Kirchheim an der Weinstraße', 3019, 'RP', 82, 'DE', 49.53722000, 8.18083000, '2019-10-05 22:41:09', '2020-05-01 17:22:49', 1, 'Q22975'),
(26624, 'Kirchheim bei München', 3009, 'BY', 82, 'DE', 48.17656000, 11.75563000, '2019-10-05 22:41:09', '2020-05-01 17:22:47', 1, 'Q504880'),
(26625, 'Kirchheim unter Teck', 3006, 'BW', 82, 'DE', 48.64683000, 9.45378000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q14866'),
(26626, 'Kirchheimbolanden', 3019, 'RP', 82, 'DE', 49.66249000, 8.01513000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q200763'),
(26627, 'Kirchhundem', 3017, 'NW', 82, 'DE', 51.08556000, 8.08893000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q10906'),
(26628, 'Kirchlauter', 3009, 'BY', 82, 'DE', 50.04430000, 10.71776000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q503872'),
(26629, 'Kirchlengern', 3017, 'NW', 82, 'DE', 52.20000000, 8.63333000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q53911'),
(26630, 'Kirchlinteln', 3008, 'NI', 82, 'DE', 52.94236000, 9.31811000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q504134'),
(26631, 'Kirchroth', 3009, 'BY', 82, 'DE', 48.95000000, 12.55000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q179520'),
(26632, 'Kirchseelte', 3008, 'NI', 82, 'DE', 52.95000000, 8.68333000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q179520'),
(26633, 'Kirchseeon', 3009, 'BY', 82, 'DE', 48.07143000, 11.88875000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q521078'),
(26634, 'Kirchtimke', 3008, 'NI', 82, 'DE', 53.25000000, 9.15000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q506804'),
(26635, 'Kirchwald', 3019, 'RP', 82, 'DE', 50.36667000, 7.15000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q629359'),
(26636, 'Kirchwalsede', 3008, 'NI', 82, 'DE', 53.01667000, 9.40000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q629359'),
(26637, 'Kirchweidach', 3009, 'BY', 82, 'DE', 48.08575000, 12.64530000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q701470'),
(26638, 'Kirchworbis', 3015, 'TH', 82, 'DE', 51.41122000, 10.39625000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q552685'),
(26639, 'Kirchzarten', 3006, 'BW', 82, 'DE', 47.96667000, 7.95000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q528089'),
(26640, 'Kirchzell', 3009, 'BY', 82, 'DE', 49.61800000, 9.17785000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q507292'),
(26641, 'Kirkel', 3020, 'SL', 82, 'DE', 49.28333000, 7.23333000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q553589'),
(26642, 'Kirn', 3019, 'RP', 82, 'DE', 49.78912000, 7.45765000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q22470'),
(26643, 'Kirrweiler', 3019, 'RP', 82, 'DE', 49.30124000, 8.16288000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q654122'),
(26644, 'Kirschau', 3021, 'SN', 82, 'DE', 51.09317000, 14.42840000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q550175'),
(26645, 'Kirschweiler', 3019, 'RP', 82, 'DE', 49.75000000, 7.25000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q377642'),
(26646, 'Kirtorf', 3018, 'HE', 82, 'DE', 50.76942000, 9.10389000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q570077'),
(26647, 'Kisdorf', 3005, 'SH', 82, 'DE', 53.81667000, 10.01667000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q533366'),
(26648, 'Kissenbrück', 3008, 'NI', 82, 'DE', 52.10956000, 10.58996000, '2019-10-05 22:41:09', '2020-05-01 17:22:48', 1, 'Q533366'),
(26649, 'Kissing', 3009, 'BY', 82, 'DE', 48.30375000, 10.97088000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q533366'),
(26650, 'Kist', 3009, 'BY', 82, 'DE', 49.74278000, 9.84389000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q533366'),
(26651, 'Kitzen', 3021, 'SN', 82, 'DE', 51.22172000, 12.22349000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q663696'),
(26652, 'Kitzingen', 3009, 'BY', 82, 'DE', 49.73973000, 10.15072000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q269447'),
(26653, 'Kitzscher', 3021, 'SN', 82, 'DE', 51.16444000, 12.55260000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q10768'),
(26654, 'Kißlegg', 3006, 'BW', 82, 'DE', 47.78894000, 9.88383000, '2019-10-05 22:41:09', '2020-05-01 17:22:47', 1, 'Q705800'),
(26655, 'Kladow', 3010, 'BE', 82, 'DE', 52.45423000, 13.14445000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q519632'),
(26656, 'Klausdorf', 3005, 'SH', 82, 'DE', 54.30899000, 10.21372000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q690151'),
(26657, 'Klausen', 3019, 'RP', 82, 'DE', 49.90000000, 6.88333000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q690151'),
(26658, 'Klein Berßen', 3008, 'NI', 82, 'DE', 52.76797000, 7.46182000, '2019-10-05 22:41:09', '2020-05-01 17:22:48', 1, 'Q633808'),
(26659, 'Klein Gusborn', 3008, 'NI', 82, 'DE', 53.08331000, 11.19323000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q32065542'),
(26660, 'Klein Nordende', 3005, 'SH', 82, 'DE', 53.72219000, 9.65424000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q554084'),
(26661, 'Klein Offenseth-Sparrieshoop', 3005, 'SH', 82, 'DE', 53.79748000, 9.68653000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q554084'),
(26662, 'Klein Reken', 3017, 'NW', 82, 'DE', 51.78710000, 7.04367000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q29561600'),
(26663, 'Klein Rogahn', 3007, 'MV', 82, 'DE', 53.60534000, 11.34579000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q29561600'),
(26664, 'Klein Rönnau', 3005, 'SH', 82, 'DE', 53.96667000, 10.31667000, '2019-10-05 22:41:09', '2020-05-01 17:22:49', 1, 'Q29561600'),
(26665, 'Klein Schwülper', 3008, 'NI', 82, 'DE', 52.34153000, 10.42903000, '2019-10-05 22:41:09', '2020-05-01 17:22:48', 1, 'Q33452143'),
(26666, 'Klein Wanzleben', 3011, 'ST', 82, 'DE', 52.07005000, 11.36594000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q688074'),
(26667, 'Klein-Winternheim', 3019, 'RP', 82, 'DE', 49.93833000, 8.21194000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q630978'),
(26668, 'Kleinaitingen', 3009, 'BY', 82, 'DE', 48.21850000, 10.86923000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q630978'),
(26669, 'Kleinblittersdorf', 3020, 'SL', 82, 'DE', 49.15780000, 7.03734000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q39781'),
(26670, 'Kleiner Grasbrook', 3016, 'HH', 82, 'DE', 53.53111000, 9.99361000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q1573'),
(26671, 'Kleinfurra', 3015, 'TH', 82, 'DE', 51.41556000, 10.76454000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q1573'),
(26672, 'Kleinheubach', 3009, 'BY', 82, 'DE', 49.72109000, 9.21346000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q508414'),
(26673, 'Kleinkahl', 3009, 'BY', 82, 'DE', 50.11667000, 9.26667000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q509368'),
(26674, 'Kleinlangheim', 3009, 'BY', 82, 'DE', 49.77087000, 10.28430000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q505068'),
(26675, 'Kleinmachnow', 3013, 'BB', 82, 'DE', 52.40786000, 13.22514000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q104192'),
(26676, 'Kleinmaischeid', 3019, 'RP', 82, 'DE', 50.51345000, 7.60830000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q570860'),
(26677, 'Kleinostheim', 3009, 'BY', 82, 'DE', 50.00000000, 9.06667000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q507889'),
(26678, 'Kleinrinderfeld', 3009, 'BY', 82, 'DE', 49.70028000, 9.84472000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q507889'),
(26679, 'Kleinsendelbach', 3009, 'BY', 82, 'DE', 49.59558000, 11.15773000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q503092'),
(26680, 'Kleinwallstadt', 3009, 'BY', 82, 'DE', 49.87464000, 9.16927000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q504686'),
(26681, 'Kleinwenden', 3015, 'TH', 82, 'DE', 51.41611000, 10.65902000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q32066000'),
(26682, 'Klettbach', 3015, 'TH', 82, 'DE', 50.91667000, 11.15000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q651182'),
(26683, 'Kleve', 3017, 'NW', 82, 'DE', 51.78826000, 6.13865000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q6842'),
(26684, 'Klieken', 3011, 'ST', 82, 'DE', 51.88806000, 12.37070000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q697622'),
(26685, 'Klietz', 3011, 'ST', 82, 'DE', 52.66835000, 12.06812000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q634706'),
(26686, 'Klingenberg am Main', 3009, 'BY', 82, 'DE', 49.78507000, 9.18025000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q502934'),
(26687, 'Klingenmünster', 3019, 'RP', 82, 'DE', 49.14056000, 8.01861000, '2019-10-05 22:41:09', '2020-05-01 17:22:49', 1, 'Q564539'),
(26688, 'Klingenstein', 3006, 'BW', 82, 'DE', 48.41849000, 9.90812000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q564539'),
(26689, 'Klingenthal', 3021, 'SN', 82, 'DE', 50.35963000, 12.46463000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q46866'),
(26690, 'Klink', 3007, 'MV', 82, 'DE', 53.47758000, 12.62131000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q32066075'),
(26691, 'Klipphausen', 3021, 'SN', 82, 'DE', 51.06841000, 13.51374000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q8726'),
(26692, 'Klitten', 3021, 'SN', 82, 'DE', 51.35109000, 14.60526000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q703135'),
(26693, 'Kloster Lehnin', 3013, 'BB', 82, 'DE', 52.32039000, 12.74320000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q703135'),
(26694, 'Klosterlechfeld', 3009, 'BY', 82, 'DE', 48.15586000, 10.82986000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q703135'),
(26695, 'Klostermansfeld', 3011, 'ST', 82, 'DE', 51.58333000, 11.50000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q634966'),
(26696, 'Klotten', 3019, 'RP', 82, 'DE', 50.16667000, 7.20000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q269397'),
(26697, 'Kluse', 3008, 'NI', 82, 'DE', 52.93633000, 7.34093000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q269397'),
(26698, 'Klötze', 3011, 'ST', 82, 'DE', 52.62725000, 11.16424000, '2019-10-05 22:41:09', '2020-05-01 17:22:49', 1, 'Q33452384'),
(26699, 'Klüsserath', 3019, 'RP', 82, 'DE', 49.84500000, 6.85086000, '2019-10-05 22:41:09', '2020-05-01 17:22:49', 1, 'Q569589'),
(26700, 'Klütz', 3007, 'MV', 82, 'DE', 53.96470000, 11.16367000, '2019-10-05 22:41:09', '2020-05-01 17:22:49', 1, 'Q507165'),
(26701, 'Knetzgau', 3009, 'BY', 82, 'DE', 49.98333000, 10.55000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q506509'),
(26702, 'Knittelsheim', 3019, 'RP', 82, 'DE', 49.19167000, 8.25139000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q431500'),
(26703, 'Knittlingen', 3006, 'BW', 82, 'DE', 49.02487000, 8.75606000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q80565'),
(26704, 'Kobern-Gondorf', 3019, 'RP', 82, 'DE', 50.30230000, 7.45612000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q553347'),
(26705, 'Koblenz', 3019, 'RP', 82, 'DE', 50.35357000, 7.57883000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q3104'),
(26706, 'Kochel', 3009, 'BY', 82, 'DE', 47.65926000, 11.36827000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q256533'),
(26707, 'Kodersdorf', 3021, 'SN', 82, 'DE', 51.24272000, 14.89336000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q506108'),
(26708, 'Kohlberg', 3009, 'BY', 82, 'DE', 49.59121000, 12.01948000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q508486'),
(26709, 'Kohlberg', 3006, 'BW', 82, 'DE', 48.55845000, 9.33576000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q508486'),
(26710, 'Kolbermoor', 3009, 'BY', 82, 'DE', 47.84965000, 12.06696000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q253445'),
(26711, 'Kolbingen', 3006, 'BW', 82, 'DE', 48.05227000, 8.88957000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q550582'),
(26712, 'Kolitzheim', 3009, 'BY', 82, 'DE', 49.91667000, 10.23333000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q558930'),
(26713, 'Kolkwitz', 3013, 'BB', 82, 'DE', 51.75000000, 14.25000000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q32066528'),
(26714, 'Kollmar', 3005, 'SH', 82, 'DE', 53.72936000, 9.47880000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q572999'),
(26715, 'Kollnburg', 3009, 'BY', 82, 'DE', 49.04664000, 12.86121000, '2019-10-05 22:41:09', '2019-10-05 22:41:09', 1, 'Q488636'),
(26716, 'Konradshöhe', 3010, 'BE', 82, 'DE', 52.58535000, 13.22758000, '2019-10-05 22:41:09', '2020-05-01 17:22:48', 1, 'Q561871'),
(26717, 'Konradsreuth', 3009, 'BY', 82, 'DE', 50.26667000, 11.85000000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q503866'),
(26718, 'Konstanz', 3006, 'BW', 82, 'DE', 47.66033000, 9.17582000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q3834'),
(26719, 'Konz', 3019, 'RP', 82, 'DE', 49.70045000, 6.57652000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q503185'),
(26720, 'Konzell', 3009, 'BY', 82, 'DE', 49.07240000, 12.71114000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q373716'),
(26721, 'Korb', 3006, 'BW', 82, 'DE', 48.84303000, 9.36258000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q373716'),
(26722, 'Korbach', 3018, 'HE', 82, 'DE', 51.27561000, 8.87300000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q500147'),
(26723, 'Kordel', 3019, 'RP', 82, 'DE', 49.83333000, 6.63333000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q500147'),
(26724, 'Korntal', 3006, 'BW', 82, 'DE', 48.83218000, 9.12140000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q500147'),
(26725, 'Kornwestheim', 3006, 'BW', 82, 'DE', 48.86158000, 9.18569000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q16102'),
(26726, 'Korschenbroich', 3017, 'NW', 82, 'DE', 51.19139000, 6.51352000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q16042'),
(26727, 'Kosel', 3005, 'SH', 82, 'DE', 54.50580000, 9.75653000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q16042'),
(26728, 'Koserow', 3007, 'MV', 82, 'DE', 54.05189000, 14.00197000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q654107'),
(26729, 'Kossa', 3021, 'SN', 82, 'DE', 51.61707000, 12.68197000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32066745'),
(26730, 'Kottenheim', 3019, 'RP', 82, 'DE', 50.35000000, 7.25000000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q569802'),
(26731, 'Kottgeisering', 3009, 'BY', 82, 'DE', 48.11667000, 11.13333000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32066767'),
(26732, 'Kottweiler-Schwanden', 3019, 'RP', 82, 'DE', 49.48333000, 7.53333000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32066767'),
(26733, 'Kraftsdorf', 3015, 'TH', 82, 'DE', 50.87574000, 11.92944000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q630313'),
(26734, 'Kraiburg am Inn', 3009, 'BY', 82, 'DE', 48.18191000, 12.43073000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q278967'),
(26735, 'Kraichtal', 3006, 'BW', 82, 'DE', 49.14623000, 8.73276000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q278967'),
(26736, 'Krailling', 3009, 'BY', 82, 'DE', 48.10000000, 11.40000000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q530780'),
(26737, 'Krakow am See', 3007, 'MV', 82, 'DE', 53.65163000, 12.27034000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q536858'),
(26738, 'Kramerhof', 3007, 'MV', 82, 'DE', 54.35366000, 13.05412000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q556010'),
(26739, 'Kranenburg', 3017, 'NW', 82, 'DE', 51.78333000, 6.01667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q556010'),
(26740, 'Kranichfeld', 3015, 'TH', 82, 'DE', 50.85446000, 11.20057000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q539604'),
(26741, 'Krauchenwies', 3006, 'BW', 82, 'DE', 48.01651000, 9.24757000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q557646'),
(26742, 'Krauschwitz', 3021, 'SN', 82, 'DE', 51.52153000, 14.71211000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q557646'),
(26743, 'Krauthausen', 3015, 'TH', 82, 'DE', 51.01667000, 10.26667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q659113'),
(26744, 'Krautheim', 3006, 'BW', 82, 'DE', 49.38789000, 9.63553000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32067805'),
(26745, 'Krebeck', 3008, 'NI', 82, 'DE', 51.58333000, 10.11667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q652602'),
(26746, 'Krefeld', 3017, 'NW', 82, 'DE', 51.33645000, 6.55381000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q2805'),
(26747, 'Kreiensen', 3008, 'NI', 82, 'DE', 51.85363000, 9.96481000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q503423'),
(26748, 'Kreischa', 3021, 'SN', 82, 'DE', 50.94534000, 13.75514000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q6787'),
(26749, 'Kremmen', 3013, 'BB', 82, 'DE', 52.76216000, 13.02515000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q587094'),
(26750, 'Krempe', 3005, 'SH', 82, 'DE', 53.83694000, 9.48831000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32068022'),
(26751, 'Kremperheide', 3005, 'SH', 82, 'DE', 53.88721000, 9.47809000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32068022'),
(26752, 'Kressbronn am Bodensee', 3006, 'BW', 82, 'DE', 47.59760000, 9.59707000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q557658'),
(26753, 'Kretzschau', 3011, 'ST', 82, 'DE', 51.05000000, 12.06667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q635076'),
(26754, 'Kreut', 3009, 'BY', 82, 'DE', 47.79868000, 11.48312000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q635076'),
(26755, 'Kreuzau', 3017, 'NW', 82, 'DE', 50.74699000, 6.49069000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q199988'),
(26756, 'Kreuzberg', 3010, 'BE', 82, 'DE', 52.49973000, 13.40338000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q199988'),
(26757, 'Kreuztal', 3017, 'NW', 82, 'DE', 50.96775000, 7.98848000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q5999'),
(26758, 'Kreuzwertheim', 3009, 'BY', 82, 'DE', 49.76818000, 9.51819000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q266170'),
(26759, 'Krickenbach', 3019, 'RP', 82, 'DE', 49.36667000, 7.66667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q566969'),
(26760, 'Kriebitzsch', 3015, 'TH', 82, 'DE', 51.02347000, 12.33318000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q34096'),
(26761, 'Kriebstein', 3021, 'SN', 82, 'DE', 51.05000000, 13.01667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q70985'),
(26762, 'Kriegsfeld', 3019, 'RP', 82, 'DE', 49.70992000, 7.91687000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q643312'),
(26763, 'Kriftel', 3018, 'HE', 82, 'DE', 50.08408000, 8.46977000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q643312'),
(26764, 'Kritzmow', 3007, 'MV', 82, 'DE', 54.05237000, 12.05311000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32068203'),
(26765, 'Krombach', 3009, 'BY', 82, 'DE', 50.08319000, 9.20609000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q32068203'),
(26766, 'Kromsdorf', 3015, 'TH', 82, 'DE', 51.00000000, 11.36667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q684733'),
(26767, 'Kronach', 3009, 'BY', 82, 'DE', 50.23963000, 11.33308000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q494537'),
(26768, 'Kronau', 3006, 'BW', 82, 'DE', 49.22250000, 8.63111000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q494537'),
(26769, 'Kronberg', 3018, 'HE', 82, 'DE', 50.18424000, 8.52320000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q569634'),
(26770, 'Kronberg Tal', 3018, 'HE', 82, 'DE', 50.17929000, 8.50370000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q569634'),
(26771, 'Kronburg', 3009, 'BY', 82, 'DE', 47.90432000, 10.15720000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q547520'),
(26772, 'Kronshagen', 3005, 'SH', 82, 'DE', 54.33333000, 10.08333000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q565416'),
(26773, 'Kropp', 3005, 'SH', 82, 'DE', 54.41667000, 9.51667000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q557021'),
(26774, 'Kroppenstedt', 3011, 'ST', 82, 'DE', 51.94211000, 11.30841000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q573449'),
(26775, 'Kropstädt', 3011, 'ST', 82, 'DE', 51.96307000, 12.74550000, '2019-10-05 22:41:10', '2020-05-01 17:22:49', 1, 'Q696842'),
(26776, 'Krostitz', 3021, 'SN', 82, 'DE', 51.46208000, 12.45360000, '2019-10-05 22:41:10', '2019-10-05 22:41:10', 1, 'Q16049'),
(26777, 'Kruft', 3019, 'RP', 82, 'DE', 50.38333000, 7.33333000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q653058'),
(26778, 'Krumbach', 3009, 'BY', 82, 'DE', 48.24182000, 10.36320000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q504803'),
(26779, 'Krummennaab', 3009, 'BY', 82, 'DE', 49.83333000, 12.10000000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q553775'),
(26780, 'Krummesse', 3005, 'SH', 82, 'DE', 53.78333000, 10.65000000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q629376'),
(26781, 'Krumpa', 3011, 'ST', 82, 'DE', 51.29724000, 11.84412000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q1790204'),
(26782, 'Krölpa', 3015, 'TH', 82, 'DE', 50.67743000, 11.53848000, '2019-10-05 22:41:11', '2020-05-01 17:22:50', 1, 'Q554751'),
(26783, 'Kröpelin', 3007, 'MV', 82, 'DE', 54.06963000, 11.79473000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q645814'),
(26784, 'Kröppelshagen-Fahrendorf', 3005, 'SH', 82, 'DE', 53.48979000, 10.31697000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q645814'),
(26785, 'Kröslin', 3007, 'MV', 82, 'DE', 54.11662000, 13.75067000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q629874'),
(26786, 'Kröv', 3019, 'RP', 82, 'DE', 49.98333000, 7.08333000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q553171'),
(26787, 'Krün', 3009, 'BY', 82, 'DE', 47.50515000, 11.27924000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q49285180'),
(26788, 'Kubschütz', 3021, 'SN', 82, 'DE', 51.16667000, 14.50000000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q93253'),
(26789, 'Kuchen', 3006, 'BW', 82, 'DE', 48.63583000, 9.79989000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q32068543'),
(26790, 'Kuddewörde', 3005, 'SH', 82, 'DE', 53.58333000, 10.40000000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q32068543'),
(26791, 'Kueps Oberfranken', 3009, 'BY', 82, 'DE', 50.19265000, 11.27599000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q32068543'),
(26792, 'Kuhardt', 3019, 'RP', 82, 'DE', 49.14583000, 8.31444000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q601057'),
(26793, 'Kulmain', 3009, 'BY', 82, 'DE', 49.90000000, 11.90000000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q566580'),
(26794, 'Kulmbach', 3009, 'BY', 82, 'DE', 50.10068000, 11.45032000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q157598'),
(26795, 'Kumhausen', 3009, 'BY', 82, 'DE', 48.50938000, 12.15637000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q260564'),
(26796, 'Kummerfeld', 3005, 'SH', 82, 'DE', 53.69262000, 9.79099000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q551706'),
(26797, 'Kunreuth', 3009, 'BY', 82, 'DE', 50.23321000, 11.53119000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q32068747'),
(26798, 'Kupferberg', 3009, 'BY', 82, 'DE', 50.13960000, 11.57762000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q505242'),
(26799, 'Kupferzell', 3006, 'BW', 82, 'DE', 49.22778000, 9.69000000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q547357'),
(26800, 'Kuppenheim', 3006, 'BW', 82, 'DE', 48.82794000, 8.25417000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q518505'),
(26801, 'Kurort Gohrisch', 3021, 'SN', 82, 'DE', 50.91234000, 14.10687000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q6698'),
(26802, 'Kurort Jonsdorf', 3021, 'SN', 82, 'DE', 50.85751000, 14.70922000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q544821'),
(26803, 'Kurort Oberwiesenthal', 3021, 'SN', 82, 'DE', 50.41943000, 12.96836000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q57926'),
(26804, 'Kurort Oybin', 3021, 'SN', 82, 'DE', 50.84105000, 14.74124000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q57926'),
(26805, 'Kusel', 3019, 'RP', 82, 'DE', 49.53772000, 7.40472000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q22937'),
(26806, 'Kusey', 3011, 'ST', 82, 'DE', 52.57987000, 11.09056000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q702853'),
(26807, 'Kusterdingen', 3006, 'BW', 82, 'DE', 48.52291000, 9.11977000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q702853'),
(26808, 'Kutenholz', 3008, 'NI', 82, 'DE', 53.48152000, 9.32118000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q505523'),
(26809, 'Kutzenhausen', 3009, 'BY', 82, 'DE', 48.34202000, 10.69459000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q505523'),
(26810, 'Kyllburg', 3019, 'RP', 82, 'DE', 50.03864000, 6.59478000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q125124'),
(26811, 'Kyritz', 3013, 'BB', 82, 'DE', 52.94212000, 12.39704000, '2019-10-05 22:41:11', '2019-10-05 22:41:11', 1, 'Q505160'),
(26812, 'Köditz', 3009, 'BY', 82, 'DE', 50.33333000, 11.85000000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q505700'),
(26813, 'Ködnitz', 3009, 'BY', 82, 'DE', 50.10000000, 11.53333000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q511126'),
(26814, 'Köfering', 3009, 'BY', 82, 'DE', 48.93333000, 12.20000000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q491602'),
(26815, 'Kölbingen', 3019, 'RP', 82, 'DE', 50.55000000, 7.93333000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q491602'),
(26816, 'Kölleda', 3015, 'TH', 82, 'DE', 51.18745000, 11.24488000, '2019-10-05 22:41:11', '2020-05-01 17:22:50', 1, 'Q539554'),
(26817, 'Kölln-Reisiek', 3005, 'SH', 82, 'DE', 53.75748000, 9.69772000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q567415'),
(26818, 'Köln', 3017, 'NW', 82, 'DE', 50.93333000, 6.95000000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q365'),
(26819, 'Köngen', 3006, 'BW', 82, 'DE', 48.68333000, 9.36667000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q80644'),
(26820, 'Köngernheim', 3019, 'RP', 82, 'DE', 49.84583000, 8.24667000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q678804'),
(26821, 'Königheim', 3006, 'BW', 82, 'DE', 49.62028000, 9.59583000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q61729'),
(26822, 'Königs Wusterhausen', 3013, 'BB', 82, 'DE', 52.30141000, 13.63300000, '2019-10-05 22:41:11', '2020-05-01 17:22:48', 1, 'Q16013'),
(26823, 'Königsbach-Stein', 3006, 'BW', 82, 'DE', 48.96585000, 8.60573000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q81247'),
(26824, 'Königsbronn', 3006, 'BW', 82, 'DE', 48.74317000, 10.11193000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q1796326'),
(26825, 'Königsbrunn', 3009, 'BY', 82, 'DE', 48.27506000, 10.89178000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q1796326'),
(26826, 'Königsbrück', 3021, 'SN', 82, 'DE', 51.26451000, 13.90540000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q81739'),
(26827, 'Königsdorf', 3009, 'BY', 82, 'DE', 47.81546000, 11.48063000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q81739'),
(26828, 'Königsee', 3015, 'TH', 82, 'DE', 50.66143000, 11.09748000, '2019-10-05 22:41:11', '2020-05-01 17:22:50', 1, 'Q550822'),
(26829, 'Königsfeld', 3021, 'SN', 82, 'DE', 51.06667000, 12.75000000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q550822'),
(26830, 'Königsfeld', 3009, 'BY', 82, 'DE', 49.94603000, 11.16520000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q550822'),
(26831, 'Königsfeld im Schwarzwald', 3006, 'BW', 82, 'DE', 48.13814000, 8.41973000, '2019-10-05 22:41:11', '2020-05-01 17:22:47', 1, 'Q81421'),
(26832, 'Königshain', 3021, 'SN', 82, 'DE', 51.18333000, 14.86667000, '2019-10-05 22:41:11', '2020-05-01 17:22:49', 1, 'Q506096');

