INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(85513, '<PERSON><PERSON>', 3176, 'P<PERSON>', 167, 'P<PERSON>', 32.84318000, 71.36192000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q15233153'),
(85514, 'Kambar', 3175, 'SD', 167, 'PK', 27.58753000, 68.00066000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q15233153'),
(85515, '<PERSON><PERSON><PERSON>', 3176, 'PB', 167, 'PK', 31.97526000, 74.22304000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1260929'),
(85516, 'Kamra', 3176, 'P<PERSON>', 167, '<PERSON><PERSON>', 33.74698000, 73.51229000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q1260929'),
(85517, '<PERSON>ndhkot', 3175, 'SD', 167, 'PK', 28.24574000, 69.17974000, '2019-10-05 23:13:32', '2019-10-05 23:13:32', 1, 'Q3695624'),
(85518, 'Kandiari', 3175, 'SD', 167, 'PK', 26.91550000, 68.52193000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3695624'),
(85519, 'Kandiaro', 3175, 'SD', 167, 'PK', 27.05918000, 68.21022000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3695624'),
(85520, 'Kanganpur', 3176, 'PB', 167, 'PK', 30.76468000, 74.12286000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6362466'),
(85521, 'Karachi', 3175, 'SD', 167, 'PK', 24.86080000, 67.01040000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q8660'),
(85522, 'Karak', 3171, 'KP', 167, 'PK', 33.11633000, 71.09354000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2579936'),
(85523, 'Karaundi', 3175, 'SD', 167, 'PK', 26.89709000, 68.40643000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2579936'),
(85524, 'Kario Ghanwar', 3175, 'SD', 167, 'PK', 24.80817000, 68.60483000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2579936'),
(85525, 'Karor', 3176, 'PB', 167, 'PK', 31.22460000, 70.95153000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2579936'),
(85526, 'Kashmor', 3175, 'SD', 167, 'PK', 28.43260000, 69.58364000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2625917'),
(85527, 'Kasur', 3176, 'PB', 167, 'PK', 31.11866000, 74.45025000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2625917'),
(85528, 'Keshupur', 3176, 'PB', 167, 'PK', 32.26000000, 72.50000000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2625917'),
(85529, 'Keti Bandar', 3175, 'SD', 167, 'PK', 24.14422000, 67.45094000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2625917'),
(85530, 'Khadan Khak', 3174, 'BA', 167, 'PK', 30.75236000, 67.71133000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2625917'),
(85531, 'Khadro', 3175, 'SD', 167, 'PK', 26.14713000, 68.71777000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2625917'),
(85532, 'Khairpur', 3175, 'SD', 167, 'PK', 28.06437000, 69.70363000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2625917'),
(85533, 'Khairpur Mir’s', 3175, 'SD', 167, 'PK', 27.52948000, 68.75915000, '2019-10-05 23:13:33', '2020-05-01 17:23:04', 1, 'Q1179634'),
(85534, 'Khairpur Nathan Shah', 3175, 'SD', 167, 'PK', 27.09064000, 67.73489000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1179634'),
(85535, 'Khairpur Tamiwali', 3176, 'PB', 167, 'PK', 29.58139000, 72.23804000, '2019-10-05 23:13:33', '2020-07-04 17:13:25', 1, 'Q1179634'),
(85536, 'Khalabat', 3171, 'KP', 167, 'PK', 34.05997000, 72.88963000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1179634'),
(85537, 'Khandowa', 3176, 'PB', 167, 'PK', 32.74255000, 72.73478000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1179634'),
(85538, 'Khanewal', 3176, 'PB', 167, 'PK', 30.30173000, 71.93212000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1253395'),
(85539, 'Khanga Dogran', 3176, 'PB', 167, 'PK', 31.83294000, 73.62213000, '2019-10-05 23:13:33', '2020-07-04 17:13:43', 1, 'Q1253395'),
(85540, 'Khangarh', 3176, 'PB', 167, 'PK', 29.91446000, 71.16067000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1253395'),
(85541, 'Khanpur', 3176, 'PB', 167, 'PK', 28.64739000, 70.65694000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1250097'),
(85542, 'Khanpur Mahar', 3175, 'SD', 167, 'PK', 27.84088000, 69.41302000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1250097'),
(85543, 'Kharan', 3174, 'BA', 167, 'PK', 28.58459000, 65.41501000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1740586'),
(85544, 'Kharian', 3176, 'PB', 167, 'PK', 32.81612000, 73.88697000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3696367'),
(85545, 'Khewra', 3176, 'PB', 167, 'PK', 32.64910000, 73.01059000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3696036'),
(85546, 'Khurrianwala', 3176, 'PB', 167, 'PK', 31.49936000, 73.26763000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6403122'),
(85547, 'Khushab', 3176, 'PB', 167, 'PK', 32.29667000, 72.35250000, '2019-10-05 23:13:33', '2020-07-04 11:37:55', 1, 'Q1249939'),
(85548, 'Khuzdar', 3174, 'BA', 167, 'PK', 27.81193000, 66.61096000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1249939'),
(85549, 'Khuzdār District', 3174, 'BA', 167, 'PK', 27.48680000, 66.58703000, '2019-10-05 23:13:33', '2020-05-01 17:23:04', 1, 'Q2642523'),
(85550, 'Khārān District', 3174, 'BA', 167, 'PK', 27.96308000, 64.57113000, '2019-10-05 23:13:33', '2020-05-01 17:23:04', 1, 'Q537334'),
(85551, 'Kohat', 3171, 'KP', 167, 'PK', 33.58196000, 71.44929000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1195983'),
(85552, 'Kohlu', 3174, 'BA', 167, 'PK', 29.89651000, 69.25324000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2633568'),
(85553, 'Kot Addu Tehsil', 3176, 'PB', 167, 'PK', 30.46907000, 70.96699000, '2019-10-05 23:13:33', '2020-07-04 17:15:35', 1, 'Q1260951'),
(85554, 'Kot Diji', 3175, 'SD', 167, 'PK', 27.34156000, 68.70821000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85555, 'Kot Ghulam Muhammad', 3176, 'PB', 167, 'PK', 32.33311000, 74.54694000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85556, 'Kot Malik Barkhurdar', 3174, 'BA', 167, 'PK', 30.20379000, 66.98723000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85557, 'Kot Mumin', 3176, 'PB', 167, 'PK', 32.18843000, 73.02987000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85558, 'Kot Radha Kishan', 3176, 'PB', 167, 'PK', 31.17068000, 74.10126000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85559, 'Kot Rajkour', 3176, 'PB', 167, 'PK', 32.41208000, 74.62855000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85560, 'Kot Samaba', 3176, 'PB', 167, 'PK', 28.55207000, 70.46837000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85561, 'Kot Sultan', 3176, 'PB', 167, 'PK', 30.77370000, 70.93125000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1017084'),
(85562, 'Kotli', 3172, 'JK', 167, 'PK', 33.51836000, 73.90220000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2586558'),
(85563, 'Kotli District', 3172, 'JK', 167, 'PK', 33.44559000, 73.91557000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2571465'),
(85564, 'Kotli Loharan', 3176, 'PB', 167, 'PK', 32.58893000, 74.49466000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2571465'),
(85565, 'Kotri', 3175, 'SD', 167, 'PK', 25.36566000, 68.30831000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2571465'),
(85566, 'Kulachi', 3171, 'KP', 167, 'PK', 31.93058000, 70.45959000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2571465'),
(85567, 'Kundian', 3176, 'PB', 167, 'PK', 32.45775000, 71.47892000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q951628'),
(85568, 'Kunjah', 3176, 'PB', 167, 'PK', 32.52982000, 73.97486000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q951628'),
(85569, 'Kunri', 3175, 'SD', 167, 'PK', 25.17874000, 69.56572000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q951628'),
(85570, 'Lachi', 3171, 'KP', 167, 'PK', 33.38291000, 71.33733000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q951628'),
(85571, 'Ladhewala Waraich', 3176, 'PB', 167, 'PK', 32.15692000, 74.11564000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q951628'),
(85572, 'Lahore', 3176, 'PB', 167, 'PK', 31.55800000, 74.35071000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q11739'),
(85573, 'Lakhi', 3175, 'SD', 167, 'PK', 27.84884000, 68.69972000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q11739'),
(85574, 'Lakki', 3171, 'KP', 167, 'PK', 32.60724000, 70.91234000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q11739'),
(85575, 'Lala Musa', 3176, 'PB', 167, 'PK', 32.70138000, 73.95746000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3695697'),
(85576, 'Lalian', 3176, 'PB', 167, 'PK', 31.82462000, 72.80116000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3298223'),
(85577, 'Landi Kotal', 3173, 'TA', 167, 'PK', 34.09880000, 71.14108000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1803018'),
(85578, 'Larkana', 3175, 'SD', 167, 'PK', 27.55898000, 68.21204000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q696605'),
(85579, 'Lasbela District', 3174, 'BA', 167, 'PK', 25.78634000, 66.60330000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q44942'),
(85580, 'Layyah', 3176, 'PB', 167, 'PK', 30.96128000, 70.93904000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q44942'),
(85581, 'Layyah District', 3176, 'PB', 167, 'PK', 30.96800000, 70.94300000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2418995'),
(85582, 'Liliani', 3176, 'PB', 167, 'PK', 32.20393000, 72.95120000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2418995'),
(85583, 'Lodhran', 3176, 'PB', 167, 'PK', 29.53390000, 71.63244000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1249783'),
(85584, 'Loralai', 3174, 'BA', 167, 'PK', 30.37051000, 68.59795000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2663236'),
(85585, 'Loralai District', 3174, 'BA', 167, 'PK', 30.30253000, 68.84636000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2281268'),
(85586, 'Mach', 3174, 'BA', 167, 'PK', 29.86371000, 67.33018000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q639806'),
(85587, 'Madeji', 3175, 'SD', 167, 'PK', 27.75314000, 68.45166000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q639806'),
(85588, 'Mailsi', 3176, 'PB', 167, 'PK', 29.80123000, 72.17398000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3695872'),
(85589, 'Malakwal', 3176, 'PB', 167, 'PK', 32.55449000, 73.21274000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q446394'),
(85590, 'Malakwal City', 3176, 'PB', 167, 'PK', 32.55492000, 73.21220000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q446394'),
(85591, 'Malir Cantonment', 3175, 'SD', 167, 'PK', 24.94343000, 67.20591000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q446394'),
(85592, 'Mamu Kanjan', 3176, 'PB', 167, 'PK', 30.83044000, 72.79943000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6745993'),
(85593, 'Mananwala', 3176, 'PB', 167, 'PK', 31.58803000, 73.68927000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6745993'),
(85594, 'Mandi Bahauddin', 3176, 'PB', 167, 'PK', 32.58704000, 73.49123000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6745993'),
(85595, 'Mangla', 3176, 'PB', 167, 'PK', 31.89306000, 72.38167000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6745993'),
(85596, 'Mankera', 3176, 'PB', 167, 'PK', 31.38771000, 71.44047000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3695856'),
(85597, 'Mansehra', 3171, 'KP', 167, 'PK', 34.33023000, 73.19679000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2662991'),
(85598, 'Mardan', 3171, 'KP', 167, 'PK', 34.19794000, 72.04965000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q993859'),
(85599, 'Mastung', 3174, 'BA', 167, 'PK', 29.79966000, 66.84553000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2592655'),
(85600, 'Mastung District', 3174, 'BA', 167, 'PK', 29.79455000, 66.72068000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1026625'),
(85601, 'Matiari', 3175, 'SD', 167, 'PK', 25.59709000, 68.44670000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q112453'),
(85602, 'Matli', 3175, 'SD', 167, 'PK', 25.04290000, 68.65591000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q112453'),
(85603, 'Mehar', 3175, 'SD', 167, 'PK', 27.18027000, 67.82051000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q112453'),
(85604, 'Mehmand Chak', 3176, 'PB', 167, 'PK', 32.78518000, 73.82306000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q112453'),
(85605, 'Mehrabpur', 3174, 'BA', 167, 'PK', 28.10773000, 68.02554000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q112453'),
(85606, 'Mian Channun', 3176, 'PB', 167, 'PK', 30.44067000, 72.35679000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q4311575'),
(85607, 'Mianke Mor', 3176, 'PB', 167, 'PK', 31.20240000, 73.94857000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q4311575'),
(85608, 'Mianwali', 3176, 'PB', 167, 'PK', 32.57756000, 71.52847000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3307925'),
(85609, 'Minchinabad', 3176, 'PB', 167, 'PK', 30.16356000, 73.56858000, '2019-10-05 23:13:33', '2020-07-04 17:41:49', 1, 'Q3695838'),
(85610, 'Mingora', 3171, 'KP', 167, 'PK', 34.77950000, 72.36265000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q31356'),
(85611, 'Miran Shah', 3173, 'TA', 167, 'PK', 33.00059000, 70.07117000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q124385'),
(85612, 'Miro Khan', 3175, 'SD', 167, 'PK', 27.75985000, 68.09195000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q124385'),
(85613, 'Mirpur Bhtoro', 3175, 'SD', 167, 'PK', 24.72852000, 68.26010000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6874468'),
(85614, 'Mirpur District', 3172, 'JK', 167, 'PK', 33.21556000, 73.75144000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2571434'),
(85615, 'Mirpur Khas', 3175, 'SD', 167, 'PK', 25.52760000, 69.01255000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1020490'),
(85616, 'Mirpur Mathelo', 3175, 'SD', 167, 'PK', 28.02136000, 69.54914000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1020490'),
(85617, 'Mirpur Sakro', 3175, 'SD', 167, 'PK', 24.54692000, 67.62797000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1020490'),
(85618, 'Mirwah Gorchani', 3175, 'SD', 167, 'PK', 25.30981000, 69.05019000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1020490'),
(85619, 'Mitha Tiwana', 3176, 'PB', 167, 'PK', 32.24540000, 72.10615000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1020490'),
(85620, 'Mithi', 3175, 'SD', 167, 'PK', 24.73701000, 69.79707000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3246442'),
(85621, 'Moro', 3175, 'SD', 167, 'PK', 26.66317000, 68.00016000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3246442'),
(85622, 'Moza Shahwala', 3176, 'PB', 167, 'PK', 30.80563000, 70.84911000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q3246442'),
(85623, 'Multan', 3176, 'PB', 167, 'PK', 30.19679000, 71.47824000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q185453'),
(85624, 'Muridke', 3176, 'PB', 167, 'PK', 31.80258000, 74.25772000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2978509'),
(85625, 'Murree', 3176, 'PB', 167, 'PK', 33.90836000, 73.39030000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q1020224'),
(85626, 'Mustafabad', 3176, 'PB', 167, 'PK', 30.89222000, 73.49889000, '2019-10-05 23:13:33', '2020-07-04 11:38:11', 1, 'Q1020224'),
(85627, 'Muzaffargarh', 3176, 'PB', 167, 'PK', 30.07258000, 71.19379000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q301627'),
(85628, 'Muzaffarābād', 3172, 'JK', 167, 'PK', 34.37002000, 73.47082000, '2019-10-05 23:13:33', '2020-05-01 17:23:04', 1, 'Q461307'),
(85629, 'Mūsa Khel District', 3174, 'BA', 167, 'PK', 30.84937000, 69.90069000, '2019-10-05 23:13:33', '2020-05-01 17:23:04', 1, 'Q461307'),
(85630, 'Nabisar', 3175, 'SD', 167, 'PK', 25.06717000, 69.64340000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q461307'),
(85631, 'Nankana Sahib', 3176, 'PB', 167, 'PK', 31.45010000, 73.70653000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2353077'),
(85632, 'Narang Mandi', 3176, 'PB', 167, 'PK', 31.90376000, 74.51587000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q6965345'),
(85633, 'Narowal', 3176, 'PB', 167, 'PK', 32.10197000, 74.87303000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2977081'),
(85634, 'Nasirabad', 3175, 'SD', 167, 'PK', 27.38137000, 67.91644000, '2019-10-05 23:13:33', '2019-10-05 23:13:33', 1, 'Q2977081'),
(85635, 'Nasīrābād District', 3174, 'BA', 167, 'PK', 28.62643000, 68.12925000, '2019-10-05 23:13:34', '2020-05-01 17:23:04', 1, 'Q2315447'),
(85636, 'Naudero', 3175, 'SD', 167, 'PK', 27.66684000, 68.36090000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3695612'),
(85637, 'Naukot', 3175, 'SD', 167, 'PK', 24.85822000, 69.40153000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3695612'),
(85638, 'Naushahra Virkan', 3176, 'PB', 167, 'PK', 31.96258000, 73.97117000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3695612'),
(85639, 'Naushahro Firoz', 3175, 'SD', 167, 'PK', 26.84010000, 68.12265000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3695612'),
(85640, 'Nawabshah', 3175, 'SD', 167, 'PK', 26.23939000, 68.40369000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1017637'),
(85641, 'Nazir Town', 3176, 'PB', 167, 'PK', 33.30614000, 73.48330000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1017637'),
(85642, 'New Bādāh', 3175, 'SD', 167, 'PK', 27.34167000, 68.03194000, '2019-10-05 23:13:34', '2020-05-01 17:23:04', 1, 'Q1017637'),
(85643, 'New Mirpur', 3172, 'JK', 167, 'PK', 33.14782000, 73.75187000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2579925'),
(85644, 'Noorabad', 3171, 'KP', 167, 'PK', 34.25195000, 71.96656000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2579925'),
(85645, 'North Wazīristān Agency', 3173, 'TA', 167, 'PK', 32.95087000, 69.95764000, '2019-10-05 23:13:34', '2020-05-01 17:23:04', 1, 'Q2579925'),
(85646, 'Nowshera', 3171, 'KP', 167, 'PK', 34.01583000, 71.98123000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2003745'),
(85647, 'Nowshera Cantonment', 3171, 'KP', 167, 'PK', 33.99829000, 71.99834000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2003745'),
(85648, 'Nushki', 3174, 'BA', 167, 'PK', 29.55218000, 66.02288000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q34759319'),
(85650, 'Okara', 3176, 'PB', 167, 'PK', 30.81029000, 73.45155000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q968211'),
(85651, 'Ormara', 3174, 'BA', 167, 'PK', 25.21018000, 64.63626000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1188222'),
(85652, 'Pabbi', 3171, 'KP', 167, 'PK', 34.00968000, 71.79445000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3045006'),
(85653, 'Pad Idan', 3175, 'SD', 167, 'PK', 26.77455000, 68.30094000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3045006'),
(85654, 'Paharpur', 3171, 'KP', 167, 'PK', 32.10502000, 70.97055000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3045006'),
(85655, 'Pakpattan', 3176, 'PB', 167, 'PK', 30.34314000, 73.38944000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1249801'),
(85656, 'Panjgūr District', 3174, 'BA', 167, 'PK', 26.73750000, 64.20380000, '2019-10-05 23:13:34', '2020-05-01 17:23:04', 1, 'Q2428944'),
(85657, 'Pano Aqil', 3175, 'SD', 167, 'PK', 27.85619000, 69.11111000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7131436'),
(85658, 'Pasni', 3174, 'BA', 167, 'PK', 25.26302000, 63.46921000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q715199'),
(85659, 'Pasrur', 3176, 'PB', 167, 'PK', 32.26286000, 74.66327000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3246361'),
(85660, 'Pattoki', 3176, 'PB', 167, 'PK', 31.02021000, 73.85333000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2592708'),
(85661, 'Peshawar', 3171, 'KP', 167, 'PK', 34.00800000, 71.57849000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1113311'),
(85662, 'Phalia', 3176, 'PB', 167, 'PK', 32.43104000, 73.57900000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3696336'),
(85663, 'Pind Dadan Khan', 3176, 'PB', 167, 'PK', 32.58662000, 73.04456000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3696336'),
(85664, 'Pindi Bhattian', 3176, 'PB', 167, 'PK', 31.89844000, 73.27339000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7195018'),
(85665, 'Pindi Gheb', 3176, 'PB', 167, 'PK', 33.24095000, 72.26480000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7195018'),
(85666, 'Pir Jo Goth', 3175, 'SD', 167, 'PK', 27.59178000, 68.61848000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7195018'),
(85667, 'Pir Mahal', 3176, 'PB', 167, 'PK', 30.76663000, 72.43455000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7195018'),
(85668, 'Pishin', 3174, 'BA', 167, 'PK', 30.58176000, 66.99406000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2449537'),
(85669, 'Pithoro', 3175, 'SD', 167, 'PK', 25.51122000, 69.37803000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2449537'),
(85670, 'Qadirpur Ran', 3176, 'PB', 167, 'PK', 30.29184000, 71.67164000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7265783'),
(85671, 'Qila Saifullāh District', 3174, 'BA', 167, 'PK', 30.95392000, 68.33996000, '2019-10-05 23:13:34', '2020-05-01 17:23:04', 1, 'Q7265783'),
(85672, 'Quetta', 3174, 'BA', 167, 'PK', 30.18414000, 67.00141000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q185458'),
(85673, 'Quetta District', 3174, 'BA', 167, 'PK', 30.17458000, 66.76203000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2315549'),
(85674, 'Rahim Yar Khan', 3176, 'PB', 167, 'PK', 28.41987000, 70.30345000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q989297'),
(85675, 'Raiwind', 3176, 'PB', 167, 'PK', 31.24895000, 74.21534000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q4688896'),
(85676, 'Raja Jang', 3176, 'PB', 167, 'PK', 31.22078000, 74.25483000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7285457'),
(85677, 'Rajanpur', 3176, 'PB', 167, 'PK', 29.10408000, 70.32969000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2998238'),
(85678, 'Rajo Khanani', 3175, 'SD', 167, 'PK', 24.98391000, 68.85370000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2998238'),
(85679, 'Ranipur', 3175, 'SD', 167, 'PK', 27.28720000, 68.50623000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2998238'),
(85680, 'Rasulnagar', 3176, 'PB', 167, 'PK', 32.32794000, 73.78040000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2998238'),
(85681, 'Ratodero', 3175, 'SD', 167, 'PK', 27.80227000, 68.28902000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3246396'),
(85682, 'Rawala Kot', 3172, 'JK', 167, 'PK', 33.85782000, 73.76043000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3246396'),
(85683, 'Rawalpindi', 3176, 'PB', 167, 'PK', 33.59733000, 73.04790000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q93230'),
(85684, 'Rawalpindi District', 3176, 'PB', 167, 'PK', 33.42987000, 73.23092000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q24440'),
(85685, 'Renala Khurd', 3176, 'PB', 167, 'PK', 30.87878000, 73.59857000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3321405'),
(85686, 'Risalpur Cantonment', 3171, 'KP', 167, 'PK', 34.06048000, 71.99276000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1740709'),
(85687, 'Rohri', 3175, 'SD', 167, 'PK', 27.69203000, 68.89503000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q33687'),
(85688, 'Rojhan', 3176, 'PB', 167, 'PK', 28.68735000, 69.95350000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q33687'),
(85689, 'Rustam', 3175, 'SD', 167, 'PK', 27.96705000, 68.80386000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q33687'),
(85690, 'Sadiqabad', 3176, 'PB', 167, 'PK', 28.30910000, 70.12652000, '2019-10-05 23:13:34', '2020-07-04 17:43:52', 1, 'Q1251234'),
(85691, 'Sahiwal', 3176, 'PB', 167, 'PK', 30.66595000, 73.10186000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q523778'),
(85692, 'Sakrand', 3175, 'SD', 167, 'PK', 26.13845000, 68.27444000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3696284'),
(85693, 'Samaro', 3175, 'SD', 167, 'PK', 25.28143000, 69.39623000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7408863'),
(85694, 'Sambrial', 3176, 'PB', 167, 'PK', 32.47835000, 74.35338000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3246527'),
(85695, 'Sanghar', 3175, 'SD', 167, 'PK', 26.04694000, 68.94917000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3472203'),
(85696, 'Sangla Hill', 3176, 'PB', 167, 'PK', 31.71667000, 73.38333000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3248290'),
(85697, 'Sanjwal', 3176, 'PB', 167, 'PK', 33.76105000, 72.43315000, '2019-10-05 23:13:34', '2020-07-04 17:44:25', 1, 'Q33480741'),
(85698, 'Sann', 3175, 'SD', 167, 'PK', 26.04030000, 68.13763000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3248290'),
(85699, 'Sarai Alamgir', 3176, 'PB', 167, 'PK', 32.90495000, 73.75518000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3243456'),
(85700, 'Sarai Naurang', 3171, 'KP', 167, 'PK', 32.82581000, 70.78107000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3243456'),
(85701, 'Sarai Sidhu', 3176, 'PB', 167, 'PK', 30.59476000, 71.96990000, '2019-10-05 23:13:34', '2020-07-04 17:45:30', 1, 'Q33481932'),
(85702, 'Sargodha', 3176, 'PB', 167, 'PK', 32.08586000, 72.67418000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q855997'),
(85703, 'Sehwan', 3175, 'SD', 167, 'PK', 26.42495000, 67.86126000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3308397'),
(85704, 'Setharja Old', 3175, 'SD', 167, 'PK', 27.21270000, 68.46883000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3308397'),
(85705, 'Shabqadar', 3171, 'KP', 167, 'PK', 34.21599000, 71.55480000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q4519132'),
(85706, 'Shahdad Kot', 3175, 'SD', 167, 'PK', 27.84726000, 67.90679000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q4519132'),
(85707, 'Shahdadpur', 3175, 'SD', 167, 'PK', 25.92539000, 68.62280000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3248111'),
(85708, 'Shorkot', 3176, 'PB', 167, 'PK', 30.50000000, 72.40000000, '2019-10-05 23:13:34', '2020-07-04 17:23:37', 1, 'Q7501510'),
(85709, 'Shahpur', 3176, 'PB', 167, 'PK', 32.26820000, 72.46884000, '2019-10-05 23:13:34', '2020-07-04 17:47:08', 1, 'Q33484369'),
(85710, 'Shahpur Chakar', 3175, 'SD', 167, 'PK', 26.15411000, 68.65013000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3243491'),
(85711, 'Shahr Sultan', 3176, 'PB', 167, 'PK', 29.57517000, 71.02209000, '2019-10-05 23:13:34', '2020-07-04 17:46:38', 1, 'Q33483938'),
(85712, 'Shakargarh', 3176, 'PB', 167, 'PK', 32.26361000, 75.16008000, '2019-10-05 23:13:34', '2020-07-04 17:46:47', 1, 'Q33483959'),
(85713, 'Sharqpur', 3176, 'PB', 167, 'PK', 31.46116000, 74.10091000, '2019-10-05 23:13:34', '2020-07-04 17:46:53', 1, 'Q33484038'),
(85714, 'Sheikhupura', 3176, 'PB', 167, 'PK', 31.71287000, 73.98556000, '2019-10-05 23:13:34', '2020-07-04 17:22:23', 1, 'Q972756'),
(85715, 'Shikarpur', 3175, 'SD', 167, 'PK', 27.95558000, 68.63823000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1250069'),
(85716, 'Shingli Bala', 3171, 'KP', 167, 'PK', 34.67872000, 72.98491000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1250069'),
(85717, 'Shinpokh', 3173, 'TA', 167, 'PK', 34.32959000, 71.17852000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1250069'),
(85718, 'Shorkot', 3171, 'KP', 167, 'PK', 31.91023000, 70.87757000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1250069'),
(85719, 'Shujaabad', 3176, 'PB', 167, 'PK', 29.88092000, 71.29344000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1250069'),
(85720, 'Sialkot', 3176, 'PB', 167, 'PK', 32.49268000, 74.53134000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q643883'),
(85721, 'Sibi', 3174, 'BA', 167, 'PK', 29.54299000, 67.87726000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2364754'),
(85722, 'Sillanwali', 3176, 'PB', 167, 'PK', 31.82539000, 72.54064000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3694879'),
(85723, 'Sinjhoro', 3175, 'SD', 167, 'PK', 26.03008000, 68.80867000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3694879'),
(85724, 'Skardu', 3170, 'GB', 167, 'PK', 35.29787000, 75.63372000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q976955'),
(85725, 'Sobhodero', 3175, 'SD', 167, 'PK', 27.30475000, 68.39715000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q976955'),
(85726, 'Sodhra', 3176, 'PB', 167, 'PK', 32.46211000, 74.18207000, '2019-10-05 23:13:34', '2020-07-04 17:47:41', 1, 'Q7553268'),
(85727, 'Sohbatpur', 3174, 'BA', 167, 'PK', 28.52038000, 68.54298000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7635235'),
(85728, 'South Wazīristān Agency', 3173, 'TA', 167, 'PK', 32.30397000, 69.68207000, '2019-10-05 23:13:34', '2020-05-01 17:23:04', 1, 'Q7635235'),
(85729, 'Sukheke Mandi', 3176, 'PB', 167, 'PK', 31.86541000, 73.50875000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7635235'),
(85730, 'Sukkur', 3175, 'SD', 167, 'PK', 27.70323000, 68.85889000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q868631'),
(85731, 'Surab', 3174, 'BA', 167, 'PK', 28.49276000, 66.25999000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q868631'),
(85732, 'Surkhpur', 3176, 'PB', 167, 'PK', 32.71816000, 74.44773000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q868631'),
(85733, 'Swabi', 3171, 'KP', 167, 'PK', 34.12018000, 72.46982000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85734, 'Sīta Road', 3175, 'SD', 167, 'PK', 27.03333000, 67.85000000, '2019-10-05 23:13:34', '2020-05-01 17:23:04', 1, '********'),
(85735, 'Talagang', 3176, 'PB', 167, 'PK', 32.92766000, 72.41594000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85736, 'Talamba', 3176, 'PB', 167, 'PK', 30.52693000, 72.24079000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85737, 'Talhar', 3175, 'SD', 167, 'PK', 24.88454000, 68.81437000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85738, 'Tandlianwala', 3176, 'PB', 167, 'PK', 31.03359000, 73.13268000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85739, 'Tando Adam', 3175, 'SD', 167, 'PK', 25.76818000, 68.66196000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85740, 'Tando Allahyar', 3175, 'SD', 167, 'PK', 25.46050000, 68.71745000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85741, 'Tando Bago', 3175, 'SD', 167, 'PK', 24.78914000, 68.96535000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85742, 'Tando Jam', 3175, 'SD', 167, 'PK', 25.42813000, 68.52923000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85743, 'Tando Mitha Khan', 3175, 'SD', 167, 'PK', 25.99625000, 69.20251000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85744, 'Tando Muhammad Khan', 3175, 'SD', 167, 'PK', 25.12384000, 68.53677000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85745, 'Tangi', 3171, 'KP', 167, 'PK', 34.30090000, 71.65238000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85746, 'Tangwani', 3175, 'SD', 167, 'PK', 28.27886000, 68.99760000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85747, 'Tank', 3171, 'KP', 167, 'PK', 32.21707000, 70.38315000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, '********'),
(85748, 'Taunsa', 3176, 'PB', 167, 'PK', 30.70358000, 70.65054000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3243471'),
(85749, 'Thal', 3171, 'KP', 167, 'PK', 35.47836000, 72.24383000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3243471'),
(85750, 'Tharu Shah', 3175, 'SD', 167, 'PK', 26.94230000, 68.11759000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q3243471'),
(85751, 'Thatta', 3175, 'SD', 167, 'PK', 24.74745000, 67.92353000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q27212'),
(85752, 'Thul', 3175, 'SD', 167, 'PK', 28.24030000, 68.77550000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7798734'),
(85753, 'Toba Tek Singh', 3176, 'PB', 167, 'PK', 30.97127000, 72.48275000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2659599'),
(85754, 'Topi', 3171, 'KP', 167, 'PK', 34.07034000, 72.62147000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q4460662'),
(85755, 'Turbat', 3174, 'BA', 167, 'PK', 26.00122000, 63.04849000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2977093'),
(85756, 'Ubauro', 3175, 'SD', 167, 'PK', 28.16429000, 69.73114000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7876431'),
(85757, 'Umarkot', 3175, 'SD', 167, 'PK', 25.36329000, 69.74184000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q7876431'),
(85758, 'Umerkot District', 3175, 'SD', 167, 'PK', 25.37000000, 69.73000000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2418946'),
(85759, 'Upper Dir', 3171, 'KP', 167, 'PK', 35.20740000, 71.87680000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q654483'),
(85760, 'Usta Muhammad', 3174, 'BA', 167, 'PK', 28.17723000, 68.04367000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q10388486'),
(85761, 'Uthal', 3174, 'BA', 167, 'PK', 25.80722000, 66.62194000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2581505'),
(85762, 'Utmanzai', 3171, 'KP', 167, 'PK', 34.18775000, 71.76274000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2581505'),
(85763, 'Vihari', 3176, 'PB', 167, 'PK', 30.04450000, 72.35560000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q2581505'),
(85764, 'Wana', 3173, 'TA', 167, 'PK', 32.29889000, 69.57250000, '2019-10-05 23:13:34', '2019-10-05 23:13:34', 1, 'Q1026635'),
(85765, 'Warah', 3175, 'SD', 167, 'PK', 27.44805000, 67.79654000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q1026635'),
(85766, 'Wazirabad', 3176, 'PB', 167, 'PK', 32.44324000, 74.12000000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q1026613'),
(85767, 'Yazman', 3176, 'PB', 167, 'PK', 29.12122000, 71.74459000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q8050625'),
(85768, 'Zafarwal', 3176, 'PB', 167, 'PK', 32.34464000, 74.89990000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q8050625'),
(85769, 'Zahir Pir', 3176, 'PB', 167, 'PK', 28.81284000, 70.52341000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q8076090'),
(85770, 'Zaida', 3171, 'KP', 167, 'PK', 34.05950000, 72.46690000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q4184172'),
(85771, 'Zhob', 3174, 'BA', 167, 'PK', 31.34082000, 69.44930000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q2665223'),
(85772, 'Zhob District', 3174, 'BA', 167, 'PK', 31.36444000, 69.20749000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q2281313'),
(85773, 'Ziarat', 3174, 'BA', 167, 'PK', 30.38244000, 67.72562000, '2019-10-05 23:13:35', '2019-10-05 23:13:35', 1, 'Q1968523'),
(85774, 'Ziārat District', 3174, 'BA', 167, 'PK', 30.43591000, 67.50962000, '2019-10-05 23:13:35', '2020-05-01 17:23:04', 1, 'Q2315423'),
(85775, 'Abramów', 1638, '06', 176, 'PL', 51.45647000, 22.31521000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q2277800'),
(85776, 'Adamów', 1638, '06', 176, 'PL', 51.74335000, 22.26414000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q2035593'),
(85777, 'Adamówka', 1626, '18', 176, 'PL', 50.25857000, 22.69595000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, 'Q2332206'),
(85778, 'Albigowa', 1626, '18', 176, 'PL', 50.01425000, 22.22414000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, 'Q4712370'),
(85779, 'Aleksandrów', 1638, '06', 176, 'PL', 50.46630000, 22.89225000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q2606206'),
(85780, 'Aleksandrów', 1636, '10', 176, 'PL', 51.27126000, 19.99005000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, 'Q2466714'),
(85781, 'Aleksandrów Kujawski', 1625, '04', 176, 'PL', 52.87659000, 18.69345000, '2019-10-05 23:13:35', '2025-03-19 16:06:21', 1, 'Q325124'),
(85782, 'Aleksandrów Łódzki', 1636, '10', 176, 'PL', 51.81965000, 19.30384000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, 'Q664371'),
(85783, 'Alwernia', 1635, '12', 176, 'PL', 50.06056000, 19.53953000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q983896'),
(85784, 'Andrespol', 1636, '10', 176, 'PL', 51.72783000, 19.64175000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, 'Q503371'),
(85785, 'Andrychów', 1635, '12', 176, 'PL', 49.85497000, 19.33834000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q984857'),
(85786, 'Annopol', 1638, '06', 176, 'PL', 50.88551000, 21.85678000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q567332'),
(85787, 'Augustów', 1632, '20', 176, 'PL', 53.84321000, 22.97979000, '2019-10-05 23:13:35', '2025-03-19 16:11:50', 1, 'Q567332'),
(85788, 'Babiak', 1634, '30', 176, 'PL', 52.34530000, 18.66663000, '2019-10-05 23:13:35', '2025-03-19 16:13:08', 1, 'Q554897'),
(85789, 'Babica', 1626, '18', 176, 'PL', 49.93476000, 21.87035000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, 'Q4837848'),
(85790, 'Babice', 1635, '12', 176, 'PL', 50.05565000, 19.19955000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q4837852'),
(85791, 'Babimost', 1631, '08', 176, 'PL', 52.16488000, 15.82769000, '2019-10-05 23:13:35', '2025-03-19 16:08:41', 1, 'Q797713'),
(85792, 'Baborów', 1622, '16', 176, 'PL', 50.15760000, 17.98513000, '2019-10-05 23:13:35', '2025-03-19 16:11:35', 1, 'Q592262'),
(85793, 'Baboszewo', 1637, '14', 176, 'PL', 52.68070000, 20.25527000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q1894607'),
(85794, 'Bachowice', 1635, '12', 176, 'PL', 49.95812000, 19.49369000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q2216196'),
(85795, 'Balice', 1635, '12', 176, 'PL', 50.08799000, 19.79462000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q138828'),
(85796, 'Baligród', 1626, '18', 176, 'PL', 49.33090000, 22.28566000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, 'Q3496277'),
(85797, 'Balin', 1635, '12', 176, 'PL', 50.16799000, 19.38340000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q2880878'),
(85798, 'Banie', 1633, '32', 176, 'PL', 53.10031000, 14.66228000, '2019-10-05 23:13:35', '2025-03-19 16:10:04', 1, 'Q557365'),
(85799, 'Banie Mazurskie', 1628, '28', 176, 'PL', 54.24662000, 22.03617000, '2019-10-05 23:13:35', '2025-03-19 16:12:57', 1, 'Q806576'),
(85800, 'Banino', 1624, '22', 176, 'PL', 54.39215000, 18.40622000, '2019-10-05 23:13:35', '2025-03-19 16:12:14', 1, 'Q2856096'),
(85801, 'Baniocha', 1637, '14', 176, 'PL', 52.01653000, 21.13984000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q4855776'),
(85802, 'Baranowo', 1637, '14', 176, 'PL', 53.17554000, 21.29803000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q1986598'),
(85803, 'Baranowo', 1634, '30', 176, 'PL', 52.43525000, 16.78631000, '2019-10-05 23:13:35', '2025-03-19 16:13:08', 1, 'Q1986598'),
(85804, 'Baranów', 1638, '06', 176, 'PL', 51.55786000, 22.13625000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q2147121'),
(85805, 'Baranów', 1634, '30', 176, 'PL', 51.26342000, 18.00470000, '2019-10-05 23:13:35', '2025-03-19 16:13:08', 1, 'Q2147121'),
(85806, 'Baranów Sandomierski', 1626, '18', 176, 'PL', 50.49912000, 21.54204000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, 'Q1837259'),
(85807, 'Barciany', 1628, '28', 176, 'PL', 54.21993000, 21.35347000, '2019-10-05 23:13:35', '2025-03-19 16:12:57', 1, 'Q599957'),
(85808, 'Barcin', 1625, '04', 176, 'PL', 52.86607000, 17.94625000, '2019-10-05 23:13:35', '2025-03-19 16:06:21', 1, 'Q1007355'),
(85809, 'Barczewo', 1628, '28', 176, 'PL', 53.83055000, 20.69112000, '2019-10-05 23:13:35', '2025-03-19 16:12:57', 1, 'Q807991'),
(85810, 'Bardo', 1629, '02', 176, 'PL', 50.50589000, 16.73986000, '2019-10-05 23:13:35', '2025-03-19 16:05:21', 1, 'Q808036'),
(85811, 'Barlinek', 1633, '32', 176, 'PL', 52.99464000, 15.21864000, '2019-10-05 23:13:35', '2025-03-19 16:10:04', 1, 'Q808344'),
(85812, 'Bartniczka', 1625, '04', 176, 'PL', 53.24776000, 19.60433000, '2019-10-05 23:13:35', '2025-03-19 16:06:21', 1, 'Q163846'),
(85813, 'Bartoszyce', 1628, '28', 176, 'PL', 54.25354000, 20.80819000, '2019-10-05 23:13:35', '2025-03-19 16:12:57', 1, 'Q809585'),
(85814, 'Baruchowo', 1625, '04', 176, 'PL', 52.49412000, 19.26496000, '2019-10-05 23:13:35', '2025-03-19 16:06:21', 1, 'Q809635'),
(85815, 'Barwałd Górny', 1635, '12', 176, 'PL', 49.86211000, 19.61746000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q2840435'),
(85816, 'Barwałd Średni', 1635, '12', 176, 'PL', 49.86632000, 19.59360000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q2202109'),
(85817, 'Barwice', 1633, '32', 176, 'PL', 53.74490000, 16.35530000, '2019-10-05 23:13:35', '2025-03-19 16:10:04', 1, 'Q809664'),
(85818, 'Batorz', 1638, '06', 176, 'PL', 50.85050000, 22.49313000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, '********'),
(85819, 'Baćkowice', 1630, '26', 176, 'PL', 50.79194000, 21.23211000, '2019-10-05 23:13:35', '2025-03-19 16:12:25', 1, '********'),
(85820, 'Bałtów', 1630, '26', 176, 'PL', 51.01845000, 21.54385000, '2019-10-05 23:13:35', '2025-03-19 16:12:25', 1, '********'),
(85821, 'Bażanowice', 1623, '24', 176, 'PL', 49.73791000, 18.70345000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, '********'),
(85822, 'Bedlno', 1636, '10', 176, 'PL', 52.20829000, 19.57592000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, 'Q610732'),
(85823, 'Bejsce', 1630, '26', 176, 'PL', 50.23903000, 20.59834000, '2019-10-05 23:13:35', '2025-03-19 16:12:25', 1, 'Q554433'),
(85824, 'Bemowo', 1637, '14', 176, 'PL', 52.25460000, 20.90844000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q7101'),
(85825, 'Besko', 1626, '18', 176, 'PL', 49.58757000, 21.95292000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, '********'),
(85826, 'Bestwina', 1623, '24', 176, 'PL', 49.89712000, 19.05776000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, '********'),
(85827, 'Bestwinka', 1623, '24', 176, 'PL', 49.93272000, 19.06694000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, '********'),
(85828, 'Bełchatów', 1636, '10', 176, 'PL', 51.36883000, 19.35671000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, 'Q157992'),
(85829, 'Bełk', 1623, '24', 176, 'PL', 50.13048000, 18.71667000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, 'Q4900423'),
(85830, 'Bełsznica', 1623, '24', 176, 'PL', 49.97811000, 18.36313000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, 'Q2912201'),
(85831, 'Bełżec', 1638, '06', 176, 'PL', 50.38453000, 23.43839000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q2179399'),
(85832, 'Bełżyce', 1638, '06', 176, 'PL', 51.17415000, 22.28027000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q854624'),
(85833, 'Biała', 1622, '16', 176, 'PL', 50.38587000, 17.66035000, '2019-10-05 23:13:35', '2025-03-19 16:11:35', 1, 'Q248144'),
(85834, 'Biała', 1637, '14', 176, 'PL', 52.60524000, 19.64956000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, '********'),
(85835, 'Biała Piska', 1628, '28', 176, 'PL', 53.61191000, 22.06321000, '2019-10-05 23:13:35', '2025-03-19 16:12:57', 1, 'Q855940'),
(85836, 'Biała Podlaska', 1638, '06', 176, 'PL', 52.03238000, 23.11652000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q209932'),
(85837, 'Biała Rawska', 1636, '10', 176, 'PL', 51.80779000, 20.47259000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, 'Q148956'),
(85838, 'Białaczów', 1636, '10', 176, 'PL', 51.29815000, 20.29724000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, '********'),
(85839, 'Białe Błota', 1625, '04', 176, 'PL', 53.09516000, 17.91621000, '2019-10-05 23:13:35', '2025-03-19 16:06:21', 1, 'Q617798'),
(85840, 'Białka', 1635, '12', 176, 'PL', 49.69306000, 19.67033000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, '********'),
(85841, 'Białka Tatrzańska', 1635, '12', 176, 'PL', 49.38975000, 20.10507000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, '********'),
(85842, 'Białobrzegi', 1626, '18', 176, 'PL', 50.10252000, 22.31907000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, '********'),
(85843, 'Białobrzegi', 1637, '14', 176, 'PL', 51.64695000, 20.95041000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q855976'),
(85844, 'Białogard', 1633, '32', 176, 'PL', 54.00696000, 15.98751000, '2019-10-05 23:13:35', '2025-03-19 16:10:04', 1, 'Q161919'),
(85845, 'Białowieża', 1632, '20', 176, 'PL', 52.70000000, 23.86667000, '2019-10-05 23:13:35', '2025-03-19 16:11:50', 1, 'Q2334100'),
(85846, 'Białołeka', 1637, '14', 176, 'PL', 52.32127000, 20.97204000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q855983'),
(85847, 'Białośliwie', 1634, '30', 176, 'PL', 53.10461000, 17.12533000, '2019-10-05 23:13:35', '2025-03-19 16:13:08', 1, 'Q855970'),
(85848, 'Biały Bór', 1633, '32', 176, 'PL', 53.89670000, 16.83543000, '2019-10-05 23:13:35', '2025-03-19 16:10:04', 1, 'Q856028'),
(85849, 'Biały Dunajec', 1635, '12', 176, 'PL', 49.37380000, 20.00898000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q2470939'),
(85850, 'Białystok', 1632, '20', 176, 'PL', 53.13333000, 23.16433000, '2019-10-05 23:13:35', '2025-03-19 16:11:50', 1, 'Q761'),
(85851, 'Biecz', 1635, '12', 176, 'PL', 49.73596000, 21.26301000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q460971'),
(85852, 'Bielany', 1637, '14', 176, 'PL', 52.29242000, 20.93531000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q857304'),
(85853, 'Bielany Wrocławskie', 1629, '02', 176, 'PL', 51.03610000, 16.96770000, '2019-10-05 23:13:35', '2025-03-19 16:05:21', 1, 'Q1916566'),
(85854, 'Bielawa', 1629, '02', 176, 'PL', 50.69075000, 16.62300000, '2019-10-05 23:13:35', '2025-03-19 16:05:21', 1, 'Q857331'),
(85855, 'Bielawy', 1636, '10', 176, 'PL', 52.07539000, 19.65557000, '2019-10-05 23:13:35', '2025-03-19 16:09:33', 1, 'Q2457726'),
(85856, 'Bielice', 1633, '32', 176, 'PL', 53.20022000, 14.72760000, '2019-10-05 23:13:35', '2025-03-19 16:10:04', 1, 'Q557530'),
(85857, 'Bieliny', 1626, '18', 176, 'PL', 50.44237000, 22.30482000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, '********'),
(85858, 'Bieliny', 1630, '26', 176, 'PL', 50.84947000, 20.94149000, '2019-10-05 23:13:35', '2025-03-19 16:12:25', 1, 'Q553647'),
(85859, 'Bielsk', 1637, '14', 176, 'PL', 52.67180000, 19.80500000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, '********'),
(85860, 'Bielsk Podlaski', 1632, '20', 176, 'PL', 52.76512000, 23.18647000, '2019-10-05 23:13:35', '2025-03-19 16:11:50', 1, '********'),
(85861, 'Bielsko-Biala', 1623, '24', 176, 'PL', 49.82245000, 19.04686000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, 'Q106583'),
(85863, 'Bieniewice', 1637, '14', 176, 'PL', 52.18268000, 20.56306000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, '********'),
(85864, 'Bierawa', 1622, '16', 176, 'PL', 50.28111000, 18.24177000, '2019-10-05 23:13:35', '2025-03-19 16:11:35', 1, 'Q980004'),
(85865, 'Bierdzany', 1622, '16', 176, 'PL', 50.81852000, 18.15808000, '2019-10-05 23:13:35', '2025-03-19 16:11:35', 1, 'Q857808'),
(85866, 'Bierutów', 1629, '02', 176, 'PL', 51.12443000, 17.54607000, '2019-10-05 23:13:35', '2025-03-19 16:05:21', 1, 'Q858130'),
(85867, 'Bieruń', 1623, '24', 176, 'PL', 50.09000000, 19.09291000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, 'Q325108'),
(85868, 'Bierzwnik', 1633, '32', 176, 'PL', 53.03567000, 15.66500000, '2019-10-05 23:13:35', '2025-03-19 16:10:04', 1, 'Q325108'),
(85869, 'Bieńkówka', 1635, '12', 176, 'PL', 49.77600000, 19.77179000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q4904706'),
(85870, 'Bieżuń', 1637, '14', 176, 'PL', 52.96107000, 19.88976000, '2019-10-05 23:13:35', '2025-03-19 16:11:22', 1, 'Q858312'),
(85871, 'Binarowa', 1635, '12', 176, 'PL', 49.75621000, 21.22816000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q863503'),
(85872, 'Bircza', 1626, '18', 176, 'PL', 49.69173000, 22.47854000, '2019-10-05 23:13:35', '2025-03-19 16:12:02', 1, 'Q2481888'),
(85873, 'Biskupice Radłowskie', 1635, '12', 176, 'PL', 50.12069000, 20.85943000, '2019-10-05 23:13:35', '2025-03-19 16:10:40', 1, 'Q2904701'),
(85874, 'Biskupiec', 1628, '28', 176, 'PL', 53.86467000, 20.95692000, '2019-10-05 23:13:35', '2025-03-19 16:12:57', 1, 'Q201186'),
(85875, 'Biskupin', 1629, '02', 176, 'PL', 51.10097000, 17.10442000, '2019-10-05 23:13:35', '2025-03-19 16:05:21', 1, 'Q201186'),
(85876, 'Biszcza', 1638, '06', 176, 'PL', 50.40146000, 22.65063000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q673585'),
(85877, 'Bisztynek', 1628, '28', 176, 'PL', 54.08633000, 20.90192000, '2019-10-05 23:13:35', '2025-03-19 16:12:57', 1, 'Q510842'),
(85878, 'Biłgoraj', 1638, '06', 176, 'PL', 50.54114000, 22.72204000, '2019-10-05 23:13:35', '2025-03-19 16:07:51', 1, 'Q319445'),
(85879, 'Blachownia', 1623, '24', 176, 'PL', 50.78015000, 18.96389000, '2019-10-05 23:13:35', '2025-03-19 16:12:46', 1, 'Q1483871'),
(85880, 'Bledzew', 1631, '08', 176, 'PL', 52.51711000, 15.41382000, '2019-10-05 23:13:35', '2025-03-19 16:08:41', 1, 'Q1014942'),
(85881, 'Blizanów', 1634, '30', 176, 'PL', 51.90372000, 18.01003000, '2019-10-05 23:13:35', '2025-03-19 16:13:08', 1, 'Q884545'),
(85882, 'Blizne', 1626, '18', 176, 'PL', 49.75332000, 21.97351000, '2019-10-05 23:13:36', '2025-03-19 16:12:02', 1, 'Q4927002'),
(85883, 'Bliżyn', 1630, '26', 176, 'PL', 51.10778000, 20.75935000, '2019-10-05 23:13:36', '2025-03-19 16:12:25', 1, 'Q3913984'),
(85884, 'Bobolice', 1633, '32', 176, 'PL', 53.95508000, 16.58893000, '2019-10-05 23:13:36', '2025-03-19 16:10:04', 1, 'Q511956'),
(85885, 'Bobowa', 1635, '12', 176, 'PL', 49.70866000, 20.94767000, '2019-10-05 23:13:36', '2025-03-19 16:10:40', 1, 'Q2341034'),
(85886, 'Bobowo', 1624, '22', 176, 'PL', 53.88378000, 18.55681000, '2019-10-05 23:13:37', '2025-03-19 16:12:14', 1, 'Q888835'),
(85887, 'Bobrowice', 1631, '08', 176, 'PL', 51.94850000, 15.09058000, '2019-10-05 23:13:37', '2025-03-19 16:08:41', 1, 'Q515216'),
(85888, 'Bobrowniki', 1636, '10', 176, 'PL', 52.06442000, 20.01949000, '2019-10-05 23:13:37', '2025-03-19 16:09:33', 1, 'Q4935979'),
(85889, 'Bobrowniki', 1625, '04', 176, 'PL', 52.78086000, 18.96026000, '2019-10-05 23:13:37', '2025-03-19 16:06:21', 1, 'Q4935979'),
(85890, 'Bobrowniki', 1623, '24', 176, 'PL', 50.37985000, 18.98661000, '2019-10-05 23:13:37', '2025-03-19 16:12:46', 1, 'Q4935979'),
(85891, 'Bobrowo', 1625, '04', 176, 'PL', 53.28549000, 19.27053000, '2019-10-05 23:13:37', '2025-03-19 16:06:21', 1, 'Q617826'),
(85892, 'Bochnia', 1635, '12', 176, 'PL', 49.96905000, 20.43028000, '2019-10-05 23:13:37', '2025-03-19 16:10:40', 1, 'Q815903'),
(85893, 'Bodzanów', 1637, '14', 176, 'PL', 52.49992000, 20.02945000, '2019-10-05 23:13:37', '2025-03-19 16:11:22', 1, 'Q2328720'),
(85894, 'Bodzentyn', 1630, '26', 176, 'PL', 50.94115000, 20.95719000, '2019-10-05 23:13:37', '2025-03-19 16:12:25', 1, 'Q890126'),
(85895, 'Bogatynia', 1629, '02', 176, 'PL', 50.90747000, 14.95634000, '2019-10-05 23:13:37', '2025-03-19 16:05:21', 1, 'Q123438'),
(85896, 'Bogdaniec', 1631, '08', 176, 'PL', 52.68897000, 15.07127000, '2019-10-05 23:13:37', '2025-03-19 16:08:41', 1, 'Q123438'),
(85897, 'Bogoria', 1630, '26', 176, 'PL', 50.65175000, 21.26000000, '2019-10-05 23:13:37', '2025-03-19 16:12:25', 1, 'Q553664'),
(85898, 'Boguchwała', 1626, '18', 176, 'PL', 49.98473000, 21.94528000, '2019-10-05 23:13:37', '2025-03-19 16:12:02', 1, 'Q890681'),
(85899, 'Boguszów-Gorce', 1629, '02', 176, 'PL', 50.75514000, 16.20494000, '2019-10-05 23:13:37', '2025-03-19 16:05:21', 1, 'Q890709'),
(85900, 'Boguty-Pianki', 1637, '14', 176, 'PL', 52.71680000, 22.41546000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q2580095'),
(85901, 'Bojadła', 1631, '08', 176, 'PL', 51.95321000, 15.81036000, '2019-10-05 23:13:38', '2025-03-19 16:08:41', 1, 'Q2580095'),
(85902, 'Bojano', 1624, '22', 176, 'PL', 54.47123000, 18.38408000, '2019-10-05 23:13:38', '2025-03-19 16:12:14', 1, 'Q4938638'),
(85903, 'Bojanowo', 1634, '30', 176, 'PL', 51.70749000, 16.74827000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q987012'),
(85904, 'Bojanowo Stare', 1634, '30', 176, 'PL', 51.99302000, 16.58369000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q7601625'),
(85905, 'Bojanów', 1626, '18', 176, 'PL', 50.42531000, 21.95111000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q2174309'),
(85906, 'Bojszowy', 1623, '24', 176, 'PL', 50.05780000, 19.10145000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q891194'),
(85907, 'Bojszowy Nowe', 1623, '24', 176, 'PL', 50.05284000, 19.05012000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q4938693'),
(85908, 'Bolechowice', 1635, '12', 176, 'PL', 50.14831000, 19.79273000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q4939140'),
(85909, 'Boleszkowice', 1633, '32', 176, 'PL', 52.72493000, 14.56901000, '2019-10-05 23:13:38', '2025-03-19 16:10:04', 1, 'Q558777'),
(85910, 'Bolesław', 1635, '12', 176, 'PL', 50.29729000, 19.48073000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q10864397'),
(85911, 'Bolesławiec', 1629, '02', 176, 'PL', 51.26418000, 15.56970000, '2019-10-05 23:13:38', '2025-03-19 16:05:21', 1, 'Q668389'),
(85912, 'Bolesławiec', 1636, '10', 176, 'PL', 51.19866000, 18.19147000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q2169150'),
(85913, 'Bolimów', 1636, '10', 176, 'PL', 52.07671000, 20.16352000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q627392'),
(85914, 'Bolków', 1629, '02', 176, 'PL', 50.92203000, 16.10111000, '2019-10-05 23:13:38', '2025-03-19 16:05:21', 1, 'Q2461704'),
(85915, 'Bolszewo', 1624, '22', 176, 'PL', 54.61801000, 18.17585000, '2019-10-05 23:13:38', '2025-03-19 16:12:14', 1, 'Q2577584'),
(85916, 'Bolęcin', 1635, '12', 176, 'PL', 50.11750000, 19.48116000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q1817195'),
(85917, 'Boniewo', 1625, '04', 176, 'PL', 52.46532000, 18.89116000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q2462922'),
(85918, 'Borek', 1635, '12', 176, 'PL', 50.01778000, 20.53087000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q935966'),
(85919, 'Borek Wielkopolski', 1634, '30', 176, 'PL', 51.91674000, 17.24133000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q1005972'),
(85920, 'Borki', 1638, '06', 176, 'PL', 51.72161000, 22.52129000, '2019-10-05 23:13:38', '2025-03-19 16:07:51', 1, 'Q1005972'),
(85921, 'Borkowice', 1637, '14', 176, 'PL', 51.32025000, 20.68339000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q2258025'),
(85922, 'Borne Sulinowo', 1633, '32', 176, 'PL', 53.57661000, 16.53395000, '2019-10-05 23:13:38', '2025-03-19 16:10:04', 1, 'Q1027526'),
(85923, 'Boronów', 1623, '24', 176, 'PL', 50.67460000, 18.90678000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q894061'),
(85924, 'Borowa', 1626, '18', 176, 'PL', 50.38548000, 21.35150000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q2061801'),
(85925, 'Borowie', 1637, '14', 176, 'PL', 51.94908000, 21.76580000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q2224725'),
(85926, 'Borowno', 1623, '24', 176, 'PL', 50.93247000, 19.27380000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q4946478'),
(85927, 'Borucin', 1623, '24', 176, 'PL', 50.00763000, 18.15748000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q549001'),
(85928, 'Borzechów', 1638, '06', 176, 'PL', 51.09258000, 22.28414000, '2019-10-05 23:13:38', '2025-03-19 16:07:51', 1, 'Q2246402'),
(85929, 'Borzytuchom', 1624, '22', 176, 'PL', 54.20021000, 17.36801000, '2019-10-05 23:13:38', '2025-03-19 16:12:14', 1, 'Q894322'),
(85930, 'Borzęcin', 1635, '12', 176, 'PL', 50.06540000, 20.71103000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q894322'),
(85931, 'Borzęta', 1635, '12', 176, 'PL', 49.86228000, 19.97924000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q4947007'),
(85932, 'Bozkow', 1629, '02', 176, 'PL', 50.51315000, 16.57528000, '2019-10-05 23:13:38', '2025-03-19 16:05:21', 1, 'Q830423'),
(85933, 'Boćki', 1632, '20', 176, 'PL', 52.65155000, 23.04485000, '2019-10-05 23:13:38', '2025-03-19 16:11:50', 1, 'Q615436'),
(85934, 'Bralin', 1634, '30', 176, 'PL', 51.28581000, 17.90325000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q556572'),
(85935, 'Branice', 1622, '16', 176, 'PL', 50.05108000, 17.79399000, '2019-10-05 23:13:38', '2025-03-19 16:11:35', 1, 'Q735041'),
(85936, 'Braniewo', 1628, '28', 176, 'PL', 54.37971000, 19.81959000, '2019-10-05 23:13:38', '2025-03-19 16:12:57', 1, 'Q160814'),
(85937, 'Brańsk', 1632, '20', 176, 'PL', 52.74440000, 22.83774000, '2019-10-05 23:13:38', '2025-03-19 16:11:50', 1, 'Q607410'),
(85938, 'Brańszczyk', 1637, '14', 176, 'PL', 52.62931000, 21.58745000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q302205'),
(85939, 'Brdów', 1634, '30', 176, 'PL', 52.35390000, 18.72980000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q903461'),
(85940, 'Brenna', 1623, '24', 176, 'PL', 49.72576000, 18.90249000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q903461'),
(85941, 'Brenno', 1634, '30', 176, 'PL', 51.92257000, 16.21488000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q9179208'),
(85942, 'Brochów', 1637, '14', 176, 'PL', 52.31950000, 20.26257000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q736201'),
(85943, 'Brodnica', 1625, '04', 176, 'PL', 53.25967000, 19.39653000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q690030'),
(85944, 'Brodnica', 1634, '30', 176, 'PL', 52.14123000, 16.89096000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q555765'),
(85945, 'Brody', 1630, '26', 176, 'PL', 51.02466000, 21.22147000, '2019-10-05 23:13:38', '2025-03-19 16:12:25', 1, 'Q554042'),
(85946, 'Brody', 1635, '12', 176, 'PL', 49.86742000, 19.69746000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q1962586'),
(85947, 'Brody', 1631, '08', 176, 'PL', 51.79046000, 14.77335000, '2019-10-05 23:13:38', '2025-03-19 16:08:41', 1, 'Q2241673'),
(85948, 'Brody-Parcele', 1637, '14', 176, 'PL', 52.47797000, 20.74974000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q2094311'),
(85949, 'Brodła', 1635, '12', 176, 'PL', 50.04331000, 19.58879000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q2616132'),
(85950, 'Brojce', 1633, '32', 176, 'PL', 53.95705000, 15.35975000, '2019-10-05 23:13:38', '2025-03-19 16:10:04', 1, 'Q729148'),
(85951, 'Brok', 1637, '14', 176, 'PL', 52.69948000, 21.85704000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q925506'),
(85952, 'Broniszewice', 1634, '30', 176, 'PL', 51.96696000, 17.81648000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q4973925'),
(85953, 'Bronów', 1623, '24', 176, 'PL', 49.87782000, 18.92103000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q4974287'),
(85954, 'Brudzew', 1634, '30', 176, 'PL', 52.09949000, 18.60432000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q992269'),
(85955, 'Brudzeń Duży', 1637, '14', 176, 'PL', 52.66884000, 19.50399000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q2352607'),
(85956, 'Brusy', 1624, '22', 176, 'PL', 53.88446000, 17.71786000, '2019-10-05 23:13:38', '2025-03-19 16:12:14', 1, 'Q994662'),
(85957, 'Brwinów', 1637, '14', 176, 'PL', 52.14269000, 20.71697000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q2328687'),
(85958, 'Brzeg', 1622, '16', 176, 'PL', 50.86079000, 17.46740000, '2019-10-05 23:13:38', '2025-03-19 16:11:35', 1, 'Q214640'),
(85959, 'Brzeg Dolny', 1629, '02', 176, 'PL', 51.27299000, 16.70815000, '2019-10-05 23:13:38', '2025-03-19 16:05:21', 1, 'Q995429'),
(85960, 'Brzesko', 1635, '12', 176, 'PL', 49.96911000, 20.60606000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q324884'),
(85961, 'Brzeszcze', 1635, '12', 176, 'PL', 49.98203000, 19.15157000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q916514'),
(85962, 'Brzezinka', 1635, '12', 176, 'PL', 50.04237000, 19.19020000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q995437'),
(85963, 'Brzeziny', 1630, '26', 176, 'PL', 50.77273000, 20.57319000, '2019-10-05 23:13:38', '2025-03-19 16:12:25', 1, 'Q4981351'),
(85964, 'Brzeziny', 1636, '10', 176, 'PL', 51.80023000, 19.75144000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q324917'),
(85965, 'Brzeziny Śląskie', 1623, '24', 176, 'PL', 50.35439000, 18.98129000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q324917'),
(85966, 'Brześć Kujawski', 1625, '04', 176, 'PL', 52.60532000, 18.90173000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q985649'),
(85967, 'Brzeźnica', 1626, '18', 176, 'PL', 50.10065000, 21.48025000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q4981461'),
(85968, 'Brzeźnica', 1635, '12', 176, 'PL', 49.96497000, 19.61952000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q2192638'),
(85969, 'Brzeźnio', 1636, '10', 176, 'PL', 51.49400000, 18.62234000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q985745'),
(85970, 'Brzostek', 1626, '18', 176, 'PL', 49.87954000, 21.41102000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q776418'),
(85971, 'Brzozie', 1625, '04', 176, 'PL', 53.32554000, 19.60485000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q618176'),
(85972, 'Brzozów', 1626, '18', 176, 'PL', 49.69501000, 22.01926000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q653797'),
(85973, 'Brzuze', 1625, '04', 176, 'PL', 53.05458000, 19.26195000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q984897'),
(85974, 'Brzyska', 1626, '18', 176, 'PL', 49.82226000, 21.39004000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q4981732'),
(85975, 'Brzóza Królewska', 1626, '18', 176, 'PL', 50.23908000, 22.32559000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q2207059'),
(85976, 'Brzóza Stadnicka', 1626, '18', 176, 'PL', 50.19965000, 22.28233000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q2746617'),
(85977, 'Brzączowice', 1635, '12', 176, 'PL', 49.87478000, 20.03709000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q4981779'),
(85978, 'Brójce', 1636, '10', 176, 'PL', 51.66436000, 19.64802000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q2284958'),
(85979, 'Brójce', 1631, '08', 176, 'PL', 52.31745000, 15.67414000, '2019-10-05 23:13:38', '2025-03-19 16:08:41', 1, 'Q4981893'),
(85980, 'Brąszewice', 1636, '10', 176, 'PL', 51.49902000, 18.44982000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q985713'),
(85981, 'Buczek', 1636, '10', 176, 'PL', 51.50228000, 19.16419000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q2467269'),
(85982, 'Buczkowice', 1623, '24', 176, 'PL', 49.72858000, 19.06908000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q2723435'),
(85983, 'Budziszewice', 1636, '10', 176, 'PL', 51.66739000, 19.93581000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q4985322'),
(85984, 'Budzyń', 1634, '30', 176, 'PL', 52.88954000, 16.98812000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q4985322'),
(85985, 'Budzów', 1629, '02', 176, 'PL', 50.59337000, 16.71038000, '2019-10-05 23:13:38', '2025-03-19 16:05:21', 1, 'Q4985348'),
(85986, 'Budzów', 1635, '12', 176, 'PL', 49.77622000, 19.67274000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q2296118'),
(85987, 'Bujaków', 1623, '24', 176, 'PL', 49.85182000, 19.19432000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q3958231'),
(85988, 'Buk', 1634, '30', 176, 'PL', 52.35532000, 16.51958000, '2019-10-05 23:13:38', '2025-03-19 16:13:08', 1, 'Q667588'),
(85989, 'Bukowiec', 1625, '04', 176, 'PL', 53.43383000, 18.24048000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q2463043'),
(85990, 'Bukowina Tatrzańska', 1635, '12', 176, 'PL', 49.34302000, 20.10807000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q1003408'),
(85991, 'Bukowno', 1635, '12', 176, 'PL', 50.26474000, 19.45962000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q985653'),
(85992, 'Bukowsko', 1626, '18', 176, 'PL', 49.48039000, 22.06329000, '2019-10-05 23:13:38', '2025-03-19 16:12:02', 1, 'Q2664126'),
(85993, 'Bulkowo', 1637, '14', 176, 'PL', 52.54087000, 20.11889000, '2019-10-05 23:13:38', '2025-03-19 16:11:22', 1, 'Q2240639'),
(85994, 'Bulowice', 1635, '12', 176, 'PL', 49.87650000, 19.28873000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q34784413'),
(85995, 'Burzenin', 1636, '10', 176, 'PL', 51.46077000, 18.83233000, '2019-10-05 23:13:38', '2025-03-19 16:09:33', 1, 'Q769395'),
(85996, 'Busko-Zdrój', 1630, '26', 176, 'PL', 50.47078000, 20.71884000, '2019-10-05 23:13:38', '2025-03-19 16:12:25', 1, 'Q256782'),
(85997, 'Bychawa', 1638, '06', 176, 'PL', 51.01608000, 22.53296000, '2019-10-05 23:13:38', '2025-03-19 16:07:51', 1, 'Q995820'),
(85998, 'Byczyna', 1622, '16', 176, 'PL', 51.11387000, 18.21413000, '2019-10-05 23:13:38', '2025-03-19 16:11:35', 1, 'Q990828'),
(85999, 'Bydgoszcz', 1625, '04', 176, 'PL', 53.12350000, 18.00762000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q41252'),
(86000, 'Bystra', 1635, '12', 176, 'PL', 49.64796000, 19.77994000, '2019-10-05 23:13:38', '2025-03-19 16:10:40', 1, 'Q5004546'),
(86001, 'Bystra', 1623, '24', 176, 'PL', 49.76042000, 19.05973000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q640109'),
(86002, 'Bystrzyca', 1629, '02', 176, 'PL', 50.96048000, 17.39702000, '2019-10-05 23:13:38', '2025-03-19 16:05:21', 1, 'Q5004582'),
(86003, 'Bystrzyca Kłodzka', 1629, '02', 176, 'PL', 50.30179000, 16.64231000, '2019-10-05 23:13:38', '2025-03-19 16:05:21', 1, 'Q991022'),
(86004, 'Bytnica', 1631, '08', 176, 'PL', 52.15066000, 15.16946000, '2019-10-05 23:13:38', '2025-03-19 16:08:41', 1, 'Q991022'),
(86005, 'Bytom', 1623, '24', 176, 'PL', 50.34802000, 18.93282000, '2019-10-05 23:13:38', '2025-03-19 16:12:46', 1, 'Q102350'),
(86006, 'Bytom Odrzański', 1631, '08', 176, 'PL', 51.73062000, 15.82362000, '2019-10-05 23:13:38', '2025-03-19 16:08:41', 1, 'Q987471'),
(86007, 'Bytoń', 1625, '04', 176, 'PL', 52.55757000, 18.59522000, '2019-10-05 23:13:38', '2025-03-19 16:06:21', 1, 'Q984993'),
(86008, 'Bytów', 1624, '22', 176, 'PL', 54.17057000, 17.49187000, '2019-10-05 23:13:39', '2025-03-19 16:12:14', 1, 'Q326577'),
(86009, 'Bąków', 1623, '24', 176, 'PL', 49.89342000, 18.71495000, '2019-10-05 23:13:39', '2025-03-19 16:12:46', 1, 'Q5005509'),
(86010, 'Bębło', 1635, '12', 176, 'PL', 50.18053000, 19.78741000, '2019-10-05 23:13:39', '2025-03-19 16:10:40', 1, 'Q367087'),
(86011, 'Bęczarka', 1635, '12', 176, 'PL', 49.87793000, 19.86723000, '2019-10-05 23:13:39', '2025-03-19 16:10:40', 1, 'Q4557003'),
(86012, 'Będków', 1636, '10', 176, 'PL', 51.58763000, 19.74956000, '2019-10-05 23:13:39', '2025-03-19 16:09:33', 1, 'Q1824768'),
(86013, 'Będzin', 1623, '24', 176, 'PL', 50.32607000, 19.12565000, '2019-10-05 23:13:39', '2025-03-19 16:12:46', 1, 'Q461513'),
(86014, 'Błaszki', 1636, '10', 176, 'PL', 51.65163000, 18.43472000, '2019-10-05 23:13:39', '2025-03-19 16:09:33', 1, 'Q2402841');

