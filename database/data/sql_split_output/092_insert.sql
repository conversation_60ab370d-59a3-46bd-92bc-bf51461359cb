INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(46303, 'Saint-Martin-sur-Ocre', 4818, '<PERSON>V<PERSON>', 75, 'FR', 47.65942000, 2.65810000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46304, 'Saint-Martin-sur-Oust', 4807, 'BRE', 75, 'FR', 47.74600000, -2.25343000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46305, '<PERSON><PERSON><PERSON><PERSON>', 4795, 'NAQ', 75, 'FR', 45.70674000, 0.75908000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46306, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 4799, '<PERSON><PERSON>', 75, 'FR', 43.76795000, 3.85814000, '2019-10-05 22:48:54', '2020-05-01 17:22:46', 1, '********'),
(46307, '<PERSON>-Mathurin', 4802, 'PDL', 75, 'FR', 46.56493000, -1.71389000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46308, '<PERSON>-Mathurin-sur-Loire', 4802, 'PDL', 75, 'FR', 47.41667000, -0.31667000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46309, 'Saint-Maur', 4818, 'CVL', 75, 'FR', 46.80657000, 1.63904000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46310, 'Saint-Maur-des-Fossés', 4796, 'IDF', 75, 'FR', 48.79395000, 2.49323000, '2019-10-05 22:48:54', '2020-05-01 17:22:43', 1, '********'),
(46311, 'Saint-Maurice', 4796, 'IDF', 75, 'FR', 48.82182000, 2.42716000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46312, 'Saint-Maurice-Montcouronne', 4796, 'IDF', 75, 'FR', 48.58288000, 2.12504000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46313, 'Saint-Maurice-de-Beynost', 4798, 'ARA', 75, 'FR', 45.83440000, 4.97750000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46314, 'Saint-Maurice-de-Gourdans', 4798, 'ARA', 75, 'FR', 45.82248000, 5.19450000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46315, 'Saint-Maurice-de-Lignon', 4798, 'ARA', 75, 'FR', 45.22430000, 4.13880000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46316, 'Saint-Maurice-en-Gourgois', 4798, 'ARA', 75, 'FR', 45.40127000, 4.18253000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46317, 'Saint-Maurice-la-Clouère', 4795, 'NAQ', 75, 'FR', 46.37804000, 0.41253000, '2019-10-05 22:48:54', '2020-05-01 17:22:46', 1, '********'),
(46318, 'Saint-Maurice-la-Souterraine', 4795, 'NAQ', 75, 'FR', 46.21388000, 1.43130000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46319, 'Saint-Maurice-sur-Dargoire', 4798, 'ARA', 75, 'FR', 45.58224000, 4.63120000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46320, 'Saint-Maurice-sur-Fessard', 4818, 'CVL', 75, 'FR', 47.99231000, 2.62157000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46321, 'Saint-Maurice-sur-Moselle', 4820, 'GES', 75, 'FR', 47.85889000, 6.82477000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46322, 'Saint-Mauront', 4812, 'PAC', 75, 'FR', 43.31552000, 5.37538000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46323, 'Saint-Max', 4820, 'GES', 75, 'FR', 48.70426000, 6.20686000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q607016'),
(46324, 'Saint-Maximin', 4828, 'HDF', 75, 'FR', 49.22182000, 2.45359000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q676780'),
(46325, 'Saint-Maximin-la-Sainte-Baume', 4812, 'PAC', 75, 'FR', 43.44813000, 5.86081000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q848718'),
(46326, 'Saint-Maxire', 4795, 'NAQ', 75, 'FR', 46.39911000, -0.47988000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q848718'),
(46327, 'Saint-Melaine-sur-Aubance', 4802, 'PDL', 75, 'FR', 47.36667000, -0.50000000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46328, 'Saint-Memmie', 4820, 'GES', 75, 'FR', 48.95247000, 4.38409000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46329, 'Saint-Menet', 4812, 'PAC', 75, 'FR', 43.28997000, 5.50427000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46330, 'Saint-Menges', 4820, 'GES', 75, 'FR', 49.73873000, 4.92628000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q978427'),
(46331, 'Saint-Mesmin', 4802, 'PDL', 75, 'FR', 46.79369000, -0.73262000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q978427'),
(46332, 'Saint-Mexant', 4795, 'NAQ', 75, 'FR', 45.28514000, 1.65799000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q978427'),
(46333, 'Saint-Michel', 4795, 'NAQ', 75, 'FR', 45.65000000, 0.10000000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q978427'),
(46334, 'Saint-Michel', 4828, 'HDF', 75, 'FR', 49.91952000, 4.13278000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q457052'),
(46335, 'Saint-Michel-Chef-Chef', 4802, 'PDL', 75, 'FR', 47.18072000, -2.14869000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46336, 'Saint-Michel-Mont-Mercure', 4802, 'PDL', 75, 'FR', 46.82941000, -0.88298000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q510824'),
(46337, 'Saint-Michel-de-Maurienne', 4798, 'ARA', 75, 'FR', 45.22011000, 6.46868000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q510824'),
(46338, 'Saint-Michel-le-Cloucq', 4802, 'PDL', 75, 'FR', 46.48500000, -0.75262000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46339, 'Saint-Michel-l’Observatoire', 4812, 'PAC', 75, 'FR', 43.90977000, 5.71584000, '2019-10-05 22:48:54', '2020-05-01 17:22:47', 1, '********'),
(46340, 'Saint-Michel-sur-Meurthe', 4820, 'GES', 75, 'FR', 48.32219000, 6.89024000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, '********'),
(46341, 'Saint-Michel-sur-Orge', 4796, 'IDF', 75, 'FR', 48.63479000, 2.30831000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q275562'),
(46342, 'Saint-Mihiel', 4820, 'GES', 75, 'FR', 48.88746000, 5.55099000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q194932'),
(46343, 'Saint-Mitre', 4812, 'PAC', 75, 'FR', 43.34640000, 5.42274000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q194932'),
(46344, 'Saint-Mitre-les-Remparts', 4812, 'PAC', 75, 'FR', 43.45503000, 5.01429000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q640845'),
(46345, 'Saint-Molf', 4802, 'PDL', 75, 'FR', 47.39167000, -2.42357000, '2019-10-05 22:48:54', '2019-10-05 22:48:54', 1, 'Q666111'),
(46346, 'Saint-Morillon', 4795, 'NAQ', 75, 'FR', 44.65060000, -0.50322000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q666111'),
(46347, 'Saint-Médard-de-Guizières', 4795, 'NAQ', 75, 'FR', 45.01667000, -0.05000000, '2019-10-05 22:48:55', '2020-05-01 17:22:46', 1, 'Q666111'),
(46348, 'Saint-Médard-de-Mussidan', 4795, 'NAQ', 75, 'FR', 45.03333000, 0.35000000, '2019-10-05 22:48:55', '2020-05-01 17:22:46', 1, 'Q204736'),
(46349, 'Saint-Médard-d’Eyrans', 4795, 'NAQ', 75, 'FR', 44.71667000, -0.51667000, '2019-10-05 22:48:55', '2020-05-01 17:22:46', 1, 'Q457662'),
(46350, 'Saint-Médard-en-Jalles', 4795, 'NAQ', 75, 'FR', 44.89692000, -0.72136000, '2019-10-05 22:48:55', '2020-05-01 17:22:46', 1, 'Q238434'),
(46351, 'Saint-Médard-sur-Ille', 4807, 'BRE', 75, 'FR', 48.27202000, -1.65968000, '2019-10-05 22:48:55', '2020-05-01 17:22:44', 1, 'Q238434'),
(46352, 'Saint-Méen-le-Grand', 4807, 'BRE', 75, 'FR', 48.18971000, -2.19486000, '2019-10-05 22:48:55', '2020-05-01 17:22:44', 1, 'Q390437'),
(46353, 'Saint-Méloir-des-Ondes', 4807, 'BRE', 75, 'FR', 48.63790000, -1.90448000, '2019-10-05 22:48:55', '2020-05-01 17:22:44', 1, 'Q390437'),
(46354, 'Saint-Même-le-Tenu', 4802, 'PDL', 75, 'FR', 47.02005000, -1.79459000, '2019-10-05 22:48:55', '2020-05-01 17:22:46', 1, 'Q645994'),
(46355, 'Saint-Même-les-Carrières', 4795, 'NAQ', 75, 'FR', 45.65000000, -0.15000000, '2019-10-05 22:48:55', '2020-05-01 17:22:46', 1, 'Q939108'),
(46356, 'Saint-Nabord', 4820, 'GES', 75, 'FR', 48.05171000, 6.58248000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q186880'),
(46357, 'Saint-Nauphary', 4799, 'OCC', 75, 'FR', 43.96712000, 1.42549000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q186880'),
(46358, 'Saint-Nazaire', 4802, 'PDL', 75, 'FR', 47.27956000, -2.20993000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q152027'),
(46359, 'Saint-Nazaire', 4799, 'OCC', 75, 'FR', 42.66790000, 2.99168000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q152027'),
(46360, 'Saint-Nazaire-d’Aude', 4799, 'OCC', 75, 'FR', 43.24518000, 2.89443000, '2019-10-05 22:48:55', '2020-05-01 17:22:46', 1, 'Q152027'),
(46361, 'Saint-Nazaire-les-Eymes', 4798, 'ARA', 75, 'FR', 45.24921000, 5.85254000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q152027'),
(46362, 'Saint-Nicolas', 4828, 'HDF', 75, 'FR', 50.30413000, 2.77939000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q152027'),
(46363, 'Saint-Nicolas-de-Bourgueil', 4818, 'CVL', 75, 'FR', 47.28493000, 0.12727000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q152027'),
(46364, 'Saint-Nicolas-de-Port', 4820, 'GES', 75, 'FR', 48.62857000, 6.29668000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q125278'),
(46365, 'Saint-Nicolas-de-Redon', 4802, 'PDL', 75, 'FR', 47.64343000, -2.06305000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, '********'),
(46366, 'Saint-Nicolas-de-la-Grave', 4799, 'OCC', 75, 'FR', 44.06417000, 1.02280000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q639040'),
(46367, 'Saint-Nicolas-de-la-Taille', 4804, 'NOR', 75, 'FR', 49.51085000, 0.47405000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, '********'),
(46368, 'Saint-Nicolas-du-Pélem', 4807, 'BRE', 75, 'FR', 48.31222000, -3.16465000, '2019-10-05 22:48:55', '2020-05-01 17:22:44', 1, '********'),
(46369, 'Saint-Nicolas-d’Aliermont', 4804, 'NOR', 75, 'FR', 49.87857000, 1.22486000, '2019-10-05 22:48:55', '2020-05-01 17:22:45', 1, '********'),
(46370, 'Saint-Nizier-sous-Charlieu', 4798, 'ARA', 75, 'FR', 46.15290000, 4.12399000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, '********'),
(46371, 'Saint-Nolff', 4807, 'BRE', 75, 'FR', 47.70365000, -2.65209000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q128974'),
(46372, 'Saint-Nom-la-Bretêche', 4796, 'IDF', 75, 'FR', 48.85942000, 2.02233000, '2019-10-05 22:48:55', '2020-05-01 17:22:43', 1, 'Q128974'),
(46373, 'Saint-Omer', 4828, 'HDF', 75, 'FR', 50.74834000, 2.26091000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208793'),
(46374, 'Saint-Omer-en-Chaussée', 4828, 'HDF', 75, 'FR', 49.53142000, 2.00335000, '2019-10-05 22:48:55', '2020-05-01 17:22:45', 1, 'Q208793'),
(46375, 'Saint-Orens-de-Gameville', 4799, 'OCC', 75, 'FR', 43.55402000, 1.53411000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208793'),
(46376, 'Saint-Ouen', 4796, 'IDF', 75, 'FR', 48.90654000, 2.33339000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208889'),
(46377, 'Saint-Ouen', 4818, 'CVL', 75, 'FR', 47.81404000, 1.08067000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208889'),
(46378, 'Saint-Ouen', 4828, 'HDF', 75, 'FR', 50.03819000, 2.12088000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208889'),
(46379, 'Saint-Ouen-de-Thouberville', 4804, 'NOR', 75, 'FR', 49.35726000, 0.88848000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208889'),
(46380, 'Saint-Ouen-des-Alleux', 4807, 'BRE', 75, 'FR', 48.32805000, -1.42590000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208889'),
(46381, 'Saint-Ouen-du-Tilleul', 4804, 'NOR', 75, 'FR', 49.29723000, 0.94760000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208889'),
(46382, 'Saint-Ouen-en-Belin', 4802, 'PDL', 75, 'FR', 47.83302000, 0.20924000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q208889'),
(46383, 'Saint-Ouen-l’Aumône', 4796, 'IDF', 75, 'FR', 49.04353000, 2.12134000, '2019-10-05 22:48:55', '2020-05-01 17:22:43', 1, 'Q208889'),
(46384, 'Saint-Ours', 4798, 'ARA', 75, 'FR', 45.85024000, 2.89163000, '2019-10-05 22:48:55', '2019-10-05 22:48:55', 1, 'Q752053'),
(46385, 'Saint-Ouën-des-Toits', 4802, 'PDL', 75, 'FR', 48.13333000, -0.90000000, '2019-10-05 22:48:56', '2020-05-01 17:22:46', 1, 'Q752053'),
(46386, 'Saint-Pabu', 4807, 'BRE', 75, 'FR', 48.56667000, -4.60000000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q178553'),
(46387, 'Saint-Pair-sur-Mer', 4804, 'NOR', 75, 'FR', 48.81455000, -1.56761000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46388, 'Saint-Pal-de-Mons', 4798, 'ARA', 75, 'FR', 45.24634000, 4.27416000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46389, 'Saint-Palais', 4795, 'NAQ', 75, 'FR', 43.32867000, -1.03333000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46390, 'Saint-Palais-sur-Mer', 4795, 'NAQ', 75, 'FR', 45.64255000, -1.08810000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46391, 'Saint-Pantaléon-de-Larche', 4795, 'NAQ', 75, 'FR', 45.14122000, 1.44652000, '2019-10-05 22:48:56', '2020-05-01 17:22:46', 1, '********'),
(46392, 'Saint-Pardoux', 4795, 'NAQ', 75, 'FR', 46.57155000, -0.30542000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46393, 'Saint-Pardoux-Isaac', 4795, 'NAQ', 75, 'FR', 44.61190000, 0.37345000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46394, 'Saint-Pardoux-la-Rivière', 4795, 'NAQ', 75, 'FR', 45.49388000, 0.74651000, '2019-10-05 22:48:56', '2020-05-01 17:22:46', 1, '********'),
(46395, 'Saint-Pargoire', 4799, 'OCC', 75, 'FR', 43.52766000, 3.51870000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46396, 'Saint-Parize-le-Châtel', 4825, 'BFC', 75, 'FR', 46.85447000, 3.18267000, '2019-10-05 22:48:56', '2020-05-01 17:22:44', 1, '********'),
(46397, 'Saint-Parres-aux-Tertres', 4820, 'GES', 75, 'FR', 48.29780000, 4.11752000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46398, 'Saint-Paterne', 4802, 'PDL', 75, 'FR', 48.41614000, 0.11271000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '*********'),
(46399, 'Saint-Paterne-Racan', 4818, 'CVL', 75, 'FR', 47.60213000, 0.48478000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '*********'),
(46400, 'Saint-Pathus', 4796, 'IDF', 75, 'FR', 49.07136000, 2.79886000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46401, 'Saint-Paul', 4828, 'HDF', 75, 'FR', 49.42962000, 2.00755000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46402, 'Saint-Paul', 4798, 'ARA', 75, 'FR', 45.39840000, 4.22617000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46403, 'Saint-Paul', 4795, 'NAQ', 75, 'FR', 45.75114000, 1.43238000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46404, 'Saint-Paul-Cap-de-Joux', 4799, 'OCC', 75, 'FR', 43.64791000, 1.97559000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46405, 'Saint-Paul-Trois-Châteaux', 4798, 'ARA', 75, 'FR', 44.34594000, 4.76356000, '2019-10-05 22:48:56', '2020-05-01 17:22:43', 1, 'Q900116'),
(46406, 'Saint-Paul-de-Fenouillet', 4799, 'OCC', 75, 'FR', 42.80938000, 2.50374000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46407, 'Saint-Paul-de-Jarrat', 4799, 'OCC', 75, 'FR', 42.91404000, 1.65437000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46408, 'Saint-Paul-de-Varax', 4798, 'ARA', 75, 'FR', 46.09692000, 5.12959000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46409, 'Saint-Paul-de-Varces', 4798, 'ARA', 75, 'FR', 45.07175000, 5.64247000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q900116'),
(46410, 'Saint-Paul-de-Vence', 4812, 'PAC', 75, 'FR', 43.70000000, 7.11667000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q208260'),
(46411, 'Saint-Paul-des-Landes', 4798, 'ARA', 75, 'FR', 44.94356000, 2.31439000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q208260'),
(46412, 'Saint-Paul-en-Chablais', 4798, 'ARA', 75, 'FR', 46.37958000, 6.62483000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q848683'),
(46413, 'Saint-Paul-en-Forêt', 4812, 'PAC', 75, 'FR', 43.56709000, 6.69206000, '2019-10-05 22:48:56', '2020-05-01 17:22:47', 1, 'Q848683'),
(46414, 'Saint-Paul-en-Jarez', 4798, 'ARA', 75, 'FR', 45.48512000, 4.57113000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q848683'),
(46415, 'Saint-Paul-en-Pareds', 4802, 'PDL', 75, 'FR', 46.82214000, -0.98977000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q848683'),
(46416, 'Saint-Paul-lès-Dax', 4795, 'NAQ', 75, 'FR', 43.72715000, -1.05162000, '2019-10-05 22:48:56', '2020-05-01 17:22:46', 1, 'Q848683'),
(46417, 'Saint-Paul-lès-Romans', 4798, 'ARA', 75, 'FR', 45.06725000, 5.13213000, '2019-10-05 22:48:56', '2020-05-01 17:22:43', 1, 'Q848683'),
(46418, 'Saint-Paulet-de-Caisson', 4799, 'OCC', 75, 'FR', 44.26055000, 4.59785000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q268273'),
(46419, 'Saint-Paulien', 4798, 'ARA', 75, 'FR', 45.13629000, 3.81290000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q268273'),
(46420, 'Saint-Paër', 4804, 'NOR', 75, 'FR', 49.51452000, 0.87935000, '2019-10-05 22:48:56', '2020-05-01 17:22:45', 1, 'Q268273'),
(46421, 'Saint-Perdon', 4795, 'NAQ', 75, 'FR', 43.86560000, -0.59069000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q268273'),
(46422, 'Saint-Perreux', 4807, 'BRE', 75, 'FR', 47.66952000, -2.10809000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q268273'),
(46423, 'Saint-Philbert-de-Bouaine', 4802, 'PDL', 75, 'FR', 46.98568000, -1.52022000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, 'Q268273'),
(46424, 'Saint-Philbert-de-Grand-Lieu', 4802, 'PDL', 75, 'FR', 47.03580000, -1.64120000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46425, 'Saint-Philbert-du-Peuple', 4802, 'PDL', 75, 'FR', 47.39314000, -0.04360000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46426, 'Saint-Philibert', 4807, 'BRE', 75, 'FR', 47.58821000, -2.99978000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46427, 'Saint-Piat', 4818, 'CVL', 75, 'FR', 48.54668000, 1.58363000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46428, 'Saint-Pierre', 4812, 'PAC', 75, 'FR', 43.29282000, 5.40682000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46429, 'Saint-Pierre-Montlimart', 4802, 'PDL', 75, 'FR', 47.26976000, -1.02738000, '2019-10-05 22:48:56', '2019-10-05 22:48:56', 1, '********'),
(46430, 'Saint-Pierre-Quiberon', 4807, 'BRE', 75, 'FR', 47.52061000, -3.13084000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q127952'),
(46431, 'Saint-Pierre-de-Bœuf', 4798, 'ARA', 75, 'FR', 45.36667000, 4.75000000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, 'Q127952'),
(46432, 'Saint-Pierre-de-Chandieu', 4798, 'ARA', 75, 'FR', 45.64625000, 5.01481000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q127952'),
(46433, 'Saint-Pierre-de-Plesguen', 4807, 'BRE', 75, 'FR', 48.44638000, -1.91278000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q658532'),
(46434, 'Saint-Pierre-de-Varengeville', 4804, 'NOR', 75, 'FR', 49.50240000, 0.93118000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46435, 'Saint-Pierre-des-Corps', 4818, 'CVL', 75, 'FR', 47.38623000, 0.74849000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46436, 'Saint-Pierre-des-Fleurs', 4804, 'NOR', 75, 'FR', 49.25000000, 0.96667000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46437, 'Saint-Pierre-des-Nids', 4802, 'PDL', 75, 'FR', 48.39826000, -0.09984000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46438, 'Saint-Pierre-des-Échaubrognes', 4795, 'NAQ', 75, 'FR', 46.98988000, -0.74441000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, '********'),
(46439, 'Saint-Pierre-du-Chemin', 4802, 'PDL', 75, 'FR', 46.69523000, -0.70095000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46440, 'Saint-Pierre-du-Mont', 4795, 'NAQ', 75, 'FR', 43.88453000, -0.52185000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46441, 'Saint-Pierre-du-Perray', 4796, 'IDF', 75, 'FR', 48.61064000, 2.49429000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q973571'),
(46442, 'Saint-Pierre-du-Regard', 4804, 'NOR', 75, 'FR', 48.84286000, -0.54631000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q973571'),
(46443, 'Saint-Pierre-du-Vauvray', 4804, 'NOR', 75, 'FR', 49.23208000, 1.22125000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q973571'),
(46444, 'Saint-Pierre-d’Aurillac', 4795, 'NAQ', 75, 'FR', 44.57168000, -0.18922000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, 'Q973571'),
(46445, 'Saint-Pierre-d’Oléron', 4795, 'NAQ', 75, 'FR', 45.94430000, -1.30630000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, 'Q973571'),
(46446, 'Saint-Pierre-en-Val', 4804, 'NOR', 75, 'FR', 50.02168000, 1.44707000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q973571'),
(46447, 'Saint-Pierre-la-Cour', 4802, 'PDL', 75, 'FR', 48.11258000, -1.02496000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q544967'),
(46448, 'Saint-Pierre-la-Palud', 4798, 'ARA', 75, 'FR', 45.79092000, 4.61177000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q544967'),
(46449, 'Saint-Pierre-le-Moûtier', 4825, 'BFC', 75, 'FR', 46.79277000, 3.11657000, '2019-10-05 22:48:57', '2020-05-01 17:22:44', 1, 'Q544967'),
(46450, 'Saint-Pierre-lès-Elbeuf', 4804, 'NOR', 75, 'FR', 49.27901000, 1.04305000, '2019-10-05 22:48:57', '2020-05-01 17:22:45', 1, 'Q544967'),
(46451, 'Saint-Pierre-lès-Nemours', 4796, 'IDF', 75, 'FR', 48.26733000, 2.67966000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, 'Q544967'),
(46452, 'Saint-Pierre-sur-Dives', 4804, 'NOR', 75, 'FR', 49.01667000, -0.03333000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q243649'),
(46453, 'Saint-Pierre-Église', 4804, 'NOR', 75, 'FR', 49.66848000, -1.40358000, '2019-10-05 22:48:57', '2020-05-01 17:22:45', 1, 'Q243649'),
(46454, 'Saint-Planchers', 4804, 'NOR', 75, 'FR', 48.82269000, -1.52598000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q243649'),
(46455, 'Saint-Pol-de-Léon', 4807, 'BRE', 75, 'FR', 48.68333000, -3.98333000, '2019-10-05 22:48:57', '2020-05-01 17:22:44', 1, 'Q243649'),
(46456, 'Saint-Pol-sur-Mer', 4828, 'HDF', 75, 'FR', 51.03116000, 2.33983000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q1112729'),
(46457, 'Saint-Pol-sur-Ternoise', 4828, 'HDF', 75, 'FR', 50.38113000, 2.33407000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q385527'),
(46458, 'Saint-Pons-de-Thomières', 4799, 'OCC', 75, 'FR', 43.48333000, 2.76667000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, 'Q385527'),
(46459, 'Saint-Porchaire', 4795, 'NAQ', 75, 'FR', 45.82075000, -0.78235000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q385527'),
(46460, 'Saint-Porquier', 4799, 'OCC', 75, 'FR', 44.00344000, 1.17932000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q385527'),
(46461, 'Saint-Pouange', 4820, 'GES', 75, 'FR', 48.22656000, 4.03979000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750510'),
(46462, 'Saint-Pourçain-sur-Sioule', 4798, 'ARA', 75, 'FR', 46.30748000, 3.28931000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, 'Q750510'),
(46463, 'Saint-Prest', 4818, 'CVL', 75, 'FR', 48.49107000, 1.53034000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750510'),
(46464, 'Saint-Priest', 4798, 'ARA', 75, 'FR', 45.69651000, 4.94385000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750510'),
(46465, 'Saint-Priest-Taurion', 4795, 'NAQ', 75, 'FR', 45.88686000, 1.40016000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750510'),
(46466, 'Saint-Priest-en-Jarez', 4798, 'ARA', 75, 'FR', 45.47390000, 4.37678000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750510'),
(46467, 'Saint-Priest-sous-Aixe', 4795, 'NAQ', 75, 'FR', 45.81667000, 1.10000000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750510'),
(46468, 'Saint-Privat', 4798, 'ARA', 75, 'FR', 44.62859000, 4.41489000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q155247'),
(46469, 'Saint-Privat', 4795, 'NAQ', 75, 'FR', 45.13831000, 2.09902000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q155247'),
(46470, 'Saint-Privat-des-Vieux', 4799, 'OCC', 75, 'FR', 44.14415000, 4.12988000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q155247'),
(46471, 'Saint-Privat-la-Montagne', 4820, 'GES', 75, 'FR', 49.18768000, 6.03874000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q21940'),
(46472, 'Saint-Prix', 4796, 'IDF', 75, 'FR', 49.01667000, 2.26667000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q21940'),
(46473, 'Saint-Projet-Saint-Constant', 4795, 'NAQ', 75, 'FR', 45.72802000, 0.33851000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q961542'),
(46474, 'Saint-Prouant', 4802, 'PDL', 75, 'FR', 46.75822000, -0.95703000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q631952'),
(46475, 'Saint-Pryvé-Saint-Mesmin', 4818, 'CVL', 75, 'FR', 47.88177000, 1.86950000, '2019-10-05 22:48:57', '2020-05-01 17:22:44', 1, 'Q631952'),
(46476, 'Saint-Python', 4828, 'HDF', 75, 'FR', 50.19001000, 3.48027000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q631952'),
(46477, 'Saint-Père', 4807, 'BRE', 75, 'FR', 48.58727000, -1.92413000, '2019-10-05 22:48:57', '2020-05-01 17:22:44', 1, 'Q631952'),
(46478, 'Saint-Père-en-Retz', 4802, 'PDL', 75, 'FR', 47.20559000, -2.04095000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, '********'),
(46479, 'Saint-Père-sur-Loire', 4818, 'CVL', 75, 'FR', 47.76667000, 2.36667000, '2019-10-05 22:48:57', '2020-05-01 17:22:44', 1, '********'),
(46480, 'Saint-Pé-de-Bigorre', 4799, 'OCC', 75, 'FR', 43.10369000, -0.15943000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, '********'),
(46481, 'Saint-Pée-sur-Nivelle', 4795, 'NAQ', 75, 'FR', 43.35564000, -1.55013000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, 'Q243738'),
(46482, 'Saint-Péray', 4798, 'ARA', 75, 'FR', 44.94866000, 4.84510000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, 'Q243738'),
(46483, 'Saint-Quay-Perros', 4807, 'BRE', 75, 'FR', 48.78333000, -3.45000000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q243738'),
(46484, 'Saint-Quay-Portrieux', 4807, 'BRE', 75, 'FR', 48.64992000, -2.83059000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q226408'),
(46485, 'Saint-Quentin', 4828, 'HDF', 75, 'FR', 49.84889000, 3.28757000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q226408'),
(46486, 'Saint-Quentin-Fallavier', 4798, 'ARA', 75, 'FR', 45.64335000, 5.11266000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q636250'),
(46487, 'Saint-Quentin-de-Baron', 4795, 'NAQ', 75, 'FR', 44.81802000, -0.28636000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q636250'),
(46488, 'Saint-Quentin-en-Mauges', 4802, 'PDL', 75, 'FR', 47.29130000, -0.91191000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46489, 'Saint-Quentin-en-Yvelines', 4796, 'IDF', 75, 'FR', 48.77186000, 2.01891000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46490, 'Saint-Quentin-la-Poterie', 4799, 'OCC', 75, 'FR', 44.04392000, 4.44432000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46491, 'Saint-Quentin-sur-Isère', 4798, 'ARA', 75, 'FR', 45.27843000, 5.54463000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, '********'),
(46492, 'Saint-Quentin-sur-le-Homme', 4804, 'NOR', 75, 'FR', 48.64751000, -1.31806000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46493, 'Saint-Rambert-en-Bugey', 4798, 'ARA', 75, 'FR', 45.94734000, 5.43630000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q589126'),
(46494, 'Saint-Raphaël', 4812, 'PAC', 75, 'FR', 43.42332000, 6.77350000, '2019-10-05 22:48:57', '2020-05-01 17:22:47', 1, 'Q589126'),
(46495, 'Saint-Renan', 4807, 'BRE', 75, 'FR', 48.43333000, -4.61667000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q274846'),
(46496, 'Saint-Restitut', 4798, 'ARA', 75, 'FR', 44.32446000, 4.79282000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q274846'),
(46497, 'Saint-Riquier', 4828, 'HDF', 75, 'FR', 50.13235000, 1.94755000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q734988'),
(46498, 'Saint-Rogatien', 4795, 'NAQ', 75, 'FR', 46.15000000, -1.06963000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q734988'),
(46499, 'Saint-Romain-de-Benet', 4795, 'NAQ', 75, 'FR', 45.69150000, -0.84765000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750532'),
(46500, 'Saint-Romain-de-Colbosc', 4804, 'NOR', 75, 'FR', 49.53093000, 0.35719000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q750532'),
(46501, 'Saint-Romain-de-Jalionas', 4798, 'ARA', 75, 'FR', 45.75677000, 5.21741000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q1150536'),
(46502, 'Saint-Romain-de-Popey', 4798, 'ARA', 75, 'FR', 45.84725000, 4.53118000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q1150536'),
(46503, 'Saint-Romain-en-Gal', 4798, 'ARA', 75, 'FR', 45.53255000, 4.86190000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q1389036'),
(46504, 'Saint-Romain-la-Motte', 4798, 'ARA', 75, 'FR', 46.08150000, 3.99076000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q1389036'),
(46505, 'Saint-Romain-le-Puy', 4798, 'ARA', 75, 'FR', 45.55837000, 4.12296000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, 'Q1389036'),
(46506, 'Saint-Romain-sur-Cher', 4818, 'CVL', 75, 'FR', 47.31877000, 1.39956000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46507, 'Saint-Romans', 4798, 'ARA', 75, 'FR', 45.11278000, 5.32239000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46508, 'Saint-Rémy', 4825, 'BFC', 75, 'FR', 46.76334000, 4.83928000, '2019-10-05 22:48:57', '2020-05-01 17:22:44', 1, '********'),
(46509, 'Saint-Rémy', 4804, 'NOR', 75, 'FR', 48.94007000, -0.50344000, '2019-10-05 22:48:57', '2020-05-01 17:22:45', 1, '********'),
(46510, 'Saint-Rémy-de-Maurienne', 4798, 'ARA', 75, 'FR', 45.40000000, 6.26667000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, '********'),
(46511, 'Saint-Rémy-de-Provence', 4812, 'PAC', 75, 'FR', 43.78848000, 4.83167000, '2019-10-05 22:48:57', '2020-05-01 17:22:47', 1, 'Q221507'),
(46512, 'Saint-Rémy-en-Mauges', 4802, 'PDL', 75, 'FR', 47.27202000, -1.07499000, '2019-10-05 22:48:57', '2020-05-01 17:22:46', 1, 'Q221507'),
(46513, 'Saint-Rémy-en-Rollat', 4798, 'ARA', 75, 'FR', 46.18330000, 3.39106000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, 'Q221507'),
(46514, 'Saint-Rémy-lès-Chevreuse', 4796, 'IDF', 75, 'FR', 48.70708000, 2.07692000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, 'Q794124'),
(46515, 'Saint-Rémy-sur-Avre', 4818, 'CVL', 75, 'FR', 48.76143000, 1.24532000, '2019-10-05 22:48:57', '2020-05-01 17:22:44', 1, 'Q794124'),
(46516, 'Saint-Rémy-sur-Durolle', 4798, 'ARA', 75, 'FR', 45.88815000, 3.59257000, '2019-10-05 22:48:57', '2020-05-01 17:22:43', 1, '********'),
(46517, 'Saint-Samson-sur-Rance', 4807, 'BRE', 75, 'FR', 48.49252000, -2.02865000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46518, 'Saint-Satur', 4818, 'CVL', 75, 'FR', 47.33914000, 2.83734000, '2019-10-05 22:48:57', '2019-10-05 22:48:57', 1, '********'),
(46519, 'Saint-Saturnin', 4795, 'NAQ', 75, 'FR', 45.66667000, 0.05000000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46520, 'Saint-Saturnin', 4802, 'PDL', 75, 'FR', 48.05795000, 0.15218000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46521, 'Saint-Saturnin', 4798, 'ARA', 75, 'FR', 45.65956000, 3.09232000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46522, 'Saint-Saturnin-lès-Apt', 4812, 'PAC', 75, 'FR', 43.93333000, 5.38333000, '2019-10-05 22:48:58', '2020-05-01 17:22:47', 1, '********'),
(46523, 'Saint-Saturnin-lès-Avignon', 4812, 'PAC', 75, 'FR', 43.95501000, 4.92548000, '2019-10-05 22:48:58', '2020-05-01 17:22:47', 1, '********'),
(46524, 'Saint-Saturnin-sur-Loire', 4802, 'PDL', 75, 'FR', 47.39438000, -0.43565000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46525, 'Saint-Saulve', 4828, 'HDF', 75, 'FR', 50.37141000, 3.55612000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46526, 'Saint-Sauvant', 4795, 'NAQ', 75, 'FR', 46.35965000, 0.05634000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46527, 'Saint-Sauves-d\'Auvergne', 4798, 'ARA', 75, 'FR', 45.60000000, 2.68333000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46528, 'Saint-Sauveur', 4828, 'HDF', 75, 'FR', 49.31857000, 2.78321000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46529, 'Saint-Sauveur', 4798, 'ARA', 75, 'FR', 45.15335000, 5.35280000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46530, 'Saint-Sauveur', 4825, 'BFC', 75, 'FR', 47.80530000, 6.38583000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46531, 'Saint-Sauveur', 4799, 'OCC', 75, 'FR', 43.74830000, 1.40085000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46532, 'Saint-Sauveur', 4795, 'NAQ', 75, 'FR', 45.20272000, -0.83433000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46533, 'Saint-Sauveur-Lendelin', 4804, 'NOR', 75, 'FR', 49.12988000, -1.41405000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46534, 'Saint-Sauveur-de-Montagut', 4798, 'ARA', 75, 'FR', 44.82142000, 4.57993000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46535, 'Saint-Sauveur-des-Landes', 4807, 'BRE', 75, 'FR', 48.34278000, -1.31248000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46536, 'Saint-Sauveur-d’Aunis', 4795, 'NAQ', 75, 'FR', 46.21716000, -0.88580000, '2019-10-05 22:48:58', '2020-05-01 17:22:46', 1, '********'),
(46537, 'Saint-Sauveur-en-Rue', 4798, 'ARA', 75, 'FR', 45.26981000, 4.49512000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q632779'),
(46538, 'Saint-Sauveur-le-Vicomte', 4804, 'NOR', 75, 'FR', 49.38547000, -1.53310000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q668199'),
(46539, 'Saint-Sauveur-sur-École', 4796, 'IDF', 75, 'FR', 48.49750000, 2.54711000, '2019-10-05 22:48:58', '2020-05-01 17:22:43', 1, 'Q668199'),
(46540, 'Saint-Savin', 4798, 'ARA', 75, 'FR', 45.62776000, 5.30856000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q668199'),
(46541, 'Saint-Savin', 4795, 'NAQ', 75, 'FR', 45.15000000, -0.45000000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q668199'),
(46542, 'Saint-Savinien', 4795, 'NAQ', 75, 'FR', 45.87711000, -0.67919000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q549150'),
(46543, 'Saint-Savournin', 4812, 'PAC', 75, 'FR', 43.40848000, 5.52690000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q675839'),
(46544, 'Saint-Saëns', 4804, 'NOR', 75, 'FR', 49.67302000, 1.28525000, '2019-10-05 22:48:58', '2020-05-01 17:22:45', 1, 'Q675839'),
(46545, 'Saint-Selve', 4795, 'NAQ', 75, 'FR', 44.67095000, -0.47887000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q675839'),
(46546, 'Saint-Senier-sous-Avranches', 4804, 'NOR', 75, 'FR', 48.68399000, -1.33126000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q675839'),
(46547, 'Saint-Senoux', 4807, 'BRE', 75, 'FR', 47.90552000, -1.78819000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q675839'),
(46548, 'Saint-Sernin', 4798, 'ARA', 75, 'FR', 44.57147000, 4.39186000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q675839'),
(46549, 'Saint-Sernin-du-Bois', 4825, 'BFC', 75, 'FR', 46.84074000, 4.43271000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q675839'),
(46550, 'Saint-Seurin-sur-l’Isle', 4795, 'NAQ', 75, 'FR', 45.01667000, 0.00000000, '2019-10-05 22:48:58', '2020-05-01 17:22:46', 1, 'Q675839'),
(46551, 'Saint-Sever', 4795, 'NAQ', 75, 'FR', 43.75727000, -0.57357000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q1019173'),
(46552, 'Saint-Sever-Calvados', 4804, 'NOR', 75, 'FR', 48.84035000, -1.04773000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q845115'),
(46553, 'Saint-Simon', 4798, 'ARA', 75, 'FR', 44.96320000, 2.48994000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q264298'),
(46554, 'Saint-Siméon-de-Bressieux', 4798, 'ARA', 75, 'FR', 45.33850000, 5.26591000, '2019-10-05 22:48:58', '2020-05-01 17:22:43', 1, 'Q264298'),
(46555, 'Saint-Sorlin-en-Bugey', 4798, 'ARA', 75, 'FR', 45.88576000, 5.36688000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q264298'),
(46556, 'Saint-Sorlin-en-Valloire', 4798, 'ARA', 75, 'FR', 45.28774000, 4.94903000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q264298'),
(46557, 'Saint-Souplet', 4828, 'HDF', 75, 'FR', 50.05639000, 3.53179000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q681510'),
(46558, 'Saint-Soupplets', 4796, 'IDF', 75, 'FR', 49.03866000, 2.80723000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q681510'),
(46559, 'Saint-Sulpice', 4828, 'HDF', 75, 'FR', 49.35049000, 2.12314000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q924614'),
(46560, 'Saint-Sulpice-de-Cognac', 4795, 'NAQ', 75, 'FR', 45.75978000, -0.38093000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q45992'),
(46561, 'Saint-Sulpice-de-Faleyrens', 4795, 'NAQ', 75, 'FR', 44.86667000, -0.18333000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q1005035'),
(46562, 'Saint-Sulpice-de-Pommeray', 4818, 'CVL', 75, 'FR', 47.60000000, 1.26667000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q1157245'),
(46563, 'Saint-Sulpice-de-Royan', 4795, 'NAQ', 75, 'FR', 45.67035000, -1.01252000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46564, 'Saint-Sulpice-et-Cameyrac', 4795, 'NAQ', 75, 'FR', 44.91131000, -0.39048000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46565, 'Saint-Sulpice-la-Forêt', 4807, 'BRE', 75, 'FR', 48.21667000, -1.57972000, '2019-10-05 22:48:58', '2020-05-01 17:22:44', 1, '********'),
(46566, 'Saint-Sulpice-la-Pointe', 4799, 'OCC', 75, 'FR', 43.77500000, 1.68511000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46567, 'Saint-Sulpice-le-Guérétois', 4795, 'NAQ', 75, 'FR', 46.20097000, 1.82826000, '2019-10-05 22:48:58', '2020-05-01 17:22:46', 1, '********'),
(46568, 'Saint-Sulpice-les-Feuilles', 4795, 'NAQ', 75, 'FR', 46.31868000, 1.36792000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46569, 'Saint-Sulpice-sur-Lèze', 4799, 'OCC', 75, 'FR', 43.33076000, 1.32091000, '2019-10-05 22:48:58', '2020-05-01 17:22:46', 1, '********'),
(46570, 'Saint-Sulpice-sur-Risle', 4804, 'NOR', 75, 'FR', 48.78063000, 0.65739000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46571, 'Saint-Sylvain', 4804, 'NOR', 75, 'FR', 49.05624000, -0.21758000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46572, 'Saint-Sylvestre-Cappel', 4828, 'HDF', 75, 'FR', 50.77625000, 2.55622000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46573, 'Saint-Sylvestre-sur-Lot', 4795, 'NAQ', 75, 'FR', 44.39667000, 0.80441000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46574, 'Saint-Symphorien', 4795, 'NAQ', 75, 'FR', 46.26442000, -0.49220000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46575, 'Saint-Symphorien-de-Lay', 4798, 'ARA', 75, 'FR', 45.94772000, 4.21318000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46576, 'Saint-Symphorien-sur-Coise', 4798, 'ARA', 75, 'FR', 45.63220000, 4.45709000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46577, 'Saint-Sébastien-de-Morsent', 4804, 'NOR', 75, 'FR', 49.01096000, 1.08730000, '2019-10-05 22:48:58', '2020-05-01 17:22:45', 1, '********'),
(46578, 'Saint-Sébastien-sur-Loire', 4802, 'PDL', 75, 'FR', 47.20768000, -1.50332000, '2019-10-05 22:48:58', '2020-05-01 17:22:46', 1, 'Q337636'),
(46579, 'Saint-Thibault-des-Vignes', 4796, 'IDF', 75, 'FR', 48.87111000, 2.68041000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46580, 'Saint-Thibéry', 4799, 'OCC', 75, 'FR', 43.39657000, 3.41774000, '2019-10-05 22:48:58', '2020-05-01 17:22:46', 1, '********'),
(46581, 'Saint-Thonan', 4807, 'BRE', 75, 'FR', 48.48333000, -4.33333000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q210529'),
(46582, 'Saint-Thurial', 4807, 'BRE', 75, 'FR', 48.02924000, -1.93181000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q210529'),
(46583, 'Saint-Thuriau', 4807, 'BRE', 75, 'FR', 48.01667000, -2.95000000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q210529'),
(46584, 'Saint-Thégonnec', 4807, 'BRE', 75, 'FR', 48.51667000, -3.95000000, '2019-10-05 22:48:58', '2020-05-01 17:22:44', 1, 'Q333614'),
(46585, 'Saint-Trivier-de-Courtes', 4798, 'ARA', 75, 'FR', 46.45980000, 5.07762000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q333614'),
(46586, 'Saint-Trivier-sur-Moignans', 4798, 'ARA', 75, 'FR', 46.07233000, 4.89721000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q333614'),
(46587, 'Saint-Trojan-les-Bains', 4795, 'NAQ', 75, 'FR', 45.84134000, -1.20728000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q669993'),
(46588, 'Saint-Tronc', 4812, 'PAC', 75, 'FR', 43.27093000, 5.42285000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q669993'),
(46589, 'Saint-Tropez', 4812, 'PAC', 75, 'FR', 43.26764000, 6.64049000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q1813'),
(46590, 'Saint-Urbain', 4802, 'PDL', 75, 'FR', 46.87557000, -2.00961000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q1813'),
(46591, 'Saint-Urbain', 4807, 'BRE', 75, 'FR', 48.40000000, -4.23333000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q1813'),
(46592, 'Saint-Usage', 4825, 'BFC', 75, 'FR', 47.11009000, 5.26044000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q193046'),
(46593, 'Saint-Usuge', 4825, 'BFC', 75, 'FR', 46.67900000, 5.25089000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q193046'),
(46594, 'Saint-Uze', 4798, 'ARA', 75, 'FR', 45.18409000, 4.86000000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q193046'),
(46595, 'Saint-Vaast-la-Hougue', 4804, 'NOR', 75, 'FR', 49.58843000, -1.26931000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q627585'),
(46596, 'Saint-Valery-en-Caux', 4804, 'NOR', 75, 'FR', 49.86667000, 0.73333000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q829711'),
(46597, 'Saint-Valery-sur-Somme', 4828, 'HDF', 75, 'FR', 50.18333000, 1.63333000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q473842'),
(46598, 'Saint-Vallier', 4825, 'BFC', 75, 'FR', 46.64107000, 4.37107000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46599, 'Saint-Vallier', 4798, 'ARA', 75, 'FR', 45.17154000, 4.81758000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46600, 'Saint-Vallier-de-Thiey', 4812, 'PAC', 75, 'FR', 43.69841000, 6.84779000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46601, 'Saint-Valérien', 4825, 'BFC', 75, 'FR', 48.17805000, 3.09523000, '2019-10-05 22:48:58', '2020-05-01 17:22:44', 1, '********'),
(46602, 'Saint-Varent', 4795, 'NAQ', 75, 'FR', 46.88934000, -0.23210000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46603, 'Saint-Vaury', 4795, 'NAQ', 75, 'FR', 46.20417000, 1.75654000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46604, 'Saint-Venant', 4828, 'HDF', 75, 'FR', 50.61955000, 2.53946000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46605, 'Saint-Viance', 4795, 'NAQ', 75, 'FR', 45.21760000, 1.45263000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46606, 'Saint-Viaud', 4802, 'PDL', 75, 'FR', 47.25619000, -2.01833000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46607, 'Saint-Victor', 4798, 'ARA', 75, 'FR', 46.39506000, 2.60819000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46608, 'Saint-Victor-de-Cessieu', 4798, 'ARA', 75, 'FR', 45.54113000, 5.39098000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46609, 'Saint-Victor-la-Coste', 4799, 'OCC', 75, 'FR', 44.06295000, 4.64238000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, '********'),
(46610, 'Saint-Victoret', 4812, 'PAC', 75, 'FR', 43.41957000, 5.23396000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46611, 'Saint-Victurnien', 4795, 'NAQ', 75, 'FR', 45.87855000, 1.01376000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46612, 'Saint-Vigor-le-Grand', 4804, 'NOR', 75, 'FR', 49.28242000, -0.68579000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46613, 'Saint-Vincent-de-Mercuze', 4798, 'ARA', 75, 'FR', 45.37215000, 5.95424000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46614, 'Saint-Vincent-de-Paul', 4795, 'NAQ', 75, 'FR', 43.74431000, -1.00662000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46615, 'Saint-Vincent-de-Tyrosse', 4795, 'NAQ', 75, 'FR', 43.66031000, -1.30799000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46616, 'Saint-Vincent-des-Landes', 4802, 'PDL', 75, 'FR', 47.65695000, -1.49572000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46617, 'Saint-Vincent-sur-Graon', 4802, 'PDL', 75, 'FR', 46.51690000, -1.38881000, '2019-10-05 22:48:58', '2019-10-05 22:48:58', 1, 'Q640703'),
(46618, 'Saint-Vincent-sur-Oust', 4807, 'BRE', 75, 'FR', 47.70010000, -2.14657000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q640703'),
(46619, 'Saint-Vit', 4825, 'BFC', 75, 'FR', 47.18333000, 5.81667000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q906399'),
(46620, 'Saint-Vite', 4795, 'NAQ', 75, 'FR', 44.47133000, 0.93876000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q906399'),
(46621, 'Saint-Vivien-de-Médoc', 4795, 'NAQ', 75, 'FR', 45.42695000, -1.03377000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q906399'),
(46622, 'Saint-Viâtre', 4818, 'CVL', 75, 'FR', 47.52438000, 1.93276000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, '********'),
(46623, 'Saint-Vrain', 4796, 'IDF', 75, 'FR', 48.54302000, 2.33331000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q752277'),
(46624, 'Saint-Vérand', 4798, 'ARA', 75, 'FR', 45.17344000, 5.33261000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q752277'),
(46625, 'Saint-Wandrille-Rançon', 4804, 'NOR', 75, 'FR', 49.52658000, 0.76497000, '2019-10-05 22:48:59', '2020-05-01 17:22:45', 1, 'Q752277'),
(46626, 'Saint-Witz', 4796, 'IDF', 75, 'FR', 49.09100000, 2.57122000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q1625104'),
(46627, 'Saint-Xandre', 4795, 'NAQ', 75, 'FR', 46.20444000, -1.10267000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q1625104'),
(46628, 'Saint-Yan', 4825, 'BFC', 75, 'FR', 46.41147000, 4.03876000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q1625104'),
(46629, 'Saint-Yorre', 4798, 'ARA', 75, 'FR', 46.06598000, 3.46430000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q41111'),
(46630, 'Saint-Yrieix-la-Perche', 4795, 'NAQ', 75, 'FR', 45.51604000, 1.20569000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q523015'),
(46631, 'Saint-Yrieix-sur-Charente', 4795, 'NAQ', 75, 'FR', 45.68333000, 0.11667000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q1624697'),
(46632, 'Saint-Yvi', 4807, 'BRE', 75, 'FR', 47.96667000, -3.93333000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q207823'),
(46633, 'Saint-Yzan-de-Soudiac', 4795, 'NAQ', 75, 'FR', 45.14118000, -0.41078000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q207823'),
(46634, 'Saint-Zacharie', 4812, 'PAC', 75, 'FR', 43.38521000, 5.70808000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q207823'),
(46635, 'Saint-Égrève', 4798, 'ARA', 75, 'FR', 45.23313000, 5.68154000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q207823'),
(46636, 'Saint-Éloi', 4825, 'BFC', 75, 'FR', 46.97342000, 3.22228000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, 'Q207823'),
(46637, 'Saint-Éloy-de-Gy', 4818, 'CVL', 75, 'FR', 47.15552000, 2.34267000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, 'Q207823'),
(46638, 'Saint-Éloy-les-Mines', 4798, 'ARA', 75, 'FR', 46.16051000, 2.83379000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q207823'),
(46639, 'Saint-Émilion', 4795, 'NAQ', 75, 'FR', 44.88333000, -0.15000000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q192548'),
(46640, 'Saint-Épain', 4818, 'CVL', 75, 'FR', 47.14482000, 0.53668000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, 'Q751795'),
(46641, 'Saint-Étienne', 4798, 'ARA', 75, 'FR', 45.43389000, 4.39000000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q42716'),
(46642, 'Saint-Étienne-au-Mont', 4828, 'HDF', 75, 'FR', 50.67794000, 1.63084000, '2019-10-05 22:48:59', '2020-05-01 17:22:45', 1, 'Q874139'),
(46643, 'Saint-Étienne-de-Baïgorry', 4795, 'NAQ', 75, 'FR', 43.17533000, -1.34670000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q279252'),
(46644, 'Saint-Étienne-de-Chigny', 4818, 'CVL', 75, 'FR', 47.38333000, 0.53333000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, 'Q279252'),
(46645, 'Saint-Étienne-de-Crossey', 4798, 'ARA', 75, 'FR', 45.38027000, 5.64365000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q279252'),
(46646, 'Saint-Étienne-de-Fontbellon', 4798, 'ARA', 75, 'FR', 44.60077000, 4.38635000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q279252'),
(46647, 'Saint-Étienne-de-Mer-Morte', 4802, 'PDL', 75, 'FR', 46.92848000, -1.74272000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q687553'),
(46648, 'Saint-Étienne-de-Montluc', 4802, 'PDL', 75, 'FR', 47.27690000, -1.78072000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, '********'),
(46649, 'Saint-Étienne-de-Saint-Geoirs', 4798, 'ARA', 75, 'FR', 45.33909000, 5.34650000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, '********'),
(46650, 'Saint-Étienne-de-Tinée', 4812, 'PAC', 75, 'FR', 44.25643000, 6.92499000, '2019-10-05 22:48:59', '2020-05-01 17:22:47', 1, 'Q993780'),
(46651, 'Saint-Étienne-des-Oullières', 4798, 'ARA', 75, 'FR', 46.06731000, 4.64945000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q993780'),
(46652, 'Saint-Étienne-du-Bois', 4802, 'PDL', 75, 'FR', 46.83041000, -1.59714000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q993780'),
(46653, 'Saint-Étienne-du-Bois', 4798, 'ARA', 75, 'FR', 46.28990000, 5.29471000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q993780'),
(46654, 'Saint-Étienne-du-Grès', 4812, 'PAC', 75, 'FR', 43.78045000, 4.72534000, '2019-10-05 22:48:59', '2020-05-01 17:22:47', 1, 'Q204383'),
(46655, 'Saint-Étienne-du-Rouvray', 4804, 'NOR', 75, 'FR', 49.37794000, 1.10467000, '2019-10-05 22:48:59', '2020-05-01 17:22:45', 1, '********'),
(46656, 'Saint-Étienne-en-Coglès', 4807, 'BRE', 75, 'FR', 48.40172000, -1.32812000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, '********'),
(46657, 'Saint-Étienne-lès-Remiremont', 4820, 'GES', 75, 'FR', 48.02287000, 6.60868000, '2019-10-05 22:48:59', '2020-05-01 17:22:45', 1, 'Q203434'),
(46658, 'Saint-Étienne-sur-Chalaronne', 4798, 'ARA', 75, 'FR', 46.14778000, 4.87129000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q203434'),
(46659, 'Saint-Évarzec', 4807, 'BRE', 75, 'FR', 47.93333000, -4.01667000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, 'Q203434'),
(46660, 'Sainte-Adresse', 4804, 'NOR', 75, 'FR', 49.50890000, 0.08446000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q604222'),
(46661, 'Sainte-Agnès', 4812, 'PAC', 75, 'FR', 43.80054000, 7.46150000, '2019-10-05 22:48:59', '2020-05-01 17:22:47', 1, 'Q604222'),
(46662, 'Sainte-Anastasie-sur-Issole', 4812, 'PAC', 75, 'FR', 43.34266000, 6.12493000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q604222'),
(46663, 'Sainte-Anne', 4812, 'PAC', 75, 'FR', 43.25720000, 5.39377000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q604222'),
(46664, 'Sainte-Anne-d\'Auray', 4807, 'BRE', 75, 'FR', 47.70000000, -2.95000000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q604222'),
(46665, 'Sainte-Anne-sur-Brivet', 4802, 'PDL', 75, 'FR', 47.46071000, -2.00415000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46666, 'Sainte-Bazeille', 4795, 'NAQ', 75, 'FR', 44.53073000, 0.09740000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46667, 'Sainte-Catherine', 4828, 'HDF', 75, 'FR', 50.30757000, 2.76404000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46668, 'Sainte-Colombe', 4796, 'IDF', 75, 'FR', 48.53047000, 3.25517000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46669, 'Sainte-Colombe', 4798, 'ARA', 75, 'FR', 45.52531000, 4.86664000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46670, 'Sainte-Colombe-en-Bruilhois', 4795, 'NAQ', 75, 'FR', 44.17822000, 0.51572000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46671, 'Sainte-Consorce', 4798, 'ARA', 75, 'FR', 45.77585000, 4.69033000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46672, 'Sainte-Croix-aux-Mines', 4820, 'GES', 75, 'FR', 48.26249000, 7.22739000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q83242'),
(46673, 'Sainte-Croix-en-Plaine', 4820, 'GES', 75, 'FR', 48.00876000, 7.38556000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q83246'),
(46674, 'Sainte-Cécile', 4802, 'PDL', 75, 'FR', 46.74286000, -1.11429000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q83246'),
(46675, 'Sainte-Cécile-les-Vignes', 4812, 'PAC', 75, 'FR', 44.24542000, 4.88613000, '2019-10-05 22:48:59', '2020-05-01 17:22:47', 1, '********'),
(46676, 'Sainte-Eulalie', 4795, 'NAQ', 75, 'FR', 44.90667000, -0.47417000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46677, 'Sainte-Euphémie', 4798, 'ARA', 75, 'FR', 45.97101000, 4.79853000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, '********'),
(46678, 'Sainte-Feyre', 4795, 'NAQ', 75, 'FR', 46.13900000, 1.91517000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46679, 'Sainte-Flaive-des-Loups', 4802, 'PDL', 75, 'FR', 46.61303000, -1.58082000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q818304'),
(46680, 'Sainte-Florine', 4798, 'ARA', 75, 'FR', 45.40519000, 3.31732000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q275824'),
(46681, 'Sainte-Fortunade', 4795, 'NAQ', 75, 'FR', 45.20691000, 1.77117000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q275824'),
(46682, 'Sainte-Foy', 4802, 'PDL', 75, 'FR', 46.54488000, -1.67265000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q275824'),
(46683, 'Sainte-Foy-de-Peyrolières', 4799, 'OCC', 75, 'FR', 43.49262000, 1.14477000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q275824'),
(46684, 'Sainte-Foy-la-Grande', 4795, 'NAQ', 75, 'FR', 44.83333000, 0.21667000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q320233'),
(46685, 'Sainte-Foy-lès-Lyon', 4798, 'ARA', 75, 'FR', 45.73375000, 4.80259000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, 'Q387460'),
(46686, 'Sainte-Foy-l’Argentière', 4798, 'ARA', 75, 'FR', 45.70823000, 4.47025000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, '********'),
(46687, 'Sainte-Féréole', 4795, 'NAQ', 75, 'FR', 45.22932000, 1.58248000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, '********'),
(46688, 'Sainte-Gauburge-Sainte-Colombe', 4804, 'NOR', 75, 'FR', 48.71722000, 0.43126000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46689, 'Sainte-Gemme-la-Plaine', 4802, 'PDL', 75, 'FR', 46.48286000, -1.11321000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46690, 'Sainte-Gemmes-sur-Loire', 4802, 'PDL', 75, 'FR', 47.42290000, -0.55684000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46691, 'Sainte-Geneviève', 4828, 'HDF', 75, 'FR', 49.28920000, 2.19904000, '2019-10-05 22:48:59', '2020-05-01 17:22:45', 1, '********'),
(46692, 'Sainte-Geneviève-des-Bois', 4818, 'CVL', 75, 'FR', 47.81777000, 2.81652000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, '********'),
(46693, 'Sainte-Geneviève-des-Bois', 4796, 'IDF', 75, 'FR', 48.64682000, 2.31965000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, '********'),
(46694, 'Sainte-Geneviève-sur-Argence', 4799, 'OCC', 75, 'FR', 44.80222000, 2.75954000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, '********'),
(46695, 'Sainte-Hermine', 4802, 'PDL', 75, 'FR', 46.55619000, -1.05476000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46696, 'Sainte-Honorine-du-Fay', 4804, 'NOR', 75, 'FR', 49.07775000, -0.49295000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46697, 'Sainte-Hélène', 4795, 'NAQ', 75, 'FR', 44.96667000, -0.88333000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, '********'),
(46698, 'Sainte-Hélène', 4807, 'BRE', 75, 'FR', 47.71976000, -3.20359000, '2019-10-05 22:48:59', '2020-05-01 17:22:44', 1, '********'),
(46699, 'Sainte-Hélène-sur-Isère', 4798, 'ARA', 75, 'FR', 45.61434000, 6.32052000, '2019-10-05 22:48:59', '2020-05-01 17:22:43', 1, '********'),
(46700, 'Sainte-Jamme-sur-Sarthe', 4802, 'PDL', 75, 'FR', 48.14264000, 0.16743000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q667916'),
(46701, 'Sainte-Livrade-sur-Lot', 4795, 'NAQ', 75, 'FR', 44.39929000, 0.59120000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q217288'),
(46702, 'Sainte-Lizaigne', 4818, 'CVL', 75, 'FR', 47.00695000, 2.02266000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, '********'),
(46703, 'Sainte-Luce-sur-Loire', 4802, 'PDL', 75, 'FR', 47.25381000, -1.48430000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q657190'),
(46704, 'Sainte-Marguerite', 4820, 'GES', 75, 'FR', 48.26735000, 6.98439000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q657190'),
(46705, 'Sainte-Marguerite', 4812, 'PAC', 75, 'FR', 43.26196000, 5.40125000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q657190'),
(46706, 'Sainte-Marguerite-sur-Duclair', 4804, 'NOR', 75, 'FR', 49.50852000, 0.84362000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q657190'),
(46707, 'Sainte-Marie', 4807, 'BRE', 75, 'FR', 47.69429000, -2.00190000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q657190'),
(46708, 'Sainte-Marie-Kerque', 4828, 'HDF', 75, 'FR', 50.89917000, 2.13664000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q657190'),
(46709, 'Sainte-Marie-Plage', 4799, 'OCC', 75, 'FR', 42.72498000, 3.03751000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q657190'),
(46710, 'Sainte-Marie-aux-Chênes', 4820, 'GES', 75, 'FR', 49.19262000, 6.00150000, '2019-10-05 22:48:59', '2020-05-01 17:22:45', 1, 'Q657190'),
(46711, 'Sainte-Marie-aux-Mines', 4820, 'GES', 75, 'FR', 48.24649000, 7.18385000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q500400'),
(46712, 'Sainte-Marie-de-Gosse', 4795, 'NAQ', 75, 'FR', 43.55000000, -0.23333000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q651802'),
(46713, 'Sainte-Marie-de-Ré', 4795, 'NAQ', 75, 'FR', 46.15237000, -1.31281000, '2019-10-05 22:48:59', '2020-05-01 17:22:46', 1, 'Q910030'),
(46714, 'Sainte-Marie-des-Champs', 4804, 'NOR', 75, 'FR', 49.62120000, 0.77904000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q910030'),
(46715, 'Sainte-Marthe', 4812, 'PAC', 75, 'FR', 43.33449000, 5.39112000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q910030'),
(46716, 'Sainte-Maure', 4820, 'GES', 75, 'FR', 48.34729000, 4.05962000, '2019-10-05 22:48:59', '2019-10-05 22:48:59', 1, 'Q910030'),
(46717, 'Sainte-Maure-de-Touraine', 4818, 'CVL', 75, 'FR', 47.11130000, 0.62236000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q910030'),
(46718, 'Sainte-Maxime', 4812, 'PAC', 75, 'FR', 43.30907000, 6.63849000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q693017'),
(46719, 'Sainte-Menehould', 4820, 'GES', 75, 'FR', 49.09008000, 4.89733000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q213307'),
(46720, 'Sainte-Mère-Église', 4804, 'NOR', 75, 'FR', 49.41000000, -1.31726000, '2019-10-05 22:49:00', '2020-05-01 17:22:45', 1, 'Q273393'),
(46721, 'Sainte-Pazanne', 4802, 'PDL', 75, 'FR', 47.10301000, -1.80950000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46722, 'Sainte-Radegonde', 4799, 'OCC', 75, 'FR', 44.33743000, 2.62672000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46723, 'Sainte-Radegonde', 4795, 'NAQ', 75, 'FR', 46.98333000, -0.25000000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46724, 'Sainte-Reine-de-Bretagne', 4802, 'PDL', 75, 'FR', 47.44131000, -2.19238000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46725, 'Sainte-Savine', 4820, 'GES', 75, 'FR', 48.29637000, 4.04642000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46726, 'Sainte-Sigolène', 4798, 'ARA', 75, 'FR', 45.24329000, 4.23343000, '2019-10-05 22:49:00', '2020-05-01 17:22:43', 1, 'Q264579'),
(46727, 'Sainte-Solange', 4818, 'CVL', 75, 'FR', 47.13628000, 2.55019000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q264579'),
(46728, 'Sainte-Soulle', 4795, 'NAQ', 75, 'FR', 46.18847000, -1.01607000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q264579'),
(46729, 'Sainte-Suzanne', 4802, 'PDL', 75, 'FR', 48.09818000, -0.35439000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q264579'),
(46730, 'Sainte-Suzanne', 4825, 'BFC', 75, 'FR', 47.50017000, 6.76775000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q264579'),
(46731, 'Sainte-Terre', 4795, 'NAQ', 75, 'FR', 44.83333000, -0.11667000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q264579'),
(46732, 'Sainte-Tulle', 4812, 'PAC', 75, 'FR', 43.78619000, 5.76513000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46733, 'Sainte-Verge', 4795, 'NAQ', 75, 'FR', 47.00818000, -0.21033000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46734, 'Saintes', 4795, 'NAQ', 75, 'FR', 45.74544000, -0.63450000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q191126'),
(46735, 'Saintes-Maries-de-la-Mer', 4812, 'PAC', 75, 'FR', 43.45214000, 4.42913000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q330920'),
(46736, 'Saintry-sur-Seine', 4796, 'IDF', 75, 'FR', 48.59640000, 2.49515000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q1165862'),
(46737, 'Saints', 4796, 'IDF', 75, 'FR', 48.76066000, 3.04645000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q271332'),
(46738, 'Saivres', 4795, 'NAQ', 75, 'FR', 46.43262000, -0.23677000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q271332'),
(46739, 'Saizerais', 4820, 'GES', 75, 'FR', 48.79250000, 6.04470000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q592439'),
(46740, 'Salagnon', 4798, 'ARA', 75, 'FR', 45.66863000, 5.36325000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q592439'),
(46741, 'Salaise-sur-Sanne', 4798, 'ARA', 75, 'FR', 45.35126000, 4.81070000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q1067733'),
(46742, 'Salbert', 4825, 'BFC', 75, 'FR', 47.66959000, 6.81269000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q1067733'),
(46743, 'Salbris', 4818, 'CVL', 75, 'FR', 47.42420000, 2.05124000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q579654'),
(46744, 'Saleilles', 4799, 'OCC', 75, 'FR', 42.65418000, 2.95309000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q579654'),
(46745, 'Salernes', 4812, 'PAC', 75, 'FR', 43.56350000, 6.23386000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q840242'),
(46746, 'Sales', 4798, 'ARA', 75, 'FR', 45.87441000, 5.95996000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q840242'),
(46747, 'Saleux', 4828, 'HDF', 75, 'FR', 49.85630000, 2.23698000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q840242'),
(46748, 'Salies-de-Béarn', 4795, 'NAQ', 75, 'FR', 43.47422000, -0.92448000, '2019-10-05 22:49:00', '2020-05-01 17:22:46', 1, 'Q324160'),
(46749, 'Salies-du-Salat', 4799, 'OCC', 75, 'FR', 43.10074000, 0.95866000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q324160'),
(46750, 'Salignac', 4795, 'NAQ', 75, 'FR', 45.01607000, -0.37964000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q324160'),
(46751, 'Salignac-Eyvigues', 4795, 'NAQ', 75, 'FR', 44.97464000, 1.32428000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q474604'),
(46752, 'Saligny', 4802, 'PDL', 75, 'FR', 46.80833000, -1.42726000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q474604'),
(46753, 'Salindres', 4799, 'OCC', 75, 'FR', 44.17174000, 4.16020000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q474604'),
(46754, 'Salins', 4796, 'IDF', 75, 'FR', 48.42156000, 3.02130000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q474604'),
(46755, 'Salins-les-Bains', 4825, 'BFC', 75, 'FR', 46.94663000, 5.87763000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q820042'),
(46756, 'Salins-les-Thermes', 4798, 'ARA', 75, 'FR', 45.47169000, 6.53051000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q820042'),
(46757, 'Sallanches', 4798, 'ARA', 75, 'FR', 45.94423000, 6.63162000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q845855'),
(46758, 'Sallaumines', 4828, 'HDF', 75, 'FR', 50.41749000, 2.86174000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q453707'),
(46759, 'Sallebœuf', 4795, 'NAQ', 75, 'FR', 44.83333000, -0.40000000, '2019-10-05 22:49:00', '2020-05-01 17:22:46', 1, 'Q453707'),
(46760, 'Sallertaine', 4802, 'PDL', 75, 'FR', 46.86017000, -1.95522000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q509991'),
(46761, 'Salles', 4795, 'NAQ', 75, 'FR', 44.55000000, -0.86073000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q509991'),
(46762, 'Salles-Curan', 4799, 'OCC', 75, 'FR', 44.18220000, 2.78821000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, '********'),
(46763, 'Salles-la-Source', 4799, 'OCC', 75, 'FR', 44.43505000, 2.51283000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q585724'),
(46764, 'Salles-sur-Mer', 4795, 'NAQ', 75, 'FR', 46.10543000, -1.05741000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q585724'),
(46765, 'Salomé', 4828, 'HDF', 75, 'FR', 50.53352000, 2.84709000, '2019-10-05 22:49:00', '2020-05-01 17:22:45', 1, 'Q585724'),
(46766, 'Salon-de-Provence', 4812, 'PAC', 75, 'FR', 43.64229000, 5.09478000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q585724'),
(46767, 'Salouël', 4828, 'HDF', 75, 'FR', 49.86988000, 2.24340000, '2019-10-05 22:49:00', '2020-05-01 17:22:45', 1, 'Q585724'),
(46768, 'Salses-le-Château', 4799, 'OCC', 75, 'FR', 42.83333000, 2.91667000, '2019-10-05 22:49:00', '2020-05-01 17:22:46', 1, 'Q821246'),
(46769, 'Salviac', 4799, 'OCC', 75, 'FR', 44.68080000, 1.26506000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q821246'),
(46770, 'Samadet', 4795, 'NAQ', 75, 'FR', 43.63761000, -0.48785000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q821246'),
(46771, 'Samatan', 4799, 'OCC', 75, 'FR', 43.49286000, 0.92976000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q821246'),
(46772, 'Samer', 4828, 'HDF', 75, 'FR', 50.63840000, 1.74628000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q74641'),
(46773, 'Sammeron', 4796, 'IDF', 75, 'FR', 48.94721000, 3.08333000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q74641'),
(46774, 'Samois-sur-Seine', 4796, 'IDF', 75, 'FR', 48.45251000, 2.75040000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q250106'),
(46775, 'Samoreau', 4796, 'IDF', 75, 'FR', 48.42946000, 2.75587000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q64616'),
(46776, 'Samoëns', 4798, 'ARA', 75, 'FR', 46.08282000, 6.72647000, '2019-10-05 22:49:00', '2020-05-01 17:22:43', 1, 'Q741646'),
(46777, 'Saméon', 4828, 'HDF', 75, 'FR', 50.47135000, 3.33544000, '2019-10-05 22:49:00', '2020-05-01 17:22:45', 1, 'Q741646'),
(46778, 'San-Martino-di-Lota', 4806, '20R', 75, 'FR', 42.73163000, 9.43966000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q972882'),
(46779, 'San-Nicolao', 4806, '20R', 75, 'FR', 42.36993000, 9.51443000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q972882'),
(46780, 'Sanary-sur-Mer', 4812, 'PAC', 75, 'FR', 43.11985000, 5.80155000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q659484'),
(46781, 'Sancerre', 4818, 'CVL', 75, 'FR', 47.32889000, 2.83447000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q459683'),
(46782, 'Sancey-le-Grand', 4825, 'BFC', 75, 'FR', 47.29489000, 6.58287000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q459683'),
(46783, 'Sancoins', 4818, 'CVL', 75, 'FR', 46.83314000, 2.92238000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q753096'),
(46784, 'Sancé', 4825, 'BFC', 75, 'FR', 46.33926000, 4.83049000, '2019-10-05 22:49:00', '2020-05-01 17:22:44', 1, 'Q753096'),
(46785, 'Sand', 4820, 'GES', 75, 'FR', 48.38319000, 7.61233000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q753096'),
(46786, 'Sandillon', 4818, 'CVL', 75, 'FR', 47.84510000, 2.03155000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q1108198'),
(46787, 'Sangatte', 4828, 'HDF', 75, 'FR', 50.94564000, 1.75321000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q457820'),
(46788, 'Sanguinet', 4795, 'NAQ', 75, 'FR', 44.48320000, -1.07457000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q457820'),
(46789, 'Sannerville', 4804, 'NOR', 75, 'FR', 49.18018000, -0.22434000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q457820'),
(46790, 'Sannois', 4796, 'IDF', 75, 'FR', 48.96667000, 2.25000000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q259216'),
(46791, 'Sansac-de-Marmiesse', 4798, 'ARA', 75, 'FR', 44.88376000, 2.34768000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q259216'),
(46792, 'Sant Andreu de Sureda', 4799, 'OCC', 75, 'FR', 42.55201000, 2.97129000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q259216'),
(46793, 'Sant Joan de Pladecorts', 4799, 'OCC', 75, 'FR', 42.51069000, 2.79091000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q8657'),
(46794, 'Sant Llorenç de Cerdans', 4799, 'OCC', 75, 'FR', 42.38473000, 2.61320000, '2019-10-05 22:49:00', '2020-05-01 17:22:46', 1, 'Q8657'),
(46795, 'Santa-Lucia-di-Moriani', 4806, '20R', 75, 'FR', 42.38212000, 9.52242000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q8657'),
(46796, 'Santa-Maria-di-Lota', 4806, '20R', 75, 'FR', 42.74783000, 9.43202000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q1240561'),
(46797, 'Santec', 4807, 'BRE', 75, 'FR', 48.70000000, -4.03333000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q622502'),
(46798, 'Santeny', 4796, 'IDF', 75, 'FR', 48.72730000, 2.57346000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q622502'),
(46799, 'Santes', 4828, 'HDF', 75, 'FR', 50.59316000, 2.96289000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q665107'),
(46800, 'Sanvignes-les-Mines', 4825, 'BFC', 75, 'FR', 46.66444000, 4.29188000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q665107'),
(46801, 'Saran', 4818, 'CVL', 75, 'FR', 47.95013000, 1.87601000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q665107'),
(46802, 'Sarbazan', 4795, 'NAQ', 75, 'FR', 44.02029000, -0.31294000, '2019-10-05 22:49:00', '2019-10-05 22:49:00', 1, 'Q665107');

