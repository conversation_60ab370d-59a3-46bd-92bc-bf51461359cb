INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(44802, 'Oucques', 4818, 'CV<PERSON>', 75, 'FR', 47.82252000, 1.29383000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q22434'),
(44803, 'Oudon', 4802, 'PDL', 75, 'FR', 47.34774000, -1.28500000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q22434'),
(44804, 'Ouges', 4825, 'BFC', 75, 'FR', 47.26164000, 5.07395000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q22434'),
(44805, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 4804, 'NOR', 75, 'FR', 49.27566000, -0.25910000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q274825'),
(44806, 'Oulins', 4818, 'CVL', 75, 'FR', 48.86451000, 1.47038000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q274825'),
(44807, 'Oullins', 4798, 'ARA', 75, 'FR', 45.71404000, 4.80755000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q8358'),
(44808, 'Ouroux-sur-Saône', 4825, 'BFC', 75, 'FR', 46.72147000, 4.95262000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, '********'),
(44809, 'Oursbelille', 4799, 'OCC', 75, 'FR', 43.28508000, 0.03473000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44810, 'Ourville-en-Caux', 4804, 'NOR', 75, 'FR', 49.72854000, 0.60460000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44811, 'Ousse', 4795, 'NAQ', 75, 'FR', 43.28333000, -0.26667000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44812, 'Outarville', 4818, 'CVL', 75, 'FR', 48.21393000, 2.02198000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1378013'),
(44813, 'Outreau', 4828, 'HDF', 75, 'FR', 50.70535000, 1.58970000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1378013'),
(44814, 'Ouveillan', 4799, 'OCC', 75, 'FR', 43.28915000, 2.97124000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1378013'),
(44815, 'Ouzouer-le-Marché', 4818, 'CVL', 75, 'FR', 47.91055000, 1.52607000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, 'Q1170125'),
(44816, 'Ouzouer-sur-Loire', 4818, 'CVL', 75, 'FR', 47.76638000, 2.48038000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1377364'),
(44817, 'Ouzouer-sur-Trézée', 4818, 'CVL', 75, 'FR', 47.67266000, 2.80800000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, 'Q1470534'),
(44818, 'Oye-Plage', 4828, 'HDF', 75, 'FR', 50.97713000, 2.04276000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q1470534'),
(44819, 'Oyonnax', 4798, 'ARA', 75, 'FR', 46.25917000, 5.65727000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q191856'),
(44820, 'Oytier-Saint-Oblas', 4798, 'ARA', 75, 'FR', 45.55757000, 5.03083000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q191856'),
(44821, 'Ozoir-la-Ferrière', 4796, 'IDF', 75, 'FR', 48.76699000, 2.66871000, '2019-10-05 22:48:37', '2020-05-01 17:22:43', 1, 'Q274588'),
(44822, 'Ozouer-le-Voulgis', 4796, 'IDF', 75, 'FR', 48.66012000, 2.77409000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q274588'),
(44823, 'Pabu', 4807, 'BRE', 75, 'FR', 48.58608000, -3.13608000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q541459'),
(44824, 'Pacy-sur-Eure', 4804, 'NOR', 75, 'FR', 49.01667000, 1.38333000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '*********'),
(44825, 'Pacé', 4807, 'BRE', 75, 'FR', 48.14848000, -1.77327000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, '*********'),
(44826, 'Pagny-sur-Moselle', 4820, 'GES', 75, 'FR', 48.98365000, 6.02069000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q597800'),
(44827, 'Paillet', 4795, 'NAQ', 75, 'FR', 44.68512000, -0.36500000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q597800'),
(44828, 'Paimboeuf', 4802, 'PDL', 75, 'FR', 47.28654000, -2.03048000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44829, 'Paimpol', 4807, 'BRE', 75, 'FR', 48.77841000, -3.04375000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q322453'),
(44830, 'Paimpont', 4807, 'BRE', 75, 'FR', 48.01809000, -2.17105000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q152741'),
(44831, 'Palais de Justice', 4812, 'PAC', 75, 'FR', 43.28657000, 5.37603000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q152741'),
(44832, 'Palaiseau', 4796, 'IDF', 75, 'FR', 48.71828000, 2.24980000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q205580'),
(44833, 'Palaja', 4799, 'OCC', 75, 'FR', 43.17363000, 2.38462000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q205580'),
(44834, 'Palama', 4812, 'PAC', 75, 'FR', 43.36566000, 5.44427000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q205580'),
(44835, 'Palau-del-Vidre', 4799, 'OCC', 75, 'FR', 42.57162000, 2.96033000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q205580'),
(44836, 'Palavas-les-Flots', 4799, 'OCC', 75, 'FR', 43.52805000, 3.92705000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q819267'),
(44837, 'Palinges', 4825, 'BFC', 75, 'FR', 46.55357000, 4.21964000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q819267'),
(44838, 'Pamiers', 4799, 'OCC', 75, 'FR', 43.11650000, 1.61079000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q209834'),
(44839, 'Pamproux', 4795, 'NAQ', 75, 'FR', 46.39578000, -0.05327000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q209834'),
(44840, 'Panazol', 4795, 'NAQ', 75, 'FR', 45.83465000, 1.32759000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44841, 'Pancé', 4807, 'BRE', 75, 'FR', 47.88181000, -1.65953000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, 'Q735675'),
(44842, 'Panissières', 4798, 'ARA', 75, 'FR', 45.79185000, 4.34163000, '2019-10-05 22:48:37', '2020-05-01 17:22:43', 1, 'Q735675'),
(44843, 'Pannecé', 4802, 'PDL', 75, 'FR', 47.48583000, -1.23940000, '2019-10-05 22:48:37', '2020-05-01 17:22:46', 1, 'Q735675'),
(44844, 'Pannes', 4818, 'CVL', 75, 'FR', 48.01937000, 2.66755000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q735675'),
(44845, 'Pantin', 4796, 'IDF', 75, 'FR', 48.89437000, 2.40935000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q209086'),
(44846, 'Paradou', 4812, 'PAC', 75, 'FR', 43.71699000, 4.78604000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q501966'),
(44847, 'Paray-Vieille-Poste', 4796, 'IDF', 75, 'FR', 48.71402000, 2.36283000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q752150'),
(44848, 'Paray-le-Monial', 4825, 'BFC', 75, 'FR', 46.45457000, 4.11584000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q654255'),
(44849, 'Parcé-sur-Sarthe', 4802, 'PDL', 75, 'FR', 47.84356000, -0.20104000, '2019-10-05 22:48:37', '2020-05-01 17:22:46', 1, 'Q654255'),
(44850, 'Parempuyre', 4795, 'NAQ', 75, 'FR', 44.95038000, -0.60453000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q654255'),
(44851, 'Parentis-en-Born', 4795, 'NAQ', 75, 'FR', 44.35274000, -1.07095000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44852, 'Pargny-sur-Saulx', 4820, 'GES', 75, 'FR', 48.76866000, 4.83758000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q326118'),
(44853, 'Parigny', 4804, 'NOR', 75, 'FR', 48.59450000, -1.07925000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q326118'),
(44854, 'Parigné', 4807, 'BRE', 75, 'FR', 48.42727000, -1.19210000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, 'Q326118'),
(44855, 'Parigné-le-Pôlin', 4802, 'PDL', 75, 'FR', 47.84987000, 0.10705000, '2019-10-05 22:48:37', '2020-05-01 17:22:46', 1, 'Q326118'),
(44856, 'Paris', 4796, 'IDF', 75, 'FR', 48.85340000, 2.34860000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q90'),
(44857, 'Parmain', 4796, 'IDF', 75, 'FR', 49.11247000, 2.21487000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44858, 'Paron', 4825, 'BFC', 75, 'FR', 48.17888000, 3.25075000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44859, 'Parthenay', 4795, 'NAQ', 75, 'FR', 46.64872000, -0.24682000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q213315'),
(44860, 'Parçay-Meslay', 4818, 'CVL', 75, 'FR', 47.44195000, 0.74847000, '2019-10-05 22:48:37', '2020-05-01 17:22:44', 1, 'Q213315'),
(44861, 'Parçay-les-Pins', 4802, 'PDL', 75, 'FR', 47.43668000, 0.16312000, '2019-10-05 22:48:37', '2020-05-01 17:22:46', 1, '********'),
(44862, 'Pas-de-Calais', 4828, 'HDF', 75, 'FR', 50.48280000, 2.28664000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q12689'),
(44863, 'Paslières', 4798, 'ARA', 75, 'FR', 45.92889000, 3.49736000, '2019-10-05 22:48:37', '2020-05-01 17:22:43', 1, 'Q12689'),
(44864, 'Pasly', 4828, 'HDF', 75, 'FR', 49.40167000, 3.29631000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, '********'),
(44865, 'Passy', 4798, 'ARA', 75, 'FR', 45.92341000, 6.69562000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q388949'),
(44866, 'Patay', 4818, 'CVL', 75, 'FR', 48.04833000, 1.69500000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q388949'),
(44867, 'Pau', 4795, 'NAQ', 75, 'FR', 43.30000000, -0.36667000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q132671'),
(44868, 'Pauillac', 4795, 'NAQ', 75, 'FR', 45.19644000, -0.74873000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q272528'),
(44869, 'Paulhac', 4799, 'OCC', 75, 'FR', 43.75639000, 1.55667000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q272528'),
(44870, 'Paulhaguet', 4798, 'ARA', 75, 'FR', 45.20795000, 3.51331000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q272528'),
(44871, 'Paulhan', 4799, 'OCC', 75, 'FR', 43.53944000, 3.45760000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q272528'),
(44872, 'Paulx', 4802, 'PDL', 75, 'FR', 46.96181000, -1.75520000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q272528'),
(44873, 'Pavie', 4799, 'OCC', 75, 'FR', 43.60969000, 0.59143000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q380501'),
(44874, 'Pavilly', 4804, 'NOR', 75, 'FR', 49.56703000, 0.95445000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q380501'),
(44875, 'Payns', 4820, 'GES', 75, 'FR', 48.38255000, 3.97755000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q380501'),
(44876, 'Payrin-Augmontel', 4799, 'OCC', 75, 'FR', 43.54450000, 2.35406000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q380501'),
(44877, 'Payzac', 4795, 'NAQ', 75, 'FR', 45.40000000, 1.21667000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q380501'),
(44878, 'Peaugres', 4798, 'ARA', 75, 'FR', 45.28651000, 4.72845000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q380501'),
(44879, 'Pechbonnieu', 4799, 'OCC', 75, 'FR', 43.70242000, 1.46538000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q380501'),
(44880, 'Pecquencourt', 4828, 'HDF', 75, 'FR', 50.37850000, 3.21277000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q532525'),
(44881, 'Peillac', 4807, 'BRE', 75, 'FR', 47.71391000, -2.21971000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q532525'),
(44882, 'Peille', 4812, 'PAC', 75, 'FR', 43.80296000, 7.40191000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q866754'),
(44883, 'Peillon', 4812, 'PAC', 75, 'FR', 43.77861000, 7.38278000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q327669'),
(44884, 'Peillonnex', 4798, 'ARA', 75, 'FR', 46.12861000, 6.37715000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q327669'),
(44885, 'Peipin', 4812, 'PAC', 75, 'FR', 44.13778000, 5.95722000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q383608'),
(44886, 'Pellegrue', 4795, 'NAQ', 75, 'FR', 44.74355000, 0.07595000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q379897'),
(44887, 'Pellouailles-les-Vignes', 4802, 'PDL', 75, 'FR', 47.52172000, -0.43954000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q379897'),
(44888, 'Pelousey', 4825, 'BFC', 75, 'FR', 47.27538000, 5.92263000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q379897'),
(44889, 'Peltre', 4820, 'GES', 75, 'FR', 49.07480000, 6.22721000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q22312'),
(44890, 'Pencran', 4807, 'BRE', 75, 'FR', 48.43333000, -4.23333000, '2019-10-05 22:48:37', '2019-10-05 22:48:37', 1, 'Q22312'),
(44891, 'Pendé', 4828, 'HDF', 75, 'FR', 50.16055000, 1.58541000, '2019-10-05 22:48:38', '2020-05-01 17:22:45', 1, 'Q22312'),
(44892, 'Pennautier', 4799, 'OCC', 75, 'FR', 43.24513000, 2.31892000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22312'),
(44893, 'Penta-di-Casinca', 4806, '20R', 75, 'FR', 42.46579000, 9.45884000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q265287'),
(44894, 'Penvénan', 4807, 'BRE', 75, 'FR', 48.81182000, -3.29550000, '2019-10-05 22:48:38', '2020-05-01 17:22:44', 1, 'Q224023'),
(44895, 'Percy', 4804, 'NOR', 75, 'FR', 48.91714000, -1.18916000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q224023'),
(44896, 'Peri', 4806, '20R', 75, 'FR', 42.00345000, 8.92066000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q224023'),
(44897, 'Pernes', 4828, 'HDF', 75, 'FR', 50.48437000, 2.41253000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q224023'),
(44898, 'Pernes-les-Fontaines', 4812, 'PAC', 75, 'FR', 43.99802000, 5.05906000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q472574'),
(44899, 'Perpignan', 4799, 'OCC', 75, 'FR', 42.69764000, 2.89541000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q6730'),
(44900, 'Perrecy-les-Forges', 4825, 'BFC', 75, 'FR', 46.61453000, 4.21380000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q6730'),
(44901, 'Perreux', 4798, 'ARA', 75, 'FR', 46.04004000, 4.12663000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q6730'),
(44902, 'Perriers-sur-Andelle', 4804, 'NOR', 75, 'FR', 49.41487000, 1.37098000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q1073588'),
(44903, 'Perrignier', 4798, 'ARA', 75, 'FR', 46.30558000, 6.43928000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q1073588'),
(44904, 'Perrigny', 4825, 'BFC', 75, 'FR', 46.66744000, 5.58456000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q1073588'),
(44905, 'Perrigny-lès-Dijon', 4825, 'BFC', 75, 'FR', 47.26648000, 5.00628000, '2019-10-05 22:48:38', '2020-05-01 17:22:44', 1, 'Q20323'),
(44906, 'Perros-Guirec', 4807, 'BRE', 75, 'FR', 48.81454000, -3.43963000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q239943'),
(44907, 'Perrusson', 4818, 'CVL', 75, 'FR', 47.09873000, 1.01438000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q239943'),
(44908, 'Pers-Jussy', 4798, 'ARA', 75, 'FR', 46.10594000, 6.26955000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q239943'),
(44909, 'Persan', 4796, 'IDF', 75, 'FR', 49.15335000, 2.27218000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q239943'),
(44910, 'Perthes', 4796, 'IDF', 75, 'FR', 48.47821000, 2.55509000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q239943'),
(44911, 'Pertuis', 4812, 'PAC', 75, 'FR', 43.69415000, 5.50291000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q239943'),
(44912, 'Peschadoires', 4798, 'ARA', 75, 'FR', 45.82622000, 3.49255000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q239943'),
(44913, 'Pesmes', 4825, 'BFC', 75, 'FR', 47.28008000, 5.56698000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q601099'),
(44914, 'Pessac', 4795, 'NAQ', 75, 'FR', 44.81011000, -0.64129000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q207156'),
(44915, 'Petit-Couronne', 4804, 'NOR', 75, 'FR', 49.38333000, 1.01667000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q207156'),
(44916, 'Petit-Mars', 4802, 'PDL', 75, 'FR', 47.39512000, -1.45262000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44917, 'Petit-Noir', 4825, 'BFC', 75, 'FR', 46.93333000, 5.33333000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44918, 'Petit-Réderching', 4820, 'GES', 75, 'FR', 49.05494000, 7.30640000, '2019-10-05 22:48:38', '2020-05-01 17:22:45', 1, '********'),
(44919, 'Petite-Forêt', 4828, 'HDF', 75, 'FR', 50.36667000, 3.48333000, '2019-10-05 22:48:38', '2020-05-01 17:22:45', 1, '********'),
(44920, 'Petite-Rosselle', 4820, 'GES', 75, 'FR', 49.21177000, 6.85607000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44921, 'Petiville', 4804, 'NOR', 75, 'FR', 49.46078000, 0.58740000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44922, 'Peujard', 4795, 'NAQ', 75, 'FR', 45.03619000, -0.44096000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44923, 'Peymeinade', 4812, 'PAC', 75, 'FR', 43.64244000, 6.87583000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44924, 'Peynier', 4812, 'PAC', 75, 'FR', 43.44837000, 5.64139000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q594551'),
(44925, 'Peypin', 4812, 'PAC', 75, 'FR', 43.38503000, 5.57788000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q594551'),
(44926, 'Peyrat-de-Bellac', 4795, 'NAQ', 75, 'FR', 46.14087000, 1.03661000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q594551'),
(44927, 'Peyrat-le-Château', 4795, 'NAQ', 75, 'FR', 45.81578000, 1.77233000, '2019-10-05 22:48:38', '2020-05-01 17:22:46', 1, 'Q779574'),
(44928, 'Peyrehorade', 4795, 'NAQ', 75, 'FR', 43.54886000, -1.11574000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q664928'),
(44929, 'Peyrestortes', 4799, 'OCC', 75, 'FR', 42.75480000, 2.85232000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q664928'),
(44930, 'Peyriac-Minervois', 4799, 'OCC', 75, 'FR', 43.29068000, 2.56613000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q664928'),
(44931, 'Peyriac-de-Mer', 4799, 'OCC', 75, 'FR', 43.08720000, 2.95831000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q664928'),
(44932, 'Peyrilhac', 4795, 'NAQ', 75, 'FR', 45.95043000, 1.13503000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q664928'),
(44933, 'Peyrins', 4798, 'ARA', 75, 'FR', 45.09331000, 5.04513000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q664928'),
(44934, 'Peyrolles-en-Provence', 4812, 'PAC', 75, 'FR', 43.64545000, 5.58492000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q640764'),
(44935, 'Peyruis', 4812, 'PAC', 75, 'FR', 44.02880000, 5.94032000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q374183'),
(44936, 'Pezens', 4799, 'OCC', 75, 'FR', 43.25446000, 2.26868000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q374183'),
(44937, 'Pfaffenheim', 4820, 'GES', 75, 'FR', 47.98567000, 7.28928000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q147712'),
(44938, 'Pfaffenhoffen', 4820, 'GES', 75, 'FR', 48.84220000, 7.60714000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q21301'),
(44939, 'Pfastatt', 4820, 'GES', 75, 'FR', 47.76915000, 7.30295000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q148497'),
(44940, 'Pfetterhouse', 4820, 'GES', 75, 'FR', 47.50128000, 7.16598000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q148099'),
(44941, 'Pfulgriesheim', 4820, 'GES', 75, 'FR', 48.64403000, 7.67086000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q148099'),
(44942, 'Phalempin', 4828, 'HDF', 75, 'FR', 50.51691000, 3.01584000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q745616'),
(44943, 'Phalsbourg', 4820, 'GES', 75, 'FR', 48.76771000, 7.25820000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22160'),
(44944, 'Pia', 4799, 'OCC', 75, 'FR', 42.74490000, 2.91930000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22160'),
(44945, 'Pibrac', 4799, 'OCC', 75, 'FR', 43.62042000, 1.28540000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22160'),
(44946, 'Picauville', 4804, 'NOR', 75, 'FR', 49.37810000, -1.40048000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22160'),
(44947, 'Picquigny', 4828, 'HDF', 75, 'FR', 49.94413000, 2.14376000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q587061'),
(44948, 'Piennes', 4820, 'GES', 75, 'FR', 49.30964000, 5.78009000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q587061'),
(44949, 'Pierre-Buffière', 4795, 'NAQ', 75, 'FR', 45.69193000, 1.36193000, '2019-10-05 22:48:38', '2020-05-01 17:22:46', 1, 'Q587061'),
(44950, 'Pierre-Bénite', 4798, 'ARA', 75, 'FR', 45.70359000, 4.82424000, '2019-10-05 22:48:38', '2020-05-01 17:22:43', 1, 'Q1617042'),
(44951, 'Pierre-Châtel', 4798, 'ARA', 75, 'FR', 44.95735000, 5.77454000, '2019-10-05 22:48:38', '2020-05-01 17:22:43', 1, 'Q1617042'),
(44952, 'Pierre-de-Bresse', 4825, 'BFC', 75, 'FR', 46.88333000, 5.25000000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q1617042'),
(44953, 'Pierrefeu-du-Var', 4812, 'PAC', 75, 'FR', 43.22411000, 6.14536000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q829276'),
(44954, 'Pierrefitte-Nestalas', 4799, 'OCC', 75, 'FR', 42.96667000, -0.06667000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q829276'),
(44955, 'Pierrefitte-sur-Seine', 4796, 'IDF', 75, 'FR', 48.96691000, 2.36104000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q253939'),
(44956, 'Pierrefonds', 4828, 'HDF', 75, 'FR', 49.34878000, 2.97790000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q253939'),
(44957, 'Pierrefontaine-les-Varans', 4825, 'BFC', 75, 'FR', 47.21601000, 6.54030000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q253939'),
(44958, 'Pierrefort', 4798, 'ARA', 75, 'FR', 44.92175000, 2.83811000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q275202'),
(44959, 'Pierrelatte', 4798, 'ARA', 75, 'FR', 44.37549000, 4.70314000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q324027'),
(44960, 'Pierrelaye', 4796, 'IDF', 75, 'FR', 49.02110000, 2.15481000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q324027'),
(44961, 'Pierrepont', 4820, 'GES', 75, 'FR', 49.41646000, 5.70908000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q324027'),
(44962, 'Pierres', 4818, 'CVL', 75, 'FR', 48.59209000, 1.56444000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q324027'),
(44963, 'Pierrevert', 4812, 'PAC', 75, 'FR', 43.81137000, 5.74958000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q1017916'),
(44964, 'Pierrevillers', 4820, 'GES', 75, 'FR', 49.22468000, 6.10337000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22923'),
(44965, 'Pierry', 4820, 'GES', 75, 'FR', 49.02024000, 3.94071000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q766472'),
(44966, 'Pietranera', 4806, '20R', 75, 'FR', 42.72338000, 9.45621000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q766472'),
(44967, 'Pietrosella', 4806, '20R', 75, 'FR', 41.83576000, 8.84573000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q766472'),
(44968, 'Pignan', 4799, 'OCC', 75, 'FR', 43.58365000, 3.75981000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q979659'),
(44969, 'Pignans', 4812, 'PAC', 75, 'FR', 43.30071000, 6.22650000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q979659'),
(44970, 'Pinet', 4799, 'OCC', 75, 'FR', 43.40556000, 3.51000000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q199554'),
(44971, 'Piney', 4820, 'GES', 75, 'FR', 48.36366000, 4.33325000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q199554'),
(44972, 'Pinon', 4828, 'HDF', 75, 'FR', 49.48916000, 3.44703000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q325426'),
(44973, 'Pinsaguel', 4799, 'OCC', 75, 'FR', 43.51040000, 1.38831000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q325426'),
(44974, 'Piolenc', 4812, 'PAC', 75, 'FR', 44.17765000, 4.76157000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q512467'),
(44975, 'Pionsat', 4798, 'ARA', 75, 'FR', 46.10963000, 2.69319000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q512467'),
(44976, 'Pipriac', 4807, 'BRE', 75, 'FR', 47.80953000, -1.94638000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q512467'),
(44977, 'Pirey', 4825, 'BFC', 75, 'FR', 47.26136000, 5.96466000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q512467'),
(44978, 'Piriac-sur-Mer', 4802, 'PDL', 75, 'FR', 47.37938000, -2.54616000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q646812'),
(44979, 'Pirou', 4804, 'NOR', 75, 'FR', 49.18089000, -1.57384000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q733902'),
(44980, 'Piré-sur-Seiche', 4807, 'BRE', 75, 'FR', 48.00958000, -1.42909000, '2019-10-05 22:48:38', '2020-05-01 17:22:44', 1, 'Q196579'),
(44981, 'Pissos', 4795, 'NAQ', 75, 'FR', 44.30833000, -0.77963000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q196579'),
(44982, 'Pissotte', 4802, 'PDL', 75, 'FR', 46.49725000, -0.80666000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q507844'),
(44983, 'Pissy-Pôville', 4804, 'NOR', 75, 'FR', 49.52986000, 0.99281000, '2019-10-05 22:48:38', '2020-05-01 17:22:45', 1, 'Q507844'),
(44984, 'Pithiviers', 4818, 'CVL', 75, 'FR', 48.17185000, 2.25185000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q217159'),
(44985, 'Pithiviers-le-Vieil', 4818, 'CVL', 75, 'FR', 48.16313000, 2.20922000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q217159'),
(44986, 'Piégut-Pluviers', 4795, 'NAQ', 75, 'FR', 45.62297000, 0.69013000, '2019-10-05 22:48:38', '2020-05-01 17:22:46', 1, 'Q217159'),
(44987, 'Plabennec', 4807, 'BRE', 75, 'FR', 48.50244000, -4.42656000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q218240'),
(44988, 'Plailly', 4828, 'HDF', 75, 'FR', 49.10288000, 2.58549000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44989, 'Plaimpied-Givaudins', 4818, 'CVL', 75, 'FR', 46.99800000, 2.45428000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44990, 'Plaine-Haute', 4807, 'BRE', 75, 'FR', 48.44442000, -2.85416000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, '********'),
(44991, 'Plainfaing', 4820, 'GES', 75, 'FR', 48.17284000, 7.01539000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q903654'),
(44992, 'Plaintel', 4807, 'BRE', 75, 'FR', 48.40733000, -2.81733000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q209712'),
(44993, 'Plaisance', 4799, 'OCC', 75, 'FR', 43.60436000, 0.04615000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q209712'),
(44994, 'Plaisance-du-Touch', 4799, 'OCC', 75, 'FR', 43.56566000, 1.29749000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q209712'),
(44995, 'Plaisir', 4796, 'IDF', 75, 'FR', 48.82319000, 1.95410000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q209712'),
(44996, 'Plan-d\'Aups-Sainte-Baume', 4812, 'PAC', 75, 'FR', 43.33333000, 5.71667000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q209712'),
(44997, 'Plan-de-Cuques', 4812, 'PAC', 75, 'FR', 43.34753000, 5.46398000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q533703'),
(44998, 'Plancher-Bas', 4825, 'BFC', 75, 'FR', 47.71977000, 6.73041000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q533703'),
(44999, 'Plancher-les-Mines', 4825, 'BFC', 75, 'FR', 47.76150000, 6.74308000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q533703'),
(45000, 'Plancoët', 4807, 'BRE', 75, 'FR', 48.52199000, -2.23540000, '2019-10-05 22:48:38', '2020-05-01 17:22:44', 1, 'Q533703'),
(45001, 'Planguenoual', 4807, 'BRE', 75, 'FR', 48.53300000, -2.57642000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q543746'),
(45002, 'Plappeville', 4820, 'GES', 75, 'FR', 49.13019000, 6.12471000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22268'),
(45003, 'Plaudren', 4807, 'BRE', 75, 'FR', 47.77868000, -2.69331000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22268'),
(45004, 'Plauzat', 4798, 'ARA', 75, 'FR', 45.62157000, 3.14896000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22268'),
(45005, 'Pleaux', 4798, 'ARA', 75, 'FR', 45.13479000, 2.22652000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q22268'),
(45006, 'Pleine-Fougères', 4807, 'BRE', 75, 'FR', 48.53308000, -1.56534000, '2019-10-05 22:48:38', '2020-05-01 17:22:44', 1, 'Q22268'),
(45007, 'Plerguer', 4807, 'BRE', 75, 'FR', 48.52676000, -1.84769000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q196505'),
(45008, 'Plescop', 4807, 'BRE', 75, 'FR', 47.69726000, -2.80560000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q196505'),
(45009, 'Pleslin-Trigavou', 4807, 'BRE', 75, 'FR', 48.53333000, -2.06667000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q371638'),
(45010, 'Plessala', 4807, 'BRE', 75, 'FR', 48.27642000, -2.61876000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q222970'),
(45011, 'Plessé', 4802, 'PDL', 75, 'FR', 47.54180000, -1.88609000, '2019-10-05 22:48:38', '2020-05-01 17:22:46', 1, '********'),
(45012, 'Plestan', 4807, 'BRE', 75, 'FR', 48.42266000, -2.44777000, '2019-10-05 22:48:38', '2019-10-05 22:48:38', 1, 'Q223157'),
(45013, 'Plestin-les-Grèves', 4807, 'BRE', 75, 'FR', 48.65844000, -3.63056000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q864086'),
(45014, 'Pleubian', 4807, 'BRE', 75, 'FR', 48.84267000, -3.13900000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q864086'),
(45015, 'Pleucadeuc', 4807, 'BRE', 75, 'FR', 47.75876000, -2.37362000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128741'),
(45016, 'Pleudaniel', 4807, 'BRE', 75, 'FR', 48.76644000, -3.14033000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q868518'),
(45017, 'Pleudihen-sur-Rance', 4807, 'BRE', 75, 'FR', 48.51667000, -1.96667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q383288'),
(45018, 'Pleugriffet', 4807, 'BRE', 75, 'FR', 47.98594000, -2.68454000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q383288'),
(45019, 'Pleugueneuc', 4807, 'BRE', 75, 'FR', 48.39735000, -1.90301000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q383288'),
(45020, 'Pleumartin', 4795, 'NAQ', 75, 'FR', 46.73786000, 0.76900000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q1435417'),
(45021, 'Pleumeleuc', 4807, 'BRE', 75, 'FR', 48.18438000, -1.91962000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q1435417'),
(45022, 'Pleumeur-Bodou', 4807, 'BRE', 75, 'FR', 48.76667000, -3.51667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q453263'),
(45023, 'Pleumeur-Gautier', 4807, 'BRE', 75, 'FR', 48.80044000, -3.15578000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q453263'),
(45024, 'Pleurtuit', 4807, 'BRE', 75, 'FR', 48.57858000, -2.05805000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q598335'),
(45025, 'Pleuven', 4807, 'BRE', 75, 'FR', 47.90000000, -4.03333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q598335'),
(45026, 'Pleyben', 4807, 'BRE', 75, 'FR', 48.23333000, -3.96667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q478231'),
(45027, 'Pleyber-Christ', 4807, 'BRE', 75, 'FR', 48.50000000, -3.86667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q475828'),
(45028, 'Plobannalec-Lesconil', 4807, 'BRE', 75, 'FR', 47.81667000, -4.21667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q622503'),
(45029, 'Plobsheim', 4820, 'GES', 75, 'FR', 48.46979000, 7.72442000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q21539'),
(45030, 'Ploemel', 4807, 'BRE', 75, 'FR', 47.65153000, -3.07030000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128193'),
(45031, 'Ploemeur', 4807, 'BRE', 75, 'FR', 47.73333000, -3.43333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128369'),
(45032, 'Ploeren', 4807, 'BRE', 75, 'FR', 47.65713000, -2.86397000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128648'),
(45033, 'Plogastel-Saint-Germain', 4807, 'BRE', 75, 'FR', 47.98333000, -4.26667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q474684'),
(45034, 'Plogoff', 4807, 'BRE', 75, 'FR', 48.03700000, -4.66606000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q390901'),
(45035, 'Plogonnec', 4807, 'BRE', 75, 'FR', 48.08333000, -4.18333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q471129'),
(45036, 'Plombières-les-Bains', 4820, 'GES', 75, 'FR', 47.96337000, 6.45758000, '2019-10-05 22:48:39', '2020-05-01 17:22:45', 1, 'Q632969'),
(45037, 'Plombières-lès-Dijon', 4825, 'BFC', 75, 'FR', 47.33333000, 4.96667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q632969'),
(45038, 'Plomelin', 4807, 'BRE', 75, 'FR', 47.93333000, -4.15000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q225512'),
(45039, 'Plomeur', 4807, 'BRE', 75, 'FR', 47.83333000, -4.28333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q475592'),
(45040, 'Plomodiern', 4807, 'BRE', 75, 'FR', 48.18333000, -4.23333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q210660'),
(45041, 'Plonéis', 4807, 'BRE', 75, 'FR', 48.01667000, -4.21667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q210660'),
(45042, 'Plonéour-Lanvern', 4807, 'BRE', 75, 'FR', 47.90000000, -4.28333000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q494980'),
(45043, 'Plonévez-Porzay', 4807, 'BRE', 75, 'FR', 48.10000000, -4.21667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q471095'),
(45044, 'Plonévez-du-Faou', 4807, 'BRE', 75, 'FR', 48.25000000, -3.83333000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q221266'),
(45045, 'Plouagat', 4807, 'BRE', 75, 'FR', 48.53611000, -2.99956000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q543942'),
(45046, 'Plouaret', 4807, 'BRE', 75, 'FR', 48.61667000, -3.46667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q225616'),
(45047, 'Plouarzel', 4807, 'BRE', 75, 'FR', 48.43333000, -4.73333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q208323'),
(45048, 'Plouasne', 4807, 'BRE', 75, 'FR', 48.30081000, -2.00698000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q208323'),
(45049, 'Plouay', 4807, 'BRE', 75, 'FR', 47.91444000, -3.33384000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q129002'),
(45050, 'Ploubalay', 4807, 'BRE', 75, 'FR', 48.58109000, -2.14069000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q868543'),
(45051, 'Ploubazlanec', 4807, 'BRE', 75, 'FR', 48.80000000, -3.03333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q783528'),
(45052, 'Ploubezre', 4807, 'BRE', 75, 'FR', 48.70000000, -3.45000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q473616'),
(45053, 'Ploudalmézeau', 4807, 'BRE', 75, 'FR', 48.53333000, -4.65000000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q12169'),
(45054, 'Ploudaniel', 4807, 'BRE', 75, 'FR', 48.53333000, -4.31667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q622488'),
(45055, 'Plouescat', 4807, 'BRE', 75, 'FR', 48.66667000, -4.16667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q391138'),
(45056, 'Plouezoc\'h', 4807, 'BRE', 75, 'FR', 48.63333000, -3.81667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q391138'),
(45057, 'Ploufragan', 4807, 'BRE', 75, 'FR', 48.49141000, -2.79458000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q225204'),
(45058, 'Plougasnou', 4807, 'BRE', 75, 'FR', 48.70000000, -3.80000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q166941'),
(45059, 'Plougastel-Daoulas', 4807, 'BRE', 75, 'FR', 48.36667000, -4.36667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q383277'),
(45060, 'Plougonvelin', 4807, 'BRE', 75, 'FR', 48.34059000, -4.71846000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q383277'),
(45061, 'Plougonven', 4807, 'BRE', 75, 'FR', 48.51667000, -3.71667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q634057'),
(45062, 'Plougoulm', 4807, 'BRE', 75, 'FR', 48.66667000, -4.05000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q456831'),
(45063, 'Plougoumelen', 4807, 'BRE', 75, 'FR', 47.65070000, -2.91716000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q456831'),
(45064, 'Plougourvest', 4807, 'BRE', 75, 'FR', 48.55000000, -4.08333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q456831'),
(45065, 'Plougrescant', 4807, 'BRE', 75, 'FR', 48.84026000, -3.22886000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q456831'),
(45066, 'Plouguenast', 4807, 'BRE', 75, 'FR', 48.28208000, -2.70443000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q455242'),
(45067, 'Plouguerneau', 4807, 'BRE', 75, 'FR', 48.60000000, -4.50000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q477749'),
(45068, 'Plouguernével', 4807, 'BRE', 75, 'FR', 48.23940000, -3.25071000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1021331'),
(45069, 'Plouguiel', 4807, 'BRE', 75, 'FR', 48.79742000, -3.23883000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q1021331'),
(45070, 'Plouguin', 4807, 'BRE', 75, 'FR', 48.53333000, -4.60000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q635153'),
(45071, 'Plouha', 4807, 'BRE', 75, 'FR', 48.67650000, -2.92842000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q217114'),
(45072, 'Plouharnel', 4807, 'BRE', 75, 'FR', 47.59804000, -3.11274000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q127874'),
(45073, 'Plouhinec', 4807, 'BRE', 75, 'FR', 48.01667000, -4.48333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q127874'),
(45074, 'Plouider', 4807, 'BRE', 75, 'FR', 48.61667000, -4.30000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q622485'),
(45075, 'Plouigneau', 4807, 'BRE', 75, 'FR', 48.56667000, -3.70000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q218049'),
(45076, 'Plouisy', 4807, 'BRE', 75, 'FR', 48.57817000, -3.18434000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q218049'),
(45077, 'Ploumagoar', 4807, 'BRE', 75, 'FR', 48.54509000, -3.13233000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q1021304'),
(45078, 'Ploumilliau', 4807, 'BRE', 75, 'FR', 48.68333000, -3.51667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q219649'),
(45079, 'Ploumoguer', 4807, 'BRE', 75, 'FR', 48.40000000, -4.71667000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q219649'),
(45080, 'Plounéour-Ménez', 4807, 'BRE', 75, 'FR', 48.45000000, -3.88333000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q495496'),
(45081, 'Plounéour-Trez', 4807, 'BRE', 75, 'FR', 48.65000000, -4.31667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q495496'),
(45082, 'Plounéventer', 4807, 'BRE', 75, 'FR', 48.51667000, -4.21667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q226598'),
(45083, 'Plounévez-Lochrist', 4807, 'BRE', 75, 'FR', 48.61667000, -4.21667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q383495'),
(45084, 'Plounévez-Moëdec', 4807, 'BRE', 75, 'FR', 48.56667000, -3.45000000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1013174'),
(45085, 'Plounévez-Quintin', 4807, 'BRE', 75, 'FR', 48.28333000, -3.23333000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1013174'),
(45086, 'Plounévézel', 4807, 'BRE', 75, 'FR', 48.30000000, -3.60000000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1013174'),
(45087, 'Plouray', 4807, 'BRE', 75, 'FR', 48.14580000, -3.38671000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q1013174'),
(45088, 'Plourin-lès-Morlaix', 4807, 'BRE', 75, 'FR', 48.53333000, -3.78333000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q219128'),
(45089, 'Plourivo', 4807, 'BRE', 75, 'FR', 48.74550000, -3.07075000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q387815'),
(45090, 'Plouvien', 4807, 'BRE', 75, 'FR', 48.53333000, -4.45000000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q216971'),
(45091, 'Plouvorn', 4807, 'BRE', 75, 'FR', 48.58333000, -4.03333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q209866'),
(45092, 'Plouzané', 4807, 'BRE', 75, 'FR', 48.38333000, -4.61667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q474768'),
(45093, 'Plouzévédé', 4807, 'BRE', 75, 'FR', 48.60000000, -4.11667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q474768'),
(45094, 'Plouédern', 4807, 'BRE', 75, 'FR', 48.48333000, -4.25000000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q474521'),
(45095, 'Plouénan', 4807, 'BRE', 75, 'FR', 48.63333000, -4.00000000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q622457'),
(45096, 'Plouézec', 4807, 'BRE', 75, 'FR', 48.75122000, -2.98467000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q221278'),
(45097, 'Plouëc-du-Trieux', 4807, 'BRE', 75, 'FR', 48.68333000, -3.20000000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1023496'),
(45098, 'Plouër-sur-Rance', 4807, 'BRE', 75, 'FR', 48.52669000, -2.00298000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q665309'),
(45099, 'Plozévet', 4807, 'BRE', 75, 'FR', 47.98333000, -4.41667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q474680'),
(45100, 'Ploërdut', 4807, 'BRE', 75, 'FR', 48.08742000, -3.28550000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q127965'),
(45101, 'Ploërmel', 4807, 'BRE', 75, 'FR', 47.93172000, -2.39781000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q127775'),
(45102, 'Ploëzal', 4807, 'BRE', 75, 'FR', 48.71534000, -3.20333000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q945919'),
(45103, 'Pluduno', 4807, 'BRE', 75, 'FR', 48.53110000, -2.26848000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q945919'),
(45104, 'Pluguffan', 4807, 'BRE', 75, 'FR', 47.98333000, -4.18333000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q945919'),
(45105, 'Pluherlin', 4807, 'BRE', 75, 'FR', 47.69632000, -2.36349000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128872'),
(45106, 'Plumaugat', 4807, 'BRE', 75, 'FR', 48.25479000, -2.23843000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q594119'),
(45107, 'Plumelec', 4807, 'BRE', 75, 'FR', 47.83754000, -2.63987000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128990'),
(45108, 'Plumelin', 4807, 'BRE', 75, 'FR', 47.86036000, -2.88754000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128990'),
(45109, 'Plumergat', 4807, 'BRE', 75, 'FR', 47.74134000, -2.91501000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128990'),
(45110, 'Plumieux', 4807, 'BRE', 75, 'FR', 48.10303000, -2.58382000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128990'),
(45111, 'Pluméliau', 4807, 'BRE', 75, 'FR', 47.95769000, -2.97494000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q128215'),
(45112, 'Pluneret', 4807, 'BRE', 75, 'FR', 47.67525000, -2.95782000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128997'),
(45113, 'Plurien', 4807, 'BRE', 75, 'FR', 48.62559000, -2.40423000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q128997'),
(45114, 'Pluvigner', 4807, 'BRE', 75, 'FR', 47.77627000, -3.01013000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q127796'),
(45115, 'Pluzunet', 4807, 'BRE', 75, 'FR', 48.64076000, -3.37113000, '2019-10-05 22:48:39', '2019-10-05 22:48:39', 1, 'Q655322'),
(45116, 'Pléchâtel', 4807, 'BRE', 75, 'FR', 47.89496000, -1.74876000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q655322'),
(45117, 'Plédran', 4807, 'BRE', 75, 'FR', 48.44600000, -2.74667000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q456818'),
(45118, 'Plédéliac', 4807, 'BRE', 75, 'FR', 48.45000000, -2.38333000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1013479'),
(45119, 'Pléguien', 4807, 'BRE', 75, 'FR', 48.63525000, -2.94001000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1013479'),
(45120, 'Pléhédel', 4807, 'BRE', 75, 'FR', 48.69645000, -3.00833000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1013479'),
(45121, 'Plélan-le-Grand', 4807, 'BRE', 75, 'FR', 48.00124000, -2.09914000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1011325'),
(45122, 'Plélan-le-Petit', 4807, 'BRE', 75, 'FR', 48.43157000, -2.21740000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q742008'),
(45123, 'Plélo', 4807, 'BRE', 75, 'FR', 48.55534000, -2.94634000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1082451'),
(45124, 'Plémet', 4807, 'BRE', 75, 'FR', 48.17601000, -2.59325000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q868564'),
(45125, 'Plémy', 4807, 'BRE', 75, 'FR', 48.33644000, -2.68244000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q868564'),
(45126, 'Pléneuf-Val-André', 4807, 'BRE', 75, 'FR', 48.59300000, -2.54675000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q212964'),
(45127, 'Plénée-Jugon', 4807, 'BRE', 75, 'FR', 48.36462000, -2.40036000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q212964'),
(45128, 'Plérin', 4807, 'BRE', 75, 'FR', 48.54249000, -2.77983000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q214598'),
(45129, 'Plœuc-sur-Lié', 4807, 'BRE', 75, 'FR', 48.35000000, -2.75000000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q216908'),
(45130, 'Pocé-les-Bois', 4807, 'BRE', 75, 'FR', 48.11626000, -1.25168000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q216908'),
(45131, 'Pocé-sur-Cisse', 4818, 'CVL', 75, 'FR', 47.44330000, 0.99142000, '2019-10-05 22:48:39', '2020-05-01 17:22:44', 1, 'Q1170864'),
(45132, 'Podensac', 4795, 'NAQ', 75, 'FR', 44.65038000, -0.35508000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1170864'),
(45133, 'Poey-de-Lescar', 4795, 'NAQ', 75, 'FR', 43.35000000, -0.46667000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q857961'),
(45134, 'Poilly-lez-Gien', 4818, 'CVL', 75, 'FR', 47.67724000, 2.59743000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q857961'),
(45135, 'Poisat', 4798, 'ARA', 75, 'FR', 45.15852000, 5.76051000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q857961'),
(45136, 'Poissy', 4796, 'IDF', 75, 'FR', 48.92902000, 2.04952000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q457'),
(45137, 'Poisy', 4798, 'ARA', 75, 'FR', 45.92142000, 6.06356000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q532070'),
(45138, 'Poitiers', 4795, 'NAQ', 75, 'FR', 46.58333000, 0.33333000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q6616'),
(45139, 'Poix-de-Picardie', 4828, 'HDF', 75, 'FR', 49.78333000, 1.98333000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q6616'),
(45140, 'Poix-du-Nord', 4828, 'HDF', 75, 'FR', 50.18927000, 3.60956000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q6616'),
(45141, 'Polignac', 4798, 'ARA', 75, 'FR', 45.07090000, 3.86031000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q6616'),
(45142, 'Poligny', 4825, 'BFC', 75, 'FR', 46.83712000, 5.70505000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q6616'),
(45143, 'Pollestres', 4799, 'OCC', 75, 'FR', 42.64200000, 2.87116000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q6616'),
(45144, 'Polliat', 4798, 'ARA', 75, 'FR', 46.24849000, 5.12658000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q6616'),
(45145, 'Pollionnay', 4798, 'ARA', 75, 'FR', 45.76459000, 4.66112000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45146, 'Polminhac', 4798, 'ARA', 75, 'FR', 44.95177000, 2.57751000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45147, 'Pomarez', 4795, 'NAQ', 75, 'FR', 43.62971000, -0.82934000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45148, 'Pommeret', 4807, 'BRE', 75, 'FR', 48.46378000, -2.62689000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45149, 'Pommerit-Jaudy', 4807, 'BRE', 75, 'FR', 48.73058000, -3.24208000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45150, 'Pommerit-le-Vicomte', 4807, 'BRE', 75, 'FR', 48.61989000, -3.09000000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45151, 'Pommeuse', 4796, 'IDF', 75, 'FR', 48.81667000, 3.01667000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45152, 'Pommiers', 4798, 'ARA', 75, 'FR', 45.95540000, 4.69251000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45153, 'Pompaire', 4795, 'NAQ', 75, 'FR', 46.60727000, -0.23237000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45154, 'Pompertuzat', 4799, 'OCC', 75, 'FR', 43.49277000, 1.51531000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45155, 'Pompey', 4820, 'GES', 75, 'FR', 48.76876000, 6.12639000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45156, 'Pompignac', 4795, 'NAQ', 75, 'FR', 44.85114000, -0.43705000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45157, 'Pompignan', 4799, 'OCC', 75, 'FR', 43.81768000, 1.31209000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45158, 'Pomponne', 4796, 'IDF', 75, 'FR', 48.88130000, 2.68232000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1388871'),
(45159, 'Pomérols', 4799, 'OCC', 75, 'FR', 43.39139000, 3.49944000, '2019-10-05 22:48:40', '2020-05-01 17:22:46', 1, 'Q384838'),
(45160, 'Ponchon', 4828, 'HDF', 75, 'FR', 49.34652000, 2.19677000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q384838'),
(45161, 'Poncin', 4798, 'ARA', 75, 'FR', 46.08531000, 5.41120000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q384838'),
(45162, 'Pons', 4795, 'NAQ', 75, 'FR', 45.57988000, -0.54783000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q384838'),
(45163, 'Pont de Vivaux', 4812, 'PAC', 75, 'FR', 43.27604000, 5.41586000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q384838'),
(45164, 'Pont-Audemer', 4804, 'NOR', 75, 'FR', 49.35000000, 0.51667000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q367667'),
(45165, 'Pont-Aven', 4807, 'BRE', 75, 'FR', 47.85000000, -3.75000000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q238377'),
(45166, 'Pont-Croix', 4807, 'BRE', 75, 'FR', 48.04088000, -4.48714000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q238377'),
(45167, 'Pont-Hébert', 4804, 'NOR', 75, 'FR', 49.16714000, -1.13428000, '2019-10-05 22:48:40', '2020-05-01 17:22:45', 1, 'Q238377'),
(45168, 'Pont-Remy', 4828, 'HDF', 75, 'FR', 50.05000000, 1.91667000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q238377'),
(45169, 'Pont-Saint-Esprit', 4799, 'OCC', 75, 'FR', 44.25494000, 4.64736000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q243074'),
(45170, 'Pont-Saint-Martin', 4802, 'PDL', 75, 'FR', 47.12338000, -1.58455000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q243074'),
(45171, 'Pont-Saint-Pierre', 4804, 'NOR', 75, 'FR', 49.33561000, 1.27601000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q243074'),
(45172, 'Pont-Saint-Vincent', 4820, 'GES', 75, 'FR', 48.60519000, 6.09850000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, '********'),
(45173, 'Pont-Sainte-Marie', 4820, 'GES', 75, 'FR', 48.31848000, 4.09447000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, '********'),
(45174, 'Pont-Sainte-Maxence', 4828, 'HDF', 75, 'FR', 49.30168000, 2.60467000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, '********'),
(45175, 'Pont-Salomon', 4798, 'ARA', 75, 'FR', 45.33777000, 4.24781000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, '********'),
(45176, 'Pont-Scorff', 4807, 'BRE', 75, 'FR', 47.83343000, -3.40251000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q128278'),
(45177, 'Pont-d\'Ouilly', 4804, 'NOR', 75, 'FR', 48.87590000, -0.40221000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q128278'),
(45178, 'Pont-de-Chéruy', 4798, 'ARA', 75, 'FR', 45.75344000, 5.17245000, '2019-10-05 22:48:40', '2020-05-01 17:22:43', 1, 'Q509919'),
(45179, 'Pont-de-Larn', 4799, 'OCC', 75, 'FR', 43.50412000, 2.40786000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q509919'),
(45180, 'Pont-de-Metz', 4828, 'HDF', 75, 'FR', 49.87820000, 2.24266000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q509919'),
(45181, 'Pont-de-Roide', 4825, 'BFC', 75, 'FR', 47.38738000, 6.76840000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q841396'),
(45182, 'Pont-de-Salars', 4799, 'OCC', 75, 'FR', 44.28080000, 2.72783000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1141413'),
(45183, 'Pont-de-Vaux', 4798, 'ARA', 75, 'FR', 46.43326000, 4.94473000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1141413'),
(45184, 'Pont-de-Veyle', 4798, 'ARA', 75, 'FR', 46.26166000, 4.88612000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q769579'),
(45185, 'Pont-du-Casse', 4795, 'NAQ', 75, 'FR', 44.22867000, 0.67924000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1070029'),
(45186, 'Pont-du-Château', 4798, 'ARA', 75, 'FR', 45.79830000, 3.24839000, '2019-10-05 22:48:40', '2020-05-01 17:22:43', 1, 'Q375535'),
(45187, 'Pont-d’Ain', 4798, 'ARA', 75, 'FR', 46.05583000, 5.33974000, '2019-10-05 22:48:40', '2020-05-01 17:22:43', 1, 'Q269706'),
(45188, 'Pont-en-Royans', 4798, 'ARA', 75, 'FR', 45.06250000, 5.34094000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q631960'),
(45189, 'Pont-l’Abbé', 4807, 'BRE', 75, 'FR', 47.86667000, -4.21667000, '2019-10-05 22:48:40', '2020-05-01 17:22:44', 1, 'Q631960'),
(45190, 'Pont-l’Abbé-d’Arnoult', 4795, 'NAQ', 75, 'FR', 45.82875000, -0.87499000, '2019-10-05 22:48:40', '2020-05-01 17:22:46', 1, 'Q631960'),
(45191, 'Pont-l’Évêque', 4804, 'NOR', 75, 'FR', 49.28912000, 0.19161000, '2019-10-05 22:48:40', '2020-05-01 17:22:45', 1, 'Q631960'),
(45192, 'Pont-sur-Sambre', 4828, 'HDF', 75, 'FR', 50.22195000, 3.84693000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1100346'),
(45193, 'Pont-sur-Yonne', 4825, 'BFC', 75, 'FR', 48.28297000, 3.20198000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q752938'),
(45194, 'Pont-Écrepin', 4804, 'NOR', 75, 'FR', 48.76425000, -0.24455000, '2019-10-05 22:48:40', '2020-05-01 17:22:45', 1, 'Q752938'),
(45195, 'Pont-Évêque', 4798, 'ARA', 75, 'FR', 45.53217000, 4.90922000, '2019-10-05 22:48:40', '2020-05-01 17:22:43', 1, 'Q752938'),
(45196, 'Pont-à-Marcq', 4828, 'HDF', 75, 'FR', 50.52213000, 3.11441000, '2019-10-05 22:48:40', '2020-05-01 17:22:45', 1, 'Q752938'),
(45197, 'Pont-à-Mousson', 4820, 'GES', 75, 'FR', 48.90702000, 6.05635000, '2019-10-05 22:48:40', '2020-05-01 17:22:45', 1, 'Q461413'),
(45198, 'Pont-à-Vendin', 4828, 'HDF', 75, 'FR', 50.47358000, 2.88884000, '2019-10-05 22:48:40', '2020-05-01 17:22:45', 1, 'Q461413'),
(45199, 'Pontacq', 4795, 'NAQ', 75, 'FR', 43.18333000, -0.11667000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q380481'),
(45200, 'Pontailler-sur-Saône', 4825, 'BFC', 75, 'FR', 47.30455000, 5.41479000, '2019-10-05 22:48:40', '2020-05-01 17:22:44', 1, 'Q68995'),
(45201, 'Pontarlier', 4825, 'BFC', 75, 'FR', 46.90347000, 6.35542000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q214540'),
(45202, 'Pontault-Combault', 4796, 'IDF', 75, 'FR', 48.79813000, 2.60676000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q717049'),
(45203, 'Pontcarré', 4796, 'IDF', 75, 'FR', 48.79772000, 2.70508000, '2019-10-05 22:48:40', '2020-05-01 17:22:43', 1, '********'),
(45204, 'Pontcharra', 4798, 'ARA', 75, 'FR', 45.43588000, 6.01782000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q472184'),
(45205, 'Pontcharra-sur-Turdine', 4798, 'ARA', 75, 'FR', 45.87413000, 4.48989000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q472184'),
(45206, 'Pontchâteau', 4802, 'PDL', 75, 'FR', 47.43797000, -2.09011000, '2019-10-05 22:48:40', '2020-05-01 17:22:46', 1, 'Q818116'),
(45207, 'Ponteilla', 4799, 'OCC', 75, 'FR', 42.62594000, 2.81335000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q818116'),
(45208, 'Pontenx-les-Forges', 4795, 'NAQ', 75, 'FR', 44.24134000, -1.12095000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q818116'),
(45209, 'Pontfaverger-Moronvilliers', 4820, 'GES', 75, 'FR', 49.29702000, 4.31657000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, '********'),
(45210, 'Ponthierry', 4796, 'IDF', 75, 'FR', 48.53366000, 2.54419000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, '********'),
(45211, 'Pontivy', 4807, 'BRE', 75, 'FR', 48.06862000, -2.96280000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q55905'),
(45212, 'Pontlevoy', 4818, 'CVL', 75, 'FR', 47.39002000, 1.25465000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q266148'),
(45213, 'Pontoise', 4796, 'IDF', 75, 'FR', 49.05000000, 2.10000000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q201352'),
(45214, 'Pontonx-sur-l\'Adour', 4795, 'NAQ', 75, 'FR', 43.78783000, -0.92508000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q201352'),
(45215, 'Pontorson', 4804, 'NOR', 75, 'FR', 48.55316000, -1.50754000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q331426'),
(45216, 'Pontpoint', 4828, 'HDF', 75, 'FR', 49.30000000, 2.65000000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q764113'),
(45217, 'Pontrieux', 4807, 'BRE', 75, 'FR', 48.70111000, -3.15967000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q764113'),
(45218, 'Pontvallain', 4802, 'PDL', 75, 'FR', 47.75169000, 0.19145000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q769338'),
(45219, 'Porcelette', 4820, 'GES', 75, 'FR', 49.15657000, 6.65513000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q22296'),
(45220, 'Porcheville', 4796, 'IDF', 75, 'FR', 48.97254000, 1.77973000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q244294'),
(45221, 'Porcieu', 4798, 'ARA', 75, 'FR', 45.83416000, 5.40168000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q244294'),
(45222, 'Pordic', 4807, 'BRE', 75, 'FR', 48.57051000, -2.81783000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q661268'),
(45223, 'Pornic', 4802, 'PDL', 75, 'FR', 47.11205000, -2.08888000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q752390'),
(45224, 'Pornichet', 4802, 'PDL', 75, 'FR', 47.26682000, -2.33794000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q656483'),
(45225, 'Porspoder', 4807, 'BRE', 75, 'FR', 48.50000000, -4.76667000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q468249'),
(45226, 'Port à Binson', 4820, 'GES', 75, 'FR', 49.08202000, 3.75989000, '2019-10-05 22:48:40', '2020-05-01 17:22:45', 1, 'Q468249'),
(45227, 'Port-Brillet', 4802, 'PDL', 75, 'FR', 48.11315000, -0.97080000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q742451'),
(45228, 'Port-Louis', 4807, 'BRE', 75, 'FR', 47.70704000, -3.35484000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q742451'),
(45229, 'Port-Saint-Louis-du-Rhône', 4812, 'PAC', 75, 'FR', 43.38734000, 4.82609000, '2019-10-05 22:48:40', '2020-05-01 17:22:46', 1, 'Q268765'),
(45230, 'Port-Saint-Père', 4802, 'PDL', 75, 'FR', 47.13294000, -1.74850000, '2019-10-05 22:48:40', '2020-05-01 17:22:46', 1, 'Q650520'),
(45231, 'Port-Sainte-Foy-et-Ponchapt', 4795, 'NAQ', 75, 'FR', 44.83333000, 0.20000000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q629660'),
(45232, 'Port-Sainte-Marie', 4795, 'NAQ', 75, 'FR', 44.25158000, 0.39134000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q629660'),
(45233, 'Port-de-Bouc', 4812, 'PAC', 75, 'FR', 43.40657000, 4.98090000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q675925'),
(45234, 'Port-des-Barques', 4795, 'NAQ', 75, 'FR', 45.94763000, -1.07795000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q675925'),
(45235, 'Port-en-Bessin-Huppain', 4804, 'NOR', 75, 'FR', 49.34460000, -0.75557000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q987304'),
(45236, 'Port-la-Nouvelle', 4799, 'OCC', 75, 'FR', 43.01991000, 3.04366000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q626282'),
(45237, 'Port-sur-Saône', 4825, 'BFC', 75, 'FR', 47.68774000, 6.05011000, '2019-10-05 22:48:40', '2020-05-01 17:22:44', 1, 'Q831303'),
(45238, 'Portbail', 4804, 'NOR', 75, 'FR', 49.33571000, -1.69560000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q630364'),
(45239, 'Portel-des-Corbières', 4799, 'OCC', 75, 'FR', 43.05000000, 2.91667000, '2019-10-05 22:48:40', '2020-05-01 17:22:46', 1, 'Q630364'),
(45240, 'Portes-lès-Valence', 4798, 'ARA', 75, 'FR', 44.87585000, 4.87415000, '2019-10-05 22:48:40', '2020-05-01 17:22:43', 1, 'Q630364'),
(45241, 'Portet-sur-Garonne', 4799, 'OCC', 75, 'FR', 43.52333000, 1.40651000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q1371171'),
(45242, 'Portets', 4795, 'NAQ', 75, 'FR', 44.69679000, -0.42452000, '2019-10-05 22:48:40', '2019-10-05 22:48:40', 1, 'Q379674'),
(45243, 'Portieux', 4820, 'GES', 75, 'FR', 48.34466000, 6.34465000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q836332'),
(45244, 'Portiragnes', 4799, 'OCC', 75, 'FR', 43.30460000, 3.33365000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q836332'),
(45245, 'Porto-Vecchio', 4806, '20R', 75, 'FR', 41.59101000, 9.27947000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q241567'),
(45246, 'Portvendres', 4799, 'OCC', 75, 'FR', 42.51792000, 3.10553000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q204648'),
(45247, 'Poses', 4804, 'NOR', 75, 'FR', 49.30539000, 1.24353000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q204648'),
(45248, 'Potigny', 4804, 'NOR', 75, 'FR', 48.96925000, -0.24166000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q204648'),
(45249, 'Pouancé', 4802, 'PDL', 75, 'FR', 47.74167000, -1.17366000, '2019-10-05 22:48:41', '2020-05-01 17:22:46', 1, 'Q639582'),
(45250, 'Pougues-les-Eaux', 4825, 'BFC', 75, 'FR', 47.07518000, 3.10150000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q46575'),
(45251, 'Pouilley-les-Vignes', 4825, 'BFC', 75, 'FR', 47.25770000, 5.93581000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q906642'),
(45252, 'Pouillon', 4795, 'NAQ', 75, 'FR', 43.60480000, -0.99947000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q906642'),
(45253, 'Pouilly-en-Auxois', 4825, 'BFC', 75, 'FR', 47.26238000, 4.55583000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q906642'),
(45254, 'Pouilly-les-Nonains', 4798, 'ARA', 75, 'FR', 46.04508000, 3.98247000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q906642'),
(45255, 'Pouilly-lès-Feurs', 4798, 'ARA', 75, 'FR', 45.79760000, 4.23289000, '2019-10-05 22:48:41', '2020-05-01 17:22:43', 1, 'Q906642'),
(45256, 'Pouilly-sous-Charlieu', 4798, 'ARA', 75, 'FR', 46.14551000, 4.11287000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q906642'),
(45257, 'Pouilly-sur-Loire', 4825, 'BFC', 75, 'FR', 47.28377000, 2.95442000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q46529'),
(45258, 'Poulainville', 4828, 'HDF', 75, 'FR', 49.94745000, 2.31373000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q46529'),
(45259, 'Pouldergat', 4807, 'BRE', 75, 'FR', 48.05000000, -4.33333000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q46529'),
(45260, 'Pouldreuzic', 4807, 'BRE', 75, 'FR', 47.95000000, -4.36667000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q496294'),
(45261, 'Pouligny-Saint-Pierre', 4818, 'CVL', 75, 'FR', 46.68095000, 1.03877000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q616668'),
(45262, 'Poullan-sur-Mer', 4807, 'BRE', 75, 'FR', 48.08333000, -4.41667000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q226416'),
(45263, 'Poullaouen', 4807, 'BRE', 75, 'FR', 48.33333000, -3.65000000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q495478'),
(45264, 'Poulx', 4799, 'OCC', 75, 'FR', 43.90798000, 4.41629000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q273202'),
(45265, 'Pourrain', 4825, 'BFC', 75, 'FR', 47.75615000, 3.41193000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q273202'),
(45266, 'Pourrières', 4812, 'PAC', 75, 'FR', 43.50611000, 5.73452000, '2019-10-05 22:48:41', '2020-05-01 17:22:46', 1, 'Q273202'),
(45267, 'Pouru-Saint-Remy', 4820, 'GES', 75, 'FR', 49.68333000, 5.08333000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q273202'),
(45268, 'Poussan', 4799, 'OCC', 75, 'FR', 43.48944000, 3.67083000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, '********'),
(45269, 'Pouxeux', 4820, 'GES', 75, 'FR', 48.10621000, 6.57299000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, '********'),
(45270, 'Pouzac', 4799, 'OCC', 75, 'FR', 43.08606000, 0.13522000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, '********'),
(45271, 'Pouzauges', 4802, 'PDL', 75, 'FR', 46.77941000, -0.83619000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q838334'),
(45272, 'Prades', 4799, 'OCC', 75, 'FR', 42.61715000, 2.42208000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q838334'),
(45273, 'Prades-le-Lez', 4799, 'OCC', 75, 'FR', 43.69754000, 3.86463000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q196475'),
(45274, 'Pradines', 4799, 'OCC', 75, 'FR', 44.48341000, 1.40105000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q196475'),
(45275, 'Pragoulin', 4798, 'ARA', 75, 'FR', 46.05425000, 3.39346000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q744225'),
(45276, 'Prahecq', 4795, 'NAQ', 75, 'FR', 46.25897000, -0.34425000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q744225'),
(45277, 'Prat', 4807, 'BRE', 75, 'FR', 48.67677000, -3.29707000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q744225'),
(45278, 'Prats de Molló', 4799, 'OCC', 75, 'FR', 42.40404000, 2.47928000, '2019-10-05 22:48:41', '2020-05-01 17:22:46', 1, 'Q744225'),
(45279, 'Prayssac', 4799, 'OCC', 75, 'FR', 44.50440000, 1.18862000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q744225'),
(45280, 'Praz-sur-Arly', 4798, 'ARA', 75, 'FR', 45.83731000, 6.57259000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q744225'),
(45281, 'Preignac', 4795, 'NAQ', 75, 'FR', 44.58438000, -0.29423000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q194920'),
(45282, 'Preignan', 4799, 'OCC', 75, 'FR', 43.71177000, 0.62608000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q194920'),
(45283, 'Presles-en-Brie', 4796, 'IDF', 75, 'FR', 48.71527000, 2.74112000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q194920'),
(45284, 'Preuilly-sur-Claise', 4818, 'CVL', 75, 'FR', 46.85424000, 0.92954000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q680407'),
(45285, 'Priay', 4798, 'ARA', 75, 'FR', 46.00183000, 5.28684000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q680407'),
(45286, 'Prigonrieux', 4795, 'NAQ', 75, 'FR', 44.85451000, 0.40275000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q204253'),
(45287, 'Pringy', 4798, 'ARA', 75, 'FR', 45.94622000, 6.12608000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q204253'),
(45288, 'Pringy', 4796, 'IDF', 75, 'FR', 48.51815000, 2.56333000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q204253'),
(45289, 'Prinquiau', 4802, 'PDL', 75, 'FR', 47.36215000, -2.00952000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q633101'),
(45290, 'Prissé', 4825, 'BFC', 75, 'FR', 46.32130000, 4.74469000, '2019-10-05 22:48:41', '2020-05-01 17:22:44', 1, 'Q633101'),
(45291, 'Privas', 4798, 'ARA', 75, 'FR', 44.73500000, 4.59918000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q193060'),
(45292, 'Prix-lès-Mézières', 4820, 'GES', 75, 'FR', 49.75549000, 4.69113000, '2019-10-05 22:48:41', '2020-05-01 17:22:45', 1, 'Q193060'),
(45293, 'Priziac', 4807, 'BRE', 75, 'FR', 48.06667000, -3.41667000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q128863'),
(45294, 'Propriano', 4806, '20R', 75, 'FR', 41.67590000, 8.90412000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q128863'),
(45295, 'Prouvy', 4828, 'HDF', 75, 'FR', 50.31793000, 3.45096000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, '********'),
(45296, 'Proville', 4828, 'HDF', 75, 'FR', 50.16137000, 3.20629000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, '********'),
(45297, 'Provin', 4828, 'HDF', 75, 'FR', 50.51430000, 2.90794000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, '********'),
(45298, 'Provins', 4796, 'IDF', 75, 'FR', 48.55897000, 3.29939000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q203285'),
(45299, 'Pruillé-le-Chétif', 4802, 'PDL', 75, 'FR', 47.99383000, 0.10677000, '2019-10-05 22:48:41', '2020-05-01 17:22:46', 1, 'Q203285'),
(45300, 'Prunelli-di-Fiumorbo', 4806, '20R', 75, 'FR', 42.01047000, 9.32473000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, '********'),
(45301, 'Pruniers-en-Sologne', 4818, 'CVL', 75, 'FR', 47.31667000, 1.66667000, '2019-10-05 22:48:41', '2019-10-05 22:48:41', 1, 'Q752901');

