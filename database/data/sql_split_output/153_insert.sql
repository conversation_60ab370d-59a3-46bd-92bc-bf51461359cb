INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(77033, 'Monguno', 307, '<PERSON><PERSON>', 161, 'NG', 12.67059000, 13.61224000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q6887995'),
(77034, 'Moriki', 299, 'ZA', 161, 'NG', 12.87405000, 6.48754000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q6887995'),
(77035, 'Mu<PERSON>', 320, 'AD', 161, 'NG', 10.26858000, 13.26701000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77036, '<PERSON><PERSON> B<PERSON>yu', 319, 'TA', 161, 'NG', 8.64138000, 10.77355000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77037, 'Nafada', 310, 'GO', 161, 'NG', 11.09596000, 11.33261000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77038, 'Nasarawa', 301, 'NA', 161, 'NG', 8.53895000, 7.70821000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77039, 'New Shagunnu', 317, 'NI', 161, 'NG', 10.33957000, 4.46880000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77040, 'Ngala', 307, 'BO', 161, 'NG', 12.34053000, 14.18670000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77041, 'Ngurore', 320, 'AD', 161, 'NG', 9.28871000, 12.24026000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77042, 'Nguru', 297, 'YO', 161, 'NG', 12.87695000, 10.45536000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77043, 'Nkpor', 315, 'AN', 161, 'NG', 6.15038000, 6.83042000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q1951627'),
(77044, 'Nnewi', 315, 'AN', 161, 'NG', 6.01962000, 6.91729000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q2750772'),
(77045, 'Nsukka', 289, 'EN', 161, 'NG', 6.85783000, 7.39577000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q2750772'),
(77046, 'Numan', 320, 'AD', 161, 'NG', 9.46374000, 12.03062000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q2750772'),
(77047, 'Obiaruku', 316, 'DE', 161, 'NG', 5.84672000, 6.15290000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q7074926'),
(77048, 'Obolo-Eke (1)', 289, 'EN', 161, 'NG', 6.88333000, 7.63333000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q7074926'),
(77049, 'Ochobo', 291, 'BE', 161, 'NG', 7.18045000, 7.98240000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q7074926'),
(77050, 'Ode', 321, 'ON', 161, 'NG', 7.78990000, 5.71170000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q7074926'),
(77051, 'Offa', 295, 'KW', 161, 'NG', 8.14911000, 4.72074000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q7074926'),
(77052, 'Ogaminana', 298, 'KO', 161, 'NG', 7.59383000, 6.21798000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q7074926'),
(77053, 'Ogbomoso', 296, 'OY', 161, 'NG', 8.13373000, 4.24014000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q500366'),
(77054, 'Ogurugu', 298, 'KO', 161, 'NG', 6.78636000, 6.95017000, '2019-10-05 23:10:41', '2019-10-05 23:10:41', 1, 'Q500366'),
(77055, 'Oguta', 308, 'IM', 161, 'NG', 5.71044000, 6.80936000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q500366'),
(77056, 'Ogwashi-Uku', 316, 'DE', 161, 'NG', 6.17811000, 6.52461000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7080311'),
(77057, 'Ohafia-Ifigh', 303, 'AB', 161, 'NG', 5.61455000, 7.81191000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7080311'),
(77058, 'Oke Ila', 309, 'EK', 161, 'NG', 7.95000000, 4.98333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7080311'),
(77059, 'Oke Mesi', 322, 'OS', 161, 'NG', 7.81667000, 4.91667000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7080311'),
(77060, 'Okeho', 296, 'OY', 161, 'NG', 8.03386000, 3.34759000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7080311'),
(77061, 'Okene', 298, 'KO', 161, 'NG', 7.55122000, 6.23589000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q2017187'),
(77062, 'Okigwe', 308, 'IM', 161, 'NG', 5.82917000, 7.35056000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q2017187'),
(77063, 'Okuta', 295, 'KW', 161, 'NG', 9.21667000, 3.18333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q2017187'),
(77064, 'Olupona', 322, 'OS', 161, 'NG', 7.60000000, 4.18333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q2017187'),
(77065, 'Omu-Aran', 295, 'KW', 161, 'NG', 8.13857000, 5.10260000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q2017187'),
(77066, 'Omuo-Ekiti', 309, 'EK', 161, 'NG', 7.75833000, 5.72227000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q2017187'),
(77067, 'Ondo', 321, 'ON', 161, 'NG', 7.09316000, 4.83528000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1853310'),
(77068, 'Onitsha', 315, 'AN', 161, 'NG', 6.14978000, 6.78569000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q852238'),
(77069, 'Opi', 289, 'EN', 161, 'NG', 6.78223000, 7.43319000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q852238'),
(77070, 'Ore', 321, 'ON', 161, 'NG', 6.74716000, 4.87610000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q852238'),
(77071, 'Orerokpe', 316, 'DE', 161, 'NG', 5.63747000, 5.89013000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q852238'),
(77072, 'Orita Eruwa', 296, 'OY', 161, 'NG', 7.55000000, 3.43333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q852238'),
(77073, 'Orlu', 308, 'IM', 161, 'NG', 5.79565000, 7.03513000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q852238'),
(77074, 'Orodo', 308, 'IM', 161, 'NG', 5.61667000, 7.03333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q852238'),
(77075, 'Osogbo', 322, 'OS', 161, 'NG', 7.77104000, 4.55698000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q868203'),
(77076, 'Otan Ayegbaju', 322, 'OS', 161, 'NG', 7.94783000, 4.78836000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3320817'),
(77077, 'Otukpa', 291, 'BE', 161, 'NG', 7.10168000, 7.65945000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3320817'),
(77078, 'Owerri', 308, 'IM', 161, 'NG', 5.48363000, 7.03325000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q583619'),
(77079, 'Owo', 321, 'ON', 161, 'NG', 7.19620000, 5.58681000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1788128'),
(77080, 'Owode', 323, 'OG', 161, 'NG', 6.94851000, 3.50561000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1788128'),
(77081, 'Oyan', 322, 'OS', 161, 'NG', 8.05000000, 4.76667000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1788128'),
(77082, 'Oyo', 296, 'OY', 161, 'NG', 7.85257000, 3.93125000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77083, 'Ozubulu', 315, 'AN', 161, 'NG', 5.95753000, 6.85305000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77084, 'Pankshin', 302, 'PL', 161, 'NG', 9.32541000, 9.43520000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77085, 'Panyam', 302, 'PL', 161, 'NG', 9.40756000, 9.21481000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77086, 'Patani', 316, 'DE', 161, 'NG', 5.22885000, 6.19139000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77087, 'Patigi', 295, 'KW', 161, 'NG', 8.72851000, 5.75561000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77088, 'Pindiga', 310, 'GO', 161, 'NG', 9.98433000, 10.95229000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77089, 'Potiskum', 297, 'YO', 161, 'NG', 11.71391000, 11.08108000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77090, 'Rabah', 292, 'SO', 161, 'NG', 13.12257000, 5.50762000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77091, 'Ringim', 288, 'JI', 161, 'NG', 12.15143000, 9.16216000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77092, 'Riti', 319, 'TA', 161, 'NG', 7.90844000, 9.61688000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77093, 'Ruma', 313, 'KT', 161, 'NG', 12.86260000, 7.23469000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77094, 'Runka', 313, 'KT', 161, 'NG', 12.44788000, 7.30918000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77095, 'Sade', 312, 'BA', 161, 'NG', 11.35950000, 10.67320000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q1023703'),
(77096, 'Saki', 296, 'OY', 161, 'NG', 8.66762000, 3.39393000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77097, 'Samamiya', 288, 'JI', 161, 'NG', 11.34873000, 9.63989000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77098, 'Saminaka', 294, 'KD', 161, 'NG', 10.41227000, 8.68748000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77099, 'Sapele', 316, 'DE', 161, 'NG', 5.89405000, 5.67666000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77100, 'Sauri', 299, 'ZA', 161, 'NG', 11.72655000, 6.78374000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77101, 'Shaffa', 307, 'BO', 161, 'NG', 10.50673000, 12.33315000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77102, 'Shanga', 290, 'KE', 161, 'NG', 11.21374000, 4.57941000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77103, 'Shani', 307, 'BO', 161, 'NG', 10.21824000, 12.06059000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77104, 'Siluko', 318, 'ED', 161, 'NG', 6.53589000, 5.16005000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77105, 'Soba', 294, 'KD', 161, 'NG', 10.98133000, 8.05749000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77106, 'Sofo-Birnin-Gwari', 294, 'KD', 161, 'NG', 11.01537000, 6.78036000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3500788'),
(77107, 'Sokoto', 292, 'SO', 161, 'NG', 13.06269000, 5.24322000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77108, 'Suleja', 317, 'NI', 161, 'NG', 9.18059000, 7.17939000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77109, 'Suya', 295, 'KW', 161, 'NG', 9.46667000, 3.18333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77110, 'Takum', 291, 'BE', 161, 'NG', 7.26667000, 9.98333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77111, 'Tambuwal', 292, 'SO', 161, 'NG', 12.40592000, 4.64605000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77112, 'Tegina', 317, 'NI', 161, 'NG', 10.07060000, 6.19060000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77113, 'Tokombere', 307, 'BO', 161, 'NG', 11.22135000, 13.48783000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77114, 'Toungo', 320, 'AD', 161, 'NG', 8.11733000, 12.04611000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77115, 'Tsafe', 299, 'ZA', 161, 'NG', 11.95775000, 6.92083000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q336350'),
(77116, 'Twon-Brass', 305, 'BY', 161, 'NG', 4.31231000, 6.24091000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7859479'),
(77117, 'Uba', 307, 'BO', 161, 'NG', 10.45509000, 13.22233000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7859479'),
(77118, 'Ubiaja', 318, 'ED', 161, 'NG', 6.65581000, 6.38494000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7859479'),
(77119, 'Udi', 289, 'EN', 161, 'NG', 6.31592000, 7.42086000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7859479'),
(77120, 'Uga', 315, 'AN', 161, 'NG', 5.93576000, 7.07930000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7859479'),
(77121, 'Ugbokpo', 291, 'BE', 161, 'NG', 7.65321000, 7.88410000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q7859479'),
(77122, 'Ugep', 314, 'CR', 161, 'NG', 5.80865000, 8.08098000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3084715'),
(77123, 'Ughelli', 316, 'DE', 161, 'NG', 5.48956000, 6.00407000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3509166'),
(77124, 'Ukata', 317, 'NI', 161, 'NG', 10.83122000, 5.82494000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3509166'),
(77125, 'Umuahia', 303, 'AB', 161, 'NG', 5.52491000, 7.49461000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q203301'),
(77126, 'Umunede', 316, 'DE', 161, 'NG', 6.26549000, 6.30962000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q203301'),
(77127, 'Uromi', 318, 'ED', 161, 'NG', 6.70000000, 6.33333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3510262'),
(77128, 'Uruobo-Okija', 315, 'AN', 161, 'NG', 5.90016000, 6.84312000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3510262'),
(77129, 'Uyo', 304, 'AK', 161, 'NG', 5.05127000, 7.93350000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3510262'),
(77130, 'Vom', 302, 'PL', 161, 'NG', 9.72910000, 8.79138000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3510262'),
(77131, 'Wagini', 313, 'KT', 161, 'NG', 12.68781000, 7.19579000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3510262'),
(77132, 'Wamba', 301, 'NA', 161, 'NG', 8.94153000, 8.60315000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q3510262'),
(77133, 'Warri', 316, 'DE', 161, 'NG', 5.51737000, 5.75006000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77134, 'Wasagu', 290, 'KE', 161, 'NG', 11.37640000, 5.79536000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77135, 'Wawa', 317, 'NI', 161, 'NG', 9.90222000, 4.41917000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77136, 'Wukari', 319, 'TA', 161, 'NG', 7.87139000, 9.77786000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77137, 'Wurno', 292, 'SO', 161, 'NG', 13.29048000, 5.42373000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77138, 'Wuyo', 307, 'BO', 161, 'NG', 10.38522000, 11.69678000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77139, 'Yajiwa', 307, 'BO', 161, 'NG', 11.38623000, 12.71992000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77140, 'Yamrat', 312, 'BA', 161, 'NG', 10.11161000, 9.82604000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77141, 'Yanda Bayo', 312, 'BA', 161, 'NG', 11.50710000, 10.74590000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77142, 'Yandev', 291, 'BE', 161, 'NG', 7.36308000, 9.04235000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77143, 'Yashikera', 295, 'KW', 161, 'NG', 9.76667000, 3.40000000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77144, 'Yelwa', 302, 'PL', 161, 'NG', 8.83333000, 9.63333000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77145, 'Yenagoa', 305, 'BY', 161, 'NG', 4.92675000, 6.26764000, '2019-10-05 23:10:42', '2019-10-05 23:10:42', 1, 'Q574593'),
(77146, 'Yola', 320, 'AD', 161, 'NG', 9.20839000, 12.48146000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q994114'),
(77147, 'Yuli', 312, 'BA', 161, 'NG', 9.69707000, 10.27350000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q994114'),
(77148, 'Zadawa', 312, 'BA', 161, 'NG', 11.44960000, 10.36720000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q994114'),
(77149, 'Zaki Biam', 291, 'BE', 161, 'NG', 7.50671000, 9.61040000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q994114'),
(77150, 'Zalanga', 312, 'BA', 161, 'NG', 10.61432000, 10.17647000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q994114'),
(77151, 'Zaria', 294, 'KD', 161, 'NG', 11.11128000, 7.72270000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q147975'),
(77152, 'Zungeru', 317, 'NI', 161, 'NG', 9.80726000, 6.15238000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q8075286'),
(77153, 'Zuru', 290, 'KE', 161, 'NG', 11.43522000, 5.23494000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q8075286'),
(77154, 'Achuapa', 944, 'LE', 159, 'NI', 13.05370000, -86.59004000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q8075286'),
(77155, 'Acoyapa', 940, 'CO', 159, 'NI', 11.97028000, -85.17113000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1186803'),
(77156, 'Altagracia', 942, 'RI', 159, 'NI', 11.56615000, -85.57840000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1186803'),
(77157, 'Belén', 942, 'RI', 159, 'NI', 11.50299000, -85.88935000, '2019-10-05 23:11:40', '2020-05-01 17:23:03', 1, 'Q1647916'),
(77158, 'Bluefields', 952, 'AS', 159, 'NI', 12.01366000, -83.76353000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q885996'),
(77159, 'Boaco', 946, 'BO', 159, 'NI', 12.47224000, -85.65860000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q887488'),
(77160, 'Bocana de Paiwas', 952, 'AS', 159, 'NI', 12.78571000, -85.12269000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q691374'),
(77161, 'Bonanza', 951, 'AN', 159, 'NI', 14.02885000, -84.59103000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q892092'),
(77162, 'Buenos Aires', 942, 'RI', 159, 'NI', 11.46916000, -85.81661000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1648230'),
(77163, 'Camoapa', 946, 'BO', 159, 'NI', 12.38383000, -85.51277000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2605022'),
(77164, 'Catarina', 953, 'MS', 159, 'NI', 11.91197000, -86.07383000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1647897'),
(77165, 'Chichigalpa', 954, 'CI', 159, 'NI', 12.57758000, -87.02705000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1072117'),
(77166, 'Chinandega', 954, 'CI', 159, 'NI', 12.62937000, -87.13105000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q974354'),
(77167, 'Cinco Pinos', 954, 'CI', 159, 'NI', 13.22956000, -86.86808000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1647867'),
(77168, 'Ciudad Darío', 947, 'MT', 159, 'NI', 12.73143000, -86.12402000, '2019-10-05 23:11:40', '2020-05-01 17:23:03', 1, 'Q1015865'),
(77169, 'Ciudad Sandino', 941, 'MN', 159, 'NI', 12.15889000, -86.34417000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2720849'),
(77170, 'Comalapa', 940, 'CO', 159, 'NI', 12.28345000, -85.51081000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q677924'),
(77171, 'Condega', 945, 'ES', 159, 'NI', 13.36502000, -86.39846000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1012589'),
(77172, 'Corinto', 954, 'CI', 159, 'NI', 12.48250000, -87.17304000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1132939'),
(77173, 'Corn Island', 952, 'AS', 159, 'NI', 12.17575000, -83.06145000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q22280935'),
(77174, 'Cuapa', 940, 'CO', 159, 'NI', 12.26875000, -85.38205000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2215598'),
(77175, 'Cárdenas', 942, 'RI', 159, 'NI', 11.19639000, -85.50890000, '2019-10-05 23:11:40', '2020-05-01 17:23:03', 1, 'Q1647924'),
(77176, 'Diriamba', 950, 'CA', 159, 'NI', 11.85812000, -86.23922000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1227687'),
(77177, 'Diriomo', 943, 'GR', 159, 'NI', 11.87631000, -86.05184000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2215569'),
(77178, 'Diriá', 943, 'GR', 159, 'NI', 11.88420000, -86.05508000, '2019-10-05 23:11:40', '2020-05-01 17:23:03', 1, 'Q1647847'),
(77179, 'Dolores', 950, 'CA', 159, 'NI', 11.85672000, -86.21552000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1647920'),
(77180, 'El Almendro', 949, 'SJ', 159, 'NI', 11.67859000, -84.70269000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2621532'),
(77181, 'El Ayote', 940, 'CO', 159, 'NI', 12.19046000, -85.28737000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2215912'),
(77182, 'El Crucero', 941, 'MN', 159, 'NI', 11.99008000, -86.30954000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2605353'),
(77183, 'El Cuá', 955, 'JI', 159, 'NI', 13.41667000, -85.75000000, '2019-10-05 23:11:40', '2020-05-01 17:23:03', 1, 'Q2214906'),
(77184, 'El Jicaral', 944, 'LE', 159, 'NI', 12.72676000, -86.38057000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q369423'),
(77185, 'El Rama', 952, 'AS', 159, 'NI', 12.15965000, -84.21952000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q21054015'),
(77186, 'El Realejo', 954, 'CI', 159, 'NI', 12.54333000, -87.16517000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q368227'),
(77187, 'El Rosario', 950, 'CA', 159, 'NI', 11.77756000, -86.37374000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1648222'),
(77188, 'El Sauce', 944, 'LE', 159, 'NI', 12.88687000, -86.53903000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1647835'),
(77189, 'El Tortuguero', 952, 'AS', 159, 'NI', 12.82247000, -84.19629000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q21053675'),
(77190, 'El Viejo', 954, 'CI', 159, 'NI', 12.66348000, -87.16663000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q516482'),
(77191, 'Estelí', 945, 'ES', 159, 'NI', 13.09185000, -86.35384000, '2019-10-05 23:11:40', '2020-05-01 17:23:03', 1, 'Q1007634'),
(77192, 'Granada', 943, 'GR', 159, 'NI', 11.92988000, -85.95602000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q205648'),
(77193, 'Greytown', 949, 'SJ', 159, 'NI', 10.94684000, -83.73467000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q2063725'),
(77194, 'Jinotega', 955, 'JI', 159, 'NI', 13.09103000, -86.00234000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1026296'),
(77195, 'Jinotepe', 950, 'CA', 159, 'NI', 11.84962000, -86.19903000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q477580'),
(77196, 'Jiquilillo', 954, 'CI', 159, 'NI', 12.74593000, -87.45160000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q21054284'),
(77197, 'Juigalpa', 940, 'CO', 159, 'NI', 12.10629000, -85.36452000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1025801'),
(77198, 'Kukrahill', 952, 'AS', 159, 'NI', 12.24096000, -83.74517000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1647889'),
(77199, 'LLano de La Cruz', 955, 'JI', 159, 'NI', 13.12449000, -86.00833000, '2019-10-05 23:11:40', '2019-10-05 23:11:40', 1, 'Q1647889'),
(77200, 'La Concepción', 953, 'MS', 159, 'NI', 11.93711000, -86.18976000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q538269'),
(77201, 'La Concordia', 955, 'JI', 159, 'NI', 13.19528000, -86.16659000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2395382'),
(77202, 'La Conquista', 950, 'CA', 159, 'NI', 11.73426000, -86.19279000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3820751'),
(77203, 'La Cruz de Río Grande', 952, 'AS', 159, 'NI', 13.11290000, -84.18588000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q963732'),
(77204, 'La Libertad', 940, 'CO', 159, 'NI', 12.21635000, -85.16595000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2606938'),
(77205, 'La Paz Centro', 944, 'LE', 159, 'NI', 12.34000000, -86.67528000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q21052193'),
(77206, 'La Paz de Carazo', 950, 'CA', 159, 'NI', 11.82311000, -86.12781000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215549'),
(77207, 'La Trinidad', 945, 'ES', 159, 'NI', 12.96881000, -86.23534000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q993829'),
(77208, 'Laguna de Perlas', 952, 'AS', 159, 'NI', 12.34294000, -83.67123000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215086'),
(77209, 'Larreynaga', 944, 'LE', 159, 'NI', 12.67692000, -86.57193000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215577'),
(77210, 'Las Praderas', 955, 'JI', 159, 'NI', 13.17000000, -85.85000000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2217474'),
(77211, 'Las Sabanas', 948, 'MD', 159, 'NI', 13.34302000, -86.62184000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215117'),
(77212, 'León', 944, 'LE', 159, 'NI', 12.43787000, -86.87804000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q208151'),
(77213, 'Managua', 941, 'MN', 159, 'NI', 12.13282000, -86.25040000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3274'),
(77214, 'Masachapa', 941, 'MN', 159, 'NI', 11.78687000, -86.51416000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q21053389'),
(77215, 'Masatepe', 953, 'MS', 159, 'NI', 11.91445000, -86.14458000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2720586'),
(77216, 'Masaya', 953, 'MS', 159, 'NI', 11.97444000, -86.09417000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1001914'),
(77217, 'Matagalpa', 947, 'MT', 159, 'NI', 12.92559000, -85.91747000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3851689'),
(77218, 'Matiguás', 947, 'MT', 159, 'NI', 12.83734000, -85.46218000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215416'),
(77219, 'Morrito', 949, 'SJ', 159, 'NI', 11.62118000, -85.08052000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2621523'),
(77220, 'Moyogalpa', 942, 'RI', 159, 'NI', 11.54006000, -85.69795000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215641'),
(77221, 'Muelle de los Bueyes', 952, 'AS', 159, 'NI', 12.06999000, -84.53503000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215047'),
(77222, 'Municipio de Altagracia', 942, 'RI', 159, 'NI', 11.47983000, -85.54166000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647907'),
(77223, 'Municipio de Belén', 942, 'RI', 159, 'NI', 11.57193000, -85.96316000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q1647907'),
(77224, 'Municipio de Buenos Aires', 942, 'RI', 159, 'NI', 11.51760000, -85.78333000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647907'),
(77225, 'Municipio de Cárdenas', 942, 'RI', 159, 'NI', 11.14912000, -85.42323000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q1647907'),
(77226, 'Municipio de Masatepe', 953, 'MS', 159, 'NI', 11.90567000, -86.14946000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647907'),
(77227, 'Municipio de Moyogalpa', 942, 'RI', 159, 'NI', 11.52632000, -85.67329000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647907'),
(77228, 'Municipio de Nandasmo', 953, 'MS', 159, 'NI', 11.93333000, -86.11667000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647907'),
(77229, 'Municipio de Niquinohomo', 953, 'MS', 159, 'NI', 11.88228000, -86.10035000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647907'),
(77230, 'Municipio de Potosí', 942, 'RI', 159, 'NI', 11.57854000, -85.87764000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q1647907'),
(77231, 'Municipio de Rivas', 942, 'RI', 159, 'NI', 11.43931000, -85.82700000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647907'),
(77232, 'Municipio de San Francisco del Norte', 954, 'CI', 159, 'NI', 13.21187000, -86.77107000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2214920'),
(77233, 'Municipio de San Jorge', 942, 'RI', 159, 'NI', 11.42703000, -85.78904000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2214920'),
(77234, 'Municipio de San Juan de Oriente', 953, 'MS', 159, 'NI', 11.90517000, -86.07460000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2214920'),
(77235, 'Municipio de San Juan del Sur', 942, 'RI', 159, 'NI', 11.22368000, -85.78289000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2214920'),
(77236, 'Municipio de San Marcos', 950, 'CA', 159, 'NI', 11.91784000, -86.27474000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2214920'),
(77237, 'Municipio de Tola', 942, 'RI', 159, 'NI', 11.46009000, -86.00789000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2214920'),
(77238, 'Muy Muy', 947, 'MT', 159, 'NI', 12.76224000, -85.62915000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215631'),
(77239, 'Nagarote', 944, 'LE', 159, 'NI', 12.26593000, -86.56474000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2604568'),
(77240, 'Nandaime', 943, 'GR', 159, 'NI', 11.75696000, -86.05286000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1964554'),
(77241, 'Nandasmo', 953, 'MS', 159, 'NI', 11.92411000, -86.12072000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q641207'),
(77242, 'Nindirí', 953, 'MS', 159, 'NI', 12.00386000, -86.12128000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215135'),
(77243, 'Niquinohomo', 953, 'MS', 159, 'NI', 11.90518000, -86.09446000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1514478'),
(77244, 'Nueva Guinea', 952, 'AS', 159, 'NI', 11.68758000, -84.45616000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2554341'),
(77245, 'Palacagüina', 948, 'MD', 159, 'NI', 13.45566000, -86.40622000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215620'),
(77246, 'Posoltega', 954, 'CI', 159, 'NI', 12.54422000, -86.97982000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2604656'),
(77247, 'Potosí', 942, 'RI', 159, 'NI', 11.49416000, -85.85680000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q1647911'),
(77248, 'Prinzapolka', 951, 'AN', 159, 'NI', 13.40708000, -83.56452000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1018776'),
(77249, 'Pueblo Nuevo', 945, 'ES', 159, 'NI', 13.37984000, -86.48075000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647679'),
(77250, 'Puerto Cabezas', 951, 'AN', 159, 'NI', 14.03507000, -83.38882000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1008456'),
(77251, 'Puerto Morazán', 954, 'CI', 159, 'NI', 12.85042000, -87.17167000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2605034'),
(77252, 'Quezalguaque', 944, 'LE', 159, 'NI', 12.50683000, -86.90292000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q739841'),
(77253, 'Rivas', 942, 'RI', 159, 'NI', 11.43716000, -85.82632000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1757609'),
(77254, 'Río Blanco', 947, 'MT', 159, 'NI', 12.93435000, -85.22354000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2605369'),
(77255, 'San Carlos', 949, 'SJ', 159, 'NI', 11.12360000, -84.77795000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1020320'),
(77256, 'San Dionisio', 947, 'MT', 159, 'NI', 12.76023000, -85.85018000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647872'),
(77257, 'San Jorge', 942, 'RI', 159, 'NI', 11.45584000, -85.80308000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q956573'),
(77258, 'San José de Bocay', 955, 'JI', 159, 'NI', 13.54204000, -85.53942000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215147'),
(77259, 'San José de Cusmapa', 948, 'MD', 159, 'NI', 13.28841000, -86.65539000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215949'),
(77260, 'San José de los Remates', 946, 'BO', 159, 'NI', 12.59750000, -85.76174000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215558'),
(77261, 'San Juan de Limay', 945, 'ES', 159, 'NI', 13.17603000, -86.61234000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2215462'),
(77262, 'San Juan de Oriente', 953, 'MS', 159, 'NI', 11.90624000, -86.07343000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q384555'),
(77263, 'San Juan de Río Coco', 948, 'MD', 159, 'NI', 13.54476000, -86.16499000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q1647654'),
(77264, 'San Juan del Sur', 942, 'RI', 159, 'NI', 11.25292000, -85.87049000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q240247'),
(77265, 'San Lorenzo', 946, 'BO', 159, 'NI', 12.37830000, -85.66646000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647826'),
(77266, 'San Lucas', 948, 'MD', 159, 'NI', 13.41380000, -86.61110000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647851'),
(77267, 'San Marcos', 950, 'CA', 159, 'NI', 11.90949000, -86.20351000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1013654'),
(77268, 'San Miguelito', 949, 'SJ', 159, 'NI', 11.40255000, -84.89991000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1015364'),
(77269, 'San Rafael del Norte', 955, 'JI', 159, 'NI', 13.21248000, -86.11089000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2604700'),
(77270, 'San Rafael del Sur', 941, 'MN', 159, 'NI', 11.84854000, -86.43839000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q964406'),
(77271, 'San Ramón', 947, 'MT', 159, 'NI', 12.92344000, -85.83898000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2395748'),
(77272, 'San Sebastián de Yalí', 955, 'JI', 159, 'NI', 13.30540000, -86.18641000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215401'),
(77273, 'Santa Lucía', 946, 'BO', 159, 'NI', 12.53262000, -85.71074000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q1647681'),
(77274, 'Santa Rosa del Peñón', 944, 'LE', 159, 'NI', 12.80116000, -86.36994000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2215566'),
(77275, 'Santa Teresa', 950, 'CA', 159, 'NI', 11.74321000, -86.21413000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q21054785'),
(77276, 'Santo Domingo', 940, 'CO', 159, 'NI', 12.26438000, -85.08235000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647902'),
(77277, 'Santo Tomás', 940, 'CO', 159, 'NI', 12.06938000, -85.09059000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q31513084'),
(77278, 'Santo Tomás del Norte', 954, 'CI', 159, 'NI', 13.18610000, -86.92267000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q31513084'),
(77279, 'Siuna', 951, 'AN', 159, 'NI', 13.73321000, -84.77725000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1329302'),
(77280, 'Somotillo', 954, 'CI', 159, 'NI', 13.04387000, -86.90506000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2604179'),
(77281, 'Somoto', 948, 'MD', 159, 'NI', 13.48082000, -86.58208000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1010954'),
(77282, 'Telica', 944, 'LE', 159, 'NI', 12.52200000, -86.85938000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1647817'),
(77283, 'Telpaneca', 948, 'MD', 159, 'NI', 13.53151000, -86.28710000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2216981'),
(77284, 'Terrabona', 947, 'MT', 159, 'NI', 12.73028000, -85.96474000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q664620'),
(77285, 'Teustepe', 946, 'BO', 159, 'NI', 12.42030000, -85.79798000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2216040'),
(77286, 'Ticuantepe', 941, 'MN', 159, 'NI', 12.02263000, -86.20493000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2216951'),
(77287, 'Tipitapa', 941, 'MN', 159, 'NI', 12.19732000, -86.09706000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1365146'),
(77288, 'Tisma', 953, 'MS', 159, 'NI', 12.08194000, -86.01739000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2216452'),
(77289, 'Tola', 942, 'RI', 159, 'NI', 11.43927000, -85.93891000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q942590'),
(77290, 'Totogalpa', 948, 'MD', 159, 'NI', 13.56284000, -86.49254000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2216999'),
(77291, 'Valle San Francisco', 941, 'MN', 159, 'NI', 12.51667000, -86.28333000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q21054823'),
(77292, 'Villa El Carmen', 941, 'MN', 159, 'NI', 11.98009000, -86.50571000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q21054975'),
(77293, 'Villa Sandino', 940, 'CO', 159, 'NI', 12.04830000, -84.99362000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q21054975'),
(77294, 'Waslala', 951, 'AN', 159, 'NI', 13.23333000, -85.38333000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1018767'),
(77295, 'Waspán', 951, 'AN', 159, 'NI', 14.74189000, -83.97170000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2217458'),
(77296, 'Yalagüina', 948, 'MD', 159, 'NI', 13.48383000, -86.49305000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, 'Q2216994'),
(77297, '\'s Gravenmoer', 2623, 'NB', 156, 'NL', 51.65594000, 4.94076000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q131883'),
(77298, '\'s-Gravenland', 2614, 'ZH', 156, 'NL', 51.92336000, 4.55315000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2517974'),
(77299, '\'s-Gravenzande', 2614, 'ZH', 156, 'NL', 52.00167000, 4.16528000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q425786'),
(77300, '\'s-Heerenberg', 2611, 'GE', 156, 'NL', 51.87670000, 6.25877000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q425810'),
(77301, '\'s-Hertogenbosch', 2623, 'NB', 156, 'NL', 51.69917000, 5.30417000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2766547'),
(77302, '\'t Hofke', 2623, 'NB', 156, 'NL', 51.44943000, 5.51926000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2766547'),
(77303, '\'t Zand', 2612, 'NH', 156, 'NL', 52.83667000, 4.75556000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2766547'),
(77304, 'Aagtekerke', 2620, 'ZE', 156, 'NL', 51.54667000, 3.50972000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q300808'),
(77305, 'Aalburg', 2623, 'NB', 156, 'NL', 51.75482000, 5.13156000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3111055'),
(77306, 'Aalden', 2613, 'DR', 156, 'NL', 52.79000000, 6.71806000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2432807'),
(77307, 'Aalsmeer', 2612, 'NH', 156, 'NL', 52.25917000, 4.75972000, '2019-10-05 23:11:41', '2022-05-08 22:22:06', 1, 'Q9897'),
(77309, 'Aalst', 2611, 'GE', 156, 'NL', 51.78250000, 5.12778000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1940675'),
(77310, 'Aalten', 2611, 'GE', 156, 'NL', 51.92500000, 6.58056000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q593220'),
(77311, 'Aardenburg', 2620, 'ZE', 156, 'NL', 51.27333000, 3.44722000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q955'),
(77312, 'Aarle-Rixtel', 2623, 'NB', 156, 'NL', 51.50976000, 5.63839000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2003434'),
(77313, 'Abbekerk', 2612, 'NH', 156, 'NL', 52.73167000, 5.01806000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q969'),
(77314, 'Abcoude', 2610, 'UT', 156, 'NL', 52.27250000, 4.96944000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q969'),
(77315, 'Abcoven', 2623, 'NB', 156, 'NL', 51.52750000, 5.08333000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q969'),
(77316, 'Abdissenbosch', 2615, 'LI', 156, 'NL', 50.91667000, 6.03333000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q969'),
(77317, 'Adegeest', 2614, 'ZH', 156, 'NL', 52.13621000, 4.45249000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q969'),
(77318, 'Aduard', 2617, 'GR', 156, 'NL', 53.25667000, 6.45972000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q612259'),
(77319, 'Afferden', 2611, 'GE', 156, 'NL', 51.88000000, 5.63472000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q612259'),
(77320, 'Akert', 2623, 'NB', 156, 'NL', 51.41407000, 5.55960000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q612259'),
(77321, 'Akkrum', 2622, 'FR', 156, 'NL', 53.05024000, 5.83087000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '********'),
(77322, 'Alblasserdam', 2614, 'ZH', 156, 'NL', 51.86583000, 4.66111000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '********'),
(77323, 'Aldeboarn', 2622, 'FR', 156, 'NL', 53.05000000, 5.90000000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '********'),
(77324, 'Aldlân-Oost', 2622, 'FR', 156, 'NL', 53.18860000, 5.82825000, '2019-10-05 23:11:41', '2020-05-01 17:23:03', 1, '********'),
(77325, 'Alkmaar', 2612, 'NH', 156, 'NL', 52.63167000, 4.74861000, '2019-10-05 23:11:41', '2022-05-08 22:22:06', 1, 'Q972'),
(77326, 'Almelo', 2618, 'OV', 156, 'NL', 52.35667000, 6.66250000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '*********'),
(77327, 'Almere Stad', 2619, 'FL', 156, 'NL', 52.37025000, 5.21413000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '*********'),
(77328, 'Almkerk', 2623, 'NB', 156, 'NL', 51.77083000, 4.95972000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '********'),
(77329, 'Alphen', 2623, 'NB', 156, 'NL', 51.48167000, 4.95833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '********'),
(77330, 'Alphen aan den Rijn', 2614, 'ZH', 156, 'NL', 52.12917000, 4.65546000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '*********'),
(77331, 'Alverna', 2611, 'GE', 156, 'NL', 51.80417000, 5.75972000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, '*********'),
(77332, 'Amby', 2615, 'LI', 156, 'NL', 50.86215000, 5.73226000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1969048'),
(77333, 'America', 2615, 'LI', 156, 'NL', 51.43667000, 5.97917000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q463100'),
(77334, 'Amerongen', 2610, 'UT', 156, 'NL', 52.00250000, 5.45972000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1615400'),
(77335, 'Amersfoort', 2610, 'UT', 156, 'NL', 52.15500000, 5.38750000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q33421377'),
(77336, 'Ammerstol', 2614, 'ZH', 156, 'NL', 51.92750000, 4.80833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2366824'),
(77337, 'Ammerzoden', 2611, 'GE', 156, 'NL', 51.74917000, 5.22083000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2073542'),
(77338, 'Amstelveen', 2612, 'NH', 156, 'NL', 52.30083000, 4.86389000, '2019-10-05 23:11:41', '2022-05-08 22:22:06', 1, 'Q9898'),
(77339, 'Amstenrade', 2615, 'LI', 156, 'NL', 50.93917000, 5.92361000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2844505'),
(77340, 'Amsterdam', 2612, 'NH', 156, 'NL', 52.37403000, 4.88969000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q727'),
(77341, 'Amsterdam-Zuidoost', 2612, 'NH', 156, 'NL', 52.30750000, 4.97222000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q478670'),
(77342, 'Andel', 2623, 'NB', 156, 'NL', 51.78333000, 5.05833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2253787'),
(77343, 'Andelst', 2611, 'GE', 156, 'NL', 51.90833000, 5.72917000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2265087'),
(77344, 'Angelslo', 2613, 'DR', 156, 'NL', 52.78090000, 6.92645000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2265087'),
(77345, 'Angeren', 2611, 'GE', 156, 'NL', 51.91583000, 5.95833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q538054'),
(77346, 'Angerlo', 2611, 'GE', 156, 'NL', 51.99583000, 6.13472000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1055698'),
(77347, 'Ankeveense Rade', 2612, 'NH', 156, 'NL', 52.25891000, 5.10160000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1055698'),
(77348, 'Anklaar', 2611, 'GE', 156, 'NL', 52.23111000, 5.98497000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1055698'),
(77349, 'Annen', 2613, 'DR', 156, 'NL', 53.05750000, 6.71944000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2439040'),
(77350, 'Apeldoorn', 2611, 'GE', 156, 'NL', 52.21000000, 5.96944000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3018561'),
(77351, 'Appelscha', 2622, 'FR', 156, 'NL', 52.95526000, 6.35053000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q620806'),
(77352, 'Appingedam', 2617, 'GR', 156, 'NL', 53.32167000, 6.85833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q620806'),
(77353, 'Arcen', 2615, 'LI', 156, 'NL', 51.47667000, 6.18056000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q631873'),
(77354, 'Arkel', 2614, 'ZH', 156, 'NL', 51.86417000, 4.99444000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1165364'),
(77355, 'Arnemuiden', 2620, 'ZE', 156, 'NL', 51.50167000, 3.67500000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q81183'),
(77356, 'Arnhem', 2611, 'GE', 156, 'NL', 51.98000000, 5.91111000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q49280467'),
(77357, 'Assen', 2613, 'DR', 156, 'NL', 52.99667000, 6.56250000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q49280472'),
(77358, 'Asten', 2623, 'NB', 156, 'NL', 51.40417000, 5.74861000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2022283'),
(77359, 'Augustinusga', 2622, 'FR', 156, 'NL', 53.21785000, 6.16170000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2017681'),
(77360, 'Austerlitz', 2610, 'UT', 156, 'NL', 52.08000000, 5.31528000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2380583'),
(77361, 'Avenhorn', 2612, 'NH', 156, 'NL', 52.61750000, 4.95139000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2338786'),
(77362, 'Axel', 2620, 'ZE', 156, 'NL', 51.26667000, 3.90833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1616240'),
(77363, 'Baalder', 2618, 'OV', 156, 'NL', 52.58579000, 6.65299000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1616240'),
(77364, 'Baambrugge', 2610, 'UT', 156, 'NL', 52.24583000, 4.98889000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q535988'),
(77365, 'Baardwijk', 2623, 'NB', 156, 'NL', 51.69246000, 5.09628000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3025163'),
(77366, 'Baarle-Nassau', 2623, 'NB', 156, 'NL', 51.44750000, 4.92917000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3025163'),
(77367, 'Baarlo', 2615, 'LI', 156, 'NL', 51.33083000, 6.09444000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2701692'),
(77368, 'Baarn', 2610, 'UT', 156, 'NL', 52.21167000, 5.28750000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q33423050'),
(77369, 'Babberich', 2611, 'GE', 156, 'NL', 51.90750000, 6.11111000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2579355'),
(77370, 'Baflo', 2617, 'GR', 156, 'NL', 53.36250000, 6.51389000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q799792'),
(77371, 'Bakel', 2623, 'NB', 156, 'NL', 51.50333000, 5.74028000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1858964'),
(77372, 'Bakenberg', 2611, 'GE', 156, 'NL', 52.00511000, 5.87710000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1858964'),
(77373, 'Bakhuizen', 2622, 'FR', 156, 'NL', 52.86975000, 5.45926000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2192128'),
(77374, 'Bakkeveen', 2622, 'FR', 156, 'NL', 53.08072000, 6.25671000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2389357'),
(77375, 'Balk', 2622, 'FR', 156, 'NL', 52.89756000, 5.57964000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2240991'),
(77376, 'Ballast', 2613, 'DR', 156, 'NL', 52.67218000, 6.73299000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2240991'),
(77377, 'Bangert', 2612, 'NH', 156, 'NL', 52.73582000, 5.18010000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1844935'),
(77378, 'Banholt', 2615, 'LI', 156, 'NL', 50.79000000, 5.80833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q100002'),
(77379, 'Barendrecht', 2614, 'ZH', 156, 'NL', 51.85667000, 4.53472000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q100002'),
(77380, 'Barger-Oosterveld', 2613, 'DR', 156, 'NL', 52.77000000, 6.95833000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2884467'),
(77381, 'Bargeres', 2613, 'DR', 156, 'NL', 52.76152000, 6.88145000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2884467'),
(77382, 'Barneveld', 2611, 'GE', 156, 'NL', 52.14000000, 5.58472000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2001429'),
(77383, 'Bavel', 2623, 'NB', 156, 'NL', 51.56583000, 4.83056000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q1990493'),
(77384, 'Bedum', 2617, 'GR', 156, 'NL', 53.30083000, 6.60278000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2690942'),
(77385, 'Beegden', 2615, 'LI', 156, 'NL', 51.18917000, 5.91944000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q631499'),
(77386, 'Beek', 2623, 'NB', 156, 'NL', 51.52890000, 5.63382000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q631499'),
(77387, 'Beek', 2615, 'LI', 156, 'NL', 50.94083000, 5.79722000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q3972033'),
(77388, 'Beek gem Montferland', 2611, 'GE', 156, 'NL', 51.90667000, 6.18750000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2893665'),
(77389, 'Beekbergen', 2611, 'GE', 156, 'NL', 52.16000000, 5.96389000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2893671'),
(77390, 'Beers', 2623, 'NB', 156, 'NL', 51.72583000, 5.82778000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q954104'),
(77391, 'Beersdal', 2615, 'LI', 156, 'NL', 50.90597000, 5.96440000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q954104'),
(77392, 'Beesd', 2611, 'GE', 156, 'NL', 51.88750000, 5.19167000, '2019-10-05 23:11:41', '2019-10-05 23:11:41', 1, 'Q2219434'),
(77393, 'Beesel', 2615, 'LI', 156, 'NL', 51.26833000, 6.03889000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2219434'),
(77394, 'Beetsterzwaag', 2622, 'FR', 156, 'NL', 53.05914000, 6.07711000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2675898'),
(77395, 'Beilen', 2613, 'DR', 156, 'NL', 52.86333000, 6.51389000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2034045'),
(77396, 'Belcrum', 2623, 'NB', 156, 'NL', 51.59911000, 4.76994000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2034045'),
(77397, 'Belfort', 2615, 'LI', 156, 'NL', 50.84698000, 5.65991000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2034045'),
(77398, 'Bellingwolde', 2617, 'GR', 156, 'NL', 53.11583000, 7.16528000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2181173'),
(77399, 'Beltrum', 2611, 'GE', 156, 'NL', 52.06667000, 6.56389000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2011066'),
(77400, 'Bemmel', 2611, 'GE', 156, 'NL', 51.89167000, 5.89861000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q784364'),
(77401, 'Bennebroek', 2612, 'NH', 156, 'NL', 52.32083000, 4.59861000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q817840'),
(77402, 'Bennekom', 2611, 'GE', 156, 'NL', 51.99833000, 5.67639000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2281003'),
(77403, 'Benthuizen', 2614, 'ZH', 156, 'NL', 52.07750000, 4.54444000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q818270'),
(77404, 'Berg', 2615, 'LI', 156, 'NL', 50.86167000, 5.78333000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q100138'),
(77405, 'Berg en Bos', 2611, 'GE', 156, 'NL', 52.22009000, 5.93340000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q100138'),
(77406, 'Berg en Dal', 2611, 'GE', 156, 'NL', 51.82167000, 5.91667000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2116037'),
(77407, 'Bergeijk', 2623, 'NB', 156, 'NL', 51.30182000, 5.33946000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q9812'),
(77408, 'Bergen op Zoom', 2623, 'NB', 156, 'NL', 51.49500000, 4.29167000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q192508'),
(77409, 'Bergharen', 2611, 'GE', 156, 'NL', 51.85083000, 5.66944000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2084100'),
(77410, 'Berghem', 2623, 'NB', 156, 'NL', 51.76991000, 5.56827000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2042013'),
(77411, 'Bergschenhoek', 2614, 'ZH', 156, 'NL', 51.99000000, 4.49861000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q820175'),
(77412, 'Bergstoep', 2614, 'ZH', 156, 'NL', 51.92250000, 4.78472000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2717671'),
(77413, 'Beringe', 2615, 'LI', 156, 'NL', 51.33667000, 5.94861000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1878867'),
(77414, 'Berkel en Rodenrijs', 2614, 'ZH', 156, 'NL', 51.99313000, 4.47865000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q820839'),
(77415, 'Berkelland', 2611, 'GE', 156, 'NL', 52.10606000, 6.56748000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q47104'),
(77416, 'Berkenwoude', 2614, 'ZH', 156, 'NL', 51.94500000, 4.70694000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2897199'),
(77417, 'Berkhout', 2612, 'NH', 156, 'NL', 52.64083000, 5.00139000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2272260'),
(77418, 'Berkum', 2618, 'OV', 156, 'NL', 52.52395000, 6.13655000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2272260'),
(77419, 'Berlicum', 2623, 'NB', 156, 'NL', 51.67750000, 5.40000000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2897233'),
(77420, 'Berltsum', 2622, 'FR', 156, 'NL', 53.24370000, 5.65101000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1107931'),
(77421, 'Besoijen', 2623, 'NB', 156, 'NL', 51.68333000, 5.05000000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1107931'),
(77422, 'Best', 2623, 'NB', 156, 'NL', 51.50750000, 5.39028000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1107931'),
(77423, 'Besterd', 2623, 'NB', 156, 'NL', 51.56380000, 5.08658000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1107931'),
(77424, 'Beuningen', 2611, 'GE', 156, 'NL', 51.86083000, 5.76667000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q33424703'),
(77425, 'Beusichem', 2611, 'GE', 156, 'NL', 51.95000000, 5.29167000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1879468'),
(77426, 'Beverwijk', 2612, 'NH', 156, 'NL', 52.48333000, 4.65694000, '2019-10-05 23:11:42', '2022-05-08 22:22:07', 1, 'Q9905'),
(77427, 'Biddinghuizen', 2619, 'FL', 156, 'NL', 52.45500000, 5.69306000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q599331'),
(77428, 'Biesdonk', 2623, 'NB', 156, 'NL', 51.60613000, 4.78515000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q599331'),
(77429, 'Biesland', 2615, 'LI', 156, 'NL', 50.84110000, 5.67573000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2380712'),
(77430, 'Bijvanck', 2612, 'NH', 156, 'NL', 52.28585000, 5.26534000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2380712'),
(77431, 'Bilgaard', 2622, 'FR', 156, 'NL', 53.21551000, 5.79574000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2380712'),
(77432, 'Binnenhof', 2614, 'ZH', 156, 'NL', 52.16418000, 4.53644000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2380712'),
(77433, 'Binnenstad', 2611, 'GE', 156, 'NL', 52.21304000, 5.95957000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2380712'),
(77434, 'Bitswijk', 2623, 'NB', 156, 'NL', 51.66870000, 5.60921000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2380712'),
(77435, 'Blaarthem', 2623, 'NB', 156, 'NL', 51.42501000, 5.45784000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2380712'),
(77436, 'Bladel', 2623, 'NB', 156, 'NL', 51.36833000, 5.22083000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1899862'),
(77437, 'Blaricum', 2612, 'NH', 156, 'NL', 52.27250000, 5.24167000, '2019-10-05 23:11:42', '2022-05-08 22:22:07', 1, 'Q9906'),
(77438, 'Bleijerheide', 2615, 'LI', 156, 'NL', 50.85538000, 6.06789000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1899862'),
(77439, 'Bleiswijk', 2614, 'ZH', 156, 'NL', 52.01083000, 4.53194000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q883690'),
(77440, 'Bleskensgraaf', 2614, 'ZH', 156, 'NL', 51.87250000, 4.78333000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2109338'),
(77441, 'Blijham', 2617, 'GR', 156, 'NL', 53.10917000, 7.07639000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2109338'),
(77442, 'Blitterswijck', 2615, 'LI', 156, 'NL', 51.53083000, 6.10833000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2906656'),
(77443, 'Bloemendaal', 2612, 'NH', 156, 'NL', 52.40250000, 4.62222000, '2019-10-05 23:11:42', '2022-05-08 22:22:07', 1, 'Q9908'),
(77444, 'Bloemendaal', 2614, 'ZH', 156, 'NL', 52.02878000, 4.69440000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2086096'),
(77445, 'Bloemhof', 2614, 'ZH', 156, 'NL', 51.89723000, 4.49943000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2149295'),
(77446, 'Blokzijl', 2618, 'OV', 156, 'NL', 52.72667000, 5.96111000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q59605'),
(77447, 'Bocholtz', 2615, 'LI', 156, 'NL', 50.81833000, 6.00556000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2217726'),
(77448, 'Bodegraven', 2614, 'ZH', 156, 'NL', 52.08250000, 4.75000000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q832938'),
(77449, 'Bodegraven-Reeuwijk', 2614, 'ZH', 156, 'NL', 52.06541000, 4.76634000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q889423'),
(77450, 'Boekel', 2623, 'NB', 156, 'NL', 51.60333000, 5.67500000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q13441242'),
(77451, 'Bolnes', 2614, 'ZH', 156, 'NL', 51.89500000, 4.57917000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2039640'),
(77452, 'Bolsward', 2622, 'FR', 156, 'NL', 53.06555000, 5.53176000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q329582'),
(77453, 'Boornbergum', 2622, 'FR', 156, 'NL', 53.08284000, 6.04578000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2209207'),
(77454, 'Borculo', 2611, 'GE', 156, 'NL', 52.11583000, 6.52222000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1025685'),
(77455, 'Borgele', 2618, 'OV', 156, 'NL', 52.27630000, 6.14926000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1025685'),
(77456, 'Borger', 2613, 'DR', 156, 'NL', 52.92333000, 6.79306000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2301825'),
(77457, 'Borgharen', 2615, 'LI', 156, 'NL', 50.87750000, 5.68750000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q262725'),
(77458, 'Born', 2615, 'LI', 156, 'NL', 51.03167000, 5.80972000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q502343'),
(77459, 'Borne', 2618, 'OV', 156, 'NL', 52.30136000, 6.74820000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q502343'),
(77460, 'Borssele', 2620, 'ZE', 156, 'NL', 51.42333000, 3.73472000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2911365'),
(77461, 'Bosch en Duin', 2610, 'UT', 156, 'NL', 52.11667000, 5.24167000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2911449'),
(77462, 'Boskamp', 2618, 'OV', 156, 'NL', 52.33083000, 6.12778000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q508326'),
(77463, 'Boskoop', 2614, 'ZH', 156, 'NL', 52.07500000, 4.65556000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q508326'),
(77464, 'Bosschenhoofd', 2623, 'NB', 156, 'NL', 51.56083000, 4.54028000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2911564'),
(77465, 'Boven-Hardinxveld', 2614, 'ZH', 156, 'NL', 51.82333000, 4.88194000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2692102'),
(77466, 'Boxmeer', 2623, 'NB', 156, 'NL', 51.64667000, 5.94722000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q12012490'),
(77467, 'Boxtel', 2623, 'NB', 156, 'NL', 51.59083000, 5.32917000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q13443096'),
(77468, 'Brabander', 2615, 'LI', 156, 'NL', 51.53583000, 5.96806000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q13443096'),
(77469, 'Brachterbeek', 2615, 'LI', 156, 'NL', 51.14694000, 5.90446000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q13443096'),
(77470, 'Brakel', 2611, 'GE', 156, 'NL', 51.81750000, 5.09028000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2916660'),
(77471, 'Brakkenstein', 2611, 'GE', 156, 'NL', 51.81324000, 5.86539000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2916660'),
(77472, 'Brand', 2623, 'NB', 156, 'NL', 51.45839000, 5.62427000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2916660'),
(77473, 'Breda', 2623, 'NB', 156, 'NL', 51.58656000, 4.77596000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q40844'),
(77474, 'Bredevoort', 2611, 'GE', 156, 'NL', 51.94167000, 6.62083000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q73501'),
(77475, 'Bredeweg', 2611, 'GE', 156, 'NL', 51.76019000, 5.94189000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q73501'),
(77476, 'Breezand', 2612, 'NH', 156, 'NL', 52.89000000, 4.80417000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q940668'),
(77477, 'Breskens', 2620, 'ZE', 156, 'NL', 51.39583000, 3.55556000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q641390'),
(77478, 'Breugel', 2623, 'NB', 156, 'NL', 51.51750000, 5.51111000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1981676'),
(77479, 'Breukelen', 2610, 'UT', 156, 'NL', 52.17417000, 5.00139000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q615732'),
(77480, 'Brielle', 2614, 'ZH', 156, 'NL', 51.90167000, 4.16250000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q33426218'),
(77481, 'Brinkhorst', 2611, 'GE', 156, 'NL', 52.21302000, 5.95167000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q33426218'),
(77482, 'Broek in Waterland', 2612, 'NH', 156, 'NL', 52.43417000, 4.99583000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2925962'),
(77483, 'Broek op Langedijk', 2612, 'NH', 156, 'NL', 52.67417000, 4.80556000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2464635'),
(77484, 'Broekhem', 2615, 'LI', 156, 'NL', 50.87119000, 5.82069000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q100115'),
(77485, 'Broekhoven', 2623, 'NB', 156, 'NL', 51.54801000, 5.09175000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q100115'),
(77486, 'Broeksittard', 2615, 'LI', 156, 'NL', 51.00290000, 5.89511000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2925966'),
(77487, 'Broeksterwâld', 2622, 'FR', 156, 'NL', 53.27466000, 5.99648000, '2019-10-05 23:11:42', '2020-05-01 17:23:03', 1, 'Q2594220'),
(77488, 'Brouwershaven', 2620, 'ZE', 156, 'NL', 51.72667000, 3.91250000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q991009'),
(77489, 'Bruchem', 2611, 'GE', 156, 'NL', 51.78667000, 5.23611000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q991951'),
(77490, 'Bruinisse', 2620, 'ZE', 156, 'NL', 51.66167000, 4.09444000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q992371'),
(77491, 'Brukske', 2615, 'LI', 156, 'NL', 51.52101000, 5.99270000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q23046156'),
(77492, 'Brummelhof', 2611, 'GE', 156, 'NL', 52.20503000, 5.96789000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q23046156'),
(77493, 'Brummen', 2611, 'GE', 156, 'NL', 52.09000000, 6.15556000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q16069702'),
(77494, 'Brunnepe', 2618, 'OV', 156, 'NL', 52.56185000, 5.90343000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q16069702'),
(77495, 'Brunssum', 2615, 'LI', 156, 'NL', 50.94667000, 5.97083000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q33426662'),
(77496, 'Buchten', 2615, 'LI', 156, 'NL', 51.04333000, 5.80972000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q33426662'),
(77497, 'Budel', 2623, 'NB', 156, 'NL', 51.27167000, 5.57500000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1001369'),
(77498, 'Budel-Dorplein', 2623, 'NB', 156, 'NL', 51.23667000, 5.58750000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2165583'),
(77499, 'Budel-Schoot', 2623, 'NB', 156, 'NL', 51.24750000, 5.56528000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2165583'),
(77500, 'Budschop', 2615, 'LI', 156, 'NL', 51.28511000, 5.75898000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2165583'),
(77501, 'Buitenpost', 2622, 'FR', 156, 'NL', 53.25166000, 6.14483000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q181500'),
(77502, 'Bunde', 2615, 'LI', 156, 'NL', 50.89667000, 5.73194000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1005360'),
(77503, 'Bunnik', 2610, 'UT', 156, 'NL', 52.06667000, 5.19861000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q937679'),
(77504, 'Bunschoten', 2610, 'UT', 156, 'NL', 52.24304000, 5.37884000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q937679'),
(77505, 'Burdaard', 2622, 'FR', 156, 'NL', 53.29421000, 5.87897000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2049915'),
(77506, 'Burgemeesterswijk', 2611, 'GE', 156, 'NL', 51.98943000, 5.89597000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2049915'),
(77507, 'Burgum', 2622, 'FR', 156, 'NL', 53.19243000, 5.99009000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1016068'),
(77508, 'Bussum', 2612, 'NH', 156, 'NL', 52.27333000, 5.16111000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q9909'),
(77509, 'Callantsoog', 2612, 'NH', 156, 'NL', 52.84000000, 4.69583000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q991661'),
(77510, 'Camminghaburen', 2622, 'FR', 156, 'NL', 53.20973000, 5.84318000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2345784'),
(77511, 'Capelle aan den IJssel', 2614, 'ZH', 156, 'NL', 51.92917000, 4.57778000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2345784'),
(77512, 'Capelle-West', 2614, 'ZH', 156, 'NL', 51.91667000, 4.56667000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1967376'),
(77513, 'Carnisse', 2614, 'ZH', 156, 'NL', 51.88932000, 4.47758000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1967376'),
(77514, 'Castricum', 2612, 'NH', 156, 'NL', 52.54833000, 4.66944000, '2019-10-05 23:11:42', '2022-05-08 22:22:07', 1, 'Q9910'),
(77515, 'Chaam', 2623, 'NB', 156, 'NL', 51.50583000, 4.86111000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2369076'),
(77516, 'Chevremont', 2615, 'LI', 156, 'NL', 50.87554000, 6.05981000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2369076'),
(77517, 'Coendersborg', 2617, 'GR', 156, 'NL', 53.19410000, 6.58944000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2369076'),
(77518, 'Coevering', 2623, 'NB', 156, 'NL', 51.41742000, 5.57463000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2369076'),
(77519, 'Coevorden', 2613, 'DR', 156, 'NL', 52.66103000, 6.74046000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2369076'),
(77520, 'Colijnsplaat', 2620, 'ZE', 156, 'NL', 51.59917000, 3.84861000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q538319'),
(77521, 'Commandeurs', 2612, 'NH', 156, 'NL', 52.50902000, 4.65843000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q538319'),
(77522, 'Componistenkwartier', 2611, 'GE', 156, 'NL', 52.18977000, 5.96609000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q538319'),
(77523, 'Corlaer', 2611, 'GE', 156, 'NL', 52.21639000, 5.46535000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q538319'),
(77524, 'Cothen', 2610, 'UT', 156, 'NL', 51.99667000, 5.30833000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2860067'),
(77525, 'Cranendonck', 2623, 'NB', 156, 'NL', 51.30417000, 5.58889000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2860067'),
(77526, 'Cuijk', 2623, 'NB', 156, 'NL', 51.73083000, 5.87917000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q13635419'),
(77527, 'Culemborg', 2611, 'GE', 156, 'NL', 51.95500000, 5.22778000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q13635419'),
(77528, 'Dalen', 2613, 'DR', 156, 'NL', 52.69917000, 6.75556000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q1974246'),
(77529, 'Dalfsen', 2618, 'OV', 156, 'NL', 52.51167000, 6.25694000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q15644904'),
(77530, 'Damwâld', 2622, 'FR', 156, 'NL', 53.29046000, 5.99785000, '2019-10-05 23:11:42', '2020-05-01 17:23:03', 1, 'Q2272796'),
(77531, 'Dauwendaele', 2620, 'ZE', 156, 'NL', 51.49299000, 3.62624000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2272796'),
(77532, 'De Bilt', 2610, 'UT', 156, 'NL', 52.11000000, 5.18056000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2272796'),
(77533, 'De Blaak', 2623, 'NB', 156, 'NL', 51.54626000, 5.04465000, '2019-10-05 23:11:42', '2019-10-05 23:11:42', 1, 'Q2272796');

