INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(69994, 'El Huérfano', 3464, 'VER', 142, 'MX', 19.74082000, -96.74528000, '2019-10-05 23:08:39', '2020-05-01 17:23:02', 1, 'Q5822143'),
(69995, 'El Jabalí', 3461, 'SLP', 142, 'MX', 21.88431000, -100.05315000, '2019-10-05 23:08:39', '2020-05-01 17:23:02', 1, 'Q5822143'),
(69996, 'El Jacal', 3450, 'MEX', 142, 'MX', 19.40500000, -100.08944000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238098'),
(69997, '<PERSON>', 3469, '<PERSON><PERSON><PERSON>', 142, 'M<PERSON>', 20.91499000, -101.63426000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238098'),
(69998, 'El Jagüey', 3470, 'HID', 142, 'MX', 20.24691000, -99.61516000, '2019-10-05 23:08:39', '2020-05-01 17:23:00', 1, 'Q20238098'),
(69999, 'El Jaral', 3455, 'QUE', 142, 'MX', 20.40647000, -100.45524000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238098'),
(70000, 'El Jaralito', 3461, 'SLP', 142, 'MX', 22.03851000, -100.82119000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238098'),
(70001, 'El Jardín', 3451, 'CHP', 142, 'MX', 17.16657000, -92.59933000, '2019-10-05 23:08:39', '2020-05-01 17:22:59', 1, 'Q20238098'),
(70002, 'El Jazmín', 3455, 'QUE', 142, 'MX', 20.34444000, -100.04667000, '2019-10-05 23:08:39', '2020-05-01 17:23:02', 1, 'Q20239362'),
(70003, 'El Jiadi', 3470, 'HID', 142, 'MX', 20.24842000, -98.92815000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q50010759'),
(70004, 'El Jicaral', 3459, 'GRO', 142, 'MX', 17.12617000, -98.19697000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20230841'),
(70005, 'El Jicote', 3477, 'NAY', 142, 'MX', 21.72545000, -105.05787000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20230841'),
(70006, 'El Jobo', 3464, 'VER', 142, 'MX', 20.01405000, -97.16461000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20230841'),
(70007, 'El Jobo', 3451, 'CHP', 142, 'MX', 16.70369000, -93.10569000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20230841'),
(70008, 'El Juile', 3464, 'VER', 142, 'MX', 17.74412000, -94.98946000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20230841'),
(70009, 'El Lampotal', 3462, 'ZAC', 142, 'MX', 22.89583000, -102.41944000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20220807'),
(70010, 'El Laurel', 3464, 'VER', 142, 'MX', 18.27409000, -95.32900000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20220807'),
(70011, 'El Lencero', 3464, 'VER', 142, 'MX', 19.48897000, -96.81622000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20220807'),
(70012, 'El Lequeitio', 3471, 'COA', 142, 'MX', 25.84673000, -103.27890000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20220807'),
(70013, 'El Letrero', 3474, 'MIC', 142, 'MX', 18.96339000, -102.11025000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20220807'),
(70014, 'El Limar', 3451, 'CHP', 142, 'MX', 17.41491000, -92.40293000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20279584'),
(70015, 'El Limonar', 3451, 'CHP', 142, 'MX', 16.97026000, -91.37820000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20213072'),
(70016, 'El Limón', 3463, 'TAM', 142, 'MX', 22.82498000, -99.00512000, '2019-10-05 23:08:39', '2020-05-01 17:23:02', 1, 'Q20213072'),
(70017, 'El Limón', 3459, 'GRO', 142, 'MX', 16.99556000, -99.37750000, '2019-10-05 23:08:39', '2020-05-01 17:23:00', 1, 'Q20279629'),
(70018, 'El Limón de los Ramos', 3449, 'SIN', 142, 'MX', 24.91361000, -107.52333000, '2019-10-05 23:08:39', '2020-05-01 17:23:02', 1, 'Q20279595'),
(70019, 'El Lindero', 3464, 'VER', 142, 'MX', 21.31278000, -98.22000000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20223530'),
(70020, 'El Llanito', 3469, 'GUA', 142, 'MX', 21.12143000, -100.95110000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20223530'),
(70021, 'El Llano', 3470, 'HID', 142, 'MX', 20.06537000, -99.31976000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20242016'),
(70022, 'El Llano', 3477, 'NAY', 142, 'MX', 21.41854000, -105.17963000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20242016'),
(70023, 'El Llano', 3456, 'AGU', 142, 'MX', 21.91667000, -101.96666000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q2474668'),
(70024, 'El Llano Santa María', 3450, 'MEX', 142, 'MX', 19.78389000, -99.08639000, '2019-10-05 23:08:39', '2020-05-01 17:23:00', 1, 'Q20238123'),
(70025, 'El Llano del Compromiso', 3450, 'MEX', 142, 'MX', 19.25472000, -99.47917000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70026, 'El Lobo', 3455, 'QUE', 142, 'MX', 20.72904000, -100.20226000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70027, 'El Lucero', 3453, 'DUR', 142, 'MX', 25.87830000, -103.40209000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70028, 'El Maguey', 3464, 'VER', 142, 'MX', 18.83295000, -96.73434000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70029, 'El Maguey', 3469, 'GUA', 142, 'MX', 20.98035000, -101.84651000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70030, 'El Magueyal', 3450, 'MEX', 142, 'MX', 19.94250000, -99.57462000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70031, 'El Maluco', 3474, 'MIC', 142, 'MX', 20.14434000, -101.67528000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70032, 'El Mango', 3451, 'CHP', 142, 'MX', 17.16236000, -92.05985000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238120'),
(70033, 'El Mante', 3463, 'TAM', 142, 'MX', 22.67241000, -98.93820000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q2000714'),
(70034, 'El Mesón', 3459, 'GRO', 142, 'MX', 16.84570000, -99.05868000, '2019-10-05 23:08:39', '2020-05-01 17:23:00', 1, 'Q2000714'),
(70035, 'El Metlapil', 3459, 'GRO', 142, 'MX', 16.84167000, -99.74889000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206792'),
(70036, 'El Mezquitillo', 3469, 'GUA', 142, 'MX', 20.96213000, -101.80857000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206792'),
(70037, 'El Milagro', 3455, 'QUE', 142, 'MX', 20.47429000, -100.34918000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206792'),
(70038, 'El Mirador', 3464, 'VER', 142, 'MX', 21.01168000, -97.94387000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20206792'),
(70039, 'El Mirador', 3465, 'MOR', 142, 'MX', 18.77139000, -99.34139000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20235891'),
(70040, 'El Mirador', 3470, 'HID', 142, 'MX', 20.03000000, -98.81056000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20279884'),
(70041, 'El Mirador', 3476, 'PUE', 142, 'MX', 19.55694000, -97.77194000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20229847'),
(70042, 'El Mogote', 3450, 'MEX', 142, 'MX', 20.16583000, -99.91444000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238151'),
(70043, 'El Molino', 3464, 'VER', 142, 'MX', 21.91602000, -98.29885000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238151'),
(70044, 'El Molino', 3448, 'OAX', 142, 'MX', 17.77536000, -97.75227000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238151'),
(70045, 'El Molino', 3447, 'CHH', 142, 'MX', 28.16608000, -105.53783000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238151'),
(70046, 'El Molino', 3449, 'SIN', 142, 'MX', 24.57147000, -107.66483000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20238151'),
(70047, 'El Molino', 3476, 'PUE', 142, 'MX', 19.81333000, -97.58028000, '2019-10-05 23:08:39', '2019-10-05 23:08:39', 1, 'Q20229864'),
(70048, 'El Mollejon', 3464, 'VER', 142, 'MX', 20.54222000, -97.41111000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20223726'),
(70049, 'El Moral', 3469, 'GUA', 142, 'MX', 20.13030000, -101.28862000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20223726'),
(70050, 'El Moreno (San Miguel Moreno)', 3470, 'HID', 142, 'MX', 20.30139000, -99.17361000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20242065'),
(70051, 'El Morro', 3448, 'OAX', 142, 'MX', 16.09745000, -95.37906000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20242065'),
(70052, 'El Nabo', 3455, 'QUE', 142, 'MX', 20.69389000, -100.48028000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20246282'),
(70053, 'El Nacimiento', 3469, 'GUA', 142, 'MX', 20.53596000, -100.61017000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20246282'),
(70054, 'El Naranjillo', 3469, 'GUA', 142, 'MX', 20.71417000, -101.00056000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20206594'),
(70055, 'El Naranjito', 3459, 'GRO', 142, 'MX', 17.98801000, -102.15752000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20206594'),
(70056, 'El Naranjito', 3464, 'VER', 142, 'MX', 17.99785000, -94.60666000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20206594'),
(70057, 'El Naranjo', 3461, 'SLP', 142, 'MX', 22.52597000, -99.32968000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20206594'),
(70058, 'El Naranjo', 3475, 'CAM', 142, 'MX', 18.02193000, -91.12654000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20206594'),
(70059, 'El Naranjo', 3472, 'COL', 142, 'MX', 19.13016000, -104.41821000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20206594'),
(70060, 'El Nayar', 3453, 'DUR', 142, 'MX', 23.96422000, -104.69541000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20206594'),
(70061, 'El Nigromante', 3462, 'ZAC', 142, 'MX', 22.15373000, -101.71056000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20220862'),
(70062, 'El Nigromante', 3464, 'VER', 142, 'MX', 17.76472000, -95.75722000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20220862'),
(70063, 'El Nilo', 3471, 'COA', 142, 'MX', 25.73363000, -102.94297000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20220862'),
(70064, 'El Nio', 3449, 'SIN', 142, 'MX', 25.62455000, -108.40029000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20220862'),
(70065, 'El Nith', 3470, 'HID', 142, 'MX', 20.48778000, -99.19194000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20242086'),
(70066, 'El Niño', 3457, 'BCN', 142, 'MX', 32.50750000, -116.79194000, '2019-10-05 23:08:40', '2020-05-01 17:22:59', 1, 'Q20280003'),
(70067, 'El Nopal', 3451, 'CHP', 142, 'MX', 16.91161000, -92.84471000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213306'),
(70068, 'El Obraje', 3462, 'ZAC', 142, 'MX', 22.18059000, -101.59057000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213306'),
(70069, 'El Obraje', 3450, 'MEX', 142, 'MX', 19.72269000, -99.96259000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280048'),
(70070, 'El Ocotal', 3450, 'MEX', 142, 'MX', 19.67983000, -99.47008000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280048'),
(70071, 'El Ocotito', 3459, 'GRO', 142, 'MX', 17.24603000, -99.51525000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280061'),
(70072, 'El Olvera', 3470, 'HID', 142, 'MX', 20.27000000, -99.02556000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20252081'),
(70073, 'El Organal', 3455, 'QUE', 142, 'MX', 20.47751000, -100.05457000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20252081'),
(70074, 'El Oro', 3447, 'CHH', 142, 'MX', 26.86320000, -105.84838000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q38241853'),
(70075, 'El Oro de Hidalgo', 3450, 'MEX', 142, 'MX', 19.80272000, -100.13081000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q2115222'),
(70076, 'El Pacayal', 3451, 'CHP', 142, 'MX', 15.59849000, -92.04254000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q2115222'),
(70077, 'El Palacio', 3450, 'MEX', 142, 'MX', 19.35139000, -99.35667000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238188'),
(70078, 'El Palmar', 3455, 'QUE', 142, 'MX', 20.69347000, -99.70645000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238188'),
(70079, 'El Palmar', 3464, 'VER', 142, 'MX', 18.80330000, -96.66560000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238188'),
(70080, 'El Palmar', 3476, 'PUE', 142, 'MX', 19.13972000, -97.05306000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20252288'),
(70081, 'El Palmar (San Gabriel)', 3451, 'CHP', 142, 'MX', 16.84556000, -93.01639000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280117'),
(70082, 'El Palmar Grande', 3451, 'CHP', 142, 'MX', 15.49858000, -92.25518000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280117'),
(70083, 'El Palmarcito', 3451, 'CHP', 142, 'MX', 15.49728000, -93.21387000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213439'),
(70084, 'El Palmito', 3450, 'MEX', 142, 'MX', 19.91733000, -99.68127000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213439'),
(70085, 'El Panorama', 3464, 'VER', 142, 'MX', 20.28918000, -97.63554000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213439'),
(70086, 'El Papayo', 3459, 'GRO', 142, 'MX', 17.03468000, -100.24788000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213439'),
(70087, 'El Paracho', 3474, 'MIC', 142, 'MX', 20.10819000, -102.52272000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213439'),
(70088, 'El Paraíso', 3464, 'VER', 142, 'MX', 17.51592000, -95.14271000, '2019-10-05 23:08:40', '2020-05-01 17:23:02', 1, 'Q20223959'),
(70089, 'El Paraíso', 3455, 'QUE', 142, 'MX', 20.56402000, -100.21617000, '2019-10-05 23:08:40', '2020-05-01 17:23:02', 1, 'Q20223959'),
(70090, 'El Paraíso', 3459, 'GRO', 142, 'MX', 17.34361000, -100.22806000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20223959'),
(70091, 'El Paraíso', 3451, 'CHP', 142, 'MX', 17.24639000, -92.55694000, '2019-10-05 23:08:40', '2020-05-01 17:22:59', 1, 'Q20252730'),
(70092, 'El Paraíso (La Charca)', 3464, 'VER', 142, 'MX', 19.42889000, -96.35500000, '2019-10-05 23:08:40', '2020-05-01 17:23:02', 1, 'Q20223973'),
(70093, 'El Paredoncito', 3468, 'SON', 142, 'MX', 27.05920000, -109.91286000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20261820'),
(70094, 'El Paredón', 3470, 'HID', 142, 'MX', 19.86686000, -98.25224000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20261820'),
(70095, 'El Parral', 3451, 'CHP', 142, 'MX', 16.37035000, -93.00567000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20261820'),
(70096, 'El Pedregal', 3474, 'MIC', 142, 'MX', 19.51083000, -101.44917000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238034'),
(70097, 'El Pedregal de Guadalupe Hidalgo', 3450, 'MEX', 142, 'MX', 19.25278000, -99.46500000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20253066'),
(70098, 'El Pericón', 3459, 'GRO', 142, 'MX', 16.98001000, -99.32573000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20253066'),
(70099, 'El Perú', 3471, 'COA', 142, 'MX', 25.60290000, -103.34423000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20253066'),
(70100, 'El Pescadero', 3460, 'BCS', 142, 'MX', 23.36417000, -110.16833000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q4359610'),
(70101, 'El Peñasco', 3461, 'SLP', 142, 'MX', 22.32534000, -100.95014000, '2019-10-05 23:08:40', '2020-05-01 17:23:02', 1, 'Q4359610'),
(70102, 'El Picacho', 3469, 'GUA', 142, 'MX', 20.71016000, -100.63704000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q4359610'),
(70103, 'El Pilar', 3450, 'MEX', 142, 'MX', 20.24201000, -101.40209000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q4359610'),
(70104, 'El Pino', 3450, 'MEX', 142, 'MX', 19.35139000, -98.94833000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238228'),
(70105, 'El Pinto', 3455, 'QUE', 142, 'MX', 20.80270000, -100.41096000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238228'),
(70106, 'El Pintor', 3470, 'HID', 142, 'MX', 21.15932000, -98.41284000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238228'),
(70107, 'El Pitahayo', 3459, 'GRO', 142, 'MX', 16.53236000, -98.52760000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20253523'),
(70108, 'El Pital', 3464, 'VER', 142, 'MX', 20.15695000, -96.89805000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20253523'),
(70109, 'El Plan', 3450, 'MEX', 142, 'MX', 19.37452000, -99.80049000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238230'),
(70110, 'El Platanal', 3474, 'MIC', 142, 'MX', 20.06200000, -102.57779000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238230'),
(70111, 'El Plateado de Joaquín Amaro', 3462, 'ZAC', 142, 'MX', 21.93622000, -103.09267000, '2019-10-05 23:08:40', '2020-05-01 17:23:03', 1, 'Q3847453'),
(70112, 'El Pochotal', 3449, 'SIN', 142, 'MX', 25.99541000, -108.84355000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q3847453'),
(70113, 'El Polvorín', 3459, 'GRO', 142, 'MX', 16.57833000, -98.80139000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20280373'),
(70114, 'El Portal', 3451, 'CHP', 142, 'MX', 15.67726000, -92.09588000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280373'),
(70115, 'El Porvenir', 3448, 'OAX', 142, 'MX', 17.47446000, -95.25596000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280373'),
(70116, 'El Porvenir', 3457, 'BCN', 142, 'MX', 32.07606000, -116.62473000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280373'),
(70117, 'El Porvenir', 3464, 'VER', 142, 'MX', 18.92083000, -96.92583000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20224095'),
(70118, 'El Porvenir', 3451, 'CHP', 142, 'MX', 15.43092000, -92.26447000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q3288000'),
(70119, 'El Porvenir (El Porvenir de Arriba)', 3471, 'COA', 142, 'MX', 25.77444000, -103.32528000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20253878'),
(70120, 'El Porvenir Agrarista', 3451, 'CHP', 142, 'MX', 16.16465000, -91.83575000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20253878'),
(70121, 'El Porvenir I', 3450, 'MEX', 142, 'MX', 19.31694000, -99.72889000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238235'),
(70122, 'El Porvenir de Velasco Suárez', 3451, 'CHP', 142, 'MX', 15.45695000, -92.27987000, '2019-10-05 23:08:40', '2020-05-01 17:22:59', 1, 'Q27769762'),
(70123, 'El Potrerillo (Potrerillo del Rincón)', 3459, 'GRO', 142, 'MX', 16.95806000, -98.73083000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20254039'),
(70124, 'El Potrero', 3450, 'MEX', 142, 'MX', 19.30625000, -99.99393000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20254039'),
(70125, 'El Potrero', 3464, 'VER', 142, 'MX', 18.67822000, -97.25499000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20254039'),
(70126, 'El Potrero de San Diego', 3450, 'MEX', 142, 'MX', 19.48899000, -100.08210000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20254039'),
(70127, 'El Potrero de Sataya', 3449, 'SIN', 142, 'MX', 24.69000000, -107.70917000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20254087'),
(70128, 'El Pozo', 3451, 'CHP', 142, 'MX', 16.92034000, -92.41166000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20254249'),
(70129, 'El Pozo', 3455, 'QUE', 142, 'MX', 20.63750000, -100.33306000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20246299'),
(70130, 'El Pozole', 3449, 'SIN', 142, 'MX', 22.91472000, -105.91333000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280463'),
(70131, 'El Progreso', 3449, 'SIN', 142, 'MX', 25.43104000, -108.59195000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280463'),
(70132, 'El Progreso', 3476, 'PUE', 142, 'MX', 20.01917000, -97.36472000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20229982'),
(70133, 'El Progreso', 3451, 'CHP', 142, 'MX', 16.87694000, -93.22222000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280490'),
(70134, 'El Progreso', 3464, 'VER', 142, 'MX', 20.11417000, -97.01444000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20224148'),
(70135, 'El Progreso Hidalgo', 3450, 'MEX', 142, 'MX', 18.84639000, -99.61417000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20238245'),
(70136, 'El Pueblito', 3455, 'QUE', 142, 'MX', 20.53996000, -100.43817000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q1962054'),
(70137, 'El Pueblito', 3464, 'VER', 142, 'MX', 18.88639000, -96.96946000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q1962054'),
(70138, 'El Pueblito (Garbanzal)', 3464, 'VER', 142, 'MX', 19.59389000, -96.92417000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20224156'),
(70139, 'El Puerto', 3451, 'CHP', 142, 'MX', 16.42575000, -92.43059000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20224156'),
(70140, 'El Puerto Magú', 3450, 'MEX', 142, 'MX', 19.68846000, -99.37290000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20224156'),
(70141, 'El Puesto', 3469, 'GUA', 142, 'MX', 20.46890000, -100.82763000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20224156'),
(70142, 'El Quelite', 3449, 'SIN', 142, 'MX', 23.55897000, -106.46738000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20224156'),
(70143, 'El Ramal (Porvenir)', 3451, 'CHP', 142, 'MX', 15.86583000, -92.94556000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213833'),
(70144, 'El Rayo', 3453, 'DUR', 142, 'MX', 25.52112000, -103.60821000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213833'),
(70145, 'El Realito', 3463, 'TAM', 142, 'MX', 25.66528000, -97.87540000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20213833'),
(70146, 'El Recodo', 3468, 'SON', 142, 'MX', 27.07722000, -109.52750000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20261873'),
(70147, 'El Recodo de San José Axalco', 3450, 'MEX', 142, 'MX', 19.20278000, -98.90278000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20280607'),
(70148, 'El Recreo', 3454, 'TAB', 142, 'MX', 18.28058000, -93.09285000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280607'),
(70149, 'El Recuerdo de Ancón (Xoconoxtle de Arriba)', 3469, 'GUA', 142, 'MX', 20.63806000, -101.12694000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20206785'),
(70150, 'El Refugio', 3470, 'HID', 142, 'MX', 20.00480000, -99.18835000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q56605894'),
(70151, 'El Refugio', 3449, 'SIN', 142, 'MX', 25.99619000, -109.29990000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20255189'),
(70152, 'El Refugio', 3462, 'ZAC', 142, 'MX', 22.37028000, -101.76083000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20220975'),
(70153, 'El Refugio', 3459, 'GRO', 142, 'MX', 17.03833000, -99.20056000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20207055'),
(70154, 'El Refugio de Peñuelas', 3456, 'AGU', 142, 'MX', 21.71635000, -102.28941000, '2019-10-05 23:08:40', '2020-05-01 17:22:59', 1, 'Q20207055'),
(70155, 'El Refugio de Providencia (Providencia)', 3456, 'AGU', 142, 'MX', 22.34667000, -102.25778000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20255170'),
(70156, 'El Refugio de los Sauces', 3469, 'GUA', 142, 'MX', 21.01488000, -101.53084000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280633'),
(70157, 'El Remolino', 3464, 'VER', 142, 'MX', 20.38921000, -97.21213000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280633'),
(70158, 'El Retiro', 3471, 'COA', 142, 'MX', 25.82758000, -103.13004000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280633'),
(70159, 'El Rincón', 3455, 'QUE', 142, 'MX', 20.74111000, -99.78472000, '2019-10-05 23:08:40', '2020-05-01 17:23:02', 1, 'Q20280732'),
(70160, 'El Rincón', 3470, 'HID', 142, 'MX', 20.27334000, -98.90431000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q50010758'),
(70161, 'El Rincón (Santa Cruz del Rincón)', 3459, 'GRO', 142, 'MX', 16.99611000, -98.73528000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20207082'),
(70162, 'El Rincón Citlaltépetl', 3476, 'PUE', 142, 'MX', 19.15556000, -97.89444000, '2019-10-05 23:08:40', '2020-05-01 17:23:01', 1, 'Q20255636'),
(70163, 'El Rincón de San Felipe (Tercera Manzana San Felipe)', 3474, 'MIC', 142, 'MX', 19.50639000, -100.37278000, '2019-10-05 23:08:40', '2020-05-01 17:23:01', 1, 'Q20238316'),
(70164, 'El Rincón de la Candelaria', 3450, 'MEX', 142, 'MX', 19.79000000, -99.84306000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20238283'),
(70165, 'El Rincón de los Perales', 3450, 'MEX', 142, 'MX', 19.54222000, -99.82806000, '2019-10-05 23:08:40', '2020-05-01 17:23:00', 1, 'Q20238284'),
(70166, 'El Roble', 3449, 'SIN', 142, 'MX', 23.24556000, -106.20583000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20280762'),
(70167, 'El Roble', 3470, 'HID', 142, 'MX', 20.05806000, -98.75833000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20242214'),
(70168, 'El Rodeo', 3465, 'MOR', 142, 'MX', 18.77864000, -99.32269000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q20242214'),
(70169, 'El Rodeo', 3468, 'SON', 142, 'MX', 27.01482000, -109.64254000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q41772392'),
(70170, 'El Rosal', 3450, 'MEX', 142, 'MX', 20.10126000, -99.68555000, '2019-10-05 23:08:40', '2019-10-05 23:08:40', 1, 'Q41772392'),
(70171, 'El Rosario', 3448, 'OAX', 142, 'MX', 17.04271000, -96.69209000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q41772392'),
(70172, 'El Rosario', 3470, 'HID', 142, 'MX', 20.24842000, -99.02539000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q50011692'),
(70173, 'El Rosario', 3458, 'TLA', 142, 'MX', 19.66109000, -98.22892000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q50011692'),
(70174, 'El Rosario', 3461, 'SLP', 142, 'MX', 21.73411000, -100.92631000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q50011692'),
(70175, 'El Rosario', 3477, 'NAY', 142, 'MX', 20.89195000, -104.46836000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q50011692'),
(70176, 'El Rosario', 3449, 'SIN', 142, 'MX', 24.25556000, -107.18278000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70177, 'El Rosario', 3455, 'QUE', 142, 'MX', 20.39180000, -100.06583000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70178, 'El Rosario de Arriba', 3457, 'BCN', 142, 'MX', 30.05984000, -115.72448000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70179, 'El Rucio', 3462, 'ZAC', 142, 'MX', 23.40743000, -102.08194000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70180, 'El Sabino', 3449, 'SIN', 142, 'MX', 25.68325000, -108.39349000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70181, 'El Sabino', 3469, 'GUA', 142, 'MX', 20.28131000, -101.00224000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70182, 'El Sacrificio', 3451, 'CHP', 142, 'MX', 14.87283000, -92.22782000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70183, 'El Sahuaral', 3468, 'SON', 142, 'MX', 26.92633000, -109.66643000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70184, 'El Saladito', 3449, 'SIN', 142, 'MX', 23.86987000, -106.84784000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1858643'),
(70185, 'El Salero', 3456, 'AGU', 142, 'MX', 22.38706000, -102.32323000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20280854'),
(70186, 'El Salitre', 3450, 'MEX', 142, 'MX', 18.95250000, -99.58972000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20238307'),
(70187, 'El Salitre', 3455, 'QUE', 142, 'MX', 20.66596000, -100.42288000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20239487'),
(70188, 'El Salitre', 3465, 'MOR', 142, 'MX', 18.67583000, -98.95806000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20235943'),
(70189, 'El Salto', 3459, 'GRO', 142, 'MX', 16.86460000, -99.76543000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20235943'),
(70190, 'El Salto', 3453, 'DUR', 142, 'MX', 23.77781000, -105.36192000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q5352072'),
(70191, 'El Salto', 3462, 'ZAC', 142, 'MX', 23.19556000, -103.06750000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20264539'),
(70192, 'El Salto de Espejo', 3469, 'GUA', 142, 'MX', 20.45045000, -100.50587000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20206883'),
(70193, 'El Salto de Eyipantla', 3464, 'VER', 142, 'MX', 18.38954000, -95.20285000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20206883'),
(70194, 'El Salvador', 3462, 'ZAC', 142, 'MX', 24.52093000, -100.86631000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20206883'),
(70195, 'El Salvador (Ranchito San José del Carmen)', 3469, 'GUA', 142, 'MX', 20.28750000, -100.87722000, '2019-10-05 23:08:41', '2020-05-01 17:23:00', 1, 'Q20206889'),
(70196, 'El Saneal', 3468, 'SON', 142, 'MX', 26.91150000, -109.38817000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q41783118'),
(70197, 'El Saucillo', 3470, 'HID', 142, 'MX', 20.07694000, -98.73722000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27825334'),
(70198, 'El Saucillo (Fraccionamiento)', 3470, 'HID', 142, 'MX', 20.06778000, -98.73194000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20242248'),
(70199, 'El Saucito (El Horno)', 3462, 'ZAC', 142, 'MX', 22.73111000, -102.09528000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20221037'),
(70200, 'El Sauz', 3447, 'CHH', 142, 'MX', 29.05056000, -106.25280000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20221037'),
(70201, 'El Sauz (El Sauz de Villaseñor)', 3469, 'GUA', 142, 'MX', 20.41056000, -100.79667000, '2019-10-05 23:08:41', '2020-05-01 17:23:00', 1, 'Q20206915'),
(70202, 'El Sauz de Abajo', 3474, 'MIC', 142, 'MX', 20.07500000, -102.26833000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20280961'),
(70203, 'El Sauzal de Rodríguez', 3457, 'BCN', 142, 'MX', 31.89329000, -116.69256000, '2019-10-05 23:08:41', '2020-05-01 17:22:59', 1, 'Q20280977'),
(70204, 'El Saúz', 3455, 'QUE', 142, 'MX', 20.47774000, -100.11540000, '2019-10-05 23:08:41', '2020-05-01 17:23:02', 1, 'Q20246330'),
(70205, 'El Sibal', 3451, 'CHP', 142, 'MX', 16.98000000, -91.49111000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20214212'),
(70206, 'El Silencio', 3460, 'BCS', 142, 'MX', 27.46801000, -113.29987000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20245268'),
(70207, 'El Sitio', 3462, 'ZAC', 142, 'MX', 21.96418000, -101.58832000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20245268'),
(70208, 'El Siviral (Jigica)', 3468, 'SON', 142, 'MX', 27.10222000, -109.50083000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20228517'),
(70209, 'El Susto', 3470, 'HID', 142, 'MX', 20.05905000, -98.49303000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20228517'),
(70210, 'El Súchil', 3459, 'GRO', 142, 'MX', 17.22694000, -100.63917000, '2019-10-05 23:08:41', '2020-05-01 17:23:00', 1, 'Q20207142'),
(70211, 'El Tamarindo', 3477, 'NAY', 142, 'MX', 21.95810000, -105.23220000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20207142'),
(70212, 'El Tecolote', 3469, 'GUA', 142, 'MX', 20.88407000, -101.92432000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20207142'),
(70213, 'El Tejar', 3464, 'VER', 142, 'MX', 19.07433000, -96.16097000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20265947'),
(70214, 'El Tejocote', 3459, 'GRO', 142, 'MX', 17.32903000, -98.65866000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20265947'),
(70215, 'El Tejocote', 3455, 'QUE', 142, 'MX', 20.61976000, -100.03447000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20265947'),
(70216, 'El Tejocote (El Domingo)', 3469, 'GUA', 142, 'MX', 21.67611000, -100.91500000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20257968'),
(70217, 'El Tepetatal', 3450, 'MEX', 142, 'MX', 19.34581000, -99.77086000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20238364'),
(70218, 'El Tepetate', 3461, 'SLP', 142, 'MX', 22.01344000, -101.24195000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20238364'),
(70219, 'El Tepeyac', 3470, 'HID', 142, 'MX', 20.05073000, -98.31063000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q50012188'),
(70220, 'El Tephé', 3470, 'HID', 142, 'MX', 20.44861000, -99.17500000, '2019-10-05 23:08:41', '2020-05-01 17:23:00', 1, 'Q50012188'),
(70221, 'El Tequesquite', 3474, 'MIC', 142, 'MX', 20.30174000, -102.28833000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q50012188'),
(70222, 'El Terrero', 3464, 'VER', 142, 'MX', 19.53709000, -96.79907000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q50012188'),
(70223, 'El Terrero', 3450, 'MEX', 142, 'MX', 18.79028000, -99.64528000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20129069'),
(70224, 'El Terrero', 3447, 'CHH', 142, 'MX', 29.18152000, -107.38740000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20129069'),
(70225, 'El Terrero', 3459, 'GRO', 142, 'MX', 18.12934000, -100.31819000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20129069'),
(70226, 'El Ticui', 3459, 'GRO', 142, 'MX', 17.21639000, -100.44472000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20207230'),
(70227, 'El Tigre', 3454, 'TAB', 142, 'MX', 18.10194000, -92.94194000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20227910'),
(70228, 'El Tigre (Segunda Manzana de Crescencio Morales)', 3474, 'MIC', 142, 'MX', 19.47361000, -100.24417000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20238660'),
(70229, 'El Tintal', 3467, 'ROO', 142, 'MX', 20.89194000, -87.46611000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281218'),
(70230, 'El Tizate', 3477, 'NAY', 142, 'MX', 21.80179000, -105.11670000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281218'),
(70231, 'El Tobarito', 3468, 'SON', 142, 'MX', 27.37057000, -109.89379000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20129673'),
(70232, 'El Tortuguero', 3454, 'TAB', 142, 'MX', 18.31413000, -93.28499000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20129673'),
(70233, 'El Trapiche', 3472, 'COL', 142, 'MX', 19.27787000, -103.66090000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20129673'),
(70234, 'El Triunfo', 3454, 'TAB', 142, 'MX', 17.92241000, -91.16946000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20228543'),
(70235, 'El Triunfo', 3451, 'CHP', 142, 'MX', 16.20200000, -91.85916000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20228543'),
(70236, 'El Triunfo', 3464, 'VER', 142, 'MX', 19.42136000, -97.22002000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20228543'),
(70237, 'El Triunfo 1ra. Sección (Cardona)', 3451, 'CHP', 142, 'MX', 17.60778000, -93.23306000, '2019-10-05 23:08:41', '2020-05-01 17:22:59', 1, 'Q20281261'),
(70238, 'El Triunfo de las Tres Maravillas', 3451, 'CHP', 142, 'MX', 15.64694000, -92.15639000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20130313'),
(70239, 'El Tular', 3454, 'TAB', 142, 'MX', 18.15429000, -93.33820000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20130313'),
(70240, 'El Tule', 3456, 'AGU', 142, 'MX', 22.08438000, -102.09100000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20130313'),
(70241, 'El Tule', 3462, 'ZAC', 142, 'MX', 22.64128000, -102.03830000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20130313'),
(70242, 'El Tule', 3447, 'CHH', 142, 'MX', 27.07325000, -106.31735000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q2990973'),
(70243, 'El Tulín', 3464, 'VER', 142, 'MX', 18.24750000, -94.95373000, '2019-10-05 23:08:41', '2020-05-01 17:23:02', 1, 'Q2990973'),
(70244, 'El Tumbo', 3451, 'CHP', 142, 'MX', 17.07779000, -91.62595000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q2990973'),
(70245, 'El Tunal', 3469, 'GUA', 142, 'MX', 20.56944000, -100.61995000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q2990973'),
(70246, 'El Tzay', 3451, 'CHP', 142, 'MX', 16.86361000, -92.31222000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20130874'),
(70247, 'El Vado de San Pedro', 3477, 'NAY', 142, 'MX', 21.94742000, -105.18005000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20130874'),
(70248, 'El Varal', 3469, 'GUA', 142, 'MX', 20.34845000, -101.60028000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20130874'),
(70249, 'El Varal (San Sebastián Número Uno)', 3449, 'SIN', 142, 'MX', 25.64278000, -108.42500000, '2019-10-05 23:08:41', '2020-05-01 17:23:02', 1, 'Q20131276'),
(70250, 'El Vegil', 3455, 'QUE', 142, 'MX', 20.43111000, -100.35024000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281331'),
(70251, 'El Veladero', 3476, 'PUE', 142, 'MX', 18.96421000, -97.48289000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281331'),
(70252, 'El Venado', 3470, 'HID', 142, 'MX', 20.06291000, -98.76018000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281331'),
(70253, 'El Venado', 3477, 'NAY', 142, 'MX', 21.94426000, -105.00100000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281331'),
(70254, 'El Verde', 3449, 'SIN', 142, 'MX', 23.36461000, -106.13514000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281331'),
(70255, 'El Vergel', 3451, 'CHP', 142, 'MX', 16.57951000, -92.08148000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281331'),
(70256, 'El Vergel', 3453, 'DUR', 142, 'MX', 25.63965000, -103.52139000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70257, 'El Vicarlo', 3469, 'GUA', 142, 'MX', 20.58211000, -100.67120000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70258, 'El Volador', 3464, 'VER', 142, 'MX', 20.64294000, -97.24885000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70259, 'El Walamo', 3449, 'SIN', 142, 'MX', 23.14009000, -106.24500000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70260, 'El Zapotal', 3451, 'CHP', 142, 'MX', 16.74991000, -91.44192000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70261, 'El Zapote', 3469, 'GUA', 142, 'MX', 21.73563000, -101.04905000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70262, 'El Zapote Bravo', 3464, 'VER', 142, 'MX', 20.66200000, -97.99907000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70263, 'El Zapotillo', 3451, 'CHP', 142, 'MX', 16.83700000, -92.95152000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20219114'),
(70264, 'El Águila', 3451, 'CHP', 142, 'MX', 15.09334000, -92.18512000, '2019-10-05 23:08:41', '2020-05-01 17:22:59', 1, 'Q20219114'),
(70265, 'El Águila', 3464, 'VER', 142, 'MX', 20.63072000, -97.45632000, '2019-10-05 23:08:41', '2020-05-01 17:23:02', 1, 'Q20219114'),
(70266, 'El Águila (La Mesa)', 3450, 'MEX', 142, 'MX', 19.16694000, -99.40639000, '2019-10-05 23:08:41', '2020-05-01 17:23:00', 1, 'Q20142929'),
(70267, 'El Ámbar', 3451, 'CHP', 142, 'MX', 17.02500000, -92.83417000, '2019-10-05 23:08:41', '2020-05-01 17:22:59', 1, 'Q20277267'),
(70268, 'El Ámbar (El Ámbar de Echeverría)', 3451, 'CHP', 142, 'MX', 16.09639000, -92.83333000, '2019-10-05 23:08:41', '2020-05-01 17:22:59', 1, 'Q20277268'),
(70269, 'Eldorado', 3449, 'SIN', 142, 'MX', 24.32444000, -107.36722000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20259323'),
(70270, 'Elota', 3449, 'SIN', 142, 'MX', 24.03463000, -106.84491000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q11077460'),
(70271, 'Elotepec', 3464, 'VER', 142, 'MX', 19.18820000, -97.03768000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q11077460'),
(70272, 'Eloxochitlán', 3470, 'HID', 142, 'MX', 20.72703000, -98.87693000, '2019-10-05 23:08:41', '2020-05-01 17:23:00', 1, 'Q11077460'),
(70273, 'Eloxochitlán', 3476, 'PUE', 142, 'MX', 18.50883000, -96.92271000, '2019-10-05 23:08:41', '2020-05-01 17:23:01', 1, 'Q5829881'),
(70274, 'Eloxochitlán de Flores Magón', 3448, 'OAX', 142, 'MX', 18.17724000, -96.87538000, '2019-10-05 23:08:41', '2020-05-01 17:23:01', 1, 'Q2651860'),
(70275, 'Emancipación Quetzalapa', 3476, 'PUE', 142, 'MX', 19.17333000, -97.39203000, '2019-10-05 23:08:41', '2020-05-01 17:23:01', 1, 'Q2651860'),
(70276, 'Emiliano Zapata', 3451, 'CHP', 142, 'MX', 17.74058000, -91.76635000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q10758396'),
(70277, 'Emiliano Zapata', 3465, 'MOR', 142, 'MX', 18.84065000, -99.18463000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134374'),
(70278, 'Emiliano Zapata', 3470, 'HID', 142, 'MX', 19.65490000, -98.54497000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134267'),
(70279, 'Emiliano Zapata', 3458, 'TLA', 142, 'MX', 19.55861000, -97.91667000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27777391'),
(70280, 'Emiliano Zapata', 3466, 'YUC', 142, 'MX', 20.22605000, -89.46813000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27777391'),
(70281, 'Emiliano Zapata', 3461, 'SLP', 142, 'MX', 21.92149000, -100.91358000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27777391'),
(70282, 'Emiliano Zapata', 3462, 'ZAC', 142, 'MX', 23.71873000, -103.19220000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27777391'),
(70283, 'Emiliano Zapata', 3477, 'NAY', 142, 'MX', 21.36477000, -104.91283000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27777391'),
(70284, 'Emiliano Zapata', 3453, 'DUR', 142, 'MX', 24.44218000, -103.88784000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27777391'),
(70285, 'Emiliano Zapata', 3456, 'AGU', 142, 'MX', 22.10635000, -102.30056000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q27777391'),
(70286, 'Emiliano Zapata', 3450, 'MEX', 142, 'MX', 19.34361000, -98.97472000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20238407'),
(70287, 'Emiliano Zapata', 3474, 'MIC', 142, 'MX', 19.33780000, -101.93550000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20238407'),
(70288, 'Emiliano Zapata', 3452, 'NLE', 142, 'MX', 25.91028000, -100.28056000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134432'),
(70289, 'Emiliano Zapata', 3457, 'BCN', 142, 'MX', 30.75417000, -116.00306000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20247543'),
(70290, 'Emiliano Zapata', 3464, 'VER', 142, 'MX', 19.45766000, -96.76584000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q5371735'),
(70291, 'Emiliano Zapata (Casahuates)', 3465, 'MOR', 142, 'MX', 18.93056000, -98.95889000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134560'),
(70292, 'Emiliano Zapata (San José Bata)', 3450, 'MEX', 142, 'MX', 19.91111000, -99.00667000, '2019-10-05 23:08:41', '2020-05-01 17:23:00', 1, 'Q20238405'),
(70293, 'Emiliano Zapata (Santo Domingo)', 3450, 'MEX', 142, 'MX', 19.57972000, -99.78806000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134305'),
(70294, 'Emilio Carranza', 3462, 'ZAC', 142, 'MX', 24.10390000, -103.66746000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134305'),
(70295, 'Emilio Carranza (Santa Cruz)', 3476, 'PUE', 142, 'MX', 19.71278000, -97.65278000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134578'),
(70296, 'Emilio Portes Gil', 3476, 'PUE', 142, 'MX', 19.30751000, -97.51027000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134578'),
(70297, 'Emilio Portes Gil', 3450, 'MEX', 142, 'MX', 19.64444000, -99.91667000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20281524'),
(70298, 'Empalme', 3468, 'SON', 142, 'MX', 27.96166000, -110.81411000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q934086'),
(70299, 'Empalme Escobedo', 3469, 'GUA', 142, 'MX', 20.67250000, -100.74675000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20207060'),
(70300, 'Empaque Tarriba', 3449, 'SIN', 142, 'MX', 23.90028000, -106.93111000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134655'),
(70301, 'Encinal Colorado', 3448, 'OAX', 142, 'MX', 17.00995000, -95.10732000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134655'),
(70302, 'Encrucijada 3ra. Sección (Las Calzadas)', 3454, 'TAB', 142, 'MX', 18.25491000, -93.55619000, '2019-10-05 23:08:41', '2020-05-01 17:23:02', 1, 'Q20134798'),
(70303, 'Enramadas', 3461, 'SLP', 142, 'MX', 21.88316000, -100.78204000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134798'),
(70304, 'Enrique Estrada', 3461, 'SLP', 142, 'MX', 22.24030000, -100.88869000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20134798'),
(70305, 'Ensenada', 3457, 'BCN', 142, 'MX', 31.86667000, -116.61666000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1548691'),
(70306, 'Entabladero', 3464, 'VER', 142, 'MX', 20.27515000, -97.55114000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q1548691'),
(70307, 'Enthavi', 3465, 'MOR', 142, 'MX', 19.49842000, -99.57502000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20238414'),
(70308, 'Entronque Laredo-Salinas Victoria', 3452, 'NLE', 142, 'MX', 25.85500000, -100.24722000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20135088'),
(70309, 'Entronque de Matehuala (El Huizache)', 3461, 'SLP', 142, 'MX', 22.92500000, -100.45889000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q20259133'),
(70310, 'Epazoyucan', 3470, 'HID', 142, 'MX', 20.02654000, -98.63802000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q3845429'),
(70311, 'Epigmenio González', 3455, 'QUE', 142, 'MX', 20.55090000, -100.16505000, '2019-10-05 23:08:41', '2020-05-01 17:23:02', 1, 'Q3845429'),
(70312, 'Epitacio Huerta', 3450, 'MEX', 142, 'MX', 20.13493000, -100.29321000, '2019-10-05 23:08:41', '2019-10-05 23:08:41', 1, 'Q3845429'),
(70313, 'Ermita de Guadalupe', 3462, 'ZAC', 142, 'MX', 22.58579000, -103.03133000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q3845429'),
(70314, 'Erongarícuaro', 3474, 'MIC', 142, 'MX', 19.58881000, -101.72110000, '2019-10-05 23:08:42', '2020-05-01 17:23:01', 1, 'Q2057767'),
(70315, 'Escaleras', 3456, 'AGU', 142, 'MX', 22.25054000, -102.33377000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q2057767'),
(70316, 'Escalerilla Lagunas', 3459, 'GRO', 142, 'MX', 17.37806000, -98.84667000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20281579'),
(70317, 'Escalerillas', 3461, 'SLP', 142, 'MX', 22.11167000, -101.07305000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20281581'),
(70318, 'Escamillas', 3449, 'SIN', 142, 'MX', 23.27217000, -106.24670000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20281581'),
(70319, 'Escolasticas', 3455, 'QUE', 142, 'MX', 20.42809000, -100.21186000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20281581'),
(70320, 'Escolín de Olarte', 3464, 'VER', 142, 'MX', 20.49840000, -97.42336000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20281581'),
(70321, 'Escuadrón Doscientos Uno', 3471, 'COA', 142, 'MX', 25.67433000, -103.34829000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20135412'),
(70322, 'Escuinapa', 3449, 'SIN', 142, 'MX', 22.83279000, -105.77772000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q1367437'),
(70323, 'Escuintla', 3451, 'CHP', 142, 'MX', 15.33333000, -92.63333000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q1477389'),
(70324, 'Escárcega', 3475, 'CAM', 142, 'MX', 18.61404000, -90.72870000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q2541910'),
(70325, 'Esfuerzos Unidos', 3453, 'DUR', 142, 'MX', 24.82214000, -104.99000000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q2541910'),
(70326, 'Esmeralda', 3453, 'DUR', 142, 'MX', 25.74326000, -103.43097000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q2541910'),
(70327, 'Esperanza', 3476, 'PUE', 142, 'MX', 18.85853000, -97.37641000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20142521'),
(70328, 'Esperanza', 3455, 'QUE', 142, 'MX', 20.64610000, -100.11128000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20142521'),
(70329, 'Esperanza', 3468, 'SON', 142, 'MX', 27.57916000, -109.92980000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q27940770'),
(70330, 'Espinal', 3464, 'VER', 142, 'MX', 20.26997000, -97.48927000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q8778635'),
(70331, 'Espita', 3466, 'YUC', 142, 'MX', 21.01098000, -88.30681000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q2448265'),
(70332, 'Esqueda', 3468, 'SON', 142, 'MX', 30.72412000, -109.58930000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20228584'),
(70333, 'Estacion Bamoa', 3449, 'SIN', 142, 'MX', 25.70842000, -108.31298000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20228584'),
(70334, 'Estación Camacho', 3462, 'ZAC', 142, 'MX', 24.44038000, -102.37283000, '2019-10-05 23:08:42', '2020-05-01 17:23:03', 1, 'Q20228584'),
(70335, 'Estación Capomas', 3449, 'SIN', 142, 'MX', 25.60066000, -108.22333000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20228584'),
(70336, 'Estación Chimaneco', 3449, 'SIN', 142, 'MX', 24.95430000, -107.74913000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20228584'),
(70337, 'Estación Coahuila', 3457, 'BCN', 142, 'MX', 32.19304000, -114.99933000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q20135850'),
(70338, 'Estación Conchos', 3447, 'CHH', 142, 'MX', 27.97326000, -105.28843000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q4231964'),
(70339, 'Estación Consuelo', 3447, 'CHH', 142, 'MX', 28.33118000, -105.59471000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q4231964'),
(70340, 'Estación Corral', 3468, 'SON', 142, 'MX', 27.62889000, -109.96611000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20135856'),
(70341, 'Estación Corralejo', 3469, 'GUA', 142, 'MX', 20.47000000, -101.61639000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20281645'),
(70342, 'Estación Dobladero', 3464, 'VER', 142, 'MX', 18.11500000, -95.76611000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20135893'),
(70343, 'Estación Huehuetán', 3451, 'CHP', 142, 'MX', 15.01091000, -92.40662000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q20135893'),
(70344, 'Estación Joaquín', 3469, 'GUA', 142, 'MX', 20.56082000, -101.51993000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20135893'),
(70345, 'Estación Juanita', 3464, 'VER', 142, 'MX', 17.80889000, -95.21750000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20135918'),
(70346, 'Estación Llano', 3468, 'SON', 142, 'MX', 30.36149000, -111.10381000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q43157979'),
(70347, 'Estación Mogoñé', 3448, 'OAX', 142, 'MX', 16.99472000, -95.03667000, '2019-10-05 23:08:42', '2020-05-01 17:23:01', 1, 'Q20135954'),
(70348, 'Estación Obispo', 3449, 'SIN', 142, 'MX', 24.29306000, -107.15944000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20259332'),
(70349, 'Estación Pescaderos', 3457, 'BCN', 142, 'MX', 32.31196000, -115.13823000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q20259332'),
(70350, 'Estación Pénjamo', 3469, 'GUA', 142, 'MX', 20.39470000, -101.69188000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20259332'),
(70351, 'Estación Queréndaro', 3474, 'MIC', 142, 'MX', 19.88285000, -100.94861000, '2019-10-05 23:08:42', '2020-05-01 17:23:01', 1, 'Q20298047'),
(70352, 'Estación Rosales', 3449, 'SIN', 142, 'MX', 24.83719000, -107.58005000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20298047'),
(70353, 'Estación San José', 3462, 'ZAC', 142, 'MX', 23.18972000, -102.78472000, '2019-10-05 23:08:42', '2020-05-01 17:23:03', 1, 'Q20135993'),
(70354, 'Estación San Manuel', 3451, 'CHP', 142, 'MX', 17.65256000, -93.38427000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q20135993'),
(70355, 'Estación Santa Engracia', 3463, 'TAM', 142, 'MX', 24.01528000, -99.20194000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20225370'),
(70356, 'Estación Tamuín', 3461, 'SLP', 142, 'MX', 22.07750000, -98.81111000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20259149'),
(70357, 'Estación Tuzantán', 3451, 'CHP', 142, 'MX', 15.10643000, -92.45092000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q20259149'),
(70358, 'Estación Zamora', 3468, 'SON', 142, 'MX', 29.26116000, -110.88653000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20230225'),
(70359, 'Estación de Apulco', 3470, 'HID', 142, 'MX', 20.28611000, -98.34750000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20135870'),
(70360, 'Estación de San Francisco', 3469, 'GUA', 142, 'MX', 21.03722000, -101.82472000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20135887'),
(70361, 'Estación la Piedad', 3469, 'GUA', 142, 'MX', 20.36750000, -101.99806000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20207085'),
(70362, 'Estancia de Guadalupe', 3462, 'ZAC', 142, 'MX', 22.28063000, -101.65745000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20207085'),
(70363, 'Estancia de Ánimas', 3462, 'ZAC', 142, 'MX', 22.48795000, -101.99526000, '2019-10-05 23:08:42', '2020-05-01 17:23:03', 1, 'Q20207085'),
(70364, 'Estancia del Llano', 3469, 'GUA', 142, 'MX', 20.51101000, -100.72637000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20207085'),
(70365, 'Estanzuela', 3464, 'VER', 142, 'MX', 19.46081000, -96.85819000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20207085'),
(70366, 'Estanzuela', 3461, 'SLP', 142, 'MX', 22.26889000, -101.00806000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20259160'),
(70367, 'Estanzuela de Romero', 3469, 'GUA', 142, 'MX', 20.18477000, -100.50448000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20259160'),
(70368, 'Estapilla', 3454, 'TAB', 142, 'MX', 17.54491000, -91.40503000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20259160'),
(70369, 'Estero de Milpas', 3464, 'VER', 142, 'MX', 21.25370000, -97.45082000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20259160'),
(70370, 'Estero del Ídolo', 3464, 'VER', 142, 'MX', 20.88953000, -97.65878000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20259160'),
(70371, 'Esteros', 3463, 'TAM', 142, 'MX', 22.52005000, -98.12607000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q27798278'),
(70372, 'Estrada', 3469, 'GUA', 142, 'MX', 20.52918000, -100.86550000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q27798278'),
(70373, 'Estrella de Belén', 3451, 'CHP', 142, 'MX', 17.38722000, -91.95861000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q20281687'),
(70374, 'Etchojoa', 3468, 'SON', 142, 'MX', 26.91094000, -109.62610000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q1360265'),
(70375, 'Etchoropo', 3468, 'SON', 142, 'MX', 26.76727000, -109.68236000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20228593'),
(70376, 'Etúcuaro', 3450, 'MEX', 142, 'MX', 19.89286000, -102.12945000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20136266'),
(70377, 'Etúcuaro', 3474, 'MIC', 142, 'MX', 19.41939000, -101.21547000, '2019-10-05 23:08:42', '2020-05-01 17:23:01', 1, 'Q20136266'),
(70378, 'Euan', 3466, 'YUC', 142, 'MX', 20.99660000, -89.34248000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20136266'),
(70379, 'Eureka de Media Luna (Eureka)', 3453, 'DUR', 142, 'MX', 25.79889000, -103.37361000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20136313'),
(70380, 'Eureka y Belén', 3454, 'TAB', 142, 'MX', 17.55968000, -92.92899000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20136315'),
(70381, 'Ex-Hacienda Santa Inés', 3450, 'MEX', 142, 'MX', 19.70667000, -99.07250000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20281728'),
(70382, 'Ex-Hacienda de Guadalupe', 3450, 'MEX', 142, 'MX', 19.80889000, -98.97250000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20281709'),
(70383, 'Ex-Hacienda de Guadalupe', 3474, 'MIC', 142, 'MX', 19.78417000, -101.21472000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20136384'),
(70384, 'Ex-Hacienda del Copal', 3469, 'GUA', 142, 'MX', 20.74083000, -101.33528000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20207099'),
(70385, 'Ex-Hacienda el Hospital', 3465, 'MOR', 142, 'MX', 18.83111000, -98.99667000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20235962'),
(70386, 'Ex-Rancho San Dimas', 3450, 'MEX', 142, 'MX', 19.17389000, -99.56528000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20238431'),
(70387, 'Ex-Viñedos Guadalupe', 3456, 'AGU', 142, 'MX', 21.95944000, -102.27417000, '2019-10-05 23:08:42', '2020-05-01 17:22:59', 1, 'Q20136559'),
(70388, 'Ex-hacienda de Xalpa', 3450, 'MEX', 142, 'MX', 19.82150000, -99.18275000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20136559'),
(70389, 'Ex-hacienda la Concepción', 3464, 'VER', 142, 'MX', 18.84855000, -96.82437000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20136559'),
(70390, 'Excola', 3464, 'VER', 142, 'MX', 19.13876000, -97.12251000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20136559'),
(70391, 'Ezequiel Montes', 3455, 'QUE', 142, 'MX', 20.66481000, -99.89960000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q2448212'),
(70392, 'Faja de Oro', 3451, 'CHP', 142, 'MX', 15.03244000, -92.15634000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q2448212'),
(70393, 'Felipe Angeles', 3449, 'SIN', 142, 'MX', 25.86763000, -109.04080000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q2448212'),
(70394, 'Felipe Carrillo Puerto', 3467, 'ROO', 142, 'MX', 19.57750000, -88.04529000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q1403187'),
(70395, 'Felipe Carrillo Puerto', 3458, 'TLA', 142, 'MX', 19.39922000, -97.85520000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q1403187'),
(70396, 'Felipe Carrillo Puerto', 3464, 'VER', 142, 'MX', 20.13578000, -96.95164000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q1403187'),
(70397, 'Felipe Carrillo Puerto', 3474, 'MIC', 142, 'MX', 19.16325000, -102.70834000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q5441955'),
(70398, 'Felipe Carrillo Puerto', 3477, 'NAY', 142, 'MX', 21.13200000, -104.86068000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q5441955'),
(70399, 'Felipe Carrillo Puerto', 3453, 'DUR', 142, 'MX', 24.33305000, -104.01105000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q5441955'),
(70400, 'Felipe Neri (Cuatepec)', 3465, 'MOR', 142, 'MX', 19.03944000, -98.94361000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20235969'),
(70401, 'Fermín Rabadán Cervantes', 3459, 'GRO', 142, 'MX', 18.31528000, -99.56722000, '2019-10-05 23:08:42', '2020-05-01 17:23:00', 1, 'Q20281852'),
(70402, 'Filomeno Mata', 3464, 'VER', 142, 'MX', 20.20004000, -97.70392000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20262201'),
(70403, 'Flor Azul', 3449, 'SIN', 142, 'MX', 25.86987000, -109.00839000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20138339'),
(70404, 'Flor Batavia', 3448, 'OAX', 142, 'MX', 17.98475000, -96.51880000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20138339'),
(70405, 'Flor de Cacao', 3451, 'CHP', 142, 'MX', 16.12889000, -90.45111000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20214552'),
(70406, 'Florencia', 3462, 'ZAC', 142, 'MX', 21.50392000, -103.55353000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20214552'),
(70407, 'Florencio Villarreal', 3459, 'GRO', 142, 'MX', 16.70143000, -99.15247000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q3847032'),
(70408, 'Fontezuelas', 3470, 'HID', 142, 'MX', 20.48853000, -98.87164000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q3847032'),
(70409, 'Forjadores de Pachuca', 3470, 'HID', 142, 'MX', 20.05500000, -98.76333000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20242334'),
(70410, 'Fortín de las Flores', 3464, 'VER', 142, 'MX', 18.90806000, -97.00000000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q2652472'),
(70411, 'Fraccionamiento Arboledas San Ramón', 3464, 'VER', 142, 'MX', 19.09417000, -96.15667000, '2019-10-05 23:08:42', '2020-05-01 17:23:02', 1, 'Q20266259'),
(70412, 'Fraccionamiento Carlos Salinas de Gortari', 3470, 'HID', 142, 'MX', 20.11028000, -98.43028000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20138883'),
(70413, 'Fraccionamiento Ciudad Olmeca', 3464, 'VER', 142, 'MX', 18.15056000, -94.55250000, '2019-10-05 23:08:42', '2019-10-05 23:08:42', 1, 'Q20138891'),
(70414, 'Fraccionamiento Ciudad Yagul', 3448, 'OAX', 142, 'MX', 16.97750000, -96.46194000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20263904'),
(70415, 'Fraccionamiento Colinas Universidad', 3474, 'MIC', 142, 'MX', 20.01833000, -102.73694000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20138913'),
(70416, 'Fraccionamiento Colinas del Sol', 3450, 'MEX', 142, 'MX', 19.37222000, -99.74028000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20138909'),
(70417, 'Fraccionamiento Cosmópolis Octavo Sector', 3452, 'NLE', 142, 'MX', 25.82528000, -100.24611000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20263168'),
(70418, 'Fraccionamiento Costa Dorada', 3464, 'VER', 142, 'MX', 19.20833000, -96.21639000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20266261'),
(70419, 'Fraccionamiento Ex-Hacienda Catano', 3448, 'OAX', 142, 'MX', 17.22750000, -96.81556000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20138990'),
(70420, 'Fraccionamiento Ex-Hacienda el Refugio', 3474, 'MIC', 142, 'MX', 19.95083000, -102.25639000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20238845'),
(70421, 'Fraccionamiento Galaxia Tarímbaro', 3474, 'MIC', 142, 'MX', 19.76167000, -101.19444000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20238845'),
(70422, 'Fraccionamiento Geovillas los Pinos', 3464, 'VER', 142, 'MX', 19.21722000, -96.22639000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20269183'),
(70423, 'Fraccionamiento Hacienda del Bosque', 3450, 'MEX', 142, 'MX', 19.72667000, -98.96861000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20282033'),
(70424, 'Fraccionamiento Laureles Eréndira', 3474, 'MIC', 142, 'MX', 19.77222000, -101.13056000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20238861'),
(70425, 'Fraccionamiento Lomas de Ahuatlán', 3465, 'MOR', 142, 'MX', 18.95444000, -99.25722000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20139142'),
(70426, 'Fraccionamiento Metrópolis II', 3474, 'MIC', 142, 'MX', 19.75639000, -101.20444000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20282085'),
(70427, 'Fraccionamiento Misión de San Javier', 3452, 'NLE', 142, 'MX', 25.74556000, -100.15194000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20139217'),
(70428, 'Fraccionamiento Misión del Valle', 3474, 'MIC', 142, 'MX', 19.76556000, -101.12167000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20139220'),
(70429, 'Fraccionamiento Monte Olivo', 3474, 'MIC', 142, 'MX', 19.98472000, -102.25111000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139230'),
(70430, 'Fraccionamiento Ocuiltzapotlán Dos', 3454, 'TAB', 142, 'MX', 18.10889000, -92.86778000, '2019-10-05 23:08:43', '2020-05-01 17:23:02', 1, 'Q20227937'),
(70431, 'Fraccionamiento Paraíso Real', 3469, 'GUA', 142, 'MX', 21.11250000, -101.58083000, '2019-10-05 23:08:43', '2020-05-01 17:23:00', 1, 'Q20282101'),
(70432, 'Fraccionamiento Paseo de las Torres', 3469, 'GUA', 142, 'MX', 21.16833000, -101.75806000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20207178'),
(70433, 'Fraccionamiento Praderas de la Venta', 3469, 'GUA', 142, 'MX', 20.53611000, -101.00000000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20207180'),
(70434, 'Fraccionamiento Privadas del Sol', 3474, 'MIC', 142, 'MX', 19.75222000, -101.19139000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139292'),
(70435, 'Fraccionamiento Real Palmas', 3452, 'NLE', 142, 'MX', 25.90417000, -100.15972000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139314'),
(70436, 'Fraccionamiento Real de San Pablo', 3450, 'MEX', 142, 'MX', 19.38944000, -99.64861000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20282113'),
(70437, 'Fraccionamiento Real del Valle', 3448, 'OAX', 142, 'MX', 16.93278000, -96.76778000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20282116'),
(70438, 'Fraccionamiento Riberas de San Jerónimo', 3448, 'OAX', 142, 'MX', 17.11250000, -96.77722000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20139326'),
(70439, 'Fraccionamiento Rinconada del Valle', 3450, 'MEX', 142, 'MX', 19.41111000, -99.57417000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20282121'),
(70440, 'Fraccionamiento San Miguel', 3448, 'OAX', 142, 'MX', 17.21278000, -96.78694000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20231602'),
(70441, 'Fraccionamiento San Miguel', 3474, 'MIC', 142, 'MX', 20.07111000, -102.68667000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139379'),
(70442, 'Fraccionamiento Santa Cruz', 3464, 'VER', 142, 'MX', 17.97194000, -94.89500000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139388'),
(70443, 'Fraccionamiento Universo', 3465, 'MOR', 142, 'MX', 18.96167000, -99.18639000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139415'),
(70444, 'Fraccionamiento Valle Dorado', 3464, 'VER', 142, 'MX', 18.86889000, -97.12222000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20266281'),
(70445, 'Fraccionamiento Villa Jardín', 3469, 'GUA', 142, 'MX', 20.99167000, -101.85194000, '2019-10-05 23:08:43', '2020-05-01 17:23:00', 1, 'Q20139433'),
(70446, 'Fraccionamiento Villas de Guanajuato', 3469, 'GUA', 142, 'MX', 20.96028000, -101.29806000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20207186'),
(70447, 'Fraccionamiento Villas de la Llave', 3464, 'VER', 142, 'MX', 18.91583000, -96.99111000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139449'),
(70448, 'Fraccionamiento del Magisterio Tulancinguense', 3470, 'HID', 142, 'MX', 20.07639000, -98.39972000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20242340'),
(70449, 'Fraccionamiento del Valle', 3457, 'BCN', 142, 'MX', 31.91139000, -116.26056000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20247683'),
(70450, 'Fraccionamiento la Florida', 3464, 'VER', 142, 'MX', 20.58667000, -97.42833000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20266269'),
(70451, 'Fraccionamiento la Mezquitera', 3469, 'GUA', 142, 'MX', 21.00972000, -101.83472000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20207165'),
(70452, 'Fraccionamiento la Noria', 3471, 'COA', 142, 'MX', 25.55722000, -103.34361000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139072'),
(70453, 'Fraccionamiento la Trinidad', 3450, 'MEX', 142, 'MX', 19.82917000, -99.08278000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139089'),
(70454, 'Fraccionamiento la Virgen', 3458, 'TLA', 142, 'MX', 19.32306000, -98.27389000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139091'),
(70455, 'Fraccionamiento las Fuentes', 3464, 'VER', 142, 'MX', 19.49611000, -96.88639000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139105'),
(70456, 'Fraccionamiento las Liebres', 3469, 'GUA', 142, 'MX', 20.64750000, -101.39556000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139111'),
(70457, 'Fraccionamiento los Prados', 3464, 'VER', 142, 'MX', 17.97778000, -94.63917000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139183'),
(70458, 'Fraccionamiento los Álamos', 3464, 'VER', 142, 'MX', 18.92389000, -96.97528000, '2019-10-05 23:08:43', '2020-05-01 17:23:02', 1, 'Q20139156'),
(70459, 'Fraccionamiento los Ángeles', 3449, 'SIN', 142, 'MX', 23.18833000, -106.33000000, '2019-10-05 23:08:43', '2020-05-01 17:23:02', 1, 'Q20282067'),
(70460, 'Fraccionamiento y Club de Golf los Encinos', 3450, 'MEX', 142, 'MX', 19.28917000, -99.48000000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139476'),
(70461, 'Fracción Milpillas', 3461, 'SLP', 142, 'MX', 22.22778000, -100.96417000, '2019-10-05 23:08:43', '2020-05-01 17:23:02', 1, 'Q20259177'),
(70462, 'Fracción San Roque (El Prieto)', 3450, 'MEX', 142, 'MX', 19.68722000, -99.17222000, '2019-10-05 23:08:43', '2020-05-01 17:23:00', 1, 'Q20138761'),
(70463, 'Francisco I. Madero', 3458, 'TLA', 142, 'MX', 19.52096000, -98.49052000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20138761'),
(70464, 'Francisco I. Madero', 3454, 'TAB', 142, 'MX', 18.34047000, -93.20999000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20138761'),
(70465, 'Francisco I. Madero', 3464, 'VER', 142, 'MX', 19.52718000, -97.27067000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20266297'),
(70466, 'Francisco I. Madero', 3476, 'PUE', 142, 'MX', 19.24985000, -97.22648000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20266297'),
(70467, 'Francisco I. Madero', 3453, 'DUR', 142, 'MX', 24.40091000, -104.31980000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20266297'),
(70468, 'Francisco I. Madero', 3477, 'NAY', 142, 'MX', 21.58186000, -104.81927000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20266297'),
(70469, 'Francisco I. Madero', 3451, 'CHP', 142, 'MX', 16.87427000, -93.21678000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139675'),
(70470, 'Francisco Ibarra Ramos', 3448, 'OAX', 142, 'MX', 17.97781000, -97.91748000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139675'),
(70471, 'Francisco Ignacio Madero', 3476, 'PUE', 142, 'MX', 18.49306000, -97.46384000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139675'),
(70472, 'Francisco Javier Mina', 3468, 'SON', 142, 'MX', 27.46217000, -110.10893000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q27639490'),
(70473, 'Francisco León', 3451, 'CHP', 142, 'MX', 17.31667000, -93.25000000, '2019-10-05 23:08:43', '2020-05-01 17:22:59', 1, 'Q1759430'),
(70474, 'Francisco Rueda', 3454, 'TAB', 142, 'MX', 17.82917000, -93.93389000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20227946'),
(70475, 'Francisco Sarabia', 3451, 'CHP', 142, 'MX', 16.94583000, -93.01306000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139847'),
(70476, 'Francisco Sarabia', 3476, 'PUE', 142, 'MX', 18.96417000, -98.31389000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139841'),
(70477, 'Francisco Sarabia (Cerrito Pelón)', 3474, 'MIC', 142, 'MX', 20.03086000, -102.70158000, '2019-10-05 23:08:43', '2020-05-01 17:23:01', 1, 'Q20282201'),
(70478, 'Francisco Serrato (San Bartolo)', 3474, 'MIC', 142, 'MX', 19.50680000, -100.26000000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139849'),
(70479, 'Francisco Villa', 3458, 'TLA', 142, 'MX', 19.58630000, -98.46225000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139849'),
(70480, 'Francisco Villa', 3451, 'CHP', 142, 'MX', 16.21426000, -93.33697000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20139849'),
(70481, 'Francisco Villa', 3474, 'MIC', 142, 'MX', 19.87500000, -100.93583000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20140062'),
(70482, 'Francisco Villa', 3476, 'PUE', 142, 'MX', 18.84446000, -97.76336000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20140062'),
(70483, 'Francisco Z. Mena', 3476, 'PUE', 142, 'MX', 20.72614000, -97.81882000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q5484072'),
(70484, 'Francisco Zarco', 3457, 'BCN', 142, 'MX', 32.09872000, -116.56863000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q5484072'),
(70485, 'Franco', 3469, 'GUA', 142, 'MX', 20.94327000, -101.46180000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q5484072'),
(70486, 'Franco Tavera', 3469, 'GUA', 142, 'MX', 20.60595000, -100.92700000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20140081'),
(70487, 'Fredepo', 3464, 'VER', 142, 'MX', 18.88056000, -96.97694000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20140119'),
(70488, 'Fresnillo', 3462, 'ZAC', 142, 'MX', 23.18126000, -102.87136000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q1816543'),
(70489, 'Fresno Nichi', 3450, 'MEX', 142, 'MX', 19.56694000, -99.94889000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20140159'),
(70490, 'Frontera', 3454, 'TAB', 142, 'MX', 18.54326000, -92.64530000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q20261539'),
(70491, 'Frontera', 3471, 'COA', 142, 'MX', 26.92814000, -101.45212000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q1997138'),
(70492, 'Frontera Corozal', 3451, 'CHP', 142, 'MX', 16.81473000, -90.88351000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q1997138'),
(70493, 'Frontera Hidalgo', 3451, 'CHP', 142, 'MX', 14.77744000, -92.17741000, '2019-10-05 23:08:43', '2019-10-05 23:08:43', 1, 'Q27769157');

