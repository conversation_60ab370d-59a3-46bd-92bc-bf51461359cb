INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(16674, 'Meaford', 866, 'ON', 39, 'CA', 44.60725000, -80.61081000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q3492570'),
(16675, 'Medicine Hat', 872, 'AB', 39, 'CA', 50.03928000, -110.67661000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q368241'),
(16676, 'Melfort', 870, 'SK', 39, 'CA', 52.86673000, -104.61768000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1904228'),
(16677, '<PERSON><PERSON>', 867, '<PERSON>', 39, 'CA', 49.26811000, -100.99669000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q312033'),
(16678, 'Melocheville', 873, 'QC', 39, 'CA', 45.31726000, -73.93710000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q312033'),
(16679, 'Melville', 870, 'SK', 39, 'CA', 50.91671000, -102.80099000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q605427'),
(16680, 'Mercier', 873, 'QC', 39, 'CA', 45.31678000, -73.74919000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q142192'),
(16681, 'Merritt', 875, 'BC', 39, 'CA', 50.11225000, -120.79420000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1025990'),
(16682, 'Metabetchouan-Lac-a-la-Croix', 873, 'QC', 39, 'CA', 48.41000000, -71.78000000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q142325'),
(16683, 'Metcalfe', 866, 'ON', 39, 'CA', 45.23340000, -75.46603000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '*********'),
(16684, 'Metchosin', 875, 'BC', 39, 'CA', 48.38293000, -123.53591000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16685, 'Metro Vancouver Regional District', 875, 'BC', 39, 'CA', 49.33296000, -123.00264000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16686, 'Middleton', 874, 'NS', 39, 'CA', 44.94284000, -65.07022000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16687, 'Midland', 866, 'ON', 39, 'CA', 44.75010000, -79.88296000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16688, 'Mildmay', 866, 'ON', 39, 'CA', 44.05009000, -81.11644000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16689, 'Mill Woods Town Centre', 872, 'AB', 39, 'CA', 53.45639000, -113.42751000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16690, 'Millbrook', 866, 'ON', 39, 'CA', 44.15012000, -78.44954000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16691, 'Millet', 872, 'AB', 39, 'CA', 53.10013000, -113.46870000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, '********'),
(16692, 'Milton', 866, 'ON', 39, 'CA', 43.51681000, -79.88294000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q425057'),
(16693, 'Minnedosa', 867, 'MB', 39, 'CA', 50.24532000, -99.84364000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q281649'),
(16694, 'Mirabel', 873, 'QC', 39, 'CA', 45.65008000, -74.08251000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q141921'),
(16695, 'Miramichi', 868, 'NB', 39, 'CA', 47.02895000, -65.50186000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1348913'),
(16696, 'Mission', 875, 'BC', 39, 'CA', 49.13298000, -122.30258000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1348913'),
(16697, 'Mississauga', 866, 'ON', 39, 'CA', 43.57890000, -79.65830000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q50816'),
(16698, 'Mississauga Beach', 866, 'ON', 39, 'CA', 43.26682000, -79.08287000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q50816'),
(16699, 'Mistissini', 873, 'QC', 39, 'CA', 50.41667000, -73.88333000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q50816'),
(16700, 'Moncton', 868, 'NB', 39, 'CA', 46.09454000, -64.79650000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q457334'),
(16701, 'Mont-Joli', 873, 'QC', 39, 'CA', 48.58388000, -68.19214000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q141717'),
(16702, 'Mont-Laurier', 873, 'QC', 39, 'CA', 46.55011000, -75.49930000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q142058'),
(16703, 'Mont-Royal', 873, 'QC', 39, 'CA', 45.51675000, -73.64918000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q139088'),
(16704, 'Mont-Saint-Grégoire', 873, 'QC', 39, 'CA', 45.33338000, -73.16585000, '2019-10-05 22:35:47', '2020-05-01 17:22:39', 1, 'Q3321620'),
(16705, 'Mont-Saint-Hilaire', 873, 'QC', 39, 'CA', 45.56515000, -73.18680000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q3321620'),
(16706, 'Mont-Tremblant', 873, 'QC', 39, 'CA', 46.21274000, -74.58438000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q142082'),
(16707, 'Montague', 871, 'PE', 39, 'CA', 46.16681000, -62.64866000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1009804'),
(16708, 'Montmagny', 873, 'QC', 39, 'CA', 46.98043000, -70.55493000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q141946'),
(16709, 'Montréal', 873, 'QC', 39, 'CA', 45.50008000, -73.68248000, '2019-10-05 22:35:47', '2020-05-01 17:22:39', 1, 'Q2826806'),
(16710, 'Montréal-Est', 873, 'QC', 39, 'CA', 45.63202000, -73.50750000, '2019-10-05 22:35:47', '2020-05-01 17:22:39', 1, 'Q139040'),
(16711, 'Montréal-Ouest', 873, 'QC', 39, 'CA', 45.45286000, -73.64918000, '2019-10-05 22:35:47', '2020-05-01 17:22:39', 1, 'Q22368804'),
(16712, 'Moose Factory', 866, 'ON', 39, 'CA', 51.26689000, -80.61624000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q3031892'),
(16713, 'Moose Jaw', 870, 'SK', 39, 'CA', 50.40005000, -105.53445000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1019496'),
(16714, 'Moose Lake', 867, 'MB', 39, 'CA', 49.20559000, -95.30629000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1019496'),
(16715, 'Moosomin', 870, 'SK', 39, 'CA', 50.13332000, -101.66766000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1893562'),
(16716, 'Moosonee', 866, 'ON', 39, 'CA', 51.27931000, -80.63450000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1736159'),
(16717, 'Morden', 867, 'MB', 39, 'CA', 49.19190000, -98.10136000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q579689'),
(16718, 'Morin-Heights', 873, 'QC', 39, 'CA', 45.90009000, -74.24922000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q2082493'),
(16719, 'Morinville', 872, 'AB', 39, 'CA', 53.80014000, -113.65203000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1746026'),
(16720, 'Morris', 867, 'MB', 39, 'CA', 49.35499000, -97.36567000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1746026'),
(16721, 'Morrisburg', 866, 'ON', 39, 'CA', 44.90010000, -75.18261000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q11883244'),
(16722, 'Mount Albert', 866, 'ON', 39, 'CA', 44.13341000, -79.31630000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q11883244'),
(16723, 'Mount Brydges', 866, 'ON', 39, 'CA', 42.90009000, -81.48306000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q11883244'),
(16724, 'Mount Pearl', 877, 'NL', 39, 'CA', 47.51659000, -52.78135000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1950648'),
(16725, 'Métabetchouan', 873, 'QC', 39, 'CA', 48.36679000, -72.01583000, '2019-10-05 22:35:47', '2020-05-01 17:22:39', 1, 'Q1950648'),
(16726, 'Nackawic', 868, 'NB', 39, 'CA', 45.99666000, -67.24028000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q835359'),
(16727, 'Nakusp', 875, 'BC', 39, 'CA', 50.24987000, -117.80226000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q2957435'),
(16728, 'Nanaimo', 875, 'BC', 39, 'CA', 49.16638000, -123.94003000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q16461'),
(16729, 'Nanton', 872, 'AB', 39, 'CA', 50.35008000, -113.76866000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1744143'),
(16730, 'Napanee', 866, 'ON', 39, 'CA', 44.25012000, -76.94944000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1744143'),
(16731, 'Napanee Downtown', 866, 'ON', 39, 'CA', 44.24832000, -76.95069000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q1744143'),
(16732, 'Napierville', 873, 'QC', 39, 'CA', 45.18648000, -73.40468000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q3335889'),
(16733, 'Neebing', 866, 'ON', 39, 'CA', 48.16680000, -89.41683000, '2019-10-05 22:35:47', '2019-10-05 22:35:47', 1, 'Q4859501'),
(16734, 'Neepawa', 867, 'MB', 39, 'CA', 50.22892000, -99.46642000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2450886'),
(16735, 'Nelson', 875, 'BC', 39, 'CA', 49.49985000, -117.28553000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1779015'),
(16736, 'Nepean', 866, 'ON', 39, 'CA', 45.35215000, -75.73975000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1639083'),
(16737, 'Neuville', 873, 'QC', 39, 'CA', 46.69823000, -71.58275000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142490'),
(16738, 'New Carlisle', 873, 'QC', 39, 'CA', 48.00956000, -65.33621000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q985588'),
(16739, 'New Glasgow', 874, 'NS', 39, 'CA', 45.58344000, -62.64863000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q286048'),
(16740, 'New Hamburg', 866, 'ON', 39, 'CA', 43.38339000, -80.69970000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7008036'),
(16741, 'New Maryland', 868, 'NB', 39, 'CA', 45.87932000, -66.66828000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1383701'),
(16742, 'New Westminster', 875, 'BC', 39, 'CA', 49.20678000, -122.91092000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q876122'),
(16743, 'New-Richmond', 873, 'QC', 39, 'CA', 48.16059000, -65.85823000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142295'),
(16744, 'Newmarket', 866, 'ON', 39, 'CA', 44.05011000, -79.46631000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q52999'),
(16745, 'Niagara Falls', 866, 'ON', 39, 'CA', 43.10012000, -79.06627000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q274120'),
(16746, 'Nicolet', 873, 'QC', 39, 'CA', 46.21676000, -72.61582000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141595'),
(16747, 'Nipawin', 870, 'SK', 39, 'CA', 53.36678000, -104.00092000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1915543'),
(16748, 'Nipissing District', 866, 'ON', 39, 'CA', 46.00010000, -78.99959000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1436530'),
(16749, 'Niverville', 867, 'MB', 39, 'CA', 49.60559000, -97.04234000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q277461'),
(16750, 'Noonan', 868, 'NB', 39, 'CA', 45.96682000, -66.53218000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3343397'),
(16751, 'Nord-du-Québec', 873, 'QC', 39, 'CA', 51.96200000, -74.89610000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q927561'),
(16752, 'Norfolk County', 866, 'ON', 39, 'CA', 42.83340000, -80.38297000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q385229'),
(16753, 'Norman Wells', 878, 'NT', 39, 'CA', 65.28201000, -126.83290000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3343828'),
(16754, 'Normandin', 873, 'QC', 39, 'CA', 48.83328000, -72.53209000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142402'),
(16755, 'North Battleford', 870, 'SK', 39, 'CA', 52.77972000, -108.29670000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q999654'),
(16756, 'North Bay', 866, 'ON', 39, 'CA', 46.31680000, -79.46633000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q912253'),
(16757, 'North Cowichan', 875, 'BC', 39, 'CA', 48.84133000, -123.68596000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7054960'),
(16758, 'North Oyster/Yellow Point', 875, 'BC', 39, 'CA', 49.04807000, -123.83395000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7054960'),
(16759, 'North Perth', 866, 'ON', 39, 'CA', 43.72510000, -80.96723000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3089368'),
(16760, 'North Saanich', 875, 'BC', 39, 'CA', 48.66634000, -123.41933000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2000769'),
(16761, 'North Vancouver', 875, 'BC', 39, 'CA', 49.31636000, -123.06934000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2000769'),
(16762, 'North York', 866, 'ON', 39, 'CA', 43.76681000, -79.41630000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1586510'),
(16763, 'Norwood', 866, 'ON', 39, 'CA', 44.38342000, -77.98281000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q18632575'),
(16764, 'Notre-Dame-de-Grâce', 873, 'QC', 39, 'CA', 45.47675000, -73.61432000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q18632575'),
(16765, 'Notre-Dame-de-l\'Île-Perrot', 873, 'QC', 39, 'CA', 45.36678000, -73.93250000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q141389'),
(16766, 'Notre-Dame-des-Prairies', 873, 'QC', 39, 'CA', 46.05007000, -73.43245000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142475'),
(16767, 'Notre-Dame-du-Lac', 873, 'QC', 39, 'CA', 46.75012000, -79.04961000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142475'),
(16768, 'Notre-Dame-du-Mont-Carmel', 873, 'QC', 39, 'CA', 46.01680000, -75.08259000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142475'),
(16769, 'Oak Bay', 875, 'BC', 39, 'CA', 48.44964000, -123.30260000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7073424'),
(16770, 'Oakville', 866, 'ON', 39, 'CA', 43.45011000, -79.68292000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q424984'),
(16771, 'Oka', 873, 'QC', 39, 'CA', 45.46489000, -74.08892000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q729382'),
(16772, 'Okanagan', 875, 'BC', 39, 'CA', 50.36386000, -119.34997000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q729382'),
(16773, 'Okanagan Falls', 875, 'BC', 39, 'CA', 49.35000000, -119.56667000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3350012'),
(16774, 'Okotoks', 872, 'AB', 39, 'CA', 50.72885000, -113.98281000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1363726'),
(16775, 'Olds', 872, 'AB', 39, 'CA', 51.78341000, -114.10199000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q991692'),
(16776, 'Oliver', 875, 'BC', 39, 'CA', 49.18306000, -119.55240000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3350642'),
(16777, 'Omemee', 866, 'ON', 39, 'CA', 44.29897000, -78.55989000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q22702375'),
(16778, 'Orangeville', 866, 'ON', 39, 'CA', 43.91680000, -80.09967000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2470087'),
(16779, 'Orillia', 866, 'ON', 39, 'CA', 44.60868000, -79.42068000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2373358'),
(16780, 'Ormstown', 873, 'QC', 39, 'CA', 45.13338000, -73.99922000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3356519'),
(16781, 'Oromocto', 868, 'NB', 39, 'CA', 45.83512000, -66.47917000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1966925'),
(16782, 'Osgoode', 866, 'ON', 39, 'CA', 45.14887000, -75.59778000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7106550'),
(16783, 'Oshawa', 866, 'ON', 39, 'CA', 43.90012000, -78.84957000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q211867'),
(16784, 'Osoyoos', 875, 'BC', 39, 'CA', 49.03306000, -119.45237000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q917796'),
(16785, 'Ottawa', 866, 'ON', 39, 'CA', 45.41117000, -75.69812000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1930'),
(16786, 'Otterburn Park', 873, 'QC', 39, 'CA', 45.53338000, -73.21585000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141623'),
(16787, 'Outaouais', 873, 'QC', 39, 'CA', 46.26681000, -76.31606000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q222948'),
(16788, 'Outlook', 870, 'SK', 39, 'CA', 51.50008000, -107.05128000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1916369'),
(16789, 'Owen Sound', 866, 'ON', 39, 'CA', 44.56717000, -80.94349000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1017735'),
(16790, 'Oxbow', 870, 'SK', 39, 'CA', 49.23335000, -102.16760000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1916400'),
(16791, 'Oxford', 874, 'NS', 39, 'CA', 45.73345000, -63.86542000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3248963'),
(16792, 'Paisley', 866, 'ON', 39, 'CA', 44.30641000, -81.27265000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7125167'),
(16793, 'Pangnirtung', 876, 'NU', 39, 'CA', 66.14642000, -65.69996000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q631747'),
(16794, 'Papineauville', 873, 'QC', 39, 'CA', 45.61680000, -75.01599000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3362915'),
(16795, 'Parc-Boutin', 873, 'QC', 39, 'CA', 45.29209000, -73.26154000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3362915'),
(16796, 'Paris', 866, 'ON', 39, 'CA', 43.20000000, -80.38333000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3305306'),
(16797, 'Parkhill', 866, 'ON', 39, 'CA', 43.15993000, -81.68464000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3305306'),
(16798, 'Parksville', 875, 'BC', 39, 'CA', 49.31947000, -124.31575000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1438931'),
(16799, 'Parrsboro', 874, 'NS', 39, 'CA', 45.40567000, -64.32585000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q499355'),
(16800, 'Parry Sound', 866, 'ON', 39, 'CA', 45.34732000, -80.03527000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2219667'),
(16801, 'Parry Sound District', 866, 'ON', 39, 'CA', 45.75011000, -79.83297000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1798108'),
(16802, 'Pasadena', 877, 'NL', 39, 'CA', 49.01671000, -57.59837000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3367223'),
(16803, 'Peace River', 872, 'AB', 39, 'CA', 56.23715000, -117.29176000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1743985'),
(16804, 'Peace River Regional District', 875, 'BC', 39, 'CA', 56.66650000, -122.75302000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2065772'),
(16805, 'Peachland', 875, 'BC', 39, 'CA', 49.76647000, -119.73568000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1718869'),
(16806, 'Peel', 866, 'ON', 39, 'CA', 43.75011000, -79.78293000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q50811'),
(16807, 'Pelican Narrows', 870, 'SK', 39, 'CA', 55.16685000, -102.93410000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q50811'),
(16808, 'Pemberton', 875, 'BC', 39, 'CA', 50.31641000, -122.80273000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1727611'),
(16809, 'Pembroke', 866, 'ON', 39, 'CA', 45.81681000, -77.11616000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1075086'),
(16810, 'Penhold', 872, 'AB', 39, 'CA', 52.13342000, -113.86870000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3921021'),
(16811, 'Penticton', 875, 'BC', 39, 'CA', 49.48062000, -119.58584000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q935519'),
(16812, 'Perth', 866, 'ON', 39, 'CA', 44.90011000, -76.24939000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2428683'),
(16813, 'Petawawa', 866, 'ON', 39, 'CA', 45.89452000, -77.28007000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1455440'),
(16814, 'Peterborough', 866, 'ON', 39, 'CA', 44.30012000, -78.31623000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q776930'),
(16815, 'Petrolia', 866, 'ON', 39, 'CA', 42.86678000, -82.14981000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3089341'),
(16816, 'Pickering', 866, 'ON', 39, 'CA', 43.90012000, -79.13289000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q386626'),
(16817, 'Picton', 866, 'ON', 39, 'CA', 44.00012000, -77.13275000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7191105'),
(16818, 'Pictou', 874, 'NS', 39, 'CA', 45.67875000, -62.70936000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1018711'),
(16819, 'Pictou County', 874, 'NS', 39, 'CA', 45.50015000, -62.58193000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2092485'),
(16820, 'Picture Butte', 872, 'AB', 39, 'CA', 49.88330000, -112.78516000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q4464252'),
(16821, 'Piedmont', 873, 'QC', 39, 'CA', 45.90008000, -74.13251000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3382642'),
(16822, 'Pierreville', 873, 'QC', 39, 'CA', 46.07034000, -72.81125000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3387912'),
(16823, 'Pilot Butte', 870, 'SK', 39, 'CA', 50.46678000, -104.41778000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1916449'),
(16824, 'Pincher Creek', 872, 'AB', 39, 'CA', 49.48328000, -113.95195000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1744046'),
(16825, 'Pincourt', 873, 'QC', 39, 'CA', 45.38338000, -73.98250000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141551'),
(16826, 'Pitt Meadows', 875, 'BC', 39, 'CA', 49.22119000, -122.68965000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1369892'),
(16827, 'Plantagenet', 866, 'ON', 39, 'CA', 45.53260000, -74.99369000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1369892'),
(16828, 'Plattsville', 866, 'ON', 39, 'CA', 43.30010000, -80.61639000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1369892'),
(16829, 'Plessisville', 873, 'QC', 39, 'CA', 46.21856000, -71.76201000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141378'),
(16830, 'Pohénégamook', 873, 'QC', 39, 'CA', 47.46315000, -69.22666000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q142284'),
(16831, 'Pointe-Calumet', 873, 'QC', 39, 'CA', 45.50008000, -73.96590000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q509329'),
(16832, 'Pointe-Claire', 873, 'QC', 39, 'CA', 45.44868000, -73.81669000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142271'),
(16833, 'Pointe-du-Lac', 873, 'QC', 39, 'CA', 48.50009000, -71.78241000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142271'),
(16834, 'Ponoka', 872, 'AB', 39, 'CA', 52.67680000, -113.58147000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1744063'),
(16835, 'Pont Rouge', 873, 'QC', 39, 'CA', 48.88332000, -72.08247000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1744063'),
(16836, 'Pont-Rouge', 873, 'QC', 39, 'CA', 46.75468000, -71.69566000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142313'),
(16837, 'Port Alberni', 875, 'BC', 39, 'CA', 49.24133000, -124.80280000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1022127'),
(16838, 'Port Colborne', 866, 'ON', 39, 'CA', 42.90012000, -79.23288000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q424704'),
(16839, 'Port Coquitlam', 875, 'BC', 39, 'CA', 49.26637000, -122.76932000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q643326'),
(16840, 'Port Hawkesbury', 874, 'NS', 39, 'CA', 45.61685000, -61.34853000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2326975'),
(16841, 'Port Hope', 866, 'ON', 39, 'CA', 44.01682000, -78.39953000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2326975'),
(16842, 'Port McNeill', 875, 'BC', 39, 'CA', 50.58716000, -127.08053000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2104736'),
(16843, 'Port Moody', 875, 'BC', 39, 'CA', 49.28124000, -122.82457000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q776261'),
(16844, 'Port Rowan', 866, 'ON', 39, 'CA', 42.61680000, -80.46638000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q776261'),
(16845, 'Port Stanley', 866, 'ON', 39, 'CA', 42.66679000, -81.21644000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7230953'),
(16846, 'Port Williams', 874, 'NS', 39, 'CA', 45.10015000, -64.41546000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7230953'),
(16847, 'Port-Cartier', 873, 'QC', 39, 'CA', 50.03339000, -66.86545000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141783'),
(16848, 'Portage la Prairie', 867, 'MB', 39, 'CA', 49.97282000, -98.29263000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1017624'),
(16849, 'Portneuf', 873, 'QC', 39, 'CA', 46.69058000, -71.89011000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142430'),
(16850, 'Powassan', 866, 'ON', 39, 'CA', 46.03340000, -79.34961000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142430'),
(16851, 'Powell River', 875, 'BC', 39, 'CA', 49.83278000, -124.52368000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q599678'),
(16852, 'Preeceville', 870, 'SK', 39, 'CA', 51.94998000, -102.66766000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1916526'),
(16853, 'Prescott', 866, 'ON', 39, 'CA', 44.71681000, -75.51604000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2420628'),
(16854, 'Prince Albert', 870, 'SK', 39, 'CA', 53.20008000, -105.76772000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q671431'),
(16855, 'Prince Edward', 866, 'ON', 39, 'CA', 44.00012000, -77.24946000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q385085'),
(16856, 'Prince George', 875, 'BC', 39, 'CA', 53.91660000, -122.75301000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q590205'),
(16857, 'Prince Rupert', 875, 'BC', 39, 'CA', 54.31507000, -130.32098000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1015639'),
(16858, 'Princeton', 875, 'BC', 39, 'CA', 49.45802000, -120.51076000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1020218'),
(16859, 'Princeville', 874, 'NS', 39, 'CA', 45.76684000, -61.29853000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1020218'),
(16860, 'Princeville', 873, 'QC', 39, 'CA', 46.17163000, -71.87462000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142516'),
(16861, 'Provost', 872, 'AB', 39, 'CA', 52.35014000, -110.26828000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1744077'),
(16862, 'Prévost', 873, 'QC', 39, 'CA', 45.86678000, -74.08251000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q141385'),
(16863, 'Puntledge', 875, 'BC', 39, 'CA', 49.66168000, -125.05686000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141385'),
(16864, 'Queenswood Heights', 866, 'ON', 39, 'CA', 45.47083000, -75.50556000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141385'),
(16865, 'Quesnel', 875, 'BC', 39, 'CA', 52.97842000, -122.49310000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2067514'),
(16866, 'Quinte West', 866, 'ON', 39, 'CA', 44.18342000, -77.56618000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3276918'),
(16867, 'Québec', 873, 'QC', 39, 'CA', 46.81228000, -71.21454000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q2145'),
(16868, 'Rainy River District', 866, 'ON', 39, 'CA', 48.49981000, -92.50031000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q943829'),
(16869, 'Rankin Inlet', 876, 'NU', 39, 'CA', 62.80906000, -92.08534000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1021766'),
(16870, 'Rawdon', 873, 'QC', 39, 'CA', 46.05007000, -73.71587000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3420451'),
(16871, 'Raymond', 872, 'AB', 39, 'CA', 49.44998000, -112.65185000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1001461'),
(16872, 'Rayside-Balfour', 866, 'ON', 39, 'CA', 46.60873000, -81.20763000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3421337'),
(16873, 'Red Deer', 872, 'AB', 39, 'CA', 52.26682000, -113.80200000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q23025'),
(16874, 'Red Lake', 866, 'ON', 39, 'CA', 51.01678000, -93.82736000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q23025'),
(16875, 'Regina', 870, 'SK', 39, 'CA', 50.45008000, -104.61780000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2123'),
(16876, 'Regina Beach', 870, 'SK', 39, 'CA', 50.78338000, -105.00112000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1916747'),
(16877, 'Regional District of Alberni-Clayoquot', 875, 'BC', 39, 'CA', 49.24962000, -125.33615000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1778727'),
(16878, 'Regional District of Central Okanagan', 875, 'BC', 39, 'CA', 49.99978000, -119.41908000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q945201'),
(16879, 'Regional Municipality of Waterloo', 866, 'ON', 39, 'CA', 43.50010000, -80.49969000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q175735'),
(16880, 'Renfrew', 866, 'ON', 39, 'CA', 45.46681000, -76.68272000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2143987'),
(16881, 'Repentigny', 873, 'QC', 39, 'CA', 45.74222000, -73.45008000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q139427'),
(16882, 'Revelstoke', 875, 'BC', 39, 'CA', 50.99712000, -118.19530000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1893229'),
(16883, 'Richelieu', 873, 'QC', 39, 'CA', 45.44336000, -73.24602000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q142306'),
(16884, 'Richibucto', 868, 'NB', 39, 'CA', 46.68073000, -64.88044000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3431443'),
(16885, 'Richmond', 875, 'BC', 39, 'CA', 49.17003000, -123.13683000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q236837'),
(16886, 'Richmond', 873, 'QC', 39, 'CA', 45.66677000, -72.14910000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q236837'),
(16887, 'Richmond', 866, 'ON', 39, 'CA', 45.18340000, -75.83266000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q14875404'),
(16888, 'Richmond Hill', 866, 'ON', 39, 'CA', 43.87111000, -79.43725000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q52998'),
(16889, 'Rideau Park', 872, 'AB', 39, 'CA', 53.47899000, -113.50470000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q52998'),
(16890, 'Ridgetown', 866, 'ON', 39, 'CA', 42.43339000, -81.89978000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q52998'),
(16891, 'Rigaud', 873, 'QC', 39, 'CA', 45.47927000, -74.30238000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3431913'),
(16892, 'Rimbey', 872, 'AB', 39, 'CA', 52.63340000, -114.23532000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q7334420'),
(16893, 'Rimouski', 873, 'QC', 39, 'CA', 48.44879000, -68.52396000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1336'),
(16894, 'Rivers', 867, 'MB', 39, 'CA', 50.03081000, -100.24029000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q138967'),
(16895, 'Rivière-Rouge', 873, 'QC', 39, 'CA', 46.41679000, -74.86596000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q142301'),
(16896, 'Rivière-du-Loup', 873, 'QC', 39, 'CA', 47.82699000, -69.54243000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q142024'),
(16897, 'Roberval', 873, 'QC', 39, 'CA', 48.51680000, -72.23244000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141954'),
(16898, 'Roblin', 867, 'MB', 39, 'CA', 51.22999000, -101.35650000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1659829'),
(16899, 'Rock Forest', 873, 'QC', 39, 'CA', 45.35699000, -71.99676000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1659829'),
(16900, 'Rockwood', 866, 'ON', 39, 'CA', 43.61899000, -80.14441000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q695157'),
(16901, 'Rocky Mountain House', 872, 'AB', 39, 'CA', 52.36683000, -114.91880000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1743575'),
(16902, 'Rosemère', 873, 'QC', 39, 'CA', 45.63338000, -73.79919000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q142196'),
(16903, 'Rosetown', 870, 'SK', 39, 'CA', 51.55010000, -108.00136000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1916875'),
(16904, 'Rossland', 875, 'BC', 39, 'CA', 49.08313000, -117.80224000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1973819'),
(16905, 'Rosthern', 870, 'SK', 39, 'CA', 52.66679000, -106.33446000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1916887'),
(16906, 'Rougemont', 873, 'QC', 39, 'CA', 45.43338000, -73.04914000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3443238'),
(16907, 'Rouyn-Noranda', 873, 'QC', 39, 'CA', 48.23656000, -79.02311000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q141804'),
(16908, 'Royston', 875, 'BC', 39, 'CA', 49.64703000, -124.94670000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1664593'),
(16909, 'Russell', 866, 'ON', 39, 'CA', 45.25010000, -75.36602000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q1664593'),
(16910, 'Sackville', 868, 'NB', 39, 'CA', 45.91875000, -64.38455000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q2028824'),
(16911, 'Sacré-Coeur', 873, 'QC', 39, 'CA', 48.22970000, -69.80061000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q3461040'),
(16912, 'Saguenay', 873, 'QC', 39, 'CA', 48.41675000, -71.06573000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q139229'),
(16913, 'Saint Andrews', 868, 'NB', 39, 'CA', 45.07370000, -67.05312000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q957700'),
(16914, 'Saint John', 868, 'NB', 39, 'CA', 45.27271000, -66.06766000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q203403'),
(16915, 'Saint-Adolphe-d\'Howard', 873, 'QC', 39, 'CA', 45.96679000, -74.33253000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q203403'),
(16916, 'Saint-Alexandre', 873, 'QC', 39, 'CA', 45.50010000, -75.74935000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q203403'),
(16917, 'Saint-Amable', 873, 'QC', 39, 'CA', 45.65008000, -73.29916000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3461735'),
(16918, 'Saint-Ambroise', 873, 'QC', 39, 'CA', 48.55009000, -71.33238000, '2019-10-05 22:35:48', '2019-10-05 22:35:48', 1, 'Q3461746'),
(16919, 'Saint-André-Avellin', 873, 'QC', 39, 'CA', 45.71680000, -75.06599000, '2019-10-05 22:35:48', '2020-05-01 17:22:39', 1, 'Q2362715'),
(16920, 'Saint-Anselme', 873, 'QC', 39, 'CA', 46.62922000, -70.97340000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3461785'),
(16921, 'Saint-Antoine', 868, 'NB', 39, 'CA', 46.36294000, -64.74985000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3461795'),
(16922, 'Saint-Antoine-de-Tilly', 873, 'QC', 39, 'CA', 46.66346000, -71.57335000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q932023'),
(16923, 'Saint-Augustin', 873, 'QC', 39, 'CA', 51.22602000, -58.65017000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q932023'),
(16924, 'Saint-Augustin-de-Desmaures', 873, 'QC', 39, 'CA', 46.74064000, -71.45131000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142102'),
(16925, 'Saint-Barnabé-Sud', 873, 'QC', 39, 'CA', 45.72977000, -72.92244000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q142102'),
(16926, 'Saint-Basile-le-Grand', 873, 'QC', 39, 'CA', 45.53338000, -73.28246000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142170'),
(16927, 'Saint-Boniface', 873, 'QC', 39, 'CA', 46.50011000, -75.98264000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142170'),
(16928, 'Saint-Bruno', 873, 'QC', 39, 'CA', 48.46679000, -71.64910000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3461928'),
(16929, 'Saint-Bruno-de-Guigues', 873, 'QC', 39, 'CA', 47.46685000, -79.43296000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3461925'),
(16930, 'Saint-Bruno-de-Montarville', 873, 'QC', 39, 'CA', 45.53341000, -73.34916000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142333'),
(16931, 'Saint-Canut', 873, 'QC', 39, 'CA', 45.71502000, -74.08376000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142333'),
(16932, 'Saint-Charles', 873, 'QC', 39, 'CA', 45.70288000, -73.55417000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142333'),
(16933, 'Saint-Constant', 873, 'QC', 39, 'CA', 45.36678000, -73.56588000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142064'),
(16934, 'Saint-Cyrille-de-Wendover', 873, 'QC', 39, 'CA', 45.93336000, -72.43241000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462025'),
(16935, 'Saint-Césaire', 873, 'QC', 39, 'CA', 45.41678000, -72.99914000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q142044'),
(16936, 'Saint-Côme-Linière', 873, 'QC', 39, 'CA', 46.06677000, -70.51573000, '2019-10-05 22:35:49', '2022-03-13 15:05:22', 1, 'Q3462038'),
(16937, 'Saint-Damase', 873, 'QC', 39, 'CA', 45.53341000, -72.99914000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462038'),
(16938, 'Saint-Denis-sur-Richelieu', 873, 'QC', 39, 'CA', 45.78338000, -73.14915000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q788410'),
(16939, 'Saint-Donat-de-Montcalm', 873, 'QC', 39, 'CA', 46.31868000, -74.22171000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q788410'),
(16940, 'Saint-Elzéar', 873, 'QC', 39, 'CA', 45.60338000, -73.72698000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q788410'),
(16941, 'Saint-Eustache', 873, 'QC', 39, 'CA', 45.56500000, -73.90554000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141505'),
(16942, 'Saint-Félicien', 873, 'QC', 39, 'CA', 48.65007000, -72.44906000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q141511'),
(16943, 'Saint-Félix-de-Valois', 873, 'QC', 39, 'CA', 46.16977000, -73.42525000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q141511'),
(16944, 'Saint-Gabriel', 873, 'QC', 39, 'CA', 46.30007000, -73.38245000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141371'),
(16945, 'Saint-Georges', 873, 'QC', 39, 'CA', 46.11353000, -70.66526000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q143227'),
(16946, 'Saint-Germain-de-Grantham', 873, 'QC', 39, 'CA', 45.83337000, -72.56582000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462266'),
(16947, 'Saint-Gédéon', 873, 'QC', 39, 'CA', 48.50009000, -71.76581000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3462366'),
(16948, 'Saint-Henri', 873, 'QC', 39, 'CA', 46.69314000, -71.06927000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462366'),
(16949, 'Saint-Hippolyte', 873, 'QC', 39, 'CA', 45.93338000, -74.01590000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462419'),
(16950, 'Saint-Honoré', 873, 'QC', 39, 'CA', 48.53338000, -71.08236000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3462419'),
(16951, 'Saint-Hyacinthe', 873, 'QC', 39, 'CA', 45.63076000, -72.95699000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141873'),
(16952, 'Saint-Isidore', 873, 'QC', 39, 'CA', 47.43345000, -79.29965000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141873'),
(16953, 'Saint-Jacques-le-Mineur', 873, 'QC', 39, 'CA', 45.28338000, -73.41587000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462475'),
(16954, 'Saint-Jean-Baptiste', 873, 'QC', 39, 'CA', 45.38060000, -74.01210000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462475'),
(16955, 'Saint-Jean-sur-Richelieu', 873, 'QC', 39, 'CA', 45.30713000, -73.26259000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141977'),
(16956, 'Saint-Joseph', 873, 'QC', 39, 'CA', 45.95817000, -73.22025000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141977'),
(16957, 'Saint-Joseph-de-Beauce', 873, 'QC', 39, 'CA', 46.30000000, -70.86667000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141613'),
(16958, 'Saint-Joseph-de-Coleraine', 873, 'QC', 39, 'CA', 45.96677000, -71.36577000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462601'),
(16959, 'Saint-Joseph-du-Lac', 873, 'QC', 39, 'CA', 45.53338000, -73.99920000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462609'),
(16960, 'Saint-Jérôme', 873, 'QC', 39, 'CA', 45.78036000, -74.00365000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q141724'),
(16961, 'Saint-Lambert-de-Lauzon', 873, 'QC', 39, 'CA', 46.58624000, -71.20892000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462681'),
(16962, 'Saint-Laurent', 873, 'QC', 39, 'CA', 45.50008000, -73.66585000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2117999'),
(16963, 'Saint-Lazare', 873, 'QC', 39, 'CA', 45.40008000, -74.13256000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142426'),
(16964, 'Saint-Liboire', 873, 'QC', 39, 'CA', 45.65068000, -72.76348000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462737'),
(16965, 'Saint-Lin-Laurentides', 873, 'QC', 39, 'CA', 45.85008000, -73.76588000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142072'),
(16966, 'Saint-Léonard', 873, 'QC', 39, 'CA', 45.58773000, -73.59501000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q1325316'),
(16967, 'Saint-Léonard', 868, 'NB', 39, 'CA', 47.16317000, -67.92460000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q985187'),
(16968, 'Saint-Léonard-d\'Aston', 873, 'QC', 39, 'CA', 46.10006000, -72.36580000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q2392099'),
(16969, 'Saint-Marc-des-Carrières', 873, 'QC', 39, 'CA', 46.68335000, -72.04910000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q142320'),
(16970, 'Saint-Mathieu', 873, 'QC', 39, 'CA', 45.31678000, -73.51587000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462944'),
(16971, 'Saint-Michel', 873, 'QC', 39, 'CA', 45.56758000, -73.62168000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462944'),
(16972, 'Saint-Michel-des-Saints', 873, 'QC', 39, 'CA', 46.67702000, -73.91881000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462998'),
(16973, 'Saint-Nazaire', 873, 'QC', 39, 'CA', 48.58944000, -71.55247000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463040'),
(16974, 'Saint-Norbert', 873, 'QC', 39, 'CA', 46.16949000, -73.31494000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463040'),
(16975, 'Saint-Pacôme', 873, 'QC', 39, 'CA', 47.40457000, -69.95025000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3463040'),
(16976, 'Saint-Pascal', 873, 'QC', 39, 'CA', 47.51813000, -69.80301000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142451'),
(16977, 'Saint-Philippe-de-La Prairie', 873, 'QC', 39, 'CA', 45.35723000, -73.47706000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463156'),
(16978, 'Saint-Pie', 873, 'QC', 39, 'CA', 45.50277000, -72.90890000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463156'),
(16979, 'Saint-Pierre-les-Becquets', 873, 'QC', 39, 'CA', 46.50005000, -72.19910000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463195'),
(16980, 'Saint-Prime', 873, 'QC', 39, 'CA', 48.58339000, -72.33244000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463229'),
(16981, 'Saint-Raphaël', 873, 'QC', 39, 'CA', 46.25011000, -76.01605000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3463229'),
(16982, 'Saint-Raymond', 873, 'QC', 39, 'CA', 45.46698000, -73.60948000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463229'),
(16983, 'Saint-Rémi', 873, 'QC', 39, 'CA', 45.26678000, -73.61588000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q142504'),
(16984, 'Saint-Rémi-de-Tingwick', 873, 'QC', 39, 'CA', 45.86677000, -71.81581000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3463306'),
(16985, 'Saint-Sauveur', 873, 'QC', 39, 'CA', 45.88686000, -74.17943000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q143360'),
(16986, 'Saint-Sauveur-des-Monts', 873, 'QC', 39, 'CA', 45.90008000, -74.16591000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q143360'),
(16987, 'Saint-Siméon', 873, 'QC', 39, 'CA', 47.84431000, -69.87837000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q143360'),
(16988, 'Saint-Thomas', 873, 'QC', 39, 'CA', 46.01677000, -73.34915000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3463443'),
(16989, 'Saint-Tite', 873, 'QC', 39, 'CA', 46.73336000, -72.56581000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142386'),
(16990, 'Saint-Victor', 873, 'QC', 39, 'CA', 45.61118000, -73.51527000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142386'),
(16991, 'Saint-Zotique', 873, 'QC', 39, 'CA', 45.25009000, -74.24924000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142386'),
(16992, 'Saint-Édouard', 873, 'QC', 39, 'CA', 45.23338000, -73.51588000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3463573'),
(16993, 'Saint-Éphrem-de-Beauce', 873, 'QC', 39, 'CA', 46.06677000, -70.94905000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3463593'),
(16994, 'Sainte Catherine de la Jacques Cartier', 873, 'QC', 39, 'CA', 46.85244000, -71.62056000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141565'),
(16995, 'Sainte-Adèle', 873, 'QC', 39, 'CA', 45.95008000, -74.13251000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q142560'),
(16996, 'Sainte-Agathe-des-Monts', 873, 'QC', 39, 'CA', 46.05009000, -74.28252000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141738'),
(16997, 'Sainte-Anne-de-Bellevue', 873, 'QC', 39, 'CA', 45.40618000, -73.94560000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141500'),
(16998, 'Sainte-Anne-des-Monts', 873, 'QC', 39, 'CA', 49.12402000, -66.49243000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q51557'),
(16999, 'Sainte-Anne-des-Plaines', 873, 'QC', 39, 'CA', 45.76468000, -73.81156000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141492'),
(17000, 'Sainte-Béatrix', 873, 'QC', 39, 'CA', 46.20007000, -73.61587000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3464195'),
(17001, 'Sainte-Catherine', 873, 'QC', 39, 'CA', 45.40008000, -73.58248000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142190'),
(17002, 'Sainte-Croix', 873, 'QC', 39, 'CA', 45.13368000, -72.80083000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142190'),
(17003, 'Sainte-Julie', 873, 'QC', 39, 'CA', 45.58338000, -73.33246000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142156'),
(17004, 'Sainte-Julienne', 873, 'QC', 39, 'CA', 45.96677000, -73.71587000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3464313'),
(17005, 'Sainte-Madeleine', 873, 'QC', 39, 'CA', 45.60008000, -73.09914000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3464332'),
(17006, 'Sainte-Marie', 873, 'QC', 39, 'CA', 46.43401000, -71.01168000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141652'),
(17007, 'Sainte-Marthe-sur-le-Lac', 873, 'QC', 39, 'CA', 45.53338000, -73.93250000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142436'),
(17008, 'Sainte-Martine', 873, 'QC', 39, 'CA', 45.25008000, -73.79919000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2339439'),
(17009, 'Sainte-Sophie', 873, 'QC', 39, 'CA', 45.81678000, -73.89919000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q684201'),
(17010, 'Sainte-Thècle', 873, 'QC', 39, 'CA', 46.81676000, -72.49911000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q3464432'),
(17011, 'Sainte-Thérèse', 873, 'QC', 39, 'CA', 45.63922000, -73.82757000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q142087'),
(17012, 'Sainte-Élisabeth', 873, 'QC', 39, 'CA', 46.09502000, -73.35176000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q142087'),
(17013, 'Salaberry-de-Valleyfield', 873, 'QC', 39, 'CA', 45.25008000, -74.13253000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142039'),
(17014, 'Salisbury', 868, 'NB', 39, 'CA', 46.03905000, -65.04628000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3469820'),
(17015, 'Salluit', 873, 'QC', 39, 'CA', 62.20411000, -75.64344000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1669213'),
(17016, 'Salmo', 875, 'BC', 39, 'CA', 49.19986000, -117.26890000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2957546'),
(17017, 'Salmon Arm', 875, 'BC', 39, 'CA', 50.69980000, -119.30237000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2957546'),
(17018, 'Salt Spring Island', 875, 'BC', 39, 'CA', 48.81852000, -123.49061000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2957546'),
(17019, 'Saltair', 875, 'BC', 39, 'CA', 48.94963000, -123.76939000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2957546'),
(17020, 'Sarnia', 866, 'ON', 39, 'CA', 42.97866000, -82.40407000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q34106'),
(17021, 'Saskatoon', 870, 'SK', 39, 'CA', 52.13238000, -106.66892000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q10566'),
(17022, 'Sault Ste. Marie', 866, 'ON', 39, 'CA', 46.51677000, -84.33325000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q463165'),
(17023, 'Scarborough', 866, 'ON', 39, 'CA', 43.77223000, -79.25666000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q463165'),
(17024, 'Seaforth', 866, 'ON', 39, 'CA', 43.55009000, -81.39976000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q7440408'),
(17025, 'Sechelt', 875, 'BC', 39, 'CA', 49.47512000, -123.75903000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1259274'),
(17026, 'Selkirk', 867, 'MB', 39, 'CA', 50.14360000, -96.88452000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1018239'),
(17027, 'Senneterre', 873, 'QC', 39, 'CA', 48.39302000, -77.23951000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q142379'),
(17028, 'Sept-Îles', 873, 'QC', 39, 'CA', 50.20011000, -66.38208000, '2019-10-05 22:35:49', '2020-05-01 17:22:39', 1, 'Q141699'),
(17029, 'Sexsmith', 872, 'AB', 39, 'CA', 55.34998000, -118.78602000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1008802'),
(17030, 'Shannon', 873, 'QC', 39, 'CA', 46.88026000, -71.51464000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3481424'),
(17031, 'Shaunavon', 870, 'SK', 39, 'CA', 49.65005000, -108.41810000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1916970'),
(17032, 'Shawinigan', 873, 'QC', 39, 'CA', 46.56675000, -72.74913000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q141980'),
(17033, 'Shawville', 873, 'QC', 39, 'CA', 45.60011000, -76.48270000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3481643'),
(17034, 'Shediac', 868, 'NB', 39, 'CA', 46.21981000, -64.54107000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1009713'),
(17035, 'Shediac Bridge-Shediac River', 868, 'NB', 39, 'CA', 46.26886000, -64.60047000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1009713'),
(17036, 'Shelburne', 866, 'ON', 39, 'CA', 44.07870000, -80.20408000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q4859980'),
(17037, 'Shelburne', 874, 'NS', 39, 'CA', 43.76325000, -65.32355000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1693101'),
(17038, 'Shellbrook', 870, 'SK', 39, 'CA', 53.21679000, -106.40109000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1916980'),
(17039, 'Sherbrooke', 873, 'QC', 39, 'CA', 45.40008000, -71.89908000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q139473'),
(17040, 'Sherwood Park', 872, 'AB', 39, 'CA', 53.51684000, -113.31870000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q139473'),
(17041, 'Shilo', 867, 'MB', 39, 'CA', 49.80509000, -99.63175000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q139473'),
(17042, 'Shippagan', 868, 'NB', 39, 'CA', 47.74424000, -64.70804000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q139473'),
(17043, 'Sicamous', 875, 'BC', 39, 'CA', 50.83312000, -118.98565000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q7507390'),
(17044, 'Silver Berry', 872, 'AB', 39, 'CA', 53.45787000, -113.38170000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q7507390'),
(17045, 'Simcoe', 866, 'ON', 39, 'CA', 42.83340000, -80.29967000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3054408'),
(17046, 'Sioux Lookout', 866, 'ON', 39, 'CA', 50.06676000, -91.98358000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3054408'),
(17047, 'Six Mile', 875, 'BC', 39, 'CA', 48.45767000, -123.46088000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3054408'),
(17048, 'Skatepark', 866, 'ON', 39, 'CA', 44.25122000, -76.94424000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3054408'),
(17049, 'Slave Lake', 872, 'AB', 39, 'CA', 55.28344000, -114.76896000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q163977'),
(17050, 'Smithers', 875, 'BC', 39, 'CA', 54.78036000, -127.17428000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1009247'),
(17051, 'Smiths Falls', 866, 'ON', 39, 'CA', 44.90452000, -76.02333000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1938153'),
(17052, 'Smoky Lake', 872, 'AB', 39, 'CA', 54.11687000, -112.46863000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1744205'),
(17053, 'Sooke', 875, 'BC', 39, 'CA', 48.37463000, -123.72762000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q993727'),
(17054, 'Sorel-Tracy', 873, 'QC', 39, 'CA', 46.04178000, -73.11358000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q993727'),
(17055, 'Souris', 871, 'PE', 39, 'CA', 46.35010000, -62.24862000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2167346'),
(17056, 'Souris', 867, 'MB', 39, 'CA', 49.61720000, -100.26120000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q487130'),
(17057, 'South Huron', 866, 'ON', 39, 'CA', 43.31679000, -81.51647000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q4397'),
(17058, 'South Pender Harbour', 875, 'BC', 39, 'CA', 49.62202000, -124.02484000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q4397'),
(17059, 'South River', 866, 'ON', 39, 'CA', 45.83340000, -79.38293000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3492521'),
(17060, 'Sparwood', 875, 'BC', 39, 'CA', 49.73332000, -114.88532000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q7573961'),
(17061, 'Spirit River', 872, 'AB', 39, 'CA', 55.78327000, -118.83607000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1744192'),
(17062, 'Springbrook', 872, 'AB', 39, 'CA', 52.17920000, -113.87981000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1744192'),
(17063, 'Springdale', 877, 'NL', 39, 'CA', 49.49995000, -56.06492000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q7580835'),
(17064, 'Springhill', 874, 'NS', 39, 'CA', 45.65015000, -64.04873000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1000316'),
(17065, 'Spruce Grove', 872, 'AB', 39, 'CA', 53.53344000, -113.91874000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1013456'),
(17066, 'St-Jean-Port-Joli', 873, 'QC', 39, 'CA', 47.21418000, -70.26969000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462513'),
(17067, 'St. Adolphe', 867, 'MB', 39, 'CA', 49.67440000, -97.11124000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3462513'),
(17068, 'St. Albert', 872, 'AB', 39, 'CA', 53.63344000, -113.63533000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q939127'),
(17069, 'St. Anthony', 877, 'NL', 39, 'CA', 51.37039000, -55.59743000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2317218'),
(17070, 'St. Catharines', 866, 'ON', 39, 'CA', 43.17126000, -79.24267000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q126805'),
(17071, 'St. George', 866, 'ON', 39, 'CA', 43.24495000, -80.25144000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q126805'),
(17072, 'St. John\'s', 877, 'NL', 39, 'CA', 47.56494000, -52.70931000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2082'),
(17073, 'St. Thomas', 866, 'ON', 39, 'CA', 42.77361000, -81.18038000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2078072'),
(17074, 'Starlight Village', 868, 'NB', 39, 'CA', 45.88308000, -66.76905000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q2078072'),
(17075, 'Steinbach', 867, 'MB', 39, 'CA', 49.52579000, -96.68451000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1026567'),
(17076, 'Stephenville', 877, 'NL', 39, 'CA', 48.55001000, -58.58180000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q626279'),
(17077, 'Stephenville Crossing', 877, 'NL', 39, 'CA', 48.50001000, -58.43180000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q7611009'),
(17078, 'Stettler', 872, 'AB', 39, 'CA', 52.31683000, -112.71861000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1746019'),
(17079, 'Stirling', 866, 'ON', 39, 'CA', 44.30012000, -77.54948000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1746019'),
(17080, 'Stonewall', 867, 'MB', 39, 'CA', 50.13441000, -97.32676000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q393324'),
(17081, 'Stoney Point', 866, 'ON', 39, 'CA', 44.26681000, -79.53292000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q393324'),
(17082, 'Stony Plain', 872, 'AB', 39, 'CA', 53.53343000, -114.00205000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1744021'),
(17083, 'Stratford', 866, 'ON', 39, 'CA', 43.36679000, -80.94972000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q740756'),
(17084, 'Strathmore', 872, 'AB', 39, 'CA', 51.05011000, -113.38523000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q1018325'),
(17085, 'Sudbury', 866, 'ON', 39, 'CA', 47.16679000, -81.99980000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q383600'),
(17086, 'Summerland', 875, 'BC', 39, 'CA', 49.59977000, -119.66911000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q984379'),
(17087, 'Summerside', 871, 'PE', 39, 'CA', 46.39593000, -63.78762000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q863058'),
(17088, 'Sundre', 872, 'AB', 39, 'CA', 51.80010000, -114.63532000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q3243949'),
(17089, 'Surrey', 875, 'BC', 39, 'CA', 49.10635000, -122.82509000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q390583'),
(17090, 'Sussex', 868, 'NB', 39, 'CA', 45.72266000, -65.50663000, '2019-10-05 22:35:49', '2019-10-05 22:35:49', 1, 'Q984084'),
(17091, 'Sutton', 873, 'QC', 39, 'CA', 45.10008000, -72.61582000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q142521'),
(17092, 'Swan Hills', 872, 'AB', 39, 'CA', 54.71681000, -115.40226000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q928787'),
(17093, 'Swan River', 867, 'MB', 39, 'CA', 52.10580000, -101.26759000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q487931'),
(17094, 'Swift Current', 870, 'SK', 39, 'CA', 50.28337000, -107.80135000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q619937'),
(17095, 'Sydney', 874, 'NS', 39, 'CA', 46.13510000, -60.18310000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q932261'),
(17096, 'Sydney Mines', 874, 'NS', 39, 'CA', 46.23669000, -60.21767000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q7660041'),
(17097, 'Sylvan Lake', 872, 'AB', 39, 'CA', 52.31100000, -114.08375000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q2046751'),
(17098, 'Taber', 872, 'AB', 39, 'CA', 49.78703000, -112.14603000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1746037'),
(17099, 'Tamarack', 872, 'AB', 39, 'CA', 53.46441000, -113.36235000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1746037'),
(17100, 'Tavistock', 866, 'ON', 39, 'CA', 43.31679000, -80.83302000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1746037'),
(17101, 'Temiskaming Shores', 866, 'ON', 39, 'CA', 47.49376000, -79.71529000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1982768'),
(17102, 'Terrace', 875, 'BC', 39, 'CA', 54.51634000, -128.60345000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1021037'),
(17103, 'Terrasse-des-Pins', 873, 'QC', 39, 'CA', 45.86449000, -74.06627000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1021037'),
(17104, 'Terrebonne', 873, 'QC', 39, 'CA', 45.70004000, -73.64732000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q139190'),
(17105, 'The Pas', 867, 'MB', 39, 'CA', 53.82509000, -101.25413000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q61422'),
(17106, 'Thessalon', 866, 'ON', 39, 'CA', 46.25006000, -83.56660000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q998994'),
(17107, 'Thetford-Mines', 873, 'QC', 39, 'CA', 46.09371000, -71.30539000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q998994'),
(17108, 'Thompson', 867, 'MB', 39, 'CA', 55.74350000, -97.85579000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q991295'),
(17109, 'Thorold', 866, 'ON', 39, 'CA', 43.11682000, -79.19958000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q448120'),
(17110, 'Three Hills', 872, 'AB', 39, 'CA', 51.70012000, -113.26863000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1743795'),
(17111, 'Thunder Bay', 866, 'ON', 39, 'CA', 48.38202000, -89.25018000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q34116'),
(17112, 'Thunder Bay District', 866, 'ON', 39, 'CA', 49.50011000, -88.50004000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q385689'),
(17113, 'Thurso', 873, 'QC', 39, 'CA', 45.60010000, -75.24931000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q142370'),
(17114, 'Timiskaming District', 866, 'ON', 39, 'CA', 47.75016000, -80.33303000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q385780'),
(17115, 'Timmins', 866, 'ON', 39, 'CA', 48.46686000, -81.33312000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q991310'),
(17116, 'Tisdale', 870, 'SK', 39, 'CA', 52.85002000, -104.05096000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1917345'),
(17117, 'Tobermory', 866, 'ON', 39, 'CA', 45.25007000, -81.66647000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q7811572'),
(17118, 'Tofield', 872, 'AB', 39, 'CA', 53.36684000, -112.66862000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q2146186'),
(17119, 'Tofino', 875, 'BC', 39, 'CA', 49.15314000, -125.90744000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q462564'),
(17120, 'Torbay', 877, 'NL', 39, 'CA', 47.66659000, -52.73135000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q7825435'),
(17121, 'Toronto', 866, 'ON', 39, 'CA', 43.70011000, -79.41630000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q172'),
(17122, 'Toronto county', 866, 'ON', 39, 'CA', 43.69655000, -79.42909000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q172'),
(17123, 'Tottenham', 866, 'ON', 39, 'CA', 44.02437000, -79.80553000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q172'),
(17124, 'Tracadie-Sheila', 868, 'NB', 39, 'CA', 47.51444000, -64.91806000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q2320312'),
(17125, 'Trail', 875, 'BC', 39, 'CA', 49.09983000, -117.70223000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1808502'),
(17126, 'Trois-Rivières', 873, 'QC', 39, 'CA', 46.34515000, -72.54770000, '2019-10-05 22:35:50', '2020-05-01 17:22:39', 1, 'Q44012'),
(17127, 'Truro', 874, 'NS', 39, 'CA', 45.36685000, -63.26538000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q283064'),
(17128, 'Tsawwassen', 875, 'BC', 39, 'CA', 49.01667000, -123.08333000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q283064'),
(17129, 'Tumbler Ridge', 875, 'BC', 39, 'CA', 55.13027000, -120.99415000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q7852567'),
(17130, 'Tweed', 866, 'ON', 39, 'CA', 44.47512000, -77.31616000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q7852567'),
(17131, 'Two Hills', 872, 'AB', 39, 'CA', 53.71686000, -111.75181000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1746071'),
(17132, 'Témiscaming', 873, 'QC', 39, 'CA', 46.72122000, -79.09712000, '2019-10-05 22:35:50', '2020-05-01 17:22:39', 1, 'Q142174'),
(17133, 'Ucluelet', 875, 'BC', 39, 'CA', 48.94153000, -125.54635000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q994047'),
(17134, 'Unity', 870, 'SK', 39, 'CA', 52.45014000, -109.16816000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1917375'),
(17135, 'Upper Island Cove', 877, 'NL', 39, 'CA', 47.64989000, -53.21478000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q7898706'),
(17136, 'Uxbridge', 866, 'ON', 39, 'CA', 44.10012000, -79.11628000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q386959'),
(17137, 'Val-David', 873, 'QC', 39, 'CA', 46.03338000, -74.21592000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3553367'),
(17138, 'Val-Morin', 873, 'QC', 39, 'CA', 46.00008000, -74.18251000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1667428'),
(17139, 'Val-d\'Or', 873, 'QC', 39, 'CA', 48.09740000, -77.79737000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q141937'),
(17140, 'Val-des-Monts', 873, 'QC', 39, 'CA', 45.65010000, -75.66604000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3553385'),
(17141, 'Valcourt', 873, 'QC', 39, 'CA', 45.50008000, -72.31581000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q141813'),
(17142, 'Valley East', 866, 'ON', 39, 'CA', 46.66773000, -81.00028000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q141813'),
(17143, 'Valleyview', 872, 'AB', 39, 'CA', 55.06673000, -117.28585000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1743736'),
(17144, 'Vallée-Jonction', 873, 'QC', 39, 'CA', 46.37441000, -70.91881000, '2019-10-05 22:35:50', '2020-05-01 17:22:39', 1, 'Q3553918'),
(17145, 'Vancouver', 875, 'BC', 39, 'CA', 49.24966000, -123.11934000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q24639'),
(17146, 'Vanderhoof', 875, 'BC', 39, 'CA', 54.01657000, -124.01982000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q908439'),
(17147, 'Vanier', 866, 'ON', 39, 'CA', 45.43990000, -75.66498000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3554592'),
(17148, 'Varennes', 873, 'QC', 39, 'CA', 45.68338000, -73.43246000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q142014'),
(17149, 'Vaudreuil-Dorion', 873, 'QC', 39, 'CA', 45.40008000, -74.03251000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q141711'),
(17150, 'Vaughan', 866, 'ON', 39, 'CA', 43.83610000, -79.49827000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q44013'),
(17151, 'Vegreville', 872, 'AB', 39, 'CA', 53.50015000, -112.05182000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1276220'),
(17152, 'Venise-en-Québec', 873, 'QC', 39, 'CA', 45.08338000, -73.13245000, '2019-10-05 22:35:50', '2020-05-01 17:22:39', 1, 'Q2512941'),
(17153, 'Verchères', 873, 'QC', 39, 'CA', 45.78338000, -73.34916000, '2019-10-05 22:35:50', '2020-05-01 17:22:39', 1, 'Q2514459'),
(17154, 'Vermilion', 872, 'AB', 39, 'CA', 53.35409000, -110.85849000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3845986'),
(17155, 'Vernon', 875, 'BC', 39, 'CA', 50.26729000, -119.27337000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q985671'),
(17156, 'Victoria', 875, 'BC', 39, 'CA', 48.43590000, -123.35155000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q2132'),
(17157, 'Victoriaville', 873, 'QC', 39, 'CA', 46.05007000, -71.96579000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q141731'),
(17158, 'Viking', 872, 'AB', 39, 'CA', 53.10014000, -111.76844000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3558263'),
(17159, 'Ville-Marie', 873, 'QC', 39, 'CA', 47.33345000, -79.43297000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q141645'),
(17160, 'Vineland', 866, 'ON', 39, 'CA', 43.15012000, -79.39960000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q7932434'),
(17161, 'Virden', 867, 'MB', 39, 'CA', 49.85080000, -100.93262000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q75861'),
(17162, 'Virgil', 866, 'ON', 39, 'CA', 43.21682000, -79.13288000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q75861'),
(17163, 'Vulcan', 872, 'AB', 39, 'CA', 50.40008000, -113.25189000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1746129'),
(17164, 'Wabana', 877, 'NL', 39, 'CA', 47.63319000, -52.94806000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3303444'),
(17165, 'Wadena', 870, 'SK', 39, 'CA', 51.94999000, -103.80102000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1917448'),
(17166, 'Wainwright', 872, 'AB', 39, 'CA', 52.83482000, -110.85342000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q835304'),
(17167, 'Wakefield', 873, 'QC', 39, 'CA', 45.66680000, -75.83265000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q835304'),
(17168, 'Walnut Grove', 875, 'BC', 39, 'CA', 49.16473000, -122.64042000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q835304'),
(17169, 'Walpole Island', 866, 'ON', 39, 'CA', 42.61520000, -82.51398000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q835304'),
(17170, 'Warman', 870, 'SK', 39, 'CA', 52.31679000, -106.56791000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q1904521'),
(17171, 'Warwick', 873, 'QC', 39, 'CA', 45.95007000, -71.98240000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q142397'),
(17172, 'Wasaga Beach', 866, 'ON', 39, 'CA', 44.51680000, -80.01637000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3047234'),
(17173, 'Waskaganish', 873, 'QC', 39, 'CA', 51.48333000, -78.75000000, '2019-10-05 22:35:50', '2019-10-05 22:35:50', 1, 'Q3047234');

