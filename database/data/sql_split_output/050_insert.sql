INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(24832, 'Ebersbach', 3021, 'SN', 82, 'DE', 51.00762000, 14.58621000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q157480'),
(24833, 'Ebersbach an der Fils', 3006, 'BW', 82, 'DE', 48.71600000, 9.52360000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q80844'),
(24834, 'Ebersbach-Musbach', 3006, 'BW', 82, 'DE', 47.96667000, 9.58333000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q533779'),
(24835, 'Ebersberg', 3009, '<PERSON><PERSON>', 82, '<PERSON>', 48.07710000, 11.97063000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q253003'),
(24836, 'Ebersdorf', 3008, 'NI', 82, 'DE', 53.52514000, 9.04897000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q253003'),
(24837, 'Ebersdorf', 3009, 'BY', 82, 'DE', 50.32898000, 11.15266000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q31981839'),
(24838, 'Eberstadt', 3006, 'BW', 82, 'DE', 49.18028000, 9.32111000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q31981839'),
(24839, 'Eberswalde', 3013, 'BB', 82, 'DE', 52.83492000, 13.81951000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q9306'),
(24840, 'Ebertsheim', 3019, 'RP', 82, 'DE', 49.56833000, 8.10861000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q22532'),
(24841, 'Ebhausen', 3006, 'BW', 82, 'DE', 48.58333000, 8.68333000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q536887'),
(24842, 'Ebnath', 3009, 'BY', 82, 'DE', 49.95000000, 11.93333000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q548598'),
(24843, 'Ebringen', 3006, 'BW', 82, 'DE', 47.95780000, 7.77652000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q527868'),
(24844, 'Ebstorf', 3008, 'NI', 82, 'DE', 53.02785000, 10.41839000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q659643'),
(24845, 'Echem', 3008, 'NI', 82, 'DE', 53.33333000, 10.53333000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q689687'),
(24846, 'Eching', 3009, 'BY', 82, 'DE', 48.30000000, 11.61667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q254001'),
(24847, 'Echzell', 3018, 'HE', 82, 'DE', 50.38888000, 8.88605000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q567565'),
(24848, 'Eckartsberga', 3011, 'ST', 82, 'DE', 51.12380000, 11.56045000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q530107'),
(24849, 'Eckernförde', 3005, 'SH', 82, 'DE', 54.46854000, 9.83824000, '2019-10-05 22:40:44', '2020-05-01 17:22:49', 1, 'Q490453'),
(24850, 'Eckersdorf', 3009, 'BY', 82, 'DE', 50.02874000, 11.39611000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q31982566'),
(24851, 'Eddelak', 3005, 'SH', 82, 'DE', 53.95000000, 9.15000000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q551677'),
(24852, 'Edderitz', 3011, 'ST', 82, 'DE', 51.69933000, 11.93712000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q688276'),
(24853, 'Edelsfeld', 3009, 'BY', 82, 'DE', 49.57644000, 11.69589000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q520907'),
(24854, 'Edemissen', 3008, 'NI', 82, 'DE', 52.38702000, 10.26140000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q629287'),
(24855, 'Edenkoben', 3019, 'RP', 82, 'DE', 49.28393000, 8.12714000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q537985'),
(24856, 'Ederheim', 3009, 'BY', 82, 'DE', 48.80827000, 10.46609000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q504113'),
(24857, 'Edersleben', 3011, 'ST', 82, 'DE', 51.41667000, 11.28333000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q504113'),
(24858, 'Edesheim', 3019, 'RP', 82, 'DE', 49.26333000, 8.13500000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q23022'),
(24859, 'Edewecht', 3008, 'NI', 82, 'DE', 53.12699000, 7.98406000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q503410'),
(24860, 'Ediger-Eller', 3019, 'RP', 82, 'DE', 50.11667000, 7.15000000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q547805'),
(24861, 'Edingen-Neckarhausen', 3006, 'BW', 82, 'DE', 49.45722000, 8.60639000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q547805'),
(24862, 'Effelder', 3015, 'TH', 82, 'DE', 51.23948000, 10.24778000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q547805'),
(24863, 'Effeltrich', 3009, 'BY', 82, 'DE', 49.65929000, 11.09319000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q503233'),
(24864, 'Efringen-Kirchen', 3006, 'BW', 82, 'DE', 47.65000000, 7.56667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q61640'),
(24865, 'Egeln', 3011, 'ST', 82, 'DE', 51.94384000, 11.43265000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q518194'),
(24866, 'Egelsbach', 3018, 'HE', 82, 'DE', 49.96792000, 8.66341000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q622212'),
(24867, 'Egenhausen', 3006, 'BW', 82, 'DE', 48.56667000, 8.61667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q622212'),
(24868, 'Egenhofen', 3009, 'BY', 82, 'DE', 48.28333000, 11.16667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q31983901'),
(24869, 'Egestorf', 3008, 'NI', 82, 'DE', 52.28568000, 9.51676000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q1297333'),
(24870, 'Egg an der Günz', 3009, 'BY', 82, 'DE', 48.08714000, 10.28495000, '2019-10-05 22:40:44', '2020-05-01 17:22:47', 1, 'Q120620'),
(24871, 'Eggebek', 3005, 'SH', 82, 'DE', 54.61667000, 9.36667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q557146'),
(24872, 'Eggenfelden', 3009, 'BY', 82, 'DE', 48.40509000, 12.75752000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q255201'),
(24873, 'Eggenstein-Leopoldshafen', 3006, 'BW', 82, 'DE', 49.09006000, 8.39879000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q520583'),
(24874, 'Eggenthal', 3009, 'BY', 82, 'DE', 47.91667000, 10.51667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q516289'),
(24875, 'Eggermühlen', 3008, 'NI', 82, 'DE', 52.56667000, 7.81667000, '2019-10-05 22:40:44', '2020-05-01 17:22:48', 1, 'Q683212'),
(24876, 'Eggersdorf', 3011, 'ST', 82, 'DE', 51.97621000, 11.70971000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q704442'),
(24877, 'Eggesin', 3007, 'MV', 82, 'DE', 53.67973000, 14.07992000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q572034'),
(24878, 'Eggingen', 3006, 'BW', 82, 'DE', 47.70000000, 8.40000000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q179890'),
(24879, 'Egglham', 3009, 'BY', 82, 'DE', 48.52902000, 13.05402000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q529310'),
(24880, 'Egglkofen', 3009, 'BY', 82, 'DE', 48.40000000, 12.45000000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q517499'),
(24881, 'Eggolsheim', 3009, 'BY', 82, 'DE', 49.76961000, 11.05701000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q502812'),
(24882, 'Eggstätt', 3009, 'BY', 82, 'DE', 47.92698000, 12.37919000, '2019-10-05 22:40:44', '2020-05-01 17:22:47', 1, 'Q551667'),
(24883, 'Eging', 3009, 'BY', 82, 'DE', 48.46328000, 12.18957000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q551667'),
(24884, 'Egling', 3009, 'BY', 82, 'DE', 47.92316000, 11.50517000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q264025'),
(24885, 'Egloffstein', 3009, 'BY', 82, 'DE', 49.70182000, 11.25749000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q503026'),
(24886, 'Egmating', 3009, 'BY', 82, 'DE', 48.00405000, 11.79528000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q503026'),
(24887, 'Ehekirchen', 3009, 'BY', 82, 'DE', 48.63333000, 11.10000000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q504009'),
(24888, 'Ehingen', 3006, 'BW', 82, 'DE', 48.28259000, 9.72749000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q158523'),
(24889, 'Ehingen', 3009, 'BY', 82, 'DE', 48.60000000, 10.80000000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q158523'),
(24890, 'Ehlscheid', 3019, 'RP', 82, 'DE', 50.51448000, 7.46655000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q586033'),
(24891, 'Ehningen', 3006, 'BW', 82, 'DE', 48.65882000, 8.94124000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q314758'),
(24892, 'Ehrenburg', 3008, 'NI', 82, 'DE', 52.75000000, 8.70000000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q314758'),
(24893, 'Ehrenfriedersdorf', 3021, 'SN', 82, 'DE', 50.64934000, 12.97009000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q57876'),
(24894, 'Ehringshausen', 3018, 'HE', 82, 'DE', 50.60000000, 8.38333000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q633810'),
(24895, 'Eibau', 3021, 'SN', 82, 'DE', 50.98272000, 14.66214000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q502524'),
(24896, 'Eibelstadt', 3009, 'BY', 82, 'DE', 49.72392000, 9.99962000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q481834'),
(24897, 'Eibenstock', 3021, 'SN', 82, 'DE', 50.49431000, 12.59978000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q57881'),
(24898, 'Eichenau', 3009, 'BY', 82, 'DE', 48.16667000, 11.31667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q57881'),
(24899, 'Eichenbarleben', 3011, 'ST', 82, 'DE', 52.16688000, 11.40125000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q80032'),
(24900, 'Eichenbühl', 3009, 'BY', 82, 'DE', 49.70556000, 9.32917000, '2019-10-05 22:40:44', '2020-05-01 17:22:47', 1, 'Q503096'),
(24901, 'Eichendorf', 3009, 'BY', 82, 'DE', 48.63266000, 12.85586000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q31986191'),
(24902, 'Eichenried', 3009, 'BY', 82, 'DE', 48.27748000, 11.78206000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q31986191'),
(24903, 'Eichenzell', 3018, 'HE', 82, 'DE', 50.49536000, 9.69672000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q627310'),
(24904, 'Eichigt', 3021, 'SN', 82, 'DE', 50.35000000, 12.16667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q46745'),
(24905, 'Eichstetten', 3006, 'BW', 82, 'DE', 48.09427000, 7.74244000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q46745'),
(24906, 'Eichstätt', 3009, 'BY', 82, 'DE', 48.88854000, 11.19675000, '2019-10-05 22:40:44', '2020-05-01 17:22:47', 1, 'Q46745'),
(24907, 'Eichwalde', 3013, 'BB', 82, 'DE', 52.36667000, 13.61667000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q583542'),
(24908, 'Eickendorf', 3011, 'ST', 82, 'DE', 51.94765000, 11.67538000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q704419'),
(24909, 'Eicklingen', 3008, 'NI', 82, 'DE', 52.55037000, 10.18439000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q621920'),
(24910, 'Eidelstedt', 3016, 'HH', 82, 'DE', 53.60697000, 9.90538000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q1609'),
(24911, 'Eigeltingen', 3006, 'BW', 82, 'DE', 47.85802000, 8.89784000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q62021'),
(24912, 'Eil', 3017, 'NW', 82, 'DE', 50.89362000, 7.07967000, '2019-10-05 22:40:44', '2019-10-05 22:40:44', 1, 'Q62021'),
(24913, 'Eilenburg', 3021, 'SN', 82, 'DE', 51.45984000, 12.63338000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q12055'),
(24914, 'Eilsleben', 3011, 'ST', 82, 'DE', 52.14708000, 11.21035000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q573458'),
(24915, 'Eime', 3008, 'NI', 82, 'DE', 52.07449000, 9.72118000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q573458'),
(24916, 'Eimeldingen', 3006, 'BW', 82, 'DE', 47.63333000, 7.60000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q61639'),
(24917, 'Eimen', 3008, 'NI', 82, 'DE', 51.88333000, 9.78333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q61639'),
(24918, 'Eimke', 3008, 'NI', 82, 'DE', 52.96601000, 10.31324000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q696381'),
(24919, 'Eimsbüttel', 3016, 'HH', 82, 'DE', 53.57416000, 9.95679000, '2019-10-05 22:40:45', '2020-05-01 17:22:48', 1, 'Q1632'),
(24920, 'Einbeck', 3008, 'NI', 82, 'DE', 51.82018000, 9.86961000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q47244'),
(24921, 'Einhausen', 3018, 'HE', 82, 'DE', 49.67667000, 8.54833000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q47244'),
(24922, 'Eisdorf am Harz', 3008, 'NI', 82, 'DE', 51.76152000, 10.17591000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q47244'),
(24923, 'Eiselfing', 3009, 'BY', 82, 'DE', 48.04114000, 12.24272000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q545191'),
(24924, 'Eisenach', 3015, 'TH', 82, 'DE', 50.98070000, 10.31522000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q7070'),
(24925, 'Eisenbach', 3006, 'BW', 82, 'DE', 47.96117000, 8.26802000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q117878'),
(24926, 'Eisenberg', 3015, 'TH', 82, 'DE', 50.96860000, 11.90207000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q117878'),
(24927, 'Eisenberg', 3019, 'RP', 82, 'DE', 49.55859000, 8.07199000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31987709'),
(24928, 'Eisenburg', 3009, 'BY', 82, 'DE', 48.01404000, 10.20870000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31987709'),
(24929, 'Eisenhüttenstadt', 3013, 'BB', 82, 'DE', 52.15000000, 14.65000000, '2019-10-05 22:40:45', '2020-05-01 17:22:48', 1, 'Q16106'),
(24930, 'Eisfeld', 3015, 'TH', 82, 'DE', 50.42648000, 10.90695000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q486795'),
(24931, 'Eisingen', 3006, 'BW', 82, 'DE', 48.95000000, 8.66667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q486795'),
(24932, 'Eisingen', 3009, 'BY', 82, 'DE', 49.75972000, 9.83111000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q486795'),
(24933, 'Eisleben Lutherstadt', 3011, 'ST', 82, 'DE', 51.52754000, 11.54835000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q484870'),
(24934, 'Eislingen', 3006, 'BW', 82, 'DE', 48.69515000, 9.70676000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q484870'),
(24935, 'Eitelborn', 3019, 'RP', 82, 'DE', 50.37806000, 7.72333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q570311'),
(24936, 'Eiterfeld', 3018, 'HE', 82, 'DE', 50.76667000, 9.80000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q259444'),
(24937, 'Eitorf', 3017, 'NW', 82, 'DE', 50.76667000, 7.45000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q254649'),
(24938, 'Eitting', 3009, 'BY', 82, 'DE', 48.35957000, 11.89110000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q513542'),
(24939, 'Elbe', 3008, 'NI', 82, 'DE', 52.08333000, 10.28333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q513542'),
(24940, 'Elbingerode', 3011, 'ST', 82, 'DE', 51.77039000, 10.80557000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q504884'),
(24941, 'Eldena', 3007, 'MV', 82, 'DE', 53.23191000, 11.41804000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q504884'),
(24942, 'Eldingen', 3008, 'NI', 82, 'DE', 52.68333000, 10.33333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q625099'),
(24943, 'Elfershausen', 3009, 'BY', 82, 'DE', 50.14743000, 9.96151000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q504259'),
(24944, 'Elgersburg', 3015, 'TH', 82, 'DE', 50.70603000, 10.85310000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q553076'),
(24945, 'Elkenroth', 3019, 'RP', 82, 'DE', 50.73333000, 7.88333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q559960'),
(24946, 'Ellefeld', 3021, 'SN', 82, 'DE', 50.48367000, 12.38880000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q46749'),
(24947, 'Ellenberg', 3006, 'BW', 82, 'DE', 49.01667000, 10.21667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q46749'),
(24948, 'Ellerau', 3005, 'SH', 82, 'DE', 53.75000000, 9.91667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q554129'),
(24949, 'Ellerbek', 3005, 'SH', 82, 'DE', 53.65817000, 9.86991000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q554129'),
(24950, 'Ellerhoop', 3005, 'SH', 82, 'DE', 53.72603000, 9.76933000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q554129'),
(24951, 'Ellerstadt', 3019, 'RP', 82, 'DE', 49.46167000, 8.25944000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q23017'),
(24952, 'Ellgau', 3009, 'BY', 82, 'DE', 48.60000000, 10.86667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q23017'),
(24953, 'Ellhofen', 3006, 'BW', 82, 'DE', 49.14667000, 9.32194000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q518171'),
(24954, 'Ellingen', 3009, 'BY', 82, 'DE', 49.06076000, 10.96783000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q518171'),
(24955, 'Ellrich', 3015, 'TH', 82, 'DE', 51.58656000, 10.66326000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q518240'),
(24956, 'Ellwangen', 3006, 'BW', 82, 'DE', 48.96164000, 10.13173000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q163973'),
(24957, 'Ellzee', 3009, 'BY', 82, 'DE', 48.33971000, 10.31891000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q175698'),
(24958, 'Elmenhorst', 3005, 'SH', 82, 'DE', 53.76667000, 10.26667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q662651'),
(24959, 'Elmenhorst', 3007, 'MV', 82, 'DE', 54.15583000, 12.01084000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q662651'),
(24960, 'Elmshorn', 3005, 'SH', 82, 'DE', 53.75396000, 9.65339000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q6845'),
(24961, 'Elmstein', 3019, 'RP', 82, 'DE', 49.36667000, 7.93333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q566064'),
(24962, 'Elsdorf', 3008, 'NI', 82, 'DE', 53.24120000, 9.35056000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31994269'),
(24963, 'Elsdorf', 3017, 'NW', 82, 'DE', 50.93739000, 6.56828000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q49282863'),
(24964, 'Elsdorf-Westermühlen', 3005, 'SH', 82, 'DE', 54.26667000, 9.51667000, '2019-10-05 22:40:45', '2020-05-01 17:22:49', 1, 'Q49282863'),
(24965, 'Elsendorf', 3009, 'BY', 82, 'DE', 48.70757000, 11.80982000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q256423'),
(24966, 'Elsenfeld', 3009, 'BY', 82, 'DE', 49.84289000, 9.16355000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q502996'),
(24967, 'Elsfleth', 3008, 'NI', 82, 'DE', 53.23748000, 8.45664000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q509939'),
(24968, 'Elsnig', 3021, 'SN', 82, 'DE', 51.61024000, 12.92833000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q509939'),
(24969, 'Elster', 3011, 'ST', 82, 'DE', 51.83011000, 12.82424000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31994476'),
(24970, 'Elsterberg', 3021, 'SN', 82, 'DE', 50.60841000, 12.16787000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q46859'),
(24971, 'Elstertrebnitz', 3021, 'SN', 82, 'DE', 51.15000000, 12.23333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q10748'),
(24972, 'Elsterwerda', 3013, 'BB', 82, 'DE', 51.46043000, 13.52001000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q584473'),
(24973, 'Elstra', 3021, 'SN', 82, 'DE', 51.22172000, 14.13201000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q81735'),
(24974, 'Elterlein', 3021, 'SN', 82, 'DE', 50.57663000, 12.86836000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q57885'),
(24975, 'Eltmann', 3009, 'BY', 82, 'DE', 49.97148000, 10.66712000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q504534'),
(24976, 'Eltville', 3018, 'HE', 82, 'DE', 50.02858000, 8.11754000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q275193'),
(24977, 'Elxleben', 3015, 'TH', 82, 'DE', 51.05000000, 10.95000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q275193'),
(24978, 'Elz', 3018, 'HE', 82, 'DE', 50.41667000, 8.03333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q275193'),
(24979, 'Elzach', 3006, 'BW', 82, 'DE', 48.17247000, 8.06992000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q506773'),
(24980, 'Elze', 3008, 'NI', 82, 'DE', 52.12263000, 9.73595000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q518719'),
(24981, 'Embsen', 3008, 'NI', 82, 'DE', 53.17606000, 10.34625000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q672032'),
(24982, 'Emden', 3008, 'NI', 82, 'DE', 53.36745000, 7.20778000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q4174'),
(24983, 'Emersacker', 3009, 'BY', 82, 'DE', 48.48965000, 10.67380000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q503345'),
(24984, 'Emkendorf', 3005, 'SH', 82, 'DE', 54.26667000, 9.85000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q560893'),
(24985, 'Emlichheim', 3008, 'NI', 82, 'DE', 52.61667000, 6.85000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q514150'),
(24986, 'Emmelsbüll-Horsbüll', 3005, 'SH', 82, 'DE', 54.81667000, 8.70000000, '2019-10-05 22:40:45', '2020-05-01 17:22:49', 1, 'Q514150'),
(24987, 'Emmelshausen', 3019, 'RP', 82, 'DE', 50.15484000, 7.55185000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q539565'),
(24988, 'Emmendingen', 3006, 'BW', 82, 'DE', 48.12096000, 7.85359000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q15276466'),
(24989, 'Emmerich', 3017, 'NW', 82, 'DE', 51.83933000, 6.24792000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31995210'),
(24990, 'Emmering', 3009, 'BY', 82, 'DE', 48.18333000, 11.28333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31995210'),
(24991, 'Emmingen-Liptingen', 3006, 'BW', 82, 'DE', 47.93333000, 8.88333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q530508'),
(24992, 'Empfingen', 3006, 'BW', 82, 'DE', 48.39258000, 8.71036000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q80467'),
(24993, 'Emsbüren', 3008, 'NI', 82, 'DE', 52.40000000, 7.30000000, '2019-10-05 22:40:45', '2020-05-01 17:22:48', 1, 'Q629052'),
(24994, 'Emsdetten', 3017, 'NW', 82, 'DE', 52.17340000, 7.52781000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31995364'),
(24995, 'Emskirchen', 3009, 'BY', 82, 'DE', 49.55282000, 10.71278000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q505214'),
(24996, 'Emstek', 3008, 'NI', 82, 'DE', 52.83333000, 8.15000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q559488'),
(24997, 'Emtinghausen', 3008, 'NI', 82, 'DE', 52.93333000, 8.96667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q178910'),
(24998, 'Emtmannsberg', 3009, 'BY', 82, 'DE', 49.89231000, 11.64466000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31596'),
(24999, 'Endingen', 3006, 'BW', 82, 'DE', 48.14221000, 7.70049000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q168511'),
(25000, 'Engeln', 3008, 'NI', 82, 'DE', 52.79363000, 8.91815000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q502794'),
(25001, 'Engelsberg', 3009, 'BY', 82, 'DE', 48.11631000, 12.54267000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q534074'),
(25002, 'Engelsbrand', 3006, 'BW', 82, 'DE', 48.83333000, 8.65000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q80505'),
(25003, 'Engelskirchen', 3017, 'NW', 82, 'DE', 50.98854000, 7.41391000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31995977'),
(25004, 'Engelthal', 3009, 'BY', 82, 'DE', 49.47181000, 11.39943000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q524573'),
(25005, 'Engen', 3006, 'BW', 82, 'DE', 47.85534000, 8.77342000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q31996016'),
(25006, 'Enger', 3017, 'NW', 82, 'DE', 52.14063000, 8.55772000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q53901'),
(25007, 'Eningen unter Achalm', 3006, 'BW', 82, 'DE', 48.48686000, 9.25946000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q81423'),
(25008, 'Enkenbach-Alsenborn', 3019, 'RP', 82, 'DE', 49.48333000, 7.90000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q81423'),
(25009, 'Enkirch', 3019, 'RP', 82, 'DE', 49.98434000, 7.12997000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q553838'),
(25010, 'Ennepetal', 3017, 'NW', 82, 'DE', 51.29848000, 7.36290000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q11301'),
(25011, 'Ennetach', 3006, 'BW', 82, 'DE', 48.05238000, 9.32010000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q880749'),
(25012, 'Ennigerloh', 3017, 'NW', 82, 'DE', 51.83843000, 8.03093000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q2760'),
(25013, 'Ensdorf', 3009, 'BY', 82, 'DE', 49.34062000, 11.93587000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q2760'),
(25014, 'Ensdorf', 3020, 'SL', 82, 'DE', 49.30000000, 6.78333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q2760'),
(25015, 'Enzklösterle', 3006, 'BW', 82, 'DE', 48.66638000, 8.47083000, '2019-10-05 22:40:45', '2020-05-01 17:22:47', 1, 'Q246783'),
(25016, 'Epfenbach', 3006, 'BW', 82, 'DE', 49.33917000, 8.90778000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q83374'),
(25017, 'Epfendorf', 3006, 'BW', 82, 'DE', 48.25000000, 8.60000000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q81302'),
(25018, 'Eppelborn', 3020, 'SL', 82, 'DE', 49.40000000, 6.96667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q570286'),
(25019, 'Eppelheim', 3006, 'BW', 82, 'DE', 49.40190000, 8.63644000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q82539'),
(25020, 'Eppelsheim', 3019, 'RP', 82, 'DE', 49.70556000, 8.16528000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q569785'),
(25021, 'Eppenbrunn', 3019, 'RP', 82, 'DE', 49.11667000, 7.56667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q565853'),
(25022, 'Eppendorf', 3021, 'SN', 82, 'DE', 50.80000000, 13.23333000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q565853'),
(25023, 'Eppertshausen', 3018, 'HE', 82, 'DE', 49.95056000, 8.85389000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q631184'),
(25024, 'Eppingen', 3006, 'BW', 82, 'DE', 49.13645000, 8.91229000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q506450'),
(25025, 'Eppishausen', 3009, 'BY', 82, 'DE', 48.16667000, 10.51667000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q547945'),
(25026, 'Eppstein', 3018, 'HE', 82, 'DE', 50.14277000, 8.39231000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q45347'),
(25027, 'Erbach', 3018, 'HE', 82, 'DE', 49.66148000, 8.99402000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q571748'),
(25028, 'Erbach', 3006, 'BW', 82, 'DE', 48.32841000, 9.88752000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q571748'),
(25029, 'Erbendorf', 3009, 'BY', 82, 'DE', 49.83983000, 12.04593000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q504596'),
(25030, 'Erbes-Büdesheim', 3019, 'RP', 82, 'DE', 49.75444000, 8.03139000, '2019-10-05 22:40:45', '2020-05-01 17:22:49', 1, 'Q563950'),
(25031, 'Erdeborn', 3011, 'ST', 82, 'DE', 51.47554000, 11.63487000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q696531'),
(25032, 'Erding', 3009, 'BY', 82, 'DE', 48.30603000, 11.90686000, '2019-10-05 22:40:45', '2019-10-05 22:40:45', 1, 'Q15980'),
(25033, 'Erdmannhausen', 3006, 'BW', 82, 'DE', 48.94256000, 9.29615000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q61651'),
(25034, 'Erdweg', 3009, 'BY', 82, 'DE', 48.33180000, 11.30339000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q541512'),
(25035, 'Eresing', 3009, 'BY', 82, 'DE', 48.08700000, 11.02388000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q31997435'),
(25036, 'Erfde', 3005, 'SH', 82, 'DE', 54.30000000, 9.31667000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q118540'),
(25037, 'Erftstadt', 3017, 'NW', 82, 'DE', 50.81481000, 6.79387000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q4195'),
(25038, 'Erfurt', 3015, 'TH', 82, 'DE', 50.97870000, 11.03283000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q1729'),
(25039, 'Erfweiler', 3019, 'RP', 82, 'DE', 49.15712000, 7.81231000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q1729'),
(25040, 'Ergersheim', 3009, 'BY', 82, 'DE', 49.51667000, 10.33333000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q1729'),
(25041, 'Ergolding', 3009, 'BY', 82, 'DE', 48.57654000, 12.17102000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q264256'),
(25042, 'Ergoldsbach', 3009, 'BY', 82, 'DE', 48.69263000, 12.20442000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q33435454'),
(25043, 'Ering', 3009, 'BY', 82, 'DE', 48.29921000, 13.15029000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q589578'),
(25044, 'Eriskirch', 3006, 'BW', 82, 'DE', 47.62479000, 9.54197000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q515372'),
(25045, 'Erkelenz', 3017, 'NW', 82, 'DE', 51.07947000, 6.31531000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q7025'),
(25046, 'Erkenbrechtsweiler', 3006, 'BW', 82, 'DE', 48.55656000, 9.43211000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q80494'),
(25047, 'Erkerode', 3008, 'NI', 82, 'DE', 52.20485000, 10.70982000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q634367'),
(25048, 'Erkheim', 3009, 'BY', 82, 'DE', 48.03730000, 10.33570000, '2019-10-05 22:40:46', '2019-10-05 22:40:46', 1, 'Q547152'),
(25049, 'Erkner', 3013, 'BB', 82, 'DE', 52.42000000, 13.75437000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q572392'),
(25050, 'Erkrath', 3017, 'NW', 82, 'DE', 51.22235000, 6.90831000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q6968'),
(25051, 'Erlabrunn', 3009, 'BY', 82, 'DE', 49.85972000, 9.84417000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q504236'),
(25052, 'Erlangen', 3009, 'BY', 82, 'DE', 49.59099000, 11.00783000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q3126'),
(25053, 'Erlau', 3021, 'SN', 82, 'DE', 51.00938000, 12.94549000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q3126'),
(25054, 'Erlbach', 3021, 'SN', 82, 'DE', 50.31667000, 12.36667000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q46753'),
(25055, 'Erlbach', 3009, 'BY', 82, 'DE', 48.30000000, 12.78333000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q46753'),
(25056, 'Erlenbach', 3009, 'BY', 82, 'DE', 49.97857000, 9.64459000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q46753'),
(25057, 'Erlenbach', 3006, 'BW', 82, 'DE', 49.17250000, 9.26833000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q46753'),
(25058, 'Erlenbach am Main', 3009, 'BY', 82, 'DE', 49.80341000, 9.16311000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q46753'),
(25059, 'Erlenmoos', 3006, 'BW', 82, 'DE', 48.06530000, 9.97567000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q46753'),
(25060, 'Erlensee', 3018, 'HE', 82, 'DE', 50.16304000, 8.97823000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q46753'),
(25061, 'Erligheim', 3006, 'BW', 82, 'DE', 49.02250000, 9.09722000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q61702'),
(25062, 'Ermlitz', 3011, 'ST', 82, 'DE', 51.39182000, 12.15946000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q1355991'),
(25063, 'Erndtebrück', 3017, 'NW', 82, 'DE', 50.98927000, 8.25288000, '2019-10-05 22:40:47', '2020-05-01 17:22:49', 1, 'Q10956'),
(25064, 'Ernsgaden', 3009, 'BY', 82, 'DE', 48.73333000, 11.58333000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q31998593'),
(25065, 'Ernstroda', 3015, 'TH', 82, 'DE', 50.86484000, 10.62056000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q695956'),
(25066, 'Erolzheim', 3006, 'BW', 82, 'DE', 48.09001000, 10.07300000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q519018'),
(25067, 'Erpel', 3019, 'RP', 82, 'DE', 50.58333000, 7.23333000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q566435'),
(25068, 'Erpolzheim', 3019, 'RP', 82, 'DE', 49.48917000, 8.22472000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q22526'),
(25069, 'Ersingen', 3006, 'BW', 82, 'DE', 48.29260000, 9.85510000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q22526'),
(25070, 'Ertingen', 3006, 'BW', 82, 'DE', 48.10000000, 9.46667000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q538711'),
(25071, 'Erwitte', 3017, 'NW', 82, 'DE', 51.61270000, 8.33840000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q31998810'),
(25072, 'Erxleben', 3011, 'ST', 82, 'DE', 52.21812000, 11.24245000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q31998810'),
(25073, 'Erzhausen', 3018, 'HE', 82, 'DE', 49.95528000, 8.64750000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q635503'),
(25074, 'Eschach', 3006, 'BW', 82, 'DE', 48.88832000, 9.86999000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q635503'),
(25075, 'Eschau', 3009, 'BY', 82, 'DE', 49.81889000, 9.25920000, '2019-10-05 22:40:47', '2019-10-05 22:40:47', 1, 'Q504370'),
(25076, 'Eschborn', 3018, 'HE', 82, 'DE', 50.14328000, 8.57111000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q569648'),
(25077, 'Escheburg', 3005, 'SH', 82, 'DE', 53.46667000, 10.31667000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q555756'),
(25078, 'Eschede', 3008, 'NI', 82, 'DE', 52.73494000, 10.23540000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q555622'),
(25079, 'Eschelbronn', 3006, 'BW', 82, 'DE', 49.31944000, 8.86528000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q13622'),
(25080, 'Eschenbach', 3006, 'BW', 82, 'DE', 48.65264000, 9.67037000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q13622'),
(25081, 'Eschenlohe', 3009, 'BY', 82, 'DE', 48.01731000, 12.03012000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q31999248'),
(25082, 'Eschershausen', 3008, 'NI', 82, 'DE', 51.92664000, 9.64282000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q58757'),
(25083, 'Eschlkam', 3009, 'BY', 82, 'DE', 49.29883000, 12.91573000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q505997'),
(25084, 'Eschwege', 3018, 'HE', 82, 'DE', 51.18386000, 10.05329000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q1468'),
(25085, 'Eschweiler', 3017, 'NW', 82, 'DE', 50.81854000, 6.27184000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q4083'),
(25086, 'Esens', 3008, 'NI', 82, 'DE', 53.64866000, 7.61267000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q4083'),
(25087, 'Eslarn', 3009, 'BY', 82, 'DE', 49.58345000, 12.52156000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q507911'),
(25088, 'Eslohe', 3017, 'NW', 82, 'DE', 51.25369000, 8.16949000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q10929'),
(25089, 'Espelkamp', 3017, 'NW', 82, 'DE', 52.38251000, 8.62127000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q182691'),
(25090, 'Espenau', 3018, 'HE', 82, 'DE', 51.39664000, 9.47021000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q627482'),
(25091, 'Espenhain', 3021, 'SN', 82, 'DE', 51.18926000, 12.47885000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q10749'),
(25092, 'Essel', 3008, 'NI', 82, 'DE', 52.69131000, 9.64141000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q10749'),
(25093, 'Esselbach', 3009, 'BY', 82, 'DE', 49.85528000, 9.52583000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q16831261'),
(25094, 'Essen', 3008, 'NI', 82, 'DE', 52.72258000, 7.93710000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q16831261'),
(25095, 'Essen', 3017, 'NW', 82, 'DE', 51.45657000, 7.01228000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q2066'),
(25096, 'Essenbach', 3009, 'BY', 82, 'DE', 48.61332000, 12.21833000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q269499'),
(25097, 'Essenheim', 3019, 'RP', 82, 'DE', 49.93056000, 8.15556000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q572400'),
(25098, 'Essing', 3009, 'BY', 82, 'DE', 48.93467000, 11.78972000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q174307'),
(25099, 'Essingen', 3006, 'BW', 82, 'DE', 48.80859000, 10.02773000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q174307'),
(25100, 'Essingen', 3019, 'RP', 82, 'DE', 49.23583000, 8.17472000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q174307'),
(25101, 'Esslingen', 3006, 'BW', 82, 'DE', 48.73961000, 9.30473000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q3786'),
(25102, 'Estenfeld', 3009, 'BY', 82, 'DE', 49.82896000, 10.00588000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q508383'),
(25103, 'Esterwegen', 3008, 'NI', 82, 'DE', 52.99288000, 7.63327000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q673391'),
(25104, 'Esthal', 3019, 'RP', 82, 'DE', 49.38333000, 7.98333000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q566061'),
(25105, 'Estorf', 3008, 'NI', 82, 'DE', 52.58850000, 9.14147000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q566061'),
(25106, 'Ettenheim', 3006, 'BW', 82, 'DE', 48.25696000, 7.81247000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q506871'),
(25107, 'Ettlingen', 3006, 'BW', 82, 'DE', 48.94094000, 8.40763000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q14893'),
(25108, 'Ettringen', 3019, 'RP', 82, 'DE', 50.35000000, 7.21667000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q14893'),
(25109, 'Ettringen', 3009, 'BY', 82, 'DE', 48.10000000, 10.65000000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q566578'),
(25110, 'Etzbach', 3019, 'RP', 82, 'DE', 50.77831000, 7.68800000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q563128'),
(25111, 'Etzelwang', 3009, 'BY', 82, 'DE', 49.52760000, 11.58603000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q520834'),
(25112, 'Etzenricht', 3009, 'BY', 82, 'DE', 49.63333000, 12.10000000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q258790'),
(25113, 'Euerbach', 3009, 'BY', 82, 'DE', 50.06199000, 10.13695000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q581630'),
(25114, 'Euerdorf', 3009, 'BY', 82, 'DE', 50.14853000, 10.02331000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q504321'),
(25115, 'Eurasburg', 3009, 'BY', 82, 'DE', 47.85389000, 11.40587000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q504321'),
(25116, 'Euskirchen', 3017, 'NW', 82, 'DE', 50.66057000, 6.78722000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q4074'),
(25117, 'Eutin', 3005, 'SH', 82, 'DE', 54.14054000, 10.60751000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q490445'),
(25118, 'Eutingen an der Enz', 3006, 'BW', 82, 'DE', 48.91246000, 8.74898000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q32000235'),
(25119, 'Eußenheim', 3009, 'BY', 82, 'DE', 49.98589000, 9.80899000, '2019-10-05 22:40:48', '2020-05-01 17:22:47', 1, 'Q34561197'),
(25120, 'Everswinkel', 3017, 'NW', 82, 'DE', 51.92595000, 7.84690000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q32000368'),
(25121, 'Evessen', 3008, 'NI', 82, 'DE', 52.18888000, 10.71081000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q664998'),
(25122, 'Eydelstedt', 3008, 'NI', 82, 'DE', 52.69241000, 8.54668000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q504747'),
(25123, 'Eyendorf', 3008, 'NI', 82, 'DE', 53.20000000, 10.15000000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q504747'),
(25124, 'Eystrup', 3008, 'NI', 82, 'DE', 52.77935000, 9.21315000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q564448'),
(25125, 'Fachbach', 3019, 'RP', 82, 'DE', 50.33333000, 7.68333000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q573466'),
(25126, 'Fahrdorf', 3005, 'SH', 82, 'DE', 54.50000000, 9.60000000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q117919'),
(25127, 'Fahrenbach', 3006, 'BW', 82, 'DE', 49.43139000, 9.15056000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q534257'),
(25128, 'Fahrenkrug', 3005, 'SH', 82, 'DE', 53.95000000, 10.25000000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q534257'),
(25129, 'Fahrenzhausen', 3009, 'BY', 82, 'DE', 48.35317000, 11.55521000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q506233'),
(25130, 'Faid', 3019, 'RP', 82, 'DE', 50.14454000, 7.11951000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q567212'),
(25131, 'Falkenau', 3021, 'SN', 82, 'DE', 50.85559000, 13.11718000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q542078'),
(25132, 'Falkenberg', 3009, 'BY', 82, 'DE', 48.46154000, 12.73049000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q542078'),
(25133, 'Falkenberg', 3013, 'BB', 82, 'DE', 51.58557000, 13.24347000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q32000922'),
(25134, 'Falkenberg', 3010, 'BE', 82, 'DE', 52.56800000, 13.54597000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q674988'),
(25135, 'Falkenfels', 3009, 'BY', 82, 'DE', 49.00415000, 12.59480000, '2019-10-05 22:40:48', '2019-10-05 22:40:48', 1, 'Q428511'),
(25136, 'Falkenhagener Feld', 3010, 'BE', 82, 'DE', 52.55191000, 13.16802000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q559222'),
(25137, 'Falkenhain', 3021, 'SN', 82, 'DE', 51.39888000, 12.87083000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q631101'),
(25138, 'Falkensee', 3013, 'BB', 82, 'DE', 52.56014000, 13.09270000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q14842'),
(25139, 'Falkenstein', 3021, 'SN', 82, 'DE', 50.47788000, 12.37129000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q46863'),
(25140, 'Falkenstein', 3009, 'BY', 82, 'DE', 49.09750000, 12.48802000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q521215'),
(25141, 'Fambach', 3015, 'TH', 82, 'DE', 50.73333000, 10.36667000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q818881'),
(25142, 'Farchant', 3009, 'BY', 82, 'DE', 47.53036000, 11.11151000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q32001473'),
(25143, 'Farmsen-Berne', 3016, 'HH', 82, 'DE', 53.60639000, 10.11972000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q1808'),
(25144, 'Farnstädt', 3011, 'ST', 82, 'DE', 51.43333000, 11.56667000, '2019-10-05 22:40:49', '2020-05-01 17:22:49', 1, 'Q339068'),
(25145, 'Faulbach', 3009, 'BY', 82, 'DE', 49.78528000, 9.44417000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q35145944'),
(25146, 'Faßberg', 3008, 'NI', 82, 'DE', 52.90000000, 10.16667000, '2019-10-05 22:40:49', '2020-05-01 17:22:48', 1, 'Q60569'),
(25147, 'Fedderwarden', 3008, 'NI', 82, 'DE', 53.56142000, 8.04371000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q1399888'),
(25148, 'Fehmarn', 3005, 'SH', 82, 'DE', 54.43780000, 11.19352000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q3172'),
(25149, 'Fehrbellin', 3013, 'BB', 82, 'DE', 52.81350000, 12.76440000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q626792'),
(25150, 'Feilbingert', 3019, 'RP', 82, 'DE', 49.76667000, 7.80000000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q567663'),
(25151, 'Feilitzsch', 3009, 'BY', 82, 'DE', 50.36667000, 11.93333000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q158919'),
(25152, 'Feldafing', 3009, 'BY', 82, 'DE', 47.94602000, 11.29326000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q507424'),
(25153, 'Feldberg', 3006, 'BW', 82, 'DE', 47.77435000, 7.64142000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q507424'),
(25154, 'Felde', 3005, 'SH', 82, 'DE', 54.30000000, 9.93333000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q631676'),
(25155, 'Feldkirchen', 3009, 'BY', 82, 'DE', 48.14811000, 11.73100000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q631676'),
(25156, 'Feldkirchen-Westerham', 3009, 'BY', 82, 'DE', 47.90748000, 11.84266000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q532758'),
(25157, 'Feldstadt', 3007, 'MV', 82, 'DE', 53.62331000, 11.40673000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q1402836'),
(25158, 'Fell', 3019, 'RP', 82, 'DE', 49.76667000, 6.78333000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q1402836'),
(25159, 'Fellbach', 3006, 'BW', 82, 'DE', 48.80912000, 9.27697000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q7022'),
(25160, 'Fellheim', 3009, 'BY', 82, 'DE', 48.07280000, 10.15224000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q545144'),
(25161, 'Felm', 3005, 'SH', 82, 'DE', 54.41667000, 10.05000000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q545144'),
(25162, 'Felsberg', 3018, 'HE', 82, 'DE', 51.13763000, 9.42139000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q32003382'),
(25163, 'Fennpfuhl', 3010, 'BE', 82, 'DE', 52.52921000, 13.47267000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q566387'),
(25164, 'Ferdinandshof', 3007, 'MV', 82, 'DE', 53.66124000, 13.88724000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q554282'),
(25165, 'Feucht', 3009, 'BY', 82, 'DE', 49.37597000, 11.21433000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q512293'),
(25166, 'Feuchtwangen', 3009, 'BY', 82, 'DE', 49.16287000, 10.33850000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q502418'),
(25167, 'Fichtelberg', 3009, 'BY', 82, 'DE', 50.00288000, 11.85425000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q502418'),
(25168, 'Fichtenberg', 3006, 'BW', 82, 'DE', 48.98601000, 9.71199000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q502418'),
(25169, 'Fichtenwalde', 3013, 'BB', 82, 'DE', 52.28038000, 12.88349000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q502418'),
(25170, 'Filderstadt', 3006, 'BW', 82, 'DE', 48.65698000, 9.22049000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q7032'),
(25171, 'Filsum', 3008, 'NI', 82, 'DE', 53.24213000, 7.62785000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q696261'),
(25172, 'Finnentrop', 3017, 'NW', 82, 'DE', 51.16800000, 7.97300000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q8922776'),
(25173, 'Finningen', 3009, 'BY', 82, 'DE', 48.65452000, 10.49864000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q510751'),
(25174, 'Finsing', 3009, 'BY', 82, 'DE', 48.21615000, 11.82412000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q511719'),
(25175, 'Finsterbergen', 3015, 'TH', 82, 'DE', 50.83464000, 10.58916000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q695969'),
(25176, 'Finsterwalde', 3013, 'BB', 82, 'DE', 51.63388000, 13.70662000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q588905'),
(25177, 'Fintel', 3008, 'NI', 82, 'DE', 53.17188000, 9.66969000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q503684'),
(25178, 'Fischach', 3009, 'BY', 82, 'DE', 48.29047000, 10.65592000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q504062'),
(25179, 'Fischbach', 3019, 'RP', 82, 'DE', 49.08771000, 7.71160000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q32006991'),
(25180, 'Fischbachau', 3009, 'BY', 82, 'DE', 47.71952000, 11.95081000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q32006991'),
(25181, 'Fischerbach', 3006, 'BW', 82, 'DE', 48.28687000, 8.10959000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q32006991'),
(25182, 'Flachslanden', 3009, 'BY', 82, 'DE', 49.39845000, 10.51323000, '2019-10-05 22:40:49', '2019-10-05 22:40:49', 1, 'Q502992'),
(25183, 'Flacht', 3019, 'RP', 82, 'DE', 50.34528000, 8.05028000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q554022'),
(25184, 'Fladungen', 3009, 'BY', 82, 'DE', 50.52054000, 10.14581000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q582942'),
(25185, 'Flammersfeld', 3019, 'RP', 82, 'DE', 50.64711000, 7.52713000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q568197'),
(25186, 'Flechtingen', 3011, 'ST', 82, 'DE', 52.33083000, 11.24176000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q568197'),
(25187, 'Fleckeby', 3005, 'SH', 82, 'DE', 54.48333000, 9.70000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q568197'),
(25188, 'Flein', 3006, 'BW', 82, 'DE', 49.10306000, 9.21083000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q509394'),
(25189, 'Flensburg', 3005, 'SH', 82, 'DE', 54.78431000, 9.43961000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q3798'),
(25190, 'Flessau', 3011, 'ST', 82, 'DE', 52.75996000, 11.67093000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q836717'),
(25191, 'Flieden', 3018, 'HE', 82, 'DE', 50.42389000, 9.56660000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q624955'),
(25192, 'Flintbek', 3005, 'SH', 82, 'DE', 54.25000000, 10.06667000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q570392'),
(25193, 'Flintsbach', 3009, 'BY', 82, 'DE', 47.72576000, 12.12419000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q532824'),
(25194, 'Flomborn', 3019, 'RP', 82, 'DE', 49.69056000, 8.14917000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q566022'),
(25195, 'Flonheim', 3019, 'RP', 82, 'DE', 49.78500000, 8.04000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q553680'),
(25196, 'Florstadt', 3018, 'HE', 82, 'DE', 50.31667000, 8.86667000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q570394'),
(25197, 'Flossenbürg', 3009, 'BY', 82, 'DE', 49.73333000, 12.35000000, '2019-10-05 22:40:50', '2020-05-01 17:22:47', 1, 'Q502427'),
(25198, 'Floß', 3009, 'BY', 82, 'DE', 49.72395000, 12.27593000, '2019-10-05 22:40:50', '2020-05-01 17:22:47', 1, 'Q33436449'),
(25199, 'Flöha', 3021, 'SN', 82, 'DE', 50.85613000, 13.07407000, '2019-10-05 22:40:50', '2020-05-01 17:22:49', 1, 'Q71166'),
(25200, 'Flörsheim', 3018, 'HE', 82, 'DE', 50.01311000, 8.42779000, '2019-10-05 22:40:50', '2020-05-01 17:22:48', 1, 'Q622593'),
(25201, 'Flöthe', 3008, 'NI', 82, 'DE', 52.08333000, 10.48333000, '2019-10-05 22:40:50', '2020-05-01 17:22:48', 1, 'Q622593'),
(25202, 'Fockbek', 3005, 'SH', 82, 'DE', 54.30000000, 9.60000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q622593'),
(25203, 'Forbach', 3006, 'BW', 82, 'DE', 48.68333000, 8.35000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q81306'),
(25204, 'Forchheim', 3009, 'BY', 82, 'DE', 49.71754000, 11.05877000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q16116'),
(25205, 'Forchheim', 3006, 'BW', 82, 'DE', 48.16667000, 7.70000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q16116'),
(25206, 'Forchtenberg', 3006, 'BW', 82, 'DE', 49.28867000, 9.56026000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q502780'),
(25207, 'Forst', 3013, 'BB', 82, 'DE', 51.73544000, 14.63971000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q572545'),
(25208, 'Forst', 3006, 'BW', 82, 'DE', 49.15861000, 8.58083000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q572545'),
(25209, 'Forstinning', 3009, 'BY', 82, 'DE', 48.16866000, 11.91244000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q510974'),
(25210, 'Framersheim', 3019, 'RP', 82, 'DE', 49.75806000, 8.17417000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q553509'),
(25211, 'Frammersbach', 3009, 'BY', 82, 'DE', 50.06468000, 9.46888000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q503743'),
(25212, 'Frankenau', 3018, 'HE', 82, 'DE', 51.09269000, 8.93447000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q249066'),
(25213, 'Frankenberg', 3018, 'HE', 82, 'DE', 51.05890000, 8.80077000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q573603'),
(25214, 'Frankenberg', 3021, 'SN', 82, 'DE', 50.91297000, 13.04011000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q71104'),
(25215, 'Frankenheim', 3015, 'TH', 82, 'DE', 50.54452000, 10.06828000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q628547'),
(25216, 'Frankenstein', 3021, 'SN', 82, 'DE', 50.90064000, 13.20933000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q32016272'),
(25217, 'Frankenstein', 3019, 'RP', 82, 'DE', 49.44015000, 7.97744000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q32016272'),
(25218, 'Frankenthal', 3019, 'RP', 82, 'DE', 49.53414000, 8.35357000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q6905'),
(25219, 'Frankenthal', 3021, 'SN', 82, 'DE', 51.13129000, 14.10893000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q6905'),
(25220, 'Frankenwinheim', 3009, 'BY', 82, 'DE', 49.88733000, 10.31432000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q6905'),
(25221, 'Frankfurt (Oder)', 3013, 'BB', 82, 'DE', 52.34714000, 14.55062000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q4024'),
(25222, 'Frankfurt am Main', 3018, 'HE', 82, 'DE', 50.11552000, 8.68417000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q1794'),
(25223, 'Frankleben', 3011, 'ST', 82, 'DE', 51.31144000, 11.92932000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q1445126'),
(25224, 'Franzburg', 3007, 'MV', 82, 'DE', 54.18501000, 12.88210000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q571954'),
(25225, 'Französisch Buchholz', 3010, 'BE', 82, 'DE', 52.60242000, 13.43019000, '2019-10-05 22:40:50', '2020-05-01 17:22:48', 1, 'Q675015'),
(25226, 'Frasdorf', 3009, 'BY', 82, 'DE', 47.80376000, 12.28512000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q126889'),
(25227, 'Frauenau', 3009, 'BY', 82, 'DE', 48.98895000, 13.30067000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q489045'),
(25228, 'Fraueneuharting', 3009, 'BY', 82, 'DE', 48.03873000, 12.04780000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q489045'),
(25229, 'Frauenprießnitz', 3015, 'TH', 82, 'DE', 51.01667000, 11.73333000, '2019-10-05 22:40:50', '2020-05-01 17:22:50', 1, 'Q658842'),
(25230, 'Frauenstein', 3021, 'SN', 82, 'DE', 50.80279000, 13.53790000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q658842'),
(25231, 'Frauenwald', 3015, 'TH', 82, 'DE', 50.58430000, 10.85841000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q658842'),
(25232, 'Fraunberg', 3009, 'BY', 82, 'DE', 48.36667000, 12.00000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q658842'),
(25233, 'Fraureuth', 3021, 'SN', 82, 'DE', 50.70000000, 12.35000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q17116'),
(25234, 'Frechen', 3017, 'NW', 82, 'DE', 50.91485000, 6.81180000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q6823'),
(25235, 'Freckenfeld', 3019, 'RP', 82, 'DE', 49.06500000, 8.11389000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q567261'),
(25236, 'Freden', 3008, 'NI', 82, 'DE', 51.92771000, 9.89350000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q555597'),
(25237, 'Frei-Laubersheim', 3019, 'RP', 82, 'DE', 49.80000000, 7.90000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q565120'),
(25238, 'Freiberg', 3021, 'SN', 82, 'DE', 50.91089000, 13.33881000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q14819'),
(25239, 'Freiberg am Neckar', 3006, 'BW', 82, 'DE', 48.93196000, 9.20240000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q61840'),
(25240, 'Freiburg', 3006, 'BW', 82, 'DE', 47.99590000, 7.85222000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q2833'),
(25241, 'Freiburg', 3008, 'NI', 82, 'DE', 53.82529000, 9.28803000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q502617'),
(25242, 'Freiburg Region', 3006, 'BW', 82, 'DE', 48.16667000, 8.33333000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q502617'),
(25243, 'Freienbessingen', 3015, 'TH', 82, 'DE', 51.23333000, 10.76667000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q502617'),
(25244, 'Freiensteinau', 3018, 'HE', 82, 'DE', 50.42509000, 9.40267000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q32017509'),
(25245, 'Freienwill', 3005, 'SH', 82, 'DE', 54.72752000, 9.49414000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q545804'),
(25246, 'Freihung', 3009, 'BY', 82, 'DE', 49.62134000, 11.90817000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q515907'),
(25247, 'Freilassing', 3009, 'BY', 82, 'DE', 47.84085000, 12.98114000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q278352'),
(25248, 'Freinsheim', 3019, 'RP', 82, 'DE', 49.50649000, 8.21186000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q539630'),
(25249, 'Freisbach', 3019, 'RP', 82, 'DE', 49.27167000, 8.27194000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q567245'),
(25250, 'Freisen', 3020, 'SL', 82, 'DE', 49.55000000, 7.25000000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q569850'),
(25251, 'Freising', 3009, 'BY', 82, 'DE', 48.40351000, 11.74876000, '2019-10-05 22:40:50', '2019-10-05 22:40:50', 1, 'Q6998'),
(25252, 'Freital', 3021, 'SN', 82, 'DE', 51.00166000, 13.64880000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q5870'),
(25253, 'Fremdingen', 3009, 'BY', 82, 'DE', 48.97241000, 10.45754000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q504189'),
(25254, 'Frensdorf', 3009, 'BY', 82, 'DE', 49.81667000, 10.86667000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q529719'),
(25255, 'Freren', 3008, 'NI', 82, 'DE', 52.48700000, 7.54313000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q565523'),
(25256, 'Freudenberg', 3017, 'NW', 82, 'DE', 50.89741000, 7.87415000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018001'),
(25257, 'Freudenberg', 3009, 'BY', 82, 'DE', 49.48333000, 11.98333000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018001'),
(25258, 'Freudenberg', 3006, 'BW', 82, 'DE', 49.75349000, 9.32748000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018001'),
(25259, 'Freudenburg', 3019, 'RP', 82, 'DE', 49.54348000, 6.53292000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q569706'),
(25260, 'Freudenstadt', 3006, 'BW', 82, 'DE', 48.46695000, 8.41371000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q82838'),
(25261, 'Freudental', 3006, 'BW', 82, 'DE', 49.00972000, 9.05917000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q61656'),
(25262, 'Freyburg', 3011, 'ST', 82, 'DE', 51.21362000, 11.76804000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q61656'),
(25263, 'Freystadt', 3009, 'BY', 82, 'DE', 49.20007000, 11.33032000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018063'),
(25264, 'Freyung', 3009, 'BY', 82, 'DE', 48.80952000, 13.54768000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018069'),
(25265, 'Frickenhausen', 3009, 'BY', 82, 'DE', 49.67089000, 10.09268000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018091'),
(25266, 'Frickenhausen', 3006, 'BW', 82, 'DE', 48.59353000, 9.36005000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018091'),
(25267, 'Frickingen', 3006, 'BW', 82, 'DE', 47.81438000, 9.27349000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q161019'),
(25268, 'Fridingen an der Donau', 3006, 'BW', 82, 'DE', 48.01955000, 8.92322000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q506549'),
(25269, 'Fridolfing', 3009, 'BY', 82, 'DE', 47.99773000, 12.82628000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q259202'),
(25270, 'Friedberg', 3009, 'BY', 82, 'DE', 48.35693000, 10.98461000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q507194'),
(25271, 'Friedberg', 3018, 'HE', 82, 'DE', 50.33739000, 8.75591000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q516485'),
(25272, 'Friedeburg', 3008, 'NI', 82, 'DE', 53.45000000, 7.83333000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q502602'),
(25273, 'Friedelsheim', 3019, 'RP', 82, 'DE', 49.44694000, 8.22306000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q22984'),
(25274, 'Friedenau', 3010, 'BE', 82, 'DE', 52.47133000, 13.32813000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q692390'),
(25275, 'Friedenfels', 3009, 'BY', 82, 'DE', 49.88260000, 12.10124000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q692390'),
(25276, 'Friedenweiler', 3006, 'BW', 82, 'DE', 47.91778000, 8.25627000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q519987'),
(25277, 'Friedersdorf', 3021, 'SN', 82, 'DE', 51.02416000, 14.56246000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q702829'),
(25278, 'Friedersdorf', 3011, 'ST', 82, 'DE', 51.64809000, 12.36555000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q688691'),
(25279, 'Friedewald', 3018, 'HE', 82, 'DE', 50.88333000, 9.86667000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q624067'),
(25280, 'Friedewald', 3019, 'RP', 82, 'DE', 50.71106000, 7.96040000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q624067'),
(25281, 'Friedland', 3007, 'MV', 82, 'DE', 53.67028000, 13.55400000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q46009'),
(25282, 'Friedland', 3013, 'BB', 82, 'DE', 52.10493000, 14.26399000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, '*********'),
(25283, 'Friedland', 3008, 'NI', 82, 'DE', 51.41917000, 9.91762000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, '*********'),
(25284, 'Friedrichroda', 3015, 'TH', 82, 'DE', 50.85754000, 10.56507000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q520025'),
(25285, 'Friedrichsbrunn', 3011, 'ST', 82, 'DE', 51.69028000, 11.03737000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q704165'),
(25286, 'Friedrichsdorf', 3018, 'HE', 82, 'DE', 50.24962000, 8.64281000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q627742'),
(25287, 'Friedrichsfelde', 3010, 'BE', 82, 'DE', 52.50575000, 13.50812000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q563588'),
(25288, 'Friedrichshafen', 3006, 'BW', 82, 'DE', 47.65689000, 9.47554000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q4051'),
(25289, 'Friedrichshagen', 3010, 'BE', 82, 'DE', 52.45052000, 13.62463000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q563528'),
(25290, 'Friedrichshain', 3010, 'BE', 82, 'DE', 52.51559000, 13.45482000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q317056'),
(25291, 'Friedrichskoog', 3005, 'SH', 82, 'DE', 54.01667000, 8.91667000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q538124'),
(25292, 'Friedrichstadt', 3011, 'ST', 82, 'DE', 51.88751000, 12.66947000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q538124'),
(25293, 'Friedrichstadt', 3005, 'SH', 82, 'DE', 54.37566000, 9.08672000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q21065'),
(25294, 'Friedrichsthal', 3020, 'SL', 82, 'DE', 49.32786000, 7.09622000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q32018650'),
(25295, 'Frielendorf', 3018, 'HE', 82, 'DE', 50.97073000, 9.32269000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q428950'),
(25296, 'Friemar', 3015, 'TH', 82, 'DE', 50.97702000, 10.78851000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q568145'),
(25297, 'Friesack', 3013, 'BB', 82, 'DE', 52.73764000, 12.57969000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q585632'),
(25298, 'Friesenhagen', 3019, 'RP', 82, 'DE', 50.90377000, 7.80961000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q627732'),
(25299, 'Friesenried', 3009, 'BY', 82, 'DE', 47.87477000, 10.53464000, '2019-10-05 22:40:51', '2019-10-05 22:40:51', 1, 'Q627732'),
(25300, 'Friesoythe', 3008, 'NI', 82, 'DE', 53.02260000, 7.85764000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q559637'),
(25301, 'Friolzheim', 3006, 'BW', 82, 'DE', 48.83333000, 8.83333000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q81721'),
(25302, 'Frittlingen', 3006, 'BW', 82, 'DE', 48.12709000, 8.70774000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q550786'),
(25303, 'Fritzlar', 3018, 'HE', 82, 'DE', 51.13181000, 9.27557000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q492503'),
(25304, 'Frohburg', 3021, 'SN', 82, 'DE', 51.05719000, 12.55746000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q10763'),
(25305, 'Frohnau', 3010, 'BE', 82, 'DE', 52.63336000, 13.29024000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q641467'),
(25306, 'Fronhausen', 3018, 'HE', 82, 'DE', 50.70000000, 8.70000000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q624872'),
(25307, 'Fronreute', 3006, 'BW', 82, 'DE', 47.87053000, 9.56944000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q624872'),
(25308, 'Frontenhausen', 3009, 'BY', 82, 'DE', 48.54628000, 12.53118000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q32019315'),
(25309, 'Frose', 3011, 'ST', 82, 'DE', 51.79581000, 11.37914000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q822173'),
(25310, 'Fränkisch-Crumbach', 3018, 'HE', 82, 'DE', 49.74611000, 8.85861000, '2019-10-05 22:40:52', '2020-05-01 17:22:48', 1, 'Q622126'),
(25311, 'Fröndenberg', 3017, 'NW', 82, 'DE', 51.47563000, 7.76946000, '2019-10-05 22:40:52', '2020-05-01 17:22:49', 1, 'Q10933'),
(25312, 'Fuchsmühl', 3009, 'BY', 82, 'DE', 49.92265000, 12.14582000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q33437329'),
(25313, 'Fuchsstadt', 3009, 'BY', 82, 'DE', 50.10825000, 9.93370000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q504304'),
(25314, 'Fuhlsbüttel', 3016, 'HH', 82, 'DE', 53.63459000, 10.01608000, '2019-10-05 22:40:52', '2020-05-01 17:22:48', 1, 'Q1163'),
(25315, 'Fulda', 3018, 'HE', 82, 'DE', 50.55162000, 9.67518000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q3963'),
(25316, 'Fuldatal', 3018, 'HE', 82, 'DE', 51.38333000, 9.56667000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q625894'),
(25317, 'Furth', 3009, 'BY', 82, 'DE', 48.40616000, 12.38121000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q32020588'),
(25318, 'Furth im Wald', 3009, 'BY', 82, 'DE', 49.30955000, 12.84156000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q502370'),
(25319, 'Furtwangen', 3006, 'BW', 82, 'DE', 48.05156000, 8.20715000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q82717'),
(25320, 'Fußgönheim', 3019, 'RP', 82, 'DE', 49.46111000, 8.29222000, '2019-10-05 22:40:52', '2020-05-01 17:22:49', 1, 'Q22460'),
(25321, 'Föhren', 3019, 'RP', 82, 'DE', 49.85929000, 6.76480000, '2019-10-05 22:40:52', '2020-05-01 17:22:49', 1, 'Q543244'),
(25322, 'Förderstedt', 3011, 'ST', 82, 'DE', 51.89717000, 11.63349000, '2019-10-05 22:40:52', '2020-05-01 17:22:49', 1, 'Q698748'),
(25323, 'Föritz', 3015, 'TH', 82, 'DE', 50.34174000, 11.23180000, '2019-10-05 22:40:52', '2020-05-01 17:22:50', 1, 'Q33437435'),
(25324, 'Fünfstetten', 3009, 'BY', 82, 'DE', 48.83215000, 10.76540000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q512876'),
(25325, 'Fürfeld', 3019, 'RP', 82, 'DE', 49.77737000, 7.89264000, '2019-10-05 22:40:52', '2020-05-01 17:22:49', 1, 'Q564247'),
(25326, 'Fürstenau', 3008, 'NI', 82, 'DE', 52.51667000, 7.67670000, '2019-10-05 22:40:52', '2020-05-01 17:22:48', 1, 'Q564247'),
(25327, 'Fürstenberg', 3008, 'NI', 82, 'DE', 51.73333000, 9.40000000, '2019-10-05 22:40:52', '2020-05-01 17:22:48', 1, 'Q564247'),
(25328, 'Fürstenberg', 3013, 'BB', 82, 'DE', 53.18427000, 13.14442000, '2019-10-05 22:40:52', '2020-05-01 17:22:48', 1, 'Q564247'),
(25329, 'Fürstenberg', 3006, 'BW', 82, 'DE', 47.67895000, 9.15535000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q564247'),
(25330, 'Fürsteneck', 3009, 'BY', 82, 'DE', 48.71667000, 13.46667000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q505727'),
(25331, 'Fürstenfeldbruck', 3009, 'BY', 82, 'DE', 48.17904000, 11.25470000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q505727');

