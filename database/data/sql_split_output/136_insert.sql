INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(68484, 'Bacame Nuevo', 3468, 'S<PERSON>', 142, 'MX', 27.15899000, -109.59286000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q513027'),
(68485, 'Bacanora', 3468, 'SON', 142, 'MX', 28.98152000, -109.40012000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2652290'),
(68486, '<PERSON><PERSON><PERSON>', 3468, 'SON', 142, 'MX', 30.35565000, -108.93147000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2991325'),
(68487, '<PERSON><PERSON><PERSON>', 3451, 'CHP', 142, 'M<PERSON>', 17.03963000, -92.18986000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q2991325'),
(68488, '<PERSON>igualatito', 3449, 'S<PERSON>', 142, 'MX', 24.71344000, -107.46938000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2991325'),
(68489, 'Bachoco', 3449, 'SIN', 142, 'MX', 25.69687000, -108.81437000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2991325'),
(68490, 'Bachomobampo Número Dos', 3449, 'SIN', 142, 'MX', 25.73888000, -109.14776000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20274666'),
(68491, 'Bachíniva', 3447, 'CHH', 142, 'MX', 28.76801000, -107.25559000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q863131'),
(68492, 'Bacobampo', 3468, 'SON', 142, 'MX', 26.98239000, -109.65350000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q863131'),
(68493, 'Bacorehuis', 3449, 'SIN', 142, 'MX', 26.31749000, -109.08510000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q863131'),
(68494, 'Bacubirito', 3449, 'SIN', 142, 'MX', 25.80944000, -107.91500000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20252918'),
(68495, 'Badiraguato', 3449, 'SIN', 142, 'MX', 25.36525000, -107.55083000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q1552895'),
(68496, 'Bagojo Colectivo', 3449, 'SIN', 142, 'MX', 25.87417000, -109.11778000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274670'),
(68497, 'Bahuichivo', 3447, 'CHH', 142, 'MX', 27.40950000, -108.06740000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274670'),
(68498, 'Bahía Asunción', 3460, 'BCS', 142, 'MX', 27.14231000, -114.29582000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q4075356'),
(68499, 'Bahía Tortugas', 3460, 'BCS', 142, 'MX', 27.69056000, -114.89660000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q4075356'),
(68500, 'Bahía de Kino', 3468, 'SON', 142, 'MX', 28.82278000, -111.94083000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q477216'),
(68501, 'Bahía de Lobos', 3468, 'SON', 142, 'MX', 27.35167000, -110.45472000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20140235'),
(68502, 'Bajos de Chila', 3448, 'OAX', 142, 'MX', 15.92343000, -97.12113000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20140235'),
(68503, 'Bajos del Ejido', 3459, 'GRO', 142, 'MX', 16.96086000, -99.97169000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20205553'),
(68504, 'Bajucu', 3451, 'CHP', 142, 'MX', 16.47520000, -92.06471000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20205553'),
(68505, 'Bajío de Bonillas', 3469, 'GUA', 142, 'MX', 20.94944000, -101.49528000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20140376'),
(68506, 'Bajío de San Nicolás', 3462, 'ZAC', 142, 'MX', 22.56499000, -102.00784000, '2019-10-05 23:08:29', '2020-05-01 17:23:03', 1, 'Q20140376'),
(68507, 'Balancán', 3454, 'TAB', 142, 'MX', 17.80903000, -91.53682000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q2075650'),
(68508, 'Balleza', 3447, 'CHH', 142, 'MX', 26.95154000, -106.34921000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2606280'),
(68509, 'Bamoa', 3449, 'SIN', 142, 'MX', 25.70523000, -108.34614000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2606280'),
(68510, 'Banco Nacional', 3453, 'DUR', 142, 'MX', 25.87139000, -103.35667000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20140802'),
(68511, 'Banderas', 3464, 'VER', 142, 'MX', 20.99083000, -97.39361000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20264030'),
(68512, 'Banderas (Guatacalca 2da. Sección)', 3454, 'TAB', 142, 'MX', 18.15167000, -92.89881000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20140828'),
(68513, 'Banderas del Águila', 3453, 'DUR', 142, 'MX', 23.92596000, -105.33950000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20217905'),
(68514, 'Banderilla', 3464, 'VER', 142, 'MX', 19.58893000, -96.93727000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253078'),
(68515, 'Bangandhó', 3470, 'HID', 142, 'MX', 20.48277000, -99.14901000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20253078'),
(68516, 'Bara de Chachalacas', 3464, 'VER', 142, 'MX', 19.40643000, -96.34159000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253078'),
(68517, 'Bariometo', 3449, 'SIN', 142, 'MX', 24.76695000, -107.66254000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253078'),
(68518, 'Barra de Cazones', 3464, 'VER', 142, 'MX', 20.72307000, -97.20320000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253078'),
(68519, 'Barra de Tecoanapa', 3459, 'GRO', 142, 'MX', 16.50864000, -98.73216000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q27767804'),
(68520, 'Barrancas', 3464, 'VER', 142, 'MX', 18.06918000, -94.58691000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q27767804'),
(68521, 'Barrancas y Amate 3ra. Sección', 3454, 'TAB', 142, 'MX', 17.97100000, -92.76173000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20141424'),
(68522, 'Barretal', 3463, 'TAM', 142, 'MX', 24.08337000, -99.12526000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20141424'),
(68523, 'Barretos', 3469, 'GUA', 142, 'MX', 20.93933000, -101.64282000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20141424'),
(68524, 'Barrio Aztlán', 3470, 'HID', 142, 'MX', 20.46562000, -98.08553000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20141578'),
(68525, 'Barrio Bordo Nuevo', 3450, 'MEX', 142, 'MX', 19.37917000, -99.67333000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274727'),
(68526, 'Barrio Chiquichuca', 3450, 'MEX', 142, 'MX', 19.32139000, -100.21278000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237029'),
(68527, 'Barrio Cuarto (La Loma)', 3450, 'MEX', 142, 'MX', 19.76861000, -99.67694000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274728'),
(68528, 'Barrio Nuevo', 3459, 'GRO', 142, 'MX', 17.72075000, -101.63657000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274728'),
(68529, 'Barrio Nuevo de los Muertos', 3459, 'GRO', 142, 'MX', 16.89278000, -99.54389000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20205612'),
(68530, 'Barrio San Diego', 3448, 'OAX', 142, 'MX', 17.27917000, -97.67417000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274778'),
(68531, 'Barrio San Joaquín el Junco', 3450, 'MEX', 142, 'MX', 19.56417000, -99.77333000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20237183'),
(68532, 'Barrio San Miguel Dorami', 3450, 'MEX', 142, 'MX', 19.44622000, -99.34296000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274781'),
(68533, 'Barrio Santa Cruz', 3450, 'MEX', 142, 'MX', 19.35139000, -99.66278000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237211'),
(68534, 'Barrio Tepetitlán Emilio Portes Gil', 3450, 'MEX', 142, 'MX', 19.65306000, -99.88667000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20237223'),
(68535, 'Barrio Tlatenco', 3450, 'MEX', 142, 'MX', 19.73917000, -99.19167000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237228'),
(68536, 'Barrio de Arriba de San Juan Xoconusco', 3450, 'MEX', 142, 'MX', 19.32040000, -100.27020000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237228'),
(68537, 'Barrio de Boyecha', 3450, 'MEX', 142, 'MX', 19.70778000, -99.68444000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20141682'),
(68538, 'Barrio de Canales', 3450, 'MEX', 142, 'MX', 19.37333000, -99.37556000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274730'),
(68539, 'Barrio de Centro del Cerrillo', 3450, 'MEX', 142, 'MX', 19.53694000, -99.95417000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20141698'),
(68540, 'Barrio de Ensido', 3450, 'MEX', 142, 'MX', 19.49389000, -99.37444000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274733'),
(68541, 'Barrio de España', 3450, 'MEX', 142, 'MX', 19.86139000, -99.08167000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20274735'),
(68542, 'Barrio de Guadalupe', 3450, 'MEX', 142, 'MX', 19.23054000, -98.93090000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20141756'),
(68543, 'Barrio de Guadalupe del Mezquitillo', 3469, 'GUA', 142, 'MX', 20.94417000, -101.80083000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20205514'),
(68544, 'Barrio de Jesús Fracción Primera', 3450, 'MEX', 142, 'MX', 19.36778000, -99.68694000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20274737'),
(68545, 'Barrio de México', 3450, 'MEX', 142, 'MX', 19.28250000, -99.82833000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20141929'),
(68546, 'Barrio de Nuevo León', 3476, 'PUE', 142, 'MX', 19.08861000, -98.29000000, '2019-10-05 23:08:29', '2020-05-01 17:23:01', 1, 'Q20141931'),
(68547, 'Barrio de Puentecillas', 3450, 'MEX', 142, 'MX', 19.53558000, -99.98463000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237068'),
(68548, 'Barrio de San Isidro', 3450, 'MEX', 142, 'MX', 19.52139000, -99.95861000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237074'),
(68549, 'Barrio de San Juan', 3450, 'MEX', 142, 'MX', 19.41420000, -99.53717000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237079'),
(68550, 'Barrio de San Miguel', 3464, 'VER', 142, 'MX', 19.64333000, -97.12389000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253424'),
(68551, 'Barrio de San Miguel', 3450, 'MEX', 142, 'MX', 19.51232000, -99.95467000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253424'),
(68552, 'Barrio de San Pedro la Cabecera', 3450, 'MEX', 142, 'MX', 19.56528000, -99.75000000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274749'),
(68553, 'Barrio de San Ramón', 3450, 'MEX', 142, 'MX', 19.37361000, -99.33111000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20142019'),
(68554, 'Barrio de la Barranca', 3450, 'MEX', 142, 'MX', 19.43583000, -99.55833000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237058'),
(68555, 'Barrio del Cajón', 3450, 'MEX', 142, 'MX', 19.39139000, -99.65806000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20237102'),
(68556, 'Barrio el Boncho', 3450, 'MEX', 142, 'MX', 19.42611000, -100.10639000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20142197'),
(68557, 'Barrio el Vivero', 3450, 'MEX', 142, 'MX', 19.45306000, -100.03528000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20237130'),
(68558, 'Barrio la Joya', 3450, 'MEX', 142, 'MX', 19.42000000, -100.09972000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253544'),
(68559, 'Barrio la Tenería', 3450, 'MEX', 142, 'MX', 19.69361000, -99.79194000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20142520'),
(68560, 'Barrio los Tules', 3450, 'MEX', 142, 'MX', 19.42917000, -100.07472000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274769'),
(68561, 'Barrón', 3449, 'SIN', 142, 'MX', 23.12322000, -106.27650000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20274769'),
(68562, 'Barrón', 3469, 'GUA', 142, 'MX', 20.67667000, -101.08423000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20274769'),
(68563, 'Basaseachic', 3447, 'CHH', 142, 'MX', 28.20626000, -108.21270000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274769'),
(68564, 'Basconcobe', 3468, 'SON', 142, 'MX', 26.95491000, -109.66860000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q28051646'),
(68565, 'Basúchil', 3447, 'CHH', 142, 'MX', 28.52479000, -107.40172000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q4868437'),
(68566, 'Batopilas', 3447, 'CHH', 142, 'MX', 27.02846000, -107.74125000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2651150'),
(68567, 'Bautista Chico', 3451, 'CHP', 142, 'MX', 16.79972000, -92.71194000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20274799'),
(68568, 'Bavispe', 3468, 'SON', 142, 'MX', 30.47931000, -108.93982000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q2919569'),
(68569, 'Becanchén', 3466, 'YUC', 142, 'MX', 19.87467000, -89.21732000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q2919569'),
(68570, 'Bejucal de Ocampo', 3451, 'CHP', 142, 'MX', 15.45535000, -92.15835000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q1507960'),
(68571, 'Bejucos', 3450, 'MEX', 142, 'MX', 18.77802000, -100.42778000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q1507960'),
(68572, 'Belem', 3450, 'MEX', 142, 'MX', 19.63334000, -98.79664000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q1507960'),
(68573, 'Belisario Domínguez', 3474, 'MIC', 142, 'MX', 19.87140000, -100.97184000, '2019-10-05 23:08:29', '2020-05-01 17:23:01', 1, 'Q1507960'),
(68574, 'Belisario Domínguez', 3451, 'CHP', 142, 'MX', 15.29944000, -92.38056000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q20274815'),
(68575, 'Bella Esperanza', 3464, 'VER', 142, 'MX', 19.43329000, -96.86648000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20253861'),
(68576, 'Bella Vista', 3451, 'CHP', 142, 'MX', 15.58333000, -92.21667000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q1784069'),
(68577, 'Bella Vista del Río', 3455, 'QUE', 142, 'MX', 20.69000000, -99.57139000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20246126'),
(68578, 'Bellas Fuentes', 3474, 'MIC', 142, 'MX', 19.82132000, -101.67950000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20246126'),
(68579, 'Bellavista', 3477, 'NAY', 142, 'MX', 21.56236000, -104.88267000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20246126'),
(68580, 'Bellavista', 3449, 'SIN', 142, 'MX', 24.81373000, -107.46188000, '2019-10-05 23:08:29', '2019-10-05 23:08:29', 1, 'Q20246126'),
(68581, 'Bellavista de Victoria (San José Bellavista)', 3476, 'PUE', 142, 'MX', 18.87639000, -97.54389000, '2019-10-05 23:08:29', '2020-05-01 17:23:01', 1, 'Q20229009'),
(68582, 'Belén', 3454, 'TAB', 142, 'MX', 17.78907000, -92.61137000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20227628'),
(68583, 'Belén Atzitzimititlán', 3458, 'TLA', 142, 'MX', 19.34694000, -98.19000000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20224335'),
(68584, 'Benemérito Juárez', 3448, 'OAX', 142, 'MX', 18.11944000, -96.00194000, '2019-10-05 23:08:29', '2020-05-01 17:23:01', 1, 'Q20274830'),
(68585, 'Benemérito de las Américas', 3451, 'CHP', 142, 'MX', 16.51599000, -90.65371000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q20274829'),
(68586, 'Benito García (El Zorrillo)', 3457, 'BCN', 142, 'MX', 31.67111000, -116.51139000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q20217835'),
(68587, 'Benito González', 3454, 'TAB', 142, 'MX', 17.86993000, -92.70834000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20217835'),
(68588, 'Benito Juarez', 3473, 'CMX', 142, 'MX', 19.39840000, -99.15766000, '2019-10-05 23:08:29', '2024-01-24 12:10:50', 1, 'Q2356998'),
(68589, 'Benito Juárez', 3458, 'TLA', 142, 'MX', 19.37391000, -97.90900000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20254040'),
(68590, 'Benito Juárez', 3470, 'HID', 142, 'MX', 20.16047000, -98.82606000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q20254040'),
(68591, 'Benito Juárez', 3454, 'TAB', 142, 'MX', 18.42573000, -92.80285000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20254040'),
(68592, 'Benito Juárez', 3476, 'PUE', 142, 'MX', 19.81654000, -97.79108000, '2019-10-05 23:08:29', '2020-05-01 17:23:01', 1, 'Q20144109'),
(68593, 'Benito Juárez', 3448, 'OAX', 142, 'MX', 15.88178000, -96.32405000, '2019-10-05 23:08:29', '2020-05-01 17:23:01', 1, 'Q20144109'),
(68594, 'Benito Juárez', 3464, 'VER', 142, 'MX', 20.88635000, -98.20594000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q4888100'),
(68595, 'Benito Juárez', 3450, 'MEX', 142, 'MX', 19.43765000, -99.85384000, '2019-10-05 23:08:29', '2020-05-01 17:23:00', 1, 'Q4888100'),
(68596, 'Benito Juárez', 3457, 'BCN', 142, 'MX', 32.56852000, -114.99422000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q4888100'),
(68598, 'Benito Juárez', 3447, 'CHH', 142, 'MX', 29.74624000, -107.93966000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q20211838'),
(68599, 'Benito Juárez', 3474, 'MIC', 142, 'MX', 19.23333000, -100.46667000, '2019-10-05 23:08:29', '2020-05-01 17:23:01', 1, 'Q20144003'),
(68600, 'Benito Juárez', 3449, 'SIN', 142, 'MX', 25.77373000, -109.03311000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20144003'),
(68601, 'Benito Juárez', 3451, 'CHP', 142, 'MX', 15.05500000, -92.19111000, '2019-10-05 23:08:29', '2020-05-01 17:22:59', 1, 'Q20143925'),
(68602, 'Benito Juárez', 3467, 'ROO', 142, 'MX', 21.02029000, -87.04101000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q2279198'),
(68603, 'Benito Juárez (La Playita)', 3454, 'TAB', 142, 'MX', 17.93231000, -93.36472000, '2019-10-05 23:08:29', '2020-05-01 17:23:02', 1, 'Q20227635'),
(68604, 'Benito Juárez (Vinatería)', 3449, 'SIN', 142, 'MX', 25.97376000, -108.87315000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20253977'),
(68605, 'Benito Juárez II (San Martín)', 3448, 'OAX', 142, 'MX', 18.14056000, -96.46139000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q20261660'),
(68606, 'Benito Juárez Uno', 3475, 'CAM', 142, 'MX', 18.27695000, -91.09914000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q20261660'),
(68607, 'Benjamín Hill', 3468, 'SON', 142, 'MX', 30.16904000, -111.11403000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q817772'),
(68608, 'Beristain', 3476, 'PUE', 142, 'MX', 20.09426000, -98.13290000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q817772'),
(68609, 'Bermejillo', 3453, 'DUR', 142, 'MX', 25.88682000, -103.62069000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q5726411'),
(68610, 'Bernal', 3455, 'QUE', 142, 'MX', 20.74030000, -99.94125000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q5726411'),
(68611, 'Berriozábal', 3451, 'CHP', 142, 'MX', 16.80000000, -93.26667000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q2007940'),
(68612, 'Betania', 3451, 'CHP', 142, 'MX', 16.60639000, -92.52194000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20210726'),
(68613, 'Betania', 3454, 'TAB', 142, 'MX', 18.28003000, -93.32081000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144308'),
(68614, 'Bethania', 3448, 'OAX', 142, 'MX', 17.91939000, -96.00459000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144308'),
(68615, 'Bienvenido', 3476, 'PUE', 142, 'MX', 20.12235000, -97.74434000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144308'),
(68616, 'Bimbaletes Aguascalientes (El Álamo)', 3456, 'AGU', 142, 'MX', 22.23444000, -102.01917000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q20274868'),
(68617, 'Blanca Espuma', 3464, 'VER', 142, 'MX', 19.58117000, -96.68819000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20274868'),
(68618, 'Blasillo 1ra. Sección (Nicolás Bravo)', 3454, 'TAB', 142, 'MX', 18.07861000, -93.91833000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20144556'),
(68619, 'Bledos', 3461, 'SLP', 142, 'MX', 21.84148000, -101.11651000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144556'),
(68620, 'Bobashi de Guadalupe', 3450, 'MEX', 142, 'MX', 19.87583000, -99.91167000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144577'),
(68621, 'Boca del Monte', 3464, 'VER', 142, 'MX', 19.15451000, -96.83270000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20220968'),
(68622, 'Boca del Rio', 3464, 'VER', 142, 'MX', 19.10464000, -96.10405000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q888954'),
(68623, 'Bocaneo (San Pedro)', 3474, 'MIC', 142, 'MX', 19.83861000, -100.81722000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144799'),
(68624, 'Bocas', 3461, 'SLP', 142, 'MX', 22.51817000, -101.02186000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144799'),
(68625, 'Bochil', 3451, 'CHP', 142, 'MX', 16.99625000, -92.89218000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20274882'),
(68626, 'Bochojbo Alto', 3451, 'CHP', 142, 'MX', 16.73583000, -92.71694000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20144818'),
(68627, 'Bocoyna', 3447, 'CHH', 142, 'MX', 27.84133000, -107.58918000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2997082'),
(68628, 'Bokoba', 3466, 'YUC', 142, 'MX', 21.00755000, -89.17913000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q8250474'),
(68630, 'Bolonchén de Rejón', 3475, 'CAM', 142, 'MX', 20.00386000, -89.74663000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q2576175'),
(68631, 'Bolón', 3466, 'YUC', 142, 'MX', 20.85010000, -89.83193000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q2576175'),
(68632, 'Bomanxotha', 3470, 'HID', 142, 'MX', 20.51542000, -99.66679000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2576175'),
(68633, 'Bomintzha', 3470, 'HID', 142, 'MX', 20.01400000, -99.27277000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2576175'),
(68634, 'Bondojito', 3470, 'HID', 142, 'MX', 20.44395000, -99.70022000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2576175'),
(68635, 'Bonifacio García', 3465, 'MOR', 142, 'MX', 18.71786000, -99.12011000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q2576175'),
(68636, 'Bonifacio Moreno (El Aguaje)', 3474, 'MIC', 142, 'MX', 18.99167000, -102.71083000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20235974'),
(68637, 'Boquiapa', 3454, 'TAB', 142, 'MX', 18.16111000, -93.13750000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20227645'),
(68638, 'Boquilla de Babisas (La Boquilla de Conchos)', 3447, 'CHH', 142, 'MX', 27.54861000, -105.40361000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q8250809'),
(68639, 'Boquilla de las Perlas', 3471, 'COA', 142, 'MX', 25.32803000, -103.28870000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q8250809'),
(68640, 'Boquillas', 3469, 'GUA', 142, 'MX', 20.41642000, -101.43333000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q8250809'),
(68641, 'Bordo Blanco', 3455, 'QUE', 142, 'MX', 20.49631000, -99.92855000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q8250809'),
(68642, 'Boshindó', 3450, 'MEX', 142, 'MX', 19.91922000, -99.84075000, '2019-10-05 23:08:30', '2020-05-01 17:23:00', 1, 'Q8250809'),
(68643, 'Bosque de Saloya', 3454, 'TAB', 142, 'MX', 18.01611000, -92.95806000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20145182'),
(68644, 'Bosques de San Pedro', 3452, 'NLE', 142, 'MX', 25.60139000, -100.17972000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20233470'),
(68645, 'Bosques de la Magdalena', 3450, 'MEX', 142, 'MX', 19.35778000, -98.94750000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20145208'),
(68646, 'Boxasní', 3455, 'QUE', 142, 'MX', 20.67083000, -99.84861000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20246128'),
(68647, 'Boyé', 3455, 'QUE', 142, 'MX', 20.68091000, -99.73933000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20246128'),
(68648, 'Bravo', 3455, 'QUE', 142, 'MX', 20.39977000, -100.42423000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20246128'),
(68649, 'Brisas Barra de Suchiate', 3451, 'CHP', 142, 'MX', 14.53588000, -92.22414000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20285831'),
(68650, 'Brisas de Zicatela', 3448, 'OAX', 142, 'MX', 15.83694000, -97.04194000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q5734043'),
(68651, 'Brisas del Carrizal', 3454, 'TAB', 142, 'MX', 18.01556000, -92.97083000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20227646'),
(68652, 'Briseñas', 3474, 'MIC', 142, 'MX', 20.25149000, -102.57124000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q10741338'),
(68653, 'Briseñas de Matamoros', 3474, 'MIC', 142, 'MX', 20.26821000, -102.56195000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q20235984'),
(68654, 'Buaysiacobe', 3468, 'SON', 142, 'MX', 27.06795000, -109.68628000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q27949120'),
(68655, 'Buayums', 3468, 'SON', 142, 'MX', 26.86336000, -109.43427000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q27949120'),
(68656, 'Bucerías', 3477, 'NAY', 142, 'MX', 20.75626000, -105.33438000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q4982521'),
(68657, 'Buctzotz', 3466, 'YUC', 142, 'MX', 21.20303000, -88.79271000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q8253334'),
(68658, 'Buen Retiro (El Retiro)', 3449, 'SIN', 142, 'MX', 25.46194000, -108.47833000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20274912'),
(68659, 'Buena Vista', 3464, 'VER', 142, 'MX', 19.04278000, -96.85667000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20145790'),
(68660, 'Buena Vista', 3459, 'GRO', 142, 'MX', 16.96250000, -98.58077000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20145790'),
(68661, 'Buena Vista', 3452, 'NLE', 142, 'MX', 25.85222000, -100.36000000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20254454'),
(68662, 'Buena Vista', 3476, 'PUE', 142, 'MX', 20.12767000, -97.45373000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20254454'),
(68663, 'Buena Vista (Apasco)', 3454, 'TAB', 142, 'MX', 17.65556000, -92.44389000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20261262'),
(68664, 'Buena Vista (Puxcatán)', 3454, 'TAB', 142, 'MX', 17.73073000, -92.61996000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q27825693'),
(68665, 'Buena Vista 1ra. Sección', 3454, 'TAB', 142, 'MX', 18.14417000, -92.74972000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20227648'),
(68666, 'Buena Vista de la Salud', 3459, 'GRO', 142, 'MX', 17.26082000, -99.50183000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20227648'),
(68667, 'Buenaventura', 3447, 'CHH', 142, 'MX', 30.02116000, -107.19308000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2656434'),
(68668, 'Buenavista', 3454, 'TAB', 142, 'MX', 17.91667000, -92.53333000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20227656'),
(68669, 'Buenavista', 3464, 'VER', 142, 'MX', 18.89306000, -97.03694000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20221044'),
(68670, 'Buenavista', 3450, 'MEX', 142, 'MX', 19.60833000, -99.16944000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1801492'),
(68671, 'Buenavista', 3465, 'MOR', 142, 'MX', 19.80082000, -100.04385000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1801492'),
(68672, 'Buenavista', 3455, 'QUE', 142, 'MX', 20.82372000, -100.47009000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1801492'),
(68673, 'Buenavista', 3451, 'CHP', 142, 'MX', 16.24278000, -91.98778000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20210864'),
(68674, 'Buenavista', 3476, 'PUE', 142, 'MX', 19.25767000, -97.07531000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20229059'),
(68675, 'Buenavista', 3474, 'MIC', 142, 'MX', 19.18802000, -102.58471000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q4985488'),
(68676, 'Buenavista (Matasanos)', 3451, 'CHP', 142, 'MX', 17.10778000, -93.06694000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20210857'),
(68677, 'Buenavista Tetela', 3476, 'PUE', 142, 'MX', 18.91967000, -98.17632000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20145981'),
(68678, 'Buenavista Tomatlán', 3474, 'MIC', 142, 'MX', 19.21281000, -102.58735000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q20145986'),
(68679, 'Buenavista de Allende', 3459, 'GRO', 142, 'MX', 17.00086000, -99.21325000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20145986'),
(68680, 'Buenavista de Benito Juárez', 3474, 'MIC', 142, 'MX', 18.46034000, -98.63735000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q20145986'),
(68681, 'Buenavista de Cortés', 3469, 'GUA', 142, 'MX', 20.37326000, -101.87021000, '2019-10-05 23:08:30', '2020-05-01 17:23:00', 1, 'Q20145986'),
(68682, 'Buenavista de Cuéllar', 3459, 'GRO', 142, 'MX', 18.45958000, -99.40868000, '2019-10-05 23:08:30', '2020-05-01 17:23:00', 1, 'Q3311271'),
(68683, 'Buenavista de Juárez', 3476, 'PUE', 142, 'MX', 18.94500000, -97.82778000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q20229054'),
(68684, 'Buenavista de Trujillo', 3462, 'ZAC', 142, 'MX', 23.15200000, -103.18767000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20229054'),
(68685, 'Buenos Aires', 3464, 'VER', 142, 'MX', 18.46463000, -95.24879000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20229054'),
(68686, 'Buenos Aires', 3451, 'CHP', 142, 'MX', 14.88980000, -92.48121000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20229054'),
(68687, 'Buenos Aires', 3450, 'MEX', 142, 'MX', 19.61745000, -99.66790000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20229054'),
(68688, 'Buenos Aires', 3474, 'MIC', 142, 'MX', 18.02444000, -102.28583000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20146661'),
(68689, 'Buenos Aires', 3457, 'BCN', 142, 'MX', 32.42278000, -116.94111000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20274956'),
(68690, 'Buenos Aires', 3476, 'PUE', 142, 'MX', 20.15333000, -97.74833000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20229066'),
(68691, 'Buenos Aires (San Isidro)', 3464, 'VER', 142, 'MX', 20.84028000, -97.60139000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20268941'),
(68692, 'Burgos', 3463, 'TAM', 142, 'MX', 24.94722000, -98.79921000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q49868682'),
(68693, 'Bustamante', 3463, 'TAM', 142, 'MX', 23.43524000, -99.75875000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q49868694'),
(68694, 'Bustamante', 3452, 'NLE', 142, 'MX', 26.55567000, -100.50603000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3850267'),
(68695, 'Bácum', 3468, 'SON', 142, 'MX', 27.55142000, -110.08330000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q3844371'),
(68696, 'Bécal', 3475, 'CAM', 142, 'MX', 20.44172000, -90.02738000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q20143370'),
(68697, 'C-32 (Licenciado Francisco Trujillo Gurría)', 3454, 'TAB', 142, 'MX', 17.96833000, -93.50139000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20146885'),
(68698, 'C-41 (Licenciado Carlos A. Madrazo)', 3454, 'TAB', 142, 'MX', 17.92306000, -93.44806000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20227657'),
(68699, 'CEFERESO Número 3', 3463, 'TAM', 142, 'MX', 25.84722000, -97.63333000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20227642'),
(68700, 'CERESO Nuevo', 3449, 'SIN', 142, 'MX', 25.89139000, -109.04222000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20257391'),
(68701, 'Cabecera de Indígenas', 3450, 'MEX', 142, 'MX', 19.37213000, -100.13667000, '2019-10-05 23:08:30', '2020-05-01 17:23:00', 1, 'Q20257391'),
(68702, 'Cabeza de Toro', 3451, 'CHP', 142, 'MX', 15.93698000, -93.78256000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20257391'),
(68703, 'Cabezas', 3464, 'VER', 142, 'MX', 19.37167000, -96.38278000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147049'),
(68704, 'Cabo San Lucas', 3460, 'BCS', 142, 'MX', 22.89088000, -109.91238000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1020776'),
(68705, 'Cacahoatán', 3451, 'CHP', 142, 'MX', 14.99201000, -92.16376000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q20254830'),
(68706, 'Cacahuatal', 3464, 'VER', 142, 'MX', 18.83098000, -96.84192000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20254830'),
(68707, 'Cacalchen', 3466, 'YUC', 142, 'MX', 20.98358000, -89.22686000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q4208884'),
(68709, 'Cacalomacan', 3450, 'MEX', 142, 'MX', 19.25311000, -99.70623000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20236020'),
(68710, 'Cacalotenango', 3459, 'GRO', 142, 'MX', 18.54639000, -99.64361000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147168'),
(68711, 'Cacalotán', 3449, 'SIN', 142, 'MX', 23.06779000, -105.84194000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20147168'),
(68712, 'Cacaloxúchitl', 3476, 'PUE', 142, 'MX', 18.78747000, -98.48614000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q20147183'),
(68713, 'Cacalutla', 3459, 'GRO', 142, 'MX', 17.12433000, -100.35038000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147183'),
(68714, 'Cacao', 3467, 'ROO', 142, 'MX', 18.19458000, -88.69529000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147183'),
(68715, 'Cacaté', 3451, 'CHP', 142, 'MX', 16.84534000, -92.83485000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q20147183'),
(68716, 'Cadereyta', 3455, 'QUE', 142, 'MX', 20.69701000, -99.81624000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2878559'),
(68717, 'Cadereyta', 3452, 'NLE', 142, 'MX', 25.58333000, -99.98333000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147277'),
(68718, 'Cadereyta Jiménez', 3452, 'NLE', 142, 'MX', 25.58896000, -100.00156000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q2038325'),
(68719, 'Cahuatache', 3459, 'GRO', 142, 'MX', 17.41624000, -98.53184000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2038325'),
(68720, 'Caimanero', 3449, 'SIN', 142, 'MX', 25.60564000, -108.44247000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2038325'),
(68721, 'Caja de Agua', 3450, 'MEX', 142, 'MX', 19.64329000, -99.36497000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2038325'),
(68722, 'Cajelitos', 3459, 'GRO', 142, 'MX', 17.26778000, -99.49306000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20205686'),
(68723, 'Cajonos', 3448, 'OAX', 142, 'MX', 17.16801000, -96.26174000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3889115'),
(68724, 'Cala Norte', 3476, 'PUE', 142, 'MX', 19.84778000, -97.42306000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20261392'),
(68725, 'Cala Sur', 3476, 'PUE', 142, 'MX', 19.83278000, -97.42111000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147458'),
(68726, 'Calakmul', 3475, 'CAM', 142, 'MX', 18.00000000, -89.75000000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2605348'),
(68727, 'Calamanda', 3455, 'QUE', 142, 'MX', 20.54672000, -100.18616000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2605348'),
(68728, 'Calcahualco', 3464, 'VER', 142, 'MX', 19.13698000, -97.14201000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3892704'),
(68729, 'Calcehtoc', 3466, 'YUC', 142, 'MX', 20.56807000, -89.91242000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3892704'),
(68730, 'Calderitas', 3467, 'ROO', 142, 'MX', 18.55564000, -88.25518000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q4210483'),
(68731, 'Calera Chica', 3465, 'MOR', 142, 'MX', 18.85833000, -99.18194000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147578'),
(68732, 'Caleras', 3472, 'COL', 142, 'MX', 18.99687000, -103.87898000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147578'),
(68733, 'Caleras de Ameche', 3469, 'GUA', 142, 'MX', 20.56133000, -100.56013000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147578'),
(68734, 'Calería', 3464, 'VER', 142, 'MX', 18.42883000, -95.17737000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q20147578'),
(68735, 'Calichar Palma Sola', 3464, 'VER', 142, 'MX', 20.44155000, -97.55103000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147578'),
(68736, 'California', 3453, 'DUR', 142, 'MX', 25.76090000, -103.37556000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20147578'),
(68737, 'Calihualá', 3448, 'OAX', 142, 'MX', 17.53134000, -98.27792000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q4576809'),
(68738, 'Calimaya', 3450, 'MEX', 142, 'MX', 19.16324000, -99.61810000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3300431'),
(68739, 'Calipan', 3476, 'PUE', 142, 'MX', 18.29519000, -97.16376000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20261389'),
(68740, 'Calixtlahuaca', 3450, 'MEX', 142, 'MX', 19.33651000, -99.68923000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1027061'),
(68741, 'Calkiní', 3475, 'CAM', 142, 'MX', 20.38583000, -89.98764000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q2605401'),
(68742, 'Calle Real', 3465, 'MOR', 142, 'MX', 19.48858000, -99.61332000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2605401'),
(68743, 'Calmeca', 3476, 'PUE', 142, 'MX', 18.63501000, -98.63414000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20261401'),
(68744, 'Calnali', 3470, 'HID', 142, 'MX', 20.90221000, -98.57029000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q27796628'),
(68745, 'Calotmul', 3466, 'YUC', 142, 'MX', 21.02494000, -88.12581000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2575635'),
(68746, 'Calpanería Atezquilla', 3476, 'PUE', 142, 'MX', 19.65556000, -97.99528000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q20147783'),
(68747, 'Calpulalpan', 3458, 'TLA', 142, 'MX', 19.58867000, -98.56972000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1997497'),
(68748, 'Caltimacan', 3470, 'HID', 142, 'MX', 20.53672000, -99.37000000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1997497'),
(68749, 'Caltzontzín', 3474, 'MIC', 142, 'MX', 19.42340000, -102.00673000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q1997497'),
(68750, 'Calvario Buenavista', 3450, 'MEX', 142, 'MX', 19.70387000, -99.95674000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1997497'),
(68751, 'Calvario del Carmen', 3450, 'MEX', 142, 'MX', 19.63562000, -100.01030000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1997497'),
(68752, 'Calvillo', 3456, 'AGU', 142, 'MX', 21.85000000, -102.71666000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2656541'),
(68753, 'Calzada Larga', 3451, 'CHP', 142, 'MX', 16.35115000, -93.31518000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2656541'),
(68754, 'Calzada del Tepozán', 3469, 'GUA', 142, 'MX', 20.73762000, -101.88299000, '2019-10-05 23:08:30', '2020-05-01 17:23:00', 1, 'Q2656541'),
(68755, 'Calzadas', 3464, 'VER', 142, 'MX', 18.10121000, -94.45524000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2656541'),
(68756, 'Camalotita', 3477, 'NAY', 142, 'MX', 22.40453000, -105.43156000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2656541'),
(68757, 'Camalú', 3457, 'BCN', 142, 'MX', 30.84222000, -116.06328000, '2019-10-05 23:08:30', '2020-05-01 17:22:59', 1, 'Q5024916'),
(68758, 'Camargo', 3447, 'CHH', 142, 'MX', 27.82905000, -104.78012000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q2652276'),
(68759, 'Camargo', 3463, 'TAM', 142, 'MX', 26.23130000, -98.85019000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3845876'),
(68760, 'Camarón de Tejeda', 3464, 'VER', 142, 'MX', 19.00810000, -96.56807000, '2019-10-05 23:08:30', '2020-05-01 17:23:02', 1, 'Q5024980'),
(68761, 'Camelia (Barrio la Camelia)', 3470, 'HID', 142, 'MX', 20.14917000, -98.72222000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q5721128'),
(68762, 'Camelia Roja', 3448, 'OAX', 142, 'MX', 18.02833000, -96.20806000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20255161'),
(68763, 'Camerino Z. Mendoza', 3464, 'VER', 142, 'MX', 18.78246000, -97.16016000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q1997984'),
(68764, 'Camichín de Jauja', 3477, 'NAY', 142, 'MX', 21.46532000, -104.80080000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q1997984'),
(68765, 'Camocuautla', 3476, 'PUE', 142, 'MX', 20.03805000, -97.75853000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3893085'),
(68766, 'Camotepec', 3476, 'PUE', 142, 'MX', 20.04720000, -98.06825000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3893085'),
(68767, 'Camotinchan', 3448, 'OAX', 142, 'MX', 16.55873000, -98.12433000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q3893085'),
(68768, 'Camotlán de Miraflores', 3472, 'COL', 142, 'MX', 19.22033000, -104.23491000, '2019-10-05 23:08:30', '2020-05-01 17:23:00', 1, 'Q3893085'),
(68769, 'Campanario', 3459, 'GRO', 142, 'MX', 16.83534000, -99.57178000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20205700'),
(68770, 'Campeche', 3475, 'CAM', 142, 'MX', 19.84386000, -90.52554000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q61301'),
(68771, 'Campestre Flamboyanes', 3466, 'YUC', 142, 'MX', 21.21000000, -89.65778000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q5745614'),
(68772, 'Campestre Tarímbaro', 3474, 'MIC', 142, 'MX', 19.77500000, -101.12917000, '2019-10-05 23:08:30', '2020-05-01 17:23:01', 1, 'Q3847328'),
(68773, 'Campestre Villas del Álamo', 3470, 'HID', 142, 'MX', 20.10167000, -98.70722000, '2019-10-05 23:08:30', '2020-05-01 17:23:00', 1, 'Q20241347'),
(68774, 'Campo Balbuena', 3449, 'SIN', 142, 'MX', 24.74688000, -107.54975000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20241347'),
(68775, 'Campo Carretero', 3468, 'SON', 142, 'MX', 30.77154000, -110.85380000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q28315004'),
(68776, 'Campo Chico', 3464, 'VER', 142, 'MX', 18.83639000, -97.02802000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20148604'),
(68777, 'Campo Grande', 3464, 'VER', 142, 'MX', 18.82296000, -97.01265000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20148604'),
(68778, 'Campo Pesquero el Colorado', 3449, 'SIN', 142, 'MX', 25.75828000, -109.31510000, '2019-10-05 23:08:30', '2019-10-05 23:08:30', 1, 'Q20148604'),
(68779, 'Campo Sesenta', 3468, 'SON', 142, 'MX', 27.45000000, -110.10000000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20227854'),
(68780, 'Campo de Águila', 3464, 'VER', 142, 'MX', 18.06750000, -94.98611000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20268951'),
(68781, 'Campo la Arrocera', 3449, 'SIN', 142, 'MX', 25.84500000, -108.91833000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20149073'),
(68782, 'Camémbaro', 3474, 'MIC', 142, 'MX', 19.42722000, -100.39889000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q20236042'),
(68783, 'Canalejas', 3450, 'MEX', 142, 'MX', 19.97867000, -99.60548000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20236042'),
(68784, 'Cananea', 3468, 'SON', 142, 'MX', 30.98699000, -110.29062000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q176924'),
(68785, 'Canatlán', 3453, 'DUR', 142, 'MX', 24.52576000, -104.77327000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q5124140'),
(68786, 'Cancuc', 3451, 'CHP', 142, 'MX', 16.91667000, -92.48333000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275169'),
(68787, 'Cancún', 3467, 'ROO', 142, 'MX', 21.17429000, -86.84656000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q8969'),
(68788, 'Candela', 3471, 'COA', 142, 'MX', 26.83840000, -100.66630000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q8261827'),
(68789, 'Candelaria', 3475, 'CAM', 142, 'MX', 18.00000000, -90.75000000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q2312662'),
(68790, 'Candelaria Loxicha', 3448, 'OAX', 142, 'MX', 15.92638000, -96.49268000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3845777'),
(68791, 'Candelaria Portezuelo', 3476, 'PUE', 142, 'MX', 19.04194000, -97.70083000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275174'),
(68792, 'Cansahcab', 3466, 'YUC', 142, 'MX', 21.15749000, -89.10131000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5747629'),
(68793, 'Cantabria', 3474, 'MIC', 142, 'MX', 19.83994000, -101.72652000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5747629'),
(68794, 'Cantamayec', 3466, 'YUC', 142, 'MX', 20.43186000, -89.07176000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q2575621'),
(68795, 'Cantera de Villagrán', 3470, 'HID', 142, 'MX', 19.84083000, -99.30917000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20275197'),
(68796, 'Cantinela', 3470, 'HID', 142, 'MX', 20.45778000, -99.21028000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256295'),
(68797, 'Cantioc', 3451, 'CHP', 142, 'MX', 17.26222000, -92.40694000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211129'),
(68798, 'Cantuna', 3462, 'ZAC', 142, 'MX', 23.62460000, -103.34630000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211129'),
(68799, 'Cantón Rancho Nuevo', 3451, 'CHP', 142, 'MX', 15.06742000, -92.52565000, '2019-10-05 23:08:31', '2020-05-01 17:22:59', 1, 'Q20211129'),
(68800, 'Cantón Villaflor', 3451, 'CHP', 142, 'MX', 15.11424000, -92.35261000, '2019-10-05 23:08:31', '2020-05-01 17:22:59', 1, 'Q20211201'),
(68801, 'Cantón las Delicias', 3451, 'CHP', 142, 'MX', 15.10245000, -92.50002000, '2019-10-05 23:08:31', '2020-05-01 17:22:59', 1, 'Q20211201'),
(68802, 'Caobanal 1ra. Sección (Mezcalapa)', 3454, 'TAB', 142, 'MX', 17.64858000, -93.39464000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20227664'),
(68803, 'Caobas', 3467, 'ROO', 142, 'MX', 18.44497000, -89.10498000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20227664'),
(68804, 'Capacho', 3474, 'MIC', 142, 'MX', 19.96457000, -101.23117000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20227664'),
(68805, 'Caparroso', 3454, 'TAB', 142, 'MX', 18.34500000, -92.80361000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20227665'),
(68806, 'Capitán Felipe Castellanos Díaz (San Pedro)', 3454, 'TAB', 142, 'MX', 17.77306000, -91.14750000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20261265'),
(68807, 'Capoluca', 3464, 'VER', 142, 'MX', 18.80639000, -97.02722000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256434'),
(68808, 'Capula', 3450, 'MEX', 142, 'MX', 18.87018000, -99.96075000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256434'),
(68809, 'Capula', 3458, 'TLA', 142, 'MX', 19.53642000, -98.04219000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256434'),
(68810, 'Capula', 3474, 'MIC', 142, 'MX', 19.70000000, -101.80000000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20236109'),
(68811, 'Capulhuac de Mirafuentes', 3450, 'MEX', 142, 'MX', 19.19354000, -99.46585000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3308583'),
(68812, 'Capulálpam de Méndez', 3448, 'OAX', 142, 'MX', 17.30972000, -96.44656000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q590948'),
(68813, 'Capulín de Bustos', 3469, 'GUA', 142, 'MX', 20.88242000, -101.30680000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q590948'),
(68814, 'Capácuaro', 3474, 'MIC', 142, 'MX', 19.54741000, -102.05184000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q20275220'),
(68815, 'Carapán', 3450, 'MEX', 142, 'MX', 19.86003000, -102.03644000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20275220'),
(68816, 'Carbo', 3468, 'SON', 142, 'MX', 29.68306000, -110.95619000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275220'),
(68817, 'Carbonera', 3455, 'QUE', 142, 'MX', 20.80518000, -100.17117000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275220'),
(68818, 'Carboneras', 3463, 'TAM', 142, 'MX', 24.62766000, -97.71755000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275220'),
(68819, 'Carboneras', 3470, 'HID', 142, 'MX', 20.09000000, -98.70528000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20241366'),
(68820, 'Carboneras', 3456, 'AGU', 142, 'MX', 22.18311000, -102.24601000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20241366'),
(68821, 'Cardonal', 3470, 'HID', 142, 'MX', 20.05689000, -99.23025000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q56559927'),
(68822, 'Carichí', 3447, 'CHH', 142, 'MX', 27.74601000, -107.07795000, '2019-10-05 23:08:31', '2020-05-01 17:22:59', 1, 'Q15043366'),
(68823, 'Caristay', 3464, 'VER', 142, 'MX', 20.64538000, -97.30593000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q15043366'),
(68824, 'Carlos A. Carrillo', 3464, 'VER', 142, 'MX', 18.37477000, -95.75444000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256568'),
(68825, 'Carlos A. Madrazo', 3467, 'ROO', 142, 'MX', 18.50172000, -88.52508000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256568'),
(68826, 'Carmen', 3452, 'NLE', 142, 'MX', 25.93650000, -100.36396000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256568'),
(68827, 'Carmen', 3475, 'CAM', 142, 'MX', 18.63000000, -91.83000000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q2720137'),
(68828, 'Carmen Serdán', 3476, 'PUE', 142, 'MX', 19.03639000, -97.81194000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q20256663'),
(68829, 'Carmen Yalchuch', 3451, 'CHP', 142, 'MX', 16.64482000, -92.36245000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256663'),
(68830, 'Carmen Zacatal', 3451, 'CHP', 142, 'MX', 17.08083000, -92.80250000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211267'),
(68831, 'Carona', 3474, 'MIC', 142, 'MX', 19.52667000, -102.45074000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211267'),
(68832, 'Carpinteros', 3470, 'HID', 142, 'MX', 20.58606000, -98.53964000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211267'),
(68833, 'Carranco', 3461, 'SLP', 142, 'MX', 21.82002000, -101.09699000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20258180'),
(68834, 'Carretas', 3447, 'CHH', 142, 'MX', 28.24920000, -106.51062000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5095723'),
(68835, 'Carricitos', 3449, 'SIN', 142, 'MX', 25.97595000, -108.93130000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5095723'),
(68836, 'Carrillo', 3462, 'ZAC', 142, 'MX', 23.20400000, -102.98676000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5095723'),
(68837, 'Carrillo Puerto', 3451, 'CHP', 142, 'MX', 15.01120000, -92.19552000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5095723'),
(68838, 'Carrillo Puerto', 3475, 'CAM', 142, 'MX', 19.09400000, -90.52279000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5095723'),
(68839, 'Carrillo Puerto', 3463, 'TAM', 142, 'MX', 22.43139000, -97.96417000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256732'),
(68840, 'Carrillo Puerto', 3464, 'VER', 142, 'MX', 18.81314000, -96.58230000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3894268'),
(68841, 'Carrizal', 3464, 'VER', 142, 'MX', 20.59552000, -97.25676000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3894268'),
(68842, 'Carrizal Grande', 3469, 'GUA', 142, 'MX', 20.70634000, -101.30600000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3894268'),
(68843, 'Carrizalillo', 3459, 'GRO', 142, 'MX', 17.85077000, -99.71263000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3894268'),
(68844, 'Cartagena [Fraccionamiento]', 3456, 'AGU', 142, 'MX', 21.95639000, -102.27639000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20256790'),
(68845, 'Carácuaro', 3474, 'MIC', 142, 'MX', 18.98513000, -101.04164000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q5784494'),
(68846, 'Casa Blanca', 3476, 'PUE', 142, 'MX', 19.04222000, -98.11889000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20229170'),
(68847, 'Casa Blanca', 3449, 'SIN', 142, 'MX', 25.43036000, -108.42308000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20229170'),
(68848, 'Casa Blanca', 3455, 'QUE', 142, 'MX', 20.38658000, -100.02009000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20229170'),
(68849, 'Casa Nueva', 3450, 'MEX', 142, 'MX', 19.83333000, -99.20944000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20237329'),
(68850, 'Casa de Cerros', 3462, 'ZAC', 142, 'MX', 22.88499000, -102.52124000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20237329'),
(68851, 'Casahuatlán', 3465, 'MOR', 142, 'MX', 18.57271000, -99.39051000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q20237329'),
(68852, 'Casas', 3463, 'TAM', 142, 'MX', 23.72686000, -98.73662000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20224486'),
(68853, 'Casas Grandes', 3447, 'CHH', 142, 'MX', 30.38269000, -107.95628000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3867357'),
(68854, 'Casas Viejas', 3450, 'MEX', 142, 'MX', 19.15167000, -100.10778000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20237335'),
(68855, 'Caserío de Cortés', 3450, 'MEX', 142, 'MX', 19.19861000, -98.95444000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20275318'),
(68856, 'Cash', 3451, 'CHP', 142, 'MX', 16.26426000, -92.09775000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275318'),
(68857, 'Casitas', 3464, 'VER', 142, 'MX', 20.25532000, -96.80114000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275318'),
(68858, 'Castamay', 3475, 'CAM', 142, 'MX', 19.83782000, -90.43181000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275318'),
(68859, 'Castaños', 3471, 'COA', 142, 'MX', 26.78932000, -101.43211000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20275318'),
(68860, 'Castillo de Teayo', 3464, 'VER', 142, 'MX', 20.74927000, -97.63030000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5049941'),
(68861, 'Catarino Rodríguez', 3452, 'NLE', 142, 'MX', 24.84542000, -100.32046000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q5049941'),
(68862, 'Catazajá', 3451, 'CHP', 142, 'MX', 17.73333000, -92.01667000, '2019-10-05 23:08:31', '2020-05-01 17:22:59', 1, 'Q1952920'),
(68863, 'Catemaco', 3464, 'VER', 142, 'MX', 18.42131000, -95.11398000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20221358'),
(68864, 'Catishtic', 3451, 'CHP', 142, 'MX', 16.78028000, -92.73778000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211293'),
(68865, 'Caucel', 3466, 'YUC', 142, 'MX', 21.01539000, -89.70347000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275325'),
(68866, 'Caurio de Guadalupe', 3474, 'MIC', 142, 'MX', 19.92455000, -101.86067000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275325'),
(68867, 'Caxapa', 3464, 'VER', 142, 'MX', 18.43103000, -96.77456000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275325'),
(68868, 'Caxhuacán', 3476, 'PUE', 142, 'MX', 20.06359000, -97.60688000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q5055255'),
(68869, 'Caxitepec', 3459, 'GRO', 142, 'MX', 17.29449000, -98.96741000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275330'),
(68870, 'Caxuxi', 3470, 'HID', 142, 'MX', 20.30522000, -98.99067000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5759022'),
(68871, 'Cayaco', 3459, 'GRO', 142, 'MX', 17.04743000, -100.26859000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q5759022'),
(68872, 'Cazones de Herrera', 3464, 'VER', 142, 'MX', 20.70423000, -97.30994000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20221367'),
(68873, 'Cañada', 3470, 'HID', 142, 'MX', 19.99798000, -99.23947000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q56605930'),
(68874, 'Cañada de Bustos', 3469, 'GUA', 142, 'MX', 20.90436000, -101.31260000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q56605930'),
(68875, 'Cañada de Caracheo', 3469, 'GUA', 142, 'MX', 20.37552000, -100.94549000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20205614'),
(68876, 'Cañada de Cisneros', 3450, 'MEX', 142, 'MX', 19.69534000, -99.33570000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20205614'),
(68877, 'Cañada de Guadarrama', 3450, 'MEX', 142, 'MX', 19.31272000, -99.78972000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20205614'),
(68878, 'Cañada de Madero', 3470, 'HID', 142, 'MX', 19.96528000, -99.39389000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20205614'),
(68879, 'Cañada de Negros', 3469, 'GUA', 142, 'MX', 20.92636000, -101.92115000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20205614'),
(68880, 'Cañada de Ramírez', 3474, 'MIC', 142, 'MX', 20.29637000, -101.96360000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q20205614'),
(68881, 'Cañada del Tabaco', 3477, 'NAY', 142, 'MX', 21.70972000, -105.35417000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q8343236'),
(68882, 'Cañas', 3462, 'ZAC', 142, 'MX', 23.74366000, -103.24400000, '2019-10-05 23:08:31', '2020-05-01 17:23:03', 1, 'Q20220453'),
(68883, 'Cañitas de Felipe Pescador', 3462, 'ZAC', 142, 'MX', 23.60371000, -102.72704000, '2019-10-05 23:08:31', '2020-05-01 17:23:03', 1, 'Q20220456'),
(68884, 'Cebadilla 1ra. Sección', 3451, 'CHP', 142, 'MX', 14.86381000, -92.27969000, '2019-10-05 23:08:31', '2020-05-01 17:22:59', 1, 'Q20211296'),
(68885, 'Ceballos', 3453, 'DUR', 142, 'MX', 26.52580000, -104.12950000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20218036'),
(68886, 'Cecilio Terán', 3464, 'VER', 142, 'MX', 18.81480000, -97.22526000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20218036'),
(68887, 'Cedro de la Manzana', 3450, 'MEX', 142, 'MX', 19.69213000, -100.11656000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20218036'),
(68888, 'Cedros', 3462, 'ZAC', 142, 'MX', 24.67919000, -101.77387000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20218036'),
(68889, 'Ceiba 1ra. Sección (Jahuactal)', 3454, 'TAB', 142, 'MX', 18.06222000, -93.11306000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20264364'),
(68890, 'Ceja de Bravo', 3455, 'QUE', 142, 'MX', 20.37856000, -100.39423000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20264364'),
(68891, 'Celaya', 3469, 'GUA', 142, 'MX', 20.52353000, -100.81570000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q580649'),
(68892, 'Celayita', 3450, 'MEX', 142, 'MX', 20.24113000, -99.86142000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q580649'),
(68893, 'Celestún', 3466, 'YUC', 142, 'MX', 20.85973000, -90.39902000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q1917547'),
(68894, 'Cementeras del Pital', 3464, 'VER', 142, 'MX', 20.17694000, -96.89583000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20268976'),
(68895, 'Cenobio Aguilar (La Trinidad)', 3451, 'CHP', 142, 'MX', 17.40472000, -92.31944000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211307'),
(68896, 'Cenobio Moreno', 3474, 'MIC', 142, 'MX', 19.09629000, -102.50415000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211307'),
(68897, 'Cenotillo', 3466, 'YUC', 142, 'MX', 20.96621000, -88.60438000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q8343446'),
(68898, 'Centro', 3454, 'TAB', 142, 'MX', 17.98633000, -92.88674000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q2994111'),
(68899, 'Centro Familiar la Soledad', 3469, 'GUA', 142, 'MX', 21.13500000, -101.74972000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20205738'),
(68900, 'Centro de Arriba', 3456, 'AGU', 142, 'MX', 21.73123000, -102.49888000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20205738'),
(68901, 'Centro de Readaptación Social', 3464, 'VER', 142, 'MX', 19.62500000, -97.22028000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20264544'),
(68902, 'Centro de Readaptación Social Nuevo', 3468, 'SON', 142, 'MX', 31.18444000, -110.96778000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20257349'),
(68903, 'Cepeda', 3466, 'YUC', 142, 'MX', 20.50600000, -90.10979000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257378'),
(68904, 'Cerano', 3469, 'GUA', 142, 'MX', 20.10877000, -101.38710000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q978419'),
(68905, 'Cereso 14 (El Amate)', 3451, 'CHP', 142, 'MX', 16.59472000, -93.80278000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20211321'),
(68906, 'Cereso del Hongo', 3457, 'BCN', 142, 'MX', 32.48417000, -116.25056000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20247259'),
(68907, 'Cerocahui', 3447, 'CHH', 142, 'MX', 27.29906000, -108.05500000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20247259'),
(68908, 'Cerralvo', 3452, 'NLE', 142, 'MX', 26.09766000, -99.65340000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20247259'),
(68909, 'Cerrillos (Campo 35)', 3449, 'SIN', 142, 'MX', 25.87806000, -108.90139000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275367'),
(68910, 'Cerrito de Gasca', 3469, 'GUA', 142, 'MX', 20.61829000, -101.06200000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275367'),
(68911, 'Cerrito de Jaral', 3461, 'SLP', 142, 'MX', 22.22361000, -101.06250000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257423'),
(68912, 'Cerrito de Zavala', 3461, 'SLP', 142, 'MX', 22.53690000, -100.97683000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257423'),
(68913, 'Cerrito de la Cruz', 3462, 'ZAC', 142, 'MX', 22.65781000, -102.27263000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257423'),
(68914, 'Cerritos', 3464, 'VER', 142, 'MX', 19.13329000, -96.60931000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257423'),
(68915, 'Cerritos', 3469, 'GUA', 142, 'MX', 20.88552000, -100.59880000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257423'),
(68916, 'Cerritos', 3461, 'SLP', 142, 'MX', 22.42835000, -100.28474000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275386'),
(68917, 'Cerritos', 3470, 'HID', 142, 'MX', 20.35306000, -99.01222000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20241385'),
(68918, 'Cerritos de Cárdenas', 3465, 'MOR', 142, 'MX', 19.96942000, -100.02254000, '2019-10-05 23:08:31', '2020-05-01 17:23:01', 1, 'Q20241385'),
(68919, 'Cerritos del Pilar', 3450, 'MEX', 142, 'MX', 19.44508000, -100.01909000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20241385'),
(68920, 'Cerro Agudo', 3449, 'SIN', 142, 'MX', 25.59796000, -107.96310000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20241385'),
(68921, 'Cerro Armadillo Grande', 3448, 'OAX', 142, 'MX', 17.86611000, -96.30861000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20275392'),
(68922, 'Cerro Azul', 3464, 'VER', 142, 'MX', 21.19200000, -97.74088000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q27772333'),
(68923, 'Cerro Colorado', 3469, 'GUA', 142, 'MX', 20.40111000, -101.32451000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q27772333'),
(68924, 'Cerro Colorado de Ocampo', 3474, 'MIC', 142, 'MX', 19.31712000, -100.46538000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q27772333'),
(68925, 'Cerro Gordo', 3464, 'VER', 142, 'MX', 19.56951000, -96.64835000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257875'),
(68926, 'Cerro Gordo', 3455, 'QUE', 142, 'MX', 20.37329000, -99.91385000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257875'),
(68927, 'Cerro Gordo', 3461, 'SLP', 142, 'MX', 21.97448000, -100.80661000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257885'),
(68928, 'Cerro Gordo', 3469, 'GUA', 142, 'MX', 20.59294000, -101.12684000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257879'),
(68929, 'Cerro Grande', 3464, 'VER', 142, 'MX', 20.23861000, -97.68194000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20221539'),
(68930, 'Cerro Guzmán', 3464, 'VER', 142, 'MX', 19.23543000, -96.37856000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20221539'),
(68931, 'Cerro La Calera', 3450, 'MEX', 142, 'MX', 19.18333000, -99.81667000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257912'),
(68932, 'Cerro Quemado', 3448, 'OAX', 142, 'MX', 18.15417000, -96.57500000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257997'),
(68933, 'Cerro de Ortega', 3472, 'COL', 142, 'MX', 18.75080000, -103.72155000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257997'),
(68934, 'Cerro de Piedra', 3459, 'GRO', 142, 'MX', 16.77646000, -99.63037000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20205791'),
(68935, 'Cerro de San Francisco', 3450, 'MEX', 142, 'MX', 19.37038000, -99.35162000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20237367'),
(68936, 'Cerro de San Pedro', 3461, 'SLP', 142, 'MX', 22.21780000, -100.79961000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3311798'),
(68937, 'Cerro de la Cruz', 3455, 'QUE', 142, 'MX', 20.81474000, -100.50109000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3311798'),
(68938, 'Cerro del Aire', 3448, 'OAX', 142, 'MX', 16.11012000, -97.20438000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q3311798'),
(68939, 'Cerro del Murciélago', 3450, 'MEX', 142, 'MX', 19.28194000, -99.75472000, '2019-10-05 23:08:31', '2020-05-01 17:23:00', 1, 'Q20275429'),
(68940, 'Cerro las Iguanas', 3464, 'VER', 142, 'MX', 18.41083000, -95.19972000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257918'),
(68941, 'Ceuta', 3449, 'SIN', 142, 'MX', 23.90042000, -106.92847000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257918'),
(68942, 'Chablekal', 3466, 'YUC', 142, 'MX', 21.09647000, -89.57774000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257918'),
(68943, 'Chablé', 3454, 'TAB', 142, 'MX', 17.85895000, -91.78130000, '2019-10-05 23:08:31', '2020-05-01 17:23:02', 1, 'Q20257918'),
(68944, 'Chacalapa', 3459, 'GRO', 142, 'MX', 16.80682000, -98.45788000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20257918'),
(68945, 'Chacalapa', 3464, 'VER', 142, 'MX', 18.07860000, -94.70280000, '2019-10-05 23:08:31', '2019-10-05 23:08:31', 1, 'Q20268995'),
(68946, 'Chacaljocóm', 3451, 'CHP', 142, 'MX', 16.29919000, -92.18345000, '2019-10-05 23:08:32', '2020-05-01 17:22:59', 1, 'Q20268995'),
(68947, 'Chacaltianguis', 3464, 'VER', 142, 'MX', 18.30528000, -95.84167000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q3847197'),
(68948, 'Chachahuantla', 3470, 'HID', 142, 'MX', 20.20182000, -98.13261000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q3847197'),
(68949, 'Chacoma', 3451, 'CHP', 142, 'MX', 16.87611000, -92.49139000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20211369'),
(68950, 'Chacsinkin', 3466, 'YUC', 142, 'MX', 20.17222000, -89.01654000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q5764020'),
(68952, 'Chahuite', 3448, 'OAX', 142, 'MX', 16.30000000, -94.18333000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20229969'),
(68953, 'Chahuites', 3448, 'OAX', 142, 'MX', 16.28842000, -94.19478000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20229969'),
(68954, 'Chalahuiyapa', 3470, 'HID', 142, 'MX', 21.15088000, -98.36429000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20241442'),
(68955, 'Chalam', 3451, 'CHP', 142, 'MX', 16.88771000, -92.55009000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20241442'),
(68956, 'Chalcatzingo', 3465, 'MOR', 142, 'MX', 18.68951000, -98.77434000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20241442'),
(68957, 'Chalchihuapan', 3450, 'MEX', 142, 'MX', 18.97694000, -99.57472000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20237404'),
(68958, 'Chalchihuapan', 3476, 'PUE', 142, 'MX', 18.97183000, -98.33698000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20237404'),
(68959, 'Chalchihuites', 3462, 'ZAC', 142, 'MX', 23.47498000, -103.88293000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q2652043'),
(68960, 'Chalchihuitán', 3451, 'CHP', 142, 'MX', 16.96667000, -92.65000000, '2019-10-05 23:08:32', '2020-05-01 17:22:59', 1, 'Q1785681'),
(68961, 'Chalchocoyo', 3461, 'SLP', 142, 'MX', 21.31444000, -98.79194000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20275488'),
(68962, 'Chalco', 3450, 'MEX', 142, 'MX', 19.26174000, -98.89775000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q1962104'),
(68963, 'Chalma', 3450, 'MEX', 142, 'MX', 18.93048000, -99.43505000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q739817'),
(68964, 'Chalma', 3464, 'VER', 142, 'MX', 21.20799000, -98.37526000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q4936711'),
(68965, 'Chalmita', 3450, 'MEX', 142, 'MX', 18.93433000, -99.42325000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q4936711'),
(68966, 'Chametla', 3449, 'SIN', 142, 'MX', 22.87750000, -105.95750000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20275495'),
(68967, 'Chametla', 3460, 'BCS', 142, 'MX', 24.09926000, -110.37395000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20275495'),
(68968, 'Champotón', 3475, 'CAM', 142, 'MX', 19.35042000, -90.70991000, '2019-10-05 23:08:32', '2020-05-01 17:22:59', 1, 'Q2543920'),
(68969, 'Chamula', 3451, 'CHP', 142, 'MX', 16.78416000, -92.68959000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q1061542'),
(68970, 'Chamulapita', 3451, 'CHP', 142, 'MX', 15.06892000, -92.36942000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q1061542'),
(68971, 'Chamácuaro', 3469, 'GUA', 142, 'MX', 20.10444000, -100.82756000, '2019-10-05 23:08:32', '2020-05-01 17:23:00', 1, 'Q1061542'),
(68972, 'Chan Cenote', 3466, 'YUC', 142, 'MX', 20.99142000, -87.78564000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q1061542'),
(68973, 'Chanal', 3451, 'CHP', 142, 'MX', 16.65750000, -92.25750000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20211392'),
(68974, 'Changata', 3459, 'GRO', 142, 'MX', 18.27306000, -100.58528000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20205855'),
(68975, 'Chankom', 3466, 'YUC', 142, 'MX', 20.49656000, -88.56928000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q2577482'),
(68976, 'Chapa', 3474, 'MIC', 142, 'MX', 19.37976000, -101.66556000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q2577482'),
(68977, 'Chapa de Mota', 3450, 'MEX', 142, 'MX', 19.82420000, -99.55252000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q2990953'),
(68978, 'Chapab', 3466, 'YUC', 142, 'MX', 20.48633000, -89.46779000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q2577517'),
(68979, 'Chapalilla', 3477, 'NAY', 142, 'MX', 21.18852000, -104.63783000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q2577517'),
(68980, 'Chapallal Grande', 3451, 'CHP', 142, 'MX', 17.29631000, -92.94519000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q2577517'),
(68981, 'Chapantongo', 3470, 'HID', 142, 'MX', 20.28635000, -99.41319000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q3844596'),
(68982, 'Chaparaco', 3450, 'MEX', 142, 'MX', 19.96061000, -102.26092000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q3844596'),
(68983, 'Chaparrosa', 3462, 'ZAC', 142, 'MX', 23.08339000, -102.27728000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q3844596'),
(68984, 'Chapopote Chico', 3464, 'VER', 142, 'MX', 21.13704000, -98.24780000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q3844596'),
(68985, 'Chapopote Núñez', 3464, 'VER', 142, 'MX', 20.93167000, -97.68274000, '2019-10-05 23:08:32', '2020-05-01 17:23:02', 1, 'Q3844596'),
(68986, 'Chapulco', 3476, 'PUE', 142, 'MX', 18.62586000, -97.40474000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20261503'),
(68987, 'Chapulhuacanito', 3470, 'HID', 142, 'MX', 21.20911000, -98.67016000, '2019-10-05 23:08:32', '2019-10-05 23:08:32', 1, 'Q20261503');

