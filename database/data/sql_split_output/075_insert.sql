INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(37688, '<PERSON><PERSON><PERSON> Lerda', 5113, 'Z', 207, 'ES', 42.56572000, -1.16944000, '2019-10-05 22:46:16', '2022-08-29 11:42:55', 1, 'Q617326'),
(37689, 'Unzué', 1204, 'NA', 207, 'ES', 42.65252000, -1.62598000, '2019-10-05 22:46:16', '2022-08-29 12:06:07', 1, 'Q1647468'),
(37690, 'Urda', 1205, 'TO', 207, 'ES', 39.41179000, -3.71493000, '2019-10-05 22:46:17', '2022-08-29 11:08:30', 1, 'Q579668'),
(37691, '<PERSON>rdiales del Páramo', 1200, 'LE', 207, 'ES', 42.37034000, -5.77297000, '2019-10-05 22:46:17', '2020-05-01 17:23:17', 1, 'Q579668'),
(37693, 'Urnieta', 1191, 'SS', 207, 'ES', 43.24727000, -1.99084000, '2019-10-05 22:46:17', '2022-08-28 18:36:50', 1, 'Q1633844'),
(37694, 'Urones de Castroponce', 1183, 'VA', 207, 'ES', 42.09911000, -5.28222000, '2019-10-05 22:46:17', '2022-08-29 11:48:45', 1, 'Q24016872'),
(37695, 'Urrea de Jalón', 5113, 'Z', 207, 'ES', 41.66715000, -1.23420000, '2019-10-05 22:46:17', '2022-08-29 11:42:55', 1, 'Q23991364'),
(37696, 'Urriés', 5113, 'Z', 207, 'ES', 42.51943000, -1.13022000, '2019-10-05 22:46:17', '2022-08-29 11:42:55', 1, 'Q24019807'),
(37697, 'Urrácal', 5095, 'AL', 207, 'ES', 37.39740000, -2.36485000, '2019-10-05 22:46:17', '2022-08-28 18:41:41', 1, 'Q24019807'),
(37698, 'Urueña', 1183, 'VA', 207, 'ES', 41.72738000, -5.20304000, '2019-10-05 22:46:17', '2022-08-29 11:48:45', 1, 'Q24019807'),
(37699, 'Urueñas', 1192, 'SG', 207, 'ES', 41.35589000, -3.77391000, '2019-10-05 22:46:17', '2022-08-29 11:50:43', 1, 'Q24019807'),
(37700, 'Uruñuela', 1171, 'LO', 207, 'ES', 42.44310000, -2.70749000, '2019-10-05 22:46:17', '2022-08-29 12:05:09', 1, 'Q24019807'),
(37701, 'Urzainqui', 1204, 'NA', 207, 'ES', 42.83029000, -0.94617000, '2019-10-05 22:46:17', '2022-08-29 12:06:07', 1, 'Q1648019'),
(37702, 'Urús', 5103, 'GI', 207, 'ES', 42.35131000, 1.85343000, '2019-10-05 22:46:17', '2022-08-29 10:53:17', 1, 'Q13761'),
(37703, 'Usagre', 5092, 'BA', 207, 'ES', 38.35000000, -6.16667000, '2019-10-05 22:46:17', '2022-08-28 18:09:23', 1, 'Q784442'),
(37704, 'Used', 5113, 'Z', 207, 'ES', 41.05554000, -1.55954000, '2019-10-05 22:46:17', '2022-08-29 11:42:55', 1, 'Q24019806'),
(37705, 'Usera', 1158, 'M', 207, 'ES', 40.38866000, -3.70035000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q953368'),
(37706, 'Usurbil', 1191, 'SS', 207, 'ES', 43.27164000, -2.04912000, '2019-10-05 22:46:17', '2022-08-28 18:36:50', 1, 'Q953368'),
(37707, 'Utande', 5107, 'GU', 207, 'ES', 40.84832000, -2.92770000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1656826'),
(37708, 'Utebo', 5113, 'Z', 207, 'ES', 41.70826000, -0.99916000, '2019-10-05 22:46:17', '2022-08-29 11:42:55', 1, 'Q220073'),
(37709, 'Uterga', 1204, 'NA', 207, 'ES', 42.70981000, -1.76004000, '2019-10-05 22:46:17', '2022-08-29 12:06:07', 1, 'Q984879'),
(37710, 'Utiel', 1175, 'V', 207, 'ES', 39.56667000, -1.20000000, '2019-10-05 22:46:17', '2022-08-29 12:05:40', 1, 'Q115572'),
(37711, 'Utrera', 1193, 'SE', 207, 'ES', 37.18516000, -5.78093000, '2019-10-05 22:46:17', '2022-08-28 19:08:49', 1, 'Q753825'),
(37712, 'Utrillas', 5111, 'TE', 207, 'ES', 40.81267000, -0.84545000, '2019-10-05 22:46:17', '2022-08-29 11:29:44', 1, 'Q1768489'),
(37713, 'Uña', 5106, 'CU', 207, 'ES', 40.22410000, -1.97788000, '2019-10-05 22:46:17', '2022-08-29 11:05:01', 1, 'Q769315'),
(37714, 'Uña de Quintana', 1161, 'ZA', 207, 'ES', 42.08662000, -6.14475000, '2019-10-05 22:46:17', '2022-08-29 11:48:04', 1, 'Q1769093'),
(37715, 'Vadillo', 1208, 'SO', 207, 'ES', 41.79111000, -3.00845000, '2019-10-05 22:46:17', '2022-08-29 11:51:23', 1, 'Q302875'),
(37716, 'Vadillo de la Guareña', 1161, 'ZA', 207, 'ES', 41.28242000, -5.35300000, '2019-10-05 22:46:17', '2022-08-29 11:48:04', 1, 'Q1766877'),
(37717, 'Vadillo de la Sierra', 1189, 'AV', 207, 'ES', 40.60634000, -5.12506000, '2019-10-05 22:46:17', '2022-08-29 11:49:57', 1, 'Q1614699'),
(37718, 'Vadocondes', 1146, 'BU', 207, 'ES', 41.63925000, -3.57378000, '2019-10-05 22:46:17', '2022-08-29 11:24:21', 1, 'Q1630958'),
(37719, 'Val de San Lorenzo', 1200, 'LE', 207, 'ES', 42.41819000, -6.12391000, '2019-10-05 22:46:17', '2019-10-05 22:46:17', 1, 'Q928409'),
(37720, 'Val de San Martín', 5113, 'Z', 207, 'ES', 41.05787000, -1.44934000, '2019-10-05 22:46:17', '2022-08-29 11:42:55', 1, 'Q1639213'),
(37721, 'Valacloche', 5111, 'TE', 207, 'ES', 40.19092000, -1.09134000, '2019-10-05 22:46:17', '2022-08-29 11:29:44', 1, 'Q1639213'),
(37722, 'Valbona', 5111, 'TE', 207, 'ES', 40.22877000, -0.81079000, '2019-10-05 22:46:17', '2022-08-29 11:29:44', 1, 'Q1639213'),
(37723, 'Valbuena de Duero', 1183, 'VA', 207, 'ES', 41.64323000, -4.29271000, '2019-10-05 22:46:17', '2022-08-29 11:48:45', 1, 'Q749388'),
(37724, 'Valbuena de Pisuerga', 1157, 'P', 207, 'ES', 42.14584000, -4.24010000, '2019-10-05 22:46:17', '2022-08-29 11:45:45', 1, 'Q1815688'),
(37725, 'Valcabado', 1161, 'ZA', 207, 'ES', 41.54863000, -5.74916000, '2019-10-05 22:46:17', '2022-08-29 11:48:04', 1, 'Q1752423'),
(37726, 'Valdaracete', 1158, 'M', 207, 'ES', 40.20770000, -3.19101000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q1647098'),
(37727, 'Valdarachas', 5107, 'GU', 207, 'ES', 40.51710000, -3.12652000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q742725'),
(37728, 'Valdastillas', 1190, 'CC', 207, 'ES', 40.13293000, -5.87932000, '2019-10-05 22:46:17', '2022-08-28 18:12:22', 1, 'Q588290'),
(37729, 'Valdealgorfa', 5111, 'TE', 207, 'ES', 40.99066000, -0.03433000, '2019-10-05 22:46:17', '2022-08-29 11:29:44', 1, 'Q1651175'),
(37730, 'Valdeande', 1146, 'BU', 207, 'ES', 41.83302000, -3.52792000, '2019-10-05 22:46:17', '2022-08-29 11:24:21', 1, 'Q1621503'),
(37731, 'Valdearcos de la Vega', 1183, 'VA', 207, 'ES', 41.64260000, -4.04845000, '2019-10-05 22:46:17', '2022-08-29 11:48:45', 1, 'Q1767866'),
(37732, 'Valdearenas', 5107, 'GU', 207, 'ES', 40.80939000, -2.99218000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q675656'),
(37733, 'Valdeavellano', 5107, 'GU', 207, 'ES', 40.66560000, -2.96977000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1656890'),
(37734, 'Valdeavellano de Tera', 1208, 'SO', 207, 'ES', 41.94229000, -2.57634000, '2019-10-05 22:46:17', '2022-08-29 11:51:23', 1, 'Q833439'),
(37735, 'Valdeavero', 1158, 'M', 207, 'ES', 40.62980000, -3.33001000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q1647110'),
(37736, 'Valdeaveruelo', 5107, 'GU', 207, 'ES', 40.63473000, -3.31367000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1656917'),
(37737, 'Valdecaballeros', 5092, 'BA', 207, 'ES', 39.24289000, -5.19000000, '2019-10-05 22:46:17', '2022-08-28 18:09:23', 1, 'Q1614671'),
(37738, 'Valdecarros', 1147, 'SA', 207, 'ES', 40.77007000, -5.42219000, '2019-10-05 22:46:17', '2022-08-29 11:44:52', 1, 'Q1905277'),
(37739, 'Valdecasa', 1189, 'AV', 207, 'ES', 40.65931000, -5.01183000, '2019-10-05 22:46:17', '2022-08-29 11:49:57', 1, 'Q670740'),
(37740, 'Valdecañas de Tajo', 1190, 'CC', 207, 'ES', 39.75919000, -5.61951000, '2019-10-05 22:46:17', '2022-08-28 18:12:22', 1, 'Q1658788'),
(37741, 'Valdeconcha', 5107, 'GU', 207, 'ES', 40.45643000, -2.87663000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1656983'),
(37742, 'Valdecuenca', 5111, 'TE', 207, 'ES', 40.29776000, -1.40829000, '2019-10-05 22:46:17', '2022-08-29 11:29:44', 1, 'Q1767407'),
(37743, 'Valdefinjas', 1161, 'ZA', 207, 'ES', 41.45172000, -5.45232000, '2019-10-05 22:46:17', '2022-08-29 11:48:04', 1, 'Q1646343'),
(37744, 'Valdefresno', 1200, 'LE', 207, 'ES', 42.59492000, -5.49355000, '2019-10-05 22:46:17', '2019-10-05 22:46:17', 1, 'Q426206'),
(37745, 'Valdefuentes', 1190, 'CC', 207, 'ES', 39.27433000, -6.12183000, '2019-10-05 22:46:17', '2022-08-28 18:12:22', 1, 'Q426206'),
(37746, 'Valdefuentes de Sangusín', 1147, 'SA', 207, 'ES', 40.46578000, -5.83286000, '2019-10-05 22:46:17', '2022-08-29 11:44:52', 1, 'Q426206'),
(37747, 'Valdefuentes del Páramo', 1200, 'LE', 207, 'ES', 42.32370000, -5.83097000, '2019-10-05 22:46:17', '2020-05-01 17:23:17', 1, 'Q1635346'),
(37749, 'Valdegrudas', 5107, 'GU', 207, 'ES', 40.71125000, -3.01209000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1656777'),
(37750, 'Valdehijaderos', 1147, 'SA', 207, 'ES', 40.41916000, -5.84792000, '2019-10-05 22:46:17', '2022-08-29 11:44:52', 1, 'Q1905473'),
(37751, 'Valdehorna', 5113, 'Z', 207, 'ES', 41.07275000, -1.42376000, '2019-10-05 22:46:17', '2022-08-29 11:42:55', 1, 'Q740785'),
(37752, 'Valdehúncar', 1190, 'CC', 207, 'ES', 39.83659000, -5.52307000, '2019-10-05 22:46:17', '2022-08-28 18:12:22', 1, 'Q1642313'),
(37753, 'Valdelacalzada', 5092, 'BA', 207, 'ES', 38.88943000, -6.70029000, '2019-10-05 22:46:17', '2022-08-28 18:09:23', 1, 'Q1246417'),
(37754, 'Valdelacasa', 1147, 'SA', 207, 'ES', 40.50631000, -5.76354000, '2019-10-05 22:46:17', '2022-08-29 11:44:52', 1, 'Q1766697'),
(37755, 'Valdelacasa de Tajo', 1190, 'CC', 207, 'ES', 39.72588000, -5.28296000, '2019-10-05 22:46:17', '2022-08-28 18:12:22', 1, 'Q1656602'),
(37756, 'Valdelageve', 1147, 'SA', 207, 'ES', 40.36949000, -5.99078000, '2019-10-05 22:46:17', '2022-08-29 11:44:52', 1, 'Q1767876'),
(37757, 'Valdelagua del Cerro', 1208, 'SO', 207, 'ES', 41.88825000, -2.11538000, '2019-10-05 22:46:17', '2022-08-29 11:51:23', 1, 'Q1767876'),
(37758, 'Valdelarco', 5099, 'H', 207, 'ES', 37.94877000, -6.68202000, '2019-10-05 22:46:17', '2022-08-28 19:00:43', 1, 'Q530504'),
(37759, 'Valdelcubo', 5107, 'GU', 207, 'ES', 41.22637000, -2.67588000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1656836'),
(37760, 'Valdelinares', 5111, 'TE', 207, 'ES', 40.39114000, -0.60593000, '2019-10-05 22:46:17', '2022-08-29 11:29:44', 1, 'Q1650933'),
(37761, 'Valdelosa', 1147, 'SA', 207, 'ES', 41.17157000, -5.78390000, '2019-10-05 22:46:17', '2022-08-29 11:44:52', 1, 'Q1767822'),
(37762, 'Valdeltormo', 5111, 'TE', 207, 'ES', 40.98716000, 0.08342000, '2019-10-05 22:46:17', '2022-08-29 11:29:44', 1, 'Q1650770'),
(37763, 'Valdemadera', 1171, 'LO', 207, 'ES', 41.98408000, -2.07403000, '2019-10-05 22:46:17', '2022-08-29 12:05:09', 1, 'Q975139'),
(37764, 'Valdemaluque', 1208, 'SO', 207, 'ES', 41.67377000, -3.04643000, '2019-10-05 22:46:17', '2022-08-29 11:51:23', 1, 'Q836802'),
(37765, 'Valdemanco', 1158, 'M', 207, 'ES', 40.87039000, -3.65873000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q1915163'),
(37766, 'Valdemanco del Esteras', 5105, 'CR', 207, 'ES', 38.93858000, -4.82920000, '2019-10-05 22:46:17', '2022-08-29 11:03:25', 1, 'Q1751983'),
(37767, 'Valdemaqueda', 1158, 'M', 207, 'ES', 40.51201000, -4.29722000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q24012671'),
(37768, 'Valdemeca', 5106, 'CU', 207, 'ES', 40.22367000, -1.74358000, '2019-10-05 22:46:17', '2022-08-29 11:05:01', 1, 'Q1647156'),
(37769, 'Valdemierque', 1147, 'SA', 207, 'ES', 40.82176000, -5.58225000, '2019-10-05 22:46:17', '2022-08-29 11:44:52', 1, 'Q1904273'),
(37770, 'Valdemora', 1200, 'LE', 207, 'ES', 42.19541000, -5.42747000, '2019-10-05 22:46:17', '2019-10-05 22:46:17', 1, 'Q1904273'),
(37771, 'Valdemorales', 1190, 'CC', 207, 'ES', 39.20588000, -6.06622000, '2019-10-05 22:46:17', '2022-08-28 18:12:22', 1, 'Q1750947'),
(37772, 'Valdemorillo', 1158, 'M', 207, 'ES', 40.50064000, -4.06710000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q24012670'),
(37773, 'Valdemorillo de la Sierra', 5106, 'CU', 207, 'ES', 40.03333000, -1.78333000, '2019-10-05 22:46:17', '2022-08-29 11:05:01', 1, 'Q1649575'),
(37774, 'Valdemoro', 1158, 'M', 207, 'ES', 40.19081000, -3.67887000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q840373'),
(37775, 'Valdemoro-Sierra', 5106, 'CU', 207, 'ES', 40.10000000, -1.76667000, '2019-10-05 22:46:17', '2022-08-29 11:05:01', 1, 'Q1767502'),
(37776, 'Valdenebro', 1208, 'SO', 207, 'ES', 41.57167000, -2.96424000, '2019-10-05 22:46:17', '2022-08-29 11:51:23', 1, 'Q834561'),
(37777, 'Valdenebro de los Valles', 1183, 'VA', 207, 'ES', 41.85690000, -4.97005000, '2019-10-05 22:46:17', '2022-08-29 11:48:45', 1, 'Q1919495'),
(37778, 'Valdeobispo', 1190, 'CC', 207, 'ES', 40.08297000, -6.24757000, '2019-10-05 22:46:17', '2022-08-28 18:12:22', 1, 'Q1643024'),
(37779, 'Valdeolivas', 5106, 'CU', 207, 'ES', 40.50600000, -2.44532000, '2019-10-05 22:46:17', '2022-08-29 11:05:01', 1, 'Q1766388'),
(37780, 'Valdeolmillos', 1157, 'P', 207, 'ES', 42.04123000, -4.40015000, '2019-10-05 22:46:17', '2022-08-29 11:45:45', 1, 'Q1905926'),
(37781, 'Valdeolmos', 1158, 'M', 207, 'ES', 40.63708000, -3.45064000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q23991714'),
(37782, 'Valdepeñas', 5105, 'CR', 207, 'ES', 38.76211000, -3.38483000, '2019-10-05 22:46:17', '2022-08-29 11:03:25', 1, 'Q23991714'),
(37783, 'Valdepeñas de Jaén', 5100, 'J', 207, 'ES', 37.58903000, -3.81450000, '2019-10-05 22:46:17', '2022-08-28 19:04:30', 1, 'Q1641765'),
(37784, 'Valdepeñas de la Sierra', 5107, 'GU', 207, 'ES', 40.90562000, -3.38414000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1750921'),
(37785, 'Valdepiélago', 1200, 'LE', 207, 'ES', 42.86895000, -5.39763000, '2019-10-05 22:46:17', '2020-05-01 17:23:17', 1, 'Q594945'),
(37786, 'Valdepiélagos', 1158, 'M', 207, 'ES', 40.75842000, -3.46163000, '2019-10-05 22:46:17', '2022-08-29 12:04:40', 1, 'Q1980542'),
(37787, 'Valdepolo', 1200, 'LE', 207, 'ES', 42.57686000, -5.22513000, '2019-10-05 22:46:17', '2019-10-05 22:46:17', 1, 'Q1607194'),
(37788, 'Valdeprado', 1208, 'SO', 207, 'ES', 41.93768000, -2.10915000, '2019-10-05 22:46:17', '2022-08-29 11:51:23', 1, 'Q1607194'),
(37789, 'Valdeprados', 1192, 'SG', 207, 'ES', 40.81766000, -4.25734000, '2019-10-05 22:46:17', '2022-08-29 11:50:43', 1, 'Q1917009'),
(37790, 'Valderas', 1200, 'LE', 207, 'ES', 42.07830000, -5.44355000, '2019-10-05 22:46:17', '2019-10-05 22:46:17', 1, 'Q1917009'),
(37791, 'Valderrebollo', 5107, 'GU', 207, 'ES', 40.81035000, -2.72887000, '2019-10-05 22:46:17', '2022-08-29 11:06:46', 1, 'Q1657984'),
(37792, 'Valderrey', 1200, 'LE', 207, 'ES', 42.39408000, -6.02151000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q1635228'),
(37793, 'Valderrobres', 5111, 'TE', 207, 'ES', 40.87209000, 0.15431000, '2019-10-05 22:46:18', '2022-08-29 11:29:44', 1, 'Q1641285'),
(37794, 'Valderrodilla', 1208, 'SO', 207, 'ES', 41.56346000, -2.80777000, '2019-10-05 22:46:18', '2022-08-29 11:51:23', 1, 'Q834027'),
(37795, 'Valderrodrigo', 1147, 'SA', 207, 'ES', 41.06524000, -6.50878000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q1766757'),
(37797, 'Valderrueda', 1200, 'LE', 207, 'ES', 42.81558000, -4.94731000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q619138'),
(37798, 'Valderrábano', 1157, 'P', 207, 'ES', 42.60674000, -4.65584000, '2019-10-05 22:46:18', '2022-08-29 11:45:45', 1, 'Q1906496'),
(37799, 'Valdesamario', 1200, 'LE', 207, 'ES', 42.72094000, -5.95104000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q926811'),
(37800, 'Valdescorriel', 1161, 'ZA', 207, 'ES', 42.02233000, -5.50998000, '2019-10-05 22:46:18', '2022-08-29 11:48:04', 1, 'Q1766901'),
(37801, 'Valdesotos', 5107, 'GU', 207, 'ES', 40.95546000, -3.32542000, '2019-10-05 22:46:18', '2022-08-29 11:06:46', 1, 'Q383525'),
(37802, 'Valdestillas', 1183, 'VA', 207, 'ES', 41.47697000, -4.77116000, '2019-10-05 22:46:18', '2022-08-29 11:48:45', 1, 'Q1766688'),
(37803, 'Valdetorres', 5092, 'BA', 207, 'ES', 38.91542000, -6.06765000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1630933'),
(37804, 'Valdetorres de Jarama', 1158, 'M', 207, 'ES', 40.69366000, -3.51156000, '2019-10-05 22:46:18', '2022-08-29 12:04:40', 1, 'Q24012666'),
(37805, 'Valdevacas de Montejo', 1192, 'SG', 207, 'ES', 41.52068000, -3.63571000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1917000'),
(37806, 'Valdeverdeja', 1205, 'TO', 207, 'ES', 39.79635000, -5.24544000, '2019-10-05 22:46:18', '2022-08-29 11:08:30', 1, 'Q24017261'),
(37807, 'Valdevimbre', 1200, 'LE', 207, 'ES', 42.41959000, -5.61975000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q1607172'),
(37808, 'Valdezate', 1146, 'BU', 207, 'ES', 41.60231000, -3.93045000, '2019-10-05 22:46:18', '2022-08-29 11:24:21', 1, 'Q1643929'),
(37810, 'Valdilecha', 1158, 'M', 207, 'ES', 40.29530000, -3.30233000, '2019-10-05 22:46:18', '2022-08-29 12:04:40', 1, 'Q24012665'),
(37811, 'Valdorros', 1146, 'BU', 207, 'ES', 42.17232000, -3.70938000, '2019-10-05 22:46:18', '2022-08-29 11:24:21', 1, 'Q1643797'),
(37812, 'Valdoviño', 5089, 'C', 207, 'ES', 43.60000000, -8.13333000, '2019-10-05 22:46:18', '2022-08-28 13:37:17', 1, 'Q1643797'),
(37813, 'Valdunciel', 1147, 'SA', 207, 'ES', 41.08544000, -5.67217000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q1766596'),
(37814, 'Valdunquillo', 1183, 'VA', 207, 'ES', 42.04232000, -5.31339000, '2019-10-05 22:46:18', '2022-08-29 11:48:45', 1, 'Q1908545'),
(37816, 'Valencia', 1175, 'V', 207, 'ES', 39.46975000, -0.37739000, '2019-10-05 22:46:18', '2022-08-29 12:05:40', 1, 'Q8818'),
(37817, 'Valencia de Alcántara', 1190, 'CC', 207, 'ES', 39.41148000, -7.24435000, '2019-10-05 22:46:18', '2022-08-28 18:12:22', 1, 'Q1613421'),
(37818, 'Valencia de Don Juan', 1200, 'LE', 207, 'ES', 42.29374000, -5.51720000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q385017'),
(37819, 'Valencia de las Torres', 5092, 'BA', 207, 'ES', 38.40518000, -6.00403000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q920525'),
(37820, 'Valencia del Mombuey', 5092, 'BA', 207, 'ES', 38.24243000, -7.11965000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1658582'),
(37821, 'Valencia del Ventoso', 5092, 'BA', 207, 'ES', 38.26667000, -6.46667000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1630828'),
(37822, 'Valencina de la Concepción', 1193, 'SE', 207, 'ES', 37.41618000, -6.07422000, '2019-10-05 22:46:18', '2022-08-28 19:08:49', 1, 'Q1603015'),
(37823, 'Valenzuela', 5097, 'CO', 207, 'ES', 37.77560000, -4.22038000, '2019-10-05 22:46:18', '2022-08-28 18:49:38', 1, 'Q1603015'),
(37824, 'Valenzuela de Calatrava', 5105, 'CR', 207, 'ES', 38.85254000, -3.77210000, '2019-10-05 22:46:18', '2022-08-29 11:03:25', 1, 'Q1657961'),
(37825, 'Valero', 1147, 'SA', 207, 'ES', 40.53533000, -5.94307000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q1657961'),
(37826, 'Valfarta', 1177, 'HU', 207, 'ES', 41.55726000, -0.13304000, '2019-10-05 22:46:18', '2022-08-29 12:06:20', 1, 'Q984619'),
(37827, 'Valfermoso de Tajuña', 5107, 'GU', 207, 'ES', 40.61902000, -2.95407000, '2019-10-05 22:46:18', '2022-08-29 11:06:46', 1, 'Q1657990'),
(37828, 'Valga', 1167, 'PO', 207, 'ES', 42.69792000, -8.63959000, '2019-10-05 22:46:18', '2022-08-28 17:57:54', 1, 'Q1628179'),
(37829, 'Valgañón', 1171, 'LO', 207, 'ES', 42.31782000, -3.06725000, '2019-10-05 22:46:18', '2022-08-29 12:05:09', 1, 'Q507404'),
(37830, 'Valhermoso', 5107, 'GU', 207, 'ES', 40.78590000, -1.96121000, '2019-10-05 22:46:18', '2022-08-29 11:06:46', 1, 'Q507404'),
(37831, 'Valhermoso de la Fuente', 5106, 'CU', 207, 'ES', 39.56667000, -2.01667000, '2019-10-05 22:46:18', '2022-08-29 11:05:01', 1, 'Q603235'),
(37832, 'Valjunquera', 5111, 'TE', 207, 'ES', 40.95250000, 0.02575000, '2019-10-05 22:46:18', '2022-08-29 11:29:44', 1, 'Q1651425'),
(37833, 'Vall de Almonacid', 5110, 'CS', 207, 'ES', 39.90000000, -0.45000000, '2019-10-05 22:46:18', '2022-08-29 11:26:43', 1, 'Q780982'),
(37834, 'Vall de Ebo', 1175, 'V', 207, 'ES', 38.80561000, -0.15890000, '2019-10-05 22:46:18', '2022-08-29 12:05:40', 1, 'Q780982'),
(37835, 'Vallada', 1175, 'V', 207, 'ES', 38.89575000, -0.69104000, '2019-10-05 22:46:18', '2022-08-29 12:05:40', 1, 'Q648740'),
(37836, 'Valladolid', 1183, 'VA', 207, 'ES', 41.65518000, -4.72372000, '2019-10-05 22:46:18', '2022-08-29 11:48:45', 1, 'Q31925679'),
(37837, 'Vallanca', 1175, 'V', 207, 'ES', 40.06667000, -1.33333000, '2019-10-05 22:46:18', '2022-08-29 12:05:40', 1, 'Q934397'),
(37838, 'Vallarta de Bureba', 1146, 'BU', 207, 'ES', 42.58864000, -3.20457000, '2019-10-05 22:46:18', '2022-08-29 11:24:21', 1, 'Q1642697'),
(37840, 'Vallbona de les Monges', 5104, 'L', 207, 'ES', 41.52631000, 1.08872000, '2019-10-05 22:46:18', '2022-08-29 10:55:25', 1, 'Q1905685'),
(37842, 'Vallclara', 1203, 'T', 207, 'ES', 41.37958000, 0.98342000, '2019-10-05 22:46:18', '2022-08-29 10:57:33', 1, 'Q24024956'),
(37843, 'Valldemossa', 1174, 'PM', 207, 'ES', 39.71042000, 2.62230000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q832967'),
(37844, 'Valle de Cerrato', 1157, 'P', 207, 'ES', 41.88045000, -4.36243000, '2019-10-05 22:46:18', '2022-08-29 11:45:45', 1, 'Q1919153'),
(37845, 'Valle de Matamoros', 5092, 'BA', 207, 'ES', 38.37889000, -6.80371000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1647560'),
(37846, 'Valle de Santa Ana', 5092, 'BA', 207, 'ES', 38.36637000, -6.78860000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1643055'),
(37847, 'Valle de Tabladillo', 1192, 'SG', 207, 'ES', 41.36304000, -3.83966000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1917499'),
(37848, 'Valle de la Serena', 5092, 'BA', 207, 'ES', 38.71010000, -5.79847000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1637562'),
(37849, 'Vallecillo', 1200, 'LE', 207, 'ES', 42.35604000, -5.21088000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q1650257'),
(37850, 'Vallehermosa', 1185, 'GC', 207, 'ES', 28.17944000, -17.26664000, '2019-10-05 22:46:18', '2022-08-29 12:06:32', 1, 'Q635308'),
(37851, 'Vallejera de Riofrío', 1147, 'SA', 207, 'ES', 40.40910000, -5.71943000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q1766730'),
(37852, 'Vallelado', 1192, 'SG', 207, 'ES', 41.40436000, -4.42662000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1938432'),
(37853, 'Valleruela de Pedraza', 1192, 'SG', 207, 'ES', 41.17937000, -3.80717000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1916630'),
(37854, 'Valleruela de Sepúlveda', 1192, 'SG', 207, 'ES', 41.18794000, -3.77277000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1905711'),
(37855, 'Valles de Palenzuela', 1146, 'BU', 207, 'ES', 42.12019000, -4.07750000, '2019-10-05 22:46:18', '2022-08-29 11:24:21', 1, 'Q1642622'),
(37856, 'Vallesa de la Guareña', 1161, 'ZA', 207, 'ES', 41.13532000, -5.32611000, '2019-10-05 22:46:18', '2022-08-29 11:48:04', 1, 'Q1766957'),
(37857, 'Valleseco', 1185, 'GC', 207, 'ES', 28.04330000, -15.57623000, '2019-10-05 22:46:18', '2022-08-29 12:06:32', 1, 'Q1766957'),
(37858, 'Vallfogona de Balaguer', 5104, 'L', 207, 'ES', 41.75211000, 0.81385000, '2019-10-05 22:46:18', '2022-08-29 10:55:25', 1, 'Q23991842'),
(37859, 'Vallgorguina', 5102, 'B', 207, 'ES', 41.64822000, 2.50996000, '2019-10-05 22:46:18', '2022-08-29 10:50:01', 1, 'Q23991842'),
(37860, 'Vallibona', 5110, 'CS', 207, 'ES', 40.60300000, 0.04642000, '2019-10-05 22:46:18', '2022-08-29 11:26:43', 1, 'Q1767567'),
(37861, 'Vallirana', 5102, 'B', 207, 'ES', 41.38676000, 1.93205000, '2019-10-05 22:46:18', '2022-08-29 10:50:01', 1, 'Q1767567'),
(37862, 'Vallmoll', 1203, 'T', 207, 'ES', 41.24311000, 1.24900000, '2019-10-05 22:46:18', '2022-08-29 10:57:33', 1, 'Q576658'),
(37863, 'Valls', 1203, 'T', 207, 'ES', 41.28612000, 1.24993000, '2019-10-05 22:46:18', '2022-08-29 10:57:33', 1, 'Q576658'),
(37864, 'Valluércanes', 1146, 'BU', 207, 'ES', 42.57220000, -3.12113000, '2019-10-05 22:46:18', '2022-08-29 11:24:21', 1, 'Q1630713'),
(37866, 'Vallés', 1175, 'V', 207, 'ES', 38.98518000, -0.55696000, '2019-10-05 22:46:18', '2022-08-29 12:05:40', 1, 'Q1630713'),
(37867, 'Valmadrid', 5113, 'Z', 207, 'ES', 41.44351000, -0.88482000, '2019-10-05 22:46:18', '2022-08-29 11:42:55', 1, 'Q24019802'),
(37868, 'Valmala', 1146, 'BU', 207, 'ES', 42.30590000, -3.25456000, '2019-10-05 22:46:18', '2022-08-29 11:24:21', 1, 'Q630537'),
(37869, 'Valmojado', 1205, 'TO', 207, 'ES', 40.20444000, -4.09146000, '2019-10-05 22:46:18', '2022-08-29 11:08:30', 1, 'Q1630794'),
(37870, 'Valoria la Buena', 1183, 'VA', 207, 'ES', 41.79966000, -4.53055000, '2019-10-05 22:46:18', '2022-08-29 11:48:45', 1, 'Q1907435'),
(37871, 'Valpalmas', 5113, 'Z', 207, 'ES', 42.15821000, -0.85481000, '2019-10-05 22:46:18', '2022-08-29 11:42:55', 1, 'Q1650402'),
(37872, 'Valsalabroso', 1147, 'SA', 207, 'ES', 41.10962000, -6.50269000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q1768264'),
(37873, 'Valsalobre', 5106, 'CU', 207, 'ES', 40.61742000, -2.09297000, '2019-10-05 22:46:18', '2022-08-29 11:05:01', 1, 'Q1767495'),
(37874, 'Valseca', 1192, 'SG', 207, 'ES', 40.99933000, -4.17514000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1938467'),
(37875, 'Valsequillo de Gran Canaria', 1185, 'GC', 207, 'ES', 27.98562000, -15.49725000, '2019-10-05 22:46:18', '2022-08-29 12:06:32', 1, 'Q914195'),
(37876, 'Valtablado del Río', 5107, 'GU', 207, 'ES', 40.71420000, -2.40225000, '2019-10-05 22:46:18', '2022-08-29 11:06:46', 1, 'Q733886'),
(37877, 'Valtajeros', 1208, 'SO', 207, 'ES', 41.93808000, -2.22289000, '2019-10-05 22:46:18', '2022-08-29 11:51:23', 1, 'Q831099'),
(37878, 'Valtiendas', 1192, 'SG', 207, 'ES', 41.47851000, -3.91766000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1919269'),
(37879, 'Valtierra', 1204, 'NA', 207, 'ES', 42.19653000, -1.63459000, '2019-10-05 22:46:18', '2022-08-29 12:06:07', 1, 'Q1751469'),
(37880, 'Valtorres', 5113, 'Z', 207, 'ES', 41.29855000, -1.74080000, '2019-10-05 22:46:18', '2022-08-29 11:42:55', 1, 'Q24019800'),
(37881, 'Valverde', 5112, 'TF', 207, 'ES', 27.80628000, -17.91578000, '2019-10-05 22:46:18', '2022-08-29 11:31:13', 1, 'Q1853429'),
(37882, 'Valverde de Alcalá', 1158, 'M', 207, 'ES', 40.41667000, -3.29631000, '2019-10-05 22:46:18', '2022-08-29 12:04:40', 1, 'Q2003388'),
(37883, 'Valverde de Burguillos', 5092, 'BA', 207, 'ES', 38.32710000, -6.53648000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q2003388'),
(37884, 'Valverde de Campos', 1183, 'VA', 207, 'ES', 41.83510000, -5.03674000, '2019-10-05 22:46:18', '2022-08-29 11:48:45', 1, 'Q1919588'),
(37885, 'Valverde de Júcar', 5106, 'CU', 207, 'ES', 39.71866000, -2.22135000, '2019-10-05 22:46:18', '2022-08-29 11:05:01', 1, 'Q1767561'),
(37886, 'Valverde de Leganés', 5092, 'BA', 207, 'ES', 38.67059000, -6.98036000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1630843'),
(37887, 'Valverde de Llerena', 5092, 'BA', 207, 'ES', 38.21667000, -5.81667000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q1642776'),
(37888, 'Valverde de Mérida', 5092, 'BA', 207, 'ES', 38.91122000, -6.21999000, '2019-10-05 22:46:18', '2022-08-28 18:09:23', 1, 'Q377785'),
(37889, 'Valverde de Valdelacasa', 1147, 'SA', 207, 'ES', 40.48155000, -5.78145000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q1648209'),
(37890, 'Valverde de la Vera', 1190, 'CC', 207, 'ES', 40.12268000, -5.49541000, '2019-10-05 22:46:18', '2022-08-28 18:12:22', 1, 'Q1642209'),
(37891, 'Valverde de la Virgen', 1200, 'LE', 207, 'ES', 42.56823000, -5.68461000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q918087'),
(37892, 'Valverde de los Arroyos', 5107, 'GU', 207, 'ES', 41.12930000, -3.23333000, '2019-10-05 22:46:18', '2022-08-29 11:06:46', 1, 'Q594289'),
(37893, 'Valverde del Camino', 5099, 'H', 207, 'ES', 37.57511000, -6.75432000, '2019-10-05 22:46:18', '2022-08-28 19:00:43', 1, 'Q844038'),
(37894, 'Valverde del Fresno', 1190, 'CC', 207, 'ES', 40.22210000, -6.87848000, '2019-10-05 22:46:18', '2022-08-28 18:12:22', 1, 'Q1441219'),
(37895, 'Valverde del Majano', 1192, 'SG', 207, 'ES', 40.95689000, -4.23468000, '2019-10-05 22:46:18', '2022-08-29 11:50:43', 1, 'Q1905401'),
(37896, 'Valverde-Enrique', 1200, 'LE', 207, 'ES', 42.30459000, -5.29990000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q1640137'),
(37897, 'Valverdejo', 5106, 'CU', 207, 'ES', 39.61667000, -2.01667000, '2019-10-05 22:46:18', '2022-08-29 11:05:01', 1, 'Q1767463'),
(37898, 'Valverdón', 1147, 'SA', 207, 'ES', 41.04691000, -5.76955000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q24014702'),
(37899, 'Vara de Rey', 5106, 'CU', 207, 'ES', 39.42595000, -2.29404000, '2019-10-05 22:46:18', '2022-08-29 11:05:01', 1, 'Q24011468'),
(37900, 'Vecindario', 1185, 'GC', 207, 'ES', 27.84636000, -15.44455000, '2019-10-05 22:46:18', '2022-08-29 12:06:32', 1, 'Q9092770'),
(37901, 'Vecinos', 1147, 'SA', 207, 'ES', 40.77875000, -5.87815000, '2019-10-05 22:46:18', '2022-08-29 11:44:52', 1, 'Q9092770'),
(37902, 'Vedra', 5089, 'C', 207, 'ES', 42.77817000, -8.47636000, '2019-10-05 22:46:18', '2022-08-28 13:37:17', 1, 'Q1616092'),
(37903, 'Vega de Espinareda', 1200, 'LE', 207, 'ES', 42.72537000, -6.65439000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q1605960'),
(37904, 'Vega de Infanzones', 1200, 'LE', 207, 'ES', 42.48116000, -5.53295000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q1615360'),
(37905, 'Vega de Pas', 1170, 'S', 207, 'ES', 43.15692000, -3.78316000, '2019-10-05 22:46:18', '2019-10-05 22:46:18', 1, 'Q1602441'),
(37906, 'Vega de Ruiponce', 1183, 'VA', 207, 'ES', 42.18808000, -5.11477000, '2019-10-05 22:46:18', '2022-08-29 11:48:45', 1, 'Q1929334'),
(37907, 'Vega de San Mateo', 1185, 'GC', 207, 'ES', 28.00892000, -15.53330000, '2019-10-05 22:46:18', '2022-08-29 12:06:32', 1, 'Q1929334'),
(37908, 'Vega de Santa María', 1189, 'AV', 207, 'ES', 40.83618000, -4.64287000, '2019-10-05 22:46:18', '2022-08-29 11:49:57', 1, 'Q1630550'),
(37909, 'Vega de Tera', 1161, 'ZA', 207, 'ES', 41.99759000, -6.12500000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q1766897'),
(37910, 'Vega de Tirados', 1147, 'SA', 207, 'ES', 41.02543000, -5.88667000, '2019-10-05 22:46:19', '2022-08-29 11:44:52', 1, 'Q1910902'),
(37911, 'Vega de Valcarce', 1200, 'LE', 207, 'ES', 42.66068000, -6.93785000, '2019-10-05 22:46:19', '2019-10-05 22:46:19', 1, 'Q918084'),
(37912, 'Vega de Valdetronco', 1183, 'VA', 207, 'ES', 41.59364000, -5.11319000, '2019-10-05 22:46:19', '2022-08-29 11:48:45', 1, 'Q1922901'),
(37913, 'Vega de Villalobos', 1161, 'ZA', 207, 'ES', 41.97057000, -5.46324000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q1769028'),
(37914, 'Vega del Codorno', 5106, 'CU', 207, 'ES', 40.42457000, -1.91312000, '2019-10-05 22:46:19', '2022-08-29 11:05:01', 1, 'Q1766366'),
(37915, 'Vegacervera', 1200, 'LE', 207, 'ES', 42.88808000, -5.53549000, '2019-10-05 22:46:19', '2019-10-05 22:46:19', 1, 'Q1635326'),
(37916, 'Vegalatrave', 1161, 'ZA', 207, 'ES', 41.70026000, -6.10675000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q979661'),
(37917, 'Veganzones', 1192, 'SG', 207, 'ES', 41.19308000, -3.99329000, '2019-10-05 22:46:19', '2022-08-29 11:50:43', 1, 'Q1905633'),
(37918, 'Vegaquemada', 1200, 'LE', 207, 'ES', 42.81870000, -5.33242000, '2019-10-05 22:46:19', '2019-10-05 22:46:19', 1, 'Q1635299'),
(37919, 'Vegas de Matute', 1192, 'SG', 207, 'ES', 40.79476000, -4.27762000, '2019-10-05 22:46:19', '2022-08-29 11:50:43', 1, 'Q1916649'),
(37920, 'Veguillas de la Sierra', 5111, 'TE', 207, 'ES', 40.15000000, -1.40000000, '2019-10-05 22:46:19', '2022-08-29 11:29:44', 1, 'Q1650911'),
(37921, 'Vejer de la Frontera', 5096, 'CA', 207, 'ES', 36.25213000, -5.96717000, '2019-10-05 22:46:19', '2022-08-28 18:44:29', 1, 'Q827393'),
(37922, 'Velada', 1205, 'TO', 207, 'ES', 39.97687000, -4.97641000, '2019-10-05 22:46:19', '2022-08-29 11:08:30', 1, 'Q1628847'),
(37923, 'Velamazán', 1208, 'SO', 207, 'ES', 41.44895000, -2.69935000, '2019-10-05 22:46:19', '2022-08-29 11:51:23', 1, 'Q832876'),
(37924, 'Velascálvaro', 1183, 'VA', 207, 'ES', 41.23028000, -4.97242000, '2019-10-05 22:46:19', '2022-08-29 11:48:45', 1, 'Q1907096'),
(37925, 'Velayos', 1189, 'AV', 207, 'ES', 40.84157000, -4.62324000, '2019-10-05 22:46:19', '2022-08-29 11:49:57', 1, 'Q1630403'),
(37926, 'Velefique', 5095, 'AL', 207, 'ES', 37.19407000, -2.40155000, '2019-10-05 22:46:19', '2022-08-28 18:41:41', 1, 'Q23978704'),
(37928, 'Velilla', 1183, 'VA', 207, 'ES', 41.55920000, -5.00454000, '2019-10-05 22:46:19', '2022-08-29 11:48:45', 1, 'Q1110343'),
(37929, 'Velilla de Ebro', 5113, 'Z', 207, 'ES', 41.37416000, -0.43483000, '2019-10-05 22:46:19', '2022-08-29 11:42:55', 1, 'Q1650389'),
(37930, 'Velilla de Jiloca', 5113, 'Z', 207, 'ES', 41.27509000, -1.60396000, '2019-10-05 22:46:19', '2022-08-29 11:42:55', 1, 'Q764206'),
(37931, 'Velilla de San Antonio', 1158, 'M', 207, 'ES', 40.36516000, -3.48484000, '2019-10-05 22:46:19', '2022-08-29 12:04:40', 1, 'Q1999742'),
(37932, 'Velilla de los Ajos', 1208, 'SO', 207, 'ES', 41.49058000, -2.25579000, '2019-10-05 22:46:19', '2022-08-29 11:51:23', 1, 'Q832805'),
(37933, 'Velilla del Río Carrión', 1157, 'P', 207, 'ES', 42.82611000, -4.84626000, '2019-10-05 22:46:19', '2022-08-29 11:45:45', 1, 'Q986559'),
(37934, 'Vellisca', 5106, 'CU', 207, 'ES', 40.12965000, -2.81444000, '2019-10-05 22:46:19', '2022-08-29 11:05:01', 1, 'Q1768243'),
(37935, 'Velliza', 1183, 'VA', 207, 'ES', 41.57918000, -4.94655000, '2019-10-05 22:46:19', '2022-08-29 11:48:45', 1, 'Q1921104'),
(37936, 'Venialbo', 1161, 'ZA', 207, 'ES', 41.38957000, -5.53596000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q1752370'),
(37937, 'Venta de Baños', 1157, 'P', 207, 'ES', 41.92110000, -4.49089000, '2019-10-05 22:46:19', '2022-08-29 11:45:44', 1, 'Q1657213'),
(37938, 'Venta del Moro', 1175, 'V', 207, 'ES', 39.48333000, -1.35000000, '2019-10-05 22:46:19', '2022-08-29 12:05:40', 1, 'Q742947'),
(37939, 'Ventalló', 5103, 'GI', 207, 'ES', 42.14921000, 3.02635000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q11908'),
(37941, 'Ventas de Huelma', 5098, 'GR', 207, 'ES', 37.06840000, -3.81983000, '2019-10-05 22:46:19', '2022-08-28 18:52:58', 1, 'Q599102'),
(37942, 'Ventosa', 1171, 'LO', 207, 'ES', 42.40495000, -2.62651000, '2019-10-05 22:46:19', '2022-08-29 12:05:09', 1, 'Q599102'),
(37943, 'Ventosa de la Cuesta', 1183, 'VA', 207, 'ES', 41.41110000, -4.82932000, '2019-10-05 22:46:19', '2022-08-29 11:48:45', 1, 'Q1907014'),
(37944, 'Ventosa del Río Almar', 1147, 'SA', 207, 'ES', 40.92728000, -5.34838000, '2019-10-05 22:46:19', '2022-08-29 11:44:52', 1, 'Q1766784'),
(37945, 'Ventrosa', 1171, 'LO', 207, 'ES', 42.15705000, -2.85044000, '2019-10-05 22:46:19', '2022-08-29 12:05:09', 1, 'Q1636171'),
(37946, 'Venturada', 1158, 'M', 207, 'ES', 40.79717000, -3.61974000, '2019-10-05 22:46:19', '2022-08-29 12:04:40', 1, 'Q1647121'),
(37947, 'Vera', 5095, 'AL', 207, 'ES', 37.24345000, -1.85905000, '2019-10-05 22:46:19', '2022-08-28 18:41:41', 1, 'Q1647121'),
(37948, 'Vera de Moncayo', 5113, 'Z', 207, 'ES', 41.82400000, -1.68799000, '2019-10-05 22:46:19', '2022-08-29 11:42:55', 1, 'Q24019796'),
(37950, 'Verdú', 5104, 'L', 207, 'ES', 41.61057000, 1.14284000, '2019-10-05 22:46:19', '2022-08-29 10:55:25', 1, 'Q24019796'),
(37951, 'Verea', 5091, 'OR', 207, 'ES', 42.10653000, -8.00092000, '2019-10-05 22:46:19', '2022-08-28 17:53:26', 1, 'Q1628482'),
(37952, 'Vergel', 1175, 'V', 207, 'ES', 38.84709000, 0.01034000, '2019-10-05 22:46:19', '2022-08-29 12:05:40', 1, 'Q1751998'),
(37953, 'Verges', 5103, 'GI', 207, 'ES', 42.06283000, 3.04579000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q1751998'),
(37954, 'Vertavillo', 1157, 'P', 207, 'ES', 41.83281000, -4.32707000, '2019-10-05 22:46:19', '2022-08-29 11:45:45', 1, 'Q672722'),
(37955, 'Verín', 5091, 'OR', 207, 'ES', 41.94149000, -7.43809000, '2019-10-05 22:46:19', '2022-08-28 17:53:26', 1, 'Q176504'),
(37956, 'Vezdemarbán', 1161, 'ZA', 207, 'ES', 41.65480000, -5.36609000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q1652170'),
(37957, 'Viana de Cega', 1183, 'VA', 207, 'ES', 41.52927000, -4.75245000, '2019-10-05 22:46:19', '2022-08-29 11:48:45', 1, 'Q1766715'),
(37958, 'Viana de Duero', 1208, 'SO', 207, 'ES', 41.53406000, -2.46099000, '2019-10-05 22:46:19', '2022-08-29 11:51:23', 1, 'Q832233'),
(37959, 'Viana de Jadraque', 5107, 'GU', 207, 'ES', 41.02577000, -2.76966000, '2019-10-05 22:46:19', '2022-08-29 11:06:46', 1, 'Q1658084'),
(37960, 'Viandar de la Vera', 1190, 'CC', 207, 'ES', 40.12191000, -5.53593000, '2019-10-05 22:46:19', '2022-08-28 18:12:22', 1, 'Q623175'),
(37961, 'Viator', 5095, 'AL', 207, 'ES', 36.89006000, -2.42695000, '2019-10-05 22:46:19', '2022-08-28 18:41:41', 1, 'Q623175'),
(37962, 'Vic', 5102, 'B', 207, 'ES', 41.93012000, 2.25486000, '2019-10-05 22:46:19', '2022-08-29 10:50:01', 1, 'Q623175'),
(37963, 'Vicálvaro', 1158, 'M', 207, 'ES', 40.40000000, -3.60000000, '2019-10-05 22:46:19', '2022-08-29 12:04:40', 1, 'Q589403'),
(37964, 'Vidayanes', 1161, 'ZA', 207, 'ES', 41.91011000, -5.57424000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q727977'),
(37965, 'Videmala', 1161, 'ZA', 207, 'ES', 41.61299000, -6.04056000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q1652161'),
(37966, 'Vidreres', 5103, 'GI', 207, 'ES', 41.78333000, 2.78333000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q13715'),
(37967, 'Vidrà', 5103, 'GI', 207, 'ES', 42.12285000, 2.30977000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q13715'),
(37969, 'Vierlas', 5113, 'Z', 207, 'ES', 41.92708000, -1.68123000, '2019-10-05 22:46:19', '2022-08-29 11:42:55', 1, 'Q594134'),
(37970, 'Vigo', 1167, 'PO', 207, 'ES', 42.23282000, -8.72264000, '2019-10-05 22:46:19', '2022-08-28 17:57:54', 1, 'Q594134'),
(37971, 'Viguera', 1171, 'LO', 207, 'ES', 42.30934000, -2.53334000, '2019-10-05 22:46:19', '2022-08-29 12:05:09', 1, 'Q1646039'),
(37972, 'Vila-real', 5110, 'CS', 207, 'ES', 39.93830000, -0.10087000, '2019-10-05 22:46:19', '2022-08-29 11:26:43', 1, 'Q1646039'),
(37973, 'Vila-seca', 1203, 'T', 207, 'ES', 41.11118000, 1.14764000, '2019-10-05 22:46:19', '2022-08-29 10:57:33', 1, 'Q1646039'),
(37974, 'Vilabella', 1203, 'T', 207, 'ES', 41.24779000, 1.33019000, '2019-10-05 22:46:19', '2022-08-29 10:57:33', 1, 'Q1646039'),
(37975, 'Vilabertran', 5103, 'GI', 207, 'ES', 42.28255000, 2.98144000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q12148'),
(37976, 'Vilablareix', 5103, 'GI', 207, 'ES', 41.95746000, 2.77377000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q13457'),
(37977, 'Viladasens', 5103, 'GI', 207, 'ES', 42.08333000, 2.93333000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q13455'),
(37978, 'Viladecans', 5102, 'B', 207, 'ES', 41.31405000, 2.01427000, '2019-10-05 22:46:19', '2022-08-29 10:50:01', 1, 'Q15652'),
(37979, 'Vilademuls', 5103, 'GI', 207, 'ES', 42.13890000, 2.88819000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q13620'),
(37980, 'Viladrau', 5103, 'GI', 207, 'ES', 41.84746000, 2.39019000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q13858'),
(37981, 'Vilafant', 5103, 'GI', 207, 'ES', 42.24668000, 2.93820000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q12153'),
(37982, 'Vilaflor', 5112, 'TF', 207, 'ES', 28.15623000, -16.63592000, '2019-10-05 22:46:19', '2022-08-29 11:31:13', 1, 'Q12153'),
(37983, 'Vilafranca de Bonany', 1174, 'PM', 207, 'ES', 39.56894000, 3.08815000, '2019-10-05 22:46:19', '2019-10-05 22:46:19', 1, 'Q975679'),
(37984, 'Vilafranca del Penedès', 5102, 'B', 207, 'ES', 41.34618000, 1.69713000, '2019-10-05 22:46:19', '2022-08-29 10:50:01', 1, 'Q975679'),
(37985, 'Vilagarcía de Arousa', 1167, 'PO', 207, 'ES', 42.59631000, -8.76426000, '2019-10-05 22:46:19', '2022-08-28 17:57:54', 1, 'Q23991995'),
(37986, 'Vilajuïga', 5103, 'GI', 207, 'ES', 42.32533000, 3.09302000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q23991995'),
(37987, 'Vilalba', 5090, 'LU', 207, 'ES', 43.29806000, -7.68130000, '2019-10-05 22:46:19', '2022-08-28 17:49:36', 1, 'Q23991995'),
(37988, 'Vilamacolum', 5103, 'GI', 207, 'ES', 42.19618000, 3.05662000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q12161'),
(37989, 'Vilamalla', 5103, 'GI', 207, 'ES', 42.21720000, 2.97009000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q12163'),
(37990, 'Vilamaniscle', 5103, 'GI', 207, 'ES', 42.37522000, 3.06755000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q12165'),
(37991, 'Vilamarxant', 1175, 'V', 207, 'ES', 39.56916000, -0.62453000, '2019-10-05 22:46:19', '2022-08-29 12:05:40', 1, 'Q987433'),
(37992, 'Vilanant', 5103, 'GI', 207, 'ES', 42.25471000, 2.88923000, '2019-10-05 22:46:19', '2022-08-29 10:53:17', 1, 'Q12173'),
(37994, 'Vilanova de Arousa', 1167, 'PO', 207, 'ES', 42.56400000, -8.82797000, '2019-10-05 22:46:19', '2022-08-28 17:57:54', 1, 'Q1010284'),
(37995, 'Vilanova de Bellpuig', 5104, 'L', 207, 'ES', 41.61379000, 0.96432000, '2019-10-05 22:46:19', '2022-08-29 10:55:25', 1, 'Q1903534'),
(37996, 'Vilanova de Prades', 1203, 'T', 207, 'ES', 41.34850000, 0.95667000, '2019-10-05 22:46:19', '2022-08-29 10:57:33', 1, 'Q1903534'),
(37997, 'Vilanova de Sau', 5102, 'B', 207, 'ES', 41.94700000, 2.38440000, '2019-10-05 22:46:19', '2022-08-29 10:50:01', 1, 'Q1903534'),
(37998, 'Vilanova del Camí', 5102, 'B', 207, 'ES', 41.57165000, 1.63751000, '2019-10-05 22:46:19', '2022-08-29 10:50:01', 1, 'Q1903534'),
(37999, 'Vilanova i la Geltrú', 5102, 'B', 207, 'ES', 41.22392000, 1.72511000, '2019-10-05 22:46:19', '2022-08-29 10:50:01', 1, 'Q1903534'),
(38001, 'Vilaplana', 1203, 'T', 207, 'ES', 41.22800000, 1.03325000, '2019-10-05 22:46:19', '2022-08-29 10:57:33', 1, 'Q1903534'),
(38002, 'Vilasantar', 5089, 'C', 207, 'ES', 43.07106000, -8.12163000, '2019-10-05 22:46:19', '2022-08-28 13:37:17', 1, 'Q340094'),
(38004, 'Vilassar de Mar', 5102, 'B', 207, 'ES', 41.50507000, 2.39227000, '2019-10-05 22:46:19', '2022-08-29 10:50:01', 1, 'Q340094'),
(38005, 'Vilaxoán', 1167, 'PO', 207, 'ES', 42.58353000, -8.79353000, '2019-10-05 22:46:19', '2022-08-28 18:05:08', 1, 'Q340094'),
(38006, 'Vilches', 5100, 'J', 207, 'ES', 38.20695000, -3.51025000, '2019-10-05 22:46:19', '2022-08-28 19:04:30', 1, 'Q684071'),
(38007, 'Vileña', 1146, 'BU', 207, 'ES', 42.62227000, -3.32293000, '2019-10-05 22:46:19', '2022-08-29 11:24:21', 1, 'Q1642451'),
(38009, 'Villa de Vallecas', 1158, 'M', 207, 'ES', 40.36695000, -3.60146000, '2019-10-05 22:46:19', '2022-08-29 12:04:40', 1, 'Q1947988'),
(38011, 'Villa del Campo', 1190, 'CC', 207, 'ES', 40.14223000, -6.42679000, '2019-10-05 22:46:19', '2022-08-28 18:12:22', 1, 'Q956050'),
(38012, 'Villa del Prado', 1158, 'M', 207, 'ES', 40.27852000, -4.30534000, '2019-10-05 22:46:19', '2022-08-29 12:04:40', 1, 'Q24012659'),
(38013, 'Villa del Rey', 1190, 'CC', 207, 'ES', 39.65955000, -6.82122000, '2019-10-05 22:46:19', '2022-08-28 18:12:22', 1, 'Q1642021'),
(38014, 'Villa del Río', 5097, 'CO', 207, 'ES', 37.98108000, -4.29003000, '2019-10-05 22:46:19', '2022-08-28 18:49:38', 1, 'Q1642021'),
(38015, 'Villabaruz de Campos', 1183, 'VA', 207, 'ES', 42.01099000, -4.99624000, '2019-10-05 22:46:19', '2022-08-29 11:48:46', 1, 'Q62128606'),
(38016, 'Villablanca', 5099, 'H', 207, 'ES', 37.30239000, -7.34413000, '2019-10-05 22:46:19', '2022-08-28 19:00:43', 1, 'Q281318'),
(38017, 'Villablino', 1200, 'LE', 207, 'ES', 42.93932000, -6.31943000, '2019-10-05 22:46:19', '2019-10-05 22:46:19', 1, 'Q1605938'),
(38018, 'Villabona', 1191, 'SS', 207, 'ES', 43.18540000, -2.05304000, '2019-10-05 22:46:19', '2022-08-28 18:36:50', 1, 'Q1605938'),
(38019, 'Villabraz', 1200, 'LE', 207, 'ES', 42.24658000, -5.44593000, '2019-10-05 22:46:19', '2019-10-05 22:46:19', 1, 'Q24013070'),
(38020, 'Villabrágima', 1183, 'VA', 207, 'ES', 41.82178000, -5.11546000, '2019-10-05 22:46:19', '2022-08-29 11:48:46', 1, 'Q24016855'),
(38021, 'Villabrázaro', 1161, 'ZA', 207, 'ES', 42.05394000, -5.72748000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q1769082'),
(38022, 'Villabuena del Puente', 1161, 'ZA', 207, 'ES', 41.38074000, -5.40787000, '2019-10-05 22:46:19', '2022-08-29 11:48:04', 1, 'Q1766845'),
(38023, 'Villabáñez', 1183, 'VA', 207, 'ES', 41.63097000, -4.52192000, '2019-10-05 22:46:19', '2022-08-29 11:48:46', 1, 'Q1766845'),
(38024, 'Villacarralón', 1183, 'VA', 207, 'ES', 42.18999000, -5.04324000, '2019-10-05 22:46:19', '2022-08-29 11:48:46', 1, 'Q1913798'),
(38025, 'Villacarriedo', 1170, 'S', 207, 'ES', 43.22851000, -3.81057000, '2019-10-05 22:46:19', '2019-10-05 22:46:19', 1, 'Q1623024'),
(38026, 'Villacarrillo', 5100, 'J', 207, 'ES', 38.11560000, -3.08480000, '2019-10-05 22:46:19', '2022-08-28 19:04:30', 1, 'Q1751217'),
(38027, 'Villacastín', 1192, 'SG', 207, 'ES', 40.77960000, -4.41357000, '2019-10-05 22:46:20', '2022-08-29 11:50:43', 1, 'Q1766562'),
(38028, 'Villacañas', 1205, 'TO', 207, 'ES', 39.62367000, -3.33813000, '2019-10-05 22:46:20', '2022-08-29 11:08:30', 1, 'Q659416'),
(38029, 'Villacid de Campos', 1183, 'VA', 207, 'ES', 42.08250000, -5.12498000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1920662'),
(38030, 'Villacidaler', 1157, 'P', 207, 'ES', 42.22168000, -4.97668000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q62128526'),
(38031, 'Villaciervos', 1208, 'SO', 207, 'ES', 41.76227000, -2.62678000, '2019-10-05 22:46:20', '2022-08-29 11:51:23', 1, 'Q834704'),
(38032, 'Villaco', 1183, 'VA', 207, 'ES', 41.74022000, -4.26817000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1919521'),
(38033, 'Villaconancio', 1157, 'P', 207, 'ES', 41.87174000, -4.22379000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1906856'),
(38034, 'Villaconejos', 1158, 'M', 207, 'ES', 40.10139000, -3.48258000, '2019-10-05 22:46:20', '2022-08-29 12:04:40', 1, 'Q1906856'),
(38035, 'Villaconejos de Trabaque', 5106, 'CU', 207, 'ES', 40.40064000, -2.31956000, '2019-10-05 22:46:20', '2022-08-29 11:05:01', 1, 'Q24011466'),
(38036, 'Villada', 1157, 'P', 207, 'ES', 42.25111000, -4.96708000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q24012108'),
(38037, 'Villadangos del Páramo', 1200, 'LE', 207, 'ES', 42.51672000, -5.76737000, '2019-10-05 22:46:20', '2020-05-01 17:23:17', 1, 'Q918080'),
(38038, 'Villadecanes', 1200, 'LE', 207, 'ES', 42.57973000, -6.75971000, '2019-10-05 22:46:20', '2019-10-05 22:46:20', 1, 'Q1755967'),
(38039, 'Villademor de la Vega', 1200, 'LE', 207, 'ES', 42.26964000, -5.56808000, '2019-10-05 22:46:20', '2019-10-05 22:46:20', 1, 'Q596287'),
(38040, 'Villadepera', 1161, 'ZA', 207, 'ES', 41.54700000, -6.13305000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1766920'),
(38041, 'Villadiego', 1146, 'BU', 207, 'ES', 42.51589000, -4.00958000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1630718'),
(38042, 'Villadoz', 5113, 'Z', 207, 'ES', 41.16234000, -1.28800000, '2019-10-05 22:46:20', '2022-08-29 11:42:55', 1, 'Q1639196'),
(38043, 'Villaeles de Valdavia', 1157, 'P', 207, 'ES', 42.56556000, -4.58318000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1906953'),
(38044, 'Villaescusa', 1161, 'ZA', 207, 'ES', 41.20617000, -5.46406000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1922230'),
(38046, 'Villaescusa de Roa', 1146, 'BU', 207, 'ES', 41.72727000, -4.01720000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1630578'),
(38047, 'Villaescusa la Sombría', 1146, 'BU', 207, 'ES', 42.41518000, -3.41840000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1642428'),
(38048, 'Villaespasa', 1146, 'BU', 207, 'ES', 42.09991000, -3.40436000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1642610'),
(38049, 'Villafamés', 1175, 'V', 207, 'ES', 40.11667000, -0.05000000, '2019-10-05 22:46:20', '2022-08-29 12:05:40', 1, 'Q23990478'),
(38050, 'Villafeliche', 5113, 'Z', 207, 'ES', 41.19509000, -1.50983000, '2019-10-05 22:46:20', '2022-08-29 11:42:55', 1, 'Q1650316'),
(38051, 'Villaferrueña', 1161, 'ZA', 207, 'ES', 42.09830000, -5.85802000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1922214'),
(38052, 'Villaflor', 1189, 'AV', 207, 'ES', 40.75865000, -4.87379000, '2019-10-05 22:46:20', '2022-08-29 11:49:57', 1, 'Q1922214'),
(38053, 'Villaflores', 1147, 'SA', 207, 'ES', 41.08397000, -5.23436000, '2019-10-05 22:46:20', '2022-08-29 11:44:52', 1, 'Q636002'),
(38054, 'Villafrades de Campos', 1183, 'VA', 207, 'ES', 42.07853000, -4.97125000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q24016850'),
(38055, 'Villafranca', 1204, 'NA', 207, 'ES', 42.27933000, -1.74628000, '2019-10-05 22:46:20', '2022-08-29 12:06:07', 1, 'Q24016850'),
(38056, 'Villafranca de Córdoba', 5097, 'CO', 207, 'ES', 37.96257000, -4.54547000, '2019-10-05 22:46:20', '2022-08-28 18:49:38', 1, 'Q24016850'),
(38057, 'Villafranca de Duero', 1183, 'VA', 207, 'ES', 41.43310000, -5.30192000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1929368'),
(38058, 'Villafranca de Ebro', 5113, 'Z', 207, 'ES', 41.57473000, -0.65039000, '2019-10-05 22:46:20', '2022-08-29 11:42:55', 1, 'Q1638846'),
(38059, 'Villafranca de la Sierra', 1189, 'AV', 207, 'ES', 40.49806000, -5.23005000, '2019-10-05 22:46:20', '2022-08-29 11:49:57', 1, 'Q1630388'),
(38060, 'Villafranca de los Barros', 5092, 'BA', 207, 'ES', 38.56144000, -6.33810000, '2019-10-05 22:46:20', '2022-08-28 18:09:23', 1, 'Q613908'),
(38061, 'Villafranca de los Caballeros', 1205, 'TO', 207, 'ES', 39.42824000, -3.36079000, '2019-10-05 22:46:20', '2022-08-29 11:08:30', 1, 'Q1629782'),
(38062, 'Villafranca del Bierzo', 1200, 'LE', 207, 'ES', 42.60601000, -6.81069000, '2019-10-05 22:46:20', '2019-10-05 22:46:20', 1, 'Q848300'),
(38063, 'Villafranca del Campo', 5111, 'TE', 207, 'ES', 40.69545000, -1.34722000, '2019-10-05 22:46:20', '2022-08-29 11:29:44', 1, 'Q960090'),
(38064, 'Villafranca del Cid', 1175, 'V', 207, 'ES', 40.42885000, -0.25775000, '2019-10-05 22:46:20', '2022-08-29 12:05:40', 1, 'Q960090'),
(38065, 'Villafrechós', 1183, 'VA', 207, 'ES', 41.89290000, -5.21859000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1929390'),
(38066, 'Villafruela', 1146, 'BU', 207, 'ES', 41.91647000, -3.91371000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1642469'),
(38067, 'Villafuerte', 1183, 'VA', 207, 'ES', 41.73431000, -4.32365000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1642469'),
(38068, 'Villafufre', 1170, 'S', 207, 'ES', 43.26557000, -3.89370000, '2019-10-05 22:46:20', '2019-10-05 22:46:20', 1, 'Q1642469'),
(38069, 'Villafáfila', 1161, 'ZA', 207, 'ES', 41.84692000, -5.61527000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q965140'),
(38070, 'Villagalijo', 1146, 'BU', 207, 'ES', 42.34872000, -3.19203000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1642482'),
(38071, 'Villagarcía de Campos', 1183, 'VA', 207, 'ES', 41.78032000, -5.19242000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q24016847'),
(38072, 'Villagarcía de la Torre', 5092, 'BA', 207, 'ES', 38.30000000, -6.08333000, '2019-10-05 22:46:20', '2022-08-28 18:09:23', 1, 'Q1628543'),
(38073, 'Villagarcía del Llano', 5106, 'CU', 207, 'ES', 39.31667000, -1.83333000, '2019-10-05 22:46:20', '2022-08-29 11:05:01', 1, 'Q1648723'),
(38074, 'Villagatón', 1200, 'LE', 207, 'ES', 42.63413000, -6.16184000, '2019-10-05 22:46:20', '2020-05-01 17:23:17', 1, 'Q1622304'),
(38075, 'Villageriz', 1161, 'ZA', 207, 'ES', 42.11884000, -5.95554000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1768976'),
(38076, 'Villagonzalo', 5092, 'BA', 207, 'ES', 38.86329000, -6.19665000, '2019-10-05 22:46:20', '2022-08-28 18:09:23', 1, 'Q784629'),
(38077, 'Villagonzalo de Tormes', 1147, 'SA', 207, 'ES', 40.89221000, -5.49664000, '2019-10-05 22:46:20', '2022-08-29 11:44:52', 1, 'Q1904710'),
(38078, 'Villagonzalo-Pedernales', 1200, 'LE', 207, 'ES', 42.30145000, -3.73539000, '2019-10-05 22:46:20', '2019-10-05 22:46:20', 1, 'Q1904710'),
(38080, 'Villagómez la Nueva', 1183, 'VA', 207, 'ES', 42.15592000, -5.14208000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1907555'),
(38081, 'Villaharta', 5097, 'CO', 207, 'ES', 38.13333000, -4.90000000, '2019-10-05 22:46:20', '2022-08-28 18:49:38', 1, 'Q612826'),
(38082, 'Villahermosa', 5105, 'CR', 207, 'ES', 38.75023000, -2.87066000, '2019-10-05 22:46:20', '2022-08-29 11:03:25', 1, 'Q23978008'),
(38083, 'Villahermosa del Campo', 5111, 'TE', 207, 'ES', 41.10942000, -1.24692000, '2019-10-05 22:46:20', '2022-08-29 11:29:44', 1, 'Q302820'),
(38084, 'Villahermosa del Río', 5110, 'CS', 207, 'ES', 40.20268000, -0.41990000, '2019-10-05 22:46:20', '2022-08-29 11:26:43', 1, 'Q302820'),
(38085, 'Villaherreros', 1157, 'P', 207, 'ES', 42.38964000, -4.46254000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1918842'),
(38086, 'Villahoz', 1146, 'BU', 207, 'ES', 42.07587000, -3.91290000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q961807'),
(38087, 'Villahán', 1157, 'P', 207, 'ES', 42.05031000, -4.13104000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1918587'),
(38088, 'Villajoyosa', 1175, 'V', 207, 'ES', 38.50754000, -0.23346000, '2019-10-05 22:46:20', '2022-08-29 12:05:40', 1, 'Q1918587'),
(38089, 'Villalaco', 1157, 'P', 207, 'ES', 42.15569000, -4.25940000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1906914'),
(38090, 'Villalar de los Comuneros', 1183, 'VA', 207, 'ES', 41.54966000, -5.13810000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1908262'),
(38091, 'Villalazán', 1161, 'ZA', 207, 'ES', 41.49414000, -5.58910000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q673625'),
(38092, 'Villalba de Duero', 1146, 'BU', 207, 'ES', 41.68243000, -3.74443000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1630696'),
(38093, 'Villalba de Guardo', 1157, 'P', 207, 'ES', 42.72229000, -4.82328000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1919165'),
(38094, 'Villalba de Perejil', 5113, 'Z', 207, 'ES', 41.32742000, -1.54833000, '2019-10-05 22:46:20', '2022-08-29 11:42:55', 1, 'Q1650366'),
(38095, 'Villalba de Rioja', 1171, 'LO', 207, 'ES', 42.60967000, -2.88687000, '2019-10-05 22:46:20', '2022-08-29 12:05:09', 1, 'Q1646985'),
(38096, 'Villalba de la Lampreana', 1161, 'ZA', 207, 'ES', 41.74327000, -5.64094000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1769018'),
(38097, 'Villalba de la Loma', 1183, 'VA', 207, 'ES', 42.17472000, -5.19023000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1907030'),
(38098, 'Villalba de la Sierra', 5106, 'CU', 207, 'ES', 40.23437000, -2.08929000, '2019-10-05 22:46:20', '2022-08-29 11:05:01', 1, 'Q1767420'),
(38099, 'Villalba de los Alcores', 1183, 'VA', 207, 'ES', 41.86361000, -4.86063000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1907118'),
(38100, 'Villalba de los Barros', 5092, 'BA', 207, 'ES', 38.61294000, -6.50914000, '2019-10-05 22:46:20', '2022-08-28 18:09:23', 1, 'Q1628578'),
(38101, 'Villalba de los Llanos', 1147, 'SA', 207, 'ES', 40.80020000, -5.97580000, '2019-10-05 22:46:20', '2022-08-29 11:44:52', 1, 'Q1905453'),
(38102, 'Villalba del Alcor', 5099, 'H', 207, 'ES', 37.39731000, -6.47461000, '2019-10-05 22:46:20', '2022-08-28 19:00:43', 1, 'Q904236'),
(38103, 'Villalba del Rey', 5106, 'CU', 207, 'ES', 40.34653000, -2.63902000, '2019-10-05 22:46:20', '2022-08-29 11:05:01', 1, 'Q24011462'),
(38104, 'Villalbarba', 1183, 'VA', 207, 'ES', 41.60389000, -5.21281000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1929274'),
(38105, 'Villalbilla', 1158, 'M', 207, 'ES', 40.43044000, -3.29904000, '2019-10-05 22:46:20', '2022-08-29 12:04:40', 1, 'Q1646224'),
(38106, 'Villalbilla de Burgos', 1146, 'BU', 207, 'ES', 42.34757000, -3.78101000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q526762'),
(38107, 'Villalbilla de Gumiel', 1146, 'BU', 207, 'ES', 41.80621000, -3.62584000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1642028'),
(38108, 'Villalcampo', 1161, 'ZA', 207, 'ES', 41.52229000, -6.04801000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1752356'),
(38109, 'Villalcázar de Sirga', 1157, 'P', 207, 'ES', 42.31662000, -4.54278000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1010261'),
(38110, 'Villalcón', 1157, 'P', 207, 'ES', 42.29287000, -4.85527000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1906503'),
(38111, 'Villaldemiro', 1146, 'BU', 207, 'ES', 42.24719000, -3.98534000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1642565'),
(38112, 'Villalengua', 5113, 'Z', 207, 'ES', 41.43548000, -1.84125000, '2019-10-05 22:46:20', '2022-08-29 11:42:55', 1, 'Q1650349'),
(38113, 'Villalgordo del Marquesado', 5106, 'CU', 207, 'ES', 39.68245000, -2.50885000, '2019-10-05 22:46:20', '2022-08-29 11:05:01', 1, 'Q1767433'),
(38114, 'Villalmanzo', 1146, 'BU', 207, 'ES', 42.04840000, -3.74195000, '2019-10-05 22:46:20', '2022-08-29 11:24:21', 1, 'Q1642603'),
(38115, 'Villalobar de Rioja', 1171, 'LO', 207, 'ES', 42.49123000, -2.96404000, '2019-10-05 22:46:20', '2022-08-29 12:05:09', 1, 'Q1637502'),
(38116, 'Villalobos', 1161, 'ZA', 207, 'ES', 41.94567000, -5.47517000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1922078'),
(38117, 'Villalobón', 1157, 'P', 207, 'ES', 42.03037000, -4.50340000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q191773'),
(38118, 'Villalonga', 1175, 'V', 207, 'ES', 38.88566000, -0.20795000, '2019-10-05 22:46:20', '2022-08-29 12:05:40', 1, 'Q1993779'),
(38119, 'Villalonso', 1161, 'ZA', 207, 'ES', 41.59767000, -5.29769000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1646319'),
(38120, 'Villalpando', 1161, 'ZA', 207, 'ES', 41.86517000, -5.41231000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1646362'),
(38121, 'Villalpardo', 5106, 'CU', 207, 'ES', 39.46667000, -1.63333000, '2019-10-05 22:46:20', '2022-08-29 11:05:01', 1, 'Q510908'),
(38122, 'Villalube', 1161, 'ZA', 207, 'ES', 41.61005000, -5.54545000, '2019-10-05 22:46:20', '2022-08-29 11:48:04', 1, 'Q1766477'),
(38123, 'Villaluenga de la Vega', 1157, 'P', 207, 'ES', 42.52386000, -4.76489000, '2019-10-05 22:46:20', '2022-08-29 11:45:45', 1, 'Q1919363'),
(38124, 'Villaluenga del Rosario', 5096, 'CA', 207, 'ES', 36.69644000, -5.38601000, '2019-10-05 22:46:20', '2022-08-28 18:44:29', 1, 'Q650598'),
(38125, 'Villalán de Campos', 1183, 'VA', 207, 'ES', 42.01484000, -5.23642000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q1983000'),
(38126, 'Villalón de Campos', 1183, 'VA', 207, 'ES', 42.09994000, -5.03440000, '2019-10-05 22:46:20', '2022-08-29 11:48:46', 1, 'Q24016839'),
(38128, 'Villamalur', 5110, 'CS', 207, 'ES', 39.96667000, -0.40000000, '2019-10-05 22:46:20', '2022-08-29 11:26:43', 1, 'Q609894'),
(38129, 'Villamandos', 1200, 'LE', 207, 'ES', 42.18058000, -5.59397000, '2019-10-05 22:46:20', '2019-10-05 22:46:20', 1, 'Q1640171'),
(38130, 'Villamanrique', 5105, 'CR', 207, 'ES', 38.54636000, -2.99729000, '2019-10-05 22:46:21', '2022-08-29 11:03:25', 1, 'Q1641411'),
(38131, 'Villamanrique de Tajo', 1158, 'M', 207, 'ES', 40.06617000, -3.23668000, '2019-10-05 22:46:21', '2022-08-29 12:04:40', 1, 'Q1751921'),
(38132, 'Villamanrique de la Condesa', 1193, 'SE', 207, 'ES', 37.24481000, -6.30665000, '2019-10-05 22:46:21', '2022-08-28 19:08:49', 1, 'Q1601699'),
(38133, 'Villamanta', 1158, 'M', 207, 'ES', 40.29858000, -4.10880000, '2019-10-05 22:46:21', '2022-08-29 12:04:40', 1, 'Q1915133'),
(38134, 'Villamantilla', 1158, 'M', 207, 'ES', 40.33846000, -4.12986000, '2019-10-05 22:46:21', '2022-08-29 12:04:40', 1, 'Q1915124'),
(38135, 'Villamanín', 1200, 'LE', 207, 'ES', 42.93864000, -5.65669000, '2019-10-05 22:46:21', '2020-05-01 17:23:17', 1, 'Q977246'),
(38136, 'Villamartín', 5096, 'CA', 207, 'ES', 36.85979000, -5.64485000, '2019-10-05 22:46:21', '2022-08-28 18:44:29', 1, 'Q1442451'),
(38137, 'Villamartín de Campos', 1157, 'P', 207, 'ES', 42.01598000, -4.66414000, '2019-10-05 22:46:21', '2022-08-29 11:45:45', 1, 'Q24013801'),
(38138, 'Villamartín de Don Sancho', 1200, 'LE', 207, 'ES', 42.56943000, -5.06056000, '2019-10-05 22:46:21', '2020-05-01 17:23:17', 1, 'Q637293'),
(38139, 'Villamayor', 1147, 'SA', 207, 'ES', 40.99939000, -5.69579000, '2019-10-05 22:46:21', '2022-08-29 11:44:52', 1, 'Q637293'),
(38140, 'Villamayor de Calatrava', 5105, 'CR', 207, 'ES', 38.78763000, -4.13774000, '2019-10-05 22:46:21', '2022-08-29 11:03:25', 1, 'Q1766526'),
(38141, 'Villamayor de Campos', 1161, 'ZA', 207, 'ES', 41.89870000, -5.35963000, '2019-10-05 22:46:21', '2022-08-29 11:48:04', 1, 'Q1766872'),
(38142, 'Villamayor de Gállego', 5113, 'Z', 207, 'ES', 41.68584000, -0.77230000, '2019-10-05 22:46:21', '2022-08-29 11:42:55', 1, 'Q1766872'),
(38143, 'Villamayor de Monjardín', 1204, 'NA', 207, 'ES', 42.62937000, -2.10503000, '2019-10-05 22:46:21', '2022-08-29 12:06:07', 1, 'Q597234'),
(38144, 'Villamayor de Santiago', 5106, 'CU', 207, 'ES', 39.73072000, -2.92357000, '2019-10-05 22:46:21', '2022-08-29 11:05:01', 1, 'Q1750918'),
(38145, 'Villamayor de Treviño', 1146, 'BU', 207, 'ES', 42.46048000, -4.11923000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q1642492'),
(38146, 'Villamayor de los Montes', 1146, 'BU', 207, 'ES', 42.10565000, -3.76542000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q1642587'),
(38147, 'Villamañán', 1200, 'LE', 207, 'ES', 42.32198000, -5.58195000, '2019-10-05 22:46:21', '2020-05-01 17:23:17', 1, 'Q1607256'),
(38148, 'Villamediana', 1157, 'P', 207, 'ES', 42.05024000, -4.36115000, '2019-10-05 22:46:21', '2022-08-29 11:45:45', 1, 'Q521159'),
(38149, 'Villamediana de Iregua', 1171, 'LO', 207, 'ES', 42.42658000, -2.41943000, '2019-10-05 22:46:21', '2022-08-29 12:05:09', 1, 'Q1606588'),
(38150, 'Villamedianilla', 1146, 'BU', 207, 'ES', 42.16070000, -4.14592000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q1642598'),
(38151, 'Villamejil', 1200, 'LE', 207, 'ES', 42.56205000, -6.02514000, '2019-10-05 22:46:21', '2019-10-05 22:46:21', 1, 'Q1642598'),
(38152, 'Villameriel', 1157, 'P', 207, 'ES', 42.52822000, -4.47572000, '2019-10-05 22:46:21', '2022-08-29 11:45:45', 1, 'Q1918506'),
(38153, 'Villamesías', 1190, 'CC', 207, 'ES', 39.24568000, -5.87307000, '2019-10-05 22:46:21', '2022-08-28 18:12:22', 1, 'Q1642246'),
(38154, 'Villamiel', 1190, 'CC', 207, 'ES', 40.18635000, -6.78425000, '2019-10-05 22:46:21', '2022-08-28 18:12:22', 1, 'Q1613433'),
(38155, 'Villamiel de Toledo', 1205, 'TO', 207, 'ES', 39.96482000, -4.12627000, '2019-10-05 22:46:21', '2022-08-29 11:08:30', 1, 'Q1629276'),
(38156, 'Villamiel de la Sierra', 1146, 'BU', 207, 'ES', 42.19124000, -3.41771000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q615265'),
(38157, 'Villaminaya', 1205, 'TO', 207, 'ES', 39.71197000, -3.87055000, '2019-10-05 22:46:21', '2022-08-29 11:08:30', 1, 'Q1641258'),
(38158, 'Villamol', 1200, 'LE', 207, 'ES', 42.42766000, -5.04832000, '2019-10-05 22:46:21', '2019-10-05 22:46:21', 1, 'Q24013062'),
(38159, 'Villamontán de la Valduerna', 1200, 'LE', 207, 'ES', 42.30962000, -5.99656000, '2019-10-05 22:46:21', '2020-05-01 17:23:17', 1, 'Q1617611'),
(38160, 'Villamor de los Escuderos', 1161, 'ZA', 207, 'ES', 41.25244000, -5.57485000, '2019-10-05 22:46:21', '2022-08-29 11:48:04', 1, 'Q1769022'),
(38161, 'Villamoratiel de las Matas', 1200, 'LE', 207, 'ES', 42.39755000, -5.30064000, '2019-10-05 22:46:21', '2019-10-05 22:46:21', 1, 'Q1640195'),
(38162, 'Villamoronta', 1157, 'P', 207, 'ES', 42.40337000, -4.69899000, '2019-10-05 22:46:21', '2022-08-29 11:45:45', 1, 'Q1917722'),
(38163, 'Villamuelas', 1205, 'TO', 207, 'ES', 39.81784000, -3.73461000, '2019-10-05 22:46:21', '2022-08-29 11:08:30', 1, 'Q24017248'),
(38164, 'Villamuera de la Cueza', 1157, 'P', 207, 'ES', 42.25843000, -4.68860000, '2019-10-05 22:46:21', '2022-08-29 11:45:45', 1, 'Q1906884'),
(38165, 'Villamuriel de Campos', 1183, 'VA', 207, 'ES', 41.94690000, -5.20717000, '2019-10-05 22:46:21', '2022-08-29 11:48:46', 1, 'Q1908560'),
(38166, 'Villamuriel de Cerrato', 1157, 'P', 207, 'ES', 41.94935000, -4.51584000, '2019-10-05 22:46:21', '2022-08-29 11:45:45', 1, 'Q936806'),
(38167, 'Villangómez', 1146, 'BU', 207, 'ES', 42.17961000, -3.77419000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q1643589'),
(38168, 'Villanubla', 1183, 'VA', 207, 'ES', 41.69877000, -4.84173000, '2019-10-05 22:46:21', '2022-08-29 11:48:46', 1, 'Q1651671'),
(38169, 'Villanueva de Alcardete', 1205, 'TO', 207, 'ES', 39.67321000, -3.01445000, '2019-10-05 22:46:21', '2022-08-29 11:08:30', 1, 'Q1629794'),
(38170, 'Villanueva de Alcorón', 5107, 'GU', 207, 'ES', 40.67956000, -2.25145000, '2019-10-05 22:46:21', '2022-08-29 11:06:46', 1, 'Q1658033'),
(38171, 'Villanueva de Algaidas', 5101, 'MA', 207, 'ES', 37.18350000, -4.45032000, '2019-10-05 22:46:21', '2022-08-28 19:06:53', 1, 'Q1647726'),
(38172, 'Villanueva de Argaño', 1146, 'BU', 207, 'ES', 42.38045000, -3.93353000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q767200'),
(38173, 'Villanueva de Argecilla', 5107, 'GU', 207, 'ES', 40.90257000, -2.91431000, '2019-10-05 22:46:21', '2022-08-29 11:06:46', 1, 'Q1658020'),
(38174, 'Villanueva de Azoague', 1161, 'ZA', 207, 'ES', 41.97607000, -5.66400000, '2019-10-05 22:46:21', '2022-08-29 11:48:04', 1, 'Q1775549'),
(38175, 'Villanueva de Bogas', 1205, 'TO', 207, 'ES', 39.72347000, -3.65743000, '2019-10-05 22:46:21', '2022-08-29 11:08:30', 1, 'Q24017245'),
(38176, 'Villanueva de Cameros', 1171, 'LO', 207, 'ES', 42.16719000, -2.65040000, '2019-10-05 22:46:21', '2022-08-29 12:05:09', 1, 'Q1646911'),
(38177, 'Villanueva de Campeán', 1161, 'ZA', 207, 'ES', 41.35447000, -5.77020000, '2019-10-05 22:46:21', '2022-08-29 11:48:04', 1, 'Q1652201'),
(38178, 'Villanueva de Carazo', 1146, 'BU', 207, 'ES', 41.98275000, -3.32428000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q1630669'),
(38179, 'Villanueva de Castellón', 1175, 'V', 207, 'ES', 39.07741000, -0.51167000, '2019-10-05 22:46:21', '2022-08-29 12:05:40', 1, 'Q2041822'),
(38180, 'Villanueva de Córdoba', 5097, 'CO', 207, 'ES', 38.32277000, -4.62873000, '2019-10-05 22:46:21', '2022-08-28 18:49:38', 1, 'Q879493'),
(38181, 'Villanueva de Duero', 1183, 'VA', 207, 'ES', 41.51946000, -4.86671000, '2019-10-05 22:46:21', '2022-08-29 11:48:46', 1, 'Q1651722'),
(38182, 'Villanueva de Gormaz', 1208, 'SO', 207, 'ES', 41.46740000, -3.06223000, '2019-10-05 22:46:21', '2022-08-29 11:51:23', 1, 'Q834844'),
(38183, 'Villanueva de Gumiel', 1146, 'BU', 207, 'ES', 41.73778000, -3.62658000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q1642407'),
(38184, 'Villanueva de Gállego', 5113, 'Z', 207, 'ES', 41.76917000, -0.82350000, '2019-10-05 22:46:21', '2022-08-29 11:42:55', 1, 'Q1639259'),
(38185, 'Villanueva de Gómez', 1189, 'AV', 207, 'ES', 40.88269000, -4.71650000, '2019-10-05 22:46:21', '2022-08-29 11:49:57', 1, 'Q664700'),
(38186, 'Villanueva de Jiloca', 5113, 'Z', 207, 'ES', 41.07625000, -1.38960000, '2019-10-05 22:46:21', '2022-08-29 11:42:55', 1, 'Q1639226'),
(38187, 'Villanueva de Perales', 1158, 'M', 207, 'ES', 40.34541000, -4.09891000, '2019-10-05 22:46:21', '2022-08-29 12:04:40', 1, 'Q1639226'),
(38188, 'Villanueva de San Carlos', 5105, 'CR', 207, 'ES', 38.62173000, -3.90903000, '2019-10-05 22:46:21', '2022-08-29 11:03:25', 1, 'Q1658066'),
(38189, 'Villanueva de San Juan', 1193, 'SE', 207, 'ES', 37.04955000, -5.17540000, '2019-10-05 22:46:21', '2022-08-28 19:08:49', 1, 'Q1442251'),
(38190, 'Villanueva de San Mancio', 1183, 'VA', 207, 'ES', 41.92842000, -5.01200000, '2019-10-05 22:46:21', '2022-08-29 11:48:46', 1, 'Q24016830'),
(38191, 'Villanueva de Sigena', 1177, 'HU', 207, 'ES', 41.71576000, -0.00897000, '2019-10-05 22:46:21', '2022-08-29 12:06:20', 1, 'Q987390'),
(38192, 'Villanueva de Tapia', 5101, 'MA', 207, 'ES', 37.18276000, -4.33383000, '2019-10-05 22:46:21', '2022-08-28 19:06:53', 1, 'Q949909'),
(38193, 'Villanueva de Teba', 1146, 'BU', 207, 'ES', 42.64890000, -3.16313000, '2019-10-05 22:46:21', '2022-08-29 11:24:21', 1, 'Q943842'),
(38195, 'Villanueva de Viver', 5110, 'CS', 207, 'ES', 40.05000000, -0.65000000, '2019-10-05 22:46:21', '2022-08-29 11:26:43', 1, 'Q1646187'),
(38196, 'Villanueva de la Cañada', 1158, 'M', 207, 'ES', 40.44689000, -4.00428000, '2019-10-05 22:46:21', '2022-08-29 12:04:40', 1, 'Q24012654'),
(38197, 'Villanueva de la Condesa', 1183, 'VA', 207, 'ES', 42.14914000, -5.09550000, '2019-10-05 22:46:21', '2022-08-29 11:48:46', 1, 'Q1907042'),
(38198, 'Villanueva de la Fuente', 5105, 'CR', 207, 'ES', 38.69463000, -2.69637000, '2019-10-05 22:46:21', '2022-08-29 11:03:25', 1, 'Q1642043'),
(38199, 'Villanueva de la Jara', 5106, 'CU', 207, 'ES', 39.43333000, -1.93333000, '2019-10-05 22:46:21', '2022-08-29 11:05:01', 1, 'Q319074'),
(38200, 'Villanueva de la Reina', 5100, 'J', 207, 'ES', 38.00432000, -3.91603000, '2019-10-05 22:46:21', '2022-08-28 19:04:30', 1, 'Q1641608'),
(38201, 'Villanueva de la Serena', 5092, 'BA', 207, 'ES', 38.97655000, -5.79740000, '2019-10-05 22:46:21', '2022-08-28 18:09:23', 1, 'Q695380'),
(38202, 'Villanueva de la Sierra', 1190, 'CC', 207, 'ES', 40.20163000, -6.40611000, '2019-10-05 22:46:21', '2022-08-28 18:12:22', 1, 'Q788259'),
(38203, 'Villanueva de la Torre', 5107, 'GU', 207, 'ES', 40.58216000, -3.29764000, '2019-10-05 22:46:21', '2022-08-29 11:06:46', 1, 'Q1642060'),
(38204, 'Villanueva de la Vera', 1190, 'CC', 207, 'ES', 40.12998000, -5.46250000, '2019-10-05 22:46:21', '2022-08-28 18:12:22', 1, 'Q1629837'),
(38205, 'Villanueva de las Cruces', 5099, 'H', 207, 'ES', 37.62783000, -7.02359000, '2019-10-05 22:46:21', '2022-08-28 19:00:43', 1, 'Q1630132'),
(38206, 'Villanueva de las Manzanas', 1200, 'LE', 207, 'ES', 42.47264000, -5.48043000, '2019-10-05 22:46:21', '2019-10-05 22:46:21', 1, 'Q1640166'),
(38207, 'Villanueva de las Peras', 1161, 'ZA', 207, 'ES', 41.93478000, -5.97942000, '2019-10-05 22:46:21', '2022-08-29 11:48:04', 1, 'Q1922198'),
(38208, 'Villanueva de las Torres', 5098, 'GR', 207, 'ES', 37.55719000, -3.08868000, '2019-10-05 22:46:21', '2022-08-28 18:52:58', 1, 'Q619519');

