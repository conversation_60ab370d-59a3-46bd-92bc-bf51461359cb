INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(30835, 'Padborg', 1529, '83', 59, 'D<PERSON>', 54.82657000, 9.36247000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q707345'),
(30836, '<PERSON><PERSON><PERSON>', 1532, '81', 59, 'DK', 57.22147000, 9.67569000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3169598'),
(30837, 'P<PERSON>æstø', 1528, '85', 59, 'DK', 55.12374000, 12.04477000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1563543'),
(30838, 'Randers', 1531, '82', 59, '<PERSON><PERSON>', 56.46070000, 10.03639000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q27168'),
(30839, '<PERSON><PERSON> <PERSON>mmune', 1531, '82', 59, '<PERSON><PERSON>', 56.51561000, 10.06901000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q512457'),
(30840, '<PERSON><PERSON>d <PERSON>mmune', 1532, '81', 59, 'DK', 56.80556000, 9.77778000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q499896'),
(30841, 'Ribe', 1529, '83', 59, 'DK', 55.33051000, 8.76966000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q322361'),
(30842, 'Ringe', 1529, '83', 59, 'DK', 55.23828000, 10.47860000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q303111'),
(30843, 'Ringkøbing', 1531, '82', 59, 'DK', 56.09006000, 8.24402000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q502693'),
(30844, 'Ringkøbing-Skjern Kommune', 1531, '82', 59, 'DK', 56.01000000, 8.39667000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q514777'),
(30845, 'Ringsted', 1528, '85', 59, 'DK', 55.44260000, 11.79011000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q283658'),
(30846, 'Ringsted Kommune', 1528, '85', 59, 'DK', 55.44721000, 11.81720000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q503116'),
(30847, 'Roskilde', 1528, '85', 59, 'DK', 55.64152000, 12.08035000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q26563'),
(30848, 'Roskilde Kommune', 1528, '85', 59, 'DK', 55.65000000, 12.10000000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q510810'),
(30849, 'Rudersdal Kommune', 1530, '84', 59, 'DK', 55.82500000, 12.49167000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q511949'),
(30850, 'Rudkøbing', 1529, '83', 59, 'DK', 54.93639000, 10.71019000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q731023'),
(30851, 'Ry', 1531, '82', 59, 'DK', 56.09038000, 9.76505000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3350663'),
(30852, 'Ryomgård', 1531, '82', 59, 'DK', 56.38430000, 10.50295000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2555857'),
(30853, 'Rødby', 1528, '85', 59, 'DK', 54.69495000, 11.38885000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q161975'),
(30854, 'Rødbyhavn', 1528, '85', 59, 'DK', 54.65944000, 11.35504000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1011683'),
(30855, 'Rødding', 1529, '83', 59, 'DK', 55.36569000, 9.06316000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q836960'),
(30856, 'Rødekro', 1529, '83', 59, 'DK', 55.07076000, 9.33531000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q4541616'),
(30857, 'Rødovre', 1530, '84', 59, 'DK', 55.68062000, 12.45373000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q28230'),
(30858, 'Rødovre Kommune', 1530, '84', 59, 'DK', 55.68852000, 12.44834000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q503097'),
(30859, 'Rødvig', 1528, '85', 59, 'DK', 55.25540000, 12.37936000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q848502'),
(30860, 'Rønde', 1531, '82', 59, 'DK', 56.30145000, 10.47505000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1993774'),
(30861, 'Rønne', 1530, '84', 59, 'DK', 55.10091000, 14.70664000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q690062'),
(30862, 'Rønnede', 1528, '85', 59, 'DK', 55.25710000, 12.02125000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1402824'),
(30863, 'Sabro', 1531, '82', 59, 'DK', 56.21333000, 10.03441000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q7396449'),
(30864, 'Sakskøbing', 1528, '85', 59, 'DK', 54.79970000, 11.62599000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q13218306'),
(30865, 'Samsø Kommune', 1531, '82', 59, 'DK', 55.85245000, 10.60045000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q513256'),
(30866, 'Seden', 1529, '83', 59, 'DK', 55.42648000, 10.44265000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q513256'),
(30867, 'Silkeborg', 1531, '82', 59, 'DK', 56.16970000, 9.54508000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21180'),
(30868, 'Silkeborg Kommune', 1531, '82', 59, 'DK', 56.17495000, 9.54666000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q505151'),
(30869, 'Sindal', 1532, '81', 59, 'DK', 57.47117000, 10.20312000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3177752'),
(30870, 'Skaerbaek', 1529, '83', 59, 'DK', 55.15735000, 8.76901000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2622689'),
(30871, 'Skagen', 1532, '81', 59, 'DK', 57.72093000, 10.58394000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q215177'),
(30872, 'Skanderborg', 1531, '82', 59, 'DK', 56.03434000, 9.93177000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q503379'),
(30873, 'Skanderborg Kommune', 1531, '82', 59, 'DK', 56.07956000, 9.89868000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q7725510'),
(30874, 'Skibby', 1530, '84', 59, 'DK', 55.75122000, 11.96083000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q12335622'),
(30875, 'Skive', 1531, '82', 59, 'DK', 56.56699000, 9.02707000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1019470'),
(30876, 'Skive Kommune', 1531, '82', 59, 'DK', 56.64478000, 8.97660000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q503108'),
(30877, 'Skjern', 1531, '82', 59, 'DK', 55.95000000, 8.50000000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2035962'),
(30878, 'Skovby', 1531, '82', 59, 'DK', 56.15672000, 9.94523000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2035962'),
(30879, 'Skælskør', 1528, '85', 59, 'DK', 55.25058000, 11.29352000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1668279'),
(30880, 'Skævinge', 1530, '84', 59, 'DK', 55.90785000, 12.15036000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q12342777'),
(30881, 'Skørping', 1532, '81', 59, 'DK', 56.83626000, 9.89255000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q3413549'),
(30882, 'Slagelse', 1528, '85', 59, 'DK', 55.40276000, 11.35459000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3414556'),
(30883, 'Slagelse Kommune', 1528, '85', 59, 'DK', 55.34546000, 11.33390000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q29927'),
(30884, 'Slangerup', 1530, '84', 59, 'DK', 55.85000000, 12.18333000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3617406'),
(30885, 'Smørumnedre', 1530, '84', 59, 'DK', 55.74232000, 12.30276000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1715139'),
(30886, 'Snejbjerg', 1531, '82', 59, 'DK', 56.13291000, 8.90353000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1715139'),
(30887, 'Snoghøj', 1529, '83', 59, 'DK', 55.52253000, 9.72125000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2253859'),
(30888, 'Solbjerg', 1531, '82', 59, 'DK', 56.04271000, 10.08631000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2884430'),
(30889, 'Solrød', 1528, '85', 59, 'DK', 55.53628000, 12.18270000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2884430'),
(30890, 'Solrød Kommune', 1528, '85', 59, 'DK', 55.53553000, 12.17337000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q499469'),
(30891, 'Solrød Strand', 1528, '85', 59, 'DK', 55.53285000, 12.22227000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2229515'),
(30892, 'Sorø', 1528, '85', 59, 'DK', 55.43184000, 11.55547000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q27858'),
(30893, 'Sorø Kommune', 1528, '85', 59, 'DK', 55.48268000, 11.55693000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q21151'),
(30894, 'Spentrup', 1531, '82', 59, 'DK', 56.53703000, 10.03792000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2403073'),
(30895, 'Starup', 1529, '83', 59, 'DK', 55.24097000, 9.53503000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q12336888'),
(30896, 'Stavnsholt', 1530, '84', 59, 'DK', 55.81479000, 12.40545000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q12336957'),
(30897, 'Stavtrup', 1531, '82', 59, 'DK', 56.13124000, 10.11987000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2107465'),
(30898, 'Stege', 1528, '85', 59, 'DK', 54.98704000, 12.28461000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2301524'),
(30899, 'Stenlille', 1528, '85', 59, 'DK', 55.53888000, 11.59120000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q603045'),
(30900, 'Stenløse', 1530, '84', 59, 'DK', 55.76828000, 12.19723000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q9345649'),
(30901, 'Stevns Kommune', 1528, '85', 59, 'DK', 55.33373000, 12.30692000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q503153'),
(30902, 'Stige', 1529, '83', 59, 'DK', 55.43941000, 10.40940000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q503153'),
(30903, 'Stilling', 1531, '82', 59, 'DK', 56.06224000, 9.98822000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2382537'),
(30904, 'Stoholm', 1531, '82', 59, 'DK', 56.48454000, 9.14617000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2311042'),
(30905, 'Store Heddinge', 1528, '85', 59, 'DK', 55.30965000, 12.38885000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1019126'),
(30906, 'Storvorde', 1532, '81', 59, 'DK', 57.00392000, 10.10125000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2349723'),
(30907, 'Strandby', 1532, '81', 59, 'DK', 57.49150000, 10.49609000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1999151'),
(30908, 'Strib', 1529, '83', 59, 'DK', 55.54021000, 9.76748000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1951945'),
(30909, 'Struer', 1531, '82', 59, 'DK', 56.49205000, 8.59397000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1797189'),
(30910, 'Struer Kommune', 1531, '82', 59, 'DK', 56.46667000, 8.58333000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q503227'),
(30911, 'Strøby Egede', 1528, '85', 59, 'DK', 55.41382000, 12.24502000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2357704'),
(30912, 'Stubbekøbing', 1528, '85', 59, 'DK', 54.88875000, 12.04102000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q507747'),
(30913, 'Støvring', 1532, '81', 59, 'DK', 56.88536000, 9.83839000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q3413440'),
(30914, 'Sundby', 1528, '85', 59, 'DK', 54.76711000, 11.84979000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2595635'),
(30915, 'Sunds', 1531, '82', 59, 'DK', 56.20743000, 9.01386000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2788609'),
(30916, 'Svebølle', 1528, '85', 59, 'DK', 55.65183000, 11.28658000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q4353560'),
(30917, 'Svejbæk', 1531, '82', 59, 'DK', 56.13255000, 9.63289000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2173126'),
(30918, 'Svendborg', 1529, '83', 59, 'DK', 55.05982000, 10.60677000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q28031'),
(30919, 'Svendborg Kommune', 1529, '83', 59, 'DK', 55.08410000, 10.61391000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21182'),
(30920, 'Svenstrup', 1532, '81', 59, 'DK', 56.97230000, 9.84806000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21182'),
(30921, 'Svinninge', 1528, '85', 59, 'DK', 55.72111000, 11.46547000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q7652712'),
(30922, 'Svogerslev', 1528, '85', 59, 'DK', 55.63423000, 12.01465000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2217489'),
(30923, 'Syddjurs Kommune', 1531, '82', 59, 'DK', 56.31250000, 10.52083000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q512581'),
(30924, 'Sæby', 1532, '81', 59, 'DK', 57.33188000, 10.52251000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q346802'),
(30925, 'Søften', 1531, '82', 59, 'DK', 56.23801000, 10.08510000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q1880810'),
(30926, 'Sønder Bjert', 1529, '83', 59, 'DK', 55.45272000, 9.56741000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2856489'),
(30927, 'Sønderborg', 1529, '83', 59, 'DK', 54.90896000, 9.78917000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q21179'),
(30928, 'Sønderborg Kommune', 1529, '83', 59, 'DK', 54.91667000, 9.80000000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q21108'),
(30929, 'Søndersø', 1529, '83', 59, 'DK', 55.48526000, 10.25540000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q3531267'),
(30930, 'Taastrup', 1530, '84', 59, 'DK', 55.65006000, 12.30160000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q20999'),
(30931, 'Tarm', 1531, '82', 59, 'DK', 55.90861000, 8.53041000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2582163'),
(30932, 'Taulov', 1529, '83', 59, 'DK', 55.54582000, 9.61553000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q4994751'),
(30933, 'Thisted', 1532, '81', 59, 'DK', 56.95523000, 8.69491000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1008152'),
(30934, 'Thisted Kommune', 1532, '81', 59, 'DK', 57.00397000, 8.61834000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q7715809'),
(30935, 'Thurø By', 1529, '83', 59, 'DK', 55.04740000, 10.66385000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q23734741'),
(30936, 'Thyborøn', 1531, '82', 59, 'DK', 56.69846000, 8.21238000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q165793'),
(30937, 'Tinglev', 1529, '83', 59, 'DK', 54.93788000, 9.25547000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3618891'),
(30938, 'Tjæreborg', 1529, '83', 59, 'DK', 55.46457000, 8.57968000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2339781'),
(30939, 'Toftlund', 1529, '83', 59, 'DK', 55.18858000, 9.06925000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2051188'),
(30940, 'Tommerup', 1529, '83', 59, 'DK', 55.31952000, 10.20659000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2702722'),
(30941, 'Tommerup Stationsby', 1529, '83', 59, 'DK', 55.34535000, 10.17594000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q4358920'),
(30942, 'Tranbjerg', 1531, '82', 59, 'DK', 56.09519000, 10.13605000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2329474'),
(30943, 'Trige', 1531, '82', 59, 'DK', 56.25291000, 10.14840000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2739196'),
(30944, 'Trørød', 1530, '84', 59, 'DK', 55.83946000, 12.54432000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q3432926'),
(30945, 'Tune', 1528, '85', 59, 'DK', 55.59287000, 12.16968000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2505467'),
(30946, 'Tårnby', 1530, '84', 59, 'DK', 55.63030000, 12.60035000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q28173'),
(30947, 'Tårnby Kommune', 1530, '84', 59, 'DK', 55.60391000, 12.59599000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q282496'),
(30948, 'Tårs', 1532, '81', 59, 'DK', 57.38333000, 10.11667000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2086166'),
(30949, 'Tølløse', 1528, '85', 59, 'DK', 55.61250000, 11.77034000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q7061097'),
(30950, 'Tønder', 1529, '83', 59, 'DK', 54.93306000, 8.86674000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q3223979'),
(30951, 'Tønder Kommune', 1529, '83', 59, 'DK', 55.07304000, 8.87214000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q3162907'),
(30952, 'Tørring', 1531, '82', 59, 'DK', 55.85000000, 9.48333000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2183058'),
(30953, 'Ulfborg', 1531, '82', 59, 'DK', 56.26725000, 8.32167000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2474915'),
(30954, 'Ullerslev', 1529, '83', 59, 'DK', 55.36172000, 10.65190000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3547139'),
(30955, 'Ulstrup', 1531, '82', 59, 'DK', 56.38994000, 9.79354000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1942974'),
(30956, 'Vadum', 1532, '81', 59, 'DK', 57.11790000, 9.85700000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2590975'),
(30957, 'Vallensbæk Strand', 1530, '84', 59, 'DK', 55.62199000, 12.38511000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2590975'),
(30958, 'Vallensbæk Kommune', 1530, '84', 59, 'DK', 55.63128000, 12.37369000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q514844'),
(30959, 'Vamdrup', 1529, '83', 59, 'DK', 55.42764000, 9.28435000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3547196'),
(30960, 'Vanløse', 1530, '84', 59, 'DK', 55.68361000, 12.48713000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q3547196'),
(30961, 'Varde', 1529, '83', 59, 'DK', 55.62112000, 8.48069000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2048321'),
(30962, 'Varde Kommune', 1529, '83', 59, 'DK', 55.61667000, 8.50000000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21142'),
(30963, 'Vejen', 1529, '83', 59, 'DK', 55.48117000, 9.13795000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1016313'),
(30964, 'Vejen Kommune', 1529, '83', 59, 'DK', 55.46312000, 9.05004000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21150'),
(30965, 'Vejle', 1529, '83', 59, 'DK', 55.70927000, 9.53570000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q27116'),
(30966, 'Vejle Kommune', 1529, '83', 59, 'DK', 55.74874000, 9.40421000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q28150'),
(30967, 'Veksø', 1530, '84', 59, 'DK', 55.75473000, 12.23837000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q7918750'),
(30968, 'Vestbjerg', 1532, '81', 59, 'DK', 57.13166000, 9.95942000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2458635'),
(30969, 'Vester Hassing', 1532, '81', 59, 'DK', 57.06767000, 10.12558000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1015392'),
(30970, 'Vester-Skerninge', 1529, '83', 59, 'DK', 55.07322000, 10.45536000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3386709'),
(30971, 'Vesthimmerland Kommune', 1532, '81', 59, 'DK', 56.80000000, 9.37083000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q511975'),
(30972, 'Viborg', 1531, '82', 59, 'DK', 56.45319000, 9.40201000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q21176'),
(30973, 'Viborg Kommune', 1531, '82', 59, 'DK', 56.45000000, 9.36667000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q502962'),
(30974, 'Viby', 1528, '85', 59, 'DK', 55.54872000, 12.02391000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q12340905'),
(30975, 'Videbæk', 1531, '82', 59, 'DK', 56.08760000, 8.62852000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q12343916'),
(30976, 'Vildbjerg', 1531, '82', 59, 'DK', 56.20000000, 8.76667000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2693795'),
(30977, 'Vindeby', 1529, '83', 59, 'DK', 55.04489000, 10.61309000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1286001'),
(30978, 'Vinderup', 1531, '82', 59, 'DK', 56.48176000, 8.77991000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3591036'),
(30979, 'Vindinge', 1528, '85', 59, 'DK', 55.62298000, 12.13870000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q16325016'),
(30980, 'Vipperød', 1528, '85', 59, 'DK', 55.66768000, 11.73967000, '2019-10-05 22:45:16', '2020-05-01 17:22:42', 1, 'Q2481211'),
(30981, 'Virklund', 1531, '82', 59, 'DK', 56.13218000, 9.55582000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2441845'),
(30982, 'Vissenbjerg', 1529, '83', 59, 'DK', 55.38482000, 10.13784000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q3547096'),
(30983, 'Vodskov', 1532, '81', 59, 'DK', 57.10854000, 10.02215000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2205752'),
(30984, 'Vojens', 1529, '83', 59, 'DK', 55.24639000, 9.30603000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q1645444'),
(30985, 'Vordingborg', 1528, '85', 59, 'DK', 55.00801000, 11.91057000, '2019-10-05 22:45:16', '2019-10-05 22:45:16', 1, 'Q2608258'),
(30986, 'Vordingborg Kommune', 1528, '85', 59, 'DK', 55.01383000, 12.10008000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q183572'),
(30987, 'Vrå', 1532, '81', 59, 'DK', 57.35370000, 9.94176000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q912141'),
(30988, 'Værløse', 1530, '84', 59, 'DK', 55.78251000, 12.36856000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q3171395'),
(30989, 'Åbybro', 1532, '81', 59, 'DK', 57.16249000, 9.72996000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q3171757'),
(30990, 'Åkirkeby', 1530, '84', 59, 'DK', 55.07080000, 14.91978000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q259969'),
(30991, 'Ålborg Kommune', 1532, '81', 59, 'DK', 57.00000000, 9.95000000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q300856'),
(30992, 'Ålestrup', 1532, '81', 59, 'DK', 56.69470000, 9.49336000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q300888'),
(30993, 'Aarhus', 1531, '82', 59, 'DK', 56.15674000, 10.21076000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q25319'),
(30994, 'Aarhus Kommune', 1531, '82', 59, 'DK', 56.16317000, 10.16897000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q240262'),
(30995, 'Årslev', 1529, '83', 59, 'DK', 55.30353000, 10.46428000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q3624563'),
(30996, 'Årup', 1529, '83', 59, 'DK', 55.37315000, 10.04131000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q3405593'),
(30997, 'Ærø Kommune', 1529, '83', 59, 'DK', 54.85833000, 10.43333000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q21104'),
(30998, 'Ærøskøbing', 1529, '83', 59, 'DK', 54.88803000, 10.41117000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q272129'),
(30999, 'Ølgod', 1529, '83', 59, 'DK', 55.80682000, 8.62859000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q308106'),
(31000, 'Ølstykke', 1530, '84', 59, 'DK', 55.79567000, 12.15509000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q308106'),
(31001, 'Ørslev', 1528, '85', 59, 'DK', 55.04356000, 11.96792000, '2019-10-05 22:45:17', '2020-05-01 17:22:42', 1, 'Q308256'),
(31002, 'Berekua', 4080, '09', 61, 'DM', 15.23333000, -61.31667000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q1136090'),
(31003, 'Calibishie', 4082, '02', 61, 'DM', 15.59297000, -61.34901000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q2933944'),
(31004, 'Castle Bruce', 4078, '03', 61, 'DM', 15.44397000, -61.25723000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q1049398'),
(31005, 'Colihaut', 4081, '11', 61, 'DM', 15.48478000, -61.46215000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q1108306'),
(31006, 'La Plaine', 4080, '09', 61, 'DM', 15.32768000, -61.24753000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q3174970'),
(31007, 'Mahaut', 4084, '10', 61, 'DM', 15.36357000, -61.39701000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q3052683'),
(31008, 'Marigot', 4082, '02', 61, 'DM', 15.53886000, -61.28375000, '2019-10-05 22:45:17', '2019-10-05 22:45:17', 1, 'Q3052620'),
(31009, 'Pointe Michel', 4083, '07', 61, 'DM', 15.25976000, -61.37452000, '2019-10-05 22:45:18', '2019-10-05 22:45:18', 1, 'Q1934011'),
(31010, 'Pont Cassé', 4084, '10', 61, 'DM', 15.36667000, -61.35000000, '2019-10-05 22:45:18', '2020-05-01 17:22:42', 1, 'Q1972895'),
(31011, 'Portsmouth', 4076, '05', 61, 'DM', 15.58333000, -61.46667000, '2019-10-05 22:45:19', '2019-10-05 22:45:19', 1, 'Q1014235'),
(31012, 'Rosalie', 4078, '03', 61, 'DM', 15.36667000, -61.26667000, '2019-10-05 22:45:19', '2019-10-05 22:45:19', 1, 'Q976314'),
(31013, 'Roseau', 4079, '04', 61, 'DM', 15.30174000, -61.38808000, '2019-10-05 22:45:20', '2019-10-05 22:45:20', 1, 'Q36281'),
(31014, 'Saint Joseph', 4085, '06', 61, 'DM', 15.40000000, -61.43333000, '2019-10-05 22:45:21', '2019-10-05 22:45:21', 1, 'Q1021646'),
(31015, 'Salisbury', 4085, '06', 61, 'DM', 15.43689000, -61.43637000, '2019-10-05 22:45:21', '2019-10-05 22:45:21', 1, 'Q3555054'),
(31016, 'Soufrière', 4077, '08', 61, 'DM', 15.23374000, -61.35881000, '2019-10-05 22:45:21', '2020-05-01 17:22:42', 1, 'Q1027850'),
(31017, 'Wesley', 4082, '02', 61, 'DM', 15.56667000, -61.31667000, '2019-10-05 22:45:21', '2019-10-05 22:45:21', 1, 'Q595272'),
(31018, 'Woodford Hill', 4082, '02', 61, 'DM', 15.58093000, -61.33149000, '2019-10-05 22:45:21', '2019-10-05 22:45:21', 1, 'Q3098466'),
(31019, 'Agua Santa del Yuna', 4113, '06', 62, 'DO', 19.15072000, -69.80069000, '2019-10-05 22:45:21', '2019-10-05 22:45:21', 1, 'Q4694455'),
(31020, 'Altamira', 4092, '18', 62, 'DO', 19.70000000, -70.83333000, '2019-10-05 22:45:21', '2019-10-05 22:45:21', 1, 'Q23779605'),
(31021, 'Amina', 4104, '27', 62, 'DO', 19.54813000, -70.99599000, '2019-10-05 22:45:21', '2019-10-05 22:45:21', 1, 'Q23779645'),
(31022, 'Arenoso', 4113, '06', 62, 'DO', 19.18732000, -69.85917000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q3622180'),
(31023, 'Arroyo Salado', 4094, '14', 62, 'DO', 19.50000000, -69.90000000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q4796250'),
(31024, 'Azua', 4114, '02', 62, 'DO', 18.45319000, -70.73490000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q1836680'),
(31025, 'Baitoa', 4108, '25', 62, 'DO', 19.32512000, -70.70357000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q4848740'),
(31026, 'Bajos de Haina', 4091, '21', 62, 'DO', 18.41667000, -70.03333000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q2438383'),
(31027, 'Baní', 4096, '17', 62, 'DO', 18.27964000, -70.33185000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q1902349'),
(31028, 'Bayaguana', 4111, '29', 62, 'DO', 18.78333000, -69.60000000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q1770505'),
(31029, 'Bella Vista', 4095, '01', 62, 'DO', 18.45539000, -69.94540000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q4883376'),
(31030, 'Bisonó', 4108, '25', 62, 'DO', 19.58333000, -70.86667000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q2690547'),
(31031, 'Boca Chica', 4093, '32', 62, 'DO', 18.45000000, -69.60000000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q888935'),
(31032, 'Boca de Yuma', 4109, '11', 62, 'DO', 18.37825000, -68.60900000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q4936128'),
(31033, 'Bohechío', 4098, '22', 62, 'DO', 18.77515000, -70.98889000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q23778512'),
(31034, 'Bonao', 4099, '28', 62, 'DO', 18.91667000, -70.46667000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q2306136'),
(31035, 'Cabarete', 4092, '18', 62, 'DO', 19.74982000, -70.40829000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q459919'),
(31036, 'Cabral', 4090, '04', 62, 'DO', 18.19991000, -71.24660000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q1770655'),
(31037, 'Cabrera', 4094, '14', 62, 'DO', 19.64260000, -69.90489000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q23778187'),
(31038, 'Cachón', 4090, '04', 62, 'DO', 18.24833000, -71.18912000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q5350853'),
(31039, 'Cambita Garabitos', 4091, '21', 62, 'DO', 18.50000000, -70.23333000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q3651189'),
(31040, 'Cana Chapetón', 4115, '15', 62, 'DO', 19.60703000, -71.25734000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q5029151'),
(31041, 'Canoa', 4090, '04', 62, 'DO', 18.35499000, -71.15851000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q23777833'),
(31042, 'Castañuelas', 4115, '15', 62, 'DO', 19.71387000, -71.49876000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q519050'),
(31043, 'Castillo', 4113, '06', 62, 'DO', 19.25000000, -70.00000000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q3663066'),
(31044, 'Cayetano Germosén', 4102, '09', 62, 'DO', 19.34285000, -70.47573000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q3664033'),
(31045, 'Cercado Abajo', 4098, '22', 62, 'DO', 18.72681000, -71.51742000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q23777178'),
(31046, 'Cevicos', 4088, '24', 62, 'DO', 19.00449000, -69.97896000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q3665682'),
(31047, 'Ciudad Nueva', 4095, '01', 62, 'DO', 18.46707000, -69.89339000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q14932551'),
(31048, 'Concepción de La Vega', 4116, '13', 62, 'DO', 19.22207000, -70.52956000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q538953'),
(31049, 'Constanza', 4116, '13', 62, 'DO', 18.90919000, -70.74499000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q459801'),
(31050, 'Cotuí', 4088, '24', 62, 'DO', 19.05272000, -70.14939000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q2087489'),
(31051, 'Cristo Rey', 4095, '01', 62, 'DO', 18.50000000, -69.93333000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q5186441'),
(31052, 'Cristóbal', 4097, '10', 62, 'DO', 18.29405000, -71.29298000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q23776103'),
(31053, 'Dajabón', 4107, '05', 62, 'DO', 19.54878000, -71.70829000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q1969407'),
(31054, 'Don Juan', 4111, '29', 62, 'DO', 18.82774000, -69.94629000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q5292861'),
(31055, 'Duvergé', 4097, '10', 62, 'DO', 18.31634000, -71.59451000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q3716731'),
(31056, 'El Cacao', 4091, '21', 62, 'DO', 18.52719000, -70.29585000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q23775949'),
(31057, 'El Carril', 4091, '21', 62, 'DO', 18.44905000, -70.02785000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q5350946'),
(31058, 'El Cercado', 4098, '22', 62, 'DO', 18.70000000, -71.46667000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q5350946'),
(31059, 'El Factor', 4094, '14', 62, 'DO', 19.31834000, -69.88827000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q3720911'),
(31060, 'El Guayabal', 4114, '02', 62, 'DO', 18.74960000, -70.83690000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q984301'),
(31061, 'El Palmar', 4105, '03', 62, 'DO', 18.41139000, -71.24558000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q23774454'),
(31062, 'El Peñón', 4090, '04', 62, 'DO', 18.29643000, -71.18410000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q1770893'),
(31063, 'El Pino', 4107, '05', 62, 'DO', 19.43537000, -71.47540000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q3720977'),
(31064, 'El Puerto', 4110, '23', 62, 'DO', 18.78333000, -69.46667000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q23774554'),
(31065, 'El Valle', 4106, '30', 62, 'DO', 18.93333000, -69.38333000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q1770328'),
(31066, 'Enriquillo', 4090, '04', 62, 'DO', 17.91667000, -71.25000000, '2019-10-05 22:45:22', '2019-10-05 22:45:22', 1, 'Q2433632'),
(31067, 'Ensanche Luperón', 4095, '01', 62, 'DO', 18.50000000, -69.90000000, '2019-10-05 22:45:22', '2020-05-01 17:22:42', 1, 'Q5379909'),
(31068, 'Esperalvillo', 4111, '29', 62, 'DO', 18.81509000, -70.03557000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3733090'),
(31069, 'Esperanza', 4104, '27', 62, 'DO', 19.62379000, -70.97141000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q1770079'),
(31070, 'Estebanía', 4114, '02', 62, 'DO', 18.45770000, -70.64350000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q3733396'),
(31071, 'Estero Hondo', 4092, '18', 62, 'DO', 19.82712000, -71.17411000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q5401028'),
(31072, 'Fantino', 4088, '24', 62, 'DO', 19.11667000, -70.30000000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q23774194'),
(31073, 'Fundación', 4090, '04', 62, 'DO', 18.28668000, -71.18147000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q3754387'),
(31074, 'Galván', 4105, '03', 62, 'DO', 18.50228000, -71.34271000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q3757889'),
(31075, 'Gaspar Hernández', 4102, '09', 62, 'DO', 19.62748000, -70.27772000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q3758618'),
(31076, 'Gonzalo', 4111, '29', 62, 'DO', 18.95147000, -69.75114000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q23773798'),
(31077, 'Guananico', 4092, '18', 62, 'DO', 19.72693000, -70.92294000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3778171'),
(31078, 'Guatapanal', 4104, '27', 62, 'DO', 19.50705000, -70.91713000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q5614204'),
(31079, 'Guayabal', 4097, '10', 62, 'DO', 18.59810000, -71.64184000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q5614357'),
(31080, 'Guayabo Dulce', 4106, '30', 62, 'DO', 18.65000000, -69.28333000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q5614361'),
(31081, 'Guaymate', 4087, '12', 62, 'DO', 18.58793000, -68.97867000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q628537'),
(31082, 'Guayubín', 4115, '15', 62, 'DO', 19.61667000, -71.33333000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q628537'),
(31083, 'Hatillo Palma', 4115, '15', 62, 'DO', 19.66256000, -71.19406000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q5681615'),
(31084, 'Hato Mayor del Rey', 4106, '30', 62, 'DO', 18.76278000, -69.25681000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q2427660'),
(31085, 'Higüey', 4109, '11', 62, 'DO', 18.70000000, -68.66667000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q2427660'),
(31086, 'Hostos', 4113, '06', 62, 'DO', 19.18043000, -70.02046000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q14932517'),
(31087, 'Imbert', 4092, '18', 62, 'DO', 19.75371000, -70.82906000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3796589'),
(31088, 'Jaibón', 4104, '27', 62, 'DO', 19.61169000, -71.14847000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q23770722'),
(31089, 'Jamao al Norte', 4102, '09', 62, 'DO', 19.63552000, -70.44664000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3806431'),
(31090, 'Jaquimeyes', 4090, '04', 62, 'DO', 18.31173000, -71.16145000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3807300'),
(31091, 'Jarabacoa', 4116, '13', 62, 'DO', 19.11683000, -70.63595000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q990182'),
(31092, 'Jicomé', 4104, '27', 62, 'DO', 19.64955000, -70.94927000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q6192099'),
(31093, 'Jima Abajo', 4116, '13', 62, 'DO', 19.13292000, -70.37607000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3808393'),
(31094, 'Jimaní', 4097, '10', 62, 'DO', 18.49169000, -71.85022000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q1409818'),
(31095, 'Joba Arriba', 4102, '09', 62, 'DO', 19.56667000, -70.26667000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q6206763'),
(31096, 'Juan Adrián', 4099, '28', 62, 'DO', 18.76365000, -70.33732000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q6298761'),
(31097, 'Juan López Abajo', 4102, '09', 62, 'DO', 19.43333000, -70.50000000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q6298761'),
(31098, 'Juan de Herrera', 4098, '22', 62, 'DO', 18.87402000, -71.23830000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3810939'),
(31099, 'Juancho', 4101, '16', 62, 'DO', 17.85782000, -71.29311000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q6301488'),
(31100, 'Juncalito Abajo', 4108, '25', 62, 'DO', 19.21990000, -70.81905000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q5637332'),
(31101, 'La Agustina', 4095, '01', 62, 'DO', 18.50000000, -69.93333000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q6461013'),
(31102, 'La Canela', 4108, '25', 62, 'DO', 19.47341000, -70.81629000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q23770192'),
(31103, 'La Caya', 4104, '27', 62, 'DO', 19.69981000, -71.12084000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q6461598'),
(31104, 'La Ciénaga', 4090, '04', 62, 'DO', 18.06858000, -71.10651000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q3820737'),
(31105, 'La Descubierta', 4097, '10', 62, 'DO', 18.57053000, -71.72967000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q3820813'),
(31106, 'La Entrada', 4094, '14', 62, 'DO', 19.55367000, -69.90762000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q23769735'),
(31107, 'La Julia', 4095, '01', 62, 'DO', 18.46667000, -69.93333000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q6463278'),
(31108, 'La Romana', 4087, '12', 62, 'DO', 18.42733000, -68.97285000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q40508'),
(31109, 'La Uvilla', 4105, '03', 62, 'DO', 18.36186000, -71.21046000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q7876527'),
(31110, 'Laguna Salada', 4104, '27', 62, 'DO', 19.70000000, -71.13333000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q1770074'),
(31111, 'Las Charcas', 4114, '02', 62, 'DO', 18.45026000, -70.61724000, '2019-10-05 22:45:23', '2019-10-05 22:45:23', 1, 'Q676874'),
(31112, 'Las Guáranas', 4113, '06', 62, 'DO', 19.19310000, -70.20835000, '2019-10-05 22:45:23', '2020-05-01 17:22:42', 1, 'Q3827243'),
(31113, 'Las Matas de Farfán', 4098, '22', 62, 'DO', 18.91667000, -71.50000000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q23767955'),
(31114, 'Las Matas de Santa Cruz', 4115, '15', 62, 'DO', 19.67119000, -71.50471000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3827245'),
(31115, 'Las Salinas', 4090, '04', 62, 'DO', 18.27485000, -71.31596000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3827252'),
(31116, 'Las Terrenas', 4103, '20', 62, 'DO', 19.31102000, -69.54280000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1806486'),
(31117, 'Licey al Medio', 4108, '25', 62, 'DO', 19.45000000, -70.60000000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1806486'),
(31118, 'Loma de Cabrera', 4107, '05', 62, 'DO', 19.41667000, -71.58333000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1771467'),
(31119, 'Los Botados', 4111, '29', 62, 'DO', 18.73226000, -69.99536000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q6682531'),
(31120, 'Los Hidalgos', 4092, '18', 62, 'DO', 19.73333000, -71.03333000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q518820'),
(31121, 'Los Llanos', 4110, '23', 62, 'DO', 18.62035000, -69.49581000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3947425'),
(31122, 'Los Ríos', 4105, '03', 62, 'DO', 18.52131000, -71.59106000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3837408'),
(31123, 'Luperón', 4092, '18', 62, 'DO', 19.89131000, -70.96204000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3840823'),
(31124, 'Maimón', 4099, '28', 62, 'DO', 18.88333000, -70.30000000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3843528'),
(31125, 'Maizal', 4104, '27', 62, 'DO', 19.63655000, -71.02734000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q6737246'),
(31126, 'Majagual', 4111, '29', 62, 'DO', 19.04134000, -69.83616000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q6737401'),
(31127, 'Mao', 4104, '27', 62, 'DO', 19.55186000, -71.07813000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q692560'),
(31128, 'Matanzas', 4096, '17', 62, 'DO', 18.24297000, -70.41768000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q23765292'),
(31129, 'Matayaya', 4098, '22', 62, 'DO', 18.89036000, -71.59459000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q15251099'),
(31130, 'Mella', 4097, '10', 62, 'DO', 18.35871000, -71.41716000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3854258'),
(31131, 'Miches', 4086, '08', 62, 'DO', 18.98364000, -69.04760000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1770668'),
(31132, 'Moca', 4102, '09', 62, 'DO', 19.50000000, -70.50000000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1241374'),
(31133, 'Monción', 4100, '26', 62, 'DO', 19.46667000, -71.16667000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3307947'),
(31134, 'Monte Cristi', 4115, '15', 62, 'DO', 19.83333000, -71.61667000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q2130701'),
(31135, 'Monte Llano', 4092, '18', 62, 'DO', 19.73460000, -70.59915000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q2130701'),
(31136, 'Monte Plata', 4111, '29', 62, 'DO', 18.80700000, -69.78399000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q634487'),
(31137, 'Nagua', 4094, '14', 62, 'DO', 19.38320000, -69.84740000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q2306984'),
(31138, 'Neiba', 4105, '03', 62, 'DO', 18.48137000, -71.41965000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q2107258'),
(31139, 'Nizao', 4096, '17', 62, 'DO', 18.24697000, -70.21053000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3877399'),
(31140, 'Otra Banda', 4109, '11', 62, 'DO', 18.65017000, -68.66281000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7109031'),
(31141, 'Oviedo', 4101, '16', 62, 'DO', 17.80136000, -71.40100000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1770802'),
(31142, 'Padre Las Casas', 4114, '02', 62, 'DO', 18.75000000, -70.88333000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q966782'),
(31143, 'Palmar Arriba', 4108, '25', 62, 'DO', 19.53957000, -70.73826000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7128130'),
(31144, 'Palmar de Ocoa', 4114, '02', 62, 'DO', 18.29656000, -70.58635000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7128144'),
(31145, 'Paraíso', 4090, '04', 62, 'DO', 18.02652000, -71.20889000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q1988637'),
(31146, 'Partido', 4107, '05', 62, 'DO', 19.48403000, -71.54730000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3896653'),
(31147, 'Paya', 4096, '17', 62, 'DO', 18.26196000, -70.29560000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q23764171'),
(31148, 'Pedernales', 4101, '16', 62, 'DO', 18.08333000, -71.60000000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q23764182'),
(31149, 'Pedro Corto', 4098, '22', 62, 'DO', 18.84856000, -71.41041000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7159534'),
(31150, 'Pedro García', 4108, '25', 62, 'DO', 19.59202000, -70.65256000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q23764196'),
(31151, 'Pedro Sánchez', 4086, '08', 62, 'DO', 18.86375000, -69.10868000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q7159964'),
(31152, 'Pepillo Salcedo', 4115, '15', 62, 'DO', 19.66667000, -71.66667000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3899368'),
(31153, 'Peralta', 4114, '02', 62, 'DO', 18.58164000, -70.77029000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3899640'),
(31154, 'Pescadería', 4090, '04', 62, 'DO', 18.26766000, -71.16612000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q7171336'),
(31155, 'Piedra Blanca', 4099, '28', 62, 'DO', 18.84431000, -70.31688000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1770214'),
(31156, 'Pimentel', 4113, '06', 62, 'DO', 19.21667000, -70.16667000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3905064'),
(31157, 'Pizarrete', 4096, '17', 62, 'DO', 18.29935000, -70.22648000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7199936'),
(31158, 'Polo', 4090, '04', 62, 'DO', 18.07873000, -71.28723000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1770610'),
(31159, 'Postrer Río', 4097, '10', 62, 'DO', 18.54374000, -71.63561000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3909478'),
(31160, 'Pueblo Viejo', 4114, '02', 62, 'DO', 18.40000000, -70.76765000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3925382'),
(31161, 'Puerto Plata', 4092, '18', 62, 'DO', 19.75119000, -70.70251000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q988413'),
(31162, 'Punta Cana', 4109, '11', 62, 'DO', 18.58182000, -68.40431000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1568095'),
(31163, 'Quisqueya', 4110, '23', 62, 'DO', 18.55542000, -69.40814000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3928010'),
(31164, 'Ramón Santana', 4110, '23', 62, 'DO', 18.54224000, -69.17979000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3929979'),
(31165, 'Restauración', 4107, '05', 62, 'DO', 19.31532000, -71.69239000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q1770897'),
(31166, 'Rincón', 4116, '13', 62, 'DO', 19.11938000, -70.40632000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q23763422'),
(31167, 'Río Grande', 4092, '18', 62, 'DO', 19.66667000, -70.76667000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q7386283'),
(31168, 'Río San Juan', 4094, '14', 62, 'DO', 19.55022000, -70.07703000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3943218'),
(31169, 'Río Verde Arriba', 4116, '13', 62, 'DO', 19.31583000, -70.56643000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q7386429'),
(31170, 'Sabana Buey', 4096, '17', 62, 'DO', 18.27351000, -70.52352000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7395869'),
(31171, 'Sabana Grande de Boyá', 4111, '29', 62, 'DO', 18.94498000, -69.79331000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q23762283'),
(31172, 'Sabana Grande de Palenque', 4091, '21', 62, 'DO', 18.26256000, -70.14821000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3944201'),
(31173, 'Sabana Iglesia', 4108, '25', 62, 'DO', 19.32114000, -70.75992000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3944203'),
(31174, 'Sabana Yegua', 4114, '02', 62, 'DO', 18.71667000, -71.01667000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q23762304'),
(31175, 'Sabana de la Mar', 4106, '30', 62, 'DO', 19.03333000, -69.41667000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q23762304'),
(31176, 'Sabana del Puerto', 4099, '28', 62, 'DO', 19.06667000, -70.41667000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7395875'),
(31177, 'Sabaneta', 4100, '26', 62, 'DO', 19.47793000, -71.34125000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q7395875'),
(31178, 'Salcedo', 4089, '19', 62, 'DO', 19.41667000, -70.38333000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q1023951'),
(31179, 'Salsipuedes', 4089, '19', 62, 'DO', 19.40552000, -70.37985000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q23762401'),
(31180, 'Salvaleón de Higüey', 4109, '11', 62, 'DO', 18.61501000, -68.70798000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q1020261'),
(31181, 'Samaná', 4103, '20', 62, 'DO', 19.20561000, -69.33685000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q579950'),
(31182, 'San Carlos', 4095, '01', 62, 'DO', 18.48333000, -69.90000000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q6118516'),
(31183, 'San Cristóbal', 4091, '21', 62, 'DO', 18.41667000, -70.13333000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q373759'),
(31184, 'San Fernando de Monte Cristi', 4115, '15', 62, 'DO', 19.84826000, -71.64597000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q373759'),
(31185, 'San Francisco de Macorís', 4113, '06', 62, 'DO', 19.30099000, -70.25259000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q681999'),
(31186, 'San Gregorio de Nigua', 4091, '21', 62, 'DO', 18.38333000, -70.08333000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q3947356'),
(31187, 'San Ignacio de Sabaneta', 4100, '26', 62, 'DO', 19.38333000, -71.35000000, '2019-10-05 22:45:24', '2019-10-05 22:45:24', 1, 'Q2021983'),
(31188, 'San José de Las Matas', 4108, '25', 62, 'DO', 19.33915000, -70.93819000, '2019-10-05 22:45:24', '2020-05-01 17:22:42', 1, 'Q3947426'),
(31189, 'San José de Ocoa', 4112, '31', 62, 'DO', 18.54661000, -70.50631000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q2119082'),
(31190, 'San Juan de la Maguana', 4098, '22', 62, 'DO', 18.80588000, -71.22991000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q2430320'),
(31191, 'San Pedro de Macorís', 4110, '23', 62, 'DO', 18.45390000, -69.30864000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q680596'),
(31192, 'San Rafael del Yuma', 4109, '11', 62, 'DO', 18.42993000, -68.67390000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q649483'),
(31193, 'San Víctor Arriba', 4102, '09', 62, 'DO', 19.47741000, -70.53585000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q23761180'),
(31194, 'Santa Cruz de Barahona', 4090, '04', 62, 'DO', 18.20854000, -71.10077000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q958586'),
(31195, 'Santa Cruz de El Seibo', 4086, '08', 62, 'DO', 18.76559000, -69.03886000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q2420339'),
(31196, 'Santiago de los Caballeros', 4108, '25', 62, 'DO', 19.45170000, -70.69703000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q42763'),
(31197, 'Santo Domingo', 4095, '01', 62, 'DO', 18.47186000, -69.89232000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q34820'),
(31198, 'Santo Domingo Este', 4093, '32', 62, 'DO', 18.48847000, -69.85707000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q2713354'),
(31199, 'Santo Domingo Oeste', 4093, '32', 62, 'DO', 18.50000000, -70.00000000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q3949488'),
(31200, 'Santo Tomás de Jánico', 4108, '25', 62, 'DO', 19.23528000, -70.79515000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q3949488'),
(31201, 'Sosúa', 4092, '18', 62, 'DO', 19.75220000, -70.51995000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q1639488'),
(31202, 'Sánchez', 4103, '20', 62, 'DO', 19.22810000, -69.61370000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q23761049'),
(31203, 'Tamayo', 4105, '03', 62, 'DO', 18.50000000, -71.16667000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q32750457'),
(31204, 'Tamboril', 4108, '25', 62, 'DO', 19.48538000, -70.61104000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q23760505'),
(31205, 'Tenares', 4089, '19', 62, 'DO', 19.37439000, -70.35087000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q1770877'),
(31206, 'Tireo Arriba', 4116, '13', 62, 'DO', 18.93537000, -70.68850000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q23760139'),
(31207, 'Tábara Arriba', 4114, '02', 62, 'DO', 18.56999000, -70.87978000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q1770884'),
(31208, 'Vallejuelo', 4098, '22', 62, 'DO', 18.65354000, -71.33431000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q4008204'),
(31209, 'Veragua Arriba', 4102, '09', 62, 'DO', 19.58333000, -70.33333000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q4008204'),
(31210, 'Vicente Noble', 4090, '04', 62, 'DO', 18.38443000, -71.18009000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q1770645'),
(31211, 'Villa Altagracia', 4091, '21', 62, 'DO', 18.63333000, -70.25000000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q4011765'),
(31212, 'Villa Bisonó', 4108, '25', 62, 'DO', 19.56378000, -70.87582000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q23756782'),
(31213, 'Villa Consuelo', 4095, '01', 62, 'DO', 18.48333000, -69.90000000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q7930297'),
(31214, 'Villa Elisa', 4115, '15', 62, 'DO', 19.68560000, -71.27007000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q7930310'),
(31215, 'Villa Francisca', 4095, '01', 62, 'DO', 18.48278000, -69.88914000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q7930325'),
(31216, 'Villa González', 4108, '25', 62, 'DO', 19.54057000, -70.78853000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q4012077'),
(31217, 'Villa Isabela', 4092, '18', 62, 'DO', 19.81564000, -71.06056000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q4012142'),
(31218, 'Villa Jaragua', 4105, '03', 62, 'DO', 18.49077000, -71.48377000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q4012143'),
(31219, 'Villa Riva', 4113, '06', 62, 'DO', 19.15051000, -69.88370000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q949768'),
(31220, 'Villa Tapia', 4089, '19', 62, 'DO', 19.30084000, -70.42199000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q1770889'),
(31221, 'Villa Vásquez', 4115, '15', 62, 'DO', 19.80791000, -71.44000000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q1770880'),
(31222, 'Villarpando', 4114, '02', 62, 'DO', 18.65860000, -71.03916000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q7930938'),
(31223, 'Yaguate', 4091, '21', 62, 'DO', 18.33333000, -70.18333000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q7930938'),
(31224, 'Yamasá', 4111, '29', 62, 'DO', 18.77315000, -70.02583000, '2019-10-05 22:45:25', '2020-05-01 17:22:42', 1, 'Q4022462'),
(31225, 'Yayas de Viajama', 4114, '02', 62, 'DO', 18.60710000, -70.92753000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q3827307'),
(31226, 'Abou el Hassan', 1105, '02', 4, 'DZ', 36.41657000, 1.19616000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q2821729'),
(31227, 'Adrar', 1118, '01', 4, 'DZ', 27.87429000, -0.29388000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q251181'),
(31228, 'Aflou', 1138, '03', 4, 'DZ', 34.11279000, 2.10228000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q500560'),
(31229, 'Akbou', 1128, '06', 4, 'DZ', 36.45750000, 4.53494000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q2828968'),
(31230, 'Algiers', 1144, '16', 4, 'DZ', 36.73225000, 3.08746000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q3561'),
(31231, 'Amizour', 1128, '06', 4, 'DZ', 36.64022000, 4.90131000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q2214578'),
(31232, 'Ammi Moussa', 1130, '48', 4, 'DZ', 35.86781000, 1.11143000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q4747238'),
(31233, 'Annaba', 1103, '23', 4, 'DZ', 36.90000000, 7.76667000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q45942'),
(31234, 'Aoulef', 1118, '01', 4, 'DZ', 26.96667000, 1.08333000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q602548'),
(31235, 'Arbatache', 1125, '35', 4, 'DZ', 36.63773000, 3.37127000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q23824843'),
(31236, 'Arhribs', 1131, '15', 4, 'DZ', 36.79361000, 4.31158000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q1648638'),
(31237, 'Arris', 1142, '05', 4, 'DZ', 35.25881000, 6.34706000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q1388056'),
(31238, 'Azazga', 1131, '15', 4, 'DZ', 36.74472000, 4.37222000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q2875118'),
(31239, 'Azzaba', 1110, '21', 4, 'DZ', 36.73944000, 7.10528000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q23823384'),
(31240, 'Aïn Arnat', 1141, '19', 4, 'DZ', 36.18683000, 5.31347000, '2019-10-05 22:45:25', '2020-05-01 17:22:33', 1, 'Q2876253'),
(31241, 'Aïn Bessem', 1104, '10', 4, 'DZ', 36.29333000, 3.67319000, '2019-10-05 22:45:25', '2020-05-01 17:22:32', 1, 'Q2876271'),
(31242, 'Aïn Beïda', 1136, '04', 4, 'DZ', 35.79639000, 7.39278000, '2019-10-05 22:45:25', '2020-05-01 17:22:33', 1, 'Q2088349'),
(31243, 'Aïn Defla', 1119, '44', 4, 'DZ', 36.26405000, 1.96790000, '2019-10-05 22:45:25', '2020-05-01 17:22:32', 1, 'Q168846'),
(31244, 'Aïn Fakroun', 1136, '04', 4, 'DZ', 35.97108000, 6.87374000, '2019-10-05 22:45:25', '2020-05-01 17:22:33', 1, 'Q2876323'),
(31245, 'Aïn Kercha', 1136, '04', 4, 'DZ', 35.92472000, 6.69528000, '2019-10-05 22:45:25', '2020-05-01 17:22:33', 1, 'Q593739'),
(31246, 'Aïn Oussera', 1098, '17', 4, 'DZ', 35.45139000, 2.90583000, '2019-10-05 22:45:25', '2020-05-01 17:22:32', 1, 'Q651497'),
(31247, 'Aïn Sefra', 1102, '45', 4, 'DZ', 32.75000000, -0.58333000, '2019-10-05 22:45:25', '2020-05-01 17:22:33', 1, 'Q2745712'),
(31248, 'Aïn Smara', 1121, '25', 4, 'DZ', 36.26740000, 6.50135000, '2019-10-05 22:45:25', '2020-05-01 17:22:32', 1, 'Q2876376'),
(31249, 'Aïn Taya', 1144, '16', 4, 'DZ', 36.79333000, 3.28694000, '2019-10-05 22:45:25', '2020-05-01 17:22:32', 1, 'Q2876387'),
(31250, 'Aïn Temouchent', 1122, '46', 4, 'DZ', 35.29749000, -1.14037000, '2019-10-05 22:45:25', '2020-05-01 17:22:32', 1, 'Q2876387'),
(31251, 'Aïn Touta', 1142, '05', 4, 'DZ', 35.37675000, 5.90001000, '2019-10-05 22:45:25', '2020-05-01 17:22:32', 1, 'Q2876412'),
(31252, 'Aïn el Bya', 1101, '31', 4, 'DZ', 35.80389000, -0.30178000, '2019-10-05 22:45:25', '2020-05-01 17:22:33', 1, 'Q23826440'),
(31253, 'BABOR - VILLE', 1141, '19', 4, 'DZ', 36.48994000, 5.53930000, '2019-10-05 22:45:25', '2019-10-05 22:45:25', 1, 'Q23826440'),
(31254, 'Bab Ezzouar', 1144, '16', 4, 'DZ', 36.72615000, 3.18291000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q59579'),
(31255, 'Baraki', 1115, '42', 4, 'DZ', 36.66655000, 3.09606000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2736044'),
(31256, 'Barbacha', 1128, '06', 4, 'DZ', 36.56667000, 4.96667000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2214633'),
(31257, 'Barika', 1142, '05', 4, 'DZ', 35.38901000, 5.36584000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q648253'),
(31258, 'Batna', 1142, '05', 4, 'DZ', 35.55597000, 6.17414000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q338844'),
(31259, 'Bejaïa', 1128, '06', 4, 'DZ', 36.75587000, 5.08433000, '2019-10-05 22:45:26', '2020-05-01 17:22:32', 1, 'Q59975'),
(31260, 'Ben Mehidi', 1100, '36', 4, 'DZ', 36.76967000, 7.90641000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q253342'),
(31261, 'Beni Amrane', 1125, '35', 4, 'DZ', 36.66774000, 3.59115000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2223571'),
(31262, 'Beni Douala', 1131, '15', 4, 'DZ', 36.61954000, 4.08282000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2895885'),
(31263, 'Beni Mered', 1111, '09', 4, 'DZ', 36.52389000, 2.86131000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2895897'),
(31264, 'Beni Mester', 1107, '13', 4, 'DZ', 34.87045000, -1.42319000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649238'),
(31265, 'Beni Saf', 1122, '46', 4, 'DZ', 35.30099000, -1.38226000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q80408'),
(31266, 'Bensekrane', 1107, '13', 4, 'DZ', 35.07465000, -1.22431000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q282544'),
(31267, 'Berrahal', 1103, '23', 4, 'DZ', 36.83528000, 7.45333000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649131'),
(31268, 'Berriane', 1127, '47', 4, 'DZ', 32.82648000, 3.76689000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649062'),
(31269, 'Berrouaghia', 1109, '26', 4, 'DZ', 36.13516000, 2.91085000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2214965'),
(31270, 'Besbes', 1100, '36', 4, 'DZ', 36.70222000, 7.84722000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649634'),
(31271, 'Bir el Ater', 1117, '12', 4, 'DZ', 34.74488000, 8.06024000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2904370'),
(31272, 'Bir el Djir', 1101, '31', 4, 'DZ', 35.72000000, -0.54500000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q401759'),
(31273, 'Birine', 1098, '17', 4, 'DZ', 35.63500000, 3.22500000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649303'),
(31274, 'Birkhadem', 1144, '16', 4, 'DZ', 36.71499000, 3.05002000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2904515'),
(31275, 'Biskra', 1114, '07', 4, 'DZ', 34.85038000, 5.72805000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q312759'),
(31276, 'Blida', 1111, '09', 4, 'DZ', 36.47004000, 2.82770000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q216990'),
(31277, 'Boghni', 1131, '15', 4, 'DZ', 36.54222000, 3.95306000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649016'),
(31278, 'Bordj Bou Arreridj', 1116, '34', 4, 'DZ', 36.07321000, 4.76108000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q544733'),
(31279, 'Bordj Ghdir', 1116, '34', 4, 'DZ', 35.90111000, 4.89806000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q23820716'),
(31280, 'Bordj Zemoura', 1116, '34', 4, 'DZ', 36.27462000, 4.85668000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1648662'),
(31281, 'Bordj el Kiffan', 1144, '16', 4, 'DZ', 36.74871000, 3.19249000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1184993'),
(31282, 'Bou Hanifia el Hamamat', 1124, '29', 4, 'DZ', 35.31473000, -0.05037000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2911890'),
(31283, 'Bou Ismaïl', 1115, '42', 4, 'DZ', 36.64262000, 2.69007000, '2019-10-05 22:45:26', '2020-05-01 17:22:33', 1, 'Q1438489'),
(31284, 'Bou Tlelis', 1101, '31', 4, 'DZ', 35.57272000, -0.89960000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q23819224'),
(31285, 'Boudjima', 1131, '15', 4, 'DZ', 36.80218000, 4.15187000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2921489'),
(31286, 'Boudouaou', 1125, '35', 4, 'DZ', 36.72735000, 3.40995000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q311984'),
(31287, 'Boufarik', 1111, '09', 4, 'DZ', 36.57413000, 2.91214000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2344308'),
(31288, 'Bougaa', 1141, '19', 4, 'DZ', 36.33293000, 5.08843000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2921542'),
(31289, 'Bougara', 1111, '09', 4, 'DZ', 36.54178000, 3.08100000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2573679'),
(31290, 'Bouinan', 1111, '09', 4, 'DZ', 36.53167000, 2.99194000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2921643'),
(31291, 'Boukadir', 1105, '02', 4, 'DZ', 36.06629000, 1.12602000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1648911'),
(31292, 'Boumagueur', 1142, '05', 4, 'DZ', 35.50520000, 5.55250000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1648911'),
(31293, 'Boumahra Ahmed', 1137, '24', 4, 'DZ', 36.45833000, 7.51389000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649113'),
(31294, 'Boumerdas', 1125, '35', 4, 'DZ', 36.76639000, 3.47717000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q770518'),
(31295, 'Bouïra', 1104, '10', 4, 'DZ', 36.37489000, 3.90200000, '2019-10-05 22:45:26', '2020-05-01 17:22:32', 1, 'Q722522'),
(31296, 'Boû Arfa', 1111, '09', 4, 'DZ', 36.46298000, 2.81464000, '2019-10-05 22:45:26', '2020-05-01 17:22:32', 1, 'Q23820810'),
(31297, 'Brezina', 1129, '32', 4, 'DZ', 33.09892000, 1.26082000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2924791'),
(31298, 'Béchar', 1108, '08', 4, 'DZ', 31.61667000, -2.21667000, '2019-10-05 22:45:26', '2020-05-01 17:22:32', 1, 'Q638704'),
(31299, 'Chabet el Ameur', 1125, '35', 4, 'DZ', 36.63709000, 3.69474000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q37926868'),
(31300, 'Charef', 1098, '17', 4, 'DZ', 34.62098000, 2.79503000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649151'),
(31301, 'Chebli', 1111, '09', 4, 'DZ', 36.57722000, 3.00917000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649700'),
(31302, 'Chelghoum el Aïd', 1132, '43', 4, 'DZ', 36.16286000, 6.16651000, '2019-10-05 22:45:26', '2020-05-01 17:22:33', 1, 'Q23818040'),
(31303, 'Chemini', 1131, '15', 4, 'DZ', 36.60000000, 4.61667000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2962465'),
(31304, 'Cheraga', 1115, '42', 4, 'DZ', 36.76775000, 2.95924000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q335751'),
(31305, 'Cheria', 1117, '12', 4, 'DZ', 35.27306000, 7.75194000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q736205'),
(31306, 'Chetouane', 1107, '13', 4, 'DZ', 34.92129000, -1.29512000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2962883'),
(31307, 'Chiffa', 1111, '09', 4, 'DZ', 36.46293000, 2.73873000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2963503'),
(31308, 'Chlef', 1105, '02', 4, 'DZ', 36.16525000, 1.33452000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q849561'),
(31309, 'Chorfa', 1104, '10', 4, 'DZ', 36.36505000, 4.32636000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q23816562'),
(31310, 'Constantine', 1121, '25', 4, 'DZ', 36.36500000, 6.61472000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1093156'),
(31311, 'Dar Chioukh', 1098, '17', 4, 'DZ', 34.89638000, 3.48543000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3015935'),
(31312, 'Dar el Beïda', 1144, '16', 4, 'DZ', 36.71333000, 3.21250000, '2019-10-05 22:45:26', '2020-05-01 17:22:32', 1, 'Q59419'),
(31313, 'Debila', 1099, '39', 4, 'DZ', 33.51667000, 6.95000000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3020835'),
(31314, 'Dellys', 1125, '35', 4, 'DZ', 36.91716000, 3.91311000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3021755'),
(31315, 'Didouche Mourad', 1121, '25', 4, 'DZ', 36.45250000, 6.63639000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3027257'),
(31316, 'Djamaa', 1139, '30', 4, 'DZ', 33.53388000, 5.99306000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3032751'),
(31317, 'Djebilet Rosfa', 1106, '14', 4, 'DZ', 34.86375000, 0.83496000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649054'),
(31318, 'Djelfa', 1098, '17', 4, 'DZ', 34.67279000, 3.26300000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2794758'),
(31319, 'Djidiouia', 1130, '48', 4, 'DZ', 35.92989000, 0.82871000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3032949'),
(31320, 'Douera', 1115, '42', 4, 'DZ', 36.67000000, 2.94444000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649106'),
(31321, 'Draa Ben Khedda', 1131, '15', 4, 'DZ', 36.73436000, 3.96223000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q5306152'),
(31322, 'Draa el Mizan', 1104, '10', 4, 'DZ', 36.53628000, 3.83340000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q4119323'),
(31323, 'Drean', 1103, '23', 4, 'DZ', 36.68482000, 7.75111000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1924629'),
(31324, 'Ech Chettia', 1105, '02', 4, 'DZ', 36.19591000, 1.25537000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q2962891'),
(31325, 'El Abadia', 1119, '44', 4, 'DZ', 36.26951000, 1.68609000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1649058'),
(31326, 'El Abiodh Sidi Cheikh', 1129, '32', 4, 'DZ', 32.89300000, 0.54839000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3049673'),
(31327, 'El Achir', 1116, '34', 4, 'DZ', 36.06386000, 4.62744000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3049676'),
(31328, 'El Affroun', 1115, '42', 4, 'DZ', 36.47010000, 2.62528000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3049687'),
(31329, 'El Amria', 1122, '46', 4, 'DZ', 35.52439000, -1.01577000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1657351'),
(31330, 'El Aouinet', 1136, '04', 4, 'DZ', 35.86691000, 7.88673000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q921801'),
(31331, 'El Attaf', 1119, '44', 4, 'DZ', 36.22393000, 1.67187000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q1029528'),
(31332, 'El Bayadh', 1129, '32', 4, 'DZ', 33.68318000, 1.01927000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q648784'),
(31333, 'El Eulma', 1141, '19', 4, 'DZ', 36.15281000, 5.69016000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q401747'),
(31334, 'El Hadjar', 1103, '23', 4, 'DZ', 36.80377000, 7.73684000, '2019-10-05 22:45:26', '2019-10-05 22:45:26', 1, 'Q3043115');

