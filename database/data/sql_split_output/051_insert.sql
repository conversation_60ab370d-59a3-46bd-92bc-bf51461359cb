INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(25332, 'Fürstenhausen', 3020, 'SL', 82, 'DE', 49.24075000, 6.86817000, '2019-10-05 22:40:52', '2020-05-01 17:22:49', 1, 'Q505727'),
(25333, '<PERSON><PERSON><PERSON><PERSON>', 3009, 'BY', 82, 'DE', 48.71667000, 13.33333000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q509768'),
(25334, 'F<PERSON><PERSON>walde', 3013, 'BB', 82, 'DE', 52.36067000, 14.06185000, '2019-10-05 22:40:52', '2020-05-01 17:22:48', 1, 'Q509768'),
(25335, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 3009, 'B<PERSON>', 82, '<PERSON>', 48.52163000, 13.31749000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q256810'),
(25336, '<PERSON><PERSON>rth', 3018, 'H<PERSON>', 82, '<PERSON>', 49.65083000, 8.78472000, '2019-10-05 22:40:52', '2020-05-01 17:22:48', 1, 'Q256810'),
(25338, '<PERSON><PERSON><PERSON>en', 3019, 'RP', 82, 'DE', 50.78333000, 7.66667000, '2019-10-05 22:40:52', '2020-05-01 17:22:49', 1, 'Q564240'),
(25339, 'Füssen', 3009, 'BY', 82, 'DE', 47.57143000, 10.70171000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q262684'),
(25340, 'Gablenz', 3021, 'SN', 82, 'DE', 51.53333000, 14.66667000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q262684'),
(25341, 'Gablingen', 3009, 'BY', 82, 'DE', 48.45000000, 10.81667000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q529373'),
(25342, 'Gachenbach', 3009, 'BY', 82, 'DE', 48.50000000, 11.23333000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q510090'),
(25343, 'Gadebusch', 3007, 'MV', 82, 'DE', 53.70137000, 11.11676000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q506665'),
(25344, 'Gadheim', 3009, 'BY', 82, 'DE', 49.84584000, 9.90566000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q32021538'),
(25345, 'Gaggenau', 3006, 'BW', 82, 'DE', 48.80000000, 8.33333000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q504451'),
(25346, 'Gaiberg', 3006, 'BW', 82, 'DE', 49.36639000, 8.74972000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q60573'),
(25347, 'Gaienhofen', 3006, 'BW', 82, 'DE', 47.68333000, 8.98333000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q61701'),
(25348, 'Gaildorf', 3006, 'BW', 82, 'DE', 49.00027000, 9.76953000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q81811'),
(25349, 'Gailingen', 3006, 'BW', 82, 'DE', 47.69711000, 8.75567000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q62026'),
(25350, 'Gaimersheim', 3009, 'BY', 82, 'DE', 48.80705000, 11.36801000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q528700'),
(25351, 'Gaißach', 3009, 'BY', 82, 'DE', 47.75000000, 11.58333000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q528700'),
(25352, 'Gallus', 3018, 'HE', 82, 'DE', 50.10038000, 8.62950000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q447986'),
(25353, 'Gammelsdorf', 3009, 'BY', 82, 'DE', 48.55000000, 11.95000000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q627451'),
(25354, 'Gammelshausen', 3006, 'BW', 82, 'DE', 48.64023000, 9.65072000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q80472'),
(25355, 'Gammertingen', 3006, 'BW', 82, 'DE', 48.25240000, 9.22349000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q83088'),
(25356, 'Ganderkesee', 3008, 'NI', 82, 'DE', 53.03333000, 8.53333000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q16111'),
(25357, 'Gangelt', 3017, 'NW', 82, 'DE', 50.99247000, 5.99802000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q204793'),
(25358, 'Gangkofen', 3009, 'BY', 82, 'DE', 48.43701000, 12.56419000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q581591'),
(25359, 'Gangloffsömmern', 3015, 'TH', 82, 'DE', 51.18940000, 10.94332000, '2019-10-05 22:40:52', '2020-05-01 17:22:50', 1, 'Q556425'),
(25360, 'Garbsen', 3008, 'NI', 82, 'DE', 52.41371000, 9.58990000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q4001'),
(25361, 'Garbsen-Mitte', 3008, 'NI', 82, 'DE', 52.42659000, 9.60383000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q1493967'),
(25362, 'Garching an der Alz', 3009, 'BY', 82, 'DE', 48.13451000, 12.58152000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q32024590'),
(25363, 'Garching bei München', 3009, 'BY', 82, 'DE', 48.24896000, 11.65101000, '2019-10-05 22:40:52', '2020-05-01 17:22:47', 1, 'Q32024590'),
(25364, 'Gardelegen', 3011, 'ST', 82, 'DE', 52.52520000, 11.39523000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q32024620'),
(25365, 'Garding', 3005, 'SH', 82, 'DE', 54.33056000, 8.78056000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q27324'),
(25366, 'Garlstorf', 3008, 'NI', 82, 'DE', 53.23728000, 10.10137000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q27324'),
(25367, 'Garmisch-Partenkirchen', 3009, 'BY', 82, 'DE', 47.49209000, 11.09576000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q27324'),
(25368, 'Garrel', 3008, 'NI', 82, 'DE', 52.95000000, 8.01667000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q559605'),
(25369, 'Gars', 3009, 'BY', 82, 'DE', 48.15386000, 12.27767000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q531813'),
(25370, 'Garstedt', 3008, 'NI', 82, 'DE', 53.28467000, 10.16137000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q639664'),
(25371, 'Gartenstadt', 3019, 'RP', 82, 'DE', 49.45937000, 8.40377000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q639664'),
(25372, 'Gartow', 3008, 'NI', 82, 'DE', 53.02470000, 11.46200000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q656665'),
(25373, 'Gartz', 3013, 'BB', 82, 'DE', 53.20829000, 14.39226000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q571998'),
(25374, 'Garz', 3007, 'MV', 82, 'DE', 54.31843000, 13.35125000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q571998'),
(25375, 'Gatersleben', 3011, 'ST', 82, 'DE', 51.82215000, 11.28661000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q686432'),
(25376, 'Gatow', 3010, 'BE', 82, 'DE', 52.48324000, 13.18285000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q515832'),
(25377, 'Gattendorf', 3009, 'BY', 82, 'DE', 50.32207000, 11.99776000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q515832'),
(25378, 'Gau-Algesheim', 3019, 'RP', 82, 'DE', 49.95669000, 8.01569000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q570316'),
(25379, 'Gau-Bickelheim', 3019, 'RP', 82, 'DE', 49.83528000, 8.02056000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q610873'),
(25380, 'Gau-Bischofsheim', 3019, 'RP', 82, 'DE', 49.91583000, 8.27278000, '2019-10-05 22:40:52', '2019-10-05 22:40:52', 1, 'Q678819'),
(25381, 'Gau-Odernheim', 3019, 'RP', 82, 'DE', 49.78472000, 8.19417000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q653864'),
(25382, 'Gaukönigshofen', 3009, 'BY', 82, 'DE', 49.63333000, 10.00000000, '2019-10-05 22:40:53', '2020-05-01 17:22:47', 1, 'Q527345'),
(25383, 'Gauting', 3009, 'BY', 82, 'DE', 48.06919000, 11.37703000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q74057'),
(25384, 'Gebesee', 3015, 'TH', 82, 'DE', 51.11488000, 10.93455000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q529868'),
(25385, 'Gebhardshain', 3019, 'RP', 82, 'DE', 50.75000000, 7.81667000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q638852'),
(25386, 'Gebsattel', 3009, 'BY', 82, 'DE', 49.35000000, 10.20000000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q510077'),
(25387, 'Gechingen', 3006, 'BW', 82, 'DE', 48.69527000, 8.82915000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q266250'),
(25388, 'Gedern', 3018, 'HE', 82, 'DE', 50.42482000, 9.19840000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q560940'),
(25389, 'Geeste', 3008, 'NI', 82, 'DE', 52.60000000, 7.26667000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q560940'),
(25390, 'Geesthacht', 3005, 'SH', 82, 'DE', 53.43575000, 10.37790000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q494790'),
(25391, 'Gefrees', 3009, 'BY', 82, 'DE', 50.09538000, 11.73772000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q504562'),
(25392, 'Gehrde', 3008, 'NI', 82, 'DE', 52.57684000, 8.00342000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q650107'),
(25393, 'Gehrden', 3008, 'NI', 82, 'DE', 52.31362000, 9.60033000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q549864'),
(25394, 'Gehren', 3015, 'TH', 82, 'DE', 50.64852000, 11.00471000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q550866'),
(25395, 'Geiersthal', 3009, 'BY', 82, 'DE', 49.04569000, 12.98171000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q488714'),
(25396, 'Geilenkirchen', 3017, 'NW', 82, 'DE', 50.96745000, 6.11763000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q205050'),
(25397, 'Geisa', 3015, 'TH', 82, 'DE', 50.71465000, 9.95075000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q539040'),
(25398, 'Geiselbach', 3009, 'BY', 82, 'DE', 50.12329000, 9.19664000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q264422'),
(25399, 'Geiselhöring', 3009, 'BY', 82, 'DE', 48.82505000, 12.39649000, '2019-10-05 22:40:53', '2020-05-01 17:22:47', 1, 'Q33438088'),
(25400, 'Geiselwind', 3009, 'BY', 82, 'DE', 49.77356000, 10.47063000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q514058'),
(25401, 'Geisenfeld', 3009, 'BY', 82, 'DE', 48.68426000, 11.61233000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q259795'),
(25402, 'Geisenhausen', 3009, 'BY', 82, 'DE', 48.47609000, 12.25817000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q533032'),
(25403, 'Geisenheim', 3018, 'HE', 82, 'DE', 49.98470000, 7.96835000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q558516'),
(25404, 'Geising', 3021, 'SN', 82, 'DE', 50.75742000, 13.79278000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q572891'),
(25405, 'Geisingen', 3006, 'BW', 82, 'DE', 47.92504000, 8.65002000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q506571'),
(25406, 'Geisleden', 3015, 'TH', 82, 'DE', 51.35000000, 10.20000000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q628077'),
(25407, 'Geislingen', 3006, 'BW', 82, 'DE', 48.28768000, 8.81241000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q628077'),
(25408, 'Geislingen an der Steige', 3006, 'BW', 82, 'DE', 48.62423000, 9.82736000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q80836'),
(25409, 'Geismar', 3015, 'TH', 82, 'DE', 51.23169000, 10.16548000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q517804'),
(25410, 'Geithain', 3021, 'SN', 82, 'DE', 51.05528000, 12.69674000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q10765'),
(25411, 'Gelbensande', 3007, 'MV', 82, 'DE', 54.20272000, 12.30168000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q32028356'),
(25412, 'Geldern', 3017, 'NW', 82, 'DE', 51.51908000, 6.32363000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q16024'),
(25413, 'Geldersheim', 3009, 'BY', 82, 'DE', 50.04286000, 10.15609000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q558818'),
(25414, 'Gelenau', 3021, 'SN', 82, 'DE', 50.71151000, 12.96666000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q33438119'),
(25415, 'Gelnhausen', 3018, 'HE', 82, 'DE', 50.20164000, 9.18742000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q494733'),
(25416, 'Gelsenkirchen', 3017, 'NW', 82, 'DE', 51.50508000, 7.09654000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q2765'),
(25417, 'Geltendorf', 3009, 'BY', 82, 'DE', 48.11737000, 11.03216000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, '*********'),
(25418, 'Gelting', 3005, 'SH', 82, 'DE', 54.75000000, 9.90000000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q548337'),
(25419, 'Gemeinde Friedland', 3008, 'NI', 82, 'DE', 51.41667000, 9.93333000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q548337'),
(25420, 'Gemmingen', 3006, 'BW', 82, 'DE', 49.15639000, 8.98194000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q518786'),
(25421, 'Gemmrigheim', 3006, 'BW', 82, 'DE', 49.02833000, 9.15556000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q62059'),
(25422, 'Gemünden', 3019, 'RP', 82, 'DE', 49.89444000, 7.47750000, '2019-10-05 22:40:53', '2020-05-01 17:22:49', 1, 'Q650841'),
(25423, 'Gemünden am Main', 3009, 'BY', 82, 'DE', 50.04946000, 9.70593000, '2019-10-05 22:40:53', '2020-05-01 17:22:47', 1, 'Q505332'),
(25424, 'Gemünden an der Wohra', 3018, 'HE', 82, 'DE', 50.97401000, 8.96946000, '2019-10-05 22:40:53', '2020-05-01 17:22:48', 1, 'Q624854'),
(25425, 'Genderkingen', 3009, 'BY', 82, 'DE', 48.70000000, 10.88333000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q511883'),
(25426, 'Gengenbach', 3006, 'BW', 82, 'DE', 48.40476000, 8.01433000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q505495'),
(25427, 'Gensingen', 3019, 'RP', 82, 'DE', 49.90000000, 7.93333000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q678407'),
(25428, 'Genthin', 3011, 'ST', 82, 'DE', 52.40668000, 12.15920000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q284317'),
(25429, 'Georgenberg', 3009, 'BY', 82, 'DE', 49.70297000, 12.42082000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q284317'),
(25430, 'Georgensgmünd', 3009, 'BY', 82, 'DE', 49.18972000, 11.01667000, '2019-10-05 22:40:53', '2020-05-01 17:22:47', 1, 'Q550757'),
(25431, 'Georgenthal', 3015, 'TH', 82, 'DE', 50.83205000, 10.66266000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q567994'),
(25432, 'Georgsdorf', 3008, 'NI', 82, 'DE', 52.56667000, 7.08333000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q621869'),
(25433, 'Georgsmarienhütte', 3008, 'NI', 82, 'DE', 52.20296000, 8.04480000, '2019-10-05 22:40:53', '2020-05-01 17:22:48', 1, 'Q16078'),
(25434, 'Gera', 3015, 'TH', 82, 'DE', 50.88029000, 12.08187000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q16078'),
(25435, 'Geraberg', 3015, 'TH', 82, 'DE', 50.71594000, 10.83737000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q649035'),
(25436, 'Gerabronn', 3006, 'BW', 82, 'DE', 48.97071000, 9.91986000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q649035'),
(25437, 'Gerach', 3009, 'BY', 82, 'DE', 50.03333000, 10.80000000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q569566'),
(25438, 'Gerbrunn', 3009, 'BY', 82, 'DE', 49.77528000, 9.99361000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q545262'),
(25439, 'Gerbstedt', 3011, 'ST', 82, 'DE', 51.63281000, 11.62669000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q50891'),
(25440, 'Gerdau', 3008, 'NI', 82, 'DE', 52.96667000, 10.41667000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q50891'),
(25441, 'Geretsried', 3009, 'BY', 82, 'DE', 47.85775000, 11.48054000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q504780'),
(25442, 'Gerhardshofen', 3009, 'BY', 82, 'DE', 49.63123000, 10.69133000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q511188'),
(25443, 'Geringswalde', 3021, 'SN', 82, 'DE', 51.07677000, 12.90725000, '2019-10-05 22:40:53', '2019-10-05 22:40:53', 1, 'Q71111'),
(25444, 'Gerlingen', 3006, 'BW', 82, 'DE', 48.79954000, 9.06316000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q61843'),
(25445, 'Germering', 3009, 'BY', 82, 'DE', 48.13392000, 11.37650000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q32030061'),
(25446, 'Germersheim', 3019, 'RP', 82, 'DE', 49.22306000, 8.36389000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q22629'),
(25447, 'Gernrode', 3011, 'ST', 82, 'DE', 51.72575000, 11.13876000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q22629'),
(25448, 'Gernrode', 3015, 'TH', 82, 'DE', 51.40000000, 10.40000000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q22629'),
(25449, 'Gernsbach', 3006, 'BW', 82, 'DE', 48.77034000, 8.34306000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q520119'),
(25450, 'Gernsheim', 3018, 'HE', 82, 'DE', 49.75305000, 8.48859000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q625091'),
(25451, 'Geroldsgrün', 3009, 'BY', 82, 'DE', 50.33333000, 11.60000000, '2019-10-05 22:40:54', '2020-05-01 17:22:47', 1, 'Q502840'),
(25452, 'Geroldshausen', 3009, 'BY', 82, 'DE', 49.68361000, 9.90222000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q527406'),
(25453, 'Gerolfingen', 3009, 'BY', 82, 'DE', 49.05340000, 10.51151000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q510813'),
(25454, 'Gerolsbach', 3009, 'BY', 82, 'DE', 48.49283000, 11.36149000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q260245'),
(25455, 'Gerolsheim', 3019, 'RP', 82, 'DE', 49.54778000, 8.26389000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q22524'),
(25456, 'Gerolstein', 3019, 'RP', 82, 'DE', 50.22224000, 6.65984000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q389985'),
(25457, 'Gerolzhofen', 3009, 'BY', 82, 'DE', 49.90025000, 10.34832000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q519134'),
(25458, 'Gersdorf', 3021, 'SN', 82, 'DE', 51.11220000, 12.93889000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q32030338'),
(25459, 'Gersfeld', 3018, 'HE', 82, 'DE', 50.45138000, 9.91422000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q593813'),
(25460, 'Gersheim', 3020, 'SL', 82, 'DE', 49.15000000, 7.20000000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q627722'),
(25461, 'Gersten', 3008, 'NI', 82, 'DE', 52.58333000, 7.51667000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q627722'),
(25462, 'Gerstetten', 3006, 'BW', 82, 'DE', 48.62254000, 10.01984000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q32030427'),
(25463, 'Gersthofen', 3009, 'BY', 82, 'DE', 48.42432000, 10.87273000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q502767'),
(25464, 'Gerstungen', 3015, 'TH', 82, 'DE', 50.96667000, 10.06667000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q675672'),
(25465, 'Gerswalde', 3013, 'BB', 82, 'DE', 53.16988000, 13.74853000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q625872'),
(25466, 'Gerwisch', 3011, 'ST', 82, 'DE', 52.17662000, 11.73972000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q702804'),
(25467, 'Gerzen', 3009, 'BY', 82, 'DE', 48.50706000, 12.42686000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q127896'),
(25468, 'Gescher', 3017, 'NW', 82, 'DE', 51.95400000, 7.00481000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q32030555'),
(25469, 'Geschwenda', 3015, 'TH', 82, 'DE', 50.73178000, 10.82540000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q637363'),
(25470, 'Gesees', 3009, 'BY', 82, 'DE', 50.03207000, 11.66623000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q32030580'),
(25471, 'Geseke', 3017, 'NW', 82, 'DE', 51.64091000, 8.51090000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q32030588'),
(25472, 'Geslau', 3009, 'BY', 82, 'DE', 49.36643000, 10.31528000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q510100'),
(25473, 'Gessertshausen', 3009, 'BY', 82, 'DE', 48.32904000, 10.73278000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q512928'),
(25474, 'Gestratz', 3009, 'BY', 82, 'DE', 47.65000000, 9.98333000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q512928'),
(25475, 'Gesundbrunnen', 3010, 'BE', 82, 'DE', 52.55035000, 13.39139000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q644350'),
(25476, 'Gettorf', 3005, 'SH', 82, 'DE', 54.40000000, 9.98333000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q630251'),
(25477, 'Geusa', 3011, 'ST', 82, 'DE', 51.33914000, 11.94382000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q690003'),
(25478, 'Gevelsberg', 3017, 'NW', 82, 'DE', 51.31971000, 7.33920000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q11331'),
(25479, 'Geyer', 3021, 'SN', 82, 'DE', 50.62632000, 12.92074000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q57891'),
(25480, 'Giebelstadt', 3009, 'BY', 82, 'DE', 49.65308000, 9.94441000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q536433'),
(25481, 'Gieboldehausen', 3008, 'NI', 82, 'DE', 51.60962000, 10.21619000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q566701'),
(25482, 'Giekau', 3005, 'SH', 82, 'DE', 54.31667000, 10.51667000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q566701'),
(25483, 'Gielow', 3007, 'MV', 82, 'DE', 53.69756000, 12.74521000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q50954'),
(25484, 'Giengen an der Brenz', 3006, 'BW', 82, 'DE', 48.62219000, 10.24312000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q547710'),
(25485, 'Giersleben', 3011, 'ST', 82, 'DE', 51.76667000, 11.56667000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q564370'),
(25486, 'Giesen', 3008, 'NI', 82, 'DE', 52.19716000, 9.89890000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q635190'),
(25487, 'Gießen', 3018, 'HE', 82, 'DE', 50.58727000, 8.67554000, '2019-10-05 22:40:54', '2020-05-01 17:22:48', 1, 'Q3874'),
(25488, 'Gifhorn', 3008, 'NI', 82, 'DE', 52.47774000, 10.55110000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q14813'),
(25489, 'Gilching', 3009, 'BY', 82, 'DE', 48.10755000, 11.29360000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q552131'),
(25490, 'Gillenfeld', 3019, 'RP', 82, 'DE', 50.12790000, 6.90383000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q659751'),
(25491, 'Gilserberg', 3018, 'HE', 82, 'DE', 50.95000000, 9.06667000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q659751'),
(25492, 'Gilten', 3008, 'NI', 82, 'DE', 52.70000000, 9.58333000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q659751'),
(25493, 'Gimbsheim', 3019, 'RP', 82, 'DE', 49.77778000, 8.37500000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q565408'),
(25494, 'Gingen an der Fils', 3006, 'BW', 82, 'DE', 48.65979000, 9.78092000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q80522'),
(25495, 'Gingst', 3007, 'MV', 82, 'DE', 54.45654000, 13.25737000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q560992'),
(25496, 'Ginsheim-Gustavsburg', 3018, 'HE', 82, 'DE', 49.97110000, 8.34532000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q639519'),
(25497, 'Girod', 3019, 'RP', 82, 'DE', 50.45000000, 7.91667000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q674063'),
(25498, 'Gittelde', 3008, 'NI', 82, 'DE', 51.79840000, 10.18780000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q633531'),
(25499, 'Gladbeck', 3017, 'NW', 82, 'DE', 51.57077000, 6.98593000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q3905'),
(25500, 'Gladenbach', 3018, 'HE', 82, 'DE', 50.76847000, 8.58085000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q551378'),
(25501, 'Glan-Münchweiler', 3019, 'RP', 82, 'DE', 49.47222000, 7.44204000, '2019-10-05 22:40:54', '2020-05-01 17:22:49', 1, 'Q646584'),
(25502, 'Glandorf', 3008, 'NI', 82, 'DE', 52.08474000, 7.99944000, '2019-10-05 22:40:54', '2019-10-05 22:40:54', 1, 'Q646584'),
(25503, 'Glashütte', 3021, 'SN', 82, 'DE', 50.85196000, 13.77977000, '2019-10-05 22:40:54', '2020-05-01 17:22:49', 1, 'Q33438660'),
(25504, 'Glashütten', 3009, 'BY', 82, 'DE', 49.88739000, 11.44870000, '2019-10-05 22:40:54', '2020-05-01 17:22:47', 1, 'Q33438660'),
(25505, 'Glashütten', 3018, 'HE', 82, 'DE', 50.21667000, 8.40000000, '2019-10-05 22:40:55', '2020-05-01 17:22:48', 1, 'Q637032'),
(25506, 'Glattbach', 3009, 'BY', 82, 'DE', 50.00000000, 9.15000000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q507364'),
(25507, 'Glatten', 3006, 'BW', 82, 'DE', 48.44246000, 8.51116000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q80475'),
(25508, 'Glaubitz', 3021, 'SN', 82, 'DE', 51.32498000, 13.37817000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q8706'),
(25509, 'Glauburg', 3018, 'HE', 82, 'DE', 50.31667000, 9.00000000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q625348'),
(25510, 'Glauchau', 3021, 'SN', 82, 'DE', 50.81987000, 12.54493000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q20071'),
(25511, 'Gleichamberg', 3015, 'TH', 82, 'DE', 50.37068000, 10.59822000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q649102'),
(25512, 'Glienicke', 3013, 'BB', 82, 'DE', 52.63353000, 13.32564000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q649102'),
(25513, 'Glinde', 3005, 'SH', 82, 'DE', 53.54410000, 10.20048000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q32034335'),
(25514, 'Glindenberg', 3011, 'ST', 82, 'DE', 52.23857000, 11.68361000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q701275'),
(25515, 'Glonn', 3009, 'BY', 82, 'DE', 47.98751000, 11.86721000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q629629'),
(25516, 'Glowe', 3007, 'MV', 82, 'DE', 54.56911000, 13.46550000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q561014'),
(25517, 'Glöthe', 3011, 'ST', 82, 'DE', 51.91000000, 11.67471000, '2019-10-05 22:40:55', '2020-05-01 17:22:49', 1, 'Q1532713'),
(25518, 'Glött', 3009, 'BY', 82, 'DE', 48.50000000, 10.48333000, '2019-10-05 22:40:55', '2020-05-01 17:22:47', 1, 'Q1532713'),
(25519, 'Glücksburg', 3005, 'SH', 82, 'DE', 54.83522000, 9.54853000, '2019-10-05 22:40:55', '2020-05-01 17:22:49', 1, 'Q494617'),
(25520, 'Glückstadt', 3005, 'SH', 82, 'DE', 53.78893000, 9.42576000, '2019-10-05 22:40:55', '2020-05-01 17:22:49', 1, 'Q377277'),
(25521, 'Gmund am Tegernsee', 3009, 'BY', 82, 'DE', 47.75119000, 11.73810000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q31662'),
(25522, 'Gnarrenburg', 3008, 'NI', 82, 'DE', 53.38333000, 9.00000000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q503947'),
(25523, 'Gnoien', 3007, 'MV', 82, 'DE', 53.96870000, 12.71099000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q536734'),
(25524, 'Gnutz', 3005, 'SH', 82, 'DE', 54.13333000, 9.81667000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q536734'),
(25525, 'Goch', 3017, 'NW', 82, 'DE', 51.67873000, 6.15895000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q16006'),
(25526, 'Gochsheim', 3009, 'BY', 82, 'DE', 50.01667000, 10.28333000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q585507'),
(25527, 'Goel', 3005, 'SH', 82, 'DE', 54.28350000, 10.94036000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q585507'),
(25528, 'Goldbach', 3009, 'BY', 82, 'DE', 49.99951000, 9.18440000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q516238'),
(25529, 'Goldbach', 3015, 'TH', 82, 'DE', 51.04566000, 11.43289000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q32035907'),
(25530, 'Goldbeck', 3011, 'ST', 82, 'DE', 52.71695000, 11.86060000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q32035907'),
(25531, 'Goldberg', 3007, 'MV', 82, 'DE', 53.58878000, 12.08855000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q32035907'),
(25532, 'Goldenstedt', 3008, 'NI', 82, 'DE', 52.78833000, 8.43201000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q565790'),
(25533, 'Goldkronach', 3009, 'BY', 82, 'DE', 50.01086000, 11.68750000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q503133'),
(25534, 'Golmbach', 3008, 'NI', 82, 'DE', 51.90000000, 9.55000000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q550977'),
(25535, 'Golzow', 3013, 'BB', 82, 'DE', 52.27617000, 12.60052000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q550977'),
(25536, 'Golßen', 3013, 'BB', 82, 'DE', 51.97204000, 13.60115000, '2019-10-05 22:40:55', '2020-05-01 17:22:48', 1, 'Q544892'),
(25537, 'Gomadingen', 3006, 'BW', 82, 'DE', 48.39980000, 9.39065000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q83126'),
(25538, 'Gomaringen', 3006, 'BW', 82, 'DE', 48.45349000, 9.09582000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q507707'),
(25539, 'Gommern', 3011, 'ST', 82, 'DE', 52.07391000, 11.82297000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q50880'),
(25540, 'Gommersheim', 3019, 'RP', 82, 'DE', 49.29111000, 8.26583000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q50880'),
(25541, 'Gondelsheim', 3006, 'BW', 82, 'DE', 49.05917000, 8.65833000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q542411'),
(25542, 'Gondershausen', 3019, 'RP', 82, 'DE', 50.15000000, 7.50000000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q509532'),
(25543, 'Gornau', 3021, 'SN', 82, 'DE', 50.75901000, 13.03731000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q57761'),
(25544, 'Gornsdorf', 3021, 'SN', 82, 'DE', 50.70000000, 12.88333000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q57765'),
(25545, 'Goseck', 3011, 'ST', 82, 'DE', 51.20000000, 11.86667000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q649074'),
(25546, 'Gosheim', 3006, 'BW', 82, 'DE', 48.13430000, 8.75426000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q532790'),
(25547, 'Goslar', 3008, 'NI', 82, 'DE', 51.90425000, 10.42766000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q3896'),
(25548, 'Gossersweiler-Stein', 3019, 'RP', 82, 'DE', 49.15000000, 7.93333000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q648853'),
(25549, 'Gotha', 3015, 'TH', 82, 'DE', 50.94823000, 10.70193000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q6986'),
(25550, 'Gottenheim', 3006, 'BW', 82, 'DE', 48.05000000, 7.73333000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q519267'),
(25551, 'Gotteszell', 3009, 'BY', 82, 'DE', 48.96667000, 12.96667000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q489005'),
(25552, 'Gottfrieding', 3009, 'BY', 82, 'DE', 48.63737000, 12.53561000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q32038507'),
(25553, 'Gottmadingen', 3006, 'BW', 82, 'DE', 47.73511000, 8.77687000, '2019-10-05 22:40:55', '2019-10-05 22:40:55', 1, 'Q61999'),
(25554, 'Goßwitz', 3015, 'TH', 82, 'DE', 50.63536000, 11.47737000, '2019-10-05 22:40:55', '2020-05-01 17:22:50', 1, 'Q61999'),
(25555, 'Graben', 3009, 'BY', 82, 'DE', 48.18732000, 10.82222000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q61999'),
(25556, 'Graben-Neudorf', 3006, 'BW', 82, 'DE', 49.16695000, 8.49243000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q22942'),
(25557, 'Grabenstetten', 3006, 'BW', 82, 'DE', 48.52397000, 9.46155000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q81539'),
(25558, 'Grabenstätt', 3009, 'BY', 82, 'DE', 47.84531000, 12.54330000, '2019-10-05 22:40:56', '2020-05-01 17:22:47', 1, 'Q254786'),
(25559, 'Grabow', 3007, 'MV', 82, 'DE', 53.27966000, 11.56502000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q254786'),
(25560, 'Grabowhöfe', 3007, 'MV', 82, 'DE', 53.56865000, 12.59482000, '2019-10-05 22:40:56', '2020-05-01 17:22:49', 1, 'Q254786'),
(25561, 'Grabsleben', 3015, 'TH', 82, 'DE', 50.93633000, 10.83508000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q287431'),
(25562, 'Grafenau', 3009, 'BY', 82, 'DE', 48.85768000, 13.39740000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32039801'),
(25563, 'Grafenau', 3006, 'BW', 82, 'DE', 48.71427000, 8.91219000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32039801'),
(25564, 'Grafengehaig', 3009, 'BY', 82, 'DE', 50.20310000, 11.59358000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q511113'),
(25565, 'Grafenhausen', 3006, 'BW', 82, 'DE', 48.28427000, 7.76678000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q511113'),
(25566, 'Grafenrheinfeld', 3009, 'BY', 82, 'DE', 50.00000000, 10.20000000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q705742'),
(25567, 'Grafenwiesen', 3009, 'BY', 82, 'DE', 49.20000000, 12.88333000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q511378'),
(25568, 'Grafenwöhr', 3009, 'BY', 82, 'DE', 49.71728000, 11.90645000, '2019-10-05 22:40:56', '2020-05-01 17:22:47', 1, 'Q252595'),
(25569, 'Grafhorst', 3008, 'NI', 82, 'DE', 52.44482000, 10.94513000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q667612'),
(25570, 'Grafing bei München', 3009, 'BY', 82, 'DE', 48.04596000, 11.96797000, '2019-10-05 22:40:56', '2020-05-01 17:22:47', 1, 'Q31606'),
(25571, 'Grafrath', 3009, 'BY', 82, 'DE', 48.11667000, 11.16667000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32039930'),
(25572, 'Grainau', 3009, 'BY', 82, 'DE', 47.47614000, 11.02405000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32039930'),
(25573, 'Grainet', 3009, 'BY', 82, 'DE', 48.80000000, 13.65000000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32040017'),
(25574, 'Gramkow', 3007, 'MV', 82, 'DE', 53.92092000, 11.31740000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32040112'),
(25575, 'Gramzow', 3013, 'BB', 82, 'DE', 53.21246000, 14.00748000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q636566'),
(25576, 'Granschütz', 3011, 'ST', 82, 'DE', 51.18431000, 12.05149000, '2019-10-05 22:40:56', '2020-05-01 17:22:49', 1, 'Q673343'),
(25577, 'Gransee', 3013, 'BB', 82, 'DE', 53.00704000, 13.15750000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q585627'),
(25578, 'Grasberg', 3008, 'NI', 82, 'DE', 53.18333000, 8.98333000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q680429'),
(25579, 'Grasbrunn', 3009, 'BY', 82, 'DE', 48.07905000, 11.74361000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q260566'),
(25580, 'Grasleben', 3008, 'NI', 82, 'DE', 52.30638000, 11.01465000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q660991'),
(25581, 'Grassau', 3009, 'BY', 82, 'DE', 47.78099000, 12.45359000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q179698'),
(25582, 'Grattersdorf', 3009, 'BY', 82, 'DE', 48.80364000, 13.15372000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32040855'),
(25583, 'Grebenau', 3018, 'HE', 82, 'DE', 50.74242000, 9.47307000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q565525'),
(25584, 'Grebenhain', 3018, 'HE', 82, 'DE', 50.48924000, 9.33855000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q1544475'),
(25585, 'Grebenstein', 3018, 'HE', 82, 'DE', 51.44648000, 9.41250000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q260708'),
(25586, 'Grebin', 3005, 'SH', 82, 'DE', 54.20000000, 10.50000000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q635388'),
(25587, 'Greding', 3009, 'BY', 82, 'DE', 49.04699000, 11.35703000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q534651'),
(25588, 'Greffern', 3006, 'BW', 82, 'DE', 48.75205000, 8.00515000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q1544858'),
(25589, 'Grefrath', 3017, 'NW', 82, 'DE', 51.33630000, 6.34072000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q163772'),
(25590, 'Greifenberg', 3009, 'BY', 82, 'DE', 48.07062000, 11.08349000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32041887'),
(25591, 'Greifenstein', 3018, 'HE', 82, 'DE', 50.61667000, 8.30000000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q32041887'),
(25592, 'Greifswald', 3007, 'MV', 82, 'DE', 54.09311000, 13.38786000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q4098'),
(25593, 'Greiling', 3009, 'BY', 82, 'DE', 47.76667000, 11.61667000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q545202'),
(25594, 'Greimerath', 3019, 'RP', 82, 'DE', 49.56155000, 6.68374000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q545202'),
(25595, 'Greiz', 3015, 'TH', 82, 'DE', 50.65778000, 12.19918000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q502710'),
(25596, 'Gremberghoven', 3017, 'NW', 82, 'DE', 50.90152000, 7.06129000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q1642511'),
(25597, 'Gremersdorf', 3005, 'SH', 82, 'DE', 54.33333000, 10.93333000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q1642511'),
(25598, 'Gremsdorf', 3009, 'BY', 82, 'DE', 49.69506000, 10.83218000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q510590'),
(25599, 'Grenzach-Wyhlen', 3006, 'BW', 82, 'DE', 47.55000000, 7.68333000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q62005'),
(25600, 'Greppin', 3011, 'ST', 82, 'DE', 51.64682000, 12.30066000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q649244'),
(25601, 'Grettstadt', 3009, 'BY', 82, 'DE', 49.98333000, 10.31667000, '2019-10-05 22:40:56', '2019-10-05 22:40:56', 1, 'Q583394'),
(25602, 'Greußen', 3015, 'TH', 82, 'DE', 51.22964000, 10.94422000, '2019-10-05 22:40:57', '2020-05-01 17:22:50', 1, 'Q552683'),
(25603, 'Greußenheim', 3009, 'BY', 82, 'DE', 49.81667000, 9.76667000, '2019-10-05 22:40:57', '2020-05-01 17:22:47', 1, 'Q527459'),
(25604, 'Greven', 3017, 'NW', 82, 'DE', 52.09364000, 7.59396000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q33440002'),
(25605, 'Grevenbroich', 3017, 'NW', 82, 'DE', 51.09102000, 6.58270000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q3973'),
(25606, 'Grevesmühlen', 3007, 'MV', 82, 'DE', 53.86337000, 11.19160000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q517353'),
(25607, 'Gries', 3019, 'RP', 82, 'DE', 49.41667000, 7.40000000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q679243'),
(25608, 'Griesheim', 3018, 'HE', 82, 'DE', 49.86085000, 8.57250000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q569685'),
(25609, 'Griesstätt', 3009, 'BY', 82, 'DE', 47.99403000, 12.17727000, '2019-10-05 22:40:57', '2020-05-01 17:22:47', 1, 'Q551697'),
(25610, 'Grimma', 3021, 'SN', 82, 'DE', 51.23367000, 12.71959000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q10780'),
(25611, 'Grimmen', 3007, 'MV', 82, 'DE', 54.11215000, 13.04051000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q571992'),
(25612, 'Groitzsch', 3021, 'SN', 82, 'DE', 51.15536000, 12.28279000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q10766'),
(25613, 'Grolsheim', 3019, 'RP', 82, 'DE', 49.90834000, 7.91599000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q688801'),
(25614, 'Gronau', 3017, 'NW', 82, 'DE', 52.21099000, 7.02238000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q6924'),
(25615, 'Gronau', 3008, 'NI', 82, 'DE', 52.08460000, 9.77678000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q32042813'),
(25616, 'Gropiusstadt', 3010, 'BE', 82, 'DE', 52.42500000, 13.46667000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q677917'),
(25617, 'Grosselfingen', 3006, 'BW', 82, 'DE', 48.33229000, 8.88704000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q573843'),
(25618, 'Grossenbrode', 3005, 'SH', 82, 'DE', 54.38333000, 11.08333000, '2019-10-05 22:40:57', '2019-10-05 22:40:57', 1, 'Q541681'),
(25619, 'Groß Ammensleben', 3011, 'ST', 82, 'DE', 52.23279000, 11.52195000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q638070'),
(25620, 'Groß Börnecke', 3011, 'ST', 82, 'DE', 51.88468000, 11.47032000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q1547889'),
(25621, 'Groß Düben', 3021, 'SN', 82, 'DE', 51.56667000, 14.56667000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q502548'),
(25622, 'Groß Grönau', 3005, 'SH', 82, 'DE', 53.80000000, 10.75000000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q555878'),
(25623, 'Groß Ippener', 3008, 'NI', 82, 'DE', 52.96667000, 8.61667000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q555878'),
(25624, 'Groß Kiesow', 3007, 'MV', 82, 'DE', 54.01261000, 13.47851000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q613280'),
(25625, 'Groß Kreutz', 3013, 'BB', 82, 'DE', 52.40281000, 12.77940000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q33440552'),
(25626, 'Groß Kummerfeld', 3005, 'SH', 82, 'DE', 54.05000000, 10.08333000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q33440552'),
(25627, 'Groß Köris', 3013, 'BB', 82, 'DE', 52.16587000, 13.65935000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q548429'),
(25628, 'Groß Laasch', 3007, 'MV', 82, 'DE', 53.34646000, 11.54919000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q548429'),
(25629, 'Groß Lindow', 3013, 'BB', 82, 'DE', 52.23333000, 14.53333000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q624110'),
(25630, 'Groß Miltzow', 3007, 'MV', 82, 'DE', 53.53565000, 13.59354000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q50930'),
(25631, 'Groß Munzel', 3008, 'NI', 82, 'DE', 52.36554000, 9.47828000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q1453469'),
(25632, 'Groß Nemerow', 3007, 'MV', 82, 'DE', 53.47342000, 13.22482000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q45941'),
(25633, 'Groß Oesingen', 3008, 'NI', 82, 'DE', 52.64761000, 10.46366000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q709065'),
(25634, 'Groß Pankow', 3013, 'BB', 82, 'DE', 53.11952000, 12.04975000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q370074'),
(25635, 'Groß Quenstedt', 3011, 'ST', 82, 'DE', 51.93333000, 11.11667000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q370074'),
(25636, 'Groß Rheide', 3005, 'SH', 82, 'DE', 54.44154000, 9.43508000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q370074'),
(25637, 'Groß Rodensleben', 3011, 'ST', 82, 'DE', 52.12139000, 11.38367000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q688602'),
(25638, 'Groß Rosenburg', 3011, 'ST', 82, 'DE', 51.91696000, 11.89184000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q177585'),
(25639, 'Groß Santersleben', 3011, 'ST', 82, 'DE', 52.19143000, 11.45486000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q688685'),
(25640, 'Groß Twülpstedt', 3008, 'NI', 82, 'DE', 52.37361000, 10.91929000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q632740'),
(25641, 'Groß Wittensee', 3005, 'SH', 82, 'DE', 54.40000000, 9.76667000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q632740'),
(25642, 'Groß Wokern', 3007, 'MV', 82, 'DE', 53.75135000, 12.49249000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q643643'),
(25643, 'Groß-Bieberau', 3018, 'HE', 82, 'DE', 49.80064000, 8.82430000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q539579'),
(25644, 'Groß-Gerau', 3018, 'HE', 82, 'DE', 49.92139000, 8.48255000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q251689'),
(25645, 'Groß-Rohrheim', 3018, 'HE', 82, 'DE', 49.72111000, 8.48278000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q970081'),
(25646, 'Groß-Umstadt', 3018, 'HE', 82, 'DE', 49.86899000, 8.93210000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q516474'),
(25647, 'Groß-Zimmern', 3018, 'HE', 82, 'DE', 49.87410000, 8.82898000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q622589'),
(25648, 'Großaitingen', 3009, 'BY', 82, 'DE', 48.22753000, 10.77948000, '2019-10-05 22:40:57', '2020-05-01 17:22:47', 1, 'Q520900'),
(25649, 'Großalmerode', 3018, 'HE', 82, 'DE', 51.25858000, 9.78450000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q538635'),
(25650, 'Großbardau', 3021, 'SN', 82, 'DE', 51.20419000, 12.69848000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q1548027'),
(25651, 'Großbardorf', 3009, 'BY', 82, 'DE', 50.26667000, 10.36667000, '2019-10-05 22:40:57', '2020-05-01 17:22:47', 1, 'Q583472'),
(25652, 'Großbartloff', 3015, 'TH', 82, 'DE', 51.25000000, 10.21667000, '2019-10-05 22:40:57', '2020-05-01 17:22:50', 1, 'Q628543'),
(25653, 'Großbeeren', 3013, 'BB', 82, 'DE', 52.35862000, 13.30994000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q628528'),
(25654, 'Großbettlingen', 3006, 'BW', 82, 'DE', 48.59052000, 9.30782000, '2019-10-05 22:40:57', '2020-05-01 17:22:47', 1, 'Q83220'),
(25655, 'Großbodungen', 3015, 'TH', 82, 'DE', 51.47572000, 10.48104000, '2019-10-05 22:40:57', '2020-05-01 17:22:50', 1, 'Q696436'),
(25656, 'Großbothen', 3021, 'SN', 82, 'DE', 51.18784000, 12.75101000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q560850'),
(25657, 'Großbottwar', 3006, 'BW', 82, 'DE', 49.00147000, 9.29348000, '2019-10-05 22:40:57', '2020-05-01 17:22:47', 1, 'Q61830'),
(25658, 'Großbreitenbach', 3015, 'TH', 82, 'DE', 50.58335000, 11.00955000, '2019-10-05 22:40:57', '2020-05-01 17:22:50', 1, 'Q550878'),
(25659, 'Großdubrau', 3021, 'SN', 82, 'DE', 51.25422000, 14.45897000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q93233'),
(25660, 'Großeibstadt', 3009, 'BY', 82, 'DE', 50.30102000, 10.40980000, '2019-10-05 22:40:57', '2020-05-01 17:22:47', 1, 'Q566627'),
(25661, 'Großenaspe', 3005, 'SH', 82, 'DE', 53.98333000, 9.96667000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q566627'),
(25662, 'Großenehrich', 3015, 'TH', 82, 'DE', 51.24838000, 10.83458000, '2019-10-05 22:40:57', '2020-05-01 17:22:50', 1, 'Q558556'),
(25663, 'Großengottern', 3015, 'TH', 82, 'DE', 51.14821000, 10.56232000, '2019-10-05 22:40:57', '2020-05-01 17:22:50', 1, 'Q686889'),
(25664, 'Großenhain', 3021, 'SN', 82, 'DE', 51.28951000, 13.53350000, '2019-10-05 22:40:57', '2020-05-01 17:22:49', 1, 'Q8714'),
(25665, 'Großenkneten', 3008, 'NI', 82, 'DE', 52.94377000, 8.25323000, '2019-10-05 22:40:57', '2020-05-01 17:22:48', 1, 'Q570266'),
(25666, 'Großenlüder', 3018, 'HE', 82, 'DE', 50.59250000, 9.54231000, '2019-10-05 22:40:58', '2020-05-01 17:22:48', 1, 'Q632739'),
(25667, 'Großensee', 3005, 'SH', 82, 'DE', 53.61263000, 10.33961000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q632739'),
(25668, 'Großenseebach', 3009, 'BY', 82, 'DE', 49.63251000, 10.87483000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q632739'),
(25669, 'Großenstein', 3015, 'TH', 82, 'DE', 50.90000000, 12.20000000, '2019-10-05 22:40:58', '2020-05-01 17:22:50', 1, 'Q554764'),
(25670, 'Großenwiehe', 3005, 'SH', 82, 'DE', 54.70000000, 9.25000000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q544597'),
(25671, 'Großerlach', 3006, 'BW', 82, 'DE', 49.05368000, 9.51356000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q81120'),
(25672, 'Großgoltern', 3008, 'NI', 82, 'DE', 52.33261000, 9.50111000, '2019-10-05 22:40:58', '2020-05-01 17:22:48', 1, 'Q1549275'),
(25673, 'Großhabersdorf', 3009, 'BY', 82, 'DE', 49.40000000, 10.78333000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q521557'),
(25674, 'Großhansdorf', 3005, 'SH', 82, 'DE', 53.66667000, 10.28333000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q553192'),
(25675, 'Großharthau', 3021, 'SN', 82, 'DE', 51.10694000, 14.09911000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q93234'),
(25676, 'Großhartmannsdorf', 3021, 'SN', 82, 'DE', 50.80000000, 13.33333000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q70957'),
(25677, 'Großheirath', 3009, 'BY', 82, 'DE', 50.17603000, 10.95050000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q31911'),
(25678, 'Großhennersdorf', 3021, 'SN', 82, 'DE', 50.98597000, 14.78776000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q502696'),
(25679, 'Großheubach', 3009, 'BY', 82, 'DE', 49.72834000, 9.22280000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q502986'),
(25680, 'Großholbach', 3019, 'RP', 82, 'DE', 50.45000000, 7.88333000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q502986'),
(25681, 'Großkarlbach', 3019, 'RP', 82, 'DE', 49.53833000, 8.22472000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q22525'),
(25682, 'Großkarolinenfeld', 3009, 'BY', 82, 'DE', 47.89102000, 12.08101000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q532876'),
(25683, 'Großkayna', 3011, 'ST', 82, 'DE', 51.29068000, 11.93269000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q1549375'),
(25684, 'Großkmehlen', 3013, 'BB', 82, 'DE', 51.37901000, 13.72501000, '2019-10-05 22:40:58', '2020-05-01 17:22:48', 1, 'Q585221'),
(25685, 'Großkorbetha', 3011, 'ST', 82, 'DE', 51.26002000, 12.03012000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q673108'),
(25686, 'Großkrotzenburg', 3018, 'HE', 82, 'DE', 50.08333000, 8.98333000, '2019-10-05 22:40:58', '2020-05-01 17:22:48', 1, 'Q634120'),
(25687, 'Großkugel', 3011, 'ST', 82, 'DE', 51.41728000, 12.14641000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q2561873'),
(25688, 'Großlangheim', 3009, 'BY', 82, 'DE', 49.75620000, 10.24065000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q504795'),
(25689, 'Großlehna', 3021, 'SN', 82, 'DE', 51.30687000, 12.17190000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q1549432'),
(25690, 'Großlittgen', 3019, 'RP', 82, 'DE', 50.02787000, 6.79865000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q656098'),
(25691, 'Großmaischeid', 3019, 'RP', 82, 'DE', 50.50000000, 7.63333000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q651274'),
(25692, 'Großmonra', 3015, 'TH', 82, 'DE', 51.21299000, 11.29578000, '2019-10-05 22:40:58', '2020-05-01 17:22:50', 1, 'Q635357'),
(25693, 'Großnaundorf', 3021, 'SN', 82, 'DE', 51.20000000, 13.93333000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q635357'),
(25694, 'Großniedesheim', 3019, 'RP', 82, 'DE', 49.57528000, 8.31222000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q653842'),
(25695, 'Großolbersdorf', 3021, 'SN', 82, 'DE', 50.70000000, 13.08333000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q57769'),
(25696, 'Großostheim', 3009, 'BY', 82, 'DE', 49.91985000, 9.07596000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q504246'),
(25697, 'Großpostwitz', 3021, 'SN', 82, 'DE', 51.12083000, 14.44065000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q33441285'),
(25698, 'Großpösna', 3021, 'SN', 82, 'DE', 51.26667000, 12.50000000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q10750'),
(25699, 'Großreuth bei Schweinau', 3009, 'BY', 82, 'DE', 49.43449000, 11.02273000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q10750'),
(25700, 'Großrinderfeld', 3006, 'BW', 82, 'DE', 49.66536000, 9.73356000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q61728'),
(25701, 'Großrosseln', 3020, 'SL', 82, 'DE', 49.20296000, 6.84148000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q39797'),
(25702, 'Großrudestedt', 3015, 'TH', 82, 'DE', 51.09305000, 11.09977000, '2019-10-05 22:40:58', '2020-05-01 17:22:50', 1, 'Q49283577'),
(25703, 'Großräschen', 3013, 'BB', 82, 'DE', 51.58760000, 14.01093000, '2019-10-05 22:40:58', '2020-05-01 17:22:48', 1, 'Q588844'),
(25704, 'Großröhrsdorf', 3021, 'SN', 82, 'DE', 51.14528000, 14.01917000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q81736'),
(25705, 'Großrückerswalde', 3021, 'SN', 82, 'DE', 50.63333000, 13.11667000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q57773'),
(25706, 'Großschirma', 3021, 'SN', 82, 'DE', 50.96602000, 13.28590000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q71117'),
(25707, 'Großschweidnitz', 3021, 'SN', 82, 'DE', 51.06939000, 14.64297000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q502454'),
(25708, 'Großschönau', 3021, 'SN', 82, 'DE', 50.90000000, 14.68333000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q502454'),
(25709, 'Großsolt', 3005, 'SH', 82, 'DE', 54.70000000, 9.51667000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q641731'),
(25710, 'Großthiemig', 3013, 'BB', 82, 'DE', 51.38333000, 13.66667000, '2019-10-05 22:40:58', '2020-05-01 17:22:48', 1, 'Q551468'),
(25711, 'Großwallstadt', 3009, 'BY', 82, 'DE', 49.87936000, 9.15338000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q31622'),
(25712, 'Großweil', 3009, 'BY', 82, 'DE', 47.67598000, 11.30108000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q505796'),
(25713, 'Großweitzschen', 3021, 'SN', 82, 'DE', 51.15845000, 13.04517000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q70961'),
(25714, 'Großwoltersdorf', 3013, 'BB', 82, 'DE', 53.06667000, 13.10000000, '2019-10-05 22:40:58', '2020-05-01 17:22:48', 1, 'Q671788'),
(25715, 'Großörner', 3011, 'ST', 82, 'DE', 51.61527000, 11.48980000, '2019-10-05 22:40:58', '2020-05-01 17:22:49', 1, 'Q1549804'),
(25716, 'Grub', 3009, 'BY', 82, 'DE', 50.10285000, 11.92904000, '2019-10-05 22:40:58', '2019-10-05 22:40:58', 1, 'Q32046052'),
(25717, 'Grube', 3005, 'SH', 82, 'DE', 54.23333000, 11.03333000, '2019-10-05 22:40:58', '2019-10-05 22:40:58', 1, 'Q32046052'),
(25718, 'Gruibingen', 3006, 'BW', 82, 'DE', 48.59483000, 9.64389000, '2019-10-05 22:40:58', '2019-10-05 22:40:58', 1, 'Q82013'),
(25719, 'Grunewald', 3010, 'BE', 82, 'DE', 52.48338000, 13.26586000, '2019-10-05 22:40:58', '2019-10-05 22:40:58', 1, 'Q2874431'),
(25720, 'Gräfelfing', 3009, 'BY', 82, 'DE', 48.11878000, 11.42939000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q504854'),
(25721, 'Gräfenberg', 3009, 'BY', 82, 'DE', 49.64426000, 11.24971000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q504854'),
(25722, 'Gräfendorf', 3009, 'BY', 82, 'DE', 50.12267000, 9.74050000, '2019-10-05 22:40:58', '2020-05-01 17:22:47', 1, 'Q49283607'),
(25723, 'Gräfenhain', 3015, 'TH', 82, 'DE', 50.81667000, 10.70000000, '2019-10-05 22:40:59', '2020-05-01 17:22:50', 1, 'Q49283607'),
(25724, 'Gräfenhainichen', 3011, 'ST', 82, 'DE', 51.72892000, 12.45651000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q33441461'),
(25725, 'Gräfenroda', 3015, 'TH', 82, 'DE', 50.74781000, 10.81063000, '2019-10-05 22:40:59', '2020-05-01 17:22:50', 1, 'Q559966'),
(25726, 'Grävenwiesbach', 3018, 'HE', 82, 'DE', 50.39024000, 8.45690000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q624187'),
(25727, 'Gröbenzell', 3009, 'BY', 82, 'DE', 48.20000000, 11.36667000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q624187'),
(25728, 'Gröbers', 3011, 'ST', 82, 'DE', 51.43050000, 12.11623000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q1551614'),
(25729, 'Gröbzig', 3011, 'ST', 82, 'DE', 51.68226000, 11.87453000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q705627'),
(25730, 'Gröden', 3013, 'BB', 82, 'DE', 51.40000000, 13.56667000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q705627'),
(25731, 'Grömitz', 3005, 'SH', 82, 'DE', 54.15000000, 10.96667000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q541739'),
(25732, 'Gröningen', 3011, 'ST', 82, 'DE', 51.93744000, 11.21601000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q571925'),
(25733, 'Grönwohld', 3005, 'SH', 82, 'DE', 53.64162000, 10.40784000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q641644'),
(25734, 'Grünau', 3010, 'BE', 82, 'DE', 52.41642000, 13.58039000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q643857'),
(25735, 'Grünbach', 3021, 'SN', 82, 'DE', 50.44995000, 12.36186000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q46757'),
(25736, 'Grünberg', 3018, 'HE', 82, 'DE', 50.59403000, 8.95866000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q46757'),
(25737, 'Grünenbach', 3009, 'BY', 82, 'DE', 47.62864000, 10.00843000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q46757'),
(25738, 'Grünendeich', 3008, 'NI', 82, 'DE', 53.56952000, 9.61218000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q46757'),
(25739, 'Grünhain', 3021, 'SN', 82, 'DE', 50.58019000, 12.80695000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q1552242'),
(25740, 'Grünhainichen', 3021, 'SN', 82, 'DE', 50.76720000, 13.15366000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q57776'),
(25741, 'Grünheide', 3013, 'BB', 82, 'DE', 52.42343000, 13.81324000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q622449'),
(25742, 'Grünkraut', 3006, 'BW', 82, 'DE', 47.74417000, 9.65588000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q533937'),
(25743, 'Grünsfeld', 3006, 'BW', 82, 'DE', 49.60949000, 9.74725000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q61845'),
(25744, 'Grünstadt', 3019, 'RP', 82, 'DE', 49.56302000, 8.16279000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q22956'),
(25745, 'Grünwald', 3009, 'BY', 82, 'DE', 48.03947000, 11.52320000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q220090'),
(25746, 'Gschwend', 3006, 'BW', 82, 'DE', 48.93333000, 9.74436000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q513443'),
(25747, 'Gstadt am Chiemsee', 3009, 'BY', 82, 'DE', 47.88453000, 12.41813000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q629619'),
(25748, 'Guben', 3013, 'BB', 82, 'DE', 51.94987000, 14.71447000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q494747'),
(25749, 'Gudensberg', 3018, 'HE', 82, 'DE', 51.17706000, 9.36748000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q558498'),
(25750, 'Guderhandviertel', 3008, 'NI', 82, 'DE', 53.54919000, 9.60960000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q558498'),
(25751, 'Gudow', 3005, 'SH', 82, 'DE', 53.55556000, 10.77021000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q632215'),
(25752, 'Gummersbach', 3017, 'NW', 82, 'DE', 51.02608000, 7.56473000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q4181'),
(25753, 'Gumtow', 3013, 'BB', 82, 'DE', 52.98333000, 12.25000000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q628142'),
(25754, 'Gundelfingen', 3006, 'BW', 82, 'DE', 48.04246000, 7.86570000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q535222'),
(25755, 'Gundelfingen', 3009, 'BY', 82, 'DE', 48.55347000, 10.37223000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q32048627'),
(25756, 'Gundelsheim', 3006, 'BW', 82, 'DE', 49.28330000, 9.16037000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q32048635'),
(25757, 'Gundelsheim', 3009, 'BY', 82, 'DE', 49.93708000, 10.91990000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q32048635'),
(25758, 'Gundersheim', 3019, 'RP', 82, 'DE', 49.69611000, 8.20250000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q566919'),
(25759, 'Gundremmingen', 3009, 'BY', 82, 'DE', 48.50000000, 10.40000000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q507819'),
(25760, 'Guntersblum', 3019, 'RP', 82, 'DE', 49.79750000, 8.34556000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q656289'),
(25761, 'Gunzenhausen', 3009, 'BY', 82, 'DE', 49.11663000, 10.75971000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q503640'),
(25762, 'Gusenburg', 3019, 'RP', 82, 'DE', 49.63333000, 6.90000000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q429087'),
(25763, 'Gusterath', 3019, 'RP', 82, 'DE', 49.70000000, 6.71667000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q429087'),
(25764, 'Gutach', 3006, 'BW', 82, 'DE', 48.24844000, 8.21293000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q557834'),
(25765, 'Gutach im Breisgau', 3006, 'BW', 82, 'DE', 48.11667000, 7.98333000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q538615'),
(25766, 'Gutenberg', 3019, 'RP', 82, 'DE', 49.88333000, 7.80000000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q538615'),
(25767, 'Gutenberg', 3011, 'ST', 82, 'DE', 51.54710000, 11.97181000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q32048902'),
(25768, 'Gutenstetten', 3009, 'BY', 82, 'DE', 49.61667000, 10.63333000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q505073'),
(25769, 'Gutenzell-Hürbel', 3006, 'BW', 82, 'DE', 48.11667000, 9.98333000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q528047'),
(25770, 'Guttau', 3021, 'SN', 82, 'DE', 51.25904000, 14.56132000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q93243'),
(25771, 'Guxhagen', 3018, 'HE', 82, 'DE', 51.20000000, 9.48333000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q632407'),
(25772, 'Gyhum', 3008, 'NI', 82, 'DE', 53.21667000, 9.31667000, '2019-10-05 22:40:59', '2019-10-05 22:40:59', 1, 'Q505611'),
(25773, 'Gägelow', 3007, 'MV', 82, 'DE', 53.68558000, 11.89639000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q505611'),
(25774, 'Gärtringen', 3006, 'BW', 82, 'DE', 48.64176000, 8.90073000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q550684'),
(25775, 'Göda', 3021, 'SN', 82, 'DE', 51.17800000, 14.31951000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q93232'),
(25776, 'Göggingen', 3006, 'BW', 82, 'DE', 48.86123000, 9.88398000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q93232'),
(25777, 'Göllheim', 3019, 'RP', 82, 'DE', 49.59500000, 8.05083000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q566711'),
(25778, 'Gönnheim', 3019, 'RP', 82, 'DE', 49.44806000, 8.23861000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q553267'),
(25779, 'Göppingen', 3006, 'BW', 82, 'DE', 48.70354000, 9.65209000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q4072'),
(25780, 'Görisried', 3009, 'BY', 82, 'DE', 47.70668000, 10.51015000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q513882'),
(25781, 'Görlitz', 3021, 'SN', 82, 'DE', 51.15518000, 14.98853000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q4077'),
(25782, 'Görmin', 3007, 'MV', 82, 'DE', 53.99092000, 13.27054000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q668483'),
(25783, 'Görsbach', 3015, 'TH', 82, 'DE', 51.46224000, 10.93706000, '2019-10-05 22:40:59', '2020-05-01 17:22:50', 1, 'Q668483'),
(25784, 'Görwihl', 3006, 'BW', 82, 'DE', 47.64275000, 8.07482000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q388453'),
(25785, 'Görzig', 3011, 'ST', 82, 'DE', 51.66381000, 11.99759000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q689757'),
(25786, 'Görzke', 3013, 'BB', 82, 'DE', 52.17179000, 12.37506000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q625844'),
(25787, 'Gössenheim', 3009, 'BY', 82, 'DE', 50.01667000, 9.78333000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q34622051'),
(25788, 'Göttingen', 3008, 'NI', 82, 'DE', 51.53443000, 9.93228000, '2019-10-05 22:40:59', '2020-05-01 17:22:48', 1, 'Q3033'),
(25789, 'Gößnitz', 3015, 'TH', 82, 'DE', 50.88902000, 12.43292000, '2019-10-05 22:40:59', '2020-05-01 17:22:50', 1, 'Q3033'),
(25790, 'Gößweinstein', 3009, 'BY', 82, 'DE', 49.76898000, 11.33841000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q507319'),
(25791, 'Gückingen', 3019, 'RP', 82, 'DE', 50.39091000, 8.00726000, '2019-10-05 22:40:59', '2020-05-01 17:22:49', 1, 'Q560389'),
(25792, 'Güglingen', 3006, 'BW', 82, 'DE', 49.06642000, 9.00175000, '2019-10-05 22:40:59', '2020-05-01 17:22:47', 1, 'Q53803'),
(25793, 'Gülzow', 3005, 'SH', 82, 'DE', 53.45000000, 10.50000000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q53803'),
(25794, 'Güntersleben', 3009, 'BY', 82, 'DE', 49.86972000, 9.90500000, '2019-10-05 22:41:00', '2020-05-01 17:22:47', 1, 'Q510716'),
(25795, 'Günthersdorf', 3011, 'ST', 82, 'DE', 51.34570000, 12.17175000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q608020'),
(25796, 'Günzach', 3009, 'BY', 82, 'DE', 47.82491000, 10.43547000, '2019-10-05 22:41:00', '2020-05-01 17:22:47', 1, 'Q510734'),
(25797, 'Günzburg', 3009, 'BY', 82, 'DE', 48.45599000, 10.27695000, '2019-10-05 22:41:00', '2020-05-01 17:22:47', 1, 'Q489685'),
(25798, 'Güsten', 3011, 'ST', 82, 'DE', 51.79637000, 11.61246000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q246502'),
(25799, 'Güster', 3005, 'SH', 82, 'DE', 53.53851000, 10.67607000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q246502'),
(25800, 'Güstrow', 3007, 'MV', 82, 'DE', 53.79720000, 12.17337000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q16147'),
(25801, 'Gütenbach', 3006, 'BW', 82, 'DE', 48.05000000, 8.15000000, '2019-10-05 22:41:00', '2020-05-01 17:22:47', 1, 'Q81429'),
(25802, 'Gütersloh', 3017, 'NW', 82, 'DE', 51.90693000, 8.37853000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q3771'),
(25803, 'Gützkow', 3007, 'MV', 82, 'DE', 53.72432000, 13.10664000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q3771'),
(25804, 'Haag an der Amper', 3009, 'BY', 82, 'DE', 48.45840000, 11.82796000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q517447'),
(25805, 'Haag in Oberbayern', 3009, 'BY', 82, 'DE', 48.16196000, 12.17942000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q519928'),
(25806, 'Haan', 3017, 'NW', 82, 'DE', 51.19382000, 7.01330000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2974'),
(25807, 'Haar', 3009, 'BY', 82, 'DE', 48.10881000, 11.72653000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2974'),
(25808, 'Haarbach', 3009, 'BY', 82, 'DE', 48.50000000, 13.15000000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q261515'),
(25809, 'Habach', 3009, 'BY', 82, 'DE', 47.73333000, 11.28333000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q261515'),
(25810, 'Hachenburg', 3019, 'RP', 82, 'DE', 50.65998000, 7.82276000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q534476'),
(25811, 'Hackenheim', 3019, 'RP', 82, 'DE', 49.82524000, 7.89907000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q561674'),
(25812, 'Hadamar', 3018, 'HE', 82, 'DE', 50.44593000, 8.04253000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q565530'),
(25813, 'Hadmersleben', 3011, 'ST', 82, 'DE', 51.99275000, 11.30283000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q673201'),
(25814, 'Hafenlohr', 3009, 'BY', 82, 'DE', 49.86944000, 9.60222000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q508592'),
(25815, 'Hage', 3008, 'NI', 82, 'DE', 53.60274000, 7.28527000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q634309'),
(25816, 'Hagelstadt', 3009, 'BY', 82, 'DE', 48.90000000, 12.21667000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q634309'),
(25817, 'Hagen', 3008, 'NI', 82, 'DE', 52.19629000, 7.98041000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q630969'),
(25818, 'Hagen', 3017, 'NW', 82, 'DE', 51.36081000, 7.47168000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q2871'),
(25819, 'Hagen im Bremischen', 3008, 'NI', 82, 'DE', 53.35707000, 8.64341000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q585546'),
(25820, 'Hagenbach', 3019, 'RP', 82, 'DE', 49.01734000, 8.25024000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q541669'),
(25821, 'Hagenburg', 3008, 'NI', 82, 'DE', 52.43367000, 9.32473000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q645430'),
(25822, 'Hagenbüchach', 3009, 'BY', 82, 'DE', 49.53333000, 10.76667000, '2019-10-05 22:41:00', '2020-05-01 17:22:47', 1, 'Q507049'),
(25823, 'Hagenow', 3007, 'MV', 82, 'DE', 53.43134000, 11.19159000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q565611'),
(25824, 'Hagnau', 3006, 'BW', 82, 'DE', 47.67666000, 9.31787000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q539889'),
(25825, 'Hahnbach', 3009, 'BY', 82, 'DE', 49.53389000, 11.80302000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q503372'),
(25826, 'Hahnheim', 3019, 'RP', 82, 'DE', 49.86083000, 8.23694000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q617192'),
(25827, 'Hahnstätten', 3019, 'RP', 82, 'DE', 50.30000000, 8.06667000, '2019-10-05 22:41:00', '2020-05-01 17:22:49', 1, 'Q553988'),
(25828, 'Haibach', 3009, 'BY', 82, 'DE', 49.96444000, 9.20722000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q553988'),
(25829, 'Haidmühle', 3009, 'BY', 82, 'DE', 48.82565000, 13.77649000, '2019-10-05 22:41:00', '2020-05-01 17:22:47', 1, 'Q505927'),
(25830, 'Haiger', 3018, 'HE', 82, 'DE', 50.74162000, 8.20778000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q516448'),
(25831, 'Haigerloch', 3006, 'BW', 82, 'DE', 48.36614000, 8.80357000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q505856'),
(25832, 'Haimhausen', 3009, 'BY', 82, 'DE', 48.31545000, 11.55453000, '2019-10-05 22:41:00', '2019-10-05 22:41:00', 1, 'Q512178');

