INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(41802, 'Ferques', 4828, 'HDF', 75, 'FR', 50.83002000, 1.75994000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q587192'),
(41803, 'Ferrals-les-Corbières', 4799, 'OCC', 75, 'FR', 43.15000000, 2.73333000, '2019-10-05 22:48:01', '2020-05-01 17:22:46', 1, 'Q587192'),
(41804, 'Fe<PERSON><PERSON>', 4820, 'GES', 75, 'FR', 47.49436000, 7.31372000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q392059'),
(41805, '<PERSON><PERSON><PERSON><PERSON>la-Grande', 4828, 'HDF', 75, 'FR', 50.25521000, 3.99288000, '2019-10-05 22:48:01', '2020-05-01 17:22:45', 1, 'Q392059'),
(41806, '<PERSON>rri<PERSON>-en-<PERSON>rie', 4796, 'IDF', 75, 'FR', 48.82352000, 2.70664000, '2019-10-05 22:48:01', '2020-05-01 17:22:43', 1, 'Q392059'),
(41807, 'Fesches-le-Châtel', 4825, 'BFC', 75, 'FR', 47.52415000, 6.90535000, '2019-10-05 22:48:01', '2020-05-01 17:22:44', 1, 'Q392059'),
(41808, 'Fessenheim', 4820, 'GES', 75, 'FR', 47.91565000, 7.53499000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q392059'),
(41809, 'Festubert', 4828, 'HDF', 75, 'FR', 50.54250000, 2.73593000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q392059'),
(41810, 'Feucherolles', 4796, 'IDF', 75, 'FR', 48.87002000, 1.97402000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q273897'),
(41811, 'Feuchy', 4828, 'HDF', 75, 'FR', 50.29439000, 2.84335000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q273897'),
(41812, 'Feuquières', 4828, 'HDF', 75, 'FR', 49.64677000, 1.84784000, '2019-10-05 22:48:01', '2020-05-01 17:22:45', 1, 'Q273897'),
(41813, 'Feuquières-en-Vimeu', 4828, 'HDF', 75, 'FR', 50.05982000, 1.60465000, '2019-10-05 22:48:01', '2020-05-01 17:22:45', 1, 'Q273897'),
(41814, 'Feurs', 4798, 'ARA', 75, 'FR', 45.73337000, 4.22755000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q273897'),
(41815, 'Feytiat', 4795, 'NAQ', 75, 'FR', 45.80905000, 1.33033000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q273897'),
(41816, 'Feyzin', 4798, 'ARA', 75, 'FR', 45.67287000, 4.85894000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q13600'),
(41817, 'Figanières', 4812, 'PAC', 75, 'FR', 43.56882000, 6.49722000, '2019-10-05 22:48:01', '2020-05-01 17:22:46', 1, 'Q13600'),
(41818, 'Figari', 4806, '20R', 75, 'FR', 41.48792000, 9.13013000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q13600'),
(41819, 'Figeac', 4799, 'OCC', 75, 'FR', 44.60880000, 2.03187000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q207614'),
(41820, 'Fillinges', 4798, 'ARA', 75, 'FR', 46.15944000, 6.34237000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q207614'),
(41821, 'Fillé', 4802, 'PDL', 75, 'FR', 47.90011000, 0.12543000, '2019-10-05 22:48:01', '2020-05-01 17:22:46', 1, 'Q727670'),
(41822, 'Finhan', 4799, 'OCC', 75, 'FR', 43.91335000, 1.22120000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q727670'),
(41823, 'Firmi', 4799, 'OCC', 75, 'FR', 44.54106000, 2.30764000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q727670'),
(41824, 'Firminy', 4798, 'ARA', 75, 'FR', 45.38956000, 4.28860000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q387642'),
(41825, 'Fismes', 4820, 'GES', 75, 'FR', 49.30773000, 3.68607000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41826, 'Fitilieu', 4798, 'ARA', 75, 'FR', 45.54744000, 5.56194000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41827, 'Fitz-James', 4828, 'HDF', 75, 'FR', 49.39113000, 2.43070000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q690520'),
(41828, 'Flamanville', 4804, 'NOR', 75, 'FR', 49.53274000, -1.86560000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q690520'),
(41829, 'Flassans-sur-Issole', 4812, 'PAC', 75, 'FR', 43.36856000, 6.22154000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q840276'),
(41830, 'Flaviac', 4798, 'ARA', 75, 'FR', 44.74777000, 4.67434000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q840276'),
(41831, 'Flavigny-sur-Moselle', 4820, 'GES', 75, 'FR', 48.56567000, 6.18878000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1060441'),
(41832, 'Flavin', 4799, 'OCC', 75, 'FR', 44.28890000, 2.60513000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1060441'),
(41833, 'Flavy-le-Martel', 4828, 'HDF', 75, 'FR', 49.71299000, 3.19150000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1060441'),
(41834, 'Flaxlanden', 4820, 'GES', 75, 'FR', 47.69547000, 7.31484000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1060441'),
(41835, 'Flayosc', 4812, 'PAC', 75, 'FR', 43.53396000, 6.39660000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q910050'),
(41836, 'Flers', 4804, 'NOR', 75, 'FR', 48.73722000, -0.57466000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q910050'),
(41837, 'Flers-en-Escrebieux', 4828, 'HDF', 75, 'FR', 50.39790000, 3.06038000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q910050'),
(41838, 'Flesselles', 4828, 'HDF', 75, 'FR', 50.00202000, 2.26119000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q910050'),
(41839, 'Fleurance', 4799, 'OCC', 75, 'FR', 43.84824000, 0.66302000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q473923'),
(41840, 'Fleurbaix', 4828, 'HDF', 75, 'FR', 50.65061000, 2.83305000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1024755'),
(41841, 'Fleurey-sur-Ouche', 4825, 'BFC', 75, 'FR', 47.31182000, 4.85911000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q23928'),
(41842, 'Fleurie', 4798, 'ARA', 75, 'FR', 46.19219000, 4.69747000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1356894'),
(41843, 'Fleurieu-sur-Saône', 4798, 'ARA', 75, 'FR', 45.86095000, 4.84633000, '2019-10-05 22:48:01', '2020-05-01 17:22:43', 1, 'Q977460'),
(41844, 'Fleurines', 4828, 'HDF', 75, 'FR', 49.25901000, 2.58385000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q819566'),
(41845, 'Fleury', 4799, 'OCC', 75, 'FR', 43.23095000, 3.13745000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q819566'),
(41846, 'Fleury', 4820, 'GES', 75, 'FR', 49.04181000, 6.19329000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q819566'),
(41847, 'Fleury-Mérogis', 4796, 'IDF', 75, 'FR', 48.63730000, 2.36378000, '2019-10-05 22:48:02', '2020-05-01 17:22:43', 1, 'Q828640'),
(41848, 'Fleury-la-Vallée', 4825, 'BFC', 75, 'FR', 47.86672000, 3.44908000, '2019-10-05 22:48:02', '2020-05-01 17:22:44', 1, 'Q828640'),
(41849, 'Fleury-les-Aubrais', 4818, 'CVL', 75, 'FR', 47.93328000, 1.91811000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q857771'),
(41850, 'Fleury-sur-Andelle', 4804, 'NOR', 75, 'FR', 49.36176000, 1.35599000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q857771'),
(41851, 'Fleury-sur-Orne', 4804, 'NOR', 75, 'FR', 49.14851000, -0.37508000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q516709'),
(41852, 'Flines-lez-Raches', 4828, 'HDF', 75, 'FR', 50.41667000, 3.18333000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q516709'),
(41853, 'Flines-lès-Mortagne', 4828, 'HDF', 75, 'FR', 50.50281000, 3.46495000, '2019-10-05 22:48:02', '2020-05-01 17:22:45', 1, 'Q516709'),
(41854, 'Flins-sur-Seine', 4796, 'IDF', 75, 'FR', 48.96523000, 1.87314000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q516709'),
(41855, 'Flixecourt', 4828, 'HDF', 75, 'FR', 50.01465000, 2.08095000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q516709'),
(41856, 'Flize', 4820, 'GES', 75, 'FR', 49.69875000, 4.77171000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1000134'),
(41857, 'Flogny-la-Chapelle', 4825, 'BFC', 75, 'FR', 47.95000000, 3.86667000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1000134'),
(41858, 'Floing', 4820, 'GES', 75, 'FR', 49.72216000, 4.92947000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1000134'),
(41859, 'Floirac', 4795, 'NAQ', 75, 'FR', 44.83238000, -0.51411000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1000134'),
(41860, 'Florac', 4799, 'OCC', 75, 'FR', 44.32632000, 3.59301000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q20097'),
(41861, 'Florange', 4820, 'GES', 75, 'FR', 49.32373000, 6.12120000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q20097'),
(41862, 'Florensac', 4799, 'OCC', 75, 'FR', 43.38301000, 3.46638000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q20097'),
(41863, 'Flourens', 4799, 'OCC', 75, 'FR', 43.59273000, 1.56259000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q20097'),
(41864, 'Fléac', 4795, 'NAQ', 75, 'FR', 45.66667000, 0.10000000, '2019-10-05 22:48:02', '2020-05-01 17:22:45', 1, 'Q20097'),
(41865, 'Fléville-devant-Nancy', 4820, 'GES', 75, 'FR', 48.62495000, 6.20325000, '2019-10-05 22:48:02', '2020-05-01 17:22:44', 1, 'Q20097'),
(41866, 'Foissiat', 4798, 'ARA', 75, 'FR', 46.37150000, 5.17525000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q20097'),
(41867, 'Foix', 4799, 'OCC', 75, 'FR', 42.96046000, 1.60787000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q184392'),
(41868, 'Folembray', 4828, 'HDF', 75, 'FR', 49.54334000, 3.29119000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q184392'),
(41869, 'Folkling', 4820, 'GES', 75, 'FR', 49.14756000, 6.89482000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q184392'),
(41870, 'Follainville-Dennemont', 4796, 'IDF', 75, 'FR', 49.02194000, 1.71331000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q639332'),
(41871, 'Folschviller', 4820, 'GES', 75, 'FR', 49.07732000, 6.68358000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q21937'),
(41872, 'Fonbeauzard', 4799, 'OCC', 75, 'FR', 43.67830000, 1.43440000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q21937'),
(41873, 'Fondettes', 4818, 'CVL', 75, 'FR', 47.40350000, 0.59686000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q527676'),
(41874, 'Fonsorbes', 4799, 'OCC', 75, 'FR', 43.53524000, 1.22937000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q527676'),
(41875, 'Font-Romeu-Odeillo-Via', 4799, 'OCC', 75, 'FR', 42.50552000, 2.04011000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q458715'),
(41876, 'Fontaine', 4798, 'ARA', 75, 'FR', 45.19275000, 5.68821000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q458715'),
(41877, 'Fontaine-Notre-Dame', 4828, 'HDF', 75, 'FR', 50.16681000, 3.15812000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q458715'),
(41878, 'Fontaine-au-Pire', 4828, 'HDF', 75, 'FR', 50.13250000, 3.37667000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q367373'),
(41879, 'Fontaine-la-Guyon', 4818, 'CVL', 75, 'FR', 48.47270000, 1.31417000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q367373'),
(41880, 'Fontaine-la-Mallet', 4804, 'NOR', 75, 'FR', 49.53600000, 0.14625000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q367373'),
(41881, 'Fontaine-le-Bourg', 4804, 'NOR', 75, 'FR', 49.56451000, 1.16391000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q367373'),
(41882, 'Fontaine-le-Comte', 4795, 'NAQ', 75, 'FR', 46.53217000, 0.26176000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1454185'),
(41883, 'Fontaine-le-Dun', 4804, 'NOR', 75, 'FR', 49.81182000, 0.85095000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1454185'),
(41884, 'Fontaine-lès-Dijon', 4825, 'BFC', 75, 'FR', 47.34238000, 5.02007000, '2019-10-05 22:48:02', '2020-05-01 17:22:44', 1, 'Q46712'),
(41885, 'Fontaine-lès-Luxeuil', 4825, 'BFC', 75, 'FR', 47.85592000, 6.33482000, '2019-10-05 22:48:02', '2020-05-01 17:22:44', 1, 'Q46712'),
(41886, 'Fontaine-Étoupefour', 4804, 'NOR', 75, 'FR', 49.14600000, -0.45522000, '2019-10-05 22:48:02', '2020-05-01 17:22:45', 1, 'Q46712'),
(41887, 'Fontainebleau', 4796, 'IDF', 75, 'FR', 48.40908000, 2.70177000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q46712'),
(41888, 'Fontaines', 4825, 'BFC', 75, 'FR', 46.84905000, 4.77036000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q46712'),
(41889, 'Fontaines-Saint-Martin', 4798, 'ARA', 75, 'FR', 45.84420000, 4.85305000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1364086'),
(41890, 'Fontaines-sur-Saône', 4798, 'ARA', 75, 'FR', 45.83572000, 4.84490000, '2019-10-05 22:48:02', '2020-05-01 17:22:43', 1, 'Q1364086'),
(41891, 'Fontanil-Cornillon', 4798, 'ARA', 75, 'FR', 45.25280000, 5.66308000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1364086'),
(41892, 'Fontannes', 4798, 'ARA', 75, 'FR', 45.30149000, 3.76366000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1364086'),
(41893, 'Fontcouverte', 4795, 'NAQ', 75, 'FR', 45.76708000, -0.58682000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q1364086'),
(41894, 'Fontenay', 4804, 'NOR', 75, 'FR', 49.55993000, 0.18391000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, '********'),
(41895, 'Fontenay-Trésigny', 4796, 'IDF', 75, 'FR', 48.70647000, 2.87047000, '2019-10-05 22:48:02', '2020-05-01 17:22:43', 1, 'Q839540'),
(41896, 'Fontenay-aux-Roses', 4796, 'IDF', 75, 'FR', 48.79325000, 2.29275000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q839540'),
(41897, 'Fontenay-en-Parisis', 4796, 'IDF', 75, 'FR', 49.05371000, 2.45156000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, '********'),
(41898, 'Fontenay-le-Comte', 4802, 'PDL', 75, 'FR', 46.46720000, -0.80645000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q213497'),
(41899, 'Fontenay-le-Fleury', 4796, 'IDF', 75, 'FR', 48.81253000, 2.04863000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q243331'),
(41900, 'Fontenay-le-Marmion', 4804, 'NOR', 75, 'FR', 49.09346000, -0.35294000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q243331'),
(41901, 'Fontenay-lès-Briis', 4796, 'IDF', 75, 'FR', 48.61962000, 2.15276000, '2019-10-05 22:48:02', '2020-05-01 17:22:43', 1, 'Q168655'),
(41902, 'Fontenay-sous-Bois', 4796, 'IDF', 75, 'FR', 48.85442000, 2.48268000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q193899'),
(41903, 'Fontenay-sur-Loing', 4818, 'CVL', 75, 'FR', 48.10365000, 2.77542000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, '********'),
(41904, 'Fontenilles', 4799, 'OCC', 75, 'FR', 43.55286000, 1.19096000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, '********'),
(41905, 'Fontevraud-l\'Abbaye', 4802, 'PDL', 75, 'FR', 47.18333000, 0.05000000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q632276'),
(41906, 'Fontoy', 4820, 'GES', 75, 'FR', 49.35597000, 5.99250000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q632276'),
(41907, 'Fontvieille', 4812, 'PAC', 75, 'FR', 43.72806000, 4.70953000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q632276'),
(41908, 'Forbach', 4820, 'GES', 75, 'FR', 49.18848000, 6.89255000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q22703'),
(41909, 'Forcalqueiret', 4812, 'PAC', 75, 'FR', 43.33618000, 6.08346000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q22703'),
(41910, 'Forcalquier', 4812, 'PAC', 75, 'FR', 43.95927000, 5.77945000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q104431'),
(41911, 'Forest-sur-Marque', 4828, 'HDF', 75, 'FR', 50.63341000, 3.18939000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q104431'),
(41912, 'Forges-les-Bains', 4796, 'IDF', 75, 'FR', 48.62942000, 2.10264000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q247112'),
(41913, 'Forges-les-Eaux', 4804, 'NOR', 75, 'FR', 49.61391000, 1.54449000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q382746'),
(41914, 'Formerie', 4828, 'HDF', 75, 'FR', 49.64928000, 1.73106000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q661456'),
(41915, 'Fors', 4795, 'NAQ', 75, 'FR', 46.23570000, -0.40904000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q661456'),
(41916, 'Fort-Mahon-Plage', 4828, 'HDF', 75, 'FR', 50.33955000, 1.55984000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q147606'),
(41917, 'Fort-Mardyck', 4828, 'HDF', 75, 'FR', 51.02899000, 2.30724000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q601863'),
(41918, 'Fortschwihr', 4820, 'GES', 75, 'FR', 48.08825000, 7.45050000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q325073'),
(41919, 'Fos-sur-Mer', 4812, 'PAC', 75, 'FR', 43.43774000, 4.94457000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q455657'),
(41920, 'Fosses', 4796, 'IDF', 75, 'FR', 49.09808000, 2.50957000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q243260'),
(41921, 'Foucarmont', 4804, 'NOR', 75, 'FR', 49.84682000, 1.56899000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q243260'),
(41922, 'Foucherans', 4825, 'BFC', 75, 'FR', 47.08094000, 5.45503000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q243260'),
(41923, 'Fouesnant', 4807, 'BRE', 75, 'FR', 47.89187000, -4.01484000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q245149'),
(41924, 'Foug', 4820, 'GES', 75, 'FR', 48.68385000, 5.78735000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q245149'),
(41925, 'Fougerolles', 4825, 'BFC', 75, 'FR', 47.88542000, 6.40454000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q245149'),
(41926, 'Fougerolles-du-Plessis', 4802, 'PDL', 75, 'FR', 48.47424000, -0.97255000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q245149'),
(41927, 'Fougères', 4807, 'BRE', 75, 'FR', 48.35185000, -1.19989000, '2019-10-05 22:48:02', '2020-05-01 17:22:44', 1, 'Q245149'),
(41928, 'Fouillard', 4807, 'BRE', 75, 'FR', 48.15820000, -1.57915000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q245149'),
(41929, 'Foulayronnes', 4795, 'NAQ', 75, 'FR', 44.24029000, 0.64516000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, '********'),
(41930, 'Fouquereuil', 4828, 'HDF', 75, 'FR', 50.51885000, 2.60024000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, '********'),
(41931, 'Fouquières-lès-Béthune', 4828, 'HDF', 75, 'FR', 50.51534000, 2.60999000, '2019-10-05 22:48:02', '2020-05-01 17:22:45', 1, '********'),
(41932, 'Fouquières-lès-Lens', 4828, 'HDF', 75, 'FR', 50.42842000, 2.91321000, '2019-10-05 22:48:02', '2020-05-01 17:22:45', 1, '********'),
(41933, 'Fouras', 4795, 'NAQ', 75, 'FR', 45.98736000, -1.09275000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q745213'),
(41934, 'Fourchambault', 4825, 'BFC', 75, 'FR', 47.01667000, 3.08333000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q694905'),
(41935, 'Fourmies', 4828, 'HDF', 75, 'FR', 50.01532000, 4.04784000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q694905'),
(41936, 'Fournes-en-Weppes', 4828, 'HDF', 75, 'FR', 50.58497000, 2.88793000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q694905'),
(41937, 'Fourques', 4799, 'OCC', 75, 'FR', 43.69450000, 4.60932000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q694905'),
(41938, 'Fourques-sur-Garonne', 4795, 'NAQ', 75, 'FR', 44.44798000, 0.15703000, '2019-10-05 22:48:02', '2019-10-05 22:48:02', 1, 'Q192740'),
(41939, 'Fourqueux', 4796, 'IDF', 75, 'FR', 48.88693000, 2.06367000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q637717'),
(41940, 'Foussais-Payré', 4802, 'PDL', 75, 'FR', 46.53333000, -0.68333000, '2019-10-05 22:48:03', '2020-05-01 17:22:46', 1, 'Q679386'),
(41941, 'Foëcy', 4818, 'CVL', 75, 'FR', 47.17618000, 2.16257000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q679386'),
(41942, 'Frahier-et-Chatebier', 4825, 'BFC', 75, 'FR', 47.66667000, 6.75000000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q679386'),
(41943, 'Fraisans', 4825, 'BFC', 75, 'FR', 47.14857000, 5.76051000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q679386'),
(41944, 'Fraisses', 4798, 'ARA', 75, 'FR', 45.38838000, 4.26373000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, '********'),
(41945, 'Fraize', 4820, 'GES', 75, 'FR', 48.18660000, 6.99787000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q957731'),
(41946, 'Francheleins', 4798, 'ARA', 75, 'FR', 46.07440000, 4.80920000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q957731'),
(41947, 'Francheville', 4798, 'ARA', 75, 'FR', 45.73637000, 4.76358000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q957731'),
(41948, 'Francheville', 4804, 'NOR', 75, 'FR', 48.78647000, 0.84962000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q957731'),
(41949, 'Franconville', 4796, 'IDF', 75, 'FR', 48.98333000, 2.23333000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q957731'),
(41950, 'Frangy', 4798, 'ARA', 75, 'FR', 46.02000000, 5.93220000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q762897'),
(41951, 'Franois', 4825, 'BFC', 75, 'FR', 47.23055000, 5.92869000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q762897'),
(41952, 'Frans', 4798, 'ARA', 75, 'FR', 45.99291000, 4.77820000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q841585'),
(41953, 'Frasne', 4825, 'BFC', 75, 'FR', 46.85641000, 6.15940000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q324488'),
(41954, 'Freigné', 4802, 'PDL', 75, 'FR', 47.54858000, -1.12274000, '2019-10-05 22:48:03', '2020-05-01 17:22:46', 1, '********'),
(41955, 'Frelinghien', 4828, 'HDF', 75, 'FR', 50.71667000, 2.93333000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, '********'),
(41956, 'Freneuse', 4796, 'IDF', 75, 'FR', 49.04832000, 1.60168000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, '********'),
(41957, 'Fresnay-sur-Sarthe', 4802, 'PDL', 75, 'FR', 48.28199000, 0.02288000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q909600'),
(41958, 'Fresnes', 4796, 'IDF', 75, 'FR', 48.75568000, 2.32241000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q909600'),
(41959, 'Fresnes-sur-Escaut', 4828, 'HDF', 75, 'FR', 50.43382000, 3.57752000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q909600'),
(41960, 'Fresnoy-le-Grand', 4828, 'HDF', 75, 'FR', 49.94757000, 3.41841000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q909600'),
(41961, 'Fresse-sur-Moselle', 4820, 'GES', 75, 'FR', 47.87589000, 6.78589000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41962, 'Fressenneville', 4828, 'HDF', 75, 'FR', 50.06838000, 1.57816000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41963, 'Fretin', 4828, 'HDF', 75, 'FR', 50.55745000, 3.13668000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41964, 'Freyming-Merlebach', 4820, 'GES', 75, 'FR', 49.15000000, 6.78333000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41965, 'Frignicourt', 4820, 'GES', 75, 'FR', 48.69966000, 4.59153000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41966, 'Friville-Escarbotin', 4828, 'HDF', 75, 'FR', 50.08602000, 1.54560000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41967, 'Froges', 4798, 'ARA', 75, 'FR', 45.27377000, 5.92098000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41968, 'Froideconche', 4825, 'BFC', 75, 'FR', 47.82121000, 6.41523000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q920853'),
(41969, 'Froidfond', 4802, 'PDL', 75, 'FR', 46.86898000, -1.75740000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q965578'),
(41970, 'Fromelennes', 4820, 'GES', 75, 'FR', 50.12333000, 4.85952000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q965578'),
(41971, 'Froncles', 4820, 'GES', 75, 'FR', 48.29756000, 5.14586000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q965578'),
(41972, 'Fronsac', 4795, 'NAQ', 75, 'FR', 44.91667000, -0.26667000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q965578'),
(41973, 'Frontenay-Rohan-Rohan', 4795, 'NAQ', 75, 'FR', 46.25276000, -0.53833000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, '********'),
(41974, 'Frontenex', 4798, 'ARA', 75, 'FR', 45.63335000, 6.31168000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, '********'),
(41975, 'Frontignan', 4799, 'OCC', 75, 'FR', 43.44848000, 3.75400000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q216280'),
(41976, 'Fronton', 4799, 'OCC', 75, 'FR', 43.83931000, 1.38931000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q216280'),
(41977, 'Frontonas', 4798, 'ARA', 75, 'FR', 45.64487000, 5.19701000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q216280'),
(41978, 'Frossay', 4802, 'PDL', 75, 'FR', 47.24451000, -1.93557000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q730397'),
(41979, 'Frotey-lès-Vesoul', 4825, 'BFC', 75, 'FR', 47.62078000, 6.18831000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q730397'),
(41980, 'Frouard', 4820, 'GES', 75, 'FR', 48.75994000, 6.13036000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q195098'),
(41981, 'Frouzins', 4799, 'OCC', 75, 'FR', 43.51482000, 1.32523000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q831032'),
(41982, 'Fruges', 4828, 'HDF', 75, 'FR', 50.51501000, 2.13292000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q831032'),
(41983, 'Fréhel', 4807, 'BRE', 75, 'FR', 48.63333000, -2.36667000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q831032'),
(41984, 'Fréjairolles', 4799, 'OCC', 75, 'FR', 43.88094000, 2.23151000, '2019-10-05 22:48:03', '2020-05-01 17:22:46', 1, 'Q831032'),
(41985, 'Fréjus', 4812, 'PAC', 75, 'FR', 43.43325000, 6.73555000, '2019-10-05 22:48:03', '2020-05-01 17:22:46', 1, 'Q215155'),
(41986, 'Fréland', 4820, 'GES', 75, 'FR', 48.17215000, 7.19167000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q381604'),
(41987, 'Frépillon', 4796, 'IDF', 75, 'FR', 49.05216000, 2.20528000, '2019-10-05 22:48:03', '2020-05-01 17:22:43', 1, 'Q381604'),
(41988, 'Fréthun', 4828, 'HDF', 75, 'FR', 50.91729000, 1.82505000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, 'Q381604'),
(41989, 'Frévent', 4828, 'HDF', 75, 'FR', 50.27608000, 2.28725000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, 'Q331982'),
(41990, 'Fublaines', 4796, 'IDF', 75, 'FR', 48.93816000, 2.93655000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q331982'),
(41991, 'Fumay', 4820, 'GES', 75, 'FR', 49.99132000, 4.70771000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q320629'),
(41992, 'Fumel', 4795, 'NAQ', 75, 'FR', 44.49862000, 0.96506000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q659721'),
(41993, 'Furdenheim', 4820, 'GES', 75, 'FR', 48.61129000, 7.56100000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q659721'),
(41994, 'Furiani', 4806, '20R', 75, 'FR', 42.65847000, 9.41446000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q637279'),
(41995, 'Fussy', 4818, 'CVL', 75, 'FR', 47.14372000, 2.42953000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q637279'),
(41996, 'Fuveau', 4812, 'PAC', 75, 'FR', 43.45578000, 5.56149000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q637279'),
(41997, 'Fère-Champenoise', 4820, 'GES', 75, 'FR', 48.75431000, 3.99069000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q834061'),
(41998, 'Fère-en-Tardenois', 4828, 'HDF', 75, 'FR', 49.20000000, 3.51667000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, 'Q332026'),
(41999, 'Fécamp', 4804, 'NOR', 75, 'FR', 49.75787000, 0.37457000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, 'Q269732'),
(42000, 'Féchain', 4828, 'HDF', 75, 'FR', 50.26638000, 3.21024000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, '********'),
(42001, 'Fégréac', 4802, 'PDL', 75, 'FR', 47.58476000, -2.04410000, '2019-10-05 22:48:03', '2020-05-01 17:22:46', 1, '********'),
(42002, 'Félines', 4798, 'ARA', 75, 'FR', 45.31668000, 4.72836000, '2019-10-05 22:48:03', '2020-05-01 17:22:43', 1, '********'),
(42003, 'Fénay', 4825, 'BFC', 75, 'FR', 47.23185000, 5.06211000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q52869'),
(42004, 'Férel', 4807, 'BRE', 75, 'FR', 47.48203000, -2.34227000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q71101'),
(42005, 'Férin', 4828, 'HDF', 75, 'FR', 50.32732000, 3.07415000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, 'Q71101'),
(42006, 'Férolles', 4818, 'CVL', 75, 'FR', 47.83475000, 2.11113000, '2019-10-05 22:48:03', '2020-05-01 17:22:44', 1, 'Q71101'),
(42007, 'Férolles-Attilly', 4796, 'IDF', 75, 'FR', 48.73184000, 2.63088000, '2019-10-05 22:48:03', '2020-05-01 17:22:43', 1, 'Q71101'),
(42008, 'Féternes', 4798, 'ARA', 75, 'FR', 46.35582000, 6.54829000, '2019-10-05 22:48:03', '2020-05-01 17:22:43', 1, 'Q71101'),
(42009, 'Gabarret', 4795, 'NAQ', 75, 'FR', 43.98779000, 0.00978000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q71101'),
(42010, 'Gacé', 4804, 'NOR', 75, 'FR', 48.79344000, 0.29624000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, 'Q71101'),
(42011, 'Gagnac-sur-Garonne', 4799, 'OCC', 75, 'FR', 43.69990000, 1.37535000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q71101'),
(42012, 'Gagny', 4796, 'IDF', 75, 'FR', 48.88333000, 2.53333000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q179821'),
(42013, 'Gaillac', 4799, 'OCC', 75, 'FR', 43.90160000, 1.89686000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q343613'),
(42014, 'Gaillac-Toulza', 4799, 'OCC', 75, 'FR', 43.25536000, 1.47141000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q343613'),
(42015, 'Gaillan-en-Médoc', 4795, 'NAQ', 75, 'FR', 45.32133000, -0.95794000, '2019-10-05 22:48:03', '2020-05-01 17:22:45', 1, 'Q343613'),
(42016, 'Gaillard', 4798, 'ARA', 75, 'FR', 46.18530000, 6.20693000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q343613'),
(42017, 'Gaillefontaine', 4804, 'NOR', 75, 'FR', 49.65371000, 1.61547000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q343613'),
(42018, 'Gaillon', 4804, 'NOR', 75, 'FR', 49.16104000, 1.34016000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q627220'),
(42019, 'Gainneville', 4804, 'NOR', 75, 'FR', 49.51845000, 0.26164000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q627220'),
(42020, 'Galgon', 4795, 'NAQ', 75, 'FR', 44.98333000, -0.26667000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q627220'),
(42021, 'Gallardon', 4818, 'CVL', 75, 'FR', 48.52622000, 1.69307000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q627220'),
(42022, 'Gallargues-le-Montueux', 4799, 'OCC', 75, 'FR', 43.71667000, 4.16667000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q75014'),
(42023, 'Galluis', 4796, 'IDF', 75, 'FR', 48.79657000, 1.79414000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q640180'),
(42024, 'Gamaches', 4828, 'HDF', 75, 'FR', 49.98615000, 1.55624000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q640180'),
(42025, 'Gambais', 4796, 'IDF', 75, 'FR', 48.77351000, 1.67196000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q641127'),
(42026, 'Gambsheim', 4820, 'GES', 75, 'FR', 48.69209000, 7.88286000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q21428'),
(42027, 'Gan', 4795, 'NAQ', 75, 'FR', 43.23333000, -0.38333000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q21428'),
(42028, 'Gandrange', 4820, 'GES', 75, 'FR', 49.27133000, 6.12536000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q21428'),
(42029, 'Ganges', 4799, 'OCC', 75, 'FR', 43.93380000, 3.70784000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q734074'),
(42030, 'Gannat', 4798, 'ARA', 75, 'FR', 46.09987000, 3.19842000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q272347'),
(42031, 'Gap', 4812, 'PAC', 75, 'FR', 44.55858000, 6.07868000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q175081'),
(42032, 'Garancières', 4796, 'IDF', 75, 'FR', 48.82271000, 1.75512000, '2019-10-05 22:48:03', '2020-05-01 17:22:43', 1, 'Q175081'),
(42033, 'Garat', 4795, 'NAQ', 75, 'FR', 45.63333000, 0.26667000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q175081'),
(42034, 'Garches', 4796, 'IDF', 75, 'FR', 48.84226000, 2.18232000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q246199'),
(42035, 'Garchizy', 4825, 'BFC', 75, 'FR', 47.04786000, 3.09625000, '2019-10-05 22:48:03', '2019-10-05 22:48:03', 1, 'Q246199'),
(42036, 'Gard', 4799, 'OCC', 75, 'FR', 44.01790000, 4.28751000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q12515'),
(42037, 'Gardanne', 4812, 'PAC', 75, 'FR', 43.45491000, 5.46913000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q318085'),
(42038, 'Gardonne', 4795, 'NAQ', 75, 'FR', 44.83333000, 0.35000000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1078919'),
(42039, 'Gardouch', 4799, 'OCC', 75, 'FR', 43.39096000, 1.68313000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1078919'),
(42040, 'Garennes-sur-Eure', 4804, 'NOR', 75, 'FR', 48.91116000, 1.43836000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1078919'),
(42041, 'Gargas', 4812, 'PAC', 75, 'FR', 43.90196000, 5.35814000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1078919'),
(42042, 'Gargenville', 4796, 'IDF', 75, 'FR', 48.98802000, 1.81176000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q638923'),
(42043, 'Garges-lès-Gonesse', 4796, 'IDF', 75, 'FR', 48.96791000, 2.39781000, '2019-10-05 22:48:04', '2020-05-01 17:22:43', 1, 'Q244271'),
(42044, 'Garidech', 4799, 'OCC', 75, 'FR', 43.71076000, 1.56036000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q244271'),
(42045, 'Garlin', 4795, 'NAQ', 75, 'FR', 43.55927000, -0.27321000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q858165'),
(42046, 'Garnay', 4818, 'CVL', 75, 'FR', 48.70489000, 1.33706000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1343957'),
(42047, 'Garons', 4799, 'OCC', 75, 'FR', 43.76861000, 4.42753000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1343957'),
(42048, 'Garéoult', 4812, 'PAC', 75, 'FR', 43.32833000, 6.04616000, '2019-10-05 22:48:04', '2020-05-01 17:22:46', 1, 'Q1343957'),
(42049, 'Gasny', 4804, 'NOR', 75, 'FR', 49.09184000, 1.60336000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1073294'),
(42050, 'Gassin', 4812, 'PAC', 75, 'FR', 43.22882000, 6.58549000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1073294'),
(42051, 'Gasville-Oisème', 4818, 'CVL', 75, 'FR', 48.46973000, 1.53843000, '2019-10-05 22:48:04', '2020-05-01 17:22:44', 1, 'Q1073294'),
(42052, 'Gattières', 4812, 'PAC', 75, 'FR', 43.75951000, 7.17574000, '2019-10-05 22:48:04', '2020-05-01 17:22:46', 1, 'Q1073294'),
(42053, 'Gauchy', 4828, 'HDF', 75, 'FR', 49.82765000, 3.27371000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1073294'),
(42054, 'Gauriaguet', 4795, 'NAQ', 75, 'FR', 45.03904000, -0.39191000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1073294'),
(42055, 'Gavray', 4804, 'NOR', 75, 'FR', 48.91108000, -1.35059000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1071338'),
(42056, 'Gazeran', 4796, 'IDF', 75, 'FR', 48.63264000, 1.77149000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q246326'),
(42057, 'Gaël', 4807, 'BRE', 75, 'FR', 48.13209000, -2.22267000, '2019-10-05 22:48:04', '2020-05-01 17:22:44', 1, 'Q246326'),
(42058, 'Geispolsheim', 4820, 'GES', 75, 'FR', 48.51603000, 7.64825000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q22643'),
(42059, 'Gelles', 4798, 'ARA', 75, 'FR', 45.76947000, 2.76309000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q22643'),
(42060, 'Gelos', 4795, 'NAQ', 75, 'FR', 43.28333000, -0.36667000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q742879'),
(42061, 'Genas', 4798, 'ARA', 75, 'FR', 45.73131000, 5.00211000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q767263'),
(42062, 'Genay', 4798, 'ARA', 75, 'FR', 45.89681000, 4.84091000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q767263'),
(42063, 'Genech', 4828, 'HDF', 75, 'FR', 50.53122000, 3.21651000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q536740'),
(42064, 'Geneston', 4802, 'PDL', 75, 'FR', 47.05639000, -1.51139000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q833422'),
(42065, 'Genillé', 4818, 'CVL', 75, 'FR', 47.18333000, 1.10000000, '2019-10-05 22:48:04', '2020-05-01 17:22:44', 1, 'Q833422'),
(42066, 'Genlis', 4825, 'BFC', 75, 'FR', 47.24203000, 5.22415000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q47711'),
(42067, 'Gennes', 4802, 'PDL', 75, 'FR', 47.34010000, -0.23149000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q47711'),
(42068, 'Gennevilliers', 4796, 'IDF', 75, 'FR', 48.93333000, 2.30000000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q221968'),
(42069, 'Gensac-la-Pallue', 4795, 'NAQ', 75, 'FR', 45.65000000, -0.25000000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q221968'),
(42070, 'Gentilly', 4796, 'IDF', 75, 'FR', 48.81294000, 2.34170000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q640102'),
(42071, 'Ger', 4795, 'NAQ', 75, 'FR', 43.25000000, -0.05000000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q640102'),
(42072, 'Gerbéviller', 4820, 'GES', 75, 'FR', 48.49618000, 6.51075000, '2019-10-05 22:48:04', '2020-05-01 17:22:44', 1, 'Q224346'),
(42073, 'Gerde', 4799, 'OCC', 75, 'FR', 43.05567000, 0.16688000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q224346'),
(42074, 'Gergy', 4825, 'BFC', 75, 'FR', 46.87557000, 4.94527000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1601927'),
(42075, 'Gers', 4799, 'OCC', 75, 'FR', 43.71731000, 0.45422000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q12517'),
(42076, 'Gerstheim', 4820, 'GES', 75, 'FR', 48.38215000, 7.70395000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q12517'),
(42077, 'Gerzat', 4798, 'ARA', 75, 'FR', 45.82581000, 3.14473000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, '********'),
(42078, 'Gespunsart', 4820, 'GES', 75, 'FR', 49.82143000, 4.82926000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, '********'),
(42079, 'Gestel', 4807, 'BRE', 75, 'FR', 47.80361000, -3.44245000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, '********'),
(42080, 'Gesté', 4802, 'PDL', 75, 'FR', 47.18179000, -1.10917000, '2019-10-05 22:48:04', '2020-05-01 17:22:46', 1, '********'),
(42081, 'Geudertheim', 4820, 'GES', 75, 'FR', 48.72415000, 7.75188000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, '********'),
(42082, 'Gevrey-Chambertin', 4825, 'BFC', 75, 'FR', 47.22614000, 4.96806000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q52871'),
(42083, 'Gex', 4798, 'ARA', 75, 'FR', 46.33323000, 6.05766000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q52871'),
(42084, 'Ghisonaccia', 4806, '20R', 75, 'FR', 42.01582000, 9.40507000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q637313'),
(42085, 'Ghyvelde', 4828, 'HDF', 75, 'FR', 51.05275000, 2.52642000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q637313'),
(42086, 'Giberville', 4804, 'NOR', 75, 'FR', 49.18158000, -0.28386000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q637313'),
(42087, 'Gidy', 4818, 'CVL', 75, 'FR', 47.98539000, 1.83816000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q637313'),
(42088, 'Gien', 4818, 'CVL', 75, 'FR', 47.69332000, 2.63094000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q475599'),
(42089, 'Gif-sur-Yvette', 4796, 'IDF', 75, 'FR', 48.68333000, 2.13333000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q208851'),
(42090, 'Gigean', 4799, 'OCC', 75, 'FR', 43.50028000, 3.71167000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q208851'),
(42091, 'Gignac', 4799, 'OCC', 75, 'FR', 43.65200000, 3.55090000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q208851'),
(42092, 'Gignac-la-Nerthe', 4812, 'PAC', 75, 'FR', 43.39287000, 5.23586000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q208851'),
(42093, 'Gilette', 4812, 'PAC', 75, 'FR', 43.84976000, 7.16346000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q208851'),
(42094, 'Gilley', 4825, 'BFC', 75, 'FR', 47.04740000, 6.48257000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q208851'),
(42095, 'Gillonnay', 4798, 'ARA', 75, 'FR', 45.39251000, 5.29413000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q208851'),
(42096, 'Gilly-sur-Isère', 4798, 'ARA', 75, 'FR', 45.65917000, 6.35096000, '2019-10-05 22:48:04', '2020-05-01 17:22:43', 1, 'Q208851'),
(42097, 'Gimont', 4799, 'OCC', 75, 'FR', 43.62627000, 0.87655000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q322406'),
(42098, 'Ginasservis', 4812, 'PAC', 75, 'FR', 43.67088000, 5.84911000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1151797'),
(42099, 'Ginestas', 4799, 'OCC', 75, 'FR', 43.26645000, 2.87038000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1151797'),
(42100, 'Giraumont', 4820, 'GES', 75, 'FR', 49.17076000, 5.91448000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1151797'),
(42101, 'Giromagny', 4825, 'BFC', 75, 'FR', 47.74272000, 6.82733000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q753708'),
(42102, 'Gironde', 4795, 'NAQ', 75, 'FR', 44.69306000, -0.41400000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q12526'),
(42103, 'Gironde-sur-Dropt', 4795, 'NAQ', 75, 'FR', 44.58333000, -0.08333000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q12526'),
(42104, 'Giroussens', 4799, 'OCC', 75, 'FR', 43.76199000, 1.77608000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q12526'),
(42105, 'Gisors', 4804, 'NOR', 75, 'FR', 49.28178000, 1.78010000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q271072'),
(42106, 'Givenchy-en-Gohelle', 4828, 'HDF', 75, 'FR', 50.39080000, 2.77320000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q366425'),
(42107, 'Givet', 4820, 'GES', 75, 'FR', 50.13796000, 4.82545000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q241940'),
(42108, 'Givors', 4798, 'ARA', 75, 'FR', 45.59063000, 4.76878000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q1976'),
(42109, 'Givrand', 4802, 'PDL', 75, 'FR', 46.67083000, -1.88612000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, '********'),
(42110, 'Givry', 4825, 'BFC', 75, 'FR', 46.78202000, 4.74262000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, '********'),
(42111, 'Gières', 4798, 'ARA', 75, 'FR', 45.17997000, 5.78935000, '2019-10-05 22:48:04', '2020-05-01 17:22:43', 1, '********'),
(42112, 'Gièvres', 4818, 'CVL', 75, 'FR', 47.27847000, 1.66943000, '2019-10-05 22:48:04', '2020-05-01 17:22:44', 1, '********'),
(42113, 'Gleizé', 4798, 'ARA', 75, 'FR', 45.98916000, 4.69708000, '2019-10-05 22:48:04', '2020-05-01 17:22:43', 1, '********'),
(42114, 'Glomel', 4807, 'BRE', 75, 'FR', 48.22300000, -3.39696000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q862045'),
(42115, 'Goderville', 4804, 'NOR', 75, 'FR', 49.64566000, 0.36593000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q862045'),
(42116, 'Godewaersvelde', 4828, 'HDF', 75, 'FR', 50.79399000, 2.64456000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q14007'),
(42117, 'Goetzenbruck', 4820, 'GES', 75, 'FR', 48.97778000, 7.37960000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q22441'),
(42118, 'Goincourt', 4828, 'HDF', 75, 'FR', 49.42614000, 2.03621000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q22441'),
(42119, 'Golbey', 4820, 'GES', 75, 'FR', 48.19764000, 6.43966000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q22441'),
(42120, 'Gometz-la-Ville', 4796, 'IDF', 75, 'FR', 48.67219000, 2.12868000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q276350'),
(42121, 'Gometz-le-Châtel', 4796, 'IDF', 75, 'FR', 48.67837000, 2.13792000, '2019-10-05 22:48:04', '2020-05-01 17:22:43', 1, 'Q671869'),
(42122, 'Gommegnies', 4828, 'HDF', 75, 'FR', 50.27155000, 3.70625000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q671869'),
(42123, 'Goncelin', 4798, 'ARA', 75, 'FR', 45.34428000, 5.97896000, '2019-10-05 22:48:04', '2019-10-05 22:48:04', 1, 'Q671869'),
(42124, 'Gond-Pontouvre', 4795, 'NAQ', 75, 'FR', 45.68333000, 0.16667000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1450508'),
(42125, 'Gondecourt', 4828, 'HDF', 75, 'FR', 50.54469000, 2.98378000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q665439'),
(42126, 'Gondrecourt-le-Château', 4820, 'GES', 75, 'FR', 48.51366000, 5.51058000, '2019-10-05 22:48:05', '2020-05-01 17:22:44', 1, 'Q665439'),
(42127, 'Gondreville', 4820, 'GES', 75, 'FR', 48.69373000, 5.96467000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q665439'),
(42128, 'Gondrin', 4799, 'OCC', 75, 'FR', 43.88466000, 0.23737000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q665439'),
(42129, 'Gonesse', 4796, 'IDF', 75, 'FR', 48.98693000, 2.44892000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q665439'),
(42130, 'Gonfaron', 4812, 'PAC', 75, 'FR', 43.32036000, 6.28929000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q612401'),
(42131, 'Gonnehem', 4828, 'HDF', 75, 'FR', 50.56061000, 2.57277000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q612401'),
(42132, 'Gonneville-la-Mallet', 4804, 'NOR', 75, 'FR', 49.63950000, 0.22245000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q612401'),
(42133, 'Gontaud-de-Nogaret', 4795, 'NAQ', 75, 'FR', 44.45000000, 0.30000000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q612401'),
(42134, 'Gorbio', 4812, 'PAC', 75, 'FR', 43.78678000, 7.44375000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q612401'),
(42135, 'Gorcy', 4820, 'GES', 75, 'FR', 49.53487000, 5.68487000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, '********'),
(42136, 'Gordes', 4812, 'PAC', 75, 'FR', 43.91290000, 5.19892000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q272308'),
(42137, 'Gorges', 4802, 'PDL', 75, 'FR', 47.09917000, -1.30024000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q272308'),
(42138, 'Gorron', 4802, 'PDL', 75, 'FR', 48.41095000, -0.81267000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q744662'),
(42139, 'Gorze', 4820, 'GES', 75, 'FR', 49.05236000, 6.00072000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q21768'),
(42140, 'Gosnay', 4828, 'HDF', 75, 'FR', 50.50622000, 2.58904000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q741641'),
(42141, 'Gosné', 4807, 'BRE', 75, 'FR', 48.24674000, -1.46558000, '2019-10-05 22:48:05', '2020-05-01 17:22:44', 1, 'Q741641'),
(42142, 'Gouaix', 4796, 'IDF', 75, 'FR', 48.48539000, 3.29336000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q741641'),
(42143, 'Gouarec', 4807, 'BRE', 75, 'FR', 48.22725000, -3.17994000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q844458'),
(42144, 'Goudargues', 4799, 'OCC', 75, 'FR', 44.21376000, 4.46652000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q844458'),
(42145, 'Goudelin', 4807, 'BRE', 75, 'FR', 48.60375000, -3.01842000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q844458'),
(42146, 'Gouesnach', 4807, 'BRE', 75, 'FR', 47.91040000, -4.11450000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q844458'),
(42147, 'Gouesnou', 4807, 'BRE', 75, 'FR', 48.45267000, -4.46456000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q328777'),
(42148, 'Goult', 4812, 'PAC', 75, 'FR', 43.86297000, 5.24390000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1019030'),
(42149, 'Gourdan-Polignan', 4799, 'OCC', 75, 'FR', 43.07092000, 0.57482000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1019030'),
(42150, 'Gourdon', 4799, 'OCC', 75, 'FR', 44.73742000, 1.38297000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1019030'),
(42151, 'Gourin', 4807, 'BRE', 75, 'FR', 48.13866000, -3.60755000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q71216'),
(42152, 'Gournay-en-Bray', 4804, 'NOR', 75, 'FR', 49.48285000, 1.72471000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q578194'),
(42153, 'Gournay-sur-Marne', 4796, 'IDF', 75, 'FR', 48.86223000, 2.58452000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q245479'),
(42154, 'Goussainville', 4796, 'IDF', 75, 'FR', 49.01367000, 2.46595000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q245479'),
(42155, 'Gouvernes', 4796, 'IDF', 75, 'FR', 48.86007000, 2.69074000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q271389'),
(42156, 'Gouvieux', 4828, 'HDF', 75, 'FR', 49.18705000, 2.41439000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q752088'),
(42157, 'Gouville-sur-Mer', 4804, 'NOR', 75, 'FR', 49.09611000, -1.57908000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1003071'),
(42158, 'Gouy-sous-Bellonne', 4828, 'HDF', 75, 'FR', 50.31139000, 3.05657000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1003071'),
(42159, 'Gouzeaucourt', 4828, 'HDF', 75, 'FR', 50.05606000, 3.12351000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1003071'),
(42160, 'Gouzon', 4795, 'NAQ', 75, 'FR', 46.19286000, 2.23876000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1003071'),
(42161, 'Gouézec', 4807, 'BRE', 75, 'FR', 48.16911000, -3.97277000, '2019-10-05 22:48:05', '2020-05-01 17:22:44', 1, 'Q474667'),
(42162, 'Goven', 4807, 'BRE', 75, 'FR', 48.00638000, -1.84629000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1074743'),
(42163, 'Grabels', 4799, 'OCC', 75, 'FR', 43.64797000, 3.79865000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1074743'),
(42164, 'Gradignan', 4795, 'NAQ', 75, 'FR', 44.77262000, -0.61393000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q234769'),
(42165, 'Gragnague', 4799, 'OCC', 75, 'FR', 43.68091000, 1.58461000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q234769'),
(42166, 'Grainville-la-Teinturière', 4804, 'NOR', 75, 'FR', 49.74773000, 0.64048000, '2019-10-05 22:48:05', '2020-05-01 17:22:45', 1, 'Q234769'),
(42167, 'Grainville-sur-Odon', 4804, 'NOR', 75, 'FR', 49.13936000, -0.53046000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q234769'),
(42168, 'Gramat', 4799, 'OCC', 75, 'FR', 44.78075000, 1.71957000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q430834'),
(42169, 'Grambois', 4812, 'PAC', 75, 'FR', 43.76233000, 5.58860000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q623883'),
(42170, 'Grand-Champ', 4807, 'BRE', 75, 'FR', 47.75804000, -2.84635000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q70896'),
(42171, 'Grand-Charmont', 4825, 'BFC', 75, 'FR', 47.52674000, 6.82604000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q70896'),
(42172, 'Grand-Couronne', 4804, 'NOR', 75, 'FR', 49.35563000, 1.00647000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q70896'),
(42173, 'Grand-Fort-Philippe', 4828, 'HDF', 75, 'FR', 50.99961000, 2.10784000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1066836'),
(42174, 'Grand-Fougeray', 4807, 'BRE', 75, 'FR', 47.72390000, -1.73210000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q381743'),
(42175, 'Grandcamp-Maisy', 4804, 'NOR', 75, 'FR', 49.38333000, -1.03333000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q381174'),
(42176, 'Grande-Synthe', 4828, 'HDF', 75, 'FR', 51.01540000, 2.29975000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42177, 'Grandfontaine', 4825, 'BFC', 75, 'FR', 47.19763000, 5.90079000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42178, 'Grandfresnoy', 4828, 'HDF', 75, 'FR', 49.37218000, 2.65216000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42179, 'Grandris', 4798, 'ARA', 75, 'FR', 46.03780000, 4.47526000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42180, 'Grandvillars', 4825, 'BFC', 75, 'FR', 47.53929000, 6.97100000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42181, 'Grandvilliers', 4828, 'HDF', 75, 'FR', 49.66547000, 1.94088000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42182, 'Grane', 4798, 'ARA', 75, 'FR', 44.73255000, 4.92203000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42183, 'Granges-sur-Vologne', 4820, 'GES', 75, 'FR', 48.14517000, 6.79095000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q769160'),
(42184, 'Grans', 4812, 'PAC', 75, 'FR', 43.60862000, 5.06290000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q675950'),
(42185, 'Granville', 4804, 'NOR', 75, 'FR', 48.83792000, -1.59714000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q675950'),
(42186, 'Grasse', 4812, 'PAC', 75, 'FR', 43.65783000, 6.92537000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q173705'),
(42187, 'Gratentour', 4799, 'OCC', 75, 'FR', 43.72360000, 1.43234000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1432870'),
(42188, 'Graulhet', 4799, 'OCC', 75, 'FR', 43.76688000, 1.98938000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1620475'),
(42189, 'Gravelines', 4828, 'HDF', 75, 'FR', 50.98651000, 2.12807000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q323399'),
(42190, 'Graveson', 4812, 'PAC', 75, 'FR', 43.85051000, 4.77361000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q269854'),
(42191, 'Gravigny', 4804, 'NOR', 75, 'FR', 49.05310000, 1.16962000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q269854'),
(42192, 'Gray', 4825, 'BFC', 75, 'FR', 47.44575000, 5.59215000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q265332'),
(42193, 'Gray-la-Ville', 4825, 'BFC', 75, 'FR', 47.43932000, 5.57252000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q265332'),
(42194, 'Graçay', 4818, 'CVL', 75, 'FR', 47.14371000, 1.84733000, '2019-10-05 22:48:05', '2020-05-01 17:22:44', 1, 'Q265332'),
(42195, 'Grenade', 4799, 'OCC', 75, 'FR', 43.76667000, 1.28333000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q671069'),
(42196, 'Grenade-sur-l’Adour', 4795, 'NAQ', 75, 'FR', 43.77753000, -0.42961000, '2019-10-05 22:48:05', '2020-05-01 17:22:45', 1, 'Q671069'),
(42197, 'Grenay', 4798, 'ARA', 75, 'FR', 45.66347000, 5.08031000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q671069'),
(42198, 'Grenay', 4828, 'HDF', 75, 'FR', 50.44962000, 2.75168000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q671069'),
(42199, 'Grendelbruch', 4820, 'GES', 75, 'FR', 48.49272000, 7.32239000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q21487'),
(42200, 'Grenoble', 4798, 'ARA', 75, 'FR', 45.17869000, 5.71479000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1289'),
(42201, 'Gresswiller', 4820, 'GES', 75, 'FR', 48.53542000, 7.43251000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1289'),
(42202, 'Gretz-Armainvilliers', 4796, 'IDF', 75, 'FR', 48.74120000, 2.73105000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1289'),
(42203, 'Grez-Neuville', 4802, 'PDL', 75, 'FR', 47.60288000, -0.68193000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q1289'),
(42204, 'Grez-sur-Loing', 4796, 'IDF', 75, 'FR', 48.31754000, 2.68848000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q251748'),
(42205, 'Gries', 4820, 'GES', 75, 'FR', 48.75370000, 7.81403000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q251748'),
(42206, 'Griesheim-près-Molsheim', 4820, 'GES', 75, 'FR', 48.50296000, 7.53027000, '2019-10-05 22:48:05', '2020-05-01 17:22:44', 1, 'Q251748'),
(42207, 'Griesheim-sur-Souffel', 4820, 'GES', 75, 'FR', 48.63591000, 7.66923000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q251748'),
(42208, 'Grignan', 4798, 'ARA', 75, 'FR', 44.41967000, 4.90785000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q384722'),
(42209, 'Grignols', 4795, 'NAQ', 75, 'FR', 44.38842000, -0.04287000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q384722'),
(42210, 'Grignon', 4798, 'ARA', 75, 'FR', 45.65122000, 6.37795000, '2019-10-05 22:48:05', '2019-10-05 22:48:05', 1, 'Q384722'),
(42211, 'Grigny', 4798, 'ARA', 75, 'FR', 45.60843000, 4.78976000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42212, 'Grigny', 4796, 'IDF', 75, 'FR', 48.65412000, 2.39343000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42213, 'Grillon', 4812, 'PAC', 75, 'FR', 44.39508000, 4.92954000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42214, 'Grimaud', 4812, 'PAC', 75, 'FR', 43.27329000, 6.52171000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42215, 'Grisolles', 4799, 'OCC', 75, 'FR', 43.82920000, 1.29673000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42216, 'Grisy-Suisnes', 4796, 'IDF', 75, 'FR', 48.68538000, 2.66781000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42217, 'Grièges', 4798, 'ARA', 75, 'FR', 46.25619000, 4.84856000, '2019-10-05 22:48:06', '2020-05-01 17:22:43', 1, 'Q384722'),
(42218, 'Groffliers', 4828, 'HDF', 75, 'FR', 50.38444000, 1.61474000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42219, 'Groissiat', 4798, 'ARA', 75, 'FR', 46.22213000, 5.60775000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q384722'),
(42220, 'Groisy', 4798, 'ARA', 75, 'FR', 46.01008000, 6.16983000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q775014'),
(42221, 'Groix', 4807, 'BRE', 75, 'FR', 47.63887000, -3.45430000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q70086'),
(42222, 'Gron', 4825, 'BFC', 75, 'FR', 48.15967000, 3.26345000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q687401'),
(42223, 'Gros-Réderching', 4820, 'GES', 75, 'FR', 49.06835000, 7.21914000, '2019-10-05 22:48:06', '2020-05-01 17:22:44', 1, 'Q687401'),
(42224, 'Grosbliederstroff', 4820, 'GES', 75, 'FR', 49.15808000, 7.02413000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q22395'),
(42225, 'Grosbreuil', 4802, 'PDL', 75, 'FR', 46.53946000, -1.61655000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, '********'),
(42226, 'Groslay', 4796, 'IDF', 75, 'FR', 48.98561000, 2.34736000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, '********'),
(42227, 'Grosseto-Prugna', 4806, '20R', 75, 'FR', 41.87097000, 8.96403000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, '********'),
(42228, 'Gruchet-le-Valasse', 4804, 'NOR', 75, 'FR', 49.55466000, 0.48684000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, '********'),
(42229, 'Gruffy', 4798, 'ARA', 75, 'FR', 45.78897000, 6.05648000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, '********'),
(42230, 'Gruissan', 4799, 'OCC', 75, 'FR', 43.10759000, 3.08651000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q815593'),
(42231, 'Gruson', 4828, 'HDF', 75, 'FR', 50.59583000, 3.20777000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q815593'),
(42232, 'Grâces', 4807, 'BRE', 75, 'FR', 48.55651000, -3.18533000, '2019-10-05 22:48:06', '2020-05-01 17:22:44', 1, 'Q815593'),
(42233, 'Gréasque', 4812, 'PAC', 75, 'FR', 43.43310000, 5.54449000, '2019-10-05 22:48:06', '2020-05-01 17:22:46', 1, 'Q676005'),
(42234, 'Gréoux-les-Bains', 4812, 'PAC', 75, 'FR', 43.75844000, 5.88351000, '2019-10-05 22:48:06', '2020-05-01 17:22:46', 1, 'Q455660'),
(42235, 'Grésy-sur-Aix', 4798, 'ARA', 75, 'FR', 45.72311000, 5.92522000, '2019-10-05 22:48:06', '2020-05-01 17:22:43', 1, 'Q455660'),
(42236, 'Grésy-sur-Isère', 4798, 'ARA', 75, 'FR', 45.59898000, 6.25358000, '2019-10-05 22:48:06', '2020-05-01 17:22:43', 1, 'Q455660'),
(42237, 'Grézieu-la-Varenne', 4798, 'ARA', 75, 'FR', 45.74736000, 4.69037000, '2019-10-05 22:48:06', '2020-05-01 17:22:43', 1, 'Q581290'),
(42238, 'Guarbecque', 4828, 'HDF', 75, 'FR', 50.61162000, 2.48895000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q581290'),
(42239, 'Guebwiller', 4820, 'GES', 75, 'FR', 47.91667000, 7.20000000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q209814'),
(42240, 'Guengat', 4807, 'BRE', 75, 'FR', 48.04193000, -4.20470000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q431567'),
(42241, 'Guenrouet', 4802, 'PDL', 75, 'FR', 47.51881000, -1.95381000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q431567'),
(42242, 'Guer', 4807, 'BRE', 75, 'FR', 47.90619000, -2.12314000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q69990'),
(42243, 'Guerlesquin', 4807, 'BRE', 75, 'FR', 48.51758000, -3.58583000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q69990'),
(42244, 'Guermantes', 4796, 'IDF', 75, 'FR', 48.85303000, 2.70495000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q69990'),
(42245, 'Guern', 4807, 'BRE', 75, 'FR', 48.02964000, -3.09145000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q69990'),
(42246, 'Guerville', 4796, 'IDF', 75, 'FR', 48.94388000, 1.73429000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q69990'),
(42247, 'Guesnain', 4828, 'HDF', 75, 'FR', 50.35000000, 3.15000000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q69990'),
(42248, 'Gueugnon', 4825, 'BFC', 75, 'FR', 46.60357000, 4.06286000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q431817'),
(42249, 'Gueux', 4820, 'GES', 75, 'FR', 49.25048000, 3.91024000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q431817'),
(42250, 'Guewenheim', 4820, 'GES', 75, 'FR', 47.75283000, 7.09253000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q431817'),
(42251, 'Guichainville', 4804, 'NOR', 75, 'FR', 49.00139000, 1.19305000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q431817'),
(42252, 'Guichen', 4807, 'BRE', 75, 'FR', 47.96762000, -1.79534000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q864120'),
(42253, 'Guiclan', 4807, 'BRE', 75, 'FR', 48.54967000, -3.96211000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q475506'),
(42254, 'Guidel-Plage', 4807, 'BRE', 75, 'FR', 47.76768000, -3.52180000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q475506'),
(42255, 'Guignen', 4807, 'BRE', 75, 'FR', 47.92009000, -1.86161000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q1074650'),
(42256, 'Guignes', 4796, 'IDF', 75, 'FR', 48.63333000, 2.80000000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q243598'),
(42257, 'Guignicourt', 4828, 'HDF', 75, 'FR', 49.43714000, 3.96755000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q243598'),
(42258, 'Guilers', 4807, 'BRE', 75, 'FR', 48.42545000, -4.55801000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q328795'),
(42259, 'Guilherand-Granges', 4798, 'ARA', 75, 'FR', 44.93278000, 4.87372000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q328795'),
(42260, 'Guillac', 4807, 'BRE', 75, 'FR', 47.91096000, -2.46571000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q328795'),
(42261, 'Guillestre', 4812, 'PAC', 75, 'FR', 44.65950000, 6.64948000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q382624'),
(42262, 'Guilliers', 4807, 'BRE', 75, 'FR', 48.04286000, -2.40562000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q382624'),
(42263, 'Guilvinec', 4807, 'BRE', 75, 'FR', 47.79861000, -4.28111000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q381260'),
(42264, 'Guingamp', 4807, 'BRE', 75, 'FR', 48.56251000, -3.15096000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q193808'),
(42265, 'Guipavas', 4807, 'BRE', 75, 'FR', 48.43522000, -4.39722000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q325889'),
(42266, 'Guipel', 4807, 'BRE', 75, 'FR', 48.29937000, -1.71866000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q325889'),
(42267, 'Guipry', 4807, 'BRE', 75, 'FR', 47.82637000, -1.84218000, '2019-10-05 22:48:06', '2019-10-05 22:48:06', 1, 'Q1074620'),
(42268, 'Guiscard', 4828, 'HDF', 75, 'FR', 49.65660000, 3.05127000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q1074620'),
(42269, 'Guiscriff', 4807, 'BRE', 75, 'FR', 48.04944000, -3.64356000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q70470'),
(42270, 'Guise', 4828, 'HDF', 75, 'FR', 49.90055000, 3.62801000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q271093'),
(42271, 'Guissény', 4807, 'BRE', 75, 'FR', 48.63292000, -4.40884000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q634074'),
(42272, 'Gujan-Mestras', 4795, 'NAQ', 75, 'FR', 44.63333000, -1.06667000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q634074'),
(42273, 'Gumbrechtshoffen', 4820, 'GES', 75, 'FR', 48.90580000, 7.62915000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q634074'),
(42274, 'Gundershoffen', 4820, 'GES', 75, 'FR', 48.90648000, 7.66096000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q634074'),
(42275, 'Gurgy', 4825, 'BFC', 75, 'FR', 47.86712000, 3.56341000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q634074'),
(42276, 'Guyancourt', 4796, 'IDF', 75, 'FR', 48.77334000, 2.07393000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q237303'),
(42277, 'Guécélard', 4802, 'PDL', 75, 'FR', 47.87751000, 0.12930000, '2019-10-05 22:48:07', '2020-05-01 17:22:46', 1, 'Q903880'),
(42278, 'Guégon', 4807, 'BRE', 75, 'FR', 47.93707000, -2.56450000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q71329'),
(42279, 'Guémar', 4820, 'GES', 75, 'FR', 48.18869000, 7.39706000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q381598'),
(42280, 'Guémené-Penfao', 4802, 'PDL', 75, 'FR', 47.63333000, -1.83333000, '2019-10-05 22:48:07', '2020-05-01 17:22:46', 1, 'Q381598'),
(42281, 'Guémené-sur-Scorff', 4807, 'BRE', 75, 'FR', 48.06667000, -3.20000000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q70623'),
(42282, 'Guénange', 4820, 'GES', 75, 'FR', 49.29945000, 6.20535000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q22341'),
(42283, 'Guénin', 4807, 'BRE', 75, 'FR', 47.90757000, -2.97941000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q69919'),
(42284, 'Guérande', 4802, 'PDL', 75, 'FR', 47.32911000, -2.42829000, '2019-10-05 22:48:07', '2020-05-01 17:22:46', 1, 'Q501971'),
(42285, 'Guérard', 4796, 'IDF', 75, 'FR', 48.82086000, 2.95969000, '2019-10-05 22:48:07', '2020-05-01 17:22:43', 1, 'Q501971'),
(42286, 'Guéreins', 4798, 'ARA', 75, 'FR', 46.10346000, 4.77275000, '2019-10-05 22:48:07', '2020-05-01 17:22:43', 1, 'Q501971'),
(42287, 'Guéret', 4795, 'NAQ', 75, 'FR', 46.17234000, 1.87456000, '2019-10-05 22:48:07', '2020-05-01 17:22:45', 1, 'Q188511'),
(42288, 'Guérigny', 4825, 'BFC', 75, 'FR', 47.08703000, 3.20182000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, '********'),
(42289, 'Guéthary', 4795, 'NAQ', 75, 'FR', 43.42285000, -1.61073000, '2019-10-05 22:48:07', '2020-05-01 17:22:45', 1, 'Q672766'),
(42290, 'Guînes', 4828, 'HDF', 75, 'FR', 50.86708000, 1.87025000, '2019-10-05 22:48:07', '2020-05-01 17:22:45', 1, 'Q325999'),
(42291, 'Guîtres', 4795, 'NAQ', 75, 'FR', 45.03333000, -0.18333000, '2019-10-05 22:48:07', '2020-05-01 17:22:45', 1, 'Q822213'),
(42292, 'Gy', 4825, 'BFC', 75, 'FR', 47.40588000, 5.81226000, '2019-10-05 22:48:07', '2019-10-05 22:48:07', 1, 'Q822213'),
(42293, 'Gémenos', 4812, 'PAC', 75, 'FR', 43.29751000, 5.62843000, '2019-10-05 22:48:07', '2020-05-01 17:22:46', 1, 'Q822213'),
(42294, 'Gémozac', 4795, 'NAQ', 75, 'FR', 45.56896000, -0.67574000, '2019-10-05 22:48:07', '2020-05-01 17:22:45', 1, '********'),
(42295, 'Génelard', 4825, 'BFC', 75, 'FR', 46.58119000, 4.23619000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, '********'),
(42296, 'Génissac', 4795, 'NAQ', 75, 'FR', 44.85000000, -0.25000000, '2019-10-05 22:48:07', '2020-05-01 17:22:45', 1, '********'),
(42297, 'Génissieux', 4798, 'ARA', 75, 'FR', 45.08413000, 5.08133000, '2019-10-05 22:48:07', '2020-05-01 17:22:43', 1, '********'),
(42298, 'Générac', 4799, 'OCC', 75, 'FR', 43.72830000, 4.35000000, '2019-10-05 22:48:07', '2020-05-01 17:22:46', 1, '********'),
(42299, 'Gérardmer', 4820, 'GES', 75, 'FR', 48.07346000, 6.87787000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q461219'),
(42300, 'Gétigné', 4802, 'PDL', 75, 'FR', 47.07650000, -1.24810000, '2019-10-05 22:48:07', '2020-05-01 17:22:46', 1, '********'),
(42301, 'Gévezé', 4807, 'BRE', 75, 'FR', 48.21933000, -1.78952000, '2019-10-05 22:48:07', '2020-05-01 17:22:44', 1, 'Q220149');

