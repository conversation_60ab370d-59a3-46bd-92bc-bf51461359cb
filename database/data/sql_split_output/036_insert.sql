INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(17680, 'Delémont', 1658, 'J<PERSON>', 214, 'CH', 47.36493000, 7.34453000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q63896'),
(17681, 'Delémont District', 1658, 'JU', 214, 'CH', 47.35434000, 7.33053000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q659702'),
(17682, 'Den<PERSON>büren', 1639, 'AG', 214, 'CH', 47.45260000, 8.05330000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22622647'),
(17683, 'Derendingen', 1662, '<PERSON><PERSON>', 214, 'CH', 47.19850000, 7.58844000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22623465'),
(17684, '<PERSON>gten', 1641, 'BL', 214, '<PERSON>', 47.41380000, 7.81085000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630012'),
(17685, 'Die<PERSON>dorf', 1656, 'ZH', 214, 'CH', 47.48146000, 8.45850000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630050'),
(17686, 'Diemtigen', 1645, 'BE', 214, 'CH', 46.64928000, 7.56477000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630083'),
(17687, 'Diepoldsau', 1644, 'SG', 214, 'CH', 47.38600000, 9.65558000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630126'),
(17688, 'Diessenhofen', 1657, 'TG', 214, 'CH', 47.68908000, 8.74958000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q67128'),
(17689, 'Dietikon', 1656, 'ZH', 214, 'CH', 47.40165000, 8.40015000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630211'),
(17690, 'Dietikon / Almend', 1656, 'ZH', 214, 'CH', 47.40477000, 8.39168000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630220'),
(17691, 'Dietikon / Guggenbühl', 1656, 'ZH', 214, 'CH', 47.40009000, 8.40818000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22630235'),
(17692, 'Dietikon / Hofacker', 1656, 'ZH', 214, 'CH', 47.39718000, 8.41609000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630240'),
(17693, 'Dietikon / Kreuzacker', 1656, 'ZH', 214, 'CH', 47.39919000, 8.40146000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630254'),
(17694, 'Dietikon / Oberdorf', 1656, 'ZH', 214, 'CH', 47.40065000, 8.39416000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630259'),
(17695, 'Dietikon / Schönenwerd', 1656, 'ZH', 214, 'CH', 47.39706000, 8.42576000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22630273'),
(17696, 'Dietikon / Vorstadt', 1656, 'ZH', 214, 'CH', 47.40819000, 8.39719000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630286'),
(17697, 'Dietlikon / Dietlikon (Dorf)', 1656, 'ZH', 214, 'CH', 47.41827000, 8.61880000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q31843690'),
(17698, 'Dietlikon / Eichwiesen', 1656, 'ZH', 214, 'CH', 47.41910000, 8.62078000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22630312'),
(17699, 'Disentis', 1660, 'GR', 214, 'CH', 46.70341000, 8.85090000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q68347'),
(17700, 'Domat', 1660, 'GR', 214, 'CH', 46.83483000, 9.45075000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q68347'),
(17701, 'Dombresson', 1659, 'NE', 214, 'CH', 47.07192000, 6.95920000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q70459'),
(17702, 'Domdidier', 1640, 'FR', 214, 'CH', 46.86716000, 7.01337000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q67681'),
(17703, 'Dorf', 1656, 'ZH', 214, 'CH', 47.23900000, 8.73567000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22586902'),
(17704, 'Dornach', 1662, 'SO', 214, 'CH', 47.48038000, 7.61644000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22586902'),
(17705, 'Dottikon', 1639, 'AG', 214, 'CH', 47.38437000, 8.23981000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22588230'),
(17706, 'Dällikon / Dällikon (Dorf)', 1656, 'ZH', 214, 'CH', 47.43983000, 8.43813000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22609979'),
(17707, 'Dänikon', 1656, 'ZH', 214, 'CH', 47.44674000, 8.40648000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22610022'),
(17708, 'Därligen', 1645, 'BE', 214, 'CH', 46.66175000, 7.80808000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22610065'),
(17709, 'Dübendorf', 1656, 'ZH', 214, 'CH', 47.39724000, 8.61872000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22608376'),
(17710, 'Dübendorf / Kunklerstrasse', 1656, 'ZH', 214, 'CH', 47.40109000, 8.62724000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22608612'),
(17711, 'Dübendorf / Sonnenberg', 1656, 'ZH', 214, 'CH', 47.39469000, 8.63162000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22608669'),
(17712, 'Dübendorf / Vogelquartier', 1656, 'ZH', 214, 'CH', 47.39502000, 8.61184000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22608708'),
(17713, 'Dübendorf / Wasserfurren', 1656, 'ZH', 214, 'CH', 47.39352000, 8.60850000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22608717'),
(17714, 'Düdingen', 1640, 'FR', 214, 'CH', 46.84916000, 7.19150000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22608802'),
(17715, 'Dürnten', 1656, 'ZH', 214, 'CH', 47.27856000, 8.84156000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q67220'),
(17716, 'Dürrenroth', 1645, 'BE', 214, 'CH', 47.08953000, 7.79170000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22609464'),
(17717, 'Dürrenäsch', 1639, 'AG', 214, 'CH', 47.32094000, 8.15874000, '2019-10-05 22:35:53', '2020-05-01 17:23:18', 1, 'Q22609484'),
(17718, 'Ebikon', 1663, 'LU', 214, 'CH', 47.07937000, 8.34041000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22362748'),
(17719, 'Ebmatingen', 1656, 'ZH', 214, 'CH', 47.34987000, 8.64013000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22698506'),
(17720, 'Ebnat-Kappel', 1644, 'SG', 214, 'CH', 47.26195000, 9.12473000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22698506'),
(17721, 'Echallens', 1651, 'VD', 214, 'CH', 46.64130000, 6.63317000, '2019-10-05 22:35:53', '2019-10-05 22:35:53', 1, 'Q22364065'),
(17722, 'Ecublens', 1651, 'VD', 214, 'CH', 46.52899000, 6.56261000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22364065'),
(17723, 'Ecublens', 1640, 'FR', 214, 'CH', 46.60735000, 6.80895000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22366537'),
(17724, 'Effretikon', 1656, 'ZH', 214, 'CH', 47.42575000, 8.69094000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22698735'),
(17725, 'Effretikon / Rappenhalde-Bannhalde', 1656, 'ZH', 214, 'CH', 47.42388000, 8.69653000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22374589'),
(17726, 'Effretikon / Rikon', 1656, 'ZH', 214, 'CH', 47.43099000, 8.68624000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22374605'),
(17727, 'Effretikon / Watt', 1656, 'ZH', 214, 'CH', 47.42797000, 8.69822000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22374629'),
(17728, 'Egerkingen', 1662, 'SO', 214, 'CH', 47.31957000, 7.78424000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22375175'),
(17729, 'Egg', 1656, 'ZH', 214, 'CH', 47.29976000, 8.69032000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22375370'),
(17730, 'Eggersriet', 1644, 'SG', 214, 'CH', 47.44202000, 9.46901000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q66868'),
(17731, 'Eggiwil', 1645, 'BE', 214, 'CH', 46.87575000, 7.79567000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22376488'),
(17732, 'Egliswil', 1639, 'AG', 214, 'CH', 47.34922000, 8.18553000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22376926'),
(17733, 'Egnach', 1657, 'TG', 214, 'CH', 47.54268000, 9.38048000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22377046'),
(17734, 'Ehrendingen', 1639, 'AG', 214, 'CH', 47.50250000, 8.34729000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22377720'),
(17735, 'Eich', 1663, 'LU', 214, 'CH', 47.15116000, 8.16695000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22377806'),
(17736, 'Eichberg', 1644, 'SG', 214, 'CH', 47.34374000, 9.53140000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22377845'),
(17737, 'Eiken', 1639, 'AG', 214, 'CH', 47.53361000, 7.98801000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22379634'),
(17738, 'Einsiedeln', 1653, 'SZ', 214, 'CH', 47.12849000, 8.74735000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22380143'),
(17739, 'Elgg', 1656, 'ZH', 214, 'CH', 47.49715000, 8.86523000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22387433'),
(17740, 'Elgg / Städtchen und Umgebung', 1656, 'ZH', 214, 'CH', 47.49265000, 8.86680000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22387473'),
(17741, 'Elsau-Räterschen', 1656, 'ZH', 214, 'CH', 47.50238000, 8.79874000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22396255'),
(17742, 'Elsau-Räterschen / Räterschen', 1656, 'ZH', 214, 'CH', 47.49927000, 8.79600000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22396296'),
(17743, 'Embrach', 1656, 'ZH', 214, 'CH', 47.50561000, 8.59406000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22398804'),
(17744, 'Embrach / Embrach (Dorfkern)', 1656, 'ZH', 214, 'CH', 47.50400000, 8.59477000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22398870'),
(17745, 'Embrach / Kellersacker', 1656, 'ZH', 214, 'CH', 47.51455000, 8.59146000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22398888'),
(17746, 'Emmen', 1663, 'LU', 214, 'CH', 47.07819000, 8.27331000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22406934'),
(17747, 'Emmental District', 1645, 'BE', 214, 'CH', 47.04425000, 7.66176000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q666253'),
(17748, 'Emmetten', 1652, 'NW', 214, 'CH', 46.95658000, 8.51467000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22407232'),
(17749, 'Endingen', 1639, 'AG', 214, 'CH', 47.53742000, 8.29036000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22702045'),
(17750, 'Engelberg', 1650, 'OW', 214, 'CH', 46.82107000, 8.40133000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22702045'),
(17751, 'Ennenda', 1661, 'GL', 214, 'CH', 47.03363000, 9.07888000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q659434'),
(17752, 'Ennetbürgen', 1652, 'NW', 214, 'CH', 46.98423000, 8.41003000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22412498'),
(17753, 'Entlebuch', 1663, 'LU', 214, 'CH', 46.99559000, 8.06354000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22414457'),
(17754, 'Entlebuch District', 1663, 'LU', 214, 'CH', 46.92590000, 8.01427000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22414457'),
(17755, 'Entremont District', 1648, 'VS', 214, 'CH', 46.02099000, 7.21260000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q660495'),
(17756, 'Epalinges', 1651, 'VD', 214, 'CH', 46.54896000, 6.66831000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22415728'),
(17757, 'Ependes', 1640, 'FR', 214, 'CH', 46.75368000, 7.14609000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22416046'),
(17758, 'Eriswil', 1645, 'BE', 214, 'CH', 47.07816000, 7.85149000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22416046'),
(17759, 'Erlach', 1645, 'BE', 214, 'CH', 47.04220000, 7.09728000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22418892'),
(17760, 'Erlen', 1657, 'TG', 214, 'CH', 47.54814000, 9.23415000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22418998'),
(17761, 'Erlenbach', 1656, 'ZH', 214, 'CH', 47.30298000, 8.59743000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22419022'),
(17762, 'Erlenbach / links des Dorfbachs oberhalb Bahnlinie', 1656, 'ZH', 214, 'CH', 47.29950000, 8.60188000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22419043'),
(17763, 'Erlenbach / rechts des Dorfbachs oberhalb Bahnlinie', 1656, 'ZH', 214, 'CH', 47.30658000, 8.60271000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22419086'),
(17764, 'Erlenbach im Simmental', 1645, 'BE', 214, 'CH', 46.66021000, 7.55445000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22419144'),
(17765, 'Erlinsbach', 1662, 'SO', 214, 'CH', 47.39753000, 8.00797000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q689567'),
(17766, 'Ermatingen', 1657, 'TG', 214, 'CH', 47.67057000, 9.08573000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22419347'),
(17767, 'Ersigen', 1645, 'BE', 214, 'CH', 47.09368000, 7.59507000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22420533'),
(17768, 'Erstfeld', 1642, 'UR', 214, 'CH', 46.81885000, 8.65052000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22420533'),
(17769, 'Eschenbach', 1644, 'SG', 214, 'CH', 47.23981000, 8.92156000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22421781'),
(17770, 'Eschenz', 1657, 'TG', 214, 'CH', 47.64793000, 8.87472000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22421820'),
(17771, 'Eschlikon', 1657, 'TG', 214, 'CH', 47.46361000, 8.96381000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22421820'),
(17772, 'Escholzmatt', 1663, 'LU', 214, 'CH', 46.91350000, 7.93426000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q66908'),
(17773, 'Esslingen', 1656, 'ZH', 214, 'CH', 47.28325000, 8.71038000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q687918'),
(17774, 'Estavayer-le-Lac', 1640, 'FR', 214, 'CH', 46.84876000, 6.84650000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q67516'),
(17775, 'Ettingen', 1641, 'BL', 214, 'CH', 47.48225000, 7.54654000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22445194'),
(17776, 'Ettiswil', 1663, 'LU', 214, 'CH', 47.15031000, 8.01759000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q14592'),
(17777, 'Evilard', 1645, 'BE', 214, 'CH', 47.15046000, 7.23895000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22447932'),
(17778, 'Evionnaz', 1648, 'VS', 214, 'CH', 46.18096000, 7.02232000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22447932'),
(17779, 'Evolène', 1648, 'VS', 214, 'CH', 46.11422000, 7.49407000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22447932'),
(17780, 'Faido', 1643, 'TI', 214, 'CH', 46.47700000, 8.80125000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22450710'),
(17781, 'Fehraltorf', 1656, 'ZH', 214, 'CH', 47.38775000, 8.75149000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22450710'),
(17782, 'Feldmeilen', 1656, 'ZH', 214, 'CH', 47.27873000, 8.62165000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q5441654'),
(17783, 'Felsberg', 1660, 'GR', 214, 'CH', 46.84566000, 9.47588000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q64961'),
(17784, 'Ferenbalm', 1645, 'BE', 214, 'CH', 46.94880000, 7.21124000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22461483'),
(17785, 'Ferreyres', 1651, 'VD', 214, 'CH', 46.65804000, 6.48520000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q68500'),
(17786, 'Feuerthalen', 1656, 'ZH', 214, 'CH', 47.69054000, 8.64357000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22463759'),
(17787, 'Feusisberg', 1653, 'SZ', 214, 'CH', 47.18707000, 8.74724000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22463784'),
(17788, 'Fiesch', 1648, 'VS', 214, 'CH', 46.39981000, 8.13533000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22464667'),
(17789, 'Fischingen', 1657, 'TG', 214, 'CH', 47.41422000, 8.96862000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22471713'),
(17790, 'Flamatt', 1640, 'FR', 214, 'CH', 46.88994000, 7.32204000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22700500'),
(17791, 'Flawil', 1644, 'SG', 214, 'CH', 47.41301000, 9.18324000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22482402'),
(17792, 'Fleurier', 1659, 'NE', 214, 'CH', 46.90224000, 6.58253000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q628354'),
(17793, 'Flims', 1660, 'GR', 214, 'CH', 46.83705000, 9.28458000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q628354'),
(17794, 'Flums', 1644, 'SG', 214, 'CH', 47.09058000, 9.34301000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q628354'),
(17795, 'Flurlingen', 1656, 'ZH', 214, 'CH', 47.68390000, 8.62995000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q66757'),
(17796, 'Flüelen', 1642, 'UR', 214, 'CH', 46.90478000, 8.62396000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22487014'),
(17797, 'Flühli', 1663, 'LU', 214, 'CH', 46.88391000, 8.01558000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22487066'),
(17798, 'Fontainemelon', 1659, 'NE', 214, 'CH', 47.05495000, 6.88680000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q68544'),
(17799, 'Fontenais', 1658, 'JU', 214, 'CH', 47.40292000, 7.08108000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q68780'),
(17800, 'Founex', 1651, 'VD', 214, 'CH', 46.33277000, 6.19243000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22498734'),
(17801, 'Franches-Montagnes District', 1658, 'JU', 214, 'CH', 47.24935000, 7.04562000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q167627'),
(17802, 'Fraubrunnen', 1645, 'BE', 214, 'CH', 47.08620000, 7.52727000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q167627'),
(17803, 'Frauenfeld', 1657, 'TG', 214, 'CH', 47.55776000, 8.89893000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q68120'),
(17804, 'Frauenfeld District', 1657, 'TG', 214, 'CH', 47.61115000, 8.89444000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q660546'),
(17805, 'Frauenkappelen', 1645, 'BE', 214, 'CH', 46.95425000, 7.33835000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22508037'),
(17806, 'Freienbach', 1653, 'SZ', 214, 'CH', 47.20534000, 8.75842000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22509889'),
(17807, 'Freienstein', 1656, 'ZH', 214, 'CH', 47.53307000, 8.58455000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22701069'),
(17808, 'Frenkendorf', 1641, 'BL', 214, 'CH', 47.50686000, 7.71648000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22511682'),
(17809, 'Fribourg', 1640, 'FR', 214, 'CH', 46.80237000, 7.15128000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22511682'),
(17810, 'Frick', 1639, 'AG', 214, 'CH', 47.51169000, 8.02471000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22578706'),
(17811, 'Froideville', 1651, 'VD', 214, 'CH', 46.60123000, 6.68085000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22578706'),
(17812, 'Frutigen', 1645, 'BE', 214, 'CH', 46.58723000, 7.64945000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22578706'),
(17813, 'Frutigen-Niedersimmental District', 1645, 'BE', 214, 'CH', 46.60236000, 7.62292000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q661974'),
(17814, 'Fulenbach', 1662, 'SO', 214, 'CH', 47.27103000, 7.83136000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22617552'),
(17815, 'Fully', 1648, 'VS', 214, 'CH', 46.13851000, 7.11468000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q70137'),
(17816, 'Fällanden', 1656, 'ZH', 214, 'CH', 47.37169000, 8.63869000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q64869'),
(17817, 'Füllinsdorf', 1641, 'BL', 214, 'CH', 47.50688000, 7.73129000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22619754'),
(17818, 'Gachnang', 1657, 'TG', 214, 'CH', 47.53935000, 8.85311000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22620576'),
(17819, 'Gais', 1655, 'AR', 214, 'CH', 47.36150000, 9.45356000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22621764'),
(17820, 'Gampel', 1648, 'VS', 214, 'CH', 46.31599000, 7.74210000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q689575'),
(17821, 'Gams', 1644, 'SG', 214, 'CH', 47.20429000, 9.44172000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22624991'),
(17822, 'Ganterschwil', 1644, 'SG', 214, 'CH', 47.38103000, 9.09239000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q65461'),
(17823, 'Gattikon', 1656, 'ZH', 214, 'CH', 47.28439000, 8.54830000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22631161'),
(17824, 'Gebenstorf', 1639, 'AG', 214, 'CH', 47.48136000, 8.23949000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22633025'),
(17825, 'Gelterkinden', 1641, 'BL', 214, 'CH', 47.46497000, 7.85174000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22634007'),
(17826, 'Genolier', 1651, 'VD', 214, 'CH', 46.43537000, 6.21809000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q69290'),
(17827, 'Geneva', 1647, 'GE', 214, 'CH', 46.20222000, 6.14569000, '2019-10-05 22:35:54', '2025-03-28 12:37:53', 1, 'Q69290'),
(17828, 'Gerlafingen', 1662, 'SO', 214, 'CH', 47.17087000, 7.57249000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22637456'),
(17829, 'Geroldswil', 1656, 'ZH', 214, 'CH', 47.42213000, 8.41085000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22637782'),
(17830, 'Gersau', 1653, 'SZ', 214, 'CH', 46.99419000, 8.52500000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22638089'),
(17831, 'Geuensee', 1663, 'LU', 214, 'CH', 47.19970000, 8.10689000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22638501'),
(17832, 'Giffers', 1640, 'FR', 214, 'CH', 46.76230000, 7.20845000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22638501'),
(17833, 'Gimel', 1651, 'VD', 214, 'CH', 46.50945000, 6.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22644154'),
(17834, 'Gipf-Oberfrick', 1639, 'AG', 214, 'CH', 47.49875000, 8.00497000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22644619'),
(17835, 'Giswil', 1650, 'OW', 214, 'CH', 46.83333000, 8.18065000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22645320'),
(17836, 'Giubiasco', 1643, 'TI', 214, 'CH', 46.17246000, 9.00793000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22645562'),
(17837, 'Givisiez', 1640, 'FR', 214, 'CH', 46.81201000, 7.12639000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22645562'),
(17838, 'Gland', 1651, 'VD', 214, 'CH', 46.42082000, 6.27010000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q69300'),
(17839, 'Glarus', 1661, 'GL', 214, 'CH', 47.04057000, 9.06804000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q15730054'),
(17840, 'Glattbrugg', 1656, 'ZH', 214, 'CH', 47.43130000, 8.56272000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22702084'),
(17841, 'Glattbrugg / Rohr/Platten-Balsberg', 1656, 'ZH', 214, 'CH', 47.43721000, 8.56642000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22647146'),
(17842, 'Glattbrugg / Wydacker/Bettacker/Lättenwiesen', 1656, 'ZH', 214, 'CH', 47.42908000, 8.56657000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22647157'),
(17843, 'Glattfelden', 1656, 'ZH', 214, 'CH', 47.55871000, 8.50167000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22647205'),
(17844, 'Glovelier', 1658, 'JU', 214, 'CH', 47.33534000, 7.20556000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q68494'),
(17845, 'Glâne District', 1640, 'FR', 214, 'CH', 46.66667000, 6.91667000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q660374'),
(17846, 'Gockhausen', 1656, 'ZH', 214, 'CH', 47.38098000, 8.59978000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22702178'),
(17847, 'Goldach', 1644, 'SG', 214, 'CH', 47.47401000, 9.46711000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22359812'),
(17848, 'Goldau', 1653, 'SZ', 214, 'CH', 47.04761000, 8.54616000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q474619'),
(17849, 'Goldingen', 1644, 'SG', 214, 'CH', 47.26477000, 8.96167000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q68408'),
(17850, 'Gommiswald', 1644, 'SG', 214, 'CH', 47.23128000, 9.02355000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22364530'),
(17851, 'Goms District', 1648, 'VS', 214, 'CH', 46.46161000, 8.22190000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q660663'),
(17852, 'Gonten', 1649, 'AI', 214, 'CH', 47.32725000, 9.34705000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22366733'),
(17853, 'Gontenschwil', 1639, 'AG', 214, 'CH', 47.27166000, 8.14396000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22366778'),
(17854, 'Gordola', 1643, 'TI', 214, 'CH', 46.18256000, 8.86657000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22376597'),
(17855, 'Gorgier', 1659, 'NE', 214, 'CH', 46.90143000, 6.77985000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22380135'),
(17856, 'Gossau', 1644, 'SG', 214, 'CH', 47.41551000, 9.25482000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22382609'),
(17857, 'Gossau', 1656, 'ZH', 214, 'CH', 47.30510000, 8.75831000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22382633'),
(17858, 'Gottlieben', 1657, 'TG', 214, 'CH', 47.66380000, 9.13371000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22383643'),
(17859, 'Grabs', 1644, 'SG', 214, 'CH', 47.18248000, 9.44395000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22383643'),
(17860, 'Grancy', 1651, 'VD', 214, 'CH', 46.59214000, 6.46391000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22401560'),
(17861, 'Grand-Savagnier', 1659, 'NE', 214, 'CH', 47.05101000, 6.95489000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22404273'),
(17862, 'Grandson', 1651, 'VD', 214, 'CH', 46.80946000, 6.64600000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22406467'),
(17863, 'Gravesano', 1643, 'TI', 214, 'CH', 46.04208000, 8.91832000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22421122'),
(17864, 'Greifensee', 1656, 'ZH', 214, 'CH', 47.36717000, 8.68115000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22439567'),
(17865, 'Greifensee / Müllerwis / Seilerwis', 1656, 'ZH', 214, 'CH', 47.37042000, 8.68151000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22439683'),
(17866, 'Greifensee / Pfisterhölzli', 1656, 'ZH', 214, 'CH', 47.36430000, 8.68979000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22439718'),
(17867, 'Grellingen', 1641, 'BL', 214, 'CH', 47.44231000, 7.58906000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22440045'),
(17868, 'Grenchen', 1662, 'SO', 214, 'CH', 47.19210000, 7.39586000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22440227'),
(17869, 'Grimisuat', 1648, 'VS', 214, 'CH', 46.25938000, 7.38408000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q69579'),
(17870, 'Grindelwald', 1645, 'BE', 214, 'CH', 46.62396000, 8.03601000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q69579'),
(17871, 'Grolley', 1640, 'FR', 214, 'CH', 46.83360000, 7.07116000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22650127'),
(17872, 'Gros-de-Vaud District', 1651, 'VD', 214, 'CH', 46.65180000, 6.65728000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q681215'),
(17873, 'Grossacker/Opfikon', 1656, 'ZH', 214, 'CH', 47.42696000, 8.57886000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22650620'),
(17874, 'Grossaffoltern', 1645, 'BE', 214, 'CH', 47.06534000, 7.35689000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22650628'),
(17875, 'Grosswangen', 1663, 'LU', 214, 'CH', 47.13310000, 8.05041000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22650825'),
(17876, 'Gruyère District', 1640, 'FR', 214, 'CH', 46.60753000, 7.10741000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q388306'),
(17877, 'Gruyères', 1640, 'FR', 214, 'CH', 46.58338000, 7.08207000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q69600'),
(17878, 'Gryon', 1651, 'VD', 214, 'CH', 46.27377000, 7.05975000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22651964'),
(17879, 'Grächen', 1648, 'VS', 214, 'CH', 46.19529000, 7.83745000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22652016'),
(17880, 'Gränichen', 1639, 'AG', 214, 'CH', 47.35930000, 8.10243000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22652016'),
(17881, 'Grône', 1648, 'VS', 214, 'CH', 46.25288000, 7.45947000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22652016'),
(17882, 'Grüsch', 1660, 'GR', 214, 'CH', 46.97965000, 9.64639000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q64635'),
(17883, 'Grüt', 1656, 'ZH', 214, 'CH', 47.31151000, 8.78339000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q64635'),
(17884, 'Gstaad', 1645, 'BE', 214, 'CH', 46.47215000, 7.28685000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q37149'),
(17885, 'Guggisberg', 1645, 'BE', 214, 'CH', 46.76756000, 7.32946000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22652800'),
(17886, 'Gunzgen', 1662, 'SO', 214, 'CH', 47.31375000, 7.83102000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22656336'),
(17887, 'Gunzwil', 1663, 'LU', 214, 'CH', 47.21072000, 8.17932000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q669254'),
(17888, 'Gutenswil', 1656, 'ZH', 214, 'CH', 47.38387000, 8.71763000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17889, 'Güttingen', 1657, 'TG', 214, 'CH', 47.60349000, 9.28742000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, '*********'),
(17890, 'Haag (Rheintal)', 1644, 'SG', 214, 'CH', 47.20989000, 9.48931000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17891, 'Hadlikon', 1656, 'ZH', 214, 'CH', 47.28750000, 8.85719000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17892, 'Hallau', 1654, 'SH', 214, 'CH', 47.69648000, 8.45827000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17893, 'Hasle', 1663, 'LU', 214, 'CH', 46.97787000, 8.05326000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17894, 'Haslen', 1649, 'AI', 214, 'CH', 47.36931000, 9.36752000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17895, 'Hausen', 1639, 'AG', 214, 'CH', 47.46396000, 8.20988000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17896, 'Hausen am Albis / Hausen (Dorf)', 1656, 'ZH', 214, 'CH', 47.24496000, 8.53299000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, '*********'),
(17897, 'Hedingen', 1656, 'ZH', 214, 'CH', 47.29794000, 8.44833000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q65129'),
(17898, 'Hegnau', 1656, 'ZH', 214, 'CH', 47.39227000, 8.66988000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22703149'),
(17899, 'Hegnau / Dammboden-Grindel', 1656, 'ZH', 214, 'CH', 47.38713000, 8.66657000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22613058'),
(17900, 'Hegnau / Sunnebüel-Eich', 1656, 'ZH', 214, 'CH', 47.39246000, 8.67910000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22613097'),
(17901, 'Heiden', 1655, 'AR', 214, 'CH', 47.44255000, 9.53293000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22613135'),
(17902, 'Heimberg', 1645, 'BE', 214, 'CH', 46.79482000, 7.60433000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22613276'),
(17903, 'Heimiswil', 1645, 'BE', 214, 'CH', 47.06755000, 7.66665000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22613320'),
(17904, 'Heitenried', 1640, 'FR', 214, 'CH', 46.82762000, 7.29944000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22613496'),
(17905, 'Henggart', 1656, 'ZH', 214, 'CH', 47.56272000, 8.68215000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22616507'),
(17906, 'Hergiswil', 1652, 'NW', 214, 'CH', 46.98429000, 8.30944000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22617966'),
(17907, 'Herisau', 1655, 'AR', 214, 'CH', 47.38615000, 9.27916000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22617966'),
(17908, 'Hermiswil', 1645, 'BE', 214, 'CH', 46.83125000, 7.47775000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22618343'),
(17909, 'Herrliberg', 1656, 'ZH', 214, 'CH', 47.29064000, 8.61464000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22619435'),
(17910, 'Herzogenbuchsee', 1645, 'BE', 214, 'CH', 47.18795000, 7.70620000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22619637'),
(17911, 'Hettlingen', 1656, 'ZH', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q64967'),
(17912, 'Hildisrieden', 1663, 'LU', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22624728'),
(17913, 'Hilterfingen', 1645, 'BE', 214, 'CH', 46.********, 7.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22626375'),
(17914, 'Himmelried', 1662, 'SO', 214, 'CH', 47.********, 7.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22626603'),
(17915, 'Hindelbank', 1645, 'BE', 214, 'CH', 47.********, 7.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22626763'),
(17916, 'Hinteregg', 1656, 'ZH', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22627440'),
(17917, 'Hinterrhein', 1660, 'GR', 214, 'CH', 46.********, 9.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22627440'),
(17918, 'Hinwil', 1656, 'ZH', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q69383'),
(17919, 'Hittnau / Hittnau (Dorf)', 1656, 'ZH', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q69383'),
(17920, 'Hitzkirch', 1663, 'LU', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22628561'),
(17921, 'Hochdorf', 1663, 'LU', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22629731'),
(17922, 'Hochdorf District', 1663, 'LU', 214, 'CH', 47.********, 8.********, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q1568996'),
(17923, 'Hochfelden', 1656, 'ZH', 214, 'CH', 47.52262000, 8.51564000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22629749'),
(17924, 'Hochwald', 1662, 'SO', 214, 'CH', 47.45389000, 7.64459000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22629840'),
(17925, 'Hofstetten', 1656, 'ZH', 214, 'CH', 47.47778000, 8.50646000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22630725'),
(17926, 'Hohenrain', 1663, 'LU', 214, 'CH', 47.18083000, 8.31802000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q7109'),
(17927, 'Hombrechtikon', 1656, 'ZH', 214, 'CH', 47.25298000, 8.77212000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22635601'),
(17928, 'Homburg', 1657, 'TG', 214, 'CH', 47.63469000, 9.00756000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22635714'),
(17929, 'Horgen', 1656, 'ZH', 214, 'CH', 47.25983000, 8.59778000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q68286'),
(17930, 'Horgen / Allmend', 1656, 'ZH', 214, 'CH', 47.24729000, 8.60660000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22640144'),
(17931, 'Horgen / Horgen (Dorfkern)', 1656, 'ZH', 214, 'CH', 47.25604000, 8.60159000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22640157'),
(17932, 'Horgen / Oberdorf', 1656, 'ZH', 214, 'CH', 47.25837000, 8.59013000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22640176'),
(17933, 'Horgen / Scheller-Stockerstrasse', 1656, 'ZH', 214, 'CH', 47.26584000, 8.58760000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22640176'),
(17934, 'Horn', 1644, 'SG', 214, 'CH', 47.49425000, 9.46246000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22640420'),
(17935, 'Horw', 1663, 'LU', 214, 'CH', 47.01692000, 8.30956000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22644684'),
(17936, 'Huttwil', 1645, 'BE', 214, 'CH', 47.11502000, 7.86209000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22652058'),
(17937, 'Hägendorf', 1662, 'SO', 214, 'CH', 47.33497000, 7.84133000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22652857'),
(17938, 'Häggenschwil', 1644, 'SG', 214, 'CH', 47.49462000, 9.34487000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q66087'),
(17939, 'Hägglingen', 1639, 'AG', 214, 'CH', 47.38851000, 8.25285000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22652879'),
(17940, 'Hérens District', 1648, 'VS', 214, 'CH', 46.19407000, 7.42391000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q633980'),
(17941, 'Hérémence', 1648, 'VS', 214, 'CH', 46.18032000, 7.40477000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q633980'),
(17942, 'Hölstein', 1641, 'BL', 214, 'CH', 47.42290000, 7.77041000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22444499'),
(17943, 'Höri', 1656, 'ZH', 214, 'CH', 47.50799000, 8.51203000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22409066'),
(17944, 'Hünenberg', 1646, 'ZG', 214, 'CH', 47.17536000, 8.42497000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22652584'),
(17945, 'Hüttwilen', 1657, 'TG', 214, 'CH', 47.60674000, 8.87343000, '2019-10-05 22:35:54', '2020-05-01 17:23:18', 1, 'Q22652813'),
(17946, 'Ibach', 1653, 'SZ', 214, 'CH', 47.01105000, 8.64538000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q687867'),
(17947, 'Igis', 1660, 'GR', 214, 'CH', 46.94531000, 9.57218000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q658739'),
(17948, 'Ilanz', 1660, 'GR', 214, 'CH', 46.77413000, 9.20461000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q64213'),
(17949, 'Illnau', 1656, 'ZH', 214, 'CH', 47.41130000, 8.72125000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22703545'),
(17950, 'Illnau / Unter-Illnau', 1656, 'ZH', 214, 'CH', 47.40787000, 8.72607000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22423516'),
(17951, 'Ingenbohl', 1653, 'SZ', 214, 'CH', 46.99880000, 8.61529000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22436765'),
(17952, 'Ins', 1645, 'BE', 214, 'CH', 47.00584000, 7.10609000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22441463'),
(17953, 'Interlaken', 1645, 'BE', 214, 'CH', 46.68387000, 7.86638000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22441463'),
(17954, 'Interlaken-Oberhasli District', 1645, 'BE', 214, 'CH', 46.68931000, 7.98869000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22441463'),
(17955, 'Inwil', 1663, 'LU', 214, 'CH', 47.12530000, 8.34885000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22445345'),
(17956, 'Itingen', 1641, 'BL', 214, 'CH', 47.46651000, 7.78502000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22445345'),
(17957, 'Jegenstorf', 1645, 'BE', 214, 'CH', 47.04802000, 7.50787000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22473993'),
(17958, 'Jenaz', 1660, 'GR', 214, 'CH', 46.92892000, 9.71275000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22473993'),
(17959, 'Jona', 1644, 'SG', 214, 'CH', 47.22983000, 8.83884000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q669350'),
(17960, 'Jonen', 1639, 'AG', 214, 'CH', 47.29750000, 8.39282000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22486116'),
(17961, 'Jongny', 1651, 'VD', 214, 'CH', 46.47883000, 6.84114000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22486116'),
(17962, 'Jonschwil', 1644, 'SG', 214, 'CH', 47.42402000, 9.08689000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22487207'),
(17963, 'Jura bernois', 1645, 'BE', 214, 'CH', 47.21892000, 7.21981000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q636280'),
(17964, 'Jura-Nord vaudois District', 1651, 'VD', 214, 'CH', 46.72981000, 6.45429000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q660762'),
(17965, 'Jussy', 1647, 'GE', 214, 'CH', 46.23590000, 6.26701000, '2019-10-05 22:35:54', '2019-10-05 22:35:54', 1, 'Q22492286'),
(17966, 'Kaiseraugst', 1639, 'AG', 214, 'CH', 47.53966000, 7.72605000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22504776'),
(17967, 'Kaisten', 1639, 'AG', 214, 'CH', 47.54160000, 8.04337000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22504869'),
(17968, 'Kallnach', 1645, 'BE', 214, 'CH', 47.02032000, 7.23545000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22511843'),
(17969, 'Kaltbrunn', 1644, 'SG', 214, 'CH', 47.21367000, 9.02590000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q66786'),
(17970, 'Kandersteg', 1645, 'BE', 214, 'CH', 46.49467000, 7.67326000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22526551'),
(17971, 'Kappelen', 1645, 'BE', 214, 'CH', 47.06015000, 7.26860000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22535153'),
(17972, 'Kehrsatz', 1645, 'BE', 214, 'CH', 46.91035000, 7.47096000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22369922'),
(17973, 'Kerns', 1650, 'OW', 214, 'CH', 46.90121000, 8.27514000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22386464'),
(17974, 'Kerzers', 1640, 'FR', 214, 'CH', 46.97586000, 7.19570000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22387822'),
(17975, 'Kilchberg', 1656, 'ZH', 214, 'CH', 47.32438000, 8.54548000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q66633'),
(17976, 'Kilchberg / Bächler-Stocken', 1656, 'ZH', 214, 'CH', 47.32728000, 8.53957000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22405294'),
(17977, 'Kilchberg / Kilchberg (Dorfkern)', 1656, 'ZH', 214, 'CH', 47.32012000, 8.54306000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22405332'),
(17978, 'Killwangen', 1639, 'AG', 214, 'CH', 47.43223000, 8.35097000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22408216'),
(17979, 'Kindhausen / Kindhausen (Dorf)', 1656, 'ZH', 214, 'CH', 47.40644000, 8.68296000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22415873'),
(17980, 'Kirchberg', 1644, 'SG', 214, 'CH', 47.41159000, 9.04020000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22429263'),
(17981, 'Kirchberg', 1645, 'BE', 214, 'CH', 47.08538000, 7.58288000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22429249'),
(17982, 'Kirchlindach', 1645, 'BE', 214, 'CH', 46.99965000, 7.41735000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22429368'),
(17983, 'Kleinandelfingen', 1656, 'ZH', 214, 'CH', 47.60058000, 8.68362000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22442585'),
(17984, 'Kleinlützel', 1662, 'SO', 214, 'CH', 47.42518000, 7.41607000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22543608'),
(17985, 'Klingnau', 1639, 'AG', 214, 'CH', 47.58361000, 8.24880000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22543965'),
(17986, 'Klosters Serneus', 1660, 'GR', 214, 'CH', 46.88918000, 9.83826000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22699156'),
(17987, 'Kloten', 1656, 'ZH', 214, 'CH', 47.45152000, 8.58491000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22699156'),
(17988, 'Kloten / Balsberg', 1656, 'ZH', 214, 'CH', 47.44233000, 8.57496000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544490'),
(17989, 'Kloten / Freienberg (Chanzler-Chlini Chaseren)', 1656, 'ZH', 214, 'CH', 47.46584000, 8.58145000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544515'),
(17990, 'Kloten / Geissberg', 1656, 'ZH', 214, 'CH', 47.45424000, 8.59066000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544525'),
(17991, 'Kloten / Holberg', 1656, 'ZH', 214, 'CH', 47.44642000, 8.57661000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544533'),
(17992, 'Kloten / Horainli', 1656, 'ZH', 214, 'CH', 47.45399000, 8.58306000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544542'),
(17993, 'Kloten / Hostrass', 1656, 'ZH', 214, 'CH', 47.45255000, 8.59464000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544550'),
(17994, 'Kloten / Kloten (Zentrum)', 1656, 'ZH', 214, 'CH', 47.45134000, 8.58683000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544561'),
(17995, 'Kloten / Rütlen', 1656, 'ZH', 214, 'CH', 47.44726000, 8.58808000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22544569'),
(17996, 'Kloten / Spitz', 1656, 'ZH', 214, 'CH', 47.44455000, 8.58724000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22544577'),
(17997, 'Knonau', 1656, 'ZH', 214, 'CH', 47.22350000, 8.46197000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22546268'),
(17998, 'Knutwil', 1663, 'LU', 214, 'CH', 47.19953000, 8.07315000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22546912'),
(17999, 'Koblenz', 1639, 'AG', 214, 'CH', 47.60972000, 8.23750000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22547330'),
(18000, 'Kollbrunn', 1656, 'ZH', 214, 'CH', 47.45793000, 8.78295000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q692007'),
(18001, 'Kollbrunn / Kollbrunn (Dorfkern)', 1656, 'ZH', 214, 'CH', 47.45748000, 8.77413000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22549708'),
(18002, 'Konolfingen', 1645, 'BE', 214, 'CH', 46.87909000, 7.62013000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22552090'),
(18003, 'Koppigen', 1645, 'BE', 214, 'CH', 47.13125000, 7.60525000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22552090'),
(18004, 'Krauchthal', 1645, 'BE', 214, 'CH', 47.00964000, 7.56640000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22557614'),
(18005, 'Kreuzlingen', 1657, 'TG', 214, 'CH', 47.65051000, 9.17504000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22557867'),
(18006, 'Kreuzlingen District', 1657, 'TG', 214, 'CH', 47.63046000, 9.16458000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q660450'),
(18007, 'Kriegstetten', 1662, 'SO', 214, 'CH', 47.17449000, 7.59799000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22557922'),
(18008, 'Kriens', 1663, 'LU', 214, 'CH', 47.03110000, 8.28547000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22557938'),
(18009, 'Krummenau', 1644, 'SG', 214, 'CH', 47.24755000, 9.17064000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q26202692'),
(18010, 'Kölliken', 1639, 'AG', 214, 'CH', 47.33337000, 8.02237000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565297'),
(18011, 'Köniz', 1645, 'BE', 214, 'CH', 46.92436000, 7.41457000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565315'),
(18012, 'Künten', 1639, 'AG', 214, 'CH', 47.38887000, 8.33045000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565012'),
(18013, 'Küsnacht', 1656, 'ZH', 214, 'CH', 47.31805000, 8.58401000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q69216'),
(18014, 'Küsnacht / Dorf', 1656, 'ZH', 214, 'CH', 47.31783000, 8.58303000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565080'),
(18015, 'Küsnacht / Goldbach', 1656, 'ZH', 214, 'CH', 47.32729000, 8.58077000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565091'),
(18016, 'Küsnacht / Heslibach', 1656, 'ZH', 214, 'CH', 47.31307000, 8.58849000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565101'),
(18017, 'Küsnacht / Itschnach', 1656, 'ZH', 214, 'CH', 47.32714000, 8.60068000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565108'),
(18018, 'Küsnacht / Schiedhalden', 1656, 'ZH', 214, 'CH', 47.32112000, 8.58881000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565118'),
(18019, 'Küssnacht', 1653, 'SZ', 214, 'CH', 47.08557000, 8.44206000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565125'),
(18020, 'Küttigen', 1639, 'AG', 214, 'CH', 47.41484000, 8.04979000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22565182'),
(18021, 'La Chaux-de-Fonds', 1659, 'NE', 214, 'CH', 47.09993000, 6.82586000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22565182'),
(18022, 'La Chaux-de-Fonds District', 1659, 'NE', 214, 'CH', 47.12159000, 6.84064000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q661152'),
(18023, 'La Neuveville', 1645, 'BE', 214, 'CH', 47.06592000, 7.09717000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22655396'),
(18024, 'La Punt Chamues-ch', 1660, 'GR', 214, 'CH', 46.57887000, 9.92015000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q65637'),
(18025, 'La Roche', 1640, 'FR', 214, 'CH', 46.69620000, 7.13721000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22584432'),
(18026, 'La Sarraz', 1651, 'VD', 214, 'CH', 46.65863000, 6.51077000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22584432'),
(18027, 'La Tour-de-Peilz', 1651, 'VD', 214, 'CH', 46.45312000, 6.85856000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22585077'),
(18028, 'La Tour-de-Trême', 1640, 'FR', 214, 'CH', 46.61061000, 7.06496000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q689582'),
(18029, 'Laax', 1660, 'GR', 214, 'CH', 46.80452000, 9.25787000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q689582'),
(18030, 'Lachen', 1653, 'SZ', 214, 'CH', 47.19224000, 8.85324000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22531440'),
(18031, 'Lake District', 1640, 'FR', 214, 'CH', 46.91016000, 7.14088000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q659778'),
(18032, 'Lancy', 1647, 'GE', 214, 'CH', 46.18981000, 6.11441000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22359172'),
(18033, 'Landquart', 1660, 'GR', 214, 'CH', 46.95000000, 9.56667000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q70631'),
(18034, 'Langenbruck', 1641, 'BL', 214, 'CH', 47.34917000, 7.76802000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22362386'),
(18035, 'Langendorf', 1662, 'SO', 214, 'CH', 47.21974000, 7.51469000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22362425'),
(18036, 'Langenthal', 1645, 'BE', 214, 'CH', 47.21526000, 7.79607000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22362464'),
(18037, 'Langnau', 1645, 'BE', 214, 'CH', 46.93936000, 7.78738000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22362464'),
(18038, 'Langnau / Langnau (Dorf)', 1656, 'ZH', 214, 'CH', 47.28643000, 8.53627000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22363448'),
(18039, 'Langnau / Vitaquartier', 1656, 'ZH', 214, 'CH', 47.29377000, 8.53758000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22363482'),
(18040, 'Langnau am Albis', 1656, 'ZH', 214, 'CH', 47.28885000, 8.54110000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22363482'),
(18041, 'Langrickenbach', 1657, 'TG', 214, 'CH', 47.59353000, 9.24727000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22363645'),
(18042, 'Laufen', 1641, 'BL', 214, 'CH', 47.42193000, 7.49946000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22372470'),
(18043, 'Laufenburg', 1639, 'AG', 214, 'CH', 47.55985000, 8.06225000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22372470'),
(18044, 'Lauffohr (Brugg)', 1639, 'AG', 214, 'CH', 47.50154000, 8.23122000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q492440'),
(18045, 'Laupen', 1645, 'BE', 214, 'CH', 46.90214000, 7.23973000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22373177'),
(18046, 'Laupersdorf', 1662, 'SO', 214, 'CH', 47.31260000, 7.65465000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q66407'),
(18047, 'Lauperswil', 1645, 'BE', 214, 'CH', 46.96564000, 7.74214000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22373292'),
(18048, 'Lausanne', 1651, 'VD', 214, 'CH', 46.51600000, 6.63282000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22373292'),
(18049, 'Lausanne District', 1651, 'VD', 214, 'CH', 46.52131000, 6.63334000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q660628'),
(18050, 'Lausen', 1641, 'BL', 214, 'CH', 47.47139000, 7.76030000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22374830'),
(18051, 'Lauterbrunnen', 1645, 'BE', 214, 'CH', 46.59307000, 7.90938000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22374830'),
(18052, 'Lavaux-Oron District', 1651, 'VD', 214, 'CH', 46.53618000, 6.77086000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q660985'),
(18053, 'Lavertezzo', 1643, 'TI', 214, 'CH', 46.25893000, 8.83756000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22376106'),
(18054, 'Le Chenit', 1651, 'VD', 214, 'CH', 46.60688000, 6.23062000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q52743'),
(18055, 'Le Grand-Saconnex', 1647, 'GE', 214, 'CH', 46.23188000, 6.12091000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q52743'),
(18056, 'Le Landeron', 1659, 'NE', 214, 'CH', 47.05702000, 7.07052000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22385481'),
(18057, 'Le Locle', 1659, 'NE', 214, 'CH', 47.05619000, 6.74913000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22385481'),
(18058, 'Le Locle District', 1659, 'NE', 214, 'CH', 47.01927000, 6.69635000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q138960'),
(18059, 'Le Mont-sur-Lausanne', 1651, 'VD', 214, 'CH', 46.55815000, 6.63145000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22386151'),
(18060, 'Le Noirmont', 1658, 'JU', 214, 'CH', 47.22464000, 6.95784000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22386493'),
(18061, 'Le Vaud', 1651, 'VD', 214, 'CH', 46.47753000, 6.23603000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22390176'),
(18062, 'Leibstadt', 1639, 'AG', 214, 'CH', 47.58790000, 8.17611000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22401542'),
(18063, 'Lengnau', 1645, 'BE', 214, 'CH', 47.18155000, 7.36814000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22407645'),
(18064, 'Lenk', 1645, 'BE', 214, 'CH', 46.45826000, 7.44298000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22408143'),
(18065, 'Lens', 1648, 'VS', 214, 'CH', 46.28298000, 7.44976000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q70361'),
(18066, 'Lenzburg', 1639, 'AG', 214, 'CH', 47.38853000, 8.17503000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q70361'),
(18067, 'Lenzerheide', 1660, 'GR', 214, 'CH', 46.72215000, 9.55905000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q676017'),
(18068, 'Les Avanchets', 1647, 'GE', 214, 'CH', 46.22168000, 6.10814000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22703138'),
(18069, 'Les Bois', 1658, 'JU', 214, 'CH', 47.17715000, 6.90498000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22412191'),
(18070, 'Les Brenets', 1659, 'NE', 214, 'CH', 47.06774000, 6.70478000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22412297'),
(18071, 'Les Breuleux', 1658, 'JU', 214, 'CH', 47.21099000, 7.00792000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22412337'),
(18072, 'Les Geneveys-sur-Coffrane', 1659, 'NE', 214, 'CH', 47.01528000, 6.85130000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q70448'),
(18073, 'Les Ponts-de-Martel', 1659, 'NE', 214, 'CH', 46.99735000, 6.73059000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22414413'),
(18074, 'Leuk', 1648, 'VS', 214, 'CH', 46.31736000, 7.63412000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22414413'),
(18075, 'Leuk District', 1648, 'VS', 214, 'CH', 46.31439000, 7.67291000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q661326'),
(18076, 'Leukerbad', 1648, 'VS', 214, 'CH', 46.37943000, 7.62687000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22418566'),
(18077, 'Leuzigen', 1645, 'BE', 214, 'CH', 47.17458000, 7.45775000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22418693'),
(18078, 'Leventina District', 1643, 'TI', 214, 'CH', 46.46912000, 8.75539000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q539271'),
(18079, 'Leysin', 1651, 'VD', 214, 'CH', 46.34183000, 7.01151000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q69610'),
(18080, 'Leytron', 1648, 'VS', 214, 'CH', 46.18660000, 7.20780000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q68849'),
(18081, 'Lichtensteig', 1644, 'SG', 214, 'CH', 47.32381000, 9.08758000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22425811'),
(18082, 'Liesberg', 1641, 'BL', 214, 'CH', 47.40398000, 7.42787000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22426676'),
(18083, 'Liestal', 1641, 'BL', 214, 'CH', 47.48455000, 7.73446000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22426676'),
(18084, 'Ligornetto', 1643, 'TI', 214, 'CH', 45.86161000, 8.95166000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q70330'),
(18085, 'Linden', 1645, 'BE', 214, 'CH', 46.84690000, 7.67831000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22543837'),
(18086, 'Linthal', 1661, 'GL', 214, 'CH', 46.92127000, 8.99799000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q661454'),
(18087, 'Littau', 1663, 'LU', 214, 'CH', 47.05000000, 8.26274000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q688652'),
(18088, 'Locarno', 1643, 'TI', 214, 'CH', 46.17086000, 8.79953000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q688652'),
(18089, 'Locarno District', 1643, 'TI', 214, 'CH', 46.18033000, 8.75991000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q660653'),
(18090, 'Lodrino', 1643, 'TI', 214, 'CH', 46.30016000, 8.97986000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22576667'),
(18091, 'Losone', 1643, 'TI', 214, 'CH', 46.16866000, 8.75928000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22659847'),
(18092, 'Lostorf', 1662, 'SO', 214, 'CH', 47.38372000, 7.94655000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22660406'),
(18093, 'Lotzwil', 1645, 'BE', 214, 'CH', 47.19135000, 7.79102000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22660578'),
(18094, 'Lucens', 1651, 'VD', 214, 'CH', 46.70854000, 6.83931000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22595619'),
(18095, 'Lucerne-Land District', 1663, 'LU', 214, 'CH', 47.04046000, 8.29271000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q2540080'),
(18096, 'Lucerne-Stadt District', 1663, 'LU', 214, 'CH', 47.05334000, 8.31063000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q1670385'),
(18097, 'Luchsingen', 1661, 'GL', 214, 'CH', 46.96640000, 9.03715000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q659932'),
(18098, 'Lugano', 1643, 'TI', 214, 'CH', 46.01008000, 8.96004000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q7024'),
(18099, 'Lugano District', 1643, 'TI', 214, 'CH', 46.02322000, 8.93802000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q660612'),
(18100, 'Lumino', 1643, 'TI', 214, 'CH', 46.23020000, 9.06420000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22604346'),
(18101, 'Lungern', 1650, 'OW', 214, 'CH', 46.78578000, 8.15984000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q64292'),
(18102, 'Luterbach', 1662, 'SO', 214, 'CH', 47.21426000, 7.58463000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q66839'),
(18103, 'Luthern', 1663, 'LU', 214, 'CH', 47.05753000, 7.91692000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22610416'),
(18104, 'Lutry', 1651, 'VD', 214, 'CH', 46.50241000, 6.68647000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22610628'),
(18105, 'Luzein', 1660, 'GR', 214, 'CH', 46.91957000, 9.76080000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22610628'),
(18106, 'Luzern', 1663, 'LU', 214, 'CH', 47.05048000, 8.30635000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q4191'),
(18107, 'Lyss', 1645, 'BE', 214, 'CH', 47.07410000, 7.30655000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22614042'),
(18108, 'Lyssach', 1645, 'BE', 214, 'CH', 47.06445000, 7.58228000, '2019-10-05 22:35:55', '2019-10-05 22:35:55', 1, 'Q22614061'),
(18109, 'Läufelfingen', 1641, 'BL', 214, 'CH', 47.39457000, 7.85578000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22614554'),
(18110, 'Löhningen', 1654, 'SH', 214, 'CH', 47.70121000, 8.55236000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22614554'),
(18111, 'Lütisburg', 1644, 'SG', 214, 'CH', 47.39451000, 9.08312000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22614096'),
(18112, 'Lützelflüh', 1645, 'BE', 214, 'CH', 47.00757000, 7.69165000, '2019-10-05 22:35:55', '2020-05-01 17:23:18', 1, 'Q22614392'),
(18113, 'Madiswil', 1645, 'BE', 214, 'CH', 47.16463000, 7.79858000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22361946'),
(18114, 'Magadino', 1643, 'TI', 214, 'CH', 46.14892000, 8.85610000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q661679'),
(18115, 'Magden', 1639, 'AG', 214, 'CH', 47.52868000, 7.81128000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22365375'),
(18116, 'Maienfeld', 1660, 'GR', 214, 'CH', 47.00472000, 9.53115000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22365375'),
(18117, 'Malans', 1660, 'GR', 214, 'CH', 46.98096000, 9.57527000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22365375'),
(18118, 'Malleray', 1645, 'BE', 214, 'CH', 47.23839000, 7.27286000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q67544'),
(18119, 'Malters', 1663, 'LU', 214, 'CH', 47.03628000, 8.18193000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22391884'),
(18120, 'Malvaglia', 1643, 'TI', 214, 'CH', 46.40588000, 8.98190000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q660999'),
(18121, 'Marin-Epagnier', 1659, 'NE', 214, 'CH', 47.01017000, 6.99941000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q685089'),
(18122, 'Marly', 1640, 'FR', 214, 'CH', 46.77611000, 7.16459000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q685089'),
(18123, 'Marsens', 1640, 'FR', 214, 'CH', 46.65644000, 7.05948000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22436523'),
(18124, 'Marthalen', 1656, 'ZH', 214, 'CH', 47.62913000, 8.65326000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22440258'),
(18125, 'Martigny District', 1648, 'VS', 214, 'CH', 46.12386000, 7.10354000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q656949'),
(18126, 'Martigny-Combe', 1648, 'VS', 214, 'CH', 46.07817000, 7.05099000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q68871'),
(18127, 'Martigny-Ville', 1648, 'VS', 214, 'CH', 46.10276000, 7.07245000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q15834158'),
(18128, 'Massagno', 1643, 'TI', 214, 'CH', 46.01257000, 8.94354000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18129, 'Matten', 1645, 'BE', 214, 'CH', 46.67833000, 7.86889000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18130, 'Mattenbach (Kreis 7)', 1656, 'ZH', 214, 'CH', 47.48705000, 8.74681000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18131, 'Mattenbach (Kreis 7) / Deutweg', 1656, 'ZH', 214, 'CH', 47.49419000, 8.73948000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18132, 'Mattenbach (Kreis 7) / Endliker', 1656, 'ZH', 214, 'CH', 47.48821000, 8.74938000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18133, 'Mattenbach (Kreis 7) / Gutschick', 1656, 'ZH', 214, 'CH', 47.49104000, 8.75258000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18134, 'Matzendorf', 1662, 'SO', 214, 'CH', 47.30374000, 7.62820000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18135, 'Matzingen', 1657, 'TG', 214, 'CH', 47.51957000, 8.93365000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18136, 'Mauensee', 1663, 'LU', 214, 'CH', 47.16703000, 8.06793000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18137, 'Mauraz', 1651, 'VD', 214, 'CH', 46.60558000, 6.42074000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q70572'),
(18138, 'Meggen', 1663, 'LU', 214, 'CH', 47.04691000, 8.37467000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, '*********'),
(18139, 'Meierskappel', 1663, 'LU', 214, 'CH', 47.12470000, 8.44274000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q14574'),
(18140, 'Meilen', 1656, 'ZH', 214, 'CH', 47.27232000, 8.64617000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22492308'),
(18141, 'Meinier', 1647, 'GE', 214, 'CH', 46.24706000, 6.23423000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22492549'),
(18142, 'Meinisberg', 1645, 'BE', 214, 'CH', 47.15965000, 7.34801000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22492567'),
(18143, 'Meiringen', 1645, 'BE', 214, 'CH', 46.72709000, 8.18720000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22492567'),
(18144, 'Meisterschwanden', 1639, 'AG', 214, 'CH', 47.29489000, 8.22867000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22492753'),
(18145, 'Melano', 1643, 'TI', 214, 'CH', 45.92202000, 8.98435000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22493195'),
(18146, 'Melchnau', 1645, 'BE', 214, 'CH', 47.18213000, 7.85128000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22493402'),
(18147, 'Melide', 1643, 'TI', 214, 'CH', 45.95455000, 8.94725000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22493402'),
(18148, 'Mellingen', 1639, 'AG', 214, 'CH', 47.41903000, 8.27331000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22493909'),
(18149, 'Mendrisio', 1643, 'TI', 214, 'CH', 45.87019000, 8.98160000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q69041'),
(18150, 'Mendrisio District', 1643, 'TI', 214, 'CH', 45.86592000, 8.99931000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q869646'),
(18151, 'Menziken', 1639, 'AG', 214, 'CH', 47.23965000, 8.18996000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22496025'),
(18152, 'Menzingen', 1646, 'ZG', 214, 'CH', 47.17764000, 8.59215000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22496025'),
(18153, 'Menznau', 1663, 'LU', 214, 'CH', 47.08364000, 8.03971000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22496078'),
(18154, 'Merenschwand', 1639, 'AG', 214, 'CH', 47.25944000, 8.37633000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22496906'),
(18155, 'Mesocco', 1660, 'GR', 214, 'CH', 46.39390000, 9.23333000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22496906'),
(18156, 'Messen', 1662, 'SO', 214, 'CH', 47.09155000, 7.44528000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22498630'),
(18157, 'Mettmenstetten', 1656, 'ZH', 214, 'CH', 47.24529000, 8.46347000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22499750'),
(18158, 'Meyrin', 1647, 'GE', 214, 'CH', 46.23424000, 6.08025000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22500194'),
(18159, 'Minusio', 1643, 'TI', 214, 'CH', 46.17769000, 8.81473000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q22521685'),
(18160, 'Mogelsberg', 1644, 'SG', 214, 'CH', 47.36222000, 9.13541000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q869624'),
(18161, 'Mollens', 1651, 'VD', 214, 'CH', 46.57760000, 6.36320000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q869624'),
(18162, 'Mollis', 1661, 'GL', 214, 'CH', 47.08878000, 9.07242000, '2019-10-05 22:35:56', '2019-10-05 22:35:56', 1, 'Q644055'),
(18163, 'Montagnola', 1643, 'TI', 214, 'CH', 45.98323000, 8.91786000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q694086'),
(18164, 'Montagny', 1651, 'VD', 214, 'CH', 46.79289000, 6.61222000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22364149'),
(18165, 'Montana', 1648, 'VS', 214, 'CH', 46.31338000, 7.48839000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q70186'),
(18166, 'Monte Carasso', 1643, 'TI', 214, 'CH', 46.18649000, 8.99892000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q70075'),
(18167, 'Monthey', 1648, 'VS', 214, 'CH', 46.25451000, 6.95406000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q70075'),
(18168, 'Monthey District', 1648, 'VS', 214, 'CH', 46.27043000, 6.90274000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q660517'),
(18169, 'Montreux', 1651, 'VD', 214, 'CH', 46.43301000, 6.91143000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q69354'),
(18170, 'Morbio Inferiore', 1643, 'TI', 214, 'CH', 45.84915000, 9.01907000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22386770'),
(18171, 'Morges', 1651, 'VD', 214, 'CH', 46.51127000, 6.49854000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22388872'),
(18172, 'Morges District', 1651, 'VD', 214, 'CH', 46.53578000, 6.48662000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q660462'),
(18173, 'Mosnang', 1644, 'SG', 214, 'CH', 47.36252000, 9.04296000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22400547'),
(18174, 'Moudon', 1651, 'VD', 214, 'CH', 46.66758000, 6.79781000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22409836'),
(18175, 'Moutier', 1645, 'BE', 214, 'CH', 47.27818000, 7.36951000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22560112'),
(18176, 'Muhen', 1639, 'AG', 214, 'CH', 47.33578000, 8.05536000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22624864'),
(18177, 'Mumpf', 1639, 'AG', 214, 'CH', 47.54563000, 7.92123000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q22578650'),
(18178, 'Muolen', 1644, 'SG', 214, 'CH', 47.52102000, 9.32484000, '2019-10-05 22:35:57', '2019-10-05 22:35:57', 1, 'Q67044'),
(18179, 'Muotathal', 1653, 'SZ', 214, 'CH', 46.97676000, 8.76499000, '2019-10-05 22:35:58', '2019-10-05 22:35:58', 1, 'Q22629922');

