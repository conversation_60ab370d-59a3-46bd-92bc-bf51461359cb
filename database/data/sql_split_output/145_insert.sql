INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(73008, 'Pánuco de Coronado', 3453, 'D<PERSON>', 142, 'MX', 24.53972000, -104.32707000, '2019-10-05 23:09:28', '2020-05-01 17:23:00', 1, 'Q2913809'),
(73009, 'Páreo', 3474, 'MIC', 142, 'MX', 19.33259000, -102.45219000, '2019-10-05 23:09:28', '2020-05-01 17:23:01', 1, 'Q2913809'),
(73010, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 3474, 'MIC', 142, 'MX', 19.51594000, -101.60887000, '2019-10-05 23:09:28', '2020-05-01 17:23:01', 1, 'Q1021051'),
(73011, '<PERSON><PERSON><PERSON><PERSON>', 3469, '<PERSON>U<PERSON>', 142, 'M<PERSON>', 20.43108000, -101.72261000, '2019-10-05 23:09:28', '2020-05-01 17:23:00', 1, 'Q1962279'),
(73012, '<PERSON><PERSON><PERSON><PERSON>', 3454, 'TAB', 142, 'MX', 18.43444000, -93.09306000, '2019-10-05 23:09:28', '2020-05-01 17:23:02', 1, 'Q20261787'),
(73013, '<PERSON> de Galeana', 3465, 'MOR', 142, 'MX', 19.97933000, -99.14164000, '2019-10-05 23:09:28', '2020-05-01 17:23:01', 1, 'Q20261787'),
(73014, 'Pórticos de San Antonio', 3457, 'BCN', 142, 'MX', 32.44250000, -117.03417000, '2019-10-05 23:09:28', '2020-05-01 17:22:59', 1, 'Q394921'),
(73015, 'Quamila', 3476, 'PUE', 142, 'MX', 20.16632000, -97.91855000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q394921'),
(73016, 'Quebrantadero', 3465, 'MOR', 142, 'MX', 18.52429000, -98.79023000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298033'),
(73017, 'Quecholac', 3476, 'PUE', 142, 'MX', 18.95471000, -97.65858000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20231373'),
(73018, 'Quechulac', 3476, 'PUE', 142, 'MX', 19.37273000, -97.34783000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20231373'),
(73019, 'Quechultenango', 3459, 'GRO', 142, 'MX', 17.41425000, -99.24221000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q3845800'),
(73020, 'Quelové', 3448, 'OAX', 142, 'MX', 15.98273000, -96.67890000, '2019-10-05 23:09:28', '2020-05-01 17:23:01', 1, 'Q3845800'),
(73021, 'Querendas', 3459, 'GRO', 142, 'MX', 18.34250000, -100.63444000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298049'),
(73022, 'Querobabi', 3468, 'SON', 142, 'MX', 30.05062000, -111.02649000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298049'),
(73023, 'Queréndaro', 3474, 'MIC', 142, 'MX', 19.69745000, -100.84379000, '2019-10-05 23:09:28', '2020-05-01 17:23:01', 1, 'Q6094509'),
(73024, 'Querétaro', 3451, 'CHP', 142, 'MX', 15.83752000, -92.75774000, '2019-10-05 23:09:28', '2020-05-01 17:22:59', 1, 'Q6094509'),
(73025, 'Querétaro', 3455, 'QUE', 142, 'MX', 20.72105000, -100.44738000, '2019-10-05 23:09:28', '2020-05-01 17:23:02', 1, 'Q2917451'),
(73026, 'Quesería', 3472, 'COL', 142, 'MX', 19.38708000, -103.57243000, '2019-10-05 23:09:28', '2020-05-01 17:23:00', 1, 'Q20254984'),
(73027, 'Quetchehueca', 3468, 'SON', 142, 'MX', 27.26365000, -109.94920000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q28052148'),
(73028, 'Quetzalapa', 3459, 'GRO', 142, 'MX', 16.78551000, -98.50272000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q28052148'),
(73029, 'Quetzalapa', 3450, 'MEX', 142, 'MX', 18.94056000, -99.59694000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298063'),
(73030, 'Quiamoloapan', 3464, 'VER', 142, 'MX', 18.06180000, -94.99574000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298063'),
(73031, 'Quila', 3449, 'SIN', 142, 'MX', 24.42362000, -107.22186000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298094'),
(73032, 'Quimichis', 3477, 'NAY', 142, 'MX', 22.36122000, -105.53913000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298094'),
(73033, 'Quinceo', 3474, 'MIC', 142, 'MX', 19.59470000, -101.99977000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298094'),
(73034, 'Quinicuena', 3448, 'OAX', 142, 'MX', 16.53101000, -97.52385000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298094'),
(73035, 'Quinta del Cedro', 3457, 'BCN', 142, 'MX', 32.43750000, -117.06583000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20247999'),
(73036, 'Quintana Roo', 3451, 'CHP', 142, 'MX', 16.60667000, -93.56194000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q20298187'),
(73037, 'Quintana Roo', 3466, 'YUC', 142, 'MX', 20.84464000, -88.65081000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q2588077'),
(73038, 'Quintero', 3463, 'TAM', 142, 'MX', 22.66289000, -99.03584000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q2588077'),
(73039, 'Quinto Barrio (Ejido Cahuacán)', 3450, 'MEX', 142, 'MX', 19.62222000, -99.41806000, '2019-10-05 23:09:28', '2020-05-01 17:23:00', 1, 'Q20298189'),
(73040, 'Quintín Arauz', 3454, 'TAB', 142, 'MX', 18.37275000, -93.21879000, '2019-10-05 23:09:28', '2020-05-01 17:23:02', 1, 'Q20298189'),
(73041, 'Quiriego', 3468, 'SON', 142, 'MX', 27.51987000, -109.25233000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q3311475'),
(73042, 'Quiroga', 3474, 'MIC', 142, 'MX', 19.66557000, -101.52287000, '2019-10-05 23:09:28', '2019-10-05 23:09:28', 1, 'Q7272602'),
(73043, 'Rafael Delgado', 3464, 'VER', 142, 'MX', 18.81014000, -97.07172000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q3845824'),
(73044, 'Rafael J. García', 3476, 'PUE', 142, 'MX', 19.25413000, -97.18315000, '2019-10-05 23:09:29', '2020-05-01 17:23:01', 1, 'Q20231387'),
(73045, 'Rafael Lucio', 3464, 'VER', 142, 'MX', 19.59308000, -96.99023000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20271805'),
(73046, 'Rafael Pascacio Gamboa', 3451, 'CHP', 142, 'MX', 17.91818000, -93.26833000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20271805'),
(73047, 'Rafael Ramírez', 3451, 'CHP', 142, 'MX', 16.46199000, -91.94523000, '2019-10-05 23:09:29', '2020-05-01 17:22:59', 1, 'Q20271805'),
(73048, 'Rafael Yáñez Sosa (El Mezquite)', 3462, 'ZAC', 142, 'MX', 23.32833000, -102.61028000, '2019-10-05 23:09:29', '2020-05-01 17:23:03', 1, 'Q20222034'),
(73049, 'Ramos Arizpe', 3471, 'COA', 142, 'MX', 25.53928000, -100.94742000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q1013930'),
(73050, 'Ramírez', 3463, 'TAM', 142, 'MX', 25.95180000, -97.78558000, '2019-10-05 23:09:29', '2020-05-01 17:23:02', 1, 'Q1013930'),
(73051, 'Ramón Corona', 3453, 'DUR', 142, 'MX', 24.18611000, -103.63028000, '2019-10-05 23:09:29', '2020-05-01 17:23:00', 1, 'Q20298225'),
(73052, 'Ramón F. Balboa', 3451, 'CHP', 142, 'MX', 16.47427000, -91.40318000, '2019-10-05 23:09:29', '2020-05-01 17:22:59', 1, 'Q20298225'),
(73053, 'Ranchería de Pocitos', 3458, 'TLA', 142, 'MX', 19.39389000, -97.75944000, '2019-10-05 23:09:29', '2020-05-01 17:23:02', 1, 'Q20224700'),
(73054, 'Ranchito de Castro', 3449, 'SIN', 142, 'MX', 25.58688000, -108.41767000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20224700'),
(73055, 'Rancho Alegre', 3450, 'MEX', 142, 'MX', 19.62528000, -99.68361000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20239740'),
(73056, 'Rancho Alegre', 3471, 'COA', 142, 'MX', 25.50237000, -103.32789000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20239740'),
(73057, 'Rancho Alegre', 3464, 'VER', 142, 'MX', 20.20722000, -97.63889000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20271839'),
(73058, 'Rancho Bellavista [Fraccionamiento]', 3455, 'QUE', 142, 'MX', 20.60667000, -100.44972000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20246709'),
(73059, 'Rancho Chico', 3476, 'PUE', 142, 'MX', 18.74667000, -97.66500000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20231407'),
(73060, 'Rancho Grande', 3448, 'OAX', 142, 'MX', 18.35389000, -96.34944000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20281961'),
(73061, 'Rancho La Gloria', 3457, 'BCN', 142, 'MX', 32.44619000, -117.00103000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20281961'),
(73062, 'Rancho Nuevo', 3454, 'TAB', 142, 'MX', 18.07201000, -93.07252000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20281961'),
(73063, 'Rancho Nuevo', 3462, 'ZAC', 142, 'MX', 22.40783000, -102.34863000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20281961'),
(73064, 'Rancho Nuevo', 3461, 'SLP', 142, 'MX', 22.22216000, -100.92287000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20281961'),
(73065, 'Rancho Nuevo', 3464, 'VER', 142, 'MX', 20.67250000, -97.20611000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20271969'),
(73066, 'Rancho Nuevo', 3476, 'PUE', 142, 'MX', 18.58139000, -97.22472000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20231460'),
(73067, 'Rancho Nuevo de Morelos (De Guadalupe)', 3462, 'ZAC', 142, 'MX', 22.34056000, -101.78528000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20222184'),
(73068, 'Rancho Nuevo de la Cruz', 3469, 'GUA', 142, 'MX', 20.65105000, -101.51786000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20222184'),
(73069, 'Rancho Nuevo de la Luz', 3469, 'GUA', 142, 'MX', 20.96246000, -101.64573000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20222184'),
(73070, 'Rancho Nuevo del Llanito', 3469, 'GUA', 142, 'MX', 20.82361000, -101.36861000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20209794'),
(73071, 'Rancho Verde', 3457, 'BCN', 142, 'MX', 31.91611000, -116.58833000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20219583'),
(73072, 'Rancho Viejo', 3448, 'OAX', 142, 'MX', 16.33966000, -97.96399000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20219583'),
(73073, 'Rancho Viejo', 3464, 'VER', 142, 'MX', 19.44451000, -96.78168000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20219583'),
(73074, 'Rancho Viejo', 3469, 'GUA', 142, 'MX', 21.02091000, -100.77142000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20219583'),
(73075, 'Rancho Viejo', 3474, 'MIC', 142, 'MX', 19.24878000, -100.41737000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20219583'),
(73076, 'Rancho Viejo', 3459, 'GRO', 142, 'MX', 16.86500000, -99.28944000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20284424'),
(73077, 'Rancho Viejo (Rancho Nuevo de la Democracia)', 3459, 'GRO', 142, 'MX', 17.03257000, -98.22389000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20284462'),
(73078, 'Rancho de Guadalupe', 3469, 'GUA', 142, 'MX', 21.21639000, -101.01000000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20209654'),
(73079, 'Rancho de las Lomas', 3459, 'GRO', 142, 'MX', 17.73972000, -99.24583000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20281509'),
(73080, 'Rancho del Padre', 3464, 'VER', 142, 'MX', 19.06553000, -96.17281000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20281509'),
(73081, 'Rascón', 3461, 'SLP', 142, 'MX', 21.97075000, -99.25742000, '2019-10-05 23:09:29', '2020-05-01 17:23:02', 1, 'Q20281509'),
(73082, 'Raudales Malpaso', 3451, 'CHP', 142, 'MX', 17.18833000, -93.60583000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20284596'),
(73083, 'Raya Zaragoza', 3454, 'TAB', 142, 'MX', 17.40595000, -92.68519000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q20284596'),
(73084, 'Raymundo Enríquez', 3451, 'CHP', 142, 'MX', 14.86722000, -92.31466000, '2019-10-05 23:09:29', '2020-05-01 17:22:59', 1, 'Q20284596'),
(73085, 'Rayones', 3452, 'NLE', 142, 'MX', 25.01799000, -100.07362000, '2019-10-05 23:09:29', '2019-10-05 23:09:29', 1, 'Q3844502'),
(73086, 'Rayón', 3461, 'SLP', 142, 'MX', 21.84329000, -99.64258000, '2019-10-05 23:09:29', '2020-05-01 17:23:02', 1, 'Q3849514'),
(73087, 'Rayón', 3451, 'CHP', 142, 'MX', 17.20137000, -93.01110000, '2019-10-05 23:09:29', '2020-05-01 17:22:59', 1, 'Q117485'),
(73088, 'Rayón', 3468, 'SON', 142, 'MX', 29.71195000, -110.56749000, '2019-10-05 23:09:29', '2020-05-01 17:23:02', 1, 'Q3311502'),
(73089, 'Real Hacienda (Metrópolis)', 3474, 'MIC', 142, 'MX', 19.75056000, -101.19694000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20284717'),
(73090, 'Real de Catorce', 3461, 'SLP', 142, 'MX', 23.68996000, -100.88676000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1027749'),
(73091, 'Real del Sol', 3452, 'NLE', 142, 'MX', 25.93833000, -100.18111000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20235103'),
(73092, 'Recoveco', 3449, 'SIN', 142, 'MX', 24.99356000, -107.72796000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20235103'),
(73093, 'Reforma', 3454, 'TAB', 142, 'MX', 18.21398000, -93.34291000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20235103'),
(73094, 'Reforma', 3451, 'CHP', 142, 'MX', 17.86876000, -93.22673000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1953134'),
(73095, 'Reforma de Pineda', 3448, 'OAX', 142, 'MX', 16.40103000, -94.45786000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q617157'),
(73096, 'Reforma y Planada', 3451, 'CHP', 142, 'MX', 17.38694000, -92.86583000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20284841'),
(73097, 'Residencial Arboledas', 3470, 'HID', 142, 'MX', 20.06444000, -99.30167000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20243871'),
(73098, 'Residencial Tajín', 3464, 'VER', 142, 'MX', 20.59806000, -97.41694000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20272128'),
(73099, 'Residencial las Olas', 3464, 'VER', 142, 'MX', 18.11861000, -94.56611000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20272126'),
(73100, 'Revolución Mexicana', 3451, 'CHP', 142, 'MX', 16.17156000, -93.07666000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q20272126'),
(73101, 'Reyes Acozac', 3465, 'MOR', 142, 'MX', 19.77550000, -98.98079000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q5980300'),
(73102, 'Reyes Etla', 3448, 'OAX', 142, 'MX', 17.20220000, -96.81802000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1658623'),
(73103, 'Reyes Mantecón', 3448, 'OAX', 142, 'MX', 16.90717000, -96.72702000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q1658623'),
(73104, 'Reynosa', 3463, 'TAM', 142, 'MX', 26.08061000, -98.28835000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q738303'),
(73105, 'Ricardo Flores Magón', 3463, 'TAM', 142, 'MX', 22.45115000, -97.90739000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20226812'),
(73106, 'Ricardo Flores Magón', 3451, 'CHP', 142, 'MX', 16.39397000, -92.69618000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q20285324'),
(73107, 'Ricardo Flores Magón', 3476, 'PUE', 142, 'MX', 20.19224000, -97.61961000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20285324'),
(73108, 'Ricardo Flores Magón', 3453, 'DUR', 142, 'MX', 24.46250000, -104.53040000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20285324'),
(73109, 'Ricardo Flores Magón', 3464, 'VER', 142, 'MX', 20.51444000, -97.49500000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20272144'),
(73110, 'Ricardo Flores Magón', 3449, 'SIN', 142, 'MX', 25.74056000, -108.99583000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20260488'),
(73111, 'Rinconada', 3464, 'VER', 142, 'MX', 19.35438000, -96.56581000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20129159'),
(73112, 'Rinconada', 3461, 'SLP', 142, 'MX', 22.25332000, -100.94974000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20129159'),
(73113, 'Rinconada de los Ángeles', 3470, 'HID', 142, 'MX', 20.02611000, -98.71139000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20243877'),
(73114, 'Rinconadas de San Francisco', 3470, 'HID', 142, 'MX', 20.06611000, -98.76806000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20243879'),
(73115, 'Rinconadas del Bosque', 3469, 'GUA', 142, 'MX', 20.08528000, -101.19833000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20129167'),
(73116, 'Rinconadas del Venado I', 3470, 'HID', 142, 'MX', 20.06361000, -98.76333000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20243880'),
(73117, 'Rincones de la Hacienda', 3470, 'HID', 142, 'MX', 20.12000000, -98.40167000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20129177'),
(73118, 'Rincón Chamula', 3451, 'CHP', 142, 'MX', 17.20718000, -92.93912000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q20285388'),
(73119, 'Rincón Moreno', 3448, 'OAX', 142, 'MX', 16.23007000, -95.31327000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20285388'),
(73120, 'Rincón Verde', 3450, 'MEX', 142, 'MX', 19.49639000, -99.29083000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20285622'),
(73121, 'Rincón de Aguirre', 3450, 'MEX', 142, 'MX', 18.89947000, -100.13659000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20285407'),
(73122, 'Rincón de Barrabás', 3464, 'VER', 142, 'MX', 19.00590000, -96.53640000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20285407'),
(73123, 'Rincón de Buena Vista', 3464, 'VER', 142, 'MX', 18.75556000, -96.86861000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20272150'),
(73124, 'Rincón de Cano', 3469, 'GUA', 142, 'MX', 21.05519000, -100.24196000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20272150'),
(73125, 'Rincón de Cedeños (Rincón de Dolores)', 3474, 'MIC', 142, 'MX', 19.67806000, -100.57639000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20285424'),
(73126, 'Rincón de Curungueo', 3474, 'MIC', 142, 'MX', 19.48336000, -100.34414000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20285424'),
(73127, 'Rincón de Guadalupe', 3450, 'MEX', 142, 'MX', 19.27404000, -99.99648000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20285424'),
(73128, 'Rincón de Guayabitos', 3477, 'NAY', 142, 'MX', 21.02315000, -105.27054000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20285424'),
(73129, 'Rincón de Jaimes', 3450, 'MEX', 142, 'MX', 18.89844000, -100.16536000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20285424'),
(73130, 'Rincón de López', 3472, 'COL', 142, 'MX', 19.05308000, -103.93241000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20285424'),
(73131, 'Rincón de Nicolás Romero (Cedros Tercera Manzana)', 3474, 'MIC', 142, 'MX', 19.41361000, -100.29750000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20285479'),
(73132, 'Rincón de Parangueo', 3469, 'GUA', 142, 'MX', 20.42193000, -101.25101000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20285479'),
(73133, 'Rincón de Romos', 3456, 'AGU', 142, 'MX', 22.23333000, -102.31666000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q4288377'),
(73134, 'Rincón de Tamayo', 3469, 'GUA', 142, 'MX', 20.42344000, -100.75470000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q7334633'),
(73135, 'Rincón de los Pirules', 3450, 'MEX', 142, 'MX', 19.73932000, -100.03022000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q7334633'),
(73136, 'Rincón de los Reyes', 3476, 'PUE', 142, 'MX', 19.19444000, -97.16056000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20128492'),
(73137, 'Rincón del Centeno', 3469, 'GUA', 142, 'MX', 20.66076000, -100.88532000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20128492'),
(73138, 'Rincón del Porvenir', 3461, 'SLP', 142, 'MX', 22.32306000, -101.17639000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20128857'),
(73139, 'Rio de Medina', 3462, 'ZAC', 142, 'MX', 23.50260000, -103.01791000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20128857'),
(73140, 'Rio de la Soledad', 3470, 'HID', 142, 'MX', 20.11500000, -98.71194000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20129445'),
(73141, 'Rioverde', 3461, 'SLP', 142, 'MX', 21.93115000, -99.99488000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20260759'),
(73142, 'Riva Palacio', 3447, 'CHH', 142, 'MX', 28.81462000, -106.64990000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q2991372'),
(73143, 'Rizo de Oro', 3451, 'CHP', 142, 'MX', 15.97088000, -92.48316000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q2991372'),
(73144, 'Rizos de la Joya (Rizos del Saucillo)', 3469, 'GUA', 142, 'MX', 21.14861000, -101.76194000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20209908'),
(73145, 'Roblada Grande', 3451, 'CHP', 142, 'MX', 16.48909000, -93.19499000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20209908'),
(73146, 'Rodeo', 3453, 'DUR', 142, 'MX', 25.17860000, -104.55950000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q2682355'),
(73147, 'Rodolfo Sánchez Taboada', 3457, 'BCN', 142, 'MX', 31.72099000, -116.57184000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q20219624'),
(73148, 'Rodrigo', 3461, 'SLP', 142, 'MX', 21.89158000, -100.95363000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20219624'),
(73149, 'Rodrígo M. Quevedo', 3447, 'CHH', 142, 'MX', 29.91982000, -107.52079000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q20219624'),
(73150, 'Rodríguez Tejeda', 3464, 'VER', 142, 'MX', 18.63198000, -96.41526000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20219624'),
(73151, 'Rodulfo Figueroa', 3451, 'CHP', 142, 'MX', 15.92006000, -91.90370000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20130556'),
(73152, 'Rojas de Cuauhtémoc', 3448, 'OAX', 142, 'MX', 17.00861000, -96.61944000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q4358462'),
(73153, 'Romerillo', 3451, 'CHP', 142, 'MX', 16.76528000, -92.56750000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20130667'),
(73154, 'Romero de Guzmán', 3474, 'MIC', 142, 'MX', 20.02974000, -102.25876000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20286324'),
(73155, 'Romero de Torres', 3474, 'MIC', 142, 'MX', 20.02435000, -102.25295000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20286324'),
(73156, 'Romita', 3469, 'GUA', 142, 'MX', 20.87127000, -101.51683000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q2651353'),
(73157, 'Roque', 3469, 'GUA', 142, 'MX', 20.58228000, -100.83727000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20209915'),
(73158, 'Rosa Blanca', 3477, 'NAY', 142, 'MX', 21.12454000, -104.35811000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20130770'),
(73159, 'Rosales', 3447, 'CHH', 142, 'MX', 28.18753000, -105.55717000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q7419450'),
(73160, 'Rosales', 3468, 'SON', 142, 'MX', 27.13294000, -109.43843000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q7419450'),
(73161, 'Rosamorada', 3477, 'NAY', 142, 'MX', 22.12208000, -105.20640000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20264059'),
(73162, 'Rosario', 3468, 'SON', 142, 'MX', 27.84111000, -109.36806000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q3844430'),
(73163, 'Rosario', 3447, 'CHH', 142, 'MX', 27.25616000, -106.29878000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q2990914'),
(73164, 'Rosarito', 3457, 'BCN', 142, 'MX', 32.36044000, -117.04645000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1020753'),
(73165, 'Rumorosa', 3457, 'BCN', 142, 'MX', 32.52396000, -116.05397000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q2450936'),
(73166, 'Ruíz', 3477, 'NAY', 142, 'MX', 21.95107000, -105.14380000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q1023929'),
(73167, 'Río Blanco', 3464, 'VER', 142, 'MX', 18.83036000, -97.15600000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q2179073'),
(73168, 'Río Blanco', 3451, 'CHP', 142, 'MX', 16.20501000, -91.63429000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q2179073'),
(73169, 'Río Bravo', 3471, 'COA', 142, 'MX', 28.31254000, -100.91668000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q2179073'),
(73170, 'Río Bravo', 3463, 'TAM', 142, 'MX', 25.83854000, -98.03879000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q13422660'),
(73171, 'Río Chancalá', 3451, 'CHP', 142, 'MX', 17.33454000, -91.68430000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q13422660'),
(73172, 'Río Chiquito', 3448, 'OAX', 142, 'MX', 17.61989000, -95.93688000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q13422660'),
(73173, 'Río Florido', 3462, 'ZAC', 142, 'MX', 23.34464000, -102.99039000, '2019-10-05 23:09:30', '2020-05-01 17:23:03', 1, 'Q13422660'),
(73174, 'Río Frío de Juárez', 3450, 'MEX', 142, 'MX', 19.35336000, -98.67057000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q6115141'),
(73175, 'Río Grande', 3448, 'OAX', 142, 'MX', 16.01079000, -97.43574000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q6115141'),
(73176, 'Río Grande', 3462, 'ZAC', 142, 'MX', 23.82647000, -103.03034000, '2019-10-05 23:09:30', '2020-05-01 17:23:03', 1, 'Q20129713'),
(73177, 'Río Grande', 3474, 'MIC', 142, 'MX', 20.34417000, -102.00556000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20285915'),
(73178, 'Río Jordán', 3451, 'CHP', 142, 'MX', 17.24611000, -91.93306000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q20129763'),
(73179, 'Río Lagartos', 3466, 'YUC', 142, 'MX', 21.59670000, -88.15773000, '2019-10-05 23:09:30', '2020-05-01 17:23:03', 1, 'Q6115313'),
(73180, 'Río Laja', 3469, 'GUA', 142, 'MX', 21.20326000, -100.92339000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q6115313'),
(73181, 'Río Medio [Granja]', 3464, 'VER', 142, 'MX', 19.20972000, -96.20889000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20272179'),
(73182, 'Río Pachiñe', 3448, 'OAX', 142, 'MX', 16.96870000, -95.06533000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q20272179'),
(73183, 'Río Seco 2da. Sección (Santiaguito)', 3454, 'TAB', 142, 'MX', 18.13750000, -93.27583000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20130027'),
(73184, 'Río Seco Puente de Doria', 3470, 'HID', 142, 'MX', 20.21094000, -98.50077000, '2019-10-05 23:09:30', '2020-05-01 17:23:00', 1, 'Q50011618'),
(73185, 'Río Verde', 3452, 'NLE', 142, 'MX', 24.90081000, -99.54345000, '2019-10-05 23:09:30', '2020-05-01 17:23:01', 1, 'Q50011618'),
(73186, 'Río Viejo Primera Sección', 3454, 'TAB', 142, 'MX', 17.93913000, -92.98022000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q50011618'),
(73187, 'Río de Teapa', 3454, 'TAB', 142, 'MX', 17.78342000, -92.90033000, '2019-10-05 23:09:30', '2020-05-01 17:23:02', 1, 'Q20264615'),
(73188, 'Saban', 3467, 'ROO', 142, 'MX', 20.03450000, -88.53891000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20286542'),
(73189, 'Sabanas de Xalostoc', 3464, 'VER', 142, 'MX', 20.22223000, -97.53525000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20286542'),
(73190, 'Sabancuy', 3475, 'CAM', 142, 'MX', 18.97270000, -91.17783000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20247219'),
(73191, 'Sabaneta', 3464, 'VER', 142, 'MX', 18.22325000, -95.08082000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20247219'),
(73192, 'Sabanilla', 3451, 'CHP', 142, 'MX', 17.30308000, -92.58368000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q25479854'),
(73193, 'Sabanillas', 3459, 'GRO', 142, 'MX', 17.00038000, -99.70266000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q25479854'),
(73194, 'Sabidos', 3467, 'ROO', 142, 'MX', 18.35716000, -88.58743000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q25479854'),
(73195, 'Sabinalito', 3451, 'CHP', 142, 'MX', 15.70186000, -91.98636000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q25479854'),
(73196, 'Sabinas', 3471, 'COA', 142, 'MX', 27.85591000, -101.11738000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20275847'),
(73197, 'Sacalum', 3466, 'YUC', 142, 'MX', 20.49749000, -89.59035000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q6116629'),
(73198, 'Saclamantón', 3451, 'CHP', 142, 'MX', 16.78833000, -92.63500000, '2019-10-05 23:09:30', '2020-05-01 17:22:59', 1, 'Q20128536'),
(73199, 'Sacramento', 3471, 'COA', 142, 'MX', 27.00264000, -101.72463000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q4405466'),
(73200, 'Sahcaba', 3466, 'YUC', 142, 'MX', 20.79174000, -89.18138000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20131715'),
(73201, 'Sahuaral de Otero', 3468, 'SON', 142, 'MX', 26.88487000, -109.73175000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20131715'),
(73202, 'Sahuaripa', 3468, 'SON', 142, 'MX', 29.05408000, -109.23399000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q376541'),
(73203, 'Sahuayo', 3474, 'MIC', 142, 'MX', 20.05792000, -102.75119000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q2448165'),
(73204, 'Sahuayo de Morelos', 3474, 'MIC', 142, 'MX', 20.05859000, -102.71575000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20286690'),
(73205, 'Sain Alto', 3462, 'ZAC', 142, 'MX', 23.58086000, -103.24807000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20131804'),
(73206, 'Sain Bajo', 3462, 'ZAC', 142, 'MX', 23.61627000, -103.17447000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20131804'),
(73207, 'Saladero', 3464, 'VER', 142, 'MX', 21.42380000, -97.54381000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20131804'),
(73208, 'Salado', 3449, 'SIN', 142, 'MX', 24.50775000, -107.15872000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q20131804'),
(73209, 'Salamanca', 3469, 'GUA', 142, 'MX', 20.57196000, -101.19154000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1801321'),
(73210, 'Salazar', 3465, 'MOR', 142, 'MX', 19.30767000, -99.38863000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1801321'),
(73211, 'Saldarriaga', 3455, 'QUE', 142, 'MX', 20.62781000, -100.29307000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1801321'),
(73212, 'Salguero', 3474, 'MIC', 142, 'MX', 18.41401000, -100.62583000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1801321'),
(73213, 'Salina Cruz', 3448, 'OAX', 142, 'MX', 16.17535000, -95.19424000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1640261'),
(73214, 'Salinas', 3464, 'VER', 142, 'MX', 18.89022000, -95.94299000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q1640261'),
(73215, 'Salinas Victoria', 3452, 'NLE', 142, 'MX', 25.96329000, -100.29091000, '2019-10-05 23:09:30', '2019-10-05 23:09:30', 1, 'Q3844496'),
(73216, 'Salinas de Hidalgo', 3461, 'SLP', 142, 'MX', 22.62795000, -101.71417000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q1997557'),
(73217, 'Salitral de Carrera', 3461, 'SLP', 142, 'MX', 22.86605000, -102.08305000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20260778'),
(73218, 'Salitrillo', 3450, 'MEX', 142, 'MX', 19.43333000, -99.85000000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20286817'),
(73219, 'Salitrillo', 3470, 'HID', 142, 'MX', 19.82937000, -99.21594000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20286817'),
(73220, 'Salitrillo', 3469, 'GUA', 142, 'MX', 21.03629000, -101.45650000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20286817'),
(73221, 'Saloya 2da. Sección', 3454, 'TAB', 142, 'MX', 18.05306000, -92.93639000, '2019-10-05 23:09:31', '2020-05-01 17:23:02', 1, 'Q20228382'),
(73222, 'Saltabarranca', 3464, 'VER', 142, 'MX', 18.59119000, -95.53226000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20132169'),
(73223, 'Saltillo', 3476, 'PUE', 142, 'MX', 19.29644000, -97.29661000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20132169'),
(73224, 'Saltillo', 3464, 'VER', 142, 'MX', 18.15893000, -94.85325000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20132169'),
(73225, 'Saltillo', 3451, 'CHP', 142, 'MX', 16.39359000, -91.94890000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20132169'),
(73226, 'Saltillo', 3471, 'COA', 142, 'MX', 25.42321000, -101.00530000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q53077'),
(73227, 'Salto de Agua', 3451, 'CHP', 142, 'MX', 17.55435000, -92.33941000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q27768065'),
(73228, 'Salvador Díaz Mirón', 3464, 'VER', 142, 'MX', 19.78580000, -96.87314000, '2019-10-05 23:09:31', '2020-05-01 17:23:02', 1, 'Q27768065'),
(73229, 'Salvador Rosas Magallón', 3457, 'BCN', 142, 'MX', 31.90139000, -116.54778000, '2019-10-05 23:09:31', '2020-05-01 17:22:59', 1, 'Q20132418'),
(73230, 'Salvador Urbina', 3451, 'CHP', 142, 'MX', 15.03535000, -92.20805000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20132418'),
(73231, 'Salvatierra', 3469, 'GUA', 142, 'MX', 20.21322000, -100.88023000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q2447267'),
(73232, 'Samachique', 3447, 'CHH', 142, 'MX', 27.30156000, -107.53897000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q2447267'),
(73233, 'Samahil', 3466, 'YUC', 142, 'MX', 20.86433000, -89.91398000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q2588196'),
(73234, 'Samalayuca', 3447, 'CHH', 142, 'MX', 31.34242000, -106.47981000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q4406274'),
(73235, 'Samarkanda', 3454, 'TAB', 142, 'MX', 18.04216000, -92.91050000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20132554'),
(73236, 'San Acateno', 3476, 'PUE', 142, 'MX', 19.87527000, -97.36686000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q20132554'),
(73237, 'San Agustin de las Juntas', 3448, 'OAX', 142, 'MX', 17.00389000, -96.70806000, '2019-10-05 23:09:31', '2019-10-05 23:09:31', 1, 'Q1195409'),
(73238, 'San Agustín', 3450, 'MEX', 142, 'MX', 18.94224000, -99.93913000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q20287058'),
(73239, 'San Agustín', 3447, 'CHH', 142, 'MX', 31.51674000, -106.25548000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q20287058'),
(73240, 'San Agustín Amatengo', 3448, 'OAX', 142, 'MX', 16.51086000, -96.78919000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q5363812'),
(73241, 'San Agustín Atenango', 3448, 'OAX', 142, 'MX', 17.61150000, -98.01095000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q4967775'),
(73242, 'San Agustín Atzompa', 3476, 'PUE', 142, 'MX', 19.20174000, -98.51384000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q4967775'),
(73243, 'San Agustín Berros', 3450, 'MEX', 142, 'MX', 19.40272000, -100.01048000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q4967775'),
(73244, 'San Agustín Buenavista', 3450, 'MEX', 142, 'MX', 20.04337000, -99.48960000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q4967775'),
(73245, 'San Agustín Chayuco', 3448, 'OAX', 142, 'MX', 16.40234000, -97.80823000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q4967775'),
(73246, 'San Agustín Citlali', 3450, 'MEX', 142, 'MX', 19.48025000, -99.80222000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q4967775'),
(73247, 'San Agustín Etla', 3448, 'OAX', 142, 'MX', 17.18654000, -96.76661000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q1658715'),
(73248, 'San Agustín Huitzizilapan', 3450, 'MEX', 142, 'MX', 19.42000000, -99.46360000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q1658715'),
(73249, 'San Agustín Huixaxtla', 3476, 'PUE', 142, 'MX', 18.91778000, -98.39694000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q20132648'),
(73250, 'San Agustín Loxicha', 3448, 'OAX', 142, 'MX', 16.01687000, -96.61615000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q3888202'),
(73251, 'San Agustín Mextepec', 3450, 'MEX', 142, 'MX', 19.63292000, -99.92654000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q20286976'),
(73252, 'San Agustín Mimbres', 3450, 'MEX', 142, 'MX', 19.45069000, -99.55626000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q20286976'),
(73253, 'San Agustín Oapan', 3459, 'GRO', 142, 'MX', 17.95250000, -99.43806000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q20132684'),
(73254, 'San Agustín Tlaxco', 3476, 'PUE', 142, 'MX', 19.05630000, -97.99158000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q20132684'),
(73255, 'San Agustín Tlaxiaca', 3470, 'HID', 142, 'MX', 20.11510000, -98.88640000, '2019-10-05 23:09:31', '2020-05-01 17:23:00', 1, 'Q9073296'),
(73256, 'San Agustín Yatareni', 3448, 'OAX', 142, 'MX', 17.08635000, -96.67845000, '2019-10-05 23:09:31', '2020-05-01 17:23:01', 1, 'Q1193972'),
(73257, 'San Agustín Zapotlán', 3470, 'HID', 142, 'MX', 19.87550000, -98.71426000, '2019-10-05 23:09:32', '2020-05-01 17:23:00', 1, 'Q1193972'),
(73258, 'San Agustín de las Flores', 3469, 'GUA', 142, 'MX', 21.00727000, -101.43710000, '2019-10-05 23:09:32', '2020-05-01 17:23:00', 1, 'Q1193972'),
(73260, 'San Agustín de las Palmas', 3450, 'MEX', 142, 'MX', 19.32607000, -100.14911000, '2019-10-05 23:09:32', '2020-05-01 17:23:00', 1, 'Q1193972'),
(73261, 'San Agustín del Maíz', 3474, 'MIC', 142, 'MX', 19.89910000, -101.16467000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q1193972'),
(73262, 'San Agustín del Palmar', 3464, 'VER', 142, 'MX', 18.51679000, -96.72085000, '2019-10-05 23:09:32', '2020-05-01 17:23:02', 1, 'Q1193972'),
(73263, 'San Agustín del Pulque', 3474, 'MIC', 142, 'MX', 19.96267000, -101.07327000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q20286963'),
(73264, 'San Alejo', 3450, 'MEX', 142, 'MX', 18.83857000, -99.72922000, '2019-10-05 23:09:32', '2019-10-05 23:09:32', 1, 'Q20286963'),
(73265, 'San Andrés', 3470, 'HID', 142, 'MX', 20.05446000, -99.38008000, '2019-10-05 23:09:32', '2020-05-01 17:23:00', 1, 'Q20286963'),
(73266, 'San Andrés', 3447, 'CHH', 142, 'MX', 28.54726000, -106.50350000, '2019-10-05 23:09:32', '2020-05-01 17:23:00', 1, 'Q1437107'),
(73267, 'San Andrés', 3476, 'PUE', 142, 'MX', 19.79000000, -97.33889000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q20133554'),
(73268, 'San Andrés (San Andrés Chichayotla)', 3470, 'HID', 142, 'MX', 20.92667000, -98.56333000, '2019-10-05 23:09:32', '2020-05-01 17:23:00', 1, 'Q20133340'),
(73269, 'San Andrés Ahuashuatepec', 3458, 'TLA', 142, 'MX', 19.37831000, -98.10654000, '2019-10-05 23:09:32', '2020-05-01 17:23:02', 1, 'Q20133340'),
(73270, 'San Andrés Azumiatla', 3476, 'PUE', 142, 'MX', 18.90417000, -98.25278000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q20133170'),
(73271, 'San Andrés Cacaloapan', 3476, 'PUE', 142, 'MX', 18.58364000, -97.58711000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q20133179'),
(73272, 'San Andrés Calpan', 3476, 'PUE', 142, 'MX', 19.10356000, -98.46188000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q3893123'),
(73273, 'San Andrés Chicahuaxtla', 3448, 'OAX', 142, 'MX', 17.15698000, -97.83703000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q3893123'),
(73274, 'San Andrés Cholula', 3476, 'PUE', 142, 'MX', 19.05144000, -98.29526000, '2019-10-05 23:09:32', '2020-05-01 17:23:01', 1, 'Q6959881'),
(73275, 'San Andrés Coru', 3474, 'MIC', 142, 'MX', 19.46680000, -101.94490000, '2019-10-05 23:09:33', '2020-05-01 17:23:01', 1, 'Q6959881'),
(73276, 'San Andrés Cuexcontitlán', 3465, 'MOR', 142, 'MX', 19.35892000, -99.62151000, '2019-10-05 23:09:33', '2020-05-01 17:23:01', 1, 'Q6959881'),
(73277, 'San Andrés Dinicuiti', 3448, 'OAX', 142, 'MX', 17.68812000, -97.72669000, '2019-10-05 23:09:33', '2020-05-01 17:23:01', 1, 'Q3887673'),
(73278, 'San Andrés Duraznal', 3451, 'CHP', 142, 'MX', 17.15653000, -92.79606000, '2019-10-05 23:09:33', '2020-05-01 17:22:59', 1, 'Q15104792'),
(73279, 'San Andrés Enguaro', 3469, 'GUA', 142, 'MX', 20.19472000, -101.23639000, '2019-10-05 23:09:33', '2020-05-01 17:23:00', 1, 'Q20133225'),
(73280, 'San Andrés Hidalgo', 3448, 'OAX', 142, 'MX', 18.13734000, -96.80604000, '2019-10-05 23:09:33', '2020-05-01 17:23:01', 1, 'Q20133225'),
(73281, 'San Andrés Huaxpaltepec', 3448, 'OAX', 142, 'MX', 16.32952000, -97.91647000, '2019-10-05 23:09:33', '2020-05-01 17:23:01', 1, 'Q20235025'),
(73282, 'San Andrés Huayápam', 3448, 'OAX', 142, 'MX', 17.10264000, -96.66556000, '2019-10-05 23:09:33', '2020-05-01 17:23:01', 1, 'Q1193971'),
(73283, 'San Andrés Hueyacatitla', 3476, 'PUE', 142, 'MX', 19.25600000, -98.53628000, '2019-10-05 23:09:33', '2020-05-01 17:23:01', 1, 'Q1193971'),
(73284, 'San Andrés Ixtlahuaca', 3448, 'OAX', 142, 'MX', 17.07112000, -96.82635000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q1193291'),
(73285, 'San Andrés Metla', 3450, 'MEX', 142, 'MX', 19.21695000, -98.84167000, '2019-10-05 23:09:34', '2020-05-01 17:23:00', 1, 'Q1193291'),
(73286, 'San Andrés Mixquic', 3473, 'CMX', 142, 'MX', 19.22512000, -98.96408000, '2019-10-05 23:09:34', '2024-01-24 12:10:50', 1, 'Q937097'),
(73287, 'San Andrés Nicolás Bravo', 3450, 'MEX', 142, 'MX', 18.76778000, -99.46528000, '2019-10-05 23:09:34', '2020-05-01 17:23:00', 1, 'Q20133256'),
(73288, 'San Andrés Ocotepec', 3450, 'MEX', 142, 'MX', 18.90361000, -100.04667000, '2019-10-05 23:09:34', '2020-05-01 17:23:00', 1, 'Q20287144'),
(73289, 'San Andrés Ocotlán', 3465, 'MOR', 142, 'MX', 19.19297000, -99.58612000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q20240027'),
(73290, 'San Andrés Paxtlán', 3448, 'OAX', 142, 'MX', 16.21621000, -96.50783000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q7413249'),
(73291, 'San Andrés Payuca', 3476, 'PUE', 142, 'MX', 19.52563000, -97.61782000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q7413249'),
(73292, 'San Andrés Playa Encantada (El Podrido)', 3459, 'GRO', 142, 'MX', 16.69222000, -99.63389000, '2019-10-05 23:09:34', '2020-05-01 17:23:00', 1, 'Q20133272'),
(73293, 'San Andrés Sinaxtla', 3448, 'OAX', 142, 'MX', 17.46928000, -97.28298000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q3889141'),
(73294, 'San Andrés Solaga', 3448, 'OAX', 142, 'MX', 17.27254000, -96.23610000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q3889498'),
(73295, 'San Andrés Tenejapan', 3464, 'VER', 142, 'MX', 18.77389000, -97.08583000, '2019-10-05 23:09:34', '2020-05-01 17:23:02', 1, 'Q3846544'),
(73296, 'San Andrés Teotilalpam', 3448, 'OAX', 142, 'MX', 17.95414000, -96.65607000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q7413250'),
(73297, 'San Andrés Tepetitlán', 3450, 'MEX', 142, 'MX', 18.89833000, -99.91972000, '2019-10-05 23:09:34', '2020-05-01 17:23:00', 1, 'Q20287154'),
(73298, 'San Andrés Timilpan', 3450, 'MEX', 142, 'MX', 19.87610000, -99.73375000, '2019-10-05 23:09:34', '2020-05-01 17:23:00', 1, 'Q3300331'),
(73299, 'San Andrés Tlalamac', 3450, 'MEX', 142, 'MX', 18.96694000, -98.80778000, '2019-10-05 23:09:34', '2020-05-01 17:23:00', 1, 'Q20133285'),
(73300, 'San Andrés Tuxtla', 3464, 'VER', 142, 'MX', 18.44870000, -95.21327000, '2019-10-05 23:09:34', '2020-05-01 17:23:02', 1, 'Q2652055'),
(73301, 'San Andrés Yahuitlalpan', 3476, 'PUE', 142, 'MX', 19.67333000, -97.72694000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q20231532'),
(73302, 'San Andrés Yaá', 3448, 'OAX', 142, 'MX', 17.29290000, -96.15384000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q10812390'),
(73303, 'San Andrés Zabache', 3448, 'OAX', 142, 'MX', 16.59917000, -96.85889000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q3890012'),
(73304, 'San Andrés Zautla', 3448, 'OAX', 142, 'MX', 17.18697000, -96.86419000, '2019-10-05 23:09:34', '2020-05-01 17:23:01', 1, 'Q1656545'),
(73305, 'San Andrés Ziróndaro', 3474, 'MIC', 142, 'MX', 19.66943000, -101.63221000, '2019-10-05 23:09:35', '2020-05-01 17:23:01', 1, 'Q20133301'),
(73306, 'San Andrés de la Cal', 3465, 'MOR', 142, 'MX', 18.95694000, -99.11444000, '2019-10-05 23:09:35', '2020-05-01 17:23:01', 1, 'Q4407228'),
(73307, 'San Andrés de las Peras', 3450, 'MEX', 142, 'MX', 19.55653000, -98.79202000, '2019-10-05 23:09:35', '2020-05-01 17:23:00', 1, 'Q4407228'),
(73308, 'San Andrés de los Gama', 3450, 'MEX', 142, 'MX', 19.03766000, -99.97403000, '2019-10-05 23:09:35', '2020-05-01 17:23:00', 1, 'Q4407228'),
(73309, 'San Andrés del Pedregal', 3450, 'MEX', 142, 'MX', 19.58104000, -99.86860000, '2019-10-05 23:09:35', '2020-05-01 17:23:00', 1, 'Q4407228'),
(73310, 'San Angel', 3467, 'ROO', 142, 'MX', 21.23535000, -87.43156000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q4407228'),
(73311, 'San Antonino Castillo Velasco', 3448, 'OAX', 142, 'MX', 16.80900000, -96.69267000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q3889178'),
(73312, 'San Antonino Monte Verde', 3448, 'OAX', 142, 'MX', 17.51869000, -97.72654000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q3890503'),
(73313, 'San Antonino el Alto', 3448, 'OAX', 142, 'MX', 16.81984000, -97.02719000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q7413283'),
(73314, 'San Antonio', 3470, 'HID', 142, 'MX', 20.53204000, -99.71038000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q7413283'),
(73315, 'San Antonio', 3456, 'AGU', 142, 'MX', 22.22823000, -102.25034000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q7413283'),
(73316, 'San Antonio', 3452, 'NLE', 142, 'MX', 25.63868000, -100.05844000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20135492'),
(73317, 'San Antonio', 3474, 'MIC', 142, 'MX', 19.66722000, -101.26806000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20135550'),
(73318, 'San Antonio', 3476, 'PUE', 142, 'MX', 19.75611000, -97.35694000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20231567'),
(73319, 'San Antonio Acahualco', 3450, 'MEX', 142, 'MX', 19.27400000, -99.77169000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20287258'),
(73320, 'San Antonio Alpanocan', 3476, 'PUE', 142, 'MX', 18.87722000, -98.71083000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20133775'),
(73321, 'San Antonio Arrazola', 3448, 'OAX', 142, 'MX', 17.03972000, -96.79034000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20133775'),
(73322, 'San Antonio Atotonilco', 3476, 'PUE', 142, 'MX', 19.37466000, -98.46584000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20133775'),
(73323, 'San Antonio Bonixi', 3450, 'MEX', 142, 'MX', 19.49226000, -99.72686000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20133775'),
(73324, 'San Antonio Buenavista', 3450, 'MEX', 142, 'MX', 19.26083000, -99.71194000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20287322'),
(73325, 'San Antonio Buenavista', 3451, 'CHP', 142, 'MX', 16.15234000, -91.65020000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20287322'),
(73326, 'San Antonio Bulujib', 3451, 'CHP', 142, 'MX', 16.99667000, -92.29667000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20287325'),
(73327, 'San Antonio Calichar', 3469, 'GUA', 142, 'MX', 20.49669000, -100.51775000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20287325'),
(73328, 'San Antonio Cañada', 3476, 'PUE', 142, 'MX', 18.51604000, -97.29412000, '2019-10-05 23:09:35', '2020-05-01 17:23:01', 1, 'Q7413310'),
(73329, 'San Antonio Chiverías', 3465, 'MOR', 142, 'MX', 18.65222000, -99.22139000, '2019-10-05 23:09:35', '2020-05-01 17:23:01', 1, 'Q20264718'),
(73330, 'San Antonio Coyahuacán', 3459, 'GRO', 142, 'MX', 17.86194000, -98.68000000, '2019-10-05 23:09:35', '2020-05-01 17:23:00', 1, 'Q20133984'),
(73331, 'San Antonio Cárdenas', 3475, 'CAM', 142, 'MX', 18.61399000, -92.22341000, '2019-10-05 23:09:35', '2020-05-01 17:22:59', 1, 'Q20133984'),
(73332, 'San Antonio Eménguaro', 3469, 'GUA', 142, 'MX', 20.13997000, -100.87874000, '2019-10-05 23:09:35', '2020-05-01 17:23:00', 1, 'Q20133984'),
(73333, 'San Antonio Enchisi', 3450, 'MEX', 142, 'MX', 19.75472000, -99.81667000, '2019-10-05 23:09:35', '2019-10-05 23:09:35', 1, 'Q20134601'),
(73334, 'San Antonio Gallardo', 3469, 'GUA', 142, 'MX', 20.62833000, -100.76778000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20209997'),
(73335, 'San Antonio Guaracha', 3450, 'MEX', 142, 'MX', 19.94592000, -102.55590000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20209997'),
(73336, 'San Antonio Huitepec', 3448, 'OAX', 142, 'MX', 16.92888000, -97.14670000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20209997'),
(73337, 'San Antonio Juárez', 3476, 'PUE', 142, 'MX', 18.79379000, -97.98964000, '2019-10-05 23:09:36', '2020-05-01 17:23:01', 1, 'Q20231547'),
(73338, 'San Antonio Matlahuacales', 3476, 'PUE', 142, 'MX', 19.75528000, -98.08194000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20231552'),
(73339, 'San Antonio Mihuacán', 3476, 'PUE', 142, 'MX', 19.15333000, -98.30472000, '2019-10-05 23:09:36', '2020-05-01 17:23:01', 1, 'Q20134842'),
(73340, 'San Antonio Molinos', 3474, 'MIC', 142, 'MX', 20.07533000, -100.21501000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134854'),
(73341, 'San Antonio Nixini', 3450, 'MEX', 142, 'MX', 19.67472000, -99.68472000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134872'),
(73342, 'San Antonio Ocopetlatlán', 3476, 'PUE', 142, 'MX', 20.24703000, -97.94621000, '2019-10-05 23:09:36', '2020-05-01 17:23:01', 1, 'Q20134893'),
(73343, 'San Antonio Ocotlán', 3448, 'OAX', 142, 'MX', 16.64929000, -98.16585000, '2019-10-05 23:09:36', '2020-05-01 17:23:01', 1, 'Q20134893'),
(73344, 'San Antonio Portezuelo', 3476, 'PUE', 142, 'MX', 18.99889000, -97.71361000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20231554'),
(73345, 'San Antonio Primero', 3469, 'GUA', 142, 'MX', 21.09417000, -100.52844000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20231554'),
(73346, 'San Antonio Rayón', 3463, 'TAM', 142, 'MX', 22.42079000, -98.41648000, '2019-10-05 23:09:36', '2020-05-01 17:23:02', 1, 'Q20231554'),
(73347, 'San Antonio Sahcabchén', 3475, 'CAM', 142, 'MX', 20.30696000, -90.13778000, '2019-10-05 23:09:36', '2020-05-01 17:22:59', 1, 'Q20231554'),
(73348, 'San Antonio Soledad', 3476, 'PUE', 142, 'MX', 18.78414000, -97.40738000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20231554'),
(73349, 'San Antonio Solís', 3450, 'MEX', 142, 'MX', 19.99965000, -100.08622000, '2019-10-05 23:09:36', '2020-05-01 17:23:00', 1, 'Q20231554'),
(73350, 'San Antonio Tariácuri', 3474, 'MIC', 142, 'MX', 19.88249000, -101.74715000, '2019-10-05 23:09:36', '2020-05-01 17:23:01', 1, 'Q20231554'),
(73351, 'San Antonio Tecolco', 3476, 'PUE', 142, 'MX', 18.85222000, -97.77806000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134996'),
(73352, 'San Antonio Tecómitl', 3473, 'CMX', 142, 'MX', 19.21778000, -98.98806000, '2019-10-05 23:09:36', '2024-01-24 12:10:50', 1, 'Q6118383'),
(73353, 'San Antonio Tedzidz', 3466, 'YUC', 142, 'MX', 20.83560000, -89.97575000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q6118383'),
(73354, 'San Antonio Tepetlapa', 3448, 'OAX', 142, 'MX', 16.54391000, -98.06551000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q3891513'),
(73355, 'San Antonio Texas', 3469, 'GUA', 142, 'MX', 20.98556000, -101.49000000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20287801'),
(73356, 'San Antonio Tlacamilco', 3476, 'PUE', 142, 'MX', 19.06271000, -97.93024000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20287801'),
(73357, 'San Antonio Tlaltecahuacán', 3450, 'MEX', 142, 'MX', 19.15795000, -98.76895000, '2019-10-05 23:09:36', '2020-05-01 17:23:00', 1, 'Q20287801'),
(73358, 'San Antonio Tlatenco', 3476, 'PUE', 142, 'MX', 19.17339000, -98.49996000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20287801'),
(73359, 'San Antonio Villalongín', 3474, 'MIC', 142, 'MX', 19.53456000, -100.75271000, '2019-10-05 23:09:36', '2020-05-01 17:23:01', 1, 'Q20287801'),
(73360, 'San Antonio Virreyes', 3476, 'PUE', 142, 'MX', 19.40989000, -97.63697000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20135100'),
(73361, 'San Antonio Xahuento', 3450, 'MEX', 142, 'MX', 19.69167000, -99.10722000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20135105'),
(73362, 'San Antonio Xoquitla', 3464, 'VER', 142, 'MX', 19.34056000, -97.16500000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20135115'),
(73363, 'San Antonio Zaragoza', 3470, 'HID', 142, 'MX', 20.25497000, -98.99828000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20135115'),
(73364, 'San Antonio de Corrales', 3469, 'GUA', 142, 'MX', 20.74556000, -100.96361000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134058'),
(73365, 'San Antonio de Peñuelas', 3456, 'AGU', 142, 'MX', 21.67471000, -102.30010000, '2019-10-05 23:09:36', '2020-05-01 17:22:59', 1, 'Q20134058'),
(73366, 'San Antonio de Romerillo', 3469, 'GUA', 142, 'MX', 20.60784000, -100.94854000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134058'),
(73367, 'San Antonio de la Cal', 3455, 'QUE', 142, 'MX', 20.76693000, -99.93771000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134058'),
(73368, 'San Antonio de la Cal', 3448, 'OAX', 142, 'MX', 17.02921000, -96.70094000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134058'),
(73369, 'San Antonio de la Laguna', 3450, 'MEX', 142, 'MX', 19.30134000, -100.07179000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134058'),
(73370, 'San Antonio de las Alazanas', 3471, 'COA', 142, 'MX', 25.27166000, -100.57835000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q7413380'),
(73371, 'San Antonio de las Huertas', 3450, 'MEX', 142, 'MX', 19.56222000, -99.97111000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134203'),
(73372, 'San Antonio de las Huertas', 3459, 'GRO', 142, 'MX', 18.28151000, -100.51828000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134203'),
(73373, 'San Antonio de las Palmas', 3450, 'MEX', 142, 'MX', 19.71806000, -98.85684000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134232'),
(73374, 'San Antonio de los Horcones', 3456, 'AGU', 142, 'MX', 21.99832000, -102.32663000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134232'),
(73375, 'San Antonio de los Morales', 3469, 'GUA', 142, 'MX', 20.58055000, -100.91020000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20134232'),
(73376, 'San Antonio de los Ríos', 3456, 'AGU', 142, 'MX', 22.16104000, -102.47118000, '2019-10-05 23:09:36', '2020-05-01 17:22:59', 1, 'Q20134318'),
(73377, 'San Antonio de los Tepetates', 3469, 'GUA', 142, 'MX', 21.04472000, -101.67611000, '2019-10-05 23:09:36', '2019-10-05 23:09:36', 1, 'Q20209988'),
(73378, 'San Antonio del Cipres', 3462, 'ZAC', 142, 'MX', 22.93857000, -102.48710000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20209988'),
(73379, 'San Antonio del Coyote', 3471, 'COA', 142, 'MX', 25.69444000, -103.28556000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20134420'),
(73380, 'San Antonio del Monte', 3451, 'CHP', 142, 'MX', 16.76028000, -92.65306000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20134455'),
(73381, 'San Antonio del Puente', 3465, 'MOR', 142, 'MX', 19.41832000, -99.61734000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20134455'),
(73382, 'San Antonio del Rosario', 3450, 'MEX', 142, 'MX', 18.40012000, -100.30874000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20134455'),
(73383, 'San Antonio el Chico', 3469, 'GUA', 142, 'MX', 20.62798000, -101.28373000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20134455'),
(73384, 'San Antonio el Grande', 3470, 'HID', 142, 'MX', 20.45683000, -98.03484000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q50024808'),
(73385, 'San Antonio el Llanito', 3450, 'MEX', 142, 'MX', 19.27750000, -99.49500000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20240038'),
(73386, 'San Antonio el Paso', 3470, 'HID', 142, 'MX', 20.14722000, -98.61611000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20243891'),
(73387, 'San Antonio el Rico', 3469, 'GUA', 142, 'MX', 20.82308000, -101.37161000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20243891'),
(73388, 'San Antonio la Isla', 3450, 'MEX', 142, 'MX', 19.17317000, -99.55400000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q3844542'),
(73389, 'San Antonio la Labor', 3474, 'MIC', 142, 'MX', 19.02942000, -102.30075000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20134695'),
(73390, 'San Antonio la Portilla', 3476, 'PUE', 142, 'MX', 18.79611000, -97.76278000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20231549'),
(73391, 'San Antonio las Palmas', 3448, 'OAX', 142, 'MX', 17.67167000, -96.09083000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20235040'),
(73392, 'San Atenógenes (La Villita)', 3453, 'DUR', 142, 'MX', 23.99056000, -104.01722000, '2019-10-05 23:09:37', '2020-05-01 17:23:00', 1, 'Q20288118'),
(73393, 'San Baltazar Atlimeyaya', 3476, 'PUE', 142, 'MX', 18.98694000, -98.48111000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20231571'),
(73394, 'San Baltazar Chichicapam', 3448, 'OAX', 142, 'MX', 16.76151000, -96.48970000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q1754432'),
(73395, 'San Baltazar Guelavila', 3448, 'OAX', 142, 'MX', 16.79638000, -96.30542000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q1754432'),
(73396, 'San Baltazar Loxicha', 3448, 'OAX', 142, 'MX', 16.07670000, -96.78740000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q3845609'),
(73397, 'San Baltazar Yatzachi el Bajo', 3448, 'OAX', 142, 'MX', 17.22958000, -96.22046000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q3891761'),
(73398, 'San Bartolito Tlaltelolco', 3450, 'MEX', 142, 'MX', 19.19170000, -99.56136000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q3891761'),
(73399, 'San Bartolo', 3450, 'MEX', 142, 'MX', 19.24496000, -100.05329000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20288164'),
(73400, 'San Bartolo', 3448, 'OAX', 142, 'MX', 18.09222000, -96.10750000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20136049'),
(73401, 'San Bartolo Coyotepec', 3448, 'OAX', 142, 'MX', 16.94877000, -96.70977000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q3891277'),
(73402, 'San Bartolo Coyotespec', 3448, 'OAX', 142, 'MX', 16.95837000, -96.70930000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q3891277'),
(73403, 'San Bartolo Cuautlalpan', 3450, 'MEX', 142, 'MX', 19.81510000, -99.01044000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20135969'),
(73404, 'San Bartolo Cuitareo', 3474, 'MIC', 142, 'MX', 19.65139000, -100.60111000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20288122'),
(73405, 'San Bartolo Lanzados', 3450, 'MEX', 142, 'MX', 19.86639000, -99.89389000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20240060'),
(73406, 'San Bartolo Morelos', 3450, 'MEX', 142, 'MX', 19.78643000, -99.66879000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20240060'),
(73407, 'San Bartolo Oxtotitlán', 3450, 'MEX', 142, 'MX', 19.61902000, -99.61347000, '2019-10-05 23:09:37', '2020-05-01 17:23:00', 1, 'Q20240060'),
(73408, 'San Bartolo Ozocalpan', 3470, 'HID', 142, 'MX', 20.22336000, -99.48906000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20135992'),
(73409, 'San Bartolo Tutotepec', 3470, 'HID', 142, 'MX', 20.39841000, -98.20080000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q27796601'),
(73410, 'San Bartolo Yautepec', 3448, 'OAX', 142, 'MX', 16.42935000, -95.97438000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q27796601'),
(73411, 'San Bartolo de Berrios', 3469, 'GUA', 142, 'MX', 21.61240000, -101.06845000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q27796601'),
(73412, 'San Bartolo del Llano', 3450, 'MEX', 142, 'MX', 19.59069000, -99.74099000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20288136'),
(73413, 'San Bartolo del Llano (San Isidro)', 3450, 'MEX', 142, 'MX', 19.30528000, -99.82750000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20240054'),
(73414, 'San Bartolo del Progreso', 3450, 'MEX', 142, 'MX', 19.10156000, -99.40409000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20240054'),
(73415, 'San Bartolo el Viejo', 3450, 'MEX', 142, 'MX', 19.29889000, -99.82389000, '2019-10-05 23:09:37', '2019-10-05 23:09:37', 1, 'Q20240055'),
(73416, 'San Bartolome Xicomulco', 3473, 'CMX', 142, 'MX', 19.20607000, -99.06820000, '2019-10-05 23:09:37', '2024-01-24 12:10:50', 1, 'Q20240055'),
(73417, 'San Bartolomé', 3476, 'PUE', 142, 'MX', 18.91765000, -97.61912000, '2019-10-05 23:09:37', '2020-05-01 17:23:01', 1, 'Q20240055'),
(73418, 'San Bartolomé', 3450, 'MEX', 142, 'MX', 19.00944000, -99.67856000, '2019-10-05 23:09:37', '2020-05-01 17:23:00', 1, 'Q20240055'),
(73419, 'San Bartolomé Aguas Calientes', 3469, 'GUA', 142, 'MX', 20.49556000, -100.54833000, '2019-10-05 23:09:37', '2020-05-01 17:23:00', 1, 'Q20136079'),
(73420, 'San Bartolomé Atlatlahuca', 3450, 'MEX', 142, 'MX', 19.06963000, -99.60981000, '2019-10-05 23:09:37', '2020-05-01 17:23:00', 1, 'Q20274632'),
(73421, 'San Bartolomé Ayautla', 3448, 'OAX', 142, 'MX', 18.03239000, -96.67078000, '2019-10-05 23:09:37', '2020-05-01 17:23:01', 1, 'Q3891771'),
(73422, 'San Bartolomé Coatepec', 3450, 'MEX', 142, 'MX', 19.39776000, -99.31211000, '2019-10-05 23:09:37', '2020-05-01 17:23:00', 1, 'Q6118459'),
(73423, 'San Bartolomé Cuahuixmatlac', 3458, 'TLA', 142, 'MX', 19.29333000, -98.14806000, '2019-10-05 23:09:37', '2020-05-01 17:23:02', 1, 'Q4407233'),
(73424, 'San Bartolomé Hueyapan', 3476, 'PUE', 142, 'MX', 19.02528000, -97.93056000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20231577'),
(73425, 'San Bartolomé Loxícha', 3448, 'OAX', 142, 'MX', 15.96981000, -96.70957000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q3892488'),
(73426, 'San Bartolomé Quialana', 3448, 'OAX', 142, 'MX', 16.90302000, -96.50169000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q3846986'),
(73427, 'San Bartolomé Tenango', 3458, 'TLA', 142, 'MX', 19.25253000, -98.29172000, '2019-10-05 23:09:38', '2020-05-01 17:23:02', 1, 'Q3846986'),
(73428, 'San Bartolomé Tlaltelulco', 3465, 'MOR', 142, 'MX', 19.22738000, -99.62964000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20288201'),
(73429, 'San Benito', 3474, 'MIC', 142, 'MX', 19.70714000, -102.32170000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20288201'),
(73430, 'San Benito Encinal', 3448, 'OAX', 142, 'MX', 17.98722000, -95.91056000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20235066'),
(73431, 'San Benito Xaltocan', 3458, 'TLA', 142, 'MX', 19.40594000, -98.16683000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20235066'),
(73432, 'San Bernabé Temoxtitla', 3476, 'PUE', 142, 'MX', 18.99722000, -98.33083000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20231578'),
(73433, 'San Bernabé de las Canteras', 3474, 'MIC', 142, 'MX', 19.76639000, -101.16333000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20136185'),
(73434, 'San Bernardino', 3450, 'MEX', 142, 'MX', 19.47667000, -98.89635000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136185'),
(73435, 'San Bernardino Lagunas', 3476, 'PUE', 142, 'MX', 18.60374000, -97.26566000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136185'),
(73436, 'San Bernardino Tlaxcalancingo', 3476, 'PUE', 142, 'MX', 19.02867000, -98.27698000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20232596'),
(73437, 'San Bernardino de Milpillas Chico', 3453, 'DUR', 142, 'MX', 23.38463000, -105.15314000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20232596'),
(73438, 'San Bernardo', 3469, 'GUA', 142, 'MX', 20.96764000, -101.86972000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q7413455'),
(73439, 'San Bernardo', 3468, 'SON', 142, 'MX', 27.39909000, -108.84440000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q26218058'),
(73440, 'San Bernardo', 3453, 'DUR', 142, 'MX', 26.16986000, -105.66251000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q7413467'),
(73441, 'San Bernardo Mixtepec', 3448, 'OAX', 142, 'MX', 16.82579000, -96.89911000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q2449343'),
(73442, 'San Bernardo Peña Blanca', 3469, 'GUA', 142, 'MX', 20.46167000, -101.48250000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q20136219'),
(73443, 'San Bernardo Tlalmimilolpan', 3450, 'MEX', 142, 'MX', 19.56179000, -98.79288000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136219'),
(73444, 'San Blas', 3449, 'SIN', 142, 'MX', 26.08019000, -108.76110000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20288277'),
(73445, 'San Blas', 3477, 'NAY', 142, 'MX', 21.54333000, -105.28558000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q5482273'),
(73446, 'San Blas', 3462, 'ZAC', 142, 'MX', 22.29899000, -101.95497000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q5482273'),
(73447, 'San Blas Atempa', 3448, 'OAX', 142, 'MX', 16.33103000, -95.22559000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3893802'),
(73448, 'San Buenaventura', 3470, 'HID', 142, 'MX', 19.82083000, -99.32209000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3893802'),
(73449, 'San Buenaventura', 3476, 'PUE', 142, 'MX', 18.90204000, -97.92616000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3893802'),
(73450, 'San Buenaventura', 3471, 'COA', 142, 'MX', 27.06193000, -101.54891000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q7413489'),
(73451, 'San Buenaventura', 3447, 'CHH', 142, 'MX', 29.84320000, -107.46067000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q584751'),
(73452, 'San Buenaventura', 3450, 'MEX', 142, 'MX', 19.30250000, -98.86306000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136336'),
(73453, 'San Buenaventura Atempan', 3458, 'TLA', 142, 'MX', 19.32667000, -98.22194000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20264492'),
(73454, 'San Buenaventura Tecaltzingo', 3476, 'PUE', 142, 'MX', 19.24778000, -98.46056000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20231581'),
(73455, 'San Caralampio', 3451, 'CHP', 142, 'MX', 15.82935000, -92.03555000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20231581'),
(73456, 'San Carlos', 3454, 'TAB', 142, 'MX', 17.55221000, -91.15408000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136621'),
(73457, 'San Carlos', 3476, 'PUE', 142, 'MX', 19.43882000, -97.69035000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136621'),
(73458, 'San Carlos', 3463, 'TAM', 142, 'MX', 24.58223000, -98.94208000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q27798247'),
(73459, 'San Carlos', 3471, 'COA', 142, 'MX', 29.02844000, -100.90217000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q27798247'),
(73460, 'San Carlos', 3447, 'CHH', 142, 'MX', 29.10665000, -103.90722000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q1953046'),
(73461, 'San Carlos', 3468, 'SON', 142, 'MX', 27.95749000, -111.04354000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q7413529'),
(73462, 'San Carlos Autopan', 3450, 'MEX', 142, 'MX', 19.38583000, -99.68722000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136402'),
(73463, 'San Carlos Yautepec', 3448, 'OAX', 142, 'MX', 16.49699000, -96.10648000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3846926'),
(73464, 'San Cayetano', 3477, 'NAY', 142, 'MX', 21.44949000, -104.81702000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3846926'),
(73465, 'San Cayetano', 3469, 'GUA', 142, 'MX', 20.60357000, -100.82021000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3846926'),
(73466, 'San Cayetano', 3451, 'CHP', 142, 'MX', 16.96119000, -92.76016000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20288480'),
(73467, 'San Ciro de Acosta', 3461, 'SLP', 142, 'MX', 21.65072000, -99.81916000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3846966'),
(73468, 'San Clemente', 3455, 'QUE', 142, 'MX', 20.51509000, -100.08459000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3846966'),
(73469, 'San Clemente de Lima', 3477, 'NAY', 142, 'MX', 20.73722000, -105.27083000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20234709'),
(73470, 'San Cosme Atlamaxac', 3458, 'TLA', 142, 'MX', 19.23840000, -98.22513000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20234709'),
(73471, 'San Cristóbal', 3459, 'GRO', 142, 'MX', 16.82221000, -98.40283000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q20234709'),
(73472, 'San Cristóbal', 3469, 'GUA', 142, 'MX', 20.65787000, -101.47683000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q20288570'),
(73473, 'San Cristóbal', 3462, 'ZAC', 142, 'MX', 22.59085000, -102.23390000, '2019-10-05 23:09:38', '2020-05-01 17:23:03', 1, 'Q20288570'),
(73474, 'San Cristóbal', 3464, 'VER', 142, 'MX', 18.82979000, -97.12392000, '2019-10-05 23:09:38', '2020-05-01 17:23:02', 1, 'Q20288570'),
(73475, 'San Cristóbal Amatlán', 3448, 'OAX', 142, 'MX', 16.31713000, -96.40785000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q12354858'),
(73476, 'San Cristóbal Amoltepec', 3448, 'OAX', 142, 'MX', 17.28428000, -97.57216000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q7413558'),
(73477, 'San Cristóbal Chacón', 3470, 'HID', 142, 'MX', 20.08028000, -98.73278000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q20136826'),
(73478, 'San Cristóbal Hidalgo', 3476, 'PUE', 142, 'MX', 19.02194000, -97.81028000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20136848'),
(73479, 'San Cristóbal Honduras', 3448, 'OAX', 142, 'MX', 16.32528000, -97.04111000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20288549'),
(73480, 'San Cristóbal Lachirioag', 3448, 'OAX', 142, 'MX', 17.33582000, -96.16503000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q7413560'),
(73481, 'San Cristóbal Nexquipayac', 3450, 'MEX', 142, 'MX', 19.58010000, -98.93053000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q7413560'),
(73482, 'San Cristóbal Xochimilpa', 3476, 'PUE', 142, 'MX', 20.06639000, -97.90750000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20136873'),
(73483, 'San Cristóbal de las Casas', 3451, 'CHP', 142, 'MX', 16.73176000, -92.64126000, '2019-10-05 23:09:38', '2020-05-01 17:22:59', 1, 'Q524894'),
(73484, 'San Cristóbal los Nava', 3476, 'PUE', 142, 'MX', 18.99528000, -97.83194000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20136860'),
(73485, 'San Damián Texoloc', 3458, 'TLA', 142, 'MX', 19.27833000, -98.28544000, '2019-10-05 23:09:38', '2020-05-01 17:23:02', 1, 'Q20136860'),
(73486, 'San Diego', 3461, 'SLP', 142, 'MX', 21.91692000, -100.10100000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136860'),
(73487, 'San Diego', 3465, 'MOR', 142, 'MX', 19.00833000, -99.65434000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136860'),
(73488, 'San Diego', 3449, 'SIN', 142, 'MX', 24.38250000, -107.33135000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136860'),
(73489, 'San Diego', 3469, 'GUA', 142, 'MX', 20.88838000, -101.40973000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136860'),
(73490, 'San Diego', 3476, 'PUE', 142, 'MX', 19.85667000, -97.36111000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20137092'),
(73491, 'San Diego', 3450, 'MEX', 142, 'MX', 18.97291000, -99.58530000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20137092'),
(73492, 'San Diego Alcalá', 3465, 'MOR', 142, 'MX', 19.45121000, -99.62115000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q20137092'),
(73493, 'San Diego Huehuecalco', 3450, 'MEX', 142, 'MX', 19.09276000, -98.76336000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20288618'),
(73494, 'San Diego Linares', 3465, 'MOR', 142, 'MX', 19.38628000, -99.63748000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20288618'),
(73495, 'San Diego de Alcalá', 3469, 'GUA', 142, 'MX', 20.03062000, -100.86350000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q20288618'),
(73496, 'San Diego de la Unión', 3469, 'GUA', 142, 'MX', 21.46749000, -100.87310000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q2651955'),
(73497, 'San Diego del Cerrito', 3450, 'MEX', 142, 'MX', 19.50795000, -100.00684000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q2651955'),
(73498, 'San Diego el Organal', 3476, 'PUE', 142, 'MX', 18.73083000, -98.51667000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136982'),
(73499, 'San Diego la Huerta', 3450, 'MEX', 142, 'MX', 19.14472000, -99.63333000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20136989'),
(73500, 'San Diego la Mesa Tochimiltzingo', 3476, 'PUE', 142, 'MX', 18.79205000, -98.31631000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q4407249'),
(73501, 'San Diego los Padres Cuexcontitlán Sección 5 B', 3450, 'MEX', 142, 'MX', 19.37611000, -99.61222000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q20288622'),
(73502, 'San Dionisio Ocotepec', 3448, 'OAX', 142, 'MX', 16.80457000, -96.39334000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q2066177'),
(73503, 'San Dionisio Ocotlán', 3448, 'OAX', 142, 'MX', 16.74704000, -96.68002000, '2019-10-05 23:09:38', '2020-05-01 17:23:01', 1, 'Q7413781'),
(73504, 'San Dionisio del Mar', 3448, 'OAX', 142, 'MX', 16.32371000, -94.75830000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q558469'),
(73505, 'San Elías', 3469, 'GUA', 142, 'MX', 20.63529000, -100.84120000, '2019-10-05 23:09:38', '2020-05-01 17:23:00', 1, 'Q558469'),
(73506, 'San Esteban', 3470, 'HID', 142, 'MX', 20.54452000, -98.06004000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q20243903'),
(73507, 'San Esteban Atatlahuca', 3448, 'OAX', 142, 'MX', 17.06716000, -97.67836000, '2019-10-05 23:09:38', '2019-10-05 23:09:38', 1, 'Q3893346'),
(73508, 'San Esteban Tizatlán', 3458, 'TLA', 142, 'MX', 19.33911000, -98.21374000, '2019-10-05 23:09:38', '2020-05-01 17:23:02', 1, 'Q3893346');

