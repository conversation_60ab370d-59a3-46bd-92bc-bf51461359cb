INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(90527, 'B<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 4731, 'NT', 181, 'RO', 47.11667000, 26.30000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q15957670'),
(90528, 'Bălțești', 4729, 'PH', 181, 'RO', 45.10965000, 26.12937000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q278686'),
(90529, '<PERSON><PERSON><PERSON><PERSON>', 4728, 'TR', 181, 'RO', 43.93975000, 24.94997000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q278686'),
(90530, '<PERSON><PERSON><PERSON><PERSON>', 4747, '<PERSON><PERSON>', 181, '<PERSON><PERSON>', 45.93333000, 27.93333000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q2289234'),
(90531, '<PERSON><PERSON>neasa', 4726, 'GR', 181, 'RO', 44.04611000, 26.06417000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q2717304'),
(90532, 'Băneasa', 4737, 'CT', 181, 'RO', 44.06667000, 27.70000000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q2717304'),
(90533, 'Băneşti', 4729, 'PH', 181, 'RO', 45.10000000, 25.76667000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q1855079'),
(90534, 'Bănești', 4720, 'SV', 181, 'RO', 47.58946000, 26.52335000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081897'),
(90535, 'Bănia', 4753, 'CS', 181, 'RO', 44.89583000, 22.04472000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q853209'),
(90536, 'Bănişor', 4741, 'SJ', 181, 'RO', 47.10431000, 22.83731000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q1189860'),
(90537, 'Băniţa', 4721, 'HD', 181, 'RO', 45.45000000, 23.26667000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12081936'),
(90538, 'Bărbuleţu', 4745, 'DB', 181, 'RO', 45.13333000, 25.30000000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12081963'),
(90539, 'Bărbulești', 4743, 'IL', 181, 'RO', 44.72629000, 26.59929000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q3791441'),
(90540, 'Bărbăteşti', 4757, 'VL', 181, 'RO', 45.18333000, 24.11667000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q1840232'),
(90541, 'Bărbăteşti', 4750, 'GJ', 181, 'RO', 44.86667000, 23.50000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2717747'),
(90542, 'Bărcăneşti', 4743, 'IL', 181, 'RO', 44.63333000, 26.65000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2541446'),
(90543, 'Bărcăneşti', 4731, 'NT', 181, 'RO', 46.71667000, 26.58333000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12082302'),
(90544, 'Bărcăneşti', 4729, 'PH', 181, 'RO', 44.88333000, 26.05000000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12082303'),
(90545, 'Bărăbanț', 4724, 'AB', 181, 'RO', 46.09930000, 23.58487000, '2019-10-05 23:14:43', '2020-05-01 17:23:07', 1, 'Q12082303'),
(90546, 'Bărăganu', 4737, 'CT', 181, 'RO', 44.08939000, 28.41980000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q855750'),
(90547, 'Bărăganul', 4736, 'BR', 181, 'RO', 44.80000000, 27.51667000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q1155339'),
(90548, 'Bărăști', 4722, 'AG', 181, 'RO', 45.25369000, 24.59651000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q12082271'),
(90549, 'Bărăștii de Vede', 4738, 'OT', 181, 'RO', 44.71601000, 24.65919000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q12082281'),
(90550, 'Băseşti', 4760, 'MM', 181, 'RO', 47.48333000, 23.15000000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q1188987'),
(90551, 'Bătarci', 4746, 'SM', 181, 'RO', 48.03333000, 23.16667000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q791291'),
(90552, 'Bătești', 4729, 'PH', 181, 'RO', 44.84830000, 26.04031000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q12082441'),
(90553, 'Bătrâna', 4721, 'HD', 181, 'RO', 45.78333000, 22.58333000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q5064626'),
(90554, 'Bătrâni', 4729, 'PH', 181, 'RO', 45.31425000, 26.15091000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q12082462'),
(90555, 'Băuţar', 4753, 'CS', 181, 'RO', 45.51667000, 22.56667000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q75522'),
(90556, 'Băţanii Mari', 4754, 'CV', 181, 'RO', 46.08333000, 25.68333000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q10623333'),
(90557, 'C.A. Rosetti', 4727, 'TL', 181, 'RO', 45.30000000, 29.56667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q856004'),
(90558, 'C.a. Rosetti', 4756, 'BZ', 181, 'RO', 45.04622000, 27.16640000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q13542193'),
(90559, 'Cacica', 4720, 'SV', 181, 'RO', 47.63333000, 25.90000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q15634267'),
(90560, 'Cadea', 4723, 'BH', 181, 'RO', 47.31185000, 22.06225000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q820169'),
(90561, 'Cajvana', 4720, 'SV', 181, 'RO', 47.70000000, 25.96667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q16898554'),
(90562, 'Calafat', 4742, 'DJ', 181, 'RO', 43.99069000, 22.93328000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q16898554'),
(90563, 'Calafindeşti', 4720, 'SV', 181, 'RO', 47.85000000, 26.11667000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q2606142'),
(90564, 'Calomfirești', 4728, 'TR', 181, 'RO', 43.91736000, 25.35801000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q12107842'),
(90565, 'Calopăr', 4742, 'DJ', 181, 'RO', 44.16502000, 23.76060000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q777129'),
(90566, 'Calvini', 4756, 'BZ', 181, 'RO', 45.25000000, 26.30000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12107760'),
(90567, 'Camăr', 4741, 'SJ', 181, 'RO', 47.30000000, 22.61667000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q13581071'),
(90568, 'Caporal Alexa', 4739, 'AR', 181, 'RO', 46.33793000, 21.58316000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q13581071'),
(90569, 'Capu Codrului', 4720, 'SV', 181, 'RO', 47.53117000, 25.98724000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12108410'),
(90570, 'Capu Câmpului', 4720, 'SV', 181, 'RO', 47.50822000, 25.97959000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q2720661'),
(90571, 'Capu Piscului', 4722, 'AG', 181, 'RO', 45.17999000, 24.97679000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12108411'),
(90572, 'Caracal', 4738, 'OT', 181, 'RO', 44.11667000, 24.35000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12108411'),
(90573, 'Caraclău', 4744, 'BC', 181, 'RO', 46.30696000, 26.71641000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q12108525'),
(90574, 'Carani', 4748, 'TM', 181, 'RO', 45.91095000, 21.15686000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q730776'),
(90575, 'Caransebeş', 4753, 'CS', 181, 'RO', 45.41667000, 22.21667000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q730776'),
(90576, 'Carastelec', 4741, 'SJ', 181, 'RO', 47.30000000, 22.70000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1188800'),
(90577, 'Caraula', 4742, 'DJ', 181, 'RO', 44.18333000, 23.25000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2468475'),
(90578, 'Caraşova', 4753, 'CS', 181, 'RO', 45.19861000, 21.87000000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q1091761'),
(90579, 'Carcaliu', 4727, 'TL', 181, 'RO', 45.18333000, 28.15000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q858396'),
(90580, 'Carei', 4746, 'SM', 181, 'RO', 47.68333000, 22.46667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q858396'),
(90581, 'Carpen', 4742, 'DJ', 181, 'RO', 44.33333000, 23.25000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2468571'),
(90582, 'Cartojani', 4726, 'GR', 181, 'RO', 44.43221000, 25.48994000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12108898'),
(90583, 'Casimcea', 4727, 'TL', 181, 'RO', 44.73333000, 28.36667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q858861'),
(90584, 'Castelu', 4737, 'CT', 181, 'RO', 44.25000000, 28.33333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q858861'),
(90585, 'Castranova', 4742, 'DJ', 181, 'RO', 44.11667000, 24.01667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2469712'),
(90586, 'Castrele Traiane', 4742, 'DJ', 181, 'RO', 44.22865000, 23.13921000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12108993'),
(90587, 'Catalina', 4754, 'CV', 181, 'RO', 45.96667000, 26.15000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1077241'),
(90588, 'Cataloi', 4727, 'TL', 181, 'RO', 45.09581000, 28.72856000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q3623793'),
(90589, 'Catane', 4742, 'DJ', 181, 'RO', 43.92671000, 23.41178000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2606847'),
(90590, 'Catanele', 4738, 'OT', 181, 'RO', 44.38199000, 24.56145000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12109047'),
(90591, 'Cavadineşti', 4747, 'GL', 181, 'RO', 46.06667000, 28.01694000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q2289293'),
(90592, 'Cavnic', 4760, 'MM', 181, 'RO', 47.66667000, 23.86667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2289293'),
(90593, 'Cazaci', 4745, 'DB', 181, 'RO', 44.80152000, 25.55405000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12107646'),
(90594, 'Cazasu', 4736, 'BR', 181, 'RO', 45.27172000, 27.89011000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2284197'),
(90595, 'Caşin', 4744, 'BC', 181, 'RO', 46.20000000, 26.75000000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q15633485'),
(90596, 'Caţa', 4759, 'BV', 181, 'RO', 46.08333000, 25.26667000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q15241280'),
(90597, 'Cașoca', 4756, 'BZ', 181, 'RO', 45.48914000, 26.26706000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q12109225'),
(90598, 'Ceacu', 4732, 'CL', 181, 'RO', 44.26530000, 27.25213000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169089'),
(90599, 'Ceahlău', 4731, 'NT', 181, 'RO', 47.05000000, 25.96667000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q870359'),
(90600, 'Ceamurlia de Jos', 4727, 'TL', 181, 'RO', 44.73333000, 28.71667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q859186'),
(90601, 'Ceamurlia de Sus', 4727, 'TL', 181, 'RO', 44.76373000, 28.60881000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q3656158'),
(90602, 'Ceanu Mare', 4734, 'CJ', 181, 'RO', 46.66667000, 23.95000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q663597'),
(90603, 'Ceardac', 4758, 'VN', 181, 'RO', 45.66967000, 27.16611000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169151'),
(90604, 'Ceatalchioi', 4727, 'TL', 181, 'RO', 45.28333000, 28.78333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q859191'),
(90605, 'Ceauru', 4750, 'GJ', 181, 'RO', 45.01175000, 23.20211000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169211'),
(90606, 'Cefa', 4723, 'BH', 181, 'RO', 46.91667000, 21.73333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1093228'),
(90607, 'Cegani', 4743, 'IL', 181, 'RO', 44.46279000, 27.90175000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169252'),
(90608, 'Cehal', 4746, 'SM', 181, 'RO', 47.38333000, 22.60000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1188260'),
(90609, 'Cehei', 4741, 'SJ', 181, 'RO', 47.25632000, 22.77507000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1188260'),
(90610, 'Cehu Silvaniei', 4741, 'SJ', 181, 'RO', 47.41177000, 23.17489000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q16898510'),
(90611, 'Ceica', 4723, 'BH', 181, 'RO', 46.85000000, 22.18333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1189427'),
(90612, 'Celaru', 4742, 'DJ', 181, 'RO', 44.05000000, 24.13333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2336803'),
(90613, 'Cenad', 4748, 'TM', 181, 'RO', 46.13333000, 20.58333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q14776866'),
(90614, 'Cenade', 4724, 'AB', 181, 'RO', 46.05000000, 24.01667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1195548'),
(90615, 'Cenei', 4748, 'TM', 181, 'RO', 45.71583000, 20.90389000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q14813623'),
(90616, 'Cepari', 4738, 'OT', 181, 'RO', 44.49733000, 24.17122000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169531'),
(90617, 'Ceparii Pământeni', 4722, 'AG', 181, 'RO', 45.21667000, 24.56667000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q12169533'),
(90618, 'Cepleniţa', 4735, 'IS', 181, 'RO', 47.38333000, 27.01667000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q2607500'),
(90619, 'Ceptura de Jos', 4729, 'PH', 181, 'RO', 45.02088000, 26.32722000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169547'),
(90620, 'Ceptura de Sus', 4729, 'PH', 181, 'RO', 45.01667000, 26.31667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169550'),
(90621, 'Ceraşu', 4729, 'PH', 181, 'RO', 45.31667000, 26.03333000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q12169566'),
(90622, 'Cerbăl', 4721, 'HD', 181, 'RO', 45.78333000, 22.71667000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q5053353'),
(90623, 'Cerchezu', 4737, 'CT', 181, 'RO', 43.81667000, 28.10000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q859620'),
(90624, 'Cerdac', 4744, 'BC', 181, 'RO', 46.23794000, 26.51638000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169683'),
(90625, 'Cergău Mare', 4724, 'AB', 181, 'RO', 46.09869000, 23.92284000, '2019-10-05 23:14:44', '2020-05-01 17:23:07', 1, 'Q741543'),
(90626, 'Cergău Mic', 4724, 'AB', 181, 'RO', 46.08333000, 23.93333000, '2019-10-05 23:14:44', '2020-05-01 17:23:07', 1, 'Q718007'),
(90627, 'Cermei', 4739, 'AR', 181, 'RO', 46.55000000, 21.85000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q872231'),
(90628, 'Cerna', 4727, 'TL', 181, 'RO', 45.08333000, 28.31667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169866'),
(90629, 'Cernat', 4754, 'CV', 181, 'RO', 45.95000000, 26.03333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q13583705'),
(90630, 'Cernavodă', 4737, 'CT', 181, 'RO', 44.33957000, 28.03273000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q13583705'),
(90631, 'Cernele', 4742, 'DJ', 181, 'RO', 44.33363000, 23.74140000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169876'),
(90632, 'Cernetu', 4728, 'TR', 181, 'RO', 43.90727000, 25.45688000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169893'),
(90633, 'Cerneşti', 4760, 'MM', 181, 'RO', 47.51667000, 23.75000000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q872282'),
(90634, 'Cerneți', 4751, 'MH', 181, 'RO', 44.63476000, 22.70931000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q12169894'),
(90635, 'Cernica', 4725, 'IF', 181, 'RO', 44.41667000, 26.28333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2349589'),
(90636, 'Cernişoara', 4757, 'VL', 181, 'RO', 45.03333000, 23.98333000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q2627501'),
(90637, 'Cernu', 4744, 'BC', 181, 'RO', 46.46999000, 26.58477000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12169976'),
(90638, 'Cernăteşti', 4756, 'BZ', 181, 'RO', 45.26667000, 26.76667000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q4685349'),
(90639, 'Cernăteşti', 4742, 'DJ', 181, 'RO', 44.45000000, 23.43333000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q768104'),
(90640, 'Certeju de Sus', 4721, 'HD', 181, 'RO', 45.96667000, 22.96667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q5065771'),
(90641, 'Certeze', 4746, 'SM', 181, 'RO', 47.90000000, 23.46667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q782833'),
(90642, 'Ceru-Băcăinţi', 4724, 'AB', 181, 'RO', 45.98333000, 23.25000000, '2019-10-05 23:14:44', '2020-05-01 17:23:07', 1, 'Q937980'),
(90643, 'Cervenia', 4728, 'TR', 181, 'RO', 43.83333000, 25.46667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2720117'),
(90644, 'Cerăt', 4742, 'DJ', 181, 'RO', 44.06667000, 23.66667000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q2193040'),
(90645, 'Cerţeşti', 4747, 'GL', 181, 'RO', 46.01667000, 27.61667000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q2289266'),
(90646, 'Cetariu', 4723, 'BH', 181, 'RO', 47.13333000, 22.01667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q1188611'),
(90647, 'Cetate', 4742, 'DJ', 181, 'RO', 44.10000000, 23.05000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2468502'),
(90648, 'Cetatea', 4726, 'GR', 181, 'RO', 43.95105000, 25.93450000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12170133'),
(90649, 'Cetatea de Baltă', 4724, 'AB', 181, 'RO', 46.24794000, 24.17244000, '2019-10-05 23:14:44', '2020-05-01 17:23:07', 1, 'Q935602'),
(90650, 'Cetăţeni', 4722, 'AG', 181, 'RO', 45.20000000, 25.18333000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q2534279'),
(90651, 'Cezieni', 4738, 'OT', 181, 'RO', 44.18333000, 24.26667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2719266'),
(90652, 'Checea', 4748, 'TM', 181, 'RO', 45.75298000, 20.83596000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12346145'),
(90653, 'Cheia', 4757, 'VL', 181, 'RO', 45.18306000, 24.21666000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12346145'),
(90654, 'Chelința', 4760, 'MM', 181, 'RO', 47.45311000, 23.31799000, '2019-10-05 23:14:44', '2020-05-01 17:23:10', 1, 'Q739966'),
(90655, 'Chendrea', 4741, 'SJ', 181, 'RO', 47.12236000, 23.29904000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q280636'),
(90656, 'Cherechiu', 4723, 'BH', 181, 'RO', 47.38333000, 22.13333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2351082'),
(90657, 'Chereluș', 4739, 'AR', 181, 'RO', 46.46860000, 21.70706000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q747171'),
(90658, 'Chesinț', 4739, 'AR', 181, 'RO', 46.04973000, 21.57944000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q753410'),
(90659, 'Chevereşu Mare', 4748, 'TM', 181, 'RO', 45.66694000, 21.49083000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q14757795'),
(90660, 'Cheșereu', 4723, 'BH', 181, 'RO', 47.42779000, 22.11416000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q722456'),
(90661, 'Cheț', 4723, 'BH', 181, 'RO', 47.41943000, 22.33877000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q722456'),
(90662, 'Chiajna', 4725, 'IF', 181, 'RO', 44.46000000, 25.97333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12116357'),
(90663, 'Chichiş', 4754, 'CV', 181, 'RO', 45.78333000, 25.80000000, '2019-10-05 23:14:44', '2020-05-01 17:23:09', 1, 'Q1093119'),
(90664, 'Chier', 4739, 'AR', 181, 'RO', 46.34199000, 21.82488000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q755949'),
(90665, 'Chieşd', 4741, 'SJ', 181, 'RO', 47.38333000, 22.88333000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q1188939'),
(90666, 'Chilia Veche', 4727, 'TL', 181, 'RO', 45.41667000, 29.28333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q860733'),
(90667, 'Chilii', 4731, 'NT', 181, 'RO', 46.80254000, 27.03394000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12116436'),
(90668, 'Chiliile', 4756, 'BZ', 181, 'RO', 45.45000000, 26.58333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q3293887'),
(90669, 'Chinteni', 4734, 'CJ', 181, 'RO', 46.86667000, 23.53333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q719714'),
(90670, 'Chintinici', 4731, 'NT', 181, 'RO', 46.82754000, 26.49339000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12129665'),
(90671, 'Chiochiş', 4733, 'BN', 181, 'RO', 46.98333000, 24.18333000, '2019-10-05 23:14:44', '2020-05-01 17:23:08', 1, 'Q541663'),
(90672, 'Chiojdeanca', 4729, 'PH', 181, 'RO', 45.16667000, 26.26667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12116538'),
(90673, 'Chiojdeni', 4758, 'VN', 181, 'RO', 45.55000000, 26.86667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q13829989'),
(90674, 'Chiojdu', 4756, 'BZ', 181, 'RO', 45.35000000, 26.20000000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q6314746'),
(90675, 'Chiraftei', 4747, 'GL', 181, 'RO', 45.77220000, 28.02716000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12116562'),
(90676, 'Chircești', 4752, 'VS', 181, 'RO', 46.84629000, 27.84920000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q12116647'),
(90677, 'Chiriacu', 4726, 'GR', 181, 'RO', 44.05036000, 25.77726000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q12116651'),
(90678, 'Chirnogeni', 4737, 'CT', 181, 'RO', 43.90000000, 28.23333000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q860821'),
(90679, 'Chirnogi', 4732, 'CL', 181, 'RO', 44.11667000, 26.56667000, '2019-10-05 23:14:44', '2019-10-05 23:14:44', 1, 'Q2620875'),
(90680, 'Chirpăr', 4755, 'SB', 181, 'RO', 45.90000000, 24.60000000, '2019-10-05 23:14:44', '2020-05-01 17:23:11', 1, 'Q1126535'),
(90681, 'Chiscani', 4736, 'BR', 181, 'RO', 45.18333000, 27.93333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12116671'),
(90682, 'Chiselet', 4732, 'CL', 181, 'RO', 44.18333000, 26.85000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2453883'),
(90683, 'Chisindia', 4739, 'AR', 181, 'RO', 46.28333000, 22.10000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1067664'),
(90684, 'Chitila', 4725, 'IF', 181, 'RO', 44.50806000, 25.98222000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1067664'),
(90685, 'Chiuiești', 4734, 'CJ', 181, 'RO', 47.29686000, 23.87553000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q1075731'),
(90686, 'Chiuza', 4733, 'BN', 181, 'RO', 47.23333000, 24.25000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1126087'),
(90687, 'Chişineu-Criş', 4739, 'AR', 181, 'RO', 46.52509000, 21.51844000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q1126087'),
(90688, 'Chişlaz', 4723, 'BH', 181, 'RO', 47.28333000, 22.23333000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q1189440'),
(90689, 'Chișcăreni', 4735, 'IS', 181, 'RO', 47.46974000, 27.19626000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q12116727'),
(90690, 'Chișoda', 4748, 'TM', 181, 'RO', 45.70380000, 21.21437000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, 'Q550415'),
(90691, 'Chițoc', 4752, 'VS', 181, 'RO', 46.59545000, 27.67450000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, 'Q12116708'),
(90692, 'Chițorani', 4729, 'PH', 181, 'RO', 44.97458000, 26.11895000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, 'Q12116710'),
(90693, 'Ciacova', 4748, 'TM', 181, 'RO', 45.50806000, 21.12861000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12116710'),
(90694, 'Ciceu', 4749, 'HR', 181, 'RO', 46.41328000, 25.78204000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1069815'),
(90695, 'Ciceu-Giurgeşti', 4733, 'BN', 181, 'RO', 47.25000000, 24.01667000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q872775'),
(90696, 'Ciceu-Mihăiești', 4733, 'BN', 181, 'RO', 47.18568000, 23.97480000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q872791'),
(90697, 'Ciclova-Română', 4753, 'CS', 181, 'RO', 45.01361000, 21.71917000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q872920'),
(90698, 'Cicârlău', 4760, 'MM', 181, 'RO', 47.69155000, 23.40380000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q1081311'),
(90699, 'Cicănești', 4722, 'AG', 181, 'RO', 45.24601000, 24.60526000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q2534337'),
(90700, 'Cidreag', 4746, 'SM', 181, 'RO', 47.98677000, 22.96395000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q747694'),
(90701, 'Cilibia', 4756, 'BZ', 181, 'RO', 45.06667000, 27.06667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q3293641'),
(90702, 'Cilieni', 4738, 'OT', 181, 'RO', 43.90000000, 24.61667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q958061'),
(90703, 'Cincu', 4759, 'BV', 181, 'RO', 45.91667000, 24.80000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q15241297'),
(90704, 'Cintei', 4739, 'AR', 181, 'RO', 46.44434000, 21.56556000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q755727'),
(90705, 'Ciobanu', 4737, 'CT', 181, 'RO', 44.71759000, 27.98698000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q867451'),
(90706, 'Ciocani', 4752, 'VS', 181, 'RO', 46.26044000, 27.55994000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2297424'),
(90707, 'Ciochina', 4743, 'IL', 181, 'RO', 44.58333000, 27.06667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12724641'),
(90708, 'Ciochiuța', 4751, 'MH', 181, 'RO', 44.60306000, 23.11698000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q12724641'),
(90709, 'Ciocile', 4736, 'BR', 181, 'RO', 44.81667000, 27.23333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q3692492'),
(90710, 'Ciocârlia', 4743, 'IL', 181, 'RO', 44.80000000, 26.66667000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q12170419'),
(90711, 'Ciocârlia', 4737, 'CT', 181, 'RO', 44.10000000, 28.28333000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q867452'),
(90712, 'Ciocârlia de Sus', 4737, 'CT', 181, 'RO', 44.11712000, 28.33402000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q12170418'),
(90713, 'Ciocănari', 4745, 'DB', 181, 'RO', 44.69770000, 25.97717000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q12170407'),
(90714, 'Ciocăneşti', 4732, 'CL', 181, 'RO', 44.20000000, 27.06667000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q2715887'),
(90715, 'Ciocăneşti', 4745, 'DB', 181, 'RO', 44.60000000, 25.85000000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q12170411'),
(90716, 'Ciocănești', 4720, 'SV', 181, 'RO', 47.48107000, 25.27912000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, 'Q2720035'),
(90717, 'Ciofliceni', 4725, 'IF', 181, 'RO', 44.68122000, 26.11923000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170676'),
(90718, 'Ciofrângeni', 4722, 'AG', 181, 'RO', 45.08333000, 24.53333000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q2348382'),
(90719, 'Ciohorăni', 4735, 'IS', 181, 'RO', 47.13520000, 26.68955000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q2540484'),
(90720, 'Ciolpani', 4725, 'IF', 181, 'RO', 44.73333000, 26.08333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170439'),
(90721, 'Ciomăgeşti', 4722, 'AG', 181, 'RO', 44.83333000, 24.45000000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q2535277'),
(90722, 'Ciorani', 4758, 'VN', 181, 'RO', 45.98824000, 27.21614000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170457'),
(90723, 'Cioranii de Jos', 4729, 'PH', 181, 'RO', 44.81667000, 26.41667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170458'),
(90724, 'Cioranii de Sus', 4729, 'PH', 181, 'RO', 44.83923000, 26.39473000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170460'),
(90725, 'Cioroboreni', 4751, 'MH', 181, 'RO', 44.38877000, 22.76326000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170627'),
(90726, 'Ciorogârla', 4725, 'IF', 181, 'RO', 44.44250000, 25.88333000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q12170626'),
(90727, 'Cioroiași', 4742, 'DJ', 181, 'RO', 44.08333000, 23.45000000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q2127277'),
(90728, 'Ciorteşti', 4735, 'IS', 181, 'RO', 46.90000000, 27.83333000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q2607419'),
(90729, 'Ciorăşti', 4758, 'VN', 181, 'RO', 45.43333000, 27.30000000, '2019-10-05 23:14:45', '2020-05-01 17:23:12', 1, 'Q13916603'),
(90730, 'Ciorăști', 4747, 'GL', 181, 'RO', 46.11380000, 27.45311000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q12170468'),
(90731, 'Ciprian Porumbescu', 4720, 'SV', 181, 'RO', 47.56667000, 26.06667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q867468'),
(90732, 'Cireşu', 4751, 'MH', 181, 'RO', 44.82333000, 22.53806000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q371856'),
(90733, 'Cireşu', 4736, 'BR', 181, 'RO', 44.95000000, 27.35000000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q12170289'),
(90734, 'Cireșoaia', 4744, 'BC', 181, 'RO', 46.24816000, 26.54929000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q12170285'),
(90735, 'Cireșoaia', 4733, 'BN', 181, 'RO', 47.14265000, 24.06217000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q517234'),
(90736, 'Cislău', 4756, 'BZ', 181, 'RO', 45.25000000, 26.36667000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q12170310'),
(90737, 'Cisnădie', 4755, 'SB', 181, 'RO', 45.71667000, 24.15000000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, 'Q12170310'),
(90738, 'Ciuani', 4728, 'TR', 181, 'RO', 44.23333000, 25.65000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q34795501'),
(90739, 'Ciucani', 4749, 'HR', 181, 'RO', 46.25701000, 25.94744000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q713389'),
(90740, 'Ciucea', 4734, 'CJ', 181, 'RO', 46.95000000, 22.81667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q949509'),
(90741, 'Ciuchici', 4753, 'CS', 181, 'RO', 44.94139000, 21.61000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q649773'),
(90742, 'Ciucsângeorgiu', 4749, 'HR', 181, 'RO', 46.31667000, 25.95000000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q876030'),
(90743, 'Ciucurova', 4727, 'TL', 181, 'RO', 44.93333000, 28.48333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170765'),
(90744, 'Ciudanoviţa', 4753, 'CS', 181, 'RO', 45.14472000, 21.79778000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q75693'),
(90745, 'Ciugheș', 4744, 'BC', 181, 'RO', 46.52220000, 26.11970000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q1224494'),
(90746, 'Ciugud', 4724, 'AB', 181, 'RO', 46.05000000, 23.61667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q936124'),
(90747, 'Ciulniţa', 4743, 'IL', 181, 'RO', 44.53333000, 27.35000000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q2291791'),
(90748, 'Ciulnița', 4722, 'AG', 181, 'RO', 44.78769000, 25.15388000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q12170775'),
(90749, 'Ciumani', 4749, 'HR', 181, 'RO', 46.68333000, 25.51667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q523482'),
(90750, 'Ciumbrud', 4724, 'AB', 181, 'RO', 46.30810000, 23.76222000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q720811'),
(90751, 'Ciumeghiu', 4723, 'BH', 181, 'RO', 46.73333000, 21.58333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1078300'),
(90752, 'Ciumești', 4746, 'SM', 181, 'RO', 47.66382000, 22.34069000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, 'Q874054'),
(90753, 'Ciuperceni', 4750, 'GJ', 181, 'RO', 44.93333000, 23.01667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2717787'),
(90754, 'Ciuperceni', 4728, 'TR', 181, 'RO', 43.76667000, 24.95000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2720733'),
(90755, 'Ciupercenii Noi', 4742, 'DJ', 181, 'RO', 43.90972000, 22.94833000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2468466'),
(90756, 'Ciupercenii Vechi', 4742, 'DJ', 181, 'RO', 43.94512000, 22.89469000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12170821'),
(90757, 'Ciurea', 4735, 'IS', 181, 'RO', 47.05000000, 27.56667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2607482'),
(90758, 'Ciurila', 4734, 'CJ', 181, 'RO', 46.65000000, 23.55000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1076333'),
(90759, 'Ciuruleasa', 4724, 'AB', 181, 'RO', 46.25000000, 23.03333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q874875'),
(90760, 'Ciutelec', 4723, 'BH', 181, 'RO', 47.26200000, 22.38690000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q725159'),
(90761, 'Ciușlea', 4758, 'VN', 181, 'RO', 45.78761000, 27.30644000, '2019-10-05 23:14:45', '2020-05-01 17:23:12', 1, 'Q12170902'),
(90762, 'Cizer', 4741, 'SJ', 181, 'RO', 47.06667000, 22.88333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1189469'),
(90763, 'Cișmele', 4747, 'GL', 181, 'RO', 45.51634000, 27.92975000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q12170354'),
(90764, 'Cleanov', 4742, 'DJ', 181, 'RO', 44.35288000, 23.21032000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12110598'),
(90765, 'Cleja', 4744, 'BC', 181, 'RO', 46.41667000, 26.90000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q15633616'),
(90766, 'Clejani', 4726, 'GR', 181, 'RO', 44.31972000, 25.69944000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2465969'),
(90767, 'Clinceni', 4725, 'IF', 181, 'RO', 44.37333000, 25.95472000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12110632'),
(90768, 'Clit', 4720, 'SV', 181, 'RO', 47.75396000, 25.85691000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12110650'),
(90769, 'Clocotici', 4753, 'CS', 181, 'RO', 45.24495000, 21.83467000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q839593'),
(90770, 'Cloșani', 4750, 'GJ', 181, 'RO', 45.06897000, 22.80250000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q12110544'),
(90771, 'Cluj-Napoca', 4734, 'CJ', 181, 'RO', 46.76667000, 23.60000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12110544'),
(90772, 'Coada Izvorului', 4729, 'PH', 181, 'RO', 44.85766000, 25.81851000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12110791'),
(90773, 'Coarnele Caprei', 4735, 'IS', 181, 'RO', 47.38333000, 27.10000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2621221'),
(90774, 'Coaș', 4760, 'MM', 181, 'RO', 47.53957000, 23.58584000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q1077871'),
(90775, 'Cobadin', 4737, 'CT', 181, 'RO', 44.08333000, 28.21667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q868112'),
(90776, 'Cochirleanca', 4756, 'BZ', 181, 'RO', 45.21667000, 27.03333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q3293657'),
(90777, 'Cociu', 4733, 'BN', 181, 'RO', 47.19926000, 24.23238000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q3892349'),
(90778, 'Cociuba Mare', 4723, 'BH', 181, 'RO', 46.73333000, 22.00000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q1195200'),
(90779, 'Coconi', 4732, 'CL', 181, 'RO', 44.24769000, 26.88409000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12111510'),
(90780, 'Cocora', 4743, 'IL', 181, 'RO', 44.73333000, 27.05000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2291913'),
(90781, 'Cocoreni', 4750, 'GJ', 181, 'RO', 44.82772000, 23.31405000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, '*********'),
(90782, 'Cocorăștii Colț', 4729, 'PH', 181, 'RO', 44.83425000, 25.89937000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, '*********'),
(90783, 'Cocorăștii Mislii', 4729, 'PH', 181, 'RO', 45.08528000, 25.92206000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, '*********'),
(90784, 'Cocu', 4722, 'AG', 181, 'RO', 44.86667000, 24.65000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, '********'),
(90785, 'Codlea', 4759, 'BV', 181, 'RO', 45.70000000, 25.45000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, '********'),
(90786, 'Codăeşti', 4752, 'VS', 181, 'RO', 46.86667000, 27.75000000, '2019-10-05 23:14:45', '2020-05-01 17:23:11', 1, '********'),
(90787, 'Cogealac', 4737, 'CT', 181, 'RO', 44.55000000, 28.56667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q868214'),
(90788, 'Cogeasca', 4735, 'IS', 181, 'RO', 47.16644000, 27.38537000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, '*********'),
(90789, 'Cojasca', 4745, 'DB', 181, 'RO', 44.71667000, 25.85000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, '********'),
(90790, 'Cojocna', 4734, 'CJ', 181, 'RO', 46.75000000, 23.83333000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q267692'),
(90791, 'Colacu', 4745, 'DB', 181, 'RO', 44.66895000, 25.75881000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q267692'),
(90792, 'Colacu', 4720, 'SV', 181, 'RO', 47.53830000, 25.37211000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12111544'),
(90793, 'Colceag', 4729, 'PH', 181, 'RO', 44.95000000, 26.35000000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q12111823'),
(90794, 'Colelia', 4743, 'IL', 181, 'RO', 44.76112000, 27.00693000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q3791428'),
(90795, 'Colibaşi', 4726, 'GR', 181, 'RO', 44.20222000, 26.19472000, '2019-10-05 23:14:45', '2020-05-01 17:23:09', 1, 'Q2289382'),
(90796, 'Colibași', 4722, 'AG', 181, 'RO', 44.93544000, 24.90905000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q12111861'),
(90797, 'Coloneşti', 4744, 'BC', 181, 'RO', 46.56667000, 27.30000000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q15633673'),
(90798, 'Coloneşti', 4738, 'OT', 181, 'RO', 44.65000000, 24.66667000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q2292689'),
(90799, 'Colonia Bod', 4759, 'BV', 181, 'RO', 45.75588000, 25.59960000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q848431'),
(90800, 'Colonia Fabricii', 4748, 'TM', 181, 'RO', 45.76620000, 22.31463000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q3888931'),
(90801, 'Coltău', 4760, 'MM', 181, 'RO', 47.59837000, 23.52459000, '2019-10-05 23:14:45', '2020-05-01 17:23:10', 1, 'Q1068882'),
(90802, 'Colţi', 4756, 'BZ', 181, 'RO', 45.38333000, 26.38333000, '2019-10-05 23:14:45', '2020-05-01 17:23:08', 1, 'Q2289319'),
(90803, 'Comana', 4726, 'GR', 181, 'RO', 44.17056000, 26.14500000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q2717316'),
(90804, 'Comana', 4737, 'CT', 181, 'RO', 43.90000000, 28.31667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q868420'),
(90805, 'Comana de Jos', 4759, 'BV', 181, 'RO', 45.91375000, 25.23129000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q4845481'),
(90806, 'Comana de Sus', 4759, 'BV', 181, 'RO', 45.90000000, 25.26667000, '2019-10-05 23:14:45', '2019-10-05 23:14:45', 1, 'Q247011'),
(90807, 'Comanca', 4738, 'OT', 181, 'RO', 44.07790000, 24.35863000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12111904'),
(90808, 'Comanda', 4751, 'MH', 181, 'RO', 44.64863000, 23.16413000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12111904'),
(90809, 'Comandău', 4754, 'CV', 181, 'RO', 45.76667000, 26.26667000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q430691'),
(90810, 'Comani', 4738, 'OT', 181, 'RO', 44.18286000, 24.49160000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12111907'),
(90811, 'Comarna', 4735, 'IS', 181, 'RO', 47.05000000, 27.78333000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2607513'),
(90812, 'Comarnic', 4729, 'PH', 181, 'RO', 45.25000000, 25.63333000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2607513'),
(90813, 'Comişani', 4745, 'DB', 181, 'RO', 44.88333000, 25.58333000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q3726347'),
(90814, 'Comloşu Mare', 4748, 'TM', 181, 'RO', 45.89056000, 20.62722000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q535485'),
(90815, 'Comoșteni', 4742, 'DJ', 181, 'RO', 43.87405000, 23.86356000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q3684949'),
(90816, 'Comuna 1 Decembrie', 4725, 'IF', 181, 'RO', 44.28869000, 26.06178000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q1076455'),
(90817, 'Comuna 23 August', 4737, 'CT', 181, 'RO', 43.91938000, 28.55437000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q216248'),
(90818, 'Comuna Abram', 4723, 'BH', 181, 'RO', 47.32443000, 22.42023000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q1194316'),
(90819, 'Comuna Abrămuţ', 4723, 'BH', 181, 'RO', 47.33346000, 22.25607000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q1088691'),
(90820, 'Comuna Acâş', 4746, 'SM', 181, 'RO', 47.53143000, 22.74106000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q5055516'),
(90821, 'Comuna Adamclisi', 4737, 'CT', 181, 'RO', 44.11447000, 27.94990000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q351559'),
(90822, 'Comuna Adunaţi', 4729, 'PH', 181, 'RO', 45.18832000, 25.60181000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q1089713'),
(90823, 'Comuna Adunaţii-Copăceni', 4726, 'GR', 181, 'RO', 44.25847000, 26.06477000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q12724497'),
(90824, 'Comuna Adâncata', 4720, 'SV', 181, 'RO', 47.73678000, 26.30819000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q506706'),
(90825, 'Comuna Adâncata', 4743, 'IL', 181, 'RO', 44.76691000, 26.43010000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q4681569'),
(90826, 'Comuna Adăşeni', 4740, 'BT', 181, 'RO', 48.06287000, 26.95205000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q3653294'),
(90827, 'Comuna Afumaţi', 4742, 'DJ', 181, 'RO', 43.99793000, 23.44386000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q2538246'),
(90828, 'Comuna Afumaţi', 4725, 'IF', 181, 'RO', 44.52501000, 26.24943000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q2486305'),
(90829, 'Comuna Agapia', 4731, 'NT', 181, 'RO', 47.16196000, 26.29545000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q515078'),
(90830, 'Comuna Aghireşu', 4734, 'CJ', 181, 'RO', 46.87785000, 23.25320000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q12724502'),
(90831, 'Comuna Agigea', 4737, 'CT', 181, 'RO', 44.09654000, 28.62684000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q516748'),
(90832, 'Comuna Agrij', 4741, 'SJ', 181, 'RO', 47.06784000, 23.09764000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16427273'),
(90833, 'Comuna Agriş', 4746, 'SM', 181, 'RO', 47.87377000, 23.00343000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q5063878'),
(90834, 'Comuna Agăş', 4744, 'BC', 181, 'RO', 46.46458000, 26.22962000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q1195094'),
(90835, 'Comuna Aita Mare', 4754, 'CV', 181, 'RO', 45.97126000, 25.58824000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16439527'),
(90836, 'Comuna Aiton', 4734, 'CJ', 181, 'RO', 46.68275000, 23.71608000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16426133'),
(90837, 'Comuna Albac', 4724, 'AB', 181, 'RO', 46.45246000, 22.95027000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q1027784'),
(90838, 'Comuna Albeni', 4750, 'GJ', 181, 'RO', 45.01363000, 23.60208000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q4746529'),
(90839, 'Comuna Albeşti', 4740, 'BT', 181, 'RO', 47.68387000, 27.08704000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q4845241'),
(90840, 'Comuna Albeşti', 4743, 'IL', 181, 'RO', 44.53575000, 27.12464000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q8272784'),
(90841, 'Comuna Albeşti', 4752, 'VS', 181, 'RO', 46.49850000, 27.85167000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q12724496'),
(90842, 'Comuna Albeşti', 4737, 'CT', 181, 'RO', 43.80863000, 28.40325000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q12077550'),
(90843, 'Comuna Albeşti-Paleologu', 4729, 'PH', 181, 'RO', 44.93431000, 26.23302000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q1195733'),
(90844, 'Comuna Albeștii de Argeș', 4722, 'AG', 181, 'RO', 45.21667000, 24.66667000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q2348189'),
(90845, 'Comuna Albeștii de Muscel', 4722, 'AG', 181, 'RO', 45.31667000, 25.00000000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q1884138'),
(90846, 'Comuna Albota', 4722, 'AG', 181, 'RO', 44.78287000, 24.81838000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724500'),
(90847, 'Comuna Alexandru Cel Bun', 4731, 'NT', 181, 'RO', 46.93988000, 26.26849000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2719337'),
(90848, 'Comuna Alexandru I. Cuza', 4735, 'IS', 181, 'RO', 47.14424000, 26.85449000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5114322'),
(90849, 'Comuna Alexandru Odobescu', 4732, 'CL', 181, 'RO', 44.27254000, 27.09402000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q609482'),
(90850, 'Comuna Alexandru Vlăhuţă', 4752, 'VS', 181, 'RO', 46.44973000, 27.62420000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q16427794'),
(90851, 'Comuna Alexeni', 4743, 'IL', 181, 'RO', 44.67966000, 26.71064000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16426744'),
(90852, 'Comuna Aliman', 4737, 'CT', 181, 'RO', 44.18430000, 27.83697000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12077924'),
(90853, 'Comuna Alimpeşti', 4750, 'GJ', 181, 'RO', 45.10542000, 23.79651000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q12724499'),
(90854, 'Comuna Alma', 4755, 'SB', 181, 'RO', 46.22726000, 24.46825000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16427410'),
(90855, 'Comuna Almaş', 4739, 'AR', 181, 'RO', 46.28480000, 22.23173000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q857467'),
(90856, 'Comuna Almaşu', 4741, 'SJ', 181, 'RO', 46.95000000, 23.13333000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q12724510'),
(90857, 'Comuna Almaşu Mare', 4724, 'AB', 181, 'RO', 46.10877000, 23.12038000, '2019-10-05 23:14:46', '2020-05-01 17:23:07', 1, 'Q16423977'),
(90858, 'Comuna Almãj', 4742, 'DJ', 181, 'RO', 44.44093000, 23.70915000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q587728'),
(90859, 'Comuna Aluniş', 4729, 'PH', 181, 'RO', 45.20105000, 25.89661000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q16427194'),
(90860, 'Comuna Aluniş', 4734, 'CJ', 181, 'RO', 47.05595000, 23.74365000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q16426136'),
(90861, 'Comuna Alunu', 4757, 'VL', 181, 'RO', 45.00954000, 23.81760000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5117360'),
(90862, 'Comuna Alțâna', 4755, 'SB', 181, 'RO', 45.95534000, 24.46144000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q2840450'),
(90863, 'Comuna Amaru', 4756, 'BZ', 181, 'RO', 44.93770000, 26.59991000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2631497'),
(90864, 'Comuna Amzacea', 4737, 'CT', 181, 'RO', 43.95983000, 28.32122000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12078004'),
(90865, 'Comuna Amărăşti', 4757, 'VL', 181, 'RO', 44.76107000, 24.14382000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q3916038'),
(90866, 'Comuna Amărăştii de Jos', 4742, 'DJ', 181, 'RO', 43.92611000, 24.16076000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q1993249'),
(90867, 'Comuna Amărăştii de Sus', 4742, 'DJ', 181, 'RO', 43.98588000, 24.16715000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q269188'),
(90868, 'Comuna Andreiaşu de Jos', 4758, 'VN', 181, 'RO', 45.73669000, 26.82752000, '2019-10-05 23:14:46', '2020-05-01 17:23:12', 1, 'Q764767'),
(90869, 'Comuna Andrid', 4746, 'SM', 181, 'RO', 47.51720000, 22.34688000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5063237'),
(90870, 'Comuna Andrieşeni', 4735, 'IS', 181, 'RO', 47.51112000, 27.29502000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q5113934'),
(90871, 'Comuna Andrăşeşti', 4743, 'IL', 181, 'RO', 44.57648000, 27.12438000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q2718077'),
(90872, 'Comuna Aninoasa', 4722, 'AG', 181, 'RO', 45.20222000, 24.91206000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2218703'),
(90873, 'Comuna Aninoasa', 4750, 'GJ', 181, 'RO', 44.74836000, 23.47372000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724508'),
(90874, 'Comuna Aninoasa', 4745, 'DB', 181, 'RO', 44.96863000, 25.43802000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2621232'),
(90875, 'Comuna Apa', 4746, 'SM', 181, 'RO', 47.74934000, 23.18966000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5054942'),
(90876, 'Comuna Apahida', 4734, 'CJ', 181, 'RO', 46.78296000, 23.74433000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16426139'),
(90877, 'Comuna Apateu', 4739, 'AR', 181, 'RO', 46.62598000, 21.82530000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16425390'),
(90878, 'Comuna Apaţa', 4759, 'BV', 181, 'RO', 45.95043000, 25.53312000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q1070596'),
(90879, 'Comuna Apele Vii', 4742, 'DJ', 181, 'RO', 44.06658000, 24.06611000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2468564'),
(90880, 'Comuna Apoldu de Jos', 4755, 'SB', 181, 'RO', 45.89598000, 23.85036000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5065712'),
(90881, 'Comuna Apostolache', 4729, 'PH', 181, 'RO', 45.12123000, 26.26537000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q1411305'),
(90882, 'Comuna Arbore', 4720, 'SV', 181, 'RO', 47.74775000, 25.90345000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q1744621'),
(90883, 'Comuna Arcani', 4750, 'GJ', 181, 'RO', 45.07232000, 23.14108000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724512'),
(90884, 'Comuna Archiş', 4739, 'AR', 181, 'RO', 46.48333000, 22.09663000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q12682828'),
(90885, 'Comuna Arcuş', 4754, 'CV', 181, 'RO', 45.90077000, 25.77673000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q12682832'),
(90886, 'Comuna Ardeoani', 4744, 'BC', 181, 'RO', 46.52183000, 26.60165000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q1195075'),
(90887, 'Comuna Ardusat', 4760, 'MM', 181, 'RO', 47.63241000, 23.39156000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5055537'),
(90888, 'Comuna Arefu', 4722, 'AG', 181, 'RO', 45.33142000, 24.62944000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724513'),
(90889, 'Comuna Argetoaia', 4742, 'DJ', 181, 'RO', 44.50920000, 23.37593000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2468510'),
(90890, 'Comuna Ariceştii Zeletin', 4729, 'PH', 181, 'RO', 45.21513000, 26.16879000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q16427201'),
(90891, 'Comuna Ariceştii-Rahtivani', 4729, 'PH', 181, 'RO', 44.94512000, 25.86208000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q16427198'),
(90892, 'Comuna Arieşeni', 4724, 'AB', 181, 'RO', 46.47659000, 22.75535000, '2019-10-05 23:14:46', '2020-05-01 17:23:07', 1, 'Q1820095'),
(90893, 'Comuna Ariniş', 4760, 'MM', 181, 'RO', 47.50757000, 23.22435000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q16426860'),
(90894, 'Comuna Armeniş', 4753, 'CS', 181, 'RO', 45.21901000, 22.35198000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q5065543'),
(90895, 'Comuna Armăşeşti', 4743, 'IL', 181, 'RO', 44.76397000, 26.57999000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q16426747'),
(90896, 'Comuna Aroneanu', 4735, 'IS', 181, 'RO', 47.21584000, 27.60632000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5069585'),
(90897, 'Comuna Arpaşu de Jos', 4755, 'SB', 181, 'RO', 45.77314000, 24.60870000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q12079139'),
(90898, 'Comuna Arsura', 4752, 'VS', 181, 'RO', 46.79618000, 28.03896000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724514'),
(90899, 'Comuna Asuaju de Sus', 4760, 'MM', 181, 'RO', 47.55000000, 23.20000000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5054856'),
(90900, 'Comuna Asău', 4744, 'BC', 181, 'RO', 46.46495000, 26.37497000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q779771'),
(90901, 'Comuna Atid', 4749, 'HR', 181, 'RO', 46.45844000, 25.04357000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5056483'),
(90902, 'Comuna Augustin', 4759, 'BV', 181, 'RO', 46.04613000, 25.55333000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q937652'),
(90903, 'Comuna Auşeu', 4723, 'BH', 181, 'RO', 47.04398000, 22.50835000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q1189640'),
(90904, 'Comuna Avram Iancu', 4723, 'BH', 181, 'RO', 46.66944000, 21.52417000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q954773'),
(90905, 'Comuna Avram Iancu', 4724, 'AB', 181, 'RO', 46.38333000, 22.78333000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16425205'),
(90906, 'Comuna Avrămeni', 4740, 'BT', 181, 'RO', 48.02439000, 26.95734000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q12724516'),
(90907, 'Comuna Avrămeşti', 4749, 'HR', 181, 'RO', 46.36400000, 25.03223000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q116292'),
(90908, 'Comuna Axente Sever', 4755, 'SB', 181, 'RO', 46.07315000, 24.22535000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q779030'),
(90909, 'Comuna Axintele', 4743, 'IL', 181, 'RO', 44.58688000, 26.78809000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2718996'),
(90910, 'Comuna Aşchileu', 4734, 'CJ', 181, 'RO', 46.98498000, 23.48468000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q8238655'),
(90911, 'Comuna Aştileu', 4723, 'BH', 181, 'RO', 47.02217000, 22.38444000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q1077905'),
(90912, 'Comuna Aţel', 4755, 'SB', 181, 'RO', 46.15465000, 24.48297000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q5060260'),
(90913, 'Comuna Baba Ana', 4729, 'PH', 181, 'RO', 44.97508000, 26.48588000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5053313'),
(90914, 'Comuna Baciu', 4734, 'CJ', 181, 'RO', 46.82010000, 23.49036000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16426143'),
(90915, 'Comuna Bahna', 4731, 'NT', 181, 'RO', 46.77860000, 26.79742000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2605720'),
(90916, 'Comuna Baia', 4720, 'SV', 181, 'RO', 47.41311000, 26.20278000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q1044072'),
(90917, 'Comuna Baia', 4727, 'TL', 181, 'RO', 44.75708000, 28.64028000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5066924'),
(90918, 'Comuna Baia de Criş', 4721, 'HD', 181, 'RO', 46.18227000, 22.70027000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q543387'),
(90919, 'Comuna Baia de Fier', 4750, 'GJ', 181, 'RO', 45.17402000, 23.74152000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724520'),
(90920, 'Comuna Bala', 4751, 'MH', 181, 'RO', 44.89479000, 22.83235000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724528'),
(90921, 'Comuna Balaci', 4728, 'TR', 181, 'RO', 44.35188000, 24.90962000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724522'),
(90922, 'Comuna Balaciu', 4743, 'IL', 181, 'RO', 44.60985000, 26.88277000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q743613'),
(90923, 'Comuna Balc', 4723, 'BH', 181, 'RO', 47.31135000, 22.49954000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16425608'),
(90924, 'Comuna Balcani', 4744, 'BC', 181, 'RO', 46.64108000, 26.54412000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q789391'),
(90925, 'Comuna Baldovineşti', 4738, 'OT', 181, 'RO', 44.39195000, 24.03898000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q16427072'),
(90926, 'Comuna Balinţ', 4748, 'TM', 181, 'RO', 45.82290000, 21.85841000, '2019-10-05 23:14:46', '2020-05-01 17:23:11', 1, 'Q804998'),
(90927, 'Comuna Baloteşti', 4725, 'IF', 181, 'RO', 44.62026000, 26.08664000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q2102634'),
(90928, 'Comuna Balta', 4751, 'MH', 181, 'RO', 44.90652000, 22.61789000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q2605922'),
(90929, 'Comuna Balta Albă', 4756, 'BZ', 181, 'RO', 45.26376000, 27.30558000, '2019-10-05 23:14:46', '2020-05-01 17:23:09', 1, 'Q2536077'),
(90930, 'Comuna Balta Doamnei', 4729, 'PH', 181, 'RO', 44.75775000, 26.19465000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16427204'),
(90931, 'Comuna Balş', 4735, 'IS', 181, 'RO', 47.29219000, 26.96028000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q5113877'),
(90932, 'Comuna Balşa', 4721, 'HD', 181, 'RO', 46.04738000, 23.06886000, '2019-10-05 23:14:46', '2020-05-01 17:23:10', 1, 'Q5054485'),
(90933, 'Comuna Banca', 4752, 'VS', 181, 'RO', 46.34406000, 27.82091000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q685493'),
(90934, 'Comuna Banloc', 4748, 'TM', 181, 'RO', 45.38603000, 21.13203000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q806854'),
(90935, 'Comuna Bara', 4748, 'TM', 181, 'RO', 45.91096000, 21.90440000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q790186'),
(90936, 'Comuna Barcani', 4754, 'CV', 181, 'RO', 45.70483000, 26.06393000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q16426282'),
(90937, 'Comuna Barcea', 4747, 'GL', 181, 'RO', 45.74744000, 27.46265000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q12724527'),
(90938, 'Comuna Baru', 4721, 'HD', 181, 'RO', 45.47912000, 23.15132000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5063993'),
(90939, 'Comuna Bata', 4739, 'AR', 181, 'RO', 45.99641000, 22.07805000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5060807'),
(90940, 'Comuna Batăr', 4723, 'BH', 181, 'RO', 46.71431000, 21.79161000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q1093207'),
(90941, 'Comuna Bazna', 4755, 'SB', 181, 'RO', 46.21384000, 24.26438000, '2019-10-05 23:14:46', '2019-10-05 23:14:46', 1, 'Q5061780'),
(90942, 'Comuna Başcov', 4722, 'AG', 181, 'RO', 44.89295000, 24.78532000, '2019-10-05 23:14:46', '2020-05-01 17:23:08', 1, 'Q5067012'),
(90943, 'Comuna Beba Veche', 4748, 'TM', 181, 'RO', 46.13503000, 20.36289000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q813520'),
(90944, 'Comuna Beceni', 4756, 'BZ', 181, 'RO', 45.38456000, 26.76978000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q2725070'),
(90945, 'Comuna Becicherecu Mic', 4748, 'TM', 181, 'RO', 45.83475000, 21.05195000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q813656'),
(90946, 'Comuna Beciu', 4728, 'TR', 181, 'RO', 44.01272000, 24.66163000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q12724529'),
(90947, 'Comuna Beclean', 4759, 'BV', 181, 'RO', 45.83119000, 24.92317000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q1078916'),
(90948, 'Comuna Beidaud', 4727, 'TL', 181, 'RO', 44.72132000, 28.53265000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5066952'),
(90949, 'Comuna Belceşti', 4735, 'IS', 181, 'RO', 47.30581000, 27.08747000, '2019-10-05 23:14:47', '2020-05-01 17:23:10', 1, 'Q5069787'),
(90950, 'Comuna Belciugatele', 4732, 'CL', 181, 'RO', 44.51160000, 26.44556000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q2325305'),
(90951, 'Comuna Beleţi-Negreşti', 4722, 'AG', 181, 'RO', 44.94651000, 25.08603000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q1896908'),
(90952, 'Comuna Belin', 4754, 'CV', 181, 'RO', 45.93361000, 25.59126000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16426285'),
(90953, 'Comuna Belinţ', 4748, 'TM', 181, 'RO', 45.77023000, 21.77550000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q792253'),
(90954, 'Comuna Beliu', 4739, 'AR', 181, 'RO', 46.52965000, 21.99566000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5060686'),
(90955, 'Comuna Beliş', 4734, 'CJ', 181, 'RO', 46.65246000, 22.95213000, '2019-10-05 23:14:47', '2020-05-01 17:23:09', 1, 'Q16426147'),
(90956, 'Comuna Beltiug', 4746, 'SM', 181, 'RO', 47.56523000, 22.85452000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16427246'),
(90957, 'Comuna Benesat', 4741, 'SJ', 181, 'RO', 47.41667000, 23.30000000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16439970'),
(90958, 'Comuna Bengeşti-Ciocadia', 4750, 'GJ', 181, 'RO', 45.09845000, 23.60621000, '2019-10-05 23:14:47', '2020-05-01 17:23:10', 1, 'Q2717824'),
(90959, 'Comuna Berca', 4756, 'BZ', 181, 'RO', 45.30098000, 26.65989000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q2716494'),
(90960, 'Comuna Berceni', 4729, 'PH', 181, 'RO', 44.92376000, 26.10532000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q281642'),
(90961, 'Comuna Berceni', 4725, 'IF', 181, 'RO', 44.30887000, 26.19051000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q12724532'),
(90962, 'Comuna Berchişeşti', 4720, 'SV', 181, 'RO', 47.53260000, 26.04276000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q12724533'),
(90963, 'Comuna Berevoeşti', 4722, 'AG', 181, 'RO', 45.25327000, 24.94292000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q12724537'),
(90964, 'Comuna Berezeni', 4752, 'VS', 181, 'RO', 46.39654000, 28.11570000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q12724536'),
(90965, 'Comuna Bereşti-Bistriţa', 4744, 'BC', 181, 'RO', 46.70870000, 26.85648000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q3407910'),
(90966, 'Comuna Bereşti-Meria', 4747, 'GL', 181, 'RO', 46.10362000, 27.92288000, '2019-10-05 23:14:47', '2020-05-01 17:23:09', 1, 'Q3725370'),
(90967, 'Comuna Bereşti-Tazlău', 4744, 'BC', 181, 'RO', 46.48302000, 26.67877000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q3407946'),
(90968, 'Comuna Berghin', 4724, 'AB', 181, 'RO', 46.07484000, 23.73768000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16425208'),
(90969, 'Comuna Berislăveşti', 4757, 'VL', 181, 'RO', 45.26406000, 24.43785000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q12724540'),
(90970, 'Comuna Beriu', 4721, 'HD', 181, 'RO', 45.76347000, 23.23126000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q793937'),
(90971, 'Comuna Berlişte', 4753, 'CS', 181, 'RO', 44.99004000, 21.46920000, '2019-10-05 23:14:47', '2020-05-01 17:23:09', 1, 'Q5065521'),
(90972, 'Comuna Bertea', 4729, 'PH', 181, 'RO', 45.24083000, 25.84545000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q793781'),
(90973, 'Comuna Berteştii de Jos', 4736, 'BR', 181, 'RO', 44.83024000, 27.72008000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q2621183'),
(90974, 'Comuna Berveni', 4746, 'SM', 181, 'RO', 47.74896000, 22.47553000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5063296'),
(90975, 'Comuna Berzasca', 4753, 'CS', 181, 'RO', 44.64845000, 21.98580000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5056350'),
(90976, 'Comuna Berzovia', 4753, 'CS', 181, 'RO', 45.40777000, 21.60233000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5054021'),
(90977, 'Comuna Berzunţi', 4744, 'BC', 181, 'RO', 46.41360000, 26.62237000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q2534933'),
(90978, 'Comuna Bethausen', 4748, 'TM', 181, 'RO', 45.82505000, 21.96054000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q794223'),
(90979, 'Comuna Beuca', 4728, 'TR', 181, 'RO', 44.26595000, 24.97928000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q12724542'),
(90980, 'Comuna Bezdead', 4745, 'DB', 181, 'RO', 45.13705000, 25.49476000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q2246098'),
(90981, 'Comuna Beştepe', 4727, 'TL', 181, 'RO', 45.11243000, 29.02425000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q12724543'),
(90982, 'Comuna Bicaz', 4760, 'MM', 181, 'RO', 47.46241000, 23.01474000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q3813859'),
(90983, 'Comuna Bicaz Chei', 4731, 'NT', 181, 'RO', 46.83435000, 25.87234000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q658477'),
(90984, 'Comuna Bicazu Ardelean', 4731, 'NT', 181, 'RO', 46.86884000, 25.92570000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q1029373'),
(90985, 'Comuna Biertan', 4755, 'SB', 181, 'RO', 46.12456000, 24.51945000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5068884'),
(90986, 'Comuna Biharia', 4723, 'BH', 181, 'RO', 47.16001000, 21.93299000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q1094953'),
(90987, 'Comuna Bilbor', 4749, 'HR', 181, 'RO', 47.05615000, 25.49989000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16426672'),
(90988, 'Comuna Bilca', 4720, 'SV', 181, 'RO', 47.91826000, 25.75457000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q12724545'),
(90989, 'Comuna Bilciureşti', 4745, 'DB', 181, 'RO', 44.74770000, 25.80621000, '2019-10-05 23:14:47', '2020-05-01 17:23:09', 1, 'Q2621160'),
(90990, 'Comuna Biled', 4748, 'TM', 181, 'RO', 45.88760000, 20.96214000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q745258'),
(90991, 'Comuna Bilieşti', 4758, 'VN', 181, 'RO', 45.72106000, 27.34879000, '2019-10-05 23:14:47', '2020-05-01 17:23:12', 1, 'Q3915648'),
(90992, 'Comuna Birchiş', 4739, 'AR', 181, 'RO', 45.96987000, 22.16845000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q5061643'),
(90993, 'Comuna Birda', 4748, 'TM', 181, 'RO', 45.41265000, 21.33733000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q803933'),
(90994, 'Comuna Bisoca', 4756, 'BZ', 181, 'RO', 45.54340000, 26.70591000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q2716813'),
(90995, 'Comuna Bistra', 4724, 'AB', 181, 'RO', 46.37856000, 23.10150000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16425212'),
(90996, 'Comuna Bistra', 4760, 'MM', 181, 'RO', 47.86860000, 24.19291000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5064031'),
(90997, 'Comuna Bistreţ', 4742, 'DJ', 181, 'RO', 43.90162000, 23.52291000, '2019-10-05 23:14:47', '2020-05-01 17:23:09', 1, 'Q3304905'),
(90998, 'Comuna Bistriţa Bârgăului', 4733, 'BN', 181, 'RO', 47.19026000, 24.83429000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q5070528'),
(90999, 'Comuna Bivolari', 4735, 'IS', 181, 'RO', 47.52988000, 27.42726000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5114003'),
(91000, 'Comuna Bixad', 4746, 'SM', 181, 'RO', 47.92031000, 23.37337000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5054525'),
(91001, 'Comuna Bixad', 4754, 'CV', 181, 'RO', 46.10225000, 25.86344000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16426289'),
(91002, 'Comuna Blejeşti', 4728, 'TR', 181, 'RO', 44.30317000, 25.45269000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q12724546'),
(91003, 'Comuna Blejoi', 4729, 'PH', 181, 'RO', 44.98262000, 26.01881000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q12082897'),
(91004, 'Comuna Blândeşti', 4740, 'BT', 181, 'RO', 47.71218000, 26.89193000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q12724547'),
(91005, 'Comuna Blândiana', 4724, 'AB', 181, 'RO', 45.98146000, 23.38543000, '2019-10-05 23:14:47', '2020-05-01 17:23:07', 1, 'Q16425215'),
(91006, 'Comuna Blăgeşti', 4744, 'BC', 181, 'RO', 46.68668000, 26.64835000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q3600604'),
(91007, 'Comuna Blăgeşti', 4752, 'VS', 181, 'RO', 46.14130000, 27.99695000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q12724548'),
(91008, 'Comuna Blăjani', 4756, 'BZ', 181, 'RO', 45.29964000, 26.81716000, '2019-10-05 23:14:47', '2020-05-01 17:23:09', 1, 'Q2536178'),
(91009, 'Comuna Blăjel', 4755, 'SB', 181, 'RO', 46.22436000, 24.34108000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q5056793'),
(91010, 'Comuna Blăjeni', 4721, 'HD', 181, 'RO', 46.25491000, 22.90335000, '2019-10-05 23:14:47', '2020-05-01 17:23:10', 1, 'Q804494'),
(91011, 'Comuna Bobiceşti', 4738, 'OT', 181, 'RO', 44.39321000, 24.14215000, '2019-10-05 23:14:47', '2020-05-01 17:23:10', 1, 'Q12724549'),
(91012, 'Comuna Bobota', 4741, 'SJ', 181, 'RO', 47.38333000, 22.76667000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16427286'),
(91013, 'Comuna Bobâlna', 4734, 'CJ', 181, 'RO', 47.13319000, 23.65163000, '2019-10-05 23:14:47', '2020-05-01 17:23:09', 1, 'Q16426150'),
(91014, 'Comuna Bocicoiu Mare', 4760, 'MM', 181, 'RO', 47.94673000, 24.01121000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16426866'),
(91015, 'Comuna Bocsig', 4739, 'AR', 181, 'RO', 46.41893000, 21.98424000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5062541'),
(91016, 'Comuna Bocşa', 4741, 'SJ', 181, 'RO', 47.30332000, 22.91241000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q16427289'),
(91017, 'Comuna Bod', 4759, 'BV', 181, 'RO', 45.75987000, 25.62077000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q1094553'),
(91018, 'Comuna Bodeşti-Precista', 4731, 'NT', 181, 'RO', 47.03819000, 26.43790000, '2019-10-05 23:14:47', '2020-05-01 17:23:10', 1, 'Q2605672'),
(91019, 'Comuna Bodoc', 4754, 'CV', 181, 'RO', 45.96190000, 25.84149000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q5058820'),
(91020, 'Comuna Bogaţi', 4722, 'AG', 181, 'RO', 44.87344000, 25.15045000, '2019-10-05 23:14:47', '2020-05-01 17:23:08', 1, 'Q1884147'),
(91021, 'Comuna Bogda', 4748, 'TM', 181, 'RO', 45.97025000, 21.56964000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q890405'),
(91022, 'Comuna Bogdan Vodă', 4760, 'MM', 181, 'RO', 47.69480000, 24.29548000, '2019-10-05 23:14:47', '2020-05-01 17:23:10', 1, 'Q5056010'),
(91023, 'Comuna Bogdana', 4752, 'VS', 181, 'RO', 46.55124000, 27.61516000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q13569995'),
(91024, 'Comuna Bogdana', 4728, 'TR', 181, 'RO', 43.92860000, 25.08358000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q16427596'),
(91025, 'Comuna Bogdand', 4746, 'SM', 181, 'RO', 47.41824000, 22.92868000, '2019-10-05 23:14:47', '2019-10-05 23:14:47', 1, 'Q12083237'),
(91026, 'Comuna Bogdăneşti', 4720, 'SV', 181, 'RO', 47.36671000, 26.27386000, '2019-10-05 23:14:47', '2020-05-01 17:23:11', 1, 'Q12724553');

