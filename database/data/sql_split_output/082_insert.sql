INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(41300, 'Craponne-sur-Arzon', 4798, 'ARA', 75, 'FR', 45.33092000, 3.84817000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q270654'),
(41301, 'Cravanche', 4825, 'BFC', 75, 'FR', 47.65346000, 6.83197000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q270654'),
(41302, 'Creil', 4828, 'HDF', 75, 'FR', 49.25672000, 2.48477000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q270654'),
(41303, '<PERSON><PERSON><PERSON>', 4799, '<PERSON><PERSON>', 75, 'FR', 43.37524000, 3.01196000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q270654'),
(41304, 'Creissels', 4799, 'OCC', 75, 'FR', 44.08588000, 3.06071000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q270654'),
(41305, 'Creney-près-Troyes', 4820, 'GES', 75, 'FR', 48.33333000, 4.13333000, '2019-10-05 22:47:57', '2020-05-01 17:22:44', 1, 'Q270654'),
(41306, 'Crespin', 4828, 'HDF', 75, 'FR', 50.42110000, 3.66137000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q270654'),
(41307, 'Crespières', 4796, 'IDF', 75, 'FR', 48.88317000, 1.92151000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q270654'),
(41308, 'Cresserons', 4804, 'NOR', 75, 'FR', 49.28701000, -0.35569000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q270654'),
(41309, 'Crest', 4798, 'ARA', 75, 'FR', 44.72639000, 5.01517000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q327259'),
(41310, 'Creully', 4804, 'NOR', 75, 'FR', 49.28478000, -0.53976000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q243675'),
(41311, 'Creuse', 4795, 'NAQ', 75, 'FR', 46.07523000, 2.05476000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q3353'),
(41312, 'Creutzwald', 4820, 'GES', 75, 'FR', 49.20531000, 6.69668000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797557'),
(41313, 'Creuzier-le-Vieux', 4798, 'ARA', 75, 'FR', 46.16253000, 3.43311000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q535340'),
(41314, 'Crevin', 4807, 'BRE', 75, 'FR', 47.93333000, -1.66667000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797571'),
(41315, 'Creys-Mépieu', 4798, 'ARA', 75, 'FR', 45.73333000, 5.48333000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q34797587'),
(41316, 'Creysse', 4795, 'NAQ', 75, 'FR', 44.85474000, 0.56583000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797587'),
(41317, 'Criel-sur-Mer', 4804, 'NOR', 75, 'FR', 50.01508000, 1.31459000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797587'),
(41318, 'Criquebeuf-sur-Seine', 4804, 'NOR', 75, 'FR', 49.30563000, 1.09964000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797587'),
(41319, 'Criquetot-l’Esneval', 4804, 'NOR', 75, 'FR', 49.64555000, 0.26571000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q34797587'),
(41320, 'Crisolles', 4828, 'HDF', 75, 'FR', 49.62250000, 3.01451000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797587'),
(41321, 'Crissey', 4825, 'BFC', 75, 'FR', 46.81652000, 4.88185000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797587'),
(41322, 'Croisilles', 4828, 'HDF', 75, 'FR', 50.19978000, 2.87935000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797587'),
(41323, 'Croissy-Beaubourg', 4796, 'IDF', 75, 'FR', 48.82830000, 2.66964000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q830951'),
(41324, 'Croissy-sur-Seine', 4796, 'IDF', 75, 'FR', 48.87925000, 2.13836000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q830951'),
(41325, 'Croix', 4828, 'HDF', 75, 'FR', 50.67846000, 3.14930000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q830951'),
(41326, 'Crolles', 4798, 'ARA', 75, 'FR', 45.27724000, 5.87837000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q579807'),
(41327, 'Crosne', 4796, 'IDF', 75, 'FR', 48.71921000, 2.45728000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q579807'),
(41328, 'Crossac', 4802, 'PDL', 75, 'FR', 47.41119000, -2.16952000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '*********'),
(41329, 'Croth', 4804, 'NOR', 75, 'FR', 48.84557000, 1.37861000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '*********'),
(41330, 'Crottet', 4798, 'ARA', 75, 'FR', 46.27602000, 4.89388000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '*********'),
(41331, 'Crouy', 4828, 'HDF', 75, 'FR', 49.40274000, 3.35834000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '*********'),
(41332, 'Crouy-en-Thelle', 4828, 'HDF', 75, 'FR', 49.21487000, 2.32146000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '*********'),
(41333, 'Crouy-sur-Ourcq', 4796, 'IDF', 75, 'FR', 49.08980000, 3.07530000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q677372'),
(41334, 'Crozon', 4807, 'BRE', 75, 'FR', 48.24643000, -4.48993000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q218058'),
(41335, 'Cruas', 4798, 'ARA', 75, 'FR', 44.65706000, 4.76293000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q218058'),
(41336, 'Cruet', 4798, 'ARA', 75, 'FR', 45.52890000, 6.09220000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q218058'),
(41337, 'Cruseilles', 4798, 'ARA', 75, 'FR', 46.02980000, 6.10831000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q762920'),
(41338, 'Crusnes', 4820, 'GES', 75, 'FR', 49.43406000, 5.91557000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q762920'),
(41339, 'Crèvecœur-le-Grand', 4828, 'HDF', 75, 'FR', 49.60000000, 2.08333000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q748650'),
(41340, 'Créances', 4804, 'NOR', 75, 'FR', 49.19873000, -1.56938000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q34797816'),
(41341, 'Crécy-en-Ponthieu', 4828, 'HDF', 75, 'FR', 50.25193000, 1.88085000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q61765'),
(41342, 'Crécy-sur-Serre', 4828, 'HDF', 75, 'FR', 49.69594000, 3.62453000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q840977'),
(41343, 'Crédin', 4807, 'BRE', 75, 'FR', 48.03462000, -2.76630000, '2019-10-05 22:47:57', '2020-05-01 17:22:44', 1, 'Q34797823'),
(41344, 'Crégy-lès-Meaux', 4796, 'IDF', 75, 'FR', 48.97648000, 2.87483000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q34797823'),
(41345, 'Créhange', 4820, 'GES', 75, 'FR', 49.05897000, 6.58178000, '2019-10-05 22:47:57', '2020-05-01 17:22:44', 1, 'Q34797823'),
(41346, 'Créhen', 4807, 'BRE', 75, 'FR', 48.54546000, -2.21307000, '2019-10-05 22:47:57', '2020-05-01 17:22:44', 1, 'Q34797830'),
(41347, 'Crémieu', 4798, 'ARA', 75, 'FR', 45.72557000, 5.24911000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q460800'),
(41348, 'Créon', 4795, 'NAQ', 75, 'FR', 44.77457000, -0.34800000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q34797837'),
(41349, 'Crépy', 4828, 'HDF', 75, 'FR', 49.60000000, 3.51667000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q34797849'),
(41350, 'Crépy-en-Valois', 4828, 'HDF', 75, 'FR', 49.23359000, 2.88807000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q182287'),
(41351, 'Créteil', 4796, 'IDF', 75, 'FR', 48.79266000, 2.46569000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q46135'),
(41352, 'Crézancy', 4828, 'HDF', 75, 'FR', 49.04833000, 3.51047000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q46135'),
(41353, 'Crêches-sur-Saône', 4825, 'BFC', 75, 'FR', 46.24475000, 4.78678000, '2019-10-05 22:47:57', '2020-05-01 17:22:44', 1, 'Q46135'),
(41354, 'Cublac', 4795, 'NAQ', 75, 'FR', 45.14488000, 1.30609000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q46135'),
(41355, 'Cublize', 4798, 'ARA', 75, 'FR', 46.01810000, 4.37738000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q46135'),
(41356, 'Cubzac-les-Ponts', 4795, 'NAQ', 75, 'FR', 44.97119000, -0.44976000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q221831'),
(41357, 'Cucq', 4828, 'HDF', 75, 'FR', 50.47733000, 1.62018000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q782596'),
(41358, 'Cucuron', 4812, 'PAC', 75, 'FR', 43.77298000, 5.43858000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q322680'),
(41359, 'Cuers', 4812, 'PAC', 75, 'FR', 43.23754000, 6.07178000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q322680'),
(41360, 'Cuffies', 4828, 'HDF', 75, 'FR', 49.40358000, 3.32119000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q322680'),
(41361, 'Cuffy', 4818, 'CVL', 75, 'FR', 46.96275000, 3.05238000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q322680'),
(41362, 'Cugand', 4802, 'PDL', 75, 'FR', 47.06318000, -1.25270000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '*********'),
(41363, 'Cuges-les-Pins', 4812, 'PAC', 75, 'FR', 43.27607000, 5.69955000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '*********'),
(41364, 'Cugnaux', 4799, 'OCC', 75, 'FR', 43.53635000, 1.34428000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q632107'),
(41365, 'Cuinchy', 4828, 'HDF', 75, 'FR', 50.51750000, 2.74880000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q632107'),
(41366, 'Cuincy', 4828, 'HDF', 75, 'FR', 50.37933000, 3.05301000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q632107'),
(41367, 'Cuise-la-Motte', 4828, 'HDF', 75, 'FR', 49.38642000, 3.00588000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q632107'),
(41368, 'Cuiseaux', 4825, 'BFC', 75, 'FR', 46.49473000, 5.38931000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q632107'),
(41369, 'Cuisery', 4825, 'BFC', 75, 'FR', 46.55696000, 4.99797000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q632107'),
(41370, 'Cuisiat', 4798, 'ARA', 75, 'FR', 46.30021000, 5.38809000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797927'),
(41371, 'Culhat', 4798, 'ARA', 75, 'FR', 45.86259000, 3.33676000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34797927'),
(41372, 'Culoz', 4798, 'ARA', 75, 'FR', 45.84886000, 5.78537000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q243597'),
(41373, 'Cunac', 4799, 'OCC', 75, 'FR', 43.93010000, 2.21878000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q243597'),
(41374, 'Cunlhat', 4798, 'ARA', 75, 'FR', 45.63198000, 3.55927000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q245451'),
(41375, 'Curgies', 4828, 'HDF', 75, 'FR', 50.32975000, 3.60299000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q245451'),
(41376, 'Curgy', 4825, 'BFC', 75, 'FR', 46.98714000, 4.38452000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q1434154'),
(41377, 'Curtin', 4798, 'ARA', 75, 'FR', 45.64353000, 5.48982000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34798024'),
(41378, 'Cussac', 4795, 'NAQ', 75, 'FR', 45.70666000, 0.85124000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34798060'),
(41379, 'Cussac-Fort-Médoc', 4795, 'NAQ', 75, 'FR', 45.11667000, -0.73333000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, 'Q34798066'),
(41380, 'Cussac-sur-Loire', 4798, 'ARA', 75, 'FR', 44.98807000, 3.88416000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34798074'),
(41381, 'Cusset', 4798, 'ARA', 75, 'FR', 46.13452000, 3.45639000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q321502'),
(41382, 'Custines', 4820, 'GES', 75, 'FR', 48.79127000, 6.14461000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q321502'),
(41383, 'Cusy', 4798, 'ARA', 75, 'FR', 45.76603000, 6.02825000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q321502'),
(41384, 'Cuttoli-Corticchiato', 4806, '20R', 75, 'FR', 41.98333000, 8.91667000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34798121'),
(41385, 'Cuverville', 4804, 'NOR', 75, 'FR', 49.18998000, -0.26474000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34798121'),
(41386, 'Cuxac-Cabardès', 4799, 'OCC', 75, 'FR', 43.37087000, 2.28369000, '2019-10-05 22:47:57', '2020-05-01 17:22:46', 1, 'Q1086439'),
(41387, 'Cuzieu', 4798, 'ARA', 75, 'FR', 45.60764000, 4.25761000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q1086439'),
(41388, 'Cysoing', 4828, 'HDF', 75, 'FR', 50.56939000, 3.21627000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q428594'),
(41389, 'Cébazat', 4798, 'ARA', 75, 'FR', 45.83140000, 3.09992000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q946963'),
(41390, 'Cély', 4796, 'IDF', 75, 'FR', 48.45959000, 2.53245000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, '*********'),
(41391, 'Cénac', 4795, 'NAQ', 75, 'FR', 44.78003000, -0.45999000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, '*********'),
(41392, 'Cénac-et-Saint-Julien', 4795, 'NAQ', 75, 'FR', 44.79968000, 1.20535000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, '*********'),
(41393, 'Cépet', 4799, 'OCC', 75, 'FR', 43.74885000, 1.43168000, '2019-10-05 22:47:57', '2020-05-01 17:22:46', 1, '*********'),
(41394, 'Cérans-Foulletourte', 4802, 'PDL', 75, 'FR', 47.82651000, 0.07724000, '2019-10-05 22:47:57', '2020-05-01 17:22:46', 1, '*********'),
(41395, 'Cérences', 4804, 'NOR', 75, 'FR', 48.91667000, -1.43470000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, '*********'),
(41396, 'Céreste', 4812, 'PAC', 75, 'FR', 43.85580000, 5.58685000, '2019-10-05 22:47:57', '2020-05-01 17:22:46', 1, 'Q865855'),
(41397, 'Cérilly', 4798, 'ARA', 75, 'FR', 46.61791000, 2.82080000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q865855'),
(41398, 'Cérons', 4795, 'NAQ', 75, 'FR', 44.63572000, -0.33351000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, '*********'),
(41399, 'Cézac', 4795, 'NAQ', 75, 'FR', 45.09019000, -0.41963000, '2019-10-05 22:47:57', '2020-05-01 17:22:45', 1, '*********'),
(41400, 'Cézy', 4825, 'BFC', 75, 'FR', 47.99265000, 3.34067000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, '********'),
(41401, 'Dabo', 4820, 'GES', 75, 'FR', 48.65373000, 7.23611000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '********'),
(41402, 'Dachstein', 4820, 'GES', 75, 'FR', 48.56127000, 7.53233000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '********'),
(41403, 'Dadonville', 4818, 'CVL', 75, 'FR', 48.15776000, 2.27150000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, '********'),
(41404, 'Dagneux', 4798, 'ARA', 75, 'FR', 45.85140000, 5.07780000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q241543'),
(41405, 'Dainville', 4828, 'HDF', 75, 'FR', 50.28097000, 2.72603000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q241543'),
(41406, 'Daix', 4825, 'BFC', 75, 'FR', 47.35081000, 5.00052000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q241543'),
(41407, 'Dallet', 4798, 'ARA', 75, 'FR', 45.77047000, 3.23875000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q646289'),
(41408, 'Damazan', 4795, 'NAQ', 75, 'FR', 44.29068000, 0.27694000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q646289'),
(41409, 'Dambach-la-Ville', 4820, 'GES', 75, 'FR', 48.32379000, 7.42547000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q317287'),
(41410, 'Damelevières', 4820, 'GES', 75, 'FR', 48.55930000, 6.38453000, '2019-10-05 22:47:57', '2020-05-01 17:22:44', 1, 'Q317287'),
(41411, 'Damery', 4820, 'GES', 75, 'FR', 49.07238000, 3.88036000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q317287'),
(41412, 'Damgan', 4807, 'BRE', 75, 'FR', 47.51799000, -2.57698000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34800164'),
(41413, 'Damigny', 4804, 'NOR', 75, 'FR', 48.45000000, 0.06667000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34800164'),
(41414, 'Dammarie', 4818, 'CVL', 75, 'FR', 48.34314000, 1.49444000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34800164'),
(41415, 'Dammarie-les-Lys', 4796, 'IDF', 75, 'FR', 48.51667000, 2.65000000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34800203'),
(41416, 'Dammartin-en-Goële', 4796, 'IDF', 75, 'FR', 49.05423000, 2.67777000, '2019-10-05 22:47:57', '2020-05-01 17:22:43', 1, 'Q34800203'),
(41417, 'Damparis', 4825, 'BFC', 75, 'FR', 47.07556000, 5.41398000, '2019-10-05 22:47:57', '2019-10-05 22:47:57', 1, 'Q34800203'),
(41418, 'Dampierre', 4825, 'BFC', 75, 'FR', 47.15498000, 5.74167000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800203'),
(41419, 'Dampierre-en-Burly', 4818, 'CVL', 75, 'FR', 47.76143000, 2.51962000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800203'),
(41420, 'Dampierre-en-Yvelines', 4796, 'IDF', 75, 'FR', 48.70000000, 1.98333000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800225'),
(41421, 'Dampierre-les-Bois', 4825, 'BFC', 75, 'FR', 47.50639000, 6.91279000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800225'),
(41422, 'Dampierre-sur-Salon', 4825, 'BFC', 75, 'FR', 47.55719000, 5.67970000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800225'),
(41423, 'Dampmart', 4796, 'IDF', 75, 'FR', 48.88854000, 2.74095000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800225'),
(41424, 'Damprichard', 4825, 'BFC', 75, 'FR', 47.24413000, 6.88121000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800225'),
(41425, 'Damville', 4804, 'NOR', 75, 'FR', 48.86930000, 1.07458000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800225'),
(41426, 'Dangé-Saint-Romain', 4795, 'NAQ', 75, 'FR', 46.93333000, 0.60000000, '2019-10-05 22:47:58', '2020-05-01 17:22:45', 1, 'Q1147040'),
(41427, 'Danjoutin', 4825, 'BFC', 75, 'FR', 47.61822000, 6.86204000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q1147040'),
(41428, 'Dannemarie', 4820, 'GES', 75, 'FR', 47.63034000, 7.11903000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q1147040'),
(41429, 'Dannemarie-sur-Crête', 4825, 'BFC', 75, 'FR', 47.20488000, 5.87018000, '2019-10-05 22:47:58', '2020-05-01 17:22:44', 1, 'Q1147040'),
(41430, 'Dannes', 4828, 'HDF', 75, 'FR', 50.58799000, 1.61381000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q390379'),
(41431, 'Daoulas', 4807, 'BRE', 75, 'FR', 48.36089000, -4.25977000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q34800409'),
(41432, 'Dardilly', 4798, 'ARA', 75, 'FR', 45.80558000, 4.75319000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q8365'),
(41433, 'Dargnies', 4828, 'HDF', 75, 'FR', 50.04144000, 1.52526000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q8365'),
(41434, 'Darney', 4820, 'GES', 75, 'FR', 48.08660000, 6.04917000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q816498'),
(41435, 'Darnieulles', 4820, 'GES', 75, 'FR', 48.19950000, 6.34929000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q816498'),
(41436, 'Darnétal', 4804, 'NOR', 75, 'FR', 49.44533000, 1.15144000, '2019-10-05 22:47:58', '2020-05-01 17:22:45', 1, '********'),
(41437, 'Darvoy', 4818, 'CVL', 75, 'FR', 47.85839000, 2.10033000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41438, 'Dasle', 4825, 'BFC', 75, 'FR', 47.47843000, 6.89728000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41439, 'Dauendorf', 4820, 'GES', 75, 'FR', 48.82987000, 7.65532000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q21234'),
(41440, 'Daumeray', 4802, 'PDL', 75, 'FR', 47.70070000, -0.36119000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '*********'),
(41441, 'Daux', 4799, 'OCC', 75, 'FR', 43.69506000, 1.26892000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41442, 'Davézieux', 4798, 'ARA', 75, 'FR', 45.25520000, 4.70744000, '2019-10-05 22:47:58', '2020-05-01 17:22:43', 1, '********'),
(41443, 'Dax', 4795, 'NAQ', 75, 'FR', 43.71032000, -1.05366000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41444, 'Deauville', 4804, 'NOR', 75, 'FR', 49.35700000, 0.06995000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q209021'),
(41445, 'Decazeville', 4799, 'OCC', 75, 'FR', 44.56045000, 2.25091000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q591281'),
(41446, 'Dechy', 4828, 'HDF', 75, 'FR', 50.35000000, 3.11667000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q591281'),
(41447, 'Decize', 4825, 'BFC', 75, 'FR', 46.82852000, 3.46192000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q217180'),
(41448, 'Delle', 4825, 'BFC', 75, 'FR', 47.50797000, 6.99975000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q217180'),
(41449, 'Demigny', 4825, 'BFC', 75, 'FR', 46.93048000, 4.83381000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q217180'),
(41450, 'Denain', 4828, 'HDF', 75, 'FR', 50.32930000, 3.39430000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q217180'),
(41451, 'Denguin', 4795, 'NAQ', 75, 'FR', 43.36667000, -0.50000000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q174061'),
(41452, 'Denicé', 4798, 'ARA', 75, 'FR', 46.00158000, 4.64555000, '2019-10-05 22:47:58', '2020-05-01 17:22:43', 1, 'Q174061'),
(41453, 'Denée', 4802, 'PDL', 75, 'FR', 47.37885000, -0.60816000, '2019-10-05 22:47:58', '2020-05-01 17:22:46', 1, '*********'),
(41454, 'Derval', 4802, 'PDL', 75, 'FR', 47.66724000, -1.66990000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '*********'),
(41455, 'Descartes', 4818, 'CVL', 75, 'FR', 46.96667000, 0.70000000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '*********'),
(41456, 'Dessenheim', 4820, 'GES', 75, 'FR', 47.97767000, 7.48891000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '*********'),
(41457, 'Desvres', 4828, 'HDF', 75, 'FR', 50.66884000, 1.83478000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q762554'),
(41458, 'Dettwiller', 4820, 'GES', 75, 'FR', 48.75415000, 7.46633000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q22813'),
(41459, 'Deuil-la-Barre', 4796, 'IDF', 75, 'FR', 48.97674000, 2.32722000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q271654'),
(41460, 'Devecey', 4825, 'BFC', 75, 'FR', 47.32169000, 6.01900000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q271654'),
(41461, 'Deville', 4820, 'GES', 75, 'FR', 49.87893000, 4.70610000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q520698'),
(41462, 'Deyvillers', 4820, 'GES', 75, 'FR', 48.20013000, 6.51533000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q520698'),
(41463, 'Deûlémont', 4828, 'HDF', 75, 'FR', 50.73333000, 2.95000000, '2019-10-05 22:47:58', '2020-05-01 17:22:45', 1, 'Q520698'),
(41464, 'Dhuizon', 4818, 'CVL', 75, 'FR', 47.58723000, 1.65809000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q1135535'),
(41465, 'Didenheim', 4820, 'GES', 75, 'FR', 47.71866000, 7.30157000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q1135535'),
(41466, 'Die', 4798, 'ARA', 75, 'FR', 44.75360000, 5.37033000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q209773'),
(41467, 'Diebling', 4820, 'GES', 75, 'FR', 49.10966000, 6.93974000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q209773'),
(41468, 'Diemeringen', 4820, 'GES', 75, 'FR', 48.94013000, 7.18839000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q22392'),
(41469, 'Dieppe', 4804, 'NOR', 75, 'FR', 49.92160000, 1.07772000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q183557'),
(41470, 'Diesen', 4820, 'GES', 75, 'FR', 49.17818000, 6.67798000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q183557'),
(41471, 'Dietwiller', 4820, 'GES', 75, 'FR', 47.69278000, 7.40300000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q452464'),
(41472, 'Dieue-sur-Meuse', 4820, 'GES', 75, 'FR', 49.07200000, 5.42248000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q740367'),
(41473, 'Dieulefit', 4798, 'ARA', 75, 'FR', 44.52563000, 5.06180000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q325414'),
(41474, 'Dieulouard', 4820, 'GES', 75, 'FR', 48.84137000, 6.06782000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q325414'),
(41475, 'Dieuze', 4820, 'GES', 75, 'FR', 48.81263000, 6.71780000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q22695'),
(41476, 'Diges', 4825, 'BFC', 75, 'FR', 47.72862000, 3.39786000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q22695'),
(41477, 'Dignac', 4795, 'NAQ', 75, 'FR', 45.55000000, 0.28333000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q1599430'),
(41478, 'Digne-les-Bains', 4812, 'PAC', 75, 'FR', 44.09252000, 6.23199000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q181306'),
(41479, 'Digoin', 4825, 'BFC', 75, 'FR', 46.48124000, 3.97946000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q475418'),
(41480, 'Digosville', 4804, 'NOR', 75, 'FR', 49.63104000, -1.52627000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q475418'),
(41481, 'Dijon', 4825, 'BFC', 75, 'FR', 47.31667000, 5.01667000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q7003'),
(41482, 'Dinan', 4807, 'BRE', 75, 'FR', 48.45551000, -2.05019000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q201311'),
(41483, 'Dinard', 4807, 'BRE', 75, 'FR', 48.63143000, -2.06144000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q131600'),
(41484, 'Dingsheim', 4820, 'GES', 75, 'FR', 48.63053000, 7.66932000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q131600'),
(41485, 'Dingy-Saint-Clair', 4798, 'ARA', 75, 'FR', 45.90664000, 6.22554000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q131600'),
(41486, 'Dingé', 4807, 'BRE', 75, 'FR', 48.35702000, -1.71489000, '2019-10-05 22:47:58', '2020-05-01 17:22:44', 1, 'Q34801864'),
(41487, 'Dinsheim-sur-Bruche', 4820, 'GES', 75, 'FR', 48.54258000, 7.42727000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q21244'),
(41488, 'Dinéault', 4807, 'BRE', 75, 'FR', 48.21922000, -4.16411000, '2019-10-05 22:47:58', '2020-05-01 17:22:44', 1, 'Q221300'),
(41489, 'Diou', 4798, 'ARA', 75, 'FR', 46.53432000, 3.74453000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q221300'),
(41490, 'Dirac', 4795, 'NAQ', 75, 'FR', 45.60000000, 0.25000000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q221300'),
(41491, 'Dirinon', 4807, 'BRE', 75, 'FR', 48.39775000, -4.27017000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q221287'),
(41492, 'Dissay', 4795, 'NAQ', 75, 'FR', 46.70007000, 0.43311000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41493, 'Distroff', 4820, 'GES', 75, 'FR', 49.33295000, 6.26662000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41494, 'Distré', 4802, 'PDL', 75, 'FR', 47.22281000, -0.11071000, '2019-10-05 22:47:58', '2020-05-01 17:22:46', 1, '*********'),
(41495, 'Dives-sur-Mer', 4804, 'NOR', 75, 'FR', 49.28176000, -0.10125000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41496, 'Divion', 4828, 'HDF', 75, 'FR', 50.47169000, 2.50546000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41497, 'Divonne-les-Bains', 4798, 'ARA', 75, 'FR', 46.35710000, 6.13494000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41498, 'Dizy', 4820, 'GES', 75, 'FR', 49.06667000, 3.96667000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41499, 'Diémoz', 4798, 'ARA', 75, 'FR', 45.59099000, 5.08884000, '2019-10-05 22:47:58', '2020-05-01 17:22:43', 1, 'Q536728'),
(41500, 'Docelles', 4820, 'GES', 75, 'FR', 48.14475000, 6.61289000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41501, 'Dogneville', 4820, 'GES', 75, 'FR', 48.22251000, 6.45944000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41502, 'Doingt', 4828, 'HDF', 75, 'FR', 49.92113000, 2.96766000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41503, 'Dol-de-Bretagne', 4807, 'BRE', 75, 'FR', 48.54765000, -1.75018000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q536728'),
(41504, 'Dole', 4825, 'BFC', 75, 'FR', 47.09225000, 5.48966000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q205586'),
(41505, 'Dollon', 4802, 'PDL', 75, 'FR', 48.03879000, 0.58686000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q205586'),
(41506, 'Dolomieu', 4798, 'ARA', 75, 'FR', 45.60540000, 5.48571000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q205586'),
(41507, 'Dolus-d\'Oléron', 4795, 'NAQ', 75, 'FR', 45.91667000, -1.26667000, '2019-10-05 22:47:58', '2020-05-01 17:22:45', 1, 'Q205586'),
(41508, 'Dom-le-Mesnil', 4820, 'GES', 75, 'FR', 49.69012000, 4.80363000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q205586'),
(41509, 'Domagné', 4807, 'BRE', 75, 'FR', 48.07081000, -1.39291000, '2019-10-05 22:47:58', '2020-05-01 17:22:44', 1, 'Q378633'),
(41510, 'Domalain', 4807, 'BRE', 75, 'FR', 47.99617000, -1.24250000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41511, 'Domancy', 4798, 'ARA', 75, 'FR', 45.91205000, 6.65271000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41512, 'Domarin', 4798, 'ARA', 75, 'FR', 45.58662000, 5.24599000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41513, 'Domart-en-Ponthieu', 4828, 'HDF', 75, 'FR', 50.07437000, 2.12596000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41514, 'Dombasle-sur-Meurthe', 4820, 'GES', 75, 'FR', 48.61861000, 6.35538000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41515, 'Domessin', 4798, 'ARA', 75, 'FR', 45.54739000, 5.70495000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41516, 'Domfront', 4804, 'NOR', 75, 'FR', 48.59208000, -0.64588000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41517, 'Domgermain', 4820, 'GES', 75, 'FR', 48.64276000, 5.82957000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q769276'),
(41518, 'Dommartin-lès-Remiremont', 4820, 'GES', 75, 'FR', 47.99902000, 6.64048000, '2019-10-05 22:47:58', '2020-05-01 17:22:44', 1, 'Q833820'),
(41519, 'Dommartin-lès-Toul', 4820, 'GES', 75, 'FR', 48.66949000, 5.91005000, '2019-10-05 22:47:58', '2020-05-01 17:22:44', 1, 'Q922611'),
(41520, 'Domme', 4795, 'NAQ', 75, 'FR', 44.80218000, 1.21459000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q922611'),
(41521, 'Domont', 4796, 'IDF', 75, 'FR', 49.02782000, 2.32638000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41522, 'Dompierre-sur-Besbre', 4798, 'ARA', 75, 'FR', 46.52214000, 3.68106000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q243724'),
(41523, 'Dompierre-sur-Mer', 4795, 'NAQ', 75, 'FR', 46.18817000, -1.06351000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q59701'),
(41524, 'Dompierre-sur-Veyle', 4798, 'ARA', 75, 'FR', 46.07108000, 5.20232000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q59701'),
(41525, 'Dompierre-sur-Yon', 4802, 'PDL', 75, 'FR', 46.73857000, -1.38463000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q59701'),
(41526, 'Domène', 4798, 'ARA', 75, 'FR', 45.20289000, 5.83335000, '2019-10-05 22:47:58', '2020-05-01 17:22:43', 1, 'Q59701'),
(41527, 'Domérat', 4798, 'ARA', 75, 'FR', 46.36036000, 2.53455000, '2019-10-05 22:47:58', '2020-05-01 17:22:43', 1, 'Q59701'),
(41528, 'Don', 4828, 'HDF', 75, 'FR', 50.54912000, 2.91817000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q59701'),
(41529, 'Donchery', 4820, 'GES', 75, 'FR', 49.69584000, 4.87417000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q59701'),
(41530, 'Doncourt-lès-Conflans', 4820, 'GES', 75, 'FR', 49.14250000, 5.93368000, '2019-10-05 22:47:58', '2020-05-01 17:22:44', 1, 'Q59701'),
(41531, 'Donges', 4802, 'PDL', 75, 'FR', 47.31824000, -2.07538000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q693048'),
(41532, 'Donnemarie-Dontilly', 4796, 'IDF', 75, 'FR', 48.47719000, 3.13162000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q243183'),
(41533, 'Donnery', 4818, 'CVL', 75, 'FR', 47.91486000, 2.10299000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41534, 'Donville-les-Bains', 4804, 'NOR', 75, 'FR', 48.84608000, -1.58315000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, '********'),
(41535, 'Donzenac', 4795, 'NAQ', 75, 'FR', 45.22731000, 1.52400000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q176365'),
(41536, 'Donzy', 4825, 'BFC', 75, 'FR', 47.37066000, 3.12548000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q674674'),
(41537, 'Donzère', 4798, 'ARA', 75, 'FR', 44.44246000, 4.71134000, '2019-10-05 22:47:58', '2020-05-01 17:22:43', 1, 'Q817843'),
(41538, 'Dordives', 4818, 'CVL', 75, 'FR', 48.14253000, 2.76775000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q817843'),
(41539, 'Dordogne', 4795, 'NAQ', 75, 'FR', 45.12735000, 0.73504000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q3357'),
(41540, 'Dorlisheim', 4820, 'GES', 75, 'FR', 48.52485000, 7.48624000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q3357'),
(41541, 'Dormans', 4820, 'GES', 75, 'FR', 49.07392000, 3.63819000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q3357'),
(41542, 'Dornes', 4825, 'BFC', 75, 'FR', 46.71600000, 3.35343000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q3357'),
(41543, 'Dortan', 4798, 'ARA', 75, 'FR', 46.31973000, 5.66028000, '2019-10-05 22:47:58', '2019-10-05 22:47:58', 1, 'Q271081'),
(41544, 'Dossenheim-sur-Zinsel', 4820, 'GES', 75, 'FR', 48.80590000, 7.40273000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q21241'),
(41545, 'Douai', 4828, 'HDF', 75, 'FR', 50.37069000, 3.07922000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q193826'),
(41546, 'Douarnenez', 4807, 'BRE', 75, 'FR', 48.09542000, -4.32904000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q193826'),
(41547, 'Doubs', 4825, 'BFC', 75, 'FR', 47.19967000, 6.43487000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q3361'),
(41548, 'Douchy', 4818, 'CVL', 75, 'FR', 47.94282000, 3.05392000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q3361'),
(41549, 'Douchy-les-Mines', 4828, 'HDF', 75, 'FR', 50.30080000, 3.39270000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q922518'),
(41550, 'Doudeville', 4804, 'NOR', 75, 'FR', 49.72268000, 0.78479000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q922518'),
(41551, 'Doue', 4796, 'IDF', 75, 'FR', 48.86641000, 3.16269000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q1416679'),
(41552, 'Doulaincourt-Saucourt', 4820, 'GES', 75, 'FR', 48.31667000, 5.20000000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q1416679'),
(41553, 'Doullens', 4828, 'HDF', 75, 'FR', 50.15724000, 2.34019000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q69055'),
(41554, 'Dourdan', 4796, 'IDF', 75, 'FR', 48.52772000, 2.01113000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q646609'),
(41555, 'Dourges', 4828, 'HDF', 75, 'FR', 50.43636000, 2.98589000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q841275'),
(41556, 'Dourgne', 4799, 'OCC', 75, 'FR', 43.48381000, 2.13989000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q841275'),
(41557, 'Doussard', 4798, 'ARA', 75, 'FR', 45.77512000, 6.22553000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q736359'),
(41558, 'Douvaine', 4798, 'ARA', 75, 'FR', 46.30544000, 6.30375000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q736359'),
(41559, 'Douvres-la-Délivrande', 4804, 'NOR', 75, 'FR', 49.29472000, -0.38039000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q736359'),
(41560, 'Douvrin', 4828, 'HDF', 75, 'FR', 50.50916000, 2.83053000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q854870'),
(41561, 'Douzy', 4820, 'GES', 75, 'FR', 49.67080000, 5.04156000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q854870'),
(41562, 'Doué-la-Fontaine', 4802, 'PDL', 75, 'FR', 47.19372000, -0.27492000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, '********'),
(41563, 'Doyet', 4798, 'ARA', 75, 'FR', 46.33558000, 2.79718000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, '********'),
(41564, 'Dozulé', 4804, 'NOR', 75, 'FR', 49.23132000, -0.04454000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q840210'),
(41565, 'Dracy-le-Fort', 4825, 'BFC', 75, 'FR', 46.79750000, 4.76215000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q840210'),
(41566, 'Draguignan', 4812, 'PAC', 75, 'FR', 43.53692000, 6.46458000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q194141'),
(41567, 'Drain', 4802, 'PDL', 75, 'FR', 47.33655000, -1.20773000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q194141'),
(41568, 'Drancy', 4796, 'IDF', 75, 'FR', 48.92578000, 2.44533000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q194141'),
(41569, 'Drap', 4812, 'PAC', 75, 'FR', 43.75508000, 7.32152000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q194141'),
(41570, 'Draveil', 4796, 'IDF', 75, 'FR', 48.68466000, 2.41418000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q242524'),
(41571, 'Drefféac', 4802, 'PDL', 75, 'FR', 47.47464000, -2.05774000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q942968'),
(41572, 'Dreslincourt', 4828, 'HDF', 75, 'FR', 49.52809000, 2.92699000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q942968'),
(41573, 'Dreuil-lès-Amiens', 4828, 'HDF', 75, 'FR', 49.91483000, 2.22893000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q942968'),
(41574, 'Dreux', 4818, 'CVL', 75, 'FR', 48.73649000, 1.36566000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q142410'),
(41575, 'Drocourt', 4828, 'HDF', 75, 'FR', 50.39002000, 2.92425000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q142410'),
(41576, 'Droue-sur-Drouette', 4818, 'CVL', 75, 'FR', 48.60053000, 1.70113000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, '********'),
(41577, 'Droué', 4818, 'CVL', 75, 'FR', 48.04023000, 1.07534000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, '********'),
(41578, 'Druelle', 4799, 'OCC', 75, 'FR', 44.36006000, 2.50505000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, '********'),
(41579, 'Drulingen', 4820, 'GES', 75, 'FR', 48.86804000, 7.18956000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, '********'),
(41580, 'Drumettaz', 4798, 'ARA', 75, 'FR', 45.66136000, 5.92191000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q734567'),
(41581, 'Drusenheim', 4820, 'GES', 75, 'FR', 48.76207000, 7.95326000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q734567'),
(41582, 'Dry', 4818, 'CVL', 75, 'FR', 47.79612000, 1.71330000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q1470363'),
(41583, 'Drémil-Lafage', 4799, 'OCC', 75, 'FR', 43.59624000, 1.60117000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q1470363'),
(41584, 'Ducey', 4804, 'NOR', 75, 'FR', 48.61843000, -1.29052000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q168626'),
(41585, 'Duclair', 4804, 'NOR', 75, 'FR', 49.48385000, 0.87617000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q1174207'),
(41586, 'Dugny', 4796, 'IDF', 75, 'FR', 48.95371000, 2.41734000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q738458'),
(41587, 'Dugny-sur-Meuse', 4820, 'GES', 75, 'FR', 49.10953000, 5.38550000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q1617753'),
(41588, 'Duisans', 4828, 'HDF', 75, 'FR', 50.30633000, 2.68653000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q1617753'),
(41589, 'Dun-le-Palestel', 4795, 'NAQ', 75, 'FR', 46.30000000, 1.66667000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q1617753'),
(41590, 'Dun-sur-Auron', 4818, 'CVL', 75, 'FR', 46.88490000, 2.57345000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q817409'),
(41591, 'Dunières', 4798, 'ARA', 75, 'FR', 45.21558000, 4.34471000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q817409'),
(41592, 'Dunkerque', 4828, 'HDF', 75, 'FR', 51.03297000, 2.37700000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q45797'),
(41593, 'Duppigheim', 4820, 'GES', 75, 'FR', 48.52866000, 7.59421000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q22779'),
(41594, 'Duras', 4795, 'NAQ', 75, 'FR', 44.67618000, 0.18247000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q22779'),
(41595, 'Durrenbach', 4820, 'GES', 75, 'FR', 48.89681000, 7.76769000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q22779'),
(41596, 'Durtal', 4802, 'PDL', 75, 'FR', 47.67247000, -0.23393000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q819837'),
(41597, 'Durtol', 4798, 'ARA', 75, 'FR', 45.79621000, 3.05156000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q819837'),
(41598, 'Dury', 4828, 'HDF', 75, 'FR', 49.84731000, 2.27070000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q819837'),
(41599, 'Duttlenheim', 4820, 'GES', 75, 'FR', 48.52553000, 7.56572000, '2019-10-05 22:47:59', '2019-10-05 22:47:59', 1, 'Q819837'),
(41600, 'Décines-Charpieu', 4798, 'ARA', 75, 'FR', 45.76873000, 4.95883000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, '********'),
(41601, 'Démouville', 4804, 'NOR', 75, 'FR', 49.18001000, -0.26947000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, '********'),
(41602, 'Déols', 4818, 'CVL', 75, 'FR', 46.82934000, 1.70428000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q473103'),
(41603, 'Département d\'Eure-et-Loir', 4818, 'CVL', 75, 'FR', 48.50000000, 1.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q3377'),
(41604, 'Département d\'Ille-et-Vilaine', 4807, 'BRE', 75, 'FR', 48.16667000, -1.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12549'),
(41605, 'Département d\'Indre-et-Loire', 4818, 'CVL', 75, 'FR', 47.25000000, 0.75000000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12556'),
(41606, 'Département de Maine-et-Loire', 4802, 'PDL', 75, 'FR', 47.50000000, -0.33333000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q12584'),
(41607, 'Département de Saône-et-Loire', 4825, 'BFC', 75, 'FR', 46.63646000, 4.58819000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12736'),
(41608, 'Département de l\'Ain', 4798, 'ARA', 75, 'FR', 46.16667000, 5.33333000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q3083'),
(41609, 'Département de l\'Aisne', 4828, 'HDF', 75, 'FR', 49.50000000, 3.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q3093'),
(41610, 'Département de l\'Allier', 4798, 'ARA', 75, 'FR', 46.50000000, 3.00000000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q3113'),
(41611, 'Département de l\'Ardèche', 4798, 'ARA', 75, 'FR', 44.66667000, 4.33333000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q3148'),
(41612, 'Département de l\'Ariège', 4799, 'OCC', 75, 'FR', 43.00000000, 1.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q3184'),
(41613, 'Département de l\'Aube', 4820, 'GES', 75, 'FR', 48.25000000, 4.08333000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q3194'),
(41614, 'Département de l\'Aude', 4799, 'OCC', 75, 'FR', 43.08333000, 2.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q3207'),
(41615, 'Département de l\'Aveyron', 4799, 'OCC', 75, 'FR', 44.25000000, 2.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q3216'),
(41616, 'Département de l\'Essonne', 4796, 'IDF', 75, 'FR', 48.50000000, 2.25000000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q3368'),
(41617, 'Département de l\'Eure', 4804, 'NOR', 75, 'FR', 49.16667000, 1.00000000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q3372'),
(41618, 'Département de l\'Hérault', 4799, 'OCC', 75, 'FR', 43.66667000, 3.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q12545'),
(41619, 'Département de l\'Indre', 4818, 'CVL', 75, 'FR', 46.83333000, 1.66667000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12553'),
(41620, 'Département de l\'Isère', 4798, 'ARA', 75, 'FR', 45.16667000, 5.83333000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q12559'),
(41621, 'Département de l\'Oise', 4828, 'HDF', 75, 'FR', 49.50000000, 2.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q12675'),
(41622, 'Département de l\'Orne', 4804, 'NOR', 75, 'FR', 48.66667000, 0.08333000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q12679'),
(41623, 'Département de l\'Yonne', 4825, 'BFC', 75, 'FR', 47.91667000, 3.75000000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12816'),
(41624, 'Département de la Corrèze', 4795, 'NAQ', 75, 'FR', 45.34754000, 1.87319000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q3326'),
(41626, 'Département de la Côte-d\'Or', 4825, 'BFC', 75, 'FR', 47.50000000, 4.83333000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q3342'),
(41627, 'Département de la Drôme', 4798, 'ARA', 75, 'FR', 44.69971000, 5.16521000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q3364'),
(41629, 'Département de la Haute-Saône', 4825, 'BFC', 75, 'FR', 47.67625000, 6.10066000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12730'),
(41630, 'Département de la Loire', 4798, 'ARA', 75, 'FR', 45.47169000, 4.43298000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q12569'),
(41631, 'Département de la Lozère', 4799, 'OCC', 75, 'FR', 44.50000000, 3.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q12580'),
(41632, 'Département de la Nièvre', 4825, 'BFC', 75, 'FR', 47.11695000, 3.49192000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12657'),
(41633, 'Département de la Vendée', 4802, 'PDL', 75, 'FR', 46.64169000, -1.30407000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q12798'),
(41634, 'Département des Bouches-du-Rhône', 4812, 'PAC', 75, 'FR', 43.52366000, 5.42450000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q3240'),
(41635, 'Département des Côtes-d’Armor', 4807, 'BRE', 75, 'FR', 48.34295000, -2.78216000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q3349'),
(41636, 'Département des Deux-Sèvres', 4795, 'NAQ', 75, 'FR', 46.53918000, -0.30838000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q12765'),
(41637, 'Département des Hautes-Pyrénées', 4799, 'OCC', 75, 'FR', 43.03686000, 0.18632000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q12700'),
(41638, 'Département des Pyrénées-Atlantiques', 4795, 'NAQ', 75, 'FR', 43.23587000, -0.81642000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q12703'),
(41639, 'Département des Pyrénées-Orientales', 4799, 'OCC', 75, 'FR', 42.60075000, 2.58889000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q12709'),
(41640, 'Département du Finistère', 4807, 'BRE', 75, 'FR', 48.25224000, -3.99263000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q3389'),
(41641, 'Département du Loir-et-Cher', 4818, 'CVL', 75, 'FR', 47.50000000, 1.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:44', 1, 'Q12564'),
(41642, 'Département du Lot-et-Garonne', 4795, 'NAQ', 75, 'FR', 44.33333000, 0.50000000, '2019-10-05 22:47:59', '2020-05-01 17:22:45', 1, 'Q12578'),
(41643, 'Département du Puy-de-Dôme', 4798, 'ARA', 75, 'FR', 45.70549000, 3.14600000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q12694'),
(41644, 'Département du Rhône', 4798, 'ARA', 75, 'FR', 45.89126000, 4.53039000, '2019-10-05 22:47:59', '2020-05-01 17:22:43', 1, 'Q46130'),
(41645, 'Département du Tarn-et-Garonne', 4799, 'OCC', 75, 'FR', 44.00000000, 1.16667000, '2019-10-05 22:47:59', '2020-05-01 17:22:46', 1, 'Q12779'),
(41646, 'Département du Val-d’Oise', 4796, 'IDF', 75, 'FR', 49.07891000, 2.17673000, '2019-10-05 22:48:00', '2020-05-01 17:22:43', 1, 'Q12784'),
(41647, 'Département du Vaucluse', 4812, 'PAC', 75, 'FR', 44.00000000, 5.16667000, '2019-10-05 22:48:00', '2020-05-01 17:22:46', 1, 'Q12792'),
(41648, 'Désaignes', 4798, 'ARA', 75, 'FR', 44.99466000, 4.51695000, '2019-10-05 22:48:00', '2020-05-01 17:22:43', 1, 'Q12792'),
(41649, 'Désertines', 4798, 'ARA', 75, 'FR', 46.35456000, 2.61924000, '2019-10-05 22:48:00', '2020-05-01 17:22:43', 1, 'Q12792'),
(41650, 'Déville-lès-Rouen', 4804, 'NOR', 75, 'FR', 49.46942000, 1.05214000, '2019-10-05 22:48:00', '2020-05-01 17:22:45', 1, 'Q12792'),
(41651, 'Eaubonne', 4796, 'IDF', 75, 'FR', 48.99712000, 2.28249000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q240771'),
(41652, 'Eaunes', 4799, 'OCC', 75, 'FR', 43.42079000, 1.35397000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q781891'),
(41653, 'Eauze', 4799, 'OCC', 75, 'FR', 43.86055000, 0.10199000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q781891'),
(41654, 'Ebersheim', 4820, 'GES', 75, 'FR', 48.30438000, 7.49903000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q781891'),
(41655, 'Eckbolsheim', 4820, 'GES', 75, 'FR', 48.58075000, 7.68768000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q21347'),
(41656, 'Eckwersheim', 4820, 'GES', 75, 'FR', 48.68167000, 7.69687000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q21347'),
(41657, 'Ecques', 4828, 'HDF', 75, 'FR', 50.66998000, 2.28633000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q21347'),
(41658, 'Ecquevilly', 4796, 'IDF', 75, 'FR', 48.95192000, 1.92338000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q273975'),
(41659, 'Edern', 4807, 'BRE', 75, 'FR', 48.10000000, -3.98333000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q273975'),
(41660, 'Eguisheim', 4820, 'GES', 75, 'FR', 48.04280000, 7.30617000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q272897'),
(41661, 'Einville-au-Jard', 4820, 'GES', 75, 'FR', 48.65569000, 6.48447000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q272897'),
(41662, 'Elbeuf', 4804, 'NOR', 75, 'FR', 49.28669000, 1.00288000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q733414'),
(41663, 'Elliant', 4807, 'BRE', 75, 'FR', 47.99417000, -3.88966000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q733414'),
(41664, 'Elne', 4799, 'OCC', 75, 'FR', 42.60031000, 2.97146000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q23073'),
(41665, 'Elven', 4807, 'BRE', 75, 'FR', 47.73230000, -2.58956000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q23073'),
(41666, 'Embrun', 4812, 'PAC', 75, 'FR', 44.56387000, 6.49526000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q23073'),
(41667, 'Emmerin', 4828, 'HDF', 75, 'FR', 50.59475000, 3.00124000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q541052'),
(41668, 'Enchenberg', 4820, 'GES', 75, 'FR', 49.01354000, 7.33868000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q541052'),
(41669, 'Enghien-les-Bains', 4796, 'IDF', 75, 'FR', 48.96667000, 2.31667000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q255105'),
(41670, 'Englefontaine', 4828, 'HDF', 75, 'FR', 50.19091000, 3.64401000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q255105'),
(41671, 'Ennery', 4820, 'GES', 75, 'FR', 49.22619000, 6.21778000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q255105'),
(41672, 'Ennery', 4796, 'IDF', 75, 'FR', 49.07505000, 2.10599000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q255105'),
(41673, 'Ennetières-en-Weppes', 4828, 'HDF', 75, 'FR', 50.63530000, 2.94012000, '2019-10-05 22:48:00', '2020-05-01 17:22:45', 1, 'Q255105'),
(41674, 'Ennevelin', 4828, 'HDF', 75, 'FR', 50.54121000, 3.12972000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q255105'),
(41675, 'Ennezat', 4798, 'ARA', 75, 'FR', 45.89772000, 3.22348000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q333441'),
(41676, 'Ensisheim', 4820, 'GES', 75, 'FR', 47.86584000, 7.35052000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q455645'),
(41677, 'Ensuès-la-Redonne', 4812, 'PAC', 75, 'FR', 43.35464000, 5.20357000, '2019-10-05 22:48:00', '2020-05-01 17:22:46', 1, 'Q498224'),
(41678, 'Entraigues-sur-la-Sorgue', 4812, 'PAC', 75, 'FR', 44.00320000, 4.92657000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, '*********'),
(41679, 'Entrammes', 4802, 'PDL', 75, 'FR', 47.99759000, -0.71399000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, '*********'),
(41680, 'Entrange', 4820, 'GES', 75, 'FR', 49.41300000, 6.10501000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, '*********'),
(41681, 'Entraygues-sur-Truyère', 4799, 'OCC', 75, 'FR', 44.64606000, 2.56745000, '2019-10-05 22:48:00', '2020-05-01 17:22:46', 1, '*********'),
(41682, 'Entre-Deux-Guiers', 4798, 'ARA', 75, 'FR', 45.43061000, 5.75209000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, '*********'),
(41683, 'Entzheim', 4820, 'GES', 75, 'FR', 48.53424000, 7.63772000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, '*********'),
(41684, 'Enval', 4798, 'ARA', 75, 'FR', 45.89921000, 3.04981000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, '*********'),
(41685, 'Envermeu', 4804, 'NOR', 75, 'FR', 49.89560000, 1.26493000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q731374'),
(41686, 'Epfig', 4820, 'GES', 75, 'FR', 48.35933000, 7.46427000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q21320'),
(41687, 'Epiniac', 4807, 'BRE', 75, 'FR', 48.50000000, -1.70000000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q21320'),
(41688, 'Eppeville', 4828, 'HDF', 75, 'FR', 49.74069000, 3.05114000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q82064'),
(41689, 'Erbray', 4802, 'PDL', 75, 'FR', 47.65492000, -1.31783000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q82064'),
(41690, 'Erbrée', 4807, 'BRE', 75, 'FR', 48.09745000, -1.12479000, '2019-10-05 22:48:00', '2020-05-01 17:22:44', 1, 'Q82064'),
(41691, 'Ercuis', 4828, 'HDF', 75, 'FR', 49.23465000, 2.30407000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q82064'),
(41692, 'Ercé-en-Lamée', 4807, 'BRE', 75, 'FR', 47.83048000, -1.55867000, '2019-10-05 22:48:00', '2020-05-01 17:22:44', 1, 'Q82064'),
(41693, 'Ercé-près-Liffré', 4807, 'BRE', 75, 'FR', 48.25454000, -1.51560000, '2019-10-05 22:48:00', '2020-05-01 17:22:44', 1, 'Q82064'),
(41694, 'Erdeven', 4807, 'BRE', 75, 'FR', 47.64210000, -3.15706000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q69996'),
(41695, 'Ergué-Gabéric', 4807, 'BRE', 75, 'FR', 47.99562000, -4.02223000, '2019-10-05 22:48:00', '2020-05-01 17:22:44', 1, 'Q868513'),
(41696, 'Ermont', 4796, 'IDF', 75, 'FR', 48.99004000, 2.25804000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q239123'),
(41697, 'Ernolsheim-Bruche', 4820, 'GES', 75, 'FR', 48.56531000, 7.56503000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q239123'),
(41698, 'Ernée', 4802, 'PDL', 75, 'FR', 48.29764000, -0.93143000, '2019-10-05 22:48:00', '2020-05-01 17:22:46', 1, 'Q838741'),
(41699, 'Erquinghem-Lys', 4828, 'HDF', 75, 'FR', 50.67601000, 2.84505000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q838741'),
(41700, 'Erquy', 4807, 'BRE', 75, 'FR', 48.63186000, -2.46280000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q868650'),
(41701, 'Erre', 4828, 'HDF', 75, 'FR', 50.36252000, 3.31561000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q868650'),
(41702, 'Erstein', 4820, 'GES', 75, 'FR', 48.42373000, 7.66262000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q22517'),
(41703, 'Ervy-le-Châtel', 4820, 'GES', 75, 'FR', 48.04116000, 3.90988000, '2019-10-05 22:48:00', '2020-05-01 17:22:44', 1, 'Q22517'),
(41704, 'Esbly', 4796, 'IDF', 75, 'FR', 48.90520000, 2.81235000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q900984'),
(41705, 'Escalquens', 4799, 'OCC', 75, 'FR', 43.51744000, 1.55893000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q900984'),
(41706, 'Escaudain', 4828, 'HDF', 75, 'FR', 50.33315000, 3.34430000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q900984'),
(41707, 'Escaudœuvres', 4828, 'HDF', 75, 'FR', 50.20000000, 3.26667000, '2019-10-05 22:48:00', '2020-05-01 17:22:45', 1, 'Q630749'),
(41708, 'Escautpont', 4828, 'HDF', 75, 'FR', 50.41875000, 3.55341000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1106584'),
(41709, 'Eschau', 4820, 'GES', 75, 'FR', 48.48897000, 7.71644000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1106584'),
(41710, 'Eschbach', 4820, 'GES', 75, 'FR', 48.87465000, 7.73609000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1106584'),
(41711, 'Eschentzwiller', 4820, 'GES', 75, 'FR', 47.71270000, 7.39773000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1106584'),
(41712, 'Esches', 4828, 'HDF', 75, 'FR', 49.22086000, 2.16595000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q511910'),
(41713, 'Escoutoux', 4798, 'ARA', 75, 'FR', 45.81942000, 3.56336000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q511910'),
(41714, 'Eslettes', 4804, 'NOR', 75, 'FR', 49.54820000, 1.05515000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q511910'),
(41715, 'Esnandes', 4795, 'NAQ', 75, 'FR', 46.25033000, -1.11566000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q471624'),
(41716, 'Espalion', 4799, 'OCC', 75, 'FR', 44.52237000, 2.76265000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q326699'),
(41717, 'Espaly-Saint-Marcel', 4798, 'ARA', 75, 'FR', 45.04790000, 3.86557000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q326699'),
(41718, 'Espelette', 4795, 'NAQ', 75, 'FR', 43.34015000, -1.44737000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q236575'),
(41719, 'Espéraza', 4799, 'OCC', 75, 'FR', 42.93225000, 2.22006000, '2019-10-05 22:48:00', '2020-05-01 17:22:46', 1, 'Q609690'),
(41720, 'Esquelbecq', 4828, 'HDF', 75, 'FR', 50.88694000, 2.43215000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q624890'),
(41721, 'Esquerdes', 4828, 'HDF', 75, 'FR', 50.70414000, 2.18851000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q259803'),
(41722, 'Esquibien', 4807, 'BRE', 75, 'FR', 48.02506000, -4.56139000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q259803'),
(41723, 'Esquéhéries', 4828, 'HDF', 75, 'FR', 49.98391000, 3.74743000, '2019-10-05 22:48:00', '2020-05-01 17:22:45', 1, 'Q259803'),
(41724, 'Essars', 4828, 'HDF', 75, 'FR', 50.54868000, 2.66620000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q259803'),
(41725, 'Essert', 4825, 'BFC', 75, 'FR', 47.63342000, 6.81702000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q259803'),
(41726, 'Essey-lès-Nancy', 4820, 'GES', 75, 'FR', 48.70500000, 6.22691000, '2019-10-05 22:48:00', '2020-05-01 17:22:44', 1, 'Q1085872'),
(41727, 'Essigny-le-Grand', 4828, 'HDF', 75, 'FR', 49.77865000, 3.27774000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1085872'),
(41728, 'Essômes-sur-Marne', 4828, 'HDF', 75, 'FR', 49.02881000, 3.37571000, '2019-10-05 22:48:00', '2020-05-01 17:22:45', 1, 'Q1085872'),
(41729, 'Estagel', 4799, 'OCC', 75, 'FR', 42.77314000, 2.69665000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1085872'),
(41730, 'Estaires', 4828, 'HDF', 75, 'FR', 50.64574000, 2.72782000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q732710'),
(41731, 'Esternay', 4820, 'GES', 75, 'FR', 48.73232000, 3.56159000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q732710'),
(41732, 'Estevelles', 4828, 'HDF', 75, 'FR', 50.47653000, 2.90928000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q989662'),
(41733, 'Estillac', 4795, 'NAQ', 75, 'FR', 44.15766000, 0.56383000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q584743'),
(41734, 'Estissac', 4820, 'GES', 75, 'FR', 48.26920000, 3.80515000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q584743'),
(41735, 'Estivareilles', 4798, 'ARA', 75, 'FR', 46.42526000, 2.61872000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q584743'),
(41736, 'Estrablin', 4798, 'ARA', 75, 'FR', 45.51546000, 4.96173000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1069558'),
(41737, 'Estrées', 4828, 'HDF', 75, 'FR', 50.30079000, 3.06980000, '2019-10-05 22:48:00', '2020-05-01 17:22:45', 1, 'Q1069558'),
(41738, 'Estrées-Saint-Denis', 4828, 'HDF', 75, 'FR', 49.42602000, 2.64293000, '2019-10-05 22:48:00', '2020-05-01 17:22:45', 1, 'Q1069558'),
(41739, 'Esvres', 4818, 'CVL', 75, 'FR', 47.28537000, 0.78588000, '2019-10-05 22:48:00', '2019-10-05 22:48:00', 1, 'Q1069558'),
(41740, 'Etaux', 4798, 'ARA', 75, 'FR', 46.06835000, 6.29533000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1069558'),
(41741, 'Etzling', 4820, 'GES', 75, 'FR', 49.17943000, 6.95864000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1069558'),
(41742, 'Eu', 4804, 'NOR', 75, 'FR', 50.04606000, 1.42079000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1069558'),
(41743, 'Eulmont', 4820, 'GES', 75, 'FR', 48.75026000, 6.22731000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1069558'),
(41744, 'Eurre', 4798, 'ARA', 75, 'FR', 44.75825000, 4.98843000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q385700'),
(41745, 'Eurville-Bienville', 4820, 'GES', 75, 'FR', 48.58333000, 5.03333000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1100865'),
(41746, 'Euville', 4820, 'GES', 75, 'FR', 48.75030000, 5.62603000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1100865'),
(41747, 'Excideuil', 4795, 'NAQ', 75, 'FR', 45.33635000, 1.04754000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1011687'),
(41748, 'Exideuil', 4795, 'NAQ', 75, 'FR', 45.88639000, 0.67318000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q900620'),
(41749, 'Exincourt', 4825, 'BFC', 75, 'FR', 47.49465000, 6.83169000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q900620'),
(41750, 'Exireuil', 4795, 'NAQ', 75, 'FR', 46.43382000, -0.19251000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q900620'),
(41751, 'Eybens', 4798, 'ARA', 75, 'FR', 45.14771000, 5.75014000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1002915'),
(41752, 'Eygalières', 4812, 'PAC', 75, 'FR', 43.76099000, 4.94968000, '2019-10-05 22:48:01', '2020-05-01 17:22:46', 1, 'Q384239'),
(41753, 'Eyguières', 4812, 'PAC', 75, 'FR', 43.69481000, 5.03131000, '2019-10-05 22:48:01', '2020-05-01 17:22:46', 1, 'Q384438'),
(41754, 'Eymet', 4795, 'NAQ', 75, 'FR', 44.66812000, 0.39961000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q864102'),
(41755, 'Eymoutiers', 4795, 'NAQ', 75, 'FR', 45.73790000, 1.74189000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1078961'),
(41756, 'Eyragues', 4812, 'PAC', 75, 'FR', 43.84103000, 4.84231000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q1078961'),
(41757, 'Eysines', 4795, 'NAQ', 75, 'FR', 44.88352000, -0.64686000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q467370'),
(41758, 'Eyvigues-et-Eybènes', 4795, 'NAQ', 75, 'FR', 44.93333000, 1.35000000, '2019-10-05 22:48:01', '2020-05-01 17:22:45', 1, 'Q467370'),
(41759, 'Eyzin-Pinet', 4798, 'ARA', 75, 'FR', 45.47377000, 4.99845000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q467370'),
(41760, 'Fabrezan', 4799, 'OCC', 75, 'FR', 43.13581000, 2.69814000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q628489'),
(41761, 'Fabrègues', 4799, 'OCC', 75, 'FR', 43.55057000, 3.77637000, '2019-10-05 22:48:01', '2020-05-01 17:22:46', 1, 'Q590659'),
(41762, 'Faches-Thumesnil', 4828, 'HDF', 75, 'FR', 50.58333000, 3.06667000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q640115'),
(41763, 'Fagnières', 4820, 'GES', 75, 'FR', 48.96385000, 4.31692000, '2019-10-05 22:48:01', '2020-05-01 17:22:44', 1, 'Q640115'),
(41764, 'Fains-Véel', 4820, 'GES', 75, 'FR', 48.78333000, 5.13333000, '2019-10-05 22:48:01', '2020-05-01 17:22:44', 1, 'Q640115'),
(41765, 'Falaise', 4804, 'NOR', 75, 'FR', 48.89217000, -0.19527000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q640115'),
(41766, 'Falck', 4820, 'GES', 75, 'FR', 49.22695000, 6.63373000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q640115'),
(41767, 'Falicon', 4812, 'PAC', 75, 'FR', 43.74861000, 7.27856000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q867129'),
(41768, 'Falleron', 4802, 'PDL', 75, 'FR', 46.88160000, -1.70208000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q867129'),
(41769, 'Famars', 4828, 'HDF', 75, 'FR', 50.31743000, 3.51945000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q665618'),
(41770, 'Fameck', 4820, 'GES', 75, 'FR', 49.29895000, 6.10915000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q665618'),
(41771, 'Fampoux', 4828, 'HDF', 75, 'FR', 50.30173000, 2.87310000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q665618'),
(41772, 'Fareins', 4798, 'ARA', 75, 'FR', 46.01913000, 4.76136000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q665618'),
(41773, 'Faremoutiers', 4796, 'IDF', 75, 'FR', 48.79962000, 2.99607000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q665618'),
(41774, 'Fargues-Saint-Hilaire', 4795, 'NAQ', 75, 'FR', 44.82304000, -0.44676000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q457674'),
(41775, 'Farnay', 4798, 'ARA', 75, 'FR', 45.49622000, 4.58311000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q457674'),
(41776, 'Farschviller', 4820, 'GES', 75, 'FR', 49.09406000, 6.89517000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q457674'),
(41777, 'Farébersviller', 4820, 'GES', 75, 'FR', 49.11898000, 6.86914000, '2019-10-05 22:48:01', '2020-05-01 17:22:44', 1, 'Q457674'),
(41778, 'Faulquemont', 4820, 'GES', 75, 'FR', 49.04910000, 6.59732000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q457674'),
(41779, 'Faulx', 4820, 'GES', 75, 'FR', 48.79266000, 6.19554000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q457674'),
(41780, 'Faumont', 4828, 'HDF', 75, 'FR', 50.46017000, 3.13713000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q457674'),
(41781, 'Fauville-en-Caux', 4804, 'NOR', 75, 'FR', 49.65257000, 0.59197000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q457674'),
(41782, 'Faverges', 4798, 'ARA', 75, 'FR', 45.75116000, 6.29151000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q828265'),
(41783, 'Faverges-de-la-Tour', 4798, 'ARA', 75, 'FR', 45.59068000, 5.52136000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q828265'),
(41784, 'Faverney', 4825, 'BFC', 75, 'FR', 47.76713000, 6.10428000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q828265'),
(41785, 'Faverolles-sur-Cher', 4818, 'CVL', 75, 'FR', 47.31732000, 1.19045000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41786, 'Favières', 4796, 'IDF', 75, 'FR', 48.76324000, 2.77470000, '2019-10-05 22:48:01', '2020-05-01 17:22:43', 1, '********'),
(41787, 'Fay-aux-Loges', 4818, 'CVL', 75, 'FR', 47.92724000, 2.14012000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41788, 'Fay-de-Bretagne', 4802, 'PDL', 75, 'FR', 47.41453000, -1.79155000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41789, 'Fayence', 4812, 'PAC', 75, 'FR', 43.62570000, 6.69531000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q687823'),
(41790, 'Fayl-Billot', 4820, 'GES', 75, 'FR', 47.78199000, 5.59917000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q687823'),
(41791, 'Fegersheim', 4820, 'GES', 75, 'FR', 48.49016000, 7.68107000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q687823'),
(41792, 'Feignies', 4828, 'HDF', 75, 'FR', 50.29806000, 3.91534000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q640887'),
(41793, 'Feigères', 4798, 'ARA', 75, 'FR', 46.11228000, 6.07937000, '2019-10-05 22:48:01', '2020-05-01 17:22:43', 1, 'Q640887'),
(41794, 'Feillens', 4798, 'ARA', 75, 'FR', 46.33426000, 4.89146000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q640887'),
(41795, 'Fellering', 4820, 'GES', 75, 'FR', 47.89659000, 6.98552000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q640887'),
(41796, 'Felletin', 4795, 'NAQ', 75, 'FR', 45.88333000, 2.17431000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q201165'),
(41797, 'Fenain', 4828, 'HDF', 75, 'FR', 50.36667000, 3.30000000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q201165'),
(41798, 'Feneu', 4802, 'PDL', 75, 'FR', 47.57211000, -0.59422000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41799, 'Fenouillet', 4799, 'OCC', 75, 'FR', 43.68041000, 1.39200000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41800, 'Fermanville', 4804, 'NOR', 75, 'FR', 49.68673000, -1.46284000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, '********'),
(41801, 'Ferney-Voltaire', 4798, 'ARA', 75, 'FR', 46.25858000, 6.11063000, '2019-10-05 22:48:01', '2019-10-05 22:48:01', 1, 'Q233733');

