INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(29334, 'Staig', 3006, 'B<PERSON>', 82, 'DE', 48.29998000, 9.99138000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q540673'),
(29335, 'Stallwang', 3009, 'BY', 82, 'DE', 48.55838000, 12.23108000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q540673'),
(29336, 'St<PERSON><PERSON>ch', 3009, 'BY', 82, 'DE', 50.14551000, 11.69129000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q73395'),
(29337, 'Stammham', 3009, '<PERSON><PERSON>', 82, '<PERSON>', 48.29604000, 11.86961000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q73395'),
(29338, '<PERSON>ams<PERSON>', 3009, 'BY', 82, 'DE', 49.26755000, 12.53051000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q430752'),
(29339, 'Stapelburg', 3011, 'ST', 82, 'DE', 51.90075000, 10.66240000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q700505'),
(29340, 'Stapelfeld', 3005, 'SH', 82, 'DE', 53.60000000, 10.21667000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q700505'),
(29341, 'Starnberg', 3009, 'BY', 82, 'DE', 48.00193000, 11.34416000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q61936'),
(29342, 'Stauchitz', 3021, 'SN', 82, 'DE', 51.24350000, 13.21437000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q8790'),
(29343, 'Staudach-Egerndach', 3009, 'BY', 82, 'DE', 47.78333000, 12.48333000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q8790'),
(29344, 'Staudernheim', 3019, 'RP', 82, 'DE', 49.77675000, 7.68890000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q646472'),
(29345, 'Staufen', 3006, 'BW', 82, 'DE', 47.75686000, 8.21078000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q646472'),
(29346, 'Staufenberg', 3018, 'HE', 82, 'DE', 50.66197000, 8.73158000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q646472'),
(29347, 'Staßfurt', 3011, 'ST', 82, 'DE', 51.85186000, 11.58508000, '2019-10-05 22:44:50', '2020-05-01 17:22:49', 1, 'Q525409'),
(29348, 'Stedesdorf', 3008, 'NI', 82, 'DE', 53.63333000, 7.66667000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q205619'),
(29349, 'Stedten', 3011, 'ST', 82, 'DE', 51.44308000, 11.69291000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q699210'),
(29350, 'Steeden', 3018, 'HE', 82, 'DE', 50.41539000, 8.12748000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q699210'),
(29351, 'Stegaurach', 3009, 'BY', 82, 'DE', 49.86543000, 10.84385000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q178365'),
(29352, 'Stegen', 3006, 'BW', 82, 'DE', 47.98333000, 7.96667000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q1923887'),
(29353, 'Steglitz', 3010, 'BE', 82, 'DE', 52.45606000, 13.33200000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q700211'),
(29354, 'Steilshoop', 3016, 'HH', 82, 'DE', 53.61028000, 10.05917000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q700211'),
(29355, 'Steimbke', 3008, 'NI', 82, 'DE', 52.65483000, 9.39091000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q652577'),
(29356, 'Steimel', 3019, 'RP', 82, 'DE', 50.61667000, 7.63333000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q648820'),
(29357, 'Stein', 3009, 'BY', 82, 'DE', 49.41581000, 11.01599000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q522045'),
(29358, 'Steina', 3021, 'SN', 82, 'DE', 51.20000000, 14.05000000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q93302'),
(29359, 'Steinach', 3009, 'BY', 82, 'DE', 48.95417000, 12.60709000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q93302'),
(29360, 'Steinach', 3006, 'BW', 82, 'DE', 48.30000000, 8.05000000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q93302'),
(29361, 'Steinach', 3015, 'TH', 82, 'DE', 50.43129000, 11.15909000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q93302'),
(29362, 'Steinau an der Straße', 3018, 'HE', 82, 'DE', 50.31401000, 9.46335000, '2019-10-05 22:44:50', '2020-05-01 17:22:48', 1, 'Q263974'),
(29363, 'Steinbach', 3015, 'TH', 82, 'DE', 50.83321000, 10.36393000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q684626'),
(29364, 'Steinbach', 3009, 'BY', 82, 'DE', 50.15328000, 11.65055000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q684626'),
(29365, 'Steinbach am Taunus', 3018, 'HE', 82, 'DE', 50.16774000, 8.57278000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q622573'),
(29366, 'Steinbach-Hallenberg', 3015, 'TH', 82, 'DE', 50.69624000, 10.56541000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q622573'),
(29367, 'Steinberg', 3005, 'SH', 82, 'DE', 54.76667000, 9.78333000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q622573'),
(29368, 'Steinbergkirche', 3005, 'SH', 82, 'DE', 54.75463000, 9.76069000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q556975'),
(29369, 'Steinen', 3006, 'BW', 82, 'DE', 47.64446000, 7.73914000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q556975'),
(29370, 'Steinenbronn', 3006, 'BW', 82, 'DE', 48.66667000, 9.11667000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q557784'),
(29371, 'Steinfeld', 3009, 'BY', 82, 'DE', 49.95278000, 9.66944000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q557784'),
(29372, 'Steinfeld', 3019, 'RP', 82, 'DE', 49.04833000, 8.03694000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q557784'),
(29373, 'Steinfeld', 3008, 'NI', 82, 'DE', 52.58596000, 8.21417000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q693942'),
(29374, 'Steinfurt', 3017, 'NW', 82, 'DE', 52.15045000, 7.33664000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q16018'),
(29375, 'Steingaden', 3009, 'BY', 82, 'DE', 47.70000000, 10.86667000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q49290661'),
(29376, 'Steinhagen', 3017, 'NW', 82, 'DE', 52.00000000, 8.40000000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q49290661'),
(29377, 'Steinhagen', 3007, 'MV', 82, 'DE', 54.22616000, 12.98867000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q49290661'),
(29378, 'Steinheid', 3015, 'TH', 82, 'DE', 50.46538000, 11.08265000, '2019-10-05 22:44:50', '2019-10-05 22:44:50', 1, 'Q656987'),
(29379, 'Steinheim', 3017, 'NW', 82, 'DE', 51.87066000, 9.09136000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q656987'),
(29380, 'Steinheim', 3009, 'BY', 82, 'DE', 48.01473000, 10.16081000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q656987'),
(29381, 'Steinheim am Albuch', 3006, 'BW', 82, 'DE', 48.69090000, 10.06382000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q2341049'),
(29382, 'Steinheim am der Murr', 3006, 'BW', 82, 'DE', 48.96821000, 9.27708000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q2341049'),
(29383, 'Steinhorst', 3008, 'NI', 82, 'DE', 52.68333000, 10.40000000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q694616'),
(29384, 'Steinhöfel', 3013, 'BB', 82, 'DE', 52.40000000, 14.16667000, '2019-10-05 22:44:51', '2020-05-01 17:22:48', 1, 'Q624100'),
(29385, 'Steinhöring', 3009, 'BY', 82, 'DE', 48.08679000, 12.03140000, '2019-10-05 22:44:51', '2020-05-01 17:22:48', 1, 'Q511780'),
(29386, 'Steinigtwolmsdorf', 3021, 'SN', 82, 'DE', 51.06314000, 14.34540000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q93303'),
(29387, 'Steinkirchen', 3008, 'NI', 82, 'DE', 53.56601000, 9.61111000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q93303'),
(29388, 'Steinkirchen', 3009, 'BY', 82, 'DE', 48.38333000, 12.08333000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q93303'),
(29389, 'Steinmauern', 3006, 'BW', 82, 'DE', 48.90095000, 8.19692000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q81679'),
(29390, 'Steinsfeld', 3009, 'BY', 82, 'DE', 49.41667000, 10.21667000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q504705'),
(29391, 'Steinweiler', 3019, 'RP', 82, 'DE', 49.12111000, 8.14139000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q594298'),
(29392, 'Steinwenden', 3019, 'RP', 82, 'DE', 49.45708000, 7.52726000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q630105'),
(29393, 'Steinwiesen', 3009, 'BY', 82, 'DE', 50.29444000, 11.46295000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q505099'),
(29394, 'Steißlingen', 3006, 'BW', 82, 'DE', 47.80000000, 8.93333000, '2019-10-05 22:44:51', '2020-05-01 17:22:47', 1, 'Q62122'),
(29395, 'Stelle', 3008, 'NI', 82, 'DE', 53.38416000, 10.11138000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q62122'),
(29396, 'Stellingen', 3016, 'HH', 82, 'DE', 53.59220000, 9.92870000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q1621'),
(29397, 'Stelzenberg', 3019, 'RP', 82, 'DE', 49.37756000, 7.73814000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q654676'),
(29398, 'Stendal', 3011, 'ST', 82, 'DE', 52.60690000, 11.85867000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q654676'),
(29399, 'Stephanskirchen', 3009, 'BY', 82, 'DE', 47.85389000, 12.18560000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q532986'),
(29400, 'Stephansposching', 3009, 'BY', 82, 'DE', 48.81667000, 12.80000000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q262333'),
(29401, 'Sternberg', 3007, 'MV', 82, 'DE', 53.71236000, 11.82678000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q262333'),
(29402, 'Sternenfels', 3006, 'BW', 82, 'DE', 49.05139000, 8.85083000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q80526'),
(29403, 'Sterup', 3005, 'SH', 82, 'DE', 54.72650000, 9.73572000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q80526'),
(29404, 'Stetten', 3009, 'BY', 82, 'DE', 48.02382000, 10.44474000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q80526'),
(29405, 'Stetten am Kalten Markt', 3006, 'BW', 82, 'DE', 48.12419000, 9.07775000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q550651'),
(29406, 'Stettfeld', 3009, 'BY', 82, 'DE', 49.97090000, 10.72170000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q525796'),
(29407, 'Steyerberg', 3008, 'NI', 82, 'DE', 52.57017000, 9.02423000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q666695'),
(29408, 'Stiefenhofen', 3009, 'BY', 82, 'DE', 47.59320000, 10.00365000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q504026'),
(29409, 'Stimpfach', 3006, 'BW', 82, 'DE', 49.06135000, 10.09274000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q81084'),
(29410, 'Stockach', 3006, 'BW', 82, 'DE', 47.85105000, 9.00910000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q61752'),
(29411, 'Stockelsdorf', 3005, 'SH', 82, 'DE', 53.89220000, 10.64713000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q551818'),
(29412, 'Stockheim', 3009, 'BY', 82, 'DE', 50.30639000, 11.28172000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q551818'),
(29413, 'Stockstadt am Main', 3009, 'BY', 82, 'DE', 49.97005000, 9.07153000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q504297'),
(29414, 'Stockstadt am Rhein', 3018, 'HE', 82, 'DE', 49.80944000, 8.47278000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q504297'),
(29415, 'Stolberg', 3011, 'ST', 82, 'DE', 51.57426000, 10.95582000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q686386'),
(29416, 'Stolberg', 3017, 'NW', 82, 'DE', 50.77368000, 6.22595000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q4068'),
(29417, 'Stollberg', 3021, 'SN', 82, 'DE', 50.70999000, 12.78034000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q57955'),
(29418, 'Stolpe', 3005, 'SH', 82, 'DE', 54.13333000, 10.21667000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q57955'),
(29419, 'Stolpen', 3021, 'SN', 82, 'DE', 51.04901000, 14.07943000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q8243'),
(29420, 'Stolzenau', 3008, 'NI', 82, 'DE', 52.51667000, 9.06667000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q661656'),
(29421, 'Storkow', 3013, 'BB', 82, 'DE', 52.25662000, 13.93337000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q33496389'),
(29422, 'Stoßdorf', 3017, 'NW', 82, 'DE', 50.78248000, 7.25126000, '2019-10-05 22:44:51', '2020-05-01 17:22:49', 1, 'Q2352469'),
(29423, 'Straelen', 3017, 'NW', 82, 'DE', 51.44190000, 6.26639000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q33496679'),
(29424, 'Stralendorf', 3007, 'MV', 82, 'DE', 53.57498000, 11.30356000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q640892'),
(29425, 'Stralsund', 3007, 'MV', 82, 'DE', 54.30911000, 13.08180000, '2019-10-05 22:44:51', '2019-10-05 22:44:51', 1, 'Q4065'),
(29426, 'Strande', 3005, 'SH', 82, 'DE', 54.43333000, 10.16667000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q561233'),
(29427, 'Strasburg', 3007, 'MV', 82, 'DE', 53.51030000, 13.74513000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q561233'),
(29428, 'Straubing', 3009, 'BY', 82, 'DE', 48.88126000, 12.57385000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q7027'),
(29429, 'Straupitz', 3013, 'BB', 82, 'DE', 51.91357000, 14.12275000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q572581'),
(29430, 'Strausberg', 3013, 'BB', 82, 'DE', 52.57859000, 13.88741000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q490521'),
(29431, 'Straußfurt', 3015, 'TH', 82, 'DE', 51.16667000, 10.98333000, '2019-10-05 22:44:52', '2020-05-01 17:22:50', 1, 'Q530170'),
(29432, 'Straßberg', 3006, 'BW', 82, 'DE', 48.17805000, 9.09059000, '2019-10-05 22:44:52', '2020-05-01 17:22:47', 1, 'Q530170'),
(29433, 'Straßkirchen', 3009, 'BY', 82, 'DE', 48.83071000, 12.72105000, '2019-10-05 22:44:52', '2020-05-01 17:22:48', 1, 'Q31574'),
(29434, 'Straßlach-Dingharting', 3009, 'BY', 82, 'DE', 48.00491000, 11.51410000, '2019-10-05 22:44:52', '2020-05-01 17:22:48', 1, 'Q503326'),
(29435, 'Strehla', 3021, 'SN', 82, 'DE', 51.35248000, 13.22660000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q8792'),
(29436, 'Stromberg', 3019, 'RP', 82, 'DE', 50.45779000, 7.60041000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q8792'),
(29437, 'Strullendorf', 3009, 'BY', 82, 'DE', 49.84429000, 10.97208000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q539907'),
(29438, 'Struppen', 3021, 'SN', 82, 'DE', 50.93333000, 14.01667000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q8244'),
(29439, 'Struvenhütten', 3005, 'SH', 82, 'DE', 53.86667000, 10.05000000, '2019-10-05 22:44:52', '2020-05-01 17:22:50', 1, 'Q8244'),
(29440, 'Ströbeck', 3011, 'ST', 82, 'DE', 51.91406000, 10.94445000, '2019-10-05 22:44:52', '2020-05-01 17:22:49', 1, 'Q697010'),
(29441, 'Stubenberg', 3009, 'BY', 82, 'DE', 48.31667000, 13.08333000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q697010'),
(29442, 'Stuhr', 3008, 'NI', 82, 'DE', 53.03333000, 8.75000000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q697010'),
(29443, 'Stulln', 3009, 'BY', 82, 'DE', 49.41667000, 12.13333000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q545275'),
(29444, 'Stuttgart', 3006, 'BW', 82, 'DE', 48.78232000, 9.17702000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q1022'),
(29445, 'Stuttgart Feuerbach', 3006, 'BW', 82, 'DE', 48.80867000, 9.15719000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q289874'),
(29446, 'Stuttgart Mühlhausen', 3006, 'BW', 82, 'DE', 48.84232000, 9.23028000, '2019-10-05 22:44:52', '2020-05-01 17:22:47', 1, 'Q289874'),
(29447, 'Stuttgart-Ost', 3006, 'BW', 82, 'DE', 48.78363000, 9.21032000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q2359472'),
(29448, 'Stäbelow', 3007, 'MV', 82, 'DE', 54.04119000, 12.02336000, '2019-10-05 22:44:52', '2020-05-01 17:22:49', 1, 'Q690617'),
(29449, 'Stöckse', 3008, 'NI', 82, 'DE', 52.64067000, 9.34027000, '2019-10-05 22:44:52', '2020-05-01 17:22:48', 1, 'Q678670'),
(29450, 'Stödtlen', 3006, 'BW', 82, 'DE', 49.00000000, 10.30000000, '2019-10-05 22:44:52', '2020-05-01 17:22:47', 1, 'Q536448'),
(29451, 'Störnstein', 3009, 'BY', 82, 'DE', 49.73232000, 12.20547000, '2019-10-05 22:44:52', '2020-05-01 17:22:48', 1, 'Q503977'),
(29452, 'Stötten am Auerberg', 3009, 'BY', 82, 'DE', 47.73871000, 10.68881000, '2019-10-05 22:44:52', '2020-05-01 17:22:48', 1, 'Q511941'),
(29453, 'Stöttwang', 3009, 'BY', 82, 'DE', 47.88333000, 10.71667000, '2019-10-05 22:44:52', '2020-05-01 17:22:48', 1, 'Q512055'),
(29454, 'Stößen', 3011, 'ST', 82, 'DE', 51.11440000, 11.92405000, '2019-10-05 22:44:52', '2020-05-01 17:22:49', 1, 'Q550848'),
(29455, 'Stühlingen', 3006, 'BW', 82, 'DE', 47.74580000, 8.44813000, '2019-10-05 22:44:52', '2020-05-01 17:22:47', 1, 'Q527783'),
(29456, 'Stützengrün', 3021, 'SN', 82, 'DE', 50.53333000, 12.53333000, '2019-10-05 22:44:52', '2020-05-01 17:22:49', 1, 'Q57842'),
(29457, 'Stützerbach', 3015, 'TH', 82, 'DE', 50.63333000, 10.86667000, '2019-10-05 22:44:52', '2020-05-01 17:22:50', 1, 'Q627879'),
(29458, 'Suddendorf', 3008, 'NI', 82, 'DE', 52.30096000, 7.22572000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q627879'),
(29459, 'Suderburg', 3008, 'NI', 82, 'DE', 52.89586000, 10.45141000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q604584'),
(29460, 'Sudwalde', 3008, 'NI', 82, 'DE', 52.79337000, 8.83611000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q502826'),
(29461, 'Sugenheim', 3009, 'BY', 82, 'DE', 49.60276000, 10.43563000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q511707'),
(29462, 'Suhl', 3015, 'TH', 82, 'DE', 50.60911000, 10.69401000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q511707'),
(29463, 'Suhlendorf', 3008, 'NI', 82, 'DE', 52.93333000, 10.76667000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q694312'),
(29464, 'Sukow', 3007, 'MV', 82, 'DE', 53.54428000, 11.56194000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q694312'),
(29465, 'Sulingen', 3008, 'NI', 82, 'DE', 52.68373000, 8.80949000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q268485'),
(29466, 'Sulz am Neckar', 3006, 'BW', 82, 'DE', 48.36241000, 8.63309000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q83167'),
(29467, 'Sulzbach', 3020, 'SL', 82, 'DE', 49.29882000, 7.05696000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q83167'),
(29468, 'Sulzbach', 3018, 'HE', 82, 'DE', 50.13396000, 8.52797000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q624417'),
(29469, 'Sulzbach am Main', 3009, 'BY', 82, 'DE', 49.91149000, 9.15315000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q120357'),
(29470, 'Sulzbach an der Murr', 3006, 'BW', 82, 'DE', 49.00303000, 9.50030000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q83048'),
(29471, 'Sulzbach-Rosenberg', 3009, 'BY', 82, 'DE', 49.50126000, 11.74598000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q503633'),
(29472, 'Sulzberg', 3009, 'BY', 82, 'DE', 47.66033000, 10.34991000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q503633'),
(29473, 'Sulzburg', 3006, 'BW', 82, 'DE', 47.84116000, 7.70777000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q505849'),
(29474, 'Sulzdorf', 3009, 'BY', 82, 'DE', 49.65002000, 9.90389000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q505849'),
(29475, 'Sulzemoos', 3009, 'BY', 82, 'DE', 48.29107000, 11.26356000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q524223'),
(29476, 'Sulzfeld', 3009, 'BY', 82, 'DE', 50.25972000, 10.40525000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q524223'),
(29477, 'Sulzfeld', 3006, 'BW', 82, 'DE', 49.10472000, 8.85583000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q524223'),
(29478, 'Sulzfeld am Main', 3009, 'BY', 82, 'DE', 49.70694000, 10.13248000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q506608'),
(29479, 'Sulzheim', 3009, 'BY', 82, 'DE', 49.95000000, 10.33333000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q506608'),
(29480, 'Sulzheim', 3019, 'RP', 82, 'DE', 49.84306000, 8.09167000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q506608'),
(29481, 'Sundern', 3017, 'NW', 82, 'DE', 51.32810000, 8.00369000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q33499550'),
(29482, 'Surberg', 3009, 'BY', 82, 'DE', 47.86667000, 12.70000000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q251557'),
(29483, 'Surwold', 3008, 'NI', 82, 'DE', 52.97800000, 7.51534000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q683965'),
(29484, 'Sustrum', 3008, 'NI', 82, 'DE', 52.91667000, 7.28333000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q672853'),
(29485, 'Swabia', 3009, 'BY', 82, 'DE', 48.33333000, 10.50000000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q10557'),
(29486, 'Syke', 3008, 'NI', 82, 'DE', 52.91345000, 8.82209000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q503751'),
(29487, 'Sylt-Ost', 3005, 'SH', 82, 'DE', 54.86110000, 8.41141000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q697215'),
(29488, 'Syrau', 3021, 'SN', 82, 'DE', 50.54297000, 12.07933000, '2019-10-05 22:44:52', '2019-10-05 22:44:52', 1, 'Q573537'),
(29489, 'Söchtenau', 3009, 'BY', 82, 'DE', 47.93118000, 12.22959000, '2019-10-05 22:44:52', '2020-05-01 17:22:48', 1, 'Q532842'),
(29490, 'Södel', 3018, 'HE', 82, 'DE', 50.39382000, 8.80474000, '2019-10-05 22:44:53', '2020-05-01 17:22:48', 1, 'Q532842'),
(29491, 'Sögel', 3008, 'NI', 82, 'DE', 52.85000000, 7.51667000, '2019-10-05 22:44:53', '2020-05-01 17:22:48', 1, 'Q700831'),
(29492, 'Söhlde', 3008, 'NI', 82, 'DE', 52.18898000, 10.23239000, '2019-10-05 22:44:53', '2020-05-01 17:22:48', 1, 'Q574384'),
(29493, 'Sölden', 3006, 'BW', 82, 'DE', 47.93333000, 7.81667000, '2019-10-05 22:44:53', '2020-05-01 17:22:47', 1, 'Q574384'),
(29494, 'Sömmerda', 3015, 'TH', 82, 'DE', 51.15914000, 11.11524000, '2019-10-05 22:44:53', '2020-05-01 17:22:50', 1, 'Q530794'),
(29495, 'Sörgenloch', 3019, 'RP', 82, 'DE', 49.88306000, 8.20111000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q647797'),
(29496, 'Sörup', 3005, 'SH', 82, 'DE', 54.71667000, 9.66667000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q638417'),
(29497, 'Süderbrarup', 3005, 'SH', 82, 'DE', 54.63333000, 9.78333000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q559153'),
(29498, 'Südergellersen', 3008, 'NI', 82, 'DE', 53.21667000, 10.30000000, '2019-10-05 22:44:53', '2020-05-01 17:22:48', 1, '*********'),
(29499, 'Süderlügum', 3005, 'SH', 82, 'DE', 54.87391000, 8.91111000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q559482'),
(29500, 'Süderstapel', 3005, 'SH', 82, 'DE', 54.34967000, 9.21907000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q556021'),
(29501, 'Südlohn', 3017, 'NW', 82, 'DE', 51.93333000, 6.86667000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, '*********'),
(29502, 'Sülfeld', 3005, 'SH', 82, 'DE', 53.80000000, 10.23333000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q528761'),
(29503, 'Sülstorf', 3007, 'MV', 82, 'DE', 53.50931000, 11.37463000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q691724'),
(29504, 'Sünching', 3009, 'BY', 82, 'DE', 48.87855000, 12.35129000, '2019-10-05 22:44:53', '2020-05-01 17:22:48', 1, 'Q490326'),
(29505, 'Süplingen', 3011, 'ST', 82, 'DE', 52.28759000, 11.32450000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q663684'),
(29506, 'Süpplingen', 3008, 'NI', 82, 'DE', 52.22811000, 10.90393000, '2019-10-05 22:44:53', '2020-05-01 17:22:48', 1, 'Q663684'),
(29507, 'Süsel', 3005, 'SH', 82, 'DE', 54.08135000, 10.70172000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q554068'),
(29508, 'Süstedt', 3008, 'NI', 82, 'DE', 52.86165000, 8.92213000, '2019-10-05 22:44:53', '2020-05-01 17:22:48', 1, 'Q504173'),
(29509, 'Süßen', 3006, 'BW', 82, 'DE', 48.67934000, 9.75534000, '2019-10-05 22:44:53', '2020-05-01 17:22:47', 1, 'Q80849'),
(29510, 'Tabarz', 3015, 'TH', 82, 'DE', 50.87529000, 10.51607000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q309670'),
(29511, 'Tacherting', 3009, 'BY', 82, 'DE', 48.07825000, 12.57008000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q574661'),
(29512, 'Taching am See', 3009, 'BY', 82, 'DE', 47.95942000, 12.72933000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q253804'),
(29513, 'Tagmersheim', 3009, 'BY', 82, 'DE', 48.81667000, 10.96667000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q253804'),
(29514, 'Talheim', 3006, 'BW', 82, 'DE', 49.08333000, 9.19306000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q253804'),
(29515, 'Tambach-Dietharz', 3015, 'TH', 82, 'DE', 50.79245000, 10.61568000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q530007'),
(29516, 'Tamm', 3006, 'BW', 82, 'DE', 48.91988000, 9.11556000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q530007'),
(29517, 'Tangerhütte', 3011, 'ST', 82, 'DE', 52.43530000, 11.80724000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q50859'),
(29518, 'Tangermünde', 3011, 'ST', 82, 'DE', 52.54463000, 11.97647000, '2019-10-05 22:44:53', '2020-05-01 17:22:49', 1, 'Q498565'),
(29519, 'Tangstedt', 3005, 'SH', 82, 'DE', 53.73333000, 10.08333000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q498565'),
(29520, 'Tann', 3018, 'HE', 82, 'DE', 50.64284000, 10.02385000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q585043'),
(29521, 'Tann', 3009, 'BY', 82, 'DE', 48.31463000, 12.89301000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q585601'),
(29522, 'Tanna', 3015, 'TH', 82, 'DE', 50.49460000, 11.85725000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q585601'),
(29523, 'Tannenberg', 3021, 'SN', 82, 'DE', 50.60738000, 12.93798000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q585601'),
(29524, 'Tannenbergsthal', 3021, 'SN', 82, 'DE', 50.43665000, 12.45754000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q699351'),
(29525, 'Tannhausen', 3006, 'BW', 82, 'DE', 48.97870000, 10.36129000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q328684'),
(29526, 'Tannheim', 3006, 'BW', 82, 'DE', 48.00000000, 10.08333000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q328684'),
(29527, 'Tapfheim', 3009, 'BY', 82, 'DE', 48.67322000, 10.68377000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q503123'),
(29528, 'Tappenbeck', 3008, 'NI', 82, 'DE', 52.47055000, 10.74068000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q503123'),
(29529, 'Tarmstedt', 3008, 'NI', 82, 'DE', 53.22505000, 9.07763000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q504760'),
(29530, 'Tarnow', 3007, 'MV', 82, 'DE', 53.77941000, 12.01785000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q504760'),
(29531, 'Tarp', 3005, 'SH', 82, 'DE', 54.66667000, 9.40000000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q504760'),
(29532, 'Tating', 3005, 'SH', 82, 'DE', 54.32607000, 8.70802000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q27313'),
(29533, 'Tauberbischofsheim', 3006, 'BW', 82, 'DE', 49.62472000, 9.66278000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q61724'),
(29534, 'Taucha', 3021, 'SN', 82, 'DE', 51.38333000, 12.48333000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q12050'),
(29535, 'Tauche', 3013, 'BB', 82, 'DE', 52.15000000, 14.16667000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q627082'),
(29536, 'Taufkirchen', 3009, 'BY', 82, 'DE', 48.04860000, 11.61701000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q257018'),
(29537, 'Taunusstein', 3018, 'HE', 82, 'DE', 50.14993000, 8.15206000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q569664'),
(29538, 'Taura', 3021, 'SN', 82, 'DE', 50.91667000, 12.85000000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q71057'),
(29539, 'Tauscha', 3021, 'SN', 82, 'DE', 51.26667000, 13.80000000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q71057'),
(29540, 'Tautenhain', 3015, 'TH', 82, 'DE', 50.92470000, 11.91945000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q71057'),
(29541, 'Tawern', 3019, 'RP', 82, 'DE', 49.66667000, 6.51667000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q547752'),
(29542, 'Tecklenburg', 3017, 'NW', 82, 'DE', 52.21957000, 7.81357000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q33505784'),
(29543, 'Tegel', 3010, 'BE', 82, 'DE', 52.57601000, 13.29389000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q700055'),
(29544, 'Tegernheim', 3009, 'BY', 82, 'DE', 49.02394000, 12.17303000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q490206'),
(29545, 'Tegernsee', 3009, 'BY', 82, 'DE', 47.71230000, 11.75820000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q260130'),
(29546, 'Teicha', 3011, 'ST', 82, 'DE', 51.55887000, 11.95482000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q1984932'),
(29547, 'Teichwolframsdorf', 3015, 'TH', 82, 'DE', 50.72093000, 12.24689000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q83377'),
(29548, 'Teisendorf', 3009, 'BY', 82, 'DE', 47.84921000, 12.81919000, '2019-10-05 22:44:53', '2019-10-05 22:44:53', 1, 'Q681864'),
(29549, 'Teising', 3009, 'BY', 82, 'DE', 48.22721000, 12.61137000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q765374'),
(29550, 'Teisnach', 3009, 'BY', 82, 'DE', 49.04114000, 12.98784000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q488612'),
(29551, 'Teistungen', 3015, 'TH', 82, 'DE', 51.46667000, 10.26667000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q504045'),
(29552, 'Telgte', 3017, 'NW', 82, 'DE', 51.97995000, 7.78293000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q504045'),
(29553, 'Tellingstedt', 3005, 'SH', 82, 'DE', 54.21667000, 9.28333000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q550433'),
(29554, 'Teltow', 3013, 'BB', 82, 'DE', 52.40310000, 13.26014000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q572512'),
(29555, 'Tempelhof', 3010, 'BE', 82, 'DE', 52.46667000, 13.40000000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q363830'),
(29556, 'Templin', 3013, 'BB', 82, 'DE', 53.11865000, 13.50220000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q519157'),
(29557, 'Tengen', 3006, 'BW', 82, 'DE', 47.82128000, 8.66117000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q519157'),
(29558, 'Teningen', 3006, 'BW', 82, 'DE', 48.12952000, 7.81205000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q539862'),
(29559, 'Tennenbronn', 3006, 'BW', 82, 'DE', 48.19041000, 8.35176000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q515678'),
(29560, 'Tespe', 3008, 'NI', 82, 'DE', 53.39640000, 10.41084000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q630594'),
(29561, 'Tessin', 3007, 'MV', 82, 'DE', 54.02764000, 12.46519000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q326057'),
(29562, 'Teterow', 3007, 'MV', 82, 'DE', 53.77545000, 12.57487000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q645748'),
(29563, 'Tettau', 3009, 'BY', 82, 'DE', 50.46979000, 11.25888000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q504364'),
(29564, 'Tettenweis', 3009, 'BY', 82, 'DE', 48.44281000, 13.26955000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q506272'),
(29565, 'Tettnang', 3006, 'BW', 82, 'DE', 47.66857000, 9.59132000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q506272'),
(29566, 'Teublitz', 3009, 'BY', 82, 'DE', 49.22289000, 12.08727000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q507731'),
(29567, 'Teuchel', 3011, 'ST', 82, 'DE', 51.88842000, 12.64587000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q507731'),
(29568, 'Teuchern', 3011, 'ST', 82, 'DE', 51.12094000, 12.02410000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q534715'),
(29569, 'Teugn', 3009, 'BY', 82, 'DE', 48.89299000, 12.01175000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q251915'),
(29570, 'Teunz', 3009, 'BY', 82, 'DE', 49.48333000, 12.38333000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q547100'),
(29571, 'Teupitz', 3013, 'BB', 82, 'DE', 52.12967000, 13.61960000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q569554'),
(29572, 'Teuschnitz', 3009, 'BY', 82, 'DE', 50.39839000, 11.38235000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q502604'),
(29573, 'Teutschenthal', 3011, 'ST', 82, 'DE', 51.45000000, 11.80000000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q538873'),
(29574, 'Thal', 3015, 'TH', 82, 'DE', 50.91684000, 10.39209000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q538873'),
(29575, 'Thale', 3011, 'ST', 82, 'DE', 51.74861000, 11.04100000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q506460'),
(29576, 'Thaleischweiler-Fröschen', 3019, 'RP', 82, 'DE', 49.26667000, 7.58333000, '2019-10-05 22:44:54', '2020-05-01 17:22:49', 1, 'Q656128'),
(29577, 'Thalfang', 3019, 'RP', 82, 'DE', 49.75000000, 7.00000000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q659866'),
(29578, 'Thalheim', 3011, 'ST', 82, 'DE', 51.65268000, 12.22756000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q685220'),
(29579, 'Thalheim', 3021, 'SN', 82, 'DE', 50.70077000, 12.84996000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q57993'),
(29580, 'Thallwitz', 3021, 'SN', 82, 'DE', 51.43333000, 12.68333000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q10759'),
(29581, 'Thalmassing', 3009, 'BY', 82, 'DE', 48.91167000, 12.15500000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q491653'),
(29583, 'Thannhausen', 3009, 'BY', 82, 'DE', 48.28334000, 10.46917000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q527361'),
(29584, 'Thanstein', 3009, 'BY', 82, 'DE', 49.38333000, 12.46667000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q546475'),
(29585, 'Tharandt', 3021, 'SN', 82, 'DE', 50.98525000, 13.58035000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q8245'),
(29586, 'Thedinghausen', 3008, 'NI', 82, 'DE', 52.96667000, 9.01667000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q503404'),
(29587, 'Theilheim', 3009, 'BY', 82, 'DE', 49.75403000, 10.03056000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q508367'),
(29588, 'Theisseil', 3009, 'BY', 82, 'DE', 49.69034000, 12.22744000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q503790'),
(29589, 'Theißen', 3011, 'ST', 82, 'DE', 51.08748000, 12.10659000, '2019-10-05 22:44:54', '2020-05-01 17:22:49', 1, 'Q701856'),
(29590, 'Themar', 3015, 'TH', 82, 'DE', 50.50465000, 10.61536000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q518346'),
(29591, 'Theuma', 3021, 'SN', 82, 'DE', 50.47007000, 12.22195000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q46804'),
(29592, 'Thiendorf', 3021, 'SN', 82, 'DE', 51.29375000, 13.74124000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q46804'),
(29593, 'Thierhaupten', 3009, 'BY', 82, 'DE', 48.56531000, 10.90862000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q302907'),
(29594, 'Thiersheim', 3009, 'BY', 82, 'DE', 50.07609000, 12.12650000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q505732'),
(29595, 'Thierstein', 3009, 'BY', 82, 'DE', 50.10643000, 12.10203000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q528114'),
(29596, 'Tholey', 3020, 'SL', 82, 'DE', 49.48374000, 7.03691000, '2019-10-05 22:44:54', '2019-10-05 22:44:54', 1, 'Q638618'),
(29597, 'Thomasburg', 3008, 'NI', 82, 'DE', 53.23333000, 10.66667000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q631968'),
(29598, 'Thomm', 3019, 'RP', 82, 'DE', 49.74140000, 6.80492000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q553243'),
(29599, 'Thuine', 3008, 'NI', 82, 'DE', 52.50000000, 7.48333000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q631974'),
(29600, 'Thum', 3021, 'SN', 82, 'DE', 50.67081000, 12.95091000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q57996'),
(29601, 'Thundorf in Unterfranken', 3009, 'BY', 82, 'DE', 50.20097000, 10.31906000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q57996'),
(29602, 'Thurmansbang', 3009, 'BY', 82, 'DE', 48.76627000, 13.31550000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q33506445'),
(29603, 'Thurnau', 3009, 'BY', 82, 'DE', 50.02542000, 11.39348000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q33506445'),
(29604, 'Thyrnau', 3009, 'BY', 82, 'DE', 48.61667000, 13.53333000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q504978'),
(29605, 'Thüngen', 3009, 'BY', 82, 'DE', 49.94140000, 9.85860000, '2019-10-05 22:44:55', '2020-05-01 17:22:48', 1, 'Q517233'),
(29606, 'Thüngersheim', 3009, 'BY', 82, 'DE', 49.87833000, 9.84917000, '2019-10-05 22:44:55', '2020-05-01 17:22:48', 1, 'Q518018'),
(29607, 'Thür', 3019, 'RP', 82, 'DE', 50.35692000, 7.27689000, '2019-10-05 22:44:55', '2020-05-01 17:22:49', 1, 'Q680980'),
(29608, 'Tiddische', 3008, 'NI', 82, 'DE', 52.51667000, 10.80000000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q548529'),
(29609, 'Tiefenbach', 3009, 'BY', 82, 'DE', 48.50000000, 12.10000000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q548529'),
(29610, 'Tiefenbronn', 3006, 'BW', 82, 'DE', 48.82398000, 8.80129000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q80548'),
(29611, 'Tiefenort', 3015, 'TH', 82, 'DE', 50.83946000, 10.16604000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q631922'),
(29612, 'Tiergarten', 3010, 'BE', 82, 'DE', 52.51667000, 13.36667000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q328672'),
(29613, 'Timmaspe', 3005, 'SH', 82, 'DE', 54.13967000, 9.89430000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q328672'),
(29614, 'Timmendorfer Strand', 3005, 'SH', 82, 'DE', 53.99530000, 10.77676000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q541750'),
(29615, 'Timmenrode', 3011, 'ST', 82, 'DE', 51.77113000, 11.00624000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q698870'),
(29616, 'Tinnum', 3005, 'SH', 82, 'DE', 54.89932000, 8.33476000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q20646'),
(29617, 'Tirpersdorf', 3021, 'SN', 82, 'DE', 50.43333000, 12.25000000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q46808'),
(29618, 'Tirschenreuth', 3009, 'BY', 82, 'DE', 49.88263000, 12.33112000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q205436'),
(29619, 'Titisee-Neustadt', 3006, 'BW', 82, 'DE', 47.92104000, 8.19063000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q505504'),
(29620, 'Tittling', 3009, 'BY', 82, 'DE', 48.72619000, 13.38221000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q517967'),
(29621, 'Tittmoning', 3009, 'BY', 82, 'DE', 48.06164000, 12.76760000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q505158'),
(29622, 'Titz', 3017, 'NW', 82, 'DE', 51.00619000, 6.42477000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q200076'),
(29623, 'Todenbüttel', 3005, 'SH', 82, 'DE', 54.13333000, 9.55000000, '2019-10-05 22:44:55', '2020-05-01 17:22:50', 1, 'Q200076'),
(29624, 'Todendorf', 3005, 'SH', 82, 'DE', 53.69461000, 10.34781000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q200076'),
(29625, 'Todesfelde', 3005, 'SH', 82, 'DE', 53.90000000, 10.18333000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q200076'),
(29626, 'Todtenweis', 3009, 'BY', 82, 'DE', 48.51712000, 10.92856000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q259742'),
(29627, 'Todtmoos', 3006, 'BW', 82, 'DE', 47.74014000, 8.00183000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q509189'),
(29628, 'Todtnau', 3006, 'BW', 82, 'DE', 47.82941000, 7.94380000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q61665'),
(29629, 'Tolk', 3005, 'SH', 82, 'DE', 54.57987000, 9.63844000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q61665'),
(29630, 'Tollwitz', 3011, 'ST', 82, 'DE', 51.28601000, 12.09733000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q688656'),
(29631, 'Toppenstedt', 3008, 'NI', 82, 'DE', 53.26430000, 10.11451000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q640065'),
(29632, 'Torgau', 3021, 'SN', 82, 'DE', 51.56016000, 12.99617000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q12062'),
(29633, 'Torgelow', 3007, 'MV', 82, 'DE', 53.63415000, 14.01346000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q571970'),
(29634, 'Tornesch', 3005, 'SH', 82, 'DE', 53.70000000, 9.71667000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q504812'),
(29635, 'Tostedt', 3008, 'NI', 82, 'DE', 53.28333000, 9.71667000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q651407'),
(29636, 'Traben-Trarbach', 3019, 'RP', 82, 'DE', 49.95076000, 7.11562000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q540919'),
(29637, 'Trabitz', 3009, 'BY', 82, 'DE', 49.80000000, 11.90000000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q504667'),
(29638, 'Train', 3009, 'BY', 82, 'DE', 48.73333000, 11.83333000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q504667'),
(29639, 'Traitsching', 3009, 'BY', 82, 'DE', 49.15000000, 12.65000000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q503985'),
(29640, 'Trappenkamp', 3005, 'SH', 82, 'DE', 54.03988000, 10.21496000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q549151'),
(29641, 'Trappstadt', 3009, 'BY', 82, 'DE', 50.31880000, 10.56995000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q558891'),
(29642, 'Trassem', 3019, 'RP', 82, 'DE', 49.57849000, 6.52540000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q656830'),
(29643, 'Traunreut', 3009, 'BY', 82, 'DE', 47.96269000, 12.59231000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q55792046'),
(29644, 'Traunstein', 3009, 'BY', 82, 'DE', 47.86825000, 12.64335000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q253119'),
(29645, 'Trausnitz', 3009, 'BY', 82, 'DE', 49.52114000, 12.26249000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q253119'),
(29646, 'Trautskirchen', 3009, 'BY', 82, 'DE', 49.45810000, 10.59361000, '2019-10-05 22:44:55', '2019-10-05 22:44:55', 1, 'Q502874'),
(29647, 'Travemünde', 3005, 'SH', 82, 'DE', 53.96304000, 10.87090000, '2019-10-05 22:44:55', '2020-05-01 17:22:50', 1, 'Q321792'),
(29648, 'Trebbin', 3013, 'BB', 82, 'DE', 52.21675000, 13.22496000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q570114'),
(29649, 'Trebendorf', 3021, 'SN', 82, 'DE', 51.53333000, 14.56667000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q544870'),
(29650, 'Trebgast', 3009, 'BY', 82, 'DE', 50.06667000, 11.55000000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q503901'),
(29651, 'Trebitz', 3011, 'ST', 82, 'DE', 51.58615000, 11.91965000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q503901'),
(29652, 'Trebsen', 3021, 'SN', 82, 'DE', 51.28898000, 12.75496000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q33507411'),
(29653, 'Trebur', 3018, 'HE', 82, 'DE', 49.92639000, 8.40732000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q634084'),
(29654, 'Trechtingshausen', 3019, 'RP', 82, 'DE', 50.01031000, 7.84709000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q680372'),
(29655, 'Treffelstein', 3009, 'BY', 82, 'DE', 49.42246000, 12.61574000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q511207'),
(29656, 'Treffurt', 3015, 'TH', 82, 'DE', 51.13691000, 10.23361000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q546754'),
(29657, 'Treia', 3005, 'SH', 82, 'DE', 54.50000000, 9.31667000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q546754'),
(29658, 'Treis-Karden', 3019, 'RP', 82, 'DE', 50.17174000, 7.30218000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q656110'),
(29659, 'Tremsbüttel', 3005, 'SH', 82, 'DE', 53.74384000, 10.31024000, '2019-10-05 22:44:56', '2020-05-01 17:22:50', 1, 'Q377818'),
(29660, 'Trendelburg', 3018, 'HE', 82, 'DE', 51.57408000, 9.42095000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q515941'),
(29661, 'Treuchtlingen', 3009, 'BY', 82, 'DE', 48.95473000, 10.90833000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q252466'),
(29662, 'Treuen', 3021, 'SN', 82, 'DE', 50.54247000, 12.30339000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q46909'),
(29663, 'Treuenbrietzen', 3013, 'BB', 82, 'DE', 52.09754000, 12.87258000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q625078'),
(29664, 'Triberg', 3006, 'BW', 82, 'DE', 48.13175000, 8.23317000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q83271'),
(29665, 'Tribsees', 3007, 'MV', 82, 'DE', 54.09556000, 12.75683000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q572060'),
(29666, 'Triebel', 3021, 'SN', 82, 'DE', 50.37153000, 12.12118000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q46811'),
(29667, 'Triebes', 3015, 'TH', 82, 'DE', 50.68489000, 12.02042000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q826545'),
(29668, 'Trier', 3019, 'RP', 82, 'DE', 49.75565000, 6.63935000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q826545'),
(29669, 'Trierweiler', 3019, 'RP', 82, 'DE', 49.76231000, 6.55987000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q826545'),
(29670, 'Triftern', 3009, 'BY', 82, 'DE', 48.39468000, 13.00627000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q550561'),
(29671, 'Trinwillershagen', 3007, 'MV', 82, 'DE', 54.25048000, 12.62312000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q667494'),
(29672, 'Trippstadt', 3019, 'RP', 82, 'DE', 49.35935000, 7.77480000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q656103'),
(29673, 'Triptis', 3015, 'TH', 82, 'DE', 50.73567000, 11.87015000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q266510'),
(29674, 'Trittau', 3005, 'SH', 82, 'DE', 53.61667000, 10.40000000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q511820'),
(29675, 'Trittenheim', 3019, 'RP', 82, 'DE', 49.82471000, 6.89929000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q511820'),
(29676, 'Trochtelfingen', 3006, 'BW', 82, 'DE', 48.30843000, 9.24491000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q82285'),
(29677, 'Trogen', 3009, 'BY', 82, 'DE', 50.36667000, 11.95000000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q82285'),
(29678, 'Troisdorf', 3017, 'NW', 82, 'DE', 50.80901000, 7.14968000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q3900'),
(29679, 'Trollenhagen', 3007, 'MV', 82, 'DE', 53.60706000, 13.29103000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q49440'),
(29680, 'Trossin', 3021, 'SN', 82, 'DE', 51.61667000, 12.81667000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q16066'),
(29681, 'Trossingen', 3006, 'BW', 82, 'DE', 48.07667000, 8.64409000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q505529'),
(29682, 'Trostberg an der Alz', 3009, 'BY', 82, 'DE', 48.02802000, 12.55804000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q31644'),
(29683, 'Trulben', 3019, 'RP', 82, 'DE', 49.13916000, 7.54370000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q656084'),
(29684, 'Trunkelsberg', 3009, 'BY', 82, 'DE', 48.00000000, 10.21667000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q548626'),
(29685, 'Trusetal', 3015, 'TH', 82, 'DE', 50.78333000, 10.41667000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q665797'),
(29686, 'Tröstau', 3009, 'BY', 82, 'DE', 50.01667000, 11.95000000, '2019-10-05 22:44:56', '2020-05-01 17:22:48', 1, 'Q665797'),
(29687, 'Tschernitz', 3013, 'BB', 82, 'DE', 51.58333000, 14.61667000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q633598'),
(29688, 'Tucheim', 3011, 'ST', 82, 'DE', 52.28923000, 12.18423000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q706395'),
(29689, 'Tuchenbach', 3009, 'BY', 82, 'DE', 49.52794000, 10.85973000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q510922'),
(29690, 'Tuningen', 3006, 'BW', 82, 'DE', 48.03333000, 8.60000000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q83293'),
(29691, 'Tuntenhausen', 3009, 'BY', 82, 'DE', 47.93509000, 12.01518000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q545218'),
(29692, 'Tussenhausen', 3009, 'BY', 82, 'DE', 48.10218000, 10.56069000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q547229'),
(29693, 'Tutow', 3007, 'MV', 82, 'DE', 53.91566000, 13.24814000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q655303'),
(29694, 'Tuttlingen', 3006, 'BW', 82, 'DE', 47.98464000, 8.81770000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q15987'),
(29695, 'Tutzing', 3009, 'BY', 82, 'DE', 47.90938000, 11.28030000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q533166'),
(29696, 'Twistringen', 3008, 'NI', 82, 'DE', 52.79926000, 8.64163000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q503418'),
(29697, 'Tyrlaching', 3009, 'BY', 82, 'DE', 48.06667000, 12.66667000, '2019-10-05 22:44:56', '2019-10-05 22:44:56', 1, 'Q1617853'),
(29698, 'Täferrot', 3006, 'BW', 82, 'DE', 48.84972000, 9.83824000, '2019-10-05 22:44:56', '2020-05-01 17:22:47', 1, 'Q529205'),
(29699, 'Tännesberg', 3009, 'BY', 82, 'DE', 49.53182000, 12.32765000, '2019-10-05 22:44:56', '2020-05-01 17:22:48', 1, 'Q505382'),
(29700, 'Töging am Inn', 3009, 'BY', 82, 'DE', 48.26018000, 12.58460000, '2019-10-05 22:44:56', '2020-05-01 17:22:48', 1, 'Q256375'),
(29701, 'Tönisvorst', 3017, 'NW', 82, 'DE', 51.32092000, 6.49412000, '2019-10-05 22:44:56', '2020-05-01 17:22:49', 1, 'Q163570'),
(29702, 'Tönning', 3005, 'SH', 82, 'DE', 54.31879000, 8.94234000, '2019-10-05 22:44:56', '2020-05-01 17:22:50', 1, 'Q21018'),
(29703, 'Töpen', 3009, 'BY', 82, 'DE', 50.39067000, 11.87329000, '2019-10-05 22:44:57', '2020-05-01 17:22:48', 1, 'Q21018'),
(29704, 'Tübingen', 3006, 'BW', 82, 'DE', 48.52266000, 9.05222000, '2019-10-05 22:44:57', '2020-05-01 17:22:47', 1, 'Q3806'),
(29705, 'Tübingen Region', 3006, 'BW', 82, 'DE', 48.16667000, 9.50000000, '2019-10-05 22:44:57', '2020-05-01 17:22:47', 1, 'Q8170'),
(29706, 'Tülau', 3008, 'NI', 82, 'DE', 52.57578000, 10.87818000, '2019-10-05 22:44:57', '2020-05-01 17:22:48', 1, 'Q8170'),
(29707, 'Türkenfeld', 3009, 'BY', 82, 'DE', 48.10531000, 11.08303000, '2019-10-05 22:44:57', '2020-05-01 17:22:48', 1, 'Q49291984'),
(29708, 'Türkheim', 3009, 'BY', 82, 'DE', 48.06401000, 10.64156000, '2019-10-05 22:44:57', '2020-05-01 17:22:48', 1, 'Q552257'),
(29709, 'Türnich', 3017, 'NW', 82, 'DE', 50.85892000, 6.75535000, '2019-10-05 22:44:57', '2020-05-01 17:22:49', 1, 'Q2194016'),
(29710, 'Tüttendorf', 3005, 'SH', 82, 'DE', 54.40000000, 10.00000000, '2019-10-05 22:44:57', '2020-05-01 17:22:50', 1, 'Q2194016'),
(29711, 'Tüßling', 3009, 'BY', 82, 'DE', 48.21218000, 12.59954000, '2019-10-05 22:44:57', '2020-05-01 17:22:48', 1, 'Q2194016'),
(29712, 'Ubstadt-Weiher', 3006, 'BW', 82, 'DE', 49.16296000, 8.63165000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q520414'),
(29713, 'Uchte', 3008, 'NI', 82, 'DE', 52.49975000, 8.90928000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q559374'),
(29714, 'Uchtspringe', 3011, 'ST', 82, 'DE', 52.54011000, 11.59959000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q704102'),
(29715, 'Udenhausen', 3018, 'HE', 82, 'DE', 51.46385000, 9.46335000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q2473066'),
(29716, 'Udenheim', 3019, 'RP', 82, 'DE', 49.86472000, 8.17167000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q2473066'),
(29717, 'Uder', 3015, 'TH', 82, 'DE', 51.36243000, 10.07210000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q552793'),
(29718, 'Uebigau', 3013, 'BB', 82, 'DE', 51.59415000, 13.29983000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q552793'),
(29719, 'Ueckermünde', 3007, 'MV', 82, 'DE', 53.73795000, 14.04473000, '2019-10-05 22:44:57', '2020-05-01 17:22:49', 1, 'Q506700'),
(29720, 'Uedem', 3017, 'NW', 82, 'DE', 51.66520000, 6.27371000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q49292010'),
(29721, 'Uehlfeld', 3009, 'BY', 82, 'DE', 49.67085000, 10.72017000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q505394'),
(29722, 'Uehrde', 3008, 'NI', 82, 'DE', 52.09902000, 10.76523000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q505394'),
(29723, 'Uelsen', 3008, 'NI', 82, 'DE', 52.50000000, 6.88333000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q572300'),
(29724, 'Uelversheim', 3019, 'RP', 82, 'DE', 49.81000000, 8.28861000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q572300'),
(29725, 'Uelzen', 3008, 'NI', 82, 'DE', 52.96572000, 10.56111000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q15989'),
(29726, 'Uenglingen', 3011, 'ST', 82, 'DE', 52.61797000, 11.80893000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q701881'),
(29727, 'Uetersen', 3005, 'SH', 82, 'DE', 53.68769000, 9.66394000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q1404'),
(29728, 'Uettingen', 3009, 'BY', 82, 'DE', 49.79472000, 9.73056000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q510112'),
(29729, 'Uetze', 3008, 'NI', 82, 'DE', 52.46511000, 10.20467000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q510112'),
(29730, 'Uffenheim', 3009, 'BY', 82, 'DE', 49.54415000, 10.23286000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q572410'),
(29731, 'Uffing', 3009, 'BY', 82, 'DE', 47.71378000, 11.15034000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q572410'),
(29732, 'Uftrungen', 3011, 'ST', 82, 'DE', 51.49859000, 10.98066000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q698213'),
(29733, 'Uhingen', 3006, 'BW', 82, 'DE', 48.70475000, 9.58570000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q80826'),
(29734, 'Uhldingen-Mühlhofen', 3006, 'BW', 82, 'DE', 47.73333000, 9.25000000, '2019-10-05 22:44:57', '2020-05-01 17:22:47', 1, 'Q527642'),
(29735, 'Uhyst', 3021, 'SN', 82, 'DE', 51.36469000, 14.50600000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q703140'),
(29736, 'Uichteritz', 3011, 'ST', 82, 'DE', 51.20652000, 11.92215000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q699039'),
(29737, 'Ulm', 3006, 'BW', 82, 'DE', 48.39841000, 9.99155000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q3012'),
(29738, 'Ulmen', 3019, 'RP', 82, 'DE', 50.20943000, 6.97941000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q570101'),
(29739, 'Ulrichstein', 3018, 'HE', 82, 'DE', 50.57550000, 9.19272000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q570101'),
(29740, 'Umkirch', 3006, 'BW', 82, 'DE', 48.03333000, 7.76667000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q527982'),
(29741, 'Ummendorf', 3011, 'ST', 82, 'DE', 52.15645000, 11.18151000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q527982'),
(29742, 'Ummendorf', 3006, 'BW', 82, 'DE', 48.06667000, 9.83333000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q527982'),
(29743, 'Ummern', 3008, 'NI', 82, 'DE', 52.59111000, 10.43195000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q638909'),
(29744, 'Undenheim', 3019, 'RP', 82, 'DE', 49.83806000, 8.21889000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q638909'),
(29745, 'Ungerhausen', 3009, 'BY', 82, 'DE', 48.00566000, 10.26672000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q628949'),
(29746, 'Unkel', 3019, 'RP', 82, 'DE', 50.59653000, 7.21888000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q498589'),
(29747, 'Unlingen', 3006, 'BW', 82, 'DE', 48.16734000, 9.52219000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q519779'),
(29748, 'Unna', 3017, 'NW', 82, 'DE', 51.53795000, 7.68969000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q3949'),
(29749, 'Unnau', 3019, 'RP', 82, 'DE', 50.64881000, 7.90827000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q681248'),
(29750, 'Unseburg', 3011, 'ST', 82, 'DE', 51.93284000, 11.51281000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q694968'),
(29751, 'Unter-Abtsteinach', 3018, 'HE', 82, 'DE', 49.52711000, 8.78679000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q1666816'),
(29752, 'Unterammergau', 3009, 'BY', 82, 'DE', 47.61658000, 11.02718000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q49292075'),
(29753, 'Unterbreizbach', 3015, 'TH', 82, 'DE', 50.81667000, 9.98333000, '2019-10-05 22:44:57', '2019-10-05 22:44:57', 1, 'Q631418'),
(29754, 'Unterdietfurt', 3009, 'BY', 82, 'DE', 48.38333000, 12.66667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q581527'),
(29755, 'Unterdießen', 3009, 'BY', 82, 'DE', 47.98333000, 10.83333000, '2019-10-05 22:44:58', '2020-05-01 17:22:48', 1, 'Q581527'),
(29756, 'Unteregg', 3009, 'BY', 82, 'DE', 47.96667000, 10.46667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q547049'),
(29757, 'Untereisesheim', 3006, 'BW', 82, 'DE', 49.21111000, 9.20194000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q503220'),
(29758, 'Unterensingen', 3006, 'BW', 82, 'DE', 48.65452000, 9.35799000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q80803'),
(29759, 'Unterföhring', 3009, 'BY', 82, 'DE', 48.19253000, 11.64293000, '2019-10-05 22:44:58', '2020-05-01 17:22:48', 1, 'Q267408'),
(29760, 'Untergriesbach', 3009, 'BY', 82, 'DE', 48.57434000, 13.66725000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q517938'),
(29761, 'Untergruppenbach', 3006, 'BW', 82, 'DE', 49.08940000, 9.27516000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q506597'),
(29762, 'Unterhaching', 3009, 'BY', 82, 'DE', 48.06598000, 11.61564000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q264899'),
(29763, 'Unterhausen', 3006, 'BW', 82, 'DE', 48.42980000, 9.25504000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q264899'),
(29764, 'Unterjettingen', 3006, 'BW', 82, 'DE', 48.56230000, 8.78445000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q33510632'),
(29765, 'Unterkirnach', 3006, 'BW', 82, 'DE', 48.07946000, 8.36575000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q81419'),
(29766, 'Unterkrozingen', 3006, 'BW', 82, 'DE', 47.91933000, 7.69045000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q81419'),
(29767, 'Unterleinleiter', 3009, 'BY', 82, 'DE', 49.82303000, 11.18906000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q627413'),
(29768, 'Unterlüß', 3008, 'NI', 82, 'DE', 52.83471000, 10.29685000, '2019-10-05 22:44:58', '2020-05-01 17:22:48', 1, 'Q566271'),
(29769, 'Untermaßfeld', 3015, 'TH', 82, 'DE', 50.53333000, 10.41667000, '2019-10-05 22:44:58', '2020-05-01 17:22:50', 1, 'Q546638'),
(29770, 'Untermeitingen', 3009, 'BY', 82, 'DE', 48.16082000, 10.80694000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q546638'),
(29771, 'Untermerzbach', 3009, 'BY', 82, 'DE', 50.12754000, 10.85634000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q504990'),
(29772, 'Untermünkheim', 3006, 'BW', 82, 'DE', 49.15255000, 9.73384000, '2019-10-05 22:44:58', '2020-05-01 17:22:47', 1, 'Q83143'),
(29773, 'Unterneukirchen', 3009, 'BY', 82, 'DE', 48.16667000, 12.61667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q931366'),
(29774, 'Unterpleichfeld', 3009, 'BY', 82, 'DE', 49.86886000, 10.04399000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q514399'),
(29775, 'Unterreichenbach', 3006, 'BW', 82, 'DE', 48.82496000, 8.70885000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q536333'),
(29776, 'Unterreit', 3009, 'BY', 82, 'DE', 48.11667000, 12.33333000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q627477'),
(29777, 'Unterschleißheim', 3009, 'BY', 82, 'DE', 48.28038000, 11.57684000, '2019-10-05 22:44:58', '2020-05-01 17:22:48', 1, 'Q627477'),
(29778, 'Unterschneidheim', 3006, 'BW', 82, 'DE', 48.93333000, 10.36667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q514389'),
(29779, 'Untersiemau', 3009, 'BY', 82, 'DE', 50.19415000, 10.97448000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q572622'),
(29780, 'Untersteinach', 3009, 'BY', 82, 'DE', 50.13333000, 11.51667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q504163'),
(29781, 'Unterthingau', 3009, 'BY', 82, 'DE', 47.77155000, 10.50446000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q512033'),
(29782, 'Unterwellenborn', 3015, 'TH', 82, 'DE', 50.65000000, 11.43333000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q551957'),
(29783, 'Unterwössen', 3009, 'BY', 82, 'DE', 47.73333000, 12.46667000, '2019-10-05 22:44:58', '2020-05-01 17:22:48', 1, 'Q534872'),
(29784, 'Untrasried', 3009, 'BY', 82, 'DE', 47.83333000, 10.38333000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q528104'),
(29785, 'Upgant-Schott', 3008, 'NI', 82, 'DE', 53.51667000, 7.28333000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q426639'),
(29786, 'Upper Bavaria', 3009, 'BY', 82, 'DE', 48.00000000, 11.00000000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q10562'),
(29787, 'Upper Franconia', 3009, 'BY', 82, 'DE', 49.83333000, 11.33333000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q10554'),
(29788, 'Upper Palatinate', 3009, 'BY', 82, 'DE', 49.50000000, 12.00000000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q10555'),
(29789, 'Urbach', 3015, 'TH', 82, 'DE', 51.29468000, 10.60540000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q10555'),
(29790, 'Urbach', 3006, 'BW', 82, 'DE', 48.81680000, 9.57690000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q81221'),
(29791, 'Urbach-Überdorf', 3019, 'RP', 82, 'DE', 50.55690000, 7.58695000, '2019-10-05 22:44:58', '2020-05-01 17:22:49', 1, 'Q81221'),
(29792, 'Urbar', 3019, 'RP', 82, 'DE', 50.38333000, 7.63333000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q81221'),
(29793, 'Urmitz', 3019, 'RP', 82, 'DE', 50.41667000, 7.51667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q555328'),
(29794, 'Ursberg', 3009, 'BY', 82, 'DE', 48.26428000, 10.44594000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q508668'),
(29795, 'Ursensollen', 3009, 'BY', 82, 'DE', 49.40204000, 11.75503000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q532954'),
(29796, 'Urspringen', 3009, 'BY', 82, 'DE', 49.90166000, 9.67123000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q508832'),
(29797, 'Usedom', 3007, 'MV', 82, 'DE', 53.87537000, 13.92394000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q508832'),
(29798, 'Usingen', 3018, 'HE', 82, 'DE', 50.33554000, 8.53688000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q622565'),
(29799, 'Uslar', 3008, 'NI', 82, 'DE', 51.65690000, 9.63501000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q159814'),
(29800, 'Ustersbach', 3009, 'BY', 82, 'DE', 48.31667000, 10.65000000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q258835'),
(29801, 'Uthleben', 3015, 'TH', 82, 'DE', 51.44912000, 10.83800000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q303979'),
(29802, 'Uthlede', 3008, 'NI', 82, 'DE', 53.31119000, 8.57861000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q629094'),
(29803, 'Uttenreuth', 3009, 'BY', 82, 'DE', 49.59675000, 11.07216000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q2503881'),
(29804, 'Uttenweiler', 3006, 'BW', 82, 'DE', 48.15000000, 9.61667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q518981'),
(29805, 'Utting am Ammersee', 3009, 'BY', 82, 'DE', 48.02608000, 11.08612000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q518981'),
(29806, 'Vaale', 3005, 'SH', 82, 'DE', 54.00000000, 9.38333000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q518981'),
(29807, 'Vacha', 3015, 'TH', 82, 'DE', 50.82790000, 10.02185000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q518981'),
(29808, 'Vachendorf', 3009, 'BY', 82, 'DE', 47.84256000, 12.60606000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q259158'),
(29809, 'Vagen', 3009, 'BY', 82, 'DE', 47.87410000, 11.88446000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q252664'),
(29810, 'Vaihingen an der Enz', 3006, 'BW', 82, 'DE', 48.93563000, 8.96045000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q61928'),
(29811, 'Vallendar', 3019, 'RP', 82, 'DE', 50.39586000, 7.62427000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q536559'),
(29812, 'Valley', 3009, 'BY', 82, 'DE', 47.89310000, 11.77915000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q536559'),
(29813, 'Varel', 3008, 'NI', 82, 'DE', 53.39693000, 8.13621000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q496056'),
(29814, 'Varrel', 3008, 'NI', 82, 'DE', 52.62150000, 8.73310000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q505217'),
(29815, 'Vaterstetten', 3009, 'BY', 82, 'DE', 48.10537000, 11.76825000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q542921'),
(29816, 'Vechelde', 3008, 'NI', 82, 'DE', 52.26038000, 10.36491000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q554288'),
(29817, 'Vechta', 3008, 'NI', 82, 'DE', 52.72632000, 8.28598000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q16096'),
(29818, 'Veckenstedt', 3011, 'ST', 82, 'DE', 51.90113000, 10.73203000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q695600'),
(29819, 'Vegesack', 3014, 'HB', 82, 'DE', 53.16667000, 8.61667000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q315005'),
(29820, 'Veilsdorf', 3015, 'TH', 82, 'DE', 50.40876000, 10.80947000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q631479'),
(29821, 'Veitsbronn', 3009, 'BY', 82, 'DE', 49.51239000, 10.88797000, '2019-10-05 22:44:58', '2019-10-05 22:44:58', 1, 'Q524234'),
(29822, 'Veitshöchheim', 3009, 'BY', 82, 'DE', 49.83278000, 9.88167000, '2019-10-05 22:44:59', '2020-05-01 17:22:48', 1, 'Q573628'),
(29823, 'Velbert', 3017, 'NW', 82, 'DE', 51.33537000, 7.04348000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q3840'),
(29824, 'Velburg', 3009, 'BY', 82, 'DE', 49.23212000, 11.67160000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q504758'),
(29825, 'Velden', 3009, 'BY', 82, 'DE', 48.36632000, 12.25596000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q551234'),
(29826, 'Velen', 3017, 'NW', 82, 'DE', 51.89447000, 6.98807000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q49292274'),
(29827, 'Velgast', 3007, 'MV', 82, 'DE', 54.27204000, 12.81075000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q263965'),
(29828, 'Vellberg', 3006, 'BW', 82, 'DE', 49.08430000, 9.87914000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q81061'),
(29829, 'Vellmar', 3018, 'HE', 82, 'DE', 51.35806000, 9.47974000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q569651'),
(29830, 'Velpke', 3008, 'NI', 82, 'DE', 52.40797000, 10.93637000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q633397'),
(29831, 'Velten', 3013, 'BB', 82, 'DE', 52.69149000, 13.17533000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q585613'),
(29832, 'Veltheim', 3008, 'NI', 82, 'DE', 52.21874000, 10.68327000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q664879'),
(29833, 'Venusberg', 3021, 'SN', 82, 'DE', 50.69918000, 13.01854000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q58015'),
(29834, 'Verden', 3008, 'NI', 82, 'DE', 52.92343000, 9.23491000, '2019-10-05 22:44:59', '2019-10-05 22:44:59', 1, 'Q486551');

