INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(11170, 'Carvoeira', 2014, 'SC', 31, 'BR', -27.59898000, -48.52618000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22072148'),
(11171, 'Casa Branca', 2021, 'SP', 31, 'BR', -21.81396000, -47.08264000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1760459'),
(11172, 'Casa Grande', 1998, 'MG', 31, 'BR', -20.84072000, -43.93951000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1074537'),
(11173, 'Casa Nova', 2002, 'BA', 31, 'BR', -9.10439000, -41.15736000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1074537'),
(11174, '<PERSON>asca', 2001, 'RS', 31, 'BR', -28.57590000, -51.91980000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1786658'),
(11175, 'Cascalho Rico', 1998, 'MG', 31, 'BR', -18.54617000, -47.85132000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1786658'),
(11176, 'Cascavel', 2022, 'PR', 31, 'BR', -24.95583000, -53.45528000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q205681'),
(11177, 'Cascavel', 2016, 'CE', 31, 'BR', -4.27004000, -38.27377000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q787864'),
(11178, 'Caseara', 2020, 'TO', 31, 'BR', -9.45982000, -49.83604000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q787864'),
(11179, 'Caseiros', 2001, 'RS', 31, 'BR', -28.23222000, -51.78157000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q787864'),
(11180, 'Casimiro de Abreu', 1997, 'RJ', 31, 'BR', -22.48056000, -42.20417000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1761688'),
(11181, 'Casinhas', 2006, 'PE', 31, 'BR', -7.75811000, -35.69922000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1761688'),
(11182, 'Casserengue', 2005, 'PB', 31, 'BR', -6.78130000, -35.81676000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1816259'),
(11183, 'Cassilândia', 2010, 'MS', 31, 'BR', -19.11333000, -51.73417000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1806605'),
(11184, 'Castanhal', 2009, 'PA', 31, 'BR', -1.27231000, -47.85627000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q984581'),
(11185, 'Castanheira', 2011, 'MT', 31, 'BR', -10.87932000, -58.65100000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q984581'),
(11186, 'Castanheiras', 2013, 'RO', 31, 'BR', -11.42661000, -61.88918000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q984581'),
(11187, 'Castelo', 2018, 'ES', 31, 'BR', -20.60361000, -41.18472000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1794952'),
(11188, 'Castelo do Piauí', 2008, 'PI', 31, 'BR', -5.21580000, -41.55735000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q2078710'),
(11189, 'Castelândia', 2000, 'GO', 31, 'BR', -18.15581000, -50.34000000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q2078710'),
(11190, 'Castilho', 2021, 'SP', 31, 'BR', -20.87222000, -51.48750000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q607594'),
(11191, 'Castro', 2022, 'PR', 31, 'BR', -24.83040000, -49.85535000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2077411'),
(11192, 'Castro Alves', 2002, 'BA', 31, 'BR', -12.74937000, -39.37691000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1764155'),
(11193, 'Cataguases', 1998, 'MG', 31, 'BR', -21.34548000, -42.64976000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1749746'),
(11194, 'Catalão', 2000, 'GO', 31, 'BR', -18.16583000, -47.94639000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q35135'),
(11195, 'Catanduva', 2021, 'SP', 31, 'BR', -21.13778000, -48.97278000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q983683'),
(11196, 'Catanduvas', 2022, 'PR', 31, 'BR', -25.26047000, -53.17016000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q983683'),
(11197, 'Catanduvas', 2014, 'SC', 31, 'BR', -27.05390000, -51.72395000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q983683'),
(11198, 'Catarina', 2016, 'CE', 31, 'BR', -6.25016000, -39.94870000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22059576'),
(11199, 'Catas Altas', 1998, 'MG', 31, 'BR', -20.07049000, -43.42197000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22059576'),
(11200, 'Catas Altas da Noruega', 1998, 'MG', 31, 'BR', -20.68094000, -43.49780000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22059576'),
(11201, 'Catende', 2006, 'PE', 31, 'BR', -8.66667000, -35.71667000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2011032'),
(11202, 'Catiguá', 2021, 'SP', 31, 'BR', -21.06536000, -49.04977000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q2011032'),
(11203, 'Catingueira', 2005, 'PB', 31, 'BR', -7.13699000, -37.59654000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2011032'),
(11204, 'Catolândia', 2002, 'BA', 31, 'BR', -12.29613000, -44.67528000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q2011032'),
(11205, 'Catolé do Rocha', 2005, 'PB', 31, 'BR', -6.34389000, -37.74667000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1808697'),
(11206, 'Catu', 2002, 'BA', 31, 'BR', -12.35306000, -38.37889000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1762371'),
(11207, 'Catuji', 1998, 'MG', 31, 'BR', -17.36328000, -41.48183000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1762371'),
(11208, 'Catunda', 2016, 'CE', 31, 'BR', -4.60469000, -40.18630000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22059575'),
(11209, 'Caturama', 2002, 'BA', 31, 'BR', -13.18601000, -42.30250000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22059575'),
(11210, 'Caturaí', 2000, 'GO', 31, 'BR', -16.46981000, -49.59556000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q22059575'),
(11211, 'Caturité', 2005, 'PB', 31, 'BR', -7.41991000, -36.04194000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q22059575'),
(11212, 'Catuti', 1998, 'MG', 31, 'BR', -15.32360000, -43.11391000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22059575'),
(11213, 'Catuípe', 2001, 'RS', 31, 'BR', -28.19806000, -54.05868000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q22059575'),
(11214, 'Caucaia', 2016, 'CE', 31, 'BR', -3.73611000, -38.65306000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q817233'),
(11215, 'Cavalcante', 2000, 'GO', 31, 'BR', -13.79750000, -47.45833000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q988763'),
(11216, 'Caxambu', 1998, 'MG', 31, 'BR', -21.97722000, -44.93250000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q829417'),
(11217, 'Caxambu do Sul', 2014, 'SC', 31, 'BR', -27.13095000, -52.91856000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q829417'),
(11218, 'Caxias', 2015, 'MA', 31, 'BR', -4.85889000, -43.35611000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q997107'),
(11219, 'Caxias do Sul', 2001, 'RS', 31, 'BR', -29.16806000, -51.17944000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q110017'),
(11220, 'Caxingó', 2008, 'PI', 31, 'BR', -3.40863000, -41.88771000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q110017'),
(11221, 'Caçador', 2014, 'SC', 31, 'BR', -26.77528000, -51.01500000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q986540'),
(11222, 'Caçapava', 2021, 'SP', 31, 'BR', -23.10083000, -45.70694000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1759103'),
(11223, 'Caçapava do Sul', 2001, 'RS', 31, 'BR', -30.61002000, -53.47227000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1749869'),
(11224, 'Caçu', 2000, 'GO', 31, 'BR', -18.55667000, -51.13083000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1052080'),
(11225, 'Caém', 2002, 'BA', 31, 'BR', -11.14182000, -40.29142000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1946795'),
(11226, 'Ceará Mirim', 2019, 'RN', 31, 'BR', -5.63444000, -35.42556000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q22024239'),
(11227, 'Ceará-Mirim', 2019, 'RN', 31, 'BR', -5.55046000, -35.37667000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1759622'),
(11228, 'Cedral', 2015, 'MA', 31, 'BR', -1.96086000, -44.57424000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1759622'),
(11229, 'Cedral', 2021, 'SP', 31, 'BR', -20.90742000, -49.25495000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1759622'),
(11230, 'Cedro', 2016, 'CE', 31, 'BR', -6.59104000, -39.12042000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2027857'),
(11231, 'Cedro', 2006, 'PE', 31, 'BR', -7.71879000, -39.23334000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2027857'),
(11232, 'Cedro de São João', 2003, 'SE', 31, 'BR', -10.28046000, -36.88830000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q2027857'),
(11233, 'Cedro do Abaeté', 1998, 'MG', 31, 'BR', -19.11465000, -45.70070000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q2027857'),
(11234, 'Celso Ramos', 2014, 'SC', 31, 'BR', -27.63444000, -51.33639000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1750758'),
(11235, 'Centenário', 2020, 'TO', 31, 'BR', -9.15331000, -47.44772000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1750758'),
(11236, 'Centenário', 2001, 'RS', 31, 'BR', -27.78654000, -52.00905000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q387167'),
(11237, 'Centenário do Sul', 2022, 'PR', 31, 'BR', -22.82111000, -51.59528000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1775764'),
(11238, 'Central', 2002, 'BA', 31, 'BR', -11.15424000, -42.08146000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2021433'),
(11239, 'Central de Minas', 1998, 'MG', 31, 'BR', -18.76784000, -41.28963000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2021433'),
(11240, 'Central do Maranhão', 2015, 'MA', 31, 'BR', -2.25521000, -44.84104000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q2021433'),
(11241, 'Centralina', 1998, 'MG', 31, 'BR', -18.60922000, -49.16062000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1792154'),
(11242, 'Centro Novo do Maranhão', 2015, 'MA', 31, 'BR', -3.31669000, -46.79364000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1792154'),
(11243, 'Centro do Guilherme', 2015, 'MA', 31, 'BR', -2.28072000, -46.07349000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1792154'),
(11244, 'Cerejeiras', 2013, 'RO', 31, 'BR', -13.18319000, -61.47518000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1792154'),
(11245, 'Ceres', 2000, 'GO', 31, 'BR', -15.27350000, -49.64455000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2019995'),
(11246, 'Cerqueira César', 2021, 'SP', 31, 'BR', -23.03556000, -49.16611000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1750093'),
(11247, 'Cerquilho', 2021, 'SP', 31, 'BR', -23.16500000, -47.74361000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1759399'),
(11248, 'Cerrito', 2001, 'RS', 31, 'BR', -31.73952000, -52.78378000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1785871'),
(11249, 'Cerro Azul', 2022, 'PR', 31, 'BR', -24.85878000, -49.29561000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2201300'),
(11250, 'Cerro Branco', 2001, 'RS', 31, 'BR', -29.63374000, -52.98725000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q2201300'),
(11251, 'Cerro Corá', 2019, 'RN', 31, 'BR', -6.04556000, -36.34583000, '2019-10-05 22:35:01', '2020-05-01 17:22:37', 1, 'Q1802687'),
(11252, 'Cerro Grande', 2001, 'RS', 31, 'BR', -27.62261000, -53.16028000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1802687'),
(11253, 'Cerro Grande do Sul', 2001, 'RS', 31, 'BR', -30.58623000, -51.74239000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1802687'),
(11254, 'Cerro Largo', 2001, 'RS', 31, 'BR', -28.13490000, -54.73667000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1786013'),
(11255, 'Cerro Negro', 2014, 'SC', 31, 'BR', -27.77270000, -50.94035000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1786013'),
(11256, 'Cesário Lange', 2021, 'SP', 31, 'BR', -23.21195000, -47.88465000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1786013'),
(11257, 'Cezarina', 2000, 'GO', 31, 'BR', -17.11818000, -49.72995000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1786013'),
(11258, 'Chalé', 1998, 'MG', 31, 'BR', -20.03354000, -41.67809000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1786013'),
(11259, 'Chapada', 2001, 'RS', 31, 'BR', -28.10434000, -53.08861000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1758303'),
(11260, 'Chapada Gaúcha', 1998, 'MG', 31, 'BR', -15.57934000, -45.41242000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1758303'),
(11261, 'Chapada da Natividade', 2020, 'TO', 31, 'BR', -11.54159000, -47.88094000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1758303'),
(11262, 'Chapada de Areia', 2020, 'TO', 31, 'BR', -10.14838000, -49.19998000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1758303'),
(11263, 'Chapada do Norte', 1998, 'MG', 31, 'BR', -17.16293000, -42.37718000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1758303'),
(11264, 'Chapada dos Guimarães', 2011, 'MT', 31, 'BR', -15.02015000, -55.53735000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q986247'),
(11265, 'Chapadinha', 2015, 'MA', 31, 'BR', -3.74167000, -43.36028000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q746060'),
(11266, 'Chapadão do Céu', 2000, 'GO', 31, 'BR', -18.37043000, -52.56560000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q746060'),
(11267, 'Chapadão do Lageado', 2014, 'SC', 31, 'BR', -27.58487000, -49.55644000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q1784522'),
(11268, 'Chapadão do Sul', 2010, 'MS', 31, 'BR', -19.06696000, -52.74056000, '2019-10-05 22:35:01', '2020-05-01 17:22:36', 1, 'Q1784522'),
(11269, 'Chapecó', 2014, 'SC', 31, 'BR', -27.09639000, -52.61833000, '2019-10-05 22:35:01', '2020-05-01 17:22:38', 1, 'Q817387'),
(11270, 'Charqueada', 2021, 'SP', 31, 'BR', -22.53724000, -47.73770000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q983515'),
(11271, 'Charqueadas', 2001, 'RS', 31, 'BR', -29.95472000, -51.62528000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1751344'),
(11272, 'Charrua', 2001, 'RS', 31, 'BR', -27.94543000, -51.99053000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1751344'),
(11273, 'Chaval', 2016, 'CE', 31, 'BR', -3.07497000, -41.23723000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1751344'),
(11274, 'Chavantes', 2021, 'SP', 31, 'BR', -23.03889000, -49.70944000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1795758'),
(11275, 'Chaves', 2009, 'PA', 31, 'BR', -0.06289000, -49.64787000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1795758'),
(11276, 'Chiador', 1998, 'MG', 31, 'BR', -21.97253000, -43.00735000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1795758'),
(11277, 'Chiapetta', 2001, 'RS', 31, 'BR', -27.97630000, -53.92051000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q1795758'),
(11278, 'Chopinzinho', 2022, 'PR', 31, 'BR', -25.81428000, -52.45682000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q972174'),
(11279, 'Chorozinho', 2016, 'CE', 31, 'BR', -4.29181000, -38.48895000, '2019-10-05 22:35:01', '2019-10-05 22:35:01', 1, 'Q22059571'),
(11280, 'Chorrochó', 2002, 'BA', 31, 'BR', -9.30550000, -39.15652000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q22059571'),
(11281, 'Choró', 2016, 'CE', 31, 'BR', -4.77277000, -39.17779000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q22059571'),
(11282, 'Chupinguaia', 2013, 'RO', 31, 'BR', -12.67240000, -60.92622000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22059571'),
(11283, 'Chuvisca', 2001, 'RS', 31, 'BR', -30.77649000, -52.00221000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22059571'),
(11284, 'Chuí', 2001, 'RS', 31, 'BR', -33.69111000, -53.45667000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q665968'),
(11285, 'Chácara', 1998, 'MG', 31, 'BR', -21.67919000, -43.21835000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q665968'),
(11286, 'Chã Grande', 2006, 'PE', 31, 'BR', -8.23833000, -35.46167000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q2010970'),
(11287, 'Chã Preta', 2007, 'AL', 31, 'BR', -9.22921000, -36.33049000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q2010970'),
(11288, 'Chã de Alegria', 2006, 'PE', 31, 'BR', -7.99013000, -35.18974000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q2010970'),
(11289, 'Cianorte', 2022, 'PR', 31, 'BR', -23.66333000, -52.60500000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1803580'),
(11290, 'Cidade Gaúcha', 2022, 'PR', 31, 'BR', -23.36535000, -52.97466000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1803580'),
(11291, 'Cidade Ocidental', 2000, 'GO', 31, 'BR', -16.16178000, -47.79998000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1803580'),
(11292, 'Cidelândia', 2015, 'MA', 31, 'BR', -5.05234000, -47.86857000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1803580'),
(11293, 'Cidreira', 2001, 'RS', 31, 'BR', -30.09103000, -50.26938000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1749883'),
(11294, 'Cipotânea', 1998, 'MG', 31, 'BR', -20.93027000, -43.36547000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1749883'),
(11295, 'Cipó', 2002, 'BA', 31, 'BR', -11.09972000, -38.51361000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1762353'),
(11296, 'Ciríaco', 2001, 'RS', 31, 'BR', -28.37066000, -51.91242000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1762353'),
(11297, 'Claraval', 1998, 'MG', 31, 'BR', -20.35681000, -47.25241000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1762353'),
(11298, 'Claro dos Poções', 1998, 'MG', 31, 'BR', -17.09679000, -44.23300000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1762353'),
(11299, 'Clementina', 2021, 'SP', 31, 'BR', -21.57634000, -50.46202000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1762353'),
(11300, 'Clevelândia', 2022, 'PR', 31, 'BR', -26.39583000, -52.47083000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1773364'),
(11301, 'Cláudia', 2011, 'MT', 31, 'BR', -11.45086000, -54.99828000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q2065373'),
(11302, 'Cláudio', 1998, 'MG', 31, 'BR', -20.39006000, -44.79135000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q22062655'),
(11303, 'Coaraci', 2002, 'BA', 31, 'BR', -14.64083000, -39.55111000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q264690'),
(11304, 'Coari', 2004, 'AM', 31, 'BR', -4.08500000, -63.14139000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q645204'),
(11305, 'Cocal', 2014, 'SC', 31, 'BR', -28.60321000, -49.32767000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22046198'),
(11306, 'Cocal', 2008, 'PI', 31, 'BR', -3.43751000, -41.54594000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q511764'),
(11307, 'Cocal de Telha', 2008, 'PI', 31, 'BR', -4.64857000, -41.99568000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q511764'),
(11308, 'Cocal do Sul', 2014, 'SC', 31, 'BR', -28.59988000, -49.32582000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q511764'),
(11309, 'Cocal dos Alves', 2008, 'PI', 31, 'BR', -3.57383000, -41.45183000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q511764'),
(11310, 'Cocalinho', 2011, 'MT', 31, 'BR', -13.80273000, -51.15344000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q511764'),
(11311, 'Cocalzinho de Goiás', 2000, 'GO', 31, 'BR', -15.65900000, -48.57595000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q511764'),
(11312, 'Cocos', 2002, 'BA', 31, 'BR', -14.59438000, -45.27960000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q511764'),
(11313, 'Codajás', 2004, 'AM', 31, 'BR', -3.83667000, -62.05694000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1762074'),
(11314, 'Codó', 2015, 'MA', 31, 'BR', -4.45528000, -43.88556000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q2013225'),
(11315, 'Coelho Neto', 2015, 'MA', 31, 'BR', -4.25667000, -43.01278000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2019296'),
(11316, 'Coimbra', 1998, 'MG', 31, 'BR', -20.84494000, -42.79834000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22062654'),
(11317, 'Coité do Nóia', 2007, 'AL', 31, 'BR', -9.62616000, -36.60044000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1816142'),
(11318, 'Coivaras', 2008, 'PI', 31, 'BR', -5.11661000, -42.26583000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1816142'),
(11319, 'Colares', 2009, 'PA', 31, 'BR', -0.90894000, -48.25340000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1816142'),
(11320, 'Colatina', 2018, 'ES', 31, 'BR', -19.53944000, -40.63056000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q933380'),
(11321, 'Colina', 2021, 'SP', 31, 'BR', -20.75233000, -48.58162000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1761606'),
(11322, 'Colinas', 2015, 'MA', 31, 'BR', -6.02583000, -44.24917000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q369679'),
(11323, 'Colinas', 2001, 'RS', 31, 'BR', -29.39282000, -51.86831000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1759203'),
(11324, 'Colinas do Sul', 2000, 'GO', 31, 'BR', -14.01221000, -48.05124000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1759203'),
(11325, 'Colinas do Tocantins', 2020, 'TO', 31, 'BR', -8.09451000, -48.52143000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1759203'),
(11326, 'Colméia', 2020, 'TO', 31, 'BR', -8.87655000, -48.75788000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1759203'),
(11327, 'Colniza', 2011, 'MT', 31, 'BR', -9.29188000, -60.30457000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1759203'),
(11328, 'Colombo', 2022, 'PR', 31, 'BR', -25.30126000, -49.16965000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q926688'),
(11329, 'Colorado', 2022, 'PR', 31, 'BR', -22.83750000, -51.97306000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2077965'),
(11330, 'Colorado', 2001, 'RS', 31, 'BR', -28.48320000, -52.99088000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2077965'),
(11331, 'Colorado do Oeste', 2013, 'RO', 31, 'BR', -13.08252000, -60.49419000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2077965'),
(11332, 'Coluna', 1998, 'MG', 31, 'BR', -18.26842000, -42.82813000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1780178'),
(11333, 'Colíder', 2011, 'MT', 31, 'BR', -10.56333000, -55.44573000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1780178'),
(11334, 'Colômbia', 2021, 'SP', 31, 'BR', -20.29302000, -48.72328000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1780178'),
(11335, 'Colônia Leopoldina', 2006, 'PE', 31, 'BR', -8.90889000, -35.72500000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q22024413'),
(11336, 'Colônia Leopoldina', 2007, 'AL', 31, 'BR', -8.94183000, -35.76005000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1795581'),
(11337, 'Colônia do Gurguéia', 2008, 'PI', 31, 'BR', -8.14465000, -43.77459000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1795581'),
(11338, 'Colônia do Piauí', 2008, 'PI', 31, 'BR', -7.22638000, -42.21262000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q2066010'),
(11339, 'Combinado', 2020, 'TO', 31, 'BR', -12.82321000, -46.53424000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2066010'),
(11340, 'Comendador Gomes', 1998, 'MG', 31, 'BR', -19.65330000, -49.08738000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2066010'),
(11341, 'Comendador Levy Gasparian', 1997, 'RJ', 31, 'BR', -22.03655000, -43.25312000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2066010'),
(11342, 'Comercinho', 1998, 'MG', 31, 'BR', -16.28896000, -41.77346000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2066010'),
(11343, 'Comodoro', 2011, 'MT', 31, 'BR', -13.32583000, -59.82145000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1221976'),
(11344, 'Conceição', 2005, 'PB', 31, 'BR', -7.49917000, -38.51375000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1221976'),
(11345, 'Conceição da Aparecida', 1998, 'MG', 31, 'BR', -21.10120000, -46.22626000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1221976'),
(11346, 'Conceição da Barra', 2018, 'ES', 31, 'BR', -18.58540000, -39.73618000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1998957'),
(11347, 'Conceição da Barra de Minas', 1998, 'MG', 31, 'BR', -21.14228000, -44.48863000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1998957'),
(11348, 'Conceição da Feira', 2002, 'BA', 31, 'BR', -12.50583000, -38.99861000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1762474'),
(11349, 'Conceição das Alagoas', 1998, 'MG', 31, 'BR', -19.95627000, -48.30510000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q745089'),
(11350, 'Conceição das Pedras', 1998, 'MG', 31, 'BR', -22.13866000, -45.42694000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q745089'),
(11351, 'Conceição de Ipanema', 1998, 'MG', 31, 'BR', -19.91781000, -41.70024000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q745089'),
(11352, 'Conceição de Macabu', 1997, 'RJ', 31, 'BR', -22.12612000, -41.85341000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q745089'),
(11353, 'Conceição do Almeida', 2002, 'BA', 31, 'BR', -12.77944000, -39.17000000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1772585'),
(11354, 'Conceição do Araguaia', 2009, 'PA', 31, 'BR', -8.26441000, -49.26982000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1788037'),
(11355, 'Conceição do Canindé', 2008, 'PI', 31, 'BR', -7.98695000, -41.57544000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1788037'),
(11356, 'Conceição do Castelo', 2018, 'ES', 31, 'BR', -20.36226000, -41.26622000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1795227'),
(11357, 'Conceição do Coité', 2002, 'BA', 31, 'BR', -11.56389000, -39.28278000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1772525'),
(11358, 'Conceição do Jacuípe', 2002, 'BA', 31, 'BR', -12.31667000, -38.76667000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q988781'),
(11359, 'Conceição do Lago-Açu', 2015, 'MA', 31, 'BR', -3.73591000, -44.79669000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q988781'),
(11360, 'Conceição do Mato Dentro', 1998, 'MG', 31, 'BR', -18.90010000, -43.50229000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1792783'),
(11361, 'Conceição do Pará', 1998, 'MG', 31, 'BR', -19.78550000, -44.87016000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1792783'),
(11362, 'Conceição do Rio Verde', 1998, 'MG', 31, 'BR', -21.90132000, -45.07975000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1756600'),
(11363, 'Conceição do Tocantins', 2020, 'TO', 31, 'BR', -12.09878000, -47.27047000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1756600'),
(11364, 'Conceição dos Ouros', 1998, 'MG', 31, 'BR', -22.45037000, -45.77976000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1756600'),
(11365, 'Conchal', 2021, 'SP', 31, 'BR', -22.34611000, -47.13620000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1759722'),
(11366, 'Conchas', 2021, 'SP', 31, 'BR', -23.01528000, -48.01056000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1760352'),
(11367, 'Concórdia', 2014, 'SC', 31, 'BR', -27.22936000, -52.00106000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q749798'),
(11368, 'Concórdia do Pará', 2009, 'PA', 31, 'BR', -1.86154000, -47.96322000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q749798'),
(11369, 'Condado', 2006, 'PE', 31, 'BR', -7.58583000, -35.10583000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q507095'),
(11370, 'Condado', 2005, 'PB', 31, 'BR', -6.85244000, -37.61864000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q507095'),
(11371, 'Conde', 2005, 'PB', 31, 'BR', -7.25972000, -34.90750000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1999780'),
(11372, 'Conde', 2002, 'BA', 31, 'BR', -11.81361000, -37.61056000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1003399'),
(11373, 'Condeúba', 2002, 'BA', 31, 'BR', -14.93275000, -42.00693000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1003399'),
(11374, 'Condor', 2001, 'RS', 31, 'BR', -28.19261000, -53.49261000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1003399'),
(11375, 'Confins', 1998, 'MG', 31, 'BR', -19.65686000, -43.98131000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1003399'),
(11376, 'Confresa', 2011, 'MT', 31, 'BR', -10.30959000, -51.73651000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1003399'),
(11377, 'Congo', 2005, 'PB', 31, 'BR', -7.79578000, -36.63487000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2009615'),
(11378, 'Congonhal', 1998, 'MG', 31, 'BR', -22.13543000, -46.03992000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2009615'),
(11379, 'Congonhas', 1998, 'MG', 31, 'BR', -20.50525000, -43.85880000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q275654'),
(11380, 'Congonhas do Norte', 1998, 'MG', 31, 'BR', -18.88604000, -43.69620000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q275654'),
(11381, 'Congonhinhas', 2022, 'PR', 31, 'BR', -23.62943000, -50.49858000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q275654'),
(11382, 'Conquista', 1998, 'MG', 31, 'BR', -19.86841000, -47.63352000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q275654'),
(11383, 'Conquista D\'oeste', 2011, 'MT', 31, 'BR', -14.61506000, -59.26741000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2089624'),
(11384, 'Conselheiro Lafaiete', 1998, 'MG', 31, 'BR', -20.66028000, -43.78611000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q570312'),
(11385, 'Conselheiro Mairinck', 2022, 'PR', 31, 'BR', -23.58771000, -50.11629000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q570312'),
(11386, 'Conselheiro Pena', 1998, 'MG', 31, 'BR', -19.17411000, -41.45800000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1792851'),
(11387, 'Consolação', 1998, 'MG', 31, 'BR', -22.54002000, -45.90414000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1805751'),
(11388, 'Constantina', 2001, 'RS', 31, 'BR', -27.70625000, -53.00221000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1996363'),
(11389, 'Contagem', 1998, 'MG', 31, 'BR', -19.93167000, -44.05361000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q271333'),
(11390, 'Contenda', 2022, 'PR', 31, 'BR', -25.71409000, -49.50798000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q271333'),
(11391, 'Contendas do Sincorá', 2002, 'BA', 31, 'BR', -13.79599000, -41.05002000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q271333'),
(11392, 'Coqueiral', 1998, 'MG', 31, 'BR', -21.17947000, -45.43619000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q271333'),
(11393, 'Coqueiro Baixo', 2001, 'RS', 31, 'BR', -29.17479000, -52.10209000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q271333'),
(11394, 'Coqueiro Seco', 2007, 'AL', 31, 'BR', -9.64656000, -35.80938000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q271333'),
(11395, 'Coqueiros do Sul', 2001, 'RS', 31, 'BR', -28.13683000, -52.76136000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q271333'),
(11396, 'Coração de Jesus', 1998, 'MG', 31, 'BR', -16.68619000, -44.36279000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1779901'),
(11397, 'Coração de Maria', 2002, 'BA', 31, 'BR', -12.23333000, -38.75000000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1762086'),
(11398, 'Corbélia', 2022, 'PR', 31, 'BR', -24.79889000, -53.30667000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1804001'),
(11399, 'Cordeiro', 1997, 'RJ', 31, 'BR', -22.02861000, -42.36083000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q534937'),
(11400, 'Cordeiros', 2002, 'BA', 31, 'BR', -14.99846000, -41.89749000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q534937'),
(11401, 'Cordeirópolis', 2021, 'SP', 31, 'BR', -22.48194000, -47.45667000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1750107'),
(11402, 'Cordilheira Alta', 2014, 'SC', 31, 'BR', -26.97554000, -52.63305000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1750107'),
(11403, 'Cordisburgo', 1998, 'MG', 31, 'BR', -19.09925000, -44.16458000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1750107'),
(11404, 'Cordislândia', 1998, 'MG', 31, 'BR', -21.78750000, -45.67156000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1750107'),
(11405, 'Coreaú', 2016, 'CE', 31, 'BR', -3.68827000, -40.72725000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q2027322'),
(11406, 'Coremas', 2005, 'PB', 31, 'BR', -7.01444000, -37.94583000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1843416'),
(11407, 'Corguinho', 2010, 'MS', 31, 'BR', -19.81861000, -55.00199000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2012367'),
(11408, 'Coribe', 2002, 'BA', 31, 'BR', -13.72337000, -44.43538000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2012367'),
(11409, 'Corinto', 1998, 'MG', 31, 'BR', -18.35246000, -44.60589000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22062630'),
(11410, 'Cornélio Procópio', 2022, 'PR', 31, 'BR', -23.18111000, -50.64667000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q2007572'),
(11411, 'Coroaci', 1998, 'MG', 31, 'BR', -18.61187000, -42.25835000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2007572'),
(11412, 'Coroados', 2021, 'SP', 31, 'BR', -21.37115000, -50.31271000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1761110'),
(11413, 'Coroatá', 2015, 'MA', 31, 'BR', -4.13000000, -44.12417000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q967552'),
(11414, 'Coromandel', 1998, 'MG', 31, 'BR', -18.40456000, -47.15161000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22062628'),
(11415, 'Coronel Barros', 2001, 'RS', 31, 'BR', -28.39445000, -54.06367000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22062628'),
(11416, 'Coronel Bicaco', 2001, 'RS', 31, 'BR', -27.79949000, -53.65310000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22062628'),
(11417, 'Coronel Domingos Soares', 2022, 'PR', 31, 'BR', -26.18290000, -51.97777000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22062628'),
(11418, 'Coronel Ezequiel', 2019, 'RN', 31, 'BR', -6.31992000, -36.22997000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q22062628'),
(11419, 'Coronel Fabriciano', 1998, 'MG', 31, 'BR', -19.51861000, -42.62889000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q80004'),
(11420, 'Coronel Freitas', 2014, 'SC', 31, 'BR', -26.89431000, -52.76329000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q80004'),
(11421, 'Coronel José Dias', 2008, 'PI', 31, 'BR', -9.07216000, -42.27241000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q80004'),
(11422, 'Coronel João Pessoa', 2019, 'RN', 31, 'BR', -6.26217000, -38.41730000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q80004'),
(11423, 'Coronel João Sá', 2002, 'BA', 31, 'BR', -10.38878000, -37.94281000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q80004'),
(11424, 'Coronel Macedo', 2021, 'SP', 31, 'BR', -23.62092000, -49.30305000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q80004'),
(11425, 'Coronel Martins', 2014, 'SC', 31, 'BR', -26.54773000, -52.67392000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q80004'),
(11426, 'Coronel Murta', 1998, 'MG', 31, 'BR', -16.58925000, -42.19742000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q80004'),
(11427, 'Coronel Pacheco', 1998, 'MG', 31, 'BR', -21.60865000, -43.29006000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q80004'),
(11428, 'Coronel Pilar', 2001, 'RS', 31, 'BR', -29.25977000, -51.71821000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1803215'),
(11429, 'Coronel Sapucaia', 2010, 'MS', 31, 'BR', -23.33759000, -55.38899000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1803215'),
(11430, 'Coronel Vivida', 2022, 'PR', 31, 'BR', -25.97972000, -52.56778000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1803573'),
(11431, 'Coronel Xavier Chaves', 1998, 'MG', 31, 'BR', -21.02869000, -44.20221000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1803573'),
(11432, 'Corrego Grande', 2014, 'SC', 31, 'BR', -27.60141000, -48.50593000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1803573'),
(11433, 'Correia Pinto', 2014, 'SC', 31, 'BR', -27.58472000, -50.36111000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1764914'),
(11434, 'Corrente', 2008, 'PI', 31, 'BR', -10.47219000, -45.04691000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2662106'),
(11435, 'Correntes', 2006, 'PE', 31, 'BR', -9.13378000, -36.32033000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q776802'),
(11436, 'Correntina', 2002, 'BA', 31, 'BR', -13.34333000, -44.63667000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1061205'),
(11437, 'Cortês', 2006, 'PE', 31, 'BR', -8.44254000, -35.53378000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1061205'),
(11438, 'Corumbataí', 2021, 'SP', 31, 'BR', -22.21698000, -47.59961000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1061205'),
(11439, 'Corumbataí do Sul', 2022, 'PR', 31, 'BR', -24.12007000, -52.14622000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1061205'),
(11440, 'Corumbaíba', 2000, 'GO', 31, 'BR', -18.16664000, -48.53205000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1061205'),
(11441, 'Corumbiara', 2013, 'RO', 31, 'BR', -12.87592000, -61.11993000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1061205'),
(11442, 'Corumbá', 2010, 'MS', 31, 'BR', -19.00917000, -57.65333000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q271215'),
(11443, 'Corumbá de Goiás', 2000, 'GO', 31, 'BR', -15.92190000, -48.62979000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q271215'),
(11444, 'Corupá', 2014, 'SC', 31, 'BR', -26.42528000, -49.24306000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1020408'),
(11445, 'Coruripe', 2007, 'AL', 31, 'BR', -10.12556000, -36.17556000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1796426'),
(11446, 'Cosmorama', 2021, 'SP', 31, 'BR', -20.40934000, -49.74905000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1796426'),
(11447, 'Cosmópolis', 2021, 'SP', 31, 'BR', -22.64583000, -47.19611000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1754569'),
(11448, 'Costa Marques', 2013, 'RO', 31, 'BR', -12.06168000, -64.07323000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1754569'),
(11449, 'Costa Rica', 2010, 'MS', 31, 'BR', -18.63739000, -53.27361000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1024894'),
(11450, 'Costeira do Pirajubae', 2014, 'SC', 31, 'BR', -27.63586000, -48.52120000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1024894'),
(11451, 'Cotegipe', 2002, 'BA', 31, 'BR', -11.58844000, -44.17000000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1024894'),
(11452, 'Cotia', 2021, 'SP', 31, 'BR', -23.60389000, -46.91917000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q669305'),
(11453, 'Cotiporã', 2001, 'RS', 31, 'BR', -29.00719000, -51.69528000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q669305'),
(11454, 'Cotriguaçu', 2011, 'MT', 31, 'BR', -9.50334000, -58.79134000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q2013031'),
(11455, 'Couto de Magalhães', 2020, 'TO', 31, 'BR', -8.50138000, -49.17287000, '2019-10-05 22:35:02', '2025-04-28 16:30:21', 1, 'Q2013031'),
(11456, 'Couto de Magalhães de Minas', 1998, 'MG', 31, 'BR', -18.11856000, -43.41813000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q2013031'),
(11457, 'Coxilha', 2001, 'RS', 31, 'BR', -28.10327000, -52.36576000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2013031'),
(11458, 'Coxim', 2010, 'MS', 31, 'BR', -18.50667000, -54.76000000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1793013'),
(11459, 'Coxixola', 2005, 'PB', 31, 'BR', -7.66749000, -36.61824000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1793013'),
(11460, 'Crateús', 2016, 'CE', 31, 'BR', -5.25084000, -40.74335000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q2021123'),
(11461, 'Crato', 2016, 'CE', 31, 'BR', -7.14714000, -39.47132000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1004897'),
(11462, 'Cravinhos', 2021, 'SP', 31, 'BR', -21.34028000, -47.72944000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1798797'),
(11463, 'Cravolândia', 2002, 'BA', 31, 'BR', -13.47018000, -39.75493000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1798797'),
(11464, 'Craíbas', 2007, 'AL', 31, 'BR', -9.60115000, -36.80273000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1798797'),
(11465, 'Criciúma', 2014, 'SC', 31, 'BR', -28.67750000, -49.36972000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1439157'),
(11466, 'Crissiumal', 2001, 'RS', 31, 'BR', -27.48087000, -54.14173000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1439157'),
(11467, 'Cristais', 1998, 'MG', 31, 'BR', -20.80957000, -45.52448000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1439157'),
(11468, 'Cristais Paulista', 2021, 'SP', 31, 'BR', -20.36538000, -47.37300000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1439157'),
(11469, 'Cristal', 2001, 'RS', 31, 'BR', -31.02454000, -52.02929000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2662293'),
(11470, 'Cristal do Sul', 2001, 'RS', 31, 'BR', -27.42515000, -53.24362000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q2662293'),
(11471, 'Cristalina', 2000, 'GO', 31, 'BR', -16.76769000, -47.61530000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1012541'),
(11472, 'Cristalândia', 2020, 'TO', 31, 'BR', -10.62568000, -49.36863000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1012541'),
(11473, 'Cristalândia do Piauí', 2008, 'PI', 31, 'BR', -10.82007000, -45.11052000, '2019-10-05 22:35:02', '2020-05-01 17:22:37', 1, 'Q1012541'),
(11474, 'Cristiano Otoni', 1998, 'MG', 31, 'BR', -20.83688000, -43.82931000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1012541'),
(11475, 'Cristianópolis', 2000, 'GO', 31, 'BR', -17.19669000, -48.68114000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1012541'),
(11476, 'Cristina', 1998, 'MG', 31, 'BR', -22.21592000, -45.28820000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1796446'),
(11477, 'Cristino Castro', 2008, 'PI', 31, 'BR', -8.69043000, -44.04806000, '2019-10-05 22:35:02', '2019-10-05 22:35:02', 1, 'Q1796446'),
(11478, 'Cristinápolis', 2003, 'SE', 31, 'BR', -11.47556000, -37.75528000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q1802492'),
(11479, 'Cristália', 1998, 'MG', 31, 'BR', -16.72547000, -42.81870000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1802492'),
(11480, 'Cristópolis', 2002, 'BA', 31, 'BR', -12.21789000, -44.24987000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1761896'),
(11481, 'Crisólita', 1998, 'MG', 31, 'BR', -17.24147000, -40.97498000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1761896'),
(11482, 'Crisópolis', 2002, 'BA', 31, 'BR', -11.45532000, -38.13933000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q1761896'),
(11483, 'Crixás', 2000, 'GO', 31, 'BR', -14.54889000, -49.96917000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q177030'),
(11484, 'Crixás do Tocantins', 2020, 'TO', 31, 'BR', -11.16426000, -49.07254000, '2019-10-05 22:35:02', '2020-05-01 17:22:38', 1, 'Q177030'),
(11485, 'Croatá', 2016, 'CE', 31, 'BR', -4.39092000, -40.86795000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q177030'),
(11486, 'Cromínia', 2000, 'GO', 31, 'BR', -17.24762000, -49.32979000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q177030'),
(11487, 'Crucilândia', 1998, 'MG', 31, 'BR', -20.40660000, -44.35991000, '2019-10-05 22:35:02', '2020-05-01 17:22:36', 1, 'Q177030'),
(11488, 'Cruz', 2016, 'CE', 31, 'BR', -2.89619000, -40.33573000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q22059569'),
(11489, 'Cruz Alta', 2001, 'RS', 31, 'BR', -28.64397000, -53.60633000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q985966'),
(11490, 'Cruz Machado', 2022, 'PR', 31, 'BR', -25.91086000, -51.22563000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q985966'),
(11491, 'Cruz das Almas', 2002, 'BA', 31, 'BR', -12.67000000, -39.10194000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1648067'),
(11492, 'Cruz do Espírito Santo', 2005, 'PB', 31, 'BR', -7.14000000, -35.08639000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q512810'),
(11493, 'Cruzaltense', 2001, 'RS', 31, 'BR', -27.64105000, -52.63264000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2007550'),
(11494, 'Cruzeiro', 2021, 'SP', 31, 'BR', -22.57316000, -44.97108000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1003429'),
(11495, 'Cruzeiro da Fortaleza', 1998, 'MG', 31, 'BR', -18.96712000, -46.65779000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1003429'),
(11496, 'Cruzeiro do Iguaçu', 2022, 'PR', 31, 'BR', -25.60303000, -53.12736000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1003429'),
(11497, 'Cruzeiro do Oeste', 2022, 'PR', 31, 'BR', -23.78500000, -53.07333000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2003794'),
(11498, 'Cruzeiro do Sul', 2012, 'AC', 31, 'BR', -7.62759000, -72.67756000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q941136'),
(11499, 'Cruzeiro do Sul', 2022, 'PR', 31, 'BR', -22.97418000, -52.15969000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q941136'),
(11500, 'Cruzeiro do Sul', 2001, 'RS', 31, 'BR', -29.56709000, -52.03042000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q941136'),
(11501, 'Cruzeta', 2019, 'RN', 31, 'BR', -6.32021000, -36.84725000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q941136'),
(11502, 'Cruzmaltina', 2022, 'PR', 31, 'BR', -24.02357000, -51.48481000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q941136'),
(11503, 'Cruzália', 2021, 'SP', 31, 'BR', -22.73707000, -50.75793000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q941136'),
(11504, 'Cruzília', 1998, 'MG', 31, 'BR', -21.73596000, -44.79827000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1756446'),
(11505, 'Cubati', 2005, 'PB', 31, 'BR', -6.86116000, -36.33415000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1756446'),
(11506, 'Cubatão', 2021, 'SP', 31, 'BR', -23.89500000, -46.42528000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q817954'),
(11507, 'Cuiabá', 2011, 'MT', 31, 'BR', -15.41924000, -55.89023000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q170762'),
(11508, 'Cuitegi', 2005, 'PB', 31, 'BR', -6.90639000, -35.53423000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q170762'),
(11509, 'Cuité', 2005, 'PB', 31, 'BR', -6.48361000, -36.15361000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q2064364'),
(11510, 'Cuité de Mamanguape', 2005, 'PB', 31, 'BR', -6.89788000, -35.26042000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q2064364'),
(11511, 'Cujubim', 2013, 'RO', 31, 'BR', -8.97819000, -62.44252000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2064364'),
(11512, 'Cumari', 2000, 'GO', 31, 'BR', -18.31597000, -48.16375000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2064364'),
(11513, 'Cumaru', 2006, 'PE', 31, 'BR', -8.00611000, -35.69722000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2089188'),
(11514, 'Cumaru do Norte', 2009, 'PA', 31, 'BR', -8.39144000, -51.24084000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2089188'),
(11515, 'Cumbe', 2003, 'SE', 31, 'BR', -10.34959000, -37.17665000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q656402'),
(11516, 'Cunha', 2021, 'SP', 31, 'BR', -23.07444000, -44.95972000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1003456'),
(11517, 'Cunha Porã', 2014, 'SC', 31, 'BR', -26.86612000, -53.21020000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1003456'),
(11518, 'Cunhataí', 2014, 'SC', 31, 'BR', -26.97742000, -53.10785000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1759005'),
(11519, 'Cuparaque', 1998, 'MG', 31, 'BR', -18.99664000, -41.13368000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1759005'),
(11520, 'Cupira', 2006, 'PE', 31, 'BR', -8.61667000, -35.95000000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1916653'),
(11521, 'Curaçá', 2002, 'BA', 31, 'BR', -8.99028000, -39.90944000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q677518'),
(11522, 'Curimatá', 2008, 'PI', 31, 'BR', -9.86209000, -44.38994000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q655584'),
(11523, 'Curionópolis', 2009, 'PA', 31, 'BR', -6.18137000, -49.66441000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q2008449'),
(11524, 'Curitiba', 2022, 'PR', 31, 'BR', -25.42778000, -49.27306000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q4361'),
(11525, 'Curitibanos', 2014, 'SC', 31, 'BR', -27.27414000, -50.60962000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1765072'),
(11526, 'Curiúva', 2022, 'PR', 31, 'BR', -24.00386000, -50.46541000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q2007405'),
(11527, 'Currais', 2008, 'PI', 31, 'BR', -8.73985000, -44.86466000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2007405'),
(11528, 'Currais Novos', 2019, 'RN', 31, 'BR', -6.21399000, -36.48400000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1759405'),
(11529, 'Curral Novo do Piauí', 2008, 'PI', 31, 'BR', -7.88978000, -40.76427000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1759405'),
(11530, 'Curral Velho', 2005, 'PB', 31, 'BR', -7.55796000, -38.19847000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2010383'),
(11531, 'Curral de Cima', 2005, 'PB', 31, 'BR', -6.72359000, -35.28596000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2010383'),
(11532, 'Curral de Dentro', 1998, 'MG', 31, 'BR', -15.84632000, -41.75405000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2010383'),
(11533, 'Curralinho', 2009, 'PA', 31, 'BR', -1.58236000, -49.98697000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2010383'),
(11534, 'Curralinhos', 2008, 'PI', 31, 'BR', -5.58488000, -42.86082000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2010383'),
(11535, 'Cururupu', 2015, 'MA', 31, 'BR', -1.82833000, -44.86833000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1886395'),
(11536, 'Curuá', 2009, 'PA', 31, 'BR', -1.73821000, -55.11492000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q2008226'),
(11537, 'Curuçá', 2009, 'PA', 31, 'BR', -0.69787000, -47.86755000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1814514'),
(11538, 'Curvelo', 1998, 'MG', 31, 'BR', -18.78525000, -44.41599000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1749748'),
(11539, 'Curvelândia', 2011, 'MT', 31, 'BR', -15.63715000, -57.84548000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q2066057'),
(11540, 'Custódia', 2006, 'PE', 31, 'BR', -8.12882000, -37.66208000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q2013538'),
(11541, 'Cutias', 1999, 'AP', 31, 'BR', 0.99713000, -50.52041000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2013538'),
(11542, 'Cáceres', 2011, 'MT', 31, 'BR', -16.42263000, -57.71082000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q986526'),
(11543, 'Cássia', 1998, 'MG', 31, 'BR', -20.59294000, -46.91761000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1793175'),
(11544, 'Cássia dos Coqueiros', 2021, 'SP', 31, 'BR', -21.25917000, -47.13783000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1793175'),
(11545, 'Cândido Godói', 2001, 'RS', 31, 'BR', -27.89509000, -54.74128000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1793175'),
(11546, 'Cândido Mendes', 2015, 'MA', 31, 'BR', -1.44667000, -45.71667000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q2068838'),
(11547, 'Cândido Mota', 2021, 'SP', 31, 'BR', -22.74639000, -50.38694000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1761075'),
(11548, 'Cândido Rodrigues', 2021, 'SP', 31, 'BR', -21.34194000, -48.63294000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1761075'),
(11549, 'Cândido Sales', 2002, 'BA', 31, 'BR', -15.46082000, -41.16353000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1761075'),
(11550, 'Cândido de Abreu', 2022, 'PR', 31, 'BR', -24.56694000, -51.33333000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1803993'),
(11551, 'Céu Azul', 2022, 'PR', 31, 'BR', -25.29831000, -53.74848000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1803993'),
(11552, 'Cícero Dantas', 2002, 'BA', 31, 'BR', -10.60000000, -38.38333000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1761858'),
(11553, 'Córrego Danta', 1998, 'MG', 31, 'BR', -19.81349000, -45.96149000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1756093'),
(11554, 'Córrego Fundo', 1998, 'MG', 31, 'BR', -20.45029000, -45.53592000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1756378'),
(11555, 'Córrego Novo', 1998, 'MG', 31, 'BR', -19.82874000, -42.44214000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1791989'),
(11556, 'Córrego do Bom Jesus', 1998, 'MG', 31, 'BR', -22.62998000, -45.99681000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1791989'),
(11557, 'Córrego do Ouro', 2000, 'GO', 31, 'BR', -16.39566000, -50.57320000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1791989'),
(11558, 'Cônego Marinho', 1998, 'MG', 31, 'BR', -14.98755000, -44.60965000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1791989'),
(11559, 'Damianópolis', 2000, 'GO', 31, 'BR', -14.54949000, -46.19367000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1806869'),
(11560, 'Damião', 2005, 'PB', 31, 'BR', -6.67843000, -35.92205000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q663661'),
(11561, 'Damolândia', 2000, 'GO', 31, 'BR', -16.25109000, -49.34505000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q663661'),
(11562, 'Darcinópolis', 2020, 'TO', 31, 'BR', -6.76072000, -47.75687000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q663661'),
(11563, 'Datas', 1998, 'MG', 31, 'BR', -18.48073000, -43.65216000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q663661'),
(11564, 'David Canabarro', 2001, 'RS', 31, 'BR', -28.41776000, -51.81680000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q663661'),
(11565, 'Davinópolis', 2015, 'MA', 31, 'BR', -5.57008000, -47.30640000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q663661'),
(11566, 'Davinópolis', 2000, 'GO', 31, 'BR', -18.13603000, -47.57750000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q663661'),
(11567, 'Delfim Moreira', 1998, 'MG', 31, 'BR', -22.51165000, -45.28972000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1780238'),
(11568, 'Delfinópolis', 1998, 'MG', 31, 'BR', -20.34248000, -46.83952000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1780238'),
(11569, 'Delmiro Gouveia', 2007, 'AL', 31, 'BR', -9.38861000, -37.99917000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1808849'),
(11570, 'Delta', 1998, 'MG', 31, 'BR', -19.93414000, -47.80435000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q22062605'),
(11571, 'Demerval Lobão', 2008, 'PI', 31, 'BR', -5.35833000, -42.67639000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1851653'),
(11572, 'Denise', 2011, 'MT', 31, 'BR', -14.75742000, -56.95115000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1847425'),
(11573, 'Deodápolis', 2010, 'MS', 31, 'BR', -22.08040000, -54.20322000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1847425'),
(11574, 'Deputado Irapuan Pinheiro', 2016, 'CE', 31, 'BR', -5.88737000, -39.25923000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1847425'),
(11575, 'Derrubadas', 2001, 'RS', 31, 'BR', -27.25025000, -53.88415000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1847425'),
(11576, 'Descalvado', 2021, 'SP', 31, 'BR', -21.90389000, -47.61944000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1761089'),
(11577, 'Descanso', 2014, 'SC', 31, 'BR', -26.87384000, -53.48415000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1761089'),
(11578, 'Descoberto', 1998, 'MG', 31, 'BR', -21.45167000, -42.97128000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1792077'),
(11579, 'Desterro', 2005, 'PB', 31, 'BR', -7.29056000, -37.09389000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2009945'),
(11580, 'Desterro de Entre Rios', 1998, 'MG', 31, 'BR', -20.64186000, -44.28135000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2009945'),
(11581, 'Desterro do Melo', 1998, 'MG', 31, 'BR', -21.14290000, -43.52011000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2009945'),
(11582, 'Dezesseis de Novembro', 2001, 'RS', 31, 'BR', -28.19125000, -55.07548000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2009945'),
(11583, 'Diadema', 2021, 'SP', 31, 'BR', -23.68611000, -46.62278000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q651891'),
(11584, 'Diamante', 2005, 'PB', 31, 'BR', -7.41004000, -38.30873000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q651891'),
(11585, 'Diamante d\'Oeste', 2022, 'PR', 31, 'BR', -24.94559000, -54.10349000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1804009'),
(11586, 'Diamante do Norte', 2022, 'PR', 31, 'BR', -22.63752000, -52.87280000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1804009'),
(11587, 'Diamante do Sul', 2022, 'PR', 31, 'BR', -24.98680000, -52.70134000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1804009'),
(11588, 'Diamantina', 1998, 'MG', 31, 'BR', -17.97795000, -43.60415000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q34028'),
(11589, 'Diamantino', 2011, 'MT', 31, 'BR', -14.02239000, -56.77742000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1259020'),
(11590, 'Dianópolis', 2020, 'TO', 31, 'BR', -11.69578000, -46.73544000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1259020'),
(11591, 'Dias d\'Ávila', 2002, 'BA', 31, 'BR', -12.60625000, -38.33717000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1761912'),
(11592, 'Dilermando de Aguiar', 2001, 'RS', 31, 'BR', -29.80603000, -54.15398000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q630661'),
(11593, 'Diogo de Vasconcelos', 1998, 'MG', 31, 'BR', -20.47593000, -43.19094000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2296402'),
(11594, 'Dionísio', 1998, 'MG', 31, 'BR', -19.83679000, -42.68817000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q2296402'),
(11595, 'Dionísio Cerqueira', 2014, 'SC', 31, 'BR', -26.33340000, -53.53403000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q2296402'),
(11596, 'Diorama', 2000, 'GO', 31, 'BR', -16.22318000, -51.34971000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2296402'),
(11597, 'Dirce Reis', 2021, 'SP', 31, 'BR', -20.45591000, -50.62798000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2296402'),
(11598, 'Dirceu Arcoverde', 2008, 'PI', 31, 'BR', -9.33230000, -42.43422000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2296402'),
(11599, 'Divina Pastora', 2003, 'SE', 31, 'BR', -10.67935000, -37.16817000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2296402'),
(11600, 'Divino', 1998, 'MG', 31, 'BR', -20.58996000, -42.17550000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q2296402'),
(11601, 'Divino das Laranjeiras', 1998, 'MG', 31, 'BR', -18.77778000, -41.47972000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1791982'),
(11602, 'Divino de São Lourenço', 2018, 'ES', 31, 'BR', -20.59751000, -41.72185000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1791982'),
(11603, 'Divinolândia', 2021, 'SP', 31, 'BR', -21.66459000, -46.70450000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1791982'),
(11604, 'Divinolândia de Minas', 1998, 'MG', 31, 'BR', -18.77559000, -42.57075000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1791982'),
(11605, 'Divinésia', 1998, 'MG', 31, 'BR', -20.99465000, -42.99513000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1791982'),
(11606, 'Divinópolis', 1998, 'MG', 31, 'BR', -20.14355000, -44.89065000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q926606'),
(11607, 'Divinópolis de Goiás', 2000, 'GO', 31, 'BR', -13.22378000, -46.52704000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q926606'),
(11608, 'Divinópolis do Tocantins', 2020, 'TO', 31, 'BR', -9.65731000, -49.38737000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q926606'),
(11609, 'Divisa Alegre', 1998, 'MG', 31, 'BR', -15.69447000, -41.39107000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q926606'),
(11610, 'Divisa Nova', 1998, 'MG', 31, 'BR', -21.52382000, -46.24417000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q926606'),
(11611, 'Divisópolis', 1998, 'MG', 31, 'BR', -15.76740000, -40.92393000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q926606'),
(11612, 'Dobrada', 2021, 'SP', 31, 'BR', -21.51531000, -48.37033000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q926606'),
(11613, 'Dois Córregos', 2021, 'SP', 31, 'BR', -22.36611000, -48.38028000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q685613'),
(11614, 'Dois Irmãos', 2001, 'RS', 31, 'BR', -29.61158000, -51.09333000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q685613'),
(11615, 'Dois Irmãos das Missões', 2001, 'RS', 31, 'BR', -27.68117000, -53.51477000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q685613'),
(11616, 'Dois Irmãos do Buriti', 2010, 'MS', 31, 'BR', -20.57189000, -55.36839000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q685613'),
(11617, 'Dois Irmãos do Tocantins', 2020, 'TO', 31, 'BR', -9.29074000, -49.15573000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q685613'),
(11618, 'Dois Lajeados', 2001, 'RS', 31, 'BR', -28.97092000, -51.84970000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q685613'),
(11619, 'Dois Riachos', 2007, 'AL', 31, 'BR', -9.39250000, -37.10056000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q988800'),
(11620, 'Dois Vizinhos', 2022, 'PR', 31, 'BR', -25.73361000, -53.05722000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1803648'),
(11621, 'Dolcinópolis', 2021, 'SP', 31, 'BR', -20.11666000, -50.52771000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1803648'),
(11622, 'Dom Aquino', 2011, 'MT', 31, 'BR', -15.72098000, -54.83043000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1803648'),
(11623, 'Dom Basílio', 2002, 'BA', 31, 'BR', -13.79717000, -41.70877000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1803648'),
(11624, 'Dom Bosco', 1998, 'MG', 31, 'BR', -16.81133000, -46.28457000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1803648'),
(11625, 'Dom Cavati', 1998, 'MG', 31, 'BR', -19.38835000, -42.09365000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1755986'),
(11626, 'Dom Eliseu', 2009, 'PA', 31, 'BR', -4.08267000, -47.89052000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1755986'),
(11627, 'Dom Expedito Lopes', 2008, 'PI', 31, 'BR', -6.95806000, -41.71629000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1755986'),
(11628, 'Dom Feliciano', 2001, 'RS', 31, 'BR', -30.61390000, -52.19315000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1755986'),
(11629, 'Dom Inocêncio', 2008, 'PI', 31, 'BR', -8.88480000, -41.72000000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1755986'),
(11630, 'Dom Joaquim', 1998, 'MG', 31, 'BR', -18.92927000, -43.26602000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1755986'),
(11631, 'Dom Macedo Costa', 2002, 'BA', 31, 'BR', -12.93379000, -39.14621000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1755986'),
(11632, 'Dom Pedrito', 2001, 'RS', 31, 'BR', -30.98278000, -54.67306000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1749875'),
(11633, 'Dom Pedro', 2015, 'MA', 31, 'BR', -5.03749000, -44.43857000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1823871'),
(11634, 'Dom Pedro de Alcântara', 2001, 'RS', 31, 'BR', -29.35681000, -49.87288000, '2019-10-05 22:35:03', '2020-05-01 17:22:38', 1, 'Q1823871'),
(11635, 'Dom Silvério', 1998, 'MG', 31, 'BR', -20.12728000, -42.94651000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1823871'),
(11636, 'Dom Viçoso', 1998, 'MG', 31, 'BR', -22.23185000, -45.14880000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1823871'),
(11637, 'Domingos Martins', 2018, 'ES', 31, 'BR', -20.36333000, -40.65917000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q616475'),
(11638, 'Domingos Mourão', 2008, 'PI', 31, 'BR', -4.19141000, -41.34833000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q616475'),
(11639, 'Dona Emma', 2014, 'SC', 31, 'BR', -26.97694000, -49.81076000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q616475'),
(11640, 'Dona Eusébia', 1998, 'MG', 31, 'BR', -21.32337000, -42.80829000, '2019-10-05 22:35:03', '2020-05-01 17:22:36', 1, 'Q1756805'),
(11641, 'Dona Francisca', 2001, 'RS', 31, 'BR', -29.58683000, -53.33964000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1756805'),
(11642, 'Dona Inês', 2005, 'PB', 31, 'BR', -6.61354000, -35.62654000, '2019-10-05 22:35:03', '2020-05-01 17:22:37', 1, 'Q1814531'),
(11643, 'Dores de Campos', 1998, 'MG', 31, 'BR', -21.11373000, -43.99358000, '2019-10-05 22:35:03', '2019-10-05 22:35:03', 1, 'Q1814531'),
(11644, 'Dores de Guanhães', 1998, 'MG', 31, 'BR', -19.04398000, -42.92816000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1790727'),
(11645, 'Dores do Indaiá', 1998, 'MG', 31, 'BR', -19.47427000, -45.54046000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q1757114'),
(11646, 'Dores do Rio Preto', 2018, 'ES', 31, 'BR', -20.63511000, -41.81208000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1757114'),
(11647, 'Dores do Turvo', 1998, 'MG', 31, 'BR', -21.02779000, -43.16365000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1757114'),
(11648, 'Doresópolis', 1998, 'MG', 31, 'BR', -20.29693000, -45.86864000, '2019-10-05 22:35:04', '2020-05-01 17:22:37', 1, 'Q1756593'),
(11649, 'Dormentes', 2006, 'PE', 31, 'BR', -8.43020000, -40.59717000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1756593'),
(11650, 'Douradina', 2022, 'PR', 31, 'BR', -23.34040000, -53.27489000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q2472540'),
(11651, 'Douradina', 2010, 'MS', 31, 'BR', -21.98525000, -54.58145000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1805588'),
(11652, 'Dourado', 2021, 'SP', 31, 'BR', -22.11665000, -48.35204000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1805588'),
(11653, 'Douradoquara', 1998, 'MG', 31, 'BR', -18.44204000, -47.61478000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q1805588'),
(11654, 'Dourados', 2010, 'MS', 31, 'BR', -22.22111000, -54.80556000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651874'),
(11655, 'Doutor Camargo', 2022, 'PR', 31, 'BR', -23.56158000, -52.22341000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651874'),
(11656, 'Doutor Maurício Cardoso', 2001, 'RS', 31, 'BR', -27.49540000, -54.36400000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q651874'),
(11657, 'Doutor Pedrinho', 2014, 'SC', 31, 'BR', -26.71142000, -49.55827000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651874'),
(11658, 'Doutor Ricardo', 2001, 'RS', 31, 'BR', -29.10117000, -51.97440000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651874'),
(11659, 'Doutor Severiano', 2019, 'RN', 31, 'BR', -6.11208000, -38.39660000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651874'),
(11660, 'Doutor Ulysses', 2022, 'PR', 31, 'BR', -24.63500000, -49.40020000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651874'),
(11661, 'Doverlândia', 2000, 'GO', 31, 'BR', -16.87767000, -52.49917000, '2019-10-05 22:35:04', '2020-05-01 17:22:36', 1, 'Q651874'),
(11662, 'Dracena', 2021, 'SP', 31, 'BR', -21.57936000, -51.59598000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q651874'),
(11663, 'Duartina', 2021, 'SP', 31, 'BR', -22.41444000, -49.40389000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q303413'),
(11664, 'Duas Barras', 1997, 'RJ', 31, 'BR', -22.07281000, -42.52467000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q303413'),
(11665, 'Duas Estradas', 2005, 'PB', 31, 'BR', -6.71946000, -35.39737000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q303413'),
(11666, 'Dueré', 2020, 'TO', 31, 'BR', -11.35537000, -49.46137000, '2019-10-05 22:35:04', '2020-05-01 17:22:38', 1, 'Q303413'),
(11667, 'Dumont', 2021, 'SP', 31, 'BR', -21.24438000, -47.99598000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q270048'),
(11668, 'Duque Bacelar', 2015, 'MA', 31, 'BR', -4.11143000, -43.03176000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q270048'),
(11669, 'Duque de Caxias', 1997, 'RJ', 31, 'BR', -22.78556000, -43.31167000, '2019-10-05 22:35:04', '2019-10-05 22:35:04', 1, 'Q193021');

