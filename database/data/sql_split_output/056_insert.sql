INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(27833, 'Niederbergkirchen', 3009, 'BY', 82, 'DE', 48.31667000, 12.50000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q625888'),
(27834, 'Niederbiel', 3018, 'HE', 82, 'DE', 50.55505000, 8.39845000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q1463438'),
(27835, '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', 3019, 'RP', 82, 'DE', 50.53051000, 7.42099000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q566747'),
(27836, '<PERSON>ede<PERSON>unnersdorf', 3021, '<PERSON><PERSON>', 82, '<PERSON>', 51.05398000, 14.65830000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q502508'),
(27837, '<PERSON><PERSON><PERSON><PERSON>', 3021, 'SN', 82, 'DE', 50.72623000, 12.78587000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q502508'),
(27838, '<PERSON><PERSON><PERSON>rf<PERSON>en', 3018, 'HE', 82, 'DE', 50.19415000, 8.80005000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q622117'),
(27839, 'Niederdorla', 3015, 'TH', 82, 'DE', 51.16025000, 10.44820000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q638063'),
(27840, 'Niederdreisbach', 3019, 'RP', 82, 'DE', 50.74955000, 7.92352000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q554300'),
(27841, 'Niederdürenbach', 3019, 'RP', 82, 'DE', 50.45000000, 7.18333000, '2019-10-05 22:41:22', '2020-05-01 17:22:49', 1, 'Q567276'),
(27842, 'Niederelbert', 3019, 'RP', 82, 'DE', 50.40775000, 7.80990000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q642780'),
(27843, 'Niedererbach', 3019, 'RP', 82, 'DE', 50.42610000, 7.97425000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q560306'),
(27844, 'Niedereschach', 3006, 'BW', 82, 'DE', 48.13333000, 8.53333000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q81751'),
(27845, 'Niederfell', 3019, 'RP', 82, 'DE', 50.28333000, 7.46667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q553445'),
(27846, 'Niederfischbach', 3019, 'RP', 82, 'DE', 50.85000000, 7.86667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q553518'),
(27847, 'Niederfrohna', 3021, 'SN', 82, 'DE', 50.90000000, 12.71667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q17125'),
(27848, 'Niederfüllbach', 3009, 'BY', 82, 'DE', 50.22002000, 10.99086000, '2019-10-05 22:41:22', '2020-05-01 17:22:47', 1, 'Q532430'),
(27849, 'Niedergörsdorf', 3013, 'BB', 82, 'DE', 51.97943000, 12.98541000, '2019-10-05 22:41:22', '2020-05-01 17:22:48', 1, 'Q634423'),
(27850, 'Niederkassel', 3017, 'NW', 82, 'DE', 50.81503000, 7.03777000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32187966'),
(27851, 'Niederkirchen', 3019, 'RP', 82, 'DE', 49.58333000, 7.70000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32187966'),
(27852, 'Niederkirchen bei Deidesheim', 3019, 'RP', 82, 'DE', 49.41500000, 8.21000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q566001'),
(27853, 'Niederklein', 3018, 'HE', 82, 'DE', 50.79400000, 8.99694000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q566001'),
(27854, 'Niederkrüchten', 3017, 'NW', 82, 'DE', 51.20000000, 6.21667000, '2019-10-05 22:41:22', '2020-05-01 17:22:49', 1, 'Q163837'),
(27855, 'Niederlangen', 3008, 'NI', 82, 'DE', 52.85712000, 7.28199000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q580549'),
(27856, 'Niederlauer', 3009, 'BY', 82, 'DE', 50.29411000, 10.17712000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q558860'),
(27857, 'Niedermerz', 3017, 'NW', 82, 'DE', 50.88151000, 6.26651000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32188173'),
(27858, 'Niedermohr', 3019, 'RP', 82, 'DE', 49.45899000, 7.46955000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q567201'),
(27859, 'Niedermurach', 3009, 'BY', 82, 'DE', 49.44999000, 12.37610000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q546498'),
(27860, 'Niedernberg', 3009, 'BY', 82, 'DE', 49.91222000, 9.13694000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q505671'),
(27861, 'Niederndodeleben', 3011, 'ST', 82, 'DE', 52.13416000, 11.50085000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q687995'),
(27862, 'Niederneisen', 3019, 'RP', 82, 'DE', 50.33333000, 8.05000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q645416'),
(27863, 'Niedernhall', 3006, 'BW', 82, 'DE', 49.29516000, 9.61604000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q386998'),
(27864, 'Niedernhausen', 3018, 'HE', 82, 'DE', 50.16307000, 8.31338000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q427360'),
(27865, 'Niedernwöhren', 3008, 'NI', 82, 'DE', 52.35214000, 9.14788000, '2019-10-05 22:41:22', '2020-05-01 17:22:48', 1, 'Q267831'),
(27866, 'Niederorschel', 3015, 'TH', 82, 'DE', 51.37222000, 10.42372000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q554925'),
(27867, 'Niederrad', 3018, 'HE', 82, 'DE', 50.08309000, 8.62852000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q567307'),
(27868, 'Niederrieden', 3009, 'BY', 82, 'DE', 48.05760000, 10.18321000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q547576'),
(27869, 'Niederroßla', 3015, 'TH', 82, 'DE', 51.03333000, 11.48333000, '2019-10-05 22:41:22', '2020-05-01 17:22:50', 1, 'Q588635'),
(27870, 'Niedersachswerfen', 3015, 'TH', 82, 'DE', 51.55062000, 10.76594000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q588635'),
(27871, 'Niederschöna', 3021, 'SN', 82, 'DE', 50.96671000, 13.41910000, '2019-10-05 22:41:22', '2020-05-01 17:22:49', 1, 'Q991571'),
(27872, 'Niederschönenfeld', 3009, 'BY', 82, 'DE', 48.71667000, 10.93333000, '2019-10-05 22:41:22', '2020-05-01 17:22:47', 1, 'Q504486'),
(27873, 'Niederschöneweide', 3010, 'BE', 82, 'DE', 52.45564000, 13.51554000, '2019-10-05 22:41:22', '2020-05-01 17:22:48', 1, 'Q644062'),
(27874, 'Niederschönhausen', 3010, 'BE', 82, 'DE', 52.58483000, 13.40272000, '2019-10-05 22:41:22', '2020-05-01 17:22:48', 1, 'Q512959'),
(27875, 'Niederstetten', 3006, 'BW', 82, 'DE', 49.40000000, 9.91944000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q61713'),
(27876, 'Niederstotzingen', 3006, 'BW', 82, 'DE', 48.54127000, 10.23505000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q584682'),
(27877, 'Niederstriegis', 3021, 'SN', 82, 'DE', 51.08243000, 13.14925000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q71017'),
(27878, 'Niedertaufkirchen', 3009, 'BY', 82, 'DE', 48.33333000, 12.55000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q514327'),
(27879, 'Niederviehbach', 3009, 'BY', 82, 'DE', 48.61667000, 12.38333000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32188954'),
(27880, 'Niederwangen', 3006, 'BW', 82, 'DE', 47.67192000, 9.79880000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32188954'),
(27881, 'Niederwerrn', 3009, 'BY', 82, 'DE', 50.06149000, 10.18270000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q589503'),
(27882, 'Niederwerth', 3019, 'RP', 82, 'DE', 50.40000000, 7.61667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q652134'),
(27883, 'Niederwiesa', 3021, 'SN', 82, 'DE', 50.86667000, 13.01667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q71021'),
(27884, 'Niederwinkling', 3009, 'BY', 82, 'DE', 48.88333000, 12.80000000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q559939'),
(27885, 'Niederzier', 3017, 'NW', 82, 'DE', 50.88333000, 6.46667000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32189299'),
(27886, 'Niederzimmern', 3015, 'TH', 82, 'DE', 51.00476000, 11.19028000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32189299'),
(27887, 'Niederzissen', 3019, 'RP', 82, 'DE', 50.45876000, 7.21810000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q647234'),
(27888, 'Niefern-Öschelbronn', 3006, 'BW', 82, 'DE', 48.91667000, 8.78333000, '2019-10-05 22:41:22', '2020-05-01 17:22:47', 1, 'Q647234'),
(27889, 'Nieheim', 3017, 'NW', 82, 'DE', 51.80499000, 9.11302000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32189440'),
(27890, 'Niemberg', 3011, 'ST', 82, 'DE', 51.55035000, 12.09089000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q689545'),
(27891, 'Niemegk', 3013, 'BB', 82, 'DE', 52.07388000, 12.68947000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q635620'),
(27892, 'Nienburg', 3008, 'NI', 82, 'DE', 52.64610000, 9.22086000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32189647'),
(27893, 'Nienburg/Saale', 3011, 'ST', 82, 'DE', 51.83747000, 11.76979000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32189647'),
(27894, 'Nienhagen', 3007, 'MV', 82, 'DE', 54.14869000, 12.17434000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q32189647'),
(27895, 'Nienstädt', 3008, 'NI', 82, 'DE', 52.29242000, 9.16440000, '2019-10-05 22:41:22', '2020-05-01 17:22:48', 1, 'Q635977'),
(27896, 'Niepars', 3007, 'MV', 82, 'DE', 54.31447000, 12.92447000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q671615'),
(27897, 'Nierstein', 3019, 'RP', 82, 'DE', 49.87003000, 8.33647000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q680403'),
(27898, 'Niesky', 3021, 'SN', 82, 'DE', 51.29241000, 14.82107000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q165160'),
(27899, 'Nikolassee', 3010, 'BE', 82, 'DE', 52.43440000, 13.20095000, '2019-10-05 22:41:22', '2019-10-05 22:41:22', 1, 'Q576653'),
(27900, 'Nindorf', 3005, 'SH', 82, 'DE', 54.08333000, 9.11667000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q576653'),
(27901, 'Nippes', 3017, 'NW', 82, 'DE', 50.96545000, 6.95314000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q576653'),
(27902, 'Nister', 3019, 'RP', 82, 'DE', 50.67583000, 7.83833000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q576653'),
(27903, 'Nittel', 3019, 'RP', 82, 'DE', 49.65000000, 6.45000000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q594609'),
(27904, 'Nittenau', 3009, 'BY', 82, 'DE', 49.19425000, 12.26741000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q529176'),
(27905, 'Nittendorf', 3009, 'BY', 82, 'DE', 49.02459000, 11.96126000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q491658'),
(27906, 'Nobitz', 3015, 'TH', 82, 'DE', 50.97621000, 12.48605000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q542641'),
(27907, 'Nohfelden', 3020, 'SL', 82, 'DE', 49.58693000, 7.14283000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q570209'),
(27908, 'Nohra', 3015, 'TH', 82, 'DE', 50.96136000, 11.25971000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q570209'),
(27909, 'Nonnenhorn', 3009, 'BY', 82, 'DE', 47.57386000, 9.61038000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q517513'),
(27910, 'Nonnweiler', 3020, 'SL', 82, 'DE', 49.60762000, 6.96986000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q550261'),
(27911, 'Norddeich', 3008, 'NI', 82, 'DE', 53.61349000, 7.16043000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q896276'),
(27912, 'Norden', 3008, 'NI', 82, 'DE', 53.59552000, 7.20639000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q32192050'),
(27913, 'Nordendorf', 3009, 'BY', 82, 'DE', 48.59478000, 10.83183000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q505111'),
(27914, 'Nordenham', 3008, 'NI', 82, 'DE', 53.48610000, 8.48093000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q503711'),
(27915, 'Norderney', 3008, 'NI', 82, 'DE', 53.70828000, 7.15819000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q503711'),
(27916, 'Norderstedt', 3005, 'SH', 82, 'DE', 53.68590000, 9.98041000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q3928'),
(27917, 'Nordhalben', 3009, 'BY', 82, 'DE', 50.37411000, 11.50992000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q505015'),
(27918, 'Nordhastedt', 3005, 'SH', 82, 'DE', 54.16667000, 9.18333000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q528348'),
(27919, 'Nordhausen', 3015, 'TH', 82, 'DE', 51.50180000, 10.79570000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q7033'),
(27920, 'Nordheim', 3009, 'BY', 82, 'DE', 49.85870000, 10.18545000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q32192624'),
(27921, 'Nordheim', 3006, 'BW', 82, 'DE', 49.10861000, 9.12778000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q506050'),
(27922, 'Nordholz', 3008, 'NI', 82, 'DE', 53.78448000, 8.61354000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q508015'),
(27923, 'Nordhorn', 3008, 'NI', 82, 'DE', 52.43081000, 7.06833000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q4133'),
(27924, 'Nordkirchen', 3017, 'NW', 82, 'DE', 51.73827000, 7.52197000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q14923'),
(27925, 'Nordleda', 3008, 'NI', 82, 'DE', 53.76667000, 8.83333000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q624524'),
(27926, 'Nordrach', 3006, 'BW', 82, 'DE', 48.40000000, 8.08333000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q533652'),
(27927, 'Nordstemmen', 3008, 'NI', 82, 'DE', 52.16196000, 9.78350000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q555657'),
(27928, 'Nordwalde', 3017, 'NW', 82, 'DE', 52.08333000, 7.48333000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q182248'),
(27929, 'Norheim', 3019, 'RP', 82, 'DE', 49.81091000, 7.81478000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q651363'),
(27930, 'Northeim', 3008, 'NI', 82, 'DE', 51.70662000, 9.99997000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q498575'),
(27931, 'Nortmoor', 3008, 'NI', 82, 'DE', 53.24608000, 7.57178000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q696277'),
(27932, 'Nortorf', 3005, 'SH', 82, 'DE', 54.16738000, 9.85437000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q573031'),
(27933, 'Nortrup', 3008, 'NI', 82, 'DE', 52.61503000, 7.86072000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q631938'),
(27934, 'Nossen', 3021, 'SN', 82, 'DE', 51.05798000, 13.29652000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q8751'),
(27935, 'Nostorf', 3007, 'MV', 82, 'DE', 53.40656000, 10.65408000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q691328'),
(27936, 'Nottensdorf', 3008, 'NI', 82, 'DE', 53.48333000, 9.60000000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q691328'),
(27937, 'Nottuln', 3017, 'NW', 82, 'DE', 51.93333000, 7.35000000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q32195124'),
(27938, 'Notzingen', 3006, 'BW', 82, 'DE', 48.67076000, 9.45721000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q80636'),
(27939, 'Nudersdorf', 3011, 'ST', 82, 'DE', 51.93025000, 12.59554000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q542116'),
(27940, 'Nufringen', 3006, 'BW', 82, 'DE', 48.62253000, 8.89009000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q557825'),
(27941, 'Nusse', 3005, 'SH', 82, 'DE', 53.65654000, 10.58391000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q514600'),
(27942, 'Nußdorf', 3009, 'BY', 82, 'DE', 47.90564000, 12.59608000, '2019-10-05 22:41:23', '2020-05-01 17:22:47', 1, 'Q251127'),
(27943, 'Nußdorf am Inn', 3009, 'BY', 82, 'DE', 47.74232000, 12.15611000, '2019-10-05 22:41:23', '2020-05-01 17:22:47', 1, 'Q536462'),
(27944, 'Nußloch', 3006, 'BW', 82, 'DE', 49.32389000, 8.69556000, '2019-10-05 22:41:23', '2020-05-01 17:22:47', 1, 'Q81979'),
(27945, 'Nöbdenitz', 3015, 'TH', 82, 'DE', 50.86667000, 12.28333000, '2019-10-05 22:41:23', '2020-05-01 17:22:50', 1, 'Q47040'),
(27946, 'Nördlingen', 3009, 'BY', 82, 'DE', 48.85122000, 10.48868000, '2019-10-05 22:41:23', '2020-05-01 17:22:47', 1, 'Q489902'),
(27947, 'Nörten-Hardenberg', 3008, 'NI', 82, 'DE', 51.62878000, 9.93593000, '2019-10-05 22:41:23', '2020-05-01 17:22:48', 1, 'Q503414'),
(27948, 'Nörtershausen', 3019, 'RP', 82, 'DE', 50.21667000, 7.48333000, '2019-10-05 22:41:23', '2020-05-01 17:22:49', 1, 'Q648442'),
(27949, 'Nörvenich', 3017, 'NW', 82, 'DE', 50.80604000, 6.63952000, '2019-10-05 22:41:23', '2020-05-01 17:22:49', 1, 'Q200068'),
(27950, 'Nübbel', 3005, 'SH', 82, 'DE', 54.26667000, 9.61667000, '2019-10-05 22:41:23', '2020-05-01 17:22:49', 1, 'Q200068'),
(27951, 'Nüdlingen', 3009, 'BY', 82, 'DE', 50.22063000, 10.12296000, '2019-10-05 22:41:23', '2020-05-01 17:22:47', 1, 'Q502978'),
(27952, 'Nümbrecht', 3017, 'NW', 82, 'DE', 50.90427000, 7.54063000, '2019-10-05 22:41:23', '2020-05-01 17:22:49', 1, 'Q11052'),
(27953, 'Nünchritz', 3021, 'SN', 82, 'DE', 51.29993000, 13.38555000, '2019-10-05 22:41:23', '2020-05-01 17:22:49', 1, 'Q8756'),
(27954, 'Nürnberg', 3009, 'BY', 82, 'DE', 49.45421000, 11.07752000, '2019-10-05 22:41:23', '2020-05-01 17:22:47', 1, 'Q2090'),
(27955, 'Nürtingen', 3006, 'BW', 82, 'DE', 48.62565000, 9.34203000, '2019-10-05 22:41:23', '2020-05-01 17:22:47', 1, 'Q14853'),
(27956, 'Nützen', 3005, 'SH', 82, 'DE', 53.86667000, 9.91667000, '2019-10-05 22:41:23', '2020-05-01 17:22:49', 1, 'Q14853'),
(27957, 'Ober-Flörsheim', 3019, 'RP', 82, 'DE', 49.68306000, 8.15528000, '2019-10-05 22:41:23', '2020-05-01 17:22:49', 1, 'Q639922'),
(27958, 'Ober-Mörlen', 3018, 'HE', 82, 'DE', 50.37350000, 8.69087000, '2019-10-05 22:41:23', '2020-05-01 17:22:48', 1, 'Q633608'),
(27959, 'Ober-Olm', 3019, 'RP', 82, 'DE', 49.93722000, 8.18889000, '2019-10-05 22:41:23', '2019-10-05 22:41:23', 1, 'Q22988'),
(27960, 'Ober-Ramstadt', 3018, 'HE', 82, 'DE', 49.83078000, 8.74887000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q569633'),
(27961, 'Ober-Saulheim', 3019, 'RP', 82, 'DE', 49.86376000, 8.13526000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q32198836'),
(27962, 'Oberammergau', 3009, 'BY', 82, 'DE', 47.59812000, 11.06692000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q32198935'),
(27963, 'Oberasbach', 3009, 'BY', 82, 'DE', 49.42275000, 10.95766000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q504751'),
(27964, 'Oberaudorf', 3009, 'BY', 82, 'DE', 47.64822000, 12.17242000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q524660'),
(27965, 'Oberaula', 3018, 'HE', 82, 'DE', 50.85000000, 9.46667000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q524660'),
(27966, 'Oberbergkirchen', 3009, 'BY', 82, 'DE', 48.30000000, 12.38333000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q514237'),
(27967, 'Oberbiel', 3018, 'HE', 82, 'DE', 50.55320000, 8.42797000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q2009209'),
(27968, 'Oberboihingen', 3006, 'BW', 82, 'DE', 48.65000000, 9.36667000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q80606'),
(27969, 'Obercunnersdorf', 3021, 'SN', 82, 'DE', 50.91711000, 13.56015000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q32199232'),
(27970, 'Oberdachstetten', 3009, 'BY', 82, 'DE', 49.41667000, 10.43333000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q506577'),
(27971, 'Oberderdingen', 3006, 'BW', 82, 'DE', 49.06556000, 8.80306000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q542545'),
(27972, 'Oberding', 3009, 'BY', 82, 'DE', 48.31667000, 11.85000000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q254933'),
(27973, 'Oberdischingen', 3006, 'BW', 82, 'DE', 48.30000000, 9.83333000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q515264'),
(27974, 'Oberdorla', 3015, 'TH', 82, 'DE', 51.16585000, 10.42163000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q665765'),
(27975, 'Oberelbert', 3019, 'RP', 82, 'DE', 50.39167000, 7.81722000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q674099'),
(27976, 'Oberelsbach', 3009, 'BY', 82, 'DE', 50.44118000, 10.11692000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q284696'),
(27977, 'Oberfell', 3019, 'RP', 82, 'DE', 50.26038000, 7.44461000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q647068'),
(27978, 'Obergriesbach', 3009, 'BY', 82, 'DE', 48.42305000, 11.06850000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q503555'),
(27979, 'Obergurig', 3021, 'SN', 82, 'DE', 51.13294000, 14.40488000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q93271'),
(27980, 'Obergünzburg', 3009, 'BY', 82, 'DE', 47.84545000, 10.41821000, '2019-10-05 22:41:24', '2020-05-01 17:22:47', 1, 'Q513923'),
(27981, 'Oberhaching', 3009, 'BY', 82, 'DE', 48.02455000, 11.59744000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q504891'),
(27982, 'Oberhaid', 3009, 'BY', 82, 'DE', 50.12614000, 11.80514000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q504891'),
(27983, 'Oberharmersbach', 3006, 'BW', 82, 'DE', 48.37358000, 8.12542000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q112559'),
(27984, 'Oberhausen', 3009, 'BY', 82, 'DE', 48.72201000, 11.11151000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q112559'),
(27985, 'Oberhausen', 3017, 'NW', 82, 'DE', 51.47805000, 6.86250000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q2838'),
(27986, 'Oberhausen-Rheinhausen', 3006, 'BW', 82, 'DE', 49.27389000, 8.47167000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q636197'),
(27987, 'Oberhof', 3015, 'TH', 82, 'DE', 50.70435000, 10.72716000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q636197'),
(27988, 'Oberjettingen', 3006, 'BW', 82, 'DE', 48.57468000, 8.77636000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q636197'),
(27989, 'Oberkirch', 3006, 'BW', 82, 'DE', 48.53241000, 8.07864000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q541381'),
(27990, 'Oberkochen', 3006, 'BW', 82, 'DE', 48.78379000, 10.10519000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q521397'),
(27991, 'Oberkotzau', 3009, 'BY', 82, 'DE', 50.26236000, 11.93484000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q503409'),
(27992, 'Oberleichtersbach', 3009, 'BY', 82, 'DE', 50.28333000, 9.80000000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q504282'),
(27993, 'Oberlichtenau', 3021, 'SN', 82, 'DE', 51.21875000, 13.98992000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q700434'),
(27994, 'Oberlungwitz', 3021, 'SN', 82, 'DE', 50.78230000, 12.70789000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q20084'),
(27995, 'Obermarchtal', 3006, 'BW', 82, 'DE', 48.23279000, 9.57235000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q540779'),
(27996, 'Obermaßfeld-Grimmenthal', 3015, 'TH', 82, 'DE', 50.52898000, 10.43963000, '2019-10-05 22:41:24', '2020-05-01 17:22:50', 1, 'Q540779'),
(27997, 'Obermehler', 3015, 'TH', 82, 'DE', 51.26996000, 10.59754000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q637507'),
(27998, 'Obermeitingen', 3009, 'BY', 82, 'DE', 48.14685000, 10.80626000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q637507'),
(27999, 'Obermichelbach', 3009, 'BY', 82, 'DE', 49.53117000, 10.90891000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q521007'),
(28000, 'Obermoschel', 3019, 'RP', 82, 'DE', 49.72797000, 7.77266000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q23034'),
(28001, 'Obernbeck', 3017, 'NW', 82, 'DE', 52.20178000, 8.70404000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q23034'),
(28002, 'Obernbreit', 3009, 'BY', 82, 'DE', 49.65863000, 10.16424000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q504679'),
(28003, 'Obernburg am Main', 3009, 'BY', 82, 'DE', 49.83577000, 9.13101000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q504679'),
(28004, 'Oberndorf', 3006, 'BW', 82, 'DE', 48.29050000, 8.57221000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q83206'),
(28005, 'Oberndorf', 3008, 'NI', 82, 'DE', 53.75000000, 9.15000000, '2019-10-05 22:41:24', '2019-10-05 22:41:24', 1, 'Q83206'),
(28006, 'Oberndorf', 3009, 'BY', 82, 'DE', 48.66918000, 10.86749000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32201675'),
(28007, 'Obernfeld', 3008, 'NI', 82, 'DE', 51.55000000, 10.23333000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q560177'),
(28008, 'Obernheim', 3006, 'BW', 82, 'DE', 48.16297000, 8.86113000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32201764'),
(28009, 'Obernheim-Kirchenarnbach', 3019, 'RP', 82, 'DE', 49.35000000, 7.58333000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q247000'),
(28010, 'Obernkirchen', 3008, 'NI', 82, 'DE', 52.27210000, 9.12912000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q570427'),
(28011, 'Obernzell', 3009, 'BY', 82, 'DE', 48.55485000, 13.63729000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q268431'),
(28012, 'Obernzenn', 3009, 'BY', 82, 'DE', 49.45193000, 10.46670000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q505306'),
(28013, 'Oberostendorf', 3009, 'BY', 82, 'DE', 47.94360000, 10.74270000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q514359'),
(28014, 'Oberotterbach', 3019, 'RP', 82, 'DE', 49.07044000, 7.96955000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q631019'),
(28015, 'Oberottmarshausen', 3009, 'BY', 82, 'DE', 48.23716000, 10.85754000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q631019'),
(28016, 'Oberpframmern', 3009, 'BY', 82, 'DE', 48.02202000, 11.81331000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q511697'),
(28017, 'Oberpleichfeld', 3009, 'BY', 82, 'DE', 49.87694000, 10.08682000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q511697'),
(28018, 'Oberpöring', 3009, 'BY', 82, 'DE', 48.70178000, 12.82482000, '2019-10-05 22:41:25', '2020-05-01 17:22:47', 1, 'Q504425'),
(28019, 'Oberreichenbach', 3009, 'BY', 82, 'DE', 49.58946000, 10.76892000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q504425'),
(28020, 'Oberreichenbach', 3006, 'BW', 82, 'DE', 48.73333000, 8.66667000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q504425'),
(28021, 'Oberreute', 3009, 'BY', 82, 'DE', 47.56303000, 9.94449000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q203830'),
(28022, 'Oberried', 3006, 'BW', 82, 'DE', 47.93333000, 7.95000000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q203830'),
(28023, 'Oberrieden', 3009, 'BY', 82, 'DE', 48.08886000, 10.42611000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q203830'),
(28024, 'Oberriexingen', 3006, 'BW', 82, 'DE', 48.92652000, 9.02701000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q61704'),
(28025, 'Oberrot', 3006, 'BW', 82, 'DE', 49.01444000, 9.66722000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q83215'),
(28026, 'Oberrotweil', 3006, 'BW', 82, 'DE', 48.08990000, 7.63601000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q83215'),
(28027, 'Oberröblingen', 3011, 'ST', 82, 'DE', 51.43705000, 11.30750000, '2019-10-05 22:41:25', '2020-05-01 17:22:49', 1, 'Q2010811'),
(28028, 'Oberscheinfeld', 3009, 'BY', 82, 'DE', 49.70829000, 10.43418000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q505388'),
(28029, 'Oberschleißheim', 3009, 'BY', 82, 'DE', 48.25000000, 11.56667000, '2019-10-05 22:41:25', '2020-05-01 17:22:47', 1, 'Q253881'),
(28030, 'Oberschneiding', 3009, 'BY', 82, 'DE', 48.79573000, 12.64200000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q547257'),
(28031, 'Oberschwarzach', 3009, 'BY', 82, 'DE', 49.86051000, 10.40999000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q558942'),
(28032, 'Oberschweinbach', 3009, 'BY', 82, 'DE', 48.23817000, 11.15571000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32202407'),
(28033, 'Oberschöna', 3021, 'SN', 82, 'DE', 50.89759000, 13.25379000, '2019-10-05 22:41:25', '2020-05-01 17:22:49', 1, 'Q71025'),
(28034, 'Oberschöneweide', 3010, 'BE', 82, 'DE', 52.46106000, 13.52108000, '2019-10-05 22:41:25', '2020-05-01 17:22:48', 1, 'Q644448'),
(28035, 'Obersinn', 3009, 'BY', 82, 'DE', 50.20875000, 9.61545000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q510952'),
(28036, 'Obersontheim', 3006, 'BW', 82, 'DE', 49.05667000, 9.89917000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q83053'),
(28037, 'Oberstadion', 3006, 'BW', 82, 'DE', 48.18596000, 9.69241000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q557622'),
(28038, 'Oberstaufen', 3009, 'BY', 82, 'DE', 47.55568000, 10.02245000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q127637'),
(28039, 'Oberstdorf', 3009, 'BY', 82, 'DE', 47.40724000, 10.27939000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q266230'),
(28040, 'Oberstenfeld', 3006, 'BW', 82, 'DE', 49.02611000, 9.32083000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q61739'),
(28041, 'Oberstreu', 3009, 'BY', 82, 'DE', 50.40338000, 10.28775000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q583385'),
(28042, 'Obersöchering', 3009, 'BY', 82, 'DE', 47.73333000, 11.21667000, '2019-10-05 22:41:25', '2020-05-01 17:22:47', 1, 'Q49287598'),
(28043, 'Obersüßbach', 3009, 'BY', 82, 'DE', 48.61667000, 11.95000000, '2019-10-05 22:41:25', '2020-05-01 17:22:47', 1, 'Q513261'),
(28044, 'Obertaufkirchen', 3009, 'BY', 82, 'DE', 48.26046000, 12.27904000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q514296'),
(28045, 'Oberteuringen', 3006, 'BW', 82, 'DE', 47.72409000, 9.46979000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q544761'),
(28046, 'Oberthal', 3020, 'SL', 82, 'DE', 49.51257000, 7.08382000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q544761'),
(28047, 'Oberthulba', 3009, 'BY', 82, 'DE', 50.19904000, 9.95882000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q505153'),
(28048, 'Obertraubling', 3009, 'BY', 82, 'DE', 48.96667000, 12.16667000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q491647'),
(28049, 'Obertrubach', 3009, 'BY', 82, 'DE', 49.70000000, 11.35000000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q502774'),
(28050, 'Obertshausen', 3018, 'HE', 82, 'DE', 50.07139000, 8.85123000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q569628'),
(28051, 'Oberursel', 3018, 'HE', 82, 'DE', 50.20731000, 8.57747000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q7044'),
(28052, 'Oberviechtach', 3009, 'BY', 82, 'DE', 49.45809000, 12.41669000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q257823'),
(28053, 'Oberweißbach', 3015, 'TH', 82, 'DE', 50.58231000, 11.14382000, '2019-10-05 22:41:25', '2020-05-01 17:22:50', 1, 'Q310955'),
(28054, 'Oberwesel', 3019, 'RP', 82, 'DE', 50.10777000, 7.72522000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q668327'),
(28055, 'Oberwiera', 3021, 'SN', 82, 'DE', 50.88913000, 12.54415000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q17126'),
(28056, 'Oberwolfach', 3006, 'BW', 82, 'DE', 48.31667000, 8.21667000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q557688'),
(28057, 'Oberzissen', 3019, 'RP', 82, 'DE', 50.45000000, 7.20000000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q658647'),
(28058, 'Obhausen', 3011, 'ST', 82, 'DE', 51.39172000, 11.65312000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q542937'),
(28059, 'Obing', 3009, 'BY', 82, 'DE', 48.00049000, 12.40528000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q254467'),
(28060, 'Obrigheim', 3006, 'BW', 82, 'DE', 49.35194000, 9.09083000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q254467'),
(28061, 'Obrigheim', 3019, 'RP', 82, 'DE', 49.59161000, 8.20483000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32203688'),
(28062, 'Ochsenfurt', 3009, 'BY', 82, 'DE', 49.66429000, 10.06227000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q504849'),
(28063, 'Ochsenhausen', 3006, 'BW', 82, 'DE', 48.07029000, 9.95030000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q522526'),
(28064, 'Ochtendung', 3019, 'RP', 82, 'DE', 50.35000000, 7.38333000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q646825'),
(28065, 'Ochtrup', 3017, 'NW', 82, 'DE', 52.20802000, 7.18988000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32204291'),
(28066, 'Ockenfels', 3019, 'RP', 82, 'DE', 50.57610000, 7.27535000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q651767'),
(28067, 'Ockenheim', 3019, 'RP', 82, 'DE', 49.94371000, 7.97127000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q389741'),
(28068, 'Odelzhausen', 3009, 'BY', 82, 'DE', 48.30889000, 11.19889000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q512193'),
(28069, 'Odenthal', 3017, 'NW', 82, 'DE', 51.03333000, 7.11667000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32204526'),
(28070, 'Oderberg', 3013, 'BB', 82, 'DE', 52.86571000, 14.04508000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q553202'),
(28071, 'Odernheim', 3019, 'RP', 82, 'DE', 49.76511000, 7.70427000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32204655'),
(28072, 'Oebisfelde', 3011, 'ST', 82, 'DE', 52.43421000, 10.98786000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q2014884'),
(28073, 'Oederan', 3021, 'SN', 82, 'DE', 50.86064000, 13.17164000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q71141'),
(28074, 'Oederquart', 3008, 'NI', 82, 'DE', 53.80267000, 9.23680000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q503942'),
(28075, 'Oedheim', 3006, 'BW', 82, 'DE', 49.24028000, 9.25333000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q518048'),
(28076, 'Oelde', 3017, 'NW', 82, 'DE', 51.82890000, 8.14724000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q2752'),
(28077, 'Oelixdorf', 3005, 'SH', 82, 'DE', 53.92738000, 9.56383000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q556261'),
(28078, 'Oelsnitz', 3021, 'SN', 82, 'DE', 50.41470000, 12.16950000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q46845'),
(28079, 'Oer-Erkenschwick', 3017, 'NW', 82, 'DE', 51.64198000, 7.26451000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q16138'),
(28080, 'Oerel', 3008, 'NI', 82, 'DE', 53.48333000, 9.05000000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q504713'),
(28081, 'Oerlenbach', 3009, 'BY', 82, 'DE', 50.15000000, 10.13333000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q339825'),
(28082, 'Oerlinghausen', 3017, 'NW', 82, 'DE', 51.95453000, 8.66220000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q242757'),
(28083, 'Oettingen in Bayern', 3009, 'BY', 82, 'DE', 48.95274000, 10.60465000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q556312'),
(28084, 'Oeversee', 3005, 'SH', 82, 'DE', 54.70000000, 9.43333000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q538052'),
(28085, 'Offenau', 3006, 'BW', 82, 'DE', 49.24583000, 9.16056000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q503878'),
(28086, 'Offenbach', 3018, 'HE', 82, 'DE', 50.10061000, 8.76647000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q503878'),
(28087, 'Offenbach an der Queich', 3019, 'RP', 82, 'DE', 49.19546000, 8.19779000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q631832'),
(28088, 'Offenbach-Hundheim', 3019, 'RP', 82, 'DE', 49.61729000, 7.55117000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q644168'),
(28089, 'Offenberg', 3009, 'BY', 82, 'DE', 48.86195000, 12.86293000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q32205472'),
(28090, 'Offenburg', 3006, 'BW', 82, 'DE', 48.47377000, 7.94495000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q4044'),
(28091, 'Offenhausen', 3009, 'BY', 82, 'DE', 49.44971000, 11.41316000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q4044'),
(28092, 'Offingen', 3009, 'BY', 82, 'DE', 48.48258000, 10.36249000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q507245'),
(28093, 'Offstein', 3019, 'RP', 82, 'DE', 49.60611000, 8.23806000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q641840'),
(28094, 'Ofterdingen', 3006, 'BW', 82, 'DE', 48.41667000, 9.03333000, '2019-10-05 22:41:25', '2019-10-05 22:41:25', 1, 'Q521449'),
(28095, 'Ofterschwang', 3009, 'BY', 82, 'DE', 47.50000000, 10.23333000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q834920'),
(28096, 'Oftersheim', 3006, 'BW', 82, 'DE', 49.36528000, 8.58306000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q81862'),
(28097, 'Ohlsbach', 3006, 'BW', 82, 'DE', 48.43222000, 7.99384000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q533966'),
(28098, 'Ohlsdorf', 3016, 'HH', 82, 'DE', 53.62594000, 10.03145000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q533966'),
(28099, 'Ohlstadt', 3009, 'BY', 82, 'DE', 47.63333000, 11.23333000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32205935'),
(28100, 'Ohmden', 3006, 'BW', 82, 'DE', 48.64599000, 9.52698000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q80704'),
(28101, 'Ohorn', 3021, 'SN', 82, 'DE', 51.17199000, 14.04669000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q93272'),
(28102, 'Olbernhau', 3021, 'SN', 82, 'DE', 50.65870000, 13.34250000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q57933'),
(28103, 'Olbersdorf', 3021, 'SN', 82, 'DE', 50.87304000, 14.77035000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q502559'),
(28104, 'Olching', 3009, 'BY', 82, 'DE', 48.20000000, 11.33333000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32206345'),
(28105, 'Oldenburg', 3008, 'NI', 82, 'DE', 53.14118000, 8.21467000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q2936'),
(28106, 'Oldenburg in Holstein', 3005, 'SH', 82, 'DE', 54.29576000, 10.90156000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q496435'),
(28107, 'Oldendorf', 3005, 'SH', 82, 'DE', 53.95341000, 9.45858000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q496435'),
(28108, 'Oldendorf', 3008, 'NI', 82, 'DE', 53.15580000, 10.21431000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32206933'),
(28109, 'Oldenswort', 3005, 'SH', 82, 'DE', 54.37243000, 8.93996000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q20784'),
(28110, 'Oldisleben', 3015, 'TH', 82, 'DE', 51.30975000, 11.17112000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q628741'),
(28111, 'Olfen', 3017, 'NW', 82, 'DE', 51.70787000, 7.37893000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q14937'),
(28112, 'Olpe', 3017, 'NW', 82, 'DE', 51.02899000, 7.85139000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q14937'),
(28113, 'Olsberg', 3017, 'NW', 82, 'DE', 51.35613000, 8.48899000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32207430'),
(28114, 'Olsbrücken', 3019, 'RP', 82, 'DE', 49.53884000, 7.65857000, '2019-10-05 22:41:26', '2020-05-01 17:22:49', 1, 'Q818519'),
(28115, 'Opfenbach', 3009, 'BY', 82, 'DE', 47.63333000, 9.83333000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q259366'),
(28116, 'Opladen', 3017, 'NW', 82, 'DE', 51.06863000, 7.00387000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q315138'),
(28117, 'Oppach', 3021, 'SN', 82, 'DE', 51.06667000, 14.50000000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q502562'),
(28118, 'Oppenau', 3006, 'BW', 82, 'DE', 48.47332000, 8.15970000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q515470'),
(28119, 'Oppenheim', 3019, 'RP', 82, 'DE', 49.85470000, 8.35974000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q518690'),
(28120, 'Oppenweiler', 3006, 'BW', 82, 'DE', 48.98270000, 9.45850000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q83221'),
(28121, 'Oppin', 3011, 'ST', 82, 'DE', 51.55136000, 12.03274000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q689946'),
(28122, 'Oppurg', 3015, 'TH', 82, 'DE', 50.71065000, 11.65289000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q660085'),
(28123, 'Oranienbaum', 3011, 'ST', 82, 'DE', 51.79860000, 12.40583000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32208345'),
(28124, 'Oranienburg', 3013, 'BB', 82, 'DE', 52.75577000, 13.24197000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q14808'),
(28125, 'Orenhofen', 3019, 'RP', 82, 'DE', 49.90000000, 6.65000000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q567498'),
(28126, 'Orlamünde', 3015, 'TH', 82, 'DE', 50.77486000, 11.51929000, '2019-10-05 22:41:26', '2020-05-01 17:22:50', 1, 'Q33469783'),
(28127, 'Ornbau', 3009, 'BY', 82, 'DE', 49.17623000, 10.65797000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q572242'),
(28128, 'Orscholz', 3020, 'SL', 82, 'DE', 49.50593000, 6.52502000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q1656236'),
(28129, 'Orsingen-Nenzingen', 3006, 'BW', 82, 'DE', 47.84272000, 8.95909000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q62019'),
(28130, 'Ortenberg', 3018, 'HE', 82, 'DE', 50.35584000, 9.05602000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32209201'),
(28131, 'Ortenberg', 3006, 'BW', 82, 'DE', 48.45158000, 7.97178000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32209201'),
(28132, 'Ortenburg', 3009, 'BY', 82, 'DE', 48.54597000, 13.22250000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q264999'),
(28133, 'Ortrand', 3013, 'BB', 82, 'DE', 51.37505000, 13.75982000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q572503'),
(28134, 'Osann-Monzel', 3019, 'RP', 82, 'DE', 49.91667000, 6.95000000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q648793'),
(28135, 'Osburg', 3019, 'RP', 82, 'DE', 49.71667000, 6.78333000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q659819'),
(28136, 'Oschatz', 3021, 'SN', 82, 'DE', 51.30001000, 13.10984000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q12056'),
(28137, 'Oschersleben', 3011, 'ST', 82, 'DE', 52.03039000, 11.22898000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q525392'),
(28138, 'Osdorf', 3005, 'SH', 82, 'DE', 54.43333000, 10.01667000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q525392'),
(28139, 'Osloß', 3008, 'NI', 82, 'DE', 52.46925000, 10.68011000, '2019-10-05 22:41:26', '2020-05-01 17:22:48', 1, 'Q633134'),
(28140, 'Osnabrück', 3008, 'NI', 82, 'DE', 52.27264000, 8.04980000, '2019-10-05 22:41:26', '2020-05-01 17:22:48', 1, 'Q2916'),
(28141, 'Ossendorf', 3017, 'NW', 82, 'DE', 50.97059000, 6.90628000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q2916'),
(28142, 'Ostbevern', 3017, 'NW', 82, 'DE', 52.04018000, 7.84229000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q2804'),
(28143, 'Osteel', 3008, 'NI', 82, 'DE', 53.54417000, 7.25400000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q561115'),
(28144, 'Ostelsheim', 3006, 'BW', 82, 'DE', 48.72633000, 8.84816000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q545124'),
(28145, 'Osten', 3008, 'NI', 82, 'DE', 53.70000000, 9.20000000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q545124'),
(28146, 'Ostenfeld', 3005, 'SH', 82, 'DE', 54.46400000, 9.23430000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q32209784'),
(28147, 'Osterburg', 3011, 'ST', 82, 'DE', 52.78721000, 11.75297000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q504010'),
(28148, 'Osterburken', 3006, 'BW', 82, 'DE', 49.42997000, 9.42252000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q529154'),
(28149, 'Ostercappeln', 3008, 'NI', 82, 'DE', 52.35000000, 8.23333000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q529154'),
(28150, 'Osterfeld', 3011, 'ST', 82, 'DE', 51.08014000, 11.93047000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q529154'),
(28151, 'Osterhausen', 3011, 'ST', 82, 'DE', 51.45417000, 11.50456000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q696085'),
(28152, 'Osterhofen', 3009, 'BY', 82, 'DE', 48.70000000, 13.02221000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q503156'),
(28153, 'Osterholz-Scharmbeck', 3008, 'NI', 82, 'DE', 53.22698000, 8.79528000, '2019-10-05 22:41:26', '2019-10-05 22:41:26', 1, 'Q16141'),
(28154, 'Osternienburg', 3011, 'ST', 82, 'DE', 51.79384000, 12.02616000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q688246'),
(28155, 'Osternienburger Land', 3011, 'ST', 82, 'DE', 51.80000000, 12.01667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q688246'),
(28156, 'Osterode am Harz', 3008, 'NI', 82, 'DE', 51.72686000, 10.25089000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q327727'),
(28157, 'Osterrönfeld', 3005, 'SH', 82, 'DE', 54.29013000, 9.69900000, '2019-10-05 22:41:27', '2020-05-01 17:22:49', 1, 'Q565888'),
(28158, 'Osterspai', 3019, 'RP', 82, 'DE', 50.24453000, 7.61227000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q636235'),
(28159, 'Osterwieck', 3011, 'ST', 82, 'DE', 51.96990000, 10.71042000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q547619'),
(28160, 'Ostfildern', 3006, 'BW', 82, 'DE', 48.72704000, 9.24954000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q547619'),
(28161, 'Ostheim', 3017, 'NW', 82, 'DE', 50.93117000, 7.04412000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q547619'),
(28162, 'Ostheim vor der Rhön', 3009, 'BY', 82, 'DE', 50.45996000, 10.23057000, '2019-10-05 22:41:27', '2020-05-01 17:22:48', 1, 'Q547619'),
(28163, 'Osthofen', 3019, 'RP', 82, 'DE', 49.70382000, 8.32419000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q655880'),
(28164, 'Ostrach', 3006, 'BW', 82, 'DE', 47.95000000, 9.38333000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q632770'),
(28165, 'Ostrau', 3021, 'SN', 82, 'DE', 51.20639000, 12.88421000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32210952'),
(28166, 'Ostrhauderfehn', 3008, 'NI', 82, 'DE', 53.13333000, 7.61667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q559568'),
(28167, 'Ostritz', 3021, 'SN', 82, 'DE', 51.01451000, 14.93059000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q165167'),
(28168, 'Ostseebad Binz', 3007, 'MV', 82, 'DE', 54.39995000, 13.61052000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q523490'),
(28169, 'Ostseebad Boltenhagen', 3007, 'MV', 82, 'DE', 53.98779000, 11.20193000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q631074'),
(28170, 'Ostseebad Dierhagen', 3007, 'MV', 82, 'DE', 54.29243000, 12.35799000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q631074'),
(28171, 'Ostseebad Göhren', 3007, 'MV', 82, 'DE', 54.34140000, 13.73823000, '2019-10-05 22:41:27', '2020-05-01 17:22:49', 1, 'Q631074'),
(28172, 'Ostseebad Kühlungsborn', 3007, 'MV', 82, 'DE', 54.15035000, 11.75994000, '2019-10-05 22:41:27', '2020-05-01 17:22:49', 1, 'Q565620'),
(28173, 'Ostseebad Prerow', 3007, 'MV', 82, 'DE', 54.44469000, 12.57677000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q565620'),
(28174, 'Ostseebad Sellin', 3007, 'MV', 82, 'DE', 54.37846000, 13.69394000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q541654'),
(28175, 'Ostseebad Zinnowitz', 3007, 'MV', 82, 'DE', 54.07668000, 13.91127000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32211150'),
(28176, 'Oststeinbek', 3005, 'SH', 82, 'DE', 53.54321000, 10.16939000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q560887'),
(28177, 'Ottenbach', 3006, 'BW', 82, 'DE', 48.73649000, 9.74844000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q560887'),
(28178, 'Ottendorf-Okrilla', 3021, 'SN', 82, 'DE', 51.18333000, 13.83333000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q93276'),
(28179, 'Ottenhofen', 3009, 'BY', 82, 'DE', 48.21667000, 11.88333000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q511825'),
(28180, 'Ottenhofen', 3006, 'BW', 82, 'DE', 48.73560000, 8.12981000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32211352'),
(28181, 'Ottensen', 3016, 'HH', 82, 'DE', 53.55000000, 9.91667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q1597'),
(28182, 'Ottensoos', 3009, 'BY', 82, 'DE', 49.50949000, 11.34158000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q524623'),
(28183, 'Ottenstein', 3008, 'NI', 82, 'DE', 51.94707000, 9.40700000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q549898'),
(28184, 'Otter', 3008, 'NI', 82, 'DE', 53.24015000, 9.74247000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q549898'),
(28185, 'Otterbach', 3019, 'RP', 82, 'DE', 49.48554000, 7.73450000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q549898'),
(28186, 'Otterberg', 3019, 'RP', 82, 'DE', 49.50301000, 7.76995000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q632266'),
(28187, 'Otterfing', 3009, 'BY', 82, 'DE', 47.90898000, 11.67546000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32211461'),
(28188, 'Otterndorf', 3008, 'NI', 82, 'DE', 53.80908000, 8.90068000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q514493'),
(28189, 'Ottersberg', 3008, 'NI', 82, 'DE', 53.10990000, 9.14408000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q515036'),
(28190, 'Ottersheim', 3019, 'RP', 82, 'DE', 49.19129000, 8.23176000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32211566'),
(28191, 'Otterstadt', 3019, 'RP', 82, 'DE', 49.37222000, 8.44778000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q23035'),
(28192, 'Ottersweier', 3006, 'BW', 82, 'DE', 48.67022000, 8.11323000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q80571'),
(28193, 'Otterwisch', 3021, 'SN', 82, 'DE', 51.20000000, 12.61667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q10756'),
(28194, 'Ottobeuren', 3009, 'BY', 82, 'DE', 47.94130000, 10.29971000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q532455'),
(28195, 'Ottobrunn', 3009, 'BY', 82, 'DE', 48.06489000, 11.66327000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q506388'),
(28196, 'Ottrau', 3018, 'HE', 82, 'DE', 50.80599000, 9.38575000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q621450'),
(28197, 'Ottweiler', 3020, 'SL', 82, 'DE', 49.40133000, 7.16424000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q562147'),
(28198, 'Otzing', 3009, 'BY', 82, 'DE', 48.76134000, 12.80877000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q119474'),
(28199, 'Ovelgönne', 3008, 'NI', 82, 'DE', 53.34189000, 8.42179000, '2019-10-05 22:41:27', '2020-05-01 17:22:48', 1, 'Q652356'),
(28200, 'Overath', 3017, 'NW', 82, 'DE', 50.93275000, 7.28389000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32213241'),
(28201, 'Owen', 3006, 'BW', 82, 'DE', 48.58742000, 9.44978000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32213241'),
(28202, 'Owingen', 3006, 'BW', 82, 'DE', 47.80967000, 9.17173000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q557773'),
(28203, 'Owschlag', 3005, 'SH', 82, 'DE', 54.39336000, 9.59243000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q557773'),
(28204, 'Oy-Mittelberg', 3009, 'BY', 82, 'DE', 47.63333000, 10.43333000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q526444'),
(28205, 'Oyten', 3008, 'NI', 82, 'DE', 53.05000000, 9.01667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q501642'),
(28206, 'Oßling', 3021, 'SN', 82, 'DE', 51.35999000, 14.16567000, '2019-10-05 22:41:27', '2020-05-01 17:22:49', 1, 'Q93273'),
(28207, 'Oßmanstedt', 3015, 'TH', 82, 'DE', 51.01815000, 11.42746000, '2019-10-05 22:41:27', '2020-05-01 17:22:50', 1, 'Q93273'),
(28208, 'Padenstedt', 3005, 'SH', 82, 'DE', 54.05000000, 9.91667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q93273'),
(28209, 'Paderborn', 3017, 'NW', 82, 'DE', 51.71905000, 8.75439000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q2971'),
(28210, 'Pahlen', 3005, 'SH', 82, 'DE', 54.26667000, 9.30000000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q2971'),
(28211, 'Painten', 3009, 'BY', 82, 'DE', 48.99731000, 11.81947000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q503579'),
(28212, 'Palling', 3009, 'BY', 82, 'DE', 48.00204000, 12.63702000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q262325'),
(28213, 'Palzem', 3019, 'RP', 82, 'DE', 49.56667000, 6.36667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q636128'),
(28214, 'Pampow', 3007, 'MV', 82, 'DE', 53.75513000, 12.60815000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q32215042'),
(28215, 'Panker', 3005, 'SH', 82, 'DE', 54.33333000, 10.56667000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q526753'),
(28216, 'Pankow', 3010, 'BE', 82, 'DE', 52.56926000, 13.40186000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q4707648'),
(28217, 'Panschwitz-Kuckau', 3021, 'SN', 82, 'DE', 51.23395000, 14.19791000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q93279'),
(28218, 'Papenburg', 3008, 'NI', 82, 'DE', 53.07738000, 7.40444000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q14957'),
(28219, 'Papendorf', 3007, 'MV', 82, 'DE', 54.03523000, 12.13263000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q659551'),
(28220, 'Pappenheim', 3009, 'BY', 82, 'DE', 48.93383000, 10.97431000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q503763'),
(28221, 'Pappenheim', 3015, 'TH', 82, 'DE', 50.79631000, 10.47489000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q1747024'),
(28222, 'Parchim', 3007, 'MV', 82, 'DE', 53.42631000, 11.84875000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q559010'),
(28223, 'Parkstein', 3009, 'BY', 82, 'DE', 49.72921000, 12.06755000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q374423'),
(28224, 'Parkstetten', 3009, 'BY', 82, 'DE', 48.91667000, 12.60000000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q205594'),
(28225, 'Parsau', 3008, 'NI', 82, 'DE', 52.53471000, 10.89020000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q526974'),
(28226, 'Parsberg', 3009, 'BY', 82, 'DE', 49.16074000, 11.71834000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q509925'),
(28227, 'Partenheim', 3019, 'RP', 82, 'DE', 49.88389000, 8.08222000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q647129'),
(28228, 'Partenstein', 3009, 'BY', 82, 'DE', 50.04176000, 9.51991000, '2019-10-05 22:41:27', '2019-10-05 22:41:27', 1, 'Q314715'),
(28229, 'Pasewalk', 3007, 'MV', 82, 'DE', 53.50627000, 13.98997000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q493924'),
(28230, 'Pasing', 3009, 'BY', 82, 'DE', 48.14146000, 11.45599000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q256731'),
(28231, 'Passau', 3009, 'BY', 82, 'DE', 48.56650000, 13.43122000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q4190'),
(28232, 'Pastetten', 3009, 'BY', 82, 'DE', 48.19911000, 11.94368000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q511486'),
(28233, 'Patersdorf', 3009, 'BY', 82, 'DE', 49.01667000, 12.98333000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q489054'),
(28234, 'Pattensen', 3008, 'NI', 82, 'DE', 52.26448000, 9.76436000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q501636'),
(28235, 'Paulinenaue', 3013, 'BB', 82, 'DE', 52.67701000, 12.71067000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q585355'),
(28236, 'Paulsstadt', 3007, 'MV', 82, 'DE', 53.63283000, 11.40372000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q14912778'),
(28237, 'Paunzhausen', 3009, 'BY', 82, 'DE', 48.47486000, 11.56463000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q14912778'),
(28238, 'Pausa', 3021, 'SN', 82, 'DE', 50.58172000, 11.99732000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q46895'),
(28239, 'Pechbrunn', 3009, 'BY', 82, 'DE', 49.96667000, 12.16667000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q553803'),
(28240, 'Pegau', 3021, 'SN', 82, 'DE', 51.16715000, 12.25144000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q10773'),
(28241, 'Pegnitz', 3009, 'BY', 82, 'DE', 49.75216000, 11.54187000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32219848'),
(28242, 'Peine', 3008, 'NI', 82, 'DE', 52.31928000, 10.23520000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q6850'),
(28243, 'Peiting', 3009, 'BY', 82, 'DE', 47.79549000, 10.92951000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32219892'),
(28244, 'Peitz', 3013, 'BB', 82, 'DE', 51.85841000, 14.41138000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q574791'),
(28245, 'Peißen', 3011, 'ST', 82, 'DE', 51.74347000, 11.75756000, '2019-10-05 22:41:28', '2020-05-01 17:22:49', 1, 'Q701826'),
(28246, 'Peißenberg', 3009, 'BY', 82, 'DE', 47.80466000, 11.06990000, '2019-10-05 22:41:28', '2020-05-01 17:22:48', 1, 'Q33471776'),
(28247, 'Pellingen', 3019, 'RP', 82, 'DE', 49.67552000, 6.67085000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q655962'),
(28248, 'Pellworm', 3005, 'SH', 82, 'DE', 54.51610000, 8.64512000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q3146'),
(28249, 'Pelm', 3019, 'RP', 82, 'DE', 50.23188000, 6.69076000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q653374'),
(28250, 'Pemfling', 3009, 'BY', 82, 'DE', 49.26667000, 12.61667000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q503980'),
(28251, 'Penig', 3021, 'SN', 82, 'DE', 50.93336000, 12.70422000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q71145'),
(28252, 'Penkun', 3007, 'MV', 82, 'DE', 53.29695000, 14.23616000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q572013'),
(28253, 'Pennigsehl', 3008, 'NI', 82, 'DE', 52.64181000, 9.02630000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q572013'),
(28254, 'Pentling', 3009, 'BY', 82, 'DE', 48.98343000, 12.05870000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q491788'),
(28255, 'Penzberg', 3009, 'BY', 82, 'DE', 47.75293000, 11.37700000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32220678'),
(28256, 'Penzing', 3009, 'BY', 82, 'DE', 48.07481000, 10.92745000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32220678'),
(28257, 'Penzlin', 3007, 'MV', 82, 'DE', 53.50400000, 13.08407000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q20314'),
(28258, 'Perach', 3009, 'BY', 82, 'DE', 48.26667000, 12.76667000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q258993'),
(28259, 'Perkam', 3009, 'BY', 82, 'DE', 48.85528000, 12.43979000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q559141'),
(28260, 'Perleberg', 3013, 'BB', 82, 'DE', 53.07583000, 11.85739000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q498540'),
(28261, 'Perlesreut', 3009, 'BY', 82, 'DE', 48.78181000, 13.43840000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32221243'),
(28262, 'Petersaurach', 3009, 'BY', 82, 'DE', 49.30000000, 10.75000000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q507397'),
(28263, 'Petersdorf', 3009, 'BY', 82, 'DE', 48.51667000, 11.03333000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q507397'),
(28264, 'Petershagen', 3017, 'NW', 82, 'DE', 52.37513000, 8.96538000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q182857'),
(28265, 'Petershagen', 3013, 'BB', 82, 'DE', 52.52078000, 13.78748000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32221799'),
(28266, 'Petershausen', 3009, 'BY', 82, 'DE', 48.40967000, 11.47056000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q533046'),
(28267, 'Pettendorf', 3009, 'BY', 82, 'DE', 49.35475000, 12.35926000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32222134'),
(28268, 'Petting', 3009, 'BY', 82, 'DE', 47.91232000, 12.81512000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32222134'),
(28269, 'Pettstadt', 3009, 'BY', 82, 'DE', 49.82996000, 10.92839000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q427456'),
(28270, 'Pfaffen-Schwabenheim', 3019, 'RP', 82, 'DE', 49.85000000, 7.95000000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q631811'),
(28271, 'Pfaffenhausen', 3009, 'BY', 82, 'DE', 48.11878000, 10.45504000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q547219'),
(28272, 'Pfaffenhofen', 3006, 'BW', 82, 'DE', 49.06444000, 8.97639000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q537465'),
(28273, 'Pfaffenhofen', 3009, 'BY', 82, 'DE', 48.39334000, 11.20563000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q537465'),
(28274, 'Pfaffenhofen an der Ilm', 3009, 'BY', 82, 'DE', 48.53053000, 11.50500000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q537465'),
(28275, 'Pfaffenhofen an der Roth', 3009, 'BY', 82, 'DE', 48.35454000, 10.16184000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q628969'),
(28276, 'Pfaffenweiler', 3006, 'BW', 82, 'DE', 47.93333000, 7.75000000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q632889'),
(28277, 'Pfaffing', 3009, 'BY', 82, 'DE', 48.05456000, 12.10917000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q632889'),
(28278, 'Pfaffroda', 3021, 'SN', 82, 'DE', 50.86134000, 12.51497000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32222605'),
(28279, 'Pfakofen', 3009, 'BY', 82, 'DE', 48.85822000, 12.22744000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q490162'),
(28280, 'Pfalzgrafenweiler', 3006, 'BW', 82, 'DE', 48.52650000, 8.56582000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q83091'),
(28281, 'Pfarrkirchen', 3009, 'BY', 82, 'DE', 48.43205000, 12.93812000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q545359'),
(28282, 'Pfarrweisach', 3009, 'BY', 82, 'DE', 50.15000000, 10.73333000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q504412'),
(28283, 'Pfatter', 3009, 'BY', 82, 'DE', 48.96309000, 12.38254000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q491751'),
(28284, 'Pfedelbach', 3006, 'BW', 82, 'DE', 49.17806000, 9.50500000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q552218'),
(28285, 'Pfeffelbach', 3019, 'RP', 82, 'DE', 49.53948000, 7.32768000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q648665'),
(28286, 'Pfeffenhausen', 3009, 'BY', 82, 'DE', 48.66466000, 11.96594000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q513303'),
(28287, 'Pfofeld', 3009, 'BY', 82, 'DE', 49.10590000, 10.83664000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q525945'),
(28288, 'Pforzen', 3009, 'BY', 82, 'DE', 47.92997000, 10.61357000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q516343'),
(28289, 'Pforzheim', 3006, 'BW', 82, 'DE', 48.88436000, 8.69892000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q3046'),
(28290, 'Pfreimd', 3009, 'BY', 82, 'DE', 49.49114000, 12.18069000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q522639'),
(28291, 'Pfronstetten', 3006, 'BW', 82, 'DE', 48.27641000, 9.35995000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q83227'),
(28292, 'Pfronten', 3009, 'BY', 82, 'DE', 47.58220000, 10.54962000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q505488'),
(28293, 'Pfullendorf', 3006, 'BW', 82, 'DE', 47.92610000, 9.25780000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q82917'),
(28294, 'Pfullingen', 3006, 'BW', 82, 'DE', 48.46458000, 9.22796000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q82053'),
(28295, 'Pfungstadt', 3018, 'HE', 82, 'DE', 49.80557000, 8.60307000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q516500'),
(28296, 'Philippsburg', 3006, 'BW', 82, 'DE', 49.23170000, 8.46074000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q541161'),
(28297, 'Philippsthal', 3018, 'HE', 82, 'DE', 50.83948000, 10.00906000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32223549'),
(28298, 'Piding', 3009, 'BY', 82, 'DE', 47.76667000, 12.91667000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q267865'),
(28299, 'Pielenhofen', 3009, 'BY', 82, 'DE', 49.07235000, 11.95699000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q490167'),
(28300, 'Piesport', 3019, 'RP', 82, 'DE', 49.88640000, 6.91649000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q375762'),
(28301, 'Piesteritz', 3011, 'ST', 82, 'DE', 51.86956000, 12.59835000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q375762'),
(28302, 'Pilsach', 3009, 'BY', 82, 'DE', 49.32137000, 11.50311000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q505419'),
(28303, 'Pilsting', 3009, 'BY', 82, 'DE', 48.70116000, 12.65105000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32225061'),
(28304, 'Pinneberg', 3005, 'SH', 82, 'DE', 53.66732000, 9.78936000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q7074'),
(28305, 'Pinnow', 3007, 'MV', 82, 'DE', 53.60164000, 11.54577000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32225859'),
(28306, 'Pinzberg', 3009, 'BY', 82, 'DE', 49.68971000, 11.10207000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q503085'),
(28307, 'Pirk', 3009, 'BY', 82, 'DE', 49.63333000, 12.16667000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q503460'),
(28308, 'Pirmasens', 3019, 'RP', 82, 'DE', 49.20145000, 7.60529000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q14849'),
(28309, 'Pirna', 3021, 'SN', 82, 'DE', 50.95843000, 13.93702000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q6477'),
(28310, 'Pittenhart', 3009, 'BY', 82, 'DE', 47.97724000, 12.38997000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q253981'),
(28311, 'Plaidt', 3019, 'RP', 82, 'DE', 50.39260000, 7.39251000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q657704'),
(28312, 'Planegg', 3009, 'BY', 82, 'DE', 48.10672000, 11.42483000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q255796'),
(28313, 'Plankstadt', 3006, 'BW', 82, 'DE', 49.39444000, 8.59611000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q81959'),
(28314, 'Plate', 3007, 'MV', 82, 'DE', 53.55145000, 11.50927000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q32227247'),
(28315, 'Plattenburg', 3013, 'BB', 82, 'DE', 52.95919000, 12.02951000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q637036'),
(28316, 'Plattling', 3009, 'BY', 82, 'DE', 48.77866000, 12.87509000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q509566'),
(28317, 'Plau am See', 3007, 'MV', 82, 'DE', 53.45821000, 12.26246000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q53887'),
(28318, 'Plaue', 3015, 'TH', 82, 'DE', 50.77835000, 10.89969000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q552628'),
(28319, 'Plauen', 3021, 'SN', 82, 'DE', 50.49730000, 12.13782000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q3952'),
(28320, 'Pleidelsheim', 3006, 'BW', 82, 'DE', 48.95920000, 9.20311000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q62121'),
(28321, 'Pleinfeld', 3009, 'BY', 82, 'DE', 49.10473000, 10.98194000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q252320'),
(28322, 'Pleiskirchen', 3009, 'BY', 82, 'DE', 48.30713000, 12.59832000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q1620884'),
(28323, 'Plessa', 3013, 'BB', 82, 'DE', 51.46667000, 13.61667000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q551522'),
(28324, 'Plettenberg', 3017, 'NW', 82, 'DE', 51.20949000, 7.87261000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q5542'),
(28325, 'Pleystein', 3009, 'BY', 82, 'DE', 49.64907000, 12.40631000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q503149'),
(28326, 'Pliening', 3009, 'BY', 82, 'DE', 48.19556000, 11.80069000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q521701'),
(28327, 'Pliezhausen', 3006, 'BW', 82, 'DE', 48.55934000, 9.20749000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q80980'),
(28328, 'Plochingen', 3006, 'BW', 82, 'DE', 48.71067000, 9.41949000, '2019-10-05 22:41:28', '2019-10-05 22:41:28', 1, 'Q82274'),
(28329, 'Pluwig', 3019, 'RP', 82, 'DE', 49.68976000, 6.71239000, '2019-10-05 22:41:29', '2019-10-05 22:41:29', 1, 'Q630553'),
(28330, 'Plänterwald', 3010, 'BE', 82, 'DE', 52.48106000, 13.47276000, '2019-10-05 22:41:29', '2020-05-01 17:22:48', 1, 'Q560550'),
(28331, 'Plön', 3005, 'SH', 82, 'DE', 54.16241000, 10.42333000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q497060'),
(28332, 'Plötzkau', 3011, 'ST', 82, 'DE', 51.75000000, 11.68333000, '2019-10-05 22:41:29', '2020-05-01 17:22:49', 1, 'Q497060');

