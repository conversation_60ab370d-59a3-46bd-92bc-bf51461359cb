INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(18680, 'Villars-sur-Ollon', 1651, 'VD', 214, 'CH', 46.29832000, 7.05631000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q674237'),
(18681, 'Villaz-Saint-Pierre', 1640, 'FR', 214, 'CH', 46.72074000, 6.95638000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22423581'),
(18682, '<PERSON><PERSON><PERSON>', 1651, 'VD', 214, 'CH', 46.39869000, 6.92654000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22423755'),
(18683, 'V<PERSON>igen', 1639, 'AG', 214, 'CH', 47.52682000, 8.21486000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22423944'),
(18684, '<PERSON>ill<PERSON>gen', 1639, 'AG', 214, 'CH', 47.35009000, 8.24762000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22423982'),
(18685, 'Villnachern', 1639, 'AG', 214, 'CH', 47.47098000, 8.15975000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22424021'),
(18686, 'Vionnaz', 1648, 'VS', 214, 'CH', 46.31101000, 6.90062000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q69549'),
(18687, 'Visp', 1648, 'VS', 214, 'CH', 46.29370000, 7.88149000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q69549'),
(18688, 'Visp District', 1648, 'VS', 214, 'CH', 46.17612000, 7.85609000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q660348'),
(18689, 'Visperterminen', 1648, 'VS', 214, 'CH', 46.25899000, 7.90192000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q70387'),
(18690, 'Vitznau', 1663, 'LU', 214, 'CH', 47.01014000, 8.48420000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22427341'),
(18691, 'Volketswil / Volketswil (Dorf)', 1656, 'ZH', 214, 'CH', 47.39016000, 8.69085000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22427341'),
(18692, 'Vordemwald', 1639, 'AG', 214, 'CH', 47.27585000, 7.90114000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22429969'),
(18693, 'Vorderthal', 1653, 'SZ', 214, 'CH', 47.12172000, 8.90225000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22430856'),
(18694, 'Vouvry', 1648, 'VS', 214, 'CH', 46.33746000, 6.88950000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q68889'),
(18695, 'Vuadens', 1640, 'FR', 214, 'CH', 46.61545000, 7.01732000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22432146'),
(18696, 'Vuarrens', 1651, 'VD', 214, 'CH', 46.68578000, 6.64793000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q68910'),
(18697, 'Vétroz', 1648, 'VS', 214, 'CH', 46.22171000, 7.27858000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22418739'),
(18698, 'Wagenhausen', 1657, 'TG', 214, 'CH', 47.66003000, 8.84782000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22437603'),
(18699, 'Wahlen', 1641, 'BL', 214, 'CH', 47.40226000, 7.51511000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22438542'),
(18700, 'Wahlkreis Rheintal', 1644, 'SG', 214, 'CH', 47.37769000, 9.57903000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q188220'),
(18701, 'Wahlkreis Rorschach', 1644, 'SG', 214, 'CH', 47.46647000, 9.44246000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q661312'),
(18702, 'Wahlkreis Sarganserland', 1644, 'SG', 214, 'CH', 47.05000000, 9.********, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q661066'),
(18703, 'Wahlkreis See-Gaster', 1644, 'SG', 214, 'CH', 47.22459000, 9.01680000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q661193'),
(18704, 'Wahlkreis St. Gallen', 1644, 'SG', 214, 'CH', 47.46342000, 9.39052000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q661143'),
(18705, 'Wahlkreis Toggenburg', 1644, 'SG', 214, 'CH', 47.29453000, 9.17283000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q693185'),
(18706, 'Wahlkreis Werdenberg', 1644, 'SG', 214, 'CH', 47.17743000, 9.46299000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q693185'),
(18707, 'Wahlkreis Wil', 1644, 'SG', 214, 'CH', 47.42591000, 9.12451000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q661216'),
(18708, 'Walchwil', 1646, 'ZG', 214, 'CH', 47.10169000, 8.51693000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22440729'),
(18709, 'Wald', 1656, 'ZH', 214, 'CH', 47.27595000, 8.91405000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22440796'),
(18710, 'Waldenburg', 1641, 'BL', 214, 'CH', 47.********, 7.********, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22440950'),
(18711, 'Waldkirch', 1644, 'SG', 214, 'CH', 47.46859000, 9.28665000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q64630'),
(18712, 'Waldstatt', 1655, 'AR', 214, 'CH', 47.35627000, 9.28345000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22441321'),
(18713, 'Walenstadt', 1644, 'SG', 214, 'CH', 47.12411000, 9.31194000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22441445'),
(18714, 'Walkringen', 1645, 'BE', 214, 'CH', 46.94856000, 7.62040000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22542643'),
(18715, 'Wallisellen', 1656, 'ZH', 214, 'CH', 47.41499000, 8.59672000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22543822'),
(18716, 'Wallisellen / Rieden', 1656, 'ZH', 214, 'CH', 47.41738000, 8.60028000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22543840'),
(18717, 'Wallisellen / Wallisellen-Ost', 1656, 'ZH', 214, 'CH', 47.41446000, 8.59727000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22543847'),
(18718, 'Wallisellen / Wallisellen-West', 1656, 'ZH', 214, 'CH', 47.41906000, 8.58586000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22543854'),
(18719, 'Waltenschwil', 1639, 'AG', 214, 'CH', 47.33339000, 8.29791000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22544770'),
(18720, 'Walzenhausen', 1655, 'AR', 214, 'CH', 47.44867000, 9.60495000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22545370'),
(18721, 'Wangen', 1662, 'SO', 214, 'CH', 47.34365000, 7.86982000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22546139'),
(18722, 'Wangen', 1656, 'ZH', 214, 'CH', 47.41182000, 8.64516000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22546145'),
(18723, 'Wangen an der Aare', 1645, 'BE', 214, 'CH', 47.23208000, 7.65253000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22546154'),
(18724, 'Wattenwil', 1645, 'BE', 214, 'CH', 46.76973000, 7.50835000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22554839'),
(18725, 'Wattwil', 1644, 'SG', 214, 'CH', 47.29955000, 9.08657000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22555103'),
(18726, 'Wauwil', 1663, 'LU', 214, 'CH', 47.18457000, 8.02100000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q14623'),
(18727, 'Weesen', 1644, 'SG', 214, 'CH', 47.13447000, 9.09644000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q14623'),
(18728, 'Wegenstetten', 1639, 'AG', 214, 'CH', 47.49797000, 7.93141000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22559280'),
(18729, 'Weggis', 1663, 'LU', 214, 'CH', 47.03208000, 8.43219000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22559314'),
(18730, 'Weinfelden', 1657, 'TG', 214, 'CH', 47.********, 9.10000000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22559587'),
(18731, 'Weinfelden District', 1657, 'TG', 214, 'CH', 47.54237000, 9.15713000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q175071'),
(18732, 'Weiningen', 1656, 'ZH', 214, 'CH', 47.42022000, 8.43644000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22559616'),
(18733, 'Weisslingen', 1656, 'ZH', 214, 'CH', 47.43063000, 8.76787000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22559926'),
(18734, 'Welschenrohr', 1662, 'SO', 214, 'CH', 47.28052000, 7.52664000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22559926'),
(18735, 'Werthenstein', 1663, 'LU', 214, 'CH', 47.05578000, 8.10182000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22562673'),
(18736, 'Wettingen', 1639, 'AG', 214, 'CH', 47.47049000, 8.31636000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22601617'),
(18737, 'Wettswil / Ausser-Dorf', 1656, 'ZH', 214, 'CH', 47.33197000, 8.47732000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22601655'),
(18738, 'Wettswil / Wettswil (Dorf)', 1656, 'ZH', 214, 'CH', 47.34152000, 8.47149000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22601655'),
(18739, 'Wetzikon', 1656, 'ZH', 214, 'CH', 47.32640000, 8.79779000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q68305'),
(18740, 'Wetzikon / Kempten', 1656, 'ZH', 214, 'CH', 47.33319000, 8.80982000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q692132'),
(18741, 'Wetzikon / Ober-Wetzikon', 1656, 'ZH', 214, 'CH', 47.32514000, 8.80005000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18742, 'Wetzikon / Robenhausen', 1656, 'ZH', 214, 'CH', 47.33089000, 8.78762000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18743, 'Wetzikon / Unter-Wetzikon', 1656, 'ZH', 214, 'CH', 47.31637000, 8.79369000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18744, 'Wichtrach', 1645, 'BE', 214, 'CH', 46.85010000, 7.57748000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18745, 'Wiedlisbach', 1645, 'BE', 214, 'CH', 47.25194000, 7.64610000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18746, 'Wiesendangen / Wiesendangen (Dorf)', 1656, 'ZH', 214, 'CH', 47.52170000, 8.78967000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q64853'),
(18747, 'Wigoltingen', 1657, 'TG', 214, 'CH', 47.59770000, 9.03141000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18748, 'Wikon', 1663, 'LU', 214, 'CH', 47.26339000, 7.96801000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18749, 'Wil', 1644, 'SG', 214, 'CH', 47.46152000, 9.04552000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q64077'),
(18750, 'Wila', 1656, 'ZH', 214, 'CH', 47.41928000, 8.84524000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18751, 'Wilchingen', 1654, 'SH', 214, 'CH', 47.66745000, 8.46774000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18752, 'Wilderswil', 1645, 'BE', 214, 'CH', 46.66369000, 7.86175000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, '*********'),
(18753, 'Wildhaus', 1644, 'SG', 214, 'CH', 47.20583000, 9.35402000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q597413'),
(18754, 'Willisau', 1663, 'LU', 214, 'CH', 47.12183000, 7.99418000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22620682'),
(18755, 'Willisau District', 1663, 'LU', 214, 'CH', 47.14097000, 7.95933000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22620682'),
(18756, 'Wimmis', 1645, 'BE', 214, 'CH', 46.67587000, 7.63972000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22624023'),
(18757, 'Windisch', 1639, 'AG', 214, 'CH', 47.47899000, 8.21842000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22624981'),
(18758, 'Winterthur', 1656, 'ZH', 214, 'CH', 47.50564000, 8.72413000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q9125'),
(18759, 'Wittenbach', 1644, 'SG', 214, 'CH', 47.46108000, 9.38601000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22629807'),
(18760, 'Wittnau', 1639, 'AG', 214, 'CH', 47.48139000, 7.97577000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22075295'),
(18761, 'Wohlen', 1639, 'AG', 214, 'CH', 47.35066000, 8.27517000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22630188'),
(18762, 'Wohlen', 1645, 'BE', 214, 'CH', 46.97118000, 7.35685000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22630200'),
(18763, 'Wolfenschiessen', 1652, 'NW', 214, 'CH', 46.90322000, 8.39423000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22632117'),
(18764, 'Wolfhausen', 1656, 'ZH', 214, 'CH', 47.25619000, 8.79910000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22702548'),
(18765, 'Wolfwil', 1662, 'SO', 214, 'CH', 47.26871000, 7.79652000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22632420'),
(18766, 'Wolhusen', 1663, 'LU', 214, 'CH', 47.05983000, 8.07389000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22632439'),
(18767, 'Wollerau', 1653, 'SZ', 214, 'CH', 47.19478000, 8.71903000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22632539'),
(18768, 'Worb', 1645, 'BE', 214, 'CH', 46.92984000, 7.56306000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22637066'),
(18769, 'Worben', 1645, 'BE', 214, 'CH', 47.10279000, 7.29518000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22637087'),
(18770, 'Wynau', 1645, 'BE', 214, 'CH', 47.25570000, 7.81626000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22641034'),
(18771, 'Wynigen', 1645, 'BE', 214, 'CH', 47.10586000, 7.66681000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22641085'),
(18772, 'Wyssachen', 1645, 'BE', 214, 'CH', 47.07851000, 7.82922000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22641364'),
(18773, 'Wädenswil', 1656, 'ZH', 214, 'CH', 47.22683000, 8.66870000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641605'),
(18774, 'Wädenswil / Boller-Giessen', 1656, 'ZH', 214, 'CH', 47.22115000, 8.68385000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641614'),
(18775, 'Wädenswil / Büelen', 1656, 'ZH', 214, 'CH', 47.23395000, 8.66346000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641624'),
(18776, 'Wädenswil / Dorf (Wädenswil)', 1656, 'ZH', 214, 'CH', 47.22923000, 8.67220000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641634'),
(18777, 'Wädenswil / Eichweid', 1656, 'ZH', 214, 'CH', 47.21847000, 8.67440000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641639'),
(18778, 'Wädenswil / Hangenmoos', 1656, 'ZH', 214, 'CH', 47.23367000, 8.65251000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641649'),
(18779, 'Wädenswil / Leihof-Mühlebach', 1656, 'ZH', 214, 'CH', 47.22364000, 8.67149000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641658'),
(18780, 'Wädenswil / Untermosen-Fuhr', 1656, 'ZH', 214, 'CH', 47.22772000, 8.66303000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641667'),
(18781, 'Wängi', 1657, 'TG', 214, 'CH', 47.49654000, 8.95325000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641736'),
(18782, 'Wölflinswil', 1639, 'AG', 214, 'CH', 47.46070000, 7.99835000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641825'),
(18783, 'Wülflingen (Kreis 6)', 1656, 'ZH', 214, 'CH', 47.51036000, 8.68333000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q2596602'),
(18784, 'Wülflingen (Kreis 6) / Härti', 1656, 'ZH', 214, 'CH', 47.51262000, 8.68400000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q2596602'),
(18785, 'Wülflingen (Kreis 6) / Lindenplatz', 1656, 'ZH', 214, 'CH', 47.51098000, 8.69290000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q2596602'),
(18786, 'Wülflingen (Kreis 6) / Niederfeld', 1656, 'ZH', 214, 'CH', 47.50904000, 8.67968000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q2596602'),
(18787, 'Wülflingen (Kreis 6) / Oberfeld', 1656, 'ZH', 214, 'CH', 47.49873000, 8.69897000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q2596602'),
(18788, 'Würenlingen', 1639, 'AG', 214, 'CH', 47.53356000, 8.25666000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641249'),
(18789, 'Würenlos', 1639, 'AG', 214, 'CH', 47.44205000, 8.36261000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22641271'),
(18790, 'Yverdon-les-Bains', 1651, 'VD', 214, 'CH', 46.77852000, 6.64115000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q12174841'),
(18791, 'Yvonand', 1651, 'VD', 214, 'CH', 46.80034000, 6.74249000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q15964301'),
(18792, 'Zell', 1663, 'LU', 214, 'CH', 47.13673000, 7.92495000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22655832'),
(18793, 'Zermatt', 1648, 'VS', 214, 'CH', 46.01998000, 7.74863000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q27494'),
(18794, 'Zernez', 1660, 'GR', 214, 'CH', 46.69862000, 10.09268000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q27494'),
(18795, 'Zizers', 1660, 'GR', 214, 'CH', 46.93575000, 9.56491000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q27494'),
(18796, 'Zofingen', 1639, 'AG', 214, 'CH', 47.28779000, 7.94586000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q27494'),
(18797, 'Zollikerberg', 1656, 'ZH', 214, 'CH', 47.34510000, 8.60088000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q218665'),
(18798, 'Zollikofen', 1645, 'BE', 214, 'CH', 46.99905000, 7.45809000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22658029'),
(18799, 'Zollikon', 1656, 'ZH', 214, 'CH', 47.34019000, 8.57407000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22658040'),
(18800, 'Zuchwil', 1662, 'SO', 214, 'CH', 47.20173000, 7.56649000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22658040'),
(18801, 'Zug', 1646, 'ZG', 214, 'CH', 47.17242000, 8.51745000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22658040'),
(18802, 'Zumikon', 1656, 'ZH', 214, 'CH', 47.33158000, 8.62271000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22658742'),
(18803, 'Zunzgen', 1641, 'BL', 214, 'CH', 47.44925000, 7.80789000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22658914'),
(18804, 'Zuoz', 1660, 'GR', 214, 'CH', 46.60206000, 9.95965000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22658914'),
(18805, 'Zuzwil', 1644, 'SG', 214, 'CH', 47.47452000, 9.11196000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22659047'),
(18806, 'Zweisimmen', 1645, 'BE', 214, 'CH', 46.55539000, 7.37302000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22659075'),
(18807, 'Zwingen', 1641, 'BL', 214, 'CH', 47.43825000, 7.53027000, '2019-10-05 22:36:01', '2019-10-05 22:36:01', 1, 'Q22659164'),
(18808, 'Zäziwil', 1645, 'BE', 214, 'CH', 46.90196000, 7.66185000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22659246'),
(18809, 'Zürich', 1656, 'ZH', 214, 'CH', 47.********, 8.55000000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q22659246'),
(18810, 'Zürich (Kreis 1)', 1656, 'ZH', 214, 'CH', 47.37055000, 8.54177000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q445559'),
(18811, 'Zürich (Kreis 1) / City', 1656, 'ZH', 214, 'CH', 47.37269000, 8.53576000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q445559'),
(18812, 'Zürich (Kreis 1) / Lindenhof', 1656, 'ZH', 214, 'CH', 47.37188000, 8.54036000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q10987378'),
(18813, 'Zürich (Kreis 1) / Rathaus', 1656, 'ZH', 214, 'CH', 47.37161000, 8.54501000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q692511'),
(18814, 'Zürich (Kreis 10)', 1656, 'ZH', 214, 'CH', 47.40773000, 8.50050000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q692511'),
(18815, 'Zürich (Kreis 10) / Höngg', 1656, 'ZH', 214, 'CH', 47.40313000, 8.49710000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q455496'),
(18816, 'Zürich (Kreis 10) / Rütihof', 1656, 'ZH', 214, 'CH', 47.41444000, 8.47928000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q49006175'),
(18817, 'Zürich (Kreis 10) / Wipkingen', 1656, 'ZH', 214, 'CH', 47.39503000, 8.52529000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q678030'),
(18818, 'Zürich (Kreis 11)', 1656, 'ZH', 214, 'CH', 47.42326000, 8.52166000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q678030'),
(18819, 'Zürich (Kreis 11) / Affoltern', 1656, 'ZH', 214, 'CH', 47.41814000, 8.51220000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q382903'),
(18820, 'Zürich (Kreis 11) / Oerlikon', 1656, 'ZH', 214, 'CH', 47.40823000, 8.54258000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q167179'),
(18821, 'Zürich (Kreis 11) / Schwandenholz', 1656, 'ZH', 214, 'CH', 47.42476000, 8.52125000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q49006177'),
(18822, 'Zürich (Kreis 11) / Seebach', 1656, 'ZH', 214, 'CH', 47.42181000, 8.54779000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q276792'),
(18823, 'Zürich (Kreis 12)', 1656, 'ZH', 214, 'CH', 47.40372000, 8.57608000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q657525'),
(18824, 'Zürich (Kreis 12) / Auzelg', 1656, 'ZH', 214, 'CH', 47.41511000, 8.57014000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q49006178'),
(18825, 'Zürich (Kreis 12) / Hirzenbach', 1656, 'ZH', 214, 'CH', 47.40187000, 8.58633000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693374'),
(18826, 'Zürich (Kreis 12) / Saatlen', 1656, 'ZH', 214, 'CH', 47.41127000, 8.56480000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693374'),
(18827, 'Zürich (Kreis 12) / Schwamendingen-Mitte', 1656, 'ZH', 214, 'CH', 47.40630000, 8.57242000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q692728'),
(18828, 'Zürich (Kreis 2)', 1656, 'ZH', 214, 'CH', 47.33756000, 8.52110000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q692728'),
(18829, 'Zürich (Kreis 2) / Enge', 1656, 'ZH', 214, 'CH', 47.36050000, 8.53127000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q648218'),
(18830, 'Zürich (Kreis 2) / Mittel-Leimbach', 1656, 'ZH', 214, 'CH', 47.32538000, 8.51391000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q49006168'),
(18831, 'Zürich (Kreis 2) / Unter-Leimbach', 1656, 'ZH', 214, 'CH', 47.33362000, 8.51433000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q49006168'),
(18832, 'Zürich (Kreis 2) / Wollishofen', 1656, 'ZH', 214, 'CH', 47.34010000, 8.53134000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q642353'),
(18833, 'Zürich (Kreis 3)', 1656, 'ZH', 214, 'CH', 47.35785000, 8.50296000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q675017'),
(18834, 'Zürich (Kreis 3) / Alt-Wiedikon', 1656, 'ZH', 214, 'CH', 47.36201000, 8.51497000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q433012'),
(18835, 'Zürich (Kreis 3) / Friesenberg', 1656, 'ZH', 214, 'CH', 47.36372000, 8.50417000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693357'),
(18836, 'Zürich (Kreis 3) / Sihlfeld', 1656, 'ZH', 214, 'CH', 47.37382000, 8.51164000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693357'),
(18837, 'Zürich (Kreis 4) / Aussersihl', 1656, 'ZH', 214, 'CH', 47.37752000, 8.52127000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q677133'),
(18838, 'Zürich (Kreis 4) / Hard', 1656, 'ZH', 214, 'CH', 47.38311000, 8.50942000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q870084'),
(18839, 'Zürich (Kreis 4) / Langstrasse', 1656, 'ZH', 214, 'CH', 47.37767000, 8.52854000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q1805410'),
(18840, 'Zürich (Kreis 4) / Werd', 1656, 'ZH', 214, 'CH', 47.37178000, 8.52584000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q531899'),
(18841, 'Zürich (Kreis 5)', 1656, 'ZH', 214, 'CH', 47.38767000, 8.52152000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q460885'),
(18842, 'Zürich (Kreis 5) / Escher-Wyss', 1656, 'ZH', 214, 'CH', 47.39052000, 8.51292000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q687052'),
(18843, 'Zürich (Kreis 5) / Gewerbeschule', 1656, 'ZH', 214, 'CH', 47.38481000, 8.53011000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693413'),
(18844, 'Zürich (Kreis 6)', 1656, 'ZH', 214, 'CH', 47.39223000, 8.54381000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693413'),
(18845, 'Zürich (Kreis 6) / Oberstrass', 1656, 'ZH', 214, 'CH', 47.38917000, 8.55040000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693483'),
(18846, 'Zürich (Kreis 6) / Unterstrass', 1656, 'ZH', 214, 'CH', 47.39530000, 8.53721000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q656446'),
(18847, 'Zürich (Kreis 7)', 1656, 'ZH', 214, 'CH', 47.37328000, 8.58038000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q656446'),
(18848, 'Zürich (Kreis 7) / Fluntern', 1656, 'ZH', 214, 'CH', 47.38013000, 8.56133000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693269'),
(18849, 'Zürich (Kreis 7) / Hirslanden', 1656, 'ZH', 214, 'CH', 47.36240000, 8.56755000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q476940'),
(18850, 'Zürich (Kreis 7) / Hottingen', 1656, 'ZH', 214, 'CH', 47.37011000, 8.56306000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693454'),
(18851, 'Zürich (Kreis 7) / Witikon', 1656, 'ZH', 214, 'CH', 47.35751000, 8.59105000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q392079'),
(18852, 'Zürich (Kreis 8)', 1656, 'ZH', 214, 'CH', 47.35480000, 8.56097000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q677710'),
(18853, 'Zürich (Kreis 8) / Mühlebach', 1656, 'ZH', 214, 'CH', 47.35727000, 8.55744000, '2019-10-05 22:36:01', '2020-05-01 17:23:18', 1, 'Q693397'),
(18854, 'Zürich (Kreis 8) / Seefeld', 1656, 'ZH', 214, 'CH', 47.35462000, 8.55537000, '2019-10-05 22:36:02', '2020-05-01 17:23:18', 1, 'Q692773'),
(18855, 'Zürich (Kreis 8) / Weinegg', 1656, 'ZH', 214, 'CH', 47.35250000, 8.57011000, '2019-10-05 22:36:02', '2020-05-01 17:23:18', 1, 'Q693321'),
(18856, 'Zürich (Kreis 9)', 1656, 'ZH', 214, 'CH', 47.38245000, 8.47993000, '2019-10-05 22:36:02', '2020-05-01 17:23:18', 1, 'Q693321'),
(18857, 'Zürich (Kreis 9) / Albisrieden', 1656, 'ZH', 214, 'CH', 47.37398000, 8.49007000, '2019-10-05 22:36:02', '2020-05-01 17:23:18', 1, 'Q80797'),
(18858, 'Zürich (Kreis 9) / Altstetten', 1656, 'ZH', 214, 'CH', 47.38946000, 8.48533000, '2019-10-05 22:36:02', '2020-05-01 17:23:18', 1, 'Q445711'),
(18859, 'Abengourou', 2654, 'CM', 54, 'CI', 6.72972000, -3.49639000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1044225'),
(18860, 'Abidjan', 2634, 'AB', 54, 'CI', 5.30966000, -4.01266000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1515'),
(18861, 'Abobo', 2634, 'AB', 54, 'CI', 5.41613000, -4.01590000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q322624'),
(18862, 'Aboisso', 2654, 'CM', 54, 'CI', 5.46779000, -3.20711000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q521322'),
(18863, 'Adiaké', 2654, 'CM', 54, 'CI', 5.28634000, -3.30403000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q521322'),
(18864, 'Adzopé', 2627, 'LG', 54, 'CI', 6.10694000, -3.86194000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q1270691'),
(18865, 'Affery', 2627, 'LG', 54, 'CI', 6.32035000, -3.95235000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q1270691'),
(18866, 'Agboville', 2627, 'LG', 54, 'CI', 5.92801000, -4.21319000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q2080938'),
(18867, 'Agnibilékrou', 2654, 'CM', 54, 'CI', 7.13113000, -3.20415000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q2080938'),
(18868, 'Agnéby-Tiassa', 2627, 'LG', 54, 'CI', 5.79488000, -4.37187000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q2080938'),
(18869, 'Akoupé', 2627, 'LG', 54, 'CI', 6.38423000, -3.88759000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q2080938'),
(18870, 'Anyama', 2634, 'AB', 54, 'CI', 5.49462000, -4.05183000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2080938'),
(18871, 'Arrah', 2632, 'LC', 54, 'CI', 6.67342000, -3.96938000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q4795810'),
(18872, 'Ayamé', 2654, 'CM', 54, 'CI', 5.60520000, -3.15709000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q4795810'),
(18873, 'Bafing', 2650, 'WR', 54, 'CI', 8.40611000, -7.58048000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q799800'),
(18874, 'Bangolo', 2645, '06', 54, 'CI', 7.01232000, -7.48639000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2882487'),
(18875, 'Biankouma', 2645, '06', 54, 'CI', 7.73909000, -7.61377000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1102796'),
(18876, 'Bingerville', 2634, 'AB', 54, 'CI', 5.35581000, -3.88537000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q863795'),
(18877, 'Bocanda', 2632, 'LC', 54, 'CI', 7.06264000, -4.49948000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q863795'),
(18878, 'Bondoukou', 2641, 'ZZ', 54, 'CI', 8.04020000, -2.80003000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q892276'),
(18879, 'Bongouanou', 2632, 'LC', 54, 'CI', 6.65175000, -4.20406000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q892276'),
(18880, 'Bonoua', 2654, 'CM', 54, 'CI', 5.27247000, -3.59625000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q892276'),
(18881, 'Botro', 2637, 'VB', 54, 'CI', 7.85249000, -5.31063000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q892276'),
(18882, 'Bouaflé', 2648, 'SM', 54, 'CI', 6.99041000, -5.74420000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1588797'),
(18883, 'Bouaké', 2637, 'VB', 54, 'CI', 7.69385000, -5.03031000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q271000'),
(18884, 'Bouna', 2641, 'ZZ', 54, 'CI', 9.26927000, -2.99510000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2922170'),
(18885, 'Bounkani', 2641, 'ZZ', 54, 'CI', 9.47841000, -3.31238000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2922170'),
(18886, 'Bélier', 2632, 'LC', 54, 'CI', 7.02582000, -5.06744000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q2922170'),
(18887, 'Béoumi', 2637, 'VB', 54, 'CI', 7.67395000, -5.58085000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q2922170'),
(18888, 'Béré', 2650, 'WR', 54, 'CI', 8.18952000, -6.17157000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q2922170'),
(18889, 'Cavally', 2645, '06', 54, 'CI', 6.56343000, -7.92526000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2922170'),
(18890, 'Dabakala', 2637, 'VB', 54, 'CI', 8.36321000, -4.42863000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q6571316'),
(18891, 'Dabou', 2627, 'LG', 54, 'CI', 5.32556000, -4.37685000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q1588249'),
(18892, 'Daloa', 2648, 'SM', 54, 'CI', 6.87735000, -6.45022000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q860304'),
(18893, 'Danané', 2645, '06', 54, 'CI', 7.25957000, -8.15498000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q860304'),
(18894, 'Daoukro', 2632, 'LC', 54, 'CI', 7.05910000, -3.96310000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q3015844'),
(18895, 'Dimbokro', 2632, 'LC', 54, 'CI', 6.64678000, -4.70519000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q3015844'),
(18896, 'Divo', 2651, 'GD', 54, 'CI', 5.83739000, -5.35723000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1530295'),
(18897, 'Duekoué', 2645, '06', 54, 'CI', 6.74202000, -7.34918000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1268139'),
(18898, 'Folon', 2644, 'DN', 54, 'CI', 9.81241000, -7.51894000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1268139'),
(18899, 'Gagnoa', 2651, 'GD', 54, 'CI', 6.13193000, -5.95060000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1268139'),
(18900, 'Gbêkê', 2637, 'VB', 54, 'CI', 7.70271000, -5.28511000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q1268139'),
(18901, 'Gbôklé', 2643, 'BS', 54, 'CI', 4.95712000, -6.09372000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1268139'),
(18902, 'Gontougo', 2641, 'ZZ', 54, 'CI', 7.87132000, -3.07068000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1268139'),
(18903, 'Grand-Bassam', 2654, 'CM', 54, 'CI', 5.21180000, -3.73884000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q582922'),
(18904, 'Grand-Lahou', 2627, 'LG', 54, 'CI', 5.25068000, -5.00333000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q1102140'),
(18905, 'Grands-Ponts', 2627, 'LG', 54, 'CI', 5.30487000, -4.39247000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q1102140'),
(18906, 'Guibéroua', 2651, 'GD', 54, 'CI', 6.23869000, -6.17147000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1102140'),
(18907, 'Guiglo', 2645, '06', 54, 'CI', 6.54368000, -7.49350000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1553776'),
(18908, 'Guémon', 2645, '06', 54, 'CI', 7.09300000, -7.17785000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1553776'),
(18909, 'Gôh', 2651, 'GD', 54, 'CI', 6.14459000, -5.92644000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1553776'),
(18910, 'Hambol', 2637, 'VB', 54, 'CI', 8.30368000, -5.15396000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q1553776'),
(18911, 'Haut-Sassandra', 2648, 'SM', 54, 'CI', 6.66961000, -6.50116000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1553776'),
(18912, 'Iffou', 2632, 'LC', 54, 'CI', 7.11509000, -3.95027000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q1553776'),
(18913, 'Indénié-Djuablin', 2654, 'CM', 54, 'CI', 6.74434000, -3.49400000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1553776'),
(18914, 'Issia', 2648, 'SM', 54, 'CI', 6.49224000, -6.58558000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1553776'),
(18915, 'Kabadougou', 2644, 'DN', 54, 'CI', 9.60571000, -7.43774000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1553776'),
(18916, 'Katiola', 2637, 'VB', 54, 'CI', 8.13728000, -5.10095000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q2409805'),
(18917, 'Lakota', 2651, 'GD', 54, 'CI', 5.84752000, -5.68200000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2409805'),
(18918, 'Lôh-Djiboua', 2651, 'GD', 54, 'CI', 5.82483000, -5.47668000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q2409805'),
(18919, 'Man', 2645, '06', 54, 'CI', 7.41251000, -7.55383000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1191011'),
(18920, 'Mankono', 2650, 'WR', 54, 'CI', 8.05861000, -6.18972000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1191011'),
(18921, 'Marahoué', 2648, 'SM', 54, 'CI', 7.03252000, -5.80215000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1191011'),
(18922, 'Moronou', 2632, 'LC', 54, 'CI', 6.66830000, -4.13155000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q1191011'),
(18923, 'N\'Zi', 2632, 'LC', 54, 'CI', 6.82803000, -4.58130000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q1191011'),
(18924, 'Nawa', 2643, 'BS', 54, 'CI', 5.80112000, -6.60313000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1191011'),
(18925, 'Odienné', 2644, 'DN', 54, 'CI', 9.50511000, -7.56433000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q506811'),
(18926, 'Oumé', 2651, 'GD', 54, 'CI', 6.38309000, -5.********, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1102546'),
(18927, 'Sakassou', 2637, 'VB', 54, 'CI', 7.45462000, -5.29263000, '2019-10-05 22:36:02', '2025-07-05 17:00:45', 1, 'Q1102546'),
(18928, 'San-Pédro', 2643, 'BS', 54, 'CI', 4.76768000, -6.65033000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1102546'),
(18929, 'Sassandra', 2643, 'BS', 54, 'CI', 4.95384000, -6.08531000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1102546'),
(18930, 'Sinfra', 2641, 'ZZ', 54, 'CI', 6.62103000, -5.91144000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q1102546'),
(18931, 'Sud-Comoé', 2654, 'CM', 54, 'CI', 5.49961000, -3.24080000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q1102546'),
(18932, 'Séguéla', 2650, 'WR', 54, 'CI', 7.96111000, -6.67306000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q952665'),
(18933, 'Tabou', 2643, 'BS', 54, 'CI', 4.42295000, -7.35280000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q952665'),
(18934, 'Tanda', 2641, 'ZZ', 54, 'CI', 7.80335000, -3.16832000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q952665'),
(18935, 'Tiassalé', 2627, 'LG', 54, 'CI', 5.89839000, -4.82293000, '2019-10-05 22:36:02', '2025-07-05 16:57:49', 1, 'Q952665'),
(18936, 'Tonkpi', 2645, '06', 54, 'CI', 7.56785000, -7.60941000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q952665'),
(18937, 'Touba', 2650, 'WR', 54, 'CI', 8.********, -7.68333000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2308752'),
(18938, 'Toulépleu Gueré', 2645, '06', 54, 'CI', 6.57395000, -8.42592000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q2308752'),
(18939, 'Toumodi', 2632, 'LC', 54, 'CI', 6.55799000, -5.01769000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q2308752'),
(18940, 'Vavoua', 2648, 'SM', 54, 'CI', 7.38194000, -6.47778000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2308752'),
(18941, 'Worodougou', 2650, 'WR', 54, 'CI', 8.16482000, -6.66595000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2308752'),
(18942, 'Yamoussoukro', 2632, 'LC', 54, 'CI', 6.82055000, -5.27674000, '2019-10-05 22:36:02', '2025-07-05 16:58:59', 1, 'Q3768'),
(18943, 'Zuénoula', 2648, 'SM', 54, 'CI', 7.43027000, -6.05054000, '2019-10-05 22:36:02', '2020-05-01 17:22:40', 1, 'Q3768'),
(18944, 'Ancud', 2835, 'LL', 44, 'CL', -41.87070000, -73.81622000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3597'),
(18945, 'Angol', 2826, 'AR', 44, 'CL', -37.79519000, -72.71636000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3608'),
(18946, 'Antofagasta', 2832, 'AN', 44, 'CL', -23.65236000, -70.39540000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3612'),
(18947, 'Arauco', 2827, 'BI', 44, 'CL', -37.24630000, -73.31752000, '2019-10-05 22:36:02', '2021-07-18 12:21:23', 1, 'Q3632'),
(18948, 'Arica', 2829, 'AP', 44, 'CL', -18.47460000, -70.29792000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q2203'),
(18949, 'Buin', 2824, 'RM', 44, 'CL', -33.73257000, -70.74281000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3656'),
(18950, 'Bulnes', 2831, 'NB', 44, 'CL', -36.74232000, -72.29854000, '2019-10-05 22:36:02', '2021-07-18 13:22:42', 1, 'Q3663'),
(18951, 'Cabrero', 2827, 'BI', 44, 'CL', -37.03394000, -72.40468000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3678'),
(18952, 'Calama', 2832, 'AN', 44, 'CL', -22.45667000, -68.92371000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3685'),
(18953, 'Calbuco', 2835, 'LL', 44, 'CL', -41.77338000, -73.13049000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3691'),
(18954, 'Carahue', 2826, 'AR', 44, 'CL', -38.71122000, -73.16101000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3736'),
(18955, 'Cartagena', 2830, 'VS', 44, 'CL', -33.55384000, -71.60761000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3738'),
(18956, 'Castro', 2835, 'LL', 44, 'CL', -42.47210000, -73.77319000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3744'),
(18957, 'Cauquenes', 2833, 'ML', 44, 'CL', -35.96710000, -72.32248000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3749'),
(18958, 'Cañete', 2827, 'BI', 44, 'CL', -37.80128000, -73.39616000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q3753'),
(18959, 'Chaitén', 2835, 'LL', 44, 'CL', -42.91596000, -72.70632000, '2019-10-05 22:36:02', '2021-07-18 14:02:46', 1, 'Q3759'),
(18961, 'Chiguayante', 2827, 'BI', 44, 'CL', -36.92560000, -73.02841000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3765'),
(18962, 'Chile Chico', 2828, 'AI', 44, 'CL', -46.53760000, -71.72930000, '2019-10-05 22:36:02', '2021-07-18 12:16:31', 1, 'Q3770'),
(18963, 'Chillán', 2831, 'NB', 44, 'CL', -36.60664000, -72.10344000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q3774'),
(18964, 'Chimbarongo', 2838, 'LI', 44, 'CL', -34.71247000, -71.04340000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3782'),
(18965, 'Chonchi', 2835, 'LL', 44, 'CL', -42.62387000, -73.77500000, '2019-10-05 22:36:02', '2021-07-18 14:03:02', 1, 'Q3788'),
(18966, 'Cochrane', 2828, 'AI', 44, 'CL', -47.25390000, -72.57320000, '2019-10-05 22:36:02', '2021-07-18 12:17:13', 1, 'Q3800'),
(18967, 'Coihueco', 2831, 'NB', 44, 'CL', -36.62785000, -71.83068000, '2019-10-05 22:36:02', '2021-07-18 13:22:48', 1, 'Q3814'),
(18968, 'Colbún', 2833, 'ML', 44, 'CL', -35.69494000, -71.40568000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q3836'),
(18969, 'Collipulli', 2826, 'AR', 44, 'CL', -37.95453000, -72.43438000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3843'),
(18970, 'Concepción', 2827, 'BI', 44, 'CL', -36.82699000, -73.04977000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q1880'),
(18971, 'Constitución', 2833, 'ML', 44, 'CL', -35.33321000, -72.41156000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q3862'),
(18972, 'Copiapó', 2823, 'AT', 44, 'CL', -27.36650000, -70.33230000, '2019-10-05 22:36:02', '2021-07-18 11:56:29', 1, 'Q3868'),
(18973, 'Coquimbo', 2825, 'CO', 44, 'CL', -29.95332000, -71.33947000, '2019-10-05 22:36:02', '2021-07-18 12:34:16', 1, 'Q23660214'),
(18974, 'Coronel', 2827, 'BI', 44, 'CL', -37.03386000, -73.14019000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3873'),
(18975, 'Corral', 2834, 'LR', 44, 'CL', -39.88730000, -73.43101000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3875'),
(18976, 'Coyhaique', 2828, 'AI', 44, 'CL', -45.57524000, -72.06619000, '2019-10-05 22:36:02', '2021-07-18 12:14:54', 1, 'Q3810'),
(18977, 'Curanilahue', 2827, 'BI', 44, 'CL', -37.47793000, -73.34495000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q3892'),
(18978, 'Curicó', 2833, 'ML', 44, 'CL', -34.98279000, -71.23943000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q13030'),
(18980, 'Dalcahue', 2835, 'LL', 44, 'CL', -42.37845000, -73.65011000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q13031'),
(18981, 'Diego de Almagro', 2823, 'AT', 44, 'CL', -26.********, -70.05000000, '2019-10-05 22:36:02', '2021-07-18 12:01:18', 1, 'Q13033'),
(18982, 'El Monte', 2824, 'RM', 44, 'CL', -33.67969000, -70.98482000, '2019-10-05 22:36:02', '2021-07-18 14:29:45', 1, 'Q13038'),
(18983, 'Freire', 2826, 'AR', 44, 'CL', -38.95252000, -72.62653000, '2019-10-05 22:36:02', '2021-07-18 12:46:14', 1, 'Q13049'),
(18984, 'Futaleufú', 2835, 'LL', 44, 'CL', -43.18492000, -71.86722000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q13060'),
(18985, 'Graneros', 2838, 'LI', 44, 'CL', -34.06863000, -70.72747000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q13068'),
(18988, 'Illapel', 2825, 'CO', 44, 'CL', -31.63349000, -71.16967000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14434'),
(18989, 'Iquique', 2837, 'TA', 44, 'CL', -20.21326000, -70.15027000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14440'),
(18992, 'La Ligua', 2830, 'VS', 44, 'CL', -32.45242000, -71.23106000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14463'),
(18993, 'La Pintana', 2824, 'RM', 44, 'CL', -33.58331000, -70.63419000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14464'),
(18994, 'La Serena', 2825, 'CO', 44, 'CL', -29.90453000, -71.24894000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14467'),
(18995, 'La Unión', 2834, 'LR', 44, 'CL', -40.29313000, -73.08167000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q14469'),
(18996, 'Laja', 2827, 'BI', 44, 'CL', -37.28415000, -72.71105000, '2019-10-05 22:36:02', '2021-07-18 12:22:28', 1, 'Q14475'),
(18997, 'Lampa', 2824, 'RM', 44, 'CL', -33.28630000, -70.87561000, '2019-10-05 22:36:02', '2021-07-18 14:29:36', 1, 'Q14477'),
(19000, 'Lautaro', 2826, 'AR', 44, 'CL', -38.53066000, -72.43652000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14486'),
(19001, 'Lebu', 2827, 'BI', 44, 'CL', -37.60825000, -73.65356000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14488'),
(19002, 'Limache', 2830, 'VS', 44, 'CL', -33.********, -71.26667000, '2019-10-05 22:36:02', '2021-07-18 13:51:10', 1, 'Q14493'),
(19003, 'Linares', 2833, 'ML', 44, 'CL', -35.84667000, -71.59308000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14494'),
(19004, 'Llaillay', 2830, 'VS', 44, 'CL', -32.84043000, -70.95623000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14498'),
(19005, 'Lo Prado', 2824, 'RM', 44, 'CL', -33.44430000, -70.72552000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q14504'),
(19006, 'Loncoche', 2826, 'AR', 44, 'CL', -39.36708000, -72.63087000, '2019-10-05 22:36:02', '2021-07-18 12:46:30', 1, 'Q16900'),
(19007, 'Longaví', 2833, 'ML', 44, 'CL', -35.96496000, -71.68360000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q16901'),
(19008, 'Los Andes', 2830, 'VS', 44, 'CL', -32.83369000, -70.59827000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q16903'),
(19009, 'Los Ángeles', 2827, 'BI', 44, 'CL', -37.46973000, -72.35366000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q16910'),
(19010, 'Lota', 2827, 'BI', 44, 'CL', -37.08994000, -73.15770000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q16911'),
(19011, 'Machalí', 2838, 'LI', 44, 'CL', -34.18082000, -70.64933000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q16915'),
(19012, 'Melipilla', 2824, 'RM', 44, 'CL', -33.68909000, -71.21528000, '2019-10-05 22:36:02', '2021-07-18 14:29:04', 1, 'Q51032'),
(19013, 'Molina', 2833, 'ML', 44, 'CL', -35.11428000, -71.28232000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51033'),
(19014, 'Monte Patria', 2825, 'CO', 44, 'CL', -30.69496000, -70.95770000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51034'),
(19015, 'Mulchén', 2827, 'BI', 44, 'CL', -37.71893000, -72.24099000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q51036'),
(19016, 'Nacimiento', 2827, 'BI', 44, 'CL', -37.50253000, -72.67307000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51039'),
(19017, 'Nueva Imperial', 2826, 'AR', 44, 'CL', -38.74451000, -72.95025000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51050'),
(19018, 'Osorno', 2835, 'LL', 44, 'CL', -40.57395000, -73.13348000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51059'),
(19019, 'Ovalle', 2825, 'CO', 44, 'CL', -30.60106000, -71.19901000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51060'),
(19020, 'Paine', 2824, 'RM', 44, 'CL', -33.80796000, -70.74109000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51070'),
(19021, 'Palena', 2835, 'LL', 44, 'CL', -43.61876000, -71.80434000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51073'),
(19022, 'Panguipulli', 2834, 'LR', 44, 'CL', -39.64355000, -72.33269000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51076'),
(19023, 'Parral', 2833, 'ML', 44, 'CL', -36.14311000, -71.82605000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51083'),
(19024, 'Penco', 2827, 'BI', 44, 'CL', -36.74075000, -72.99528000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51093'),
(19026, 'Peñaflor', 2824, 'RM', 44, 'CL', -33.60627000, -70.87649000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q51098'),
(19027, 'Pitrufquén', 2826, 'AR', 44, 'CL', -38.98635000, -72.63721000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q51115'),
(19028, 'Porvenir', 2836, 'MA', 44, 'CL', -53.********, -70.36666667, '2019-10-05 22:36:02', '2021-07-18 13:14:49', 1, 'Q51120'),
(19029, 'Antártica', 2836, 'MA', 44, 'CL', -75.********, -71.********, '2019-10-05 22:36:02', '2021-07-18 13:13:34', 1, 'Q15050531'),
(19033, 'Mejillones', 2832, 'AN', 44, 'CL', -23.10000000, -70.********, '2019-10-05 22:36:02', '2021-07-18 11:13:30', 1, 'Q16928'),
(19035, 'Camarones', 2829, 'AP', 44, 'CL', -19.01666667, -69.86666667, '2019-10-05 22:36:02', '2021-07-18 11:18:00', 1, 'Q3729'),
(19036, 'Alto Biobío', 2827, 'BI', 44, 'CL', -38.05000000, -71.31666667, '2019-10-05 22:36:02', '2021-07-18 12:27:15', 1, 'Q2213'),
(19042, 'Chañaral', 2823, 'AT', 44, 'CL', -26.34790000, -70.62240000, '2019-10-05 22:36:02', '2021-07-18 12:00:49', 1, 'Q3763'),
(19051, 'Sierra Gorda', 2832, 'AN', 44, 'CL', -22.88333333, -69.31666667, '2019-10-05 22:36:02', '2021-07-18 11:14:23', 1, 'Q188027'),
(19053, 'Huasco', 2823, 'AT', 44, 'CL', -28.********, -71.21666667, '2019-10-05 22:36:02', '2021-07-18 12:04:32', 1, 'Q14422'),
(19055, 'Isla de Pascua', 2830, 'VS', 44, 'CL', -27.11048000, -109.29749000, '2019-10-05 22:36:02', '2021-07-18 13:32:23', 1, 'Q721369'),
(19058, 'Llanquihue', 2835, 'LL', 44, 'CL', -41.34285000, -72.65800000, '2019-10-05 22:36:02', '2021-07-18 14:03:14', 1, 'Q14500'),
(19067, 'Putre', 2829, 'AP', 44, 'CL', -18.19640000, -69.55920000, '2019-10-05 22:36:02', '2021-07-18 11:18:26', 1, 'Q51605'),
(19074, 'Ollagüe', 2832, 'AN', 44, 'CL', -21.22496000, -68.25352000, '2019-10-05 22:36:02', '2021-07-18 11:13:40', 1, 'Q51055'),
(19080, 'Pucón', 2826, 'AR', 44, 'CL', -39.28223000, -71.95427000, '2019-10-05 22:36:02', '2020-05-01 17:22:39', 1, 'Q51590'),
(19081, 'Puente Alto', 2824, 'RM', 44, 'CL', -33.61169000, -70.57577000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51594'),
(19082, 'Aysén', 2828, 'AI', 44, 'CL', -45.40303000, -72.69184000, '2019-10-05 22:36:02', '2021-07-18 12:14:32', 1, 'Q3651'),
(19084, 'Cisnes', 2828, 'AI', 44, 'CL', -44.72750000, -72.68050000, '2019-10-05 22:36:02', '2021-07-18 12:16:58', 1, 'Q940455'),
(19085, 'Puerto Montt', 2835, 'LL', 44, 'CL', -41.46930000, -72.94237000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q36214'),
(19087, 'Puerto Varas', 2835, 'LL', 44, 'CL', -41.31946000, -72.98538000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51596'),
(19088, 'Punta Arenas', 2836, 'MA', 44, 'CL', -53.16270000, -70.90810000, '2019-10-05 22:36:02', '2021-07-18 13:15:03', 1, 'Q51599'),
(19089, 'Purranque', 2835, 'LL', 44, 'CL', -40.91305000, -73.15913000, '2019-10-05 22:36:02', '2019-10-05 22:36:02', 1, 'Q51601'),
(19091, 'Quellón', 2835, 'LL', 44, 'CL', -43.11819000, -73.61661000, '2019-10-05 22:36:03', '2021-07-18 14:03:43', 1, 'Q51608'),
(19092, 'Quillota', 2830, 'VS', 44, 'CL', -32.88341000, -71.24882000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q56108'),
(19093, 'Quilpué', 2830, 'VS', 44, 'CL', -33.04752000, -71.44249000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q56110'),
(19094, 'Quirihue', 2831, 'NB', 44, 'CL', -36.27998000, -72.54118000, '2019-10-05 22:36:03', '2021-07-18 13:22:56', 1, 'Q56115'),
(19095, 'Rancagua', 2838, 'LI', 44, 'CL', -34.17083000, -70.74444000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q4582'),
(19096, 'Rauco', 2833, 'ML', 44, 'CL', -34.92546000, -71.31722000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q56116'),
(19097, 'Rengo', 2838, 'LI', 44, 'CL', -34.40639000, -70.85834000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q56120'),
(19098, 'Río Bueno', 2834, 'LR', 44, 'CL', -40.33494000, -72.95564000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q56126'),
(19099, 'Salamanca', 2825, 'CO', 44, 'CL', -31.77922000, -70.96389000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q56134'),
(19100, 'San Antonio', 2830, 'VS', 44, 'CL', -33.59473000, -71.60746000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q56135'),
(19102, 'San Bernardo', 2824, 'RM', 44, 'CL', -33.59217000, -70.69960000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q56136'),
(19103, 'San Carlos', 2831, 'NB', 44, 'CL', -36.42477000, -71.95800000, '2019-10-05 22:36:03', '2021-07-18 13:23:06', 1, 'Q56137'),
(19104, 'San Clemente', 2833, 'ML', 44, 'CL', -35.53777000, -71.48700000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q56138'),
(19105, 'San Felipe', 2830, 'VS', 44, 'CL', -32.74976000, -70.72584000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q187802'),
(19106, 'San Javier', 2833, 'ML', 44, 'CL', -35.59520000, -71.72924000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q187831'),
(19107, 'San Pedro de Atacama', 2832, 'AN', 44, 'CL', -22.91110000, -68.20113000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q187893'),
(19109, 'San Vicente', 2838, 'LI', 44, 'CL', -34.43859000, -71.07751000, '2019-10-05 22:36:03', '2021-07-18 13:01:19', 1, 'Q187936'),
(19110, 'Santa Cruz', 2838, 'LI', 44, 'CL', -34.63881000, -71.36576000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q187969'),
(19111, 'Santiago', 2824, 'RM', 44, 'CL', -33.45694000, -70.64827000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2887'),
(19112, 'Talagante', 2824, 'RM', 44, 'CL', -33.66386000, -70.92734000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q188038'),
(19113, 'Talca', 2833, 'ML', 44, 'CL', -35.42640000, -71.65542000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q4469'),
(19114, 'Talcahuano', 2827, 'BI', 44, 'CL', -36.72494000, -73.11684000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q41997'),
(19115, 'Taltal', 2832, 'AN', 44, 'CL', -25.40713000, -70.48554000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q188049'),
(19116, 'Temuco', 2826, 'AR', 44, 'CL', -38.73965000, -72.59842000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q82128'),
(19117, 'Teno', 2833, 'ML', 44, 'CL', -34.87055000, -71.16219000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2383688'),
(19118, 'Tocopilla', 2832, 'AN', 44, 'CL', -22.09198000, -70.19792000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q188192'),
(19119, 'Tomé', 2827, 'BI', 44, 'CL', -36.61756000, -72.95593000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q200934'),
(19120, 'Traiguén', 2826, 'AR', 44, 'CL', -38.24960000, -72.67027000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q200961'),
(19121, 'Valdivia', 2834, 'LR', 44, 'CL', -39.81422000, -73.24589000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q160687'),
(19122, 'Vallenar', 2823, 'AT', 44, 'CL', -28.57617000, -70.75938000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q200978'),
(19123, 'Valparaíso', 2830, 'VS', 44, 'CL', -33.03600000, -71.62963000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q33986'),
(19124, 'Victoria', 2826, 'AR', 44, 'CL', -38.23291000, -72.33292000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q200999'),
(19125, 'Vicuña', 2825, 'CO', 44, 'CL', -30.03541000, -70.71274000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q201007'),
(19126, 'Vilcún', 2826, 'AR', 44, 'CL', -38.66875000, -72.22565000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q1643896'),
(19127, 'Villa Alemana', 2830, 'VS', 44, 'CL', -33.04222000, -71.37333000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q174744'),
(19129, 'Villarrica', 2826, 'AR', 44, 'CL', -39.28569000, -72.22790000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q201019'),
(19130, 'Viña del Mar', 2830, 'VS', 44, 'CL', -33.02457000, -71.55183000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q184345'),
(19131, 'Yumbel', 2827, 'BI', 44, 'CL', -37.09820000, -72.56084000, '2019-10-05 22:36:03', '2021-07-18 12:21:34', 1, 'Q201050'),
(19132, 'Abong Mbang', 2661, 'ES', 38, 'CM', 3.98333000, 13.18333000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q322917'),
(19133, 'Akom II', 2659, 'SU', 38, 'CM', 2.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q322917'),
(19134, 'Akono', 2660, 'CE', 38, 'CM', 3.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q322917'),
(19135, 'Akonolinga', 2660, 'CE', 38, 'CM', 3.********, 12.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2829121'),
(19136, 'Ambam', 2659, 'SU', 38, 'CM', 2.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2841553'),
(19137, 'Babanki', 2657, 'NW', 38, 'CM', 6.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2841553'),
(19138, 'Bafang', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q667828'),
(19139, 'Bafia', 2660, 'CE', 38, 'CM', 4.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q799790'),
(19140, 'Bafoussam', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q799808'),
(19141, 'Bali', 2657, 'NW', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q799808'),
(19142, 'Bamenda', 2657, 'NW', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q528432'),
(19143, 'Bamendjou', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q528432'),
(19144, 'Bamusso', 2658, 'SW', 38, 'CM', 4.********, 8.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q528432'),
(19145, 'Bana', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q528432'),
(19146, 'Bandjoun', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2629132'),
(19147, 'Bangangté', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q2629132'),
(19148, 'Bankim', 2663, 'AD', 38, 'CM', 6.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2629132'),
(19149, 'Bansoa', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23676440'),
(19150, 'Banyo', 2663, 'AD', 38, 'CM', 6.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q520600'),
(19151, 'Batibo', 2657, 'NW', 38, 'CM', 5.********, 9.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q520600'),
(19152, 'Batouri', 2661, 'ES', 38, 'CM', 4.********, 14.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q673185'),
(19153, 'Bazou', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q673185'),
(19154, 'Bekondo', 2658, 'SW', 38, 'CM', 4.********, 9.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q25337079'),
(19155, 'Belo', 2657, 'NW', 38, 'CM', 6.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q25337079'),
(19156, 'Bertoua', 2661, 'ES', 38, 'CM', 4.57728000, 13.68459000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q657734'),
(19157, 'Bogo', 2656, 'EN', 38, 'CM', 10.73360000, 14.60928000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23676899'),
(19158, 'Bonabéri', 2662, 'LT', 38, 'CM', 4.07142000, 9.68177000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q4941069'),
(19159, 'Boyo', 2657, 'NW', 38, 'CM', 6.36365000, 10.35540000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q4941069'),
(19160, 'Buea', 2658, 'SW', 38, 'CM', 4.15342000, 9.24231000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q312209'),
(19161, 'Bélabo', 2661, 'ES', 38, 'CM', 4.93333000, 13.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q2929807'),
(19162, 'Bélel', 2663, 'AD', 38, 'CM', 7.05000000, 14.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q2929807'),
(19163, 'Bétaré Oya', 2661, 'ES', 38, 'CM', 5.60000000, 14.08333000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q23676678'),
(19164, 'Diang', 2662, 'LT', 38, 'CM', 4.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23676202'),
(19165, 'Dibombari', 2662, 'LT', 38, 'CM', 4.17870000, 9.65610000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23676202'),
(19166, 'Dimako', 2661, 'ES', 38, 'CM', 4.********, 13.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1100817'),
(19167, 'Dizangué', 2662, 'LT', 38, 'CM', 3.********, 9.98333000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q1100817'),
(19168, 'Djohong', 2663, 'AD', 38, 'CM', 6.83333000, 14.70000000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1100817'),
(19169, 'Douala', 2662, 'LT', 38, 'CM', 4.04827000, 9.70428000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q132830'),
(19170, 'Doumé', 2661, 'ES', 38, 'CM', 4.********, 13.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q132830'),
(19171, 'Dschang', 2664, 'OU', 38, 'CM', 5.44397000, 10.05332000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q289267'),
(19172, 'Edéa', 2662, 'LT', 38, 'CM', 3.80000000, 10.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q1002900'),
(19173, 'Essé', 2660, 'CE', 38, 'CM', 4.10000000, 11.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q23675648'),
(19174, 'Eséka', 2660, 'CE', 38, 'CM', 3.65000000, 10.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q3591508'),
(19175, 'Fako Division', 2658, 'SW', 38, 'CM', 4.16667000, 9.16667000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q955885'),
(19176, 'Faro Department', 2665, 'NO', 38, 'CM', 8.25014000, 12.87829000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q955885'),
(19177, 'Fontem', 2658, 'SW', 38, 'CM', 5.46850000, 9.88180000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q9261275'),
(19178, 'Foumban', 2664, 'OU', 38, 'CM', 5.72662000, 10.89865000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1439847'),
(19179, 'Foumbot', 2664, 'OU', 38, 'CM', 5.50803000, 10.63250000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1439847'),
(19180, 'Fundong', 2657, 'NW', 38, 'CM', 6.********, 10.26667000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1439847'),
(19181, 'Garoua', 2665, 'NO', 38, 'CM', 9.30143000, 13.39771000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q157915'),
(19182, 'Garoua Boulaï', 2661, 'ES', 38, 'CM', 5.88333000, 14.55000000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q5523752'),
(19183, 'Guider', 2665, 'NO', 38, 'CM', 9.93330000, 13.94671000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2708439'),
(19184, 'Hauts-Plateaux', 2664, 'OU', 38, 'CM', 5.29632000, 10.34314000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443778'),
(19185, 'Jakiri', 2657, 'NW', 38, 'CM', 6.10000000, 10.65000000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443778'),
(19186, 'Kaélé', 2656, 'EN', 38, 'CM', 10.10917000, 14.45083000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q2443778'),
(19187, 'Kontcha', 2663, 'AD', 38, 'CM', 7.96667000, 12.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443778'),
(19188, 'Koung-Khi', 2664, 'OU', 38, 'CM', 5.33848000, 10.47453000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443749'),
(19189, 'Kousséri', 2656, 'EN', 38, 'CM', 12.07689000, 15.03063000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q697662'),
(19190, 'Koza', 2656, 'EN', 38, 'CM', 10.86846000, 13.88205000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23672974'),
(19191, 'Kribi', 2659, 'SU', 38, 'CM', 2.93725000, 9.90765000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q997121'),
(19192, 'Kumba', 2658, 'SW', 38, 'CM', 4.63630000, 9.44690000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q997047'),
(19193, 'Kumbo', 2657, 'NW', 38, 'CM', 6.20000000, 10.66667000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2533580'),
(19194, 'Lagdo', 2665, 'NO', 38, 'CM', 9.05828000, 13.66605000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2533580'),
(19195, 'Lebialem', 2658, 'SW', 38, 'CM', 5.56043000, 9.92316000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443757'),
(19196, 'Limbe', 2658, 'SW', 38, 'CM', 4.02356000, 9.20607000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q819314'),
(19197, 'Lolodorf', 2659, 'SU', 38, 'CM', 3.********, 10.73333000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q819314'),
(19198, 'Loum', 2662, 'LT', 38, 'CM', 4.71820000, 9.73510000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1828275'),
(19199, 'Makary', 2656, 'EN', 38, 'CM', 12.57535000, 14.45483000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q6738809'),
(19200, 'Mamfe', 2658, 'SW', 38, 'CM', 5.75132000, 9.31370000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1888230'),
(19201, 'Manjo', 2662, 'LT', 38, 'CM', 4.84280000, 9.82170000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q1888230'),
(19202, 'Maroua', 2656, 'EN', 38, 'CM', 10.59095000, 14.31593000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q818824'),
(19203, 'Mayo-Banyo', 2663, 'AD', 38, 'CM', 6.58138000, 11.73522000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443291'),
(19204, 'Mayo-Louti', 2665, 'NO', 38, 'CM', 9.96577000, 13.72738000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443752'),
(19205, 'Mayo-Rey', 2665, 'NO', 38, 'CM', 8.12630000, 14.61456000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2443767'),
(19206, 'Mayo-Sava', 2656, 'EN', 38, 'CM', 11.10682000, 14.20560000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q613340'),
(19207, 'Mayo-Tsanaga', 2656, 'EN', 38, 'CM', 10.58221000, 13.79351000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q613340'),
(19208, 'Mbalmayo', 2660, 'CE', 38, 'CM', 3.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2702755'),
(19209, 'Mbam-Et-Inoubou', 2660, 'CE', 38, 'CM', 4.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2702755'),
(19210, 'Mbandjok', 2660, 'CE', 38, 'CM', 4.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2702755'),
(19211, 'Mbang', 2661, 'ES', 38, 'CM', 4.********, 13.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23671107'),
(19212, 'Mbanga', 2662, 'LT', 38, 'CM', 4.********, 9.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23671107'),
(19213, 'Mbankomo', 2660, 'CE', 38, 'CM', 3.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23671107'),
(19214, 'Mbengwi', 2657, 'NW', 38, 'CM', 6.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23671107'),
(19215, 'Mbouda', 2664, 'OU', 38, 'CM', 5.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2709339'),
(19216, 'Mefou-et-Akono', 2660, 'CE', 38, 'CM', 3.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2444484'),
(19217, 'Melong', 2662, 'LT', 38, 'CM', 5.********, 9.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2444484'),
(19218, 'Meïganga', 2663, 'AD', 38, 'CM', 6.********, 14.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q2629052'),
(19219, 'Mfoundi', 2660, 'CE', 38, 'CM', 3.********, 11.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2445046'),
(19220, 'Mindif', 2656, 'EN', 38, 'CM', 10.********, 14.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2445046'),
(19221, 'Minta', 2660, 'CE', 38, 'CM', 4.********, 12.80000000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2445046'),
(19222, 'Mme-Bafumen', 2657, 'NW', 38, 'CM', 6.********, 10.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23669912'),
(19223, 'Mokolo', 2656, 'EN', 38, 'CM', 10.74244000, 13.80227000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2708658'),
(19224, 'Mora', 2656, 'EN', 38, 'CM', 11.04611000, 14.14011000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2708675'),
(19225, 'Mouanko', 2662, 'LT', 38, 'CM', 3.63972000, 9.77694000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2708675'),
(19226, 'Mundemba', 2658, 'SW', 38, 'CM', 4.94790000, 8.87240000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q2708675'),
(19227, 'Mutengene', 2658, 'SW', 38, 'CM', 4.09130000, 9.31440000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q10334043'),
(19228, 'Muyuka', 2658, 'SW', 38, 'CM', 4.28980000, 9.41030000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23667291'),
(19229, 'Mvangué', 2659, 'SU', 38, 'CM', 2.96667000, 11.********, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q23667291'),
(19230, 'Mvila', 2659, 'SU', 38, 'CM', 2.79796000, 11.39434000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23667291'),
(19231, 'Nanga Eboko', 2660, 'CE', 38, 'CM', 4.68333000, 12.********, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23667291'),
(19232, 'Ndelele', 2661, 'ES', 38, 'CM', 4.04065000, 14.92501000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q23667291'),
(19233, 'Ndikiniméki', 2660, 'CE', 38, 'CM', 4.********, 10.83333000, '2019-10-05 22:36:03', '2020-05-01 17:22:39', 1, 'Q23667291'),
(19234, 'Ndom', 2662, 'LT', 38, 'CM', 4.49780000, 9.56280000, '2019-10-05 22:36:03', '2019-10-05 22:36:03', 1, 'Q12698776');

