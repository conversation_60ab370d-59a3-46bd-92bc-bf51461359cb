INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(92528, 'Comuna Padina', 4756, '<PERSON><PERSON>', 181, 'RO', 44.87082000, 27.11566000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q2716490'),
(92529, 'Comuna Padina', 4751, 'MH', 181, 'RO', 44.43776000, 23.01426000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q2605887'),
(92530, 'Comuna Palanca', 4744, 'BC', 181, 'RO', 46.50647000, 26.11239000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q1087620'),
(92531, '<PERSON>muna Paleu', 4723, '<PERSON><PERSON>', 181, '<PERSON><PERSON>', 47.10762000, 21.97748000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q288125'),
(92532, 'Comuna Paltin', 4758, 'VN', 181, 'RO', 45.77918000, 26.74770000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725018'),
(92533, 'Comuna Panaci', 4720, 'SV', 181, 'RO', 47.19584000, 25.43169000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725021'),
(92534, 'Comuna Pantelimon', 4737, 'CT', 181, 'RO', 44.60047000, 28.36014000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5063949'),
(92535, 'Comuna Panticeu', 4734, 'CJ', 181, 'RO', 47.04079000, 23.55639000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725020'),
(92536, 'Comuna Parava', 4744, 'BC', 181, 'RO', 46.30993000, 26.96787000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q1087627'),
(92537, 'Comuna Pardina', 4727, 'TL', 181, 'RO', 45.30780000, 28.95691000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5070645'),
(92538, 'Comuna Pardoşi', 4756, 'BZ', 181, 'RO', 45.44989000, 26.87934000, '2019-10-05 23:15:01', '2020-05-01 17:23:09', 1, 'Q12699584'),
(92539, 'Comuna Parincea', 4744, 'BC', 181, 'RO', 46.47841000, 27.12877000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q1195040'),
(92540, 'Comuna Parva', 4733, 'BN', 181, 'RO', 47.39601000, 24.54218000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q12725022'),
(92541, 'Comuna Parţa', 4748, 'TM', 181, 'RO', 45.62888000, 21.13877000, '2019-10-05 23:15:01', '2020-05-01 17:23:11', 1, 'Q12725025'),
(92542, 'Comuna Peceneaga', 4727, 'TL', 181, 'RO', 45.00849000, 28.14872000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5070636'),
(92543, 'Comuna Pechea', 4747, 'GL', 181, 'RO', 45.64303000, 27.82756000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5113634'),
(92544, 'Comuna Pecineaga', 4737, 'CT', 181, 'RO', 43.88676000, 28.51012000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5066493'),
(92545, 'Comuna Peciu Nou', 4748, 'TM', 181, 'RO', 45.62070000, 21.00816000, '2019-10-05 23:15:01', '2019-10-05 23:15:01', 1, 'Q5060192'),
(92546, 'Comuna Peregu Mare', 4739, 'AR', 181, 'RO', 46.23377000, 20.90612000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12139552'),
(92547, 'Comuna Peretu', 4728, 'TR', 181, 'RO', 44.04372000, 25.09285000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725026'),
(92548, 'Comuna Periam', 4748, 'TM', 181, 'RO', 46.04394000, 20.87517000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5059294'),
(92549, 'Comuna Pericei', 4741, 'SJ', 181, 'RO', 47.24643000, 22.87644000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16427371'),
(92550, 'Comuna Perieni', 4752, 'VS', 181, 'RO', 46.30407000, 27.61323000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725029'),
(92551, 'Comuna Perieţi', 4743, 'IL', 181, 'RO', 44.57191000, 27.26027000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q250253'),
(92552, 'Comuna Perieţi', 4738, 'OT', 181, 'RO', 44.40468000, 24.55611000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q16427143'),
(92553, 'Comuna Periş', 4725, 'IF', 181, 'RO', 44.70859000, 26.01472000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q1656754'),
(92554, 'Comuna Perişani', 4757, 'VL', 181, 'RO', 45.37925000, 24.40776000, '2019-10-05 23:15:02', '2020-05-01 17:23:12', 1, 'Q2603579'),
(92555, 'Comuna Perişor', 4742, 'DJ', 181, 'RO', 44.15843000, 23.49214000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q12725030'),
(92556, 'Comuna Perişoru', 4732, 'CL', 181, 'RO', 44.43266000, 27.50197000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q2716841'),
(92557, 'Comuna Perşinari', 4745, 'DB', 181, 'RO', 44.80185000, 25.49880000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q2538229'),
(92558, 'Comuna Pesac', 4748, 'TM', 181, 'RO', 45.99590000, 20.83522000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5055881'),
(92559, 'Comuna Pescari', 4753, 'CS', 181, 'RO', 44.67286000, 21.70092000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16426006'),
(92560, 'Comuna Pesceana', 4757, 'VL', 181, 'RO', 44.88169000, 24.12797000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16427895'),
(92561, 'Comuna Pestişu Mic', 4721, 'HD', 181, 'RO', 45.80178000, 22.83551000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q999380'),
(92562, 'Comuna Petreşti', 4746, 'SM', 181, 'RO', 47.58993000, 22.35972000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q5064046'),
(92563, 'Comuna Petreşti', 4745, 'DB', 181, 'RO', 44.66761000, 25.31458000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q1912804'),
(92564, 'Comuna Petreştii De Jos', 4734, 'CJ', 181, 'RO', 46.58162000, 23.62191000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q12725032'),
(92565, 'Comuna Petricani', 4731, 'NT', 181, 'RO', 47.15304000, 26.44114000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5112198'),
(92566, 'Comuna Petriş', 4739, 'AR', 181, 'RO', 46.06770000, 22.39523000, '2019-10-05 23:15:02', '2020-05-01 17:23:08', 1, 'Q5064094'),
(92567, 'Comuna Petrova', 4760, 'MM', 181, 'RO', 47.83003000, 24.21603000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5063770'),
(92568, 'Comuna Petru Rareş', 4733, 'BN', 181, 'RO', 47.19221000, 24.00731000, '2019-10-05 23:15:02', '2020-05-01 17:23:08', 1, 'Q2621179'),
(92569, 'Comuna Petrăchioaia', 4725, 'IF', 181, 'RO', 44.57953000, 26.31114000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q1703719'),
(92570, 'Comuna Peştera', 4737, 'CT', 181, 'RO', 44.18951000, 28.10722000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q5054134'),
(92571, 'Comuna Peştişani', 4750, 'GJ', 181, 'RO', 45.07061000, 23.04902000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q16426644'),
(92572, 'Comuna Pianu', 4724, 'AB', 181, 'RO', 45.87127000, 23.49549000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q182693'),
(92573, 'Comuna Piatra', 4728, 'TR', 181, 'RO', 43.81058000, 25.16777000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16427646'),
(92574, 'Comuna Piatra Şoimului', 4731, 'NT', 181, 'RO', 46.82443000, 26.41848000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q5066698'),
(92575, 'Comuna Pieleşti', 4742, 'DJ', 181, 'RO', 44.35632000, 23.97570000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q12725036'),
(92576, 'Comuna Pietrari', 4745, 'DB', 181, 'RO', 45.10034000, 25.29356000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2288693'),
(92577, 'Comuna Pietrari', 4757, 'VL', 181, 'RO', 45.11586000, 24.10242000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16427898'),
(92578, 'Comuna Pietroasa', 4723, 'BH', 181, 'RO', 46.58383000, 22.63371000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16425685'),
(92579, 'Comuna Pietroasa', 4748, 'TM', 181, 'RO', 45.82012000, 22.41805000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12137219'),
(92580, 'Comuna Pietroasele', 4756, 'BZ', 181, 'RO', 45.10357000, 26.59143000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2621212'),
(92581, 'Comuna Pietroşani', 4728, 'TR', 181, 'RO', 43.71364000, 25.63998000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q16427649'),
(92582, 'Comuna Pietroşani', 4722, 'AG', 181, 'RO', 45.15375000, 24.84439000, '2019-10-05 23:15:02', '2020-05-01 17:23:08', 1, 'Q3396126'),
(92583, 'Comuna Pietroşiţa', 4745, 'DB', 181, 'RO', 45.18773000, 25.42828000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q5071076'),
(92584, 'Comuna Pilu', 4739, 'AR', 181, 'RO', 46.59727000, 21.34711000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5062839'),
(92585, 'Comuna Pipirig', 4731, 'NT', 181, 'RO', 47.22800000, 26.08483000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5112415'),
(92586, 'Comuna Pir', 4746, 'SM', 181, 'RO', 47.46650000, 22.39380000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5054734'),
(92587, 'Comuna Piscu', 4747, 'GL', 181, 'RO', 45.51681000, 27.71645000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5113820'),
(92588, 'Comuna Piscu Vechi', 4742, 'DJ', 181, 'RO', 43.89938000, 23.16364000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16426496'),
(92589, 'Comuna Pişchia', 4748, 'TM', 181, 'RO', 45.90460000, 21.40241000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q12146428'),
(92590, 'Comuna Pişcolt', 4746, 'SM', 181, 'RO', 47.59761000, 22.27498000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q5055091'),
(92591, 'Comuna Platoneşti', 4743, 'IL', 181, 'RO', 44.60713000, 27.68637000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q2291850'),
(92592, 'Comuna Pleniţa', 4742, 'DJ', 181, 'RO', 44.22532000, 23.16364000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q2089057'),
(92593, 'Comuna Pleşcuţa', 4739, 'AR', 181, 'RO', 46.28315000, 22.43178000, '2019-10-05 23:15:02', '2020-05-01 17:23:08', 1, 'Q1895138'),
(92594, 'Comuna Pleşoi', 4742, 'DJ', 181, 'RO', 44.35380000, 23.54284000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q2538633'),
(92595, 'Comuna Pleşoiu', 4738, 'OT', 181, 'RO', 44.48165000, 24.24212000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q12725038'),
(92596, 'Comuna Plopana', 4744, 'BC', 181, 'RO', 46.66549000, 27.24403000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2469285'),
(92597, 'Comuna Plopii Slăviţeşti', 4728, 'TR', 181, 'RO', 43.96098000, 24.68525000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q12725040'),
(92598, 'Comuna Plopiş', 4741, 'SJ', 181, 'RO', 47.11425000, 22.64240000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q16427375'),
(92599, 'Comuna Plopu', 4729, 'PH', 181, 'RO', 45.02686000, 26.14227000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q1079507'),
(92600, 'Comuna Plopşoru', 4750, 'GJ', 181, 'RO', 44.73869000, 23.39053000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q12725041'),
(92601, 'Comuna Plosca', 4728, 'TR', 181, 'RO', 44.02338000, 25.14920000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16427654'),
(92602, 'Comuna Ploscoş', 4734, 'CJ', 181, 'RO', 46.66066000, 23.84322000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q12725045'),
(92603, 'Comuna Ploscuţeni', 4758, 'VN', 181, 'RO', 46.06103000, 27.26595000, '2019-10-05 23:15:02', '2020-05-01 17:23:12', 1, 'Q937108'),
(92604, 'Comuna Plugari', 4735, 'IS', 181, 'RO', 47.47732000, 27.11577000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5113832'),
(92605, 'Comuna Plãieşii De Jos', 4749, 'HR', 181, 'RO', 46.21626000, 26.09436000, '2019-10-05 23:15:02', '2020-05-01 17:23:10', 1, 'Q12140915'),
(92606, 'Comuna Plătăreşti', 4732, 'CL', 181, 'RO', 44.34476000, 26.38255000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q2716848'),
(92607, 'Comuna Poarta Albă', 4737, 'CT', 181, 'RO', 44.21622000, 28.41904000, '2019-10-05 23:15:02', '2020-05-01 17:23:09', 1, 'Q1188568'),
(92608, 'Comuna Poboru', 4738, 'OT', 181, 'RO', 44.68335000, 24.48678000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16427147'),
(92609, 'Comuna Pochidia', 4752, 'VS', 181, 'RO', 46.05233000, 27.58450000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725042'),
(92610, 'Comuna Pocola', 4723, 'BH', 181, 'RO', 46.69894000, 22.29593000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16425688'),
(92611, 'Comuna Podari', 4742, 'DJ', 181, 'RO', 44.24394000, 23.79217000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725043'),
(92612, 'Comuna Podeni', 4751, 'MH', 181, 'RO', 44.89218000, 22.55503000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725048'),
(92613, 'Comuna Podenii Noi', 4729, 'PH', 181, 'RO', 45.10423000, 26.19023000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2605330'),
(92614, 'Comuna Podgoria', 4756, 'BZ', 181, 'RO', 45.45591000, 27.00626000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2716867'),
(92615, 'Comuna Podoleni', 4731, 'NT', 181, 'RO', 46.81772000, 26.63280000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5113807'),
(92616, 'Comuna Podu Turcului', 4744, 'BC', 181, 'RO', 46.19703000, 27.38796000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q1195136'),
(92617, 'Comuna Poduri', 4744, 'BC', 181, 'RO', 46.47888000, 26.56593000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2469277'),
(92618, 'Comuna Poeni', 4728, 'TR', 181, 'RO', 44.43297000, 25.33868000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725044'),
(92619, 'Comuna Pogana', 4752, 'VS', 181, 'RO', 46.33546000, 27.55360000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q13571648'),
(92620, 'Comuna Pogoneşti', 4752, 'VS', 181, 'RO', 46.14748000, 27.52665000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q16427834'),
(92621, 'Comuna Poian', 4754, 'CV', 181, 'RO', 46.07731000, 26.17423000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16426331'),
(92622, 'Comuna Poiana', 4747, 'GL', 181, 'RO', 45.99554000, 27.26693000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q3726432'),
(92623, 'Comuna Poiana', 4745, 'DB', 181, 'RO', 44.57429000, 25.67684000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q1483885'),
(92624, 'Comuna Poiana  Ilvei', 4733, 'BN', 181, 'RO', 47.35749000, 24.74104000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16425782'),
(92625, 'Comuna Poiana Blenchii', 4741, 'SJ', 181, 'RO', 47.30626000, 23.76012000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16427378'),
(92626, 'Comuna Poiana Cristei', 4758, 'VN', 181, 'RO', 45.66882000, 26.97125000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2722773'),
(92627, 'Comuna Poiana Câmpina', 4729, 'PH', 181, 'RO', 45.12079000, 25.71905000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q2605280'),
(92628, 'Comuna Poiana Lacului', 4722, 'AG', 181, 'RO', 44.81199000, 24.71296000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16425522'),
(92629, 'Comuna Poiana Mare', 4742, 'DJ', 181, 'RO', 43.92040000, 23.06271000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16426501'),
(92630, 'Comuna Poiana Mărului (Brașov)', 4759, 'BV', 181, 'RO', 45.60462000, 25.30927000, '2019-10-05 23:15:02', '2020-05-01 17:23:08', 1, 'Q580851'),
(92631, 'Comuna Poiana Sibiului', 4755, 'SB', 181, 'RO', 45.80920000, 23.73095000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5064344'),
(92632, 'Comuna Poiana Stampei', 4720, 'SV', 181, 'RO', 47.31118000, 25.12932000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725046'),
(92633, 'Comuna Poiana Teiului', 4731, 'NT', 181, 'RO', 47.11403000, 25.92517000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5113569'),
(92634, 'Comuna Poiana Vadului', 4724, 'AB', 181, 'RO', 46.40457000, 22.87080000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16425329'),
(92635, 'Comuna Poienari', 4731, 'NT', 181, 'RO', 46.88625000, 27.11966000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5069717'),
(92636, 'Comuna Poienarii Burchii', 4729, 'PH', 181, 'RO', 44.74995000, 25.99784000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5069856'),
(92637, 'Comuna Poienarii de Argeş', 4722, 'AG', 181, 'RO', 45.06667000, 24.53333000, '2019-10-05 23:15:02', '2020-05-01 17:23:08', 1, 'Q1896924'),
(92638, 'Comuna Poienarii de Muscel', 4722, 'AG', 181, 'RO', 45.21667000, 25.05000000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q2486611'),
(92639, 'Comuna Poieneşti', 4752, 'VS', 181, 'RO', 46.57978000, 27.54608000, '2019-10-05 23:15:02', '2020-05-01 17:23:11', 1, 'Q12725047'),
(92640, 'Comuna Poieni', 4734, 'CJ', 181, 'RO', 46.88361000, 22.86041000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q16426229'),
(92641, 'Comuna Poieni-Solca', 4720, 'SV', 181, 'RO', 47.68626000, 25.89374000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725049'),
(92642, 'Comuna Poienile Izei', 4760, 'MM', 181, 'RO', 47.70309000, 24.11276000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q5054115'),
(92643, 'Comuna Poienile de sub Munte', 4760, 'MM', 181, 'RO', 47.82371000, 24.43674000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12149439'),
(92644, 'Comuna Pojejena', 4753, 'CS', 181, 'RO', 44.78437000, 21.53365000, '2019-10-05 23:15:02', '2019-10-05 23:15:02', 1, 'Q12725050'),
(92645, 'Comuna Pojorâta', 4720, 'SV', 181, 'RO', 47.50476000, 25.42180000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q12725051'),
(92646, 'Comuna Polovragi', 4750, 'GJ', 181, 'RO', 45.17196000, 23.82134000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12725053'),
(92647, 'Comuna Pomezeu', 4723, 'BH', 181, 'RO', 46.79113000, 22.30018000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q16425692'),
(92648, 'Comuna Pomi', 4746, 'SM', 181, 'RO', 47.68071000, 23.32362000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5055124'),
(92649, 'Comuna Pomârla', 4740, 'BT', 181, 'RO', 48.06225000, 26.32090000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q12725052'),
(92650, 'Comuna Ponoarele', 4751, 'MH', 181, 'RO', 44.97347000, 22.75676000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q288036'),
(92651, 'Comuna Ponor', 4724, 'AB', 181, 'RO', 46.32065000, 23.40376000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q16425333'),
(92652, 'Comuna Popeşti', 4722, 'AG', 181, 'RO', 44.44521000, 25.09114000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q3407579'),
(92653, 'Comuna Popeşti', 4723, 'BH', 181, 'RO', 47.22088000, 22.41515000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q12142364'),
(92654, 'Comuna Popeşti', 4735, 'IS', 181, 'RO', 47.13704000, 27.26169000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q5112844'),
(92655, 'Comuna Popeşti', 4758, 'VN', 181, 'RO', 45.58983000, 27.06560000, '2019-10-05 23:15:03', '2020-05-01 17:23:12', 1, 'Q3915463'),
(92656, 'Comuna Popeşti', 4757, 'VL', 181, 'RO', 44.97908000, 24.11014000, '2019-10-05 23:15:03', '2020-05-01 17:23:12', 1, 'Q3915283'),
(92657, 'Comuna Poplaca', 4755, 'SB', 181, 'RO', 45.72815000, 24.05380000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5056058'),
(92658, 'Comuna Popricani', 4735, 'IS', 181, 'RO', 47.26750000, 27.52729000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5113017'),
(92659, 'Comuna Poroina Mare', 4751, 'MH', 181, 'RO', 44.49516000, 22.94161000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12725056'),
(92660, 'Comuna Poroschia', 4728, 'TR', 181, 'RO', 43.92887000, 25.35760000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q16427657'),
(92661, 'Comuna Porumbacu de Jos', 4755, 'SB', 181, 'RO', 45.75078000, 24.49551000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12142577'),
(92662, 'Comuna Porumbeni', 4749, 'HR', 181, 'RO', 46.27415000, 25.12006000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q13470064'),
(92663, 'Comuna Porumbeşti', 4746, 'SM', 181, 'RO', 47.98491000, 22.97236000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q5071370'),
(92664, 'Comuna Poseşti', 4729, 'PH', 181, 'RO', 45.27519000, 26.14944000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q282866'),
(92665, 'Comuna Potlogi', 4745, 'DB', 181, 'RO', 44.57362000, 25.60164000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q968375'),
(92666, 'Comuna Poşaga', 4724, 'AB', 181, 'RO', 46.45354000, 23.36115000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q935480'),
(92667, 'Comuna Poşta Câlnãu', 4756, 'BZ', 181, 'RO', 45.24683000, 26.86785000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q8272837'),
(92668, 'Comuna Praid', 4749, 'HR', 181, 'RO', 46.54844000, 25.14717000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5059444'),
(92669, 'Comuna Predeal-Sărari', 4729, 'PH', 181, 'RO', 45.18891000, 26.10613000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q2605511'),
(92670, 'Comuna Predeşti', 4742, 'DJ', 181, 'RO', 44.34640000, 23.58506000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q12725058'),
(92671, 'Comuna Prejmer', 4759, 'BV', 181, 'RO', 45.73164000, 25.78673000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q1021466'),
(92672, 'Comuna Preuteşti', 4720, 'SV', 181, 'RO', 47.45479000, 26.42181000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q16427547'),
(92673, 'Comuna Priboieni', 4722, 'AG', 181, 'RO', 44.87728000, 25.08680000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12725060'),
(92674, 'Comuna Prigor', 4753, 'CS', 181, 'RO', 44.94774000, 22.12896000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q16426040'),
(92675, 'Comuna Prigoria', 4750, 'GJ', 181, 'RO', 45.05711000, 23.68453000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12725059'),
(92676, 'Comuna Priponeşti', 4747, 'GL', 181, 'RO', 46.10150000, 27.43939000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q2289429'),
(92677, 'Comuna Priseaca', 4738, 'OT', 181, 'RO', 44.51206000, 24.43787000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12725061'),
(92678, 'Comuna Pristol', 4751, 'MH', 181, 'RO', 44.22080000, 22.71200000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12725063'),
(92679, 'Comuna Prisăcani', 4735, 'IS', 181, 'RO', 47.07015000, 27.90795000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q5113190'),
(92680, 'Comuna Probota', 4735, 'IS', 181, 'RO', 47.38792000, 27.49446000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5069737'),
(92681, 'Comuna Produleşti', 4745, 'DB', 181, 'RO', 44.70336000, 25.50466000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q2335080'),
(92682, 'Comuna Proviţa de Jos', 4729, 'PH', 181, 'RO', 45.11190000, 25.67791000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q2605612'),
(92683, 'Comuna Proviţa de Sus', 4729, 'PH', 181, 'RO', 45.13604000, 25.63454000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q2605273'),
(92684, 'Comuna Prundeni', 4757, 'VL', 181, 'RO', 44.73460000, 24.24863000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5117634'),
(92685, 'Comuna Prundu', 4726, 'GR', 181, 'RO', 44.08334000, 26.21210000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q12145285'),
(92686, 'Comuna Prundu Bârgăului', 4733, 'BN', 181, 'RO', 47.22154000, 24.72450000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q5070404'),
(92687, 'Comuna Prunişor', 4751, 'MH', 181, 'RO', 44.60030000, 22.90476000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q16426962'),
(92688, 'Comuna Prăjeni', 4740, 'BT', 181, 'RO', 47.49577000, 27.02386000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q12725064'),
(92689, 'Comuna Prăjeşti', 4744, 'BC', 181, 'RO', 46.65469000, 26.97631000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q3600712'),
(92690, 'Comuna Pucheni', 4745, 'DB', 181, 'RO', 45.18211000, 25.26926000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q1971487'),
(92691, 'Comuna Puchenii Mari', 4729, 'PH', 181, 'RO', 44.82386000, 26.07697000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q2605296'),
(92692, 'Comuna Pufeşti', 4758, 'VN', 181, 'RO', 46.01111000, 27.20901000, '2019-10-05 23:15:03', '2020-05-01 17:23:12', 1, 'Q3915488'),
(92693, 'Comuna Pui', 4721, 'HD', 181, 'RO', 45.51086000, 23.08463000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q1022837'),
(92694, 'Comuna Puieşti', 4752, 'VS', 181, 'RO', 46.44541000, 27.46214000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q16427838'),
(92695, 'Comuna Puieşti', 4756, 'BZ', 181, 'RO', 45.39338000, 27.20850000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q653118'),
(92696, 'Comuna Pungeşti', 4752, 'VS', 181, 'RO', 46.70274000, 27.38157000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q16427842'),
(92697, 'Comuna Punghina', 4751, 'MH', 181, 'RO', 44.29534000, 22.96318000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5065667'),
(92698, 'Comuna Purani', 4728, 'TR', 181, 'RO', 44.36509000, 25.41322000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q16427660'),
(92699, 'Comuna Putineiu', 4728, 'TR', 181, 'RO', 43.91429000, 24.96969000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q16427663'),
(92700, 'Comuna Putineiu', 4726, 'GR', 181, 'RO', 43.90072000, 25.78225000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q5117126'),
(92701, 'Comuna Putna', 4720, 'SV', 181, 'RO', 47.88518000, 25.60374000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q16427550'),
(92702, 'Comuna Puşcaşi', 4752, 'VS', 181, 'RO', 46.62530000, 27.62563000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q12725065'),
(92703, 'Comuna Pânceşti', 4731, 'NT', 181, 'RO', 46.90170000, 27.16879000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q2388722'),
(92704, 'Comuna Pângăraţi', 4731, 'NT', 181, 'RO', 46.93333000, 26.15000000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q5113209'),
(92705, 'Comuna Pârgăreşti', 4744, 'BC', 181, 'RO', 46.24414000, 26.65017000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q1065609'),
(92706, 'Comuna Pârjol', 4744, 'BC', 181, 'RO', 46.59014000, 26.60903000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q1086348'),
(92707, 'Comuna Pârscov', 4756, 'BZ', 181, 'RO', 45.30760000, 26.55283000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q2716824'),
(92708, 'Comuna Pârâu', 4759, 'BV', 181, 'RO', 45.85139000, 25.21584000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q1070934'),
(92709, 'Comuna Pârşcoveni', 4738, 'OT', 181, 'RO', 44.29678000, 24.21836000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q16427139'),
(92710, 'Comuna Pãlatca', 4734, 'CJ', 181, 'RO', 46.87358000, 23.98288000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q12725070'),
(92711, 'Comuna Pãltiniş', 4753, 'CS', 181, 'RO', 45.40607000, 22.11233000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q12725071'),
(92712, 'Comuna Pãuleni-Ciuc', 4749, 'HR', 181, 'RO', 46.40073000, 25.84076000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q5057847'),
(92713, 'Comuna Pãuleşti', 4746, 'SM', 181, 'RO', 47.74992000, 22.94743000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q5055940'),
(92714, 'Comuna Păcureţi', 4729, 'PH', 181, 'RO', 45.14696000, 26.13997000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q607605'),
(92715, 'Comuna Pădureni', 4748, 'TM', 181, 'RO', 45.60039000, 21.21773000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q16427748'),
(92716, 'Comuna Pădureni', 4752, 'VS', 181, 'RO', 46.59285000, 28.09127000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q13571578'),
(92717, 'Comuna Păltiniş', 4740, 'BT', 181, 'RO', 48.23494000, 26.68555000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q16425861'),
(92718, 'Comuna Păltinoasa', 4720, 'SV', 181, 'RO', 47.54101000, 25.97138000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q16427540'),
(92719, 'Comuna Pănceşti', 4744, 'BC', 181, 'RO', 46.36459000, 27.11377000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q3410419'),
(92720, 'Comuna Pănătău', 4756, 'BZ', 181, 'RO', 45.30516000, 26.39159000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q2538625'),
(92721, 'Comuna Părteştii de Jos', 4720, 'SV', 181, 'RO', 47.62306000, 25.96689000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q12725066'),
(92722, 'Comuna Păstrăveni', 4731, 'NT', 181, 'RO', 47.15940000, 26.57615000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q5112248'),
(92723, 'Comuna Pătrăuţi', 4720, 'SV', 181, 'RO', 47.72097000, 26.18841000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q16427542'),
(92724, 'Comuna Pătulele', 4751, 'MH', 181, 'RO', 44.34984000, 22.80368000, '2019-10-05 23:15:03', '2020-05-01 17:23:10', 1, 'Q2465435'),
(92725, 'Comuna Păuca', 4755, 'SB', 181, 'RO', 46.00209000, 23.91696000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q5059399'),
(92726, 'Comuna Păuleşti', 4758, 'VN', 181, 'RO', 45.88712000, 26.68703000, '2019-10-05 23:15:03', '2020-05-01 17:23:12', 1, 'Q2300522'),
(92727, 'Comuna Păuleşti', 4729, 'PH', 181, 'RO', 45.00549000, 25.96991000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q2605173'),
(92728, 'Comuna Păuliş', 4739, 'AR', 181, 'RO', 46.12506000, 21.60246000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q5064140'),
(92729, 'Comuna Păuneşti', 4758, 'VN', 181, 'RO', 46.04066000, 27.10336000, '2019-10-05 23:15:03', '2020-05-01 17:23:12', 1, 'Q775438'),
(92730, 'Comuna Păuşeşti', 4757, 'VL', 181, 'RO', 45.07341000, 24.13552000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q3915644'),
(92731, 'Comuna Păuşeşti-Măglaşi', 4757, 'VL', 181, 'RO', 45.14165000, 24.24474000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q2298482'),
(92732, 'Comuna Raciu', 4745, 'DB', 181, 'RO', 44.81644000, 25.43460000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q2288716'),
(92733, 'Comuna Racova', 4744, 'BC', 181, 'RO', 46.69790000, 26.78097000, '2019-10-05 23:15:03', '2019-10-05 23:15:03', 1, 'Q2470418'),
(92734, 'Comuna Racoviţa', 4748, 'TM', 181, 'RO', 45.70441000, 21.63935000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q16427753'),
(92735, 'Comuna Racoviţa', 4755, 'SB', 181, 'RO', 45.66604000, 24.35142000, '2019-10-05 23:15:03', '2020-05-01 17:23:11', 1, 'Q12146916'),
(92736, 'Comuna Racoviţa', 4757, 'VL', 181, 'RO', 45.41200000, 24.31792000, '2019-10-05 23:15:03', '2020-05-01 17:23:12', 1, 'Q12725079'),
(92737, 'Comuna Racoviţa', 4736, 'BR', 181, 'RO', 45.30627000, 27.46720000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q3691956'),
(92738, 'Comuna Racoviţeni', 4756, 'BZ', 181, 'RO', 45.35025000, 26.89919000, '2019-10-05 23:15:03', '2020-05-01 17:23:09', 1, 'Q2716874'),
(92739, 'Comuna Racoş', 4759, 'BV', 181, 'RO', 46.02051000, 25.39563000, '2019-10-05 23:15:03', '2020-05-01 17:23:08', 1, 'Q1070980'),
(92740, 'Comuna Racu', 4749, 'HR', 181, 'RO', 46.45397000, 25.77098000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5058297'),
(92741, 'Comuna Racşa', 4746, 'SM', 181, 'RO', 47.82068000, 23.33247000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q12725076'),
(92742, 'Comuna Radomireşti', 4738, 'OT', 181, 'RO', 44.11049000, 24.67816000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q16427150'),
(92743, 'Comuna Radovan', 4742, 'DJ', 181, 'RO', 44.17687000, 23.58518000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725077'),
(92744, 'Comuna Radovanu', 4732, 'CL', 181, 'RO', 44.17671000, 26.53259000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q2716733'),
(92745, 'Comuna Rafaila', 4752, 'VS', 181, 'RO', 46.80064000, 27.36236000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q13542495'),
(92746, 'Comuna Ramna', 4753, 'CS', 181, 'RO', 45.46530000, 21.72257000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725081'),
(92747, 'Comuna Rapoltu Mare', 4721, 'HD', 181, 'RO', 45.87904000, 23.11543000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q1189281'),
(92748, 'Comuna Rasova', 4737, 'CT', 181, 'RO', 44.25918000, 27.96962000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5062358'),
(92749, 'Comuna Rast', 4742, 'DJ', 181, 'RO', 43.88713000, 23.28409000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16426506'),
(92750, 'Comuna Rebra', 4733, 'BN', 181, 'RO', 47.32588000, 24.49984000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16425785'),
(92751, 'Comuna Rebricea', 4752, 'VS', 181, 'RO', 46.87229000, 27.57250000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725082'),
(92752, 'Comuna Rebrişoara', 4733, 'BN', 181, 'RO', 47.31994000, 24.46568000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q1111464'),
(92753, 'Comuna Recea', 4759, 'BV', 181, 'RO', 45.74312000, 24.94086000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q3421827'),
(92754, 'Comuna Recea', 4760, 'MM', 181, 'RO', 47.63108000, 23.48610000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5055754'),
(92755, 'Comuna Recea', 4722, 'AG', 181, 'RO', 44.54264000, 25.02738000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725084'),
(92756, 'Comuna Recea Cristur', 4734, 'CJ', 181, 'RO', 47.12155000, 23.54554000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725083'),
(92757, 'Comuna Reci', 4754, 'CV', 181, 'RO', 45.81913000, 25.95552000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q732894'),
(92758, 'Comuna Redea', 4738, 'OT', 181, 'RO', 44.04157000, 24.30677000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16427153'),
(92759, 'Comuna Rediu', 4731, 'NT', 181, 'RO', 46.75000000, 26.56667000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q3878761'),
(92760, 'Comuna Rediu', 4747, 'GL', 181, 'RO', 45.72065000, 27.84647000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q3725426'),
(92761, 'Comuna Rediu-Tătar', 4735, 'IS', 181, 'RO', 47.23169000, 27.48747000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q5113548'),
(92762, 'Comuna Reghiu', 4758, 'VN', 181, 'RO', 45.79672000, 26.83648000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q2721766'),
(92763, 'Comuna Remetea', 4723, 'BH', 181, 'RO', 46.72956000, 22.35276000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16425699'),
(92764, 'Comuna Remetea', 4749, 'HR', 181, 'RO', 46.81908000, 25.42286000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5064072'),
(92765, 'Comuna Remetea Chioarului', 4760, 'MM', 181, 'RO', 47.52129000, 23.52891000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5055610'),
(92766, 'Comuna Remetea Mare', 4748, 'TM', 181, 'RO', 45.81036000, 21.39893000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5053654'),
(92767, 'Comuna Remeţi', 4760, 'MM', 181, 'RO', 47.99630000, 23.58741000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q5060861'),
(92768, 'Comuna Repedea', 4760, 'MM', 181, 'RO', 47.83212000, 24.40142000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5054670'),
(92769, 'Comuna Reviga', 4743, 'IL', 181, 'RO', 44.69251000, 27.10866000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q2718556'),
(92770, 'Comuna Ribiţa', 4721, 'HD', 181, 'RO', 46.20595000, 22.79638000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q1189096'),
(92771, 'Comuna Rieni', 4723, 'BH', 181, 'RO', 46.56878000, 22.44508000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16425702'),
(92772, 'Comuna Ripiceni', 4740, 'BT', 181, 'RO', 47.91899000, 27.13958000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5114150'),
(92773, 'Comuna Roata De Jos', 4726, 'GR', 181, 'RO', 44.41388000, 25.53061000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5117109'),
(92774, 'Comuna Robeasca', 4756, 'BZ', 181, 'RO', 45.14246000, 27.14697000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q2716401'),
(92775, 'Comuna Robăneşti', 4742, 'DJ', 181, 'RO', 44.30356000, 24.00582000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q584228'),
(92776, 'Comuna Rociu', 4722, 'AG', 181, 'RO', 44.66838000, 25.02014000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725087'),
(92777, 'Comuna Rodna', 4733, 'BN', 181, 'RO', 47.45140000, 24.82366000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12148261'),
(92778, 'Comuna Roeşti', 4757, 'VL', 181, 'RO', 44.92016000, 24.07793000, '2019-10-05 23:15:04', '2020-05-01 17:23:12', 1, 'Q3915110'),
(92779, 'Comuna Rogova', 4751, 'MH', 181, 'RO', 44.47076000, 22.82951000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16426966'),
(92780, 'Comuna Rojişte', 4742, 'DJ', 181, 'RO', 44.02515000, 23.91865000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q3725712'),
(92781, 'Comuna Roma', 4740, 'BT', 181, 'RO', 47.84497000, 26.57084000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725091'),
(92782, 'Comuna Romanu', 4736, 'BR', 181, 'RO', 45.31818000, 27.74046000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q2715936'),
(92783, 'Comuna Romos', 4721, 'HD', 181, 'RO', 45.82922000, 23.30902000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q1188184'),
(92784, 'Comuna Romuli', 4733, 'BN', 181, 'RO', 47.56247000, 24.42897000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5059064'),
(92785, 'Comuna Românaşi', 4741, 'SJ', 181, 'RO', 47.11470000, 23.17627000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q12702041'),
(92786, 'Comuna Româneşti', 4740, 'BT', 181, 'RO', 47.70860000, 27.23491000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12725093'),
(92787, 'Comuna Româneşti', 4735, 'IS', 181, 'RO', 47.27171000, 27.35819000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q5113303'),
(92788, 'Comuna Români', 4731, 'NT', 181, 'RO', 46.80242000, 26.69945000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q12725092'),
(92789, 'Comuna Rona de Jos', 4760, 'MM', 181, 'RO', 47.91667000, 24.01667000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5063925'),
(92790, 'Comuna Rona de Sus', 4760, 'MM', 181, 'RO', 47.90184000, 24.03877000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5063122'),
(92791, 'Comuna Roseţi', 4732, 'CL', 181, 'RO', 44.21435000, 27.45085000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q12725094'),
(92792, 'Comuna Rotunda', 4738, 'OT', 181, 'RO', 43.98427000, 24.31402000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725095'),
(92793, 'Comuna Rozavlea', 4760, 'MM', 181, 'RO', 47.73791000, 24.20116000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q5063854'),
(92794, 'Comuna Roşcani', 4735, 'IS', 181, 'RO', 47.44259000, 27.41778000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q5112967'),
(92795, 'Comuna Roşia', 4723, 'BH', 181, 'RO', 46.79896000, 22.40529000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12148998'),
(92796, 'Comuna Roşia', 4755, 'SB', 181, 'RO', 45.80385000, 24.31786000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q5057041'),
(92797, 'Comuna Roşia De Amaradia', 4750, 'GJ', 181, 'RO', 45.04419000, 23.75991000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q16426649'),
(92798, 'Comuna Roşia Montană', 4724, 'AB', 181, 'RO', 46.30991000, 23.08697000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12148992'),
(92799, 'Comuna Roşia de Secaş', 4724, 'AB', 181, 'RO', 46.04807000, 23.85329000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12148995'),
(92800, 'Comuna Roşieşti', 4752, 'VS', 181, 'RO', 46.45107000, 27.89202000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q2548866'),
(92801, 'Comuna Roşiile', 4757, 'VL', 181, 'RO', 44.88537000, 23.92438000, '2019-10-05 23:15:04', '2020-05-01 17:23:12', 1, 'Q3915394'),
(92802, 'Comuna Roşiori', 4743, 'IL', 181, 'RO', 44.61265000, 26.53496000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q16426815'),
(92803, 'Comuna Roşiori', 4723, 'BH', 181, 'RO', 47.25579000, 21.96166000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12148988'),
(92804, 'Comuna Roşiori', 4736, 'BR', 181, 'RO', 44.84097000, 27.34129000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q2780031'),
(92805, 'Comuna Roşiori', 4744, 'BC', 181, 'RO', 46.72620000, 27.10446000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q2542438'),
(92806, 'Comuna Rucăr', 4722, 'AG', 181, 'RO', 45.41607000, 25.17338000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q16425532'),
(92807, 'Comuna Rugineşti', 4758, 'VN', 181, 'RO', 46.07885000, 27.11803000, '2019-10-05 23:15:04', '2020-05-01 17:23:12', 1, 'Q2063385'),
(92808, 'Comuna Ruginoasa', 4735, 'IS', 181, 'RO', 47.25259000, 26.85589000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q1032848'),
(92809, 'Comuna Ruginoasa', 4731, 'NT', 181, 'RO', 46.97447000, 26.70674000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q3879874'),
(92810, 'Comuna Runcu', 4750, 'GJ', 181, 'RO', 45.12041000, 23.13974000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16426652'),
(92811, 'Comuna Runcu', 4745, 'DB', 181, 'RO', 45.17702000, 25.38533000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q2443642'),
(92812, 'Comuna Runcu', 4757, 'VL', 181, 'RO', 45.18685000, 24.45979000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q12725100'),
(92813, 'Comuna Runcu Salvei', 4733, 'BN', 181, 'RO', 47.34382000, 24.32529000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16425791'),
(92814, 'Comuna Rus', 4741, 'SJ', 181, 'RO', 47.27800000, 23.57813000, '2019-10-05 23:15:04', '2019-10-05 23:15:04', 1, 'Q16427381'),
(92815, 'Comuna Rusca Montană', 4753, 'CS', 181, 'RO', 45.60042000, 22.43638000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q12725101'),
(92816, 'Comuna Rusăneşti', 4738, 'OT', 181, 'RO', 43.94385000, 24.59498000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q3879592'),
(92817, 'Comuna Ruşcova', 4760, 'MM', 181, 'RO', 47.79289000, 24.28546000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q5054306'),
(92818, 'Comuna Ruşeţu', 4756, 'BZ', 181, 'RO', 44.94214000, 27.21863000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q2716081'),
(92819, 'Comuna Râca', 4722, 'AG', 181, 'RO', 44.44216000, 25.03671000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12725103'),
(92820, 'Comuna Râfov', 4729, 'PH', 181, 'RO', 44.85001000, 26.20573000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q2605244'),
(92821, 'Comuna Râmetea', 4724, 'AB', 181, 'RO', 46.43815000, 23.56579000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q16467066'),
(92822, 'Comuna Râmeţ', 4724, 'AB', 181, 'RO', 46.31564000, 23.52602000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q2285124'),
(92823, 'Comuna Râmnicelu', 4736, 'BR', 181, 'RO', 45.29786000, 27.51338000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q2468801'),
(92824, 'Comuna Râmnicelu', 4756, 'BZ', 181, 'RO', 45.37840000, 27.13293000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q2716637'),
(92825, 'Comuna Râu Alb', 4745, 'DB', 181, 'RO', 45.14850000, 25.33714000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q2538282'),
(92826, 'Comuna Râu Sadului', 4755, 'SB', 181, 'RO', 45.62401000, 24.06132000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q12725104'),
(92827, 'Comuna Râu de Mori', 4721, 'HD', 181, 'RO', 45.50717000, 22.86463000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q1021928'),
(92828, 'Comuna Râşca', 4720, 'SV', 181, 'RO', 47.36821000, 26.18987000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q16427557'),
(92829, 'Comuna Râșca', 4734, 'CJ', 181, 'RO', 46.73082000, 23.15413000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q10811999'),
(92830, 'Comuna Rãdoieşti', 4728, 'TR', 181, 'RO', 44.14377000, 25.15357000, '2019-10-05 23:15:04', '2020-05-01 17:23:11', 1, 'Q960982'),
(92831, 'Comuna Rãdãuţi-Prut', 4740, 'BT', 181, 'RO', 48.22492000, 26.81304000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12725110'),
(92832, 'Comuna Rãzboieni', 4731, 'NT', 181, 'RO', 47.07424000, 26.56150000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q3878768'),
(92833, 'Comuna Rãzvad', 4745, 'DB', 181, 'RO', 44.93588000, 25.52914000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q2606057'),
(92834, 'Comuna Răbăgani', 4723, 'BH', 181, 'RO', 46.75432000, 22.24302000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q16425695'),
(92835, 'Comuna Răchitoasa', 4744, 'BC', 181, 'RO', 46.44772000, 27.36875000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q1195151'),
(92836, 'Comuna Răchitova', 4721, 'HD', 181, 'RO', 45.60982000, 22.76739000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q1084732'),
(92837, 'Comuna Răchiţeni', 4735, 'IS', 181, 'RO', 47.05163000, 26.90504000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q5069898'),
(92838, 'Comuna Răchiţi', 4740, 'BT', 181, 'RO', 47.77282000, 26.68847000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12725107'),
(92839, 'Comuna Răcoasa', 4758, 'VN', 181, 'RO', 45.99786000, 26.88548000, '2019-10-05 23:15:04', '2020-05-01 17:23:12', 1, 'Q2722729'),
(92840, 'Comuna Răcăciuni', 4744, 'BC', 181, 'RO', 46.35011000, 26.96705000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q2469339'),
(92841, 'Comuna Răcăşdia', 4753, 'CS', 181, 'RO', 44.99767000, 21.58786000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q12725109'),
(92842, 'Comuna Rădeşti', 4724, 'AB', 181, 'RO', 46.24931000, 23.74444000, '2019-10-05 23:15:04', '2020-05-01 17:23:08', 1, 'Q12147304'),
(92843, 'Comuna Rădeşti', 4747, 'GL', 181, 'RO', 46.08312000, 27.79606000, '2019-10-05 23:15:04', '2020-05-01 17:23:09', 1, 'Q3725367'),
(92844, 'Comuna Răducăneni', 4735, 'IS', 181, 'RO', 46.95541000, 27.97344000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q5066901'),
(92845, 'Comuna Răduleşti', 4743, 'IL', 181, 'RO', 44.77609000, 26.33987000, '2019-10-05 23:15:04', '2020-05-01 17:23:10', 1, 'Q2718065'),
(92846, 'Comuna Rădăşeni', 4720, 'SV', 181, 'RO', 47.48804000, 26.24924000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q16427553'),
(92847, 'Comuna Răscăeţi', 4745, 'DB', 181, 'RO', 44.59760000, 25.25621000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q2539218'),
(92848, 'Comuna Răsmireşti', 4728, 'TR', 181, 'RO', 43.99981000, 25.57118000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q16427667'),
(92849, 'Comuna Răstoaca', 4758, 'VN', 181, 'RO', 45.66083000, 27.28723000, '2019-10-05 23:15:05', '2020-05-01 17:23:12', 1, 'Q16427965'),
(92850, 'Comuna Răsuceni', 4726, 'GR', 181, 'RO', 44.08416000, 25.68758000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q5117074'),
(92851, 'Comuna Răteşti', 4722, 'AG', 181, 'RO', 44.71690000, 25.15858000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q16425526'),
(92852, 'Comuna Răuceşti', 4731, 'NT', 181, 'RO', 47.26151000, 26.41170000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q5113656'),
(92853, 'Comuna Răuseni', 4740, 'BT', 181, 'RO', 47.55941000, 27.22551000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q5114720'),
(92854, 'Comuna Războeni-Cetate', 4733, 'BN', 181, 'RO', 47.11072000, 24.62878000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q859805'),
(92855, 'Comuna Răşinari', 4755, 'SB', 181, 'RO', 45.69761000, 24.07378000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q5059649'),
(92856, 'Comuna Sacoşu Turcesc', 4748, 'TM', 181, 'RO', 45.63526000, 21.39828000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q12725118'),
(92857, 'Comuna Sacu', 4753, 'CS', 181, 'RO', 45.57152000, 22.11048000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725116'),
(92858, 'Comuna Sadova', 4742, 'DJ', 181, 'RO', 43.88749000, 23.94622000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725113'),
(92859, 'Comuna Sadova', 4720, 'SV', 181, 'RO', 47.54248000, 25.50380000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725114'),
(92860, 'Comuna Sadu', 4755, 'SB', 181, 'RO', 45.67199000, 24.18393000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16427441'),
(92861, 'Comuna Saelele', 4728, 'TR', 181, 'RO', 43.85274000, 24.73838000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725115'),
(92862, 'Comuna Sagna', 4731, 'NT', 181, 'RO', 46.96806000, 27.01670000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q3878823'),
(92863, 'Comuna Salcia', 4729, 'PH', 181, 'RO', 45.18323000, 26.32600000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q938193'),
(92864, 'Comuna Salcia', 4728, 'TR', 181, 'RO', 43.94157000, 24.92663000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725117'),
(92865, 'Comuna Salcia', 4751, 'MH', 181, 'RO', 44.14124000, 22.93048000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q2246077'),
(92866, 'Comuna Salcia Tudor', 4736, 'BR', 181, 'RO', 45.40260000, 27.50380000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q2716354'),
(92867, 'Comuna Saligny', 4737, 'CT', 181, 'RO', 44.28318000, 28.08929000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5110221'),
(92868, 'Comuna Salva', 4733, 'BN', 181, 'RO', 47.30586000, 24.35557000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16425794'),
(92869, 'Comuna Samarineşti', 4750, 'GJ', 181, 'RO', 44.77152000, 23.04889000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q16426655'),
(92870, 'Comuna Sanislău', 4746, 'SM', 181, 'RO', 47.63116000, 22.33109000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q12150559'),
(92871, 'Comuna Santa Mare', 4740, 'BT', 181, 'RO', 47.63308000, 27.30260000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725120'),
(92872, 'Comuna Santău', 4746, 'SM', 181, 'RO', 47.51152000, 22.47327000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q5054603'),
(92873, 'Comuna Saraiu', 4737, 'CT', 181, 'RO', 44.71747000, 28.14732000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5052607'),
(92874, 'Comuna Saravale', 4748, 'TM', 181, 'RO', 46.06929000, 20.74085000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5062011'),
(92875, 'Comuna Sarichioi', 4727, 'TL', 181, 'RO', 44.92129000, 28.81870000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5070979'),
(92876, 'Comuna Sarmizegetusa', 4721, 'HD', 181, 'RO', 45.50996000, 22.76955000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q1069821'),
(92877, 'Comuna Sasca Montană', 4753, 'CS', 181, 'RO', 44.90394000, 21.70167000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q12725122'),
(92878, 'Comuna Sascut', 4744, 'BC', 181, 'RO', 46.19288000, 27.09163000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q1086357'),
(92879, 'Comuna Satchinez', 4748, 'TM', 181, 'RO', 45.93933000, 21.07597000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5062862'),
(92880, 'Comuna Satu Mare', 4749, 'HR', 181, 'RO', 46.34082000, 25.38378000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5056276'),
(92881, 'Comuna Satu Mare', 4720, 'SV', 181, 'RO', 47.82377000, 26.01339000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q1089034'),
(92882, 'Comuna Satulung', 4760, 'MM', 181, 'RO', 47.57189000, 23.41978000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12150780'),
(92883, 'Comuna Scheia', 4720, 'SV', 181, 'RO', 47.65030000, 26.18094000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16427566'),
(92884, 'Comuna Scheia', 4735, 'IS', 181, 'RO', 46.93950000, 27.49765000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16426820'),
(92885, 'Comuna Schela', 4747, 'GL', 181, 'RO', 45.51810000, 27.85229000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16426575'),
(92886, 'Comuna Schela', 4750, 'GJ', 181, 'RO', 45.15883000, 23.32395000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5117053'),
(92887, 'Comuna Schitu', 4738, 'OT', 181, 'RO', 44.35679000, 24.55926000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16427158'),
(92888, 'Comuna Schitu', 4726, 'GR', 181, 'RO', 44.14574000, 25.84240000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5116862'),
(92889, 'Comuna Schitu-Duca', 4735, 'IS', 181, 'RO', 47.00255000, 27.76703000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5113348'),
(92890, 'Comuna Schitu-Goleşti', 4722, 'AG', 181, 'RO', 45.18630000, 25.00576000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q2542469'),
(92891, 'Comuna Scoarţa', 4750, 'GJ', 181, 'RO', 45.02872000, 23.47131000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q12725125'),
(92892, 'Comuna Scobinţi', 4735, 'IS', 181, 'RO', 47.40529000, 26.89850000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q12153298'),
(92893, 'Comuna Scorţaru Nou', 4736, 'BR', 181, 'RO', 45.34905000, 27.61478000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q3696117'),
(92894, 'Comuna Scorţeni', 4744, 'BC', 181, 'RO', 46.58037000, 26.68543000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q2542455'),
(92895, 'Comuna Scorţeni', 4729, 'PH', 181, 'RO', 45.08876000, 25.84712000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q2300922'),
(92896, 'Comuna Scorţoasa', 4756, 'BZ', 181, 'RO', 45.36204000, 26.66176000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q2716411'),
(92897, 'Comuna Scrioaştea', 4728, 'TR', 181, 'RO', 44.16453000, 24.95299000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q16427674'),
(92898, 'Comuna Scundu', 4757, 'VL', 181, 'RO', 44.84880000, 24.17981000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725123'),
(92899, 'Comuna Scurtu Mare', 4728, 'TR', 181, 'RO', 44.35259000, 25.25895000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725128'),
(92900, 'Comuna Scutelnici', 4756, 'BZ', 181, 'RO', 44.83964000, 26.93747000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q2716878'),
(92901, 'Comuna Scânteia', 4735, 'IS', 181, 'RO', 46.92408000, 27.58855000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q5112696'),
(92902, 'Comuna Scânteia', 4743, 'IL', 181, 'RO', 44.73262000, 27.44334000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q2291828'),
(92903, 'Comuna Scânteieşti', 4747, 'GL', 181, 'RO', 45.68833000, 28.00432000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q2291899'),
(92904, 'Comuna Scăeşti', 4742, 'DJ', 181, 'RO', 44.46591000, 23.54822000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q12725132'),
(92905, 'Comuna Scărişoara', 4724, 'AB', 181, 'RO', 46.46072000, 22.88166000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q16425349'),
(92906, 'Comuna Scărişoara', 4738, 'OT', 181, 'RO', 43.98528000, 24.58333000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q3879575'),
(92907, 'Comuna Seaca', 4738, 'OT', 181, 'RO', 44.15159000, 24.76149000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725130'),
(92908, 'Comuna Seaca', 4728, 'TR', 181, 'RO', 43.74933000, 25.07423000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725129'),
(92909, 'Comuna Seaca de Câmp', 4742, 'DJ', 181, 'RO', 43.92824000, 23.19955000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q16426514'),
(92910, 'Comuna Seaca de Pădure', 4742, 'DJ', 181, 'RO', 44.37108000, 23.32359000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q945704'),
(92911, 'Comuna Secaş', 4748, 'TM', 181, 'RO', 45.90501000, 21.80832000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q12151712'),
(92912, 'Comuna Secu', 4742, 'DJ', 181, 'RO', 44.47854000, 23.29525000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16426517'),
(92913, 'Comuna Secuieni', 4744, 'BC', 181, 'RO', 46.65329000, 27.09674000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q2467674'),
(92914, 'Comuna Secuieni', 4731, 'NT', 181, 'RO', 46.86853000, 26.81062000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5112941'),
(92915, 'Comuna Secuieni', 4749, 'HR', 181, 'RO', 46.27623000, 24.96946000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16426709'),
(92916, 'Comuna Secusigiu', 4739, 'AR', 181, 'RO', 46.09260000, 21.00295000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5060148'),
(92917, 'Comuna Secăria', 4729, 'PH', 181, 'RO', 45.27498000, 25.68457000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q2605323'),
(92918, 'Comuna Segarcea Vale', 4728, 'TR', 181, 'RO', 43.82417000, 24.81422000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16427677'),
(92919, 'Comuna Seimeni', 4737, 'CT', 181, 'RO', 44.40184000, 28.08756000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5052979'),
(92920, 'Comuna Seleuş', 4739, 'AR', 181, 'RO', 46.37886000, 21.74875000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q5063279'),
(92921, 'Comuna Semlac', 4739, 'AR', 181, 'RO', 46.11260000, 20.93210000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5059753'),
(92922, 'Comuna Sfinţeşti', 4728, 'TR', 181, 'RO', 44.18824000, 25.10389000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q16427680'),
(92923, 'Comuna Sfântu Gheorghe', 4743, 'IL', 181, 'RO', 44.64735000, 26.84315000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q2291856'),
(92924, 'Comuna Sfântu Gheorghe', 4727, 'TL', 181, 'RO', 44.89654000, 29.59357000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q5054095'),
(92925, 'Comuna Sic', 4734, 'CJ', 181, 'RO', 46.93134000, 23.89615000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16426242'),
(92926, 'Comuna Sicheviţa', 4753, 'CS', 181, 'RO', 44.70616000, 21.82910000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q16426045'),
(92927, 'Comuna Siculeni', 4749, 'HR', 181, 'RO', 46.43537000, 25.75409000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5063726'),
(92928, 'Comuna Sihlea', 4758, 'VN', 181, 'RO', 45.49398000, 27.15629000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q2722236'),
(92929, 'Comuna Silivaşu De Câmpie', 4733, 'BN', 181, 'RO', 46.78127000, 24.29155000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q5065411'),
(92930, 'Comuna Siliştea', 4728, 'TR', 181, 'RO', 44.37398000, 25.35104000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q12725135'),
(92931, 'Comuna Siliştea', 4737, 'CT', 181, 'RO', 44.42359000, 28.20967000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q5053224'),
(92932, 'Comuna Siliştea', 4736, 'BR', 181, 'RO', 45.35263000, 27.82592000, '2019-10-05 23:15:05', '2020-05-01 17:23:08', 1, 'Q3691949'),
(92933, 'Comuna Siliştea Crucii', 4742, 'DJ', 181, 'RO', 44.04488000, 23.48194000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q2539161'),
(92934, 'Comuna Siliștea Gumești', 4728, 'TR', 181, 'RO', 44.38028000, 25.01389000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q12725139'),
(92935, 'Comuna Simian', 4751, 'MH', 181, 'RO', 44.62736000, 22.74227000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q16439834'),
(92936, 'Comuna Siminicea', 4720, 'SV', 181, 'RO', 47.70926000, 26.39132000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725140'),
(92937, 'Comuna Simoneşti', 4749, 'HR', 181, 'RO', 46.35466000, 25.11325000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q5090172'),
(92938, 'Comuna Sineşti', 4757, 'VL', 181, 'RO', 44.93231000, 23.83951000, '2019-10-05 23:15:05', '2020-05-01 17:23:12', 1, 'Q3915650'),
(92939, 'Comuna Sineşti', 4735, 'IS', 181, 'RO', 47.12117000, 27.18745000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q5113141'),
(92940, 'Comuna Singureni', 4726, 'GR', 181, 'RO', 44.22975000, 25.95363000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5118482'),
(92941, 'Comuna Sintea Mare', 4739, 'AR', 181, 'RO', 46.51684000, 21.60800000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5062733'),
(92942, 'Comuna Sinteşti', 4743, 'IL', 181, 'RO', 44.56260000, 26.41162000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q2291878'),
(92943, 'Comuna Sireţel', 4735, 'IS', 181, 'RO', 47.41840000, 26.73381000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q5113069'),
(92944, 'Comuna Siriu', 4756, 'BZ', 181, 'RO', 45.50872000, 26.24470000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q2716782'),
(92945, 'Comuna Siseşti', 4751, 'MH', 181, 'RO', 44.77146000, 22.83754000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q12725296'),
(92946, 'Comuna Sita Buzăului', 4754, 'CV', 181, 'RO', 45.61959000, 26.10621000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q5064539'),
(92947, 'Comuna Slatina', 4720, 'SV', 181, 'RO', 47.43630000, 25.97797000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12725138'),
(92948, 'Comuna Slatina-Timiş', 4753, 'CS', 181, 'RO', 45.26064000, 22.31807000, '2019-10-05 23:15:05', '2020-05-01 17:23:09', 1, 'Q12725137'),
(92949, 'Comuna Slava Cercheză', 4727, 'TL', 181, 'RO', 44.87578000, 28.58134000, '2019-10-05 23:15:05', '2020-05-01 17:23:11', 1, 'Q917604'),
(92950, 'Comuna Slimnic', 4755, 'SB', 181, 'RO', 45.94465000, 24.19777000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q12153852'),
(92951, 'Comuna Slivileşti', 4750, 'GJ', 181, 'RO', 44.79198000, 23.10761000, '2019-10-05 23:15:05', '2020-05-01 17:23:10', 1, 'Q12725141'),
(92952, 'Comuna Slobozia', 4726, 'GR', 181, 'RO', 43.85671000, 25.91074000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q5091784'),
(92953, 'Comuna Slobozia', 4743, 'IL', 181, 'RO', 44.57437000, 27.38755000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q215723'),
(92954, 'Comuna Slobozia', 4722, 'AG', 181, 'RO', 44.52197000, 25.24092000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q3407918'),
(92955, 'Comuna Slobozia Bradului', 4758, 'VN', 181, 'RO', 45.49203000, 27.03977000, '2019-10-05 23:15:05', '2019-10-05 23:15:05', 1, 'Q2722219'),
(92956, 'Comuna Slobozia Conachi', 4747, 'GL', 181, 'RO', 45.56430000, 27.77143000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q13572349'),
(92957, 'Comuna Slobozia Moara', 4745, 'DB', 181, 'RO', 44.59865000, 25.73126000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q16426386'),
(92958, 'Comuna Slobozia-Ciorăşti', 4758, 'VN', 181, 'RO', 45.60606000, 27.21399000, '2019-10-05 23:15:06', '2020-05-01 17:23:12', 1, 'Q3916029'),
(92959, 'Comuna Slobozia-Mândra', 4728, 'TR', 181, 'RO', 43.91576000, 24.69914000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q16427685'),
(92960, 'Comuna Slătioara', 4757, 'VL', 181, 'RO', 45.12396000, 23.88581000, '2019-10-05 23:15:06', '2020-05-01 17:23:12', 1, 'Q16427911'),
(92961, 'Comuna Slătioara', 4738, 'OT', 181, 'RO', 44.41545000, 24.32082000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q16427161'),
(92962, 'Comuna Smeeni', 4756, 'BZ', 181, 'RO', 44.98816000, 26.91261000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q10372415'),
(92963, 'Comuna Smulţi', 4747, 'GL', 181, 'RO', 45.93246000, 27.75488000, '2019-10-05 23:15:06', '2020-05-01 17:23:09', 1, 'Q13542168'),
(92964, 'Comuna Smârdan', 4747, 'GL', 181, 'RO', 45.49694000, 27.93554000, '2019-10-05 23:15:06', '2020-05-01 17:23:09', 1, 'Q3725414'),
(92965, 'Comuna Smârdan', 4727, 'TL', 181, 'RO', 45.28462000, 28.00599000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q5070654'),
(92966, 'Comuna Smârdioasa', 4728, 'TR', 181, 'RO', 43.83939000, 25.44190000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q16427688'),
(92967, 'Comuna Snagov', 4725, 'IF', 181, 'RO', 44.68481000, 26.12384000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q1682269'),
(92968, 'Comuna Socodor', 4739, 'AR', 181, 'RO', 46.51010000, 21.44128000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5062134'),
(92969, 'Comuna Socol', 4753, 'CS', 181, 'RO', 44.85334000, 21.41782000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12756677'),
(92970, 'Comuna Socond', 4746, 'SM', 181, 'RO', 47.54018000, 22.98135000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5054581'),
(92971, 'Comuna Sohatu', 4732, 'CL', 181, 'RO', 44.35549000, 26.52161000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q2621237'),
(92972, 'Comuna Sohodol', 4724, 'AB', 181, 'RO', 46.32748000, 22.99414000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q16425359'),
(92973, 'Comuna Soleşti', 4752, 'VS', 181, 'RO', 46.77174000, 27.80613000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q16427845'),
(92974, 'Comuna Solonţ', 4744, 'BC', 181, 'RO', 46.56929000, 26.53863000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q1215088'),
(92975, 'Comuna Someş-Odorhei', 4741, 'SJ', 181, 'RO', 47.32846000, 23.22294000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q3885303'),
(92976, 'Comuna Somova', 4727, 'TL', 181, 'RO', 45.18861000, 28.65673000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5071003'),
(92977, 'Comuna Sopot', 4742, 'DJ', 181, 'RO', 44.39061000, 23.51252000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q16426522'),
(92978, 'Comuna Sovarna', 4751, 'MH', 181, 'RO', 44.84837000, 22.81235000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q16426977'),
(92979, 'Comuna Soveja', 4758, 'VN', 181, 'RO', 45.99705000, 26.65351000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q2722151'),
(92980, 'Comuna Spanţov', 4732, 'CL', 181, 'RO', 44.13110000, 26.78896000, '2019-10-05 23:15:06', '2020-05-01 17:23:09', 1, 'Q2717108'),
(92981, 'Comuna Spermezeu', 4733, 'BN', 181, 'RO', 47.30000000, 24.15000000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5065736'),
(92982, 'Comuna Spineni', 4738, 'OT', 181, 'RO', 44.71808000, 24.55897000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725146'),
(92983, 'Comuna Spinuş', 4723, 'BH', 181, 'RO', 47.20209000, 22.20217000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q12157049'),
(92984, 'Comuna Sprâncenata', 4738, 'OT', 181, 'RO', 44.06110000, 24.63984000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q12725145'),
(92985, 'Comuna Spulber', 4758, 'VN', 181, 'RO', 45.74654000, 26.73925000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725149'),
(92986, 'Comuna Starchiojd', 4729, 'PH', 181, 'RO', 45.32851000, 26.17417000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q2720381'),
(92987, 'Comuna Stejari', 4750, 'GJ', 181, 'RO', 44.77359000, 23.70567000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725148'),
(92988, 'Comuna Stejaru', 4727, 'TL', 181, 'RO', 44.78528000, 28.53996000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5070958'),
(92989, 'Comuna Stejaru', 4728, 'TR', 181, 'RO', 44.17715000, 24.88208000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725147'),
(92990, 'Comuna Stelnica', 4743, 'IL', 181, 'RO', 44.42585000, 27.88823000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q2718388'),
(92991, 'Comuna Stoeneşti', 4738, 'OT', 181, 'RO', 44.11675000, 24.49720000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q12725152'),
(92992, 'Comuna Stoeneşti', 4722, 'AG', 181, 'RO', 45.26432000, 25.17674000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q16425539'),
(92993, 'Comuna Stoeneşti', 4757, 'VL', 181, 'RO', 45.14715000, 24.16337000, '2019-10-05 23:15:06', '2020-05-01 17:23:12', 1, 'Q3915596'),
(92994, 'Comuna Stoeneşti', 4726, 'GR', 181, 'RO', 44.14164000, 25.89349000, '2019-10-05 23:15:06', '2020-05-01 17:23:09', 1, 'Q3791199'),
(92995, 'Comuna Stoicăneşti', 4738, 'OT', 181, 'RO', 44.18568000, 24.64267000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q2301049'),
(92996, 'Comuna Stoileşti', 4757, 'VL', 181, 'RO', 44.91463000, 24.35992000, '2019-10-05 23:15:06', '2020-05-01 17:23:12', 1, 'Q3915143'),
(92997, 'Comuna Stoina', 4750, 'GJ', 181, 'RO', 44.67998000, 23.64563000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725157'),
(92998, 'Comuna Stolniceni-Prăjescu', 4735, 'IS', 181, 'RO', 47.18563000, 26.73525000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q5113116'),
(92999, 'Comuna Stolnici', 4722, 'AG', 181, 'RO', 44.58075000, 24.77335000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725158'),
(93000, 'Comuna Storobăneasa', 4728, 'TR', 181, 'RO', 43.87974000, 25.45622000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q16427694'),
(93001, 'Comuna Straja', 4720, 'SV', 181, 'RO', 47.91825000, 25.55275000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725155'),
(93002, 'Comuna Strejeşti', 4738, 'OT', 181, 'RO', 44.52733000, 24.27036000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q2300852'),
(93003, 'Comuna Stremţ', 4724, 'AB', 181, 'RO', 46.25606000, 23.59340000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q16425363'),
(93004, 'Comuna Stroeşti', 4757, 'VL', 181, 'RO', 45.07519000, 23.92645000, '2019-10-05 23:15:06', '2020-05-01 17:23:12', 1, 'Q2219766'),
(93005, 'Comuna Stroieşti', 4720, 'SV', 181, 'RO', 47.61943000, 26.12741000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q15977380'),
(93006, 'Comuna Strugari', 4744, 'BC', 181, 'RO', 46.52715000, 26.73578000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q2542360'),
(93007, 'Comuna Strunga', 4735, 'IS', 181, 'RO', 47.16021000, 26.94400000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5066983'),
(93008, 'Comuna Strâmtura', 4760, 'MM', 181, 'RO', 47.75156000, 24.10267000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q5064327'),
(93009, 'Comuna Străoane', 4758, 'VN', 181, 'RO', 45.92895000, 27.04359000, '2019-10-05 23:15:06', '2020-05-01 17:23:12', 1, 'Q2722289'),
(93010, 'Comuna Studina', 4738, 'OT', 181, 'RO', 43.96784000, 24.42136000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725162'),
(93011, 'Comuna Stulpicani', 4720, 'SV', 181, 'RO', 47.43327000, 25.75705000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725165'),
(93012, 'Comuna Stâlpeni', 4722, 'AG', 181, 'RO', 45.05665000, 24.99307000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q2486575'),
(93013, 'Comuna Stâlpu', 4756, 'BZ', 181, 'RO', 45.08519000, 26.70839000, '2019-10-05 23:15:06', '2020-05-01 17:23:09', 1, 'Q16425977'),
(93014, 'Comuna Stângăceaua', 4751, 'MH', 181, 'RO', 44.60024000, 23.29263000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q1956235'),
(93015, 'Comuna Stăncuţa', 4736, 'BR', 181, 'RO', 44.88885000, 27.79614000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q3696105'),
(93016, 'Comuna Stăneşti', 4726, 'GR', 181, 'RO', 43.93344000, 25.87967000, '2019-10-05 23:15:06', '2020-05-01 17:23:09', 1, 'Q5070143'),
(93017, 'Comuna Stăneşti', 4750, 'GJ', 181, 'RO', 45.13050000, 23.25328000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q12725163'),
(93018, 'Comuna Stăneşti', 4757, 'VL', 181, 'RO', 44.81359000, 24.04264000, '2019-10-05 23:15:06', '2020-05-01 17:23:12', 1, 'Q3915317'),
(93019, 'Comuna Stănileşti', 4752, 'VS', 181, 'RO', 46.65030000, 28.16151000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q12725166'),
(93020, 'Comuna Stănişeşti', 4744, 'BC', 181, 'RO', 46.42760000, 27.31055000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q2469306'),
(93021, 'Comuna Stăniţa', 4731, 'NT', 181, 'RO', 47.01216000, 27.13704000, '2019-10-05 23:15:06', '2020-05-01 17:23:10', 1, 'Q5112992'),
(93022, 'Comuna Stăuceni', 4740, 'BT', 181, 'RO', 47.72142000, 26.77471000, '2019-10-05 23:15:06', '2020-05-01 17:23:08', 1, 'Q5114679'),
(93023, 'Comuna Suatu', 4734, 'CJ', 181, 'RO', 46.75372000, 23.95820000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725169'),
(93024, 'Comuna Subcetate', 4749, 'HR', 181, 'RO', 46.85645000, 25.40652000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5058207'),
(93025, 'Comuna Suceveni', 4747, 'GL', 181, 'RO', 45.99495000, 28.05748000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q12725168'),
(93026, 'Comuna Suceviţa', 4720, 'SV', 181, 'RO', 47.79051000, 25.74000000, '2019-10-05 23:15:06', '2020-05-01 17:23:11', 1, 'Q16427563'),
(93027, 'Comuna Suciu de Sus', 4760, 'MM', 181, 'RO', 47.44309000, 24.04303000, '2019-10-05 23:15:06', '2019-10-05 23:15:06', 1, 'Q5054626');

