INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(90025, 'Bacău', 4744, 'BC', 181, 'RO', 46.56718000, 26.91384000, '2019-10-05 23:14:39', '2020-05-01 17:23:08', 1, 'Q16898105'),
(90026, 'Bahna', 4731, 'NT', 181, 'RO', 46.78333000, 26.78333000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q15957595'),
(90027, 'Bai<PERSON>', 4727, 'TL', 181, 'RO', 44.71667000, 28.66667000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q788628'),
(90028, '<PERSON><PERSON>', 4720, '<PERSON>', 181, '<PERSON><PERSON>', 47.41667000, 26.21667000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q12081399'),
(90029, 'Baia Mare', 4760, 'MM', 181, 'RO', 47.65729000, 23.56808000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q16898378'),
(90030, 'Baia Sprie', 4760, 'MM', 181, 'RO', 47.66189000, 23.69215000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q16898384'),
(90031, 'Baia de Aramă', 4751, 'MH', 181, 'RO', 44.99929000, 22.80784000, '2019-10-05 23:14:39', '2020-05-01 17:23:10', 1, 'Q16898407'),
(90032, 'Baia de Arieş', 4724, 'AB', 181, 'RO', 46.38045000, 23.28115000, '2019-10-05 23:14:39', '2020-05-01 17:23:07', 1, 'Q16898073'),
(90033, 'Baia de Criş', 4721, 'HD', 181, 'RO', 46.16667000, 22.71667000, '2019-10-05 23:14:39', '2020-05-01 17:23:10', 1, 'Q15296381'),
(90034, 'Baia de Fier', 4750, 'GJ', 181, 'RO', 45.16667000, 23.76667000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q2717973'),
(90035, 'Bajura', 4740, 'BT', 181, 'RO', 48.20119000, 26.53987000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q12080260'),
(90036, 'Bala', 4751, 'MH', 181, 'RO', 44.88389000, 22.83333000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q932323'),
(90037, 'Balaci', 4728, 'TR', 181, 'RO', 44.35000000, 24.91667000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q784679'),
(90038, 'Balaciu', 4743, 'IL', 181, 'RO', 44.63333000, 26.88333000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q13569904'),
(90039, 'Balc', 4723, 'BH', 181, 'RO', 47.28333000, 22.53333000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q1096671'),
(90040, 'Balcani', 4744, 'BC', 181, 'RO', 46.63333000, 26.55000000, '2019-10-05 23:14:39', '2019-10-05 23:14:39', 1, 'Q15627758'),
(90041, 'Baldovineşti', 4738, 'OT', 181, 'RO', 44.38333000, 24.05000000, '2019-10-05 23:14:39', '2020-05-01 17:23:10', 1, 'Q2719003'),
(90042, 'Baldovinești', 4736, 'BR', 181, 'RO', 45.32182000, 27.91107000, '2019-10-05 23:14:39', '2020-05-01 17:23:08', 1, 'Q12080611'),
(90043, 'Balintești', 4747, 'GL', 181, 'RO', 46.02937000, 27.92201000, '2019-10-05 23:14:39', '2020-05-01 17:23:09', 1, 'Q12080749'),
(90044, 'Balinţ', 4748, 'TM', 181, 'RO', 45.81250000, 21.85361000, '2019-10-05 23:14:39', '2020-05-01 17:23:11', 1, 'Q2603576'),
(90045, 'Baloteşti', 4725, 'IF', 181, 'RO', 44.61667000, 26.11667000, '2019-10-05 23:14:39', '2020-05-01 17:23:10', 1, 'Q12080685'),
(90046, 'Balta', 4751, 'MH', 181, 'RO', 44.88694000, 22.63694000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q6275173'),
(90047, 'Balta Albă', 4756, 'BZ', 181, 'RO', 45.30423000, 27.28557000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q12080689'),
(90048, 'Balta Doamnei', 4729, 'PH', 181, 'RO', 44.75000000, 26.16667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q789744'),
(90049, 'Balta Verde', 4751, 'MH', 181, 'RO', 44.34411000, 22.59878000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12080693'),
(90050, 'Balş', 4738, 'OT', 181, 'RO', 44.35000000, 24.10000000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q12080693'),
(90051, 'Balşa', 4721, 'HD', 181, 'RO', 46.03333000, 23.11667000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q789736'),
(90052, 'Balș', 4735, 'IS', 181, 'RO', 47.29628000, 26.97856000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q2717370'),
(90053, 'Banca', 4752, 'VS', 181, 'RO', 46.30000000, 27.80000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12080828'),
(90054, 'Bancu', 4749, 'HR', 181, 'RO', 46.30291000, 25.94422000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q713722'),
(90055, 'Banloc', 4748, 'TM', 181, 'RO', 45.38742000, 21.13581000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14758001'),
(90056, 'Bara', 4748, 'TM', 181, 'RO', 45.89472000, 21.87194000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14770193'),
(90057, 'Baranca', 4740, 'BT', 181, 'RO', 48.19651000, 26.47893000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12080933'),
(90058, 'Baraolt', 4754, 'CV', 181, 'RO', 46.07514000, 25.60029000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q16898226'),
(90059, 'Barați', 4744, 'BC', 181, 'RO', 46.57569000, 26.87118000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q12080973'),
(90060, 'Barcani', 4754, 'CV', 181, 'RO', 45.70000000, 26.08333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1096779'),
(90061, 'Barcea', 4747, 'GL', 181, 'RO', 45.75000000, 27.46667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2717097'),
(90062, 'Barticești', 4731, 'NT', 181, 'RO', 47.06324000, 26.79254000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q12081105'),
(90063, 'Baru', 4721, 'HD', 181, 'RO', 45.47218000, 23.16271000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q809628'),
(90064, 'Baru Mic', 4721, 'HD', 181, 'RO', 45.46667000, 23.15000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q31911826'),
(90065, 'Barza', 4738, 'OT', 181, 'RO', 44.33333000, 24.78333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12081025'),
(90066, 'Barza', 4721, 'HD', 181, 'RO', 46.11990000, 22.85695000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q6487774'),
(90067, 'Basarabi', 4742, 'DJ', 181, 'RO', 44.00561000, 23.00915000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12081131'),
(90068, 'Basarabi', 4720, 'SV', 181, 'RO', 47.45370000, 26.43972000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12081132'),
(90069, 'Bata', 4739, 'AR', 181, 'RO', 46.01667000, 22.03333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q791370'),
(90070, 'Batârăşti', 4757, 'VL', 181, 'RO', 44.81667000, 24.15000000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q791370'),
(90071, 'Batăr', 4723, 'BH', 181, 'RO', 46.70000000, 21.81667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q15739450'),
(90072, 'Bazna', 4755, 'SB', 181, 'RO', 46.20000000, 24.28333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q812683'),
(90073, 'Başcov', 4722, 'AG', 181, 'RO', 44.90000000, 24.81667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q2484442'),
(90074, 'Beba Veche', 4748, 'TM', 181, 'RO', 46.13333000, 20.31667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14771126'),
(90075, 'Beceni', 4756, 'BZ', 181, 'RO', 45.38333000, 26.78333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q3292959'),
(90076, 'Bechet', 4742, 'DJ', 181, 'RO', 43.78188000, 23.95706000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q3292959'),
(90077, 'Becicherecu Mic', 4748, 'TM', 181, 'RO', 45.82917000, 21.05139000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14772465'),
(90078, 'Beciu', 4728, 'TR', 181, 'RO', 44.00429000, 24.66571000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2549966'),
(90079, 'Beclean', 4759, 'BV', 181, 'RO', 45.83119000, 24.92180000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q15233652'),
(90080, 'Beclean', 4733, 'BN', 181, 'RO', 47.18333000, 24.18333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q15233652'),
(90081, 'Beidaud', 4727, 'TL', 181, 'RO', 44.71667000, 28.56667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q792067'),
(90082, 'Beiu', 4728, 'TR', 181, 'RO', 43.87352000, 25.45548000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12082511'),
(90083, 'Beiuş', 4723, 'BH', 181, 'RO', 46.66667000, 22.35000000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q12082511'),
(90084, 'Belceşti', 4735, 'IS', 181, 'RO', 47.30000000, 27.08333000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q792228'),
(90085, 'Belciugatele', 4732, 'CL', 181, 'RO', 44.48333000, 26.43333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12081826'),
(90086, 'Beleţi', 4722, 'AG', 181, 'RO', 44.90000000, 25.08333000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q12081746'),
(90087, 'Belin', 4754, 'CV', 181, 'RO', 45.93333000, 25.56667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1071428'),
(90088, 'Belin-Vale', 4754, 'CV', 181, 'RO', 45.93237000, 25.60878000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q278749'),
(90089, 'Belinţ', 4748, 'TM', 181, 'RO', 45.75500000, 21.76028000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q14771252'),
(90090, 'Beliu', 4739, 'AR', 181, 'RO', 46.48333000, 21.98333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q854214'),
(90091, 'Beliş', 4734, 'CJ', 181, 'RO', 46.65000000, 23.03333000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q428723'),
(90092, 'Beltiug', 4746, 'SM', 181, 'RO', 47.55000000, 22.85000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1059058'),
(90093, 'Benesat', 4741, 'SJ', 181, 'RO', 47.41049000, 23.28904000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1189435'),
(90094, 'Bengești', 4750, 'GJ', 181, 'RO', 45.06667000, 23.60000000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q12081875'),
(90095, 'Berbeşti', 4757, 'VL', 181, 'RO', 44.98333000, 23.88333000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q12081875'),
(90096, 'Berbești', 4760, 'MM', 181, 'RO', 47.84865000, 23.93448000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q781602'),
(90097, 'Berca', 4756, 'BZ', 181, 'RO', 45.28333000, 26.68333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12082297'),
(90098, 'Berceni', 4729, 'PH', 181, 'RO', 44.93333000, 26.11667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q13962650'),
(90099, 'Berceni', 4725, 'IF', 181, 'RO', 44.31417000, 26.18556000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2090150'),
(90100, 'Berchișești', 4720, 'SV', 181, 'RO', 47.52306000, 26.03207000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q2720652'),
(90101, 'Beregsău Mare', 4748, 'TM', 181, 'RO', 45.75921000, 21.02666000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q793329'),
(90102, 'Berevoeşti', 4722, 'AG', 181, 'RO', 45.23333000, 24.91667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q1896955'),
(90103, 'Berezeni', 4752, 'VS', 181, 'RO', 46.37611000, 28.14778000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2547229'),
(90104, 'Bereşti-Bistriţa', 4744, 'BC', 181, 'RO', 46.71667000, 26.83333000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q15627787'),
(90105, 'Bereşti-Sat', 4747, 'GL', 181, 'RO', 46.10000000, 27.88333000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q31914415'),
(90106, 'Bereşti-Tazlău', 4744, 'BC', 181, 'RO', 46.46667000, 26.66667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q15629002'),
(90107, 'Berești', 4747, 'GL', 181, 'RO', 46.10000000, 27.88333000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q15629002'),
(90108, 'Berești', 4744, 'BC', 181, 'RO', 46.20329000, 27.14476000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q12082270'),
(90109, 'Berghin', 4724, 'AB', 181, 'RO', 46.07782000, 23.73742000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1092886'),
(90110, 'Berislăveşti', 4757, 'VL', 181, 'RO', 45.25000000, 24.41667000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q526754'),
(90111, 'Beriu', 4721, 'HD', 181, 'RO', 45.78333000, 23.18333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q5054562'),
(90112, 'Berleşti', 4750, 'GJ', 181, 'RO', 44.91667000, 23.66667000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q2717753'),
(90113, 'Berlişte', 4753, 'CS', 181, 'RO', 44.98667000, 21.46306000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q75534'),
(90114, 'Bertea', 4729, 'PH', 181, 'RO', 45.23333000, 25.86667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12082375'),
(90115, 'Berteştii de Jos', 4736, 'BR', 181, 'RO', 44.83333000, 27.75000000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q5112125'),
(90116, 'Berveni', 4746, 'SM', 181, 'RO', 47.75000000, 22.46667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q855201'),
(90117, 'Berzasca', 4753, 'CS', 181, 'RO', 44.64712000, 21.95428000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q793732'),
(90118, 'Berzovia', 4753, 'CS', 181, 'RO', 45.42611000, 21.62806000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1195116'),
(90119, 'Berzunţi', 4744, 'BC', 181, 'RO', 46.40000000, 26.63333000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q15629066'),
(90120, 'Bethausen', 4748, 'TM', 181, 'RO', 45.83306000, 21.95278000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14769077'),
(90121, 'Beuca', 4728, 'TR', 181, 'RO', 44.25200000, 24.96637000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2549913'),
(90122, 'Bezdead', 4745, 'DB', 181, 'RO', 45.15000000, 25.51667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12081529'),
(90123, 'Beznea', 4723, 'BH', 181, 'RO', 46.95967000, 22.61954000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q853558'),
(90124, 'Beștepe', 4727, 'TL', 181, 'RO', 45.09241000, 29.01478000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q794476'),
(90125, 'Bicaz', 4760, 'MM', 181, 'RO', 47.46667000, 23.03333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q794938'),
(90126, 'Bicaz', 4731, 'NT', 181, 'RO', 46.91667000, 26.06667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q794938'),
(90127, 'Bicaz-Chei', 4731, 'NT', 181, 'RO', 46.81667000, 25.88333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q15960058'),
(90128, 'Bicazu Ardelean', 4731, 'NT', 181, 'RO', 46.85000000, 25.93333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q15961717'),
(90129, 'Bichigiu', 4733, 'BN', 181, 'RO', 47.42522000, 24.33893000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q852122'),
(90130, 'Biertan', 4755, 'SB', 181, 'RO', 46.13333000, 24.51667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q793388'),
(90131, 'Biharia', 4723, 'BH', 181, 'RO', 47.15000000, 21.91667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q15742818'),
(90132, 'Biia', 4724, 'AB', 181, 'RO', 46.23097000, 24.00461000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q823156'),
(90133, 'Bijghir', 4744, 'BC', 181, 'RO', 46.60185000, 27.01859000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12086447'),
(90134, 'Bilbor', 4749, 'HR', 181, 'RO', 47.05000000, 25.51667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q134153'),
(90135, 'Bilca', 4720, 'SV', 181, 'RO', 47.91667000, 25.75000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2606095'),
(90136, 'Bilciureşti', 4745, 'DB', 181, 'RO', 44.73333000, 25.80000000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q12086846'),
(90137, 'Biled', 4748, 'TM', 181, 'RO', 45.88639000, 20.95889000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14769169'),
(90138, 'Biliești', 4758, 'VN', 181, 'RO', 45.72106000, 27.34879000, '2019-10-05 23:14:40', '2020-05-01 17:23:12', 1, 'Q653883'),
(90139, 'Birchiş', 4739, 'AR', 181, 'RO', 45.96667000, 22.16667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q1066879'),
(90140, 'Bircii', 4738, 'OT', 181, 'RO', 44.51673000, 24.60324000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12086998'),
(90141, 'Birda', 4748, 'TM', 181, 'RO', 45.42996000, 21.34359000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14771631'),
(90142, 'Bisoca', 4756, 'BZ', 181, 'RO', 45.53333000, 26.70000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12087020'),
(90143, 'Bistra', 4724, 'AB', 181, 'RO', 46.37820000, 23.10081000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q804075'),
(90144, 'Bistra', 4760, 'MM', 181, 'RO', 47.86667000, 24.20000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q866731'),
(90145, 'Bistra', 4723, 'BH', 181, 'RO', 47.25478000, 22.42295000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q748457'),
(90146, 'Bistreț', 4742, 'DJ', 181, 'RO', 43.90000000, 23.50000000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q1927985'),
(90147, 'Bistriţa', 4733, 'BN', 181, 'RO', 47.13316000, 24.50011000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q1927985'),
(90148, 'Bistriţa Bârgăului', 4733, 'BN', 181, 'RO', 47.21667000, 24.76667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q834887'),
(90149, 'Bistrița', 4751, 'MH', 181, 'RO', 44.58511000, 22.78762000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q12087028'),
(90150, 'Bistrița', 4757, 'VL', 181, 'RO', 45.18612000, 24.04010000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q12087029'),
(90151, 'Bistrița', 4731, 'NT', 181, 'RO', 46.94856000, 26.29598000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q12087030'),
(90152, 'Bivolari', 4735, 'IS', 181, 'RO', 47.53333000, 27.43333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2467888'),
(90153, 'Bivolărie', 4720, 'SV', 181, 'RO', 47.91464000, 25.65525000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q12086403'),
(90154, 'Bixad', 4746, 'SM', 181, 'RO', 47.93333000, 23.40000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q794931'),
(90155, 'Bixad', 4754, 'CV', 181, 'RO', 46.10000000, 25.86667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q583332'),
(90156, 'Blaj', 4724, 'AB', 181, 'RO', 46.17508000, 23.91578000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q583332'),
(90157, 'Blejeşti', 4728, 'TR', 181, 'RO', 44.30000000, 25.46667000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q2720621'),
(90158, 'Blejoi', 4729, 'PH', 181, 'RO', 45.00000000, 26.01667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q804450'),
(90159, 'Blideşti', 4724, 'AB', 181, 'RO', 46.30000000, 23.13333000, '2019-10-05 23:14:40', '2020-05-01 17:23:07', 1, 'Q12082997'),
(90160, 'Blândeşti', 4740, 'BT', 181, 'RO', 47.70000000, 26.86667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q2534471'),
(90161, 'Blândiana', 4724, 'AB', 181, 'RO', 45.97943000, 23.38543000, '2019-10-05 23:14:40', '2020-05-01 17:23:07', 1, 'Q1027845'),
(90162, 'Blânzi', 4747, 'GL', 181, 'RO', 45.93575000, 27.65063000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q12082924'),
(90163, 'Blăgeşti', 4752, 'VS', 181, 'RO', 46.13722000, 28.01306000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q2297459'),
(90164, 'Blăgeşti', 4744, 'BC', 181, 'RO', 46.68333000, 26.65000000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q15630115'),
(90165, 'Blăjani', 4756, 'BZ', 181, 'RO', 45.31667000, 26.81667000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q3292587'),
(90166, 'Blăjel', 4755, 'SB', 181, 'RO', 46.21667000, 24.31667000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q789927'),
(90167, 'Blăjeni', 4721, 'HD', 181, 'RO', 46.23333000, 22.90000000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q5065634'),
(90168, 'Bobiceşti', 4738, 'OT', 181, 'RO', 44.38333000, 24.15000000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q2719195'),
(90169, 'Boboc', 4756, 'BZ', 181, 'RO', 45.19570000, 26.98136000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12083049'),
(90170, 'Boboiești', 4731, 'NT', 181, 'RO', 47.25370000, 26.03749000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q12083060'),
(90171, 'Bobolia', 4729, 'PH', 181, 'RO', 45.09395000, 25.74259000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12083052'),
(90172, 'Bobota', 4741, 'SJ', 181, 'RO', 47.38333000, 22.76667000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1081295'),
(90173, 'Bobulești', 4740, 'BT', 181, 'RO', 47.75135000, 27.22697000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q12083126'),
(90174, 'Bobâlna', 4734, 'CJ', 181, 'RO', 47.14385000, 23.64461000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q498347'),
(90175, 'Bocicoiu Mare', 4760, 'MM', 181, 'RO', 47.96667000, 24.00000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1093246'),
(90176, 'Bocsig', 4739, 'AR', 181, 'RO', 46.41667000, 21.95000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2296567'),
(90177, 'Bocşa', 4741, 'SJ', 181, 'RO', 47.29390000, 22.91405000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q1078043'),
(90178, 'Bocşa', 4753, 'CS', 181, 'RO', 45.37333000, 21.70917000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q1078043'),
(90179, 'Bod', 4759, 'BV', 181, 'RO', 45.76667000, 25.65000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12083434'),
(90180, 'Bodeşti', 4731, 'NT', 181, 'RO', 47.03333000, 26.41667000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q15963805'),
(90181, 'Bodești', 4757, 'VL', 181, 'RO', 45.14167000, 24.11549000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q12083459'),
(90182, 'Bodeștii de Jos', 4731, 'NT', 181, 'RO', 47.02752000, 26.44483000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q12083462'),
(90183, 'Bodoc', 4754, 'CV', 181, 'RO', 45.95000000, 25.85000000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q1091912'),
(90184, 'Bogata', 4720, 'SV', 181, 'RO', 47.40789000, 26.18655000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q12083161'),
(90185, 'Bogaţi', 4722, 'AG', 181, 'RO', 44.86667000, 25.13333000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q2534330'),
(90186, 'Bogda', 4748, 'TM', 181, 'RO', 45.97833000, 21.57250000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q14771810'),
(90187, 'Bogdan Vodă', 4760, 'MM', 181, 'RO', 47.69197000, 24.26605000, '2019-10-05 23:14:40', '2020-05-01 17:23:10', 1, 'Q1098854'),
(90188, 'Bogdana', 4752, 'VS', 181, 'RO', 46.52668000, 27.63278000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q2547591'),
(90189, 'Bogdana', 4728, 'TR', 181, 'RO', 43.93333000, 25.08333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q950842'),
(90190, 'Bogdand', 4746, 'SM', 181, 'RO', 47.41667000, 22.93333000, '2019-10-05 23:14:40', '2019-10-05 23:14:40', 1, 'Q833343'),
(90191, 'Bogdăneşti', 4720, 'SV', 181, 'RO', 47.36667000, 26.28333000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q679037'),
(90192, 'Bogdăneşti', 4734, 'CJ', 181, 'RO', 46.63333000, 23.13333000, '2019-10-05 23:14:40', '2020-05-01 17:23:09', 1, 'Q679037'),
(90193, 'Bogdăneşti', 4744, 'BC', 181, 'RO', 46.21667000, 26.68333000, '2019-10-05 23:14:40', '2020-05-01 17:23:08', 1, 'Q15630148'),
(90194, 'Bogdănești', 4752, 'VS', 181, 'RO', 46.44965000, 27.72679000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q1656308'),
(90195, 'Bogdăniţa', 4752, 'VS', 181, 'RO', 46.45000000, 27.68333000, '2019-10-05 23:14:40', '2020-05-01 17:23:11', 1, 'Q2548898'),
(90196, 'Bogei', 4723, 'BH', 181, 'RO', 47.26550000, 22.36079000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q280233'),
(90197, 'Bogheşti', 4758, 'VN', 181, 'RO', 46.16667000, 27.40000000, '2019-10-05 23:14:41', '2020-05-01 17:23:12', 1, 'Q12684129'),
(90198, 'Boghicea', 4731, 'NT', 181, 'RO', 47.05414000, 27.06935000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15963828'),
(90199, 'Boghiș', 4741, 'SJ', 181, 'RO', 47.15512000, 22.73880000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q169944'),
(90200, 'Bogza', 4758, 'VN', 181, 'RO', 45.50798000, 27.19773000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12083311'),
(90201, 'Bohotin', 4735, 'IS', 181, 'RO', 46.93914000, 27.97782000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084417'),
(90202, 'Boian', 4755, 'SB', 181, 'RO', 46.20159000, 24.22862000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q663384'),
(90203, 'Boianu Mare', 4723, 'BH', 181, 'RO', 47.38333000, 22.53333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15614927'),
(90204, 'Boinești', 4746, 'SM', 181, 'RO', 47.91567000, 23.35117000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q726047'),
(90205, 'Boiu Mare', 4760, 'MM', 181, 'RO', 47.40000000, 23.58333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q1195051'),
(90206, 'Boişoara', 4757, 'VL', 181, 'RO', 45.43333000, 24.38333000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q2298101'),
(90207, 'Boița', 4755, 'SB', 181, 'RO', 45.63454000, 24.26037000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q833686'),
(90208, 'Bolboşi', 4750, 'GJ', 181, 'RO', 44.75000000, 23.21667000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2717817'),
(90209, 'Boldeşti', 4729, 'PH', 181, 'RO', 44.86597000, 26.54906000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q12083753'),
(90210, 'Boldeşti-Scăeni', 4729, 'PH', 181, 'RO', 45.03333000, 26.03333000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q12083753'),
(90211, 'Boldu', 4756, 'BZ', 181, 'RO', 45.33333000, 27.23333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12722850'),
(90212, 'Boldur', 4748, 'TM', 181, 'RO', 45.69417000, 21.77667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q14772528'),
(90213, 'Bolintin Deal', 4726, 'GR', 181, 'RO', 44.45917000, 25.82111000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2717321'),
(90214, 'Bolintin Vale', 4726, 'GR', 181, 'RO', 44.44889000, 25.75778000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2717321'),
(90215, 'Boloteşti', 4758, 'VN', 181, 'RO', 45.83333000, 27.06667000, '2019-10-05 23:14:41', '2020-05-01 17:23:12', 1, 'Q12083787'),
(90216, 'Bolovăniș', 4744, 'BC', 181, 'RO', 46.61633000, 26.05263000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q852878'),
(90217, 'Bolvașnița', 4753, 'CS', 181, 'RO', 45.34558000, 22.31012000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q75526'),
(90218, 'Bolătău', 4744, 'BC', 181, 'RO', 46.63345000, 26.40149000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q12083767'),
(90219, 'Bonțida', 4734, 'CJ', 181, 'RO', 46.91550000, 23.81475000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q628100'),
(90220, 'Borca', 4731, 'NT', 181, 'RO', 47.18333000, 25.76667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15963876'),
(90221, 'Borcea', 4732, 'CL', 181, 'RO', 44.33333000, 27.75000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12724560'),
(90222, 'Borcut', 4760, 'MM', 181, 'RO', 47.48200000, 23.84071000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q725278'),
(90223, 'Bordeasca Veche', 4758, 'VN', 181, 'RO', 45.54447000, 27.31730000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12083986'),
(90224, 'Bordei Verde', 4736, 'BR', 181, 'RO', 45.08333000, 27.56667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12083968'),
(90225, 'Bordenii Mari', 4729, 'PH', 181, 'RO', 45.08951000, 25.86964000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12083970'),
(90226, 'Bordeşti', 4758, 'VN', 181, 'RO', 45.55000000, 27.05000000, '2019-10-05 23:14:41', '2020-05-01 17:23:12', 1, 'Q12083975'),
(90227, 'Borduşani', 4743, 'IL', 181, 'RO', 44.48333000, 27.90000000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12083983'),
(90228, 'Borleşti', 4731, 'NT', 181, 'RO', 46.76667000, 26.48333000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2605696'),
(90229, 'Borlova', 4753, 'CS', 181, 'RO', 45.36738000, 22.35200000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q621513'),
(90230, 'Boroaia', 4720, 'SV', 181, 'RO', 47.35000000, 26.33333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2606147'),
(90231, 'Borod', 4723, 'BH', 181, 'RO', 46.98333000, 22.63333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15768741'),
(90232, 'Borosoaia', 4735, 'IS', 181, 'RO', 47.46300000, 27.09862000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084218'),
(90233, 'Boroşneu Mare', 4754, 'CV', 181, 'RO', 45.81667000, 26.00000000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q1097085'),
(90234, 'Borsec', 4749, 'HR', 181, 'RO', 46.95000000, 25.56667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q1097085'),
(90235, 'Borănești', 4743, 'IL', 181, 'RO', 44.66069000, 26.60505000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12722899'),
(90236, 'Borăscu', 4750, 'GJ', 181, 'RO', 44.70000000, 23.28333000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2718036'),
(90237, 'Borş', 4723, 'BH', 181, 'RO', 47.11667000, 21.81667000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q12084264'),
(90238, 'Borşa', 4760, 'MM', 181, 'RO', 47.65527000, 24.66328000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12084264'),
(90239, 'Borşa', 4734, 'CJ', 181, 'RO', 46.93333000, 23.66667000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q1089942'),
(90240, 'Bosanci', 4720, 'SV', 181, 'RO', 47.58333000, 26.31667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q848206'),
(90241, 'Bosia', 4735, 'IS', 181, 'RO', 47.21696000, 27.76707000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084346'),
(90242, 'Boteni', 4722, 'AG', 181, 'RO', 45.18333000, 25.11667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q367260'),
(90243, 'Botești', 4731, 'NT', 181, 'RO', 47.05200000, 26.74863000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2719212'),
(90244, 'Botiz', 4746, 'SM', 181, 'RO', 47.83333000, 22.95000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q791336'),
(90245, 'Botiza', 4760, 'MM', 181, 'RO', 47.66667000, 24.15000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q586692'),
(90246, 'Botoroaga', 4728, 'TR', 181, 'RO', 44.14833000, 25.54472000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2720643'),
(90247, 'Botoşana', 4720, 'SV', 181, 'RO', 47.68333000, 25.95000000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q848491'),
(90248, 'Botoşani', 4740, 'BT', 181, 'RO', 47.75000000, 26.66667000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q848491'),
(90249, 'Botoşeşti-Paia', 4742, 'DJ', 181, 'RO', 44.40000000, 23.26667000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q2538604'),
(90250, 'Boureni', 4742, 'DJ', 181, 'RO', 44.01589000, 23.43393000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084404'),
(90251, 'Boureni', 4735, 'IS', 181, 'RO', 47.26497000, 26.97672000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084402'),
(90252, 'Bozieni', 4731, 'NT', 181, 'RO', 46.83333000, 27.15000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2541052'),
(90253, 'Bozioru', 4756, 'BZ', 181, 'RO', 45.38333000, 26.48333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12083548'),
(90254, 'Bozovici', 4753, 'CS', 181, 'RO', 44.93889000, 22.00167000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q848684'),
(90255, 'Boşorod', 4721, 'HD', 181, 'RO', 45.68333000, 23.08333000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q5053732'),
(90256, 'Boţeşti', 4752, 'VS', 181, 'RO', 46.80000000, 27.88333000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q2547310'),
(90257, 'Boţeşti', 4722, 'AG', 181, 'RO', 45.01667000, 25.13333000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q2534261'),
(90258, 'Brabeți', 4742, 'DJ', 181, 'RO', 43.96098000, 24.01339000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q12084517'),
(90259, 'Brabova', 4742, 'DJ', 181, 'RO', 44.36667000, 23.43333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2280742'),
(90260, 'Brad', 4721, 'HD', 181, 'RO', 46.13333000, 22.78333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q16898312'),
(90261, 'Bradu', 4755, 'SB', 181, 'RO', 45.72221000, 24.33234000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q541227'),
(90262, 'Bradu', 4722, 'AG', 181, 'RO', 44.78333000, 24.90000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2348302'),
(90263, 'Bragadiru', 4728, 'TR', 181, 'RO', 43.76667000, 25.51667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q945389'),
(90264, 'Bragadiru', 4725, 'IF', 181, 'RO', 44.37111000, 25.97750000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q16898362'),
(90265, 'Braloştiţa', 4742, 'DJ', 181, 'RO', 44.50000000, 23.51667000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q635609'),
(90266, 'Bran', 4759, 'BV', 181, 'RO', 45.51667000, 25.35000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15233674'),
(90267, 'Braneț', 4738, 'OT', 181, 'RO', 44.32576000, 24.17792000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12084569'),
(90268, 'Braniştea', 4733, 'BN', 181, 'RO', 47.16667000, 24.06667000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q1077572'),
(90269, 'Braniştea', 4745, 'DB', 181, 'RO', 44.68333000, 25.58333000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q12084583'),
(90270, 'Braniştea', 4747, 'GL', 181, 'RO', 45.45000000, 27.85000000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q2288726'),
(90271, 'Braniștea', 4726, 'GR', 181, 'RO', 43.96315000, 26.03812000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q12084580'),
(90272, 'Braniștea', 4751, 'MH', 181, 'RO', 44.24201000, 22.97354000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2091745'),
(90273, 'Brastavățu', 4738, 'OT', 181, 'RO', 43.90634000, 24.40784000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2718995'),
(90274, 'Bratca', 4723, 'BH', 181, 'RO', 46.93333000, 22.61667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15909982'),
(90275, 'Brateiu', 4755, 'SB', 181, 'RO', 46.16667000, 24.41667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q791097'),
(90276, 'Brateş', 4754, 'CV', 181, 'RO', 45.83333000, 26.06667000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q2924083'),
(90277, 'Bratovoești', 4742, 'DJ', 181, 'RO', 44.13333000, 23.90000000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q2538619'),
(90278, 'Brazi', 4721, 'HD', 181, 'RO', 45.48716000, 22.84040000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q6486112'),
(90279, 'Brazii', 4739, 'AR', 181, 'RO', 46.23333000, 22.33333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q1047450'),
(90280, 'Brazii de Sus', 4729, 'PH', 181, 'RO', 44.86667000, 26.01667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084549'),
(90281, 'Braşov', 4759, 'BV', 181, 'RO', 45.64861000, 25.60613000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q16898139'),
(90282, 'Breasta', 4742, 'DJ', 181, 'RO', 44.33333000, 23.68333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2329026'),
(90283, 'Breaza', 4729, 'PH', 181, 'RO', 45.18333000, 25.66667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2329026'),
(90284, 'Breaza', 4733, 'BN', 181, 'RO', 47.35108000, 24.06220000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q4967501'),
(90285, 'Breaza', 4756, 'BZ', 181, 'RO', 45.10000000, 26.53333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q3293935'),
(90286, 'Breaza', 4720, 'SV', 181, 'RO', 47.61502000, 25.31816000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2606064'),
(90287, 'Breaza de Jos', 4729, 'PH', 181, 'RO', 45.17125000, 25.67571000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12085091'),
(90288, 'Breaza de Sus', 4729, 'PH', 181, 'RO', 45.19879000, 25.65578000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12085094'),
(90289, 'Breazu', 4735, 'IS', 181, 'RO', 47.21343000, 27.51815000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12085100'),
(90290, 'Breb', 4760, 'MM', 181, 'RO', 47.74854000, 23.90494000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q724877'),
(90291, 'Brebeni', 4738, 'OT', 181, 'RO', 44.36667000, 24.45000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2605154'),
(90292, 'Brebu', 4753, 'CS', 181, 'RO', 45.42028000, 21.99250000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12724577'),
(90293, 'Brebu Megieșesc', 4729, 'PH', 181, 'RO', 45.16774000, 25.77474000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q12084700'),
(90294, 'Brebu Mânăstirei', 4729, 'PH', 181, 'RO', 45.18333000, 25.76667000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q12084703'),
(90295, 'Brebu Nou', 4753, 'CS', 181, 'RO', 45.23333000, 22.13333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q515559'),
(90296, 'Brehuiești', 4740, 'BT', 181, 'RO', 47.70194000, 26.54472000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q12084827'),
(90297, 'Brestovăț', 4748, 'TM', 181, 'RO', 45.87342000, 21.68201000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q14772582'),
(90298, 'Bretea Română', 4721, 'HD', 181, 'RO', 45.66085000, 23.01739000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q5054429'),
(90299, 'Brezniţa Ocol', 4751, 'MH', 181, 'RO', 44.66667000, 22.61833000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2605902'),
(90300, 'Brezniţa-Motru', 4751, 'MH', 181, 'RO', 44.56667000, 23.21667000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2465539'),
(90301, 'Brezoaele', 4745, 'DB', 181, 'RO', 44.58333000, 25.73333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q5069916'),
(90302, 'Brezoaia', 4745, 'DB', 181, 'RO', 44.55683000, 25.78527000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084756'),
(90303, 'Brezoi', 4757, 'VL', 181, 'RO', 45.33797000, 24.24863000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084756'),
(90304, 'Breţcu', 4754, 'CV', 181, 'RO', 46.05000000, 26.30000000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q2924828'),
(90305, 'Brodina', 4720, 'SV', 181, 'RO', 47.88333000, 25.41667000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2606112'),
(90306, 'Brodoc', 4752, 'VS', 181, 'RO', 46.65090000, 27.67669000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q12084961'),
(90307, 'Broscăuţi', 4740, 'BT', 181, 'RO', 47.95000000, 26.45000000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q2715853'),
(90308, 'Broşteni', 4720, 'SV', 181, 'RO', 47.23333000, 25.70000000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q2715853'),
(90309, 'Broşteni', 4758, 'VN', 181, 'RO', 45.75000000, 27.03333000, '2019-10-05 23:14:41', '2020-05-01 17:23:12', 1, 'Q13823064'),
(90310, 'Broşteni', 4751, 'MH', 181, 'RO', 44.76083000, 22.97944000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q275649'),
(90312, 'Broșteni', 4745, 'DB', 181, 'RO', 44.72053000, 25.50935000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q12085023'),
(90313, 'Broșteni', 4743, 'IL', 181, 'RO', 44.67192000, 26.74787000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12085025'),
(90314, 'Bruiu', 4755, 'SB', 181, 'RO', 45.86667000, 24.70000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q850214'),
(90315, 'Brusturi', 4731, 'NT', 181, 'RO', 47.28333000, 26.38333000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q2605687'),
(90316, 'Brusturi', 4723, 'BH', 181, 'RO', 47.15000000, 22.25000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15910016'),
(90317, 'Brusturoasa', 4744, 'BC', 181, 'RO', 46.51667000, 26.20000000, '2019-10-05 23:14:41', '2019-10-05 23:14:41', 1, 'Q15632217'),
(90318, 'Brânceni', 4728, 'TR', 181, 'RO', 43.88333000, 25.40000000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q2549789'),
(90319, 'Brâncoveanca', 4728, 'TR', 181, 'RO', 43.94075000, 24.69336000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q12084898'),
(90320, 'Brâncoveni', 4738, 'OT', 181, 'RO', 44.31667000, 24.30000000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q525096'),
(90321, 'Brădeanu', 4756, 'BZ', 181, 'RO', 44.93333000, 26.85000000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q12084742'),
(90322, 'Brădeni', 4755, 'SB', 181, 'RO', 46.08333000, 24.83333000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q997245'),
(90323, 'Brădeşti', 4749, 'HR', 181, 'RO', 46.35000000, 25.35000000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q999873'),
(90324, 'Brădeşti', 4742, 'DJ', 181, 'RO', 44.48333000, 23.63333000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q2058229'),
(90325, 'Brădicești', 4735, 'IS', 181, 'RO', 46.84810000, 27.90270000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12084745'),
(90326, 'Brăduleţ', 4722, 'AG', 181, 'RO', 45.28333000, 24.76667000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q2534269'),
(90327, 'Brăduţ', 4754, 'CV', 181, 'RO', 46.13333000, 25.61667000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q790399'),
(90328, 'Brăeşti', 4740, 'BT', 181, 'RO', 47.86667000, 26.45000000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q2534480'),
(90329, 'Brăeşti', 4735, 'IS', 181, 'RO', 47.15000000, 27.10000000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q2718512'),
(90330, 'Brăeşti', 4756, 'BZ', 181, 'RO', 45.43333000, 26.50000000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q12084841'),
(90331, 'Brăhăşeştii de Sus', 4747, 'GL', 181, 'RO', 46.05000000, 27.35000000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q12084841'),
(90332, 'Brăhășești', 4747, 'GL', 181, 'RO', 46.03333000, 27.36667000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q2288686'),
(90333, 'Brăiești', 4720, 'SV', 181, 'RO', 47.49673000, 26.07182000, '2019-10-05 23:14:41', '2020-05-01 17:23:11', 1, 'Q12084842'),
(90334, 'Brăila', 4736, 'BR', 181, 'RO', 45.27152000, 27.97429000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q12084842'),
(90335, 'Brăneşti', 4745, 'DB', 181, 'RO', 45.03333000, 25.41667000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q12084777'),
(90336, 'Brăneşti', 4750, 'GJ', 181, 'RO', 44.65000000, 23.46667000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q780153'),
(90337, 'Brăneşti', 4725, 'IF', 181, 'RO', 44.45000000, 26.33333000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12084774'),
(90338, 'Brănişca', 4721, 'HD', 181, 'RO', 45.91667000, 22.78333000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12084779'),
(90339, 'Brăniștari', 4726, 'GR', 181, 'RO', 44.17626000, 26.05953000, '2019-10-05 23:14:41', '2020-05-01 17:23:09', 1, 'Q12084781'),
(90340, 'Brătești', 4735, 'IS', 181, 'RO', 47.19135000, 26.68580000, '2019-10-05 23:14:41', '2020-05-01 17:23:10', 1, 'Q12084781'),
(90341, 'Brătești', 4744, 'BC', 181, 'RO', 46.32540000, 26.64333000, '2019-10-05 23:14:41', '2020-05-01 17:23:08', 1, 'Q12084802'),
(90342, 'Brătila', 4744, 'BC', 181, 'RO', 46.32348000, 26.77442000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q12084820'),
(90343, 'Bucecea', 4740, 'BT', 181, 'RO', 47.76667000, 26.43333000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12084820'),
(90344, 'Bucerdea-Grânoasă', 4724, 'AB', 181, 'RO', 46.20000000, 23.83333000, '2019-10-05 23:14:42', '2020-05-01 17:23:07', 1, 'Q1092561'),
(90345, 'Buceş', 4721, 'HD', 181, 'RO', 46.18333000, 22.93333000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q5065682'),
(90346, 'Bucești', 4747, 'GL', 181, 'RO', 45.65250000, 27.53008000, '2019-10-05 23:14:42', '2020-05-01 17:23:09', 1, 'Q12086058'),
(90347, 'Bucharest', 4730, 'B', 181, 'RO', 44.43225000, 26.10626000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q19660'),
(90348, 'Buchin', 4753, 'CS', 181, 'RO', 45.36667000, 22.25000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q1090028'),
(90349, 'Bucinişu', 4738, 'OT', 181, 'RO', 43.95000000, 24.25000000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q2719586'),
(90350, 'Bucium', 4724, 'AB', 181, 'RO', 46.26402000, 23.17910000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q1193905'),
(90351, 'Bucium-Orlea', 4721, 'HD', 181, 'RO', 45.58333000, 22.96667000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q3178687'),
(90352, 'Buciumeni', 4725, 'IF', 181, 'RO', 44.54975000, 25.96419000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12086081'),
(90353, 'Buciumeni', 4747, 'GL', 181, 'RO', 46.00000000, 27.30000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q2288744'),
(90354, 'Buciumeni', 4745, 'DB', 181, 'RO', 45.15000000, 25.45000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12086085'),
(90355, 'Buciumi', 4744, 'BC', 181, 'RO', 46.20230000, 26.78445000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q15632511'),
(90356, 'Buciumi', 4741, 'SJ', 181, 'RO', 47.03333000, 23.06667000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q1081327'),
(90357, 'Bucov', 4729, 'PH', 181, 'RO', 44.96667000, 26.08333000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085503'),
(90358, 'Bucova', 4753, 'CS', 181, 'RO', 45.50906000, 22.63731000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q766649'),
(90359, 'Bucovineni', 4740, 'BT', 181, 'RO', 47.85000000, 26.33333000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q766649'),
(90360, 'Bucovăţ', 4742, 'DJ', 181, 'RO', 44.30000000, 23.75000000, '2019-10-05 23:14:42', '2020-05-01 17:23:09', 1, 'Q2294097'),
(90361, 'Bucovăț', 4748, 'TM', 181, 'RO', 45.75569000, 21.38324000, '2019-10-05 23:14:42', '2020-05-01 17:23:11', 1, 'Q14772645'),
(90362, 'Bucoşniţa', 4753, 'CS', 181, 'RO', 45.30000000, 22.26667000, '2019-10-05 23:14:42', '2020-05-01 17:23:09', 1, 'Q75496'),
(90363, 'Bucu', 4743, 'IL', 181, 'RO', 44.60532000, 27.49337000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q145755'),
(90364, 'Bucureşci', 4721, 'HD', 181, 'RO', 46.13333000, 22.90000000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q5065959'),
(90365, 'Bucşani', 4726, 'GR', 181, 'RO', 44.37611000, 25.65528000, '2019-10-05 23:14:42', '2020-05-01 17:23:09', 1, 'Q2717327'),
(90366, 'Bucşani', 4745, 'DB', 181, 'RO', 44.86667000, 25.65000000, '2019-10-05 23:14:42', '2020-05-01 17:23:09', 1, 'Q12085543'),
(90367, 'Bucșenești', 4722, 'AG', 181, 'RO', 45.30096000, 24.65841000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q12085542'),
(90368, 'Bucșenești-Lotași', 4722, 'AG', 181, 'RO', 45.01307000, 24.96845000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q12085544'),
(90369, 'Bucșești', 4744, 'BC', 181, 'RO', 46.48381000, 26.58230000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q12085545'),
(90370, 'Bucșoaia', 4720, 'SV', 181, 'RO', 47.53972000, 25.81118000, '2019-10-05 23:14:42', '2020-05-01 17:23:11', 1, 'Q12085545'),
(90371, 'Buda', 4729, 'PH', 181, 'RO', 44.82443000, 26.18522000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q8253342'),
(90372, 'Buda', 4752, 'VS', 181, 'RO', 46.76231000, 27.42113000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085196'),
(90373, 'Buda', 4744, 'BC', 181, 'RO', 46.67111000, 26.71953000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085187'),
(90374, 'Buda', 4756, 'BZ', 181, 'RO', 45.50000000, 26.90000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q3293912'),
(90375, 'Buda', 4735, 'IS', 181, 'RO', 47.40318000, 26.67835000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085194'),
(90376, 'Budacu de Jos', 4733, 'BN', 181, 'RO', 47.08333000, 24.51667000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q1188965'),
(90377, 'Budacu de Sus', 4733, 'BN', 181, 'RO', 47.06118000, 24.65752000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q4967462'),
(90378, 'Budeasa Mică', 4722, 'AG', 181, 'RO', 44.95000000, 24.85000000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q12085377'),
(90379, 'Budeni', 4720, 'SV', 181, 'RO', 47.41383000, 26.65927000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085226'),
(90380, 'Budeşti', 4757, 'VL', 181, 'RO', 45.05000000, 24.38333000, '2019-10-05 23:14:42', '2020-05-01 17:23:11', 1, 'Q12085238'),
(90381, 'Budeşti', 4760, 'MM', 181, 'RO', 47.73333000, 23.95000000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q851403'),
(90382, 'Budeşti', 4732, 'CL', 181, 'RO', 44.23472000, 26.46583000, '2019-10-05 23:14:42', '2020-05-01 17:23:09', 1, 'Q851403'),
(90383, 'Budeşti', 4733, 'BN', 181, 'RO', 46.88333000, 24.25000000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q851306'),
(90384, 'Budești', 4731, 'NT', 181, 'RO', 46.94084000, 26.71177000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q12085235'),
(90385, 'Budești', 4758, 'VN', 181, 'RO', 45.62779000, 27.05669000, '2019-10-05 23:14:42', '2020-05-01 17:23:12', 1, 'Q12085234'),
(90386, 'Budila', 4759, 'BV', 181, 'RO', 45.66667000, 25.80000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q1059438'),
(90387, 'Budureasa', 4723, 'BH', 181, 'RO', 46.66667000, 22.50000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q15910051'),
(90388, 'Buduslău', 4723, 'BH', 181, 'RO', 47.40000000, 22.26667000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q15966806'),
(90389, 'Budăi', 4735, 'IS', 181, 'RO', 47.21773000, 27.22282000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q12128424'),
(90390, 'Buești', 4743, 'IL', 181, 'RO', 44.54473000, 27.18729000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q12724589'),
(90391, 'Buftea', 4725, 'IF', 181, 'RO', 44.56139000, 25.94889000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q16898370'),
(90392, 'Bughea de Jos', 4722, 'AG', 181, 'RO', 45.26667000, 25.00000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q948699'),
(90393, 'Bughea de Jos', 4729, 'PH', 181, 'RO', 45.17985000, 26.02044000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085177'),
(90394, 'Bughea de Sus', 4722, 'AG', 181, 'RO', 45.29812000, 25.03284000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q957334'),
(90395, 'Buhalnița', 4735, 'IS', 181, 'RO', 47.37892000, 26.91547000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q12085986'),
(90396, 'Buhoci', 4744, 'BC', 181, 'RO', 46.56667000, 27.01667000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q15633430'),
(90397, 'Buhuşi', 4744, 'BC', 181, 'RO', 46.71667000, 26.70000000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q16898108'),
(90398, 'Bujoreni', 4728, 'TR', 181, 'RO', 44.11667000, 25.63333000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q2720408'),
(90399, 'Bujoreni', 4757, 'VL', 181, 'RO', 45.13333000, 24.35000000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q1889960'),
(90400, 'Bujoru', 4728, 'TR', 181, 'RO', 43.71667000, 25.56667000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q2720577'),
(90401, 'Bulbucata', 4726, 'GR', 181, 'RO', 44.28333000, 25.80333000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q2717380'),
(90402, 'Bulgăruș', 4748, 'TM', 181, 'RO', 45.91947000, 20.82219000, '2019-10-05 23:14:42', '2020-05-01 17:23:11', 1, 'Q833543'),
(90403, 'Buliga', 4743, 'IL', 181, 'RO', 44.35731000, 27.79225000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q833543'),
(90404, 'Bulz', 4723, 'BH', 181, 'RO', 46.91667000, 22.68333000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q1191387'),
(90405, 'Bulzeşti', 4742, 'DJ', 181, 'RO', 44.53333000, 23.88333000, '2019-10-05 23:14:42', '2020-05-01 17:23:09', 1, 'Q2468459'),
(90406, 'Bulzeștii de Sus', 4721, 'HD', 181, 'RO', 46.30070000, 22.76118000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q5054394'),
(90407, 'Bumbeşti-Jiu', 4750, 'GJ', 181, 'RO', 45.16667000, 23.40000000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q5054394'),
(90408, 'Buneşti', 4757, 'VL', 181, 'RO', 45.11667000, 24.18333000, '2019-10-05 23:14:42', '2020-05-01 17:23:11', 1, 'Q437181'),
(90409, 'Buneşti', 4759, 'BV', 181, 'RO', 46.10000000, 25.05000000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q15241187'),
(90410, 'Buneşti', 4752, 'VS', 181, 'RO', 46.83333000, 27.96667000, '2019-10-05 23:14:42', '2020-05-01 17:23:11', 1, 'Q11070209'),
(90411, 'Bunești', 4720, 'SV', 181, 'RO', 47.52501000, 26.28826000, '2019-10-05 23:14:42', '2020-05-01 17:23:11', 1, 'Q783003'),
(90412, 'Bungetu', 4745, 'DB', 181, 'RO', 44.84323000, 25.53816000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085641'),
(90413, 'Bunila', 4721, 'HD', 181, 'RO', 45.70000000, 22.66667000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q5064659'),
(90414, 'Bunteşti', 4723, 'BH', 181, 'RO', 46.61667000, 22.46667000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q1086482'),
(90415, 'Burca', 4758, 'VN', 181, 'RO', 45.91224000, 26.94905000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085740'),
(90416, 'Buriaș', 4725, 'IF', 181, 'RO', 44.73304000, 25.98053000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q12085879'),
(90417, 'Burila Mare', 4751, 'MH', 181, 'RO', 44.45278000, 22.57361000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q2465449'),
(90418, 'Burjuc', 4721, 'HD', 181, 'RO', 45.95000000, 22.48333000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q5054314'),
(90419, 'Burla', 4720, 'SV', 181, 'RO', 47.79033000, 25.92696000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q2719998'),
(90420, 'Bursuceni', 4720, 'SV', 181, 'RO', 47.65097000, 26.47530000, '2019-10-05 23:14:42', '2019-10-05 23:14:42', 1, 'Q12085825'),
(90421, 'Burueneşti', 4756, 'BZ', 181, 'RO', 45.31667000, 26.35000000, '2019-10-05 23:14:42', '2020-05-01 17:23:08', 1, 'Q12085825'),
(90422, 'Buruienești', 4731, 'NT', 181, 'RO', 46.99595000, 26.97732000, '2019-10-05 23:14:42', '2020-05-01 17:23:10', 1, 'Q225012'),
(90423, 'Burzuc', 4723, 'BH', 181, 'RO', 47.15552000, 22.17096000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q754946'),
(90424, 'Bustuchin', 4750, 'GJ', 181, 'RO', 44.96667000, 23.73333000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q2483604'),
(90425, 'Butea', 4735, 'IS', 181, 'RO', 47.06667000, 26.91667000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q2718476'),
(90426, 'Buteni', 4739, 'AR', 181, 'RO', 46.31667000, 22.11667000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q852398'),
(90427, 'Butimanu', 4745, 'DB', 181, 'RO', 44.68333000, 25.90000000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q12085977'),
(90428, 'Butoieşti', 4751, 'MH', 181, 'RO', 44.58333000, 23.36667000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q1861214'),
(90429, 'Buturugeni', 4726, 'GR', 181, 'RO', 44.36528000, 25.83528000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q2717335'),
(90430, 'Buza', 4734, 'CJ', 181, 'RO', 46.90000000, 24.15000000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q1076348'),
(90431, 'Buzescu', 4728, 'TR', 181, 'RO', 44.01667000, 25.23333000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q2720421'),
(90432, 'Buziaş', 4748, 'TM', 181, 'RO', 45.64917000, 21.60361000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q16898586'),
(90433, 'Buznea', 4735, 'IS', 181, 'RO', 47.19515000, 27.01572000, '2019-10-05 23:14:43', '2019-10-05 23:14:43', 1, 'Q12085457'),
(90434, 'Buzoeşti', 4722, 'AG', 181, 'RO', 44.58333000, 24.91667000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q1896983'),
(90435, 'Buzău', 4756, 'BZ', 181, 'RO', 45.15000000, 26.83333000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q26327473'),
(90436, 'Buşteni', 4729, 'PH', 181, 'RO', 45.40000000, 25.53333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q26327473'),
(90437, 'Bușca', 4738, 'OT', 181, 'RO', 44.11166000, 24.80238000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12086094'),
(90438, 'Bâcleș', 4751, 'MH', 181, 'RO', 44.48333000, 23.13333000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q1846565'),
(90439, 'Bâcu', 4726, 'GR', 181, 'RO', 44.48259000, 25.88904000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12082564'),
(90440, 'Bâldana', 4745, 'DB', 181, 'RO', 44.60300000, 25.77836000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12082578'),
(90441, 'Bâlta', 4750, 'GJ', 181, 'RO', 45.11791000, 23.10220000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12082586'),
(90442, 'Bâlta', 4742, 'DJ', 181, 'RO', 44.53564000, 23.47019000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12082586'),
(90443, 'Bâlteni', 4750, 'GJ', 181, 'RO', 44.86919000, 23.27311000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2538916'),
(90444, 'Bâlvăneşti', 4751, 'MH', 181, 'RO', 44.79750000, 22.68028000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2258398'),
(90445, 'Bâra', 4731, 'NT', 181, 'RO', 47.03333000, 27.05000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q15963075'),
(90446, 'Bârca', 4742, 'DJ', 181, 'RO', 43.96667000, 23.61667000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12724604'),
(90447, 'Bârghiş', 4755, 'SB', 181, 'RO', 45.98333000, 24.53333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q855637'),
(90448, 'Bârgăuani', 4731, 'NT', 181, 'RO', 46.98333000, 26.63333000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q15959439'),
(90449, 'Bârla', 4722, 'AG', 181, 'RO', 44.42606000, 24.77743000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q2621227'),
(90450, 'Bârlad', 4752, 'VS', 181, 'RO', 46.23175000, 27.66907000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q2621227'),
(90451, 'Bârlogu', 4722, 'AG', 181, 'RO', 44.62658000, 25.08066000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12082633'),
(90452, 'Bârna', 4748, 'TM', 181, 'RO', 45.71667000, 22.05000000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q14770250'),
(90453, 'Bârnova', 4735, 'IS', 181, 'RO', 47.06667000, 27.61667000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2621250'),
(90454, 'Bârsa', 4739, 'AR', 181, 'RO', 46.38333000, 22.06667000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q791066'),
(90455, 'Bârsana', 4760, 'MM', 181, 'RO', 47.81667000, 24.06667000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q790394'),
(90456, 'Bârseşti', 4758, 'VN', 181, 'RO', 45.91667000, 26.73333000, '2019-10-05 23:14:43', '2020-05-01 17:23:12', 1, 'Q12724608'),
(90457, 'Bârsăneşti', 4744, 'BC', 181, 'RO', 46.33333000, 26.70000000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q15630051'),
(90458, 'Bârsău de Sus', 4746, 'SM', 181, 'RO', 47.60000000, 23.21667000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q999045'),
(90460, 'Bârzava', 4739, 'AR', 181, 'RO', 46.11667000, 21.98333000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q1059019'),
(90461, 'Bârzești', 4752, 'VS', 181, 'RO', 46.75000000, 27.55000000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12082616'),
(90462, 'Bâsca Chiojdului', 4756, 'BZ', 181, 'RO', 45.37120000, 26.18355000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12082663'),
(90463, 'Bâsca Rozilei', 4756, 'BZ', 181, 'RO', 45.45387000, 26.33714000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12082664'),
(90464, 'Bâscenii de Jos', 4756, 'BZ', 181, 'RO', 45.24708000, 26.32122000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12082693'),
(90465, 'Bâscenii de Sus', 4756, 'BZ', 181, 'RO', 45.26662000, 26.27073000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12082695'),
(90466, 'Bâscoveni', 4728, 'TR', 181, 'RO', 44.24444000, 25.35998000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12082666'),
(90467, 'Băbana', 4722, 'AG', 181, 'RO', 44.90000000, 24.70000000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q2348288'),
(90468, 'Băbeni', 4741, 'SJ', 181, 'RO', 47.30000000, 23.40000000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q1078309'),
(90469, 'Băbeni', 4756, 'BZ', 181, 'RO', 45.44635000, 26.97875000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12081427'),
(90470, 'Băbeni', 4757, 'VL', 181, 'RO', 44.96667000, 24.23333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081427'),
(90471, 'Băbeni-Oltețu', 4757, 'VL', 181, 'RO', 44.61404000, 23.97798000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081425'),
(90472, 'Băbiciu', 4738, 'OT', 181, 'RO', 44.03333000, 24.56667000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2718984'),
(90473, 'Băbăiţa', 4728, 'TR', 181, 'RO', 44.16667000, 25.38333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q2719977'),
(90474, 'Băcani', 4752, 'VS', 181, 'RO', 46.33333000, 27.66667000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q2547213'),
(90475, 'Băceşti', 4752, 'VS', 181, 'RO', 46.85000000, 27.23333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q2721415'),
(90476, 'Băcia', 4721, 'HD', 181, 'RO', 45.80000000, 23.01667000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q852600'),
(90477, 'Băcioiu', 4744, 'BC', 181, 'RO', 46.31431000, 27.15745000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12082497'),
(90478, 'Băcălești', 4728, 'TR', 181, 'RO', 44.05997000, 24.81636000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081691'),
(90479, 'Bădeana', 4752, 'VS', 181, 'RO', 46.15000000, 27.58333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081483'),
(90480, 'Bădeni', 4735, 'IS', 181, 'RO', 47.40581000, 26.94898000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12081462'),
(90481, 'Bădeni', 4745, 'DB', 181, 'RO', 45.15272000, 25.40263000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12081460'),
(90482, 'Bădeuți', 4720, 'SV', 181, 'RO', 47.80277000, 25.98387000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081460'),
(90483, 'Bădești', 4722, 'AG', 181, 'RO', 45.17157000, 24.86064000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12081470'),
(90484, 'Bădoși', 4742, 'DJ', 181, 'RO', 44.14723000, 23.88974000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12081473'),
(90485, 'Băduleasa', 4728, 'TR', 181, 'RO', 43.92286000, 24.98274000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081482'),
(90486, 'Băicoi', 4729, 'PH', 181, 'RO', 45.03333000, 25.85000000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081482'),
(90487, 'Băiculeşti', 4722, 'AG', 181, 'RO', 45.06667000, 24.70000000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q1148364'),
(90488, 'Băile Borșa', 4760, 'MM', 181, 'RO', 47.68416000, 24.71391000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q754346'),
(90489, 'Băile Drânceni', 4752, 'VS', 181, 'RO', 46.81361000, 28.10083000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12082521'),
(90490, 'Băile Govora', 4757, 'VL', 181, 'RO', 45.08333000, 24.18333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12082521'),
(90491, 'Băile Herculane', 4753, 'CS', 181, 'RO', 44.87972000, 22.41250000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12082521'),
(90492, 'Băile Olăneşti', 4757, 'VL', 181, 'RO', 45.20000000, 24.23333000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12082521'),
(90493, 'Băile Tuşnad', 4749, 'HR', 181, 'RO', 46.15000000, 25.85000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12082521'),
(90494, 'Băileşti', 4742, 'DJ', 181, 'RO', 44.01667000, 23.35000000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q16898251'),
(90495, 'Băiuţ', 4760, 'MM', 181, 'RO', 47.61667000, 24.00000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q24332'),
(90496, 'Băişoara', 4734, 'CJ', 181, 'RO', 46.58333000, 23.46667000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q250038'),
(90497, 'Băiţa', 4721, 'HD', 181, 'RO', 46.03333000, 22.90000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q221417'),
(90498, 'Băiţa de sub Codru', 4760, 'MM', 181, 'RO', 47.53333000, 23.15000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q1179400'),
(90499, 'Băișești', 4720, 'SV', 181, 'RO', 47.48024000, 26.11115000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q12081684'),
(90500, 'Băița', 4760, 'MM', 181, 'RO', 47.70614000, 23.49326000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12081684'),
(90501, 'Băjești', 4722, 'AG', 181, 'RO', 45.02615000, 24.94006000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q12081503'),
(90502, 'Bălan', 4741, 'SJ', 181, 'RO', 47.15599000, 23.31213000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q1080875'),
(90503, 'Bălan', 4749, 'HR', 181, 'RO', 46.65050000, 25.80834000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q1080875'),
(90504, 'Bălcaciu', 4724, 'AB', 181, 'RO', 46.19122000, 24.06139000, '2019-10-05 23:14:43', '2020-05-01 17:23:07', 1, 'Q253915'),
(90505, 'Bălceşti', 4757, 'VL', 181, 'RO', 44.61667000, 23.95000000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q253915'),
(90506, 'Bălcești', 4750, 'GJ', 181, 'RO', 45.10516000, 23.63290000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12081819'),
(90507, 'Bălcăuţi', 4720, 'SV', 181, 'RO', 47.89149000, 26.07279000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q1065929'),
(90508, 'Băleni', 4747, 'GL', 181, 'RO', 45.81667000, 27.83333000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q2288735'),
(90509, 'Băleni Sârbi', 4745, 'DB', 181, 'RO', 44.81667000, 25.63333000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12081742'),
(90510, 'Băleşti', 4750, 'GJ', 181, 'RO', 45.01667000, 23.21667000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2717662'),
(90511, 'Băleşti', 4758, 'VN', 181, 'RO', 45.43333000, 27.23333000, '2019-10-05 23:14:43', '2020-05-01 17:23:12', 1, 'Q2457284'),
(90512, 'Bălileşti', 4722, 'AG', 181, 'RO', 45.06667000, 24.93333000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q2534288'),
(90513, 'Bălnaca', 4723, 'BH', 181, 'RO', 46.94384000, 22.57206000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q752226'),
(90514, 'Bălteni', 4752, 'VS', 181, 'RO', 46.66667000, 27.61667000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q2546764'),
(90515, 'Bălteni', 4738, 'OT', 181, 'RO', 44.44883000, 24.53247000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2541967'),
(90516, 'Bălteni', 4745, 'DB', 181, 'RO', 44.66488000, 25.69339000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q12081784'),
(90517, 'Băluşeni', 4740, 'BT', 181, 'RO', 47.66667000, 26.80000000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q855734'),
(90518, 'Bălușești', 4731, 'NT', 181, 'RO', 46.81591000, 26.99885000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12081804'),
(90519, 'Bălăbănești', 4747, 'GL', 181, 'RO', 46.09546000, 27.72206000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q855738'),
(90520, 'Bălăceana', 4720, 'SV', 181, 'RO', 47.64338000, 26.04764000, '2019-10-05 23:14:43', '2020-05-01 17:23:11', 1, 'Q2720669'),
(90521, 'Bălăceanca', 4725, 'IF', 181, 'RO', 44.39249000, 26.29009000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q12081747'),
(90522, 'Bălăceanu', 4756, 'BZ', 181, 'RO', 45.26667000, 27.15000000, '2019-10-05 23:14:43', '2020-05-01 17:23:08', 1, 'Q6314610'),
(90523, 'Bălăciţa', 4751, 'MH', 181, 'RO', 44.38333000, 23.13333000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q283479'),
(90524, 'Bălănești', 4738, 'OT', 181, 'RO', 44.24970000, 24.48216000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q11071053'),
(90525, 'Bălăşeşti', 4747, 'GL', 181, 'RO', 46.10000000, 27.66667000, '2019-10-05 23:14:43', '2020-05-01 17:23:09', 1, 'Q2289217'),
(90526, 'Bălţaţi', 4735, 'IS', 181, 'RO', 47.21667000, 27.15000000, '2019-10-05 23:14:43', '2020-05-01 17:23:10', 1, 'Q2607552');

