INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(94532, 'Her<PERSON>', 4720, '<PERSON>', 181, 'R<PERSON>', 47.44286000, 26.01254000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12167177'),
(94533, '<PERSON><PERSON><PERSON>', 4726, 'GR', 181, 'RO', 44.21174000, 26.35903000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q2539270'),
(94534, 'Hida', 4741, 'SJ', 181, 'RO', 47.06667000, 23.28333000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q858383'),
(94535, '<PERSON><PERSON><PERSON><PERSON><PERSON>', 4723, '<PERSON><PERSON>', 181, '<PERSON><PERSON>', 46.95000000, 22.05000000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q1088293'),
(94536, '<PERSON><PERSON><PERSON>eu-<PERSON>ria', 4740, 'BT', 181, 'RO', 48.03333000, 26.25000000, '2019-10-05 23:15:21', '2020-05-01 17:23:08', 1, 'Q2715911'),
(94537, 'Hinova', 4751, 'MH', 181, 'RO', 44.54056000, 22.77694000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q2465415'),
(94538, 'Hlipiceni', 4740, 'BT', 181, 'RO', 47.60000000, 27.15000000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q2715531'),
(94539, 'Hlăpești', 4731, 'NT', 181, 'RO', 47.03817000, 26.59842000, '2019-10-05 23:15:21', '2020-05-01 17:23:10', 1, 'Q12167278'),
(94540, 'Hoceni', 4752, 'VS', 181, 'RO', 46.53917000, 28.00667000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q2548469'),
(94541, 'Hociungi', 4731, 'NT', 181, 'RO', 46.81311000, 26.77596000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q12167753'),
(94542, 'Hodod', 4746, 'SM', 181, 'RO', 47.40000000, 23.03333000, '2019-10-05 23:15:21', '2019-10-05 23:15:21', 1, 'Q1014893'),
(94543, 'Hodoni', 4748, 'TM', 181, 'RO', 45.90646000, 21.08851000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q221319'),
(94544, 'Hodora', 4735, 'IS', 181, 'RO', 47.33712000, 27.01970000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12167443'),
(94545, 'Hodoșa', 4749, 'HR', 181, 'RO', 46.86558000, 25.44620000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q1029365'),
(94546, 'Hoghilag', 4755, 'SB', 181, 'RO', 46.23333000, 24.61667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1037563'),
(94547, 'Hoghiz', 4759, 'BV', 181, 'RO', 45.98333000, 25.30000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q15256061'),
(94548, 'Holbav', 4759, 'BV', 181, 'RO', 45.65922000, 25.38744000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q15256089'),
(94549, 'Holboca', 4735, 'IS', 181, 'RO', 47.15000000, 27.70000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1037420'),
(94550, 'Holod', 4723, 'BH', 181, 'RO', 46.78333000, 22.13333000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1084993'),
(94551, 'Holt', 4744, 'BC', 181, 'RO', 46.57856000, 26.97499000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1625161'),
(94552, 'Homocea', 4758, 'VN', 181, 'RO', 46.13333000, 27.23333000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q14027649'),
(94553, 'Homorod', 4759, 'BV', 181, 'RO', 46.05000000, 25.26667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1070950'),
(94554, 'Homorodu de Jos', 4746, 'SM', 181, 'RO', 47.66667000, 23.08333000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q664596'),
(94555, 'Homorâciu', 4729, 'PH', 181, 'RO', 45.26940000, 26.01634000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q12167570'),
(94556, 'Hopârta', 4724, 'AB', 181, 'RO', 46.32134000, 23.87244000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q1008443'),
(94557, 'Horea', 4724, 'AB', 181, 'RO', 46.50246000, 22.95034000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q774739'),
(94558, 'Horezu', 4757, 'VL', 181, 'RO', 45.15000000, 24.01667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q774739'),
(94559, 'Horgeşti', 4744, 'BC', 181, 'RO', 46.43333000, 27.06667000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q15662774'),
(94560, 'Horia', 4737, 'CT', 181, 'RO', 44.63333000, 28.11667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1055134'),
(94561, 'Horia', 4739, 'AR', 181, 'RO', 46.20847000, 21.46287000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q714450'),
(94562, 'Horia', 4727, 'TL', 181, 'RO', 45.01667000, 28.45000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1042123'),
(94563, 'Horia', 4731, 'NT', 181, 'RO', 46.90698000, 26.91919000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2542027'),
(94564, 'Horleşti', 4735, 'IS', 181, 'RO', 47.11667000, 27.36667000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q1055169'),
(94565, 'Horoatu Crasnei', 4741, 'SJ', 181, 'RO', 47.13333000, 22.88333000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q752198'),
(94566, 'Horodnic de Jos', 4720, 'SV', 181, 'RO', 47.86523000, 25.81856000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q933624'),
(94567, 'Horodnic de Sus', 4720, 'SV', 181, 'RO', 47.84012000, 25.82354000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q963318'),
(94568, 'Horodniceni', 4720, 'SV', 181, 'RO', 47.53333000, 26.16667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2606153'),
(94569, 'Hotar', 4723, 'BH', 181, 'RO', 47.01967000, 22.28515000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q240944'),
(94570, 'Hotarele', 4726, 'GR', 181, 'RO', 44.17250000, 26.37028000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2539912'),
(94571, 'Hudeşti', 4740, 'BT', 181, 'RO', 48.15000000, 26.50000000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q2715928'),
(94572, 'Huedin', 4734, 'CJ', 181, 'RO', 46.86667000, 23.05000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2715928'),
(94573, 'Hulubeşti', 4745, 'DB', 181, 'RO', 44.83333000, 25.23333000, '2019-10-05 23:15:22', '2020-05-01 17:23:09', 1, 'Q937219'),
(94574, 'Hulubești', 4726, 'GR', 181, 'RO', 44.16748000, 25.94146000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q937219'),
(94575, 'Humele', 4722, 'AG', 181, 'RO', 44.52612000, 24.97714000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12168014'),
(94576, 'Humoreni', 4720, 'SV', 181, 'RO', 47.66667000, 25.98333000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12168015'),
(94577, 'Humulești', 4731, 'NT', 181, 'RO', 47.19846000, 26.35273000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q12168018'),
(94578, 'Hunedoara', 4721, 'HD', 181, 'RO', 45.75000000, 22.90000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12168018'),
(94579, 'Hunia', 4742, 'DJ', 181, 'RO', 44.05532000, 23.09893000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12168022'),
(94580, 'Hurdugi', 4752, 'VS', 181, 'RO', 46.50778000, 28.05472000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12168027'),
(94581, 'Hurezani', 4750, 'GJ', 181, 'RO', 44.80000000, 23.65000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2718025'),
(94582, 'Hurjuieni', 4720, 'SV', 181, 'RO', 47.91667000, 25.81667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12168031'),
(94583, 'Huruiești', 4744, 'BC', 181, 'RO', 46.26667000, 27.25000000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q15662679'),
(94584, 'Husasău de Tinca', 4723, 'BH', 181, 'RO', 46.81667000, 21.91667000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q1194302'),
(94585, 'Husnicioara', 4751, 'MH', 181, 'RO', 44.67806000, 22.84250000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12168035'),
(94586, 'Huta Certeze', 4746, 'SM', 181, 'RO', 47.92583000, 23.48715000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q741529'),
(94587, 'Huşi', 4752, 'VS', 181, 'RO', 46.67361000, 28.05944000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q741529'),
(94588, 'Hârlău', 4735, 'IS', 181, 'RO', 47.43333000, 26.90000000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q741529'),
(94589, 'Hârseşti', 4722, 'AG', 181, 'RO', 44.53333000, 24.78333000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q1035798'),
(94590, 'Hârtieşti', 4722, 'AG', 181, 'RO', 45.11667000, 25.10000000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q2486688'),
(94591, 'Hârtoape', 4735, 'IS', 181, 'RO', 47.32164000, 26.72459000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q12167263'),
(94592, 'Hârtop', 4720, 'SV', 181, 'RO', 47.49000000, 26.37226000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q2720373'),
(94593, 'Hârşeni', 4759, 'BV', 181, 'RO', 45.75000000, 25.01667000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q15256020'),
(94594, 'Hârşova', 4737, 'CT', 181, 'RO', 44.68555000, 27.95009000, '2019-10-05 23:15:22', '2020-05-01 17:23:09', 1, 'Q15256020'),
(94595, 'Hăbeni', 4745, 'DB', 181, 'RO', 44.85629000, 25.62873000, '2019-10-05 23:15:22', '2020-05-01 17:23:09', 1, 'Q12167127'),
(94596, 'Hăghiac', 4744, 'BC', 181, 'RO', 46.31200000, 26.51841000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q12167133'),
(94597, 'Hăghig', 4754, 'CV', 181, 'RO', 45.83333000, 25.58333000, '2019-10-05 23:15:22', '2020-05-01 17:23:09', 1, 'Q19025'),
(94598, 'Hălchiu', 4759, 'BV', 181, 'RO', 45.76667000, 25.55000000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q15244568'),
(94599, 'Hălmagiu', 4739, 'AR', 181, 'RO', 46.26667000, 22.58333000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q1059026'),
(94600, 'Hălmăgel', 4739, 'AR', 181, 'RO', 46.26667000, 22.61667000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q1069063'),
(94601, 'Hălărești', 4752, 'VS', 181, 'RO', 46.39447000, 27.55614000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q12167144'),
(94602, 'Hălăuceşti', 4735, 'IS', 181, 'RO', 47.10000000, 26.80000000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q2607406'),
(94603, 'Hăneşti', 4740, 'BT', 181, 'RO', 47.91667000, 26.98333000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q2715903'),
(94604, 'Hănțești', 4720, 'SV', 181, 'RO', 47.75507000, 26.37366000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q2719685'),
(94605, 'Hărman', 4759, 'BV', 181, 'RO', 45.71667000, 25.68333000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q15244612'),
(94606, 'Hărmăneasa', 4735, 'IS', 181, 'RO', 47.20000000, 26.86667000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q12167183'),
(94607, 'Hărmăneștii Vechi', 4735, 'IS', 181, 'RO', 47.27732000, 26.81394000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q12167179'),
(94608, 'Hărău', 4721, 'HD', 181, 'RO', 45.90000000, 22.95000000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q5065504'),
(94609, 'Hărțăgani', 4721, 'HD', 181, 'RO', 46.05012000, 22.92183000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q588775'),
(94610, 'Hășmaș', 4739, 'AR', 181, 'RO', 46.50000000, 22.08333000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q1417304'),
(94611, 'I. C. Brătianu', 4727, 'TL', 181, 'RO', 45.40581000, 28.05381000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q916208'),
(94612, 'I. L. Caragiale', 4745, 'DB', 181, 'RO', 44.90963000, 25.70251000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q3726467'),
(94613, 'Iablaniţa', 4753, 'CS', 181, 'RO', 44.95028000, 22.31417000, '2019-10-05 23:15:22', '2020-05-01 17:23:09', 1, 'Q75513'),
(94614, 'Iacobeni', 4740, 'BT', 181, 'RO', 47.86115000, 26.91304000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12173478'),
(94615, 'Iacobeni', 4720, 'SV', 181, 'RO', 47.43333000, 25.31667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1065619'),
(94616, 'Iacobeni', 4755, 'SB', 181, 'RO', 46.05000000, 24.71667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1059402'),
(94617, 'Iadăra', 4760, 'MM', 181, 'RO', 47.47927000, 23.39893000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q725426'),
(94618, 'Iana', 4752, 'VS', 181, 'RO', 46.38333000, 27.55000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2547430'),
(94619, 'Ianca', 4738, 'OT', 181, 'RO', 43.78333000, 24.18333000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q767526'),
(94620, 'Ianca', 4736, 'BR', 181, 'RO', 45.13333000, 27.45000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q767526'),
(94621, 'Iancu Jianu', 4738, 'OT', 181, 'RO', 44.50000000, 24.03333000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2549867'),
(94622, 'Ianoșda', 4723, 'BH', 181, 'RO', 46.83791000, 21.80778000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q741831'),
(94623, 'Iapa', 4760, 'MM', 181, 'RO', 47.92513000, 23.82539000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q741831'),
(94624, 'Iara', 4734, 'CJ', 181, 'RO', 46.55000000, 23.51667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1696811'),
(94625, 'Iaslovăț', 4720, 'SV', 181, 'RO', 47.75862000, 25.97742000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q2720350'),
(94626, 'Iazu', 4745, 'DB', 181, 'RO', 44.71764000, 25.83119000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12173410'),
(94627, 'Iazu', 4743, 'IL', 181, 'RO', 44.73120000, 27.42074000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12173412'),
(94628, 'Iazu Nou', 4735, 'IS', 181, 'RO', 47.48272000, 27.20913000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12173411'),
(94629, 'Iazurile', 4727, 'TL', 181, 'RO', 45.02162000, 28.94281000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12173414'),
(94630, 'Iaşi', 4735, 'IS', 181, 'RO', 47.16667000, 27.60000000, '2019-10-05 23:15:22', '2020-05-01 17:23:10', 1, 'Q16898350'),
(94631, 'Ibrianu', 4745, 'DB', 181, 'RO', 44.77509000, 25.84375000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12073547'),
(94632, 'Ibăneşti', 4740, 'BT', 181, 'RO', 48.06667000, 26.36667000, '2019-10-05 23:15:22', '2020-05-01 17:23:08', 1, 'Q2534799'),
(94633, 'Ibănești', 4752, 'VS', 181, 'RO', 46.39482000, 27.62548000, '2019-10-05 23:15:22', '2020-05-01 17:23:11', 1, 'Q2547382'),
(94634, 'Iclod', 4734, 'CJ', 181, 'RO', 46.98333000, 23.80000000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q1076054'),
(94635, 'Icoana', 4738, 'OT', 181, 'RO', 44.41667000, 24.71667000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q2719556'),
(94636, 'Icoana', 4726, 'GR', 181, 'RO', 44.49309000, 25.74623000, '2019-10-05 23:15:22', '2019-10-05 23:15:22', 1, 'Q12074144'),
(94637, 'Icuseşti', 4731, 'NT', 181, 'RO', 46.80000000, 26.93333000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q2605667'),
(94638, 'Icușeni', 4740, 'BT', 181, 'RO', 47.59269000, 26.59605000, '2019-10-05 23:15:23', '2020-05-01 17:23:08', 1, 'Q12074161'),
(94639, 'Iecea Mare', 4748, 'TM', 181, 'RO', 45.85074000, 20.89310000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q984235'),
(94640, 'Iecea Mică', 4748, 'TM', 181, 'RO', 45.82147000, 20.92322000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q848594'),
(94641, 'Iedera de Jos', 4745, 'DB', 181, 'RO', 45.03333000, 25.63333000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12073218'),
(94642, 'Iedera de Sus', 4745, 'DB', 181, 'RO', 45.04174000, 25.63084000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12073217'),
(94643, 'Iepureşti', 4726, 'GR', 181, 'RO', 44.25806000, 25.88222000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q3786792'),
(94644, 'Ieud', 4760, 'MM', 181, 'RO', 47.67796000, 24.23399000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q739611'),
(94645, 'Iezeru', 4732, 'CL', 181, 'RO', 44.28617000, 27.62447000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12073261'),
(94646, 'Igești', 4758, 'VN', 181, 'RO', 45.82043000, 27.18253000, '2019-10-05 23:15:23', '2020-05-01 17:23:12', 1, 'Q12073957'),
(94647, 'Ighiel', 4724, 'AB', 181, 'RO', 46.14680000, 23.47968000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q723969'),
(94648, 'Ighiu', 4724, 'AB', 181, 'RO', 46.15000000, 23.51667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1022813'),
(94649, 'Ighișu Nou', 4755, 'SB', 181, 'RO', 46.11121000, 24.34920000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q845488'),
(94650, 'Igneşti', 4739, 'AR', 181, 'RO', 46.40000000, 22.16667000, '2019-10-05 23:15:23', '2020-05-01 17:23:08', 1, 'Q1069480'),
(94651, 'Igoiu', 4757, 'VL', 181, 'RO', 45.04824000, 23.80835000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12073913'),
(94652, 'Igriș', 4748, 'TM', 181, 'RO', 46.11313000, 20.78496000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q630837'),
(94653, 'Ilba', 4760, 'MM', 181, 'RO', 47.71280000, 23.35765000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q725179'),
(94654, 'Ileana', 4732, 'CL', 181, 'RO', 44.51667000, 26.66667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q2537505'),
(94655, 'Ileanda', 4741, 'SJ', 181, 'RO', 47.33333000, 23.63333000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1093266'),
(94656, 'Ilia', 4721, 'HD', 181, 'RO', 45.93333000, 22.65000000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q5062445'),
(94657, 'Ilieni', 4754, 'CV', 181, 'RO', 45.80000000, 25.76667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1096328'),
(94658, 'Ilișești', 4720, 'SV', 181, 'RO', 47.60878000, 26.05061000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q1065993'),
(94659, 'Iloviţa', 4751, 'MH', 181, 'RO', 44.75639000, 22.47278000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q2026105'),
(94660, 'Ilovăț', 4751, 'MH', 181, 'RO', 44.81197000, 22.76333000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q2465591'),
(94661, 'Ilva Mare', 4733, 'BN', 181, 'RO', 47.36667000, 24.90000000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1188912'),
(94662, 'Ilva Mică', 4733, 'BN', 181, 'RO', 47.31667000, 24.66667000, '2019-10-05 23:15:23', '2020-05-01 17:23:08', 1, 'Q682807'),
(94663, 'Imper', 4749, 'HR', 181, 'RO', 46.21667000, 26.10000000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q714035'),
(94664, 'Independenţa', 4737, 'CT', 181, 'RO', 43.96667000, 28.08333000, '2019-10-05 23:15:23', '2020-05-01 17:23:09', 1, 'Q1091607'),
(94665, 'Independenţa', 4747, 'GL', 181, 'RO', 45.48333000, 27.75000000, '2019-10-05 23:15:23', '2020-05-01 17:23:09', 1, 'Q853739'),
(94666, 'Independenţa', 4732, 'CL', 181, 'RO', 44.28333000, 27.15000000, '2019-10-05 23:15:23', '2020-05-01 17:23:09', 1, 'Q12074444'),
(94667, 'Ineu', 4739, 'AR', 181, 'RO', 46.43372000, 21.84048000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12074444'),
(94668, 'Ineu', 4723, 'BH', 181, 'RO', 47.08333000, 22.11667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1194277'),
(94669, 'Ineu', 4749, 'HR', 181, 'RO', 46.54530000, 25.76357000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q829853'),
(94670, 'Inotești', 4729, 'PH', 181, 'RO', 44.96146000, 26.35782000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q12127833'),
(94671, 'Intregalde', 4724, 'AB', 181, 'RO', 46.25000000, 23.41667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q980510'),
(94672, 'Iojib', 4746, 'SM', 181, 'RO', 47.81617000, 23.15603000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q724021'),
(94673, 'Ion Corvin', 4737, 'CT', 181, 'RO', 44.11667000, 27.80000000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1091887'),
(94674, 'Ion Creangă', 4731, 'NT', 181, 'RO', 46.87029000, 26.98023000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q2541062'),
(94675, 'Ion Neculce', 4735, 'IS', 181, 'RO', 47.20344000, 27.05268000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q2718492'),
(94676, 'Ion Roată', 4743, 'IL', 181, 'RO', 44.66667000, 26.76667000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q12074904'),
(94677, 'Ioneşti', 4750, 'GJ', 181, 'RO', 44.61667000, 23.43333000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q537298'),
(94678, 'Ioneşti', 4757, 'VL', 181, 'RO', 44.85000000, 24.23333000, '2019-10-05 23:15:23', '2020-05-01 17:23:12', 1, 'Q2297612'),
(94679, 'Ionești', 4745, 'DB', 181, 'RO', 44.69374000, 25.27780000, '2019-10-05 23:15:23', '2020-05-01 17:23:09', 1, 'Q12074919'),
(94680, 'Ionășeni', 4740, 'BT', 181, 'RO', 47.73029000, 26.99329000, '2019-10-05 23:15:23', '2020-05-01 17:23:08', 1, 'Q12074909'),
(94681, 'Iordăcheanu', 4729, 'PH', 181, 'RO', 45.04392000, 26.23920000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q14079750'),
(94682, 'Ip', 4741, 'SJ', 181, 'RO', 47.23333000, 22.65000000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1027794'),
(94683, 'Ipatele', 4735, 'IS', 181, 'RO', 46.91667000, 27.41667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q2605974'),
(94684, 'Ipoteşti', 4720, 'SV', 181, 'RO', 47.61667000, 26.28333000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q2720017'),
(94685, 'Ipotești', 4738, 'OT', 181, 'RO', 44.32529000, 24.40089000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q2549753'),
(94686, 'Iratoşu', 4739, 'AR', 181, 'RO', 46.31667000, 21.20000000, '2019-10-05 23:15:23', '2020-05-01 17:23:08', 1, 'Q1069504'),
(94687, 'Irești', 4758, 'VN', 181, 'RO', 45.92993000, 26.94393000, '2019-10-05 23:15:23', '2020-05-01 17:23:12', 1, 'Q12074949'),
(94688, 'Isaccea', 4727, 'TL', 181, 'RO', 45.26667000, 28.46667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12074949'),
(94689, 'Iscroni', 4721, 'HD', 181, 'RO', 45.37794000, 23.34201000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q663438'),
(94690, 'Islaz', 4725, 'IF', 181, 'RO', 44.46117000, 26.38724000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12075057'),
(94691, 'Islaz', 4728, 'TR', 181, 'RO', 43.72006000, 24.76436000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q285442'),
(94692, 'Istria', 4737, 'CT', 181, 'RO', 44.56667000, 28.71667000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q1079468'),
(94693, 'Isverna', 4751, 'MH', 181, 'RO', 44.97972000, 22.62917000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q305238'),
(94694, 'Iteşti', 4744, 'BC', 181, 'RO', 46.65309000, 26.87256000, '2019-10-05 23:15:23', '2020-05-01 17:23:08', 1, 'Q15707854'),
(94695, 'Iugani', 4735, 'IS', 181, 'RO', 47.04431000, 26.82891000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q12172885'),
(94696, 'Iveşti', 4747, 'GL', 181, 'RO', 45.68333000, 27.51667000, '2019-10-05 23:15:23', '2020-05-01 17:23:09', 1, 'Q2289283'),
(94697, 'Iveşti', 4752, 'VS', 181, 'RO', 46.18333000, 27.53333000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q923575'),
(94698, 'Ivănești', 4752, 'VS', 181, 'RO', 46.63481000, 27.46048000, '2019-10-05 23:15:23', '2020-05-01 17:23:11', 1, 'Q2724536'),
(94699, 'Izbiceni', 4738, 'OT', 181, 'RO', 43.83333000, 24.65000000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q2549571'),
(94700, 'Izimșa', 4751, 'MH', 181, 'RO', 44.17819000, 22.95649000, '2019-10-05 23:15:23', '2020-05-01 17:23:10', 1, 'Q12074138'),
(94701, 'Izvin', 4748, 'TM', 181, 'RO', 45.80034000, 21.46066000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q673594'),
(94702, 'Izvoare', 4742, 'DJ', 181, 'RO', 44.14773000, 23.29582000, '2019-10-05 23:15:23', '2019-10-05 23:15:23', 1, 'Q2493354'),
(94703, 'Izvoare', 4731, 'NT', 181, 'RO', 46.74699000, 26.80221000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074005'),
(94704, 'Izvoarele', 4738, 'OT', 181, 'RO', 44.26667000, 24.51667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2719609'),
(94705, 'Izvoarele', 4745, 'DB', 181, 'RO', 45.03333000, 25.26667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074016'),
(94706, 'Izvoarele', 4735, 'IS', 181, 'RO', 47.04241000, 26.90230000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074020'),
(94707, 'Izvoarele', 4726, 'GR', 181, 'RO', 44.03389000, 25.77472000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12731247'),
(94708, 'Izvoarele', 4727, 'TL', 181, 'RO', 45.03333000, 28.53333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1091573'),
(94709, 'Izvoarele', 4751, 'MH', 181, 'RO', 44.30958000, 22.66348000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074017'),
(94710, 'Izvoarele', 4724, 'AB', 181, 'RO', 46.17011000, 23.90377000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074017'),
(94711, 'Izvoarele', 4728, 'TR', 181, 'RO', 43.81667000, 25.38333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2721090'),
(94712, 'Izvoarele', 4729, 'PH', 181, 'RO', 45.28376000, 26.00119000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1099012'),
(94713, 'Izvoarele Sucevei', 4720, 'SV', 181, 'RO', 47.75000000, 25.18333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1089013'),
(94714, 'Izvoru', 4756, 'BZ', 181, 'RO', 45.19249000, 26.59154000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074057'),
(94715, 'Izvoru', 4722, 'AG', 181, 'RO', 44.49414000, 25.06470000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2534649'),
(94716, 'Izvoru Berheciului', 4744, 'BC', 181, 'RO', 46.58333000, 27.21667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15707977'),
(94717, 'Izvoru Bârzii', 4751, 'MH', 181, 'RO', 44.70667000, 22.67417000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q660169'),
(94718, 'Izvoru Crişului', 4734, 'CJ', 181, 'RO', 46.83333000, 23.10000000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q911307'),
(94719, 'Izvoru Dulce', 4756, 'BZ', 181, 'RO', 45.34178000, 26.77730000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074035'),
(94720, 'Izvoru de Sus', 4722, 'AG', 181, 'RO', 44.81667000, 24.65000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12074045'),
(94721, 'Işalniţa', 4742, 'DJ', 181, 'RO', 44.40000000, 23.73333000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q2248972'),
(94722, 'Jamu Mare', 4748, 'TM', 181, 'RO', 45.25417000, 21.41694000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1020863'),
(94723, 'Jariştea', 4758, 'VN', 181, 'RO', 45.78333000, 27.06667000, '2019-10-05 23:15:24', '2020-05-01 17:23:12', 1, 'Q14029104'),
(94724, 'Jebel', 4748, 'TM', 181, 'RO', 45.55556000, 21.21361000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1076084'),
(94725, 'Jegălia', 4732, 'CL', 181, 'RO', 44.30000000, 27.63333000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q12724887'),
(94726, 'Jiana', 4751, 'MH', 181, 'RO', 44.40184000, 22.71278000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2164070'),
(94727, 'Jiana Veche', 4751, 'MH', 181, 'RO', 44.39083000, 22.66861000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12104926'),
(94728, 'Jibert', 4759, 'BV', 181, 'RO', 46.00000000, 25.06667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15256295'),
(94729, 'Jiblea Veche', 4757, 'VL', 181, 'RO', 45.24360000, 24.35163000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15256295'),
(94730, 'Jibou', 4741, 'SJ', 181, 'RO', 47.25896000, 23.25651000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15256295'),
(94731, 'Jichişu de Jos', 4734, 'CJ', 181, 'RO', 47.11667000, 23.80000000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q535465'),
(94732, 'Jidoștița', 4751, 'MH', 181, 'RO', 44.71582000, 22.59311000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q12104500'),
(94733, 'Jidvei', 4724, 'AB', 181, 'RO', 46.21667000, 24.10000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q937616'),
(94734, 'Jieni', 4738, 'OT', 181, 'RO', 43.95095000, 24.59349000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12104946'),
(94735, 'Jijila', 4727, 'TL', 181, 'RO', 45.30000000, 28.15000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1066631'),
(94736, 'Jilava', 4725, 'IF', 181, 'RO', 44.33333000, 26.07806000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1019174'),
(94737, 'Jilavele', 4743, 'IL', 181, 'RO', 44.76667000, 26.53333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2717420'),
(94738, 'Jimbolia', 4748, 'TM', 181, 'RO', 45.79139000, 20.71722000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q16898597'),
(94739, 'Jina', 4755, 'SB', 181, 'RO', 45.78333000, 23.68333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q936156'),
(94740, 'Jirlău', 4736, 'BR', 181, 'RO', 45.16667000, 27.16667000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q12104547'),
(94741, 'Jirov', 4751, 'MH', 181, 'RO', 44.67098000, 23.08693000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12104549'),
(94742, 'Jitia', 4758, 'VN', 181, 'RO', 45.58333000, 26.71667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q14029752'),
(94743, 'Jiu-Paroșeni', 4721, 'HD', 181, 'RO', 45.36175000, 23.26044000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q14029752'),
(94744, 'Joiţa', 4726, 'GR', 181, 'RO', 44.49417000, 25.85389000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q2717566'),
(94745, 'Joldești', 4740, 'BT', 181, 'RO', 47.56004000, 26.57705000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q12104752'),
(94746, 'Jorăşti', 4747, 'GL', 181, 'RO', 45.98333000, 27.86667000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q2716861'),
(94747, 'Jorăști', 4758, 'VN', 181, 'RO', 45.71477000, 27.26335000, '2019-10-05 23:15:24', '2020-05-01 17:23:12', 1, 'Q12104774'),
(94748, 'Joseni', 4749, 'HR', 181, 'RO', 46.70000000, 25.50000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1020617'),
(94749, 'Josenii Bârgăului', 4733, 'BN', 181, 'RO', 47.21667000, 24.68333000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q663704'),
(94750, 'Jugur', 4722, 'AG', 181, 'RO', 45.19255000, 25.08493000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12104795'),
(94751, 'Jugureanu', 4736, 'BR', 181, 'RO', 44.94313000, 27.27878000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12104798'),
(94752, 'Jugureni', 4729, 'PH', 181, 'RO', 45.10000000, 26.45000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q14089457'),
(94753, 'Jupâneşti', 4750, 'GJ', 181, 'RO', 44.90000000, 23.53333000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q2539296'),
(94754, 'Jupânești', 4722, 'AG', 181, 'RO', 45.06667000, 24.85000000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q12104862'),
(94755, 'Jurilovca', 4727, 'TL', 181, 'RO', 44.76667000, 28.86667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1091518'),
(94756, 'Lacu Sărat', 4736, 'BR', 181, 'RO', 45.21867000, 27.88764000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q576333'),
(94757, 'Laloşu', 4757, 'VL', 181, 'RO', 44.55000000, 24.01667000, '2019-10-05 23:15:24', '2020-05-01 17:23:12', 1, 'Q2008165'),
(94758, 'Lancrăm', 4724, 'AB', 181, 'RO', 45.98708000, 23.55665000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q835580'),
(94759, 'Lanurile', 4736, 'BR', 181, 'RO', 45.04149000, 27.74916000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12117079'),
(94760, 'Lapoș', 4729, 'PH', 181, 'RO', 45.15197000, 26.41911000, '2019-10-05 23:15:24', '2020-05-01 17:23:11', 1, 'Q14193660'),
(94761, 'Larga', 4744, 'BC', 181, 'RO', 46.35060000, 26.53321000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12117130'),
(94762, 'Largu', 4756, 'BZ', 181, 'RO', 44.96667000, 27.15000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q3293875'),
(94763, 'Laslea', 4755, 'SB', 181, 'RO', 46.21667000, 24.65000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1019416'),
(94764, 'Laza', 4752, 'VS', 181, 'RO', 46.65000000, 27.58333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1020234'),
(94765, 'Lazuri', 4745, 'DB', 181, 'RO', 44.87859000, 25.54964000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12116974'),
(94766, 'Lazuri', 4746, 'SM', 181, 'RO', 47.85000000, 22.86667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1076447'),
(94767, 'Lazuri de Beiuş', 4723, 'BH', 181, 'RO', 46.58333000, 22.40000000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q1194284'),
(94768, 'Lechinţa', 4733, 'BN', 181, 'RO', 47.01667000, 24.35000000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q1811336'),
(94769, 'Lehliu', 4732, 'CL', 181, 'RO', 44.46667000, 26.81667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12117798'),
(94770, 'Lehliu-Gară', 4732, 'CL', 181, 'RO', 44.43333000, 26.85000000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q12117798'),
(94771, 'Leicești', 4722, 'AG', 181, 'RO', 45.09586000, 24.86108000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q12117525'),
(94772, 'Leleasca', 4738, 'OT', 181, 'RO', 44.78333000, 24.43333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2719308'),
(94773, 'Lelese', 4721, 'HD', 181, 'RO', 45.73333000, 22.70000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q5053438'),
(94774, 'Leleşti', 4750, 'GJ', 181, 'RO', 45.10000000, 23.20000000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q2717901'),
(94775, 'Leliceni', 4749, 'HR', 181, 'RO', 46.34713000, 25.84782000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q876066'),
(94776, 'Lemnia', 4754, 'CV', 181, 'RO', 46.05000000, 26.26667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q19416'),
(94777, 'Lenauheim', 4748, 'TM', 181, 'RO', 45.87194000, 20.79944000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q870864'),
(94778, 'Leorda', 4740, 'BT', 181, 'RO', 47.81667000, 26.45000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2715880'),
(94779, 'Leordeni', 4722, 'AG', 181, 'RO', 44.78333000, 25.11667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2296585'),
(94780, 'Leordina', 4760, 'MM', 181, 'RO', 47.78333000, 24.25000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q181527'),
(94781, 'Lereşti', 4722, 'AG', 181, 'RO', 45.33333000, 25.06667000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q2348314'),
(94782, 'Lespezi', 4735, 'IS', 181, 'RO', 47.36667000, 26.70000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2469527'),
(94783, 'Lespezi', 4758, 'VN', 181, 'RO', 46.16027000, 27.24065000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12117739'),
(94784, 'Lespezi', 4744, 'BC', 181, 'RO', 46.66493000, 26.76882000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15929729'),
(94785, 'Letca', 4741, 'SJ', 181, 'RO', 47.33333000, 23.45000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1191363'),
(94786, 'Letca Nouă', 4726, 'GR', 181, 'RO', 44.23496000, 25.74262000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q2717559'),
(94787, 'Letca Veche', 4726, 'GR', 181, 'RO', 44.19911000, 25.68681000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12117773'),
(94788, 'Letea Veche', 4744, 'BC', 181, 'RO', 46.55000000, 26.95000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15709454'),
(94789, 'Leu', 4742, 'DJ', 181, 'RO', 44.18333000, 24.00000000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1967877'),
(94790, 'Leşu', 4733, 'BN', 181, 'RO', 47.31667000, 24.75000000, '2019-10-05 23:15:24', '2020-05-01 17:23:08', 1, 'Q1129326'),
(94791, 'Leţcani', 4735, 'IS', 181, 'RO', 47.18333000, 27.41667000, '2019-10-05 23:15:24', '2020-05-01 17:23:10', 1, 'Q2607493'),
(94792, 'Libotin', 4760, 'MM', 181, 'RO', 47.50541000, 23.96833000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q755576'),
(94793, 'Licurici', 4750, 'GJ', 181, 'RO', 44.91667000, 23.61667000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2718354'),
(94794, 'Liebling', 4748, 'TM', 181, 'RO', 45.57750000, 21.32167000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1093025'),
(94795, 'Lieşti', 4747, 'GL', 181, 'RO', 45.61667000, 27.51667000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q957694'),
(94796, 'Lilieci', 4744, 'BC', 181, 'RO', 46.62861000, 26.87176000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12119675'),
(94797, 'Liliești', 4729, 'PH', 181, 'RO', 45.03458000, 25.88554000, '2019-10-05 23:15:24', '2020-05-01 17:23:11', 1, 'Q12119675'),
(94798, 'Limanu', 4737, 'CT', 181, 'RO', 43.80000000, 28.53333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q1079477'),
(94799, 'Limpeziș', 4756, 'BZ', 181, 'RO', 44.95357000, 26.70490000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q12119683'),
(94800, 'Lipia', 4725, 'IF', 181, 'RO', 44.71003000, 26.26445000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12119795'),
(94801, 'Lipia', 4756, 'BZ', 181, 'RO', 45.13970000, 26.73505000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q12119796'),
(94802, 'Lipniţa', 4737, 'CT', 181, 'RO', 44.10000000, 27.60000000, '2019-10-05 23:15:24', '2020-05-01 17:23:09', 1, 'Q1130294'),
(94803, 'Lipova', 4744, 'BC', 181, 'RO', 46.71667000, 27.23333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15709644'),
(94804, 'Lipova', 4739, 'AR', 181, 'RO', 46.09085000, 21.69628000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q15709644'),
(94805, 'Lipovu', 4742, 'DJ', 181, 'RO', 44.10000000, 23.63333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q670699'),
(94806, 'Lipovăţ', 4752, 'VS', 181, 'RO', 46.56667000, 27.70000000, '2019-10-05 23:15:24', '2020-05-01 17:23:11', 1, 'Q2547280'),
(94807, 'Lipăneşti', 4729, 'PH', 181, 'RO', 45.05000000, 26.01667000, '2019-10-05 23:15:24', '2020-05-01 17:23:11', 1, 'Q14193673'),
(94808, 'Lisa', 4728, 'TR', 181, 'RO', 43.80000000, 25.13333000, '2019-10-05 23:15:24', '2019-10-05 23:15:24', 1, 'Q2721056'),
(94809, 'Lisa', 4759, 'BV', 181, 'RO', 45.71667000, 24.85000000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q671987'),
(94810, 'Liteni', 4735, 'IS', 181, 'RO', 47.30169000, 27.03837000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12119910'),
(94811, 'Liteni', 4720, 'SV', 181, 'RO', 47.56667000, 26.20000000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12119909'),
(94812, 'Liubcova', 4753, 'CS', 181, 'RO', 44.65885000, 21.89559000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q678309'),
(94813, 'Livada', 4746, 'SM', 181, 'RO', 47.86667000, 23.13333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q678309'),
(94814, 'Livada', 4739, 'AR', 181, 'RO', 46.22284000, 21.38403000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q995708'),
(94815, 'Livada de Bihor', 4723, 'BH', 181, 'RO', 47.01039000, 21.80836000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q645761'),
(94816, 'Livadea', 4729, 'PH', 181, 'RO', 45.18663000, 25.92805000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12119544'),
(94817, 'Livadia', 4757, 'VL', 181, 'RO', 45.20546000, 24.24192000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12119544'),
(94818, 'Livezeni', 4722, 'AG', 181, 'RO', 45.03838000, 24.97676000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12119555'),
(94819, 'Livezi', 4757, 'VL', 181, 'RO', 44.84029000, 23.82505000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12119556'),
(94820, 'Livezi', 4744, 'BC', 181, 'RO', 46.40639000, 26.73613000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q15709692'),
(94821, 'Livezi-Vale', 4744, 'BC', 181, 'RO', 46.41667000, 26.73333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q15709692'),
(94822, 'Livezile', 4751, 'MH', 181, 'RO', 44.51222000, 22.86333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q2465443'),
(94823, 'Livezile', 4724, 'AB', 181, 'RO', 46.35000000, 23.63333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q937028'),
(94824, 'Livezile', 4748, 'TM', 181, 'RO', 45.39026000, 21.05794000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1089925'),
(94825, 'Livezile', 4733, 'BN', 181, 'RO', 47.18333000, 24.56667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1092983'),
(94826, 'Liţa', 4728, 'TR', 181, 'RO', 43.80000000, 24.81667000, '2019-10-05 23:15:25', '2020-05-01 17:23:11', 1, 'Q2549875'),
(94827, 'Lișteava', 4742, 'DJ', 181, 'RO', 43.83418000, 23.92022000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q12120103'),
(94828, 'Loamneş', 4755, 'SB', 181, 'RO', 45.93333000, 24.10000000, '2019-10-05 23:15:25', '2020-05-01 17:23:11', 1, 'Q1077212'),
(94829, 'Logreşti', 4750, 'GJ', 181, 'RO', 44.90000000, 23.70000000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q1077212'),
(94830, 'Logrești Moșteni', 4750, 'GJ', 181, 'RO', 44.90663000, 23.70480000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q12118262'),
(94831, 'Loloiasca', 4729, 'PH', 181, 'RO', 44.96808000, 26.29141000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118371'),
(94832, 'Lopadea Nouă', 4724, 'AB', 181, 'RO', 46.29090000, 23.81901000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q937029'),
(94833, 'Lopătari', 4756, 'BZ', 181, 'RO', 45.48333000, 26.58333000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q3293693'),
(94834, 'Lovrin', 4748, 'TM', 181, 'RO', 45.96806000, 20.77028000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q671395'),
(94835, 'Lozna', 4741, 'SJ', 181, 'RO', 47.31667000, 23.46667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1081303'),
(94836, 'Lozna', 4740, 'BT', 181, 'RO', 47.95144000, 26.27723000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q2535310'),
(94837, 'Lucieni', 4745, 'DB', 181, 'RO', 44.85000000, 25.43333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q3726412'),
(94838, 'Luciu', 4756, 'BZ', 181, 'RO', 44.96667000, 27.08333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q3293603'),
(94839, 'Luciu', 4743, 'IL', 181, 'RO', 44.74722000, 27.72760000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12119045'),
(94840, 'Lucăceni', 4746, 'SM', 181, 'RO', 47.74194000, 22.47819000, '2019-10-05 23:15:25', '2020-05-01 17:23:11', 1, 'Q754257'),
(94841, 'Ludași', 4744, 'BC', 181, 'RO', 46.61962000, 26.57926000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q12118639'),
(94842, 'Ludeşti', 4745, 'DB', 181, 'RO', 44.86667000, 25.23333000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q3726377'),
(94843, 'Ludoş', 4755, 'SB', 181, 'RO', 45.91667000, 23.90000000, '2019-10-05 23:15:25', '2020-05-01 17:23:11', 1, 'Q1077551'),
(94844, 'Lueta', 4749, 'HR', 181, 'RO', 46.26667000, 25.48333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1090746'),
(94845, 'Lugaşu de Jos', 4723, 'BH', 181, 'RO', 47.06667000, 22.35000000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q1189462'),
(94846, 'Lugoj', 4748, 'TM', 181, 'RO', 45.68861000, 21.90306000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1189462'),
(94847, 'Luica', 4732, 'CL', 181, 'RO', 44.23333000, 26.58333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118672'),
(94848, 'Luizi-Călugăra', 4744, 'BC', 181, 'RO', 46.53333000, 26.83333000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q15709723'),
(94849, 'Lumina', 4737, 'CT', 181, 'RO', 44.28333000, 28.56667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1159853'),
(94850, 'Luminiș', 4731, 'NT', 181, 'RO', 46.81332000, 26.45275000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q12118777'),
(94851, 'Luna', 4734, 'CJ', 181, 'RO', 46.50659000, 23.91959000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q775862'),
(94852, 'Luna de Sus', 4734, 'CJ', 181, 'RO', 46.74313000, 23.43539000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q713910'),
(94853, 'Lunca', 4735, 'IS', 181, 'RO', 47.28144000, 26.73708000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118843'),
(94854, 'Lunca', 4723, 'BH', 181, 'RO', 46.51667000, 22.46667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1195828'),
(94855, 'Lunca', 4731, 'NT', 181, 'RO', 47.22765000, 26.29979000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118829'),
(94856, 'Lunca', 4745, 'DB', 181, 'RO', 45.20022000, 25.44351000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118837'),
(94857, 'Lunca', 4727, 'TL', 181, 'RO', 44.73288000, 28.77495000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118849'),
(94858, 'Lunca', 4756, 'BZ', 181, 'RO', 45.00494000, 27.21335000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118836'),
(94859, 'Lunca', 4740, 'BT', 181, 'RO', 47.61667000, 26.98333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q2715940'),
(94860, 'Lunca', 4728, 'TR', 181, 'RO', 43.83333000, 24.76667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q2720940'),
(94861, 'Lunca Banului', 4752, 'VS', 181, 'RO', 46.59500000, 28.16833000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q2548890'),
(94862, 'Lunca Cernii de Jos', 4721, 'HD', 181, 'RO', 45.63333000, 22.61667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q5054267'),
(94863, 'Lunca Cetățuii', 4735, 'IS', 181, 'RO', 47.09660000, 27.56319000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q12118822'),
(94864, 'Lunca Corbului', 4722, 'AG', 181, 'RO', 44.68391000, 24.75855000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q2348396'),
(94865, 'Lunca Ilvei', 4733, 'BN', 181, 'RO', 47.36667000, 24.98333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1069228'),
(94866, 'Lunca Jariștei', 4756, 'BZ', 181, 'RO', 45.48231000, 26.25483000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q12118806'),
(94867, 'Lunca Leșului', 4733, 'BN', 181, 'RO', 47.30943000, 24.77602000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q12118810'),
(94868, 'Lunca Mureşului', 4724, 'AB', 181, 'RO', 46.43333000, 23.90000000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q949873'),
(94869, 'Lunca Prahovei', 4729, 'PH', 181, 'RO', 45.04714000, 25.77161000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118817'),
(94870, 'Lunca Priporului', 4756, 'BZ', 181, 'RO', 45.43909000, 26.30869000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12118819'),
(94871, 'Lunca de Jos', 4749, 'HR', 181, 'RO', 46.56667000, 25.98333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1020620'),
(94872, 'Lunca de Sus', 4749, 'HR', 181, 'RO', 46.53333000, 25.96667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1029575'),
(94873, 'Lunca la Tisa', 4760, 'MM', 181, 'RO', 47.95213000, 24.05216000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q852327'),
(94874, 'Luncani', 4734, 'CJ', 181, 'RO', 46.47274000, 23.95095000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q714762'),
(94875, 'Luncaviţa', 4753, 'CS', 181, 'RO', 45.08333000, 22.26667000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q771716'),
(94876, 'Luncaviţa', 4727, 'TL', 181, 'RO', 45.28333000, 28.26667000, '2019-10-05 23:15:25', '2020-05-01 17:23:11', 1, 'Q1159915'),
(94877, 'Luncoiu de Jos', 4721, 'HD', 181, 'RO', 46.10000000, 22.76667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q5053377'),
(94878, 'Luncșoara', 4723, 'BH', 181, 'RO', 47.03663000, 22.54213000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q755084'),
(94879, 'Lunga', 4754, 'CV', 181, 'RO', 46.01822000, 26.21208000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q714128'),
(94880, 'Lungani', 4735, 'IS', 181, 'RO', 47.18333000, 27.15000000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1085265'),
(94881, 'Lungeşti', 4757, 'VL', 181, 'RO', 44.56667000, 24.18333000, '2019-10-05 23:15:25', '2020-05-01 17:23:12', 1, 'Q3915152'),
(94882, 'Lunguleţu', 4745, 'DB', 181, 'RO', 44.61667000, 25.65000000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q12118784'),
(94883, 'Lupac', 4753, 'CS', 181, 'RO', 45.28000000, 21.81278000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q426370'),
(94884, 'Lupeni', 4721, 'HD', 181, 'RO', 45.36029000, 23.23832000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q16898309'),
(94885, 'Lupeni', 4749, 'HR', 181, 'RO', 46.38333000, 25.21667000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q996501'),
(94886, 'Lupşa', 4724, 'AB', 181, 'RO', 46.36667000, 23.20000000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q913412'),
(94887, 'Lupşanu', 4732, 'CL', 181, 'RO', 44.38333000, 26.90000000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q12118913'),
(94888, 'Lăceni', 4728, 'TR', 181, 'RO', 44.08466000, 25.33270000, '2019-10-05 23:15:25', '2020-05-01 17:23:11', 1, 'Q12117806'),
(94889, 'Lăculețe', 4745, 'DB', 181, 'RO', 45.01837000, 25.43108000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q12117542'),
(94890, 'Lăcusteni', 4743, 'IL', 181, 'RO', 44.60715000, 27.67569000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q3915262'),
(94891, 'Lăcusteni', 4757, 'VL', 181, 'RO', 44.69544000, 23.89984000, '2019-10-05 23:15:25', '2020-05-01 17:23:12', 1, 'Q2040535'),
(94892, 'Lădeşti', 4757, 'VL', 181, 'RO', 44.88333000, 24.05000000, '2019-10-05 23:15:25', '2020-05-01 17:23:12', 1, 'Q12117482'),
(94893, 'Lămășeni', 4720, 'SV', 181, 'RO', 47.49322000, 26.22806000, '2019-10-05 23:15:25', '2020-05-01 17:23:11', 1, 'Q12117578'),
(94894, 'Lăpoș', 4744, 'BC', 181, 'RO', 46.39363000, 26.46245000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q12117110'),
(94895, 'Lăpugiu de Jos', 4721, 'HD', 181, 'RO', 45.88333000, 22.48333000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q5064375'),
(94896, 'Lăpuş', 4760, 'MM', 181, 'RO', 47.50000000, 24.01667000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q1079866'),
(94897, 'Lăpuşnicel', 4753, 'CS', 181, 'RO', 44.98306000, 22.22694000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q75602'),
(94898, 'Lăpuşnicu Mare', 4753, 'CS', 181, 'RO', 44.91306000, 21.94417000, '2019-10-05 23:15:25', '2020-05-01 17:23:09', 1, 'Q75490'),
(94899, 'Lăpușel', 4760, 'MM', 181, 'RO', 47.61830000, 23.47913000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q371262'),
(94900, 'Lăzarea', 4749, 'HR', 181, 'RO', 46.75000000, 25.53333000, '2019-10-05 23:15:25', '2020-05-01 17:23:10', 1, 'Q1029395'),
(94901, 'Lăzăreni', 4723, 'BH', 181, 'RO', 46.86667000, 22.06667000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q1194411'),
(94902, 'Lăzărești', 4722, 'AG', 181, 'RO', 45.15659000, 24.99540000, '2019-10-05 23:15:25', '2020-05-01 17:23:08', 1, 'Q12117500'),
(94903, 'Macea', 4739, 'AR', 181, 'RO', 46.38333000, 21.30000000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q1066818'),
(94904, 'Maglavit', 4742, 'DJ', 181, 'RO', 44.03333000, 23.10000000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q731519'),
(94905, 'Magula', 4729, 'PH', 181, 'RO', 44.93185000, 26.30114000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q12120297'),
(94906, 'Mahmudia', 4727, 'TL', 181, 'RO', 45.08333000, 29.08333000, '2019-10-05 23:15:25', '2019-10-05 23:15:25', 1, 'Q970516'),
(94907, 'Maia', 4743, 'IL', 181, 'RO', 44.73616000, 26.40151000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2539262'),
(94908, 'Mailat', 4739, 'AR', 181, 'RO', 46.04189000, 21.10702000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q831635'),
(94909, 'Mal', 4741, 'SJ', 181, 'RO', 47.06979000, 22.81441000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q728877'),
(94910, 'Malaia', 4757, 'VL', 181, 'RO', 45.35000000, 24.03333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12734752'),
(94911, 'Malcoci', 4727, 'TL', 181, 'RO', 45.13851000, 28.88824000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1754872'),
(94912, 'Maliuc', 4727, 'TL', 181, 'RO', 45.17678000, 29.11334000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1066617'),
(94913, 'Malnaş', 4754, 'CV', 181, 'RO', 46.01667000, 25.83333000, '2019-10-05 23:15:26', '2020-05-01 17:23:09', 1, 'Q3282268'),
(94914, 'Malovăţ', 4751, 'MH', 181, 'RO', 44.70444000, 22.73111000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q975394'),
(94915, 'Malu', 4726, 'GR', 181, 'RO', 43.81518000, 25.81863000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2291392'),
(94916, 'Malu Mare', 4742, 'DJ', 181, 'RO', 44.24182000, 23.85321000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1667944'),
(94917, 'Malu Roșu', 4743, 'IL', 181, 'RO', 44.78088000, 26.57945000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q12120973'),
(94918, 'Malu Spart', 4726, 'GR', 181, 'RO', 44.44391000, 25.71758000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12120975'),
(94919, 'Malu cu Flori', 4745, 'DB', 181, 'RO', 45.15000000, 25.20000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2606390'),
(94920, 'Mamaia-Sat', 4737, 'CT', 181, 'RO', 44.31159000, 28.62546000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2606390'),
(94921, 'Manasia', 4743, 'IL', 181, 'RO', 44.70000000, 26.66667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2718304'),
(94922, 'Mangalia', 4737, 'CT', 181, 'RO', 43.80000000, 28.58333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2718304'),
(94923, 'Manoleasa', 4740, 'BT', 181, 'RO', 47.98333000, 27.06667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2715861'),
(94924, 'Mara', 4760, 'MM', 181, 'RO', 47.75552000, 23.83126000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q753172'),
(94925, 'Marca', 4741, 'SJ', 181, 'RO', 47.21667000, 22.56667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1191370'),
(94926, 'Mareș', 4722, 'AG', 181, 'RO', 44.77824000, 24.79005000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q12121338'),
(94927, 'Marga', 4753, 'CS', 181, 'RO', 45.50000000, 22.51667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q75509'),
(94928, 'Marghita', 4723, 'BH', 181, 'RO', 47.35000000, 22.33333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q75509'),
(94929, 'Margina', 4748, 'TM', 181, 'RO', 45.85841000, 22.26654000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1166246'),
(94930, 'Marginea', 4720, 'SV', 181, 'RO', 47.81667000, 25.81667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1084311'),
(94931, 'Marin', 4741, 'SJ', 181, 'RO', 47.12754000, 22.81055000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q737121'),
(94932, 'Marpod', 4755, 'SB', 181, 'RO', 45.86667000, 24.50000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1076600'),
(94933, 'Mastacăn', 4731, 'NT', 181, 'RO', 46.78918000, 26.48677000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q12121792'),
(94934, 'Matca', 4747, 'GL', 181, 'RO', 45.85000000, 27.53333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q946916'),
(94935, 'Mateeşti', 4757, 'VL', 181, 'RO', 45.06667000, 23.85000000, '2019-10-05 23:15:26', '2020-05-01 17:23:12', 1, 'Q2721425'),
(94936, 'Matei', 4733, 'BN', 181, 'RO', 46.98333000, 24.26667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1077438'),
(94937, 'Mavrodin', 4728, 'TR', 181, 'RO', 44.03333000, 25.25000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2720611'),
(94938, 'Mavrodin', 4745, 'DB', 181, 'RO', 44.64919000, 25.71731000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2720611'),
(94939, 'Maxenu', 4756, 'BZ', 181, 'RO', 45.05162000, 26.85498000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12120614'),
(94940, 'Maxut', 4735, 'IS', 181, 'RO', 47.44040000, 26.89204000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12120666'),
(94941, 'Maşloc', 4748, 'TM', 181, 'RO', 45.99806000, 21.44972000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q1084767'),
(94942, 'Medgidia', 4737, 'CT', 181, 'RO', 44.25000000, 28.28333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1084767'),
(94943, 'Mediaş', 4755, 'SB', 181, 'RO', 46.16667000, 24.35000000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q1084767'),
(94944, 'Medieşu Aurit', 4746, 'SM', 181, 'RO', 47.78333000, 23.15000000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q775867'),
(94945, 'Mehadia', 4753, 'CS', 181, 'RO', 44.90083000, 22.36694000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1092694'),
(94946, 'Mehadica', 4753, 'CS', 181, 'RO', 45.03333000, 22.26667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1075754'),
(94947, 'Melineşti', 4742, 'DJ', 181, 'RO', 44.56667000, 23.71667000, '2019-10-05 23:15:26', '2020-05-01 17:23:09', 1, 'Q2312895'),
(94948, 'Mera', 4734, 'CJ', 181, 'RO', 46.81525000, 23.45405000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q716229'),
(94949, 'Mera', 4758, 'VN', 181, 'RO', 45.76667000, 26.95000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q14174093'),
(94950, 'Merei', 4756, 'BZ', 181, 'RO', 45.13333000, 26.68333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12122701'),
(94951, 'Mereni', 4754, 'CV', 181, 'RO', 46.07924000, 26.23597000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q32523'),
(94952, 'Mereni', 4737, 'CT', 181, 'RO', 44.05000000, 28.36667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1091903'),
(94953, 'Merenii de Jos', 4728, 'TR', 181, 'RO', 44.23214000, 25.64346000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12122708'),
(94954, 'Merenii de Sus', 4728, 'TR', 181, 'RO', 44.22839000, 25.62103000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12122710'),
(94955, 'Mereşti', 4749, 'HR', 181, 'RO', 46.23333000, 25.45000000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q650353'),
(94956, 'Merghindeal', 4755, 'SB', 181, 'RO', 45.96667000, 24.73333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1179236'),
(94957, 'Meri', 4729, 'PH', 181, 'RO', 44.83366000, 26.30863000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12122791'),
(94958, 'Merii Petchii', 4725, 'IF', 181, 'RO', 44.73532000, 26.30054000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12122795'),
(94959, 'Merişani', 4722, 'AG', 181, 'RO', 44.96820000, 24.74327000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q1896266'),
(94960, 'Merișani', 4728, 'TR', 181, 'RO', 44.25190000, 24.87550000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q12129935'),
(94961, 'Meseşenii de Jos', 4741, 'SJ', 181, 'RO', 47.15000000, 22.98333000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q12697735'),
(94962, 'Meteş', 4724, 'AB', 181, 'RO', 46.10000000, 23.41667000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q858192'),
(94963, 'Meziad', 4723, 'BH', 181, 'RO', 46.74090000, 22.42846000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q726506'),
(94964, 'Mica', 4734, 'CJ', 181, 'RO', 47.13333000, 23.93333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q757069'),
(94965, 'Miceşti', 4722, 'AG', 181, 'RO', 44.95000000, 24.86667000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q1896287'),
(94966, 'Miceştii de Câmpie', 4733, 'BN', 181, 'RO', 46.86667000, 24.31667000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q589519'),
(94967, 'Micești', 4724, 'AB', 181, 'RO', 46.10141000, 23.55573000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q840024'),
(94968, 'Micfalău', 4754, 'CV', 181, 'RO', 46.05342000, 25.83737000, '2019-10-05 23:15:26', '2020-05-01 17:23:09', 1, 'Q45706'),
(94969, 'Micleşti', 4752, 'VS', 181, 'RO', 46.81667000, 27.83333000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q2298051'),
(94970, 'Micula', 4746, 'SM', 181, 'RO', 47.90000000, 22.95000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1087603'),
(94971, 'Micăsasa', 4755, 'SB', 181, 'RO', 46.08333000, 24.11667000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q1069123'),
(94972, 'Miercurea Sibiului', 4755, 'SB', 181, 'RO', 45.88333000, 23.80000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1069123'),
(94973, 'Miercurea-Ciuc', 4749, 'HR', 181, 'RO', 46.35000000, 25.80000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q16898293'),
(94974, 'Mihai Bravu', 4723, 'BH', 181, 'RO', 47.25780000, 21.93811000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1004706'),
(94975, 'Mihai Bravu', 4727, 'TL', 181, 'RO', 44.95000000, 28.65000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1093120'),
(94976, 'Mihai Bravu', 4726, 'GR', 181, 'RO', 44.14056000, 26.06278000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q650892'),
(94977, 'Mihai Bravu', 4736, 'BR', 181, 'RO', 44.78469000, 27.72144000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12131755'),
(94978, 'Mihai Viteazu', 4737, 'CT', 181, 'RO', 44.63333000, 28.68333000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q1176862'),
(94979, 'Mihai Viteazu', 4732, 'CL', 181, 'RO', 44.34864000, 27.07361000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12131759'),
(94980, 'Mihai Viteazu', 4734, 'CJ', 181, 'RO', 46.53333000, 23.75000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q267794'),
(94981, 'Mihai Vodă', 4726, 'GR', 181, 'RO', 44.43916000, 25.81957000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q12131757'),
(94982, 'Mihail Kogălniceanu', 4727, 'TL', 181, 'RO', 45.03333000, 28.73333000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q1176875'),
(94983, 'Mihail Kogălniceanu', 4743, 'IL', 181, 'RO', 44.68333000, 27.73333000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q2291778'),
(94984, 'Mihail Kogălniceanu', 4737, 'CT', 181, 'RO', 44.36798000, 28.46000000, '2019-10-05 23:15:26', '2020-05-01 17:23:09', 1, 'Q1077654'),
(94985, 'Mihalţ', 4724, 'AB', 181, 'RO', 46.15000000, 23.73333000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q731535'),
(94986, 'Mihoveni', 4720, 'SV', 181, 'RO', 47.67994000, 26.18220000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12131804'),
(94987, 'Mihăeşti', 4757, 'VL', 181, 'RO', 45.03333000, 24.25000000, '2019-10-05 23:15:26', '2020-05-01 17:23:12', 1, 'Q2245804'),
(94988, 'Mihăeşti', 4722, 'AG', 181, 'RO', 45.11667000, 25.00000000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q2534239'),
(94989, 'Mihăeşti', 4738, 'OT', 181, 'RO', 44.13333000, 24.78333000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q2719531'),
(94990, 'Mihăiești', 4720, 'SV', 181, 'RO', 47.51563000, 26.21734000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q12131796'),
(94991, 'Mihăileni', 4755, 'SB', 181, 'RO', 45.98333000, 24.35000000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q1077845'),
(94992, 'Mihăileni', 4749, 'HR', 181, 'RO', 46.46667000, 25.81667000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q876070'),
(94993, 'Mihăileni', 4740, 'BT', 181, 'RO', 47.96667000, 26.15000000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q2534460'),
(94994, 'Mihăileşti', 4756, 'BZ', 181, 'RO', 44.91667000, 26.66667000, '2019-10-05 23:15:26', '2020-05-01 17:23:09', 1, 'Q12131780'),
(94995, 'Mihăileşti', 4726, 'GR', 181, 'RO', 44.32667000, 25.90833000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q12131780'),
(94996, 'Mihălceni', 4758, 'VN', 181, 'RO', 45.44493000, 27.30466000, '2019-10-05 23:15:26', '2020-05-01 17:23:12', 1, 'Q12131787'),
(94997, 'Mihălăşeni', 4740, 'BT', 181, 'RO', 47.88333000, 27.06667000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q2534855'),
(94998, 'Mija', 4745, 'DB', 181, 'RO', 44.91356000, 25.67585000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q12131007'),
(94999, 'Mijlocenii Bârgăului', 4733, 'BN', 181, 'RO', 47.21592000, 24.68936000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q610811'),
(95000, 'Milaş', 4733, 'BN', 181, 'RO', 46.81667000, 24.43333000, '2019-10-05 23:15:26', '2020-05-01 17:23:08', 1, 'Q1087253'),
(95001, 'Milcoiu', 4757, 'VL', 181, 'RO', 45.05000000, 24.46667000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q2721399'),
(95002, 'Milcovul', 4758, 'VN', 181, 'RO', 45.65000000, 27.25000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q14174544'),
(95003, 'Milcovățu', 4726, 'GR', 181, 'RO', 44.26570000, 25.69882000, '2019-10-05 23:15:26', '2020-05-01 17:23:10', 1, 'Q12131293'),
(95004, 'Mileanca', 4740, 'BT', 181, 'RO', 48.08333000, 26.70000000, '2019-10-05 23:15:26', '2019-10-05 23:15:26', 1, 'Q3553'),
(95005, 'Milişăuţi', 4720, 'SV', 181, 'RO', 47.78333000, 26.00000000, '2019-10-05 23:15:26', '2020-05-01 17:23:11', 1, 'Q3553'),
(95006, 'Miloşeşti', 4743, 'IL', 181, 'RO', 44.73333000, 27.23333000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q2718332'),
(95007, 'Mineri', 4727, 'TL', 181, 'RO', 45.16472000, 28.71573000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q989834'),
(95008, 'Minieri', 4729, 'PH', 181, 'RO', 44.98372000, 25.76083000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12131533'),
(95009, 'Mintiu Gherlii', 4734, 'CJ', 181, 'RO', 47.05000000, 23.95000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q1011653'),
(95010, 'Miorcani', 4740, 'BT', 181, 'RO', 48.20223000, 26.85279000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12131535'),
(95011, 'Mioveni', 4722, 'AG', 181, 'RO', 44.95905000, 24.94198000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12131535'),
(95012, 'Mircea Vodă', 4737, 'CT', 181, 'RO', 44.28333000, 28.16667000, '2019-10-05 23:15:27', '2020-05-01 17:23:09', 1, 'Q1068870'),
(95013, 'Mircea Vodă', 4736, 'BR', 181, 'RO', 45.11667000, 27.38333000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q12131590'),
(95014, 'Mirceşti', 4735, 'IS', 181, 'RO', 47.08333000, 26.83333000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q1176088'),
(95015, 'Mirceștii Noi', 4758, 'VN', 181, 'RO', 45.75072000, 27.27260000, '2019-10-05 23:15:27', '2020-05-01 17:23:12', 1, 'Q12131597'),
(95016, 'Mireşu Mare', 4760, 'MM', 181, 'RO', 47.50000000, 23.33333000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q1188994'),
(95017, 'Mireșu Mare', 4729, 'PH', 181, 'RO', 45.13508000, 26.37714000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q12131552'),
(95018, 'Miron Costin', 4731, 'NT', 181, 'RO', 46.92030000, 26.80567000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12131565'),
(95019, 'Mironeasa', 4735, 'IS', 181, 'RO', 46.96667000, 27.41667000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2605987'),
(95020, 'Mironu', 4720, 'SV', 181, 'RO', 47.48799000, 26.00952000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q12131569'),
(95021, 'Miroslava', 4735, 'IS', 181, 'RO', 47.15000000, 27.51667000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2639798'),
(95022, 'Miroslovești', 4735, 'IS', 181, 'RO', 47.15000000, 26.65000000, '2019-10-05 23:15:27', '2020-05-01 17:23:10', 1, 'Q2607487'),
(95023, 'Miroslăvești', 4729, 'PH', 181, 'RO', 44.80383000, 26.08689000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q12131573'),
(95024, 'Miroşi', 4722, 'AG', 181, 'RO', 44.41667000, 24.93333000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q2534621'),
(95025, 'Mirăslău', 4724, 'AB', 181, 'RO', 46.36667000, 23.71667000, '2019-10-05 23:15:27', '2020-05-01 17:23:08', 1, 'Q1096653'),
(95026, 'Mirşid', 4741, 'SJ', 181, 'RO', 47.23333000, 23.13333000, '2019-10-05 23:15:27', '2020-05-01 17:23:11', 1, 'Q1191846'),
(95027, 'Mischii', 4742, 'DJ', 181, 'RO', 44.38333000, 23.85000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q761521'),
(95028, 'Misentea', 4749, 'HR', 181, 'RO', 46.32714000, 25.89506000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q713657'),
(95029, 'Mislea', 4729, 'PH', 181, 'RO', 45.09668000, 25.82444000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q3311099'),
(95030, 'Mitoc', 4740, 'BT', 181, 'RO', 48.10000000, 27.03333000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q2535251'),
(95031, 'Mitocu Dragomirnei', 4720, 'SV', 181, 'RO', 47.73333000, 26.25000000, '2019-10-05 23:15:27', '2019-10-05 23:15:27', 1, 'Q948210');

