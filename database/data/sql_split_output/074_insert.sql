INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(37176, 'Sasamón', 1146, 'BU', 207, 'ES', 42.41687000, -4.04288000, '2019-10-05 22:46:12', '2022-08-29 11:24:20', 1, 'Q1628353'),
(37177, 'Saucedilla', 1190, 'CC', 207, 'ES', 39.85284000, -5.67781000, '2019-10-05 22:46:12', '2022-08-28 18:12:22', 1, 'Q1628353'),
(37178, '<PERSON><PERSON><PERSON>', 1147, 'SA', 207, 'ES', 41.04769000, -6.75328000, '2019-10-05 22:46:12', '2022-08-29 11:44:52', 1, 'Q1650196'),
(37179, '<PERSON><PERSON><PERSON><PERSON>', 1192, 'SG', 207, 'ES', 41.19422000, -4.06884000, '2019-10-05 22:46:12', '2022-08-29 11:50:43', 1, 'Q1938346'),
(37180, '<PERSON>uzal', 1185, 'GC', 207, 'ES', 28.46667000, -16.41667000, '2019-10-05 22:46:12', '2022-08-29 12:06:32', 1, 'Q782910'),
(37181, 'Sax', 5108, 'A', 207, 'ES', 38.53729000, -0.81779000, '2019-10-05 22:46:12', '2022-08-29 11:15:48', 1, 'Q782910'),
(37182, 'Sayalonga', 5101, 'MA', 207, 'ES', 36.79819000, -4.01325000, '2019-10-05 22:46:12', '2022-08-28 19:06:53', 1, 'Q1630141'),
(37183, 'Sayatón', 5107, 'GU', 207, 'ES', 40.37635000, -2.85253000, '2019-10-05 22:46:12', '2022-08-29 11:06:46', 1, 'Q1656086'),
(37184, 'Saúca', 5107, 'GU', 207, 'ES', 41.03098000, -2.52905000, '2019-10-05 22:46:12', '2022-08-29 11:06:45', 1, 'Q1766058'),
(37185, 'Sebúlcor', 1192, 'SG', 207, 'ES', 41.27091000, -3.88398000, '2019-10-05 22:46:12', '2022-08-29 11:50:43', 1, 'Q1917492'),
(37186, 'Secastilla', 1177, 'HU', 207, 'ES', 42.18112000, 0.26736000, '2019-10-05 22:46:12', '2022-08-29 12:06:20', 1, 'Q13050343'),
(37187, 'Sedaví', 1175, 'V', 207, 'ES', 39.43333000, -0.38333000, '2019-10-05 22:46:12', '2022-08-29 12:05:40', 1, 'Q102862'),
(37188, 'Sedella', 5101, 'MA', 207, 'ES', 36.86232000, -4.03314000, '2019-10-05 22:46:12', '2022-08-28 19:06:53', 1, 'Q1630165'),
(37189, 'Sediles', 5113, 'Z', 207, 'ES', 41.34613000, -1.53177000, '2019-10-05 22:46:12', '2022-08-29 11:42:54', 1, 'Q769756'),
(37190, 'Segart', 1175, 'V', 207, 'ES', 39.68333000, -0.36667000, '2019-10-05 22:46:12', '2022-08-29 12:05:40', 1, 'Q769756'),
(37191, 'Segorbe', 5110, 'CS', 207, 'ES', 39.85000000, -0.48333000, '2019-10-05 22:46:12', '2022-08-29 11:26:42', 1, 'Q769756'),
(37192, 'Segovia', 1192, 'SG', 207, 'ES', 40.94808000, -4.11839000, '2019-10-05 22:46:12', '2022-08-29 11:50:43', 1, 'Q23992605'),
(37193, 'Segundo Ensanche', 1204, 'NA', 207, 'ES', 42.81390000, -1.64295000, '2019-10-05 22:46:12', '2022-08-29 12:06:07', 1, 'Q23992605'),
(37194, 'Segura', 1191, 'SS', 207, 'ES', 43.00753000, -2.25400000, '2019-10-05 22:46:12', '2022-08-28 18:36:50', 1, 'Q23992605'),
(37195, 'Segura de León', 5092, 'BA', 207, 'ES', 38.11667000, -6.51667000, '2019-10-05 22:46:12', '2022-08-28 18:09:23', 1, 'Q23979296'),
(37196, 'Segura de Toro', 1190, 'CC', 207, 'ES', 40.22427000, -5.94836000, '2019-10-05 22:46:12', '2022-08-28 18:12:22', 1, 'Q542646'),
(37197, 'Segura de la Sierra', 5100, 'J', 207, 'ES', 38.29777000, -2.65229000, '2019-10-05 22:46:12', '2022-08-28 19:04:30', 1, 'Q1645054'),
(37198, 'Segura de los Baños', 5111, 'TE', 207, 'ES', 40.94088000, -0.95111000, '2019-10-05 22:46:12', '2022-08-29 11:29:44', 1, 'Q1650805'),
(37199, 'Segurilla', 1205, 'TO', 207, 'ES', 40.02386000, -4.86418000, '2019-10-05 22:46:12', '2022-08-29 11:08:30', 1, 'Q1641340'),
(37200, 'Seira', 1177, 'HU', 207, 'ES', 42.47696000, 0.43127000, '2019-10-05 22:46:12', '2022-08-29 12:06:20', 1, 'Q987417'),
(37201, 'Selas', 5107, 'GU', 207, 'ES', 40.95150000, -2.10203000, '2019-10-05 22:46:12', '2022-08-29 11:06:46', 1, 'Q987417'),
(37202, 'Selaya', 1170, 'S', 207, 'ES', 43.21376000, -3.80563000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q987417'),
(37203, 'Sella', 5108, 'A', 207, 'ES', 38.60926000, -0.27305000, '2019-10-05 22:46:12', '2022-08-29 11:15:48', 1, 'Q985137'),
(37204, 'Sellent', 1175, 'V', 207, 'ES', 39.03221000, -0.58784000, '2019-10-05 22:46:12', '2022-08-29 12:05:40', 1, 'Q2046224'),
(37205, 'Selva', 1174, 'PM', 207, 'ES', 39.75504000, 2.90069000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q2046224'),
(37206, 'Semillas', 5107, 'GU', 207, 'ES', 41.05857000, -3.11945000, '2019-10-05 22:46:12', '2022-08-29 11:06:46', 1, 'Q1624191'),
(37207, 'Sempere', 1175, 'V', 207, 'ES', 38.92014000, -0.48140000, '2019-10-05 22:46:12', '2022-08-29 12:05:40', 1, 'Q1993064'),
(37208, 'Sena de Luna', 1200, 'LE', 207, 'ES', 42.92906000, -5.95267000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q1607678'),
(37209, 'Sencelles', 1174, 'PM', 207, 'ES', 39.64598000, 2.89769000, '2019-10-05 22:46:12', '2019-10-05 22:46:12', 1, 'Q987394'),
(37210, 'Senija', 5108, 'A', 207, 'ES', 38.72804000, 0.04176000, '2019-10-05 22:46:12', '2022-08-29 11:15:48', 1, 'Q1983238'),
(37211, 'Seno', 5111, 'TE', 207, 'ES', 40.81225000, -0.33816000, '2019-10-05 22:46:12', '2022-08-29 11:29:44', 1, 'Q1983238'),
(37212, 'Sentmenat', 5102, 'B', 207, 'ES', 41.60862000, 2.13532000, '2019-10-05 22:46:12', '2022-08-29 10:50:01', 1, 'Q1983238'),
(37213, 'Senyera', 1175, 'V', 207, 'ES', 39.06667000, -0.50000000, '2019-10-05 22:46:12', '2022-08-29 12:05:40', 1, 'Q1983238'),
(37214, 'Senés de Alcubierre', 1177, 'HU', 207, 'ES', 41.90731000, -0.48906000, '2019-10-05 22:46:12', '2022-08-29 12:06:20', 1, 'Q911046'),
(37215, 'Sepúlveda', 1192, 'SG', 207, 'ES', 41.29695000, -3.74221000, '2019-10-05 22:46:12', '2022-08-29 11:50:43', 1, 'Q23992630'),
(37216, 'Sequera de Fresno', 1192, 'SG', 207, 'ES', 41.36640000, -3.54612000, '2019-10-05 22:46:13', '2022-08-29 11:50:43', 1, 'Q945595'),
(37217, 'Sequeros', 1147, 'SA', 207, 'ES', 40.51295000, -6.02495000, '2019-10-05 22:46:13', '2022-08-29 11:44:52', 1, 'Q1766219'),
(37218, 'Serra', 1175, 'V', 207, 'ES', 39.68333000, -0.43333000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q55822'),
(37219, 'Serra de Daró', 5103, 'GI', 207, 'ES', 42.02877000, 3.07222000, '2019-10-05 22:46:13', '2022-08-29 10:53:16', 1, 'Q23992641'),
(37220, 'Serrada', 1183, 'VA', 207, 'ES', 41.45722000, -4.86279000, '2019-10-05 22:46:13', '2022-08-29 11:48:45', 1, 'Q23992641'),
(37221, 'Serradilla', 1190, 'CC', 207, 'ES', 39.82920000, -6.14034000, '2019-10-05 22:46:13', '2022-08-28 18:12:22', 1, 'Q1628832'),
(37222, 'Serradilla del Arroyo', 1147, 'SA', 207, 'ES', 40.52133000, -6.36008000, '2019-10-05 22:46:13', '2022-08-29 11:44:52', 1, 'Q1766084'),
(37223, 'Serradilla del Llano', 1147, 'SA', 207, 'ES', 40.49996000, -6.35701000, '2019-10-05 22:46:13', '2022-08-29 11:44:52', 1, 'Q1905309'),
(37224, 'Serranillos', 1189, 'AV', 207, 'ES', 40.33625000, -4.91165000, '2019-10-05 22:46:13', '2022-08-29 11:49:57', 1, 'Q766377'),
(37225, 'Serranillos del Valle', 1158, 'M', 207, 'ES', 40.20211000, -3.88187000, '2019-10-05 22:46:13', '2022-08-29 12:04:40', 1, 'Q1772168'),
(37226, 'Serrejón', 1190, 'CC', 207, 'ES', 39.81735000, -5.80263000, '2019-10-05 22:46:13', '2022-08-28 18:12:22', 1, 'Q1640773'),
(37227, 'Seròs', 5104, 'L', 207, 'ES', 41.46667000, 0.41667000, '2019-10-05 22:46:13', '2022-08-29 10:55:25', 1, 'Q1640773'),
(37228, 'Serón', 5095, 'AL', 207, 'ES', 37.34485000, -2.50913000, '2019-10-05 22:46:13', '2022-08-28 18:41:41', 1, 'Q1606427'),
(37229, 'Ses Salines', 1174, 'PM', 207, 'ES', 39.33831000, 3.05274000, '2019-10-05 22:46:13', '2019-10-05 22:46:13', 1, 'Q1606427'),
(37230, 'Sesa', 1177, 'HU', 207, 'ES', 41.99484000, -0.24511000, '2019-10-05 22:46:13', '2022-08-29 12:06:20', 1, 'Q987448'),
(37231, 'Seseña', 1205, 'TO', 207, 'ES', 40.10473000, -3.69793000, '2019-10-05 22:46:13', '2022-08-29 11:08:30', 1, 'Q24017276'),
(37232, 'Sesma', 1204, 'NA', 207, 'ES', 42.47748000, -2.08353000, '2019-10-05 22:46:13', '2022-08-29 12:06:07', 1, 'Q1767933'),
(37233, 'Sestao', 5094, 'BI', 207, 'ES', 43.30975000, -3.00716000, '2019-10-05 22:46:13', '2022-08-28 18:25:56', 1, 'Q909707'),
(37234, 'Sestrica', 5113, 'Z', 207, 'ES', 41.48595000, -1.59501000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q1639129'),
(37235, 'Sesué', 1177, 'HU', 207, 'ES', 42.55131000, 0.47212000, '2019-10-05 22:46:13', '2022-08-29 12:06:20', 1, 'Q984503'),
(37236, 'Setenil de las Bodegas', 5096, 'CA', 207, 'ES', 36.86397000, -5.18177000, '2019-10-05 22:46:13', '2022-08-28 18:44:29', 1, 'Q918623'),
(37237, 'Setiles', 5107, 'GU', 207, 'ES', 40.73419000, -1.61720000, '2019-10-05 22:46:13', '2022-08-29 11:06:46', 1, 'Q1655922'),
(37238, 'Seva', 5102, 'B', 207, 'ES', 41.83831000, 2.28007000, '2019-10-05 22:46:13', '2022-08-29 10:50:01', 1, 'Q1655922'),
(37239, 'Sevilla', 1193, 'SE', 207, 'ES', 37.38283000, -5.97317000, '2019-10-05 22:46:13', '2022-08-28 19:08:49', 1, 'Q55893348'),
(37240, 'Sevilla La Nueva', 1158, 'M', 207, 'ES', 40.34775000, -4.02727000, '2019-10-05 22:46:13', '2022-08-29 12:04:40', 1, 'Q1641045'),
(37241, 'Sevilleja de la Jara', 1205, 'TO', 207, 'ES', 39.57488000, -4.96387000, '2019-10-05 22:46:13', '2022-08-29 11:08:30', 1, 'Q1628453'),
(37242, 'Sidamon', 5104, 'L', 207, 'ES', 41.63333000, 0.83333000, '2019-10-05 22:46:13', '2022-08-29 10:55:25', 1, 'Q1916315'),
(37243, 'Sienes', 5107, 'GU', 207, 'ES', 41.20096000, -2.65332000, '2019-10-05 22:46:13', '2022-08-29 11:06:46', 1, 'Q1655867'),
(37244, 'Sierra de Fuentes', 1190, 'CC', 207, 'ES', 39.43997000, -6.27242000, '2019-10-05 22:46:13', '2022-08-28 18:12:22', 1, 'Q1628461'),
(37245, 'Sierra de Luna', 5113, 'Z', 207, 'ES', 42.04818000, -0.91025000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q1650382'),
(37246, 'Sierra de Yeguas', 5101, 'MA', 207, 'ES', 37.12420000, -4.86773000, '2019-10-05 22:46:13', '2022-08-28 19:06:53', 1, 'Q1647758'),
(37247, 'Sierra-Engarcerán', 1175, 'V', 207, 'ES', 40.26929000, -0.01892000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q1647758'),
(37248, 'Sierro', 5095, 'AL', 207, 'ES', 37.32237000, -2.39844000, '2019-10-05 22:46:13', '2022-08-28 18:41:41', 1, 'Q1628481'),
(37249, 'Siete Aguas', 1175, 'V', 207, 'ES', 39.46667000, -0.91667000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q1628481'),
(37250, 'Siete Iglesias de Trabancos', 1183, 'VA', 207, 'ES', 41.35223000, -5.18488000, '2019-10-05 22:46:13', '2022-08-29 11:48:45', 1, 'Q1907055'),
(37251, 'Sigeres', 1189, 'AV', 207, 'ES', 40.79939000, -4.93361000, '2019-10-05 22:46:13', '2022-08-29 11:49:57', 1, 'Q1607058'),
(37252, 'Sigüenza', 5107, 'GU', 207, 'ES', 41.06892000, -2.64308000, '2019-10-05 22:46:13', '2022-08-29 11:06:46', 1, 'Q24011087'),
(37253, 'Sigüés', 5113, 'Z', 207, 'ES', 42.63030000, -1.01212000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q24019834'),
(37254, 'Siles', 5100, 'J', 207, 'ES', 38.38983000, -2.58190000, '2019-10-05 22:46:13', '2022-08-28 19:04:30', 1, 'Q1774790'),
(37255, 'Silla', 1175, 'V', 207, 'ES', 39.36257000, -0.41169000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q1774790'),
(37256, 'Silleda', 1167, 'PO', 207, 'ES', 42.69605000, -8.24653000, '2019-10-05 22:46:13', '2022-08-28 17:57:54', 1, 'Q1607540'),
(37257, 'Sils', 5103, 'GI', 207, 'ES', 41.80842000, 2.74507000, '2019-10-05 22:46:13', '2022-08-29 10:53:16', 1, 'Q1607540'),
(37258, 'Simancas', 1183, 'VA', 207, 'ES', 41.59072000, -4.82796000, '2019-10-05 22:46:13', '2022-08-29 11:48:45', 1, 'Q986572'),
(37259, 'Simat de la Valldigna', 1175, 'V', 207, 'ES', 39.03333000, -0.31667000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q1020545'),
(37260, 'Sinarcas', 1175, 'V', 207, 'ES', 39.73333000, -1.23333000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q1020545'),
(37261, 'Sineu', 1174, 'PM', 207, 'ES', 39.64254000, 3.01034000, '2019-10-05 22:46:13', '2019-10-05 22:46:13', 1, 'Q943922'),
(37262, 'Singra', 5111, 'TE', 207, 'ES', 40.65508000, -1.31158000, '2019-10-05 22:46:13', '2022-08-29 11:29:44', 1, 'Q24015033'),
(37263, 'Sinlabajos', 1189, 'AV', 207, 'ES', 41.07650000, -4.83242000, '2019-10-05 22:46:13', '2022-08-29 11:49:57', 1, 'Q1628122'),
(37264, 'Siruela', 5092, 'BA', 207, 'ES', 38.97718000, -5.04969000, '2019-10-05 22:46:13', '2022-08-28 18:09:23', 1, 'Q1372816'),
(37265, 'Sisamón', 5113, 'Z', 207, 'ES', 41.17124000, -2.00386000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q1639113'),
(37266, 'Sisante', 5106, 'CU', 207, 'ES', 39.40849000, -2.20173000, '2019-10-05 22:46:13', '2022-08-29 11:05:01', 1, 'Q195679'),
(37267, 'Sitges', 5102, 'B', 207, 'ES', 41.23506000, 1.81193000, '2019-10-05 22:46:13', '2022-08-29 10:50:01', 1, 'Q195679'),
(37268, 'Siurana', 5103, 'GI', 207, 'ES', 42.20916000, 2.99392000, '2019-10-05 22:46:13', '2022-08-29 10:53:16', 1, 'Q195679'),
(37269, 'Siétamo', 1177, 'HU', 207, 'ES', 42.12392000, -0.28066000, '2019-10-05 22:46:13', '2022-08-29 12:06:20', 1, 'Q24013313'),
(37270, 'Sober', 5090, 'LU', 207, 'ES', 42.46127000, -7.58640000, '2019-10-05 22:46:13', '2022-08-28 17:49:36', 1, 'Q1442183'),
(37272, 'Sobradiel', 5113, 'Z', 207, 'ES', 41.73875000, -1.03765000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q1650760'),
(37273, 'Sobradillo', 1147, 'SA', 207, 'ES', 40.91754000, -6.79729000, '2019-10-05 22:46:13', '2022-08-29 11:44:52', 1, 'Q1650760'),
(37274, 'Sobrado', 5089, 'C', 207, 'ES', 43.03882000, -8.02784000, '2019-10-05 22:46:13', '2022-08-28 13:37:17', 1, 'Q644626'),
(37275, 'Sobrado', 1200, 'LE', 207, 'ES', 42.51667000, -6.85000000, '2019-10-05 22:46:13', '2019-10-05 22:46:13', 1, 'Q1990944'),
(37277, 'Socuéllamos', 5105, 'CR', 207, 'ES', 39.28581000, -2.79205000, '2019-10-05 22:46:13', '2022-08-29 11:03:25', 1, 'Q1614556'),
(37278, 'Sojuela', 1171, 'LO', 207, 'ES', 42.36988000, -2.54525000, '2019-10-05 22:46:13', '2022-08-29 12:05:09', 1, 'Q1641127'),
(37279, 'Solana de los Barros', 5092, 'BA', 207, 'ES', 38.72527000, -6.53899000, '2019-10-05 22:46:13', '2022-08-28 18:09:23', 1, 'Q1442582'),
(37280, 'Solanillos del Extremo', 5107, 'GU', 207, 'ES', 40.75088000, -2.69799000, '2019-10-05 22:46:13', '2022-08-29 11:06:46', 1, 'Q1655874'),
(37281, 'Solarana', 1146, 'BU', 207, 'ES', 41.97168000, -3.65876000, '2019-10-05 22:46:13', '2022-08-29 11:24:20', 1, 'Q627922'),
(37282, 'Soliedra', 1208, 'SO', 207, 'ES', 41.46932000, -2.38164000, '2019-10-05 22:46:13', '2022-08-29 11:51:23', 1, 'Q830913'),
(37283, 'Sollana', 1175, 'V', 207, 'ES', 39.27830000, -0.38238000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q830913'),
(37284, 'Solosancho', 1189, 'AV', 207, 'ES', 40.55294000, -4.90479000, '2019-10-05 22:46:13', '2022-08-29 11:49:57', 1, 'Q1610933'),
(37285, 'Solsona', 5104, 'L', 207, 'ES', 41.99395000, 1.51706000, '2019-10-05 22:46:13', '2022-08-29 10:55:25', 1, 'Q1610933'),
(37286, 'Solórzano', 1170, 'S', 207, 'ES', 43.38233000, -3.58785000, '2019-10-05 22:46:13', '2020-05-01 17:23:15', 1, 'Q1610933'),
(37287, 'Somolinos', 5107, 'GU', 207, 'ES', 41.24606000, -3.06004000, '2019-10-05 22:46:13', '2022-08-29 11:06:46', 1, 'Q1610933'),
(37288, 'Somontín', 5095, 'AL', 207, 'ES', 37.39176000, -2.38828000, '2019-10-05 22:46:13', '2022-08-28 18:41:41', 1, 'Q1610933'),
(37290, 'Son Ferrer', 1174, 'PM', 207, 'ES', 39.49666000, 2.50102000, '2019-10-05 22:46:13', '2019-10-05 22:46:13', 1, 'Q1442802'),
(37291, 'Son Servera', 1174, 'PM', 207, 'ES', 39.62073000, 3.36008000, '2019-10-05 22:46:13', '2019-10-05 22:46:13', 1, 'Q610838'),
(37292, 'Sondika', 5094, 'BI', 207, 'ES', 43.30020000, -2.92442000, '2019-10-05 22:46:13', '2022-08-28 18:25:56', 1, 'Q942025'),
(37293, 'Soneja', 5110, 'CS', 207, 'ES', 39.81667000, -0.41667000, '2019-10-05 22:46:13', '2022-08-29 11:26:42', 1, 'Q1645719'),
(37294, 'Sonseca', 1205, 'TO', 207, 'ES', 39.67747000, -3.97448000, '2019-10-05 22:46:13', '2022-08-29 11:08:30', 1, 'Q1628387'),
(37296, 'Soportújar', 5098, 'GR', 207, 'ES', 36.92863000, -3.40542000, '2019-10-05 22:46:13', '2022-08-28 18:52:58', 1, 'Q615823'),
(37297, 'Sopuerta', 5094, 'BI', 207, 'ES', 43.26239000, -3.15505000, '2019-10-05 22:46:13', '2022-08-28 18:25:56', 1, 'Q1228749'),
(37298, 'Sorbas', 5095, 'AL', 207, 'ES', 37.09761000, -2.12349000, '2019-10-05 22:46:13', '2022-08-28 18:41:41', 1, 'Q842658'),
(37299, 'Sordillos', 1146, 'BU', 207, 'ES', 42.46197000, -4.10726000, '2019-10-05 22:46:13', '2022-08-29 11:24:20', 1, 'Q1641185'),
(37300, 'Soria', 1208, 'SO', 207, 'ES', 41.76401000, -2.46883000, '2019-10-05 22:46:13', '2022-08-29 11:51:23', 1, 'Q12155'),
(37301, 'Sorihuela', 1147, 'SA', 207, 'ES', 40.44378000, -5.67818000, '2019-10-05 22:46:13', '2022-08-29 11:44:52', 1, 'Q1905293'),
(37302, 'Sorihuela del Guadalimar', 5100, 'J', 207, 'ES', 38.24062000, -3.05360000, '2019-10-05 22:46:13', '2022-08-28 19:04:30', 1, 'Q1751226'),
(37303, 'Sorlada', 1204, 'NA', 207, 'ES', 42.61507000, -2.21525000, '2019-10-05 22:46:13', '2022-08-29 12:06:07', 1, 'Q1647408'),
(37304, 'Sort', 5104, 'L', 207, 'ES', 42.41319000, 1.13045000, '2019-10-05 22:46:13', '2022-08-29 10:55:25', 1, 'Q12690'),
(37305, 'Sorvilán', 5098, 'GR', 207, 'ES', 36.79505000, -3.26769000, '2019-10-05 22:46:13', '2022-08-28 18:52:58', 1, 'Q615809'),
(37306, 'Sorzano', 1171, 'LO', 207, 'ES', 42.34307000, -2.52872000, '2019-10-05 22:46:13', '2022-08-29 12:05:09', 1, 'Q1646023'),
(37307, 'Sos del Rey Católico', 5113, 'Z', 207, 'ES', 42.49686000, -1.21514000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q11703972'),
(37308, 'Soses', 5104, 'L', 207, 'ES', 41.53333000, 0.48333000, '2019-10-05 22:46:13', '2022-08-29 10:55:25', 1, 'Q1766622'),
(37309, 'Sot de Chera', 1175, 'V', 207, 'ES', 39.63333000, -0.90000000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q1766622'),
(37310, 'Sot de Ferrer', 5110, 'CS', 207, 'ES', 39.80000000, -0.40000000, '2019-10-05 22:46:13', '2022-08-29 11:26:42', 1, 'Q1766245'),
(37311, 'Sotalbo', 1189, 'AV', 207, 'ES', 40.54170000, -4.84548000, '2019-10-05 22:46:13', '2022-08-29 11:49:57', 1, 'Q1768457'),
(37312, 'Sotillo', 1192, 'SG', 207, 'ES', 41.25846000, -3.63729000, '2019-10-05 22:46:13', '2022-08-29 11:50:43', 1, 'Q606669'),
(37313, 'Sotillo de la Adrada', 1189, 'AV', 207, 'ES', 40.29120000, -4.58385000, '2019-10-05 22:46:13', '2022-08-29 11:49:57', 1, 'Q1609922'),
(37314, 'Sotillo de la Ribera', 1146, 'BU', 207, 'ES', 41.77717000, -3.82525000, '2019-10-05 22:46:13', '2022-08-29 11:24:20', 1, 'Q1628395'),
(37315, 'Sotillo de las Palomas', 1205, 'TO', 207, 'ES', 40.10359000, -4.82736000, '2019-10-05 22:46:13', '2022-08-29 11:08:30', 1, 'Q1641964'),
(37316, 'Sotillo del Rincón', 1208, 'SO', 207, 'ES', 41.93220000, -2.60297000, '2019-10-05 22:46:13', '2022-08-29 11:51:23', 1, 'Q834303'),
(37317, 'Soto de Cerrato', 1157, 'P', 207, 'ES', 41.95312000, -4.42998000, '2019-10-05 22:46:13', '2022-08-29 11:45:45', 1, 'Q1918481'),
(37318, 'Soto de la Vega', 1200, 'LE', 207, 'ES', 42.33227000, -5.88225000, '2019-10-05 22:46:13', '2019-10-05 22:46:13', 1, 'Q734200'),
(37319, 'Soto en Cameros', 1171, 'LO', 207, 'ES', 42.28591000, -2.42597000, '2019-10-05 22:46:13', '2022-08-29 12:05:09', 1, 'Q1645875'),
(37320, 'Soto y Amío', 1200, 'LE', 207, 'ES', 42.77477000, -5.88693000, '2019-10-05 22:46:13', '2020-05-01 17:23:17', 1, 'Q1615429'),
(37321, 'Sotobañado y Priorato', 1157, 'P', 207, 'ES', 42.58987000, -4.44185000, '2019-10-05 22:46:13', '2022-08-29 11:45:45', 1, 'Q1949169'),
(37322, 'Sotodosos', 5107, 'GU', 207, 'ES', 40.92149000, -2.39211000, '2019-10-05 22:46:13', '2022-08-29 11:06:46', 1, 'Q1656288'),
(37323, 'Sotosalbos', 1192, 'SG', 207, 'ES', 41.03537000, -3.94115000, '2019-10-05 22:46:13', '2022-08-29 11:50:43', 1, 'Q1938445'),
(37324, 'Sotoserrano', 1147, 'SA', 207, 'ES', 40.43499000, -6.03261000, '2019-10-05 22:46:13', '2022-08-29 11:44:52', 1, 'Q1650210'),
(37325, 'Sotragero', 1146, 'BU', 207, 'ES', 42.40996000, -3.71312000, '2019-10-05 22:46:13', '2022-08-29 11:24:20', 1, 'Q942983'),
(37326, 'Sotresgudo', 1146, 'BU', 207, 'ES', 42.57935000, -4.17725000, '2019-10-05 22:46:13', '2022-08-29 11:24:20', 1, 'Q1641152'),
(37327, 'Suances', 1170, 'S', 207, 'ES', 43.43341000, -4.04338000, '2019-10-05 22:46:13', '2019-10-05 22:46:13', 1, 'Q1641152'),
(37328, 'Subirats', 5102, 'B', 207, 'ES', 41.40000000, 1.83333000, '2019-10-05 22:46:13', '2022-08-29 10:50:01', 1, 'Q782758'),
(37329, 'Sueca', 1175, 'V', 207, 'ES', 39.20260000, -0.31114000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q782758'),
(37330, 'Suellacabras', 1208, 'SO', 207, 'ES', 41.85298000, -2.22398000, '2019-10-05 22:46:13', '2022-08-29 11:51:23', 1, 'Q486017'),
(37331, 'Suflí', 5095, 'AL', 207, 'ES', 37.33866000, -2.38817000, '2019-10-05 22:46:13', '2022-08-28 18:41:41', 1, 'Q486017'),
(37332, 'Sumacàrcer', 1175, 'V', 207, 'ES', 39.10000000, -0.63333000, '2019-10-05 22:46:13', '2022-08-29 12:05:40', 1, 'Q486017'),
(37333, 'Susinos del Páramo', 1146, 'BU', 207, 'ES', 42.47108000, -3.92555000, '2019-10-05 22:46:13', '2022-08-29 11:24:20', 1, 'Q920359'),
(37334, 'Susqueda', 5103, 'GI', 207, 'ES', 41.97776000, 2.51652000, '2019-10-05 22:46:13', '2022-08-29 10:53:16', 1, 'Q13642'),
(37335, 'Sádaba', 5113, 'Z', 207, 'ES', 42.28180000, -1.26951000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q24019849'),
(37336, 'Sástago', 5113, 'Z', 207, 'ES', 41.32166000, -0.35075000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q956485'),
(37337, 'Sóller', 1174, 'PM', 207, 'ES', 39.76623000, 2.71521000, '2019-10-05 22:46:13', '2020-05-01 17:23:15', 1, 'Q956485'),
(37338, 'Súria', 5102, 'B', 207, 'ES', 41.83333000, 1.75000000, '2019-10-05 22:46:13', '2022-08-29 10:50:01', 1, 'Q956485'),
(37339, 'Tabanera de Cerrato', 1157, 'P', 207, 'ES', 42.02469000, -4.12361000, '2019-10-05 22:46:13', '2022-08-29 11:45:45', 1, 'Q1918812'),
(37340, 'Tabanera de Valdavia', 1157, 'P', 207, 'ES', 42.64728000, -4.69414000, '2019-10-05 22:46:13', '2022-08-29 11:45:45', 1, 'Q1906976'),
(37341, 'Tabanera la Luenga', 1192, 'SG', 207, 'ES', 41.09647000, -4.23937000, '2019-10-05 22:46:13', '2022-08-29 11:50:43', 1, 'Q1916977'),
(37342, 'Tabera de Abajo', 1147, 'SA', 207, 'ES', 40.91021000, -6.00227000, '2019-10-05 22:46:13', '2022-08-29 11:44:52', 1, 'Q1766858'),
(37343, 'Tabernas', 5095, 'AL', 207, 'ES', 37.04992000, -2.39084000, '2019-10-05 22:46:13', '2022-08-28 18:41:41', 1, 'Q1444062'),
(37344, 'Taboada', 5090, 'LU', 207, 'ES', 42.69900000, -7.82298000, '2019-10-05 22:46:13', '2022-08-28 17:49:36', 1, 'Q23992081'),
(37345, 'Taboadela', 5091, 'OR', 207, 'ES', 42.24234000, -7.82719000, '2019-10-05 22:46:13', '2022-08-28 17:53:26', 1, 'Q970921'),
(37346, 'Tabuenca', 5113, 'Z', 207, 'ES', 41.69500000, -1.54335000, '2019-10-05 22:46:13', '2022-08-29 11:42:54', 1, 'Q675840'),
(37347, 'Tacoronte', 5112, 'TF', 207, 'ES', 28.47688000, -16.41016000, '2019-10-05 22:46:13', '2022-08-29 11:31:13', 1, 'Q675840'),
(37348, 'Tafalla', 1204, 'NA', 207, 'ES', 42.52687000, -1.67446000, '2019-10-05 22:46:13', '2022-08-29 12:06:07', 1, 'Q820224'),
(37349, 'Tagamanent', 5102, 'B', 207, 'ES', 41.73747000, 2.26720000, '2019-10-05 22:46:13', '2022-08-29 10:50:01', 1, 'Q15457'),
(37350, 'Tahal', 5095, 'AL', 207, 'ES', 37.22797000, -2.28470000, '2019-10-05 22:46:14', '2022-08-28 18:41:41', 1, 'Q1611746'),
(37351, 'Tajahuerce', 1208, 'SO', 207, 'ES', 41.73991000, -2.15069000, '2019-10-05 22:46:14', '2022-08-29 11:51:23', 1, 'Q837609'),
(37352, 'Tajueco', 1208, 'SO', 207, 'ES', 41.53641000, -2.84845000, '2019-10-05 22:46:14', '2022-08-29 11:51:23', 1, 'Q834423'),
(37353, 'Talamanca', 5102, 'B', 207, 'ES', 41.73740000, 1.97791000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q16738'),
(37354, 'Talamanca de Jarama', 1158, 'M', 207, 'ES', 40.74325000, -3.50889000, '2019-10-05 22:46:14', '2022-08-29 12:04:40', 1, 'Q16738'),
(37355, 'Talamantes', 5113, 'Z', 207, 'ES', 41.73051000, -1.67857000, '2019-10-05 22:46:14', '2022-08-29 11:42:54', 1, 'Q1639321'),
(37356, 'Talarrubias', 5092, 'BA', 207, 'ES', 39.03697000, -5.23423000, '2019-10-05 22:46:14', '2022-08-28 18:09:23', 1, 'Q669707'),
(37357, 'Talavera La Real', 5092, 'BA', 207, 'ES', 38.87794000, -6.76856000, '2019-10-05 22:46:14', '2022-08-28 18:09:23', 1, 'Q1387075'),
(37358, 'Talavera de la Reina', 1205, 'TO', 207, 'ES', 39.96348000, -4.83076000, '2019-10-05 22:46:14', '2022-08-29 11:08:30', 1, 'Q23979439'),
(37359, 'Talaván', 1190, 'CC', 207, 'ES', 39.71715000, -6.28146000, '2019-10-05 22:46:14', '2022-08-28 18:12:22', 1, 'Q1640803'),
(37360, 'Talayuela', 1190, 'CC', 207, 'ES', 39.98701000, -5.60982000, '2019-10-05 22:46:14', '2022-08-28 18:12:22', 1, 'Q1442461'),
(37361, 'Tales', 5110, 'CS', 207, 'ES', 39.94844000, -0.30719000, '2019-10-05 22:46:14', '2022-08-29 11:26:42', 1, 'Q1442461'),
(37362, 'Tamajón', 5107, 'GU', 207, 'ES', 40.99914000, -3.24743000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q1442461'),
(37363, 'Tamames', 1147, 'SA', 207, 'ES', 40.65725000, -6.10536000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q1640615'),
(37364, 'Tamarit de Llitera / Tamarite de Litera', 1177, 'HU', 207, 'ES', 41.86910000, 0.42214000, '2019-10-05 22:46:14', '2022-08-29 12:06:20', 1, 'Q23992092'),
(37365, 'Tamariz de Campos', 1183, 'VA', 207, 'ES', 41.97706000, -5.02335000, '2019-10-05 22:46:14', '2022-08-29 11:48:45', 1, 'Q62128626'),
(37366, 'Tamarón', 1146, 'BU', 207, 'ES', 42.27386000, -3.99154000, '2019-10-05 22:46:14', '2022-08-29 11:24:20', 1, 'Q62128626'),
(37367, 'Tamurejo', 5092, 'BA', 207, 'ES', 38.98333000, -4.93333000, '2019-10-05 22:46:14', '2022-08-28 18:09:23', 1, 'Q1629497'),
(37368, 'Tanque', 1185, 'GC', 207, 'ES', 28.36667000, -16.83333000, '2019-10-05 22:46:14', '2022-08-29 12:06:32', 1, 'Q23979490'),
(37369, 'Tapioles', 1161, 'ZA', 207, 'ES', 41.85741000, -5.49623000, '2019-10-05 22:46:14', '2022-08-29 11:48:04', 1, 'Q1766884'),
(37370, 'Taradell', 5102, 'B', 207, 'ES', 41.87495000, 2.28662000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q24013984'),
(37371, 'Taragudo', 5107, 'GU', 207, 'ES', 40.82106000, -3.07680000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q24013984'),
(37372, 'Tarancón', 5106, 'CU', 207, 'ES', 40.00851000, -3.00731000, '2019-10-05 22:46:14', '2022-08-29 11:05:01', 1, 'Q24011487'),
(37373, 'Taravilla', 5107, 'GU', 207, 'ES', 40.69743000, -1.96817000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q339355'),
(37374, 'Tarazona', 5113, 'Z', 207, 'ES', 41.90475000, -1.72678000, '2019-10-05 22:46:14', '2022-08-29 11:42:54', 1, 'Q24019827'),
(37375, 'Tarazona de Guareña', 1147, 'SA', 207, 'ES', 41.17206000, -5.25005000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q1648232'),
(37377, 'Tardajos', 1146, 'BU', 207, 'ES', 42.34909000, -3.81700000, '2019-10-05 22:46:14', '2022-08-29 11:24:20', 1, 'Q940805'),
(37378, 'Tardelcuende', 1208, 'SO', 207, 'ES', 41.59402000, -2.64439000, '2019-10-05 22:46:14', '2022-08-29 11:51:23', 1, 'Q832238'),
(37379, 'Tardienta', 1177, 'HU', 207, 'ES', 41.97781000, -0.53731000, '2019-10-05 22:46:14', '2022-08-29 12:06:20', 1, 'Q904194'),
(37380, 'Tardáguila', 1147, 'SA', 207, 'ES', 41.11493000, -5.57423000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q1648252'),
(37381, 'Tarifa', 5096, 'CA', 207, 'ES', 36.01393000, -5.60695000, '2019-10-05 22:46:14', '2022-08-28 18:44:29', 1, 'Q222702'),
(37382, 'Taroda', 1208, 'SO', 207, 'ES', 41.34798000, -2.43318000, '2019-10-05 22:46:14', '2022-08-29 11:51:23', 1, 'Q830942'),
(37383, 'Tarragona', 1203, 'T', 207, 'ES', 41.11667000, 1.25000000, '2019-10-05 22:46:14', '2022-08-29 10:57:33', 1, 'Q1501940'),
(37384, 'Tartanedo', 5107, 'GU', 207, 'ES', 40.99347000, -1.92459000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q774047'),
(37385, 'Tauste', 5113, 'Z', 207, 'ES', 41.91804000, -1.25343000, '2019-10-05 22:46:14', '2022-08-29 11:42:54', 1, 'Q24019826'),
(37386, 'Tavernes Blanques', 1175, 'V', 207, 'ES', 39.50000000, -0.36667000, '2019-10-05 22:46:14', '2022-08-29 12:05:40', 1, 'Q24019826'),
(37387, 'Tavernes de la Valldigna', 1175, 'V', 207, 'ES', 39.07195000, -0.26623000, '2019-10-05 22:46:14', '2022-08-29 12:05:40', 1, 'Q24019826'),
(37388, 'Tavertet', 5102, 'B', 207, 'ES', 41.99572000, 2.41859000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q24012017'),
(37389, 'Tazacorte', 5112, 'TF', 207, 'ES', 28.64186000, -17.93394000, '2019-10-05 22:46:14', '2022-08-29 11:31:13', 1, 'Q426350'),
(37390, 'Teba', 5101, 'MA', 207, 'ES', 36.98358000, -4.91913000, '2019-10-05 22:46:14', '2022-08-28 19:06:53', 1, 'Q1630260'),
(37391, 'Tegueste', 5112, 'TF', 207, 'ES', 28.51667000, -16.31667000, '2019-10-05 22:46:14', '2022-08-29 11:31:13', 1, 'Q1630260'),
(37392, 'Teguise', 1185, 'GC', 207, 'ES', 29.06049000, -13.56397000, '2019-10-05 22:46:14', '2022-08-29 12:06:32', 1, 'Q4397801'),
(37393, 'Teià', 5102, 'B', 207, 'ES', 41.49804000, 2.32206000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q4397801'),
(37394, 'Tejada', 1146, 'BU', 207, 'ES', 41.95249000, -3.53514000, '2019-10-05 22:46:14', '2022-08-29 11:24:20', 1, 'Q4397801'),
(37395, 'Tejadillos', 5106, 'CU', 207, 'ES', 40.13333000, -1.63333000, '2019-10-05 22:46:14', '2022-08-29 11:05:01', 1, 'Q24011485'),
(37396, 'Tejado', 1208, 'SO', 207, 'ES', 41.58887000, -2.26721000, '2019-10-05 22:46:14', '2022-08-29 11:51:23', 1, 'Q837193'),
(37397, 'Tejeda', 1185, 'GC', 207, 'ES', 27.99508000, -15.61543000, '2019-10-05 22:46:14', '2022-08-29 12:06:32', 1, 'Q986361'),
(37398, 'Tejeda de Tiétar', 1190, 'CC', 207, 'ES', 40.01790000, -5.86953000, '2019-10-05 22:46:14', '2022-08-28 18:12:22', 1, 'Q1750981'),
(37399, 'Tejeda y Segoyuela', 1147, 'SA', 207, 'ES', 40.63155000, -6.02311000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q1648391'),
(37400, 'Telde', 1185, 'GC', 207, 'ES', 27.99243000, -15.41915000, '2019-10-05 22:46:14', '2022-08-29 12:06:32', 1, 'Q24013877'),
(37401, 'Tembleque', 1205, 'TO', 207, 'ES', 39.69541000, -3.50429000, '2019-10-05 22:46:14', '2022-08-29 11:08:30', 1, 'Q1313265'),
(37402, 'Tendilla', 5107, 'GU', 207, 'ES', 40.54379000, -2.95782000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q1120060'),
(37403, 'Tenebrón', 1147, 'SA', 207, 'ES', 40.62449000, -6.35450000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q1766869'),
(37404, 'Teo', 5089, 'C', 207, 'ES', 42.75000000, -8.50000000, '2019-10-05 22:46:14', '2022-08-28 13:37:17', 1, 'Q1635642'),
(37405, 'Teror', 1185, 'GC', 207, 'ES', 28.06062000, -15.54909000, '2019-10-05 22:46:14', '2022-08-29 12:06:32', 1, 'Q430147'),
(37406, 'Terque', 5095, 'AL', 207, 'ES', 36.98393000, -2.59679000, '2019-10-05 22:46:14', '2022-08-28 18:41:41', 1, 'Q1611807'),
(37407, 'Terradillos', 1147, 'SA', 207, 'ES', 40.83836000, -5.54160000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q1650177'),
(37408, 'Terradillos de Esgueva', 1146, 'BU', 207, 'ES', 41.81885000, -3.84318000, '2019-10-05 22:46:14', '2022-08-29 11:24:20', 1, 'Q634337'),
(37409, 'Terrassa', 5102, 'B', 207, 'ES', 41.56667000, 2.01667000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q634337'),
(37410, 'Terrateig', 1175, 'V', 207, 'ES', 38.89453000, -0.31993000, '2019-10-05 22:46:14', '2022-08-29 12:05:40', 1, 'Q2041965'),
(37411, 'Terrer', 5113, 'Z', 207, 'ES', 41.32811000, -1.71329000, '2019-10-05 22:46:14', '2022-08-29 11:42:54', 1, 'Q24019825'),
(37412, 'Terriente', 5111, 'TE', 207, 'ES', 40.29741000, -1.50399000, '2019-10-05 22:46:14', '2022-08-29 11:29:44', 1, 'Q1650823'),
(37413, 'Terrinches', 5105, 'CR', 207, 'ES', 38.61057000, -2.84215000, '2019-10-05 22:46:14', '2022-08-29 11:03:25', 1, 'Q1657914'),
(37414, 'Terroba', 1171, 'LO', 207, 'ES', 42.25809000, -2.44375000, '2019-10-05 22:46:14', '2022-08-29 12:05:09', 1, 'Q1636960'),
(37415, 'Teruel', 5111, 'TE', 207, 'ES', 40.34560000, -1.10646000, '2019-10-05 22:46:14', '2022-08-29 11:29:44', 1, 'Q55868696'),
(37416, 'Terzaga', 5107, 'GU', 207, 'ES', 40.69508000, -1.90386000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q1656858'),
(37417, 'Tetuán de las Victorias', 1158, 'M', 207, 'ES', 40.45975000, -3.69750000, '2019-10-05 22:46:14', '2022-08-29 12:04:40', 1, 'Q1773540'),
(37418, 'Teulada', 5108, 'A', 207, 'ES', 38.72940000, 0.10383000, '2019-10-05 22:46:14', '2022-08-29 11:15:48', 1, 'Q1773540'),
(37419, 'Tiana', 5102, 'B', 207, 'ES', 41.48201000, 2.26702000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q1773540'),
(37420, 'Tibi', 5108, 'A', 207, 'ES', 38.53072000, -0.57776000, '2019-10-05 22:46:14', '2022-08-29 11:15:48', 1, 'Q1751457'),
(37421, 'Tiedra', 1183, 'VA', 207, 'ES', 41.65239000, -5.26688000, '2019-10-05 22:46:14', '2022-08-29 11:48:45', 1, 'Q1916434'),
(37422, 'Tielmes', 1158, 'M', 207, 'ES', 40.24652000, -3.31461000, '2019-10-05 22:46:14', '2022-08-29 12:04:40', 1, 'Q1766203'),
(37423, 'Tierz', 1177, 'HU', 207, 'ES', 42.13356000, -0.35489000, '2019-10-05 22:46:14', '2022-08-29 12:06:20', 1, 'Q2047626'),
(37424, 'Tierzo', 5107, 'GU', 207, 'ES', 40.74925000, -1.93069000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q593530'),
(37425, 'Tijarafe', 5112, 'TF', 207, 'ES', 28.70000000, -17.95000000, '2019-10-05 22:46:14', '2022-08-29 11:31:13', 1, 'Q434286'),
(37426, 'Tinajas', 5106, 'CU', 207, 'ES', 40.32547000, -2.58228000, '2019-10-05 22:46:14', '2022-08-29 11:05:01', 1, 'Q24011484'),
(37427, 'Tinajo', 1185, 'GC', 207, 'ES', 29.06326000, -13.67647000, '2019-10-05 22:46:14', '2022-08-29 12:06:32', 1, 'Q24011484'),
(37428, 'Tirapu', 1204, 'NA', 207, 'ES', 42.65740000, -1.70263000, '2019-10-05 22:46:14', '2022-08-29 12:06:07', 1, 'Q1647412'),
(37429, 'Tirgo', 1171, 'LO', 207, 'ES', 42.54587000, -2.94940000, '2019-10-05 22:46:14', '2022-08-29 12:05:09', 1, 'Q681489'),
(37430, 'Titaguas', 1175, 'V', 207, 'ES', 39.86667000, -1.08333000, '2019-10-05 22:46:14', '2022-08-29 12:05:40', 1, 'Q2041464'),
(37431, 'Titulcia', 1158, 'M', 207, 'ES', 40.13537000, -3.56763000, '2019-10-05 22:46:14', '2022-08-29 12:04:40', 1, 'Q1647106'),
(37432, 'Tiurana', 5104, 'L', 207, 'ES', 41.97527000, 1.25608000, '2019-10-05 22:46:14', '2022-08-29 10:55:25', 1, 'Q1647106'),
(37433, 'Tivenys', 1203, 'T', 207, 'ES', 40.90787000, 0.51236000, '2019-10-05 22:46:14', '2022-08-29 10:57:33', 1, 'Q1647106'),
(37434, 'Tiñosillos', 1189, 'AV', 207, 'ES', 40.93400000, -4.72767000, '2019-10-05 22:46:14', '2022-08-29 11:49:57', 1, 'Q1610960'),
(37435, 'Tobar', 1146, 'BU', 207, 'ES', 42.48384000, -3.93984000, '2019-10-05 22:46:14', '2022-08-29 11:24:20', 1, 'Q1641356'),
(37437, 'Tobed', 5113, 'Z', 207, 'ES', 41.33866000, -1.39975000, '2019-10-05 22:46:14', '2022-08-29 11:42:54', 1, 'Q1650660'),
(37438, 'Tobía', 1171, 'LO', 207, 'ES', 42.29846000, -2.81399000, '2019-10-05 22:46:14', '2022-08-29 12:05:09', 1, 'Q1636283'),
(37439, 'Tocina', 1193, 'SE', 207, 'ES', 37.60904000, -5.73403000, '2019-10-05 22:46:14', '2022-08-28 19:08:49', 1, 'Q63004'),
(37440, 'Todolella', 5110, 'CS', 207, 'ES', 40.64675000, -0.24675000, '2019-10-05 22:46:14', '2022-08-29 11:26:43', 1, 'Q63004'),
(37441, 'Toga', 5110, 'CS', 207, 'ES', 40.05000000, -0.36667000, '2019-10-05 22:46:14', '2022-08-29 11:26:43', 1, 'Q1992572'),
(37442, 'Tolbaños', 1189, 'AV', 207, 'ES', 40.75168000, -4.58191000, '2019-10-05 22:46:14', '2022-08-29 11:49:57', 1, 'Q1628112'),
(37443, 'Toledo', 1205, 'TO', 207, 'ES', 39.85810000, -4.02263000, '2019-10-05 22:46:14', '2022-08-29 11:08:30', 1, 'Q51275630'),
(37444, 'Tollos', 5108, 'A', 207, 'ES', 38.75629000, -0.27466000, '2019-10-05 22:46:14', '2022-08-29 11:15:48', 1, 'Q1750925'),
(37445, 'Tolocirio', 1192, 'SG', 207, 'ES', 41.13425000, -4.65121000, '2019-10-05 22:46:14', '2022-08-29 11:50:43', 1, 'Q1917652'),
(37447, 'Tolosa', 1191, 'SS', 207, 'ES', 43.13484000, -2.07801000, '2019-10-05 22:46:14', '2022-08-28 18:36:50', 1, 'Q4892347'),
(37448, 'Tolox', 5101, 'MA', 207, 'ES', 36.68721000, -4.90511000, '2019-10-05 22:46:14', '2022-08-28 19:06:53', 1, 'Q743326'),
(37449, 'Tomares', 1193, 'SE', 207, 'ES', 37.37281000, -6.04589000, '2019-10-05 22:46:14', '2022-08-28 19:08:49', 1, 'Q1447544'),
(37450, 'Tomelloso', 5105, 'CR', 207, 'ES', 39.15759000, -3.02156000, '2019-10-05 22:46:14', '2022-08-29 11:03:25', 1, 'Q820229'),
(37451, 'Tomiño', 1167, 'PO', 207, 'ES', 41.98772000, -8.75502000, '2019-10-05 22:46:14', '2022-08-28 17:57:54', 1, 'Q820229'),
(37452, 'Tona', 5102, 'B', 207, 'ES', 41.84789000, 2.22808000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q820229'),
(37453, 'Topas', 1147, 'SA', 207, 'ES', 41.15759000, -5.63402000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q24014720'),
(37454, 'Toral de los Guzmanes', 1200, 'LE', 207, 'ES', 42.24274000, -5.56771000, '2019-10-05 22:46:14', '2019-10-05 22:46:14', 1, 'Q1640206'),
(37455, 'Tordehumos', 1183, 'VA', 207, 'ES', 41.81531000, -5.15811000, '2019-10-05 22:46:14', '2022-08-29 11:48:45', 1, 'Q24016887'),
(37456, 'Tordellego', 5107, 'GU', 207, 'ES', 40.72165000, -1.67036000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q584623'),
(37457, 'Tordelrábano', 5107, 'GU', 207, 'ES', 41.21827000, -2.75792000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q1658074'),
(37458, 'Tordera', 5102, 'B', 207, 'ES', 41.69914000, 2.71888000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q24022126'),
(37459, 'Tordesillas', 1183, 'VA', 207, 'ES', 41.50202000, -5.00146000, '2019-10-05 22:46:14', '2022-08-29 11:48:45', 1, 'Q286004'),
(37460, 'Tordesilos', 5107, 'GU', 207, 'ES', 40.67074000, -1.59372000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q1656949'),
(37461, 'Tordillos', 1147, 'SA', 207, 'ES', 40.85266000, -5.35278000, '2019-10-05 22:46:14', '2022-08-29 11:44:52', 1, 'Q1648261'),
(37462, 'Tordómar', 1146, 'BU', 207, 'ES', 42.04630000, -3.86514000, '2019-10-05 22:46:14', '2022-08-29 11:24:20', 1, 'Q24010341'),
(37463, 'Torelló', 5102, 'B', 207, 'ES', 42.04627000, 2.26679000, '2019-10-05 22:46:14', '2022-08-29 10:50:01', 1, 'Q13854'),
(37464, 'Toreno', 1200, 'LE', 207, 'ES', 42.69955000, -6.51236000, '2019-10-05 22:46:14', '2019-10-05 22:46:14', 1, 'Q44913'),
(37465, 'Torija', 5107, 'GU', 207, 'ES', 40.74251000, -3.02830000, '2019-10-05 22:46:14', '2022-08-29 11:06:46', 1, 'Q31925224'),
(37466, 'Toril', 1190, 'CC', 207, 'ES', 39.89749000, -5.77950000, '2019-10-05 22:46:14', '2022-08-28 18:12:22', 1, 'Q31925224'),
(37467, 'Torlengua', 1208, 'SO', 207, 'ES', 41.45483000, -2.16183000, '2019-10-05 22:46:14', '2022-08-29 11:51:23', 1, 'Q832894'),
(37468, 'Tormantos', 1171, 'LO', 207, 'ES', 42.49431000, -3.07446000, '2019-10-05 22:46:15', '2022-08-29 12:05:09', 1, 'Q1645994'),
(37469, 'Tormellas', 1189, 'AV', 207, 'ES', 40.30404000, -5.51185000, '2019-10-05 22:46:15', '2022-08-29 11:49:57', 1, 'Q1607010'),
(37470, 'Tormos', 5108, 'A', 207, 'ES', 38.80143000, -0.07160000, '2019-10-05 22:46:15', '2022-08-29 11:15:48', 1, 'Q578908'),
(37471, 'Tormón', 5111, 'TE', 207, 'ES', 40.20266000, -1.35406000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1641534'),
(37472, 'Tornabous', 5104, 'L', 207, 'ES', 41.70117000, 1.05384000, '2019-10-05 22:46:15', '2022-08-29 10:55:25', 1, 'Q785963'),
(37473, 'Tornadizos de Ávila', 1189, 'AV', 207, 'ES', 40.62757000, -4.61426000, '2019-10-05 22:46:15', '2022-08-29 11:49:57', 1, 'Q1627969'),
(37474, 'Tornavacas', 1190, 'CC', 207, 'ES', 40.25498000, -5.68876000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1626858'),
(37475, 'Tornos', 5111, 'TE', 207, 'ES', 40.96188000, -1.43389000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1651016'),
(37476, 'Toro', 1161, 'ZA', 207, 'ES', 41.52417000, -5.39534000, '2019-10-05 22:46:15', '2022-08-29 11:48:04', 1, 'Q1651016'),
(37477, 'Torquemada', 1157, 'P', 207, 'ES', 42.03490000, -4.31841000, '2019-10-05 22:46:15', '2022-08-29 11:45:45', 1, 'Q603164'),
(37478, 'Torralba', 5106, 'CU', 207, 'ES', 40.30253000, -2.28546000, '2019-10-05 22:46:15', '2022-08-29 11:05:01', 1, 'Q603164'),
(37479, 'Torralba de Aragón', 1177, 'HU', 207, 'ES', 41.93487000, -0.51053000, '2019-10-05 22:46:15', '2022-08-29 12:06:20', 1, 'Q541612'),
(37480, 'Torralba de Calatrava', 5105, 'CR', 207, 'ES', 39.01785000, -3.75105000, '2019-10-05 22:46:15', '2022-08-29 11:03:25', 1, 'Q928368'),
(37481, 'Torralba de Oropesa', 1205, 'TO', 207, 'ES', 39.93384000, -5.15404000, '2019-10-05 22:46:15', '2022-08-29 11:08:30', 1, 'Q24017269'),
(37482, 'Torralba de Ribota', 5113, 'Z', 207, 'ES', 41.41803000, -1.68429000, '2019-10-05 22:46:15', '2022-08-29 11:42:54', 1, 'Q24019822'),
(37483, 'Torralba de los Frailes', 5113, 'Z', 207, 'ES', 41.03622000, -1.66046000, '2019-10-05 22:46:15', '2022-08-29 11:42:54', 1, 'Q1639425'),
(37484, 'Torralba de los Sisones', 5111, 'TE', 207, 'ES', 40.89084000, -1.45866000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1650900'),
(37485, 'Torralba del Pinar', 5110, 'CS', 207, 'ES', 39.98333000, -0.43333000, '2019-10-05 22:46:15', '2022-08-29 11:26:43', 1, 'Q1646877'),
(37486, 'Torralbilla', 5113, 'Z', 207, 'ES', 41.21009000, -1.33800000, '2019-10-05 22:46:15', '2022-08-29 11:42:54', 1, 'Q1638854'),
(37487, 'Torre Alháquime', 5096, 'CA', 207, 'ES', 36.91588000, -5.23381000, '2019-10-05 22:46:15', '2022-08-28 18:44:29', 1, 'Q918626'),
(37489, 'Torre Val de San Pedro', 1192, 'SG', 207, 'ES', 41.07534000, -3.87116000, '2019-10-05 22:46:15', '2022-08-29 11:50:43', 1, 'Q1938397'),
(37490, 'Torre de Arcas', 5111, 'TE', 207, 'ES', 40.75137000, -0.06892000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1650917'),
(37491, 'Torre de Don Miguel', 1190, 'CC', 207, 'ES', 40.22333000, -6.57686000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1641468'),
(37492, 'Torre de Esgueva', 1183, 'VA', 207, 'ES', 41.76821000, -4.20003000, '2019-10-05 22:46:15', '2022-08-29 11:48:45', 1, 'Q1765406'),
(37493, 'Torre de Juan Abad', 5105, 'CR', 207, 'ES', 38.58417000, -3.05994000, '2019-10-05 22:46:15', '2022-08-29 11:03:25', 1, 'Q1640790'),
(37494, 'Torre de Miguel Sesmero', 5092, 'BA', 207, 'ES', 38.61913000, -6.79580000, '2019-10-05 22:46:15', '2022-08-28 18:09:23', 1, 'Q1613555'),
(37495, 'Torre de Peñafiel', 1183, 'VA', 207, 'ES', 41.53646000, -4.08833000, '2019-10-05 22:46:15', '2022-08-29 11:48:45', 1, 'Q1651708'),
(37496, 'Torre de Santa María', 1190, 'CC', 207, 'ES', 39.25441000, -6.11610000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1183759'),
(37497, 'Torre de la Horadada', 1175, 'V', 207, 'ES', 37.86970000, -0.75840000, '2019-10-05 22:46:15', '2022-08-29 12:05:40', 1, 'Q4894850'),
(37498, 'Torre de las Arcas', 5111, 'TE', 207, 'ES', 40.84166000, -0.71783000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1650799'),
(37499, 'Torre del Bierzo', 1200, 'LE', 207, 'ES', 42.60769000, -6.36675000, '2019-10-05 22:46:15', '2019-10-05 22:46:15', 1, 'Q1605945'),
(37500, 'Torre del Burgo', 5107, 'GU', 207, 'ES', 40.79176000, -3.07723000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q1656972'),
(37501, 'Torre del Campo', 5100, 'J', 207, 'ES', 37.77051000, -3.89731000, '2019-10-05 22:46:15', '2022-08-28 19:04:30', 1, 'Q1765676'),
(37502, 'Torre del Compte', 5111, 'TE', 207, 'ES', 40.93625000, 0.10934000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1641245'),
(37504, 'Torre en Cameros', 1171, 'LO', 207, 'ES', 42.24133000, -2.51805000, '2019-10-05 22:46:15', '2022-08-29 12:05:09', 1, 'Q1636228'),
(37505, 'Torre los Negros', 5111, 'TE', 207, 'ES', 40.85308000, -1.09782000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1641538'),
(37506, 'Torre-Cardela', 5098, 'GR', 207, 'ES', 37.50456000, -3.35609000, '2019-10-05 22:46:15', '2022-08-28 18:52:58', 1, 'Q618392'),
(37507, 'Torre-Pacheco', 1176, 'MU', 207, 'ES', 37.74293000, -0.95396000, '2019-10-05 22:46:15', '2022-08-29 12:05:49', 1, 'Q949880'),
(37508, 'Torreadrada', 1192, 'SG', 207, 'ES', 41.44474000, -3.84000000, '2019-10-05 22:46:15', '2022-08-29 11:50:43', 1, 'Q2046725'),
(37509, 'Torreblacos', 1208, 'SO', 207, 'ES', 41.66927000, -2.87881000, '2019-10-05 22:46:15', '2022-08-29 11:51:23', 1, 'Q837156'),
(37510, 'Torreblanca', 5110, 'CS', 207, 'ES', 40.22033000, 0.19650000, '2019-10-05 22:46:15', '2022-08-29 11:26:43', 1, 'Q837156'),
(37511, 'Torreblascopedro', 5100, 'J', 207, 'ES', 37.99750000, -3.63780000, '2019-10-05 22:46:15', '2022-08-28 19:04:30', 1, 'Q1750949'),
(37512, 'Torrecaballeros', 1192, 'SG', 207, 'ES', 40.99211000, -4.02470000, '2019-10-05 22:46:15', '2022-08-29 11:50:43', 1, 'Q1917243'),
(37513, 'Torrecampo', 5097, 'CO', 207, 'ES', 38.46667000, -4.66667000, '2019-10-05 22:46:15', '2022-08-28 18:49:38', 1, 'Q975665'),
(37514, 'Torrechiva', 5110, 'CS', 207, 'ES', 40.05000000, -0.40000000, '2019-10-05 22:46:15', '2022-08-29 11:26:43', 1, 'Q1765434'),
(37515, 'Torrecilla de Alcañiz', 5111, 'TE', 207, 'ES', 40.96056000, -0.09077000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1650777'),
(37516, 'Torrecilla de la Abadesa', 1183, 'VA', 207, 'ES', 41.48498000, -5.08762000, '2019-10-05 22:46:15', '2022-08-29 11:48:45', 1, 'Q1765426'),
(37517, 'Torrecilla de la Jara', 1205, 'TO', 207, 'ES', 39.70425000, -4.77186000, '2019-10-05 22:46:15', '2022-08-29 11:08:30', 1, 'Q1641481'),
(37518, 'Torrecilla de la Orden', 1183, 'VA', 207, 'ES', 41.21885000, -5.22384000, '2019-10-05 22:46:15', '2022-08-29 11:48:45', 1, 'Q1907298'),
(37519, 'Torrecilla de la Torre', 1183, 'VA', 207, 'ES', 41.66716000, -5.04982000, '2019-10-05 22:46:15', '2022-08-29 11:48:45', 1, 'Q1765499'),
(37520, 'Torrecilla de los Ángeles', 1190, 'CC', 207, 'ES', 40.24788000, -6.41606000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1628823'),
(37521, 'Torrecilla del Monte', 1146, 'BU', 207, 'ES', 42.09490000, -3.69356000, '2019-10-05 22:46:15', '2022-08-29 11:24:20', 1, 'Q1628410'),
(37522, 'Torrecilla del Pinar', 1192, 'SG', 207, 'ES', 41.37360000, -4.03838000, '2019-10-05 22:46:15', '2022-08-29 11:50:43', 1, 'Q1628410'),
(37523, 'Torrecilla del Rebollar', 5111, 'TE', 207, 'ES', 40.90978000, -1.07244000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1651029'),
(37524, 'Torrecilla en Cameros', 1171, 'LO', 207, 'ES', 42.25532000, -2.63109000, '2019-10-05 22:46:15', '2022-08-29 12:05:09', 1, 'Q1637356'),
(37525, 'Torrecilla sobre Alesanco', 1171, 'LO', 207, 'ES', 42.40825000, -2.83379000, '2019-10-05 22:46:15', '2022-08-29 12:05:09', 1, 'Q1637487'),
(37526, 'Torrecillas de la Tiesa', 1190, 'CC', 207, 'ES', 39.56837000, -5.74238000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1628401'),
(37527, 'Torrecuadrada de Molina', 5107, 'GU', 207, 'ES', 40.74947000, -1.80707000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q1750989'),
(37528, 'Torrecuadradilla', 5107, 'GU', 207, 'ES', 40.85364000, -2.53215000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q1656901'),
(37529, 'Torredembarra', 1203, 'T', 207, 'ES', 41.14505000, 1.39861000, '2019-10-05 22:46:15', '2022-08-29 10:57:33', 1, 'Q1656901'),
(37530, 'Torredonjimeno', 5100, 'J', 207, 'ES', 37.76748000, -3.95776000, '2019-10-05 22:46:15', '2022-08-28 19:04:30', 1, 'Q1915208'),
(37531, 'Torrefarrera', 5104, 'L', 207, 'ES', 41.67318000, 0.60671000, '2019-10-05 22:46:15', '2022-08-29 10:55:25', 1, 'Q987375'),
(37532, 'Torregalindo', 1146, 'BU', 207, 'ES', 41.58299000, -3.75222000, '2019-10-05 22:46:15', '2022-08-29 11:24:20', 1, 'Q1751248'),
(37533, 'Torregamones', 1161, 'ZA', 207, 'ES', 41.48700000, -6.18335000, '2019-10-05 22:46:15', '2022-08-29 11:48:04', 1, 'Q1766483'),
(37534, 'Torrehermosa', 5113, 'Z', 207, 'ES', 41.23767000, -2.12800000, '2019-10-05 22:46:15', '2022-08-29 11:42:54', 1, 'Q663442'),
(37535, 'Torreiglesias', 1192, 'SG', 207, 'ES', 41.10236000, -4.03280000, '2019-10-05 22:46:15', '2022-08-29 11:50:43', 1, 'Q1938324'),
(37536, 'Torrejoncillo', 1190, 'CC', 207, 'ES', 39.89675000, -6.46712000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q521090'),
(37537, 'Torrejoncillo del Rey', 5106, 'CU', 207, 'ES', 40.00840000, -2.57107000, '2019-10-05 22:46:15', '2022-08-29 11:05:01', 1, 'Q24011483'),
(37538, 'Torrejón de Ardoz', 1158, 'M', 207, 'ES', 40.45535000, -3.46973000, '2019-10-05 22:46:15', '2022-08-29 12:04:40', 1, 'Q24012681'),
(37539, 'Torrejón de Velasco', 1158, 'M', 207, 'ES', 40.18746000, -3.77681000, '2019-10-05 22:46:15', '2022-08-29 12:04:40', 1, 'Q919911'),
(37540, 'Torrejón de la Calzada', 1158, 'M', 207, 'ES', 40.19886000, -3.79700000, '2019-10-05 22:46:15', '2022-08-29 12:04:40', 1, 'Q960213'),
(37541, 'Torrejón del Rey', 5107, 'GU', 207, 'ES', 40.64325000, -3.33376000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q748951'),
(37542, 'Torrejón el Rubio', 1190, 'CC', 207, 'ES', 39.77068000, -6.01260000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q615676'),
(37543, 'Torrelaguna', 1158, 'M', 207, 'ES', 40.82764000, -3.53683000, '2019-10-05 22:46:15', '2022-08-29 12:04:40', 1, 'Q1641236'),
(37544, 'Torrelapaja', 5113, 'Z', 207, 'ES', 41.58133000, -1.95181000, '2019-10-05 22:46:15', '2022-08-29 11:42:54', 1, 'Q1639206'),
(37545, 'Torrelara', 1146, 'BU', 207, 'ES', 42.16673000, -3.51689000, '2019-10-05 22:46:15', '2022-08-29 11:24:21', 1, 'Q1641169'),
(37546, 'Torrelavega', 1170, 'S', 207, 'ES', 43.34943000, -4.04785000, '2019-10-05 22:46:15', '2019-10-05 22:46:15', 1, 'Q23991428'),
(37547, 'Torrella', 1175, 'V', 207, 'ES', 38.98446000, -0.56727000, '2019-10-05 22:46:15', '2022-08-29 12:05:40', 1, 'Q23991428'),
(37548, 'Torrellas', 5113, 'Z', 207, 'ES', 41.89444000, -1.77139000, '2019-10-05 22:46:15', '2022-08-29 11:42:54', 1, 'Q1650599'),
(37549, 'Torrelles de Llobregat', 5102, 'B', 207, 'ES', 41.35000000, 1.98333000, '2019-10-05 22:46:15', '2022-08-29 10:50:01', 1, 'Q1650599'),
(37550, 'Torrelobatón', 1183, 'VA', 207, 'ES', 41.64942000, -5.02526000, '2019-10-05 22:46:15', '2022-08-29 11:48:45', 1, 'Q1651719'),
(37551, 'Torrelodones', 1158, 'M', 207, 'ES', 40.57654000, -3.92658000, '2019-10-05 22:46:15', '2022-08-29 12:04:40', 1, 'Q1651719'),
(37552, 'Torremayor', 5092, 'BA', 207, 'ES', 38.90140000, -6.53858000, '2019-10-05 22:46:15', '2022-08-28 18:09:23', 1, 'Q1613573'),
(37554, 'Torremenga', 1190, 'CC', 207, 'ES', 40.04658000, -5.77471000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1641394'),
(37555, 'Torremocha', 1190, 'CC', 207, 'ES', 39.34565000, -6.17335000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1626819'),
(37556, 'Torremocha de Jadraque', 5107, 'GU', 207, 'ES', 41.01881000, -2.89918000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q1656931'),
(37557, 'Torremocha de Jarama', 1158, 'M', 207, 'ES', 40.84148000, -3.49666000, '2019-10-05 22:46:15', '2022-08-29 12:04:40', 1, 'Q1765576'),
(37558, 'Torremocha del Campo', 5107, 'GU', 207, 'ES', 40.97819000, -2.61881000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q1656459'),
(37559, 'Torremocha del Pinar', 5107, 'GU', 207, 'ES', 40.88954000, -2.04497000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q958171'),
(37560, 'Torremochuela', 5107, 'GU', 207, 'ES', 40.76528000, -1.84190000, '2019-10-05 22:46:15', '2022-08-29 11:06:46', 1, 'Q1656880'),
(37561, 'Torremolinos', 5101, 'MA', 207, 'ES', 36.62035000, -4.49976000, '2019-10-05 22:46:15', '2022-08-28 19:06:53', 1, 'Q492737'),
(37562, 'Torremontalbo', 1171, 'LO', 207, 'ES', 42.50000000, -2.68333000, '2019-10-05 22:46:15', '2022-08-29 12:05:09', 1, 'Q1636263'),
(37563, 'Torremormojón', 1157, 'P', 207, 'ES', 41.96018000, -4.77765000, '2019-10-05 22:46:15', '2022-08-29 11:45:45', 1, 'Q1906728'),
(37564, 'Torrent', 1175, 'V', 207, 'ES', 39.43705000, -0.46546000, '2019-10-05 22:46:15', '2022-08-29 12:05:40', 1, 'Q1906728'),
(37565, 'Torrent', 5103, 'GI', 207, 'ES', 41.95243000, 3.12684000, '2019-10-05 22:46:15', '2022-08-29 10:53:16', 1, 'Q13572'),
(37567, 'Torrenueva', 5105, 'CR', 207, 'ES', 38.63960000, -3.36259000, '2019-10-05 22:46:15', '2022-08-29 11:03:25', 1, 'Q13572'),
(37568, 'Torreorgaz', 1190, 'CC', 207, 'ES', 39.38310000, -6.24941000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q749842'),
(37569, 'Torreperogil', 5100, 'J', 207, 'ES', 38.03540000, -3.28998000, '2019-10-05 22:46:15', '2022-08-28 19:04:30', 1, 'Q1642300'),
(37570, 'Torrequemada', 1190, 'CC', 207, 'ES', 39.36664000, -6.22052000, '2019-10-05 22:46:15', '2022-08-28 18:12:22', 1, 'Q1643596'),
(37571, 'Torres', 5100, 'J', 207, 'ES', 37.78537000, -3.50902000, '2019-10-05 22:46:15', '2022-08-28 19:04:30', 1, 'Q1643596'),
(37572, 'Torres de Albarracín', 5111, 'TE', 207, 'ES', 40.42709000, -1.53242000, '2019-10-05 22:46:15', '2022-08-29 11:29:44', 1, 'Q1650890'),
(37573, 'Torres de Alcanadre', 1177, 'HU', 207, 'ES', 41.96727000, -0.11096000, '2019-10-05 22:46:15', '2022-08-29 12:06:20', 1, 'Q53552'),
(37574, 'Torres de Barbués', 1177, 'HU', 207, 'ES', 41.96050000, -0.43350000, '2019-10-05 22:46:15', '2022-08-29 12:06:20', 1, 'Q984587'),
(37575, 'Torres de Berrellén', 5113, 'Z', 207, 'ES', 41.75797000, -1.06550000, '2019-10-05 22:46:15', '2022-08-29 11:42:54', 1, 'Q1628472'),
(37576, 'Torres de Segre', 5104, 'L', 207, 'ES', 41.53399000, 0.51420000, '2019-10-05 22:46:15', '2022-08-29 10:55:25', 1, 'Q599382'),
(37577, 'Torres de la Alameda', 1158, 'M', 207, 'ES', 40.40264000, -3.35767000, '2019-10-05 22:46:15', '2022-08-29 12:04:40', 1, 'Q1751912'),
(37578, 'Torres del Carrizal', 1161, 'ZA', 207, 'ES', 41.61701000, -5.67173000, '2019-10-05 22:46:16', '2022-08-29 11:48:04', 1, 'Q1652967'),
(37579, 'Torres del Río', 1204, 'NA', 207, 'ES', 42.55162000, -2.27285000, '2019-10-05 22:46:16', '2022-08-29 12:06:07', 1, 'Q1652967'),
(37580, 'Torresandino', 1146, 'BU', 207, 'ES', 41.82920000, -3.90981000, '2019-10-05 22:46:16', '2022-08-29 11:24:21', 1, 'Q1630680'),
(37581, 'Torrescárcela', 1183, 'VA', 207, 'ES', 41.48455000, -4.31920000, '2019-10-05 22:46:16', '2022-08-29 11:48:45', 1, 'Q731999'),
(37582, 'Torresmenudas', 1147, 'SA', 207, 'ES', 41.10249000, -5.78475000, '2019-10-05 22:46:16', '2022-08-29 11:44:52', 1, 'Q1648697'),
(37583, 'Torrevelilla', 5111, 'TE', 207, 'ES', 40.90200000, -0.10977000, '2019-10-05 22:46:16', '2022-08-29 11:29:44', 1, 'Q1553894'),
(37584, 'Torrevieja', 5108, 'A', 207, 'ES', 37.97872000, -0.68222000, '2019-10-05 22:46:16', '2022-08-29 11:15:48', 1, 'Q221749'),
(37585, 'Torrico', 1205, 'TO', 207, 'ES', 39.82918000, -5.22581000, '2019-10-05 22:46:16', '2022-08-29 11:08:30', 1, 'Q24017266'),
(37586, 'Torrijas', 5111, 'TE', 207, 'ES', 40.01667000, -0.95000000, '2019-10-05 22:46:16', '2022-08-29 11:29:44', 1, 'Q1651208'),
(37587, 'Torrijo de la Cañada', 5113, 'Z', 207, 'ES', 41.47304000, -1.87441000, '2019-10-05 22:46:16', '2022-08-29 11:42:54', 1, 'Q1650416'),
(37588, 'Torrijo del Campo', 5111, 'TE', 207, 'ES', 40.82575000, -1.33766000, '2019-10-05 22:46:16', '2022-08-29 11:29:44', 1, 'Q1650828'),
(37589, 'Torrijos', 1205, 'TO', 207, 'ES', 39.98195000, -4.28349000, '2019-10-05 22:46:16', '2022-08-29 11:08:30', 1, 'Q1650828'),
(37590, 'Torroella de Fluvià', 5103, 'GI', 207, 'ES', 42.17522000, 3.04025000, '2019-10-05 22:46:16', '2022-08-29 10:53:16', 1, 'Q1650828'),
(37591, 'Torroella de Montgrí', 5103, 'GI', 207, 'ES', 42.04254000, 3.12703000, '2019-10-05 22:46:16', '2022-08-29 10:53:17', 1, 'Q23991223'),
(37592, 'Torrox', 5101, 'MA', 207, 'ES', 36.75793000, -3.95233000, '2019-10-05 22:46:16', '2022-08-28 19:06:53', 1, 'Q13150'),
(37593, 'Torrubia', 5107, 'GU', 207, 'ES', 40.96581000, -1.90064000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q13150'),
(37594, 'Torrubia de Soria', 1208, 'SO', 207, 'ES', 41.63045000, -2.09151000, '2019-10-05 22:46:16', '2022-08-29 11:51:23', 1, 'Q834312'),
(37595, 'Torrubia del Campo', 5106, 'CU', 207, 'ES', 39.89749000, -2.96133000, '2019-10-05 22:46:16', '2022-08-29 11:05:01', 1, 'Q24011482'),
(37596, 'Torrubia del Castillo', 5106, 'CU', 207, 'ES', 39.65852000, -2.31171000, '2019-10-05 22:46:16', '2022-08-29 11:05:01', 1, 'Q771700'),
(37597, 'Tortosa', 1203, 'T', 207, 'ES', 40.81249000, 0.52160000, '2019-10-05 22:46:16', '2022-08-29 10:57:33', 1, 'Q12812'),
(37598, 'Tortuera', 5107, 'GU', 207, 'ES', 40.97181000, -1.79764000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q1656964'),
(37599, 'Tortuero', 5107, 'GU', 207, 'ES', 40.93414000, -3.35024000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q1752002'),
(37601, 'Torás', 5110, 'CS', 207, 'ES', 39.91667000, -0.68333000, '2019-10-05 22:46:16', '2022-08-29 11:26:43', 1, 'Q1983317'),
(37602, 'Tosantos', 1146, 'BU', 207, 'ES', 42.41368000, -3.24286000, '2019-10-05 22:46:16', '2022-08-29 11:24:21', 1, 'Q958974'),
(37603, 'Tosos', 5113, 'Z', 207, 'ES', 41.31542000, -1.07292000, '2019-10-05 22:46:16', '2022-08-29 11:42:54', 1, 'Q1639161'),
(37604, 'Tossa de Mar', 5103, 'GI', 207, 'ES', 41.71667000, 2.93333000, '2019-10-05 22:46:16', '2022-08-29 10:53:17', 1, 'Q1639161'),
(37605, 'Totalán', 5101, 'MA', 207, 'ES', 36.76526000, -4.29707000, '2019-10-05 22:46:16', '2022-08-28 19:06:53', 1, 'Q24012568'),
(37606, 'Totana', 1176, 'MU', 207, 'ES', 37.76880000, -1.50229000, '2019-10-05 22:46:16', '2022-08-29 12:05:49', 1, 'Q338174'),
(37607, 'Totanés', 1205, 'TO', 207, 'ES', 39.71057000, -4.22655000, '2019-10-05 22:46:16', '2022-08-29 11:08:30', 1, 'Q24017265'),
(37608, 'Touro', 5089, 'C', 207, 'ES', 42.86812000, -8.30764000, '2019-10-05 22:46:16', '2022-08-28 13:37:17', 1, 'Q904203'),
(37609, 'Tous', 1175, 'V', 207, 'ES', 39.13951000, -0.58777000, '2019-10-05 22:46:16', '2022-08-29 12:05:40', 1, 'Q904203'),
(37610, 'Toén', 5091, 'OR', 207, 'ES', 42.31765000, -7.95435000, '2019-10-05 22:46:16', '2022-08-28 17:53:26', 1, 'Q943030'),
(37611, 'Trabada', 5090, 'LU', 207, 'ES', 43.44475000, -7.19527000, '2019-10-05 22:46:16', '2022-08-28 17:49:36', 1, 'Q943030'),
(37612, 'Trabadelo', 1200, 'LE', 207, 'ES', 42.64874000, -6.88000000, '2019-10-05 22:46:16', '2019-10-05 22:46:16', 1, 'Q842698'),
(37613, 'Trabanca', 1147, 'SA', 207, 'ES', 41.23278000, -6.38484000, '2019-10-05 22:46:16', '2022-08-29 11:44:52', 1, 'Q1648369'),
(37614, 'Trabazos', 1161, 'ZA', 207, 'ES', 41.74793000, -6.49094000, '2019-10-05 22:46:16', '2022-08-29 11:48:04', 1, 'Q936798'),
(37615, 'Traiguera', 5110, 'CS', 207, 'ES', 40.52511000, 0.29023000, '2019-10-05 22:46:16', '2022-08-29 11:26:43', 1, 'Q1645728'),
(37616, 'Tramacastiel', 5111, 'TE', 207, 'ES', 40.18842000, -1.24081000, '2019-10-05 22:46:16', '2022-08-29 11:29:44', 1, 'Q669114'),
(37617, 'Tramacastilla', 5111, 'TE', 207, 'ES', 40.43059000, -1.57466000, '2019-10-05 22:46:16', '2022-08-29 11:29:44', 1, 'Q1651178'),
(37618, 'Tramaced', 1177, 'HU', 207, 'ES', 41.97318000, -0.29752000, '2019-10-05 22:46:16', '2022-08-29 12:06:20', 1, 'Q984627'),
(37619, 'Trasierra', 5092, 'BA', 207, 'ES', 38.18333000, -6.00000000, '2019-10-05 22:46:16', '2022-08-28 18:09:23', 1, 'Q952282'),
(37620, 'Trasmiras', 5091, 'OR', 207, 'ES', 42.02540000, -7.61735000, '2019-10-05 22:46:16', '2022-08-28 17:53:26', 1, 'Q917185'),
(37621, 'Trasmoz', 5113, 'Z', 207, 'ES', 41.82599000, -1.72279000, '2019-10-05 22:46:16', '2022-08-29 11:42:55', 1, 'Q64167231'),
(37622, 'Trasobares', 5113, 'Z', 207, 'ES', 41.64308000, -1.64192000, '2019-10-05 22:46:16', '2022-08-29 11:42:55', 1, 'Q1639140'),
(37623, 'Traspinedo', 1183, 'VA', 207, 'ES', 41.57509000, -4.47569000, '2019-10-05 22:46:16', '2022-08-29 11:48:45', 1, 'Q1764593'),
(37624, 'Trazo', 5089, 'C', 207, 'ES', 43.01667000, -8.53333000, '2019-10-05 22:46:16', '2022-08-28 13:37:17', 1, 'Q1616102'),
(37625, 'Trebujena', 5096, 'CA', 207, 'ES', 36.87075000, -6.17586000, '2019-10-05 22:46:16', '2022-08-28 18:44:29', 1, 'Q1610648'),
(37626, 'Trefacio', 1161, 'ZA', 207, 'ES', 42.12165000, -6.65407000, '2019-10-05 22:46:16', '2022-08-29 11:48:04', 1, 'Q1752362'),
(37627, 'Tremedal de Tormes', 1147, 'SA', 207, 'ES', 41.07423000, -6.18164000, '2019-10-05 22:46:16', '2022-08-29 11:44:52', 1, 'Q519470'),
(37628, 'Tremp', 5104, 'L', 207, 'ES', 42.16703000, 0.89487000, '2019-10-05 22:46:16', '2022-08-29 10:55:25', 1, 'Q519470'),
(37629, 'Tres Cantos', 1158, 'M', 207, 'ES', 40.60092000, -3.70806000, '2019-10-05 22:46:16', '2022-08-29 12:04:40', 1, 'Q24012644'),
(37630, 'Trescasas', 1192, 'SG', 207, 'ES', 40.96442000, -4.03590000, '2019-10-05 22:46:16', '2022-08-29 11:50:43', 1, 'Q1938334'),
(37631, 'Tresjuncos', 5106, 'CU', 207, 'ES', 39.70087000, -2.75502000, '2019-10-05 22:46:16', '2022-08-29 11:05:01', 1, 'Q1767763'),
(37632, 'Trespaderne', 1146, 'BU', 207, 'ES', 42.80221000, -3.38989000, '2019-10-05 22:46:16', '2022-08-29 11:24:21', 1, 'Q1643937'),
(37633, 'Treviana', 1171, 'LO', 207, 'ES', 42.55901000, -3.05032000, '2019-10-05 22:46:16', '2022-08-29 12:05:09', 1, 'Q1768222'),
(37634, 'Trevélez', 5098, 'GR', 207, 'ES', 37.00037000, -3.26545000, '2019-10-05 22:46:16', '2022-08-28 18:52:58', 1, 'Q618381'),
(37635, 'Tribaldos', 5106, 'CU', 207, 'ES', 39.97264000, -2.89809000, '2019-10-05 22:46:16', '2022-08-29 11:05:01', 1, 'Q24011479'),
(37636, 'Tricio', 1171, 'LO', 207, 'ES', 42.40223000, -2.71912000, '2019-10-05 22:46:16', '2022-08-29 12:05:09', 1, 'Q1646043'),
(37637, 'Trigueros', 5099, 'H', 207, 'ES', 37.38606000, -6.82680000, '2019-10-05 22:46:16', '2022-08-28 19:00:43', 1, 'Q1614522'),
(37638, 'Trigueros del Valle', 1183, 'VA', 207, 'ES', 41.83049000, -4.65179000, '2019-10-05 22:46:16', '2022-08-29 11:48:45', 1, 'Q1907256'),
(37639, 'Trijueque', 5107, 'GU', 207, 'ES', 40.77426000, -2.99253000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q1750972'),
(37640, 'Trillo', 5107, 'GU', 207, 'ES', 40.70086000, -2.59265000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q1768232'),
(37641, 'Triollo', 1157, 'P', 207, 'ES', 42.92456000, -4.68130000, '2019-10-05 22:46:16', '2022-08-29 11:45:45', 1, 'Q1906984'),
(37642, 'Tronchón', 5111, 'TE', 207, 'ES', 40.62091000, -0.39833000, '2019-10-05 22:46:16', '2022-08-29 11:29:44', 1, 'Q1768483'),
(37643, 'Truchas', 1200, 'LE', 207, 'ES', 42.26093000, -6.43605000, '2019-10-05 22:46:16', '2019-10-05 22:46:16', 1, 'Q1774781'),
(37644, 'Trujillanos', 5092, 'BA', 207, 'ES', 38.95233000, -6.25759000, '2019-10-05 22:46:16', '2022-08-28 18:09:23', 1, 'Q2572293'),
(37645, 'Trujillo', 1190, 'CC', 207, 'ES', 39.45786000, -5.88203000, '2019-10-05 22:46:16', '2022-08-28 18:12:22', 1, 'Q23979008'),
(37646, 'Tubilla del Agua', 1146, 'BU', 207, 'ES', 42.70932000, -3.80116000, '2019-10-05 22:46:16', '2022-08-29 11:24:21', 1, 'Q593383'),
(37647, 'Tubilla del Lago', 1146, 'BU', 207, 'ES', 41.80154000, -3.58625000, '2019-10-05 22:46:16', '2022-08-29 11:24:21', 1, 'Q373254'),
(37648, 'Tudela', 1204, 'NA', 207, 'ES', 42.06166000, -1.60452000, '2019-10-05 22:46:16', '2022-08-29 12:06:07', 1, 'Q373254'),
(37649, 'Tudela de Duero', 1183, 'VA', 207, 'ES', 41.58450000, -4.58093000, '2019-10-05 22:46:16', '2022-08-29 11:48:45', 1, 'Q1651692'),
(37650, 'Tudelilla', 1171, 'LO', 207, 'ES', 42.30023000, -2.11749000, '2019-10-05 22:46:16', '2022-08-29 12:05:09', 1, 'Q1646935'),
(37651, 'Tui', 1167, 'PO', 207, 'ES', 42.04713000, -8.64435000, '2019-10-05 22:46:16', '2022-08-28 17:57:54', 1, 'Q1646935'),
(37652, 'Tuineje', 1185, 'GC', 207, 'ES', 28.32372000, -14.04722000, '2019-10-05 22:46:16', '2022-08-29 12:06:32', 1, 'Q1646935'),
(37653, 'Tulebras', 1204, 'NA', 207, 'ES', 41.97695000, -1.67618000, '2019-10-05 22:46:16', '2022-08-29 12:06:07', 1, 'Q631828'),
(37654, 'Turcia', 1200, 'LE', 207, 'ES', 42.53431000, -5.87844000, '2019-10-05 22:46:16', '2019-10-05 22:46:16', 1, 'Q1635258'),
(37655, 'Turleque', 1205, 'TO', 207, 'ES', 39.60138000, -3.61404000, '2019-10-05 22:46:16', '2022-08-29 11:08:30', 1, 'Q1643004'),
(37656, 'Turre', 5095, 'AL', 207, 'ES', 37.15224000, -1.89497000, '2019-10-05 22:46:16', '2022-08-28 18:41:41', 1, 'Q1358302'),
(37657, 'Turrillas', 5095, 'AL', 207, 'ES', 37.02948000, -2.26607000, '2019-10-05 22:46:16', '2022-08-28 18:41:41', 1, 'Q1406739'),
(37658, 'Turégano', 1192, 'SG', 207, 'ES', 41.15610000, -4.00696000, '2019-10-05 22:46:16', '2022-08-29 11:50:43', 1, 'Q1916641'),
(37659, 'Turís', 1175, 'V', 207, 'ES', 39.38333000, -0.70000000, '2019-10-05 22:46:16', '2022-08-29 12:05:40', 1, 'Q1916641'),
(37660, 'Tuéjar', 1175, 'V', 207, 'ES', 39.76667000, -1.03333000, '2019-10-05 22:46:16', '2022-08-29 12:05:40', 1, 'Q1993055'),
(37661, 'Tàrrega', 5104, 'L', 207, 'ES', 41.64704000, 1.13957000, '2019-10-05 22:46:16', '2022-08-29 10:55:25', 1, 'Q1993055'),
(37662, 'Tábara', 1161, 'ZA', 207, 'ES', 41.82458000, -5.96420000, '2019-10-05 22:46:16', '2022-08-29 11:48:04', 1, 'Q1752406'),
(37663, 'Táliga', 5092, 'BA', 207, 'ES', 38.52861000, -7.01713000, '2019-10-05 22:46:16', '2022-08-28 18:09:23', 1, 'Q1613535'),
(37664, 'Tébar', 5106, 'CU', 207, 'ES', 39.50000000, -2.16667000, '2019-10-05 22:46:16', '2022-08-29 11:05:01', 1, 'Q24011486'),
(37665, 'Térmens', 5104, 'L', 207, 'ES', 41.71667000, 0.76667000, '2019-10-05 22:46:16', '2022-08-29 10:55:25', 1, 'Q1766633'),
(37666, 'Tías', 1185, 'GC', 207, 'ES', 28.96108000, -13.64502000, '2019-10-05 22:46:16', '2022-08-29 12:06:32', 1, 'Q2046900'),
(37667, 'Tíjola', 5095, 'AL', 207, 'ES', 37.34606000, -2.43326000, '2019-10-05 22:46:16', '2022-08-28 18:41:41', 1, 'Q284894'),
(37668, 'Tórtola de Henares', 5107, 'GU', 207, 'ES', 40.70425000, -3.12316000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q548550'),
(37669, 'Tórtoles', 1189, 'AV', 207, 'ES', 40.56120000, -5.26120000, '2019-10-05 22:46:16', '2022-08-29 11:49:57', 1, 'Q1606741'),
(37670, 'Ubrique', 5096, 'CA', 207, 'ES', 36.67777000, -5.44600000, '2019-10-05 22:46:16', '2022-08-28 18:44:29', 1, 'Q1092146'),
(37671, 'Uceda', 5107, 'GU', 207, 'ES', 40.83944000, -3.46040000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q1641757'),
(37672, 'Ucero', 1208, 'SO', 207, 'ES', 41.71685000, -3.05154000, '2019-10-05 22:46:16', '2022-08-29 11:51:23', 1, 'Q835085'),
(37673, 'Uclés', 5106, 'CU', 207, 'ES', 39.97938000, -2.86143000, '2019-10-05 22:46:16', '2022-08-29 11:05:01', 1, 'Q24011478'),
(37674, 'Ugena', 1205, 'TO', 207, 'ES', 40.15572000, -3.87603000, '2019-10-05 22:46:16', '2022-08-29 11:08:30', 1, 'Q1631336'),
(37675, 'Ugíjar', 5098, 'GR', 207, 'ES', 36.96087000, -3.05523000, '2019-10-05 22:46:16', '2022-08-28 18:52:58', 1, 'Q545852'),
(37676, 'Ujados', 5107, 'GU', 207, 'ES', 41.23526000, -3.00482000, '2019-10-05 22:46:16', '2022-08-29 11:06:46', 1, 'Q740155'),
(37677, 'Ujué', 1204, 'NA', 207, 'ES', 42.50000000, -1.50000000, '2019-10-05 22:46:16', '2022-08-29 12:06:07', 1, 'Q943072'),
(37678, 'Ulea', 1176, 'MU', 207, 'ES', 38.14045000, -1.33007000, '2019-10-05 22:46:16', '2022-08-29 12:05:49', 1, 'Q1767758'),
(37679, 'Uleila del Campo', 5095, 'AL', 207, 'ES', 37.18460000, -2.20491000, '2019-10-05 22:46:16', '2022-08-28 18:41:41', 1, 'Q1447729'),
(37680, 'Ullastrell', 5102, 'B', 207, 'ES', 41.52643000, 1.95537000, '2019-10-05 22:46:16', '2022-08-29 10:50:01', 1, 'Q13944'),
(37681, 'Ulldecona', 1203, 'T', 207, 'ES', 40.59734000, 0.44718000, '2019-10-05 22:46:16', '2022-08-29 10:57:33', 1, 'Q23991324'),
(37682, 'Ulldemolins', 1203, 'T', 207, 'ES', 41.32216000, 0.87650000, '2019-10-05 22:46:16', '2022-08-29 10:57:33', 1, 'Q23991324'),
(37683, 'Ullà', 5103, 'GI', 207, 'ES', 42.04964000, 3.10754000, '2019-10-05 22:46:16', '2022-08-29 10:53:17', 1, 'Q13592'),
(37684, 'Ultramort', 5103, 'GI', 207, 'ES', 42.03640000, 3.03455000, '2019-10-05 22:46:16', '2022-08-29 10:53:17', 1, 'Q13588'),
(37685, 'Umbrete', 1193, 'SE', 207, 'ES', 37.36881000, -6.15847000, '2019-10-05 22:46:16', '2022-08-28 19:08:49', 1, 'Q602485'),
(37686, 'Umbrías', 1189, 'AV', 207, 'ES', 40.31530000, -5.58037000, '2019-10-05 22:46:16', '2022-08-29 11:49:57', 1, 'Q1645654'),
(37687, 'Uncastillo', 5113, 'Z', 207, 'ES', 42.35963000, -1.12842000, '2019-10-05 22:46:16', '2022-08-29 11:42:55', 1, 'Q23991339');

