INSERT INTO `cities` (`id`, `name`, `state_id`, `state_code`, `country_id`, `country_code`, `latitude`, `longitude`, `created_at`, `updated_at`, `flag`, `wikiDataId`) VALUES
(40800, 'Charleval', 4812, 'PAC', 75, 'FR', 43.71864000, 5.24546000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q212793'),
(40801, 'Charleval', 4804, 'NOR', 75, 'FR', 49.37290000, 1.38369000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q212793'),
(40802, 'Charleville-Mézières', 4820, 'GES', 75, 'FR', 49.76850000, 4.72487000, '2019-10-05 22:47:52', '2020-05-01 17:22:44', 1, 'Q2960786'),
(40803, '<PERSON>u', 4798, 'ARA', 75, 'FR', 46.16202000, 4.17228000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q688187'),
(40804, '<PERSON>rly', 4798, 'ARA', 75, 'FR', 45.64887000, 4.79461000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q688187'),
(40805, 'Charly-sur-Marne', 4828, 'HDF', 75, 'FR', 48.97749000, 3.28464000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q34791996'),
(40806, 'Charmes', 4820, 'GES', 75, 'FR', 48.37220000, 6.29117000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q34791996'),
(40807, 'Charmes', 4828, 'HDF', 75, 'FR', 49.65345000, 3.37857000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q34791996'),
(40808, 'Charmes-sur-Rhône', 4798, 'ARA', 75, 'FR', 44.86367000, 4.83533000, '2019-10-05 22:47:52', '2020-05-01 17:22:43', 1, 'Q323743'),
(40809, 'Charnay', 4798, 'ARA', 75, 'FR', 45.89058000, 4.66821000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q323743'),
(40810, 'Charnay-lès-Mâcon', 4825, 'BFC', 75, 'FR', 46.30751000, 4.78479000, '2019-10-05 22:47:52', '2020-05-01 17:22:44', 1, 'Q323743'),
(40811, 'Charny', 4825, 'BFC', 75, 'FR', 47.88661000, 3.09583000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q323743'),
(40812, 'Charny', 4796, 'IDF', 75, 'FR', 48.97098000, 2.76121000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q323743'),
(40813, 'Charnècles', 4798, 'ARA', 75, 'FR', 45.34398000, 5.52799000, '2019-10-05 22:47:52', '2020-05-01 17:22:43', 1, 'Q323743'),
(40814, 'Charolles', 4825, 'BFC', 75, 'FR', 46.43451000, 4.27527000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q213509'),
(40815, 'Charquemont', 4825, 'BFC', 75, 'FR', 47.21417000, 6.81980000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q213509'),
(40816, 'Charron', 4795, 'NAQ', 75, 'FR', 46.29433000, -1.10572000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q34792044'),
(40817, 'Charroux', 4795, 'NAQ', 75, 'FR', 46.14410000, 0.40354000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q34792044'),
(40818, 'Chars', 4796, 'IDF', 75, 'FR', 49.16032000, 1.93669000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q578229'),
(40819, 'Chartres', 4818, 'CVL', 75, 'FR', 48.44685000, 1.48925000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q130272'),
(40820, 'Chartres-de-Bretagne', 4807, 'BRE', 75, 'FR', 48.03924000, -1.70533000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q34792060'),
(40821, 'Chartrettes', 4796, 'IDF', 75, 'FR', 48.48808000, 2.70083000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q271985'),
(40822, 'Charvieu-Chavagneux', 4798, 'ARA', 75, 'FR', 45.75000000, 5.15000000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q383132'),
(40823, 'Chasné-sur-Illet', 4807, 'BRE', 75, 'FR', 48.24247000, -1.56138000, '2019-10-05 22:47:52', '2020-05-01 17:22:44', 1, 'Q34792110'),
(40824, 'Chassagny', 4798, 'ARA', 75, 'FR', 45.60670000, 4.73214000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q1364445'),
(40825, 'Chasse-sur-Rhône', 4798, 'ARA', 75, 'FR', 45.57850000, 4.80985000, '2019-10-05 22:47:52', '2020-05-01 17:22:43', 1, 'Q926254'),
(40826, 'Chasselay', 4798, 'ARA', 75, 'FR', 45.87440000, 4.77237000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q926254'),
(40827, 'Chasseneuil-du-Poitou', 4795, 'NAQ', 75, 'FR', 46.65112000, 0.37329000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q926254'),
(40828, 'Chasseneuil-sur-Bonnieure', 4795, 'NAQ', 75, 'FR', 45.81667000, 0.45000000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q34792116'),
(40829, 'Chassieu', 4798, 'ARA', 75, 'FR', 45.74584000, 4.97088000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q1617692'),
(40830, 'Chassors', 4795, 'NAQ', 75, 'FR', 45.70000000, -0.21667000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '*********'),
(40831, 'Chatou', 4796, 'IDF', 75, 'FR', 48.88980000, 2.15863000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q240752'),
(40832, 'Chatte', 4798, 'ARA', 75, 'FR', 45.14248000, 5.28224000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q240752'),
(40833, 'Chatuzange-le-Goubet', 4798, 'ARA', 75, 'FR', 45.00359000, 5.09079000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q240752'),
(40834, 'Chauché', 4802, 'PDL', 75, 'FR', 46.82963000, -1.27178000, '2019-10-05 22:47:52', '2020-05-01 17:22:46', 1, '********'),
(40835, 'Chauconin-Neufmontiers', 4796, 'IDF', 75, 'FR', 48.96667000, 2.85000000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q271227'),
(40836, 'Chaudes-Aigues', 4798, 'ARA', 75, 'FR', 44.85455000, 3.00406000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q269112'),
(40837, 'Chaudon', 4818, 'CVL', 75, 'FR', 48.66276000, 1.49670000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q269112'),
(40838, 'Chaudron-en-Mauges', 4802, 'PDL', 75, 'FR', 47.28809000, -0.98547000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '*********'),
(40839, 'Chauffailles', 4825, 'BFC', 75, 'FR', 46.20726000, 4.33932000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q750710'),
(40840, 'Chaulgnes', 4825, 'BFC', 75, 'FR', 47.12889000, 3.10348000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q750710'),
(40841, 'Chaulnes', 4828, 'HDF', 75, 'FR', 49.81699000, 2.80064000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q58892'),
(40842, 'Chaumes-en-Brie', 4796, 'IDF', 75, 'FR', 48.66853000, 2.84015000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q58892'),
(40843, 'Chaumont', 4820, 'GES', 75, 'FR', 48.11121000, 5.14134000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q193880'),
(40844, 'Chaumont-en-Vexin', 4828, 'HDF', 75, 'FR', 49.26595000, 1.88597000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q193880'),
(40845, 'Chaumont-sur-Loire', 4818, 'CVL', 75, 'FR', 47.48108000, 1.18929000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q193880'),
(40846, 'Chaumont-sur-Tharonne', 4818, 'CVL', 75, 'FR', 47.61039000, 1.90514000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q193880'),
(40847, 'Chaumontel', 4796, 'IDF', 75, 'FR', 49.12470000, 2.43237000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q273239'),
(40848, 'Chaunay', 4795, 'NAQ', 75, 'FR', 46.20759000, 0.16084000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q957132'),
(40849, 'Chauny', 4828, 'HDF', 75, 'FR', 49.61514000, 3.21857000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q269833'),
(40850, 'Chauray', 4795, 'NAQ', 75, 'FR', 46.35997000, -0.37859000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '*********'),
(40851, 'Chauriat', 4798, 'ARA', 75, 'FR', 45.75127000, 3.27895000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '*********'),
(40852, 'Chaussin', 4825, 'BFC', 75, 'FR', 46.96612000, 5.40791000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q669873'),
(40853, 'Chauvigny', 4795, 'NAQ', 75, 'FR', 46.56747000, 0.64928000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q688473'),
(40854, 'Chauvé', 4802, 'PDL', 75, 'FR', 47.15174000, -1.98489000, '2019-10-05 22:47:52', '2020-05-01 17:22:46', 1, '*********'),
(40855, 'Chavagne', 4807, 'BRE', 75, 'FR', 48.05438000, -1.78571000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '*********'),
(40856, 'Chavagnes-en-Paillers', 4802, 'PDL', 75, 'FR', 46.89167000, -1.25214000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '*********'),
(40857, 'Chavanay', 4798, 'ARA', 75, 'FR', 45.41647000, 4.72602000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '*********'),
(40858, 'Chavanod', 4798, 'ARA', 75, 'FR', 45.89005000, 6.03928000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q828253'),
(40859, 'Chavanoz', 4798, 'ARA', 75, 'FR', 45.76846000, 5.18808000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '********'),
(40860, 'Chavelot', 4820, 'GES', 75, 'FR', 48.23459000, 6.43809000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '********'),
(40861, 'Chavenay', 4796, 'IDF', 75, 'FR', 48.85437000, 1.99163000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q272616'),
(40862, 'Chavigny', 4820, 'GES', 75, 'FR', 48.62899000, 6.12317000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q272616'),
(40863, 'Chaville', 4796, 'IDF', 75, 'FR', 48.80565000, 2.18864000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q494125'),
(40864, 'Chazelles', 4795, 'NAQ', 75, 'FR', 45.64713000, 0.36748000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q494125'),
(40865, 'Chazelles-sur-Lyon', 4798, 'ARA', 75, 'FR', 45.63779000, 4.38890000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q1066107'),
(40866, 'Chazey-sur-Ain', 4798, 'ARA', 75, 'FR', 45.89300000, 5.25352000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q838332'),
(40867, 'Chef-Boutonne', 4795, 'NAQ', 75, 'FR', 46.10859000, -0.07083000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q838332'),
(40868, 'Cheillé', 4818, 'CVL', 75, 'FR', 47.26114000, 0.40553000, '2019-10-05 22:47:52', '2020-05-01 17:22:44', 1, 'Q838332'),
(40869, 'Chelles', 4796, 'IDF', 75, 'FR', 48.88109000, 2.59295000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q838332'),
(40870, 'Chemaudin', 4825, 'BFC', 75, 'FR', 47.22392000, 5.89419000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, 'Q838332'),
(40871, 'Chemazé', 4802, 'PDL', 75, 'FR', 47.78690000, -0.77523000, '2019-10-05 22:47:52', '2020-05-01 17:22:46', 1, '*********'),
(40872, 'Chemillé-Melay', 4802, 'PDL', 75, 'FR', 47.21476000, -0.72488000, '2019-10-05 22:47:52', '2020-05-01 17:22:46', 1, '*********'),
(40873, 'Cheniménil', 4820, 'GES', 75, 'FR', 48.13880000, 6.60346000, '2019-10-05 22:47:52', '2020-05-01 17:22:44', 1, '*********'),
(40874, 'Chennevières-sur-Marne', 4796, 'IDF', 75, 'FR', 48.79702000, 2.54046000, '2019-10-05 22:47:52', '2020-05-01 17:22:43', 1, '*********'),
(40875, 'Chenoise', 4796, 'IDF', 75, 'FR', 48.61462000, 3.19459000, '2019-10-05 22:47:52', '2019-10-05 22:47:52', 1, '********'),
(40876, 'Chens-sur-Léman', 4798, 'ARA', 75, 'FR', 46.32459000, 6.27075000, '2019-10-05 22:47:52', '2020-05-01 17:22:43', 1, '********'),
(40877, 'Cheny', 4825, 'BFC', 75, 'FR', 47.95166000, 3.53340000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '********'),
(40878, 'Chenôve', 4825, 'BFC', 75, 'FR', 47.29323000, 5.00457000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, '********'),
(40879, 'Cheptainville', 4796, 'IDF', 75, 'FR', 48.55090000, 2.27665000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q1138417'),
(40880, 'Cher', 4818, 'CVL', 75, 'FR', 47.11304000, 2.50983000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q3286'),
(40881, 'Cherbourg-Octeville', 4804, 'NOR', 75, 'FR', 49.63984000, -1.61636000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q160199'),
(40882, 'Cherisy', 4818, 'CVL', 75, 'FR', 48.75000000, 1.43333000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q160199'),
(40883, 'Chermignac', 4795, 'NAQ', 75, 'FR', 45.68578000, -0.67349000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q665849'),
(40884, 'Cherrueix', 4807, 'BRE', 75, 'FR', 48.60629000, -1.70405000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q196540'),
(40885, 'Cherré', 4802, 'PDL', 75, 'FR', 48.17290000, 0.65781000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, '*********'),
(40886, 'Cherves-Richemont', 4795, 'NAQ', 75, 'FR', 45.74345000, -0.35096000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q661000'),
(40887, 'Cherveux', 4795, 'NAQ', 75, 'FR', 46.41543000, -0.35706000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '*********'),
(40888, 'Chessy', 4798, 'ARA', 75, 'FR', 45.88716000, 4.62339000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '*********'),
(40889, 'Chessy', 4796, 'IDF', 75, 'FR', 48.88333000, 2.76667000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '*********'),
(40890, 'Cheux', 4804, 'NOR', 75, 'FR', 49.16611000, -0.52544000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '*********'),
(40891, 'Chevaigné', 4807, 'BRE', 75, 'FR', 48.21153000, -1.62933000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, '*********'),
(40892, 'Cheval-Blanc', 4812, 'PAC', 75, 'FR', 43.80189000, 5.06229000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q470532'),
(40893, 'Chevanceaux', 4795, 'NAQ', 75, 'FR', 45.30000000, -0.23333000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q1349746'),
(40894, 'Chevannes', 4796, 'IDF', 75, 'FR', 48.53259000, 2.44388000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q1349746'),
(40895, 'Cheverny', 4818, 'CVL', 75, 'FR', 47.50079000, 1.45951000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q659942'),
(40896, 'Chevigny-Saint-Sauveur', 4825, 'BFC', 75, 'FR', 47.29908000, 5.13367000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q659942'),
(40897, 'Chevillon', 4820, 'GES', 75, 'FR', 48.52869000, 5.13086000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q659942'),
(40898, 'Chevillon-sur-Huillard', 4818, 'CVL', 75, 'FR', 47.96197000, 2.62601000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q659942'),
(40899, 'Chevilly', 4818, 'CVL', 75, 'FR', 48.02973000, 1.87402000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q659942'),
(40900, 'Chevilly-Larue', 4796, 'IDF', 75, 'FR', 48.76476000, 2.35030000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q258757'),
(40901, 'Chevreuse', 4796, 'IDF', 75, 'FR', 48.70662000, 2.03329000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q399366'),
(40902, 'Chevrières', 4828, 'HDF', 75, 'FR', 49.34645000, 2.68219000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q399366'),
(40903, 'Chevry', 4798, 'ARA', 75, 'FR', 46.28136000, 6.03873000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q399366'),
(40904, 'Chevry-Cossigny', 4796, 'IDF', 75, 'FR', 48.72465000, 2.66106000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q634882'),
(40905, 'Chiché', 4795, 'NAQ', 75, 'FR', 46.79643000, -0.35560000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q34793011'),
(40906, 'Chierry', 4828, 'HDF', 75, 'FR', 49.03940000, 3.42976000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40907, 'Chilleurs-aux-Bois', 4818, 'CVL', 75, 'FR', 48.07220000, 2.13540000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40908, 'Chilly', 4798, 'ARA', 75, 'FR', 45.99185000, 5.95477000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40909, 'Chilly-Mazarin', 4796, 'IDF', 75, 'FR', 48.71489000, 2.31638000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40910, 'Chimilin', 4798, 'ARA', 75, 'FR', 45.57490000, 5.59569000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40911, 'Chindrieux', 4798, 'ARA', 75, 'FR', 45.81948000, 5.85024000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40912, 'Chinon', 4818, 'CVL', 75, 'FR', 47.16701000, 0.24284000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40913, 'Chirac', 4799, 'OCC', 75, 'FR', 44.52289000, 3.26652000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q34793011'),
(40914, 'Chirens', 4798, 'ARA', 75, 'FR', 45.41024000, 5.55634000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q574297'),
(40915, 'Chiry-Ourscamp', 4828, 'HDF', 75, 'FR', 49.54378000, 2.94721000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q282431'),
(40916, 'Chissay-en-Touraine', 4818, 'CVL', 75, 'FR', 47.33747000, 1.13362000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q965163'),
(40917, 'Chitenay', 4818, 'CVL', 75, 'FR', 47.49753000, 1.37139000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q1135543'),
(40918, 'Chocques', 4828, 'HDF', 75, 'FR', 50.54084000, 2.57193000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q1135543'),
(40919, 'Choisey', 4825, 'BFC', 75, 'FR', 47.06389000, 5.45911000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '********'),
(40920, 'Choisy', 4798, 'ARA', 75, 'FR', 45.99359000, 6.05866000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '********'),
(40921, 'Choisy-au-Bac', 4828, 'HDF', 75, 'FR', 49.43777000, 2.87739000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q741010'),
(40922, 'Choisy-en-Brie', 4796, 'IDF', 75, 'FR', 48.75867000, 3.21705000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q741010'),
(40923, 'Choisy-le-Roi', 4796, 'IDF', 75, 'FR', 48.76846000, 2.41874000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q212987'),
(40924, 'Cholet', 4802, 'PDL', 75, 'FR', 47.05905000, -0.87953000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q185066'),
(40925, 'Chomérac', 4798, 'ARA', 75, 'FR', 44.70752000, 4.66164000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q185066'),
(40926, 'Chorges', 4812, 'PAC', 75, 'FR', 44.54879000, 6.27727000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q185066'),
(40927, 'Chouzy-sur-Cisse', 4818, 'CVL', 75, 'FR', 47.52576000, 1.24661000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, '********'),
(40928, 'Chouzé-sur-Loire', 4818, 'CVL', 75, 'FR', 47.23673000, 0.12364000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q681259'),
(40929, 'Chutes-Lavie', 4812, 'PAC', 75, 'FR', 43.31017000, 5.39464000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q681259'),
(40930, 'Chuzelles', 4798, 'ARA', 75, 'FR', 45.58481000, 4.87703000, '2019-10-05 22:47:53', '2019-10-05 22:47:53', 1, 'Q681259'),
(40931, 'Châbons', 4798, 'ARA', 75, 'FR', 45.44282000, 5.42542000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q681259'),
(40932, 'Châlette-sur-Loing', 4818, 'CVL', 75, 'FR', 48.01337000, 2.73587000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q1169620'),
(40933, 'Châlons-en-Champagne', 4820, 'GES', 75, 'FR', 48.95393000, 4.36724000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q1169620'),
(40934, 'Châlonvillars', 4825, 'BFC', 75, 'FR', 47.64055000, 6.78407000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q1169620'),
(40935, 'Châlus', 4795, 'NAQ', 75, 'FR', 45.65440000, 0.98011000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q622720'),
(40936, 'Chârost', 4818, 'CVL', 75, 'FR', 46.99390000, 2.11639000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q622720'),
(40937, 'Château-Chinon(Ville)', 4825, 'BFC', 75, 'FR', 47.06667000, 3.93333000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q622720'),
(40938, 'Château-Gaillard', 4798, 'ARA', 75, 'FR', 45.97239000, 5.30436000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q622720'),
(40939, 'Château-Gontier', 4802, 'PDL', 75, 'FR', 47.82872000, -0.70265000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q214580'),
(40940, 'Château-Guibert', 4802, 'PDL', 75, 'FR', 46.58110000, -1.23656000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, '********'),
(40941, 'Château-Landon', 4796, 'IDF', 75, 'FR', 48.14721000, 2.69754000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q845816'),
(40942, 'Château-Porcien', 4820, 'GES', 75, 'FR', 49.52740000, 4.24533000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q845816'),
(40943, 'Château-Renard', 4818, 'CVL', 75, 'FR', 47.93333000, 2.93333000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q819787'),
(40944, 'Château-Renault', 4818, 'CVL', 75, 'FR', 47.59188000, 0.91143000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q819787'),
(40945, 'Château-Salins', 4820, 'GES', 75, 'FR', 48.81885000, 6.51455000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q22745'),
(40946, 'Château-Thierry', 4828, 'HDF', 75, 'FR', 49.04636000, 3.40304000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q194145'),
(40947, 'Château-du-Loir', 4802, 'PDL', 75, 'FR', 47.69492000, 0.41851000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q829292'),
(40948, 'Château-d’Olonne', 4802, 'PDL', 75, 'FR', 46.50382000, -1.74097000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, '********'),
(40949, 'Château-la-Vallière', 4818, 'CVL', 75, 'FR', 47.54665000, 0.32458000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, '********'),
(40950, 'Châteaubernard', 4795, 'NAQ', 75, 'FR', 45.66667000, -0.33333000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, '*********'),
(40951, 'Châteaubourg', 4807, 'BRE', 75, 'FR', 48.11119000, -1.40019000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, '*********'),
(40952, 'Châteaubriant', 4802, 'PDL', 75, 'FR', 47.71712000, -1.37624000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q211187'),
(40953, 'Châteaudun', 4818, 'CVL', 75, 'FR', 48.07086000, 1.33783000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q181886'),
(40954, 'Châteaufort', 4796, 'IDF', 75, 'FR', 48.73584000, 2.09054000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q181886'),
(40955, 'Châteaugay', 4798, 'ARA', 75, 'FR', 45.85117000, 3.08482000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q181886'),
(40956, 'Châteaugiron', 4807, 'BRE', 75, 'FR', 48.04629000, -1.50438000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, '*********'),
(40957, 'Châteaulin', 4807, 'BRE', 75, 'FR', 48.19677000, -4.09008000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q202374'),
(40958, 'Châteaumeillant', 4818, 'CVL', 75, 'FR', 46.56219000, 2.19515000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q639610'),
(40959, 'Châteauneuf', 4798, 'ARA', 75, 'FR', 45.52457000, 4.64044000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q34794450'),
(40960, 'Châteauneuf-Grasse', 4812, 'PAC', 75, 'FR', 43.66667000, 6.98333000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q324244'),
(40961, 'Châteauneuf-de-Gadagne', 4812, 'PAC', 75, 'FR', 43.92683000, 4.94453000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q377797'),
(40962, 'Châteauneuf-de-Galaure', 4798, 'ARA', 75, 'FR', 45.22977000, 4.95777000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q863711'),
(40963, 'Châteauneuf-du-Faou', 4807, 'BRE', 75, 'FR', 48.18755000, -3.81789000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q34794462'),
(40964, 'Châteauneuf-du-Pape', 4812, 'PAC', 75, 'FR', 44.05638000, 4.83244000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q338877'),
(40965, 'Châteauneuf-du-Rhône', 4798, 'ARA', 75, 'FR', 44.48906000, 4.71706000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q541278'),
(40966, 'Châteauneuf-d’Ille-et-Vilaine', 4807, 'BRE', 75, 'FR', 48.56083000, -1.92838000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q541278'),
(40967, 'Châteauneuf-en-Thymerais', 4818, 'CVL', 75, 'FR', 48.58112000, 1.24085000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q475369'),
(40968, 'Châteauneuf-la-Forêt', 4795, 'NAQ', 75, 'FR', 45.71436000, 1.60610000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q475369'),
(40969, 'Châteauneuf-le-Rouge', 4812, 'PAC', 75, 'FR', 43.48936000, 5.56921000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q474041'),
(40970, 'Châteauneuf-les-Martigues', 4812, 'PAC', 75, 'FR', 43.38383000, 5.16403000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q474041'),
(40971, 'Châteauneuf-sur-Charente', 4795, 'NAQ', 75, 'FR', 45.60000000, -0.05000000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q837510'),
(40972, 'Châteauneuf-sur-Cher', 4818, 'CVL', 75, 'FR', 46.85778000, 2.31710000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q837510'),
(40973, 'Châteauneuf-sur-Isère', 4798, 'ARA', 75, 'FR', 45.01667000, 4.93333000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, '*********'),
(40974, 'Châteauneuf-sur-Loire', 4818, 'CVL', 75, 'FR', 47.86575000, 2.21903000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q819934'),
(40975, 'Châteauneuf-sur-Sarthe', 4802, 'PDL', 75, 'FR', 47.68145000, -0.48652000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, '*********'),
(40976, 'Châteauponsac', 4795, 'NAQ', 75, 'FR', 46.13536000, 1.27623000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, '*********'),
(40977, 'Châteaurenard', 4812, 'PAC', 75, 'FR', 43.88169000, 4.85493000, '2019-10-05 22:47:53', '2020-05-01 17:22:46', 1, 'Q474021'),
(40978, 'Châteauroux', 4818, 'CVL', 75, 'FR', 46.81248000, 1.69362000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q174251'),
(40979, 'Châteauvillain', 4820, 'GES', 75, 'FR', 48.03655000, 4.91823000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q681210'),
(40980, 'Châtel', 4798, 'ARA', 75, 'FR', 46.26495000, 6.84030000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q681210'),
(40981, 'Châtel-Guyon', 4798, 'ARA', 75, 'FR', 45.92258000, 3.06423000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q681210'),
(40982, 'Châtel-Saint-Germain', 4820, 'GES', 75, 'FR', 49.12255000, 6.08006000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q22119'),
(40983, 'Châtel-sur-Moselle', 4820, 'GES', 75, 'FR', 48.31430000, 6.39403000, '2019-10-05 22:47:53', '2020-05-01 17:22:44', 1, 'Q1414345'),
(40984, 'Châtelaillon-Plage', 4795, 'NAQ', 75, 'FR', 46.07190000, -1.08926000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q34794502'),
(40985, 'Châtellerault', 4795, 'NAQ', 75, 'FR', 46.81712000, 0.54536000, '2019-10-05 22:47:53', '2020-05-01 17:22:45', 1, 'Q205576'),
(40986, 'Châtenay-Malabry', 4796, 'IDF', 75, 'FR', 48.76507000, 2.26655000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q200945'),
(40987, 'Châtenay-sur-Seine', 4796, 'IDF', 75, 'FR', 48.41839000, 3.09474000, '2019-10-05 22:47:53', '2020-05-01 17:22:43', 1, 'Q200945'),
(40988, 'Châtenois', 4820, 'GES', 75, 'FR', 48.27201000, 7.40109000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q200945'),
(40989, 'Châtenois-les-Forges', 4825, 'BFC', 75, 'FR', 47.55875000, 6.84871000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q200945'),
(40990, 'Châtenoy-le-Royal', 4825, 'BFC', 75, 'FR', 46.79797000, 4.81190000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q200945'),
(40991, 'Châtillon', 4798, 'ARA', 75, 'FR', 45.80091000, 5.84352000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q34794543'),
(40992, 'Châtillon', 4796, 'IDF', 75, 'FR', 48.80240000, 2.29346000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q34794543'),
(40993, 'Châtillon-Coligny', 4818, 'CVL', 75, 'FR', 47.82266000, 2.84563000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q732483'),
(40994, 'Châtillon-en-Bazois', 4825, 'BFC', 75, 'FR', 47.05464000, 3.65893000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q732483'),
(40995, 'Châtillon-en-Michaille', 4798, 'ARA', 75, 'FR', 46.14320000, 5.79950000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q182134'),
(40996, 'Châtillon-en-Vendelais', 4807, 'BRE', 75, 'FR', 48.22409000, -1.17695000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q182134'),
(40997, 'Châtillon-la-Palud', 4798, 'ARA', 75, 'FR', 45.97171000, 5.25290000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q182134'),
(40998, 'Châtillon-le-Duc', 4825, 'BFC', 75, 'FR', 47.30486000, 6.00792000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q182134'),
(40999, 'Châtillon-sur-Chalaronne', 4798, 'ARA', 75, 'FR', 46.11834000, 4.95656000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q208439'),
(41000, 'Châtillon-sur-Cher', 4818, 'CVL', 75, 'FR', 47.27571000, 1.49424000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q1135644'),
(41001, 'Châtillon-sur-Cluses', 4798, 'ARA', 75, 'FR', 46.08765000, 6.58041000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q1135644'),
(41002, 'Châtillon-sur-Indre', 4818, 'CVL', 75, 'FR', 46.98735000, 1.17218000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q470682'),
(41003, 'Châtillon-sur-Marne', 4820, 'GES', 75, 'FR', 49.10048000, 3.76023000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q688485'),
(41004, 'Châtillon-sur-Seiche', 4807, 'BRE', 75, 'FR', 48.03448000, -1.67114000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q34794563'),
(41005, 'Châtillon-sur-Seine', 4825, 'BFC', 75, 'FR', 47.85851000, 4.57375000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q244707'),
(41006, 'Châtillon-sur-Thouet', 4795, 'NAQ', 75, 'FR', 46.66176000, -0.23489000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, '*********'),
(41007, 'Châtonnay', 4798, 'ARA', 75, 'FR', 45.48734000, 5.21168000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, '*********'),
(41008, 'Châtres-sur-Cher', 4818, 'CVL', 75, 'FR', 47.26505000, 1.90591000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, '********'),
(41009, 'Chèvremont', 4825, 'BFC', 75, 'FR', 47.62912000, 6.92056000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, '********'),
(41010, 'Chécy', 4818, 'CVL', 75, 'FR', 47.89402000, 2.02304000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q693520'),
(41011, 'Chéméré', 4802, 'PDL', 75, 'FR', 47.11667000, -1.91667000, '2019-10-05 22:47:54', '2020-05-01 17:22:46', 1, '*********'),
(41012, 'Chépy', 4828, 'HDF', 75, 'FR', 50.06361000, 1.64694000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, '*********'),
(41013, 'Chérac', 4795, 'NAQ', 75, 'FR', 45.70456000, -0.43859000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, '********'),
(41014, 'Chéraute', 4795, 'NAQ', 75, 'FR', 43.23096000, -0.86831000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q274861'),
(41015, 'Chéreng', 4828, 'HDF', 75, 'FR', 50.61059000, 3.20666000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q303294'),
(41016, 'Chéroy', 4825, 'BFC', 75, 'FR', 48.20076000, 3.00011000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q303294'),
(41017, 'Chézy-sur-Marne', 4828, 'HDF', 75, 'FR', 48.98881000, 3.36786000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q303294'),
(41018, 'Chênehutte-Trèves-Cunault', 4802, 'PDL', 75, 'FR', 47.31084000, -0.16042000, '2019-10-05 22:47:54', '2020-05-01 17:22:46', 1, '*********'),
(41019, 'Ciboure', 4795, 'NAQ', 75, 'FR', 43.38287000, -1.67600000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q235208'),
(41020, 'Cinq Avenues', 4812, 'PAC', 75, 'FR', 43.30375000, 5.39761000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q235208'),
(41021, 'Cinq-Mars-la-Pile', 4818, 'CVL', 75, 'FR', 47.34638000, 0.45873000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q235208'),
(41022, 'Cinqueux', 4828, 'HDF', 75, 'FR', 49.31739000, 2.52997000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41023, 'Cintegabelle', 4799, 'OCC', 75, 'FR', 43.31316000, 1.53333000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41024, 'Cintré', 4807, 'BRE', 75, 'FR', 48.10504000, -1.87162000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, '*********'),
(41025, 'Cires-lès-Mello', 4828, 'HDF', 75, 'FR', 49.27242000, 2.35840000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, '*********'),
(41026, 'Cirey-sur-Vezouze', 4820, 'GES', 75, 'FR', 48.58093000, 6.94573000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '*********'),
(41027, 'Ciry-le-Noble', 4825, 'BFC', 75, 'FR', 46.60607000, 4.29869000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '*********'),
(41028, 'Ciré-d’Aunis', 4795, 'NAQ', 75, 'FR', 46.05544000, -0.93056000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q1147332'),
(41029, 'Cissé', 4795, 'NAQ', 75, 'FR', 46.64583000, 0.22870000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q1147332'),
(41030, 'Civens', 4798, 'ARA', 75, 'FR', 45.77964000, 4.25170000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q1147332'),
(41031, 'Civray', 4795, 'NAQ', 75, 'FR', 46.14710000, 0.29509000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q1147332'),
(41032, 'Civray-de-Touraine', 4818, 'CVL', 75, 'FR', 47.33253000, 1.04952000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q1135868'),
(41033, 'Civrieux', 4798, 'ARA', 75, 'FR', 45.92086000, 4.88249000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q1135868'),
(41034, 'Claira', 4799, 'OCC', 75, 'FR', 42.76036000, 2.95572000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q199773'),
(41035, 'Clairac', 4795, 'NAQ', 75, 'FR', 44.36011000, 0.37893000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q199773'),
(41036, 'Clairoix', 4828, 'HDF', 75, 'FR', 49.44264000, 2.84628000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q669796'),
(41037, 'Clairvaux-les-Lacs', 4825, 'BFC', 75, 'FR', 46.57473000, 5.74825000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q477009'),
(41038, 'Claix', 4798, 'ARA', 75, 'FR', 45.11994000, 5.67292000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q477009'),
(41039, 'Clamart', 4796, 'IDF', 75, 'FR', 48.80299000, 2.26692000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q477009'),
(41040, 'Clamecy', 4825, 'BFC', 75, 'FR', 47.46017000, 3.51940000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q477009'),
(41041, 'Clapiers', 4799, 'OCC', 75, 'FR', 43.65833000, 3.88917000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q931360'),
(41042, 'Clarensac', 4799, 'OCC', 75, 'FR', 43.82536000, 4.22047000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q931360'),
(41043, 'Claret', 4799, 'OCC', 75, 'FR', 43.86244000, 3.90522000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q931360'),
(41044, 'Clary', 4828, 'HDF', 75, 'FR', 50.07789000, 3.39943000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q931360'),
(41045, 'Claville', 4804, 'NOR', 75, 'FR', 49.04844000, 1.01954000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q931360'),
(41046, 'Claye-Souilly', 4796, 'IDF', 75, 'FR', 48.94492000, 2.68566000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41047, 'Clermont', 4828, 'HDF', 75, 'FR', 49.37897000, 2.41258000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41048, 'Clermont-Créans', 4802, 'PDL', 75, 'FR', 47.71831000, -0.01459000, '2019-10-05 22:47:54', '2020-05-01 17:22:46', 1, '********'),
(41049, 'Clermont-Ferrand', 4798, 'ARA', 75, 'FR', 45.77969000, 3.08682000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41050, 'Clermont-en-Argonne', 4820, 'GES', 75, 'FR', 49.10711000, 5.07002000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41051, 'Clerval', 4825, 'BFC', 75, 'FR', 47.39167000, 6.49925000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41052, 'Clichy', 4796, 'IDF', 75, 'FR', 48.90018000, 2.30952000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '********'),
(41053, 'Clichy-sous-Bois', 4796, 'IDF', 75, 'FR', 48.91020000, 2.55323000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q206931'),
(41054, 'Clinchamps-sur-Orne', 4804, 'NOR', 75, 'FR', 49.07857000, -0.40156000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q232056'),
(41055, 'Clion', 4818, 'CVL', 75, 'FR', 46.94085000, 1.23214000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q232056'),
(41056, 'Clisson', 4802, 'PDL', 75, 'FR', 47.08714000, -1.28286000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '*********'),
(41057, 'Clohars-Carnoët', 4807, 'BRE', 75, 'FR', 47.79606000, -3.58502000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, '*********'),
(41058, 'Clohars-Fouesnant', 4807, 'BRE', 75, 'FR', 47.89657000, -4.06396000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '*********'),
(41059, 'Clonas-sur-Varèze', 4798, 'ARA', 75, 'FR', 45.41382000, 4.79077000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, '*********'),
(41060, 'Clouange', 4820, 'GES', 75, 'FR', 49.26240000, 6.09723000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '*********'),
(41061, 'Cloyes-sur-le-Loir', 4818, 'CVL', 75, 'FR', 47.99726000, 1.23711000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, '*********'),
(41062, 'Cluis', 4818, 'CVL', 75, 'FR', 46.54486000, 1.74933000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q587042'),
(41063, 'Cluny', 4825, 'BFC', 75, 'FR', 46.43318000, 4.65845000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q587042'),
(41064, 'Cluses', 4798, 'ARA', 75, 'FR', 46.06251000, 6.57497000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q654153'),
(41065, 'Clères', 4804, 'NOR', 75, 'FR', 49.60000000, 1.11667000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q654153'),
(41066, 'Clécy', 4804, 'NOR', 75, 'FR', 48.91718000, -0.48041000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q654153'),
(41067, 'Cléden-Poher', 4807, 'BRE', 75, 'FR', 48.23644000, -3.66911000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q34795797'),
(41068, 'Cléder', 4807, 'BRE', 75, 'FR', 48.66300000, -4.10200000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q221537'),
(41069, 'Cléguer', 4807, 'BRE', 75, 'FR', 47.85407000, -3.38219000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q34795806'),
(41070, 'Cléguérec', 4807, 'BRE', 75, 'FR', 48.12577000, -3.07162000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q34795812'),
(41071, 'Cléon', 4804, 'NOR', 75, 'FR', 49.31235000, 1.02950000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q1166766'),
(41072, 'Clérac', 4795, 'NAQ', 75, 'FR', 45.18333000, -0.21667000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q1078527'),
(41073, 'Clérieux', 4798, 'ARA', 75, 'FR', 45.07591000, 4.95983000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q1078527'),
(41074, 'Cléry-Saint-André', 4818, 'CVL', 75, 'FR', 47.82218000, 1.75091000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q816291'),
(41075, 'Cléré-les-Pins', 4818, 'CVL', 75, 'FR', 47.42550000, 0.38963000, '2019-10-05 22:47:54', '2020-05-01 17:22:44', 1, 'Q816291'),
(41076, 'Coarraze', 4795, 'NAQ', 75, 'FR', 43.16667000, -0.23333000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q174462'),
(41077, 'Cocheren', 4820, 'GES', 75, 'FR', 49.14384000, 6.85649000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q174462'),
(41078, 'Codognan', 4799, 'OCC', 75, 'FR', 43.73033000, 4.22120000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q174462'),
(41079, 'Cognac', 4795, 'NAQ', 75, 'FR', 45.69583000, -0.32916000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q285'),
(41080, 'Cognac-la-Forêt', 4795, 'NAQ', 75, 'FR', 45.83333000, 1.00000000, '2019-10-05 22:47:54', '2020-05-01 17:22:45', 1, 'Q34795924'),
(41081, 'Cognin', 4798, 'ARA', 75, 'FR', 45.55952000, 5.89113000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q34795924'),
(41082, 'Cogny', 4798, 'ARA', 75, 'FR', 45.98747000, 4.62475000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q34795924'),
(41083, 'Cogolin', 4812, 'PAC', 75, 'FR', 43.25294000, 6.52981000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q34795924'),
(41084, 'Coignières', 4796, 'IDF', 75, 'FR', 48.75010000, 1.92082000, '2019-10-05 22:47:54', '2020-05-01 17:22:43', 1, 'Q492333'),
(41085, 'Coincy', 4828, 'HDF', 75, 'FR', 49.16036000, 3.42202000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q492333'),
(41086, 'Coise', 4798, 'ARA', 75, 'FR', 45.52822000, 6.14389000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q492333'),
(41087, 'Colayrac-Saint-Cirq', 4795, 'NAQ', 75, 'FR', 44.22095000, 0.55061000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q492333'),
(41088, 'Coligny', 4798, 'ARA', 75, 'FR', 46.38252000, 5.34554000, '2019-10-05 22:47:54', '2019-10-05 22:47:54', 1, 'Q492333'),
(41089, 'Colleret', 4828, 'HDF', 75, 'FR', 50.25693000, 4.08083000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q492333'),
(41090, 'Colleville-Montgomery', 4804, 'NOR', 75, 'FR', 49.27528000, -0.30052000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q838576'),
(41091, 'Collioure', 4799, 'OCC', 75, 'FR', 42.52462000, 3.08235000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q254829'),
(41092, 'Collobrières', 4812, 'PAC', 75, 'FR', 43.23718000, 6.30901000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, 'Q254829'),
(41093, 'Collonges', 4798, 'ARA', 75, 'FR', 46.13819000, 5.90506000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q254829'),
(41094, 'Collonges-sous-Salève', 4798, 'ARA', 75, 'FR', 46.14160000, 6.15372000, '2019-10-05 22:47:55', '2020-05-01 17:22:43', 1, 'Q34796059'),
(41095, 'Collégien', 4796, 'IDF', 75, 'FR', 48.83571000, 2.67365000, '2019-10-05 22:47:55', '2020-05-01 17:22:43', 1, 'Q34796059'),
(41096, 'Colmar', 4820, 'GES', 75, 'FR', 48.08078000, 7.35584000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796059'),
(41097, 'Colomars', 4812, 'PAC', 75, 'FR', 43.76320000, 7.22191000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796059'),
(41098, 'Colombe', 4798, 'ARA', 75, 'FR', 45.40024000, 5.45441000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796059'),
(41099, 'Colombelles', 4804, 'NOR', 75, 'FR', 49.20490000, -0.29571000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q662396'),
(41100, 'Colombes', 4796, 'IDF', 75, 'FR', 48.91882000, 2.25404000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q174002'),
(41101, 'Colombey-les-Belles', 4820, 'GES', 75, 'FR', 48.52920000, 5.89451000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q174002'),
(41102, 'Colombier-Fontaine', 4825, 'BFC', 75, 'FR', 47.45224000, 6.69010000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q174002'),
(41103, 'Colombiers', 4799, 'OCC', 75, 'FR', 43.31424000, 3.13947000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q174002'),
(41104, 'Colombiers', 4795, 'NAQ', 75, 'FR', 46.77158000, 0.42388000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q174002'),
(41105, 'Colombiès', 4799, 'OCC', 75, 'FR', 44.34414000, 2.33772000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, 'Q174002'),
(41106, 'Colomiers', 4799, 'OCC', 75, 'FR', 43.61058000, 1.33467000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q318071'),
(41107, 'Colpo', 4807, 'BRE', 75, 'FR', 47.81778000, -2.81002000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796154'),
(41108, 'Combaillaux', 4799, 'OCC', 75, 'FR', 43.67191000, 3.76767000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796154'),
(41109, 'Combloux', 4798, 'ARA', 75, 'FR', 45.89790000, 6.64420000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796154'),
(41110, 'Combourg', 4807, 'BRE', 75, 'FR', 48.41267000, -1.74424000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q324273'),
(41111, 'Combrand', 4795, 'NAQ', 75, 'FR', 46.86405000, -0.68869000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41112, 'Combrit', 4807, 'BRE', 75, 'FR', 47.88734000, -4.15817000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41113, 'Combronde', 4798, 'ARA', 75, 'FR', 45.98099000, 3.08807000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41114, 'Combrée', 4802, 'PDL', 75, 'FR', 47.70446000, -1.03003000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, '********'),
(41115, 'Combs-la-Ville', 4796, 'IDF', 75, 'FR', 48.66497000, 2.56957000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q275375'),
(41116, 'Comines', 4828, 'HDF', 75, 'FR', 50.76150000, 3.01063000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q275375'),
(41117, 'Commelle', 4798, 'ARA', 75, 'FR', 46.00086000, 4.05794000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q275375'),
(41118, 'Commentry', 4798, 'ARA', 75, 'FR', 46.28876000, 2.74163000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q510013'),
(41119, 'Commequiers', 4802, 'PDL', 75, 'FR', 46.76049000, -1.83901000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41120, 'Commer', 4802, 'PDL', 75, 'FR', 48.23333000, -0.61667000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41121, 'Commercy', 4820, 'GES', 75, 'FR', 48.76128000, 5.59067000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q212456'),
(41122, 'Communay', 4798, 'ARA', 75, 'FR', 45.60442000, 4.83488000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q212456'),
(41123, 'Compertrix', 4820, 'GES', 75, 'FR', 48.94179000, 4.34631000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q212456'),
(41124, 'Compiègne', 4828, 'HDF', 75, 'FR', 49.41794000, 2.82606000, '2019-10-05 22:47:55', '2020-05-01 17:22:45', 1, 'Q180871'),
(41125, 'Compreignac', 4795, 'NAQ', 75, 'FR', 45.99162000, 1.27561000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q180871'),
(41126, 'Comps', 4799, 'OCC', 75, 'FR', 43.85304000, 4.60567000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q180871'),
(41127, 'Concarneau', 4807, 'BRE', 75, 'FR', 47.87502000, -3.92245000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796293'),
(41128, 'Conches-en-Ouche', 4804, 'NOR', 75, 'FR', 48.95768000, 0.94052000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q326369'),
(41129, 'Conches-sur-Gondoire', 4796, 'IDF', 75, 'FR', 48.85624000, 2.71783000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q250702'),
(41130, 'Condat', 4798, 'ARA', 75, 'FR', 45.34093000, 2.75791000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796347'),
(41131, 'Condat-sur-Vienne', 4795, 'NAQ', 75, 'FR', 45.78648000, 1.28454000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q195757'),
(41132, 'Condette', 4828, 'HDF', 75, 'FR', 50.65817000, 1.63386000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q988758'),
(41133, 'Condom', 4799, 'OCC', 75, 'FR', 43.95816000, 0.37199000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q988758'),
(41134, 'Condrieu', 4798, 'ARA', 75, 'FR', 45.46300000, 4.76765000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q828612'),
(41135, 'Condé-Sainte-Libiaire', 4796, 'IDF', 75, 'FR', 48.89695000, 2.83904000, '2019-10-05 22:47:55', '2020-05-01 17:22:43', 1, 'Q828612'),
(41136, 'Condé-sur-Huisne', 4804, 'NOR', 75, 'FR', 48.38103000, 0.85093000, '2019-10-05 22:47:55', '2020-05-01 17:22:45', 1, 'Q828612'),
(41137, 'Condé-sur-Noireau', 4804, 'NOR', 75, 'FR', 48.84881000, -0.55214000, '2019-10-05 22:47:55', '2020-05-01 17:22:45', 1, 'Q244196'),
(41138, 'Condé-sur-Sarthe', 4804, 'NOR', 75, 'FR', 48.43197000, 0.03398000, '2019-10-05 22:47:55', '2020-05-01 17:22:45', 1, 'Q1060068'),
(41139, 'Condé-sur-Vesgre', 4796, 'IDF', 75, 'FR', 48.74199000, 1.66069000, '2019-10-05 22:47:55', '2020-05-01 17:22:43', 1, 'Q1060068'),
(41140, 'Condé-sur-Vire', 4804, 'NOR', 75, 'FR', 49.05000000, -1.03333000, '2019-10-05 22:47:55', '2020-05-01 17:22:45', 1, '********'),
(41141, 'Condé-sur-l’Escaut', 4828, 'HDF', 75, 'FR', 50.45436000, 3.58884000, '2019-10-05 22:47:55', '2020-05-01 17:22:45', 1, '********'),
(41142, 'Conflans-Sainte-Honorine', 4796, 'IDF', 75, 'FR', 49.00158000, 2.09694000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41143, 'Conflans-en-Jarnisy', 4820, 'GES', 75, 'FR', 49.16725000, 5.85515000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41144, 'Confolens', 4795, 'NAQ', 75, 'FR', 46.01363000, 0.67231000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q217155'),
(41145, 'Congis-sur-Thérouanne', 4796, 'IDF', 75, 'FR', 49.00000000, 2.98333000, '2019-10-05 22:47:55', '2020-05-01 17:22:43', 1, '*********'),
(41146, 'Congrier', 4802, 'PDL', 75, 'FR', 47.80989000, -1.11700000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41147, 'Congénies', 4799, 'OCC', 75, 'FR', 43.76667000, 4.16667000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, 'Q274330'),
(41148, 'Conlie', 4802, 'PDL', 75, 'FR', 48.12334000, -0.01739000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q274330'),
(41149, 'Connantre', 4820, 'GES', 75, 'FR', 48.72657000, 3.92403000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q274330'),
(41150, 'Connaux', 4799, 'OCC', 75, 'FR', 44.08919000, 4.59387000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q274330'),
(41151, 'Connerré', 4802, 'PDL', 75, 'FR', 48.05698000, 0.49344000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, 'Q274330'),
(41152, 'Conquereuil', 4802, 'PDL', 75, 'FR', 47.62524000, -1.75105000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41153, 'Conques-sur-Orbiel', 4799, 'OCC', 75, 'FR', 43.26667000, 2.41667000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41154, 'Contamine-sur-Arve', 4798, 'ARA', 75, 'FR', 46.14223000, 6.33215000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41155, 'Contes', 4812, 'PAC', 75, 'FR', 43.81278000, 7.31444000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41156, 'Contres', 4818, 'CVL', 75, 'FR', 47.41754000, 1.42849000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41157, 'Contrexéville', 4820, 'GES', 75, 'FR', 48.18424000, 5.89572000, '2019-10-05 22:47:55', '2020-05-01 17:22:44', 1, 'Q633628'),
(41158, 'Conty', 4828, 'HDF', 75, 'FR', 49.74100000, 2.15120000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q68977'),
(41159, 'Coquelles', 4828, 'HDF', 75, 'FR', 50.93461000, 1.79880000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q380987'),
(41160, 'Coray', 4807, 'BRE', 75, 'FR', 48.05934000, -3.83056000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796536'),
(41161, 'Corbarieu', 4799, 'OCC', 75, 'FR', 43.94415000, 1.36881000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q34796536'),
(41162, 'Corbas', 4798, 'ARA', 75, 'FR', 45.66798000, 4.90198000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q1651173'),
(41163, 'Corbehem', 4828, 'HDF', 75, 'FR', 50.33141000, 3.04995000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q1651173'),
(41164, 'Corbeil-Essonnes', 4796, 'IDF', 75, 'FR', 48.60603000, 2.48757000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q1651173'),
(41165, 'Corbeilles', 4818, 'CVL', 75, 'FR', 48.07201000, 2.55030000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q1651173'),
(41166, 'Corbelin', 4798, 'ARA', 75, 'FR', 45.60733000, 5.54261000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q1651173'),
(41167, 'Corbenay', 4825, 'BFC', 75, 'FR', 47.89275000, 6.33047000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q1651173'),
(41168, 'Corbie', 4828, 'HDF', 75, 'FR', 49.90672000, 2.50682000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q71469'),
(41169, 'Corbigny', 4825, 'BFC', 75, 'FR', 47.25678000, 3.68285000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q71469'),
(41170, 'Corbreuse', 4796, 'IDF', 75, 'FR', 48.50065000, 1.95913000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41171, 'Corcieux', 4820, 'GES', 75, 'FR', 48.17236000, 6.88148000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q832301'),
(41172, 'Corcoué-sur-Logne', 4802, 'PDL', 75, 'FR', 46.96667000, -1.58333000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, '********'),
(41173, 'Cordemais', 4802, 'PDL', 75, 'FR', 47.29107000, -1.87869000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41174, 'Cordes-sur-Ciel', 4799, 'OCC', 75, 'FR', 44.06667000, 1.95000000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q635125'),
(41175, 'Cordon', 4798, 'ARA', 75, 'FR', 45.92099000, 6.60536000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41176, 'Corenc', 4798, 'ARA', 75, 'FR', 45.21889000, 5.76497000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41177, 'Corlay', 4807, 'BRE', 75, 'FR', 48.31725000, -3.05733000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41178, 'Corme-Royal', 4795, 'NAQ', 75, 'FR', 45.74395000, -0.81471000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41179, 'Cormeilles', 4804, 'NOR', 75, 'FR', 49.24803000, 0.37654000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41180, 'Cormeilles-en-Parisis', 4796, 'IDF', 75, 'FR', 48.97111000, 2.20491000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q841957'),
(41181, 'Cormelles-le-Royal', 4804, 'NOR', 75, 'FR', 49.15398000, -0.33062000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q330937'),
(41182, 'Cormeray', 4818, 'CVL', 75, 'FR', 47.49195000, 1.40610000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q330937'),
(41183, 'Cormery', 4818, 'CVL', 75, 'FR', 47.26812000, 0.83583000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q330937'),
(41184, 'Cormicy', 4820, 'GES', 75, 'FR', 49.37071000, 3.89595000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q330937'),
(41185, 'Cormontreuil', 4820, 'GES', 75, 'FR', 49.21667000, 4.05000000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q538779'),
(41186, 'Cornas', 4798, 'ARA', 75, 'FR', 44.96382000, 4.84839000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q538779'),
(41187, 'Cornebarrieu', 4799, 'OCC', 75, 'FR', 43.64895000, 1.32407000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q538779'),
(41188, 'Corneilhan', 4799, 'OCC', 75, 'FR', 43.39957000, 3.19147000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q538779'),
(41189, 'Corneilla-del-Vercol', 4799, 'OCC', 75, 'FR', 42.62390000, 2.95216000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q538779'),
(41190, 'Corneilla-la-Rivière', 4799, 'OCC', 75, 'FR', 42.69675000, 2.72962000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, 'Q538779'),
(41191, 'Corneville-sur-Risle', 4804, 'NOR', 75, 'FR', 49.34128000, 0.58628000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q468229'),
(41192, 'Cornier', 4798, 'ARA', 75, 'FR', 46.09304000, 6.29895000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q468229'),
(41193, 'Cornil', 4795, 'NAQ', 75, 'FR', 45.21062000, 1.69173000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q180500'),
(41194, 'Cornillon-Confoux', 4812, 'PAC', 75, 'FR', 43.56267000, 5.07162000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q384393'),
(41195, 'Cornimont', 4820, 'GES', 75, 'FR', 47.95998000, 6.83038000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q384393'),
(41196, 'Corny-sur-Moselle', 4820, 'GES', 75, 'FR', 49.03557000, 6.06084000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, 'Q21764'),
(41197, 'Corné', 4802, 'PDL', 75, 'FR', 47.47091000, -0.34992000, '2019-10-05 22:47:55', '2020-05-01 17:22:46', 1, '*********'),
(41198, 'Coron', 4802, 'PDL', 75, 'FR', 47.12726000, -0.64512000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41199, 'Corpeau', 4825, 'BFC', 75, 'FR', 46.92917000, 4.75226000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41200, 'Corps-Nuds', 4807, 'BRE', 75, 'FR', 47.97915000, -1.58409000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41201, 'Corquilleroy', 4818, 'CVL', 75, 'FR', 48.04212000, 2.70382000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41202, 'Corrèze', 4795, 'NAQ', 75, 'FR', 45.37244000, 1.87513000, '2019-10-05 22:47:55', '2020-05-01 17:22:45', 1, '********'),
(41203, 'Corsept', 4802, 'PDL', 75, 'FR', 47.27703000, -2.05904000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '********'),
(41204, 'Corseul', 4807, 'BRE', 75, 'FR', 48.48180000, -2.16947000, '2019-10-05 22:47:55', '2019-10-05 22:47:55', 1, '*********'),
(41205, 'Corte', 4806, '20R', 75, 'FR', 42.30956000, 9.14917000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41206, 'Corzé', 4802, 'PDL', 75, 'FR', 47.55956000, -0.39062000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, '*********'),
(41207, 'Cosnac', 4795, 'NAQ', 75, 'FR', 45.13423000, 1.58544000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q672599'),
(41208, 'Cosne-Cours-sur-Loire', 4825, 'BFC', 75, 'FR', 47.41101000, 2.92528000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q476253'),
(41209, 'Cosnes', 4820, 'GES', 75, 'FR', 49.51964000, 5.71210000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q476253'),
(41210, 'Cossé-le-Vivien', 4802, 'PDL', 75, 'FR', 47.94599000, -0.91185000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, '*********'),
(41211, 'Cotignac', 4812, 'PAC', 75, 'FR', 43.52876000, 6.14955000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q688450'),
(41212, 'Coubert', 4796, 'IDF', 75, 'FR', 48.67186000, 2.69733000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q688450'),
(41213, 'Coublevie', 4798, 'ARA', 75, 'FR', 45.35856000, 5.61146000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '********'),
(41214, 'Coubon', 4798, 'ARA', 75, 'FR', 44.99731000, 3.91783000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '********'),
(41215, 'Coubron', 4796, 'IDF', 75, 'FR', 48.91667000, 2.58333000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q275284'),
(41216, 'Couches', 4825, 'BFC', 75, 'FR', 46.86667000, 4.56667000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41217, 'Couchey', 4825, 'BFC', 75, 'FR', 47.25991000, 4.98257000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41218, 'Coucy-le-Château-Auffrique', 4828, 'HDF', 75, 'FR', 49.52083000, 3.32381000, '2019-10-05 22:47:56', '2020-05-01 17:22:45', 1, 'Q34796877'),
(41219, 'Coudekerque-Branche', 4828, 'HDF', 75, 'FR', 51.02288000, 2.39359000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q243851'),
(41220, 'Coudekerque-Village', 4828, 'HDF', 75, 'FR', 51.00000000, 2.41667000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q243851'),
(41221, 'Coudoux', 4812, 'PAC', 75, 'FR', 43.55800000, 5.24889000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q692732'),
(41222, 'Coudun', 4828, 'HDF', 75, 'FR', 49.46146000, 2.81248000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q692732'),
(41223, 'Couffé', 4802, 'PDL', 75, 'FR', 47.39120000, -1.29321000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, '********'),
(41224, 'Coufouleux', 4799, 'OCC', 75, 'FR', 43.81713000, 1.73078000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '********'),
(41225, 'Couhé', 4795, 'NAQ', 75, 'FR', 46.29911000, 0.18174000, '2019-10-05 22:47:56', '2020-05-01 17:22:45', 1, 'Q832135'),
(41226, 'Couilly-Pont-aux-Dames', 4796, 'IDF', 75, 'FR', 48.88473000, 2.85677000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q257909'),
(41227, 'Couiza', 4799, 'OCC', 75, 'FR', 42.94198000, 2.25453000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q188516'),
(41228, 'Coulaines', 4802, 'PDL', 75, 'FR', 48.02409000, 0.20411000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q305768'),
(41229, 'Coulanges-lès-Nevers', 4825, 'BFC', 75, 'FR', 47.00509000, 3.18756000, '2019-10-05 22:47:56', '2020-05-01 17:22:44', 1, 'Q305768'),
(41230, 'Coulans-sur-Gée', 4802, 'PDL', 75, 'FR', 48.02070000, 0.00974000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, 'Q305768'),
(41231, 'Coullons', 4818, 'CVL', 75, 'FR', 47.62105000, 2.49258000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q305768'),
(41232, 'Coulogne', 4828, 'HDF', 75, 'FR', 50.92463000, 1.88137000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q305768'),
(41233, 'Coulombiers', 4795, 'NAQ', 75, 'FR', 46.48324000, 0.18494000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q305768'),
(41234, 'Coulombs', 4818, 'CVL', 75, 'FR', 48.65253000, 1.54646000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q305768'),
(41235, 'Coulommiers', 4796, 'IDF', 75, 'FR', 48.81451000, 3.08498000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q305768'),
(41236, 'Coulon', 4795, 'NAQ', 75, 'FR', 46.32328000, -0.58561000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41237, 'Coulonges-sur-l’Autize', 4795, 'NAQ', 75, 'FR', 46.48333000, -0.59845000, '2019-10-05 22:47:56', '2020-05-01 17:22:45', 1, '*********'),
(41238, 'Coulounieix-Chamiers', 4795, 'NAQ', 75, 'FR', 45.15289000, 0.68852000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q746946'),
(41239, 'Coupvray', 4796, 'IDF', 75, 'FR', 48.89289000, 2.79670000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q828631'),
(41240, 'Cour-Cheverny', 4818, 'CVL', 75, 'FR', 47.51033000, 1.45583000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1429296'),
(41241, 'Courbevoie', 4796, 'IDF', 75, 'FR', 48.89672000, 2.25666000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q189715'),
(41242, 'Courcelles-Chaussy', 4820, 'GES', 75, 'FR', 49.10940000, 6.40153000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q189715'),
(41243, 'Courcelles-lès-Lens', 4828, 'HDF', 75, 'FR', 50.41667000, 3.01667000, '2019-10-05 22:47:56', '2020-05-01 17:22:45', 1, 'Q189715'),
(41244, 'Courcelles-lès-Montbéliard', 4825, 'BFC', 75, 'FR', 47.50113000, 6.78461000, '2019-10-05 22:47:56', '2020-05-01 17:22:44', 1, 'Q189715'),
(41245, 'Courcelles-sur-Seine', 4804, 'NOR', 75, 'FR', 49.18285000, 1.36008000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q189715'),
(41246, 'Courchelettes', 4828, 'HDF', 75, 'FR', 50.34043000, 3.05938000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q189715'),
(41247, 'Courchevel', 4798, 'ARA', 75, 'FR', 45.41538000, 6.63643000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q189715'),
(41248, 'Courcité', 4802, 'PDL', 75, 'FR', 48.30633000, -0.24961000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, '*********'),
(41249, 'Courcouronnes', 4796, 'IDF', 75, 'FR', 48.61429000, 2.40762000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '********'),
(41250, 'Courcy', 4820, 'GES', 75, 'FR', 49.32361000, 4.00257000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '********'),
(41251, 'Courdimanche', 4796, 'IDF', 75, 'FR', 49.03513000, 2.00096000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q249665'),
(41252, 'Courlay', 4795, 'NAQ', 75, 'FR', 46.77941000, -0.56607000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41253, 'Courlon-sur-Yonne', 4825, 'BFC', 75, 'FR', 48.33927000, 3.16660000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41254, 'Courmelles', 4828, 'HDF', 75, 'FR', 49.34595000, 3.31215000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q847122'),
(41255, 'Cournon-d’Auvergne', 4798, 'ARA', 75, 'FR', 45.74123000, 3.19643000, '2019-10-05 22:47:56', '2020-05-01 17:22:43', 1, 'Q847122'),
(41256, 'Cournonsec', 4799, 'OCC', 75, 'FR', 43.54944000, 3.70556000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1073109'),
(41257, 'Cournonterral', 4799, 'OCC', 75, 'FR', 43.55889000, 3.72000000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q196035'),
(41258, 'Courpalay', 4796, 'IDF', 75, 'FR', 48.64947000, 2.96116000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q196035'),
(41259, 'Courpière', 4798, 'ARA', 75, 'FR', 45.75689000, 3.54216000, '2019-10-05 22:47:56', '2020-05-01 17:22:43', 1, 'Q196035'),
(41260, 'Courrières', 4828, 'HDF', 75, 'FR', 50.45701000, 2.94725000, '2019-10-05 22:47:56', '2020-05-01 17:22:45', 1, 'Q323072'),
(41261, 'Cours-de-Pile', 4795, 'NAQ', 75, 'FR', 44.83608000, 0.54656000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1011936'),
(41262, 'Cours-la-Ville', 4798, 'ARA', 75, 'FR', 46.10000000, 4.31667000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1617140'),
(41263, 'Cours-les-Barres', 4818, 'CVL', 75, 'FR', 47.02513000, 3.03167000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1617140'),
(41264, 'Coursac', 4795, 'NAQ', 75, 'FR', 45.12881000, 0.63919000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1010864'),
(41265, 'Coursan', 4799, 'OCC', 75, 'FR', 43.23370000, 3.05712000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q379490'),
(41266, 'Courseulles-sur-Mer', 4804, 'NOR', 75, 'FR', 49.33027000, -0.45612000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q309123'),
(41267, 'Courteilles', 4804, 'NOR', 75, 'FR', 48.77495000, -0.19942000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q34797143'),
(41268, 'Courtenay', 4818, 'CVL', 75, 'FR', 48.03901000, 3.05851000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q34797143'),
(41269, 'Courthézon', 4812, 'PAC', 75, 'FR', 44.08760000, 4.88407000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, 'Q377943'),
(41270, 'Courtisols', 4820, 'GES', 75, 'FR', 48.98670000, 4.51700000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q377943'),
(41271, 'Courtry', 4796, 'IDF', 75, 'FR', 48.91906000, 2.60431000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q34797175'),
(41272, 'Courville-sur-Eure', 4818, 'CVL', 75, 'FR', 48.44899000, 1.24085000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1072386'),
(41273, 'Courzieu', 4798, 'ARA', 75, 'FR', 45.74270000, 4.57084000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q1072386'),
(41274, 'Courçon', 4795, 'NAQ', 75, 'FR', 46.24389000, -0.81300000, '2019-10-05 22:47:56', '2020-05-01 17:22:45', 1, 'Q34797181'),
(41275, 'Cousance', 4825, 'BFC', 75, 'FR', 46.53324000, 5.39214000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q34797186'),
(41276, 'Cousolre', 4828, 'HDF', 75, 'FR', 50.24607000, 4.14941000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q426922'),
(41277, 'Coussac-Bonneval', 4795, 'NAQ', 75, 'FR', 45.51199000, 1.32261000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q426922'),
(41278, 'Coutances', 4804, 'NOR', 75, 'FR', 49.04541000, -1.44518000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q202565'),
(41279, 'Couterne', 4804, 'NOR', 75, 'FR', 48.51318000, -0.41538000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q34797191'),
(41280, 'Couternon', 4825, 'BFC', 75, 'FR', 47.33333000, 5.15000000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q34797191'),
(41281, 'Coutiches', 4828, 'HDF', 75, 'FR', 50.45520000, 3.20384000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q941432'),
(41282, 'Coutouvre', 4798, 'ARA', 75, 'FR', 46.07303000, 4.20535000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q941432'),
(41283, 'Coutras', 4795, 'NAQ', 75, 'FR', 45.03333000, -0.13333000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q243898'),
(41284, 'Couvron-et-Aumencourt', 4828, 'HDF', 75, 'FR', 49.64476000, 3.51857000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q243898'),
(41285, 'Coux', 4798, 'ARA', 75, 'FR', 44.73484000, 4.62057000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q243898'),
(41286, 'Couzeix', 4795, 'NAQ', 75, 'FR', 45.87047000, 1.23828000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '********'),
(41287, 'Couzon-au-Mont-d’Or', 4798, 'ARA', 75, 'FR', 45.84436000, 4.82883000, '2019-10-05 22:47:56', '2020-05-01 17:22:43', 1, '********'),
(41288, 'Couëron', 4802, 'PDL', 75, 'FR', 47.21508000, -1.72171000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, '*********'),
(41289, 'Coye-la-Forêt', 4828, 'HDF', 75, 'FR', 49.14233000, 2.47038000, '2019-10-05 22:47:56', '2020-05-01 17:22:45', 1, 'Q651924'),
(41290, 'Cozes', 4795, 'NAQ', 75, 'FR', 45.58375000, -0.83178000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41291, 'Coësmes', 4807, 'BRE', 75, 'FR', 47.88325000, -1.44074000, '2019-10-05 22:47:56', '2020-05-01 17:22:44', 1, '*********'),
(41292, 'Coëtmieux', 4807, 'BRE', 75, 'FR', 48.49117000, -2.60067000, '2019-10-05 22:47:56', '2020-05-01 17:22:44', 1, '*********'),
(41293, 'Coëx', 4802, 'PDL', 75, 'FR', 46.69808000, -1.75956000, '2019-10-05 22:47:56', '2020-05-01 17:22:46', 1, '********'),
(41294, 'Crach', 4807, 'BRE', 75, 'FR', 47.61700000, -3.00170000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41295, 'Cran-Gevrier', 4798, 'ARA', 75, 'FR', 45.90000000, 6.10000000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q642409'),
(41296, 'Cransac', 4799, 'OCC', 75, 'FR', 44.52411000, 2.28370000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q389500'),
(41297, 'Cranves-Sales', 4798, 'ARA', 75, 'FR', 46.18799000, 6.29903000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, 'Q389500'),
(41298, 'Craon', 4802, 'PDL', 75, 'FR', 47.84687000, -0.94929000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********'),
(41299, 'Craponne', 4798, 'ARA', 75, 'FR', 45.74526000, 4.72322000, '2019-10-05 22:47:56', '2019-10-05 22:47:56', 1, '*********');

