<?php

namespace Database\Seeders;

use App\Models\TicketCategory;
use Illuminate\Database\Seeder;

class TicketCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $structure = [
            [
                'name' => 'Technical Support',
                'slug' => 'technical-support',
                'replies' => [
                    'can you specify the kind of technical support you need?',
                ],
                'children' => [
                    [
                        'name' => 'Retrieve Server ID',
                        'slug' => 'retrieve-server-id',
                        'replies' => [
                            'To retrieve your server ID, please follow these steps: [Link to Instructions]',
                        ],
                    ],
                    [
                        'name' => 'Sonar Installation',
                        'slug' => 'sonar-installation',
                        'replies' => [
                            'Here’s how to install SonarQube: http://ccc.com',
                        ],
                    ],
                    [
                        'name' => 'Subscription Support',
                        'slug' => 'subscription-support',
                        'replies' => [
                            'For subscription-related queries, please contact our sales team'
                        ],
                    ],
                    [
                        'name' => 'Something else',
                        'slug' => 'something-else-tech',
                        'replies' => [
                            'Please provide more details about your technical issue.',
                        ],
                    ],
                ],
            ],
            [
                'name' => 'Sales',
                'slug' => 'sales',
                'replies' => [
                    'Would you like to reach out to a specific account manager, or should we connect you with the first available sales contact?',
                ],
                'children' => [
                    [
                        'name' => 'Specific Account Manager',
                        'slug' => 'specific-account-manager',
                        'replies' => [],
                    ],
                    [
                        'name' => 'First Available Sales',
                        'slug' => 'first-available-sales',
                        'replies' => [],
                    ],
                ],
            ],
            [
                'name' => 'Request a demo',
                'slug' => 'request-a-demo',
                'children' => [], 
                'replies' => [],
            ],
            [
                'name' => 'Finance support',
                'slug' => 'finance-support',
                'children' => [], 
                'replies' => [],
            ],
        ];

        collect($structure)->each(function ($parentData) {
            // Create or update the parent category
            $parentCategory = TicketCategory::updateOrCreate(
                ['slug' => $parentData['slug']],
                ['name' => $parentData['name']]
            );

            // Handle replies for parent categories
            collect($parentData['replies'] ?? [])->each(function ($replyContent) use ($parentCategory) {
                $parentCategory->replies()->updateOrCreate(['content' => $replyContent]);
            });

            // Create or update child categories and their replies
            collect($parentData['children'])->each(function ($childData) use ($parentCategory) {
                $childCategory = $parentCategory->children()->updateOrCreate(
                    ['slug' => $childData['slug']],
                    ['name' => $childData['name']]
                );

                collect($childData['replies'] ?? [])->each(function ($replyContent) use ($childCategory) {
                    $childCategory->replies()->updateOrCreate(['content' => $replyContent]);
                });
            });
        });
    }
}
