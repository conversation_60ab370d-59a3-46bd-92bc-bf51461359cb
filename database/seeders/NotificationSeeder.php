<?php

namespace Database\Seeders;

use App\Enums\DefaultValues;
use App\Enums\Notifications;
use App\Models\NotificationType;
use App\Models\NotificationTypeTranslation;
use App\Models\User;
use App\Models\UserNotificationSetting;
use Illuminate\Database\Seeder;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedNotificationTypes();
        $this->seedDefaultUserNotificationSettings();
    }

    private function seedNotificationTypes(): void
    {
        $notificationTypes = [
            [
                'name' => Notifications::NOTIFICATION_EVENT_ACCOUNT_CREATED->value,
                'is_visible' => false,
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_CREATED->value,
                'sort_order' => null,  // Hidden notifications don't need sorting
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when a new account is created',
                        'template_string' => 'Welcome {user_name} your account has been created successfully.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_ACCOUNT_OTP_SENT->value,
                'is_visible' => false,
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_OTP_SENT->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'otp_code', 'action', 'expiry_minutes'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when OTP verification code is generated',
                        'template_string' => 'Hello {user_name}, your OTP verification code for {action} is: {otp_code}. This code will expire in {expiry_minutes} minutes.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET->value,
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_PASSWORD_RESET->value,
                'sort_order' => 1,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when password reset is requested',
                        'template_string' => 'Hello {user_name}, your password has been changed.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_ACCOUNT_ROLE_CHANGED->value,
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_ROLE_CHANGED->value,
                'sort_order' => 2,
                'is_visible' => false,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'old_role', 'new_role', 'changed_by'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when user role is changed',
                        'template_string' => 'Hello {user_name}, your account role has been changed from {old_role} to {new_role} by {changed_by}.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT->value,
                'is_visible' => false,
                'event' => Notifications::NOTIFICATION_EVENT_ACCOUNT_INVITATION_SENT->value,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'company_name', 'invitation_url'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when user is invited to join workspace',
                        'template_string' => 'Hello {user_name}, you have been invited to join {company_name} workspace. Click here to accept: {invitation_url}'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_SUBSCRIPTION_ACTIVATED->value,
                'event' => Notifications::NOTIFICATION_EVENT_SUBSCRIPTION_ACTIVATED->value,
                'sort_order' => 5,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'subscription_id', 'plan_name'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when subscription is activated',
                        'template_string' => 'Hello {user_name}, your subscription {subscription_id} for {plan_name} has been activated successfully.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_PROVIDED_LICENSE->value,
                'event' => Notifications::NOTIFICATION_EVENT_PROVIDED_LICENSE->value,
                'sort_order' => 8,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'license_key', 'environment', 'subscription_id'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when license key is provided',
                        'template_string' => 'Hello {user_name}, your license key for {environment} environment has been provided. License: {license_key} for subscription {subscription_id}.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_ASSIGNED_LICENSE->value,
                'event' => Notifications::NOTIFICATION_EVENT_ASSIGNED_LICENSE->value,
                'sort_order' => 7,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'license_key', 'environment', 'subscription_id'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when license is activated',
                        'template_string' => 'Hello {user_name}, your license for {environment} environment has been activated. License: {license_key} for subscription {subscription_id}.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_RECEIPT_APPROVED->value,
                'event' => Notifications::NOTIFICATION_EVENT_RECEIPT_APPROVED->value,
                'sort_order' => 3,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'license_key', 'environment', 'subscription_id'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when payment is approved',
                        'template_string' => 'Hello {user_name}, your payment for subscription {subscription_id} has been approved.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_RECEIPT_REJECTED->value,
                'event' => Notifications::NOTIFICATION_EVENT_RECEIPT_REJECTED->value,
                'sort_order' => 4,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'license_key', 'environment', 'subscription_id'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when payment is rejected',
                        'template_string' => 'Hello {user_name}, your payment for subscription {subscription_id} has been rejected.'
                    ]
                ]
            ],
            [
                'name' => Notifications::NOTIFICATION_EVENT_LICENSE_REQUESTED->value,
                'event' => Notifications::NOTIFICATION_EVENT_LICENSE_REQUESTED->value,
                'sort_order' => 6,
                'default_channels' => [
                    Notifications::NOTIFICATION_CHANNEL_MAIL->value,
                    Notifications::NOTIFICATION_CHANNEL_DATABASE->value
                ],
                'placeholders' => ['user_name', 'subscription_id'],
                'translations' => [
                    'en' => [
                        'description' => 'Notification sent when license is requested',
                        'template_string' => 'Hello {user_name}, your license request for subscription {subscription_id} has been received.'
                    ]
                ]
            ]
        ];

        foreach ($notificationTypes as $typeData) {
            $translations = $typeData['translations'];
            unset($typeData['translations']);

            $notificationType = NotificationType::updateOrCreate(
                ['name' => $typeData['name']],
                array_merge($typeData, [
                    'created_by' => DefaultValues::SYSTEM_USER_ID->get(),
                    'updated_by' => DefaultValues::SYSTEM_USER_ID->get(),
                    'is_visible' => $typeData['is_visible'] ?? true,
                ])
            );

            // Create translations
            foreach ($translations as $langCode => $translation) {
                NotificationTypeTranslation::updateOrCreate(
                    [
                        'type_id' => $notificationType->notification_type_id,
                        'language_code' => $langCode
                    ],
                    array_merge($translation, [
                        'created_by' => DefaultValues::SYSTEM_USER_ID->get(),
                        'updated_by' => DefaultValues::SYSTEM_USER_ID->get(),
                    ])
                );
            }
        }
    }

    /**
     * Seed default notification settings for all existing users.
     * This ensures that existing users have default email and SMS settings.
     */
    private function seedDefaultUserNotificationSettings(): void
    {
        $users = User::all();
        $notificationTypes = NotificationType::all();

        foreach ($users as $user) {
            foreach ($notificationTypes as $notificationType) {
                // Only create if setting doesn't already exist
                UserNotificationSetting::firstOrCreate(
                    [
                        'user_id' => $user->user_id,
                        'notification_type_id' => $notificationType->notification_type_id,
                    ],
                    [
                        'is_enabled' => true,
                        'email_enabled' => true,
                        'sms_enabled' => true,
                        'delivery_channels' => null,
                        'created_by' => DefaultValues::SYSTEM_USER_ID->get(),
                        'updated_by' => DefaultValues::SYSTEM_USER_ID->get(),
                    ]
                );
            }
        }
    }
}