<?php

namespace Database\Seeders;

use App\Enums\DefaultValues;
use App\Models\Feature;
use App\Models\Plan;
use App\Models\PlanFeature;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Schema;

class PlanSeeder extends Seeder
{
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();

        Plan::truncate();
        Feature::truncate();
        PlanFeature::truncate();

        Schema::enableForeignKeyConstraints();

        DB::transaction(function () {
            $defaultCurrency = DefaultValues::CURRENCY->get();

            /**
             * Pricing data for different plans.
             */
            $pricingData = [
                // Package, ONPREM, KSA-GCP, UAE-Azure, UAE-AWS, Qatar-GCP, Qatar-Azure, Support, Security
                ['1M', 49000, 58833, 59536, 64796, 58482, 59486, 40000, 12750],
                ['5M', 62500, 72603, 73036, 78341, 72190, 72986, 40000, 39000],
                ['10M', 83500, 93666, 94066, 99398, 93238, 94022, 40000, 60000],
                ['20M', 101500, 111947, 112287, 117410, 111451, 112235, 40000, 78000],
                ['30M', 121500, 132010, 132351, 137431, 131490, 132306, 40000, 120000],
                ['50M', 158500, 175344, 176322, 182907, 173308, 176288, 40000, 157000],
                ['75M', 218500, 245421, 246915, 266405, 240961, 246849, 43400, 217000],
                ['100M', 284500, 311421, 312915, 332405, 306961, 312849, 56600, 390000],
            ];

            $features = [
                [
                    'id' => 1,
                    'name' => 'On Prem',
                    'valid_from_minimum' => null,
                    'valid_to_maximum'   => null,
                    'price' => 0,
                    'value' => 0,
                ],
                [
                    'id' => 2,
                    'name' => 'Saas',
                    'valid_from_minimum' => null,
                    'valid_to_maximum'   => null,
                    'price' => 0,
                    'value' => 0,
                ],
            ];

            $locTiers = $this->getLocTiers();

            $saasCombinations = [
                2 => ['iso' => 'SA', 'provider' => 'GCP'],
                3 => ['iso' => 'AE', 'provider' => 'Azure'],
                4 => ['iso' => 'AE', 'provider' => 'AWS'],
                5 => ['iso' => 'QA', 'provider' => 'GCP'],
                6 => ['iso' => 'QA', 'provider' => 'Azure'],
            ];

            $lineOfCodes = ['On Prem' => [], 'Saas' => []];

            foreach ($pricingData as $index => $row) {
                $package = $row[0];
                $tier = $locTiers[$package];

                // On-prem bundles
                $lineOfCodes['On Prem'][] = [
                    'name' => 'line of code bundle ' . ($index + 1),
                    'valid_from_minimum' => $tier['min'],
                    'valid_to_maximum' => $tier['max'],
                    'price' => $row[1],
                    'value' => 0,
                    'country_iso' => null,
                    'provider' => null,
                ];

                // SaaS bundles
                foreach ($saasCombinations as $colIndex => $combo) {
                    $lineOfCodes['Saas'][] = [
                        'name' => 'line of code bundle ' . ($index + 1),
                        'valid_from_minimum' => $tier['min'],
                        'valid_to_maximum' => $tier['max'],
                        'price' => $row[$colIndex],
                        'value' => 0,
                        'country_iso' => $combo['iso'],
                        'provider' => $combo['provider'],
                    ];
                }
            }

            $addons = [
                'On Prem' => $this->getPlansAddons(),
                'Saas' => $this->getPlansAddons()
            ];

            $extraAddons = [
                'On Prem' => $this->getPlansExtraAddons($pricingData, $locTiers),
                'Saas' => $this->getPlansExtraAddons($pricingData, $locTiers)
            ];

            $plans = [
                [
                    'name' => "Trial Version",
                    'description' => "Analyze your projects, identify code smells, bugs, and vulnerabilities, and explore the platform's features and benefits before committing to a subscription.",
                    'currency' => $defaultCurrency,
                    'billing_cycle' => 'yearly',
                    'trial_interval' => 'day',
                    'trial_period' => DefaultValues::SUBSCRIPTION_TRIAL_PERIOD_DAYS->get(),
                    'is_active' => true,
                    'features' => $features,
                    'line_of_codes' => $lineOfCodes,
                ],
                [
                    'name' => 'Enterprise Edition',
                    'description' => 'Enterprise Edition offers advanced features and functionalities to meet the demanding needs of enterprise software development. ',
                    'badge' => 'Starting USD 40K a year - VAT Excl.',
                    'currency' => $defaultCurrency,
                    'billing_cycle' => 'yearly',
                    'is_active' => true,
                    'features' => $features,
                    'line_of_codes' => $lineOfCodes,
                    'addons' => $extraAddons,
                ],
                [
                    'name' => 'Data Center Edition',
                    'description' => 'Data Center Edition offers a clustered deployment option. This ensures continuous operation and optimal performance even under heavy loads, making it ideal for mission-critical applications and large development teams.',
                    'badge' => 'Starting USD 40K a year - VAT Excl.',
                    'currency' => $defaultCurrency,
                    'billing_cycle' => 'yearly',
                    'is_active' => true,
                    'features' => $features,
                    'addons' => [
                        'On Prem' => array_merge(
                            $addons['On Prem'],
                            $extraAddons['On Prem']
                        ),
                        'Saas'    => array_merge(
                            $addons['Saas'],
                            $extraAddons['Saas']
                        )
                    ],
                    'line_of_codes' => $lineOfCodes,
                ],
            ];

            foreach ($plans as $plan) {
                $planFeaturesData = $plan['features'];
                $planAddonsData = $plan['addons'] ?? [];
                $planLineOfCodesData = $plan['line_of_codes'];

                unset($plan['features'], $plan['addons'], $plan['line_of_codes']);

                $planModel = Plan::updateOrCreate(['name->en' => $plan['name']], $plan);


                collect($planFeaturesData)->each(function ($feature) use ($planModel) {
                    $featureModel = Feature::create(
                        ['plan_id' => $planModel->id, 'name->en' => $feature['name'], 'value' => $feature['value']],
                    );
                    PlanFeature::updateOrCreate(
                        [
                            'plan_id' => $planModel->id,
                            'feature_id' => $featureModel->id,
                            'valid_from_minimum' => $feature['valid_from_minimum'],
                            'valid_to_maximum' => $feature['valid_to_maximum']
                        ],
                        ['price' => $feature['price']]
                    );
                });

                collect($planAddonsData)->each(function ($addonsArr, $parentFeatureName) use ($planModel) {

                    $parentFeature = Feature::where('plan_id', $planModel->id)->where('name->en', $parentFeatureName)->first();

                    // if ($planModel->name === 'Data Center Edition') {
                    //         print_r($addonsArr);
                    //         exit;
                    // }

                    collect($addonsArr)->each(function ($addon) use ($planModel, $parentFeature, $parentFeatureName) {

                        $addonFeature = Feature::updateOrCreate(
                            ['plan_id' => $planModel->id, 'name->en' => $addon['name']],
                            ['value' => $addon['value']]
                        );

                        $parentPlanFeatureModel = PlanFeature::with('feature')
                            ->where('plan_id', $planModel->id)
                            ->where('feature_id', $parentFeature->id)
                            ->whereHas('feature', function ($q) use ($parentFeature) {
                                $q->where('name->en', $parentFeature->name);
                            })
                            ->first();

                        PlanFeature::updateOrCreate(
                            [
                                'plan_id' => $planModel->id,
                                'feature_id' => $addonFeature->id,
                                'parent_plan_feature_id' => $parentPlanFeatureModel->plan_feature_id,
                                'valid_from_minimum' => $addon['valid_from_minimum'],
                                'valid_to_maximum' => $addon['valid_to_maximum'],
                            ],
                            [
                                'price' => $addon['price'],
                                'is_addon' => $addon['is_addon'] ?? false,
                                'is_extra_addon' => $addon['is_extra_addon'] ?? false,
                                'country_iso' => null,
                                'provider' => null,
                            ]
                        );
                    });
                });

                collect($planLineOfCodesData)->each(function ($locArr, $parentFeatureName) use ($planModel) {
                    $parentFeature = Feature::where('plan_id', $planModel->id)->where('name->en', $parentFeatureName)->first();
                    collect($locArr)->each(function ($loc) use ($planModel, $parentFeature) {

                        $locFeature = Feature::updateOrCreate(
                            ['plan_id' => $planModel->id, 'name->en' => $loc['name']],
                            ['value' => $loc['value']]
                        );

                        $parentPlanFeatureModel = PlanFeature::with('feature')
                            ->where('plan_id', $planModel->id)
                            ->where('feature_id', $parentFeature->id)
                            ->whereHas('feature', function ($q) use ($parentFeature) {
                                $q->where('name->en', $parentFeature->name);
                            })
                            ->first();

                        PlanFeature::updateOrCreate(
                            [
                                'plan_id' => $planModel->id,
                                'feature_id' => $locFeature->id,
                                'parent_plan_feature_id' => $parentPlanFeatureModel->plan_feature_id,
                                'valid_from_minimum' => $loc['valid_from_minimum'],
                                'valid_to_maximum' => $loc['valid_to_maximum'],
                                'country_iso' => $loc['country_iso'],
                                'provider' => $loc['provider'],
                            ],
                            ['price' => $loc['price']]
                        );
                    });
                });
            }
        });
    }

    private function getLocTiers(): array
    {
        return [
            '1M'   => ['min' => 1, 'max' => 1000000],
            '5M'   => ['min' => 1000001, 'max' => 5000000],
            '10M'  => ['min' => 5000001, 'max' => 10000000],
            '20M'  => ['min' => 10000001, 'max' => 20000000],
            '30M'  => ['min' => 20000001, 'max' => 30000000],
            '50M'  => ['min' => 30000001, 'max' => 50000000],
            '75M'  => ['min' => 50000001, 'max' => 75000000],
            '100M' => ['min' => 75000001, 'max' => 100000000],
        ];
    }

    /**
     * DR price is 10% of the original license price  
     */
    private function getPlansAddons(): array
    {
        return [
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => null, 'valid_to_maximum' => null, 'price' => 0, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 1, 'valid_to_maximum' => 1000000, 'price' => 4750, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 1000001, 'valid_to_maximum' => 5000000, 'price' => 6100, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 5000001, 'valid_to_maximum' => 10000000, 'price' => 8200, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 10000001, 'valid_to_maximum' => 20000000, 'price' => 10000, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 20000001, 'valid_to_maximum' => 30000000, 'price' => 12000, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 30000001, 'valid_to_maximum' => 50000000, 'price' => 15700, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 50000001, 'valid_to_maximum' => 75000000, 'price' => 21700, 'value' => 0, 'is_addon' => true],
            ['name' => 'Include Disaster Recovery', 'valid_from_minimum' => 75000001, 'valid_to_maximum' => 100000000, 'price' => 28300, 'value' => 0, 'is_addon' => true],
        ];
    }

    private function getPlansExtraAddons(array $pricingData, array $locTiers): array
    {
        $addons = [
            ['name' => 'Premium support', 'valid_from_minimum' => null, 'valid_to_maximum' => null, 'price' => 0, 'value' => 0, 'is_addon' => true, 'is_extra_addon' => true],
            ['name' => 'Security add-on', 'valid_from_minimum' => null, 'valid_to_maximum' => null, 'price' => 0, 'value' => 0, 'is_addon' => true, 'is_extra_addon' => true],
        ];
        foreach ($pricingData as $row) {
            $tier = $locTiers[$row[0]];
            $addons[] = ['name' => 'Premium support', 'valid_from_minimum' => $tier['min'], 'valid_to_maximum' => $tier['max'], 'price' => $row[7], 'value' => 0, 'is_addon' => true, 'is_extra_addon' => true];
            $addons[] = ['name' => 'Security add-on', 'valid_from_minimum' => $tier['min'], 'valid_to_maximum' => $tier['max'], 'price' => $row[8], 'value' => 0, 'is_addon' => true, 'is_extra_addon' => true];
        }
        return $addons;
    }
}
