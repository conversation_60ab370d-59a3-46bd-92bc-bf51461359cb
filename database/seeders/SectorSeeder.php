<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Sector;

class SectorSeeder extends Seeder
{
    public function run(): void
    {
        $sectors = [
            ['name' => 'Banking'],
            ['name' => 'Insurance'],
            ['name' => 'Capital Markets'],
            ['name' => 'Retail'],
            ['name' => 'E-commerce'],
            ['name' => 'Healthcare'],
            ['name' => 'Pharmaceuticals'],
            ['name' => 'Public Sector'],
            ['name' => 'Education'],
            ['name' => 'Energy'],
            ['name' => 'Telecommunications'],
            ['name' => 'Transportation'],
            ['name' => 'Logistics & Supply Chain'],
            ['name' => 'Manufacturing'],
            ['name' => 'Construction'],
            ['name' => 'Real Estate'],
            ['name' => 'Automotive'],
            ['name' => 'Media & Entertainment'],
            ['name' => 'Travel & Hospitality'],
            ['name' => 'Agriculture'],
            ['name' => 'Food & Beverage'],
            ['name' => 'Technology'],
            ['name' => 'IT & Software Services'],
            ['name' => 'Legal & Compliance'],
            ['name' => 'Marketing & Advertising'],
            ['name' => 'Other']
        ];

        foreach ($sectors as $sector) {
            Sector::firstOrCreate($sector);
        }
    }
}
