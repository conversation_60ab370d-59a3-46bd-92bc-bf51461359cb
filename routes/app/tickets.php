<?php

use App\Http\Controllers\Tickets\Api\TicketsController;
use Illuminate\Support\Facades\Route;

Route::prefix('v1/tickets')->middleware(['jwt.auth'])->group(function () {

    # tickets
    Route::get('/', [TicketsController::class, 'index']);
    Route::post('/', [TicketsController::class, 'store']);
    Route::post('/{ticket}/reply', [TicketsController::class, 'reply']);

    # ticket categories
    Route::get('/categories', [TicketsController::class, 'getCategories']);
});
