<?php

use App\Http\Controllers\Auth\Api\DeveloperController;
use App\Http\Controllers\Auth\Api\LoginController;
use App\Http\Controllers\Auth\Api\LogoutController;
use App\Http\Controllers\Auth\Api\OtpController;
use App\Http\Controllers\Auth\Api\PasswordController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\Api\RegistrationController;
use App\Http\Controllers\Settings\Api\SettingsController;

Route::prefix('v1')->group(function () {
    Route::post('/register', [RegistrationController::class, 'register']);

    Route::post('/login', [LoginController::class, 'login']);

    Route::post('/otp/verify', [OtpController::class, 'verify']);
    Route::post('/otp/renew', [OtpController::class, 'renew']);

    Route::post('/password/set', [PasswordController::class, 'set']);
    Route::post('/password/forgot', [PasswordController::class, 'forgot']);
    Route::post('/password/reset', [PasswordController::class, 'reset']);

    Route::get('/settings/list', [SettingsController::class, 'list']);
});

Route::prefix('v1')->middleware(['jwt.auth'])->group(function () {
    Route::post('/logout', [LogoutController::class, 'logout']);

    Route::post('/password/change', [PasswordController::class, 'change']);
});