<?php

namespace App\Http\Requests;

use App\Enums\DefaultValues;
use App\Helpers\ApiHelper;
use App\Services\ApiResponseService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Contracts\Validation\Validator;

class StoreTicketReplyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * We'll set this to true, but you can add custom logic here if needed.
     */
    public function authorize(): bool
    {
        // TODO: create a custom permission for adding a ticket 
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
          'content' => ['required', 'string', 'max:65535'],
        ];
    }

    /**
     * Handle a failed validation attempt. 
     */
    protected function failedValidation(Validator $validator)
    {
        $response = ApiHelper::validationError(
            app(ApiResponseService::class),
            $validator->errors()->toArray()
        );

        throw new HttpResponseException($response);
    }
}
