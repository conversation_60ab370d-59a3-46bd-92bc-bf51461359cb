<?php

namespace App\Http\Controllers\Tickets\Api;

use App\Enums\ApiStatus;
use App\Helpers\AppHelper;
use App\Helpers\CustomerHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PaginatedRequest;
use App\Http\Requests\StoreTicketReplyRequest;
use App\Http\Requests\StoreTicketRequest;
use App\Http\Resources\TicketCategoryResource;
use App\Http\Resources\TicketReplyResource;
use App\Http\Resources\TicketResource;
use App\Models\Ticket;
use App\Services\ApiResponseService;
use App\Services\TicketService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TicketsController extends Controller
{
    public function __construct(
        protected TicketService $ticketService,
        protected ApiResponseService $apiResponseService
    ) {}

    /**
     * Get a nested list of ticket categories.
     */
    public function getCategories(Request $request): JsonResponse
    {
        $categories = $this->ticketService->listCategories();
        $data = TicketCategoryResource::collection($categories)->toArray($request);
        
        return $this->apiResponseService
            ->props(ApiStatus::SUCCESS)
            ->withData($data)
            ->send();
    }

    /**
     * Display a paginated list of the user's tickets.
     */
    public function index(PaginatedRequest $request): JsonResponse
    {
        $data = $this->ticketService->listUserTickets($request);

        return $this->apiResponseService
            ->props(ApiStatus::SUCCESS)
            ->withData($data)
            ->send();
    }

    /**
     * Store a newly created ticket.
     */
    public function store(StoreTicketRequest $request): JsonResponse
    {
        try {
            $ticket = $this->ticketService->createNewTicket($request->validated());
            $data = (new TicketResource($ticket))->toArray($request);

            return $this->apiResponseService
                ->props(ApiStatus::CREATED, 'Thanks! Your message has been sent successfully, our team will get back to you shortly.')
                ->withData($data)
                ->send();

        } catch (Exception $e) {

            # Company not found
            if( AppHelper::matchStrings($e->getMessage(), 'company-not-found')) {
               return CustomerHelper::companyNotFound($this->apiResponseService);
            }

            # Insufficient balance
            if (AppHelper::matchStrings($e->getMessage(), 'insufficient-balance')) {
                return $this->apiResponseService
                    ->props(ApiStatus::BAD_REQUEST, 'Your company has no ticket balance remaining.')
                    ->send();
            }

            return $this->apiResponseService
                ->props(ApiStatus::SERVER_ERROR, 'An unexpected error occurred while creating your ticket. Please try again later.')
                ->send();
        }
    }

    /**
     * Store a new reply for a ticket.
     */
    public function reply(StoreTicketReplyRequest $request, Ticket $ticket): JsonResponse
    {
        $reply = $this->ticketService->createTicketReply($ticket, $request->validated());

        if (!$reply) {
            return $this->apiResponseService
                ->props(ApiStatus::FORBIDDEN, 'You are not authorized to reply to this ticket.')
                ->send();
        }

        $data = (new TicketReplyResource($reply))->toArray($request);

        return $this->apiResponseService
            ->props(ApiStatus::CREATED, 'Reply submitted successfully.')
            ->withData($data)
            ->send();
    }
}
