<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TicketCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->ticket_category_id,
            'name' => $this->name,
            'slug' => $this->slug,
            'replies' => TicketCategoryReplyResource::collection($this->whenLoaded('replies')),
            'children' => TicketCategoryResource::collection($this->whenLoaded('children')),
        ];
    }
}
