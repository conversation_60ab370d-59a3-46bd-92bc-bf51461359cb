<?php

namespace App\Filament\Resources\TicketResource\Pages;

use App\Enums\TicketEnum;
use App\Filament\Resources\TicketResource;
use App\Helpers\AppHelper;
use App\Models\User;
use Filament\Actions;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid as InfolistGrid;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Contracts\Support\Htmlable;
use TangoDevIt\FilamentEmojiPicker\EmojiPickerAction;

class ViewTicket extends ViewRecord implements HasForms, HasInfolists
{
    use InteractsWithForms;
    use InteractsWithInfolists;

    protected static string $resource = TicketResource::class;
    protected static string $view = 'filament.resources.ticket-resource.pages.view-ticket';

    public ?array $data = [];

    public function mount($record): void
    {
        parent::mount($record);
        $this->form->fill();
    }

    public function getTitle(): string | Htmlable
    {
        return 'Manage ' . $this->record->customer->name . '\'s issue';
    }

    /**
     * Define the main infolist using a Split component for the layout.
     */
    public function ticketInfolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->record($this->record)
            ->schema([
                InfolistGrid::make(12)->schema([
                    InfolistSection::make('Ticket Details')
                        ->heading(null)
                        ->schema([
                            InfolistGrid::make(4)->schema([
                                TextEntry::make('ticket_id')->label('Ticket ID')->copyable(),
                                TextEntry::make('customer.user_id')->label('User ID')->copyable(),
                                TextEntry::make('customer.name')->label('Full Name')->copyable(),
                                TextEntry::make('customer.email')
                                    ->copyable()
                                    ->label('Email address')
                                    ->extraAttributes([
                                        'class' => 'truncate',
                                    ]),
                            ]),
                            InfolistGrid::make(4)->schema([
                                TextEntry::make('customer.phone_number')->label('Phone')->copyable(),
                                TextEntry::make('status')
                                    ->badge()
                                    ->copyable()
                                    ->color(fn(TicketEnum $state): string => match ($state) {
                                        TicketEnum::TICKET_STATUS_OPEN => 'info',
                                        TicketEnum::TICKET_STATUS_RESOLVED => 'success',
                                        TicketEnum::TICKET_STATUS_CLOSED => 'danger',
                                        default => 'gray',
                                    }),
                                TextEntry::make('assignee.name')->label('Assigned To')->copyable(),
                            ]),
                        ])->columnSpan(8),

                    InfolistSection::make('Timestamps')
                        ->heading(null)
                        ->schema([
                            TextEntry::make('created_at')->label('Created at')->since(),
                            TextEntry::make('updated_at')->label('Last modified')->since(),
                            TextEntry::make('resolved_at')->label('Resolved at')->since()->placeholder('---'),
                        ])
                        ->columnSpan(4),
                ]),
            ]);
    }

    /**
     * Define the form for submitting a new reply.
     */
    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('content')
                    ->label('')
                    ->placeholder('Write your message here...')
                    ->required()
                    ->maxLength(255)
                    ->markAsRequired(false)
                    ->prefixAction(EmojiPickerAction::make('emoji-title'))
                    ->suffixAction(
                        Action::make('sendReply')
                            ->label('Send')
                            ->icon('heroicon-o-paper-airplane')
                            ->action('reply')
                    ),
            ])
            ->statePath('data');
    }

    /**
     * This action is called when the reply form is submitted.
     */
    public function reply(): void
    {
        $data = $this->form->getState();

        $this->record->replies()->create([
            'user_id' => auth()->id(),
            'content' => $data['content'],
        ]);

        $this->form->fill();

        Notification::make()
            ->title('Reply sent successfully')
            ->success()
            ->send();
    }

    /**
     * Defines the interactive actions for changing status and assigning agents.
     */
    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('assign')
                ->label('Assign Ticket')
                ->icon('heroicon-o-user-plus')
                ->form([
                    Select::make('assigned_to')
                        ->label('Assign To Agent')
                        ->options(User::where('type', 'admin')->pluck('name', 'user_id'))
                        ->default($this->record->assigned_to)
                        ->searchable()
                        ->native(false)
                        ->required(),
                ])
                ->action(function (array $data) {
                    $this->record->update(['assigned_to' => $data['assigned_to']]);
                    Notification::make()->title('Ticket assigned')->success()->send();
                }),

            Actions\Action::make('status')
                ->label('Change Status')
                ->icon('heroicon-o-tag')
                ->form([
                    Select::make('status')
                        ->label('Status')
                        ->native(false)
                        ->searchable()
                        ->options(collect(TicketEnum::cases())->filter(fn($case) => str_starts_with($case->name, 'TICKET_STATUS'))->mapWithKeys(fn($case) => [$case->value => $case->label()]))
                        ->default($this->record->status->value)
                        ->required(),
                ])
                ->action(function (array $data) {
                    $updateData = ['status' => $data['status']];

                    if (AppHelper::matchStrings($data['status'], TicketEnum::TICKET_STATUS_RESOLVED)) {
                        $updateData['resolved_at'] = now();
                    } else {
                        $updateData['resolved_at'] = null;
                    }

                    $this->record->update($updateData);
                    Notification::make()->title('Status updated')->success()->send();
                }),

            Actions\EditAction::make(),
        ];
    }

    public function getFormStatePath(): ?string
    {
        return 'data';
    }
}
