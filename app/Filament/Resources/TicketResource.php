<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TicketResource\Pages;
use App\Models\Ticket;
use App\Models\User;
use App\Enums\TicketEnum;
use App\Helpers\RbacHelper;
use App\Models\TicketCategory;
use App\Traits\HasShieldAccess;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class TicketResource extends Resource
{
    use HasShieldAccess;

    protected static ?string $model = Ticket::class;

    protected static ?string $navigationIcon = 'heroicon-o-ticket';
    protected static ?string $navigationGroup = 'Tickets';
    protected static ?string $recordTitleAttribute = 'title';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()->schema([
                    Forms\Components\TextInput::make('title')
                        ->required()
                        ->maxLength(255),
                    Forms\Components\Select::make('user_id')
                        ->relationship(
                            'customer',
                            'name',
                            fn($query) => $query->where('type', RbacHelper::TYPE_CUSTOMER)
                        )
                        ->searchable()
                        ->native(false)
                        ->required(),
                    Forms\Components\Select::make('ticket_category_id')
                        ->relationship('category', 'name')
                        ->native(false)
                        ->searchable()
                        ->required()
                        ->preload(),
                    Forms\Components\RichEditor::make('content')
                        ->required()
                        ->columnSpanFull(),
                ])->columns(3),

                Forms\Components\Section::make('Details')->schema([
                    Forms\Components\Select::make('status')
                        ->options(TicketEnum::getEnumCases('TICKET_STATUS'))
                        ->native(false)
                        ->searchable()
                        ->required(),
                    Forms\Components\Select::make('priority')
                        ->options(TicketEnum::getEnumCases('TICKET_PRIORITY'))
                        ->native(false)
                        ->searchable()
                        ->required(),
                    Forms\Components\Select::make('assigned_to')
                        ->label('Assign To Agent')
                        ->options(User::where('type', RbacHelper::TYPE_ADMIN)->get()->pluck('name', 'user_id'))
                        ->native(false)
                        ->searchable()
                ])->columns(3),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('ticket_id')
                    ->label('Ticket ID')
                    ->searchable(),
                Tables\Columns\TextColumn::make('title')
                    ->searchable(),
                Tables\Columns\TextColumn::make('customer.name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('category.name')
                    ->sortable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->color(fn(TicketEnum $state): string => match ($state) {
                        TicketEnum::TICKET_STATUS_OPEN => 'info',
                        TicketEnum::TICKET_STATUS_RESOLVED => 'success',
                        TicketEnum::TICKET_STATUS_CLOSED => 'warning',
                    }),
                Tables\Columns\BadgeColumn::make('priority')
                    ->color(fn(TicketEnum $state): string => match ($state) {
                        TicketEnum::TICKET_PRIORITY_LOW => 'info',
                        TicketEnum::TICKET_PRIORITY_MEDIUM => 'warning',
                        TicketEnum::TICKET_PRIORITY_HIGH => 'danger',
                    }),
                Tables\Columns\TextColumn::make('assignee.name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([

                Tables\Filters\SelectFilter::make('ticket_category_id')
                    ->label('Category')
                    ->options(TicketCategory::getHierarchicalCategoryOptions())
                    ->searchable()
                    ->native(false),

                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->native(false)
                    ->searchable()
                    ->preload()
                    ->options(TicketEnum::getEnumCases('TICKET_STATUS')),

                Tables\Filters\SelectFilter::make('customer')
                    ->relationship('customer', 'name')
                    ->label('Customer')
                    ->native(false)
                    ->searchable()
                    ->preload(),

                // Filter by Assigned Support Agent
                Tables\Filters\SelectFilter::make('assignee')
                    ->relationship('assignee', 'name')
                    ->label('Assigned Support Agent')
                    ->native(false)
                    ->searchable()
                    ->preload(),

                // Filter by Created At Date Range
                Tables\Filters\Filter::make('created_at')
                    ->form([
                        Forms\Components\DatePicker::make('created_from')
                            ->label('Created From'),
                        Forms\Components\DatePicker::make('created_until')
                            ->label('Created Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),

                // Filter by Updated At Date Range
                Tables\Filters\Filter::make('updated_at')
                    ->form([
                        Forms\Components\DatePicker::make('updated_from')
                            ->label('Updated From'),
                        Forms\Components\DatePicker::make('updated_until')
                            ->label('Updated Until'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['updated_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('updated_at', '>=', $date),
                            )
                            ->when(
                                $data['updated_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('updated_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                Tables\Actions\Action::make('view')
                    ->url(fn(Ticket $record): string => static::getUrl('view', ['record' => $record]))
                    ->icon('heroicon-o-eye'),
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTickets::route('/'),
            'create' => Pages\CreateTicket::route('/create'),
            'edit' => Pages\EditTicket::route('/{record}/edit'),
            'view' => Pages\ViewTicket::route('/{record}'),
        ];
    }
}
