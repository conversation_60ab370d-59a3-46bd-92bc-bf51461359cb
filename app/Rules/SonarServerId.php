<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class SonarServerId implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!preg_match('/^[A-Z0-9]{8}-[A-Za-z0-9_]{20}$/', $value)) {
            $fail('Format: XXXXXXXX-XXXXXXXXXXXXXXXXXXXX (8 uppercase chars/digits, hyphen, 20 chars/digits/underscores)');
        }
    }
}