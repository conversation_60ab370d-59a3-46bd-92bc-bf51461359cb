<?php

namespace App\Models;

use App\Scopes\SortingScope;
use App\Traits\Blamable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\Sluggable\HasSlug;
use Spatie\Sluggable\SlugOptions;

class TicketCategory extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable, HasSlug;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'ticket_category_id';

    /**
     * The table associated with the model.
     *
     * @var string|null
     */
    //protected $table = 'table';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'slug',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    /**
     * Get the parent category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(TicketCategory::class, 'parent_id', 'ticket_category_id');
    }

    /**
     * Get the child categories.
     */
    public function children(): HasMany
    {
        return $this->hasMany(TicketCategory::class, 'parent_id', 'ticket_category_id');
    }

    /**
     * Get all of the tickets for the TicketCategory.
     */
    public function tickets(): HasMany
    {
        return $this->hasMany(Ticket::class, 'ticket_category_id', 'ticket_category_id');
    }

    /**
     * Get all of the pre-defined replies for the category.
     */
    public function replies(): HasMany
    {
        return $this->hasMany(TicketCategoryReply::class, 'ticket_category_id', 'ticket_category_id');
    }

    /**
     * Get the options for generating the slug.
     */
    public function getSlugOptions(): SlugOptions
    {
        return SlugOptions::create()
            ->generateSlugsFrom('name')
            ->saveSlugsTo('slug');
    }


    /**
     * Generates a hierarchical list of categories for a select dropdown.
     * Parent categories with children are group labels.
     * Parent categories without children are selectable options.
     */
    public static function getHierarchicalCategoryOptions(): array
    {
        $options = [];
        $parentCategories = self::whereNull('parent_id')->with('children')->get();

        foreach ($parentCategories as $parent) {
            if ($parent->children->isNotEmpty()) {
                // The value is an array of children, using ticket_category_id as the key.
                $options[$parent->name] = $parent->children->pluck('name', 'ticket_category_id')->toArray();
            } else {
                // If the parent has no children, add it as a regular selectable option.
                $options[$parent->ticket_category_id] = $parent->name;
            }
        }

        return $options;
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
