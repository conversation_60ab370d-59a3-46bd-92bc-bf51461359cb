<?php

namespace App\Models;

use App\Enums\Notifications;
use App\Helpers\RbacHelper;
use App\Scopes\SortingScope;
use App\Traits\Blamable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;

class Notification extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'notification_id';

    /**
     * The table associated with the model.
     *
     * @var string|null
     */
    protected $table = 'notifications';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'notification_type_id',
        'template_params',
        'delivery_channels',
        'link_url',
        'is_sent',
        'is_read',
        'read_at',
        'is_archived',
        'expires_at',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'template_params' => 'array',
            'delivery_channels' => 'array',
            'is_read' => 'boolean',
            'is_archived' => 'boolean',
            'read_at' => 'datetime',
            'expires_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'user_id')
            ->where('type', RbacHelper::TYPE_CUSTOMER);
    }

    public function notificationType()
    {
        return $this->belongsTo(NotificationType::class, 'notification_type_id', 'notification_type_id');
    }



    /**
     * Parses the template string and replaces placeholders in a performant way.
     *
     * @return string The fully rendered notification message.
     */
    public function getFormattedMessage(): string
    {
        $templateString = $this->notificationType?->translation?->template_string ?? '';

        if (empty($this->template_params)) {
            return $templateString;
        }

        $placeholders = array_map(function ($key) {
            return '{' . $key . '}';
        }, array_keys($this->template_params));

        $values = array_values($this->template_params);

        return str_replace($placeholders, $values, $templateString);
    }

    public function scopeVisibleToUser($query, User $user)
    {
        return $query
            ->where('is_archived', false)
            ->whereJsonContains('delivery_channels', Notifications::NOTIFICATION_CHANNEL_DATABASE->value)
            ->whereHas('notificationType', function ($subQuery) {
                $subQuery->where('is_visible', 1);
            })
            ->whereDoesntHave('notificationType', function ($query) use ($user) {
                $query->whereExists(function ($subQuery) use ($user) {
                    $subQuery->select(DB::raw(1))
                        ->from('user_notification_settings')
                        ->whereColumn('user_notification_settings.notification_type_id', 'notification_types.notification_type_id')
                        ->where('user_notification_settings.user_id', $user->user_id)
                        ->where('user_notification_settings.is_enabled', 0);
                });
            });
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
