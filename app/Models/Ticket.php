<?php

namespace App\Models;

use App\Enums\TicketEnum;
use App\Helpers\RbacHelper;
use App\Scopes\SortingScope;
use App\Traits\Blamable;
use App\Traits\Timezones;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use OwenIt\Auditing\Contracts\Auditable;

class Ticket extends Model implements Auditable
{
    use \OwenIt\Auditing\Auditable, Blamable, Timezones;

    /**
     * The primary key column name.
     *
     * @var string
     */
    protected $primaryKey = 'ticket_id';

    /**
     * The table associated with the model.
     *
     * @var string|null
     */
    //protected $table = 'table';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'user_id',
        'ticket_category_id',
        'title',
        'content',
        'status',
        'priority',
        'resolved_at',
        'preferred_agent_id',
        'assigned_to',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
        'deleted_at',
        'deleted_by'
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'status' => TicketEnum::class,
            'priority' => TicketEnum::class,
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
            //'password' => 'hashed',
            //'countries' => 'array',
        ];
    }

    /**
     * Attributes to include in the Audit.
     *
     * @var array
     */
    // protected $auditInclude = [
    //     'title',
    //     'content',
    // ];


    /**
     * Get the customer (user) that owns the ticket.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id')->where('type', RbacHelper::TYPE_CUSTOMER)->whereNotNull('company_id');
    }

    /**
     * Get the category for the ticket.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(TicketCategory::class, 'ticket_category_id');
    }

    /**
     * Get the agent assigned to the ticket.
     */
    public function assignee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to')->where('type', RbacHelper::TYPE_ADMIN);
    }

    /**
     * Get all of the replies for the Ticket.
     */
    public function replies(): HasMany
    {
        return $this->hasMany(TicketReply::class, 'ticket_id', 'ticket_id');
    }

    public function getRouteKeyName()
    {
        return 'ticket_id';
    }

    protected static function booted()
    {
        static::addGlobalScope(new SortingScope());

        static::creating(function ($model) {
            self::setCreatedUpdatedBy($model);
        });

        static::saving(function ($model) {
            self::setUpdatedBy($model);
        });

        static::deleting(function ($model) {
            self::setDeletedBy($model);
        });

        return parent::booted();
    }
}
